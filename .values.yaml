app:
  group: cf  #项目的group名字
  name: cf-admin-api   #项目的application.name
service:
  port: 8084  #http端口号
  grpcPort: 18084   #grpc端口号
ingress:   #如果是需要对外暴露api的服务，则需要加入ingress段，目前测试环境通过api-gateway进行链接内部服务，无需设置此选项。只有在无api-gateway的环境中才需要声明
  enabled: true
  subdomain: sea
  paths:
    - /admin/cf/  #指定需要映射到本服务的path列表。可以是多个
    - /admin/crowdfunding/
    - /admin/ad/
    - /admin/pay/
#secrets:   #如果项目需要引用支付证书，则引入该段
#  - name: pay-certs
#    path: /data/cert
#    secretName: pay-certs
healthPath: /actuator/health