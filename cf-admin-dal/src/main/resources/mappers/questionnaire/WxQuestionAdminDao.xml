<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.questionnaire.WxQuestionAdminDao">

    <sql id="TABLE_NAME">
      `wx_question`
    </sql>

    <sql id="FIELDS">
      `qnr_id`,
      `tag_id`,
      `tag_code`,
      `tag_name`,
      `content`,
      `reply_type`,
      `option_value` as `option`,
      `min_value`,
      `max_value`,
      `remark`,
      `sequence`,
      `create_by`,
      `update_by`
    </sql>

    <insert id="save" parameterType="com.shuidihuzhu.cf.model.questionnaire.WxQuestion">
        INSERT INTO
        <include refid="TABLE_NAME"/>
        (`qnr_id`, `tag_id`, `tag_code`, `tag_name`, `content`, `reply_type`,
        <if test="item.replyType >0">
            `option_value`,
        </if>
        <if test="item.replyType ==0">
            `min_value`, `max_value`,
        </if>
        `remark`, `sequence`, `create_by`, `update_by`)
        VALUES
        (#{item.qnrId}, #{item.tagId}, #{item.tagCode}, #{item.tagName}, #{item.content}, #{item.replyType},
        <if test="item.replyType >0">
            #{item.option},
        </if>
        <if test="item.replyType ==0">
            #{item.minValue}, #{item.maxValue},
        </if>
         #{item.remark}, #{item.sequence}, #{item.createBy}, #{item.updateBy})
    </insert>

    <select id="getByQnrId" resultType="com.shuidihuzhu.cf.model.questionnaire.WxQuestion">
        SELECT <include refid="FIELDS" />
        FROM <include refid="TABLE_NAME" />
        WHERE qnr_id =#{qnrId} AND `is_delete` =0
    </select>
    <update id="delByQnrId" parameterType="java.lang.Integer">
        UPDATE
        <include refid="TABLE_NAME"/>
        SET
        `is_delete` = 1
        WHERE
        qnr_id = #{qnrId}
    </update>

</mapper>