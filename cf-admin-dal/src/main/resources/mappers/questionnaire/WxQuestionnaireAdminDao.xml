<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.questionnaire.WxQuestionnaireAdminDao">

    <sql id="TABLE_NAME">
      `wx_questionnaire`
    </sql>

    <sql id="FIELDS">
      `id`,
      `name`,
      `title`,
       `tag_names`,
       `create_by`
    </sql>

    <select id="getById" resultType="com.shuidihuzhu.cf.model.questionnaire.WxQuestionnaire">
        SELECT <include refid="FIELDS" /> FROM <include refid="TABLE_NAME" />
        WHERE  `is_delete` =0
        AND `id` = #{id}
    </select>

    <select id="checkeNameIsExists" resultType="java.lang.Integer">
        SELECT count(*) FROM <include refid="TABLE_NAME" />
        WHERE `is_delete` =0 AND `name` = #{name}
    </select>

    <select id="getWxQuestionnairePage" resultType="com.shuidihuzhu.cf.vo.questionnaire.WxQuestionnaireAdminVo">
        SELECT
            page.`id`,
            page.`name`,
            page.`title`,
            page.`tag_names`,
            page.`create_by`,
            page.`create_time`,
            page.`update_by`,
            page.`update_time`
        FROM
        <include refid="TABLE_NAME"/> page
        WHERE `is_delete` =0
        <if test="title != null and title != '' ">
           AND page.`title` LIKE CONCAT('%',#{title},'%')
        </if>
        <if test="name != null and name !='' ">
           AND page.`name` LIKE CONCAT('%',#{name},'%')
        </if>
        <if test="tagName != null and tagName !=''">
            AND page.`tag_names` LIKE CONCAT('%',#{tagName},'%')
        </if>
        <if test="createBy != null and createBy != ''">
            AND page.`create_by` = #{createBy}
        </if>
    </select>

    <insert id="add" parameterType="com.shuidihuzhu.cf.model.questionnaire.WxQuestionnaire" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="TABLE_NAME"/>
        (`name`, `title`,
        <if test="type != null">
            `type`,
        </if>
        <if test="tagNames != null and tagNames != ''">
            `tag_names`,
        </if>
        `create_by`,`update_by`)
        VALUES
        (#{name}, #{title},
        <if test="type != null">
            #{type},
        </if>
        <if test="tagNames != null and tagNames != ''">
            #{tagNames},
        </if>
        #{createBy}, #{createBy})
    </insert>

    <update id="edit" parameterType="com.shuidihuzhu.cf.model.questionnaire.WxQuestionnaire">
        UPDATE
        <include refid="TABLE_NAME"/>
        SET
        <if test="name != null">
          `name` = #{name},
        </if>
        <if test="title != null">
            `title` = #{title},
        </if>
        <if test="type != null">
            `type` = #{type},
        </if>
        <if test="tagNames != null">
            `tag_names` = #{tagNames},
        </if>
        <if test="remark != null">
            `remark` = #{remark},
        </if>
        <if test="updateBy != null">
            `update_by` = #{updateBy}
        </if>
        WHERE `id` = #{id}
    </update>

</mapper>