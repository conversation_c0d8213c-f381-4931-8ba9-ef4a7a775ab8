<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.questionnaire.WxSdTagAdminDao">

    <sql id="TABLE_NAME">
      `wx_sd_tag`
    </sql>

    <sql id="PARENT_FIELDS">
      `id`,
      `tag_name`
    </sql>

    <sql id="FIELDS">
      `id`,
      `parent_id`,
      `tag_name`,
      `tag_code`
    </sql>

    <select id="getAll" resultType="com.shuidihuzhu.cf.vo.questionnaire.WxSdTagAdminVo">
        SELECT <include refid="PARENT_FIELDS" />
        FROM <include refid="TABLE_NAME" />
        WHERE `parent_id` = 0 AND `is_delete` =0
    </select>

    <select id="getByParentId" resultType="com.shuidihuzhu.cf.vo.questionnaire.WxSdTagValueAdminVo">
        SELECT `id`, `tag_name` as label, `tag_code` as `value`
        FROM <include refid="TABLE_NAME" />
        WHERE `is_delete` =0 AND `parent_id` = #{parentId}
    </select>

    <select id="getByTagId" resultType="com.shuidihuzhu.cf.model.questionnaire.WxSdTag">
        SELECT  <include refid="FIELDS" />  FROM <include refid="TABLE_NAME" />
        WHERE  `is_delete` =0 AND `id` = #{id}
    </select>
</mapper>