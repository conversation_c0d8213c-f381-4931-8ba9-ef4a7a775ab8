<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.approve.AdminApproveDAO">

    <sql id="table_name">
        `admin_crowdfunding_approve`
    </sql>

    <sql id="select_fields">
        `id`,
        `oprtime`,
        <include refid="insert_Column_List"/>
    </sql>

    <sql id="insert_Column_List">
        `crowdfunding_id`,
        `oprid`,
        `status`,
        `comment`,
        `organization`,
        `source_type`
	</sql>

    <insert id="insert"
            useGeneratedKeys="true"
            keyProperty="id"
            parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingApprove">
        insert into
        <include refid="table_name"/>
        (<include refid="insert_Column_List"/>)
        values (
        #{crowdfundingId} ,
        #{oprid} ,
        #{status} ,
        #{comment} ,
        #{organization} ,
        #{sourceType}
        )
    </insert>

    <select id="listByCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingApprove">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        <where>
            `crowdfunding_id` = #{caseId,jdbcType=INTEGER}
        </where>
    </select>

    <select id="listByCaseIdAndSourceTypes"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingApprove">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        <where>
            `crowdfunding_id` = #{caseId,jdbcType=INTEGER}
            AND `source_type` in
            <foreach collection="sourceTypes" item="sourceType" open="(" separator="," close=")">
            #{sourceType}
            </foreach>
        </where>
    </select>

    <select id="getLastByCaseIdAndOrgKey" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingApprove">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        <where>
            `crowdfunding_id` = #{caseId,jdbcType=INTEGER}
            AND organization like concat('%', #{orgKey}, '%')
        </where>
        order by id desc
        limit 1
    </select>

    <select id="getById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingApprove">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        <where>
            `id` = id
        </where>
        limit 1
    </select>
</mapper>