<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.approve.ApproveControlRecordDAO">

    <sql id="table_name">
        `cf_case_approve_control_record`
    </sql>

    <sql id="select_fields">
        `id`,
        `create_time`,
        `update_time`,
        `handle_time`,
        <include refid="insert_Column_List"/>
    </sql>

    <sql id="insert_Column_List">
        `case_id`,
        `handle_status`,
        `source_type`,
        `work_order_id`,
        `operator_id`,
        `flow_type`
	</sql>

    <insert id="insert"
            useGeneratedKeys="true"
            keyProperty="id"
            parameterType="com.shuidihuzhu.cf.domain.approve.ApproveControlRecordDO">
        insert into
        <include refid="table_name"/>
        (<include refid="insert_Column_List"/>)
        values (
        #{caseId}  ,
        #{handleStatus}  ,
        #{sourceType}  ,
        #{workOrderId}  ,
        #{operatorId} ,
        #{flowType}
        )
    </insert>

    <select id="getNewestRecordByCaseIdAndHandleStatus"
            resultType="com.shuidihuzhu.cf.domain.approve.ApproveControlRecordDO">
        select <include refid="select_fields"/>
        from <include refid="table_name"/>
        <where>
            `case_id` = #{caseId}
            and `handle_status` = #{handleStatus}
            and `is_delete` = 0
        </where>
        order by id desc
        limit 1
    </select>

    <select id="getById" resultType="com.shuidihuzhu.cf.domain.approve.ApproveControlRecordDO">
        select <include refid="select_fields"/>
        from <include refid="table_name"/>
        <where>
            `id` = #{id}
            and `is_delete` = 0
        </where>
    </select>

    <select id="getByCaseIdAndWorkOrderId"
            resultType="com.shuidihuzhu.cf.domain.approve.ApproveControlRecordDO">
        select <include refid="select_fields"/>
        from <include refid="table_name"/>
        <where>
            `case_id` = #{caseId}
            and `work_order_id` = #{workOrderId}
            and `is_delete` = 0
        </where>
        order by id desc
        limit 1
    </select>

    <update id="updateHandleStatusById">
        update <include refid="table_name"/>
        set `handle_status` = #{newHandleStatus} , `handle_time` = #{handleTime}
        where `id` = #{id}
        and `handle_status` = #{oldHandleStatus}
        and `is_delete` = 0
    </update>

</mapper>