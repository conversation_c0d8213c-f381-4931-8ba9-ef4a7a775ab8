<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.approve.ApproveSubItemDAO">

    <sql id="table_name">
        `admin_approve_sub_item`
    </sql>

    <sql id="select_fields">
        `id`,
        `case_id`,
        `code`,
        `passed`
    </sql>

    <sql id="insert_Column_List">
        `case_id`,
        `code`,
        `passed`
	</sql>

    <insert id="insert"
            parameterType="com.shuidihuzhu.cf.domain.approve.ApproveSubItemDO">
        <selectKey order="AFTER" resultType="long" keyProperty="id">
            select last_insert_id() as id
        </selectKey>
        insert into
        <include refid="table_name"/>
        (<include refid="insert_Column_List"/>)
        values(
        #{caseId},
        #{code,jdbcType=INTEGER} ,
        #{passed,jdbcType=BOOLEAN}
        )
    </insert>

    <select id="listByCaseId" resultType="com.shuidihuzhu.cf.domain.approve.ApproveSubItemDO">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        <where>
            `case_id` = #{caseId,jdbcType=INTEGER}
            AND `is_delete` = 0
        </where>
    </select>

    <select id="getByCaseIdAndCode" resultType="com.shuidihuzhu.cf.domain.approve.ApproveSubItemDO">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        <where>
            `case_id` = #{caseId,jdbcType=INTEGER}
            AND `code` = #{code,jdbcType=INTEGER}
            AND `is_delete` = 0
        </where>
        limit 1
    </select>

    <update id="updatePassed">
        UPDATE <include refid="table_name"/>
        SET  `passed`=#{passed}
        WHERE `case_id`=#{caseId}
        AND `code` = #{code}
    </update>


</mapper>