<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.approve.CfServiceSubsidyRecordDao">

    <sql id="table_name">
        `cf_service_subsidy_apply_record`
    </sql>

    <sql id="select_fields">
        `work_order_id`,
        `case_id`,
        `audit_result`,
        `handle_remark`,
        `operator_id`,
        `organization`,
        `create_time`
    </sql>

    <sql id="insert_Column_List">
        `work_order_id`,
        `case_id`,
        `audit_result`,
        `handle_remark`,
        `operator_id`,
        `organization`
	</sql>

    <insert id="insertSubsidyApplyRecord" parameterType="com.shuidihuzhu.cf.domain.approve.SubsidyApplyRecordDO">
        insert into
        <include refid="table_name"/>
        (<include refid="insert_Column_List"/>)
        values (
        #{workOrderId} ,
        #{caseId} ,
        #{auditResult} ,
        #{handleRemark} ,
        #{operatorId} ,
        #{organization}
        )
    </insert>

    <select id="selectRecordByCaseId" resultType="com.shuidihuzhu.cf.domain.approve.SubsidyApplyRecordDO">
        select <include refid="select_fields"/>
        from <include refid="table_name"/>
        where case_id = #{caseId} and is_delete = 0
    </select>

</mapper>