<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.approve.AdminApproveExtDAO">

    <sql id="table_name">
        `admin_crowdfunding_approve_ext`
    </sql>

    <sql id="select_fields">
        `id`,
        <include refid="insert_Column_List"/>
    </sql>

    <sql id="insert_Column_List">
        `approve_id`,
        `ext_name`,
        `ext_value`
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.shuidihuzhu.cf.domain.approve.AdminApproveExt">
        insert into
        <include refid="table_name"/>
        (<include refid="insert_Column_List"/>)
        values (
        #{approveId},
        #{extName},
        #{extValue}
        )
    </insert>

    <select id="listByApproveIdAndExtName" resultType="com.shuidihuzhu.cf.domain.approve.AdminApproveExt">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        <where>
            `approve_id` = #{approveId,jdbcType=INTEGER} AND `ext_name` = #{extName} AND `is_delete` = 0
        </where>
    </select>

    <select id="getApproveExtList" resultType="com.shuidihuzhu.cf.domain.approve.AdminApproveExt">
        select <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        where approve_id in
        <foreach collection="approveIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and ext_name=#{extName} and is_delete =0

    </select>

    <update id="updateExtValue">
        UPDATE <include refid="table_name"/>
        SET  `ext_value`=#{extValue}
        WHERE `id` = #{id}
    </update>
</mapper>