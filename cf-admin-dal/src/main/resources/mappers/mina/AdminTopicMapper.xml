<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.shuidihuzhu.cf.dao.mina.AdminTopicDao">
    <sql id="TABLE">
        cf_xcx_topic
    </sql>

    <sql id="FIELD">
        `id` AS id,
        `title` AS title,
        `description` AS description,
        `icon` AS icon,
        `phase_id` AS phaseId,
        `is_delete` AS isDelete,
         img_urls`
    </sql>

    <sql id="FIELD_DETAIL">
        `id` AS id,
        `title` AS title,
        `description` AS description,
        `icon` AS imgUrl,
        `phase_id` AS phaseId,
        `create_time` AS createTime,
        `img_urls`
    </sql>

    <sql id="FIELD_JOIN">
       cxt.`id` AS id,
       cxt.`title` AS title,
       cxt.`description` AS description,
       cxt.`icon` AS imgUrl,
       cxt.`phase_id` AS phaseId,
       cxt.`create_time` AS createTime,
       cxt.`img_urls` AS  imgUrls
    </sql>

    <select id="getByTitle" resultType="com.shuidihuzhu.cf.model.miniprogram.CfTopic">
        SELECT <include refid="FIELD_JOIN"/>
        FROM <include refid="TABLE"/> as cxt
        <if test="title==null">
        LEFT JOIN `cf_xcx_publish_phase` AS cxpp
        ON cxt.`phase_id` = cxpp.`phase_id`
        </if>
        WHERE cxt.`is_delete` = 0
        <if test="title!=null">
            AND cxt.`title` LIKE CONCAT('%',#{title},'%')
        </if>
        <if test="title==null">
            AND cxt.`phase_id` IN
            <foreach collection="phaseIds" item="phaseId" open="(" separator="," close=")">
                #{phaseId}
            </foreach>
            AND cxpp.`publish_status` = 1
        </if>
        ORDER BY cxt.`phase_id` DESC
    </select>

    <select id="getTopics" resultType="com.shuidihuzhu.cf.vo.mina.CfMinaTopicDetailVo">
        SELECT <include refid="FIELD_DETAIL"/>
        FROM <include refid="TABLE"/>
        WHERE `is_delete`=0
        <if test="title!=null">
            AND `title` LIKE CONCAT('%',#{title},'%')
        </if>
        ORDER BY `id` DESC
    </select>


    <select id="getPublishTopics" resultType="com.shuidihuzhu.cf.model.miniprogram.CfTopic">
        SELECT <include refid="FIELD_JOIN"/>
        FROM <include refid="TABLE"/> as cxt
        LEFT JOIN `cf_xcx_publish_phase` AS cxpp
        ON cxt.`phase_id` = cxpp.`phase_id`
        WHERE cxt.`is_delete`=0
        <if test="title!=null">
            AND cxt.`title` LIKE CONCAT('%',#{title},'%')
        </if>
        <if test="title ==null ">
            AND cxpp.`publish_status` = 1
        </if>
    </select>


    <select id="getUnPublishTopics" resultType="com.shuidihuzhu.cf.vo.mina.CfMinaTopicDetailVo">
        SELECT <include refid="FIELD_DETAIL"/>
        FROM <include refid="TABLE"/>
        WHERE `phase_id`=0
        <if test="title!=null and title!=''">
            AND `title` LIKE CONCAT('%',#{title},'%')
        </if>
        AND `is_delete`=0
        ORDER BY `id` DESC
    </select>

    <select id="listByPhaseIds" resultType="com.shuidihuzhu.cf.vo.mina.CfMinaTopicDetailVo">
        SELECT <include refid="FIELD_DETAIL"/>
        FROM <include refid="TABLE"/>
        WHERE `phase_id` IN
        <foreach collection="phaseIds" item="phaseId" open="(" separator="," close=")">
            #{phaseId}
        </foreach>
        AND `is_delete`=0
    </select>

    <update id="updatePhaseId">
        UPDATE <include refid="TABLE"/>
        SET `phase_id`=#{phaseId}
        WHERE `id`=#{topicId}
        AND `is_delete`=0
    </update>


    <insert id="addOne" parameterType="com.shuidihuzhu.cf.model.miniprogram.CfTopic" useGeneratedKeys="true" keyProperty="cfTopic.id">
        <!--<selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>-->
        INSERT INTO <include refid="TABLE"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cfTopic.title != null">
                title,
            </if>
            <if test="cfTopic.description != null">
                description,
            </if>
            <if test="cfTopic.icon != null">
                icon,
            </if>
            <if test="cfTopic.phaseId != null">
                phase_id,
            </if>
            <if test="cfTopic.imgUrls != null">
                `img_urls`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="cfTopic.title != null">
                #{cfTopic.title,jdbcType=VARCHAR},
            </if>
            <if test="cfTopic.description != null">
                #{cfTopic.description,jdbcType=VARCHAR},
            </if>
            <if test="cfTopic.icon != null">
                #{cfTopic.icon,jdbcType=VARCHAR},
            </if>
            <if test="cfTopic.phaseId != null">
                #{cfTopic.phaseId,jdbcType=INTEGER},
            </if>
            <if test="cfTopic.imgUrls != null">
                #{cfTopic.imgUrls},
            </if>
        </trim>
    </insert>

    <update id="deletePhaseById">
        UPDATE <include refid="TABLE"/>
        SET `phase_id` = 0
        where `id` IN
        <foreach collection="topicIds" item="topicId" open="(" separator="," close=")">
            #{topicId}
        </foreach>
    </update>

    <update id="deleteByPhaseId">
        UPDATE <include refid="TABLE"/>
        SET `phase_id` = 0
        where `phase_id` = #{phaseId}
    </update>

</mapper>