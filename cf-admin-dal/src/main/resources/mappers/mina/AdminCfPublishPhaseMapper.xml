<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.mina.AdminCfPublishPhaseDao">
    <sql id="TABLE">
        cf_xcx_publish_phase
    </sql>
    <sql id="FIELDS">
        `id` AS id,
        `keyword_id` AS keywordId,
        `phase_id` AS phaseId,
        `publish_status` AS publishStatus,
        `publish_time` AS publishTime,
        `is_delete` AS isDelete
    </sql>

    <select id="getLatestPhase" resultType="com.shuidihuzhu.cf.model.miniprogram.CfPublishPhase">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="TABLE"/>
        ORDER BY `phase_id` DESC
        limit 1
    </select>

    <update id="updatePhaseStatusByTime">
        UPDATE <include refid="TABLE"/>
        SET `publish_status` = 1
        WHERE `publish_time`<![CDATA[ <= ]]>#{time}
        AND `is_delete`=0
    </update>

    <update id="updateByPhaseId" parameterType="com.shuidihuzhu.cf.model.miniprogram.CfPublishPhase">
        UPDATE <include refid="TABLE"/>
        <set>
            <if test="cfPublishPhase.keywordId != null">
                `keyword_id` = #{cfPublishPhase.keywordId},
            </if>
            <if test="cfPublishPhase.publishStatus != null">
                `publish_status` = #{cfPublishPhase.publishStatus},
            </if>
            <if test="cfPublishPhase.publishTime != null">
                `publish_time` = #{cfPublishPhase.publishTime},
            </if>
        </set>
        WHERE `phase_id`=#{phaseId}
        AND `is_delete`=0
    </update>


    <update id="delete">
        UPDATE <include refid="TABLE"/>
        SET `is_delete` = 1
        WHERE `phase_id`=#{phaseId}
    </update>

</mapper>