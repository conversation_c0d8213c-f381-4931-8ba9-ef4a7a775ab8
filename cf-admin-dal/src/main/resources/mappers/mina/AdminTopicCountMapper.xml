<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.mina.AdminTopicCountDao">
    <sql id="TABLE">
        cf_xcx_topic_count
    </sql>
    <sql id="FIELDS">
        `id` AS id,
        `topic_id` AS topicId,
        `comment_count` AS commentCount,
        `share_count` AS shareCount,
        `praise_count` AS praiseCount
    </sql>

    <update id="deleteByTopicId">
        UPDATE <include refid="TABLE"/>
        SET `is_delete`=1
        WHERE `topic_id`=#{topicId}
    </update>

    <select id="listOrderByCommentNum" resultType="com.shuidihuzhu.cf.model.miniprogram.CfTopicShareCommentCount">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE `is_delete`=0
        AND `topic_id` IN
        <foreach collection="cfTopicPublishIds" item="cfTopicPublishId" separator="," open="(" close=")">
            #{cfTopicPublishId}
        </foreach>
        ORDER BY `comment_count` DESC
    </select>

    <select id="listOrderByPraiseNum" resultType="com.shuidihuzhu.cf.model.miniprogram.CfTopicShareCommentCount">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE `is_delete`=0
        AND `topic_id` IN
        <foreach collection="cfTopicPublishIds" item="cfTopicPublishId" separator="," open="(" close=")">
            #{cfTopicPublishId}
        </foreach>
        ORDER BY `praise_count` DESC
    </select>
</mapper>