<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.mina.AdminCfTopicCommentDao">
    <sql id="table_name">
        cf_xcx_topic_comment
    </sql>

    <sql id="select_fields">
        `id`,
        `topic_id`,
        `user_id`,
        `nickname`,
        `head_url`,
        `content`,
        `sensitive`,
        `create_time`,
        `update_time`,
        `parent_id`,
        `comment_group_id`,
        `is_delete`
    </sql>

    <select id="selectByPage" resultType="com.shuidihuzhu.cf.model.miniprogram.CfTopicComment">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        <where>
            <if test="commentId!=null and commentId>0">
                `id`=#{commentId}
            </if>
            <if test="topicId!=null and topicId>0">
                AND `topic_id`=#{topicId}
            </if>
            <if test="comment!=null and comment!=''">
                AND `content` LIKE CONCAT('%',#{comment},'%')
            </if>
            <if test="commentUserId!=null and commentUserId>0">
                AND `user_id`=#{commentUserId}
            </if>
            <if test="isSensitiveWord!=null">
                AND `sensitive`=#{isSensitiveWord}
            </if>
            <if test="beginTime!=null and endTime!=null">
                AND <![CDATA[ `create_time`>=#{beginTime} AND `create_time`<#{endTime} ]]>
            </if>
            AND `is_delete`=0
            AND `type`= 1
        </where>
        <if test="orderType != null">
            <if test="orderType == 1">
                ORDER BY `create_time` DESC
            </if>
            <if test="orderType == 2">
                ORDER BY `topic_id` DESC
            </if>
        </if>
        <if test="orderType == null">
            ORDER BY `create_time` DESC
        </if>
    </select>


    <update id="deleteByTopicId">
        UPDATE <include refid="table_name"/>
        SET `is_delete` = 1
        where `topic_id` = #{topicId}
    </update>


    <update id="deleteByGroupId">
        UPDATE <include refid="table_name"/>
        SET `is_delete` = 1
        where `comment_group_id` = #{groupId}
    </update>

</mapper>