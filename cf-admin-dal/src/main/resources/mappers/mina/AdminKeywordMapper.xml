<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.mina.AdminKeywordDao">
    <sql id="TABLE">
        cf_xcx_topic_keyword
    </sql>

    <insert id="addOne" parameterType="com.shuidihuzhu.cf.vo.mina.AdminKeyword" useGeneratedKeys="true" keyProperty="adminKeyword.id">
        INSERT INTO
        <include refid="TABLE"/> (`keyword`)
        VALUES (#{adminKeyword.keyword})
    </insert>
</mapper>