<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.mina.AdminCommentDynamicDao">
    <sql id="TABLE">
        cf_xcx_comment_dynamic
    </sql>

    <update id="updateTopStatusByCommentId">
        UPDATE <include refid="TABLE"/>
        SET `top_status`=#{status}
        WHERE `comment_id`=#{commentId}
        AND `is_delete`=0
    </update>
</mapper>