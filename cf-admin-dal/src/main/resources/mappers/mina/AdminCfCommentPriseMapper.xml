<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.mina.AdminCfCommentPriseDao">
    <sql id="table_name">
        cf_xcx_comment_praise
    </sql>
    <sql id="select_fields">
        `id`,
        `topic_id`,
        `user_id`,
        `comment_id`,
        `praise_status`,
        `is_delete`
    </sql>

    <select id="selectPraiseCountByCommentIdList" resultType="com.shuidihuzhu.cf.vo.mina.CfIdCountVo">
        SELECT `comment_id` AS id, SUM(`praise_status`) AS counts
        FROM <include refid="table_name"/>
        WHERE `comment_id` IN 
        <foreach collection="set" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY `comment_id`
    </select>

</mapper>