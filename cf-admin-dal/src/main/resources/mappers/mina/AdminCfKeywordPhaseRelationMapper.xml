<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.mina.AdminCfKeywordPhaseRelationDao">
    <sql id="TABLE">
        cf_xcx_keyword_phase_relation
    </sql>
    <sql id="FIELD">
        `id` AS id,
        `keyword_id` AS keywordId,
        `phase_id` AS phaseId,
        `expect_number` AS expectNumber,
        `not_expect_number` AS notExpectNumber,
        `is_delete` AS isDelete
    </sql>


    <update id="updateByPhaseId" parameterType="com.shuidihuzhu.cf.model.miniprogram.CfKeywordPhaseRelation">
        UPDATE <include refid="TABLE"/>
        <set>
            <if test="cfKeywordPhaseRelation.keywordId != null">
                `keyword_id` = #{cfKeywordPhaseRelation.keywordId,jdbcType=INTEGER},
            </if>
        </set>
        WHERE `phase_id`=#{phaseId}
        AND `is_delete`=0
    </update>

</mapper>