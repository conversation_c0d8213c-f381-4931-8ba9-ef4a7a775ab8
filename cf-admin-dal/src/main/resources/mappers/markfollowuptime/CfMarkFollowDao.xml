<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.markfollowuptime.CfMarkFollowDao">

    <insert id="add" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO cf_mark_follow(biz_id, operator_id, order_type, target_time, version, disable, done)
        VALUES (#{bizId}, #{operatorId}, #{orderType}, #{targetTime}, #{version}, #{disable}, #{done})
    </insert>

    <select id="getById" resultType="com.shuidihuzhu.cf.model.markfollowuptime.CfMarkFollowDo">
        SELECT id,
               biz_id,
               operator_id,
               order_type,
               target_time,
               version,
               done,
               disable
        FROM cf_mark_follow
        <where>
            id = #{id}
            and now() <![CDATA[ < ]]> target_time
            and disable = 0
            and is_delete = 0
        </where>
    </select>

    <select id="getByBizId" resultType="com.shuidihuzhu.cf.model.markfollowuptime.CfMarkFollowDo">
        SELECT id,
        biz_id,
        operator_id,
        order_type,
        target_time,
        version,
        done,
        disable
        FROM cf_mark_follow
        <where>
            biz_id = #{bizId}
            and now() <![CDATA[ < ]]> target_time
            and disable = 0
            and is_delete = 0
        </where>
    </select>


    <update id="updateTargetTimeById">
        update
        cf_mark_follow
        <set>
            target_time = #{targetTime}
            ,version = #{version}
            ,operator_id = #{operatorId}
        </set>
        <where>
            id = #{id}
        </where>
    </update>

    <update id="updateOperatorIdByBizId">
        update
        cf_mark_follow
        <set>
            operator_id = #{operatorId}
        </set>
        <where>
            biz_id = #{bizId}
        </where>
    </update>

    <update id="removeById">
        update
        cf_mark_follow
        <set>
            disable = 1
        </set>
        <where>
            id = #{id}
        </where>
    </update>

    <update id="doneById">
        update
        cf_mark_follow
        <set>
            done = 1
        </set>
        <where>
            id = #{id}
        </where>
    </update>

    <select id="getByBizIds" resultType="com.shuidihuzhu.cf.model.markfollowuptime.CfMarkFollowDo">
        SELECT id,
        biz_id,
        operator_id,
        order_type,
        target_time,
        version,
        done,
        disable
        FROM cf_mark_follow
        WHERE biz_id in <foreach collection="bizIds" item="bizId" open="(" separator="," close=")">#{bizId}</foreach>
        and now() <![CDATA[ < ]]> target_time
        and disable = 0
        and is_delete = 0
    </select>

    <select id="getListByOperatorId" resultType="com.shuidihuzhu.cf.model.markfollowuptime.CfMarkFollowDo">
        SELECT id,
        biz_id,
        operator_id,
        order_type,
        target_time,
        version,
        done,
        disable
        FROM cf_mark_follow
        WHERE operator_id = #{operatorId}
        and now() <![CDATA[ < ]]> target_time
        and disable = 0
        and done = 0
        and is_delete = 0
    </select>

</mapper>
