<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.call.CallOutRecordDao">
    <sql id="table">
        export_clink_call_out_day
    </sql>

    <sql id="fields">
      `id`, `day_key`, `summary`, `region`, `k3`, `seat_number`, `seat_name`, `connect_time`,
      `answer_status`, `call_duration`, `call_charge`, `total_call_charge`,  `total_time`, `call_type`, `call_out_task`, `record_url`,
      `comment`, `is_custom_hang_up`, `is_in_cf_info`, `quality_grade`, `call_time`, `status`, `create_time`, `update_time`, `is_delete`,
      encrypt_custom_phone_number,encrypt_seat_phone_number
    </sql>

    <sql id="insertFields">
        (`day_key`, `summary`, `region`, `k3`, `seat_number`, `seat_name`, `connect_time`,
      `answer_status`, `call_duration`, `call_charge`, `total_call_charge`,  `total_time`, `call_type`, `call_out_task`, `record_url`,
      `comment`, `is_custom_hang_up`, `is_in_cf_info`, `quality_grade`, `call_time`, `status`,`encrypt_custom_phone_number`,`encrypt_seat_phone_number`)
    </sql>

    <select id="getRecordsByDayTimeAndCustomerPhoneNum" resultType="com.shuidihuzhu.cf.call.CallOutModel">
        select <include refid="fields"/>
        from <include refid="table"/>
        where `day_key` >= #{startDay} and `day_key` &lt;= #{endDay}
        <if test="encryptoCustomerPhoneNumber != null and encryptoCustomerPhoneNumber !=''">
            and encrypt_custom_phone_number = #{encryptoCustomerPhoneNumber}
        </if>
    </select>

    <select id="getCallOutRecordById" resultType="com.shuidihuzhu.cf.call.CallOutModel">
        select <include refid="fields"/>
        from <include refid="table"/>
        where id = #{callRecordId}
    </select>

    <select id="getAllByPhoneNum" resultType="com.shuidihuzhu.cf.call.CallOutModel">
        select <include refid="fields"/>
        from <include refid="table"/>
        where encrypt_custom_phone_number = #{customerPhoneNumber}
    </select>

    <insert id="insertCallOutModel" parameterType="com.shuidihuzhu.cf.call.CallOutModel">
        INSERT INTO
        <include refid="table"/>
        <include refid="insertFields"/>
        VALUES
        (#{dayKey}, #{summary}, #{region}, #{k3}, #{seatNumber},#{seatName},#{connectTime},
        #{answerStatus}, #{callDuration}, #{callCharge}, #{totalCallCharge},#{totalTime},#{callType}, #{callOutTask},
        #{recordUrl},
        #{comment}, #{isCustomHangUp},#{isInCfInfo},#{qualityGrade},#{callTime}, #{status}, #{encryptCustomPhoneNumber},
        #{encryptSeatPhoneNumber})
    </insert>

</mapper>