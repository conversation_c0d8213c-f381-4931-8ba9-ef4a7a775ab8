<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.call.CallInRecordDao">
    <sql id="table">
        export_clink_call_in_day
    </sql>

    <sql id="fields">
        `id`, `day_key`, `summary`, `region`, `hotine_number`, `ivr`, `queue_number`, `queue_name`, `seat_number`,
        `seat_name`, `seat_phone_number`, `call_time`, `connect_time`, `call_duration`, `answer_status`, `is_hang_up`, `total_time`, `record_url`,
        `call_charge`, `k18`, `comment`, `is_in_cf_info`, `quality_grade`, `quality_comment`, `status`, `create_time`, `update_time`, `is_delete`,`encrypt_custom_phone_number`
    </sql>

    <sql id="insertFields">
        (`day_key`, `summary`, `region`, `hotine_number`, `ivr`, `queue_number`, `queue_name`, `seat_number`,
        `seat_name`, `seat_phone_number`, `call_time`, `connect_time`, `call_duration`, `answer_status`, `is_hang_up`, `total_time`, `record_url`,
        `call_charge`, `k18`, `comment`, `is_in_cf_info`, `quality_grade`, `quality_comment`, `status`,`encrypt_custom_phone_number`)
    </sql>

    <select id="getRecordsByDayTimeAndCustomerPhoneNum" resultType="com.shuidihuzhu.cf.call.CallInModel">
        select <include refid="fields"/>
        from <include refid="table"/>
        where `day_key` >= #{startDay} and `day_key` &lt;= #{endDay}
        <if test="encryptoCustomerPhoneNumber != null and encryptoCustomerPhoneNumber !=''">
            and encrypt_custom_phone_number = #{encryptoCustomerPhoneNumber}
        </if>
    </select>

    <select id="getAllByPhoneNum" resultType="com.shuidihuzhu.cf.call.CallInModel">
        select <include refid="fields"/>
        from <include refid="table"/>
        where encrypt_custom_phone_number = #{customerPhoneNum}
    </select>

    <select id="getCallInRecordById" resultType="com.shuidihuzhu.cf.call.CallInModel">
        select <include refid="fields"/>
        from <include refid="table"/>
        where id = #{callRecordId}
    </select>

    <insert id="insertCallInModel" parameterType="com.shuidihuzhu.cf.call.CallInModel">
        INSERT INTO
        <include refid="table"/>
        <include refid="insertFields"/>
        VALUES
        (#{dayKey}, #{summary}, #{region}, #{hotineNumber}, #{ivr},#{queueNumber},#{queueName}, #{seatNumber}, #{seatName}, #{seatPhoneNumber}, #{callTime},
        #{connectTime},#{callDuration}, #{answerStatus}, #{isHangUp}, #{totalTime}, #{recordUrl},#{callCharge},
        #{k18},#{comment}, #{isInCfInfo}, #{qualityGrade}, #{qualityComment}, #{status},#{encryptCustomPhoneNumber})
    </insert>

</mapper>