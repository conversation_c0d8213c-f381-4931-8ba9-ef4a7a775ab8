<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.sd.admin.common.OperationHistorySummaryDao">
	<sql id="insertFields">
		(operation_type, summary, operator_id)
	</sql>

	<sql id="tableName">
		sd_admin_operatioin_history_summary
	</sql>

	<insert id="insert" parameterType="com.shuidihuzhu.cf.model.admin.common.OperationHistorySummary">
		insert into
		<include refid="tableName" />
		<include refid="insertFields" />
		values(#{operationType}, #{summary}, #{operatorId})
	</insert>

</mapper>