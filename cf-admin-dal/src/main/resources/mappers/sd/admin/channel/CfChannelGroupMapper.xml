<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.sd.admin.channel.CfChannelGroupDao" >

    <sql id="tableName">
        cf_channel_group
    </sql>

    <sql id="selectFields">
        `id` as id,
        `name` as name,
        `description` as description
    </sql>

    <sql id="insertFields">
        `name`,
        `description`
    </sql>

    <select id="listGroups" resultType="com.shuidihuzhu.cf.model.admin.channel.CfChannelGroup">
        SELECT <include refid="selectFields"/>
        FROM <include refid="tableName"/>
        WHERE `id` <![CDATA[ <= ]]> #{anchorId}
        LIMIT #{limit}
    </select>

    <select id="listByIds" resultType="com.shuidihuzhu.cf.model.admin.channel.CfChannelGroup">
        SELECT <include refid="selectFields"/>
        FROM <include refid="tableName"/>
        WHERE `id` in
          <foreach collection="ids" open="(" close=")" separator="," item="id">
              #{id}
          </foreach>
    </select>

    <select id="listGroupsByPage" resultType="com.shuidihuzhu.cf.model.admin.channel.CfChannelGroup">
        SELECT <include refid="selectFields"/>
        FROM <include refid="tableName"/>
        WHERE `valid`=1
        ORDER BY `id` DESC
    </select>

    <select id="getById" resultType="com.shuidihuzhu.cf.model.admin.channel.CfChannelGroup">
        SELECT <include refid="selectFields"/>
        FROM <include refid="tableName"/>
        WHERE `id`=#{groupId}
        AND `valid`=1
        LIMIT 1
    </select>

    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.admin.channel.CfChannelGroup">
        INSERT INTO <include refid="tableName"/>
        (<include refid="insertFields"/>)
        VALUES (#{name},#{description})
    </insert>
</mapper>
