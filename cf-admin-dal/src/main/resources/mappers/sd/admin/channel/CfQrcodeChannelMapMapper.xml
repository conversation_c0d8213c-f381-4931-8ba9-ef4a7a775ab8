<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.sd.admin.channel.CfQrcodeChannelMapDao" >
  <sql id="tableName">
    cf_qrcode_channel_map
  </sql>

  <sql id="selectFields">
      `id` as id,
      `qrcode_id` as qrCodeId,
      `cf_channel_id` as cfChannelId,
      `cf_channel_group_id` as cfChannelGroupId
  </sql>

    <insert id="add" parameterType="com.shuidihuzhu.cf.model.admin.channel.CfQrcodeChannel">
        INSERT <include refid="tableName"/>
        (`qrcode_id`, `cf_channel_id`, `cf_channel_group_id`)
        VALUES
        (#{qrCodeId}, #{cfChannelId}, #{cfChannelGroupId})
    </insert>

    <select id="listGroupIdsByQrcodeIds" resultType="com.shuidihuzhu.cf.model.admin.channel.CfQrcodeChannel">
        SELECT <include refid="selectFields"/>
        FROM <include refid="tableName"/>
        WHERE `qrcode_id` IN
          <foreach collection="ids" open="(" close=")" separator="," item="id">
              #{id}
          </foreach>
    </select>

    <update id="update">
        UPDATE <include refid="tableName"/>
        SET
          `cf_channel_id`=#{channelId},
          `cf_channel_group_id`=#{groupId}
        WHERE
          `qrcode_id`=#{qrcodeId}
        LIMIT 1
    </update>

    <select id="getByQrcodeId" resultType="com.shuidihuzhu.cf.model.admin.channel.CfQrcodeChannel">
        SELECT <include refid="selectFields"/>
        FROM <include refid="tableName"/>
        WHERE `qrcode_id`=#{qrcodeId}
        LIMIT 1
    </select>

</mapper>
