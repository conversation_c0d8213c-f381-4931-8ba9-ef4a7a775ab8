<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.sd.admin.channel.CfChannelDao" >
  <sql id="tableName">
    cf_channel
  </sql>

  <sql id="selectFields">
      `id` as id,
      `group_id` as groupId,
      `name` as name,
      `description` as description,
      `create_time` as createTime,
      `update_time` as updateTime
  </sql>

    <insert id="add" parameterType="com.shuidihuzhu.cf.model.admin.channel.CfChannel">
        INSERT <include refid="tableName"/>
        (`group_id`, `name`, `description`, `type`)
        VALUES
        (#{groupId}, #{name}, #{description}, #{type})
    </insert>

    <select id="getByName" resultType="com.shuidihuzhu.cf.model.admin.channel.CfChannel">
        SELECT <include refid="selectFields"/>
        FROM <include refid="tableName"/>
        WHERE `name`=#{name}
        AND `valid`=1
        LIMIT 1
    </select>

    <select id="get" resultType="com.shuidihuzhu.cf.model.admin.channel.CfChannel">
        SELECT <include refid="selectFields"/>
        FROM <include refid="tableName"/>
        WHERE `id`=#{channelId}
        AND   `valid`=1
        LIMIT 1
    </select>

    <select id="listByIds" resultType="com.shuidihuzhu.cf.model.admin.channel.CfChannel">
        SELECT <include refid="selectFields"/>
        FROM <include refid="tableName"/>
        WHERE
        `id` in
        <foreach collection="ids" close=")" separator="," open="(" item="item">
            #{item}
        </foreach>
        AND
        `valid` = 1
    </select>

    <select id="listByGroupId" resultType="com.shuidihuzhu.cf.model.admin.channel.CfChannel">
        SELECT <include refid="selectFields"/>
        FROM <include refid="tableName"/>
        WHERE
          `group_id` = #{groupId}
        AND
          `id` <![CDATA[ <= ]]> #{anchorId}
        AND
          `valid` = 1
        ORDER BY  `id` DESC
        LIMIT #{limit}
    </select>

    <select id="list" resultType="com.shuidihuzhu.cf.model.admin.channel.CfChannel">
        SELECT <include refid="selectFields"/>
        FROM <include refid="tableName"/>
        WHERE
        `id` <![CDATA[ <= ]]> #{anchorId}
        AND
        `valid` = 1
        ORDER BY `id` DESC
        LIMIT #{limit}
    </select>

    <select id="listByGroupIdAndPage" resultType="com.shuidihuzhu.cf.model.admin.channel.CfChannel">
        SELECT <include refid="selectFields"/>
        FROM <include refid="tableName"/>
        WHERE
        `group_id` = #{groupId}
        AND
        `valid` = 1
        ORDER BY `id` DESC
    </select>

    <select id="listByPage" resultType="com.shuidihuzhu.cf.model.admin.channel.CfChannel">
        SELECT <include refid="selectFields"/>
        FROM <include refid="tableName"/>
        WHERE
        `valid` = 1
        ORDER BY `id` DESC
    </select>

    <update id="update">
        UPDATE <include refid="tableName"/>
        SET
          `group_id`=#{groupId},
          `description`=#{description}
        WHERE
          `id`=#{channelId}
        LIMIT 1
    </update>

    <select id="prefixSearch" resultType="com.shuidihuzhu.cf.model.admin.channel.CfChannel">
        SELECT <include refid="selectFields"/>
        FROM <include refid="tableName"/>
        WHERE
        <if test="groupId > 0">
          `group_id`=#{groupId}
          AND
        </if>
        `name` LIKE CONCAT('%', #{keyword}, '%')
        ORDER BY `id` DESC
    </select>

    <select id="prefixSearchWithoutKeyword" resultType="com.shuidihuzhu.cf.model.admin.channel.CfChannel">
        SELECT <include refid="selectFields"/>
        FROM <include refid="tableName"/>
        WHERE
        <if test="groupId >= 0">
            `group_id`=#{groupId}
        </if>
        ORDER BY `id` DESC
    </select>

</mapper>
