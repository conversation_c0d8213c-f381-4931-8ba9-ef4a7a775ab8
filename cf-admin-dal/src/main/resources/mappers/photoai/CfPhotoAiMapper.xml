<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.photoai.CfPhotoAiDao">
    <sql id="tableName">
		cf_ai_photo_info
	</sql>

    <sql id="insertFields">
        (`info_id`, `idcard_photo_url`, `only_idcard_photo_url`, `img_similarity_score`, `shots_score`, `artwork_score`,
            `name_score`, `id_score`, `status`, `name`, `id_number`, `description`,`photo_type`, `id_card_number`)
    </sql>

    <insert id="savePhotoInfo" parameterType="com.shuidihuzhu.cf.model.aiphoto.PhotoAiInfoModel">
        INSERT INTO
        <include refid="tableName"/>
        <include refid="insertFields"/>
        VALUES
        (#{infoId}, #{idCard}, #{onlyIdCard}, #{imageSimilarityScore}, #{shotsScore}, #{artworkScore}, #{nameScore}, #{idScore}, #{status}, #{name}, #{idNumber}, #{description},#{photoType}, #{idCardNumber})
    </insert>

    <update id="deletePhotoAiInfo">
        update
        <include refid="tableName"/>
        set
        `is_delete` = 1
        where `info_id` = #{infoId} and photo_type=#{photoType}
    </update>

    <select id="checkAIPhotoExist" resultType="java.lang.Integer">
        select count(*)
        from
        <include refid="tableName"/>
        where
        `info_id` = #{infoId} and
        `is_delete` = 0 and photo_type=#{photoType}
    </select>

    <select id="selectPhotoStatus" resultType="com.shuidihuzhu.cf.model.admin.PhotoStatus">
        select
         `status`, `description`, `artificial_res`, `name`, `id_number`, `id_card_number`
         from <include refid="tableName"/>
         where `info_id` = #{infoId}
        and `is_delete` = 0 and photo_type=#{photoType}
        order by `create_time`
        desc
        limit 0,1
    </select>

    <update id="updateArtificialRes">
        update
        <include refid="tableName"/>
        set
        `artificial_res` = #{artificialRes}
        where
        `info_id` = #{infoId} and photo_type=#{photoType}
        and  `is_delete` = 0
    </update>
</mapper>