<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.oneservice.TidbDirectQueryDao">

    <select id="getAd" resultType="java.util.HashMap">
        SELECT
        dt AS dt,
        sum(exposure_pv) AS exposure_pv,
        sum(click_pv) AS click_pv,
        sum(income) AS income
        FROM shuidi_fundraising.rpt_cf_external_page_ad_stat_d
        WHERE 1 = 1
        <choose>
            <when test="operate == '>='">
                AND dt >= #{dateStr}
            </when>
            <otherwise>
                AND dt = #{dateStr}
            </otherwise>
        </choose>
        GROUP BY dt
        LIMIT 0, 2000
    </select>
    <select id="getBaoFei" resultType="java.util.HashMap">
        SELECT
        stat_date AS stat_date,
        target_channel AS target_channel,
        sum(order_num) AS order_num,
        sum(scale_premium) AS scale_premium
        FROM shuidi_fundraising.rpt_sdb_px_target_finish_full_d
        WHERE 1 = 1
        <choose>
            <when test="operate == '>='">
                AND stat_date >= #{dateStr}
            </when>
            <otherwise>
                AND stat_date = #{dateStr}
            </otherwise>
        </choose>
        AND target_channel = '短险-筹'
        GROUP BY stat_date,
        target_channel
        LIMIT 0, 2000
    </select>
</mapper>