<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.risk.DishonestDao">



    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.risk.Dishonest">
        INSERT INTO crowdfunding_dishonest(user_id,user_type,user_name,user_id_card,dishonest,case_id)
        VALUES(#{userId},#{userType},#{username},#{userIdCard},#{dishonest},#{caseId});
    </insert>

    <select id="getDishonestInfo" resultType="com.shuidihuzhu.cf.model.risk.Dishonest" >
        SELECT * FROM crowdfunding_dishonest
        <where>
            case_id = #{caseId}
            AND user_type =#{userType}
            <if test="name != null">
                AND user_name=#{name}
            </if>
            AND is_delete = 0
        </where>
        ORDER BY id desc LIMIT 1
    </select>
</mapper>