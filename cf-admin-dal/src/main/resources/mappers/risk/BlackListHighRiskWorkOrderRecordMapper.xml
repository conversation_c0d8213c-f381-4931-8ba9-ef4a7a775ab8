<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.risk.BlackListHighRiskWorkOrderRecordDao">
    <sql id="TABLE_NAME">
        black_list_high_risk_work_order_record
    </sql>

    <sql id="INSERT_FIELDS">
        (`case_id`, `work_order_id`, `black_list_id`, `hit_type`, `hit_id_card`)
    </sql>

    <sql id="SELECT_FIELDS">
        `id`, `case_id`, `work_order_id`, `black_list_id`, `hit_type`, `hit_id_card`, `is_delete`, `create_time`, `update_time`
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.risk.BlackListHighRiskWorkOrderRecord" keyProperty="id" useGeneratedKeys="true">
        insert into <include refid="TABLE_NAME"/>
        <include refid="INSERT_FIELDS"/>
        values(#{caseId}, #{workOrderId}, #{blackListId}, #{hitType}, #{hitIdCard})
    </insert>


    <select id="getByWorkOrderId" resultType="com.shuidihuzhu.cf.model.risk.BlackListHighRiskWorkOrderRecord">
        select <include refid="SELECT_FIELDS"/>
        from <include refid="TABLE_NAME"/>
        where work_order_id = #{workOrderId}
    </select>


</mapper>