<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.risk.BanShareWhiteListDao">

    <sql id="tableName">
        ban_share_white_list
    </sql>

    <sql id="insert_fields">
        `id`,
        `user_id`,
        `operator_name`,
        `valid_time`,
        `max_count`,
        `valid_state`,
        `operate_reason`,
        `create_time`,
        `is_delete`
    </sql>
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into <include refid="tableName" />
        (`user_id`,`operator_name`,`valid_time`,`max_count`,`operate_reason`)
        values
        (#{banShareWhiteListDO.userId},#{banShareWhiteListDO.operatorName},#{banShareWhiteListDO.validTime},#{banShareWhiteListDO.maxCount},#{banShareWhiteListDO.operateReason})
    </insert>
    <update id="updateMaxCount">
        update <include refid="tableName"/>
        set `max_count` = #{maxCount}, `valid_time` = #{date},`valid_state` = 1
        where `id` = #{id}
    </update>
    <update id="updateState">
        update <include refid="tableName"/>
        set `valid_state` = 2
    </update>
    <select id="getList" resultType="com.shuidihuzhu.cf.model.risk.BanShareWhiteListDO">
        select <include refid="insert_fields"/>
        from <include refid="tableName"/>
        where `user_id` = #{cfUserId}
            and `is_delete` = 0
        order by `id` desc
    </select>
    <select id="getByUserIdAndState" resultType="com.shuidihuzhu.cf.model.risk.BanShareWhiteListDO">
        select <include refid="insert_fields"/>
        from <include refid="tableName"/>
        where `user_id` = #{cfUserId}
        and `valid_state` = #{state}
    </select>
    <select id="getById" resultType="com.shuidihuzhu.cf.model.risk.BanShareWhiteListDO">
        select <include refid="insert_fields"/>
        from <include refid="tableName"/>
        where `id` = #{id}
    </select>
    <select id="getAll" resultType="com.shuidihuzhu.cf.model.risk.BanShareWhiteListDO">
        select <include refid="insert_fields"/>
        from <include refid="tableName"/>
        <where>
            `is_delete` = 0
        </where>
    </select>
    <select id="getByState" resultType="com.shuidihuzhu.cf.model.risk.BanShareWhiteListDO">
        select <include refid="insert_fields"/>
        from <include refid="tableName"/>
        <where>
            `valid_state` = #{state}
        </where>
    </select>
    <select id="getByUserId" resultType="com.shuidihuzhu.cf.model.risk.BanShareWhiteListDO">
        select <include refid="insert_fields"/>
        from <include refid="tableName"/>
        where `user_id` = #{cfUserId}
    </select>
    <select id="getByCondition" resultType="com.shuidihuzhu.cf.model.risk.BanShareWhiteListDO">
        select <include refid="insert_fields"/>
        from <include refid="tableName"/>
        <where>
            <if test="cfUserId != 0">
                AND `user_id` = #{cfUserId}
            </if>
            <if test="name != null and name != ''">
                AND `operator_name` = #{name}
            </if>
            <if test="state == 1">
                AND `valid_state` = #{state}
            </if>
            <if test="state == 2">
                AND `valid_state` = #{state}
            </if>
        </where>
        order by `id` desc
    </select>

</mapper>