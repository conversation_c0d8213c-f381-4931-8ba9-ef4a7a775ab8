<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.duizhang.AdminPAKHKF03DuiZhangDetailDao">

	<sql id="TABLE">
		pa_pay_khkf03_duizhang_detail_record
	</sql>

	<sql id="QUERY_FIELDS">
		`id`, `file_date`, `trade_date`, `trade_time`, `settle_date`, `trade_no`,
		`batch_no`, `in_acc_no`, `amount`, `fee`, `account_date`, `account_no`,
		`error_no`, `error_msg`, `remark`
	</sql>

	<sql id="INSERT_FIELDS">
		`file_date`, `trade_date`, `trade_time`, `settle_date`, `trade_no`,
		`batch_no`, `in_acc_no`, `amount`, `fee`, `account_date`,
		`account_no`, `error_no`, `error_msg`, `remark`
	</sql>

	<insert id="add" parameterType="com.shuidihuzhu.client.baseservice.pay.model.pingan.duizhang.PADuiZhangDetail">
		INSERT INTO
		<include refid="TABLE"/>
		(<include refid="INSERT_FIELDS"/>)
		VALUES
		<foreach collection="details" item="item" separator=",">
			(#{item.fileDate}, #{item.tradeDate}, #{item.tradeTime}, #{item.settleDate}, #{item.tradeNo},
			#{item.batchNo}, #{item.inAccNo}, #{item.amount}, #{item.fee}, #{item.accountDate},
			#{item.accountNo}, #{item.errorNo}, #{item.errorMsg}, #{item.remark})
		</foreach>
	</insert>

	<update id="delete">
		UPDATE
		<include refid="TABLE"/>
		SET `is_delete`=1
		WHERE `file_date`=#{fileDate}
	</update>

	<select id="getByFileDate" resultType="com.shuidihuzhu.client.baseservice.pay.model.pingan.duizhang.PADuiZhangDetail">
		SELECT <include refid="QUERY_FIELDS"/>
		FROM <include refid="TABLE"/>
		WHERE `file_date`=#{fileDate} AND `is_delete`=0
		limit #{offset},#{limit}
	</select>

	<select id="getByTradeNos" resultType="com.shuidihuzhu.client.baseservice.pay.model.pingan.duizhang.PADuiZhangDetail">
		SELECT <include refid="QUERY_FIELDS"/>
		FROM <include refid="TABLE"/>
		WHERE `is_delete`= 0 AND
		`trade_no` IN
		<foreach collection="tradeNos" item="tradeNo" open="(" close=")" separator=",">
			#{tradeNo}
		</foreach>
	</select>

</mapper>