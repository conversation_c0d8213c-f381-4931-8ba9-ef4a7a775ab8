<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.duizhang.AdminPAKHKF03ChaCuoDetailDao">

	<sql id="TABLE">
		pa_pay_khkf03_chacuo_detail_record
	</sql>

	<sql id="QUERY_FIELDS">
		`id`, `file_date`, `ret_back_date`, `settle_date`, `in_acc_no`, `in_acc_name`, `bank_flow_no`,
		`amount`, `request_date`, `request_trade_no`, `batch_no`, `status`, `original_remark`,
		`remark`
	</sql>

	<sql id="INSERT_FIELDS">
		`file_date`, `ret_back_date`, `settle_date`, `in_acc_no`, `in_acc_name`, `bank_flow_no`,
		`amount`, `request_date`, `request_trade_no`, `batch_no`, `status`, `original_remark`,
		`remark`
	</sql>
	
	<insert id="add" parameterType="com.shuidihuzhu.client.baseservice.pay.model.pingan.duizhang.PAChacuoDetail">
		INSERT INTO
		<include refid="TABLE"/>
		(<include refid="INSERT_FIELDS"/>)
		VALUES
		<foreach collection="details" item="item" separator=",">
			(
			#{item.fileDate}, #{item.retBackDate}, #{item.settleDate}, #{item.inAccNo}, #{item.inAccName},
			#{item.bankFlowNo},
			#{item.amount}, #{item.requestDate}, #{item.requestTradeNo},
			#{item.batchNo}, #{item.status}, #{item.originalRemark},
			#{item.remark}
			)
		</foreach>
	</insert>

	<update id="delete">
		UPDATE
		<include refid="TABLE"/>
		SET `is_delete`=1
		WHERE `file_date`=#{fileDate}
	</update>

	<select id="get" resultType="com.shuidihuzhu.client.baseservice.pay.model.pingan.duizhang.PAChacuoDetail">
		SELECT
		<include refid="QUERY_FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE `file_date`=#{fileDate} AND `is_delete`=0
		limit #{offset},#{limit}
	</select>


	<select id="getByFlowNo" resultType="com.shuidihuzhu.client.baseservice.pay.model.pingan.duizhang.PAChacuoDetail">
		SELECT <include refid="QUERY_FIELDS"/>
		FROM <include refid="TABLE"/>
		WHERE `is_delete`=0 AND
		`request_trade_no` IN
		<foreach collection="flowNos" item="flowNo" open="(" close=")" separator=",">
			#{flowNo}
		</foreach>
	</select>

</mapper>