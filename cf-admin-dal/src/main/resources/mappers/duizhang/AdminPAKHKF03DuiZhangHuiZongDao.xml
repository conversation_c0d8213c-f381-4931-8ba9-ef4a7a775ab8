<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.duizhang.AdminPAKHKF03DuiZhangHuiZongDao">

	<sql id="TABLE">
		`pa_pay_khkf03_duizhang_huizong_record`
	</sql>

	<sql id="QUERY_FIELDS">
		`id`, `file_date`, `total_count`, `total_money`, `success_count`,
		`success_money`, `fail_count`, `fail_money`
	</sql>

	<sql id="INSERT_FIELDS">
		`file_date`, `total_count`, `total_money`, `success_count`,
		`success_money`, `fail_count`, `fail_money`
	</sql>

	<insert id="add" parameterType="com.shuidihuzhu.client.baseservice.pay.model.pingan.duizhang.PADuizhangHuiZong">
		INSERT INTO
		<include refid="TABLE"/>
		(<include refid="INSERT_FIELDS"/>)
		VALUES (#{fileDate}, #{totalCount}, #{totalMoney}, #{successCount}, #{successMoney},
		#{failCount}, #{failMoney})
	</insert>

	<update id="delete">
		UPDATE
		<include refid="TABLE"/>
		SET `is_delete`=1
		WHERE `file_date`=#{fileDate}
	</update>

	<select id="get">
		SELECT
		<include refid="QUERY_FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE `file_date`=#{fileDate} AND `is_delete`=0
		ORDER BY `id` DESC limit 1
	</select>


</mapper>