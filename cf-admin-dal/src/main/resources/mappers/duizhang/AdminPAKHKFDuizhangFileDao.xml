<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.duizhang.AdminPAKHKFDuizhangFileDao">

	<sql id="TABLE">
		pa_pay_khkf03_duizhang_file_record
	</sql>

	<sql id="QUERY_FIELDS">
		`id`, `trade_sn`, `file_date`, `file_type`, `stt`, `file_name`, `file_path`,
		`random_pwd`, `success`
	</sql>

	<sql id="INSERT_FIELDS">
		`trade_sn`, `file_date`, `file_type`, `stt`, `file_name`, `file_path`,
		`random_pwd`, `success`
	</sql>

	<insert id="add" parameterType="com.shuidihuzhu.client.baseservice.pay.model.pingan.PADuiZhangFile">
		INSERT INTO
		<include refid="TABLE"/>
		(<include refid="INSERT_FIELDS"/>)
		VALUES (#{tradeSn}, #{fileDate}, #{fileType}, #{stt}, #{fileName}, #{filePath},
		#{randomPwd}, #{success})
	</insert>

	<select id="get" resultType="com.shuidihuzhu.client.baseservice.pay.model.pingan.PADuiZhangFile">
		SELECT
		<include refid="QUERY_FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE `file_date`=#{fileDate} AND `file_type`=#{fileType} AND `is_delete`=0
		ORDER BY `id` DESC limit 1
	</select>

	<update id="updateTradeSn">
		UPDATE
		<include refid="TABLE"/>
		SET `trade_sn`=#{tradeSN}, `file_path`=#{filePath}
		WHERE `id`=#{id}
	</update>
	
	<update id="updateSuccess">
		UPDATE
		<include refid="TABLE"/>
		SET `success`=1
		WHERE `id`=#{id}
	</update>

</mapper>