<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.duizhang.AdminPAKHKF03ChaCuoHuiZongDao">

	<sql id="TABLE">
		pa_pay_khkf03_chacuo_huizong_record
	</sql>

	<sql id="QUERY_FIELDS">
		`id`, `file_date`, `ret_back_date`, `total_count`, `total_amount`, `remark`
	</sql>

	<sql id="INSERT_FIELDS">
		`file_date`, `ret_back_date`, `total_count`, `total_amount`, `remark`
	</sql>

	<insert id="add" parameterType="com.shuidihuzhu.client.baseservice.pay.model.pingan.duizhang.PAChacuoHuiZong">
		INSERT INTO
		<include refid="TABLE"/>
		(<include refid="INSERT_FIELDS"/>)
		VALUES (#{fileDate}, #{retBackDate}, #{totalCount}, #{totalAmount}, #{remark})
	</insert>

	<update id="delete">
		UPDATE
		<include refid="TABLE"/>
		SET `is_delete`=1 WHERE `file_date`=#{fileDate}
	</update>

	<select id="get" resultType="com.shuidihuzhu.client.baseservice.pay.model.pingan.duizhang.PAChacuoHuiZong">
		SELECT
		<include refid="QUERY_FIELDS"/>
		FROM <include refid="TABLE" />
		WHERE `file_date`=#{fileDate} AND `is_delete`=0
	</select>


</mapper>