<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.tdsql.TdAdminCrowdfundingCommentDao">
    <sql id="tableName">
        crowdfunding_comment_new
	</sql>

    <sql id="fields">
		`id`, `parent_id`,`user_id`,`user_third_id`,`user_third_type`,`comment_id`,`content`,`type`,`is_deleted`,`create_time`,`crowdfunding_id`
	</sql>

    <select id="getByPage" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingComment" parameterType="com.shuidihuzhu.common.web.util.admin.BasicExample">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        <include refid="com.shuidihuzhu.cf.dao.message.Example_Where_Clause"/>
    </select>

    <select id="getCommentByParentIdFromTiDb" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingComment">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE `parent_id`=#{parentId}
        LIMIT #{limit}
    </select>

    <select id="getCommentByUserIdAndTypeFromTiDb" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingComment">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE `user_id`=#{userId}
        AND `type` = #{type}
        LIMIT #{limit}
    </select>

    <select id="getCommentByUserIdFromTiDb" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingComment">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE `user_id`=#{userId}
        LIMIT #{limit}
    </select>

    <select id="selectCountByMin" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM <include refid="tableName"/>
        WHERE <![CDATA[ `create_time`>=#{begin} ]]> AND <![CDATA[ `create_time`<#{end} ]]>
    </select>
</mapper>
