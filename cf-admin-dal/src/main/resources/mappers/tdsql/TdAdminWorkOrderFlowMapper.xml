<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.tdsql.TdAdminWorkOrderFlowDao">

    <sql id="table_name">
        `admin_work_order_flow`
    </sql>

    <sql id="insert_fields">
        `work_order_id`,
        `problem_type`,
        `problem_content`,
        `problem_img`,
        `handle_img`,
        `case_id`,
        `second_classify_Id`,
        `new_first_classify_id`,
        `user_identity`,
        `asssign_channel`,
        `jingxi_channel`,
        `encrypt_mobile`
    </sql>

    <sql id="select_fields">
        `id`,
        `work_order_id`,
        `problem_type`,
        `problem_content`,
        `problem_img`,
        `handle_img`,
        `case_id`,
        `second_classify_Id`,
        `new_first_classify_id`,
        `new_second_classify_id`,
        `new_third_classify_id`,
        `user_identity`,
        `create_time`,
        `update_time`,
        `encrypt_mobile`,
        `follow_tags`,
        `jingxi_channel`,
        city_id,
        city_name,
        province_id,
        province_name,
        county_id,
        county_name,
        hospital,
        hospital_id
    </sql>

    <sql id="base_select_fields">
        `id`,
        `work_order_id`,
        `problem_type`,
        `problem_content`,
        `problem_img`,
        `handle_img`,
        `case_id`,
        `second_classify_Id`,
        `new_first_classify_id`,
        `new_second_classify_id`,
        `new_third_classify_id`,
        `jingxi_channel`,
        `encrypt_mobile`,
        `follow_tags`
    </sql>

    <select id = "countFinishFlowByParam" resultType="java.lang.Integer">
        SELECT count(*)
        FROM <include refid="table_name"/> awof
        INNER JOIN
        ( SELECT DISTINCT flow_id from admin_work_order_flow_record WHERE
        `operator_id`=#{recordOperatorId}

        <if test="recordOperatorTypes != null and recordOperatorTypes.size() > 0">
            AND operate_type IN
            <foreach collection="recordOperatorTypes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ) awofr on awofr.flow_id = awof.id
        INNER JOIN admin_work_order awo ON awof.work_order_id = awo.id

        <where>
            <if test="flowId != null and flowId != 0">
                AND awof.id = #{flowId}
            </if>

            <if test="caseId != null">
                AND awof.case_id = #{caseId}
            </if>

            <if test="encryptMobile != null and encryptMobile != ''">
                AND awof.encrypt_mobile = #{encryptMobile}
            </if>

            <if test="level != null">
                AND awo.level = #{level}
            </if>

            <if test="updateStartTime != null">
                AND awo.update_time >= #{updateStartTime}
            </if>

            <if test="updateEndTime != null">
                AND awo.update_time  <![CDATA[ <= ]]> #{updateEndTime}
            </if>

            <if test="secondClassifyIds != null and secondClassifyIds.size() > 0">
                AND awof.second_classify_id IN
                <foreach collection="secondClassifyIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="workOrderStatusList != null and workOrderStatusList.size() > 0">
                AND awo.order_status IN
                <foreach collection="workOrderStatusList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="followTag != null">
                AND awof.follow_tags LIKE  CONCAT('%', ',', #{followTag}, ',', '%')
            </if>
        </where>
    </select>

    <select id = "selectFinishFlowByParam" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView">
        SELECT
        awof.`id`,
        awof.`work_order_id`,
        awof.`problem_type`,
        awof.`problem_content`,
        awof.`problem_img`,
        awof.`handle_img`,
        awof.`encrypt_mobile`,
        awof.`case_id`,
        awof.`second_classify_Id`,
        awof.`follow_tags`,
        awo.order_status as workOrderStatus,
        awo.level,
        awo.comment,
        awo.operator_id,
        awo.creator_id,
        awo.`create_time`,
        awo.`update_time`
        FROM <include refid="table_name"/> awof
        INNER JOIN
        ( SELECT DISTINCT flow_id from admin_work_order_flow_record WHERE
        `operator_id`=#{recordOperatorId}

        <if test="recordOperatorTypes != null and recordOperatorTypes.size() > 0">
            AND operate_type IN
            <foreach collection="recordOperatorTypes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ) awofr on awofr.flow_id = awof.id
        INNER JOIN admin_work_order awo ON awof.work_order_id = awo.id

        <where>
            <if test="flowId != null and flowId != 0">
                AND awof.id = #{flowId}
            </if>

            <if test="caseId != null">
                AND awof.case_id = #{caseId}
            </if>

            <if test="encryptMobile != null and encryptMobile != ''">
                AND awof.encrypt_mobile = #{encryptMobile}
            </if>

            <if test="level != null">
                AND awo.level = #{level}
            </if>

            <if test="updateStartTime != null">
                AND awo.update_time >= #{updateStartTime}
            </if>

            <if test="updateEndTime != null">
                AND awo.update_time  <![CDATA[ <= ]]> #{updateEndTime}
            </if>

            <if test="secondClassifyIds != null and secondClassifyIds.size() > 0">
                AND awof.second_classify_id IN
                <foreach collection="secondClassifyIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="newFirstClassifyId != null and newFirstClassifyId != 0">
                AND awof.new_first_classify_id = #{newFirstClassifyId}
            </if>
            <if test="newSecondClassifyId != null and newSecondClassifyId != 0">
                AND awof.new_second_classify_id = #{newSecondClassifyId}
            </if>
            <if test="newThirdClassifyId != null and newThirdClassifyId != 0">
                AND awof.new_third_classify_id = #{newThirdClassifyId}
            </if>
            <if test="workOrderStatusList != null and workOrderStatusList.size() > 0">
                AND awo.order_status IN
                <foreach collection="workOrderStatusList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        <if test="followTag != null">
            AND awof.follow_tags LIKE  CONCAT('%', ',', #{followTag}, ',', '%')
        </if>
        ORDER BY awo.update_time DESC limit #{limitSize}, #{offset}
    </select>

</mapper>