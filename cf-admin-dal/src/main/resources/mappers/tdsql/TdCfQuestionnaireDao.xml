<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.tdsql.TdCfQuestionnaireDao">

    <select id="getList" resultType="com.shuidihuzhu.cf.model.admin.CfQuestionnaire">
        SELECT *
        FROM cf_questionnaire
        where is_delete = 0

        <if test="anchor != 0">
            <if test="isPre == true">
                AND id  <![CDATA[ < ]]> #{anchor}
            </if>
            <if test="isPre == false">
                AND id  <![CDATA[ > ]]> #{anchor}
            </if>
        </if>

        <if test="qid != null and qid !=''">
            AND q_id=#{qid}
        </if>

        <if test="qname !=null and qname != ''">
            AND q_name=#{qname}
        </if>

        <if test="userId != 0">
            AND user_id=#{userId}
        </if>

        <if test="name != null and name !=''">
            AND name=#{name}
        </if>

        <if test="channel != null and channel !=''">
            AND channel=#{channel}
        </if>

        <if test="source != null and source !=''">
            AND source=#{source}
        </if>

        <if test="status >= 0">
            AND q_status=#{status}
        </if>

        <if test="encryptMobile != null and encryptMobile != ''">
            AND mobile=#{encryptMobile}
        </if>

        <if test="startTime != null and startTime !='' and endTime != null and endTime !='' ">
            AND send_time between #{startTime} and #{endTime}
        </if>

        <if test="recordId > 0">
            AND record_id=#{recordId}
        </if>

        <if test="cfQid != 0">
            and <![CDATA[ `id`<=#{cfQid} ]]>
        </if>

        <if test="isPre == true">
            order by id desc
        </if>

        <if test="isPre == false">
            order by id
        </if>

        <if test="pageSize > 0">
            limit #{pageSize}
        </if>

        <if test="size != 0">
            LIMIT #{size}
        </if>

    </select>

    <select id="total" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM cf_questionnaire
        where is_delete = 0

        <if test="qid != null and qid !=''">
            AND q_id=#{qid}
        </if>

        <if test="userId != 0">
            AND user_id=#{userId}
        </if>

        <if test="qname !=null and qname != ''">
            AND q_name=#{qname}
        </if>

        <if test="name != null and name !=''">
            AND name=#{name}
        </if>

        <if test="channel != null and channel !=''">
            AND channel=#{channel}
        </if>

        <if test="source != null and source !=''">
            AND source=#{source}
        </if>

        <if test="status >= 0">
            AND q_status=#{status}
        </if>

        <if test="encryptMobile != null and encryptMobile != ''">
            AND mobile=#{encryptMobile}
        </if>

        <if test="startTime != null and startTime !='' and endTime != null and endTime !='' ">
            AND send_time between #{startTime} and #{endTime}
        </if>

        <if test="recordId > 0">
            AND record_id=#{recordId}
        </if>

    </select>
</mapper>
