<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.tdsql.TdDataStatDao">

    <select id="getPaySuccessCountByTime" resultType="java.lang.Long">
        SELECT
        COALESCE(count(1) ,0) as paySuccessCount
        FROM
        crowdfunding_order_crowdfundingid_sharding
        WHERE
        `pay_time` >= #{startTime} and  <![CDATA[ `pay_time` <= #{endTime} ]]>
        AND `pay_status` = 1
    </select>

</mapper>