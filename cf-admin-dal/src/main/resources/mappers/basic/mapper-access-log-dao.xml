<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.basic.IAccessLogDAO" >
    <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.domain.basic.AccessLogDO">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="user_id" property="userId" jdbcType="INTEGER"/>
        <result column="page_name" property="pageName" jdbcType="VARCHAR"/>
        <result column="user_name" property="userName" jdbcType="VARCHAR"/>
        <result column="org_name" property="orgName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="INSERT_Column_List">
      id,user_id,page_name,user_name,org_name
    </sql>
    <sql id="Base_Column_List">
      id,user_id,page_name,user_name,org_name,create_time,update_time
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.domain.basic.AccessLogDO">
        <selectKey order="AFTER" resultType="long" keyProperty="id">
            select last_insert_id() as id
        </selectKey>
        insert into sea_access_log(<include refid="INSERT_Column_List" />)
        values(
        #{id,jdbcType=INTEGER},
        #{userId,jdbcType=INTEGER},
        #{pageName,jdbcType=VARCHAR},
        #{userName,jdbcType=VARCHAR},
        #{orgName,jdbcType=VARCHAR}
        )
    </insert>

</mapper>