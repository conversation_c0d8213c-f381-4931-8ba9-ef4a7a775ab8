<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.stat.message.MpNewsOperatingRecordDao">

    <sql id="table">
        `mpnews_operating_record`
    </sql>

    <sql id="insert_field">
        `assignment_id`,
        `subtask_id`,
        `type`,
        `user_id`,
        `user_name`,
        `comment`
    </sql>

    <sql id="select_field">
        `id`,
        `assignment_id`,
        `subtask_id`,
        `type`,
        `user_id`,
        `user_name`,
        `comment`,
        `create_time`
    </sql>


    <insert id="insertOperatingRecord" parameterType="com.shuidihuzhu.cf.model.message.MpNewsOperatingRecord">
        INSERT INTO
        <include refid="table"/>
        (<include refid="insert_field"/>)
        VALUES (#{assignmentId},#{subtaskId},#{type},#{userId},#{userName},#{comment})
    </insert>

    <select id="getByTaskId" resultType="com.shuidihuzhu.cf.model.message.MpNewsOperatingRecord">
        select
        <include refid="select_field"/>
        from
        <include refid="table"/>
        where `assignment_id`=#{taskId}
        ORDER BY `create_time` DESC
        limit #{offset},#{limit}
    </select>

    <select id="countOperatingRecordByTaskId" resultType="java.lang.Integer">
        select COUNT(*)
        from
        <include refid="table"/>
        where `assignment_id`=#{taskId}
    </select>

    <select id="getByTaskIdAndType" resultType="com.shuidihuzhu.cf.model.message.MpNewsOperatingRecord">
        select
        <include refid="select_field"/>
        from
        <include refid="table"/>
        where `assignment_id`=#{taskId}
        and `type`  = #{type}
        ORDER BY `create_time` DESC
        limit #{offset},#{limit}
    </select>

    <select id="getByUserNameAndType" resultType="com.shuidihuzhu.cf.model.message.MpNewsOperatingRecord">
        select
        <include refid="select_field"/>
        from
        <include refid="table"/>
        where `user_name`=#{userName}
        and `type`  = #{type}
        ORDER BY `create_time` DESC
        limit #{offset},#{limit}
    </select>
</mapper>