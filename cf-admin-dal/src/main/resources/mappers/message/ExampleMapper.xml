<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.message">
    <sql id="Example_Where_Clause">
        <if test="_parameter != null">
            <where>
                <foreach collection="oredCriteria" item="criteria" separator="or">
                    <if test="criteria.valid">
                        <trim prefix="(" suffix=")" prefixOverrides="and">
                            <foreach collection="criteria.criteria" item="criterion">
                                <choose>
                                    <when test="criterion.noValue">
                                        and ${criterion.condition}
                                    </when>
                                    <when test="criterion.singleValue">
                                        and ${criterion.condition} #{criterion.value}
                                    </when>
                                    <when test="criterion.betweenValue">
                                        and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                    </when>
                                    <when test="criterion.listValue">
                                        and ${criterion.condition}
                                        <foreach collection="criterion.value" item="listItem" open="(" close=")"
                                                 separator=",">
                                            #{listItem}
                                        </foreach>
                                    </when>
                                </choose>
                            </foreach>
                        </trim>
                    </if>
                </foreach>
            </where>
        </if>
        <if test="groupByClause != null">
            group by ${groupByClause}
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limitClause != null">
            limit ${limitClause}
        </if>
    </sql>
</mapper>