<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfCommitVerifyItemDao">

    <sql id="tableName">
        cf_commit_verify_item
    </sql>

    <resultMap id="CfRefuseReasonMap" type="com.shuidihuzhu.cf.model.crowdfunding.CfCommitVerifyItem" autoMapping="true">
        <id column="ccvi_id" property="id"/>
        <result column="ccvi_pid" property="pid"/>
        <result column="ccvi_describe" property="describe"/>
        <collection property="cfRefuseReasonItems" ofType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonItem">
            <id column="crri_id" property="id"/>
            <result column="crri_content" property="content"/>
            <result column="crri_type" property="type"/>
            <collection property="cfRefuseReasons" ofType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReason">
                <id column="crr_id" property="id"/>
                <result column="crr_pid" property="pid"/>
                <result column="crr_content" property="content"/>
            </collection>
        </collection>
    </resultMap>

    <select id="selectRefuseList" resultMap="CfRefuseReasonMap">
        SELECT
            ccvi.id AS ccvi_id,
            ccvi.pid AS ccvi_pid,
            ccvi.describe AS ccvi_describe,
            crri.id AS crri_id,
            crri.content AS crri_content,
            crri.type AS crri_type,
            crr.id AS crr_id,
            crr.pid AS crr_pid,
            crr.content AS crr_content
        FROM
            (SELECT type FROM crowdfunding_info_status WHERE info_uuid=#{infoUuid}) cist
            LEFT JOIN
            cf_commit_verify_item ccvi ON cist.type = ccvi.id
            LEFT JOIN
            cf_refuse_reason_item crri ON ccvi.id = crri.type
            LEFT JOIN
            cf_refuse_reason crr ON crri.id = crr.pid
        ORDER BY ccvi.id , crri.pro_type , crri.type , crri.group_rank , crr.id;
    </select>

    <select id="selectBaseInfoRefuseList" resultMap="CfRefuseReasonMap">
        SELECT
        ccvi.id AS ccvi_id,
        ccvi.pid AS ccvi_pid,
        ccvi.describe AS ccvi_describe,
        crri.id AS crri_id,
        crri.content AS crri_content,
        crri.type AS crri_type,
        crr.id AS crr_id,
        crr.pid AS crr_pid,
        crr.content AS crr_content
        FROM
        cf_commit_verify_item ccvi
        LEFT JOIN
        cf_refuse_reason_item crri ON ccvi.id = crri.type
        LEFT JOIN
        cf_refuse_reason crr ON crri.id = crr.pid
        WHERE ccvi.id = 1
        ORDER BY ccvi.id , crri.pro_type , crri.type , crri.group_rank;
    </select>

    <select id="selectRefusesByList" resultMap="CfRefuseReasonMap">
        SELECT
        ccvi.id AS ccvi_id,
        ccvi.pid AS ccvi_pid,
        ccvi.describe AS ccvi_describe,
        crri.id AS crri_id,
        crri.content AS crri_content,
        crri.type AS crri_type,
        crr.id AS crr_id,
        crr.pid AS crr_pid,
        crr.content AS crr_content
        FROM
        cf_commit_verify_item ccvi
        LEFT JOIN
        cf_refuse_reason_item crri ON ccvi.id = crri.type
        LEFT JOIN
        cf_refuse_reason crr ON crri.id = crr.pid
        WHERE crr.id IN
        <foreach collection="cfRefuseReasonIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY ccvi.id , crri.group_rank , crr.pid;
    </select>

    <select id="selectAllType" resultType="int">
        SELECT `id` FROM <include refid="tableName"/> LIMIT #{start},#{size}
    </select>

    <select id="selectByIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfCommitVerifyItem">
        SELECT `id`,`describe`
        FROM <include refid="tableName"/>
        WHERE `id` IN
        <foreach collection="set" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectAll" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfCommitVerifyItem">
        SELECT * FROM <include refid="tableName"/> LIMIT #{start},#{size}
    </select>
</mapper>