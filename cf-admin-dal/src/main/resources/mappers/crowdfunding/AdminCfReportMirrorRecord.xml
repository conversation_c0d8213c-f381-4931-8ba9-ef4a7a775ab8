<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCfReportMirrorRecordDao">

    <sql id="tableName">
        `cf_report_mirror_record`
    </sql>

    <sql id="insertFields">
        (`info_uuid`,
        `type`,
        `operating_record_id`,
        `report_info`,
        `case_report_status`,
        `report_follow_type`,
        `operating_type`,
        `content`,
        `add_trust_status`,
        `add_trust_info`
        )
    </sql>

    <sql id="selectFields">
        `id`,
        `info_uuid`,
        `type`,
        `operating_record_id`,
        `report_info`,
        `case_report_status`,
        `report_follow_type`,
        `operating_type`,
        `content`,
        `add_trust_status`,
        `add_trust_info`,
        `create_time`,
        `update_time`
    </sql>


    <select id="getByOperationRecordId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportMirrorRecord">
        SELECT <include refid="selectFields"/>
        FROM <include refid="tableName"/>
        WHERE `is_delete` = 0
        AND `operating_record_id` = #{operationRecordId}
    </select>

    <select id="getByInfoUuidAndType" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportMirrorRecord">
        SELECT <include refid="selectFields"/>
        FROM <include refid="tableName"/>
        WHERE `is_delete` = 0
        AND `info_uuid` = #{infoUuid}
        AND `operating_type` = #{role}
        order by id desc limit 1,2
    </select>

</mapper>