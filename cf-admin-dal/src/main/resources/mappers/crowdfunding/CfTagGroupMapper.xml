<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfTagGroupDao">

    <sql id="table_name">
        cf_tag_group
    </sql>

    <sql id="insert_fields">
        `describe`
    </sql>

    <resultMap id="groupResultMap" type="com.shuidihuzhu.cf.model.crowdfunding.CfTagGroup" autoMapping="true">
        <id column="g_id" property="id"/>
        <result column="describe" property="describe"/>
        <result property="type" column="type"/>
        <result property="dateCreated" column="date_created"/>
        <result property="lastModified" column="last_modified"/>
        <collection property="itemList" ofType="com.shuidihuzhu.cf.model.crowdfunding.CfTagItem" >
            <id property="id" column="i_id"/>
            <result property="pid" column="pid"/>
            <result property="tagName" column="tag_name"/>
            <result property="type" column="type"/>
            <result property="dateCreated" column="date_created"/>
            <result property="lastModified" column="last_modified"/>
        </collection>
    </resultMap>

    <select id="getList" resultMap="groupResultMap">
        SELECT
            cg.id AS g_id,
            cg.describe,
            ci.id AS i_id,
            ci.pid,
            ci.tag_name,
            ci.type
        FROM cf_tag_group cg  LEFT JOIN cf_tag_item ci ON cg.id=ci.pid
        WHERE cg.id > 0
        AND cg.type IN
        <foreach collection="types" open="(" close=")" separator="," item="type">
            #{type}
        </foreach>
        AND ci.type = 0
    </select>

    <select id="getById" parameterType="int" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfTagGroup">
        SELECT *
        FROM <include refid="table_name"/>
        WHERE `id`=#{pid}
    </select>
</mapper>