<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingInfoPayeeDao">

    <sql id="TABLE_NAME">
        crowdfunding_info_payee
    </sql>

    <sql id="QUERY_FIELDS">
        `id`,
        `info_uuid` as infoUuid,
        `relation_type` as relationType,
        `name`,
        `id_card` as idCard,
        `id_type` as idType,
        `mobile`,
        `bank_name` as bankName,
        `bank_branch_name` as bankBranchName,
        `bank_card` as bankCard,
        `date_created` as dateCreated,
        `last_modified` as lastModified
    </sql>

    <select id="selectByPayeeName" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoPayee">
        SELECT <include refid="QUERY_FIELDS"/>
        FROM <include refid="TABLE_NAME"/>
        WHERE `name`=#{name}
    </select>

    <select id="selectByInfoUuidList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoPayee">
        SELECT <include refid="QUERY_FIELDS"/>
        FROM <include refid="TABLE_NAME"/>
        WHERE `info_uuid` IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByPayeeIdCard" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoPayee">
        SELECT <include refid="QUERY_FIELDS"/>
        FROM <include refid="TABLE_NAME"/>
        WHERE `id_card`=#{idCard}
    </select>
</mapper>