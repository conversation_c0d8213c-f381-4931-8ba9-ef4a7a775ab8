<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfCaseTagDao">

    <sql id="table_name">
        cf_case_tag
    </sql>

    <sql id="insert_fields">
        `info_id`,
        `tag_id`
    </sql>
    
    <select id="getByInfoId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfCaseTag">
        SELECT *
        FROM <include refid="table_name"/>
        WHERE `info_id`=#{infoId}
    </select>
    
    <insert id="addCaseTags" useGeneratedKeys="true">
        INSERT INTO 
        <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES 
        <foreach collection="tagList" item="item" separator=",">
            (#{item.infoId},#{item.tagId})
        </foreach>
    </insert>

    <delete id="deleteTags" parameterType="int">
        DELETE FROM <include refid="table_name"/>
        WHERE `info_id`=#{infoId}
    </delete>
</mapper>