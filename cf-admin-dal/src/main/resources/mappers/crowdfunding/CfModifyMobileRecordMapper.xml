<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfModifyMobileRecordDao">

    <sql id="TABLE">
        cf_modify_mobile_record
    </sql>

    <sql id="Base_Field">
        `id` as id,
        `case_id` as caseId,
        `origin_user_id` as originUserId,
        `target_user_id` as targetUserId,
        `operator_id` as operatorId
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="TABLE"/>
        (`case_id`,`origin_user_id`,`target_user_id`,`operator_id`)
        VALUES
        (#{caseId},#{originUserId},#{targetUserId},#{operatorId})
    </insert>


    <select id="selectByCaseId" resultType="com.shuidihuzhu.cf.client.adminpure.model.caseinfo.CfModifyMobileRecord">
        SELECT <include refid="Base_Field"/>
        FROM <include refid="TABLE"/>
        WHERE case_id = #{caseId} AND `is_delete` = 0 ORDER BY id
    </select>

</mapper>
