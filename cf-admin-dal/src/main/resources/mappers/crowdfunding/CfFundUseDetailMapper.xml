<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfFundUseDetailDao">

    <sql id="table_name">
        `crowdfunding_fund_use_detail`
    </sql>

    <sql id="insert_fields">
        `case_id`,
        `fund_use_progress_id`,
        `content_type`,
        `content`
    </sql>

    <sql id="select_fields">
        `id`,
        `case_id` as caseId,
        `fund_use_progress_id` as fundUseProgressId,
        `content_type` as contentType,
        `content` as content,
        `bill_money` as billMoney,
        `audit_result` as auditResult,
        `custom_audit_result` as customAuditResult,
        `self_pay_tag` as selfPayTag
    </sql>


    <insert id="batchInsert">
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES
        <foreach collection="fundUseDetailDOList" item="item" separator=",">
            (#{item.caseId}, #{item.fundUseProgressId}, #{item.contentType}, #{item.content})
        </foreach>
    </insert>

    <update id="auditDetail" parameterType="com.shuidihuzhu.cf.model.admin.workorder.CfFundUseDetailDO">
        update <include refid="table_name"/>
        <set>
            <if test="billMoney > 0">
                `bill_money`= #{billMoney},
            </if>
            <if test="auditResult > 0">
                `audit_result`= #{auditResult},
            </if>
            <if test="customAuditResult != null">
                `custom_audit_result` = #{customAuditResult},
            </if>
            <if test="selfPayTag > 0">
                `self_pay_tag` = #{selfPayTag}
            </if>
        </set>
        where id = #{id}
    </update>


    <select id="listByWorkOrderId" resultType="com.shuidihuzhu.cf.model.admin.workorder.CfFundUseDetailDO">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        <where>
            `work_order_id` = #{workOrderId}
        </where>
    </select>


    <select id="listByProgressIds" resultType="com.shuidihuzhu.cf.model.admin.workorder.CfFundUseDetailDO">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        <where>
            `fund_use_progress_id` in
            <foreach collection="progressIds" item="progressId" open="(" separator="," close=")">
                #{progressId}
            </foreach>
        </where>
    </select>


    <select id="listByCaseId" resultType="com.shuidihuzhu.cf.model.admin.workorder.CfFundUseDetailDO">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        <where>
            `case_id` = #{caseId}  and `audit_result` = 1 and `is_delete` = 0
        </where>
    </select>
</mapper>