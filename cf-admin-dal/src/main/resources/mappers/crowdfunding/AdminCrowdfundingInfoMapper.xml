<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingInfoDao">
	<sql id="tableName">
		crowdfunding_info
	</sql>

    <sql id="fields">
		`id`, `info_id`, `user_id`,
		`relation`, `applicant_name`, `applicant_qq`, `applicant_mail`, `relation_type`, `channel_type`,`channel`,
		`payee_name`, `payee_id_card`, `payee_mobile`, `payee_bank_name`, `payee_bank_branch_name`, `payee_bank_card`,
		`bank_card_verify_status`,
		`bank_card_verify_message`, `bank_card_verify_message2`, `title`, `title_img`, `content`,
		`target_amount`, `amount`, `donation_count`, `status`, `create_time`, `begin_time`, `end_time`, `from`, `use`,`data_status`,
		`type`, `content_image`, `content_image_status`
	</sql>

    <sql id="join_fields">
		ci.`id`, ci.`info_id`, ci.`user_id`,
		ci.`relation`, ci.`applicant_name`, ci.`applicant_qq`, ci.`applicant_mail`, ci.`relation_type`, ci.`channel_type`,ci.`channel`,
		ci.`payee_name`, ci.`payee_id_card`, ci.`payee_mobile`, ci.`payee_bank_name`, ci.`payee_bank_branch_name`, ci.`payee_bank_card`,
		ci.`bank_card_verify_status`,
		ci.`bank_card_verify_message`, ci.`bank_card_verify_message2`, ci.`title`, ci.`title_img`, ci.`content`,
		ci.`target_amount`, ci.`amount`, ci.`donation_count`, ci.`status`, ci.`create_time`, ci.`begin_time`,
		ci.`end_time`, ci.`from`, ci.`use`,ci.`data_status`,ci.`type`
	</sql>

	<select id="selectByExample" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo"
            parameterType="com.shuidihuzhu.common.web.util.admin.BasicExample">
		select <include refid="join_fields"/>
		FROM crowdfunding_info ci
        <include refid="com.shuidihuzhu.cf.dao.message.Example_Where_Clause"/>
	</select>


	<select id="getApproveList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT
		<include refid="fields"/>
		FROM
		<include refid="tableName"/>
		WHERE
		status = #{status}
		<if test="applicantName != null and applicantName != ''">
			<bind name="applicantNamePattern" value="'%' + applicantName + '%'"/>
			AND applicant_name LIKE #{applicantNamePattern}
		</if>
		<if test="title != null and title != ''">
			<bind name="titlePattern" value="'%' + title + '%'"/>
			AND title LIKE #{titlePattern}
		</if>
		ORDER BY create_time DESC
		LIMIT #{size} OFFSET #{offset}
	</select>



	<update id="addAmount">
		UPDATE
		<include refid="tableName"/>
		SET
		amount = amount + #{amount},
		donation_count = donation_count + 1
		WHERE id = #{id}
	</update>

	<update id="subtractAmount">
		UPDATE
		<include refid="tableName"/>
		SET
		amount = amount - #{amount}
		WHERE id = #{id}
	</update>

	<update id="updateFrom" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		UPDATE
		<include refid="tableName"/>
		SET
		`from` = #{from}
		WHERE info_id = #{infoId}
	</update>

	<update id="updateTitleAndContent" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		UPDATE
		<include refid="tableName"/>
		SET
		`title` = #{title},
		`content`=#{content},
		`encrypt_content`=#{encryptContent}
		WHERE info_id = #{infoId}
	</update>

	<update id="updateRelationType" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		UPDATE
		<include refid="tableName"/>
		SET
		`relation_type` = #{relationType, typeHandler=org.apache.ibatis.type.EnumOrdinalTypeHandler, javaType=com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType}
		WHERE info_id = #{infoId}
	</update>

	<update id="updatePayeeInfo" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		UPDATE
		<include refid="tableName"/>
		SET
		`payee_name` = #{payeeName},
		`payee_id_card`= #{payeeIdCard},
		`payee_bank_name` = #{payeeBankName},
		`payee_bank_branch_name` = #{payeeBankBranchName},
		`payee_bank_card` = #{payeeBankCard},
		`relation`=#{relation},
		`relation_type`=#{relationType, typeHandler=org.apache.ibatis.type.EnumOrdinalTypeHandler, javaType=com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType},
		`bank_card_verify_status`=#{bankCardVerifyStatus}
		WHERE info_id = #{infoId}
	</update>

	<update id="adjustAmountFromOrders">
		UPDATE
		<include refid="tableName"/>
		SET
		`amount`=#{amount}
		WHERE id = #{id}
	</update>

	<update id="doApprove">
		UPDATE
		<include refid="tableName"/>
		SET status = #{status}, begin_time = #{beginTime}
		WHERE id = #{crowdfundingId}
	</update>

	<update id="updateEndTime">
		UPDATE
		<include refid="tableName"/>
		SET `end_time`=#{endTime}
		WHERE id = #{id}
	</update>

	<update id="updateDataStatus">
		UPDATE
		<include refid="tableName"/>
		SET `data_status`=#{dataStatus}
		WHERE id = #{id}
	</update>

	<update id="updateType">
		UPDATE
		<include refid="tableName"/>
		SET `type`=#{type}
		WHERE id = #{id}
	</update>

	<update id="updateStatus">
		UPDATE
		<include refid="tableName"/>
		SET `status`=#{newStatus}
		WHERE id=#{id} AND `status`=#{oldStatus}
	</update>

	<update id="updateVerifyStatus">
		UPDATE
		<include refid="tableName"/>
		SET `bank_card_verify_status`=#{verifyStatus},
		`bank_card_verify_message`=#{bankCardVerifyMessage},
		`bank_card_verify_message2`=#{bankCardVerifyMessage2}
		WHERE
		`id`=#{id}
	</update>


	<select id="getApplyCount" resultType="java.lang.Integer">
		SELECT count(1)
		FROM
		<include refid="tableName"/>
		WHERE user_id = #{userId} AND (create_time BETWEEN #{startTime} AND #{endTime})
	</select>

	<select id="getTrueMobile" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		select * from <include refid="tableName"/> where CHARACTER_LENGTH(payee_mobile)=11 and type = 99;
	</select>

	<update id="updateMoblie" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		UPDATE <include refid="tableName"/>
		SET `payee_mobile`=#{payeeMobile}
		WHERE `id`=#{id}
	</update>

	<update id="updateTargetAmount">
		UPDATE <include refid="tableName"/>
		SET `target_amount`=#{targetAmount}
        WHERE `id`=#{id}
	</update>

    <update id="editType">
        UPDATE <include refid="tableName"/>
        SET `type`=#{type},`data_status`=#{dataStatus}
        WHERE `id`=#{id}
    </update>

    <update id="updateContent">
        UPDATE <include refid="tableName"/>
        SET `title`=#{title},`content`=#{content},`encrypt_content`=#{encryptContent}
        WHERE `id`=#{id}
    </update>

	<update id="updateCaseUserId">
		update <include refid="tableName"/>
		set user_id = #{userId}
		where id = #{caseId}
	</update>

	<select id="getInfoByUniqueCode" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminCrowdfundingInfo">
		select cr.id,cr.info_id,cr.user_id,cr.title,cr.target_amount,cr.amount,cr.donation_count,cf.volunteer_unique_code,cr.create_time
        FROM
		crowdfunding_info cr,cf_info_ext cf
        WHERE cr.info_id = cf.info_uuid
        AND
        cf.volunteer_unique_code=#{volunteerUniqueCode}
		<if test="startTime!=null and startTime!=''">
			AND <![CDATA[ cr.`create_time` >= #{startTime} ]]>
		</if>
		<if test="endTime!=null and endTime!=''">
			AND <![CDATA[ cr.`create_time` <= #{endTime} ]]>
		</if>
		<if test="userId > 0">
			AND cr.user_id = #{userId}
		</if>
		ORDER BY create_time DESC
	</select>

	<select id="getInfoByInfoUuidWithUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminCrowdfundingInfo">
		select id,info_id,user_id,title,target_amount,amount,donation_count,create_time
		FROM
		crowdfunding_info
		WHERE info_id in
		<foreach collection="infoIds" item="infoId" separator="," open="(" close=")">
			#{infoId}
		</foreach>
		<if test="userId > 0">
			AND user_id = #{userId}
		</if>
	</select>
	<select id="getInfoByMobile" parameterType="String" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminCrowdfundingInfo">
		SELECT cr.user_id
		FROM crowdfunding_info cr,cf_info_ext cf
		WHERE cr.info_id = cf.info_uuid
		AND cf.crypto_register_mobile = #{mobile}
		limit 1
	</select>

	<select id="getListAfterId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT <include refid="fields"/>
		FROM <include refid="tableName"/>
		WHERE `id` <![CDATA[ > ]]> #{infoId}
	</select>

	<select id="getCaseCountByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoChild">
		select user_id , count(id) as count
		from <include refid="tableName"/>
		where status in (0,1,2)
        and user_id in
		<foreach collection="userIds" open="(" close=")" separator="," item="userId">
			#{userId}
		</foreach>
		group by user_id
	</select>

	<select id="getCrowdfundingReportInfo" resultType="com.shuidihuzhu.cf.vo.crowdfunding.AdminWorkOrderReportVo">
		select info.id as caseId,info.amount,info.status,IFNULL(cs.pause_state,2) as pauseState,report_id, report.work_order_id,report.id id
		from <include refid="tableName"/> info
		left join cf_state cs
		on info.id = cs.info_id
		left join crowdfunding_author author
		on info.id = author.crowdfunding_id
		left join admin_work_order_report report
		on info.id = report.case_id
		<where>
		<if test="title != null and title != ''">
			and info.title like  concat('%','${title}','%')
		</if>
		<if test="caseId != null">
			and info.id = #{caseId}
		</if>
		<if test="userId != null and userId > 0">
			and info.user_id = #{userId}
		</if>
		<if test="name != null and name !=''">
			and author.name = #{name}
		</if>
		</where>
		group by info.id
	</select>

    <select id="selectByUserIdWhereNotEnd" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where `user_id` in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and <![CDATA[ `end_time`>now() ]]>
    </select>

    <select id="selectByCaseIdNotEnd" resultType="java.lang.Integer">
        select `id`
        from <include refid="tableName"/>
        where `id` in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and <![CDATA[ `end_time`>now() ]]>
    </select>

    <select id="selectByCaseIdList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where `id` in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

	<select id="getInfoListByMobile" parameterType="String" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT cr.`id`, cr.`info_id` cr.user_id
		FROM crowdfunding_info cr,cf_info_ext cf
		WHERE cr.info_id = cf.info_uuid
		AND cf.crypto_register_mobile = #{mobile}
	</select>

	<select id="selectByIdLimit" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		select <include refid="fields"/>
		from <include refid="tableName"/>
		where  id > #{id}
		AND type = #{type}
		order by id
		limit #{limit}
	</select>


	<select id="getInfoIdsByIdsOrderByFieldIds" resultType="java.lang.String">
		select info_id
		from <include refid="tableName"/>
		where id in
		<foreach collection="ids" item="id" open="(" separator="," close=")">
			#{id}
		</foreach>
		order by field(id,
		<foreach collection="ids" item="id" separator=",">
			#{id}
		</foreach>
		)
	</select>
	<select id="getInfoByUniqueCodeAndCaseId"
			resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminCrowdfundingInfo">
		select cr.id,cr.info_id,cr.user_id,cr.title,cr.target_amount,cr.amount,cr.donation_count,cf.volunteer_unique_code,cr.create_time
		FROM
		crowdfunding_info cr,cf_info_ext cf
		WHERE cr.info_id = cf.info_uuid
		AND cr.id =#{caseId}
	</select>

	<update id="updateTitleImg">
		UPDATE
		<include refid="tableName"/>
		SET `title_img`=#{titleImg}
		WHERE id=#{id}
	</update>

	<update id="updateContentImageStatus">
		UPDATE
		<include refid="tableName"/>
		SET `content_image_status`=#{targetStatus}
		WHERE id=#{id} and `content_image_status`=#{sourceStatus}
	</update>

	<update id="updateContentImage">
		UPDATE
		<include refid="tableName"/>
		SET `content_image_status`=#{targetStatus}, `content_image`=#{contentImage}
		WHERE id=#{id} and `content_image_status`=#{sourceStatus}
	</update>

	<select id="selectByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT *
		FROM <include refid="tableName"/>
		WHERE user_id = #{userId}
	</select>

	<select id="getByInfoId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		select id ,info_id as infoId,status as status,end_time as endTime
		FROM <include refid="tableName"/>
		WHERE info_id = #{infoId}
	</select>

</mapper>
