<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingAuthorDao">
    <sql id="tableName">
        `crowdfunding_author`
    </sql>

    <sql id="selectFields">
        `id`,`crowdfunding_id`,`name`,`crypto_id_card`,`crypto_phone`,`id_type`,`mail`,`qq`,`health_insurance`,`commercial_insurance`,`create_time`,`last_modified`, `face_id_result`
    </sql>

    <select id="get" parameterType="int" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor">
        SELECT
        <include refid="selectFields"/>
        FROM
        <include refid="tableName"/>
        WHERE
        crowdfunding_id=#{crowdfundingId}
    </select>

    <select id="getByInfoIdList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor">
        SELECT
        <include refid="selectFields"/>
        FROM
        <include refid="tableName"/>
        WHERE
        crowdfunding_id in
        <foreach collection="infoIdList" item="crowdfundingId" open="(" separator="," close=")">
            #{crowdfundingId}
        </foreach>
    </select>

    <select id="getByName" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor">
        SELECT <include refid="selectFields"/>
        FROM
        <include refid="tableName"/>
        WHERE `name`=#{name}
    </select>

    <insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="tableName"/>
        (`name`,
        `crypto_id_card`,
        `crypto_phone`,
        `status`,
        `id_type`,
        `mail`,
        `qq`,
        `crowdfunding_id`,
        `health_insurance`,
        `commercial_insurance`
        ) VALUES (
        #{name},
        #{cryptoIdCard},
        #{cryptoPhone},
        #{status},
        #{idType, typeHandler=org.apache.ibatis.type.EnumOrdinalTypeHandler, javaType=com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType},
        #{mail},
        #{qq},
        #{crowdfundingId},
        #{healthInsurance},
        #{commercialInsurance}
        )
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor">
        UPDATE <include refid="tableName"/>
        SET `name`=#{name},
        `crypto_id_card`=#{cryptoIdCard},
        `crypto_phone`=#{cryptoPhone},
        `status`=#{status},
        `id_type`=#{idType, typeHandler=org.apache.ibatis.type.EnumOrdinalTypeHandler, javaType=com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType},
        `mail`=#{mail},
        `qq`=#{qq},
        `health_insurance`=#{healthInsurance},
        `commercial_insurance`=#{commercialInsurance}
        WHERE
        `crowdfunding_id`=#{crowdfundingId}
    </update>

    <select id="selectNameByCfId" parameterType="int" resultType="java.lang.String">
        SELECT `name`
        FROM <include refid="tableName"/>
        WHERE `crowdfunding_id`=#{crowdfundingId}
    </select>

    <select id="getApplyCount" resultType="int">
        SELECT count(*)
        FROM <include refid="tableName"/>
        WHERE `name` = #{name} AND (`create_time` BETWEEN #{startTime} AND #{endTime})
    </select>
    
    <select id="selectByCaseIdList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor">
        select <include refid="selectFields"/>
        from <include refid="tableName"/>
        where `crowdfunding_id` in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByNameList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor">
        select <include refid="selectFields"/>
        from <include refid="tableName"/>
        where `name` in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByIdCardList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor">
        select <include refid="selectFields"/>
        from <include refid="tableName"/>
        where `crypto_id_card` in
        <foreach collection="idCardList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
