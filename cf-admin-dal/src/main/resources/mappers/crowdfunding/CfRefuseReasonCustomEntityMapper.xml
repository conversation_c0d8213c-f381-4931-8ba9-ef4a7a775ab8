<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfRefuseReasonCustomEntityDao">

    <sql id="table_name">
        `cf_refuse_reason_custom_entity`
    </sql>

    <sql id="insert_fields">
        `case_id`,
        `work_order_id`,
        `refuse_reason_entity_id`,
        `refuse_custom_reason`,
        `refuse_status`
    </sql>

    <sql id="select_fields">
        `case_id`,
        `work_order_id`,
        `refuse_reason_entity_id`,
        `refuse_custom_reason`,
        `refuse_status`
    </sql>

    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonCustomEntity">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES (#{caseId}, #{workOrderId}, #{refuseReasonEntityId}, #{refuseCustomReason}, #{refuseStatus})
    </insert>

    <select id="selectByCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonCustomEntity">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `is_delete`=0 AND `case_id` = #{caseId}
    </select>

    <select id="selectByWorkOrderId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonCustomEntity">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `is_delete`=0 AND `work_order_id` = #{workOrderId}
    </select>

    <update id="updateStatusByCaseIdAndRefuseId" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonCustomEntity">
        UPDATE <include refid="table_name"/>
        SET `refuse_status`= #{refuseStatus}
        WHERE `case_id`= #{caseId} AND `refuse_reason_entity_id`= #{refuseReasonEntityId}
    </update>

</mapper>
