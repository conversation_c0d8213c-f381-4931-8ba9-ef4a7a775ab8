<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfCasePublicInfoDao">

    <sql id="TABLE">
        cf_case_public_info
    </sql>

    <sql id="Base_Field">
        `id` as id,
        `case_id` as caseId,
        `info_uuid` as infoUuid,
        `type` as type,
        `switch_info` as switchInfo,
        `image_url` as imageUrl,
        `operate_id` as operateId,
        `img_handle_status` as imgHandleStatus
    </sql>

    <insert id="addCasePublicInfo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="TABLE"/>
        (`case_id`,`info_uuid`,`type`,`switch_info`,`image_url`,`operate_id`,`img_handle_status`)
        VALUES
        (#{caseId},#{infoUuid},#{type},#{switchInfo},#{imageUrl},#{operateId},#{imgHandleStatus})
    </insert>

    <select id="getListByIdList" resultType="com.shuidihuzhu.cf.client.adminpure.model.caseinfo.CfCasePublicInfo">
        SELECT <include refid="Base_Field"/>
        FROM <include refid="TABLE"/>
        WHERE id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND `is_delete` = 0
        ORDER BY id
    </select>

    <select id="getListByInfoUuidAndType" resultType="com.shuidihuzhu.cf.client.adminpure.model.caseinfo.CfCasePublicInfo">
        SELECT <include refid="Base_Field"/>
        FROM <include refid="TABLE"/>
        WHERE info_uuid = #{infoUuid} AND `is_delete` = 0 ORDER BY id
    </select>

    <update id="update" parameterType="com.shuidihuzhu.cf.client.adminpure.model.caseinfo.CfCasePublicInfo">
        update <include refid="TABLE"/>
        <set>
            `type`=#{type}, `switch_info`=#{switchInfo}, `image_url`=#{imageUrl}
        </set>
        where `id`=#{id}
    </update>


    <update id="deleteByCaseIdAndType">
        update <include refid="TABLE"/>
        <set>
            `is_delete`= 1
        </set>
        where `case_id`=#{caseId} AND `type`=#{type}
    </update>

    <select id="getByCaseId" resultType="com.shuidihuzhu.client.cf.admin.model.AdminCaseDetailsMsg">
        SELECT
        <include refid="Base_Field"/>
        FROM
        <include refid="TABLE"/>
        WHERE
        case_id =#{caseId}
        and is_delete = 0
    </select>

    <update id="updateShowTag">
        update <include refid="TABLE"/>
        set show_tag = 1
        where case_id = #{caseId}
    </update>

    <insert id="insert" >
        INSERT INTO
        <include refid="TABLE"/>
        (`case_id`,`info_uuid`,`show_tag`)
        VALUES
        (#{caseId},#{infoUuid},#{showTag})
    </insert>

</mapper>
