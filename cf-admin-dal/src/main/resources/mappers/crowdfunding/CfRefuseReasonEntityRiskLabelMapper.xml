<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfRefuseReasonEntityRiskLabelDao">

    <sql id="table_name">
        `cf_refuse_reason_entity_risk_label`
    </sql>

    <sql id="insert_fields">
        `refuse_entity_id`,
        `risk_label_related`,
        `risk_label_id`
    </sql>

    <sql id="select_fields">
        `id`,
        `refuse_entity_id`,
        `risk_label_related`,
        `risk_label_id`
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntityRiskLabel" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES (#{refuseEntityId}, #{riskLabelRelated}, #{riskLabelId})
    </insert>

    <select id="selectByRefuseEntityIdList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntityRiskLabel">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `refuse_entity_id` IN
        <foreach collection="refuseEntityIdList" item="refuseEntityId" open="(" separator="," close=")">
            #{refuseEntityId}
        </foreach>
        and is_delete = 0
    </select>

    <select id="selectByRefuseEntityId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntityRiskLabel">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `refuse_entity_id` = #{refuseEntityId} and is_delete = 0
    </select>

    <select id="selectByRiskLabelId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntityRiskLabel">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `risk_label_id` = #{riskLabelId} and is_delete = 0
    </select>

    <update id="deleteByReasonEntityId" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntityRiskLabel">
        UPDATE <include refid="table_name"/>
        SET `is_delete`= 1
        WHERE `refuse_entity_id` = #{refuseEntityId}
    </update>

</mapper>
