<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfAiMaterialsDao">

	<insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterials" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO cf_ai_materials (work_order_id,work_order_type,case_id,materials_type,materials)
		VALUES
		<foreach collection="list" item="item" separator=",">
			(#{item.workOrderId},#{item.workOrderType},#{item.caseId},#{item.materialsType},#{item.materials})
		</foreach>
	</insert>


	<select id="getAiMaterials" resultType="com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterials">

		select * from cf_ai_materials
		where work_order_id = #{workOrderId} and materials_type= #{materialsType}  and is_delete=0

	</select>


	<select id="getByWorkOrderId" resultType="com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterials">

		select * from cf_ai_materials
		where work_order_id = #{workOrderId} and is_delete=0

	</select>


	<select id="getByCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterials">

		select * from cf_ai_materials
		where case_id = #{caseId} and materials_type=#{materialsType}  and is_delete=0
		order by id desc limit 1

	</select>


	<select id="getByCaseIdAndDate" resultType="com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterials">

		select * from cf_ai_materials
		where case_id = #{caseId} and materials_type=#{materialsType} and create_time <![CDATA[ < ]]>  #{time}   and is_delete=0
		order by id desc limit 1

	</select>


	<update id="saveResult" parameterType="com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterialsResult" useGeneratedKeys="true" keyProperty="id">
		insert into cf_ai_materials_result (work_order_id,case_id,reject_result,stop_result,label_name,reject_field)
		values
		(#{workOrderIds},#{caseId},#{rejectResult},#{stopResult},#{label},#{rejectField})
	</update>

	<select id="getResultByCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterialsResult">

		select work_order_id as 'work_order_ids',case_id,reject_result,stop_result,label_name as label,reject_field from cf_ai_materials_result
		where case_id = #{caseId} and is_delete=0
		order by id desc limit 1

	</select>

</mapper>
