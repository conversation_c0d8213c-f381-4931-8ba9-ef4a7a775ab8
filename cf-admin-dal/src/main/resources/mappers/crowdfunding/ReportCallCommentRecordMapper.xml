<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.ReportCallCommentRecordDao">
    <sql id="tableName">
        report_call_comment_record
    </sql>

    <sql id="insertFields">
        case_id,
        report_id,
        comment,
        call_status,
        type
    </sql>

    <sql id="selectFields">
        id,
        case_id,
        report_id,
        comment,
        call_status,
        type,
        create_time
    </sql>

    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.report.ReportCallCommentRecord">
        insert into
        <include refid="tableName"/>
        (<include refid="insertFields"/>)
        values (#{caseId},#{reportId},#{comment},#{callStatus},#{type})
    </insert>

    <select id="getReportCallCommentRecord" resultType="com.shuidihuzhu.cf.model.report.ReportCallCommentRecord">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        where case_id = #{caseId}
        and report_id = #{reportId}
        and type = #{type}
        and is_delete = 0
    </select>
</mapper>