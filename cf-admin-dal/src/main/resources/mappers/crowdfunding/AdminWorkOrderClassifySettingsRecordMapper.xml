<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminWorkOrderClassifySettingsRecordDao">

    <sql id="table_name">
        admin_work_order_classify_settings_record
    </sql>

    <sql id="select_fileds">
        `id` as id,
        `classify_id` as classifyId,
        `operate_type` as operateType,
        `comment` as comment,
        `operator_name` as operatorName,
        `create_time` as createTime
    </sql>


    <sql id="insert_fields">
        `classify_id`, `operate_type`, `comment`, `operator_name`
    </sql>


    <insert id = "insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderClassifySettingsRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES(#{classifyId}, #{operateType}, #{comment}, #{operatorName})
    </insert>


    <select id = "listRecords" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderClassifySettingsRecord" >
        SELECT * FROM
        <include refid="table_name"/>
        WHERE classify_id = #{problemClassifyId}
    </select>


</mapper>