<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCfRepeatInfoDAO">


    <sql id="tableName">
        crowdfunding_repeat_info
    </sql>

    <sql id = "Base_Field">
        case_id, repeat_base_info, repeat_diagnosis_info, need_update_base_info, repeat_summary
    </sql>

    <update id="updateRepeatInfoByCaseId">
        UPDATE <include refid="tableName"/>
        SET repeat_base_info = #{repeatInfo},
        repeat_summary = #{repeatSummary}
        WHERE
        case_id = #{caseId}
    </update>

    <update id="updateRepeatDiagnosisByCaseId">
        UPDATE <include refid="tableName"/>
        SET repeat_diagnosis_info = #{repeatDiagnosisInfo}
        WHERE
        case_id = #{caseId}
    </update>

    <update id="updateNeedUpdateByCaseIds">
        UPDATE <include refid="tableName"/>
        SET need_update_base_info = #{needUpdate}
        WHERE case_id IN
        <foreach collection="caseIds" item="caseId" open="(" separator="," close=")" >
          #{caseId}
        </foreach>
    </update>


    <select id="selectByCaseIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminCfRepeatInfo">
        SELECT *
        FROM <include refid="tableName"/>
        <where>
            <if test="caseIds != null and caseIds.size() > 0">
                case_id IN
                <foreach collection="caseIds" item="caseId" open="(" separator="," close=")">
                    #{caseId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectByCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminCfRepeatInfo">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE case_id = #{caseId}
    </select>

    <insert id="insertOrUpdate" parameterType="com.shuidihuzhu.cf.model.crowdfunding.AdminCfRepeatInfo" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO <include refid="tableName"/>
        (<include refid="Base_Field"/>)
        VALUES (#{caseId}, #{repeatBaseInfo}, #{repeatDiagnosisInfo}, #{needUpdateBaseInfo}, #{repeatSummary})
        ON DUPLICATE KEY UPDATE
        repeat_base_info = #{repeatBaseInfo},
        repeat_diagnosis_info = #{repeatDiagnosisInfo},
        need_update_base_info = #{needUpdateBaseInfo},
        repeat_summary = #{repeatSummary}
    </insert>

</mapper>