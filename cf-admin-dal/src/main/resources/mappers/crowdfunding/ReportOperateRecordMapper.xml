<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.ReportOperateRecordDAO">

    <sql id="TableName">
        cf_report_operate_record
    </sql>

    <sql id="BaseFields">
        `id`,`case_id`,`work_order_id`,`type`,`content`,`comment`,`operator_id`,`operator`,`department`,`create_time`,`update_time`
    </sql>

    <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.model.crowdfunding.ReportOperateRecordDO">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="case_id" property="caseId" jdbcType="INTEGER"/>
        <result column="work_order_id" property="workOrderId" jdbcType="INTEGER"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="comment" property="comment" jdbcType="VARCHAR"/>
        <result column="operator_id" property="operatorId" jdbcType="INTEGER"/>
        <result column="operator" property="operator" jdbcType="VARCHAR"/>
        <result column="department" property="department" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="queryByWorkOrderId" resultMap="BaseResultMap">
        select <include refid="BaseFields"/>
        from <include refid="TableName"/>
        where work_order_id = #{workOrderId}
    </select>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.ReportOperateRecordDO">
        insert into <include refid="TableName"/>
        (`case_id`,`work_order_id`,`type`,`content`,`comment`,`operator_id`,`operator`,`department`)
        values
        (#{caseId},#{workOrderId},#{type},#{content},#{comment},#{operatorId},#{operator},#{department})
    </insert>
</mapper>