<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfAiMaterialsSmartAIResultDao">

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.ai.CfAiMaterialsSmartAIResult">
        INSERT INTO cf_ai_materials_smart_result (case_id,work_order_id,ai_content_work_order_result,title_disease_name,content_disease_name)
        VALUES
        (#{caseId},#{workOrderId},#{aiContentWorkOrderResult},#{titleDiseaseName},#{contentDiseaseName})
    </insert>


    <select id="getByWorkOrderId" resultType="com.shuidihuzhu.cf.model.ai.CfAiMaterialsSmartAIResult">

        select * from cf_ai_materials_smart_result
        where work_order_id = #{workOrderId} and is_delete=0 limit 1

    </select>

</mapper>
