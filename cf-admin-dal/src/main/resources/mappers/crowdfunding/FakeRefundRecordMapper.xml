<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.FakeRefundRecordDao">

    <sql id="table_name">
        fake_refund_record
    </sql>

    <sql id="fields">
        `operation_user`,
        `pay_uid`,
        `crowdfunding_order_id`,
        `crowdfunding_id`,`amount`,`refund_type`
    </sql>

    <insert id="save" parameterType="com.shuidihuzhu.cf.model.crowdfunding.FakeRefundRecord">
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="fields"/>)
        VALUES
        (#{operationUser}, #{payUid}, #{crowdfundingOrderId}, #{crowdfundingId},#{amount},#{refundType})
    </insert>

    <select id="findByPayUid" resultType="com.shuidihuzhu.cf.model.crowdfunding.FakeRefundRecord">
        select <include refid="fields"/>
        from <include refid="table_name"/>
        where pay_uid = #{payUid}
    </select>

</mapper>