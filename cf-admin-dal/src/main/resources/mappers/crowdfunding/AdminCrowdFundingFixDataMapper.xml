<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">



<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdFundingFixDataDao">

    <select id="countBlessByInfoId" resultType="java.lang.Integer">
        SELECT COUNT(id)
        FROM cf_info_blessing
        WHERE info_uuid = #{infoUuid} AND blessing = 1
    </select>

    <select id="selectBlessByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoBlessing">
        SELECT *
        FROM cf_info_blessing
        WHERE user_id = #{userId} AND blessing = 1 AND valid = 1
    </select>


    <select id="selectCfByCreate" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
        SELECT *
        FROM crowdfunding_info
        WHERE
         create_time >= #{begin}
        AND
        <![CDATA[ create_time <= #{end} ]]>
    </select>


    <select id = "selectFirstApproveByCreateTime" resultType="java.lang.Integer">
        select info_id
        from cf_first_approve_material
        where create_time >= #{begin}
        AND
        <![CDATA[ create_time <= #{end} ]]>
    </select>

    <select id = "selectCrowdfundingCityById"  resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity">
        select *
        from crowdfunding_city
        where id > #{id}
        order by id
        limit #{size}
    </select>


    <insert id="insertCrowdfundingCity" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity">
        insert into crowdfunding_city (`id`, `code`, `name`, `parent_id`, `first_letter`, `level`, `valid`)
        values (#{id}, #{code}, #{name}, #{parentId}, #{firstLetter}, #{level},#{valid})
    </insert>

    <select id="getByInfoUuid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfCapitalAccount">
        SELECT *
        FROM cf_capital_account
        WHERE `info_uuid`=#{infoUuid}
    </select>

    <select id="getCfInfoStatById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoStat">
        SELECT
        *
        FROM
        cf_info_stat
        WHERE
        `id` = #{id}
        LIMIT 1
    </select>

    <insert id="addCfInfoStat" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoStat" useGeneratedKeys="true" keyProperty="id">
        replace INTO
        cf_info_stat
        (`id`,`share_count`,`donation_count`,`verify_user_count`,`comment_count`,`amount`,`verify_friend_count`,`verify_hospital_count`)
        VALUES
        (#{id},#{shareCount},#{donationCount},#{verifyUserCount},#{commentCount},#{amount},#{verifyFriendCount},#{verifyHospitalCount})
    </insert>

    <select id="selectByUserId" resultType="java.lang.Long">
        SELECT user_id
        FROM user_real_info
        force index (idx_user_id)
        WHERE user_id = #{userId}
        AND
        <![CDATA[ id <= #{id} ]]>
        AND
        idcard_verify_status = 2
        LIMIT 1
    </select>

</mapper>