<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdFundingProgressDao">
    <sql id="tableName">
        crowdfunding_progress
    </sql>


    <resultMap id="selectResult" type="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="activityId" column="crowdfunding_id"/>
        <result property="type" column="type"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="imageUrls" column="image_urls"/>
        <result property="postDate" column="create_time"/>
        <result property="updateDate" column="update_time"/>
    </resultMap>


    <resultMap id="selectAdminCrowdfundingProgressResult" type="com.shuidihuzhu.cf.vo.crowdfunding.AdminCrowdfundingProgress">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="crowdfundingId" column="crowdfunding_id"/>
        <result property="title" column="title"/>
        <result property="fundUseRejectedReason" column="fund_use_rejected_reason"/>
        <result property="content" column="content"/>
        <result property="imageUrls" column="image_urls"/>
        <result property="createTime" column="create_time"/>
    </resultMap>



    <select id="getActivityProgress" resultMap="selectResult" parameterType="com.shuidihuzhu.common.web.util.admin.BasicExample">
        SELECT *
        FROM <include refid="tableName"/>
        <include refid="com.shuidihuzhu.cf.dao.message.Example_Where_Clause"/>
    </select>

    <select id="getActivityProgressById" resultMap="selectResult">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE id=#{id}
    </select>

    <select id="selectCountByFiveMin" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM <include refid="tableName"/>
        WHERE <![CDATA[ `create_time`>#{begin} ]]> AND <![CDATA[ `create_time`<#{end} ]]>
    </select>

    <update id="updateImageUrls">
        UPDATE <include refid="tableName"/>
        SET `image_urls`=#{imageUrls}
        WHERE `id`=#{id}
        AND `crowdfunding_id`=#{crowdfundingId}
        AND `is_delete` = 0
    </update>

    <update id="updateImageUrlsByNoIsDelete">
        UPDATE <include refid="tableName"/>
        SET `image_urls`=#{imageUrls}
        WHERE `id`=#{id}
        AND `crowdfunding_id`=#{crowdfundingId}
    </update>

    <select id="getByCreateTime" resultMap="selectResult">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE <![CDATA[ `create_time`>=#{beginTime} AND `create_time`<#{endTime} ]]>
    </select>

	<select id="getByIds" resultMap="selectResult">
		SELECT *
		FROM <include refid="tableName"/>
		WHERE `id` in <foreach collection="ids" item="id" open="(" separator="," close=")">#{id}</foreach>
	</select>

    <update id="updateContent">
        UPDATE <include refid="tableName"/>
        SET `content`=#{content}
        WHERE `id`=#{id}
    </update>

    <insert id="insertInCrowdfundingProgress" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO <include refid="tableName"/>
        (`user_id`, `crowdfunding_id`, `type`, `title`, `content`, `image_urls`)
        VALUES
        (
        #{userId}, #{activityId}, #{type},
        #{title}, #{content}, #{imageUrls}
        )
    </insert>

    <select id="getProgressById" resultMap="selectAdminCrowdfundingProgressResult">
        SELECT *
        FROM `shuidi_crowdfunding`.`crowdfunding_fund_use_progress`
        WHERE `id` = #{progressId}
    </select>

    <select id="getLastOneByCaseId" resultMap="selectAdminCrowdfundingProgressResult">
        SELECT *
        FROM `shuidi_crowdfunding`.`crowdfunding_fund_use_progress`
        WHERE `crowdfunding_id` = #{caseId}
        order by id desc
        limit 1
    </select>

    <update id="delProgressById">
        UPDATE <include refid="tableName"/>
        SET `is_delete` = 1
        WHERE `id`=#{id}
    </update>

    <update id="reviveProgressById">
        UPDATE <include refid="tableName"/>
        SET `is_delete` = 0
        WHERE `id`=#{id}
    </update>

    <select id="getFirstOneByCaseId" resultMap="selectAdminCrowdfundingProgressResult">
        SELECT *
        FROM `shuidi_crowdfunding`.`crowdfunding_fund_use_progress`
        WHERE `crowdfunding_id` = #{caseId}
        AND is_delete = 0
        order by id
        limit 1
    </select>
</mapper>
