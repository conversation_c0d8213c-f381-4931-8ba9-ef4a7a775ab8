<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfAndroidChannelDao">
    <sql id="table_name">
      `umeng_app_data`
    </sql>

    <insert id="insertList">
        INSERT IGNORE INTO <include refid="table_name"/>
        (`select_date`,`channel_name`,`new_users`,`active_users`,`start_ups`,`times`,`retention_rate`)
        VALUES
        <foreach collection="list" item="item" separator=",">
        (#{item.selectDate},#{item.channelName},#{item.newUsers},#{item.activeUsers},#{item.startUps},#{item.times},#{item.retentionRate})
        </foreach>
    </insert>

    <select id="selectOne" resultType="com.shuidihuzhu.cf.model.admin.CfAndroidChannel">
        SELECT * FROM <include refid="table_name"/>
        WHERE `select_date`=#{selectDate} AND `channel_name`=#{channelName}
        LIMIT 1
    </select>

    <delete id="deleteData">
        DELETE FROM <include refid="table_name"/>
        WHERE `id`>0
    </delete>
</mapper>