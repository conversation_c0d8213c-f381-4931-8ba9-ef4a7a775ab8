<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminReportStatDao">


    <select id="getHandlerUser" resultType="java.lang.Integer">
      select distinct o.operator_id
      from admin_work_order_report r left join admin_work_order o
      on r.work_order_id=o.id
      where r.handle_time between #{start} and #{end}
      and o.operator_id>0
    </select>

    <select id="getlingquNum" resultType="java.lang.Long">
      select o.id
      from admin_work_order_report r left join admin_work_order o
      on r.work_order_id=o.id
      where r.handle_time between #{start} and #{end}
      and o.operator_id>0
        <if test="userId > 0 ">
            and o.operator_id = #{userId}
        </if>
    </select>

    <select id="getyiliuNum" resultType="java.lang.Long">
      select o.id
      from admin_work_order_report r left join admin_work_order o
      on r.work_order_id=o.id
      where <![CDATA[  r.handle_time < #{start} ]]>
      and o.operator_id>0 and r.deal_result=1
        <if test="userId > 0 ">
            and o.operator_id = #{userId}
        </if>
    </select>

   <select id="getweilingquNum" resultType="java.lang.Long">

      select id from admin_work_order where order_type=5 and order_task=12 and operator_id=0

    </select>

    <select id="getyiliuFinishNum" resultType="java.lang.Long">
        select o.id
        from admin_work_order_report r left join admin_work_order o
        on r.work_order_id=o.id
        where <![CDATA[  r.handle_time < #{start} ]]>
        and o.operator_id>0 and r.deal_result=#{dealResult}
        and r.update_time between #{start} and #{end}
        <if test="userId > 0 ">
            and o.operator_id = #{userId}
        </if>
    </select>


    <select id="getyiliuDoingFinishNum" resultType="java.lang.Long">
        select o.id
        from admin_work_order_report r left join admin_work_order o
        on r.work_order_id=o.id
        where <![CDATA[  r.handle_time < #{start} ]]>
        and o.operator_id>0 and r.deal_result in
        <foreach collection="dealResults" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="userId > 0 ">
            and o.operator_id = #{userId}
        </if>
    </select>


    <select id="getEntryNum" resultType="java.lang.Integer">
      select count(1) from work_report_map where work_order_id in

        <foreach collection="workOrderIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>

    <select id="getjinriFinishNum" resultType="java.lang.Long">
      select o.id
      from admin_work_order_report r left join admin_work_order o
      on r.work_order_id=o.id
      where r.handle_time between #{start} and #{end}
      and o.operator_id>0 and r.deal_result=#{dealResult}
        <if test="userId > 0 ">
            and o.operator_id = #{userId}
        </if>
    </select>

    <select id="getEntryStatNum" resultType="com.shuidihuzhu.cf.model.report.EntryStatNum">
      select c.deal_status status ,count(1) num from crowdfunding_report c,
      (select report_id from work_report_map where work_order_id in
        <foreach collection="workOrderIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ) t
      where c.id = t.report_id and c.deal_status in
        <foreach collection="dealResults" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

        group by c.deal_status
    </select>



    <select id="getLostNum" resultType="java.lang.Integer">

      select count(1)
      from admin_cf_lost_contact
      where create_time between #{start} and #{end} and lost=1 and valid=1 and is_delete=0

    </select>


    <select id="getUserLostNum" resultType="java.lang.Integer">

    select count(1)
    from admin_work_order_report r
    left join admin_work_order o on r.work_order_id=o.id
    left join crowdfunding_info i on r.case_id=i.id
    left join admin_cf_lost_contact l on i.info_id = l.info_uuid
    where l.create_time between #{start} and #{end}
    and l.lost=1
    and l.valid=1
    and l.is_delete=0
    and o.operator_id=#{userId}

    </select>


    <select id="queryWorkOrderIdAndOperatorId" resultType="com.shuidihuzhu.cf.model.report.ReportStatTotal$SimpleWorkIdAndOperatorId">
      select r.work_order_id, o.operator_id
      from admin_work_order_report r left join admin_work_order o
      on r.work_order_id = o.id
      where r.create_time >= #{createTime}
    </select>

    <select id="queryOrderHandleResult" resultType="com.shuidihuzhu.cf.model.report.ReportStatTotal$SimpleOrderHandleResult">
      select r.work_order_id, o.operator_id, r.deal_result, r.create_time, o.update_time, o.handle_result, r.case_id, r.handle_time
      from admin_work_order_report r left join admin_work_order o
      on r.work_order_id = o.id
      where r.update_time >= #{updateTime}
      and o.operator_id > 0 and r.deal_result in
      <foreach collection="dealStatus" item="item" open="(" separator="," close=")">
          #{item}
      </foreach>
    </select>

    <select id="queryReportIdByWorkIds" resultType="java.lang.Integer">
        select report_id from work_report_map where work_order_id in
        <foreach collection="workOrderIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryNoHandleWorks" resultType="com.shuidihuzhu.cf.model.report.ReportStatTotal$SimpleOrderHandleResult">
        select r.id, r.work_order_id, o.operator_id, r.deal_result, r.create_time, o.update_time, o.handle_result, r.case_id, r.handle_time
        from admin_work_order_report r left join admin_work_order o
        on r.work_order_id = o.id
        where r.id >= #{fromId} AND r.deal_result in
        <foreach collection="dealStatus" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="operatorIds != null and operatorIds.size() > 0">
            AND o.operator_id IN
            <foreach collection="operatorIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        AND r.is_delete = 0
        AND o.is_delete = 0
        order by r.id
    </select>

    <select id="getHandlerUserByAssignTime" resultType="com.shuidihuzhu.cf.model.report.ReportStatTotal$SimpleWorkIdAndOperatorId">
      select r.work_order_id, o.operator_id
      from admin_work_order_report r left join admin_work_order o
      on r.work_order_id=o.id
      where r.handle_time between #{start} and #{end}
      and o.operator_id > 0
    </select>


    <select id="getUserLostNumByDate" resultType="java.lang.Integer">

    select count(1)
    from admin_work_order_report r
    left join admin_work_order o on r.work_order_id=o.id
    left join crowdfunding_info i on r.case_id=i.id
    left join admin_cf_lost_contact l on i.info_id = l.info_uuid
    where l.create_time between #{start} and #{end}
    and l.lost=1
    and l.valid=1
    and l.is_delete=0
    and o.operator_id=#{userId}

    </select>


</mapper>