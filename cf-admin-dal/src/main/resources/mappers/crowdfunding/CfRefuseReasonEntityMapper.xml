<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfRefuseReasonEntityDao">

    <sql id="table_name">
        `cf_refuse_reason_entity`
    </sql>

    <sql id="insert_fields">
        `content`,
        `tag_id`,
        `item_ids`,
        `choice_relation_ids`,
        `suggest_modify_ids`,
        `weight_service_lib`,
        `custom_type`,
        `is_delete`
    </sql>

    <sql id="select_fields">
        `id`,
        `content`,
        `tag_id`,
        `item_ids`,
        `frequency`,
        `choice_relation_ids`,
        `suggest_modify_ids`,
        `weight_service_lib`,
        `custom_type`,
        `is_delete`
    </sql>

    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES (#{content}, #{tagId}, #{itemIds}, #{choiceRelationIds}, #{suggestModifyIds}, #{weightServiceLib}, #{customType}, #{isDelete})
    </insert>

    <select id="selectByIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `is_delete`=0 AND `id` IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY
        <foreach collection="list" item="item" open="field(id," separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="frequencyPlusOne">
        UPDATE <include refid="table_name"/>
        SET `frequency`=`frequency`+1
        WHERE `id` IN
        <foreach collection="set" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="selectAll" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        LIMIT #{start},#{size}
    </select>

    <update id="deleteOne">
        UPDATE <include refid="table_name"/>
        SET `is_delete`=1
        WHERE `id`=#{id}
    </update>

    <select id="selectByIdAndDeleteStatus" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity">
    SELECT <include refid="select_fields"/>
    FROM <include refid="table_name"/>
    WHERE  `id`=#{id}
    <if test="isDelete != null">
      AND  `is_delete`= #{isDelete}
    </if>
    </select>

    <select id="selectByReasonIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `id` IN
        <foreach collection="set" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="deleteStatus != null">
            AND `is_delete`= #{deleteStatus}
        </if>
    </select>


    <update id="updateDeleteStatus">
        UPDATE <include refid="table_name"/>
        SET `is_delete`= #{deleteStatus}
        WHERE `id`= #{id}
    </update>

    <select id="selectByTagIdAndDeleteStatus" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE tag_id = #{tagId}  AND `is_delete`= #{deleteStatus}
        ORDER BY update_time
    </select>

    <update id="updateStateById">
        UPDATE <include refid="table_name"/>
        <set>
            <if test="deleteStatus != null">
                `is_delete`= #{deleteStatus},
            </if>
            <if test="choiceRelationIds != null">
                `choice_relation_ids`= #{choiceRelationIds},
            </if>
        </set>
        WHERE `id`= #{id}
    </update>

    <select id="selectAllValidEntitys" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE is_delete = 0
    </select>

    <update id="updateItemAndSuggestModify">
        UPDATE <include refid="table_name"/>
        SET `item_ids`= #{itemIds},
        `suggest_modify_ids` = #{suggestModifyIds},
        `weight_service_lib` = #{weightServiceLib}
        WHERE `id`= #{entityId}
    </update>

</mapper>
