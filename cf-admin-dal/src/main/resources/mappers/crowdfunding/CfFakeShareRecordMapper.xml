<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfFakeShareRecordDao">

    <sql id="table_name">
        `cf_fake_share_record`
    </sql>

    <sql id="insert_fields">
        `info_uuid`,
        `user_id`
    </sql>

    <sql id="select_fields">
        `id`,
        `info_uuid`,
        `user_id`
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfFakeShareRecord">
        INSERT <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES (#{infoUuid},#{userId})
    </insert>

    <insert id="insertList">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.infoUuid},#{item.userId})
        </foreach>
    </insert>

    <select id="selectByLimit" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfFakeShareRecord">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        LIMIT #{start}, #{size}
    </select>
</mapper>