<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.ReportWorkOrderFollowActionDao">

    <sql id="tableName">
		report_work_order_follow_action
	</sql>

    <insert id="insertOne">
        insert into
        <include refid="tableName"/>
        (work_order_id,operator_id,order_type,action_type)
        values (#{workOrderId},#{operatorId},#{orderType},#{actionType})
    </insert>

    <select id="getCountByOperatorId" resultType="com.shuidihuzhu.cf.model.report.ReportWorkOrderCount">
        select operator_id,count(distinct work_order_id) as num
        from
        <include refid="tableName"/>
        where <![CDATA[ create_time > #{dayOfZero} ]]>
        and operator_id in
        <foreach collection="operatorIds" open="(" close=")" item="operatorId" separator=",">
            #{operatorId}
        </foreach>
        and order_type in
        <foreach collection="orderTypes" open="(" close=")" item="orderType" separator=",">
            #{orderType}
        </foreach>
        and is_delete = 0
        GROUP BY operator_id
    </select>

    <select id="getCount" resultType="com.shuidihuzhu.cf.model.report.ReportWorkOrderCount">
        select operator_id,count(distinct work_order_id) as num
        from
        <include refid="tableName"/>
        where <![CDATA[ create_time > #{dayOfZero} ]]>
        and order_type = #{orderType}
        and operator_id = #{operatorId}
        and is_delete = 0
    </select>

    <select id="getByWorkOrderId" resultType="com.shuidihuzhu.cf.model.report.ReportWorkOrderFollowAction">
        select * from
        <include refid="tableName"/>
        where work_order_id = #{workOrderId}
        and action_type in
        <foreach collection="actionTypes" open="(" close=")" separator="," item="actionType">
            #{actionType}
        </foreach>
        and is_delete = 0
    </select>

    <select id="getWorkOrderId" resultType="java.lang.Long">
        select distinct work_order_id
        from
        <include refid="tableName"/>
        where <![CDATA[ create_time > #{dayOfZero} ]]>
        and order_type in
        <foreach collection="orderTypes" open="(" close=")" item="orderType" separator=",">
            #{orderType}
        </foreach>
        and operator_id = #{operatorId}
        and is_delete = 0
    </select>


</mapper>
