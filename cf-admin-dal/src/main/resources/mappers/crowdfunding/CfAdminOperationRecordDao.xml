<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfAdminOperationRecordDao">

    <sql id="tableName">
        cf_admin_operation_record
    </sql>

    <sql id="fields">
        `id`,
        `info_id`,
        `operator_id`,
        `operation`,
        `reason`,
        `created_time` as createdTime,
        `last_modified` as lastModified
    </sql>

    <sql id="insertFields">
        `info_id`,
        `operator_id`,
        `operation`,
        `reason`
    </sql>

    <insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfOperationRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tableName"/>
        (<include refid="insertFields"/>)
        VALUES
        (#{infoId},#{operatorId},#{operation},#{reason})
    </insert>

    <select id="selectOpByInfoId" parameterType="java.lang.String" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfOperationRecord">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>

        force index(idx_info_id)

        WHERE `info_id`=#{info_id} AND operation <![CDATA[ < ]]> 4
        ORDER BY created_time DESC limit 1
    </select>

    <select id="selectContact" parameterType="java.lang.String" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfOperationRecord">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>

        force index(idx_info_id)

        WHERE `info_id`=#{infoId} AND `operation` = 11
        ORDER BY `created_time` DESC
        LIMIT 1
    </select>

    <select id="getLastByOperation"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.CfOperationRecord">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE `info_id`=#{infoId}
        AND `operation` = #{operation}
        ORDER BY `created_time` DESC
        LIMIT 1
    </select>

    <select id="selectRefuseOperation" resultType="java.util.Map">
        SELECT DISTINCT(info_id) AS infoId,count(*) AS counts
        FROM shuidi_crowdfunding.cf_admin_operation_record
        WHERE operation = 1 GROUP BY info_id LIMIT #{curNum},5000
    </select>

    <select id="getLastOneByInfoUUidList" parameterType="java.lang.String" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfOperationRecord">
        select <include refid="fields"/>
        FROM (SELECT *
        FROM <include refid="tableName"/>
        WHERE  operation <![CDATA[ < ]]> 4
        AND `info_id` IN
        <foreach collection="infoUuids" item="infoUuid" close=")" open="(" separator=",">
            #{infoUuid}
        </foreach>
        order by `id` DESC ) o
        GROUP BY o.`info_id`
    </select>


</mapper>