<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCredibleInfoWorkOrderDAO">

    <sql id="table_name">
        `cf_report_credible_info_work_order`
    </sql>

    <sql id="insert_fields">
        case_id,work_order_id,credible_info_id
    </sql>

    <select id="queryByCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfCredibleInfoWorkOrderDO">
        select *
        from <include refid="table_name"/>
        where `case_id`=#{caseId} and `is_delete`=0
    </select>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfCredibleInfoWorkOrderDO">
        insert into <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        values
        (#{caseId},#{workOrderId},#{credibleInfoId})
    </insert>


</mapper>