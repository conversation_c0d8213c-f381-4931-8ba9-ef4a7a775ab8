<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCfRefuseReasonMsgDao">

    <sql id="table_name">
        `cf_refuse_reason_msg`
    </sql>

    <sql id="insert_fields">
        `info_uuid`,
        `type`,
        `reason_ids`,
        `item_ids`,
        `item_reason`,
        `suggest_modify`,
        `refuse_count`
    </sql>

    <sql id="select_fields">
        `id`,
        `info_uuid`,
        `type`,
        `reason_ids`,
        `item_ids`,
        `item_reason`,
        `suggest_modify`,
        `create_time`
    </sql>

    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonMsg">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES
        (#{infoUuid},#{type},#{reasonIds},#{itemIds},#{itemReason},#{suggestModify},#{refuseCount})
    </insert>

    <select id="selectByInfoUuidAndType" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonMsg">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `info_uuid`=#{infoUuid} AND `type`=#{type}
        AND `is_delete`=0 AND `disable`=0
        ORDER BY `create_time` DESC
    </select>

    <select id="selectByInfoIdAndType" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonMsg">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `info_uuid`=#{infoUuid} AND `type`=#{type}
        AND `is_delete`=0 AND `disable`=0
        ORDER BY `create_time` DESC
        limit 1
    </select>

    <insert id="insertList">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.infoUuid},#{item.type},#{item.reasonIds},#{item.itemIds},#{item.itemReason},#{item.suggestModify},#{item.refuseCount})
        </foreach>
    </insert>

    <select id="selectWithTimeLimit" resultType="java.lang.String">
        SELECT `reason_ids` FROM <include refid="table_name"/>
        WHERE <![CDATA[ `create_time`>=#{begin} ]]> AND <![CDATA[ `create_time`<#{end} ]]>
        LIMIT #{start},#{size}
    </select>

    <update id="deleteByInfoUuid">
        UPDATE <include refid="table_name"/>
        SET `disable`=1
        WHERE `info_uuid`=#{infoUuid}
    </update>

    <update id="deleteByInfoUuidAndTypes">
        UPDATE <include refid="table_name"/>
        SET `disable`=1
        WHERE `info_uuid`=#{infoUuid}
        AND `type` IN
        <foreach collection="set" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="selectByInfoUuid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonMsg">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `info_uuid`=#{infoUuid}
        ORDER BY `create_time` DESC
    </select>

    <select id="selectSimpleFieldsByTimeLimit" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonMsg">
        SELECT `id`,`reason_ids`,`refuse_count`
        FROM <include refid="table_name"/>
        WHERE <![CDATA[ `create_time`>=#{begin} ]]> AND <![CDATA[ `create_time`<#{end} ]]>
        LIMIT #{start},#{size}
    </select>
</mapper>