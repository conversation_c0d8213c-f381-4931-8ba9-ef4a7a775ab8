<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfReportProblemDao">

    <sql id="table_name">
        `cf_report_problem`
    </sql>

    <sql id="insert_fields">
        `label_id`,
        `problem`,
        `problem_type`,
        `answer_type`,
        `direct_show`,
        `unknow_tag`,
        `must_answer`,
        `is_need_verify`,
        `sort`,
        show_location,
        must_use
    </sql>

    <sql id="select_fields">
        `id`,
        `label_id` as labelId,
        `problem` as problem,
        `problem_type` as problemType,
        `answer_type` as answerType,
        `direct_show` as directShow,
        `unknow_tag` as unknowTag,
        `must_answer` as mustAnswer,
        `is_use` as isUse,
        `is_need_verify` as isNeedVerify,
        `sort` as sort,
        show_location,
        must_use
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfReportProblem" useGeneratedKeys="true" keyProperty="id">
        INSERT  ignore <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES (#{labelId}, #{problem}, #{problemType}, #{answerType}, #{directShow}, #{unknowTag},
        #{mustAnswer}, #{isNeedVerify}, #{sort},#{showLocation},#{mustUse})
    </insert>

    <update id="updateById">
        update ignore <include refid="table_name"/>
        set
        `problem` = #{problem},
        `problem_type` = #{problemType},
        `answer_type` = #{answerType},
        `direct_show` = #{directShow},
        `unknow_tag` = #{unknowTag},
        `must_answer` = #{mustAnswer},
        `is_need_verify` = #{isNeedVerify},
         `sort` = #{sort},
        show_location = #{showLocation},
        must_use = #{mustUse}
        where id = #{id}
    </update>


    <select id="findByLabelIdAndProblem" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportProblem">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `label_id` = #{labelId} and `problem` = #{problem}
    </select>

    <select id="findById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportProblem">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `id` = #{id}
    </select>


    <select id="listByIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportProblem">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        <where>
            `id` in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>


    <select id="listDirectShowByLabelId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportProblem">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        <where>
            `label_id` in
            <foreach collection="labelIds" item="labelId" open="(" separator="," close=")">
                #{labelId}
            </foreach> and `direct_show` = 0 and is_use = 1
            and show_location in
            <foreach collection="showLocations" item="showLocation" open="(" separator="," close=")">
                #{showLocation}
            </foreach>
        </where>
    </select>

    <select id="listForManager" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportProblem">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        <where>
            <if test="problemDesc != null and problemDesc != ''">
                `problem` like  concat('%','${problemDesc}','%')
            </if>
            <if test="isUse != null and isUse > 0">
                and is_use = #{isUse}
            </if>
        </where>
    </select>


    <select id="listByLabelId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportProblem">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        <where>
            `label_id` = #{labelId}
        </where>
    </select>
    
    <update id="updateUseStatusById">
       update <include refid="table_name"/>
       set is_use = #{isUse}
       where id = #{id}
    </update>


    <select id="getList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportProblem">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        <where>
            <if test="problemDesc != null and problemDesc != ''">
                `problem` like  concat('%','${problemDesc}','%') and
            </if>
            <if test="isUse >= 0">
                 is_use = #{isUse} and
            </if>
            <if test="labelId >= 0">
                  `label_id` = #{labelId} and
            </if>
             is_delete = 0
        </where>
        <if test="collation == null">
            order by is_use desc,create_time
        </if>
        <if test="collation != null and collation == 0">
            order by sort, is_use desc,create_time
        </if>
        <if test="collation != null and collation == 1">
            order by sort desc, is_use desc,create_time
        </if>
    </select>


    <select id="listByLabelIdAndIsUse" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportProblem">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        <where>
            `label_id` = #{labelId}
             and is_use = #{isUse} and  is_delete = 0
        </where>
    </select>

</mapper>