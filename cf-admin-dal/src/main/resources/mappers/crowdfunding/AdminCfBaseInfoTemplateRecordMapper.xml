<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCfBaseInfoTemplateRecordDao">

    <sql id="table_name">
        `cf_base_info_template_record`
    </sql>

    <sql id="select_fields">
        `id`,
        `info_uuid`,
        `title_id`,
        `content_id`,
        `relationship`,
        `amount`,
        `name`,
        `author_name`,
        `disease_name`,
        `hospital_name`,
        `age`,
        `hometown`,
        `disaster_day`,
        `cost`
    </sql>

    <select id = "selectByParam" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplateRecord">
      SELECT <include refid="select_fields"/>
      FROM <include refid="table_name"/>
      <where>
          <if test="infoUuid != null and infoUuid != ''">
            AND info_uuid = #{infoUuid}
          </if>
          <if test="authorName != null and authorName != ''">
           AND author_name = #{authorName}
          </if>
      </where>
    </select>

</mapper>