<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfUpgradeWorkOrderRecordDao">
    <sql id="TABLE_NAME">
        cf_upgrade_work_order_record
    </sql>

    <sql id="INSERT_FIELDS">
        (`case_id`, `work_order_id`, `work_order_type`, `upgrade_type`, `upgrade_work_order_id`, `reason`, `operator_id`)
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.admin.CfUpgradeWorkOrderRecord">
        insert into <include refid="TABLE_NAME"/>
        <include refid="INSERT_FIELDS"/>
        values(#{caseId}, #{workOrderId}, #{workOrderType}, #{upgradeType}, #{upgradeWorkOrderId}, #{reason}, #{operatorId})
    </insert>
</mapper>