<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdFundingVerificationDao">

    <sql id="table_name">
        `crowd_funding_verification`
    </sql>

    <select id="getVerify" parameterType="String"
            resultType="com.shuidihuzhu.cf.vo.crowdfunding.CrowdfundingfundingVerificationVo">
        SELECT COUNT(id) as count,crowd_funding_info_id
        FROM
        <include refid="table_name"/>
        WHERE
        crowd_funding_info_id IN
        <foreach collection="crowdfundingInfoIds" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
        GROUP BY crowd_funding_info_id
    </select>

    <update id="updateValid">
        UPDATE <include refid="table_name"/>
        SET `valid`=#{valid},operate_time = now()
        WHERE `id`=#{id}
    </update>
</mapper>
