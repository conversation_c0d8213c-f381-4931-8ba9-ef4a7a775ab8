package com.shuidihuzhu.cf.dao;


import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.CfGrowthToolDS;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2018-08-29  20:15
 */
@DataSource(CfGrowthToolDS.CF_SLAVE2_DATA_SOURCE)
public interface AdminCrowdfundingVolunteerCardConfigDao {

	CrowdfundingVolunteer getByUniqueCode(@Param("uniqueCode") String uniqueCode);

	CrowdfundingVolunteer getByPhone(@Param("mobile") String mobile);

	CrowdfundingVolunteer getByIdCardNumber(@Param("idCardNumber") String idCardNumber);
}
