<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.comment.CrowdfundingCommentShareDao">

    <sql id="tableName">
        crowdfunding_comment_share
    </sql>

    <sql id="fields">
        `id`, `crowdfunding_id`,`parent_id`,`user_id`,`user_third_id`,`user_third_type`,`comment_id`,`content`,`type`,`is_deleted`,`create_time`
    </sql>

    <sql id="insertFields">
        `id`, `crowdfunding_id`,`parent_id`,`comment_id`,`user_id`,`user_third_id`,`user_third_type`,`content`,`type`
    </sql>


    <select id="getByIdNoCareDeleted" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingComment">
        SELECT
        <include refid="fields"/>
        FROM
        <include refid="tableName"/>
        WHERE crowdfunding_id=#{crowdfundingId} AND id=#{id}
    </select>


    <select id="getCommentByParentId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingComment">
        SELECT
        <include refid="fields"/>
        FROM
        <include refid="tableName"/>
        WHERE crowdfunding_id =#{crowdfundingId} AND parent_id=#{parentId}
    </select>

</mapper>
