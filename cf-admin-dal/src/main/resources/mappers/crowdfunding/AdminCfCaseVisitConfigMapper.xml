<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCfCaseVisitConfigDao">
	<sql id="TABLE">
		cf_case_visit_config
	</sql>

	<sql id="FIELDS">
		`id` as id,
		`case_id` as caseId,
		`show_banner` as showBanner,
		`banner_img_url` as bannerImgUrl,
		`banner_url` as bannerUrl,
		`sharable` as sharable,
		`show_popup` as showPopup,
		`popup_text` as popupText,
		`popup_title` as popupTitle,
		`change_title` as changeitle,
		`official_dynamic` as officialDynamic,
		`operator` as operator,
		`operator_id` as operatorId,
		`abnormal_hidden` as abnormalHidden,
		`abnormal_hidden_self_title` as abnormalHiddenSelfTitle,
		`abnormal_hidden_other_title` as abnormalHiddenOtherTitle,
		`update_time` as updateTime
	</sql>

	<select id="getByCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminCfCaseVisitConfig">
		SELECT <include refid="FIELDS"/>
		FROM <include refid="TABLE"/>
		WHERE `case_id`=#{caseId}
		AND `is_delete`=0
		LIMIT 1
	</select>

	<select id="getList" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminCfCaseVisitConfig">
		SELECT <include refid="FIELDS"	/>
		FROM <include refid="TABLE"/>
		WHERE `is_delete`=0
		ORDER BY `id` DESC
	</select>


	<select id="getListByType" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminCfCaseVisitConfig">
		SELECT <include refid="FIELDS"	/>
		FROM <include refid="TABLE"/>
		WHERE `is_delete`=0
		<if test="caseId != null and caseId != 0">
			and case_id=#{caseId}
		</if>
		<if test="operator != null and operator != 0">
			and operator_id=#{operator}
		</if>
		<if test="caseId == null or caseId == 0">
			and operator_id >0
		</if>
		<if test="start != null and end != null">
			and update_time between #{start} and #{end}
		</if>
		<if test="type == 1">
			and show_banner=true
		</if>
		<if test="type == 2">
			and sharable=false
		</if>
		<if test="type == 3">
			and change_title=true
		</if>
		<if test="type == 4">
			and official_dynamic=true
		</if>
		<if test="type == 5">
			and abnormal_hidden = true
		</if>

		ORDER BY `update_time` DESC
	</select>



	<insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.AdminCfCaseVisitConfig">
		INSERT INTO <include refid="TABLE"/>
		(`case_id`, `show_banner`, `banner_img_url`, `banner_url`, `sharable`, `show_popup`, `popup_text`, `popup_title`,`change_title`,`official_dynamic`,`operator`,`operator_id`)
		VALUES
		(#{caseId}, #{showBanner}, #{bannerImgUrl}, #{bannerUrl}, #{sharable}, #{showPopup}, #{popupText}, #{popupTitle},#{changeitle},#{officialDynamic},#{operator},#{operatorId})
	</insert>

	<update id="update" parameterType="com.shuidihuzhu.cf.model.crowdfunding.AdminCfCaseVisitConfig">
		UPDATE <include refid="TABLE"/>
		SET `show_banner`=#{showBanner},
		`banner_img_url` =#{bannerImgUrl},
		`banner_url`=#{bannerUrl},
		`sharable`=#{sharable},
		`show_popup`=#{showPopup},
		`popup_text`=#{popupText},
		`popup_title`=#{popupTitle},
		`change_title` = #{changeitle},
		`official_dynamic` = #{officialDynamic},
		`operator` = #{operator},
		`operator_id` = #{operatorId}
		WHERE `id`=#{id}
	</update>

	<update id="updateAbnormalHiddenAndHiddenTitle">
		UPDATE <include refid="TABLE"/>
		<set>
			<if test="abnormalHidden != null">
				`abnormal_hidden`=#{abnormalHidden},
			</if>
			<if test="abnormalHiddenSelfTitle != null">
				`abnormal_hidden_self_title`=#{abnormalHiddenSelfTitle},
			</if>
			<if test="abnormalHiddenOtherTitle != null">
				`abnormal_hidden_other_title`=#{abnormalHiddenOtherTitle}
			</if>
		</set>
		WHERE `case_id`=#{caseId}
	</update>

	<select id="getOperatorList" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.AdminOperatorVo">
		SELECT operator_id as operatorId,operator as operator
		FROM <include refid="TABLE"/>
		WHERE `is_delete`=0 and operator_id>0  group by operator_id
	</select>
	<update id="updateCanShowByCaseId">
		update <include refid="TABLE"/>
		set can_show = #{canShow}
		where `case_id`=#{caseId}
	</update>
	<update id="updateBannerTextAndStartEndTime">
		update <include refid="TABLE"/>
		set banner_text=#{bannerText},
			start_time=#{startTime},
			end_time=#{endTime}
		where `case_id`=#{caseId}
	</update>
</mapper>