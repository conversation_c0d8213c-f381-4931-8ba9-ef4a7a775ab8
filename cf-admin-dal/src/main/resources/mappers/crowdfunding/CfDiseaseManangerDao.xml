<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfDiseaseManagerDao">

    <sql id="table_name">
        `cf_disease_manager`
    </sql>

    <sql id="insert_fields">
        `disease_classify_id`,
        `standard_name`,
        `medical_name`,
        `normal_name`,
        `treatment_project`,
        `constom_treatment`,
        `min_treatment_fee`,
        `max_treatment_fee`,
        `raise_type`
    </sql>

    <sql id="select_fields">
        `id`,
        `disease_classify_id` as diseaseClassifyId,
        `standard_name` as standardName,
        `medical_name` as medicalName,
        `normal_name` as normalName,
        `treatment_project` as treatmentProject,
        `constom_treatment` as customTreatment,
        `min_treatment_fee` as minTreatmentFee,
        `max_treatment_fee` as maxTreatmentFee,
        `raise_type` as raiseType,
        `create_time` as createTime,
        `update_time` as updateTime,
        `is_delete`
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfDiseaseManagerDO" useGeneratedKeys="true" keyProperty="id">
        INSERT <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES (#{diseaseClassifyId}, #{standardName}, #{medicalName}, #{normalName}, #{treatmentProject},
        #{customTreatment}, #{minTreatmentFee}, #{maxTreatmentFee}, #{raiseType})
    </insert>

    <update id="edit" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfDiseaseManagerDO" useGeneratedKeys="true" keyProperty="id">
        update <include refid="table_name"/>
        <set>
            <if test="diseaseClassifyId > 0">
                `disease_classify_id`= #{diseaseClassifyId},
            </if>
            <if test="standardName != null">
                `standard_name`= #{standardName},
            </if>
            <if test="standardName != null">
                `medical_name` = #{medicalName},
            </if>
            <if test="normalName != null">
                `normal_name` = #{normalName},
            </if>
            <if test="treatmentProject > 0">
                `treatment_project` = #{treatmentProject},
            </if>
            <if test="customTreatment != null">
                `constom_treatment` = #{customTreatment},
            </if>
            <if test="minTreatmentFee > 0">
                `min_treatment_fee` = #{minTreatmentFee},
            </if>
            <if test="maxTreatmentFee > 0">
                `max_treatment_fee` = #{maxTreatmentFee},
            </if>
            <if test="raiseType > 0">
                `raise_type` = #{raiseType}
            </if>
        </set>
        WHERE `id`= #{id}
    </update>

    <update id="delete">
        update <include refid="table_name"/>
        set `is_delete` = 1
        where `id` = #{id}
    </update>

    <select id="listForSeaAdmin" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfDiseaseManagerDO">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        <where>
            <if test="diseaseClassifyId > 0">
                `disease_classify_id`= #{diseaseClassifyId} and
            </if>
            <if test="standardName != null and standardName != ''">
                `standard_name` like CONCAT('%', #{standardName}, '%') and
            </if>
            <if test="medicalName != null and medicalName != ''">
                `medical_name` like CONCAT('%', #{medicalName}, '%') and
            </if>
            <if test="normalName != null and normalName != ''">
                `normal_name` like CONCAT('%', #{normalName}, '%') and
            </if>
            <if test="raiseType > 0">
                `raise_type` = #{raiseType} and
            </if>
                `is_delete` = 0
        </where>
    </select>


    <select id="getById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfDiseaseManagerDO">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        <where>
            `id` = #{id} and `is_delete` = 0
        </where>
    </select>
</mapper>