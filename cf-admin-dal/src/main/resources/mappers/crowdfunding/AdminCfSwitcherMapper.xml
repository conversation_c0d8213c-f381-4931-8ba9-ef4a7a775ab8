<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCfSwitcherDao">
    <sql id="TABLE">
        cf_switcher
    </sql>
    <sql id="QUERY_FIELDS">
        `id`,
        `name`,
        `comment`,
        `value`,
        `date_created` as dateCreated,
		`last_modified` as lastModified
    </sql>

    <sql id="INSERT_FIELDS">
        `id`,
        `name`,
        `comment`,
        `value`
    </sql>
    
    <select id="getAll" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfSwitcher">
    	SELECT <include refid="QUERY_FIELDS" />
    	FROM <include refid="TABLE" />
    </select>

    <insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfSwitcher">
        INSERT INTO
        <include refid="TABLE"/>
        (<include refid="INSERT_FIELDS"/>)
        VALUES (#{id},#{name},#{comment},#{value})
    </insert>

    <select id="getByName" parameterType="java.lang.String" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfSwitcher">
        SELECT <include refid="QUERY_FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE name=#{name}
    </select>

    <update id="updateValue">
        UPDATE <include refid="TABLE"/>
        SET `value` =#{value}
        WHERE id=#{id}
    </update>
</mapper>