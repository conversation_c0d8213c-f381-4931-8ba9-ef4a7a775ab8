<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCfFundUseAuditDao">
    <sql id="TABLE_NAME">
        crowdfunding_fund_use_progress
    </sql>

    <update id="updateAuditStatusRejected">
        UPDATE <include refid="TABLE_NAME"/>
        SET `fund_use_rejected_reason` = #{fundAuditRejectedReason},
        `status` = 2
        WHERE `id` = #{progressId}
    </update>
	
	<update id="updateDrawTime">
		UPDATE <include refid="TABLE_NAME"/>
		SET `draw_finish_time` = #{drawTime}
		WHERE `id` = #{progressId}
	</update>
	
    <update id="updateAuditPass">
        UPDATE <include refid="TABLE_NAME"/>
        SET `status` = 1
        where `id` = #{progressId}
    </update>

    <select id="selectByProgressId" resultMap="selectFundUseAuditResult">
        SELECT *
        FROM <include refid="TABLE_NAME"/>
        WHERE `id` = #{progressId}
    </select>
	
	<resultMap id="selectFundUseAuditResult" type="com.shuidihuzhu.cf.model.crowdfunding.AdminCfFundUseAuditInfo">
		<id property="crowdfundingId" column="crowdfunding_id"/>
		<id property="drawCashTime" column="draw_finish_time"/>
		<id property="fundUseSubmitTime" column="create_time"/>
		<id property="fundUseImageMaterial" column="image_urls"/>
		<id property="fundAuditStatus" column="status"/>
		<id property="fundUseContent" column="content"/>
		<id property="progressId" column="progress_id"/>
	</resultMap>
	
	
	<select id="getFundUseAuditInfoByUnionSelect" resultMap="selectFundUseAuditResult">
		SELECT distinct crowdfunding_id, `draw_finish_time`, progress.`create_time`, progress.`content`,
		progress.`image_urls`, progress.`status`, progress.`id` as progress_id
		FROM  crowdfunding_fund_use_progress as progress
		<if test="hasReport=='true' or hasReport=='false'">
			left  join  crowdfunding_report as report on  progress.crowdfunding_id = report.activity_id
		</if>
		WHERE progress.`is_delete` = 0
		<if test="crowdfundingIds != null">
			AND progress.crowdfunding_id IN
			<foreach collection="crowdfundingIds" item="crowdfundingId" open="(" separator="," close=")">
				#{crowdfundingId}
			</foreach>
		</if>
		<if test="progressStatus != null">
			and progress.status = #{progressStatus}
		</if>
		<if test="drawCashStartTime != null and drawCashEndTime != null">
			and progress.draw_finish_time between #{drawCashStartTime} AND #{drawCashEndTime}
		</if>
		<if test="fundUseSubmitStartTime != null and fundUseSubmitEndTime != null">
			and progress.create_time between #{fundUseSubmitStartTime} and #{fundUseSubmitEndTime}
		</if>
		<if test="hasReport=='true'">
			and ifnull(report.id, 0) > 0
			and report.deal_status = 2
		</if>
		<if test="hasReport=='false'">
			and ifnull(report.deal_status, 0) != 2
		</if>
		ORDER BY `progress`.`create_time` DESC
		LIMIT #{current}, #{pageSize}
	</select>
	
	<select id="getRecordsByMultiField" resultType="java.lang.Integer">
		
		SELECT count(distinct progress.id)
		FROM crowdfunding_fund_use_progress as progress
		<if test="hasReport=='true' or hasReport=='false'">
			left  join  crowdfunding_report as report on  progress.crowdfunding_id = report.activity_id
		</if>
		WHERE progress.`is_delete` = 0
		<if test="crowdfundingIds != null">
			AND progress.crowdfunding_id IN
			<foreach collection="crowdfundingIds" item="crowdfundingId" open="(" separator="," close=")">
				#{crowdfundingId}
			</foreach>
		</if>
		<if test="progressStatus != null">
			and progress.status = #{progressStatus}
		</if>
		<if test="drawCashStartTime != null and drawCashEndTime != null">
			and progress.draw_finish_time between #{drawCashStartTime} AND #{drawCashEndTime}
		</if>
		
		<if test="fundUseSubmitStartTime != null and fundUseSubmitEndTime != null">
			and progress.create_time between #{fundUseSubmitStartTime} and #{fundUseSubmitEndTime}
		</if>
		<if test="hasReport=='true'">
			and ifnull(report.id, 0) > 0
			and report.deal_status = 2
		</if>
		<if test="hasReport=='false'">
			and ifnull(report.deal_status, 0) != 2
		</if>
	
	</select>
	
</mapper>