<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfReportProblemLabelDao">

    <sql id="table_name">
        `cf_report_problem_label`
    </sql>

    <sql id="insert_fields">
        `label_desc`,
        `parent_id`,
        `level`,
        `sort`,
         is_use,
         is_mandatory
    </sql>

    <sql id="select_fields">
        `id`,
        `label_desc` as labelDesc,
        `parent_id` as parentId,
        `level` as labelLevel,
        `is_use` as isUse,
        `sort` as sort,
        `is_mandatory` as isMandatory
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfReportProblemLabel" useGeneratedKeys="true" keyProperty="id">
        INSERT ignore <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES (#{labelDesc}, #{parentId}, #{labelLevel}, #{sort}, #{isUse}, #{isMandatory})
    </insert>

    <select id="findByIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportProblemLabel">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        <where>
            `id` in
            <foreach collection="ids" item="labelId" open="(" separator="," close=")">
                #{labelId}
            </foreach>
            <if test="isUse != null and isUse > 0">
                and is_use = #{isUse}
            </if>
        </where>
    </select>

    <select id="findByDesc" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportProblemLabel">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        <where>
            label_desc = #{labelDesc}
        </where>
    </select>


    <select id="listByLevelAndParentId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportProblemLabel">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        <where>
            <if test="level != null and level > 0">
                `level` = #{level}
            </if>
             <if test="parentId != null and parentId != ''">
                 and `parent_id` = #{parentId}
             </if>
            <if test="isUse != null and isUse > 0">
                and is_use = #{isUse}
            </if>
        </where>
    </select>


    <update id="updateLabel">
        update <include refid="table_name"/>
        set label_desc = #{labelDesc}, sort = #{sort}
        where id = #{id} and is_delete = 0
    </update>


    <insert id="addProblemClassify" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfReportProblemLabel" useGeneratedKeys="true" keyProperty="id">
        insert ignore into <include refid="table_name"/>
        (`label_desc`, `sort`, `level`)
        VALUES (#{labelDesc}, #{sort}, #{labelLevel})
    </insert>

    <update id="updateIsUse">
        update <include refid="table_name"/>
        set is_use = #{isUse}
        where id = #{id}
    </update>

    <select id="getByParentIdAndIsUse" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportProblemLabel">
        select * from
        <include refid="table_name"/>
        where parent_id = #{parentId} and is_use = #{isUse} and is_delete = 0
    </select>

    <update id="updateProblemModule">
        update <include refid="table_name"/>
        set parent_id = #{parentId}, label_desc = #{labelDesc}, sort = #{sort}, is_mandatory = #{isMandatory}
        where id = #{id} and is_delete = 0
    </update>

    <select id="getById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportProblemLabel">
         select <include refid="select_fields"/>
         from <include refid="table_name"/>
         where id = #{id} and is_delete = 0
    </select>

    <select id="getByPartentIdAndLabelDesc" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportProblemLabel">
        select <include refid="select_fields"/>
        from <include refid="table_name"/>
        where parent_id = #{parentId} and label_desc = #{labelDesc} and is_delete = 0
    </select>

</mapper>