<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCfRepeatFinishInfoDAO">

    <sql id="tableName">
        crowdfunding_repeat_finish_info
    </sql>

    <sql id = "Base_Field">
        case_id, finish_case_id, repeat_base_info, finish_type, finish_id
    </sql>


    <insert id = "insert" >
        INSERT INTO <include refid="tableName"/>
        (<include refid="Base_Field"/>)
        VALUES (#{caseId},
              #{finishCaseId},
            #{repeatBaseInfo},
            #{finishType},
            #{finishId}
            )
    </insert>

    <insert id = "insertList" >
        INSERT INTO <include refid="tableName"/>
        (<include refid="Base_Field"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.caseId},
            #{item.finishCaseId},
            #{item.repeatBaseInfo},
            #{item.finishType},
            #{item.finishId}
            )
        </foreach>
    </insert>


</mapper>