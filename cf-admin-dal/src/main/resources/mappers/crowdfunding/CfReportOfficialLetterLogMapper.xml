<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfReportOfficialLetterLogDao">

    <sql id="tableName">
		cf_report_official_letter_log
	</sql>

    <sql id="selectFields">
		id,
		letter_id,
		user_id,
		comment,
		create_time
	</sql>

    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.report.CfReportOfficialLetterLog">
        insert into
        <include refid="tableName"/>
        (letter_id,user_id,comment)
        values (#{letterId},#{userId},#{comment})
    </insert>
    <select id="getByLetterId" resultType="com.shuidihuzhu.cf.model.report.CfReportOfficialLetterLog">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        where letter_id = #{letterId}
        and is_delete = 0
    </select>

</mapper>
