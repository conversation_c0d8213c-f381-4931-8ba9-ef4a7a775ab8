<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfReportFollowCommentDao">
    <sql id="tableName">
        `cf_report_follow_comment`
    </sql>

    <sql id="fields">
        `id`,`info_id`,`operator_id`,`operator_name`,`comment`,`follow_type`,`organization`,`create_time`,`update_time`
    </sql>
    <sql id="insertFields">
        (`info_id`,`operator_id`,`operator_name`,`comment`,`follow_type`, `organization`)
    </sql>

    <select id="getCommentListByInfoId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportFollowComment">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE is_delete = 0
        AND `info_id` = #{infoId}
    </select>


    <select id="getCommentListByInfoIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportFollowComment">
        SELECT c1.*
        FROM <include refid="tableName"/> c1,
        (SELECT max(id) as id FROM <include refid="tableName"/>
        WHERE  `info_id` IN
        <foreach collection="infoIds" item="infoId" open="(" separator="," close=")">
            #{infoId}
        </foreach>
        GROUP BY `info_id`) c2
        WHERE is_delete = 0
        AND c1.id = c2.id
    </select>

    <insert id="save" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfReportFollowComment">
        INSERT INTO <include refid="tableName"/>
        <include refid="insertFields"/>
        VALUES (#{infoId},#{operatorId},#{operatorName},#{comment},#{followType},#{organization})
    </insert>

    <select id="descFindByInfoId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportFollowComment">
        select
        <include refid="fields"/>
        from
        <include refid="tableName"/>
        where info_id = #{infoId}
        and is_delete = 0
        order by id desc
    </select>
</mapper>