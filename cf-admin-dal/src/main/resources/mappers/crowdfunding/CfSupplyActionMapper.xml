<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfSupplyActionDao">

    <sql id="table_name">
        cf_info_supply_action
    </sql>

    <sql id="insert_fields">
        `case_id`,
        `action_type`,
        `handle_status`,
        `supply_user_id`,
        `supply_org_id`,
        `supply_org_name`,
        `supply_reason`,
        `comment`,
        `org_for_search`,
        img_urls,
        use_template
    </sql>

    <sql id="select_fields">
        `id`,
        `case_id` as caseId,
        `action_type` as actionType,
        `handle_status` as handleStatus,
        `supply_user_id` as supplyUserId,
        `supply_org_id` as supplyOrgId,
        `supply_org_name` as supplyOrgName,
        `supply_reason` as supplyReason,
        `org_for_search` as orgForSearch,
        `comment` as comment,
        `create_time` as createTime,
        `update_time` as updateTime,
        img_urls,
        use_template
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES
        (#{caseId}, #{actionType}, #{handleStatus}, #{supplyUserId}, #{supplyOrgId},
        #{supplyOrgName}, #{supplyReason}, #{comment}, #{orgForSearch},#{imgUrls},#{useTemplate})
    </insert>

    <update id="updateHandleStatus">
        UPDATE
        <include refid="table_name"/>
        SET `handle_status`= #{handleStatus}
        WHERE `id` = #{id}
    </update>


    <select id="getById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyAction">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE `id` = #{id}
    </select>


    <select id="listByCaseIdAndActionType" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyAction">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE `case_id` = #{caseId} and
        `action_type` = #{actionType}
    </select>


    <select id="listByCaseIdAndActionTypes" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyAction">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE `case_id` = #{caseId}
        and  `action_type`   in
        <foreach collection="actionTypes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <select id="listByCaseIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyAction">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE `case_id` IN
        <foreach collection="caseIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and `action_type` = #{actionType}
    </select>


    <select id="listBySupplyActionSearchParam" parameterType="com.shuidihuzhu.cf.model.param.SupplyActionSearchParam"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyAction">
        SELECT
        cfsupply.`id`,
        cfsupply.`case_id` as caseId,
        cfsupply.`action_type` as actionType,
        cfsupply.`handle_status` as handleStatus,
        cfsupply.`supply_user_id` as supplyUserId,
        cfsupply.`supply_org_id` as supplyOrgId,
        cfsupply.`supply_org_name` as supplyOrgName,
        cfsupply.`supply_reason` as supplyReason,
        cfsupply.`comment` as comment,
        cfsupply.`create_time` as createTime,
        cfsupply.`update_time` as updateTime
        FROM
        <choose>
            <when test="submitStartTime != null or submitEndTime != null">
                cf_info_supply_progress as sp left join
                <include refid="table_name"/> as cfsupply on sp.progress_action_id = cfsupply.id
            </when>
            <otherwise>
                <include refid="table_name"/> as cfsupply
            </otherwise>
        </choose>
        <if test="caseStatus != null">
            left join crowdfunding_info as ci on cfsupply.case_id = ci.id
        </if>
        <where>
            cfsupply.action_type = 1
            <if test="caseId != null and caseId > 0">
                and cfsupply.case_id = #{caseId}
            </if>
            <if test="handleStatus != null">
                and cfsupply.handle_status = #{handleStatus}
            </if>
            <if test="orgId != null and orgId >0">
                and cfsupply.org_for_search = #{orgId}
            </if>
            <if test="createStartTime != null">
                and cfsupply.create_time &gt;= #{createStartTime}
            </if>
            <if test="createEndTime != null">
                and cfsupply.create_time &lt;= #{createEndTime}
            </if>
            <if test="handleStartTime != null">
                and cfsupply.update_time &gt;= #{handleStartTime}
            </if>
            <if test="handleEndTime != null">
                and cfsupply.update_time &lt;= #{handleEndTime}
            </if>
            <if test="caseStatus != null">
                and ci.status = #{caseStatus}
            </if>
            <if test="submitStartTime != null">
                and sp.create_time &gt;= #{submitStartTime}
            </if>
            <if test="submitEndTime != null">
                and sp.create_time &lt;= #{submitEndTime}
            </if>
            order by cfsupply.create_time desc
        </where>
    </select>


    <select id="listCaseIdByReasonAndOrg" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyAction">
        SELECT <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        <where>
            <if test="reason > 0">
                `supply_reason` LIKE CONCAT('%', #{reason}, '%')
            </if>
            <if test="org > 0">
                and `org_for_search` = #{org}
            </if>
        </where>
    </select>


    <select id="listByIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyAction">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE `id` IN
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>


    <update id="updateForSubmit">
        UPDATE
        <include refid="table_name"/>
        SET `handle_status`= #{handleStatus},
        comment = #{comment},img_urls=#{imgUrls}
        WHERE `id` = #{id}
    </update>

    <insert id="insertCfInfoSupplyFields" parameterType="com.shuidihuzhu.client.cf.admin.model.CfInfoSupplyField" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        cf_info_supply_field
        (action_id,field,field_name,field_value,operation_type,modify,field_id)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.actionId},
            #{item.field},
            #{item.fieldName},
            #{item.fieldValue},
            #{item.operationType},
            #{item.modify},
            #{item.fieldId}
            )
        </foreach>
    </insert>


    <update id="deleteSupplyField">
        UPDATE
        cf_info_supply_field
        SET `is_delete`= 1
        WHERE `action_id` = #{actionId} and operation_type=#{operationType} and is_delete=0
    </update>


    <select id="getByActionId" resultType="com.shuidihuzhu.client.cf.admin.model.CfInfoSupplyField">
        SELECT *
        FROM cf_info_supply_field
        WHERE action_id = #{actionId} and is_delete=0
    </select>

    <select id="listByCaseIdAndHandleStatus" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyAction">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE `case_id` = #{caseId}
        <if test = "statusList != null and statusList.size() > 0">
            AND  `handle_status`   IN
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        AND is_delete = 0
        ORDER BY id desc
    </select>

</mapper>