<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCfInfoStatDao">
    <sql id="table_name">
        `cf_info_stat`
    </sql>

    <sql id="FIELDS">
        id as id,
        share_count as shareCount,
        donation_count as donationCount,
        verify_user_count as verifyUserCount,
        comment_count as commentCount,
        amount as amount,
        verify_friend_count as verifyFriendCount,
        verify_hospital_count as verifyHospitalCount,
        blessing_count as blessingCount
    </sql>

    <select id="selectFakeShare" resultType="int">
        SELECT `id`
        FROM <include refid="table_name"/>
        WHERE
        <![CDATA[ IFNULL(amount / share_count, 0) < #{modulus} ]]>
        AND share_count > #{shareCount}
    </select>
</mapper>