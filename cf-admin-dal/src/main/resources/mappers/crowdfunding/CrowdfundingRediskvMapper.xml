<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingRediskvDao">

    <sql id="tablename">
        cf_redis_kv
    </sql>
    <select id="selectMailRecipient" resultType="String">
        SELECT v
        FROM <include refid="tablename"/>
        WHERE k = #{k}
    </select>

    <insert id="addOne">
        INSERT INTO
        <include refid="tablename"/>
        (`k`, `v`)
        VALUES (#{k}, #{v})
    </insert>
</mapper>