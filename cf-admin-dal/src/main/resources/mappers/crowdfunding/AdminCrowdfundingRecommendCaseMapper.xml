<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingRecommendCaseDao">
    <sql id="tableName">
        crowdfunding_recommend_case
    </sql>



    <select id="getInfoIdList" resultType="java.lang.String">
        SELECT
        info_id as infoId
        FROM
        <include refid="tableName"/>
        WHERE
        `type`=#{type}
        AND
        `valid`=1
        ORDER BY sort
        LIMIT #{limit}
    </select>

    <select id="getIdsByTypeOrderBySort" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminCrowdfundingRecommendCaseDO">
        select id, info_id
        from <include refid="tableName"/>
        where type = #{type}
        order by sort
    </select>

    <select id="getTypeByInfoIdAndTypes" resultType="java.lang.Integer">
        select `type`
        from <include refid="tableName"/>
        <where>
            `info_id` = #{infoUuid}
            and `type` in
            <foreach collection="types" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and `valid` = 1
        </where>
        union all
        select recommend_type as 'type'
        from cf_recommend_history
        <where>
            `info_id` = #{infoUuid}
            and recommend_type in
            <foreach collection="types" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        limit 1
    </select>

    <update id="batchUpdateCrowdfundingRecommendCaseByIds" parameterType="java.util.List">
        INSERT INTO <include refid="tableName"/>
        (id,info_id) VALUES
        <foreach collection="crowdfundingRecommendCaseDOs" item="crowdfundingRecommendCaseDO" separator=",">
            (#{crowdfundingRecommendCaseDO.id},#{crowdfundingRecommendCaseDO.infoId})
        </foreach>
        ON duplicate KEY UPDATE
        info_id=VALUES(info_id)
    </update>



    <!--快手小程序白名单-->
    <insert id="insert">
        insert into <include refid="tableName"/>
        (info_id,type,sort,case_id,creator,case_status,user_id,patient_name)
        values
        (#{infoId},#{type},#{sort},#{caseId},#{creator},#{caseStatus},#{userId},#{patientName})
    </insert>

    <select id="getByUuid" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminCrowdfundingRecommendCaseDO">
        select id, info_id as infoId,type as type,sort as sort,create_time as createTime,
        case_id as caseId,creator as creator,case_status as caseStatus
        from <include refid="tableName"/>
        where info_id = #{infoId}
        and type = #{type}
    </select>

    <!--<update id="delete">
        update <include refid="tableName"/>
        set valid = 0,
        creator = #{creator},
        user_id = #{userId}
        where id = #{id}
        and valid = 1
    </update>-->
    <delete id="delete">
        delete from <include refid="tableName"/> where id = #{id}
    </delete>

    <update id="updateStatus">
        update <include refid="tableName"/>
        set case_status = #{caseStatus}
        where id = #{id}
        and valid = 1
    </update>

    <select id="getById" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminCrowdfundingRecommendCaseDO">
        select id, info_id as infoId,type as type,sort as sort,create_time as createTime,
        case_id as caseId,creator as creator,case_status as caseStatus
        from <include refid="tableName"/>
        where id = #{id}
        and valid = 1
    </select>

    <select id="getCaseList" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminCrowdfundingRecommendCaseDO">
        select id, info_id as infoId,type as type,sort as sort,create_time as createTime,
        case_id as caseId,creator as creator,case_status as caseStatus,patient_name as patientName
        from <include refid="tableName"/>
        <where>
            <if test="creator != null and creator !=''">
                and creator like CONCAT('%', #{creator} , '%')
            </if>
            <if test="status != null">
                and case_status = #{status}
            </if>
            <if test="infoId != null and infoId != ''">
                and info_id = #{infoId}
            </if>
            and type = #{type}
            and valid = 1
            order by id desc
            limit #{offset},#{limit}
        </where>
    </select>

    <select id="getCaseListCount" resultType="java.lang.Long">
        select id
        from <include refid="tableName"/>
        <where>
        <if test="creator != null and creator !=''">
            and creator like CONCAT('%', #{creator} , '%')
        </if>
        <if test="status != null">
            and case_status = #{status}
        </if>
        <if test="infoId != null and infoId != ''">
            and info_id = #{infoId}
        </if>
        and type = #{type}
        and valid = 1
        </where>
    </select>

</mapper>
