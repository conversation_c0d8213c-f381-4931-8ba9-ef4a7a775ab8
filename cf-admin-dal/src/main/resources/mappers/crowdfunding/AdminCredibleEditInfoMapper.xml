<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCredibleEditInfoDAO">
    <sql id="table_name">
        `cf_credible_edit_info`
    </sql>

    <sql id="insert_fields">
        case_id,source,content,image_urls
    </sql>

    <select id="queryById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfCredibleEditInfoDO">
        select *
        from <include refid="table_name"/>
        where `id`=#{id} and `is_delete`=0
    </select>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfCredibleEditInfoDO" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        values
        (#{caseId},#{source},#{content},#{imageUrls})
    </insert>
</mapper>