<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfSendProveRejectedReasonDao">

    <sql id="tableName">
         cf_send_prove_rejected_reason
    </sql>

    <insert id="insertOne">
        insert into
        <include refid="tableName"/>
        (case_id,prove_id,rejected_reason,type)
        values (#{caseId},#{proveId},#{rejectedReason},#{type})
    </insert>

</mapper>
