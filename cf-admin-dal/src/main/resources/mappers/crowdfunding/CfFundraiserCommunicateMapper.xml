<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfReportFundraiserCommunicateDAO">
    <sql id="table_name">
        cf_report_fundraiser_communicates
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfFundraiserCommunicateDO">
        INSERT INTO <include refid="table_name"/>
        (`case_id`, `relation_key`, `relation_value`, `mobile`, `connect_status`, `answer_ids`, `operator_id`, connect_object)
        VALUES
        (#{caseId}, #{relationKey}, #{relationValue}, #{mobile}, #{connectStatus}, #{answerIds}, #{operatorId}, #{connectObject})
    </insert>

    <update id="updateConnectStatus">
        update <include refid="table_name"/>
        set connect_status = #{connectStatus}
        where id = #{id}
    </update>

    <update id="updateAnswer">
        update <include refid="table_name"/>
        set connect_status = #{connectStatus},answer_ids = #{answerIds}
        where id = #{id}
    </update>

    <select id="query" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfFundraiserCommunicateDO">
        SELECT *
        FROM <include refid="table_name"/>
        WHERE case_id = #{caseId}
    </select>

    <select id="queryById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfFundraiserCommunicateDO">
        SELECT *
        FROM <include refid="table_name"/>
        WHERE id = #{id}
    </select>

    <select id="queryByIdAndCase" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfFundraiserCommunicateDO">
        SELECT *
        FROM <include refid="table_name"/>
        WHERE id = #{id} and case_id = #{caseId}
    </select>

    <select id="getByMobileAndCaseId"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.CfFundraiserCommunicateDO">
        select * from
        <include refid="table_name"/>
        where case_id = #{caseId}
        and mobile = #{mobile}
        and is_delete = 0
    </select>

</mapper>