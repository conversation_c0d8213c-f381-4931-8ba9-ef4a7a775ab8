<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfReportOfficialLetterDao">

    <sql id="tableName">
		cf_report_official_letter
	</sql>

    <sql id="selectFields">
		id,
		case_id,
		letter_type,
		num,
		images,
		letter_status,
		comment,
        `name`
	</sql>

    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.report.CfReportOfficialLetter" keyProperty="id"
            useGeneratedKeys="true">
        insert into
        <include refid="tableName"/>
        (case_id,letter_type,num,images,letter_status,comment,`name`)
        values (#{caseId},#{letterType},#{num},#{images},#{letterStatus},#{comment},#{name})
    </insert>

    <select id="getByCaseId" resultType="com.shuidihuzhu.cf.model.report.CfReportOfficialLetter">
        select
        *
        from
        <include refid="tableName"/>
        where case_id = #{caseId}
        and is_delete = 0
    </select>

    <update id="update">
        update
        <include refid="tableName"/>
        set letter_type = #{letterType},num = #{num},images = #{images},letter_status = #{letterStatus},
        comment = #{comment}
        where id = #{id}
    </update>

    <select id="getById" resultType="com.shuidihuzhu.cf.model.report.CfReportOfficialLetter">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        where id = #{id}
        and is_delete = 0
    </select>

    <select id="getLastByCaseId" resultType="com.shuidihuzhu.cf.model.report.CfReportOfficialLetter">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        where case_id = #{caseId}
        and is_delete = 0
        order by id desc
    </select>


</mapper>
