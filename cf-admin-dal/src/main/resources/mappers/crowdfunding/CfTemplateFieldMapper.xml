<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfTemplateFieldDao">


    <insert id="addField" parameterType="com.shuidihuzhu.client.cf.admin.model.CfItemField" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO cf_item_field
        (`field`, `field_type`, `field_limiter`, `field_length`,`is_delete`)
        VALUES
        (#{field}, #{fieldType}, #{fieldLimiter}, #{fieldLength},1)
    </insert>



    <update id="updateField">
        UPDATE cf_item_field
        SET `is_delete`= #{delete}
        WHERE id = #{id}
    </update>



    <select id="listCfItemFields" resultType="com.shuidihuzhu.client.cf.admin.model.CfItemField">
        select *
        FROM cf_item_field
        WHERE is_delete= #{delete}
    </select>


    <select id="getCfItemFieldByIds" resultType="com.shuidihuzhu.client.cf.admin.model.CfItemField">
        select *
        FROM cf_item_field
        WHERE id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <insert id="addTemplate" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfItemTemplate" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO cf_item_template
        (`template_name`, `template_value`, `level`, `parent_id`,`repeat_send`,`is_delete`)
        VALUES
        (#{templateName}, #{templateValue}, #{level}, #{parentId},#{repeatSend},1)
    </insert>

    <update id="updateTemplate">
        UPDATE cf_item_template
        SET `is_delete`= #{delete}
        WHERE id = #{id}
    </update>


    <select id="getTemplates" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfItemTemplate">
        select *
        FROM cf_item_template
        WHERE is_delete = #{delete} and level=0

    </select>

    <select id="getTemplatesByParentId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfItemTemplate">
        select *
        FROM cf_item_template
        WHERE is_delete = #{delete} and parent_id = #{id}

    </select>

    <select id="getById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfItemTemplate">
        select *
        FROM cf_item_template
        WHERE id = #{id}

    </select>

    <select id="getTemplatesByIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfItemTemplate">
        select *
        FROM cf_item_template
        WHERE id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>


    <insert id="addTemplateField" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfTemplateField" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO cf_template_field
        (`template_id`, `field_id`,`template_name`)
        VALUES
        <foreach collection="list" separator="," item="item">
            ( #{item.templateId}, #{item.fieldId},#{item.templateName})
        </foreach>

    </insert>


    <select id="getFieldIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfTemplateField">
        select *
        FROM cf_template_field
        WHERE is_delete = 0 and field_id in
        <foreach collection="fieldIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>

    <select id="getFieldByTempId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfTemplateField">
        select *
        FROM cf_template_field
        WHERE is_delete = 0 and template_id = #{tempId}

    </select>


    <select id="getFieldByTempIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfTemplateField">
        select *
        FROM cf_template_field
        WHERE is_delete = 0 and template_id  in
        <foreach collection="tempIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>

</mapper>
