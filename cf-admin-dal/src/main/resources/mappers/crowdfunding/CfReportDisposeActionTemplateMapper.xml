<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfReportDisposeActionTemplateDao">

    <sql id="tableName">
        cf_report_dispose_action_template
    </sql>

    <sql id="selectFields">
        id,
        action_id,
        title,
        content,
        commitment_content
    </sql>

    <insert id="addTemplate">
        insert into
        <include refid="tableName"/>
        (action_id,title,content,commitment_content)
        values
        (#{actionId},#{title},#{content},#{commitmentContent})
    </insert>

    <update id="updateByActionId">
        update
        <include refid="tableName"/>
        set title = #{title} , content = #{content} , commitment_content = #{commitmentContent}
        where action_id = #{actionId}
    </update>

    <select id="selectByActionIds" resultType="com.shuidihuzhu.cf.model.report.CfReportDisposeActionTemplate">
        select <include refid="selectFields"/>
        from <include refid="tableName"/>
        where action_id in
        <foreach collection="actionIds" open="(" close=")" item="actionId" separator=",">
            #{actionId}
        </foreach>
    </select>

    <select id="selectByActionId" resultType="com.shuidihuzhu.cf.model.report.CfReportDisposeActionTemplate">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        where action_id = #{actionId}
        and is_delete = 0
    </select>

</mapper>
