<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfReportProblemOperationLogDao">

    <sql id="table_name">
        `cf_report_problem_operation_log`
    </sql>


    <insert id="add">
        insert into <include refid="table_name"/>
        (`problem_id`, `action`, `operator`)
        values
        (#{problemId}, #{action}, #{operator})
    </insert>


    <select id="getByProblemId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportProblemOperationLog">
        select * from
        <include refid="table_name"/>
        where problem_id = #{problemId} and is_delete = 0
        order by id desc
    </select>

</mapper>