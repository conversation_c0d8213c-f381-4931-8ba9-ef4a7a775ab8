<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCfBaseInfoTemplateOperatorHistoryDao">

    <sql id="table_name">
        `cf_base_info_templatize_operator_history`
    </sql>

    <sql id="insert_fields">
        `cf_base_template_id`,
        `remark`,
        `operator_id`,
        `operator_name`
    </sql>

    <sql id="select_fields">
        `cf_base_template_id`,
        `remark`,
        `operator_id`,
        `operator_name`,
        `create_time`
    </sql>

    <select id="selectByTemplateId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplateOperatorHistory">
        select <include refid="select_fields"/>
        from <include refid="table_name"/>
        where `cf_base_template_id`=#{cfBaseTemplateId} and `is_delete`=0
    </select>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplateOperatorHistory">
        insert into <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        values
        (#{cfBaseTemplateId},#{remark},#{operatorId},#{operatorName})
    </insert>
</mapper>