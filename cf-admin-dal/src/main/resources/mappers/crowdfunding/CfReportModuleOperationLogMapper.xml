<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfReportModuleOperationLogDao">

    <sql id="table_name">
        `cf_report_module_operation_log`
    </sql>



    <select id="getByLabelId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportModuleOperationLog">
        select * from <include refid="table_name"/>
        where label_id = #{labelId} and is_delete = 0
        order by id desc
    </select>

    <insert id="addLog">
        insert into <include refid="table_name"/>
        (`label_id`, `action`, `operator`)
        values
        (#{labelId}, #{action}, #{operator})
    </insert>

</mapper>