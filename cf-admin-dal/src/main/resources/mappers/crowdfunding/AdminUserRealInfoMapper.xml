<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">



<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminUserRealInfoDao">

    <sql id="tableName">
        user_real_info
    </sql>

    <select id="getSuccessByUserIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.UserRealInfo">
        SELECT *
        FROM <include refid="tableName" />
        WHERE `user_id` in <foreach collection="userIds" open="(" separator="," close=")" item="userId">#{userId}</foreach>
        AND `idcard_verify_status`=2
        AND is_delete = 0
    </select>

</mapper>