<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCfReportDisposeActionDao">

	<sql id="tableName">
        cf_report_dispose_action
    </sql>


    <insert id="add" keyProperty="id" useGeneratedKeys="true">
        insert into
        <include refid="tableName"/>
        (dispose_action, action_classify_id,is_help,has_template)
        values (#{disposeAction}, #{actionClassifyId},#{isHelp},#{hasTemplate})
    </insert>

    <update id="updateDisponseAction">
        update
        <include refid="tableName"/>
        set dispose_action = #{disposeAction}, action_classify_id = #{actionClassifyId},is_help = #{isHelp},has_template
        = #{hasTemplate}
        where id = #{id}
    </update>


    <update id="updateIsUse">
		update <include refid="tableName"/>
		set is_use = #{isUse}
		where id = #{id}
	</update>


	<select id="getAll" resultType="com.shuidihuzhu.cf.model.report.CfReportDisposeAction">
         select * from
         <include refid="tableName"/>
         where is_delete = 0
	</select>


	<select id="getByActionClassifyId" resultType="com.shuidihuzhu.cf.model.report.CfReportDisposeAction">
         select * from
         <include refid="tableName"/>
         where action_classify_id = #{actionClassifyId}
         and is_delete = 0
	</select>


	<select id="getByUse" resultType="com.shuidihuzhu.cf.model.report.CfReportDisposeAction">
		select * from
		<include refid="tableName"/>
		where is_use = #{isUse} and is_delete = 0
	</select>

    <select id="selectByIds" resultType="com.shuidihuzhu.cf.model.report.CfReportDisposeAction">
        select * from
        <include refid="tableName"/>
        where id in
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id}
        </foreach>
    </select>

	<select id="getById" resultType="com.shuidihuzhu.cf.model.report.CfReportDisposeAction">
        select * from <include refid="tableName"/>
        where id = #{id}
    </select>
</mapper>
