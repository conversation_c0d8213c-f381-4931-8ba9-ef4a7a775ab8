<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfBlacklistWordDao">

    <sql id="table_name">
        `cf_blacklist_word`
    </sql>

    <sql id="insert_fields">
        `word`,`type`
    </sql>

    <sql id="select_fields">
        `id`,`word`,`type`,`disable`
    </sql>

    <sql id="word">
        `word`
    </sql>

    <insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfBlacklistWord">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES (#{word},#{type})
    </insert>

    <insert id="addList" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfBlacklistWord">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.word},#{item.type})
        </foreach>
    </insert>

    <select id="selectAllWordsLimit" resultType="java.lang.String">
        SELECT <include refid="word"/>
        FROM <include refid="table_name"/>
        WHERE `is_delete`=0 AND `type`=#{type}
        LIMIT #{start},#{size}
    </select>

</mapper>