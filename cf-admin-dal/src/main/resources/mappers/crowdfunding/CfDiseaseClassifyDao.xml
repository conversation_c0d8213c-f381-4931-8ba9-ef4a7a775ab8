<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfDiseaseClassifyDao">

    <sql id="table_name">
        `cf_disease_classify`
    </sql>

    <sql id="insert_fields">
        `classify_desc`
    </sql>

    <sql id="select_fields">
        `id`,
        `classify_desc` as classifyDesc
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfDiseaseClassifyDO" useGeneratedKeys="true" keyProperty="id">
        INSERT <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES (#{classifyDesc})
    </insert>

    <select id="listAllByPage" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfDiseaseClassifyDO">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE id > #{id} limit #{limit}
    </select>


</mapper>