<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminTwModifyDao">

    <sql id="tableName">
        cf_tw_modify_record
    </sql>

    <sql id="fields">
        `info_uuid`,
        `work_order_id`,
        `modify_channel`,
        `modify_flag`
    </sql>

    <insert id="saveTwModifyRecord" parameterType="com.shuidihuzhu.cf.model.admin.TwModifyRecordDO">
        INSERT INTO
        <include refid="tableName"/>
        (<include refid="fields"/>)
        VALUES
        (#{infoUuid}, #{workOrderId}, #{modifyChannel}, #{modifyFlag});
    </insert>

    <update id="updateTwModifyFlag">
        update
        <include refid="tableName"/>
        set `modify_flag` = #{modifyFlag}
        where `work_order_id` = #{wordOrderId}
        and `is_delete` = 0
    </update>

    <select id="selectByInfoId" resultType="com.shuidihuzhu.cf.model.admin.TwModifyRecordDO">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where `info_uuid` = #{infoUuid}
        and `is_delete` = 0
        limit 1
    </select>

    <select id="selectByWorkOrderId" resultType="com.shuidihuzhu.cf.model.admin.TwModifyRecordDO">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where `work_order_id` = #{workOrderId}
        and `is_delete` = 0
        limit 1
    </select>

    <select id="selectByInfoIds" resultType="com.shuidihuzhu.cf.model.admin.TwModifyRecordDO">
        SELECT
        <include refid="fields"/>
        FROM
        <include refid="tableName"/>
        WHERE `info_uuid` in
        <foreach collection="infoUuids" open="(" close=")" item="infoUuid" separator=",">
            #{infoUuid}
        </foreach>
    </select>

    <select id="selectByWorkOrderIdList" resultType="com.shuidihuzhu.cf.model.admin.TwModifyRecordDO">
        SELECT
        <include refid="fields"/>
        FROM
        <include refid="tableName"/>
        WHERE `work_order_id` in
        <foreach collection="workOrderIdList" open="(" close=")" item="workOrderId" separator=",">
            #{workOrderId}
        </foreach>
    </select>

</mapper>