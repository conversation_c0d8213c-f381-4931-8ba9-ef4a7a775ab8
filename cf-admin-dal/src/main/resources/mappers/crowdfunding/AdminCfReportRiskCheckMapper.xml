<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCfReportRiskCheckDao">

    <sql id="tableName">
        crowdfunding_report_risk_check
    </sql>

    <sql id="insertFields">
        `case_id`,`content`,`operator_id`
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.report.AdminCfReportRiskCheck">
        INSERT INTO
        <include refid="tableName"/>
        (<include refid="insertFields"/>)
        VALUES
        (#{caseId},#{content},#{operatorId});
    </insert>

    <select id="getByCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.report.AdminCfReportRiskCheck">
        select * from  <include refid="tableName"/>
        where case_id = #{caseId}
        and is_delete = 0 order by id desc
    </select>
</mapper>
