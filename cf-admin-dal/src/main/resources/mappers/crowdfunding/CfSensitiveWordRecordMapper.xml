<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfSensitiveWordRecordDao">

    <sql id="TABLE">
        cf_sensitive_word_record
    </sql>

    <sql id="insert_fields">
        `info_uuid`,`biz_id`,`parent_biz_id`,`biz_type`,`sensitive_word`,`biz_time`,`user_id`,`content`,`mode`,`content_valid`
    </sql>

    <sql id="select_fields">
        `id`,`info_uuid`,`biz_id`,`parent_biz_id`,`biz_type`,`sensitive_word`,`biz_time`,`user_id`,`content`,`mode`,`content_valid`
    </sql>
    <insert id="saveBatch" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
    	INSERT INTO <include refid="TABLE" />
    		(<include refid="insert_fields"/>)
    	VALUES
    		<foreach collection="list" item="cfSensitiveWordRecord" index="index" separator=",">
    			(#{cfSensitiveWordRecord.infoUuid},#{cfSensitiveWordRecord.bizId},#{cfSensitiveWordRecord.parentBizId},
    			#{cfSensitiveWordRecord.bizType},#{cfSensitiveWordRecord.sensitiveWord},#{cfSensitiveWordRecord.bizTime},
    			#{cfSensitiveWordRecord.userId},#{cfSensitiveWordRecord.content},#{cfSensitiveWordRecord.mode},
    			#{cfSensitiveWordRecord.contentValid})
    		</foreach>
    </insert>

    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfSensitiveWordRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="TABLE" />
        (<include refid="insert_fields"/>)
        VALUES
        (#{infoUuid},#{bizId},#{parentBizId},
        #{bizType},#{sensitiveWord},#{bizTime},
        #{userId},#{content},#{mode},
        #{contentValid})
    </insert>
    
    <update id="delByBizTime">
        UPDATE <include refid="TABLE"/>
        SET `is_delete`=1
        WHERE <![CDATA[ `biz_time`>=#{beginTime} AND `biz_time`<#{endTime} ]]>
    </update>

    <select id="selectByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfSensitiveWordRecord">
        SELECT <include refid="select_fields"/>
        FROM <include refid="TABLE"/>
        WHERE `is_delete`=0 AND `user_id` IN
        <foreach collection="set" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfSensitiveWordRecord">
        SELECT <include refid="select_fields"/>
        FROM <include refid="TABLE"/>
        WHERE `is_delete`=0 AND `id` IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfSensitiveWordRecord">
        SELECT <include refid="select_fields"/>
        FROM <include refid="TABLE"/>
        WHERE `is_delete`=0 AND `id`=#{id}
    </select>

    <select id="selectIdByTime" resultType="java.lang.Long">
        SELECT `id`
        FROM <include refid="TABLE"/>
        WHERE <![CDATA[ `biz_time`>=#{beginTime} AND `biz_time`<#{endTime} ]]>
        LIMIT #{start},#{size}
    </select>

    <select id="selectByBizIdAndBizType" resultType="java.lang.Long">
        SELECT id
        FROM <include refid="TABLE"/>
        WHERE `biz_id` = #{bizId}
        AND
        `biz_type` = #{bizType}
        LIMIT 1
    </select>


    <select id="listByBizIdAndBizType" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfSensitiveWordRecord">
        SELECT <include refid="select_fields"/>
        FROM <include refid="TABLE"/>
        WHERE `is_delete`=0
        AND biz_id = #{bizId}
        AND biz_type = #{bizType}
    </select>
</mapper>