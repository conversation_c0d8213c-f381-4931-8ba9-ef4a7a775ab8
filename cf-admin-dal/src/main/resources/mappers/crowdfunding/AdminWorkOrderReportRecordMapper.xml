<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminWorkOrderReportRecordDao">
    <sql id="tableName">
        `admin_work_order_report_record`
    </sql>

    <sql id="insertFields">
        work_order_report_id,
        work_order_id,
        type,
        case_id,
        report_id,
        case_risk,
        follow_type,
        deal_result,
        operator_id
    </sql>

    <insert id="insertAdminWorkOrderReportRecord"
            parameterType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderReportRecord">
        INSERT INTO
        <include refid="tableName"/>
        (<include refid="insertFields"/>)
        VALUES
        (#{workOrderReportId},#{workOrderId},#{type},#{caseId},#{reportId},#{caseRisk},#{followType},#{dealResult},#{operatorId})
    </insert>

    <select id="getFollowCountByTime" resultType="int">
        select count(distinct work_order_report_id)
        from
        <include refid="tableName"/>
        where is_delete = 0
        and follow_type in (1,2,3,4)
        <if test="startTime != null and startTime !=''">
            and create_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null and endTime !=''">
            and create_time <![CDATA[ < ]]> #{endTime}
        </if>
    </select>

    <select id="getFollowCountByUserIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderDataVo">
        select workOrder.operator_id,
        count(distinct record.work_order_report_id) as count
        from
        <include refid="tableName"/>
        record
        join admin_work_order workOrder
        on record.work_order_id = workOrder.id
        where record.is_delete = 0
        and workOrder.order_type = #{orderType}
        and workOrder.order_task = #{orderTask}
        and record.follow_type in (1,2,3,4)
        and workOrder.operator_id in
        <foreach collection="userIds" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
        <if test="startTime != null and startTime !=''">
            and record.create_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null and endTime !=''">
            and record.create_time <![CDATA[ < ]]> #{endTime}
        </if>
        group by workOrder.operator_id
    </select>

</mapper>