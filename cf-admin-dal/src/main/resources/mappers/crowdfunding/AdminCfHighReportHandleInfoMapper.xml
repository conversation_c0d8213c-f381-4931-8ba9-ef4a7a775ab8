<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCfHighReportHandleInfoDao">

    <sql id="TABLE_NAME">
        crowdfunding_high_report_system_handle_info
    </sql>


    <insert id="insertRecord">
        INSERT INTO <include refid="TABLE_NAME"/>
        (case_id, system_end)
        VALUES (#{caseId}, #{systemEnd})
    </insert>

    <select id = "selectHasFinishCaseByTime" resultType="java.lang.Integer">
        SELECT DISTINCT case_id
        FROM
        <include refid="TABLE_NAME"/>
        WHERE
        create_time <![CDATA[ >= ]]> #{beginTime}
        AND
        create_time <![CDATA[ <= ]]> #{endTime}
        AND system_end = #{systemEnd}
        AND is_delete = 0
    </select>

    <select id = "selectByCaseIdAndTime" resultType="java.lang.Integer">
        SELECT case_id
        FROM
        <include refid="TABLE_NAME"/>
        WHERE
        case_id = #{caseId}
        AND
        create_time <![CDATA[ >= ]]> #{beginTime}
        AND
        create_time <![CDATA[ <= ]]> #{endTime}
        AND system_end = #{systemEnd}
        AND is_delete = 0
    </select>



</mapper>
