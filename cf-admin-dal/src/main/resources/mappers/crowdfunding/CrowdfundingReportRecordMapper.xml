<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingReportRecordDao">
    <sql id="table_name">
        cf_report_record
    </sql>

    <sql id="fields">
        `id`,
        `report_id` as reportId,
        `operator_id` as operatorId,
        `deal_status` as dealStatus,
        `comment`,
        `created_time` as createdTime,
    </sql>

    <sql id="insertFields">
        `report_id` ,
        `operator_id` ,
        `deal_status`,
        `comment`
    </sql>

    <insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReportRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insertFields"/>)
        VALUES
        (#{reportId},#{operatorId},#{dealStatus},#{comment})
    </insert>

    <select id="getreportRecordListByReportIds"  resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReportRecord">
        select *
        FROM <include refid="table_name"/>
        where is_delete=0
        AND `report_id` IN
        <foreach item="reportId" index="index" collection="list"
                 open="(" separator="," close=")">
            #{reportId}
        </foreach>
    </select>
    <select id="getreportRecordList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReportRecord">
        select *
        FROM <include refid="table_name"/>
        WHERE is_delete=0
    </select>

    <select id="getReportRecordGroupByReportId" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingReportRecordVo">
        select crr.*,cr.activity_id from <include refid="table_name"/> crr
        join crowdfunding_report cr
        on cr.id = crr.report_id
        where crr.is_delete = 0
        AND crr.`report_id` IN
       <foreach collection="reportIds" open="(" close=")" separator="," item="reportId">
           #{reportId}
       </foreach>
        group by crr.report_id
    </select>
</mapper>