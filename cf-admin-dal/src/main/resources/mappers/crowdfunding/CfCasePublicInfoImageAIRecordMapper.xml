<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfCasePublicInfoImageAIRecordDao">

    <sql id="TABLE">
        cf_case_public_info_image_ai_record
    </sql>

    <sql id="Base_Field">
        `id` as id,
        `case_id` as caseId,
        `info_uuid` as infoUuid,
        `image_url` as imageUrl,
        `image_url_ai` as imageUrlAi,
        `img_handle_status` as imgHandleStatus,
        `result_code` as resultCode,
        `finish_ai_time` as finishAiTime,
        `create_time` as createTime
    </sql>

    <sql id="Insert_Field">
        `case_id`,
        `info_uuid`,
        `image_url`
    </sql>

    <insert id="add" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="TABLE"/>
        (<include refid="Insert_Field"/>)
        VALUES
        (#{caseId},#{infoUuid},#{imageUrl})
    </insert>

    <select id="getById" resultType="com.shuidihuzhu.cf.client.adminpure.model.caseinfo.CfCasePublicInfoImageAIRecord">
        SELECT <include refid="Base_Field"/>
        FROM <include refid="TABLE"/>
        WHERE id = #{id}
        AND `is_delete` = 0

    </select>

    <update id="update" parameterType="com.shuidihuzhu.cf.client.adminpure.model.caseinfo.CfCasePublicInfoImageAIRecord">
        update <include refid="TABLE"/>
        <set>
            `image_url_ai`=#{imageUrlAi}, `img_handle_status`=#{imgHandleStatus}, `result_code`=#{resultCode}, `finish_ai_time`=#{finishAiTime}
        </set>
        where `id`=#{id}
    </update>

</mapper>
