<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfSendProveDao">

    <sql id="tableName">
        cf_send_prove
    </sql>

    <sql id="selectFields">
        id,
        case_id,
        picture_url,
        picture_audit_status,
        rejected_reason,
        cancel_reason,
        picture_rejected_reason
    </sql>

    <insert id="insertOne" keyProperty="id" useGeneratedKeys="true">
        insert into
        <include refid="tableName"/>
        (case_id,picture_audit_status)
        values (#{caseId},#{pictureAuditStatus})
    </insert>

    <select id="getById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfSendProve">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        where id = #{id}
        and is_delete = 0
    </select>

    <update id="auditPictureUrl">
        update
        <include refid="tableName"/>
        set picture_url = #{pictureUrl},picture_audit_status = #{pictureAuditStatus},picture_rejected_reason = #{pictureRejectedReason}
        where id = #{id}
        and is_delete = 0
    </update>
    <update id="updatePictureAuditStatusById">
        update
        <include refid="tableName"/>
        set picture_audit_status = #{pictureAuditStatus}
        where id = #{id}
        and is_delete = 0
    </update>
    <select id="getLastOneByCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfSendProve">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        where case_id = #{caseId}
        and is_delete = 0
        order by id desc limit 1
    </select>
    <update id="updateRejectedReason">
        update
        <include refid="tableName"/>
        set rejected_reason = #{rejectedReason}
        where id = #{id}
        and is_delete = 0
    </update>
    <update id="updateCancelReason">
        update
        <include refid="tableName"/>
        set cancel_reason = #{cancelReason}
        where id = #{id}
        and is_delete = 0
    </update>

</mapper>
