<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCaseConfirmationDao">
    <sql id="TABLE_NAME">
        `cf_case_end_white_list`
    </sql>

    <sql id="fields">
        `id` as id,
        `case_id` as caseId,
        `user_id` as userId,
        `create_time` as createTime,
        `update_time` as updateTime
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfCaseEndWhiteList">
        INSERT INTO
        <include refid="TABLE_NAME"/>
        (`case_id`,`user_id`)
        values(#{caseId},#{userId})
    </insert>

    <select id="selectWhiteList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfCaseEndWhiteList">
        select
        <include refid="fields"/>
        from
        <include refid="TABLE_NAME"/>
        where is_delete = 0
        <if test="caseId != 0">
            and case_id =#{caseId}
        </if>
        order by id desc
    </select>

    <select id="selectByCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfCaseEndWhiteList">
        select
        <include refid="fields"/>
        from
        <include refid="TABLE_NAME"/>
        where `case_id`=#{caseId} and `is_delete`=0
    </select>

</mapper>
