<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdFundingVerificationSlaveDao">

    <sql id="tableName">
        `crowd_funding_verification`
    </sql>


    <select id="getById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification">
        SELECT * FROM <include refid="tableName"/> WHERE id = #{id}
    </select>

    <select id="selectByPage" parameterType="com.shuidihuzhu.common.web.util.admin.BasicExample" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification">
        SELECT * FROM <include refid="tableName"/>
        <include refid="com.shuidihuzhu.cf.dao.message.Example_Where_Clause"/>
    </select>

    <select id="listIdByVerifyUserIdAndCreateTimeWithLimit" resultType="java.lang.Long">
    	select id
    	from <include refid="tableName"/>
    	where verify_user_id = #{verifyUserId,jdbcType=BIGINT}
    	and create_time >= #{createTime,jdbcType=TIMESTAMP}
    	limit #{limit}
	</select>

    <select id="getListByIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification">
        SELECT *
        FROM
        <include refid="tableName"/>
        WHERE
        id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

</mapper>

