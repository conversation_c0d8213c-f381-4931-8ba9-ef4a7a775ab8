<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCfRiskLabelDao">
    <sql id="TABLE_NAME">
        `crowdfunding_risk_label`
    </sql>

    <insert id="add">
        insert ignore into
        <include refid="TABLE_NAME"/>
        (`activity_id`,`risk_label`)
        values
        (#{activityId}, #{riskLabel})
    </insert>

    <update id="updateRiskLabel">
        update <include refid="TABLE_NAME"/>
        set risk_label = #{riskLabel}
        where activity_id = #{activityId}
    </update>

    <select id="getByActivityId" resultType="string">
        select risk_label
        from <include refid="TABLE_NAME"/>
        where activity_id = #{activityId}
    </select>


</mapper>
