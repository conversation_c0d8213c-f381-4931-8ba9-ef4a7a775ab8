<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfLabelRiskDAO">
    <sql id="tableName">
        cf_label_risk
    </sql>

    <sql id="insertFields">
        case_id,risk_type,operator_id
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfLabelRiskDO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tableName"/>
        (<include refid="insertFields"/>)
        VALUES (#{caseId},#{riskType},#{operatorId})
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfLabelRiskDO">
        UPDATE <include refid="tableName"/>
        SET
        `risk_type`=#{riskType},
        `operator_id`=#{operatorId}
        WHERE `case_id` = #{caseId}
    </update>

    <select id="query" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfLabelRiskDO">
        select *
        from <include refid="tableName"/>
        where case_id = #{caseId}
    </select>
</mapper>
