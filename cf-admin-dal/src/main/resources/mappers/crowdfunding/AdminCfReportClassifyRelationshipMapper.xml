<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCfReportClassifyRelationshipDao">

    <sql id="tableName">
        cf_report_classify_relationship
    </sql>


    <insert id="add">
        insert into
        <include refid="tableName"/>
        (action_classify_id, dispose_action_id, label_id, `type`)
        values
        <foreach collection="cfReportClassifyRelationships" open="" close="" separator="," item="item">
            (#{item.actionClassifyId}, #{item.disposeActionId},#{item.labelId},#{item.type})
        </foreach>
    </insert>

    <update id="updateRelationship">
        update
        <include refid="tableName"/>
        set action_classify_id = #{actionClassifyId},
        dispose_action_id = #{disposeActionId},
        `type` = #{type}
        where label_id = #{labelId}
    </update>

    <update id="deleteRelationshipByLabelId">
        update <include refid="tableName"/>
        set is_delete = 1
        where label_id = #{labelId}
    </update>

    <select id="getByLabelId" resultType="com.shuidihuzhu.cf.model.report.CfReportClassifyRelationship">
        select * from
        <include refid="tableName"/>
        where label_id = #{labelId}
        and is_delete = 0
    </select>


    <select id="getByTypeAndLabelIds" resultType="com.shuidihuzhu.cf.model.report.CfReportClassifyRelationship">
        select * from
        <include refid="tableName"/>
        where label_id in
        <foreach collection="labels" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and type = #{type}
        and is_delete = 0
    </select>


</mapper>
