<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminTaskUgcDao">

    <sql id="table_name">
        `admin_task_ugc`
    </sql>

    <sql id="insert_fields">
        `work_order_id`,
        `modules`,
        `content_type`,
        `ext_id`,
        `word_id`,
        `result`,
        `case_id`,
        `action`,
        `hit_words`
    </sql>

    <sql id="select_fields">
        `id`,
        `work_order_id`,
        `modules`,
        `content_type`,
        `ext_id`,
        `word_id`,
        `result`,
        `case_id`,
        `action`,
        `hit_words`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.crowdfunding.AdminTaskUgc">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES (#{workOrderId},#{modules},#{contentType},#{extId},#{wordId},#{result},#{caseId},#{action},#{hitWords})
    </insert>

    <insert id="insertList">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.workOrderId},
            #{item.modules},
            #{item.contentType},
            #{item.extId},
            #{item.wordId},
            #{item.result},
            #{item.caseId},
            #{item.action},
            #{item.hitWords})
        </foreach>
    </insert>

    <select id="selectByWorkOrderIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminTaskUgc">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `work_order_id` IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectModulesLimit" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminTaskUgc">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `modules`=#{modules}
        LIMIT #{start},#{size}
    </select>

    <select id="selectByWorkOrderId" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminTaskUgc">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `work_order_id`=#{workOrderId}
        LIMIT 1
    </select>

    <update id="updateResult">
        UPDATE <include refid="table_name"/>
        SET `result`=#{result}
        WHERE `id`=#{id}
    </update>
    
    <update id="deleteByWordIdList">
        UPDATE <include refid="table_name"/>
        SET `is_delete`=1
        WHERE `word_id` IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>


    <select id="selectFirstByCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminTaskUgc">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `case_id` =#{caseId} and result = 0  and is_delete =0 and content_type=7
        order by id desc limit 1;
    </select>


    <select id="selectFirstCommentByCaseId" resultType="java.lang.String">
        SELECT a.comment
        FROM <include refid="table_name"/> g , admin_work_order a
        WHERE g.work_order_id=a.id and g.`case_id` =#{caseId}  and g.is_delete =0 and g.content_type=7
        order by g.id desc limit 1;
    </select>

    <select id="selectByCreateTimeAndTaskType" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminTaskUgc">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE
        `create_time`  <![CDATA[ >= ]]> #{beginDate}
        AND
        `create_time` <![CDATA[ < ]]> #{endDate}
        AND
        `content_type` = #{contentType}
        ORDER BY create_time limit #{limit}
    </select>

    <select id="selectByUpdateTimeAndTaskStatus" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminTaskUgc">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE
        `update_time`  <![CDATA[ >= ]]> #{beginDate}
        AND
        `update_time` <![CDATA[ < ]]> #{endDate}
        AND
        `content_type` = #{contentType}
        AND
        `result` IN
        <foreach collection="resultStatus" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY `update_time` limit #{limit}
    </select>

    <select id="selectLatelyTaskByCaseIdAndContent" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminTaskUgc">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE
        case_id = #{caseId}
        AND
        `content_type` = #{contentType}
        AND is_delete =0
        ORDER BY `update_time` DESC limit 1
    </select>

</mapper>