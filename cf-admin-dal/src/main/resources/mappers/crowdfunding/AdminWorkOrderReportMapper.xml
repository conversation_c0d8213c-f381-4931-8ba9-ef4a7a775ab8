<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminWorkOrderReportDao">
    <sql id="tableName">
        `admin_work_order_report`
    </sql>

    <sql id="Fields">
        work_order_id,
        type,
        case_id,
        report_id,
        case_risk,
        follow_type,
        deal_result
    </sql>

    <sql id="selectFields">
        report.id,
        report.work_order_id,
        report.type,
        report.case_id,
        report.report_id,
        report.case_risk,
        report.follow_type,
        report.deal_result
    </sql>

    <insert id="insertAdminWorkOrderReport" parameterType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderReport"
            keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tableName"/>
        (<include refid="Fields"/>)
        VALUES
        (#{workOrderId},#{type},#{caseId},#{reportId},#{caseRisk},#{followType},#{dealResult})
    </insert>

    <update id="updateAppointTime">
        update <include refid="tableName"/>
        set `appoint_time` = #{appointTime}
        where work_order_id = #{workOrderId}
    </update>

    <select id="getByWorkOrderIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderReport">
        select * from <include refid="tableName"/>
        where work_order_id in
        <foreach collection="workOrderIds" item="workOrderId" open="(" separator="," close=")">
            #{workOrderId}
        </foreach>
    </select>

    <select id="getAdminWorkOrderReportByCaseId" parameterType="int"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderReport">
        select
        *
        from
        <include refid="tableName"/>
        where case_id = #{caseId}
        and is_delete = 0
        order by create_time desc
        limit 1
    </select>

    <select id="getOneByCaseIdAndStatusList" parameterType="int"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderReport">
        select
        *
        from
        <include refid="tableName"/>
        <where>
            case_id = #{caseId}
            and `deal_result` in
            <foreach collection="statusList" open="(" separator="," close=")" item="status">
                #{status}
            </foreach>
            and is_delete = 0
        </where>
        limit 1
    </select>

    <select id="listByCaseIdsAndStatusList"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderReport">
        select
        *
        from
        <include refid="tableName"/>
        <where>
            and case_id in
            <foreach collection="caseIds" open="(" close=")" separator="," item="caseId">
                #{caseId}
            </foreach>
            and `deal_result` in
            <foreach collection="statusList" open="(" separator="," close=")" item="status">
                #{status}
            </foreach>
            and is_delete = 0
        </where>
    </select>

    <select id="getAdminWorkOrderReportByCount" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderReport">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        report
        left join admin_work_order workOrder
        on report.work_order_id = workOrder.id
        where report.is_delete = 0
        and workOrder.order_type = #{orderType}
        and workOrder.order_task = #{orderTask}
        and workOrder.operator_id = 0
        and workOrder.order_status = 0
        order by report.create_time asc
        limit #{count}
    </select>

    <select id="getAdminWorkOrderReportList" resultType="com.shuidihuzhu.cf.vo.crowdfunding.AdminWorkOrderReportVo">
        select
        report.id,report.case_id, report.work_order_id,
        workOrder.operator_id,info.amount,cr.is_newreport,report.case_risk,report.deal_result,state.pause_state,
        IFNULL(trust.audit_status,-1) as addTrustAuditStatus,workOrder.update_time as lastOperationTime,report.report_id,
        report.appoint_time as appointTime
        from
        <include refid="tableName"/>
        report
        left join crowdfunding_report cr
        on cr.id = report.report_id
        left join cf_state state
        on state.info_id = report.case_id
        LEFT JOIN admin_work_order workOrder
        on report.work_order_id = workOrder.id
        left JOIN crowdfunding_info info
        on info.id = report.case_id
        <if test="refundStatus!=null">
            LEFT JOIN cf_refund AS cre
            ON cre.info_uuid=info.info_id
        </if>
        left JOIN cf_report_add_trust trust
        on trust.info_uuid = info.info_id
        <if test="lostContact != 0">
            left join admin_cf_lost_contact
            on admin_cf_lost_contact.info_uuid = info.info_id and valid = 1
        </if>
        left join cf_draw_cash cdc
        on cdc.info_uuid=info.info_id

        <if test="reprotType!=null">
            LEFT JOIN crowdfunding_report_label AS rl
            ON cr.id=rl.report_id
        </if>

        where report.is_delete=0
        and workOrder.operator_id = #{userId}
        and report.deal_result in (0,1)
        <if test="addTrustStatus != null">
            and trust.audit_status = #{addTrustStatus}
        </if>
        <if test="followStatus != null">
            and report.deal_result = #{followStatus}
        </if>
        <if test="approveStatus!=null">
            AND info.status=#{approveStatus}
        </if>
        <if test="drawStatus==null and drawApplyStatus==0">
            AND cdc.apply_status in (1,3)
        </if>
        <if test="drawStatus==-1 and drawApplyStatus==2">
            AND cdc.apply_status = #{drawApplyStatus}
            AND cdc.draw_status=#{drawStatus}
        </if>
        <if test="drawStatus==3 and drawApplyStatus==2">
            AND cdc.apply_status = #{drawApplyStatus}
            AND cdc.draw_status IN (0,3)
        </if>
        <if test="drawStatus==2 and drawApplyStatus==2">
            AND cdc.apply_status = #{drawApplyStatus}
            AND cdc.draw_status IN (1,2,4,5)
        </if>
        <if test="refundStatus!=null">
            AND cre.apply_status > #{refundStatus}
        </if>
        <if test="infoId != null">
            AND report.case_id = #{infoId}
        </if>
        <if test="lostContact != 0">
            and ifnull(admin_cf_lost_contact.lost, 2) = #{lostContact}
        </if>
        <if test="reprotType!=null">
            and rl.report_label=#{reprotType}
        </if>
        <if test="appointStartTime != null">
            and report.appoint_time &gt;= #{appointStartTime}
        </if>
        <if test="appointEndTime != null">
            and report.appoint_time &lt;= #{appointEndTime}
        </if>
        <if test="realName != null and realName != 0">
            AND cr.real_name_report = #{realName}
        </if>
        and ifnull(trust.is_delete, 0) = 0
        order by report.case_risk desc, report.id
    </select>

    <select id="getAdminWorkOrderReportById" parameterType="java.util.List"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderReport">
        select
        <include refid="Fields"/>
        from
        <include refid="tableName"/>
        where is_delete = 0
        and id in
        <foreach collection="ids" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </select>

    <select id="getAdminWorkOrderReportCount" parameterType="int" resultType="int">
        select
        count(report.id)
        from
        <include refid="tableName"/>
        report
        join admin_work_order workOrder
        on report.work_order_id = workOrder.id
        where report.is_delete = 0
        and workOrder.order_type = #{orderType}
        and workOrder.order_task = #{orderTask}
        and workOrder.operator_id = 0
        and workOrder.order_status = 0
        <if test="startTime != null and startTime !=''">
            and workOrder.update_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null and endTime !=''">
            and workOrder.update_time <![CDATA[ < ]]> #{endTime}
        </if>
    </select>

    <update id="updateCaseRisk" parameterType="int">
        update
        <include refid="tableName"/>
        set case_risk = #{caseRisk}
        where id = #{id}
    </update>

    <select id="selectAdminWorkOrderReport" resultType="com.shuidihuzhu.cf.vo.crowdfunding.AdminWorkOrderReportVo">
        select
        report.id,report.case_id, report.work_order_id,
        workOrder.operator_id,info.amount,cr.is_newreport,report.case_risk,report.deal_result,state.pause_state,
        IFNULL(trust.audit_status,-1) as addTrustAuditStatus,workOrder.update_time as lastOperationTime,report.report_id
        from
        <include refid="tableName"/>
        report
        left join crowdfunding_report cr
        on cr.id = report.report_id
        left join cf_state state
        on state.info_id = report.case_id
        LEFT JOIN admin_work_order workOrder
        on report.work_order_id = workOrder.id
        left JOIN crowdfunding_info info
        on info.id = report.case_id
        left JOIN cf_report_add_trust trust
        on trust.info_uuid = info.info_id
        <if test="lostContact != 0">
            left join admin_cf_lost_contact
            on admin_cf_lost_contact.info_uuid = info.info_id and valid = 1
        </if>
        where report.is_delete=0
        <if test="addTrustStatus != null">
            and trust.audit_status = #{addTrustStatus}
        </if>
        <if test="followStatus != null">
            and report.deal_result = #{followStatus}
        </if>
        <if test="caseRisk != null">
            and report.case_risk = #{caseRisk}
        </if>
        <if test="startTime != null and startTime !=''">
            and report.create_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null and startTime !=''">
            and report.create_time <![CDATA[ < ]]> #{endTime}
        </if>
        <if test="isDrawCash == 0">
            AND state.pause_state = 1
        </if>
        <if test="isDrawCash == 1">
            AND state.pause_state = 0
        </if>

        <if test="lostContact != 0">
            and ifnull(admin_cf_lost_contact.lost, 2) = #{lostContact}
        </if>

        <if test="operatorIds != null">
            AND workOrder.operator_id IN
            <foreach collection="operatorIds" item="operatorId" separator="," close=")" open="(">
                #{operatorId}
            </foreach>
        </if>

        <if test="realName != null and realName != 0">
            AND cr.real_name_report = #{realName}
        </if>
        order by report.case_risk desc, report.id
    </select>

    <update id="updateDealResultById" parameterType="int">
        update
        <include refid="tableName"/>
        set deal_result = #{dealResult}
        where is_delete = 0
        and id = #{id}
    </update>

    <select id="getCount" resultType="int">
        select
        count(report.id)
        from
        <include refid="tableName"/>
        report
        join admin_work_order workOrder
        on report.work_order_id = workOrder.id
        where report.is_delete = 0
        and workOrder.order_type = #{orderType}
        and workOrder.order_task = #{orderTask}
        and workOrder.operator_id > 0
        and workOrder.order_status in (1,2,3)
        <if test="startTime != null and startTime !=''">
            and workOrder.update_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null and endTime !=''">
            and workOrder.update_time <![CDATA[ < ]]> #{endTime}
        </if>
    </select>

    <select id="getDealCount" resultType="int">
        select
        count(report.id)
        from
        <include refid="tableName"/>
        report
        join admin_work_order workOrder
        on report.work_order_id = workOrder.id
        where report.is_delete = 0
        and workOrder.order_type = #{orderType}
        and workOrder.order_task = #{orderTask}
        and report.deal_result = #{dealResult}
        <if test="startTime != null and startTime !=''">
            and report.update_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null and endTime !=''">
            and report.update_time <![CDATA[ < ]]> #{endTime}
        </if>
    </select>

    <select id="getOldFollowCount" resultType="int">
        select
        count(report.id)
        from
        <include refid="tableName"/>
        report
        join admin_work_order workOrder
        on report.work_order_id = workOrder.id
        where report.is_delete = 0
        and workOrder.order_type = #{orderType}
        and workOrder.order_task = #{orderTask}
        and report.deal_result = #{dealResult}
        <if test="startTime != null and startTime !=''">
            and report.update_time <![CDATA[ < ]]> #{startTime}
        </if>
    </select>

    <select id="getNoNeedDealCountByUserIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderDataVo">
        select workOrder.operator_id,
        count(report.id) as count
        from
        <include refid="tableName"/>
        report
        join admin_work_order workOrder
        on report.work_order_id = workOrder.id
        where report.is_delete = 0
        and workOrder.order_type = #{orderType}
        and workOrder.order_task = #{orderTask}
        and report.deal_result = #{dealResult}
        and workOrder.operator_id in
        <foreach collection="userIds" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
        <if test="startTime != null and startTime !=''">
            and report.update_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null and endTime !=''">
            and report.update_time <![CDATA[ < ]]> #{endTime}
        </if>
        group by workOrder.operator_id
    </select>

    <select id="getOldFollowCountByUserIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderDataVo">
        select workOrder.operator_id,
        count(report.id) as count
        from
        <include refid="tableName"/>
        report
        join admin_work_order workOrder
        on report.work_order_id = workOrder.id
        where report.is_delete = 0
        and workOrder.order_type = #{orderType}
        and workOrder.order_task = #{orderTask}
        and report.deal_result = #{dealResult}
        and workOrder.operator_id in
        <foreach collection="userIds" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
        <if test="startTime != null and startTime !=''">
            and report.update_time <![CDATA[ < ]]> #{startTime}
        </if>
        group by workOrder.operator_id
    </select>

    <select id="selectWorkOrderReportByCaseIds" parameterType="java.util.List"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderReport">
        select * from
        <include refid="tableName"/>
        where is_delete = 0
        and case_id in
        <foreach collection="caseIds" open="(" close=")" separator="," item="caseId">
            #{caseId}
        </foreach>
    </select>

    <select id="getWorkOrderOperatorIdByCaseIds" parameterType="java.util.List"
            resultType="com.shuidihuzhu.cf.vo.crowdfunding.AdminWorkOrderReportVo">
        select report.case_id,workOrder.operator_id
        from
        <include refid="tableName"/>
        report
        left join admin_work_order workOrder
        on report.work_order_id = workOrder.id
        where report.is_delete = 0
        and report.case_id in
        <foreach collection="caseIds" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </select>

    <update id="updateFollowTypeById" parameterType="int">
        update
        <include refid="tableName"/>
        set follow_type = #{followType}
        where is_delete = 0
        and id = #{id}
    </update>

    <select id="getCountByUserIds" parameterType="java.util.List"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderDataVo">
        select workOrder.operator_id,
        count(report.id) as count
        from
        <include refid="tableName"/>
        report
        join admin_work_order workOrder
        on report.work_order_id = workOrder.id
        where report.is_delete = 0
        and workOrder.order_type = #{orderType}
        and workOrder.order_task = #{orderTask}
        and workOrder.operator_id in
        <foreach collection="userIds" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
        <if test="startTime != null and startTime !=''">
            and workOrder.update_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null and endTime !=''">
            and workOrder.update_time <![CDATA[ < ]]> #{endTime}
        </if>
        group by workOrder.operator_id
    </select>

    <select id="selectWorkOrderReportById" parameterType="int"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderReport">
        select
        <include refid="Fields"/>
        from
        <include refid="tableName"/>
        where is_delete = 0
        and id = #{id}
    </select>

    <select id="getAdminReportDataVo" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminReportDataVo">
        select a.id workId,
        i.`id` caseId,
        i.info_id caseUuid,
        i.`title` title,
        r.id reportId,
        r.handle_time handleTime,
        a.handle_result status,
        a.operator_id operatorId,
        a.update_time dealime,
        a.create_time createTime
        from admin_work_order a
        left join admin_work_order_report r on a.id=r.`work_order_id`
        left join `crowdfunding_info` i on r.`case_id`=i.`id`
        where a.`order_type`=5 and a.`order_task`=12 and a.is_delete = 0
        <if test="workId != null">
            and a.id = #{workId}
        </if>
        <if test="status != null">
            and a.handle_result = #{status}
        </if>
        <if test="operator != null">
            and a.operator_id = #{operator}
        </if>
        <if test="caseId != null">
            and r.case_id = #{caseId}
        </if>
        <if test="startDealTime != null and endDealTime !=null">
            and a.update_time between #{startDealTime} and #{endDealTime}
        </if>
        <if test="startHandleTime != null and endHandleTime !=null">
            and r.handle_time between #{startHandleTime} and #{endHandleTime}
        </if>
        <if test="startCreateTime != null and endCreateTime !=null">
            and a.`create_time` between #{startCreateTime} and #{endCreateTime}
        </if>

        order by a.`create_time` desc
    </select>


    <select id="getAdminReportCount" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminReportDataVo">
       select work_order_id workId ,count(work_order_id) reportCount
       from work_report_map
       where work_order_id in
        <foreach collection="list" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
       group by work_order_id
    </select>


    <insert id="addAdminWorkReportMap" parameterType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkReportMap">

        INSERT INTO
        work_report_map
        (case_id,report_id,work_order_id,pic,content)
        VALUES
        (#{caseId},#{reportId},#{workOrderId},#{pic},#{content})

    </insert>


    <select id="getLastAdminWorkReportMap" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkReportMap">
        select work_order_id workOrderId,report_id reportId
        from work_report_map
        where work_order_id = #{workOrderId}
        order by create_time desc
    </select>

    <select id="getWorkReportMapList" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkReportMap">
        select work_order_id workOrderId,report_id reportId,pic,content,create_time createTime
        from work_report_map
        where work_order_id in
        <foreach collection="list" open="(" close=")" separator="," item="id">
        #{id}
        </foreach>
        order by create_time desc
    </select>




    <select id="getNewNum" resultType="int">
        select count(1) from
        admin_work_order_report r
        left join admin_work_order a on r.work_order_id=a.id
        where r.create_time between #{yesterday} and #{today}
        and a.operator_id >0 and r.is_delete=0
    </select>


    <select id="getNewActivityNum" resultType="int">
          select count(1)
          from admin_work_order_report
          where create_time between #{yesterday} and #{today}
          and deal_result in (1,2)  and is_delete=0
    </select>

    <select id="getDoingNum" resultType="int">
         select count(1)
         from admin_work_order
         where order_type=5  and order_task=12 and handle_result=1 and is_delete=0 and create_time>='2018-07-01 00:00:00'
    </select>

    <select id="getAllActivityNum" resultType="int">
          select count(1)
          from admin_work_order_report
          where update_time between #{yesterday} and #{today}
          and deal_result=2 and is_delete=0
    </select>


    <select id="getFinishNum" resultType="int">
          select count(1)
          from admin_work_order
          where order_type=5  and order_task=12 and update_time between #{yesterday} and #{today} and operator_id >0 and handle_result in (2,5) and is_delete=0
    </select>


    <select id="getFinishNewNum" resultType="int">
        select count(1)
        from admin_work_order_report r
        left join admin_work_order a on r.work_order_id=a.id
        where r.create_time between #{yesterday} and #{today} and a.operator_id >0 and a.handle_result in (2,5)
    </select>


    <select id="getLostNum" resultType="int">
        select count(1)
        from admin_work_order_report r
        left join crowdfunding_info i on r.case_id = i.id
        left join admin_cf_lost_contact a on i.info_id = a.info_uuid
        where r.create_time between #{yesterday} and #{today} and a.valid=1 and r.is_delete=0
    </select>


    <select id="getRisktNum" resultType="int">
      select count(1)
      from admin_work_order_report
      where create_time between #{yesterday} and #{today} and case_risk=1 and is_delete=0
    </select>


    <select id="getNewNumByUser" resultType="com.shuidihuzhu.cf.model.crowdfunding.ReportWorkStat">
        select a.operator_id operatorId,count(1) newNum from
        admin_work_order_report r
        left join admin_work_order a on r.work_order_id=a.id
        where r.create_time between #{yesterday} and #{today}
        and r.is_delete=0 and a.operator_id in

        <foreach collection="list" open="(" close=")" separator="," item="userId">
              #{userId}
        </foreach>

        group by a.operator_id
    </select>

    <select id="getNewActivityNumByUser" resultType="com.shuidihuzhu.cf.model.crowdfunding.ReportWorkStat">

        select a.operator_id operatorId,count(1) newActivityNum
        from admin_work_order_report r
        left join admin_work_order a on r.work_order_id=a.id
        where r.create_time between #{yesterday} and #{today}
        and r.is_delete=0 and a.operator_id in
        <foreach collection="list" open="(" close=")" separator="," item="userId">
            #{userId}
        </foreach>
        and r.deal_result in (1,2)  and r.is_delete=0
        group by a.operator_id
    </select>


    <select id="getAllActivityNumByUser" resultType="com.shuidihuzhu.cf.model.crowdfunding.ReportWorkStat">

        select a.operator_id operatorId,count(1) allActivityNum
        from admin_work_order_report r
        left join admin_work_order a on r.work_order_id=a.id
        where r.update_time between #{yesterday} and #{today}
        and r.is_delete=0 and a.operator_id in
        <foreach collection="list" open="(" close=")" separator="," item="userId">
            #{userId}
        </foreach>
        and r.deal_result = 2 and r.is_delete=0
        group by a.operator_id
    </select>

    <select id="getDoingNumByUser" resultType="com.shuidihuzhu.cf.model.crowdfunding.ReportWorkStat">
        select operator_id operatorId,count(1) allActivityNum
        from admin_work_order
        where order_type=5  and order_task=12 and handle_result=1 and is_delete=0
        and create_time>='2018-07-01 00:00:00'
        and operator_id in
        <foreach collection="list" open="(" close=")" separator="," item="userId">
            #{userId}
        </foreach>
        group by operator_id
    </select>

    <select id="getFinishNumByUser" resultType="com.shuidihuzhu.cf.model.crowdfunding.ReportWorkStat">
          select operator_id operatorId,count(1) finishNum
          from admin_work_order
          where order_type=5  and order_task=12 and operator_id >0 and handle_result in (2,5) and is_delete=0
          and update_time between #{yesterday} and #{today}
          and operator_id in
          <foreach collection="list" open="(" close=")" separator="," item="userId">
              #{userId}
          </foreach>
          group by operator_id
    </select>

    <select id="getFinishNewNumByUser" resultType="com.shuidihuzhu.cf.model.crowdfunding.ReportWorkStat">
         select operator_id operatorId,count(1) finishNewNum
        from admin_work_order_report r
        left join admin_work_order a on r.work_order_id=a.id
        where r.create_time between #{yesterday} and #{today} and a.operator_id >0 and a.handle_result in (2,5)
        and a.operator_id in
        <foreach collection="list" open="(" close=")" separator="," item="userId">
            #{userId}
        </foreach>
        group by a.operator_id
    </select>


    <select id="getLostNumByUser" resultType="com.shuidihuzhu.cf.model.crowdfunding.ReportWorkStat">
        select operator_id operatorId,count(1) lostNum
        from admin_work_order_report r
        left join admin_work_order a on r.work_order_id=a.id
        left join crowdfunding_info i on r.case_id = i.id
        left join admin_cf_lost_contact l on i.info_id = l.info_uuid
        where r.create_time between #{yesterday} and #{today} and l.valid=1 and r.is_delete=0
        and a.operator_id in
        <foreach collection="list" open="(" close=")" separator="," item="userId">
            #{userId}
        </foreach>
        group by a.operator_id
    </select>


    <select id="getRisktNumByUser" resultType="com.shuidihuzhu.cf.model.crowdfunding.ReportWorkStat">
        select operator_id operatorId,count(1) risktNum
      from admin_work_order_report r
      left join admin_work_order a on r.work_order_id=a.id
      where r.create_time between #{yesterday} and #{today} and r.case_risk=1 and r.is_delete=0
      and a.operator_id in
        <foreach collection="list" open="(" close=")" separator="," item="userId">
            #{userId}
        </foreach>
      group by a.operator_id
    </select>


    <update id="updateHandkeTime">
        update
        <include refid="tableName"/>
        set handle_time = now()
        where work_order_id in
        <foreach collection="list" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </update>



    <select id="getReportMapByReportIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkReportMap">
        select work_order_id workOrderId,report_id reportId,case_id caseId
        from work_report_map
        where report_id in
        <foreach collection="list" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </select>


    <select id="getAdminWorkReportMapByReportId" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkReportMap">
        select work_order_id workOrderId,report_id reportId, case_id caseId
        from work_report_map
        where report_id = #{reportId}
        limit 1;
    </select>

    <select id="findByCaseIdAndDealResult"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderReport">
        select id,work_order_id,type,case_id,report_id,case_risk,follow_type,deal_result,create_time
        from
        <include refid="tableName"/>
        where case_id = #{caseId}
        and deal_result in
        <foreach collection="dealResults" open="(" close=")" separator="," item="dealResult">
            #{dealResult}
        </foreach>
        and is_delete = 0
    </select>

    <select id="findByCaseIdAndReportId"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderReport">
        select id,work_order_id,type,case_id,report_id,case_risk,follow_type,deal_result
        from
        <include refid="tableName"/>
        where case_id = #{caseId}
        and report_id = #{reportId}
        and is_delete = 0
    </select>


</mapper>