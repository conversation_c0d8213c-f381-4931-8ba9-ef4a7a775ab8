<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfDiseaseManagerRecordDao">

    <sql id="table_name">
        `cf_disease_manager_record`
    </sql>

    <sql id="insert_fields">
        `disease_manager_id`,
        `operator_id`,
        `operator_name`,
        `operate_type`
    </sql>

    <sql id="select_fields">
        `id` as id,
        `disease_manager_id` as diseaseManagerId,
        `operator_id` as operatorId,
        `operator_name`as operatorName,
        `operate_type` as operateType,
        `create_time` as createTime,
        `update_time` as updateTime,
        `is_delete`
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfDiseaseManagerRecordDO">
        INSERT <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES (#{diseaseManagerId}, #{operatorId}, #{operatorName}, #{operateType})
    </insert>


    <select id="listByManagerId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfDiseaseManagerRecordDO">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        <where>
            `disease_manager_id` = #{diseaseManagerId}
        </where>
    </select>
</mapper>