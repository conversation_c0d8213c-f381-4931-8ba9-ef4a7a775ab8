<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingReportDao">

    <sql id="tableName">
		crowdfunding_report
	</sql>

    <sql id="fields">
		`id`, `activity_id`,`user_id`,`image_urls`,`content`,`encrypt_contact`,`create_time`,`deal_status`,`is_newreport`,`case_follow_status`,`real_name_report`,`name`,`identity`
	</sql>
    <sql id="fields2">
        cr.id as cr_id,
        cr.activity_id as cr_activity_id,
        cr.user_id as cr_user_id,
        cr.image_urls as cr_image_urls,
        cr.content as cr_content,
        cr.encryptContact as cr_encrypt_contact,
        cr.create_time as cr_create_time,
        cr.deal_status as cr_deal_status,
        cr.operator_id as cr_operator_id,
        cr.is_newreport as cr_is_newreport,
        cr.case_follow_status as cr_case_follow_status,
    </sql>

    <resultMap id="reportResultMap" type="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport">
        <id property="id" column="cr_id" />
        <result property="activityId" column="cr_activity_id"/>
        <result property="userId" column="cr_user_id"/>
        <result property="imageUrls" column="cr_image_urls"/>
        <result property="content" column="cr_content"/>
        <result property="encryptContact" column="cr_encrypt_contact"/>
        <result property="createTime" column="cr_create_time"/>
        <result property="dealStatus" column="cr_deal_status"/>
        <result property="operatorId" column="cr_operator_id"/>
    </resultMap>

    <sql id="insertFields">
        `activity_id`,`user_id`,`image_urls`,`content`,`encrypt_contact`,`is_newreport`,`deal_status`,`case_follow_status`,`report_source`
    </sql>

    <sql id="BaseInsertFields">
		`activity_id`,`user_id`,`image_urls`,`content`,`encrypt_contact`,`is_newreport`,`real_name_report`,`name`,`identity`,`handle_status`,`connect_status`,`hit_black_list`,`report_channel`,`report_channel_other`
	</sql>

    <insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport"
            useGeneratedKeys="true"
            keyProperty="id"
    >
        INSERT INTO
        <include refid="tableName"/>
        (<include refid="insertFields"/>)
        VALUES
        (#{activityId},#{userId},#{imageUrls},#{content},#{encryptContact},#{isNewreport},#{dealStatus},#{caseFollowStatus},#{reportSource});
    </insert>

    <insert id="addRealNameReport" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tableName"/>
        (<include refid="BaseInsertFields"/>)
        VALUES
        (#{activityId},#{userId},#{imageUrls},#{content},#{encryptContact},#{isNewreport},#{realNameReport},#{name},#{identity},#{handleStatus},#{connectStatus},#{hitBlackList},#{reportChannel},#{reportChannelOther});
    </insert>

    <select id="getReports" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE create_time <![CDATA[ > ]]> DATE_SUB(NOW(),INTERVAL #{timer} MINUTE)
        ORDER BY create_time
        limit #{start},#{size};
    </select>

    <select id="query" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE id = #{reportId} and activity_id = #{caseId}
    </select>

    <select id="getByInfoId" parameterType="int" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE activity_id = #{activityId}
        limit 1;
    </select>

    <select id="getListByInfoId" parameterType="int" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE activity_id = #{activityId} order by id desc;
    </select>

    <select id="getListByInfoIds" parameterType="int" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE activity_id in <foreach collection="activityIds" open="(" separator="," close=")" item="activityId">#{activityId}</foreach>
        GROUP BY activity_id
    </select>

    <select id="getListByInfoIdsV2" parameterType="int" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE activity_id in <foreach collection="activityIds" open="(" separator="," close=")" item="activityId">#{activityId}</foreach>
    </select>

    <update id="updateDealStatus">
        UPDATE <include refid="tableName"/>
        SET `deal_status`=#{dealStatus},
        `operator_id`=#{operatorId}
        WHERE `id`=#{id}
    </update>

    <update id="updateHandleAndConnectStatus">
        UPDATE <include refid="tableName"/>
        SET `handle_status`=#{handleStatus},`connect_status`=#{connectStatus},`new_operator_id`=#{newOperatorId},`deal_status` = #{dealStatus}
        WHERE `id`=#{id}
    </update>

    <select id="getListByPage" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport">
        SELECT *
        FROM crowdfunding_report as cr
        LEFT JOIN crowdfunding_info as info
        on cr.activity_id=info.id
        WHERE cr.case_follow_status=0
        <if test="caseType!=null">
            AND info.type = #{caseType}
        </if>
        <if test="reportDealStatus!=null">
            AND cr.deal_status = #{reportDealStatus}
        </if>
        <if test="activityIds!=null">
            AND cr.activity_id in
            <foreach collection="activityIds" open="(" separator="," close=")" item="activityId">
                #{activityId}
            </foreach>
        </if>
        ORDER BY cr.create_time DESC
    </select>



    <select id="getListByWhereAndPage" resultMap="reportResultMap" resultType="java.util.List">
        SELECT <include refid="fields2"/>
        FROM crowdfunding_report as cr
        LEFT JOIN crowdfunding_info as info
        ON cr.activity_id=info.id
        <if test="cfRefundStatus!=null">
            LEFT JOIN cf_refund AS cre
            ON cre.info_uuid=info.info_id
        </if>
        WHERE cr.case_follow_status=0
        <if test="reportDealStatus!=null">
            AND cr.deal_status=#{reportDealStatus}
        </if>
        <if test="activityIds!=null">
            AND cr.activity_id in <foreach collection="activityIds" open="(" separator="," close=")" item="activityId">#{activityId}</foreach>
        </if>
        <if test="caseType!=null">
            AND info.type = #{caseType}
        </if>
        <if test="approveStatus!=null">
            AND info.status=#{approveStatus}
        </if>
        <if test="approveStatus==null">
            AND info.status!=2
        </if>
        <if test="cfRefundStatus!=null">
            AND cre.apply_status > #{cfRefundStatus}
        </if>
        ORDER BY cr.create_time DESC
    </select>

    <select id="getListByWhereDrawCashAndPage" resultMap="reportResultMap" resultType="java.util.List">
        SELECT <include refid="fields2"/>
        FROM crowdfunding_report as cr
        LEFT JOIN crowdfunding_info as info
        ON cr.activity_id=info.id
        LEFT JOIN cf_draw_cash AS cdc
        ON cdc.info_uuid=info.info_id
        WHERE cr.case_follow_status=0
        AND info.status=#{approveStatus}
        <if test="caseType!=null">
            AND info.type = #{caseType}
        </if>
        <if test="reportDealStatus!=null">
            AND cr.deal_status=#{reportDealStatus}
        </if>
        <if test="activityIds!=null">
            AND cr.activity_id in <foreach collection="activityIds" open="(" separator="," close=")" item="activityId">#{activityId}</foreach>
        </if>
        <if test="DrawStatus==null and cfCashStatus==0">
            AND cdc.apply_status in (1,3)
        </if>
        <if test="DrawStatus==-1 and cfCashStatus==2">
            AND cdc.apply_status = #{cfCashStatus}
            AND cdc.draw_status=#{DrawStatus}
        </if>
        <if test="DrawStatus==3 and cfCashStatus==2">
            AND cdc.apply_status = #{cfCashStatus}
            AND cdc.draw_status IN (0,3)
        </if>
        <if test="DrawStatus==2 and cfCashStatus==2">
            AND cdc.apply_status = #{cfCashStatus}
            AND cdc.draw_status IN (1,2,4,5)
        </if>
        ORDER BY cr.create_time DESC
    </select>

    <select id="selectCaseCount" resultType="java.util.Map">
        SELECT `activity_id` AS id, count(*) AS counts
        FROM <include refid="tableName"/>
        WHERE `activity_id` IN
        <foreach collection="set" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY `activity_id`
        HAVING counts IN
        <foreach collection="countList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByActivityIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE activity_id IN <foreach collection="list" open="(" separator="," close=")" item="activityId">#{activityId}</foreach>
    </select>

    <update id="updateReportStatusList" >
        UPDATE  <include refid="tableName"/>
        SET  `case_follow_status`=1
        where id IN
        <foreach collection="reportIds" item="reportId" separator="," open="(" close=")">
            #{reportId}
        </foreach>
    </update>

    <update id="updateReportListFollowStatus" >
        UPDATE  <include refid="tableName"/>
        SET  `case_follow_status`=#{followStatus}
        where id IN
        <foreach collection="reportIds" item="reportId" separator="," open="(" close=")">
            #{reportId}
        </foreach>
    </update>

    <update id="updateReportListDealStatus" >
        UPDATE  <include refid="tableName"/>
        SET  `deal_status`=#{dealStatus},
        `operator_id`=#{operatorId}
        where id IN
        <foreach collection="reportIds" item="reportId" separator="," open="(" close=")">
            #{reportId}
        </foreach>
    </update>

    <update id="updateReportListOperator" >
        UPDATE  <include refid="tableName"/>
        SET  `operator_id`=#{targetUserId}
        where id IN
        <foreach collection="reportIds" item="reportId" separator="," open="(" close=")">
            #{reportId}
        </foreach>
    </update>

    <update id="updateReportIsNewStatusList" >
        UPDATE  <include refid="tableName"/>
        SET  `is_newreport`=0
        where id IN
        <foreach collection="reportIds" item="reportId" separator="," open="(" close=")">
            #{reportId}
        </foreach>
    </update>


    <select id="getFirstListByCreateTimeAndInfoid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        where `case_follow_status` = 1
        <if test="infoids!=null">
        AND `activity_id` IN
        <foreach collection="infoids" item="infoid" separator="," open="(" close=")">
            #{infoid}
        </foreach>
        </if>
        GROUP BY `activity_id`
        ORDER BY `create_time` DESC
    </select>

    <select id="getLastListByCreateTimeAndInfoid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport">
        SELECT *
        FROM (select * from <include refid="tableName"/>
        where case_follow_status = 1
        <if test="infoids != null">
            AND `activity_id` IN
            <foreach collection="infoids" item="infoid" separator="," open="(" close=")">
                #{infoid}
            </foreach>
        </if>
        order by create_time desc) as cr
        group by cr.activity_id
        order by cr.create_time desc
    </select>

    <select id="getIsHaveNewReport" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport">
        select `activity_id`,`is_newreport`
        FROM <include refid="tableName"/>
        WHERE `case_follow_status` = 1
        AND `is_newreport` = 1
        group BY `activity_id`
    </select>

    <select id="getListByInfoIdAndPage" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport">
        select
        <include refid="fields"/>
        FROM
        <include refid="tableName"/>
        WHERE `activity_id` = #{infoId}
        ORDER BY `create_time` DESC
    </select>

    <select id="getListByReportIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport">
        SELECT   <include refid="fields"/>
        FROM  <include refid="tableName"/>
        where id IN
        <foreach collection="reportIds" item="reportId" separator="," open="(" close=")">
            #{reportId}
        </foreach>
    </select>

    <select id="getCaseReportCount" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminCrowdfundingReportChild">
        select activity_id,count(id) as count
        from <include refid="tableName"/>
        where activity_id in
        <foreach collection="caseIds" open="(" close=")" separator="," item="caseId">
            #{caseId}
        </foreach>
        group by activity_id
    </select>

    <select id="getFirstCreateTimeByInfoIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        where `activity_id` IN
        <foreach collection="infoids" item="infoid" separator="," open="(" close=")">
             #{infoid}
         </foreach>
        GROUP BY `activity_id`
    </select>

    <select id="getLastCreateTimeByInfoIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport">
        SELECT a.*
        FROM <include refid="tableName"/> as a,
        (
        select max(id) as id from <include refid="tableName"/>
        where  `activity_id` IN
        <foreach collection="infoids" item="infoid" separator="," open="(" close=")">
                #{infoid}
        </foreach>
        group by activity_id
        ) as b
        where a.id = b.id
    </select>


    <select id="getReportByTimes" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where create_time between #{start} and #{end}
    </select>


    <select id="getReportLabels" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReportLabel">
        select report_id reportId,report_label reportLabel, report_comment reportComment
        from crowdfunding_report_label
        where  report_id in
        <foreach collection="list" item="id" separator="," open="(" close=")">
          #{id}
        </foreach>
        and is_delete = 0
    </select>

    <update id = "deleteReportLabels">
        update crowdfunding_report_label
        set is_delete = 1
        where report_id = #{reportId}
    </update>

    <insert id="addReportLabelsForAdmin" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReportLabel"
            useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO
        crowdfunding_report_label
        (report_id, report_label, report_comment)
        VALUES
        <foreach collection="list" item="item" separator="," >
            (#{item.reportId}, #{item.reportLabel}, #{item.reportComment})
        </foreach>
    </insert>

    <insert id="addLabel" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReportLabel"
            useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO
        crowdfunding_report_label
        (report_id,report_label)
        VALUES
        <foreach collection="list" item="item" separator="," >
            (#{item.reportId},#{item.reportLabel})
        </foreach>

    </insert>

    <select id="selectReportByCaseIdAndCreateTime" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE  activity_id = #{caseId}
        AND create_time >= #{createTime}
        AND deal_status = #{dealStatus}
    </select>

    <select id="listByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where user_id = #{userId}
    </select>

    <select id="countByUserId" resultType="java.lang.Integer">
        select count(*)
        from <include refid="tableName"/>
        where user_id = #{userId}
    </select>

    <select id="getListByInfoIdAndName" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE  activity_id = #{caseId}
        AND `name` = #{userName}
        limit 10
    </select>

    <update id="updateRiskLabel">
        update <include refid="tableName"/>
        set risk_label = #{riskLabel}
        where id = #{reportId}
    </update>

    <select id="countByInfoId" resultType="java.lang.Integer">
        SELECT count(1)
        FROM
        <include refid="tableName"/>
        WHERE activity_id = #{caseId};
    </select>

    <update id = "deleteReportLabelsByIdAndType">
        update crowdfunding_report_label_modify
        set is_delete = 1
        where report_id = #{reportId} and is_delete = 0
    </update>

    <update id="updateEncryptMobileById">
        update <include refid="tableName"/>
        set encrypt_contact = #{encryptMobile}
        where id = #{id}
    </update>

    <insert id="addModifyLabel" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReportLabel" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        crowdfunding_report_label_modify
        (report_id,report_label,report_comment)
        VALUES
        <foreach collection="list" item="item" separator="," >
            (#{item.reportId},#{item.reportLabel},#{item.reportComment})
        </foreach>

    </insert>

    <select id="getReportLabelsModify" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReportLabel">
        select report_id,report_label,report_comment
        from crowdfunding_report_label_modify
        where  report_id in
        <foreach collection="list" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        and is_delete = 0
    </select>
    <select id="getById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport">
        select * from <include refid="tableName"/>
        where id = #{id}
    </select>
    <select id="queryReportLabelByReportId"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReportLabel">
        SELECT *
        FROM crowdfunding_report_label
        WHERE report_id = #{reportId}
    </select>
</mapper>
