<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfReportAddTrustSnapshotDao">

    <sql id="tableName">
    cf_report_add_trust_snapshot
  </sql>

    <sql id="insertFields">
    add_trust_id,
    audit_status,
    add_trust_snapshot
  </sql>

    <sql id="selectFields">
    id,
    add_trust_id,
    audit_status,
    add_trust_snapshot
  </sql>

    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.report.CfReportAddTrustSnapshot" keyProperty="id"
            useGeneratedKeys="true">
        insert into
        <include refid="tableName"/>
        (<include refid="insertFields"/>)
        values (#{addTrustId},#{auditStatus},#{addTrustSnapshot})
    </insert>

    <select id="findByAddTrustIdAndAuditStatus"
            resultType="com.shuidihuzhu.cf.model.report.CfReportAddTrustSnapshot">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        where add_trust_id = #{addTrustId}
        and audit_status = #{auditStatus}
        and is_delete = 0
        order by id desc
    </select>

</mapper>