<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfRepeatUserIdRecordDao">
    <sql id="table_name">
        `cf_repeat_user_id_record`
    </sql>

    <sql id="insert_fields">
        `user_id`,
        `remain_counts`
    </sql>

    <sql id="select_fields">
        `id`,
        `user_id`,
        `remain_counts`
    </sql>

    <insert id="insertList" parameterType="java.util.List">
        INSERT ignore INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.userId},#{item.remainCounts})
        </foreach>
    </insert>

    <select id="selectUserId" resultType="int">
        SELECT `user_id`
        FROM <include refid="table_name"/>
        WHERE `remain_counts` <![CDATA[ > ]]> 0
    </select>

    <select id="selectByRemainCounts" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfRepeatUserIdRecord">
        SELECT * FROM <include refid="table_name"/>
        WHERE `remain_counts` <![CDATA[ > ]]> 0
    </select>

    <update id="updateRemainCount">
        UPDATE <include refid="table_name"/>
        SET `remain_counts`=#{remainCounts}
        WHERE `user_id`=#{userId}
    </update>

    <select id="selectAll" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfRepeatUserIdRecord">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        LIMIT #{start},#{limit}
    </select>
</mapper>