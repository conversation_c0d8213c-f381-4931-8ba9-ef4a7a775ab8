<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCfSceneDao">

    <sql id="table_name">
        cf_scene
    </sql>

    <sql id="fields">
        `id` as id,
        `name` as name,
        `description` as description
    </sql>


    <select id="listScenes" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfScene">
        SELECT <include refid="fields"/>
        FROM <include refid="table_name" />
        WHERE `valid`=1
        AND `type`=1
    </select>

    <select id="queryById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfScene">
        SELECT <include refid="fields"/>
        FROM <include refid="table_name" />
        WHERE `id`=#{id}
        AND `type`=1
        AND `valid`=1
    </select>

</mapper>