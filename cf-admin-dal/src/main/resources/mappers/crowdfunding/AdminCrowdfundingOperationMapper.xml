<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingOperationDao">
    <sql id="tableName">
        crowdfunding_operation
    </sql>

    <sql id="fields">
        `id`,
        `info_id`,
        `operator_id`,
        `operation`,
        `reason`,
        `audit_commit_time` as auditCommitTime,
        `operate_time`as operateTime,
        `refuse_count`,
        `user_refuse_count` as userRefuseCount,
        `call_count`,
        `call_status`,
        `report_status`,
        `follow_type`,
        `date_created` as dateCreated,
        `last_modified` as lastModified,
        `case_id`,
        fuwu_type
    </sql>

    <sql id="filelds2">
        co.`info_id` as infoUuid,
        co.`operator_id`,
        co.`operation`,
        co.`operate_time`as lastOperationTime,
        co.`follow_type` as followStatus,
        info.id as id,
        info.type as type,
        info.title as title,
        count(cr.id) as reportNumber
        <if test="hospitalAuditStatus != null">
            ,
            hospitalAudit.`audit_status` AS  hospitalAuditStatus,
            hospitalAudit.`create_time` AS hospitalAuditSendTime,
            hospitalAudit.`operator_org` AS operatorOrg
        </if>
    </sql>

    <sql id="insertFields">
		`info_id`,
		`operator_id`,
        `operation`,
        `reason`,
        `case_id`
	</sql>

    <sql id="sortType">
        GROUP  BY co.info_id
        <if test="sortType == 1">
            ORDER BY reportNumber DESC
        </if>
        <if test="sortType == 2 || sortType == 3">
            <if test="infoUuids!=null">
            order by field(co.info_id,
            <foreach collection="infoUuids" separator="," close=")" item="infoUuid">
                #{infoUuid}
            </foreach>
            </if>
        </if>
        <if test="sortType == 4||sortType == 0">
            order by co.`operate_time` DESC
        </if>
    </sql>

    <sql id="addTrust">
        <if test="addTrustStatus!=null">
            LEFT JOIN cf_report_add_trust AS crat
            on co.info_id = crat.info_uuid
        </if>
    </sql>

    <sql id="hospitalAudit">
        <if test="hospitalAuditStatus!=null or hospitalSendBeginTime != null or hospitalSendBeginTime != null">
            LEFT JOIN (SELECT  * from cf_info_hospital_audit
            ORDER  BY `id` DESC )AS hospitalAudit
            on  co.info_id = hospitalAudit.info_uuid
        </if>
    </sql>

    <insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tableName"/>
        	(<include refid="insertFields"/>)
        VALUES
        	(#{infoId},#{operatorId},#{operation},#{reason},#{caseId})
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation">
		UPDATE <include refid="tableName"/>
		SET `operator_id`=#{operatorId},`operation`=#{operation},`reason`=#{reason},`follow_type` = #{followType},`operate_time` = #{operateTime}
		WHERE info_id = #{infoId}
	</update>

    <update id="updateOperation">
        UPDATE <include refid="tableName"/>
        SET `operator_id`=#{operatorId},`operation`=#{operation},`reason`=#{reason}, `defer_contact_reason_type`=#{deferContactReasonType},`operate_time`=NOW()
        WHERE info_id = #{infoUuid}
    </update>

    <select id="getByInfoId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation">
    	SELECT <include refid="fields" />
    	FROM <include refid="tableName" />
    	WHERE info_id=#{infoId}
    </select>

    <select id="getByInfoIdMaster" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation">
        SELECT <include refid="fields" />
        FROM <include refid="tableName" />
        WHERE info_id=#{infoId}
    </select>

    <insert id="insertOrUpdateCommitTime">
        INSERT INTO <include refid="tableName"/>
        (`info_id`, `audit_commit_time`)
        VALUES
        (#{infoId}, #{commitTime})
        ON DUPLICATE KEY
        UPDATE `audit_commit_time`=#{commitTime}
    </insert>

    <update id="addCallCount">
        UPDATE <include refid="tableName"/>
        SET `call_count`=`call_count`+#{count}
        WHERE `info_id`=#{infoId}
    </update>

    <update id="updateCallStatus">
        UPDATE <include refid="tableName"/>
        SET `call_status`=#{callStatus}
        WHERE `info_id`=#{infoId}
    </update>

    <update id="addRefuseCount">
        UPDATE <include refid="tableName"/>
        SET `refuse_count`=`refuse_count`+#{count},
        user_refuse_count = user_refuse_count + #{userRefuseCount, jdbcType=INTEGER}
        WHERE `info_id`=#{infoId}
    </update>

    <update id="setRefuseCount">
        UPDATE <include refid="tableName"/>
        SET `refuse_count`=#{count}
        WHERE `info_id`=#{infoId}
    </update>

    <update id="updateReportStatus">
        UPDATE <include refid="tableName"/>
        SET `report_status`=#{reportStatus}
        WHERE `info_id`=#{infoUuid}
    </update>

    <select id="selectByInfoUuid" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminCrowdfundingOperation">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE `info_id`=#{infoUuid}
    </select>

    <select id="selectByInfoUuids" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminCrowdfundingOperation">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE `info_id` IN
        <foreach collection="infoUuids" item="infoUuid" open="(" separator="," close=")">
            #{infoUuid}
        </foreach>
    </select>

    <select id="getByInfoIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation">
        SELECT *
        FROM <include refid="tableName" />
        WHERE info_id in
        <foreach collection="infoIds" open="(" separator="," close=")" item="infoId">#{infoId}</foreach>
    </select>

    <select id="getByTimeAndCallStatus" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation">
        SELECT *
        FROM <include refid="tableName" />
        WHERE  `call_status` = #{callStatus}
        AND <![CDATA[ `date_created`>=#{startTime} and `date_created`<#{endTime} ]]>
        limit #{offset},#{limit}
    </select>


    <select id="getReportCaseList" resultType="com.shuidihuzhu.cf.vo.crowdfunding.ReportCaseVo">
        SELECT <include refid="filelds2"/>
        FROM <include refid="tableName" /> AS co
        LEFT join crowdfunding_info AS info
        on co.info_id = info.info_id
        LEFT JOIN crowdfunding_report as cr
        on cr.activity_id = info.id
        <include refid="addTrust"/>
        <include refid="hospitalAudit"/>
        <if test="hospitalAuditStatus != null">
           <if test="hospitalAuditStatus != 0">
               WHERE hospitalAudit.`audit_status` = #{hospitalAuditStatus}
           </if>
           <if test="hospitalAuditStatus == 0">
               WHERE hospitalAudit.`audit_status` in (1,2,3,4)
           </if>
           <if test="onWorkOrder >= 0">
               AND hospitalAudit.`on_work_order` = #{onWorkOrder}
           </if>
        </if>
        <if test="hospitalAuditStatus == null">
            WHERE co.`report_Status`=2
        </if>
        <if test="caseType!=null">
            AND info.type=#{caseType}
        </if>
        <if test="followType!=null">
            AND co.`follow_type`=#{followType}
        </if>
        <if test="addTrustStatus!=null">
            AND crat.`audit_status`=#{addTrustStatus}
        </if>
        <if test="infoUuids!=null">
            AND co.`info_id` IN
            <foreach collection="infoUuids" open="(" separator="," close=")" item="infoUuid">
                #{infoUuid}
            </foreach>
        </if>
        <include refid="handleTimeFilter"/>
        <include refid="sortType"/>
    </select>

    <select id="getListByWhereAndPage" resultType="com.shuidihuzhu.cf.vo.crowdfunding.ReportCaseVo">
        SELECT <include refid="filelds2"/>
        FROM <include refid="tableName" /> AS co
        LEFT join crowdfunding_info AS info
        on co.info_id = info.info_id
        LEFT JOIN crowdfunding_report as cr
        on cr.activity_id = info.id
        <include refid="addTrust"/>
        <include refid="hospitalAudit"/>
        <if test="cfRefundStatus!=null">
            LEFT JOIN cf_refund AS cre
            ON cre.info_uuid=info.info_id
        </if>
        <if test="hospitalAuditStatus != null">
            <if test="hospitalAuditStatus != 0">
                WHERE hospitalAudit.`audit_status` = #{hospitalAuditStatus}
            </if>
            <if test="hospitalAuditStatus == 0">
                WHERE hospitalAudit.`audit_status` in (1,2,3,4)
            </if>
            <if test="onWorkOrder >= 0">
                AND hospitalAudit.`on_work_order` = #{onWorkOrder}
            </if>
        </if>
        <if test="hospitalAuditStatus == null">
            WHERE co.`report_Status`=2
        </if>
        <if test="caseType!=null">
            AND info.type=#{caseType}
        </if>
        <if test="followType!=null">
            AND co.`follow_type`=#{followType}
        </if>
        <if test="addTrustStatus!=null">
            AND crat.`audit_status`=#{addTrustStatus}
        </if>
        <if test="approveStatus!=null">
            AND info.status=#{approveStatus}
        </if>
        <if test="approveStatus==null">
            AND info.status != 2
        </if>
        <if test="infoUuids!=null">
            AND co.`info_id` IN
            <foreach collection="infoUuids" open="(" separator="," close=")" item="infoUuid">#{infoUuid}</foreach>
        </if>
        <if test="cfRefundStatus!=null">
            AND cre.apply_status > #{cfRefundStatus}
        </if>
        <include refid="handleTimeFilter"/>
        <include refid="sortType"/>
    </select>

    <select id="getListByWhereDrawCashAndPage" resultType="com.shuidihuzhu.cf.vo.crowdfunding.ReportCaseVo">
        SELECT <include refid="filelds2"/>
        FROM <include refid="tableName" /> AS co
        LEFT join crowdfunding_info AS info
        on co.info_id = info.info_id
        LEFT JOIN cf_draw_cash AS cdc
        ON cdc.info_uuid=info.info_id
        LEFT JOIN crowdfunding_report as cr
        on cr.activity_id = info.id
        <include refid="addTrust"/>
        <include refid="hospitalAudit"/>
        <if test="hospitalAuditStatus != null">
            <if test="hospitalAuditStatus != 0">
                WHERE hospitalAudit.`audit_status` = #{hospitalAuditStatus}
            </if>
            <if test="hospitalAuditStatus == 0">
                WHERE hospitalAudit.`audit_status` in (1,2,3,4)
            </if>
            <if test="onWorkOrder >= 0">
                AND hospitalAudit.`on_work_order` = #{onWorkOrder}
            </if>
        </if>
        <if test="hospitalAuditStatus == null">
            WHERE co.`report_Status`=2
        </if>
        AND info.status=#{approveStatus}
        <if test="caseType!=null">
            AND info.type=#{caseType}
        </if>
        <if test="followType!=null">
            AND co.`follow_type`=#{followType}
        </if>
        <if test="addTrustStatus!=null">
            AND crat.`audit_status`=#{addTrustStatus}
        </if>
        <if test="infoUuids!=null">
            AND co.`info_id` IN
            <foreach collection="infoUuids" open="(" separator="," close=")" item="infoUuid">#{infoUuid}</foreach>
        </if>
        <if test="DrawStatus==null and cfCashStatus==0">
            AND cdc.apply_status in (1,3)
        </if>
        <if test="DrawStatus==-1 and cfCashStatus==2">
            AND cdc.apply_status = #{cfCashStatus}
            AND cdc.draw_status=#{DrawStatus}
        </if>
        <if test="DrawStatus==3 and cfCashStatus==2">
            AND cdc.apply_status = #{cfCashStatus}
            AND cdc.draw_status IN (0,3)
        </if>
        <if test="DrawStatus==2 and cfCashStatus==2">
            AND cdc.apply_status = #{cfCashStatus}
            AND cdc.draw_status IN (1,2,4,5)
        </if>
        <include refid="handleTimeFilter"/>
        <include refid="sortType"/>
    </select>
    
    <select id="selectByInfoIdList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE `info_id` IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <sql id = "handleTimeFilter" >
        <if test="hospitalSendBeginTime != null and hospitalSendBeginTime > 0">
            AND hospitalAudit.create_time >= FROM_UNIXTIME(#{hospitalSendBeginTime})
        </if>
        <if test="hospitalSendEndTime != null and hospitalSendEndTime > 0">
            AND hospitalAudit.create_time  <![CDATA[ <= ]]> FROM_UNIXTIME(#{hospitalSendEndTime})
        </if>
        <if test="updateBeginTime != null and updateBeginTime > 0">
            AND co.operate_time >= FROM_UNIXTIME(#{updateBeginTime})
        </if>
        <if test="updateEndTime != null and updateEndTime > 0">
            AND co.operate_time <![CDATA[ <= ]]> FROM_UNIXTIME(#{updateEndTime})
        </if>
    </sql>

    <select id="getCrowdfundingOperation" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation">
        select
        <include refid="fields"/>
        from
        crowdfunding_operation
        where
        case_id  IN
        <foreach collection="caseIds" item="caseId" separator="," open="(" close=")">
            #{caseId}
        </foreach>
    </select>

    <update id="updateCreditStatus">
        UPDATE <include refid="tableName"/>
        SET `credit_status`=#{creditStatus}
        WHERE `case_id`=#{caseId}
    </update>

    <select id="findByReportStatus" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation">
        select
        <include refid="fields"/>
        from
        <include refid="tableName"/>
        where report_status in
        <foreach collection="reportStatusList" open="(" close=")" item="reportStatus" separator=",">
            #{reportStatus}
        </foreach>
    </select>


    <update id="updateFuwuType">
        UPDATE <include refid="tableName"/>
        SET `fuwu_type`= #{type}
        WHERE id=#{id}
    </update>

    <select id="getByCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation">
        SELECT <include refid="fields" />
        FROM <include refid="tableName" />
        WHERE case_id=#{caseId}
    </select>

</mapper>
