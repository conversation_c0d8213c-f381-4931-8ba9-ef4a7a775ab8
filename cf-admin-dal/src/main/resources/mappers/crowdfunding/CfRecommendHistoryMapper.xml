<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfRecommendHistoryDao">
  <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.model.crowdfunding.CfRecommendHistory">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="recommend_id" jdbcType="INTEGER" property="recommendId" />
    <result column="info_id" jdbcType="VARCHAR" property="infoId" />
    <result column="recommend_type" jdbcType="INTEGER" property="recommendType" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, recommend_id, info_id, recommend_type, is_delete, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'false' as QUERYID,
    <include refid="Base_Column_List" />
    from cf_recommend_history
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insertOnDuplicateUpdateBatch" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfRecommendHistory" useGeneratedKeys="true">
    insert into cf_recommend_history (recommend_id, info_id, recommend_type)
    values
    <foreach collection="list" item="history" separator=",">
      (#{history.recommendId,jdbcType=INTEGER}, #{history.infoId,jdbcType=VARCHAR},
      #{history.recommendType,jdbcType=INTEGER})
    </foreach>
    ON duplicate KEY UPDATE
    recommend_id=VALUES(recommend_id)
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfRecommendHistory" useGeneratedKeys="true">
    insert into cf_recommend_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="recommendId != null">
        recommend_id,
      </if>
      <if test="infoId != null">
        info_id,
      </if>
      <if test="recommendType != null">
        recommend_type,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="recommendId != null">
        #{recommendId,jdbcType=INTEGER},
      </if>
      <if test="infoId != null">
        #{infoId,jdbcType=VARCHAR},
      </if>
      <if test="recommendType != null">
        #{recommendType,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
</mapper>