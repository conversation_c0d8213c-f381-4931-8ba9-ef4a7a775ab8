<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfLabelMarkDao">

	<insert id="insertList" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfLabelRuleModel" useGeneratedKeys="true" keyProperty="id">
		INSERT cf_label_mark
		(`case_id`, `rule_name`, `rule_msg`)
		VALUES
		<foreach collection="list" separator="," item="item">
			(#{item.caseId}, #{item.ruleName}, #{item.ruleMsg})
		</foreach>
	</insert>


	<select id="hitQQLabel" resultType="java.lang.Integer">

		select count(1) from cf_label_mark where case_id = #{caseId} and
		is_delete = 0
	</select>
</mapper>
