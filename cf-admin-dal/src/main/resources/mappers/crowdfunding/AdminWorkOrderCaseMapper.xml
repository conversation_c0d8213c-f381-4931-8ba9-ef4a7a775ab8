<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminWorkOrderCaseDao">

    <sql id="table_name">
        `admin_work_order_case`
    </sql>
    
    <sql id="insert_fields">
        `work_order_id`,
        `case_id`,
        `type`,
        `status`,
        `call_status`,
        `approve_result`
    </sql>

    <sql id="select_fields">
        `id`,
        `work_order_id`,
        `case_id`,
        `type`,
        `status`,
        `call_status`,
        `create_time`,
        `update_time`,
        `approve_result`
    </sql>
    
    <sql id="join_fields">
        os.`id`,
        `work_order_id`,
        `case_id`,
        os.`type`,
        os.`status`,
        os.`call_status`,
        os.`create_time`,
        os.`update_time`
    </sql>
    
    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderCase" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES (#{workOrderId},#{caseId},#{type},#{status},#{callStatus},#{approveResult})
    </insert>

    <insert id="insertList" parameterType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderCase" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.workOrderId},#{item.caseId},#{item.type},#{item.status},#{item.callStatus},#{item.approveResult})
        </foreach>
    </insert>

    <select id="selectByCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderCase">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `case_id`=#{caseId}
        AND  `type`  = 1
        ORDER BY id DESC
        limit 1
    </select>


    <select id="selectByCaseIdList" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderCase">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `is_delete` = 0
        AND `work_order_id` IN
        <foreach collection="workOrderIds" item="workOrderId" separator="," open="(" close=")">
            #{workOrderId}
        </foreach>
    </select>

    <select id="selectUnHandleCaseTask" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderCase">
        SELECT <include refid="join_fields"/>
        FROM <include refid="table_name"/> os
        LEFT JOIN `admin_work_order` wo
        ON os.`work_order_id` = wo.`id`
        LEFT JOIN `crowdfunding_info` info
        ON os.`case_id` = info.id
        WHERE wo.`order_type`=#{orderType} AND wo.`order_task`=#{orderTask}
        AND `order_status`=0 AND `operator_id`=0 AND wo.`is_delete`=0
        ORDER BY info.`amount` DESC
        LIMIT #{count}
    </select>

    <select id="selectUnHandleCaseTaskByTime" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderCase">
        SELECT <include refid="join_fields"/>
        FROM <include refid="table_name"/> os
        LEFT JOIN `admin_work_order` wo
        ON os.`work_order_id` = wo.`id`
        LEFT JOIN `crowdfunding_info` info
        ON os.`case_id` = info.id
        WHERE wo.`order_type`=#{orderType} AND wo.`order_task`=#{orderTask}
        AND `order_status`=0 AND `operator_id`=0 AND wo.`is_delete`=0
        AND os.`create_time` BETWEEN #{startTime} AND #{endTime}
    </select>

    <select id="selectUnHandleCaseTaskByCount" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderCase">
        SELECT <include refid="join_fields"/>
        FROM <include refid="table_name"/> os
        LEFT JOIN `admin_work_order` wo ON os.`work_order_id` = wo.`id`
        <if test="channel > 0">
          left join crowdfunding_info i on os.case_id=i.id
        </if>
        WHERE wo.`order_type`= #{type}
        AND wo.`order_task`= #{task}
        AND wo.`task_type`= #{taskType}
        AND `order_status`= 0
        AND `operator_id`= 0
        AND wo.`is_delete`= 0

        <if test="channel == 1">
            AND  i.`channel`= 'cf_volunteer'
        </if>

        <if test="channel == 2">
            AND  i.`channel` != 'cf_volunteer'
        </if>

        ORDER BY wo.`create_time`
        LIMIT #{count}
    </select>

    <select id="selectById" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderCase">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `id` = #{id}
    </select>



    <update id="updateStatusById">
        UPDATE <include refid="table_name"/>
        SET `status`=#{status}
        WHERE `id`=#{id}
    </update>


    <update id="update">
        UPDATE <include refid="table_name"/>
        SET `status`=#{status},`call_status`=#{callStatus},`approve_result`=#{approveResult}
        WHERE `id`=#{id}
    </update>

    <update id="updateCallStatusById">
        UPDATE <include refid="table_name"/>
        SET `call_status`=#{callStatus}
        WHERE `id`=#{id}
    </update>


    <select id="selectByCaseIdAndTypeAndStatusAndTime" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderCase">
        SELECT `id`,`work_order_id`,`status`
        FROM
        <include refid="table_name"/>
        WHERE `case_id`=#{caseId}
        <if test="type!=null">
            AND `type`=#{type}
        </if>
        <if test="status!=null">
            AND `status`=#{status}
        </if>
        <if test="beginTime!=null">
            AND <![CDATA[ `create_time`>=#{beginTime} ]]>
        </if>
        <if test="endTime!=null">
            AND <![CDATA[ `create_time`<#{endTime} ]]>
        </if>
        AND `is_delete`=0
    </select>

    <select id="getLastByCaseIdAndType"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderCase">
        SELECT <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE `case_id`=#{caseId}
        <if test="type!=null">
            AND `type`=#{type}
        </if>
        AND `is_delete`=0
        order by id desc
        limit 1
    </select>

    <update id="updateStatusByIdList">
        UPDATE <include refid="table_name"/>
        SET `status`=#{status}
        WHERE `id` IN 
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>



    <update id="updateOrderCaseStatus" parameterType="int">
        update <include refid="table_name"/>
        set status = #{status}
        WHERE work_order_id = #{workOrderId}
    </update>



    <select id="selectPage" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderCase">
        SELECT <include refid="join_fields"/>
        FROM <include refid="table_name"/> os
        LEFT JOIN `admin_work_order` workOrder  ON os.`work_order_id`=workOrder.`id`
        <if test="channel > 0">
          left join crowdfunding_info i on os.case_id=i.id
        </if>
        <where>
            workOrder.`operator_id`=#{operatorId}
            AND os.`is_delete` = 0
            AND workOrder.`is_delete` = 0
            AND os.`status`=#{status}
            AND os.`type`=#{type}
            <if test="channel == 1">
                AND  i.`channel` = 'cf_volunteer'
            </if>
            <if test="channel == 2">
                AND  i.`channel` !='cf_volunteer'
            </if>
        </where>
    </select>

</mapper>
