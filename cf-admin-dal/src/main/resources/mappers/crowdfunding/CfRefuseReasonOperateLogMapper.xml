<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfRefuseReasonOperateLogDao">
    <sql id="table_name">
        `cf_refuse_reason_operate_log`
    </sql>

    <insert id="addLog" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity$CfRefuseReasonOperateLog" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="table_name"/>
        (`reason_entity_id`, `operator`,  `action`)
        VALUES (#{reasonEntityId}, #{operator}, #{action})
    </insert>

    <select id = "selectOperateLogByEntityId" resultType = "com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity$CfRefuseReasonOperateLog">
      SELECT * FROM <include refid="table_name"/>
      WHERE reason_entity_id = #{entityId}
    </select>

</mapper>