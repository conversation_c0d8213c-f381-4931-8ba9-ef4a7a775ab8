<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfBlackListRecordDao">
    <sql id="table_name">
        `cf_blacklist_record`
    </sql>

    <sql id="insert_fields">
        `user_id`,
        `is_blacklist`,
        `operator_id`,
        `limit_range`,
        `limit_type`,
        `limit_part`,
        `mode`,
        `reason`,
        `content_valid`
    </sql>

    <sql id="select_fields">
        `id`,
        `user_id`,
        `is_blacklist`,
        `operator_id`,
        `limit_range`,
        `limit_type`,
        `limit_part`,
        `mode`,
        `reason`,
        `content_valid`
        `create_time`,
        `update_time`
    </sql>

    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfBlackListRecord">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES (#{userId},#{isBlacklist},#{operatorId},#{limitRange},#{limitType},#{limitPart},#{mode},#{reason},#{contentValid})
    </insert>

    <select id="selectByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfBlackListRecord">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `user_id`=#{userId}
    </select>
</mapper>