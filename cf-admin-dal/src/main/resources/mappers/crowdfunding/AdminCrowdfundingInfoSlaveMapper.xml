<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingInfoSlaveDao">
	<sql id="tableName">
		crowdfunding_info
	</sql>

	<sql id="fields">
		`id`, `info_id`, `user_id`,
		`relation`, `applicant_name`, `applicant_qq`, `applicant_mail`, `relation_type`, `channel_type`,`channel`,
		`payee_name`, `payee_id_card`, `payee_mobile`, `payee_bank_name`, `payee_bank_branch_name`, `payee_bank_card`,
		`bank_card_verify_status`,
		`bank_card_verify_message`, `bank_card_verify_message2`, `title`, `title_img`, `content`, `encrypt_content`,
		`target_amount`, `amount`, `donation_count`, `status`, `create_time`, `begin_time`, `end_time`, `from`, `use`,`data_status`,
		`type`, `content_type`, `last_modified`, `content_image`, `content_image_status`,`material_plan_id`
	</sql>

	<sql id="join_fields">
		ci.`id`, ci.`info_id`, ci.`user_id`,
		ci.`relation`, ci.`applicant_name`, ci.`applicant_qq`, ci.`applicant_mail`, ci.`relation_type`, ci.`channel_type`,ci.`channel`,
		ci.`payee_name`, ci.`payee_id_card`, ci.`payee_mobile`, ci.`payee_bank_name`, ci.`payee_bank_branch_name`, ci.`payee_bank_card`,
		ci.`bank_card_verify_status`,
		ci.`bank_card_verify_message`, ci.`bank_card_verify_message2`, ci.`title`, ci.`title_img`, ci.`content`,
		ci.`target_amount`, ci.`amount`, ci.`donation_count`, ci.`status`, ci.`create_time`, ci.`begin_time`,
		ci.`end_time`, ci.`from`, ci.`use`,ci.`data_status`,ci.`type`, ci.`content_type`
	</sql>

	<sql id="join_fields2">
		ci.`id`, ci.`info_id`, ci.`user_id`,ci.`material_plan_id`,
		ci.`relation`, ci.`applicant_name`, ci.`applicant_qq`, ci.`applicant_mail`, ci.`relation_type`, ci.`channel_type`,ci.`channel`,
		ci.`payee_name`, ci.`payee_id_card`, ci.`payee_mobile`, ci.`payee_bank_name`, ci.`payee_bank_branch_name`, ci.`payee_bank_card`,
		ci.`bank_card_verify_status`,
		ci.`bank_card_verify_message`, ci.`bank_card_verify_message2`, ci.`title`, ci.`title_img`, ci.`content`,
		ci.`target_amount`, ci.`amount`, ci.`donation_count`, ci.`status`, ci.`create_time`, ci.`begin_time`,
		ci.`end_time`, ci.`from`, ci.`use`,ci.`data_status`,ci.`type`,ci.`content_type`, ci.`content_image`, ci.`content_image_status`,
		cfo.`operation`,cfo.`audit_commit_time`,cfo.`refuse_count`,cfo.`user_refuse_count`,cfo.`call_count`,cfo.`call_status`,cfo.`operator_id`,
		cie.`crypto_register_mobile`
	</sql>

	<sql id="join_fields3">
		ci.`id`, ci.`info_id`, ci.`user_id`,
		ci.`relation`, ci.`applicant_name`, ci.`applicant_qq`, ci.`applicant_mail`, ci.`relation_type`, ci.`channel_type`,ci.`channel`,
		ci.`payee_name`, ci.`payee_id_card`, ci.`payee_mobile`, ci.`payee_bank_name`, ci.`payee_bank_branch_name`, ci.`payee_bank_card`,
		ci.`bank_card_verify_status`,
		ci.`bank_card_verify_message`, ci.`bank_card_verify_message2`, ci.`title`, ci.`title_img`, ci.`content`,
		ci.`target_amount`, ci.`amount`, ci.`donation_count`, ci.`status`, ci.`create_time`, ci.`begin_time`,
		ci.`end_time`, ci.`from`, ci.`use`,ci.`data_status`,ci.`type`,ci.`content_type`,
		cie.`crypto_register_mobile`
	</sql>

    <sql id="join_fields_dream">
        ci.`id`, ci.`info_id`, ci.`user_id`,
        ci.`relation`, ci.`applicant_name`, ci.`applicant_qq`, ci.`applicant_mail`, ci.`relation_type`, ci.`channel_type`,ci.`channel`,
        ci.`payee_name`, ci.`payee_id_card`, ci.`payee_mobile`, ci.`payee_bank_name`, ci.`payee_bank_branch_name`, ci.`payee_bank_card`,
        ci.`bank_card_verify_status`,
        ci.`bank_card_verify_message`, ci.`bank_card_verify_message2`, ci.`title`, ci.`title_img`, ci.`content`,
        ci.`target_amount`, ci.`amount`, ci.`donation_count`, ci.`status`, ci.`create_time`, ci.`begin_time`,
        ci.`end_time`, ci.`from`, ci.`use`,ci.`data_status`,ci.`type`,ci.`content_type`,
        cfo.`operation`,cfo.`audit_commit_time`,cfo.`refuse_count`,cfo.`user_refuse_count`,cfo.`call_count`,cfo.`call_status`,cfo.`operator_id`,
        cd.`case_status`,cd.`submit_status`
    </sql>

    <sql id="base_list_page_fields">
		ci.`id`, ci.`info_id`, ci.`user_id`,
		ci.`relation`, ci.`applicant_name`, ci.`applicant_qq`, ci.`applicant_mail`, ci.`relation_type`, ci.`channel_type`,ci.`channel`,
		ci.`payee_name`, ci.`payee_id_card`, ci.`payee_mobile`, ci.`payee_bank_name`, ci.`payee_bank_branch_name`, ci.`payee_bank_card`,
		ci.`bank_card_verify_status`,
		ci.`bank_card_verify_message`, ci.`bank_card_verify_message2`, ci.`title`, ci.`title_img`, ci.`content`,
		ci.`target_amount`, ci.`amount`, ci.`donation_count`, ci.`status`, ci.`create_time`, ci.`begin_time`,
		ci.`end_time`, ci.`from`, ci.`use`,ci.`data_status`,ci.`type`,ci.`content_type`,
		cfo.`operation`,cfo.`audit_commit_time`,cfo.`refuse_count`,cfo.`user_refuse_count`,cfo.`call_count`,cfo.`call_status`,cfo.`operator_id`
	</sql>

	<select id="getCrownfundingSummary" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingSummary">
		SELECT
		sum(`amount`) as totalAmount, sum(`donation_count`) as totalCount
		FROM
		<include refid="tableName"/>
		WHERE
		 	`type` = #{type}
	</select>

	<select id="selectByExampleJoin" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoVo"
			parameterType="com.shuidihuzhu.common.web.util.admin.BasicExample">
		select <include refid="join_fields2"/>
		FROM crowdfunding_info ci
		JOIN cf_info_ext cie on ci.info_id=cie.info_uuid
		LEFT JOIN crowdfunding_operation cfo on ci.info_id=cfo.info_id
		LEFT JOIN crowdfunding_repeat_info cri on ci.id = cri.case_id
		<include refid="com.shuidihuzhu.cf.dao.message.Example_Where_Clause"/>
	</select>

    <select id="selectByDreamExampleJoin" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingDreamVo"
            parameterType="com.shuidihuzhu.common.web.util.admin.BasicExample">
        SELECT
        <include refid="join_fields_dream"/>
        FROM crowdfunding_dream cd
        LEFT JOIN crowdfunding_info ci ON cd.info_id=ci.info_id
        LEFT JOIN crowdfunding_operation cfo ON ci.info_id=cfo.info_id
        <include refid="com.shuidihuzhu.cf.dao.message.Example_Where_Clause"/>
    </select>

    <select id="selectByGoodExampleJoin" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingDreamVo"
            parameterType="com.shuidihuzhu.common.web.util.admin.BasicExample">
        SELECT
        <include refid="join_fields2"/>
        FROM crowdfunding_info ci
        JOIN cf_info_ext cie on ci.info_id=cie.info_uuid
        LEFT JOIN crowdfunding_operation cfo ON ci.info_id=cfo.info_id
        <include refid="com.shuidihuzhu.cf.dao.message.Example_Where_Clause"/>
    </select>

	<select id="getFundingInfo" parameterType="String"
			resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT
		<include refid="fields"/>
		FROM
		<include refid="tableName"/>
		WHERE
		info_id = #{infoId}
	</select>

	<select id="getFundingInfoList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT <include refid="fields"/>
		FROM <include refid="tableName"/>
		where <![CDATA[ amount>=3000000 and `end_time`<now() ]]>
		order by begin_time desc limit 30
	</select>

	<select id="getFundingInfoById" parameterType="int" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT
		<include refid="fields"/>
		FROM
		<include refid="tableName"/>
		WHERE id = #{id}
	</select>

    <select id="getFundingInfoVoByInfoUuid" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoVo">
        SELECT <include refid="join_fields2"/>
        FROM crowdfunding_info ci
        JOIN cf_info_ext cie on ci.info_id=cie.info_uuid
        LEFT JOIN crowdfunding_operation cfo on ci.info_id=cfo.info_id
        WHERE ci.`info_id`=#{infoUuid}
    </select>

	<select id="getFundingInfoByIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT <include refid="fields"/>
		FROM <include refid="tableName"/>
		WHERE id in
		<foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<select id="getFundingInfoByInfoIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT <include refid="fields"/>
		FROM <include refid="tableName"/>
		WHERE `info_id` IN
		<foreach item="item" index="index" collection="infoIds" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<select id="getByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT
		<include refid="fields"/>
		FROM
		<include refid="tableName"/>
		WHERE user_id = #{userId}
		ORDER BY id desc
	</select>

	<select id="getRefundListByIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT <include refid="join_fields"/>
		FROM crowdfunding_info ci
		LEFT JOIN cf_refund cr ON ci.info_id=cr.info_uuid
		WHERE ci.id IN <foreach item="id" collection="ids" open="(" separator="," close=")">#{id}</foreach>
			AND (cr.id is null OR cr.is_deleted=0)
	</select>

	<select id="getRefundByPage" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT <include refid="join_fields"/>
		FROM crowdfunding_info ci
		JOIN cf_refund cr on ci.info_id=cr.info_uuid
		WHERE cr.is_deleted=0
		<if test="bizType!=null">
			AND cr.biz_type=#{bizType}
		</if>
		<if test="applyStatus!=null and applyStatus>-1">
			AND cr.apply_status=#{applyStatus}
		</if>
		<if test="applyStatus == null or applyStatus==-1">
			AND cr.apply_status != 0
		</if>
		<if test="refundStatus!=null">
			AND cr.refund_status=#{refundStatus}
		</if>
		<if test="operationType!=null">
			AND cr.operation_type=#{operationType}
		</if>
		ORDER BY ci.id desc
	</select>

	<select id="getByCreateTime" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT <include refid="fields"/>
		FROM <include refid="tableName"/>
		WHERE <![CDATA[ `create_time`>=#{startTime} and `create_time`<#{endTime} ]]>
	</select>

	<select id="getCrowdfundingTextByIds" resultType="com.shuidihuzhu.cf.vo.crowdfunding.CrowdfundingTextVo">
		SELECT `id`,`info_id`,`type`,`title`,`content`
		FROM <include refid="tableName"/>
		WHERE 1=1 and id in
		<foreach item="item" index="index" collection="idcon"
				 open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

    <select id="selectGarbageUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfRepeatUserIdRecord">
        SELECT
            `user_id`,COUNT(*) as `remain_counts`
        FROM
            crowdfunding_info
        WHERE
            `end_time`  <![CDATA[ > ]]> CURRENT_TIMESTAMP
        GROUP BY
            `user_id`
        HAVING
            count(*) <![CDATA[ > ]]> 1
    </select>

    <select id="selectByUserId" parameterType="java.util.List" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoVo">
        SELECT <include refid="join_fields2"/>
        FROM crowdfunding_info ci
        JOIN cf_info_ext cie on ci.info_id=cie.info_uuid
        LEFT JOIN crowdfunding_operation cfo on ci.info_id=cfo.info_id
    </select>

    <select id="selectCountByFiveMin" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM <include refid="tableName"/>
        WHERE <![CDATA[ `create_time`>#{begin} ]]> AND <![CDATA[ `create_time`<#{end} ]]>
    </select>

    <select id="selectGoodOperation" resultType="java.lang.String">
        SELECT
            ci.`info_id`
        FROM
            <include refid="tableName"/> ci
            LEFT JOIN
            `crowdfunding_operation` co ON ci.`info_id` = co.`info_id`
        WHERE
            ci.`type` = 1 AND co.id IS NULL
    </select>

    <select id="selectByIdAndAmount" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE `id` IN
        <foreach collection="set" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND `amount`>=#{amount}
    </select>

    <select id="selectIdAndUuidAndTypeById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
        SELECT `id`,`info_id`,`type`
        FROM <include refid="tableName"/>
        WHERE `id` IN
        <foreach collection="set" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectIdAndUuidAndTypeByInfoUuid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
        SELECT `id`,`info_id`,`type`
        FROM <include refid="tableName"/>
        WHERE `info_id` IN
        <foreach collection="set" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByUserIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE `user_id` IN
        <foreach collection="set" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectDreamInfoNotMapDreamInfoId" resultType="java.lang.String">
        select ci.info_id from (select info_id from <include refid="tableName"/> where type = 99) ci left join crowdfunding_dream cd on cd.info_id = ci.info_id where cd.info_id is null
        limit #{start},#{size}
    </select>

    <select id="selectByTimeAndAmount" resultType="java.lang.Integer">
        SELECT `id` FROM <include refid="tableName"/>
        WHERE <![CDATA[ `create_time` >= #{beginTime} ]]> AND <![CDATA[ `create_time`<#{endTime} ]]>
        AND <![CDATA[ `end_time` > now() ]]>
        `amount` != 0
    </select>

	<select id="selectCaseAuditStatusByCaseIds" parameterType="java.util.List" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminReportAddTrustBo">
		select info.id as caseId,trust.audit_status
		from <include refid="tableName"/> info
		left join cf_report_add_trust trust
		on info.info_id = trust.info_uuid
		where trust.info_uuid in
		<foreach collection="infoIdList" open="(" close=")" separator="," item="infoId">
			#{infoId}
		</foreach>
	</select>


	<select id="getInfoByCreateTimeAndId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT
		<include refid="fields"/>
		FROM
		<include refid="tableName"/>
		WHERE
		<![CDATA[`id` <= #{id}]]>
		AND `create_time` > #{createTime}
		AND `status` = #{status}
		ORDER BY create_time DESC
		LIMIT #{offset},#{limit}
	</select>

	<select id="getCountByCreateTime" resultType="java.lang.Integer">
		SELECT
		count(*)
		FROM
		<include refid="tableName"/>
		WHERE
	  	`create_time` > #{createTime}
		AND `status` = #{status}
	</select>

	<select id="selectByPage" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoVo">
		select <include refid="join_fields2"/>
		FROM crowdfunding_info ci
		JOIN cf_info_ext cie on ci.info_id= cie.info_uuid
		LEFT JOIN crowdfunding_operation cfo on ci.info_id=cfo.info_id
		<if test="(userId == null or userId == 0) and title == null and idListByName == null and mobile == null and id == null">
			LEFT  JOIN (select * from cf_info_hospital_audit where `is_delete` = 0 AND `audit_status` != 2) as ciha
			ON ciha.info_uuid = ci.info_id
		</if>
		WHERE ci.`type` = 0
		<if test="id != null">
			AND ci.id = #{id}
		</if>
		<if test="idListByName != null">
			AND ci.id IN
			<foreach collection="idListByName" item="id" separator="," open="(" close=")">
				#{id}
			</foreach>
		</if>
		<if test="userId != null and userId > 0">
			AND ci.user_id = #{userId}
		</if>
		<if test="title != null">
			<bind name="namePattern" value="'%' + title + '%'"/>
			AND ci.`title` LIKE #{namePattern}
		</if>
		<if test="mobile != null"> AND cie.`crypto_register_mobile` = #{mobile}  </if>
		<if test="(userId == null or userId == 0) and title == null and idListByName == null and mobile == null and id == null">
			<include refid="handle"/>
				<if test="status != null">
					AND ci.`status` = #{status}
				</if>
				<if test="finished == 0">AND <![CDATA[ ci.`end_time` > #{Date} ]]>  </if>
				<if test="finished == 1">AND <![CDATA[ ci.`end_time` <= #{Date} ]]> </if>
				<if test="dataStatus != null">AND ci.`data_status` = #{dataStatus}</if>
				<if test="startTime !=null and endTime !=null">
					AND <![CDATA[ cfo.`audit_commit_time` > #{startTime} ]]>
					AND <![CDATA[ cfo.`audit_commit_time` <= #{endTime} ]]>
				</if>
				<if test="operationStatus != null and handle == null">
					AND cfo.`operation` = #{operationStatus}
				</if>
				<include refid="refuseCountHandle"/>
				<include refid="contact"/>
				AND ciha.`info_uuid` is null
		</if>
		ORDER BY  ${sortHandle}
	</select>

	<sql id="refuseCountHandle">
		<if test="refuseCountHandle != null">
			<if test="refuseCountHandle == 0">
				AND  cfo.`refuse_count` > 3
			</if>
			<if test="refuseCountHandle == 1">
				AND <![CDATA[ cfo.`refuse_count` >= 1 ]]>
				AND <![CDATA[ cfo.`refuse_count` <= 3 ]]>
			</if>
			<if test="refuseCountHandle == 2">
				AND cfo.`refuse_count` = 0
			</if>
		</if>
	</sql>

	<sql id="contact">
		<if test="isContact != null">
			<if test="isContact == 0">
				AND  cfo.`call_status` = 0
			</if>
			<if test="isContact == 1 ">
				AND cfo.`call_status` != 0
			</if>
		</if>
	</sql>

	<sql id="handle">
		<if test="handle != null">
			AND cfo.operation = #{handle}
		</if>
		<if test="handle == null and (userId == null or userId == 0) and title == null and idListByName == null and mobile == null and id == null">
			AND cfo.`operation` in (0,1,2)
		</if>
	</sql>

    <select id="selectBaseApproveListOnlyOne" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoVo">
        SELECT
            <include refid="base_list_page_fields"/>
        FROM
            crowdfunding_info ci force index (idx_create_time)
            LEFT JOIN
            crowdfunding_operation cfo ON ci.info_id = cfo.info_id
            LEFT JOIN
            cf_info_hospital_audit AS ciha ON ciha.info_uuid = ci.info_id
        WHERE
            ci.`type` = 0
            AND cfo.`operation` IN (0 , 1, 2)
            AND (ciha.`info_uuid` IS NULL OR ciha.audit_status = 2 OR ciha.is_delete = 1)
        ORDER BY ci.create_time DESC limit #{pageSize}
    </select>

	<select id="selectBaseApproveListPages" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoVo">
		SELECT
		<include refid="base_list_page_fields"/>
		FROM
		crowdfunding_info ci force index (idx_create_time)
		LEFT JOIN
		crowdfunding_operation cfo ON ci.info_id = cfo.info_id
		LEFT JOIN
		cf_info_hospital_audit AS ciha ON ciha.info_uuid = ci.info_id
		WHERE
		ci.`type` = 0
		AND cfo.`operation` IN (0 , 1, 2)
		AND (ciha.`info_uuid` IS NULL OR ciha.audit_status = 2 OR ciha.is_delete = 1)
		ORDER BY ci.create_time  DESC limit #{begin}, #{offset}
	</select>


    <select id="selectBaseContactListPages" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoVo">
        SELECT
            <include refid="base_list_page_fields"/>
        FROM
            crowdfunding_info ci force index (idx_create_time)
		LEFT JOIN
            crowdfunding_operation cfo ON ci.info_id = cfo.info_id
			LEFT JOIN cf_info_ext cie on ci.info_id= cie.info_uuid
        WHERE
            ci.type = 0
            AND cfo.operation IN (0 , 1, 2)
			AND cie.first_approve_status IN (0, 30)
        ORDER BY ci.create_time DESC limit #{begin}, #{offset}
    </select>

    <select id="selectByRepeatCase" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where  <![CDATA[ `create_time` > date_sub(now(), interval 1 month) ]]>
    </select>

	<select id="reserveSelectByPage" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoVo">
		select
		<include refid="join_fields3"/>
		FROM crowdfunding_info ci
		JOIN cf_info_ext cie on ci.info_id= cie.info_uuid
		WHERE ci.`type` = 0
		<if test="id != null">
			AND ci.id = #{id}
		</if>
		<if test="idListByName != null">
			AND ci.id IN
			<foreach collection="idListByName" item="id" separator="," open="(" close=")">
				#{id}
			</foreach>
		</if>
		<if test="userId != null and userId > 0">
			AND ci.user_id = #{userId}
		</if>
		<if test="title != null">
			<bind name="namePattern" value="'%' + title + '%'"/>
			AND ci.`title` LIKE #{namePattern}
		</if>
		<if test="mobile != null">AND cie.`crypto_register_mobile` = #{mobile}</if>
		ORDER BY ci.id DESC
	</select>

	<select id="getFundingInfoByInfoIdsOrderById"
			resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT
		<include refid="fields"/>
		FROM
		<include refid="tableName"/>
		WHERE info_id IN
		<foreach item="item" collection="infoIds" open="(" separator="," close=")">
			#{item}
		</foreach>
		ORDER BY id DESC
	</select>

	<select id="getFundingInfoByIdsOrderById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT <include refid="fields"/>
		FROM <include refid="tableName"/>
		WHERE id in
		<foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
			#{item}
		</foreach>
		ORDER BY id DESC
	</select>
</mapper>
