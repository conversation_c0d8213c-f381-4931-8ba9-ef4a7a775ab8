<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminGoodsGearDao">
    <sql id="tableName">
        goods_gear
    </sql>

    <update id="updateByGoodsGear" parameterType="com.shuidihuzhu.cf.model.goods.GoodsGear">
        UPDATE <include refid="tableName"/>
        SET `target_num`=#{targetNum},`title`=#{title},`has_limit`=#{hasLimit},`valid`=#{valid},
        `detail`=#{detail},`need_address`=#{needAddress},`need_email`=#{needEmail},`amount`=#{amount}
        WHERE `id`=#{id} AND `info_uuid`=#{infoUuid}
    </update>

    <update id="updateValid">
        UPDATE <include refid="tableName"/>
        SET `valid`=#{valid}
        WHERE `id`=#{id}
    </update>
</mapper>