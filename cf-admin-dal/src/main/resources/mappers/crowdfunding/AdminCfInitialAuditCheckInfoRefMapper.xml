<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCfInitialAuditCheckInfoRefDAO">
    <sql id="SELECT_FIELDS">
        `id` AS id,
        `reference_name` AS referenceName,
        `reference_type` AS referenceType,
        `create_time` AS createTime,
        `update_time` AS updateTime,
        `is_delete` AS isDelete
    </sql>

    <sql id="TABLE_NAME">
        `cf_admin_initial_audit_check_info_reference`
    </sql>

    <insert id="batchInsert" parameterType="com.shuidihuzhu.cf.domain.cf.AdminCfInitialAuditCheckInfoRefDO">
        INSERT INTO
        <include refid="TABLE_NAME"/>
        (`reference_name`, `reference_type`)
        VALUES
        <foreach collection="list" separator="," item="item">
            (#{item.referenceName}, #{item.referenceType})
        </foreach>
    </insert>

    <select id="getByRefNameAndType" resultType="com.shuidihuzhu.cf.domain.cf.AdminCfInitialAuditCheckInfoRefDO">
        SELECT
        <include refid="SELECT_FIELDS"/>
        FROM
        <include refid="TABLE_NAME"/>
        WHERE `reference_name` = #{refName} AND `reference_type` = #{refType}
    </select>
</mapper>