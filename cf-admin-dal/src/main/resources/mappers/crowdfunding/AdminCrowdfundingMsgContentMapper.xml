<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingMsgContentDao">

    <sql id="tableName">
        `crowdfunding_msg_content`
    </sql>

    <sql id="QUERY_FIELDS">
        `id`,
        `key`,
        `content`,
        `type`,
        `name`,
        `date_created` as dateCreated,
        `last_modified` as lastModified
    </sql>

    <sql id="insert_fields">
        `key`,
        `name`,
        `content`
    </sql>
    
    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingMsgContent">
        INSERT INTO <include refid="tableName"/>
        (<include refid="insert_fields"/>)
        VALUES (#{key},#{name},#{content})
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingMsgContent">
        UPDATE <include refid="tableName"/>
        SET `name`=#{name}, `content`=#{content}
        WHERE `id`=#{id}
    </update>

    <select id="selectById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingMsgContent">
        SELECT <include refid="QUERY_FIELDS"/>
        FROM <include refid="tableName"/>
        WHERE `id`=#{id}
    </select>

    <delete id="deleteOne">
        DELETE FROM <include refid="tableName"/>
        WHERE `id`=#{id}
    </delete>
</mapper>