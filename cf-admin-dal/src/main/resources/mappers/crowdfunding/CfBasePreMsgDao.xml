<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfBasePreMsgDao">


	<sql id="selectFields">
	   id,
	   case_id,
	   info_id,
	   task_id,
       mobile,
       disease_name,
       reason,
       operator_id,
       operator_org,
       msg_status,
       patient_name,
       patient_id_type,
       patient_crypto_idcard,
       relation_type,
       initiator_name,
       initiator_crypto_idcard,
       treatment_url,
       title,
       content,
       target_amount,
       pic_url,
       msg_url,
       create_time,
       update_time,
       exchange_flag
	</sql>

	<select id="getCfBasePreMsg" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfBasePreMsg">

		select <include refid="selectFields"/> from cf_base_pre_msg
		where mobile = #{mobile} and is_delete=0
		order by id desc limit 1
	</select>

	<update id="updateMsgStatusByMobile">
		update cf_base_pre_msg
		set msg_status= #{status},
			case_id = #{caseId},
			info_id= #{uuid}
	<![CDATA[ where mobile = #{mobile} and is_delete=0 and msg_status < 4  ]]>

	</update>

	<insert id="insertRecord" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfBasePreMsgRecord" useGeneratedKeys="true" keyProperty="id">

		insert into cf_base_pre_msg_record (msg_id,mode,operator_id,operator_org)
		values (#{msgId},#{mode},#{operatorId},#{operatorOrg})

	</insert>

	<select id="getRecords" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfBasePreMsgRecord">
		select id, msg_id,mode,operator_id,operator_org,update_time
		from cf_base_pre_msg_record
		where msg_id= #{msgId}
	</select>

	<select id = "selectUseRecordByPreIds" resultType = "com.shuidihuzhu.cf.model.crowdfunding.CfBasePreMsg$UseTemplateRecord">
		SELECT *
		FROM
		cf_base_pre_template_record
		WHERE pre_id IN
		<foreach collection="preIds" item="preId" open="(" separator="," close=")" >
			#{preId}
		</foreach>

		AND is_delete = 0
	</select>
	<select id="get1V1MsgList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfBasePreMsg">
		select <include refid="selectFields"/>  from cf_base_pre_msg
		where is_delete=0
		<if test="mobile != null and mobile != ''">
			and mobile = #{mobile}
		</if>
		<if test="startTime != null and startTime != '' and endTime != null and endTime != ''  ">
			and update_time between #{startTime} and #{endTime}
		</if>
	</select>

</mapper>
