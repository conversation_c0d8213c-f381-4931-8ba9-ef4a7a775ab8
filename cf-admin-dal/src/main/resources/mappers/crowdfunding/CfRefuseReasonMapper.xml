<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfRefuseReasonDao">
    <resultMap type="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReason" id="CfRefuseReasonResult">
        <id property="id" column="id"/>
        <result property="content" column="content"/>
        <result property="frequency" column="frequency"/>
    </resultMap>
    <sql id="tableName">
        shuidi_crowdfunding.cf_refuse_reason
    </sql>
    <update id="editReason">
        UPDATE
        <include refid="tableName"/>
        SET `content` = #{content}
        WHERE id= #{id}
    </update>
    <select id="getRefuseReason" resultMap="CfRefuseReasonResult">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE pid >= 0
    </select>
    <update id="editFrequency" parameterType="int">
        UPDATE
        <include refid="tableName"/>
        SET `frequency` = frequency + 1
        WHERE id = #{id}
    </update>

    <select id="findText" parameterType="int" resultType="java.lang.String">
        SELECT
        `content`
        FROM <include refid="tableName"/>
        WHERE id = #{id};
    </select>

    <insert id="insertReason">
        INSERT INTO
        <include refid="tableName"/>
        (
        `id`,
        `pid`,
        `content`
        )
        VALUES (#{id},#{pid},#{content})
    </insert>

    <select id="selectMax" resultType="int">
        SELECT max(`id`)+1
        FROM <include refid="tableName"/>
    </select>

    <select id="selectPid" parameterType="int" resultType="int">
        SELECT `pid`
        FROM <include refid="tableName"/>
        WHERE id = #{id}
    </select>

    <update id="emptyFrequency">
        UPDATE <include refid="tableName"/>
        SET `frequency` = 0
        WHERE `pid` >= 0 and `id` >= 0;
    </update>

    <select id="selectAll" resultMap="CfRefuseReasonResult">
        SELECT * FROM <include refid="tableName"/>
        WHERE `pid` >= 0
    </select>
    <select id="selectWithItem" parameterType="int" resultType="int">
        SELECT `type`
        FROM `cf_refuse_reason` cr LEFT JOIN `cf_refuse_reason_item` ci
        ON cr.`pid` = ci.`id` WHERE
        cr.`id` = #{id}
    </select>
    <update id="deleteById">
        UPDATE <include refid="tableName"/>
        SET `pid`=-1
        WHERE `id`=#{id}
    </update>

    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReason" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tableName"/>
        (`pid`,`content`)
        VALUES
        (#{pid},#{content})
    </insert>
</mapper>