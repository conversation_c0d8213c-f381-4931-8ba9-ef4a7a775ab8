<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingVolunteerHospitalDao">
    <sql id="tableName">
        crowdfunding_volunteer_hospital
    </sql>

    <sql id="insertFields">
        name,
        level,
        city_id
    </sql>

    <sql id="selectFields">
        id,
        name,
        level,
        city_id
    </sql>

    <insert id="insertHospital" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingVolunteerHospital">
        INSERT INTO
        <include refid="tableName"/>
        (<include refid="insertFields"/>)
        VALUES
        (#{name},#{level},#{cityId})
    </insert>

    <select id="getHospitalName" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingVolunteerHospital">
        SELECT
        <include refid="selectFields"/>
        FROM
        <include refid="tableName"/>
        WHERE city_id = #{cityId}
    </select>
</mapper>