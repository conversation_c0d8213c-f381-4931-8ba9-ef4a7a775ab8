<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCfInitialAuditCheckInfoDAO">
    <sql id="SELECT_FIELDS">
        `id` AS id,
        `case_id` AS caseId,
        `check_type` AS checkType,
        `check_name` AS checkName,
        `check_result` AS checkResult
    </sql>

    <sql id="INSERT_FIELDS">
        `case_id`, `check_type`, `check_name`, `check_result`
    </sql>

    <sql id="TABLE_NAME">
        `cf_admin_initial_audit_check_info`
    </sql>

    <insert id="add" parameterType="com.shuidihuzhu.cf.domain.cf.AdminCfInitialAuditCheckInfoDO">
        INSERT INTO
        <include refid="TABLE_NAME"/>
        (<include refid="INSERT_FIELDS"/>)
        VALUES (#{caseId}, #{checkType}, #{checkName}, #{checkResult})
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.cf.domain.cf.AdminCfInitialAuditCheckInfoDO">
        UPDATE
        <include refid="TABLE_NAME"/>
        SET
        `check_name` = #{checkName},
        `check_result` = #{checkResult}
        WHERE `id` = #{id}
    </update>

    <select id="getByCaseIdAndCheckType" resultType="com.shuidihuzhu.cf.domain.cf.AdminCfInitialAuditCheckInfoDO">
        SELECT
        <include refid="SELECT_FIELDS"/>
        FROM
        <include refid="TABLE_NAME"/>
        WHERE `case_id` = #{caseId} AND `check_type` = #{checkType}
    </select>

    <select id="listByCaseIdAndCheckType" resultType="com.shuidihuzhu.cf.domain.cf.AdminCfInitialAuditCheckInfoDO">
        SELECT
        <include refid="SELECT_FIELDS"/>
        FROM
        <include refid="TABLE_NAME"/>
        WHERE `case_id` = #{caseId}
    </select>
</mapper>