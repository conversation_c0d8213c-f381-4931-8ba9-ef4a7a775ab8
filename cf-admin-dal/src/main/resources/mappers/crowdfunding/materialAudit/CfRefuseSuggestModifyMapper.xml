<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.materialAudit.CfRefuseSuggestModifyDao">

    <sql id="tableName">
      cf_refuse_suggest_modify
    </sql>


    <select id="selectSuggestByDateTypes" resultType="com.shuidihuzhu.cf.model.crowdfunding.material.CfRefuseSuggestModify">
        SELECT *
        FROM
        <include refid="tableName"/>
        WHERE data_type
        IN
        <foreach collection="types" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectSuggestByUniqueIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.material.CfRefuseSuggestModify">
        SELECT *
        FROM
        <include refid="tableName"/>
        WHERE unique_id
        IN
        <foreach collection="uniqueIds" item="uniqueId" separator="," open="(" close=")">
            #{uniqueId}
        </foreach>
    </select>


</mapper>