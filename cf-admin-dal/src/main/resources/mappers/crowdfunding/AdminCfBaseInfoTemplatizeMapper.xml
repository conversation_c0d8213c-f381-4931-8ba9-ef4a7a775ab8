<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCfBaseInfoTemplatizeDao">

    <sql id="table_name">
        `cf_base_info_templatize`
    </sql>

    <sql id="insert_fields">
        `relation_type`,
        `content_type`,
        `channel_type`,
        `content`,
        `min_age`,
        `max_age`,
        `operator_id`,
        `operator_name`,
        `applicable_diseases`
    </sql>

    <sql id="select_fields">
        `id`,
        `relation_type`,
        `content_type`,
        `channel_type`,
        `content`,
        `min_age`,
        `max_age`,
        `operator_id`,
        `operator_name`,
        `applicable_diseases`,
        `create_time`,
        `update_time`
    </sql>

    <select id="selectByPage" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplatize">
        select <include refid="select_fields"/>
        from <include refid="table_name"/>
        <where>
            <if test="context!=null and context!=''">
                <bind name="content" value="'%' + context + '%'"/>
                AND `content` like #{content}
            </if>
            <if test="contentType!=0">
                AND `content_type`=#{contentType}
            </if>
            <if test="relationType!=0">
                AND `relation_type`=#{relationType}
            </if>
            <if test="channelType!=0">
                AND `channel_type`=#{channelType}
            </if>
            AND `is_delete`=0
            <if test="ids != null and ids.size() > 1">
            AND  id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            </if>

        </where>
        order by `id` desc
    </select>

    <select id="selectById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplatize">
        select <include refid="select_fields"/>
        from <include refid="table_name"/>
        where `id`=#{id} and `is_delete`=0
    </select>

    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplatize"  useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        values
        (#{relationType},#{contentType},#{channelType},#{content},#{minAge},#{maxAge},#{operatorId},#{operatorName},#{applicableDiseases})
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplatize">
        update <include refid="table_name"/>
        <set>
            `relation_type`=#{relationType}, `content_type`=#{contentType},
            `channel_type`=#{channelType}, `content`=#{content},
            `min_age`=#{minAge}, `max_age`=#{maxAge},
            `operator_id`=#{operatorId}, `operator_name`=#{operatorName}
            <if test="applicableDiseases != null">
                , `applicable_diseases`=#{applicableDiseases}
            </if>
        </set>
        where `id`=#{id}
    </update>

    <update id="delete">
        update <include refid="table_name"/>
        set `is_delete`=1
        where `id`=#{id}
    </update>

    <select id="get1v1TemplateCount" resultType="java.lang.Integer">
        select count(*) from
        <include refid="table_name"/>
        <where>
            `is_delete` = 0
            <if test="context!=null and context!=''">
                <bind name="content" value="'%' + context + '%'"/>
                AND `content` like #{content}
            </if>
            <if test="contentType!=0">
                AND `content_type` = #{contentType}
            </if>
            <if test="relationType != null and relationType!=0">
                AND `relation_type` = #{relationType}
            </if>
            <if test="channelType!=0">
                AND `channel_type` = #{channelType}
            </if>
            <if test = "diseaseName != null and diseaseName != ''">
                AND `applicable_diseases` like concat('%',#{diseaseName},'%')
            </if>
            <if test="age != null and age > -1">
                AND `min_age` &lt;= #{age} AND `max_age` &gt;= #{age}
            </if>
            <if test="ids != null and ids.size() > 1">
                AND id IN
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <select id="get1v1TemplateList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplatize">
        select <include refid="select_fields"/>
        from <include refid="table_name"/>
        <where>
            `is_delete` = 0
            <if test="context!=null and context!=''">
                <bind name="content" value="'%' + context + '%'"/>
                AND `content` like #{content}
            </if>
            <if test="contentType!=0">
                AND `content_type` = #{contentType}
            </if>
            <if test="relationType != null and relationType!=0">
                AND `relation_type` = #{relationType}
            </if>
            <if test="channelType!=0">
                AND `channel_type` = #{channelType}
            </if>
            <if test = "diseaseName != null and diseaseName != ''">
                AND `applicable_diseases` like concat('%',#{diseaseName},'%')
            </if>
            <if test="age != null and age > -1">
                AND `min_age` &lt;= #{age} AND `max_age` &gt;= #{age}
            </if>
            <if test="ids != null and ids.size() > 1">
                AND id IN
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        order by `id` desc
        limit #{offset},#{pageSize}
    </select>

</mapper>