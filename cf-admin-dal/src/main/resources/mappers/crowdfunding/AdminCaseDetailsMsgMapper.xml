<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCaseDetailsMsgDao">

    <sql id="TABLE">
        case_details_msg
    </sql>

    <sql id="Base_Field">
        `id` as id,
        `case_id` as caseId,
        `info_uuid` as infoUuid,
        `head_picture_url` as headPictureUrl,
        `carousel_text` as carouselText,
        `case_label` as caseLabel,
        `case_label_sort` as caseLabelSort,
        `show_tag` as showTag
    </sql>

    <insert id="addCaseDetailsMsg">
        INSERT INTO
        <include refid="TABLE"/>
        (`case_id`,`info_uuid`,`head_picture_url`,`carousel_text`,`case_label`,`case_label_sort`,`operate_id`)
        VALUES
        (#{caseId},#{infoUuid},#{headPictureUrl},#{carouselText},#{caseLabel},#{caseLabelSort},#{operateId})
    </insert>

    <update id="updateCaseDetailsMsg">
        UPDATE
        <include refid="TABLE"/>
        SET
        head_picture_url=#{headPictureUrl},
        carousel_text=#{carouselText},
        case_label=#{caseLabel},
        case_label_sort=#{caseLabelSort},
        operate_id=#{operateId}
        WHERE id=#{id}
    </update>

    <update id="updateHeadPictureUrl">
        UPDATE
        <include refid="TABLE"/>
        SET
        head_picture_url=#{headPictureUrl}
        WHERE case_id=#{caseId}
    </update>


    <select id="getByInfoUuid" resultType="com.shuidihuzhu.client.cf.admin.model.AdminCaseDetailsMsg">
        SELECT
        <include refid="Base_Field"/>
        FROM
        <include refid="TABLE"/>
        WHERE
        info_uuid =#{infoUuid}
        and is_delete = 0
    </select>

    <select id="getByCaseId" resultType="com.shuidihuzhu.client.cf.admin.model.AdminCaseDetailsMsg">
        SELECT
        <include refid="Base_Field"/>
        FROM
        <include refid="TABLE"/>
        WHERE
        case_id =#{caseId}
        and is_delete = 0
    </select>
    
    <update id="updateShowTag">
        update <include refid="TABLE"/>
        set show_tag = 1
        where case_id = #{caseId}
    </update>

    <insert id="insert" >
        INSERT INTO
        <include refid="TABLE"/>
        (`case_id`,`info_uuid`,`show_tag`)
        VALUES
        (#{caseId},#{infoUuid},#{showTag})
    </insert>

</mapper>
