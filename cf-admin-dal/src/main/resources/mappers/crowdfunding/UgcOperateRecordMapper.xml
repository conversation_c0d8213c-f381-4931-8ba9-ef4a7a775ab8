<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.IUgcOperateRecordDAO">

    <sql id="TableName">
        cf_ugc_operate_record
    </sql>

    <sql id="BaseFields">
        `id`,`case_id`,`biz_id`,`biz_type`,`operate_type`,`content`,`operator_id`,`operator`,`department`,`is_delete`,`create_time`,`update_time`
    </sql>

    <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.model.crowdfunding.AdminUgcOperateRecordDO">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="case_id" property="caseId" jdbcType="BIGINT"/>
        <result column="biz_id" property="bizId" jdbcType="BIGINT"/>
        <result column="biz_type" property="bizType" jdbcType="INTEGER"/>
        <result column="operate_type" property="operateType" jdbcType="INTEGER"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="operator_id" property="operatorId" jdbcType="INTEGER"/>
        <result column="operator" property="operator" jdbcType="VARCHAR"/>
        <result column="department" property="department" jdbcType="VARCHAR"/>
        <result column="is_delete" property="isDelete" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="query" resultMap="BaseResultMap">
        select <include refid="BaseFields"/>
        from <include refid="TableName"/>
        where case_id = #{caseId} and biz_type=#{bizType} and biz_id=#{bizId}
    </select>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.AdminUgcOperateRecordDO">
        insert into <include refid="TableName"/>
        (`case_id`,`biz_id`,`biz_type`,`operate_type`,`content`,`operator_id`,`operator`,`department`)
        values
        (#{caseId},#{bizId},#{bizType},#{operateType},#{content},#{operatorId},#{operator},#{department})
    </insert>
</mapper>