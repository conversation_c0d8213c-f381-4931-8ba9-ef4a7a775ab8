<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.NewAdminCfFundUseAuditDao">
    <sql id="TABLE_NAME">
        new_crowdfunding_fund_use_progress
    </sql>

    <sql id="select_fields">
        `id`,
        `user_id` as userId,
        `crowdfunding_id` as crowdfundingId,
        `title` as title,
        `fund_use_rejected_reason` as fundUseRejectedReason,
        `content` as content,
        `image_urls` as imageUrls
    </sql>


    <update id="updateAuditStatusRejected">
        UPDATE <include refid="TABLE_NAME"/>
        SET `fund_use_rejected_reason` = #{fundAuditRejectedReason},
        `status` = 2
        WHERE `id` = #{progressId}
    </update>

    <update id="updateAuditPass">
        UPDATE <include refid="TABLE_NAME"/>
        <set>
            `status` = 1
            <if test="comment != null and comment != ''">
                , `fund_use_rejected_reason` = #{comment}
            </if>
        </set>
        where `id` = #{progressId}
    </update>

    <select id="getAuditStatusRejectedReason" resultType="java.lang.String">
        SELECT `fund_use_rejected_reason`
        FROM <include refid="TABLE_NAME"/>
        WHERE `id` = #{progressId}
    </select>

    <select id="selectByProgressId" resultType="com.shuidihuzhu.cf.vo.crowdfunding.AdminCrowdfundingProgress">
        SELECT <include refid="select_fields"/>
        FROM <include refid="TABLE_NAME"/>
        WHERE `id` = #{progressId}
    </select>

    <select id="selectByProgressIdList" resultType="com.shuidihuzhu.cf.vo.crowdfunding.AdminCrowdfundingProgress">
        SELECT *
        FROM <include refid="TABLE_NAME" />
        WHERE id in
        <foreach collection="idList" open="(" separator="," close=")" item="id">#{id}</foreach>
    </select>

    <select id="getLastOneByCaseId" resultType="com.shuidihuzhu.cf.vo.crowdfunding.AdminCrowdfundingProgress">
        SELECT *
        FROM  <include refid="TABLE_NAME"/>
        WHERE `crowdfunding_id` = #{caseId}
        order by id desc
        limit 1
    </select>

    <select id="getFirstOneByCaseId" resultType="com.shuidihuzhu.cf.vo.crowdfunding.AdminCrowdfundingProgress">
        SELECT *
        FROM  <include refid="TABLE_NAME"/>
        WHERE `crowdfunding_id` = #{caseId}
        AND is_delete = 0
        order by id
        limit 1
    </select>

    <update id="updateContentAndImg">
        UPDATE <include refid="TABLE_NAME"/>
        <set>
            `content` = #{content}, `image_urls` = #{imageUrls}
        </set>
        where `id` = #{progressId}
    </update>


</mapper>