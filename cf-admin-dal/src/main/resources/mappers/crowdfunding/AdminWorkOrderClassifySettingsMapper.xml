<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminWorkOrderClassifySettingsDao">

    <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderClassifySettings">
        <id column="id" property="id"/>
        <result column="operator_id" property="operatorId" />
        <result column="all_text" property="allText"/>
        <result column="parent_id" property="parentId" />
        <result column="auto_trigger" property="autoTrigger" />
        <result column="is_delete" property="delete"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime" />
        <result column="available" property="available" />
        <result column="weight" property="weight" />
    </resultMap>

    <sql id="table_name">
        admin_work_order_classify_settings
    </sql>


    <sql id="insert_fields">
        `operator_id`, `all_text`, `parent_id`, `auto_trigger`,`is_delete`, `available`, `weight`
    </sql>

    <select id = "selectAllClassifySettingsByDelStatus" resultMap="BaseResultMap" >
        SELECT * FROM
        <include refid="table_name"/>
        <where>
            <if test = "deleteStatus != null">
                and is_delete = #{deleteStatus}
            </if>
        </where>
        ORDER BY id
    </select>

    <select id = "selectChildClassifySettings" resultMap="BaseResultMap" >
        SELECT * FROM
        <include refid="table_name"/>
        WHERE parent_id = #{parentId} AND is_delete = #{deleteStatus}
    </select>

    <insert id = "insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderClassifySettings" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES(#{operatorId}, #{allText}, #{parentId}, #{autoTrigger}, #{delete}, #{available}, #{weight})
    </insert>

    <update id ="updateDelStatusById" >
        UPDATE <include refid="table_name"/>
        SET is_delete = #{deleteStatus},
        operator_id = #{userId}
        WHERE
        id IN
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id = "selectClassifyByIdsAndDels" resultMap="BaseResultMap" >
        SELECT * FROM
        <include refid="table_name"/>
        WHERE
        id IN
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="delStatusList != null and delStatusList.size() > 0">
        AND is_delete IN
        <foreach collection="delStatusList" item="delStatus" open="(" separator="," close=")">
            #{delStatus}
        </foreach>
        </if>
        ORDER BY id
    </select>

    <select id = "selectClassifySettingsById" resultMap="BaseResultMap" >
        SELECT * FROM
        <include refid="table_name"/>
        WHERE id = #{id}
    </select>

    <select id = "selectClassifySettingsByText" resultMap="BaseResultMap" >
        SELECT * FROM
        <include refid="table_name"/>
        WHERE all_text = #{text}
        AND is_delete = 0
    </select>

    <select id = "selectMaxId" resultType="java.lang.Long" >
        SELECT max(`id`) FROM
        <include refid="table_name"/>
    </select>

    <update id = "changeAvailableStatus" >
        update <include refid="table_name"/> set
        `available` = #{available} where id = #{id}
    </update>

    <update id = "changeWeight" >
        update <include refid="table_name"/> set
        `weight` = #{weight} where id = #{id}
    </update>

</mapper>