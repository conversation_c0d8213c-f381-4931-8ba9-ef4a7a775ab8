<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfReportCommunicaterListDAO">
    <sql id="table_name">
        cf_report_communicater_list
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfReportCommunicaterDO">
        INSERT INTO
        <include refid="table_name"/>
        (`case_id`, `report_id`, `type`, `relation_key`, `relation_value`, `mobile`, `operator_id`,`is_manual_add`)
        VALUES
        (#{caseId}, #{reportId}, #{type}, #{relationKey}, #{relationValue}, #{mobile}, #{operatorId},#{isManualAdd})
    </insert>

    <select id="query" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportCommunicaterDO">
        SELECT *
        FROM <include refid="table_name"/>
        WHERE case_id = #{caseId} AND report_id = #{reportId} AND `type` = #{type}
        and is_delete = 0
    </select>

    <select id="queryById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportCommunicaterDO">
        SELECT *
        FROM <include refid="table_name"/>
        WHERE id = #{id} AND case_id = #{caseId} AND report_id = #{reportId}
        and is_delete = 0
    </select>

    <select id="queryByMobile" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportCommunicaterDO">
        SELECT *
        FROM <include refid="table_name"/>
        WHERE case_id = #{caseId} AND report_id = #{reportId} AND mobile = #{mobile}
        and is_delete = 0
    </select>

    <update id="updateIsDelete">
        update
        <include refid="table_name"/>
        set is_delete = 1
        WHERE id = #{id} AND case_id = #{caseId} AND report_id = #{reportId}
    </update>

</mapper>