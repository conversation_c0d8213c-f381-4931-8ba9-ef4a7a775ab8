<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfVerificationOperateRecordDao">

    <insert id="addCfVerificationOperateRecord">
        insert into cf_verification_operate_record(verification_id, content, operator_id)
        values (#{verificationId}, #{content}, #{operatorId});
    </insert>

    <select id="getCfVerificationOperateRecord" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfVerificationOperateRecord">
        SELECT content, operator_id, create_time,verification_id
        FROM cf_verification_operate_record
        WHERE verification_id = #{verificationId}
    </select>

</mapper>
