<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCasePageAnnouncementManageRecordDao">

    <sql id="TABLE">
        case_page_announcement_manage_record
    </sql>

    <sql id="Base_Field">
        `id`,
        `announcement_id` as announcementId,
        `comment`,
        `operator_id` as operatorId,
        `create_time` as createTime
    </sql>

    <insert id="add">
        INSERT INTO
        <include refid="TABLE"/>
        (`announcement_id`,`comment`,`operator_id`)
        VALUES
        (#{announcementId}, #{comment}, #{operatorId})
    </insert>

    <select id="getList" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminCasePageAnnouncementManageRecord">
        SELECT
        <include refid="Base_Field"/>
        FROM
        <include refid="TABLE"/>
        WHERE
        announcement_id = #{announcementId}
        and
        is_delete = 0
    </select>

</mapper>
