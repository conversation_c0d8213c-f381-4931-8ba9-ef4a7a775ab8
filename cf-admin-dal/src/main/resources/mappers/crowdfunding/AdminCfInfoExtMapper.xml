<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCfInfoExtDao">

	<sql id="TABLE">
        cf_info_ext
    </sql>

	<sql id="FIELDS">
		id as id,
		info_uuid as infoUuid,
		from_type as fromType,
		from_detail as fromDetail,
		self_tag as selfTag,
		product_name as productName,
		finish_status as finishStatus,
		refund_status as refundStatus,
		transfer_status as transferStatus,
		refund_end_time as refundEndTime,
		pay_type as payType,
		user_third_type as userThirdType,
		date_created as dateCreated,
		last_modified as lastModified,
		crypto_register_mobile as cryptoRegisterMobile,
		first_approve_status as firstApproveStatus,
		first_approve_time as firstApproveTime,
		volunteer_unique_code as volunteerUniqueCode,
		client_ip as clientIp,
		need_case_list as needCaseList,
		cf_version as cfVersion,
		pre_id as preId,
		bd_followed as bdFollowed,
		finish_str as finishStr,
		case_id as caseId,
		no_handling_fee as noHandlingFee,
		primary_channel as primaryChannel
	</sql>

	<update id="updateDealWithStatus">
		UPDATE <include refid="TABLE"/>
		SET deal_with_status=#{status}
		WHERE info_uuid=#{infoUuid}
	</update>

	<update id="updateUserRefund">
		UPDATE <include refid="TABLE"/>
		SET refund_end_time=#{refundEndTime}
		WHERE info_uuid=#{infoUuid}
	</update>

	<update id="updateCryptoRegisterMobile">
		UPDATE <include refid="TABLE"/>
		SET crypto_register_mobile=#{cryptoRegisterMobile}
		WHERE info_uuid=#{infoUuid}
	</update>

    <insert id="insertList" parameterType="java.util.List">
        INSERT INTO <include refid="TABLE"/>
        (`info_uuid`) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item})
        </foreach>
    </insert>

    <select id="selectByInfoUuidList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE `info_uuid` IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

	<update id="updateSuggestStop">
		UPDATE <include refid="TABLE"/>
		SET suggest_stop=#{suggestStop}
		WHERE info_uuid=#{infoUuid}
	</update>
	<update id="updateTransferStatusAndFinishStatus">
		update <include refid="TABLE"/>
		set finish_status= 3,transfer_status=0
		where info_uuid=#{infoUuid}
	</update>

	<select id="selectByInfoUuidFromMaster" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt">
		SELECT <include refid="FIELDS"/>
		FROM <include refid="TABLE"/>
		WHERE `info_uuid` = #{infoUuid}
	</select>

	<select id="getListByUuids" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE `info_uuid` IN
		<foreach collection="infoUuids" open="(" close=")" separator="," item="infoUuid">
			#{infoUuid}
		</foreach>
	</select>

	<select id="getByInfoUuid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE
		`info_uuid` = #{infoUuid}
		LIMIT 1
	</select>

	<update id="updateFromType">
		UPDATE <include refid="TABLE"/>
		SET from_type=#{fromType}
		WHERE info_uuid=#{infoUuid}
		LIMIT 1
	</update>

	<update id="updateFirstApproveStatus">
		UPDATE <include refid="TABLE"/>
		SET first_approve_status = #{firstApproveStatus}
		<if test = "time != null">
			, `first_approve_time` = #{time}
		</if>
		WHERE info_uuid=#{infoUuid}

	</update>
	<update id="updateFirstApproveTime">
		UPDATE
		<include refid="TABLE"/>
		SET
		`first_approve_time`= now()
		WHERE
		`info_uuid`=#{infoUuid}
	</update>
	<update id="updateFinishStr">
		update <include refid="TABLE"/>
		set finish_str = #{finishStr}
		where info_uuid=#{caseUuid}
	</update>
	<select id="getByCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE
		`case_id` = #{caseId}
		LIMIT 1
	</select>

	<update id="updateFinishStatus">
		UPDATE
		<include refid="TABLE"/>
		SET
		`finish_status`=#{finishStatus}
		WHERE
		`info_uuid`=#{infoUuid}
	</update>

	<update id="updateFinishStatusByCaseId">
		UPDATE
		<include refid="TABLE"/>
		SET
		`finish_status`=#{finishStatus}
		WHERE
		`case_id`=#{caseId}
	</update>

	<select id="getListByCaseIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE `case_id` IN
		<foreach collection="caseIds" open="(" close=")" separator="," item="caseId">
			#{caseId}
		</foreach>
	</select>

	<update id="updateNoHandlingFeeByInfoUuid">
		UPDATE
		<include refid="TABLE"/>
		SET
		`no_handling_fee`=#{noHandlingFee}
		WHERE
		`info_uuid`=#{infoUuid}
	</update>

</mapper>
