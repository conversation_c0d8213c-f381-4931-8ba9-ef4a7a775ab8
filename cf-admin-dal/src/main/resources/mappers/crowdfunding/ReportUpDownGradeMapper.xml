<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.IReportUpDownGradeDAO">

    <sql id="TableName">
        cf_report_up_down_record
    </sql>

    <sql id="BaseFields">
        `id`,`case_id`,`work_order_id`,`type`,`target_user_id`,`target_user_name`,`operator`,`operator_id`,`create_time`,`update_time`
    </sql>

    <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.model.crowdfunding.ReportUpDownGradeRecord">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="case_id" property="caseId" jdbcType="INTEGER"/>
        <result column="work_order_id" property="workOrderId" jdbcType="INTEGER"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="target_user_id" property="targetUserId" jdbcType="INTEGER"/>
        <result column="target_user_name" property="targetUserName" jdbcType="VARCHAR"/>
        <result column="operator" property="operator" jdbcType="VARCHAR"/>
        <result column="operator_id" property="operatorId" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="queryByCaseId" resultMap="BaseResultMap">
        select <include refid="BaseFields"/>
        from <include refid="TableName"/>
        where case_id = #{caseId}
        order by id desc limit 1
    </select>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.ReportUpDownGradeRecord">
        insert into <include refid="TableName"/>
        (`case_id`,`work_order_id`,`type`,`target_user_id`,`target_user_name`,`operator`,`operator_id`)
        values
        (#{caseId},#{workOrderId},#{type},#{targetUserId},#{targetUserName},#{operator},#{operatorId})
    </insert>
</mapper>