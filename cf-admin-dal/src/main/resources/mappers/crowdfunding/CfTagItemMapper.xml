<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfTagItemDao">

    <sql id="table_name">
        cf_tag_item
    </sql>

    <sql id="insert_fields">
        `tag_name`
    </sql>

    <resultMap type="com.shuidihuzhu.cf.model.crowdfunding.CfTagItem" id="itemResultMap">
        <id property="id" column="id"/>
        <result property="tagName" column="tag_name"/>
        <result property="type" column="type"/>
        <result property="dateCreated" column="date_created"/>
        <result property="lastModified" column="last_modified"/>
        <association property="cfTagGroup" column="pid" javaType="com.shuidihuzhu.cf.model.crowdfunding.CfTagGroup">
            <id column="id" property="id"/>
            <result column="describe" property="describe"/>
            <result property="dateCreated" column="date_created"/>
            <result property="lastModified" column="last_modified"/>
        </association>
    </resultMap>

    <select id="getByPid" resultMap="itemResultMap">
        SELECT *
        FROM <include refid="table_name"/>
        WHERE `pid`=#{id}
    </select>

    <select id="getList" resultMap="itemResultMap">
        SELECT *
        FROM <include refid="table_name"/>
    </select>

    <insert id="addOtherTag" parameterType="java.lang.String">
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES <foreach collection="list" item="item" separator=",">(#{item})</foreach>
    </insert>

    <select id="getByTagName" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfTagItem">
        SELECT *
        FROM <include refid="table_name"/>
        WHERE `pid`=0 AND `tag_name` IN <foreach collection="others" item="item" open="(" separator="," close=")">#{item}</foreach>
    </select>

    <select id="getByInfoIdOther" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfTagItem">
        SELECT *
        FROM cf_tag_item ci LEFT JOIN cf_case_tag ct ON ci.id=ct.tag_id
        WHERE ci.pid=0 AND ct.info_id=#{infoId}
    </select>

    <select id="getByInfoId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfTagItem">
        SELECT *
        FROM cf_tag_item ci LEFT JOIN cf_case_tag ct ON ci.id=ct.tag_id
        WHERE ct.info_id=#{infoId}
    </select>

    <select id="selectAllZore" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfTagItem">
        SELECT *
        FROM <include refid="table_name"/>
        WHERE `pid`=0
    </select>

    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfTagItem" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES (#{tagName})
    </insert>

</mapper>