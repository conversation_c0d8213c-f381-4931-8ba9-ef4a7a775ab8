<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfSupplyProgressDao">

    <sql id="table_name">
        cf_info_supply_progress
    </sql>


    <sql id="insert_fields">
        `user_id`,
        `case_id`,
        `info_uuid`,
        `progress_action_id`,
        `content`,
        `img_urls`
    </sql>


    <sql id="select_fields">
        `id`,
        `user_id` as userId,
        `case_id` as caseId,
        `info_uuid` as infoUUId,
        `progress_action_id` as progressActionId,
        `progress_id` as progressId,
        `content` as content,
        `img_urls` as imgUrls,
        `content_status` as contentStatus,
        `img_status` as imgStatus,
        `create_time` as createTime
    </sql>

    <insert id="add" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES
        (#{userId}, #{caseId}, #{infoUUId}, #{progressActionId}, #{content}, #{imgUrls})
    </insert>


    <update id="reject" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyProgress"
            useGeneratedKeys="true" keyProperty="id">
        update
        <include refid="table_name"/>
        <set>
            <if test="contentStatus > 0">
                `content_status`= #{contentStatus},
            </if>
            <if test="imgStatus != null">
                `img_status`= #{imgStatus},
            </if>
            <if test="imgUrls != null">
                `img_urls`= #{imgUrls}
            </if>
        </set>
        WHERE `id`= #{id}
    </update>


    <update id="pass">
        update
        <include refid="table_name"/>
        <set>
            `content_status`= 1,
            `img_status`= 1,
            `progress_id` = #{progressId},
            <if test="imgUrls != null">
                `img_urls`= #{imgUrls}
            </if>
        </set>
        WHERE `id`= #{id}
    </update>


    <update id="reprocess">
        update
        <include refid="table_name"/>
        <set>
            `content_status`= 0,
            `img_status`= 0
        </set>
        WHERE `id`= #{id}
    </update>


    <select id="listBySupplyActionId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyProgress">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE `progress_action_id` = #{actionId}
    </select>


    <select id="listByIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyProgress">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE `id` IN
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <select id="listByActionIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyProgress">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE `progress_action_id`
        <foreach collection="actionIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getByActionId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyProgress">
        SELECT *
        FROM
        <include refid="table_name"/>
        WHERE `progress_action_id` = #{progressActionId}
    </select>

</mapper>