<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCasePageAnnouncementManageDao">

    <sql id="TABLE">
        case_page_announcement_manage
    </sql>

    <sql id="Base_Field">
        `id`,
        `type`,
        `top`,
        `img_url` as imgUrl,
        `pop_img_url` as popImgUrl,
        `status`,
        `title`,
        `shortcut_url` as shortcutUrl,
        `shortcut_url_desc` as shortcutUrlDesc,
        `create_time` as createTime
    </sql>

    <insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.AdminCasePageAnnouncementManage"
            keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="TABLE"/>
        (`type`,`top`,`img_url`,`pop_img_url`,`status`,`title`,`shortcut_url`,`shortcut_url_desc`)
        VALUES
        (#{type}, #{top}, #{imgUrl},#{popImgUrl}, #{status}, #{title}, #{shortcutUrl}, #{shortcutUrlDesc})
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.cf.model.crowdfunding.AdminCasePageAnnouncementManage">
        UPDATE
        <include refid="TABLE"/>
        SET
        `type`=#{type},
        `top`=#{top},
        `img_url`=#{imgUrl},
        `pop_img_url`=#{popImgUrl},
        `shortcut_url`=#{shortcutUrl},
        `shortcut_url_desc`=#{shortcutUrlDesc},
        `status`=#{status},
        `title`=#{title}
        WHERE id=#{id}
    </update>

    <update id="updateByOnline">
        UPDATE
        <include refid="TABLE"/>
        SET
        `status`=#{status}
        WHERE id=#{id}
    </update>

    <update id="updateByTop">
        UPDATE
        <include refid="TABLE"/>
        SET
        `top`=#{top}
        WHERE id=#{id}
    </update>

    <update id="delete">
        UPDATE
        <include refid="TABLE"/>
        SET
        is_delete = 1
        WHERE id=#{id}
    </update>

    <select id="getList" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminCasePageAnnouncementManageVo">
        SELECT
        <include refid="Base_Field"/>
        FROM
        <include refid="TABLE"/>
        <where>
            is_delete = 0
            <if test="title != null and title != ''">
                AND title=#{title}
            </if>
            <if test="status!=-1">
                AND status=#{status}
            </if>
            <if test="type!=-1">
                AND type=#{type}
            </if>
            <if test="top!=-1">
                AND top=#{top}
            </if>
        </where>
        order by id desc
    </select>

</mapper>
