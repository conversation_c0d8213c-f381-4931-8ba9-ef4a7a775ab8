<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminWorkOrderCaseRecordDao">
    
    <sql id="table_name">
        `admin_work_order_case_record`
    </sql>

    <sql id="insert_fields">
        `order_case_id`,
        `work_order_id`,
        `case_id`,
        `status`,
        `type`,
        `operator_id`,
        `create_time`,
        `approve_result`
    </sql>
    
    <sql id="select_fields">
        `id`,
        `order_case_id`,
        `work_order_id`,
        `case_id`,
        `status`,
        `type`,
        `operator_id`,
        `create_time`,
        `update_time`,
        `approve_result`
    </sql>

    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderCaseRecord">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES (#{orderCaseId},#{workOrderId},#{caseId},#{status},#{type},#{operatorId},#{createTime}, #{approveResult})
    </insert>

    <insert id="insertList" parameterType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderCaseRecord">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.orderCaseId},#{item.workOrderId},#{item.caseId},#{item.status},
            #{item.type},#{item.operatorId},#{item.createTime}, #{item.approveResult})
        </foreach>
    </insert>

    <select id="selectByWorkOrderId" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderCaseRecord">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `work_order_id`=#{workOrderId}
    </select>
    
</mapper>