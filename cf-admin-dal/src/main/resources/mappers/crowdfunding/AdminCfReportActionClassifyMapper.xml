<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCfReportActionClassifyDao">

	<sql id="tableName">
        cf_report_action_classify
    </sql>


	<insert id="add">
		insert into <include refid="tableName"/>
		(`action_classify`)
		values(#{actionClassify})
	</insert>

	<update id="updateActionClassify">
		update <include refid="tableName"/>
		set action_classify = #{actionClassify}
		where id = #{id}
	</update>

	<update id="updateIsUse">
		update <include refid="tableName"/>
		set is_use = #{isUse}
		where id = #{id}
	</update>

	<select id="getAll" resultType="com.shuidihuzhu.cf.model.report.CfReportActionClassify">
		select * from <include refid="tableName"/>
		where  is_delete = 0
	</select>
	
	
	<select id="getByIds" resultType="com.shuidihuzhu.cf.model.report.CfReportActionClassify">
       select * from <include refid="tableName"/>
       where id in
		<foreach collection="ids" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
		and is_delete = 0
	</select>

	<select id="getByIdsAndIsUse" resultType="com.shuidihuzhu.cf.model.report.CfReportActionClassify">
		select * from <include refid="tableName"/>
		where id in
		<foreach collection="ids" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
		and is_use = #{isUse}
		and is_delete = 0
	</select>

	<select id="getByUse" resultType="com.shuidihuzhu.cf.model.report.CfReportActionClassify">
		select * from
		<include refid="tableName"/>
		where is_use = #{isUse} and is_delete = 0
	</select>

    <select id="getById" resultType="com.shuidihuzhu.cf.model.report.CfReportActionClassify">
        select * from
        <include refid="tableName"/>
        where id = #{id}
        and is_delete = 0
    </select>

</mapper>
