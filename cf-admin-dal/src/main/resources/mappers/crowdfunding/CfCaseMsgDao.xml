<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfCaseMsgDao">
    <sql id="tableName">
        cf_case_msg
    </sql>


    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfCaseMsg" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tableName"/>
        (case_id,msg_id,msg_value)
        VALUES (#{caseId},#{msgId},#{msgValue})
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfCaseMsg">
        UPDATE <include refid="tableName"/>
        SET
        `msg_value`=#{msgValue}
        WHERE `case_id` = #{caseId} and msg_id = #{msgId}
    </update>

    <select id="queryByCaeId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfCaseMsg">
        select *
        from <include refid="tableName"/>
        where case_id = #{caseId} and msg_id = #{msgId}
        order by id desc limit 1;
    </select>

</mapper>
