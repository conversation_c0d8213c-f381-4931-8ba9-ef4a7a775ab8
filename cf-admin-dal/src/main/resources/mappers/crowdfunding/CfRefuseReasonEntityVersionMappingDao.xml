<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfRefuseReasonEntityVersionMappingDao">
    <sql id="table">
        `cf_refuse_reason_entity_version_mapping`
    </sql>
    <insert id="insertOne">
        insert into <include refid="table"/>
            (`material_plan_id`, `refuse_reason_entity_id`)
        values(#{planId}, #{entityId})
    </insert>
    <insert id="batchInsert">
        insert into <include refid="table"/>
        (`refuse_reason_entity_id`,`material_plan_id`)
        values
        <foreach collection="mapping.entrySet()" index="key" item="value" separator=",">
            (#{key},#{value})
        </foreach>
    </insert>
    <update id="updateOne">
        update <include refid="table"/>
        set `material_plan_id` = #{planId}, `refuse_reason_entity_id` = #{entityId}
        where id = #{id}
    </update>
    <delete id="deleteOne">
        update <include refid="table"/>
        set `is_delete` = 1
        where id = #{id}
    </delete>
    <select id="getEntityIdsByMaterialPlanId" resultType="java.lang.Integer">
        select `refuse_reason_entity_id` from
        <include refid="table"/>
        where `is_delete` = 0 and `material_plan_id` = #{planId}
    </select>
    <select id="getMaterialPlanId" resultType="java.util.HashMap">
        select `material_plan_id`, `refuse_reason_entity_id`
        from <include refid="table"/>
        where `is_delete` = 0 and `refuse_reason_entity_id` in
        <foreach collection="entityIds" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </select>
</mapper>