<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfReportCallRecordDAO">
    <sql id="table_name">
        cf_report_call_record
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfReportCallRecordDO">
        INSERT INTO <include refid="table_name"/>
        (`case_id`, `report_id`, `add_id`, `mobile`, `unique_id`,`operator_id`)
        VALUES
        (#{caseId}, #{reportId}, #{addId}, #{mobile}, #{uniqueId}, #{operatorId})
    </insert>

    <select id="query" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportCallRecordDO">
        SELECT *
        FROM <include refid="table_name"/>
        WHERE case_id = #{caseId} AND report_id = #{reportId} AND add_id = #{addId}
    </select>
</mapper>