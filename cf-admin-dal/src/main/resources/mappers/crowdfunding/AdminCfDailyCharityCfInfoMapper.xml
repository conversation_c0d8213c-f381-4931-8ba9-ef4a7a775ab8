<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCfDailyCharityCfInfoDao">
	<resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.model.crowdfunding.CfDailyCharityCfInfo">
		<constructor>
			<idArg column="id" jdbcType="INTEGER" javaType="java.lang.Integer"/>
			<arg column="case_id" jdbcType="INTEGER" javaType="java.lang.Integer"/>
			<arg column="dt" jdbcType="DATE" javaType="java.util.Date"/>
			<arg column="order_num" jdbcType="INTEGER" javaType="java.lang.Integer"/>
			<arg column="create_time" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
			<arg column="last_modified" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
			<arg column="case_type" jdbcType="INTEGER" javaType="java.lang.Integer"/>
			<arg column="valid" jdbcType="BIT" javaType="java.lang.Boolean"/>
			<arg column="tag" jdbcType="VARCHAR" javaType="java.lang.String"/>

		</constructor>
	</resultMap>
	<update id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    update cf_daily_charity_cf_info
    set valid = 0
    where id = #{id,jdbcType=INTEGER}
  </update>
	<insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfDailyCharityCfInfo">
		<selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER">
			SELECT LAST_INSERT_ID()
		</selectKey>
		insert into cf_daily_charity_cf_info (case_id, dt, order_num,
		create_time, last_modified, case_type,
		valid,tag)
		values (#{caseId,jdbcType=INTEGER}, #{dt,jdbcType=DATE}, #{orderNum,jdbcType=INTEGER},
		#{createTime,jdbcType=TIMESTAMP}, #{lastModified,jdbcType=TIMESTAMP}, #{caseType,jdbcType=INTEGER},
		#{valid,jdbcType=BIT},#{tag})
	</insert>
	<update id="updateByPrimaryKey" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfDailyCharityCfInfo">
    update cf_daily_charity_cf_info
    set case_id = #{caseId,jdbcType=INTEGER},
      dt = #{dt,jdbcType=DATE},
      order_num = #{orderNum,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      last_modified = #{lastModified,jdbcType=TIMESTAMP},
      case_type = #{caseType,jdbcType=INTEGER},
      valid = #{valid,jdbcType=BIT},
      tag = #{tag}
    where id = #{id,jdbcType=INTEGER}
  </update>
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
    select id, case_id, dt, order_num, create_time, last_modified, case_type, valid,tag
    from cf_daily_charity_cf_info
    where id = #{id,jdbcType=INTEGER}
  </select>
	<select id="selectByDt" resultMap="BaseResultMap">
    select id, case_id, dt, order_num, create_time, last_modified, case_type, valid,tag
    from cf_daily_charity_cf_info
    WHERE dt = #{dt} AND valid = 1 order by order_num limit #{limit}
  </select>
	<select id="selectByPage" parameterType="com.shuidihuzhu.common.web.util.admin.BasicExample" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfDailyCharityCfInfo">
		SELECT id, case_id, dt, order_num, create_time, last_modified, case_type, valid,tag FROM cf_daily_charity_cf_info
		<include refid="com.shuidihuzhu.cf.dao.message.Example_Where_Clause"/>
	</select>
</mapper>
