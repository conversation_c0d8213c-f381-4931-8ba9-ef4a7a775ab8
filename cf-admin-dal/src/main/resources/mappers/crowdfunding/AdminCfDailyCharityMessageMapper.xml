<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCfDailyCharityMessageDao">
	<resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.model.crowdfunding.CfDailyCharityMessage">
		<constructor>
			<idArg column="id" jdbcType="INTEGER" javaType="java.lang.Integer"/>
			<arg column="start_date" jdbcType="DATE" javaType="java.util.Date"/>
			<arg column="title" jdbcType="VARCHAR" javaType="java.lang.String"/>
			<arg column="content" jdbcType="VARCHAR" javaType="java.lang.String"/>
			<arg column="url" jdbcType="VARCHAR" javaType="java.lang.String"/>
			<arg column="create_time" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
			<arg column="last_modified" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
			<arg column="valid" jdbcType="BIT" javaType="java.lang.Boolean"/>
			<arg column="pic_url" jdbcType="VARCHAR" javaType="java.lang.String"/>
		</constructor>
	</resultMap>
	<update id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    update  cf_daily_charity_message
    set valid = 0
    where id = #{id,jdbcType=INTEGER}
    </update>
	<insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfDailyCharityMessage">
		<selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER">
			SELECT LAST_INSERT_ID()
		</selectKey>
		insert into cf_daily_charity_message
        (start_date, title, content,
		url,pic_url
        )
		values (#{startDate}, #{title}, #{content},
		#{url},
        <if test="picUrl != null">
            #{picUrl}
        </if>
        <if test="picUrl == null">
            ''
        </if>
        )
	</insert>
	<update id="updateByPrimaryKey" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfDailyCharityMessage">
    update cf_daily_charity_message
    set start_date = #{startDate},
      title = #{title},
      content = #{content},
      url = #{url},
      valid = #{valid},
      pic_url = #{picUrl}
    where id = #{id}
    </update>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select id, start_date, title, content, url, create_time, last_modified, valid,pic_url
        from cf_daily_charity_message
        where id = #{id}
    </select>
	<select id="selectByStartDate" resultMap="BaseResultMap">
    select id, start_date, title, content, url, create_time, last_modified, valid,pic_url
    from cf_daily_charity_message
    WHERE start_date = #{startDate} and valid = 1
    </select>
    <select id="selectByPage" parameterType="com.shuidihuzhu.common.web.util.admin.BasicExample" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfDailyCharityMessage">
        SELECT * FROM cf_daily_charity_message
        <include refid="com.shuidihuzhu.cf.dao.message.Example_Where_Clause"/>
    </select>
</mapper>
