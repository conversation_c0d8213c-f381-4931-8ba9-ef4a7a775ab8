<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfCallOutConditionDao">

    <sql id="tableName">
        `cf_call_out_condition`
    </sql>

    <sql id="insertFileds">
        `info_uuid`,
        `status`,
        `operator`,
        `comment`
    </sql>

    <insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfCallOutCondition">
        INSERT INTO <include refid="tableName"/>
          (<include refid="insertFileds"/>)
        VALUES (
          #{infoUuid},#{status},#{operator},#{comment}
        )
    </insert>

</mapper>