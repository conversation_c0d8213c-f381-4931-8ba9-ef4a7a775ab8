<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfSendProveTemplateDao">

    <sql id="tableName">
        cf_send_prove_template
    </sql>

    <sql id="selectFields">
        id,
        case_id,
        prove_id,
        action_id,
        template_id,
        title,
        content,
        commitment_content,
        audit_status
    </sql>

    <select id="findByCaseIdAndAuditStatus" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfSendProveTemplate">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        where case_id = #{caseId}
        and audit_status in
        <foreach collection="auditStatus" open="(" close=")" item="status" separator=",">
            #{status}
        </foreach>
        and is_delete = 0
    </select>

    <insert id="batchInsert">
        insert into
        <include refid="tableName"/>
        (case_id,prove_id,action_id,template_id,title,content,commitment_content,audit_status)
        values
        <foreach collection="cfSendProveTemplates" item="cfSendProveTemplate" separator=",">
            (#{cfSendProveTemplate.caseId},#{cfSendProveTemplate.proveId},#{cfSendProveTemplate.actionId},#{cfSendProveTemplate.templateId},
            #{cfSendProveTemplate.title},#{cfSendProveTemplate.content},#{cfSendProveTemplate.commitmentContent},#{cfSendProveTemplate.auditStatus})
        </foreach>
    </insert>
    <select id="findByCaseIdAndProveId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfSendProveTemplate">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        where case_id = #{caseId}
        and prove_id = #{proveId}
        and is_delete = 0
    </select>

    <update id="updateAuditStatus">
        update
        <include refid="tableName"/>
        set audit_status = #{auditStatus}
        where case_id = #{caseId}
        and prove_id = #{proveId}
        and template_id = #{templateId}
        and is_delete = 0
    </update>

    <update id="updateById">
        update
        <include refid="tableName"/>
        set content = #{content}
    </update>

    <select id="findByCaseIdAndProveIdAndTemplateId"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.CfSendProveTemplate">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        where case_id = #{caseId}
        and prove_id = #{proveId}
        and action_id = #{actionId}
        and template_id = #{templateId}
        and is_delete = 0
        limit 1
    </select>

    <insert id="insertOne">
        insert into
        <include refid="tableName"/>
        (case_id,prove_id,action_id,template_id,title,content,commitment_content,audit_status)
        values
        (#{caseId},#{proveId},#{actionId},#{templateId},#{title},#{content},#{commitmentContent},#{auditStatus})
    </insert>

    <update id="updateAuditStatusAndContent">
        update
        <include refid="tableName"/>
        set audit_status = #{auditStatus},content = #{content}
        where case_id = #{caseId}
        and prove_id = #{proveId}
        and template_id = #{templateId}
        and is_delete = 0
    </update>
    <update id="updateAllAuditStatus">
        update
        <include refid="tableName"/>
        set audit_status = #{auditStatus}
        where case_id = #{caseId}
        and prove_id = #{proveId}
        and is_delete = 0
    </update>

</mapper>
