<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCfCaseSceneDao">

    <sql id="table_name">
        cf_case_scene
    </sql>

    <sql id="fields">
        `id` as id,
        `info_uuid` as infoUuid,
        `scene_id` as sceneId,
        `scene_name` as sceneName,
        `description` as description
    </sql>

    <insert id="insertOrUpdate" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfCaseScene">
        INSERT INTO
        <include refid="table_name"/>
        (`info_uuid`, `scene_id`, `scene_name`, `description`)
        VALUES
        (#{infoUuid}, #{sceneId}, #{sceneName}, #{description})
        ON DUPLICATE KEY UPDATE
        `scene_id`=#{sceneId},
        `scene_name`=#{sceneName},
        `description`=#{description}
    </insert>

    <select id="getByInfoUuid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfCaseScene">
        SELECT
        <include refid="fields"/>
        FROM
        <include refid="table_name"/>
        WHERE
        `info_uuid`=#{infoUuid}
        LIMIT 1
    </select>

</mapper>