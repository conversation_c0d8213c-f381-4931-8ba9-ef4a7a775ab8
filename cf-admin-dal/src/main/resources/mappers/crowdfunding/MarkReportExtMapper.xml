<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.MarkReportExtDAO">
    <sql id="table_name">
        cf_mark_report_ext
    </sql>

    <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.model.crowdfunding.AdminMarkReportExtDO">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="case_id" property="caseId" jdbcType="INTEGER"/>
        <result column="report_id" property="reportId" jdbcType="INTEGER"/>
        <result column="reporter_name" property="reporterName" jdbcType="VARCHAR"/>
        <result column="marker_user_id" property="markerUserId" jdbcType="VARCHAR"/>
        <result column="marker_name" property="markerName" jdbcType="VARCHAR"/>
        <result column="marker_org" property="markerOrg" jdbcType="VARCHAR"/>
        <result column="encrypt_reporter_mobile" property="encryptReporterMobile" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="select_field">
        id, case_id, report_id, reporter_name, marker_user_id, marker_name, marker_org,encrypt_reporter_mobile
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.AdminMarkReportExtDO">
		insert into <include refid="table_name"/>
        (`case_id`, `report_id`, `reporter_name`, `marker_user_id`, `marker_name`, `marker_org`,encrypt_reporter_mobile)
		values(#{caseId},#{reportId},#{reporterName},#{markerUserId},#{markerName},#{markerOrg},#{encryptReporterMobile});
	</insert>

    <select id="queryByReportId" resultMap="BaseResultMap">
    select <include refid="select_field"/>
    from <include refid="table_name"/>
    where report_id = #{reportId}
</select>
</mapper>
