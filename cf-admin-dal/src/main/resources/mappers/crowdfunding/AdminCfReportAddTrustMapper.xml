<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCfReportAddTrustDao">
    <sql id="tableName">
        `cf_report_add_trust`
    </sql>

    <sql id="insertFields">
        (`info_uuid`,
        `audit_status`,
        `operator_content`,
        `content`,
        `image_urls`,
        `issued_commitment`)
    </sql>

    <sql id="selectFields">
        `id`,
        `info_uuid`,
        `audit_status`,
        `operator_content`,
        `content`,
        `image_urls`,
        `create_time`,
        `update_time`,
        `issued_commitment`
    </sql>

    <select id="getListByInfoUuid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportAddTrust">
        SELECT <include refid="selectFields"/>
        FROM (SELECT <include refid="selectFields"/> FROM <include refid="tableName"/>
        WHERE `is_delete` = 0
        AND `info_uuid` IN
        <foreach collection="infoUuids" item="infoUuid" open="(" close=")" separator=",">
            #{infoUuid}
        </foreach>
        ORDER BY id desc) add_trust
        GROUP BY `info_uuid`
    </select>
    <insert id="save" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfReportAddTrust" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="tableName"/>
        <include refid="insertFields"/>
        VALUES (#{infoUuid},#{auditStatus},#{operatorContent},#{content},#{imageUrls},#{issuedCommitment})
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfReportAddTrust">
        UPDATE <include refid="tableName"/>
        SET
        `audit_status`=#{auditStatus},
        `operator_content`=#{operatorContent},
        `content`=#{content},
        `image_urls`=#{imageUrls}
        WHERE `id` = #{id}
    </update>

    <select id="getNewestByInfoUuid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportAddTrust">
        SELECT <include refid="selectFields"/>
        from <include refid="tableName"/>
        where `is_delete` = 0
        AND `info_uuid` = #{infoUuid}
        ORDER BY id desc limit 1
    </select>

    <select id="queryById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportAddTrust">
        SELECT *
        from <include refid="tableName"/>
        where id = #{id}
        and is_delete = 0
    </select>


    <update id="delete">
        UPDATE <include refid="tableName"/>
        SET `is_delete` = 1
        WHERE `info_uuid` = #{infoUuid}
    </update>

    <select id="getByBatch" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportAddTrust">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        where
        <if test="id > 0">
            id > #{id} and
        </if>
        is_delete = 0
        order by id
        limit #{size}
    </select>

    <select id="getTrustById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportAddTrust">
        SELECT <include refid="selectFields"/>
        FROM <include refid="tableName"/>
        WHERE id = #{id}
    </select>

</mapper>
