<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCredibleInfoDAO">

    <sql id="table_name">
        `cf_report_credible_info`
    </sql>

    <sql id="insert_fields">
        case_id,sub_id,mobile,`type`,audit_status,operator_id,send_time
    </sql>

    <select id="queryByCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfCredibleInfoDO">
        select *
        from <include refid="table_name"/>
        where `case_id`=#{caseId} and `is_delete`=0
    </select>

    <select id="queryBySubId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfCredibleInfoDO">
        select *
        from
        <include refid="table_name"/>
        where `sub_id`=#{subId}
        and type = #{type}
        and `is_delete`=0
    </select>

    <select id="queryById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfCredibleInfoDO">
        select *
        from <include refid="table_name"/>
        where `id`=#{id} and `is_delete`=0
    </select>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfCredibleInfoDO">
        insert into <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        values
        (#{caseId},#{subId},#{mobile},#{type},#{auditStatus},#{operatorId},#{sendTime})
    </insert>

    <update id="updateAuditInfo">
        update <include refid="table_name"/>
        set audit_status = #{auditStatus}, audit_time = now()
        where sub_id = #{subId}
        and type = #{type}
    </update>

    <update id="updateSubmitInfo">
        update <include refid="table_name"/>
        set audit_status = #{auditStatus}, submit_time = now()
        where sub_id = #{subId}
        and type = #{type}
    </update>

    <update id="delete">
        update <include refid="table_name"/>
        set is_delete = 1
        where sub_id = #{subId}
        and type = #{type}
    </update>

    <select id="findBySubIdAndType" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfCredibleInfoDO">
        select * from
        <include refid="table_name"/>
        where sub_id = #{subId}
        and type = #{type}
        and is_delete = 0
    </select>

    <update id="updateAuditStatusById">
        update
        <include refid="table_name"/>
        set audit_status = #{auditStatus}
        where id = #{id}
    </update>
    <select id="getLastOneByCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfCredibleInfoDO">
        select * from
        <include refid="table_name"/>
        where case_id = #{caseId}
        and type = #{type}
        and is_delete = 0
        order by id desc limit 1
    </select>

    <select id="getListByOperatorId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfCredibleInfoDO">
        select * from
        <include refid="table_name"/>
        where operator_id = #{operatorId}
        and type = #{type}
        and to_days(submit_time) = to_days(now())
        and is_delete = 0
    </select>

</mapper>