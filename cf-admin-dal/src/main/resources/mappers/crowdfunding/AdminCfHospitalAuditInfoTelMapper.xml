<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCfHospitalAuditInfoTelDao">
    <sql id="tableName">
        `cf_info_hospital_audit_tel`
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfHospitalAuditInfoTel">
        INSERT INTO <include refid="tableName" />
            (`cf_hospital_audit_info_id`,`area_code`,`tel_num`,`ext_num`)
        VALUES
        <foreach collection="cfHospitalAuditInfoTels" item="item"  separator=",">
            (#{cfHospitalAuditInfoId}, #{areaCode}, #{telNum}, #{extNum})
        </foreach>
    </insert>
    <update id="update">
        update <include refid="tableName" />
        set `area_code`=#{areaCode},`tel_num`=#{telNum},`ext_num`=#{extNum},`risk_level`=#{riskLevel},`risk_msg`=#{riskMsg}
        where `id`=#{id}
    </update>
    <update id="updateRisk">
        update <include refid="tableName" />
        set `risk_level`=#{riskLevel},`risk_msg`=#{riskMsg}
        where `id`=#{id}
    </update>

    <select id="getByCfHospitalAuditInfoId"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.CfHospitalAuditInfoTel">
        select *
        from <include refid="tableName" />
        where `cf_hospital_audit_info_id`=#{cfHospitalAuditInfoId} and `is_delete`=0
    </select>

    <select id="getById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfHospitalAuditInfoTel">
        select *
        from <include refid="tableName" />
        where `id`=#{id}
    </select>

</mapper>