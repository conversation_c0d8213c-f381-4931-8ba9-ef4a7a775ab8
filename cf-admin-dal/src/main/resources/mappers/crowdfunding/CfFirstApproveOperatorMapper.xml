<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfFirstApproveOperatorDao">

    <sql id="table_name">
        `cf_first_approve_oporator`
    </sql>

    <sql id="insert_fields">
        `operator_id`,
        `count`
    </sql>

    <insert id="insertCfFirstApproveOperator">
        insert into <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        values(#{operatorId}, #{count})
    </insert>

    <update id="updateCfFirstApproveOperatorCount">
        update <include refid="table_name"/>
        set count=#{count}
        where operator_id=#{operatorId}
    </update>

    <select id="getCfFirstApproveOperatorCountById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfFirstApproveOperator">
        select *
        from <include refid="table_name"/>
        where operator_id=#{operatorId}
        and is_delete=0
    </select>


</mapper>