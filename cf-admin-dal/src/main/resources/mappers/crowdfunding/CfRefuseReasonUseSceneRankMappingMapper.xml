<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfRefuseReasonUseSceneRankMappingDao">
    <sql id="table_name">
        `cf_refuse_reason_use_scene_rank_mapping`
    </sql>

    <insert id = "addUseSceneRankMappingList">
        INSERT INTO <include refid="table_name"/>
        (reason_entity_id, use_scene, rank, is_delete)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.reasonEntityId}, #{item.useScene}, #{item.rank}, #{item.isDelete})
        </foreach>
    </insert>

    <select id = "selectByEntityId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity$CfRefuseReasonUseSceneRankMapping">
        SELECT *
        FROM <include refid="table_name"/>
        WHERE reason_entity_id = #{entityId}
        AND is_delete = 0
    </select>

    <update id = "updateDeleteOrRandByIds">
        UPDATE <include refid="table_name"/>
        <set>
            <if test="isDelete != null">
               is_delete = #{isDelete},
            </if>
            <if test="rank != null">
               rank = #{rank},
            </if>
        </set>
        WHERE id IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>

    <select id = "selectByEntityIdsAndUseScene" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity$CfRefuseReasonUseSceneRankMapping">
        SELECT *
        FROM <include refid="table_name"/>
        WHERE reason_entity_id IN
        <foreach collection="entityIds" item="entityId" separator="," open="(" close=")">
            #{entityId}
        </foreach>
        <if test = "useScene != null">
            AND use_scene = #{useScene}
        </if>
        AND is_delete = 0
    </select>
</mapper>