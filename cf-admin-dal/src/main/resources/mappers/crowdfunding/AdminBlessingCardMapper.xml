<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminBlessingCardDao">

    <sql id="TABLE">
        case_details_blessing_card
    </sql>

    <sql id="Base_Field">
        `id` as id,
        `blessing_card_image` as blessingCardImage,
        `blessing_card_text` as blessingCardText,
        `blessing_card_amount` as blessingCardAmount
    </sql>


    <insert id="addBlessingCard">
        INSERT INTO
        <include refid="TABLE"/>
        (`blessing_card_image`,`blessing_card_text`,`blessing_card_amount`,`operate_id`)
        VALUES
        (#{blessingCardImage}, #{blessingCardText}, #{blessingCardAmount}, #{operateId})
    </insert>

    <update id="updateBlessingCard">
        UPDATE
        <include refid="TABLE"/>
        SET
        blessing_card_image=#{blessingCardImage},
        blessing_card_text=#{blessingCardText},
        blessing_card_amount=#{blessingCardAmount},
        operate_id=#{operateId}
        WHERE id=#{id}
    </update>

    <update id="delBlessingCard">
        UPDATE
        <include refid="TABLE"/>
        SET
        is_delete = 1,
        operate_id=#{operateId}
        WHERE id=#{id}
    </update>

    <select id="getList" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.AdminBlessingCardVo">
        SELECT
        <include refid="Base_Field"/>
        FROM
        <include refid="TABLE"/>
        WHERE
        is_delete = 0
    </select>

    <select id="selectListByImageOrText" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.AdminBlessingCardVo">
        SELECT
        <include refid="Base_Field"/>
        FROM
        <include refid="TABLE"/>
        WHERE
        blessing_card_image=#{blessingCardImage} or
        blessing_card_text=#{blessingCardText} or
        blessing_card_amount=#{blessingCardAmount} and
        is_delete = 0
    </select>

    <select id="getIdByText" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.AdminBlessingCardVo">
        SELECT
        <include refid="Base_Field"/>
        FROM
        <include refid="TABLE"/>
        WHERE
        blessing_card_text=#{blessingCardText} and
        is_delete = 0
    </select>

</mapper>
