<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfPromoteBillDao">
    <sql id="TABLE_NAME">
        cf_promote_bill_info
    </sql>


    <insert id = "addResult" parameterType = "com.shuidihuzhu.cf.model.admin.vo.PromoteBillHandleParam">
        INSERT INTO
        <include refid="TABLE_NAME"/>
        (`case_id`, `work_order_id`, `call_out_result`, `user_fit_level`, `user_no_fit_reason_types`,
        `other_reason`, `comment`, `operate_id`, `handle_time`)
        VALUES (#{caseId}, #{workOrderId}, #{callOutResult}, #{userFitLevel}, #{userNoFitReasonTypes}, #{otherReason},#{comment}, #{operateId}, #{handleTime})
    </insert>


    <select id = "selectLastByWorkOrderId" resultType = "com.shuidihuzhu.cf.model.admin.vo.PromoteBillHandleParam">
        SELECT *
        FROM  <include refid="TABLE_NAME"/>
        WHERE work_order_id = #{workOrderId}
        AND is_delete = 0
        ORDER BY id DESC LIMIT 1
    </select>




</mapper>