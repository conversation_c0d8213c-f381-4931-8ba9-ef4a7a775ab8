<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfReportProblemRelationshipDao">

    <sql id="table_name">
        `cf_report_problem_relationship`
    </sql>

    <sql id="insert_fields">
        `problem_id`,
        `problem`,
        `next_problem_id`,
        `next_problem`,
        `content`
    </sql>

    <sql id="select_fields">
        `id`,
        `problem_id` as problemId,
        `problem` as problem,
        `next_problem_id` as nextProblemId,
        `next_problem` as nextProblem,
        `content` as content
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfReportProblemRelationship" useGeneratedKeys="true" keyProperty="id">
        INSERT <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES (#{problemId}, #{problem}, #{nextProblemId}, #{nextProblem}, #{content})
    </insert>



    <update id="bindRelationship">
        update <include refid="table_name"/>
        set `next_problem_id` = #{nextProblemId}, `next_problem` = #{nextProblem}
        where `problem_id` = #{problemId}
        <if test="content != null and content != ''">
            and `content` = #{content}
        </if>
        and `is_delete` = 0
    </update>


    <delete id="deleteRelationship">
        delete from <include refid="table_name"/>
        where `problem_id` = #{problemId}
        <if test="nextProblemIds != null and nextProblemIds.size() > 0">
            and `next_problem_id` in
            <foreach collection="nextProblemIds" item="nextProblemId" open="(" separator="," close=")">
                #{nextProblemId}
            </foreach>
        </if>
    </delete>


    <delete id="unBindRelationship">
        delete from <include refid="table_name"/>
        where `problem_id` = #{problemId}
        <if test="contents != null and contents.size() > 0">
            and `content` in
            <foreach collection="contents" item="content" open="(" separator="," close=")">
                #{content}
            </foreach>
        </if>
    </delete>

    <update id="changeProblemName">
        update <include refid="table_name"/>
        set `problem` = #{problemName}
        where `problem_id` = #{problemId}
    </update>

    <update id="changeNextProblemName">
        update <include refid="table_name"/>
        set `problem` = #{problemName}
        where `next_problem_id` = #{nextProblemId}
    </update>

    <select id="listByProblemIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportProblemRelationship">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        <where>
            `problem_id` in
            <foreach collection="problemIds" item="problemId" open="(" separator="," close=")">
                #{problemId}
            </foreach>
        </where>
        and `is_delete` = 0
    </select>

    <select id="obtainRelationProblemId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportProblemRelationship">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        <where>
            `problem_id` = #{problemId}
            <if test="content != null and content != ''">
                and `content` = #{content}
            </if>
            and `is_delete` = 0
        </where>
    </select>


    <select id="listByNextProblemIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportProblemRelationship">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        <where>
            `next_problem_id` in
            <foreach collection="nextProblemIds" item="problemId" open="(" separator="," close=")">
                #{problemId}
            </foreach>
            and `is_delete` = 0
        </where>
    </select>


    <select id="selectRelationIdsByProblemId" resultType="java.lang.Integer">
        SELECT id
        FROM <include refid="table_name"/>
        WHERE `problem_id` = #{problemId}
        <if test="contents != null and contents.size() > 0">
            and `content` in
            <foreach collection="contents" item="content" open="(" separator="," close=")">
                #{content}
            </foreach>
        </if>
    </select>


</mapper>