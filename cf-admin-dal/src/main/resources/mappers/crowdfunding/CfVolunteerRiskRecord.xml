<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfVolunteerRiskRecordDao">
    <sql id="tableName">
        cf_volunteer_risk_record
    </sql>


    <insert id="insert" parameterType="com.shuidihuzhu.cf.client.adminpure.model.initial.CfVolunteerRiskRecord">
        INSERT INTO <include refid="tableName" />
        (`case_id`,`use_type`,`risk_type`,`patient_name`,`patient_id_card`,`year_amount`)
        VALUES
        (#{caseId},#{useType},#{riskType},#{patientName},#{patientIdCard},#{yearAmount})
    </insert>

    <select id="getByCaseIdList" resultType="com.shuidihuzhu.cf.client.adminpure.model.initial.CfVolunteerRiskRecord">
        select * from <include refid="tableName" />
        where case_id IN
        <foreach collection="caseIdList" index="index" item="caseId" open="(" separator="," close=")">
            #{caseId}
        </foreach>
    </select>
    <select id="getByCaseId"
            resultType="com.shuidihuzhu.cf.client.adminpure.model.initial.CfVolunteerRiskRecord">
        select * from
        <include refid="tableName" />
        where case_id = #{caseId}
    </select>

</mapper>