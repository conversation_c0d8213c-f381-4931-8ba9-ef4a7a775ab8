<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfRefuseReasonTagDao">

    <sql id="table_name">
        `cf_refuse_reason_tag`
    </sql>

    <sql id="insert_fields">
        `describe`,
        `data_type`,
        `reason_ids`,
        `describe_c`,
        `data_step`
    </sql>

    <sql id="select_fields">
        `id`,
        `describe`,
        `data_type`,
        `reason_ids`,
        `data_step`
    </sql>

    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonTag" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES (#{describe},#{dataType}, #{reasonIds}, #{describe}, #{dataStep})
    </insert>

    <select id="selectAllWithUuid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonTag">
        SELECT
        <include refid="select_fields"/>
        FROM
        (SELECT
        `type`
        FROM
        `crowdfunding_info_status`
        WHERE
        `info_uuid` =#{infoUuid}) cis
        LEFT JOIN
        `cf_refuse_reason_tag` crrt ON cis.`type` = crrt.`data_type`
        WHERE
        crrt.id IS NOT NULL
        AND crrt.`is_delete`=0
        LIMIT #{start},#{size}
    </select>

    <select id="selectSortTagByDataType" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonTag">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `data_type`=#{dataType}
        <if test = "isDelete != null">
            AND `is_delete`= #{isDelete}
        </if>
        ORDER BY data_step
    </select>

    <select id="selectById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonTag">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `id`=#{id}
    </select>

    <select id="selectByTagIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonTag">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `is_delete`=0 AND `id` IN
        <foreach collection="set" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByTagId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonTag">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `is_delete`=0 AND `id`=#{id}
    </select>


    <update id="updateTagStateById">
        UPDATE <include refid="table_name"/>
        <set>
            <if test="reasonIds != null">
                `reason_ids`=#{reasonIds},
            </if>
            <if test="dataStep != null">
                `data_step` = #{dataStep},
            </if>
        </set>
        WHERE `id`=#{id}
    </update>


</mapper>

