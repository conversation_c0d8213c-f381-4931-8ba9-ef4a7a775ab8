<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfSendProveSnapshotDao">
    <sql id="tableName">
        cf_send_prove_snapshot
    </sql>

    <insert id="insertOne">
        insert into
        <include refid="tableName"/>
        (case_id,prove_id,prove_snapshot,audit_status)
        values
        (#{caseId},#{proveId},#{proveSnapshot},#{auditStatus})
    </insert>

    <select id="getLastOne" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfSendProveSnapshot">
        select * from
        <include refid="tableName"/>
        where case_id = #{caseId}
        and prove_id = #{proveId}
        and is_delete = 0
        order by id desc limit 1
    </select>

    <select id="getSnapshot" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfSendProveSnapshot">
        select * from
        <include refid="tableName"/>
        where case_id = #{caseId}
        and prove_id = #{proveId}
        and audit_status in
        <foreach collection="auditStatusList" open="(" close=")" item="auditStatus" separator=",">
            #{auditStatus}
        </foreach>
        and is_delete = 0
        order by id
    </select>


</mapper>
