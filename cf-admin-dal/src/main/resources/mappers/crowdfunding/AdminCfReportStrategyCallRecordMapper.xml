<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCfReportStrategyCallRecordDao">
    <sql id="tableName">
        `cf_report_strategy_call_record`
    </sql>

    <insert id="addInfo">
        insert into
        <include refid="tableName"/>
        (`case_id`, `work_order_id`, `all_questioner_module`, `all_fundraiser_module`, `un_known_fundraiser_module`,
        `last_current_role_module`, `name`, `organization`)
        values (#{caseId}, #{workOrderId},#{allQuestionModule},#{allFundraiserModule},#{unKnowFundraiserModule},
        #{lastCurrentRoleModule}, #{name}, #{organization})
    </insert>

</mapper>
