<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfRefuseReasonItemDao">

    <sql id="tableName">
        shuidi_crowdfunding.cf_refuse_reason_item
    </sql>

    <resultMap id="CfRefuseMap" type="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonItem" autoMapping="true">
        <id column="ci_id" property="id"/>
        <result column="ci_content" property="content"/>
        <result column="type" property="type"/>
        <result column="pro_type" property="proType"/>
        <collection property="cfRefuseReasons" ofType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReason" >
            <id property="id" column="cr_id"/>
            <result property="pid" column="pid"/>
            <result property="content" column="cr_content"/>
        </collection>
    </resultMap>

    <select id="getRefuseItem" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonItem">
        SELECT `id`,`content`,`type`
        FROM <include refid="tableName"/>
        ORDER BY `pro_type`,`type` ASC
    </select>

    <select id="selectSubGroupByType" resultMap="CfRefuseMap">
        SELECT
            ci.id AS ci_id,
            ci.content AS ci_content,
            ci.type,
            ci.pro_type,
            cr.id AS cr_id,
            cr.pid,
            cr.content AS cr_content
        FROM
            cf_refuse_reason_item ci
            LEFT JOIN
            cf_refuse_reason cr ON ci.id = cr.pid
        WHERE
            ci.id > 0
        ORDER BY ci.pro_type , ci.type;
    </select>

    <select id="getContentById" parameterType="int" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonItem">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE `id`=#{id}
    </select>

    <select id="selectByIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonItem">
        SELECT `id`,`content`,`type`
        FROM <include refid="tableName"/>
        WHERE `id` IN
        <foreach collection="set" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByType" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonItem">
        SELECT `id`,`content`,`type`, `group_rank`
        FROM <include refid="tableName"/>
        WHERE `type`=#{type}
    </select>

    <insert id="insertReasonItem" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonItem" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tableName"/>
        (`content`, `type`, `group_rank`, `pro_type`)
        VALUES
        (#{content}, #{type}, #{groupRank}, #{proType})
    </insert>

    <select id="selectByContent" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonItem">
        SELECT `id`,`content`,`type`, `group_rank`
        FROM <include refid="tableName"/>
        WHERE `content`=#{content}
    </select>

</mapper>