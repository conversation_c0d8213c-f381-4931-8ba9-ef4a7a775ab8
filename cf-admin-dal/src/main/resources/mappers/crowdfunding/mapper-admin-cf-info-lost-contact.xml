<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCfInfoLostContactDAO">

    <sql id="table_name">
        `admin_cf_lost_contact`
    </sql>

    <sql id="select_fields">
        `id`,
        `info_uuid`,
        `lost`,
        `reason`,
        `valid`,
        create_time
    </sql>

    <sql id="insert_Column_List">
        `info_uuid`,
        `lost`,
        `reason`,
        `valid`
	</sql>

    <select id="getValidByInfoUuid" resultType="com.shuidihuzhu.cf.domain.cf.CfInfoLostContactDO">
        SELECT <include refid="select_fields"/>
        from <include refid="table_name"/>
        where `info_uuid` = #{infoUuid}
        and `valid` = 1
        and `is_delete` = 0
    </select>

    <insert id="insert"
            parameterType="com.shuidihuzhu.cf.domain.cf.CfInfoLostContactDO">
        <selectKey order="AFTER" resultType="long" keyProperty="id">
            select last_insert_id() as id
        </selectKey>
        insert into <include refid="table_name"/>
        (<include refid="insert_Column_List" />)
        values(
        #{infoUuid},
        #{lost},
        #{reason},
        #{valid}
        )
    </insert>
    
    <update id="setOldRecordInvalid">
        update <include refid="table_name"/>
        set `valid` = 0
        where info_uuid = #{infoUuid}
    </update>

</mapper>