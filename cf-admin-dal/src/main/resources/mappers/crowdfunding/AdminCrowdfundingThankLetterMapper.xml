<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingThankLetterDao">
    <sql id="tableName">
		crowdfunding_thank_letter
	</sql>

    <select id="getThankLetterByTime" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingThankLetter">
        select * from
        <include refid="tableName"/>
        where is_delete = 0
        <if test="startTime != null and startTime != ''">
            and create_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and create_time <![CDATA[ < ]]> #{endTime}
        </if>
    </select>

    <update id="updateThankLetter" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingThankLetter">
        update
        <include refid="tableName"/>
        set thanks_letter = #{thanksLetter},
        patient_status_head = #{patientStatusHead},
        patient_status_waist = #{patientStatusWaist},
        patient_status_tail = #{patientStatusTail},
        patient_name = #{patientName},
        disease_name = #{diseaseName},
        patient_identity = #{patientIdentity},
        donation_count = #{donationCount},
        amount = #{amount},
        thank_person_identity = #{thankPersonIdentity}
        where case_id = #{caseId}
        and id = #{id}
    </update>
</mapper>