<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCfHospitalAuditDao">
    <sql id="tableName">
        `cf_info_hospital_audit`
    </sql>

    <sql id="selectFields">
        `id` ,
        `info_uuid` ,
        `patient_name` ,
        `hospital_name` ,
        `department` ,
        `floor_number` ,
        `bed_number` ,
        `hospitalization_number` ,
        `doctor_name` ,
        `department_tel_number` ,
        `audit_status`,
        `operator_content`,
        `type`,
        `reason`,
        `reason_supply`,
        `operator_id`
        `operator_org`,
        `easy_to_verify_time`,
        `easy_to_verify_time_status`,
        `on_work_order`,
        `audit_operator_id` ,
        `audit_time` ,
        `submit_time`,
        `create_time`,
        `is_delete`,
        `update_time`,
        `province_id`,
        `city_id`,
        `hospital_id`,
        `province_name`,
        `city_name`
    </sql>

    <sql id="insertFields">
        (`info_uuid` ,
        `patient_name` ,
        `hospital_name` ,
        `department` ,
        `floor_number` ,
        `bed_number` ,
        `hospitalization_number` ,
        `doctor_name` ,
        `department_tel_number` ,
        `audit_status`,
        `operator_content`,
        `type`,
        `reason`,
        `reason_supply`,
        `operator_id`,
        `operator_org`,
        `easy_to_verify_time`,
        `on_work_order`
        )
    </sql>

    <insert id="save" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfHospitalAuditInfo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="tableName"/>
        <include refid="insertFields"/>
        VALUES
        (#{infoUuid}, #{patientName}, #{hospitalName}, #{department},
        #{floorNumber}, #{bedNumber}, #{hospitalizationNumber}, #{doctorName},
        #{departmentTelNumber}, #{auditStatus}, #{operatorContent}, #{type},
        #{reason}, #{reasonSupply} ,#{operatorId} ,#{operatorOrg} ,
        #{easyToVerifyTime} ,#{onWorkOrder} )
    </insert>

    <select id="getByInfoUuid" resultType="com.shuidihuzhu.cf.model.admin.CfHospitalAuditInfoExt">
        SELECT
        <include refid="selectFields"/>
        FROM
        <include refid="tableName"/>
        WHERE `is_delete` = 0
        AND `info_uuid` = #{infoUuid}
        ORDER  BY `id` DESC
        limit 1
    </select>

    <select id="getById" resultType="com.shuidihuzhu.cf.model.admin.CfHospitalAuditInfoExt">
        SELECT
        <include refid="selectFields"/>
        FROM
        <include refid="tableName"/>
        WHERE `is_delete` = 0
        AND `id`= #{id}
    </select>

    <select id="getByInfoUuidNoCareDelete" resultType="com.shuidihuzhu.cf.model.admin.CfHospitalAuditInfoExt">
        SELECT
        <include refid="selectFields"/>
        FROM
        <include refid="tableName"/>
        where `info_uuid` = #{infoUuid}
        ORDER  BY `id` DESC
        limit 1
    </select>

    <select id="getAutoSendByInfoUuid" resultType="com.shuidihuzhu.cf.model.admin.CfHospitalAuditInfoExt">
        SELECT
        <include refid="selectFields"/>
        FROM
        <include refid="tableName"/>
        where `info_uuid` = #{infoUuid}
        and `type` = 1
        limit 1
    </select>


    <select id="getByInfoUuids" resultType="com.shuidihuzhu.cf.model.admin.CfHospitalAuditInfoExt">
        SELECT
        <include refid="selectFields"/>
        FROM
        <include refid="tableName"/>
        WHERE `is_delete` = 0
        AND `info_uuid` in
        <foreach collection="infoUuids" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>

    </select>

    <update id="update" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfHospitalAuditInfo">
        UPDATE <include refid="tableName"/>
        SET
        `info_uuid` = #{infoUuid},
        `patient_name` = #{patientName},
        `hospital_name` = #{hospitalName},
        `department` = #{department},
        `floor_number` = #{floorNumber},
        `bed_number` = #{bedNumber},
        `hospitalization_number` = #{hospitalizationNumber} ,
        `doctor_name` = #{doctorName},
        `department_tel_number` = #{departmentTelNumber},
        `audit_status` = #{auditStatus},
        `operator_content` = #{operatorContent},
        `audit_operator_id` = #{auditOperatorId},
        `audit_time` = #{auditTime} ,
        `is_delete` = 0
        WHERE id = #{id}
    </update>


    <update id="delete" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfHospitalAuditInfo">
        UPDATE <include refid="tableName"/>
        SET
        `hospital_name` = '',
        `department` = '',
        `floor_number` = '',
        `bed_number` = '',
        `hospitalization_number` = '' ,
        `doctor_name` = '',
        `department_tel_number` = '',
        `operator_content` = '',
        `audit_status` = 0 ,
        `audit_operator_id` = #{auditOperatorId},
        `audit_time` = #{auditTime} ,
        `is_delete` = 1
        WHERE id = #{id}
    </update>

    <update id="updateHospitalInfo">
        UPDATE <include refid="tableName"/>
        SET
        `hospital_name` = #{hospitalName},
        `province_id` = #{provinceId},
        `city_id` = #{cityId},
        `province_name` = #{provinceName},
        `city_name` = #{cityName},
        `hospital_id` = #{hospitalId}
        WHERE id = #{id}
    </update>

    <select id="getNotFinish" resultType="java.lang.String">
    SELECT `info_uuid`
    FROM
    <include refid="tableName"/>
    WHERE `is_delete` = 0
    AND `audit_status` != 2
    </select>

    <select id="countHospitalAuditByStatus" resultType="java.lang.Integer">
        SELECT COUNT(id)
        FROM
        <include refid="tableName"/>
        WHERE `is_delete` = 0
        AND `audit_status` = #{auditStatus}
    </select>

    <select id="listDistinctOrg" resultType="java.lang.String">
        select distinct `operator_org` from <include refid="tableName"/>
    </select>

    <select id="listByCondition" resultType="com.shuidihuzhu.cf.vo.approve.HospitalAuditShowVO">
        select ha.*, ci.title as title, ci.id as caseId, ci.status as crowdfundingStatus from
        <include refid="tableName"/> ha
         left join crowdfunding_info ci on ci.info_id = ha.info_uuid
         <where>
             <if test="caseId != null">
                 and ci.`id` = #{caseId}
--                  传了caseId 允许时间范围为null
                 <if test="hospitalSendBeginTime != null">
                     and ha.`create_time`<![CDATA[ >= ]]> #{hospitalSendBeginTime}
                 </if>
                 <if test="hospitalSendEndTime != null">
                     and ha.`create_time`<![CDATA[ <= ]]> #{hospitalSendEndTime}
                 </if>
             </if>
             <if test="caseId == null">
                 and ha.`create_time`<![CDATA[ >= ]]> #{hospitalSendBeginTime}
                 and ha.`create_time`<![CDATA[ <= ]]> #{hospitalSendEndTime}
             </if>
             <if test="caseStatus != null">
                 and ci.`status` = #{caseStatus}
             </if>
             <if test="onWorkOrder >= 0">
                 and ha.`on_work_order` = #{onWorkOrder}
             </if>
             <if test="hospitalAuditStatus == 0">
                 and ha.`is_delete` = 1
             </if>
             <if test="hospitalAuditStatus > 0">
                 and ha.`audit_status` = #{hospitalAuditStatus}
             </if>
             <if test="userSubmitBeginTime != null">
                 and ha.`submit_time`<![CDATA[ >= ]]> #{userSubmitBeginTime}
             </if>
             <if test="userSubmitEndTime != null">
                 and ha.`submit_time`<![CDATA[ <= ]]> #{userSubmitEndTime}
             </if>
             <if test="auditBeginTime != null">
                 and ha.`audit_time`<![CDATA[ >= ]]> #{auditBeginTime}
             </if>
             <if test="auditEndTime != null">
                 and ha.`audit_time`<![CDATA[ <= ]]> #{auditEndTime}
             </if>
         </where>
        order by ha.id desc
    </select>

    <select id="getByBatch" resultType="com.shuidihuzhu.cf.model.admin.CfHospitalAuditInfoExt">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        where
        <if test="id > 0">
            id > #{id} and
        </if>
        is_delete = 0
        order by id
        limit #{size}
    </select>

</mapper>