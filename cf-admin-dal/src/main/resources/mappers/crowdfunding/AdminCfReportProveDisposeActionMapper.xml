<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCfReportProveDisposeActionDao">

    <sql id="tableName">
        cf_report_prove_dispose_action
    </sql>


    <insert id="add" parameterType="com.shuidihuzhu.cf.model.report.CfReportProveDisposeAction">
        insert
        <include refid="tableName"/>
        (dispose_action, info_uuid,type,trust_id)
        values
        (#{disposeAction},#{infoUuid},#{type},#{trustId})
    </insert>

    <select id="getDisposeAction" resultType="java.lang.String">
        SELECT dispose_action
        from
        <include refid="tableName"/>
        where
        `trust_id` = #{trustId}
        and `is_delete` = 0
        ORDER BY id desc limit 1
    </select>

    <select id="findByInfoUuid" resultType="com.shuidihuzhu.cf.model.report.CfReportProveDisposeAction">
        select * from
        <include refid="tableName"/>
        where info_uuid = #{infoUuid}
        and is_delete = 0
        order by id desc limit 1
    </select>


</mapper>
