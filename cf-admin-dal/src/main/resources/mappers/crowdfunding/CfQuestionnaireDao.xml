<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfQuestionnaireDao">


    <insert id="save" parameterType="com.shuidihuzhu.cf.model.admin.CfQuestionnaire" useGeneratedKeys="true"
            keyProperty="id">
		insert into `cf_questionnaire`
		(
		record_id,
		q_id,
		source,
		channel,
		case_id,
		org,
		name,
		mobile,
		card,
		user_id,
		send_time
		)
		values
		(
		#{recordId},
		#{qid},
		#{source},
		#{channel},
		#{caseId},
		#{org},
		#{name},
		#{mobile},
		#{card},
		#{userId},
		#{sendTime}

		)
	</insert>


    <select id="getByCard" resultType="java.lang.String">
		SELECT card
		FROM cf_questionnaire
		where card=#{card}
		limit 1
	</select>

    <select id="getListByCard" resultType="com.shuidihuzhu.cf.model.admin.CfQuestionnaire">
		SELECT *
		FROM cf_questionnaire
		where card=#{card}
	</select>

    <select id="getById" resultType="com.shuidihuzhu.cf.model.admin.CfQuestionnaire">
		SELECT *
		FROM cf_questionnaire
		where id=#{id}
	</select>


    <select id="getByIds" resultType="com.shuidihuzhu.cf.model.admin.CfQuestionnaire">
        SELECT *
        FROM cf_questionnaire
        where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="updateContentByUserId">
		UPDATE cf_questionnaire
		SET content = #{content},q_status=#{status},submit_time=now(),q_name=#{qname},start_answering_time=#{startAnsweringTime},end_time=#{endTime}
		WHERE id = #{questionnaireId}
	</update>


    <update id="saveCaseId">
		UPDATE cf_questionnaire
		SET case_id = #{caseId}
		WHERE record_id = #{recordId}
	</update>


    <update id="saveOperatorIdAndComment">
		UPDATE cf_questionnaire
		SET operator_id = #{operatorId},comment=#{comment}
		WHERE id = #{id}
	</update>

</mapper>
