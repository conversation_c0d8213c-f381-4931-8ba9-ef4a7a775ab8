<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCfPropertyTransferHistoryDao">

    <sql id="TABLE">
        cf_property_transfer_history
    </sql>

    <sql id="SELECT_FIELDS">
        `id`, `from_user_id`, `to_user_id`, `biz_id`, `biz_type`, `biz_table_name`, `create_time`, `update_time`
    </sql>

    <sql id="INSERT_FIELDS">
        `from_user_id`, `to_user_id`, `biz_id`, `biz_type`, `biz_table_name`
    </sql>

    <select id="selectCfOrderIdsByBizType" resultType="java.lang.Long">
        select distinct biz_id
        from <include refid="TABLE"/>
        where from_user_id=#{fromUserId} and biz_type=#{bizType} and is_delete=0
    </select>

</mapper>