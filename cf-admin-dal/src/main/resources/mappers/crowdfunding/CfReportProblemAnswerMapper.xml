<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfReportAnswerDAO">
    <sql id="table_name">
        cf_report_answer
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.AdminReportProblemAnswerDetail" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="table_name"/>
        (`case_id`, `report_id`, `type`, `relation_key`, `relation_value`, `info_source_key`, `info_source_value`, `memo`, `answer_detail`, `operator_id`, `problem_options`, `mandatory_info`, `is_new_strategy`,connect_object)
        VALUES
        (#{caseId}, #{reportId}, #{type}, #{relationKey}, #{relationValue}, #{infoSourceKey}, #{infoSourceValue}, #{memo}, #{answerDetail}, #{operatorId}, #{problemOptions}, #{mandatoryInfo}, #{isNewStrategy},#{connectObject})
    </insert>

    <select id="query" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminReportProblemAnswerDetail">
        SELECT *
        FROM <include refid="table_name"/>
        WHERE case_id = #{caseId} AND report_id = #{reportId} AND `type` = #{type}
    </select>

    <select id="queryByType" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminReportProblemAnswerDetail">
        SELECT *
        FROM <include refid="table_name"/>
        WHERE case_id = #{caseId} AND `type` = #{type}
    </select>

    <select id="queryByIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminReportProblemAnswerDetail">
        SELECT *
        FROM <include refid="table_name"/>
        WHERE id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="queryById" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminReportProblemAnswerDetail">
        SELECT *
        FROM <include refid="table_name"/>
        WHERE id = #{id}
    </select>

    <select id="queryByCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminReportProblemAnswerDetail">
        SELECT *
        FROM <include refid="table_name"/>
        WHERE case_id = #{caseId}
    </select>

    <select id="queryLastByCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminReportProblemAnswerDetail">
        SELECT *
        FROM <include refid="table_name"/>
        WHERE case_id = #{caseId} and `type` = #{type}
        order by create_time desc limit 1
    </select>

    <select id="queryByCaseIdAndType" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminReportProblemAnswerDetail">
        SELECT *
        FROM <include refid="table_name"/>
        WHERE case_id = #{caseId}
        and `type` = #{type}
        and is_delete = 0
    </select>

</mapper>