<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfMaterialVerityHistoryDAO">


    <sql id = "tableName">
        cf_material_verity_history
    </sql>

    <sql id = "common_fields">
      `id`,
      `case_id`,
      `info_id`,
      `handle_type`,
      `material_id`,
      `refuse_ids`,
<!--      `material_info`,-->
      `material_info_encrypt`,
      `material_pic_info`,
<!--      `material_info_ext`,-->
      `material_info_ext_encrypt`,
      `operator_id`,
      `operator_type`,
      `comment`,
      `work_order_id`,
      `create_time`,
      `operator_detail`,
      `material_op_time`,
      `material_op_time_type`
    </sql>

    <insert id = "insertRecords" parameterType = "com.shuidihuzhu.cf.model.crowdfunding.CfMaterialVerityHistory">
        INSERT INTO <include refid="tableName"/>
        (`case_id`, `info_id`, `handle_type`, `material_id`, `refuse_ids`,
<!--        `material_info`,-->
        `material_info_encrypt`,
        `material_pic_info`,
<!--        `material_info_ext`,-->
        `material_info_ext_encrypt`,
        `operator_id`, `operator_type`, `comment`, `work_order_id`, `approve_control_id`,
        `operator_detail`, `material_op_time`, `material_op_time_type`)
        VALUES
        <foreach collection="list" item="item" separator=",">
        (
          #{item.caseId},
          #{item.infoId},
          #{item.handleType},
          #{item.materialId},
          #{item.refuseIds},
<!--          #{item.materialInfo},-->
          #{item.materialInfoEncrypt},
          #{item.materialPicInfo},
<!--          #{item.materialInfoExt},-->
          #{item.materialInfoExtEncrypt},
          #{item.operatorId},
          #{item.operatorType},
          #{item.comment},
          #{item.workOrderId},
          #{item.approveControlId},
          #{item.operatorDetail},
          #{item.materialOpTime},
          #{item.materialOpTimeType}
        )
        </foreach>
    </insert>

    <select id = "selectByMaterialType" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfMaterialVerityHistory">
      SELECT <include refid="common_fields"/>
      FROM <include refid="tableName"/>
      WHERE
      `info_id` = #{infoId}
       AND
      `material_id` = #{materialId}
      AND
      `handle_type` = #{handleType}
      ORDER BY id DESC
      limit #{limit}, #{offset}
    </select>


    <select id = "countByMaterialType" resultType="java.lang.Integer">
        SELECT count(*)
        FROM <include refid="tableName"/>
        WHERE
        `info_id` = #{infoId}
        AND
        `material_id` = #{materialId}
        AND
        `handle_type` = #{handleType}
    </select>

    <select id = "getCount" resultType="java.lang.Integer">
        SELECT count(*)
        FROM <include refid="tableName"/>
        WHERE
        `case_id` = #{caseId}
        AND
        `material_id` = #{materialId}
        AND
        `handle_type` = #{handleType}
    </select>


    <select id = "selectLatestMaterial" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfMaterialVerityHistory">
        SELECT <include refid="common_fields"/>
        FROM <include refid="tableName"/>
        WHERE
        `case_id` = #{caseId}
        AND
        `material_id` = #{materialId}
        AND
        `handle_type` = #{handleType}
        ORDER BY id DESC LIMIT 1
    </select>

    <select id = "selectLatestMaterialNoType" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfMaterialVerityHistory">
        SELECT <include refid="common_fields"/>
        FROM <include refid="tableName"/>
        WHERE
        `case_id` = #{caseId}
        AND
        `material_id` = #{materialId}
        ORDER BY id DESC LIMIT 1
    </select>


    <select id = "selectLatestMaterialByMaterial" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfMaterialVerityHistory">
        SELECT <include refid="common_fields"/>
        FROM <include refid="tableName"/>
        WHERE
        `case_id` = #{caseId}
        AND
        `material_id` = #{materialId}
        ORDER BY id DESC LIMIT 1
    </select>

    <select id = "selectLatestByWorkOrder" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfMaterialVerityHistory">
        SELECT <include refid="common_fields"/>
        FROM <include refid="tableName"/>
        WHERE
        `case_id` = #{caseId}
        AND
        `work_order_id` = #{workOrderId}
        AND
        `material_id` = #{materialId}
        ORDER BY id DESC LIMIT 1
    </select>

</mapper>
