<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.admin.stats.AdminUsageFreqStatsDao">
  <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.model.admin.stats.AdminUsageFreqStats">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_type" jdbcType="INTEGER" property="bizType" />
    <result column="stats_dim" jdbcType="VARCHAR" property="statsDim" />
    <result column="stats_key" jdbcType="VARCHAR" property="statsKey" />
    <result column="stats_value" jdbcType="BIGINT" property="statsValue" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, biz_type, stats_dim, stats_key, stats_value, is_delete, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 'false' as QUERYID,
    <include refid="Base_Column_List" />
    from admin_usage_freq_stats
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.model.admin.stats.AdminUsageFreqStats" useGeneratedKeys="true">
    insert into admin_usage_freq_stats
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="statsDim != null">
        stats_dim,
      </if>
      <if test="statsKey != null">
        stats_key,
      </if>
      <if test="statsValue != null">
        stats_value,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bizType != null">
        #{bizType,jdbcType=INTEGER},
      </if>
      <if test="statsDim != null">
        #{statsDim,jdbcType=VARCHAR},
      </if>
      <if test="statsKey != null">
        #{statsKey,jdbcType=VARCHAR},
      </if>
      <if test="statsValue != null">
        #{statsValue,jdbcType=BIGINT},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <select id="getByUniqueCondition" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from admin_usage_freq_stats
    where stats_key = #{statsKey,jdbcType=VARCHAR}
    and stats_dim = #{statsDim,jdbcType=VARCHAR}
    and biz_type = #{bizType,jdbcType=TINYINT}
  </select>

  <select id="getByUniqueConditions" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from admin_usage_freq_stats
    where stats_key in
    <foreach collection="statsKeys" open="(" item="statsKey" separator="," close=")">
        #{statsKey,jdbcType=VARCHAR}
    </foreach>
    and stats_dim = #{statsDim,jdbcType=VARCHAR}
    and biz_type = #{bizType,jdbcType=TINYINT}
  </select>

  <update id="updateByUniqueCondition">
    update admin_usage_freq_stats
    set stats_value = stats_value + #{increment}
    where stats_key = #{statsKey,jdbcType=VARCHAR}
    and stats_dim = #{statsDim,jdbcType=VARCHAR}
    and biz_type = #{bizType,jdbcType=TINYINT}
  </update>
  <update id="updateByUniqueConditions">
    update admin_usage_freq_stats
    set stats_value = stats_value + #{increment}
    where stats_key in
    <foreach collection="statsKeys" open="(" item="statsKey" separator="," close=")">
        #{statsKey,jdbcType=VARCHAR}
    </foreach>
    and stats_dim = #{statsDim,jdbcType=VARCHAR}
    and biz_type = #{bizType,jdbcType=TINYINT}
  </update>

</mapper>