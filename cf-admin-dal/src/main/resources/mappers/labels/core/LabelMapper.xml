<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.labels.core.LabelDAO">
    <sql id="table_name">
        common_label
    </sql>

    <insert id="add">
        insert into <include refid="table_name"/>
        (uuid, `name`, path_name, label_desc, seq, label_status, risk_level, parent_id, parent_path)
        values
        (
        #{uuid},
        #{name},
        #{pathName},
        #{labelDesc},
        #{seq},
        #{labelStatus},
        #{riskLevel},
        #{parentId},
        #{parentPath}
        )
    </insert>

    <select id="getByUuid" resultType="com.shuidihuzhu.cf.domain.label.core.LabelDO">
        select * from <include refid="table_name"/>
        where uuid = #{uuid}
        and is_delete = 0
    </select>

    <select id="getLabelByIds" resultType="com.shuidihuzhu.cf.domain.label.core.LabelDO">
        select * from <include refid="table_name"/>
        where id in <foreach collection="ids" open="(" close=")" separator="," item="id">
            #{id}
            </foreach>
        and label_status = 1
        and is_delete = 0
    </select>

    <select id="getEnableChildListById" resultType="com.shuidihuzhu.cf.domain.label.core.LabelDO">
        select * from <include refid="table_name"/>
        where parent_id = #{id}
        and label_status = 1
        and is_delete = 0
    </select>

    <select id="getChildListByIdAndStatus" resultType="com.shuidihuzhu.cf.domain.label.core.LabelDO">
        select * from <include refid="table_name"/>
        where parent_id = #{id}
        and label_status in <foreach collection="labelStatusList" open="(" close=")" separator="," item="labelStatus">
        #{labelStatus}
        </foreach>
        and is_delete = 0
    </select>


    <select id="getById" resultType="com.shuidihuzhu.cf.domain.label.core.LabelDO">
        select * from <include refid="table_name"/>
        where id = #{id}
        and is_delete = 0
    </select>

    <select id="getLabelByUuidList" resultType="com.shuidihuzhu.cf.domain.label.core.LabelDO">
        select * from <include refid="table_name"/>
        where uuid in <foreach collection="uuidList" open="(" close=")" separator="," item="uuid">
                #{uuid}
            </foreach>
        and label_status = 1
        and is_delete = 0
    </select>

    <update id="updateSeqById">
        update <include refid="table_name"/>
        <set>
            seq = #{targetSeq}
        </set>
        <where>
            id = #{id}
        </where>
    </update>

    <update id="updateStatusByLabelId">
        update <include refid="table_name"/>
        <set>
            label_status = #{targetLabelStatus}
        </set>
        <where>
            id = #{id}
            and label_status = #{sourceLabelStatus}
        </where>
    </update>

    <update id="updateLabelDesc">
        update <include refid="table_name"/>
        <set>
            label_desc = #{labelDesc}
        </set>
        <where>
            id = #{id}
        </where>
    </update>

    <select id="searchLabel" resultType="com.shuidihuzhu.cf.domain.label.core.LabelDO">
        select * from <include refid="table_name"/>
        <where>
            parent_path LIKE CONCAT(#{parentPathKey}, '%')
            <if test="nameKey != null">
                and name LIKE CONCAT('%', #{nameKey}, '%')
            </if>
            <if test="labelStatusList != null and labelStatusList.size() != 0">
                and label_status in <foreach collection="labelStatusList" open="(" close=")" separator="," item="labelStatus">
                #{labelStatus}
            </foreach>
            </if>
            <if test="riskLevel != null">
                and risk_level = #{riskLevel}
            </if>
            and is_delete = 0
        </where>
    </select>

</mapper>