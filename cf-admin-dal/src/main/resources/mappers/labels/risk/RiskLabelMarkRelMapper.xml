<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.labels.risk.RiskLabelMarkRelDAO">
    <sql id="table_name">
        cf_risk_label_mark_record_label_rel
    </sql>

    <insert id="addList">
        insert into <include refid="table_name"/>
        (label_mark_record_id, label_id, risk_catch_status,`description`)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.labelMarkRecordId},#{item.labelId},#{item.riskCatchStatus},#{item.description})
        </foreach>
    </insert>

    <select id="getAllByRecordId" resultType="com.shuidihuzhu.cf.domain.label.risk.RiskLabelMarkRecordRelDO">
        select * from <include refid="table_name"/>
        where label_mark_record_id = #{recordId}
    </select>
</mapper>