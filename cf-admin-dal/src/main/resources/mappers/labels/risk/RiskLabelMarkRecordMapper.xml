<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.labels.risk.RiskLabelMarkDAO">
    <sql id="table_name">
        cf_risk_label_mark_record
    </sql>

    <insert id="add" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="table_name"/>
        (case_id, scene, order_id, mark_order_id, case_risk_type, mark_desc, risk_overflow_status, operator_id,
        operator_org_name, mark_type)
        values (
        #{caseId},
        #{scene},
        #{orderId},
        #{markOrderId},
        #{caseRiskType},
        #{markDesc},
        #{riskOverflowStatus},
        #{operatorId},
        #{operatorOrgName},
        #{markType}
        )
    </insert>

    <select id="getNewByCaseId" resultType="com.shuidihuzhu.cf.domain.label.risk.RiskLabelMarkRecordDO">
        select * from <include refid="table_name"/>
        where case_id = #{caseId}
        and is_delete = 0
        order by id desc limit 1
    </select>

    <select id="getAllByCaseId" resultType="com.shuidihuzhu.cf.domain.label.risk.RiskLabelMarkRecordDO">
        select * from <include refid="table_name"/>
        where case_id = #{caseId}
        and is_delete = 0
    </select>

    <select id="getAllByCaseIdAndMarkType" resultType="com.shuidihuzhu.cf.domain.label.risk.RiskLabelMarkRecordDO">
        select * from <include refid="table_name"/>
        where case_id = #{caseId}
        and mark_type = #{markType}
        and is_delete = 0
    </select>

    <select id="getLastByCaseIdAndMarkType" resultType="com.shuidihuzhu.cf.domain.label.risk.RiskLabelMarkRecordDO">
        select * from <include refid="table_name"/>
        where case_id = #{caseId}
        and mark_type = #{markType}
        and is_delete = 0
        order by id desc limit 1
    </select>
</mapper>