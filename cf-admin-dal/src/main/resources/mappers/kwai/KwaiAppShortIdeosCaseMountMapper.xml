<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.kwai.KwaiAppShortIdeosCaseMountDao">

	<sql id="TABLE">
		kwai_app_short_ideos_case_mount
	</sql>

	<sql id="QUERY_FIELDS">
		id, encrypt_mobile, info_uuid as infoUuid, operator_id as operatorId, create_time as createTime
	</sql>

	<insert id="add" parameterType="com.shuidihuzhu.cf.model.kwai.KwaiAppShortIdeosCaseMountDo" useGeneratedKeys="true"
			keyProperty="id">
		INSERT INTO
		<include refid="TABLE"/>
		(encrypt_mobile,info_uuid,operator_id)
		VALUES (#{encryptMobile}, #{infoUuid},#{operatorId})
		ON DUPLICATE KEY UPDATE
		is_delete = 0,operator_id=#{operatorId},create_time=now()
	</insert>

	<update id="delete">
		UPDATE
		<include refid="TABLE"/>
		SET is_delete = 1 WHERE id = #{id}
	</update>

	<update id="deleteBatch">
		UPDATE
		<include refid="TABLE"/>
		SET is_delete = 1 WHERE id in
		<foreach collection="ids" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</update>

	<select id="getList" resultType="com.shuidihuzhu.cf.model.kwai.KwaiAppShortIdeosCaseMountDo">
		SELECT
		<include refid="QUERY_FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE is_delete=0
		<if test="infoUuid != null and infoUuid !=''">
			and info_uuid = #{infoUuid}
		</if>
		<if test="encryptMobile != null and encryptMobile !=''">
			and encrypt_mobile = #{encryptMobile}
		</if>
        order by id desc
	</select>

	<select id="getInfoUuidList" resultType="java.lang.String">
		SELECT
		info_uuid
		FROM
		<include refid="TABLE"/>
		WHERE
		encrypt_mobile = #{encryptMobile}
		and
		is_delete=0
	</select>
</mapper>