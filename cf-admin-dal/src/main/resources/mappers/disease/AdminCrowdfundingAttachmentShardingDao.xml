<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingAttachmentShardingDao">
    <sql id="tableName">
        cf_attachment_case_id_sharding
    </sql>

    <sql id="tableNameSharding">
        cf_attachment_case_id_sharding_${sharding}
    </sql>

    <sql id="QUERY_FIELDS">
        id,
        parent_id,
        url,
        type,
        sequence,
        create_time,
        last_modified,
        is_delete
    </sql>

    <select id="getAttachmentsByParentIdAndType" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment">
        SELECT IFNULL(url,'') as url,id,parent_id
        FROM
        <include refid="tableName"/>
        WHERE parent_id = #{parentId}
        AND `is_delete` = 0
        AND type = #{type}
        ORDER BY id
    </select>

    <update id="deleteByIds" >
        UPDATE
        <include refid="tableNameSharding"/>
        SET `is_delete`=1
        WHERE
        `parent_id` = #{parentId}
        AND
        `id` IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="getAttachmentById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment">
        SELECT <include refid="QUERY_FIELDS"/>
        FROM
        <include refid="tableName"/>
        WHERE
        `parent_id` = #{parentId}
        id = #{id}
    </select>

    <select id="queryAttachment" parameterType="int" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment">
        SELECT <include refid="QUERY_FIELDS"/>
        FROM <include refid="tableName"/>
        WHERE parent_id = #{parentId}
    </select>

    <select id="getFundingAttachment" parameterType="int" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment">
        SELECT id,url, type, sequence
        FROM
        <include refid="tableName"/>
        WHERE
        parent_id = #{parentId}
        AND `is_delete` = 0
    </select>
    <select id="getAttachmentsByType" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment">
        SELECT IFNULL(url,'') as url
        FROM
        <include refid="tableName"/>
        WHERE parent_id = #{parentId}
        AND `is_delete` = 0
        AND type =
        #{type, typeHandler=org.apache.ibatis.type.EnumOrdinalTypeHandler, javaType=com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum}
        ORDER BY id
    </select>
    <select id="getListByInfoIdListAndType" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment">
        SELECT <include refid="QUERY_FIELDS"/>
        FROM <include refid="tableName"/>
        WHERE parent_id in
        <foreach collection="parentIdList" item="parentId" open="(" separator="," close=")">
            #{parentId}
        </foreach>
        AND type =
        #{type, typeHandler=org.apache.ibatis.type.EnumOrdinalTypeHandler, javaType=com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum}
        AND `is_delete` = 0
        ORDER BY id
    </select>
    <select id="getAttachmentsByTypes" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment">
        SELECT <include refid="QUERY_FIELDS"/>
        FROM
        <include refid="tableName"/>
        WHERE parent_id = #{parentId}
        AND `is_delete` = 0
        AND `type` in
        <foreach collection="types" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getAttachmentsByCaseIdsWithDelete" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment">
        SELECT <include refid="QUERY_FIELDS"/>
        FROM
        <include refid="tableName"/>
        WHERE parent_id in
        <foreach collection="parentIdList" item="parentId" open="(" separator="," close=")">
            #{parentId}
        </foreach>
        AND `type` in
        <foreach collection="types" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getAttachmentsByIdList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment">
        SELECT <include refid="QUERY_FIELDS"/>
        FROM
        <include refid="tableName"/>
        WHERE
        `parent_id` = #{parentId}
        AND
        id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>