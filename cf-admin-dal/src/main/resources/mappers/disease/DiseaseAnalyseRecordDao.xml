<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.disease.DiseaseAnalyseRecordDao">

    <sql id="table">
        cf_disease_analyse_record
    </sql>
    <insert id="insetOne" parameterType="com.shuidihuzhu.cf.dto.DiseaseAnalyseRecordDto">
        insert into
        <include refid="table"/>
        (`case_id`,
        `work_order_id`,
        `input_disease_name`,
        `output_normalized_result`,
        `error_msg`,
        `operator`,
        `has_error`)
        values
        (#{record.caseId},
        #{record.workOrderId},
        #{record.inputDiseaseName},
        #{record.outputNormalizedResult},
        #{record.errorMsg},
        #{record.operator},
        #{record.hasError})
    </insert>
</mapper>