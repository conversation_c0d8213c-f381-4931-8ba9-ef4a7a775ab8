<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.disease.DiseaseKnowledgeRecordDao">

    <sql id="selectFields">
        `create_time`  as createTime,
        `remark`,
        `operator`
    </sql>
    <sql id="tableName">
        `disease_knowledge_record`
    </sql>


    <insert id="insert">
        insert
        <include refid="tableName"/>
        (science_id, operator, remark)
        values(#{scienceId}, #{operator}, #{remark})
    </insert>


    <select id="selectList" resultType="com.shuidihuzhu.cf.model.disease.DiseaseKnowledgeRecord">
        SELECT
        <include refid="selectFields"></include>
        FROM
        <include refid="tableName"/>
        WHERE is_delete = 0 AND science_id = #{scienceId}
        order by id desc
    </select>

</mapper>