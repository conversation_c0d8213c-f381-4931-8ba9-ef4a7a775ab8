<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.disease.DiseaseKnowledgeDao">

    <sql id="selectFields">
        `disease_norm`  as diseaseNorm,
        `disease_intro` as diseaseIntro,
        `cure_and_cost` as cureAndCost,
        `prognosis`,
        `id`
    </sql>
    <sql id="tableName">
        `disease_knowledge`
    </sql>


    <insert id="insert" keyProperty="id" useGeneratedKeys="true" parameterType="com.shuidihuzhu.cf.model.disease.DiseaseKnowledge">
        insert
        <include refid="tableName"/>
        (disease_norm, disease_intro, cure_and_cost,prognosis)
        values(#{diseaseNorm}, #{diseaseIntro}, #{cureAndCost},#{prognosis})
    </insert>

    <update id="update">
        UPDATE
        <include refid="tableName"/>
        SET
        disease_intro=#{diseaseIntro},cure_and_cost=#{cureAndCost},prognosis=#{prognosis}
        WHERE id = #{id}
    </update>

    <update id="delete">
        UPDATE
        <include refid="tableName"/>
        SET
        is_delete = 1
        WHERE id = #{id}
    </update>


    <select id="selectList" resultType="com.shuidihuzhu.cf.model.disease.DiseaseKnowledge">
        SELECT
        <include refid="selectFields"></include>
        FROM
        <include refid="tableName"/>
        WHERE is_delete = 0
        <if test="diseaseNorm != null and diseaseNorm != ''">
            AND disease_norm = #{diseaseNorm}
        </if>
    </select>

    <select id="select" resultType="com.shuidihuzhu.cf.model.disease.DiseaseKnowledge">
        SELECT
        <include refid="selectFields"></include>
        FROM
        <include refid="tableName"/>
        WHERE is_delete = 0 AND disease_norm = #{diseaseNorm}
    </select>

</mapper>