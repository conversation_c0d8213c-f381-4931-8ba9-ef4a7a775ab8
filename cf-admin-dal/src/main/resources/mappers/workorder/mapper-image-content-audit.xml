<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.workorder.CfImageContentAuditDAO">

    <sql id="table_name">
        `cf_image_content_audit_info`
    </sql>


    <insert id="addImageContentInfo">
        INSERT INTO
        <include refid="table_name"/>
        ( `case_id`, `work_order_id`, `info_status`, `reject_detail`, `operator_id`, `operator_detail`)
        VALUES ( #{caseId}, #{workOrderId}, #{infoStatus}, #{rejectDetail}, #{operatorId}, #{operatorDetail})
    </insert>


</mapper>