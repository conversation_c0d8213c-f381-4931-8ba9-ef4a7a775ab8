<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.workorder.WorkOrderFollowLabelDao">

    <sql id="table_name">
        `fuwu_follow_label`
    </sql>

    <sql id="select_fields">
        `work_order_id`,
        `follow_label`,
        `create_time`
    </sql>

    <sql id="insert_fields">
        `work_order_id`,
        `follow_label`
    </sql>

    <insert id="addFollowLabel" parameterType="com.shuidihuzhu.cf.domain.workorder.FollowLabelDO">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        values(
        #{workOrderId},
        #{followLabel}
        )
    </insert>

    <update id="updateByWorkOrderId" parameterType="com.shuidihuzhu.cf.domain.workorder.FollowLabelDO">
        update <include refid="table_name"/>
        set `follow_label` = #{followLabel}
        where `work_order_id` = #{workOrderId}
    </update>

    <select id="selectByWorkOrderId" resultType="com.shuidihuzhu.cf.domain.workorder.FollowLabelDO">
        select <include refid="select_fields"/>
        from <include refid="table_name"/>
        where `work_order_id` = #{workOrderId}
        and is_delete = 0
    </select>

</mapper>