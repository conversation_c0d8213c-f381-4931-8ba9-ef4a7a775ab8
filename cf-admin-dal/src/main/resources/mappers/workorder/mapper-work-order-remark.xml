<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.workorder.WorkOrderRemarkDAO">

    <sql id="table_name">
        `cf_work_order_remark`
    </sql>

    <sql id="select_fields">
        `id`,
        <include refid="insert_Column_List"/>,
        `create_time`
    </sql>

    <sql id="insert_Column_List">
        `case_id`,
        `work_order_id`,
        `operator_id`,
        `content`,
        `organization`
	</sql>

    <insert id="insert"
            parameterType="com.shuidihuzhu.cf.domain.workorder.WorkOrderRemarkDO">
        insert into
        <include refid="table_name"/>
        (<include refid="insert_Column_List"/>)
        values(
        #{caseId},
        #{workOrderId},
        #{operatorId},
        #{content},
        #{organization}
        )
    </insert>

    <select id="listByCaseId" resultType="com.shuidihuzhu.cf.domain.workorder.WorkOrderRemarkDO">
      select <include refid="select_fields"/>
      from <include refid="table_name"/>
      <where>
          `case_id` = #{caseId}
          and `is_delete` = 0
      </where>

    </select>

</mapper>