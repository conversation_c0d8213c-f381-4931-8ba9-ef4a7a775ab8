<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.admin.AdminWorkOrderFlowDao">

    <sql id="table_name">
        `admin_work_order_flow`
    </sql>

    <sql id="insert_fields">
        `work_order_id`,
        `problem_type`,
        `problem_content`,
        `problem_img`,
        `handle_img`,
        `case_id`,
        `second_classify_Id`,
        `new_first_classify_id`,
        `user_identity`,
        `asssign_channel`,
        `jingxi_channel`,
        `encrypt_mobile`
    </sql>

    <sql id="select_fields">
        `id`,
        `work_order_id`,
        `problem_type`,
        `problem_content`,
        `problem_img`,
        `handle_img`,
        `case_id`,
        `second_classify_Id`,
        `new_first_classify_id`,
        `new_second_classify_id`,
        `new_third_classify_id`,
        `user_identity`,
        `create_time`,
        `update_time`,
        `encrypt_mobile`,
        `follow_tags`,
        `jingxi_channel`,
        city_id,
        city_name,
        province_id,
        province_name,
        county_id,
        county_name,
        hospital,
        hospital_id
    </sql>

    <sql id="base_select_fields">
        `id`,
        `work_order_id`,
        `problem_type`,
        `problem_content`,
        `problem_img`,
        `handle_img`,
        `case_id`,
        `second_classify_Id`,
        `new_first_classify_id`,
        `new_second_classify_id`,
        `new_third_classify_id`,
        `jingxi_channel`,
        `encrypt_mobile`,
        `follow_tags`
    </sql>

    <select id="selectByWorkOrderId" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderFlow">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `work_order_id`=#{workOrderId}
    </select>

    <select id="selectByWorkOrderIdList" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderFlow">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `work_order_id` IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderFlow" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>
        <if test="cityId > 0">
            ,city_id
        </if>
        <if test="provinceId > 0">
            ,province_id
        </if>

        <if test="newSecondClassifyId > 0">
            ,new_second_classify_id
        </if>

        <if test="newThirdClassifyId > 0">
            ,new_third_classify_id
        </if>

        <if test="cityName != null and cityName!=''">
            ,city_name
        </if>

        <if test="diseaseName != null and diseaseName!=''">
            ,disease_name
        </if>

        <if test="forWhom != null and forWhom!=''">
            ,for_whom
        </if>

        <if test="provinceName !=null and provinceName!='' ">
            ,province_name
        </if>
        <if test="countyId > 0">
            ,county_id
        </if>
        <if test="countyName !=null and countyName!='' ">
            ,county_name
        </if>
        <if test="hospital !=null and hospital!='' ">
            ,hospital
        </if>
        <if test="hospitalId > 0">
            ,hospital_id
        </if>
        )
        VALUES (#{workOrderId}, #{problemType}, #{problemContent}, #{problemImg}, #{handleImg},#{caseId},
        #{secondClassifyId},#{newFirstClassifyId}, #{userIdentity}, #{assignChannel},#{jingxiChannel},#{encryptMobile}
        <if test="cityId > 0">
            ,#{cityId}
        </if>
        <if test="provinceId > 0">
            ,#{provinceId}
        </if>

        <if test="newSecondClassifyId > 0">
            ,#{newSecondClassifyId}
        </if>

        <if test="newThirdClassifyId > 0">
            ,#{newThirdClassifyId}
        </if>

        <if test="cityName != null and cityName!=''">
            ,#{cityName}
        </if>

        <if test="diseaseName != null and diseaseName!=''">
            ,#{diseaseName}
        </if>

        <if test="forWhom != null and forWhom!=''">
            ,#{forWhom}
        </if>

        <if test="provinceName !=null and provinceName!='' ">
            ,#{provinceName}
        </if>
        <if test="countyId > 0">
            ,#{countyId}
        </if>
        <if test=" countyName !=null and countyName!=''">
            ,#{countyName}
        </if>
        <if test=" hospital !=null and hospital!='' ">
            ,#{hospital}
        </if>
        <if test="hospitalId > 0">
            ,#{hospitalId}
        </if>
        )
    </insert>

    <update id="updateTaskType">
        UPDATE <include refid="table_name"/>
        SET `problem_type` = #{taskType}
        WHERE `work_order_id` = #{workOrderId}
    </update>

    <update id="updateHandleImg">
        UPDATE <include refid="table_name"/>
        SET `handle_img`=#{handleImg}
        WHERE `work_order_id` = #{workOrderId}
    </update>

    <select id="selectUnHandleTask" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView">
        SELECT awof.*,
        awo.level
        FROM <include refid="table_name"/> awof
        LEFT JOIN admin_work_order awo ON awof.work_order_id = awo.id
        WHERE awo.create_time >= #{createTime}
        AND awo.operator_id = 0 AND awo.is_delete=0
        AND awo.order_status NOT IN (4, 5)
        <if test="problemTypes != null and problemTypes.size() > 0">
            AND awof.problem_type IN
            <foreach collection="problemTypes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY awo.level DESC, awo.create_time
        <if test="size > 0">
            LIMIT #{size}
        </if>
    </select>

    <select id = "selectWorkFlowByParam" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView">
        SELECT
        awof.<include refid="base_select_fields"/>,
        awof.asssign_channel as assignChannel,
        awof.user_identity,
        awo.order_status as workOrderStatus,
        awo.level,
        awo.comment,
        awo.operator_id,
        awo.creator_id,
        awo.`create_time`,
        awo.`update_time`
        FROM <include refid="table_name"/> awof
        INNER JOIN admin_work_order awo ON awof.work_order_id = awo.id
        <where>
            <if test="flowId != null and flowId != 0">
                AND awof.id = #{flowId}
            </if>
            <if test="workOrderIds != null and workOrderIds.size() > 0">
                AND awo.id IN
                <foreach collection="workOrderIds" item = "item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="caseId != null">
                AND awof.case_id = #{caseId}
            </if>
            <if test="orgIdList != null and orgIdList.size() > 0">
                AND awof.problem_type in
                <foreach collection="orgIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="encryptMobile != null and encryptMobile != ''">
                AND awof.encrypt_mobile = #{encryptMobile}
            </if>
            <if test="level != null">
                AND awo.level = #{level}
            </if>

            <if test="creatorId != null and creatorId != 0">
                AND awo.creator_id = #{creatorId}
            </if>
            <if test="operatorId != null and operatorId != 0">
                AND awo.operator_id = #{operatorId}
            </if>

            <if test="createStartTime != null">
                AND awo.create_time >= #{createStartTime}
            </if>
            <if test="createEndTime != null">
                AND awo.create_time <![CDATA[ <= ]]> #{createEndTime}
            </if>

            <if test="updateStartTime != null">
                AND awo.update_time >= #{updateStartTime}
            </if>
            <if test="updateEndTime != null">
                AND awo.update_time  <![CDATA[ <= ]]> #{updateEndTime}
            </if>

            <if test="newFirstClassifyId != null and newFirstClassifyId != 0">
                ,new_first_classify_id
            </if>

            <if test="newSecondClassifyId != null and newSecondClassifyId != 0">
                ,new_second_classify_id
            </if>

            <if test="newThirdClassifyId != null and newThirdClassifyId != 0">
                ,new_third_classify_id
            </if>

            <if test="workOrderStatus != null">
                AND awo.order_status = #{workOrderStatus}
            </if>
            <if test="workOrderStatusList != null and workOrderStatusList.size() > 0">
                AND awo.order_status IN
                <foreach collection="workOrderStatusList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="followTag != null">
                AND awof.follow_tags LIKE  CONCAT('%', ',', #{followTag}, ',', '%')
            </if>
        </where>
        ORDER BY awo.update_time DESC
    </select>


    <select id="selectWorkFlowByCaseIdOrMobile" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView">
        SELECT awof.*,
        awo.level,
        awo.creator_id,
        awo.comment,
        awo.order_status as workOrderStatus,
        awo.operator_id,
        awof.asssign_channel as assignChannel
        FROM
        <include refid="table_name"/>
        awof
        INNER JOIN admin_work_order awo ON awof.work_order_id = awo.id
        <where>
            <if test="caseId != null">
                 awof.case_id = #{caseId}
            </if>
            <if test="encryptMobile != null and encryptMobile != ''">
                OR awof.encrypt_mobile = #{encryptMobile}
            </if>
            <if test="workOrderStatusList != null and workOrderStatusList.size() > 0">
                AND awo.order_status IN
                <foreach collection="workOrderStatusList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectOrderFlowViewById" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView">
        SELECT awof.*,
        awo.level,
        awo.creator_id,
        awo.comment,
        awo.order_status as workOrderStatus,
        awo.operator_id,
        awof.asssign_channel as assignChannel
        FROM
        <include refid="table_name"/>
        awof
        INNER JOIN admin_work_order awo ON awof.work_order_id = awo.id
        WHERE
        awof.id = #{id}
    </select>

    <select id="selectByCreateTimeAndCreateIds"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView">
        SELECT awof.id,
        awof.work_order_id,
        awo.creator_id
        FROM
        <include refid="table_name"/>
        awof
        INNER JOIN admin_work_order awo ON awof.work_order_id = awo.id
        WHERE
        awo.create_time <![CDATA[ >= ]]> #{beginTime}
        AND
        awo.create_time <![CDATA[ <= ]]> #{endTime}
        AND
        awo.creator_id IN
        <foreach collection="createIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

        <if test="level != -1">
            AND awo.level = #{level}
        </if>
        <if test="secondLevelClassifyIds != null and secondLevelClassifyIds.size() > 0">
            AND awof.second_classify_id IN
            <foreach collection="secondLevelClassifyIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectByOrderStatusAndOperators"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView">
        SELECT awof.id,
        awof.work_order_id,
        awo.operator_id,
        awo.order_status as workOrderStatus,
        awof.problem_type,
	    awo.level,
	    awof.create_time,
        awof.jingxi_channel
        FROM
        <include refid="table_name"/>
        awof
        INNER JOIN admin_work_order awo ON awof.work_order_id = awo.id
        WHERE
        awo.create_time >= #{createTime}

        AND

        awo.order_status IN
        <foreach collection="orderStatusSet" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

        <if test="operatorIds != null and operatorIds.size() >0">
            AND awo.operator_id IN
            <foreach collection="operatorIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="problemTypeCodes != null and problemTypeCodes.size() >0">
            AND awof.problem_type IN
            <foreach collection="problemTypeCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="level != -1">
            AND awo.level = #{level}
        </if>
        <if test="secondLevelClassifyIds != null and secondLevelClassifyIds.size() > 0">
            AND awof.second_classify_id IN
            <foreach collection="secondLevelClassifyIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="moreThanTimeNoHandle != null">
            AND awo.create_time <![CDATA[ <= ]]> #{moreThanTimeNoHandle}
        </if>
    </select>

    <select id="selectByCaseIdAndSecondId" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView">
        SELECT *
        FROM
        <include refid="table_name"/>
        WHERE
        case_id = #{caseId}
        AND
        second_classify_Id = #{secondClassifyId}
    </select>

    <select id="selectByCaseIdAndClassifyId" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView">
        SELECT *
        FROM
        <include refid="table_name"/>
        WHERE
        case_id = #{caseId}
        AND
        new_first_classify_id = #{newFirstClassifyId}
        AND
        new_second_classify_id = #{newSecondClassifyId}
        AND
        new_third_classify_id = #{newThirdClassifyId}
    </select>

    <select id="selectByMobileAndSecondId" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView">
        SELECT *
        FROM
        <include refid="table_name"/>
        WHERE
        encrypt_mobile = #{encryptMobile}
        AND
        second_classify_Id = #{secondClassifyId}
    </select>

    <select id="selectByMobileAndClassifyId" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView">
        SELECT *
        FROM
        <include refid="table_name"/>
        WHERE
        encrypt_mobile = #{encryptMobile}
        AND
        new_first_classify_id = #{newFirstClassifyId}
        AND
        new_second_classify_id = #{newSecondClassifyId}
        AND
        new_third_classify_id = #{newThirdClassifyId}
    </select>

    <update id="updateSecondId">
        UPDATE <include refid="table_name"/>
        SET `second_classify_id`=#{secondClassifyId}
        WHERE `work_order_id` = #{workOrderId}
    </update>

    <update id="updateClassifyId">
        UPDATE
        <include refid="table_name"/>
        SET new_first_classify_id=#{newFirstClassifyId}
        ,new_second_classify_id=#{newSecondClassifyId}
        ,new_third_classify_id=#{newThirdClassifyId}
        WHERE `work_order_id` = #{workOrderId}
    </update>

    <select id = "selectWaitForHandleFlowIdsByParam" resultType="java.lang.Integer">
        SELECT
        awof.id
        FROM <include refid="table_name"/> awof
        INNER JOIN admin_work_order awo ON awof.work_order_id = awo.id
        <where>
            <if test="createTime != null">
                AND awo.create_time >= #{createTime}
            </if>

            <if test="flowId != null and flowId != 0">
                AND awof.id = #{flowId}
            </if>
            <if test="caseId != null">
                AND awof.case_id = #{caseId}
            </if>
            <if test="encryptMobile != null and encryptMobile != ''">
                AND awof.encrypt_mobile = #{encryptMobile}
            </if>
            <if test="operatorId != null and operatorId != 0">
                AND awo.operator_id = #{operatorId}
            </if>

            <if test="level != null">
                AND awo.level = #{level}
            </if>

            <if test="updateStartTime != null">
                AND awo.update_time >= #{updateStartTime}
            </if>
            <if test="updateEndTime != null">
                AND awo.update_time  <![CDATA[ <= ]]> #{updateEndTime}
            </if>
            <if test="workOrderStatusList != null and workOrderStatusList.size() > 0">
                AND awo.order_status IN
                <foreach collection="workOrderStatusList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="secondClassifyIds != null and secondClassifyIds.size() > 0">
                AND awof.second_classify_id IN
                <foreach collection="secondClassifyIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="newFirstClassifyId != null and newFirstClassifyId != 0">
                AND awof.new_first_classify_id = #{newFirstClassifyId}
            </if>
            <if test="newSecondClassifyId != null and newSecondClassifyId != 0">
                AND awof.new_second_classify_id = #{newSecondClassifyId}
            </if>
            <if test="newThirdClassifyId != null and newThirdClassifyId != 0">
                AND awof.new_third_classify_id = #{newThirdClassifyId}
            </if>
            <if test="followTag != null">
                AND awof.follow_tags LIKE  CONCAT('%', ',', #{followTag}, ',', '%')
            </if>
        </where>
        ORDER BY awo.update_time DESC
    </select>

    <select id = "selectWorkFlowByFlowIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView">
        SELECT
        awof.`id`,
        awof.`work_order_id`,
        awof.`problem_type`,
        awof.`problem_content`,
        awof.`problem_img`,
        awof.`handle_img`,
        awof.`encrypt_mobile`,
        awof.`case_id`,
        awof.`second_classify_Id`,
        awof.`new_first_classify_id`,
        awof.`new_second_classify_id`,
        awof.`new_third_classify_id`,
        awof.`disease_name`,
        awof.`for_whom`,
        awof.`follow_tags`,
        awo.order_status as workOrderStatus,
        awo.level,
        awo.comment,
        awo.operator_id,
        awo.creator_id,
        awo.`create_time`,
        awo.`update_time`
        FROM <include refid="table_name"/> awof
        INNER JOIN admin_work_order awo ON awof.work_order_id = awo.id
        WHERE awof.id in
        <foreach collection="flowIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY awo.update_time DESC
    </select>


    <select id = "countCreateFlowByParam" resultType="java.lang.Integer">
        SELECT count(awof.id)
        FROM <include refid="table_name"/> awof
        INNER JOIN admin_work_order awo ON awof.work_order_id = awo.id
        <where>
            <if test="flowId != null and flowId != 0">
                AND awof.id = #{flowId}
            </if>
            <if test="caseId != null">
                AND awof.case_id = #{caseId}
            </if>
            <if test="encryptMobile != null and encryptMobile != ''">
                AND awof.encrypt_mobile = #{encryptMobile}
            </if>
            <if test="workOrderStatus != null">
                AND awo.order_status = #{workOrderStatus}
            </if>
            <if test="level != null">
                AND awo.level = #{level}
            </if>
            <if test="secondClassifyIds != null and secondClassifyIds.size() > 0">
                AND awof.second_classify_id IN
                <foreach collection="secondClassifyIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="creatorId != null and creatorId != 0">
                AND awo.creator_id = #{creatorId}
            </if>
            <if test="createStartTime != null">
                AND awo.create_time >= #{createStartTime}
            </if>
            <if test="createEndTime != null">
                AND awo.create_time <![CDATA[ <= ]]> #{createEndTime}
            </if>
        </where>
    </select>

    <select id = "selectCreateFlowByParam" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView">
        SELECT
        awof.`id`,
        awof.`work_order_id`,
        awof.`problem_type`,
        awof.`problem_content`,
        awof.`problem_img`,
        awof.`handle_img`,
        awof.`encrypt_mobile`,
        awof.`case_id`,
        awof.`second_classify_Id`,
        awof.`new_first_classify_id`,
        awof.`new_second_classify_id`,
        awof.`new_third_classify_id`,
        awof.`disease_name`,
        awof.`for_whom`,

        awo.order_status as workOrderStatus,
        awo.level,
        awo.comment,
        awo.operator_id,
        awo.creator_id,
        awo.`create_time`,
        awo.`update_time`
        FROM <include refid="table_name"/> awof
        INNER JOIN admin_work_order awo ON awof.work_order_id = awo.id
        <where>
            <if test="flowId != null and flowId != 0">
                AND awof.id = #{flowId}
            </if>
            <if test="caseId != null">
                AND awof.case_id = #{caseId}
            </if>
            <if test="encryptMobile != null and encryptMobile != ''">
                AND awof.encrypt_mobile = #{encryptMobile}
            </if>
            <if test="newFirstClassifyId != null and newFirstClassifyId != 0">
                AND awof.new_first_classify_id = #{newFirstClassifyId}
            </if>
            <if test="newSecondClassifyId != null and newSecondClassifyId != 0">
                AND awof.new_second_classify_id = #{newSecondClassifyId}
            </if>
            <if test="newSecondClassifyId != null and newSecondClassifyId != 0">
                AND awof.new_second_classify_id = #{newSecondClassifyId}
            </if>
            <if test="workOrderStatus != null">
                AND awo.order_status = #{workOrderStatus}
            </if>
            <if test="level != null">
                AND awo.level = #{level}
            </if>
            <if test="creatorId != null and creatorId != 0">
                AND awo.creator_id = #{creatorId}
            </if>
            <if test="createStartTime != null">
                AND awo.create_time >= #{createStartTime}
            </if>
            <if test="createEndTime != null">
                AND awo.create_time <![CDATA[ <= ]]> #{createEndTime}
            </if>
            ORDER BY awo.create_time DESC limit #{limitSize}, #{offset}
        </where>
  </select>

    <update id="updateProblemContent">
        UPDATE <include refid="table_name"/>
        SET `problem_content`=#{problemContent}
        WHERE `work_order_id` = #{workOrderId}
    </update>

    <select id="selectOrderFlowById" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderFlow">
        SELECT *
        FROM
        <include refid="table_name"/>
        WHERE
        id = #{id}
    </select>

  <select id = "selectByMobileAndTaskType" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView">
      SELECT awof.<include refid="base_select_fields"/>
      FROM <include refid="table_name"/> awof
      INNER JOIN admin_work_order awo ON awof.work_order_id = awo.id
      WHERE awof.encrypt_mobile = #{encryptMobile}
      AND awo.task_type = #{taskType}
  </select>


    <update id="updateProvinceAndCity">
        UPDATE <include refid="table_name"/>
        SET `city_id` = #{cityId},
        province_id=#{provinceId},
        city_name = #{cityName},
        province_name = #{provinceName}

        <if test="hospital !=null and hospital!='' ">
            ,hospital = #{hospital}
        </if>

        <if test="countyId>0">
            ,county_id = #{countyId}
        </if>

        <if test="countyName !=null and countyName!='' ">
            ,county_name = #{countyName}
        </if>

        <if test="hospitalId > 0">
            ,hospital_id = #{hospitalId}
        </if>

        WHERE `work_order_id` = #{workOrderId}
    </update>


    <select id = "selectByCaseIdAndTaskType" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView">
        SELECT awof.<include refid="base_select_fields"/>
        FROM <include refid="table_name"/> awof
        INNER JOIN admin_work_order awo ON awof.work_order_id = awo.id
        WHERE awof.case_id = #{caseId}
        AND awo.task_type = #{taskType} limit 1;
    </select>

    <update id="updateFlowFollowTags">
        UPDATE <include refid="table_name"/>
        SET `follow_tags` = #{followTags}
        WHERE `work_order_id` = #{workOrderId}
    </update>

    <select id="selectByOrderStatusAndOperatorsNew"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView">
        SELECT awof.id,
        awof.work_order_id,
        awo.operator_id,
        awo.order_status as workOrderStatus,
        awof.problem_type,
        awo.level,
        awof.create_time,
        awof.jingxi_channel
        FROM
        admin_work_order awo INNER JOIN admin_work_order_flow awof ON awo.id = awof.work_order_id
        WHERE
        awo.create_time >= #{createTime}

        AND

        awo.operator_id IN
        <foreach collection="operatorIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

        AND

        awo.order_status IN
        <foreach collection="orderStatusSet" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

        <if test="problemTypeCode > 0">
            AND awof.problem_type = #{problemTypeCode}
        </if>
    </select>

</mapper>