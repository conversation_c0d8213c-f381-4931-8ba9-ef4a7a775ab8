<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.admin.workorder.flowWorkOrder.WorkFlowAutoAssignRecordDAO">

    <sql id="table_name">
      work_flow_auto_assign_record
    </sql>

    <insert id = "insertOrUpdateAssigns" parameterType="com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowAutoAssignRecord">
        INSERT INTO <include refid="table_name"/>
        (`user_id`, `assign_date`, `assign_num`, `today_first_assign_time`)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.userId}, #{item.assignDate}, #{item.assignNum}, #{item.todayFirstAssignTime})
        </foreach>
        ON DUPLICATE KEY UPDATE
        `assign_num` = `assign_num` + 1
    </insert>

    <select id="selectByUserIdsAndDate" resultType="com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowAutoAssignRecord">
        SELECT *
        FROM <include refid="table_name"/>
        WHERE user_id IN
        <foreach collection="userIds" item = "userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        AND assign_date = #{assignDate}
        AND is_delete = 0
    </select>







</mapper>