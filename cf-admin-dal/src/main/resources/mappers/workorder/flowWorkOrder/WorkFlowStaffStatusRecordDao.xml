<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.WorkFlowStaffStatusRecordDao">

    <sql id="table_name">
        `work_flow_staff_status_record`
    </sql>

    <sql id="insert_fields">
        `user_id`,
        `staff_status`,
        `opt_type`,
        `operator_id`
    </sql>

    <sql id="select_fields">
        `id` as id,
        `user_id` as userId,
        `staff_status` as staffStatus,
        `opt_type` as optType,
        `operator_id` as operatorId,
        `create_time` as createTime
    </sql>

    <insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowStaffStatusRecord"
            useGeneratedKeys="true" keyProperty="id">
        INSERT
        <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES (#{userId}, #{staffStatus}, #{optType}, #{operatorId})
    </insert>


    <select id="listTodayRecords" resultType="com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowStaffStatusRecord">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        <where>
            `user_id` in
            <foreach collection="userIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and `create_time` &gt;= #{startTime}
        </where>
    </select>


</mapper>