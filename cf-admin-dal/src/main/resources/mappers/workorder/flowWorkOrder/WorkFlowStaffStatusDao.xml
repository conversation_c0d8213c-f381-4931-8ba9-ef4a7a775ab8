<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.WorkFlowStaffStatusDao">

    <sql id="table_name">
        `work_flow_staff_status`
    </sql>

    <sql id="insert_fields">
        `user_id`,
        `staff_status`,
        `opt_type`,
        `operator_id`,
        `org_type`
    </sql>

    <sql id="select_fields">
        `id` as id,
        `user_id` as userId,
        `staff_status` as staffStatus,
        `opt_type` as optType,
        `operator_id` as operatorId,
        `org_type` as orgType,
        `assign_time` as assignTime,
        `create_time` as createTime,
        `update_time` as updateTime
    </sql>

    <insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowStaffStatus"
            useGeneratedKeys="true" keyProperty="id">
        INSERT
        <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES (#{userId}, #{staffStatus}, #{optType}, #{operatorId}, #{orgType})
    </insert>


    <update id="changeStatus" parameterType="com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowStaffStatus"
            useGeneratedKeys="true" keyProperty="id">
        update
        <include refid="table_name"/>
        <set>
            <if test="staffStatus > 0">
                `staff_status`= #{staffStatus},
            </if>
            `opt_type`= #{optType},
            `org_type` = #{orgType},
            `operator_id` = #{operatorId},
            <if test="assignTime != null">
                `assign_time` = #{assignTime}
            </if>
        </set>
        WHERE `user_id`= #{userId}
    </update>

    <update id="autoOffline">
        update
        <include refid="table_name"/>
        <set>
            `staff_status` = 2,
            `operator_id` = 0,
            `opt_type` = 2
        </set>
    </update>


    <select id="groupByStatusAndOrgType" resultType="com.shuidihuzhu.cf.model.crowdfunding.workflow.StaffStatusAndNum">
        SELECT
        `staff_status` as staffStatus, `org_type` as orgType, count(*) as num
        FROM
        <include refid="table_name"/>
        <where>
            `update_time` &gt;= #{startTime}
        </where>
        group by `staff_status`, `org_type`
    </select>


    <select id="listByUserIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowStaffStatus">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        <where>
            <if test="userIds != null and userIds.size()>0">
                `user_id` in
                <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                    #{userId}
                </foreach>
                and
            </if>
            `is_delete` = 0
        </where>
    </select>

    <select id="listByUserIdsAndStatus" resultType="com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowStaffStatus">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        <where>
            <if test="userIds != null and userIds.size()>0">
                `user_id` in
                <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                    #{userId}
                </foreach>
                and
            </if>

            <if test="staffStatus>0">
                staff_status = #{staffStatus}
                and
            </if>

            `is_delete` = 0

            order by staff_status

        </where>
    </select>

    <select id="findByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowStaffStatus">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        <where>
            `user_id` = #{userId} and `is_delete` = 0
        </where>
    </select>
</mapper>