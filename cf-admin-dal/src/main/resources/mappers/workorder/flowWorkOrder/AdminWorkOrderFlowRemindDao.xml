<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.admin.AdminWorkOrderFlowRemindDao">

    <sql id="table_name">
        `admin_work_order_flow_remind`
    </sql>

    <sql id="insert_fields">
        `flow_id`,
        `work_order_flow_id`,
        `current_operator_id`,
        `current_operator_name`,
        `operator_role_name`,
        `remind_operator_id`,
        `remind_operator_name`,
        `remind_operator_org`,
        `comment`
    </sql>

    <sql id="select_fields">
        `id`,
        `flow_id` as flowId,
        `work_order_flow_id` as workOrderFlowId,
        `current_operator_id` as currentOperatorId,
        `current_operator_name` as currentOperator<PERSON><PERSON>,
        `operator_role_name` as operatorRoleName,
        `remind_operator_id` as remindOperatorId,
        `remind_operator_name`as remindOperator<PERSON><PERSON>,
        `remind_operator_org` as remindOperator<PERSON>rg,
        `comment` as comment,
        `create_time` as createTime
    </sql>

    <insert id="add">
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES (#{flowId},#{workOrderFlowId},#{currentOperatorId},#{currentOperatorName},#{operatorRoleName},#{remindOperatorId},#{remindOperatorName},
        #{remindOperatorOrg},#{comment})
    </insert>


    <select id="countByUserId" resultType="java.lang.Integer">
        SELECT count(*)
        FROM
        <include refid="table_name"/>
        WHERE
        current_operator_id = #{handleUserId} AND create_time &gt;= #{createTime}
    </select>


    <select id="listByFlowId" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderFlowRemindRecord">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE
        flow_id = #{flowId} order by id desc
    </select>

    <select id="listByFlowIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderFlowRemindRecord">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        <where>
            flow_id in
            <foreach collection="flowIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            limit 200;
        </where>
    </select>

    <select id="listByUserIdsAndTime" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderFlowRemindRecord">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        <where>
            current_operator_id in
            <foreach collection="currentOperators" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="startTime != null">
                and create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and create_time &lt;= #{endTime}
            </if>
            limit 200;
        </where>
    </select>

</mapper>