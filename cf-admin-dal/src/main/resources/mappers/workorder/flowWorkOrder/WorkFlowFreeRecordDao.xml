<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.WorkFlowFreeRecordDao">

    <sql id="table_name">
        `work_flow_free_time_record`
    </sql>

    <sql id="insert_fields">
        `user_id`,
        `org_id`,
        `free_start_time`,
        `free_end_time`
    </sql>


    <insert id="batchAdd">
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES
        <foreach collection="records" item="item" separator=",">
            (#{item.userId}, #{item.orgId}, #{item.freeStartTime}, #{item.freeEndTime})
        </foreach>
    </insert>



    <select id="count" resultType="java.lang.Integer">
        SELECT count(*)
        FROM
        <include refid="table_name"/>
        <where>
            <if test="orgId != null and orgId > 0">
                `org_id` = #{orgId} and
            </if>
            <if test="userIds != null and userIds.size()>0">
                `user_id` in
                <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                    #{userId}
                </foreach>
                and
            </if>
            <if test="startTime != null">
                `free_start_time` &gt;= #{startTime} and
            </if>
            <if test="endTime != null">
                `free_end_time`&lt;= #{endTime}
            </if>
        </where>
    </select>

</mapper>