<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.admin.AdminWorkOrderFlowRecordDao">

    <sql id="table_name">
        `admin_work_order_flow_record`
    </sql>

    <sql id="insert_fields">
        `flow_id`,
        `work_order_id`,
        `problem_type`,
        `current_problem_type`,
        `order_operator_id`,
        `problem_content`,
        `problem_img`,
        `case_id`,
        `operator_id`,
        `comment`,
        `operate_type`,
        `level`,
        `second_classify_id`,
        `encrypt_mobile`
    </sql>

    <sql id="select_fields">
        `id`,
        `flow_id`,
        `work_order_id`,
        `problem_type`,
        `current_problem_type`,
        `order_operator_id`,
        `problem_content`,
        `problem_img`,
        `case_id`,
        `operator_id`,
        `comment`,
        `operate_type`,
        `create_time`,
        `update_time`,
        `level`,
        `second_classify_id`,
        `encrypt_mobile`
    </sql>

    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderFlowRecord">
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES (#{flowId},#{workOrderId},#{problemType},#{currentProblemType},#{orderOperatorId},#{problemContent},#{problemImg},
        #{caseId},#{operatorId},#{comment}, #{operateType}, #{level}, #{secondClassifyId},#{encryptMobile})
    </insert>

    <insert id="insertList">
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.flowId},#{item.workOrderId},#{item.problemType},#{item.currentProblemType},#{item.orderOperatorId},#{item.problemContent},
            #{item.problemImg},#{item.caseId},#{item.operatorId}, #{item.comment}, #{item.operateType},
            #{item.level}, #{item.secondClassifyId},#{item.encryptMobile})
        </foreach>
    </insert>

    <select id="selectAllByWorkOrderId" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderFlowRecord">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE `work_order_id`=#{workOrderId}
        <if test="operateTypeCodes != null and operateTypeCodes.size() > 0">
            AND operate_type IN
            <foreach collection="operateTypeCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>


    <select id="selectByOperatorIdsAndTime"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderFlowRecord">
        SELECT
        afr.`work_order_id`,
        afr.`problem_type`,
        afr.`current_problem_type`,
        afr.`order_operator_id`,
        afr.`case_id`,
        afr.`operator_id`,
        afr.`operate_type`,
        afr.level,
        afr.second_classify_id,
        afr.create_time
        FROM
        <include refid="table_name"/> afr
        LEFT JOIN admin_work_order_flow af ON afr.flow_id = af.id
        LEFT JOIN admin_work_order ao ON af.work_order_id = ao.id
        WHERE
        afr.create_time <![CDATA[ >= ]]> #{beginTime}
        AND
        afr.create_time <![CDATA[ <= ]]> #{endTime}
        <if test="operatorIds != null and operatorIds.size() >0">
            AND afr.operator_id IN
            <foreach collection="operatorIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="problemTypeCodes != null and problemTypeCodes.size() >0">
            AND afr.problem_type IN
            <foreach collection="problemTypeCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND afr.operate_type IN
        <foreach collection="operateTypeCodes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="level != -1">
            AND ao.level = #{level}
        </if>
        <if test="secondLevelClassifyIds != null and secondLevelClassifyIds.size() > 0">
            AND af.second_classify_id IN
            <foreach collection="secondLevelClassifyIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectWithAssignRecords" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderFlowRecord">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE
        create_time <![CDATA[ >= ]]> #{beginTime}
        AND
        create_time <![CDATA[ <= ]]> #{endTime}
        AND
        order_operator_id = #{orderOperatorId}
        AND
        operate_type in(4, 33)
    </select>
</mapper>