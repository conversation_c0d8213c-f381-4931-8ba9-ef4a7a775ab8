<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.admin.workorder.flowWorkOrder.WorkFlowTypePropertyDAO">

    <sql id="table_name">
      work_flow_type_property
    </sql>


    <insert id = "insertPropertys" parameterType = "com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowTypeProperty">
      INSERT INTO
      <include refid="table_name"/>
        (`flow_type`, `type_id`, `property_type`, `property_value`)
       VALUES
       <foreach collection="propertyList" item="item" separator=",">
           (#{item.flowType}, #{item.typeId}, #{item.propertyType}, #{item.propertyValue})
       </foreach>
    </insert>

    <select id = "selectPropertyList" resultType = "com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowTypeProperty">
      SELECT *
      FROM <include refid="table_name"/>
      WHERE
      `flow_type` = #{flowType}
      AND
      `type_id` = #{typeId}
      AND
      `property_type` = #{propertyType}
      AND is_delete = 0
    </select>

    <update id = "deletePropertyList">
        UPDATE
        <include refid="table_name"/>
        SET is_delete = 1
        WHERE
        `flow_type` = #{flowType}
        AND
        `type_id` = #{typeId}
        AND
        `property_type` = #{propertyType}
    </update>


    <select id = "selectPropertyLists" resultType = "com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowTypeProperty">
        SELECT *
        FROM <include refid="table_name"/>
        WHERE
        `flow_type` = #{flowType}
        AND
        `type_id` IN
        <foreach collection="typeIds" item="typeId" open="(" separator="," close=")">
            #{typeId}
        </foreach>
        AND
        `property_type` = #{propertyType}
        AND is_delete = 0
    </select>
	
	<select id = "selectPropertyListsV2" resultType = "com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowTypeProperty">
		SELECT *
		FROM <include refid="table_name"/>
		WHERE
		`flow_type` = #{flowType}
		AND
		`type_id` IN
		<foreach collection="typeIds" item="typeId" open="(" separator="," close=")">
			#{typeId}
		</foreach>
		AND
		`property_type` IN
		<foreach collection="propertyTypes" item="propertyType" open="(" separator="," close=")">
			#{propertyType}
		</foreach>
		AND is_delete = 0
	</select>
</mapper>
