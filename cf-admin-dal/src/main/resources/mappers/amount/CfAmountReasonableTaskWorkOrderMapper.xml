<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.amount.CfAmountReasonableTaskWorkOrderDao">
    <sql id="TABLE_NAME">
        cf_amount_reasonable_task_work_order
    </sql>

    <sql id="INSERT_FIELDS">
        (`task_id`, `case_id`, `work_order_id`, `task_type`,`content`,`images`,`task_plan`,`task_status`,`task_after_days`, `submit_time`,`amount_start`, `amount_end`)
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.client.adminpure.model.amount.CfAmountReasonableTaskWorkOrder" keyProperty="id" useGeneratedKeys="true">
        insert into <include refid="TABLE_NAME"/>
        <include refid="INSERT_FIELDS"/>
        values(#{taskId}, #{caseId}, #{workOrderId}, #{taskType}, #{content}, #{images}, #{taskPlan}, #{taskStatus}, #{taskAfterDays}, #{submitTime}, #{amountStart}, #{amountEnd})
    </insert>


    <select id="selectByWorkOrderIdList" resultType="com.shuidihuzhu.cf.client.adminpure.model.amount.CfAmountReasonableTaskWorkOrder">
        SELECT *
        FROM <include refid="TABLE_NAME"/>
        WHERE `is_delete`=0 AND `work_order_id` IN
        <foreach collection="workOrderIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByCaseId" resultType="com.shuidihuzhu.cf.client.adminpure.model.amount.CfAmountReasonableTaskWorkOrder">
        select * from <include refid="TABLE_NAME"/>
        where case_id = #{caseId} order by id desc
    </select>

    <select id="selectById" resultType="com.shuidihuzhu.cf.client.adminpure.model.amount.CfAmountReasonableTaskWorkOrder">
        select * from <include refid="TABLE_NAME"/>
        where id = #{id}
    </select>

    <select id="selectByWorkOrderId" resultType="com.shuidihuzhu.cf.client.adminpure.model.amount.CfAmountReasonableTaskWorkOrder">
        select * from <include refid="TABLE_NAME"/>
        where work_order_id = #{workOrderId}
        LIMITT 1
    </select>

    <update id="updateCfAmountReasonableTaskWorkOrder" parameterType="com.shuidihuzhu.cf.client.adminpure.model.amount.CfAmountReasonableTask">
        update <include refid="TABLE_NAME"/>
        <set>
            <if test="content != null and content != ''">
                content = #{content},
            </if>
            <if test="images != null">
                images = #{images},
            </if>
            <if test="taskPlan != null and taskPlan > 0">
                task_plan = #{taskPlan},
            </if>
            <if test="taskAfterDays != null and taskAfterDays > 0">
                task_after_days = #{taskAfterDays},
            </if>
            <if test="taskStatus != null and taskStatus > 0">
                task_status = #{taskStatus},
            </if>
            <if test="submitTime != null and submitTime > 0">
                submit_time = #{submitTime},
            </if>
            <if test="rejectReason != null">
                reject_reason = #{rejectReason},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="amountStart != null">
                amount_start = #{amountStart},
            </if>
            <if test="amountEnd != null">
                amount_end = #{amountEnd},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>