<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.amount.CfAmountReasonableTaskDao">
    <sql id="TABLE_NAME">
        cf_amount_reasonable_task
    </sql>

    <sql id="INSERT_FIELDS">
        (`task_info_id`, `case_id`, `task_type`)
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.client.adminpure.model.amount.CfAmountReasonableTask" keyProperty="id" useGeneratedKeys="true">
        insert into <include refid="TABLE_NAME"/>
        <include refid="INSERT_FIELDS"/>
        values(#{taskInfoId}, #{caseId}, #{taskType})
    </insert>


    <select id="getByCaseIdAndTaskType" resultType="com.shuidihuzhu.cf.client.adminpure.model.amount.CfAmountReasonableTask">
        select * from <include refid="TABLE_NAME"/>
        where case_id = #{caseId} and task_type = #{taskType}
    </select>

    <select id="getByTaskInfoId" resultType="com.shuidihuzhu.cf.client.adminpure.model.amount.CfAmountReasonableTask">
        select * from <include refid="TABLE_NAME"/>
        where task_info_id = #{taskInfoId}
    </select>

    <select id="getByCaseId" resultType="com.shuidihuzhu.cf.client.adminpure.model.amount.CfAmountReasonableTask">
        select * from <include refid="TABLE_NAME"/>
        where case_id = #{caseId} and is_delete = 0
    </select>

    <update id="updateCfAmountReasonableTask" parameterType="com.shuidihuzhu.cf.client.adminpure.model.amount.CfAmountReasonableTask">
        update <include refid="TABLE_NAME"/>
        <set>
            <if test="content != null and content != ''">
                content = #{content},
            </if>
            <if test="images != null">
                images = #{images},
            </if>
            <if test="taskPlan != null and taskPlan > 0">
                task_plan = #{taskPlan},
            </if>
            <if test="taskAfterDays != null and taskAfterDays > 0">
                task_after_days = #{taskAfterDays},
            </if>
            <if test="taskStatus != null and taskStatus > 0">
                task_status = #{taskStatus},
            </if>
            <if test="submitTime != null and submitTime > 0">
                submit_time = #{submitTime},
            </if>
            <if test="rejectReason != null">
                reject_reason = #{rejectReason},
            </if>
            <if test="amountStart != null">
                amount_start = #{amountStart},
            </if>
            <if test="amountEnd != null">
                amount_end = #{amountEnd},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="selectById" resultType="com.shuidihuzhu.cf.client.adminpure.model.amount.CfAmountReasonableTask">
        select * from <include refid="TABLE_NAME"/>
        where id = #{id}
    </select>

</mapper>