<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.report.ReportScheduleDAO">

    <sql id="table_name">
        `report_schedule`
    </sql>

    <sql id="select_fields">
        `id`,
        <include refid="insert_Column_List">
        </include>
    </sql>

    <sql id="insert_Column_List">
        `case_id`,
        `operator_id`,
        `done`,
        `target_time`,
        `version`
	</sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        insert into
        <include refid="table_name">
        </include>
        (<include refid="insert_Column_List"/>)
        values
        (
        #{caseId},
        #{operatorId},
        #{done},
        #{targetTime},
        #{version}
        )
    </insert>

    <select id="getById" resultType="com.shuidihuzhu.cf.model.report.schedule.ReportScheduleDO">
        select
        <include refid="select_fields">
        </include>
        from
        <include refid="table_name">
        </include>
        <where>
            id = #{id}
            and now() <![CDATA[ < ]]> target_time
            and disable = 0
        </where>
    </select>

    <update id="updateTargetTimeById">
        update
        <include refid="table_name">
        </include>
        <set>
            target_time = #{targetTime}
            ,version = #{version}
        </set>
        <where>
            id = #{id}
        </where>
    </update>

    <update id="removeById">
        update
        <include refid="table_name">
        </include>
        <set>
            disable = 1
        </set>
        <where>
            id = #{id}
        </where>
    </update>

    <update id="doneById">
        update
        <include refid="table_name">
        </include>
        <set>
            done = 1
        </set>
        <where>
            id = #{id}
        </where>
    </update>

    <select id="getLastByCaseId" resultType="com.shuidihuzhu.cf.model.report.schedule.ReportScheduleDO">
        select
        <include refid="select_fields">
        </include>
        from
        <include refid="table_name">
        </include>
        <where>
            and case_id = #{caseId}
            and now() <![CDATA[ < ]]> target_time
            and disable = 0
        </where>
        order by id desc
        limit 1
    </select>

    <select id="getListByCaseIdList" resultType="com.shuidihuzhu.cf.model.report.schedule.ReportScheduleDO">
        select
        <include refid="select_fields">
        </include>
        from
        <include refid="table_name">
        </include>
        <where>
            case_id in
            <foreach collection="caseIdList" item="caseId" separator="," open="(" close=")">
                #{caseId}
            </foreach>
            and target_time <![CDATA[ > ]]> now()
            and target_time <![CDATA[ < ]]> #{targetTimeEnd}
            and disable = 0
        </where>
    </select>
</mapper>