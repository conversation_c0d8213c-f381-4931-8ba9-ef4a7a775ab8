<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.ai.AiStagingDao">

    <sql id="table_name">
        `ai_staging_info`
    </sql>

    <sql id="insert_Column_List">
        `clew_id`,
        `staging_type`,
        `staging_base_info`,
        `operator_id`
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.ai.DO.AiStagingDO">
        insert into
        <include refid="table_name"/>
        (<include refid="insert_Column_List"/>) values (#{clewId}, #{stagingType}, #{stagingBaseInfo}, #{operatorId})
    </insert>

    <select id="selectStagingInfo" resultType="com.shuidihuzhu.cf.model.ai.DO.AiStagingDO">
        SELECT * FROM <include refid="table_name"/>
        where `clew_id` = #{clewId} and `staging_type` = #{stagingType}
        limit 1
    </select>

    <update id="update" parameterType="com.shuidihuzhu.cf.model.ai.DO.AiStagingDO">
        update <include refid="table_name"/> set
        `staging_base_info` = #{stagingBaseInfo},
        `operator_id` = #{operatorId}
        where `clew_id` = #{clewId} and `staging_type` = #{stagingType}
    </update>

</mapper>