<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.ai.CfApproveAuditAiCallRecordDao">

    <sql id="table_name">
        `cf_approve_audit_ai_call_record`
    </sql>

    <sql id="insert_Column_List">
        `case_id`,
        `work_order_id`,
        `call_id`,
        `reject_ids`,
        `bot_id`
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.ai.CfApproveAuditAiCallRecord" useGeneratedKeys="true" keyProperty="id">
        insert into
        <include refid="table_name"/>
        (<include refid="insert_Column_List"/>)
        values (
        #{caseId},
        #{workOrderId},
        #{callId},
        #{rejectIds},
        #{botId}
        )
    </insert>

    <select id="getByCallId" resultType="com.shuidihuzhu.cf.model.ai.CfApproveAuditAiCallRecord">
        SELECT * FROM <include refid="table_name"/>
        <where>
            `call_id` = #{callId}
        </where>
        limit 1
    </select>

    <select id="getByWorkOrderId" resultType="com.shuidihuzhu.cf.model.ai.CfApproveAuditAiCallRecord">
        SELECT * FROM <include refid="table_name"/>
        <where>
            `work_order_id` = #{workOrderId}
        </where>
    </select>

    <update id="updateCallResCode">
        UPDATE <include refid="table_name"/>
        SET  `call_res_code`=#{callResCode}
        WHERE `id` = #{id}
    </update>

    <update id="updateCallIntention">
        UPDATE <include refid="table_name"/>
        SET  `call_intention`=#{callIntention}
        WHERE `id` = #{id}
    </update>

    <update id="updateCallStartTime">
        UPDATE <include refid="table_name"/>
        SET  `call_start_time`=#{callStartTime},`call_result`=#{callResult}
        WHERE `id` = #{id}
    </update>

    <update id="updateCallEndTime">
        UPDATE <include refid="table_name"/>
        SET  `call_end_time`=#{callEndTime},`call_result`=#{callResult}
        WHERE `id` = #{id}
    </update>
</mapper>