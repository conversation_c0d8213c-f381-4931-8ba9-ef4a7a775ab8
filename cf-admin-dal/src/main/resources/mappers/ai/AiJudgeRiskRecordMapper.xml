<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.ai.AiJudgeRiskRecordDao">

    <sql id="table_name">
        `ai_judge_risk_record`
    </sql>

    <sql id="Column_List">
        `case_id`,
        `scene_type`,
        `judge_type`,
        `model_type`,
        `hit_risk_factor`,
        `hit_risk_result`,
        `judge_content`,
        `judge_reason`,
        `bd_org`
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.ai.AiGenerateRecord">
        insert into
        <include refid="table_name"/>
        (<include refid="Column_List"/>)
        values (#{caseId}, #{sceneType}, #{judgeType}, #{modelType}, #{hitRiskFactor},
            #{hitRiskResult}, #{judgeContent}, #{judgeReason}, #{bdOrg})
    </insert>

</mapper>