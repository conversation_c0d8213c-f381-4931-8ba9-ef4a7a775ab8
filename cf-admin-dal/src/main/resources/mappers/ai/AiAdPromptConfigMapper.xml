<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.ai.AiAdPromptConfigDao">

    <sql id="table_name">
        `ai_ad_prompt_config`
    </sql>

    <sql id="insert_Column_List">
        `user_profile`,
        `ad_prompt`
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.ai.AiAdPromptConfig">
        insert into
        <include refid="table_name"/>
        (<include refid="insert_Column_List"/>) values (#{userProfile}, #{adPrompt})
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.cf.model.ai.AiAdPromptConfig">
        update <include refid="table_name"/>
        set ad_prompt = #{adPrompt}
        where user_profile = #{userProfile}
    </update>

    <select id="selectConfigByUserProfile" resultType="com.shuidihuzhu.cf.model.ai.AiAdPromptConfig">
        select * from
        <include refid="table_name"/>
        where user_profile = #{userProfile} and is_delete = 0
    </select>
</mapper>