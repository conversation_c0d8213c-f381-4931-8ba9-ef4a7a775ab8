<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.ai.AiGenerateDao">

    <sql id="table_name">
        `ai_generate_record`
    </sql>

    <sql id="insert_Column_List">
        `clew_id`,
        `generate_type`,
        `model_type`,
        `uuid`,
        `ai_generate_results`,
        `operator_id`
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.ai.AiGenerateRecord">
        insert into
        <include refid="table_name"/>
        (<include refid="insert_Column_List"/>) values (#{clewId}, #{generateType}, #{modelType}, #{uuid}, #{aiGenerateResults}, #{operatorId})
    </insert>

    <select id="selectRecordByUuid" resultType="com.shuidihuzhu.cf.model.ai.AiGenerateRecord">
        select * from
        <include refid="table_name"/>
        where uuid = #{uuid}
    </select>
</mapper>