<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.ai.AiUgcRiskCheckRecordDao">

    <sql id="table_name">
        `ai_ugc_risk_check_record`
    </sql>

    <insert id="insert">
        insert into <include refid="table_name"/>
        (biz_id, biz_type, score) values (#{bizId}, #{bizType}, #{score})
    </insert>
</mapper>