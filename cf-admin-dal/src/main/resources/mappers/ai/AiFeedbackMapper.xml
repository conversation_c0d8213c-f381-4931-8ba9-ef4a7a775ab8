<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.ai.AiFeedbackDao">

    <sql id="table_name">
        `ai_feedback_info`
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.ai.DO.FeedbackDO">
        insert into <include refid="table_name"/>
        (feedback_type, feedback_info, operator_id) values (#{feedbackType}, #{feedbackInfo}, #{operatorId})
    </insert>
</mapper>