<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.ai.AiPromptConfigDao">

    <sql id="table_name">
        `ai_prompt_config`
    </sql>

    <sql id="Column_List">
        `generate_type`,
        `model_type`,
        `biz_type`,
        `prompt`
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.ai.AiPromptConfig">
        insert into
        <include refid="table_name"/>
        (<include refid="Column_List"/>) values (#{generateType}, #{modelType}, #{bizType}, #{prompt})
    </insert>

    <update id="updatePrompt" parameterType="com.shuidihuzhu.cf.model.ai.AiPromptConfig">
        update <include refid="table_name"/>
        set prompt = #{prompt}
        where generate_type = #{generateType}
        and model_type = #{modelType}
        and biz_type = #{bizType}
    </update>

    <select id="selectByGenerateType" resultType="com.shuidihuzhu.cf.model.ai.AiPromptConfig">
        select <include refid="Column_List"/>
        from <include refid="table_name"/>
        where generate_type = #{generateType}
        and model_type = #{modelType}
        and biz_type = #{bizType}
        and is_delete = 0
        limit 1
    </select>

</mapper>