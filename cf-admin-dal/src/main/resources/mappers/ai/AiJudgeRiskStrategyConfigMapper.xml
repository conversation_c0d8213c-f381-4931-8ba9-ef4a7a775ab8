<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.ai.AiJudgeRiskStrategyConfigDao">

    <sql id="table_name">
        `ai_judge_risk_strategy_config`
    </sql>

    <sql id="Column_List">
        `scene_type`,
        `risk_factor`,
        `model_type`,
        `judge_prompt`,
        `judge_type`
    </sql>

    <select id="selectAllEnableConfig" resultType="com.shuidihuzhu.cf.model.ai.AiRiskStrategyConfig">
        select <include refid="Column_List"/>
        from <include refid="table_name"/>
        where judge_type = #{judgeType} and is_delete = 0
    </select>

    <select id="selectConfigByScene" resultType="com.shuidihuzhu.cf.model.ai.AiRiskStrategyConfig">
        select <include refid="Column_List"/>
        from <include refid="table_name"/>
        where is_delete = 0 and scene_type = #{sceneType}
        limit 1
    </select>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.ai.AiRiskStrategyConfig">
        insert into
        <include refid="table_name"/>
        (<include refid="Column_List"/>)
        values (#{sceneType}, #{riskFactor}, #{modelType}, #{judgePrompt}, #{judgeType})
    </insert>

    <update id="updateBySceneType">
        update <include refid="table_name"/>
        set risk_factor = #{riskFactor}, judge_prompt = #{judgePrompt}
        where scene_type = #{sceneType}
    </update>

</mapper>