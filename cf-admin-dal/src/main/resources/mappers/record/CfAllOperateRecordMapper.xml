<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.record.CfAllOperateRecordDao">


    <insert id="insert">
        INSERT INTO cf_all_operate_record(case_id,biz_id,biz_type,operate_type,content,page_type,operator_id,operator,department)
        VALUES(#{caseId},#{bizId},#{bizType},#{operateType},#{content},#{pageType},#{operatorId},#{operator},#{department})
    </insert>

    <insert id="insertBatch">
        INSERT INTO cf_all_operate_record(case_id,biz_id,biz_type,operate_type,content,page_type,operator_id,operator,department)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.caseId},#{item.bizId},#{item.bizType},#{item.operateType},
             #{item.content},#{item.pageType},#{item.operatorId},#{item.operator},#{item.department})
        </foreach>
    </insert>

    <select id="getListByCaseId" resultType="com.shuidihuzhu.cf.model.record.CfAllOperateRecordDo">
        SELECT case_id,biz_id,biz_type,operate_type,content,page_type,operator_id,operator,department,create_time FROM cf_all_operate_record
        <where>
            case_id = #{caseId}
            AND biz_type =#{bizType}
            AND biz_id =#{bizId}
            AND is_delete = 0
        </where>
    </select>


    <select id="getListByCaseIdAndBizType" resultType="com.shuidihuzhu.cf.model.record.CfAllOperateRecordDo">
        SELECT case_id,biz_id,biz_type,operate_type,content,page_type,operator_id,operator,department,create_time FROM cf_all_operate_record
        <where>
            case_id = #{caseId}
            AND biz_type =#{bizType}
            AND is_delete = 0
        </where>
    </select>
</mapper>