<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.record.TargetAmountAuditRecordDao">

    <sql id="TableName">
        `target_amount_audit_record`
    </sql>

    <sql id="BaseFiled">
        `id`,`case_id`,`work_order_id`,`min_cost_amount`,`max_cost_amount`,`reject_reason`,`is_reject`
    </sql>

    <sql id="BaseFiledWithoutId">
        `case_id`,`work_order_id`,`min_cost_amount`,`max_cost_amount`,`reject_reason`,`is_reject`
    </sql>

    <insert id="add" parameterType="com.shuidihuzhu.cf.model.record.TargetAmountAuditRecord">
        INSERT INTO<include refid="TableName"></include>
        (<include refid="BaseFiledWithoutId"></include>)
        VALUES(#{caseId},#{workOrderId},#{minCostAmount},#{maxCostAmount},#{rejectReason},#{isReject})
    </insert>

    <select id="getRecordByWorkOrderId" parameterType="long" resultType="com.shuidihuzhu.cf.model.record.TargetAmountAuditRecord">
        SELECT
        `id`,`case_id`,`work_order_id`,`min_cost_amount`,`max_cost_amount`,`reject_reason`,`create_time`,`update_time`,`is_reject`
        FROM <include refid="TableName"></include>
        <where>
            `work_order_id` = #{workerOrderId} AND `is_delete` = 0
        </where>
    </select>

    <select id="getRecordByCaseId" parameterType="long" resultType="com.shuidihuzhu.cf.model.record.TargetAmountAuditRecord">
        SELECT
        `id`,`case_id`,`work_order_id`,`min_cost_amount`,`max_cost_amount`,`reject_reason`,`create_time`,`update_time`,`is_reject`
        FROM <include refid="TableName"></include>
        <where>
            `case_id` = #{caseId} AND `is_delete` = 0
        </where>
    </select>

    <update id="deleteRecordByWorkOrderIdLogically" parameterType="long">
        UPDATE <include refid="TableName"></include>
        SET
        `is_delete` = 1
        <where>
            `work_order_id` = #{workOrderId}
        </where>
    </update>
</mapper>