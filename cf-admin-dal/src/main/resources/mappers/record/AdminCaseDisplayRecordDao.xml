<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.record.AdminCaseDisplayRecordDao">

    <sql id="table">
        case_display_record
    </sql>
    <sql id="select_fields">
        `id` as `id`,
        `case_id` as `caseId`,
        `before_setting` as `before`,
        `after_setting` as `after`,
        `unique_code` as `uniqueCode`,
        `operator_id` as `operatorId`,
        `update_channel` as `updateChannel`,
        `reason` as `reason`,
        `create_time` as `createTime`,
        `update_time` as `updateTime`
    </sql>
    <insert id="addOne" parameterType="com.shuidihuzhu.cf.model.record.AdminCaseDisplayRecordDo">
        insert into
        <include refid="table"/>
        (`case_id`,`before_setting`,`after_setting`,`unique_code`,`operator_id`, `update_channel`, `reason`)
        values(#{record.caseId}, #{record.before}, #{record.after}, #{record.uniqueCode}, #{record.operatorId}, #{record.updateChannel}, #{record.reason})
    </insert>

    <select id="selectByCaseId" resultType="com.shuidihuzhu.cf.model.record.AdminCaseDisplayRecordDo">
        select
        <include refid="select_fields"/>
        from
        <include refid="table"/>
        where `case_id` = #{caseId}
        and `is_delete` = 0
        order by id desc
    </select>
</mapper>