<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.record.CfCrowdfundingAttachmentRecordDao">

    <insert id="insertBatch">
        INSERT INTO cf_crowdfunding_attachment_record(case_id,`type`,attachment_id,valid)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.caseId},#{item.type},#{item.attachmentId},#{item.valid})
        </foreach>
    </insert>

    <select id="getListByCaseId" resultType="com.shuidihuzhu.cf.model.record.CfCrowdfundingAttachmentRecordDo">
        SELECT case_id,`type`,attachment_id,valid,create_time FROM cf_crowdfunding_attachment_record
        <where>
            case_id = #{caseId}
            AND `type` = #{type}
            AND is_delete = 0
        </where>
    </select>

</mapper>