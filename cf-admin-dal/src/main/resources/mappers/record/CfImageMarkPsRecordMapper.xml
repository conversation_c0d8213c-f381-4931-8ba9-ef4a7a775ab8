<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.record.CfImageMarkPsRecordDao">

    <sql id="table_name">
        `cf_image_mark_ps_record`
    </sql>

    <sql id="insert_Column_List">
        `case_id`,
        `attachment_id`,
        `recognition_ps`,
        `operator_id`
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.record.CfImageMarkPsRecord" useGeneratedKeys="true" keyProperty="id">
        insert into
        <include refid="table_name"/>
        (<include refid="insert_Column_List"/>)
        values (
        #{caseId},
        #{attachmentId},
        #{recognitionPs},
        #{operatorId}
        )
    </insert>

    <select id="getByAttachmentId" resultType="com.shuidihuzhu.cf.model.record.CfImageMarkPsRecord">
        SELECT * FROM <include refid="table_name"/>
        <where>
            `attachment_id` = #{attachmentId}
        </where>
        limit 1
    </select>

    <select id="getByCaseId" resultType="com.shuidihuzhu.cf.model.record.CfImageMarkPsRecord">
        SELECT * FROM <include refid="table_name"/>
        <where>
            `case_id` = #{caseId}
        </where>
    </select>

    <update id="updateById">
        UPDATE <include refid="table_name"/>
        SET  `recognition_ps`= #{recognitionPs}, `operator_id`= #{operatorId}
        WHERE `id` = #{id}
    </update>

</mapper>