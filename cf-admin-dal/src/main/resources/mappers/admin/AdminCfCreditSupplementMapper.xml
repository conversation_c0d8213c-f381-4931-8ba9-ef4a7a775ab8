<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.admin.AdminCfCreditSupplementDao">
    <sql id="tableName">
        `cf_credit_supplement`
    </sql>

    <sql id="select_fields">
        `id`,`case_id`,`info_uuid`,`count`,`property_type`,`total_value`,`status`,`sell_count`,`convention`,`sale_value`,`date_created`,`last_modified`
    </sql>

    <select id="selectByInfoUuid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfCreditSupplement">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="tableName"/>
        WHERE `info_uuid`=#{infoUuid}
        AND `is_delete`=0
    </select>

    <select id="selectByInfoUuidList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfCreditSupplement">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="tableName"/>
        WHERE
        `info_uuid` in
        <foreach collection="infoUuidList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND `is_delete`=0
    </select>
</mapper>