<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.admin.CaseLabelDao">

	<sql id="TABLE">
		cf_case_label_info
	</sql>
	<sql id="insert_fields">
		`priority`,
        `name`,
        `status`,
        `remarks`,
		`operator_id`,
		`rule_json`
	</sql>
	<sql id="select_fields">
		`id`,
		`priority`,
        `name`,
        `status`,
        `remarks`,
		`operator_id`,
		`rule_json`,
		`update_time`
	</sql>

	<insert id="addCaseLabel" parameterType="com.shuidihuzhu.cf.model.label.CfCaseLabelInfo" keyProperty="id" useGeneratedKeys="true">
		insert into <include refid="TABLE"/>
		(<include refid="insert_fields"/>)
		values
		(#{priority}, #{name}, #{status}, #{remarks}, #{operatorId}, #{ruleJson})
	</insert>

	<update id="updateCaseLabel" parameterType="com.shuidihuzhu.cf.model.label.CfCaseLabelInfo">
		update <include refid="TABLE"/>
		set `priority` = #{priority},
			`name` = #{name},
			`status` = #{status},
			`remarks` = #{remarks},
			`operator_id` = #{operatorId},
			`rule_json` = #{ruleJson}
		where id = #{id}
	</update>

	<select id="queryCaseLabels" resultType="com.shuidihuzhu.cf.model.label.CfCaseLabelInfo">
		select <include refid="select_fields"/>
		from <include refid="TABLE"/>
		<where>
			<if test="name != null and name != ''">
				AND name = #{name}
			</if>
			<if test="status != null">
				AND status = #{status}
			</if>
		</where>
		order by update_time desc
		limit #{offset}, #{limit}
	</select>

	<select id="queryCaseLabelById" resultType="com.shuidihuzhu.cf.model.label.CfCaseLabelInfo">
		select <include refid="select_fields"/>
		from <include refid="TABLE"/>
		where id = #{id} and is_delete = 0
	</select>

	<select id="queryCountByPriority" resultType="int">
		select count(1)
		from <include refid="TABLE"/>
		where priority = #{priority} and status = #{status} and id != #{id} and is_delete = 0
	</select>

	<select id="queryCaseLabelsCount" resultType="int">
		select count(1)
		from <include refid="TABLE"/>
		<where>
			<if test="name != null and name != ''">
				AND name = #{name}
			</if>
			<if test="status != null">
				AND status = #{status}
			</if>
		</where>
	</select>

	<select id="queryEnableCaseLabels" resultType="com.shuidihuzhu.cf.model.label.CfCaseLabelInfo">
		select <include refid="select_fields"/>
		from <include refid="TABLE"/>
		where status = 0 and is_delete = 0
		order by priority
	</select>

	<select id="queryAllCaseLabels" resultType="com.shuidihuzhu.cf.model.label.CfCaseLabelInfo">
		select <include refid="select_fields"/>
		from <include refid="TABLE"/>
		where is_delete = 0
		limit 200
	</select>

	<select id="getCaseLabelByName" resultType="com.shuidihuzhu.cf.model.label.CfCaseLabelInfo">
		select <include refid="select_fields"/>
		from <include refid="TABLE"/>
		where name = #{name}
		and is_delete = 0 limit 1
	</select>

	<select id="getRepeatCaseLabelByName" resultType="com.shuidihuzhu.cf.model.label.CfCaseLabelInfo">
		select <include refid="select_fields"/>
		from <include refid="TABLE"/>
		<where>
			<if test="id != null">
				AND id != #{id}
			</if>
			<if test="name != null and name != ''">
				AND `name` = #{name}
			</if>
		</where>
		and is_delete = 0
	</select>

	<select id="getCaseLabelByNameList" resultType="com.shuidihuzhu.cf.model.label.CfCaseLabelInfo">
		select <include refid="select_fields"/>
		from <include refid="TABLE"/>
		where name in
		<foreach collection="nameList" item="name" open="(" separator="," close=")">
			#{name}
		</foreach>
		and is_delete = 0
	</select>

	<update id="updateStatusById">
		update <include refid="TABLE"/>
		set status = #{status}, operator_id = #{userId}
		where id = #{id}
	</update>

</mapper>
