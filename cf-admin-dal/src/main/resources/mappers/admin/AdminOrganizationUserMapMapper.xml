<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.admin.AdminOrganizationUserMapDao">
    <sql id="TABLE_NAME">
		admin_organization_user_map
	</sql>

    <sql id="INSERT_FIELDS">
		(`user_id`, `org_id`)
	</sql>

    <sql id="SELECT_FIELDS">
		`id`, `org_id`, `user_id`, `is_manager`, `is_delete`, `create_time`, `update_time`
	</sql>

    <insert id="addEmployeeToNode">
        insert into <include refid="TABLE_NAME"/>
        <include refid="INSERT_FIELDS"/>
        values (#{uid}, #{organizationId})
    </insert>

    <update id="setManagerOfNode">
      update <include refid="TABLE_NAME"/>
      set is_manager = 1
      where user_id = #{uid} and org_id = #{organizationId}
      and is_delete = 0
    </update>

    <update id="deleteEmployeeFromNode">
        update <include refid="TABLE_NAME"/>
        set is_delete = 1
        where user_id = #{uid} and org_id = #{organizationId}
        and is_delete = 0
    </update>

    <select id="getByUserIdAndOrganizationId" resultType="com.shuidihuzhu.cf.model.admin.AdminOrganizationUserMap">
        select <include refid="SELECT_FIELDS"/>
        from <include refid="TABLE_NAME"/>
        where user_id = #{uid} and org_id = #{organizationId}
        and is_delete = 0
    </select>

    <select id="getByOrgId" resultType="com.shuidihuzhu.cf.model.admin.AdminOrganizationUserMap">
        select <include refid="SELECT_FIELDS"/>
        from <include refid="TABLE_NAME"/>
        where is_delete = 0 and org_id = #{organizationId}
    </select>

    <select id="countByOrgIds" resultType="java.lang.Long">
        select count(*)
        from <include refid="TABLE_NAME"/>
        where is_delete = 0 and org_id in
        <foreach collection="organizationIds" item="item" open="(" separator="," close=")" >
            #{item}
        </foreach>
    </select>

    <select id="getLowestOrgByUserIds" resultType="com.shuidihuzhu.cf.model.admin.AdminOrganizationUserMap">
        select <include refid="SELECT_FIELDS"/>
        from <include refid="TABLE_NAME"/>
        where is_delete = 0 and user_id
        in
        <foreach collection="userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getByOrgIds" resultType="com.shuidihuzhu.cf.model.admin.AdminOrganizationUserMap">
        select <include refid="SELECT_FIELDS"/>
        from <include refid="TABLE_NAME"/>
        where org_id
        in
        <foreach collection="organizationIds" item="item" open="(" separator="," close=")" >
            #{item}
        </foreach>
        and
        is_delete = 0
    </select>

</mapper>