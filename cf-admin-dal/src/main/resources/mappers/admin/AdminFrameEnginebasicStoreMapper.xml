<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.admin.AdminFrameEnginebasicStoreDao">
    <sql id="tableName">
        frame_engine_basic_store
    </sql>

    <update id="updateContentByActivityId">
        update <include refid="tableName"/>
        set content = #{content}
        where activity_id = #{activityId}
    </update>
</mapper>