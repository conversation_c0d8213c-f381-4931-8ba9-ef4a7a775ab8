<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.admin.AdminCrowdfundingSendMsgTemplateDao">

    <sql id="TABLE">
        crowdfunding_msg_content
    </sql>
    <sql id="QUERY_FIELDS">
        `id`,
        `key`,
        `content`,
        `type`,
        `name`,
        `date_created` as dateCreated,
        `last_modified` as lastModified
    </sql>

    <sql id="insert_fields">
        `key`,
        `name`,
        `content`,
    </sql>


    <select id="getByKey" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingMsgContent">
        SELECT <include refid="QUERY_FIELDS" />
        FROM <include refid="TABLE" />
        WHERE `key`=#{key}
    </select>

    <select id="getAllMsgTemplate" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingMsgContent">
        SELECT <include refid="QUERY_FIELDS" />
        FROM <include refid="TABLE" />
        WHERE `type`=1
    </select>

    <update id="updateMsgTemplate" >
        UPDATE <include refid="TABLE"/>
        SET  `content`=#{content},`name`=#{name}
        WHERE `key`=#{key}
    </update>



    <select id="getMsgTitleByContent" resultType="java.lang.String">
        SELECT `name`
        FROM <include refid="TABLE" />
        WHERE `type`=1
        AND `content` = #{message}
    </select>





    <sql id = "table_v1">
      crowdfunding_sms_template_settings
    </sql>

    <insert id = "addSmsTemplate"  parameterType="com.shuidihuzhu.cf.model.crowdfunding.AdminSmsTemplateSettingsInfo$SmsSettings"
            useGeneratedKeys="true" keyProperty="id">
      INSERT INTO <include refid="table_v1"/>
        (`sms_group`, `model_num`, `priority`, `operator_id`)
      values(#{smsGroup}, #{modelNum}, #{priority}, #{operatorId})
        ON DUPLICATE KEY UPDATE
        `priority`=#{priority},
        `operator_id`=#{operatorId},
        `is_delete`=#{isDelete}
    </insert>

    <update id = "updateStatusById">
      UPDATE <include refid="table_v1"/>
      SET is_delete = #{dataStatus}
      WHERE id = #{id}
    </update>

    <update id = "updatePriorityById">
        UPDATE <include refid="table_v1"/>
        SET priority = #{priority}
        WHERE id = #{id}
    </update>

    <select id = "selectTemplateByParam" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminSmsTemplateSettingsInfo$SmsSettings">
        SELECT * FROM
        <include refid="table_v1"/>
        <where>
            <if test="smsGroup != null and smsGroup != 0">
                AND sms_group = #{smsGroup}
            </if>
            <if test="modelNum != null and modelNum != ''">
                AND model_num = #{modelNum}
            </if>
            <if test="operateId != null and operateId != 0">
                AND operator_id = #{operateId}
            </if>
            <if test = "list != null and list.size() > 0">
                AND  is_delete IN
                <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id = "selectTemplateByIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminSmsTemplateSettingsInfo$SmsSettings">
        SELECT * FROM
        <include refid="table_v1"/>
        WHERE
        id in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>

    </select>

    <insert id="addSmsTemplateRecord"  parameterType="com.shuidihuzhu.cf.model.crowdfunding.AdminSmsTemplateSettingsInfo$SmsSettingsRecord">
        INSERT INTO
        crowdfunding_sms_template_settings_record
        (`sms_template_settings_id`, `operate_type`, `operator_id`)
        values(#{smsTemplateSettingsId}, #{operateType}, #{operatorId})
    </insert>

    <select id = "selectRecordByTemplateId" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminSmsTemplateSettingsInfo$SmsSettingsRecord">
        SELECT * FROM
        crowdfunding_sms_template_settings_record
        WHERE
        sms_template_settings_id = #{smsTemplateSettingsId}
        ORDER BY create_time
    </select>

    <select id = "selectValidTemplateByGroups" resultType="com.shuidihuzhu.cf.model.crowdfunding.AdminSmsTemplateSettingsInfo$SmsSettings" >
        SELECT * FROM
        crowdfunding_sms_template_settings
        WHERE
        sms_group IN
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
        AND is_delete = 0
    </select>


</mapper>