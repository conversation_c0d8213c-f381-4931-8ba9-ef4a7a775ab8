<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.admin.CfScheduleAlarmDataSourceDao">
    <insert id="insert" parameterType="com.shuidihuzhu.cf.domain.cf.CfScheduleAlarmDataSource" useGeneratedKeys="true" keyProperty="id">
        insert into cf_schedule_alarm_data_source(name, config_property, operator_id, create_time)
        values(#{name}, #{configProperty}, #{operatorId}, now())
    </insert>

    <select id="getAll" resultType="com.shuidihuzhu.cf.domain.cf.CfScheduleAlarmDataSource">
        select * from cf_schedule_alarm_data_source
        where is_delete = 0
    </select>

</mapper>
