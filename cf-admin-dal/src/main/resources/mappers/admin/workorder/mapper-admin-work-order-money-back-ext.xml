<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.admin.workorder.AdminWorkOrderMoneyBackExtDAO">

    <sql id="tableName">
        admin_work_order_money_back_ext
    </sql>

    <sql id="Base_Column_List">
        id,
        case_id,
        amount,
        work_order_id,
        encrypt_mobile,
        create_time,
        update_time,
        is_delete
    </sql>

    <sql id="insert_Column_List">
        case_id,
        amount,
        work_order_id,
        encrypt_mobile
    </sql>

    <insert id="save" parameterType="com.shuidihuzhu.cf.model.admin.AdminWorkOrderMoneyBackExt">
        <selectKey order="AFTER" resultType="long" keyProperty="id">
            select last_insert_id() as id
        </selectKey>
        INSERT INTO <include refid="tableName"/>
        (<include refid="insert_Column_List"/>)
        VALUES(
            #{caseId},
            #{amount},
            #{workOrderId},
            #{encryptMobile}
        )
    </insert>

    <insert id="batchSave" parameterType="java.util.List">
        INSERT INTO <include refid="tableName"/>
        (<include refid="insert_Column_List"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.caseId},
                #{item.amount},
                #{item.workOrderId},
                #{item.encryptMobile}
            )
        </foreach>
    </insert>

    <select id="findByWorkOrderId" resultType="com.shuidihuzhu.cf.model.admin.AdminWorkOrderMoneyBackExt">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM 
        <include refid="tableName"/>
        WHERE 
            `work_order_id` = #{workOrderId}
            AND `is_delete` = 0
    </select>

    <select id="findByCaseId" resultType="com.shuidihuzhu.cf.model.admin.AdminWorkOrderMoneyBackExt">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM 
        <include refid="tableName"/>
        WHERE 
            `case_id` = #{caseId}
            AND `is_delete` = 0
    </select>

    <select id="findByWorkOrderIdAndCaseId" resultType="com.shuidihuzhu.cf.model.admin.AdminWorkOrderMoneyBackExt">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM 
        <include refid="tableName"/>
        WHERE 
            `work_order_id` = #{workOrderId}
            AND `case_id` = #{caseId}
            AND `is_delete` = 0
        LIMIT 1
    </select>

</mapper> 