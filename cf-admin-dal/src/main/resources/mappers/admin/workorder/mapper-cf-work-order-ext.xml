<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.admin.workorder.AdminWorkOrderExtDAO">

    <sql id="tableName">
		cf_work_order_ext
	</sql>

    <sql id="Base_Column_List">
		id,
		case_id,
		work_order_id,
		content,
		content_type,
		create_time
	</sql>

    <sql id="insert_Column_List">
		case_id,
		work_order_id,
		content,
		content_type
	</sql>

    <insert id="save"
            parameterType="com.shuidihuzhu.cf.domain.cf.WorkOrderExt">
        <selectKey order="AFTER" resultType="long" keyProperty="id" >
            select last_insert_id() as id
        </selectKey>
        replace into <include refid="tableName"/>
        (<include refid="insert_Column_List" />)
        values(
        #{caseId} ,
        #{workOrderId},
        #{content},
        #{contentType}
        )
    </insert>

    <select id="get" resultType="com.shuidihuzhu.cf.domain.cf.WorkOrderExt">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="tableName"/>
        <where>
            `work_order_id` = #{workOrderId}
            AND `content_type` = #{contentType}
            AND `is_delete` = 0
        </where>
        limit 1
    </select>

    <select id="listByCaseIdAndType" resultType="com.shuidihuzhu.cf.domain.cf.WorkOrderExt">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="tableName"/>
        <where>
            `case_id` = #{caseId}
            AND `content_type` = #{contentType}
            AND `is_delete` = 0
        </where>
    </select>

    <select id="listNearlyByCaseIdAndType" resultType="com.shuidihuzhu.cf.domain.cf.WorkOrderExt">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="tableName"/>
        <where>
            `case_id` = #{caseId}
            AND `content_type` = #{contentType}
            AND `is_delete` = 0
        </where>
        ORDER BY id desc limit 1
    </select>

</mapper>
