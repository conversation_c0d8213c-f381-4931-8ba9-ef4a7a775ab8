<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.admin.AdminWorkOrderRecordDao">

    <sql id="table_name">
        `admin_work_order_record`
    </sql>

    <sql id="insert_fields">
        `work_order_id`,
        `order_type`,
        `order_task`,
        `creator_type`,
        `creator_id`,
        `operator_role`,
        `operator_id`,
        `order_status`,
        `handle_result`,
        `comment`,
        `task_type`,
        `operate_type`
    </sql>

    <sql id="insertFields">
        `work_order_id`,
        `order_type`,
        `order_task`,
        `creator_type`,
        `creator_id`,
        `operator_role`,
        `operator_id`,
        `order_status`,
        `handle_result`,
        `comment`,
        `task_type`,
        `operate_type`
    </sql>

    <sql id="select_fields">
        `id`,
        `work_order_id`,
        `order_type`,
        `order_task`,
        `creator_type`,
        `creator_id`,
        `operator_role`,
        `operator_id`,
        `order_status`,
        `handle_result`,
        `comment`,
        `task_type`,
        `operate_type`
    </sql>

    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.admin.AdminWorkOrderRecord">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES
        (#{workOrderId},#{orderType},#{orderTask},#{creatorType},#{creatorId},#{operatorRole},
        #{operatorId},#{orderStatus},#{handleResult},#{comment}, #{taskType}, #{operateType})
    </insert>

    <insert id="insertList" parameterType="com.shuidihuzhu.cf.model.admin.AdminWorkOrderRecord">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insertFields"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.workOrderId},#{item.orderType},#{item.orderTask},#{item.creatorType},#{item.creatorId},
            #{item.operatorRole},#{item.operatorId},#{item.orderStatus},#{item.handleResult},#{item.comment}, #{item.taskType},
            #{item.operateType})
        </foreach>
    </insert>

    <select id="selectByWorkIdAndOperateTypes" resultType="com.shuidihuzhu.cf.model.admin.AdminWorkOrderRecord">
        SELECT *
        FROM <include refid="table_name"/>
        WHERE `work_order_id` IN
        <foreach collection="workOrderIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="operateTypes != null and operateTypes.size() > 0">
        AND  operate_type IN
            <foreach collection="operateTypes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND `is_delete`= 0
    </select>



    <update id="deleteFirstUgcById">
        UPDATE <include refid="table_name"/>
        SET `is_delete`= 1
        WHERE
        id = #{id}
    </update>

</mapper>
