<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.admin.AdminWorkOrderDao">

    <sql id="table_name">
        `admin_work_order`
    </sql>

    <sql id="insert_fields">
        `order_type`,
        `order_task`,
        `creator_type`,
        `creator_id`,
        `operator_id`,
        `order_status`,
        `handle_result`,
        `level`,
        `comment`,
        `task_type`
    </sql>

    <sql id="select_fields">
        `id`,
        `order_type`,
        `order_task`,
        `creator_type`,
        `creator_id`,
        `operator_role`,
        `operator_id`,
        `order_status`,
        `handle_result`,
        `level`,
        `comment`,
        `create_time`,
        `update_time`,
        `task_type`
    </sql>

    <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.model.admin.AdminWorkOrder">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="order_type" property="orderType" jdbcType="INTEGER"/>
        <result column="order_task" property="orderTask" jdbcType="INTEGER"/>
        <result column="creator_type" property="creatorType" jdbcType="INTEGER"/>
        <result column="creator_id" property="creatorId" jdbcType="INTEGER"/>
        <result column="operator_role" property="operatorRole" jdbcType="INTEGER"/>
        <result column="operator_id" property="operatorId" jdbcType="INTEGER"/>
        <result column="order_status" property="orderStatus" jdbcType="INTEGER"/>
        <result column="handle_result" property="handleResult" jdbcType="INTEGER"/>
        <result column="level" property="level" jdbcType="INTEGER"/>
        <result column="comment" property="comment" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="task_type" property="taskType" jdbcType="INTEGER"/>
    </resultMap>

    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.admin.AdminWorkOrder" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES
        (#{orderType}, #{orderTask},#{creatorType},#{creatorId},#{operatorId},
        #{orderStatus},#{handleResult},#{level},#{comment}, #{taskType})
    </insert>

    <insert id="insertList" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO <include refid="table_name"/>
          (<include refid="insert_fields"/>)
        VALUES
            <foreach collection="list" item="item" separator=",">
                (#{item.orderType}, #{item.orderTask},#{item.creatorType},#{item.creatorId},#{item.operatorId},
                #{item.orderStatus},#{item.handleResult},#{item.level},#{item.comment},#{item.taskType})
            </foreach>
    </insert>

    <select id="getUnHandleTask" resultType="com.shuidihuzhu.cf.model.admin.AdminWorkOrder">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `order_status`=0
        AND `is_delete`=0
        AND `task_type` = 0
        AND `order_type`=#{orderType}
        AND `order_task`=#{orderTask}
        AND `operator_id`=0
        LIMIT #{size}
    </select>

    <select id="getUnHandleTaskByTasks" resultType="com.shuidihuzhu.cf.model.admin.AdminWorkOrder">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        <where>
            `order_status`=0
            AND `is_delete`=0
            AND `task_type` = 0
            AND `order_type`=#{orderType}
            AND `order_task` in
            <foreach collection="tasks" open="(" separator="," close=")" item="task">
                #{task}
            </foreach>
            AND `operator_id`=0
        </where>
        LIMIT #{size}
    </select>

    <update id="recoverStatusAndOperator">
        UPDATE <include refid="table_name"/>
        SET order_status=#{orderStatus},operator_id=#{operatorId}
        WHERE order_type = #{orderType} and order_task = #{orderTask} and
        `id` IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="getUnHandleTaskByOperator" resultMap="BaseResultMap">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        <where>
            `is_delete`=0
            AND `order_status`=#{orderStatus}
            AND `order_type`=#{orderType}
            AND `order_task` in
            <foreach collection="taskTypes" open="(" separator="," close=")" item="taskType">
                #{taskType}
            </foreach>
            AND `operator_id`=#{operatorId}
        </where>
    </select>

    <update id="updateOperatorId">
        UPDATE <include refid="table_name"/>
        SET `operator_id`=#{operatorId}
        WHERE `id`=#{id}
    </update>

    <update id="updateStatus">
        UPDATE <include refid="table_name"/>
        SET `order_status`=#{orderStatus}
        WHERE `id`=#{id}
    </update>

    <update id="updateResult">
        UPDATE <include refid="table_name"/>
        SET `handle_result`=#{handleResult}
        WHERE `id`=#{id}
    </update>

    <update id="updateLevel">
        UPDATE <include refid="table_name"/>
        SET `level`=#{level}
        WHERE `id`=#{id}
    </update>


    <update id="updateWithOperatorIds">
        UPDATE <include refid="table_name"/>
        SET `operator_id`=#{operatorId},`order_status`=#{orderStatus}
        WHERE `id` IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateOperator">
        UPDATE <include refid="table_name"/>
        SET `operator_id`=#{targetUserId}
        WHERE `id` = #{workOrderId} and operator_id = #{orignalUserId}
    </update>

    <update id="resetWorkOrder">
        UPDATE <include refid="table_name"/>
        SET `operator_id`=#{operatorId},`order_status`=#{orderStatus},task_type=#{taskType}
        WHERE `id` IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>



    <select id="selectUgcByPageV2" resultType="com.shuidihuzhu.cf.model.admin.AdminWorkOrder">
        SELECT awo.`id`,awo.`order_type`,awo.`order_task`,awo.`creator_type`,awo.`creator_id`,
        awo.`operator_id`,awo.`order_status`,awo.`handle_result`,awo.`comment`
        FROM <include refid="table_name"/> awo
        LEFT JOIN `admin_task_ugc` atu ON awo.`id`=atu.`work_order_id`
        <where>
            awo.`is_delete`=0
            AND atu.`is_delete`=0
            <if test="right!=1">
                AND awo.`operator_id`=#{operatorId}
            </if>
            <if test="orderType!=null">
                AND awo.`order_type`=#{orderType}
            </if>
            <if test="orderTasks!=null">
                AND awo.`order_task` in
                <foreach collection="orderTasks" item="orderTask" open="(" separator="," close=")">
                    #{orderTask}
                </foreach>
            </if>
            <if test="caseId!=null">
                AND atu.`case_id`=#{caseId}
            </if>
            <if test="result!=null">
                AND atu.`result`=#{result}
            </if>
        </where>
        ORDER BY `id` DESC
    </select>


    <select id="listUgcBaseInfoByPage" resultType="com.shuidihuzhu.cf.model.admin.AdminWorkOrder">
        SELECT awo.`id`,awo.`order_type`,awo.`order_task`,awo.`creator_type`,awo.`creator_id`,
        awo.`operator_id`,awo.`order_status`,awo.`handle_result`,awo.`comment`,awo.`update_time`
        FROM <include refid="table_name"/> awo
        LEFT JOIN `admin_task_ugc` atu ON awo.`id`=atu.`work_order_id`
        <if test="riskLevel != null or action != null">
          LEFT JOIN `cf_case_raise_risk` ccrr ON ccrr.`case_id`=atu.`case_id`
        </if>
        <where>
            awo.`is_delete`=0
            AND atu.`is_delete`=0
            <if test="operatorId != 0">
                AND awo.`operator_id`=#{operatorId}
            </if>
            <if test="orderType!=null">
                AND awo.`order_type`=#{orderType}
            </if>
            <if test="orderTasks!=null">
                AND awo.`order_task` in
                <foreach collection="orderTasks" item="orderTask" open="(" separator="," close=")">
                    #{orderTask}
                </foreach>
            </if>
            <if test="caseId!=null">
                AND atu.`case_id`=#{caseId}
            </if>
            <if test="result!=null">
                AND atu.`result`=#{result}
            </if>
            <if test="action!=null">
                AND atu.`action`=#{action}
            </if>
            <if test="riskLevel != null">
                AND ccrr.`risk_level`=#{riskLevel}
            </if>
            <if test="startTime != null">
                AND atu.`update_time` <![CDATA[ >= ]]> #{startTime}
            </if>
            <if test="endTime != null">
                AND atu.`update_time` <![CDATA[ <= ]]> #{endTime}
            </if>
        </where>
        ORDER BY `id` DESC
    </select>

    <select id="selectById" resultType="com.shuidihuzhu.cf.model.admin.AdminWorkOrder">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `id`=#{id}
    </select>

    <select id="selectByIdList" resultType="com.shuidihuzhu.cf.model.admin.AdminWorkOrder">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `id` IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="updateChangeable">
        UPDATE <include refid="table_name"/>
        SET `order_status`=#{orderStatus},`handle_result`=#{handleResult},`comment`=#{comment}
        WHERE `id`=#{id}
    </update>


    <select id="selectUnHandleCount" resultType="java.lang.Integer">
        SELECT IFNULL(COUNT(*),0) FROM <include refid="table_name"/>
        WHERE `order_status`=0 AND `is_delete`=0 AND `operator_id`=0 AND `order_type`=#{orderType}
        <if test="orderTasks!=null">
            AND `order_task` in
            <foreach collection="orderTasks" item="orderTask" open="(" separator="," close=")">
                #{orderTask}
            </foreach>
        </if>
    </select>


    <update id="updateRoleOrOperatorId">
        UPDATE <include refid="table_name"/>
        SET
        <if test="operatorId!=null">
            `operator_id`=#{operatorId}
        </if>
        WHERE `id`=#{id}
    </update>

    <update id="updateRoleOrOperatorIdWithIdList">
        UPDATE <include refid="table_name"/>
        SET
        <if test="operatorId!=null">
            `operator_id`=#{operatorId}
        </if>
        WHERE `id` IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="handleFlow">
        UPDATE <include refid="table_name"/>
        SET `handleResult`=#{handle_result}
        WHERE `id`=#{id}
    </update>

    <select id="selectByIds" resultType="com.shuidihuzhu.cf.model.admin.AdminWorkOrder">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `id` IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>




    <select id="selectStatByGroupByOperatorId" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderCaseDo">
        SELECT `operator_id` AS operatorId, COUNT(*) AS counts
        FROM <include refid="table_name"/>
        WHERE `order_status` IN (1,3) AND `handle_result` IN (1,2,3,4) AND `is_delete`=0
        AND <![CDATA[ `create_time`>=#{beginTime} ]]> AND <![CDATA[ `create_time`<#{endTime} ]]>
        GROUP BY `operator_id`
        LIMIT #{start},#{size}
    </select>


    <select id="getWorkOrderByCaseId" parameterType="int" resultType="com.shuidihuzhu.cf.model.admin.AdminWorkOrder">
        SELECT workOrder.id
        FROM <include refid="table_name"/> workOrder
        JOIN admin_work_order_case workCase
        ON workOrder.id = workCase.work_order_id
        JOIN crowdfunding_info cr
        ON workCase.case_id = cr.id
        WHERE cr.id = #{id}
        and workCase.type = #{type}
        AND workOrder.`is_delete` = 0
        AND workCase.`is_delete` = 0
    </select>

    <update id="updateOrderStatus" parameterType="int">
        update <include refid="table_name"/>
        SET order_status = #{orderStatus},handle_result = #{handleResult}
        WHERE id = #{id}
    </update>

    <update id="update" >
        update <include refid="table_name"/>
        SET `order_status` = #{orderStatus},
            `handle_result` = #{handleResult},
            `order_type` = #{orderType},
            `order_task` = #{orderTask},
            `operator_id` = #{operatorId},
            `task_type` = #{taskType},
            `level` = #{level},
            `comment` = #{comment}
        WHERE id = #{id}
    </update>

    <update id="updateMsg">
        update
        <include refid="table_name"/>
        <set>
            <if test="orderStatus != null">
                order_status= #{orderStatus},
            </if>
            <if test="operatorId != null">
                operator_id= #{operatorId},
            </if>
            <if test="level != null">
                `level` = #{level},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="getFirstUGC" resultType="com.shuidihuzhu.cf.vo.crowdfunding.WorkOrderFirstApprove">

        select g.case_id caseId, c.channel channelStr, e.first_approve_status firstStatus,a.comment reason,g.result ugcStatus,
        a.operator_id operatorId, a.update_time operatorDate, c.target_amount targetAmount,a.id workOrderId
        from admin_work_order a
        left join admin_task_ugc g on a.id = g.work_order_id
        left join crowdfunding_info c on g.case_id = c.id
        left join cf_info_ext e on c.info_id = e.info_uuid
        <where>
            a.order_type=1  and a.order_task= 13  and a.is_delete=0
            <if test="right!=1">
                AND a.`operator_id`=#{operatorId}
            </if>
            <if test="channel==1">
                and  c.channel ='cf_volunteer'
            </if>
            <if test="caseId!=0">
                and g.case_id=#{caseId}
            </if>
            <if test="status!=-1">
                and g.result =#{status}
            </if>
            <if test="currOperatorId != 0">
                and a.operator_id = #{currOperatorId}
            </if>
            <if test="raiserId != 0">
                and c.user_id = #{raiserId}
            </if>
            <if test="operatorStartDate != null">
                and a.update_time  <![CDATA[ >= ]]> #{operatorStartDate}
            </if>
            <if test="operatorEndDate != null">
                and a.update_time  <![CDATA[ <= ]]> #{operatorEndDate}
            </if>
            ORDER BY a.id DESC
        </where>


    </select>


    <select id="selectUgcSensitiveByPage" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.AdminSensitiveVo">
      select c.id caseId,c.title caseTitle,s.user_id commentUserId,g.content_type contentType,g.result handleResult,
        g.update_time operatorTime,s.content content,s.sensitive_word sensitiveWords,a.id,c.info_id caseUUid,a.id
        workOrderId,a.operator_id operatorId,
        a.order_task taskType
        from admin_work_order a
        left join admin_task_ugc g on a.id = g.work_order_id
        left join crowdfunding_info c on g.case_id = c.id
        left join cf_sensitive_word_record s on g.word_id=s.id

        where a.order_type=#{orderType}
        and a.order_task in
        <foreach collection="orderTasks" item="task" separator="," open="(" close=")" >
          #{task}
        </foreach>
        and a.is_delete=0
        <if test="operatorId != 0">
            AND a.operator_id=#{operatorId}
        </if>

        <!--<if test="right==1 and operatorId != 0">-->
            <!--AND a.operator_id=#{operatorId}-->
        <!--</if>-->

        <if test="caseId != 0">
            AND g.case_id=#{caseId}
        </if>

        <if test="title != null and title !=''">
            AND c.title=#{title}
        </if>

        <if test="commentUserId != 0">
            AND s.user_id=#{commentUserId}
        </if>

        <if test="contentTypes.size() > 0">
            AND g.content_type in
            <foreach collection="contentTypes" item="contentType" separator="," open="(" close=")" >
                #{contentType}
            </foreach>
        </if>

        <if test="result != null">
            AND g.result=#{result}
        </if>

        <if test="startTime != null and startTime != '' and endTime !=null and endTime !=''">
            AND g.update_time between #{startTime} and #{endTime}
        </if>

    </select>


    <select id="selectUgcSensitiveByAnchor" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.AdminSensitiveVo">
        select c.id caseId,c.title caseTitle,s.user_id commentUserId,g.content_type contentType,g.result handleResult,
        g.update_time operatorTime,s.content content,s.sensitive_word sensitiveWords,a.id,c.info_id caseUUid,a.id
        workOrderId,a.operator_id operatorId,
        a.order_task taskType
        from admin_work_order a
        left join admin_task_ugc g on a.id = g.work_order_id
        left join crowdfunding_info c on g.case_id = c.id
        left join cf_sensitive_word_record s on g.word_id=s.id

        <where>

            <if test="anchor != 0">
                <if test="isPre == true">
                    AND a.id  <![CDATA[ < ]]> #{anchor}
                </if>
                <if test="isPre == false">
                    AND a.id  <![CDATA[ > ]]> #{anchor}
                </if>
            </if>
             AND a.order_type=#{orderType}
            and a.order_task in
            <foreach collection="orderTasks" item="task" separator="," open="(" close=")" >
                #{task}
            </foreach>
            and a.is_delete=0
            <if test="operatorId != 0">
                AND a.operator_id=#{operatorId}
            </if>

            <!--<if test="right==1 and operatorId != 0">-->
            <!--AND a.operator_id=#{operatorId}-->
            <!--</if>-->

            <if test="caseId != 0">
                AND g.case_id=#{caseId}
            </if>

            <if test="title != null and title !=''">
                AND c.title=#{title}
            </if>

            <if test="commentUserId != 0">
                AND s.user_id=#{commentUserId}
            </if>

            <if test="contentTypes.size() > 0">
                AND g.content_type in
                <foreach collection="contentTypes" item="contentType" separator="," open="(" close=")" >
                    #{contentType}
                </foreach>
            </if>

            <if test="result != null">
                AND g.result=#{result}
            </if>

            <if test="startTime != null and startTime != '' and endTime !=null and endTime !=''">
                AND g.update_time between #{startTime} and #{endTime}
            </if>

            <if test="hitWords != null and hitWords != ''">
                AND g.hit_words like CONCAT('%', #{hitWords}, '%')
            </if>
        </where>
        <if test="isPre == true">
            order by a.id desc
        </if>
        limit #{size}
    </select>


    <select id="getFirstApproveCountByStatusAndTime" resultType="java.lang.Integer">
        select count(id)
        from <include refid="table_name"/>
        where order_type=1  and order_task= 13  and is_delete=0
        <if test="firstApproveStatus!=null">
            AND `order_status`=#{firstApproveStatus}
        </if>
        <if test="startTime != null">
            AND <![CDATA[   `update_time`>=#{startTime} ]]>
        </if>
        <if test="endTime != null">
            AND <![CDATA[   `update_time`<#{endTime} ]]>
        </if>
    </select>

    <select id="getFirstApproveAccounts" resultType="com.shuidihuzhu.cf.vo.crowdfunding.firstapprove.FirstApproveAccountMap">
        select a.operator_id, e.first_approve_status, count(a.id) as count, a.update_time
        from admin_work_order a
        left join admin_task_ugc g on a.id = g.work_order_id
        left join crowdfunding_info c on g.case_id = c.id
        left join cf_info_ext e on c.info_id = e.info_uuid
        where a.order_type=1  and a.order_task= 13  and a.is_delete=0 and a.`operator_id`!=0
        <if test="startTime != null">
            AND <![CDATA[   a.`update_time`>=#{startTime} ]]>
        </if>
        <if test="endTime != null">
            AND <![CDATA[   a.`update_time`<#{endTime} ]]>
        </if>
        <if test="operatorId > 0">
            AND a.operator_id = #{operatorId}
        </if>
        group by a.`operator_id`, e.first_approve_status
    </select>

    <select id="selectApplyingFirstApprovesByOperator" resultType="com.shuidihuzhu.cf.model.admin.AdminWorkOrder">
        select a.*
        from admin_work_order a
        left join admin_task_ugc g on a.id = g.work_order_id
        left join crowdfunding_info c on g.case_id = c.id
        left join cf_info_ext e on c.info_id = e.info_uuid
        where a.order_type=1  and a.order_task= 13  and a.is_delete=0 and e.first_approve_status=10
        and a.`operator_id`=#{operatorId}
        <if test="startTime != null">
            AND <![CDATA[   a.`update_time`>=#{startTime} ]]>
        </if>
        <if test="endTime != null">
            AND <![CDATA[   a.`update_time`<#{endTime} ]]>
        </if>
        limit 0, #{count}
    </select>

<!--    <select id="listOperatorByOrderType" resultType="int">-->
<!--        select `operator_id`-->
<!--        from admin_work_order-->
<!--        where order_type=#{orderType}-->
<!--        and is_delete=0-->
<!--        group by operator_id-->
<!--    </select>-->


    <select id="getSensitiveVOByWorkOrderId" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.AdminSensitiveVo">
        select c.id caseId,c.title caseTitle,s.user_id commentUserId,g.content_type contentType,g.result handleResult,
        g.update_time operatorTime,s.content content,s.sensitive_word sensitiveWords,a.id,c.info_id caseUUid,a.id
        workOrderId,a.operator_id operatorId,
        a.order_task taskType
        from admin_work_order a
        left join admin_task_ugc g on a.id = g.work_order_id
        left join crowdfunding_info c on g.case_id = c.id
        left join cf_sensitive_word_record s on g.word_id=s.id
        where a.`id` = #{id}
    </select>



    <select id="selectUnHandleOrders" resultType="com.shuidihuzhu.cf.model.admin.AdminWorkOrder">
        SELECT <include refid="select_fields"/> FROM <include refid="table_name"/>
        <where>
            <if test="id != null">
                id > #{id}
            </if>
        AND `operator_id`=0 AND `order_status`=0 AND `is_delete`=0   AND `order_type`=#{orderType}
        AND `order_task` = #{orderTask}
        ORDER BY id
        LIMIT #{size}
        </where>
    </select>


    <!--    ========================================================================================================================-->

    <select id="getByOperatorIdAndStatus" resultType="com.shuidihuzhu.cf.model.admin.AdminWorkOrder">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        where
        create_time >= #{createTime}
        and operator_id = #{operatorId}
        and order_status = #{orderStatus}
        limit 1
    </select>

    <select id="selectUnHandleTask" resultType="com.shuidihuzhu.cf.model.admin.AdminWorkOrder">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE create_time >= #{createTime}
        AND operator_id = 0
        ORDER BY `level` DESC, create_time
    </select>

</mapper>