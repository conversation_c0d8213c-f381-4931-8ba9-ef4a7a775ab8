<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.admin.UserCommentDao">

    <sql id="TableName">
        user_comment
    </sql>

    <sql id="BaseFiled">
        case_id,work_order_id,comment_source,comment_type,operator_id,comment,operate_mode,operate_desc
    </sql>

    <insert id="add" parameterType="com.shuidihuzhu.cf.model.admin.UserComment">
        INSERT INTO user_comment
        (case_id,comment_source,comment_type,operator_id,comment,operate_mode,operate_desc)
        VALUES (#{caseId},#{commentSource},#{commentType},#{operatorId},#{comment},#{operateMode},#{operateDesc})
    </insert>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.admin.UserComment">
        INSERT INTO <include refid="TableName"/>
        (case_id,work_order_id,comment_source,comment_type,operator_id,comment,operate_mode,operate_desc)
        VALUES (#{caseId},#{workOrderId},#{commentSource},#{commentType},#{operatorId},#{comment},#{operateMode},#{operateDesc})
    </insert>

    <insert id="addList" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO <include refid="TableName"/>
        (<include refid="BaseFiled"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.caseId}, #{item.workOrderId},#{item.commentSource},#{item.commentType},#{item.operatorId},
            #{item.comment},#{item.operateMode},#{item.operateDesc})
        </foreach>
    </insert>

    <select id="getUserComment" resultType="com.shuidihuzhu.cf.model.admin.UserComment">
        SELECT
        id,
        case_id caseId,
        comment_source commentSource,
        comment_type commentType,
        operator_id operatorId,
        comment,
        create_time createTime,
        operate_mode operateMode,
        operate_desc operateDesc,
        work_order_id workOrderId
        FROM
        user_comment
        WHERE
        `case_id`=#{caseId} AND `comment_source`=#{commentSource} and is_delete=0
        <if test="commentType !=0">
            and comment_type=#{commentType}
        </if>
    </select>


    <select id="countByCommentSoure" resultType="java.lang.Integer">
        SELECT
        COUNT(*)
        FROM
        user_comment
        WHERE
        `case_id`=#{caseId} AND `comment_source`=#{commentSource}
    </select>


    <select id="getUserCommentDescByCommentSource" resultType="com.shuidihuzhu.cf.model.admin.UserComment">
        SELECT
        id,
        case_id caseId,
        comment_source commentSource,
        comment_type commentType,
        operator_id operatorId,
        comment,
        create_time createTime,
        operate_mode operateMode,
        operate_desc operateDesc,
        work_order_id workOrderId
        FROM
        user_comment
        WHERE
        `case_id` = #{caseId} AND `comment_source` = #{commentSource}
        ORDER BY id desc limit #{start}, #{size}
    </select>

</mapper>