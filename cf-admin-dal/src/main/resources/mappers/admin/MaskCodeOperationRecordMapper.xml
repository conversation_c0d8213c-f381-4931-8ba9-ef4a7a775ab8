<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.admin.MaskCodeOperationRecordDao">

    <sql id="TableName">
        mask_code_operation_record
    </sql>

    <insert id="addMaskOperationRecord" parameterType="com.shuidihuzhu.cf.model.common.MaskCodeOperationRecord">
        INSERT INTO <include refid="TableName"/>
        (operator_id, case_id, clew_id, wechat_id, operator_mis, operation_page, operation_type, query_content, operation_time)
        VALUES (#{operatorId}, #{caseId}, #{clewId}, #{wechatId}, #{operatorMis}, #{operationPage}, #{operationType}, #{queryContent}, #{operationTime})
    </insert>
</mapper>