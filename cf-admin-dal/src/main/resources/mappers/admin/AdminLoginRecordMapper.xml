<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.admin.AdminLoginRecordDAO">

    <sql id="TABLE_NAME">
		admin_login_record
	</sql>

    <select id = "selectByLoginAndOrgIdAndUserId" resultType="com.shuidihuzhu.cf.model.admin.AdminLoginRecord">
        SELECT *
        FROM <include refid="TABLE_NAME"/>
        WHERE
        `format_date` = #{formatDate}
        <if test="orgId != 0">
            AND org_id = #{orgId}
        </if>
        <if test="userId != 0">
            AND user_id = #{userId}
        </if>
    </select>

</mapper>