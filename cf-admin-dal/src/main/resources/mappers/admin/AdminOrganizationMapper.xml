<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.admin.AdminOrganizationDao">
    <sql id="TABLE_NAME">
		admin_organization
	</sql>

    <sql id="INSERT_FIELDS">
		(`name`, `parent_org_id`)
	</sql>

    <sql id="SELECT_FIELDS">
		`id`, `name`, `parent_org_id`, `is_delete`, `create_time`, `update_time`
	</sql>

    <insert id="addOrganizationNode" parameterType="com.shuidihuzhu.cf.model.admin.AdminOrganization" keyProperty="id" useGeneratedKeys="true">
        insert into <include refid="TABLE_NAME"/>
        <include refid="INSERT_FIELDS"/>
        values(#{name}, #{parentOrgId})
    </insert>

    <update id="editOrganizationNode" parameterType="com.shuidihuzhu.cf.model.admin.AdminOrganization">
        update <include refid="TABLE_NAME"/>
        set name = #{name}
        where id = #{id}
    </update>

    <update id="deleteOrganizationNode">
      update <include refid="TABLE_NAME"/>
      set is_delete = 1
      where id = #{organizationId}
    </update>

    <select id="getAdminOrganization" resultType="com.shuidihuzhu.cf.model.admin.AdminOrganization">
        select <include refid="SELECT_FIELDS"/>
        from <include refid="TABLE_NAME"/>
        where id = #{organizationId}
    </select>

    <select id="getAdminOrganizationByParentOrgId" resultType="com.shuidihuzhu.cf.model.admin.AdminOrganization">
        select <include refid="SELECT_FIELDS"/>
        from <include refid="TABLE_NAME"/>
        where is_delete = 0 and parent_org_id = #{parentOrgId}
    </select>

    <select id="hasSameNameAndParentOrg" resultType="java.lang.Boolean">
        select count(1)
        from <include refid="TABLE_NAME"/>
        where name = #{name} and is_delete = 0
        <if test="orgIds != null">
          and `id` IN <foreach collection="orgIds" item="orgId" open="(" separator="," close=")" >#{orgId}</foreach>
        </if>
    </select>

    <select id="getAllAdminOrganization" resultType="com.shuidihuzhu.cf.model.admin.AdminOrganization">
        select <include refid="SELECT_FIELDS"/>
        from <include refid="TABLE_NAME"/>
    </select>

    <select id="getAdminOrganizationByName" resultType="com.shuidihuzhu.cf.model.admin.AdminOrganization">
        select <include refid="SELECT_FIELDS"/>
        from <include refid="TABLE_NAME"/>
        where is_delete = 0 and name = #{name}
    </select>

</mapper>