<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.admin.CfMaterialsPicDao">

	<insert id="insert" parameterType="com.shuidihuzhu.cf.model.admin.CfMaterialsPic" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO cf_materials_pic (`case_id`,`pic_url`,`pic_source`,`pic_type`,`user_name`,`repeat_pic`)
		VALUES
		<foreach collection="list" item="item" separator=",">
			(#{item.caseId},#{item.picUrl},#{item.picSource},#{item.picType},#{item.userName},#{item.repeatPic})
		</foreach>
	</insert>


	<select id="getByCaseId" resultType="com.shuidihuzhu.cf.model.admin.CfMaterialsPic">

		select * from cf_materials_pic
		where case_id = #{caseId} and is_delete=0

	</select>


	<update id="delete">
		update cf_materials_pic set is_delete=1 where case_id=#{caseId};
	</update>

</mapper>
