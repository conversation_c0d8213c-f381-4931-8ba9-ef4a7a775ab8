<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.admin.AdminExtDao">
    <select id="selectByInfoNotExistInExtTable" resultType="java.lang.String">
        SELECT
            `info_id`
        FROM
            crowdfunding_info ci
            LEFT JOIN
            cf_info_ext cfe ON ci.info_id = cfe.info_uuid
        WHERE
            cfe.id IS NULL AND type != 1;
    </select>

    <select id="selectInfoUuidsInOperationRecord" resultType="java.lang.String">
        SELECT
            `info_id`
        FROM
            cf_admin_operation_record
        WHERE
            created_time > '2017-06-21 00:00:00'
            AND info_id IN (SELECT
                                info_id
                            FROM
                                crowdfunding_info ci
                                LEFT JOIN
                                cf_info_ext cfe ON ci.info_id = cfe.info_uuid
                            WHERE
                                cfe.id IS NULL AND type IN (0 , 99))
            AND operation IN (18 , 19)
        GROUP BY info_id
        ORDER BY created_time DESC;
    </select>

</mapper>