<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.admin.CfScheduleAlarmDao">

    <insert id="insert" parameterType="com.shuidihuzhu.cf.domain.cf.CfScheduleAlarm" useGeneratedKeys="true" keyProperty="id">
        insert into cf_schedule_alarm (title, `description`, crontab, `execute_sql`, robot_key, content_type, operator_id, data_source, create_time, feishurobot_key, sign)
        values (#{title}, #{description}, #{crontab}, #{executeSql}, #{robotKey}, #{contentType}, #{operatorId}, #{dataSource}, now(), #{feishuRobotKey}, #{sign})
    </insert>


    <select id="getAll" resultType="com.shuidihuzhu.cf.domain.cf.CfScheduleAlarm">
        SELECT *
        FROM cf_schedule_alarm
        WHERE is_delete = 0
    </select>

    <select id="getAllByAnchor" resultType="com.shuidihuzhu.cf.domain.cf.CfScheduleAlarm">
        SELECT *
        FROM cf_schedule_alarm
        <where>
            <if test="anchor != 0">
                <if test="isPre == true">
                    AND id  <![CDATA[ < ]]> #{anchor}
                </if>
                <if test="isPre == false">
                    AND id  <![CDATA[ > ]]> #{anchor}
                </if>
            </if>
            and is_delete = 0
        </where>
        <if test="isPre == true">
            order by id desc
        </if>
        limit #{pageSize}
    </select>

    <select id="getListByAnchor" resultType="com.shuidihuzhu.cf.domain.cf.CfScheduleAlarm">
        SELECT *
        FROM cf_schedule_alarm
        <where>
            <if test="anchor != 0">
                <if test="isPre == true">
                    AND id  <![CDATA[ < ]]> #{anchor}
                </if>
                <if test="isPre == false">
                    AND id  <![CDATA[ > ]]> #{anchor}
                </if>
            </if>
            <if test="title != null and title != ''">
                and title like CONCAT('%',#{title},'%')
            </if>
            <if test="description != null and description != ''">
                and `description` like CONCAT('%',#{description},'%')
            </if>
            <if test="crontab != null and crontab != ''">
                and `crontab` = #{crontab}
            </if>
            <if test="robotKey != null and robotKey != ''">
                and `robot_key` = #{robotKey}
            </if>
            <if test="contentType != null and contentType >= 0 ">
                and `content_type` = #{contentType}
            </if>
            <if test="dataSource != null and dataSource != ''">
                and `data_source` = #{dataSource}
            </if>
            <if test="feishuRobotKey != null and feishuRobotKey != ''">
                and `feishurobot_key` = #{feishuRobotKey}
            </if>
            and sign in <foreach collection="signSet" item="sign" open="(" separator="," close=")">
                    #{sign}
                </foreach>
            and is_delete = 0
        </where>
        <if test="isPre == true">
            order by id desc
        </if>
        limit #{pageSize}
    </select>


    <update id="delete">
        update cf_schedule_alarm set is_delete = 1, operator_id = #{operatorId} where id = #{id}
    </update>

    <update id="update" parameterType="com.shuidihuzhu.cf.domain.cf.CfScheduleAlarm">
        update cf_schedule_alarm
        set title = #{title},
            `description` = #{description} ,
            crontab = #{crontab},
            `execute_sql` = #{executeSql},
            robot_key=#{robotKey},
            content_type = #{contentType},
            operator_id = #{operatorId},
            data_source = #{dataSource},
            feishurobot_key = #{feishuRobotKey}
        where id = #{id}
    </update>

    <select id="getById" resultType="com.shuidihuzhu.cf.domain.cf.CfScheduleAlarm">
        SELECT *
        FROM cf_schedule_alarm
        where id = #{id} and is_delete= 0
    </select>

    <select id="getByTitle" resultType="com.shuidihuzhu.cf.domain.cf.CfScheduleAlarm">
        select *
        from cf_schedule_alarm
        where title = #{title} and is_delete = 0
    </select>

</mapper>
