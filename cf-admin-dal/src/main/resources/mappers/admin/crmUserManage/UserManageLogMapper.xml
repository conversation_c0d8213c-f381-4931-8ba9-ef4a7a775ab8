<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crmUserManage.UserManageLogDao">

    <sql id="table_name">
        cf_crm_user_manage_log
    </sql>

    <insert id = "addUserManageLogs" parameterType="com.shuidihuzhu.cf.model.CrmUserManage.UserManage$UserManageLog">
        INSERT INTO
        <include refid="table_name"/>
        (`crypto_mobile`, `person_id`, `uuid`, `crypto_id_card`, `user_name`, `version`,
        `operate_source`, `operate_id`, `operate_comment`, `ext_comment`)
        VALUES
        <foreach collection="recordList" item="item" separator=",">
            (#{item.cryptoMobile}, #{item.personId},
              #{item.uuid},
              #{item.cryptoIdCard},
              #{item.userName},
              #{item.version},
              #{item.operateSource},
              #{item.operateId},
              #{item.operateComment},
              #{item.extComment}
              )
        </foreach>
    </insert>



</mapper>