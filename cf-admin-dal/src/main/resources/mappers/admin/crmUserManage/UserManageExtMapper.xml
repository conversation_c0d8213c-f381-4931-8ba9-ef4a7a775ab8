<?xml version="1.0" encoding="UTF-8" ?>
        <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
                "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crmUserManage.UserManageExtDao">


    <sql id="table_name">
        cf_crm_user_manage_ext
    </sql>

    <insert id = "addExtList">
        INSERT INTO
        <include refid="table_name"/>
        (`mark`, `mark_value`, `ext_key`, `ext_value`)
        VALUES
        <foreach collection="extList" item="item" separator=",">
        (#{item.mark}, #{item.markValue},
        #{item.extKey},
        #{item.extValue}
        )
        </foreach>
    </insert>

    <select id="selectExtValues" resultType="com.shuidihuzhu.cf.model.CrmUserManage.UserManage$UserManageExt">
        SELECT *
        FROM <include refid="table_name"/>
        WHERE mark = #{mark}
        AND
        `mark_value` = #{markValue}
        <if test="extKeyList != null and extKeyList.size() > 1">
          AND ext_key IN
          <foreach collection="extKeyList" item = "item" open="(" close=")" separator=",">
              #{item}
          </foreach>
        </if>
    </select>


    <update id="deleteUserExtByKeys">
        UPDATE <include refid="table_name"/>
        SET is_delete = 1
        WHERE mark = #{mark}
        AND
        `mark_value` = #{markValue}
        <if test="extKeyList != null and extKeyList.size() > 0">
            AND ext_key IN
            <foreach collection="extKeyList" item = "item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        AND is_delete = 0
    </update>
</mapper>