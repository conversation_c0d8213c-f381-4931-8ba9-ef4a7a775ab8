<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crmUserManage.UserManageDao">

    <sql id="table_name">
        cf_crm_user_manage
    </sql>

    <insert id = "addUsers" parameterType="com.shuidihuzhu.cf.model.CrmUserManage.UserManage$UserAccount">
        INSERT IGNORE INTO
        <include refid="table_name"/>
        (`crypto_mobile`, `person_id`)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.cryptoMobile}, #{item.personId})
        </foreach>
    </insert>

    <update id = "updateIdCardByMobiles" parameterType="com.shuidihuzhu.cf.model.CrmUserManage.UserManage$UserAccount">
      UPDATE
      <include refid="table_name"/>
      SET
      crypto_id_card = #{cryptoIdCard},
      user_name = #{userName}
      WHERE crypto_mobile IN
        <foreach collection="cryptoMobiles" item="mobile" open="(" close=")" separator=",">
            #{mobile}
        </foreach>
    </update>

    <update id = "updateUuidByIdCards">
        UPDATE
        <include refid="table_name"/>
        SET
        uuid = #{uuid}
        WHERE crypto_mobile
        IN
        <foreach collection="cryptoMobiles" item="mobile" open="(" close=")" separator=",">
            #{mobile}
        </foreach>
    </update>

    <select id = "selectByIdCards" resultType="com.shuidihuzhu.cf.model.CrmUserManage.UserManage$UserAccount">
        SELECT *
        FROM
        <include refid="table_name"/>
        WHERE crypto_id_card
        IN
        <foreach collection="cryptoIdCards" item="idCard" open="(" close=")" separator=",">
            #{idCard}
        </foreach>
        AND is_delete = 0
    </select>



    <select id = "selectByMobiles" resultType="com.shuidihuzhu.cf.model.CrmUserManage.UserManage$UserAccount">
        SELECT *
        FROM
        <include refid="table_name"/>
        WHERE crypto_mobile
        IN
        <foreach collection="cryptoMobiles" item="mobile" open="(" close=")" separator=",">
            #{mobile}
        </foreach>
        AND is_delete = 0
    </select>


    <select id = "selectByUuids" resultType="com.shuidihuzhu.cf.model.CrmUserManage.UserManage$UserAccount">
        SELECT *
        FROM
        <include refid="table_name"/>
        WHERE uuid IN
        <foreach collection="uuidList" item="uuid" open="(" close=")" separator=",">
            #{uuid}
        </foreach>
        AND is_delete = 0
    </select>








</mapper>