<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crmUserManage.UserRoleDao">

    <sql id="table_name">
        cf_crm_user_manage_user_role
    </sql>


    <insert id = "addUserRoles" parameterType="com.shuidihuzhu.cf.model.CrmUserManage.UserManage$UserRoleModel">
        INSERT INTO
        <include refid="table_name"/>
        (`crypto_mobile`, `case_id`, `user_role`)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.cryptoMobile}, #{item.caseId}, #{item.userRole})
        </foreach>
    </insert>

    <update id = "deleteUserRolesByIds">
        UPDATE
        <include refid="table_name"/>
        SET is_delete = 1
        WHERE
        id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND is_delete = 0
    </update>

    <select id="selectByMobiles" resultType="com.shuidihuzhu.cf.model.CrmUserManage.UserManage$UserRoleModel">
        SELECT *
        FROM
        <include refid="table_name"/>
        WHERE
        crypto_mobile IN
        <foreach collection="cryptoMobiles" item="mobile" open="(" close=")" separator=",">
            #{mobile}
        </foreach>
      AND is_delete = 0
    </select>

    <update id = "deleteUserRolesByMobiles">
        UPDATE
        <include refid="table_name"/>
        SET is_delete = 1
        WHERE
        crypto_mobile IN
        <foreach collection="cryptoMobiles" item="mobile" open="(" separator="," close=")">
            #{mobile}
        </foreach>
        AND is_delete = 0
    </update>

    <update id = "deleteRoleByCaseIdAndRole">
        UPDATE
        <include refid="table_name"/>
        SET is_delete = 1
        WHERE
        case_id = #{caseId}
        AND
        user_role = #{userRole}
        AND
        is_delete = 0
        LIMIT 100
    </update>

    <select id="selectByMobileAndUserRole" resultType="com.shuidihuzhu.cf.model.CrmUserManage.UserManage$UserRoleModel">
        SELECT *
        FROM
        <include refid="table_name"/>
        WHERE
        crypto_mobile = #{cryptoMobile}
        AND
        user_role = #{userRole}
        AND
        is_delete = 1
    </select>

    <select id="selectByCaseIdAndUserRole" resultType="com.shuidihuzhu.cf.model.CrmUserManage.UserManage$UserRoleModel">
        SELECT *
        FROM
        <include refid="table_name"/>
        WHERE
        case_id = #{caseId}
        AND
        user_role IN
        <foreach collection="userRoles" item="userRole" open="(" separator="," close=")">
            #{userRole}
        </foreach>
        AND
        is_delete = 0
    </select>


</mapper>

