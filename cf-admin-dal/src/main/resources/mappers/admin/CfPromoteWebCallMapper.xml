<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.admin.IPromoteWebCallDao">
    <sql id="TABLE">
        promote_web_call_info
    </sql>
    <sql id="BASE_FIELDS">
        `case_id`,
        `work_id`,
        `user_id`,
        `mobile`
    </sql>
    <sql id="FIELDS">
        `case_id`,
        `work_id`,
        `user_id`,
        `mobile`,
        `through_flag`,
        `start_time`,
        `end_time`,
		`through_length`,
        `publish_time`,
        `publish_num`
    </sql>

    <select id="selectPublishNumByCaseId" resultType="integer">
        select `publish_num` from <include refid="TABLE"/>
        where `case_id` = #{caseId} and `first_call` = 1
        order by id desc
        limit 1
    </select>

    <update id="updateByCaseId">
        update <include refid="TABLE"/>
        set `publish_time` = #{publishTime},
            `publish_num` = #{publishNum}
        where `case_id` = #{caseId} and `first_call` = 1
        order by id desc
        limit 1
    </update>

    <update id="updatePublishNumByCaseId">
        update <include refid="TABLE"/>
        set `publish_num` = #{publishNum}
        where `case_id` = #{caseId} and `first_call` = 1
        order by id desc
        limit 1
    </update>

</mapper>