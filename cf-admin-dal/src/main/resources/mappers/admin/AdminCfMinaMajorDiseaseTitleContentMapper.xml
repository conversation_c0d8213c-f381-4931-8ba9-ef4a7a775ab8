<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.admin.AdminCfMinaMajorDiseaseTitleContentDao">
    <sql id="table_name">
        `cf_mina_major_disease_title_content`
    </sql>

    <sql id="insert_fields">
        `menu_id`,
        `title`,
        `title_order`,
        `content`
    </sql>

    <sql id="select_fields">
        `id`,
        `menu_id`,
        `title`,
        `title_order`,
        `content`
    </sql>

    <update id="updateContent">
        UPDATE <include refid="table_name"/>
        SET `content`=#{content}
        WHERE `id`=#{id}
    </update>

    <select id="selectWithLimit" resultType="com.shuidihuzhu.cf.model.mina.CfMinaMajorDiseaseTitleContent">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        <bind name="contentPattern" value="'%' + content + '%'"/>
        WHERE `content` LIKE #{contentPattern}
        LIMIT #{start},#{limit}
    </select>

    <update id="changeContent">
        UPDATE <include refid="table_name"/>
        SET `content`=#{content}
        WHERE `id`=#{id}
    </update>

    <select id="selectById" resultType="com.shuidihuzhu.cf.model.mina.CfMinaMajorDiseaseTitleContent">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `id`=#{id}
    </select>

    <select id="selectByTitle" resultType="com.shuidihuzhu.cf.model.mina.CfMinaMajorDiseaseTitleContent">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `title`=#{title}
    </select>

    <update id="changeTitle">
        UPDATE <include refid="table_name"/>
        SET `title`=#{title}
        WHERE `id`=#{id}
    </update>
</mapper>