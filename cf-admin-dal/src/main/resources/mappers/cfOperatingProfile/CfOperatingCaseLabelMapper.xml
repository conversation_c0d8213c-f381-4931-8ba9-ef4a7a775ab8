<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.cfOperatingProfile.CfOperatingCaseLabelDao">

    <sql id="tableName">
        cf_operating_case_label
    </sql>

    <insert id = "addCaseLabels">
        INSERT INTO
        <include refid="tableName"/>
        (`case_id`, `case_label_id`, `operate_id`)
        values
        <foreach collection="list" index="index" item="item" separator=",">
            (#{item.caseId}, #{item.caseLabelId}, #{item.operateId})
        </foreach>

    </insert>

    <select id = "selectOperatingCaseLabels" resultType="com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingCaseLabel">
        SELECT *
        FROM
        <include refid="tableName"/>
        WHERE case_id = #{caseId}
        AND is_delete = 0
    </select>

    <update id = "deleteCaseLabelByIds">
        UPDATE
        <include refid="tableName"/>
        SET is_delete = 1
        WHERE id IN
        <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
          #{id}
        </foreach>
    </update>

</mapper>