<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.cfOperatingProfile.CfOperatingProfileSettingsExtDao">

    <sql id="tableName">
        cf_operating_profile_settings_ext
    </sql>

    <insert id="insertExtList" parameterType="com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileSettings$ProfileSettingsExt">
        INSERT INTO
        <include refid="tableName"/>
        (profile_id, ext_name, ext_value, ext_info)
        VALUES
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.profileId}, #{item.extName}, #{item.extValue}, #{item.extInfo})
        </foreach>
    </insert>

    <select id = "selectByProfileIdsAndNames" resultType="com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileSettings$ProfileSettingsExt">
        SELECT *
        FROM
        <include refid="tableName"/>
        WHERE
        profile_id IN

        <foreach collection="ids" item = "id" open="(" separator="," close=")" >
            #{id}
        </foreach>
        <if test = "names != null and names.size > 0">
            AND ext_name IN
            <foreach collection="names" item = "name" open="(" separator="," close=")" >
                #{name}
            </foreach>
        </if>
        AND is_delete = 0
    </select>


    <update id = "deleteExtByProfileIdsAndNames">
        UPDATE
        <include refid="tableName"/>
        SET is_delete = 1
        WHERE
        profile_id IN
        <foreach collection="ids" item = "id" open="(" separator="," close=")" >
            #{id}
        </foreach>
        <if test = "names != null and names.size > 0">
            AND ext_name IN
            <foreach collection="names" item = "name" open="(" separator="," close=")" >
                #{name}
            </foreach>
        </if>
        AND is_delete = 0
    </update>


    <select id = "selectByNameAndValues" resultType="com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileSettings$ProfileSettingsExt">
        SELECT *
        FROM
        <include refid="tableName"/>
        WHERE
        ext_name = #{name}
        AND ext_value IN
        <foreach collection="values" item = "value" open="(" separator="," close=")" >
            #{value}
        </foreach>
        AND is_delete = 0
    </select>

</mapper>