<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.cfOperatingProfile.CfOperatingProfileLogDao">

    <sql id="tableName">
        cf_operating_profile_log
    </sql>

    <insert id="insertOperateRecord">
        INSERT INTO
        <include refid="tableName"/>
        (`business_id`,  `operate_id`, `operate_name`, `operate_type`, `operate_comment`, `operate_log_type`)
        values
         <foreach collection="list" index="index" item="item" separator=",">
             (#{item.businessId}, #{item.operateId}, #{item.operateName}, #{item.operateType},
             #{item.operateComment}, #{item.operateLogType})
         </foreach>

    </insert>

    <select id = "selectLogByLogType" resultType="com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileLog">
        SELECT * FROM
        <include refid="tableName"/>
        WHERE
        business_id = #{businessId}
        ORDER BY create_time
    </select>






</mapper>