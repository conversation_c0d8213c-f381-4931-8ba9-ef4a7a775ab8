<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.cfOperatingProfile.CfCaseOperateStatusDao">

    <sql id="tableName">
        cf_case_operate_status
    </sql>

    <select id="selectByIdAndOperateType" resultType="com.shuidihuzhu.cf.model.cfOperatingProfile.CfCaseOperateStatus">
        SELECT *
        FROM
        <include refid="tableName"/>
        WHERE case_id = #{caseId}
        AND operate_type IN
         <foreach collection="statusList" item="item" open="(" separator="," close=")">
             #{item}
         </foreach>
        AND is_delete = 0
    </select>

    <insert id = "insertOne" parameterType="com.shuidihuzhu.cf.model.cfOperatingProfile.CfCaseOperateStatus">
        INSERT INTO
        <include refid="tableName"/>
        (`case_id`, `operate_id`, `operate_type`, `operate_comment`, `organization`)
        values (#{caseId}, #{operateId}, #{operateType},  #{operateComment}, #{organization})
    </insert>


    <update id="updateById" parameterType="com.shuidihuzhu.cf.model.cfOperatingProfile.CfCaseOperateStatus">
        UPDATE
        <include refid="tableName"/>
        SET
        `operate_id` = #{operateId},
        `operate_type` = #{operateType},
        `operate_comment` = #{operateComment},
        `organization` = #{organization}
        WHERE id = #{id}

    </update>





</mapper>
