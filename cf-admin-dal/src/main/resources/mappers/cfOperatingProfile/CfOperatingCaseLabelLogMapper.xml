<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.cfOperatingProfile.CfOperatingCaseLabelLogDao">


    <sql id="tableName">
        cf_operating_case_label_log
    </sql>


    <insert id = "addCaseLabelLog">
        INSERT INTO
        <include refid="tableName"/>
        (`case_id`, `case_label_id`, `operate_id`, `organization`, `operate_type`, `operate_source`)
        values
        <foreach collection="list" index="index" item="item" separator=",">
            (#{item.caseId}, #{item.caseLabelId}, #{item.operateId}, #{item.organization},
            #{item.operateType}, #{item.operateSource})
        </foreach>

    </insert>



</mapper>