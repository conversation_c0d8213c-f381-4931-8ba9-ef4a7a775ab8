<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.cfOperatingProfile.CfOperatingProfileSettingsDao">


    <sql id="tableName">
        cf_operating_profile_settings
    </sql>

    <insert id = "addProfile" keyProperty="id" useGeneratedKeys="true">
      INSERT INTO
      <include refid="tableName"/>
        (`rank`,  `content`, `parent_id`, `profile_type`, `is_delete`)
      values (#{rank}, #{content}, #{parentId}, #{profileType}, #{isDelete})
    </insert>

    <select id = "selectValidByProfileType" resultType="com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileSettings">
      SELECT *
      FROM
      <include refid="tableName"/>
      WHERE profile_type = #{profileType}
      AND is_delete = 0
    </select>

    <select id = "selectByParentIdAndDataStatus" resultType="com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileSettings">
       SELECT *
       FROM
       <include refid="tableName"/>
       WHERE parent_id = #{parentId}
       AND profile_type = #{profileType}
       AND is_delete = #{dataStatus}
       ORDER BY rank
    </select>

    <select id = "selectByParentIdAndContent" resultType="com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileSettings">
        SELECT *
        FROM
        <include refid="tableName"/>
        WHERE parent_id = #{parentId}
        AND profile_type = #{profileType}
        AND content = #{content}
    </select>

    <select id = "selectByIds" resultType="com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileSettings">
        SELECT *
        FROM
        <include refid="tableName"/>
        WHERE
        id IN
       <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
           #{item}
       </foreach>

    </select>

    <update id = "updateDataStatusRank">
        UPDATE
        <include refid="tableName"/>
        SET is_delete = #{dataStatus},
        rank = #{rank}
        WHERE id = #{id}
    </update>

    <update id = "updateDataRank">
        UPDATE
        <include refid="tableName"/>
        SET rank = #{rank}
        WHERE id = #{id}
    </update>

    <update id = "updateUseSize">
        UPDATE
        <include refid="tableName"/>
        SET use_size = use_size + #{useSize}
        WHERE id IN
        <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </update>

    <select id = "selectById" resultType="com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileSettings">
        SELECT *
        FROM
        <include refid="tableName"/>
        WHERE id = #{id}
    </select>


</mapper>