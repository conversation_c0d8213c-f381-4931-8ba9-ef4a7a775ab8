<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.stat.message.HuZhuSqlRecordDao">

    <sql id="tableName">
        `huzhu_sql_record`
    </sql>

    <sql id="insert_fields">
      `sql_id`,
      `name`,
      `description`,
      `biz_type`,
      `hive_sql`,
      `result_count`,
      `result_path`,
      `status`,
      `user_id`,
      `user_name`,
      `sql_create_time`,
      `sql_update_time`
    </sql>

    <insert id="insertHuZhuSqlRecord" parameterType="com.shuidihuzhu.cf.model.message.HuZhuSqlRecord">
        INSERT INTO
        <include refid="tableName"/>
        (<include refid="insert_fields"/>)
        VALUES
        (
        #{sqlId},#{name},#{description},#{bizType},#{hiveSql},#{resultCount},
        #{resultPath},#{status},#{userId},#{userName},#{sqlCreateTime},#{sqlUpdateTime}
        )
    </insert>

    <select id="queryHuZhuSqlRecordBySqlId" resultType="com.shuidihuzhu.cf.model.message.HuZhuSqlRecord">
        SELECT * from
        <include refid="tableName"/>
        WHERE `sql_id` = #{sqlId}
        AND `is_delete` = '0'
        ORDER BY `id` DESC
        LIMIT 1
    </select>

    <update id="deleteHuZhuSqlRecord">
        UPDATE
        <include refid="tableName"/>
        SET `is_delete` = '1'
        WHERE `id` = #{id}
    </update>

    <select id="queryBySqlIdSet" resultType="com.shuidihuzhu.cf.model.message.HuZhuSqlRecord">
        SELECT * from
        <include refid="tableName"/>
        WHERE `sql_id` in
        <foreach collection="sqlIdSet" open="(" separator="," item="sqlId" close=")">
            #{sqlId}
        </foreach>
        AND `is_delete` = '0'
    </select>
</mapper>