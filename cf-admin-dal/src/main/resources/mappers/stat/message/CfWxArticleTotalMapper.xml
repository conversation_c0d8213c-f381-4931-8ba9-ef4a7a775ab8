<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.stat.message.CfWxArticleTotalDao">

    <sql id="table_name">
        `stat_wx_article_total`
    </sql>

    <sql id="insert_fields">
     `third_type`,`ref_date`, `msg_data_id`,`index`, `title`,`stat_date`, `target_user`,`page_read_user`,`page_read_count`,
     `ori_page_read_user`,`ori_page_read_count`,`share_user`, `share_count`,`add_to_fav_user`, `add_to_fav_count`,
     `page_from_session_read_user`,`page_from_session_read_count`,`page_from_hist_msg_read_user`,`page_from_hist_msg_read_count`,
     `page_from_feed_read_user`,`page_from_feed_read_count`,`page_from_friends_read_user`,`page_from_friends_read_count`,
     `page_from_other_read_user`, `page_from_other_read_count`,`feed_share_from_session_user`, `feed_share_from_session_cnt`,
     `feed_share_from_feed_user`,`feed_share_from_feed_cnt`,`feed_share_from_other_user`,`feed_share_from_other_cnt`
    </sql>

    <select id="getCfWxArticleTotalByMediaIdAndThirdType" resultType="com.shuidihuzhu.cf.model.message.CfWxArticleTotal">
        SELECT *
        FROM
        <include refid="table_name"/>
        WHERE `third_type` = #{thirdType}
        AND `msg_data_id` =#{msgDataId}
        AND `index` = #{index}
        ORDER BY `stat_date` DESC
        LIMIT 1
    </select>

    <select id="getCfWxArticleTotalByDate" resultType="com.shuidihuzhu.cf.model.message.CfWxArticleTotal">
        SELECT *
        FROM
        <include refid="table_name"/>
        WHERE `third_type` = #{thirdType}
        AND `msg_data_id` =#{msgDataId}
        AND `index` = #{index}
        AND `stat_date` = #{statDate}
        ORDER BY `stat_date` DESC
        LIMIT 1
    </select>
</mapper>