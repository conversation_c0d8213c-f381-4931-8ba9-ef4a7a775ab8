<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.stat.message.CfWxArticleSummaryDao">

    <sql id="table_name">
        `stat_wx_article_summary`
    </sql>

    <sql id="insert_fields">
        `third_type`,`ref_date`,`msg_data_id`,`index`,`title`,`page_read_user`,`page_read_count`,
        `ori_page_read_user`,`ori_page_read_count`,`share_user`,`share_count`,`add_to_fav_user`,`add_to_fav_count`
    </sql>

    <select id="querySubtaskLastRecord" resultType="com.shuidihuzhu.cf.model.message.CfWxArticleSummary">
        SELECT *
        FROM
        <include refid="table_name"/>
        WHERE `third_type` = #{thirdType}
        AND `msg_data_id` =#{msgDataId}
        AND `index` = #{index}
        ORDER BY `ref_date` DESC
        LIMIT 1
    </select>
</mapper>