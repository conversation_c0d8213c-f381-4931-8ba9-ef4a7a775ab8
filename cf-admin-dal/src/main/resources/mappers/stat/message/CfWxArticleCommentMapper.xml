<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.stat.message.CfWxArticleCommentDao">
    <sql id="table_name">
        `stat_wx_article_comment`
    </sql>
    <select id="count" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM
        <include refid="table_name"/>
        WHERE `third_type` = #{thirdType}
        AND `msg_data_id` = #{msgDataId}
        AND `index` = #{index}
        AND `is_delete` = 0
    </select>

    <select id="maxUserCommentId" resultType="java.lang.Integer">
        SELECT max(`user_comment_id`)
        FROM
        <include refid="table_name"/>
        WHERE `third_type` = #{thirdType}
        AND `msg_data_id` = #{msgDataId}
        AND `index` = #{index}
        AND `is_delete` = 0
    </select>
</mapper>