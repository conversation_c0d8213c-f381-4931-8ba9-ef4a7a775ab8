<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.stat.HandleHospitalDao">

    <sql id="tableName">
        `shuidi_stat`.`temp_handle_hospital`
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.vo.crowdfunding.HandleHospital">
        INSERT INTO <include refid="tableName"/>
        (`name`,`province`,`city`,`province_id`,`city_id`)
        VALUES (#{name}, #{province}, #{city}, #{province_id}, #{city_id})
    </insert>

    <insert id="insertInit" parameterType="com.shuidihuzhu.cf.vo.crowdfunding.HandleHospital">
        INSERT INTO <include refid="tableName"/>
        (`name`,`city`)
        VALUES
        <foreach collection="list" item="item" separator=",">
        (#{item.name}, #{item.city})
        </foreach>
    </insert>

    <select id="selectAll" resultType="com.shuidihuzhu.cf.vo.crowdfunding.HandleHospital">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE `id` > #{count}
        LIMIT #{offSet}, 3000
    </select>

    <update id="update" parameterType="com.shuidihuzhu.cf.vo.crowdfunding.HandleHospital">
        UPDATE <include refid="tableName"/>
        SET
        `name`=#{name},
        `province`=#{province},
        `city`=#{city},
        `province_id`=#{provinceId},
        `city_id`=#{cityId}
        WHERE `id`=#{id}
    </update>

    <select id="selectMax" resultType="int">
        SELECT count(*) FROM <include refid="tableName"/>
    </select>

    <update id="updateValid">
        UPDATE <include refid="tableName"/>
        SET `disable`=#{disable}
        WHERE `id`=#{id}
    </update>
</mapper>