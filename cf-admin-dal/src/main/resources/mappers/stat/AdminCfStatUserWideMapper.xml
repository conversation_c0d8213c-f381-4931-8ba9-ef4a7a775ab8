<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.stat.AdminCfStatUserWideDao">
	<sql id="tableName">
		stat_cf_user_wide
	</sql>

	<sql id="selectFields">
		`id` as id,
		`user_id` as userId,
		`create_dt` as createDate,
		`create_time` as createTime,
		`open_id` as openId,
		`is_toufang_sign` as isToufangSign,
		`toufang_sign_channel` as toufangSignChannel,
		`toufang_sign_first_time` as toufangSignFirstTime,
		`is_follow` as isFollow,
		`follow_type` as followType,
		`follow_first_time` as followFirstTime,
		`follow_qr_code` as followQr<PERSON><PERSON>,
		`follow_qr_code_channel` as followQrCodeChannel,
		`follow_qr_code_channel_group` as followQrCodeChannelGroup,
		`is_app` as isApp,
		`app_first_time` as appFirstTime,
		`is_other_sign` as isOtherSign,
		`other_sign_type` as otherSignType,
		`other_sign_channel` as otherSignChannel,
		`other_sign_first_time` as otherSignFirstTime,
		`other_sign_id` as otherSignId,
		`other_sign_mobile` as otherSignMobile,
		`is_adviser_sign` as isAdviserSign,
		`adviser_sign_time` as adviserSignTime,
		`adviser_sign_first_time` as adviserSignFirstTime,
		`adviser_sign_channel` as adviserSignChannel
	</sql>

	<select id="selectUserIdByChannelGroup" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfStatUserWide">
        SELECT <include refid="selectFields"/> FROM <include refid="tableName"/>
        WHERE `follow_qr_code_channel_group`=#{channelGroup}
        AND `is_follow`=#{isFollow}
        LIMIT #{start},#{size}
    </select>

</mapper>