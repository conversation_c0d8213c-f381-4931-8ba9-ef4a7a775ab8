<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.stat.TaskDailyDao">
    <select id="getNewOrderCounts" parameterType="java.sql.Date" resultType="java.util.HashMap">
        SELECT * FROM task_daily_new_order WHERE day = #{day}
    </select>

    <select id="getDailySummary" resultType="java.util.HashMap">
        SELECT
        `day`,
        total_order_count,
        total_insured_user_count,
        total_buyer_count,
        total_order_amount,
        total_adult_count,
        total_adult_amount,
        total_adult_mean_amount,
        total_children_count,
        total_children_amount,
        total_children_mean_amount,
        total_oldman_count,
        total_oldman_amount,
        total_oldman_mean_amount,
        total_accident_count,
        total_accident_amount,
        total_accident_mean_amount
        FROM
        task_daily_summary1
        WHERE
        `day` BETWEEN #{start} AND #{end}
        ORDER BY `day` DESC
    </select>

    <select id="getDailySummaryOfNew" resultType="java.util.HashMap">
        SELECT
        `day`,
        total_order_count,
        total_insured_user_count,
        total_buyer_count,
        total_order_amount,
        total_adult_count,
        total_adult_amount,
        total_adult_mean_amount,
        total_children_count,
        total_children_amount,
        total_children_mean_amount,
        total_oldman_count,
        total_oldman_amount,
        total_oldman_mean_amount,
        total_accident_count,
        total_accident_amount,
        total_accident_mean_amount
        FROM
        task_daily_summary_new1
        WHERE
        `day` BETWEEN #{start} AND #{end}
        ORDER BY `day` DESC
    </select>
</mapper>

