<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.stat.CfWxUserCumulateDataDao">
    <sql id="tableName">
        cf_user_cumumlate_data
    </sql>

    <select id="queryByRefDateAndThirdType" resultType="com.shuidihuzhu.cf.model.message.CfWxUserCumulateData">
        SELECT *
        FROM
        <include refid="tableName"/>
        WHERE `third_type` = #{thirdType}
        AND `ref_date` = #{refDate}
    </select>

</mapper>