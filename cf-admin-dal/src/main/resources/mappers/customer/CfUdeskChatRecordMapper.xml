<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.customer.CfUdeskChatRecordDao">
    <sql id="table">
        cf_udesk_chat_record
    </sql>

    <select id="getUdeskChatRecords" resultType="com.shuidihuzhu.cf.customer.UdeskChatVo">
        select created_at as createdAt,
               chat_content as chatContent,
               sender as sender,
               session_id as sessionId,
               content_type as contentType,
               user_id as  udeskCustomerId
        from cf_udesk_chat_record chat
        where is_delete=0
              and session_id in
            <foreach collection="subSessionIds" item="subSessionId" open="(" close=")" separator=",">
                #{subSessionId}
            </foreach>
            <if test="endTime != null and endTime != ''">
              and created_at &lt; #{endTime}
            </if>
            <if test="startTime != null and startTime != ''">
              and created_at > #{startTime}
            </if>
    </select>

</mapper>