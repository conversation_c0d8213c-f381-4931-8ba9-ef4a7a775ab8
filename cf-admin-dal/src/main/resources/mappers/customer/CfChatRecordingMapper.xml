<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.customer.CfChatRecordingDao">
    <sql id="table">
        cf_chat_recording
    </sql>

    <sql id="fields">
        `send_time` as sendTime,
        `msg` as msg,
        `sender_name` as senderName
    </sql>

    <select id="getChatHistoryByCids" resultType="com.shuidihuzhu.cf.customer.ChatHistoryModelVo">
        select
        <include refid="fields"/>
        from
        <include refid="table"/>
        where is_delete = 0
          <if test="startTime != null and startTime!= ''">
            and `send_time`  <![CDATA[ >= ]]> #{startTime}
          </if>
          <if test="endTime != null and endTime != ''">
            and `send_time`  <![CDATA[ <= ]]> #{endTime}
          </if>
          and c_id IN
          <foreach collection="cids" item="cid" open="(" separator="," close=")">
             #{cid}
          </foreach>
    </select>

    <select id="queryChatRecordByCid" resultType="com.shuidihuzhu.cf.customer.ChatHistoryModelVo">
        select <include refid="fields"/>
        from <include refid="table"/>
        where c_id = #{cid} and is_delete = 0
        order by id desc
    </select>

</mapper>