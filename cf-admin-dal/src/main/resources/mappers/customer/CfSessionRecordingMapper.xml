<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.customer.CfSessionRecordingDao">
    <sql id="table">
        cf_session_recording
    </sql>

    <sql id="fields">
        `c_id`
    </sql>

    <select id="getCidsByPartnerIds" resultType="java.lang.String">
        select
        <include refid="fields"/>
        from
        <include refid="table"/>
        where
        partner_id in
        <foreach collection="partnerIds" item="partnerId" open="(" close=")" separator=",">
            #{partnerId}
        </foreach>
        and
        is_delete = 0
    </select>

    <select id="queryRecordByUserId" resultType="com.shuidihuzhu.cf.customer.CfChatRecordDO">
        select *
        from <include refid="table"/>
        where partner_id = #{partnerId} and is_delete = 0
    </select>

    <select id="getCidsByPhoneNumber" resultType="java.lang.String">
        select
        <include refid="fields"/>
        from
        <include refid="table"/>
        where
        phone_number= #{phoneNumber}
        and
        is_delete = 0
    </select>

</mapper>