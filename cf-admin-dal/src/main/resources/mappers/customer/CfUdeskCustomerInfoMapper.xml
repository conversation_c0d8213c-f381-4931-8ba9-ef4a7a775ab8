<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.customer.CfUdeskCustomerInfoDao">
    <sql id="table">
        cf_udesk_customer_info
    </sql>

    <select id="getUdeskCustomersByCfUserIds"
            resultType="com.shuidihuzhu.cf.customer.CfUdeskCustomerInfo">
        select distinct(udesk_customer_id), nick_name
        from <include refid="table"/>
        where is_delete=0
        and cf_user_id in
        <foreach collection="cfUserIds" separator="," open="(" close=")" item="cfUserId">
            #{cfUserId}
        </foreach>
    </select>

    <select id="getNickNameByUdeskUserId" resultType="java.lang.String">
        select nick_name from <include refid="table"/>
        where udesk_customer_id = #{udeskUserId}
    </select>

</mapper>