<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.customer.CfUdeskSessionRecordDao">
    <sql id="table">
        cf_udesk_session_info_record
    </sql>

    <select id="getUdeskSessionsByUdeskUserIds"
            resultType="com.shuidihuzhu.cf.customer.CfUdeskSessionRecordVo">
        select sub_session_id, agent_nick_name
        from <include refid="table"/>
        where is_delete = 0
        and udesk_customer_id in
        <foreach collection="udeskUserIds" separator="," open="(" close=")" item="udeskUserId">
            #{udeskUserId}
        </foreach>
    </select>

    <select id="listRecordByUdskIds" resultType="com.shuidihuzhu.cf.customer.CfChatRecordDO">
        select `sustain_seconds` as conversationDuration, `created_at` as startTime,
        `closed_at` as endTime, `agent_nick_name` as staffName, `sub_session_id` as cId
        from <include refid="table"/>
        where udesk_customer_id in
        <foreach collection="udeskUserIds" separator="," open="(" close=")" item="udeskUserId">
            #{udeskUserId}
        </foreach>
    </select>

    <select id="getNickNameBySubSessionId" resultType="java.lang.String">
        select agent_nick_name
        from <include refid="table"/>
        where sub_session_id = #{subSessionId}
    </select>

    <select id = "selectByCustomIdAndCreateAt" resultType="java.lang.Integer">
        select COUNT(*)
        from <include refid="table"/>
        where
        udesk_customer_id IN
        <foreach collection="customIds" item="customId" open="(" separator="," close=")">
            #{customId}
        </foreach>
        <if test="createdAt != null">
            and created_at >= #{createdAt}
        </if>
        and is_delete = 0
    </select>

    <select id = "selectLastRecordByCustomId" resultType="com.shuidihuzhu.cf.model.CrmUserManage.UserManage$UdeskBizRecord">
        select created_at, biz_record_classify
        from <include refid="table"/>
        where udesk_customer_id IN
        <foreach collection="customIds" item="customId" open="(" separator="," close=")">
            #{customId}
        </foreach>
        and is_delete = 0
        order by created_at desc limit 1
    </select>



</mapper>