package com.shuidihuzhu.cf.dao.admin.stats;

import com.shuidihuzhu.cf.model.admin.stats.AdminUsageFreqStats;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface AdminUsageFreqStatsDao {
    int insertSelective(AdminUsageFreqStats record);

    AdminUsageFreqStats selectByPrimaryKey(Long id);

    AdminUsageFreqStats getByUniqueCondition(@Param("statsKey") String statsKey, @Param("statsDim") String statsDim,
                                             @Param("bizType") Integer bizType);

    List<AdminUsageFreqStats> getByUniqueConditions(@Param("statsKeys") List<String> statsKeys, @Param("statsDim") String statsDim,
                                              @Param("bizType") Integer bizType);

    int updateByUniqueCondition(@Param("statsKey") String statsKey, @Param("statsDim") String statsDim,
                                @Param("bizType") Integer bizType, @Param("increment") int increment);

    int updateByUniqueConditions(@Param("statsKeys") List<String> statsKeys, @Param("statsDim") String statsDim,
                                @Param("bizType") Integer bizType, @Param("increment") int increment);
}