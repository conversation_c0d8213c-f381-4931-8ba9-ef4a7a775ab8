package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportModuleOperationLog;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportProblemOperationLog;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(AdminDS.CF_RW)
public interface CfReportProblemOperationLogDao {

    int add(@Param("problemId") int problemId, @Param("action") String action, @Param("operator") String operator);


    List<CfReportProblemOperationLog> getByProblemId(@Param("problemId")int problemId);

}
