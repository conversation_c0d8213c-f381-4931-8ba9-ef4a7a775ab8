package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.admin.CfQuestionnaire;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2019/12/11
 */
@DataSource(DS.CF)
public interface CfQuestionnaireDao {

    int save(CfQuestionnaire cfQuestionnaire);

    String getByCard(@Param("card") String card);

    List<CfQuestionnaire> getListByCard(@Param("card") String card);


    int updateContentByUserId(@Param("questionnaireId") long questionnaireId,
                              @Param("content") String content,
                              @Param("status") int status,
                              @Param("qname") String qname,
                              @Param("startAnsweringTime") String startAnsweringTime,
                              @Param("endTime") String endTime);

    CfQuestionnaire getById(@Param("id") long id);

    List<CfQuestionnaire> getByIds(@Param("ids") List<Long> ids);


    int saveCaseId(@Param("recordId") long recordId, @Param("caseId") int caseId);


    int saveOperatorIdAndComment(@Param("id") long id, @Param("operatorId") int operatorId, @Param("comment") String comment);

}
