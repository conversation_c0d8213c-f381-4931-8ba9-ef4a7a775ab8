package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonCustomEntity;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2022/1/6 19:30
 * @Description:
 */
@DataSource(DS.CF)
public interface CfRefuseReasonCustomEntityDao {

    int insertOne(CfRefuseReasonCustomEntity cfRefuseReasonCustomEntity);

    List<CfRefuseReasonCustomEntity> selectByCaseId(@Param("caseId") int caseId);

    List<CfRefuseReasonCustomEntity> selectByWorkOrderId(@Param("workOrderId") long workOrderId);

    int updateStatusByCaseIdAndRefuseId(CfRefuseReasonCustomEntity cfRefuseReasonCustomEntity);
}
