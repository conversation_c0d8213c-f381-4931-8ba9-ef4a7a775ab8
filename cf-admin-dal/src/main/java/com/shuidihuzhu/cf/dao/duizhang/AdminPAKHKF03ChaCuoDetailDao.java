package com.shuidihuzhu.cf.dao.duizhang;

import com.shuidihuzhu.client.baseservice.pay.model.pingan.duizhang.PAChacuoDetail;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by ch<PERSON> on 2017/10/26.
 */
@DataSource(DS.PAY)
public interface AdminPAKHKF03ChaCuoDetailDao {
	void add(@Param("details") List<PAChacuoDetail> details);

	void delete(@Param("fileDate") String fileDate);

	List<PAChacuoDetail> get(@Param("fileDate") String fileDate, @Param("offset") int offset,
	                         @Param("limit") int limit);

	List<PAChacuoDetail> getByFlowNo(@Param("flowNos") List<String> flowNos);
}
