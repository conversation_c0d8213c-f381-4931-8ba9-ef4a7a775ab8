package com.shuidihuzhu.cf.dao.admin;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.crowdfunding.CfCreditSupplement;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(AdminDS.CF_RW)
public interface AdminCfCreditSupplementDao {
    List<CfCreditSupplement> selectByInfoUuid(@Param("infoUuid") String infoUuid);

    List<CfCreditSupplement> selectByInfoUuidList(@Param("infoUuidList") List<String> infoUuidList);
}
