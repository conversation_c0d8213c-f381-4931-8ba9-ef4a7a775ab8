package com.shuidihuzhu.cf.dao.admin;

import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 催票据上传外呼信息表数据访问层
 * @Author: pan<PERSON><PERSON><PERSON>
 * @Date: 2021/12/9 5:08 下午
 */
@DataSource("shuidiCfAdminDataSource")
public interface IPromoteWebCallDao {

    Integer selectPublishNumByCaseId(@Param("caseId") int caseId);

    int updateByCaseId(@Param("caseId") int caseId, @Param("publishTime") String publishTime, @Param("publishNum") long publishNum);

    int updatePublishNumByCaseId(@Param("caseId") int caseId, @Param("publishNum") long publishNum);

}
