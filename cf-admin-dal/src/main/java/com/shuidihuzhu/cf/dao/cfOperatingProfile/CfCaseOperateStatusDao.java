package com.shuidihuzhu.cf.dao.cfOperatingProfile;

import com.shuidihuzhu.cf.model.cfOperatingProfile.CfCaseOperateStatus;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface CfCaseOperateStatusDao {

    List<CfCaseOperateStatus> selectByIdAndOperateType(@Param("caseId") int caseId, @Param("statusList") List<Integer> statusList);

    int insertOne(CfCaseOperateStatus operateStatus);

    int updateById(CfCaseOperateStatus operateStatus);
}
