package com.shuidihuzhu.cf.dao.workorder;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.domain.workorder.FollowLabelDO;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * @Description:
 * @Author: pangh<PERSON><PERSON>
 * @Date: 2022/7/22 2:39 下午
 */
@DataSource("shuidiCfAdminDataSource")
public interface WorkOrderFollowLabelDao {

    void addFollowLabel(FollowLabelDO followLabelDO);

    void updateByWorkOrderId(@Param("followLabel") int followLabel, @Param("workOrderId") Long workOrderId);

    FollowLabelDO selectByWorkOrderId(Long workOrderId);

}
