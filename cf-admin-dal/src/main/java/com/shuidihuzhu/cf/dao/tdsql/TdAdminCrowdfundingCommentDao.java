package com.shuidihuzhu.cf.dao.tdsql;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingComment;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.common.web.util.admin.BasicExample;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;

/**
 * Created by roy on 16/7/18.
 */

@DataSource("shuidiCfTdDataSource")
public interface TdAdminCrowdfundingCommentDao {
    List<CrowdfundingComment> getByPage(BasicExample basicExample);

    List<CrowdfundingComment> getCommentByParentIdFromTiDb(@Param("parentId") long parentId, @Param("limit") int limit);

    List<CrowdfundingComment> getCommentByUserIdFromTiDb(@Param("userId") long userId, @Param("limit") int limit);

    List<CrowdfundingComment> getCommentByUserIdAndTypeFromTiDb(@Param("userId") long userId, @Param("type") int type, @Param("limit") int limit);

    /**
     * 从cf-api中迁移到此
     *
     * @param begin
     * @param end
     * @return
     */
    Integer selectCountByMin(@Param("begin") Timestamp begin, @Param("end") Timestamp end);
}

