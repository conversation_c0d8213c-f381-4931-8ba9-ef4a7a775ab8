package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.AdminReportDataVo;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderReport;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkReportMap;
import com.shuidihuzhu.cf.model.crowdfunding.ReportWorkStat;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderDataVo;
import com.shuidihuzhu.cf.vo.crowdfunding.AdminWorkOrderReportVo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/10.
 */
@DataSource(DS.CF)
public interface AdminWorkOrderReportDao {
    int insertAdminWorkOrderReport(AdminWorkOrderReport adminWorkOrderReport);

    int updateAppointTime(@Param("workOrderId")int workOrderId, @Param("appointTime") Date appointTime);

    List<AdminWorkOrderReport> getByWorkOrderIds(@Param("workOrderIds") List<Long> workOrderIds);

    AdminWorkOrderReport getAdminWorkOrderReportByCaseId(@Param("caseId") int caseId);

    @DataSource(DS.CF_SLAVE_2)
    AdminWorkOrderReport getOneByCaseIdAndStatusList(@Param("caseId") int caseId,
                                                         @Param("statusList")List<Integer> statusList);

    @DataSource(DS.CF_SLAVE_2)
    List<AdminWorkOrderReport> listByCaseIdsAndStatusList(@Param("caseIds") List<Integer> caseIds,
                                                     @Param("statusList")List<Integer> statusList);

    @DataSource(DS.CF_SLAVE_2)
    List<AdminWorkOrderReport> getAdminWorkOrderReportByCount(@Param("orderType") int orderType,
                                                              @Param("orderTask") int orderTask,
                                                              @Param("count") int count);

    @DataSource(DS.CF_SLAVE_2)
    List<AdminWorkOrderReportVo> getAdminWorkOrderReportList(@Param("userId") int userId,
                                                             @Param("lostContact") int lostContact,
                                                             @Param("addTrustStatus") Integer addTrustStatus,
                                                             @Param("followStatus") Integer followStatus,
                                                             @Param("approveStatus") Integer approveStatus,
                                                             @Param("drawStatus") Integer drawStatus,
                                                             @Param("drawApplyStatus") Integer drawApplyStatus,
                                                             @Param("refundStatus") Integer refundStatus,
                                                             @Param("infoId") Integer infoId,
                                                             @Param("reprotType") Integer reprotType,
                                                             @Param("realName") Integer realName,
                                                             @Param("appointStartTime") String appointStartTime,
                                                             @Param("appointEndTime") String appointEndTime);

    @DataSource(DS.CF_SLAVE_2)
    List<AdminWorkOrderReport> getAdminWorkOrderReportById(@Param("ids") List<Integer> ids);

    @DataSource(DS.CF_SLAVE_2)
    int getAdminWorkOrderReportCount(@Param("orderType") int orderType, @Param("orderTask") int orderTask,
                                     @Param("startTime") String startTime, @Param("endTime") String endTime);

    int updateCaseRisk(@Param("id") int id, @Param("caseRisk") int caseRisk);

    @DataSource(DS.CF_SLAVE_2)
    List<AdminWorkOrderReportVo> selectAdminWorkOrderReport(@Param("lostContact") int lostContact,
                                                            @Param("addTrustStatus") Integer addTrustStatus,
                                                            @Param("followStatus") Integer followStatus,
                                                            @Param("caseRisk") Integer caseRisk,
                                                            @Param("startTime") String startTime,
                                                            @Param("endTime") String endTime,
                                                            @Param("isDrawCash") Integer isDrawCash,
                                                            @Param("operatorIds") List<Integer> reportFollowOperator,
                                                            @Param("realName") Integer realName);

    int updateDealResultById(@Param("id") int id, @Param("dealResult") int dealResult);

    @DataSource(DS.CF_SLAVE_2)
    int getCount(@Param("orderType") int orderType, @Param("orderTask") int orderTask,
                 @Param("startTime") String startTime, @Param("endTime") String endTime);

    @DataSource(DS.CF_SLAVE_2)
    int getDealCount(@Param("orderType") int orderType, @Param("orderTask") int orderTask,
                     @Param("startTime") String startTime, @Param("endTime") String endTime,
                     @Param("dealResult") int dealResult);

    @DataSource(DS.CF_SLAVE_2)
    int getOldFollowCount(@Param("orderType") int orderType, @Param("orderTask") int orderTask,
                          @Param("startTime") String startTime, @Param("dealResult") int dealResult);

    @DataSource(DS.CF_SLAVE_2)
    List<AdminWorkOrderDataVo> getNoNeedDealCountByUserIds(@Param("orderType") int orderType,
                                                           @Param("orderTask") int orderTask,
                                                           @Param("startTime") String startTime,
                                                           @Param("endTime") String endTime,
                                                           @Param("dealResult") int dealResult,
                                                           @Param("userIds") List<Integer> userIds);

    @DataSource(DS.CF_SLAVE_2)
    List<AdminWorkOrderDataVo> getOldFollowCountByUserIds(@Param("orderType") int orderType,
                                                          @Param("orderTask") int orderTask,
                                                          @Param("startTime") String startTime,
                                                          @Param("dealResult") int dealResult,
                                                          @Param("userIds") List<Integer> userIds);

    @DataSource(DS.CF_SLAVE_2)
    List<AdminWorkOrderReport> selectWorkOrderReportByCaseIds(@Param("caseIds") List<Integer> caseIds);

    List<AdminWorkOrderReportVo> getWorkOrderOperatorIdByCaseIds(@Param("caseIds") List<Integer> caseIds);

    int updateFollowTypeById(@Param("followType") int followType, @Param("id") int id);

    @DataSource(DS.CF_SLAVE_2)
    List<AdminWorkOrderDataVo> getCountByUserIds(@Param("orderType") int orderType, @Param("orderTask") int orderTask,
                                                 @Param("startTime") String startTime, @Param("endTime") String endTime,
                                                 @Param("userIds") List<Integer> userIds);

    AdminWorkOrderReport selectWorkOrderReportById(@Param("id") int id);

    @DataSource(DS.CF_SLAVE_2)
    List<AdminReportDataVo> getAdminReportCount(@Param("list")List<Long> workIds);

    @DataSource(DS.CF_SLAVE_2)
    List<AdminReportDataVo> getAdminReportDataVo(@Param("workId")Long workId,@Param("status") Integer status, @Param("operator")Long operator, @Param("caseId")Long caseId,@Param("startDealTime")String startDealTime,@Param("endDealTime")String endDealTime,
                                                 @Param("startHandleTime")String startHandleTime,@Param("endHandleTime")String endHandleTime,@Param("startCreateTime")String startCreateTime,@Param("endCreateTime")String endCreateTime);

    int addAdminWorkReportMap(AdminWorkReportMap map);

    @DataSource(DS.CF_SLAVE_2)
    List<AdminWorkReportMap> getLastAdminWorkReportMap(@Param("workOrderId")long workOrderId);


    @DataSource(DS.CF_SLAVE_2)
    List<AdminWorkReportMap> getWorkReportMapList(@Param("list")List<Long> workOrderIds);



    int getNewNum(@Param("today") Date today, @Param("yesterday") Date yesterday);

    int getNewActivityNum(@Param("today")Date today,@Param("yesterday") Date yesterday);

    int getAllActivityNum(@Param("today")Date today,@Param("yesterday") Date yesterday);


    int getFinishNum(@Param("today")Date today,@Param("yesterday") Date yesterday);

    int getFinishNewNum(@Param("today")Date today,@Param("yesterday")Date yesterday);

    int getLostNum(@Param("today")Date today,@Param("yesterday")Date yesterday);

    int getRisktNum(@Param("today")Date today,@Param("yesterday")Date yesterday);

    int getDoingNum();


    List<ReportWorkStat> getNewNumByUser(@Param("today")Date today, @Param("yesterday")Date yesterday,@Param("list") List<Integer> userIds);

    List<ReportWorkStat> getNewActivityNumByUser(@Param("today")Date today, @Param("yesterday")Date yesterday,@Param("list") List<Integer> userIds);

    List<ReportWorkStat> getDoingNumByUser(@Param("list") List<Integer> userIds);

    List<ReportWorkStat> getAllActivityNumByUser(@Param("today")Date today, @Param("yesterday")Date yesterday,@Param("list") List<Integer> userIds);



    List<ReportWorkStat> getFinishNumByUser(@Param("today")Date today,@Param("yesterday") Date yesterday,@Param("list") List<Integer> userIds);

    List<ReportWorkStat> getFinishNewNumByUser(@Param("today")Date today, @Param("yesterday")Date yesterday,@Param("list") List<Integer> userIds);

    List<ReportWorkStat> getLostNumByUser(@Param("today")Date today, @Param("yesterday")Date yesterday,@Param("list") List<Integer> userIds);

    List<ReportWorkStat> getRisktNumByUser(@Param("today")Date today, @Param("yesterday")Date yesterday,@Param("list") List<Integer> userIds);

    int updateHandkeTime(@Param("list") List<Long> workOrderIdList);

    @DataSource(DS.CF_SLAVE_2)
    List<AdminWorkReportMap> getReportMapByReportIds(@Param("list") List<Integer> reportIds);

    @DataSource(DS.CF_SLAVE_2)
    AdminWorkReportMap getAdminWorkReportMapByReportId(@Param("reportId") long reportId);

    List<AdminWorkOrderReport> findByCaseIdAndDealResult(@Param("caseId") int caseId, @Param("dealResults") List<Integer> dealResults);

    AdminWorkOrderReport findByCaseIdAndReportId(@Param("caseId") int caseId, @Param("reportId") int reportId);

}
