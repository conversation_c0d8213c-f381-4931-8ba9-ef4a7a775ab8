package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplateRecord;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

import java.util.List;

/**
 * <AUTHOR> Ahrievil
 * @date : 2018/6/27 19:19
 */
@DataSource(AdminDS.CF_RW)
public interface AdminCfBaseInfoTemplateRecordDao {

    List<CfBaseInfoTemplateRecord> selectByParam(CfBaseInfoTemplateRecord param);

}
