package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfFirstApproveOrderDomain;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.AdminTaskUgc;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * Created by Ahrievil on 2017/11/30
 */
@DataSource(DS.CF)
public interface AdminTaskUgcDao {

    int insertOne(AdminTaskUgc adminTaskUgc);

    int insertList(@Param("list") List<AdminTaskUgc> list);

    @DataSource(DS.CF_SLAVE)
    List<AdminTaskUgc> selectByWorkOrderIds(@Param("list") List<Long> list);

    @DataSource(DS.CF_SLAVE)
    List<AdminTaskUgc> selectModulesLimit(@Param("modules") int modules, @Param("start") int start, @Param("size") int size);

    AdminTaskUgc selectByWorkOrderId(@Param("workOrderId") long workOrderId);

    int updateResult(@Param("id") long id, @Param("result") int result);

    int deleteByWordIdList(@Param("list") List<Long> list);


    AdminTaskUgc selectFirstByCaseId(@Param("caseId")long caseId);


    String selectFirstCommentByCaseId (@Param("caseId")long caseId);
    @DataSource(DS.CF_SLAVE)
    List<AdminTaskUgc> selectByCreateTimeAndTaskType(@Param("beginDate")Date beginDate, @Param("endDate")Date endDate,
                                                     @Param("contentType")int contentType, @Param("limit")int limit);

    @DataSource(DS.CF_SLAVE)
    List<AdminTaskUgc> selectByUpdateTimeAndTaskStatus(@Param("beginDate")Date beginDate, @Param("endDate")Date endDate,
                                                       @Param("contentType")int contentType,
                                                       @Param("resultStatus") List<Integer> resultStatus,
                                                       @Param("limit")int limit);

    AdminTaskUgc selectLatelyTaskByCaseIdAndContent(@Param("caseId") int caseId, @Param("contentType") int contentType);
}
