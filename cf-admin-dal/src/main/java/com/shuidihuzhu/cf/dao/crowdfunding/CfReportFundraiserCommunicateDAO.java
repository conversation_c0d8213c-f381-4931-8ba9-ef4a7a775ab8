package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfFundraiserCommunicateDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/12/16 下午8:24
 * @desc
 */
@DataSource(DS.CF)
public interface CfReportFundraiserCommunicateDAO {
    int insert(CfFundraiserCommunicateDO communicateDO);
    int updateConnectStatus(@Param("id") long id, @Param("connectStatus") int connectStatus);
    int updateAnswer(@Param("id") long id, @Param("connectStatus") int connectStatus, @Param("answerIds") String answerIds);
    List<CfFundraiserCommunicateDO> query(@Param("caseId") int caseId);
    CfFundraiserCommunicateDO queryByIdAndCase(@Param("id") long id, @Param("caseId") int caseId);
    CfFundraiserCommunicateDO queryById(@Param("id") long id);

    CfFundraiserCommunicateDO getByMobileAndCaseId(@Param("caseId") int caseId, @Param("mobile") String mobile);
}
