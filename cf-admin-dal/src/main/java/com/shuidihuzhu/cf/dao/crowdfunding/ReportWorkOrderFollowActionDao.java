package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.report.ReportWorkOrderCount;
import com.shuidihuzhu.cf.model.report.ReportWorkOrderFollowAction;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @author: lix<PERSON><PERSON><PERSON>
 * @create: 2020-07-27 11:13
 **/
@DataSource(AdminDS.CF_RW)
public interface ReportWorkOrderFollowActionDao {

    int insertOne(ReportWorkOrderFollowAction reportWorkOrderFollowAction);


    List<ReportWorkOrderCount> getCountByOperatorId(@Param("operatorIds") List<Long> operatorIds,
                                                    @Param("dayOfZero") Date dayOfZero,
                                                    @Param("orderTypes") List<Integer> orderTypes);


    ReportWorkOrderCount getCount(@Param("operatorId") long operatorId,
                                  @Param("dayOfZero") Date dayOfZero,
                                  @Param("orderType") int orderType);


    List<ReportWorkOrderFollowAction> getByWorkOrderId(@Param("workOrderId") long workOrderId,
                                                       @Param("actionTypes") List<Integer> actionTypes);


    List<Long> getWorkOrderId(@Param("operatorId") long operatorId,
                                  @Param("dayOfZero") Date dayOfZero,
                                  @Param("orderTypes") List<Integer> orderTypes);
}
