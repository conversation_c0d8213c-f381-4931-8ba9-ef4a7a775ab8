package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.report.CfReportAddTrustSnapshot;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@DataSource(DS.CF)
public interface CfReportAddTrustSnapshotDao {

    int insertOne(CfReportAddTrustSnapshot cfReportAddTrustSnapshot);

    List<CfReportAddTrustSnapshot> findByAddTrustIdAndAuditStatus(@Param("addTrustId") long addTrustId,
                                                                  @Param("auditStatus") int auditStatus);
}
