package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.vo.crowdfunding.AdminWorkOrderReportVo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.enums.crowdfunding.BankCardVerifyStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.common.web.util.admin.BasicExample;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 *
 * Created by lixuan on 2017/04/14.
 *
 */
@DataSource(DS.CF)
public interface AdminCrowdfundingInfoDao {

	int updateFrom(CrowdfundingInfo crowdfundingInfo);

	int updateTitleAndContent(CrowdfundingInfo crowdfundingInfo);

	int updateRelationType(CrowdfundingInfo crowdfundingInfo);

	int updatePayeeInfo(CrowdfundingInfo crowdfundingInfo);

	int adjustAmountFromOrders(@Param("id") int id,
	                           @Param("amount") int amount);

	int doApprove(@Param("crowdfundingId") int crowdfundingId,
	              @Param("status") CrowdfundingStatus status,
	              @Param("beginTime") Date beginTime,
	              @Param("endTime") Date endTime);

	List<CrowdfundingInfo> getApproveList(@Param("size") int size,
	                                      @Param("offset") int offset,
	                                      @Param("status") CrowdfundingStatus status,
	                                      @Param("applicantName") String applicantName,
	                                      @Param("title") String title);

	List<CrowdfundingInfo> selectByExample(BasicExample basicExample);

	int addAmount(@Param("id") int id, @Param("amount") int amount);

	int subtractAmount(@Param("id") int id, @Param("amount") int amount);

	int updateEndTime(@Param("id") int id, @Param("endTime") Date endTime);

	int updateDataStatus(@Param("id") int id, @Param("dataStatus") int dataStatus);

	int updateType(@Param("id") int id, @Param("type") int type);

	int updateStatus(@Param("id") int id, @Param("newStatus") int newStatus, @Param("oldStatus") int oldStatus);

	int updateVerifyStatus(@Param("id") int id,
	                       @Param("verifyStatus") BankCardVerifyStatus verifyStatus,
	                       @Param("bankCardVerifyMessage") String bankCardVerifyMessage,
	                       @Param("bankCardVerifyMessage2") String bankCardVerifyMessage2);

	/**
	 * 获取指定时间周期内，同一个用户提交的次数
	 *
	 * @param userId
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	int getApplyCount(@Param("userId") long userId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

	List<CrowdfundingInfo> getTrueMobile();

	int updateMoblie(CrowdfundingInfo crowdfundingInfo);

    int updateTargetAmount(@Param("id") int id, @Param("targetAmount") int targetAmount);

    int editType(@Param("type") int type,@Param("dataStatus") int dataStatus, @Param("id") int id);

    int updateContent(@Param("title") String title, @Param("content") String content, @Param("encryptContent") String encryptContent, @Param("id") int id);

    @DataSource(DS.CF_SLAVE)
	List<AdminCrowdfundingInfo> getInfoByUniqueCode(@Param("volunteerUniqueCode")String volunteerUniqueCode,
													@Param("startTime")String startTime,
													@Param("endTime")String endTime,
													@Param("userId")long userId);

	@DataSource(DS.CF_SLAVE)
	List<AdminCrowdfundingInfo> getInfoByInfoUuidWithUserId(@Param("infoIds")List<String> infoIds,
															@Param("userId")long userId);

	@DataSource(DS.CF_SLAVE)
	AdminCrowdfundingInfo getInfoByMobile(@Param("mobile")String mobile);

	@DataSource(DS.CF_SLAVE)
	List<CrowdfundingInfo> getListAfterId(@Param("infoId") int infoId);

	@DataSource(DS.CF_SLAVE)
	List<CrowdfundingInfoChild> getCaseCountByUserId(@Param("userIds")List<Integer> userIds);

	@DataSource(DS.CF_SLAVE_2)
	List<AdminWorkOrderReportVo> getCrowdfundingReportInfo(@Param("title")String title, @Param("caseId")Integer caseId, @Param("userId")long userId,
														   @Param("name")String name);

	@DataSource(DS.CF_SLAVE)
    List<CrowdfundingInfo> selectByUserIdWhereNotEnd(@Param("list") List<Long> list);

	@DataSource(DS.CF_SLAVE)
    List<Integer> selectByCaseIdNotEnd(@Param("list") List<Integer> caseList);

	@DataSource(DS.CF_SLAVE)
    List<CrowdfundingInfo> selectByCaseIdList(@Param("list") List<Integer> caseList);

	@DataSource(DS.CF_SLAVE_2)
	List<CrowdfundingInfo> getInfoListByMobile(@Param("mobile")String cryptoMobile);

	@DataSource(DS.CF_SLAVE_2)
	List<CrowdfundingInfo> selectByIdLimit(@Param("id") int id, @Param("type") int type, @Param("limit") int limit);

	/**
	 * @author: wanghui
	 * @time: 2018/10/22 2:24 PM
	 * @description: 通过 id 获得 按照传入的ids的顺序查询的infoId 列表
	 * @param: [ids]
	 * @return: java.util.List<java.lang.String>
	 */
	@DataSource(DS.CF)
	List<String> getInfoIdsByIdsOrderByFieldIds(@Param("ids") List<Integer> ids);

	int updateCaseUserId(@Param("caseId")int caseId, @Param("userId") long userId);

	int updateTitleImg(@Param("titleImg") String titleImg, @Param("id") int id);

    AdminCrowdfundingInfo getInfoByUniqueCodeAndCaseId(@Param("caseId") Integer caseId);

	int updateContentImageStatus(@Param("id") int id, @Param("sourceStatus") int sourceStatus, @Param("targetStatus") int targetStatus);

	int updateContentImage(@Param("id") int id, @Param("contentImage") String contentImage,
							 @Param("sourceStatus") int sourceStatus, @Param("targetStatus") int targetStatus);

	@DataSource(DS.CF_SLAVE_2)
	List<CrowdfundingInfo> selectByUserId(@Param("userId") long userId);

	@DataSource(DS.CF_SLAVE_2)
	CrowdfundingInfo getByInfoId(@Param("infoId")String infoId);

}
