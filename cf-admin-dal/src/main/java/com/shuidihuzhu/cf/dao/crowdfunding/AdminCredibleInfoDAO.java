package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfCredibleInfoDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2020/1/10 下午2:22
 * @desc
 */
@DataSource(DS.CF)
public interface AdminCredibleInfoDAO {
    int insert(CfCredibleInfoDO credibleInfoDO);

    List<CfCredibleInfoDO> queryByCaseId(@Param("caseId") int caseId);

    CfCredibleInfoDO queryBySubId(@Param("subId") long subId, @Param("type") int type);

    CfCredibleInfoDO queryById(@Param("id") long id);

    int updateAuditInfo(@Param("subId") long subId, @Param("auditStatus") int auditStatus, @Param("type") int type);

    int updateSubmitInfo(@Param("subId") long subId, @Param("auditStatus") int auditStatus, @Param("type") int type);

    int delete(@Param("subId") long subId, @Param("type") int type);

    CfCredibleInfoDO findBySubIdAndType(@Param("subId") long subId, @Param("type") int type);

    int updateAuditStatusById(@Param("id") long id, @Param("auditStatus") int auditStatus);

    CfCredibleInfoDO getLastOneByCaseId(@Param("caseId") int caseId, @Param("type") int type);

    List<CfCredibleInfoDO> getListByOperatorId(@Param("operatorId") long operatorId, @Param("type") int type);
}
