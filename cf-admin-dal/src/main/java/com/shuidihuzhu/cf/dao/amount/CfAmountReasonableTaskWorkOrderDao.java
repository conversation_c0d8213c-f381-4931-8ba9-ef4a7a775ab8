package com.shuidihuzhu.cf.dao.amount;

import com.shuidihuzhu.cf.client.adminpure.model.amount.CfAmountReasonableTask;
import com.shuidihuzhu.cf.client.adminpure.model.amount.CfAmountReasonableTaskWorkOrder;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2022/8/26 14:17
 * @Description:
 */
@DataSource("shuidiCfAdminDataSource")
public interface CfAmountReasonableTaskWorkOrderDao {
    int insert(CfAmountReasonableTaskWorkOrder cfAmountReasonableTaskWorkOrder);

    List<CfAmountReasonableTaskWorkOrder> selectByWorkOrderIdList(@Param("workOrderIdList") List<Long> workOrderIdList);

    List<CfAmountReasonableTaskWorkOrder> selectByCaseId(@Param("caseId") int caseId);

    CfAmountReasonableTaskWorkOrder selectById(@Param("id") long id);

    int updateCfAmountReasonableTaskWorkOrder(CfAmountReasonableTaskWorkOrder cfAmountReasonableTaskWorkOrder);

    CfAmountReasonableTaskWorkOrder selectByWorkOrderId(@Param("workOrderId") long workOrderId);

}
