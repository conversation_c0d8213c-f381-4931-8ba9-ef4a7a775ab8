package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReason;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by ahrievil on 2017/1/17.
 */
@DataSource(DS.CF)
public interface CfRefuseReasonDao {
	int editReason(@Param("id") int id, @Param("content") String content);

	List<CfRefuseReason> getRefuseReason();

	int editFrequency(int id);

	String findText(int id);

	int insertReason(@Param("id") int id, @Param("pid") int pid, @Param("content") String content);

	int selectMax();

	int selectPid(int id);

	int emptyFrequency();

	List<CfRefuseReason> selectAll();

	int selectWithItem(int id);

    int deleteById(int id);

    int insertOne(CfRefuseReason cfRefuseReason);
}