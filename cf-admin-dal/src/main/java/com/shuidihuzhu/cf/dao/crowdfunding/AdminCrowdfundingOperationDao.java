package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCrowdfundingOperation;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation;
import com.shuidihuzhu.cf.vo.crowdfunding.ReportCaseVo;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;

@DataSource(DS.CF)
public interface AdminCrowdfundingOperationDao {

    int add(CrowdfundingOperation crowdfundingOperation);

    int update(CrowdfundingOperation crowdfundingOperation);

    int updateOperation(@Param("infoUuid") String infoUuid, @Param("operatorId") int operatorId,
                        @Param("operation") int operation, @Param("reason") String reason, @Param("deferContactReasonType") int deferContactReasonType);

    @DataSource(DS.CF_SLAVE)
    CrowdfundingOperation getByInfoId(@Param("infoId") String infoId);

    int insertOrUpdateCommitTime(@Param("infoId") String infoId, @Param("commitTime") Timestamp commitTime);

    int addCallCount(@Param("count") int count, @Param("infoId") String infoUuid);

    int updateCallStatus(@Param("callStatus") int callStatus, @Param("infoId") String infoUuid);

    int addRefuseCount(@Param("userRefuseCount") int userRefuseCount, @Param("count") int count, @Param("infoId") String infoUuid);

    int setRefuseCount(@Param("count") int count, @Param("infoId") String infoUuid);

    @DataSource(DS.CF_SLAVE)
    AdminCrowdfundingOperation selectByInfoUuid(@Param("infoUuid") String infoUuid);

    @DataSource(DS.CF_SLAVE)
    List<AdminCrowdfundingOperation> selectByInfoUuids(@Param("infoUuids") List<String> infoUuids);

    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingOperation> getByInfoIds(@Param("infoIds") List<String> infoIds);

    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingOperation> getByTimeAndCallStatus(@Param("startTime") String startTime,
                                                       @Param("endTime") String endTime,
                                                       @Param("callStatus") int CallStatus,
                                                       @Param("offset") int offset,
                                                       @Param("limit") int limit);

    @DataSource(DS.CF_SLAVE)
    List<ReportCaseVo> getReportCaseList(@Param("caseType") String caseType,
                                         @Param("crowdfundingStatus") int crowdfundingStatus,
                                         @Param("followType") String followType,
                                         @Param("infoUuids") List<String> infoUuids,
                                         @Param("sortType") int sortType,
                                         @Param("addTrustStatus") String addTrustStatus,
                                         @Param("hospitalAuditStatus") String hospitalAuditStatus,
                                         @Param("onWorkOrder") int onWorkOrder,
                                         @Param("hospitalSendBeginTime") Long hospitalSendBeginTime,
                                         @Param("hospitalSendEndTime") Long hospitalSendEndTime,
                                         @Param("updateBeginTime") Long updateBeginTime,
                                         @Param("updateEndTime") Long updateEndTime);

    //退款和审核状态的查询
    @DataSource(DS.CF_SLAVE)
    List<ReportCaseVo> getListByWhereAndPage(@Param("caseType") String caseType,
                                             @Param("approveStatus") Integer approveStatus,
                                             @Param("followType") String followType,
                                             @Param("sortType") int sortType,
                                             @Param("infoUuids") List<String> infoUuids,
                                             @Param("cfRefundStatus") Integer cfRefundStatus,
                                             @Param("addTrustStatus") String addTrustStatus,
                                             @Param("hospitalAuditStatus") String hospitalAuditStatus,
                                             @Param("onWorkOrder") int onWorkOrder,
                                             @Param("hospitalSendBeginTime") Long hospitalSendBeginTime,
                                             @Param("hospitalSendEndTime") Long hospitalSendEndTime,
                                             @Param("updateBeginTime") Long updateBeginTime,
                                             @Param("updateEndTime") Long updateEndTime);

    @DataSource(DS.CF_SLAVE)
    List<ReportCaseVo> getListByWhereDrawCashAndPage(@Param("caseType") String caseType,
                                                     @Param("approveStatus") Integer approveStatus,
                                                     @Param("followType") String followType,
                                                     @Param("sortType") int sortType,
                                                     @Param("infoUuids") List<String> infoUuids,
                                                     @Param("cfCashStatus") Integer cfCashStatus,
                                                     @Param("DrawStatus") Integer DrawStatus,
                                                     @Param("addTrustStatus") String addTrustStatus,
                                                     @Param("hospitalAuditStatus") String hospitalAuditStatus,
                                                     @Param("onWorkOrder") int onWorkOrder,
                                                     @Param("hospitalSendBeginTime") Long hospitalSendBeginTime,
                                                     @Param("hospitalSendEndTime") Long hospitalSendEndTime,
                                                     @Param("updateBeginTime") Long updateBeginTime,
                                                     @Param("updateEndTime") Long updateEndTime);

    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingOperation> selectByInfoIdList(@Param("list") List<String> list);

    CrowdfundingOperation getByInfoIdMaster(@Param("infoId") String infoId);

    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingOperation> getCrowdfundingOperation(@Param("caseIds") List<Integer> caseIds);

    int updateCreditStatus(@Param("caseId") int caseId, @Param("creditStatus") int creditStatus);

    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingOperation> findByReportStatus(@Param("reportStatusList")List<Integer> reportStatusList);


    int updateFuwuType(@Param("id") int id,@Param("type") int type);

    @DataSource(DS.CF_SLAVE)
    CrowdfundingOperation getByCaseId(@Param("caseId") int caseId);
}
