package com.shuidihuzhu.cf.dao.stat;

import com.shuidihuzhu.cf.model.message.CfWxUserCumulateData;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

@DataSource("statCfSlaveDataSource")
public interface CfWxUserCumulateDataDao {

    CfWxUserCumulateData queryByRefDateAndThirdType(@Param("thirdType") int thirdType, @Param("refDate") String refDate);
}
