package com.shuidihuzhu.cf.dao.risk;

import com.shuidihuzhu.cf.model.risk.BlackListHighRiskWorkOrderRecord;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * @Author: wangpeng
 * @Date: 2022/4/21 17:06
 * @Description:
 */
@DataSource("shuidiCfAdminDataSource")
public interface BlackListHighRiskWorkOrderRecordDao {
    int insert(BlackListHighRiskWorkOrderRecord riskWorkOrderRecord);

    BlackListHighRiskWorkOrderRecord getByWorkOrderId(@Param("workOrderId") long workOrderId);
}
