package com.shuidihuzhu.cf.dao.crowdfunding;

/**
 * Author: <PERSON>
 * Date: 2017/6/5 13:30
 */

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CfDailyCharityCfInfo;
import com.shuidihuzhu.common.web.util.admin.BasicExample;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@DataSource(DS.CF)
public interface AdminCfDailyCharityCfInfoDao {
	int deleteByPrimaryKey(Integer id);

	int insert(CfDailyCharityCfInfo record);

	CfDailyCharityCfInfo selectByPrimaryKey(Integer id);

	List<CfDailyCharityCfInfo> listByDt(@Param("dt") Date dt, @Param("limit") Integer limit);

	int updateByPrimaryKey(CfDailyCharityCfInfo record);

	List<CfDailyCharityCfInfo> selectByPage(BasicExample basicExample);

}

