package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by Ahrievil on 2017/9/3
 */
@DataSource(AdminDS.CF_RW)
public interface AdminCfInfoStatDao {

    List<Integer> selectFakeShare(@Param("modulus") int modulus, @Param("shareCount") int shareCount);
}
