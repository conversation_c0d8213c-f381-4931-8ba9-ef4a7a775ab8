package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoPayee;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by Ahrievil on 2017/9/25
 */
@DataSource(AdminDS.CF_RW)
public interface AdminCrowdfundingInfoPayeeDao {

    List<CrowdfundingInfoPayee> selectByPayeeName(@Param("name") String name);

    List<CrowdfundingInfoPayee> selectByInfoUuidList(@Param("list") List<String> list);

    List<CrowdfundingInfoPayee> selectByPayeeIdCard(@Param("idCard") String idCard);
}
