package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfReportProblemLabel;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2019-12-11 14:43
 **/
@DataSource(DS.CF)
public interface CfReportProblemLabelDao {

    int insert(CfReportProblemLabel reportProblemLabel);

    @DataSource(DS.CF_SLAVE)
    CfReportProblemLabel findByDesc(@Param("labelDesc") String labelDesc);

    @DataSource(DS.CF_SLAVE)
    List<CfReportProblemLabel> findByIds(@Param("ids") List<Integer> ids, @Param("isUse") Integer isUse);

    @DataSource(DS.CF_SLAVE)
    List<CfReportProblemLabel> listByLevelAndParentId(@Param("level") Integer level,
                                                      @Param("parentId") Integer parentId,
                                                      @Param("isUse") Integer isUse);

    int updateLabel(@Param("labelDesc") String labelDesc,@Param("sort")int sort ,@Param("id")int id);


    int addProblemClassify(CfReportProblemLabel reportProblemLabel);


    int updateIsUse(@Param("isUse")int isUse, @Param("id")int id);

    @DataSource(DS.CF_SLAVE)
    List<CfReportProblemLabel> getByParentIdAndIsUse(@Param("parentId")int parentId, @Param("isUse")int isUse);


    int updateProblemModule(@Param("parentId")int parentId, @Param("labelDesc")String labelDesc,
                             @Param("sort")int sort,@Param("isMandatory") int isMandatory, @Param("id") int id);

    CfReportProblemLabel getById(@Param("id")int id);

    CfReportProblemLabel getByPartentIdAndLabelDesc(@Param("parentId")int parentId, @Param("labelDesc")String labelDesc);

}
