package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.ReportUpDownGradeRecord;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @time 2019/8/15 上午11:43
 * @desc
 */
@DataSource(DS.CF)
public interface IReportUpDownGradeDAO {
    ReportUpDownGradeRecord queryByCaseId(@Param("caseId") int caseId);
    int insert(ReportUpDownGradeRecord record);
}
