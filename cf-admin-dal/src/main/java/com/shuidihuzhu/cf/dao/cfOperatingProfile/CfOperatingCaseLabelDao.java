package com.shuidihuzhu.cf.dao.cfOperatingProfile;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingCaseLabel;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(AdminDS.CF_RW)
public interface CfOperatingCaseLabelDao {

    int addCaseLabels(@Param("list") List<CfOperatingCaseLabel> list);

    @DataSource(DS.CF)
    List<CfOperatingCaseLabel> selectOperatingCaseLabels(@Param("caseId") int caseId);

    int deleteCaseLabelByIds(@Param("ids") List<Long> ids);
}
