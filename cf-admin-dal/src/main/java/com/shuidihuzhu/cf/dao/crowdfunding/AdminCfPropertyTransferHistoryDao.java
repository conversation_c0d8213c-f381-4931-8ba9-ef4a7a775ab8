package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by Ahrievil on 2017/12/12
 */
@DataSource(DS.CF)
public interface AdminCfPropertyTransferHistoryDao {

    List<Long> selectCfOrderIdsByBizType(@Param("fromUserId") long fromUserId, @Param("bizType") int bizType);
}
