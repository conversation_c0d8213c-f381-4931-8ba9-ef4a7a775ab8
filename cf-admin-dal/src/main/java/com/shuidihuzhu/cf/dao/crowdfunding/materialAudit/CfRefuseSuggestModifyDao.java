package com.shuidihuzhu.cf.dao.crowdfunding.materialAudit;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.crowdfunding.material.CfRefuseSuggestModify;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(AdminDS.CF_RW)
public interface CfRefuseSuggestModifyDao {


    List<CfRefuseSuggestModify> selectSuggestByDateTypes(@Param("types") List<Integer> types);


    List<CfRefuseSuggestModify> selectSuggestByUniqueIds(@Param("uniqueIds") List<Integer> uniqueIds);

}
