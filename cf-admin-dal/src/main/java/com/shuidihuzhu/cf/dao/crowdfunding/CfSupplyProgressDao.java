package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyProgress;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-01-09 21:16
 **/
@DataSource(DS.CF)
public interface CfSupplyProgressDao {

    int add(CfInfoSupplyProgress supplyProgress);
    /**
     * 驳回
     */
    int reject(CfInfoSupplyProgress supplyProgress);

    /**
     * 审核通过
     */
    int pass(@Param("id") long id, @Param("imgUrls") String imgUrls, @Param("progressId") int progressId);

    int reprocess(@Param("id") long id);

    List<CfInfoSupplyProgress> listBySupplyActionId(@Param("actionId") long actionId);

    List<CfInfoSupplyProgress> listByIds(@Param("ids") List<Long> ids);

    List<CfInfoSupplyProgress> listByActionIds(@Param("actionIds") List<Long> actionIds);

    List<CfInfoSupplyProgress> getByActionId(@Param("progressActionId") long progressActionId);
}
