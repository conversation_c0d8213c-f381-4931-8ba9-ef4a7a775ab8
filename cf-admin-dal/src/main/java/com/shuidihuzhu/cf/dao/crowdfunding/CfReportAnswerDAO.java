package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.AdminReportProblemAnswerDetail;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/12/16 上午11:26
 * @desc
 */
@DataSource(DS.CF)
public interface CfReportAnswerDAO {

    int insert(AdminReportProblemAnswerDetail answerDetail);

    List<AdminReportProblemAnswerDetail> query(@Param("caseId")int caseId, @Param("reportId")int reportId, @Param("type")int type);

    List<AdminReportProblemAnswerDetail> queryByType(@Param("caseId")int caseId, @Param("type")int type);

    List<AdminReportProblemAnswerDetail> queryByIds(@Param("ids") List<Long> ids);

    AdminReportProblemAnswerDetail queryById(@Param("id") Long id);

    @DataSource(DS.CF_SLAVE)
    List<AdminReportProblemAnswerDetail> queryByCaseId(@Param("caseId")int caseId);

    @DataSource(DS.CF_SLAVE)
    AdminReportProblemAnswerDetail queryLastByCaseId(@Param("caseId")int caseId, @Param("type")int type);

    @DataSource(DS.CF_SLAVE)
    List<AdminReportProblemAnswerDetail> queryByCaseIdAndType(@Param("caseId")int caseId, @Param("type")int type);

}
