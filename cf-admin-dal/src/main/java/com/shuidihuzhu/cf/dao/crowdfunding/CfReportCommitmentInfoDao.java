package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportCommitmentInfo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

@DataSource(AdminDS.CF_RW)
public interface CfReportCommitmentInfoDao {

    int insertOne(CfReportCommitmentInfo cfReportCommitmentInfo);


    CfReportCommitmentInfo findByIncrTrustId(@Param("incrTrustId") long incrTrustId);

}
