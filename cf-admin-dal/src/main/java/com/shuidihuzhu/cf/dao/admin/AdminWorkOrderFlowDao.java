package com.shuidihuzhu.cf.dao.admin;

import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderFlow;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowParam;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowStatistics;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * Created by Ahrievil on 2017/12/21
 */
@DataSource(DS.CF)
public interface AdminWorkOrderFlowDao {

    AdminWorkOrderFlow selectByWorkOrderId(@Param("workOrderId") long workOrderId);

    @DataSource(DS.CF_SLAVE)
    List<AdminWorkOrderFlow> selectByWorkOrderIdList(@Param("list") List<Long> list);

    int insertOne(AdminWorkOrderFlow adminWorkOrderFlow);

    int updateTaskType(@Param("workOrderId") long workOrderId, @Param("taskType") int taskType);

    int updateHandleImg(@Param("workOrderId") long workOrderId, @Param("handleImg") String handleImg);

    int updateProblemContent(@Param("workOrderId") long workOrderId, @Param("problemContent") String problemContent);


    int updateSecondId(@Param("workOrderId") long workOrderId, @Param("secondClassifyId")
                               long secondClassifyId);

    int updateClassifyId(@Param("workOrderId") long workOrderId, @Param("newFirstClassifyId")
            int newFirstClassifyId, @Param("newSecondClassifyId")
                                 int newSecondClassifyId, @Param("newThirdClassifyId")
                                 int newThirdClassifyId);

    @DataSource(DS.CF_SLAVE)
    List<AdminWorkOrderFlowView> selectUnHandleTask(@Param("problemTypes") List<Integer> problemTypes, @Param("size") int size,
                                                    @Param("createTime") Date createTime);

    @DataSource(DS.CF_SLAVE)
    List<AdminWorkOrderFlowView> selectWorkFlowByParam(AdminWorkOrderFlowParam.SearchParam searchParam);

    //创建工单时查看caseId或者mobile未完成的工单
    @DataSource(DS.CF_SLAVE)
    List<AdminWorkOrderFlowView> selectWorkFlowByCaseIdOrMobile(@Param("encryptMobile") String mobile, @Param("caseId") Integer caseId,
                                                                @Param("workOrderStatusList") List<Integer> workOrderStatusList);

    @DataSource(DS.CF_SLAVE)
    AdminWorkOrderFlowView selectOrderFlowViewById(@Param("id") long id);

    @DataSource(DS.CF_SLAVE)
    List<AdminWorkOrderFlowView> selectByCreateTimeAndCreateIds(AdminWorkOrderFlowStatistics.searchParam param);

    @DataSource(DS.CF_SLAVE)
    List<AdminWorkOrderFlowView> selectByOrderStatusAndOperators(AdminWorkOrderFlowStatistics.searchParam param);

    @DataSource(DS.CF_SLAVE)
    List<AdminWorkOrderFlowView> selectByCaseIdAndSecondId(@Param("caseId") int caseId,
                                                                 @Param("secondClassifyId") int secondClassifyId);

    @DataSource(DS.CF_SLAVE)
    List<AdminWorkOrderFlowView> selectByCaseIdAndClassifyId(@Param("caseId") int caseId,
                                                           @Param("newFirstClassifyId") int newFirstClassifyId,
                                                           @Param("newSecondClassifyId") int newSecondClassifyId,
                                                           @Param("newThirdClassifyId") int newThirdClassifyId);

    //处理记录中查找相似工单
    @DataSource(DS.CF_SLAVE)
    List<AdminWorkOrderFlowView> selectByMobileAndSecondId(@Param("encryptMobile") String encryptMobile,
                                                           @Param("secondClassifyId") int secondClassifyId);


    //处理记录中查找相似工单 -new
    @DataSource(DS.CF_SLAVE)
    List<AdminWorkOrderFlowView> selectByMobileAndClassifyId(@Param("encryptMobile") String encryptMobile,
                                                           @Param("newFirstClassifyId") int newFirstClassifyId,
                                                             @Param("newSecondClassifyId") int newSecondClassifyId,
                                                             @Param("newThirdClassifyId") int newThirdClassifyId);
    // 已办工单 end

    // 待办工单 begin
    @DataSource(DS.CF_SLAVE)
    List<Integer> selectWaitForHandleFlowIdsByParam(AdminWorkOrderFlowParam.SearchParam searchParam);

    @DataSource(DS.CF_SLAVE)
    List<AdminWorkOrderFlowView> selectWorkFlowByFlowIds(@Param("flowIds") List<Integer> flowIds);
    // 待办工单 end

    // 我的工单 begin
    @DataSource(DS.CF_SLAVE)
    int countCreateFlowByParam(AdminWorkOrderFlowParam.SearchParam searchParam);

    @DataSource(DS.CF_SLAVE)
    List<AdminWorkOrderFlowView> selectCreateFlowByParam(AdminWorkOrderFlowParam.SearchParam searchParam);
    // 我的工单 end

    AdminWorkOrderFlow selectOrderFlowById(@Param("id") long id);

    List<AdminWorkOrderFlowView> selectByMobileAndTaskType(@Param("encryptMobile") String encryptMobile,
                                               @Param("taskType") int taskType);

    AdminWorkOrderFlowView selectByCaseIdAndTaskType(@Param("caseId") int caseId,
                                                     @Param("taskType") int taskType);

    int updateFlowFollowTags(@Param("workOrderId") long workOrderId, @Param("followTags") String followTags);


    int updateProvinceAndCity(@Param("workOrderId") long workOrderId,
                              @Param("cityId") int cityId,
                              @Param("provinceId") int  provinceId,
                              @Param("cityName") String cityName,
                              @Param("provinceName") String provinceName,
                              @Param("countyId") int  countyId,
                              @Param("countyName") String countyName,
                              @Param("hospital") String hospital,
                              @Param("hospitalId") int  hospitalId);

    @DataSource(DS.CF_SLAVE)
    List<AdminWorkOrderFlowView> selectByOrderStatusAndOperatorsNew(@Param("createTime") Date createTime, @Param("operatorIds") List<Integer> operatorIds
            , @Param("orderStatusSet") List<Integer> orderStatusSet, @Param("problemTypeCode") int problemTypeCode);

}
