package com.shuidihuzhu.cf.dao.cfOperatingProfile;


import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileLog;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(AdminDS.CF_RW)
public interface CfOperatingProfileLogDao {

    int insertOperateRecord(@Param("list") List<CfOperatingProfileLog> logs);


    List<CfOperatingProfileLog> selectLogByLogType(@Param("businessId") long businessId);

}
