package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CfBlacklistWord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 */
@DataSource(DS.CF)
public interface CfBlacklistWordDao {

    int add(CfBlacklistWord cfBlacklistWord);

    int addList(@Param("list") List<CfBlacklistWord> list);

    List<String> selectAllWordsLimit(@Param("type") int type, @Param("start") int start, @Param("size") int size);
}
