package com.shuidihuzhu.cf.dao.cfOperatingProfile;

import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileSettings;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface CfOperatingProfileSettingsExtDao {

    int insertExtList(@Param("list") List<CfOperatingProfileSettings.ProfileSettingsExt> list);

    List<CfOperatingProfileSettings.ProfileSettingsExt> selectByProfileIdsAndNames(@Param("ids") List<Long> profileIds,
                                                                                   @Param("names")  List<String> names);

    int deleteExtByProfileIdsAndNames(@Param("ids") List<Long> profileIds,
                                      @Param("names")  List<String> names);

    List<CfOperatingProfileSettings.ProfileSettingsExt> selectByNameAndValues(@Param("name") String name,
                                                                             @Param("values") List<String> values);

}
