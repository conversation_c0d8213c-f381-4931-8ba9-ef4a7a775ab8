package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.AdminCfFundUseAuditInfo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;
import java.util.Set;

@DataSource(DS.CF_SLAVE_2)
public interface AdminCfFundUseAuditDao {

    @DataSource(DS.CF)
    int updateAuditStatusRejected(@Param("progressId") Integer progressId,
                                  @Param("fundAuditRejectedReason") String fundAuditRejectedReason);

    @DataSource(DS.CF)
    int updateDrawTime(@Param("progressId") Integer progressId,
                       @Param("drawTime") Timestamp drawTime);

    @DataSource(DS.CF)
    int updateAuditPass(@Param("progressId") Integer progressId);

    @DataSource(DS.CF_SLAVE_2)
    AdminCfFundUseAuditInfo selectByProgressId(@Param("progressId") Integer progressId);

    @DataSource(DS.CF_SLAVE_2)
    List<AdminCfFundUseAuditInfo> getFundUseAuditInfoByUnionSelect(@Param("crowdfundingIds") List<Integer> crowdfundingIds,
                                                                   @Param("progressStatus") Integer progressStatus,
                                                                   @Param("drawCashStartTime") String drawCashStartTime,
                                                                   @Param("drawCashEndTime") String drawCashEndTime,
                                                                   @Param("fundUseSubmitStartTime") String fundUseSubmitStartTime,
                                                                   @Param("fundUseSubmitEndTime") String fundUseSubmitEndTime,
                                                                   @Param("current") Integer current,
                                                                   @Param("pageSize") Integer pageSize,
                                                                   @Param("hasReport")String hasReport);

    @DataSource(DS.CF_SLAVE_2)
    Integer getRecordsByMultiField(@Param("crowdfundingIds") List<Integer> crowdfundingIds,
                                   @Param("progressStatus") Integer progressStatus,
                                   @Param("drawCashStartTime") String drawCashStartTime,
                                   @Param("drawCashEndTime") String drawCashEndTime,
                                   @Param("fundUseSubmitStartTime") String fundUseSubmitStartTime,
                                   @Param("fundUseSubmitEndTime") String fundUseSubmitEndTime,
                                   @Param("hasReport")String hasReport);
}
