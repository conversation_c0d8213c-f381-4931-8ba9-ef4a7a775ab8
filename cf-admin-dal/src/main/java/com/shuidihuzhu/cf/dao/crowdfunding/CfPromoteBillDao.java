package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.admin.vo.PromoteBillHandleParam;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@DataSource(DS.CF)
public interface CfPromoteBillDao {

    int addResult(PromoteBillHandleParam handleParam);

    @DataSource(DS.CF_SLAVE)
    PromoteBillHandleParam selectLastByWorkOrderId(@Param("workOrderId") long workOrderId);
}
