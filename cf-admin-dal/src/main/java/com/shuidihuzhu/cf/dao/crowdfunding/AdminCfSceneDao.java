package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CfScene;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by wangsf on 17/4/24.
 */
@DataSource(DS.CF)
public interface AdminCfSceneDao {

	List<CfScene> listScenes();

	CfScene queryById(@Param("id") int id);

}
