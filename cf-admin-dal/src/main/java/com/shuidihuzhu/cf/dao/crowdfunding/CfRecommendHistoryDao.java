package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfRecommendHistory;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

import java.util.List;

@DataSource(DS.CF)
public interface CfRecommendHistoryDao {
    /**
     * 批量写入筹首页推荐列表，如果unique 冲突则更新recommendId
     * @param records 待插入数据
     * @return 操作结果
     */
    int insertOnDuplicateUpdateBatch(List<CfRecommendHistory> records);

    int insertSelective(CfRecommendHistory record);

    CfRecommendHistory selectByPrimaryKey(Long id);
}