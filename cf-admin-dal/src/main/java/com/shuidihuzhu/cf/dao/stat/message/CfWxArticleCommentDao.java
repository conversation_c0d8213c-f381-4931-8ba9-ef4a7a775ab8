package com.shuidihuzhu.cf.dao.stat.message;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

@DataSource("statCfSlaveDataSource")
public interface CfWxArticleCommentDao {
    Integer count(@Param("thirdType") int thirdType, @Param("msgDataId") String msgDataId,
              @Param("index") int index);

    Integer maxUserCommentId(@Param("thirdType") int thirdType, @Param("msgDataId") String msgDataId,
                  @Param("index") int index);
}
