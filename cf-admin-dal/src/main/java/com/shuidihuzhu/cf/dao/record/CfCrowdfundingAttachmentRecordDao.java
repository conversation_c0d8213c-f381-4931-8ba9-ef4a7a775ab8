package com.shuidihuzhu.cf.dao.record;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.record.CfCrowdfundingAttachmentRecordDo;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/1  3:51 下午
 */
@DataSource(AdminDS.CF_ADMIN_RW)
public interface CfCrowdfundingAttachmentRecordDao {

    int insertBatch(@Param("list") List<CfCrowdfundingAttachmentRecordDo> list);

    List<CfCrowdfundingAttachmentRecordDo> getListByCaseId(@Param("caseId") int caseId,@Param("type") int type);

}
