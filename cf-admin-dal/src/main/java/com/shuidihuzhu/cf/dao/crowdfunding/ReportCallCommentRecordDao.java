package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.report.ReportCallCommentRecord;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(AdminDS.CF_RW)
public interface ReportCallCommentRecordDao {

    int insertOne(ReportCallCommentRecord reportCallCommentRecord);


    List<ReportCallCommentRecord> getReportCallCommentRecord(@Param("caseId") int caseId,
                                                             @Param("reportId") int reportId,
                                                             @Param("type") int type);
}
