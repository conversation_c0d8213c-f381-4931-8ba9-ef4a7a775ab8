package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CfRepeatUserIdRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by ahrievil on 2017/6/21.
 */
@DataSource(DS.CF)
public interface CfRepeatUserIdRecordDao {
    int insertList(@Param("list") List<CfRepeatUserIdRecord> list);
    List<Integer> selectUserId();
    List<CfRepeatUserIdRecord> selectByRemainCounts();
    int updateRemainCount(@Param("userId") long userId, @Param("remainCounts") int remainCounts);
    List<CfRepeatUserIdRecord> selectAll(@Param("start") int start, @Param("limit") int limit);
}
