package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification;
import com.shuidihuzhu.common.web.util.admin.BasicExample;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/10/18.
 */
@DataSource(DS.CF)
public interface AdminCrowdFundingVerificationSlaveDao {

	CrowdFundingVerification getById(@Param("id") long id);

	List<CrowdFundingVerification> selectByPage(BasicExample basicExample);

	/**
	 * 根据verifyUserId createTime查询所有的证实评论，限制limit
	 */
	@DataSource(DS.CF_SLAVE)
    List<Long> listIdByVerifyUserIdAndCreateTimeWithLimit(@Param("verifyUserId") Long verifyUserId, @Param("createTime") Date createTime, @Param("limit") int limit);

	@DataSource(DS.CF_SLAVE)
	List<CrowdFundingVerification> getListByIds(@Param("ids") List<Integer> ids);
}
