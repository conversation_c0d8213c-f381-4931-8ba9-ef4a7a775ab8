package com.shuidihuzhu.cf.dao.questionnaire;

import com.shuidihuzhu.cf.model.questionnaire.WxQuestion;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: mhk
 * @Date: 2018/11/8  17:10
 */
@DataSource(DS.CF)
public interface WxQuestionAdminDao {

    int save(@Param("item") WxQuestion wxQuestion);

    List<WxQuestion> getByQnrId(@Param("qnrId")int qnrId);

    int delByQnrId(@Param("qnrId")int qnrId);

}
