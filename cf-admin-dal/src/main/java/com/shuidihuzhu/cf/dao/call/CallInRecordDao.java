package com.shuidihuzhu.cf.dao.call;

import com.shuidihuzhu.cf.call.CallInModel;
import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@DataSource(AdminDS.CF_RW)
public interface CallInRecordDao {
    List<CallInModel> getRecordsByDayTimeAndCustomerPhoneNum(@Param("startDay") String startDay, @Param("endDay") String endDay, @Param("encryptoCustomerPhoneNumber") String encryptoCustomerPhoneNumber);

    List<CallInModel> getAllByPhoneNum(@Param("customerPhoneNum") String customerPhoneNum);

    CallInModel getCallInRecordById(@Param("callRecordId") int callRecordId);


    int insertCallInModel(CallInModel callInModel);

}
