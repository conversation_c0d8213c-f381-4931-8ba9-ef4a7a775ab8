package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface CfRefuseReasonUseSceneRankMappingDao {

    int addUseSceneRankMappingList(@Param("list") List<CfRefuseReasonEntity.CfRefuseReasonUseSceneRankMapping> list);

    List<CfRefuseReasonEntity.CfRefuseReasonUseSceneRankMapping> selectByEntityId(@Param("entityId") int entityId);

    int updateDeleteOrRandByIds(@Param("ids") List<Integer> ids, @Param("isDelete") Integer isDelete,
                                @Param("rank") Integer rank );

    List<CfRefuseReasonEntity.CfRefuseReasonUseSceneRankMapping> selectByEntityIdsAndUseScene(@Param("entityIds") List<Integer> entityIds, @Param("useScene") Integer useScene);
}
