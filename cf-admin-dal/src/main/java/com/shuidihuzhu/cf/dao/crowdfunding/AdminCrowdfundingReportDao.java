package com.shuidihuzhu.cf.dao.crowdfunding;


import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCrowdfundingReportChild;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReportLabel;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by lixuan on 2017/04/14.
 */
@DataSource(AdminDS.CF_RW)
public interface AdminCrowdfundingReportDao {

	int add(CrowdfundingReport crowdfundingReport);

	int addRealNameReport(CrowdfundingReport crowdfundingReport);


	List<CrowdfundingReport> getReports(@Param("start") int start, @Param("size") int size, @Param("timer") int timer);


	CrowdfundingReport query(@Param("caseId") int caseId, @Param("reportId") int reportId);


	CrowdfundingReport getByInfoId(@Param("activityId") Integer activityId);

	@DataSource(DS.CF)
	List<CrowdfundingReport> getListByInfoId(@Param("activityId") Integer activityId);


	List<CrowdfundingReport> getListByInfoIds(@Param("activityIds") List<Integer> activityIds);


	List<CrowdfundingReport> getListByInfoIdsV2(@Param("activityIds") List<Integer> activityIds);

	//无案例状态的操作

	List<CrowdfundingReport> getListByPage(
			@Param("reportDealStatus") Integer reportDealStatus,
			@Param("activityIds") List<Integer> activityIds, @Param("caseType") String caseType);

    //审核和退款状态的案例

	List<CrowdfundingReport> getListByWhereAndPage(@Param("reportDealStatus") Integer reportDealStatus, @Param("activityIds") List<Integer> activityIds,
												   @Param("approveStatus") Integer approveStatus,
												   @Param("cfRefundStatus") Integer cfRefundStatus,
												   @Param("caseType") String caseType);

	//提现状态的案例

	List<CrowdfundingReport> getListByWhereDrawCashAndPage(@Param("reportDealStatus") Integer reportDealStatus, @Param("activityIds") List<Integer> activityIds,
														   @Param("approveStatus") Integer approveStatus, @Param("cfCashStatus") Integer cfCashStatus,
														   @Param("DrawStatus") Integer DrawStatus,
														   @Param("caseType") String caseType);

	int updateDealStatus(@Param("dealStatus") int dealStatus, @Param("id") int id, @Param("operatorId") int operatorId);

	int updateHandleAndConnectStatus(@Param("id") int id, @Param("handleStatus") int handleStatus,
									 @Param("connectStatus") int connectStatus, @Param("newOperatorId") long newOperatorId,
									 @Param("dealStatus") int dealStatus);


    List<Map<String, Integer>> selectCaseCount(@Param("set") Set<Integer> set, @Param("countList") List<Integer> countList);


    List<CrowdfundingReport> selectByActivityIds(@Param("list") List<Integer> list);

	//批量更新
	int updateReportStatusList(@Param("reportIds") List<Integer> reportIds);

	//批量更新
	int updateReportIsNewStatusList(@Param("reportIds") List<Integer> reportIds);


	List<CrowdfundingReport> getFirstListByCreateTimeAndInfoid(@Param("infoids") List<Integer> infoids);


	List<CrowdfundingReport> getLastListByCreateTimeAndInfoid(@Param("infoids") List<Integer> infoids);


	List<CrowdfundingReport> getIsHaveNewReport(@Param("infoids") List<Integer> infoids);


	List<CrowdfundingReport> getListByReportIds(@Param("reportIds") List<Integer> reportIds);

	//@DataSource(DS.CF_SLAVE)
	@DataSource(DS.CF)
	List<CrowdfundingReport> getListByInfoIdAndPage(@Param("infoId") int infoId);

	//批量更新举报的处理状态
	int updateReportListDealStatus(@Param("reportIds") List<Integer> reportIds, @Param("dealStatus") int dealStatus, @Param("operatorId") int operatorId);

	int updateReportListOperator(@Param("reportIds") List<Integer> reportIds, @Param("targetUserId") int targetUserId);

	//批量更新案例的跟进状态
	int updateReportListFollowStatus(@Param("reportIds") List<Integer> reportIds, @Param("followStatus") int followStatus);


    List<AdminCrowdfundingReportChild> getCaseReportCount(@Param("caseIds") List<Integer> caseIds);


	List<CrowdfundingReport> getFirstCreateTimeByInfoIds(@Param("infoids") List<Integer> infoids);


	List<CrowdfundingReport> getLastCreateTimeByInfoIds(@Param("infoids") List<Integer> infoids);


	List<CrowdfundingReport> getReportByTimes(@Param("start") Date start, @Param("end") Date end);


	List<CrowdfundingReportLabel> getReportLabels(@Param("list") List<Integer> reportIds);

	int deleteReportLabels(@Param("reportId") int reportId);

	int addReportLabelsForAdmin(@Param("list") List<CrowdfundingReportLabel> newReportLables);

	int addLabel(@Param("list")List<CrowdfundingReportLabel> list);


	List<CrowdfundingReport> selectReportByCaseIdAndCreateTime(@Param("caseId") int caseId, @Param("createTime") Date createTime,
															   @Param("dealStatus") int dealStatus);



	List<CrowdfundingReport> listByUserId(@Param("userId") long userId);


	int countByUserId(@Param("userId") long userId);

	int updateRiskLabel(@Param("riskLabel") String riskLabel, @Param("reportId") int reportId);


    List<CrowdfundingReport> getListByInfoIdAndName(@Param("caseId") int caseId,
													@Param("userName") String userName);


	int countByInfoId(@Param("caseId") int caseId);

	int deleteReportLabelsByIdAndType(@Param("reportId") int reportId);

	int addModifyLabel(@Param("list")List<CrowdfundingReportLabel> list);

	List<CrowdfundingReportLabel> getReportLabelsModify(@Param("list") List<Integer> reportIds);

	CrowdfundingReport getById (@Param("id") int id);

	List<CrowdfundingReportLabel> queryReportLabelByReportId(@Param("reportId") int reportId);

	int updateEncryptMobileById (@Param("id") int id, @Param("encryptMobile") String encryptMobile);
}
