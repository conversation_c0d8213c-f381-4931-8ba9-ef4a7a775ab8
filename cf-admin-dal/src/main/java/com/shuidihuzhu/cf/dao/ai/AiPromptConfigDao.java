package com.shuidihuzhu.cf.dao.ai;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.ai.AiGenerateRecord;
import com.shuidihuzhu.cf.model.ai.AiPromptConfig;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2024/11/14 4:11 PM
 */
@DataSource(AdminDS.CF_ADMIN_RW)
public interface AiPromptConfigDao {

    int insert(AiPromptConfig aiPromptConfig);

    int updatePrompt(AiPromptConfig aiPromptConfig);

    AiPromptConfig selectByGenerateType(@Param("generateType") Integer generateType, @Param("modelType") Integer modelType, @Param("bizType") Integer bizType);

}
