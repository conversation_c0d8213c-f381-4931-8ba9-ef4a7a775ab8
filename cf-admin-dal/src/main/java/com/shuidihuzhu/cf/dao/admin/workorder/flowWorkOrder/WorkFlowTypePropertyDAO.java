package com.shuidihuzhu.cf.dao.admin.workorder.flowWorkOrder;


import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowTypeProperty;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(AdminDS.CF_RW)
public interface WorkFlowTypePropertyDAO {

    Integer insertPropertys(@Param("propertyList") List<WorkFlowTypeProperty> typeProperty);


    List<WorkFlowTypeProperty> selectPropertyList(@Param("flowType") int flowType,
                                                  @Param("typeId") long typeId,
                                                  @Param("propertyType") int propertyType);


    List<WorkFlowTypeProperty> selectPropertyLists(@Param("flowType") int flowType,
                                                  @Param("typeIds") List<Long> typeIds,
                                                  @Param("propertyType") int propertyType);


    List<WorkFlowTypeProperty> selectPropertyListsV2(@Param("flowType") int flowType,
                                                     @Param("typeIds") List<Long> typeIds,
                                                     @Param("propertyTypes") List<Integer> propertyTypes);

    int deletePropertyList(@Param("flowType") int flowType,
                           @Param("typeId") long typeId,
                           @Param("propertyType") int propertyType);

}
