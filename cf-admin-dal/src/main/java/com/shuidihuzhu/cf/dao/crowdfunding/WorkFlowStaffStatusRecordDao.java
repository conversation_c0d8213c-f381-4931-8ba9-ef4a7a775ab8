package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowStaffStatusRecord;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-02-14 19:40
 **/
@DataSource(DS.CF)
public interface WorkFlowStaffStatusRecordDao {

    int add(WorkFlowStaffStatusRecord record);

    List<WorkFlowStaffStatusRecord> listTodayRecords(@Param("userIds") List<Long> userIds, @Param("startTime")Date startTime);
}
