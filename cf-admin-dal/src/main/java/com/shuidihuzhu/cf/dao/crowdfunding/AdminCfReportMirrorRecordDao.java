package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportMirrorRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/10/20.
 */
@DataSource(AdminDS.CF_RW)
public interface AdminCfReportMirrorRecordDao {


    List<CfReportMirrorRecord> getByOperationRecordId(@Param("operationRecordId") long operationRecordId);


    List<CfReportMirrorRecord> getByInfoUuidAndType(@Param("infoUuid") String infoUuid, @Param("role")int role);
}
