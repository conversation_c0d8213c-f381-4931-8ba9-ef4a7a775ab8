package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfCredibleEditInfoDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @time 2020/1/10 下午2:32
 * @desc
 */
@DataSource(DS.CF)
public interface AdminCredibleEditInfoDAO {
    int insert(CfCredibleEditInfoDO editInfoDO);

    CfCredibleEditInfoDO queryById(@Param("id") long id);
}
