package com.shuidihuzhu.cf.dao.admin;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.admin.AdminLoginRecord;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(AdminDS.CF_RW)
public interface AdminLoginRecordDAO {



    List<AdminLoginRecord> selectByLoginAndOrgIdAndUserId(@Param("formatDate") int formatDate, @Param("orgId") int orgId,
                                                          @Param("userId") int userId);
}
