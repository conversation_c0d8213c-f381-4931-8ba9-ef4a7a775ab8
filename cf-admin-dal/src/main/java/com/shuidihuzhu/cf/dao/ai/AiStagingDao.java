package com.shuidihuzhu.cf.dao.ai;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.ai.DO.AiStagingDO;
import com.shuidihuzhu.cf.model.ai.DO.FeedbackDO;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * @Description:
 * @Author: panghair<PERSON>
 * @Date: 2024/5/30 11:51 AM
 */
@DataSource(AdminDS.CF_ADMIN_RW)
public interface AiStagingDao {

    int insert(AiStagingDO aiStagingDO);

    int update(AiStagingDO aiStagingDO);

    AiStagingDO selectStagingInfo(@Param("clewId") Integer clewId, @Param("stagingType") Integer stagingType);

}
