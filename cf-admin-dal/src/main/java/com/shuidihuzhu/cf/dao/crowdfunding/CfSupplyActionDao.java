package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyAction;
import com.shuidihuzhu.cf.model.param.SupplyActionSearchParam;
import com.shuidihuzhu.client.cf.admin.model.CfInfoSupplyField;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-01-09 21:16
 **/
@DataSource(DS.CF)
public interface CfSupplyActionDao {

    int insert(CfInfoSupplyAction supplyAction);

    int updateHandleStatus(@Param("id") long id, @Param("handleStatus") int handleStatus);

    CfInfoSupplyAction getById(@Param("id") long id);

    List<CfInfoSupplyAction> listByCaseIdAndActionType(@Param("caseId") int caseId, @Param("actionType") int actionType);

    List<CfInfoSupplyAction> listByCaseIdAndActionTypes(@Param("caseId") int caseId, @Param("actionTypes") List<Integer> actionTypes);


    List<CfInfoSupplyAction> listByCaseIds(@Param("caseIds") List<Integer> caseIds, @Param("actionType") int actionType);

    @DataSource(DS.CF_SLAVE)
    List<CfInfoSupplyAction> listBySupplyActionSearchParam(SupplyActionSearchParam searchParam);

    List<CfInfoSupplyAction> listCaseIdByReasonAndOrg(@Param("reason") int reason, @Param("org") int org);

    List<CfInfoSupplyAction> listByIds(@Param("ids") List<Long> ids);


    int updateForSubmit(@Param("id") long id,
                        @Param("handleStatus") int handleStatus,
                        @Param("comment") String comment,
                        @Param("imgUrls") String imgUrls);


    int insertCfInfoSupplyFields(@Param("list") List<CfInfoSupplyField> list);

    int deleteSupplyField(@Param("actionId") long actionId,
                          @Param("operationType")int operationType);

    List<CfInfoSupplyField> getByActionId(@Param("actionId") long actionId);

    @DataSource(DS.CF_SLAVE)
    List<CfInfoSupplyAction> listByCaseIdAndHandleStatus(@Param("caseId") int caseId, @Param("statusList") List<Integer> statusList);

}
