package com.shuidihuzhu.cf.dao.admin;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderFlowRemindRecord;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @author: fengxuan
 * @create 2019-11-27 20:40
 **/
@DataSource(AdminDS.CF_RW)
public interface AdminWorkOrderFlowRemindDao {

    int add(AdminWorkOrderFlowRemindRecord remindRecord);


    int countByUserId(@Param("handleUserId") int handleUserId, @Param("createTime") Date createTime);


    List<AdminWorkOrderFlowRemindRecord> listByFlowId(@Param("flowId") long flowId);


    List<AdminWorkOrderFlowRemindRecord> listByFlowIds(@Param("flowIds") List<Integer> flowIds);


    List<AdminWorkOrderFlowRemindRecord> listByUserIdsAndTime(@Param("currentOperators") List<Integer> currentOperators, @Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
