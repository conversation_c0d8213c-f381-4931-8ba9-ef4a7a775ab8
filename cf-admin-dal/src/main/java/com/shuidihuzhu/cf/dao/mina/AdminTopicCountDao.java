package com.shuidihuzhu.cf.dao.mina;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.miniprogram.CfTopicShareCommentCount;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.FRAME)
public interface AdminTopicCountDao {
    List<CfTopicShareCommentCount> listOrderByCommentNum(@Param("cfTopicPublishIds") List<Integer> cfTopicPublishIds);

    List<CfTopicShareCommentCount> listOrderByPraiseNum(@Param("cfTopicPublishIds") List<Integer> cfTopicPublishIds);

    int deleteByTopicId(@Param("topicId") int topicId);
}
