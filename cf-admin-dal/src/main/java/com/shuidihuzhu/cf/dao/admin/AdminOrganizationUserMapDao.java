package com.shuidihuzhu.cf.dao.admin;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.admin.AdminOrganizationUserMap;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: WangYing on 2018/9/17
 */
@DataSource(AdminDS.CF_ADMIN_RW)
public interface AdminOrganizationUserMapDao {

    void setManagerOfNode(@Param("uid") int uid, @Param("organizationId") int organizationId);

    AdminOrganizationUserMap getByUserIdAndOrganizationId(@Param("uid") int uid, @Param("organizationId") int organizationId);

    void deleteEmployeeFromNode(@Param("uid") int uid, @Param("organizationId") int organizationId);

    int addEmployeeToNode(@Param("uid") int uid, @Param("organizationId") int organizationId);

    List<AdminOrganizationUserMap> getByOrgId(@Param("organizationId") int organizationId);

    long countByOrgIds(@Param("organizationIds") List<Integer> organizationIds);

    List<AdminOrganizationUserMap> getLowestOrgByUserIds(@Param("userIds") List<Integer> userIds);

    List<AdminOrganizationUserMap> getByOrgIds(@Param("organizationIds") List<Integer> organizationIds);
}
