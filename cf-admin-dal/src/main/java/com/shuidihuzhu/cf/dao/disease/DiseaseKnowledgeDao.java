package com.shuidihuzhu.cf.dao.disease;

import com.shuidihuzhu.cf.model.disease.DiseaseKnowledge;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface DiseaseKnowledgeDao {

    int insert(DiseaseKnowledge knowledge);

    int update(@Param("id") long id, @Param("diseaseIntro") String diseaseIntro, @Param("cureAndCost") String cureAndCost, @Param("prognosis") String prognosis);

    int delete(@Param("id") long id);

    @DataSource(DS.CF_SLAVE)
    List<DiseaseKnowledge> selectList(@Param("diseaseNorm") String diseaseNorm);

    @DataSource(DS.CF_SLAVE)
    DiseaseKnowledge select(@Param("diseaseNorm") String diseaseNorm);

}
