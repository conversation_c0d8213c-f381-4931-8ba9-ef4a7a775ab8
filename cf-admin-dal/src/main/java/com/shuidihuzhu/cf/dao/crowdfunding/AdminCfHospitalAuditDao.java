package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.enums.BooleanEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoStatusEnum;
import com.shuidihuzhu.cf.model.admin.CfHospitalAuditInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CfHospitalAuditInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CfHospitalAuditInfoTel;
import com.shuidihuzhu.cf.vo.AnchorPageBigInt2VO;
import com.shuidihuzhu.cf.vo.approve.HospitalAuditShowVO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * Created by niejiangnan on 2017/11/30.
 */
@DataSource(AdminDS.CF_RW)
public interface AdminCfHospitalAuditDao {

    int save(CfHospitalAuditInfo cfHospitalAuditInfo);

    int update(CfHospitalAuditInfo cfHospitalAuditInfo);


    CfHospitalAuditInfoExt getByInfoUuid(@Param("infoUuid") String infoUuid);


    CfHospitalAuditInfoExt getById(@Param("id") long id);


    CfHospitalAuditInfoExt getByInfoUuidNoCareDelete(@Param("infoUuid") String infoUuid);


    CfHospitalAuditInfoExt getAutoSendByInfoUuid(@Param("infoUuid") String infoUuid);


    List<CfHospitalAuditInfoExt> getByInfoUuids(@Param("infoUuids") List<String> infoUuids);

    int delete(CfHospitalAuditInfo cfHospitalAuditInfo);


    List<String> getNotFinish();


    int countHospitalAuditByStatus(@Param("auditStatus") int auditStatus);


    List<String> listDistinctOrg();


    List<HospitalAuditShowVO> listByCondition(
            @Param("caseId") Integer caseId,
            @Param("caseStatus") Integer caseStatus,
            @Param("onWorkOrder") int onWorkOrder,
            @Param("hospitalAuditStatus") int hospitalAuditStatus,
            @Param("hospitalSendBeginTime") Date hospitalSendBeginTime,
            @Param("hospitalSendEndTime") Date hospitalSendEndTime,
            @Param("userSubmitBeginTime") Date userSubmitBeginTime,
            @Param("userSubmitEndTime") Date userSubmitEndTime,
            @Param("auditBeginTime") Date auditBeginTime,
            @Param("auditEndTime") Date auditEndTime);


    List<CfHospitalAuditInfoExt> getByBatch(@Param("id") long id, @Param("size") int size);

    int updateHospitalInfo(CfHospitalAuditInfo cfHospitalAuditInfo);

}
