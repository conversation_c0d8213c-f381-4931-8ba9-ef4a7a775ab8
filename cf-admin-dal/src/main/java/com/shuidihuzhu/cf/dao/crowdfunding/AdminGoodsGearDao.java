package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.goods.GoodsGear;
import org.apache.ibatis.annotations.Param;

/**
 * Created by ahrievil on 2017/6/26.
 */
@DataSource(AdminDS.CF_RW)
public interface AdminGoodsGearDao {
    int updateByGoodsGear(GoodsGear goodsGear);
    int updateValid(@Param("valid") int valid, @Param("id") int id);
}
