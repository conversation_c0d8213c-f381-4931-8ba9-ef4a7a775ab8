package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.admin.workorder.CfFundUseDetailDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2019-12-19 17:35
 **/
@DataSource(AdminDS.CF_RW)
public interface CfFundUseDetailDao {

    int batchInsert(@Param("fundUseDetailDOList") List<CfFundUseDetailDO> fundUseDetailDOList);

    int auditDetail(CfFundUseDetailDO cfFundUseDetailDO);


    List<CfFundUseDetailDO> listByWorkOrderId(@Param("workOrderId") int workOrderId);


    List<CfFundUseDetailDO> listByProgressIds(@Param("progressIds") List<Integer> progressIds);


    List<CfFundUseDetailDO> listByCaseId(@Param("caseId") int caseId);
}
