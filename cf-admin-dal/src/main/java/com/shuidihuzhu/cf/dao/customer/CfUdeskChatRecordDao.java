package com.shuidihuzhu.cf.dao.customer;

import com.shuidihuzhu.cf.customer.UdeskChatVo;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@DataSource("statCfSlaveDataSource")
public interface CfUdeskChatRecordDao {

    List<UdeskChatVo> getUdeskChatRecords(@Param("startTime") String startTime, @Param("endTime") String endTime,
                                          @Param("subSessionIds") Set<Integer> subSessionIds);
}