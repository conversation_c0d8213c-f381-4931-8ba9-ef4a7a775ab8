package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingMsgContent;
import org.apache.ibatis.annotations.Param;

/**
 * Created by Ahrievil on 2017/10/12
 */
@DataSource(DS.CF)
public interface AdminCrowdfundingMsgContentDao {
    int insertOne(CrowdfundingMsgContent crowdfundingMsgContent);
    int update(CrowdfundingMsgContent crowdfundingMsgContent);
    CrowdfundingMsgContent selectById(@Param("id") int id);
    int deleteOne(int id);
}
