package com.shuidihuzhu.cf.dao.customer;

import com.shuidihuzhu.cf.customer.ChatHistoryModelVo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource("statCfSlaveDataSource")
public interface CfChatRecordingDao {

    List<ChatHistoryModelVo> getChatHistoryByCids(@Param("startTime") String startTime,
                                                  @Param("endTime") String endTime,
                                                  @Param("cids") List<String> cids);

    List<ChatHistoryModelVo> queryChatRecordByCid(@Param("cid") String cid);
}
