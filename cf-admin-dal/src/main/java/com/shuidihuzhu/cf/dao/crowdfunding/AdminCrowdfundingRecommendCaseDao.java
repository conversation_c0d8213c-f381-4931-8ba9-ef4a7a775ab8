package com.shuidihuzhu.cf.dao.crowdfunding;


import com.shuidihuzhu.cf.model.crowdfunding.AdminCrowdfundingRecommendCaseDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by wangsf on 17/2/12.
 */
@DataSource(DS.CF)
public interface AdminCrowdfundingRecommendCaseDao {

	List<String> getInfoIdList(@Param("limit") int limit, @Param("type") int type);

	/**
	 * @author: wanghui
	 * @time: 2018/10/22 11:15 AM
	 * @description: 通过类型获得 按照 sort排序后的 id列表
	 * @param: [type]
	 * @return: java.util.List<AdminCrowdfundingRecommendCaseDO>
	 */
	List<AdminCrowdfundingRecommendCaseDO> getIdsByTypeOrderBySort(@Param("type") Integer type);

	/**
	 * @author: wanghui
	 * @time: 2018/10/22 12:02 PM
	 * @description: 根据id 批量更新 crowdfunding_recommend_case 这个表中的数据
	 * @param: [crowdfundingRecommendCaseDOs]
	 * @return: int
	 */
	int batchUpdateCrowdfundingRecommendCaseByIds(@Param("crowdfundingRecommendCaseDOs") List<AdminCrowdfundingRecommendCaseDO> crowdfundingRecommendCaseDOs);

	@DataSource(DS.CF_SLAVE)
	Integer getTypeByInfoIdAndTypes(@Param("infoUuid")String infoUuid, @Param("types")List<Integer> types);

	int insert(AdminCrowdfundingRecommendCaseDO caseDO);

	@DataSource(DS.CF_SLAVE)
	AdminCrowdfundingRecommendCaseDO getByUuid(@Param("infoId")String infoUuid, @Param("type")Integer type);

	int delete(@Param("id") long id);

	int updateStatus(@Param("id") long id,@Param("caseStatus") int caseStatus);

	@DataSource(DS.CF_SLAVE)
	AdminCrowdfundingRecommendCaseDO getById(long id);

	@DataSource(DS.CF_SLAVE)
	List<AdminCrowdfundingRecommendCaseDO> getCaseList(@Param("infoId") String infoUuid, @Param("creator") String creator, @Param("status") Integer status,
													   @Param("limit") Integer limit,@Param("offset") Integer offset,@Param("type") Integer type);
	@DataSource(DS.CF_SLAVE)
    List<Long> getCaseListCount(@Param("infoId") String infoUuid, @Param("creator") String creator, @Param("status") Integer status,@Param("type") Integer type);
}
