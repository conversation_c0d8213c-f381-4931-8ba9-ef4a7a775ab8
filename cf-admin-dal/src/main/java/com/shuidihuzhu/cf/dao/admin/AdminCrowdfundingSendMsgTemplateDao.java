package com.shuidihuzhu.cf.dao.admin;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.crowdfunding.AdminSmsTemplateSettingsInfo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingMsgContent;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * Created by niejiangnan on 2017/7/19.
 */
@DataSource(AdminDS.CF_RW)
public interface AdminCrowdfundingSendMsgTemplateDao {

    //获取所有模板

    List<CrowdfundingMsgContent> getAllMsgTemplate(int type);

    //更新模板
    int updateMsgTemplate(CrowdfundingMsgContent crowdfundingMsgContent);

    //根据Key获取模板

    CrowdfundingMsgContent getByKey(@Param("key")String key);

    //根据  模板获取Name

    String getMsgTitleByContent(@Param("message") String message);

    int addSmsTemplate(AdminSmsTemplateSettingsInfo.SmsSettings smsSettings);

    int updateStatusById(@Param("id") int id, @Param("dataStatus") int dataStatus);

    int updatePriorityById(@Param("id") int id, @Param("priority") int priority);


    List<AdminSmsTemplateSettingsInfo.SmsSettings> selectTemplateByParam(@Param("smsGroup") int smsGroup, @Param("modelNum") String modelNum,
                                                                         @Param("operateId") Integer operateId,
                                                                         @Param("list") List<Integer> deleteStatus);


    List<AdminSmsTemplateSettingsInfo.SmsSettings> selectTemplateByIds(@Param("list") List<Integer> ids);

    int addSmsTemplateRecord(AdminSmsTemplateSettingsInfo.SmsSettingsRecord smsSettingsRecord);


    List<AdminSmsTemplateSettingsInfo.SmsSettingsRecord> selectRecordByTemplateId(@Param("smsTemplateSettingsId")
                                                                                          int smsTemplateSettingsId);
    @DataSource(DS.CF)
    List<AdminSmsTemplateSettingsInfo.SmsSettings> selectValidTemplateByGroups(@Param("list") List<Integer> smsGroups);
}
