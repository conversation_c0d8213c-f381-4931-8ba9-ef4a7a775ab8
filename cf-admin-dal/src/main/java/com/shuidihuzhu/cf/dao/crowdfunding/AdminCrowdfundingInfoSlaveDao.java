package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingDreamVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingOperationVo;
import com.shuidihuzhu.cf.vo.crowdfunding.CrowdfundingTextVo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.common.web.util.admin.BasicExample;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * Created by wangsf on 17/1/16.
 */
@DataSource(sources = {DS.CF_SLAVE, DS.CF_SLAVE_2})
public interface AdminCrowdfundingInfoSlaveDao {

	CrowdfundingSummary getCrownfundingSummary(@Param("type") int type, @Param("summaryTime") Timestamp summaryTime);

	List<CrowdfundingInfoVo> selectByExampleJoin(BasicExample basicExample);

    List<CrowdfundingDreamVo> selectByDreamExampleJoin(BasicExample basicExample);

    List<CrowdfundingDreamVo> selectByGoodExampleJoin(BasicExample basicExample);

	CrowdfundingInfo getFundingInfo(String infoId);

	List<CrowdfundingInfo> getFundingInfoList();

	CrowdfundingInfo getFundingInfoById(int id);

	CrowdfundingInfoVo getFundingInfoVoByInfoUuid(@Param("infoUuid") String infoUuid);

	List<CrowdfundingInfo> getFundingInfoByIds(@Param("ids") List<Integer> ids);

	List<CrowdfundingInfo> getFundingInfoByIdsOrderById(@Param("ids") List<Integer> ids);

	List<CrowdfundingInfo> getFundingInfoByInfoIds(@Param("infoIds") List<String> infoIds);

	List<CrowdfundingInfo> getFundingInfoByInfoIdsOrderById(@Param("infoIds") List<String> infoIds);

	List<CrowdfundingInfo> getByUserId(long userId);

	List<CrowdfundingInfo> getRefundByPage(@Param("bizType") Integer bizType, @Param("applyStatus") Integer applyStatus,
			@Param("refundStatus") Integer refundStatus, @Param("operationType") Integer operationType);

	List<CrowdfundingInfo> getRefundListByIds(@Param("ids") List<Integer> ids);

	List<CrowdfundingInfo> getByCreateTime(@Param("startTime") Timestamp startTime,
			@Param("endTime") Timestamp endTime);

	List<CrowdfundingTextVo> getCrowdfundingTextByIds(@Param("idcon") List<Integer> idcon);

	List<CfRepeatUserIdRecord> selectGarbageUserId();

	Integer selectCountByFiveMin(@Param("begin") Timestamp begin, @Param("end") Timestamp end);

	List<String> selectGoodOperation();

	List<CrowdfundingInfo> selectByIdAndAmount(@Param("set") Set<Integer> set, @Param("amount") int amount);

    List<CrowdfundingInfo> selectIdAndUuidAndTypeById(@Param("set") Set<Integer> set);

    List<CrowdfundingInfo> selectIdAndUuidAndTypeByInfoUuid(@Param("set") Set<String> set);

    List<CrowdfundingInfo> selectByUserIds(@Param("set") Set<Long> set);

    List<String> selectDreamInfoNotMapDreamInfoId(@Param("start") int start, @Param("size") int size);

    List<Integer> selectByTimeAndAmount(@Param("beginTime") Timestamp beginTime, @Param("endTime") Timestamp endTime);

	List<AdminReportAddTrustBo> selectCaseAuditStatusByCaseIds(@Param("infoIdList")List<String> infoIdList);

	List<CrowdfundingInfo> getInfoByCreateTimeAndId(@Param("id")int id, @Param("createTime")Date createTime,
													@Param("offset")int offset, @Param("limit")int limit,
													@Param("status")int status);

	int  getCountByCreateTime( @Param("createTime")Date createTime, @Param("status")int status);

	List<CrowdfundingInfoVo> selectByPage(@Param("mobile") String mobile, @Param("id") Integer id,
										  @Param("idListByName") List<Integer> idListByName, @Param("title") String title,
										  @Param("userId") Long userId, @Param("refuseCountHandle") Integer refuseCountHandle,
										  @Param("status") Integer status,
										  @Param("isContact") Integer isContact,
										  @Param("finished") Integer finished, @Param("Date") Date date,
										  @Param("operationStatus") Integer operationStatus, @Param("dataStatus") Integer dataStatus,
										  @Param("startTime") String startTime,
										  @Param("endTime") String endTime, @Param("handle") Integer handle,
										  @Param("sortHandle") String sortHandle);

	/**
	 * 请注意，这个方法不要用pagehelper
	 * 1，没必要
	 * 2，特意改造为了不用pagehelper的方式
	 *
	 * @param pageSize
	 * @return
	 */
	List<CrowdfundingInfoVo> selectBaseApproveListOnlyOne(@Param("pageSize") int pageSize);

	List<CrowdfundingInfoVo> selectBaseApproveListPages(@Param("begin") int begin, @Param("offset") int offset);

	List<CrowdfundingInfoVo> selectBaseContactListPages(@Param("begin") int begin, @Param("offset") int offset);

	List<CrowdfundingInfo> selectByRepeatCase();

	List<CrowdfundingInfoVo> reserveSelectByPage(@Param("mobile") String mobile, @Param("id") Integer id,
										  @Param("idListByName") List<Integer> idListByName,@Param("userId") long userId, @Param("title") String title);

}
