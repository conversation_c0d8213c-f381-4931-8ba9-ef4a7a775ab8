package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfItemTemplate;
import com.shuidihuzhu.cf.model.crowdfunding.CfTemplateField;
import com.shuidihuzhu.client.cf.admin.model.CfItemField;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2020/11/26
 */
@DataSource(DS.CF)
public interface CfTemplateFieldDao {


    int addField(CfItemField cfItemField);

    int updateField(@Param("id") long id, @Param("delete") int delete);

    List<CfItemField> listCfItemFields(@Param("delete") int delete);

    List<CfItemField> getCfItemFieldByIds(@Param("ids") List<Long> ids);




    int addTemplate(CfItemTemplate cfItemTemplate);

    int updateTemplate(@Param("id") long id, @Param("delete") int delete);

    List<CfItemTemplate> getTemplates(@Param("delete") int delete);

    List<CfItemTemplate> getTemplatesByParentId(@Param("delete") int delete,@Param("id") long id);

    List<CfItemTemplate> getTemplatesByIds(@Param("ids") List<Long> ids);

    CfItemTemplate getById(@Param("id") long id);




    int addTemplateField(List<CfTemplateField> list);

    List<CfTemplateField> getFieldIds(@Param("fieldIds") List<Long> fieldIds);

    List<CfTemplateField> getFieldByTempId(@Param("tempId") long tempId);


    List<CfTemplateField> getFieldByTempIds(@Param("tempIds") List<Long> tempIds);


}
