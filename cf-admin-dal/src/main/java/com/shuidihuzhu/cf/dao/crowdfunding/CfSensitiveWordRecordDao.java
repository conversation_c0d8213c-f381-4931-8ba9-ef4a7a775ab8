package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CfSensitiveWordRecord;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfSensitiveWordRecordVo;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;
import java.util.Set;

@DataSource(DS.CF)
public interface CfSensitiveWordRecordDao {
	
	int saveBatch(List<CfSensitiveWordRecordVo> list);

	int insertOne(CfSensitiveWordRecord cfSensitiveWordRecord);

	int delByBizTime(@Param("beginTime") Timestamp beginTime, @Param("endTime") Timestamp endTime);

	List<CfSensitiveWordRecord> selectByUserId(@Param("set") Set<Long> set);

    List<CfSensitiveWordRecord> selectByIds(@Param("list") List<Long> list);

    CfSensitiveWordRecord selectById(@Param("id") long id);

    List<Long> selectIdByTime(@Param("beginTime") Timestamp beginTime, @Param("endTime") Timestamp endTime,
                                 @Param("start") int start, @Param("size") int size);

	@DataSource(DS.CF_SLAVE)
	Long selectByBizIdAndBizType(@Param("bizId") long bizId, @Param("bizType") int bizType);

    List<CfSensitiveWordRecord> listByBizIdAndBizType(@Param("bizId") long bizId, @Param("bizType") int bizType);
}
