package com.shuidihuzhu.cf.dao.stat.message;

import com.shuidihuzhu.cf.model.message.CfWxArticleTotal;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

@DataSource("statCfSlaveDataSource")
public interface CfWxArticleTotalDao {
    CfWxArticleTotal getCfWxArticleTotalByMediaIdAndThirdType(@Param("msgDataId") String msgDataId,
                                                              @Param("thirdType") int thirdType, @Param("index") String index);

    CfWxArticleTotal getCfWxArticleTotalByDate(@Param("msgDataId") String msgDataId,
                                               @Param("thirdType") int thirdType,
                                               @Param("index") int index,
                                               @Param("statDate") String statDate);
}
