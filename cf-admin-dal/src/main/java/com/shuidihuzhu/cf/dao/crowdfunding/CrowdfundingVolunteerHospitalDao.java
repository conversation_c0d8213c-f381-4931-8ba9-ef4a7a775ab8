package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingVolunteerHospital;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/2/28.
 */
@DataSource(AdminDS.CF_RW)
public interface CrowdfundingVolunteerHospitalDao {
    int insertHospital(CrowdfundingVolunteerHospital crowdfundingVolunteerHospital);

    @DataSource(DS.CF)
    List<CrowdfundingVolunteerHospital> getHospitalName(@Param("cityId") int cityId);
}
