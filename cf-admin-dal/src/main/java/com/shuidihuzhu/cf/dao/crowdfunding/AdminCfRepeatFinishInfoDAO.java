package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.caseRepeat.AdminCfRepeatFinishInfo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

import java.util.Collection;

@DataSource(DS.CF)
public interface AdminCfRepeatFinishInfoDAO {

    int insertList(Collection<AdminCfRepeatFinishInfo> list);

    int insert(AdminCfRepeatFinishInfo finishInfo);

}
