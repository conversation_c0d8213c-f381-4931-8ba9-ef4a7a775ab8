package com.shuidihuzhu.cf.dao.ai;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.ai.AiRiskStrategyConfig;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: ai判断内容风险策略配置
 * @Author: pangh<PERSON><PERSON>
 * @Date: 2025/5/20 17:38
 */
@DataSource(AdminDS.CF_ADMIN_RW)
public interface AiJudgeRiskStrategyConfigDao {

    List<AiRiskStrategyConfig> selectAllEnableConfig(@Param("judgeType") Integer judgeType);

    AiRiskStrategyConfig selectConfigByScene(@Param("sceneType") String sceneType);

    int insert(AiRiskStrategyConfig aiRiskStrategyConfig);

    int updateBySceneType(@Param("sceneType") String sceneType,
                          @Param("judgePrompt") String judgePrompt,
                          @Param("riskFactor") String riskFactor);

}
