package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.ai.CfAiMaterialsSmartAIResult;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2022/12/8 15:55
 * @Description:
 */
@DataSource(AdminDS.CF_ADMIN_RW)
public interface CfAiMaterialsSmartAIResultDao {

    int insert(CfAiMaterialsSmartAIResult cfAiMaterialsSmartAIResult);

    CfAiMaterialsSmartAIResult getByWorkOrderId(@Param("workOrderId") long workOrderId);
}
