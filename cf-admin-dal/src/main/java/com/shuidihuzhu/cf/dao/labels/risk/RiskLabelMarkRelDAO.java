package com.shuidihuzhu.cf.dao.labels.risk;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.domain.label.core.LabelDO;
import com.shuidihuzhu.cf.domain.label.risk.RiskLabelMarkRecordRelDO;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@DataSource(AdminDS.CF_RW)
public interface RiskLabelMarkRelDAO {

    int addList(List<RiskLabelMarkRecordRelDO> list);

    List<RiskLabelMarkRecordRelDO> getAllByRecordId(@Param("recordId") long recordId);
}
