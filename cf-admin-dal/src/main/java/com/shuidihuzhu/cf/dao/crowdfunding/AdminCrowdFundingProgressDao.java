package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.vo.crowdfunding.AdminCrowdfundingProgress;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress;
import com.shuidihuzhu.common.web.util.admin.BasicExample;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;

/**
 * Created by chao on 16/7/12.
 */

@DataSource(DS.CF_SLAVE_2)
public interface AdminCrowdFundingProgressDao {

	List<CrowdFundingProgress> getActivityProgress(BasicExample basicExample);

	CrowdFundingProgress getActivityProgressById(@Param("id") long id);

	Integer selectCountByFiveMin(@Param("begin") Timestamp begin, @Param("end") Timestamp end);

    @DataSource(DS.CF)
	int updateImageUrls(@Param("imageUrls") String imageUrls, @Param("id") int id, @Param("crowdfundingId") int crowdfundingId);

    @DataSource(DS.CF)
	int updateImageUrlsByNoIsDelete(@Param("imageUrls") String imageUrls, @Param("id") int id, @Param("crowdfundingId") int crowdfundingId);

	List<CrowdFundingProgress> getByCreateTime(@Param("beginTime") Timestamp beginTime, @Param("endTime") Timestamp endTime);

	List<CrowdFundingProgress> getByIds(@Param("ids") List<Integer> ids);

    @DataSource(DS.CF)
    int updateContent(@Param("id") long id, @Param("content") String content);

    @DataSource(DS.CF)
    int insertInCrowdfundingProgress(CrowdFundingProgress crowdFundingProgress);

    @DataSource(DS.CF_SLAVE_2)
	AdminCrowdfundingProgress getProgressById(@Param("progressId") Integer progressId);

	@DataSource(DS.CF_SLAVE_2)
	AdminCrowdfundingProgress getLastOneByCaseId(@Param("caseId") int caseId);

	@DataSource(DS.CF)
	int delProgressById(@Param("id") long id);

	@DataSource(DS.CF)
	int reviveProgressById(@Param("id") long id);

	@DataSource(DS.CF_SLAVE_2)
	AdminCrowdfundingProgress getFirstOneByCaseId(@Param("caseId") int caseId);

}
