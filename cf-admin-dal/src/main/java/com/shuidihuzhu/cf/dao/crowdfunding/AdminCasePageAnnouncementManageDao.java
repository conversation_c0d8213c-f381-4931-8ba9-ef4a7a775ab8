package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCasePageAnnouncementManage;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCasePageAnnouncementManageVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminBlessingCardVo;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/25  10:59 上午
 */
@DataSource(AdminDS.CF_RW)
public interface AdminCasePageAnnouncementManageDao {

    int add(AdminCasePageAnnouncementManage adminCasePageAnnouncementManage);

    int update(AdminCasePageAnnouncementManage adminCasePageAnnouncementManage);

    int updateByOnline(@Param("id") long id,
                       @Param("status") int status);


    int updateByTop(@Param("id") long id,
                    @Param("top") int top);

    int delete(@Param("id") long id);

    List<AdminCasePageAnnouncementManageVo> getList(@Param("title") String title,
                                                    @Param("status") int status,
                                                    @Param("top") int top,
                                                    @Param("type") int type);

}
