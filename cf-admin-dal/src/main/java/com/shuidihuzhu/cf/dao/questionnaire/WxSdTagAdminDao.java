package com.shuidihuzhu.cf.dao.questionnaire;

import com.shuidihuzhu.cf.model.questionnaire.WxSdTag;
import com.shuidihuzhu.cf.vo.questionnaire.WxSdTagValueAdminVo;
import com.shuidihuzhu.cf.vo.questionnaire.WxSdTagAdminVo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: mhk
 * @Date: 2018/11/8  17:10
 */
@DataSource(DS.CF)
public interface WxSdTagAdminDao {

    List<WxSdTagAdminVo> getAll();

    List<WxSdTagValueAdminVo> getByParentId(@Param("parentId") int parentId);

    WxSdTag getByTagId(@Param("id") int tagId);
}
