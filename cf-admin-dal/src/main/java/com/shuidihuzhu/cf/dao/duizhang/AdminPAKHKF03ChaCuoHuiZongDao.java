package com.shuidihuzhu.cf.dao.duizhang;

import com.shuidihuzhu.client.baseservice.pay.model.pingan.duizhang.PAChacuoHuiZong;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * Created by ch<PERSON> on 2017/10/26.
 */
@DataSource(DS.PAY)
public interface AdminPAKHKF03ChaCuoHuiZongDao {
	void add(PAChacuoHuiZong huiZong);

	void delete(@Param("fileDate") String fileDate);

	PAChacuoHuiZong get(@Param("fileDate") String fileDate);
}
