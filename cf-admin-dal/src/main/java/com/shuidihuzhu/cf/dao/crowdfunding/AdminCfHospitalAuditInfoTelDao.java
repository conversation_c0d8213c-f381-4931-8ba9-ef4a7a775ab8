package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfHospitalAuditInfoTel;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-07-29
 **/
@DataSource(DS.CF)
public interface AdminCfHospitalAuditInfoTelDao {

    int insert(@Param("cfHospitalAuditInfoTels") List<CfHospitalAuditInfoTel> cfHospitalAuditInfoTels);

    int update(CfHospitalAuditInfoTel cfHospitalAuditInfoTel);

    List<CfHospitalAuditInfoTel> getByCfHospitalAuditInfoId(@Param("cfHospitalAuditInfoId") long cfHospitalAuditInfoId);

    CfHospitalAuditInfoTel getById(@Param("id") long id);

    int updateRisk(CfHospitalAuditInfoTel cfHospitalAuditInfoTelRisk);
}
