package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.client.adminpure.model.caseinfo.CfCasePublicInfo;
import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2021/8/20 16:01
 * @Description:
 */
@DataSource(AdminDS.CF_RW)
public interface CfCasePublicInfoDao {

    int addCasePublicInfo(CfCasePublicInfo cfCasePublicInfo);

    List<CfCasePublicInfo> getListByIdList(@Param("idList") List<Long> caseIdList);

    List<CfCasePublicInfo> getListByInfoUuidAndType(@Param("infoUuid") String infoUuid);

    int update(CfCasePublicInfo cfCasePublicInfo);

    int deleteByCaseIdAndType(@Param("caseId") int caseId, @Param("type") int type);
}
