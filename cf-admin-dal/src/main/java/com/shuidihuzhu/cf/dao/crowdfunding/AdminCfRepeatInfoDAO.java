package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.AdminCfRepeatInfo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import lombok.Data;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@DataSource(DS.CF)
public interface AdminCfRepeatInfoDAO {

    int updateRepeatInfoByCaseId(@Param("caseId") int caseId, @Param("repeatInfo") String repeatInfo, @Param("repeatSummary") int repeatSummary);

    int updateRepeatDiagnosisByCaseId(@Param("caseId") int caseId, @Param("repeatDiagnosisInfo") String repeatDiagnosisInfo);

    int updateNeedUpdateByCaseIds(@Param("caseIds") List<Integer> caseIds, @Param("needUpdate") int needUpdate);

    List<AdminCfRepeatInfo> selectByCaseIds(@Param("caseIds") List<Integer> caseIds);

    AdminCfRepeatInfo selectByCaseId(@Param("caseId") int caseId);

    int insertOrUpdate(AdminCfRepeatInfo repeatInfo);
}
