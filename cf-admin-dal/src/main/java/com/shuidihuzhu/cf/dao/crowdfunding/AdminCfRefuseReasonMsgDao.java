package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonMsg;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;
import java.util.Set;

/**
 * Created by Ahrievil on 2017/7/31
 */
@DataSource(DS.CF)
public interface AdminCfRefuseReasonMsgDao {
    int insertOne(CfRefuseReasonMsg cfRefuseReasonMag);
    CfRefuseReasonMsg selectByInfoUuidAndType(@Param("infoUuid") String infoUuid, @Param("type") int type);
    CfRefuseReasonMsg selectByInfoIdAndType(@Param("infoUuid") String infoUuid, @Param("type") int type);
    int insertList(@Param("list") List<CfRefuseReasonMsg> list);
    List<String> selectWithTimeLimit(@Param("begin") Timestamp begin, @Param("end") Timestamp end, @Param("start") int start, @Param("size") int size);
    int deleteByInfoUuid(@Param("infoUuid") String infoUuid);
    int deleteByInfoUuidAndTypes(@Param("infoUuid") String infoUuid, @Param("set") Set<Integer> set);
    List<CfRefuseReasonMsg> selectByInfoUuid(@Param("infoUuid") String infoUuid);
    List<CfRefuseReasonMsg> selectSimpleFieldsByTimeLimit(@Param("begin") Timestamp begin, @Param("end") Timestamp end, @Param("start") int start, @Param("size") int size);
}
