package com.shuidihuzhu.cf.dao.ai;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.ai.AiPromptConfig;
import com.shuidihuzhu.cf.model.ai.AiRiskJudgeRecord;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

/**
 * @Description: ai风险命中记录
 * @Author: panghairui
 * @Date: 2025/5/21 15:31
 */
@DataSource(AdminDS.CF_ADMIN_RW)
public interface AiJudgeRiskRecordDao {

    int insert(AiRiskJudgeRecord riskRecord);

}
