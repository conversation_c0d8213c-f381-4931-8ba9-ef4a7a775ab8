package com.shuidihuzhu.cf.dao.admin;

import com.shuidihuzhu.cf.model.admin.CfMaterialsPic;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2021/3/17
 */
@DataSource("shuidiCfAdminDataSource")
public interface CfMaterialsPicDao {

    List<CfMaterialsPic> getByCaseId(@Param("caseId") int caseId);


    int insert(List<CfMaterialsPic> list);


    int delete(@Param("caseId") int caseId);
}
