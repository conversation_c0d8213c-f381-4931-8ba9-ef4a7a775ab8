package com.shuidihuzhu.cf.dao.approve;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingApprove;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@DataSource("shuidiCfAdminDataSource")
public interface AdminApproveDAO {

    int insert(CrowdfundingApprove adminApproveDO);

    List<CrowdfundingApprove> listByCaseId(@Param("caseId") int caseId);

    List<CrowdfundingApprove> listByCaseIdAndSourceTypes(@Param("caseId") int caseId, @Param("sourceTypes") List<Integer> sourceTypes);

    CrowdfundingApprove getLastByCaseIdAndOrgKey(@Param("caseId") int caseId, @Param("orgKey") String orgKey);

    CrowdfundingApprove getById(@Param("id") int id);

}
