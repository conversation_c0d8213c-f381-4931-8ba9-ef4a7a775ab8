package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfMaterialVerityHistory;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface CfMaterialVerityHistoryDAO {

    int insertRecords(@Param("list") List<CfMaterialVerityHistory> verityHistoryRecords);

    @DataSource(DS.CF_SLAVE)
    List<CfMaterialVerityHistory> selectByMaterialType(@Param("infoId") String infoId, @Param("materialId") int materialId,
                                                       @Param("handleType") int handleType, @Param("limit") int limit,
                                                       @Param("offset") int offset);

    @DataSource(DS.CF_SLAVE)
    int countByMaterialType(@Param("infoId") String infoId, @Param("materialId") int materialId,
                            @Param("handleType") int handleType);

    @DataSource(DS.CF_SLAVE)
    int getCount(@Param("caseId") int caseId,
                 @Param("materialId") int materialId,
                 @Param("handleType") int handleType);

    @DataSource(DS.CF_SLAVE)
    CfMaterialVerityHistory selectLatestMaterial(@Param("caseId") int caseId,
                                                 @Param("materialId") int materialId,
                                                 @Param("handleType") int handleType);

    CfMaterialVerityHistory selectLatestMaterialNoType(@Param("caseId") int caseId,
                                                @Param("materialId") int materialId);


    @DataSource(DS.CF_SLAVE)
    CfMaterialVerityHistory selectLatestMaterialByMaterial(@Param("caseId") int caseId,
                                                           @Param("materialId") int materialId);

    @DataSource(DS.CF_SLAVE)
    CfMaterialVerityHistory selectLatestByWorkOrder(@Param("caseId") int caseId, @Param("workOrderId") long workOrderId,
                                                           @Param("materialId") int materialId);
}
