package com.shuidihuzhu.cf.dao.crmUserManage;

import com.shuidihuzhu.cf.model.CrmUserManage.UserManage;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface UserRoleDao {

    int addUserRoles(@Param("list") List<UserManage.UserRoleModel> roleModels);

    int deleteUserRolesByIds(@Param("ids") List<Long> ids);
    int deleteUserRolesByMobiles(@Param("cryptoMobiles") List<String> cryptoMobiles);
    int deleteRoleByCaseIdAndRole(@Param("caseId") int caseId, @Param("userRole") int userRole);


    @DataSource(DS.CF_SLAVE)
    List<UserManage.UserRoleModel> selectByMobiles(@Param("cryptoMobiles") List<String> cryptoMobiles);

    @DataSource(DS.CF_SLAVE)
    List<UserManage.UserRoleModel> selectByMobileAndUserRole(@Param("cryptoMobile") String cryptoMobiles, @Param("userRole") int userRole);

    List<UserManage.UserRoleModel> selectByCaseIdAndUserRole(@Param("caseId") int caseId, @Param("userRoles") List<Integer> userRoles);


}
