package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.domain.cf.CfInfoLostContactDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2018-07-26  17:04
 */
@DataSource(DS.CF)
public interface AdminCfInfoLostContactDAO {

    /**
     * 获取最后一条此案例信息
     * @param infoUuid
     * @return
     */
    CfInfoLostContactDO getValidByInfoUuid(@Param("infoUuid") String infoUuid);

    int insert(CfInfoLostContactDO cfInfoLostContactDO);

    int setOldRecordInvalid(@Param("infoUuid") String infoUuid);

}
