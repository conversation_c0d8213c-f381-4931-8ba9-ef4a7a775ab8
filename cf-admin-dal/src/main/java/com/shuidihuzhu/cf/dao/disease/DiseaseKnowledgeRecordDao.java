package com.shuidihuzhu.cf.dao.disease;

import com.shuidihuzhu.cf.model.disease.DiseaseKnowledgeRecord;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface DiseaseKnowledgeRecordDao {

    int insert(@Param("scienceId") long scienceId, @Param("operator") long operator, @Param("remark") String remark);

    @DataSource(DS.CF_SLAVE)
    List<DiseaseKnowledgeRecord> selectList(@Param("scienceId") long scienceId);

}
