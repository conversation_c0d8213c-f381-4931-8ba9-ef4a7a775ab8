package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CfBlackListRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by Ahrievil on 2017/11/5
 */
@DataSource(DS.CF)
public interface CfBlackListRecordDao {
    int insertOne(CfBlackListRecord cfBlackListRecord);
    List<CfBlackListRecord> selectByUserId(@Param("userId") int userId);
}
