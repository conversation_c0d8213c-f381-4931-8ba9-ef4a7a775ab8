package com.shuidihuzhu.cf.dao.mina;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.miniprogram.CfTopic;
import com.shuidihuzhu.cf.vo.mina.CfMinaTopicDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.FRAME)
public interface AdminTopicDao {

    List<CfTopic> getByTitle(@Param("phaseIds") List<Integer> phaseIds, @Param("title") String title);

    List<CfMinaTopicDetailVo> getTopics(@Param("title") String title);

    List<CfMinaTopicDetailVo> getUnPublishTopics(@Param("title") String title);

    List<CfMinaTopicDetailVo> listByPhaseIds(@Param("phaseIds") List<Integer> phaseIds);

    int updatePhaseId(@Param("topicId") int topicId, @Param("phaseId") int phaseId);

    int addOne(@Param("cfTopic") CfTopic cfTopic);

    int deletePhaseById(@Param("topicIds") List<Integer> topicIds);

    List<CfTopic> getPublishTopics(@Param("title") String title);

    int deleteByPhaseId(@Param("phaseId") int phaseId);
}
