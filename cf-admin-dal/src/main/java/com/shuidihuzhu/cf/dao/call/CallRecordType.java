package com.shuidihuzhu.cf.dao.call;

import java.util.HashMap;
import java.util.Map;

public enum CallRecordType {
    call_in(1, "呼入"),
    call_out(2, "呼出");

    private int value;
    private String description;

    private static Map<Integer, CallRecordType> enumMap = new HashMap();

    static {
        for (CallRecordType tmpEnum : CallRecordType.values()) {
            enumMap.put(tmpEnum.getValue(), tmpEnum);
        }
    }

    CallRecordType(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public static boolean isValid(int value) {
        return null != enumMap.get(value);
    }

    public static CallRecordType parseInt(int intValue) {
        if (!isValid(intValue)) {
            throw new RuntimeException("枚举值不正确");
        }
        return enumMap.get(intValue);
    }
}
