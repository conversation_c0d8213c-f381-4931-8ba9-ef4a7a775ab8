package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.admin.TwModifyRecordDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 图文修改
 * @Author: panghairui
 * @Date: 2022/3/10 2:18 下午
 */
@DataSource(DS.CF)
public interface AdminTwModifyDao {

    int saveTwModifyRecord(TwModifyRecordDO twModifyRecordDO);

    int updateTwModifyFlag(@Param("wordOrderId") long wordOrderId, @Param("modifyFlag") int modifyFlag);

    TwModifyRecordDO selectByInfoId(@Param("infoUuid") String infoUuid);

    TwModifyRecordDO selectByWorkOrderId(@Param("workOrderId") long workOrderId);

    List<TwModifyRecordDO> selectByInfoIds(@Param("infoUuids") List<String> infoUuids);

    List<TwModifyRecordDO> selectByWorkOrderIdList(@Param("workOrderIdList") List<Long> workOrderIdList);

}
