package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfSendProve;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

@DataSource(DS.CF)
public interface CfSendProveDao {

    int insertOne(CfSendProve cfSendProve);

    CfSendProve getById(@Param("id") long id);

    int auditPictureUrl(@Param("id") long id,
                        @Param("pictureUrl") String pictureUrl,
                        @Param("pictureAuditStatus") int pictureAuditStatus,
                        @Param("pictureRejectedReason") String pictureRejectedReason);

    int updatePictureAuditStatusById(@Param("id") long id, @Param("pictureAuditStatus") int pictureAuditStatus);

    CfSendProve getLastOneByCaseId(@Param("caseId") int caseId);

    int updateRejectedReason(@Param("id") long id, @Param("rejectedReason") String rejectedReason);

    int updateCancelReason(@Param("id") long id, @Param("cancelReason") String cancelReason);

}
