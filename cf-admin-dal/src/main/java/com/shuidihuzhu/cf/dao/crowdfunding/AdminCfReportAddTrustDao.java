package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportAddTrust;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/10/23.
 */
@DataSource(AdminDS.CF_RW)
public interface AdminCfReportAddTrustDao {

    @DataSource(DS.CF)
    List<CfReportAddTrust> getListByInfoUuid(@Param("infoUuids") List<String> infoUuids);

    int save(CfReportAddTrust cfReportAddTrust);

    int update(CfReportAddTrust cfReportAddTrust);


    CfReportAddTrust getNewestByInfoUuid(@Param("infoUuid")String infoUuid);


    CfReportAddTrust queryById(@Param("id")long id);


    int delete(@Param("infoUuid") String infoUuid);


    List<CfReportAddTrust> getByBatch(@Param("id") long id, @Param("size") int size);


    CfReportAddTrust getTrustById(@Param("id")long id);
}
