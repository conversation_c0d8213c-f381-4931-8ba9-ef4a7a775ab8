package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.ReportOperateRecordDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/8/16 上午11:21
 * @desc
 */
@DataSource(DS.CF)
public interface ReportOperateRecordDAO {
    int insert(ReportOperateRecordDO recordDO);

    List<ReportOperateRecordDO> queryByWorkOrderId(@Param("workOrderId") long workOrderId);
}
