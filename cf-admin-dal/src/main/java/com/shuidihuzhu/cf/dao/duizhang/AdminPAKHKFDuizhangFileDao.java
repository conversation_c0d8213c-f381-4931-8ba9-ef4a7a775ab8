package com.shuidihuzhu.cf.dao.duizhang;

import com.shuidihuzhu.client.baseservice.pay.model.pingan.PADuiZhangFile;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * Created by ch<PERSON> on 2017/10/26.
 */
@DataSource(DS.PAY)
public interface AdminPAKHKFDuizhangFileDao {
	void add(PADuiZhangFile file);

	// 考虑到可能多次执行，要获取最新的那一条
	PADuiZhangFile get(@Param("fileDate") String fileDate,
	                   @Param("fileType") String fileType);

	// 更新流水号和文件路径
	void updateTradeSn(@Param("id") int id,
	                   @Param("tradeSN") String tradeSN,
	                   @Param("filePath") String filePath);

	// 下载成功之后，调用这个接口
	void updateSuccess(@Param("id") int id);
}
