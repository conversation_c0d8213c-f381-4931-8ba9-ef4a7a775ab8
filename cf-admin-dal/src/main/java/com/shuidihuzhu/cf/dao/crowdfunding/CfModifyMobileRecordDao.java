package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.client.adminpure.model.caseinfo.CfModifyMobileRecord;
import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2021/9/14 21:15
 * @Description:
 */
@DataSource(AdminDS.CF_ADMIN_RW)
public interface CfModifyMobileRecordDao {
    int insert(CfModifyMobileRecord cfModifyMobileRecord);

    List<CfModifyMobileRecord> selectByCaseId(@Param("caseId") int caseId);
}
