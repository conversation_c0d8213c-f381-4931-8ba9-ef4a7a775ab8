package com.shuidihuzhu.cf.dao.ai;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.ai.AiGenerateRecord;
import com.shuidihuzhu.cf.model.ai.DO.FeedbackDO;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2024/6/3 3:31 PM
 */
@DataSource(AdminDS.CF_ADMIN_RW)
public interface AiGenerateDao {

    int insert(AiGenerateRecord aiGenerateRecord);

    List<AiGenerateRecord> selectRecordByUuid(@Param("uuid") String uuid);

}
