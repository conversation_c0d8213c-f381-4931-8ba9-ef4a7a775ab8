package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CfOperationRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * Created by ahrievil on 2017/2/7.
 */
@DataSource(AdminDS.CF_RW)
public interface CfAdminOperationRecordDao {
    int add(CfOperationRecord cfOperationRecord);

    CfOperationRecord selectOpByInfoId(String infoId);

    CfOperationRecord selectContact(String infoId);

    List<Map<String, Object>> selectRefuseOperation(@Param("curNum") int curNum);


    CfOperationRecord getLastByOperation(@Param("infoId") String infoId, @Param("operation") int operation);

    @DataSource(DS.CF_SLAVE_2)
    List<CfOperationRecord> getLastOneByInfoUUidList(@Param("infoUuids") List<String> infoUuids);
}
