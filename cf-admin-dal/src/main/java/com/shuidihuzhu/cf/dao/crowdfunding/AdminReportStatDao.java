package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.report.EntryStatNum;
import com.shuidihuzhu.cf.model.report.ReportStatTotal;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2019/4/9
 */
@DataSource(DS.CF)
public interface AdminReportStatDao {

    List<Integer> getHandlerUser(@Param("start") String start,
                                 @Param("end") String end);



    List<Long> getlingquNum(@Param("start") String start,
                            @Param("end") String end,
                            @Param("userId")int userId);
    @DataSource(DS.CF_SLAVE)
    List<Long> getyiliuNum(@Param("start") String start,
                           @Param("userId")int userId);
    @DataSource(DS.CF_SLAVE)
    List<Long> getweilingquNum();

    int getEntryNum(@Param("workOrderIds") List<Long> workOrderIds);


    List<Long> getjinriFinishNum(@Param("start") String start,
                                 @Param("end") String end,
                                 @Param("dealResult") int dealResult,
                                 @Param("userId")int userId);

    List<Long> getyiliuFinishNum(@Param("start") String start,
                                 @Param("end") String end,
                                 @Param("dealResult") int dealResult,
                                 @Param("userId")int userId);
    @DataSource(DS.CF_SLAVE)
    List<Long> getyiliuDoingFinishNum(@Param("start") String start,
                                 @Param("end") String end,
                                 @Param("dealResults") List<Integer> dealResults,
                                 @Param("userId")int userId);


    List<EntryStatNum> getEntryStatNum(@Param("workOrderIds") List<Long> workOrderIds,
                                       @Param("dealResults") List<Integer> dealResults);


    int getLostNum(@Param("start") String start,
                   @Param("end") String end);


    int getUserLostNum(@Param("start") String start,
                       @Param("end") String end,
                       @Param("userId")int userId);

    @DataSource(DS.CF_SLAVE)
    List<ReportStatTotal.SimpleWorkIdAndOperatorId> queryWorkOrderIdAndOperatorId(@Param("createTime") Date createTime);

    @DataSource(DS.CF_SLAVE)
    List<ReportStatTotal.SimpleOrderHandleResult> queryOrderHandleResult(@Param("updateTime") Date updateTime,
                                                                         @Param("dealStatus") Collection<Integer> dealStatus);
    @DataSource(DS.CF_SLAVE)
    List<Integer> queryReportIdByWorkIds(@Param("workOrderIds")Collection<Long> workOrderIds);

    @DataSource(DS.CF_SLAVE)
    List<ReportStatTotal.SimpleOrderHandleResult> queryNoHandleWorks(@Param("fromId") long fromId,
            @Param("dealStatus") Collection<Integer> dealStatus, @Param("operatorIds") Collection<Integer> operatorIds);

    @DataSource(DS.CF_SLAVE)
    List<ReportStatTotal.SimpleWorkIdAndOperatorId> getHandlerUserByAssignTime(@Param("start") Date start,
                                 @Param("end") Date end);

    int getUserLostNumByDate(@Param("start") Date start,
                       @Param("end") Date end,
                       @Param("userId")int userId);
}
