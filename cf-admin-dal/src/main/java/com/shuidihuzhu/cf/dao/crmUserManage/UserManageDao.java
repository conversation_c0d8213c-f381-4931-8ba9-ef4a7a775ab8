package com.shuidihuzhu.cf.dao.crmUserManage;

import com.shuidihuzhu.cf.model.CrmUserManage.UserManage;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@DataSource(DS.CF)
public interface UserManageDao {

    int addUsers(List<UserManage.UserAccount> userAccount);

    int updateIdCardByMobiles(@Param("cryptoIdCard") String cryptoIdCard, @Param("userName") String userName, @Param("cryptoMobiles") Collection<String> cryptoMobiles);

    int updateUuidByIdCards(@Param("uuid") String uuid, @Param("cryptoMobiles") Collection<String> cryptoMobiles);

    @DataSource(DS.CF_SLAVE)
    List<UserManage.UserAccount> selectByMobiles(@Param("cryptoMobiles") Collection<String> cryptoMobiles);

    @DataSource(DS.CF_SLAVE)
    List<UserManage.UserAccount> selectByIdCards(@Param("cryptoIdCards") Collection<String> cryptoIdCards);

    @DataSource(DS.CF_SLAVE)
    List<UserManage.UserAccount> selectByUuids(@Param("uuidList") Collection<String> uuids);
}
