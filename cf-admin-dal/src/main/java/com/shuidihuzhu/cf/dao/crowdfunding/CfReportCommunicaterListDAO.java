package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfReportCommunicaterDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/12/13 下午4:43
 * @desc
 */
@DataSource(DS.CF)
public interface CfReportCommunicaterListDAO {
    int insert(CfReportCommunicaterDO communicaterDO);
    List<CfReportCommunicaterDO> query(@Param("caseId")int caseId, @Param("reportId") int reportId, @Param("type") int type);
    CfReportCommunicaterDO queryById(@Param("id") long id, @Param("caseId")int caseId, @Param("reportId") int reportId);
    CfReportCommunicaterDO queryByMobile(@Param("caseId")int caseId, @Param("reportId") int reportId, @Param("mobile") String mobile);

    int updateIsDelete(@Param("id") long id, @Param("caseId")int caseId, @Param("reportId") int reportId);

}
