package com.shuidihuzhu.cf.dao.approve;

import com.shuidihuzhu.cf.domain.approve.SubsidyApplyRecordDO;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/11/3 2:47 PM
 */
@DataSource("shuidiCfAdminDataSource")
public interface CfServiceSubsidyRecordDao {

    void insertSubsidyApplyRecord(SubsidyApplyRecordDO subsidyApplyRecordDO);

    List<SubsidyApplyRecordDO> selectRecordByCaseId(@Param("caseId") Integer caseId);

}
