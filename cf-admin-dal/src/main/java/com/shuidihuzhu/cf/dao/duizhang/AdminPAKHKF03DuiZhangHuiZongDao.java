package com.shuidihuzhu.cf.dao.duizhang;

import com.shuidihuzhu.client.baseservice.pay.model.pingan.duizhang.PADuizhangHuiZong;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * Created by ch<PERSON> on 2017/10/26.
 */
@DataSource(DS.PAY)
public interface AdminPAKHKF03DuiZhangHuiZongDao {
	void add(PADuizhangHuiZong huiZong);

	void delete(@Param("fileDate") String fileDate);

	PADuizhangHuiZong get(@Param("fileDate") String fileDate);
}
