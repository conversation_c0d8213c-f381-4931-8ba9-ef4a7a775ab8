package com.shuidihuzhu.cf.dao.admin;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.model.label.CfCaseLabelInfo;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/8/9 3:19 PM
 */
@DataSource(CfDataSource.ADMIN_DB_MASTER)
public interface CaseLabelDao {

    int addCaseLabel(CfCaseLabelInfo cfCaseLabelInfo);

    int updateCaseLabel(CfCaseLabelInfo cfCaseLabelInfo);

    @DataSource(CfDataSource.ADMIN_DB_SLAVE)
    List<CfCaseLabelInfo> queryCaseLabels(@Param("name") String name, @Param("status") Integer status, @Param("offset") Integer offset, @Param("limit") Integer limit);

    @DataSource(CfDataSource.ADMIN_DB_SLAVE)
    CfCaseLabelInfo queryCaseLabelById(@Param("id") Long id);

    @DataSource(CfDataSource.ADMIN_DB_SLAVE)
    int queryCountByPriority(@Param("id") Long id, @Param("priority") Integer priority, @Param("status") Integer status);

    @DataSource(CfDataSource.ADMIN_DB_SLAVE)
    int queryCaseLabelsCount(@Param("name") String name, @Param("status") Integer status);

    @DataSource(CfDataSource.ADMIN_DB_SLAVE)
    List<CfCaseLabelInfo> queryEnableCaseLabels();

    @DataSource(CfDataSource.ADMIN_DB_SLAVE)
    List<CfCaseLabelInfo> queryAllCaseLabels();

    @DataSource(CfDataSource.ADMIN_DB_SLAVE)
    CfCaseLabelInfo getCaseLabelByName(@Param("name") String name);

    @DataSource(CfDataSource.ADMIN_DB_SLAVE)
    CfCaseLabelInfo getRepeatCaseLabelByName(@Param("id") Long id, @Param("name") String name);

    @DataSource(CfDataSource.ADMIN_DB_SLAVE)
    List<CfCaseLabelInfo> getCaseLabelByNameList(@Param("nameList") List<String> nameList);

    int updateStatusById(@Param("id") Long id, @Param("status") Integer status, @Param("userId") Long userId);

}
