package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CfTagGroup;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by ahrievil on 2017/3/27.
 */
@DataSource(DS.CF)
public interface CfTagGroupDao {
    List<CfTagGroup> getList(@Param("types") List<Integer> types);
    CfTagGroup getById(@Param("pid") int pid);
}
