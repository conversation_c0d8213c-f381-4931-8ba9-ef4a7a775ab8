package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.report.CfReportOfficialLetter;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-05-21 14:43
 **/
@DataSource(DS.CF)
public interface CfReportOfficialLetterDao {

    int insertOne(CfReportOfficialLetter cfReportOfficialLetter);

    @DataSource(DS.CF_SLAVE_2)
    List<CfReportOfficialLetter> getByCaseId(@Param("caseId") int caseId);

    int update(CfReportOfficialLetter cfReportOfficialLetter);

    @DataSource(DS.CF_SLAVE_2)
    CfReportOfficialLetter getById(@Param("id") long id);

    @DataSource(DS.CF_SLAVE_2)
    List<CfReportOfficialLetter> getLastByCaseId(@Param("caseId")int caseId);

}
