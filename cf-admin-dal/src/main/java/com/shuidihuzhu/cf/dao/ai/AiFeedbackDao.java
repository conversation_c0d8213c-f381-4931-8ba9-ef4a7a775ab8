package com.shuidihuzhu.cf.dao.ai;

/**
 * @Description:
 * @Author: pan<PERSON><PERSON><PERSON>
 * @Date: 2024/5/30 11:37 AM
 */

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.domain.risk.AiUgcRiskCheckRecordDO;
import com.shuidihuzhu.cf.model.ai.DO.FeedbackDO;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
@DataSource(AdminDS.CF_ADMIN_RW)
public interface AiFeedbackDao {

    int insert(FeedbackDO feedbackDO);

}
