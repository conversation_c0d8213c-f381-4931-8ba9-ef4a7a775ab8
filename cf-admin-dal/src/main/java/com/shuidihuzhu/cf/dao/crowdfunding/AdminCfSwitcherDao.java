package com.shuidihuzhu.cf.dao.crowdfunding;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CfSwitcher;

/**
 * 
 * Created by lixuan on 2017/04/14.
 *
 */
@DataSource(DS.CF)
public interface AdminCfSwitcherDao {

	List<CfSwitcher> getAll();

	int add(CfSwitcher cfSwitcher);

	CfSwitcher getByName(String name);

	int updateValue(@Param("id") int id, @Param("value") int value);
}
