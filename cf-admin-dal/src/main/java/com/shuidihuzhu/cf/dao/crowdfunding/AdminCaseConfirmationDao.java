package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfCaseEndWhiteList;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface AdminCaseConfirmationDao {

    @DataSource(DS.CF)
    int insert(@Param("caseId") long caseId, @Param("userId") int userId);

    List<CfCaseEndWhiteList> selectWhiteList(@Param("caseId") int caseId);

    CfCaseEndWhiteList selectByCaseId(@Param("caseId") long caseId);

}
