package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.crowdfunding.report.AdminCfReportRiskTagLabel;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2022/5/25 18:15
 * @Description:
 */
@DataSource(AdminDS.CF_RW)
public interface AdminCfReportRiskTagLabelDao {

    int insert(AdminCfReportRiskTagLabel adminCfReportRiskTagLabel);

    List<AdminCfReportRiskTagLabel> getByCaseId(@Param("caseId") int caseId);
}
