package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfFirstApproveOperator;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

@DataSource(DS.CF)
public interface CfFirstApproveOperatorDao {
    CfFirstApproveOperator getCfFirstApproveOperatorCountById(int operatorId);
    void insertCfFirstApproveOperator(int operatorId, int count);
    void updateCfFirstApproveOperatorCount(int operatorId, int count);
}
