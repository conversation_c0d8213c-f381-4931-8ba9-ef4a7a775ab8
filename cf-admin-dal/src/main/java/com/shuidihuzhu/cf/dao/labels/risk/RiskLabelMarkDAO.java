package com.shuidihuzhu.cf.dao.labels.risk;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.domain.label.core.LabelDO;
import com.shuidihuzhu.cf.domain.label.risk.RiskLabelMarkRecordDO;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@DataSource(AdminDS.CF_RW)
public interface RiskLabelMarkDAO {

    int add(RiskLabelMarkRecordDO markData);

    RiskLabelMarkRecordDO getNewByCaseId(@Param("caseId") int caseId);

    List<RiskLabelMarkRecordDO> getAllByCaseId(@Param("caseId") int caseId);

    List<RiskLabelMarkRecordDO> getAllByCaseIdAndMarkType(@Param("caseId") int caseId, @Param("markType") int markType);

    RiskLabelMarkRecordDO getLastByCaseIdAndMarkType(@Param("caseId") int caseId, @Param("markType") int markType);
}
