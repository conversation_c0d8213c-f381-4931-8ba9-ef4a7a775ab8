package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfDiseaseClassifyDO;
import com.shuidihuzhu.cf.model.crowdfunding.CfDiseaseManagerDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2019-11-06 22:05
 **/
@DataSource(DS.CF)
public interface CfDiseaseClassifyDao {

    int insert(CfDiseaseClassifyDO classifyDO);

    List<CfDiseaseClassifyDO> listAllByPage(long id, int limit);
}
