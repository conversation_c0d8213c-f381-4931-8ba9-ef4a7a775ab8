package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfCredibleInfoWorkOrderDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2022/7/22 15:53
 * @Description:
 */
@DataSource(DS.CF)
public interface AdminCredibleInfoWorkOrderDAO {
    int insert(CfCredibleInfoWorkOrderDO cfCredibleInfoWorkOrderDO);

    List<CfCredibleInfoWorkOrderDO> queryByCaseId(@Param("caseId") int caseId);



}
