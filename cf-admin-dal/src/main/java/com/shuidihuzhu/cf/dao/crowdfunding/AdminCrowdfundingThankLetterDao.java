package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingThankLetter;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/23.
 */
@DataSource(DS.CF)
public interface AdminCrowdfundingThankLetterDao {
    List<CrowdfundingThankLetter> getThankLetterByTime(@Param("startTime") String startTime, @Param("endTime") String endTime);

    int updateThankLetter(CrowdfundingThankLetter crowdfundingThankLetter);
}
