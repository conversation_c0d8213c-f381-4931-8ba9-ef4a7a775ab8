package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterials;
import com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterialsResult;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2020/12/17
 */
@DataSource("shuidiCfAdminDataSource")
public interface CfAiMaterialsDao {


    int insert(List<CfAiMaterials> aiMaterials);

    List<CfAiMaterials> getByWorkOrderId(@Param("workOrderId") long workOrderId);

    CfAiMaterials getByCaseId(@Param("caseId") int caseId,@Param("materialsType") int materialsType);

    CfAiMaterials getByCaseIdAndDate(@Param("caseId") int caseId,@Param("materialsType") int materialsType,@Param("time") String time);


    List<CfAiMaterials> getAiMaterials(@Param("workOrderId") long workOrderId,@Param("materialsType") int materialsType);


    int saveResult(CfAiMaterialsResult materialsResult);


    CfAiMaterialsResult getResultByCaseId(@Param("caseId") int caseId);


}
