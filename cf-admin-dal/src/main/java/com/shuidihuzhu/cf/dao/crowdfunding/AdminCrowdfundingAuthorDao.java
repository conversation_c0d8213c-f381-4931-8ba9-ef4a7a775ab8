package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * Created by Ahrievil on 2017/8/23
 */
@DataSource(AdminDS.CF_RW)
public interface AdminCrowdfundingAuthorDao {


    CrowdfundingAuthor get(int crowdfundingId);


    List<CrowdfundingAuthor> getByName(@Param("name") String name);

    int add(CrowdfundingAuthor crowdfundingAuthor);

    int update(CrowdfundingAuthor crowdfundingAuthor);


    List<CrowdfundingAuthor> getByInfoIdList(@Param("infoIdList") List<Integer> infoIdList);


    String selectNameByCfId(int crowdfundingId);

    @DataSource(DS.CF)
    int getApplyCount(@Param("name") String name, @Param("startTime") Date startTime, @Param("endTime") Date endTime);


    List<CrowdfundingAuthor> selectByCaseIdList(@Param("list") List<Integer> list);


    List<CrowdfundingAuthor> selectByNameList(@Param("list") List<String> nameList);



    List<CrowdfundingAuthor> selectByIdCardList(@Param("idCardList") List<String> idCardList);
}
