package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CfFakeShareRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by Ahrievil on 2017/8/31
 */
@DataSource(DS.CF)
public interface CfFakeShareRecordDao {
    int insert(CfFakeShareRecord cfFakeShareRecord);
    int insertList(@Param("list") List<CfFakeShareRecord> list);
    @DataSource(DS.CF_SLAVE_2)
    List<CfFakeShareRecord> selectByLimit(@Param("start") int start, @Param("size") int size);
}
