package com.shuidihuzhu.cf.dao.stat;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CfStatUserWide;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by ahrievil on 2017/7/3.
 */
@DataSource("statCfSlaveDataSource")
public interface AdminCfStatUserWideDao {

    List<CfStatUserWide> selectUserIdByChannelGroup(@Param("channelGroup") String channelGroup, @Param("isFollow") int isFollow, @Param("start") int start, @Param("size") int size);

}
