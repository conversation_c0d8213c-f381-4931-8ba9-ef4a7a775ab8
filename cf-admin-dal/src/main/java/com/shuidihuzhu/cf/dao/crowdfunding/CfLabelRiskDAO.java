package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfLabelRiskDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @time 2020/1/12 上午11:21
 * @desc
 */
@DataSource(DS.CF)
public interface CfLabelRiskDAO {
    int insert(CfLabelRiskDO labelRiskDO);
    int update(CfLabelRiskDO labelRiskDO);
    CfLabelRiskDO query(@Param("caseId") int caseId);
}
