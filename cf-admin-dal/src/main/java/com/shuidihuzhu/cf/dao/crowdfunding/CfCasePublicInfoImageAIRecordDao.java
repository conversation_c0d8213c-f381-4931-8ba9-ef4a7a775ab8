package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.client.adminpure.model.caseinfo.CfCasePublicInfo;
import com.shuidihuzhu.cf.client.adminpure.model.caseinfo.CfCasePublicInfoImageAIRecord;
import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * @Author: wangpeng
 * @Date: 2021/11/2 15:02
 * @Description:
 */
@DataSource(AdminDS.CF_RW)
public interface CfCasePublicInfoImageAIRecordDao {

    int add(CfCasePublicInfoImageAIRecord cfCasePublicInfoImageAIRecord);

    CfCasePublicInfoImageAIRecord getById(@Param("id") long id);

    int update(CfCasePublicInfoImageAIRecord cfCasePublicInfoImageAIRecord);

}
