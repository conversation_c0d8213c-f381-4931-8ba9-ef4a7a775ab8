package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;
import org.apache.kafka.common.metrics.internals.IntGaugeSuite;

import java.util.List;
import java.util.Map;

@DataSource(DS.CF)
public interface CfRefuseReasonEntityVersionMappingDao {
    int insertOne(@Param("entityId") int entityId, @Param("planId") int planId);

    int deleteOne(@Param("id") int id);

    int updateOne(@Param("id") int id, @Param("planId") int planId, @Param("entityId") int entityId);
    List<Integer> getEntityIdsByMaterialPlanId(@Param("planId") int planId);

    List<Map<String, Object>> getMaterialPlanId(@Param("entityIds") List<Integer> entityIds);

    int batchInsert(@Param("mapping") Map<Integer, Integer> mapping);
}
