package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@DataSource(DS.CF)
public interface AdminCfInfoExtDao {

	int updateDealWithStatus(@Param("infoUuid") String infoUuid, @Param("status") int status);

	int updateUserRefund(@Param("infoUuid") String infoUuid, @Param("refundEndTime") String refundEndTime);

	int updateCryptoRegisterMobile(@Param("infoUuid") String infoUuid, @Param("cryptoRegisterMobile") String cryptoRegisterMobile);

	int insertList(@Param("list") List<String> list);

    @DataSource(DS.CF_SLAVE_2)
    List<CfInfoExt> selectByInfoUuidList(@Param("list") List<String> list);

	int updateSuggestStop(@Param("infoUuid") String infoUuid, @Param("suggestStop") int suggestStop);

	int updateTransferStatusAndFinishStatus(@Param("infoUuid") String infoUuid);

	CfInfoExt selectByInfoUuidFromMaster(@Param("infoUuid") String infoUuid);

	List<CfInfoExt> getListByUuids(@Param("infoUuids") List<String> infoUuids);
	CfInfoExt getByInfoUuid(@Param("infoUuid") String infoUuid);
	int updateFromType(@Param("infoUuid") String infoUuid, @Param("fromType") int fromType);
	int updateFirstApproveStatus(@Param("infoUuid") String infoUuid, @Param("firstApproveStatus") int status,
								 @Param("time") Date time);
	int updateFirstApproveTime(@Param("infoUuid") String infoUuid);
	void updateFinishStr(@Param("caseUuid") String caseUuid, @Param("finishStr")String finishStr);

	CfInfoExt getByCaseId(@Param("caseId") int caseId);

	int updateFinishStatus(@Param("infoUuid") String infoUuid, @Param("finishStatus") int finishStatus);

	@DataSource(DS.CF_SLAVE_2)
	List<CfInfoExt> getListByCaseIds(@Param("caseIds") List<Integer> caseIds);


	int updateFinishStatusByCaseId(@Param("caseId") int caseId, @Param("finishStatus") int finishStatus);

	int updateNoHandlingFeeByInfoUuid(@Param("infoUuid") String infoUuid, @Param("noHandlingFee") int noHandlingFee);

}
