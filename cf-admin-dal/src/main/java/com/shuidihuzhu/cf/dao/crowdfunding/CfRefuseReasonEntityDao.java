package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.materialAudit.CfRefuseModifyVo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * Created by Ahrievil on 2017/7/26
 */
@DataSource(DS.CF)
public interface CfRefuseReasonEntityDao {
    int insertOne(CfRefuseReasonEntity cfRefuseReasonEntity);
    List<CfRefuseReasonEntity> selectByIds(@Param("list") List<Integer> list);
    int frequencyPlusOne(@Param("set") Set set);
    List<CfRefuseReasonEntity> selectAll(@Param("start") int start, @Param("size") int size);
    int deleteOne(@Param("id") int id);

    @DataSource(DS.CF_SLAVE)
    CfRefuseReasonEntity selectByIdAndDeleteStatus(@Param("id") int id, @Param("isDelete")Integer isDelete);
    @DataSource(DS.CF_SLAVE)
    List<CfRefuseReasonEntity> selectByReasonIds(@Param("set") Set<Integer> set, @Param("deleteStatus")Integer deleteStatus);

    int updateDeleteStatus(@Param("id")int id, @Param("deleteStatus")int deleteStatus);
    @DataSource(DS.CF_SLAVE)
    List<CfRefuseReasonEntity> selectByTagIdAndDeleteStatus(@Param("tagId")int tagId, @Param("deleteStatus")int deleteStatus);

    int updateStateById(@Param("id") int id,
                        @Param("deleteStatus")Integer deleteStatus,
                        @Param("choiceRelationIds") String choiceRelationIds);

    @DataSource(DS.CF_SLAVE)
    List<CfRefuseReasonEntity> selectAllValidEntitys();

    int updateItemAndSuggestModify(CfRefuseModifyVo modifyVo);
}
