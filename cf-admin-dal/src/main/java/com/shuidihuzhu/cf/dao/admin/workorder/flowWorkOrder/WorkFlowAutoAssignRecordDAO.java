package com.shuidihuzhu.cf.dao.admin.workorder.flowWorkOrder;


import com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowAutoAssignRecord;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@DataSource(DS.CF)
public interface WorkFlowAutoAssignRecordDAO {

    Integer insertOrUpdateAssigns(@Param("list") List<WorkFlowAutoAssignRecord> records);

    List<WorkFlowAutoAssignRecord> selectByUserIdsAndDate(@Param("userIds") Collection<Integer> userIds,
                                                          @Param("assignDate") String assignDate);
}
