package com.shuidihuzhu.cf.dao.workorder;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.domain.workorder.WorkOrderRemarkDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-04-30  11:50
 */
@DataSource(AdminDS.CF_RW)
public interface WorkOrderRemarkDAO {

    int insert (WorkOrderRemarkDO workOrderFlowRemark);

    @DataSource(DS.CF)
    List<WorkOrderRemarkDO> listByCaseId(int caseId);
}
