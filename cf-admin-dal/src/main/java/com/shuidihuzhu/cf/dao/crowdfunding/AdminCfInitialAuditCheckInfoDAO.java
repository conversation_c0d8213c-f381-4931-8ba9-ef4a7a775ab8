package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.domain.cf.AdminCfInitialAuditCheckInfoDO;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

import java.util.List;

/**
 * <AUTHOR>
 */
@DataSource(AdminDS.CF_ADMIN_RW)
public interface AdminCfInitialAuditCheckInfoDAO {
    int add(AdminCfInitialAuditCheckInfoDO infoDO);

    int update(AdminCfInitialAuditCheckInfoDO infoDO);


    AdminCfInitialAuditCheckInfoDO getByCaseIdAndCheckType(Integer caseId, Integer checkType);


    List<AdminCfInitialAuditCheckInfoDO> listByCaseIdAndCheckType(Integer caseId);
}
