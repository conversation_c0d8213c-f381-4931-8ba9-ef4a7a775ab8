package com.shuidihuzhu.cf.dao.admin.workorder;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.domain.cf.WorkOrderExt;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-12-06  15:02
 */
@DataSource(AdminDS.CF_RW)
public interface AdminWorkOrderExtDAO {


    WorkOrderExt get(@Param("workOrderId") long workOrderId, @Param("contentType")int contentType);


    List<WorkOrderExt> listByCaseIdAndType(@Param("caseId") int caseId, @Param("contentType")int contentType);

    int save(WorkOrderExt workOrderExt);


    WorkOrderExt listNearlyByCaseIdAndType(@Param("caseId") int caseId, @Param("contentType")int contentType);
}
