package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderCaseRecord;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> Ahrievil
 */
@DataSource(DS.CF)
public interface AdminWorkOrderCaseRecordDao {
    int insertOne(AdminWorkOrderCaseRecord adminWorkOrderCaseRecord);
    int insertList(@Param("list") List<AdminWorkOrderCaseRecord> adminWorkOrderCaseRecords );
    List<AdminWorkOrderCaseRecord> selectByWorkOrderId(@Param("workOrderId") int workOrderId);
}
