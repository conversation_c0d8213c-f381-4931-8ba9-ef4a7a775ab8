package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.report.CfReportProveDisposeAction;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/10/23.
 */
@DataSource(DS.CF)
public interface AdminCfReportProveDisposeActionDao {

    int add(CfReportProveDisposeAction cfReportProveDisposeAction);

    @DataSource(DS.CF_SLAVE_2)
    String getDisposeAction(@Param("trustId") long trustId);

    @DataSource(DS.CF_SLAVE_2)
    CfReportProveDisposeAction findByInfoUuid(@Param("infoUuid")String infoUuid);
}
