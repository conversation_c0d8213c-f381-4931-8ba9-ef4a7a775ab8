package com.shuidihuzhu.cf.dao.customer;

import com.shuidihuzhu.cf.customer.CfChatRecordDO;
import com.shuidihuzhu.cf.customer.CfUdeskSessionRecordVo;
import com.shuidihuzhu.cf.model.CrmUserManage.UserManage;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;

@DataSource("statCfSlaveDataSource")
public interface CfUdeskSessionRecordDao {

    List<CfUdeskSessionRecordVo> getUdeskSessionsByUdeskUserIds(@Param("udeskUserIds") Set<Integer> udeskUserIds);

    List<CfChatRecordDO> listRecordByUdskIds(@Param("udeskUserIds") Set<Integer> udeskUserIds);

    String getNickNameBySubSessionId(@Param("subSessionId") int subSessionId);

    int selectByCustomIdAndCreateAt(@Param("customIds") List<Integer> customIds, @Param("createdAt") Date createdAt);

    UserManage.UdeskBizRecord selectLastRecordByCustomId(@Param("customIds") List<Integer> customIds);
}
