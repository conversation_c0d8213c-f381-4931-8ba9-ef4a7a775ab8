package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportFollowComment;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/9/12.
 */
@DataSource(AdminDS.CF_RW)
public interface CfReportFollowCommentDao {


    List<CfReportFollowComment> getCommentListByInfoId(@Param("infoId")int infoId);


    List<CfReportFollowComment> getCommentListByInfoIds(@Param("infoIds")List<Integer> infoIds);

    void save(CfReportFollowComment cfReportFollowComment);


    List<CfReportFollowComment> descFindByInfoId(@Param("infoId")int infoId);
}
