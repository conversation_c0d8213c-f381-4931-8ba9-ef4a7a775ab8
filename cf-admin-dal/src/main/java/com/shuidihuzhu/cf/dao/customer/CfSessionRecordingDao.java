package com.shuidihuzhu.cf.dao.customer;

import com.shuidihuzhu.cf.customer.CfChatRecordDO;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource("statCfSlaveDataSource")
public interface CfSessionRecordingDao {

    List<String> getCidsByPartnerIds(@Param("partnerIds") List<String> partnerIds);

    List<String> getCidsByPhoneNumber(@Param("phoneNumber") String phoneNumber);

    List<CfChatRecordDO> queryRecordByUserId(@Param("partnerId") String partnerId);
}
