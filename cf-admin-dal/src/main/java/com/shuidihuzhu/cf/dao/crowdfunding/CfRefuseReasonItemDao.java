package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonItem;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * Created by ahrievil on 2017/1/17.
 */
@DataSource(DS.CF)
public interface CfRefuseReasonItemDao {

	List<CfRefuseReasonItem> getRefuseItem();

	CfRefuseReasonItem getContentById(int id);

	List<CfRefuseReasonItem> selectSubGroupByType();

    List<CfRefuseReasonItem> selectByIds(@Param("set") Collection<Integer> set);

    List<CfRefuseReasonItem> selectByType(@Param("type") int type);

    int insertReasonItem(CfRefuseReasonItem reasonItem);

    List<CfRefuseReasonItem> selectByContent(@Param("content") String  content);


}