package com.shuidihuzhu.cf.dao.duizhang;

import com.shuidihuzhu.client.baseservice.pay.model.pingan.duizhang.PADuiZhangDetail;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by ch<PERSON> on 2017/10/26.
 */
@DataSource(DS.PAY)
public interface AdminPAKHKF03DuiZhangDetailDao {
	void add(@Param("details") List<PADuiZhangDetail> details);

	void delete(@Param("fileDate") String fileDate);

	List<PADuiZhangDetail> getByFileDate(@Param("fileDate") String fileDate,
	                                     @Param("offset") int offset,
	                                     @Param("limit") int limit);

	List<PADuiZhangDetail> getByTradeNos(@Param("tradeNos") List<String> tradeNos);
}
