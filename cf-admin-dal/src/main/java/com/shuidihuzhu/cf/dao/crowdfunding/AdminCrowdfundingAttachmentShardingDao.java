package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(AdminDS.CF_ATTACHMENT_SHARDING)
public interface AdminCrowdfundingAttachmentShardingDao {
    List<CrowdfundingAttachment> getAttachmentsByParentIdAndType(@Param("parentId") int parentId, @Param("type") int type);

    @DataSource(DS.CF)
    int deleteByIds(@Param("ids") List<Integer> ids, @Param("parentId") int parentId, @Param("sharding") String sharding);

    CrowdfundingAttachment getAttachmentById(@Param("id") Integer id, @Param("parentId") int parentId);

    List<CrowdfundingAttachment> getFundingAttachment(int parentId);
    List<CrowdfundingAttachment> queryAttachment(int parentId);

    List<CrowdfundingAttachment> getAttachmentsByType(@Param("parentId") int parentId,
                                                      @Param("type") AttachmentTypeEnum type);

    List<CrowdfundingAttachment> getListByInfoIdListAndType(@Param("parentIdList") List<Integer> parentIdList,
                                                            @Param("type") AttachmentTypeEnum type);
    List<CrowdfundingAttachment> getAttachmentsByTypes(@Param("parentId") int parentId,
                                                       @Param("types") List<Integer> types);


    List<CrowdfundingAttachment> getAttachmentsByCaseIdsWithDelete(@Param("parentIdList") List<Integer> parentIdList,
                                                                   @Param("types") List<Integer> types);
    List<CrowdfundingAttachment> getAttachmentsByIdList(@Param("parentId") int parentId, @Param("idList") List<Integer> idList);

}
