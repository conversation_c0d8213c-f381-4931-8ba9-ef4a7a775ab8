package com.shuidihuzhu.cf.dao.crowdfunding;

import java.util.List;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import com.shuidihuzhu.cf.model.admin.CfAndroidChannel;

@DataSource(DS.CF)
public interface CfAndroidChannelDao {
	int insertList(@Param("list") List<CfAndroidChannel> list);

	CfAndroidChannel selectOne(@Param("selectDate") String selectDate, @Param("channelName") String channelName);

	int deleteData();
}
