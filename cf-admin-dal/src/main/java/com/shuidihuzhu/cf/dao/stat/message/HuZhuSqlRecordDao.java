package com.shuidihuzhu.cf.dao.stat.message;

import com.shuidihuzhu.cf.model.message.HuZhuSqlRecord;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@DataSource("statCfDataSource")
public interface HuZhuSqlRecordDao {

    int insertHuZhuSqlRecord(HuZhuSqlRecord huZhuSqlRecord);

    @DataSource("statCfSlaveDataSource")
    HuZhuSqlRecord queryHuZhuSqlRecordBySqlId(int sqlId);

    int deleteHuZhuSqlRecord(int id);

    @DataSource("statCfSlaveDataSource")
    List<HuZhuSqlRecord> queryBySqlIdSet(@Param("sqlIdSet") Set<Integer> sqlIdSet);
}
