package com.shuidihuzhu.cf.dao.call;

import com.shuidihuzhu.cf.call.CallOutModel;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface CallOutRecordDao {
    List<CallOutModel> getRecordsByDayTimeAndCustomerPhoneNum(@Param("startDay") String startDay, @Param("endDay") String endDay, @Param("encryptoCustomerPhoneNumber") String encryptoCustomerPhoneNumber);

    CallOutModel getCallOutRecordById(@Param("callRecordId") int callRecordId);

    List<CallOutModel> getAllByPhoneNum(@Param("customerPhoneNumber") String customerPhoneNumber);

    int insertCallOutModel(CallOutModel callOutModel);
}
