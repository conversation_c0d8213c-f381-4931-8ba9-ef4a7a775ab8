package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportAddTrust;
import com.shuidihuzhu.cf.model.report.CfReportActionClassify;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/10/23.
 */
@DataSource(AdminDS.CF_RW)
public interface AdminCfReportActionClassifyDao {

    int add(@Param("actionClassify") String actionClassify);

    int updateActionClassify(@Param("actionClassify") String actionClassify, @Param("id") long id);

    int updateIsUse(@Param("isUse") int isUse, @Param("id") long id);


    List<CfReportActionClassify> getAll();



    List<CfReportActionClassify> getByIds(@Param("ids") List<Long> ids);


    List<CfReportActionClassify>  getByIdsAndIsUse(@Param("ids") List<Long> ids, @Param("isUse") int isUse);


    List<CfReportActionClassify> getByUse(@Param("isUse") int isUse);

   
    CfReportActionClassify getById(@Param("id") long id);
}
