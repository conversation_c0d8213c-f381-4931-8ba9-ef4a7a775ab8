package com.shuidihuzhu.cf.dao.admin;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.domain.cf.CfScheduleAlarm;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@DataSource(AdminDS.CF_RW)
public interface CfScheduleAlarmDao {

    List<CfScheduleAlarm> getAll();


    List<CfScheduleAlarm> getAllByAnchor(@Param("anchor") long anchor,
                                         @Param("pageSize") int pageSize,
                                         @Param("isPre") boolean isPre);


    List<CfScheduleAlarm> getListByAnchor(@Param("title") String title,
                                          @Param("description") String description,
                                          @Param("crontab") String crontab,
                                          @Param("robotKey") String robotKey,
                                          @Param("contentType") Integer contentType,
                                          @Param("dataSource") String dataSource,
                                          @Param("anchor") long anchor,
                                          @Param("pageSize") int pageSize,
                                          @Param("isPre") boolean isPre,
                                          @Param("feishuRobotKey") String feishuRobotKey,
                                          @Param("signSet") Set<String> signSet);

    int insert(CfScheduleAlarm cfScheduleAlarm);

    int delete(@Param("id") long id, @Param("operatorId") long operatorId);

    int update(CfScheduleAlarm cfScheduleAlarm);

    @DataSource(DS.CF)
    CfScheduleAlarm getById(@Param("id") long id);

    @DataSource(DS.CF)
    List<CfScheduleAlarm> getByTitle(@Param("title") String title);

}
