package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.report.CfReportOfficialLetterLog;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-05-21 14:43
 **/
@DataSource(DS.CF)
public interface CfReportOfficialLetterLogDao {

    int insertOne(CfReportOfficialLetterLog cfReportOfficialLetterLog);

    List<CfReportOfficialLetterLog> getByLetterId(@Param("letterId") long letterId);

}
