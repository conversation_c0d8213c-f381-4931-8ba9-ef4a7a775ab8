package com.shuidihuzhu.cf.dao.tdsql;

import com.shuidihuzhu.cf.model.admin.CfQuestionnaire;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource("shuidiCfTdDataSource")
public interface TdCfQuestionnaireDao {

    List<CfQuestionnaire> getList(@Param("qid") String qid,
                                  @Param("userId") long userId,
                                  @Param("qname") String qname,
                                  @Param("name") String name,
                                  @Param("channel") String channel,
                                  @Param("source") String source,
                                  @Param("status") int status,
                                  @Param("encryptMobile") String encryptMobile,
                                  @Param("startTime") String startTime,
                                  @Param("endTime") String endTime,
                                  @Param("pageSize") int pageSize,
                                  @Param("isPre") boolean isPre,
                                  @Param("anchor") long anchor,
                                  @Param("recordId") long recordId,
                                  @Param("cfQid") long cfQid,
                                  @Param("size") int size);

    int total(@Param("qid") String qid,
              @Param("userId") long userId,
              @Param("qname") String qname,
              @Param("name") String name,
              @Param("channel") String channel,
              @Param("source") String source,
              @Param("status") int status,
              @Param("encryptMobile") String encryptMobile,
              @Param("startTime") String startTime,
              @Param("endTime") String endTime,
              @Param("recordId") long recordId);
}
