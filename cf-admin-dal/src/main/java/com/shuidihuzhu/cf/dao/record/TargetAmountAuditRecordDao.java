package com.shuidihuzhu.cf.dao.record;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.record.TargetAmountAuditRecord;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.common.datasource.DS;

import java.util.List;

@DataSource(AdminDS.CF_ADMIN_RW)
public interface TargetAmountAuditRecordDao {
    int add(TargetAmountAuditRecord record);

    TargetAmountAuditRecord getRecordByWorkOrderId(long workerOrderId);

    List<TargetAmountAuditRecord> getRecordByCaseId(long caseId);

    int deleteRecordByWorkOrderIdLogically(long workOrderId);
}
