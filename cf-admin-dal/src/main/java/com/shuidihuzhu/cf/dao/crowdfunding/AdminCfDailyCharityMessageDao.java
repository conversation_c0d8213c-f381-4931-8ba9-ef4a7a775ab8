package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CfDailyCharityMessage;
import com.shuidihuzhu.common.web.util.admin.BasicExample;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * Created by ahrievil on 2017/5/11.
 */
@DataSource(DS.CF)
public interface AdminCfDailyCharityMessageDao {
    int deleteByPrimaryKey(Integer id);

    int insert(CfDailyCharityMessage record);

    CfDailyCharityMessage selectByPrimaryKey(Integer id);

    CfDailyCharityMessage selectByStartDate(@Param("startDate") Date date);

    int updateByPrimaryKey(CfDailyCharityMessage record);

    List<CfDailyCharityMessage> selectByPage(BasicExample basicExample);
}
