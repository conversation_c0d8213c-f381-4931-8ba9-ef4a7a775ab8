package com.shuidihuzhu.cf.dao.photoai;

import com.shuidihuzhu.cf.model.admin.PhotoStatus;
import com.shuidihuzhu.cf.model.aiphoto.PhotoAiInfoModel;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * @Author: liujiawei
 * @Date: 2018/7/21  17:10
 */
@DataSource(DS.CF)
public interface CfPhotoAiDao {

    int savePhotoInfo(PhotoAiInfoModel photoAiInfoModel);

    Integer checkAIPhotoExist(@Param("infoId") Integer infoId,@Param("photoType") int photoType);

    Integer deletePhotoAiInfo(@Param("infoId") Integer infoId,@Param("photoType") int photoType);

    PhotoStatus selectPhotoStatus(@Param("infoId") Integer infoId,@Param("photoType") int photoType);

    int updateArtificialRes(@Param("infoId") Integer infoId, @Param("artificialRes") Integer artificialRes,@Param("photoType") int photoType);

}
