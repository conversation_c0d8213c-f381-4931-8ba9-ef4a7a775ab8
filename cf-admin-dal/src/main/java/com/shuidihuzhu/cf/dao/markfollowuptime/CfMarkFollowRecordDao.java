package com.shuidihuzhu.cf.dao.markfollowuptime;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.markfollowuptime.CfMarkFollowDo;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2018-12-27  14:08
 */
@DataSource(AdminDS.CF_RW)
public interface CfMarkFollowRecordDao {

    int add(@Param("markFollowId") long markFollowId ,@Param("operatorId") int operatorId,@Param("operationNotes")String operationNotes);

}
