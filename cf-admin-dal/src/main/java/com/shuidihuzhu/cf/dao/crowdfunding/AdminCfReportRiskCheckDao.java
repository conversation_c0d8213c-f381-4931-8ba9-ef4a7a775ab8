package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.crowdfunding.report.AdminCfReportRiskCheck;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2022/5/25 14:32
 * @Description:
 */
@DataSource(AdminDS.CF_RW)
public interface AdminCfReportRiskCheckDao {

    int insert(AdminCfReportRiskCheck adminCfReportRiskCheck);

    List<AdminCfReportRiskCheck> getByCaseId(@Param("caseId") int caseId);
}
