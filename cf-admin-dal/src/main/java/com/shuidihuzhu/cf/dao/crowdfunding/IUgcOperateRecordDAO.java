package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.AdminUgcOperateRecordDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/11/1 下午6:07
 * @desc
 */
@DataSource(DS.CF)
public interface IUgcOperateRecordDAO {
    int insert(AdminUgcOperateRecordDO ugcOperateRecordDO);
    List<AdminUgcOperateRecordDO> query(@Param("caseId") long caseId, @Param("bizType") int bizType, @Param("bizId") long bizId);
}
