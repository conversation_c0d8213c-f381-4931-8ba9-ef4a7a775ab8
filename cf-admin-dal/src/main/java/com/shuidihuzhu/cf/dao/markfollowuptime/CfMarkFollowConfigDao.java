package com.shuidihuzhu.cf.dao.markfollowuptime;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.report.schedule.ReportScheduleDO;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-12-27  14:08
 */
@DataSource(AdminDS.CF_RW)
public interface CfMarkFollowConfigDao {
    String getBizType(@Param("orderType") int orderType);
}
