package com.shuidihuzhu.cf.dao.kwai;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.kwai.KwaiAppShortIdeosCaseMountDo;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 */
@DataSource(AdminDS.CF_RW)
public interface KwaiAppShortIdeosCaseMountDao {
    int add(KwaiAppShortIdeosCaseMountDo kwaiAppShortIdeosCaseMountDo);

    int delete(@Param("id") long id);

    int deleteBatch(@Param("ids") List<Long> ids);

    List<KwaiAppShortIdeosCaseMountDo> getList(@Param("infoUuid") String infoUuid, @Param("encryptMobile") String encryptMobile);

    List<String> getInfoUuidList(@Param("encryptMobile") String encryptMobile);

}
