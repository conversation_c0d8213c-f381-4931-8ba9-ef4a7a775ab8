package com.shuidihuzhu.cf.dao.sensitive;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.common.datasource.DS;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2020/4/3
 */
@DataSource(AdminDS.CF_RW)
public interface SensitiveSpecialSymbolLogDao {

    int insert(@Param("bizId") long bizId,
               @Param("caseId") long caseId,
               @Param("contentType") int contentType,
               @Param("content") String content,
               @Param("hitWords") String hitWords);
}
