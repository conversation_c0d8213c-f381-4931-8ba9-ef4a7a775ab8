package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.report.CfReportDisposeAction;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/10/23.
 */
@DataSource(AdminDS.CF_RW)
public interface AdminCfReportDisposeActionDao {

    int add(CfReportDisposeAction cfReportDisposeAction);

    int updateDisponseAction(@Param("disposeAction") String disposeAction,
                             @Param("actionClassifyId") long actionClassifyId,
                             @Param("id") long id,
                             @Param("isHelp") boolean isHelp,
                             @Param("hasTemplate") boolean hasTemplate);

    int updateIsUse(@Param("isUse") int isUse, @Param("id") long id);


    List<CfReportDisposeAction> getAll();


    List<CfReportDisposeAction> getByActionClassifyId(@Param("actionClassifyId") long actionClassifyId);


    List<CfReportDisposeAction> getByUse(@Param("isUse") int isUse);


    List<CfReportDisposeAction> selectByIds(@Param("ids") List<Long> ids);

    CfReportDisposeAction getById(@Param("id") Long id);
}
