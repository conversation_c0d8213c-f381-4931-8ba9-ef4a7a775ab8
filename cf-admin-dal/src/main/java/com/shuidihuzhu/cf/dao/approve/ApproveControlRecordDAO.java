package com.shuidihuzhu.cf.dao.approve;

import com.shuidihuzhu.cf.domain.approve.ApproveControlRecordDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface ApproveControlRecordDAO {

    int insert(ApproveControlRecordDO approveControlRecordDO);

    /**
     * 根据案例id 处理状态 获取最新一条记录
     *
     * @param caseId
     * @param handleStatus
     * @return
     */
    ApproveControlRecordDO getNewestRecordByCaseIdAndHandleStatus(@Param("caseId") int caseId, @Param("handleStatus") int handleStatus);

    ApproveControlRecordDO getById(@Param("id") long id);

    ApproveControlRecordDO getByCaseIdAndWorkOrderId(@Param("caseId") int caseId, @Param("workOrderId") long workOrderId);

    int updateHandleStatusById(@Param("id") long id,
                               @Param("oldHandleStatus") int oldHandleStatus,
                               @Param("newHandleStatus") int newHandleStatus,
                               @Param("handleTime") Date handleTime);

}
