package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfCaseMsg;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @DATE 2020/10/19
 */
@DataSource(DS.CF)
public interface CfCaseMsgDao {

    int insert(CfCaseMsg cfCaseMsg);

    int update(CfCaseMsg CfCaseMsg);

    CfCaseMsg queryByCaeId(@Param("caseId") int caseId,
                           @Param("msgId") int msgId);
}
