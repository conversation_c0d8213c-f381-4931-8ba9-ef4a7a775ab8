package com.shuidihuzhu.cf.dao.questionnaire;

import com.shuidihuzhu.cf.model.questionnaire.WxQuestionnaire;
import com.shuidihuzhu.cf.vo.questionnaire.WxQuestionnaireAdminVo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.pay.common.model.PageRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: mhk
 * @Date: 2018/11/8  17:10
 */
@DataSource(DS.CF)
public interface WxQuestionnaireAdminDao {

    WxQuestionnaire getById(int id);

    int add(WxQuestionnaire wxQuestionnaire);

    int edit(WxQuestionnaire wxQuestionnaire);

    Integer checkeNameIsExists(@Param("name") String name);

    List<WxQuestionnaireAdminVo> getWxQuestionnairePage(
                                                        @Param("start") int pageNo,
                                                        @Param("size") int pageSize);

    List<WxQuestionnaireAdminVo> getWxQuestionnairePage(
            @Param("name") String name,
            @Param("title")  String title,
            @Param("tagName") String tagName,
            @Param("createBy") String createBy,
            @Param("pageRequest")  PageRequest pageRequest);
}
