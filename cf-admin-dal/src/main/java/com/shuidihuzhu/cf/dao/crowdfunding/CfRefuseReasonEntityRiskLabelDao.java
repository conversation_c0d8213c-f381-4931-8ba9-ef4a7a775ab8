package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntityRiskLabel;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2022/12/1 15:18
 * @Description:
 */
@DataSource(DS.CF)
public interface CfRefuseReasonEntityRiskLabelDao {

    int insert(CfRefuseReasonEntityRiskLabel cfRefuseReasonEntityRiskLabel);

    @DataSource(DS.CF_SLAVE)
    List<CfRefuseReasonEntityRiskLabel> selectByRefuseEntityIdList(@Param("refuseEntityIdList") List<Integer> refuseEntityIdList);

    @DataSource(DS.CF_SLAVE)
    List<CfRefuseReasonEntityRiskLabel> selectByRefuseEntityId(@Param("refuseEntityId") Integer refuseEntityId);

    @DataSource(DS.CF_SLAVE)
    List<CfRefuseReasonEntityRiskLabel> selectByRiskLabelId(@Param("riskLabelId") long riskLabelId);

    int deleteByReasonEntityId(@Param("refuseEntityId") Integer refuseEntityId);
}
