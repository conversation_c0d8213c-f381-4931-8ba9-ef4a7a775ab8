package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfBasePreMsg;
import com.shuidihuzhu.cf.model.crowdfunding.CfBasePreMsgRecord;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2019/10/14
 */
@DataSource(DS.CF)
public interface CfBasePreMsgDao {

    CfBasePreMsg getCfBasePreMsg(@Param("mobile") String mobile);

    int updateMsgStatusByMobile(@Param("mobile") String mobile,
                                @Param("status") int status,
                                @Param("caseId") int caseId,
                                @Param("uuid") String uuid);

    int insertRecord(CfBasePreMsgRecord record);

    List<CfBasePreMsgRecord> getRecords(@Param("msgId") long msgId);

    @DataSource(DS.CF_SLAVE)
    List<CfBasePreMsg.UseTemplateRecord> selectUseRecordByPreIds(@Param("preIds") List<Long> preIds);
    @DataSource(DS.CF_SLAVE)
    List<CfBasePreMsg> get1V1MsgList(@Param("mobile") String mobile,
                                     @Param("startTime") String startTime,
                                     @Param("endTime") String endTime);
}
