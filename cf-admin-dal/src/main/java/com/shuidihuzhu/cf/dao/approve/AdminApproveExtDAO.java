package com.shuidihuzhu.cf.dao.approve;

import com.shuidihuzhu.cf.domain.approve.AdminApproveExt;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2021/7/1 15:26
 * @Description:
 */
@DataSource("shuidiCfAdminDataSource")
public interface AdminApproveExtDAO {

    int insert(AdminApproveExt adminApproveExt);

    List<AdminApproveExt> listByApproveIdAndExtName(@Param("approveId") int approveId, @Param("extName") String extName);

    int updateExtValue(@Param("id") long id, @Param("extValue") String extValue);

    List<AdminApproveExt> getApproveExtList(@Param("approveIds") List<Integer> approveIds, @Param("extName") String extName);

}
