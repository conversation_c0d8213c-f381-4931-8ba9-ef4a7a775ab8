package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.domain.cf.AdminCfInitialAuditCheckInfoRefDO;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

import java.util.List;

/**
 * <AUTHOR>
 */
@DataSource(AdminDS.CF_ADMIN_RW)
public interface AdminCfInitialAuditCheckInfoRefDAO {

    int batchInsert(List<AdminCfInitialAuditCheckInfoRefDO> infoRefList);


    AdminCfInitialAuditCheckInfoRefDO getByRefNameAndType(String refName, Integer refType);
}
