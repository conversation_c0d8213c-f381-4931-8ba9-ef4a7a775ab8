package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CfCaseScene;
import org.apache.ibatis.annotations.Param;

/**
 * Created by wangsf on 17/4/24.
 */
@DataSource(DS.CF)
public interface AdminCfCaseSceneDao {

	int insertOrUpdate(CfCaseScene cfCaseScene);

	CfCaseScene getByInfoUuid(@Param("infoUuid") String infoUuid);

}
