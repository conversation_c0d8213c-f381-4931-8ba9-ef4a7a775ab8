package com.shuidihuzhu.cf.dao.sd.admin.channel;


import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.cf.model.admin.channel.CfQrcodeChannel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by wangsf on 17/2/16.
 */
@DataSource(DS.CF)
public interface CfQrcodeChannelMapDao {

	int add(CfQrcodeChannel cfQrcodeChannel);

	List<CfQrcodeChannel> listGroupIdsByQrcodeIds(@Param("ids") List<Integer> ids);

	int update(@Param("qrcodeId") int qrcodeId, @Param("channelId") int channelId, @Param("groupId") int groupId);

	CfQrcodeChannel getByQrcodeId(@Param("qrcodeId") int qrcodeId);
}
