package com.shuidihuzhu.cf.dao.crmUserManage;

import com.shuidihuzhu.cf.model.CrmUserManage.UserManage;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface UserManageLogDao {

    int addUserManageLogs(@Param("recordList") List<UserManage.UserManageLog> recordList);

}
