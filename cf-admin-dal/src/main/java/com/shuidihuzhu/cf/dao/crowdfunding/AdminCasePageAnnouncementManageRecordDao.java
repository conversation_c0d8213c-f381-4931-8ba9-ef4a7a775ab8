package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCasePageAnnouncementManageRecord;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/25  10:59 上午
 */
@DataSource(AdminDS.CF_RW)
public interface AdminCasePageAnnouncementManageRecordDao {

    int add(@Param("comment") String comment,
            @Param("operatorId") long operatorId,
            @Param("announcementId") long announcementId);

    List<AdminCasePageAnnouncementManageRecord> getList(@Param("announcementId") long announcementId);

}
