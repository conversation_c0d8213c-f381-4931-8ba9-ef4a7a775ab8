package com.shuidihuzhu.cf.dao.crowdfunding;


import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.crowdfunding.CfCapitalAccount;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoBlessing;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoStat;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@DataSource(AdminDS.CF_RW)
public interface AdminCrowdFundingFixDataDao {

    int countBlessByInfoId(@Param("infoUuid")String infoId);


    List<CrowdfundingInfo> selectCfByCreate(@Param("begin") Date begin, @Param("end")Date end);


    List<Integer> selectFirstApproveByCreateTime(@Param("begin")Date time, @Param("end")Date time1);

    @DataSource(DS.CF)
    List<CrowdfundingCity> selectCrowdfundingCityById(@Param("id") int id, @Param("size") int size);


    int insertCrowdfundingCity(CrowdfundingCity city);


    CfCapitalAccount getByInfoUuid(@Param("infoUuid") String infoUuid);


    CfInfoStat getCfInfoStatById(@Param("id") Integer id);

    int addCfInfoStat(CfInfoStat cfInfoStat);


    List<CfInfoBlessing> selectBlessByUserId(@Param("userId") long userId);

    @DataSource(DS.CF_SLAVE)
    Long selectByUserId(@Param("userId") long userId,
                               @Param("id") long id);

}
