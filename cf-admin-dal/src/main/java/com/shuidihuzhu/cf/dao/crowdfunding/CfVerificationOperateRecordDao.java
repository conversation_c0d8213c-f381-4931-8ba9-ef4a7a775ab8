package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.crowdfunding.CfVerificationOperateRecord;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 */
@DataSource(AdminDS.CF_RW)
public interface CfVerificationOperateRecordDao {
    int addCfVerificationOperateRecord(@Param("verificationId") int verificationId, @Param("content") String content, @Param("operatorId") int operatorId);

    List<CfVerificationOperateRecord> getCfVerificationOperateRecord(@Param("verificationId") int verificationId);
}
