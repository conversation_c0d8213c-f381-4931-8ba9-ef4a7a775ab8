package com.shuidihuzhu.cf.dao.ai;

import com.shuidihuzhu.cf.model.ai.CfApproveAuditAiCallRecord;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2022/7/12 11:44
 * @Description:
 */
@DataSource("shuidiCfAdminDataSource")
public interface CfApproveAuditAiCallRecordDao {

    int insert(CfApproveAuditAiCallRecord cfApproveAuditAiCallRecord);

    CfApproveAuditAiCallRecord getByCallId(@Param("callId") String callId);

    List<CfApproveAuditAiCallRecord> getByWorkOrderId(@Param("workOrderId") long workOrderId);

    int updateCallResCode(@Param("id") long id, @Param("callResCode") String callResCode);

    int updateCallIntention(@Param("id") long id, @Param("callIntention") String callIntention);

    int updateCallStartTime(@Param("id") long id, @Param("callStartTime") String callStartTime, @Param("callResult") int callResult);

    int updateCallEndTime(@Param("id") long id, @Param("callEndTime") String callEndTime, @Param("callResult") int callResult);


}
