package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.vo.crowdfunding.CrowdfundingfundingVerificationVo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by Ahrievil on 2017/12/3
 */
@DataSource(AdminDS.CF_RW)
public interface AdminCrowdFundingVerificationDao {

    List<CrowdfundingfundingVerificationVo> getVerify(@Param("crowdfundingInfoIds") List<String> crowdfundingInfoIds);

    int updateValid(@Param("valid") int valid, @Param("id") int id);
}
