package com.shuidihuzhu.cf.dao.labels.core;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.domain.label.core.LabelDO;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@DataSource(AdminDS.CF_RW)
public interface LabelDAO {

    int add(LabelDO labelDO);

    LabelDO getByUuid(@Param("uuid") String uuid);

    List<LabelDO> getLabelByIds(@Param("ids") List<Long> ids);

    List<LabelDO> getEnableChildListById(@Param("id") long id);

    List<LabelDO> getChildListByIdAndStatus(@Param("id") long id, @Param("labelStatusList") List<Integer> labelStatusList);

    LabelDO getById(@Param("id") long id);

    List<LabelDO> getLabelByUuidList(@Param("uuidList") List<String> uuidList);

    int updateSeqById(@Param("id") long id, @Param("sourceSeq") int sourceSeq, @Param("targetSeq") int targetSeq);

    int updateStatusByLabelId(@Param("id") long id,
                              @Param("sourceLabelStatus") int sourceLabelStatus,
                              @Param("targetLabelStatus") int targetLabelStatus);

    int updateLabelDesc(@Param("id") long id, @Param("labelDesc") String labelDesc);

    List<LabelDO> searchLabel(@Param("parentPathKey") String parentPathKey,
                              @Param("nameKey") String nameKey,
                              @Param("labelStatusList") List<Integer> labelStatusList,
                              @Param("riskLevel") Integer riskLevel);
}
