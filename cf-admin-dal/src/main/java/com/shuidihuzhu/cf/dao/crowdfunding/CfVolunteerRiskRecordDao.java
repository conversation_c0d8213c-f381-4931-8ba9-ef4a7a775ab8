package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.client.adminpure.model.initial.CfVolunteerRiskRecord;
import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2022/1/11 10:41
 * @Description:
 */
@DataSource(AdminDS.CF_RW)
public interface CfVolunteerRiskRecordDao {

    int insert(CfVolunteerRiskRecord cfVolunteerRiskRecord);

    List<CfVolunteerRiskRecord> getByCaseIdList(@Param("caseIdList") List<Integer> caseIdList);

    List<CfVolunteerRiskRecord> getByCaseId(@Param("caseId") int caseId);
}
