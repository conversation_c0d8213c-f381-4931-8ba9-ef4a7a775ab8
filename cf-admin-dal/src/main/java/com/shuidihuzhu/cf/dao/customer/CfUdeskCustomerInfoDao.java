package com.shuidihuzhu.cf.dao.customer;

import com.shuidihuzhu.cf.customer.CfUdeskCustomerInfo;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource("statCfSlaveDataSource")
public interface CfUdeskCustomerInfoDao {
    List<CfUdeskCustomerInfo> getUdeskCustomersByCfUserIds(@Param("cfUserIds") List<Long> cfUserIds);

    String getNickNameByUdeskUserId(@Param("udeskUserId") long userId);
}
