package com.shuidihuzhu.cf.dao.admin.workorder;

import com.shuidihuzhu.cf.model.admin.AdminWorkOrderMoneyBackExt;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 返还款相关信息工单表单DAO
 */
@DataSource(DS.CF)
public interface AdminWorkOrderMoneyBackExtDAO {

    /**
     * 保存数据
     * @param record 记录
     * @return 影响行数
     */
    int save(AdminWorkOrderMoneyBackExt record);

    /**
     * 批量保存数据
     * @param records 记录列表
     * @return 影响行数
     */
    int batchSave(@Param("list") List<AdminWorkOrderMoneyBackExt> records);

    /**
     * 根据工单ID查询
     * @param workOrderId 工单ID
     * @return 记录列表
     */
    List<AdminWorkOrderMoneyBackExt> findByWorkOrderId(@Param("workOrderId") Long workOrderId);

    /**
     * 根据案例ID查询
     * @param caseId 案例ID
     * @return 记录列表
     */
    List<AdminWorkOrderMoneyBackExt> findByCaseId(@Param("caseId") Integer caseId);

    /**
     * 根据工单ID和案例ID查询
     * @param workOrderId 工单ID
     * @param caseId 案例ID
     * @return 记录
     */
    AdminWorkOrderMoneyBackExt findByWorkOrderIdAndCaseId(@Param("workOrderId") Long workOrderId, @Param("caseId") Integer caseId);
} 