package com.shuidihuzhu.cf.dao.sd.admin.channel;

import com.shuidihuzhu.cf.model.admin.channel.CfChannel;
import org.apache.ibatis.annotations.Param;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.common.datasource.DS;

import java.util.List;

/**
 * Created by wangsf on 17/2/16.
 */
@DataSource(DS.CF)
public interface CfChannelDao {

	int add(CfChannel channel);

	CfChannel getByName(@Param("name") String name);

	CfChannel get(@Param("channelId") int channelId);

	List<CfChannel> listByIds(@Param("ids") List<Integer> channelIds);

	List<CfChannel> listByGroupId(@Param("groupId") int groupId, @Param("anchorId") int anchorId, @Param("limit") int limit);

	List<CfChannel> list(@Param("anchorId") int anchorId, @Param("limit") int limit);

	List<CfChannel> listByGroupIdAndPage(@Param("groupId") int groupId);

	List<CfChannel> listByPage();

	int update(@Param("channelId") int channelId, @Param("groupId") int groupId, @Param("description") String description);

	List<CfChannel> prefixSearch(@Param("keyword") String keyword, @Param("groupId") int groupId);

	List<CfChannel> prefixSearchWithoutKeyword(@Param("groupId") int groupId);

}
