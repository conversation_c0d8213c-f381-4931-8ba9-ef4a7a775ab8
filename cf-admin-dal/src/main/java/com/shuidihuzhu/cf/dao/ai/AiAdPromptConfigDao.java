package com.shuidihuzhu.cf.dao.ai;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.ai.AiAdPromptConfig;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * @Description:
 * @Author: pan<PERSON><PERSON><PERSON>
 * @Date: 2025/7/29 11:05
 */
@DataSource(AdminDS.CF_ADMIN_RW)
public interface AiAdPromptConfigDao {

    int insert(AiAdPromptConfig record);

    int update(AiAdPromptConfig record);

    AiAdPromptConfig selectConfigByUserProfile(@Param("userProfile") String userProfile);

}
