package com.shuidihuzhu.cf.dao.markfollowuptime;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.markfollowuptime.CfMarkFollowDo;
import com.shuidihuzhu.cf.model.report.schedule.ReportScheduleDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-12-27  14:08
 */
@DataSource(AdminDS.CF_RW)
public interface CfMarkFollowDao {

    int add(CfMarkFollowDo cfMarkFollowDo);

    CfMarkFollowDo getById(@Param("id") long id);

    CfMarkFollowDo getByBizId(@Param("bizId") long bizId);

    int updateTargetTimeById(@Param("id") long id,@Param("operatorId") int operatorId,  @Param("targetTime") Date targetTime, @Param("version") long version);

    int updateOperatorIdByBizId(@Param("bizId") long bizId,@Param("operatorId") long operatorId);

    int removeById(@Param("id") long id);

    int doneById(@Param("id") long id);

    List<CfMarkFollowDo> getByBizIds(@Param("bizIds") List<Long> bizIds);

    List<CfMarkFollowDo> getListByOperatorId(@Param("operatorId") int operatorId);

}
