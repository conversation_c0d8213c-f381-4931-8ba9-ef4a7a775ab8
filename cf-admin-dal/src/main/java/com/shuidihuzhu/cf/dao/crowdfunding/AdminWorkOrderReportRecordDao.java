package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderReportRecord;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderDataVo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/10.
 */
@DataSource(DS.CF)
public interface AdminWorkOrderReportRecordDao {
    int insertAdminWorkOrderReportRecord(AdminWorkOrderReportRecord adminWorkOrderReportRecord);

    @DataSource(DS.CF_SLAVE_2)
    int getFollowCountByTime(@Param("startTime") String startTime,
                             @Param("endTime") String endTime);

    @DataSource(DS.CF_SLAVE_2)
    List<AdminWorkOrderDataVo> getFollowCountByUserIds(@Param("orderType") int orderType,
                                                       @Param("orderTask") int orderTask,
                                                       @Param("startTime") String startTime,
                                                       @Param("endTime") String endTime,
                                                       @Param("userIds") List<Integer> userIds);
}
