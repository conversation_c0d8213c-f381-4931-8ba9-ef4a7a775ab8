package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.AdminCfCaseVisitConfig;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminOperatorVo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * TABLE: cf_case_visit_config
 *
 * 案例访问记录
 *
 * Created by wangsf on 18/4/16.
 */
@DataSource(DS.CF)
public interface AdminCfCaseVisitConfigDao {

	@DataSource(DS.CF_SLAVE_2)
	AdminCfCaseVisitConfig getByCaseId(@Param("caseId") int caseId);

	int insert(AdminCfCaseVisitConfig config);

	int update(AdminCfCaseVisitConfig config);

	int updateAbnormalHiddenAndHiddenTitle(@Param("caseId") int caseId,
										   @Param("abnormalHidden") Integer abnormalHidden,
										   @Param("abnormalHiddenSelfTitle") String abnormalHiddenSelfTitle,
										   @Param("abnormalHiddenOtherTitle") String abnormalHiddenOtherTitle);

	@DataSource(DS.CF_SLAVE_2)
	List<AdminCfCaseVisitConfig> getList();

	@DataSource(DS.CF_SLAVE_2)
	List<AdminCfCaseVisitConfig> getListByType(@Param("caseId") Integer caseId, @Param("type") Integer type, @Param("operator")Integer operator, @Param("start")Timestamp start, @Param("end")Timestamp end);

	@DataSource(DS.CF_SLAVE_2)
	List<AdminOperatorVo> getOperatorList();

	int updateCanShowByCaseId(@Param("caseId") Integer caseId,
						  @Param("canShow") Integer canShow);

	int updateBannerTextAndStartEndTime(@Param("caseId") Integer caseId,
										@Param("bannerText") String bannerText,
										@Param("startTime")Date startTime,
										@Param("endTime") Date endTime);

}
