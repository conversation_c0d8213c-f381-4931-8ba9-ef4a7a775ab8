package com.shuidihuzhu.cf.dao.mina;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.miniprogram.CfTopicComment;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR> Ahrievil
 */
@DataSource(DS.FRAME)
public interface AdminCfTopicCommentDao {

    List<CfTopicComment> selectByPage(@Param("commentId") Long commentId,
                                      @Param("topicId") Integer topicId,
                                      @Param("comment") String comment,
                                      @Param("commentUserId") Integer commentUserId,
                                      @Param("isSensitiveWord") Integer isSensitiveWord,
                                      @Param("beginTime")Timestamp beginTime,
                                      @Param("endTime") Timestamp endTime,
                                      @Param("orderType") Integer orderType);

    int deleteByTopicId(@Param("topicId") long topicId);

    int deleteByGroupId(@Param("groupId") long groupId);

}
