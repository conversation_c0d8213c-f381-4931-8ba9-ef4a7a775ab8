package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CfCaseTag;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by ahrievil on 2017/3/28.
 */
@DataSource(DS.CF)
public interface CfCaseTagDao {
    List<CfCaseTag> getByInfoId(@Param("infoId") int infoId);
    int addCaseTags(@Param("tagList") List<CfCaseTag> tagList);
    int deleteTags(@Param("infoId") int infoId);
}
