package com.shuidihuzhu.cf.dao.mina;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.vo.mina.CfIdCountVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> Ahrievil
 */
@DataSource(DS.FRAME)
public interface AdminCfCommentPriseDao {

    List<CfIdCountVo> selectPraiseCountByCommentIdList(@Param("set") Set<Long> set);
}
