package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.client.cf.admin.model.AdminCaseDetailsMsg;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;


/**
 * <AUTHOR>
 * @date 2020/11/25  2:07 下午
 */
@DataSource(AdminDS.CF_RW)
public interface AdminCaseDetailsMsgDao {
    int addCaseDetailsMsg(@Param("caseId") int caseId,
                          @Param("infoUuid") String infoUuid,
                          @Param("headPictureUrl") String headPictureUrl,
                          @Param("carouselText") String carouselText,
                          @Param("caseLabel") String caseLabel,
                          @Param("caseLabelSort") String caseLabelSort,
                          @Param("operateId") int operateId);

    int updateCaseDetailsMsg(@Param("id") int id,
                             @Param("headPictureUrl") String headPictureUrl,
                             @Param("carouselText") String carouselText,
                             @Param("caseLabel") String caseLabel,
                             @Param("caseLabelSort") String caseLabelSort,
                             @Param("operateId") int operateId);

    int updateHeadPictureUrl(@Param("caseId") int caseId,
                             @Param("headPictureUrl") String headPictureUrl);


    AdminCaseDetailsMsg getByInfoUuid(@Param("infoUuid") String infoUuid);


    AdminCaseDetailsMsg getByCaseId(@Param("caseId") int caseId);

    int updateShowTag(@Param("caseId") int caseId);

    int insert(AdminCaseDetailsMsg caseMsg);
}
