package com.shuidihuzhu.cf.dao.tdsql;

import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowParam;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

import java.util.List;


@DataSource("shuidiCfTdDataSource")
public interface TdAdminWorkOrderFlowDao {

    int countFinishFlowByParam(AdminWorkOrderFlowParam.SearchParam searchParam);

    List<AdminWorkOrderFlowView> selectFinishFlowByParam(AdminWorkOrderFlowParam.SearchParam searchParam);

}
