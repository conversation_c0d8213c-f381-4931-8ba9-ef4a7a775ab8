package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminBlessingCardVo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/25  10:59 上午
 */
@DataSource(AdminDS.CF_RW)
public interface AdminBlessingCardDao {

    int addBlessingCard(@Param("blessingCardImage") String blessingCardImage,
                        @Param("blessingCardText") String blessingCardText,
                        @Param("blessingCardAmount") int blessingCardAmount,
                        @Param("operateId") int operateId);

    int updateBlessingCard(@Param("id") int id,
                           @Param("blessingCardImage") String blessingCardImage,
                           @Param("blessingCardText") String blessingCardText,
                           @Param("blessingCardAmount") int blessingCardAmount,
                           @Param("operateId") int operateId);

    int delBlessingCard(@Param("id") int id,
                        @Param("operateId") int operateId);


    List<AdminBlessingCardVo> getList();


    List<AdminBlessingCardVo> selectListByImageOrText(@Param("blessingCardImage") String blessingCardImage,
                                                      @Param("blessingCardText") String blessingCardText,
                                                      @Param("blessingCardAmount") int blessingCardAmount);


    AdminBlessingCardVo getIdByText(@Param("blessingCardText") String blessingCardText);

}
