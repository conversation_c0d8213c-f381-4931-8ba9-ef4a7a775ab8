package com.shuidihuzhu.cf.dao.crowdfunding;


import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(AdminDS.CF_RW)
public interface AdminUserRealInfoDao {

    List<UserRealInfo> getSuccessByUserIds(@Param("userIds") List<Long> userIds);
}
