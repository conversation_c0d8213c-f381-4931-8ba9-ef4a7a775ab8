package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfReportCallRecordDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/12/17 下午3:10
 * @desc
 */
@DataSource(DS.CF)
public interface CfReportCallRecordDAO {

    int insert(CfReportCallRecordDO recordDO);

    List<CfReportCallRecordDO> query(@Param("caseId") int caseId, @Param("reportId") int reportId, @Param("addId") long addId);
}
