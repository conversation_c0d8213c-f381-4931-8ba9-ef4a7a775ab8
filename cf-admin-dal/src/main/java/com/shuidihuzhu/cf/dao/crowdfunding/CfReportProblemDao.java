package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportProblem;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportProblemRelationship;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2019-12-11 14:43
 **/
@DataSource(AdminDS.CF_RW)
public interface CfReportProblemDao {

    int insert(CfReportProblem cfReportProblem);

    /**
     * 存在修改labelId和problem的情况
     */
    int updateById(CfReportProblem cfReportProblem);


    CfReportProblem findByLabelIdAndProblem(@Param("labelId") int labelId, @Param("problem") String problem);


    CfReportProblem findById(@Param("id") int id);


    List<CfReportProblem> listByIds(@Param("ids") List<Integer> ids);

    /**
     * @return only show direct_show problem
     */

    List<CfReportProblem> listDirectShowByLabelId(@Param("labelIds") List<Integer> labelIds,
                                                  @Param("showLocations") List<Integer> showLocations);


    List<CfReportProblem> listForManager(@Param("problemDesc") String problemDesc, @Param("isUse")Integer isUse);


    List<CfReportProblem> listByLabelId(@Param("labelId") int labelId);

    int updateUseStatusById(@Param("isUse")int isUse, @Param("id") int id);


    List<CfReportProblem> getList(@Param("problemDesc") String problemDesc,
                                  @Param("isUse")int isUse, @Param("labelId")int labelId, @Param("collation")Integer collation);


    List<CfReportProblem> listByLabelIdAndIsUse(@Param("labelId") int labelId, @Param("isUse")int isUse);
}
