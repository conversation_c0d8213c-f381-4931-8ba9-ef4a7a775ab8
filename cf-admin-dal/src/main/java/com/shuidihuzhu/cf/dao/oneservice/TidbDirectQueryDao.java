package com.shuidihuzhu.cf.dao.oneservice;

import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * oneservice切换tidb直连查询
 *
 * <AUTHOR>
 * @since 2025/2/19
 */
@DataSource("tidbCommonShuidiFundraisingDataSource")
public interface TidbDirectQueryDao {

    List<Map<String, Object>> getAd(@Param("operate") String operate, @Param("dateStr") String dateStr);

    List<Map<String, Object>> getBaoFei(@Param("operate") String operate, @Param("dateStr") String dateStr);
}
