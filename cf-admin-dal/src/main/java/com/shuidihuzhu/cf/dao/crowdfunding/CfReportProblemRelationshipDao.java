package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfReportProblemRelationship;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2019-12-11 14:43
 **/
@DataSource(DS.CF)
public interface CfReportProblemRelationshipDao {

    int insert(CfReportProblemRelationship problemRelationship);

    int bindRelationship(@Param("problemId") int problemId, @Param("content") String content,
                         @Param("nextProblemId") int nextProblemId, @Param("nextProblem") String nextProblem);

    int deleteRelationship(@Param("problemId") int problemId, @Param("nextProblemIds") List<Integer> nextProblemIds);

    int unBindRelationship(@Param("problemId") int problemId, @Param("contents") List<String> contents);

    int changeProblemName(@Param("problemId") int problemId, @Param("problemName") String problemName);

    int changeNextProblemName(@Param("nextProblemId")int nextProblemId, @Param("problemName") String problemName);

    @DataSource(DS.CF_SLAVE)
    List<CfReportProblemRelationship> listByProblemIds(@Param("problemIds") List<Integer> problemIds);

    @DataSource(DS.CF_SLAVE)
    List<CfReportProblemRelationship> obtainRelationProblemId(@Param("problemId") int problemId,
                                          @Param("content") String content);


    @DataSource(DS.CF_SLAVE)
    List<CfReportProblemRelationship> listByNextProblemIds(@Param("nextProblemIds") List<Integer> nextProblemIds);

    List<Integer> selectRelationIdsByProblemId(@Param("problemId") int problemId, @Param("contents") List<String> contents);
}
