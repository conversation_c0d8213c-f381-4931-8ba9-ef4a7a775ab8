package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfSendProveSnapshot;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface CfSendProveSnapshotDao {

    int insertOne(CfSendProveSnapshot cfSendProveSnapshot);

    CfSendProveSnapshot getLastOne(@Param("caseId") int caseId,
                                   @Param("proveId") long proveId);

    List<CfSendProveSnapshot> getSnapshot(@Param("caseId") int caseId,
                                          @Param("proveId") long proveId,
                                          @Param("auditStatusList") List<Integer> auditStatusList);
}
