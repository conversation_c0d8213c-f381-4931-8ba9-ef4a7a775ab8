package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.workflow.StaffStatusAndNum;
import com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowStaffStatus;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-02-14 19:40
 **/
@DataSource(DS.CF)
public interface WorkFlowStaffStatusDao {

    int add(WorkFlowStaffStatus workFlowStaffStatus);

    int changeStatus(WorkFlowStaffStatus workFlowStaffStatus);

    void autoOffline();

    List<StaffStatusAndNum> groupByStatusAndOrgType(@Param("startTime") Date startTime);

    List<WorkFlowStaffStatus> listByUserIds(@Param("userIds") List<Long> userIds);

    List<WorkFlowStaffStatus> listByUserIdsAndStatus(@Param("userIds") List<Long> userIds, @Param("staffStatus") int staffStatus);


    WorkFlowStaffStatus findByUserId(@Param("userId") long userId);
}
