package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.report.CfReportDisposeActionTemplate;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface CfReportDisposeActionTemplateDao {

    int addTemplate(CfReportDisposeActionTemplate cfReportDisposeActionTemplate);

    int updateByActionId(CfReportDisposeActionTemplate cfReportDisposeActionTemplate);

    List<CfReportDisposeActionTemplate> selectByActionIds(@Param("actionIds") List<Long> actionIds);

    CfReportDisposeActionTemplate selectByActionId(@Param("actionId")long actionId);
}
