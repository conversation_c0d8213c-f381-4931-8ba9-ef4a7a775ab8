package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderClassifySettings;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@DataSource(DS.CF)
public interface AdminWorkOrderClassifySettingsDao {

    @DataSource(DS.CF_SLAVE_2)
    List<AdminWorkOrderClassifySettings> selectAllClassifySettingsByDelStatus(@Param("deleteStatus") Integer deleteStatus);
    @DataSource(DS.CF_SLAVE_2)
    List<AdminWorkOrderClassifySettings> selectChildClassifySettings(@Param("parentId") long parentId, @Param("deleteStatus") int deleteStatus);
    int insert(AdminWorkOrderClassifySettings settings);
    int updateDelStatusById(@Param("idList") Collection<Long> idList, @Param("userId") int userId, @Param("deleteStatus") int deleteStatus);
    @DataSource(DS.CF_SLAVE_2)
    List<AdminWorkOrderClassifySettings> selectClassifyByIdsAndDels(@Param("idList") Collection<Long> idList,
                                                                    @Param("delStatusList") Collection<Integer> delStatusList);
    @DataSource(DS.CF_SLAVE_2)
    AdminWorkOrderClassifySettings selectClassifySettingsById(@Param("id") long id);

    @DataSource(DS.CF_SLAVE_2)
    List<AdminWorkOrderClassifySettings> selectClassifySettingsByText(@Param("text") String text);

    @DataSource(DS.CF_SLAVE_2)
    Long selectMaxId();

    int changeAvailableStatus(@Param("id") long id, @Param("available") int available);

    //修改排序
    int changeWeight(@Param("id") long id, @Param("weight") int weight);
}
