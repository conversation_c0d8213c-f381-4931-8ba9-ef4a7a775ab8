package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * @Auther: subing
 * @Date: 2020/6/24
 */
@DataSource(DS.CF)
public interface AdminCfReportStrategyCallRecordDao {
    int addInfo(@Param("caseId")int caseId,
                @Param("workOrderId")long workOrderId,
                @Param("allQuestionModule")String allQuestionModule,
                @Param("allFundraiserModule")String allFundraiserModule,
                @Param("unKnowFundraiserModule")String unKnowFundraiserModule,
                @Param("lastCurrentRoleModule")String lastCurrentRoleModule,
                @Param("name")String name,
                @Param("organization")String organization);
}
