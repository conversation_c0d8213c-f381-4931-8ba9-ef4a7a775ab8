package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderCase;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderCaseDo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR> Ahrievil
 */
@DataSource(DS.CF)
public interface AdminWorkOrderCaseDao {
    int insertOne(AdminWorkOrderCase adminWorkOrderCase);
    int insertList(@Param("list") List<AdminWorkOrderCase> adminWorkOrderCase);
    @DataSource(DS.CF_SLAVE)
    AdminWorkOrderCase selectByCaseId(@Param("caseId") int caseId);

    @DataSource(DS.CF)
    List<AdminWorkOrderCase> selectByCaseIdAndTypeAndStatusAndTime(@Param("caseId")int caseId, @Param("type") Integer type,
                                                                   @Param("status") Integer status,
                                                                   @Param("beginTime") Timestamp createTime, @Param("endTime") Timestamp endTime);
    @DataSource(DS.CF)
    AdminWorkOrderCase getLastByCaseIdAndType(@Param("caseId")int caseId, @Param("type") Integer type);

    @DataSource(DS.CF_SLAVE)
    AdminWorkOrderCase selectById(@Param("id") long id);


    @DataSource(DS.CF_SLAVE)
    List<AdminWorkOrderCase> selectUnHandleCaseTask(@Param("orderType") int orderType, @Param("orderTask") int orderTask, @Param("count") int count);

    @DataSource(DS.CF_SLAVE)
    List<AdminWorkOrderCase> selectUnHandleCaseTaskByTime(@Param("orderType") int orderType,
                                                           @Param("orderTask") int orderTask,
                                                           @Param("startTime") Timestamp startTime,
                                                           @Param("endTime") Timestamp endTime);

    int updateStatusById(@Param("id") long id, @Param("status") int status);

    int updateCallStatusById(@Param("id") long id, @Param("callStatus") int callStatus);


    int updateStatusByIdList(@Param("status") int status, @Param("list") List<Long> list);


    @DataSource(DS.CF_SLAVE)
    List<AdminWorkOrderCase> selectPage(@Param("operatorId") Integer operatorId, @Param("channel") int channel,@Param("status") Integer status,@Param("type") Integer Type);


    int updateOrderCaseStatus(@Param("status")int status,@Param("workOrderId")int workOrderId);


    @DataSource(DS.CF_SLAVE)
    List<AdminWorkOrderCase> selectUnHandleCaseTaskByCount(@Param("type") int type,
                                                           @Param("task") int task,
                                                           @Param("taskType") int taskType,
                                                           @Param("count") int count,
                                                           @Param("channel") int channel);

    @DataSource(DS.CF_SLAVE)
    List<AdminWorkOrderCase> selectByCaseIdList(@Param("workOrderIds") List<Long> workOrderIds);

    int update(AdminWorkOrderCase adminWorkOrderCase);
}
