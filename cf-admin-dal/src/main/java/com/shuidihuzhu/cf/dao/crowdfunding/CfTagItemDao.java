package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CfTagItem;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by ahrievil on 2017/3/27.
 */
@DataSource(DS.CF)
public interface CfTagItemDao {
    List<CfTagItem> getList();
    CfTagItem getByPid(@Param("id") int id);
    int addOtherTag(@Param("list") List<String> list);
    List<CfTagItem> getByTagName(@Param("others") List<String> others);
    List<CfTagItem> getByInfoId(@Param("infoId") int infoId);
    List<CfTagItem> getByInfoIdOther(@Param("infoId") int infoId);
    List<CfTagItem> selectAllZore();
    int insertOne(CfTagItem cfTagItem);
}