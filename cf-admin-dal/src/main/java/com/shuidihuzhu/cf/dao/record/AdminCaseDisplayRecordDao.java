package com.shuidihuzhu.cf.dao.record;

import com.shuidihuzhu.cf.client.adminpure.model.AdminCaseDisplayRecordVo;
import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.record.AdminCaseDisplayRecordDo;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(AdminDS.CF_ADMIN_RW)
public interface AdminCaseDisplayRecordDao {
    int addOne(@Param("record") AdminCaseDisplayRecordDo record);
    List<AdminCaseDisplayRecordDo> selectByCaseId(@Param("caseId") int caseId);
}
