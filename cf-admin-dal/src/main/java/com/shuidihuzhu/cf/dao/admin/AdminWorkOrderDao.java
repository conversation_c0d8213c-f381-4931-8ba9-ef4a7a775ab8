package com.shuidihuzhu.cf.dao.admin;

import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminSensitiveVo;
import com.shuidihuzhu.cf.vo.crowdfunding.WorkOrderFirstApprove;
import com.shuidihuzhu.cf.vo.crowdfunding.firstapprove.FirstApproveAccountMap;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * Created by Ahrievil on 2017/11/30
 */
@DataSource(DS.CF)
public interface AdminWorkOrderDao {

    int insertOne(AdminWorkOrder adminWorkOrder);

    int insertList(@Param("list") List<AdminWorkOrder> list);

    int updateOperatorId(@Param("id") long id, @Param("operatorId") int operatorId);

    int updateStatus(@Param("id") long id, @Param("orderStatus") int orderStatus);

    int updateResult(@Param("id") long id, @Param("handleResult") int handleResult);

    int updateLevel(@Param("id") long id, @Param("level") int level);

    int updateWithOperatorIds(@Param("list") List<Long> list, @Param("operatorId") int operatorId,
                              @Param("orderStatus") int orderStatus);

    int updateOperator(@Param("workOrderId") long workOrderId, @Param("orignalUserId") int orignalUserId, @Param("targetUserId") int targetUserId);

    int resetWorkOrder(@Param("list") List<Long> list, @Param("operatorId") int operatorId,
                       @Param("orderStatus") int orderStatus, @Param("taskType") int taskType);


    @DataSource(DS.CF_SLAVE_2)
    List<AdminWorkOrder> selectUgcByPageV2(@Param("operatorId") int operatorId, @Param("orderType") Integer orderType,
                                           @Param("orderTasks") List<Integer> orderTasks, @Param("caseId") Integer caseId,
                                           @Param("result") Integer result, @Param("right") int right);

    @DataSource(DS.CF_SLAVE_2)
    List<AdminSensitiveVo> selectUgcSensitiveByPage(@Param("orderType") int orderType,
                                                    @Param("orderTasks") List<Integer> orderTasks,
                                                    @Param("operatorId")int operatorId,
                                                    @Param("caseId")int caseId,
                                                    @Param("result") Integer result,
                                                    @Param("title")String title,
                                                    @Param("commentUserId")long commentUserId,
                                                    @Param("contentTypes")List<Integer> contentTypes,
                                                    @Param("startTime")String startTime,
                                                    @Param("endTime")String endTime);

    @DataSource(DS.CF_SLAVE_2)
    List<AdminSensitiveVo> selectUgcSensitiveByAnchor(@Param("orderType") int orderType,
                                                      @Param("orderTasks") List<Integer> orderTasks,
                                                      @Param("operatorId") int operatorId,
                                                      @Param("caseId") int caseId,
                                                      @Param("result") Integer result,
                                                      @Param("title") String title,
                                                      @Param("commentUserId") long commentUserId,
                                                      @Param("contentTypes") List<Integer> contentTypes,
                                                      @Param("hitWords")String hitWords,
                                                      @Param("startTime") String startTime,
                                                      @Param("endTime") String endTime,
                                                      @Param("anchor") int anchor,
                                                      @Param("size") int size,
                                                      @Param("isPre") boolean isPre);

    @DataSource(DS.CF_SLAVE_2)
    AdminSensitiveVo getSensitiveVOByWorkOrderId(@Param("id")int id);

//    @DataSource(DS.CF_SLAVE_2)
//    List<Integer> listOperatorByOrderType(@Param("orderType") int orderType);


    @DataSource(DS.CF_SLAVE_2)
    List<AdminWorkOrder> listUgcBaseInfoByPage(@Param("operatorId") int operatorId,
                                               @Param("orderType") Integer orderType,
                                               @Param("orderTasks") List<Integer> orderTasks,
                                               @Param("caseId") Integer caseId,
                                               @Param("result") Integer result,
                                               @Param("action") Integer action,
                                               @Param("riskLevel") Integer riskLevel,
                                               @Param("startTime") String startTime,
                                               @Param("endTime")String endTime);

    @DataSource(DS.CF_SLAVE_2)
    List<AdminWorkOrder> getUnHandleTask(@Param("orderType") int orderType, @Param("orderTask") int orderTask,
                                         @Param("size") int size);

    @DataSource(DS.CF_SLAVE_2)
    List<AdminWorkOrder> getUnHandleTaskByTasks(@Param("orderType") int orderType, @Param("tasks") List<Integer> tasks,
                                         @Param("size") int size);

    int recoverStatusAndOperator(@Param("list") List<Long> list, @Param("orderStatus") int orderStatus, @Param("operatorId") int operatorId, @Param("orderType") int orderType, @Param("orderTask") int orderTask);

    @DataSource(DS.CF_SLAVE_2)
    List<AdminWorkOrder> getUnHandleTaskByOperator(@Param("orderType") int orderType, @Param("taskTypes") List<Integer> taskTypes, @Param("orderStatus") int orderStatus, @Param("operatorId") int operatorId);

    @DataSource(DS.CF_SLAVE_2)
    AdminWorkOrder selectById(@Param("id") long id);

    @DataSource(DS.CF_SLAVE_2)
    List<AdminWorkOrder> selectByIdList(@Param("list") List<Long> list);

    int updateChangeable(AdminWorkOrder adminWorkOrder);


    @DataSource(DS.CF_SLAVE_2)
    int selectUnHandleCount(@Param("orderType") Integer orderType, @Param("orderTasks") List<Integer> orderTasks);


    int updateRoleOrOperatorId(@Param("operatorId") Integer operatorId, @Param("id") long id);

    int handleFlow(int handleResult);

    int updateRoleOrOperatorIdWithIdList(@Param("operatorId") Integer operatorId, @Param("list") List<Long> list);

    @DataSource(DS.CF_SLAVE_2)
    List<AdminWorkOrder> selectByIds(@Param("list") List<Long> list);


    AdminWorkOrder getWorkOrderByCaseId(@Param("id")int id,@Param("type")int type);

    int updateOrderStatus(@Param("orderStatus")int orderStatus,
                      @Param("handleResult")int handleResult,
                      @Param("id")int id);


    int update(AdminWorkOrder adminWorkOrder);


    @DataSource(DS.CF_SLAVE_2)
    List<WorkOrderFirstApprove> getFirstUGC(@Param("caseId")long caseId,
                                            @Param("channel")int channel,
                                            @Param("status")int status,
                                            @Param("right")int right,
                                            @Param("operatorId")int userId,
                                            @Param("currOperatorId")int currOperatorId,
                                            @Param("operatorStartDate")Date operatorStartDate,
                                            @Param("operatorEndDate")Date operatorEndDate,
                                            @Param("raiserId") long raiserId);


    @DataSource(DS.CF_SLAVE_2)
    int getFirstApproveCountByStatusAndTime(@Param("firstApproveStatus") int firstApproveStatus, @Param("startTime") Timestamp startTime, @Param("endTime") Timestamp endTime);
    @DataSource(DS.CF_SLAVE_2)
    List<FirstApproveAccountMap> getFirstApproveAccounts(@Param("startTime") Timestamp startTime, @Param("endTime") Timestamp endTime, @Param("operatorId") int operatorId);
    @DataSource(DS.CF_SLAVE_2)
    List<AdminWorkOrder> selectApplyingFirstApprovesByOperator(@Param("operatorId") int operatorId, @Param("count") int count, @Param("startTime") Timestamp startTime, @Param("endTime") Timestamp endTime);


    @DataSource(DS.CF_SLAVE_2)
    List<AdminWorkOrder> selectUnHandleOrders(@Param("orderType") int orderType, @Param("orderTask") int orderTask,
                                              @Param("id") Long id, @Param("size") int size);

    int updateMsg(@Param("id")long id, @Param("orderStatus")Integer orderStatus,@Param("operatorId")Integer operatorId,@Param("level")Integer level);

    @DataSource(DS.CF_SLAVE_2)
    AdminWorkOrder getByOperatorIdAndStatus(@Param("createTime") Date createTime, @Param("operatorId") long operatorId, @Param("orderStatus") int orderStatus);

    @DataSource(DS.CF_SLAVE_2)
    List<AdminWorkOrder> selectUnHandleTask(@Param("createTime") Date createTime);
}
