package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.crowdfunding.CfDiseaseManagerDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2019-11-06 22:05
 **/
@DataSource(AdminDS.CF_RW)
public interface CfDiseaseManagerDao {

    int insert(CfDiseaseManagerDO cfDiseaseManagerDO);

    int edit(CfDiseaseManagerDO cfDiseaseManagerDO);

    int delete(long id);

    /**
     * 仅提供给sea后台作为多个搜索条件使用
     */

    List<CfDiseaseManagerDO> listForSeaAdmin(CfDiseaseManagerDO cfDiseaseManagerDO);


    CfDiseaseManagerDO getById(long id);
}
