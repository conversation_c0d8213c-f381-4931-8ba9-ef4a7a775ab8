package com.shuidihuzhu.cf.dao.admin;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrderRecord;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * Created by Ahrievil on 2017/11/30
 */
@DataSource(AdminDS.CF_RW)
public interface AdminWorkOrderRecordDao {

    int insertOne(AdminWorkOrderRecord adminWorkOrderRecord);

    int insertList(@Param("list") List<AdminWorkOrderRecord> list);

    @DataSource(DS.CF_SLAVE_2)
    List<AdminWorkOrderRecord> selectByWorkIdAndOperateTypes(@Param("workOrderIds") List<Long> workOrderIds,
                                                             @Param("operateTypes")List<Integer> operateTypes);

    int deleteFirstUgcById(@Param("id") long id);
}
