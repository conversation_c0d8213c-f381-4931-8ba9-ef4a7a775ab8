package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.vo.report.ReportInsteadWorkOrderRelateVO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface ReportInsteadWorkOrderRelateDAO {

    int insert(ReportInsteadWorkOrderRelateVO reportInsteadWorkOrderRelateVO);

    ReportInsteadWorkOrderRelateVO getByReportInsteadId(@Param("reportInsteadId") long reportInsteadId);
}
