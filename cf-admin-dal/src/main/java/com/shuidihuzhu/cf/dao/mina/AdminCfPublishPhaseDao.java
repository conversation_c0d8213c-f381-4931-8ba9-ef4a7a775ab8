package com.shuidihuzhu.cf.dao.mina;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.miniprogram.CfPublishPhase;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;

@DataSource(DS.FRAME)
public interface AdminCfPublishPhaseDao {

    CfPublishPhase getLatestPhase();

    int updatePhaseStatusByTime(@Param("time") Timestamp time);

    int updateByPhaseId(@Param("phaseId") int phaseId,
                        @Param("cfPublishPhase") CfPublishPhase cfPublishPhase);

    int delete(@Param("phaseId") int phaseId);
}
