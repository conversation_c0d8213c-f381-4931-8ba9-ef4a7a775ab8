package com.shuidihuzhu.cf.dao.sd.admin.channel;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.admin.channel.CfChannelGroup;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by wangsf on 17/2/16.
 */
@DataSource(DS.CF)
public interface CfChannelGroupDao {

	List<CfChannelGroup> listGroups(@Param("anchorId") int anchorId, @Param("limit") int limit);

	List<CfChannelGroup> listByIds(@Param("ids") List<Integer> ids);

	List<CfChannelGroup> listGroupsByPage();

	CfChannelGroup getById(@Param("groupId") int groupId);

	int insertOne(CfChannelGroup cfChannelGroup);
}
