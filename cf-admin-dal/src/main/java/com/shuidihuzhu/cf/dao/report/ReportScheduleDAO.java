package com.shuidihuzhu.cf.dao.report;

import com.shuidihuzhu.cf.model.report.schedule.ReportScheduleDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-12-27  14:08
 */
@DataSource(DS.CF)
public interface ReportScheduleDAO {

    int insert(ReportScheduleDO reportScheduleDO);

    ReportScheduleDO getById(@Param("id") long id);

    int updateTargetTimeById(@Param("id") long id, @Param("targetTime") Date targetTime, @Param("version") long version);

    int removeById(@Param("id") long id);

    int doneById(@Param("id") long id);

    ReportScheduleDO getLastByCaseId(@Param("caseId") int caseId);

    List<ReportScheduleDO> getListByCaseIdList(@Param("caseIdList") List<Integer> caseIdList,
                                               @Param("targetTimeEnd") Date targetTimeEnd);
}
