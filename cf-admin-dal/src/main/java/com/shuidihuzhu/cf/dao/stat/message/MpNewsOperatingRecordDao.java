package com.shuidihuzhu.cf.dao.stat.message;

import com.shuidihuzhu.cf.model.message.MpNewsOperatingRecord;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

import java.util.List;

@DataSource("statCfDataSource")
public interface MpNewsOperatingRecordDao {

    int insertOperatingRecord(MpNewsOperatingRecord mpNewsOperatingRecord);

    @DataSource("statCfSlaveDataSource")
    List<MpNewsOperatingRecord> getByTaskId(@Param("taskId") long taskId,
                                            @Param("offset") int offset,
                                            @Param("limit") int limit);

    @DataSource("statCfSlaveDataSource")
    Integer countOperatingRecordByTaskId(@Param("taskId") long taskId);

    @DataSource("statCfSlaveDataSource")
    List<MpNewsOperatingRecord> getByTaskIdAndType(@Param("taskId") long taskId,
                                                  @Param("type") int type,
                                                  @Param("offset") int offset,
                                                  @Param("limit") int limit);

    @DataSource("statCfSlaveDataSource")
    List<MpNewsOperatingRecord> getByUserNameAndType(@Param("userName") String userName,
                                                     @Param("type") int type,
                                                     @Param("offset") int offset,
                                                     @Param("limit") int limit);
}
