package com.shuidihuzhu.cf.dao.stat;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.vo.crowdfunding.HandleHospital;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by ahrievil on 2017/5/5.
 */
@DataSource(DS.CF)
public interface HandleHospitalDao {
    int insert(HandleHospital handleHospital);
    int insertInit(@Param("list") List<HandleHospital> handleHospitals);
    List<HandleHospital> selectAll(@Param("offSet") int offSet, @Param("count") int count);
    int update(HandleHospital handleHospital);
    int selectMax();
    int updateValid(@Param("disable") int disable, @Param("id") int id);
}
