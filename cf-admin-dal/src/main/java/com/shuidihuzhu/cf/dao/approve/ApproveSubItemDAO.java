package com.shuidihuzhu.cf.dao.approve;

import com.shuidihuzhu.cf.domain.approve.ApproveSubItemDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-03-31  17:29
 */
@DataSource(DS.CF)
public interface ApproveSubItemDAO {

    int insert(ApproveSubItemDO approveSubItemDO);

    List<ApproveSubItemDO> listByCaseId(@Param("caseId")int caseId);

    ApproveSubItemDO getByCaseIdAndCode(@Param("caseId")int caseId, @Param("code")int code);

    int updatePassed(@Param("caseId") int caseId, @Param("code") int code,@Param("passed") boolean passed);
}
