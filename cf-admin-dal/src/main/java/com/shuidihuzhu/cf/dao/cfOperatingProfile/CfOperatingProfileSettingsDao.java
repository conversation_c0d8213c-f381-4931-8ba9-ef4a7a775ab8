package com.shuidihuzhu.cf.dao.cfOperatingProfile;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileSettings;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@DataSource(AdminDS.CF_RW)
public interface CfOperatingProfileSettingsDao {

    int addProfile(CfOperatingProfileSettings profile);


    List<CfOperatingProfileSettings> selectValidByProfileType(@Param("profileType") int profileType);


    List<CfOperatingProfileSettings> selectByParentIdAndDataStatus(@Param("parentId") int parentId,
                                                                   @Param("dataStatus") int dataStatus,
                                                                   @Param("profileType") int profileType);


    List<CfOperatingProfileSettings> selectByParentIdAndContent(@Param("parentId") int parentId,
                                                                   @Param("content") String content,
                                                                @Param("profileType") int profileType);


    List<CfOperatingProfileSettings> selectByIds(@Param("ids") List<Integer> ids);

    @DataSource(DS.CF)
    CfOperatingProfileSettings selectById(@Param("id") Integer id);

    int updateDataStatusRank(@Param("id") int id, @Param("dataStatus") int dataStatus, @Param("rank") int rank);

    int updateDataRank(@Param("id") int id, @Param("rank") int rank);

    int updateUseSize(@Param("ids") Collection<Integer> ids, @Param("useSize") int size);
}
