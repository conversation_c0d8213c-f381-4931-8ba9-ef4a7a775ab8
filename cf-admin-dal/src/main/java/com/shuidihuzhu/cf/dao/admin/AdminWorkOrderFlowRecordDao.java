package com.shuidihuzhu.cf.dao.admin;

import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowStatistics;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderFlowRecord;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * Created by Ahrievil on 2017/12/25
 */
@DataSource(DS.CF)
public interface AdminWorkOrderFlowRecordDao {
    int insertOne(AdminWorkOrderFlowRecord adminWorkOrderFlowRecord);
    int insertList(@Param("list") List<AdminWorkOrderFlowRecord> list);
    List<AdminWorkOrderFlowRecord> selectAllByWorkOrderId(@Param("workOrderId") long workOrderId,
                                                          @Param("operateTypeCodes") List<Integer> operateTypeCodes);

    @DataSource(DS.CF_SLAVE)
    List<AdminWorkOrderFlowRecord> selectByOperatorIdsAndTime(AdminWorkOrderFlowStatistics.searchParam param);

    @DataSource(DS.CF_SLAVE)
    List<AdminWorkOrderFlowRecord> selectWithAssignRecords(@Param("beginTime") Date beginTime,
                                                           @Param("endTime") Date endTime,
                                                           @Param("orderOperatorId") int orderOperatorId);
}
