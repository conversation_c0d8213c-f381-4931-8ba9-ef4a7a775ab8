package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * @Auther: subing
 * @Date: 2020/3/13
 */
@DataSource(DS.CF)
public interface AdminCfRiskLabelDao {
    int add(@Param("activityId") int activityId, @Param("riskLabel") String riskLabel);

    int updateRiskLabel(@Param("activityId") int activityId, @Param("riskLabel") String riskLabel);


    String getByActivityId(@Param("activityId") int activityId);
}
