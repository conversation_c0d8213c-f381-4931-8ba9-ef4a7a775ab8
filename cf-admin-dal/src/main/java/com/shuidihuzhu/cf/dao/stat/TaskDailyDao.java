package com.shuidihuzhu.cf.dao.stat;

import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.common.datasource.DS;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by wux<PERSON><PERSON> on 5/24/16.
 */

@DataSource("statCfSlaveDataSource")
public interface TaskDailyDao {
    // 取得指定日期新增订单数，按insurance_id分类
    List<Map<String, Object>> getNewOrderCounts(Date day);

    // 取得指定日期区间的统计数据（总量）
    List<Map<String, Object>> getDailySummary(@Param("start") Date start, @Param("end") Date end);

    // 取得指定日期区间的统计数据（新增）
    List<Map<String, Object>> getDailySummaryOfNew(@Param("start") Date start, @Param("end") Date end);

}
