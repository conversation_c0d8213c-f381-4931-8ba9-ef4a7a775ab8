package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderClassifySettings;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderClassifySettingsRecord;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@DataSource(DS.CF)
public interface AdminWorkOrderClassifySettingsRecordDao {

    int insert(AdminWorkOrderClassifySettingsRecord record);

    @DataSource(DS.CF_SLAVE_2)
    List<AdminWorkOrderClassifySettingsRecord> listRecords(@Param("problemClassifyId") long problemClassify);
}
