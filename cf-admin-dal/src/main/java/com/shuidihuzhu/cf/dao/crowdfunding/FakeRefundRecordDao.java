package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.crowdfunding.FakeRefundRecord;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

@DataSource(AdminDS.CF_RW)
public interface FakeRefundRecordDao {
    int save(FakeRefundRecord record);

    @DataSource(DS.CF)
    FakeRefundRecord findByPayUid(String payUid);
}
