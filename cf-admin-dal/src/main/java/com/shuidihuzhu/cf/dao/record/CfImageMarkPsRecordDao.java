package com.shuidihuzhu.cf.dao.record;

import com.shuidihuzhu.cf.model.record.CfImageMarkPsRecord;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2022/12/26 14:32
 * @Description:
 */
@DataSource("shuidiCfAdminDataSource")
public interface CfImageMarkPsRecordDao {

    int insert(CfImageMarkPsRecord cfImageMarkPsRecord);

    CfImageMarkPsRecord getByAttachmentId(@Param("attachmentId") int attachmentId);

    List<CfImageMarkPsRecord> getByCaseId(@Param("caseId") int caseId);

    int updateById(@Param("id") long id, @Param("recognitionPs") int recognitionPs, @Param("operatorId") long operatorId);
}
