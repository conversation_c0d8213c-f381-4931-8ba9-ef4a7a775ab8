package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.vo.crowdfunding.AdminCrowdfundingProgress;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface NewAdminCfFundUseAuditDao {

    @DataSource(DS.CF)
    int updateAuditStatusRejected(@Param("progressId") Integer progressId,
                                  @Param("fundAuditRejectedReason") String fundAuditRejectedReason);

    @DataSource(DS.CF)
    int updateAuditPass(@Param("progressId") Integer progressId, @Param("comment") String comment);

    @DataSource(DS.CF_SLAVE_2)
    String getAuditStatusRejectedReason(@Param("progressId") Integer progressId);

    @DataSource(DS.CF_SLAVE_2)
    AdminCrowdfundingProgress selectByProgressId(@Param("progressId") int progressId);

    @DataSource(DS.CF_SLAVE_2)
    List<AdminCrowdfundingProgress> selectByProgressIdList(@Param("idList") List<Integer> idList);

    @DataSource(DS.CF_SLAVE_2)
    AdminCrowdfundingProgress getLastOneByCaseId(@Param("caseId") int caseId);


    @DataSource(DS.CF_SLAVE_2)
    AdminCrowdfundingProgress getFirstOneByCaseId(@Param("caseId") int caseId);

    @DataSource(DS.CF)
    int updateContentAndImg(@Param("progressId") long progressId, @Param("content") String content, @Param("imageUrls") String imageUrls);

}
