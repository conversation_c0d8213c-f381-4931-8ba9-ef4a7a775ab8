package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplateOperatorHistory;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2022/1/5 19:45
 * @Description:
 */
@DataSource(DS.CF)
public interface AdminCfBaseInfoTemplateOperatorHistoryDao {

    List<CfBaseInfoTemplateOperatorHistory> selectByTemplateId(@Param("cfBaseTemplateId") long cfBaseTemplateId);

    int insert(CfBaseInfoTemplateOperatorHistory cfBaseInfoTemplateOperatorHistory);
}
