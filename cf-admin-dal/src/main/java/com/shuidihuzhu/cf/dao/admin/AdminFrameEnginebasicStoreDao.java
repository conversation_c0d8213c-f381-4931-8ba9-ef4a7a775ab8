package com.shuidihuzhu.cf.dao.admin;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * @author: wanghui
 * @create: 2018/10/22 1:42 PM
 */
@DataSource(DS.FRAME)
public interface AdminFrameEnginebasicStoreDao {
    /**
     * @author: wanghui
     * @time: 2018/10/22 1:51 PM
     * @description: updateContentByActivityId
     * @param: [content, activityId]
     * @return: int
     */
    int updateContentByActivityId(@Param("content") String content,
                              @Param("activityId") Integer activityId);
}
