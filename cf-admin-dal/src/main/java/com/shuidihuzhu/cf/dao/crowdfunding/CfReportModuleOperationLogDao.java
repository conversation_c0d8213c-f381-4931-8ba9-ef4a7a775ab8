package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportModuleOperationLog;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(AdminDS.CF_RW)
public interface CfReportModuleOperationLogDao {

    List<CfReportModuleOperationLog> getByLabelId(@Param("labelId")int labelId);

    int addLog(@Param("labelId")int labelId, @Param("action")String action, @Param("operator")String operator);

}
