package com.shuidihuzhu.cf.dao.crowdfunding.comment;


import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingComment;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 以crowdfunding_id 分表
 */
@DataSource("cfCommentSlaveShardingCrowdfunding")
public interface CrowdfundingCommentShareDao {
    CrowdfundingComment getByIdNoCareDeleted(@Param("crowdfundingId") Long crowdfundingId, @Param("id") Long id);

    List<CrowdfundingComment> getCommentByParentId(@Param("crowdfundingId") int crowdfundingId, @Param("parentId") long parentId);
}

