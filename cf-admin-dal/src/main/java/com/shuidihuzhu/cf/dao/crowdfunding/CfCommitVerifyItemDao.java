package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CfCommitVerifyItem;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * Created by ahrievil on 2017/4/24.
 */
@DataSource(AdminDS.CF_RW)
public interface CfCommitVerifyItemDao {


    List<CfCommitVerifyItem> selectRefuseList(@Param("infoUuid") String infoUuid);


    List<CfCommitVerifyItem> selectBaseInfoRefuseList();


    List<CfCommitVerifyItem> selectRefusesByList(@Param("cfRefuseReasonIds") List<Integer> cfRefuseReasonIds);


    List<Integer> selectAllType(@Param("start") int start, @Param("size") int size);


    List<CfCommitVerifyItem> selectByIds(@Param("set") Set<Integer> set);


    List<CfCommitVerifyItem> selectAll(@Param("start") int start, @Param("size") int size);
}
