package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonTag;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Set;

/**
 * Created by Ahrievil on 2017/7/26
 */
@DataSource(DS.CF)
public interface CfRefuseReasonTagDao {
    int insertOne(CfRefuseReasonTag cfRefuseReasonTag);
    List<CfRefuseReasonTag> selectAllWithUuid(@Param("start") int start, @Param("size") int size, @Param("infoUuid") String infoUuid);
    List<CfRefuseReasonTag> selectSortTagByDataType(@Param("dataType") int dataType, @Param("isDelete") Integer isDelete);
    CfRefuseReasonTag selectById(@Param("id") int id);
    List<CfRefuseReasonTag> selectByTagIds(@Param("set") Set<Integer> set);
    CfRefuseReasonTag selectByTagId(@Param("id") int id);

    int updateTagStateById(@Param("id") int id, @Param("reasonIds") String reasonIds,
                           @Param("dataStep") Integer dataStep);


}
