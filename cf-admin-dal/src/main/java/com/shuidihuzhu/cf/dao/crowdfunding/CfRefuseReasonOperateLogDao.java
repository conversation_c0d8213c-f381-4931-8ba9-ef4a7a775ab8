package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(AdminDS.CF_RW)
public interface CfRefuseReasonOperateLogDao {
    int addLog(CfRefuseReasonEntity.CfRefuseReasonOperateLog log);

    List<CfRefuseReasonEntity.CfRefuseReasonOperateLog> selectOperateLogByEntityId(@Param("entityId") int entityId);
}
