package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.crowdfunding.CfDiseaseManagerRecordDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2019-11-08 12:11
 * only add select
 **/
@DataSource(AdminDS.CF_RW)
public interface CfDiseaseManagerRecordDao {

    int insert(CfDiseaseManagerRecordDO cfDiseaseManagerRecordDO);


    List<CfDiseaseManagerRecordDO> listByManagerId(@Param("diseaseManagerId") long diseaseManagerId);
}
