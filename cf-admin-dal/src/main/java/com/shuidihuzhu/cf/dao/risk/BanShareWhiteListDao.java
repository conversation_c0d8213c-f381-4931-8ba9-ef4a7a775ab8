package com.shuidihuzhu.cf.dao.risk;


import com.shuidihuzhu.cf.model.risk.BanShareWhiteListDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@DataSource(DS.CF)
public interface BanShareWhiteListDao {

    int insert(@Param("banShareWhiteListDO") BanShareWhiteListDO banShareWhiteListDO);

    int updateMaxCount(@Param("id") long id,@Param("maxCount") int maxCount,@Param("date") Date date);

    BanShareWhiteListDO getList(@Param("cfUserId") long cfUserId);

    List<BanShareWhiteListDO> getAll();

    List<BanShareWhiteListDO> getByCondition(@Param("cfUserId") long cfUserId,@Param("name") String name,@Param("state") int state);

    void updateState();

    BanShareWhiteListDO getByUserIdAndState(@Param("cfUserId") long cfUserId,@Param("state") int state);

    BanShareWhiteListDO getById(@Param("id") long id);

    List<BanShareWhiteListDO> getByState(@Param("state") int state);

    BanShareWhiteListDO getByUserId(@Param("cfUserId") long cfUserId);

}
