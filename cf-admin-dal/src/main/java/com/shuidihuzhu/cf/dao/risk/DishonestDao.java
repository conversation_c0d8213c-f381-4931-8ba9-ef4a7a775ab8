package com.shuidihuzhu.cf.dao.risk;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.risk.Dishonest;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * @description:
 * @author: zheng<PERSON>u
 * @date: 2021-04-19 20:32
 **/
@DataSource(AdminDS.CF_RW)
public interface DishonestDao {

    int insert(Dishonest dishonest);


    Dishonest getDishonestInfo(@Param("caseId") int caseId, @Param("userType") int userType, @Param("name") String name);
}

