package com.shuidihuzhu.cf.dao.admin;

import com.shuidihuzhu.cf.model.admin.UserComment;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2018/7/31
 */
@DataSource(DS.CF)
public interface UserCommentDao {


    int add(UserComment userComment);

    int insert(UserComment userComment);

    int addList(@Param("list") List<UserComment> userComments);

    List<UserComment> getUserComment(@Param("caseId") long caseId, @Param("commentSource") int commentSource, @Param("commentType") int commentType);

    int countByCommentSoure(@Param("caseId") long caseId, @Param("commentSource") int commentSource);

    List<UserComment> getUserCommentDescByCommentSource(@Param("caseId") long caseId, @Param("commentSource") int commentSource,
                                                    @Param("start") int start, @Param("size") int size);

}
