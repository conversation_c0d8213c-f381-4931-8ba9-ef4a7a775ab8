package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@DataSource(DS.CF)
public interface AdminCfHighReportHandleInfoDao {

    int insertRecord(@Param("caseId") int caseId, @Param("systemEnd") int systemEnd);

    @DataSource(DS.CF_SLAVE_2)
    List<Integer> selectHasFinishCaseByTime(@Param("systemEnd") int systemEnd,
                                            @Param("beginTime")Date beginTime, @Param("endTime")Date endTime);

    @DataSource(DS.CF_SLAVE_2)
    List<Integer> selectByCaseIdAndTime(@Param("caseId") int caseId, @Param("systemEnd") int systemEnd,
                                        @Param("beginTime")Date beginTime, @Param("endTime")Date endTime);
}
