package com.shuidihuzhu.cf.dao.labels;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;


/**
 * <AUTHOR>
 */
@DataSource(AdminDS.CF_RW)
public interface CaseLabelsManagementDao {
    int add(@Param("infoUuid") String infoUuid, @Param("labelsManagement") String labelsManagement);


    String get(@Param("infoUuid") String infoUuid);

    int update(@Param("infoUuid") String infoUuid, @Param("labelsManagement") String labelsManagemen);
}
