package com.shuidihuzhu.cf.dao.crmUserManage;

import com.shuidihuzhu.cf.model.CrmUserManage.UserManage;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface UserManageExtDao {

    int addExtList(@Param("extList") List<UserManage.UserManageExt> extList);
    List<UserManage.UserManageExt> selectExtValues(@Param("mark") String mark,
                                                   @Param("markValue") String markValue,
                                                   @Param("extKeyList") List<String> extKeyList);

    int deleteUserExtByKeys(@Param("mark") String mark,
                            @Param("markValue") String markValue,
                            @Param("extKeyList") List<String> extKeyList);
}
