package com.shuidihuzhu.cf.dao.admin;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.admin.AdminOrganization;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: WangYing on 2018/9/17
 */
@DataSource(AdminDS.CF_ADMIN_RW)
public interface AdminOrganizationDao {

    int addOrganizationNode(AdminOrganization adminOrganization);

    void editOrganizationNode(AdminOrganization adminOrganization);

    void deleteOrganizationNode(@Param("organizationId") int organizationId);

    AdminOrganization getAdminOrganization(@Param("organizationId") int organizationId);

    List<AdminOrganization> getAdminOrganizationByParentOrgId(@Param("parentOrgId") int parentOrgId);

    boolean hasSameNameAndParentOrg(@Param("name") String name, @Param("orgIds") List<Integer> parentOrgId);

    List<AdminOrganization> getAllAdminOrganization();

    List<AdminOrganization> getAdminOrganizationByName(@Param("name") String name);
}
