package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * Created by ahrievil on 2017/1/20.
 */
@DataSource(DS.CF)
public interface CrowdfundingRediskvDao {
	String selectMailRecipient(@Param("k") String k);

	int addOne(@Param("k") String k, @Param("v") String v);
}
