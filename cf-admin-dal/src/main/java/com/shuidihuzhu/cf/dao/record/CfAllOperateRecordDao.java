package com.shuidihuzhu.cf.dao.record;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.record.CfAllOperateRecordDo;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/1  3:51 下午
 */
@DataSource(AdminDS.CF_ADMIN_RW)
public interface CfAllOperateRecordDao {
    int insert(CfAllOperateRecordDo cfAllOperateRecordDo);

    int insertBatch(@Param("list") List<CfAllOperateRecordDo> list);

    List<CfAllOperateRecordDo> getListByCaseId(@Param("caseId") int caseId, @Param("bizType") int bizType, @Param("bizId") long bizId);


    List<CfAllOperateRecordDo> getListByCaseIdAndBizType(@Param("caseId") int caseId, @Param("bizType") int bizType);
}
