package com.shuidihuzhu.cf.dao.admin;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.mina.CfMinaMajorDiseaseTitleContent;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by ahrievil on 2017/7/13.
 */
@DataSource(DS.CF)
public interface AdminCfMinaMajorDiseaseTitleContentDao {
    int updateContent(@Param("content") String content, @Param("id") int id);
    List<CfMinaMajorDiseaseTitleContent> selectWithLimit(@Param("start") int start, @Param("content") String content, @Param("limit") int limit);
    int changeContent(@Param("content") String content, @Param("id") int id);
    CfMinaMajorDiseaseTitleContent selectById(@Param("id") int id);
    CfMinaMajorDiseaseTitleContent selectByTitle(@Param("title") String title);
    int changeTitle(@Param("title") String title, @Param("id") int id);
}
