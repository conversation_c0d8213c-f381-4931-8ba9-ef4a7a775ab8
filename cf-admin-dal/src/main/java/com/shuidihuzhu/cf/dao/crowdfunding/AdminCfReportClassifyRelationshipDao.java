package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.report.CfReportClassifyRelationship;
import com.shuidihuzhu.cf.model.report.CfReportDisposeAction;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/10/23.
 */
@DataSource(AdminDS.CF_RW)
public interface AdminCfReportClassifyRelationshipDao {

    int add(@Param("cfReportClassifyRelationships") List<CfReportClassifyRelationship> cfReportClassifyRelationship);


    int updateRelationship(@Param("actionClassifyId") long actionClassifyId, @Param("disposeActionId") long disposeActionId,
                           @Param("labelId") long labelId, @Param("type") int type);


    int deleteRelationshipByLabelId(@Param("labelId") long labelId);


    List<CfReportClassifyRelationship> getByLabelId(@Param("labelId") long labelId);


    List<CfReportClassifyRelationship> getByTypeAndLabelIds(@Param("type")int type, @Param("labels")List<Integer> labels);

}
