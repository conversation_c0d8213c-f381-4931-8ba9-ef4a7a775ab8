package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplatize;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date : 2018/9/10 17:08
 */
@DataSource(DS.CF)
public interface AdminCfBaseInfoTemplatizeDao {

    List<CfBaseInfoTemplatize> selectByPage(@Param("context") String context, @Param("contentType") int contentType,
                                            @Param("relationType") int relationType, @Param("channelType") int channelType,
                                            @Param("ids") List<Integer> ids);

    CfBaseInfoTemplatize selectById(@Param("id") long id);

    int insertOne(CfBaseInfoTemplatize cfBaseInfoTemplatize);

    int update(CfBaseInfoTemplatize cfBaseInfoTemplatize);

    int delete(@Param("id") long id);

    int get1v1TemplateCount(@Param("context") String context, @Param("contentType") int contentType,
                            @Param("relationType") Integer relationType, @Param("channelType") int channelType,
                            @Param("ids") List<Integer> ids, @Param("diseaseName") String diseaseName, @Param("age") Integer age);

    List<CfBaseInfoTemplatize> get1v1TemplateList(@Param("context") String context, @Param("contentType") int contentType,
                                                  @Param("relationType") Integer relationType, @Param("channelType") int channelType,
                                                  @Param("ids") List<Integer> ids, @Param("diseaseName") String diseaseName, @Param("age") Integer age,
                                                  @Param("offset") int offset, @Param("pageSize") int pageSize);
}
