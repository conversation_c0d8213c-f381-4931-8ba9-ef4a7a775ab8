package com.shuidihuzhu.cf.dao.amount;

import com.shuidihuzhu.cf.client.adminpure.model.amount.CfAmountReasonableTask;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2022/8/25 10:23
 * @Description:
 */
@DataSource("shuidiCfAdminDataSource")
public interface CfAmountReasonableTaskDao {

    int insert(CfAmountReasonableTask cfAmountReasonableTask);

    List<CfAmountReasonableTask> getByCaseIdAndTaskType(@Param("caseId") int caseId, @Param("taskType") int taskType);

    CfAmountReasonableTask getByTaskInfoId(@Param("taskInfoId") String taskInfoId);

    int updateCfAmountReasonableTask(CfAmountReasonableTask cfAmountReasonableTask);

    CfAmountReasonableTask selectById(@Param("id") long id);

    List<CfAmountReasonableTask> getByCaseId(@Param("caseId") int caseId);

}
