package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingReportRecordVo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReportRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by niejiangnan on 2017/8/5.
 */
@DataSource(AdminDS.CF_RW)
public interface CrowdfundingReportRecordDao {

    int add(CrowdfundingReportRecord crowdfundingReportRecord);


    List<CrowdfundingReportRecord> getreportRecordListByReportIds(List<Integer> reportIds);


    List<CrowdfundingReportRecord> getreportRecordList();


    List<CrowdfundingReportRecordVo> getReportRecordGroupByReportId(@Param("reportIds") List<Integer> reportIds);
}
