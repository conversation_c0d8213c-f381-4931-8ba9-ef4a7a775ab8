package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfSendProveTemplate;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface CfSendProveTemplateDao {

    List<CfSendProveTemplate> findByCaseIdAndAuditStatus(@Param("caseId") int caseId,
                                                         @Param("auditStatus") List<Integer> auditStatus);

    int batchInsert(@Param("cfSendProveTemplates") List<CfSendProveTemplate> cfSendProveTemplates);


    List<CfSendProveTemplate> findByCaseIdAndProveId(@Param("caseId") int caseId,
                                                     @Param("proveId") long proveId);

    int updateAuditStatus(@Param("caseId") int caseId,
                          @Param("proveId") long proveId,
                          @Param("templateId") long templateId,
                          @Param("auditStatus") int auditStatus);

    int updateAllAuditStatus(@Param("caseId") int caseId,
                             @Param("proveId") long proveId,
                             @Param("auditStatus") int auditStatus);

    int updateById(CfSendProveTemplate cfSendProveTemplate);

    CfSendProveTemplate findByCaseIdAndProveIdAndTemplateId(@Param("caseId") int caseId,
                                                            @Param("proveId") long proveId,
                                                            @Param("actionId") long actionId,
                                                            @Param("templateId") long templateId);

    int insertOne(CfSendProveTemplate cfSendProveTemplate);

    int updateAuditStatusAndContent(@Param("caseId") int caseId,
                                    @Param("proveId") long proveId,
                                    @Param("templateId") long templateId,
                                    @Param("auditStatus") int auditStatus,
                                    @Param("content") String content);
}
