package com.shuidihuzhu.cf.dao.crowdfunding;

import java.util.List;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import org.apache.ibatis.annotations.Param;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoStatus;

@DataSource(AdminDS.CF_RW)
public interface CrowdfundingInfoOldStatusDao {

	int add(CrowdfundingInfoStatus crowdfundingInfoStatus);


	List<CrowdfundingInfoStatus> getByInfoUuid(@Param("infoUuid") String infoUuid);
	
}
