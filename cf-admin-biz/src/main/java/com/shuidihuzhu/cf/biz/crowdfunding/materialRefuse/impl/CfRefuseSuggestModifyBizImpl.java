package com.shuidihuzhu.cf.biz.crowdfunding.materialRefuse.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.materialRefuse.CfRefuseSuggestModifyBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.materialAudit.CfRefuseSuggestModifyDao;
import com.shuidihuzhu.cf.model.crowdfunding.material.CfRefuseSuggestModify;
import com.shuidihuzhu.cf.vo.v5.MaterialModifySuggestType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;


@Service
@Slf4j
public class CfRefuseSuggestModifyBizImpl implements CfRefuseSuggestModifyBiz {


    @Override
    public List<CfRefuseSuggestModify> selectSuggestModifyByDateTypes(Collection<Integer> dataTypes) {
        List<CfRefuseSuggestModify> result = Lists.newArrayList();

        if (CollectionUtils.isEmpty(dataTypes)) {
            return result;
        }

        for (MaterialModifySuggestType suggestType : MaterialModifySuggestType.values()) {
            if (dataTypes.contains(suggestType.getDataType())) {
                result.add(CfRefuseSuggestModify.buildFromSuggestType(suggestType));
            }
        }
        return result;
    }

    @Override
    public Map<Integer, CfRefuseSuggestModify> selectSuggestMapping(Collection<Integer> uniqueIds) {
        Map<Integer, CfRefuseSuggestModify> result = Maps.newHashMap();

        if (CollectionUtils.isEmpty(uniqueIds)) {
            return result;
        }

        List<CfRefuseSuggestModify> modifyList = selectSuggestModifyByIds(uniqueIds);
        for (CfRefuseSuggestModify modify : modifyList) {
            result.put(modify.getUniqueId(), modify);
        }
        return result;
    }

    @Override
    public List<CfRefuseSuggestModify> selectSuggestModifyByIds(Collection<Integer> uniqueIds) {
        List<CfRefuseSuggestModify> result = Lists.newArrayList();

        if (CollectionUtils.isEmpty(uniqueIds)) {
            return Lists.newArrayList();
        }

        for (MaterialModifySuggestType suggestType : MaterialModifySuggestType.values()) {
            if (uniqueIds.contains(suggestType.getCode())) {
                result.add(CfRefuseSuggestModify.buildFromSuggestType(suggestType));
            }
        }

        return result;
    }


}
