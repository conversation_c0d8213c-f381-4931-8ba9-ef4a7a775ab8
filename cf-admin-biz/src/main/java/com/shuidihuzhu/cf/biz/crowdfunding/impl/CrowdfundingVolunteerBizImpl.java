package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingVolunteerBiz;
import com.shuidihuzhu.cf.delegate.other.IOtherDelegate;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.delegate.volunteer.IVolunteerDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.client.baseservice.msg.v2.MsgClientV2;
import com.shuidihuzhu.client.cf.growthtool.model.*;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by lixiaoshuang on 2018/2/27.
 */
@Service
@Slf4j
public class CrowdfundingVolunteerBizImpl implements CrowdfundingVolunteerBiz {
    @Autowired
    private IVolunteerDelegate volunteerDelegate;
    @Autowired
    private IOtherDelegate otherDelegate;
    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Resource
    private MsgClientV2Service msgClientV2Service;


    @Override
    public List<CrowdfundingVolunteer> getVolunteerNew(Integer pageNum, Integer pageSize, VolunteerSearchModel volunteerSearchModel) {
        if (pageNum < 0 || pageSize < 0) {
            return new ArrayList<>();
        }
        PageReturnModel<CrowdfundingVolunteer> pageReturnModel = volunteerDelegate.getVolunteer(volunteerSearchModel, true, pageNum, pageSize);
        Page page = new Page(pageNum,pageSize);
        page.addAll(pageReturnModel!=null?pageReturnModel.getList():Lists.newArrayList());
        page.setTotal(pageReturnModel!=null?pageReturnModel.getTotal():0);
        return page;
    }

    @Override
    public Response<Integer> addVolunteer(CrowdfundingVolunteer crowdfundingVolunteer) {
        if (null == crowdfundingVolunteer) {
            return NewResponseUtil.makeFail("人员信息为空");
        }
        return volunteerDelegate.addVolunteer(crowdfundingVolunteer);
    }

    @Override
    public CrowdfundingVolunteerVo getVolunteerInfo(long id) {
        CrowdfundingVolunteerVo volunteerInfo = volunteerDelegate.getCrowdfundingVolunteerVoById(id);
        if (volunteerInfo==null){
            return null;
        }
        CfVolunteerMaterialDO volunteerMateri = null==volunteerInfo?null: otherDelegate.getVolunteerMateri(volunteerInfo.getUniqueCode());
        volunteerInfo.setCrowdfundingVolunteerInfo(volunteerMateri);
        return volunteerInfo;
    }

    @Override
    public Response<Integer> updateVolunteerInfo(CrowdfundingVolunteer crowdfundingVolunteer) {
        return volunteerDelegate.updateVolunteerInfoById(crowdfundingVolunteer);
    }

    @Override
    public String getQRCode(long id) {
        CrowdfundingVolunteerVo crowdfundingVolunteerVo = volunteerDelegate.getCrowdfundingVolunteerVoById(id);
        return crowdfundingVolunteerVo!=null?crowdfundingVolunteerVo.getQrCode():"";
    }

    @Override
    public int updateFields(String qrCode, long id) {
        return volunteerDelegate.updateQrCode(qrCode, id);
    }

    @Override
    public List<CrowdfundingVolunteer> getVolunteerName(List<String> uniqueCodes) {
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            return Lists.newArrayList();
        }
        return volunteerDelegate.getCfVolunteerDOByUniqueCodes(uniqueCodes);
    }

    @Override
    public String getUniqueCodeById(long id) throws Exception {
        CrowdfundingVolunteerVo crowdfundingVolunteerVo = volunteerDelegate.getCrowdfundingVolunteerVoById(id);
        if (crowdfundingVolunteerVo!=null){
            return crowdfundingVolunteerVo.getUniqueCode();
        }
        throw new Exception("根据id 找不到筹款顾问信息");
    }


    @Override
    public CrowdfundingVolunteer getByUniqueCode(String volunteerUniqueCode) {
        List<CrowdfundingVolunteer> volunteers = getVolunteerName(Lists.newArrayList(volunteerUniqueCode));
        if (CollectionUtils.isEmpty(volunteers)) {
            return null;
        }
        return volunteers.get(0);
    }

    private boolean isValidOffLineBd(CrowdfundingVolunteer volunteer) {
        if (volunteer == null ||
                volunteer.getVolunteerType() != CrowdfundingVolunteerEnum.volunteerType.TEAM.getValue()
                || volunteer.getWorkStatus() == CrowdfundingVolunteerEnum.workStatusEnum.DIMISSIOM.getValue()
                || volunteer.getIsDelete() == 1) {
            return false;
        }
        return true;
    }


    @Override
    public boolean checkVolunteerByUserId(long userId) {
        UserInfoModel u = userInfoServiceBiz.getUserInfoByUserId(userId);
        if (u == null) {
            return false;
        }
        String mobile = u.getCryptoMobile();
        List<String> v = volunteerDelegate.getVolunteerUniqueByMobileAndWorkStatus(
                CrowdfundingVolunteerEnum.workStatusEnum.ON_THE_JOB.getValue(),
                mobile
        );
        return CollectionUtils.isNotEmpty(v);
    }

    @Override
    public int addCfVolunteerMaterial(CfVolunteerMaterialDO cfVolunteerMaterialDO){
        return otherDelegate.addCfVolunteerMaterial(cfVolunteerMaterialDO);
    }
    @Override
    public int updateCfVolunteerMaterial(CfVolunteerMaterialDO cfVolunteerMaterialDO){
        return otherDelegate.updateCfVolunteerMaterial(cfVolunteerMaterialDO);
    }
    @Override
    public int updateApplyStatusById(long id,
                                     int applyStatus,
                                     String operatorName,
                                     int operatorUserId,
                                     String angelUrl,
                                     String refuseReasons,
                                     String qrCode){
        return volunteerDelegate.updateApplyStatusById(id, applyStatus, operatorName, operatorUserId, angelUrl, refuseReasons, qrCode);
    }
    @Override
    public CfVolunteerMaterialDO getCfVolunteerMaterialDOByUniqueCode(String uniqueCode){
        return otherDelegate.getVolunteerMateri(uniqueCode);
    }

    @Override
    public void send553Sms(List<String> mobiles, String qrCode) {

        Map<String, Map<Integer, String>> mapMap = Maps.newHashMap();
        for (String mobile : mobiles) {
            Map<Integer, String> param = Maps.newHashMap();
            param.put(1, qrCode);
            mapMap.put(mobile, param);
        }

        String modelNum0 = "553duanxin";
        msgClientV2Service.sendSmsParamsMsg(modelNum0, mapMap, false);
    }
}
