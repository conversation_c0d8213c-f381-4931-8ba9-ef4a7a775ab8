package com.shuidihuzhu.cf.biz.crowdfunding.report.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.report.CfReportOfficialLetterBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfReportOfficialLetterDao;
import com.shuidihuzhu.cf.model.report.CfReportOfficialLetter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-05-21 14:59
 **/
@Service
public class CfReportOfficialLetterBizImpl implements CfReportOfficialLetterBiz {
    @Autowired
    private CfReportOfficialLetterDao cfReportOfficialLetterDao;

    @Override
    public int insertOne(CfReportOfficialLetter cfReportOfficialLetter) {
        return cfReportOfficialLetterDao.insertOne(cfReportOfficialLetter);
    }

    @Override
    public List<CfReportOfficialLetter> getByCaseId(int caseId) {
        return cfReportOfficialLetterDao.getByCaseId(caseId);
    }

    @Override
    public int update(CfReportOfficialLetter cfReportOfficialLetter) {
        return cfReportOfficialLetterDao.update(cfReportOfficialLetter);
    }

    @Override
    public CfReportOfficialLetter getById(long id) {
        return cfReportOfficialLetterDao.getById(id);
    }

    @Override
    public List<CfReportOfficialLetter> getLastByCaseId(int caseId) {
        return cfReportOfficialLetterDao.getLastByCaseId(caseId);
    }
}
