package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.aiphoto.ImageWatermarkService;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdFundingProgressBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingCommentBiz;
import com.shuidihuzhu.cf.client.feign.CfAttachmentFeignClient;
import com.shuidihuzhu.cf.client.feign.CfCommonFeignClient;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdFundingProgressDao;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.domain.risk.RiskUgcVerifyDO;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfSensitiveWordRecordEnum;
import com.shuidihuzhu.cf.enums.risk.UgcTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCommentView;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReportLabel;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdFundingProgressVo;
import com.shuidihuzhu.cf.service.crowdfunding.CfRiskService;
import com.shuidihuzhu.cf.vo.crowdfunding.CfProgressVo;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.api.client.CfImageMaskFeignClient;
import com.shuidihuzhu.client.cf.risk.client.CfRiskBlackListClient;
import com.shuidihuzhu.client.cf.risk.model.CfBlacklistBaseResult;
import com.shuidihuzhu.client.cf.risk.model.enums.CfRiskBlackListEnum;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.model.MaskAttachmentVo;
import com.shuidihuzhu.client.model.enums.ImageMaskBizEnum;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.MobileUtil;
import com.shuidihuzhu.cf.vo.crowdfunding.AdminCrowdfundingProgress;
import com.shuidihuzhu.common.web.util.MobileUtil;
import com.shuidihuzhu.common.web.util.admin.BasicExample;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by chao on 16/7/12.
 */

@Service
public class AdminCrowdFundingProgressBizImpl implements AdminCrowdFundingProgressBiz {

	@Autowired
	private AdminCrowdFundingProgressDao progressDao;
	@Autowired
	private AdminCrowdfundingCommentBiz adminCrowdfundingCommentBiz;
	@Resource
	private UserInfoServiceBiz userInfoServiceBiz;
	@Autowired
	private CfCommonFeignClient cfCommonFeignClient;
	@Autowired
	private CfWorkOrderClient cfWorkOrderClient;
	@Autowired
	private CfRiskService cfRiskService;
	@Autowired
	private CfAttachmentFeignClient attachmentClient;
	@Autowired
	private ImageWatermarkService watermarkService;
	@Resource
	private CfImageMaskFeignClient cfImageMaskFeignClient;
	@Autowired
	private ShuidiCipher shuidiCipher;
	@Override
	public List<CfProgressVo> getProgressOfActivity(List<CrowdFundingProgress> crowdFundingProgressList) {
		if(CollectionUtils.isEmpty(crowdFundingProgressList)){
			return Collections.emptyList();
		}

		Map<Long, List<MaskAttachmentVo>> maskMap = buildMaskImageMap(crowdFundingProgressList);

		List<Long> userIdList = Lists.newArrayList();
		List<Long> parentIdList = Lists.newArrayList();
		for (CrowdFundingProgress crowdFundingProgress : crowdFundingProgressList) {
			parentIdList.add(Long.valueOf(crowdFundingProgress.getId()));
			userIdList.add(crowdFundingProgress.getUserId());
		}
		Map<Long, UserInfoModel> userAccountMap = new HashMap<>();
		List<UserInfoModel> userInfoModelList = this.userInfoServiceBiz.getUserInfoByUserIdBatch(userIdList);
		if (CollectionUtils.isNotEmpty(userInfoModelList)) {
			for (UserInfoModel userInfoModel : userInfoModelList) {
				userAccountMap.put(userInfoModel.getUserId(), userInfoModel);
			}
		}
		Map<Long, List<CrowdfundingCommentView>> crowdfundingCommentMap = this.adminCrowdfundingCommentBiz
				.getCrowdfundingTrendsCommentsByParentIdList(parentIdList, 0, 100);
		
		List<CfProgressVo> resultList = Lists.newArrayListWithCapacity(crowdFundingProgressList.size());
		crowdFundingProgressList.forEach(crowdFundingProgress -> {
			RiskUgcVerifyDO verifyDO = cfCommonFeignClient.queryUgcVerify(crowdFundingProgress.getActivityId(), UgcTypeEnum.PROGRESS.getValue(), crowdFundingProgress.getId()).getData();
			boolean isBlackList = cfRiskService.queryBlackValid(crowdFundingProgress.getUserId(), CfRiskBlackListEnum.LimitType.UGC);
			boolean seeOnlySelf = Objects.nonNull(verifyDO) || isBlackList;

			List<WorkOrderExt> workOrderExts = cfWorkOrderClient.queryWorkOrderByCaseAndType(crowdFundingProgress.getActivityId(), WorkOrderType.ugcprogress.getType(), String.valueOf(crowdFundingProgress.getId()), String.valueOf(CfSensitiveWordRecordEnum.BizType.PROGRESS.value())).getData();
			long workOrderId = CollectionUtils.isNotEmpty(workOrderExts) ? workOrderExts.get(0).getWorkOrderId() : 0;

			CfProgressVo crowdFundingProgressVo = new CfProgressVo();
			BeanUtils.copyProperties(crowdFundingProgress, crowdFundingProgressVo);
			long userId = crowdFundingProgress.getUserId();
			UserInfoModel progressUserAccount = userAccountMap.get(userId);
			if (progressUserAccount != null) {
				String nickname = progressUserAccount.getNickname();
				String maskNickName = "";
	        	if(StringUtils.isBlank(nickname)) {
	        		maskNickName = MobileUtil.getAppropriateNickname(nickname, shuidiCipher.decrypt(progressUserAccount.getCryptoMobile()));
	        	} else {
	        		maskNickName = nickname;
	        	}
	        	crowdFundingProgressVo.setUserName(maskNickName);
	        	crowdFundingProgressVo.setHeadImageUrl(progressUserAccount.getHeadImgUrl());
			}
			//防止返回null
			if (StringUtils.isEmpty(crowdFundingProgressVo.getImageUrls())){
				crowdFundingProgressVo.setImageUrls("");
			}

			// 填充掩码图片
			crowdFundingProgressVo.setMaskImageUrls("");
			if (maskMap.containsKey(Long.valueOf(crowdFundingProgress.getId()))) {
				List<MaskAttachmentVo> maskAttachmentVos = maskMap.get(Long.valueOf(crowdFundingProgress.getId()));
				List<String> maskImageUrls = maskAttachmentVos.stream().map(MaskAttachmentVo::getAiImageUrl).collect(Collectors.toList());
				String maskImages = Joiner.on(",").join(maskImageUrls);
				crowdFundingProgressVo.setMaskImageUrls(StringUtils.isBlank(maskImages) ? "" : maskImages);
			}

			List<CrowdfundingCommentView> comments = crowdfundingCommentMap.get(Long.valueOf(crowdFundingProgress.getId()));
			crowdFundingProgressVo.setComments(comments);
			crowdFundingProgressVo.setStatus(seeOnlySelf ? 1 : 0);
			crowdFundingProgressVo.setWorkOrderId(workOrderId);

			resultList.add(crowdFundingProgressVo);
		});

		watermarkService.fillProgressWatermarks(resultList);
		return resultList;
	}

    @Override
	public List<CrowdFundingProgress> queryAllByBasicExample(BasicExample basicExample, int current, int pageSize) {
		PageHelper.startPage(current, pageSize);
		return progressDao.getActivityProgress(basicExample);
    }

	@Override
	public List<CrowdFundingProgress> queryAllByBasicExample(BasicExample basicExample) {
		return progressDao.getActivityProgress(basicExample);
	}

    @Override
    public Integer selectCountByFiveMin(Timestamp begin, Timestamp end) {
        return progressDao.selectCountByFiveMin(begin, end);
    }

    @Override
    public int updateImageUrls(String imageUrls, int id, int crowdfundingId) {
        int result = progressDao.updateImageUrls(imageUrls, id, crowdfundingId);

		attachmentClient.saveAttachmentByImages(crowdfundingId, imageUrls, AttachmentTypeEnum.ATTACH_PROGRESS);

        return result;
    }

	@Override
	public int updateImageUrlsByNoIsDelete(String imageUrls, int id, int crowdfundingId) {
		int result = progressDao.updateImageUrlsByNoIsDelete(imageUrls, id, crowdfundingId);

		attachmentClient.saveAttachmentByImages(crowdfundingId, imageUrls, AttachmentTypeEnum.ATTACH_PROGRESS);

		return result;
	}

	@Override
    public CrowdFundingProgress getActivityProgressById(long id) {
        return progressDao.getActivityProgressById(id);
    }

    @Override
    public int updateContent(long id, String content) {
        return progressDao.updateContent(id, content);
    }

	@Override
	public List<CrowdFundingProgress> getByCreateTime(Timestamp startTime, Timestamp endTime) {
		return this.progressDao.getByCreateTime(startTime, endTime);
	}

	@Override
	public List<CrowdFundingProgress> getListByIds(List<Integer> ids) {
		return this.progressDao.getByIds(ids);
	}

	@Override
	public Map<Integer, CrowdFundingProgress> getMapByIds(List<Integer> ids) {
		if (CollectionUtils.isEmpty(ids)) {
			return Collections.emptyMap();
		}
		List<CrowdFundingProgress> crowdFundingProgressList = getListByIds(ids);
		if(CollectionUtils.isEmpty(crowdFundingProgressList)) {
			return Collections.emptyMap();
		}
		return crowdFundingProgressList.stream().collect(Collectors.toMap(CrowdFundingProgress::getId, Function.identity()));
	}


	@Override
	public AdminCrowdfundingProgress getProgressById(Integer progresssId) {
		return progressDao.getProgressById(progresssId);

	}

	@Override
	public int insertInCrowdfundingProgress(CrowdFundingProgress crowdFundingProgress) {
		if (crowdFundingProgress == null) {
			return 0;
		}
		int result = progressDao.insertInCrowdfundingProgress(crowdFundingProgress);
		attachmentClient.saveAttachmentByImages(crowdFundingProgress.getActivityId(), crowdFundingProgress.getImageUrls(),
				AttachmentTypeEnum.ATTACH_PROGRESS);
		return result;
	}

	@Override
	public int delProgressById(long id) {
		return progressDao.delProgressById(id);
	}

	@Override
	public int reviveProgressById(long id) {
		return progressDao.reviveProgressById(id);
	}

	private Map<Long, List<MaskAttachmentVo>> buildMaskImageMap(List<CrowdFundingProgress> crowdFundingProgressList) {

		List<Long> bizIds = crowdFundingProgressList.stream()
				.map(CrowdFundingProgress::getId)
				.map(Long::valueOf)
				.collect(Collectors.toList());
		Response<List<MaskAttachmentVo>> response = cfImageMaskFeignClient.queryMaskImage(bizIds, Lists.newArrayList(ImageMaskBizEnum.CF_PROGRESS_IMAGE.getCode()));
		List<MaskAttachmentVo> maskAttachmentVos = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList());
		if (CollectionUtils.isEmpty(maskAttachmentVos)) {
			return Maps.newHashMap();
		}

		return maskAttachmentVos.stream()
				.filter(f -> StringUtils.isNotBlank(f.getAiImageUrl()))
				.filter(f -> f.getIsDelete() != 1)
				.collect(Collectors.groupingBy(MaskAttachmentVo::getBizId));

	}
}
