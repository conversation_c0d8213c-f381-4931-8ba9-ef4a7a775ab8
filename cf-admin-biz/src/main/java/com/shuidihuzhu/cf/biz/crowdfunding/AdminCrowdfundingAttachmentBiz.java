package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * Created by ahrievil on 2017/5/8.
 */
public interface AdminCrowdfundingAttachmentBiz {

    List<CrowdfundingAttachment> getByParentId(int parentId);

    List<CrowdfundingAttachment> queryAttachment(int parentId);

    List<CrowdfundingAttachment> getAttachmentsByType(int parentId, AttachmentTypeEnum type);

    List<CrowdfundingAttachment> getListByInfoIdListAndType(List<Integer> parentIdList, AttachmentTypeEnum type);

    Map<Integer, List<CrowdfundingAttachment>> getMapByInfoIdListAndType(List<Integer> parentIdList, AttachmentTypeEnum type);


    List<CrowdfundingAttachment> getAttachmentsByParentIdAndType(int parentId, AttachmentTypeEnum type);

    int deleteByIds(List<Integer> ids, int parentId);

    void shieldedAccessFromOss(CrowdfundingAttachment attachment);

    void updateTitleImgAfterDeleteImg(CrowdfundingAttachment attachment);

    void updateTitleImgAfterUploadImg(CrowdfundingAttachment attachment);

    CrowdfundingAttachment getAttachmentById(Integer parentId, Integer id);

    Map<Integer, Integer> getImageWatermarkByAttachments(List<CrowdfundingAttachment> attachments);

    Map<Integer, Integer> getImageWatermarkByIds(List<Integer> ids);

    Map<String, Integer> getImagesTWatermark(int caseId, List<AttachmentTypeEnum> enumList);

    Map<String, Integer> getImagesTWatermark(List<Integer> caseIds, List<AttachmentTypeEnum> enumList);

    Map<String, Integer> getImageUrlWatermarkByAttachments(List<CrowdfundingAttachment> allAttachments);

    boolean canNotShowInUgcManage(AttachmentTypeEnum type);

    void addImageToBaseStage(int caseId, AttachmentTypeEnum attachmentType, String addImageUrl);

    void deleteImageFromBaseStage(int caseId, CrowdfundingAttachment attachment);


    List<CrowdfundingAttachment> getAttachmentsByTypes(int parentId, List<Integer> types);

    List<CrowdfundingAttachment> getAttachmentsByCaseIdsWithDelete(List<Integer> parentId, List<Integer> type);

    List<CrowdfundingAttachment> getAttachmentsByIdList(List<Integer> idList, int parentId);
}
