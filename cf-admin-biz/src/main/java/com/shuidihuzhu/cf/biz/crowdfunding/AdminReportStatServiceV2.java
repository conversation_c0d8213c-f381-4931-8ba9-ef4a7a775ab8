package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.report.ReportStatTotal;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

public interface AdminReportStatServiceV2 {

    ReportStatTotal.ReportWorkOrderTotalCount queryReportTotalCount();

    List<ReportStatTotal.ReportWorkOrderUserProcess> queryReportUserCount();
}
