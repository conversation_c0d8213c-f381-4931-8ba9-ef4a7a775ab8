package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.AdminCasePageAnnouncementManageVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface AdminCasePageAnnouncementManageBiz {


    List<AdminCasePageAnnouncementManageVo> getList(int current, int pageSize, String title, int status, int top, int type);

    int addOrUpdate(long id, String title, int status, int top, int type, String imgUrl, String popImgUrl, String shortcutUrl, String shortcutUrlDesc, long adminUserId);

    int delete(long id, long adminUserId);

    int updateByOnline(long id, int status, long adminUserId);

    int updateByTop(long id, int top, long adminUserId);

    Map<Integer, String> listBar(String type);

}
