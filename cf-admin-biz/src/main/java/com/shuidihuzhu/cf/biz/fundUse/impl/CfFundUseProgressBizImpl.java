package com.shuidihuzhu.cf.biz.fundUse.impl;

import com.shuidihuzhu.cf.biz.fundUse.CfFundUseProgressBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.NewAdminCfFundUseAuditDao;
import com.shuidihuzhu.cf.vo.crowdfunding.AdminCrowdfundingProgress;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2021/5/20 17:59
 * @Description:
 */
@Service
public class CfFundUseProgressBizImpl implements CfFundUseProgressBiz {

    @Autowired
    private NewAdminCfFundUseAuditDao newAdminCfFundUseAuditDao;

    @Override
    public List<AdminCrowdfundingProgress> selectByProgressIdList(List<Integer> idList) {
        return newAdminCfFundUseAuditDao.selectByProgressIdList(idList);
    }

    @Override
    public int updateContentAndImg(long fundUseProgressId, String content, String imageUrls) {
        return newAdminCfFundUseAuditDao.updateContentAndImg(fundUseProgressId, content, imageUrls);
    }
}
