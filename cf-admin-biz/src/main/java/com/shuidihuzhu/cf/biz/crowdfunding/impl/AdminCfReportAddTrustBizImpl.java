package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.aiphoto.ImageWatermarkService;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfReportAddTrustBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfReportCommitmentInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.IAdminCredibleInfoService;
import com.shuidihuzhu.cf.biz.crowdfunding.report.AdminCfReportProveDisposeActionBiz;
import com.shuidihuzhu.cf.client.feign.CfAttachmentFeignClient;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfReportAddTrustDao;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CredibleTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportAddTrust;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportCommitmentInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfReportAddTrustDisposeVo;
import com.shuidihuzhu.cf.model.report.AdminCfReportAddTrustVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by niejiangnan on 2017/10/23.
 */
@Service
@Slf4j
public class AdminCfReportAddTrustBizImpl implements AdminCfReportAddTrustBiz {

    @Autowired
    private AdminCfReportAddTrustDao adminCfReportAddTrustDao;

    @Autowired
    private IAdminCredibleInfoService adminCredibleInfoService;

    @Autowired
    private CfReportCommitmentInfoBiz cfReportCommitmentInfoBiz;

    @Autowired
    private AdminCfReportProveDisposeActionBiz adminCfReportProveDisposeActionBiz;

    @Autowired
    private ImageWatermarkService watermarkService;

    @Autowired
    private CfAttachmentFeignClient attachmentClient;

    @Autowired
    private AdminCrowdfundingInfoBiz fundingInfoBiz;

    @Override
    public Map<String, Integer> getAuditStausMapByInfoUuids(List<String> infoUuids) {
        if (CollectionUtils.isEmpty(infoUuids)) {
            return null;
        }
        List<CfReportAddTrust> cfReportAddTrustList = adminCfReportAddTrustDao.getListByInfoUuid(infoUuids);
        if (CollectionUtils.isEmpty(cfReportAddTrustList)) {
            return null;
        }
        Map<String,Integer> auditStausMap = cfReportAddTrustList.stream().collect(Collectors.toMap(CfReportAddTrust::getInfoUuid, CfReportAddTrust::getAuditStatus));
        if (!CollectionUtils.isEmpty(auditStausMap)){
            return auditStausMap;
        }
        return null;
    }

    @Override
    public long save(String infoUuid, int auditStatus, String operatorContent, String content, String imageUrls, boolean issuedCommitment) {
        CfReportAddTrust cfReportAddTrust = new CfReportAddTrust();
        if (StringUtils.isBlank(infoUuid)){
            return -1;
        }
        cfReportAddTrust.setInfoUuid(infoUuid);
        cfReportAddTrust.setAuditStatus(auditStatus);
        cfReportAddTrust.setOperatorContent(StringUtils.trimToEmpty(operatorContent));
        cfReportAddTrust.setContent(StringUtils.trimToEmpty(content));
        cfReportAddTrust.setImageUrls(StringUtils.trimToEmpty(imageUrls));
        cfReportAddTrust.setIssuedCommitment(issuedCommitment);
        adminCfReportAddTrustDao.save(cfReportAddTrust);
        updateReportTrustAttach(infoUuid, imageUrls);
        return cfReportAddTrust.getId();
    }

    @Override
    public int update(CfReportAddTrust cfReportAddTrust) {
        //处理增信时，需要一并更新可信信息列表的审核状态和审核时间
        adminCredibleInfoService.updateAuditInfo(cfReportAddTrust.getId(), cfReportAddTrust.getAuditStatus(), CredibleTypeEnum.SUPPLY_VERFIFY.getKey());
        int result = adminCfReportAddTrustDao.update(cfReportAddTrust);

        CfReportAddTrust trust = adminCfReportAddTrustDao.getTrustById(cfReportAddTrust.getId());
        if (trust != null) {
            updateReportTrustAttach(cfReportAddTrust.getInfoUuid(), cfReportAddTrust.getImageUrls());
        }

        return result;
    }

    @Override
    public CfReportAddTrust getByInfoUuid(String infoUuid) {
        if (StringUtils.isNoneBlank(infoUuid)){
            return adminCfReportAddTrustDao.getNewestByInfoUuid(infoUuid);
        }
        return null;
    }

    @Override
    public AdminCfReportAddTrustVo queryById(long id) {
        if (id <= 0) {
            return null;
        }
        CfReportAddTrust cfReportAddTrust = adminCfReportAddTrustDao.queryById(id);
        AdminCfReportAddTrustVo cfReportAddTrustVo = new AdminCfReportAddTrustVo();
        BeanUtils.copyProperties(cfReportAddTrust, cfReportAddTrustVo);
        //检查是否下发了承诺书
        if (cfReportAddTrust.isIssuedCommitment()) {
            CfReportCommitmentInfo cfReportCommitmentInfo = cfReportCommitmentInfoBiz.findByIncrTrustId(cfReportAddTrust.getId());
            if (Objects.nonNull(cfReportCommitmentInfo)) {
                cfReportAddTrustVo.setCfReportCommitmentInfo(cfReportCommitmentInfo);
            }
        }
        // 查询增信处理动作
        String disposeAction = adminCfReportProveDisposeActionBiz.getDisposeAction(id);
        List<CfReportAddTrustDisposeVo> cfReportAddTrustDisposeVos = Lists.newArrayList();
        if (StringUtils.isNotBlank(disposeAction)) {
            try {
                cfReportAddTrustDisposeVos = JSONObject.parseObject(disposeAction, new TypeReference<>() {});//已检查过
            } catch (Exception e) {
                log.error("queryById parse error", e);
            }
        }
        cfReportAddTrustVo.setDisposeAction(cfReportAddTrustDisposeVos);

        watermarkService.fillAddTrustWatermark(cfReportAddTrustVo);
        return cfReportAddTrustVo;
    }

    @Override
    public int delete(String infoUuid) {
        return adminCfReportAddTrustDao.delete(infoUuid);
    }

    private void updateReportTrustAttach(String infoUuid, String imageUrls) {
        CrowdfundingInfo fundingInfo = fundingInfoBiz.getFundingInfo(infoUuid);
        if (fundingInfo != null) {
            attachmentClient.saveAttachmentByImages(fundingInfo.getId(), imageUrls, AttachmentTypeEnum.ATTACH_REPORT_TRUST);
        }
    }
}
