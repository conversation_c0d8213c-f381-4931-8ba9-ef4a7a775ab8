package com.shuidihuzhu.cf.biz.mina.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.mina.AdminCfCommentPriseBiz;
import com.shuidihuzhu.cf.dao.mina.AdminCfCommentPriseDao;
import com.shuidihuzhu.cf.vo.mina.CfIdCountVo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> Ahrievil
 */
@Service
public class AdminCfCommentPriseBizImpl implements AdminCfCommentPriseBiz {

    @Autowired
    private AdminCfCommentPriseDao adminCfCommentPriseDao;
    @Override
    public List<CfIdCountVo> selectPraiseCountByCommentIdList(Set<Long> set) {
        if (CollectionUtils.isEmpty(set)) {
            return Lists.newArrayList();
        }
        return adminCfCommentPriseDao.selectPraiseCountByCommentIdList(set);
    }
}
