package com.shuidihuzhu.cf.biz.risk.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.risk.IUserBehaviorService;
import com.shuidihuzhu.cf.client.feign.CfCommonFeignClient;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.constants.crowdfunding.status.RelationShip;
import com.shuidihuzhu.cf.enums.crowdfunding.UserBehaviorEnum;
import com.shuidihuzhu.cf.model.crowdfunding.AdminUserBehaviorDetail;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.UserInfoDetail;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @time 2019/10/12 下午5:09
 * @desc
 */
@Service
public class UserBehaviorVerifyServiceImpl implements IUserBehaviorService {
    @Autowired
    private CfCommonFeignClient commonFeignClient;

    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;

    @Override
    public UserBehaviorEnum getBehavior() {
        return UserBehaviorEnum.VERIFY;
    }

    @Override
    public List<AdminUserBehaviorDetail> buildBehaviorDetail(String mobile, long userId, String name, String identity, UserInfoModel userInfo, UserBehaviorEnum behaviorEnum) {

        List<CrowdFundingVerification> verifications = commonFeignClient.queryByVerifyUserId(userId).getData();
        if(CollectionUtils.isEmpty(verifications)){
            return Lists.newArrayList();
        }

        UserInfoDetail userInfoDetail = new UserInfoDetail();
        userInfoDetail.setUserId(userId);
        userInfoDetail.setNickName(Objects.nonNull(userInfo) ? userInfo.getNickname() : "");

        List<AdminUserBehaviorDetail> verifyDetails = Lists.newArrayList();

        for (CrowdFundingVerification verification : verifications){

            String infoId = verification.getCrowdFundingInfoId();

            CrowdfundingInfo caseInfo = crowdfundingFeignClient.getCrowdfundingByuuid(infoId).getData();
            if(Objects.isNull(caseInfo)){
                continue;
            }

            RelationShip relationShip = RelationShip.codeOf(Optional.ofNullable(verification.getRelationShip()).orElse(0));

            StringBuilder sb = new StringBuilder();
            sb.append("案例id:").append(caseInfo.getId()).append(REDEX);
            sb.append("案例标题:").append(Objects.nonNull(caseInfo) ? caseInfo.getTitle() : "").append(REDEX);
            sb.append("证实用户姓名:").append(verification.getUserName()).append(REDEX);
            sb.append("与患者关系:").append(Objects.nonNull(relationShip) ?  relationShip.getDescription() : RelationShip.OTHER.getDescription()).append(REDEX);
            sb.append("证实内容:").append(verification.getDescription());

            AdminUserBehaviorDetail verifyDetail = new AdminUserBehaviorDetail();
            verifyDetail.setTime(verification.getCreateTime());
            verifyDetail.setBehaviorType(UserBehaviorEnum.VERIFY.getKey());
            verifyDetail.setUserInfoDetail(userInfoDetail);
            verifyDetail.setUrl(Lists.newArrayList());
            verifyDetail.setBehavoir(Lists.newArrayList(sb.toString().split(REDEX)));

            verifyDetails.add(verifyDetail);
        }


        return verifyDetails;
    }
}
