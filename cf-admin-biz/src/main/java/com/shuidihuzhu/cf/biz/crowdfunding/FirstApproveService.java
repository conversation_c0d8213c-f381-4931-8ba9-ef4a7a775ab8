package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.client.feign.cipher.constants.DesensitizeEnum;
import com.shuidihuzhu.cf.enhancer.utils.MaskCodeUtil;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.vo.CfFirsApproveMaterialVO;
import com.shuidihuzhu.cipher.ShuidiCipher;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FirstApproveService {
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private MaskUtil maskUtil;
    public CfFirsApproveMaterialVO buildWithMaterial(CfFirsApproveMaterial material) {
        CfFirsApproveMaterialVO materialVO = new CfFirsApproveMaterialVO();
        if (material == null) {
            return materialVO;
        }
        materialVO.setId(material.getId());
        materialVO.setInfoUuid(material.getInfoUuid());
        materialVO.setSelfRealName(material.getSelfRealName());
        materialVO.setSelfIdCardMask(maskUtil.buildByEncryptStrAndType(material.getSelfCryptoIdcard(), DesensitizeEnum.IDCARD));
        materialVO.setSelfIdCard(null);
        materialVO.setPatientRealName(material.getPatientRealName());
        materialVO.setPatientIdCardMask(maskUtil.buildByEncryptStrAndType(material.getPatientCryptoIdcard(), DesensitizeEnum.IDCARD));
        materialVO.setPatientIdCard(null);
        materialVO.setRelType(UserRelTypeEnum.getUserRelTypeEnum(material.getUserRelationType()));
        materialVO.setPatientHasIdCard(material.isPatientHasIdCard() ? 1 : 0);
        materialVO.setPreAuditImageUrl(material.getImageUrl());
        materialVO.setTargetAmountDesc(material.getTargetAmountDesc());
        materialVO.setRejectMessage(material.getRejectMessage());
        materialVO.setPoverty(material.getPoverty());
        materialVO.setPovertyImageUrl(material.getPovertyImageUrl());
        materialVO.setPatientBornCard(material.getPatientBornCard());
        materialVO.setPatientIdType(material.getPatientIdType());
        materialVO.setUserRelationTypeForC(material.getUserRelationTypeForC());

        //没有身份证就视为儿童
        if (material.getUserRelationType() != UserRelTypeEnum.SELF.getValue()) {
            materialVO.setChild(!material.isPatientHasIdCard());
        } else {
            materialVO.setChild(false);
        }

        return materialVO;
    }

    public String getDecryptIdCard(String cryptoIdcard) {
        if (StringUtils.isEmpty(cryptoIdcard)) {
            return "";
        }
        String idcard = shuidiCipher.decrypt(cryptoIdcard);
        if (StringUtils.isEmpty(idcard) || idcard.length() < 15) {
            return "";
        }

        return idcard;
    }

    public String getIdcardWithWildchar(String cryptoIdcard) {
        if (StringUtils.isEmpty(cryptoIdcard)) {
            return "";
        }
        String idcard = shuidiCipher.decrypt((cryptoIdcard));
        if (StringUtils.isEmpty(idcard) || idcard.length() < 15) {
            return "";
        }
        return MaskCodeUtil.maskSelfCard(idcard);
    }

    public static void main(String[] args) {
        String idcard = MaskCodeUtil.maskSelfCard("11010119900307401X");
        String phone = MaskCodeUtil.maskPhone("***********");
        String bankCard = MaskCodeUtil.maskDebitCard("6228480402564890018");
        System.out.println(MaskCodeUtil.maskSelfCard(idcard));
    }

}
