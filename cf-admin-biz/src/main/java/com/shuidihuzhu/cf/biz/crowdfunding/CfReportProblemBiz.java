package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.*;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2019-11-08 12:36
 **/
public interface CfReportProblemBiz {
    //######CfReportProblemLabelDao start###
    List<CfReportProblemLabel> listProblemLabelsByIds(List<Integer> labelIds, Integer isUse);

    boolean insertLabel(CfReportProblemLabel label);

    CfReportProblemLabel findLabelByDesc(String desc);

    List<CfReportProblemLabel> listLabels(Integer level, Integer parentId, Integer isUse);


    int updateLabel(String labelDesc,int sort, int id);

    int addProblemClassify(String labelDesc, int sort, int level);

    int updateIsUse(int isUse, int id);

    List<CfReportProblemLabel> getByParentIdAndIsUse(int parentId, int isUse);

    int addProblemModule(String moduleName, int isMandatory, int classifyId, int sort);

    int updateProblemModule(int parentId, String labelDesc, int sort, int isMandatory,  int id);

    CfReportProblemLabel getById(int id);

    //######CfReportProblemLabelDao end###


    //######CfReportProblemDao start###

    boolean updateById(CfReportProblem problem);

    boolean insert(CfReportProblem problem);

    /**
     * 问题管理中的点查
     */
    CfReportProblem findProblemById(int id);

    /**
     * 使用场景：获取下级问题,问题列表管理
     */
    List<CfReportProblem> listByIds(List<Integer> ids);

    /**
     * 获取常驻问题
     */
    List<CfReportProblem> listDirectShow(List<Integer> labelIds, List<Integer> showLocations);

    /**
     * 问题管理列表查询
     */
    List<CfReportProblem> listForManager(String problemDesc, int current, int pageSize, int id, int isUse, Integer collation);


    List<CfReportProblem> listSameLabelProblems(int labelId);

    List<CfReportProblem> listForManager(String problemDesc, Integer isUse);

    List<CfReportProblem> listByLabelIdAndIsUse(int labelId, int isUse);


    //######CfReportProblemDao start###

    int updateUseStatusById(int isUse, int id);

}
