package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Ordering;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCasePageAnnouncementManageBiz;
import com.shuidihuzhu.cf.constants.crowdfunding.AdminCasePageAnnouncementManageConst;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCasePageAnnouncementManageDao;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCasePageAnnouncementManageRecordDao;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCasePageAnnouncementManage;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCasePageAnnouncementManageRecord;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCasePageAnnouncementManageVo;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportFollowComment;
import com.shuidihuzhu.common.web.util.DateUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/7/19  17:00
 */
@Service
public class AdminCasePageAnnouncementManageBizImpl implements AdminCasePageAnnouncementManageBiz {


    @Resource
    private AdminCasePageAnnouncementManageDao adminCasePageAnnouncementManageDao;

    @Resource
    private AdminCasePageAnnouncementManageRecordDao adminCasePageAnnouncementManageRecordDao;

    /**
     * 公告状态
     */
    private final static String STATUS = "status";

    /**
     * 置顶状态
     */
    private final static String TOP = "top";

    /**
     * 公告类型
     */
    private final static String TYPE = "type";


    @Override
    public List<AdminCasePageAnnouncementManageVo> getList(int current, int pageSize, String title, int status, int top, int type) {
        PageHelper.startPage(current, pageSize);
        List<AdminCasePageAnnouncementManageVo> list = adminCasePageAnnouncementManageDao.getList(title, status, top, type);
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }

        for (AdminCasePageAnnouncementManageVo v : list) {
            Pair<Integer, String> onlinePair = AdminCasePageAnnouncementManageConst.ONLINE;
            if (v.getStatus() == onlinePair.getLeft()) {
                List<AdminCasePageAnnouncementManageRecord> recordList = adminCasePageAnnouncementManageRecordDao.getList(v.getId());
                if (CollectionUtils.isNotEmpty(recordList)) {

                    Optional<AdminCasePageAnnouncementManageRecord> optional = recordList.stream()
                            .filter(y -> y.getComment().equals(onlinePair.getRight()))
                            .max(Comparator.comparing(AdminCasePageAnnouncementManageRecord::getId));

                    optional.ifPresent(w -> v.setOnlineTime(DateUtil.formatDate(w.getCreateTime())));
                }
            }
        }

        return list;
    }

    @Override
    public int addOrUpdate(long id, String title, int status, int top, int type, String imgUrl, String popImgUrl, String shortcutUrl, String shortcutUrlDesc, long adminUserId) {

        boolean flag = id <= 0L;

        AdminCasePageAnnouncementManage adminCasePageAnnouncementManage = AdminCasePageAnnouncementManage.builder()
                .id(id)
                .title(title)
                .status(status)
                .top(top)
                .type(type)
                .imgUrl(imgUrl)
                .popImgUrl(popImgUrl)
                .shortcutUrl(shortcutUrl)
                .shortcutUrlDesc(shortcutUrlDesc)
                .build();

        int res = 0;

        //添加 or 修改公共
        if (flag) {
            res = adminCasePageAnnouncementManageDao.add(adminCasePageAnnouncementManage);
        } else {
            res = adminCasePageAnnouncementManageDao.update(adminCasePageAnnouncementManage);
        }

        //保存记录
        if (res > 0) {
            adminCasePageAnnouncementManageRecordDao.add(flag ? "添加" : "修改", adminUserId, adminCasePageAnnouncementManage.getId());

            Pair<Integer, String> onlinePair = AdminCasePageAnnouncementManageConst.ONLINE;
            if (status == onlinePair.getLeft()) {
                adminCasePageAnnouncementManageRecordDao.add(onlinePair.getRight(), adminUserId, adminCasePageAnnouncementManage.getId());
            }

        }

        return res;
    }

    @Override
    public int delete(long id, long adminUserId) {
        if (id <= 0L) {
            return 0;
        }

        int res = adminCasePageAnnouncementManageDao.delete(id);

        //保存记录
        if (res > 0) {
            adminCasePageAnnouncementManageRecordDao.add("删除", adminUserId, id);
        }

        return res;
    }

    @Override
    public int updateByOnline(long id, int status, long adminUserId) {
        int res = adminCasePageAnnouncementManageDao.updateByOnline(id, status);

        //保存记录
        if (res > 0) {

            Pair<Integer, String> onlinePair = AdminCasePageAnnouncementManageConst.ONLINE;
            Pair<Integer, String> linePair = AdminCasePageAnnouncementManageConst.LINE;

            String comment = onlinePair.getRight();
            if (linePair.getLeft() == status) {
                comment = linePair.getRight();
            }

            adminCasePageAnnouncementManageRecordDao.add(comment, adminUserId, id);
        }

        return res;
    }

    @Override
    public int updateByTop(long id, int top, long adminUserId) {
        int res = adminCasePageAnnouncementManageDao.updateByTop(id, top);

        //保存记录
        if (res > 0) {

            Pair<Integer, String> topPair = AdminCasePageAnnouncementManageConst.TOP;
            Pair<Integer, String> nonTopPair = AdminCasePageAnnouncementManageConst.NON_TOP;

            String comment = topPair.getRight();
            if (nonTopPair.getLeft() == top) {
                comment = nonTopPair.getRight();
            }

            adminCasePageAnnouncementManageRecordDao.add(comment, adminUserId, id);
        }

        return res;
    }

    @Override
    public Map<Integer, String> listBar(String type) {

        Map<Integer, String> map = Maps.newHashMap();

        if (STATUS.equals(type)) {
            Pair<Integer, String> onlinePair = AdminCasePageAnnouncementManageConst.ONLINE;
            Pair<Integer, String> linePair = AdminCasePageAnnouncementManageConst.LINE;
            map.put(onlinePair.getLeft(), onlinePair.getRight());
            map.put(linePair.getLeft(), linePair.getRight());
        } else if (TOP.equals(type)) {
            Pair<Integer, String> topPair = AdminCasePageAnnouncementManageConst.TOP;
            Pair<Integer, String> nonTopPair = AdminCasePageAnnouncementManageConst.NON_TOP;
            map.put(topPair.getLeft(), topPair.getRight());
            map.put(nonTopPair.getLeft(), nonTopPair.getRight());
        } else if (TYPE.equals(type)) {
            Pair<Integer, String> announcementTypePair = AdminCasePageAnnouncementManageConst.ANNOUNCEMENT_TYPE;
            Pair<Integer, String> rumourTypePair = AdminCasePageAnnouncementManageConst.RUMOUR_TYPE;
            map.put(announcementTypePair.getLeft(), announcementTypePair.getRight());
            map.put(rumourTypePair.getLeft(), rumourTypePair.getRight());
        }

        return map;
    }
}
