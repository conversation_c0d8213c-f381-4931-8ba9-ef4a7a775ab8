package com.shuidihuzhu.cf.biz.risk.impl;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.risk.ISimpleExportService;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingUserDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.model.crowdfunding.material.credit.CreditSupplementModel;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfCreditSupplementVo;
import com.shuidihuzhu.cf.response.OpResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * Created by sven on 2019/6/28.
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class CreditSimpleExportServiceImpl implements ISimpleExportService {

    private final static String PINKUN_CARD = "是否为脱贫户/脱贫人口";
    private final static String HOUSE_COUNT = "房产数量";
    private final static String HOUSE_VALUE = "房产价值（元）";
    private final static String SOCIAL_INSURERANCE= "有无医保";
    private final static String COMMERCIAL_INSURERANCE = "有无商业重疾保险";
    private final static String HOME_INCOME = "患者家庭年收入（元）";
    private final static String HOME_PROPERTY_VALUE = "患者家庭家庭股票价值（元）";

    @Resource
    private ICrowdfundingUserDelegate crowdfundingUserDelegate;

    @Resource
    private IRiskDelegate riskDelegate;

    @Override
    public Map<String, String> getDetail(CfInfoSimpleModel cfInfoSimpleModel) {
        Map<String,String> map = Maps.newHashMap();

        map.put(PINKUN_CARD,UNKOWN);
        map.put(HOUSE_COUNT,UNKOWN);
        map.put(HOUSE_VALUE,UNKOWN);
        map.put(SOCIAL_INSURERANCE,UNKOWN);
        map.put(COMMERCIAL_INSURERANCE,UNKOWN);
        map.put(HOME_INCOME,UNKOWN);
        map.put(HOME_PROPERTY_VALUE,UNKOWN);

        OpResult<CreditSupplementModel> opResult = crowdfundingUserDelegate.getCreditSupplementByCaseId(cfInfoSimpleModel.getId());
        if(!opResult.isSuccess()){
            return map;
        }

        CfFirsApproveMaterial cfFirsApproveMaterial = riskDelegate.getCfFirsApproveMaterialByInfoId(cfInfoSimpleModel.getId());
        if(cfFirsApproveMaterial != null){
            switch (cfFirsApproveMaterial.getPoverty()){
                case 0:
                    map.put(PINKUN_CARD,UNKOWN);
                    break;
                case 1:
                    map.put(PINKUN_CARD,"是");
                    break;
                case 2:
                    map.put(PINKUN_CARD,"否");
                    break;
                default:
                    break;
            }
        }

        CreditSupplementModel creditSupplementModel = opResult.getData();

        CfCreditSupplementVo houseCredit = getHouse(creditSupplementModel.getCreditSupplements());
        if(houseCredit !=null){
            map.put(HOUSE_COUNT,houseCredit.getCount()+"");
            map.put(HOUSE_VALUE,houseCredit.getTotalValue()+"");
        }

        map.put(SOCIAL_INSURERANCE,  getHealthInsurance(creditSupplementModel.getHealthInsurance()));
        map.put(COMMERCIAL_INSURERANCE, getHealthInsurance(creditSupplementModel.getCommercialInsurance()));
        map.put(HOME_INCOME,creditSupplementModel.getHomeIncome()==-1?UNKOWN:creditSupplementModel.getHomeIncome()+"");
        map.put(HOME_PROPERTY_VALUE, creditSupplementModel.getHomeStock() == -1?UNKOWN:creditSupplementModel.getHomeStock()+"");

        return map;
    }

    private CfCreditSupplementVo getHouse(List<CfCreditSupplementVo> creditSupplements) {
        if(CollectionUtils.isEmpty(creditSupplements)){
            return null;
        }
        for(CfCreditSupplementVo cfCreditSupplementVo : creditSupplements){
            //房产
            if(cfCreditSupplementVo.getPropertyType() == 1){
                return cfCreditSupplementVo;
            }
        }
        return null;
    }

    private String getHealthInsurance(Integer healthInsurance) {
        if(healthInsurance == null){
            return UNKOWN;
        }

        switch (healthInsurance){
            case -1:
                return UNKOWN;
            case 0:
                return "没有";
            case 1:
                return "有";
            default:
                return UNKOWN;
        }
    }

    @Override
    public String getCategory() {
        return "增信";
    }

    @Override
    public int getOrder() {
        return 6;
    }
}
