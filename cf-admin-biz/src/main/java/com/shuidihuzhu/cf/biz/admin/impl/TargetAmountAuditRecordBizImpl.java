package com.shuidihuzhu.cf.biz.admin.impl;

import com.shuidihuzhu.cf.biz.admin.TargetAmountAuditRecordBiz;
import com.shuidihuzhu.cf.dao.record.TargetAmountAuditRecordDao;
import com.shuidihuzhu.cf.model.record.TargetAmountAuditRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/09/18
 */
@Service
public class TargetAmountAuditRecordBizImpl implements TargetAmountAuditRecordBiz {
    @Autowired
    private TargetAmountAuditRecordDao targetAmountAuditRecordDao;
    @Override
    public int add(TargetAmountAuditRecord targetAmountAuditRecord) {
        return targetAmountAuditRecordDao.add(targetAmountAuditRecord);
    }

    @Override
    public TargetAmountAuditRecord getRecordByWorkOrderId(long workOrderId) {
        return targetAmountAuditRecordDao.getRecordByWorkOrderId(workOrderId);
    }

    @Override
    public List<TargetAmountAuditRecord> getRecordByCaseId(long caseId) {
        return targetAmountAuditRecordDao.getRecordByCaseId(caseId);
    }

    @Override
    public int deleteRecordByWorkOrderId(long workOrderId) {
        return targetAmountAuditRecordDao.deleteRecordByWorkOrderIdLogically(workOrderId);
    }
}
