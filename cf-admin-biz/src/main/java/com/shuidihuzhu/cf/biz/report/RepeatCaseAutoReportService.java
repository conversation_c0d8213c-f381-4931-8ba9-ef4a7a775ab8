package com.shuidihuzhu.cf.biz.report;

import com.ctrip.framework.apollo.ConfigService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingReportBiz;
import com.shuidihuzhu.cf.client.apipure.feign.CfFirstApproveMaterialFeignClient;
import com.shuidihuzhu.cf.client.apipure.model.crowdfunding.CfFirstApproveMaterialVO;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.client.feign.CfFirstApproveFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.enums.ReportSourceEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfReportTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.enums.report.ReportPayMethodEnum;
import com.shuidihuzhu.cf.finance.client.feign.CfFinancePauseFeignClient;
import com.shuidihuzhu.cf.finance.enums.CfDrawCashPauseRecordEnum;
import com.shuidihuzhu.cf.finance.model.CfDrawCashPauseRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.ApplicationUtils;
import com.shuidihuzhu.cf.service.crowdfunding.report.CfReportService;
import com.shuidihuzhu.cf.service.report.ReportScheduleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/10/22  3:28 下午
 */
@Service
@Slf4j
public class RepeatCaseAutoReportService {

    @Resource
    private CfFirstApproveMaterialFeignClient cfFirstApproveMaterialFeignClient;

    @Resource
    private CfFirstApproveFeignClient cfFirstApproveFeignClient;

    @Resource
    private AdminCrowdfundingReportBiz adminCrowdfundingReportBiz;

    @Resource
    private ReportScheduleService reportScheduleService;

    @Resource
    private CfFinancePauseFeignClient cfFinancePauseFeignClient;

    @Resource
    private CfReportService cfReportService;

    private Set<Long> testEnableUsers;

    /**
     * apollo.repeat-case-auto-report.test-enable-users
     * 这个配置里的用户 在测试环境 会走自动举报逻辑
     */
    private String KEY_TEST_ENABLE_USERS = "apollo.repeat-case-auto-report.test-enable-users";

    @PostConstruct
    public void init(){
        loadWhiteUsers();
        ConfigService.getAppConfig()
                .addChangeListener(v -> loadWhiteUsers(), Sets.newHashSet(KEY_TEST_ENABLE_USERS));

    }

    private void loadWhiteUsers(){
        String property = ConfigService.getAppConfig().getProperty(KEY_TEST_ENABLE_USERS, "");
        testEnableUsers = Arrays.stream(StringUtils.split(property, ","))
                .map(Long::parseLong)
                .collect(Collectors.toSet());
    }

    public void repeatCaseAutoReport(CrowdfundingInfo crowdfundingInfo){

        int caseId = crowdfundingInfo.getId();

        FeignResponse<CfFirsApproveMaterial> feignResponse =  cfFirstApproveFeignClient.getCfFirstApproveMaterialByCaseId(caseId);
        CfFirsApproveMaterial cfFirsApproveMaterial = Optional.ofNullable(feignResponse).filter(FeignResponse::ok).map(FeignResponse::getData).orElse(null);
        if(Objects.isNull(cfFirsApproveMaterial)){
            log.info("查询初审表信息失败 caseId:{}",caseId);
            return;
        }

        List<CfFirstApproveMaterialVO> cfFirstApproveMaterialVOList = getSingleByCard(cfFirsApproveMaterial);
        int sum = CollectionUtils.size(cfFirstApproveMaterialVOList);
        if (sum == 0 || sum == 1) {
            log.info("没有重复发起 caseId:{}", caseId);
            return;
        }

        boolean flag = false;

        for (CfFirstApproveMaterialVO cfFirstApproveMaterialVO:cfFirstApproveMaterialVOList) {
            int oldCaseId = cfFirstApproveMaterialVO.getCaseId();
            //历史案例是否有举报
            int count = adminCrowdfundingReportBiz.countByInfoId(oldCaseId);
            if(count == 0){
                continue;
            }

            ReportPayMethodEnum payMethodEnum = reportScheduleService.getPayMethodByCaseId(oldCaseId);
            int code = Optional.ofNullable(payMethodEnum).map(ReportPayMethodEnum::getCode).orElse(0);

            //历史案例的举报是否标记对公打款或者分批打款
            if(code == ReportPayMethodEnum.PUBLIC.getCode() ||
                    code == ReportPayMethodEnum.PAY_IN_BATCH.getCode()){
                flag = true;
                break;
            }

            //历史案例是否暂停打款
            com.shuidihuzhu.cf.finance.client.response.FeignResponse<List<CfDrawCashPauseRecord>> response = cfFinancePauseFeignClient.getPauseDrawCashByCaseId(oldCaseId);
            if (!response.notOk() && org.apache.commons.collections.CollectionUtils.isNotEmpty(response.getData())) {
                List<CfDrawCashPauseRecord> cfDrawCashPauseRecordList = response.getData();
                int status = cfDrawCashPauseRecordList.stream().max(Comparator.comparing(CfDrawCashPauseRecord::getId)).map(CfDrawCashPauseRecord::getStatus).get();
                if(status == CfDrawCashPauseRecordEnum.RecordStatusEnum.PAUSE.getCode() ){
                    flag = true;
                    break;
                }
            }

        }

        if (!flag) {
            log.debug("未命中");
            return;
        }
        if (ApplicationUtils.service().isDevelopment() && !testEnableUsers.contains(crowdfundingInfo.getUserId())) {
            log.info("重复发起自动举报 不在测试用户列表中");
            return;
        }

        String reportTypes = String.valueOf(CfReportTypeEnum.multipleFundraising.getCode());
        cfReportService.markReport(caseId, "二次发起案例-暂停打款中", "", "", reportTypes,
                crowdfundingInfo, false, "", 102, 11,
                "系统自动标记", "", ReportSourceEnum.STRATEGY, "");
    }


    /**
     * 通过身份证 or 出生证查询是否有历史案例
     * @param cfFirsApproveMaterial
     * @return
     */
    private List<CfFirstApproveMaterialVO> getSingleByCard(CfFirsApproveMaterial cfFirsApproveMaterial) {
        OperationResult<List<CfFirstApproveMaterialVO>> operationResult = null;

        if (UserIdentityType.identity.getCode() == cfFirsApproveMaterial.getPatientIdType()) {
            operationResult = cfFirstApproveMaterialFeignClient.getSingleByPatientCryptoIdCard(cfFirsApproveMaterial.getPatientCryptoIdcard());
        } else if (UserIdentityType.birth.getCode() == cfFirsApproveMaterial.getPatientIdType()) {
            operationResult = cfFirstApproveMaterialFeignClient.getSingleByPatientBornCard(cfFirsApproveMaterial.getPatientBornCard());
        }

        return Optional.ofNullable(operationResult)
                .filter(OperationResult::isSuccess)
                .map(OperationResult::getData)
                .orElse(Lists.newArrayList());
    }

}
