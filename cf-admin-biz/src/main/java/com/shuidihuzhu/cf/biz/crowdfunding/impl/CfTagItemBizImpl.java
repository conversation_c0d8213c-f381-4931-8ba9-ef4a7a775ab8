package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.CfTagItemBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfTagItemDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfTagItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by ahrievil on 2017/3/27.
 */
@Service
public class CfTagItemBizImpl implements CfTagItemBiz {

    @Autowired
    private CfTagItemDao cfTagItemDao;

    @Override
    public List<CfTagItem> getList() {
        return cfTagItemDao.getList();
    }

    @Override
    public int addOtherTag(List<String> list) {
        return cfTagItemDao.addOtherTag(list);
    }

    @Override
    public List<CfTagItem> getByTagName(List<String> others) {
        return cfTagItemDao.getByTagName(others);
    }

    @Override
    public List<CfTagItem> getByInfoId(int infoId) {
        return cfTagItemDao.getByInfoId(infoId);
    }

    @Override
    public List<CfTagItem> getByInfoIdOther(int infoId) {
        return cfTagItemDao.getByInfoIdOther(infoId);
    }

    @Override
    public List<CfTagItem> selectAllZore() {
        return cfTagItemDao.selectAllZore();
    }

    @Override
    public int insertOne(CfTagItem cfTagItem) {
        return cfTagItemDao.insertOne(cfTagItem);
    }


}
