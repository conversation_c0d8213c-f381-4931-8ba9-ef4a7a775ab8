package com.shuidihuzhu.cf.biz.crmUserManage;

import com.shuidihuzhu.cf.model.CrmUserManage.UserManage;

import java.util.List;

public interface CrmUserManageRecordBiz {

    int addUserManageRecords(List<UserManage.UserManageLog> recordList);

    int addCaseQualityRecord(String uuid, String personId, int raiseCaseQuality, int userId);

    List<UserManage.UserManageLog> buildRelationRecords(List<UserManage.UserAccount> newAddAccounts,
                                                        String version, String extComment);

}
