package com.shuidihuzhu.cf.biz.crowdfunding.report.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.report.ReportCallCommentRecordBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.ReportCallCommentRecordDao;
import com.shuidihuzhu.cf.model.report.ReportCallCommentRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-04-03 17:34
 **/
@Service
public class ReportCallCommentRecordBizImpl implements ReportCallCommentRecordBiz {

    @Autowired
    private ReportCallCommentRecordDao reportCallCommentRecordDao;

    @Override
    public int insertOne(ReportCallCommentRecord reportCallCommentRecord) {
        return reportCallCommentRecordDao.insertOne(reportCallCommentRecord);
    }

    @Override
    public List<ReportCallCommentRecord> getReportCallCommentRecord(int caseId, int reportId, int type) {
        return reportCallCommentRecordDao.getReportCallCommentRecord(caseId, reportId, type);
    }

}
