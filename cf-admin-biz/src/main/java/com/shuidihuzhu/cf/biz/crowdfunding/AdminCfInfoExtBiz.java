package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.response.OpResult;

import java.util.List;
import java.util.Map;

public interface AdminCfInfoExtBiz {

	int updateDealWithStatus(String infoUuid, int status);

	int updateUserRefund(String infoUuid, int days) throws Exception;

	int updateUserRefund(String infoUuid, String refundEndTime) throws Exception;

    int updateCryptoRegisterMobile(String infoUuid, String cryptoRegisterMobile);

    int insertList(List<String> list);

    List<CfInfoExt> selectByInfoUuidList(List<String> list);

    int updateSuggestStop(String infoUuid, boolean isSuggestStop);

    OpResult updateTransferStatusAndFinishStatus(String infoUuid);

    CfInfoExt selectByInfoUuidFromMaster(String infoUuid);

    List<CfInfoExt> getListByInfoUuids(List<String> infoUuids);

    CfInfoExt getByInfoUuid(String infoUuid);

    int updateFromType(String infoId, int type);

    Map<String, CfInfoExt> getMapByInfoUuids(List<String> infoUuids);


    /**
     * 更改筹款的首审
     *
     * @param infoUuid
     * @param statusEnum
     * @return
     */
    boolean updateApproveStatus(CrowdfundingInfo info, FirstApproveStatusEnum statusEnum);

    boolean updateFirstApproveTime(String infoUuid);

    /**
     * 更新运营描述字段
     * @param caseUUid
     * @param finishStr
     */
    void updateFinishStr(String caseUUid,String finishStr);

    CfInfoExt getByCaseId(int caseId);

    int updateFinishStatus(String infoUuid, int finishStatus);
    int updateFinishStatusByCaseId(int caseId, int finishStatus);

    String getRegisterMobile(CfInfoExt cfInfoExt);

    Map<Integer, CfInfoExt> getMapByCaseIds(List<Integer> caseIds);

     int updateNoHandlingFeeByInfoUuid(String infoUuid, int noHandlingFee);

}
