package com.shuidihuzhu.cf.biz.risk;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.activity.EnumPropertyVO;
import com.shuidihuzhu.cf.enums.crowdfunding.UserBehaviorEnum;
import com.shuidihuzhu.cf.model.crowdfunding.AdminUserBehaviorDetail;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/10/14 上午11:50
 * @desc
 */
public interface IAllUserBehaviorService {
    List<AdminUserBehaviorDetail> queryUserBehavior(int adminUserId, String mobile, long userId, String name, String identity, UserInfoModel userInfo, UserBehaviorEnum behaviorEnum);

    List<EnumPropertyVO> getOwnedModule(int adminUserId);

    List<EnumPropertyVO> getOwnedBehavior(int adminUserId);


    AdminErrorCode transferProperty(AdminUserBehaviorDetail.UserCaseProperty property);

    String queryHasRegister(String mobile);

    AdminUserBehaviorDetail.UserCaseProperty selectUserProperty(String mobile);
}
