package com.shuidihuzhu.cf.biz.crmUserManage;

import com.shuidihuzhu.client.cf.admin.model.CrmUserManage;
import com.shuidihuzhu.common.web.model.Response;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface CrmUserManageBiz {

    CrmUserManage.UserMapping bindUserMapping(String mobile);

    Map<String, CrmUserManage.UserMapping> batchBindUserMapping(Set<String> mobiles);

    Map<String, CrmUserManage.UserMapping> mergeUserMapping(String oldMobile, String newRaiseMobile);
    Map<String, CrmUserManage.UserMapping> mergeUuid(Set<String> mobiles);

    Map<String, CrmUserManage.UserMapping> selectUserMappings(Set<String> mobiles);

    void noticeClewCaseRaise(CrmUserManage.ClewCaseRaiseInfo raiseInfo);

    void markRaiseCaseQuality(String uuid, String personId, int raiseCaseQuality, int userId);

}
