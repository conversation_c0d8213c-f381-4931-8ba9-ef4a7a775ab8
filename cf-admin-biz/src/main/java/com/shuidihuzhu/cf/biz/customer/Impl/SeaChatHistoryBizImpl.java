package com.shuidihuzhu.cf.biz.customer.Impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.shuidihuzhu.account.utils.CryptoUserIdUtil;
import com.shuidihuzhu.cf.biz.customer.SeaChatHistoryBiz;
import com.shuidihuzhu.cf.customer.CfChatRecordDO;
import com.shuidihuzhu.cf.customer.ChatHistoryModelVo;
import com.shuidihuzhu.cf.dao.customer.CfChatRecordingDao;
import com.shuidihuzhu.cf.dao.customer.CfSessionRecordingDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class SeaChatHistoryBizImpl implements SeaChatHistoryBiz {

    @Autowired
    private CfSessionRecordingDao cfSessionRecordingDao;
    @Autowired
    private CfChatRecordingDao cfChatRecordingDao;

    @Override
    public List<String> getCidsByCryptoUserIds(List<String> cryptoAccountIds) {
        if (CollectionUtils.isEmpty(cryptoAccountIds)) {
            return Lists.newArrayList();
        }
        return cfSessionRecordingDao.getCidsByPartnerIds(cryptoAccountIds);
    }

    @Override
    public List<CfChatRecordDO> queryRecordByUserId(long userId) {
        String partnerId = CryptoUserIdUtil.cryptoUserId(userId);
        if (StringUtils.isEmpty(partnerId)) {
            return Lists.newArrayList();
        }
        return cfSessionRecordingDao.queryRecordByUserId(partnerId);
    }

    @Override
    public List<String> getCidsByPhoneNumber(String phoneNumber) {
        return cfSessionRecordingDao.getCidsByPhoneNumber(phoneNumber);
    }

    @Override
    public List<ChatHistoryModelVo> getChatHistoryByCids(String startTime,
                                                         String endTime,
                                                         List<String> cids) {
        if (CollectionUtils.isEmpty(cids)) {
            return Lists.newArrayList();
        }
        return cfChatRecordingDao.getChatHistoryByCids(startTime, endTime, cids);
    }

    @Override
    public List<ChatHistoryModelVo> queryChatRecordByCid(String cid, int current, int pageSize) {
        if(StringUtils.isEmpty(cid)){
            return Lists.newArrayList();
        }
        String orderBy = "send_time";
        PageHelper.startPage(current, pageSize, orderBy);
        return cfChatRecordingDao.queryChatRecordByCid(cid);
    }

}
