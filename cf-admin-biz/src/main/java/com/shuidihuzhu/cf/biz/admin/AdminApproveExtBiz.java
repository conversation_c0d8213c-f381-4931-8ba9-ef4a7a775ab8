package com.shuidihuzhu.cf.biz.admin;

import com.shuidihuzhu.cf.domain.approve.AdminApproveExt;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @Author: wangpeng
 * @Date: 2021/7/1 16:23
 * @Description:
 */
public interface AdminApproveExtBiz {
    int insert(AdminApproveExt adminApproveExt);

    List<AdminApproveExt> listByApproveIdAndExtName(int approveId, String extName);

    int updateExtValue(long id,  String extValue);

    Map<Integer, AdminApproveExt> getApproveExtList(@Param("approveIds") List<Integer> approveIds, @Param("extName") String extName);

}
