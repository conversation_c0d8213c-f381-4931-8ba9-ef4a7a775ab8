package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.CfTagGroupBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfTagGroupDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfTagGroup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by ahrievil on 2017/3/27.
 */
@Service
public class CfTagGroupBizImpl implements CfTagGroupBiz{

    @Autowired
    private CfTagGroupDao cfTagGroupDao;

    @Override
    public List<CfTagGroup> getList(List<Integer> types) {

       if(types == null || types.size() == 0) {
           return Lists.newArrayList();
       }

        return cfTagGroupDao.getList(types);
    }
}
