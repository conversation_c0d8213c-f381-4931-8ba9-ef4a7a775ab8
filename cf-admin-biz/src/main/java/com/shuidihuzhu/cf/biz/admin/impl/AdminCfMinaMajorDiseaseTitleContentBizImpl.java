package com.shuidihuzhu.cf.biz.admin.impl;

import com.shuidihuzhu.cf.biz.admin.AdminCfMinaMajorDiseaseTitleContentBiz;
import com.shuidihuzhu.cf.dao.admin.AdminCfMinaMajorDiseaseTitleContentDao;
import com.shuidihuzhu.cf.model.mina.CfMinaMajorDiseaseTitleContent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by ahrievil on 2017/7/13.
 */
@Service
public class AdminCfMinaMajorDiseaseTitleContentBizImpl implements AdminCfMinaMajorDiseaseTitleContentBiz {
    @Autowired
    private AdminCfMinaMajorDiseaseTitleContentDao adminCfMinaMajorDiseaseTitleContentDao;
    @Override
    public int updateContent(String content, int id) {
        return adminCfMinaMajorDiseaseTitleContentDao.updateContent(content, id);
    }

    @Override
    public List<CfMinaMajorDiseaseTitleContent> selectWithLimit(int start, String content, int limit) {
        return adminCfMinaMajorDiseaseTitleContentDao.selectWithLimit(start, content, limit);
    }

    @Override
    public int changeContent(String content, int id) {
        return adminCfMinaMajorDiseaseTitleContentDao.changeContent(content, id);
    }

    @Override
    public CfMinaMajorDiseaseTitleContent selectById(int id) {
        return adminCfMinaMajorDiseaseTitleContentDao.selectById(id);
    }

    @Override
    public CfMinaMajorDiseaseTitleContent selectByTitle(String title) {
        return adminCfMinaMajorDiseaseTitleContentDao.selectByTitle(title);
    }

    @Override
    public int changeTitle(String title, int id) {
        return adminCfMinaMajorDiseaseTitleContentDao.changeTitle(title, id);
    }
}
