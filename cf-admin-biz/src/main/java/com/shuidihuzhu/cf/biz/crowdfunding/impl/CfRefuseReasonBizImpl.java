package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseReasonBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingRediskvBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfRefuseReasonDao;
import com.shuidihuzhu.infra.starter.mail.service.EmailService;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReason;
import com.shuidihuzhu.common.web.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * Created by ahrievil on 2017/1/17.
 */
@Service
public class CfRefuseReasonBizImpl implements CfRefuseReasonBiz {

    private static final Logger LOGGER = LoggerFactory.getLogger(CfRefuseReasonBizImpl.class);

    @Autowired
    private CfRefuseReasonDao cfRefuseReasonDao;
    @Autowired
    private CrowdfundingRediskvBiz crowdfundingRediskvBiz;
    @Autowired
    private EmailService emailService;

    @Override
    public int editReason(int id, String content) {
        return cfRefuseReasonDao.editReason(id, content);
    }

    @Override
    public List<CfRefuseReason> getRefuseReasons() {
        return cfRefuseReasonDao.getRefuseReason();
    }

    @Override
    public int editFrequency(int id) {
        return cfRefuseReasonDao.editFrequency(id);
    }

    @Override
    public String findText(int id) {
        return cfRefuseReasonDao.findText(id);
    }

    @Override
    public int insertReason(int pid,String reason) {
        return cfRefuseReasonDao.insertReason(cfRefuseReasonDao.selectMax(),pid,reason);
    }

    @Override
    public int emptyFrequency() {
        return cfRefuseReasonDao.emptyFrequency();
    }

    @Override
    public List<CfRefuseReason> selectAll() {
        return cfRefuseReasonDao.selectAll();
    }

    @Override
    public void sendFrequency() {
        StringBuilder mailContent = new StringBuilder();
        List<CfRefuseReason> CfRefuseReasonList = this.selectAll();
        mailContent.append("<h>"+DateUtil.getMonthDayStr(new Date())+"  cf_refuse_reason表数据</h>");
        mailContent.append("<table border=\"1\" cellspacing=\"0\" cellpadding=\"0\" " +
                "style=\"border-collapse: collapse;border-width:0px;\">");
        mailContent.append("");
        mailContent.append("<tr>");
        mailContent.append("<td style=\"font-size: 9pt;font-family: 'Apple Braille' \" valign=\"top\"><font style=\"font-family: 'Apple Braille' \" color = \"blue\">"+"ID："+"</font></td>");
        mailContent.append("<td style=\"font-size: 9pt;font-family: 'Apple Braille' \" valign=\"top\"><font style=\"font-family: 'Apple Braille' \" color = \"blue\">"+"Content："+"</font></td>");
        mailContent.append("<td style=\"font-size: 9pt;font-family: 'Apple Braille' \" valign=\"top\"><font style=\"font-family: 'Apple Braille' \" color = \"blue\">"+"Frequency："+"</font></td>");
        mailContent.append("</tr>");
        for (CfRefuseReason cfRefuseReason : CfRefuseReasonList) {
            mailContent.append("<tr>");
            mailContent.append("<td><span>"+cfRefuseReason.getId()+"</span></td>");
            mailContent.append("<td><span>"+cfRefuseReason.getContent()+"</span></td>");
            mailContent.append("<td><span>"+cfRefuseReason.getFrequency()+"</span></td>");
            mailContent.append("</tr>");
        }
        mailContent.append("</table>");
        try {
            String[] recipients = crowdfundingRediskvBiz.selectMailRecipient("REFUSE_REASON_MAIL_RECIPIENT").split(",");
            List list = Arrays.asList(recipients);
            emailService.sendHtmlMail(list,"cf_refuse_reason表数据",mailContent.toString());
        } catch (Exception e) {
            LOGGER.error("sendFrequency Error!", e);
        }
    }

    @Override
    public int selectWithItem(int id) {
        return cfRefuseReasonDao.selectWithItem(id);
    }

    @Override
    public int deleteById(int id) {
        return cfRefuseReasonDao.deleteById(id);
    }

    @Override
    public int insertOne(CfRefuseReason cfRefuseReason) {
        return cfRefuseReasonDao.insertOne(cfRefuseReason);
    }
}
