package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRiskRuleBiz;
import com.shuidihuzhu.cf.client.adminpure.model.rule.EconomyModel;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.rule.RiskRuleCondition;
import com.shuidihuzhu.cf.enums.rule.RiskRuleResult;
import com.shuidihuzhu.cf.enums.rule.RiskRuleResultTypeEnum;
import com.shuidihuzhu.cf.enums.rule.custom.*;
import com.shuidihuzhu.cf.model.admin.rule.RiskRuleModel;
import com.shuidihuzhu.cf.response.OpResult;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2020/1/2
 */
@Service
public class CfRiskRuleBizImpl implements CfRiskRuleBiz {


    private OpResult<Set<RiskRuleResult>> judge(List<RiskRuleModel> models) {

        if (CollectionUtils.isEmpty(models)){
            return OpResult.createFailResult(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<RiskRuleResult> list = Lists.newArrayList();

        doJudge(models, list);

        return OpResult.createSucResult(RiskRuleResult.caleValidResult(list));
    }

    @Override
    public List<String> assembleTipInfo(EconomyModel model) {
        // 新版直接返回
        if (model.getRiskRuleVersion() == 2) {
            return model.getConvertResult();
        }

        // 旧版逻辑
        List<Integer> resultList = model.getResult();
        List<String> stringResult = Lists.newArrayListWithCapacity(resultList.size());
        String prefixInfo = "系统判定该患者家庭经济情况足以支撑医疗费用，请发起人再次确认";
        String suffixInfo = "";

        //用户手动调用，需要处理"可发"，但需要补充说明的情况
        if (Objects.equals(model.getStrategy(), EconomyModel.strategy_C)) {
            if (RiskRuleResult.hasResultByType(resultList, Sets.newHashSet(RiskRuleResultTypeEnum.NOT_ALLOW_LAUNCH))) {
                resultList = RiskRuleResult.removeResultType(resultList, RiskRuleResultTypeEnum.ALLOW_AND_ADD_LAUNCH);
                //如果都是可发，则拼接以下的前后缀信息
                prefixInfo = "再次确认";
                suffixInfo = "的真实准确，若信息准确，不可发起";
            }
        }

        //不可发的因素
        String notAllows = resultList.stream().filter(RiskRuleResult::isNotAllowLaunch)
                .map(code -> "”" + RiskRuleResult.fromCode(code) + "”")
                .collect(Collectors.joining("、"));
        if (StringUtils.isNotBlank(notAllows)) {
            stringResult.add(prefixInfo + notAllows + suffixInfo);
        }

        stringResult.addAll(resultList.stream()
                .filter(code -> !RiskRuleResult.isNotAllowLaunch(code))
                .map(code -> {
                    //处理进一步询问情况
                    if (RiskRuleResult.isAskFurther(code)) {
                        return "家庭经济情况信息不全面，" + RiskRuleResult.fromCode(code);
                    }
                    //可发但仍需说明情况&可发起
                    return RiskRuleResult.fromCode(code);
                })
                .collect(Collectors.toList()));

        return stringResult;
    }

    private void doJudge(List<RiskRuleModel> models, List<RiskRuleResult> list){
        models.forEach(r -> {
            if (RiskRuleCondition.condition(r.getValue(),r.getThreshold(),r.getRiskRuleCondition())){
                //如果没有二级条件，直接保留结果
                if (r.getRiskRuleModel() == null){
                    list.addAll(r.getResult());
                }else {
                    doJudge(List.of(r.getRiskRuleModel()), list);
                }
            }
        });
    }


    @Override
    public OpResult<Set<RiskRuleResult>> judge(EconomyModel model) {

        if (EconomyModel.strategy_A.equals(model.getStrategy())){
            return strategyA(model);
        }

        if (EconomyModel.strategy_B.equals(model.getStrategy())){
            return strategyB(model);
        }

        if (EconomyModel.strategy_C.equals(model.getStrategy())) {
            return strategyC(model);
        }

        return OpResult.createFailResult(AdminErrorCode.ADMIN_NO_STRATEGY);
    }

    public OpResult<Set<RiskRuleResult>> strategyA(EconomyModel model) {
        List<RiskRuleModel> models = Lists.newArrayList();

        if (model.isHasSelfHouse()) {
            this.selfHouseRule(model, models);
        } else {
            this.noSelfHouseRule(model, models);
        }

        /**
         * 车产价值及数量
         *（价值为区间值，则以最大值做计算）
         */
        //不可发
        RiskRuleModel carAmount = RiskRuleModel.strategy(model.getCarAmount(),RiskRuleModel.hundred_thousand_2,RiskRuleCondition.equal_greater,RiskRuleModel.raise);
        RiskRuleModel carRisk = RiskRuleModel.strategy(model.getCarNum(),RiskRuleModel.num_2,RiskRuleCondition.less,Lists.newArrayList(RiskRuleResult.A_7, RiskRuleResult.A_16));
        carAmount.setRiskRuleModel(carRisk);
        models.add(carAmount);

        RiskRuleModel carAmount2 = RiskRuleModel.strategy(model.getCarAmount(),RiskRuleModel.hundred_thousand_3,RiskRuleCondition.equal_greater,RiskRuleModel.raise);
        RiskRuleModel carRisk2 = RiskRuleModel.strategy(model.getCarNum(),RiskRuleModel.num_2,RiskRuleCondition.equal_greater,Lists.newArrayList(RiskRuleResult.A_7, RiskRuleResult.A_16));
        carAmount2.setRiskRuleModel(carRisk2);
        models.add(carAmount2);

        //需要进一步询问
        RiskRuleModel carAmount3 = RiskRuleModel.strategy(model.getCarAmount(),RiskRuleModel.hundred_thousand_3,RiskRuleCondition.less,RiskRuleModel.raise);
        RiskRuleModel carRisk3 = RiskRuleModel.strategy(model.getCarNum(),RiskRuleModel.num_2,RiskRuleCondition.equal_greater,Lists.newArrayList(RiskRuleResult.C_3));
        carAmount3.setRiskRuleModel(carRisk3);
        models.add(carAmount3);

        /**
         * 是否足以承担未来花费
         *（价值为区间值，则以最大值做计算）
         */
        /*models.add(RiskRuleModel.strategy(getNum(model),model.getSpentAmount(),RiskRuleCondition.equal_greater,Lists.newArrayList(RiskRuleResult.A_4,RiskRuleResult.A_7,RiskRuleResult.A_2,RiskRuleResult.A_10,RiskRuleResult.A_1)));
        models.add(RiskRuleModel.strategy(getNum(model),model.getSpentAmount(),RiskRuleCondition.less,RiskRuleModel.raise));*/


        /**
         * 家庭年收入
         * (价值为区间值，则以最大值做计算）
         */
        RiskRuleModel homeIncomeRisk = RiskRuleModel.strategy(model.getHomeIncomeRange(), RiskRuleModel.hundred_thousand_3, RiskRuleCondition.less, RiskRuleModel.raise);
        RiskRuleModel spentRisk = RiskRuleModel.strategy(model.getHomeIncomeRange(), model.getSpentAmount(), RiskRuleCondition.equal_greater, Lists.newArrayList(RiskRuleResult.A_1, RiskRuleResult.A_11));
        homeIncomeRisk.setRiskRuleModel(spentRisk);
        models.add(homeIncomeRisk);
        models.add(RiskRuleModel.strategy(model.getHomeIncomeRange(),RiskRuleModel.hundred_thousand_3,RiskRuleCondition.equal_greater,Lists.newArrayList(RiskRuleResult.A_11)));


        /**
         * 家庭金融资产与未来花费关系
         */
        RiskRuleModel financeRisk = RiskRuleModel.strategy(model.getFinanceAmount(), RiskRuleModel.hundred_thousand_3, RiskRuleCondition.less, RiskRuleModel.raise);
        RiskRuleModel spentAndFinanceRisk = RiskRuleModel.strategy(model.getFinanceAmount(), model.getSpentAmount(), RiskRuleCondition.equal_greater, Lists.newArrayList(RiskRuleResult.A_2));
        financeRisk.setRiskRuleModel(spentAndFinanceRisk);
        models.add(financeRisk);
        models.add(RiskRuleModel.strategy(model.getFinanceAmount(),RiskRuleModel.hundred_thousand_3,RiskRuleCondition.equal_greater,Lists.newArrayList(RiskRuleResult.A_2)));


        /**
         * 剩余款项与未来花费关系
         */
        models.add(RiskRuleModel.strategy(model.getRemainAmount(),model.getSpentAmount(),RiskRuleCondition.less,RiskRuleModel.raise));
        models.add(RiskRuleModel.strategy(model.getRemainAmount(),model.getSpentAmount(),RiskRuleCondition.equal_greater,Lists.newArrayList(RiskRuleResult.A_12, RiskRuleResult.A_1)));


        /**
         * 人身险
         */
        models.add(RiskRuleModel.strategy(model.getHasPersonalInsurance(),1,RiskRuleCondition.equal,Lists.newArrayList(RiskRuleResult.C_4)));


        /**
         * 事故造成
         */
        models.add(RiskRuleModel.strategy(model.getAccidentStatus(), PreposeMaterialModel.AccidentType.DEFAULT.getCode(),RiskRuleCondition.greater,Lists.newArrayList(RiskRuleResult.C_5)));

        return judge(models);
    }

    private void noSelfHouseRule(EconomyModel model, List<RiskRuleModel> models) {
        /**
         * 房产价值及数量
         *（价值为区间值，则以最大值做计算）
         */
        //不可发
        models.add(RiskRuleModel.strategy(model.getHouseAmount(), RiskRuleModel.million_500, RiskRuleCondition.greater, Lists.newArrayList(RiskRuleResult.A_4)));

        //需要进一步询问
        RiskRuleModel houseAmount = RiskRuleModel.strategy(model.getHouseAmount(), RiskRuleModel.million_300, RiskRuleCondition.equal_less, RiskRuleModel.raise);
        RiskRuleModel houseRisk = RiskRuleModel.strategy(model.getHouseNum(), RiskRuleModel.num_2, RiskRuleCondition.greater, RiskRuleModel.raise);
        houseAmount.setRiskRuleModel(houseRisk);
        RiskRuleModel houseSell = RiskRuleModel.strategy(model.getPropertySoldStatus(), PreposeMaterialModel.HasSellEnum.SELLED.getCode(), RiskRuleCondition.equal, List.of(RiskRuleResult.C_0));
        houseRisk.setRiskRuleModel(houseSell);
        models.add(houseAmount);

        RiskRuleModel houseAmount1 = RiskRuleModel.strategy(model.getHouseAmount(), RiskRuleModel.million_300, RiskRuleCondition.equal_less, RiskRuleModel.raise);
        RiskRuleModel houseRisk1 = RiskRuleModel.strategy(model.getHouseNum(), RiskRuleModel.num_2, RiskRuleCondition.greater, RiskRuleModel.raise);
        houseAmount1.setRiskRuleModel(houseRisk1);
        RiskRuleModel houseSell1 = RiskRuleModel.strategy(model.getPropertySoldStatus(), PreposeMaterialModel.HasSellEnum.NO_SELL.getCode(), RiskRuleCondition.equal, List.of(RiskRuleResult.C_1));
        houseRisk1.setRiskRuleModel(houseSell1);
        models.add(houseAmount1);

        RiskRuleModel houseAmount2 = RiskRuleModel.strategy(model.getHouseAmount(), List.of(RiskRuleModel.million_300, RiskRuleModel.million_500), RiskRuleCondition.equal_less, RiskRuleModel.raise);
        RiskRuleModel houseRisk2 = RiskRuleModel.strategy(model.getHouseNum(), RiskRuleModel.num_2, RiskRuleCondition.equal_greater, List.of(RiskRuleResult.C_2));
        houseAmount2.setRiskRuleModel(houseRisk2);
        models.add(houseAmount2);

    }

    private void selfHouseRule(EconomyModel model, List<RiskRuleModel> models) {
        /*
          可发
         */
        int totalAmount = model.getHouseAmount() + model.getSelfHouseValue();
        int totalHouseNum = model.getSelfHouseNum() + model.getHouseNum();
        int totalSellingCount = model.getSelfHouseSellingCount() + model.getOtherHouseSellingCount();

        // 【（自建房总价值+非自建房总价值）≤250万】且【（自建房数量+非自建房数量）≥2】且【（变卖中自建房数量+变卖中非自建房数量）≥1】
        RiskRuleModel strategy_1 = RiskRuleModel.strategy(totalAmount, RiskRuleModel.million_250, RiskRuleCondition.equal_less, RiskRuleModel.raise);
        RiskRuleModel strategy_2 = RiskRuleModel.strategy(totalHouseNum, RiskRuleModel.num_2, RiskRuleCondition.equal_greater, RiskRuleModel.raise);
        RiskRuleModel strategy_3 = RiskRuleModel.strategy(totalSellingCount, RiskRuleModel.num_1, RiskRuleCondition.equal_greater, List.of(RiskRuleResult.A_A_0));
        strategy_2.setRiskRuleModel(strategy_3);
        strategy_1.setRiskRuleModel(strategy_2);
        models.add(strategy_1);

        //【250<（自建房总价值+非自建房总价值）<500万】且【（自建房数量+非自建房数量）≥2】且【（变卖中自建房数量+变卖中非自建房数量）≥1】
        RiskRuleModel strategy_4 = RiskRuleModel.strategy(totalAmount, RiskRuleModel.million_250, RiskRuleCondition.greater, RiskRuleModel.raise);
        RiskRuleModel strategy_5 = RiskRuleModel.strategy(totalAmount, RiskRuleModel.million_500, RiskRuleCondition.less, RiskRuleModel.raise);
        strategy_5.setRiskRuleModel(strategy_2);
        strategy_4.setRiskRuleModel(strategy_5);
        models.add(strategy_4);

        /*
          不可发
          房产价值及数量（价值为区间值，则以最大值做计算）
         */
        //（自建房总价值+非自建房总价值）≥500万
        models.add(RiskRuleModel.strategy(totalAmount, RiskRuleModel.million_500, RiskRuleCondition.equal_greater, Lists.newArrayList(RiskRuleResult.A_4)));

        //【250<（自建房总价值+非自建房总价值）<500万】且【（自建房数量+非自建房数量）<2】
        RiskRuleModel houseValueRule_1 = RiskRuleModel.strategy(totalAmount, RiskRuleModel.million_250, RiskRuleCondition.greater, RiskRuleModel.raise);
        RiskRuleModel houseValueRule_2 = RiskRuleModel.strategy(totalAmount, RiskRuleModel.million_500, RiskRuleCondition.less,RiskRuleModel.raise);
        RiskRuleModel houseNumRule_1 = RiskRuleModel.strategy(totalHouseNum, RiskRuleModel.num_2, RiskRuleCondition.less, Lists.newArrayList(RiskRuleResult.A_4,RiskRuleResult.A_8));
        houseValueRule_2.setRiskRuleModel(houseNumRule_1);
        houseValueRule_1.setRiskRuleModel(houseValueRule_2);
        models.add(houseValueRule_1);

        /*
          需要进一步询问
         */
        // 【（自建房总价值+非自建房总价值）≤250万】且【（自建房数量+非自建房数量）≥2】且【（变卖中自建房数量+变卖中非自建房数量）=0】
        RiskRuleModel houseValueRule_4 = RiskRuleModel.strategy(totalAmount, RiskRuleModel.million_250, RiskRuleCondition.equal_less, RiskRuleModel.raise);
        RiskRuleModel houseNumRule_2 = RiskRuleModel.strategy(totalHouseNum, RiskRuleModel.num_2, RiskRuleCondition.equal_greater, RiskRuleModel.raise);
        RiskRuleModel totalSellingCountRule_1 = RiskRuleModel.strategy(totalSellingCount, 0, RiskRuleCondition.equal, Lists.newArrayList(RiskRuleResult.C_1));
        houseNumRule_2.setRiskRuleModel(totalSellingCountRule_1);
        houseValueRule_4.setRiskRuleModel(houseNumRule_2);
        models.add(houseValueRule_4);

        // 【250<（自建房总价值+非自建房总价值）<500万】且【（自建房数量+非自建房数量）≥2】且【（变卖中自建房数量+变卖中非自建房数量）=0】
        RiskRuleModel houseValueRule_5 = RiskRuleModel.strategy(totalAmount, RiskRuleModel.million_250, RiskRuleCondition.greater, RiskRuleModel.raise);
        RiskRuleModel houseValueRule_6 = RiskRuleModel.strategy(totalAmount, RiskRuleModel.million_500, RiskRuleCondition.less, RiskRuleModel.raise);
        houseValueRule_6.setRiskRuleModel(houseNumRule_2);
        houseValueRule_5.setRiskRuleModel(houseValueRule_6);
        models.add(houseValueRule_5);
    }


    public OpResult<Set<RiskRuleResult>> strategyB(EconomyModel model) {
        List<RiskRuleModel> models = Lists.newArrayList();

        if (model.isHasSelfHouse()) {
            this.selfHouseRule(model, models);
        } else {
            this.noSelfHouseRule(model, models);
        }

        /**
         * 车产价值及数量
         *（价值为区间值，则以最大值做计算）
         */
        //不可发
        RiskRuleModel carAmount = RiskRuleModel.strategy(model.getCarAmount(),RiskRuleModel.hundred_thousand_2,RiskRuleCondition.equal_greater,RiskRuleModel.raise);
        RiskRuleModel carRisk = RiskRuleModel.strategy(model.getCarNum(),RiskRuleModel.num_2,RiskRuleCondition.less,Lists.newArrayList(RiskRuleResult.A_7, RiskRuleResult.A_16));
        carAmount.setRiskRuleModel(carRisk);
        models.add(carAmount);

        RiskRuleModel carAmount2 = RiskRuleModel.strategy(model.getCarAmount(),RiskRuleModel.hundred_thousand_3,RiskRuleCondition.equal_greater,RiskRuleModel.raise);
        RiskRuleModel carRisk2 = RiskRuleModel.strategy(model.getCarNum(),RiskRuleModel.num_2,RiskRuleCondition.equal_greater,Lists.newArrayList(RiskRuleResult.A_7, RiskRuleResult.A_16));
        carAmount2.setRiskRuleModel(carRisk2);
        models.add(carAmount2);

        //需要进一步询问
        RiskRuleModel carAmount3 = RiskRuleModel.strategy(model.getCarAmount(),RiskRuleModel.hundred_thousand_3,RiskRuleCondition.less,RiskRuleModel.raise);
        RiskRuleModel carRisk3 = RiskRuleModel.strategy(model.getCarNum(),RiskRuleModel.num_2,RiskRuleCondition.equal_greater,Lists.newArrayList(RiskRuleResult.C_3));
        carAmount3.setRiskRuleModel(carRisk3);
        models.add(carAmount3);


        /**
         * 家庭年收入
         * (价值为区间值，则以最大值做计算）
         */
        RiskRuleModel homeIncomeRisk = RiskRuleModel.strategy(model.getHomeIncomeRange(), RiskRuleModel.hundred_thousand_3, RiskRuleCondition.less, RiskRuleModel.raise);
        RiskRuleModel spentRisk = RiskRuleModel.strategy(model.getHomeIncomeRange(), model.getTargetAmount(), RiskRuleCondition.equal_greater, Lists.newArrayList(RiskRuleResult.A_11, RiskRuleResult.A_9));
        homeIncomeRisk.setRiskRuleModel(spentRisk);
        models.add(homeIncomeRisk);
        models.add(RiskRuleModel.strategy(model.getHomeIncomeRange(),RiskRuleModel.hundred_thousand_3,RiskRuleCondition.equal_greater,Lists.newArrayList(RiskRuleResult.A_11)));


        /**
         * 家庭金融资产与未来花费关系
         */
        RiskRuleModel financeRisk = RiskRuleModel.strategy(model.getFinanceAmount(), RiskRuleModel.hundred_thousand_3, RiskRuleCondition.less, RiskRuleModel.raise);
        RiskRuleModel spentAndFinanceRisk = RiskRuleModel.strategy(model.getFinanceAmount(), model.getTargetAmount(), RiskRuleCondition.equal_greater, Lists.newArrayList(RiskRuleResult.A_2));
        financeRisk.setRiskRuleModel(spentAndFinanceRisk);
        models.add(financeRisk);
        models.add(RiskRuleModel.strategy(model.getFinanceAmount(),RiskRuleModel.hundred_thousand_3,RiskRuleCondition.equal_greater,Lists.newArrayList(RiskRuleResult.A_2)));


        /**
         * 剩余款项与未来花费关系
         */
        models.add(RiskRuleModel.strategy(model.getRemainAmount(),model.getTargetAmount(),RiskRuleCondition.less,RiskRuleModel.raise));
        models.add(RiskRuleModel.strategy(model.getRemainAmount(),model.getTargetAmount(),RiskRuleCondition.equal_greater,Lists.newArrayList(RiskRuleResult.A_12, RiskRuleResult.A_9)));


        /**
         * 人身险
         */
        models.add(RiskRuleModel.strategy(model.getHasPersonalInsurance(),1,RiskRuleCondition.equal,Lists.newArrayList(RiskRuleResult.C_4)));

        return judge(models);
    }

    /**
     * 用于手动调用
     * @param model
     * @return
     */
    public OpResult<Set<RiskRuleResult>> strategyC(EconomyModel model) {
        List<RiskRuleModel> models = Lists.newArrayList();
        /**
         * 房产价值及数量
         *（价值为区间值，则以最大值做计算）
         */
        var totalAmount = model.getSelfHouseValue() + model.getHouseAmount();
        var totalNum = model.getSelfHouseNum() + model.getHouseNum();
        var totalSellingNum = model.getSelfHouseSellingCount() + model.getOtherHouseSellingCount();

        // 可发
        // 【（自建房总价值+非自建房总价值）≤250万】且【（自建房数量+非自建房数量）≥2】且【（变卖中自建房数量+变卖中非自建房数量）≥1】
        RiskRuleModel strategy_1 = RiskRuleModel.strategy(totalAmount, RiskRuleModel.million_250, RiskRuleCondition.equal_less, RiskRuleModel.raise);
        RiskRuleModel strategy_2 = RiskRuleModel.strategy(totalNum, RiskRuleModel.num_2, RiskRuleCondition.equal_greater, RiskRuleModel.raise);
        RiskRuleModel strategy_3 = RiskRuleModel.strategy(totalSellingNum, RiskRuleModel.num_1, RiskRuleCondition.equal_greater, List.of(RiskRuleResult.A_A_0));
        strategy_2.setRiskRuleModel(strategy_3);
        strategy_1.setRiskRuleModel(strategy_2);
        models.add(strategy_1);

        // 【（自建房总价值+非自建房总价值）≤250万】且【（自建房数量+非自建房数量）≥2】且【（变卖中自建房数量+变卖中非自建房数量）=0】且【无闲置或出租】
        RiskRuleModel strategy_4 = RiskRuleModel.strategy(totalAmount, RiskRuleModel.million_250, RiskRuleCondition.equal_less, RiskRuleModel.raise);
        RiskRuleModel strategy_5 = RiskRuleModel.strategy(totalNum, RiskRuleModel.num_2, RiskRuleCondition.equal_greater, RiskRuleModel.raise);
        RiskRuleModel strategy_6 = RiskRuleModel.strategy(totalSellingNum, 0, RiskRuleCondition.equal, RiskRuleModel.raise);
        RiskRuleModel strategy_7 = RiskRuleModel.strategy(model.getIsIdle(), 2, RiskRuleCondition.equal,
                List.of(RiskRuleResult.A_A_2));
        strategy_6.setRiskRuleModel(strategy_7);
        strategy_5.setRiskRuleModel(strategy_6);
        strategy_4.setRiskRuleModel(strategy_5);
        models.add(strategy_4);

        // 【250<（自建房总价值+非自建房总价值）<500万】且【（自建房数量+非自建房数量）≥2】且【（变卖中自建房数量+变卖中非自建房数量）≥1】
        RiskRuleModel strategy_8 = RiskRuleModel.strategy(totalAmount, RiskRuleModel.million_250, RiskRuleCondition.greater, RiskRuleModel.raise);
        RiskRuleModel strategy_9 = RiskRuleModel.strategy(totalAmount, RiskRuleModel.million_500, RiskRuleCondition.less, RiskRuleModel.raise);
        RiskRuleModel strategy_10 = RiskRuleModel.strategy(totalNum, RiskRuleModel.num_2, RiskRuleCondition.equal_greater, RiskRuleModel.raise);
        RiskRuleModel strategy_11 = RiskRuleModel.strategy(totalSellingNum, RiskRuleModel.num_1, RiskRuleCondition.equal_greater,
                List.of(RiskRuleResult.A_A_0));
        strategy_10.setRiskRuleModel(strategy_11);
        strategy_9.setRiskRuleModel(strategy_10);
        strategy_8.setRiskRuleModel(strategy_9);
        models.add(strategy_8);

        // 【250<（自建房总价值+非自建房总价值）<500万】且【（自建房数量+非自建房数量）≥2】且【（变卖中自建房数量+变卖中非自建房数量）=0】且【无闲置或出租】
        RiskRuleModel strategy_12 = RiskRuleModel.strategy(totalAmount, RiskRuleModel.million_250, RiskRuleCondition.greater, RiskRuleModel.raise);
        RiskRuleModel strategy_13 = RiskRuleModel.strategy(totalAmount, RiskRuleModel.million_500, RiskRuleCondition.less, RiskRuleModel.raise);
        RiskRuleModel strategy_14 = RiskRuleModel.strategy(totalNum, RiskRuleModel.num_2, RiskRuleCondition.equal_greater, RiskRuleModel.raise);
        RiskRuleModel strategy_15 = RiskRuleModel.strategy(totalSellingNum, 0, RiskRuleCondition.equal, RiskRuleModel.raise);
        RiskRuleModel strategy_16 = RiskRuleModel.strategy(model.getIsIdle(), 2, RiskRuleCondition.equal,
                List.of(RiskRuleResult.A_A_2));
        strategy_15.setRiskRuleModel(strategy_16);
        strategy_14.setRiskRuleModel(strategy_15);
        strategy_13.setRiskRuleModel(strategy_14);
        strategy_12.setRiskRuleModel(strategy_13);
        models.add(strategy_12);


        //不可发
        // （自建房总价值+非自建房总价值）≥500万
        RiskRuleModel amountStrategy = RiskRuleModel.strategy(totalAmount, RiskRuleModel.million_500, RiskRuleCondition.equal_greater, List.of(RiskRuleResult.A_4));
        models.add(amountStrategy);

        //【（自建房总价值+非自建房总价值）≤250万】且【（自建房数量+非自建房数量）≥2】且【（变卖中自建房数量+变卖中非自建房数量）=0】且【自建房居住状态为有闲置或出租】或【非自建房居住状态为有闲置或出租】
        RiskRuleModel amountStrategy_2 = RiskRuleModel.strategy(totalAmount, RiskRuleModel.million_250, RiskRuleCondition.equal_less, RiskRuleModel.raise);
        RiskRuleModel numStrategy_1 = RiskRuleModel.strategy(totalNum, RiskRuleModel.num_2, RiskRuleCondition.equal_greater, RiskRuleModel.raise);
        RiskRuleModel sellingNumStrategy_1 = RiskRuleModel.strategy(totalSellingNum, 0, RiskRuleCondition.equal, RiskRuleModel.raise);

        // 且【自建房居住状态为有闲置或出租】或【非自建房居住状态为有闲置或出租】
        int idleV2 = 0;
        if(model.getIsIdle() == 1) {
            idleV2++;
        }
        if(model.getHouseLiveStatus() == 1) {
            idleV2++;
        }
        RiskRuleModel idleStrategy = RiskRuleModel.strategy(idleV2, 0, RiskRuleCondition.greater,
                List.of(RiskRuleResult.A_4, RiskRuleResult.A_8, RiskRuleResult.A_23, RiskRuleResult.A_24));

        sellingNumStrategy_1.setRiskRuleModel(idleStrategy);
        numStrategy_1.setRiskRuleModel(sellingNumStrategy_1);
        amountStrategy_2.setRiskRuleModel(numStrategy_1);
        models.add(amountStrategy_2);

        // 【250<（自建房总价值+非自建房总价值）<500万】且【（自建房数量+非自建房数量）<2】
        RiskRuleModel million250 = RiskRuleModel.strategy(totalAmount, RiskRuleModel.million_250, RiskRuleCondition.greater, RiskRuleModel.raise);
        RiskRuleModel amountStrategy_3 = RiskRuleModel.strategy(totalAmount, RiskRuleModel.million_500, RiskRuleCondition.less, RiskRuleModel.raise);
        RiskRuleModel numStrategy_2 = RiskRuleModel.strategy(totalNum, RiskRuleModel.num_2, RiskRuleCondition.less, List.of(RiskRuleResult.A_4, RiskRuleResult.A_8));
        amountStrategy_3.setRiskRuleModel(numStrategy_2);
        million250.setRiskRuleModel(amountStrategy_3);
        models.add(million250);

        // 【250<（自建房总价值+非自建房总价值）<500万】且【（自建房数量+非自建房数量）≥2】且【（变卖中自建房数量+变卖中非自建房数量）=0】且【有闲置或出租】
        RiskRuleModel amountStrategy_4 = RiskRuleModel.strategy(totalAmount, RiskRuleModel.million_250, RiskRuleCondition.greater, RiskRuleModel.raise);
        RiskRuleModel amountStrategy_5 = RiskRuleModel.strategy(totalAmount, RiskRuleModel.million_500, RiskRuleCondition.less, RiskRuleModel.raise);
        amountStrategy_5.setRiskRuleModel(numStrategy_1);
        amountStrategy_4.setRiskRuleModel(amountStrategy_5);
        models.add(amountStrategy_4);

//        //可发
//        //【房产总价值≤300万】且【房产数量>2】且【变卖中】
//        RiskRuleModel houseAmountA = RiskRuleModel.strategy(model.getHouseAmount(),RiskRuleModel.million_300,RiskRuleCondition.equal_less,RiskRuleModel.raise);
//        RiskRuleModel houseRiskA = RiskRuleModel.strategy(model.getHouseNum(),RiskRuleModel.num_2,RiskRuleCondition.greater,RiskRuleModel.raise);
//        houseAmountA.setRiskRuleModel(houseRiskA);
//        RiskRuleModel houseSellA = RiskRuleModel.strategy(model.getPropertySoldStatus(), PreposeMaterialModel.HasSellEnum.SELLING.getCode(),RiskRuleCondition.equal,Lists.newArrayList(RiskRuleResult.A_A_0));
//        houseRiskA.setRiskRuleModel(houseSellA);
//        models.add(houseAmountA);
//        //【房产总价值≤300万】且【房产数量＞2】且【已变卖】且【不可覆盖】
//        RiskRuleModel houseAmountA2 = RiskRuleModel.strategy(model.getHouseAmount(),RiskRuleModel.million_300,RiskRuleCondition.equal_less,RiskRuleModel.raise);
//        RiskRuleModel houseRiskA2 = RiskRuleModel.strategy(model.getHouseNum(),RiskRuleModel.num_2,RiskRuleCondition.greater,RiskRuleModel.raise);
//        houseAmountA2.setRiskRuleModel(houseRiskA2);
//        RiskRuleModel houseSellA2 = RiskRuleModel.strategy(model.getPropertySoldStatus(), PreposeMaterialModel.HasSellEnum.SELLED.getCode(),RiskRuleCondition.equal,RiskRuleModel.raise);
//        houseRiskA2.setRiskRuleModel(houseSellA2);
//        RiskRuleModel houseCoverA2 = RiskRuleModel.strategy(model.getCoveredTreatmentCostSold(), CoverCureEnum.NOT_COVER.getCode(),RiskRuleCondition.equal, List.of(RiskRuleResult.A_A_1));
//        houseSellA2.setRiskRuleModel(houseCoverA2);
//        models.add(houseAmountA2);
//        //【房产总价值≤300万】且【房产数量>2】且【未变卖】且【无闲置或出租】
//        RiskRuleModel houseAmountA3 = RiskRuleModel.strategy(model.getHouseAmount(),RiskRuleModel.million_300,RiskRuleCondition.equal_less,RiskRuleModel.raise);
//        RiskRuleModel houseRiskA3 = RiskRuleModel.strategy(model.getHouseNum(),RiskRuleModel.num_2,RiskRuleCondition.greater,RiskRuleModel.raise);
//        houseAmountA3.setRiskRuleModel(houseRiskA3);
//        RiskRuleModel houseSellA3 = RiskRuleModel.strategy(model.getPropertySoldStatus(), PreposeMaterialModel.HasSellEnum.NO_SELL.getCode(),RiskRuleCondition.equal,RiskRuleModel.raise);
//        houseRiskA3.setRiskRuleModel(houseSellA3);
//        RiskRuleModel houseCoverA3 = RiskRuleModel.strategy(model.getHouseLiveStatus(), HouseUseStateEnum.NO_IDLENESS.getCode(),RiskRuleCondition.equal, List.of(RiskRuleResult.A_A_2));
//        houseSellA3.setRiskRuleModel(houseCoverA3);
//        models.add(houseAmountA3);
//        //【300万<房产总价值≤500万】且【房产数量≥2】且【无闲置或出租】
//        RiskRuleModel houseAmountA4 = RiskRuleModel.strategy(model.getHouseAmount(),List.of(RiskRuleModel.million_300, RiskRuleModel.million_500),RiskRuleCondition.equal_less,RiskRuleModel.raise);
//        RiskRuleModel houseRiskA4 = RiskRuleModel.strategy(model.getHouseNum(),RiskRuleModel.num_2,RiskRuleCondition.equal_greater,RiskRuleModel.raise);
//        houseAmountA4.setRiskRuleModel(houseRiskA4);
//        RiskRuleModel houseSellA4 = RiskRuleModel.strategy(model.getHouseLiveStatus(), HouseUseStateEnum.NO_IDLENESS.getCode(),RiskRuleCondition.equal,Lists.newArrayList(RiskRuleResult.A_A_2));
//        houseRiskA4.setRiskRuleModel(houseSellA4);
//        models.add(houseAmountA4);
//        //不可发
//        //【房产总价值>500万】
//        models.add(RiskRuleModel.strategy(model.getHouseAmount(),RiskRuleModel.million_500,RiskRuleCondition.greater,Lists.newArrayList(RiskRuleResult.A_4)));
//        //【房产总价值≤300万】且【房产数量>2】且【已变卖】且【可覆盖】
//        RiskRuleModel houseAmount = RiskRuleModel.strategy(model.getHouseAmount(),RiskRuleModel.million_300,RiskRuleCondition.equal_less,RiskRuleModel.raise);
//        RiskRuleModel houseRisk = RiskRuleModel.strategy(model.getHouseNum(),RiskRuleModel.num_2,RiskRuleCondition.greater,RiskRuleModel.raise);
//        houseAmount.setRiskRuleModel(houseRisk);
//        RiskRuleModel houseSell = RiskRuleModel.strategy(model.getPropertySoldStatus(), PreposeMaterialModel.HasSellEnum.SELLED.getCode(),RiskRuleCondition.equal,RiskRuleModel.raise);
//        houseRisk.setRiskRuleModel(houseSell);
//        RiskRuleModel houseCover = RiskRuleModel.strategy(model.getCoveredTreatmentCostSold(), CoverCureEnum.CAN_COVER.getCode(),RiskRuleCondition.equal,
//                List.of(RiskRuleResult.A_4, RiskRuleResult.A_8, RiskRuleResult.A_13, RiskRuleResult.A_14));
//        houseSell.setRiskRuleModel(houseCover);
//        models.add(houseAmount);
//        //【房产总价值≤300万】且【房产数量>2】且【未变卖】且【有闲置或出租】
//        RiskRuleModel houseAmount2 = RiskRuleModel.strategy(model.getHouseAmount(),RiskRuleModel.million_300,RiskRuleCondition.equal_less,RiskRuleModel.raise);
//        RiskRuleModel houseRisk2 = RiskRuleModel.strategy(model.getHouseNum(),RiskRuleModel.num_2,RiskRuleCondition.greater,RiskRuleModel.raise);
//        houseAmount2.setRiskRuleModel(houseRisk2);
//        RiskRuleModel houseSell2 = RiskRuleModel.strategy(model.getPropertySoldStatus(), PreposeMaterialModel.HasSellEnum.NO_SELL.getCode(),RiskRuleCondition.equal,RiskRuleModel.raise);
//        houseRisk2.setRiskRuleModel(houseSell2);
//        RiskRuleModel houseCover2 = RiskRuleModel.strategy(model.getHouseLiveStatus(), HouseUseStateEnum.IDLENESS.getCode(),RiskRuleCondition.equal,
//                List.of(RiskRuleResult.A_4, RiskRuleResult.A_8, RiskRuleResult.A_13, RiskRuleResult.A_15));
//        houseSell2.setRiskRuleModel(houseCover2);
//        models.add(houseAmount2);
//        //【300万<房产总价值≤500万】且【房产数量≥2】且【有闲置或出租】
//        RiskRuleModel houseAmount3 = RiskRuleModel.strategy(model.getHouseAmount(),List.of(RiskRuleModel.million_300, RiskRuleModel.million_500),RiskRuleCondition.equal_less,RiskRuleModel.raise);
//        RiskRuleModel houseRisk3 = RiskRuleModel.strategy(model.getHouseNum(),RiskRuleModel.num_2,RiskRuleCondition.equal_greater,RiskRuleModel.raise);
//        houseAmount3.setRiskRuleModel(houseRisk3);
//        RiskRuleModel houseSell3 = RiskRuleModel.strategy(model.getHouseLiveStatus(), HouseUseStateEnum.IDLENESS.getCode(),RiskRuleCondition.equal,List.of(RiskRuleResult.A_4, RiskRuleResult.A_8, RiskRuleResult.A_15));
//        houseRisk3.setRiskRuleModel(houseSell3);
//        models.add(houseAmount3);


        /**
         * 车产价值及数量
         *（价值为区间值，则以最大值做计算）
         */
        //可发
        //【车产数量≥2】且【车产总价值＜30万 】且【刚需用车】且【均为普通品牌】且【车产总价值<未来花费金额】
        RiskRuleModel carNum = RiskRuleModel.strategy(model.getCarNum(),RiskRuleModel.num_2,RiskRuleCondition.equal_greater,RiskRuleModel.raise);
        RiskRuleModel carAmount = RiskRuleModel.strategy(model.getCarAmount(),RiskRuleModel.hundred_thousand_3,RiskRuleCondition.less,RiskRuleModel.raise);
        carNum.setRiskRuleModel(carAmount);
        RiskRuleModel carUseStatus = RiskRuleModel.strategy(model.getCarUseStatus(), CarUseStateEnum.JUST_NEED.getCode(),RiskRuleCondition.equal,RiskRuleModel.raise);
        carAmount.setRiskRuleModel(carUseStatus);
        RiskRuleModel carCover = RiskRuleModel.strategy(model.getCarAmount(), model.getSpentAmount(),RiskRuleCondition.less,RiskRuleModel.raise);
        carUseStatus.setRiskRuleModel(carCover);
        RiskRuleModel carBrand = RiskRuleModel.strategy(CollectionUtils.subtract(model.getCarBrandTypes().stream().distinct().collect(Collectors.toList()),
                List.of(CarBrandTypeEnum.GENERAL.getCode())).size(), 0,RiskRuleCondition.equal,Lists.newArrayList(RiskRuleResult.A_A_3));
        carCover.setRiskRuleModel(carBrand);
        models.add(carNum);
        //不可发
        //【车产数量<2】且【车产总价值≥20 万 】
        RiskRuleModel carNum2 = RiskRuleModel.strategy(model.getCarNum(),RiskRuleModel.num_2,RiskRuleCondition.less,RiskRuleModel.raise);
        RiskRuleModel carAmount2 = RiskRuleModel.strategy(model.getCarAmount(),RiskRuleModel.hundred_thousand_2,RiskRuleCondition.equal_greater,Lists.newArrayList(RiskRuleResult.A_16, RiskRuleResult.A_7));
        carNum2.setRiskRuleModel(carAmount2);
        models.add(carNum2);
        //【车产数量≥2】且【车产总价值＜30万 】且【有闲置或出租】
        RiskRuleModel carNum3 = RiskRuleModel.strategy(model.getCarNum(),RiskRuleModel.num_2,RiskRuleCondition.equal_greater,RiskRuleModel.raise);
        RiskRuleModel carAmount3 = RiskRuleModel.strategy(model.getCarAmount(),RiskRuleModel.hundred_thousand_3,RiskRuleCondition.less,RiskRuleModel.raise);
        carNum3.setRiskRuleModel(carAmount3);
        RiskRuleModel carJustNeed3 = RiskRuleModel.strategy(model.getCarUseStatus(),CarUseStateEnum.NO_JUST_NEED.getCode(),RiskRuleCondition.equal,Lists.newArrayList(RiskRuleResult.A_16, RiskRuleResult.A_7, RiskRuleResult.A_18));
        carAmount3.setRiskRuleModel(carJustNeed3);
        models.add(carNum3);
        //【车产数量≥2】且【车产总价值≥30万】
        RiskRuleModel carNum4 = RiskRuleModel.strategy(model.getCarNum(),RiskRuleModel.num_2,RiskRuleCondition.equal_greater,RiskRuleModel.raise);
        RiskRuleModel carAmount4 = RiskRuleModel.strategy(model.getCarAmount(),RiskRuleModel.hundred_thousand_3,RiskRuleCondition.equal_greater,Lists.newArrayList(RiskRuleResult.A_16, RiskRuleResult.A_7));
        carNum4.setRiskRuleModel(carAmount4);
        models.add(carNum4);
        //【车产数量≥2】且【车产总价值＜30万 】且【存在知名豪车】（若当所选品牌中知名豪车品牌≥1时，即判断为存在豪车）
        RiskRuleModel carNum5 = RiskRuleModel.strategy(model.getCarNum(),RiskRuleModel.num_2,RiskRuleCondition.equal_greater,RiskRuleModel.raise);
        RiskRuleModel carAmount5 = RiskRuleModel.strategy(model.getCarAmount(),RiskRuleModel.hundred_thousand_3,RiskRuleCondition.less, RiskRuleModel.raise);
        carNum5.setRiskRuleModel(carAmount5);
        RiskRuleModel carJustNeed5 = RiskRuleModel.strategy(CollectionUtils.intersection(model.getCarBrandTypes(), List.of(CarBrandTypeEnum.EXPENSIVE.getCode())).size(),0,RiskRuleCondition.greater,
                Lists.newArrayList(RiskRuleResult.A_16, RiskRuleResult.A_7, RiskRuleResult.A_17));
        carAmount5.setRiskRuleModel(carJustNeed5);
        models.add(carNum5);
        //【车产数量≥2】且【车产总价值＜30万 】且【其他】
        RiskRuleModel carNum6 = RiskRuleModel.strategy(model.getCarNum(),RiskRuleModel.num_2,RiskRuleCondition.equal_greater,RiskRuleModel.raise);
        RiskRuleModel carAmount6 = RiskRuleModel.strategy(model.getCarAmount(),RiskRuleModel.hundred_thousand_3,RiskRuleCondition.less, RiskRuleModel.raise);
        carNum6.setRiskRuleModel(carAmount6);
        RiskRuleModel carJustNeed6 = RiskRuleModel.strategy(CollectionUtils.intersection(model.getCarBrandTypes(), List.of(CarBrandTypeEnum.DONT_KNOWN.getCode())).size(),0,RiskRuleCondition.greater,
                Lists.newArrayList(RiskRuleResult.A_16, RiskRuleResult.A_7, RiskRuleResult.A_17));
        carAmount6.setRiskRuleModel(carJustNeed6);
        models.add(carNum6);
        //【车产数量≥2】且【车产总价值＜30万 】且【刚需用车】且【均为普通品牌】且【车产总价值≥未来花费金额】
        RiskRuleModel carNum7 = RiskRuleModel.strategy(model.getCarNum(),RiskRuleModel.num_2,RiskRuleCondition.equal_greater,RiskRuleModel.raise);
        RiskRuleModel carAmount7 = RiskRuleModel.strategy(model.getCarAmount(),RiskRuleModel.hundred_thousand_3,RiskRuleCondition.less, RiskRuleModel.raise);
        carNum7.setRiskRuleModel(carAmount7);
        RiskRuleModel carJustNeed7 = RiskRuleModel.strategy(model.getCarUseStatus(),CarUseStateEnum.JUST_NEED.getCode(),RiskRuleCondition.equal, RiskRuleModel.raise);
        carAmount7.setRiskRuleModel(carJustNeed7);
        RiskRuleModel carBrand7 = RiskRuleModel.strategy(
                CollectionUtils.subtract(model.getCarBrandTypes().stream().distinct().collect(Collectors.toList()), List.of(CarBrandTypeEnum.GENERAL.getCode())).size(),0,RiskRuleCondition.equal, RiskRuleModel.raise);
        carJustNeed7.setRiskRuleModel(carBrand7);
        RiskRuleModel carCover7 = RiskRuleModel.strategy(model.getCarAmount(),model.getSpentAmount(),RiskRuleCondition.equal_greater,
                Lists.newArrayList(RiskRuleResult.A_16, RiskRuleResult.A_7, RiskRuleResult.A_18, RiskRuleResult.A_17, RiskRuleResult.A_1));
        carBrand7.setRiskRuleModel(carCover7);
        models.add(carNum7);


        /**
         * 家庭年收入
         * (价值为区间值，则以最大值做计算）
         */
        //不可发
        //【家庭年收入<30万】且【家庭年收入≥ 未来花费金额】
        RiskRuleModel homeIncomeRisk = RiskRuleModel.strategy(model.getHomeIncomeRange(), RiskRuleModel.hundred_thousand_3, RiskRuleCondition.less, RiskRuleModel.raise);
        RiskRuleModel spentRisk = RiskRuleModel.strategy(model.getHomeIncomeRange(), model.getSpentAmount(), RiskRuleCondition.equal_greater, Lists.newArrayList(RiskRuleResult.A_11, RiskRuleResult.A_1));
        homeIncomeRisk.setRiskRuleModel(spentRisk);
        models.add(homeIncomeRisk);
        //【家庭年收入≥30万】
        models.add(RiskRuleModel.strategy(model.getHomeIncomeRange(),RiskRuleModel.hundred_thousand_3,RiskRuleCondition.equal_greater,Lists.newArrayList(RiskRuleResult.A_11)));


        /**
         * 家庭金融资产与未来花费关系
         */
        //不可发
        //【家庭金融总资产＜30万】且【家庭金融资产总价值 >= 未来花费金额】
        RiskRuleModel financeRisk = RiskRuleModel.strategy(model.getFinanceAmount(), RiskRuleModel.hundred_thousand_3, RiskRuleCondition.less, RiskRuleModel.raise);
        RiskRuleModel spentAndFinanceRisk = RiskRuleModel.strategy(model.getFinanceAmount(), model.getSpentAmount(), RiskRuleCondition.equal_greater, Lists.newArrayList(RiskRuleResult.A_2, RiskRuleResult.A_1));
        financeRisk.setRiskRuleModel(spentAndFinanceRisk);
        models.add(financeRisk);
        //【家庭金融总资产≥30万】
        models.add(RiskRuleModel.strategy(model.getFinanceAmount(),RiskRuleModel.hundred_thousand_3,RiskRuleCondition.equal_greater,Lists.newArrayList(RiskRuleResult.A_2)));


        /**
         * 剩余款项与未来花费关系
         */
        //不可发
        //剩余款项 ≥ 未来花费金额
        models.add(RiskRuleModel.strategy(model.getRemainAmount(),model.getSpentAmount(),RiskRuleCondition.equal_greater,Lists.newArrayList(RiskRuleResult.A_12, RiskRuleResult.A_1)));


        /**
         * 人身险
         */
        //不可发
        //【有人身险】且【已赔付】且【可覆盖】
        RiskRuleModel personalInsuranceRule = RiskRuleModel.strategy(model.getHasPersonalInsurance(), 1, RiskRuleCondition.equal, RiskRuleModel.raise);
        RiskRuleModel compensate = RiskRuleModel.strategy(model.getHasPersonalInsurance(), InsurancePayoutEnum.PAID.getCode(), RiskRuleCondition.equal, RiskRuleModel.raise);
        personalInsuranceRule.setRiskRuleModel(compensate);
        RiskRuleModel insuranceCover = RiskRuleModel.strategy(model.getInsurancePayoutCTC(), CoverCureEnum.CAN_COVER.getCode(), RiskRuleCondition.equal, Lists.newArrayList(RiskRuleResult.A_19, RiskRuleResult.A_20, RiskRuleResult.A_21));
        compensate.setRiskRuleModel(insuranceCover);
        models.add(personalInsuranceRule);
        //可发
        //【有人身险】且【不可赔付】
        RiskRuleModel personalInsuranceRule2 = RiskRuleModel.strategy(model.getHasPersonalInsurance(), 1, RiskRuleCondition.equal, RiskRuleModel.raise);
        RiskRuleModel compensate2 = RiskRuleModel.strategy(model.getHasPersonalInsurance(), InsurancePayoutEnum.NO_COMPENSATE.getCode(), RiskRuleCondition.equal, Lists.newArrayList(RiskRuleResult.A_A_4));
        personalInsuranceRule2.setRiskRuleModel(compensate2);
        models.add(personalInsuranceRule2);
        //【有人身险】且【后续可赔付】
        RiskRuleModel personalInsuranceRule3 = RiskRuleModel.strategy(model.getHasPersonalInsurance(), 1, RiskRuleCondition.equal, RiskRuleModel.raise);
        RiskRuleModel compensate3 = RiskRuleModel.strategy(model.getInsurancePayoutStatus(), InsurancePayoutEnum.AFTER_PAID.getCode(), RiskRuleCondition.equal, Lists.newArrayList(RiskRuleResult.A_A_5));
        personalInsuranceRule3.setRiskRuleModel(compensate3);
        models.add(personalInsuranceRule3);
        //【有人身险】且【已赔付】且【不可覆盖】
        RiskRuleModel personalInsuranceRule4 = RiskRuleModel.strategy(model.getHasPersonalInsurance(), 1, RiskRuleCondition.equal, RiskRuleModel.raise);
        RiskRuleModel compensate4 = RiskRuleModel.strategy(model.getHasPersonalInsurance(), InsurancePayoutEnum.PAID.getCode(), RiskRuleCondition.equal, RiskRuleModel.raise);
        personalInsuranceRule4.setRiskRuleModel(compensate4);
        RiskRuleModel insuranceCover4 = RiskRuleModel.strategy(model.getInsurancePayoutCTC(), CoverCureEnum.NOT_COVER.getCode(), RiskRuleCondition.equal, Lists.newArrayList(RiskRuleResult.A_A_6));
        compensate4.setRiskRuleModel(insuranceCover4);
        models.add(personalInsuranceRule4);
        //【有人身险】且【不清楚】
        RiskRuleModel personalInsuranceRule5 = RiskRuleModel.strategy(model.getHasPersonalInsurance(), 1, RiskRuleCondition.equal, RiskRuleModel.raise);
        RiskRuleModel compensate5 = RiskRuleModel.strategy(model.getHasPersonalInsurance(), InsurancePayoutEnum.DONT_KNOWN.getCode(), RiskRuleCondition.equal, Lists.newArrayList(RiskRuleResult.A_A_5));
        personalInsuranceRule5.setRiskRuleModel(compensate5);
        models.add(personalInsuranceRule5);


        /**
         * 事故造成
         */
        //不可发
        //【事故造成】且【已赔偿】且【可覆盖】
        RiskRuleModel accidentRule = RiskRuleModel.strategy(model.getAccidentStatus(), 1, RiskRuleCondition.equal, RiskRuleModel.raise);
        RiskRuleModel accidentCompensate = RiskRuleModel.strategy(model.getAccidentPayoutStatus(), InsurancePayoutEnum.PAID.getCode(), RiskRuleCondition.equal, RiskRuleModel.raise);
        accidentRule.setRiskRuleModel(accidentCompensate);
        RiskRuleModel accidentCover = RiskRuleModel.strategy(model.getAccidentPayoutCTC(), CoverCureEnum.CAN_COVER.getCode(), RiskRuleCondition.equal, Lists.newArrayList(RiskRuleResult.A_22, RiskRuleResult.A_20, RiskRuleResult.A_21));
        accidentCompensate.setRiskRuleModel(accidentCover);
        models.add(accidentRule);
        //可发
        //【事故造成】且【不可赔偿】
        RiskRuleModel accidentRule1 = RiskRuleModel.strategy(model.getAccidentStatus(), 1, RiskRuleCondition.equal, RiskRuleModel.raise);
        RiskRuleModel accidentCompensate1 = RiskRuleModel.strategy(model.getAccidentPayoutStatus(), InsurancePayoutEnum.NO_COMPENSATE.getCode(), RiskRuleCondition.equal, Lists.newArrayList(RiskRuleResult.A_A_7));
        accidentRule1.setRiskRuleModel(accidentCompensate1);
        models.add(accidentRule1);
        //【事故造成】且【不清楚】
        RiskRuleModel accidentRule2 = RiskRuleModel.strategy(model.getAccidentStatus(), 1, RiskRuleCondition.equal, RiskRuleModel.raise);
        RiskRuleModel accidentCompensate2 = RiskRuleModel.strategy(model.getAccidentPayoutStatus(), InsurancePayoutEnum.DONT_KNOWN.getCode(), RiskRuleCondition.equal, Lists.newArrayList(RiskRuleResult.A_A_8));
        accidentRule2.setRiskRuleModel(accidentCompensate2);
        models.add(accidentRule2);
        //【事故造成】且【后续可赔付】
        RiskRuleModel accidentRule3 = RiskRuleModel.strategy(model.getAccidentStatus(), 1, RiskRuleCondition.equal, RiskRuleModel.raise);
        RiskRuleModel accidentCompensate3 = RiskRuleModel.strategy(model.getAccidentPayoutStatus(), InsurancePayoutEnum.AFTER_PAID.getCode(), RiskRuleCondition.equal, Lists.newArrayList(RiskRuleResult.A_A_8));
        accidentRule3.setRiskRuleModel(accidentCompensate3);
        models.add(accidentRule3);
        //【事故造成】且【已赔偿】且【不可覆盖】
        RiskRuleModel accidentRule4 = RiskRuleModel.strategy(model.getAccidentStatus(), 1, RiskRuleCondition.equal, RiskRuleModel.raise);
        RiskRuleModel accidentCompensate4 = RiskRuleModel.strategy(model.getAccidentPayoutStatus(), InsurancePayoutEnum.PAID.getCode(), RiskRuleCondition.equal, Lists.newArrayList(RiskRuleResult.A_A_8));
        accidentRule4.setRiskRuleModel(accidentCompensate4);
        RiskRuleModel accidentCover4 = RiskRuleModel.strategy(model.getAccidentPayoutCTC(), CoverCureEnum.NOT_COVER.getCode(), RiskRuleCondition.equal, Lists.newArrayList(RiskRuleResult.A_A_9));
        accidentCompensate4.setRiskRuleModel(accidentCover4);
        models.add(accidentRule4);

        return judge(models);
    }

    /**
     * 用于计算是否足以承担未来花费
     * @param model
     * @return
     */
    private int getNum(EconomyModel model){
        Double d = (model.getHouseAmount()+model.getCarAmount()+model.getFinanceAmount()-model.getDebtAmount()) * 0.15;
        return d.intValue();
    }
}
