package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.CfReportModuleOperationLogBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfReportModuleOperationLogDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportModuleOperationLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/4/22
 */
@Service
public class CfReportModuleOperationLogBizImpl implements CfReportModuleOperationLogBiz {

    @Autowired
    private CfReportModuleOperationLogDao cfReportModuleOperationLogDao;

    @Override
    public List<CfReportModuleOperationLog> getByLabelId(int labelId) {
        return cfReportModuleOperationLogDao.getByLabelId(labelId);
    }

    @Override
    public int addLog(int labelId, String action, String operator) {
        return cfReportModuleOperationLogDao.addLog(labelId, action, operator);
    }
}
