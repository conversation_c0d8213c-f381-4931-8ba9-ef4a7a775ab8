package com.shuidihuzhu.cf.biz.crowdfunding.image.impl;

import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.crowdfunding.image.AdminImageHandlerBiz;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyProgress;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.cf.service.AdminImageService;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditCaseDetail;
import com.shuidihuzhu.cf.vo.crowdfunding.CfProgressVo;
import com.shuidihuzhu.common.web.util.ContextUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Author: wangpeng
 * @Date: 2021/1/27 11:06
 * @Description:
 */
@Component
public class AdminImageHandlerBizImpl implements AdminImageHandlerBiz {

    @Resource
    private SeaAccountClientV1 seaAccountClientV1;
    @Resource
    private AdminImageService adminImageService;

    @Override
    public String imageHandlerWatermark(String imageUrl, int userId) {
        if (StringUtils.isEmpty(imageUrl)) {
            return imageUrl;
        }
        if (imageUrl.contains("?waterMark=true")) {
            return imageUrl;
        }
        AuthRpcResponse<AdminUserAccountModel> validUserAccountResponse = seaAccountClientV1.getValidUserAccountById(userId);
        if (Objects.isNull(validUserAccountResponse)) {
            return imageUrl;
        }
        if (!validUserAccountResponse.isSuccess()) {
            return imageUrl;
        }
        AdminUserAccountModel result = validUserAccountResponse.getResult();
        if (Objects.isNull(result)) {
            return imageUrl;
        }
        imageUrl = adminImageService.convertSingleUrl(imageUrl);

        String mobile = StringUtils.isNotEmpty(result.getMobile()) && result.getMobile().length() >= 4 ? result.getMobile().substring(result.getMobile().length() -4) : "";

        String waterMark = "                    " + result.getName() + mobile + "-仅供审核使用                                  ";
        String dynamicWaterMarkUrl = adminImageService.dynamicWaterMark(imageUrl, waterMark);
        return StringUtils.isEmpty(dynamicWaterMarkUrl) ? imageUrl : dynamicWaterMarkUrl + "?waterMark=true";
    }

    @Override
    public void handlerCaseInfo(InitialAuditCaseDetail.CaseBaseInfo caseBaseInfo) {
        if (Objects.isNull(caseBaseInfo)) {
            return;
        }
    }

    @Override
    public void handlerCfInfoSupplyProgress(CfInfoSupplyProgress cfInfoSupplyProgress) {
        if (Objects.isNull(cfInfoSupplyProgress)) {
            return;
        }
        if (StringUtils.isEmpty(cfInfoSupplyProgress.getImgUrls())) {
            return;
        }
//        List<String> strings = Splitter.on(",")
//                .splitToList(cfInfoSupplyProgress.getImgUrls())
//                .stream()
//                .map(this::imageHandlerWatermark)
//                .collect(Collectors.toList());
//        cfInfoSupplyProgress.setImgUrls(Joiner.on(",").join(strings));
    }

    @Override
    public void handlerCfProgressVo(CfProgressVo cfProgressVo) {
        if (Objects.isNull(cfProgressVo)) {
            return;
        }
        if (StringUtils.isEmpty(cfProgressVo.getImageUrls())) {
            return;
        }
//        List<String> strings = Splitter.on(",")
//                .splitToList(cfProgressVo.getImageUrls())
//                .stream()
//                .map(this::imageHandlerWatermark)
//                .collect(Collectors.toList());
//        cfProgressVo.setImageUrls(Joiner.on(",").join(strings));
    }

    @Override
    public void handlerCrowdfundingReport(CrowdfundingReport crowdfundingReport) {
//        List<String> collect = Splitter.on(",").splitToList(crowdfundingReport.getImageUrls())
//                .stream()
//                .map(this::imageHandlerWatermark)
//                .collect(toList());
//        String join = Joiner.on(",").join(collect);
//        crowdfundingReport.setImageUrls(join);
    }

}
