package com.shuidihuzhu.cf.biz.message;

import com.shuidihuzhu.common.web.model.message.HasMulitMpUserInfo;
import com.shuidihuzhu.wx.model.WxMpConfig;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Author: <PERSON>
 * Date: 2017/9/22 15:11
 */
public class AdminUserSetMpSelectStrategyAdmin implements AdminMulitMpSelectStrategy {
	@Override
	public boolean selectThenFill(HasMulitMpUserInfo hasMulitMpUserInfo, List<WxMpConfig> wxAppInfos) {
		return true;
	}

	@Override
	public List<Boolean> selectThenFill(List<HasMulitMpUserInfo> hasMulitMpUserInfos, List<WxMpConfig> wxAppInfos) {
		return Stream.iterate(0, t -> t.intValue())
		             .map(integer -> true)
		             .limit(hasMulitMpUserInfos.size())
		             .collect(Collectors.toList());
	}
}
