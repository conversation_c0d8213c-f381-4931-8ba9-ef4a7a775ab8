package com.shuidihuzhu.cf.biz.admin.channel;

import com.shuidihuzhu.cf.model.admin.channel.CfChannel;

import java.util.List;
import java.util.Map;

/**
 * Created by wangsf on 17/2/16.
 */
public interface CfChannelBiz {

	int add(CfChannel channel);

	int update(int channelId, int groupId, String description);

	CfChannel getByName(String name);

	CfChannel get(int id);

	List<CfChannel> listByIds(List<Integer> channelIds);

	Map<Integer, CfChannel> getMapByIds(List<Integer> channelIds);

	List<CfChannel> listByGroupId(int groupId, int anchorId, int limit);

	List<CfChannel> list(int anchorId, int limit);

	List<CfChannel> listByGroupIdAndPage(int groupId, int page, int size);

	List<CfChannel> listByPage(int page, int size);

	/**
	 * 前缀搜索
	 * @param name
	 * @param groupId
	 * @return
	 */
	List<CfChannel> prefixSearch(String name, int groupId, int anchorId, int limit);

	List<CfChannel> listByGroupIdAndPage(int groupId);
}
