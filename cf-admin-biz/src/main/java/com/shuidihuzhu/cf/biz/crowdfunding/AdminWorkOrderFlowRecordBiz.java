package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderFlowRecord;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowStatistics;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * Created by Ahrievil on 2017/12/25
 */
public interface AdminWorkOrderFlowRecordBiz {

    int insertOne(AdminWorkOrderFlowRecord adminWorkOrderFlowRecord);

    int insertList(List<AdminWorkOrderFlowRecord> list);

    List<AdminWorkOrderFlowRecord> selectAllByWorkOrderId(long workOrderId, List<Integer> operateTypeCodes);

    List<AdminWorkOrderFlowRecord> selectByOperatorIdsAndTime(AdminWorkOrderFlowStatistics.searchParam param);

    List<AdminWorkOrderFlowRecord> selectWithAssignRecords(Date beginTime, Date endTime, int orderOperatorId);
}
