package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.admin.AdminWorkOrderBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.dao.admin.AdminWorkOrderDao;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminTaskUgcDao;
import com.shuidihuzhu.cf.dao.crowdfunding.CfSensitiveWordRecordDao;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminUGCTask;
import com.shuidihuzhu.cf.enums.crowdfunding.CfSensitiveWordRecordEnum;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminUserCommentVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by Ahrievil on 2017/11/30
 */
@Service
@Slf4j
public class AdminTaskUgcBizImpl implements AdminTaskUgcBiz {

    @Autowired
    private CfSensitiveWordRecordDao cfSensitiveWordRecordDao;
    @Autowired
    private AdminTaskUgcDao adminTaskUgcDao;

    @Autowired
    private AdminCrowdfundingCommentBiz adminCrowdfundingCommentBiz;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;

    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Autowired
    private AdminCrowdFundingVerificationBiz adminCrowdFundingVerificationBiz;

    @Resource
    private AdminCrowdfundingOrderBiz adminCrowdfundingOrderBiz;

    @Resource
    private AdminCrowdFundingProgressBiz adminCrowdFundingProgressBiz;

    @Autowired
    private AdminWorkOrderDao adminWorkOrderDao;

    @Override
    public int insertOne(AdminTaskUgc adminTaskUgc) {
        return adminTaskUgcDao.insertOne(adminTaskUgc);
    }

    @Override
    public int insertList(List<AdminTaskUgc> list) {
        if (CollectionUtils.isEmpty(list)) return 0;
        return adminTaskUgcDao.insertList(list);
    }

    @Override
    public List<AdminTaskUgc> selectByWorkOrderIds(List<Long> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        } else {
            List<AdminTaskUgc> adminTaskUgcs = new ArrayList<>();
            List<List<Long>> splitList = Lists.partition(list, 500);//分割
            for (List<Long> split : splitList) {//分批查询
                List<AdminTaskUgc> ugcs = adminTaskUgcDao.selectByWorkOrderIds(split);
                if (ugcs != null && ugcs.size() > 0) {
                    adminTaskUgcs.addAll(ugcs);
                }
            }
            return adminTaskUgcs;
        }
    }

    @Override
    public List<AdminTaskUgc> selectModulesLimit(int modules, int start, int size) {
        return adminTaskUgcDao.selectModulesLimit(modules, start, size);
    }

    @Override
    public AdminTaskUgc selectByWorkOrderId(long workOrderId) {
        return adminTaskUgcDao.selectByWorkOrderId(workOrderId);
    }

    @Override
    public int updateResult(long id, int result) {
        return adminTaskUgcDao.updateResult(id, result);
    }

    @Override
    public int deleteByWordIdList(List<Long> list) {
        if (CollectionUtils.isEmpty(list)) return 0;
        return adminTaskUgcDao.deleteByWordIdList(list);
    }


    @Override
    public AdminTaskUgc selectFirstByCaseId(long caseId) {
        return adminTaskUgcDao.selectFirstByCaseId(caseId);
    }

    @Override
    public String selectFirstCommentByCaseId(long caseId) {
        return adminTaskUgcDao.selectFirstCommentByCaseId(caseId);
    }


    @Override
    public AdminUserCommentVo getCommentList(long wordId, long workOrderId, int caseId, int contentType) {
        return getCommentListByWordId(wordId,workOrderId,caseId,contentType);
    }

    private AdminUserCommentVo getCommentListByWordId(long wordId, long workOrderId, int caseId, int contentType) {
        CfSensitiveWordRecord cfSensitiveWordRecord = this.selectById(wordId);

        List<AdminUserComment> comments = Lists.newArrayList();
        AdminUserCommentVo vo = new AdminUserCommentVo();

        if (Objects.nonNull(cfSensitiveWordRecord)) {
            //如果是证实
            if (cfSensitiveWordRecord.getBizType() == CfSensitiveWordRecordEnum.BizType.VERIFICATION.value()) {
                CrowdFundingVerification verification = adminCrowdFundingVerificationBiz.getById(cfSensitiveWordRecord.getBizId());
                comments.add(getAdminUserComment(verification, workOrderId));
            }

            // 如果是动态评论
            if (cfSensitiveWordRecord.getBizType() == CfSensitiveWordRecordEnum.BizType.COMMENT_PROGRESS.value()) {
                fillProgressContent(cfSensitiveWordRecord, vo);
                fillComment(caseId, workOrderId, cfSensitiveWordRecord, comments);
            }

            // 如果是捐款评论
            if (cfSensitiveWordRecord.getBizType() == CfSensitiveWordRecordEnum.BizType.COMMENT_ORDER.value()) {
                fillOrderContent(cfSensitiveWordRecord, vo);
                fillComment(caseId, workOrderId, cfSensitiveWordRecord, comments);
            }

            // 如果是捐款
            if (cfSensitiveWordRecord.getBizType() == CfSensitiveWordRecordEnum.BizType.ORDER.value()) {
                long userId = cfSensitiveWordRecord.getUserId();
                UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByUserId(userId);
                vo.setParentUserName(userInfoModel.getNickname());
                String content = cfSensitiveWordRecord.getContent();
                vo.setParentContent(content);
            }
        }


        CrowdfundingInfo info = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);

        vo.setCaseTitle(info.getTitle());
        vo.setCaseId(info.getId());
        vo.setContentTypeStr(AdminUGCTask.Content.getByCode(contentType).getWord());
        vo.setComments(comments);

        return vo;
    }

    private CfSensitiveWordRecord selectById(long id) {
        return cfSensitiveWordRecordDao.selectById(id);
    }

    /**
     * 填充评论主体信息 动态 or 捐款 内容
     *
     * @param cfSensitiveWordRecord
     * @param vo
     */
    private void fillProgressContent(CfSensitiveWordRecord cfSensitiveWordRecord, AdminUserCommentVo vo) {
        int parentBizId = Math.toIntExact(cfSensitiveWordRecord.getParentBizId());
        if (parentBizId <= 0) {
            return;
        }
        CrowdFundingProgress progress = adminCrowdFundingProgressBiz.getActivityProgressById(parentBizId);
        if (progress == null) {
            return;
        }
        long userId = progress.getUserId();
        UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByUserId(userId);
        vo.setParentUserName(userInfoModel.getNickname());
        vo.setParentContent(progress.getContent());
    }

    private void fillOrderContent(CfSensitiveWordRecord cfSensitiveWordRecord, AdminUserCommentVo vo) {
        long parentBizId = cfSensitiveWordRecord.getParentBizId();
        if (parentBizId <= 0) {
            return;
        }
        CrowdfundingOrder order = adminCrowdfundingOrderBiz.getById(parentBizId);
        if (order == null) {
            return;
        }
        long userId = order.getUserId();
        UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByUserId(userId);
        vo.setParentUserName(userInfoModel.getNickname());
        vo.setParentContent(order.getComment());
    }

    private void fillComment(int caseId,long workOrderId, CfSensitiveWordRecord cfSensitiveWordRecord, List<AdminUserComment> comments) {
        long commentId = cfSensitiveWordRecord.getBizId();
        CrowdfundingComment crowdfundingComment = adminCrowdfundingCommentBiz.getByIdNoCareDeleted( commentId,caseId);
        List<CrowdfundingComment> list = adminCrowdfundingCommentBiz.getCommentByParentId(caseId, crowdfundingComment.getParentId());
        comments.addAll(getAdminUserComment(list, workOrderId));
    }

    private List<AdminUserComment> getAdminUserComment(List<CrowdfundingComment> list, long workOrderId) {

        List<AdminUserComment> result = Lists.newArrayList();

        if (CollectionUtils.isEmpty(list)) {
            return result;
        }

        Map<Long, List<CrowdfundingComment>> child = list.stream().collect(Collectors.groupingBy(CrowdfundingComment::getId));

        list.stream().forEach(r -> {

            List<CrowdfundingComment> c = child.get(r.getCommentId());
            long replyUser = 0;
            if (CollectionUtils.isNotEmpty(c)) {
                replyUser = c.get(0).getUserId();
            }
            result.add(getAdminUserComment(r, workOrderId, replyUser));
        });

        return result;
    }

    private AdminUserComment getAdminUserComment(CrowdfundingComment crowdfundingComment, long workOrderId, long replyUser) {

        AdminUserComment comment = new AdminUserComment();

        long userId = crowdfundingComment.getUserId();
        try {
            List<Long> userIds = Lists.newArrayList(userId);
            if (replyUser > 0) {
                userIds.add(replyUser);
            }
            List<UserInfoModel> userInfoModels = userInfoServiceBiz.getUserInfoByUserIdBatch(userIds);
            if (CollectionUtils.isNotEmpty(userInfoModels)) {
                userInfoModels.stream().forEach(r -> {
                    if (r.getUserId() == userId) {
                        comment.setUserName(r.getNickname());
                    }
                    if (r.getUserId() == replyUser) {
                        comment.setReplyUser(r.getNickname());
                    }
                });
            }
        } catch (Exception e) {
            log.error("getUserInfoByUserId error userId={}", userId, e);
        }
        comment.setUserId(crowdfundingComment.getUserId());
        comment.setCommentId(crowdfundingComment.getId());
        comment.setWorkOrderId(workOrderId);
        comment.setContent(crowdfundingComment.getContent());
        comment.setParentId(crowdfundingComment.getCommentId() == null ? 0 : crowdfundingComment.getCommentId());

        return comment;
    }


    private AdminUserComment getAdminUserComment(CrowdFundingVerification verification, long workOrderId) {

        if (verification == null) {
            log.info("getAdminUserComment verification=null workOrderId={}", workOrderId);
            return null;
        }
        AdminUserComment comment = new AdminUserComment();

        long userId = verification.getVerifyUserId();
        comment.setUserName(verification.getUserName());
        comment.setUserId(userId);
        comment.setCommentId(verification.getId());
        comment.setWorkOrderId(workOrderId);
        comment.setContent(verification.getDescription());

        return comment;
    }

    @Override
    public List<AdminTaskUgc> selectByCreateTimeAndTaskType(Date beginDate, Date endDate, int contentType, int limit) {

        if (beginDate == null || endDate == null || beginDate.after(endDate)) {
            return Lists.newArrayList();
        }

        return adminTaskUgcDao.selectByCreateTimeAndTaskType(beginDate, endDate, contentType, limit);
    }

    @Override
    public List<AdminTaskUgc> selectByUpdateTimeAndTaskStatus(Date beginDate, Date endDate,
                                                              int contentType, List<Integer> resultStatus, int limit) {

        if (beginDate == null || endDate == null || beginDate.after(endDate)
                || CollectionUtils.isEmpty(resultStatus)) {
            return Lists.newArrayList();
        }

        return adminTaskUgcDao.selectByUpdateTimeAndTaskStatus(beginDate, endDate, contentType, resultStatus, limit);
    }

    @Override
    public AdminTaskUgc selectLatelyTaskByCaseIdAndContent(int caseId, int contentType) {
        return adminTaskUgcDao.selectLatelyTaskByCaseIdAndContent(caseId, contentType);
    }

    @Override
    public Map<String, Object> countUnHandleBaseInfoOrder() {

        Long id = null;
        int unHandleSize = 0;
        int firstApproveUnHandleCount = 0;

        while (true) {
            List<AdminWorkOrder> unHandleOrders = adminWorkOrderDao.selectUnHandleOrders(AdminWorkOrderConst.Type.UGC.getCode(),
                    AdminWorkOrderConst.Task.INFO_BASE_WORD.getCode(), id, AdminUGCTask.ADMIN_ORDER_TABLE_SCAN_SIZE);

            unHandleSize += unHandleOrders.size();
            firstApproveUnHandleCount += getFirstApprovePassWorkOrder(unHandleOrders).size();

            if (unHandleOrders.size() != AdminUGCTask.ADMIN_ORDER_TABLE_SCAN_SIZE) {
                break;
            }

            id = unHandleOrders.get(AdminUGCTask.ADMIN_ORDER_TABLE_SCAN_SIZE - 1).getId();
        }

        Map<String, Object> result = Maps.newHashMap();
        result.put("unhandleCount", unHandleSize);
        result.put("firstApproveUnHandleCount", firstApproveUnHandleCount);

        return result;
    }

    public List<AdminWorkOrder> getFirstApprovePassWorkOrder(List<AdminWorkOrder> baseInfoUgcOrders) {

        List<AdminWorkOrder> firstApprovePass = Lists.newArrayList();
        if (CollectionUtils.isEmpty(baseInfoUgcOrders)) {
            return firstApprovePass;
        }

        Map<Long, AdminWorkOrder> idOrderMapping = Maps.newHashMap();
        List<Long> ids = Lists.newArrayList();
        for (AdminWorkOrder workOrder : baseInfoUgcOrders) {
            ids.add(workOrder.getId());
            idOrderMapping.put(workOrder.getId(), workOrder);
        }

        List<AdminTaskUgc> baseInfoUgcTasks = selectByWorkOrderIds(ids);
        for (AdminTaskUgc taskUgc : baseInfoUgcTasks) {
            // 判断前置是否审核通过
            AdminTaskUgc firstApproveTask = adminTaskUgcDao.selectLatelyTaskByCaseIdAndContent(taskUgc.getCaseId(),
                    AdminUGCTask.Content.FIRST_APPROVE.getCode());
            if (firstApproveTask == null || firstApproveTask.getResult() == AdminUGCTask.Result.FIRST_APPROVE_PASS.getCode()) {
                firstApprovePass.add(idOrderMapping.get(taskUgc.getWorkOrderId()));
            }
        }

        return firstApprovePass;
    }

}
