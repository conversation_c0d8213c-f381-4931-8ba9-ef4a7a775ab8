package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoPayee;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Set;

/**
 * Created by Ahrievil on 2017/9/25
 */
public interface AdminCrowdfundingInfoPayeeBiz {
    List<CrowdfundingInfoPayee> selectByPayeeName(String name);
    List<CrowdfundingInfoPayee> selectByInfoUuidList(List<String> list);
    List<CrowdfundingInfoPayee> selectByPayeeIdCard(String idCard);

    Set<Integer> selectCaseIdsByPayeeIdCard(String idCard);

    List<Integer> selectCaseIdsByPayeeIdCardOrderByCreateTimeDesc(String idCard, int currentCaseId);

    int updatePayeeRelation(int caseId, int relationCode);
}
