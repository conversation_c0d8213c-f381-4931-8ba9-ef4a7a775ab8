package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.IAdminCredibleInfoService;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCredibleInfoDAO;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCredibleInfoWorkOrderDAO;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.model.crowdfunding.CfCredibleInfoDO;
import com.shuidihuzhu.cf.model.crowdfunding.CfCredibleInfoWorkOrderDO;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2020/1/10 下午2:38
 * @desc
 */
@Service
public class AdminCredibleInfoServiceImpl implements IAdminCredibleInfoService {
    @Autowired
    private AdminCredibleInfoDAO adminCredibleInfoDAO;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;
    @Resource
    private AdminCredibleInfoWorkOrderDAO adminCredibleInfoWorkOrderDAO;

    @Override
    public int insert(CfCredibleInfoDO credibleInfoDO) {
        if(Objects.isNull(credibleInfoDO)){
            return 0;
        }
        return adminCredibleInfoDAO.insert(credibleInfoDO);
    }

    @Override
    public int insertOne(int caseId, long userId, long subId, int type, int auditStatus, long operatorId) {
        UserInfoModel cfUserInfo = userInfoServiceBiz.getUserInfoByUserId(userId);
        String mobile = Objects.nonNull(cfUserInfo) ? cfUserInfo.getCryptoMobile() : "";

        CfCredibleInfoDO credibleInfoDO = new CfCredibleInfoDO();
        credibleInfoDO.setCaseId(caseId);
        credibleInfoDO.setSubId(subId);
        credibleInfoDO.setMobile(StringUtils.isNotEmpty(mobile) ? mobile : "");
        credibleInfoDO.setType(type);
        credibleInfoDO.setAuditStatus(auditStatus);
        credibleInfoDO.setOperatorId(operatorId);
        credibleInfoDO.setSendTime(new Date());
        return adminCredibleInfoDAO.insert(credibleInfoDO);
    }

    @Override
    public List<CfCredibleInfoDO>  queryByCaseId(int caseId) {
        if(caseId <= 0){
            return Lists.newArrayList();
        }

        return adminCredibleInfoDAO.queryByCaseId(caseId);
    }

    @Override
    public CfCredibleInfoDO queryBySubId(long id, int type) {
        return adminCredibleInfoDAO.queryBySubId(id, type);
    }

    @Override
    public CfCredibleInfoDO queryById(long id) {
        return adminCredibleInfoDAO.queryById(id);
    }

    @Override
    public int updateAuditInfo(long subId, int auditStatus, int type) {
        return adminCredibleInfoDAO.updateAuditInfo(subId, auditStatus, type);
    }

    @Override
    public int updateSubmitInfo(long subId, int auditStatus, int type) {
        return adminCredibleInfoDAO.updateSubmitInfo(subId, auditStatus, type);
    }

    @Override
    public int delete(long subId, int type) {
        return adminCredibleInfoDAO.delete(subId, type);
    }

    @Override
    public int updateAuditStatusById(long id, int auditStatus) {
        return adminCredibleInfoDAO.updateAuditStatusById(id, auditStatus);
    }

    @Override
    public CfCredibleInfoDO getLastOneByCaseId(int caseId, int type) {
        return adminCredibleInfoDAO.getLastOneByCaseId(caseId, type);
    }

    @Override
    public List<CfCredibleInfoDO> getListByOperatorId(long operatorId, int type) {
        return adminCredibleInfoDAO.getListByOperatorId(operatorId,type);
    }

    @Override
    public List<CfCredibleInfoWorkOrderDO> getByCaseId(int caseId) {
        return adminCredibleInfoWorkOrderDAO.queryByCaseId(caseId);
    }
}
