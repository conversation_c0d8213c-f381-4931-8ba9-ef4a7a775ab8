package com.shuidihuzhu.cf.biz.crowdfunding.report;

import com.shuidihuzhu.cf.model.report.CfReportOfficialLetter;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-05-21 14:58
 **/
public interface CfReportOfficialLetterBiz {

    int insertOne(CfReportOfficialLetter cfReportOfficialLetter);

    List<CfReportOfficialLetter> getByCaseId(int caseId);

    int update(CfReportOfficialLetter cfReportOfficialLetter);

    CfReportOfficialLetter getById(long id);

    List<CfReportOfficialLetter> getLastByCaseId(int caseId);
}
