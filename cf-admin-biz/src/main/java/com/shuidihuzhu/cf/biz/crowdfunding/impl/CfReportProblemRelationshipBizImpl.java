package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.CfReportProblemRelationshipBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfReportProblemRelationshipDao;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileSettings;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportProblem;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportProblemRelationship;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfReportProblemParam;
import com.shuidihuzhu.cf.service.cfOperatingProfile.CfOperatingProfileSettingsExtBiz;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2019-12-11 16:26
 **/
@Slf4j
@Service
public class CfReportProblemRelationshipBizImpl implements CfReportProblemRelationshipBiz {

    @Autowired
    private CfReportProblemRelationshipDao relationshipDao;
    @Autowired
    private CfOperatingProfileSettingsExtBiz settingsExtBiz;

    @Override
    public boolean insert(CfReportProblemRelationship relationship) {
        if (relationship == null || StringUtils.isBlank(relationship.getContent())) {
            return false;
        }
        String content = Optional.ofNullable(relationship.getContent()).orElse("");
        String nextProblem = Optional.ofNullable(relationship.getNextProblem()).orElse("");
        boolean hasExist = listRelationByProblemIds(Lists.newArrayList(relationship.getProblemId()))
                .stream()
                .anyMatch(item -> {
                    String itemContent = Optional.ofNullable(item.getContent()).orElse("");
                    return item.getProblemId() == relationship.getProblemId()
                            && item.getNextProblemId() == relationship.getNextProblemId()
                            && itemContent.equals(content);
                });
        log.info("label insert CfReportProblemRelationship:{} hasExist:{}", JSON.toJSONString(relationship), hasExist);
        if (hasExist) {
            return true;
        }
        relationship.setContent(content);
        relationship.setNextProblem(nextProblem);
        return relationshipDao.insert(relationship) == 1;
    }


    @Override
    public boolean bindRelationship(CfReportProblemParam.BindParam bindParam, CfReportProblem parentProblem, CfReportProblem childProblem) {
        //绑定前需要判断是否需要新增Relationship数据
        List<CfReportProblemRelationship> relationships = relationshipDao.obtainRelationProblemId(bindParam.getProblemId(), bindParam.getChoice());

        boolean hasRelation = relationships
                .stream()
                .filter(item -> item.getNextProblemId() > 0)
                .anyMatch(item -> Optional.ofNullable(item.getNextProblem()).orElse("").equals(childProblem.getProblem())
                        && item.getNextProblemId() == childProblem.getId());

        if (hasRelation) {
            log.info("已经绑定过问题了,bindParam:{},childProblem:{}", JSON.toJSONString(bindParam), JSON.toJSONString(childProblem));
            return true;
        }


        CfReportProblemRelationship relationship = new CfReportProblemRelationship();
        relationship.setContent(Optional.ofNullable(bindParam.getChoice()).orElse(""));
        relationship.setProblemId(bindParam.getProblemId());
        relationship.setProblem(parentProblem.getProblem());
        relationship.setNextProblemId(bindParam.getNextProblemId());
        relationship.setNextProblem(childProblem.getProblem());
        return relationshipDao.insert(relationship) == 1;
    }

    @Override
    public boolean deleteByProblemIdAndContent(int userId, int problemId, List<String> contents) {
        log.warn("解除绑定problemId:{}, contents:{}", problemId, JSON.toJSONString(contents));
        if (CollectionUtils.isNotEmpty(contents)) {
            List<Integer> relationIds = relationshipDao.selectRelationIdsByProblemId(problemId, contents);
            settingsExtBiz.unbindRelation(userId, CfOperatingProfileSettings.ProfileExtName.REMARK, relationIds);
            // 这里是用来解绑备注关系
            return relationshipDao.unBindRelationship(problemId, contents) >= 1;
        } else {
            log.warn("暂不支持此场景的解除绑定problemId:{}", problemId);
            return false;
        }
    }

    @Override
    public boolean deleteByProblemIdAndNextProblemId(int problemId, List<Integer> nextProblemIds) {
        if (CollectionUtils.isEmpty(nextProblemIds)) {
            return true;
        }
        return relationshipDao.deleteRelationship(problemId, nextProblemIds) >= 1;
    }

    @Override
    public boolean onlyChangeProblemName(int problemId, String newProblemName) {
        relationshipDao.changeProblemName(problemId, newProblemName);
        relationshipDao.changeNextProblemName(problemId, newProblemName);
        return true;
    }


    @Override
    public List<CfReportProblemRelationship> listRelationByProblemIds(List<Integer> problemIds) {
        if (CollectionUtils.isEmpty(problemIds)) {
            return Lists.newArrayList();
        }
        return relationshipDao.listByProblemIds(problemIds);
    }

    @Override
    public List<Integer> obtainRelationProblemId(int problemId, String content) {
        return relationshipDao.obtainRelationProblemId(problemId, content).stream().map(CfReportProblemRelationship::getNextProblemId).collect(Collectors.toList());
    }

    @Override
    public List<CfReportProblemRelationship> listByNextProblemIds(List<Integer> nextProblemIds) {
        if (CollectionUtils.isEmpty(nextProblemIds)) {
            return Lists.newArrayList();
        }
        return relationshipDao.listByNextProblemIds(nextProblemIds);
    }
}
