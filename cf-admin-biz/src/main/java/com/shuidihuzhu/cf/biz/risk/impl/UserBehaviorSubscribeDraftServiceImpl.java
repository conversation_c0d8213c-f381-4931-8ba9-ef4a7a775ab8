package com.shuidihuzhu.cf.biz.risk.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.risk.IUserBehaviorService;
import com.shuidihuzhu.cf.client.feign.CfCommonFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.enums.crowdfunding.UserBehaviorEnum;
import com.shuidihuzhu.cf.model.crowdfunding.AdminUserBehaviorDetail;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingBaseInfoBackup;
import com.shuidihuzhu.cf.model.crowdfunding.UserInfoDetail;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2019/10/11 下午5:30
 * @desc 登记草稿
 */
@Service
public class UserBehaviorSubscribeDraftServiceImpl implements IUserBehaviorService {

    @Autowired
    private CfCommonFeignClient cfCommonFeignClient;

    @Override
    public UserBehaviorEnum getBehavior() {
        return UserBehaviorEnum.REGISTER_DRAFT;
    }

    @Override
    public List<AdminUserBehaviorDetail> buildBehaviorDetail(String mobile, long userId, String name, String identity, UserInfoModel userInfo, UserBehaviorEnum behaviorEnum) {

        FeignResponse<List<CrowdfundingBaseInfoBackup>> backupRes = cfCommonFeignClient.queryBackupByUserId(userId);
        if(backupRes.notOk() || CollectionUtils.isEmpty(backupRes.getData())){
            return Lists.newArrayList();
        }

        UserInfoDetail userInfoDetail = new UserInfoDetail();
        userInfoDetail.setUserId(userId);
        userInfoDetail.setNickName(Objects.nonNull(userInfo) ? userInfo.getNickname() : "");

        List<AdminUserBehaviorDetail> backupDetails = Lists.newArrayList();

        List<CrowdfundingBaseInfoBackup> backups = backupRes.getData();
        for (CrowdfundingBaseInfoBackup backup : backups){

            String picUrl = backup.getPictureUrl();
            List<String> urls = StringUtils.isNotEmpty(picUrl) ? Lists.newArrayList(picUrl.split(",")) : Lists.newArrayList();

            StringBuilder sb = new StringBuilder();
            sb.append("草稿id:" + backup.getId()).append(REDEX);
            sb.append("草稿标题:" + backup.getTitle()).append(REDEX);
            sb.append("草稿内容:" + backup.getContent());

            AdminUserBehaviorDetail backupDetail = new AdminUserBehaviorDetail();
            backupDetail.setTime(backup.getCreateTime());
            backupDetail.setBehaviorType(UserBehaviorEnum.REGISTER_DRAFT.getKey());
            backupDetail.setBehavoir(Lists.newArrayList(sb.toString().split(REDEX)));
            backupDetail.setUrl(urls);
            backupDetail.setUserInfoDetail(userInfoDetail);

            backupDetails.add(backupDetail);

        }

        return backupDetails;
    }
}
