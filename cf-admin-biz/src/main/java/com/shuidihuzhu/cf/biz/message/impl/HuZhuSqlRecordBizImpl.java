package com.shuidihuzhu.cf.biz.message.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.message.HuZhuSqlRecordBiz;
import com.shuidihuzhu.cf.dao.stat.message.HuZhuSqlRecordDao;
import com.shuidihuzhu.cf.model.message.HuZhuSqlRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

@Slf4j
@Service
public class HuZhuSqlRecordBizImpl implements HuZhuSqlRecordBiz {
    @Autowired
    private HuZhuSqlRecordDao huZhuSqlRecordDao;

    @Override
    public int insertHuZhuSqlRecord(HuZhuSqlRecord huZhuSqlRecord) {
        try {
            return null == huZhuSqlRecord ? 0 : huZhuSqlRecordDao.insertHuZhuSqlRecord(huZhuSqlRecord);
        } catch (Exception e) {
            log.error("insertHuZhuSqlRecord", e);
        }
        return 0;
    }

    @Override
    public HuZhuSqlRecord queryHuZhuSqlRecordBySqlId(int sqlId) {
        try {
            if (sqlId <= 0) {
                return null;
            }
            return huZhuSqlRecordDao.queryHuZhuSqlRecordBySqlId(sqlId);
        } catch (Exception e) {
            log.error("queryHuZhuSqlRecordBySqlId", e);
        }
        return null;
    }

    @Override
    public int deleteHuZhuSqlRecord(int id) {
        try {
            huZhuSqlRecordDao.deleteHuZhuSqlRecord(id);
        } catch (Exception e) {
            log.error("deleteHuZhuSqlRecord", e);
        }
        return 0;
    }

    @Override
    public List<HuZhuSqlRecord> queryBySqlIdSet(Set<Integer> sqlIdSet) {
        if (CollectionUtils.isEmpty(sqlIdSet)) {
            return Lists.newArrayList();
        }
        return huZhuSqlRecordDao.queryBySqlIdSet(sqlIdSet);
    }
}
