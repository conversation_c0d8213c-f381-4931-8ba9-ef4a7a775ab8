package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonItem;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by ahrievil on 2017/1/17.
 */
public interface CfRefuseReasonItemBiz {
	List<CfRefuseReasonItem> getRefuseItem();

	CfRefuseReasonItem getContentById(int id);

    List<CfRefuseReasonItem> selectSubGroupByType();

    List<CfRefuseReasonItem> selectByIds(Set set);

    List<CfRefuseReasonItem> selectByType(@Param("type") int type);

    int insertReasonItem(CfRefuseReasonItem reasonItem);

    void fillReasonEntityItem(List<CfRefuseReasonEntity> entityList);

    Map<Integer, CfRefuseReasonItem> selectMappingByIds(List<Integer> ids);

    List<CfRefuseReasonItem> selectByContent(String  content);

}
