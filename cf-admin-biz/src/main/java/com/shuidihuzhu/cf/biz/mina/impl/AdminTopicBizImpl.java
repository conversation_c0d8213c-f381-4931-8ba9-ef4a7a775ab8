package com.shuidihuzhu.cf.biz.mina.impl;

import com.github.pagehelper.PageHelper;
import com.shuidihuzhu.cf.biz.mina.AdminTopicBiz;
import com.shuidihuzhu.cf.dao.mina.AdminTopicDao;
import com.shuidihuzhu.cf.model.miniprogram.CfTopic;
import com.shuidihuzhu.cf.vo.mina.CfMinaTopicDetailVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class AdminTopicBizImpl implements AdminTopicBiz {
    @Autowired
    private AdminTopicDao adminTopicDao;

    @Override
    public List<CfTopic> getByTitle(List<Integer> phaseIds, String title, int pageNum, int pageSize) {
        if (pageNum <= 0 || pageSize <= 0 || CollectionUtils.isEmpty(phaseIds)) {
            return Collections.EMPTY_LIST;
        }
        PageHelper.startPage(pageNum, pageSize);
        return adminTopicDao.getByTitle(phaseIds, StringUtils.trimToNull(title));
    }

    @Override
    public List<CfMinaTopicDetailVo> getTopicsPage(int pageNum, int pageSize, String title) {
        if (pageNum <= 0 || pageSize <= 0) {
            return Collections.EMPTY_LIST;
        }
        PageHelper.startPage(pageNum, pageSize);
        return adminTopicDao.getTopics(StringUtils.trimToNull(title));
    }

    @Override
    public List<CfMinaTopicDetailVo> listByPhaseIds(List<Integer> phaseIds) {
        if (CollectionUtils.isEmpty(phaseIds)) {
            return Collections.EMPTY_LIST;
        }
        return this.adminTopicDao.listByPhaseIds(phaseIds);
    }

    @Override
    public int updatePhaseId(int topicId, int phaseId) {
        if (topicId <= 0 || phaseId <= 0) {
            return 0;
        }
        return this.adminTopicDao.updatePhaseId(topicId, phaseId);
    }

    @Override
    public List<CfTopic> getPublishTopics(String title) {
        return adminTopicDao.getPublishTopics(StringUtils.trimToNull(title));
    }

    @Override
    public int deletePhaseById(List<Integer> topicIds) {
        if (CollectionUtils.isEmpty(topicIds)){
          return 0;
        }
        return adminTopicDao.deletePhaseById(topicIds);
    }

    @Override
    public List<CfMinaTopicDetailVo> getUnPublishTopics(int pageNum, int pageSize, String title) {
        if (pageNum <= 0 || pageSize <= 0) {
            return Collections.EMPTY_LIST;
        }
        PageHelper.startPage(pageNum, pageSize);
        return adminTopicDao.getUnPublishTopics(title);
    }

    @Override
    public int insertOne(CfTopic cfTopic) {
        if (cfTopic == null) {
            return 0;
        }
        return adminTopicDao.addOne(cfTopic);
    }

    @Override
    public int deleteByPhaseId(int phaseId) {
        return adminTopicDao.deleteByPhaseId(phaseId);
    }
}
