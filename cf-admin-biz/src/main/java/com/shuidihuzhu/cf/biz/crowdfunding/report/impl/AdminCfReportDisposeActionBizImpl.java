package com.shuidihuzhu.cf.biz.crowdfunding.report.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.report.AdminCfReportDisposeActionBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfReportDisposeActionDao;
import com.shuidihuzhu.cf.model.report.CfReportDisposeAction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/4/17
 */
@Service
public class AdminCfReportDisposeActionBizImpl implements AdminCfReportDisposeActionBiz {
    @Autowired
    private AdminCfReportDisposeActionDao adminCfReportDisposeActionDao;


    @Override
    public int add(CfReportDisposeAction cfReportDisposeAction) {
        if (cfReportDisposeAction.getActionClassifyId() < 0) {
            return 0;
        }
        return adminCfReportDisposeActionDao.add(cfReportDisposeAction);
    }

    @Override
    public int updateDisposeAction(String disposeAction, long actionClassifyId, long id, boolean isHelp, boolean hasTemplate) {
        if (actionClassifyId < 0 || id < 0) {
            return 0;
        }
        return adminCfReportDisposeActionDao.updateDisponseAction(disposeAction, actionClassifyId, id, isHelp, hasTemplate);
    }

    @Override
    public int updateIsUse(int isUse, long id) {
        if (isUse < 0 || id < 0) {
            return 0;
        }
        return adminCfReportDisposeActionDao.updateIsUse(isUse, id);
    }

    @Override
    public List<CfReportDisposeAction> getAll() {
        return adminCfReportDisposeActionDao.getAll();
    }

    @Override
    public List<CfReportDisposeAction> getByActionClassifyId(long actionClassifyId) {
        if (actionClassifyId < 0){
            return Lists.newArrayList();
        }
        return adminCfReportDisposeActionDao.getByActionClassifyId(actionClassifyId);
    }

    @Override
    public List<CfReportDisposeAction> getByUse(int isUse) {
        return adminCfReportDisposeActionDao.getByUse(isUse);
    }

    @Override
    public List<CfReportDisposeAction> selectByIds(List<Long> ids) {
        return adminCfReportDisposeActionDao.selectByIds(ids);
    }

    @Override
    public CfReportDisposeAction getById(Long id) {
        return adminCfReportDisposeActionDao.getById(id);
    }
}
