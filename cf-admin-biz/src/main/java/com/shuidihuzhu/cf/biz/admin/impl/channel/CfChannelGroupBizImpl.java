package com.shuidihuzhu.cf.biz.admin.impl.channel;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.admin.channel.CfChannelGroupBiz;
import com.shuidihuzhu.cf.dao.sd.admin.channel.CfChannelGroupDao;
import com.shuidihuzhu.cf.model.admin.channel.CfChannelGroup;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Created by wangsf on 17/2/16.
 */
@Service
public class CfChannelGroupBizImpl implements CfChannelGroupBiz {

	@Autowired
	private CfChannelGroupDao cfChannelGroupDao;

	@Override
	public List<CfChannelGroup> listGroups(int anchorId, int limit) {
		if(anchorId < 0) {
			anchorId = 0;
		}

		if(limit <= 0) {
			return Collections.EMPTY_LIST;
		}

		return this.cfChannelGroupDao.listGroups(anchorId, limit);
	}

	@Override
	public List<CfChannelGroup> listGroupsByPage(int current, int pageSize) {
		PageHelper.startPage(current, pageSize);
		return this.cfChannelGroupDao.listGroupsByPage();

	}

	@Override
	public List<CfChannelGroup> listByIds(List<Integer> ids) {
		if(CollectionUtils.isEmpty(ids)) {
			return Collections.EMPTY_LIST;
		}

		return this.cfChannelGroupDao.listByIds(ids);
	}

	@Override
	public Map<Integer, CfChannelGroup> getMapByIds(List<Integer> ids) {
		List<CfChannelGroup> channelGroups = this.listByIds(ids);
		if(CollectionUtils.isEmpty(channelGroups)) {
			return Maps.newHashMap();
		}

		Map<Integer, CfChannelGroup> map = Maps.newHashMap();
		for(CfChannelGroup channelGroup : channelGroups) {
			map.put(channelGroup.getId(), channelGroup);
		}
		return map;
	}

	@Override
	public CfChannelGroup getById(int groupId) {
//		return null;
		if(groupId <= 0) {
			return null;
		}

		return this.cfChannelGroupDao.getById(groupId);
	}

    @Override
    public int insertOne(CfChannelGroup cfChannelGroup) {
        return cfChannelGroupDao.insertOne(cfChannelGroup);
    }
}
