package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.cache.*;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.CfDiseaseClassifyBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfDiseaseClassifyDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfDiseaseClassifyDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2019-11-12 16:42
 **/
@Service
@Slf4j
public class CfDiseaseClassifyBizImpl implements CfDiseaseClassifyBiz {

    @Autowired
    CfDiseaseClassifyDao cfDiseaseClassifyDao;

    private LoadingCache<Long, CfDiseaseClassifyDO> classifyDOLoadingCache = CacheBuilder
            .newBuilder()
            .build(new CacheLoader<Long, CfDiseaseClassifyDO>() {
                @Override
                public CfDiseaseClassifyDO load(Long key) throws Exception {
                    return null;
                }
            });


    @Override
    public boolean add(CfDiseaseClassifyDO classifyDO) {
        int affectRows = cfDiseaseClassifyDao.insert(classifyDO);
        classifyDOLoadingCache.asMap().clear();
        return affectRows > 0;
    }

    @Override
    public List<CfDiseaseClassifyDO> listAll() {
        List<CfDiseaseClassifyDO> cfDiseaseClassifyDOS = Lists.newArrayList(classifyDOLoadingCache.asMap().values());
        if (CollectionUtils.isEmpty(cfDiseaseClassifyDOS)) {
            Map<Long, CfDiseaseClassifyDO> map = listAllFromDB()
                    .stream()
                    .collect(Collectors.toMap(CfDiseaseClassifyDO::getId, Function.identity(), (before, after) -> before));
            classifyDOLoadingCache.asMap().putAll(map);
            cfDiseaseClassifyDOS = Lists.newArrayList(map.values());
        }
        return cfDiseaseClassifyDOS;
    }


    private List<CfDiseaseClassifyDO> listAllFromDB() {
        log.info("diseaseClassify info find in db");
        boolean hasNext = true;
        long id = 0;
        int pageSize = 500;
        List<CfDiseaseClassifyDO> classifyDOList = Lists.newArrayList();
        while (hasNext) {
            List<CfDiseaseClassifyDO> classifyDOS = cfDiseaseClassifyDao.listAllByPage(id, pageSize);
            classifyDOList.addAll(classifyDOS);
            int size = classifyDOList.size();
            hasNext = (size == pageSize);
            id = classifyDOS.stream().mapToLong(CfDiseaseClassifyDO::getId).max().orElse(0);
        }
        return classifyDOList;
    }
}
