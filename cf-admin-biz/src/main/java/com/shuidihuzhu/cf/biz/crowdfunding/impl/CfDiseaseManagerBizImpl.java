package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.github.pagehelper.PageHelper;
import com.shuidihuzhu.cf.biz.crowdfunding.CfDiseaseManagerBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfDiseaseManagerDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfDiseaseManagerDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2019-11-08 16:29
 **/
@Service
@Slf4j
public class CfDiseaseManagerBizImpl implements CfDiseaseManagerBiz {

    @Autowired
    CfDiseaseManagerDao cfDiseaseManagerDao;

    @Override
    public boolean add(CfDiseaseManagerDO cfDiseaseManagerDO) {
        return cfDiseaseManagerDao.insert(cfDiseaseManagerDO) == 1;
    }

    @Override
    public boolean edit(CfDiseaseManagerDO cfDiseaseManagerDO) {
        if (cfDiseaseManagerDO.getId() <= 0) {
            log.warn("edit but no id:{}", cfDiseaseManagerDO);
            return false;
        }
        return cfDiseaseManagerDao.edit(cfDiseaseManagerDO) == 1;
    }

    @Override
    public boolean delete(long diseaseManagerId) {
        if (diseaseManagerId <= 0) {
            log.warn("edit but no id:{}", diseaseManagerId);
            return false;
        }
        return cfDiseaseManagerDao.delete(diseaseManagerId) >= 1;
    }

    @Override
    public List<CfDiseaseManagerDO> listForAdminSearch(CfDiseaseManagerDO cfDiseaseManagerDO, int current, int pageSize) {
        PageHelper.startPage(current, pageSize);
        return cfDiseaseManagerDao.listForSeaAdmin(cfDiseaseManagerDO);
    }

    @Override
    public CfDiseaseManagerDO getById(long diseaseManagerId) {
        return cfDiseaseManagerDao.getById(diseaseManagerId);
    }
}
