package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonMsg;

import java.sql.Timestamp;
import java.util.List;
import java.util.Set;

/**
 * Created by Ahrievil on 2017/7/31
 */
public interface AdminCfRefuseReasonMsgBiz {
    int insertOne(CfRefuseReasonMsg cfRefuseReasonMag);
    CfRefuseReasonMsg selectByInfoUuidAndType(String infoUuid, int type);
    CfRefuseReasonMsg selectByInfoIdAndType(String infoUuid, int type);
    int insertList(List<CfRefuseReasonMsg> list);
    List<String> selectWithTimeLimit(Timestamp begin, Timestamp end, int start, int size);
    int deleteByInfoUuid(String infoUuid);
    int deleteByInfoUuidAndTypes(String infoUuid, Set<Integer> set);
    List<CfRefuseReasonMsg> selectByInfoUuid(String infoUuid);
    List<CfRefuseReasonMsg> selectSimpleFieldsByTimeLimit(Timestamp begin, Timestamp end, int start, int size);
}
