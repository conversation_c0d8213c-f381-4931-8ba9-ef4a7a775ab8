package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfRiskLabelBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfRiskLabelDao;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Auther: subing
 * @Date: 2020/3/13
 */
@Service
public class AdminCfRiskLabelBizImpl implements AdminCfRiskLabelBiz {
    @Autowired
    private AdminCfRiskLabelDao adminCfRiskLabelDao;

    @Override
    public int add(int activityId, String riskLabel) {
        if (activityId <= 0) {
            return 0;
        }
        return adminCfRiskLabelDao.add(activityId, StringUtils.trimToEmpty(riskLabel));
    }

    @Override
    public String getByActivityId(int activityId) {
        if (activityId <= 0) {
            return null;
        }
        return adminCfRiskLabelDao.getByActivityId(activityId);
    }

    @Override
    public int updateRiskLabel(int activityId, String riskLabel) {
        if (activityId <= 0) {
            return 0;
        }
        return adminCfRiskLabelDao.updateRiskLabel(activityId, riskLabel);
    }
}
