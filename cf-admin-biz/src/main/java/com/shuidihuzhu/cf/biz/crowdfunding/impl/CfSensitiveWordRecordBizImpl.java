package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderReportBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfSensitiveWordRecordBiz;
import com.shuidihuzhu.cf.client.feign.CfUserInfoFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.constants.crowdfunding.status.RelationShip;
import com.shuidihuzhu.cf.dao.crowdfunding.CfSensitiveWordRecordDao;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.CfSensitiveWordRecordEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdFundingProgressType;
import com.shuidihuzhu.cf.enums.risk.UgcTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.deliver.CrowdFundingVerificationDeliver;
import com.shuidihuzhu.cf.model.crowdfunding.deliver.CrowdfundingCommentDeliver;
import com.shuidihuzhu.cf.model.crowdfunding.deliver.CrowdfundingOrderDeliver;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfSensitiveWordRecordVo;
import com.shuidihuzhu.cf.model.risk.verify.RiskUgcVerifyModel;
import com.shuidihuzhu.cf.mq.IAdminCommonMessageHelperService;
import com.shuidihuzhu.cf.service.crowdfunding.CfSensitiveWordService;
import com.shuidihuzhu.cf.service.risk.dark.DarkListService;
import com.shuidihuzhu.cf.service.sensitive.adapter.SensitiveCommentAdapter;
import com.shuidihuzhu.cf.service.sensitive.adapter.SensitiveOrderAdapter;
import com.shuidihuzhu.cf.service.sensitive.processor.SensitiveProcessService;
import com.shuidihuzhu.cf.service.sensitive.adapter.SensitiveVerificationAdapter;
import com.shuidihuzhu.client.cf.workorder.CfUgcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.UgcWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * @author: lixuan
 * @date: 2017/8/9 10:52
 */
@Service
@Slf4j
public class CfSensitiveWordRecordBizImpl implements CfSensitiveWordRecordBiz {

    @Autowired
    private CfSensitiveWordRecordDao cfSensitiveWordRecordDao;

    @Resource
    private IRiskDelegate riskDelegate;

    @Resource
    private AdminWorkOrderReportBiz adminWorkOrderReportBiz;

    @Resource
    private IAdminCommonMessageHelperService adminCommonMessageHelperService;

    @Resource
    private SensitiveProcessService sensitiveProcessService;

    @Resource
    private SensitiveCommentAdapter sensitiveCommentAdapter;

    @Resource
    private SensitiveVerificationAdapter sensitiveVerificationAdapter;

    @Resource
    private SensitiveOrderAdapter sensitiveOrderAdapter;

    @Resource
    private CfSensitiveWordService cfSensitiveWordService;

    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Resource
    private CfUgcWorkOrderClient ugcWorkOrderClient;

    @Resource
    private CfUserInfoFeignClient cfUserInfoFeignClient;

    @Autowired
    private DarkListService darkListService;

    private static final int ZERO = 0;

    private static final int YES = 1;

    @Override
    public List<CfSensitiveWordRecord> selectByUserId(Set<Long> set) {
        if (CollectionUtils.isEmpty(set)) {
            return Lists.newArrayList();
        }
        return cfSensitiveWordRecordDao.selectByUserId(set);
    }

    @Override
    public List<CfSensitiveWordRecord> selectByIds(List<Long> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return cfSensitiveWordRecordDao.selectByIds(list);
    }

    @Override
    public CfSensitiveWordRecord selectById(long id) {
        return cfSensitiveWordRecordDao.selectById(id);
    }

    @Override
    public List<Long> selectIdByTime(Timestamp beginTime, Timestamp endTime, int start, int size) {
        return cfSensitiveWordRecordDao.selectIdByTime(beginTime, endTime, start, size);
    }

    /**
     * 检查评论
     * 1. 判断时候命中禁止词
     * 2. 判断是否命中敏感词
     *
     * @param comment
     */
    @Override
    public void buildCommentOne(CrowdfundingCommentDeliver comment) {
        sensitiveProcessService.process(comment, sensitiveCommentAdapter);
    }

    /**
     * 检查证实
     *
     * @param verification
     * @param fix
     * TODO DELETE 修复逻辑后续删除
     */
    @Override
    public void buildVerificationOne(CrowdFundingVerificationDeliver verification, boolean fix) {


        // 证实人不是线下BD
        if (ZERO == verification.getValid() || RelationShip.HEALTH_CARE.getCode() != verification.getRelationShip()) {
            sensitiveProcessService.process(verification, sensitiveVerificationAdapter);
            return;
        }

        FeignResponse<Boolean> feignResponse = cfUserInfoFeignClient.isMedical(verification.getVerifyUserId());
        if (Objects.isNull(feignResponse.getData())) {
            log.info("cfUserInfoFeignClient request isMedical reponse is null");
            return;
        }
        // 是否为医护人员
        boolean isMedical = feignResponse.getData();
        // 查询判断是否已为认证医护人员
        if (isMedical) {
            if (fix) {
                log.info("修复证实 跳过 {}", verification);
                return;
            }
            UgcWorkOrder workOrder = buildUgcOrderAndSaveSensitiveRecord(verification);
            Response<Long> response =  ugcWorkOrderClient.createUgc(workOrder);
            log.info("buildVerificationOne workOrder={},response={}",workOrder, JSON.toJSONString(response));
            return;
        }

        // 如果省份名称 医院名称都不为空 则生成工单
        if (Objects.nonNull(verification.getProvinceCode()) && StringUtils.isNotEmpty(verification.getHospitalName())) {
            if (fix) {
                log.info("修复证实 跳过 {}", verification);
                return;
            }
            UgcWorkOrder workOrder = buildUgcOrderAndSaveSensitiveRecord(verification);
            Response<Long> response =  ugcWorkOrderClient.createUgc(workOrder);
            log.info("buildVerificationOne workOrder={},response={}",workOrder, JSON.toJSONString(response));
            return;
        }

        sensitiveProcessService.process(verification, sensitiveVerificationAdapter);
    }

    private UgcWorkOrder buildUgcOrderAndSaveSensitiveRecord(CrowdFundingVerificationDeliver verification) {
        UgcWorkOrder workOrder = buildWorkOrder(verification);
        CfSensitiveWordRecordVo record = saveSensitiveRecord(verification);
        workOrder.setWordId(record.getId() + "");
        return workOrder;
    }

    private CfSensitiveWordRecordVo saveSensitiveRecord(CrowdFundingVerificationDeliver verification) {
        CfSensitiveWordRecordVo record = sensitiveVerificationAdapter.buildRecord(verification, StringUtils.EMPTY);
        boolean isVerification = sensitiveVerificationAdapter.getUgcTypeEnum() == UgcTypeEnum.VERIFICATION;
        blackListFilter(record, isVerification);
        saveBatch(Lists.newArrayList(record));
        return record;
    }


    private UgcWorkOrder buildWorkOrder(CrowdFundingVerificationDeliver verification) {
        UgcWorkOrder workOrder = new UgcWorkOrder();
        workOrder.setVerificationId(verification.getId().toString());
        workOrder.setMedicalWorkType(String.valueOf(YES));
        workOrder.setCaseId(buildCaseId(verification));
        workOrder.setOrderType(WorkOrderType.ugcpinglun.getType());
        workOrder.setContentType(String.valueOf(CfSensitiveWordRecordEnum.BizType.VERIFICATION.value()));
        return workOrder;
    }

    private int buildCaseId(CrowdFundingVerificationDeliver verification) {
        int caseId = 0;
        String infoUuid = verification.getCrowdFundingInfoId();
        CrowdfundingInfo fundingInfo = adminCrowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (fundingInfo != null) {
             caseId = fundingInfo.getId();
        } else {
            String crowdFundingInfoId = verification.getCrowdFundingInfoId();
            CfInfoSimpleModel cfInfoSimpleModel = crowdfundingDelegate.getCfInfoSimpleModel(crowdFundingInfoId);
             caseId = cfInfoSimpleModel.getId();
        }
        return caseId;
    }

    /**
     * 检查订单
     *
     * @param order
     */
    @Override
    public void buildOneOrder(CrowdfundingOrderDeliver order) {
        sensitiveProcessService.process(order, sensitiveOrderAdapter);
    }

    /**
     * 检查动态是否有有效举报 若有 执行先审后发规则
     *
     * @param progress
     */
    @Override
    public void buildProgressOne(CrowdFundingProgress progress) {
        if (progress == null) {
            return;
        }
        Integer type = progress.getType();
        if (type != CrowdFundingProgressType.PROGRESS.value() && type != CrowdFundingProgressType.PATIENT_PROGRESS.value()) {
            adminCommonMessageHelperService.send(adminCommonMessageHelperService.getCfPublishProgressNoticeMessage(progress));
            return;
        }

        // 生成动态工单
        cfSensitiveWordService.addProcessToWorkOrder(progress);

        // 检查是否需要先审后发
        Integer caseId = progress.getActivityId();
        boolean safe = adminWorkOrderReportBiz.isSafe(caseId);
        if (safe) {
            // 发送动态进展通知
            adminCommonMessageHelperService.send(adminCommonMessageHelperService.getCfPublishProgressNoticeMessage(progress));
            riskDelegate.saveCache(UgcTypeEnum.PROGRESS, progress.getId(), caseId, true);
            return;
        }
        RiskUgcVerifyModel v = new RiskUgcVerifyModel(caseId, UgcTypeEnum.PROGRESS, progress.getId(),
                "举报状态案例动态先审后发");
        riskDelegate.addVerify(v);
    }

    /**
     * 是否在黑名单，如果在就设置mode & contentValid
     *
     * @param recordVo
     * @param isVerification
     */
    private void blackListFilter(CfSensitiveWordRecordVo recordVo, boolean isVerification) {
        long userId = recordVo.getUserId();
        boolean passed = darkListService.checkUGCPassed(userId, isVerification);
        if (passed) {
            return;
        }
        recordVo.setMode(1);
        recordVo.setContentValid(0);
    }

    /**
     * 保存命中记录
     *
     * @param cfSensitiveWordRecordVos
     * @return
     */
    private int saveBatch(List<CfSensitiveWordRecordVo> cfSensitiveWordRecordVos) {
        if (CollectionUtils.isEmpty(cfSensitiveWordRecordVos)) {
            return 0;
        }
        return cfSensitiveWordRecordDao.saveBatch(cfSensitiveWordRecordVos);
    }

    @Override
    public Long selectByBizIdAndBizType(long bizId, int bizType) {
        return cfSensitiveWordRecordDao.selectByBizIdAndBizType(bizId, bizType);
    }

    @Override
    public List<CfSensitiveWordRecord> listByBizIdAndBizType(long bizId, CfSensitiveWordRecordEnum.BizType bizType){
        if (0 >= bizId){
            return Lists.newArrayList();
        }
        return cfSensitiveWordRecordDao.listByBizIdAndBizType(bizId,bizType.value());
    }

}
