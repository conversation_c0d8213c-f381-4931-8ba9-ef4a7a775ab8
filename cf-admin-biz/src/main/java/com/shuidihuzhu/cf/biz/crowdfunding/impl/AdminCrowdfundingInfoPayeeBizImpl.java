package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoPayeeBiz;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingInfoPayeeDao;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingInfoSlaveDao;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoPayee;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by Ahrievil on 2017/9/25
 */
@Service
@Slf4j
public class AdminCrowdfundingInfoPayeeBizImpl implements AdminCrowdfundingInfoPayeeBiz {

    @Autowired
    private AdminCrowdfundingInfoPayeeDao crowdfundingInfoPayeeDao;
    @Autowired
    private AdminCrowdfundingInfoBiz cfInfoBiz;
    @Resource
    private AdminCrowdfundingInfoSlaveDao crowdfundingInfoSlaveDao;
    @Autowired
    private CrowdfundingFeignClient cfFeignClient;
    @Override
    public List<CrowdfundingInfoPayee> selectByPayeeName(String name) {
        if (StringUtils.isBlank(name)) return Lists.newArrayList();
        return crowdfundingInfoPayeeDao.selectByPayeeName(name);
    }

    @Override
    public List<CrowdfundingInfoPayee> selectByInfoUuidList(List<String> list) {
        if (CollectionUtils.isEmpty(list)) return Lists.newArrayList();
        return crowdfundingInfoPayeeDao.selectByInfoUuidList(list);
    }

    @Override
    public List<CrowdfundingInfoPayee> selectByPayeeIdCard(String idCard) {
        if (StringUtils.isBlank(idCard)) return Lists.newArrayList();
        return crowdfundingInfoPayeeDao.selectByPayeeIdCard(idCard);
    }

    @Override
    public Set<Integer> selectCaseIdsByPayeeIdCard(String payeeIdCard) {
        if (StringUtils.isEmpty(payeeIdCard)) {
            return Sets.newHashSet();
        }
        List<String> infoIds = crowdfundingInfoPayeeDao.selectByPayeeIdCard(payeeIdCard).stream()
                .map(CrowdfundingInfoPayee::getInfoUuid).collect(Collectors.toList());

        List<CrowdfundingInfo> allInfos = cfInfoBiz.getListByInfoUuIds(infoIds);
        Set<Integer> caseIds = Sets.newHashSet();
        for (CrowdfundingInfo info : allInfos) {
            // 需要是个人收款账号
            if (info.getRelationType() != CrowdfundingRelationType.charitable_organization
                    && info.getRelationType() != CrowdfundingRelationType.LOCATION_HOSPITAL_ACCOUNT) {
                caseIds.add(info.getId());
            }
        }
        return caseIds;
    }

    @Override
    public List<Integer> selectCaseIdsByPayeeIdCardOrderByCreateTimeDesc(String payeeIdCard, int currentCaseId) {
        if (StringUtils.isEmpty(payeeIdCard)) {
            return Lists.newArrayList();
        }

        List<CrowdfundingInfoPayee> crowdfundingInfoPayees = crowdfundingInfoPayeeDao.selectByPayeeIdCard(payeeIdCard);
        if (crowdfundingInfoPayees == null) {
            return Lists.newArrayList();
        }

        List<String> infoIds = crowdfundingInfoPayees.stream().map(CrowdfundingInfoPayee::getInfoUuid).collect(Collectors.toList());

        List<Integer> caseIds = new ArrayList<>();

        List<CrowdfundingInfo> allInfos = crowdfundingInfoSlaveDao.getFundingInfoByInfoIdsOrderById(infoIds);
        if (CollectionUtils.isEmpty(allInfos)) {
            return caseIds;
        }

        for (CrowdfundingInfo info : allInfos) {
            // 需要是个人收款账号，并且不能是当前的案例
            if (info.getRelationType() != CrowdfundingRelationType.charitable_organization
                    && info.getRelationType() != CrowdfundingRelationType.LOCATION_HOSPITAL_ACCOUNT && info.getId() != currentCaseId) {
                caseIds.add(info.getId());
            }
            // 取到九个就可以退出了
            if (caseIds.size() >= 9) {
                break;
            }
        }

        return caseIds;
    }

    @Override
    public int updatePayeeRelation(int caseId, int relationCode) {
        CrowdfundingRelationType relationType = CrowdfundingRelationType.getByCode(relationCode);
        FeignResponse<Integer> result = cfFeignClient.updatePayeeRelation(caseId, relationType);
        log.info("更新收款人关系.caseId:{} relationType:{} result:{}", caseId, relationType, result);
        return result == null || result.getData() == null ? 0 : result.getData();
    }

}
