package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingOrderBiz;
import com.shuidihuzhu.cf.client.feign.CfCommonFeignClient;
import com.shuidihuzhu.cf.client.feign.CrowdfundingOrderFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingUserDelegate;
import com.shuidihuzhu.cf.delegate.order.CfOrderTidbFeignClientDelegate;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.diff.Diff;
import com.shuidihuzhu.cf.domain.risk.RiskUgcVerifyDO;
import com.shuidihuzhu.cf.enums.crowdfunding.CfPayOrigin;
import com.shuidihuzhu.cf.enums.crowdfunding.CfSimpleTrueOrFalseEnum;
import com.shuidihuzhu.cf.enums.risk.UgcTypeEnum;
import com.shuidihuzhu.cf.model.contribute.CfContributeOrder;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrderModelResult;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingUser;
import com.shuidihuzhu.cf.param.CaseOrderQueryParam;
import com.shuidihuzhu.cf.service.crowdfunding.CfRiskService;
import com.shuidihuzhu.cf.vo.CrowdfundingUserVo;
import com.shuidihuzhu.cf.vo.crowdfunding.CfOrderVo;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.risk.model.enums.CfRiskBlackListEnum;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.MoneyUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by lgj on 16/6/21.
 */
@Service
@Slf4j
@RefreshScope
public class AdminCrowdfundingOrderBizImpl implements AdminCrowdfundingOrderBiz {
	@Autowired
	private CrowdfundingOrderFeignClient crowdfundingOrderFeignClient;
	@Autowired
	private ICrowdfundingUserDelegate crowdfundingUserDelegate;
    @Autowired
    private CfCommonFeignClient cfCommonFeignClient;
    @Autowired
    private CfRiskService cfRiskService;
    @Autowired
    private CfOrderTidbFeignClientDelegate cfOrderTidbFeignClientDelegate;
    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Override
    public List<CrowdfundingOrder> getSuccessByPage(Long id,
                                                    int crowdfundingId,
                                                    Integer amount,
                                                    String comment,
                                                    String ctimeStart,
                                                    String ctimeEnd,
                                                    int pageNum,
                                                    int pageSize,
                                                    String mobile,
                                                    int amountStart,
                                                    int amountEnd) {

        CaseOrderQueryParam queryParam = new CaseOrderQueryParam();
        queryParam.setId(id);
        queryParam.setCrowdfundingId(crowdfundingId);
        if (amount != null) {
            queryParam.setAmount(amount * 100);
        }
        if (amountStart >= 0 && amountEnd >= 0) {
            amountStart = amountStart * 100;
            amountEnd = amountEnd * 100;
        } else {
            amountStart = -1;
            amountEnd = -1;
        }
        queryParam.setAmountStart(amountStart);
        queryParam.setAmountEnd(amountEnd);
        queryParam.setComment(comment);
        queryParam.setCtimeStart(StringUtils.isNotEmpty(ctimeStart) ? DateUtil.getDateFromLongString(ctimeStart) : null);
        queryParam.setCtimeEnd(StringUtils.isNotEmpty(ctimeEnd) ? DateUtil.getDateFromLongString(ctimeEnd) : null);
        log.info("ugc查询订单参数: queryParam: {}", JSON.toJSONString(queryParam));
        if (StringUtils.isNotBlank(mobile)) {
            UserInfoModel infoModel = userInfoServiceBiz.getUserInfoByMobile(mobile);
            queryParam.setUserId(infoModel != null ? infoModel.getUserId() : null);
        }

        queryParam.setPageNum(pageNum);
        queryParam.setPageSize(pageSize);

        return this.getSuccessByPageFeign(queryParam);
    }
    @Override
    public List<CfOrderVo> getCrowdfundingCommentVo(List<CrowdfundingOrder> crowdfundingOrder, List<CfContributeOrder> contributeOrders,
                                                    Map<Long, CrowdfundingPayRecord> recordMappings) {
        List<CrowdfundingUser> crowdfundingUsers = Lists.newArrayList();
        List<Long> userIds = Lists.newArrayList();
        Map<String, CfContributeOrder> cfContributeOrderMap = contributeOrders.stream().collect(Collectors.toMap(CfContributeOrder::getContributeSourceId, Function.identity(), (a, b) -> b));
        for (CrowdfundingOrder order : crowdfundingOrder) {
//			order.setAmount(order.getAmount() / 100);
            CrowdfundingUser crowdfundingUser = new CrowdfundingUser();
            crowdfundingUser.setUserId(order.getUserId());
            crowdfundingUser.setUserThirdId(order.getUserThirdId());
            crowdfundingUsers.add(crowdfundingUser);
            userIds.add(order.getUserId());
        }
        CrowdfundingUserVo userInfo = crowdfundingUserDelegate.getUserInfo(crowdfundingUsers);
        Map<Long, CrowdfundingUser> userIdMap = Maps.newHashMap();
        if (userInfo != null) {
            userIdMap = userInfo.getUserIdMap();
        }
        Map<Long, Mobile> maskMobileMappings = getMaskMobiles(userIds);
        List<CfOrderVo> crowdfundingOrderVos = Lists.newArrayList();
        for (CrowdfundingOrder order : crowdfundingOrder) {

            RiskUgcVerifyDO verifyDO = cfCommonFeignClient.queryUgcVerify(order.getCrowdfundingId(), UgcTypeEnum.ORDER.getValue(), order.getId()).getData();
            boolean isBlackList = cfRiskService.queryBlackValid(order.getUserId(), CfRiskBlackListEnum.LimitType.UGC);
            boolean seeOnlySelf = Objects.nonNull(verifyDO) || isBlackList;

            CfOrderVo orderVo = new CfOrderVo();
            BeanUtils.copyProperties(order, orderVo);
            //将上面的代码移到这地方
            orderVo.setAmountInDouble(Double.parseDouble(MoneyUtil.buildBalanceV2(order.getAmount())));
            order.setAmount(order.getAmount() / 100);
            orderVo.setCrowdfundingUser(userIdMap.get(order.getUserId()));
            orderVo.setStatus(seeOnlySelf ? 1 : 0);
            Mobile mobile = Optional.ofNullable(maskMobileMappings.get(order.getUserId())).orElse(new Mobile());
            orderVo.setMobile(mobile.getMark());
            orderVo.setOriginMobile(mobile.getOrigin());
            CfContributeOrder cfContributeOrder = cfContributeOrderMap.get(String.valueOf(order.getId()));
            if (Objects.nonNull(cfContributeOrder)) {
                orderVo.setContributeAmount(MoneyUtil.buildBalance(cfContributeOrder.getRealPayAmount()));
            }
            CrowdfundingPayRecord payRecord = recordMappings.get(order.getId());
            if (Objects.isNull(payRecord) || StringUtils.isBlank(payRecord.getPayPlatform())
                    || !CfPayOrigin.isAliCode(payRecord.getPayPlatform())) {
                orderVo.setPayModeDesc("微信支付");
            } else {
                orderVo.setPayModeDesc("支付宝支付");
            }
            crowdfundingOrderVos.add(orderVo);
        }
        return crowdfundingOrderVos;
    }

    @Data
    private static class Mobile {
        private String origin;
        private String mark;
    }

    private Map<Long, Mobile> getMaskMobiles(List<Long> userIds) {
        Map<Long, Mobile> idTMobiles = Maps.newHashMap();
        List<UserInfoModel> infoModelList = userInfoServiceBiz.getUserInfoByUserIdBatch(userIds);

        for (UserInfoModel infoModel : infoModelList) {
            if (infoModel != null && StringUtils.isNotBlank(infoModel.getCryptoMobile())) {
                Mobile mobile = new Mobile();

                mobile.setOrigin(shuidiCipher.decrypt(infoModel.getCryptoMobile()));
                mobile.setMark(mobile.getOrigin().substring(0, 4) + "****" +
                        mobile.getOrigin().substring(8));
                idTMobiles.put(infoModel.getUserId(), mobile);
            }
        }

        return idTMobiles;
    }

    @Override
    public int updateValid(int valid, long id) {
    FeignResponse<Integer> feignResponse = FeignResponse.fallback();
        try {
            feignResponse = crowdfundingOrderFeignClient.updateValid(valid, id);
        }catch (Exception e){
            log.error("crowdfundingOrderFeignClient.updateValid err:",e);
        }
        return feignResponse.ok()?feignResponse.getData():0;
    }

    @Override
    public int editByIdAndComment(String comment, Long id) {
        FeignResponse<Integer> feignResponse = FeignResponse.fallback();
        try{
            feignResponse = crowdfundingOrderFeignClient.editByIdAndComment(comment, id);
        }catch (Exception e){
            log.error("crowdfundingOrderFeignClient.editByIdAndComment   err:",e);
        }
        return feignResponse.ok()?feignResponse.getData():0;
    }

    @Diff(diffMethod = "getByIdFeign", diffCompare = "com.shuidihuzhu.cf.diff.CrowdfundingOrderFeignSearchCompare")
    @Override
    public CrowdfundingOrder getById(Long id) {
        return this.getByIdFeign(id);
    }

    @Diff(diffMethod = "selectExtremeFeign", diffCompare = "com.shuidihuzhu.cf.diff.CrowdfundingOrderFeignSearchCompare")
    @Override
    public CrowdfundingOrder selectExtreme(int caseId, int orderHandle) {
        return this.selectExtremeFeign(caseId, orderHandle);
    }

    @Diff(diffMethod = "selectOrderOneByInfoIdFeign", diffCompare = "com.shuidihuzhu.cf.diff.CrowdfundingOrderFeignSearchCompare")
    @Override
    public List<CrowdfundingOrder> selectOrderOneByInfoId(Set<Integer> caseIds) {
        return this.selectOrderOneByInfoIdFeign(caseIds);
    }

    @Diff(diffMethod = "getListByInfoIdFeign", diffCompare = "com.shuidihuzhu.cf.diff.CrowdfundingOrderFeignSearchCompare")
    @Override
    public List<CrowdfundingOrder> getListByInfoId(int crowdfundingId, Integer offset, Integer limit) {
        return this.getListByInfoIdFeign(crowdfundingId, offset, limit);
    }

    @Diff(diffMethod = "getValidPayedSuccessListByIdsFeign", diffCompare = "com.shuidihuzhu.cf.diff.CrowdfundingOrderFeignSearchCompare")
    @Override
    public List<CrowdfundingOrder> getValidPayedSuccessListByIds(List<Long> crowdfundingIds){
        return this.getValidPayedSuccessListByIdsFeign(crowdfundingIds);
    }

    @Diff(diffMethod = "getByPageFeign", diffCompare = "com.shuidihuzhu.cf.diff.CrowdfundingOrderFeignSearchCompare")
    @Override
    public List<CrowdfundingOrder> getByPage(Integer crowdfundingId, int offset, int limit){
        return this.getByPageFeign(crowdfundingId, offset, limit);
    }

    @Override
    public CrowdfundingOrder getByIdFeign(Long id) {
        List<CrowdfundingOrder> crowdfundingOrderList = this.getByIds(Lists.newArrayList(id), null, null);
        return CollectionUtils.isNotEmpty(crowdfundingOrderList)?crowdfundingOrderList.get(0):null;
    }


    @Override
    public List<CrowdfundingOrder> selectByUserIdAndThirdTypeTidbFeign(Long userId, Integer caseId, Integer thirdType,
                                                                       Integer current, Integer pageSize) {
        Response<CrowdfundingOrderModelResult> feignResponse = NewResponseUtil.makeFail(null);
        try {
            feignResponse = cfOrderTidbFeignClientDelegate.selectByUserIdAndThirdType(userId, caseId, thirdType, current, pageSize);
        }catch (Exception e){
            log.error(this.getClass().getSimpleName()+" crowdfundingOrderFeignClient.selectByUserIdAndThirdType err:",e);
        }
        return this.getPageCrowdfundingOrderByFeignResponse(feignResponse,current,pageSize);
    }

    @Override
    public List<CrowdfundingOrder> selectByCaseIdAndTimeTidbFeign(int caseId, String begin, String end,
                                                                  Integer current, Integer pageSize) {
        Response<CrowdfundingOrderModelResult> feignResponse = NewResponseUtil.makeFail(null);
        try {
            feignResponse = cfOrderTidbFeignClientDelegate.selectByCaseIdAndTimeV1(caseId,
                    DateUtil.parseDate(begin).getTime(),DateUtil.parseDate(end).getTime(),current,pageSize);
        }catch (Exception e){
            log.error(this.getClass().getSimpleName()+" crowdfundingOrderFeignClient.selectByCaseIdAndTime err:",e);
        }
        return this.getPageCrowdfundingOrderByFeignResponse(feignResponse,current,pageSize);
    }

    @Override
    public CrowdfundingOrder selectExtremeFeign(int caseId, int orderHandle) {
        FeignResponse<CrowdfundingOrder> feignResponse = FeignResponse.fallback();
        try {
            feignResponse = crowdfundingOrderFeignClient.selectExtreme(caseId, orderHandle);
        }catch (Exception e){
            log.error(this.getClass().getSimpleName()+" crowdfundingOrderFeignClient.selectExtreme err:",e);
        }
        return feignResponse.ok()?feignResponse.getData():null;
    }

    @Override
    public List<CrowdfundingOrder> selectOrderOneByInfoIdFeign(Set<Integer> caseIds) {
        FeignResponse<List<CrowdfundingOrder>> feignResponse = FeignResponse.fallback();
        try {
            feignResponse = crowdfundingOrderFeignClient.selectOrderOneByInfoId(caseIds);
        }catch (Exception e){
            log.error(this.getClass().getSimpleName()+" crowdfundingOrderFeignClient.selectOrderOneByInfoId err:",e);
        }
        return feignResponse.ok()?feignResponse.getData():Lists.newArrayList();
    }

    @Override
    public List<CrowdfundingOrder> getListByInfoIdFeign(int crowdfundingId, Integer offset, Integer limit) {
        FeignResponse<List<CrowdfundingOrder>> feignResponse = FeignResponse.fallback();
        try {
            feignResponse = crowdfundingOrderFeignClient.getListByInfoId(crowdfundingId, offset, limit);
        }catch (Exception e){
            log.error(this.getClass().getSimpleName()+" crowdfundingOrderFeignClient.getListByInfoId err:",e);
        }
        return feignResponse.ok()?feignResponse.getData():Lists.newArrayList();
    }

    @Override
    public List<CrowdfundingOrder> getByPageFeign(Integer crowdfundingId, int offset, int limit) {
        FeignResponse<List<CrowdfundingOrder>> feignResponse = FeignResponse.fallback();
        try{
            feignResponse = crowdfundingOrderFeignClient.getByPage(crowdfundingId, offset, limit);
        }catch (Exception e){
            log.error(this.getClass().getSimpleName()+" crowdfundingOrderFeignClient.getByPage  err:",e);
        }
        return feignResponse.ok()?feignResponse.getData():Lists.newArrayList();
    }
    @Override
    public List<CrowdfundingOrder> getSuccessByPageFeign(CaseOrderQueryParam queryParam){
        FeignResponse<CrowdfundingOrderModelResult> feignResponse = FeignResponse.fallback();
        try {
            feignResponse = crowdfundingOrderFeignClient.getOrderByCaseIdParam(queryParam);
        }catch (Exception e){
            log.error(this.getClass().getSimpleName()+ "crowdfundingOrderFeignClient.getSuccessByPage err:");
        }
        return this.getPageCrowdfundingOrderByFeignResponse(feignResponse, queryParam.getPageNum(), queryParam.getPageSize());
    }



    @Override
    public List<CrowdfundingOrder> getValidPayedSuccessListByIdsFeign(List<Long> ids){
        if (CollectionUtils.isEmpty(ids)){
            return Lists.newArrayList();
        }

        return this.getByIds(ids,
                CfSimpleTrueOrFalseEnum.TRUE.value(),
                CfSimpleTrueOrFalseEnum.TRUE.value());
    }

    @Override
    public List<CrowdfundingOrder> getByIds(List<Long> list, Integer valid, Integer payStatus) {
        if (CollectionUtils.isEmpty(list)){
            return Lists.newArrayList();
        }
        FeignResponse<List<CrowdfundingOrder>> feignResponse = FeignResponse.fallback();
        try {
            feignResponse = crowdfundingOrderFeignClient.getByIds(list,valid, payStatus);
        }catch (Exception e){
            log.error(this.getClass().getSimpleName()+" crowdfundingOrderFeignClient.getByIds err:",e);
        }
        return feignResponse.ok()?feignResponse.getData():Lists.newArrayList();
    }

    private Page<CrowdfundingOrder> getPageCrowdfundingOrderByFeignResponse(FeignResponse<CrowdfundingOrderModelResult> feignResponse,
                                                                            int pageNum,
                                                                            int pageSize){
        Page<CrowdfundingOrder> crowdfundingOrders = new Page();
        if (feignResponse.ok()){
            crowdfundingOrders.addAll(feignResponse.getData().getCrowdfundingOrders()) ;
            crowdfundingOrders.setTotal(feignResponse.getData().getTotalCount());
            crowdfundingOrders.setPageNum(pageNum);
            crowdfundingOrders.setPageSize(pageSize);
            return crowdfundingOrders;
        }
        return new Page(pageNum,pageSize);
    }

    private Page<CrowdfundingOrder> getPageCrowdfundingOrderByFeignResponse(Response<CrowdfundingOrderModelResult> feignResponse,
                                                                            int pageNum,
                                                                            int pageSize){
        Page<CrowdfundingOrder> crowdfundingOrders = new Page();
        if (feignResponse.ok()){
            crowdfundingOrders.addAll(feignResponse.getData().getCrowdfundingOrders()) ;
            crowdfundingOrders.setTotal(feignResponse.getData().getTotalCount());
            crowdfundingOrders.setPageNum(pageNum);
            crowdfundingOrders.setPageSize(pageSize);
            return crowdfundingOrders;
        }
        return new Page(pageNum,pageSize);
    }
}
