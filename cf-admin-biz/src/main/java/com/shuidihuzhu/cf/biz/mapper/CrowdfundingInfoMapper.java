package com.shuidihuzhu.cf.biz.mapper;

import com.shuidihuzhu.cf.model.crowdfunding.AdminCrowdfundingOperation;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface CrowdfundingInfoMapper {

    CrowdfundingInfoMapper INSTANCE = Mappers.getMapper(CrowdfundingInfoMapper.class);

    CrowdfundingInfoVo toCrowdfundingInfoVo(CrowdfundingInfo info);

//    @Mappings({
//        @Mapping(source = "ext.cryptoRegisterMobile", target = "cryptoRegisterMobile"),
//        @Mapping(source = "operation.operation", target = "operation"),
//        @Mapping(source = "operation.auditCommitTime", target = "auditCommitTime"),
//        @Mapping(source = "operation.refuseCount", target = "refuseCount"),
//        @Mapping(source = "operation.callCount", target = "callCount"),
//        @Mapping(source = "operation.callStatus", target = "callStatus"),
//        @Mapping(source = "operation.operatorId", target = "operatorId")
//
//    })
//    CrowdfundingInfoVo toCrowdfundingInfoVo(CrowdfundingInfo info, CfInfoExt ext, AdminCrowdfundingOperation operation);

    List<CrowdfundingInfoVo> toCrowdfundingInfoVos(List<CrowdfundingInfo> infos);
}
