package com.shuidihuzhu.cf.biz.crmUserManage.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.admin.util.admin.AdminCfIdCardUtil;
import com.shuidihuzhu.cf.biz.crmUserManage.CrmUserManageBiz;
import com.shuidihuzhu.cf.biz.crmUserManage.CrmUserManageRecordBiz;
import com.shuidihuzhu.cf.biz.crmUserManage.CrmUserManageService;
import com.shuidihuzhu.cf.biz.crmUserManage.CrmUserRoleBiz;
import com.shuidihuzhu.cf.biz.crmUserManage.UserManageUtil;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.dao.crmUserManage.UserManageExtDao;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.model.CrmUserManage.UserManage;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.admin.model.CrmUserManage;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CrmUserManageBizImpl implements CrmUserManageBiz {

    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private Producer producer;
    @Autowired
    private CrmUserManageRecordBiz userManageRecordBiz;
    @Autowired
    private IRiskDelegate riskDelegate;
    @Autowired
    private CrmUserRoleBiz crmUserRoleBiz;
    @Autowired
    private UserManageExtDao manageExtDao;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;
    @Autowired
    private CrmUserManageService crmUserManageService;

    @Override
    public Map<String, CrmUserManage.UserMapping> batchBindUserMapping(Set<String> mobiles) {

        log.info("crm mobile绑定 mobile:{}", mobiles);

        Map<String, CrmUserManage.UserMapping> result = Maps.newHashMap();

        if (CollectionUtils.isEmpty(mobiles)) {
            return result;
        }

        // 做过关联的手机号就不在关联了
        Map<String, CrmUserManage.UserMapping> allUserMapping = selectUserMappings(mobiles);
        List<String> noMappingMobiles = Lists.newArrayList();
        for (String mobile : mobiles) {
            if (!allUserMapping.containsKey(mobile)) {
                noMappingMobiles.add(mobile);
            }
        }

        Map<String, CrmUserManage.UserMapping> userMappings = bindPersonIdMapping(noMappingMobiles);
        if (MapUtils.isNotEmpty(userMappings)) {
            allUserMapping.putAll(userMappings);
        }

        return allUserMapping;
    }

    @Override
    public CrmUserManage.UserMapping bindUserMapping(String mobile) {

        log.info("crm mobile绑定 mobile:{}", mobile);
        if (StringUtils.isBlank(mobile)) {
            return null;
        }

        String cryptoMobile = oldShuidiCipher.aesEncrypt(mobile);
        List<UserManage.UserAccount> dbAccounts = crmUserManageService.selectByMobiles(Lists.newArrayList(cryptoMobile));
        if (CollectionUtils.isNotEmpty(dbAccounts)) {
            return convertUserAccount(dbAccounts.get(0));
        }

        Map<String, CrmUserManage.UserMapping> userMappings = bindPersonIdMapping(Lists.newArrayList(mobile));

        return userMappings.get(mobile);
    }

    private Map<String, CrmUserManage.UserMapping> bindPersonIdMapping(Collection<String> mobiles)  {

        Map<String, CrmUserManage.UserMapping> userMappings = Maps.newHashMap();

        if (CollectionUtils.isEmpty(mobiles)) {
            return userMappings;
        }

        List<UserManage.UserAccount> addAccounts = Lists.newArrayList();
        for (String mobile : mobiles) {
            String personId = UserManage.PERSON_ID_PREFIX + UUID.randomUUID().toString();
            UserManage.UserAccount account = new UserManage.UserAccount();

            account.setCryptoMobile( oldShuidiCipher.aesEncrypt(mobile));
            account.setPersonId(personId);

            addAccounts.add(account);
            userMappings.put(mobile, new CrmUserManage.UserMapping(mobile, personId, ""));
        }

        crmUserManageService.addUsers(addAccounts);
        sendUserMappingMq(Lists.newArrayList(userMappings.values()));

        List<UserManage.UserManageLog> records = Lists.newArrayList();
        records.addAll(userManageRecordBiz.buildRelationRecords(addAccounts, UUID.randomUUID().toString(), UserManage.RECORD_PID_COMMENT));
        userManageRecordBiz.addUserManageRecords(records);
        log.info("crm手机号绑定param:{}", addAccounts);
        return userMappings;
    }

    @Override
    public Map<String, CrmUserManage.UserMapping> mergeUserMapping(String oldMobile, String newRaiseMobile) {

        log.info("crm 换号发起时 oldMobile:{} newRaiseMobile:{}", oldMobile, newRaiseMobile);
        if (StringUtils.isNotBlank(oldMobile) && StringUtils.isNotBlank(newRaiseMobile)
            && !Objects.equals(oldMobile, newRaiseMobile)) {
            return mergeUuid(Sets.newHashSet(oldMobile, newRaiseMobile));
        }

        return Maps.newHashMap();
    }

    @Override
    public Map<String, CrmUserManage.UserMapping> selectUserMappings(Set<String> mobiles) {

        Map<String, CrmUserManage.UserMapping> result = Maps.newHashMap();

        if (CollectionUtils.isEmpty(mobiles)) {
            return result;
        }

        List<String> cryptoMobiles = Lists.newArrayList();
        for (String mobile : mobiles) {
            cryptoMobiles.add(oldShuidiCipher.aesEncrypt(mobile));
        }

        List<UserManage.UserAccount> dbAccounts = crmUserManageService.selectByMobiles(cryptoMobiles);
        for (UserManage.UserAccount account : dbAccounts) {
            String mobile = shuidiCipher.decrypt(account.getCryptoMobile());
            result.put(mobile,
                    new CrmUserManage.UserMapping(mobile, account.getPersonId(), account.getUuid()));
        }

        return result;
    }

    private Map<String, CrmUserManage.UserMapping> convertAccountMapping(List<UserManage.UserAccount> accounts) {

        Map<String, CrmUserManage.UserMapping> result = Maps.newHashMap();

        if (CollectionUtils.isEmpty(accounts)) {
            return Maps.newHashMap();
        }

        for (UserManage.UserAccount account : accounts) {

            CrmUserManage.UserMapping mappings = convertUserAccount(account);
            if (mappings != null) {
                result.put(mappings.getMobile(), mappings);
            }
        }

        return result;
    }

    private CrmUserManage.UserMapping convertUserAccount(UserManage.UserAccount account) {
        if (account == null) {
            return null;
        }

        CrmUserManage.UserMapping userMapping = new CrmUserManage.UserMapping();
        userMapping.setMobile(shuidiCipher.decrypt(account.getCryptoMobile()));
        userMapping.setPersonId(account.getPersonId());
        userMapping.setUuid(account.getUuid());

        return userMapping;
    }

    private void sendUserMappingMq(List<CrmUserManage.UserMapping> userMappings) {
        if (CollectionUtils.isEmpty(userMappings)) {
            return;
        }

        MessageResult result = producer.send(new Message(MQTopicCons.CF,
                CfClientMQTagCons.CLEW_MOBILE_RELATION_NOTICE,
                "" + System.currentTimeMillis(),
                userMappings));

        log.info("crm 手机号用户绑定消息发送. msg:{} result:{}", userMappings, result);
    }

    @Override
    public Map<String, CrmUserManage.UserMapping> mergeUuid(Set<String> mobiles) {

        log.info("合并多个手机号到一个uuid.mobiles:{}", mobiles);

        if (CollectionUtils.isEmpty(mobiles) || mobiles.size() == 1) {
            return Maps.newHashMap();
        }

        List<String> allCryptoMobiles = getCryptoTextList(mobiles);

        List<UserManage.UserAccount> existUserAccounts = crmUserManageService.selectByMobiles(allCryptoMobiles);
        Set<String> existUuids = getCurrentUuids(existUserAccounts);
        log.info("当前合并账户已有的uuid:{}", existUuids);

        List<UserManage.UserAccount> newAddAccounts = getNeedAddAccounts(allCryptoMobiles, existUserAccounts);
        List<UserManage.UserAccount> allUserAccounts = getAllAccounts(newAddAccounts, existUserAccounts, allCryptoMobiles,
                existUuids);

        List<UserManage.UserManageLog> records = Lists.newArrayList();
        String version = UUID.randomUUID().toString();
        if (CollectionUtils.isNotEmpty(newAddAccounts)) {
            crmUserManageService.addUsers(newAddAccounts);
            log.info("这次合并需要新建personId的用户有newAddAccounts:{}", newAddAccounts);

            records.addAll(userManageRecordBiz.buildRelationRecords(newAddAccounts, version, UserManage.RECORD_PID_COMMENT));
        }

        // 如果账户都没有uuid 或者 账户有多个uuid  则统一用一个新的uuid
        boolean allRefresh = CollectionUtils.isEmpty(existUuids) || existUuids.size() > 1;
        String newUuid = allRefresh ? UserManage.UUID_ID_PREFIX + UUID.randomUUID().toString() :
                Lists.newArrayList(existUuids).get(0);
        log.info("用户合并 version:{} allRefresh:{} newUuid:{}", version, allRefresh, newUuid);

        List<CrmUserManage.UserMapping> userMappings = Lists.newArrayList();
        List<String> cryptoMobiles = Lists.newArrayList();
        for (UserManage.UserAccount account : allUserAccounts) {
            if (needRefreshUuid(allRefresh, account, newUuid)) {
                account.setUuid(newUuid);
                cryptoMobiles.add(account.getCryptoMobile());
                userMappings.add(new CrmUserManage.UserMapping(shuidiCipher.decrypt(account.getCryptoMobile()),
                        account.getPersonId(), newUuid));

                records.addAll(userManageRecordBiz.buildRelationRecords(Lists.newArrayList(account), version, UserManage.RECORD_UUID_COMMENT));
            }
        }

        if (CollectionUtils.isNotEmpty(cryptoMobiles)) {
            // 更新uuid
            log.info("最终用户合并 version:{} cryptoMobiles:{}", version, cryptoMobiles);
            crmUserManageService.updateUuidByIdCards(newUuid, cryptoMobiles);
            // 发变更消息
            sendUserMappingMq(userMappings);
        }

        userManageRecordBiz.addUserManageRecords(records);
        return convertAccountMapping(allUserAccounts);
    }

    private List<UserManage.UserAccount> getSameUuidAccounts(Set<String> cryptoMobiles, Set<String> existUuids) {

        List<UserManage.UserAccount> sameUuidAccounts = crmUserManageService.selectByUuids(existUuids);
        List<UserManage.UserAccount> needMergeAccounts = Lists.newArrayList();
        for (UserManage.UserAccount account : sameUuidAccounts) {
            if (!cryptoMobiles.contains(account.getCryptoMobile())) {
                needMergeAccounts.add(account);
                log.info("相同的uuid也需要合并 id:{} uuid:{}", account.getId(), account.getUuid());
            }
        }

        return needMergeAccounts;
    }

    private boolean needRefreshUuid(boolean allRefresh, UserManage.UserAccount account, String newUuid) {

        return allRefresh || !Objects.equals(newUuid, StringUtils.isBlank(account.getUuid()));
    }

    private List<String> getCryptoTextList(Collection<String> plainText) {
        List<String> allCryptoTexts = Lists.newArrayList();
        for (String text : plainText) {
            allCryptoTexts.add(oldShuidiCipher.aesEncrypt(text));
        }

        return allCryptoTexts;
    }

    private List<UserManage.UserAccount> getNeedAddAccounts(List<String> allCryptoMobiles,
                                                            List<UserManage.UserAccount> dbUserAccounts) {

        Set<String> existAccounts = dbUserAccounts.stream()
                .map(UserManage.UserAccount::getCryptoMobile).collect(Collectors.toSet());

        List<UserManage.UserAccount> needAddAccounts = Lists.newArrayList();
        for (String cryptoMobile : allCryptoMobiles) {
            if (!existAccounts.contains(cryptoMobile)) {

                String personId = UserManage.PERSON_ID_PREFIX + UUID.randomUUID().toString();
                UserManage.UserAccount account = new UserManage.UserAccount();
                account.setCryptoMobile(cryptoMobile);
                account.setPersonId(personId);
                needAddAccounts.add(account);
            }
        }

        return needAddAccounts;
    }

    private Set<String> getCurrentUuids(List<UserManage.UserAccount> userAccounts) {
        Set<String> existUuids = Sets.newHashSet();
        for (UserManage.UserAccount account : userAccounts) {
            if (StringUtils.isNotBlank(account.getUuid())) {
                existUuids.add(account.getUuid());
            }
        }
        return existUuids;
    }

    private List<UserManage.UserAccount> getAllAccounts(List<UserManage.UserAccount> newAddAccounts,
                                                            List<UserManage.UserAccount> existAccounts,
                                                        List<String> allCryptoMobiles,
                                                        Set<String> existUuids) {

        List<UserManage.UserAccount> allUserAccounts = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(newAddAccounts)) {
            allUserAccounts.addAll(newAddAccounts);
        }

        if (CollectionUtils.isNotEmpty(existAccounts)) {
            allUserAccounts.addAll(existAccounts);
        }

        if (CollectionUtils.isNotEmpty(existUuids)) {
            allUserAccounts.addAll(getSameUuidAccounts(Sets.newHashSet(allCryptoMobiles), existUuids));
        }

        return allUserAccounts;
    }

    @Override
    public void noticeClewCaseRaise(CrmUserManage.ClewCaseRaiseInfo raiseInfo) {

        log.info("crm用户管理线索对应的案例发起 raiseInfo:{}", raiseInfo);

        int caseId = raiseInfo.getCaseId();

        CfFirsApproveMaterial material = riskDelegate.getCfFirsApproveMaterialByInfoId(caseId);
        if (material == null) {
            log.error("不能找到案例 caseId:{}", caseId);
            return;
        }
        updateIdCard(raiseInfo, material);

 //       crmUserRoleBiz.onCaseRaise(raiseInfo, material);

        String cryptoIdCard = oldShuidiCipher.aesEncrypt(
                AdminCfIdCardUtil.convertLastCharUpper(shuidiCipher.decrypt(UserManageUtil.queryRaiseCryptoIdCard(material))));

        List<UserManage.UserAccount> userAccounts = crmUserManageService.selectByIdCards(Lists.newArrayList(cryptoIdCard));
        log.info("案例发起这些相同的身份证要合并 raiseInfo:{} accounts:{}", raiseInfo, userAccounts);
        // 这些account 需要合并
        Set<String> mobiles = Sets.newHashSet();
        mobiles.add(raiseInfo.getSourceMobile());
        if (StringUtils.isNotBlank(raiseInfo.getRaiseCaseMobile())) {
            mobiles.add(raiseInfo.getRaiseCaseMobile());
        }
        for (UserManage.UserAccount account : userAccounts) {
            mobiles.add(shuidiCipher.decrypt(account.getCryptoMobile()));
        }

        mergeUuid(mobiles);
    }

    @Override
    public void markRaiseCaseQuality(String uuid, String personId, int raiseCaseQuality, int userId) {
        log.info("更新用户发起案例的紧急程度 uuid:{} personId:{} raiseCaseQuality:{} userId:{}", uuid, personId,
                raiseCaseQuality, userId);

        if (StringUtils.isBlank(uuid) && StringUtils.isBlank(personId)) {
            return ;
        }

        // 先删除以前的标示
        UserManage.UserManageExt manageExt = new UserManage.UserManageExt();
        if (StringUtils.isNotBlank(uuid)) {
            manageExtDao.deleteUserExtByKeys(UserManage.ExtMainFieldEnum.USER_UID.getFieldName(),
                            uuid, Lists.newArrayList(UserManage.ExtFieldEnum.USER_RAISE_QUALITY.getFieldName()));
            manageExt.setMark(UserManage.ExtMainFieldEnum.USER_UID.getFieldName());
            manageExt.setMarkValue(uuid);
            manageExt.setExtKey(UserManage.ExtFieldEnum.USER_RAISE_QUALITY.getFieldName());
            manageExt.setExtValue("" + raiseCaseQuality);

        } else {
            manageExtDao.deleteUserExtByKeys(UserManage.ExtMainFieldEnum.USER_PID.getFieldName(),
                    personId, Lists.newArrayList(UserManage.ExtFieldEnum.USER_RAISE_QUALITY.getFieldName()));

            manageExt.setMark(UserManage.ExtMainFieldEnum.USER_PID.getFieldName());
            manageExt.setMarkValue(personId);
            manageExt.setExtKey(UserManage.ExtFieldEnum.USER_RAISE_QUALITY.getFieldName());
            manageExt.setExtValue("" + raiseCaseQuality);
        }

        manageExtDao.addExtList(Lists.newArrayList(manageExt));

        userManageRecordBiz.addCaseQualityRecord(uuid, personId, raiseCaseQuality, userId);

    }

    private void updateIdCard(CrmUserManage.ClewCaseRaiseInfo raiseInfo,
                              CfFirsApproveMaterial material) {

        String name =  UserManageUtil.queryRaiseUserName(material);

        Set<String> cryptoMobiles = Sets.newHashSet();
        cryptoMobiles.add(oldShuidiCipher.aesEncrypt(raiseInfo.getSourceMobile()));
        if (StringUtils.isNotBlank(raiseInfo.getRaiseCaseMobile())) {
            cryptoMobiles.add(oldShuidiCipher.aesEncrypt(raiseInfo.getRaiseCaseMobile()));
        }

        String upperCryptoIdCard = oldShuidiCipher.aesEncrypt(AdminCfIdCardUtil
                .convertLastCharUpper(shuidiCipher.decrypt(UserManageUtil.queryRaiseCryptoIdCard(material))));
        List<UserManage.UserAccount> existAccounts = crmUserManageService.selectByMobiles(cryptoMobiles);
        Set<String> plainMobiles = Sets.newHashSet();
        for (String cryptoMobile : cryptoMobiles) {
            boolean found = false;
            for (UserManage.UserAccount account : existAccounts) {
                if (Objects.equals(cryptoMobile, account.getCryptoMobile())) {
                    found = true;
                    break;
                }
            }
            if (!found) {
                log.info("发起案例时 手机号还没有关联. mobile:{}", cryptoMobile);
                plainMobiles.add(shuidiCipher.decrypt(cryptoMobile));
            }
        }
        bindPersonIdMapping(plainMobiles);

        // 更新手机号对应的身份证信息
        log.info("crm用户管理-更新手机号对应的身份证. mobiles:{} idCard:{}", cryptoMobiles, upperCryptoIdCard);
        crmUserManageService.updateIdCardByMobiles(upperCryptoIdCard, name, cryptoMobiles);
        String version = UUID.randomUUID().toString();
        userManageRecordBiz.addUserManageRecords(userManageRecordBiz
                .buildRelationRecords(existAccounts, version, String.format(UserManage.RECORD_IDCARD_COMMENT, raiseInfo.getCaseId())));
    }


}
