package com.shuidihuzhu.cf.biz.crowdfunding;

import com.github.pagehelper.PageInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CfMaterialVerityHistory;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditOperationItem;
import com.shuidihuzhu.cf.vo.approve.RiverHandleParamVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface CfMaterialVerityHistoryBiz {

    void recordVerityHistory(CfMaterialVerityHistory.CfMaterialVerityHistoryRecord historyRecord, long workOrderId, long approveControlId);

    PageInfo<CfMaterialVerityHistory.CfMaterialVerityHistoryVo> queryMaterialVerityHistory(String infoId,
                                                                                           int materialId,
                                                                                           int handleType,
                                                                                           int current,
                                                                                           int size);

    Map<Integer, CfMaterialVerityHistory.HistoryOverviewVO> getHistoryOverview(String infoId);

    boolean hasPermissionWithUser(int userId, String permission);

    CfMaterialVerityHistory selectLatestMaterial(int caseId, int materialId, int handleType);

    CfMaterialVerityHistory selectLatestMaterial(int caseId, int materialId);


    CfMaterialVerityHistory.CfMaterialVerityHistoryVo getLastDetail(int caseId, int materialId, int handleType);

    int insertList(List<CfMaterialVerityHistory> verityHistories);

    Map<Integer, CfMaterialVerityHistory.MaterialOpDetail> queryMaterialOpDetail(String infoUuid);

    void insertByType(InitialAuditOperationItem.HandleCaseInfoParam handleCaseInfoParam,
                               String infoUuid, String materialInfo, int handleResult, int type);

    String queryOperateDetail(int userId);

    int getCount(int caseId,
                 int materialId,
                 int handleType);

    void totalRecordInitialAudit(InitialAuditOperationItem.HandleCaseInfoParam param);

    void totalRecordCreditAudit(RiverHandleParamVO param);

    CfMaterialVerityHistory selectLatestByWorkOrder(int caseId, long workOrderId, int materialId);
}
