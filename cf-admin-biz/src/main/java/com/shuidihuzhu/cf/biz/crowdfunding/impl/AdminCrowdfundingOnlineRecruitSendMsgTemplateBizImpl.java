package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingDetailSendMsgTemplateBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfMaterialVerityHistoryBiz;
import com.shuidihuzhu.cf.model.crowdfunding.AdminSmsTemplateSettingsInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingMsgContent;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingMsgTemplateVo;
import com.shuidihuzhu.msg.model.SmsTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by niejiangnan on 2017/7/19.
 */
@Service("adminCrowdfundingOnlineRecruitSendMsgTemplateBizImpl")
@Slf4j
public class AdminCrowdfundingOnlineRecruitSendMsgTemplateBizImpl implements AdminCrowdfundingDetailSendMsgTemplateBiz {

    @Resource
    private CfMaterialVerityHistoryBiz verityHistoryBiz;

    @Resource(name = "adminCrowdfundingDetailSendMsgTemplateBizImpl")
    private AdminCrowdfundingDetailSendMsgTemplateBizImpl msgTemplateBiz;

    @Override
    public List<CrowdfundingMsgContent> getAllMsgTemplate() {
        return msgTemplateBiz.getAllMsgTemplate();
    }

    @Override
    public int updateMsgTemplate(CrowdfundingMsgContent crowdfundingMsgContent) {
        return msgTemplateBiz.updateMsgTemplate(crowdfundingMsgContent);
    }

    @Override
    public CrowdfundingMsgContent getByKey(String key) {
        return msgTemplateBiz.getByKey(key);
    }

    //复制需要的属性
    @Override
    public List<CrowdfundingMsgTemplateVo> setVolist(List<CrowdfundingMsgTemplateVo> Volist, List<CrowdfundingMsgContent> MsgTemplatelist) {
        return msgTemplateBiz.setVolist(Volist, MsgTemplatelist);
    }

    @Override
    public String getMsgTitleByContent(String message) {
        return msgTemplateBiz.getMsgTitleByContent(message);
    }

    //新短信service
    @Override
    public List<Integer> getAllSmsAuthGroup(int userId, int authType) {
        List<Integer> smsAuthGroupCode = Lists.newArrayList();

        for (AdminSmsTemplateSettingsInfo.OnlineRecruitSmsGroup group : AdminSmsTemplateSettingsInfo.OnlineRecruitSmsGroup.values()) {
            String authString = (authType == 0 ? group.getSendTemplateAuth() : group.getAddTemplateAuth());
            if (verityHistoryBiz.hasPermissionWithUser(userId, authString)) {
                smsAuthGroupCode.add(group.getCode());
            }
        }
        return smsAuthGroupCode;
    }

    @Override
    public int addSmsTemplate(int smsGroup, String modelNum, int userId) {
        log.info("添加短信模版. userId:{} smsGroup:{} modelNum:{}", userId, smsGroup, modelNum);

        // 一般没有这种情况的发送
        if (!verityHistoryBiz.hasPermissionWithUser(userId, AdminSmsTemplateSettingsInfo.OnlineRecruitSmsGroup.fromCode(smsGroup).getAddTemplateAuth())) {
            log.info("用户没有添加短信模版的权限.userId:{}, smsGroup:{}, modelNum:{}" , userId, smsGroup, modelNum);
            return -1;
        }

        return msgTemplateBiz.doAddSmsTemplate(smsGroup, modelNum, userId);
    }

    @Override
    public int queryCurrentPriorityBySmsGroup(int smsGroup) {
        return msgTemplateBiz.queryCurrentPriorityBySmsGroup(smsGroup);
    }

    @Override
    public int updateStatusById(int id, int dataStatus, int userId) {
        return msgTemplateBiz.updateStatusById(id, dataStatus, userId);
    }

    @Override
    public int updateSmsTemplatePriority(int upId, int downId, int operateType, int userId) {
        return msgTemplateBiz.updateSmsTemplatePriority(upId, downId, operateType, userId);
    }


    @Override
    public List<AdminSmsTemplateSettingsInfo.SmsSettingsRecordView> selectRecordByTemplateId(int smsTemplateSettingsId) {

        return msgTemplateBiz.selectRecordByTemplateId(smsTemplateSettingsId);
    }

    @Override
    public List<AdminSmsTemplateSettingsInfo.SmsContentResult> selectAuthSmsContent(int userId) {

        List<AdminSmsTemplateSettingsInfo.SmsContentResult> contentResults = Lists.newArrayList();
        List<Integer> smsGroups = getAllSmsAuthGroup(userId, 0);
        if (CollectionUtils.isEmpty(smsGroups)) {
            return contentResults;
        }

        msgTemplateBiz.obtainAuthSmsContent(smsGroups, contentResults);

        return contentResults;
    }

    @Override
    public AdminSmsTemplateSettingsInfo.SmsTemplateView selectTemplateByModelNum(String modelNum) {
        return msgTemplateBiz.selectTemplateByModelNum(modelNum);
    }

    @Override
    public List<AdminSmsTemplateSettingsInfo.SmsTemplateView> selectTemplateByParam(int smsGroup, String templateTitle,
                                                                                    Integer operatorId,
                                                                             Integer dataStatus) {
        return msgTemplateBiz.selectTemplateByParam(smsGroup, templateTitle, operatorId, dataStatus);
    }

    @Override
    public List<SmsTemplate> getTemplateByModelNum(String modelNum) {
        return msgTemplateBiz.getTemplateByModelNum(modelNum);
    }

    @Override
    public List<AdminSmsTemplateSettingsInfo.SmsContentResult> getGroupContentList(int smsGroupCode) {
        return msgTemplateBiz.getGroupContentList(smsGroupCode);
    }

}
