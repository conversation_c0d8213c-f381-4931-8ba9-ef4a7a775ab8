package com.shuidihuzhu.cf.biz.sensitive.impl;

import com.shuidihuzhu.cf.biz.sensitive.SensitiveSpecialSymbolLogBiz;
import com.shuidihuzhu.cf.dao.sensitive.SensitiveSpecialSymbolLogDao;
import com.shuidihuzhu.cf.enums.crowdfunding.CfSensitiveWordRecordEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/4/3
 */
@Service
public class SensitiveSpecialSymbolLogBizImpl implements SensitiveSpecialSymbolLogBiz {

    @Autowired
    private SensitiveSpecialSymbolLogDao sensitiveSpecialSymbolLogDao;

    @Override
    public int insert(long bizId, long caseId, CfSensitiveWordRecordEnum.BizType contentType, String content, String hitWords) {
        if ( bizId <= 0 || caseId <= 0 || contentType == null || StringUtils.isBlank(content) || StringUtils.isBlank(hitWords)) {
            return 0;
        }
        return sensitiveSpecialSymbolLogDao.insert(bizId, caseId, contentType.value(), content, hitWords);
    }
}
