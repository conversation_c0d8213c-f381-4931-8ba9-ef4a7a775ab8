package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import com.shuidihuzhu.cf.client.feign.CfCommonFeignClient;
import com.shuidihuzhu.cf.client.feign.CfPlatformEsFeignClient;
import com.shuidihuzhu.cf.client.feign.cipher.constants.DesensitizeEnum;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.constants.crowdfunding.status.RelationShip;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminUserRealInfoDao;
import com.shuidihuzhu.cf.delegate.EncryptDelegate;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.domain.risk.RiskUgcVerifyDO;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.enums.risk.UgcTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdFundingVerificationView;
import com.shuidihuzhu.cf.model.crowdfunding.vo.VerificationSearchVo;
import com.shuidihuzhu.cf.service.crowdfunding.complaint.ComplaintService;
import com.shuidihuzhu.cf.service.risk.dark.DarkListService;
import com.shuidihuzhu.client.cf.api.model.CrowdFundingVerificationEsSearch;
import com.shuidihuzhu.client.cf.api.model.CrowdFundingVerificationIndexSearchResult;
import com.shuidihuzhu.client.cf.search.client.CfSearchClient;
import com.shuidihuzhu.client.cf.search.model.CfCaseIndexSearchParam;
import com.shuidihuzhu.client.cf.search.model.CfCaseIndexSearchResult;
import com.shuidihuzhu.client.cf.search.model.CfCaseModel;
import com.shuidihuzhu.client.cf.search.model.SearchRpcResult;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/2/28
 */

@Slf4j
@Service
public class AdminCfVerificationSearchBizImpl implements AdminCfVerificationSearchBiz {

    @Autowired
    private AdminCrowdFundingVerificationBiz adminCrowdFundingVerificationBiz;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @Autowired
    private CfSearchClient cfSearchClient;

    @Autowired
    private ComplaintService complaintService;

    @Autowired
    private EncryptDelegate encryptDelegate;

    @Autowired
    private AdminUserRealInfoDao adminUserRealInfoDao;

    @Autowired
    private CfPlatformEsFeignClient cfPlatformEsFeignClient;

    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Autowired
    private DarkListService darkListService;

    @Autowired
    private CfCommonFeignClient cfCommonFeignClient;

    @Autowired
    private MaskUtil maskUtil;

    @Override
    public Pair<Long, List<CrowdFundingVerificationView>> verificationSearchByEs(VerificationSearchVo verificationSearchVo) {
        CrowdFundingVerificationEsSearch crowdFundingVerificationEsSearch = this.convertVerificationQuerySearchParam(verificationSearchVo);

        Pair<Long, List<CrowdFundingVerification>> pair = searchFromEs(crowdFundingVerificationEsSearch);
        long total = pair.getLeft();
        List<CrowdFundingVerification> crowdFundingVerifications = pair.getRight();
        List<CrowdFundingVerificationView> crowdFundingVerificationViews = verificationConvert(crowdFundingVerifications);
        return Pair.of(total, crowdFundingVerificationViews);
    }

    private CrowdFundingVerificationEsSearch convertVerificationQuerySearchParam(VerificationSearchVo verificationSearchVo) {

        CrowdFundingVerificationEsSearch crowdFundingVerificationEsSearch = new CrowdFundingVerificationEsSearch();

        Integer current = verificationSearchVo.getCurrent();
        Integer pageSize = verificationSearchVo.getPageSize();
        String infoUuid = verificationSearchVo.getInfoUuid();
        Long verifyUserId = verificationSearchVo.getVerifyUserId();
        String userName = verificationSearchVo.getUserName();
        String verificationContent = verificationSearchVo.getVerificationContent();

        //案例查询
        if (StringUtils.isNotEmpty(infoUuid)) {
            crowdFundingVerificationEsSearch.setCrowdFundingInfoId(infoUuid);
        }

        //证实人查询
        if (Objects.nonNull(verifyUserId) && verifyUserId > 0) {
            crowdFundingVerificationEsSearch.setVerifyUserId(verifyUserId);
        }

        //证实人姓名查询
        if (StringUtils.isNotEmpty(userName)) {
            crowdFundingVerificationEsSearch.setUserName(userName);
        }

        //证实内容查询
        if (StringUtils.isNotEmpty(verificationContent)) {
            crowdFundingVerificationEsSearch.setDescription(verificationContent);
        }

        crowdFundingVerificationEsSearch.setFrom((current - 1) * pageSize);
        crowdFundingVerificationEsSearch.setSize(pageSize);

        return crowdFundingVerificationEsSearch;
    }

    /**
     * 查询es
     *
     * @param
     * @return
     */
    private Pair<Long, List<CrowdFundingVerification>> searchFromEs(CrowdFundingVerificationEsSearch crowdFundingVerificationEsSearch) {
        if (Objects.isNull(crowdFundingVerificationEsSearch)) {
            return Pair.of(0L, Lists.newArrayList());
        }
        FeignResponse<CrowdFundingVerificationIndexSearchResult> feignResponse = cfPlatformEsFeignClient.queryCrowdFundingVerification(crowdFundingVerificationEsSearch);
        CrowdFundingVerificationIndexSearchResult crowdFundingVerificationIndexSearchResult = Optional.ofNullable(feignResponse).filter(FeignResponse::ok).map(FeignResponse::getData).orElse(null);
        if (Objects.isNull(crowdFundingVerificationIndexSearchResult) || CollectionUtils.isEmpty(crowdFundingVerificationIndexSearchResult.getModels())) {
            return Pair.of(0L, Lists.newArrayList());
        }

        return Pair.of(crowdFundingVerificationIndexSearchResult.getTotal(), crowdFundingVerificationIndexSearchResult.getModels());
    }

    private List<CrowdFundingVerificationView> verificationConvert(List<CrowdFundingVerification> crowdFundingVerifications) {
        if (CollectionUtils.isEmpty(crowdFundingVerifications)) {
            return Lists.newArrayList();
        }
        log.info("es查询信息 cfCaseModels:{}", JSON.toJSONString(crowdFundingVerifications));

        List<CrowdFundingVerificationView> crowdFundingVerificationViewList = Lists.newArrayList();

        Set<String> infoUuidSet = crowdFundingVerifications.stream().map(CrowdFundingVerification::getCrowdFundingInfoId).collect(Collectors.toSet());

        List<CrowdfundingInfo> crowdfundingInfoList = adminCrowdfundingInfoBiz.getListByInfoUuIds(Lists.newArrayList(infoUuidSet));
        List<Integer> caseIds = crowdfundingInfoList.stream().map(CrowdfundingInfo::getId).collect(Collectors.toList());
        Map<String, Integer> cfCaseModelMap = crowdfundingInfoList.stream().collect(Collectors.toMap(CrowdfundingInfo::getInfoId, CrowdfundingInfo::getId, (o1, o2) -> o2));

        //查询证实人用户信息
        Set<Long> verifyUserIdSet = crowdFundingVerifications.stream().map(CrowdFundingVerification::getVerifyUserId).collect(Collectors.toSet());
        List<UserInfoModel> userInfoModelList = userInfoServiceBiz.getUserInfoByUserIdBatch(Lists.newArrayList(verifyUserIdSet));
        Map<Long, UserInfoModel> userInfoModelMap = userInfoModelList.stream().collect(Collectors.toMap(UserInfoModel::getUserId, Function.identity(), (o1, o2) -> o2));

        List<UserRealInfo> userRealInfoList = Lists.newArrayList();
        List<List<Long>> verifyUserIdList = Lists.partition(Lists.newArrayList(verifyUserIdSet), 500);
        for (List<Long> userIds : verifyUserIdList) {
            List<UserRealInfo> userRealInfos = adminUserRealInfoDao.getSuccessByUserIds(userIds);
            log.info("获取证实信息 response:{}", JSON.toJSONString(userRealInfos));
            if (CollectionUtils.isNotEmpty(userRealInfos)) {
                userRealInfoList.addAll(userRealInfos);
            }
        }

        Map<Long, UserRealInfo> userRealInfoMap = userRealInfoList.stream().collect(Collectors.toMap(UserRealInfo::getUserId, Function.identity(), (o1, o2) -> o2));

        Map<String, WorkOrderExt> workOrderExtMap = Maps.newHashMap();
        List<WorkOrderVO> workOrderVOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(caseIds)) {
            Response<List<WorkOrderVO>> response = cfWorkOrderClient.listByCaseIdsAndTypes(caseIds, Lists.newArrayList(14, 72));
            workOrderVOList = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList());
            if (CollectionUtils.isNotEmpty(workOrderVOList)) {
                List<Long> workOrderIds = workOrderVOList.stream().map(WorkOrderVO::getWorkOrderId).collect(Collectors.toList());
                log.info("获取到工单id workOrderIds:{}",JSON.toJSONString(workOrderIds));
                Response<List<WorkOrderExt>> listResponse = cfWorkOrderClient.listExtInfos(workOrderIds, OrderExtName.verificationId.getName());
                List<WorkOrderExt> workOrderExtList = Optional.ofNullable(listResponse).filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList());
                workOrderExtMap = workOrderExtList.stream().collect(Collectors.toMap(WorkOrderExt::getExtValue, Function.identity(), (o1, o2) -> o2));
                Response<List<WorkOrderExt>> response1 = cfWorkOrderClient.listExtInfos(workOrderIds, OrderExtName.extId.getName());
                List<WorkOrderExt> workOrderExtList1 = Optional.ofNullable(response1).filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList());
                Map<String, WorkOrderExt> workOrderExtMap1 = workOrderExtList1.stream().collect(Collectors.toMap(WorkOrderExt::getExtValue, Function.identity(), (o1, o2) -> o2));
                workOrderExtMap.putAll(workOrderExtMap1);
            }
        }
        log.info("workOrderExtMap workOrderExtMap:{}",JSON.toJSONString(workOrderExtMap));

        Map<Long, WorkOrderVO> workOrderVOMap = workOrderVOList.stream().collect(Collectors.toMap(WorkOrderVO::getWorkOrderId, Function.identity(), (o1, o2) -> o2));

        for (CrowdFundingVerification crowdFundingVerification : crowdFundingVerifications) {

            CrowdFundingVerificationView crowdFundingVerificationView = new CrowdFundingVerificationView();
            if (Objects.nonNull(crowdFundingVerification)) {
                Integer id = crowdFundingVerification.getId();
                if (Objects.isNull(id) || id <= 0) {
                    continue;
                }
                Timestamp createTime = crowdFundingVerification.getCreateTime();
                Integer valid = crowdFundingVerification.getValid();
                String description = crowdFundingVerification.getDescription();
                Integer relationShip = crowdFundingVerification.getRelationShip();
                long verifyUserId = crowdFundingVerification.getVerifyUserId();
                String infoUuid = crowdFundingVerification.getCrowdFundingInfoId();

                crowdFundingVerificationView.setVerificationId(id);
                crowdFundingVerificationView.setVerificationTime(DateUtil.getDate2LStr(createTime));
                crowdFundingVerificationView.setValid(valid == 1 ? "未删除" : "已删除");
                crowdFundingVerificationView.setVerificationValid(valid);
                crowdFundingVerificationView.setDescription(description);
                crowdFundingVerificationView.setRelationShip(RelationShip.codeOf(Objects.nonNull(relationShip) ? relationShip : 9).getDescription());
                crowdFundingVerificationView.setCaseId(Objects.nonNull(cfCaseModelMap.get(infoUuid)) ? cfCaseModelMap.get(infoUuid) : 0);

                RiskUgcVerifyDO verifyDO = cfCommonFeignClient.queryUgcVerify(Objects.nonNull(cfCaseModelMap.get(infoUuid)) ? cfCaseModelMap.get(infoUuid) : 0, UgcTypeEnum.VERIFICATION.getValue(), id).getData();
                boolean isBlackList = !darkListService.checkUGCPassed(verifyUserId, true);
                boolean seeOnlySelf = Objects.nonNull(verifyDO) || isBlackList;
                crowdFundingVerificationView.setHandleResult(seeOnlySelf ? HandleResultEnum.only_self.getShowMsg() : HandleResultEnum.pass_show.getShowMsg());

                UserInfoModel userInfoModel = userInfoModelMap.get(verifyUserId);
                if (Objects.nonNull(userInfoModel)) {
                    long userId = userInfoModel.getUserId();

                    crowdFundingVerificationView.setUserId(userId);
                    NumberMaskVo numberMaskVo = maskUtil.buildByEncryptPhone(userInfoModel.getCryptoMobile());
                    if(numberMaskVo != null) {
                        crowdFundingVerificationView.setMobile(numberMaskVo.getEncryptNumber());
                    }
                }

                UserRealInfo userRealInfo = userRealInfoMap.get(verifyUserId);
                if (Objects.nonNull(userRealInfo)) {
                    log.info("实名认证 userRealInfo:{}", JSON.toJSONString(userRealInfo));
                    String realName = userRealInfo.getName();
                    String cryptoIdCard = userRealInfo.getCryptoIdCard();
                    String idCard = encryptDelegate.decrypt(cryptoIdCard);

                    crowdFundingVerificationView.setName(realName);
                    NumberMaskVo numberMaskVo = maskUtil.buildByDecryptStrAndType(idCard, DesensitizeEnum.IDCARD);
                    if(numberMaskVo != null) {
                        crowdFundingVerificationView.setIdentityCard(numberMaskVo.getEncryptNumber());
                    }
                }

                WorkOrderExt workOrderExt = workOrderExtMap.get(String.valueOf(id));
                if (Objects.nonNull(workOrderExt) && (StringUtils.equals(workOrderExt.getExtName(), OrderExtName.verificationId.getName()) || StringUtils.equals(workOrderExt.getExtName(), OrderExtName.extId.getName()))) {
                    long workOrderId = workOrderExt.getWorkOrderId();
                    WorkOrderVO workOrderVO = workOrderVOMap.get(workOrderId);
                    if (Objects.nonNull(workOrderVO)) {
                        int orderType = workOrderVO.getOrderType();

                        if (orderType == WorkOrderType.ugcpinglun.getType()) {
                            crowdFundingVerificationView.setUgcId(workOrderId);
                        } else if (orderType == WorkOrderType.ugc_complaint_verify.getType()) {
                            crowdFundingVerificationView.setUgcComplaintId(workOrderId);
                        }
                    }
                }
                complaintService.buildComplaintInfo(crowdFundingVerificationView, id);
            }

            crowdFundingVerificationViewList.add(crowdFundingVerificationView);
        }

        return crowdFundingVerificationViewList.stream().sorted(Comparator.comparing(CrowdFundingVerificationView::getVerificationTime).reversed()).collect(Collectors.toList());
    }

}
