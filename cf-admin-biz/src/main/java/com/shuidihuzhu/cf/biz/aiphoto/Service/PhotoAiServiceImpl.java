package com.shuidihuzhu.cf.biz.aiphoto.Service;

import com.alibaba.fastjson.JSON;
import com.baidu.aip.face.AipFace;
import com.baidu.aip.ocr.AipOcr;
import com.google.common.base.Stopwatch;
import com.shuidihuzhu.cf.admin.delegate.OCRBaiDuDelegate;
import com.shuidihuzhu.cf.biz.aiphoto.PhotoAiService;
import com.shuidihuzhu.cf.biz.aiphoto.utils.PhotoUtil;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingAuthorBiz;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.dao.photoai.CfPhotoAiDao;
import com.shuidihuzhu.cf.model.aiphoto.AIPhotoInfoEnum;
import com.shuidihuzhu.cf.model.aiphoto.PhotoAiInfoModel;
import com.shuidihuzhu.cf.model.crowdfunding.CfAIPhotoModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor;
import com.shuidihuzhu.cf.service.AdminImageService;
import com.shuidihuzhu.cipher.ShuidiCipher;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;


/**
 * 照片AI识别服务实现类
 * 主要功能：
 * 1. 调用百度AI接口进行人脸识别和身份证OCR识别
 * 2. 对比手持身份证照片与身份证照片的人脸相似度
 * 3. 识别身份证上的姓名和身份证号码信息
 * 4. 保存识别结果并记录操作日志
 * 5. 支持患者照片和收款人照片两种类型的识别
 *
 * @package: com.shuidihuzhu.cf.aiphoto.Service
 * @Author: liujiawei
 * @Date: 2018/7/21  15:45
 */
@Slf4j
@RefreshScope  // 支持配置动态刷新
@Service
public class PhotoAiServiceImpl implements PhotoAiService {

    /** 照片AI识别数据访问对象 */
    @Autowired
    private CfPhotoAiDao cfPhotoAiDao;

    /** 通用操作记录客户端，用于记录AI识别操作日志 */
    @Autowired
    private CommonOperationRecordClient commonOperationRecordClient;

    /** 水滴加密器，用于身份证号码的加密解密 */
    @Autowired
    private ShuidiCipher shuidiCipher;

    /** 众筹发起人业务逻辑类，用于获取发起人信息进行对比 */
    @Autowired
    private AdminCrowdfundingAuthorBiz adminCrowdfundingAuthorBiz;

    /** 管理端图片服务，用于图片URL转换 */
    @Autowired
    private AdminImageService adminImageService;

    // ==================== 百度AI接口配置 ====================

    /** 百度人脸识别免费账号配置 */
    private static final String APP_ID_FACE = "44282797";
    private static final String API_KEY_FACE = "37KOfexdQvryL1CqSQZMYDmm";
    private static final String SECRET_KEY_FACE = "SCC2c62F9lpDBvegrUNc4xTe1PPy979x";

    // 百度AI付费账号配置（已注释，备用）
//    public static final String APP_ID_FACE = "11225727";
//    public static final String API_KEY_FACE = "7D9ElWtKyZv4DqUYqvu2DHzg";
//    public static final String SECRET_KEY_FACE = "4IeVz25aFBW3BSWtvqSSwfibOVSFGDlW";
//    public static final String APP_ID_OCR = "11242309";
//    public static final String API_KEY_OCR = "CAKgA2SOEhkyG3b644ovVAzU";
//    public static final String SECRET_KEY_OCR = "yKV0lFrK8S4sj7lYcIeGTy7k2rpQTsRD";

    /** 百度OCR识别委托类 */
    @Autowired
    private OCRBaiDuDelegate ocrBaiDuDelegate;

    /** 百度人脸识别客户端实例 */
    private final AipFace client =  new AipFace(APP_ID_FACE, API_KEY_FACE, SECRET_KEY_FACE);


    /**
     * 初始化百度人脸识别客户端配置
     * 在Spring容器初始化完成后自动调用
     */
    @PostConstruct
    public void init(){
        // 设置连接超时时间为2秒
        client.setConnectionTimeoutInMillis(2000);
        // 设置Socket读取超时时间为60秒
        client.setSocketTimeoutInMillis(60000);
    }

    /**
     * 获取百度人脸识别客户端实例
     *
     * @return AipFace 百度人脸识别客户端
     */
    public AipFace getFace() {
        return client;
    }

    /**
     * 获取照片AI识别信息
     * 通过百度AI接口对手持身份证照片和身份证照片进行人脸识别和OCR识别
     *
     * @param cfAIPhotoModel AI照片模型，包含手持身份证照片路径和身份证照片路径
     * @return PhotoAiInfoModel 包含人脸相似度、OCR识别结果等信息的模型对象
     */
    @Override
    public PhotoAiInfoModel getPhotoAiInfo(CfAIPhotoModel cfAIPhotoModel) {
        // 获取手持身份证照片路径
        String livePath = cfAIPhotoModel.getIdCardPhoto();
        // 获取身份证照片路径
        String idcardPath = cfAIPhotoModel.getOnlyIdCardPhoto();

        // 初始化照片AI信息模型对象
        PhotoAiInfoModel photoAiInfoModel = null;
        try {
            // 调用人脸识别和OCR识别方法，获取对比结果
            photoAiInfoModel = getFaceInfo(livePath, idcardPath);
            if (photoAiInfoModel != null) {
                // 设置关联的案例ID
                photoAiInfoModel.setInfoId(cfAIPhotoModel.getParentId());
                // 设置手持身份证照片路径
                photoAiInfoModel.setIdCard(cfAIPhotoModel.getIdCardPhoto());
                // 设置身份证照片路径
                photoAiInfoModel.setOnlyIdCard(cfAIPhotoModel.getOnlyIdCardPhoto());
            }
        } catch (Exception e) {
            // 记录异常信息并返回null
            log.info("AI照片处理异常：", e.getMessage());
            return null;
        }
        return photoAiInfoModel;
    }

    /**
     * 保存照片AI识别信息到数据库
     * 在保存前会对身份证号码进行加密处理以保护用户隐私
     *
     * @param photoAiInfoModel 照片AI识别信息模型
     * @return int 保存成功返回影响的行数，失败返回0
     */
    @Override
    public int savePhotoAiInfo(PhotoAiInfoModel photoAiInfoModel) {
        // 参数校验：如果模型对象为空，直接返回0
        if (photoAiInfoModel == null) {
            return 0;
        }

        // 处理身份证号码：如果识别到了身份证号码，需要进行加密处理
        if (StringUtils.isNotBlank(photoAiInfoModel.getIdNumber())) {
            // 如果身份证号码是"无法识别"，则不加密；否则使用水滴加密器进行加密
            String idNumber = "无法识别".equals(photoAiInfoModel.getIdNumber())
                    ? photoAiInfoModel.getIdNumber()
                    : shuidiCipher.encrypt(photoAiInfoModel.getIdNumber());

            // 将加密后的身份证号码设置到idCardNumber字段
            if (StringUtils.isNotBlank(idNumber)) {
                photoAiInfoModel.setIdCardNumber(idNumber);
            }
            // 清空原始身份证号码字段，避免明文存储
            photoAiInfoModel.setIdNumber("");
        }

        // 调用DAO层方法保存照片信息到数据库
        return cfPhotoAiDao.savePhotoInfo(photoAiInfoModel);
    }

    /**
     * 获取人脸识别和OCR识别结果
     * 调用百度AI接口进行人脸对比和身份证OCR识别
     *
     * @param liveAddr 手持身份证照片地址
     * @param idAddr 身份证照片地址
     * @return PhotoAiInfoModel 包含识别结果的模型对象，异常时返回null
     */
    private PhotoAiInfoModel getFaceInfo(String liveAddr, String idAddr) {
        // 获取百度人脸识别客户端
        AipFace clientFace = getFace();
        // 获取百度OCR识别客户端
        AipOcr clientOcr = ocrBaiDuDelegate.getOCRClient();
        try {
            // 调用工具类方法进行人脸对比和OCR识别
            // 该方法会返回人脸相似度、身份证信息识别结果等
            PhotoAiInfoModel photoAiInfoModel = PhotoUtil.getNewVersionResult(clientFace, clientOcr,
                    liveAddr, idAddr);
            // 根据状态码设置描述信息
            photoAiInfoModel.setDescription(AIPhotoInfoEnum.fromCode(photoAiInfoModel.getStatus()).getDesc());
            return photoAiInfoModel;
        } catch (Exception e) {
            // 记录第三方接口调用异常
            log.info("第三方接口AI处理异常：",e);
            return null;
        }
    }

    /**
     * 删除照片AI识别信息
     * 根据案例ID和照片类型删除对应的AI识别记录
     *
     * @param parentId 案例ID（关联的众筹案例或收款人信息ID）
     * @param photoType 照片类型（0-默认患者，100-收款人）
     * @return int 删除成功返回影响的行数
     */
    @Override
    public int deletePhotoAiInfo(Integer parentId,int photoType) {
        // 调用DAO层方法删除指定案例和照片类型的AI识别信息
        return cfPhotoAiDao.deletePhotoAiInfo(parentId,photoType);
    }

    /**
     * AI照片审核主方法
     * 对手持身份证照片和身份证照片进行AI识别，包括人脸对比和OCR识别
     * 识别完成后保存结果并记录操作日志
     *
     * @param cfAIPhotoModel AI照片模型，包含待识别的照片路径信息
     * @param workOrderId 工单ID，用于记录操作日志
     * @param photoType 照片类型（0-默认患者照片，100-收款人照片）
     */
    @Override
//    @Async("aiCheckPhotosExecutor")  // 异步执行注解（已注释）
    public void aiCheckPhotos(CfAIPhotoModel cfAIPhotoModel, long workOrderId,int photoType) {
        // 获取案例ID（众筹案例ID或收款人信息ID）
        Integer caseId = cfAIPhotoModel.getParentId();

        // 参数校验：检查照片模型和照片路径是否为空
        if (cfAIPhotoModel == null || StringUtils.isBlank(cfAIPhotoModel.getIdCardPhoto()) || StringUtils.isBlank(cfAIPhotoModel.getOnlyIdCardPhoto())) {
            log.info("pic is null workOrderId={},cfAIPhotoModel={}",workOrderId,cfAIPhotoModel);
            return;
        }

        // 获取患者信息，用于后续的身份信息对比
        CrowdfundingAuthor crowdfundingAuthor = adminCrowdfundingAuthorBiz.get(caseId);
        String idCard = null;  // 患者身份证号码
        String name = null;    // 患者姓名

        if (crowdfundingAuthor != null) {
            try {
                // 尝试解密存储的身份证号码
                crowdfundingAuthor.setIdCard(shuidiCipher.decrypt(crowdfundingAuthor.getCryptoIdCard()));
            } catch (Exception e) {
                // 解密失败时使用原始加密数据
                crowdfundingAuthor.setIdCard(crowdfundingAuthor.getCryptoIdCard());
            }
            // 获取身份证号码和姓名用于后续对比
            idCard = crowdfundingAuthor.getIdCard();
            name = crowdfundingAuthor.getName();
        }
        // 将照片URL转换为原始URL格式，便于AI接口访问
        cfAIPhotoModel.setIdCardPhoto(adminImageService.convertSingleOriginUrl(cfAIPhotoModel.getIdCardPhoto()));
        cfAIPhotoModel.setOnlyIdCardPhoto(adminImageService.convertSingleOriginUrl(cfAIPhotoModel.getOnlyIdCardPhoto()));

        try {
            // 创建计时器，用于统计AI识别耗时
            Stopwatch stopwatch = Stopwatch.createStarted();

            // 调用AI识别方法，获取人脸对比和OCR识别结果
            PhotoAiInfoModel photoAiInfoModel = getPhotoAiInfo(cfAIPhotoModel);

            if (photoAiInfoModel != null) {
                // 删除该案例和照片类型的旧识别记录，避免重复数据
                deletePhotoAiInfo(photoAiInfoModel.getInfoId(),photoType);

                // 设置照片类型标识
                photoAiInfoModel.setPhotoType(photoType);

                // 保存新的AI识别结果到数据库
                savePhotoAiInfo(photoAiInfoModel);

                // 停止计时器
                stopwatch.stop();

                // 创建操作记录，记录AI识别的详细结果和对比信息
                commonOperationRecordClient.create()
                        .buildBizId(workOrderId)  // 关联工单ID
                        .buildActionType(OperationActionTypeEnum.OCR_APPROVE_CASE_INFO)  // 操作类型：OCR审核案例信息
                        .buildExt("caseId", String.valueOf(cfAIPhotoModel.getParentId()))  // 案例ID
                        .buildExt("status", String.valueOf(photoAiInfoModel.getStatus()))  // AI识别状态码
                        .buildExt("result", JSON.toJSONString(photoAiInfoModel))  // 完整的AI识别结果JSON
                        .buildExt("name", photoAiInfoModel.getName())  // 身份证OCR识别的姓名
                        .buildExt("idNumber", photoAiInfoModel.getIdNumber())  // 身份证OCR识别的身份证号
                        .buildExt("holdIdCardName", photoAiInfoModel.getHoldIdCardName())  // 手持身份证OCR识别的姓名
                        .buildExt("holdIdCardIdNumber", photoAiInfoModel.getHoldIdCardIdNumber())  // 手持身份证OCR识别的身份证号
                        // 以下字段记录AI识别结果与发起人信息的对比结果
                        .buildExt("nameEqual", String.valueOf(StringUtils.equals(photoAiInfoModel.getName(), name)))  // 身份证姓名是否匹配
                        .buildExt("idNumberEqual", String.valueOf(StringUtils.equals(photoAiInfoModel.getIdNumber(), idCard)))  // 身份证号是否匹配
                        .buildExt("holdIdCardNameEqual", String.valueOf(StringUtils.equals(photoAiInfoModel.getHoldIdCardName(), name)))  // 手持身份证姓名是否匹配
                        .buildExt("holdIdCardIdNumberEqual", String.valueOf(StringUtils.equals(photoAiInfoModel.getHoldIdCardIdNumber(), idCard)))  // 手持身份证号是否匹配
                        .save();  // 保存操作记录
            } else {
                // AI识别失败的情况处理
                log.warn("照片异常");
                // 若传入图片不符合规则或识别失败，删除该案例的旧识别记录
                // 确保数据库中不保留无效的识别结果
                deletePhotoAiInfo(cfAIPhotoModel.getParentId(),photoType);
            }
        } catch (Exception e) {
            // 捕获并记录AI照片审核过程中的所有异常
            log.error("AI照片审核异常", e);
        }
    }

}
