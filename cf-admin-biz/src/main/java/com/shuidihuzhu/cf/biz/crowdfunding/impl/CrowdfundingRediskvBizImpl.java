package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingRediskvBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingRediskvDao;

/**
 * Created by ahrievil on 2017/1/20.
 */
@Service
public class CrowdfundingRediskvBizImpl implements CrowdfundingRediskvBiz {

    @Autowired
    private CrowdfundingRediskvDao crowdfundingRediskvDao;

    public String selectMailRecipient(String k) {
        return crowdfundingRediskvDao.selectMailRecipient(k);
    }

    @Override
    public int addOne(String k, String v) {
        return crowdfundingRediskvDao.addOne(k, v);
    }

}
