package com.shuidihuzhu.cf.biz.stat.impl;

import com.shuidihuzhu.cf.biz.stat.AdminCfStatUserWideBiz;
import com.shuidihuzhu.cf.dao.stat.AdminCfStatUserWideDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfStatUserWide;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by ahrievil on 2017/7/3.
 */
@Service
public class AdminCfStatUserWideBizImpl implements AdminCfStatUserWideBiz {

    @Autowired
    private AdminCfStatUserWideDao adminCfStatUserWideDao;

    @Override
    public List<CfStatUserWide> selectUserIdByChannelGroup(String channelGroup, int isFollow, int start, int size) {
        return adminCfStatUserWideDao.selectUserIdByChannelGroup(channelGroup, isFollow, start, size);
    }
}
