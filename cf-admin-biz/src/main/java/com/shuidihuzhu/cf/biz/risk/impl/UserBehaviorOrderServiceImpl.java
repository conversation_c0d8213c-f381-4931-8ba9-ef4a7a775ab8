package com.shuidihuzhu.cf.biz.risk.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingCommentBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.risk.IUserBehaviorService;
import com.shuidihuzhu.cf.client.feign.CfPlatformEsFeignClient;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.feign.CrowdfundingOrderFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.UserBehaviorEnum;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @time 2019/10/11 下午7:51
 * @desc 捐款行为
 */
@Service
public class UserBehaviorOrderServiceImpl implements IUserBehaviorService {

    @Autowired
    private CfPlatformEsFeignClient platformEsFeignClient;

    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;

    @Autowired
    private CrowdfundingOrderFeignClient crowdfundingOrderFeignClient;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;

    @Autowired
    private AdminCrowdfundingCommentBiz cfCommentBiz;

    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Override
    public List<AdminUserBehaviorDetail> buildBehaviorDetail(String mobile, long userId, String name, String identity, UserInfoModel userInfo, UserBehaviorEnum behaviorEnum) {
        FeignResponse<List<CrowdfundingOrder>> orderRes = crowdfundingOrderFeignClient.findCrowdfundingOrder(userId, "", "");
        if(orderRes.notOk() || CollectionUtils.isEmpty(orderRes.getData())){
            return Lists.newArrayList();
        }

        UserInfoDetail userInfoDetail = new UserInfoDetail();
        userInfoDetail.setUserId(userId);
        userInfoDetail.setNickName(Objects.nonNull(userInfo) ? userInfo.getNickname() : "");

        List<CrowdfundingOrder> crowdfundingOrders = orderRes.getData();
        if (crowdfundingOrders.size() > 1000) {
            crowdfundingOrders = crowdfundingOrders.subList(0, 1000);
        }
        List<Integer> caseIds = crowdfundingOrders.stream().map(CrowdfundingOrder::getCrowdfundingId).collect(Collectors.toList());
        List<CrowdfundingInfo> crowdfundingInfoList = adminCrowdfundingInfoBiz.selectByCaseIdList(caseIds);
        if (CollectionUtils.isEmpty(crowdfundingInfoList)) {
            return Lists.newArrayList();
        }
        Map<Integer, CrowdfundingInfo> crowdfundingMap = crowdfundingInfoList.stream()
                .collect(Collectors.toMap(
                        CrowdfundingInfo::getId,
                        info -> info,
                        (existing, replacement) -> replacement
                ));

        List<AdminUserBehaviorDetail> orderDetails = Lists.newArrayList();

        for (CrowdfundingOrder order : orderRes.getData()){
            int caseId = order.getCrowdfundingId();
            CrowdfundingInfo caseInfo = crowdfundingMap.get(caseId);
            if (order.getValid() != 1 || order.getPayStatus() != 1 || Objects.isNull(caseInfo)) {
                continue;
            }

            StringBuilder sb = new StringBuilder();
            sb.append("订单id:").append(order.getId()).append(REDEX);
            sb.append("支持金额:").append(order.getAmount() / 100).append("元").append(REDEX);
            sb.append("支付过？:").append((order.getPayStatus() != null &&order.getPayStatus() == 1)?"是":"否").append(REDEX);
            sb.append("案例id:").append(caseId).append(REDEX);
            sb.append("案例标题:").append(Objects.nonNull(caseInfo) ? caseInfo.getTitle() : "").append(REDEX);
            sb.append("订单留言:").append(order.getComment()).append(REDEX);

            AdminUserBehaviorDetail orderDetail = new AdminUserBehaviorDetail();
            orderDetail.setTime(order.getCtime());
            orderDetail.setBehaviorType(UserBehaviorEnum.ORDER.getKey());
            orderDetail.setUserInfoDetail(userInfoDetail);
            orderDetail.setUrl(Lists.newArrayList());
            orderDetail.setBehavoir(Lists.newArrayList(sb.toString().split(REDEX)));

            orderDetails.add(orderDetail);
        }
        return orderDetails;
    }

    public List<UserSubBehavoirDetail> getOrderComment(long orderId) {
        List<CrowdfundingCommentVo> comments = cfCommentBiz.getCommentByParentIdFromTiDb(orderId, 2000);
        return subCommentDetail(comments);
    }

    @Override
    public UserBehaviorEnum getBehavior() {
        return UserBehaviorEnum.ORDER;
    }

    private List<UserSubBehavoirDetail> subCommentDetail(List<CrowdfundingCommentVo> comments){
        if(CollectionUtils.isEmpty(comments)){
            return Lists.newArrayList();
        }

        List<UserSubBehavoirDetail> subComments = Lists.newArrayList();

        for (CrowdfundingCommentVo comment : comments){

            UserInfoModel userInfo = userInfoServiceBiz.getUserInfoByUserId(comment.getUserId());

            UserSubBehavoirDetail detail = new UserSubBehavoirDetail();
            detail.setUserId(comment.getUserId());
            detail.setNickName(Objects.nonNull(userInfo) ? userInfo.getNickname() : "");
            detail.setTime(comment.getCreateTime());
            detail.setSubDetail(comment.getContent());

            subComments.add(detail);
        }

        return subComments;
    }
}
