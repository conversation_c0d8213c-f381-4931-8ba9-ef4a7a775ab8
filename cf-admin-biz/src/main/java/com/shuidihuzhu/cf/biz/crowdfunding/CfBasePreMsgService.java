package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfBasePreMsg;
import com.shuidihuzhu.cf.model.crowdfunding.CfBasePreMsgRecord;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.param.PageResult;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2019/10/14
 */
public interface CfBasePreMsgService {

    CfBasePreMsg getMsg(String mobile);

    int updateMsgStatus(String mobile,int status,int caseId,String uuid);

    List<CfBasePreMsgRecord> getRecords(long msgId);

    OpResult<List<CfBasePreMsg>> get1V1MsgList(String mobile, String startTime, String endTime);
}
