package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.shuidihuzhu.cf.biz.crowdfunding.CfAndroidChannelBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfAndroidChannelDao;
import com.shuidihuzhu.cf.model.admin.CfAndroidChannel;

@Service
public class CfAndroidChannelBizImpl implements CfAndroidChannelBiz {

    @Autowired
    private CfAndroidChannelDao cfAndroidChannelDao;

    @Override
    public int insertList(List<CfAndroidChannel> list) {
        return cfAndroidChannelDao.insertList(list);
    }

    @Override
    public CfAndroidChannel selectOne(String selectDate, String channelName) {
        return cfAndroidChannelDao.selectOne(selectDate, channelName);
    }

    @Override
    public int deleteData() {
        return cfAndroidChannelDao.deleteData();
    }

}
