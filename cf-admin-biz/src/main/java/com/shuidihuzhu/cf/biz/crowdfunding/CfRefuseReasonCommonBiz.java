package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonTag;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;

public interface CfRefuseReasonCommonBiz {

    void validateCanRelationId(CfRefuseReasonEntity sourceEntity, CfRefuseReasonEntity relationEntity);

    void validateRelationIds(CfRefuseReasonEntity entity, String newRelationIds);

    void validateUseScene(CfRefuseReasonEntity reasonEntity, String useScene);

    void validateEditEntityDeleteStatus(CfRefuseReasonEntity entity, CfRefuseReasonTag reasonTag, int deleteStatus);

    List<Integer> getIdListSplitterByComma(String concatByCommaIds);

    void addOperateEntityLog(int userId, int entityId, String action);

    List<CfRefuseReasonEntity.CfRefuseReasonOperateLog> queryReasonLogByEntityId(int entityId);

    List<CfRefuseReasonEntity.RejectOptionObject> queryAllRejectScene(int dataType);

    String getNewReasonEntityIds(CfRefuseReasonTag reasonTag, int entityId, int deleteStatus);


}
