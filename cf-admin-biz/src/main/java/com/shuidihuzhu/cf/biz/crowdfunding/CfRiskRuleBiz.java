package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.client.adminpure.model.rule.EconomyModel;
import com.shuidihuzhu.cf.enums.rule.RiskRuleResult;
import com.shuidihuzhu.cf.model.admin.rule.RiskRuleModel;
import com.shuidihuzhu.cf.response.OpResult;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE 2020/1/2
 */
public interface CfRiskRuleBiz {

    OpResult<Set<RiskRuleResult>> judge(EconomyModel models);

    List<String> assembleTipInfo(EconomyModel model);

}
