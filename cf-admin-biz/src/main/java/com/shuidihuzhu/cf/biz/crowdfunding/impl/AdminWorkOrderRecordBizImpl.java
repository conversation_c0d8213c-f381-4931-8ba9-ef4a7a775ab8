package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.admin.AdminWorkOrderRecordBiz;
import com.shuidihuzhu.cf.dao.admin.AdminWorkOrderRecordDao;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrderRecord;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * Created by Ahrievil on 2017/11/30
 */
@Service
public class AdminWorkOrderRecordBizImpl implements AdminWorkOrderRecordBiz {

    @Autowired
    private AdminWorkOrderRecordDao adminWorkOrderRecordDao;

    @Override
    public int insertOne(AdminWorkOrderRecord adminWorkOrderRecord) {
        return adminWorkOrderRecordDao.insertOne(adminWorkOrderRecord);
    }

    private static int LIMIT = 200;

    @Override
    public int insertOne(AdminWorkOrder adminWorkOrder) {
        AdminWorkOrderRecord adminWorkOrderRecord = new AdminWorkOrderRecord(adminWorkOrder);
        return adminWorkOrderRecordDao.insertOne(adminWorkOrderRecord);
    }

    @Override
    public int insertList(List<AdminWorkOrderRecord> list) {
        if (CollectionUtils.isEmpty(list)) return 0;
        return adminWorkOrderRecordDao.insertList(list);
    }

    @Override
    public List<AdminWorkOrderRecord> selectByWorkIdAndOperateTypes(List<Long> workOrderIds, List<Integer> operateTypes) {

        if (CollectionUtils.isEmpty(workOrderIds)) {
            return Lists.newArrayList();
        }

        List<List<Long>> partitionWorkOrderIds = Lists.partition(workOrderIds, 500);

        List<AdminWorkOrderRecord> result = Lists.newArrayList();
        for (List<Long> curWorkOrderIds : partitionWorkOrderIds) {
            result.addAll(adminWorkOrderRecordDao.selectByWorkIdAndOperateTypes(curWorkOrderIds, operateTypes));
        }

        return result;
    }



    @Override
    public int deleteFirstUgcById(long id) {
        return adminWorkOrderRecordDao.deleteFirstUgcById(id);
    }


}
