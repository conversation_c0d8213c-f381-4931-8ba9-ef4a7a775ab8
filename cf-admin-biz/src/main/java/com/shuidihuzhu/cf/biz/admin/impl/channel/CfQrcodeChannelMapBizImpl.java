package com.shuidihuzhu.cf.biz.admin.impl.channel;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.admin.channel.CfChannelBiz;
import com.shuidihuzhu.cf.biz.admin.channel.CfChannelGroupBiz;
import com.shuidihuzhu.cf.biz.admin.channel.CfQrcodeChannelMapBiz;
import com.shuidihuzhu.cf.dao.sd.admin.channel.CfQrcodeChannelMapDao;
import com.shuidihuzhu.cf.model.admin.channel.CfChannel;
import com.shuidihuzhu.cf.model.admin.channel.CfQrcodeChannel;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Created by wangsf on 17/2/16.
 */
@Service
public class CfQrcodeChannelMapBizImpl implements CfQrcodeChannelMapBiz{

	@Autowired
	private CfQrcodeChannelMapDao cfQrcodeChannelMapDao;

	@Autowired
	private CfChannelGroupBiz cfChannelGroupBiz;

	@Autowired
	private CfChannelBiz cfChannelBiz;

	@Override
	public int add(CfQrcodeChannel qrcodeChannel) {
		if(qrcodeChannel == null) {
			return -1;
		}

		return this.cfQrcodeChannelMapDao.add(qrcodeChannel);
	}

	@Override
	public List<CfQrcodeChannel> listByQrcodeIds(List<Integer> ids) {
		if(CollectionUtils.isEmpty(ids)) {
			return Collections.EMPTY_LIST;
		}

		return this.cfQrcodeChannelMapDao.listGroupIdsByQrcodeIds(ids);
	}

	@Override
	public Map<Integer, CfQrcodeChannel> getMapByIds(List<Integer> ids) {
		List<CfQrcodeChannel> qrcodeChannels = this.listByQrcodeIds(ids);
		if(CollectionUtils.isEmpty(qrcodeChannels)) {
			return Maps.newHashMap();
		}

		Map<Integer, CfQrcodeChannel> map = Maps.newHashMap();
		for(CfQrcodeChannel qrcodeChannel : qrcodeChannels) {
			map.put(qrcodeChannel.getQrcodeId(), qrcodeChannel);
		}

		return map;
	}

	@Override
	public int update(int qrcodeId, int channelId) {

		if(qrcodeId < 0 || channelId < 0) {
			return -1;
		}

		CfChannel channel = this.cfChannelBiz.get(channelId);
		if(channel == null) {
			return -1;
		}

		int groupId = channel.getGroupId();
		return this.cfQrcodeChannelMapDao.update(qrcodeId, channelId, groupId);
	}

	@Override
	public CfQrcodeChannel getByQrcodeId(int qrcodeId) {

		if(qrcodeId <= 0) {
			return null;
		}

		return this.cfQrcodeChannelMapDao.getByQrcodeId(qrcodeId);
	}
}
