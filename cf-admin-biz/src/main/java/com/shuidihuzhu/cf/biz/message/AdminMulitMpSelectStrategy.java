package com.shuidihuzhu.cf.biz.message;

import com.shuidihuzhu.common.web.model.message.HasMulitMpUserInfo;
import com.shuidihuzhu.wx.model.WxMpConfig;

import java.util.List;

/**
 * Author: <PERSON>
 * Date: 2017/9/22 14:24
 */
public interface AdminMulitMpSelectStrategy {

	/**
	 * 选择给定的WxMpConfigs, 如果找到则填充APPINFO以及OpenId
	 *
	 * @param hasMulitMpUserInfo
	 * @param wxAppInfos
	 * @return 是否找到
	 */
	@Deprecated
	boolean selectThenFill(HasMulitMpUserInfo hasMulitMpUserInfo, List<WxMpConfig> wxAppInfos);

	/**
	 * 批量版本
	 * @param hasMulitMpUserInfos
	 * @param wxAppInfos
	 * @return
	 */
	List<Boolean> selectThenFill(List<HasMulitMpUserInfo> hasMulitMpUserInfos, List<WxMpConfig> wxAppInfos);

}
