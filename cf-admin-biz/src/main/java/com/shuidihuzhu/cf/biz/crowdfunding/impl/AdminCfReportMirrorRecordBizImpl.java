package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfReportMirrorRecordBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfReportMirrorRecordDao;
import com.shuidihuzhu.cf.enums.crowdfunding.CfOperatingRecordEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportMirrorRecord;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * Created by niejiangnan on 2017/10/20.
 */
@Service
public class AdminCfReportMirrorRecordBizImpl implements AdminCfReportMirrorRecordBiz {

    @Autowired
    private AdminCfReportMirrorRecordDao adminCfReportMirrorRecordDao;

    @Override
    public List<CfReportMirrorRecord> getByInfoUuid(String infoUuid, CfOperatingRecordEnum.Role role) {
        if (StringUtils.isBlank(infoUuid)) {
            return Collections.emptyList();
        }
        return adminCfReportMirrorRecordDao.getByInfoUuidAndType(infoUuid, role.getCode());
    }
}
