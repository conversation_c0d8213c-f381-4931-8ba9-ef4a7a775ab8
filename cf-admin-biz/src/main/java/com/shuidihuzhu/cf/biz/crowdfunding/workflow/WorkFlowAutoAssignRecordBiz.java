package com.shuidihuzhu.cf.biz.crowdfunding.workflow;

import com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowTypeProperty;

import java.util.List;
import java.util.Map;

public interface WorkFlowAutoAssignRecordBiz {

    void assignWorkTOrg(int orgId, Integer sourceOperatorId);

    void assignWorkTUser(int userId);

    boolean canManualAssignOrder(List<Integer> orgIds);

    Map<Integer, Integer> getNoHandleOrgMapping();

    void addOrgNoHandleLimit(int lowestId, int limit);

    WorkFlowTypeProperty.FlowTypePropertyEntity selectPropertyByOrgId(int orgId);


    /**
     * 自动分配举报组信息传递工单
     */
    void triggerAutoAssignReportOrderWithLock();

    void onWorkOrderAssign(long workOrderId);
}
