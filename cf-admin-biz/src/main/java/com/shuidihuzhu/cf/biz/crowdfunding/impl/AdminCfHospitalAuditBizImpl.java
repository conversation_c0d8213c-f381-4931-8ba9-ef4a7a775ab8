package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Lists;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfHospitalAuditBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.IAdminCredibleInfoService;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfHospitalAuditDao;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfHospitalAuditInfoTelDao;
import com.shuidihuzhu.cf.enums.crowdfunding.CredibleTypeEnum;
import com.shuidihuzhu.cf.model.admin.CfHospitalAuditInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CfHospitalAuditInfo;

import com.shuidihuzhu.cf.model.crowdfunding.CfHospitalAuditInfoTel;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

/**
 * Created by niejiangnan on 2017/11/30.
 */
@Service
@Slf4j
public class AdminCfHospitalAuditBizImpl implements AdminCfHospitalAuditBiz {

    @Autowired
    private AdminCfHospitalAuditDao cfHospitalAuditDao;
    @Autowired
    private IAdminCredibleInfoService adminCredibleInfoService;
    @Autowired
    private AdminCfHospitalAuditInfoTelDao adminCfHospitalAuditInfoTelDao;

    @Override
    public int save(CfHospitalAuditInfo cfHospitalAuditInfo) {
        if (cfHospitalAuditInfo == null) {
            return 0;
        }
        cfHospitalAuditInfo.setHospitalName(StringUtils.trimToEmpty(cfHospitalAuditInfo.getHospitalName()));
        cfHospitalAuditInfo.setDepartment(StringUtils.trimToEmpty(cfHospitalAuditInfo.getDepartment()));
        cfHospitalAuditInfo.setFloorNumber(StringUtils.trimToEmpty(cfHospitalAuditInfo.getFloorNumber()));
        cfHospitalAuditInfo.setBedNumber(StringUtils.trimToEmpty(cfHospitalAuditInfo.getBedNumber()));
        cfHospitalAuditInfo.setHospitalizationNumber(
                StringUtils.trimToEmpty(cfHospitalAuditInfo.getHospitalizationNumber()));
        cfHospitalAuditInfo.setDoctorName(StringUtils.trimToEmpty(cfHospitalAuditInfo.getDoctorName()));
        cfHospitalAuditInfo.setDepartmentTelNumber(
                StringUtils.trimToEmpty(cfHospitalAuditInfo.getDepartmentTelNumber()));
        cfHospitalAuditInfo.setOperatorContent(
                StringUtils.trimToEmpty(cfHospitalAuditInfo.getOperatorContent()));
        cfHospitalAuditInfo.setType(cfHospitalAuditInfo.getType());

        cfHospitalAuditInfo.setReason(StringUtils.trimToEmpty(cfHospitalAuditInfo.getReason()));
        cfHospitalAuditInfo.setReasonSupply(StringUtils.trimToEmpty(cfHospitalAuditInfo.getReasonSupply()));
        cfHospitalAuditInfo.setEasyToVerifyTime(StringUtils.trimToEmpty(cfHospitalAuditInfo.getEasyToVerifyTime()));
        cfHospitalAuditInfo.setOperatorOrg(StringUtils.trimToEmpty(cfHospitalAuditInfo.getOperatorOrg()));

        return cfHospitalAuditDao.save(cfHospitalAuditInfo);
    }

    @Override
    public CfHospitalAuditInfoExt getByInfoUuid(String infoUuid) {
        if (StringUtils.isBlank(infoUuid)) {
            return null;
        }
        return cfHospitalAuditDao.getByInfoUuid(infoUuid);
    }

    @Override
    public CfHospitalAuditInfoExt getById(long id) {
        return cfHospitalAuditDao.getById(id);
    }

    @Override
    public List<CfHospitalAuditInfoExt> getByInfoUuids(List<String> infoUuids) {
        return cfHospitalAuditDao.getByInfoUuids(infoUuids);
    }

    @Override
    public int update(CfHospitalAuditInfo cfHospitalAuditInfo) {
        if (cfHospitalAuditInfo == null) {
            return 0;
        }
        //如果是来自新举报详情页的医院核实，会修改可信信息的状态
        adminCredibleInfoService.updateAuditInfo(cfHospitalAuditInfo.getId(), cfHospitalAuditInfo.getAuditStatus(), CredibleTypeEnum.HOSPITAL_CHECK.getKey());
        return cfHospitalAuditDao.update(cfHospitalAuditInfo);
    }

    @Override
    public int delete(CfHospitalAuditInfo cfHospitalAuditInfo) {
        if (cfHospitalAuditInfo == null) {
            return 0;
        }
        return  cfHospitalAuditDao.delete(cfHospitalAuditInfo);
    }

    @Override
    public List<String> getNotFinish() {
        return cfHospitalAuditDao.getNotFinish();
    }

    @Override
    public  int countHospitalAuditByStatus(int auditStatus) {
        return  cfHospitalAuditDao.countHospitalAuditByStatus(auditStatus);
    }

    @Override
    public List<CfHospitalAuditInfoTel> getByCfHospitalAuditInfoId(long cfHospitalAuditInfoId) {
        if(cfHospitalAuditInfoId <= 0) {
            return Collections.emptyList();
        }
        return this.adminCfHospitalAuditInfoTelDao.getByCfHospitalAuditInfoId(cfHospitalAuditInfoId);
    }
}
