package com.shuidihuzhu.cf.biz.customer;

import com.shuidihuzhu.cf.customer.CfChatRecordDO;
import com.shuidihuzhu.cf.customer.CfUdeskSessionRecordVo;
import com.shuidihuzhu.cf.model.CrmUserManage.UserManage;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface CfUdeskSessionRecordBiz {
    List<CfUdeskSessionRecordVo> getUdeskSessionsByUdeskUserIds(Set<Integer> udeskUserIds);

    List<CfChatRecordDO> listRecordByUdskIds(Set<Integer> udeskUserIds);

    String getNickNameBySessionId(int sessionId);

    int selectByCustomIdAndCreateAt(List<Integer> customIds, Date createdAt);

    UserManage.UdeskBizRecord selectLastRecordByCustomId(List<Integer> customIds);
}
