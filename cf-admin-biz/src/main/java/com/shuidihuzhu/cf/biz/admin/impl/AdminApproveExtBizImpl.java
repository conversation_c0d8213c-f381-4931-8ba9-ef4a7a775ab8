package com.shuidihuzhu.cf.biz.admin.impl;

import com.shuidihuzhu.cf.biz.admin.AdminApproveExtBiz;
import com.shuidihuzhu.cf.dao.approve.AdminApproveExtDAO;
import com.shuidihuzhu.cf.domain.approve.AdminApproveExt;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: wangpeng
 * @Date: 2021/7/1 16:29
 * @Description:
 */
@Service
public class AdminApproveExtBizImpl implements AdminApproveExtBiz {
    @Autowired
    private AdminApproveExtDAO adminApproveExtDAO;

    @Override
    public int insert(AdminApproveExt adminApproveExt) {
        return adminApproveExtDAO.insert(adminApproveExt);
    }

    @Override
    public List<AdminApproveExt> listByApproveIdAndExtName(int approveId, String extName) {
        return adminApproveExtDAO.listByApproveIdAndExtName(approveId, extName);
    }

    @Override
    public int updateExtValue(long id, String extValue) {
        return adminApproveExtDAO.updateExtValue(id, extValue);
    }

    @Override
    public Map<Integer, AdminApproveExt> getApproveExtList(List<Integer> approveIds, String extName) {
        List<AdminApproveExt> approveExtList = adminApproveExtDAO.getApproveExtList(approveIds, extName);
        if (CollectionUtils.isEmpty(approveExtList)) {
            return new HashMap<>();
        }
        return approveExtList.stream()
                .collect(Collectors.toMap(AdminApproveExt::getApproveId, Function.identity(), (o, x) -> o));
    }
}
