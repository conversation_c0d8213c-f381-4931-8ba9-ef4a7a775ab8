package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingCommentBiz;
import com.shuidihuzhu.cf.client.feign.CfCommonFeignClient;
import com.shuidihuzhu.cf.dao.crowdfunding.comment.CrowdfundingCommentShareDao;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingUserDelegate;
import com.shuidihuzhu.cf.delegate.ugc.IUgcDelegate;
import com.shuidihuzhu.cf.domain.risk.RiskUgcVerifyDO;
import com.shuidihuzhu.cf.enums.crowdfunding.CfSensitiveWordRecordEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingCommentType;
import com.shuidihuzhu.cf.enums.risk.UgcTypeEnum;
import com.shuidihuzhu.cf.model.UserAccountView;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingComment;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCommentView;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCommentVo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingUser;
import com.shuidihuzhu.cf.repository.AdminCrowdfundingCommentRepository;
import com.shuidihuzhu.cf.service.crowdfunding.CfRiskService;
import com.shuidihuzhu.cf.vo.CrowdfundingUserVo;
import com.shuidihuzhu.cf.vo.crowdfunding.CfCommentVo;
import com.shuidihuzhu.client.cf.risk.model.enums.CfRiskBlackListEnum;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.util.admin.BasicExample;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingCommentType.CROWDFUNDING_TRENDS;

/**
 * Created by ahrievil on 2017/5/6.
 */
@Service
public class AdminCrowdfundingCommentBizImpl implements AdminCrowdfundingCommentBiz {
    @Autowired
    private ICrowdfundingUserDelegate crowdfundingUserDelegate;
    @Autowired
    private IUgcDelegate ugcDelegate;
    @Autowired
    private CfCommonFeignClient cfCommonFeignClient;
    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;
    @Autowired
    private CfRiskService cfRiskService;

    @Autowired
    private CrowdfundingCommentShareDao crowdfundingCommentShareDao;

    @Autowired
    private AdminCrowdfundingCommentRepository adminCrowdfundingCommentRepository;

    @Override
    public List<CrowdfundingComment> getByPage(BasicExample basicExample, int current, int pageSize) {
        PageHelper.startPage(current, pageSize);
        return adminCrowdfundingCommentRepository.getByPage(basicExample);
    }

    @Override
    public List<CfCommentVo> getCrowdfundingCommentVo(List<CrowdfundingComment> crowdfundingComments, CrowdfundingCommentType commentType) {
        List<CrowdfundingUser> crowdfundingUsers = Lists.newArrayList();
        for (CrowdfundingComment comment : crowdfundingComments) {
            CrowdfundingUser crowdfundingUser = new CrowdfundingUser();
            crowdfundingUser.setUserId(comment.getUserId());
            crowdfundingUser.setUserThirdId(comment.getUserThirdId());
            crowdfundingUsers.add(crowdfundingUser);
        }
        CrowdfundingUserVo userInfo = crowdfundingUserDelegate.getUserInfo(crowdfundingUsers);
        Map<Long, CrowdfundingUser> userIdMap = Maps.newHashMap();
        if (userInfo != null) {
            userIdMap = userInfo.getUserIdMap();
        }
        List<CfCommentVo> crowdfundingCommentVos = Lists.newArrayList();
        for (CrowdfundingComment crowdfundingComment : crowdfundingComments) {

            RiskUgcVerifyDO verifyDO = cfCommonFeignClient.queryUgcVerify(crowdfundingComment.getCrowdfundingId(), UgcTypeEnum.COMMENT.getValue(), crowdfundingComment.getId()).getData();
            boolean isBlackList = cfRiskService.queryBlackValid(crowdfundingComment.getUserId(), CfRiskBlackListEnum.LimitType.UGC);
            boolean seeOnlySelf = Objects.nonNull(verifyDO) || isBlackList;

            CfSensitiveWordRecordEnum.BizType bizType = commentType == CrowdfundingCommentType.CONTRIBUTE_RECORD ? CfSensitiveWordRecordEnum.BizType.COMMENT_ORDER : CfSensitiveWordRecordEnum.BizType.COMMENT_PROGRESS;
            List<WorkOrderExt> workOrderExts = cfWorkOrderClient.queryWorkOrderByCaseAndType(crowdfundingComment.getCrowdfundingId(), WorkOrderType.ugcpinglun.getType(), String.valueOf(crowdfundingComment.getId()), String.valueOf(bizType.value())).getData();
            long workOrderId = org.apache.commons.collections4.CollectionUtils.isNotEmpty(workOrderExts) ? workOrderExts.get(0).getWorkOrderId() : 0;

            CfCommentVo crowdfundingCommentVo = new CfCommentVo();
            BeanUtils.copyProperties(crowdfundingComment, crowdfundingCommentVo);
            CrowdfundingUser crowdfundingUser = userIdMap.get(crowdfundingComment.getUserId());
            crowdfundingCommentVo.setCrowdfundingUser(crowdfundingUser != null ? crowdfundingUser : new CrowdfundingUser());
            crowdfundingCommentVo.setStatus(seeOnlySelf ? 1 : 0);
            crowdfundingCommentVo.setWorkOrderId(workOrderId);

            crowdfundingCommentVos.add(crowdfundingCommentVo);
        }
        return crowdfundingCommentVos;
    }


    @Override
    public Map<Long, List<CrowdfundingCommentView>> getCrowdfundingTrendsCommentsByParentIdList(List<Long> parentIdList,
                                                                                                   Integer offset, Integer limit) {
        return getCommentsByParentIdList(parentIdList, offset, limit, CROWDFUNDING_TRENDS);
    }


    private Map<Long, List<CrowdfundingCommentView>> getCommentsByParentIdList(List<Long> parentIdList, Integer offset,
                                                                                  Integer limit, CrowdfundingCommentType type) {
        Map<Long, List<CrowdfundingCommentView>> commentViewsMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(parentIdList)) {
            return commentViewsMap;
        }
        List<CrowdfundingComment> comments =
                ugcDelegate.getByPageNoCareCommentIdList(parentIdList, offset, limit, type.value());
        List<CrowdfundingCommentView> commentViews = getCrowdfundingCommentViews(comments);
        for (CrowdfundingCommentView commentView : commentViews) {
            List<CrowdfundingCommentView> list = commentViewsMap.get(commentView.getParentId());
            if (list == null) {
                list = Lists.newArrayList();
                commentViewsMap.put(commentView.getParentId(), list);
            }
            list.add(commentView);
        }
        return commentViewsMap;
    }

    private List<CrowdfundingCommentView> getCrowdfundingCommentViews(List<CrowdfundingComment> comments) {
        if (CollectionUtils.isEmpty(comments) || comments.size() > 10000) {
            return Collections.emptyList();
        }
        List<CrowdfundingUser> crowdfundingUserList = Lists.newArrayList();
        List<Long> subCommentIdList = Lists.newArrayList();
        for (CrowdfundingComment comment : comments) {
            CrowdfundingUser crowdfundingUser = new CrowdfundingUser();
            crowdfundingUser.setUserId(comment.getUserId());
            crowdfundingUser.setUserThirdId(comment.getUserThirdId());
            crowdfundingUserList.add(crowdfundingUser);
            if (comment.getCommentId() != null && comment.getCommentId().intValue() > 0) {
                subCommentIdList.add(comment.getCommentId());
            }
        }
        Map<Long, CrowdfundingComment> subCrowdfundingCommentMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(subCommentIdList)) {
            List<CrowdfundingComment> subCrowdfundingCommentList = this.ugcDelegate.getListByIdNoCareDeleted(subCommentIdList);
            for (CrowdfundingComment comment : subCrowdfundingCommentList) {
                CrowdfundingUser crowdfundingUser = new CrowdfundingUser();
                crowdfundingUser.setUserId(comment.getUserId());
                crowdfundingUser.setUserThirdId(comment.getUserThirdId());
                crowdfundingUserList.add(crowdfundingUser);
                subCrowdfundingCommentMap.put(comment.getId(), comment);
            }
        }
        CrowdfundingUserVo crowdfundingUserVo = this.crowdfundingUserDelegate.getUserInfo(crowdfundingUserList);
        List<CrowdfundingCommentView> commentViews = Lists.newArrayList();
        for (CrowdfundingComment comment : comments) {
            CrowdfundingCommentView commentView = new CrowdfundingCommentView();
            UserAccountView userInfo = getUserInfoByCrowdfundingUserVo(comment, crowdfundingUserVo);
            UserAccountView commentUv = null;
            if (comment.getCommentId() != null && comment.getCommentId().intValue() > 0) {
                CrowdfundingComment subComment = subCrowdfundingCommentMap.get(comment.getCommentId());
                commentUv = getUserInfoByCrowdfundingUserVo(subComment, crowdfundingUserVo);
            }
            commentView.setUser(userInfo);
            commentView.setCommentedUser(commentUv);
            commentView.setId(comment.getId());
            commentView.setCrowdfundingId(Math.toIntExact(comment.getParentId()));
            commentView.setContent(comment.getContent());
            commentView.setCreateTime(comment.getCreateTime());
            commentView.setType(comment.getType());
            commentView.setParentId(comment.getParentId());
            commentViews.add(commentView);
        }
        return commentViews;
    }

    private UserAccountView getUserInfoByCrowdfundingUserVo(CrowdfundingComment comment,
                                                            CrowdfundingUserVo crowdfundingUserVo) {
        UserAccountView userAccountView = new UserAccountView();
        Long userId = comment.getUserId();
        Integer userThirdId = comment.getUserThirdId();
        CrowdfundingUser crowdfundingUser = null;
        if (userId != null && userId > 0) {
            crowdfundingUser = crowdfundingUserVo.getUserIdMap().get(userId);
        }
        if (crowdfundingUser != null && userThirdId != null && userThirdId > 0) {
            crowdfundingUser = crowdfundingUserVo.getUserThirdIdMap().get(userThirdId);
        }
        if (crowdfundingUser != null) {
            userAccountView.setUserId(crowdfundingUser.getUserId());
            userAccountView.setNickname(crowdfundingUser.getNickname());
            userAccountView.setHeadImgUrl(crowdfundingUser.getHeadImgUrl());
        }
        return userAccountView;
    }

    @Override
    public List<CrowdfundingCommentVo> getCommentByParentIdFromTiDb(long parentId, int limit) {

        return buildFromComment(adminCrowdfundingCommentRepository.getCommentByParentIdFromTiDb(parentId, limit));
    }

    @Override
    public List<CrowdfundingCommentVo> getCommentByUserIdAndTypeFromTiDb(long userId, int type, int limit) {
//        List<CrowdfundingComment> sourceComments = null;
//
//        if (type == CrowdfundingCommentType.CONTRIBUTE_RECORD.value()
//                || type == CrowdfundingCommentType.CROWDFUNDING_TRENDS.value()) {
//            sourceComments = adminCrowdfundingCommentDao.getCommentByUserIdAndTypeFromTiDb(userId, type, limit);
//        } else {
//            sourceComments = adminCrowdfundingCommentDao.getCommentByUserIdFromTiDb(userId, limit);
//        }
        if (userId == 0) {
            return Lists.newArrayList();
        }

        return buildFromComment(adminCrowdfundingCommentRepository.getCommentByUserIdAndTypeFromTiDb(userId, type, limit));
    }

    private List<CrowdfundingCommentVo> buildFromComment(List<CrowdfundingComment> allComments) {

        List<CrowdfundingCommentVo> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(allComments)) {
            return result;
        }

        for (CrowdfundingComment comment : allComments) {
            CrowdfundingCommentVo commentVo = new CrowdfundingCommentVo();
            BeanUtils.copyProperties(comment, commentVo);
            result.add(commentVo);
        }

        return result;
    }


    @Override
    public Integer selectCountByMin(Timestamp begin, Timestamp end) {
        return adminCrowdfundingCommentRepository.selectCountByMin(begin, end);
    }


    @Override
    public CrowdfundingComment getByIdNoCareDeleted(long id, long caseid) {
        return crowdfundingCommentShareDao.getByIdNoCareDeleted(caseid, id);
    }

    @Override
    public List<CrowdfundingComment> getCommentByParentId(int caseId, long parentId) {
        return crowdfundingCommentShareDao.getCommentByParentId(caseId,parentId);
    }
}
