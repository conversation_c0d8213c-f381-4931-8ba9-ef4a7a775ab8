package com.shuidihuzhu.cf.biz.risk.impl;

import com.google.common.collect.Maps;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfRepeatInfoBiz;
import com.shuidihuzhu.cf.biz.risk.ISimpleExportService;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.finance.impl.FinanceDelegate;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType;
import com.shuidihuzhu.cf.finance.model.drawcash.CfDrawCashDetail;
import com.shuidihuzhu.cf.finance.model.drawcash.CfDrawCashLaunchRecord;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCfRepeatInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoHospitalPayee;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackApiClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewBaseInfoDO;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

/**
 * Created by sven on 2019/6/21.
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class RaiseSimpleExportServiceImpl implements ISimpleExportService {

    @Resource
    private AdminCfInfoExtBiz adminCfInfoExtBiz;

    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Autowired
    private FinanceDelegate financeDelegate;

    @Resource
    private AdminCfRepeatInfoBiz adminCfRepeatInfoBiz;
    @Autowired
    private ShuidiCipher shuidiCipher;


    @Resource
    private CfClewtrackApiClient clewtrackApiClient;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;

    private final static String RAISE_CHANNEL = "发起渠道";

    private final static String RAISE_DEVICE = "发起设备信息";
    private final static String RAISE_IP_LOCATION = "案例发起ip地";
    private final static String RAISE_RECENT_IP_LOCATION = "发起人一周内常用访问IP地";
    private final static String DEVICE_CHANEL   = "终端渠道";
    private final static String PUSH_CHANNEL    = "登记渠道";
    private final static String REPEATE_CHANLE  = "是否重复筹款";
    private final static String AUTHOR_RAISE_TIMES     = "患者第几次筹款";
    private final static String RAISER_RAISE_TIMES     = "发起人第几次发起";
    private final static String RECIVE_TIMES           = "收款方第几次收款";

    @Override
    public Map<String, String> getDetail(CfInfoSimpleModel cfInfoSimpleModel) {

        String repeatIds= "";
        AdminCfRepeatInfo adminCfRepeatInfo = adminCfRepeatInfoBiz.selectByCaseId(cfInfoSimpleModel.getId());

        if(adminCfRepeatInfo != null){
            repeatIds = adminCfRepeatInfo.getRepeatInfoCaseIdSetExcludeMayRepeat().toString();
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingDelegate.getFundingInfoById(cfInfoSimpleModel.getId());
        CfInfoExt cfInfoExt = adminCfInfoExtBiz.getByCaseId(cfInfoSimpleModel.getId());
        List<CrowdfundingInfo> infos = crowdfundingDelegate.getCrowdfundingInfoByUserId(cfInfoSimpleModel.getUserId());

        String raiseChanelDesc = StringUtils.EMPTY;
        UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByUserId(crowdfundingInfo.getUserId());

        if(userInfoModel != null){
            Response<CfClewBaseInfoDO> response = clewtrackApiClient.getClewbaseByMobile(shuidiCipher.decrypt(userInfoModel.getCryptoMobile()));
            if(response != null && response.getCode() ==0 && response.getData() != null ){
                raiseChanelDesc = response.getData().getPrimaryChannel();
            }
        }

        Map<String, String> map = Maps.newHashMap();
        map.put(RAISE_CHANNEL, cfInfoExt.getPrimaryChannel());
        map.put(RAISE_DEVICE, UNKOWN);
        map.put(RAISE_IP_LOCATION, cfInfoExt.getClientIp());
        map.put(RAISE_RECENT_IP_LOCATION, UNKOWN);
        map.put(DEVICE_CHANEL, crowdfundingInfo.getChannel());
        map.put(PUSH_CHANNEL, raiseChanelDesc);
        map.put(AUTHOR_RAISE_TIMES, UNKOWN);
        map.put(REPEATE_CHANLE, repeatIds);
        map.put(RAISER_RAISE_TIMES, CollectionUtils.size(infos)+"");
        map.put(RECIVE_TIMES, buildReceiveTimes(cfInfoSimpleModel.getInfoId()));

        return map;
    }

    /**
     * 查询收款方收款次数
     * @param infoId
     * @return
     */
    private String buildReceiveTimes(String infoId) {
        CrowdfundingInfo cfInfo = crowdfundingDelegate.getCrowdfundingInfoByInfoId(infoId);
        String payeeBankCard = "";
        if (cfInfo.getRelationType() == CrowdfundingRelationType.LOCATION_HOSPITAL_ACCOUNT) {
            CrowdfundingInfoHospitalPayee hospitalPayee = crowdfundingDelegate.getCrowdfundingInfoHospitalPayeeByInfoUuid(infoId);
            if (hospitalPayee != null) {
                payeeBankCard = hospitalPayee.getHospitalBankCard();
            }
        } else {
            payeeBankCard = shuidiCipher.decrypt(cfInfo.getPayeeBankCard());
        }
        Response<List<CfDrawCashLaunchRecord>> launchRecordsResponse
                = financeDelegate.getLaunchRecordByBankCard(payeeBankCard);
        if (launchRecordsResponse.notOk()) {
            return "资金数据获取失败，请重试";
        }
        List<CfDrawCashLaunchRecord> launchRecords = launchRecordsResponse.getData();
        List<CfDrawCashLaunchRecord> successList = launchRecords.stream()
                .filter(p -> p.getStatus() == 2)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(successList)) {
            return "0";
        }
        List<Long> drawCashDetailIds = successList.stream().map(CfDrawCashLaunchRecord::getDrawCashDetailId)
                .collect(Collectors.toList());
        Response<List<CfDrawCashDetail>>  detailsResponse = financeDelegate.getDrawCashDetailsByIds(drawCashDetailIds);
        if (detailsResponse.notOk()) {
            return "资金数据获取失败，请重试";
        }
        List<CfDrawCashDetail>  details = detailsResponse.getData();
        return details.stream().map(CfDrawCashDetail::getDrawCashId).distinct().count() + "";
    }

    @Override
    public String getCategory() {
        return "发起";
    }

    @Override
    public int getOrder() {
        return 0;
    }
}
