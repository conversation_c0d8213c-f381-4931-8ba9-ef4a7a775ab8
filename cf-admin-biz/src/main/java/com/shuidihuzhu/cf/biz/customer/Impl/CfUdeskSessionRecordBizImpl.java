package com.shuidihuzhu.cf.biz.customer.Impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.customer.CfUdeskSessionRecordBiz;
import com.shuidihuzhu.cf.customer.CfChatRecordDO;
import com.shuidihuzhu.cf.customer.CfUdeskSessionRecordVo;
import com.shuidihuzhu.cf.dao.customer.CfUdeskSessionRecordDao;
import com.shuidihuzhu.cf.model.CrmUserManage.UserManage;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Set;

@Service
public class CfUdeskSessionRecordBizImpl implements CfUdeskSessionRecordBiz {
    @Autowired
    private CfUdeskSessionRecordDao cfUdeskSessionRecordDao;

    @Override
    public List<CfUdeskSessionRecordVo> getUdeskSessionsByUdeskUserIds(Set<Integer> udeskUserIds) {
        if (CollectionUtils.isEmpty(udeskUserIds)) {
            return Lists.newArrayList();
        }
        return cfUdeskSessionRecordDao.getUdeskSessionsByUdeskUserIds(udeskUserIds);
    }

    @Override
    public List<CfChatRecordDO> listRecordByUdskIds(Set<Integer> udeskUserIds) {
        if (CollectionUtils.isEmpty(udeskUserIds)) {
            return Lists.newArrayList();
        }
        return cfUdeskSessionRecordDao.listRecordByUdskIds(udeskUserIds);
    }

    @Override
    public String getNickNameBySessionId(int sessionId) {
        if (sessionId <= 0) {
            return "";
        }
        return cfUdeskSessionRecordDao.getNickNameBySubSessionId(sessionId);
    }

    public int selectByCustomIdAndCreateAt(List<Integer> customIds, Date createdAt) {
        if (CollectionUtils.isEmpty(customIds)) {
            return 0;
        }

        return cfUdeskSessionRecordDao.selectByCustomIdAndCreateAt(customIds, createdAt);
    }

    public UserManage.UdeskBizRecord selectLastRecordByCustomId(List<Integer> customIds) {

        if (CollectionUtils.isEmpty(customIds)) {
            return null;
        }

        return cfUdeskSessionRecordDao.selectLastRecordByCustomId(customIds);
    }

}
