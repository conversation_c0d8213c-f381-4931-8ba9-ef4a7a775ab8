package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.CfRepeatUserIdRecordBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfRepeatUserIdRecordDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfRepeatUserIdRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by ahrievil on 2017/6/21.
 */
@Service
public class CfRepeatUserIdRecordBizImpl implements CfRepeatUserIdRecordBiz {

    @Autowired
    private CfRepeatUserIdRecordDao cfRepeatUserIdRecordDao;

    @Override
    public int insertList(List<CfRepeatUserIdRecord> cfRepeatUserIdRecord) {
        return cfRepeatUserIdRecordDao.insertList(cfRepeatUserIdRecord);
    }

    @Override
    public List<Integer> selectUserId() {
        return cfRepeatUserIdRecordDao.selectUserId();
    }

    @Override
    public List<CfRepeatUserIdRecord> selectByRemainCounts() {
        return cfRepeatUserIdRecordDao.selectByRemainCounts();
    }

    @Override
    public int updateRemainCount(long userId, int remainCounts) {
        return cfRepeatUserIdRecordDao.updateRemainCount(userId, remainCounts);
    }

    @Override
    public List<CfRepeatUserIdRecord> selectAll(int start, int limit) {
        return cfRepeatUserIdRecordDao.selectAll(start, limit);
    }
}
