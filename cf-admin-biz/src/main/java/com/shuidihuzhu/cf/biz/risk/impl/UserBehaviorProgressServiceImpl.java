package com.shuidihuzhu.cf.biz.risk.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingCommentBiz;
import com.shuidihuzhu.cf.biz.risk.IUserBehaviorService;
import com.shuidihuzhu.cf.client.feign.CfCommonFeignClient;
import com.shuidihuzhu.cf.client.feign.CfPlatformEsFeignClient;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.UserBehaviorEnum;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2019/10/12 下午2:33
 * @desc 发布动态
 */
@Service
public class UserBehaviorProgressServiceImpl implements IUserBehaviorService {

    @Autowired
    private CfCommonFeignClient commonFeignClient;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;

    @Autowired
    private CfPlatformEsFeignClient platformEsFeignClient;

    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;

    @Autowired
    private AdminCrowdfundingCommentBiz cfCommentBiz;


    @Override
    public UserBehaviorEnum getBehavior() {
        return UserBehaviorEnum.PROGRESS;
    }

    @Override
    public List<AdminUserBehaviorDetail> buildBehaviorDetail(String mobile, long userId, String name, String identity, UserInfoModel userInfo, UserBehaviorEnum behaviorEnum) {

        List<CrowdFundingProgress> progresses = commonFeignClient.queryProgressByUserId(userId).getData();
        if(CollectionUtils.isEmpty(progresses)){
            return Lists.newArrayList();
        }

        UserInfoDetail userInfoDetail = new UserInfoDetail();
        userInfoDetail.setUserId(userId);
        userInfoDetail.setNickName(Objects.nonNull(userInfo) ? userInfo.getNickname() : "");

        List<AdminUserBehaviorDetail> progressDetails = Lists.newArrayList();
        for (CrowdFundingProgress progress : progresses){
            List<CrowdfundingCommentVo> comments = cfCommentBiz.getCommentByParentIdFromTiDb(progress.getId(), 2000);

            int caseId = progress.getActivityId();
            String imageUrl = progress.getImageUrls();

            CrowdfundingInfo caseInfo = crowdfundingFeignClient.getCaseInfoById(caseId).getData();

            StringBuilder sb = new StringBuilder();
            sb.append("案例id:").append(caseId).append(REDEX);
            sb.append("案例标题:").append(Objects.nonNull(caseInfo) ? caseInfo.getTitle() : "").append(REDEX);
            sb.append("动态内容:").append(progress.getContent());

            AdminUserBehaviorDetail progressDetail = new AdminUserBehaviorDetail();
            progressDetail.setTime(progress.getPostDate());
            progressDetail.setBehaviorType(UserBehaviorEnum.PROGRESS.getKey());
            progressDetail.setUserInfoDetail(userInfoDetail);
            progressDetail.setUrl(StringUtils.isNotEmpty(imageUrl) ? Lists.newArrayList(imageUrl.split(",")) : Lists.newArrayList());
            progressDetail.setBehavoir(Lists.newArrayList(sb.toString().split(REDEX)));
            progressDetail.setSubBehavoirDetails(subCommentDetail(comments));
            progressDetails.add(progressDetail);
        }

        return progressDetails;
    }

    private List<Object> subCommentDetail(List<CrowdfundingCommentVo> comments){
        if(CollectionUtils.isEmpty(comments)){
            return Lists.newArrayList();
        }

        List<Object> subComments = Lists.newArrayList();

        for (CrowdfundingCommentVo progressComment : comments){

            UserInfoModel userInfo = userInfoServiceBiz.getUserInfoByUserId(progressComment.getUserId());

            UserSubBehavoirDetail detail = new UserSubBehavoirDetail();
            detail.setUserId(progressComment.getUserId());
            detail.setNickName(Objects.nonNull(userInfo) ? userInfo.getNickname() : "");
            detail.setTime(progressComment.getCreateTime());
            detail.setSubDetail(progressComment.getContent());

            subComments.add(detail);
        }

        return subComments;
    }
}
