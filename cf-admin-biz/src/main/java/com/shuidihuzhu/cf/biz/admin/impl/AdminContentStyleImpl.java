package com.shuidihuzhu.cf.biz.admin.impl;

import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.biz.admin.AdminContentStyleBiz;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.cf.service.workorder.WorkOrderRemarkService;
import com.shuidihuzhu.client.cf.api.client.CfContentStyleFeignClient;
import com.shuidihuzhu.client.cf.api.model.CfContentStyleModel;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/11/8 4:59 PM
 */
@Slf4j
@Service
public class AdminContentStyleImpl implements AdminContentStyleBiz {

    @Resource
    private CfContentStyleFeignClient cfContentStyleFeignClient;
    @Resource
    private ApproveRemarkOldService approveRemarkOldService;

    @Override
    public void addOrUpdateStyle(CfContentStyleModel cfContentStyleModel, long userId) {
        log.info("AdminContentStyleImpl addOrUpdateStyle cfContentStyleModel {}", JSONObject.toJSONString(cfContentStyleModel));
        cfContentStyleFeignClient.insertOrUpdateStyle(cfContentStyleModel);

        // 保存操作记录
        StringBuilder comment = new StringBuilder("修改文章关键字: ");
        List<String> keywords = cfContentStyleModel.getKeywords();
        for (String keyword : keywords) {
            comment.append(keyword).append(";");
        }

        approveRemarkOldService.add(cfContentStyleModel.getCaseId(), (int) userId, comment.toString());
    }

    @Override
    public CfContentStyleModel selectStyleByCaseId(Integer caseId) {
        Response<CfContentStyleModel> response = cfContentStyleFeignClient.getStyleByCaseId(caseId);
        return Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);
    }
}
