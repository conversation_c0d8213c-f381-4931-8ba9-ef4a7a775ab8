package com.shuidihuzhu.cf.biz.customer.Impl;

import com.github.pagehelper.*;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.customer.*;
import com.shuidihuzhu.cf.customer.*;
import com.shuidihuzhu.cf.dao.customer.CfUdeskChatRecordDao;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class UdeskChatRecordBizImpl implements UdeskChatRecordBiz {
    @Autowired
    private CfUdeskChatRecordDao cfUdeskChatRecordDao;
    @Autowired
    private CfUdeskCustomerInfoBiz cfUdeskCustomerInfoBiz;
    @Autowired
    private CfUdeskSessionRecordBiz cfUdeskSessionRecordBiz;

    private static final String CUSTOMER_SENDER = "customer";
    private static final String UDESK_CONTENT_IMG_TYPE = "image";


    @Override
    public List<ChatHistoryModelVo> getUdeskChatRecords(String startTime, String endTime, List<Long> cfUserIds) {
        if (CollectionUtils.isEmpty(cfUserIds)) {
            return Lists.newArrayList();
        }
        List<CfUdeskCustomerInfo> udeskCustomerInfos = cfUdeskCustomerInfoBiz.getUdeskCustomersByCfUserIds(cfUserIds);
        Map<Integer, String> udeskUserIds = udeskCustomerInfos.stream()
                .collect(Collectors.toMap(CfUdeskCustomerInfo::getUdeskCustomerId, CfUdeskCustomerInfo::getNickName));
        List<CfUdeskSessionRecordVo> cfUdeskSessionRecordVos = cfUdeskSessionRecordBiz.getUdeskSessionsByUdeskUserIds(udeskUserIds.keySet());
        Map<Integer, String> subSessionIds = cfUdeskSessionRecordVos.stream()
                .collect(Collectors.toMap(CfUdeskSessionRecordVo::getSubSessionId, CfUdeskSessionRecordVo::getAgentNickName));
        if (CollectionUtils.isEmpty(subSessionIds.keySet())) {
            return Lists.newArrayList();
        }
        List<UdeskChatVo> udeskChatVos = cfUdeskChatRecordDao.getUdeskChatRecords(startTime, endTime, subSessionIds.keySet());
        if (CollectionUtils.isEmpty(udeskChatVos)) {
            return Lists.newArrayList();
        }
        List<ChatHistoryModelVo> chatHistoryModelVos = Lists.newArrayList();
        for (UdeskChatVo udeskChatVo : udeskChatVos) {
            ChatHistoryModelVo chatHistoryModelVo = new ChatHistoryModelVo();
            chatHistoryModelVo.setMsg(udeskChatVo.getChatContent());
            if (udeskChatVo.getSender().equals(CUSTOMER_SENDER)) {
                chatHistoryModelVo.setSenderName(udeskUserIds.get(udeskChatVo.getUdeskCustomerId()));
            } else {
                chatHistoryModelVo.setSenderName(subSessionIds.get(udeskChatVo.getSessionId()));
            }
            chatHistoryModelVo.setSendTime(udeskChatVo.getCreatedAt());
            if (udeskChatVo.getContentType().equals(UDESK_CONTENT_IMG_TYPE)) {
                chatHistoryModelVo.setMsgType(1);
            }
            chatHistoryModelVos.add(chatHistoryModelVo);
        }
        return chatHistoryModelVos;
    }

    @Override
    public List<ChatHistoryModelVo> getUdeskByCid(int cid, int current, int pageSize) {
        String orderBy = "create_time";
        PageHelper.startPage(current, pageSize, orderBy);
        List<UdeskChatVo> udeskChatRecords = cfUdeskChatRecordDao.getUdeskChatRecords(null, null, Stream.of(cid).collect(Collectors.toSet()));
        String agentNickName = cfUdeskSessionRecordBiz.getNickNameBySessionId(cid);
        String userNickName = "";

        //获取用户昵称
        Integer udeskUserId = udeskChatRecords.stream().filter(item -> item.getSender().equals(CUSTOMER_SENDER))
                .map(UdeskChatVo::getUdeskCustomerId).findFirst().orElse(null);
        if (udeskUserId != null) {
            userNickName = cfUdeskCustomerInfoBiz.getNickNameByUdeskUserId((long)udeskUserId);
        }

        Page<ChatHistoryModelVo> chatHistoryModelVos = new Page<>();
        for (UdeskChatVo udeskChatVo : udeskChatRecords) {
            ChatHistoryModelVo chatHistoryModelVo = new ChatHistoryModelVo();
            chatHistoryModelVo.setMsg(udeskChatVo.getChatContent());
            if (udeskChatVo.getSender().equals(CUSTOMER_SENDER)) {
                chatHistoryModelVo.setSenderName(userNickName);
            } else {
                chatHistoryModelVo.setSenderName(agentNickName);
            }
            chatHistoryModelVo.setSendTime(udeskChatVo.getCreatedAt());
            if (udeskChatVo.getContentType().equals(UDESK_CONTENT_IMG_TYPE)) {
                chatHistoryModelVo.setMsgType(1);
            }
            chatHistoryModelVos.add(chatHistoryModelVo);
        }
        chatHistoryModelVos.setTotal(((Page) udeskChatRecords).getTotal());
        chatHistoryModelVos.setPageSize(((Page) udeskChatRecords).getPageSize());
        chatHistoryModelVos.setPageNum(((Page) udeskChatRecords).getPageNum());
        return chatHistoryModelVos;
    }
}
