package com.shuidihuzhu.cf.biz.fundUse;

import com.shuidihuzhu.cf.vo.crowdfunding.AdminCrowdfundingProgress;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2021/5/20 17:58
 * @Description:
 */
public interface CfFundUseProgressBiz {
    List<AdminCrowdfundingProgress> selectByProgressIdList(List<Integer> idList);

    int updateContentAndImg(long fundUseProgressId, String content, String imageUrls);
}
