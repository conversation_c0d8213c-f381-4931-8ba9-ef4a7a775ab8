package com.shuidihuzhu.cf.biz.mask;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.feign.AuthorFeignClient;
import com.shuidihuzhu.cf.client.feign.CfUserInfoFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.delegate.crowdfunding.impl.CrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.model.admin.AdminMaskParam;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoVo;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.baseservice.verify.v1.enums.UserRelTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Description: sea后台查看掩码
 * @Author: panghairui
 * @Date: 2022/7/5 8:19 下午
 */
@Slf4j
@Service
public class AdminViewMaskBiz {

    @Autowired
    private ShuidiCipher shuidiCipher;
    @Resource
    private AuthorFeignClient authorFeignClient;
    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;
    @Resource
    private CfUserInfoFeignClient cfUserInfoFeignClient;
    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private AdminApproveService adminApproveService;
    @Resource
    private CrowdfundingDelegate crowdfundingDelegate;

    /**
     * 查看用户手机号
     * @return
     */
    public String viewRaiseMaskMobile(AdminMaskParam adminMaskParam) {
        UserInfoModel userInfoModel = getUserInfo(adminMaskParam);
        if (Objects.isNull(userInfoModel)) {
            return "";
        }

        return shuidiCipher.decrypt(userInfoModel.getCryptoMobile());
    }

    /**
     * 查看发起人身份证号
     * @return
     */
    public String viewRaiseMaskIdCard(AdminMaskParam adminMaskParam) {
        CfFirsApproveMaterial cfFirsApproveMaterial = getBaseInfo(adminMaskParam);
        if (Objects.isNull(cfFirsApproveMaterial)) {
            return "";
        }

        if (cfFirsApproveMaterial.getUserRelationTypeForC() == UserRelTypeEnum.SELF.getValue()) {
            return StringUtils.trimToNull(shuidiCipher.decrypt(cfFirsApproveMaterial.getPatientCryptoIdcard()));
        }

        return StringUtils.trimToNull(shuidiCipher.decrypt(cfFirsApproveMaterial.getSelfCryptoIdcard()));
    }

    /**
     * 查看患者身份证号
     * @return
     */
    public String viewPatientMaskIdCard(AdminMaskParam adminMaskParam) {
        CfFirsApproveMaterial cfFirsApproveMaterial = getBaseInfo(adminMaskParam);
        if (Objects.isNull(cfFirsApproveMaterial)) {
            return "";
        }

        String idCard = "";
        if (cfFirsApproveMaterial.getPatientIdType() == 1) {
            idCard = StringUtils.trimToNull(shuidiCipher.decrypt(cfFirsApproveMaterial.getPatientCryptoIdcard()));
        } else if (cfFirsApproveMaterial.getPatientIdType() == 2) {
            idCard = cfFirsApproveMaterial.getPatientBornCard();
        }

        return idCard;
    }

    public String viewPatientMaskIdCardV2(AdminMaskParam adminMaskParam) {
        CrowdfundingInfoVo crowdfundingInfoVo = crowdfundingInfoBiz.getFundingInfoVoByInfoUuid(adminMaskParam.getInfoId());
        if (Objects.isNull(crowdfundingInfoVo)) {
            return "";
        }

        CrowdfundingAuthor crowdfundingAuthor = adminApproveService.aesDecryptIdcardAndMobile(crowdfundingInfoVo);
        if (Objects.isNull(crowdfundingAuthor)) {
            return viewPatientMaskIdCard(adminMaskParam);
        }

        return crowdfundingAuthor.getIdCard();
    }

    /**
     * 查看收款人身份证号
     */
    public String viewPayeeMaskIdCard(AdminMaskParam adminMaskParam) {
        CrowdfundingInfo crowdfundingInfo = getCrowdfundingInfo(adminMaskParam);
        if (Objects.isNull(crowdfundingInfo)) {
            return "";
        }

        return shuidiCipher.decrypt(crowdfundingInfo.getPayeeIdCard());

    }

    /**
     * 查看收款人银行卡号
     */
    public String viewPayeeMaskBankCard(AdminMaskParam adminMaskParam) {
        CrowdfundingInfo crowdfundingInfo = getCrowdfundingInfo(adminMaskParam);
        if (Objects.isNull(crowdfundingInfo)) {
            return "";
        }

        return shuidiCipher.decrypt(crowdfundingInfo.getPayeeBankCard());

    }

    /**
     * 查看证实人身份证号
     */
    public String viewConfirmMaskIdCard(AdminMaskParam adminMaskParam) {

        FeignResponse<UserRealInfo> result = cfUserInfoFeignClient.getByUserId(adminMaskParam.getId());
        if (result == null || Objects.isNull(result.getData()) || result.notOk()) {
            return "";
        }

        return shuidiCipher.decrypt(result.getData().getCryptoIdCard());

    }

    /**
     * 查看对公医院银行卡号
     */
    public String viewContraryBankCard(AdminMaskParam adminMaskParam) {
        CrowdfundingInfoHospitalPayee crowdfundingInfoHospitalPayee = crowdfundingDelegate
                .getCrowdfundingInfoHospitalPayeeByInfoUuid(adminMaskParam.getInfoId());
        if (Objects.isNull(crowdfundingInfoHospitalPayee)) {
            return "";
        }

        return crowdfundingInfoHospitalPayee.getHospitalBankCard();
    }


    private CrowdfundingInfo getCrowdfundingInfo(AdminMaskParam adminMaskParam) {
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(adminMaskParam.getCaseId());
        if (Objects.isNull(crowdfundingInfo)) {
            return null;
        }
        return crowdfundingInfo;
    }

    private CfFirsApproveMaterial getBaseInfo(AdminMaskParam adminMaskParam) {
        FeignResponse<CfFirsApproveMaterial> cfFirsApproveMaterialFeignResponse = authorFeignClient.getAuthorInfoByInfoId(adminMaskParam.getCaseId());
        return (cfFirsApproveMaterialFeignResponse == null || cfFirsApproveMaterialFeignResponse.notOk() || cfFirsApproveMaterialFeignResponse.getData() == null)
                ? null : cfFirsApproveMaterialFeignResponse.getData();
    }

    private UserInfoModel getUserInfo(AdminMaskParam adminMaskParam) {
        UserInfoModel userInfoModel = null;
        if (adminMaskParam.getId() != null) {
            userInfoModel = userInfoServiceBiz.getUserInfoByUserId(adminMaskParam.getId());
        } else if (adminMaskParam.getUserId() != null) {
            userInfoModel = userInfoServiceBiz.getUserInfoByUserId(adminMaskParam.getUserId());
        }
        return Objects.isNull(userInfoModel) ? null : userInfoModel;
    }

}
