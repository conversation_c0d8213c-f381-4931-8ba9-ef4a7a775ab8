package com.shuidihuzhu.cf.biz.admin;

import com.shuidihuzhu.cf.model.record.TargetAmountAuditRecord;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/09/15
 */
public interface TargetAmountAuditRecordBiz {
    int add(TargetAmountAuditRecord targetAmountAuditRecord);

    TargetAmountAuditRecord getRecordByWorkOrderId(long workOrderId);

    List<TargetAmountAuditRecord> getRecordByCaseId(long caseId);

    int deleteRecordByWorkOrderId(long workOrderId);
}
