package com.shuidihuzhu.cf.biz.risk.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.admin.util.AdminDateUtil;
import com.shuidihuzhu.cf.admin.util.TianRuCallRecordUtil;
import com.shuidihuzhu.cf.biz.call.CallRecordBiz;
import com.shuidihuzhu.cf.biz.risk.IUserBehaviorService;
import com.shuidihuzhu.cf.call.CallInModel;
import com.shuidihuzhu.cf.delegate.SeaAccountDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.UserBehaviorEnum;
import com.shuidihuzhu.cf.model.crowdfunding.AdminUserBehaviorDetail;
import com.shuidihuzhu.cf.model.crowdfunding.UserInfoDetail;
import com.shuidihuzhu.cf.util.crowdfunding.TimeUtils;
import com.shuidihuzhu.common.web.util.MD5Util;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @time 2019/10/12 下午6:03
 * @desc 平台主动外呼
 */
@Service
public class UserBehaviorUserCallInServiceImpl implements IUserBehaviorService {

    @Autowired
    SeaAccountDelegate seaAccountDelegate;

    @Autowired
    private CallRecordBiz callRecordBiz;

    @Override
    public UserBehaviorEnum getBehavior() {
        return UserBehaviorEnum.USER_CALL;
    }

    @Override
    public List<AdminUserBehaviorDetail> buildBehaviorDetail(String mobile, long userId, String name, String identity, UserInfoModel userInfo, UserBehaviorEnum behaviorEnum) {

        UserInfoDetail userInfoDetail = new UserInfoDetail();
        userInfoDetail.setMobile(mobile);


        List<CallInModel> callInModelList = callRecordBiz.getAllCallIn(mobile);
        if (CollectionUtils.isEmpty(callInModelList)) {
            return Lists.newArrayList();
        }

        List<AdminUserBehaviorDetail> callRecords = Lists.newArrayList();
        for (CallInModel callInModel : callInModelList) {
            long duration = Optional.ofNullable(callInModel.getCallDuration()).map(Long::valueOf).orElse(0L);
            StringBuilder sb = new StringBuilder();
            boolean callStatus = duration > 0;

            //呼出时间
            String callTime = callInModel.getCallTime();
            //挂断时间
            String hangOnTime = "";
            if (StringUtils.isNotEmpty(callTime)) {
                hangOnTime = AdminDateUtil.formatter.parseDateTime(callTime).plusSeconds((int) duration).toString(AdminDateUtil.formatter);
            }

            //处理人的组织-名称
            String seatNumber = callInModel.getSeatNumber();
            String operatorInfo = seaAccountDelegate.obtainOperatorNameAndOrg(seatNumber);

            sb.append("呼通与否:").append(callStatus ? "是" : "否").append(REDEX);
            sb.append("呼叫开始时间:").append(callTime).append(REDEX);
            sb.append("呼叫挂断时间:").append(hangOnTime).append(REDEX);
            sb.append("呼通时间:").append(TimeUtils.timeFormat((int) duration)).append(REDEX);
            sb.append("处理人:").append(operatorInfo);


            AdminUserBehaviorDetail callRecord = new AdminUserBehaviorDetail();
            callRecord.setTime(callInModel.getCreateTime());
            callRecord.setBehaviorType(UserBehaviorEnum.USER_CALL.getKey());
            callRecord.setUrl(Lists.newArrayList());
            callRecord.setUserInfoDetail(userInfoDetail);
            if (StringUtils.isNotBlank(callInModel.getRecordUrl())) {
                callRecord.setSubBehavoirDetails(Lists.newArrayList(TianRuCallRecordUtil.callRecordUrlInfo(callInModel.getRecordUrl())));
            }
            callRecord.setBehavoir(Lists.newArrayList(sb.toString().split(REDEX)));

            callRecords.add(callRecord);
        }
        return callRecords;
    }


}
