package com.shuidihuzhu.cf.biz.crowdfunding;

import com.github.pagehelper.PageInfo;
import com.shuidihuzhu.cf.client.base.page.v1.model.PaginationListVO;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCfRepeatInfo;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCfRepeatView;
import com.shuidihuzhu.cf.model.crowdfunding.CfCapitalAccount;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.cf.vo.approve.RecoverRepeatCaseVo;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface AdminCfRepeatInfoBiz {
    PaginationListVO<RecoverRepeatCaseVo> getRepeatCaseWhenRecovery(CrowdfundingInfo crowdfundingInfo, int current, int pageSize);

    void fillPatientMaterial(CrowdfundingInfo cfInfo, AdminCfRepeatInfo.AdminCfMaterial cfMaterial);

    void handleCfMaterialRepeat(int caseId, boolean caseBaseInfoChange);

    Set<Integer> findRepeatOrIds(AdminCfRepeatInfo.AdminCfMaterial cfMaterial);

    Set<Integer> findActualRepeatOrgIds(String OriginatorIdCard);

    Set<Integer> findRepeatOrMobileIds(AdminCfRepeatInfo.AdminCfMaterial cfMaterial);

    Set<Integer> findRepeatPIdCardIds(AdminCfRepeatInfo.AdminCfMaterial cfMaterial);

    Set<Integer> findActualRepeatPIdCardIds(String patientIdCard);

    Set<Integer> findRepeatBornCardIds(String bornCard);

    void handleCfMaterialRepeat(int caseId, boolean caseBaseInfoChange, CfCapitalAccount cfCapitalAccount);

    @Deprecated
    List<AdminCfRepeatView> selectRepeatInfoViewByCaseId(Integer caseId);

    Set<Integer> selectRepeatStatusByCaseId(int caseId);

    Set<Integer> selectRepeatStatusByCaseId(int caseId, CfCapitalAccount cfCapitalAccount);

    Set<String> selectRepeatDescByCaseId(int caseId, CfCapitalAccount cfCapitalAccount);

    Set<Integer> findRepeatPNameIds(AdminCfRepeatInfo.AdminCfMaterial cfMaterial);

    Set<Integer> findRepeatPayeeIds(AdminCfRepeatInfo.AdminCfMaterial cfMaterial);

    List<CrowdfundingAttachmentVo> getFundingAttachmentWithRepeatInfo(int caseId);

    Map<Integer, List<CrowdfundingAttachmentVo>> getFundingAttachmentWithRepeatInfoBatch(List<Integer> caseIdList);

    List<CrowdfundingAttachmentVo> getFundingAttachmentWithRepeatInfoSupplement(int caseId);

    AdminCfRepeatInfo selectByCaseId(int caseId);

    PageInfo<AdminCfRepeatView> selectRepeatInfoPageByCaseId(Integer caseId, int current, int pageSize);

    AdminCfRepeatInfo buildRepeatInfo(int caseId);

    AdminCfRepeatInfo.AdminCfMaterial findCfMaterialByCaseId(CrowdfundingInfo cfInfo);
}

