package com.shuidihuzhu.cf.biz.stat.impl;

import com.shuidihuzhu.cf.biz.stat.CfWxUserCumulateDataBiz;
import com.shuidihuzhu.cf.dao.stat.CfWxUserCumulateDataDao;
import com.shuidihuzhu.cf.model.message.CfWxUserCumulateData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
@Slf4j
public class CfWxUserCumulateDataBizImpl implements CfWxUserCumulateDataBiz {
    @Autowired
    private CfWxUserCumulateDataDao wxUserCumulateDataDao;

    @Override
    public CfWxUserCumulateData queryByRefDateAndThirdType(int thirdType, String refDate) {
        if (StringUtils.isEmpty(refDate) || thirdType <= 0) {
            return null;
        }
        return wxUserCumulateDataDao.queryByRefDateAndThirdType(thirdType, refDate);
    }
}
