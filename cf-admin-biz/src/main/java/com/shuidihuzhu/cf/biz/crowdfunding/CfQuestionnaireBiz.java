package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.QuestionnaireRecord;
import com.shuidihuzhu.cf.model.admin.CfQuestionnaire;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2019/12/12
 */
public interface CfQuestionnaireBiz {

    int submit_status = 1;

    String ext_questionnaireId = "questionnaireId";

    String chuci_source = "初审审核";

    String cailiao_source = "材料审核";

    String chuci_url = "https://www.shuidichou.com/qs/detail/b34dfde7-e28a-41a0-b5c1-2dcca8230b62";

    String cailiao_url = "https://www.shuidichou.com/qs/detail/05e7a520-a0dd-463d-8e46-7df55936f095";


    String getQid();

    String getQurl();

    String getUserQurl();

    CfQuestionnaire getById(long id);

    List<CfQuestionnaire> getByIds(List<Long> ids);

    int save(CfQuestionnaire cfQuestionnaire);

    String getByCard(String card);

    List<CfQuestionnaire> getListByCard(String card);

    int updateContentByUserId(long questionnaireId, String content, int status, String qname, String startAnsweringTime, String endTime);


    List<CfQuestionnaire> getList(String qid,
                                  long userId,
                                  String qname,
                                  String name,
                                  String channel,
                                  String source,
                                  int status,
                                  String mobile,
                                  String startTime,
                                  String endTime,
                                  int pageSize,
                                  boolean isPre,
                                  long anchor,
                                  long recordId);


    List<CfQuestionnaire> getExcelList(String qid,
                                       long userId,
                                       String qname,
                                       String name,
                                       String channel,
                                       String source,
                                       int status,
                                       String mobile,
                                       String startTime,
                                       String endTime,
                                       long recordId);

    int saveCaseId(long recordId, int caseId);


    int saveOperatorIdAndComment(long recordId, int operatorId, String comment);

    int total(String qid, long userId, String qname,  String name, String channel, String source, int status, String q_mobile, String startTime, String endTime, long recordId);

    QuestionnaireRecord canSend(int caseId, String source);


    long save(long userId,int caseId,String channel,String card,String mobile,String source, Date sendTime);

    void sendDelayMQForMsg(QuestionnaireRecord qr, long delaySecs);

}
