package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.CfReportProblemBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfReportProblemDao;
import com.shuidihuzhu.cf.dao.crowdfunding.CfReportProblemLabelDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportProblem;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportProblemLabel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * @author: fengxuan
 * @create 2019-12-11 16:26
 **/
@Slf4j
@Service
public class CfReportProblemBizImpl implements CfReportProblemBiz {

    @Autowired
    CfReportProblemDao problemDao;

    @Autowired
    CfReportProblemLabelDao labelDao;

    @Override
    public List<CfReportProblemLabel> listProblemLabelsByIds(List<Integer> labelIds, Integer isUse) {
        if (CollectionUtils.isEmpty(labelIds)) {
            return Lists.newArrayList();
        }
        return labelDao.findByIds(labelIds, isUse);
    }

    @Override
    public boolean insertLabel(CfReportProblemLabel label) {
        if (label == null || StringUtils.isBlank(label.getLabelDesc())) {
            log.warn("report problem label info error:{}", label);
            return false;
        }
        return labelDao.insert(label) == 1;
    }

    @Override
    public CfReportProblemLabel findLabelByDesc(String desc) {
        if (StringUtils.isBlank(desc)) {
            return null;
        }
        return labelDao.findByDesc(desc);
    }

    @Override
    public List<CfReportProblemLabel> listLabels(Integer level, Integer parentId, Integer isUse) {
        return labelDao.listByLevelAndParentId(level, parentId, isUse);
    }

    @Override
    public int updateLabel(String labelDesc,int sort, int id) {
        if (StringUtils.isBlank(labelDesc) || id < 0) {
            return 0;
        }
        return labelDao.updateLabel(labelDesc,sort, id);
    }

    @Override
    public int addProblemClassify(String labelDesc, int sort, int level) {
        if (StringUtils.isBlank(labelDesc)) {
            return 0;
        }
        CfReportProblemLabel cfReportProblemLabel = CfReportProblemLabel.buildProblemClassify(labelDesc, level, sort);
        labelDao.insert(cfReportProblemLabel);
        return cfReportProblemLabel.getId();
    }

    @Override
    public int updateIsUse(int isUse, int id) {
        return labelDao.updateIsUse(isUse, id);
    }

    @Override
    public List<CfReportProblemLabel> getByParentIdAndIsUse(int parentId, int isUse) {
        return labelDao.getByParentIdAndIsUse(parentId, isUse);
    }

    @Override
    public int addProblemModule(String moduleName, int isMandatory, int classifyId, int sort) {
        if (StringUtils.isBlank(moduleName)){
            return 0;
        }
        CfReportProblemLabel cfReportProblemLabel =
                CfReportProblemLabel.buildProblemModule(moduleName, isMandatory, classifyId, sort, 2);
        labelDao.insert(cfReportProblemLabel);
        return cfReportProblemLabel.getId();
    }

    @Override
    public int updateProblemModule(int parentId, String labelDesc, int sort, int isMandatory, int id) {
        if (id < 0){
            return 0;
        }
       if (labelDao.getByPartentIdAndLabelDesc(parentId, labelDesc) != null){
           return 0;
       }
        return labelDao.updateProblemModule(parentId, labelDesc, sort, isMandatory, id);
    }

    @Override
    public CfReportProblemLabel getById(int id) {
        if (id < 0){
            return null;
        }
        return labelDao.getById(id);
    }

    @Override
    public boolean updateById(CfReportProblem problem) {
        if (problem == null || problem.getId() == 0 || StringUtils.isBlank(problem.getProblem())) {
            log.warn("update report problem info error:{}", JSON.toJSONString(problem));
            return false;
        }
        problemDao.updateById(problem);
        return true;
    }

    @Override
    public boolean insert(CfReportProblem problem) {
        CfReportProblem reportProblem = problemDao.findByLabelIdAndProblem(problem.getLabelId(), problem.getProblem());
        if (reportProblem != null) {
            //这里一定要设置为 false 否者会导致后面出现多个相同的备选项
            log.warn("insert report problem repeat insert:{}", JSON.toJSONString(problem));
            return false;
        }
        return problemDao.insert(problem) == 1;
    }

    @Override
    public CfReportProblem findProblemById(int id) {
        return problemDao.findById(id);
    }

    @Override
    public List<CfReportProblem> listByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return problemDao.listByIds(ids);
    }

    @Override
    public List<CfReportProblem> listDirectShow(List<Integer> labelIds, List<Integer> showLocations) {
        if (CollectionUtils.isEmpty(labelIds)) {
            return Lists.newArrayList();
        }
        return problemDao.listDirectShowByLabelId(labelIds, showLocations);
    }

    @Override
    public List<CfReportProblem> listForManager(String problemDesc, int current, int pageSize,
                                                int id, int isUse, Integer collation) {
        PageHelper.startPage(current, pageSize);
        return problemDao.getList(problemDesc, isUse, id, collation);
    }

    @Override
    public List<CfReportProblem> listSameLabelProblems(int labelId) {
        return problemDao.listByLabelId(labelId);
    }

    @Override
    public List<CfReportProblem> listForManager(String problemDesc, Integer isUse) {
        return problemDao.listForManager(problemDesc, isUse);
    }

    @Override
    public List<CfReportProblem> listByLabelIdAndIsUse(int labelId, int isUse) {
        if (labelId < 0){
            return Lists.newArrayList();
        }
        return problemDao.listByLabelIdAndIsUse(labelId, isUse);
    }

    @Override
    public int updateUseStatusById(int isUse, int id) {
        if (isUse < 0 || id < 0){
            return 0;
        }
        return problemDao.updateUseStatusById(isUse, id);
    }

}
