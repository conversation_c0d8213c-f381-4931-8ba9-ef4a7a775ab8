package com.shuidihuzhu.cf.biz.aiphoto.utils;

import com.qcloud.cos.utils.IOUtils;
import com.shuidihuzhu.cf.adminfeign.ImageLoadFeign;
import feign.Feign;
import feign.Request;
import feign.Response;
import feign.Target;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.color.ColorSpace;
import java.awt.image.BufferedImage;
import java.awt.image.ColorConvertOp;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URI;

/**
 * @Author: liujiawei
 * @Date: 2018/8/24  15:17
 */
@Slf4j
public class ImageSimilarity {

   public static InputStream getStream(String fileURL) throws Exception {
        InputStream inputStream = null;
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        if (fileURL.startsWith("http") || fileURL.startsWith("https")) {
            Request.Options options = new Request.Options(10 * 1000, 60 * 1000);
            ImageLoadFeign feign = Feign.builder()
                    .options(options)
                    .target(Target.EmptyTarget.create(ImageLoadFeign.class));
            Response image = feign.getImage(new URI(fileURL));
            Response.Body body = image.body();
            inputStream = body.asInputStream();
        } else {
            log.error("图片url 非http开头 {}", fileURL);
            throw new RuntimeException("图片url 非http开头");
        }
        IOUtils.copy(inputStream, output);
       inputStream.close();
       return new ByteArrayInputStream(output.toByteArray());
    }
   public static int getImageSimilarity(String file_live, String file_id_card) {
        int result = 0;
        Image image1 = null;
        Image image2 = null;
       try {
            if (StringUtils.isBlank(file_live) || StringUtils.isBlank(file_id_card)) {
                log.warn("getImageSimilarity 图片为空 {}, {}", file_live, file_id_card);
                return result;
            }
            image1 = ImageIO.read(getStream(file_live));
            // 转换至灰度
            image1 = toGrayscale(image1);
            // 缩小成32x32的缩略图
            image1 = scale(image1);
            // 获取灰度像素数组
            int[] pixels1 = getPixels(image1);
            // 获取平均灰度颜色
            int averageColor1 = getAverageOfPixelArray(pixels1);
            // 获取灰度像素的比较数组（即图像指纹序列）
            pixels1 = getPixelDeviateWeightsArray(pixels1, averageColor1);
            image2 = ImageIO.read(getStream(file_id_card));
            // 转换至灰度
            image2 = toGrayscale(image2);
            // 缩小成32x32的缩略图
            image2 = scale(image2);
            // 获取灰度像素数组
            int[] pixels2 = getPixels(image2);
            // 获取平均灰度颜色
            int averageColor2 = getAverageOfPixelArray(pixels2);
            // 获取灰度像素的比较数组（即图像指纹序列）
            pixels2 = getPixelDeviateWeightsArray(pixels2, averageColor2);
            // 获取两个图的汉明距离（假设另一个图也已经按上面步骤得到灰度比较数组）
            int hammingDistance = getHammingDistance(pixels1, pixels2);
            // 通过汉明距离计算相似度，取值范围 [0.0, 1.0]
            double similarity = calSimilarity(hammingDistance);
            result = (int) (similarity * 100);
            return result;
        } catch (Exception e) {
            log.info("相似度校验异常：{}", e);
            return result;
        }finally {
           if (image1 != null){
               image1.flush();
           }
           if (image2 != null) {
               image2.flush();
           }
        }

    }

    // 将任意Image类型图像转换为BufferedImage类型，方便后续操作
    public static BufferedImage convertToBufferedFrom(Image srcImage) {
        BufferedImage bufferedImage = new BufferedImage(srcImage.getWidth(null),
                srcImage.getHeight(null), BufferedImage.TYPE_INT_ARGB);
        Graphics2D g = bufferedImage.createGraphics();
        g.drawImage(srcImage, null, null);
        g.dispose();
        return bufferedImage;
    }

    // 转换至灰度图
    public static BufferedImage toGrayscale(Image image) {
        BufferedImage sourceBuffered = convertToBufferedFrom(image);
        ColorSpace cs = ColorSpace.getInstance(ColorSpace.CS_GRAY);
        ColorConvertOp op = new ColorConvertOp(cs, null);
        BufferedImage grayBuffered = op.filter(sourceBuffered, null);
        return grayBuffered;
    }

    // 缩放至32x32像素缩略图
    public static Image scale(Image image) {
        image = image.getScaledInstance(32, 32, Image.SCALE_SMOOTH);
        return image;
    }

    // 获取像素数组
    public static int[] getPixels(Image image) {
        int width = image.getWidth(null);
        int height = image.getHeight(null);
        int[] pixels = convertToBufferedFrom(image).getRGB(0, 0, width, height,
                null, 0, width);
        return pixels;
    }

    // 获取灰度图的平均像素颜色值
    public static int getAverageOfPixelArray(int[] pixels) {
        Color color;
        long sumRed = 0;
        for (int i = 0; i < pixels.length; i++) {
            color = new Color(pixels[i], true);
            sumRed += color.getRed();
        }
        int averageRed = (int) (sumRed / pixels.length);
        return averageRed;
    }

    // 获取灰度图的像素比较数组（平均值的离差）
    public static int[] getPixelDeviateWeightsArray(int[] pixels, final int averageColor) {
        Color color;
        int[] dest = new int[pixels.length];
        for (int i = 0; i < pixels.length; i++) {
            color = new Color(pixels[i], true);
            dest[i] = color.getRed() - averageColor > 0 ? 1 : 0;
        }
        return dest;
    }

    // 获取两个缩略图的平均像素比较数组的汉明距离（距离越大差异越大）
    public static int getHammingDistance(int[] a, int[] b) {
        int sum = 0;
        for (int i = 0; i < a.length; i++) {
            sum += a[i] == b[i] ? 0 : 1;
        }
        return sum;
    }

    // 通过汉明距离计算相似度
    public static double calSimilarity(int hammingDistance) {
        int length = 32 * 32;
        double similarity = (length - hammingDistance) / (double) length;

        // 使用指数曲线调整相似度结果
        similarity = Math.pow(similarity, 2);
        return similarity;
    }


}
