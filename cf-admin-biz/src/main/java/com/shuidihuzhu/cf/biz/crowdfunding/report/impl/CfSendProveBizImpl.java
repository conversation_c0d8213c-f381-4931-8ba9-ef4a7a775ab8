package com.shuidihuzhu.cf.biz.crowdfunding.report.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.report.CfSendProveBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfSendProveDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfSendProve;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-09-17 20:24
 **/
@Service
public class CfSendProveBizImpl implements CfSendProveBiz {
    @Autowired
    private CfSendProveDao cfSendProveDao;

    @Override
    public int insertOne(CfSendProve cfSendProve) {
        return cfSendProveDao.insertOne(cfSendProve);
    }

    @Override
    public CfSendProve getById(long id) {
        return cfSendProveDao.getById(id);
    }

    @Override
    public int auditPictureUrl(long id, String pictureUrl, int pictureAuditStatus, String pictureRejectedReason) {
        return cfSendProveDao.auditPictureUrl(id, pictureUrl, pictureAuditStatus, pictureRejectedReason);
    }

    @Override
    public int updatePictureAuditStatusById(long id, int pictureAuditStatus) {
        return cfSendProveDao.updatePictureAuditStatusById(id, pictureAuditStatus);
    }

    @Override
    public CfSendProve getLastOneByCaseId(int caseId) {
        return cfSendProveDao.getLastOneByCaseId(caseId);
    }

    @Override
    public int updateRejectedReason(long id, String rejectedReason) {
        return cfSendProveDao.updateRejectedReason(id, rejectedReason);
    }

    @Override
    public int updateCancelReason(long id, String cancelReason) {
        return cfSendProveDao.updateCancelReason(id, cancelReason);
    }
}
