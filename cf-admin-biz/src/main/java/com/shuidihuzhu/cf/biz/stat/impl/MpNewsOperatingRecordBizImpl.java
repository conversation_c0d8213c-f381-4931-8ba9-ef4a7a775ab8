package com.shuidihuzhu.cf.biz.stat.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.stat.MpNewsOperatingRecordBiz;
import com.shuidihuzhu.cf.dao.stat.message.MpNewsOperatingRecordDao;
import com.shuidihuzhu.cf.model.message.MpNewsOperatingRecord;
import com.shuidihuzhu.cf.model.message.MpNewsOperatingTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class MpNewsOperatingRecordBizImpl implements MpNewsOperatingRecordBiz {
    @Autowired
    private MpNewsOperatingRecordDao mpNewsOperatingRecordDao;

    /**
     * @param articleTaskId
     * @param userId
     * @param userName
     * @param comment       对操作行的描述
     */
    @Override
    public void saveOperateRecord(long articleTaskId, int userId, String userName, String comment, long subtaskId,
                                  MpNewsOperatingTypeEnum type) {
        try {
            MpNewsOperatingRecord mpnewsOperatingRecord = new MpNewsOperatingRecord();
            mpnewsOperatingRecord.setUserId(userId);
            mpnewsOperatingRecord.setUserName(userName);
            mpnewsOperatingRecord.setAssignmentId(articleTaskId);
            mpnewsOperatingRecord.setComment(comment);
            mpnewsOperatingRecord.setSubtaskId(subtaskId);
            mpnewsOperatingRecord.setType(type == null ? 0 : type.getCode());
            mpNewsOperatingRecordDao.insertOperatingRecord(mpnewsOperatingRecord);
        } catch (Exception e) {
            log.error("保存操作记录失败", e);
        }
    }

    @Override
    public List<MpNewsOperatingRecord> getByTaskId(long taskId, int current, int pageSize) {
        return mpNewsOperatingRecordDao.getByTaskId(taskId, (current - 1) * pageSize, pageSize);
    }

    @Override
    public int countOperatingRecordByTaskId(long taskId) {
        if (taskId < 0) {
            return 0;
        }
        Integer total = mpNewsOperatingRecordDao.countOperatingRecordByTaskId(taskId);
        return total == null ? 0 : total.intValue();
    }

    @Override
    public List<MpNewsOperatingRecord> getByTaskIdAndType(long taskId, MpNewsOperatingTypeEnum type, int offset, int limit) {

        return mpNewsOperatingRecordDao.getByTaskIdAndType(taskId, type.getCode(), offset, limit);
    }

    @Override
    public List<MpNewsOperatingRecord> getByUserNameAndType(String userName, MpNewsOperatingTypeEnum type, int offset, int limit) {

        return mpNewsOperatingRecordDao.getByUserNameAndType(userName, type.getCode(), offset, limit);
    }
}