package com.shuidihuzhu.cf.biz.mina.impl;

import com.github.pagehelper.PageHelper;
import com.shuidihuzhu.cf.biz.mina.AdminCfTopicCommentBiz;
import com.shuidihuzhu.cf.dao.mina.AdminCfTopicCommentDao;
import com.shuidihuzhu.cf.delegate.other.IMiniAppDelegate;
import com.shuidihuzhu.cf.model.miniprogram.CfTopicComment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR> Ahrievil
 */
@Service
public class AdminCfTopicCommentBizImpl implements AdminCfTopicCommentBiz {

    @Autowired
    private AdminCfTopicCommentDao adminCfTopicCommentDao;
    @Autowired
    private IMiniAppDelegate miniAppDelegate;

    @Override
    public List<CfTopicComment> selectByPage(Long commentId, Integer topicId, String comment, Integer commentUserId, Integer isSensitiveWord,
                                             Timestamp beginTime, Timestamp endTime, int current, int pageSize, Integer orderType) {
        PageHelper.startPage(current, pageSize);
        //排序  1位按照时间  2位按照话题id  默认按照时间
        return adminCfTopicCommentDao.selectByPage(commentId, topicId, comment, commentUserId, isSensitiveWord, beginTime, endTime, orderType);
    }

    @Override
    public CfTopicComment selectById(long id) {
        return miniAppDelegate.selectCfTopicCommentById(id);
    }

    @Override
    public int deleteById(long id) {
        return miniAppDelegate.deleteCfTopicCommentById(id);
    }

    @Override
    public int deleteByGroupId(long groupId) {
        return adminCfTopicCommentDao.deleteByGroupId(groupId);
    }

    @Override
    public int deleteByTopicId(long topicId) {
        return adminCfTopicCommentDao.deleteByTopicId(topicId);
    }

}
