package com.shuidihuzhu.cf.biz.crowdfunding.report;

import com.shuidihuzhu.cf.model.report.CfReportDisposeAction;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AdminCfReportDisposeActionBiz {

    int add(CfReportDisposeAction cfReportDisposeAction);

    int updateDisposeAction(String disposeAction, long actionClassifyId, long id, boolean isHelp, boolean hasTemplate);

    int updateIsUse(int isUse, long id);

    List<CfReportDisposeAction> getAll();

    List<CfReportDisposeAction> getByActionClassifyId(long actionClassifyId);

    List<CfReportDisposeAction> getByUse(int isUse);

    List<CfReportDisposeAction> selectByIds(List<Long> ids);

    CfReportDisposeAction getById(Long id);

}
