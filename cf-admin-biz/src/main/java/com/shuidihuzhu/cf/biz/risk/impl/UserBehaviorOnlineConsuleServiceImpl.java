package com.shuidihuzhu.cf.biz.risk.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.utils.CryptoUserIdUtil;
import com.shuidihuzhu.cf.biz.customer.*;
import com.shuidihuzhu.cf.biz.risk.IUserBehaviorService;
import com.shuidihuzhu.cf.customer.CfChatRecordDO;
import com.shuidihuzhu.cf.customer.CfUdeskCustomerInfo;
import com.shuidihuzhu.cf.enums.crowdfunding.UserBehaviorEnum;
import com.shuidihuzhu.cf.model.crowdfunding.AdminUserBehaviorDetail;
import com.shuidihuzhu.cf.model.crowdfunding.UserInfoDetail;
import com.shuidihuzhu.cf.util.crowdfunding.TimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @time 2019/10/16 上午11:51
 * @desc 用户在线咨询
 */
@Slf4j
@Service
public class UserBehaviorOnlineConsuleServiceImpl implements IUserBehaviorService {
    @Autowired
    private SeaChatHistoryBiz seaChatHistoryBiz;
    @Autowired
    private CfUdeskCustomerInfoBiz udeskCustomerInfoBiz;
    @Autowired
    private CfUdeskSessionRecordBiz udeskSessionRecordBiz;

    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");


    @Override
    public List<AdminUserBehaviorDetail> buildBehaviorDetail(String mobile, long userId, String name, String identity, UserInfoModel userInfo, UserBehaviorEnum behaviorEnum) {
        List<CfChatRecordDO> chatRecordDOS = Lists.newArrayList();
        List<CfChatRecordDO> zhiciChatRecords = seaChatHistoryBiz.queryRecordByUserId(userId);
        for (CfChatRecordDO zhiciChatRecord : zhiciChatRecords) {
            zhiciChatRecord.setChannelFrom(CfChatRecordDO.ChannelFrom.ZHICI.getCode());
        }
        chatRecordDOS.addAll(zhiciChatRecords);
        List<CfUdeskCustomerInfo> udeskCustomerInfos = udeskCustomerInfoBiz.getUdeskCustomersByCfUserIds(Lists.newArrayList(userId));
        Set<Integer> udeskUserIds = udeskCustomerInfos.stream().map(CfUdeskCustomerInfo::getUdeskCustomerId).collect(Collectors.toSet());
        List<CfChatRecordDO> udeskRecords = udeskSessionRecordBiz.listRecordByUdskIds(udeskUserIds);
        for (CfChatRecordDO udeskRecord : udeskRecords) {
            udeskRecord.setChannelFrom(CfChatRecordDO.ChannelFrom.UDESK.getCode());
        }
        chatRecordDOS.addAll(udeskRecords);
        if(CollectionUtils.isEmpty(chatRecordDOS)){
            return Lists.newArrayList();
        }

        UserInfoDetail userInfoDetail = new UserInfoDetail();
        userInfoDetail.setUserId(userId);
        userInfoDetail.setNickName(Objects.nonNull(userInfo) ? userInfo.getNickname() : "");

        List<AdminUserBehaviorDetail> records = Lists.newArrayList();

        for (CfChatRecordDO cfChatRecordDO : chatRecordDOS){

            StringBuilder sb = new StringBuilder();
            sb.append("会话开始时间:").append(sdf.format(cfChatRecordDO.getStartTime())).append(REDEX);
            sb.append("会话结束时间:").append(sdf.format(cfChatRecordDO.getEndTime())).append(REDEX);
            sb.append("会话时长").append(TimeUtils.timeFormat(cfChatRecordDO.getConversationDuration())).append(REDEX);
            sb.append("处理人:").append(cfChatRecordDO.getStaffName()).append(REDEX);

            AdminUserBehaviorDetail record = new AdminUserBehaviorDetail();
            record.setTime(cfChatRecordDO.getStartTime());
            record.setBehaviorType(UserBehaviorEnum.ONLINE_CONSULE.getKey());
            record.setUserInfoDetail(userInfoDetail);
            record.setUrl(Lists.newArrayList());
            record.setBehavoir(Lists.newArrayList(sb.toString().split(REDEX)));
            record.setCId(cfChatRecordDO.getCId());

            Map<String, Object> extInfo = Maps.newHashMap();
            extInfo.put("cid", cfChatRecordDO.getCId());
            extInfo.put("channelFrom", cfChatRecordDO.getChannelFrom());
            record.setExtInfo(extInfo);

            records.add(record);
        }
        return records;
    }

    @Override
    public UserBehaviorEnum getBehavior() {
        return UserBehaviorEnum.ONLINE_CONSULE;
    }


}
