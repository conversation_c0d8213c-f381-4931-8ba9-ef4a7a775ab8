package com.shuidihuzhu.cf.biz.crowdfunding.report.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.report.AdminCfReportStrategyCallRecordBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfReportStrategyCallRecordDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Auther: subing
 * @Date: 2020/6/24
 */
@Service
public class AdminCfReportStrategyCallRecordBizImpl implements AdminCfReportStrategyCallRecordBiz {

    @Autowired
    private AdminCfReportStrategyCallRecordDao adminCfReportStrategyCallRecordDao;

    @Override
    public int addInfo(int caseId, long workOrderId, String allQuestionModule, String allFundraiserModule,
                       String unKnowFundraiserModule, String lastCurrentRoleModule, String name, String organization) {
        return adminCfReportStrategyCallRecordDao.addInfo(caseId, workOrderId, allQuestionModule, allFundraiserModule, unKnowFundraiserModule, lastCurrentRoleModule, name, organization);
    }
}
