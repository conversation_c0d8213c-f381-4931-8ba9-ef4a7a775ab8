package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.AdminCfCaseVisitConfig;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminOperatorVo;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.risk.model.CfRiskOperateLimitExtParam;
import com.shuidihuzhu.client.cf.risk.model.enums.RiskOperateSourceEnum;
import com.shuidihuzhu.client.cf.risk.model.enums.UserOperationEnum;
import com.shuidihuzhu.common.web.model.Response;

import java.util.Date;
import java.util.List;

/**
 *
 * 控制案例访问
 *
 * Created by wangsf on 18/5/20.
 */
public interface AdminCaseVisitConfigBiz {


	int add(AdminCfCaseVisitConfig config);

	int update(AdminCfCaseVisitConfig config);

	AdminCfCaseVisitConfig get(int caseId);

	List<AdminCfCaseVisitConfig> getList(int current, int pageSize);

	List<AdminCfCaseVisitConfig> getList(int current, int pageSize,Integer caseId,Integer type,Integer operatorId,String start,String end);

	int add(AdminCfCaseVisitConfig config,boolean dynamicFlag,boolean titleFlag);

	int update(AdminCfCaseVisitConfig config,boolean dynamicFlag,boolean titleFlag);

	boolean saveAbnormalHidden(int caseId, Boolean hidden, String selfTitle, String otherTitle, int userId);

	List<AdminOperatorVo> getOperatorList();

	/**
	 * @author: wanghui
	 * @time: 2018/10/25 12:12 PM
	 * @description: updateCanShowByCaseId
	 * @param: [id, canShow]
	 * @return: int
	 */
	OpResult updateCanShowByCaseId(Integer caseId, Integer canShow);

	/**
	 * @author: wanghui
	 * @time: 2018/10/25 12:31 PM
	 * @description: updateBannerTextAndStartEndTime
	 * @param: [caseId, bannerText, startTime, endTime]
	 * @return: int 
	 */
	OpResult updateBannerTextAndStartEndTime(Integer caseId,String bannerText, Date startTime, Date endTime);

	Response<Void> judeRiskCase(int caseId, int adminUserId, boolean action, long riskUserId, UserOperationEnum userOpEnum, RiskOperateSourceEnum operateSourceEnum, CfRiskOperateLimitExtParam extParam);
}
