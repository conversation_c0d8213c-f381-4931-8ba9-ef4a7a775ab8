package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.NewCfRefundConstant;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord;
import com.shuidihuzhu.cf.model.crowdfunding.NewCfRefundRecord;

import java.util.List;
import java.util.Map;

/**
 * Created by Ahrievil on 2017/9/8
 */
public interface AdminCrowdfundingPayRecordBiz {

    List<CrowdfundingPayRecord> getPaySuccessByOrderIds(List<Long> orderIds);

    Map<Long, CrowdfundingPayRecord> getPaySuccessMapping(List<Long> orderIds);

    List<CrowdfundingPayRecord> getPaySuccessByPayUids(List<String> payUids);

    CrowdfundingPayRecord getByPayUid(String payUid);

    List<CrowdfundingPayRecord> listSuccessByOrderIds(List<Long> orderIds);

    List<CrowdfundingPayRecord> selectByOrderIdList(List<Long> list, int start, int size);

    List<CrowdfundingPayRecord> getPaySuccessByOrderIdsAndRefundStatus(List<Long> orderIds,
                                                                       List<NewCfRefundConstant.RefundStatus> refundStatusList);

    List<CrowdfundingPayRecord> getPaySuccessByOrderIdsFeign(List<Long> orderIds);

    List<CrowdfundingPayRecord> getPaySuccessByPayUidsFeign(List<String> payUids);

    CrowdfundingPayRecord getByPayUidFeign(String payUid);

    List<CrowdfundingPayRecord> listSuccessByOrderIdsFeign(List<Long> orderIds);

    List<CrowdfundingPayRecord> selectByOrderIdListFeign(List<Long> list, int start, int size);

    List<CrowdfundingPayRecord> getPaySuccessByOrderIdsAndRefundStatusFeign(List<Long> orderIds, List<NewCfRefundConstant.RefundStatus> refundStatusList);
}
