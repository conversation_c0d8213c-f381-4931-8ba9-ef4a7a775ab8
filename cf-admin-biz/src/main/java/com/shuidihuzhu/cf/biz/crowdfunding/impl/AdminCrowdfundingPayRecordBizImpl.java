package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingPayRecordBiz;
import com.shuidihuzhu.cf.client.feign.CrowdfundingPayRecordFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.enums.crowdfunding.NewCfRefundConstant;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * Created by Ahrievil on 2017/9/8
 */
@Service
@Slf4j
@RefreshScope
public class AdminCrowdfundingPayRecordBizImpl implements AdminCrowdfundingPayRecordBiz {

	@Autowired
	CrowdfundingPayRecordFeignClient crowdfundingPayRecordFeignClient;



	@Override
	public List<CrowdfundingPayRecord> getPaySuccessByOrderIds(List<Long> orderIds) {
		if (CollectionUtils.isEmpty(orderIds)) {
			return Lists.newArrayList();
		}
		return this.getPaySuccessByOrderIdsFeign(orderIds);
	}

	@Override
	public Map<Long, CrowdfundingPayRecord> getPaySuccessMapping(List<Long> orderIds) {

		List<CrowdfundingPayRecord> records = getPaySuccessByOrderIds(orderIds);

		Map<Long, CrowdfundingPayRecord> payMapping = Maps.newHashMap();
		for (CrowdfundingPayRecord payRecord : records) {
			payMapping.put(payRecord.getCrowdfundingOrderId(), payRecord);
		}
		return payMapping;
	}



	@Override
	public List<CrowdfundingPayRecord> getPaySuccessByPayUids(List<String> payUids) {
		if (CollectionUtils.isEmpty(payUids)) {
			return Lists.newArrayList();
		}
		return this.getPaySuccessByPayUidsFeign(payUids);
	}


	@Override
	public CrowdfundingPayRecord getByPayUid(String payUid) {
		if (StringUtils.isEmpty(payUid)) {
			return null;
		}
		return this.getByPayUidFeign(payUid);
	}


	@Override
	public List<CrowdfundingPayRecord> listSuccessByOrderIds(List<Long> orderIds) {
		if (CollectionUtils.isEmpty(orderIds)) {
			return Collections.emptyList();
		}
		return this.listSuccessByOrderIdsFeign(orderIds);
	}


	@Override
	public List<CrowdfundingPayRecord> selectByOrderIdList(List<Long> list, int start, int size) {
		return this.selectByOrderIdListFeign(list, start, size);
	}


	@Override
	public List<CrowdfundingPayRecord> getPaySuccessByOrderIdsAndRefundStatus(List<Long> orderIds, List<NewCfRefundConstant.RefundStatus> refundStatusList) {
		if (CollectionUtils.isEmpty(orderIds)) {
			return Lists.newArrayList();
		}
		if (CollectionUtils.isEmpty(refundStatusList)) {
			refundStatusList = Lists.newArrayList(NewCfRefundConstant.RefundStatus.values());
		}
		return this.getPaySuccessByOrderIdsAndRefundStatusFeign(orderIds, refundStatusList);
	}
	@Override
	public List<CrowdfundingPayRecord> getPaySuccessByOrderIdsFeign(List<Long> orderIds) {
		if (CollectionUtils.isEmpty(orderIds)) {
			return Lists.newArrayList();
		}
		FeignResponse<List<CrowdfundingPayRecord>> feignResponse = FeignResponse.fallback();
		try{
			feignResponse = crowdfundingPayRecordFeignClient.getPaySuccessByOrderIds(orderIds);
		}catch (Exception e){
			log.error(this.getClass().getSimpleName()+"  crowdfundingPayRecordFeignClient.getPaySuccessByOrderIds  err:",e);
		}
		return feignResponse.ok()?feignResponse.getData():Lists.newArrayList();
	}
	@Override
	public List<CrowdfundingPayRecord> getPaySuccessByPayUidsFeign(List<String> payUids) {
		if (CollectionUtils.isEmpty(payUids)) {
			return Lists.newArrayList();
		}
		FeignResponse<List<CrowdfundingPayRecord>> feignResponse = FeignResponse.fallback();
		try{
			feignResponse = crowdfundingPayRecordFeignClient.getPaySuccessByPayUids(payUids);
		}catch (Exception e){
			log.error(this.getClass().getSimpleName()+"  crowdfundingPayRecordFeignClient.getPaySuccessByPayUids  err:",e);
		}
		return feignResponse.ok()?feignResponse.getData():Lists.newArrayList();
	}
	@Override
	public CrowdfundingPayRecord getByPayUidFeign(String payUid) {
		if (StringUtils.isEmpty(payUid)) {
			return null;
		}
		FeignResponse<CrowdfundingPayRecord> feignResponse = FeignResponse.fallback();
		try{
			feignResponse = crowdfundingPayRecordFeignClient.getByPayUid(payUid);
		}catch (Exception e){
			log.error(this.getClass().getSimpleName()+"  crowdfundingPayRecordFeignClient.getByPayUid  err:",e);
		}
		return feignResponse.ok()?feignResponse.getData():null;
	}
	@Override
	public List<CrowdfundingPayRecord> listSuccessByOrderIdsFeign(List<Long> orderIds) {
		if (CollectionUtils.isEmpty(orderIds)) {
			return Collections.emptyList();
		}
		FeignResponse<List<CrowdfundingPayRecord>> feignResponse = FeignResponse.fallback();
		try{
			feignResponse = crowdfundingPayRecordFeignClient.getPaySuccessByOrderIdsAndRefundStatus(orderIds,Lists.newArrayList(NewCfRefundConstant.RefundStatus.UNHANDLE,NewCfRefundConstant.RefundStatus.HANDLE_FAILED));
		}catch (Exception e){
			log.error(this.getClass().getSimpleName()+"  crowdfundingPayRecordFeignClient.getPaySuccessByOrderIdsAndRefundStatus  err:",e);
		}
		return feignResponse.ok()?feignResponse.getData():Lists.newArrayList();
	}
	@Override
    public List<CrowdfundingPayRecord> selectByOrderIdListFeign(List<Long> list, int start, int size) {
		FeignResponse<List<CrowdfundingPayRecord>> feignResponse = FeignResponse.fallback();
		try{
			feignResponse = crowdfundingPayRecordFeignClient.selectByOrderIdList(list, start, size);
		}catch (Exception e){
			log.error(this.getClass().getSimpleName()+"  crowdfundingPayRecordFeignClient.getPaySuccessByOrderIdsAndRefundStatus  err:",e);
		}
		return feignResponse.ok()?feignResponse.getData():Lists.newArrayList();
    }

	@Override
	public List<CrowdfundingPayRecord> getPaySuccessByOrderIdsAndRefundStatusFeign(List<Long> orderIds, List<NewCfRefundConstant.RefundStatus> refundStatusList) {
		if (CollectionUtils.isEmpty(orderIds)) {
			return Lists.newArrayList();
		}
		if (CollectionUtils.isEmpty(refundStatusList)) {
			refundStatusList = Lists.newArrayList(NewCfRefundConstant.RefundStatus.values());
		}
		FeignResponse<List<CrowdfundingPayRecord>> feignResponse = FeignResponse.fallback();
		try{
			feignResponse = crowdfundingPayRecordFeignClient.getPaySuccessByOrderIdsAndRefundStatus(orderIds, refundStatusList);
		}catch (Exception e){
			log.error(this.getClass().getSimpleName()+"  crowdfundingPayRecordFeignClient.getPaySuccessByOrderIdsAndRefundStatus  err:",e);
		}
		return feignResponse.ok()?feignResponse.getData():Lists.newArrayList();
	}
}
