package com.shuidihuzhu.cf.biz.crowdfunding;


import com.shuidihuzhu.cf.client.adminpure.model.initial.CfVolunteerRiskRecord;

import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2022/1/11 10:44
 * @Description:
 */
public interface CfVolunteerRiskRecordBiz {
    int insert(CfVolunteerRiskRecord cfVolunteerRiskRecord);

    List<CfVolunteerRiskRecord> getByCaseIdList(List<Integer> caseIdList);

    List<CfVolunteerRiskRecord> getByCaseId(int caseId);
}
