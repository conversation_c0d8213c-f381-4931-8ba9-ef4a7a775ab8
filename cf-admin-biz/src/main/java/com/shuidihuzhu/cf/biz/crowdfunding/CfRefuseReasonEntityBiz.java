package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonCustomEntity;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonTag;
import com.shuidihuzhu.cf.model.crowdfunding.material.AuditSuggestModifyDetail;
import com.shuidihuzhu.cf.model.crowdfunding.material.CfRefuseSuggestModify;
import com.shuidihuzhu.cf.model.crowdfunding.materialAudit.AuditSuggestModifyParam;
import com.shuidihuzhu.cf.model.crowdfunding.materialAudit.CfRefuseModifyVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestParam;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by Ahrievil on 2017/7/26
 */
public interface CfRefuseReasonEntityBiz {
    int insertOne(CfRefuseReasonEntity cfRefuseReasonEntity);
    List<CfRefuseReasonEntity> selectByIds(List<Integer> list);
    int frequencyPlusOne(Set set);
    List<CfRefuseReasonEntity> selectAll(int start, int size);
    int deleteOne(int id);
    CfRefuseReasonEntity selectById(int id, Integer isDelete);
    void sendFrequency(Timestamp begin, Timestamp end);
    List<CfRefuseReasonEntity> selectByReasonIds(Set<Integer> set, Integer dataStatus);

    int updateDeleteStatus(int id, int deleteStatus);

    List<CfRefuseReasonEntity> selectByTagIdAndDeleteStatus(int tagId, int deleteStatus);

    void editReasonEntityUseScene(CfRefuseModifyVo modifyVo);

    void editChoiceRelationId(int userId, int entityId, String relationIds);

    CfRefuseReasonEntity queryCanRelationEntityById(int sourceId, int relationId);
    List<CfRefuseReasonEntity> queryAllRelationEntitys(int entityId);
    void addReasonEntity(int userId, CfRefuseReasonEntity reasonEntity, String useSceneIds, int riskLabelRelated, String riskLabelIdList);
    void editEntityDeleteStatus(int userId, int entityId, int deleteStatus);

    void editEntityRank(int userId, int upId, int downId, int operateType, int useScene);

    /**
     *
     * @param tagId
     * @param deleteStatus
     * @param useScene
     * @param rejectPage  初审驳回页和材料审核驳回页 不需要查询 c端驳回位置 和 使用场景
     * @return
     */
    List<CfRefuseReasonEntity> queryRefuseEntityByDelStatus(int tagId, int deleteStatus, int useScene, boolean rejectPage);

    // 查找某项材料的驳回项
    List<CfRefuseReasonTag> getRefuseListByType(int dataType, int useScene);

    List<CfRefuseSuggestModify> querySuggestByEntityIds(List<Integer> entityIds);

    AuditSuggestModifyDetail querySuggestModifyDetail(AuditSuggestModifyParam param);

    Set<Integer> queryDataTypeByRejectIds(List<Integer> rejectIds);

    int insertCfRefuseReasonCustomEntity(CfRefuseReasonCustomEntity cfRefuseReasonCustomEntity);

    List<CfRefuseReasonCustomEntity> getCustomByCaseId(int caseId);

    List<CfRefuseReasonCustomEntity> getCustomByWorkOrderId(long workOrderId);

    int updateStatusByCaseIdAndRefuseId(CfRefuseReasonCustomEntity cfRefuseReasonCustomEntity);

    /**
     * 根据案例材料版本获取驳回理由
     * @param materialPlanId
     * @return
     */
    List<Integer> getRefuseReasonEntitiesByMaterialPlanId(int materialPlanId);

    /**
     * 获取驳回理由所属的案例材料版本
     * @return
     */
    Map<String, Integer> getMaterialPlanIdsOfEntities(List<Integer> entityIds);

    /**
     * 绑定驳回理由和案例材料版本
     */
    boolean bindEntityToMaterial(int entityId, int materialPlanId);

    /**
     * 批量绑定驳回理由和案例材料版本
     * @param mapping key: entityId value: materialPlanId
     */
    boolean bindEntityToMaterialBatch(Map<String, String> mapping);

    boolean deleteBindingRelation(int id);

    boolean updateBindingRelation(int id, int newPlanId, int newEntityId);
}
