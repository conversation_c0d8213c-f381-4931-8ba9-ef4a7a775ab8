package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.crowdfunding.AdminTaskUgc;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminUserCommentVo;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by Ahrievil on 2017/11/30
 */
public interface AdminTaskUgcBiz {

    int insertOne(AdminTaskUgc adminTaskUgc);

    int insertList(List<AdminTaskUgc> list);

    List<AdminTaskUgc> selectByWorkOrderIds(List<Long> list);

    List<AdminTaskUgc> selectModulesLimit(int modules, int start, int size);

    AdminTaskUgc selectByWorkOrderId(long workOrderId);

    int updateResult(long id, int result);

    int deleteByWordIdList(List<Long> list);

    AdminTaskUgc selectFirstByCaseId(long caseId);

    String selectFirstCommentByCaseId (long caseId);

    AdminUserCommentVo getCommentList(long wordId,long workOrderId,int caseId,int contentType);

    List<AdminTaskUgc> selectByCreateTimeAndTaskType(Date beginDate, Date endDate, int contentType, int limit);

    List<AdminTaskUgc> selectByUpdateTimeAndTaskStatus(Date beginDate, Date endDate,
                                                       int contentType, List<Integer> resultStatus, int limit);

    AdminTaskUgc selectLatelyTaskByCaseIdAndContent(int caseId, int contentType);

    Map<String, Object> countUnHandleBaseInfoOrder();

    List<AdminWorkOrder> getFirstApprovePassWorkOrder(List<AdminWorkOrder> baseInfoUgcOrders);

}
