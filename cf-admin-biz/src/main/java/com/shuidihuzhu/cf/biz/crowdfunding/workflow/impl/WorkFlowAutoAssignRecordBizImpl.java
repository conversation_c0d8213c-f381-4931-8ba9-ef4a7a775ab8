package com.shuidihuzhu.cf.biz.crowdfunding.workflow.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.admin.AdminOrganizationBiz;
import com.shuidihuzhu.cf.biz.admin.AdminWorkOrderBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderClassifySettingsBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderFlowBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderFlowRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.workflow.StaffStatusBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.workflow.WorkFlowAutoAssignRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.workflow.WorkFlowTypePropertyBiz;
import com.shuidihuzhu.cf.dao.admin.AdminWorkOrderDao;
import com.shuidihuzhu.cf.dao.admin.AdminWorkOrderFlowDao;
import com.shuidihuzhu.cf.dao.admin.workorder.flowWorkOrder.WorkFlowAutoAssignRecordDAO;
import com.shuidihuzhu.cf.delegate.SeaAccountDelegate;
import com.shuidihuzhu.cf.delegate.alarmbot.AlarmBotDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminWorkOrderFlowConst;
import com.shuidihuzhu.cf.model.admin.AdminOrganization;
import com.shuidihuzhu.cf.model.admin.AdminOrganizationUserMap;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderFlow;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderFlowRecord;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowStatistics;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView;
import com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowAutoAssignRecord;
import com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowStaffStatus;
import com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowTypeProperty;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.cf.service.RedisLockService;
import com.shuidihuzhu.cf.service.crowdfunding.WorkFlowStaffService;
import com.shuidihuzhu.cf.service.workorder.WorkFlowOrderCommonService;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderTypeFeignClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.ClassifyTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OneTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RefreshScope
public class WorkFlowAutoAssignRecordBizImpl implements WorkFlowAutoAssignRecordBiz {

    @Autowired
    private WorkFlowAutoAssignRecordDAO assignRecordDAO;
    @Autowired
    private WorkFlowTypePropertyBiz typePropertyBiz;
    @Autowired
    private StaffStatusBiz flowStatusBiz;
    @Autowired
    private AdminWorkOrderFlowDao adminWorkOrderFlowDao;
    @Autowired
    private AdminOrganizationBiz orgIdBiz;
    @Autowired
    private AdminWorkOrderBiz adminWorkOrderBiz;
    @Resource(name = "cfRedissonHandler")
    private RedissonHandler cfRedissonHandler;
    @Value("${apollo.can.auto.assign.flow.work:1}")
    private int canAutoAssignFlowWorkSwitch;
    @Autowired
    private AdminWorkOrderFlowRecordBiz flowRecordBiz;

    @Autowired
    private SeaAccountDelegate seaAccountDelegate;

    @Autowired
    private AdminWorkOrderClassifySettingsBiz classifySettingsBiz;

    @Autowired
    private AlarmBotDelegate alarmBotDelegate;

    @Autowired
    private WorkFlowOrderCommonService flowOrderCommonService;

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;
    @Autowired
    private RedisLockService redisLockService;

    @Autowired
    private ApplicationService applicationService;

    @Autowired
    private AdminWorkOrderFlowBiz adminWorkOrderFlowBiz;

    @Autowired
    private CfWorkOrderTypeFeignClient cfWorkOrderTypeFeignClient;

    private static String FLOW_WORK_AUTO_ASSIGN_LOCK_NAME = "FLOW_WORK_AUTO_ASSIGN_LOCK_NAME";

    // 创建工单、分配工单
    @Override
    public void assignWorkTOrg(int orgId, Integer sourceOperatorId) {

        if (!canAutoAssignWork() || orgId != getCustomServiceOrgId() || (sourceOperatorId != null && sourceOperatorId != 0)) {
            log.info("当前工单不是二线客服或工单已分配到具体的人.orgId:{} operatorId:{}", orgId, sourceOperatorId);
            return ;
        }
        assignWorkTOrg(orgId);
    }

    // 修改了最大待处理工单数的阈值
    public void assignWorkTOrg(int orgId) {

        if (!canAutoAssignWork() || orgId != getCustomServiceOrgId()) {
            return;
        }

        List<Integer> operatorsIds = getAssignPriorityUserIds(orgId);
        if (CollectionUtils.isEmpty(operatorsIds)) {
            log.info("不能找到有效的用户.不自动分配工单");
            return;
        }

        actualAssignMappings(operatorsIds);
    }

    // 用户上线、 处理中、无需处理、处理完成、分配
    @Override
    public void assignWorkTUser(int userId) {

        triggerAutoAssignReportOrderWithLock();

        // 判断用户是不是二线组织
        if (!canAutoAssignWork() || !isOrgUser(userId)) {
            log.info("不满足条件不给用户分配工单。userId:{}", userId);
            return;
        }
        // 且用户当前待处理工单数量没有到阈值
//        Map<Integer, Integer> noHandleMap = countHandingMap(Lists.newArrayList(userId));
//        Integer noHandleLimit = getFlowNoHandleLimit(getCustomServiceOrgId());
//        if (!inWaitForHandleLimit(noHandleMap.get(userId), noHandleLimit)) {
//            log.info("当前用户的待处理工单数已经超过限制。userId:{} currentHandle:{}, limit:{}", userId,
//                    noHandleMap.get(userId), noHandleLimit);
//            return;
//        }
//        actualAssignMappings(Lists.newArrayList(userId));
        // 对二线客服的自动分配
        this.assignWorkTOrg(getCustomServiceOrgId());
    }

    private List<Integer> getAssignPriorityUserIds(int orgId) {
        return getAssignPriorityUserIds(orgId, null);
    }
    /**
     * 获取指定组织下可以分配工单的人员
     *
     * @param orgId
     * @return
     */
    private List<Integer> getAssignPriorityUserIds(int orgId, @Nullable Integer noHandleLimit) {

        List<Integer> priorityUserIds = Lists.newArrayList();

        // 组织下的 用户
        List<Long> allUsers = orgIdBiz.getOrgUserByOrgIds(Lists.newArrayList(orgId)).stream().map(
                AdminOrganizationUserMap::getUserId).map(Long::valueOf).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(allUsers)) {
            log.info("当前组织下没有用户不自动分配工单。orgId:{}", orgId);
            return priorityUserIds;
        }

        // 获取在线的用户
        List<Integer> onlineUserIds = flowStatusBiz.listByUserIds(allUsers, WorkFlowStaffStatus.StaffStatusEnum.online.getCode())
                .stream().map(WorkFlowStaffStatus::getUserId).map(String::valueOf).map(Integer::valueOf).collect(Collectors.toList());
        log.info("当前在线的用户userId:{}", onlineUserIds);
        if (CollectionUtils.isEmpty(onlineUserIds)) {
            log.info("当前组织下没有在线用户不自动分配工单。orgId:{}", orgId);
            return priorityUserIds;
        }

        // 用户当日分配处理的数量
        List<WorkFlowAutoAssignRecord> assignRecords = assignRecordDAO.selectByUserIdsAndDate(onlineUserIds, DateUtil.getYMDStringByDate(new Date()));
        log.info("今天所有用户的领取记录:{}", JSON.toJSONString(assignRecords));
        // 工单数量  领取时间 排序
        if (CollectionUtils.isNotEmpty(assignRecords)) {
            Collections.sort(assignRecords, WorkFlowAutoAssignRecord.ASSIGN_COMPARATOR);
        }
        Set<Integer> todayHasAssign = assignRecords.stream().map(WorkFlowAutoAssignRecord::getUserId).collect(Collectors.toSet());

        if (noHandleLimit == null) {
            noHandleLimit = getFlowNoHandleLimit(orgId);
        }
        Map<Integer, Integer> noHandleMap = countHandingMap(onlineUserIds);
        log.info("当前用户的待处理个数:{} limit:{}", noHandleMap, noHandleLimit);
        // 今天还没有领取过工单的 优先领取
        for (Integer currUser : onlineUserIds) {
            if (!todayHasAssign.contains(currUser)
                    && inWaitForHandleLimit(noHandleMap.get(currUser), noHandleLimit)) {
                log.info("用户没有的待处理的工单数没有超过限制, 分配给这个用户.userId:{}", currUser);
                priorityUserIds.add(currUser);
            }
        }

        // 当前待处理的工单的阀值未达到上限
        for (WorkFlowAutoAssignRecord record : assignRecords) {
            if (inWaitForHandleLimit(noHandleMap.get(record.getUserId()), noHandleLimit)) {
                log.info("用户没有的待处理的工单数没有超过限制, 分配给这个用户.userId:{}", record.getUserId());
                priorityUserIds.add(record.getUserId());
            }
        }

        log.info("当前满足分配的优先级用户userIds:{} ", JSON.toJSONString(priorityUserIds));
        return priorityUserIds;
    }

    private boolean isOrgUser(int userId) {

        List<Integer> userOrgIds = orgIdBiz.getLowestOrgByUserIds(Lists.newArrayList(userId)).stream().map(AdminOrganizationUserMap::getOrgId)
                .collect(Collectors.toList());
        return userOrgIds.contains(getCustomServiceOrgId());
    }

    // 未处理的工单个数是否超过限制
    private boolean inWaitForHandleLimit(Integer curHandingNum, Integer noHandLimit) {

        return curHandingNum == null || noHandLimit == null || curHandingNum < noHandLimit;
    }

    // 统计每个用户的待处理的工单数量
    private Map<Integer, Integer> countHandingMap(List<Integer> allOnLineUserIds) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        List<AdminWorkOrderFlowView> flowViewLists = adminWorkOrderFlowDao.selectByOrderStatusAndOperatorsNew(getDate(), allOnLineUserIds
                , Lists.newArrayList(AdminWorkOrderConst.Status.CREATED.getCode()), 0);
        stopwatch.stop();
        log.info("countHandingMap 耗时:{}", stopwatch);

        Map<Integer, Integer> handingMap = Maps.newHashMap();
        for (AdminWorkOrderFlowView flowView : flowViewLists) {

            Integer count = handingMap.get(flowView.getOperatorId());
            if (count == null) {
                count = 1;
            } else {
                ++count;
            }
            handingMap.put(flowView.getOperatorId(), count);
        }
        return handingMap;
    }

    private Integer getFlowNoHandleLimit(int lowestId) {

        List<WorkFlowTypeProperty> typePropertyList = typePropertyBiz.selectPropertyList(WorkFlowTypeProperty
                        .FlowTypeEnum.LOWEST_ORDER_ID.getCode(), lowestId,
                WorkFlowTypeProperty.PropertyTypeEnum.MAX_NO_HANDLE_COUNT.getCode());

        return CollectionUtils.isEmpty(typePropertyList) ? null : Integer.valueOf(typePropertyList.get(0).getPropertyValue());
    }

    @Override
    public boolean canManualAssignOrder(List<Integer> orgIds) {
        Integer jvbaoOrgId = WorkFlowStaffService.orgIdByOrgType.get(WorkFlowStaffStatus.OrgTypeEnum.report.getCode());

        if (orgIds.contains(jvbaoOrgId)) {
            return false;
        }

        return !canAutoAssignWork() || CollectionUtils.isEmpty(orgIds) || !orgIds.contains(getCustomServiceOrgId());
    }

    @Override
    public Map<Integer, Integer> getNoHandleOrgMapping() {

        return typePropertyBiz.getNoHandleOrgMapping(Lists.newArrayList((long) getCustomServiceOrgId()));
    }

    // 传入的userid 需要有优先级。 优先分配的userid在前
    private void actualAssignMappings(List<Integer> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            log.info("userIds 为 空");
            return;
        }
        String identifier = lockForAssign();
        if (StringUtils.isBlank(identifier)) {
            log.info("用户没有获取到锁，不自动分配工单.userId:{}", userIds);
            return;
        }

        try {
            Stopwatch stopwatch = Stopwatch.createStarted();
            List<AdminWorkOrderFlowView> noHandWorkOrders = adminWorkOrderFlowDao.selectByOrderStatusAndOperatorsNew(getDate(), Lists.newArrayList(0)
                    , Lists.newArrayList(AdminWorkOrderConst.Status.CREATED.getCode(), AdminWorkOrderConst.Status.HANDLING.getCode()), getCustomServiceOrgId());
            stopwatch.stop();
            log.info("actualAssignMappings 耗时:{}", stopwatch);

            if (CollectionUtils.isEmpty(noHandWorkOrders)) {
                log.info("没有待处理的信息传递工单,不做分配。userIds:{}", userIds);
                return;
            }

            noHandWorkOrders = judgeJingXi(noHandWorkOrders);
            if (CollectionUtils.isEmpty(noHandWorkOrders)) {
                return;
            }

            Map<Integer, Long> userTFlowIdMapping = Maps.newHashMap();
            Map<Integer, Long> assignMappings = Maps.newHashMap();

            noHandWorkOrders = noHandWorkOrders.stream()
                    .sorted(Comparator.comparing(AdminWorkOrderFlowView::getLevel).reversed()
                            .thenComparing(AdminWorkOrderFlowView::getCreateTime))
                    .collect(Collectors.toList());
            int noHandleIndex = 0;
            for (Integer userId : userIds) {
                if (noHandleIndex < noHandWorkOrders.size()) {
                    // TODO 有必要查询下主库
                    assignMappings.put(userId, noHandWorkOrders.get(noHandleIndex).getWorkOrderId());
                    userTFlowIdMapping.put(userId, noHandWorkOrders.get(noHandleIndex).getId());
                    ++noHandleIndex;
                } else {
                    break;
                }
            }

            if (MapUtils.isEmpty(assignMappings)) {
                return;
            }
            assignOrder(userTFlowIdMapping, assignMappings);

        } catch (Exception e) {
            log.error("自动分配信息传递工单异常.userIds:{}", userIds);
        } finally {
            unLockForAssign(identifier);
        }
    }

    private void assignOrder(Map<Integer, Long> userTFlowIdMapping, Map<Integer, Long> assignMappings) {
        updateOperator(assignMappings);
        log.info("信息传递工单自动分配的映射.mapping:{}", assignMappings);

        typePropertyBiz.addAutoAssignRecordList(userTFlowIdMapping);
        insertAssignRecord(assignMappings.keySet());
    }

    // 更新工单的处理人
    private void updateOperator(Map<Integer, Long> assignMappings) {

        if (MapUtils.isEmpty(assignMappings)) {
            return;
        }

        for (Map.Entry<Integer, Long> entry : assignMappings.entrySet()) {
            adminWorkOrderBiz.updateRoleOrOperatorIdWithIdList(entry.getKey(), Lists.newArrayList(entry.getValue()));
            // 自动分配的操作记录
            this.addWorkOrderRecord(entry.getKey(), entry.getValue());
        }
    }

    private void addWorkOrderRecord(int operatorId, long workOrderId) {
        AdminWorkOrderFlow flowOrder = adminWorkOrderFlowDao.selectByWorkOrderId(workOrderId);
        AdminWorkOrder adminWorkOrder = adminWorkOrderBiz.selectById(workOrderId);
        List<AdminOrganizationUserMap> orgList = orgIdBiz.getLowestOrgByUserIds(Lists.newArrayList(operatorId));
        List<AdminOrganization> allOrganization = Lists.newArrayList();
        orgIdBiz.getOrgByLowestId(orgList.stream().sorted(Comparator.comparing(AdminOrganizationUserMap::getOrgId)).map(AdminOrganizationUserMap::getOrgId).findFirst().get(), allOrganization);
        String comment = "分配至： ";
        if (CollectionUtils.isNotEmpty(allOrganization)) {
            comment += allOrganization.stream().sorted(Comparator.comparing(AdminOrganization::getId)).map(AdminOrganization::getName).collect(Collectors.joining("-"));
        }
        comment += "-"+seaAccountDelegate.getNameByUserId(operatorId);

        // 分配人 id 102  系统
        AdminWorkOrderFlowRecord record = new AdminWorkOrderFlowRecord(flowOrder, 102, comment,
                AdminWorkOrderFlowConst.OperateTypeEnum.SYSTEM_ALLOT_WORK_FLOW.getCode(), adminWorkOrder.getLevel(),
                flowOrder.getSecondClassifyId());
        // 记录id
        record.setOrderOperatorId(operatorId);
        flowRecordBiz.insertOne(record);
    }

    private int getCustomServiceOrgId() {
        return WorkFlowStaffService.orgIdByOrgType.get(WorkFlowStaffStatus.OrgTypeEnum.er_xian.getCode());
    }

    private void insertAssignRecord(Collection<Integer> operatorIds) {


        if (CollectionUtils.isEmpty(operatorIds)) {
            return;
        }

        List<WorkFlowAutoAssignRecord> recordList = Lists.newArrayList();
        for (int operatorId : operatorIds) {
            WorkFlowAutoAssignRecord record = new WorkFlowAutoAssignRecord();
            record.setUserId(operatorId);
            record.setAssignDate(DateUtil.getYMDStringByDate(new Date()));
            record.setAssignNum(1);
            record.setTodayFirstAssignTime(System.currentTimeMillis());
            recordList.add(record);
        }

        assignRecordDAO.insertOrUpdateAssigns(recordList);
        log.info("给当前用户分配了信息传递工单.allRecordList:{}", recordList);
    }

    private boolean canAutoAssignWork() {
        return canAutoAssignFlowWorkSwitch == 1;
    }

    private String lockForAssign() {
        String identifier = null;

        try {
            identifier = cfRedissonHandler.tryLock(FLOW_WORK_AUTO_ASSIGN_LOCK_NAME, 2 * 1000, 10 * 1000);
        } catch (Throwable e) {
            log.warn("信息传递工单自动分配加锁异常", e);
        }

        return identifier;
    }


    private void unLockForAssign(String identifier) {

        if (StringUtils.isBlank(identifier)) {
            return;
        }
        try {
            cfRedissonHandler.unLock(FLOW_WORK_AUTO_ASSIGN_LOCK_NAME, identifier);
        } catch (Exception e) {
            log.warn("信息传递工单自动分配释放锁异常", e);
        }
    }

    @Override
    public void addOrgNoHandleLimit(int lowestId, int limit) {
        Map<Integer, Integer> handleMappings = getNoHandleOrgMapping();

        Integer oldLimit = handleMappings.get(lowestId);

        typePropertyBiz.addOrgNoHandleLimit(lowestId, limit);

        if (oldLimit == null || oldLimit < limit) {
            log.info("当前的最大待处理的工单数大于以前的限制，触发一次分配. orgId:{} currentHandle:{} limit:{}",
                    lowestId, oldLimit, limit);
            assignWorkTOrg(lowestId);
        }

    }

    @Override
    public WorkFlowTypeProperty.FlowTypePropertyEntity selectPropertyByOrgId(int orgId) {
        Map<Integer, Integer> noHandleMapping = getNoHandleOrgMapping();
        WorkFlowTypeProperty.FlowTypePropertyEntity result = new WorkFlowTypeProperty.FlowTypePropertyEntity();
        result.setNoHandleCount(noHandleMapping.get(orgId));
        return result;
    }

    @Override
    public void triggerAutoAssignReportOrderWithLock() {
        redisLockService.callWithLock(() -> {
            triggerAutoAssignReportOrder();
            return null;
        }, "work-order-flow-report-auto-assign-lock", 5000);
    }

    public void triggerAutoAssignReportOrder() {

        log.info("triggerAutoAssignReportOrder");

        try {
            Stopwatch stopwatch = Stopwatch.createStarted();
            List<AdminWorkOrderFlowView> noHandWorkOrders = adminWorkOrderFlowDao.selectByOrderStatusAndOperatorsNew(getDate(), Lists.newArrayList(0)
                    , Lists.newArrayList(AdminWorkOrderConst.Status.CREATED.getCode(), AdminWorkOrderConst.Status.HANDLING.getCode()), getOrgIdByType(WorkFlowStaffStatus.OrgTypeEnum.report));
            stopwatch.stop();
            log.info("triggerAutoAssignReportOrder 耗时:{}", stopwatch);

            if (CollectionUtils.isEmpty(noHandWorkOrders)) {
                log.info("没有待处理的举报信息传递工单,不做分配。");
                return;
            }

            noHandWorkOrders = noHandWorkOrders.stream()
                    .sorted(Comparator.comparing(AdminWorkOrderFlowView::getLevel).reversed()
                            .thenComparing(AdminWorkOrderFlowView::getCreateTime))
                    .collect(Collectors.toList());
            autoAssignReportOrderByOrderId(noHandWorkOrders);
        } catch (Exception e) {
            log.error("自动分配举报信息传递工单异常.");
        }
    }

    @Override
    public void onWorkOrderAssign(long workOrderId) {
        log.info("onWorkOrderAssign workOrderId={}", workOrderId);

        AdminWorkOrderFlow workOrderFlow = adminWorkOrderFlowDao.selectByWorkOrderId(workOrderId);
        AdminWorkOrder adminWorkOrder = adminWorkOrderBiz.selectById(workOrderId);

        // 举报传递工单被领取自动发通知
        Integer reportProblemType = getOrgIdByType(WorkFlowStaffStatus.OrgTypeEnum.report);
        if (workOrderFlow.getProblemType() == reportProblemType) {
            log.info("send notice");
            String flowOrderId = flowOrderCommonService.generateWorkFlowId(workOrderFlow.getCreateTime(), workOrderFlow.getId());
            String classify = adminWorkOrderFlowBiz.getNewClassifyDesc(workOrderFlow.getNewFirstClassifyId(),workOrderFlow.getNewSecondClassifyId(),workOrderFlow.getNewThirdClassifyId());

            alarmBotDelegate.sendFlowReportAutoAssign(flowOrderId, workOrderFlow.getCaseId(), classify,
                    workOrderFlow.getProblemContent(), adminWorkOrder.getOperatorId());
        }

    }

    private void autoAssignReportOrderByOrderId(List<AdminWorkOrderFlowView> orderList) {
        log.info("autoAssignReportOrg orderList={}", orderList);

        List<Integer> userIdList = getAssignPriorityUserIds(getOrgIdByType(WorkFlowStaffStatus.OrgTypeEnum.report), 1);
        //举报一二组是虚拟组织  仅仅作为举报组的人员筛选
        int report_1 = 53;
        int report_2 = 54;
        //设置组织映射
        if (applicationService.isDevelopment()) {
            report_1 = 162;
            report_2 = 163;
        }
        List<AdminOrganizationUserMap> users = orgIdBiz.getOrgUserByOrgIds(Lists.newArrayList(report_1,report_2));
        Map<Integer,Set<Integer>> usersMap = users.stream().collect(Collectors.groupingBy(AdminOrganizationUserMap::getOrgId, Collectors.mapping(AdminOrganizationUserMap::getUserId,Collectors.toSet())));

        Set<Integer> oneUsers = usersMap.get(report_1);
        List<Integer> oneUserIdList = userIdList.stream().filter(r->oneUsers.contains(r)).collect(Collectors.toList());
        int userSize1 = CollectionUtils.size(oneUserIdList);
        tryAssignOrder(orderList, oneUserIdList, userSize1, 1, oneUsers);

        Set<Integer> twoUsers = usersMap.get(report_2);
        List<Integer> twoUserIdList = userIdList.stream().filter(r->twoUsers.contains(r)).collect(Collectors.toList());
        int userSize2 = CollectionUtils.size(twoUserIdList);
        tryAssignOrder(orderList, twoUserIdList, userSize2, 2, twoUsers);

    }


    private void tryAssignOrder(List<AdminWorkOrderFlowView> orderList, List<Integer> userIdList, int userSize, int type, Set<Integer> users) {

        log.info("tryAssignOrder userIdList={},type={}",userIdList,type);

        Integer autoProblemType = getOrgIdByType(WorkFlowStaffStatus.OrgTypeEnum.report);
        int userIndex = 0;

        for (AdminWorkOrderFlowView o : orderList) {
            long workOrderId = o.getWorkOrderId();
            AdminWorkOrderFlow workOrderFlow = adminWorkOrderFlowDao.selectByWorkOrderId(workOrderId);

            int problemType = workOrderFlow.getProblemType();
            if (problemType != autoProblemType) {
                log.info("autoAssignReportOrg type mismatch");
                continue;
            }

            // 自动分配给指定人
            OpResult<Long> opResult = autoAssign2TargetUser(workOrderFlow);
            boolean success = opResult.isSuccess();
            if (success) {
                log.info("autoAssignReportOrg target user");
                continue;
            }

            int flag = getListFromOrderType(opResult, type, users);
            if (type != flag) {
                continue;
            }

            if (CollectionUtils.isEmpty(userIdList)) {
                log.info("autoAssignReportOrg 没有符合的用户 caseId={} id={}",workOrderFlow.getCaseId(),workOrderId);
                continue;
            }
            if (userIndex >= userSize) {
                log.info("在线用户不够了");
                return;
            }
            Integer userId = userIdList.get(userIndex);
            actualAssignOrder(workOrderFlow, userId);
            log.info("autoAssignReportOrg 自动分配举报 {} userId={}", workOrderId,userId);
            userIndex++;
        }
    }

    /**
     * 根据工单类型获取同组人
     * @param opResult
     * @return
     */
    private Integer getListFromOrderType(OpResult<Long> opResult, int type, Set<Integer> users) {

        if (AdminErrorCode.CASE_MSG_EXIST.equals(opResult.getErrorCode())) {

            Long lastOrderOperatorId = opResult.getData();

            //默认举报一线
            //此案例最新工单有处理人，但是处理人不属于举报一线组也不属于举报二线组
            //此案例没有举报工单 （首次、历史、二线、失联）
            if (lastOrderOperatorId == -1L) {
                return 1;
            }

            //不分配
            //此案例最新工单没有人领取（待分配）
            if (lastOrderOperatorId == 0L) {
                return 0;
            }

            boolean flag = users.contains(lastOrderOperatorId.intValue());

            if (type == 1 && flag) {
                return 1;
            }

            if (type == 2 && flag) {
                return 2;
            }
        }

        return 0;
    }


    private Integer getOrgIdByType(WorkFlowStaffStatus.OrgTypeEnum orgType) {
        int reportOrgType = orgType.getCode();
        return WorkFlowStaffService.orgIdByOrgType.get(reportOrgType);
    }

    private void actualAssignOrder(AdminWorkOrderFlow workOrderFlow, Integer userId) {
        long workOrderId = workOrderFlow.getWorkOrderId();
        Map<Integer, Long> userTFlowIdMapping = Maps.newHashMap();
        Map<Integer, Long> assignMappings = Maps.newHashMap();

        userTFlowIdMapping.put(userId, workOrderFlow.getId());
        assignMappings.put(userId, workOrderId);
        assignOrder(userTFlowIdMapping, assignMappings);

        onWorkOrderAssign(workOrderId);
    }

    private OpResult<Long> autoAssign2TargetUser(AdminWorkOrderFlow workOrderFlow) {
        int caseId = workOrderFlow.getCaseId();
        if (caseId <= 0) {
            log.info("没有案例id");
            return OpResult.createFailResult(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<Integer> types = Optional.ofNullable(cfWorkOrderTypeFeignClient.getByOneLevel(OneTypeEnum.report.getType()))
                .filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList());
        Response<WorkOrderVO> lastOrderResp = cfWorkOrderClient.getLastWorkOrderByTypes(caseId, types);
        if (lastOrderResp == null || lastOrderResp.notOk() || lastOrderResp.getData() == null) {
            log.info("autoAssignReportOrg get order fail {}", lastOrderResp);
            return OpResult.createFailResult(AdminErrorCode.CASE_MSG_EXIST, -1L);
        }
        WorkOrderVO lastOrder = lastOrderResp.getData();

        long lastOrderOperatorId = lastOrder.getOperatorId();
        if (lastOrderOperatorId <= 0) {
            log.info("最新工单没人领取");
            return OpResult.createFailResult(AdminErrorCode.CASE_MSG_EXIST, 0L);
        }
        WorkFlowStaffStatus staffStatus = flowStatusBiz.findByUserId(lastOrderOperatorId);
        if (staffStatus == null || staffStatus.getStaffStatus() != WorkFlowStaffStatus.StaffStatusEnum.online.getCode()) {
            log.info("不在线或没有此员工 caseId={}", workOrderFlow.getCaseId());
            return OpResult.createFailResult(AdminErrorCode.CASE_MSG_EXIST, lastOrderOperatorId);
        }
        int orgId = getOrgIdByType(WorkFlowStaffStatus.OrgTypeEnum.report);
        int userId = (int) lastOrderOperatorId;
        AdminOrganizationUserMap orgUserMap = orgIdBiz.getAdminOrganizationUserMap(userId, orgId);
        if (orgUserMap == null) {
            log.info("不是该组织用户");
            return OpResult.createFailResult(AdminErrorCode.CASE_MSG_EXIST, -1L);
        }
        actualAssignOrder(workOrderFlow, userId);
        log.info("自动分配到指定人 {} {}", userId, workOrderFlow);
        return OpResult.createSucResult();
    }

    private List<AdminWorkOrderFlowView> judgeJingXi(List<AdminWorkOrderFlowView> noHandWorkOrders) {

        List<AdminWorkOrderFlowView> adminWorkOrderFlowViews = Lists.newArrayList();
        for (AdminWorkOrderFlowView v : noHandWorkOrders) {
            if (v.getProblemType() == getOrgIdByType(WorkFlowStaffStatus.OrgTypeEnum.er_xian) && v.getJingxiChannel() == 1) {
                continue;
            }
            adminWorkOrderFlowViews.add(v);
        }
        return adminWorkOrderFlowViews;
    }

    private Date getDate() {
        LocalDate localDate = LocalDate.now().minusDays(7);

        ZoneId zoneId = ZoneId.systemDefault();

        ZonedDateTime zdt = localDate.atStartOfDay(zoneId);

        return Date.from(zdt.toInstant());
    }

}
