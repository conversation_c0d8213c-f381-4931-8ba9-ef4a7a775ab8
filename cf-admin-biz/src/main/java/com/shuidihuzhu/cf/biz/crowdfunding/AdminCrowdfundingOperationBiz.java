package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.enums.BooleanEnum;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCrowdfundingOperation;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport;
import com.shuidihuzhu.cf.vo.crowdfunding.ReportCaseVo;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

public interface AdminCrowdfundingOperationBiz {

	int add(CrowdfundingOperation crowdfundingOperation);

	int update(CrowdfundingOperation crowdfundingOperation);

	int updateOperation(String infoUuid, int operatorId, int operation, String reason, int deferContactReasonType);

	CrowdfundingOperation getByInfoId(String infoId);

	//主库查询
    CrowdfundingOperation getByInfoIdMaster(String infoId);

	int updateCommitTime(String infoId, Timestamp commitTime);

    int addCallCount(int count, String infoUuid);

    int updateCallStatus(int callStatus, String infoUuid);

    void addRefuseCount(int adminUserId, int count, String infoUuid);

    int setRefuseCount(int count, String infoUuid);

    AdminCrowdfundingOperation selectByInfoUuid(String infoUuid);

    Map<String, Integer> selectUuidReportStatus(List<String> infoUuid);

    List<CrowdfundingOperation> getByInfoIds(List<String> infoids);

    List<CrowdfundingOperation> getByTimeAndCallStatus(String startTime,
                                                       String endTime,
                                                       int callStatus,
                                                       int offset,
                                                       int limit);

    List<ReportCaseVo> getReportCaseList(String caseType, Integer crowdfundingStatus, String followType,
                                         Integer sortType, Integer pageSize, Integer current,
                                         List<String> infoUuids, String addTrustStatus, String hospitalAuditStatus,
                                         BooleanEnum onWorkOrderEnum, Long hospitalSendBeginTime,
                                         Long hospitalSendEndTime,
                                         Long updateBeginTime,
                                         Long updateEndTime);

    List<ReportCaseVo> setFirstAndLast(List<ReportCaseVo> reportCaseVoList, List<CrowdfundingReport> firstReportMap,
                                       List<CrowdfundingReport> lastReportMap, List<Integer> infoids);

    void setVoIsHaveNewReport(List<ReportCaseVo> reportCaseVoList);

    List<CrowdfundingOperation> selectByInfoIdList(List<String> list);

    boolean validateUserSubmitTime(Long userSubmitBeginTime, Long userSubmitEndTime);


    com.shuidihuzhu.common.web.model.Response generatePageEmptyResponse(Map<String, Object> result, int current, int pageSize);


    int updateCreditStatus(int caseId, int creditStatus);

    List<CrowdfundingOperation> findByReportStatus(List<Integer> reportStatusList);

    int updateFuwuType(int id,int type);

    CrowdfundingOperation getByCaseId(int caseId);

}
