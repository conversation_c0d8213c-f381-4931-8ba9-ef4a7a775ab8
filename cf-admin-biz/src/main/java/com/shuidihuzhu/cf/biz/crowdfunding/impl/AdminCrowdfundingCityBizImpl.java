package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingCityBiz;
import com.shuidihuzhu.cf.biz.mapper.CrowdfundingCityMapper;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity;
import com.shuidihuzhu.client.cf.api.client.CrowdfundingCityFeignClient;
import com.shuidihuzhu.client.cf.api.model.CrmCityModel;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: wangpeng
 * @Date: 2021/1/20 11:34
 * @Description:
 */
@Service
@Slf4j
public class AdminCrowdfundingCityBizImpl implements AdminCrowdfundingCityBiz {

    @Resource
    private CrowdfundingCityFeignClient crowdfundingCityFeignClient;

    @Override
    public List<CrowdfundingCity> getProvince() {
        Response<List<CrmCityModel>> response = crowdfundingCityFeignClient.getProvinceList();
        List<CrmCityModel> crmCityModels = Optional.ofNullable(response)
                .map(Response::getData)
                .orElse(null);
        return convertCityDoList(crmCityModels);
    }

    @Override
    public List<CrowdfundingCity> getChildren(int parentId) {
        Response<List<CrmCityModel>> response = crowdfundingCityFeignClient.getCityList(parentId);
        List<CrmCityModel> crmCityModels = Optional.ofNullable(response)
                .map(Response::getData)
                .orElse(null);
        return convertCityDoList(crmCityModels);
    }

    public List<CrowdfundingCity> getCountiesByName(String name){
        Response<List<CrowdfundingCity>> response = crowdfundingCityFeignClient.listByCityNameAndLevel(name, Lists.newArrayList(2), null);
        return response.notOk() ? Lists.newArrayList() : response.getData();
    }

    @Override
    public List<CrowdfundingCity> getCitiesByNameAndLevel(String cityNameLike, List<Integer> levels) {
        if (CollectionUtils.isEmpty(levels) || StringUtils.isEmpty(cityNameLike)) {
            return Lists.newArrayList();
        }
        Response<List<CrowdfundingCity>> response = crowdfundingCityFeignClient.listByCityNameAndLevel(cityNameLike, Lists.newArrayList(levels), 50);
        return response.notOk() ? Lists.newArrayList() : response.getData();
    }

    public CrowdfundingCity getCityByName(String name){
        Response<List<CrowdfundingCity>> response = crowdfundingCityFeignClient.listByCityNames(Lists.newArrayList(name));
        if (response.notOk() || response.getData() == null) {
            return new CrowdfundingCity();
        }
        return response.getData().stream().filter(item -> item.getLevel() == 1 && item.getValid() == 1).findFirst().orElse(new CrowdfundingCity());
    }

    public CrowdfundingCity getById(Integer id) {
        Response<CrmCityModel> response = crowdfundingCityFeignClient.getById(id);
        return response.notOk() ? null : CrowdfundingCityMapper.INSTANCE.toCityDO(response.getData());
    }

    @Override
    public List<CrowdfundingCity> getListByCode(List<Integer> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Lists.newArrayList();
        }
        List<String> collect = codes.stream().map(String::valueOf).collect(Collectors.toList());
        Response<List<CrowdfundingCity>> response = crowdfundingCityFeignClient.getListByCodes(collect);
        return response.notOk() ? Lists.newArrayList() : response.getData();
    }

    @Override
    public List<CrowdfundingCity> getByRealParentId(int realParentId) {
        Response<List<CrmCityModel>> response = crowdfundingCityFeignClient.getCityList(realParentId);
        List<CrmCityModel> crmCityModels = Optional.ofNullable(response)
                .map(Response::getData)
                .orElse(null);
        return convertCityDoList(crmCityModels);
    }

    @Override
    public List<CrowdfundingCity> getByIds(List<Integer> ids) {
        Response<List<CrmCityModel>> response = crowdfundingCityFeignClient.listByIds(ids);
        List<CrmCityModel> crmCityModels = Optional.ofNullable(response)
                .map(Response::getData)
                .orElse(null);
        return convertCityDoList(crmCityModels);
    }

    private List<CrowdfundingCity> convertCityDoList(List<CrmCityModel> crmCityModels) {
        if (CollectionUtils.isEmpty(crmCityModels)) {
            return Lists.newArrayList();
        }
        return crmCityModels.stream().map(CrowdfundingCityMapper.INSTANCE::toCityDO).collect(Collectors.toList());
    }

}
