package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.model.CaseEndModel;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView;

/**
 * @Description: 案例结束执行操作
 * @Author: pangh<PERSON><PERSON>
 * @Date: 2022/10/25 9:33 下午
 */
public interface CaseEndReasonService {

    /**
     * 用户主动结束案例，执行一些操作
     */
    void raiserFinishCaseAction(CaseEndModel caseEndModel);

    /**
     * 患者去世
     */
    OperationResult<Void> patientDied(AdminWorkOrderFlowView param);
}
