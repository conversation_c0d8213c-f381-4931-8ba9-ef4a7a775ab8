package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfPropertyTransferHistoryBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingOrderBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfPropertyTransferHistoryDao;
import com.shuidihuzhu.cf.enums.crowdfunding.CfSimpleTrueOrFalseEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by Ahrievil on 2017/12/12
 */
@Service
@Slf4j
public class AdminCfPropertyTransferHistoryBizImpl implements AdminCfPropertyTransferHistoryBiz {

    @Autowired
    private AdminCfPropertyTransferHistoryDao adminCfPropertyTransferHistoryDao;
    @Autowired
    private AdminCrowdfundingOrderBiz adminCrowdfundingOrderBiz;
    @Override
    public List<CrowdfundingOrder> selectByBizType(long fromUserId, int bizType, int current, int pageSize) {
        List<Long> crowdfundingIds = getCfOrderIdsByBizType(fromUserId,bizType,current,pageSize);
        if(CollectionUtils.isEmpty(crowdfundingIds)){
            return Lists.newArrayList();
        }
        return adminCrowdfundingOrderBiz.getValidPayedSuccessListByIds(crowdfundingIds);
    }

    private List<Long> getCfOrderIdsByBizType(long fromUserId, int bizType, int current, int pageSize){
        PageHelper.startPage(current, pageSize);
        List<Long> crowdfundingIds = adminCfPropertyTransferHistoryDao.selectCfOrderIdsByBizType(fromUserId, bizType);
        return crowdfundingIds;
    }
}
