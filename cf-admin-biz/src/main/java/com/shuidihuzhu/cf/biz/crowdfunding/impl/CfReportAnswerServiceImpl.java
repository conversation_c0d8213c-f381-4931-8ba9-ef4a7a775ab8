package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.ICfReportAnswerService;
import com.shuidihuzhu.cf.dao.crowdfunding.CfReportAnswerDAO;
import com.shuidihuzhu.cf.model.crowdfunding.AdminReportProblemAnswerDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2019/12/16 上午11:37
 * @desc
 */
@Service
public class CfReportAnswerServiceImpl implements ICfReportAnswerService {

    @Autowired
    private CfReportAnswerDAO cfReportAnswerDAO;

    @Override
    public int insert(AdminReportProblemAnswerDetail answerDetail) {
        if(Objects.isNull(answerDetail)){
            return 0;
        }
        return cfReportAnswerDAO.insert(answerDetail);
    }

    @Override
    public List<AdminReportProblemAnswerDetail> query(int caseId, int reportId, int type) {
        if(caseId <= 0 || type <= 0){
            return Lists.newArrayList();
        }
        return cfReportAnswerDAO.query(caseId, reportId, type);
    }

    @Override
    public List<AdminReportProblemAnswerDetail> queryByType(int caseId, int type) {
        return cfReportAnswerDAO.queryByType(caseId, type);
    }

    @Override
    public List<AdminReportProblemAnswerDetail> queryByIds(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return Lists.newArrayList();
        }

        return cfReportAnswerDAO.queryByIds(ids);
    }

    @Override
    public AdminReportProblemAnswerDetail queryById(Long id) {
        if(Objects.isNull(id) || id <= 0){
            return null;
        }
        return cfReportAnswerDAO.queryById(id);
    }

    @Override
    public List<AdminReportProblemAnswerDetail> queryByCaseId(int caseId) {
        if (caseId <= 0) {
            return Lists.newArrayList();
        }
        return cfReportAnswerDAO.queryByCaseId(caseId);
    }

    @Override
    public AdminReportProblemAnswerDetail queryLastByCaseId(int caseId, int type) {
        if (caseId <= 0) {
            return null;
        }
        return cfReportAnswerDAO.queryLastByCaseId(caseId, type);
    }

    @Override
    public List<AdminReportProblemAnswerDetail> queryByCaseIdAndType(int caseId, int type) {
        if (caseId <= 0) {
            return Lists.newArrayList();
        }
        return cfReportAnswerDAO.queryByCaseIdAndType(caseId, type);
    }
}
