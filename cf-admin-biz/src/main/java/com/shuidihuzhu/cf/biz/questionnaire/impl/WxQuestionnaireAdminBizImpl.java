package com.shuidihuzhu.cf.biz.questionnaire.impl;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.questionnaire.WxQuestionnaireAdminBiz;
import com.shuidihuzhu.cf.dao.questionnaire.*;
import com.shuidihuzhu.cf.enums.ResultCodeEnum;
import com.shuidihuzhu.cf.enums.questionnaire.ReplyTypeEnum;
import com.shuidihuzhu.cf.model.common.BaseResult;
import com.shuidihuzhu.cf.model.questionnaire.*;
import com.shuidihuzhu.cf.vo.questionnaire.WxQuestionnaireAdminVo;
import com.shuidihuzhu.cf.vo.questionnaire.WxSdTagValueAdminVo;
import com.shuidihuzhu.cf.vo.questionnaire.WxSdTagAdminVo;
import com.shuidihuzhu.pay.common.biz.PageHasId;
import com.shuidihuzhu.pay.common.model.PageRequest;
import com.shuidihuzhu.pay.common.model.PageResponse;
import com.shuidihuzhu.pay.common.util.PageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class WxQuestionnaireAdminBizImpl implements WxQuestionnaireAdminBiz {
    private static final String SEPARATOR = ",";
    @Autowired
    private WxSdTagAdminDao wxSdTagAdminDao;
    @Autowired
    private WxQuestionnaireAdminDao wxQuestionnaireAdminDao;
    @Autowired
    private WxQuestionAdminDao wxQuestionAdminDao;

    @Override
    public WxQuestionnaire getWxQuestionnaire(int id) {
        WxQuestionnaire wxQuestionnaire = this.wxQuestionnaireAdminDao.getById(id);
        List<WxQuestion> wxQuestions = this.wxQuestionAdminDao.getByQnrId(wxQuestionnaire.getId());
        wxQuestionnaire.setQuestionList(wxQuestions);
        return wxQuestionnaire;
    }

    @Override
    public BaseResult<Boolean> addWxQuestionnaire(String name, String title, List<WxQuestion> wxQuestions, String createBy) {
        if(Strings.isNullOrEmpty(name) || CollectionUtils.isEmpty(wxQuestions)){
            return BaseResult.build(ResultCodeEnum.PARAM_ERROR);
        }

        if(checkeNameIsExists(name)){
            return BaseResult.build(ResultCodeEnum.ALREADY_EXIST_ERROR);
        }

        WxQuestionnaire wxQuestionnaire = new WxQuestionnaire();
        wxQuestionnaire.setName(name);
        wxQuestionnaire.setTitle(title);
        wxQuestionnaire.setCreateBy(createBy);
        wxQuestionnaire.setUpdateBy(createBy);
        List<WxQuestion> questionList = setWxQuestionsDefaultValues(wxQuestions, createBy, wxQuestionnaire);
        int ret = this.wxQuestionnaireAdminDao.add(wxQuestionnaire);
        questionList.stream().forEach(question ->{
            question.setQnrId(wxQuestionnaire.getId());
            this.wxQuestionAdminDao.save(question);
        });
        return ret ==1 ? BaseResult.createSucResult(): BaseResult.createFailResult();
    }

    @NotNull
    private List<WxQuestion> setWxQuestionsDefaultValues(List<WxQuestion> wxQuestions, String opUser, WxQuestionnaire wxQuestionnaire) {
        StringBuilder tagNames = new StringBuilder();
        List<WxQuestion> questionList = Lists.newArrayList();
        wxQuestions.stream().forEach(q ->{
            q.setQnrId(wxQuestionnaire.getId());
            q.setCreateBy(opUser);
            q.setUpdateBy(opUser);
            WxSdTag wxSdTag = this.wxSdTagAdminDao.getByTagId(q.getTagId());
            if(null != wxSdTag){
                q.setTagCode(wxSdTag.getTagCode());
                q.setTagName(wxSdTag.getTagName());
                tagNames.append(wxSdTag.getTagName()).append(SEPARATOR);
            }else{
                q.setTagCode("-1");
            }

            if (ReplyTypeEnum.WX_MENU.getCode() == q.getReplyType()) {
                q.setMaxValue(0);
                q.setMinValue(0);
                q.setReplyType(ReplyTypeEnum.WX_MENU.getCode());
            } else {
                q.setOption("[]");
                q.setReplyType(ReplyTypeEnum.WX_MULTI_PUSH.getCode());
            }
            questionList.add(q);
        });
        if(null != tagNames && tagNames.length() >0) {
            String tagName = tagNames.deleteCharAt(tagNames.length() - 1).toString();
            wxQuestionnaire.setTagNames(tagName);
        }
        return questionList;
    }

    private boolean checkeNameIsExists(String name) {
        log.warn(" --questionnaire name already exists. name:{}", name);
        Integer count = this.wxQuestionnaireAdminDao.checkeNameIsExists(name);
        return count !=null && count > 0;
    }

    @Override
    public BaseResult<Boolean> editWxQuestionnaire(int id, String name, String title, List<WxQuestion> wxQuestions, String updateBy) {
        if(id <=0 || Strings.isNullOrEmpty(name)){
            log.warn(" --editWxQuestionnaire param error name is null");
            return BaseResult.build(ResultCodeEnum.PARAM_ERROR);
        }

        WxQuestionnaire wxQuestionnaire = this.wxQuestionnaireAdminDao.getById(id);
        if(null == wxQuestionnaire){
            return BaseResult.build(ResultCodeEnum.NOT_EXIST_ERROR);
        }
        if(!wxQuestionnaire.getName().equals(name)){
            if(checkeNameIsExists (name))
                return BaseResult.build(ResultCodeEnum.ALREADY_EXIST_ERROR);
        }

        wxQuestionnaire.setName(name);
        wxQuestionnaire.setTitle(title);
        wxQuestionnaire.setUpdateBy(updateBy);
        List<WxQuestion> questionList = setWxQuestionsDefaultValues(wxQuestions, updateBy, wxQuestionnaire);
        int ret = this.wxQuestionnaireAdminDao.edit(wxQuestionnaire);

        //问题有改动，先删除后增加
        if(!CollectionUtils.isEmpty(wxQuestions)){
            this.wxQuestionAdminDao.delByQnrId(id);
            questionList.stream().forEach(q ->{
                this.wxQuestionAdminDao.save(q);
            });
        }
        return ret ==1 ? BaseResult.createSucResult(): BaseResult.createFailResult();
    }

    @Override
    public PageResponse getWxQuestionnairePage(String name, String title, String tagName, String createBy,
                                               PageRequest pageRequest) {
        List<WxQuestionnaireAdminVo> vos = this.wxQuestionnaireAdminDao.getWxQuestionnairePage(name, title, tagName, createBy, pageRequest);
        List<PageHasId> pageHasIds = Lists.newArrayList();
        vos.stream().forEach(vo ->{
            pageHasIds.add((PageHasId) vo);
        });
        PageResponse pageResponse = PageUtil.buildPageResponse(pageHasIds, pageRequest);
        return pageResponse;
    }

    @Override
    public List<WxSdTagAdminVo> getAllTagList() {
        List<WxSdTagAdminVo> list = this.wxSdTagAdminDao.getAll();
        return list;
    }

    @Override
    public List<WxSdTagValueAdminVo> getTagValues(int tagId) {
        if(tagId <0){
            return Lists.newArrayList();
        }
        List<WxSdTagValueAdminVo> list = this.wxSdTagAdminDao.getByParentId(tagId);
        return list == null ? Lists.newArrayList() : list;
    }
}
