package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCaseConfirmationWhiteListBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCaseConfirmationDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfCaseEndWhiteList;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AdminCaseConfirmationWhiteListBizImpl implements AdminCaseConfirmationWhiteListBiz {

    @Autowired
    private AdminCaseConfirmationDao adminCaseConfirmationDao;

    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;

    @Override
    public int add(long caseId, int userId) {
        return adminCaseConfirmationDao.insert(caseId, userId);
    }

    @Override
    public List<CfCaseEndWhiteList> selectWhiteList(int caseId,int current, int pageSize) {
        PageHelper.startPage(current, pageSize);
        List<CfCaseEndWhiteList> cfCaseEndWhiteLists = adminCaseConfirmationDao.selectWhiteList(caseId);
        if (CollectionUtils.isEmpty(cfCaseEndWhiteLists)) {
            return null;
        }
        //查询一个案例
        if(cfCaseEndWhiteLists.size() == 1){
            CfCaseEndWhiteList cfCaseEndWhiteList =  cfCaseEndWhiteLists.get(0);
            CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById((int)cfCaseEndWhiteList.getCaseId());
            if (crowdfundingInfo == null) {
                return null;
            }
            cfCaseEndWhiteList.setInfoUuid(crowdfundingInfo.getInfoId());
            cfCaseEndWhiteList.setTitle(crowdfundingInfo.getTitle());
            return Lists.newArrayList(cfCaseEndWhiteList);
        }
        //获取所有案例
        List<Integer> caseIds = cfCaseEndWhiteLists.stream().map(v -> (int) v.getCaseId()).collect(Collectors.toList());
        Map<Integer, CrowdfundingInfo> infoMap = crowdfundingInfoBiz.getMapByIds(caseIds);
        if (MapUtils.isEmpty(infoMap)) {
            return null;
        }
        cfCaseEndWhiteLists.stream().forEach(v -> {
            CrowdfundingInfo crowdfundingInfo = infoMap.get((int) v.getCaseId());
            if (crowdfundingInfo == null) {
                return;
            }
            v.setInfoUuid(crowdfundingInfo.getInfoId());
            v.setTitle(crowdfundingInfo.getTitle());
        });
        return cfCaseEndWhiteLists;
    }

    @Override
    public CfCaseEndWhiteList getList(long caseId) {
        return adminCaseConfirmationDao.selectByCaseId(caseId);
    }

}
