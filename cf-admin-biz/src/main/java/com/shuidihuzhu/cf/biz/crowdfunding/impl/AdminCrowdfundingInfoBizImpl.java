package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.SqlUtil;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.admin.util.admin.IntegerUtil;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingAuthorBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.BuildDrawStatusUtil;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.feign.cipher.constants.DesensitizeEnum;
import com.shuidihuzhu.cf.client.material.feign.PreposeMaterialClient;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfFundUseAuditDao;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingInfoDao;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingInfoSlaveDao;
import com.shuidihuzhu.cf.delegate.bdcrm.IBdCrmDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingUserDelegate;
import com.shuidihuzhu.cf.delegate.finance.impl.FinanceDelegate;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminCfCaseStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.NewCfRefundConstant;
import com.shuidihuzhu.cf.finance.model.vo.CfDrawCashApplyVo;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoVo;
import com.shuidihuzhu.cf.service.sensitive.AdminSensitiveProcessService;
import com.shuidihuzhu.cf.vo.CrowdfundingAuthorVo;
import com.shuidihuzhu.cf.vo.crowdfunding.AdminWorkOrderReportVo;
import com.shuidihuzhu.cf.vo.crowdfunding.PreposeMaterialAdminModel;
import com.shuidihuzhu.cf.vo.crowdfunding.ReportCaseVo;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.admin.model.AdminCfModifyAmountMessage;
import com.shuidihuzhu.client.cf.growthtool.client.ClewPreproseMaterialFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.ClewPreproseMaterialResult;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.growthtool.model.VolunteerInfoUuidModel;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.constant.CfClientMQTopicCons;
import com.shuidihuzhu.common.web.constants.CrowdfundingCons;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.LocalCacheUtil;
import com.shuidihuzhu.common.web.util.admin.BasicExample;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.ibatis.annotations.Param;
import org.bouncycastle.jce.provider.JCEMac;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AdminCrowdfundingInfoBizImpl implements AdminCrowdfundingInfoBiz {

    @Autowired
    private Producer producer;
    @Autowired
    private AdminCrowdfundingInfoDao crowdfundingInfoDao;
    @Autowired
    private AdminCrowdfundingInfoSlaveDao crowdfundingInfoSlaveDao;
    @Autowired
    private ICrowdfundingUserDelegate crowdfundingUserDelegate;
    @Autowired
    private AdminCrowdfundingAuthorBiz crowdfundingAuthorBiz;
    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;
    @Autowired
    private FinanceDelegate financeDelegate;
    @Autowired
    private PreposeMaterialClient preposeMaterialClient;
    @Autowired
    private ClewPreproseMaterialFeignClient clewMaterialFeignClient;
    @Autowired
    private IBdCrmDelegate bdCrmDelegate;
    @Autowired
    private AdminSensitiveProcessService adminSensitiveProcessService;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private AdminCfFundUseAuditDao adminCfFundUseAuditDao;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;
    @Autowired
    private MaskUtil maskUtil;


    @Override
    public int updateFrom(CrowdfundingInfo crowdfundingInfo) {
        return crowdfundingInfoDao.updateFrom(crowdfundingInfo);
    }

    @Override
    public int updateTitleAndContent(CrowdfundingInfo crowdfundingInfo) {
        return crowdfundingInfoDao.updateTitleAndContent(crowdfundingInfo);
    }

    @Override
    public int updateRelationType(CrowdfundingInfo crowdfundingInfo) {
        return crowdfundingInfoDao.updateRelationType(crowdfundingInfo);
    }

    @Override
    public int updatePayeeInfo(CrowdfundingInfo crowdfundingInfo) {
        return crowdfundingInfoDao.updatePayeeInfo(crowdfundingInfo);
    }

    @Override
    public int updateCaseUserId(int caseId, long userId){
        if(caseId <= 0 || userId <= 0){
            return 0;
        }
        return crowdfundingInfoDao.updateCaseUserId(caseId, userId);
    }

    @Override
    public int doApprove(int crowdfundingId, CrowdfundingStatus status, Date beginTime, Date endTime) {
        return crowdfundingInfoDao.doApprove(crowdfundingId, status, beginTime, endTime);
    }


    @Override
    public List<CrowdfundingInfoVo> selectByExampleJoin(BasicExample basicExample, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<CrowdfundingInfoVo> crowdfundingInfoList = crowdfundingInfoSlaveDao.selectByExampleJoin(basicExample);
        return crowdfundingInfoList;
    }
    @Override
    public CrowdfundingInfo getFundingInfo(String infoId) {
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoSlaveDao.getFundingInfo(infoId);
        return crowdfundingInfo;
    }

    @Override
    public CrowdfundingInfo getFundingInfoById(Integer id) {
        return crowdfundingInfoSlaveDao.getFundingInfoById(id);
    }

    @Override
    public CrowdfundingInfoVo getFundingInfoVoByInfoUuid(String infoUuid) {
        return crowdfundingInfoSlaveDao.getFundingInfoVoByInfoUuid(infoUuid);
    }

    @Override
    public List<CrowdfundingInfo> getListByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<CrowdfundingInfo> crowdfundingInfos = Lists.newArrayList();
        List<List<Integer>> partitonIds = Lists.partition(ids, 500);
        partitonIds.forEach(tmpIds -> {

            List<CrowdfundingInfo> tmpList = this.crowdfundingInfoSlaveDao.getFundingInfoByIds(tmpIds);
            if (CollectionUtils.isNotEmpty(tmpList)) {
                crowdfundingInfos.addAll(tmpList);
            }
        });

        return crowdfundingInfos;
    }

    @Override
    public List<CrowdfundingInfo> getListByIdsOrderById(List<Integer> ids) {
        return this.crowdfundingInfoSlaveDao.getFundingInfoByIdsOrderById(ids);
    }

    @Override
    public Map<Integer, CrowdfundingInfo> getMapByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyMap();
        }

        List<CrowdfundingInfo> crowdfundingInfos = this.getListByIds(ids);

        if (CollectionUtils.isEmpty(crowdfundingInfos)) {
            return Collections.emptyMap();
        }
        Map<Integer, CrowdfundingInfo> crowdfundingInfoMap = Maps.newHashMap();
        for (CrowdfundingInfo crowdfundingInfo : crowdfundingInfos) {
            crowdfundingInfoMap.put(crowdfundingInfo.getId(), crowdfundingInfo);
        }
        return crowdfundingInfoMap;
    }

    @Override
    public List<CrowdfundingInfo> getListByInfoUuIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        return this.crowdfundingInfoSlaveDao.getFundingInfoByInfoIds(ids);
    }


    @Override
    public boolean isRechargeable(CrowdfundingInfo crowdfundingInfo) {
        if (crowdfundingInfo != null) {
            // return (crowdfundingInfo.getStatus() ==
            // CrowdfundingStatus.CROWDFUNDING_STATED) &&
            // crowdfundingInfo.getEndTime().getTime() >
            // System.currentTimeMillis();
            return crowdfundingInfo.getEndTime().getTime() > System.currentTimeMillis();
        }
        return false;
    }
    @Override
    public List<CrowdfundingInfoView> getCrowdfundingInfoList() {
        List<CrowdfundingInfo> fundingInfoList = crowdfundingInfoSlaveDao.getFundingInfoList();
        if (CollectionUtils.isEmpty(fundingInfoList)) {
            return Collections.emptyList();
        }
        Set<Integer> infoIdSet = Sets.newHashSet();
        for (CrowdfundingInfo crowdfundingInfo : fundingInfoList) {
            infoIdSet.add(crowdfundingInfo.getId());
        }
        List<Integer> infoIdList = Lists.newArrayList(infoIdSet);
        Map<Integer, CrowdfundingAuthor> crowdfundingAuthorMap = this.crowdfundingAuthorBiz.getByInfoIdList(infoIdList);
        Map<Integer, CrowdfundingTreatment> crowdfundingTreatmentMap = this.crowdfundingUserDelegate
                .getCrowdfundingTreatmentMapByInfoIdList(infoIdList);
        Map<Integer, List<CrowdfundingAttachmentVo>> crowdfundingAttachmentVoMap = this.crowdfundingDelegate
                .getByInfoIdList(infoIdList, AttachmentTypeEnum.ATTACH_CF);

        List<CrowdfundingInfoView> crowdfundingInfoViews = Lists.newArrayList();
        for (CrowdfundingInfo crowdfundingInfo : fundingInfoList) {
            // 只返回已完结 或众筹中的众筹项目
            if (CrowdfundingStatus.APPROVE_DENIED == crowdfundingInfo.getStatus()
                    || CrowdfundingStatus.APPROVE_PENDING == crowdfundingInfo.getStatus()) {
                continue;
            }
            // 屏蔽测试用众筹项目
            int infoId = crowdfundingInfo.getId();
            if (!ObjectUtils.notEqual(25, infoId)) {
                continue;
            }
            CrowdfundingInfoView crowdfundingInfoView = getCrowdfundingInfoViewOld(crowdfundingInfo,
                    crowdfundingAuthorMap.get(infoId), crowdfundingTreatmentMap.get(infoId),
                    crowdfundingAttachmentVoMap.get(infoId));
            crowdfundingInfoViews.add(crowdfundingInfoView);
        }
        return crowdfundingInfoViews;
    }

    public List<CrowdfundingInfo> getCrowdfundingList() {
        List<CrowdfundingInfo> fundingInfoList = crowdfundingInfoSlaveDao.getFundingInfoList();
        return fundingInfoList;
    }

    private CrowdfundingInfoView getCrowdfundingInfoView(CrowdfundingInfo crowdfundingInfo,
                                                         CrowdfundingAuthor crowdfundingAuthor, CrowdfundingTreatment crowdfundingTreatment,
                                                         List<CrowdfundingAttachmentVo> crowdfundingAttachmentList) {
        CrowdfundingInfoView crowdfundingInfoView = new CrowdfundingInfoView();
        BeanUtils.copyProperties(crowdfundingInfo, crowdfundingInfoView, "status");
        crowdfundingInfoView.setId(-1);
        crowdfundingInfoView.setStatus(crowdfundingInfo.getStatus().ordinal());
        crowdfundingInfoView.setContent(crowdfundingInfo.getContent().replaceAll("\n", "<p />"));
        int remainedDays = (int) ((crowdfundingInfo.getEndTime().getTime() - System.currentTimeMillis())
                / (CrowdfundingCons.ONE_DAY_MILLS));
        crowdfundingInfoView.setRemainedDays(remainedDays);
        crowdfundingInfoView.setTargetAmount(((double) crowdfundingInfo.getTargetAmount()) / 100);
        crowdfundingInfoView.setAmount(((double) crowdfundingInfo.getAmount()) / 100);
        Date endTime = crowdfundingInfo.getEndTime();
        if (endTime != null && endTime.after(new Date())) {
            // crowdfundingInfoView.setInfoStatus(CrowdfundingStatusEnum.ONLINE.getCode());
            crowdfundingInfoView.setHasFinished(false);
        } else {
            // crowdfundingInfoView.setInfoStatus(CrowdfundingStatusEnum.OFFLINE.getCode());
            crowdfundingInfoView.setHasFinished(true);
        }
        if (crowdfundingAuthor == null) {
            crowdfundingAuthor = new CrowdfundingAuthor();
        }
        crowdfundingAuthor.setCryptoIdCard(null);
        crowdfundingAuthor.setIdType(null);
        crowdfundingAuthor.setCryptoPhone(null);
        crowdfundingInfoView.setCrowdfundingAuthor(new CrowdfundingAuthorVo(crowdfundingAuthor));
        crowdfundingInfoView.setCrowdfundingAttachmentList(crowdfundingAttachmentList);
        crowdfundingInfoView.setCrowdfundingTreatment(crowdfundingTreatment);
        crowdfundingInfoView.setRechargeAble(isRechargeable(crowdfundingInfo));

        long userId = crowdfundingInfo.getUserId();
        crowdfundingInfoView.setUserId(userId);

        String keyOfTransmitCount = LocalCacheUtil.KEY_CROWDFUNDING_TRANSMIT_COUNT + "_" + crowdfundingInfo.getId();
        Integer transmitCount = LocalCacheUtil.get(keyOfTransmitCount);
        if (null != transmitCount) {
            crowdfundingInfoView.setShareCount(transmitCount);// 填充转发次数
        } else {
            int shareCount = 0;
            CfInfoStat cfInfoStat = crowdfundingDelegate.getById(crowdfundingInfo.getId());
            if (cfInfoStat != null) {
                shareCount = cfInfoStat.getShareCount();
                crowdfundingInfoView.setAmount(cfInfoStat.getAmount());
                crowdfundingInfoView.setDonationCount(cfInfoStat.getDonationCount());
            } // 填充转发次数
            crowdfundingInfoView.setShareCount(shareCount);// 填充转发次数
            LocalCacheUtil.put(keyOfTransmitCount, crowdfundingInfoView.getShareCount(), 3 * 60 * 1000);
        }
        crowdfundingInfoView.setType(crowdfundingInfo.getType());
        return crowdfundingInfoView;
    }

    @SuppressWarnings("deprecation")
    private CrowdfundingInfoView getCrowdfundingInfoViewOld(CrowdfundingInfo crowdfundingInfo,
                                                            CrowdfundingAuthor crowdfundingAuthor, CrowdfundingTreatment crowdfundingTreatment,
                                                            List<CrowdfundingAttachmentVo> crowdfundingAttachmentList) {
        CrowdfundingInfoView crowdfundingInfoView = new CrowdfundingInfoView();
        BeanUtils.copyProperties(crowdfundingInfo, crowdfundingInfoView, "status");
        crowdfundingInfoView.setId(-1);
        crowdfundingInfoView.setStatus(crowdfundingInfo.getStatus().ordinal());
        crowdfundingInfoView.setContent(crowdfundingInfo.getContent().replaceAll("\n", "<p />"));
        Date endTime = crowdfundingInfo.getEndTime();
        if (endTime != null && endTime.before(new Date())) {
            crowdfundingInfoView.setStatus(CrowdfundingStatus.FINISHED.ordinal());
        }
        int remainedDays = (int) ((crowdfundingInfo.getEndTime().getTime() - System.currentTimeMillis())
                / (CrowdfundingCons.ONE_DAY_MILLS));
        crowdfundingInfoView.setRemainedDays(remainedDays);
        crowdfundingInfoView.setTargetAmount(((double) crowdfundingInfo.getTargetAmount()) / 100);
        crowdfundingInfoView.setAmount(((double) crowdfundingInfo.getAmount()) / 100);
        if (crowdfundingAuthor == null) {
            crowdfundingAuthor = new CrowdfundingAuthor();
        }
        crowdfundingAuthor.setCryptoIdCard(null);
        crowdfundingAuthor.setIdType(null);
        crowdfundingAuthor.setCryptoPhone(null);
        crowdfundingInfoView.setCrowdfundingAuthor(new CrowdfundingAuthorVo(crowdfundingAuthor));
        crowdfundingInfoView.setCrowdfundingAttachmentList(crowdfundingAttachmentList);
        crowdfundingInfoView.setCrowdfundingTreatment(crowdfundingTreatment);
        crowdfundingInfoView.setRechargeAble(isRechargeable(crowdfundingInfo));

        long userId = crowdfundingInfo.getUserId();
        crowdfundingInfoView.setUserId(userId);

        String keyOfTransmitCount = LocalCacheUtil.KEY_CROWDFUNDING_TRANSMIT_COUNT + "_" + crowdfundingInfo.getId();
        Integer transmitCount = LocalCacheUtil.get(keyOfTransmitCount);
        if (null != transmitCount) {
            crowdfundingInfoView.setShareCount(transmitCount);// 填充转发次数
        } else {
            int shareCount = 0;
            CfInfoStat cfInfoStat = crowdfundingDelegate.getById(crowdfundingInfo.getId());
            if (cfInfoStat != null) {
                shareCount = cfInfoStat.getShareCount();
                crowdfundingInfoView.setAmount(cfInfoStat.getAmount());
                crowdfundingInfoView.setDonationCount(cfInfoStat.getDonationCount());
            } // 填充转发次数
            crowdfundingInfoView.setShareCount(shareCount);// 填充转发次数
            LocalCacheUtil.put(keyOfTransmitCount, crowdfundingInfoView.getShareCount(), 3 * 60 * 1000);
        }
        return crowdfundingInfoView;
    }

    @Override
    public int updateEndTime(int id, Date endTime) {
        return this.crowdfundingInfoDao.updateEndTime(id, endTime);
    }

    @Override
    public int updateStatus(int id, int newStatus, int oldStatus) {
        return this.crowdfundingInfoDao.updateStatus(id, newStatus, oldStatus);
    }

    @Override
    public List<CrowdfundingInfo> getByCreateTime(Timestamp startTime, Timestamp endTime) {
        return this.crowdfundingInfoSlaveDao.getByCreateTime(startTime, endTime);
    }

    @Override
    public int updateTargetAmount(int id, int targetAmonut) {

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoSlaveDao.getFundingInfoById(id);

        int num = crowdfundingInfoDao.updateTargetAmount(id, targetAmonut);

        // 发送修改金额消息
        sendModifyMsg(id, targetAmonut, crowdfundingInfo);

        return num;
    }

    private void sendModifyMsg(int caseId, int targetAmount, CrowdfundingInfo crowdfundingInfo) {

        AdminCfModifyAmountMessage adminCfModifyAmountMessage = new AdminCfModifyAmountMessage();
        adminCfModifyAmountMessage.setCaseId(caseId);
        adminCfModifyAmountMessage.setCurrentTargetAmount(targetAmount);
        adminCfModifyAmountMessage.setModifyTime(DateUtil.nowDate());
        adminCfModifyAmountMessage.setOriginalTargetAmount(crowdfundingInfo.getTargetAmount());

        Message msg = new Message(CfClientMQTopicCons.CF, CfClientMQTagCons.CF_MODIFY_TARGET_AMOUNT,
                "" + System.currentTimeMillis(), adminCfModifyAmountMessage);
        MessageResult msgResult = producer.send(msg);
        log.info("AdminCrowdfundingInfoBizImpl sendModifyMsg {} {}", msg, msgResult);
    }

    @Override
    public List<String> selectGoodOperation() {
        return crowdfundingInfoSlaveDao.selectGoodOperation();
    }

    @Override
    public List<CrowdfundingInfo> selectIdAndUuidAdnTypeById(Set<Integer> set) {
        if (CollectionUtils.isEmpty(set)) {
            return Lists.newArrayList();
        }
        return crowdfundingInfoSlaveDao.selectIdAndUuidAndTypeById(set);
    }


    @Override
    public List<CrowdfundingInfo> selectByUserIds(Set<Long> set) {
        if (CollectionUtils.isEmpty(set)) {
            return Lists.newArrayList();
        }
        return crowdfundingInfoSlaveDao.selectByUserIds(set);
    }

    @Override
    public int updateContent(String title, String content, int id) {

        content = adminSensitiveProcessService.sensitiveProcess(content);
        String encryptContent = oldShuidiCipher.aesEncrypt(content);

        return crowdfundingInfoDao.updateContent(title, content, encryptContent, id);
    }

    @Override
    public List<Integer> selectByTimeAndAmount(Timestamp beginTime, Timestamp endTime) {
        return crowdfundingInfoSlaveDao.selectByTimeAndAmount(beginTime, endTime);
    }


    @Override
    public AdminCrowdfundingInfo getInfoByUniqueCodeAndCaseId(Integer caseId) {
        return crowdfundingInfoDao.getInfoByUniqueCodeAndCaseId(caseId);
    }

    @Override
    public List<AdminCrowdfundingInfo> getInfoByVolunteerTypeWithMobileNew(Integer current, Integer pageSize, String startTime, String endTime, long userId,List<CrowdfundingVolunteer> crowdfundingVolunteers){
        Page<AdminCrowdfundingInfo> page = new Page(current,pageSize);
        page.setTotal(0);
        if (CollectionUtils.isEmpty(crowdfundingVolunteers)){
            return page;
        }
        List<String> uniqueCodeList = crowdfundingVolunteers.stream().map(CrowdfundingVolunteer::getUniqueCode).collect(Collectors.toList());
        List<VolunteerInfoUuidModel> volunteerInfoUuidModels = bdCrmDelegate.getVolunteerInfoUuidModelbyUniqueCodeListWithTime(uniqueCodeList, startTime, endTime);
        if (CollectionUtils.isEmpty(volunteerInfoUuidModels)){
            return page;
        }
        page.setTotal(volunteerInfoUuidModels.size());
        volunteerInfoUuidModels = getListByPage(volunteerInfoUuidModels, current, pageSize);
        List<String> inUuidList = volunteerInfoUuidModels.stream().map(VolunteerInfoUuidModel::getInfoUuid).collect(Collectors.toList());
        List<AdminCrowdfundingInfo> infoByInfoUuidWithUserId = crowdfundingInfoDao.getInfoByInfoUuidWithUserId(inUuidList, userId);
        if (CollectionUtils.isEmpty(infoByInfoUuidWithUserId)){
            return page;
        }
        return this.fullAdminCrowdfundingInfoList(crowdfundingVolunteers,volunteerInfoUuidModels,infoByInfoUuidWithUserId,page);
    }

    private Page<AdminCrowdfundingInfo> fullAdminCrowdfundingInfoList(List<CrowdfundingVolunteer> crowdfundingVolunteers, List<VolunteerInfoUuidModel> volunteerInfoUuidModels, List<AdminCrowdfundingInfo> infoByInfoUuidWithUserId, Page<AdminCrowdfundingInfo> page) {
        Map<String, List<VolunteerInfoUuidModel>> infoUuidMap = volunteerInfoUuidModels.stream().collect(Collectors.groupingBy(VolunteerInfoUuidModel::getInfoUuid));
        Map<String, List<CrowdfundingVolunteer>> uniqueCodeMap = crowdfundingVolunteers.stream().collect(Collectors.groupingBy(CrowdfundingVolunteer::getUniqueCode));
        for (AdminCrowdfundingInfo adminCrowdfundingInfo:infoByInfoUuidWithUserId){
            List<VolunteerInfoUuidModel> infoUuidModelList = infoUuidMap.get(adminCrowdfundingInfo.getInfoId());
            if (CollectionUtils.isEmpty(infoUuidModelList)){
                continue;
            }
            List<CrowdfundingVolunteer> volunteerList = uniqueCodeMap.get(infoUuidModelList.get(infoUuidModelList.size() - 1).getVolunteerUniqueCode());
            if (CollectionUtils.isEmpty(volunteerList)){
                continue;
            }
            CrowdfundingVolunteer volunteer = volunteerList.get(volunteerList.size() - 1);
            adminCrowdfundingInfo.setVolunteerMobile(shuidiCipher.decrypt(volunteer.getMobile()));
            adminCrowdfundingInfo.setVolunteerName(volunteer.getVolunteerName());
            adminCrowdfundingInfo.setVolunteerType(volunteer.getVolunteerType());
            adminCrowdfundingInfo.setVolunteerUniqueCode(volunteer.getUniqueCode());
            adminCrowdfundingInfo.setEmail(shuidiCipher.decrypt(volunteer.getEncryptEmail()));
        }
        page.addAll(infoByInfoUuidWithUserId);
        return page;
    }

    private <T> List<T> getListByPage(List<T> list,Integer current, Integer pageSize){
        current-=1;
        int currentIndex = current<=0?0:current*pageSize;
        int endIndex = currentIndex+pageSize;
        while (list.size()<currentIndex && currentIndex>0){
            current-=1;
            currentIndex = current<=0?0:current*pageSize;
        }
        if (list.size()<endIndex && endIndex>0){
            endIndex = list.size();
        }
        return list.subList(currentIndex,endIndex);
    }

    @Override
    public AdminCrowdfundingInfo getInfoByMobile(String mobile) {
        return crowdfundingInfoDao.getInfoByMobile(mobile);
    }

    @Override
    public List<CrowdfundingInfo> getListAfterId(int infoId) {
        return this.crowdfundingInfoDao.getListAfterId(infoId);
    }


    @Override
    public List<AdminWorkOrderReportVo> getCrowdfundingReportInfo(String title, Integer caseId, long userId, String name, int current, int pageSize) {
        PageHelper.startPage(current, pageSize);
        return crowdfundingInfoDao.getCrowdfundingReportInfo(title, caseId, userId, name);
    }

    @Override
    public List<AdminReportAddTrustBo> selectCaseAuditStatusByCaseIds(List<String> infoIdList) {
        return crowdfundingInfoSlaveDao.selectCaseAuditStatusByCaseIds(infoIdList);
    }


    @Override
    public List<CrowdfundingInfo> selectByUserIdWhereNotEnd(List<Long> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }

        List<Long> ids = new ArrayList<Long>(new HashSet<Long>(list));//去重
        List<CrowdfundingInfo> crowdfundingInfo = new ArrayList<>();
        List<List<Long>> splitList = Lists.partition(ids, 500);//分割
        for (List<Long> split : splitList) {//分批查询
            List<CrowdfundingInfo> infos = crowdfundingInfoDao.selectByUserIdWhereNotEnd(split);
            if (infos != null && infos.size() > 0) {
                crowdfundingInfo.addAll(infos);
            }
        }
        return crowdfundingInfo;
    }

    @Override
    public List<Integer> selectByCaseIdNotEnd(List<Integer> caseList) {
        if (CollectionUtils.isEmpty(caseList)) {
            return Lists.newArrayList();
        }

        List<Integer> caseIds = new ArrayList<Integer>(new HashSet<Integer>(caseList));//去重
        List<Integer> ids = new ArrayList<>();
        List<List<Integer>> splitList = Lists.partition(caseIds, 500);//分割
        for (List<Integer> split : splitList) {//分批查询
            List<Integer> list = crowdfundingInfoDao.selectByCaseIdNotEnd(split);
            if (list != null && list.size() > 0) {
                ids.addAll(list);
            }
        }
        return ids;
    }

    @Override
    public List<CrowdfundingInfo> selectByCaseIdList(List<Integer> caseList) {
        if (CollectionUtils.isEmpty(caseList)) {
            return Lists.newArrayList();
        }
        return crowdfundingInfoDao.selectByCaseIdList(caseList);
    }

    @Override
    public List<AdminCfFundUseAuditInfo> getFundUseAuditInfoByUnionSelect(List<Integer> crowdfundingIds, Integer progressStatus, String drawCashStartTime,
                                                                          String drawCashEndTime, String fundUseSubmitStartTime, String fundUseSubmitEndTime,
                                                                          Integer current, Integer pageSize,String hasReport) {
//		PageHelper.startPage(currentPage, pageSize);
        List<AdminCfFundUseAuditInfo> adminCfFundUseAuditInfoList = adminCfFundUseAuditDao.getFundUseAuditInfoByUnionSelect(crowdfundingIds, progressStatus, drawCashStartTime, drawCashEndTime,
                fundUseSubmitStartTime, fundUseSubmitEndTime, current, pageSize, hasReport);
        return adminCfFundUseAuditInfoList;
    }

    @Override
    public int selectRecordNums(List<Integer> crowdfundingIds, Integer progressStatus, String drawCashStartTime, String drawCashEndTime,
                                String fundUseSubmitStartTime, String fundUseSubmitEndTime, String hasReport) {
        Integer records = adminCfFundUseAuditDao.getRecordsByMultiField(crowdfundingIds, progressStatus, drawCashStartTime, drawCashEndTime,
                fundUseSubmitStartTime, fundUseSubmitEndTime, hasReport);
        return records == null ? 0 : records;
    }


    @Override
    public List<CrowdfundingInfoVo> selectBaseApproveListPages(int current, int pageSize) {

        //首页不启用分页插件，直接搞

        if (current == 1) {
            //不启用分页插件
            Page page = new Page();
            page.setReasonable(true);
            SqlUtil.setLocalPage(page);

            return crowdfundingInfoSlaveDao.selectBaseApproveListOnlyOne(pageSize);

        } else {
//            PageHelper.startPage(current, pageSize);
            return crowdfundingInfoSlaveDao.selectBaseApproveListPages((current - 1) * pageSize, pageSize);
        }
    }

    @Override
    public List<CrowdfundingInfoVo> selectBaseContactListPages(int current, int pageSize) {
//        PageHelper.startPage(current, pageSize);
        return crowdfundingInfoSlaveDao.selectBaseContactListPages((current - 1) * pageSize, pageSize);
    }


    @Override
    public List<CrowdfundingInfo> selectByRepeatCase() {
        return crowdfundingInfoSlaveDao.selectByRepeatCase();
    }

    @Override
    public int updateTitleImg(String titleImg, int id) {
        return crowdfundingInfoDao.updateTitleImg(titleImg, id);
    }

    @Override
    public List<String> generateInfoUuids(String caseIds, List<String> submitInfoIds) {

        if (org.apache.commons.lang.StringUtils.isNotBlank(caseIds)) {
            List<Integer> infoids = Lists.newArrayList();

            try {
                List<String> infoidString = Splitter.on(";").splitToList(caseIds);
                for (String oneinfoid : infoidString) {
                    infoids.add(IntegerUtil.parseInt(oneinfoid));
                }
            } catch (Throwable e) {
                log.info("split infoid is error:", e);
            }

            return getUnionInfoUuids(infoids, submitInfoIds);

        }
        return submitInfoIds;
    }

    private List<String> getUnionInfoUuids(List<Integer> caseIds, List<String> submitInfoIds) {

        List<String> infoUuids = getListByIds(caseIds).stream().map(CrowdfundingInfo::getInfoId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(infoUuids) || CollectionUtils.isEmpty(submitInfoIds)) {
            return infoUuids;
        }

        List<String> result = Lists.newArrayList();
        for (String infoUuid : infoUuids) {
            if (submitInfoIds.contains(infoUuid)) {
                result.add(infoUuid);
            }
        }

        return result;
    }

    @Override
    public boolean setVoCaseStatus(List<ReportCaseVo> reportCaseVoList){
        //获取 案例id
        List<Integer> infoids=reportCaseVoList.stream().map(ReportCaseVo::getId).collect(Collectors.toList());
        //获取筹款集合
        List<CrowdfundingInfo> crowdfundingInfoList= getListByIds(infoids);
        //获取infoUuids集合
        List<String> crowdfundingInfoUuIds=crowdfundingInfoList.stream().map(CrowdfundingInfo::getInfoId).collect(Collectors.toList());

        //获取退款记录集合
        Response<List<AdminCfRefund>> cfRefundsResponse = financeDelegate.getRefundByInfoUuids(crowdfundingInfoUuIds);
        if (cfRefundsResponse.notOk()) {
            return false;
        }

        //获取提现记录集合
        Response<Map<Integer, CfDrawCashApplyVo>>cfDrawCashesResponse = financeDelegate.getApplyInfoMap(infoids);
        Map<Integer, CfDrawCashApplyVo> drawCashApplyVoMap = cfDrawCashesResponse.getData();
        if (null == drawCashApplyVoMap) {
            drawCashApplyVoMap = Maps.newHashMap();
        }
        //获取退款记录集合
        List<AdminCfRefund> cfRefunds = cfRefundsResponse.getData();

        //写入案例状态
        for(ReportCaseVo reportCaseVo : reportCaseVoList){
            for(CrowdfundingInfo crowdfundingInfo:crowdfundingInfoList){
                if (reportCaseVo.getId() == crowdfundingInfo.getId()){
                    if (crowdfundingInfo.getStatus().value() != 2){
                        reportCaseVo.setCaseStatus(AdminCfCaseStatus.APPROVE_NO.getValue());
                    } else {
                        reportCaseVo.setCaseStatus(AdminCfCaseStatus.APPROVE_FINISH.getValue());
                        this.setCaseDrawStatus(reportCaseVo, drawCashApplyVoMap.get(crowdfundingInfo.getId()));
                        this.setCaseRefundStatus(reportCaseVo, cfRefunds, crowdfundingInfo.getInfoId());
                    }
                }
            }
        }
        return true;
    }

    //写入案例体现状态
    private ReportCaseVo setCaseDrawStatus(ReportCaseVo reportCaseVo, CfDrawCashApplyVo drawCashApplyVo) {
        //如果是空不改变
        if (null == drawCashApplyVo) {
            return reportCaseVo;
        }
        BuildDrawStatusUtil.buildDrawStatus(reportCaseVo, drawCashApplyVo);
        return reportCaseVo;
    }

    //写入案例退款状态
    private ReportCaseVo setCaseRefundStatus(ReportCaseVo reportCaseVo,
                                             List<AdminCfRefund> cfRefunds,String infoUuid) {
        //如果是空不改变
        if(CollectionUtils.isEmpty(cfRefunds))
        {
            return reportCaseVo;
        }
        for (AdminCfRefund cfRefund:cfRefunds){
            if(cfRefund.getInfoUuid().equals(infoUuid)){
                if(cfRefund.getApplyStatus() > NewCfRefundConstant.ApplyStatus.UNSUBMIT.getCode()){
                    //7.已申请退款
                    reportCaseVo.setCaseStatus(AdminCfCaseStatus.REFUND_SUBMIT.getValue());
                }
            }
        }
        return reportCaseVo;
    }

    @Override
    public List<PreposeMaterialAdminModel> selectMaterialByCaseId(int caseId) {
        RpcResult<List<PreposeMaterialModel.MaterialInfoVo>> materialResult = preposeMaterialClient.selectMaterialByCaseId(caseId);
        if (materialResult == null || CollectionUtils.isEmpty(materialResult.getData())) {
            return Lists.newArrayList();
        }
        List<PreposeMaterialModel.MaterialInfoVo> infoVoList = materialResult.getData();


        return fillPreposeApprovalPoint(infoVoList);
    }

    private List<PreposeMaterialAdminModel> fillPreposeApprovalPoint(List<PreposeMaterialModel.MaterialInfoVo> infoVoList) {
        if (CollectionUtils.isEmpty(infoVoList)) {
            return Lists.newArrayList();
        }

        List<Long> materialIds = Lists.newArrayList();
        for (PreposeMaterialModel.MaterialInfoVo infoVo : infoVoList) {
            materialIds.add(infoVo.getId());
        }

        Map<Long, ClewPreproseMaterialResult> materialIdRecordMapping =  getPreposeIdApproveMapping(materialIds);
        List<PreposeMaterialAdminModel> adminModels = Lists.newArrayList();
        for (PreposeMaterialModel.MaterialInfoVo infoVo : infoVoList) {
            PreposeMaterialAdminModel adminModel = new PreposeMaterialAdminModel();
            BeanUtils.copyProperties(infoVo, adminModel);
            adminModel.setApprovePoints(materialIdRecordMapping.get(infoVo.getId()));
            adminModel.setRaiseMobileMask(maskUtil.buildByDecryptPhone(infoVo.getRaiseMobile()));
            adminModel.setRaiseMobile(null);
            adminModel.setPatientIdCardMask(maskUtil.buildByDecryptStrAndType(infoVo.getPatientIdCard(), DesensitizeEnum.IDCARD));
            adminModel.setPatientIdCard(null);
//            adminModel.setSelfCryptoIdcardMask(maskUtil.buildByDecryptStrAndType(infoVo.getSelfCryptoIdcard(), DesensitizeEnum.IDCARD));
            adminModel.setSelfCryptoIdcard(null);
            adminModels.add(adminModel);
        }

        adminModels.sort(Comparator.comparing(PreposeMaterialAdminModel::getId).reversed());
        return adminModels;
    }

    private Map<Long, ClewPreproseMaterialResult> getPreposeIdApproveMapping(List<Long> materialIds) {
        Response<List<ClewPreproseMaterialResult>> materialApproveResult = clewMaterialFeignClient.getApproveLog(materialIds);

        log.info("查询外呼的报备. materialId:{}, result:{}", materialIds, JSON.toJSONString(materialApproveResult));
        Map<Long, ClewPreproseMaterialResult> materialIdApproveMapping = Maps.newHashMap();
        if (materialApproveResult == null || CollectionUtils.isEmpty(materialApproveResult.getData())) {
            return materialIdApproveMapping;
        }

        for (ClewPreproseMaterialResult material : materialApproveResult.getData()) {
            materialIdApproveMapping.put(material.getPreposeMaterialId(), material);
        }
        return materialIdApproveMapping;
    }

    @Override
    public boolean updateContentImageStatus(int caseId, int sourceStatus, int targetStatus) {
        int count = crowdfundingInfoDao.updateContentImageStatus(caseId, sourceStatus, targetStatus);
        return count > 0;
    }

    @Override
    public boolean updateContentImage(int caseId, String contentImage, int sourceStatus, int targetStatus) {
        int count = crowdfundingInfoDao.updateContentImage(caseId, contentImage, sourceStatus, targetStatus);
        return count > 0;
    }

    @Override
    public List<CrowdfundingInfo> selectByUserId(long userId) {
        return crowdfundingInfoDao.selectByUserId(userId);
    }
}
