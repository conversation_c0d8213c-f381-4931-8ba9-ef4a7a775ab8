package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.CfReportAddTrustSnapshotBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfReportCommitmentInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.report.AdminCfReportProveDisposeActionBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfReportAddTrustDao;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCredibleInfoDAO;
import com.shuidihuzhu.cf.dao.crowdfunding.CfReportAddTrustSnapshotDao;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.enums.crowdfunding.CredibleTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfCredibleInfoDO;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportAddTrust;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportCommitmentInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfReportAddTrustDisposeVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfReportAddTrustVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfReportCredibleInfoVO;
import com.shuidihuzhu.cf.model.report.CfReportAddTrustSnapshot;
import com.shuidihuzhu.cipher.ShuidiCipher;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: lixiaoshuang
 * @create: 2020-03-25 20:59
 **/
@Service
@Slf4j
public class CfReportAddTrustSnapshotBizImpl implements CfReportAddTrustSnapshotBiz {

    @Autowired
    private CfReportAddTrustSnapshotDao cfReportAddTrustSnapshotDao;
    @Autowired
    private CfReportCommitmentInfoBiz cfReportCommitmentInfoBiz;
    @Autowired
    private AdminCfReportAddTrustDao adminCfReportAddTrustDao;
    @Autowired
    private AdminCredibleInfoDAO adminCredibleInfoDAO;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private AdminCfReportProveDisposeActionBiz adminCfReportProveDisposeActionBiz;
    @Autowired
    private MaskUtil maskUtil;

    @Override
    public long addSnapshot(CfReportAddTrust cfReportAddTrust) {
        CfReportAddTrustSnapshot cfReportAddTrustSnapshot = new CfReportAddTrustSnapshot();
        cfReportAddTrustSnapshot.setAddTrustId(cfReportAddTrust.getId());
        cfReportAddTrustSnapshot.setAuditStatus(cfReportAddTrust.getAuditStatus());

        CfCredibleInfoDO cfCredibleInfoDO = adminCredibleInfoDAO.queryBySubId(cfReportAddTrust.getId(), CredibleTypeEnum.SUPPLY_VERFIFY.getKey());
        if (Objects.nonNull(cfCredibleInfoDO) && StringUtils.isNotBlank(cfCredibleInfoDO.getMobile())) {
            cfCredibleInfoDO.setMobile(shuidiCipher.decrypt(cfCredibleInfoDO.getMobile()));
        }
        String disposeAction = adminCfReportProveDisposeActionBiz.getDisposeAction(cfReportAddTrust.getId());
        List<CfReportAddTrustDisposeVo> cfReportAddTrustDisposeVos = Lists.newArrayList();
        if (StringUtils.isNotBlank(disposeAction)) {
            try {
                cfReportAddTrustDisposeVos = JSONObject.parseObject(disposeAction, new TypeReference<>() {
                });
            } catch (Exception e) {
                log.error("queryById parse error", e);
            }
        }
        //是否下发了承诺书
        if (cfReportAddTrust.isIssuedCommitment()) {
            CfReportCommitmentInfo cfReportCommitmentInfo = cfReportCommitmentInfoBiz.findByIncrTrustId(cfReportAddTrust.getId());
            cfReportAddTrustSnapshot.buildAddTrustSnapshot(cfReportAddTrust, cfCredibleInfoDO, cfReportCommitmentInfo, cfReportAddTrustDisposeVos);
        } else {
            cfReportAddTrustSnapshot.buildAddTrustSnapshot(cfReportAddTrust, cfCredibleInfoDO, null, cfReportAddTrustDisposeVos);
        }
        cfReportAddTrustSnapshotDao.insertOne(cfReportAddTrustSnapshot);
        return cfReportAddTrustSnapshot.getId();
    }

    @Override
    public List<CfReportCredibleInfoVO> getSnapshot(long addTrustId, int auditStatus) {
        CfReportAddTrust cfReportAddTrust = adminCfReportAddTrustDao.queryById(addTrustId);
        if (Objects.isNull(cfReportAddTrust)) {
            return Collections.emptyList();
        }
        List<CfReportAddTrustSnapshot> cfReportAddTrustSnapshots = cfReportAddTrustSnapshotDao.findByAddTrustIdAndAuditStatus(addTrustId, auditStatus);
        return cfReportAddTrustSnapshots.stream().map(cfReportAddTrustSnapshot -> {
            String addTrustSnapshot = cfReportAddTrustSnapshot.getAddTrustSnapshot();
            CfReportCredibleInfoVO cfReportCredibleInfoVO = JSONObject.parseObject(addTrustSnapshot, CfReportCredibleInfoVO.class);
            Optional.ofNullable(cfReportCredibleInfoVO)
                    .filter(r -> StringUtils.isNotBlank(r.getMobile()))
                    .ifPresent(r -> {
                        String mobile = r.getMobile();
                        r.setMobileMask(maskUtil.buildByDecryptPhone(mobile));
                        r.setMobile(StringUtils.EMPTY);
                    });
            return cfReportCredibleInfoVO;
        }).collect(Collectors.toList());
    }
}