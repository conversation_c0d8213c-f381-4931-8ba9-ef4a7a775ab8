package com.shuidihuzhu.cf.biz.mina.impl;

import com.github.pagehelper.PageHelper;
import com.shuidihuzhu.cf.biz.mina.AdminTopicCountBiz;
import com.shuidihuzhu.cf.dao.mina.AdminTopicCountDao;
import com.shuidihuzhu.cf.model.miniprogram.CfTopicShareCommentCount;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class AdminTopicCountBizImpl implements AdminTopicCountBiz {
    @Autowired
    private AdminTopicCountDao adminTopicCountDao;

    @Override
    public List<CfTopicShareCommentCount> listTopicCountOrderPage(int pageNum,
                                                                  int pageSize,
                                                                  int sortMode,
                                                                  List<Integer> cfTopicPublishIds) {
        if (sortMode <= 0 || sortMode >2) {
            return Collections.EMPTY_LIST;
        }
        if (sortMode == 1) {
            PageHelper.startPage(pageNum, pageSize);
            return adminTopicCountDao.listOrderByCommentNum(cfTopicPublishIds);
        } else {
            PageHelper.startPage(pageNum, pageSize);
            return adminTopicCountDao.listOrderByPraiseNum(cfTopicPublishIds);
        }
    }

    @Override
    public int deleteByTopicId(int topicId) {
        if (topicId <= 0) {
            return 0;
        }
        return adminTopicCountDao.deleteByTopicId(topicId);
    }
}
