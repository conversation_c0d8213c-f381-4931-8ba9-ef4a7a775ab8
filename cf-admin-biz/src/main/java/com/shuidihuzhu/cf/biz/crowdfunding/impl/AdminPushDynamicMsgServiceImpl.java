package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminPushDynamicMsgService;
import com.shuidihuzhu.cf.biz.crowdfunding.supply.CfSupplyActionBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.supply.CfSupplyProgressBiz;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.admin.DynamicMsgEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfOperatingRecordEnum;
import com.shuidihuzhu.cf.finance.client.feign.CfFinancePauseFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.enums.CfDrawCashPauseRecordEnum;
import com.shuidihuzhu.cf.model.admin.CfPushDynamicMsgModel;
import com.shuidihuzhu.cf.model.admin.vo.CfDynamicMsgVo;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.mq.IAdminCommonMessageHelperService;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.client.cf.workorder.CfUgcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.UgcWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderLevel;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @package: com.shuidihuzhu.cf.biz.crowdfunding.impl
 * @Author: liujiawei
 * @Date: 2020-01-09  11:07
 */
@Service
@Slf4j
public class AdminPushDynamicMsgServiceImpl implements AdminPushDynamicMsgService {
    @Autowired
    private IAdminCommonMessageHelperService iAdminCommonMessageHelperService;
    @Autowired
    private ApplicationService applicationService;
    @Autowired
    private CfFinancePauseFeignClient cfFinancePauseFeignClient;
    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private CfSupplyActionBiz cfSupplyActionBiz;
    @Autowired
    private CfSupplyProgressBiz cfSupplyProgressBiz;
    @Resource
    private CfUgcWorkOrderClient ugcWorkOrderClient;
    @Autowired(required = false)
    private Producer producer;
    /**
     * 下发动态消息
     *
     * @param caseId
     * @param pushMsgType 下发类型
     * @param supplyProgressId 动态表Id
     * @param supplyActionId 下发动态记录表Id
     */
    @Async
    @Override
    public void pushDynamicMsg(int caseId, DynamicMsgEnum.PushMsgType pushMsgType, int supplyProgressId, int supplyActionId,long workOrderId) {
        if (caseId <= 0 || pushMsgType == null || supplyProgressId < 0 || supplyActionId <= 0) {
            return;
        }
        // 原始下发的消息
        Message orginMessage = iAdminCommonMessageHelperService
                .getCfPushDynamicMsg(new CfPushDynamicMsgModel(caseId, pushMsgType.getCode(), supplyProgressId, supplyActionId,workOrderId));
        iAdminCommonMessageHelperService.send(orginMessage);

        // 24小时催发
        this.sendDelayMq(new CfPushDynamicMsgModel(caseId, DynamicMsgEnum.PushMsgType.URGE_24H.getCode(), supplyProgressId, supplyActionId,workOrderId), 1);

        // 72小时催发
        this.sendDelayMq(new CfPushDynamicMsgModel(caseId, DynamicMsgEnum.PushMsgType.URGE_72H.getCode(), supplyProgressId, supplyActionId,workOrderId), 3);
    }


    /**
     * 处理接收到的下发信息
     *
     * @param cfPushDynamicMsgModel
     */
    @Override
    public void executeDynamicMsg(CfPushDynamicMsgModel cfPushDynamicMsgModel) {
        if (cfPushDynamicMsgModel == null) {
            return;
        }
        DynamicMsgEnum.PushMsgType pushMsgType = DynamicMsgEnum.PushMsgType.getByCode(cfPushDynamicMsgModel.getPushMsgType());

        CfInfoSupplyAction cfInfoSupplyAction = cfSupplyActionBiz.getById(cfPushDynamicMsgModel.getSupplyActionId());
        if (cfInfoSupplyAction == null) {
            return;
        }
        if (pushMsgType.equals(DynamicMsgEnum.PushMsgType.ACTIVE_PUSH)) {
            if (CfInfoSupplyAction.findHandleStatusByCode(cfInfoSupplyAction.getHandleStatus())
                    != CfInfoSupplyAction.SupplyHandleStatus.init) {
                log.warn("【下发动态审核】主动下发,动态不合法无需下发{},{}", cfPushDynamicMsgModel, JSON.toJSONString(cfInfoSupplyAction));
                return;
            }
        } else if (pushMsgType.equals(DynamicMsgEnum.PushMsgType.REJECT_PUSH)){
            if (CfInfoSupplyAction.findHandleStatusByCode(cfInfoSupplyAction.getHandleStatus())
                    != CfInfoSupplyAction.SupplyHandleStatus.reject) {
                log.warn("【下发动态审核】驳回下发,动态不合法无需下发{},{}", cfPushDynamicMsgModel, JSON.toJSONString(cfInfoSupplyAction));
                return;
            }
        } else {
            if (CfInfoSupplyAction.findHandleStatusByCode(cfInfoSupplyAction.getHandleStatus())
                    != CfInfoSupplyAction.SupplyHandleStatus.init
                    && CfInfoSupplyAction.findHandleStatusByCode(cfInfoSupplyAction.getHandleStatus())
                    != CfInfoSupplyAction.SupplyHandleStatus.reject) {
                log.info("【下发动态审核】催下发,动态不合法无需下发{},{}", cfPushDynamicMsgModel, JSON.toJSONString(cfInfoSupplyAction));
                return;
            }
            //可能存在 催发送期间已经连续驳回多次，之前的催发送则无效
            if (cfInfoSupplyAction.getHandleStatus() == CfInfoSupplyAction.SupplyHandleStatus.reject.getCode()
                    && cfPushDynamicMsgModel.getSupplyProgressId() != 0) {
                List<CfInfoSupplyProgress> cfInfoSupplyProgressList = cfSupplyProgressBiz
                        .getByActionId(cfInfoSupplyAction.getId());
                if (CollectionUtils.isNotEmpty(cfInfoSupplyProgressList)) {
                    long lastProgressId = cfInfoSupplyProgressList
                            .stream()
                            .max(Comparator.comparing(CfInfoSupplyProgress::getId))
                            .get().getId();
                    if (lastProgressId != cfPushDynamicMsgModel.getSupplyProgressId()) {
                        log.info("【下发动态审核】该条崔下发无效{},{}", cfPushDynamicMsgModel, JSON.toJSONString(cfInfoSupplyAction));
                        return;
                    }
                }
            }
        }

        // 发送消息
//        this.sendDynamicMsg(cfPushDynamicMsgModel);

    }

    /**
     * 动态审核上传
     * 已废弃
     * @param cfDynamicMsgVo
     */
    @Override
    public void executeUploadDynamicInfo(CfDynamicMsgVo cfDynamicMsgVo) {
        if (cfDynamicMsgVo == null) {
            return;
        }
        //查找案例信息,如果没有直接放回并打印日志
        CrowdfundingInfo fundingInfo = crowdfundingInfoBiz.getFundingInfo(cfDynamicMsgVo.getInfoUuid());
        if (fundingInfo == null) {
            log.warn("无案例信息,无法生成下发工单");
            return;
        }
        //校验是否能提交生成工单
        int supplyActionId = cfDynamicMsgVo.getSupplyActionId();
        CfInfoSupplyAction supplyAction = cfSupplyActionBiz.getById(supplyActionId);
        if (supplyAction == null || CfInfoSupplyAction.canSubmit(supplyActionId)) {
            log.info("下发动态无效,supplyAction:{}", JSON.toJSONString(supplyAction));
            return;
        }
        //生成下发动态
        CfInfoSupplyProgress supplyProgress = new CfInfoSupplyProgress();
        supplyProgress.setUserId(fundingInfo.getUserId());
        supplyProgress.setCaseId(fundingInfo.getId());
        supplyProgress.setInfoUUId(cfDynamicMsgVo.getInfoUuid());
        supplyProgress.setContent(cfDynamicMsgVo.getContent());
        supplyProgress.setImgUrls(Joiner.on(',').join(cfDynamicMsgVo.getImgs()));
        supplyProgress.setProgressActionId(supplyActionId);
        cfSupplyProgressBiz.add(supplyProgress);
        //生成工单
        long supplyProgressId = supplyProgress.getId();
        log.info("下发动态supplyProgressId:{}", supplyProgressId);
        UgcWorkOrder workOrder = new UgcWorkOrder();
        workOrder.setCaseId(fundingInfo.getId());
        workOrder.setOrderType(WorkOrderType.xiafaprogress.getType());
        workOrder.setOrderlevel(OrderLevel.edium.getType());
        workOrder.setSupplyProgressId(String.valueOf(supplyProgressId));

        Response<Long> response =  ugcWorkOrderClient.createUgc(workOrder);
        if (response.notOk()) {
            throw new RuntimeException("调用工单系统异常");
        }
        //修改下发状态,最后修改下发状态,防止调用工单系统失败,下发状态直接卡在待审核但无工单
        cfSupplyActionBiz.updateHandleStatus(supplyActionId, CfInfoSupplyAction.SupplyHandleStatus.wait_audit.getCode());
    }


    /**
     * 下发动态审核--暂停打款
     *
     * @param caseId
     * @param operatorId
     * @return
     */
    @Override
    public Response pauseDrawCashByDynamic(int caseId, int operatorId) {

        FeignResponse feignResponse = cfFinancePauseFeignClient.addPauseV2(operatorId, caseId, CfDrawCashPauseRecordEnum.PauseSourceTypeEnum.PUSH_DYNAMIC_MSG.getCode(),
                Lists.newArrayList(CfDrawCashPauseRecordEnum.PauseReasonTypeEnum.PUSH_DYNAMIC_MSG.getCode()),
                "下发动态审核--暂停打款", CfDrawCashPauseRecordEnum.RecordStatusEnum.PAUSE.getCode(), true, false);

        log.info("下发动态审核--暂停打款:{}", JSON.toJSONString(feignResponse));
        return NewResponseUtil.makeResponse(feignResponse.getCode(), feignResponse.getMsg(), feignResponse.getData());
    }


    /**
     * 下发动态审核--解除暂停
     *
     * @param caseId
     * @return
     */
    @Override
    public Response recoverPauseDrawCashByDynamic(int caseId) {

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }

        FeignResponse feignResponse = cfFinancePauseFeignClient.recoverBySourceType(crowdfundingInfo.getInfoId(),
                crowdfundingInfo.getId(),
                CfDrawCashPauseRecordEnum.PauseSourceTypeEnum.PUSH_DYNAMIC_MSG.getCode(),
                CfDrawCashPauseRecordEnum.RecordStatusEnum.RECOVER.getCode(),
                CfOperatingRecordEnum.Role.SYSTEM.getCode(), 102, "系统",
                "下发动态审核--完成 设为通过");

        log.info("下发动态审核--解除暂停:{}", JSON.toJSONString(feignResponse));
        return NewResponseUtil.makeResponse(feignResponse.getCode(), feignResponse.getMsg(), feignResponse.getData());
    }

    private void sendDelayMq(CfPushDynamicMsgModel cfPushDynamicMsgModel, int day) {

        //测试环境快速补发
        long dispatchTime = new DateTime().plusMinutes(2).getMillis();

        if (applicationService.isProduction()) {
            dispatchTime = new DateTime().plusDays(day).getMillis();
        }
        Message message = Message.ofSchedule(MQTopicCons.CF, MQTagCons.CF_PUSH_DYNAMIC_MSG,
                "" + System.nanoTime() + cfPushDynamicMsgModel.getCaseId() + day, cfPushDynamicMsgModel, dispatchTime / 1000);
        MessageResult result = producer.send(message);
        log.info("动态催下发消息发送. msg：{}, result:{}", message, result);
    }

}
