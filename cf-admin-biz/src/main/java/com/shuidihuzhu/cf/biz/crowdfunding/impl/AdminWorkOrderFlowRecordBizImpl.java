package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderFlowRecordBiz;
import com.shuidihuzhu.cf.dao.admin.AdminWorkOrderFlowRecordDao;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderFlowRecord;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowStatistics;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * Created by Ahrievil on 2017/12/25
 */
@Service
public class AdminWorkOrderFlowRecordBizImpl implements AdminWorkOrderFlowRecordBiz {

    @Autowired
    private AdminWorkOrderFlowRecordDao adminWorkOrderFlowRecordDao;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    @Override
    public int insertOne(AdminWorkOrderFlowRecord adminWorkOrderFlowRecord) {
        if (Strings.isNullOrEmpty(adminWorkOrderFlowRecord.getMobile())) {
            adminWorkOrderFlowRecord.setEncryptMobile("");
        } else {
            adminWorkOrderFlowRecord.setEncryptMobile(oldShuidiCipher.aesEncrypt(adminWorkOrderFlowRecord.getMobile().trim()));
        }
        return adminWorkOrderFlowRecordDao.insertOne(adminWorkOrderFlowRecord);
    }

    @Override
    public int insertList(List<AdminWorkOrderFlowRecord> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        for(AdminWorkOrderFlowRecord record :list)
        {
            if (Strings.isNullOrEmpty(record.getMobile())) {
                record.setEncryptMobile("");
            } else {
                record.setEncryptMobile(oldShuidiCipher.aesEncrypt(record.getMobile().trim()));
            }
        }
        return adminWorkOrderFlowRecordDao.insertList(list);
    }

    @Override
    public List<AdminWorkOrderFlowRecord> selectAllByWorkOrderId(long workOrderId, List<Integer> operateTypeCodes) {
        return adminWorkOrderFlowRecordDao.selectAllByWorkOrderId(workOrderId, operateTypeCodes);
    }

    @Override
    public List<AdminWorkOrderFlowRecord> selectByOperatorIdsAndTime(AdminWorkOrderFlowStatistics.searchParam param) {
        return adminWorkOrderFlowRecordDao.selectByOperatorIdsAndTime(param);
    }

    @Override
    public List<AdminWorkOrderFlowRecord> selectWithAssignRecords(@Param("beginTime") Date beginTime,
                                                           @Param("endTime") Date endTime,
                                                           @Param("orderOperatorId") int orderOperatorId) {

        if (beginTime == null || endTime == null || endTime.before(beginTime)) {
            return Lists.newArrayList();
        }

        if (orderOperatorId <= 0) {
            return Lists.newArrayList();
        }
        return adminWorkOrderFlowRecordDao.selectWithAssignRecords(beginTime, endTime, orderOperatorId);
    }

}
