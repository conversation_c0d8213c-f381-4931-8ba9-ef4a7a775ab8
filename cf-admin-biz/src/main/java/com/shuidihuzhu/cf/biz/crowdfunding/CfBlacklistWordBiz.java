package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.CfBlacklistWordTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfBlacklistWord;

import java.util.List;

/**
 * <AUTHOR> Ahrievil
 */
public interface CfBlacklistWordBiz {

    int add(CfBlacklistWord cfBlacklistWord);

    int addList(List<CfBlacklistWord> list);

    List<String> selectAllWordsLimit(CfBlacklistWordTypeEnum cfBlacklistWordTypeEnum, int start, int size);
}
