package com.shuidihuzhu.cf.biz.admin.workorder;

import com.shuidihuzhu.cf.model.admin.AdminWorkOrderMoneyBackExt;
import com.shuidihuzhu.cf.model.admin.vo.AdminWorkOrderMoneyBackExtVO;

import java.util.List;

/**
 * 返还款相关信息工单表单业务接口
 */
public interface AdminWorkOrderMoneyBackExtBiz {

    /**
     * 保存数据
     * @param record 记录
     * @return 保存结果
     */
    boolean save(AdminWorkOrderMoneyBackExt record);

    /**
     * 批量保存数据
     * @param records 记录列表
     * @return 保存结果
     */
    boolean batchSave(List<AdminWorkOrderMoneyBackExt> records);

    /**
     * 根据VO列表批量保存数据
     * @param voList VO列表
     * @param workOrderId 工单ID
     * @return 保存结果
     */
    boolean batchSaveByVOList(List<AdminWorkOrderMoneyBackExtVO> voList, Long workOrderId);

    /**
     * 根据工单ID查询
     * @param workOrderId 工单ID
     * @return 记录列表
     */
    List<AdminWorkOrderMoneyBackExt> findByWorkOrderId(Long workOrderId);

    /**
     * 根据案例ID查询
     * @param caseId 案例ID
     * @return 记录列表
     */
    List<AdminWorkOrderMoneyBackExt> findByCaseId(Integer caseId);

    /**
     * 根据工单ID和案例ID查询
     * @param workOrderId 工单ID
     * @param caseId 案例ID
     * @return 记录
     */
    AdminWorkOrderMoneyBackExt findByWorkOrderIdAndCaseId(Long workOrderId, Integer caseId);
} 