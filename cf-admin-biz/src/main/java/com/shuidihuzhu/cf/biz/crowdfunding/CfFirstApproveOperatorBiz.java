package com.shuidihuzhu.cf.biz.crowdfunding;

import com.github.pagehelper.PageInfo;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.vo.crowdfunding.WorkOrderFirstApprove;
import lombok.Data;

import java.util.List;

public interface CfFirstApproveOperatorBiz {
    CfFirstApproveOperator getFirstApproveOperatorById(int oporatorId);

    void insertOperator(int operatorId, int count);

    void updateFirstApprovesCount(int operatorId, int count);

    void closeFirstUgc(int infoId,String failMsg, boolean pass, int userId);

    int getActualRejectCode(int code);

    PageInfo<IdcardVerifyWhiteList> queryAllWhiteIdCardList(int current, int pageSize, String name, String idCard);

    int deleteFirstApproveWhiteIdById(int id);

    boolean addFirstApproveWhiteIdCard(String name, String idCard, String images, int reason, String otherReason, int operatorId);

    List<IdcardVerifyWhiteListRecord> idCardWhiteListOperationRecord(int id);

    void fillUgcBaseInfoDetail(WorkOrderFirstApprove firstApprove);

    void finishUgcBaseInfo(int userId, int actualCode, int caseId);

    boolean isChild(CfFirsApproveMaterial material);

    String getIdcardWithWildchar(String cryptoIdcard);

     void sendReCallMsgAfterReject(int caseId, long ugcTaskId, String tags);
    @Data
    class RecallAfterRejectObject {
        int caseId;
        long ugcTaskId;
        int afterDay;

        public RecallAfterRejectObject(int caseId, long ugcTaskId, int afterDay) {
            this.caseId = caseId;
            this.ugcTaskId = ugcTaskId;
            this.afterDay = afterDay;
        }
    }
    
    String getGuideUserLaunchChannel(String sourceChannel);
}
