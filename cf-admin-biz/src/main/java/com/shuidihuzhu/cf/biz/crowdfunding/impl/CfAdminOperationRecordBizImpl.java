package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.CfAdminOperationRecordBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfAdminOperationRecordDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfOperationRecord;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Created by ahrievil on 2017/2/7.
 */
@Service
public class CfAdminOperationRecordBizImpl implements CfAdminOperationRecordBiz {

    private static final Logger LOGGER = LoggerFactory.getLogger(CfAdminOperationRecordBizImpl.class);

    @Autowired
    private CfAdminOperationRecordDao cfAdminOperationRecordDao;

    @Override
    public int add(CfOperationRecord cfOperationRecord) {
        return cfAdminOperationRecordDao.add(cfOperationRecord);
    }
    @Override
    public void addOneOperationRecord(String infoId,long  userId,int opertion,String reason){
        CfOperationRecord cfOperationRecord = new CfOperationRecord();
        cfOperationRecord.setInfoId(infoId);
        //FIXME 这里有个强转
        // 这里强转是安全的，userid只有sea后台的用户id
        cfOperationRecord.setOperatorId((int)userId);
        cfOperationRecord.setOperation(opertion);
        cfOperationRecord.setReason(reason);
        try {
            cfAdminOperationRecordDao.add(cfOperationRecord);
        } catch (Exception e) {
            LOGGER.error("addOneOperationRecord error",e,cfOperationRecord);
        }
    }

    @Override
    public CfOperationRecord selectOpByInfoId(String infoId) {
        return cfAdminOperationRecordDao.selectOpByInfoId(infoId);
    }

    @Override
    public CfOperationRecord selectContact(String infoId) {
        return cfAdminOperationRecordDao.selectContact(infoId);
    }

    @Override
    public Map<String, Integer> selectRefuseOperation(int curNum) {
        Map<String, Integer> resultMap = Maps.newHashMap();
        List<Map<String, Object>> maps = cfAdminOperationRecordDao.selectRefuseOperation(curNum);
        for (Map<String, Object> map : maps) {
            String infoId = (String) map.get("infoId");
            long counts = (Long) map.get("counts");
            resultMap.put(infoId, (int) counts);
        }
        return resultMap;
    }

    @Override
    public List<CfOperationRecord> selectOpByInfoIds(List<String> infoUuids) {
        if (CollectionUtils.isEmpty(infoUuids)) {
            return Lists.newArrayList();
        }
        return cfAdminOperationRecordDao.getLastOneByInfoUUidList(infoUuids);
    }

    @Override
    public CfOperationRecord getLastByOperation(String infoId, int operation) {
        return cfAdminOperationRecordDao.getLastByOperation(infoId, operation);
    }
}
