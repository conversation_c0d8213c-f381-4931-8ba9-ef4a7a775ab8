package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingThankLetterBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingThankLetterDao;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingThankLetter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/23.
 */
@Service
public class AdminCrowdfundingThankLetterBizImpl implements AdminCrowdfundingThankLetterBiz {
    @Autowired
    private AdminCrowdfundingThankLetterDao adminCrowdfundingThankLetterDao;
    @Override
    public List<CrowdfundingThankLetter> getThankLetterByTime(String startTime, String endTime) {
        return adminCrowdfundingThankLetterDao.getThankLetterByTime(startTime,endTime);
    }

    @Override
    public int updateThankLetter(CrowdfundingThankLetter crowdfundingThankLetter) {
        return adminCrowdfundingThankLetterDao.updateThankLetter(crowdfundingThankLetter);
    }
}
