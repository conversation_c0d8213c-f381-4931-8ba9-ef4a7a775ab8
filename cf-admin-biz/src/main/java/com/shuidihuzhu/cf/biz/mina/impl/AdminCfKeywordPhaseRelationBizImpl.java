package com.shuidihuzhu.cf.biz.mina.impl;

import com.shuidihuzhu.cf.biz.mina.AdminCfKeywordPhaseRelationBiz;
import com.shuidihuzhu.cf.model.miniprogram.CfKeywordPhaseRelation;
import com.shuidihuzhu.cf.dao.mina.AdminCfKeywordPhaseRelationDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/3/1.
 */
@Service
@Slf4j
public class AdminCfKeywordPhaseRelationBizImpl implements AdminCfKeywordPhaseRelationBiz {

    @Autowired
    private AdminCfKeywordPhaseRelationDao adminCfKeywordPhaseRelationDao;

    @Override
    public int updateByPhaseId(int phaseId, CfKeywordPhaseRelation cfKeywordPhaseRelation) {
        if (phaseId <= 0 || cfKeywordPhaseRelation == null) {
            return 0;
        }
        return this.adminCfKeywordPhaseRelationDao.updateByPhaseId(phaseId, cfKeywordPhaseRelation);
    }
}
