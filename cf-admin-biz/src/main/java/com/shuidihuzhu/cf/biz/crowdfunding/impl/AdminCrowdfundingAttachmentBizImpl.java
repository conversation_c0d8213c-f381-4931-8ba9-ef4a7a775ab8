package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import brave.Tracing;
import com.alibaba.fastjson.JSON;
import com.aliyun.oss.model.CannedAccessControlList;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.cdn.model.v20141111.RefreshObjectCachesRequest;
import com.aliyuncs.cdn.model.v20141111.RefreshObjectCachesResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.aliyuncs.profile.DefaultProfile;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.admin.util.lock.RedisDistributedLock;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingAttachmentBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.feign.CaseInfoApproveStageFeignClient;
import com.shuidihuzhu.cf.client.feign.CfAttachmentFeignClient;
import com.shuidihuzhu.cf.client.feign.CrowdfundingAttachmentFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingAttachmentShardingDao;
import com.shuidihuzhu.cf.domain.caseinfo.CaseInfoApproveStageDO;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachmentDTO;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.OssPictureAttrDO;
import com.shuidihuzhu.cf.util.CrowdfundingAttachmentConverterUtil;
import com.shuidihuzhu.client.cf.api.client.CfImageMaskFeignClient;
import com.shuidihuzhu.client.model.MaskAttachmentVo;
import com.shuidihuzhu.client.model.enums.ImageMaskBizEnum;
import com.shuidihuzhu.common.web.constants.CrowdfundingCons;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.aliyun.enums.OSSBucketDir;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * Created by ahrievil on 2017/5/8.
 */
@Service
@Slf4j
public class AdminCrowdfundingAttachmentBizImpl implements AdminCrowdfundingAttachmentBiz {
    @Autowired
    private Tracing tracing;
    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;
//    private static ExecutorService ALIYUN_TASK_POOL ;
    @Resource
    private CfAttachmentFeignClient cfAttachmentFeignClient;
    @Autowired
    private CaseInfoApproveStageFeignClient approveStageClient;
    @Resource
    private CfImageMaskFeignClient cfImageMaskFeignClient;
    @Resource
    private CrowdfundingAttachmentFeignClient crowdfundingAttachmentFeignClient;

    public static final Set<AttachmentTypeEnum> CANNOT_SHOW_UGC_MANAGES = Sets.newHashSet(
            AttachmentTypeEnum.ATTACH_USER_STAGE_CF,
            AttachmentTypeEnum.ATTACH_FIRST_APPROVE_MEDICAL,
            AttachmentTypeEnum.ATTACH_ALLOWANCE_IMG,
            AttachmentTypeEnum.ATTACH_POVERTY_IMG,

            AttachmentTypeEnum.ATTACH_FUND_USE,
            AttachmentTypeEnum.ATTACH_REPORT_TRUST,
            AttachmentTypeEnum.ATTACH_PROGRESS,
            AttachmentTypeEnum.ATTACH_MODIFY_STAGE_PAYEE_INFO,
            AttachmentTypeEnum.ATTACH_PATIENT_FACE_ID,
            AttachmentTypeEnum.ATTACH_PAYEE_FACE_ID
            );

//    @PostConstruct
//    public void init() {
//        ALIYUN_TASK_POOL = tracing.currentTraceContext().executorService(Executors.newFixedThreadPool(5));
//    }

    @Override
    public List<CrowdfundingAttachment> getByParentId(int parentId) {
        FeignResponse<List<CrowdfundingAttachmentDTO>> fundingAttachmentsResponse = crowdfundingAttachmentFeignClient.getFundingAttachmentToSea(parentId);
        return CrowdfundingAttachmentConverterUtil.toEntityList(Optional.ofNullable(fundingAttachmentsResponse)
                .filter(FeignResponse::ok)
                .map(FeignResponse::getData)
                .orElse(null));
    }

    @Override
    public List<CrowdfundingAttachment> queryAttachment(int parentId) {
        FeignResponse<List<CrowdfundingAttachmentDTO>> queryAttachmentsResponse = crowdfundingAttachmentFeignClient.getFundingAttachmentAllFieldToSea(parentId);
        return CrowdfundingAttachmentConverterUtil.toEntityList(Optional.ofNullable(queryAttachmentsResponse)
                .filter(FeignResponse::ok)
                .map(FeignResponse::getData)
                .orElse(null));
    }

    @Override
    public List<CrowdfundingAttachment> getAttachmentsByType(int parentId, AttachmentTypeEnum type) {

        FeignResponse<List<CrowdfundingAttachmentDTO>> typeAttachmentsResponse = crowdfundingAttachmentFeignClient.getAttachmentsByTypeToSea(parentId, type.value());
        return CrowdfundingAttachmentConverterUtil.toEntityList(Optional.ofNullable(typeAttachmentsResponse)
                .filter(FeignResponse::ok)
                .map(FeignResponse::getData)
                .orElse(null));
    }

    @Override
    public List<CrowdfundingAttachment> getListByInfoIdListAndType(List<Integer> parentIdList, AttachmentTypeEnum type) {
        FeignResponse<List<CrowdfundingAttachmentDTO>> listByInfoIdResponse = crowdfundingAttachmentFeignClient.getListByInfoIdListAndType(parentIdList, type);
        return CrowdfundingAttachmentConverterUtil.toEntityList(Optional.ofNullable(listByInfoIdResponse)
                .filter(FeignResponse::ok)
                .map(FeignResponse::getData)
                .orElse(null));
    }

    @Override
    public Map<Integer, List<CrowdfundingAttachment>> getMapByInfoIdListAndType(List<Integer> parentIdList, AttachmentTypeEnum type) {
        List<CrowdfundingAttachment> crowdfundingAttachments = getListByInfoIdListAndType(parentIdList, type);
        if(CollectionUtils.isEmpty(crowdfundingAttachments)) {
            return Collections.emptyMap();
        }
        return crowdfundingAttachments.stream().collect(Collectors.groupingBy(CrowdfundingAttachment::getParentId));
    }


    @Override
    public List<CrowdfundingAttachment> getAttachmentsByParentIdAndType(int parentId, AttachmentTypeEnum type) {

        FeignResponse<List<CrowdfundingAttachmentDTO>> response = crowdfundingAttachmentFeignClient.getAttachmentsByTypeToSea(parentId, type.value());

        return CrowdfundingAttachmentConverterUtil.toEntityList(Optional.ofNullable(response)
                .filter(FeignResponse::ok)
                .map(FeignResponse::getData)
                .orElse(null));
    }


    @Override
    public int deleteByIds(List<Integer> ids, int parentId) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }

        FeignResponse<Integer> deleteResponse = crowdfundingAttachmentFeignClient.deleteByIds(parentId, ids);
        return Optional.ofNullable(deleteResponse)
                .filter(FeignResponse::ok)
                .map(FeignResponse::getData)
                .orElse(0);
    }

    @Override
    public void shieldedAccessFromOss(CrowdfundingAttachment picAttachment) {


//        if (picAttachment == null || StringUtils.isEmpty(picAttachment.getUrl())) {
//            log.error("oss敏感图片处理,从oss处屏蔽图片失败，不能在crowdfunding_attachment找到删除的图片");
//            return;
//        }
//        ALIYUN_TASK_POOL.execute(new AliyunTask(picAttachment.getId(), picAttachment.getUrl()));
    }


    private  static String getPicResourceKeyName(String picUrl) {
        if (StringUtils.isEmpty(picUrl)) {
            return null;
        }
        int pos = picUrl.lastIndexOf(CrowdfundingCons.IMG_BASE_DIR);
        if (pos >= 0) {
            return  picUrl.substring(pos);
        }
        return null;
    }

    @Override
    public void updateTitleImgAfterDeleteImg(CrowdfundingAttachment attachment) {

        CrowdfundingInfo cf = adminCrowdfundingInfoBiz.getFundingInfoById(attachment.getParentId());

        if (cf == null || StringUtils.isBlank(cf.getTitleImg())) {
            log.info("筹款不存在或头图不存在，不需要更新头图. caseId:{}", attachment.getParentId());
            return;
        }

        if (cf.getTitleImg().equals(attachment.getUrl())) {
            log.info("筹款的头图正被删除. caseId:{}, titleImg:{}", attachment.getParentId(), attachment.getUrl());

            FeignResponse<List<CrowdfundingAttachmentDTO>> attachmentsByTypesResponse = crowdfundingAttachmentFeignClient.getAttachmentsByTypesToSea(attachment.getParentId(), Lists.newArrayList(AttachmentTypeEnum.ATTACH_CF.value()));
            List<CrowdfundingAttachment> attachmentList = CrowdfundingAttachmentConverterUtil.toEntityList(Optional.ofNullable(attachmentsByTypesResponse)
                    .filter(FeignResponse::ok)
                    .map(FeignResponse::getData)
                    .orElse(Lists.newArrayList()));

            attachmentList.sort(new Comparator<CrowdfundingAttachment>() {
                @Override
                public int compare(CrowdfundingAttachment o1, CrowdfundingAttachment o2) {
                    return o1.getSequence() - o2.getSequence();
                }
            });

            String newImg = (CollectionUtils.isEmpty(attachmentList) || attachmentList.size() == 0) ? "" : getMaskNewImg(attachmentList.get(0));

            adminCrowdfundingInfoBiz.updateTitleImg(newImg, attachment.getParentId());
            log.info("更新筹款头图 caseId:{}, titleImg:{}", attachment.getParentId(), newImg);
        }
    }

    @Override
    public void updateTitleImgAfterUploadImg(CrowdfundingAttachment attachment) {

        if (attachment.getType() != AttachmentTypeEnum.ATTACH_CF) {
            return;
        }

        CrowdfundingInfo cf = adminCrowdfundingInfoBiz.getFundingInfoById(attachment.getParentId());
        if (cf == null || StringUtils.isNotBlank(cf.getTitleImg())) {
            log.info("筹款不存在或头图存在，不需要更新头图. caseId:{}", attachment.getParentId());
            return;
        }

        adminCrowdfundingInfoBiz.updateTitleImg(attachment.getUrl(), attachment.getParentId());
        log.info("更新筹款头图 caseId:{}, titleImg:{}", attachment.getParentId(), attachment.getUrl());
    }

    @Override
    public CrowdfundingAttachment getAttachmentById(Integer parentId, Integer id) {

        FeignResponse<CrowdfundingAttachmentDTO> attachmentResponse = crowdfundingAttachmentFeignClient.getAttachmentById(parentId, id);

        return CrowdfundingAttachmentConverterUtil.toEntity(Optional.ofNullable(attachmentResponse)
                .filter(FeignResponse::ok)
                .map(FeignResponse::getData)
                .orElse(null));
    }

    @Override
    public Map<Integer, Integer> getImageWatermarkByAttachments(List<CrowdfundingAttachment> attachments) {

        return getImageWatermarkByIds(attachments.stream()
                .map(CrowdfundingAttachment::getId)
                .collect(Collectors.toList()));
    }

    @Override
    public Map<Integer, Integer> getImageWatermarkByIds(List<Integer> ids) {

        Map<Integer, Integer> watermarkMapping = Maps.newHashMap();
        if (CollectionUtils.isEmpty(ids)) {
            return  watermarkMapping;
        }
        Response<List<OssPictureAttrDO>> response = cfAttachmentFeignClient.getPicAttrsByIds(ids);
        if(response!=null && org.apache.commons.collections.CollectionUtils.isNotEmpty(response.getData())){
            response.getData().stream().forEach(r->watermarkMapping.put(r.getAttaId(), r.getWaterMark()));
        }

        return watermarkMapping;
    }

    @Override
    public Map<String, Integer> getImagesTWatermark(int caseId, List<AttachmentTypeEnum> enumList) {
        Map<String, Integer> result = Maps.newHashMap();
        if (caseId <= 0) {
            return result;
        }

        return getImagesTWatermark(Lists.newArrayList(caseId), enumList);
    }

    @Override
    public Map<String, Integer> getImagesTWatermark(List<Integer> caseIds, List<AttachmentTypeEnum> enumList) {
        Map<String, Integer> result = Maps.newHashMap();
        if (CollectionUtils.isEmpty(caseIds) || CollectionUtils.isEmpty(enumList)) {
            return result;
        }

        FeignResponse<List<CrowdfundingAttachmentDTO>> attachmentsByCaseIdsResponse = crowdfundingAttachmentFeignClient.getAttachmentsByCaseIdsWithDelete(
                caseIds,
                enumList.stream().map(AttachmentTypeEnum::value).collect(Collectors.toList()));

        List<CrowdfundingAttachment> allAttachments = CrowdfundingAttachmentConverterUtil.toEntityList(Optional.ofNullable(attachmentsByCaseIdsResponse)
                .filter(FeignResponse::ok)
                .map(FeignResponse::getData)
                .orElse(null));

        return getImageUrlWatermarkByAttachments(allAttachments);
    }

    @Override
    public Map<String, Integer> getImageUrlWatermarkByAttachments(List<CrowdfundingAttachment> allAttachments) {
        Map<String, Integer> result = Maps.newHashMap();

        if (CollectionUtils.isEmpty(allAttachments)) {
            return result;
        }

        Map<Integer, Integer> waterMarkMappings = getImageWatermarkByAttachments(allAttachments);

        for (CrowdfundingAttachment attachment : allAttachments) {
            Integer mark =  waterMarkMappings.get(attachment.getId());
            if (mark != null) {
                result.put(attachment.getUrl(), mark);
            }
        }

        return result;
    }

    @Override
    public boolean canNotShowInUgcManage(AttachmentTypeEnum type) {

        return CANNOT_SHOW_UGC_MANAGES.contains(type);
    }

    @Override
    @RedisDistributedLock(key = "handleImageBaseStage_#{caseId}")
    public void addImageToBaseStage(int caseId, AttachmentTypeEnum attachmentType, String addImageUrl) {

        if (attachmentType != AttachmentTypeEnum.ATTACH_CF || StringUtils.isBlank(addImageUrl)) {
            return;
        }

        Response<CaseInfoApproveStageDO> stageResult = approveStageClient.getStageInfo(caseId);
        if (stageResult == null || stageResult.getData() == null) {
            log.info("查询案例的图文暂存区异常。caseId:{} result:{}", caseId, JSON.toJSONString(stageResult));
            return;
        }
        String title = stageResult.getData().getTitle();
        String content = stageResult.getData().getContent();

        if (StringUtils.isNotBlank(stageResult.getData().getImages())) {
            addImageUrl =  stageResult.getData().getImages() + "," + addImageUrl;
        }

        Response<Void> saveResult = approveStageClient.saveStage(caseId, title, content, addImageUrl);
        log.info("上传的图片添加到暂存区.caseId:{} title:{} content:{} addImageUrl:{} result:{}",
                caseId, title, content, addImageUrl, JSON.toJSONString(saveResult));
    }

    @Override
    @RedisDistributedLock(key = "handleImageBaseStage_#{caseId}")
    public void deleteImageFromBaseStage(int caseId, CrowdfundingAttachment attachment) {

        if (attachment == null || attachment.getType() != AttachmentTypeEnum.ATTACH_CF
                || StringUtils.isBlank(attachment.getUrl())) {
            log.info("不用对图片做处理caseId:{}", caseId);
            return;
        }

        Response<CaseInfoApproveStageDO> stageResult = approveStageClient.getStageInfo(caseId);
        if (stageResult == null || stageResult.getData() == null) {
            log.info("不能查询到案例的图文暂存区。caseId:{} result:{}", caseId, JSON.toJSONString(stageResult));
            return;
        }
        if (StringUtils.isBlank(stageResult.getData().getImages())
                || !stageResult.getData().getImages().contains(attachment.getUrl())) {
            log.info("案例的图文暂存图片中没有当前图片。caseId:{}", caseId);
            return;
        }

        String title = stageResult.getData().getTitle();
        String content = stageResult.getData().getContent();

        List<String> imageList =  Lists.newArrayList(stageResult.getData().getImages().split(","));
        imageList.remove(attachment.getUrl());
        String imageUrls = StringUtils.join(imageList, ',');

        Response<Void> saveResult = approveStageClient.saveStage(caseId, title, content, imageUrls);
        log.info("删除的图文从暂存区中删除caseId:{} title:{} content:{} addImageUrl:{} result:{}",
                caseId, title, content, imageUrls, JSON.toJSONString(saveResult));
    }


    @Override
    public List<CrowdfundingAttachment> getAttachmentsByTypes(int parentId, List<Integer> types) {
        FeignResponse<List<CrowdfundingAttachmentDTO>> attachmentsByTypesResponse = crowdfundingAttachmentFeignClient.getAttachmentsByTypesToSea(parentId, types);
        return CrowdfundingAttachmentConverterUtil.toEntityList(Optional.ofNullable(attachmentsByTypesResponse)
                .filter(FeignResponse::ok)
                .map(FeignResponse::getData)
                .orElse(null));
    }

    @Override
    public List<CrowdfundingAttachment> getAttachmentsByCaseIdsWithDelete(List<Integer> parentId, List<Integer> type) {

        FeignResponse<List<CrowdfundingAttachmentDTO>> attachmentsByCaseIdsResponse = crowdfundingAttachmentFeignClient.getAttachmentsByCaseIdsWithDelete(parentId, type);
        return CrowdfundingAttachmentConverterUtil.toEntityList(Optional.ofNullable(attachmentsByCaseIdsResponse)
                .filter(FeignResponse::ok)
                .map(FeignResponse::getData)
                .orElse(null));
    }

    @Override
    public List<CrowdfundingAttachment> getAttachmentsByIdList(List<Integer> idList, int parentId) {

        FeignResponse<List<CrowdfundingAttachmentDTO>> attachmentsByIdListResponse = crowdfundingAttachmentFeignClient.getAttachmentsByIdListToSea(parentId, idList);

        return CrowdfundingAttachmentConverterUtil.toEntityList(Optional.ofNullable(attachmentsByIdListResponse)
                .filter(FeignResponse::ok)
                .map(FeignResponse::getData)
                .orElse(null));
    }

    private String getMaskNewImg(CrowdfundingAttachment crowdfundingAttachment) {

        // 查询掩码图片
        Response<List<MaskAttachmentVo>> response = cfImageMaskFeignClient.queryMaskImage(Lists.newArrayList((long) crowdfundingAttachment.getId()), Lists.newArrayList(ImageMaskBizEnum.CF_DETAIL_IMAGE.getCode()));
        List<MaskAttachmentVo> attachmentVos = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(Lists.newArrayList());
        if (CollectionUtils.isEmpty(attachmentVos)) {
            return crowdfundingAttachment.getUrl();
        }

        // 如果有掩码图片，且被掩码，则用掩码图片做头图
        MaskAttachmentVo maskAttachmentVo = attachmentVos.get(0);
        if (StringUtils.isNotBlank(maskAttachmentVo.getAiImageUrl())
                && Objects.nonNull(maskAttachmentVo.getImageHandleStatus())
                && maskAttachmentVo.getImageHandleStatus() == 2) {
            return maskAttachmentVo.getAiImageUrl();
        }

        return crowdfundingAttachment.getUrl();
    }

    private String getSharding(int caseId) {
        return String.format("%03d", Math.abs(caseId) % 100);
    }
}
