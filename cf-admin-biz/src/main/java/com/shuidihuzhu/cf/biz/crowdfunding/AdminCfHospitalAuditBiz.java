package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.admin.CfHospitalAuditInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CfHospitalAuditInfo;

import java.util.List;

import com.shuidihuzhu.cf.model.crowdfunding.CfHospitalAuditInfoTel;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * Created by niejiangnan on 2017/11/30.
 */
public interface AdminCfHospitalAuditBiz {

    int save(CfHospitalAuditInfo cfHospitalAuditInfo);

    CfHospitalAuditInfoExt getByInfoUuid(String infoUuid);

    CfHospitalAuditInfoExt getById(long id);

    List<CfHospitalAuditInfoExt> getByInfoUuids(List<String> infoUuids);

    int update(CfHospitalAuditInfo cfHospitalAuditInfo);

    int delete(CfHospitalAuditInfo cfHospitalAuditInfo);

    List<String> getNotFinish();

    int countHospitalAuditByStatus(int auditStatus);

    List<CfHospitalAuditInfoTel> getByCfHospitalAuditInfoId(long cfHospitalAuditInfoId);

    @Data
    @AllArgsConstructor
    class HospitalAuditData {
        int noAuditNum;
    }
}
