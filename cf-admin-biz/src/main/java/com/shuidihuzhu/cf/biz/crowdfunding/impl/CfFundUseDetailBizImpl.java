package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.CfFundUseDetailBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfFundUseDetailDao;
import com.shuidihuzhu.cf.model.admin.workorder.CfFundUseDetailDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2019-12-19 17:57
 **/
@Service
@Slf4j
public class CfFundUseDetailBizImpl implements CfFundUseDetailBiz {

    @Autowired
    private CfFundUseDetailDao detailDao;


    @Override
    public boolean batchInsert(List<CfFundUseDetailDO> detailDOS) {
        try {
            detailDao.batchInsert(detailDOS);
        } catch (DuplicateKeyException e) {
            log.error("重复插入detailDOS:{}", JSON.toJSONString(detailDOS), e);
        }
        return true;
    }

    @Override
    public Map<Integer, List<CfFundUseDetailDO>> listByProgressIds(List<Integer> progressIds) {
        if (CollectionUtils.isEmpty(progressIds)) {
            return Maps.newHashMap();
        }
        Map<Integer, List<CfFundUseDetailDO>> progressId2Details = detailDao.listByProgressIds(progressIds).stream().collect(Collectors.groupingBy(CfFundUseDetailDO::getFundUseProgressId));
        return progressId2Details;
    }

    @Override
    public List<CfFundUseDetailDO> listAuditDetailByCaseId(int caseId) {
        return detailDao.listByCaseId(caseId);
    }



    @Override
    public boolean auditFundUseDetail(CfFundUseDetailDO cfFundUseDetailDO) {
        if (cfFundUseDetailDO == null || cfFundUseDetailDO.getId() <= 0) {
            return false;
        }
        return detailDao.auditDetail(cfFundUseDetailDO) > 0;
    }
}
