package com.shuidihuzhu.cf.biz.message;

import com.shuidihuzhu.common.web.enums.message.MulitMpStrategy;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Author: <PERSON>
 * Date: 2017/9/22 14:56
 */
@Service
public class AdminMulitMpSelectStrategyManager implements InitializingBean {

	private AdminUserSetMpSelectStrategyAdmin adminUserSetMpSelectStrategy;
	@Autowired
	AdminLastFollowAdminMulitMpSelectStrategy adminLastFollowMulitMpSelectStrategy;

	public AdminMulitMpSelectStrategy get(MulitMpStrategy mulitMpStrategy) {
		switch (mulitMpStrategy) {
			case USER_SET:
				return adminUserSetMpSelectStrategy;
			case LAST_FOLLOW:
				return adminLastFollowMulitMpSelectStrategy;
		}
		return null;
	}


	@Override
	public void afterPropertiesSet() throws Exception {
		adminUserSetMpSelectStrategy = new AdminUserSetMpSelectStrategyAdmin();
	}
}
