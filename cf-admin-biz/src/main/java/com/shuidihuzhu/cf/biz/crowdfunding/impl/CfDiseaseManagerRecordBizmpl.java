package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.CfDiseaseManagerRecordBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfDiseaseManagerRecordDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfDiseaseManagerRecordDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2019-11-08 16:29
 **/
@Service
@Slf4j
public class CfDiseaseManagerRecordBizmpl implements CfDiseaseManagerRecordBiz {

    @Autowired
    CfDiseaseManagerRecordDao cfDiseaseManagerRecordDao;

    @Override
    public boolean add(CfDiseaseManagerRecordDO recordDO) {
        return cfDiseaseManagerRecordDao.insert(recordDO) == 1;
    }

    @Override
    public List<CfDiseaseManagerRecordDO> listByManagerId(long managerId) {
        return cfDiseaseManagerRecordDao.listByManagerId(managerId);
    }
}
