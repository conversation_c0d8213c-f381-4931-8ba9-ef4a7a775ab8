package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.contribute.CfContributeOrder;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord;
import com.shuidihuzhu.cf.param.CaseOrderQueryParam;
import com.shuidihuzhu.cf.vo.crowdfunding.CfOrderVo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by lgj on 16/6/21.
 */
public interface AdminCrowdfundingOrderBiz {

    List<CrowdfundingOrder> getSuccessByPage(Long id,
                                             int crowdfundingId,
                                             Integer amount,
                                             String comment,
                                             String ctimeStart,
                                             String ctimeEnd,
                                             int pageNum,
                                             int pageSize,
                                             String mobile,
                                             int amountStart,
                                             int amountEnd);

    List<CfOrderVo> getCrowdfundingCommentVo(List<CrowdfundingOrder> crowdfundingOrder,
                                             List<CfContributeOrder> contributeOrders,
                                             Map<Long, CrowdfundingPayRecord> recordMappings);

    int updateValid(int valid, long id);

    CrowdfundingOrder getById(Long id);

    int editByIdAndComment(String comment, Long id);

    CrowdfundingOrder selectExtreme(int caseId, int orderHandle);

    List<CrowdfundingOrder> selectOrderOneByInfoId(Set<Integer> caseIds);

    List<CrowdfundingOrder> getListByInfoId(int crowdfundingId, Integer offset, Integer limit);

    List<CrowdfundingOrder> getValidPayedSuccessListByIds(List<Long> crowdfundingIds);

    List<CrowdfundingOrder> getByPage(Integer crowdfundingId, int offset, int limit);

    CrowdfundingOrder getByIdFeign(Long id);

    List<CrowdfundingOrder> selectByUserIdAndThirdTypeTidbFeign(Long userId, Integer caseId, Integer thirdType,
                                                                Integer current, Integer pageSize);

    List<CrowdfundingOrder> selectByCaseIdAndTimeTidbFeign(int caseId, String begin, String end,
                                                           Integer current, Integer pageSize);

    CrowdfundingOrder selectExtremeFeign(int caseId, int orderHandle);

    List<CrowdfundingOrder> selectOrderOneByInfoIdFeign(Set<Integer> caseIds);

    List<CrowdfundingOrder> getListByInfoIdFeign(int crowdfundingId, Integer offset, Integer limit);

    List<CrowdfundingOrder> getByPageFeign(Integer crowdfundingId, int offset, int limit);

    List<CrowdfundingOrder> getSuccessByPageFeign(CaseOrderQueryParam queryParam);

    List<CrowdfundingOrder> getValidPayedSuccessListByIdsFeign(List<Long> ids);

    List<CrowdfundingOrder> getByIds(List<Long> list, Integer valid, Integer payStatus);
}
