package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.biz.crowdfunding.impl.AdminUserRealServiceImpl;
import com.shuidihuzhu.cf.model.crowdfunding.UserRealInfo;

import java.util.List;

public interface AdminUserRealService {

    int unbind(long realUserId, int userId, int id, String comment, String pic);

    List<AdminUserRealServiceImpl.UserRealInfoView> getOnceAllSuccess(long userId);

    List<AdminUserRealServiceImpl.UserRealInfoView> getByUserIdAndIdCard(String mobile, String idCard);
}
