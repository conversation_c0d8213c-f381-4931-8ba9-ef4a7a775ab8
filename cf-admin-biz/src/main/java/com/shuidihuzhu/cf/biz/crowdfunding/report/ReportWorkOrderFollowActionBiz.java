package com.shuidihuzhu.cf.biz.crowdfunding.report;

import com.shuidihuzhu.cf.model.report.ReportWorkOrderCount;
import com.shuidihuzhu.cf.model.report.ReportWorkOrderFollowAction;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface ReportWorkOrderFollowActionBiz {

    int insertOne(ReportWorkOrderFollowAction reportWorkOrderFollowAction);

    List<ReportWorkOrderCount> getCountByOperatorId(List<Long> operatorIds, Date dayOfZero, List<Integer> orderTypes);

    ReportWorkOrderCount getCount(long operatorId, Date dayOfZero, int orderType);

    List<ReportWorkOrderFollowAction> getByWorkOrderId(long workOrderId, List<Integer> actionTypes);

    List<Long> getWorkOrderId(long operatorId, Date dayOfZero, List<Integer> orderTypes);

}
