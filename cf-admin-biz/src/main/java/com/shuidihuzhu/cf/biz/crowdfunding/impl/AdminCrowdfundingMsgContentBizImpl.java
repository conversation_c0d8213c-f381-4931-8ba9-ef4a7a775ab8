package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingMsgContentBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingMsgContentDao;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingMsgContent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by Ahrievil on 2017/10/12
 */
@Service
public class AdminCrowdfundingMsgContentBizImpl implements AdminCrowdfundingMsgContentBiz {

    @Autowired
    private AdminCrowdfundingMsgContentDao adminCrowdfundingMsgContentDao;
    @Override
    public int insertOne(CrowdfundingMsgContent crowdfundingMsgContent) {
        return adminCrowdfundingMsgContentDao.insertOne(crowdfundingMsgContent);
    }

    @Override
    public int update(CrowdfundingMsgContent crowdfundingMsgContent) {
        return adminCrowdfundingMsgContentDao.update(crowdfundingMsgContent);
    }

    @Override
    public CrowdfundingMsgContent selectById(int id) {
        return adminCrowdfundingMsgContentDao.selectById(id);
    }

    @Override
    public int deleteOne(int id) {
        return adminCrowdfundingMsgContentDao.deleteOne(id);
    }
}
