package com.shuidihuzhu.cf.biz.crowdfunding;


import com.shuidihuzhu.cf.model.crowdfunding.CfReportAddTrust;
import com.shuidihuzhu.cf.model.report.AdminCfReportAddTrustVo;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON>ejiangnan on 2017/10/23.
 */
public interface AdminCfReportAddTrustBiz {

    Map<String,Integer> getAuditStausMapByInfoUuids(List<String> infoUuids);

    long save(String infoUuid, int auditStatus, String operatorContent, String content, String imageUrls, boolean issuedCommitment);

    int update(CfReportAddTrust cfReportAddTrust);

    CfReportAddTrust getByInfoUuid(String infoUuid);

    AdminCfReportAddTrustVo queryById(long id);

    int delete(String infoUuid);
}
