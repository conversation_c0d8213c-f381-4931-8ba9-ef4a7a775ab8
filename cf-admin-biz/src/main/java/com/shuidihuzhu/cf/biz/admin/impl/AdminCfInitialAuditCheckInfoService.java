package com.shuidihuzhu.cf.biz.admin.impl;

import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfInitialAuditCheckInfoDAO;
import com.shuidihuzhu.cf.domain.cf.AdminCfInitialAuditCheckInfoDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AdminCfInitialAuditCheckInfoService {

    @Resource
    private AdminCfInitialAuditCheckInfoDAO checkInfoDAO;

    public void add(Integer caseId, Integer checkType, String checkName, Integer checkResult) {
        AdminCfInitialAuditCheckInfoDO checkInfoDO = new AdminCfInitialAuditCheckInfoDO();
        checkInfoDO.setCaseId(caseId);
        checkInfoDO.setCheckType(checkType);
        checkInfoDO.setCheckName(checkName);
        checkInfoDO.setCheckResult(checkResult);
        try {
            checkInfoDAO.add(checkInfoDO);
        } catch (Exception e) {
            log.error("", e);
        }
    }

    public List<AdminCfInitialAuditCheckInfoDO> listByCaseIdOrderByCheckType(Integer caseId) {
        try {
            List<AdminCfInitialAuditCheckInfoDO> infoDOList = checkInfoDAO.listByCaseIdAndCheckType(caseId);
            if (CollectionUtils.isEmpty(infoDOList)) {
                return Collections.emptyList();
            }
            return infoDOList.stream()
                    .sorted(Comparator.comparing(AdminCfInitialAuditCheckInfoDO::getCheckType))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("", e);
        }
        return Collections.emptyList();
    }

    public void update(Integer id, String checkName, Integer checkResult) {
        AdminCfInitialAuditCheckInfoDO checkInfoDO = new AdminCfInitialAuditCheckInfoDO();
        checkInfoDO.setId(id);
        checkInfoDO.setCheckName(checkName);
        checkInfoDO.setCheckResult(checkResult);
        try {
            checkInfoDAO.update(checkInfoDO);
        } catch (Exception e) {
            log.error("", e);
        }
    }

    public AdminCfInitialAuditCheckInfoDO getByCaseIdAndCheckType(Integer caseId, Integer checkType) {
        AdminCfInitialAuditCheckInfoDO checkInfoDO = null;
        try {
            checkInfoDO = checkInfoDAO.getByCaseIdAndCheckType(caseId, checkType);
        } catch (Exception e) {
            log.error("", e);
        }
        return checkInfoDO;
    }

}
