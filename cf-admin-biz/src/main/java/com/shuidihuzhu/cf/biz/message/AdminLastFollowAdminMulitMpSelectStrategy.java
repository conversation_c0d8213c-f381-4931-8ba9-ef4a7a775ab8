package com.shuidihuzhu.cf.biz.message;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.delegate.service.WxSubscribeEventServiceBiz;
import com.shuidihuzhu.common.web.model.message.HasMulitMpUserInfo;
import com.shuidihuzhu.wx.grpc.client.WxSubscribeModel;
import com.shuidihuzhu.wx.model.WxMpConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Author: <PERSON>
 * Date: 2017/9/22 16:03
 */
@Slf4j
@Service
public class AdminLastFollowAdminMulitMpSelectStrategy implements AdminMulitMpSelectStrategy {

    @Resource
    private WxSubscribeEventServiceBiz wxSubscribeEventServiceBiz;

    @Override
    public boolean selectThenFill(HasMulitMpUserInfo hasMulitMpUserInfo, List<WxMpConfig> wxAppInfos) {
        if (CollectionUtils.isEmpty(wxAppInfos) || hasMulitMpUserInfo == null || hasMulitMpUserInfo.getUserId() <= 0 ||
                hasMulitMpUserInfo.getUserId() < 1) {
            return false;
        }
        List<Integer> thirdTypes = wxAppInfos.stream().map(wxMpGroup -> wxMpGroup.getThirdType()).collect(Collectors.toList());
        // 取用户最后一个关注公众号
        List<WxSubscribeModel> subscribeModels = this.wxSubscribeEventServiceBiz.batchCheckSubscribeByUserId(hasMulitMpUserInfo.getUserId(), thirdTypes);
        Optional<WxSubscribeModel> subscribeModel = subscribeModels.stream()
                .filter(subModel -> subModel.getIsSubscribe() == 1)
                .sorted(Comparator.comparing(WxSubscribeModel::getSubscribeTime).reversed())
                .findFirst();

        for (WxMpConfig wxAppInfo : wxAppInfos) {
            if (wxAppInfo.getThirdType() == subscribeModel.get().getThirdType()) {
                hasMulitMpUserInfo.setThirdType(wxAppInfo.getThirdType());
                hasMulitMpUserInfo.setOpenId(subscribeModel.get().getOpenId());
                return true;
            }
        }
        return false;
    }

    @Override
    public List<Boolean> selectThenFill(List<HasMulitMpUserInfo> hasMulitMpUserInfos, List<WxMpConfig> wxAppInfos) {
        if (CollectionUtils.isEmpty(wxAppInfos) || CollectionUtils.isEmpty(hasMulitMpUserInfos)) {
            return Collections.EMPTY_LIST;
        }
        Map<Integer, WxMpConfig> wxMpConfigMap = wxAppInfos.stream()
                .collect(Collectors.toMap(WxMpConfig::getThirdType, Function.identity()));
        List<Boolean> result = Lists.newArrayList();
        List<List<HasMulitMpUserInfo>> batchs = Lists.partition(hasMulitMpUserInfos, 3000);
        for (List<HasMulitMpUserInfo> batch : batchs) {
            List<Long> userIds = batch.stream()
                    .filter(hasMulitMpUserInfo -> hasMulitMpUserInfo.getUserId() > 0)
                    .map(hasMulitMpUserInfo -> hasMulitMpUserInfo.getUserId())
                    .sorted()
                    .distinct()
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(userIds)) {
                List<WxSubscribeModel> subscribeModels = Lists.newArrayList();
                for (int thirdType :wxMpConfigMap.keySet()) {
                    List<WxSubscribeModel> list = wxSubscribeEventServiceBiz.batchCheckSubscribeByUserIds(userIds, thirdType);
                    subscribeModels.addAll(list);
                }
                // 获取每个用户关注的公众号列表，并以关注事件倒序
                Map<Long, List<WxSubscribeModel>> userWxSubscribeModelMap = subscribeModels.stream()
                        .filter(wxSubscribeModel -> wxSubscribeModel.getIsSubscribe() == 1)
                        .collect(Collectors.groupingBy(
                                wxSubscribeModel -> wxSubscribeModel.getUserId() <= 0 ? null : wxSubscribeModel.getUserId(),
                                Collectors.collectingAndThen(Collectors.toList(),
                                        subscribeModels1 -> {
                                            List<WxSubscribeModel> sortedWxSubscribeModels = subscribeModels1
                                                    .stream()
                                                    .sorted(Comparator.comparing(WxSubscribeModel::getSubscribeTime).reversed()
                                                    ).collect(Collectors.toList());
                                            return sortedWxSubscribeModels;
                                        })
                                )
                        );

                for (HasMulitMpUserInfo hasMulitMpUserInfo : batch) {
                    boolean isFound = false;
                    List<WxSubscribeModel> subscribeModelList = userWxSubscribeModelMap.get(hasMulitMpUserInfo.getUserId());
                    if (!CollectionUtils.isEmpty(subscribeModelList)) {
                        Optional<WxSubscribeModel> wxMpEvent = subscribeModelList
                                .stream()
                                .filter(w -> wxMpConfigMap
                                        .containsKey(w.getThirdType()))
                                .findFirst();

                        if (wxMpEvent.isPresent()) {
                            hasMulitMpUserInfo.setThirdType(wxMpEvent.get().getThirdType());
                            hasMulitMpUserInfo.setOpenId(wxMpEvent.get().getOpenId());
                            isFound = true;
                        }
                    }
                    result.add(isFound);
                }
            }else {
                result.addAll(batch.stream()
                        .map(hasMulitMpUserInfo -> false)
                        .collect(Collectors.toList()));
            }
        }

        return result;
    }

}
