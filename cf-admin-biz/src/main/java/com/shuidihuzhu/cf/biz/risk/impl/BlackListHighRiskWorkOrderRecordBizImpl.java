package com.shuidihuzhu.cf.biz.risk.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoSearchBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfFirstApproveOperatorBiz;
import com.shuidihuzhu.cf.biz.risk.BlackListHighRiskWorkOrderRecordBiz;
import com.shuidihuzhu.cf.client.adminpure.enums.WorkOrderExtContentTypeEnum;
import com.shuidihuzhu.cf.client.material.feign.CfFirstApproveClient;
import com.shuidihuzhu.cf.dao.risk.BlackListHighRiskWorkOrderRecordDao;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CaseSearchVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoVo;
import com.shuidihuzhu.cf.model.risk.BlackListHighRiskWorkOrderRecord;
import com.shuidihuzhu.cf.model.risk.BlackListHighRiskWorkOrderRecordDto;
import com.shuidihuzhu.cf.risk.client.admin.blacklist.BlacklistClient;
import com.shuidihuzhu.cf.risk.model.admin.blacklist.BlacklistDataDto;
import com.shuidihuzhu.cf.service.workorder.WorkOrderExtService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditSearchService;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditCaseDetail;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.common.web.model.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: wangpeng
 * @Date: 2022/4/21 17:13
 * @Description:
 */
@Service
public class BlackListHighRiskWorkOrderRecordBizImpl implements BlackListHighRiskWorkOrderRecordBiz {

    @Resource
    private BlackListHighRiskWorkOrderRecordDao blackListHighRiskWorkOrderRecordDao;

    @Resource
    private AdminCfInfoSearchBiz adminCfInfoSearchBiz;

    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Resource
    private BlacklistClient blacklistClient;

    @Resource
    private ShuidiCipher shuidiCipher;


    @Override
    public int insert(BlackListHighRiskWorkOrderRecord riskWorkOrderRecord) {
        return blackListHighRiskWorkOrderRecordDao.insert(riskWorkOrderRecord);
    }

    @Override
    public BlackListHighRiskWorkOrderRecord getByWorkOrderId(long workOrderId) {
        return blackListHighRiskWorkOrderRecordDao.getByWorkOrderId(workOrderId);
    }

    @Override
    public BlackListHighRiskWorkOrderRecordDto initialHighRiskInfo(long workOrderId) {
        BlackListHighRiskWorkOrderRecordDto dto = new BlackListHighRiskWorkOrderRecordDto();
        BlackListHighRiskWorkOrderRecord workOrderRecord = blackListHighRiskWorkOrderRecordDao.getByWorkOrderId(workOrderId);
        if (Objects.isNull(workOrderRecord)) {
            return dto;
        }
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(workOrderRecord.getCaseId());
        if (Objects.isNull(crowdfundingInfo)) {
            return dto;
        }

        Response<BlacklistDataDto> blacklistDataDtoResponse = blacklistClient.queryByBlacklistId(workOrderRecord.getBlackListId());
        BlacklistDataDto blacklistDataDto = Optional.ofNullable(blacklistDataDtoResponse)
                .map(Response::getData)
                .orElse(null);
        if (Objects.isNull(blacklistDataDto)) {
            return dto;
        }

        Response<String> getBlackListLogByDataId = blacklistClient.getAddLogByDataId(workOrderRecord.getBlackListId());
        String reason = Optional.ofNullable(getBlackListLogByDataId)
                .map(Response::getData)
                .orElse("");

        CaseSearchVo caseSearchVo = new CaseSearchVo();
        if (workOrderRecord.getHitType() == 1) {
            caseSearchVo.setPatientBornCard(workOrderRecord.getHitIdCard());
        }
        if (workOrderRecord.getHitType() == 2) {
            String decrypt = shuidiCipher.decrypt(workOrderRecord.getHitIdCard());
            caseSearchVo.setPatientIdCard(decrypt);
        }
        if (workOrderRecord.getHitType() == 3) {
            String decrypt = shuidiCipher.decrypt(workOrderRecord.getHitIdCard());
            caseSearchVo.setSelfIdCard(decrypt);
        }
        Pair<Long, List<CrowdfundingInfoVo>> caseSearchByEs = adminCfInfoSearchBiz.caseSearchByEs(caseSearchVo);
        if (Objects.nonNull(caseSearchByEs) && CollectionUtils.isNotEmpty(caseSearchByEs.getRight())) {
            // 第一个案例是当前案例
            int caseIdNew = caseSearchByEs.getRight()
                    .stream()
                    .map(CrowdfundingInfoVo::getId)
                    .filter(f -> !Objects.equals(f, workOrderRecord.getCaseId()))
                    .findFirst()
                    .orElse(0);
            dto.setCaseId(caseIdNew);
        }
        dto.setReason(reason);
        dto.setHitType(workOrderRecord.getHitType());
        return dto;
    }
}
