package com.shuidihuzhu.cf.biz.crowdfunding.report;

import com.shuidihuzhu.cf.model.report.CfReportClassifyRelationship;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/4/17
 */
public interface AdminCfReportClassifyRelationshipBiz {
    int add(List<CfReportClassifyRelationship> cfReportClassifyRelationship);


    int updateRelationship(long actionClassifyId, long disposeActionId, long labelId, int type);


    List<CfReportClassifyRelationship> getByLabelId(long labelId);

    int deleteRelationshipByLabelId(long labelId);


    List<CfReportClassifyRelationship> getByTypeAndLabelIds(int type, List<Integer> labels);


}
