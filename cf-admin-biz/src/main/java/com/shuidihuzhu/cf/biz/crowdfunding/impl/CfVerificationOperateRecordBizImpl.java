package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.crowdfunding.CfVerificationOperateRecordBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfVerificationOperateRecordDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfVerificationOperateRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CfVerificationOperateRecordVo;
import com.shuidihuzhu.common.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CfVerificationOperateRecordBizImpl implements CfVerificationOperateRecordBiz {

    @Autowired
    private CfVerificationOperateRecordDao cfVerificationOperateRecordDao;

    @Resource
    private SeaAccountClientV1 seaAccountClientV1;


    @Override
    public int addCfVerificationOperateRecord(int verificationId, String content, int operatorId) {
        if (verificationId <= 0 || StringUtils.isEmpty(content) || operatorId <= 0) {
            return 0;
        }

        return cfVerificationOperateRecordDao.addCfVerificationOperateRecord(verificationId, content, operatorId);
    }

    @Override
    public List<CfVerificationOperateRecordVo> getCfVerificationOperateRecord(int verificationId) {
        if (verificationId <= 0) {
            return Lists.newArrayList();
        }
        List<CfVerificationOperateRecord> cfVerificationOperateRecords = cfVerificationOperateRecordDao.getCfVerificationOperateRecord(verificationId);

        if (CollectionUtils.isEmpty(cfVerificationOperateRecords)) {
            return Lists.newArrayList();
        }
        List<CfVerificationOperateRecordVo> cfVerificationOperateRecordVoList = Lists.newArrayList();
        for (CfVerificationOperateRecord cfVerificationOperateRecord : cfVerificationOperateRecords) {
            CfVerificationOperateRecordVo cfVerificationOperateRecordVo = new CfVerificationOperateRecordVo();
            cfVerificationOperateRecordVo.setContent(cfVerificationOperateRecord.getContent());
            cfVerificationOperateRecordVo.setCreateTime(DateUtil.getDate2MStr(cfVerificationOperateRecord.getCreateTime()));
            int operatorId = cfVerificationOperateRecord.getOperatorId();
            AuthRpcResponse<String> rpcResponse = seaAccountClientV1.getNameByUserId(operatorId);
            String name = StringUtils.EMPTY;
            if (rpcResponse != null && StringUtils.isNotEmpty(rpcResponse.getResult())) {
                name = rpcResponse.getResult();
            }
            cfVerificationOperateRecordVo.setOperatorName(name);
            cfVerificationOperateRecordVoList.add(cfVerificationOperateRecordVo);
        }
        return cfVerificationOperateRecordVoList;
    }
}
