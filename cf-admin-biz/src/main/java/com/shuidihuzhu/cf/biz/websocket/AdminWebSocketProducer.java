package com.shuidihuzhu.cf.biz.websocket;

import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Slf4j
@Service
public class AdminWebSocketProducer {

    @Autowired
    private Producer producer;

    public void sendWebSocketMessage(int userId, String message) {

        if (AdminWebSocketHandler.sessionHoldMap.contains(userId)) {
            AdminWebSocketHandler.sendMessage(userId, message);
        } else {
            WebSocketMsg socketMsg = new WebSocketMsg(userId, message);
            socketMsg.setSendTime(new Date());
            MessageResult result = producer.send(new Message(MQTopicCons.CF, MQTagCons.ADMIN_WEB_SOCKET_MSG_SEND, "" + System.nanoTime(), socketMsg));
            log.info("广播消息发送 userId:{}, message:{}, mq send result:{}", userId, message, result);
        }
    }

    @Data
    public static class WebSocketMsg {

        private int userId;

        private String message;


        private SocketMsgType msgType;

        private Date sendTime;

        private boolean mustSend;

        public WebSocketMsg(int userId, String message) {
            this.userId = userId;
            this.message = message;
        }
    }

    enum SocketMsgType {

    }
}
