package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingVolunteerHospitalBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingVolunteerHospitalDao;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingVolunteerHospital;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/2/28.
 */
@Service
public class CrowdfundingVolunerrHospitalBizImpl implements CrowdfundingVolunteerHospitalBiz {
    @Autowired
    private CrowdfundingVolunteerHospitalDao crowdfundingVolunteerHospitalDao;

    @Override
    public int insertHospital(CrowdfundingVolunteerHospital crowdfundingVolunteerHospital) {
        return crowdfundingVolunteerHospitalDao.insertHospital(crowdfundingVolunteerHospital);
    }

    @Override
    public List<CrowdfundingVolunteerHospital> getHospitalName(int cityId) {
        return crowdfundingVolunteerHospitalDao.getHospitalName(cityId);
    }
}
