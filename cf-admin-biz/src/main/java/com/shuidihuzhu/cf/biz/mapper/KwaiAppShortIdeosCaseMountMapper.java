package com.shuidihuzhu.cf.biz.mapper;

import com.shuidihuzhu.cf.model.kwai.KwaiAppShortIdeosCaseMount;
import com.shuidihuzhu.cf.model.kwai.KwaiAppShortIdeosCaseMountDo;
import com.shuidihuzhu.cf.model.kwai.KwaiAppShortIdeosCaseMountDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/28  16:47
 */
@Mapper(componentModel = "spring")
public interface KwaiAppShortIdeosCaseMountMapper {

    KwaiAppShortIdeosCaseMountDo toDo(KwaiAppShortIdeosCaseMount caseMount);

    KwaiAppShortIdeosCaseMount toEntity(KwaiAppShortIdeosCaseMountDto dto);

    List<KwaiAppShortIdeosCaseMount> mapEntity(List<KwaiAppShortIdeosCaseMountDto> dtoList);

    List<KwaiAppShortIdeosCaseMountDto> mapDto(List<KwaiAppShortIdeosCaseMount> list);

    List<KwaiAppShortIdeosCaseMountDo> mapDo(List<KwaiAppShortIdeosCaseMount> caseMountList);

    List<KwaiAppShortIdeosCaseMount> fromData(List<KwaiAppShortIdeosCaseMountDo> doList);
}
