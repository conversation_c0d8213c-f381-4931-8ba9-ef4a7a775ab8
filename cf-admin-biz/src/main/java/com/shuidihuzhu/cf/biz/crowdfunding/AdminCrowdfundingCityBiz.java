package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity;

import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2021/1/20 11:32
 * @Description: 获取省市信息
 */
public interface AdminCrowdfundingCityBiz {

    /**
     * 获取全部省份 (have jvm cache)
     *
     * @return
     */
    List<CrowdfundingCity> getProvince();

    /**
     * 根据父级Id获取省份或者市 (have jvm cache)
     *
     * @param parentId
     * @return
     */
    List<CrowdfundingCity> getChildren(int parentId);

    /**
     * 根据 name 模糊查询level为2等级城市
     * @param name
     * @return
     */
    List<CrowdfundingCity> getCountiesByName(String name);

    /**
     * 根据 name 和 level 模糊查询
     */
    List<CrowdfundingCity> getCitiesByNameAndLevel(String cityNameLike, List<Integer> level);

    /**
     * 根据 name 精确查询
     * @param name
     * @return
     */
    CrowdfundingCity getCityByName(String name);

    /**
     * 根据Id查省市信息
     *
     * @param id
     * @return
     */
    CrowdfundingCity getById(Integer id);

    /**
     * 根据code列表查询信息
     * @param codes
     * @return
     */
    List<CrowdfundingCity> getListByCode(List<Integer> codes);

    /**
     * 根据父级Id获取省份或者市 (have jvm cache)
     *
     * @param realParentId
     * @return
     */
    List<CrowdfundingCity> getByRealParentId(int realParentId);

    List<CrowdfundingCity> getByIds(List<Integer> ids);

}
