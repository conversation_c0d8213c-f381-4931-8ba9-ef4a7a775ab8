package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoStatusBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminTwModifyService;
import com.shuidihuzhu.cf.client.base.utils.ResultUtils;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingInfoDao;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminTwModifyDao;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cf.enums.admin.TwModifyChannelEnum;
import com.shuidihuzhu.cf.enums.approve.ApproveLifeCircleNodeStatusEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoDataStatusTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoStatusEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.model.admin.TwModifyRecordDO;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CfSensitiveWordRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoStatus;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.approve.lifecircle.CaseApproveLifeCircleService;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.cf.vo.approve.ApproveLifeCircleVO;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import com.shuidihuzhu.client.cf.admin.model.AdminEntranceStatus;
import com.shuidihuzhu.client.cf.admin.model.UserCaseStatusVO;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 入口是否展示
 * @Author: panghairui
 * @Date: 2022/3/9 3:21 下午
 */
@Slf4j
@Service
@RefreshScope
public class AdminTwModifyServiceImpl implements AdminTwModifyService {

    private static final int SPLIT_NUM = 30;
    private static final int UNDO = 1;


    private static final String WX_MESSAGE_TEMPLATE = "FFF4625";

    @Value("${apollo.admin.tuwen.display:true}")
    private boolean twDisplay;

    @Value("${apollo.admin.tw.modify.threshold:2}")
    private Integer selfModifyThreshold;

    @Resource
    private AdminTwModifyDao adminTwModifyDao;

    @Resource
    private AdminCfInfoExtBiz adminCfInfoExtBiz;

    @Resource
    private CfWorkOrderClient cfWorkOrderClient;

    @Resource
    private MsgClientV2Service msgClientV2Service;

    @Resource
    private AdminCrowdfundingInfoStatusBiz adminCrowdfundingInfoStatusBiz;

    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Resource
    private CaseApproveLifeCircleService caseApproveLifeCircleService;

    private static final List<CrowdfundingInfoDataStatusTypeEnum> statusEnums =
            Lists.newArrayList(CrowdfundingInfoDataStatusTypeEnum.BASE_INFO_SUBMIT, CrowdfundingInfoDataStatusTypeEnum.PATIENT_INFO_SUBMIT,
                    CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT, CrowdfundingInfoDataStatusTypeEnum.TREATMENT_INFO_SUBMIT,
                    CrowdfundingInfoDataStatusTypeEnum.FUND_USE_SUBMIT);

    /**
     * 判断用户自主修改图文入口是否展示
     */
    @Override
    public List<AdminEntranceStatus> twEntranceIsDisplay(List<String> infoIds) {
        return judgeDisplayStatus(infoIds);
    }

    @Override
    public int saveTwModifyRecord(String infoUuid, long workOrderId, int modifyChannel) {
        TwModifyRecordDO twModifyRecordDO = this.buildTwRecord(infoUuid, workOrderId, modifyChannel);
        return adminTwModifyDao.saveTwModifyRecord(twModifyRecordDO);
    }

    @Override
    public int updateTwModifyFlag(long workOrderId, CrowdfundingInfo crowdfundingInfo) {

        log.info("AdminTwModifyServiceImpl updateTwModifyFlag workOrderId:{} crowdfundingInfo:{}", workOrderId, crowdfundingInfo);

        if (workOrderId <= 0) {
            return 0;
        }

        // 更新用户自主修改图文成功
        int record = adminTwModifyDao.updateTwModifyFlag(workOrderId, TwModifyChannelEnum.Flag.USER_MODIFY_FLAG.getValue());
        if (record > 0) {
            // 发消息通知用户自主修改审核通过
            this.sendMsg(crowdfundingInfo);
        }

        return record;
    }


    // 获取是否展示图文修改入口信息
    private List<AdminEntranceStatus> judgeDisplayStatus(List<String> infoIds) {

        List<AdminEntranceStatus> result = new ArrayList<>();

        List<CrowdfundingInfo> crowdfundingInfos = adminCrowdfundingInfoBiz.getListByInfoUuIds(infoIds);
        if (CollectionUtils.isEmpty(crowdfundingInfos)) {
            log.info("AdminEntranceDisplayServiceImpl twEntranceIsDisplay 无案例信息 infoIds:{}", infoIds);
            return result;
        }

        UserCaseStatusVO caseStatus = new UserCaseStatusVO();
        // 初始化
        crowdfundingInfos.forEach(crowdfundingInfo -> {
            // 案例结束或开关没开不展示入口
            boolean caseEnd = crowdfundingInfo.getEndTime() != null && new Date().before(crowdfundingInfo.getEndTime());
            result.add(new AdminEntranceStatus(crowdfundingInfo.getInfoId(), twDisplay && caseEnd, caseStatus));
        });

        // key:案例infoId value:该案例展示状态
        Map<String, AdminEntranceStatus> entranceStatusMap = result.stream().collect(Collectors.toMap(AdminEntranceStatus::getInfoId, Function.identity()));

        List<CfInfoExt> cfInfoExts = adminCfInfoExtBiz.getListByInfoUuids(infoIds);
        Map<String, CfInfoExt> cfInfoExtMap = cfInfoExts.stream().collect(Collectors.toMap(CfInfoExt::getInfoUuid, Function.identity()));

        List<Integer> caseIds = crowdfundingInfos.stream().map(CrowdfundingInfo::getId).collect(Collectors.toList());
        Set<Integer> caseIdList = getListByFilterUndo(caseIds);

        List<CrowdfundingInfoStatus> crowdfundingInfoStatusList = adminCrowdfundingInfoStatusBiz.getByInfoUuidsAndTypes(infoIds, statusEnums);
        Map<String, List<CrowdfundingInfoStatus>> statusMap = crowdfundingInfoStatusList.stream()
                .collect(Collectors.groupingBy(CrowdfundingInfoStatus::getInfoUuid));

        // 用户自主修改图文数 > selfModifyThreshold，不展示入口
        List<TwModifyRecordDO> twModifyRecordDOS = adminTwModifyDao.selectByInfoIds(infoIds);
        // key:infoId value:该案例自主修改图文次数
        Map<String, Integer> twModifyMap = new HashMap<>();
        buildTwModifyMap(twModifyMap, twModifyRecordDOS);


        crowdfundingInfos.forEach(crowdfundingInfo -> {

            AdminEntranceStatus entranceStatus = entranceStatusMap.get(crowdfundingInfo.getInfoId());

            // 未通过预审的案例不展示入口
            CfInfoExt cfInfoExt = cfInfoExtMap.get(crowdfundingInfo.getInfoId());
            caseStatus.setFirstApproveStatus(cfInfoExt.getFirstApproveStatus());
            if (FirstApproveStatusEnum.isNotPassed(FirstApproveStatusEnum.parse(cfInfoExt.getFirstApproveStatus()))) {
                log.info("AdminEntranceDisplayServiceImpl twEntranceIsDisplay 未通过预审的案例不展示入口 caseId:{}", cfInfoExt.getCaseId());
                entranceStatus.setTwEntranceDisplay(false);
            }

            // 获取图文工单审核状态： 1待审核;2其他
            // 获取材审状态
            caseStatus.setCrowdfundingStatus(crowdfundingInfo.getStatus().value());
            // 自主图文修改入口展示状态
            caseStatus.setTwEntranceDisplay(true);

            // 有待审核的图文审核工单不展示入口
            if (caseIdList.contains(crowdfundingInfo.getId())) {
                log.info("AdminEntranceDisplayServiceImpl twEntranceIsDisplay 有待审核的图文审核工单不展示入口 caseId:{}", crowdfundingInfo.getId());
                entranceStatus.setTwEntranceDisplay(false);
                // 获取图文工单审核状态： 1待审核 ；0其他
                caseStatus.setContentWorkOrderStatus(UNDO);
                caseStatus.setTwEntranceDisplay(false);
            }

            // 已进入材审，不展示入口
            if (!judgeAuditV2(statusMap, crowdfundingInfo)) {
                log.info("AdminEntranceDisplayServiceImpl twEntranceIsDisplay 已进入材审，不展示入口 caseId:{}", crowdfundingInfo.getId());
                entranceStatus.setTwEntranceDisplay(false);
                caseStatus.setTwEntranceDisplay(false);
            }

            if (twModifyMap.containsKey(entranceStatus.getInfoId()) && twModifyMap.get(entranceStatus.getInfoId()) >= selfModifyThreshold) {
                log.info("AdminEntranceDisplayServiceImpl twEntranceIsDisplay 用户自主修改图文数超出限制 infoId:{}", entranceStatus.getInfoId());
                entranceStatus.setTwEntranceDisplay(false);
                caseStatus.setTwEntranceDisplay(false);
            }


        });

        return result;
    }

    @NotNull
    private Set<Integer> getListByFilterUndo(List<Integer> caseIds) {
        Response<List<WorkOrderVO>> response = cfWorkOrderClient.listByCaseIdsAndTypes(caseIds, Lists.newArrayList(WorkOrderType.content.getType()));
        List<WorkOrderVO> workOrderVOS = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(Lists.newArrayList());
        //   2.过滤出待审核
        Set<Integer> caseIdList = workOrderVOS.stream()
                .filter(f -> HandleResultEnum.unDoResult().contains(f.getHandleResult()))
                .map(WorkOrderVO::getCaseId)
                .collect(Collectors.toSet());
        return caseIdList;
    }

    private void buildTwModifyMap(Map<String, Integer> twModifyMap, List<TwModifyRecordDO> twModifyRecordDOS) {

        twModifyRecordDOS.forEach(twModifyRecordDO -> {
            String key = twModifyRecordDO.getInfoUuid();
            twModifyMap.put(key, twModifyMap.getOrDefault(key, 0) + twModifyRecordDO.getModifyFlag());
        });

    }

    private Boolean judgeAuditV2(Map<String, List<CrowdfundingInfoStatus>> statusMap, CrowdfundingInfo crowdfundingInfo) {

        List<CrowdfundingInfoStatus> crowdfundingInfoStatuses = statusMap.get(crowdfundingInfo.getInfoId());
        if (CollectionUtils.isEmpty(crowdfundingInfoStatuses) || crowdfundingInfoStatuses.size() < 5) {
            return true;
        }

        return crowdfundingInfoStatuses.stream().filter(f -> judgeStatus(f.getStatus())).count() != 5;

    }

    private Boolean judgeStatus(Integer status) {
        return status == CrowdfundingInfoStatusEnum.PASSED.getCode()
                || status == CrowdfundingInfoStatusEnum.SUBMITTED.getCode()
                || status == CrowdfundingInfoStatusEnum.REJECTED.getCode();
    }

    private Boolean judgeAudit(List<CrowdfundingInfoStatus> crowdfundingInfoStatuses, CrowdfundingInfo crowdfundingInfo) {

        if (crowdfundingInfo.getStatus() == CrowdfundingStatus.APPROVE_PENDING) {
            return true;
        }

        return CollectionUtils.isEmpty(crowdfundingInfoStatuses);

    }

    private void sendMsg(CrowdfundingInfo crowdfundingInfo) {
        Map<Integer, String> params = Maps.newHashMap();
        params.put(9, crowdfundingInfo.getInfoId());
        Map<Long, Map<Integer, String>> mapMap = Maps.newHashMap();
        mapMap.put(crowdfundingInfo.getUserId(), params);
        msgClientV2Service.sendWxParamsMsg(WX_MESSAGE_TEMPLATE, mapMap);
    }

    private TwModifyRecordDO buildTwRecord(String infoUuid, long workOrderId, int modifyChannel) {
        TwModifyRecordDO twModifyRecordDO = new TwModifyRecordDO();
        twModifyRecordDO.setInfoUuid(infoUuid);
        twModifyRecordDO.setWorkOrderId(workOrderId);
        twModifyRecordDO.setModifyChannel(modifyChannel);
        return twModifyRecordDO;
    }

}
