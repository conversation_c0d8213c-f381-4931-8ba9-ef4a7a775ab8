package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.github.pagehelper.PageHelper;
import com.shuidihuzhu.cf.delegate.saas.OrganizationClientV1;
import com.shuidihuzhu.cf.biz.crowdfunding.CfReportFollowCommentBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfReportFollowCommentDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportFollowComment;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by niejiangnan on 2017/9/12.
 */
@Service
public class CfReportFollowCommentBizImpl implements CfReportFollowCommentBiz {

    @Autowired
    private CfReportFollowCommentDao cfReportFollowCommentDao;

    @Resource
    private OrganizationClientV1 organizationClientV1;

    private static final int MAX_ORGANIZATION = 128;

    @Override
    public List<CfReportFollowComment> getCommentListByInfoId(int infoId) {
        return cfReportFollowCommentDao.getCommentListByInfoId(infoId);
    }

    @Override
    public List<CfReportFollowComment> getCommentListByInfoIds(List<Integer> infoIds) {
        return cfReportFollowCommentDao.getCommentListByInfoIds(infoIds);
    }

    @Override
    public void save(int userId, String comment, Integer followType, int infoId, String operatorName) {
        CfReportFollowComment cfReportFollowComment = new CfReportFollowComment();
        cfReportFollowComment.setOperatorId(userId);
        cfReportFollowComment.setFollowType(followType);
        cfReportFollowComment.setInfoId(infoId);
        cfReportFollowComment.setOperatorName(operatorName);
        cfReportFollowComment.setComment(comment);
        cfReportFollowComment.setOrganization(getOrganization(userId));
        cfReportFollowCommentDao.save(cfReportFollowComment);
    }

    @Override
    public void saveWithTag(int userId, String tag, String content, int infoId, String operatorName, int followType) {
        String comment = String.join("：", tag, content);
        save(userId, comment, followType, infoId, operatorName);
    }

    @Override
    public void saveWithTag(int userId, String tag, String content, int infoId, String operatorName) {
        saveWithTag(userId, tag, content, infoId, operatorName, 0);
    }

    @Override
    public List<CfReportFollowComment> getByPage(int infoId, int pageNum, int pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        return cfReportFollowCommentDao.descFindByInfoId(infoId);
    }

    /**
     * 填充组织架构信息
     * @param list
     * @return
     */
    private List<CfReportFollowComment> fillOrg(List<CfReportFollowComment> list) {
        for (CfReportFollowComment v : list) {
            int userId = v.getOperatorId();
            String org = organizationClientV1.getUserRelationOrgName(userId).getResult();
            v.setOrganization(org);
        }
        return list;
    }

    /**
     * 获取组织快照
     * @param operatorId
     * @return
     */
    @Nullable
    private String getOrganization(Integer operatorId) {
        String org = "";
        if (operatorId == null || operatorId <= 0) {
            return org;
        }

        org = organizationClientV1.getUserRelationOrgName(operatorId).getResult();
        if (StringUtils.length(org) <= MAX_ORGANIZATION) {
            return org;
        }

        org = StringUtils.left(org, MAX_ORGANIZATION);
        return org;
    }
}
