package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfReportProblemParam;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2019-11-08 12:36
 **/
public interface CfReportProblemRelationshipBiz {

    boolean insert(CfReportProblemRelationship relationship);

    boolean bindRelationship(CfReportProblemParam.BindParam bindParam, CfReportProblem parentProblem, CfReportProblem childProblem);

    boolean deleteByProblemIdAndContent(int userId, int problemId, List<String> contents);

    boolean deleteByProblemIdAndNextProblemId(int problemId, List<Integer> nextProblemIds);

    boolean onlyChangeProblemName(int problemId, String newProblemName);

    /**
     * 可以获取拉下框信息,获取下一级问题
     */
    List<CfReportProblemRelationship> listRelationByProblemIds(List<Integer> problemIds);

    List<Integer> obtainRelationProblemId(int problemId, String content);

    //根据 nextProblemIds 获取relationship
    List<CfReportProblemRelationship> listByNextProblemIds(List<Integer> nextProblemIds);
}
