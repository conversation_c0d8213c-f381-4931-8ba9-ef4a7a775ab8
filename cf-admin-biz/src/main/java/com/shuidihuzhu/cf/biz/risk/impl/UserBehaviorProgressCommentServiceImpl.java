package com.shuidihuzhu.cf.biz.risk.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingCommentBiz;
import com.shuidihuzhu.cf.biz.risk.IUserBehaviorService;
import com.shuidihuzhu.cf.client.feign.CfPlatformEsFeignClient;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingCommentType;
import com.shuidihuzhu.cf.enums.crowdfunding.UserBehaviorEnum;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2019/10/12 下午3:03
 * @desc 动态评论
 */
@Service
public class UserBehaviorProgressCommentServiceImpl implements IUserBehaviorService {

    @Autowired
    private CfPlatformEsFeignClient platformEsFeignClient;

    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;

    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Autowired
    private AdminCrowdfundingCommentBiz cfCommentBiz;

    @Override
    public UserBehaviorEnum getBehavior() {
        return UserBehaviorEnum.PROGRESS_COMMENT;
    }

    @Override
    public List<AdminUserBehaviorDetail> buildBehaviorDetail(String mobile, long userId, String name, String identity, UserInfoModel userInfo, UserBehaviorEnum behaviorEnum) {
        List<CrowdfundingCommentVo> comments = cfCommentBiz.getCommentByUserIdAndTypeFromTiDb(userId,
                CrowdfundingCommentType.CROWDFUNDING_TRENDS.value(), 2000);
        if(CollectionUtils.isEmpty(comments)){
            return Lists.newArrayList();
        }

        UserInfoDetail userInfoDetail = new UserInfoDetail();
        userInfoDetail.setUserId(userId);
        userInfoDetail.setNickName(Objects.nonNull(userInfo) ? userInfo.getNickname() : "");

        List<AdminUserBehaviorDetail> commentDetails = Lists.newArrayList();

        for (CrowdfundingCommentVo comment : comments){
            int caseId = comment.getCrowdfundingId();
            CrowdfundingInfo caseInfo = crowdfundingFeignClient.getCaseInfoById(caseId).getData();
            CrowdFundingProgress progress = crowdfundingDelegate.getActivityProgressById(Math.toIntExact(comment.getParentId()));
            String imageUrl = Objects.nonNull(progress) ? progress.getImageUrls() : "";

            StringBuilder sb = new StringBuilder();
            sb.append("案例id:").append(caseId).append(REDEX);
            sb.append("案例标题:").append(Objects.nonNull(caseInfo) ? caseInfo.getTitle() : "").append(REDEX);
            sb.append("动态内容:").append(Objects.nonNull(progress) ? progress.getContent() : "").append(REDEX);
            sb.append("动态评论内容:").append(comment.getContent());

            AdminUserBehaviorDetail commentDetail = new AdminUserBehaviorDetail();
            commentDetail.setTime(comment.getCreateTime());
            commentDetail.setBehaviorType(UserBehaviorEnum.PROGRESS_COMMENT.getKey());
            commentDetail.setUserInfoDetail(userInfoDetail);
            commentDetail.setUrl(StringUtils.isNotEmpty(imageUrl) ? Lists.newArrayList(imageUrl.split(",")) : Lists.newArrayList());
            commentDetail.setBehavoir(Lists.newArrayList(sb.toString().split(REDEX)));

            commentDetails.add(commentDetail);
        }

        return commentDetails;
    }
}
