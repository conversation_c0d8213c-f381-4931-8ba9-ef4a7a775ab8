package com.shuidihuzhu.cf.biz.mina.impl;

import com.shuidihuzhu.cf.biz.mina.AdminCfPublishPhaseBiz;
import com.shuidihuzhu.cf.dao.mina.AdminCfPublishPhaseDao;
import com.shuidihuzhu.cf.model.miniprogram.CfPublishPhase;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;

@Service
public class AdminCfPublishPhaseBizImpl implements AdminCfPublishPhaseBiz {
    @Autowired
    private AdminCfPublishPhaseDao adminCfPublishPhaseDao;

    @Override
    public CfPublishPhase getLatestPhase() {
        return adminCfPublishPhaseDao.getLatestPhase();
    }

    @Override
    public int updatePhaseStatusByTime(Timestamp time) {
        if (time == null) {
            return 0;
        }
        return adminCfPublishPhaseDao.updatePhaseStatusByTime(time);
    }

    @Override
    public int updateByPhaseId(int phaseId, CfPublishPhase cfPublishPhase) {
        if (phaseId <= 0 || cfPublishPhase == null) {
            return 0;
        }
        return this.adminCfPublishPhaseDao.updateByPhaseId(phaseId, cfPublishPhase);
    }

    @Override
    public int delete(int phaseId) {
        return this.adminCfPublishPhaseDao.delete(phaseId);
    }


}
