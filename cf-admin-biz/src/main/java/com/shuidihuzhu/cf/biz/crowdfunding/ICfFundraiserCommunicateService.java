package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfFundraiserCommunicateDO;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/12/16 下午8:32
 * @desc
 */
public interface ICfFundraiserCommunicateService {
    int insert(CfFundraiserCommunicateDO communicateDO);
    int updateConnectStatus(long id, int connectStatus);
    int updateAnswer(long id, int connectStatus, String answerIds);
    List<CfFundraiserCommunicateDO> query(int caseId);
    CfFundraiserCommunicateDO queryByIdAndCase(long id, int caseId);
    CfFundraiserCommunicateDO queryById(long id);

    CfFundraiserCommunicateDO getByMobileAndCaseId(int caseId,String mobile);
}
