package com.shuidihuzhu.cf.biz.stat;

import com.shuidihuzhu.cf.model.message.MpNewsOperatingRecord;
import com.shuidihuzhu.cf.model.message.MpNewsOperatingTypeEnum;

import java.util.List;

public interface MpNewsOperatingRecordBiz {
    void saveOperateRecord(long articleTaskId, int userId, String userName, String comment, long subtaskId,
                           MpNewsOperatingTypeEnum type);

    List<MpNewsOperatingRecord> getByTaskId(long taskId, int current, int pageSize);

    int countOperatingRecordByTaskId(long taskId);

    List<MpNewsOperatingRecord> getByTaskIdAndType(long taskId, MpNewsOperatingTypeEnum type, int offset, int limit);

    List<MpNewsOperatingRecord> getByUserNameAndType(String userName, MpNewsOperatingTypeEnum type, int offset, int limit);
}
