package com.shuidihuzhu.cf.biz.crowdfunding.materialRefuse;

import com.shuidihuzhu.cf.model.crowdfunding.material.CfRefuseSuggestModify;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface CfRefuseSuggestModifyBiz {

    List<CfRefuseSuggestModify> selectSuggestModifyByDateTypes(Collection<Integer> dataTypes);

    Map<Integer, CfRefuseSuggestModify> selectSuggestMapping(Collection<Integer> uniqueIds);

    List<CfRefuseSuggestModify> selectSuggestModifyByIds(Collection<Integer> uniqueIds);

}
