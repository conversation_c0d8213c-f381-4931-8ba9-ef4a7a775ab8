package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.WorkOrderAssignBiz;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.async.DeferredResult;

import java.util.Map;

/**
 * <AUTHOR>
 * @DATE 2019/11/22
 */
@Service
public class WorkOrderAssignBizImpl implements WorkOrderAssignBiz {


    private final static Map<String, DeferredResult> map = Maps.newConcurrentMap();


    @Override
    public void setResult(String key) {
        DeferredResult dr = getByKey(key);

        if (dr != null){
            dr.setResult(NewResponseUtil.makeSuccess("assgin"));
        }
    }

    @Override
    public DeferredResult getByKey(String key) {
        return map.get(key);
    }

    @Override
    public void addDeferredResult(String key,DeferredResult deferredResult) {
        map.put(key,deferredResult);
    }


    @Override
    public void delDeferredResult(String key) {
        map.remove(key);
    }
}
