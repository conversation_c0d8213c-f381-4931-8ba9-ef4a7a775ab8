package com.shuidihuzhu.cf.biz.admin.impl.verifycount;

import com.shuidihuzhu.cf.biz.admin.verifycount.VerifyCountRoute;
import com.shuidihuzhu.cf.constants.crowdfunding.RedisKeyCons;
import com.shuidihuzhu.cf.enums.admin.AdminVerifyTypeEnum;
import com.shuidihuzhu.cf.vo.admin.VerifyCountVo;
import com.shuidihuzhu.client.cf.api.chaifenbeta.commontool.CfRedisKvBizFeignClient;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/11/4
 */
@Service
@Slf4j
@RefreshScope
public class CfBankCardVerifyImpl implements VerifyCountRoute {

    @Value("${apollo.finance.idcard-verify.day.limit:10}")
    private int MAX_BANK_VERIFY_TIMES;
    private final static String REDISKEY_PREFIX = "cf.finance.verifybankcard";

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    @Override
    public String getKey() {
        return "CfBankCardVerify";
    }

    @Override
    public String getName() {
        return "提交活动收款账户，验证收款人信息";
    }

    @Override
    public AdminVerifyTypeEnum getType() {
        return AdminVerifyTypeEnum.BANK_CARD_VERIFY;
    }

    @Override
    public int getLimit() {
        return MAX_BANK_VERIFY_TIMES;
    }

    @Override
    public VerifyCountVo getCurrentCountVo(long userId) {
        VerifyCountVo verifyCountVo = initVerifyCountVo();
        Integer count = redissonHandler.get(getKey(userId), Integer.class);
        verifyCountVo.setCurrentCount(count == null ? 0 : count);
        return verifyCountVo;
    }

    private String getKey(long userId) {
        return REDISKEY_PREFIX + "." + DateUtil.getYMDStringByDate(new Date()) + "." + userId;
    }

    @Override
    public boolean clear(long userId) {
        return this.redissonHandler.del(getKey(userId));
    }

    @Override
    public void innerTest(long userId) {
        redissonHandler.incrAndSetTimeWhenNotExists(getKey(userId), RedissonHandler.ONE_DAY);
    }
}
