package com.shuidihuzhu.cf.biz.call;

import com.shuidihuzhu.cf.call.CallInModel;
import com.shuidihuzhu.cf.call.CallOutModel;
import org.apache.ibatis.annotations.Param;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

public interface CallRecordBiz {
    List<CallInModel> getCallInRecords(String startDay, String endDay, String customerPhoneNumber, int current, int pageSize);

    List<CallOutModel> getCallOutRecords(String startDay, String endDay, String customerPhoneNumber, int current, int pageSize);

    List<CallInModel> getAllCallIn(String consumerPhoneNum);

    List<CallOutModel> getAllCallOut(String consumerPhoneNum);

    int insertCallOutModel(CallOutModel callOutModel);

    int insertCallInModel(CallInModel callInModel);
}
