package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.biz.crowdfunding.CfCommitVerifyItemBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfCommitVerifyItemDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfCommitVerifyItem;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * Created by ahrievil on 2017/4/24.
 */
@Service
public class CfCommitVerifyItemBizImpl implements CfCommitVerifyItemBiz {
    @Autowired
    private CfCommitVerifyItemDao cfCommitVerifyItemDao;
    @Override
    public List<CfCommitVerifyItem> selectRefuseList(String infoUuid) {
        return cfCommitVerifyItemDao.selectRefuseList(infoUuid);
    }

    @Override
    public List<CfCommitVerifyItem> selectBaseInfoRefuseList() {
        return cfCommitVerifyItemDao.selectBaseInfoRefuseList();
    }

    @Override
    public List<CfCommitVerifyItem> selectRefusesByList(List<Integer> cfRefuseReasonIds) {
        return cfCommitVerifyItemDao.selectRefusesByList(cfRefuseReasonIds);
    }

    @Override
    public List<Integer> selectAllType(int start, int size) {
        return cfCommitVerifyItemDao.selectAllType(start, size);
    }

    @Override
    public List<CfCommitVerifyItem> selectByIds(Set<Integer> set) {
        if (CollectionUtils.isEmpty(set)){
            return new ArrayList<>();
        }

        return cfCommitVerifyItemDao.selectByIds(set);
    }

    @Override
    public List<CfCommitVerifyItem> selectAll(int start, int size) {
        return cfCommitVerifyItemDao.selectAll(start, size);
    }

}
