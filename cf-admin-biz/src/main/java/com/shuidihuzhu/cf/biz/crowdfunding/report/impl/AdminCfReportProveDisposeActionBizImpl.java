package com.shuidihuzhu.cf.biz.crowdfunding.report.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.report.AdminCfReportProveDisposeActionBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfReportProveDisposeActionDao;
import com.shuidihuzhu.cf.model.report.CfReportProveDisposeAction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * @Auther: subing
 * @Date: 2020/4/17
 */
@Service
public class AdminCfReportProveDisposeActionBizImpl implements AdminCfReportProveDisposeActionBiz {
    @Autowired
    private AdminCfReportProveDisposeActionDao adminCfReportProveDisposeActionDao;


    @Override
    public int add(CfReportProveDisposeAction cfReportProveDisposeAction) {
        if (cfReportProveDisposeAction == null) {
            return 0;
        }
        return adminCfReportProveDisposeActionDao.add(cfReportProveDisposeAction);
    }

    @Override
    public String getDisposeAction(long trustId) {
        return adminCfReportProveDisposeActionDao.getDisposeAction(trustId);
    }

    @Override
    public CfReportProveDisposeAction findByInfoUuid(String infoUuid) {
        return adminCfReportProveDisposeActionDao.findByInfoUuid(infoUuid);
    }
}
