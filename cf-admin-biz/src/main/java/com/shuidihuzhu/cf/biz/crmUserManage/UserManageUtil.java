package com.shuidihuzhu.cf.biz.crmUserManage;

import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;

public class UserManageUtil {


    public static String queryRaiseCryptoIdCard(CfFirsApproveMaterial material) {
        return  material.getUserRelationType() == UserRelTypeEnum.SELF.getValue() ?
                material.getPatientCryptoIdcard() : material.getSelfCryptoIdcard();
    }

    public static String queryRaiseUserName(CfFirsApproveMaterial material) {
        return  material.getUserRelationType() == UserRelTypeEnum.SELF.getValue() ?
                material.getPatientRealName() : material.getSelfRealName();
    }
}
