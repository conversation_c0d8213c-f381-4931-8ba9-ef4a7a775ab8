package com.shuidihuzhu.cf.biz.aiphoto;


import com.shuidihuzhu.cf.model.aiphoto.PhotoAiInfoModel;
import com.shuidihuzhu.cf.model.crowdfunding.CfAIPhotoModel;

/**
 * @package: com.shuidihuzhu.cf.aiphoto
 * @Author: l<PERSON>jiaw<PERSON>
 * @Date: 2018/7/21  15:38
 */
public interface PhotoAiService {
    /**
     * 得到AI照片对比信息
     * @param cfAIPhotoModel
     * @return
     */
    PhotoAiInfoModel getPhotoAiInfo(CfAIPhotoModel cfAIPhotoModel);

    /**
     * 信息入库
     * @param photoAiInfoModel
     */
   int savePhotoAiInfo(PhotoAiInfoModel photoAiInfoModel);

    /**
     * 删除当前这条信息
     * @param parentId
     * @param photoType  0 默认患者  100 收款人
     */
    int deletePhotoAiInfo(Integer parentId,int photoType);


    /**
     * 异步患者处理ocr识别
     * @param cfAIPhotoModel
     * @param workOrderId
     */
    void aiCheckPhotos(CfAIPhotoModel cfAIPhotoModel, long workOrderId,int photoType);


}
