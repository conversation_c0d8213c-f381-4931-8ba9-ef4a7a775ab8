package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.admin.AdminOrganizationBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderFlowBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CaseEndReasonService;
import com.shuidihuzhu.cf.client.base.enums.IBaseErrorCode;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.dao.admin.AdminWorkOrderFlowDao;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.ReportSourceEnum;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.enums.crowdfunding.CfFinishStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceCapitalAccountFeignClient;
import com.shuidihuzhu.cf.finance.client.feign.CfFinancePauseFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.enums.CfDrawCashPauseRecordEnum;
import com.shuidihuzhu.cf.model.CaseEndModel;
import com.shuidihuzhu.cf.model.admin.AdminOrganization;
import com.shuidihuzhu.cf.model.crowdfunding.CfCapitalAccount;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowParam;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView;
import com.shuidihuzhu.cf.service.crowdfunding.report.CfReportService;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.model.ImageMaskInform;
import com.shuidihuzhu.client.model.enums.CaseEndReasonEnum;
import com.shuidihuzhu.common.util.BeanUtils;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.enums.MyErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2022/10/25 9:34 下午
 */
@Slf4j
@Service
public class CaseEndReasonServiceImpl implements CaseEndReasonService {
    @Autowired
    private AdminWorkOrderFlowBiz orderFlowBiz;

    @Resource
    private AdminCrowdfundingInfoBizImpl adminCrowdfundingInfoBiz;

    @Resource
    private CfFinanceCapitalAccountFeignClient cfFinanceCapitalAccountFeignClient;

    @Resource
    private CfFinancePauseFeignClient cfFinancePauseFeignClient;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;

    @Autowired
    private AdminOrganizationBiz organizationBiz;

    @Resource
    private AdminWorkOrderFlowBiz adminWorkOrderFlowBiz;

    @Resource
    private CfReportService cfReportService;
    @Resource
    private ShuidiCipher shuidiCipher;

    @Override
    public void raiserFinishCaseAction(CaseEndModel caseEndModel) {

        if (Objects.isNull(caseEndModel)) {
            log.info("CaseEndReasonServiceImpl caseEndModel is null");
            return;
        }
        log.info("CaseEndReasonServiceImpl raiserFinishCaseAction caseEndModel is {}", caseEndModel);

        // 只处理用户主动结束的案例
        if (caseEndModel.getFinishStatus() != CfFinishStatus.FINISH_BY_RAISER.getValue()) {
            log.info("CaseEndReasonServiceImpl caseEndModel is not user {}", JSONObject.toJSONString(caseEndModel));
            return ;
        }

        CaseEndReasonEnum caseEndReasonEnum = CaseEndReasonEnum.parse(caseEndModel.getReasonType());
        if (Objects.isNull(caseEndReasonEnum)) {
            log.info("CaseEndReasonServiceImpl caseEndReasonEnum is null {}", caseEndModel);
            return;
        }

        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseEndModel.getCaseId());
        if (Objects.isNull(crowdfundingInfo)) {
            log.info("CaseEndReasonServiceImpl raiserFinishCaseAction crowdfundingInfo is null");
            return;
        }

        // 不同结束原因不同处理
        switch (caseEndReasonEnum) {
            case PATIENT_DIED_REASON:
                log.info("CaseEndReasonServiceImpl raiserFinishCaseAction PATIENT_DIED_REASON strategy {}", caseEndModel);
                // 患者去世执行操作
                patientDiedAction(caseEndModel, crowdfundingInfo);
                break;
            case GIVE_UP_TREATMENT_REASON:
                log.info("CaseEndReasonServiceImpl raiserFinishCaseAction GIVE_UP_TREATMENT_REASON strategy {}", caseEndModel);
                // 生成举报条目
                generateReportEntry(caseEndModel, crowdfundingInfo);
                break;
            default:
                log.info("CaseEndReasonServiceImpl raiserFinishCaseAction other strategy {}", caseEndModel);
        }

    }

    @Override
    public OperationResult<Void> patientDied(AdminWorkOrderFlowView param) {
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(param.getCaseId());
        if (crowdfundingInfo == null) {
            log.info("CaseEndReasonServiceImpl patientDiedAction crowdfundingInfo is null {}", param);
            return OperationResult.fail(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        //创建前判断是否能生成，同案例+同问题类型+未处理完
        Response<String> existRelateFlow = orderFlowBiz.existRelateFlow(param);
        if (existRelateFlow.notOk()) {
            return OperationResult.failWithMsg(existRelateFlow.getMsg());
        }

        // 创建信息传递工单
        Response result = adminWorkOrderFlowBiz.createWorkOrderFlow(param);
        log.info("案例结束患者去世流转信息传递工单. result code: {}, message: {}", result.getCode(), result.getMsg());
        if (result.notOk()) {
            return OperationResult.failWithMsg(result.getMsg());
        }
        return OperationResult.success();
    }

    private void patientDiedAction(CaseEndModel caseEndModel, CrowdfundingInfo crowdfundingInfo) {

        if (Objects.isNull(caseEndModel)) {
            log.info("CaseEndReasonServiceImpl patientDiedAction caseEndModel is null");
            return;
        }

        // 判断余额是否达标
        FeignResponse<CfCapitalAccount> response = cfFinanceCapitalAccountFeignClient.capitalAccountGetByInfoUuid(crowdfundingInfo.getInfoId());
        CfCapitalAccount cfCapitalAccount = Optional.ofNullable(response).filter(FeignResponse::ok).map(FeignResponse::getData).orElse(null);
        if (Objects.isNull(cfCapitalAccount)) {
            log.info("CaseEndReasonServiceImpl patientDiedAction cfCapitalAccount is null {}", caseEndModel);
            return;
        }
        if (cfCapitalAccount.getSurplusAmount() <= 2000000) {
            log.info("CaseEndReasonServiceImpl patientDiedAction SurplusAmount is not ok {}", JSONObject.toJSONString(cfCapitalAccount));
            return;
        }

        // 创建信息传递工单
        createWorkOrderFlow(caseEndModel, crowdfundingInfo);
        // 案例自动暂停打款
        stopRemittance(caseEndModel, crowdfundingInfo);
    }

    private void generateReportEntry(CaseEndModel caseEndModel, CrowdfundingInfo crowdfundingInfo) {

        // 已筹金额不符合生成举报条目要求
        if (crowdfundingInfo.getAmount() <= 3000000) {
            log.info("CaseEndReasonServiceImpl generateReportEntry amount is not ok {} {}", caseEndModel, crowdfundingInfo);
            return;
        }

        // 生成举报条目
        String content = "患者放弃/保守治疗，回家休养，用户主动停止筹款，需跟进";
        String userName = "系统：系统自动标记";
        int reportId = cfReportService.markReport(crowdfundingInfo.getId(), content, userName, "", "9",
                crowdfundingInfo, false, "", 102, 16,
                "系统自动标记","", ReportSourceEnum.STRATEGY,"");
        log.info("CaseEndReasonServiceImpl generateReportEntry caseEndModel {} {}", caseEndModel, reportId);

    }

    /**
     * 案例暂停打款
     */
    private void stopRemittance(CaseEndModel caseEndModel, CrowdfundingInfo crowdfundingInfo) {

        FeignResponse feignResponse = cfFinancePauseFeignClient.addPauseV2(102, caseEndModel.getCaseId(), CfDrawCashPauseRecordEnum.PauseSourceTypeEnum.RISK_CONTROL.getCode(),
                Lists.newArrayList(CfDrawCashPauseRecordEnum.PauseReasonTypeEnum.PATIENT_HAS_DIED.getCode()),
                "患者去世，用户主动停止筹款", CfDrawCashPauseRecordEnum.RecordStatusEnum.PAUSE.getCode(), false, false);

        if (feignResponse.notOk()) {
            log.info("CaseEndReasonServiceImpl stopRemittance feignResponse is not ok {} {}", caseEndModel, JSONObject.toJSONString(feignResponse));
        }

    }

    /**
     * 创建信息传递工单
     */
    private void createWorkOrderFlow(CaseEndModel caseEndModel, CrowdfundingInfo crowdfundingInfo) {

        // 创建信息传递工单
        AdminWorkOrderFlowView adminWorkOrderFlowView = buildAdminWorkOrderFlowView(crowdfundingInfo, caseEndModel);
        if (Objects.isNull(adminWorkOrderFlowView)) {
            log.info("CaseEndReasonServiceImpl createWorkOrderFlow adminWorkOrderFlowView is null {}", JSONObject.toJSONString(caseEndModel));
            return;
        }
        Response result = adminWorkOrderFlowBiz.createWorkOrderFlow(adminWorkOrderFlowView);
        log.info("案例结束患者去世流转信息传递工单. result code: {}, message: {}", result.getCode(), result.getMsg());
    }

    private AdminWorkOrderFlowView buildAdminWorkOrderFlowView(CrowdfundingInfo crowdfundingInfo, CaseEndModel caseEndModel) {

        UserInfoModel userInfoModel = Optional.ofNullable(userInfoServiceBiz.getUserInfoByUserId(crowdfundingInfo.getUserId())).orElse(null);
        if (Objects.isNull(userInfoModel)) {
            log.info("CaseEndReasonServiceImpl buildAdminWorkOrderFlowView userInfoModel is null {}", JSONObject.toJSONString(crowdfundingInfo));
            return null;
        }

        AdminWorkOrderFlowView vo = new AdminWorkOrderFlowView();
        vo.setLevel(AdminWorkOrderConst.Level.MEDIUM.getCode());
        vo.setCaseId(crowdfundingInfo.getId());
        vo.setCaseTitle(StringUtils.trimToEmpty(crowdfundingInfo.getTitle()));
        vo.setMobile(shuidiCipher.decrypt(userInfoModel.getCryptoMobile()));
        vo.setUserIdentity(AdminWorkOrderFlowView.WorkOrderFlowUserIdentity.DEFAULT.getCode());
        vo.setProblemContent("患者去世，用户主动停止筹款，需跟进");
        vo.setNewFirstClassifyId(3161);
        vo.setNewSecondClassifyId(3166);
        vo.setNewThirdClassifyId(3168);
        vo.setCreatorId(102);
        vo.setProblemType(getOrgIdByName("石家庄二线综合处理组"));

        return vo;
    }

    private int getOrgIdByName(String orgName) {
        List<AdminOrganization> orgList = organizationBiz.getAdminOrganizationByName(orgName);
        return CollectionUtils.isEmpty(orgList) ? 0 : orgList.get(0).getId();
    }

}
