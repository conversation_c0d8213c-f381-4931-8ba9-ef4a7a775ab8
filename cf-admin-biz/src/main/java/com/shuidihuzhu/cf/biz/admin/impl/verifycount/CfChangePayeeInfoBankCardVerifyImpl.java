package com.shuidihuzhu.cf.biz.admin.impl.verifycount;

import com.shuidihuzhu.cf.biz.admin.verifycount.VerifyCountRoute;
import com.shuidihuzhu.cf.constants.crowdfunding.RedisKeyCons;
import com.shuidihuzhu.cf.enums.admin.AdminVerifyTypeEnum;
import com.shuidihuzhu.cf.vo.admin.VerifyCountVo;
import com.shuidihuzhu.client.cf.api.chaifenbeta.commontool.CfRedisKvBizFeignClient;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/11/4
 */
@Service
@Slf4j
public class CfChangePayeeInfoBankCardVerifyImpl implements VerifyCountRoute {

    private static final int MAX_BANK_VERIFY_TIMES = 10;
    @Autowired
    private CfRedisKvBizFeignClient cfRedisKvBizFeignClient;
    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    @Override
    public String getKey() {
        return "CfChangePayeeInfoBankCardVerify";
    }

    @Override
    public String getName() {
        return "非托管案例，修改收款人，验证银行卡";
    }

    @Override
    public AdminVerifyTypeEnum getType() {
        return AdminVerifyTypeEnum.BANK_CARD_VERIFY;
    }

    @Override
    public int getLimit() {
        Response<String> response = cfRedisKvBizFeignClient.queryByKey("MAX_BANK_VERIFY_TIME", true);
        if (response.notOk()  || response.getData() == null){
            log.info("response:{}", response.getData());
            return MAX_BANK_VERIFY_TIMES;
        }
        String strMaxTime = response.getData();
        log.info("CrowdfundingDreamService MAX_BANK_VERIFY_TIME in redis KV:{}", strMaxTime);
        int max = MAX_BANK_VERIFY_TIMES;
        if(StringUtils.isNumeric(strMaxTime)) {
            max = Integer.parseInt(strMaxTime);
        }
        return max < 0 ? MAX_BANK_VERIFY_TIMES : max;
    }

    @Override
    public VerifyCountVo getCurrentCountVo(long userId) {
        VerifyCountVo verifyCountVo = initVerifyCountVo();
        Integer verifyCount = this.redissonHandler.get(getVerifyRedisKey(userId), Integer.class);
        int count = verifyCount == null ? 0 : verifyCount;
        Date lastModifyTime = querylastBankVerifyTimeOfUser(userId);
        log.info("CfChangePayeeInfoBankCardVerifyImpl queryLastBankVerifyTime in redis :{}", lastModifyTime);
        //当天没有操作过, 或者上次操作已经是昨天，则将计数器重置
        if(lastModifyTime == null || DateUtil.getCurrentDate().after(lastModifyTime)) {
            count = 0;
        }
        verifyCountVo.setCurrentCount(count);
        return verifyCountVo;
    }

    public Date querylastBankVerifyTimeOfUser(long userId) {
        String key ="cf-finance_" + RedisKeyCons.CF_INFO_KEY_USER_BANK_VERIFY_LAST_MODIFED.replace("#userId#", userId + "");
        return redissonHandler.get(key, Date.class);
    }

    private String getVerifyRedisKey(long userId) {
        return "cf-finance_" +RedisKeyCons.CF_INFO_KEY_USER_BANK_VERIFY.replace("#userId#", userId + "");
    }

    @Override
    public boolean clear(long userId) {
        return this.redissonHandler.del(getVerifyRedisKey(userId));
    }

    @Override
    public void innerTest(long userId) {
        redissonHandler.incrAndSetTimeWhenNotExists(getVerifyRedisKey(userId), RedissonHandler.ONE_DAY);
        String key ="cf-finance_" + RedisKeyCons.CF_INFO_KEY_USER_BANK_VERIFY_LAST_MODIFED.replace("#userId#", userId + "");
        redissonHandler.setNX(key, new Date(), RedissonHandler.ONE_DAY);
    }
}
