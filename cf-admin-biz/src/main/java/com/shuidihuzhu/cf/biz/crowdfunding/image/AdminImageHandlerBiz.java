package com.shuidihuzhu.cf.biz.crowdfunding.image;

import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyProgress;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditCaseDetail;
import com.shuidihuzhu.cf.vo.crowdfunding.CfProgressVo;

/**
 * @Author: wangpeng
 * @Date: 2021/1/27 11:05
 * @Description:
 */
public interface AdminImageHandlerBiz {

    String imageHandlerWatermark(String imageUrl, int userId);

    void handlerCaseInfo(InitialAuditCaseDetail.CaseBaseInfo caseBaseInfo);

    void handlerCfInfoSupplyProgress(CfInfoSupplyProgress cfInfoSupplyProgress);

    void handlerCfProgressVo(CfProgressVo cfProgressVo);

    void handlerCrowdfundingReport(CrowdfundingReport crowdfundingReport);
}
