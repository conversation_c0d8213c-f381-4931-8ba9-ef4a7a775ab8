package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.IAdminCredibleEditInfoService;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCredibleEditInfoDAO;
import com.shuidihuzhu.cf.model.crowdfunding.CfCredibleEditInfoDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2020/1/10 下午2:41
 * @desc
 */
@Service
public class AdminCredibleEditInfoServiceImpl implements IAdminCredibleEditInfoService {
    @Autowired
    private AdminCredibleEditInfoDAO adminCredibleEditInfoDAO;

    @Override
    public int insert(CfCredibleEditInfoDO editInfoDO) {
        if(Objects.isNull(editInfoDO)){
            return 0;
        }
        return adminCredibleEditInfoDAO.insert(editInfoDO);
    }

    @Override
    public CfCredibleEditInfoDO queryById(long id) {
        if(id <= 0){
            return null;
        }
        return adminCredibleEditInfoDAO.queryById(id);
    }
}
