package com.shuidihuzhu.cf.biz.stat.impl;

import com.shuidihuzhu.cf.biz.stat.CfWxArticleTotalBiz;
import com.shuidihuzhu.cf.dao.stat.message.CfWxArticleTotalDao;
import com.shuidihuzhu.cf.model.message.CfWxArticleTotal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CfWxArticleTotalBizImpl implements CfWxArticleTotalBiz {
    @Autowired
    private CfWxArticleTotalDao cfWxArticleTotalDao;

    /**
     * stat_date 为图文阅读数量的总量的统计时间,倒叙,取第一个
     *
     * @param msgDataId
     * @param thirdType
     * @return
     */
    @Override
    public CfWxArticleTotal getCfWxArticleTotalByMediaIdAndThirdType(String msgDataId, int thirdType, int index) {
        return cfWxArticleTotalDao.getCfWxArticleTotalByMediaIdAndThirdType(msgDataId, thirdType, String.valueOf(index));
    }


    @Override
    public CfWxArticleTotal getCfWxArticleTotalByDate(String msgDataId, int thirdType, int index, String statDate) {
        return cfWxArticleTotalDao.getCfWxArticleTotalByDate(msgDataId, thirdType, index, statDate);
    }
}
