package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfReportFollowComment;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/9/12.
 */
public interface CfReportFollowCommentBiz {

    /**
     * 查询某个案例的举报跟进备注
     * @param infoId
     * @return
     */
    List<CfReportFollowComment> getCommentListByInfoId(int infoId);


    /**
     * 获取最新举报进展
     * @param infoIds
     * @return
     */
    List<CfReportFollowComment> getCommentListByInfoIds(List<Integer> infoIds);

    void save(int userId, String comment, Integer followType, int infoId, String operatorName);

    void saveWithTag(int userId,String tag, String content, int infoId, String operatorName, int followType);

    void saveWithTag(int userId,String tag, String content, int infoId, String operatorName);

    /**
     * 分页获取跟进记录
     * @param infoId
     * @param pageNum
     * @param pageSize
     * @return
     */
    List<CfReportFollowComment> getByPage(int infoId, int pageNum, int pageSize);
}
