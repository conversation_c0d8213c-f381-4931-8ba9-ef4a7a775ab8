package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.CfBlacklistWordBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfBlacklistWordDao;
import com.shuidihuzhu.cf.enums.crowdfunding.CfBlacklistWordTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfBlacklistWord;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> Ahrievil
 */
@Service
public class CfBlacklistWordBizImpl implements CfBlacklistWordBiz {

    @Autowired
    private CfBlacklistWordDao cfBlacklistWordDao;


    @Override
    public int add(CfBlacklistWord cfBlacklistWord) {
        return cfBlacklistWordDao.add(cfBlacklistWord);
    }

    @Override
    public int addList(List<CfBlacklistWord> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        return cfBlacklistWordDao.addList(list);
    }

    @Override
    public List<String> selectAllWordsLimit(CfBlacklistWordTypeEnum cfBlacklistWordTypeEnum, int start, int size) {
        return cfBlacklistWordDao.selectAllWordsLimit(cfBlacklistWordTypeEnum.getCode(), start, size);
    }
}
