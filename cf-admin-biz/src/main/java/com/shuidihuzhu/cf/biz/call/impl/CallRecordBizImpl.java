package com.shuidihuzhu.cf.biz.call.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.model.GetObjectRequest;
import com.qcloud.cos.region.Region;
import com.qcloud.cos.transfer.Download;
import com.qcloud.cos.transfer.TransferManager;
import com.shuidihuzhu.cf.biz.call.CallRecordBiz;
import com.shuidihuzhu.cf.call.CallInModel;
import com.shuidihuzhu.cf.call.CallOutModel;
import com.shuidihuzhu.cf.dao.call.CallInRecordDao;
import com.shuidihuzhu.cf.dao.call.CallOutRecordDao;
import com.shuidihuzhu.cf.dao.call.CallRecordType;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.http.client.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.List;

@Slf4j
@Service
public class CallRecordBizImpl implements CallRecordBiz {
    @Autowired
    private CallInRecordDao callInRecordDao;
    @Autowired
    private CallOutRecordDao callOutRecordDao;

    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    static TransferManager transferManager = null;

    private static COSCredentials cred = new BasicCOSCredentials("AKIDptJ3els9hALoGJqSxNtHZj5GyX0ICt21", "2xfPuENrHVYAJxa8VFLOIfzDHAHDuOuE");

    private static ClientConfig clientConfig = new ClientConfig(new Region("ap-beijing"));

    private static String bucketName = "call-records-1254024480";

    @Override
    public List<CallInModel> getCallInRecords(String startDay, String endDay, String customerPhoneNumber, int current, int pageSize) {
        PageHelper.startPage(current, pageSize);
        String encryptoCustomerPhoneNumber = StringUtils.isNotEmpty(customerPhoneNumber) ? oldShuidiCipher.aesEncrypt(customerPhoneNumber) : "";
        List<CallInModel> modelList = callInRecordDao.getRecordsByDayTimeAndCustomerPhoneNum(startDay, endDay, encryptoCustomerPhoneNumber);
        if (modelList != null && modelList.size() > 0) {
            for (CallInModel model : modelList) {
                if (!Strings.isNullOrEmpty(model.getEncryptCustomPhoneNumber())) {
                    model.setCustomPhoneNumber(shuidiCipher.decrypt(model.getEncryptCustomPhoneNumber()));
                }
            }
        }
        return modelList;
    }

    @Override
    public List<CallOutModel> getCallOutRecords(String startDay, String endDay, String customerPhoneNumber, int current, int pageSize) {
        PageHelper.startPage(current, pageSize);
        String encryptoCustomerPhoneNumber = StringUtils.isNotEmpty(customerPhoneNumber) ? oldShuidiCipher.aesEncrypt(customerPhoneNumber) : "";
        List<CallOutModel> modelList = callOutRecordDao.getRecordsByDayTimeAndCustomerPhoneNum(startDay, endDay, encryptoCustomerPhoneNumber);
        if (modelList != null && modelList.size() > 0) {
            for (CallOutModel model : modelList) {
                if (!Strings.isNullOrEmpty(model.getEncryptCustomPhoneNumber())) {
                    model.setCustomPhoneNumber(shuidiCipher.decrypt(model.getEncryptCustomPhoneNumber()));
                }
                if (!Strings.isNullOrEmpty(model.getEncryptSeatPhoneNumber())) {
                    model.setSeatPhoneNumber(shuidiCipher.decrypt(model.getEncryptSeatPhoneNumber()));
                }
            }
        }
        return modelList;
    }

    @Override
    public List<CallInModel> getAllCallIn(String consumerPhoneNum) {
        return callInRecordDao.getAllByPhoneNum(oldShuidiCipher.aesEncrypt(consumerPhoneNum));
    }

    @Override
    public List<CallOutModel> getAllCallOut(String consumerPhoneNum) {
        return callOutRecordDao.getAllByPhoneNum(oldShuidiCipher.aesEncrypt(consumerPhoneNum));
    }

    @Override
    public int insertCallOutModel(CallOutModel callOutModel) {
        return callOutRecordDao.insertCallOutModel(callOutModel);
    }

    @Override
    public int insertCallInModel(CallInModel callInModel) {
        return callInRecordDao.insertCallInModel(callInModel);
    }
}

