package com.shuidihuzhu.cf.biz.admin.verifycount;

import com.shuidihuzhu.cf.enums.admin.AdminVerifyTypeEnum;
import com.shuidihuzhu.cf.vo.admin.VerifyCountVo;

/**
 * <AUTHOR>
 * @date 2020/11/4
 */
public interface VerifyCountRoute {

    String getKey();

    String getName();

    AdminVerifyTypeEnum getType();

    int getLimit();

    VerifyCountVo getCurrentCountVo(long userId);
    /**
     * 初始化验证次数Vo
     * @return
     */
    default VerifyCountVo initVerifyCountVo() {
        VerifyCountVo verifyCountVo = new VerifyCountVo();
        verifyCountVo.setKey(getKey());
        verifyCountVo.setName(getName());
        verifyCountVo.setType(getType().getType());
        verifyCountVo.setLimitCount(getLimit());
        return verifyCountVo;
    }

    boolean clear(long userId);

    void innerTest(long userId);
}
