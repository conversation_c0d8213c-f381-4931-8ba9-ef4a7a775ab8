package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.admin.util.AdminListUtil;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfRefuseReasonMsgBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseReasonEntityBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseReasonItemBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseReasonCommonBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseReasonTagBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseUseSceneRankMappingBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingRediskvBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.materialRefuse.CfRefuseSuggestModifyBiz;
import com.shuidihuzhu.cf.client.material.utils.MaterialCollectionUtils;
import com.shuidihuzhu.cf.dao.crowdfunding.CfRefuseReasonCustomEntityDao;
import com.shuidihuzhu.cf.dao.crowdfunding.CfRefuseReasonEntityDao;
import com.shuidihuzhu.cf.dao.crowdfunding.CfRefuseReasonEntityRiskLabelDao;
import com.shuidihuzhu.cf.dao.crowdfunding.CfRefuseReasonEntityVersionMappingDao;
import com.shuidihuzhu.cf.domain.label.risk.Label;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.InitialAudit.CfRefuseReasonRiskLabelRelateEnum;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.material.AuditSuggestModifyDetail;
import com.shuidihuzhu.cf.model.crowdfunding.material.CfRefuseSuggestModify;
import com.shuidihuzhu.cf.model.crowdfunding.materialAudit.AuditSuggestModifyParam;
import com.shuidihuzhu.cf.model.crowdfunding.materialAudit.CfRefuseModifyVo;
import com.shuidihuzhu.cf.service.label.risk.RiskLabelService;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditOperationItem;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.infra.starter.mail.service.EmailService;
import com.shuidihuzhu.common.web.util.DateUtil;
import io.swagger.models.auth.In;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.kafka.common.protocol.types.Field;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by Ahrievil on 2017/7/26
 */
@Service
@Slf4j
public class CfRefuseReasonEntityBizImpl implements CfRefuseReasonEntityBiz {

    private final static Logger LOGGER = LoggerFactory.getLogger(CfRefuseReasonEntityBizImpl.class);
    @Autowired
    private CfRefuseReasonEntityDao cfRefuseReasonEntityDao;
    @Autowired
    private CrowdfundingRediskvBiz crowdfundingRediskvBiz;
    @Autowired
    private AdminCfRefuseReasonMsgBiz adminCfRefuseReasonMsgBiz;
    @Autowired
    private EmailService emailService;
    @Autowired
    private CfRefuseReasonItemBiz cfRefuseReasonItemBiz;
    @Autowired
    private CfRefuseReasonCommonBiz reasonCommonBiz;
    @Autowired
    private CfRefuseReasonTagBiz reasonTagBiz;
    @Autowired
    private CfRefuseUseSceneRankMappingBiz rankMappingBiz;
    @Autowired
    private CfRefuseSuggestModifyBiz suggestModifyBiz;
    @Autowired
    private CfRefuseReasonEntityVersionMappingDao refuseReasonEntityVersionMappingDao;
    @Resource
    private CfRefuseReasonCustomEntityDao cfRefuseReasonCustomEntityDao;

    @Resource
    private CfRefuseReasonEntityRiskLabelDao cfRefuseReasonEntityRiskLabelDao;

    @Resource
    private RiskLabelService riskLabelService;

    private static String EDIT_SUGGEST_LOG = "编辑修改建议由 %s 改为 %s  ";
    private static String EDIT_ITEM_LOG = "编辑C端修改位置由 %s 改为 %s"  ;
    private static String EDIT_USE_SCENE_LOG = "编辑使用场景由 %s 改为 %s  ";
    private static String EDIT_SERVICE_LIB_LOG = "勾选后是否进入重服务库由 %s 改为 %s  ";

    @Override
    public int insertOne(CfRefuseReasonEntity cfRefuseReasonEntity) {
        return cfRefuseReasonEntityDao.insertOne(cfRefuseReasonEntity);
    }

    @Override
    public List<CfRefuseReasonEntity> selectByIds(List<Integer> list) {
        if (CollectionUtils.isEmpty(list))
            return Collections.emptyList();
        List<CfRefuseReasonEntity> cfRefuseReasonEntities = cfRefuseReasonEntityDao.selectByIds(list);
        return cfRefuseReasonEntities != null ? cfRefuseReasonEntities : Collections.emptyList() ;
    }

    @Override
    public int frequencyPlusOne(Set set) {
        return cfRefuseReasonEntityDao.frequencyPlusOne(set);
    }

    @Override
    public List<CfRefuseReasonEntity> selectAll(int start, int size) {
        return cfRefuseReasonEntityDao.selectAll(start, size);
    }

    @Override
    public int deleteOne(int id) {
        return cfRefuseReasonEntityDao.deleteOne(id);
    }

    @Override
    public CfRefuseReasonEntity selectById(int id, Integer isDelete) {
        return cfRefuseReasonEntityDao.selectByIdAndDeleteStatus(id, isDelete);
    }

    @Override
    public void sendFrequency(Timestamp begin, Timestamp end) {
        List<CfRefuseReasonMsg> msgList = AdminListUtil.getList(3000, (start, size) -> adminCfRefuseReasonMsgBiz.selectSimpleFieldsByTimeLimit(begin, end, start, size));
        List<RefuseMailVo> mailList = getMailList(msgList);
        StringBuilder mailContent = new StringBuilder();
        mailContent.append("<h>").append(DateUtil.getMonthDayStr(new Date())).append("  cf_refuse_reason_entity表数据</h>");
        mailContent.append("<table border=\"1\" cellspacing=\"0\" cellpadding=\"0\" " +
                "style=\"border-collapse: collapse;border-width:0px;\">");
        mailContent.append("");
        mailContent.append("<tr>");
        mailContent.append("<td style=\"font-size: 9pt;font-family: 'Apple Braille' \" valign=\"top\"><font style=\"font-family: 'Apple Braille' \" color = \"blue\">"+"驳回次数分组："+"</font></td>");
        mailContent.append("<td style=\"font-size: 9pt;font-family: 'Apple Braille' \" valign=\"top\"><font style=\"font-family: 'Apple Braille' \" color = \"blue\">"+"ID："+"</font></td>");
        mailContent.append("<td style=\"font-size: 9pt;font-family: 'Apple Braille' \" valign=\"top\"><font style=\"font-family: 'Apple Braille' \" color = \"blue\">"+"Content："+"</font></td>");
        mailContent.append("<td style=\"font-size: 9pt;font-family: 'Apple Braille' \" valign=\"top\"><font style=\"font-family: 'Apple Braille' \" color = \"blue\">"+"Frequency："+"</font></td>");
        mailContent.append("</tr>");
        for (RefuseMailVo refuseMailVo : mailList) {
            mailContent.append("<tr>");
            mailContent.append("<td><span>").append(refuseMailVo.getGroup()).append("</span></td>");
            mailContent.append("<td><span>").append(refuseMailVo.getId()).append("</span></td>");
            mailContent.append("<td><span>").append(refuseMailVo.getContent()).append("</span></td>");
            mailContent.append("<td><span>").append(refuseMailVo.getFrequency()).append("</span></td>");
            mailContent.append("</tr>");
        }
        mailContent.append("</table>");
        try {
            List<String> recipientsList = Arrays.asList(crowdfundingRediskvBiz.selectMailRecipient("REFUSE_REASON_MAIL_RECIPIENT").split(","));
            emailService.sendHtmlMail(recipientsList,"cf_refuse_reason_entity表数据",mailContent.toString());
        } catch (Exception e) {
            LOGGER.error("sendFrequency Error!", e);
        }
    }

    @Override
    public List<CfRefuseReasonEntity> selectByReasonIds(Set<Integer> set, Integer dataStatus) {
        if (CollectionUtils.isEmpty(set))
            return Collections.emptyList();
        List<CfRefuseReasonEntity> cfRefuseReasonItemMaps = cfRefuseReasonEntityDao.selectByReasonIds(set, dataStatus);
        return cfRefuseReasonItemMaps != null ? cfRefuseReasonItemMaps : Collections.emptyList();
    }

    private List<RefuseMailVo> getMailList(List<CfRefuseReasonMsg> msgList) {
        List<Integer> reasonIds = msgList.stream().flatMap(val -> Splitter.on(",").trimResults()
                .splitToList(val.getReasonIds()).stream().distinct()).map(Integer::valueOf).distinct().collect(Collectors.toList());
        List<CfRefuseReasonEntity> cfRefuseReasonEntities = cfRefuseReasonEntityDao.selectByReasonIds(Sets.newHashSet(reasonIds), null);
        Map<Integer, CfRefuseReasonEntity> entityMap = cfRefuseReasonEntities.stream()
                .collect(Collectors.toMap(CfRefuseReasonEntity::getId, Function.identity()));
        List<Integer> groupOne = Lists.newArrayList();
        List<Integer> groupTwo = Lists.newArrayList();
        List<Integer> groupThree = Lists.newArrayList();
        msgList.forEach(val -> {
            int refuseCount = val.getRefuseCount();
            List<Integer> reasonIdList = Splitter.on(",").splitToList(val.getReasonIds())
                    .stream().map(Integer::valueOf).distinct().collect(Collectors.toList());
            if (refuseCount == 1 || refuseCount == 0) {
                groupOne.addAll(reasonIdList);
            } else if (refuseCount <= 4 && refuseCount >= 2) {
                groupTwo.addAll(reasonIdList);
            } else {
                groupThree.addAll(reasonIdList);
            }
        });
        Map<Integer, Long> groupOneStat = groupOne.stream().filter(Objects::nonNull)
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        Map<Integer, Long> groupTwoStat = groupTwo.stream().filter(Objects::nonNull)
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        Map<Integer, Long> groupThreeStat = groupThree.stream().filter(Objects::nonNull)
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        List<RefuseMailVo> groupOneMailVoList = groupOneStat.entrySet().stream().map(val -> {
            Integer key = val.getKey();
            CfRefuseReasonEntity cfRefuseReasonEntity = entityMap.get(key);
            return new RefuseMailVo(key, cfRefuseReasonEntity.getContent(), val.getValue(), "等于0");
        }).collect(Collectors.toList());
        List<RefuseMailVo> groupTwoMailVoList = groupTwoStat.entrySet().stream().map(val -> {
            Integer key = val.getKey();
            CfRefuseReasonEntity cfRefuseReasonEntity = entityMap.get(key);
            return new RefuseMailVo(key, cfRefuseReasonEntity.getContent(), val.getValue(), "大于0小于等于3");
        }).collect(Collectors.toList());
        List<RefuseMailVo> groupThreeMailVoList = groupThreeStat.entrySet().stream().map(val -> {
            Integer key = val.getKey();
            CfRefuseReasonEntity cfRefuseReasonEntity = entityMap.get(key);
            return new RefuseMailVo(key, cfRefuseReasonEntity.getContent(), val.getValue(), "大于3");
        }).collect(Collectors.toList());
        List<RefuseMailVo> mailVoList = Lists.newArrayList();
        mailVoList.addAll(groupOneMailVoList);
        mailVoList.addAll(groupTwoMailVoList);
        mailVoList.addAll(groupThreeMailVoList);
        return mailVoList;
    }

    @Data
    public class RefuseMailVo {

        private int id;
        private String content;
        private long frequency;
        private String group;

        public RefuseMailVo() {
        }

        public RefuseMailVo(int id, String content, long frequency, String group) {
            this.id = id;
            this.content = content;
            this.frequency = frequency;
            this.group = group;
        }
    }

    @Override
    public int updateDeleteStatus(int id, int deleteStatus) {
        return cfRefuseReasonEntityDao.updateDeleteStatus(id, deleteStatus);
    }

    @Override
    public List<CfRefuseReasonEntity> selectByTagIdAndDeleteStatus(int tagId, int deleteStatus) {
        return  cfRefuseReasonEntityDao.selectByTagIdAndDeleteStatus(tagId, deleteStatus);
    }


    @Override
    public void editReasonEntityUseScene(CfRefuseModifyVo modifyVo) {
        log.info("修改驳回理由的使用场景 param:{}", JSON.toJSONString(modifyVo));

        CfRefuseReasonEntity reasonEntity = cfRefuseReasonEntityDao.selectByIdAndDeleteStatus(modifyVo.getEntityId(), null);
        reasonCommonBiz.validateUseScene(reasonEntity, modifyVo.getUseSceneIds());
        List<CfRefuseReasonEntity.CfRefuseReasonUseSceneRankMapping>  allValidRankMapping = rankMappingBiz.selectByEntityId(modifyVo.getEntityId());

        List<Integer> addSceneIds = reasonCommonBiz.getIdListSplitterByComma(modifyVo.getUseSceneIds());
        List<Integer> deleteIds = Lists.newArrayList();
        List<Integer> oldUseSceneIds = Lists.newArrayList();
        for (CfRefuseReasonEntity.CfRefuseReasonUseSceneRankMapping sceneRank : allValidRankMapping) {

            oldUseSceneIds.add(sceneRank.getUseScene());

            if (!addSceneIds.contains(sceneRank.getUseScene())) {
                deleteIds.add(sceneRank.getId());
            } else {
                addSceneIds.remove((Integer) sceneRank.getUseScene());
            }
        }

        rankMappingBiz.deleteUseSceneRankMapping(deleteIds);
        rankMappingBiz.addUseSceneRankMapping(reasonEntity, addSceneIds, true);
        cfRefuseReasonEntityDao.updateItemAndSuggestModify(modifyVo);
        String allEditLogs = getAllEditLogs(reasonEntity, modifyVo, oldUseSceneIds);
        StringBuilder stringBuilder = new StringBuilder(allEditLogs);
        editRefuseEntityRiskLabel(modifyVo, stringBuilder);
        reasonCommonBiz.addOperateEntityLog(modifyVo.getUserId(), modifyVo.getEntityId(), stringBuilder.toString());
    }

    private void editRefuseEntityRiskLabel(CfRefuseModifyVo cfRefuseModifyVo, StringBuilder allEditLogs) {
        List<CfRefuseReasonEntityRiskLabel> reasonEntityRiskLabelList = cfRefuseReasonEntityRiskLabelDao.selectByRefuseEntityId(cfRefuseModifyVo.getEntityId());
        if (CollectionUtils.isEmpty(reasonEntityRiskLabelList) && StringUtils.isEmpty(cfRefuseModifyVo.getRiskLabelIdList())) {
            return;
        }
        editRefuseEntityRiskLabelLog(cfRefuseModifyVo, reasonEntityRiskLabelList, allEditLogs);
        cfRefuseReasonEntityRiskLabelDao.deleteByReasonEntityId(cfRefuseModifyVo.getEntityId());
        if (StringUtils.isNotEmpty(cfRefuseModifyVo.getRiskLabelIdList())) {
            Splitter.on(",")
                    .splitToList(cfRefuseModifyVo.getRiskLabelIdList())
                    .stream()
                    .map(Long::valueOf)
                    .forEach(f -> {
                        CfRefuseReasonEntityRiskLabel riskLabel = new CfRefuseReasonEntityRiskLabel();
                        riskLabel.setRefuseEntityId(cfRefuseModifyVo.getEntityId());
                        riskLabel.setRiskLabelRelated(cfRefuseModifyVo.getRiskLabelRelated());
                        riskLabel.setRiskLabelId(f);
                        cfRefuseReasonEntityRiskLabelDao.insert(riskLabel);
                    });
        }

    }

    private void editRefuseEntityRiskLabelLog(CfRefuseModifyVo cfRefuseModifyVo, List<CfRefuseReasonEntityRiskLabel> riskLabelList, StringBuilder allEditLogs) {
        String riskLabelRelate = CollectionUtils.isEmpty(riskLabelList) ? "" : Objects.isNull(CfRefuseReasonRiskLabelRelateEnum.getCfRefuseReasonRiskLabelRelateEnum(riskLabelList.get(0).getRiskLabelRelated()))
                ? "" : CfRefuseReasonRiskLabelRelateEnum.getCfRefuseReasonRiskLabelRelateEnum(riskLabelList.get(0).getRiskLabelRelated()).getMsg();
        StringBuilder riskLabel = new StringBuilder();

        String riskLabelRelateModify = Objects.isNull(cfRefuseModifyVo.getRiskLabelRelated()) ? "" : Objects.isNull(CfRefuseReasonRiskLabelRelateEnum.getCfRefuseReasonRiskLabelRelateEnum(cfRefuseModifyVo.getRiskLabelRelated()))
                ? "" : CfRefuseReasonRiskLabelRelateEnum.getCfRefuseReasonRiskLabelRelateEnum(cfRefuseModifyVo.getRiskLabelRelated()).getMsg();
        StringBuilder riskLabelModify = new StringBuilder();

        List<Long> labelIds = new ArrayList<>();
        List<Long> labelIdRisk = CollectionUtils.isNotEmpty(riskLabelList) ? riskLabelList.stream()
                .map(CfRefuseReasonEntityRiskLabel::getRiskLabelId)
                .collect(Collectors.toList())
                : new ArrayList<>();
        List<Long> labelIdsModify = StringUtils.isNotEmpty(cfRefuseModifyVo.getRiskLabelIdList()) ? Splitter.on(",")
                .splitToList(cfRefuseModifyVo.getRiskLabelIdList())
                .stream()
                .map(Long::valueOf)
                .collect(Collectors.toList())
                : new ArrayList<>();
        labelIds.addAll(labelIdRisk);
        labelIds.addAll(labelIdsModify);
        List<Label> labelList = riskLabelService.getLabelByIds(labelIds);
        for (Label label : labelList) {
            String name = label.getPathName() + "/" + label.getName();
            if (labelIdRisk.contains(label.getId())) {
                riskLabel.append(name).append(" ");
            }
            if (labelIdsModify.contains(label.getId())) {
                riskLabelModify.append(name).append(" ");
            }
        }
        if (!StringUtils.equals(riskLabelRelate, riskLabelRelateModify)) {
             allEditLogs.append("是否关联风险标签：由“")
                     .append(riskLabelRelate)
                     .append("”改为“")
                     .append(riskLabelRelateModify)
                     .append("”；");
        }
        if (!StringUtils.equals(riskLabel.toString(), riskLabelModify.toString())) {
            allEditLogs.append("关联的风险标签：由“")
                    .append(riskLabel.toString())
                    .append("”改为“")
                    .append(riskLabelModify.toString())
                    .append("”；");
        }

    }

    private String getAllEditLogs(CfRefuseReasonEntity reasonEntity,
                               CfRefuseModifyVo modifyVo,
                               List<Integer> oldUseSceneIds) {
        StringBuilder allEditLogs = new StringBuilder();

        allEditLogs.append(getEditItemLog(reasonEntity.getItemIds(), modifyVo.getItemIds()));
        allEditLogs.append(getEditSuggestLog(reasonEntity.getSuggestModifyIds(), modifyVo.getSuggestModifyIds()));
        allEditLogs.append(getEditUseSceneLog(oldUseSceneIds, modifyVo.getUseSceneIds()));
        allEditLogs.append(getEditServiceLibLog(reasonEntity.getWeightServiceLib(), modifyVo.getWeightServiceLib()));

        return allEditLogs.toString();
    }

    private String getEditItemLog(String oldItemIdStr, String newItemIdStr) {
        List<Integer> oldItemIds = getSortedIdsSplitterByComma(oldItemIdStr);
        List<Integer> curItemIds = getSortedIdsSplitterByComma(newItemIdStr);
        if (hasModify(oldItemIds, curItemIds)) {
            Map<Integer, CfRefuseReasonItem> itemMapping = cfRefuseReasonItemBiz.selectMappingByIds(
                    MaterialCollectionUtils.getAllListCollection(oldItemIds, curItemIds));
            return String.format(EDIT_ITEM_LOG, getEditText(oldItemIds, itemMapping),
                    getEditText(curItemIds, itemMapping));
        }

        return "";
    }

    private String getEditSuggestLog(String oldSuggestIdStr, String newSuggestIdStr) {

        List<Integer> oldModifyIds = getSortedIdsSplitterByComma(oldSuggestIdStr);
        List<Integer> curModifyIds = getSortedIdsSplitterByComma(newSuggestIdStr);

        if (hasModify(oldModifyIds, curModifyIds)) {

            Map<Integer, CfRefuseSuggestModify> modifyMapping = suggestModifyBiz.selectSuggestMapping(
                    MaterialCollectionUtils.getAllListCollection(oldModifyIds, curModifyIds));
            return String.format(EDIT_SUGGEST_LOG, getEditText(oldModifyIds, modifyMapping),
                    getEditText(curModifyIds, modifyMapping));
        }

        return "";
    }

    private String getEditServiceLibLog(int oldServiceLib,
                                        int newServiceLib) {

        if (oldServiceLib != newServiceLib) {
            return String.format(EDIT_SERVICE_LIB_LOG, CfRefuseReasonEntity.getServiceLibLog(oldServiceLib),
                    CfRefuseReasonEntity.getServiceLibLog(newServiceLib));
        }

        return "";
    }

    // 添加日志
    private String getEditUseSceneLog(List<Integer> oldUseSceneIds, String useSceneIds) {

        Collections.sort(oldUseSceneIds);
        List<Integer> curUseScenes = getSortedIdsSplitterByComma(useSceneIds);

        return hasModify(oldUseSceneIds, curUseScenes) ? String.format(EDIT_USE_SCENE_LOG,
                CfRefuseReasonEntity.RejectOptionUseSceneEnum.getUseSceneDesc(oldUseSceneIds),
                CfRefuseReasonEntity.RejectOptionUseSceneEnum.getUseSceneDesc(curUseScenes)) : "";
    }

    private <T> String getEditText(List<Integer> modifyIds,
                                        Map<Integer, T> mapping) {
        if (CollectionUtils.isEmpty(modifyIds)) {
            return "\"\"";
        }

        String content = "";
        boolean needRemove = false;
        for (Integer modifyId : modifyIds) {
            T modify = mapping.get(modifyId);
            if (modify != null) {
                content = content + "\""  + getContent(modify) + "\"" + "、";
                needRemove = true;
            }
        }

        return needRemove ? content.substring(0, content.length() - 1) : content;
    }

    private <T> String getContent(T material) {
        if (material instanceof CfRefuseSuggestModify) {
            return ((CfRefuseSuggestModify)material).getContent();
        }

        if (material instanceof CfRefuseReasonItem) {
            return ((CfRefuseReasonItem)material).getContent();
        }
        return "";
    }

    private List<Integer> getSortedIdsSplitterByComma(String idStr) {
        List<Integer> ids = reasonCommonBiz.getIdListSplitterByComma(idStr);

        Collections.sort(ids);
        return ids;
    }

    private boolean hasModify(List<Integer> oldIds, List<Integer> curIds) {

        if (oldIds.size() != curIds.size()) {
            return true;
        }

        for (int i = 0; i < oldIds.size(); ++i) {
            if (!oldIds.get(i).equals(curIds.get(i))) {
                return true;
            }
        }

        return false;
    }

    public CfRefuseReasonEntity queryCanRelationEntityById(int sourceId, int relationId) {
        CfRefuseReasonEntity sourceEntity = cfRefuseReasonEntityDao.selectByIdAndDeleteStatus(sourceId, null);
        CfRefuseReasonEntity relationEntity = cfRefuseReasonEntityDao.selectByIdAndDeleteStatus(relationId, null);

        reasonCommonBiz.validateCanRelationId(sourceEntity, relationEntity);
        
        // 驳回位置
        relationEntity.setRefuseReasonItems(buildRefuseReasonItem(relationEntity.getItemIds()));

        return relationEntity;
    }

    private List<String> buildRefuseReasonItem(String itemIds) {
        List<String> itemList = Lists.newArrayList();
        if (StringUtils.isBlank(itemIds)) {
            return itemList;
        }

        List<Integer> rejectPositionIds = reasonCommonBiz.getIdListSplitterByComma(itemIds);
        List<CfRefuseReasonItem> cfRefuseReasonItems = cfRefuseReasonItemBiz.selectByIds(Sets.newHashSet(rejectPositionIds));
        for (CfRefuseReasonItem reasonItem : cfRefuseReasonItems) {
            itemList.add(reasonItem.getContent());
        }
        return itemList;
    }

    @Override
    public void editChoiceRelationId(int userId, int entityId, String relationIds) {

        CfRefuseReasonEntity entity = cfRefuseReasonEntityDao.selectByIdAndDeleteStatus(entityId, null);

        reasonCommonBiz.validateRelationIds(entity, relationIds);
        cfRefuseReasonEntityDao.updateStateById(entityId, null,  relationIds);

        reasonCommonBiz.addOperateEntityLog(userId,  entityId, getEditChoiceRelationIdLog(entity.getChoiceRelationIds(),
                relationIds));
    }

    private String getEditChoiceRelationIdLog(String oldRelationIds, String newRelationIds) {
        return "编辑联动ID: \"" + oldRelationIds + "\"修改为 \"" + newRelationIds + "\"";
    }

    @Override
    public List<CfRefuseReasonEntity> queryAllRelationEntitys(int entityId) {
        CfRefuseReasonEntity entity = cfRefuseReasonEntityDao.selectByIdAndDeleteStatus(entityId, null);

        List<CfRefuseReasonEntity> allRelationEntitys = Lists.newArrayList();
        if (entity == null || StringUtils.isEmpty(entity.getChoiceRelationIds())) {
            return allRelationEntitys;
        }

        allRelationEntitys =cfRefuseReasonEntityDao.selectByReasonIds(Sets.newHashSet(reasonCommonBiz.getIdListSplitterByComma(entity.getChoiceRelationIds())),
                null);
        for (CfRefuseReasonEntity reasonEntity : allRelationEntitys) {
            reasonEntity.setRefuseReasonItems(buildRefuseReasonItem(reasonEntity.getItemIds()));
        }

        return allRelationEntitys;
    }

    @Override
    public void addReasonEntity(int userId, CfRefuseReasonEntity reasonEntity, String useSceneIds, int riskLabelRelated, String riskLabelIdList) {

        LOGGER.info("添加驳回理由. userId:{}, entity:{}, useSceneIds:{}", userId, reasonEntity, useSceneIds);
        if (StringUtils.isEmpty(reasonEntity.getContent()) || StringUtils.isEmpty(useSceneIds)
            || StringUtils.isEmpty(reasonEntity.getItemIds())) {
            throw new RuntimeException("参数错误");
        }

        CfRefuseReasonTag cfRefuseReasonTag = reasonTagBiz.selectById(reasonEntity.getTagId());
        if (cfRefuseReasonTag == null) {
            throw new RuntimeException("参数错误");
        }
        List<Integer> useSceneIdList = reasonCommonBiz.getIdListSplitterByComma(useSceneIds);

        // 初审的驳回的位置 唯一
//        if (useSceneIdList.contains(CfRefuseReasonEntity.RejectOptionUseSceneEnum.INITIAL_AUDIT.getCode()) &&
//                reasonEntity.getItemIds().contains(",")) {
//            throw new RuntimeException("初审 和 材料审核-基本信息 增信信息 的驳回理由-驳回位置必须确定唯一");
//        }
        // 新增的理由 置为弃用状态
        reasonEntity.setIsDelete(InitialAuditOperationItem.RejectOperation.DISABLE.getCode());
        insertOne(reasonEntity);
        int reasonId = reasonEntity.getId();
        String oldReasonIds = cfRefuseReasonTag.getReasonIds();
        Set<String> reasonIds = Sets.newHashSet();
        if (StringUtils.isNotBlank(oldReasonIds)) {
            reasonIds = Sets.newHashSet(Arrays.asList(oldReasonIds.split(",")));
        }
        reasonIds.add(String.valueOf(reasonId));
        String reasonString = String.join(",", reasonIds);
        reasonTagBiz.updateReasonIds(reasonString, reasonEntity.getTagId());

        rankMappingBiz.addUseSceneRankMapping(reasonEntity, useSceneIdList, false);

        reasonCommonBiz.addOperateEntityLog(userId, reasonEntity.getId(),
                CfRefuseReasonEntity.CfReasonEntityOperateEnum.ADD_ENTITY.getDesc());

        refuseEntityRelateRiskLabel(reasonId, riskLabelRelated, riskLabelIdList);
    }

    /**
     * 驳回项和风险标签关联
     *
     * @param reasonId 驳回项Id
     * @param riskLabelRelated 0：无需关联；1：自动关联；2：人工关联
     * @param riskLabelIdList 风险标签Ids
     */
    private void refuseEntityRelateRiskLabel(int reasonId, int riskLabelRelated, String riskLabelIdList) {
        if (reasonId == 0 || StringUtils.isEmpty(riskLabelIdList)) {
            log.info("refuseEntityRelateRiskLabel param is limit");
            return;
        }
        Splitter.on(",")
                .splitToList(riskLabelIdList)
                .stream()
                .map(Long::valueOf)
                .forEach(f -> {
                    CfRefuseReasonEntityRiskLabel refuseReasonEntityRiskLabel = new CfRefuseReasonEntityRiskLabel();
                    refuseReasonEntityRiskLabel.setRefuseEntityId(reasonId);
                    refuseReasonEntityRiskLabel.setRiskLabelRelated(riskLabelRelated);
                    refuseReasonEntityRiskLabel.setRiskLabelId(f);
                    cfRefuseReasonEntityRiskLabelDao.insert(refuseReasonEntityRiskLabel);
                });
    }

    @Override
    public void editEntityDeleteStatus(int userId, int entityId, int deleteStatus) {

        log.info("驳回项配置的状态修改: userId:{}, entityId:{}, deleteStatus:{}", userId, entityId, deleteStatus);

        CfRefuseReasonEntity entity = selectById(entityId, null);
        CfRefuseReasonTag reasonTag = reasonTagBiz.selectByTagId(entity.getTagId());

        reasonCommonBiz.validateEditEntityDeleteStatus(entity, reasonTag, deleteStatus);

        cfRefuseReasonEntityDao.updateStateById(entityId, deleteStatus, null);
        reasonTagBiz.updateReasonIds(reasonCommonBiz.getNewReasonEntityIds(reasonTag, entityId, deleteStatus), reasonTag.getId());
        rankMappingBiz.updateUseSceneRankMapping(entity, deleteStatus);
        reasonCommonBiz.addOperateEntityLog(userId, entityId, CfRefuseReasonEntity.CfReasonEntityOperateEnum.getByDeleteStatus(
                InitialAuditOperationItem.RejectOperation.codeOf(deleteStatus)).getDesc());
    }

    @Override
    public void editEntityRank(int userId, int upId, int downId, int operateType, int useScene) {

        log.info("上移或下移驳回理由。userId:{} upId:{}  downId:{} operateType:{} useScene:{}",
                userId, upId, downId, operateType, useScene);

        rankMappingBiz.swapRankMapping(upId, downId, useScene);

        reasonCommonBiz.addOperateEntityLog(userId, operateType ==  AdminSmsTemplateSettingsInfo.SmsOperateType.UP.getCode()
                ? upId : downId,
                operateType ==  AdminSmsTemplateSettingsInfo.SmsOperateType.UP.getCode() ? "上移" : "下移");
    }

    @Override
    public List<CfRefuseReasonEntity> queryRefuseEntityByDelStatus(int tagId, int deleteStatus, int useScene, boolean rejectPage) {

        List<CfRefuseReasonEntity> entityList = selectByTagIdAndDeleteStatus(tagId, deleteStatus);
        if (CollectionUtils.isEmpty(entityList) ) {
            return entityList;
        }


        entityList = rankMappingBiz.filterUseSceneSortEntityList(entityList, useScene);

        // 驳回理由
        cfRefuseReasonItemBiz.fillReasonEntityItem(entityList);
        // 使用场景
        rankMappingBiz.fillReasonEntityUseSceneIds(entityList);

        // 失效 或 有效的关联id
        fillValidRelationIds(entityList);

        // 修改位置的文案
        fillSuggestModifyText(entityList);

        //风险标签关联
        fillRelateRiskLabel(entityList);

        return entityList;
    }

    private void fillRelateRiskLabel(List<CfRefuseReasonEntity> entityList) {
        List<Integer> entityIdList = entityList.stream()
                .map(CfRefuseReasonEntity::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(entityIdList)) {
            return;
        }
        List<CfRefuseReasonEntityRiskLabel> reasonEntityRiskLabelList = cfRefuseReasonEntityRiskLabelDao.selectByRefuseEntityIdList(entityIdList);
        if (CollectionUtils.isEmpty(reasonEntityRiskLabelList)) {
            return;
        }
        List<Long> labelIdList = reasonEntityRiskLabelList.stream()
                .map(CfRefuseReasonEntityRiskLabel::getRiskLabelId)
                .collect(Collectors.toList());
        List<Label> labelList = riskLabelService.getLabelByIds(labelIdList);
        Map<Long, Label> labelMap = labelList.stream()
                .collect(Collectors.toMap(Label::getId, Function.identity(), (x, y) -> y));
        reasonEntityRiskLabelList.forEach(f -> {
            Label label = labelMap.get(f.getRiskLabelId());
            if (Objects.nonNull(label)) {
                f.setRiskLabel(label.getPathName() + "/" + label.getName());
                f.setParentRiskLabelId(label.getParentPath());
            }
        });
        Map<Integer, List<CfRefuseReasonEntityRiskLabel>> refuseReasonEntityRiskLabelMap = reasonEntityRiskLabelList.stream()
                .collect(Collectors.groupingBy(CfRefuseReasonEntityRiskLabel::getRefuseEntityId));
        entityList.forEach(f -> {
            List<CfRefuseReasonEntityRiskLabel> refuseReasonEntityRiskLabel = refuseReasonEntityRiskLabelMap.get(f.getId());
            if (CollectionUtils.isNotEmpty(refuseReasonEntityRiskLabel)) {
                f.setRiskLabelList(refuseReasonEntityRiskLabel);
            }
        });
    }

    private void fillSuggestModifyText(List<CfRefuseReasonEntity> entityList) {
        if (CollectionUtils.isEmpty(entityList) ) {
            return;
        }

        Set<Integer> allModifyIds = Sets.newHashSet();
        entityList.forEach(item->{ allModifyIds.addAll(AdminWorkOrderFlow.getIdListSplitterByComma(item.getSuggestModifyIds()));});
        if (CollectionUtils.isEmpty(allModifyIds)) {
            return;
        }
        Map<Integer, CfRefuseSuggestModify> suggestMapping = suggestModifyBiz.selectSuggestMapping(allModifyIds);
        for (CfRefuseReasonEntity entity : entityList) {
            if (StringUtils.isBlank(entity.getSuggestModifyIds())) {
                continue;
            }
            List<String> contents = Lists.newArrayList();

            for (int suggestId : AdminWorkOrderFlow.getIdListSplitterByComma(entity.getSuggestModifyIds())) {
                CfRefuseSuggestModify modify = suggestMapping.get(suggestId);
                if (modify == null) {
                    continue;
                }
                contents.add(modify.getContent());
            }
            entity.setSuggestModifyText(Joiner.on(",").join(contents));
        }
    }

    private void fillValidRelationIds(List<CfRefuseReasonEntity> entityList) {

        if (CollectionUtils.isEmpty(entityList)) {
            return;
        }

        Set<Integer> allRelationIds = Sets.newHashSet();
        for (CfRefuseReasonEntity entity : entityList) {
            allRelationIds.addAll(reasonCommonBiz.getIdListSplitterByComma(entity.getChoiceRelationIds()));
            if (CollectionUtils.isNotEmpty(entity.getRefuseReasonItems())) {
                entity.setTotalRefuseReasonItems(Sets.newHashSet(entity.getRefuseReasonItems()));
            }
        }
        if (CollectionUtils.isEmpty(allRelationIds)) {
            return;
        }

        List<CfRefuseReasonEntity> allRelationEntitys = cfRefuseReasonEntityDao.selectByReasonIds(allRelationIds,
                null);

        Map<Integer, CfRefuseReasonEntity> id2EntityMap = Maps.newHashMap();
        for (CfRefuseReasonEntity relationEntity : allRelationEntitys) {
            if (relationEntity.getIsDelete() == 0) {
                id2EntityMap.put(relationEntity.getId(), relationEntity);
            }
        }

        // 所有的驳回理由
        for (CfRefuseReasonEntity entity : entityList) {
            List<Integer> relationIds = reasonCommonBiz.getIdListSplitterByComma(entity.getChoiceRelationIds());

            List<Integer> noDeleteEntityIds = Lists.newArrayList();
            for (Integer relation : relationIds) {
                if (id2EntityMap.containsKey(relation)) {
                    noDeleteEntityIds.add(relation);
                }
            }

            Map<Integer, Set<Integer>> relationIdGroupByUseScene = queryRelationIdGroupByUseScene(entity.getId(), noDeleteEntityIds);
            Set<Integer> validEntityIds = Sets.newHashSet();
            for (Set<Integer> relations : relationIdGroupByUseScene.values()) {
                validEntityIds.addAll(relations);
            }

            entity.setValidRelationIds(validEntityIds);
            entity.setUseSceneRelationIds(relationIdGroupByUseScene);
            entity.setInValidRelationIds(relationIds.stream().filter(item -> !validEntityIds.contains(item))
                    .collect(Collectors.toSet()));

//            fillRelationItem(entity, validEntityIds, id2EntityMap);
        }
    }

    private Map<Integer, Set<Integer>> queryRelationIdGroupByUseScene(int entityId, List<Integer> relationIds) {

        Map<Integer, Set<Integer>> relationIdByUseSceneMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(relationIds)) {
            return relationIdByUseSceneMap;
        }
        List<CfRefuseReasonEntity.CfRefuseReasonUseSceneRankMapping> allRankMapping = rankMappingBiz.selectByEntityId(entityId);

        for (CfRefuseReasonEntity.CfRefuseReasonUseSceneRankMapping rankMapping : allRankMapping) {
            List<CfRefuseReasonEntity.CfRefuseReasonUseSceneRankMapping> currMapping = rankMappingBiz
                    .selectByEntityIdsAndUseScene(relationIds, rankMapping.getUseScene());
            if (CollectionUtils.isNotEmpty(currMapping)) {
                relationIdByUseSceneMap.put(rankMapping.getUseScene(), currMapping.stream()
                        .map(CfRefuseReasonEntity.CfRefuseReasonUseSceneRankMapping::getReasonEntityId).collect(Collectors.toSet()));
            }
        }

        return relationIdByUseSceneMap;
    }

    private void fillRelationItem(CfRefuseReasonEntity entity, Set<Integer> validRelationIds, Map<Integer, CfRefuseReasonEntity> id2EntityMap) {

        Set<String> allItemList = Sets.newHashSet();
        allItemList.addAll(entity.getRefuseReasonItems());


        Set<Integer> allItemIds = Sets.newHashSet();
        for (Integer entityIds : validRelationIds) {
            allItemIds.addAll(reasonCommonBiz.getIdListSplitterByComma(id2EntityMap.get(entityIds).getItemIds()));
        }

        cfRefuseReasonItemBiz.selectByIds(allItemIds).stream().forEach(item -> allItemList.add(item.getContent()));

        entity.setTotalRefuseReasonItems(allItemList);
    }

    @Override
    public List<CfRefuseReasonTag> getRefuseListByType(int dataType, int useScene) {

        List<CfRefuseReasonTag> cfRefuseReasonTags = reasonTagBiz.selectByDataType(dataType);
        if (CollectionUtils.isEmpty(cfRefuseReasonTags)) {
            return cfRefuseReasonTags;
        }

        for (CfRefuseReasonTag reasonTag : cfRefuseReasonTags) {
            reasonTag.setEntityList(queryRefuseEntityByDelStatus( reasonTag.getId(),  0, useScene, false));
        }

        return cfRefuseReasonTags;
    }

    @Override
    public List<CfRefuseSuggestModify> querySuggestByEntityIds(List<Integer> entityIds) {
        if (CollectionUtils.isEmpty(entityIds)) {
            return Lists.newArrayList();
        }
        List<CfRefuseReasonEntity> allEntity = cfRefuseReasonEntityDao.selectByIds(entityIds);
        Set<Integer> uniqueIds = Sets.newHashSet();

        for (CfRefuseReasonEntity entity : allEntity) {
            uniqueIds.addAll(reasonCommonBiz.getIdListSplitterByComma(entity.getSuggestModifyIds()));
        }

        return suggestModifyBiz.selectSuggestModifyByIds(Lists.newArrayList(uniqueIds));
    }

    @Override
    public AuditSuggestModifyDetail querySuggestModifyDetail(AuditSuggestModifyParam param) {
        if (param == null || CollectionUtils.isEmpty(param.getEntityIds())) {
            throw new RuntimeException("参数错误");
        }

        AuditSuggestModifyDetail modifyDetail = new AuditSuggestModifyDetail();

        List<CfRefuseReasonEntity> entityReasons = cfRefuseReasonEntityDao.selectByReasonIds(param.getEntityIds(), 0);
        if (CollectionUtils.isEmpty(entityReasons)) {
            throw new RuntimeException("刷新页面，重新驳回");
        }

        Pair<Set<Integer>, Map<Integer, Set<Integer>>> result = getValidPositionIds(entityReasons, param.getUniqueIds());
        modifyDetail.setRejectTModify(result.getRight());

        Set<String> content = getRejectPositionContent(result.getLeft());
        content.addAll(getSuggestModifyContent(param.getUniqueIds()));
        modifyDetail.setSuggestUserModify(AuditSuggestModifyDetail
                .getFormatSuggestView(Joiner.on("、").join(content)));

        return modifyDetail;
    }

    private Pair<Set<Integer>, Map<Integer, Set<Integer>>> getValidPositionIds(List<CfRefuseReasonEntity> entityReasons,
                                                                               Set<Integer> uniqueIds) {
        Set<Integer> allItemIds = Sets.newHashSet();
        Map<Integer, Set<Integer>> rejectTModify = Maps.newHashMap();

        for (CfRefuseReasonEntity entity : entityReasons) {
            if (StringUtils.isBlank(entity.getItemIds())) {
                log.error("驳回理由，没有带C端驳回位置.param:{}", JSON.toJSONString(entity));
                continue;
            }

            Set<Integer> relationModifyIds = getRejectHasModifyIds(entity, uniqueIds);
            if (CollectionUtils.isNotEmpty(relationModifyIds)) {
                rejectTModify.put(entity.getId(), relationModifyIds);
                continue;
            }
            allItemIds.addAll(reasonCommonBiz.getIdListSplitterByComma(entity.getItemIds()));

        }

        return Pair.of(allItemIds, rejectTModify);
    }

    private Set<String> getRejectPositionContent(Set<Integer> allItemIds) {
        List<CfRefuseReasonItem> reasonItems = cfRefuseReasonItemBiz.selectByIds(allItemIds);

        Set<String> content = Sets.newHashSet();
        for (CfRefuseReasonItem item : reasonItems) {
            if (StringUtils.isBlank(item.getContent())) {
                log.error("驳回位置没有具体的文案。msg:{}", JSON.toJSONString(item));
                continue;
            }
            content.add(item.getContent());
        }

        return content;
    }

    private Set<String> getSuggestModifyContent(Collection<Integer> modifyIds) {

        Set<String> content = Sets.newHashSet();

        if (CollectionUtils.isEmpty(modifyIds)) {
            return Sets.newHashSet();
        }

        List<CfRefuseSuggestModify> suggestViews = suggestModifyBiz.selectSuggestModifyByIds(modifyIds);
        for (CfRefuseSuggestModify modify : suggestViews) {
            content.add(modify.getContent());
        }

        return content;
    }

    private Set<Integer> getRejectHasModifyIds(CfRefuseReasonEntity entity, Set<Integer> uniqueIds) {
        Set<Integer> modifyIds = Sets.newHashSet();
        if (StringUtils.isBlank(entity.getSuggestModifyIds()) || CollectionUtils.isEmpty(uniqueIds)) {
            return modifyIds;
        }

        List<Integer> currModifyIds = reasonCommonBiz.getIdListSplitterByComma(entity.getSuggestModifyIds());
        for (Integer modifyId : currModifyIds) {
            if (uniqueIds.contains(modifyId)) {
                 modifyIds.add(modifyId);
            }
        }

        return modifyIds;
    }

    @Override
    public Set<Integer> queryDataTypeByRejectIds(List<Integer> rejectIds) {
        List<CfRefuseReasonEntity> reasonEntities = selectByIds(rejectIds);
        if  (CollectionUtils.isEmpty(reasonEntities)) {
            return Sets.newHashSet();
        }

        Set<Integer> tagIds = reasonEntities.stream().map(CfRefuseReasonEntity::getTagId).collect(Collectors.toSet());

        return reasonTagBiz.selectByTagIds(tagIds).stream().map(CfRefuseReasonTag::getDataType).collect(Collectors.toSet());
    }

    @Override
    public int insertCfRefuseReasonCustomEntity(CfRefuseReasonCustomEntity cfRefuseReasonCustomEntity) {
        return cfRefuseReasonCustomEntityDao.insertOne(cfRefuseReasonCustomEntity);
    }

    @Override
    public List<CfRefuseReasonCustomEntity> getCustomByCaseId(int caseId) {
        return cfRefuseReasonCustomEntityDao.selectByCaseId(caseId);
    }

    @Override
    public List<CfRefuseReasonCustomEntity> getCustomByWorkOrderId(long workOrderId) {
        return cfRefuseReasonCustomEntityDao.selectByWorkOrderId(workOrderId);
    }

    @Override
    public int updateStatusByCaseIdAndRefuseId(CfRefuseReasonCustomEntity cfRefuseReasonCustomEntity) {
        return cfRefuseReasonCustomEntityDao.updateStatusByCaseIdAndRefuseId(cfRefuseReasonCustomEntity);
    }

    @Override
    public List<Integer> getRefuseReasonEntitiesByMaterialPlanId(int materialPlanId) {
        return refuseReasonEntityVersionMappingDao.getEntityIdsByMaterialPlanId(materialPlanId);
    }

    @Override
    public Map<String, Integer> getMaterialPlanIdsOfEntities(List<Integer> entityIds) {
        if (CollectionUtils.isEmpty(entityIds)) {
            return new HashMap<>();
        }
        List<Map<String, Object>> mapping = refuseReasonEntityVersionMappingDao.getMaterialPlanId(entityIds);
        Map<String, Integer> res = new HashMap<>();
        mapping.forEach(o -> {
            res.put(String.valueOf(o.get("refuse_reason_entity_id")), (Integer) o.get("material_plan_id"));
        });
        return res;
    }

    @Override
    public boolean bindEntityToMaterial(int entityId, int materialPlanId) {
        if (entityId <= 0) {
            return false;
        }
        int inserted = refuseReasonEntityVersionMappingDao.insertOne(entityId, materialPlanId);
        if (inserted != 1) {
            log.warn("驳回理由和案例材料版本绑定失败：entityId: {}, materialPlanId: {}", entityId, materialPlanId);
        }
        return inserted == 1;
    }

    @Override
    public boolean bindEntityToMaterialBatch(Map<String, String> mapping) {
        if (MapUtils.isEmpty(mapping)) {
            return false;
        }
        Map<Integer, Integer> transFormedMapping = new HashMap<>();
        mapping.forEach((key, value) -> transFormedMapping.put(Integer.parseInt(key), Integer.parseInt(value)));
        int inserted = refuseReasonEntityVersionMappingDao.batchInsert(transFormedMapping);
        if (inserted <= 0) {
            log.warn("驳回理由和案例材料版本批量绑定失败, mapping: {}", mapping);
        }
        return inserted > 0;
    }

    @Override
    public boolean deleteBindingRelation(int id) {
        if (id <= 0) {
            return false;
        }
        int deleted = refuseReasonEntityVersionMappingDao.deleteOne(id);
        if (deleted <= 0) {
            log.warn("删除驳回理由和案例材料版本绑定关系失败, id: {}", id);
        }
        return deleted == 1;
    }

    @Override
    public boolean updateBindingRelation(int id, int newPlanId, int newEntityId) {
        if (id <= 0 || newEntityId <= 0) {
            return false;
        }

        int updated = refuseReasonEntityVersionMappingDao.updateOne(id, newPlanId, newEntityId);
        if (updated <= 0) {
            log.warn("更改驳回理由和案例材料版本绑定关系失败, id: {}, newPlanId: {}, newEntityId: {}", id, newPlanId, newEntityId);
        }
        return updated == 1;
    }
}



































