package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReportRecord;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingReportRecordVo;

import java.util.List;

/**
 * Created by <PERSON>ejiangnan on 2017/8/5.
 */
public interface CrowdfundingReportRecordBiz {

    int add(int reportId,int operationId,int dealStatus,String comment);

    List<CrowdfundingReportRecord> getreportRecordListByReportIds(List<Integer> reportIds);

    List<CrowdfundingReportRecord> getreportRecordList();

    List<CrowdfundingReportRecordVo> getReportRecordGroupByReportId(List<Integer> reportIds);

}
