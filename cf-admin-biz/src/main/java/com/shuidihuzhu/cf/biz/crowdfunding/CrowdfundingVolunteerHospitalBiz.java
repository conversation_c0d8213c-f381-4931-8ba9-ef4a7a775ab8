package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingVolunteerHospital;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/2/28.
 */
public interface CrowdfundingVolunteerHospitalBiz {
    /**
     * 插入医院信息
     *
     * @param crowdfundingVolunteerHospital
     * @return
     */
    int insertHospital(CrowdfundingVolunteerHospital crowdfundingVolunteerHospital);

    /**
     * 查询城市下的所有医院
     *
     * @param cityId
     * @return
     */
    List<CrowdfundingVolunteerHospital> getHospitalName(@Param("cityId") int cityId);


}
