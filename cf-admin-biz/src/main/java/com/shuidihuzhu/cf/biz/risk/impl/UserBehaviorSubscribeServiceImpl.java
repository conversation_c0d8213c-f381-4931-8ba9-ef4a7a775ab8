package com.shuidihuzhu.cf.biz.risk.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.risk.IUserBehaviorService;
import com.shuidihuzhu.cf.delegate.service.WxSubscribeEventServiceBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.UserBehaviorEnum;
import com.shuidihuzhu.cf.model.crowdfunding.AdminUserBehaviorDetail;
import com.shuidihuzhu.cf.model.crowdfunding.UserInfoDetail;
import com.shuidihuzhu.msg.enums.AccountThirdTypeEnum;
import com.shuidihuzhu.wx.grpc.client.WxSubscribeModel;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2019/10/11 下午5:27
 * @desc 关注行为
 */
@Service
public class UserBehaviorSubscribeServiceImpl implements IUserBehaviorService {

    @Resource
    private WxSubscribeEventServiceBiz wxSubscribeEventServiceBiz;

    @Override
    public UserBehaviorEnum getBehavior() {
        return UserBehaviorEnum.SUBSCRIBE;
    }

    @Override
    public List<AdminUserBehaviorDetail> buildBehaviorDetail(String mobile, long userId, String name, String identity, UserInfoModel userInfo, UserBehaviorEnum behaviorEnum) {

        List<Integer> thirdTypes = Lists.newArrayList();
        for (AccountThirdTypeEnum thirdTypeEnum : AccountThirdTypeEnum.values()){
            if(thirdTypeEnum == AccountThirdTypeEnum.EMPTY){
                continue;
            }
            thirdTypes.add(thirdTypeEnum.getCode());
        }

        Map<Long, List<WxSubscribeModel>> subscribeMap = wxSubscribeEventServiceBiz.batchCheckSubscribeByUserIds(Lists.newArrayList(userId), thirdTypes);
        if(MapUtils.isEmpty(subscribeMap) || CollectionUtils.isEmpty(subscribeMap.get(userId))){
            return Lists.newArrayList();
        }

        List<WxSubscribeModel> subscribeModels = subscribeMap.get(userId);

        List<AdminUserBehaviorDetail> subscribeDetails = Lists.newArrayList();
        for (WxSubscribeModel subscribeModel : subscribeModels){
            UserInfoDetail userInfoDetail = new UserInfoDetail();
            userInfoDetail.setOpenId(subscribeModel.getOpenId());

            AccountThirdTypeEnum subThirdType = AccountThirdTypeEnum.valueOf(subscribeModel.getThirdType());

            StringBuilder sb = new StringBuilder();
            sb.append("关注").append(Objects.nonNull(subThirdType) ? subThirdType.getDesc() : "").append(REDEX);

            AdminUserBehaviorDetail subscribeDetail = new AdminUserBehaviorDetail();
            subscribeDetail.setTime(new Date(subscribeModel.getSubscribeTime()));
            subscribeDetail.setBehaviorType(UserBehaviorEnum.SUBSCRIBE.getKey());
            subscribeDetail.setUrl(Lists.newArrayList());
            subscribeDetail.setUserInfoDetail(userInfoDetail);
            subscribeDetail.setBehavoir(Lists.newArrayList(sb.toString().split(REDEX)));

            subscribeDetails.add(subscribeDetail);
        }

        return subscribeDetails;
    }
}
