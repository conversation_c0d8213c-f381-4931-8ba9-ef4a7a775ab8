package com.shuidihuzhu.cf.biz.admin.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.shuidihuzhu.cf.biz.admin.UserCommentBiz;
import com.shuidihuzhu.cf.dao.admin.UserCommentDao;
import com.shuidihuzhu.cf.model.admin.UserComment;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2018/7/31
 */
@Service
@Slf4j
public class UserCommentBizImpl implements UserCommentBiz {

    @Autowired
    private UserCommentDao commentDao;

    @Override
    public int add(UserComment userComment) {
        return commentDao.add(userComment);
    }

    @Override
    public int insert(UserComment userComment) {
        return commentDao.insert(userComment);
    }

    @Override
    public int addList(List<UserComment> userComments) {
        //增加空判断
        if (CollectionUtils.isEmpty(userComments)){
            return 0;
        }

        return commentDao.addList(userComments);
    }

    @Override
    public List<UserComment> getUserComment(long caseId, int commentSource) {
        return getUserComment(caseId,commentSource,0);
    }


    @Override
    public List<UserComment> getUserComment(long caseId, int commentSource, int commentType) {
        return commentDao.getUserComment(caseId,commentSource,commentType);
    }

    @Override
    public int countByCommentSoure(long caseId, int commentSource) {
        return commentDao.countByCommentSoure(caseId, commentSource);
    }

    @Override
    public List<UserComment> getUserCommentDescByCommentSource(long caseId, int commentSource,
                                                    int start, int size) {
        return commentDao.getUserCommentDescByCommentSource(caseId, commentSource, start, size);
    }

    @Override
    public String formatHitWordsTDisplay(String jsonWords) {
        if (StringUtils.isBlank(jsonWords)) {
            return "";
        }

        List<String> words = null;
        try {
            words = JSON.parseArray(jsonWords, String.class);
        } catch (Exception e) {
            log.info("json解析敏感词错误。word:{} ", jsonWords, e);
        }
        return org.apache.commons.collections.CollectionUtils.isEmpty(words) ? "" : Joiner.on(",").join(words);
    }
}
