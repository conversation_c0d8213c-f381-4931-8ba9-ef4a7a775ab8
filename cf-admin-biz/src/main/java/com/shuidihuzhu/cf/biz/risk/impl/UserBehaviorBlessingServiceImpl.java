package com.shuidihuzhu.cf.biz.risk.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.biz.risk.IUserBehaviorService;
import com.shuidihuzhu.cf.enums.crowdfunding.UserBehaviorEnum;
import com.shuidihuzhu.cf.model.crowdfunding.AdminUserBehaviorDetail;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoBlessing;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.UserInfoDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


@Service
@Slf4j
public class UserBehaviorBlessingServiceImpl implements IUserBehaviorService {


    @Autowired
    private AdminCfInfoExtBiz infoExtBiz;

    @Override
    public UserBehaviorEnum getBehavior() {
        return UserBehaviorEnum.BLESSING_RECORD;
    }


    @Override
    public List<AdminUserBehaviorDetail> buildBehaviorDetail(String mobile, long userId, String name, String identity, UserInfoModel userInfo, UserBehaviorEnum behaviorEnum) {


        List<AdminUserBehaviorDetail> behaviors = Lists.newArrayList();

        if (userId == 0) {
            return behaviors;
        }

        List<CfInfoBlessing> result = null;
        if (CollectionUtils.isEmpty(result)) {
            return behaviors;
        }

        UserInfoDetail userDetail = new UserInfoDetail();
        userDetail.setUserId(userId);
        userDetail.setNickName(Objects.nonNull(userInfo) ? userInfo.getNickname() : "");
        userDetail.setMobile(mobile);

        Set<String> infoUuids = result.stream().map(CfInfoBlessing::getInfoUuid)
                .collect(Collectors.toSet());

        Map<String, CfInfoExt> infoExtMap =  infoExtBiz.getMapByInfoUuids(Lists.newArrayList(infoUuids));
        Set<String> hasBlessing = Sets.newHashSet();
        for (CfInfoBlessing blessing : result) {
            CfInfoExt infoExt = infoExtMap.get(blessing.getInfoUuid());
            if (hasBlessing.contains(blessing.getInfoUuid()) || infoExt == null) {
                continue;
            }

            AdminUserBehaviorDetail behavior = new AdminUserBehaviorDetail();
            behavior.setTime(blessing.getLastModified());
            behavior.setBehaviorType(UserBehaviorEnum.BLESSING_RECORD.getKey());
            behavior.setUrl(Lists.newArrayList());
            behavior.setBehavoir(Lists.newArrayList("加油案例id: " + infoExt.getCaseId()));
            behavior.setUserInfoDetail(userDetail);
            behaviors.add(behavior);
        }

        return behaviors;
    }
}
