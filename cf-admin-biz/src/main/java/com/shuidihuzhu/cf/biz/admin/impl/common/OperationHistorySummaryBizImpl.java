package com.shuidihuzhu.cf.biz.admin.impl.common;

import com.shuidihuzhu.cf.biz.admin.common.OperationHistorySummaryBiz;
import com.shuidihuzhu.cf.dao.sd.admin.common.OperationHistorySummaryDao;
import com.shuidihuzhu.cf.enums.admin.common.OperationType;
import com.shuidihuzhu.cf.model.admin.common.OperationHistorySummary;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Author: wuxinlong
 * Date: 16/10/9 19:37
 */

@Service
public class OperationHistorySummaryBizImpl implements OperationHistorySummaryBiz {
	
	private static final Logger logger = LoggerFactory.getLogger(OperationHistorySummaryBizImpl.class);

	@Autowired
	private OperationHistorySummaryDao operationHistorySummaryDao;

	@Override
	public int insert(OperationHistorySummary operationHistorySummary) {
		return operationHistorySummaryDao.insert(operationHistorySummary);
	}

	@Override
	public void addOperationHistorySummary(OperationType operationType, long operatorId, String summary) {
		//FIXME 这里强转的，过后check
		OperationHistorySummary operationHistorySummary =
				new OperationHistorySummary(operationType, (int)operatorId, summary);
		operationHistorySummaryDao.insert(operationHistorySummary);
	}
}
