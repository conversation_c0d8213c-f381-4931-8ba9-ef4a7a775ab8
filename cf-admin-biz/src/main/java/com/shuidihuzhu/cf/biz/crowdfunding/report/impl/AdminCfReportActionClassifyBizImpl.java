package com.shuidihuzhu.cf.biz.crowdfunding.report.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.report.AdminCfReportActionClassifyBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfReportActionClassifyDao;
import com.shuidihuzhu.cf.model.report.CfReportActionClassify;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/4/17
 */
@Service
public class AdminCfReportActionClassifyBizImpl implements AdminCfReportActionClassifyBiz {
    @Autowired
    private AdminCfReportActionClassifyDao adminCfReportActionClassifyDao;

    @Override
    public int add(String actionClassify) {
        if (StringUtils.isBlank(actionClassify)) {
            return 0;
        }
        return adminCfReportActionClassifyDao.add(actionClassify);
    }

    @Override
    public int updateActionClassify(String actionClassify, long id) {
        if (StringUtils.isBlank(actionClassify)) {
            return 0;
        }
        return adminCfReportActionClassifyDao.updateActionClassify(actionClassify, id);
    }

    @Override
    public int updateIsUse(int isUse, long id) {
        if (isUse < 0 || id < 0){
            return 0;
        }
        return adminCfReportActionClassifyDao.updateIsUse(isUse, id);
    }

    @Override
    public List<CfReportActionClassify> getAll() {
        return adminCfReportActionClassifyDao.getAll();
    }

    @Override
    public List<CfReportActionClassify> getByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)){
            return Lists.newArrayList();
        }
        return adminCfReportActionClassifyDao.getByIds(ids);
    }

    @Override
    public List<CfReportActionClassify> getByIdsAndIsUse(List<Long> ids, int isUse) {
        if (CollectionUtils.isEmpty(ids)){
            return Lists.newArrayList();
        }
        return adminCfReportActionClassifyDao.getByIdsAndIsUse(ids, isUse);
    }

    @Override
    public List<CfReportActionClassify> getByUse(int isUse) {
        return adminCfReportActionClassifyDao.getByUse(isUse);
    }

    @Override
    public CfReportActionClassify getById(long id) {
        return adminCfReportActionClassifyDao.getById(id);
    }
}
