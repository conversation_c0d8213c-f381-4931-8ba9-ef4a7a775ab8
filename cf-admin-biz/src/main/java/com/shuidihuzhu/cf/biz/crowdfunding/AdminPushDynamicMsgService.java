package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.enums.admin.DynamicMsgEnum;
import com.shuidihuzhu.cf.model.admin.CfPushDynamicMsgModel;
import com.shuidihuzhu.cf.model.admin.vo.CfDynamicMsgVo;
import com.shuidihuzhu.common.web.model.Response;
import org.springframework.scheduling.annotation.Async;

/**
 * @package: com.shuidihuzhu.cf.biz.crowdfunding
 * @Author: liujiawei
 * @Date: 2020-01-09  11:07
 */
public interface AdminPushDynamicMsgService {
    @Async
    void pushDynamicMsg(int caseId,
                        DynamicMsgEnum.PushMsgType pushMsgType,
                        int supplyProgressId,
                        int supplyActionId,
                        long workOrderId);

    void executeDynamicMsg(CfPushDynamicMsgModel cfPushDynamicMsgModel);

    void executeUploadDynamicInfo(CfDynamicMsgVo cfDynamicMsgVo);

    Response pauseDrawCashByDynamic(int caseId, int operatorId);

    Response recoverPauseDrawCashByDynamic(int caseId);

}
