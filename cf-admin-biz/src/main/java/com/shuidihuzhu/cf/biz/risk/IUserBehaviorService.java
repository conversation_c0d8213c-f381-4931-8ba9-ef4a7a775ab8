package com.shuidihuzhu.cf.biz.risk;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.enums.crowdfunding.UserBehaviorEnum;
import com.shuidihuzhu.cf.model.crowdfunding.AdminUserBehaviorDetail;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/10/11 下午5:24
 * @desc
 */
public interface IUserBehaviorService {
    String REDEX = "######";

    UserBehaviorEnum getBehavior();

    default boolean allowNoUserId(){
        return false;
    }

    List<AdminUserBehaviorDetail> buildBehaviorDetail(String mobile, long userId, String name, String identity, UserInfoModel userInfo, UserBehaviorEnum behaviorEnum);
}
