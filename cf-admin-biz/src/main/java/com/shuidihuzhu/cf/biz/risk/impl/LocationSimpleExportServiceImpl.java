package com.shuidihuzhu.cf.biz.risk.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.admin.util.admin.AdminProvinceUtil;
import com.shuidihuzhu.cf.biz.risk.ISimpleExportService;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingUserDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingTreatment;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.dataservice.faceApi.v1.FaceApiClient;
import com.shuidihuzhu.client.model.Response;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * Created by sven on 2019/6/26.
 *
 * <AUTHOR>
 */
@Service
public class LocationSimpleExportServiceImpl implements ISimpleExportService {

    private final static String RAISER_IP = "发起人最近一次访问水滴筹IP地址";
    private final static String RAISER_HOME = "发起人家乡";
    private final static String AUTHOR_TREATMENT_LOCATION = "患者就诊地";
    private final static String AUTHOR_HOMETOWN = "患者家乡地";
    private final static String RECIVER_HOMETOWN = "收款人家乡地区";
    private final static String RAISE_PHONE_PROVINCE = "发起人手机所在省";
    private final static String RAISE_PHONE_CITY = "发起人手机所在市";
    private final static String RECIVER_PHONE_LOCATION = "收款人手机所在地";
    private final static String CASE_DETAIL_HISTORY_PV = "案例详情页历史pv";
    private final static String WEEK_ACCESS_MOST_IP = "一周内最多访问ip地址";

    @Resource
    private FaceApiClient faceApiClient;

    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Resource
    private ICrowdfundingUserDelegate crowdfundingUserDelegate;

    @Autowired
    private ShuidiCipher shuidiCipher;

    @Override
    public Map<String, String> getDetail(CfInfoSimpleModel cfInfoSimpleModel) {
        CrowdfundingInfo info = crowdfundingDelegate.getCrowdfundingInfoByInfoId(cfInfoSimpleModel.getInfoId());

        String provinceName = "";
        if (info.getRelationType() != CrowdfundingRelationType.LOCATION_HOSPITAL_ACCOUNT) {
            String payeeIdcard = shuidiCipher.decrypt(info.getPayeeIdCard());
            provinceName = getProvinceName(payeeIdcard);
        }

        CrowdfundingTreatment crowdfundingTreatment = crowdfundingUserDelegate.getCrowdfundingTreatment(cfInfoSimpleModel.getId());
        String treatLoction = crowdfundingTreatment == null?"":crowdfundingTreatment.getHospitalName();

        String lastVisitIp = UNKOWN;
        Response<Map<String, String>> dsUserResult = faceApiClient.query(cfInfoSimpleModel.getUserId(), Lists.newArrayList("data_last_visit_ip"));
        if(Objects.nonNull(dsUserResult) && 0 == dsUserResult.getCode() && MapUtils.isNotEmpty(dsUserResult.getData())){
            lastVisitIp = StringUtils.isNotEmpty(dsUserResult.getData().get("data_last_visit_ip")) ? dsUserResult.getData().get("data_last_visit_ip") : UNKOWN;
        }

        String raiserPhoneProvince = UNKOWN;
        String raiserPhoneCity = UNKOWN;
        String caseHistoryPV = UNKOWN;
        String accessMostIp = UNKOWN;
        Response<Map<String, String>> dsCaseResult = faceApiClient.caseQuery(cfInfoSimpleModel.getInfoId(), Lists.newArrayList("data_cf_fqr_phone_province", "data_cf_fqr_phone_city", "data_cf_case_history_pv", "data_cf_week_access_most_ip"));
        if(Objects.nonNull(dsCaseResult) && 0 == dsCaseResult.getCode() && MapUtils.isNotEmpty(dsCaseResult.getData())){
            Map<String, String> resultMap = dsCaseResult.getData();
            raiserPhoneProvince = StringUtils.isNotEmpty(resultMap.get("data_cf_fqr_phone_province")) ? resultMap.get("data_cf_fqr_phone_province") : UNKOWN;
            raiserPhoneCity = StringUtils.isNotEmpty(resultMap.get("data_cf_fqr_phone_city")) ? resultMap.get("data_cf_fqr_phone_city") : UNKOWN;
            caseHistoryPV = StringUtils.isNotEmpty(resultMap.get("data_cf_case_history_pv")) ? resultMap.get("data_cf_case_history_pv") : UNKOWN;
            accessMostIp = StringUtils.isNotEmpty(resultMap.get("data_cf_week_access_most_ip")) ? resultMap.get("data_cf_week_access_most_ip") : UNKOWN;
        }


        Map<String, String> map = Maps.newHashMap();
        map.put(RAISER_IP, lastVisitIp);
        map.put(RAISER_HOME,UNKOWN);
        map.put(AUTHOR_TREATMENT_LOCATION, treatLoction);
        map.put(AUTHOR_HOMETOWN, UNKOWN);

        map.put(RECIVER_HOMETOWN, provinceName);

        map.put(RAISE_PHONE_PROVINCE, raiserPhoneProvince);
        map.put(RAISE_PHONE_CITY, raiserPhoneCity);
        map.put(RECIVER_PHONE_LOCATION, UNKOWN);
        map.put(CASE_DETAIL_HISTORY_PV, caseHistoryPV);
        map.put(WEEK_ACCESS_MOST_IP, accessMostIp);

        return map;
    }

    private String getProvinceName(String idCard) {
        if (StringUtils.isEmpty(idCard)) {
            return "";
        }
        String code = idCard.substring(0,2);
        if(StringUtils.isEmpty(code) || !StringUtils.isNumeric(code)){
            return StringUtils.EMPTY;
        }

        return AdminProvinceUtil.getProvinceBycode(Integer.valueOf(code));
    }

    @Override
    public String getCategory() {
        return "地域（省+市）";
    }

    @Override
    public int getOrder() {
        return 2;
    }
}
