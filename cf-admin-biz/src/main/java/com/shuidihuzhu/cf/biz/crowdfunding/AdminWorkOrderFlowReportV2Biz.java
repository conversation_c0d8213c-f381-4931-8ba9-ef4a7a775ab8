package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowStatistics;
import org.apache.commons.lang3.tuple.Pair;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Set;

public interface AdminWorkOrderFlowReportV2Biz {

    Pair<Integer, List<AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView>> countFlowReport(int orgId, Date beginTime, Date endTime, int level, List<Long> secondLevelClassifyIds, int memberType, int current, int pageSize, boolean averageOrderTime);

    Pair<Integer, List<AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView>>  countFlowReportByUserIds(int orgId, Date beginTime, Date endTime, int level, List<Long> secondLevelClassifyIds, int current, int pageSize, boolean averageOrderTime);

    Pair<Integer, List<AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView>> countFlowReportByOrgId(int orgId, Date beginTime, Date endTime, int level, List<Long> secondLevelClassifyIds, boolean averageOrderTime);

    Pair<Long, List<AdminWorkOrder>> countNoHandleMoreThan48Hour(int orgId, int level, List<Long> secondLevelClassifyIds);


    boolean downloadDataSummaryExcelV2(HttpServletResponse response, int orgId, Date beginTime, Date endTime, int level, List<Long> secondLevelClassifyIds, int memberType, long userId);

    boolean downloadDataDetailExcelV3(HttpServletResponse response, int orgId, Date beginTime, Date endTime, int level, List<Long> secondLevelClassifyIds, int memberType, int staffId, int dataType, long userId);
}
