package com.shuidihuzhu.cf.biz.admin;

import com.shuidihuzhu.cf.model.admin.UserComment;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2018/7/31
 */
public interface UserCommentBiz {


    int add(UserComment userComment);

    int insert(UserComment userComment);

    int addList(List<UserComment> userComments);

    List<UserComment> getUserComment(long caseId, int commentSource);

    List<UserComment> getUserComment(long caseId, int commentSource,int commentType);

    int countByCommentSoure(long caseId, int commentSource);

    List<UserComment> getUserCommentDescByCommentSource(long caseId, int commentSource,
                                                    int start, int size);

    String formatHitWordsTDisplay(String jsonWords);
}
