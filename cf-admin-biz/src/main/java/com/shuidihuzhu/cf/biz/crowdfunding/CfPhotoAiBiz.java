package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.admin.PhotoStatus;

/**
 * @package: com.shuidihuzhu.cf.biz.crowdfunding
 * @Author: l<PERSON>jiawei
 * @Date: 2018/7/27  18:22
 */
public interface CfPhotoAiBiz {

    PhotoStatus selectPhotoStatus(Integer infoId);


    PhotoStatus selectPhotoStatus(Integer infoId,int photoType);


    boolean checkAIPhotoExist(Integer infoId);

    int updateArtificialRes(int crowdfundingId, int result);
}
