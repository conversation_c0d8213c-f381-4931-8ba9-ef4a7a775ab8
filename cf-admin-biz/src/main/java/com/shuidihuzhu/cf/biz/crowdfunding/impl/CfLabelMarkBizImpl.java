package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.admin.util.admin.AdminCfIdCardUtil;
import com.shuidihuzhu.cf.biz.crowdfunding.CfLabelMarkBiz;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.dao.crowdfunding.CfLabelMarkDao;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.facade.AdminApolloCofig;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CfLabelRuleModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.admin.model.CaseLabel4QQ;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @DATE 2020/11/16
 */
@Service
@Slf4j
public class CfLabelMarkBizImpl implements CfLabelMarkBiz {

    @Autowired
    private IRiskDelegate riskDelegate;

    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private CfLabelMarkDao cfLabelMarkDao;

    @Autowired(required = false)
    private Producer producer;

    private final static Pattern pattern = Pattern.compile("学校|幼儿园|托儿所|小学|中学|大学|中专|大专|高职|职高|专科|本科|技校|学院|上学|下学|放学|学生会|研究生|博士|硕士|导师|老师|教师|校长|教导处|辅导员|学生|初中|高中|恩师");


    @Override
    public void addQQLabel(CrowdfundingInfo cfInfo) {

        String qqLabelSwitch = AdminApolloCofig.getValueFromApollo("qq_label_switch","111");

        char[] chars =qqLabelSwitch.toCharArray();

        CfFirsApproveMaterial material = riskDelegate.getCfFirsApproveMaterialByInfoId(cfInfo.getId());

        List<CfLabelRuleModel> ruleModels = Lists.newArrayList();
        if (chars[0]=='1'){
            //患者年龄
            if (UserIdentityType.identity.getCode() == material.getPatientIdType()){

                int age = AdminCfIdCardUtil.getAge(shuidiCipher.decrypt(material.getPatientCryptoIdcard()));

                if (18<=age && age<=26){
                    CfLabelRuleModel r1 = new CfLabelRuleModel();
                    r1.setCaseId(cfInfo.getId());
                    r1.setTime(new Date());
                    r1.setRuleName("规则1");
                    Map<String,String> map = Maps.newHashMap();
                    map.put("idCard",material.getPatientCryptoIdcard());
                    map.put("age",age+"");
                    r1.setRuleMsg(JSON.toJSONString(map));
                    ruleModels.add(r1);
                }
            }
        }

        if (chars[1] == '1'){
            String idcard = material.getSelfCryptoIdcard();
            if (material.getUserRelationType() == UserRelTypeEnum.SELF.getValue()){
                idcard = material.getPatientCryptoIdcard();
            }
            int age = AdminCfIdCardUtil.getAge(shuidiCipher.decrypt(idcard));

            if (18<=age && age<=26){
                CfLabelRuleModel r2 = new CfLabelRuleModel();
                r2.setCaseId(cfInfo.getId());
                r2.setTime(new Date());
                r2.setRuleName("规则2");
                Map<String,String> map = Maps.newHashMap();
                map.put("idCard",idcard);
                map.put("age",age+"");
                r2.setRuleMsg(JSON.toJSONString(map));
                ruleModels.add(r2);
            }
        }

        if (chars[2] == '1'){
            String title = hit(cfInfo.getTitle());
            String content = hit(cfInfo.getContent());
            if (StringUtils.isNotBlank(title) || StringUtils.isNotBlank(content)){
                CfLabelRuleModel r3 = new CfLabelRuleModel();
                r3.setCaseId(cfInfo.getId());
                r3.setTime(new Date());
                r3.setRuleName("规则3");
                Map<String,String> map = Maps.newHashMap();
                String keyWord = "";
                keyWord = joint(keyWord,title);
                keyWord = joint(keyWord,content);
                map.put("keyWord",keyWord);
                String local = "";
                if (StringUtils.isNotBlank(title)){
                   local = joint(local,"标题");
                }
                if (StringUtils.isNotBlank(content)){
                    local = joint(local,"文章");
                }
                map.put("local",local);
                r3.setRuleMsg(JSON.toJSONString(map));
                ruleModels.add(r3);
            }
        }

        if (CollectionUtils.isNotEmpty(ruleModels)){
            cfLabelMarkDao.insertList(ruleModels);
            notice(cfInfo.getId(),material.getPatientRealName());
        }
    }

    @Override
    public boolean hitQQLabel(int caseId) {
       int c = cfLabelMarkDao.hitQQLabel(caseId);
       if (c >0){
           return true;
       }
       return false;
    }

    private String joint(String msg, String joint){

        return msg+" "+joint;
    }


    private String hit (String msg){
        String hit = "";
        Matcher matcher = pattern.matcher(msg);
        while (matcher.find()){
            hit +=" "+matcher.group();
        }
        return hit;
    }


    private void notice(int caseId,String name){
        CaseLabel4QQ caseLabel4QQ = new CaseLabel4QQ();
        caseLabel4QQ.setCaseId(caseId);
        caseLabel4QQ.setName(name);
        Message message = Message.of(MQTopicCons.CF, CfClientMQTagCons.CASE_LABEL_QQ,"qq_"+caseId,caseLabel4QQ);
        MessageResult result = producer.send(message);
        log.info("notice message={},result={}",message,result);
    }

}
