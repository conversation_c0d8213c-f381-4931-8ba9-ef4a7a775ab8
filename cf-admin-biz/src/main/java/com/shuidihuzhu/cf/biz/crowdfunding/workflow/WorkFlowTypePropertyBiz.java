package com.shuidihuzhu.cf.biz.crowdfunding.workflow;

import com.shuidihuzhu.cf.model.crowdfunding.vo.workflow.WorkFlowTypePropertyVo;
import com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowTypeProperty;

import java.util.List;
import java.util.Map;

public interface WorkFlowTypePropertyBiz {

    void addOrgNoHandleLimit(int lowestId, int limit);

    List<WorkFlowTypeProperty> selectPropertyList(int flowType, int typeId, int propertyType);

    Map<Integer, Integer> getNoHandleOrgMapping(List<Long> orgIds);

    void addAutoAssignRecordList(Map<Integer, Long> assignList);

    void addAutoAssignRecord(long flowId, int userId);

    void addFlowBeginHandleRecord(long flowId, int userId);

    Map<Long, WorkFlowTypePropertyVo> getFlowAutoAssignData(List<Integer> flowIds);

    void addFlowHandleRecord(long flowId, int userId);
}
