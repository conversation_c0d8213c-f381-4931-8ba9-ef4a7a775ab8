package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfReportAddTrust;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfReportAddTrustVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfReportCredibleInfoVO;

import java.util.List;

/**
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-03-25 20:59
 **/
public interface CfReportAddTrustSnapshotBiz {

    /**
     * 添加审核快照
     *
     * @param cfReportAddTrust
     * @return
     */
    long addSnapshot(CfReportAddTrust cfReportAddTrust);

    /**
     * 获取审核快照
     *
     * @return
     */
    List<CfReportCredibleInfoVO> getSnapshot(long addTrustId, int auditStatus);

}