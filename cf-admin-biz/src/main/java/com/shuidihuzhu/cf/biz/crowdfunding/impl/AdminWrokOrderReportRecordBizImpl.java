package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminWrokOrderReportRecordBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminWorkOrderReportRecordDao;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderReportRecord;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderDataVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/10.
 */
@Service
public class AdminWrokOrderReportRecordBizImpl implements AdminWrokOrderReportRecordBiz {
    @Autowired
    private AdminWorkOrderReportRecordDao adminWorkOrderReportRecordDao;

    @Override
    public int insertAdminWorkOrderReportRecord(AdminWorkOrderReportRecord adminWorkOrderReportRecord) {
        return adminWorkOrderReportRecordDao.insertAdminWorkOrderReportRecord(adminWorkOrderReportRecord);
    }

    @Override
    public int getFollowCountByTime(String startTime, String endTime) {
        return adminWorkOrderReportRecordDao.getFollowCountByTime(startTime, endTime);
    }

    @Override
    public List<AdminWorkOrderDataVo> getFollowCountByUserIds(int orderType, int orderTask, String startTime, String endTime, List<Integer> userIds) {
        return adminWorkOrderReportRecordDao.getFollowCountByUserIds(orderType, orderTask, startTime, endTime, userIds);
    }
}
