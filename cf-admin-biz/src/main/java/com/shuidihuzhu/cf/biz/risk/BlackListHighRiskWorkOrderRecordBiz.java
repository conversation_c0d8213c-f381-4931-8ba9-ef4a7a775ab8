package com.shuidihuzhu.cf.biz.risk;

import com.shuidihuzhu.cf.model.risk.BlackListHighRiskWorkOrderRecord;
import com.shuidihuzhu.cf.model.risk.BlackListHighRiskWorkOrderRecordDto;
import org.apache.ibatis.annotations.Param;

/**
 * @Author: wangpeng
 * @Date: 2022/4/21 17:13
 * @Description:
 */
public interface BlackListHighRiskWorkOrderRecordBiz {
    int insert(BlackListHighRiskWorkOrderRecord riskWorkOrderRecord);

    BlackListHighRiskWorkOrderRecord getByWorkOrderId(long workOrderId);

    BlackListHighRiskWorkOrderRecordDto initialHighRiskInfo(long workOrderId);
}
