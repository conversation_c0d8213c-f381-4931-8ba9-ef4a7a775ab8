package com.shuidihuzhu.cf.biz.crowdfunding;

import com.github.pagehelper.PageInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowStatistics;
import lombok.AllArgsConstructor;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface AdminWorkOrderFlowReportBiz {

    PageInfo<AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> countFlowReport(int current, int size,
                                                                                            AdminWorkOrderFlowStatistics.searchParam param);
    @AllArgsConstructor
    enum FlowReportType {
        USER(0, "用户维度"),
        ORG(1, "组织维度");
        int code;
        String desc;
    }
}
