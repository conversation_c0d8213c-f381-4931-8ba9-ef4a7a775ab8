package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.admin.model.AdminEntranceStatus;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;

/**
 * @Description: 入口是否展示服务
 * @Author: panghair<PERSON>
 * @Date: 2022/3/9 3:20 下午
 */
public interface AdminTwModifyService {

    /**
     * 用户自主修改图文入口是否展示
     */
    List<AdminEntranceStatus> twEntranceIsDisplay(List<String> infoIds);

    /**
     * 保存图文修改记录
     */
    int saveTwModifyRecord(String infoUuid, long workOrderId, int modifyChannel);

    /**
     * 更新记录图文是否被用户修改过
     */
    int updateTwModifyFlag(long workOrderId, CrowdfundingInfo crowdfundingInfo);

}
