package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.delegate.saas.AdminOrganization;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.OrganizationClientV1;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseReasonCommonBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfRefuseReasonEntityDao;
import com.shuidihuzhu.cf.dao.crowdfunding.CfRefuseReasonOperateLogDao;
import com.shuidihuzhu.cf.dao.crowdfunding.CfRefuseReasonUseSceneRankMappingDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonTag;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditOperationItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CfRefuseReasonCommonBizImpl implements CfRefuseReasonCommonBiz {

    @Autowired
    private CfRefuseReasonOperateLogDao operateLogDao;
    @Autowired
    private OrganizationClientV1 orgClientV1;
    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;
    @Autowired
    private CfRefuseReasonUseSceneRankMappingDao useSceneRankMappingDao;
    @Autowired
    private CfRefuseReasonEntityDao entityDao;

    @Override
    public void validateCanRelationId(CfRefuseReasonEntity sourceEntity, CfRefuseReasonEntity relationEntity) {

        if (sourceEntity == null ||
                sourceEntity.getIsDelete() != InitialAuditOperationItem.RejectOperation.ENABLE.getCode()) {
            throw new RuntimeException("主理由ID不存在，请刷新页面，重新配置");
        }

        if (relationEntity == null) {
            throw new RuntimeException("该理由ID不存在，请重输");
        }

        if (relationEntity.getIsDelete() != InitialAuditOperationItem.RejectOperation.ENABLE.getCode()) {
            String entityStat = relationEntity.getIsDelete() == InitialAuditOperationItem.RejectOperation.DELETE.getCode()
                    ? "已删除" : "已弃用";
            throw new RuntimeException("该理由ID" + entityStat + "，不可配置");
        }

        // 判断是否是同一个使用场景
        List<Integer> sourceUserSceneIds = useSceneRankMappingDao.selectByEntityId(sourceEntity.getId())
                .stream().map(CfRefuseReasonEntity.CfRefuseReasonUseSceneRankMapping::getUseScene).collect(Collectors.toList());
        List<Integer> relationUserSceneIds = useSceneRankMappingDao.selectByEntityId(relationEntity.getId())
                .stream().map(CfRefuseReasonEntity.CfRefuseReasonUseSceneRankMapping::getUseScene).collect(Collectors.toList());
        if ( (CollectionUtils.isEmpty(sourceUserSceneIds) &&  CollectionUtils.isEmpty(relationUserSceneIds))
                || Collections.disjoint(sourceUserSceneIds, relationUserSceneIds)) {
            throw new RuntimeException("该理由ID与主ID不在同一使用场景下，不可配置");
        }
    }

    @Override
    public void validateRelationIds(CfRefuseReasonEntity entity, String newRelationIds) {
        if (entity == null || entity.getIsDelete() != InitialAuditOperationItem.RejectOperation.ENABLE.getCode()) {
            throw new RuntimeException("主理由ID不是启用状态，不能配置关联");
        }
        List<Integer> relationIds = getIdListSplitterByComma(newRelationIds);
        if (relationIds.contains(entity.getId())) {
            throw new RuntimeException("当前驳回条目配置的联动条目 包括驳回条目本身");
        }

        if (relationIds.size() != Sets.newHashSet(relationIds).size()) {
            throw new RuntimeException("联动条目中id有重复");
        }
    }

    @Override
    public void validateUseScene(CfRefuseReasonEntity reasonEntity, String useScene) {
        if (reasonEntity == null) {
            throw new RuntimeException("不能找到驳回理由");
        }

        if (StringUtils.isBlank(useScene)) {
            throw new RuntimeException("必须配置使用场景");
        }

        List<Integer> userSceneIds = getIdListSplitterByComma(useScene);
        Set<Integer> allUseSceneCode = CfRefuseReasonEntity.RejectOptionUseSceneEnum.getAllUseSceneCode();
        for (Integer code : userSceneIds) {
            if (!allUseSceneCode.contains(code)) {
                throw new RuntimeException("配置的使用场景code错误, 前端处理");
            }
        }
//        String[] sceneArr = StringUtils.split(useScene, ",");
//        ArrayList<String> sceneList = Lists.newArrayList(sceneArr);
//        boolean hasInitialScene = sceneList.contains(String.valueOf(CfRefuseReasonEntity.RejectOptionUseSceneEnum.INITIAL_AUDIT.getCode()));
//        if (hasInitialScene && StringUtils.contains(reasonEntity.getItemIds(),",")){
//            throw new RuntimeException("初审 和 材料审核-基本信息 增信信息 的驳回理由-驳回位置必须确定唯一");
//        }
    }

    @Override
    public void validateEditEntityDeleteStatus(CfRefuseReasonEntity entity, CfRefuseReasonTag reasonTag, int deleteStatus) {
        if (entity == null) {
            throw new RuntimeException("不能找到驳回理由");
        }

        if (reasonTag == null) {
            throw new RuntimeException("不能找到驳回理由分类");
        }

        InitialAuditOperationItem.RejectOperation operation = InitialAuditOperationItem.RejectOperation.codeOf(deleteStatus);
        if (operation == null) {
            throw new RuntimeException("操作的类型不合法");
        }

        if (operation == InitialAuditOperationItem.RejectOperation.DELETE && entity.getFrequency() > 0) {
            throw new RuntimeException("驳回理由已经被使用, 不能被删除");
        }

        // 判断是否被其它关联
        if (operation == InitialAuditOperationItem.RejectOperation.DELETE) {
            List<CfRefuseReasonEntity> allEntitys = entityDao.selectAllValidEntitys();
            for (CfRefuseReasonEntity currEntity : allEntitys) {
                if (currEntity.getId() == entity.getId() || StringUtils.isEmpty(currEntity.getChoiceRelationIds())) {
                    continue;
                }

                List<Integer> allRelationIds =  getIdListSplitterByComma(currEntity.getChoiceRelationIds());
                if (allRelationIds.contains(entity.getId())) {
                    throw new RuntimeException("有其它理由关联了当前驳回理由, 当前驳回理由不能被删除");
                }
            }
        }
    }

    @Override
    public List<Integer> getIdListSplitterByComma(String concatByCommaIds) {
        List<Integer> result = Lists.newArrayList();
        if (StringUtils.isNotBlank(concatByCommaIds)) {
            result.addAll(Lists.transform(Splitter.on(CfRefuseReasonEntity.CHINESE_COMMA)
                    .splitToList(concatByCommaIds), Integer::parseInt));
        }
        return result;
    }

    @Override
    public void addOperateEntityLog(int userId, int entityId, String action) {
        CfRefuseReasonEntity.CfRefuseReasonOperateLog operateLog = new CfRefuseReasonEntity.CfRefuseReasonOperateLog();
        operateLog.setReasonEntityId(entityId);
        operateLog.setAction(action);
        operateLog.setOperator(concatOrgAndName(userId));

        log.info("驳回项操作添加日志.userId:{}, entityId:{} action:{}", userId, entityId, action);
        operateLogDao.addLog(operateLog);
    }

    private String concatOrgAndName(int userId) {
        List<String> orgName = Lists.newArrayList();
        AuthRpcResponse<Map<Integer, List<AdminOrganization>>> orgs = orgClientV1.getUserOrgs(Lists.newArrayList(userId));
        if (orgs != null && orgs.getResult() != null && orgs.getResult().get(userId) != null) {
            for (AdminOrganization currOrg : orgs.getResult().get(userId)) {
                orgName.add(currOrg.getName());
            }
        }

        AuthRpcResponse<String> userName = seaAccountClientV1.getMisByUserId(userId);
        if (userName != null && StringUtils.isNotBlank(userName.getResult())) {
            orgName.add(userName.getResult());
        }

        return Joiner.on("-").join(orgName);
    }

    @Override
    public List<CfRefuseReasonEntity.CfRefuseReasonOperateLog> queryReasonLogByEntityId(int entityId) {
        return operateLogDao.selectOperateLogByEntityId(entityId);
    }

    @Override
    public List<CfRefuseReasonEntity.RejectOptionObject> queryAllRejectScene(int dataType) {
        return CfRefuseReasonEntity.RejectOptionUseSceneEnum.getAllUseSceneObject(dataType);
    }

    @Override
    public String getNewReasonEntityIds(CfRefuseReasonTag reasonTag, int entityId, int deleteStatus) {

        // 弃用的时候不改变关系
        if (deleteStatus == InitialAuditOperationItem.RejectOperation.DISABLE.getCode()) {
            return reasonTag.getReasonIds();
        }

        List<Integer> reasonIds = getIdListSplitterByComma(reasonTag.getReasonIds());

        if (deleteStatus == InitialAuditOperationItem.RejectOperation.ENABLE.getCode() && !reasonIds.contains(entityId)) {
            reasonIds.add(entityId);
        }

        if (deleteStatus == InitialAuditOperationItem.RejectOperation.DELETE.getCode()) {
            reasonIds.remove((Integer) entityId);
        }

        return Joiner.on(CfRefuseReasonEntity.CHINESE_COMMA).join(reasonIds);
    }




}
