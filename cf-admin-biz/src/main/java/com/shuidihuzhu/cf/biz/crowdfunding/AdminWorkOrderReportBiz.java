package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.AdminReportDataVo;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderReport;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkReportMap;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderDataVo;
import com.shuidihuzhu.cf.vo.crowdfunding.AdminWorkOrderReportVo;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by l<PERSON>iaoshuang on 2018/5/10.
 */
public interface AdminWorkOrderReportBiz {
    int insertAdminWorkOrderReport(AdminWorkOrderReport adminWorkOrderReport);

    AdminWorkOrderReport getAdminWorkOrderReportByCaseId(int caseId);

    List<AdminWorkOrderReport> getAdminWorkOrderReportByCount(int orderType, int orderTask, int count);

    List<AdminWorkOrderReportVo> getAdminWorkOrderReportList(int userId, int lostContact, Integer caseStatus, Integer addTrustStatus
            , Integer followStatus, Integer infoId, int current, int pageSize, Integer reprotType, Integer realName, String appointStartTime, String appointEndTime);

    Pair<Long, List<AdminWorkOrderReportVo>> getAdminWorkOrderReportListFromEs(int userId, int lostContact, Integer caseStatus, Integer addTrustStatus
            , Integer followStatus, Integer infoId, int current, int pageSize, Integer reprotType, Integer realName, String appointStartTime, String appointEndTime);

    List<AdminWorkOrderReport> getAdminWorkOrderReportById(List<Integer> ids);

    int getAdminWorkOrderReportCount(int orderType, int orderTask, String startTime, String endTime);

    int updateCaseRisk(int id, int caseRisk);

    List<AdminWorkOrderReportVo> selectAdminWorkOrderReport(int lostContact, Integer addTrustStatus, Integer followStatus
            , Integer caseRisk, String startTime, String endTime, int current, int pageSize, Integer isDrawCash, String reportFollowOperator, Integer realName);

    Pair<Long, List<AdminWorkOrderReportVo>> selectAdminWorkOrderReportFromEs(int lostContact, Integer addTrustStatus, Integer followStatus
            , Integer caseRisk, String startTime, String endTime, int current, int pageSize, Integer isDrawCash, String reportFollowOperator, Integer realName);

    int updateDealResultById(int id, int dealResult);

    int getCount(int orderType, int orderTask, String startTime, String endTime);


    int getDealCount(int orderType, int orderTask, String startTime, String endTime, int dealResult);

    int getOldFollowCount(int orderType, int orderTask, String startTime, int dealResult);

    List<AdminWorkOrderDataVo> getNoNeedDealCountByUserIds(int orderType, int orderTask, String startTime, String endTime,
                                                           int dealResult, List<Integer> userIds);

    List<AdminWorkOrderDataVo> getOldFollowCountByUserIds(int orderType, int orderTask, String startTime, int dealResult, List<Integer> userIds);

    List<AdminWorkOrderReport> selectWorkOrderReportByCaseIds(List<Integer> caseIds);

    List<AdminWorkOrderReportVo> getWorkOrderOperatorIdByCaseIds(List<Integer> caseIds);

    int updateFollowTypeById(int followType, int id);

    List<AdminWorkOrderDataVo> getCountByUserIds(int orderType, int orderTask, String startTime, String endTime, List<Integer> userIds);

    AdminWorkOrderReport selectWorkOrderReportById(int id);

    Map<String, Object> getReportWorkStat(Date today, Date yesterday, Set<Integer> userIdByRoleId);

    List<AdminReportDataVo> getAdminReportDataVo( int current, int pageSize,Long workId, Integer status, Long operator, Long caseId,String startDealTime,String endDealTime,
                                                  String startHandleTime,String endHandleTime,String startCreateTime,String endCreateTime);

    int addAdminWorkReportMap(AdminWorkReportMap map);

    List<AdminWorkReportMap> getLastAdminWorkReportMap(long workOrderId);


    int updateHandkeTime( List<Long> workOrderIdList);


    AdminWorkReportMap getAdminWorkReportMapByReportId(long reportId);

    boolean isSafe(int caseId);

    boolean editAppointTime(int workOrderId, String appointTime);

    List<AdminWorkOrderReport> findByCaseIdAndDealResult(int caseId, List<Integer> dealResults);

    AdminWorkOrderReport findByCaseIdAndReportId(int caseId, int reportId);

    Pair<Long, List<AdminWorkOrderReportVo>> getSelectAdminWorkOrderReportEs(String title, Integer caseId, Long realIdLong,String name,int current,int pageSize);
}
