package com.shuidihuzhu.cf.biz.risk.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingCommentBiz;
import com.shuidihuzhu.cf.biz.risk.IUserBehaviorService;
import com.shuidihuzhu.cf.client.feign.CfPlatformEsFeignClient;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.feign.CrowdfundingOrderFeignClient;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingCommentType;
import com.shuidihuzhu.cf.enums.crowdfunding.UserBehaviorEnum;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2019/10/12 下午4:48
 * @desc 订单评论
 */
@Service
public class UserBehaviorOrderCommentServiceImpl implements IUserBehaviorService {

    @Autowired
    private CfPlatformEsFeignClient platformEsFeignClient;

    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;

    @Autowired
    private CrowdfundingOrderFeignClient orderFeignClient;

    @Autowired
    private AdminCrowdfundingCommentBiz cfCommentBiz;


    @Override
    public List<AdminUserBehaviorDetail> buildBehaviorDetail(String mobile, long userId, String name, String identity, UserInfoModel userInfo, UserBehaviorEnum behaviorEnum) {
        List<CrowdfundingCommentVo> comments = cfCommentBiz.getCommentByUserIdAndTypeFromTiDb(userId,
                CrowdfundingCommentType.CONTRIBUTE_RECORD.value(), 2000);
        if(CollectionUtils.isEmpty(comments)){
            return Lists.newArrayList();
        }

        UserInfoDetail userInfoDetail = new UserInfoDetail();
        userInfoDetail.setUserId(userId);
        userInfoDetail.setNickName(Objects.nonNull(userInfo) ? userInfo.getNickname() : "");

        List<AdminUserBehaviorDetail> orderDetails = Lists.newArrayList();

        for (CrowdfundingCommentVo comment : comments){

            int caseId = comment.getCrowdfundingId();
            CrowdfundingInfo caseInfo = crowdfundingFeignClient.getCaseInfoById(caseId).getData();
            long orderId = comment.getParentId();
            CrowdfundingOrder order = orderFeignClient.getById(orderId).getData();

            StringBuilder sb = new StringBuilder();
            sb.append("案例id:").append(caseId).append(REDEX);
            sb.append("案例标题:").append(Objects.nonNull(caseInfo) ? caseInfo.getTitle() : "").append(REDEX);
            sb.append("订单留言:").append(Objects.nonNull(order) ? order.getComment() : "").append(REDEX);
            sb.append("订单留言发布者:").append(Objects.nonNull(order) ? order.getUserId() : "").append(REDEX);
            sb.append("订单评论:").append(comment.getContent());

            AdminUserBehaviorDetail orderDetail = new AdminUserBehaviorDetail();
            orderDetail.setTime(comment.getCreateTime());
            orderDetail.setBehaviorType(UserBehaviorEnum.ORDER_COMMENT.getKey());
            orderDetail.setUserInfoDetail(userInfoDetail);
            orderDetail.setUrl(Lists.newArrayList());
            orderDetail.setBehavoir(Lists.newArrayList(sb.toString().split(REDEX)));

            orderDetails.add(orderDetail);
        }

        return orderDetails;
    }

    @Override
    public UserBehaviorEnum getBehavior() {
        return UserBehaviorEnum.ORDER_COMMENT;
    }
}
