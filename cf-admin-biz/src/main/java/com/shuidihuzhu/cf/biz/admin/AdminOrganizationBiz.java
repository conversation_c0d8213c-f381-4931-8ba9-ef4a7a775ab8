package com.shuidihuzhu.cf.biz.admin;

import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.model.admin.AdminOrganization;
import com.shuidihuzhu.cf.model.admin.AdminOrganizationUserMap;
import com.shuidihuzhu.cf.vo.crowdfunding.OrganizationEmployeesVo;

import java.util.List;
import java.util.Map;

/**
 * @Author: WangYing on 2018/9/17
 */
public interface AdminOrganizationBiz {


    int addOrganizationNode(String organizationName, int parentOrganizationId);

    void editOrganizationNode(int organizationId, String newOrganizationName);

    void setManagerOfNode(int uid, int organizationId);

    void deleteEmployeeFromNode(int uid, int organizationId);

    int addEmployeeToNode(int uid, int organizationId);

    void deleteOrganizationNode(int organizationId);

    AdminOrganization getAdminOrganizationById(int id);

    AdminOrganizationUserMap getAdminOrganizationUserMap(int uid, int organizationId);

    List<AdminOrganization> getOrganizationTree();

    OrganizationEmployeesVo getOrganizationEmployees(int organizationId, int current, int pageSize);

    List<AdminUserAccountModel> getOrganizationManagers(int organizationId);

    List<AdminUserAccountModel> searchEmployeesByMis(int organizationId, String mis);

    List<AdminOrganization> getAdminOrganizationByParentOrgId(int organizationId);

    boolean hasSameNameAndParentOrg(String organizationName, int parentOrganizationId);

    List<AdminOrganization> getChildrenOrgs(List<AdminOrganization> orgs);
    List<Integer> getChildrenOrgIds(List<Integer> orgIds);

    Map<Integer, List<List<AdminOrganization>>> getOrgIdsByUserIds(List<Integer> userIds);

    List<AdminOrganizationUserMap> getLowestOrgByUserIds(List<Integer> userIds);

    List<AdminOrganization> getLowestOrgByParentId(int parentId);

    /**
     * 根据最底层返回对应的全路径组织结构，且按照底层->顺序返回
     */
    void getOrgByLowestId(int lowestId, List<AdminOrganization> result);

    List<AdminOrganizationUserMap> getOrgUserByOrgIds(List<Integer> organizationIds);

    List<AdminOrganization> getAdminOrganizationByName(String name);

    String joinOrgDescByNextId(int nextId);

    Integer queryLowestIdByText(List<String> orgTexts);

    String getGroupWithAreaNameByMis(String mis);

    int countOrganizationManagers(List<Integer> organizationIds);
}
