package com.shuidihuzhu.cf.biz.message.impl;

import com.shuidihuzhu.cf.biz.message.CfWxArticleCommentBiz;
import com.shuidihuzhu.cf.dao.stat.message.CfWxArticleCommentDao;
import com.shuidihuzhu.cf.model.message.CfWxArticleComment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
public class CfWxArticleCommentBizImpl implements CfWxArticleCommentBiz {
    @Autowired
    private CfWxArticleCommentDao cfWxArticleCommentDao;

    @Override
    public int count(int thirdType, String msgDataId, int index) {
        if (thirdType < 0 || StringUtils.isEmpty(msgDataId) || index < 0) {
            return 0;
        }
        Integer count = cfWxArticleCommentDao.count(thirdType, msgDataId, index);
        return count == null ? 0 : count.intValue();
    }

    @Override
    public int maxUserCommentId(int thirdType, String msgDataId, int index) {
        if (thirdType < 0 || StringUtils.isEmpty(msgDataId) || index < 0) {
            return 0;
        }
        Integer count = cfWxArticleCommentDao.maxUserCommentId(thirdType, msgDataId, index);
        return count == null ? 0 : count.intValue();
    }
}
