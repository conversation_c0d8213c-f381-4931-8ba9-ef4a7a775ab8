package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.admin.workorder.CfFundUseDetailDO;

import java.util.List;
import java.util.Map;

/**
 * @author: fengxuan
 * @create 2019-12-19 17:55
 **/
public interface CfFundUseDetailBiz {

    boolean batchInsert(List<CfFundUseDetailDO> detailDOS);

    /**
     *
     * @param workIds
     * @return key: fundUseProgressId
     */
    Map<Integer, List<CfFundUseDetailDO>> listByProgressIds(List<Integer> workIds);

    /**
     *
     * @param caseId
     * @return
     */
    List<CfFundUseDetailDO> listAuditDetailByCaseId(int caseId);

    /**
     * 审核资金用途明细
     * @param cfFundUseDetailDO
     * @return
     */
    boolean auditFundUseDetail(CfFundUseDetailDO cfFundUseDetailDO);
}
