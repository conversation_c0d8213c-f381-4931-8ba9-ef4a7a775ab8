package com.shuidihuzhu.cf.biz.crowdfunding.report.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.report.CfSendProveTemplateBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfSendProveTemplateDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfSendProveTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-09-17 20:11
 **/
@Service
public class CfSendProveTemplateBizImpl implements CfSendProveTemplateBiz {
    @Autowired
    private CfSendProveTemplateDao cfSendProveTemplateDao;

    @Override
    public List<CfSendProveTemplate> findByCaseIdAndAuditStatus(int caseId, List<Integer> auditStatus) {
        return cfSendProveTemplateDao.findByCaseIdAndAuditStatus(caseId, auditStatus);
    }

    @Override
    public int batchInsert(List<CfSendProveTemplate> cfSendProveTemplates) {
        return cfSendProveTemplateDao.batchInsert(cfSendProveTemplates);
    }

    @Override
    public List<CfSendProveTemplate> findByCaseIdAndProveId(int caseId, long proveId) {
        return cfSendProveTemplateDao.findByCaseIdAndProveId(caseId, proveId);
    }

    @Override
    public int updateAuditStatus(int caseId, long proveId, long templateId, int auditStatus) {
        return cfSendProveTemplateDao.updateAuditStatus(caseId, proveId, templateId, auditStatus);
    }

    @Override
    public int updateAllAuditStatus(int caseId, long proveId, int auditStatus) {
        return cfSendProveTemplateDao.updateAllAuditStatus(caseId, proveId, auditStatus);
    }

    @Override
    public CfSendProveTemplate findByCaseIdAndProveIdAndTemplateId(int caseId, long proveId, long actionId, long templateId) {
        return cfSendProveTemplateDao.findByCaseIdAndProveIdAndTemplateId(caseId, proveId, actionId, templateId);
    }

    @Override
    public int insertOne(CfSendProveTemplate cfSendProveTemplate) {
        return cfSendProveTemplateDao.insertOne(cfSendProveTemplate);
    }

    @Override
    public int updateAuditStatusAndContent(int caseId, long proveId, long templateId, int auditStatus, String content) {
        return cfSendProveTemplateDao.updateAuditStatusAndContent(caseId, proveId, templateId, auditStatus, content);
    }
}
