package com.shuidihuzhu.cf.biz.amount;

import com.shuidihuzhu.cf.client.adminpure.model.amount.CfAmountReasonableTask;
import com.shuidihuzhu.cf.client.adminpure.model.amount.CfAmountReasonableTaskWorkOrder;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2022/8/25 10:31
 * @Description:
 */
public interface AmountReasonableBiz {
    int insertAmountReasonableTask(CfAmountReasonableTask amountReasonableTask);

    List<CfAmountReasonableTask> getByCaseIdAndTaskType(int caseId, int taskType);

    CfAmountReasonableTask getByTaskInfoId(String taskInfoId);

    int updateCfAmountReasonableTask(CfAmountReasonableTask cfAmountReasonableTask);

    int insertAmountReasonableTaskWorkOrder(CfAmountReasonableTaskWorkOrder cfAmountReasonableTaskWorkOrder);

    List<CfAmountReasonableTaskWorkOrder> getByCaseId(int caseId);

    CfAmountReasonableTaskWorkOrder getTaskWorkOrderById(long id);

    CfAmountReasonableTask getTaskById(long id);

    int updateCfAmountReasonableTaskWorkOrder(CfAmountReasonableTaskWorkOrder cfAmountReasonableTaskWorkOrder);

    CfAmountReasonableTaskWorkOrder selectByWorkOrderId(long workOrderId);

    List<CfAmountReasonableTaskWorkOrder> selectByWorkOrderIdList(List<Long> workOrderIdList);

    List<CfAmountReasonableTask> getTaskByCaseId(int caseId);

}
