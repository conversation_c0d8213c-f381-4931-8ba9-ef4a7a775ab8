package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingCommentType;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingComment;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCommentView;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCommentVo;
import com.shuidihuzhu.cf.vo.crowdfunding.CfCommentVo;
import com.shuidihuzhu.common.web.util.admin.BasicExample;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * Created by ahrievil on 2017/5/6.
 */
public interface AdminCrowdfundingCommentBiz {

    List<CrowdfundingComment> getByPage(BasicExample basicExample, int current, int pageSize);
    List<CfCommentVo> getCrowdfundingCommentVo (List<CrowdfundingComment> crowdfundingComments, CrowdfundingCommentType commentType);
    Map<Long, List<CrowdfundingCommentView>> getCrowdfundingTrendsCommentsByParentIdList(List<Long> parentIdList,
                                                                                                   Integer offset, Integer limit);
    CrowdfundingComment getByIdNoCareDeleted(long id,long caseid);

    List<CrowdfundingComment> getCommentByParentId(int caseId,long parentId);

    List<CrowdfundingCommentVo> getCommentByParentIdFromTiDb(long parentId, int limit);

    List<CrowdfundingCommentVo> getCommentByUserIdAndTypeFromTiDb(long userId, int type, int limit);

    Integer selectCountByMin(Timestamp begin, Timestamp end);
}