package com.shuidihuzhu.cf.biz.admin;

import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrderRecord;

import java.util.Date;
import java.util.List;

/**
 * Created by Ahrievil on 2017/11/30
 */
public interface AdminWorkOrderRecordBiz {

    int insertOne(AdminWorkOrderRecord adminWorkOrderRecord);

    int insertOne(AdminWorkOrder adminWorkOrder);

    int insertList(List<AdminWorkOrderRecord> list);

    List<AdminWorkOrderRecord> selectByWorkIdAndOperateTypes(List<Long> workOrderIds, List<Integer> operateTypes);

    int deleteFirstUgcById(long id);

}
