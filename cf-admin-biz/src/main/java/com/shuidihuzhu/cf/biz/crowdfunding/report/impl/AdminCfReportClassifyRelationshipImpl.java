package com.shuidihuzhu.cf.biz.crowdfunding.report.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.report.AdminCfReportClassifyRelationshipBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfReportClassifyRelationshipDao;
import com.shuidihuzhu.cf.model.report.CfReportClassifyRelationship;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/4/17
 */
@Service
public class AdminCfReportClassifyRelationshipImpl implements AdminCfReportClassifyRelationshipBiz {

    @Autowired
    private AdminCfReportClassifyRelationshipDao adminCfReportClassifyRelationshipDao;

    @Override
    public int add(List<CfReportClassifyRelationship> cfReportClassifyRelationship) {
        if (CollectionUtils.isEmpty(cfReportClassifyRelationship)){
            return 0;
        }
        return adminCfReportClassifyRelationshipDao.add(cfReportClassifyRelationship);
    }

    @Override
    public int updateRelationship(long actionClassifyId, long disposeActionId, long labelId, int type) {
        return adminCfReportClassifyRelationshipDao.updateRelationship(actionClassifyId, disposeActionId, labelId, type);
    }

    @Override
    public List<CfReportClassifyRelationship> getByLabelId(long labelId) {
        if (labelId < 0) {
            return Lists.newArrayList();
        }
        return adminCfReportClassifyRelationshipDao.getByLabelId(labelId);
    }

    @Override
    public int deleteRelationshipByLabelId(long labelId) {
        if (labelId < 0) {
            return 0;
        }
        return adminCfReportClassifyRelationshipDao.deleteRelationshipByLabelId(labelId);
    }

    @Override
    public List<CfReportClassifyRelationship> getByTypeAndLabelIds(int type, List<Integer> labels) {
        if (type < 0 || CollectionUtils.isEmpty(labels)){
            return Lists.newArrayList();
        }
        return adminCfReportClassifyRelationshipDao.getByTypeAndLabelIds(type, labels);
    }
}
