package com.shuidihuzhu.cf.biz.crowdfunding.report.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.report.ReportWorkOrderFollowActionBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.ReportWorkOrderFollowActionDao;
import com.shuidihuzhu.cf.model.report.ReportWorkOrderCount;
import com.shuidihuzhu.cf.model.report.ReportWorkOrderFollowAction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-07-27 11:17
 **/
@Service
public class ReportWorkOrderFollowActionBizImpl implements ReportWorkOrderFollowActionBiz {
    @Autowired
    private ReportWorkOrderFollowActionDao reportWorkOrderFollowActionDao;

    @Override
    public int insertOne(ReportWorkOrderFollowAction reportWorkOrderFollowAction) {
        return reportWorkOrderFollowActionDao.insertOne(reportWorkOrderFollowAction);
    }

    @Override
    public List<ReportWorkOrderCount> getCountByOperatorId(List<Long> operatorIds, Date dayOfZero, List<Integer> orderTypes) {
        return reportWorkOrderFollowActionDao.getCountByOperatorId(operatorIds, dayOfZero, orderTypes);
    }

    @Override
    public ReportWorkOrderCount getCount(long operatorId, Date dayOfZero, int orderType) {
        return reportWorkOrderFollowActionDao.getCount(operatorId, dayOfZero, orderType);
    }

    @Override
    public List<ReportWorkOrderFollowAction> getByWorkOrderId(long workOrderId, List<Integer> actionTypes) {
        return reportWorkOrderFollowActionDao.getByWorkOrderId(workOrderId, actionTypes);
    }

    @Override
    public  List<Long> getWorkOrderId(long operatorId, Date dayOfZero, List<Integer> orderTypes) {
        return reportWorkOrderFollowActionDao.getWorkOrderId(operatorId,dayOfZero,orderTypes);
    }
}
