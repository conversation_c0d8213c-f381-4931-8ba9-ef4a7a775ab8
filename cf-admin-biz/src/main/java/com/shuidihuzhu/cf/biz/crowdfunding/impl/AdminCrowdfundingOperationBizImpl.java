package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.delegate.saas.AdminOrganization;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.OrganizationClientV1;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingOperationDao;
import com.shuidihuzhu.cf.enums.BooleanEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.vo.crowdfunding.ReportCaseVo;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.admin.PageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.joda.time.Duration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AdminCrowdfundingOperationBizImpl implements AdminCrowdfundingOperationBiz {

    private Set<String> NOT_SHENHE_GROUPS = Sets.newHashSet("微信1v1服务组","在线组", "呼入组");

	@Autowired
	private AdminCrowdfundingOperationDao crowdfundingOperationDao;
	@Autowired
    private AdminCrowdfundingReportBiz adminCrowdfundingReportBiz;
	@Resource
	private OrganizationClientV1 organizationClientV1;

	@Override
	public int add(CrowdfundingOperation crowdfundingOperation) {
	    if (crowdfundingOperation.getFollowType() == null){
	        crowdfundingOperation.setFollowType(0);
        }
		return crowdfundingOperationDao.add(crowdfundingOperation);
	}

	@Override
	public int update(CrowdfundingOperation crowdfundingOperation) {
		return crowdfundingOperationDao.update(crowdfundingOperation);
	}

	@Override
	public int updateOperation(String infoUuid, int operatorId, int operation, String reason, int deferContactReasonType) {
		return this.crowdfundingOperationDao.updateOperation(infoUuid, operatorId, operation, reason, deferContactReasonType);
	}

	@Override
	public CrowdfundingOperation getByInfoId(String infoId) {
		return crowdfundingOperationDao.getByInfoId(infoId);
	}

    @Override
    public CrowdfundingOperation getByInfoIdMaster(String infoId) {
        return crowdfundingOperationDao.getByInfoIdMaster(infoId);
    }

    @Override
	public int updateCommitTime(String infoId, Timestamp commitTime) {
		return this.crowdfundingOperationDao.insertOrUpdateCommitTime(infoId, commitTime);
	}

    @Override
    public int addCallCount(int count, String infoUuid) {
        return crowdfundingOperationDao.addCallCount(count, infoUuid);
    }

    @Override
    public int updateCallStatus(int callStatus, String infoUuid) {
        return crowdfundingOperationDao.updateCallStatus(callStatus, infoUuid);
    }

    @Async
    @Override
    public void addRefuseCount(int adminUserId, int count, String infoUuid) {

        int userRefuseCount = getApproveCount(adminUserId, count);
        int approveRefuseCount = count;
        if(userRefuseCount > 0){
            approveRefuseCount = 0;
        }

        crowdfundingOperationDao.addRefuseCount(userRefuseCount, approveRefuseCount, infoUuid);
    }

    /**
     * 区分组别
     * 如果是材料审核组、内容审核组、首次沟通组、二线、举报组、资金组、风险服务等其他的时候是材料审核的驳回
     * 其他的是用户主动要求的驳回
     * 默认值为审核的驳回
     */
    private int getApproveCount(int adminUserId, int count) {

        int userRefuseCount = 0;
        AuthRpcResponse<Map<Integer, List<AdminOrganization>>> response = organizationClientV1.getUserOrgs(Lists.newArrayList(adminUserId));
        log.info("organizationClientV1 {},result:{}", adminUserId, response);
        if(response == null || response.getCode() != 0){
            return userRefuseCount;
        } else {
            Map<Integer, List<AdminOrganization>> map = response.getResult();
            if(MapUtils.isEmpty(map) || !map.containsKey(adminUserId) || CollectionUtils.isEmpty(map.get(adminUserId))){
                return userRefuseCount;
            }

            for( AdminOrganization adminOrganization : map.get(adminUserId)){
                if(NOT_SHENHE_GROUPS.contains(adminOrganization.getName())){
                    userRefuseCount = count;
                }
            }
        }
        return userRefuseCount;
    }

    @Override
    public int setRefuseCount(int count, String infoUuid) {
        return crowdfundingOperationDao.setRefuseCount(count, infoUuid);
    }

    @Override
    public AdminCrowdfundingOperation selectByInfoUuid(String infoUuid) {
        return crowdfundingOperationDao.selectByInfoUuid(infoUuid);
    }

    @Override
    public Map<String, Integer> selectUuidReportStatus(List<String> infoUuids) {
	    if (CollectionUtils.isEmpty(infoUuids)) return Maps.newHashMap();
        Map<String, Integer> collect = crowdfundingOperationDao.selectByInfoUuids(infoUuids).stream()
                .collect(Collectors.toMap(AdminCrowdfundingOperation::getInfoId, AdminCrowdfundingOperation::getReportStatus));
        return collect != null ? collect : Maps.newHashMap();
    }

    @Override
    public List<CrowdfundingOperation> getByInfoIds(List<String> infoids) {
	    if(CollectionUtils.isEmpty(infoids)){
	        return null;
        }
        return crowdfundingOperationDao.getByInfoIds(infoids);
    }

    @Override
    public List<CrowdfundingOperation> getByTimeAndCallStatus(String startTime,
                                                              String endTime,
                                                              int callStatus,
                                                              int offset,
                                                              int limit) {
        return crowdfundingOperationDao.getByTimeAndCallStatus(startTime, endTime, callStatus, offset, limit);
    }

    @Override
    public List<ReportCaseVo> getReportCaseList(String caseType,
                                                Integer crowdfundingStatus,
                                                String followType,
                                                Integer sortType,
                                                Integer pageSize,
                                                Integer current,
                                                List<String> infoUuids,
                                                String addTrustStatus,
                                                String hospitalAuditStatus,
                                                BooleanEnum onWorkOrderEnum,
                                                Long hospitalSendBeginTime,
                                                Long hospitalSendEndTime,
                                                Long updateBeginTime,
                                                Long updateEndTime) {
        PageHelper.startPage(current, pageSize);
        if(CollectionUtils.isEmpty(infoUuids)) {
            if(crowdfundingStatus == AdminCfCaseStatus.No.getValue()) {
                return crowdfundingOperationDao.getReportCaseList(caseType, crowdfundingStatus, followType,
                        null,sortType, addTrustStatus, hospitalAuditStatus,
                        onWorkOrderEnum.getValue(),
                        hospitalSendBeginTime,
                        hospitalSendEndTime,
                        updateBeginTime,
                        updateEndTime);
            }else{
                return this.getCaseReportListCheakCaseStatus(caseType,crowdfundingStatus,followType,sortType,
                        null, addTrustStatus, hospitalAuditStatus,
                        onWorkOrderEnum,
                        hospitalSendBeginTime,
                        hospitalSendEndTime,
                        updateBeginTime,
                        updateEndTime);
            }
        }else{
            //如果案例状态是空 不需要额外的条件
            if (crowdfundingStatus==AdminCfCaseStatus.No.getValue()){
                return crowdfundingOperationDao.getReportCaseList(caseType, crowdfundingStatus, followType,
                        infoUuids,sortType, addTrustStatus, hospitalAuditStatus, onWorkOrderEnum.getValue(), hospitalSendBeginTime,
                        hospitalSendEndTime,
                        updateBeginTime,
                        updateEndTime);
            }else{
                return this.getCaseReportListCheakCaseStatus(caseType, crowdfundingStatus,
                        followType, sortType, infoUuids, addTrustStatus, hospitalAuditStatus, onWorkOrderEnum, hospitalSendBeginTime,
                        hospitalSendEndTime,
                        updateBeginTime,
                        updateEndTime);
            }
        }
	}

    @Override
    public List<ReportCaseVo> setFirstAndLast(List<ReportCaseVo> reportCaseVoList, List<CrowdfundingReport> firstReportList, List<CrowdfundingReport> lastReportList, List<Integer> infoids) {
        if (!CollectionUtils.isEmpty(firstReportList)){
            this.setFirstReportTime(reportCaseVoList, firstReportList);
            lastReportList=adminCrowdfundingReportBiz.getListByCreateTimeAndInfoid(infoids, CfReportCaseSortTypeEnum.LAST_REPORT_TIME.getValue());
            this.setLastReportTime(reportCaseVoList, lastReportList);
            return reportCaseVoList;
        }
        if (!CollectionUtils.isEmpty(lastReportList)){
            this.setLastReportTime(reportCaseVoList, lastReportList);
            firstReportList=adminCrowdfundingReportBiz.getListByCreateTimeAndInfoid(infoids,CfReportCaseSortTypeEnum.FIRST_REPORT_TIME.getValue());
            this.setFirstReportTime(reportCaseVoList, firstReportList);
            return reportCaseVoList;
        }
        //2按首次举报排序   3 按最新举报排序
        firstReportList=adminCrowdfundingReportBiz.getListByCreateTimeAndInfoid(infoids,CfReportCaseSortTypeEnum.FIRST_REPORT_TIME.getValue());
        lastReportList=adminCrowdfundingReportBiz.getListByCreateTimeAndInfoid(infoids,CfReportCaseSortTypeEnum.LAST_REPORT_TIME.getValue());
        if (!CollectionUtils.isEmpty(firstReportList)) {
            this.setFirstReportTime(reportCaseVoList, firstReportList);
        }
        if (!CollectionUtils.isEmpty(lastReportList)) {
            this.setLastReportTime(reportCaseVoList, lastReportList);
        }
        return reportCaseVoList;
    }

    @Override
    public void setVoIsHaveNewReport(List<ReportCaseVo> reportCaseVoList) {
	    if (CollectionUtils.isEmpty(reportCaseVoList)) {
	        return;
        }
	    List<Integer> infoids=reportCaseVoList.stream().map(ReportCaseVo::getId).collect(Collectors.toList());
	    //查询出每个案例是否有新加举报
        List<CrowdfundingReport> crowdfundingReportList=adminCrowdfundingReportBiz.getIsHaveNewReport(infoids);
        if (!CollectionUtils.isEmpty(crowdfundingReportList)){
            for (CrowdfundingReport crowdfundingReport:crowdfundingReportList)
            {
                for (ReportCaseVo reportCaseVo:reportCaseVoList){
                    if (crowdfundingReport.getActivityId().equals(reportCaseVo.getId())){
                        reportCaseVo.setIsHaveNewReport(crowdfundingReport.getIsNewreport());
                    }
                }
            }
        }
    }

    @Override
    public List<CrowdfundingOperation> selectByInfoIdList(List<String> list) {
	    if (CollectionUtils.isEmpty(list)) {
	        return Lists.newArrayList();
        }
        return crowdfundingOperationDao.selectByInfoIdList(list);
    }

    private void setFirstReportTime(List<ReportCaseVo> reportCaseVoList, List<CrowdfundingReport> firstReportList){
        Map<Integer, CrowdfundingReport> firstReportMap = firstReportList.stream().
                collect(Collectors.toMap(CrowdfundingReport::getActivityId, Function.identity()));
        for (ReportCaseVo reportCaseVo : reportCaseVoList) {
            CrowdfundingReport crowdfundingReport = firstReportMap.get(reportCaseVo.getId());
            if (crowdfundingReport != null) {
                reportCaseVo.setFirstReportTime(crowdfundingReport.getCreateTime());
            }
        }
    }

    private void setLastReportTime(List<ReportCaseVo> reportCaseVoList, List<CrowdfundingReport> lastReportList){
        Map<Integer, CrowdfundingReport> lastReportMap = lastReportList.stream().
                collect(Collectors.toMap(CrowdfundingReport::getActivityId, Function.identity()));
        for(ReportCaseVo reportCaseVo:reportCaseVoList){
            CrowdfundingReport crowdfundingReport = lastReportMap.get(reportCaseVo.getId());
            if (crowdfundingReport != null) {
                reportCaseVo.setFirstReportTime(crowdfundingReport.getCreateTime());
            }
        }
    }

    //根据案例选择状态对案例进行筛选
    private List<ReportCaseVo> getCaseReportListCheakCaseStatus(String caseType,
                                                                int caseStatus,
                                                                String followType,
                                                                int sortType,
                                                                List<String> infoUuids,
                                                                String addTrustStatus,
                                                                String hospitalAuditStatus,
                                                                BooleanEnum onWorkOrderEnum,
                                                                Long hospitalSendBeginTime,
                                                                Long hospitalSendEndTime,
                                                                Long updateBeginTime,
                                                                Long updateEndTime) {
        Integer apporoveStatus = null;
        Integer drawStatus = null;
        Integer drawApplyStatus = null;
        Integer refundStatus = null;
        if (caseStatus == AdminCfCaseStatus.APPROVE_NO.getValue()) {
            //1.审核未通过
        } else if (caseStatus == AdminCfCaseStatus.APPROVE_FINISH.getValue()) {
            //2.审核通过
            apporoveStatus =  CrowdfundingStatus.CROWDFUNDING_STATED.value();

        } else if (caseStatus == AdminCfCaseStatus.DRAWCASH_SUBMIT.getValue()) {
            //3.已申请提现
            apporoveStatus =  CrowdfundingStatus.CROWDFUNDING_STATED.value();
            drawApplyStatus = AdminCfDrawCashConstant.ApplyStatus.UNSUBMIT.getCode();

        } else if (caseStatus == AdminCfCaseStatus.DRAWCASH_APPROVE.getValue()) {
            //4.体现审核通过还未生成
            apporoveStatus =  CrowdfundingStatus.CROWDFUNDING_STATED.value();
            drawApplyStatus = AdminCfDrawCashConstant.ApplyStatus.APPROVE_SUCCESS.getCode();
            drawStatus =    CfDrawCashConstant.DrawStatus.UNBUILD.getCode();

        } else if (caseStatus == AdminCfCaseStatus.DRAWCASH_BUILD.getValue()) {
            //5.已生成&打款失败
            apporoveStatus =  CrowdfundingStatus.CROWDFUNDING_STATED.value();
            drawApplyStatus = AdminCfDrawCashConstant.ApplyStatus.APPROVE_SUCCESS.getCode();
            drawStatus = CfDrawCashConstant.DrawStatus.HANDLE_FAILED.getCode();

        } else if (caseStatus == AdminCfCaseStatus.DRAWCASH_SUCCESS.getValue()) {
            //6.已打款&部分打款成功
            apporoveStatus =  CrowdfundingStatus.CROWDFUNDING_STATED.value();
            drawApplyStatus = AdminCfDrawCashConstant.ApplyStatus.APPROVE_SUCCESS.getCode();
            drawStatus = CfDrawCashConstant.DrawStatus.HANDLE_SUCCESS.getCode();

        } else if (caseStatus == AdminCfCaseStatus.REFUND_SUBMIT.getValue()) {
            //7.已申请退款 >不提交
            apporoveStatus =  CrowdfundingStatus.CROWDFUNDING_STATED.value();
            refundStatus =  NewCfRefundConstant.ApplyStatus.UNSUBMIT.getCode();
        }

        if (drawApplyStatus != null) {
            return crowdfundingOperationDao.getListByWhereDrawCashAndPage(caseType, apporoveStatus,
                    followType, sortType, infoUuids, drawApplyStatus,
                    drawStatus,  addTrustStatus, hospitalAuditStatus, onWorkOrderEnum.getValue(), hospitalSendBeginTime,
                    hospitalSendEndTime,
                    updateBeginTime,
                    updateEndTime);
        } else {
            return crowdfundingOperationDao.getListByWhereAndPage(
                    caseType, apporoveStatus, followType, sortType, infoUuids,
                    refundStatus,  addTrustStatus, hospitalAuditStatus,
                    onWorkOrderEnum.getValue(),
                    hospitalSendBeginTime,
                    hospitalSendEndTime,
                    updateBeginTime,
                    updateEndTime);
        }
    }

    @Override
    public boolean validateUserSubmitTime(Long userSubmitBeginTime, Long userSubmitEndTime) {

	    if (userSubmitBeginTime == null && userSubmitEndTime == null) {
	        return true;
        }

	    if (userSubmitBeginTime == null || userSubmitEndTime == null || userSubmitBeginTime > userSubmitEndTime
            || userSubmitEndTime - userSubmitBeginTime > Duration.standardDays(7).getStandardSeconds()) {
	        return false;
        }

	    return true;
    }

    @Override
    public com.shuidihuzhu.common.web.model.Response generatePageEmptyResponse(Map<String, Object> result, int current, int pageSize) {
        result.put("pagination", PageUtil.transform2PageMap(new Page<>(current, pageSize)));
        result.put("data",null);
        return NewResponseUtil.makeSuccess(result);
    }


    @Override
    public int updateCreditStatus(int caseId, int creditStatus) {
        return crowdfundingOperationDao.updateCreditStatus(caseId,creditStatus);
    }

    @Override
    public List<CrowdfundingOperation> findByReportStatus(List<Integer> reportStatusList) {
        return crowdfundingOperationDao.findByReportStatus(reportStatusList);
    }

    @Override
    public int updateFuwuType(int id,int type) {
        return crowdfundingOperationDao.updateFuwuType(id,type);
    }

    @Override
    public CrowdfundingOperation getByCaseId(int caseId) {
        return crowdfundingOperationDao.getByCaseId(caseId);
    }
}
