package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfDiseaseManagerDO;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2019-11-08 12:36
 **/
public interface CfDiseaseManagerBiz {

    /**
     * 添加疾病信息
     */
    boolean add(CfDiseaseManagerDO cfDiseaseManagerDO);


    /**
     * 编辑
     */
    boolean edit(CfDiseaseManagerDO cfDiseaseManagerDO);

    /**
     * 逻辑删除
     */
    boolean delete(long diseaseManagerId);

    /**
     * 列表展示
     */
    List<CfDiseaseManagerDO> listForAdminSearch(CfDiseaseManagerDO cfDiseaseManagerDO, int current, int pageSize);

    /**
     * 单条查找
     */
    CfDiseaseManagerDO getById(long diseaseManagerId);
}
