package com.shuidihuzhu.cf.biz.crowdfunding;

import org.springframework.web.context.request.async.DeferredResult;

/**
 * <AUTHOR>
 * @DATE 2019/11/22
 */
public interface WorkOrderAssignBiz {



    default String getKey(int userId,int orderType){

        return userId+"_"+orderType;
    }

    void setResult(String key);


    void addDeferredResult(String key,DeferredResult deferredResult);


    void delDeferredResult(String key);


    DeferredResult getByKey(String key);
}
