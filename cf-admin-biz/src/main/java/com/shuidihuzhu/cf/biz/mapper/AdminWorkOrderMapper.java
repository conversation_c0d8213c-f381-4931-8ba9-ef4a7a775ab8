package com.shuidihuzhu.cf.biz.mapper;

import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderFlow;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminSensitiveVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView;
import com.shuidihuzhu.cf.vo.crowdfunding.WorkOrderFirstApprove;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AdminWorkOrderMapper {

    AdminWorkOrderMapper INSTANCE = Mappers.getMapper(AdminWorkOrderMapper.class);

    @Mappings({
            @Mapping(source ="workOrder.comment" , target = "reason"),
            @Mapping(source ="workOrder.operatorId", target = "operatorId"),
            @Mapping(source ="workOrder.updateTime", target = "operatorDate"),
            @Mapping(source ="workOrder.id", target = "workOrderId"),
    })
    WorkOrderFirstApprove toWorkOrderFirstApprove(AdminWorkOrder workOrder);

    @Mappings({
            @Mapping(source = "workOrder.id" , target = "workOrderId"),
            @Mapping(source = "workOrder.operatorId" , target = "operatorId"),
            @Mapping(source = "workOrder.orderTask" , target = "taskType"),
    })
    AdminSensitiveVo toAdminSensitiveVo(AdminWorkOrder workOrder);

    @Mappings({
            @Mapping(source = "workOrder.orderStatus", target = "workOrderStatus"),
            @Mapping(source = "workOrder.level", target = "level"),
            @Mapping(source = "workOrder.comment", target = "comment"),
            @Mapping(source = "workOrder.operatorId", target = "operatorId"),
            @Mapping(source = "workOrder.creatorId", target = "creatorId"),
            @Mapping(source = "workOrder.createTime", target = "createTime"),
            @Mapping(source = "workOrder.updateTime", target = "updateTime"),

            @Mapping(source = "flow.id", target = "id"),
            @Mapping(source = "flow.workOrderId", target = "workOrderId"),
            @Mapping(source = "flow.problemType", target = "problemType"),
            @Mapping(source = "flow.problemContent", target = "problemContent"),
            @Mapping(source = "flow.problemImg", target = "problemImg"),
            @Mapping(source = "flow.handleImg", target = "handleImg"),
            @Mapping(source = "flow.mobile", target = "mobile"),
            @Mapping(source = "flow.caseId", target = "caseId"),
            @Mapping(source = "flow.secondClassifyId", target = "secondClassifyId"),
    })
    AdminWorkOrderFlowView toAdminWorkOrderFlowView(AdminWorkOrder workOrder, AdminWorkOrderFlow flow);
}
