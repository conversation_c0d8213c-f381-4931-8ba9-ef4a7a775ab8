package com.shuidihuzhu.cf.biz.stat.impl;

import com.shuidihuzhu.cf.biz.stat.CfWxArticleSummaryBiz;
import com.shuidihuzhu.cf.dao.stat.message.CfWxArticleSummaryDao;
import com.shuidihuzhu.cf.model.message.CfWxArticleSummary;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;


@Service
public class CfWxArticleSummaryBizImpl implements CfWxArticleSummaryBiz {
    @Autowired
    private CfWxArticleSummaryDao cfWxArticleSummaryDao;

    @Override
    public CfWxArticleSummary querySubtaskLastRecord(String msgDataId, int thirdType, int index) {
        if (StringUtils.isEmpty(msgDataId) || thirdType < 0 || index < 0) {
            return null;
        }
        return cfWxArticleSummaryDao.querySubtaskLastRecord(msgDataId, thirdType, String.valueOf(index));
    }
}

