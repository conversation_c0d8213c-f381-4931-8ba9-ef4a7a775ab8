package com.shuidihuzhu.cf.biz.crowdfunding.supply;

import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyProgress;

import java.util.List;
import java.util.Map;

/**
 * @author: fengxuan
 * @create 2020-01-10 10:27
 **/
public interface CfSupplyProgressBiz {


    boolean add(CfInfoSupplyProgress cfInfoSupplyProgress);

    /**
     * 驳回
     */
    boolean reject(CfInfoSupplyProgress supplyProgress);

    /**
     * 审核通过
     */
    boolean pass(long id, String imgUrls, int progressId);

    List<CfInfoSupplyProgress> listBySupplyActionId(long actionId);

    List<CfInfoSupplyProgress> listByIds(List<Long> ids);

    /**
     * 重新审核
     */
    boolean reprocess(long supplyProgressId);


    Map<Long, CfInfoSupplyProgress> listFirstSupplyProgress(List<Long> supplyProgressIds);

    List<CfInfoSupplyProgress> getByActionId(long progressActionId);
}
