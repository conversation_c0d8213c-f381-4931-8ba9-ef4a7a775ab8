package com.shuidihuzhu.cf.biz.admin.impl.verifycount;

import com.shuidihuzhu.cf.biz.admin.verifycount.VerifyCountRoute;
import com.shuidihuzhu.cf.enums.admin.AdminVerifyTypeEnum;
import com.shuidihuzhu.cf.vo.admin.VerifyCountVo;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/11/4
 */
@Service
@RefreshScope
public class PayeeAccountIdCardVerifyCountImpl implements VerifyCountRoute {

    @Value("${apollo.cf.open-deposit-verify-idcard-maxcount:20}")
    private int VERIFY_ID_CARD_MAX_COUNT;

    @Resource(name = "cfRedissonHandler")
    protected RedissonHandler cfRedissonHandler;

    @Override
    public String getKey() {
        return "payeeAccountIdCardVerify";
    }

    @Override
    public String getName() {
        return "托管提交收款人信息之前身份证验证";
    }

    @Override
    public AdminVerifyTypeEnum getType() {
        return AdminVerifyTypeEnum.ID_CARD_VERIFY;
    }

    @Override
    public int getLimit() {
        return VERIFY_ID_CARD_MAX_COUNT;
    }

    @Override
    public VerifyCountVo getCurrentCountVo(long userId) {
        VerifyCountVo verifyCountVo = initVerifyCountVo();
        Integer verifyCount = this.cfRedissonHandler.get( getKeyByUserId(userId), Integer.class);
        verifyCountVo.setCurrentCount(verifyCount == null ? 0 : verifyCount);
        return verifyCountVo;
    }

    @Override
    public boolean clear(long userId) {
        return this.cfRedissonHandler.del(getKeyByUserId(userId));
    }

    public String getKeyByUserId(long userId) {
        return "cf:deposit-verify-idcard-" + DateUtil.getDate2YMDStr(new Date()) + "-" + userId;
    }

    @Override
    public void innerTest(long userId) {
        cfRedissonHandler.incrAndSetTimeWhenNotExists(getKeyByUserId(userId), RedissonHandler.ONE_DAY);
    }

}
