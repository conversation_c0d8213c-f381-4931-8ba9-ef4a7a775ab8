package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfRepeatUserIdRecord;

import java.util.List;

/**
 * Created by ahrievil on 2017/6/21.
 */
public interface CfRepeatUserIdRecordBiz {
    int insertList(List<CfRepeatUserIdRecord> cfRepeatUserIdRecords);
    List<Integer> selectUserId();
    List<CfRepeatUserIdRecord> selectByRemainCounts();
    int updateRemainCount(long userId, int remainCounts);
    List<CfRepeatUserIdRecord> selectAll(int start, int limit);
}
