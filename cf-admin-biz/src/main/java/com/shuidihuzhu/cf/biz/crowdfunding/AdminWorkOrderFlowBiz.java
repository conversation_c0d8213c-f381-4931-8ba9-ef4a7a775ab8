package com.shuidihuzhu.cf.biz.crowdfunding;

import com.github.pagehelper.PageInfo;
import com.shuidihuzhu.cf.biz.crowdfunding.impl.AdminWorkOrderFlowBizImpl;
import com.shuidihuzhu.cf.client.adminpure.model.transformorder.SyncOrderInfoParam;
import com.shuidihuzhu.cf.client.adminpure.model.transformorder.TransformOrderCreateParam;
import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderFlow;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderFlowRemindRecord;
import com.shuidihuzhu.cf.model.crowdfunding.vo.*;
import com.shuidihuzhu.client.cf.admin.model.WorkOrderFlowParam;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.pf.common.v2.model.pagehelper.PaginationListVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Arrays;
import java.util.List;

/**
 * Created by Ahrievil on 2017/12/21
 */
public interface AdminWorkOrderFlowBiz {

    // TODO  只指定这几个角色， 后续会指定读取  126-在线客服  127-呼入客服  128-二线客服
    @Deprecated
    List<Integer> SPECIFIC_ROLE_IDS = Arrays.asList(126, 127, 128);

    AdminWorkOrderFlow selectByWorkOrderId(long workOrderId);

    void saveRecord(AdminWorkOrderFlow adminWorkOrderFlow, long userId,  String comment, int operateType, int level, long secondClassifyId);

    void saveRecord(AdminWorkOrderFlow adminWorkOrderFlow, long userId, int operatorId, int roleId, String comment, int operateType, int level, long secondClassifyId);

    Response<String> existRelateFlow(AdminWorkOrderFlow flowView);

    int relateFlowNums(AdminWorkOrderFlow flowView);

    Response createWorkOrderFlow(AdminWorkOrderFlowView vo);

    PageInfo<AdminWorkOrderFlowView> selectAdminWorkOrderByParam(AdminWorkOrderFlowParam.SearchParam searchParam);

    Pair<Long, List<AdminWorkOrderFlowView>> selectAdminWorkOrderByParamFromEs(AdminWorkOrderFlowParam.SearchParam searchParam);

    PageInfo<AdminWorkOrderFlowView> selectHasHandleWorkOrderByParam(AdminWorkOrderFlowParam.SearchParam searchParam);

    Pair<PageInfo<AdminWorkOrderFlowView>, Integer> selectWaitForHandleFlowByParam(AdminWorkOrderFlowParam.SearchParam searchParam);

    Response<Integer> AssigningTaskV2(int adminUserId);

    AdminWorkOrderFlowDetailView queryWorkOrderFlowDetailView(long id);

    List<AdminWorkOrderFlowDetailView.WorkOrderFlowLog> queryWorkOrderFlowLog(long id);

    Response handleWorkFlowOrder(AdminWorkOrderFlowParam.HandleParam param);

    AdminWorkOrderFlowBizImpl.CaseTitleAndMobile getTitleAndMobileByCaseId(int caseId);

    AdminWorkOrderFlowBizImpl.UnHandleFlowOrderSum countUnHandleOrder(int operatorId);

    List<AdminWorkOrderFlowBizImpl.OrgUnHandleOrder> countUnHandleByOrgId(List<Integer> orgIds);

    List<AdminWorkOrderFlowBizImpl.SimilarFlowOrderObj> querySimilarOrderByCaseIdAndSettingId(int caseId, int secondClassifyId);

    PageInfo<AdminWorkOrderFlowView> selectCreateWorkOrderByParam(AdminWorkOrderFlowParam.SearchParam searchParam);

    @Deprecated
    AdminWorkOrderFlowView buildFlowViewFromWorkOrder(AdminWorkOrderFlowBiz.WorkOrderFlow orderFlow);

    AdminWorkOrderFlowView buildFlowViewFromWorkOrder(WorkOrderFlowParam orderFlow);

    AdminWorkOrderFlowView selectOrderFlowViewById(long flowId);

    List<AdminWorkOrderFlowView> selectByMobileAndTaskType(String mobile, int taskType);

    void assignToGivenOrg(String generateWorkFlowId, String appendProblemDesc, List<String> orgNames);

    AdminWorkOrderFlowView selectByCaseIdAndTaskType(int caseId,int taskType);

    List<String> listCreatorChannel(long userId, int problemId);

    boolean addRemind(AdminWorkOrderFlowRemindRecord remindRecord, int adminUserId);

    PageInfo<AdminWorkOrderFlowView> listUnHandleByCaseIdOrMobile(Integer caseId, String mobile, int current, int pageSize);

    OperationResult<Long> createTransformOrder(TransformOrderCreateParam param);

    OperationResult<Long> updateTransformOrder(SyncOrderInfoParam param);

    String getNewClassifyDesc(int newFirstClassifyId,int newSecondClassifyId,int newThirdClassifyId);

    String ORG_NAME_SPLIT = "-";

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class WorkOrderFlow {
        //  【提现&退款,  单笔退款】
        private List<String> problemClassifys;
        //  【水滴筹，   运营，   客服组，    在线客服】
        private List<String> handleOrgs;
        private int handleSeaUserId;

        private int caseId;
        private String mobile;
        private int priorityLevel;
        private String problemTextDesc;
        private String problemPicDesc;

        private int userIdentity;

        // 1 --- 延迟48小时后 主动关闭工单  50001
        private int taskType;

        // 创建人id
        private int createId;

        /**
         * 新版问题分类 一级分类
         */
        private int newFirstClassifyId;
        /**
         * 新版问题分类 二级分类
         */
        private int newSecondClassifyId;
        /**
         * 新版问题分类 三级分类
         */
        private int newThirdClassifyId;
    }

    @NoArgsConstructor
    @Data
    class CaseTitleAndMobile {
        String title;
        String mobile;

        private NumberMaskVo mobileMask;

        public CaseTitleAndMobile(String title, String mobile) {
            this.title = title;
            this.mobile = mobile;
        }
    }

    // 未处理的工单的统计
    @Data
    class UnHandleFlowOrderSum {
        public int total;
        public int no;
        public int low;
        public int medium;
        public int high;
    }

    @Data
    @AllArgsConstructor
    class OrgUnHandleOrder {
        public int total;
        public int orgId;
    }

    // 相似工单
    @AllArgsConstructor
    @Data
    class SimilarFlowOrderObj {
        String idShow;
        Long idJump;
    }

    @AllArgsConstructor
    @Data
    class WorkOrderNoticeRefundObj {
        private String flowId; // 和以前一样的工单号
        private int status; // 4 无需处理  5 处理完成
    }


    @Data
    class FlowOrderContact {
        private long workOrderId;
        private String mobile;
        private String problemContent;
        private int createId;
        private String createMis;
        private List<List<String>> createOrgNameComplexList;

        public FlowOrderContact() {
        }

        public FlowOrderContact buildWorkOrderId(long workOrderId) {
            this.workOrderId = workOrderId;
            return this;
        }

        public FlowOrderContact buildMobile(String mobile) {
            this.mobile = mobile;
            return this;
        }

        public FlowOrderContact buildProblemContent(String problemContent) {
            this.problemContent = problemContent;
            return this;
        }

        public FlowOrderContact buildCreateId(int createId) {
            this.createId = createId;
            return this;
        }

        public FlowOrderContact buildCreateMis(String createMis) {
            this.createMis = createMis;
            return this;
        }

        public FlowOrderContact buildCreateOrgNameComplexList(List<List<String>> createOrgNameComplexList) {
            this.createOrgNameComplexList = createOrgNameComplexList;
            return this;
        }
    }

}
