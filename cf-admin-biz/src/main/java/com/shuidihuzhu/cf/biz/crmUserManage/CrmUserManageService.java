package com.shuidihuzhu.cf.biz.crmUserManage;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.dao.crmUserManage.UserManageDao;
import com.shuidihuzhu.cf.model.CrmUserManage.UserManage;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@Service
@Slf4j
@RefreshScope
public class CrmUserManageService {

    @Autowired
    private UserManageDao userManageDao;


    public List<UserManage.UserAccount> selectByIdCards(Collection<String> cryptoIdCards) {
        removeSpecialNum(cryptoIdCards);


        if (CollectionUtils.isEmpty(cryptoIdCards)) {
            return Lists.newArrayList();
        }
        return userManageDao.selectByIdCards(cryptoIdCards);

    }

    public List<UserManage.UserAccount> selectByUuids(Collection<String> uuids) {

        removeSpecialNum(uuids);

        if (CollectionUtils.isEmpty(uuids)) {
            return Lists.newArrayList();
        }

        return userManageDao.selectByUuids(uuids);
    }

    public List<UserManage.UserAccount> selectByMobiles(Collection<String> cryptoMobiles) {
        if (CollectionUtils.isEmpty(cryptoMobiles)) {
            return Lists.newArrayList();
        }

        return userManageDao.selectByMobiles(cryptoMobiles);
    }

    private static void removeSpecialNum(Collection<String> uuids) {
        if (CollectionUtils.isEmpty(uuids)) {
            return;
        }

        uuids.remove("");
    }

    public  int addUsers(List<UserManage.UserAccount> userAccount) {
        if (CollectionUtils.isEmpty(userAccount)) {
            return 0;
        }

        return userManageDao.addUsers(userAccount);
    }

    public int updateIdCardByMobiles(String cryptoIdCard, String userName, Collection<String> cryptoMobiles) {
        if (CollectionUtils.isEmpty(cryptoMobiles) || cryptoIdCard == null || userName == null) {
            return 0;
        }

        return userManageDao.updateIdCardByMobiles(cryptoIdCard, userName, cryptoMobiles);
    }

    public int updateUuidByIdCards(String uuid, Collection<String> cryptoMobiles) {

        if (CollectionUtils.isEmpty(cryptoMobiles) || uuid == null) {
            return 0;
        }

        return userManageDao.updateUuidByIdCards(uuid, cryptoMobiles);
    }
}
