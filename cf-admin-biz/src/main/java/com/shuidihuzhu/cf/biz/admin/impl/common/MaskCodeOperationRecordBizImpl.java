package com.shuidihuzhu.cf.biz.admin.impl.common;

import com.shuidihuzhu.cf.biz.admin.common.MaskCodeOperationRecordBiz;
import com.shuidihuzhu.cf.dao.admin.MaskCodeOperationRecordDao;
import com.shuidihuzhu.cf.model.common.MaskCodeOperationRecord;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class MaskCodeOperationRecordBizImpl implements MaskCodeOperationRecordBiz {
    @Resource
    private OldShuidiCipher oldShuidiCipher;
    @Autowired
    private MaskCodeOperationRecordDao maskCodeOperationRecordDao;

    @Override
    public void addMaskOperationRecord(MaskCodeOperationRecord maskCodeOperationRecord) {
        String queryContent = oldShuidiCipher.aesEncrypt(maskCodeOperationRecord.getQueryContent());
        if (queryContent == null) {
            queryContent = "";
        }
        maskCodeOperationRecord.setQueryContent(queryContent);
        maskCodeOperationRecordDao.addMaskOperationRecord(maskCodeOperationRecord);
    }
}
