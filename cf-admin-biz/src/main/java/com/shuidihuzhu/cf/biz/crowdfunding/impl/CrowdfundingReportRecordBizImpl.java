package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingReportRecordBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingReportRecordDao;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReportRecord;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingReportRecordVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by ni<PERSON><PERSON><PERSON><PERSON> on 2017/8/5.
 */
@Service
public class CrowdfundingReportRecordBizImpl implements CrowdfundingReportRecordBiz {

    private static final Logger LOGGER = LoggerFactory.getLogger(CrowdfundingReportRecordBizImpl.class);

    @Autowired
    private  CrowdfundingReportRecordDao crowdfundingReportRecordDao;


    @Override
    public int add(int reportId, int operationId, int dealStatus, String comment) {
        LOGGER.info("添加");
        try{
            CrowdfundingReportRecord crowdfundingReportRecord=new CrowdfundingReportRecord();
            crowdfundingReportRecord.setReportId(reportId);
            crowdfundingReportRecord.setOperatorId(operationId);
            crowdfundingReportRecord.setDealStatus(dealStatus);
            if(comment==null){
                comment="";
            }
            crowdfundingReportRecord.setComment(comment);
            return crowdfundingReportRecordDao.add(crowdfundingReportRecord);
        }catch (Exception e){
            LOGGER.info("CrowdfundingReportRecordBizImpl add error:",e);
        }
        return 0;
    }

    @Override
    public List<CrowdfundingReportRecord> getreportRecordList() {
        List<CrowdfundingReportRecord> crowdfundingReportRecordList =crowdfundingReportRecordDao.getreportRecordList();
        return crowdfundingReportRecordList;
    }

    @Override
    public List<CrowdfundingReportRecordVo> getReportRecordGroupByReportId(List<Integer> reportIds) {
        return crowdfundingReportRecordDao.getReportRecordGroupByReportId(reportIds);
    }

    @Override
    public List<CrowdfundingReportRecord> getreportRecordListByReportIds(List<Integer> reportIds) {
        List<CrowdfundingReportRecord> crowdfundingReportRecordList =crowdfundingReportRecordDao.getreportRecordListByReportIds(reportIds);
        return crowdfundingReportRecordList;
    }
}
