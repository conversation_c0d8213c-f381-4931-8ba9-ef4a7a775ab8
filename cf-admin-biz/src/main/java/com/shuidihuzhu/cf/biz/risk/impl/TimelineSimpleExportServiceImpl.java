package com.shuidihuzhu.cf.biz.risk.impl;

import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfHospitalAuditBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.biz.risk.ISimpleExportService;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfInfoLostContactDAO;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICfInfoXXXRecordDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.finance.impl.FinanceDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.delegate.ugc.IUgcDelegate;
import com.shuidihuzhu.cf.domain.cf.CfInfoLostContactDO;
import com.shuidihuzhu.cf.enums.crowdfunding.CfDrawCashConstant;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoStatusEnum;
import com.shuidihuzhu.cf.finance.client.feign.CfFinancePauseFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.model.CfDrawCashPauseRecord;
import com.shuidihuzhu.cf.finance.model.vo.CfDrawCashApplyVo;
import com.shuidihuzhu.cf.model.admin.CfHospitalAuditInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.MoneyUtil;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

/**
 * Created by sven on 2019/6/27.
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class TimelineSimpleExportServiceImpl implements ISimpleExportService {

    private final static DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd HH:mm:ss");

    private final static String RAISE_REGISTER_TIME = "发起人注册时间";
    private final static String CASE_CREATE_TIME = "案例发起时间";
    private final static String FIRST_APPROVE_TIME = "预审通过时间";
    private final static String FIRST_SHARE_TIME = "用户首次转发时间";
    private final static String FISRT_VERIFY_TIME = "案例首次证实时间";
    private final static String FIRST_SUBMIT_TIME = "首次提交审核时间";
    private final static String FIRST_REPORT_TIME = "首次举报时间";
    private final static String LATEST_REPORT_TIME = "最近一次举报时间";
    private final static String LABEL_LOST_CONTACT_TIME = "标记失联时间";
    private final static String SUBMIT_HOSPITAL_INFO = "用户提交医院核实时间";
    private final static String HOSPITAL_VERIFY_TIME = "医院核实时间";
    private final static String HOSPITAL_VERIFY_APPROVE_TIME = "医院核实通过时间";
    private final static String APPROVE_TIME = "审核通过时间";
    private final static String DELAY_CONTACT_TIME = "运营标记延迟联系时间";
    private final static String DRAW_CASH_TIME = "申请提现时间";
    private final static String DRAW_CASE_DESC = "提现说明";
    private final static String OPEN_DRAW_CASH_TIME = "运营开启打款时间";
    private final static String TASK_DRAW_CASH_TIME = "系统定时任务执行时间：资金二次收口风控时间";
    private final static String SYSTEM_APPROVE_DRAW = "资金审核：C端提现审核通过并公示时间";
    private final static String CASE_END_TIME = "筹款结束时间";
    private final static String DRAW_CASH_SUCCESS_TIME = "打款成功时间";

    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Resource
    private AdminCfInfoExtBiz cfInfoExtBiz;

    @Resource
    private IRiskDelegate riskDelegate;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;

    @Resource
    private IUgcDelegate ugcDelegate;

    @Autowired
    private AdminCfHospitalAuditBiz adminCfHospitalAuditBiz;

    @Autowired
    private FinanceDelegate financeDelegate;

    @Resource
    private AdminCfInfoLostContactDAO cfInfoLostContactDAO;

    @Autowired
    private CfFinancePauseFeignClient cfFinancePauseFeignClient;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private ICfInfoXXXRecordDelegate cfInfoXXXRecordDelegate;

    @Override
    public Map<String, String> getDetail(CfInfoSimpleModel cfInfoSimpleModel) {

        CrowdfundingInfo crowdfundingInfo = crowdfundingDelegate.getFundingInfoById(cfInfoSimpleModel.getId());
        CfInfoExt cfInfoExt = cfInfoExtBiz.getByCaseId(cfInfoSimpleModel.getId());

        List<CrowdfundingReport> reportList = riskDelegate.getCrowdfundingReportListByInfoId(cfInfoSimpleModel.getId());
        List<CrowdFundingVerification> verifications = ugcDelegate.queryAllCrowdFundingVerificationByInfoUuid(cfInfoSimpleModel.getInfoId());

        List<CfInfoShareRecord> cfInfoShareRecords = cfInfoXXXRecordDelegate.getListByInfoIdAndUserIds(cfInfoSimpleModel.getId(), Lists.newArrayList(cfInfoSimpleModel.getUserId()));

        CfHospitalAuditInfoExt cfHospitalAuditInfo = adminCfHospitalAuditBiz.getByInfoUuid(cfInfoSimpleModel.getInfoId());

        String dateStr = getUserRegisterTime(crowdfundingInfo);

        String hospitalApproveTime = "";
        if (cfHospitalAuditInfo != null && cfHospitalAuditInfo.getAuditStatus() == CrowdfundingInfoStatusEnum.PASSED.getCode()) {
            hospitalApproveTime = dateFormat.format(cfHospitalAuditInfo.getUpdateTime());
        }

        CfInfoLostContactDO cfInfoLostContactDO = cfInfoLostContactDAO.getValidByInfoUuid(cfInfoSimpleModel.getInfoId());

        List<CfDrawCashPauseRecord> drawCashPauseRecords = Lists.newArrayList();
        FeignResponse<List<CfDrawCashPauseRecord>> response = cfFinancePauseFeignClient.getPauseDrawCashByCaseId(cfInfoSimpleModel.getId());
        if (!response.notOk() && CollectionUtils.isNotEmpty(response.getData())) {
            drawCashPauseRecords.addAll(response.getData());
        }
        List<CfDrawCashPauseRecord> secondPauseLists = drawCashPauseRecords.stream()
                .filter(p -> p.getPauseSourceType() == 9)
                .collect(Collectors.toList());

        Map<String, String> map = Maps.newHashMap();

        map.put(RAISE_REGISTER_TIME, dateStr);
        map.put(CASE_CREATE_TIME, dateFormat.format(crowdfundingInfo.getCreateTime()));
        if (cfInfoExt.getFirstApproveTime() != null) {
            map.put(FIRST_APPROVE_TIME, dateFormat.format(cfInfoExt.getFirstApproveTime()));
        } else {
            map.put(FIRST_APPROVE_TIME, UNKOWN);
        }
        map.put(FIRST_SHARE_TIME, getFirstTime(cfInfoShareRecords, CfInfoShareRecord::getDateCreated));
        map.put(FISRT_VERIFY_TIME, getFirstTime(verifications, CrowdFundingVerification::getCreateTime));
        map.put(FIRST_SUBMIT_TIME, UNKOWN);
        map.put(FIRST_REPORT_TIME, getFirstTime(reportList, CrowdfundingReport::getCreateTime));
        map.put(LATEST_REPORT_TIME, getLatestTime(reportList, CrowdfundingReport::getCreateTime));
        map.put(LABEL_LOST_CONTACT_TIME, cfInfoLostContactDO == null ? "" : dateFormat.format(cfInfoLostContactDO.getCreateTime()));


        map.put(HOSPITAL_VERIFY_TIME, buildAuditApproveTime(cfHospitalAuditInfo));
        map.put(HOSPITAL_VERIFY_APPROVE_TIME, hospitalApproveTime);
        map.put(SUBMIT_HOSPITAL_INFO, buildHospitalAuditInfo(cfHospitalAuditInfo));
        map.put(APPROVE_TIME, UNKOWN);
        map.put(DELAY_CONTACT_TIME, UNKOWN);

        Response<CfDrawCashApplyVo> applyVoResponse = financeDelegate.getApplyInfo(cfInfoSimpleModel.getId());
        CfDrawCashApplyVo drawCashApplyVo = applyVoResponse.getData();

        map.put(DRAW_CASH_TIME, drawCashApplyVo != null ?
                dateFormat.format(drawCashApplyVo.getApplyTime()) : UNKOWN);

        map.put(DRAW_CASE_DESC, buildDrawCashDesc(drawCashApplyVo));
        map.put(OPEN_DRAW_CASH_TIME, buildResumeDrawTime(secondPauseLists));
        map.put(TASK_DRAW_CASH_TIME, buildPauseSecond(secondPauseLists));
        map.put(SYSTEM_APPROVE_DRAW, buildDrawCashAuditTime(drawCashApplyVo));
        map.put(CASE_END_TIME, dateFormat.format(crowdfundingInfo.getEndTime()));
        map.put(DRAW_CASH_SUCCESS_TIME, buildDrawCashSuccessTime(drawCashApplyVo));

        return map;
    }

    private String buildResumeDrawTime(List<CfDrawCashPauseRecord> secondPauseLists) {
        if (CollectionUtils.isEmpty(secondPauseLists)) {
            return "没有二次收口";
        }
        CfDrawCashPauseRecord last = secondPauseLists.get(secondPauseLists.size() - 1);
        if (last.getStatus() == 2 || last.getStatus() == 3) {
            return dateFormat.format(last.getRecoverTime());
        }
        return "未开启打款";
    }

    private String buildPauseSecond(List<CfDrawCashPauseRecord> secondPauseLists) {
        if (CollectionUtils.isEmpty(secondPauseLists)) {
            return "没有二次收口";
        }
        String result = "";
        for (CfDrawCashPauseRecord pauseRecord : secondPauseLists) {
            result += String.format("二次收口备注：%s\n  时间：%s  ",
                    pauseRecord.getPauseComment(),
                    pauseRecord.getCreateTime());
        }
        return result;
    }

    private String getHospitalAuditDetail(CfHospitalAuditInfoExt cfHospitalAuditInfoExt) {

        if (cfHospitalAuditInfoExt == null) {
            return "";
        }
        StringBuffer sb = new StringBuffer();
        sb.append("患者姓名：" + cfHospitalAuditInfoExt.getPatientName() + "\n");
        sb.append("医院名称:" + cfHospitalAuditInfoExt.getHospitalName() + "\n");
        sb.append("科室：" + cfHospitalAuditInfoExt.getDepartment() + "\n");
        sb.append("楼号：" + cfHospitalAuditInfoExt.getFloorNumber() + "\n");
        sb.append("床号: " + cfHospitalAuditInfoExt.getBedNumber() + "\n");
        sb.append("住院号: " + cfHospitalAuditInfoExt.getHospitalizationNumber() + "\n");
        sb.append("医生姓名: " + cfHospitalAuditInfoExt.getDoctorName() + "\n");
        sb.append("科室座机号码: " + cfHospitalAuditInfoExt.getDepartmentTelNumber() + "\n");
        sb.append("运营填写内容: " + cfHospitalAuditInfoExt.getOperatorContent() + "\n");
        return sb.toString();
    }

    private String buildDrawCashAuditTime(CfDrawCashApplyVo drawCashApplyVo) {
        if (drawCashApplyVo == null) {
            return "没有提现申请";
        }
        if (drawCashApplyVo.getApplyStatus() == CfDrawCashConstant.ApplyStatus.SUBMIT_APPROVE_PENDING.getCode()) {
            return "提现申请待审核";
        } else if (drawCashApplyVo.getApplyStatus() == CfDrawCashConstant.ApplyStatus.UNSUBMIT.getCode()) {
            return "提现申请未提交";
        } else if (drawCashApplyVo.getApplyStatus() == CfDrawCashConstant.ApplyStatus.APPROVE_SUCCESS.getCode()) {
            String result = dateFormat.format(drawCashApplyVo.getApplyAuditTime());
            return result;
        } else if (drawCashApplyVo.getApplyStatus() == CfDrawCashConstant.ApplyStatus.APPROVE_REJECT.getCode()) {
            return "提现申请审核拒绝";
        }
        return "";
    }

    // TODO 替换为大接口
    private String buildDrawCashSuccessTime(CfDrawCashApplyVo drawCashApplyVo) {
        if (drawCashApplyVo == null) {
           return UNKOWN;
        }

        Response<CfCapitalAccount> response = financeDelegate.getCfCapitalAccountByInfoUuid(drawCashApplyVo.getInfoUuid());
        if (response.notOk() || null == response.getData()) {
            log.error("资金服务获取打款记录失败");
            return UNKOWN;
        }

        String result = "";
        CfCapitalAccount capitalAccount = response.getData();
        long successAmount = capitalAccount.getDrawCashAmount();
        if (successAmount == 0) {
            return result;
        }
        String inAccountName = "";
        String inAccountNo = "";
        CfDrawCashConstant.ThirdType thirdType = CfDrawCashConstant.ThirdType.getByCode(drawCashApplyVo.getThirdType());
        if ( CfDrawCashConstant.ThirdType.PA.equals(thirdType)) {
            CrowdfundingInfoPayee crowdfundingInfoPayee = crowdfundingDelegate.getCrowdfundingInfoPayeeByInfoUuid(drawCashApplyVo.getInfoUuid());
            inAccountName = null == crowdfundingInfoPayee ? StringUtils.EMPTY : crowdfundingInfoPayee.getName();
            inAccountNo = null == crowdfundingInfoPayee ? StringUtils.EMPTY : crowdfundingInfoPayee.getBankCard();
        } else  if ( CfDrawCashConstant.ThirdType.PA_DG.equals(thirdType)) {
            CrowdfundingInfoHospitalPayee hospitalPayee = crowdfundingDelegate.getCrowdfundingInfoHospitalPayeeByInfoUuid(drawCashApplyVo.getInfoUuid());
            inAccountName = null == hospitalPayee ? StringUtils.EMPTY : hospitalPayee.getHospitalAccountName();
            inAccountNo = null == hospitalPayee ? StringUtils.EMPTY : hospitalPayee.getHospitalBankCard();
        } else  if ( CfDrawCashConstant.ThirdType.PA_GY_DG.equals(thirdType)) {
            CfCharityPayee charityPayee = crowdfundingDelegate.getCfCharityPayeeByUUid(drawCashApplyVo.getInfoUuid());
            inAccountName = null == charityPayee ? StringUtils.EMPTY : charityPayee.getOrgName();
            inAccountNo = null == charityPayee ? StringUtils.EMPTY : charityPayee.getOrgBankCard();
        }

        result = String.format("打款成功时间:%s  \n打款金额:%s  \n收款人:%s  \n收款人卡号:%s",
                drawCashApplyVo.getFinishTime(),
                MoneyUtil.buildBalance(successAmount),
                inAccountName,
                shuidiCipher.decrypt(inAccountNo));

        return result;
    }

    private String buildDrawCashDesc(CfDrawCashApplyVo drawCashApplyVo) {
        if (drawCashApplyVo == null) {
            return "";
        }
        String result = String.format("资金用途：%s  \n患者病情:%s  \n紧急联系人姓名:%s  \n紧急联系人电话:%s  \n紧急联系人关系:%s",
                drawCashApplyVo.getUseOfFunds(),
                drawCashApplyVo.getPatientConditionNow(),
                drawCashApplyVo.getEmergencyContactName(),
                drawCashApplyVo.getEmergencyContactPhone(),
                drawCashApplyVo.getEmergencyContactRelation());
        return result;
    }

    private String buildAuditApproveTime(CfHospitalAuditInfoExt cfHospitalAuditInfoExt) {
        if (cfHospitalAuditInfoExt == null) {
            return "";
        }
        String auditResult = "";
        if (cfHospitalAuditInfoExt.getAuditStatus() == CrowdfundingInfoStatusEnum.PASSED.getCode()) {
            auditResult = "核实通过";
        } else if (cfHospitalAuditInfoExt.getAuditStatus() == CrowdfundingInfoStatusEnum.REJECTED.getCode()) {
            auditResult = "核实拒绝";
        } else if (cfHospitalAuditInfoExt.getAuditStatus() == CrowdfundingInfoStatusEnum.UN_SUBMITTED.getCode()) {
            auditResult = "待审核";
        } else if (cfHospitalAuditInfoExt.getAuditStatus() == CrowdfundingInfoStatusEnum.UN_SAVE.getCode()) {
            auditResult = "下发未填写";
        }
        String result = String.format("时间：%s\n核实状态：%s",
                dateFormat.format(cfHospitalAuditInfoExt.getUpdateTime()),
                auditResult);
        return result;
    }

    private String buildHospitalAuditInfo(CfHospitalAuditInfoExt cfHospitalAuditInfoExt) {
        if (cfHospitalAuditInfoExt == null) {
            return "";
        }
        String result = String.format("提交时间:%s  \n患者姓名：%s  \n医院名称：%s  \n科室:%s \n楼号:%s \n床号:%s \n住院号：%s \n" +
                        "主治医生姓名:%s \n运营填写内容:%s \n科室座机号码:%s",
                dateFormat.format(cfHospitalAuditInfoExt.getCreateTime()),
                cfHospitalAuditInfoExt.getPatientName(),
                cfHospitalAuditInfoExt.getHospitalName(),
                cfHospitalAuditInfoExt.getDepartment(),
                cfHospitalAuditInfoExt.getFloorNumber(),
                cfHospitalAuditInfoExt.getBedNumber(),
                cfHospitalAuditInfoExt.getHospitalizationNumber(),
                cfHospitalAuditInfoExt.getDoctorName(),
                cfHospitalAuditInfoExt.getOperatorContent(),
                cfHospitalAuditInfoExt.getDepartmentTelNumber()
        );
        return result;
    }

    private <T> String getFirstTime(List<T> list, Function<T, Date> function) {
        if (CollectionUtils.isEmpty(list) || function == null) {
            return UNKOWN;
        }

        List<Date> dates = Lists.transform(list, function);
        List<Date> sortedDates = dates.stream().sorted(Comparator.comparing(Date::getTime)).collect(Collectors.toList());
        Date date = sortedDates.get(0);
        if (date == null) {
            return UNKOWN;
        }
        return dateFormat.format(date);
    }

    private <T> String getLatestTime(List<T> list, Function<T, Date> function) {
        if (CollectionUtils.isEmpty(list) || function == null) {
            return UNKOWN;
        }

        List<Date> dates = Lists.transform(list, function);
        List<Date> sortedDates = dates.stream().sorted(Comparator.comparing(Date::getTime).reversed()).collect(Collectors.toList());
        Date date = sortedDates.get(0);
        if (date == null) {
            return UNKOWN;
        }
        return dateFormat.format(date);
    }

    @NotNull
    private String getUserRegisterTime(CrowdfundingInfo crowdfundingInfo) {
        try {
            Date date = userInfoServiceBiz.getUserCreateTime(crowdfundingInfo.getUserId());
            if (date == null) {
                return UNKOWN;
            }
            return dateFormat.format(date);
        } catch (Exception e) {
            log.error("error in getUserCreateTime {} {}", crowdfundingInfo.getId(), crowdfundingInfo.getUserId());
            return UNKOWN;
        }
    }

    @Override
    public String getCategory() {
        return "主要时间线";
    }

    @Override
    public int getOrder() {
        return 3;
    }
}
