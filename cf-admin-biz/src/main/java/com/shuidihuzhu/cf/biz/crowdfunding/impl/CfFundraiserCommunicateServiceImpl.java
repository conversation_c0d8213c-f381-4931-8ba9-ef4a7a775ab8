package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.ICfFundraiserCommunicateService;
import com.shuidihuzhu.cf.dao.crowdfunding.CfReportFundraiserCommunicateDAO;
import com.shuidihuzhu.cf.model.crowdfunding.CfFundraiserCommunicateDO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2019/12/16 下午8:33
 * @desc
 */
@Service
public class CfFundraiserCommunicateServiceImpl implements ICfFundraiserCommunicateService {

    @Autowired
    private CfReportFundraiserCommunicateDAO cfReportFundraiserCommunicateDAO;

    @Override
    public int insert(CfFundraiserCommunicateDO communicateDO) {
        if(Objects.isNull(communicateDO)){
            return 0;
        }

        return cfReportFundraiserCommunicateDAO.insert(communicateDO);
    }

    @Override
    public int updateConnectStatus(long id, int connectStatus) {
        if(id <= 0 || connectStatus <= 0){
            return 0;
        }
        return cfReportFundraiserCommunicateDAO.updateConnectStatus(id, connectStatus);
    }

    @Override
    public int updateAnswer(long id, int connectStatus, String answerIds) {
        if(id <= 0 || StringUtils.isEmpty(answerIds)){
            return 0;
        }
        return cfReportFundraiserCommunicateDAO.updateAnswer(id, connectStatus, answerIds);
    }

    @Override
    public List<CfFundraiserCommunicateDO> query(int caseId) {
        if(caseId <= 0){
            return Lists.newArrayList();
        }
        return cfReportFundraiserCommunicateDAO.query(caseId);
    }

    @Override
    public CfFundraiserCommunicateDO queryByIdAndCase(long id, int caseId) {
        if(id <= 0 || caseId <= 0){
            return null;
        }

        return cfReportFundraiserCommunicateDAO.queryByIdAndCase(id, caseId);
    }

    @Override
    public CfFundraiserCommunicateDO queryById(long id) {
        if(id <= 0){
            return null;
        }
        return cfReportFundraiserCommunicateDAO.queryById(id);
    }

    @Override
    public CfFundraiserCommunicateDO getByMobileAndCaseId(int caseId, String mobile) {
        return cfReportFundraiserCommunicateDAO.getByMobileAndCaseId(caseId,mobile);
    }
}
