package com.shuidihuzhu.cf.biz.admin.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.admin.ChuciAnalyticsBiz;
import com.shuidihuzhu.cf.biz.admin.UserCommentBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.adminpure.enums.ContentTo1V1ReasonEnum;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.feign.CaseInfoApproveStageFeignClient;
import com.shuidihuzhu.cf.client.material.feign.CfMaterialReadClient;
import com.shuidihuzhu.cf.client.material.model.materialField.MaterialExtKeyConst;
import com.shuidihuzhu.cf.constants.AdminUserIDConstants;
import com.shuidihuzhu.cf.delegate.SeaAccountDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.domain.caseinfo.CaseInfoApproveStageDO;
import com.shuidihuzhu.cf.enums.InitialAudit.InitialAuditNoSmartReason;
import com.shuidihuzhu.cf.enums.admin.UserCommentSourceEnum;
import com.shuidihuzhu.cf.model.admin.UserComment;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.param.InitialAuditCreateOrderParam;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditPreModifyService;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialPreModifyHandleHistory;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialPreModifyHandleVo;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.workorder.model.ChuciWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import com.shuidihuzhu.data.analytics.javasdk.core.UserTagTypeEnum;
import com.shuidihuzhu.data.servicelog.meta.cf.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @DATE 2019/6/25
 */
@Service("chuciAnalyticsBiz")
@Slf4j
public class ChuciAnalyticsBizImpl implements ChuciAnalyticsBiz {

    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Autowired
    private AdminCfInfoExtBiz cfInfoExtBiz;

    @Autowired
    private IRiskDelegate riskDelegate;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private Analytics analytics;

    @Resource
    private CfMaterialReadClient cfMaterialReadClient;
    @Resource
    private InitialAuditPreModifyService initialAuditPreModifyService;
    @Resource
    private IRiskDelegate firstApproveBiz;
    @Resource
    private UserCommentBiz userCommentBiz;

    @Override
    public void initialVerifySubmit(InitialAuditCreateOrderParam initialAuditCreateOrderParam) {

        Pair<Map<String, Object>,CrowdfundingInfo> pair = getMap(initialAuditCreateOrderParam.getCaseId());
        if (pair == null){
            return;
        }

        Map<String, Object> map = pair.getKey();
        CrowdfundingInfo info = pair.getValue();

        map.put("work_order_id",initialAuditCreateOrderParam.getWorkOrderId());
        map.put("target_amount",info.getTargetAmount());
        map.put("title",info.getTitle());
        map.put("content",info.getContent());
        map.put("launch_channel",info.getChannel());

        // 身份证号和关系的赋值
        setMaterial(map,initialAuditCreateOrderParam.getCaseId());

        InitialWorkOrderCreate iwoc = new InitialWorkOrderCreate();
        try {
            iwoc.setTarget_amt(Long.valueOf(info.getTargetAmount()));
            iwoc.setCase_id(StringUtils.trimToEmpty((String)map.get("case_id")));

            Optional<Integer> infoId = Optional.ofNullable((Integer)map.get("info_id"));
            iwoc.setInfo_id(Long.valueOf(infoId.orElse(0)));

            iwoc.setUser_encrypt_mobile(StringUtils.trimToEmpty((String)map.get("cryptoMobile")));
            iwoc.setLaunch_encrypt_idcard(StringUtils.trimToEmpty((String)map.get("id_card")));
            iwoc.setCase_title(info.getTitle());
            iwoc.setCase_content(info.getContent());

            String relation = Optional.ofNullable((Integer)map.get("relation"))
                    .map(p -> String.valueOf(p)).orElse("");
            iwoc.setLaunch_patient_relation(relation);

            iwoc.setWork_order_id(initialAuditCreateOrderParam.getWorkOrderId());

            Optional<Integer> isBuildPoorCard = Optional.ofNullable((Integer)map.get("is_bulid_poor_card"));
            iwoc.setIs_bulid_poor_card(Long.valueOf(isBuildPoorCard.orElse(0)));

            CfInfoExt ext = cfInfoExtBiz.getByInfoUuid(info.getInfoId());
            if (ext != null){
                Optional<String> channelOptional = Optional.ofNullable(ext.getPrimaryChannel());
                iwoc.setPrimary_channel(channelOptional.orElse(""));
            } else {
                iwoc.setPrimary_channel("");
            }
            iwoc.setUser_tag(String.valueOf(info.getUserId()));
            iwoc.setUser_tag_type(UserTagTypeEnum.userid);
            iwoc.setInitial_no_smart_reason(initialAuditCreateOrderParam.getNoSmartAuditReason());
            iwoc.setDisease_name(initialAuditCreateOrderParam.getDiseaseName());
            iwoc.setRisk_work_order_reason(initialAuditCreateOrderParam.getRiskWorkOrderReason());

            analytics.track(iwoc);
            log.info("大数据打点上报,初审工单创建:{}", JSONObject.toJSONString(iwoc));
        } catch (Exception e) {
            log.error("大数据打点上报异常,初审工单创建:{}", JSONObject.toJSONString(iwoc), e);
        }

        try {
            aiErCiNoSmartReason(initialAuditCreateOrderParam);
        } catch (Exception e) {
            log.error("记录未进入智能审核原因 {} {}", initialAuditCreateOrderParam,  e);
        }
    }

    private void aiErCiNoSmartReason(InitialAuditCreateOrderParam initialAuditCreateOrderParam) {
        ChuciWorkOrder chuciWorkOrder = initialAuditCreateOrderParam.getChuciWorkOrder();
        if (Objects.isNull(chuciWorkOrder)) {
            return;
        }
        if (chuciWorkOrder.getOrderType() != WorkOrderType.ai_erci.getType() && StringUtils.equals(initialAuditCreateOrderParam.getNoSmartAuditReason(), InitialAuditNoSmartReason.SUCCESS.getMsg())) {
            return;
        }
        if (StringUtils.isEmpty(initialAuditCreateOrderParam.getNoSmartAuditReason())) {
            return;
        }

        UserComment comment = new UserComment();
        comment.setOperatorId(AdminUserIDConstants.SYSTEM);
        comment.setCaseId(chuciWorkOrder.getCaseId());
        comment.setCommentSource(UserCommentSourceEnum.INITIAL_AUDIT.getCode());
        comment.setCommentType(UserCommentSourceEnum.CommentType.SUBMIT_VALUE_REMARK.getCode());
        comment.setOperateMode("未进入智能审核原因：" + initialAuditCreateOrderParam.getNoSmartAuditReason());
        comment.setWorkOrderId(initialAuditCreateOrderParam.getWorkOrderId());
        comment.setComment("");
        comment.setOperateDesc("");
        userCommentBiz.insert(comment);

    }

    @Override
    public void initialVerify(int caseId, long workOrderId, long operatorId, int result){


        Pair<Map<String, Object>, CrowdfundingInfo> pair = getMap(caseId);
        if (Objects.isNull(pair)){
            return;
        }
        CfFirsApproveMaterial cfFirsApproveMaterial = firstApproveBiz.getCfFirsApproveMaterialByInfoId(caseId);
        Map<String, Object> map = pair.getKey();
        CrowdfundingInfo info = pair.getValue();

        InitialVerify iv = new InitialVerify();
        try {
            if (result == HandleResultEnum.audit_pass.getType()){
                iv.setAudit_result("passed");
            }
            if (result == HandleResultEnum.audit_reject.getType()){
                iv.setAudit_result("reject");
            }
            if (result == HandleResultEnum.stop_case.getType()){
                iv.setAudit_result("stop");
            }
            iv.setReject_work_order_id(0L);
            iv.setWork_order_id(workOrderId);
            iv.setAuditor_id(operatorId);
            iv.setCase_id(StringUtils.trimToEmpty((String)map.get("case_id")));

            Optional<Integer> infoId = Optional.ofNullable((Integer)map.get("info_id"));
            iv.setInfo_id(Long.valueOf(infoId.orElse(0)));
            iv.setUser_encrypt_mobile(StringUtils.trimToEmpty((String)map.get("cryptoMobile")));
            iv.setUser_tag(String.valueOf(info.getUserId()));
            iv.setUser_tag_type(UserTagTypeEnum.userid);
            if (Objects.nonNull(cfFirsApproveMaterial)) {
                iv.setUser_identity_type((long) cfFirsApproveMaterial.getUserRelationType());
                iv.setPatient_born_card(cfFirsApproveMaterial.getPatientBornCard());
                iv.setPatient_encrypt_idcard(cfFirsApproveMaterial.getPatientCryptoIdcard());
            }

            analytics.track(iv);
            log.info("大数据打点上报,审核人员对案例进行初次审核:{}", JSONObject.toJSONString(iv));
        } catch (Exception e) {
            log.error("大数据打点上报异常,审核人员对案例进行初次审核:{}", JSONObject.toJSONString(iv), e);
        }

    }


    @Override
    public void rejectWorkOrderTransfer(int caseId,long workOrderId, String oper) {

        Pair<Map<String, Object>,CrowdfundingInfo> pair = getMap(caseId);
        if (pair == null){
            return;
        }
        Map<String, Object> map = pair.getKey();

        Map<String, Object> rejectMap = Maps.newHashMap(map);

        CrowdfundingInfo info = pair.getValue();

        map.put("work_order_id",workOrderId);
        map.put("group",oper);

        RejectWorkOrderTransfer rwot = new RejectWorkOrderTransfer();
        try {
            rwot.setReject_work_order_id(workOrderId);
            rwot.setCase_id(StringUtils.trimToEmpty((String)map.get("case_id")));

            Optional<Integer> infoId = Optional.ofNullable((Integer)map.get("info_id"));
            rwot.setInfo_id(Long.valueOf(infoId.orElse(0)));

            rwot.setUser_encrypt_mobile(StringUtils.trimToEmpty((String)map.get("cryptoMobile")));
            rwot.setEmp_group(StringUtils.trimToEmpty(oper));
            rwot.setUser_tag(String.valueOf(info.getUserId()));
            rwot.setUser_tag_type(UserTagTypeEnum.userid);

            analytics.track(rwot);
            log.info("大数据打点上报,驳回工单流转:{}", JSONObject.toJSONString(rwot));
        } catch (Exception e) {
            log.error("大数据打点上报异常,驳回工单流转:{}", JSONObject.toJSONString(rwot), e);
        }

        if ("1v1".equals(oper)){
            rejectWorkOrderClose(rejectMap,info.getUserId(),workOrderId,"自动异常关闭-微信测试",caseId);
        }
    }

    @Override
    public void content1v1Verify(InitialAuditCreateOrderParam initialAuditCreateOrderParam) {
        if (Objects.isNull(initialAuditCreateOrderParam)) {
            return;
        }
        int caseId = initialAuditCreateOrderParam.getCaseId();
        ChuciWorkOrder chuciWorkOrder = initialAuditCreateOrderParam.getChuciWorkOrder();
        if (Objects.isNull(chuciWorkOrder)) {
            return;
        }
        // 审核工单不埋点，只有真正初审的在用工单才埋点上报
        if (WorkOrderType.shenhe.getType() == chuciWorkOrder.getOrderType()) {
            return;
        }
        RpcResult<Map<String, List<String>>> mapRpcResult = cfMaterialReadClient.selectValueByFields(caseId, Collections.singletonList(MaterialExtKeyConst.INIT_AUDIT_CONTENT_TO_1V1));
        if (Objects.isNull(mapRpcResult) || mapRpcResult.isFail() || MapUtils.isEmpty(mapRpcResult.getData())) {
            return;
        }
        boolean aBoolean = mapRpcResult.getData()
                .get(MaterialExtKeyConst.INIT_AUDIT_CONTENT_TO_1V1)
                .stream()
                .map(Boolean::valueOf)
                .findFirst()
                .orElse(false);
        if (!aBoolean) {
            return;
        }
        List<InitialPreModifyHandleHistory> initialPreModifyHandleHistories = initialAuditPreModifyService.selectPreModifyHistory(caseId);

        ContentLimitTo1v1Service contentLimitTo1v1Service = new ContentLimitTo1v1Service();
        contentLimitTo1v1Service.setCase_id(String.valueOf(caseId));
        contentLimitTo1v1Service.setKey(ContentTo1V1ReasonEnum.CREATE_WORK_ORDER_CONTENT_VERSION.name());
        contentLimitTo1v1Service.setValue(String.valueOf(CollectionUtils.isNotEmpty(initialPreModifyHandleHistories)));
        contentLimitTo1v1Service.setUser_tag(String.valueOf(caseId));
        contentLimitTo1v1Service.setUser_tag_type(UserTagTypeEnum.userid);
        analytics.track(contentLimitTo1v1Service);
    }

    /**
     * 驳回工单关闭
     * @param map
     * @param userId
     * @param workOrderId
     * @param closeCause
     * @param caseId
     */
    void rejectWorkOrderClose(Map<String, Object> map,long userId,long workOrderId,String closeCause,int caseId) {
        RejectWorkOrderClose rwoc = new RejectWorkOrderClose();
        try {
            rwoc.setClose_reason(closeCause);
            rwoc.setWork_order_id(workOrderId);
            rwoc.setCase_id(StringUtils.trimToEmpty((String)map.get("case_id")));

            Optional<Integer> infoId = Optional.ofNullable((Integer)map.get("info_id"));
            rwoc.setInfo_id(Long.valueOf(infoId.orElse(0)));
            rwoc.setUser_encrypt_mobile(StringUtils.trimToEmpty((String)map.get("cryptoMobile")));
            rwoc.setUser_tag(String.valueOf(userId));
            rwoc.setUser_tag_type(UserTagTypeEnum.userid);

            analytics.track(rwoc);
            log.info("大数据打点上报,驳回工单关闭:{}", JSONObject.toJSONString(rwoc));
        } catch (Exception e) {
            log.error("大数据打点上报异常,驳回工单关闭:{}", JSONObject.toJSONString(rwoc), e);
        }
    }

    /**
     * 生成驳回工单
     * @param userId
     * @param map
     */
    void rejectWorkOrderCreate(long userId,Map<String, Object> map,int caseId){

        RejectWorkOrderCreate rwoc = new RejectWorkOrderCreate();
        try {
            rwoc.setCase_id(StringUtils.trimToEmpty((String)map.get("case_id")));

            Optional<Integer> infoId = Optional.ofNullable((Integer)map.get("info_id"));
            rwoc.setInfo_id(Long.valueOf(infoId.orElse(0)));

            Optional<Long> workOrderId = Optional.ofNullable((Long)map.get("work_order_id"));
            rwoc.setReject_work_order_id(workOrderId.orElse(0l));
            rwoc.setUser_encrypt_mobile(StringUtils.trimToEmpty((String)map.get("cryptoMobile")));
            rwoc.setUser_tag(String.valueOf(userId));
            rwoc.setUser_tag_type(UserTagTypeEnum.userid);

            analytics.track(rwoc);
            log.info("大数据打点上报,驳回工单创建:{}", JSONObject.toJSONString(rwoc));
        } catch (Exception e) {
            log.error("大数据打点上报异常,驳回工单创建:{}", JSONObject.toJSONString(rwoc), e);
        }
    }

    /**
     * 处理驳回工单
     */
    void auditorCallOut(long userId,AuditorCallOut auditorCallOut,int caseId){
        analytics.track(auditorCallOut);
        log.info("auditorCallOut caseId={},map={}",caseId, JSON.toJSON(auditorCallOut));
    }


    private Pair<Map<String, Object>,CrowdfundingInfo> getMap(int caseId){

        Map<String, Object> map = Maps.newHashMap();

        CrowdfundingInfo info = crowdfundingDelegate.getFundingInfoById(caseId);
        if (info == null){
            log.error("initialVerifySubmit info==null caseId={}",caseId);
            return null;
        }
        long userId = info.getUserId();
        map.put("case_id",info.getInfoId());
        map.put("info_id",info.getId());
        map.put("user_id",userId);

        UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByUserId(userId);
        if (userInfoModel != null){
            map.put("mobile", shuidiCipher.decrypt(userInfoModel.getCryptoMobile()));
            map.put("cryptoMobile",userInfoModel.getCryptoMobile());
        }
        return new ImmutablePair(map, info);
    }


    private void setMaterial(Map<String, Object> map,int caseId){

        CfFirsApproveMaterial material = riskDelegate.getCfFirsApproveMaterialByInfoId(caseId);
        if (material != null){
            map.put("relation",material.getUserRelationType());
            map.put("id_card", StringUtils.isNotEmpty(material.getPatientCryptoIdcard())?material.getPatientCryptoIdcard():material.getSelfCryptoIdcard());
            map.put("is_bulid_poor_card",material.getPoverty());
        }
    }

}
