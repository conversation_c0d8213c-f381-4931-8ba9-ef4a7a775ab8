package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonTag;

import java.util.List;
import java.util.Set;

/**
 * Created by Ahrievil on 2017/7/26
 */
public interface CfRefuseReasonTagBiz {
    int insertOne(CfRefuseReasonTag cfRefuseReasonTag);
    List<CfRefuseReasonTag> selectAllWithUuid(int start, int size, String infoUuid);
    List<CfRefuseReasonTag> selectByDataType(int dataType);
    CfRefuseReasonTag selectById(int id);
    int updateReasonIds(String reasonIds, int id);
    List<CfRefuseReasonTag> selectByTagIds(Set<Integer> set);
    CfRefuseReasonTag selectByTagId(int id);

    void addReasonTag(int userId, int dataType, String describe);
    void updateReasonTagDataStep(int userId, int upId, int downId, int operateType);
}
