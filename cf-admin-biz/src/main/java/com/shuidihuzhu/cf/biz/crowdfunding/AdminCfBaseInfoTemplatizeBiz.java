package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplateOperatorHistory;
import com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplatize;

import java.util.List;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date : 2018/9/10 19:34
 */
public interface AdminCfBaseInfoTemplatizeBiz {

    List<CfBaseInfoTemplatize> selectByPage(String context, int contentType, int relationType, int channelType, int current, int pageSize,
                                            List<Integer> ids);

    CfBaseInfoTemplatize selectById(long id);

    int insertOne(CfBaseInfoTemplatize cfBaseInfoTemplatize);

    int update(CfBaseInfoTemplatize cfBaseInfoTemplatize);

    int delete(long id);

    int insertOperatorHistory(CfBaseInfoTemplateOperatorHistory cfBaseInfoTemplateOperatorHistory);

    List<CfBaseInfoTemplateOperatorHistory> selectByCfBaseTemplateId(long cfBaseTemplateId);

    List<CfBaseInfoTemplatize> get1v1TemplateList(String context, int contentType, Integer relationType, int channelType, List<Integer> ids, String diseaseName, Integer age, int offset, int pageSize);

    int get1v1TemplateCount(String context, int contentType, Integer relationType, int channelType, List<Integer> ids, String diseaseName, Integer age);
}
