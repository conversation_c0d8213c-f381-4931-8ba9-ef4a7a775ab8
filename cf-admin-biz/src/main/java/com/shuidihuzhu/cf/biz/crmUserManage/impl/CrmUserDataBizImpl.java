package com.shuidihuzhu.cf.biz.crmUserManage.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Ordering;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.model.UserThirdModel;
import com.shuidihuzhu.cf.biz.crmUserManage.CrmUserDataBiz;
import com.shuidihuzhu.cf.biz.crmUserManage.CrmUserManageService;
import com.shuidihuzhu.cf.biz.customer.CfUdeskCustomerInfoBiz;
import com.shuidihuzhu.cf.biz.customer.CfUdeskSessionRecordBiz;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.client.stat.enums.StatDataEnum;
import com.shuidihuzhu.cf.client.stat.feign.ICfStatClient;
import com.shuidihuzhu.cf.client.stat.model.CfStatResult;
import com.shuidihuzhu.cf.customer.CfUdeskCustomerInfo;
import com.shuidihuzhu.cf.dao.crmUserManage.UserRoleDao;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.delegate.service.UserThirdServiceBiz;
import com.shuidihuzhu.cf.delegate.service.WxUserEventStatusServiceBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.model.CrmUserManage.UserManage;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.approve.lifecircle.CaseApproveLifeCircleService;
import com.shuidihuzhu.cf.vo.approve.ApproveLifeCircleVO;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackApiClient;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackHistoryMarkClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewBaseInfoDO;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewtrackHistoryMarkModel;
import com.shuidihuzhu.client.dataservice.ds.v1.DsApiClient;
import com.shuidihuzhu.client.model.PhoneNumberInfoDO;
import com.shuidihuzhu.client.model.Response;
import com.shuidihuzhu.common.web.model.IdcardInfoExtractor;
import com.shuidihuzhu.wx.grpc.model.WxMpSubscribeModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CrmUserDataBizImpl implements CrmUserDataBiz {

    @Autowired
    private DsApiClient dsApiClient;
    @Autowired
    private CfUdeskCustomerInfoBiz udeskCustomerInfoBiz;
    @Autowired
    private CfUdeskSessionRecordBiz udeskSessionRecordBiz;
    @Autowired
    private CrowdfundingFeignClient cfFeignClient;
    @Autowired
    private ICfStatClient cfStatClient;
    @Resource
    private UserThirdServiceBiz userThirdServiceBiz;
    @Autowired
    private CaseApproveLifeCircleService lifeCircleService;
    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private UserRoleDao userRoleDao;
    @Autowired
    private CfClewtrackHistoryMarkClient clewtrackMarkClient;
    @Resource
    private WxUserEventStatusServiceBiz wxUserEventStatusServiceBiz;
    @Autowired
    private CfClewtrackApiClient cfClewtrackApiClient;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;
    @Autowired
    private CrmUserManageService crmUserManageService;

    private static final int THIRD_TYPE_WX = 3;

    @Override
    public List<UserManage.UserData> queryUserDataByUuid(String uuid) {
        List<UserManage.UserAccount> accountList = crmUserManageService.selectByUuids(Lists.newArrayList(uuid));

        if (CollectionUtils.isEmpty(accountList)) {
            return Lists.newArrayList();
        }

        List<UserManage.UserData> userDateList = Lists.newArrayList();
        for (UserManage.UserAccount account : accountList) {
            userDateList.add(queryUserData(account, 0));
        }

        return userDateList;
    }

    @Override
    public UserManage.UserData queryUserDataByClewId(long clewId) {
        com.shuidihuzhu.common.web.model.Response<List<CfClewBaseInfoDO>> listResponse = cfClewtrackApiClient.listCfClewBaseInfo(Lists.newArrayList(clewId));
        if (listResponse.notOk() || CollectionUtils.isEmpty(listResponse.getData())) {
            log.warn("获取线索信息失败,clewId:{}", clewId);
            return null;
        }
        CfClewBaseInfoDO cfClewBaseInfoDO = listResponse.getData().get(0);
        List<UserManage.UserAccount> accountList = Lists.newArrayList();
        if (StringUtils.isNotEmpty(cfClewBaseInfoDO.getUuid())) {
            accountList = crmUserManageService.selectByUuids(Sets.newHashSet(cfClewBaseInfoDO.getUuid()));
        } else {
            accountList = crmUserManageService.selectByMobiles(Sets.newHashSet(cfClewBaseInfoDO.getEncryptPhone()));
        }
        if (CollectionUtils.isEmpty(accountList)) {
            return null;
        }
        List<UserInfoModel> userInfoModels = accountList.stream().map(userAccount -> {
            String decryptMobile = shuidiCipher.decrypt(userAccount.getCryptoMobile());
            return userInfoServiceBiz.getUserInfoByMobile(decryptMobile);
        }).filter(Objects::nonNull).collect(Collectors.toList());

        UserManage.UserAccount account = accountList.get(0);
        com.shuidihuzhu.common.web.model.Response<CfClewtrackHistoryMarkModel> clewResult =
                clewtrackMarkClient.getClewUserInfoForCrmUserManager(account.getPersonId(), account.getUuid());
        CfClewtrackHistoryMarkModel markModel = null;
        if (clewResult.notOk() || clewResult.getData() == null) {
            log.warn("查询线索信息失败:{}", JSON.toJSONString(clewResult));
        }
        markModel = clewResult.getData();
        UserManage.UserData userData = buildUserDataByUserIds(userInfoModels.stream().map(UserInfoModel::getUserId).collect(Collectors.toList()));
        userData.setClewDetail(UserManage.UserClewData.convertFromClewMarkModel(markModel, clewId, cfClewBaseInfoDO));
        userData.setClewData(UserManage.OutCallClewData.convertFromClewMarkModel(markModel));
        return userData;
    }


    //多userId合并数据
    public UserManage.UserData buildUserDataByUserIds(List<Long> userIds) {

        UserManage.UserData userData = new UserManage.UserData();
        if (CollectionUtils.isEmpty(userIds)) {
            return userData;
        }
        List<UserManage.CaseInfo> caseInfoList = Lists.newArrayList();
        List<UserManage.DonorData> donorDataList = Lists.newArrayList();
        List<UserManage.UdeskSessionVo> udeskSessionVoList = Lists.newArrayList();
        for (Long userId : userIds) {
            caseInfoList.addAll(listUserCaseInfo(userId));
            donorDataList.add(getUserDonorData(userId));
            udeskSessionVoList.add(queryUdeskQueryByUserId(userId));
        }
        //merge postHandle userInfo
        List<UserManage.CaseInfo> resortCaseInfo = caseInfoList.stream()
                .sorted(Ordering.natural().reverse().nullsLast().onResultOf(UserManage.CaseInfo::getCaseRaiseTime))
                .collect(Collectors.toList());


        UserManage.DonorData mergedDonorData = new UserManage.DonorData();
        boolean subscribe = false;
        Date subscribeTime = null;
        int donateCount = 0;
        long donateAmount = 0L;
        int shareCount = 0;
        int reportCount = 0;
        int verifyCount = 0;
        for (UserManage.DonorData donorData : donorDataList) {
            subscribe = (subscribe || donorData.isSubscribe());
            Date thisDonorSubscribeTime = donorData.getSubscribeTime();
            if (thisDonorSubscribeTime != null) {
                Date lastDate = null;
                if (donorData.isSubscribe()) {
                    if (subscribeTime != null) {
                        lastDate = thisDonorSubscribeTime.after(subscribeTime) ? thisDonorSubscribeTime : subscribeTime;
                    }
                    subscribeTime = subscribeTime == null ? thisDonorSubscribeTime : lastDate;
                }
            }
            donateCount += donorData.getDonateCount();
            donateAmount += donorData.getDonateAmount();
            shareCount += donorData.getShareCount();
            reportCount += donorData.getReportCount();
            verifyCount += donorData.getVerifyCount();
        }
        mergedDonorData.setSubscribe(subscribe);
        mergedDonorData.setSubscribeTime(subscribeTime);
        mergedDonorData.setDonateCount(donateCount);
        mergedDonorData.setDonateAmount(donateAmount);
        mergedDonorData.setShareCount(shareCount);
        mergedDonorData.setReportCount(reportCount);
        mergedDonorData.setVerifyCount(verifyCount);

        UserManage.UdeskSessionVo mergedUdeskSessionVo = new UserManage.UdeskSessionVo();
        int totalConsultSize = 0;
        long lastConsultTime = 0L;
        String lastConsultType = null;
        for (UserManage.UdeskSessionVo sessionVo : udeskSessionVoList) {
            totalConsultSize += sessionVo.getTotalConsultSize();
            lastConsultTime = Long.max(sessionVo.getLastConsultTime(), lastConsultTime);
            if (lastConsultTime == sessionVo.getLastConsultTime()) {
                lastConsultType = sessionVo.getLastConsultType();
            }
        }
        mergedUdeskSessionVo.setTotalConsultSize(totalConsultSize);
        mergedUdeskSessionVo.setLastConsultTime(lastConsultTime);
        mergedUdeskSessionVo.setLastConsultType(lastConsultType);

        long count = caseInfoList.stream().filter(UserManage.CaseInfo::isValidCaseStatus).count();

        userData.setValidCaseCount(count);
        userData.setCaseInfoList(resortCaseInfo);
        userData.setDonorData(mergedDonorData);
        userData.setUdeskSessionVo(mergedUdeskSessionVo);
        return userData;
    }


    public UserManage.UserData queryUserData(UserManage.UserAccount account, long clewId) {

        UserManage.UserData userDate = new UserManage.UserData();
        String mobile = shuidiCipher.decrypt(account.getCryptoMobile());
        userDate.setMobile(mobile);

        UserInfoModel userModel = userInfoServiceBiz.getUserInfoByMobile(mobile);
        if (userModel == null) {
            log.warn("没有从账号系统找到用户的数据.mobile:{}", mobile);
        }

        //userDate.setRaiseBaseData(queryRaiseBaseData(account, mobile, userModel));

        if (userModel != null) {
            long userId = userModel.getUserId();
            com.shuidihuzhu.common.web.model.Response<CfClewtrackHistoryMarkModel> clewResult =
                    clewtrackMarkClient.getClewUserInfoForCrmUserManager(account.getPersonId(), account.getUuid());
            CfClewtrackHistoryMarkModel markModel = null;
            if (clewResult.notOk() || clewResult.getData() == null) {
                log.warn("查询线索信息失败:{}", JSON.toJSONString(clewResult));
            }
            markModel = clewResult.getData();
            userDate.setCaseList(getRaiseCaseData(userId));
            userDate.setCaseInfoList(listUserCaseInfo(userId));
            //线索记录
            userDate.setClewDetail(UserManage.UserClewData.convertFromClewMarkModel(markModel, clewId, null));
            userDate.setDonorData(getUserDonorData(userId));
            userDate.setClewData(UserManage.OutCallClewData.convertFromClewMarkModel(markModel));
            userDate.setUdeskSessionVo(queryUdeskQueryByUserId(userId));
        }

        return userDate;
    }


    private UserManage.RaiseBaseData queryRaiseBaseData(UserManage.UserAccount account,
                                                        String mobile,
                                                        UserInfoModel userModel) {
        UserManage.RaiseBaseData baseInfoData = new UserManage.RaiseBaseData();

        baseInfoData.setMobile(mobile);
        baseInfoData.setMobileBelong(queryMobileBelong(mobile));


        if (userModel != null) {
            baseInfoData.setUserId(userModel.getUserId());
            baseInfoData.setNickName(userModel.getNickname());

            List<UserThirdModel> thirdModelList = userThirdServiceBiz.getByUserId(userModel.getUserId());
            Set<String> openIds = Sets.newHashSet();
            Set<String> unionIds = Sets.newHashSet();
            for (UserThirdModel thirdModel : thirdModelList) {
                openIds.add(thirdModel.getOpenId());
                unionIds.add(thirdModel.getUnionId());
            }

            baseInfoData.setUnionIds(unionIds);
            baseInfoData.setOpenIds(openIds);
        }

        if (StringUtils.isNotBlank(account.getCryptoIdCard())) {

            String idCard = shuidiCipher.decrypt(account.getCryptoIdCard());
            baseInfoData.setIdCard(idCard);
            baseInfoData.setUserName(account.getUserName());
            baseInfoData.setProvince(queryIdCardProvince(idCard));
        }

        // TODO 微信外部的

        baseInfoData.setPersonId(account.getPersonId());
        baseInfoData.setUserRoleModels(userRoleDao.selectByMobiles(Lists.newArrayList(oldShuidiCipher.aesEncrypt(mobile))));

        return baseInfoData;
    }


    private List<UserManage.CaseInfo> listUserCaseInfo(long userId) {
        DateTime now = DateTime.now();
        FeignResponse<List<CrowdfundingInfo>> currentCases = cfFeignClient.getCrowdfundingListByUserId(userId);
        List<UserManage.CaseInfo> caseInfoList = Lists.newArrayList();
        if (currentCases == null || CollectionUtils.isEmpty(currentCases.getData())) {
            return caseInfoList;
        }
        for (CrowdfundingInfo cfInfo : currentCases.getData()) {
            FeignResponse<CfInfoExt> cfInfoExt = cfFeignClient.getCfInfoExtByCaseId(cfInfo.getId());
            UserManage.CaseInfo caseInfo = new UserManage.CaseInfo();
            caseInfo.setCaseId(cfInfo.getId());
            caseInfo.setTitle(cfInfo.getTitle());
            caseInfo.setFirstApproveStatus(Optional.ofNullable(cfInfoExt.getData()).map(CfInfoExt::getFirstApproveStatus).orElse(0));
            caseInfo.setTargetAmount(cfInfo.getTargetAmount());
            caseInfo.setAmount(cfInfo.getAmount());
            caseInfo.setCaseStatus(cfInfo.getStatus().value());
            caseInfo.setCaseRaiseTime(cfInfo.getCreateTime());
            caseInfo.setInfoUuid(cfInfo.getInfoId());
            if (cfInfo.getEndTime() != null && cfInfo.getEndTime().before(now.toDate())) {
                log.info("案例:{}已结束", cfInfo.getId());
                caseInfo.setValidCaseStatus(false);
                caseInfo.setCaseStatus(CrowdfundingStatus.FINISHED.value());
            } else {
                caseInfo.setValidCaseStatus(true);
            }
            caseInfoList.add(caseInfo);
        }
        return caseInfoList;
    }

    private List<UserManage.RaiseCaseData> getRaiseCaseData(long userId) {
        FeignResponse<List<CrowdfundingInfo>> currentCases = cfFeignClient.getCurrentCaseByUserId(userId);

        List<UserManage.RaiseCaseData> caseDataList = Lists.newArrayList();
        if (currentCases == null || CollectionUtils.isEmpty(currentCases.getData())) {
            return caseDataList;
        }

        for (CrowdfundingInfo info : currentCases.getData()) {
            UserManage.RaiseCaseData currCase = new UserManage.RaiseCaseData();

            currCase.setCaseId(info.getId());
            currCase.setAmount(currCase.getAmount());
            OpResult<List<ApproveLifeCircleVO>> circleResult = lifeCircleService.getLifeCircle(info.getId());

            if (circleResult != null && CollectionUtils.isNotEmpty(circleResult.getData())) {
                currCase.setLifeCircleList(circleResult.getData());
            }
            caseDataList.add(currCase);
        }

        return caseDataList;
    }

    private UserManage.DonorData getUserDonorData(long userId) {

        UserManage.DonorData donor = new UserManage.DonorData();
        //用户是否关注公众号信息
        WxMpSubscribeModel subscribeInfo = wxUserEventStatusServiceBiz.getSubscribeByUserId(userId, THIRD_TYPE_WX);
        if (subscribeInfo == null) {
            donor.setSubscribe(false);
        } else {
            donor.setSubscribe(subscribeInfo.isSubscribe());
            donor.setSubscribeTime(subscribeInfo.getSubscribeTime());
        }

        Map<StatDataEnum, CfStatResult> statResult = listCfCaseStatus(userId);


        CfStatResult stat = statResult.get(StatDataEnum.DONATE_COUNT);
        if (stat != null && stat.getValue() != null) {
            donor.setDonateCount(Integer.valueOf(stat.getValue()));
        }

        stat = statResult.get(StatDataEnum.DONATE_MONEY);
        if (stat != null && stat.getValue() != null) {
            donor.setDonateAmount(Long.valueOf(stat.getValue()));
        }
        stat = statResult.get(StatDataEnum.SHARE_COUNT);
        if (stat != null && stat.getValue() != null) {
            donor.setShareCount(Integer.valueOf(stat.getValue()));
        }
        stat = statResult.get(StatDataEnum.REPORT_COUNT);
        if (stat != null && stat.getValue() != null) {
            donor.setReportCount(Integer.valueOf(stat.getValue()));
        }
        stat = statResult.get(StatDataEnum.VERIFICATION_COUNT);
        if (stat != null && stat.getValue() != null) {
            donor.setVerifyCount(Integer.valueOf(stat.getValue()));
        }

        return donor;
    }

    private Map<StatDataEnum, CfStatResult> listCfCaseStatus(long userId) {

        RpcResult<List<CfStatResult>> cfStatResult = cfStatClient.listCfStatResult(Lists.newArrayList(
                StatDataEnum.DONATE_COUNT.getType(), StatDataEnum.DONATE_MONEY.getType(), StatDataEnum.SHARE_COUNT.getType(),
                StatDataEnum.REPORT_COUNT.getType(), StatDataEnum.VERIFICATION_COUNT.getType()
        ), userId, 0);
        log.info("查询用户相关的数据. userId:{} result:{}", userId, cfStatResult);

        Map<StatDataEnum, CfStatResult> statResult = Maps.newHashMap();
        if (cfStatResult == null || CollectionUtils.isEmpty(cfStatResult.getData())) {
            return statResult;
        }

        for (CfStatResult stat : cfStatResult.getData()) {
            statResult.put(StatDataEnum.parse(stat.getType()), stat);
        }
        return statResult;
    }

    // 身份证的省份
    private String queryIdCardProvince(String idCard) {
        if (StringUtils.isBlank(idCard)) {
            return "";
        }

        IdcardInfoExtractor idCardInfo = new IdcardInfoExtractor(idCard);
        if (idCardInfo != null) {
            return StringUtils.trimToEmpty(idCardInfo.getProvince());
        }

        return "";
    }

    // 查询手机的归属地
    private String queryMobileBelong(String mobile) {

        Response<PhoneNumberInfoDO> phoneNumberInfo = dsApiClient.getPhoneNumberInfo(Long.valueOf(mobile));
        String mobileOwnerShip = "";
        if (phoneNumberInfo.getCode() == 0) {
            PhoneNumberInfoDO data = phoneNumberInfo.getData();
            mobileOwnerShip = Optional.ofNullable(data).map(PhoneNumberInfoDO::getPhoneProvince).orElse("");
        }
        return mobileOwnerShip;
    }

    // 查询在线咨询数据
    private UserManage.UdeskSessionVo queryUdeskQueryByUserId(long userId) {
        List<CfUdeskCustomerInfo> allCustomerInfos = udeskCustomerInfoBiz.getUdeskCustomersByCfUserIds(Lists.newArrayList(userId));

        UserManage.UdeskSessionVo recordVo = new UserManage.UdeskSessionVo();

        if (CollectionUtils.isEmpty(allCustomerInfos)) {
            return recordVo;
        }

        List<Integer> udeskIds = allCustomerInfos.stream().map(CfUdeskCustomerInfo::getUdeskCustomerId).collect(Collectors.toList());

        int totalSize = udeskSessionRecordBiz.selectByCustomIdAndCreateAt(udeskIds, null);
        recordVo.setTotalConsultSize(totalSize);

        if (totalSize > 0) {
            UserManage.UdeskBizRecord lastRecord = udeskSessionRecordBiz.selectLastRecordByCustomId(udeskIds);
            if (lastRecord != null) {
                recordVo.setLastConsultTime(lastRecord.getCreatedAt() != null ? lastRecord.getCreatedAt().getTime() : 0);
                recordVo.setLastConsultType(lastRecord.getBizRecordClassify());
            }
        }
        return recordVo;
    }

}
