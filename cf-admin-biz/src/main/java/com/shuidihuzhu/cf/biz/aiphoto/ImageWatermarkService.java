package com.shuidihuzhu.cf.biz.aiphoto;

import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.client.material.model.CfBasicLivingGuardModel;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.model.admin.UgcWorkOrderVO;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCfFundUseAuditInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CfAttachmentVo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment;
import com.shuidihuzhu.cf.model.crowdfunding.vo.QueryListResultVo;
import com.shuidihuzhu.cf.model.report.AdminCfReportAddTrustVo;
import com.shuidihuzhu.cf.vo.CfFirsApproveMaterialVO;
import com.shuidihuzhu.cf.vo.admin.CfFirstApproveExt;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditCaseDetail;
import com.shuidihuzhu.cf.vo.crowdfunding.CfProgressVo;
import com.shuidihuzhu.cf.vo.crowdfunding.WorkOrderUgcBaseInfoVo;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface ImageWatermarkService {

    void fillBasicLivingWaterMark(int caseId, InitialAuditCaseDetail.CfBasicLivingGuardView guardView);

    void fillFirstMedicalWaterMark(int caseId, InitialAuditCaseDetail.FirstApproveCaseInfo approveCaseInfo);

    void fillCaseBaseWaterMark(int caseId, InitialAuditCaseDetail.CaseBaseInfo caseBaseInfo);

    void fillUgcBaseContentWatermark(int caseId, WorkOrderUgcBaseInfoVo ugcBaseVo);

    void fillUgcManageWatermark(List<Integer> ids, List<CfAttachmentVo> attachmentVos);

    void fillUgcProgressWatermark(int caseId, UgcWorkOrderVO progressVo);

    void fillFundUseAuditWatermark(List<AdminCfFundUseAuditInfo> auditInfoList);

    void fillAddTrustWatermark(AdminCfReportAddTrustVo trustVo);

    void fillProgressWatermarks(List<CfProgressVo> progressVos);

    void fillOrderListProgressWatermarks(List<QueryListResultVo> resultList);

    InitialAuditCaseDetail.CfBasicLivingGuardView getLivingGuardView(int caseId, CfBasicLivingGuardModel model);

    CfFirstApproveExt getFirstApproveExt(int caseId, CfFirsApproveMaterialVO vo);

    int getImageWatermark(Map<String, Integer> watermarks, String image);
}
