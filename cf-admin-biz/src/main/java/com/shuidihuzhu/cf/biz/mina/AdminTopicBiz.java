package com.shuidihuzhu.cf.biz.mina;

import com.shuidihuzhu.cf.model.miniprogram.CfTopic;
import com.shuidihuzhu.cf.vo.mina.CfMinaTopicDetailVo;

import java.util.List;

public interface AdminTopicBiz {
    List<CfTopic> getByTitle(List<Integer> phaseIds, String title, int pageNum, int pageSize);

    List<CfMinaTopicDetailVo> getTopicsPage(int pageNum, int pageSize, String title);

    List<CfMinaTopicDetailVo> getUnPublishTopics(int pageNum, int pageSize, String title);

    List<CfMinaTopicDetailVo> listByPhaseIds(List<Integer> phaseIds);

    int updatePhaseId(int topicId, int phaseId);

    //获取已发布的话题
    List<CfTopic> getPublishTopics(String title);

    //将话题发布id置成0
    int deletePhaseById(List<Integer> topicIds);

    int insertOne(CfTopic cfTopic);

    int deleteByPhaseId(int phaseId);
}
