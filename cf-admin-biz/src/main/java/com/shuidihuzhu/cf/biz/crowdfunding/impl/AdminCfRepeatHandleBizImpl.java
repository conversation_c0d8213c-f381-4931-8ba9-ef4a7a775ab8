package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.admin.AdminWorkOrderBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.client.subject.caseend.CaseEndClient;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfRepeatFinishInfoDAO;
import com.shuidihuzhu.cf.delegate.commonservice.ICommonServiceDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.enums.BackgroundLogEnum;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminWorkOrderCaseConst;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfDealWithStatusEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfFinishStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingOperationEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingType;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceCapitalAccountFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.crowdfunding.caseRepeat.AdminCfRepeatFinishInfo;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCfRepeatInfo;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCfRepeatView;
import com.shuidihuzhu.cf.model.crowdfunding.CfCapitalAccount;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.common.web.util.ContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class AdminCfRepeatHandleBizImpl implements AdminCfRepeatHandleBiz {

    @Autowired
    private AdminCfInfoExtBiz adminCfInfoExtBiz;
    @Autowired
    private CfFinanceCapitalAccountFeignClient cfFinanceCapitalAccountFeignClient;
    @Autowired
    private AdminCfRepeatFinishInfoDAO finishInfoDAO;
    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;
    @Autowired
    private AdminCfRepeatInfoBiz repeatInfoBiz;
    @Autowired
    private ICommonServiceDelegate commonServiceDelegate;
    @Autowired
    private AdminApproveService adminApproveService;
    @Autowired
    private AdminWorkOrderBiz adminWorkOrderBiz;
    @Autowired
    private AdminWorkOrderCaseBiz adminWorkOrderCaseBiz;

    @Autowired
    private CaseEndClient caseEndClient;

    private static ThreadLocal<String> LOG_LOCAL_PREFIX = new ThreadLocal<String>() {
        @Override
        protected String initialValue() {
            return AUTO_CLOSE_REPEAT_CASE_LOG_PREFIX;
        }
    };

    private static String AUTO_CLOSE_REPEAT_CASE_LOG_PREFIX = "案例重复自动化处理 baseCaseId:%s";
    private static String STATIC_COMMENT = "命中系统重复自动处理规则1";

    private boolean needFinishCase(CrowdfundingInfo baseCase, CrowdfundingInfo currentCf,  Map<Integer, List<CrowdfundingAttachmentVo>> attachmentMap,
                                   Date now) {

        if (currentCf.getId() == baseCase.getId()) {
            return false;
        }

        if (currentCf.getType() != CrowdfundingType.SERIOUS_ILLNESS.value()) {
            log.info("[{}] 发起的案例:{} 不是大病筹款，不处理", LOG_LOCAL_PREFIX.get(), currentCf.getId());
            return false;
        }

        if (currentCf.getCreateTime().after(baseCase.getCreateTime())) {
            log.info("[{}] 发起的案例:{} 后发起，不在处理", LOG_LOCAL_PREFIX.get(), currentCf.getId());
            return false;
        }

        if (currentCf.getEndTime().before(now)) {
            log.info("[{}] 发起的案例:{} 已经结束，不在处理", LOG_LOCAL_PREFIX.get(), currentCf.getId());
            return false;
        }

        //判断案例是否是可转发 可捐款的状态
        boolean canDonate = commonServiceDelegate.canDonate(currentCf.getId());
        if (!canDonate) {
            log.info("[{}] 发起的案例:{} 不能筹款，不在处理", LOG_LOCAL_PREFIX.get(), currentCf.getId());
            return false;
        }

        // 判断是否筹到钱，有钱就不处理
        CfCapitalAccount cfAccount = null;
        try {
            FeignResponse<CfCapitalAccount> response = cfFinanceCapitalAccountFeignClient.capitalAccountGetByInfoUuid(currentCf.getInfoId());
            if (response == null || response.notOk()) {
                log.error("finance服务异常");
                return false;
            }
            cfAccount = response.getData();
        } catch (Exception e) {
            log.error("finance服务异常", e);
            return false;
        }
        if (currentCf.getAmount() != 0 || ( cfAccount != null &&
                (cfAccount.getPayAmount() - cfAccount.getSingleRefundAmount() - cfAccount.getAllRefundAmount() != 0))) {
            log.info("[{}]  发起的案例：{}, 已经筹到了钱 不在处理。 cf.amount:{}, cfAccount.amount:{}",
                    LOG_LOCAL_PREFIX.get(), currentCf.getId(), currentCf.getAmount(), cfAccount == null ? 0 :
                            cfAccount.getPayAmount() - cfAccount.getSingleRefundAmount() - cfAccount.getAllRefundAmount());
            return false;
        }

        // 标题和文字说明 文字字数
        int titleAndContentLen = StringUtils.trimToEmpty(currentCf.getTitle()).length() +
                StringUtils.trimToEmpty(currentCf.getContent()).length();
        int baseTitleAndContentLen = StringUtils.trimToEmpty(baseCase.getTitle()).length() +
                StringUtils.trimToEmpty(baseCase.getContent()).length();
        if (titleAndContentLen > baseTitleAndContentLen) {
            log.info("[{}] 发起的案例：{}, 标题和筹款内容的文字较多 不在处理。", LOG_LOCAL_PREFIX.get(), currentCf.getId());
            return false;
        }

        // 图片的数量
        int picNum = getPicNumByCaseId(currentCf.getId(), attachmentMap);
        int basePicNum = getPicNumByCaseId(baseCase.getId(), attachmentMap);
        if (picNum > basePicNum) {
            log.info("[{}] 发起的案例：{}, 图片较多 不在处理。", LOG_LOCAL_PREFIX.get(), currentCf.getId());
            return false;
        }

        return true;
    }

    private int getPicNumByCaseId(int caseId, Map<Integer, List<CrowdfundingAttachmentVo>> attachmentMap) {

        if (MapUtils.isEmpty(attachmentMap)) {
            return 0;
        }

        List<CrowdfundingAttachmentVo> voList = attachmentMap.get(caseId);
        return voList == null ? 0 : voList.size();
    }

    @Override
    public void recordFinishCase(int baseCaseId, int caseId, CfFinishStatus finishStatus, int finishId) {
        try {
            AdminCfRepeatInfo cfRepeatInfo = repeatInfoBiz.selectByCaseId(caseId);
            if (cfRepeatInfo == null ||
                    ((cfRepeatInfo.getRepeatSummary() & AdminCfRepeatView.RepeatReasonView.REPEAT.getSummaryCode()) == 0)) {
                log.info("关闭当前案例日志记录 case:{}, 没有找到重复的情况", caseId);
                return;
            }

            AdminCfRepeatFinishInfo finishInfo = new AdminCfRepeatFinishInfo();

            finishInfo.setCaseId(baseCaseId);
            finishInfo.setFinishCaseId(caseId);
            finishInfo.setFinishType(finishStatus.getValue());
            finishInfo.setFinishId(finishId);

            finishInfo.setRepeatBaseInfo(cfRepeatInfo.getRepeatBaseInfo());


            finishInfoDAO.insert(finishInfo);

        } catch (Exception e) {
            log.info("[{}] 记录日志异常", LOG_LOCAL_PREFIX.get(), e);
        }
    }

}
