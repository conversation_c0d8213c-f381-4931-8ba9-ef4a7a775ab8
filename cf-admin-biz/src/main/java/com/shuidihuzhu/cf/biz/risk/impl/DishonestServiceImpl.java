package com.shuidihuzhu.cf.biz.risk.impl;

import com.shuidihuzhu.cf.biz.risk.IDishonestService;
import com.shuidihuzhu.cf.dao.risk.DishonestDao;
import com.shuidihuzhu.cf.model.risk.Dishonest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description:
 * @author: zheng<PERSON>u
 * @date: 2021-04-19 20:36
 **/
@Service
public class DishonestServiceImpl implements IDishonestService {

    @Autowired
    private DishonestDao dishonestDao;

    @Override
    public int insert(Dishonest dishonest) {
        return dishonestDao.insert(dishonest);
    }

    @Override
    public Dishonest getDishonestInfo(int caseId, int userType, String name) {
        return dishonestDao.getDishonestInfo(caseId, userType, name);
    }
}
