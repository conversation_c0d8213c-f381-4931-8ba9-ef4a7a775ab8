package com.shuidihuzhu.cf.biz.customer.Impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.customer.CfUdeskCustomerInfoBiz;
import com.shuidihuzhu.cf.customer.CfUdeskCustomerInfo;
import com.shuidihuzhu.cf.dao.customer.CfUdeskCustomerInfoDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
public class CfUdeskCustomerInfoBizImpl implements CfUdeskCustomerInfoBiz {
    @Autowired
    private CfUdeskCustomerInfoDao cfUdeskCustomerInfoDao;

    @Override
    public List<CfUdeskCustomerInfo> getUdeskCustomersByCfUserIds(List<Long> cfUserIds) {
        if (CollectionUtils.isEmpty(cfUserIds)) {
            return Lists.newArrayList();
        }
        return cfUdeskCustomerInfoDao.getUdeskCustomersByCfUserIds(cfUserIds);
    }

    @Override
    public String getNickNameByUdeskUserId(long udeskUserId) {
        if (udeskUserId <= 0) {
            return "";
        }
        return cfUdeskCustomerInfoDao.getNickNameByUdeskUserId(udeskUserId);
    }
}
