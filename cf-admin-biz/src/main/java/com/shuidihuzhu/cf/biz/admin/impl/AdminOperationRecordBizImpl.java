package com.shuidihuzhu.cf.biz.admin.impl;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.admin.AdminOperationRecordBiz;
import com.shuidihuzhu.cf.dao.admin.AdminLoginRecordDAO;
import com.shuidihuzhu.cf.model.admin.AdminLoginRecord;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by chao on 2017/7/20.
 */
@Service
public class AdminOperationRecordBizImpl implements AdminOperationRecordBiz {

	private static final Logger LOGGER = LoggerFactory.getLogger(AdminOperationRecordBizImpl.class);

	@Autowired
	private AdminLoginRecordDAO loginRecordDAO;



	@Override
	public List<AdminLoginRecord> selectByDateAndOrg(int formatDate, List<String> orgNameList) {

		//todo sea-client的api 不支持通过 orgName 查询orgid。  sea后台后续提供查询接口

		List<AdminLoginRecord> records = loginRecordDAO.selectByLoginAndOrgIdAndUserId(formatDate, 0, 0);

		if (CollectionUtils.isEmpty(records) || CollectionUtils.isEmpty(orgNameList)) {
			return records;
		}

		List<AdminLoginRecord> loginRecordList = Lists.newArrayList();
		String orgName = getOrgName(orgNameList);
		for (AdminLoginRecord loginRecord : records) {
			if (orgName.equals(loginRecord.getOrgName())) {
				loginRecordList.add(loginRecord);
			}
		}

		return loginRecordList;
	}


	private String getOrgName(List<String> orgNameList) {
		if (CollectionUtils.isEmpty(orgNameList)) {
			return "";
		}

		return Joiner.on("-").join(orgNameList);
	}
}
