package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import java.util.List;

import com.shuidihuzhu.common.web.enums.ValidEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfSwitcherBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfSwitcherDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfSwitcher;

@Service
public class AdminCfSwitcherBizImpl implements AdminCfSwitcherBiz {
	
	@Autowired
	private AdminCfSwitcherDao cfSwitcherDao;

	@Override
	public List<CfSwitcher> getAll() {
		return this.cfSwitcherDao.getAll();
	}

	@Override
	public int add(CfSwitcher cfSwitcher) {
		return this.cfSwitcherDao.add(cfSwitcher);
	}

	@Override
	public CfSwitcher getByName(String name) {
		return this.cfSwitcherDao.getByName(name);
	}

	@Override
	public int updateValue(int id, int value) {
		return this.cfSwitcherDao.updateValue(id,value);
	}

	@Override
	public int SwitcherOfVerify(int verify) {
		int k = 0;
		if (verify == ValidEnum.INVALID.getValue()) {
			//Verify=0使开关关闭
			k = cfSwitcherDao.updateValue(2,0);
		} else if (verify == ValidEnum.VALID.getValue()) {
			//Verify=0使开关开启
			k = cfSwitcherDao.updateValue(2,1);
		}
		return k;
	}
}
