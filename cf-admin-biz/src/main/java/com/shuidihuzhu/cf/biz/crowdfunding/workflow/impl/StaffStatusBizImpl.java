package com.shuidihuzhu.cf.biz.crowdfunding.workflow.impl;

import com.google.common.collect.*;
import com.shuidihuzhu.cf.biz.crowdfunding.workflow.StaffStatusBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.WorkFlowStaffStatusDao;
import com.shuidihuzhu.cf.dao.crowdfunding.WorkFlowStaffStatusRecordDao;
import com.shuidihuzhu.cf.model.crowdfunding.workflow.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2020-02-14 19:45
 **/
@Slf4j
@Service
public class StaffStatusBizImpl implements StaffStatusBiz {

    @Autowired
    private WorkFlowStaffStatusDao staffStatusDao;

    @Autowired
    private WorkFlowStaffStatusRecordDao recordDao;

    @Override
    public boolean add(WorkFlowStaffStatus staffStatus) {
        staffStatusDao.add(staffStatus);
        WorkFlowStaffStatusRecord record = buildRecord(staffStatus);
        recordDao.add(record);
        return true;
    }

    @NotNull
    private WorkFlowStaffStatusRecord buildRecord(WorkFlowStaffStatus staffStatus) {
        WorkFlowStaffStatusRecord record = new WorkFlowStaffStatusRecord();
        record.setUserId(staffStatus.getUserId());
        record.setOperatorId(staffStatus.getOperatorId());
        record.setOptType(staffStatus.getOptType());
        record.setStaffStatus(staffStatus.getStaffStatus());
        return record;
    }

    @Override
    public Map<Long, List<WorkFlowStaffStatusRecord>> listTodayRecord(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Maps.newHashMap();
        }
        DateTime dateTime = DateTime.now().withTimeAtStartOfDay();
        Ordering<WorkFlowStaffStatusRecord> sortByCreateTime = Ordering.natural().onResultOf(WorkFlowStaffStatusRecord::getCreateTime);
        List<WorkFlowStaffStatusRecord> statusRecords = recordDao
                .listTodayRecords(userIds, dateTime.toDate())
                .stream()
                .sorted(sortByCreateTime)
                .collect(Collectors.toList());
        return statusRecords.stream().collect(Collectors.groupingBy(WorkFlowStaffStatusRecord::getUserId));
    }

    @Override
    public boolean changeStatus(WorkFlowStaffStatus staffStatus) {
        staffStatusDao.changeStatus(staffStatus);
        WorkFlowStaffStatusRecord record = buildRecord(staffStatus);
        recordDao.add(record);
        return true;
    }


    @Override
    public boolean autoOffline() {
        staffStatusDao.autoOffline();
        //插入记录
        List<WorkFlowStaffStatus> workFlowStaffStatuses = staffStatusDao.listByUserIds(null);
        for (WorkFlowStaffStatus workFlowStaffStatus : workFlowStaffStatuses) {
            WorkFlowStaffStatusRecord record = buildRecord(workFlowStaffStatus);
            recordDao.add(record);
        }
        return false;
    }

    @Override
    public List<StaffStatusAndNum> groupByStatusAndOrgType(Date date) {
        return staffStatusDao.groupByStatusAndOrgType(date);
    }

    @Override
    public List<WorkFlowStaffStatus> listByUserIds(List<Long> userIds) {
        if ((CollectionUtils.isEmpty(userIds))) {
            return Lists.newArrayList();
        }
        return staffStatusDao.listByUserIds(userIds);
    }

    @Override
    public List<WorkFlowStaffStatus> listByUserIds(List<Long> userIds, int staffStatus) {
        if ((CollectionUtils.isEmpty(userIds))) {
            return Lists.newArrayList();
        }
        return staffStatusDao.listByUserIdsAndStatus(userIds,staffStatus);
    }

    @Override
    public WorkFlowStaffStatus findByUserId(long userId) {
        if (userId < 0) {
            return null;
        }
        return staffStatusDao.findByUserId(userId);
    }
}
