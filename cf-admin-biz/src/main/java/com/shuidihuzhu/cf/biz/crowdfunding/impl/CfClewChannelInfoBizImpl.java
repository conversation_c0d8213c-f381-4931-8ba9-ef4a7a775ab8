package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfClewChannelInfoBiz;
import com.shuidihuzhu.cf.client.adminpure.constants.MQTagPureAdminCons;
import com.shuidihuzhu.cf.client.adminpure.model.caseinfo.payload.LnMissionMarkCasePayload;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingUserDelegate;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enhancer.mq.MQHelperService;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.vo.approve.TreatmentVO;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewBaseInfoDO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: wangpeng
 * @Date: 2021/9/22 19:25
 * @Description:
 */
@Slf4j
@Service
public class CfClewChannelInfoBizImpl implements CfClewChannelInfoBiz {

    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Resource
    private CfClewtrackFeignClient cfClewtrackFeignClient;

    @Resource
    private MQHelperService mqHelperService;

    @Resource
    private ICrowdfundingUserDelegate crowdfundingUserDelegate;

    @Override
    public void sendLiaoNingMission(int caseId) {
        log.info("CfClewChannelInfoBiz sendLiaoNingMission send {}", caseId);
        String primaryChannel = getClewChannel(caseId);
        String hospitalName = "";
        String hospitalCityName = "";
        int cityId = 0;
        TreatmentVO crowdfundingTreatmentVO = crowdfundingUserDelegate.getCrowdfundingTreatmentVO(caseId);
        if (Objects.nonNull(crowdfundingTreatmentVO)) {
            cityId = crowdfundingTreatmentVO.getHospitalCityId();
            hospitalCityName = crowdfundingTreatmentVO.getHospitalCityName();
            hospitalName = crowdfundingTreatmentVO.getHospitalName();
        }
        if (StringUtils.isEmpty(primaryChannel) && StringUtils.isEmpty(hospitalName)) {
            log.info("CfClewChannelInfoBiz sendLiaoNingMission send fail primaryChannel || hospitalName is empty {}", caseId);
            return;
        }

        LnMissionMarkCasePayload payload = new LnMissionMarkCasePayload();
        payload.setCaseId(caseId);
        payload.setPrimaryChannel(primaryChannel);
        payload.setHospitalCityName(hospitalCityName);
        payload.setCityId(cityId);
        payload.setHospitalName(hospitalName);
        mqHelperService.builder()
                .setTags(MQTagPureAdminCons.LIAO_NING_MISSION)
                .addKey(caseId, System.currentTimeMillis())
                .setPayload(JSON.toJSONString(payload))
                .send();
        log.info("CfClewChannelInfoBiz sendLiaoNingMission success {} {}", caseId, payload);
    }

    private String getClewChannel(int caseId) {
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
        if (Objects.isNull(crowdfundingInfo)) {
            return "";
        }
        String mobile = Optional.ofNullable(userInfoServiceBiz.getUserInfoByUserId(crowdfundingInfo.getUserId()))
                .map(r -> shuidiCipher.decrypt(r.getCryptoMobile()))
                .orElse("");
        if (StringUtils.isEmpty(mobile)) {
            log.info("CfClewChannelInfoBiz add fail mobile is null {}", caseId);
            return "";
        }
        Response<List<CfClewBaseInfoDO>> clewBaseInfoByMobile = cfClewtrackFeignClient.getClewBaseInfoByMobile(mobile);
        if (Objects.isNull(clewBaseInfoByMobile) || clewBaseInfoByMobile.notOk() || CollectionUtils.isEmpty(clewBaseInfoByMobile.getData())) {
            log.info("CfClewChannelInfoBiz add fail CfClewBaseInfoDO is null {}", caseId);
            return "";
        }
        Date date = DateUtil.addMonth(crowdfundingInfo.getCreateTime(), -1);
        List<String> primaryChannelList = clewBaseInfoByMobile.getData()
                .stream()
                .filter(f -> f.getClewType() == 0 && f.getInfoId() == caseId && f.getCreateTime().getTime() < crowdfundingInfo.getCreateTime().getTime() && f.getCreateTime().getTime() > date.getTime())
                .sorted(Comparator.comparing(CfClewBaseInfoDO::getId).reversed())
                .map(CfClewBaseInfoDO::getPrimaryChannel)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(primaryChannelList)) {
            log.info("CfClewChannelInfoBiz add fail CfClewBaseInfoDO is limit {} {}", caseId, clewBaseInfoByMobile
                    .getData()
                    .stream()
                    .map(CfClewBaseInfoDO::getId)
                    .collect(Collectors.toList()));
            return "";
        }

        return JSONObject.toJSONString(primaryChannelList);
    }
}
