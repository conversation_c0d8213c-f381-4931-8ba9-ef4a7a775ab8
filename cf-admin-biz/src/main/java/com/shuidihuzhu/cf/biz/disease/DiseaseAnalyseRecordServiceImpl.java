package com.shuidihuzhu.cf.biz.disease;

import com.shuidihuzhu.cf.biz.disease.impl.DiseaseAnalyseRecordService;
import com.shuidihuzhu.cf.dao.disease.DiseaseAnalyseRecordDao;
import com.shuidihuzhu.cf.dto.DiseaseAnalyseRecordDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DiseaseAnalyseRecordServiceImpl implements DiseaseAnalyseRecordService {
    @Autowired
    private DiseaseAnalyseRecordDao diseaseAnalyseRecordDao;

    public void insertOne(DiseaseAnalyseRecordDto record) {
        if (record == null) {
            return;
        }
        diseaseAnalyseRecordDao.insetOne(record);
    }
}
