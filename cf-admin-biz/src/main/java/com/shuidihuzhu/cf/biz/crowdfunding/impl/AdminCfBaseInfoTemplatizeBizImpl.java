package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfBaseInfoTemplatizeBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfBaseInfoTemplateOperatorHistoryDao;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfBaseInfoTemplatizeDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplateOperatorHistory;
import com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplatize;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> Ahrievil
 * @date : 2018/9/10 19:43
 */
@Service
public class AdminCfBaseInfoTemplatizeBizImpl implements AdminCfBaseInfoTemplatizeBiz {

    @Autowired
    private AdminCfBaseInfoTemplatizeDao adminCfBaseInfoTemplatizeDao;

    @Resource
    private AdminCfBaseInfoTemplateOperatorHistoryDao adminCfBaseInfoTemplateOperatorHistoryDao;

    @Override
    public List<CfBaseInfoTemplatize> selectByPage(String context, int contentType, int relationType, int channelType, int current, int pageSize,
                                                   List<Integer> ids) {
        PageHelper.startPage(current, pageSize);
        return adminCfBaseInfoTemplatizeDao.selectByPage(context, contentType, relationType, channelType, ids);
    }

    @Override
    public CfBaseInfoTemplatize selectById(long id) {
        return adminCfBaseInfoTemplatizeDao.selectById(id);
    }

    @Override
    public int insertOne(CfBaseInfoTemplatize cfBaseInfoTemplatize) {
        return adminCfBaseInfoTemplatizeDao.insertOne(cfBaseInfoTemplatize);
    }

    @Override
    public int update(CfBaseInfoTemplatize cfBaseInfoTemplatize) {
        return adminCfBaseInfoTemplatizeDao.update(cfBaseInfoTemplatize);
    }

    @Override
    public int delete(long id) {
        return adminCfBaseInfoTemplatizeDao.delete(id);
    }

    @Override
    public int insertOperatorHistory(CfBaseInfoTemplateOperatorHistory cfBaseInfoTemplateOperatorHistory) {
        return adminCfBaseInfoTemplateOperatorHistoryDao.insert(cfBaseInfoTemplateOperatorHistory);
    }

    @Override
    public List<CfBaseInfoTemplateOperatorHistory> selectByCfBaseTemplateId(long cfBaseTemplateId) {
        return adminCfBaseInfoTemplateOperatorHistoryDao.selectByTemplateId(cfBaseTemplateId);
    }

    @Override
    public List<CfBaseInfoTemplatize> get1v1TemplateList(String context, int contentType, Integer relationType, int channelType, List<Integer> ids, String diseaseName, Integer age, int offset, int pageSize) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return adminCfBaseInfoTemplatizeDao.get1v1TemplateList(context, contentType, relationType, channelType, ids, diseaseName, age, offset, pageSize);
    }

    @Override
    public int get1v1TemplateCount(String context, int contentType, Integer relationType, int channelType, List<Integer> ids, String diseaseName, Integer age) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        return adminCfBaseInfoTemplatizeDao.get1v1TemplateCount(context, contentType, relationType, channelType, ids, diseaseName, age);
    }
}
