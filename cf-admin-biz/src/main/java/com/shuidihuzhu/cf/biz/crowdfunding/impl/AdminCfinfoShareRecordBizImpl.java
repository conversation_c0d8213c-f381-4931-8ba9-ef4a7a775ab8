package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfinfoShareRecordBiz;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICfInfoXXXRecordDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecordCountModel;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/3/2.
 */
@Service
public class AdminCfinfoShareRecordBizImpl implements AdminCfinfoShareRecordBiz {
    @Autowired
    private ICfInfoXXXRecordDelegate cfInfoShareRecordDelegate;
    @Override
    public Map<Integer,Integer> getShareCount(List<Integer> infoIds) {
        if (CollectionUtils.isEmpty(infoIds)){
            return new HashMap();
        }
        List<CfInfoShareRecordCountModel> records = Lists.newArrayList();
        List<List<Integer>> splitList = Lists.partition(infoIds, 500);
        for (List<Integer> list : splitList) {
            records.addAll(cfInfoShareRecordDelegate.getShareCount(list));
        }
        Map<Integer,Integer> shareMap = new HashMap<>();
        for (CfInfoShareRecordCountModel cfInfoShareRecordCountModel : records) {
            int count = cfInfoShareRecordCountModel.getCount();
            Integer infoId = cfInfoShareRecordCountModel.getInfoId();
            shareMap.put(infoId,count);
        }
        return shareMap;
    }
}
