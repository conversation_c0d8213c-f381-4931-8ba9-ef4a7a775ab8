package com.shuidihuzhu.cf.biz.es.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.es.CfSearch;
import com.shuidihuzhu.cf.constants.admin.QcConst;
import com.shuidihuzhu.cf.dao.admin.AdminWorkOrderDao;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.AddTrustAuditStatusEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CredibleTypeEnum;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.client.cf.search.client.CfSearchClient;
import com.shuidihuzhu.client.cf.search.model.*;
import com.shuidihuzhu.client.cf.search.param.CfWorkOrderV2IndexSearchParam;
import com.shuidihuzhu.client.cf.search.param.table.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static com.shuidihuzhu.cf.admin.constant.AsyncPoolConstants.CF_SEARCH_EXECUTOR;

/**
 * <AUTHOR>
 * @date 2019/3/21
 */
@Service
public class CfSearchImpl implements CfSearch {

    @Autowired
    private CfSearchClient cfSearchClient;

    @Autowired
    private AdminWorkOrderDao adminWorkOrderDao;
    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    @Resource(name = CF_SEARCH_EXECUTOR)
    private Executor cfSearchExecutor;

    @Override
    public Pair<Long, List<AdminWorkOrder>> cfWorkOrderIndexSearch(CfWorkOrderIndexSearchParam searchParam) {

        SearchRpcResult<CfWorkOrderIndexSearchResult> rpcResult = cfSearchClient.cfWorkOrderIndexSearch(searchParam);

        if (rpcResult == null || rpcResult.getCode() != 0
                || rpcResult.getData() == null
                || CollectionUtils.isEmpty(rpcResult.getData().getModels())) {
            return Pair.of(0L, Lists.newArrayList());
        }

        long total = rpcResult.getData().getTotal();
        List<CfWorkOrderModel> cfWorkOrderModels = rpcResult.getData().getModels();
        List<Long> workOrderIds = cfWorkOrderModels.stream().map(x -> x.getWorkOrderId()).collect(Collectors.toList());
        List<List<Long>> workOrderIdPartition = Lists.partition(workOrderIds, 500);

        List<CompletableFuture<List<AdminWorkOrder>>> futureList = workOrderIdPartition.stream().map(t -> CompletableFuture.supplyAsync(() -> {
            List<AdminWorkOrder> tmpWorkOrders = adminWorkOrderDao.selectByIdList(t);
            if (CollectionUtils.isEmpty(tmpWorkOrders)) {
                return new ArrayList<AdminWorkOrder>();
            }
            return tmpWorkOrders;
        }, cfSearchExecutor)).collect(Collectors.toList());

        List<List<AdminWorkOrder>> results = futureList.stream().map(CompletableFuture::join).collect(Collectors.toList());
        List<AdminWorkOrder> workOrders = results.stream().flatMap(List::stream).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(workOrders)) {
            return Pair.of(0L, Lists.newArrayList());
        }
        workOrders = workOrders.stream().filter(x -> x.getIsDelete() == 0).sorted(Comparator.comparing(AdminWorkOrder::getId).reversed()).collect(Collectors.toList());

        return Pair.of(total, workOrders);
    }

    @Override
    public Pair<Long, List<AdminWorkOrder>> cfWorkOrderIndexBySearch(CfWorkOrderIndexByAuthorSearchParam searchParam) {

        SearchRpcResult<CfWorkOrderIndexSearchResult> rpcResult = cfSearchClient.cfWorkOrderIndexByAuthorSearch(searchParam);

        if (rpcResult == null || rpcResult.getCode() != 0
                || rpcResult.getData() == null
                || CollectionUtils.isEmpty(rpcResult.getData().getModels())) {
            return Pair.of(0L, Lists.newArrayList());
        }

        long total = rpcResult.getData().getTotal();
        List<CfWorkOrderModel> cfWorkOrderModels = rpcResult.getData().getModels();
        List<Long> workOrderIds = cfWorkOrderModels.stream().map(x -> x.getWorkOrderId()).collect(Collectors.toList());

        List<AdminWorkOrder> workOrders = adminWorkOrderDao.selectByIdList(workOrderIds);
        if (CollectionUtils.isEmpty(workOrders)) {
            return Pair.of(0L, Lists.newArrayList());
        }
        return Pair.of(total, workOrders);
    }

    @Override
    public CfWorkOrderIndexSearchResult queryWorkOrderBySearch(int current, int pageSize, long workOrderId, int caseId,
                                                               long operatorId, String mobile, String originatorMobile, List<Integer> orderTypes,
                                                               List<Integer> handleResults, List<Integer> lostStatus,
                                                               List<Integer> supplyAuditStatus, List<Integer> highRisk,
                                                               List<Integer> letterStatusList, String startHandleTime, String endHandleTime,
                                                               int credibleStatus, int paymentMethod, String asrResult) {
        /**
         * 构建工单表的相关参数
         */
        WorkOrderTableParam woTableParam = new WorkOrderTableParam();
        woTableParam.setOrderTypes(orderTypes);
        if (workOrderId > 0){
            woTableParam.setIds(Optional.ofNullable(workOrderId).map(Lists::newArrayList).get());
        }

        woTableParam.setHandleResult(handleResults);
        if(caseId > 0){
            woTableParam.setCaseIds(Lists.newArrayList(Long.valueOf(caseId)));
        }

        if(operatorId > 0){
            woTableParam.setOperatorIds(Lists.newArrayList(operatorId));
        }

        if (StringUtils.isNotBlank(startHandleTime) && StringUtils.isNotBlank(endHandleTime)) {
            woTableParam.setUpdateStartTime(Timestamp.valueOf(startHandleTime).getTime());
            woTableParam.setUpdateEndTime(Timestamp.valueOf(endHandleTime).getTime());
        }


        /**
         * 补充证明状态
         */
        CfReportAddTrustTableParam cratTableParam = new CfReportAddTrustTableParam();
        cratTableParam.setAuditStatuses(supplyAuditStatus);

        /**
         * 是否是高危案例
         */
        CfLabelRiskTableParam clrTableParam = new CfLabelRiskTableParam();
        clrTableParam.setRiskTypes(highRisk);


        /**
         * 构建举报表的相关参数
         */
        CrowdfundingReportTableParam crTableParam = new CrowdfundingReportTableParam();
        if(StringUtils.isNotEmpty(mobile)){
            crTableParam.setContact(Lists.newArrayList(mobile));
        }

        /**
         * 构建失联表的相关参数
         */
        AdminCfLostContactTableParam aclcTableParam = new AdminCfLostContactTableParam();
        aclcTableParam.setLosts(lostStatus);

        /**
         * 构建公函表相关参数
         */
        CfReportOfficialLetterParam crolTableParam = new CfReportOfficialLetterParam();
        crolTableParam.setLetterStatus(letterStatusList);

        /**
         * 构建案例表相关参数
         */
        CrowdfundingInfoTableParam crowdfundingInfoTableParam = new CrowdfundingInfoTableParam();
        String aesEncryptMobile = oldShuidiCipher.aesEncrypt(originatorMobile);
        UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByCryptoMobile(aesEncryptMobile);
        if (Objects.nonNull(userInfoModel)){
            crowdfundingInfoTableParam.setUserId(userInfoModel.getUserId());
        }
        /**
         * 构建可信信息相关参数
         */
        CfReportCredibleInfoTableParam cfReportCredibleInfoTableParam = new CfReportCredibleInfoTableParam();
        if (credibleStatus > 0) {
            cfReportCredibleInfoTableParam.setAuditStatus(credibleStatus);
            cfReportCredibleInfoTableParam.setType(CredibleTypeEnum.HELP_PROVE.getKey());
        }

        List<WorkOrderExtTableParam> workOrderExtTableParamList = Lists.newArrayList();
        if (paymentMethod > 0) {
            WorkOrderExtTableParam workOrderExtTableParam = WorkOrderExtTableParam.create(QcConst.OrderExt.payMethod, paymentMethod);
            workOrderExtTableParamList.add(workOrderExtTableParam);
        }

        List<WorkOrderExtTableParam> workOrderExtSearchTableParamList = Lists.newArrayList();

        if (StringUtils.isNotBlank(asrResult)) {
            workOrderExtSearchTableParamList.add(WorkOrderExtTableParam.create(OrderExtName.asrResult.getName(), asrResult));
        }

        CfWorkOrderV2IndexSearchParam searchParam = new CfWorkOrderV2IndexSearchParam();
        searchParam.setWoTableParam(woTableParam);
        searchParam.setCrTableParam(crTableParam);
        searchParam.setAclcTableParam(aclcTableParam);
        searchParam.setCratTableParam(cratTableParam);
        searchParam.setClrTableParam(clrTableParam);
        searchParam.setCrolTableParam(crolTableParam);
        searchParam.setCiTableParam(crowdfundingInfoTableParam);
        searchParam.setCfReportCredibleInfoTableParam(cfReportCredibleInfoTableParam);
        searchParam.setWorkOrderExtTableParamList(workOrderExtTableParamList);
        searchParam.setWorkOrderExtShouldTableParamList(workOrderExtSearchTableParamList);
        searchParam.setFrom((current-1) * pageSize);
        searchParam.setSize(pageSize);

        SearchRpcResult<CfWorkOrderIndexSearchResult> searchRpcResult = cfSearchClient.cfWorkOrderV2IndexSearch(searchParam);
        if(Objects.isNull(searchRpcResult) || ErrorCode.SUCCESS.getCode() != searchRpcResult.getCode() || Objects.isNull(searchRpcResult.getData())){
            return null;
        }

        return searchRpcResult.getData();
    }

    @Override
    public List<Integer> workOrderIndexSearch(CfWorkOrderIndexSearchParam searchParam) {
        SearchRpcResult<CfWorkOrderIndexSearchResult> searchRpcResult = cfSearchClient.cfWorkOrderIndexSearch(searchParam);
        if (searchRpcResult == null
                || searchRpcResult.getCode() != 0
                || searchRpcResult.getData() == null
                || CollectionUtils.isEmpty(searchRpcResult.getData().getModels())) {
            return Lists.newArrayList();
        }

        return searchRpcResult.getData().getModels().stream().map(CfWorkOrderModel::getWorkOrderId).map(Long::intValue).collect(Collectors.toList());
    }
}
