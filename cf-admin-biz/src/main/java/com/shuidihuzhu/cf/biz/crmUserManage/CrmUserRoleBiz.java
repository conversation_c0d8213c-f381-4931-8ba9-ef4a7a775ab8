package com.shuidihuzhu.cf.biz.crmUserManage;

import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.client.cf.admin.model.CrmUserManage;

public interface CrmUserRoleBiz {

    /**
     *  线索关联的手机号发起案例
     * @param raiseInfo
     * @param material
     */
    void onCaseRaise(CrmUserManage.ClewCaseRaiseInfo raiseInfo, CfFirsApproveMaterial material);

    /**
     * 案例的 患者身份证、收款人身份证 修改
     * @param caseId
     */
    void updateCrmManageUserRoleMapping(int caseId);

    void updateUserRole(String mobile, String idCard, String name);
}
