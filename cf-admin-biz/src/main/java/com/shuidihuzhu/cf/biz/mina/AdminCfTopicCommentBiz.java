package com.shuidihuzhu.cf.biz.mina;

import com.shuidihuzhu.cf.model.miniprogram.CfTopicComment;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR> Ahrievil
 */
public interface AdminCfTopicCommentBiz {

    List<CfTopicComment> selectByPage(Long commentId, Integer topicId, String comment, Integer commentUserId, Integer isSensitiveWord,
                                      Timestamp beginTime, Timestamp endTime, int current, int pageSize, Integer orderType);

    CfTopicComment selectById(long id);

    int deleteById(long id);

    int deleteByGroupId(long groupId);

    int deleteByTopicId(long topicId);
}
