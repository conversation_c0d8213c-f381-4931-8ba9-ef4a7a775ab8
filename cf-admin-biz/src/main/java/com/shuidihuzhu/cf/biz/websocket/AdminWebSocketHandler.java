package com.shuidihuzhu.cf.biz.websocket;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.WebSocketMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class AdminWebSocketHandler implements WebSocketHandler {

    public static ConcurrentHashMap<Integer, WebSocketSession> sessionHoldMap = new ConcurrentHashMap<>();

    //TODO 前端不能感知后端的onclose，主动通知前端消息关闭
    private final static String END_MESSAGE = "web-socket_closed";

    public final static String USERID = "userId";
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        Integer userId = (Integer)session.getAttributes().get(USERID);

        synchronized (sessionHoldMap) {
            WebSocketSession webSession = sessionHoldMap.get(userId);
            if (webSession != null) {
                webSession.sendMessage(new TextMessage(END_MESSAGE));
                webSession.close();
            }
            sessionHoldMap.put(userId, session);
        }
        log.info("web-socket 连接建立. userId:{}", userId);
    }

    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {

            log.info("web-socket 接受来自客户端的消息. userId:{}, message:{}", session.getAttributes().get(USERID), message);
            session.sendMessage(message);

    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        log.error("web-socket 发生错误. userId：{} 异常", session.getAttributes() != null ?
                session.getAttributes().get(USERID) : "null", exception);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        Integer userId = (Integer)session.getAttributes().get(USERID);
        sessionHoldMap.remove(userId);
        log.info("web-socket 连接被关闭. userId:{}", userId);
    }

    public static void sendMessage(Integer userId, String message) {
        WebSocketSession webSession = sessionHoldMap.get(userId);

        if (webSession != null && webSession.isOpen()) {
            synchronized (webSession) {
                try {
                    webSession.sendMessage(new TextMessage(message));
                    log.info("web-socket 消息发送 userId:{}, message :{}", userId, message);
                } catch (IOException e) {
                    log.error("web-socket 发送消息错误. userId:{}", userId);
                }
            }
        }
    }

    public static int getSocketSize() {
        return sessionHoldMap.size();
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }
}
