package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfCommitVerifyItem;

import java.util.List;
import java.util.Set;

/**
 * Created by ahrievil on 2017/4/24.
 */
public interface CfCommitVerifyItemBiz {
    List<CfCommitVerifyItem> selectRefuseList(String infoUuid);
    List<CfCommitVerifyItem> selectBaseInfoRefuseList();
    List<CfCommitVerifyItem> selectRefusesByList(List<Integer> cfRefuseReasonIds);
    List<Integer> selectAllType( int start, int size);
    List<CfCommitVerifyItem> selectByIds(Set<Integer> set);
    List<CfCommitVerifyItem> selectAll(int start, int size);
}
