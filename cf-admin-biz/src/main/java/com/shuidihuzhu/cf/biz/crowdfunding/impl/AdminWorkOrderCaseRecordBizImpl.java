package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderCaseRecordBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminWorkOrderCaseRecordDao;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderCaseRecord;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> Ahrievil
 */
@Service
public class AdminWorkOrderCaseRecordBizImpl implements AdminWorkOrderCaseRecordBiz {

    @Autowired
    private AdminWorkOrderCaseRecordDao adminWorkOrderCaseRecordDao;
    @Override
    public int insertOne(AdminWorkOrderCaseRecord adminWorkOrderCaseRecord) {
        return adminWorkOrderCaseRecordDao.insertOne(adminWorkOrderCaseRecord);
    }

    @Override
    public int insertList(List<AdminWorkOrderCaseRecord> adminWorkOrderCaseRecords) {
        if (CollectionUtils.isEmpty(adminWorkOrderCaseRecords)) {
            return 0;
        }
        return adminWorkOrderCaseRecordDao.insertList(adminWorkOrderCaseRecords);
    }

    @Override
    public List<AdminWorkOrderCaseRecord> selectByWorkOrderId(int workOrderId) {
        return adminWorkOrderCaseRecordDao.selectByWorkOrderId(workOrderId);
    }
}
