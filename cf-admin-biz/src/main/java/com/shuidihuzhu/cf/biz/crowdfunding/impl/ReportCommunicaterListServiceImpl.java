package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.IReportCommunicaterListService;
import com.shuidihuzhu.cf.dao.crowdfunding.CfReportCommunicaterListDAO;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportCommunicaterDO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2019/12/13 下午4:56
 * @desc
 */
@Service
public class ReportCommunicaterListServiceImpl implements IReportCommunicaterListService {
    @Autowired
    private CfReportCommunicaterListDAO cfReportCommunicaterListDAO;

    @Override
    public int insert(CfReportCommunicaterDO communicaterDO) {
        if(Objects.isNull(communicaterDO)){
            return 0;
        }
        return cfReportCommunicaterListDAO.insert(communicaterDO);
    }

    @Override
    public List<CfReportCommunicaterDO> query(int caseId, int reportId, int type) {
        if(caseId <= 0 || type <= 0){
            return Lists.newArrayList();
        }

        return cfReportCommunicaterListDAO.query(caseId, reportId, type);
    }

    @Override
    public CfReportCommunicaterDO queryById(long id, int caseId, int reportId) {
        if(id <= 0 || caseId <= 0){
            return null;
        }
        return cfReportCommunicaterListDAO.queryById(id, caseId, reportId);
    }

    @Override
    public CfReportCommunicaterDO queryByMobile(int caseId, int reportId, String mobile) {
        if(caseId <= 0 || StringUtils.isEmpty(mobile)){
            return null;
        }

        return cfReportCommunicaterListDAO.queryByMobile(caseId, reportId, mobile);
    }

    @Override
    public int delete(long id, int caseId, int reportId) {
        return cfReportCommunicaterListDAO.updateIsDelete(id, caseId, reportId);
    }
}
