package com.shuidihuzhu.cf.biz.amount.impl;

import com.shuidihuzhu.cf.biz.amount.AmountReasonableBiz;
import com.shuidihuzhu.cf.client.adminpure.model.amount.CfAmountReasonableTask;
import com.shuidihuzhu.cf.client.adminpure.model.amount.CfAmountReasonableTaskWorkOrder;
import com.shuidihuzhu.cf.dao.amount.CfAmountReasonableTaskDao;
import com.shuidihuzhu.cf.dao.amount.CfAmountReasonableTaskWorkOrderDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2022/8/25 10:32
 * @Description:
 */
@Service
public class AmountReasonableBizImpl implements AmountReasonableBiz {

    @Resource
    private CfAmountReasonableTaskDao cfAmountReasonableTaskDao;

    @Resource
    private CfAmountReasonableTaskWorkOrderDao cfAmountReasonableTaskWorkOrderDao;

    @Override
    public int insertAmountReasonableTask(CfAmountReasonableTask amountReasonableTask) {
        return cfAmountReasonableTaskDao.insert(amountReasonableTask);
    }

    @Override
    public List<CfAmountReasonableTask> getByCaseIdAndTaskType(int caseId, int taskType) {
        return cfAmountReasonableTaskDao.getByCaseIdAndTaskType(caseId, taskType);
    }

    @Override
    public CfAmountReasonableTask getByTaskInfoId(String taskInfoId) {
        return cfAmountReasonableTaskDao.getByTaskInfoId(taskInfoId);
    }

    @Override
    public int updateCfAmountReasonableTask(CfAmountReasonableTask cfAmountReasonableTask) {
        return cfAmountReasonableTaskDao.updateCfAmountReasonableTask(cfAmountReasonableTask);
    }

    @Override
    public int insertAmountReasonableTaskWorkOrder(CfAmountReasonableTaskWorkOrder cfAmountReasonableTaskWorkOrder) {
        return cfAmountReasonableTaskWorkOrderDao.insert(cfAmountReasonableTaskWorkOrder);
    }

    @Override
    public List<CfAmountReasonableTaskWorkOrder> getByCaseId(int caseId) {
        return cfAmountReasonableTaskWorkOrderDao.selectByCaseId(caseId);
    }

    @Override
    public CfAmountReasonableTaskWorkOrder getTaskWorkOrderById(long id) {
        return cfAmountReasonableTaskWorkOrderDao.selectById(id);
    }

    @Override
    public CfAmountReasonableTask getTaskById(long id) {
        return cfAmountReasonableTaskDao.selectById(id);
    }

    @Override
    public int updateCfAmountReasonableTaskWorkOrder(CfAmountReasonableTaskWorkOrder cfAmountReasonableTaskWorkOrder) {
        return cfAmountReasonableTaskWorkOrderDao.updateCfAmountReasonableTaskWorkOrder(cfAmountReasonableTaskWorkOrder);
    }

    @Override
    public CfAmountReasonableTaskWorkOrder selectByWorkOrderId(long workOrderId) {
        return cfAmountReasonableTaskWorkOrderDao.selectByWorkOrderId(workOrderId);
    }

    @Override
    public List<CfAmountReasonableTaskWorkOrder> selectByWorkOrderIdList(List<Long> workOrderIdList) {
        return cfAmountReasonableTaskWorkOrderDao.selectByWorkOrderIdList(workOrderIdList);
    }

    @Override
    public List<CfAmountReasonableTask> getTaskByCaseId(int caseId) {
        return cfAmountReasonableTaskDao.getByCaseId(caseId);
    }
}
