package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.github.pagehelper.PageHelper;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfDailyCharityMessageBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfDailyCharityMessageDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfDailyCharityMessage;
import com.shuidihuzhu.common.web.util.admin.BasicExample;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * Created by ahrievil on 2017/5/11.
 */
@Service
public class AdminCfDailyCharityMessageBizImpl implements AdminCfDailyCharityMessageBiz {
    @Autowired
    private AdminCfDailyCharityMessageDao adminCfDailyCharityMessageDao;
    @Override
    public int deleteByPrimaryKey(Integer id) {
        return adminCfDailyCharityMessageDao.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(CfDailyCharityMessage record) {
        return adminCfDailyCharityMessageDao.insert(record);
    }

    @Override
    public CfDailyCharityMessage selectByPrimaryKey(Integer id) {
        return adminCfDailyCharityMessageDao.selectByPrimaryKey(id);
    }

    @Override
    public CfDailyCharityMessage selectByStartDate(Date date) {
        return adminCfDailyCharityMessageDao.selectByStartDate(date);
    }

    @Override
    public int updateByPrimaryKey(CfDailyCharityMessage record) {
        return adminCfDailyCharityMessageDao.updateByPrimaryKey(record);
    }

    @Override
    public List<CfDailyCharityMessage> selectByPage(BasicExample basicExample, int current, int pageSize) {
        PageHelper.startPage(current, pageSize);
        return adminCfDailyCharityMessageDao.selectByPage(basicExample);
    }
}
