package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.CfReportProblemOperationLogBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfReportProblemOperationLogDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportProblemOperationLog;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/4/22
 */
@Service
public class CfReportProblemOperationLogBizImpl implements CfReportProblemOperationLogBiz {
    @Autowired
    private CfReportProblemOperationLogDao cfReportProblemOperationLogDao;

    @Override
    public int add(int problemId, String action, String operator) {
        return cfReportProblemOperationLogDao.add(problemId, action, operator);
    }

    @Override
    public List<CfReportProblemOperationLog> getByProblemId(int problemId) {
        if (problemId < 0) {
            return Lists.newArrayList();
        }
        return cfReportProblemOperationLogDao.getByProblemId(problemId);
    }
}
