package com.shuidihuzhu.cf.biz.admin.impl.channel;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.admin.channel.CfChannelBiz;
import com.shuidihuzhu.cf.dao.sd.admin.channel.CfChannelDao;
import com.shuidihuzhu.cf.model.admin.channel.CfChannel;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Created by wangsf on 17/2/16.
 */
@Service
public class CfChannelBizImpl implements CfChannelBiz {

	@Autowired
	private CfChannelDao cfChannelDao;

	@Override
	public int add(CfChannel channel) {

		if(channel == null) {
			return -1;
		}

		return this.cfChannelDao.add(channel);
	}

	@Override
	public CfChannel getByName(String name) {
		if (StringUtils.isBlank(name)) {
			return null;
		}
		return this.cfChannelDao.getByName(name);
	}

	@Override
	public CfChannel get(int id) {
		if(id <= 0) {
			return null;
		}
		return this.cfChannelDao.get(id);
	}

	@Override
	public List<CfChannel> listByIds(List<Integer> channelIds) {
		if(CollectionUtils.isEmpty(channelIds)) {
			return Collections.EMPTY_LIST;
		}

		List<CfChannel> cfChannels = this.cfChannelDao.listByIds(channelIds);
		if(CollectionUtils.isEmpty(cfChannels)) {
			return Collections.EMPTY_LIST;
		}
		return cfChannels;
	}

	@Override
	public Map<Integer, CfChannel> getMapByIds(List<Integer> channelIds) {
		List<CfChannel> channels = this.listByIds(channelIds);
		if(CollectionUtils.isEmpty(channelIds)) {
			return Maps.newHashMap();
		}

		Map<Integer, CfChannel> channelMap = Maps.newHashMap();
		for(CfChannel channel : channels) {
			channelMap.put(channel.getId(), channel);
		}
		return channelMap;
	}

	@Override
	public List<CfChannel> listByGroupId(int groupId, int anchorId, int limit) {

		if(groupId <= 0) {
			return Collections.EMPTY_LIST;
		}

		if(limit <= 0) {
			return Collections.EMPTY_LIST;
		}

		if(anchorId < 0) {
			anchorId = 0;
		}

		List<CfChannel>	cfChannels = this.cfChannelDao.listByGroupId(groupId, anchorId, limit);
		if(CollectionUtils.isEmpty(cfChannels)) {
			return Collections.EMPTY_LIST;
		}

		return cfChannels;
	}

	@Override
	public List<CfChannel> list(int anchorId, int limit) {

		if(limit <= 0) {
			return Collections.EMPTY_LIST;
		}

		if(anchorId < 0) {
			anchorId = 0;
		}

		List<CfChannel>	cfChannels = this.cfChannelDao.list(anchorId, limit);
		if(CollectionUtils.isEmpty(cfChannels)) {
			return Collections.EMPTY_LIST;
		}

		return cfChannels;
	}

	@Override
	public List<CfChannel> listByGroupIdAndPage(int groupId, int current, int pageSize) {
		PageHelper.startPage(current, pageSize);
		return this.cfChannelDao.listByGroupIdAndPage(groupId);
	}

	@Override
	public List<CfChannel> listByPage(int current, int pageSize) {
		PageHelper.startPage(current, pageSize);
		return this.cfChannelDao.listByPage();
	}

	@Override
	public int update(int channelId, int groupId, String description) {
		if(channelId <= 0) {
			return -1;
		}

		if(groupId <= 0) {
			return -1;
		}
		return this.cfChannelDao.update(channelId, groupId, description);
	}

	@Override
	public List<CfChannel> prefixSearch(String name, int groupId, int current, int pageSize) {
		PageHelper.startPage(current, pageSize);
		if(groupId < 0) {
			groupId = 0;
		}
		if (StringUtils.isBlank(name)) {
			return cfChannelDao.prefixSearchWithoutKeyword(groupId);
		} else {
			return this.cfChannelDao.prefixSearch(name, groupId);
		}
	}

	@Override
	public List<CfChannel> listByGroupIdAndPage(int groupId) {
		return cfChannelDao.listByGroupIdAndPage(groupId);
	}

}
