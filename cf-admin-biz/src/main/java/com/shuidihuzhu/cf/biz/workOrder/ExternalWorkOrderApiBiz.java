package com.shuidihuzhu.cf.biz.workOrder;

import com.shuidihuzhu.cs.work.order.client.dto.CascadeRuleFields;
import com.shuidihuzhu.cs.work.order.client.dto.SystemFieldsMappingRule;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */
public interface ExternalWorkOrderApiBiz {
    Boolean syncWorkOrderInfoAdminWorkOrder(Long workOrderId,Integer orderStatus,Integer level,Integer operatorId);

    Boolean syncWorkOrderInfoAdminWorkOrderFlow(Long workOrderId, Integer problemType);

    SystemFieldsMappingRule mappingRuleFields(String fieldCode);

    CascadeRuleFields fieldsCascade(long parentId);

    CascadeRuleFields queryAllFeldsCascade();

    Map<String,String> getAccessImages(List<String> images);
}
