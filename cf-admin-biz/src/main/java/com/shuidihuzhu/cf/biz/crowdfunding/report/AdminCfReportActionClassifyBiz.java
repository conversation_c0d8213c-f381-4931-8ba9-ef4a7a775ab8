package com.shuidihuzhu.cf.biz.crowdfunding.report;

import com.shuidihuzhu.cf.model.report.CfReportActionClassify;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/4/17
 */
public interface AdminCfReportActionClassifyBiz {

    int add(String actionClassify);

    int updateActionClassify(String actionClassify, long id);

    int updateIsUse(int isUse, long id);

    List<CfReportActionClassify> getAll();

    List<CfReportActionClassify> getByIds(List<Long> ids);

    List<CfReportActionClassify>  getByIdsAndIsUse(List<Long> ids, int isUse);


    List<CfReportActionClassify> getByUse(int isUse);

    CfReportActionClassify getById(long id);
}
