package com.shuidihuzhu.cf.biz.crowdfunding.report;

import com.shuidihuzhu.cf.model.crowdfunding.CfSendProve;

public interface CfSendProveBiz {

    int insertOne(CfSendProve cfSendProve);

    CfSendProve getById(long id);

    int auditPictureUrl(long id, String pictureUrl, int pictureAuditStatus, String pictureRejectedReason);

    int updatePictureAuditStatusById(long id, int pictureAuditStatus);

    CfSendProve getLastOneByCaseId(int caseId);

    int updateRejectedReason(long id, String rejectedReason);

    int updateCancelReason(long id, String cancelReason);

}
