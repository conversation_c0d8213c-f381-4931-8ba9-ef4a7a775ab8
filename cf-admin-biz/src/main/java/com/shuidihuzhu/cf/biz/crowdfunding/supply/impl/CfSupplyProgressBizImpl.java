package com.shuidihuzhu.cf.biz.crowdfunding.supply.impl;

import com.google.common.collect.*;
import com.shuidihuzhu.cf.biz.crowdfunding.supply.CfSupplyProgressBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfSupplyProgressDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyProgress;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2020-01-10 10:34
 **/
@Service
public class CfSupplyProgressBizImpl implements CfSupplyProgressBiz {

    @Autowired
    private CfSupplyProgressDao cfSupplyProgressDao;

    @Override
    public boolean add(CfInfoSupplyProgress cfInfoSupplyProgress) {
        return cfSupplyProgressDao.add(cfInfoSupplyProgress) == 1;
    }

    @Override
    public boolean reject(CfInfoSupplyProgress supplyProgress) {
        return cfSupplyProgressDao.reject(supplyProgress) == 1;
    }

    @Override
    public boolean pass(long id, String imgUrls, int progressId) {
        return cfSupplyProgressDao.pass(id, imgUrls, progressId) == 1;
    }

    @Override
    public List<CfInfoSupplyProgress> listBySupplyActionId(long actionId) {
        return cfSupplyProgressDao.listBySupplyActionId(actionId);
    }

    @Override
    public List<CfInfoSupplyProgress> listByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return cfSupplyProgressDao.listByIds(ids);
    }

    @Override
    public boolean reprocess(long supplyProgressId) {
        return cfSupplyProgressDao.reprocess(supplyProgressId) == 1;
    }

    @Override
    public Map<Long, CfInfoSupplyProgress> listFirstSupplyProgress(List<Long> supplyProgressIds) {
        if (CollectionUtils.isEmpty(supplyProgressIds)) {
            return Maps.newHashMap();
        }
        List<CfInfoSupplyProgress> supplyProgresses = cfSupplyProgressDao.listByActionIds(supplyProgressIds);
        Ordering<CfInfoSupplyProgress> sortByCreateTime = Ordering.natural().onResultOf(CfInfoSupplyProgress::getCreateTime);
        return supplyProgresses
                .stream()
                .sorted(sortByCreateTime)
                .collect(Collectors.toMap(CfInfoSupplyProgress::getProgressActionId, Function.identity(), (before, after) -> before));
    }

    @Override
    public List<CfInfoSupplyProgress> getByActionId(long progressActionId) {
        return cfSupplyProgressDao.getByActionId(progressActionId);
    }
}
