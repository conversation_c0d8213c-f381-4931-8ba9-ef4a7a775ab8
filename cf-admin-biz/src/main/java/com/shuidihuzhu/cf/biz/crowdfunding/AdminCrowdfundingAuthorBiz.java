package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by Ahrievil on 2017/8/23
 */
public interface AdminCrowdfundingAuthorBiz {
    CrowdfundingAuthor get(Integer crowdfundingId);

    List<CrowdfundingAuthor> getByName(String name);

    int add(CrowdfundingAuthor crowdfundingAuthor);

    int update(CrowdfundingAuthor crowdfundingAuthor);

    Map<Integer, CrowdfundingAuthor> getByInfoIdList(List<Integer> infoIdList);

    String selectNameByCfId(int crowdfundingId);

    int getApplyCount(String name, Date startTime, Date endTime);

    List<CrowdfundingAuthor> selectByCaseIdList(List<Integer> list);

    List<CrowdfundingAuthor> selectByNameList(List<String> nameList);

    List<CrowdfundingAuthor> selectByIdCardList(List<String> idCardList);

    boolean isFaceIdSuccess(int caseId);

    boolean isFaceIdSuccess(CrowdfundingAuthor author);


}
