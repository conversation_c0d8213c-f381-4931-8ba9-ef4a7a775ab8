package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderClassifySettings;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderClassifySettingsRecord;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderClassifySettingsVo;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;
import java.util.Map;

public interface AdminWorkOrderClassifySettingsBiz {

    List<AdminWorkOrderClassifySettingsVo> queryAllValidSettings(Integer available);

    Response insert(long parentId, int userId, String allText, boolean autoTriger);

    Response update(long id, long parentId, int userId, String allText, boolean autoTriger);

    Response delete(long id, long parentId, int userId);

    List<AdminWorkOrderClassifySettings> queryAllSettings();

    List<AdminWorkOrderClassifySettings> selectChildClassifySettings(long parentId, int deleteStatus);

    AdminWorkOrderClassifySettings selectClassifySettingsById(long id);

    List<AdminWorkOrderClassifySettings> selectClassifySettingsByText(String text);

    String joinClassifyTextBySecondId(long secondId);

    Long querySecondClassifyIdByText(List<String> problemClassifys);

    List<AdminWorkOrderClassifySettingsRecord> showRecords(long problemClassify);

    boolean changeClassify(long classifyId, int changeAction, int upId, int downId, int adminUserId);
}
