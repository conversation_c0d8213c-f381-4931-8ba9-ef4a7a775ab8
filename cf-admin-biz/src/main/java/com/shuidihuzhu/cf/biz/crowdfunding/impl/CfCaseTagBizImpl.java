package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.CfCaseTagBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfCaseTagDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfCaseTag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by ahrievil on 2017/3/28.
 */
@Service
public class CfCaseTagBizImpl implements CfCaseTagBiz {

    @Autowired
    private CfCaseTagDao cfCaseTagDao;

    @Override
    public List<CfCaseTag> getByInfoId(int infoId) {
        return cfCaseTagDao.getByInfoId(infoId);
    }

    @Override
    public int addCaseTags(List<CfCaseTag> tagList) {
        return cfCaseTagDao.addCaseTags(tagList);
    }

    @Override
    public int deleteTags(int infoId) {
        return cfCaseTagDao.deleteTags(infoId);
    }
}
