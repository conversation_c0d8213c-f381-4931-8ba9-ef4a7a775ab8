package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.client.cf.growthtool.model.CfVolunteerMaterialDO;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteerVo;
import com.shuidihuzhu.client.cf.growthtool.model.VolunteerSearchModel;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/2/27.
 */
public interface CrowdfundingVolunteerBiz {

    List<CrowdfundingVolunteer> getVolunteerNew(Integer pageNum, Integer pageSize, VolunteerSearchModel volunteerSearchModel);

    /**
     * 添加志愿者
     *
     * @param crowdfundingVolunteer
     * @return
     */
    Response<Integer> addVolunteer(CrowdfundingVolunteer crowdfundingVolunteer);

    /**
     * 查看志愿者信息详情
     *
     * @param id
     * @return
     */
    CrowdfundingVolunteerVo getVolunteerInfo(long id);

    /**
     * 编辑志愿者信息
     *
     * @param crowdfundingVolunteer
     * @return
     */
    Response<Integer> updateVolunteerInfo(CrowdfundingVolunteer crowdfundingVolunteer);

    /**
     * 通过id查询二维码
     *
     * @param id
     * @return
     */
    String getQRCode(long id);

    /**
     * 更新唯一标识和二维码
     *
     * @param qrCode
     * @param id
     * @return
     */
    int updateFields(String qrCode, long id);

    /**
     * 通过唯一标识查询名称
     *
     * @param uniqueCodes
     * @return
     */
    List<CrowdfundingVolunteer> getVolunteerName(List<String> uniqueCodes);

    /**
     * 通过id查找唯一标识
     * @param id
     * @return
     */
    String getUniqueCodeById(long id) throws Exception;



    CrowdfundingVolunteer getByUniqueCode(String volunteerUniqueCode);

    int addCfVolunteerMaterial(CfVolunteerMaterialDO cfVolunteerMaterialDO);

    int updateCfVolunteerMaterial(CfVolunteerMaterialDO cfVolunteerMaterialDO);

    int updateApplyStatusById(long id,
                              int applyStatus,
                              String operatorName,
                              int operatorUserId,
                              String angelUrl,
                              String refuseReasons,String qrCode);

    CfVolunteerMaterialDO getCfVolunteerMaterialDOByUniqueCode(String uniqueCode);
    /**
     * 根据C端userId -> C端手机号 -> 线下团队手机号
     * 匹配此C端用户是否是线下团队
     * @param userId
     * @return
     */
    boolean checkVolunteerByUserId(long userId);


    void send553Sms(List<String> mobiles, String qrCode);
}
