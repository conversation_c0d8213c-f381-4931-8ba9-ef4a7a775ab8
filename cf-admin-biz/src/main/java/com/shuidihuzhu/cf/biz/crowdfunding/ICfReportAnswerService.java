package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.AdminReportProblemAnswerDetail;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/12/16 上午11:36
 * @desc
 */
public interface ICfReportAnswerService {
    int insert(AdminReportProblemAnswerDetail answerDetail);

    List<AdminReportProblemAnswerDetail> query(int caseId, int reportId, int type);

    List<AdminReportProblemAnswerDetail> queryByType(int caseId, int type);

    List<AdminReportProblemAnswerDetail> queryByIds(List<Long> ids);

    AdminReportProblemAnswerDetail queryById(Long id);

    List<AdminReportProblemAnswerDetail> queryByCaseId(int caseId);

    AdminReportProblemAnswerDetail queryLastByCaseId(int caseId, int type);

    List<AdminReportProblemAnswerDetail> queryByCaseIdAndType(int caseId, int type);


}
