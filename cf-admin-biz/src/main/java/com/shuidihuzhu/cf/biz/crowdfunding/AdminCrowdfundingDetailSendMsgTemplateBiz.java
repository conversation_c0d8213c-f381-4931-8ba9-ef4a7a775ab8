package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.AdminSmsTemplateSettingsInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingMsgContent;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingMsgTemplateVo;
import com.shuidihuzhu.msg.model.SmsTemplate;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/7/19.
 */
public interface AdminCrowdfundingDetailSendMsgTemplateBiz {

    List<CrowdfundingMsgContent> getAllMsgTemplate();

    int updateMsgTemplate(CrowdfundingMsgContent crowdfundingMsgContent);

    CrowdfundingMsgContent getByKey(String key);

    List<CrowdfundingMsgTemplateVo> setVolist(List<CrowdfundingMsgTemplateVo> Volist, List<CrowdfundingMsgContent> MsgTemplatelist);

    String getMsgTitleByContent(String message);

    List<Integer> getAllSmsAuthGroup(int userId, int authType);

    int addSmsTemplate(int smsGroup, String modelNum, int userId);

    int queryCurrentPriorityBySmsGroup(int smsGroup);

    int updateStatusById(int id, int dataStatus, int userId);

    int updateSmsTemplatePriority(int upId, int downId, int operateType, int userId);

    List<AdminSmsTemplateSettingsInfo.SmsSettingsRecordView> selectRecordByTemplateId(int smsTemplateSettingsId);

    List<AdminSmsTemplateSettingsInfo.SmsContentResult> selectAuthSmsContent(int userId);

    AdminSmsTemplateSettingsInfo.SmsTemplateView selectTemplateByModelNum(String modelNum);


    List<AdminSmsTemplateSettingsInfo.SmsTemplateView> selectTemplateByParam(int smsGroup, String templateTitle,
                                                                             Integer operatorId,
                                                                             Integer dataStatus);

    List<SmsTemplate> getTemplateByModelNum(String modelNum);

    List<AdminSmsTemplateSettingsInfo.SmsContentResult> getGroupContentList(int smsGroupCode);
}
