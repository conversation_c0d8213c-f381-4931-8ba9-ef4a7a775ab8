package com.shuidihuzhu.cf.biz.admin.impl.verifycount;

import com.shuidihuzhu.cf.biz.admin.verifycount.VerifyCountRoute;
import com.shuidihuzhu.cf.constants.crowdfunding.RedisKeyCons;
import com.shuidihuzhu.cf.enums.admin.AdminVerifyTypeEnum;
import com.shuidihuzhu.cf.vo.admin.VerifyCountVo;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/11/4
 */
@Service
@RefreshScope
public class CfFirstApproveIdCardVerifyCountImpl implements VerifyCountRoute {

    @Value("${baseinfo.first-approve.validate-max-time:40}")
    private int MAX_VERIFY_COUNT = 40;

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;


    @Override
    public String getKey() {
        return "CfFirstApproveIdCardVerify";
    }

    @Override
    public String getName() {
        return "发起案例，提交初审材料时，验证本人和患者身份证";
    }

    @Override
    public AdminVerifyTypeEnum getType() {
        return AdminVerifyTypeEnum.ID_CARD_VERIFY;
    }

    @Override
    public int getLimit() {
        return MAX_VERIFY_COUNT;
    }

    @Override
    public VerifyCountVo getCurrentCountVo(long userId) {
        VerifyCountVo verifyCountVo = initVerifyCountVo();
        Integer verifyCount = this.redissonHandler.get(getTodayVerifyRedisKey(userId), Integer.class);
        verifyCountVo.setCurrentCount(verifyCount == null ? 0 : verifyCount);
        return verifyCountVo;
    }

    @Override
    public boolean clear(long userId) {
        return  this.redissonHandler.del(getTodayVerifyRedisKey(userId));
    }

    @Override
    public void innerTest(long userId) {
        redissonHandler.incrAndSetTimeWhenNotExists(getTodayVerifyRedisKey(userId), RedissonHandler.ONE_DAY);
    }

    private String getTodayVerifyRedisKey(long userId) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String dateStr = dateFormat.format(new Date());
        return "cf-first-approve-verifyidcard-" + dateStr + userId;
    }

}
