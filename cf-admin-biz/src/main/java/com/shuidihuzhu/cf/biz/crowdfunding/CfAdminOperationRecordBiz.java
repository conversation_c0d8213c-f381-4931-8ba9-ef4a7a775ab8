package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfOperationRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * Created by ahrievil on 2017/2/7.
 */
public interface CfAdminOperationRecordBiz {

    int add(CfOperationRecord cfOperationRecord);

    void addOneOperationRecord(String infoId,long userId,int opertion,String reason);

    CfOperationRecord selectOpByInfoId(String infoId);

    CfOperationRecord selectContact(String infoId);

    Map<String, Integer> selectRefuseOperation(int curNum);

    //获取最近的一个操作记录
    List<CfOperationRecord> selectOpByInfoIds(List<String> infoUuids);

    CfOperationRecord getLastByOperation(String infoId, int operation);
}
