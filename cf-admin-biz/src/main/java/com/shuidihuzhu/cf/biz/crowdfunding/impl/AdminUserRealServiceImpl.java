package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminUserRealService;
import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import com.shuidihuzhu.cf.client.feign.CfUserInfoFeignClient;
import com.shuidihuzhu.cf.client.feign.cipher.constants.DesensitizeEnum;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationRecordDTO;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.model.crowdfunding.UserRealInfo;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.common.web.util.ContextUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class AdminUserRealServiceImpl implements AdminUserRealService {

    @Autowired
    private CfUserInfoFeignClient userInfoFeignClient;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private CommonOperationRecordClient commonOperationRecordClient;
    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Resource
    private MaskUtil maskUtil;

    @Override
    public int unbind(long realUserId, int userId, int id, String comment, String pic) {

        FeignResponse<Integer> result = userInfoFeignClient.deleteById(id);
        log.info("解除用户的证实绑定关系 userId:{} id:{} result:{}", userId, id, JSON.toJSONString(result));

        commonOperationRecordClient.create()
                .buildBasicPlatform(realUserId, userId, OperationActionTypeEnum.CF_USER_REAL_UNBIND)
                .buildExtValue("comment",  Objects.requireNonNullElse(comment, ""))
                .buildExtValue("pic",  Objects.requireNonNullElse(pic, ""))
                .buildCaseId(0)
                .save();
        return result != null && result.getData() != null ? result.getData() : 0;
    }

    @Override
    public List<UserRealInfoView> getOnceAllSuccess(long userId) {
        FeignResponse<List<UserRealInfo>> result = userInfoFeignClient.getOnceAllSuccess(userId);
        return result != null && CollectionUtils.isNotEmpty(result.getData())  ? maskUserIdCard(result.getData()) : Lists.newArrayList();
    }

    @Override
    public List<UserRealInfoView> getByUserIdAndIdCard(String mobile, String idCard) {

        if (StringUtils.isBlank(mobile) && StringUtils.isBlank(idCard)) {
            return Lists.newArrayList();
        }

        mobile = Objects.requireNonNullElse(mobile, "");
        idCard = Objects.requireNonNullElse(idCard, "");


        FeignResponse<List<UserRealInfo>> result = userInfoFeignClient.getByUserIdAndIdCard(mobile, idCard);

        return result != null && CollectionUtils.isNotEmpty(result.getData())  ? maskUserIdCard(result.getData()) : Lists.newArrayList();
    }

    private  List<UserRealInfoView> maskUserIdCard(List<UserRealInfo> realInfoList) {

        List<UserRealInfoView> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(realInfoList)) {
            return result;
        }

        for (UserRealInfo realInfo : realInfoList) {
            UserRealInfoView infoView = new UserRealInfoView();
            BeanUtils.copyProperties(realInfo, infoView);

            String idCard = shuidiCipher.decrypt(realInfo.getCryptoIdCard());
            infoView.setIdCardMask(maskUtil.buildByDecryptStrAndType(idCard, DesensitizeEnum.IDCARD));
            infoView.setIdCard(null);

            OperationRecordDTO recordDTO = commonOperationRecordClient.getLastByBizIdAndActionTypes(realInfo.getUserId(),
                    OperationActionTypeEnum.CF_USER_REAL_UNBIND);
            if (recordDTO != null) {
                infoView.setLastOperateName(recordDTO.getNameWithOrg());
                infoView.setOperateTime(recordDTO.getActionTime());
            }

            UserInfoModel infoModel = userInfoServiceBiz.getUserInfoByUserId(realInfo.getUserId());
            if (infoModel != null) {
                infoView.setMobileMask(maskUtil.buildByEncryptPhone(infoModel.getCryptoMobile()));
            }
            infoView.setMobile(null);
            result.add(infoView);
        }

        return result;
    }

    @Data
    public static class UserRealInfoView extends UserRealInfo {
        private String mobile;
        private NumberMaskVo mobileMask;
        private NumberMaskVo idCardMask;
        private String lastOperateName;
        private Date operateTime;
    }


}
