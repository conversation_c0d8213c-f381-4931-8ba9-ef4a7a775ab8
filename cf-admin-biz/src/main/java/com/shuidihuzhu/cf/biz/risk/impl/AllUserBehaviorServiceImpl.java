package com.shuidihuzhu.cf.biz.risk.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAuthClientV1;
import com.shuidihuzhu.cf.admin.util.EnumPropertyUtils;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.risk.IAllUserBehaviorService;
import com.shuidihuzhu.cf.biz.risk.IUserBehaviorService;
import com.shuidihuzhu.cf.client.feign.CfCommonFeignClient;
import com.shuidihuzhu.cf.client.feign.CfUserInfoFeignClient;
import com.shuidihuzhu.cf.client.feign.CrowdfundingOrderFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdFundingFixDataDao;

import com.shuidihuzhu.cf.enums.activity.EnumPropertyVO;
import com.shuidihuzhu.cf.enums.crowdfunding.UserBehaviorEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.UserModuleEnum;
import com.shuidihuzhu.cf.model.crowdfunding.AdminUserBehaviorDetail;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoBlessing;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.vo.duiba.UserLoveScoreView;
import com.shuidihuzhu.sdb.order.client.InsuranceOrderQueryClient;
import com.shuidihuzhu.sdb.order.model.constant.common.OrderResponse;
import com.shuidihuzhu.sdb.order.model.po.InsuranceOrderPO;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @time 2019/10/14 上午11:51
 * @desc
 */
@Slf4j
@Service
public class AllUserBehaviorServiceImpl implements IAllUserBehaviorService, InitializingBean, ApplicationContextAware {

    private List<IUserBehaviorService> userBehaviorServices = Lists.newArrayList();

    @Setter
    private ApplicationContext applicationContext;

    @Autowired
    private SeaAuthClientV1 seaAuthClientV1;
    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Autowired
    private InsuranceOrderQueryClient sdbOrderBiz;
    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingBiz;
    @Autowired
    private CommonOperationRecordClient commonOperateClient;
    @Autowired
    private CfUserInfoFeignClient userFeignClient;
    @Autowired
    private AdminCrowdFundingFixDataDao fixDataDao;
    @Autowired
    private CfCommonFeignClient commonFeignClient;
    @Autowired
    private CrowdfundingOrderFeignClient crowdfundingOrderFeignClient;


    private final static List<Integer> HUZHU_ORDER_STATUS = Lists.newArrayList(1, 22, 23, 25);
    private final static List<Integer> SDB_ORDER_STATUS = Lists.newArrayList(0, 1, 2, 3, 4, 20, 21, 30,
            5, 6, 11, 12);

    @Override
    public List<AdminUserBehaviorDetail> queryUserBehavior(int adminUserId, String mobile, long userId, String name, String identity, UserInfoModel userInfo, UserBehaviorEnum behaviorEnum) {

        Set<UserBehaviorEnum> ownedBehavior = Sets.newHashSet(getOwnedBehaviorEnum(adminUserId));
        List<AdminUserBehaviorDetail> behaviorDetails = new CopyOnWriteArrayList<>();
        userBehaviorServices.parallelStream().forEach(item -> {
            try {
                UserBehaviorEnum behavior = item.getBehavior();
                if (!ownedBehavior.contains(behavior)) {
                    return;
                }
                if(behaviorEnum != UserBehaviorEnum.DEFAULT && behaviorEnum != behavior){
                    return;
                }
                if (userId <= 0 && !item.allowNoUserId()){
                    return;
                }

                List<AdminUserBehaviorDetail> subBehaviorDetails = item.buildBehaviorDetail(mobile, userId, name, identity, userInfo, behaviorEnum);
                if (CollectionUtils.isNotEmpty(subBehaviorDetails)) {
                    behaviorDetails.addAll(subBehaviorDetails);
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });

        sort(behaviorDetails);

        return behaviorDetails;
    }

    @Override
    public List<EnumPropertyVO> getOwnedBehavior(int adminUserId) {
        return getOwnedBehaviorEnum(adminUserId).stream().map(this::c).collect(Collectors.toList());
    }

    public List<UserBehaviorEnum> getOwnedBehaviorEnum(int adminUserId) {
        List<UserBehaviorEnum> results = Lists.newArrayList();
        List<UserBehaviorEnum> allEnable = Arrays.stream(UserBehaviorEnum.values())
                .filter(UserBehaviorEnum::isEnable)
                .collect(Collectors.toList());
        List<String> allPermission = allEnable.stream().map(UserBehaviorEnum::getPermission).collect(Collectors.toList());
        AuthRpcResponse<Set<String>> validResult = seaAuthClientV1.validUserPermissions(adminUserId, allPermission);
        if (validResult == null || !validResult.isSuccess()){
            return results;
        }
        Set<String> ownedPermission = validResult.getResult();
        if (CollectionUtils.isEmpty(ownedPermission)) {
            return results;
        }
        for (UserBehaviorEnum e : allEnable) {
            if (ownedPermission.contains(e.getPermission())) {
                results.add(e);
            }
        }
        return results;
    }

    @Override
    public List<EnumPropertyVO> getOwnedModule(int adminUserId) {
        List<EnumPropertyVO> results = Lists.newArrayList();
        List<UserModuleEnum> allEnable = Arrays.stream(UserModuleEnum.values())
                .filter(UserModuleEnum::isEnable)
                .collect(Collectors.toList());
        List<String> allPermission = allEnable.stream().map(UserModuleEnum::getPermission).collect(Collectors.toList());
        AuthRpcResponse<Set<String>> validResult = seaAuthClientV1.validUserPermissions(adminUserId, allPermission);
        if (validResult == null || !validResult.isSuccess()){
            return results;
        }
        Set<String> ownedPermission = validResult.getResult();
        if (CollectionUtils.isEmpty(ownedPermission)) {
            return results;
        }
        for (UserModuleEnum e : allEnable) {
            if (ownedPermission.contains(e.getPermission())) {
                results.add(EnumPropertyUtils.parseEnum(e));
            }
        }
        return results;
    }

    private EnumPropertyVO c(UserBehaviorEnum e) {
        EnumPropertyVO v = new EnumPropertyVO();
        v.setValue(e.getKey());
        v.setName(e.name());
        v.setContent(e.getValue());
        return v;
    }


    //按时间先后顺序
    private void sort(List<AdminUserBehaviorDetail> bahaviorDetail) {
        Collections.sort(bahaviorDetail, new Comparator<AdminUserBehaviorDetail>() {
            @Override
            public int compare(AdminUserBehaviorDetail o1, AdminUserBehaviorDetail o2) {
                if (o1.getTime().after(o2.getTime())) {
                    return 1;
                }
                if (o1.getTime().before(o2.getTime())) {
                    return -1;
                }
                return 0;
            }
        });
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Map<String,IUserBehaviorService> beansMap = applicationContext.getBeansOfType(IUserBehaviorService.class);
        List<IUserBehaviorService> beanList = Lists.newArrayList(beansMap.values()).stream().collect(Collectors.toList());
        userBehaviorServices = beanList;
    }

    public AdminErrorCode transferProperty(AdminUserBehaviorDetail.UserCaseProperty property) {

        log.info("资产转移 param:{}", JSON.toJSONString(property));
        String fromMobile = property.getFromMobile();
        String toMobile = property.getToMobile();
        if (!StringUtils.isNumeric(fromMobile) || !StringUtils.isNumeric(toMobile) || Objects.equals(fromMobile, toMobile)) {
            return AdminErrorCode.SYSTEM_PARAM_ERROR;
        }

        UserInfoModel fromUser = userInfoServiceBiz.getUserInfoByMobile(fromMobile);
        UserInfoModel toUser = userInfoServiceBiz.getUserInfoByMobile(toMobile);
        if (fromUser == null || fromUser.getUserId() == 0) {
            return AdminErrorCode.SYSTEM_PARAM_ERROR;
        }
        if (toUser == null || toUser.getUserId() == 0) {
            return AdminErrorCode.USER_NOT_REGISTER_SDC;
        }
        if (fromUser.getUserId() == toUser.getUserId()) {
            return AdminErrorCode.SYSTEM_PARAM_ERROR;
        }

        userFeignClient.transferUserInfo(fromUser.getUserId(), toUser.getUserId());

        commonOperateClient.create()
                .buildBasicPlatform(Long.valueOf(property.getToMobile()),
                        property.getUserId(), OperationActionTypeEnum.USER_PROPERTY_TRANSFER)
                .buildRemark(JSON.toJSONString(property))
                .save();

        return AdminErrorCode.SUCCESS;
    }

    public AdminUserBehaviorDetail.UserCaseProperty selectUserProperty(String mobile) {
        AdminUserBehaviorDetail.UserCaseProperty property = new AdminUserBehaviorDetail.UserCaseProperty();
        if (StringUtils.isBlank(mobile)) {
            property.setMsg("电话号码为空");
            return property;
        }

        UserInfoModel userInfo = userInfoServiceBiz.getUserInfoByMobile(mobile);
        if (userInfo == null || userInfo.getUserId() == 0) {
            property.setCanTransfer(0);
            property.setMsg("手机号未注册，无可转移资产");
            return property;
        }

        List<CrowdfundingInfo> result = crowdfundingBiz.selectByUserId(userInfo.getUserId());
        property.setCaseRaise(result == null ? 0 : result.size());

        FeignResponse<List<CrowdfundingOrder>> orderRes = crowdfundingOrderFeignClient.findCrowdfundingOrder(userInfo.getUserId(),
                "", "");
        int donateCnt = 0;
        int donateAmount = 0;
        if (orderRes != null && CollectionUtils.isNotEmpty(orderRes.getData())) {
            for (CrowdfundingOrder order : orderRes.getData()) {
                if (order.getPayStatus() != 1 || order.getValid() != 1) {
                    continue;
                }

                ++donateCnt;
                donateAmount += order.getAmount();

            }
        }
        property.setDonateCount(donateCnt);
        property.setDonateAmount(donateAmount);

        List<CfInfoBlessing> blessingList = fixDataDao.selectBlessByUserId(userInfo.getUserId());
        int blessingCnt = CollectionUtils.isEmpty(blessingList) ? 0 : blessingList.stream()
                .map(CfInfoBlessing::getInfoUuid).collect(Collectors.toSet()).size();
        property.setBlessingCnt(blessingCnt);

        List<CrowdFundingVerification> verifications = commonFeignClient.queryByVerifyUserId(userInfo.getUserId()).getData();
        property.setVerifyCnt(CollectionUtils.isEmpty(verifications) ? 0 : verifications.size());

        UserLoveScoreView scoreView = queryLoveScore(userInfo.getUserId());
        property.setScore(Objects.requireNonNullElse(scoreView.getScore(), 0));
        property.setLoveValue(Objects.requireNonNullElse(scoreView.getLoveValue(), 0));

        judgeHasBuyHuZhuOrSdb(userInfo.getUserId(), property);

        return property;
    }



    private UserLoveScoreView queryLoveScore(long userId) {
        FeignResponse<Map<Long, UserLoveScoreView>> userLoveViewRes = userFeignClient
                .queryUserLoveScoreView(Lists.newArrayList(userId));

        return userLoveViewRes != null && MapUtils.isNotEmpty(userLoveViewRes.getData())
                ? userLoveViewRes.getData().get(userId) : new UserLoveScoreView();
    }

    private void judgeHasBuyHuZhuOrSdb(long userId,
                                       AdminUserBehaviorDetail.UserCaseProperty property) {

        String tipMsg = hasBuyHuZhuOrSdb(userId);
        if (StringUtils.isNotBlank(tipMsg)) {
            property.setCanTransfer(0);
            property.setMsg(tipMsg);
        } else {
            property.setCanTransfer(1);
            property.setMsg("无水滴保或水滴互助产品，可以操作资金转移");
        }
    }

    public String queryHasRegister(String mobile) {
        UserInfoModel userInfo = userInfoServiceBiz.getUserInfoByMobile(mobile);
        if (userInfo == null || userInfo.getUserId() == 0) {
            return "转入手机号未注册水滴筹，无法转入资产";
        }

        return null;
    }

    private String hasBuyHuZhuOrSdb(long userId) {

        OrderResponse<List<InsuranceOrderPO>> sdbOrderList =  sdbOrderBiz.queryByUserId(userId, SDB_ORDER_STATUS);

        if (sdbOrderList != null && CollectionUtils.isNotEmpty(sdbOrderList.getData())) {
            return "该手机号下存在" + "【水滴保】" + "的产品，无法操作资产转移";
        }

        return  null;
    }

}
