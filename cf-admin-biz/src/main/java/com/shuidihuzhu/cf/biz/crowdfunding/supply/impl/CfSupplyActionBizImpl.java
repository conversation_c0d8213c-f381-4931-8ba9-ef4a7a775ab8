package com.shuidihuzhu.cf.biz.crowdfunding.supply.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.supply.CfSupplyActionBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfSupplyActionDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyAction;
import com.shuidihuzhu.cf.model.param.SupplyActionSearchParam;
import com.shuidihuzhu.client.cf.admin.model.CfInfoSupplyField;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-01-10 10:34
 **/
@Service
public class CfSupplyActionBizImpl implements CfSupplyActionBiz {

    @Autowired
    private CfSupplyActionDao cfSupplyActionDao;

    @Override
    public boolean insert(CfInfoSupplyAction supplyAction) {
        return cfSupplyActionDao.insert(supplyAction) == 1;
    }

    @Override
    public boolean updateHandleStatus(long id, int handleStatus) {
        return cfSupplyActionDao.updateHandleStatus(id, handleStatus) == 1;
    }

    @Override
    public boolean canSupplyProgress(int caseId) {
        List<CfInfoSupplyAction> supplyActions = listByCaseIdAndType(caseId, CfInfoSupplyAction.ActionType.progress.getCode());
        boolean cannotCreate = supplyActions.stream().anyMatch(item -> CfInfoSupplyAction.can_not_create_new_supply_action.contains(
                CfInfoSupplyAction.findHandleStatusByCode(item.getHandleStatus())
        ));
        return !cannotCreate;
    }

    @Override
    public CfInfoSupplyAction getById(long id) {
        return cfSupplyActionDao.getById(id);
    }

    @Override
    public List<CfInfoSupplyAction> listByCaseIdAndType(int caseId, int actionType) {
        return cfSupplyActionDao.listByCaseIdAndActionType(caseId, actionType);
    }

    @Override
    public List<CfInfoSupplyAction> listByCaseIdAndTypes(int caseId, List<Integer> actionTypes) {
        return cfSupplyActionDao.listByCaseIdAndActionTypes(caseId,actionTypes);
    }

    @Override
    public List<CfInfoSupplyAction> listByCaseIds(List<Integer> caseIds, int actionType) {
        if (CollectionUtils.isEmpty(caseIds)) {
            return Lists.newArrayList();
        }
        return cfSupplyActionDao.listByCaseIds(caseIds, actionType);
    }

    @Override
    public List<CfInfoSupplyAction> listBySearchParam(SupplyActionSearchParam searchParam) {
        PageHelper.startPage(searchParam.getCurrent(), searchParam.getPageSize());
        return cfSupplyActionDao.listBySupplyActionSearchParam(searchParam);
    }

    @Override
    public List<CfInfoSupplyAction> listCaseIdByReasonAndOrg(int reason, int org) {
        if (reason <= 0 && org <= 0) {
            return Lists.newArrayList();
        }
        return cfSupplyActionDao.listCaseIdByReasonAndOrg(reason, org);
    }


    @Override
    public List<CfInfoSupplyAction> listByIds(List<Long> ids) {
        return cfSupplyActionDao.listByIds(ids);
    }

    @Override
    public int insertCfInfoSupplyFields(List<CfInfoSupplyField> list, long actionId, int operationType) {

        cfSupplyActionDao.deleteSupplyField(actionId,operationType);

        list.stream().forEach(r->{r.setActionId(actionId);r.setOperationType(operationType);});

        return cfSupplyActionDao.insertCfInfoSupplyFields(list);
    }

    @Override
    public int insertCfInfoSupplyFields(List<CfInfoSupplyField> list, long actionId) {
        return insertCfInfoSupplyFields(list,actionId,CfInfoSupplyField.serviceType);
    }

    @Override
    public int updateForSubmit(long id, int handleStatus, String comment, String imgUrls) {
        return cfSupplyActionDao.updateForSubmit(id,handleStatus,comment,imgUrls);
    }

    @Override
    public List<CfInfoSupplyField> getByActionId(long actionId) {
        return cfSupplyActionDao.getByActionId(actionId);
    }

    @Override
    public List<CfInfoSupplyAction> listByCaseIdAndHandleStatus(int caseId, List<Integer> statusList) {
        return cfSupplyActionDao.listByCaseIdAndHandleStatus(caseId, statusList);
    }
}
