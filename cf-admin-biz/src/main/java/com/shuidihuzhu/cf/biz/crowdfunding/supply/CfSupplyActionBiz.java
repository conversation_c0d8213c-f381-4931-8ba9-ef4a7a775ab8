package com.shuidihuzhu.cf.biz.crowdfunding.supply;

import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyAction;
import com.shuidihuzhu.cf.model.param.SupplyActionSearchParam;
import com.shuidihuzhu.client.cf.admin.model.CfInfoSupplyField;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-01-10 10:26
 **/
public interface CfSupplyActionBiz {

    boolean insert(CfInfoSupplyAction supplyAction);

    /**
     * 更新状态
     */
    boolean updateHandleStatus(long id, int handleStatus);

    /**
     * 是否能下发动态
     */
    boolean canSupplyProgress(int caseId);

    CfInfoSupplyAction getById(long id);

    List<CfInfoSupplyAction> listByCaseIdAndType(int caseId, int actionType);

    List<CfInfoSupplyAction> listByCaseIdAndTypes(int caseId, List<Integer> actionTypes);


    List<CfInfoSupplyAction> listByCaseIds(List<Integer> caseIds, int actionType);

    /**
     * sea后台根据复杂条件查询
     */
    List<CfInfoSupplyAction> listBySearchParam(SupplyActionSearchParam searchParam);

    List<CfInfoSupplyAction> listCaseIdByReasonAndOrg(int reason, int org);

    /**
     * 根据id查询
     * @param ids
     * @return
     */
    List<CfInfoSupplyAction> listByIds(List<Long> ids);


    int insertCfInfoSupplyFields(List<CfInfoSupplyField> list,long actionId);

    int insertCfInfoSupplyFields(List<CfInfoSupplyField> list,long actionId,int operationType);


    int updateForSubmit( long id,
                         int handleStatus,
                         String comment,
                         String imgUrls);

    List<CfInfoSupplyField> getByActionId(long actionId);

    List<CfInfoSupplyAction> listByCaseIdAndHandleStatus(int caseId, List<Integer> statusList);

}
