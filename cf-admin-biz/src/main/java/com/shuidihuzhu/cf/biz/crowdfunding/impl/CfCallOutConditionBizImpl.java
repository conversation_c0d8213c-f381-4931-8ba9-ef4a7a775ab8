package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Lists;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingDetailSendMsgTemplateBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfCallOutConditionBiz;
import com.shuidihuzhu.cf.constants.admin.GeneralConstant;
import com.shuidihuzhu.cf.dao.crowdfunding.CfCallOutConditionDao;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.service.WxSubscribeEventServiceBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.CfFirstCallOutMsg;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingType;
import com.shuidihuzhu.cf.model.crowdfunding.CfCallOutCondition;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoStat;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.FinanceApproveService;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.client.baseservice.msg.v2.MsgClientV2;
import com.shuidihuzhu.msg.model.SmsTemplate;
import com.shuidihuzhu.wx.enums.AccountThirdTypeEnum;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * Created by ahrievil on 2017/5/15.
 */
@Slf4j
@Service
public class CfCallOutConditionBizImpl implements CfCallOutConditionBiz {

    @Autowired
    private CfCallOutConditionDao cfCallOutConditionDao;
    @Autowired
    private FinanceApproveService financeApproveService;
    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;
    @Resource
    private WxSubscribeEventServiceBiz wxSubscribeEventServiceBiz;

    @Autowired
    private AdminCrowdfundingDetailSendMsgTemplateBiz sendMsgTemplateBiz;

    @Resource
    private MsgClientV2Service msgClientV2Service;

    private static int SYSTEM_USER_ID = 62732;

    @Override
    public int add(CfCallOutCondition cfCallOutCondition) {
        return cfCallOutConditionDao.add(cfCallOutCondition);
    }


    @Override
    public void sendFirstCallOutMsg(Long userId, String mobile, CrowdfundingInfo crowdfundingInfo) {
        if (userId == null || StringUtils.isBlank(mobile)) {
            return;
        }
        //判断案例是否为梦想筹  如果是  剔除
        if (crowdfundingInfo.getType() == CrowdfundingType.DREAM.value()) {
           log.info("crowdfudningInfo type is dream! infoId:{}", crowdfundingInfo.getId());
           return;
        }

        //判读案例是否结束
        if (new Date().after(crowdfundingInfo.getEndTime())){
            log.info("crowdfudning_info is end!");
            return;
        }
        //查询用户是否关注主号
        boolean isSubscribe = false;
        List<Integer> userThirdTypeList = Lists.newArrayList();
        userThirdTypeList.add(GeneralConstant.FUNDRAISER_THIRD_TYPE);

        AccountThirdTypeEnum accountThirdTypeEnum;
        for (int userThirdType : userThirdTypeList) {
            accountThirdTypeEnum = AccountThirdTypeEnum.valueOf(userThirdType);
            isSubscribe = wxSubscribeEventServiceBiz.checkSubscribeByUserId(userId,accountThirdTypeEnum.getCode());
            if (isSubscribe){
                break;
            }
        }

        //分享次数
        CfInfoStat cfInfoStat = crowdfundingDelegate.getById(crowdfundingInfo.getId());
        if (cfInfoStat == null){
            log.error("crowdfundingInfo cfinfoStat is null ingoId:{}", crowdfundingInfo.getInfoId());
            return;
        }
        int shareCount = cfInfoStat.getShareCount();

        //查找通道信息
        String channel = crowdfundingInfo.getChannel();
        if (StringUtils.isBlank(channel)){
            channel = "";
        }
        // 要发送的消息
        CfFirstCallOutMsg cfFirstCallOutMsg = CfFirstCallOutMsg.NO;
        if (isSubscribe) {
            if (shareCount < 5){
                cfFirstCallOutMsg = CfFirstCallOutMsg.ATTENTION_SHARE_COUNT_LESS;
            } else if (5 < shareCount && shareCount < 20) {
                cfFirstCallOutMsg = CfFirstCallOutMsg.ATTENTION_SHARE_COUNT_MIDDLE;
            } else {
                cfFirstCallOutMsg = CfFirstCallOutMsg.ATTENTION_SHARE_COUNT_MORE;
            }
        }else{
            if (channel.startsWith("app") || channel.contains("fqck")) {
                if (shareCount < 5){
                    cfFirstCallOutMsg = CfFirstCallOutMsg.NO_ATTENTION_SHARE_COUNT_LESS;
                } else if (5 < shareCount && shareCount < 20) {
                    cfFirstCallOutMsg = CfFirstCallOutMsg.NO_ATTENTION_SHARE_COUNT_MIDDLE;
                } else {
                    cfFirstCallOutMsg = CfFirstCallOutMsg.NO_ATTENTION_SHARE_COUNT_MORE;
                }
            }else{
                if (shareCount < 5) {
                    cfFirstCallOutMsg = CfFirstCallOutMsg.APP_SHARE_COUNT_LESS;
                }else{
                    cfFirstCallOutMsg = CfFirstCallOutMsg.APP_SHARE_COUNT_MIDDLE_MORE;
                }
            }
        }

        //发送短信
        msgClientV2Service.sendSmsMsg(cfFirstCallOutMsg.getModelNum(), Lists.newArrayList(mobile), false);

        financeApproveService.addApprove(crowdfundingInfo, "发送短信",
                getSendSmsComment(cfFirstCallOutMsg) + "\n 发送手机号为：【" + mobile + "】",
                SYSTEM_USER_ID);
    }

    private String getSendSmsComment(CfFirstCallOutMsg cfFirstCallOutMsg) {
        List<SmsTemplate> smsTemplates = sendMsgTemplateBiz.getTemplateByModelNum(cfFirstCallOutMsg.getModelNum());
        return (CollectionUtils.isEmpty(smsTemplates) || smsTemplates.size() > 1 ) ? "" : smsTemplates.get(0).getText();
    }


}
