package com.shuidihuzhu.cf.biz.admin;

import com.shuidihuzhu.cf.model.mina.CfMinaMajorDiseaseTitleContent;

import java.util.List;

/**
 * Created by ahrievil on 2017/7/13.
 */
public interface AdminCfMinaMajorDiseaseTitleContentBiz {
    int updateContent(String content, int id);
    List<CfMinaMajorDiseaseTitleContent> selectWithLimit(int start, String content, int limit);
    int changeContent(String content, int id);
    CfMinaMajorDiseaseTitleContent selectById(int id);
    CfMinaMajorDiseaseTitleContent selectByTitle(String title);
    int changeTitle(String title, int id);
}
