package com.shuidihuzhu.cf.biz.es;

import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.client.cf.search.model.CfWorkOrderIndexByAuthorSearchParam;
import com.shuidihuzhu.client.cf.search.model.CfWorkOrderIndexSearchParam;
import com.shuidihuzhu.client.cf.search.model.CfWorkOrderIndexSearchResult;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

public interface CfSearch {

    public Pair<Long, List<AdminWorkOrder>> cfWorkOrderIndexSearch(CfWorkOrderIndexSearchParam searchParam);

    public Pair<Long, List<AdminWorkOrder>> cfWorkOrderIndexBySearch(CfWorkOrderIndexByAuthorSearchParam searchParam);

    CfWorkOrderIndexSearchResult queryWorkOrderBySearch(int current, int pageSize, long workOrderId, int caseId,
                                                        long operatorId, String mobile, String originatorMobile, List<Integer> orderTypes,
                                                        List<Integer> handleResults, List<Integer> lostStatus,
                                                        List<Integer> supplyAuditStatus, List<Integer> highRisk,
                                                        List<Integer> letterStatusList, String startHandleTime, String endHandleTime,
                                                        int credibleStatus, int paymentMethod, String asrResult);

     List<Integer> workOrderIndexSearch(CfWorkOrderIndexSearchParam searchParam);
}
