package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.CfPhotoAiBiz;
import com.shuidihuzhu.cf.dao.photoai.CfPhotoAiDao;
import com.shuidihuzhu.cf.model.admin.PhotoStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @package: com.shuidihuzhu.cf.biz.crowdfunding.impl
 * @Author: liujiawei
 * @Date: 2018/7/27  18:24
 */
@Service
@Slf4j
public class CfPhotoAiBizImpl implements CfPhotoAiBiz {
    @Autowired
    CfPhotoAiDao cfPhotoAiDao;

    @Override
    public PhotoStatus selectPhotoStatus(Integer infoId) {
        return selectPhotoStatus(infoId,0);
    }

    @Override
    public PhotoStatus selectPhotoStatus(Integer infoId, int photoType) {
        return cfPhotoAiDao.selectPhotoStatus(infoId,photoType);
    }

    @Override
    public boolean checkAIPhotoExist(Integer infoId) {
        if (cfPhotoAiDao.checkAIPhotoExist(infoId,0) > 0) {
            return true;
        }
        return false;
    }

    @Override
    public int updateArtificialRes(int crowdfundingId, int result) {
        return cfPhotoAiDao.updateArtificialRes(crowdfundingId, result,0);
    }
}
