package com.shuidihuzhu.cf.biz.admin.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.admin.AdminCfCreditSupplementBiz;
import com.shuidihuzhu.cf.dao.admin.AdminCfCreditSupplementDao;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfCreditSupplement;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AdminCfCreditSupplementBizImpl implements AdminCfCreditSupplementBiz {

    @Autowired
    private AdminCfCreditSupplementDao adminCfCreditSupplementDao;

    @Override
    public List<CfCreditSupplement> selectByInfoUuid(String infoUuid) {
        return adminCfCreditSupplementDao.selectByInfoUuid(infoUuid);
    }

    @Override
    public List<CfCreditSupplement> selectByInfoUuidList(List<String> infoUuidList) {
        if (CollectionUtils.isEmpty(infoUuidList)) {
            return Lists.newArrayList();
        }
        return adminCfCreditSupplementDao.selectByInfoUuidList(infoUuidList);
    }
}
