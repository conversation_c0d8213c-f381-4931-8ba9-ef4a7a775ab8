package com.shuidihuzhu.cf.biz.mapper;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity;
import com.shuidihuzhu.client.cf.api.model.CrmCityModel;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface CrowdfundingCityMapper {


    CrowdfundingCityMapper INSTANCE = Mappers.getMapper(CrowdfundingCityMapper.class);

    /**
     * CrmCityModel -> CrowdfundingCity
     *
     * @param crmCityModel
     * @return
     */
    CrowdfundingCity toCityDO(CrmCityModel crmCityModel);
}
