package com.shuidihuzhu.cf.biz.admin;

import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminUGCTask;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminOperatorVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminSensitiveVo;
import com.shuidihuzhu.cf.vo.crowdfunding.WorkOrderFirstApprove;
import com.shuidihuzhu.cf.vo.crowdfunding.firstapprove.FirstApproveConfiguration;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * Created by Ahrievil on 2017/11/30
 */
public interface AdminWorkOrderBiz {

    AdminWorkOrder createUgcAdminWorkOrder(AdminWorkOrderConst.Type orderType, AdminWorkOrderConst.Task orderTask);



    AdminWorkOrder createWorkOrderReport(AdminWorkOrderConst.Task taskCode, AdminWorkOrderConst.TaskType taskType,
                                         int creatorId, AdminWorkOrderConst.Level level, String comment);

    List<AdminWorkOrder> createBatchAdminWorkOrder(AdminWorkOrderConst.Type orderType, AdminWorkOrderConst.Task orderTask,
                                                   AdminWorkOrderConst.Role roleType, int operatorId, int level, String comment,
                                                   int size);

    List<AdminWorkOrder> assigningTask(AdminWorkOrderConst.Type orderType, AdminWorkOrderConst.Task orderTask,
                                       int operatorId, int size);

    List<AdminWorkOrder> assigningTasks(AdminWorkOrderConst.Type orderType, List<AdminWorkOrderConst.Task> orderTask,
                                       int operatorId, int size);


    List<AdminSensitiveVo> selectUgcSensitiveByAnchor(int anchor, int pageSize, boolean isPre, int operatorId,
                                                      int caseId, Integer result,
                                                      String title, long commentUserId, int contentType, int taskType
            , String hitWords, String startTime, String endTime);

    List<AdminSensitiveVo> selectUgcSensitiveByAnchorFromEs(int anchor, int pageSize, boolean isPre, int operatorId,
                                                      int caseId, Integer result,
                                                      String title, long commentUserId, int contentType, int taskType
            , String hitWords, String startTime, String endTime);


    List<AdminSensitiveVo> searchUgcSensitiveByPage(int current, int pageSize, boolean isPre, int operatorId,
                                                    Integer result,
                                                    int contentType, int taskType,
                                                    String hitWords, String startTime, String endTime);

//    List<AdminOperatorVo> listOperatorByOrderType(AdminWorkOrderConst.Type orderType);


    List<AdminWorkOrder> selectUgcBaseInfoByPage(int operatorId,
                                                 AdminWorkOrderConst.Type orderType,
                                                 List<AdminWorkOrderConst.Task> orderTasks,
                                                 int current, int pageSize,
                                                 Integer caseId,
                                                 AdminUGCTask.Result result,
                                                 Integer action,
                                                 Integer riskLevel,
                                                 String startTime,
                                                 String endTime);

    Pair<Long, List<AdminWorkOrder>> selectUgcBaseInfoByPageFromEs(int operatorId,
                                                                     AdminWorkOrderConst.Type orderType,
                                                                     List<AdminWorkOrderConst.Task> orderTasks,
                                                                     int current, int pageSize,
                                                                     Integer caseId,
                                                                     AdminUGCTask.Result result,
                                                                     Integer action,
                                                                     Integer riskLevel,
                                                                     String startTime,
                                                                     String endTime);


    AdminWorkOrder selectById(long id);

    List<AdminWorkOrder> selectByIdList(List<Long> list);

    int updateChangeable(long adminWorkOrderId, AdminWorkOrderConst.Status orderStatus,
                         AdminWorkOrderConst.Result handleResult, String comment);


    int updateWithOperatorIds(List<Long> list, int operatorId, int orderStatus);



    int selectUnHandleCount(AdminWorkOrderConst.Type orderType, List<AdminWorkOrderConst.Task> orderTasks);


    int updateOperatorId(Integer operatorId, long id);

    int updateOperator(long workOrderId, int orignalUserId, int targetUserId);

    int updateRoleOrOperatorIdWithIdList(Integer operatorId, List<Long> list);

    List<AdminWorkOrder> selectByIds(List<Long> list);


    AdminWorkOrder getWorkOrderByCaseId(int id,int type);

    int updateOrderStatus(int orderStatus,int handleResult,int id);

    int update(AdminWorkOrder adminWorkOrder);


    List<WorkOrderFirstApprove> getFirstUGC(int current, int pageSize, long caseId, int channel, int status, int right,int userId,
                                            int currOperatorId,
                                            Date operatorStartDate,
                                            Date operatorEndDate,
                                            long raiserId);

    Pair<Long, List<WorkOrderFirstApprove>> getFirstUGCFromEs(int current, int pageSize, long caseId, int channel, int status, int right,int userId,
                                            int currOperatorId,
                                            Date operatorStartDate,
                                            Date operatorEndDate,
                                            long raiserId);

    int insertOne(AdminWorkOrder adminWorkOrder);

    FirstApproveConfiguration getFirstApproveConfiguration(int current, int pageSize, String date, int operatorId);

    void cancelWorkOrders(List<AdminWorkOrder> workOrders, int userId);

    List<AdminWorkOrder> selectApplyingFirstApprovesByOperator(int oporatorId, int count, String date);

    void onHandleOrder(AdminWorkOrder adminWorkOrder);


    int resetWorkOrder(List<Long> list,int operatorId, int orderStatus, int taskType);

    int recoverStatusAndOperator(List<Long> list, int orderStatus,int operatorId, int orderType, int orderTask);

    boolean updateLevel(long workId, int newLevel);

    int updateMsg(long id, Integer orderStatus,Integer operatorId,Integer level);

    AdminWorkOrder getByOperatorIdAndStatus(Date createTime, long operatorId, int orderStatus);

    List<AdminWorkOrder> selectUnHandleTask(Date createTime);

}
