package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfDailyCharityMessage;
import com.shuidihuzhu.common.web.util.admin.BasicExample;

import java.util.Date;
import java.util.List;

/**
 * Created by ahrievil on 2017/5/11.
 */
public interface AdminCfDailyCharityMessageBiz {
    int deleteByPrimaryKey(Integer id);

    int insert(CfDailyCharityMessage record);

    CfDailyCharityMessage selectByPrimaryKey(Integer id);

    CfDailyCharityMessage selectByStartDate(Date date);

    int updateByPrimaryKey(CfDailyCharityMessage record);

    List<CfDailyCharityMessage> selectByPage(BasicExample basicExample, int current, int pageSize);
}
