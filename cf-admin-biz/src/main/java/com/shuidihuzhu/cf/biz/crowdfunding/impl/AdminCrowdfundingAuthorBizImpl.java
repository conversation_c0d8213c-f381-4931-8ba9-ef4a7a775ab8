package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingAuthorBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.client.feign.AuthorFeignClient;
import com.shuidihuzhu.cf.client.material.model.CfMaterialRequiredFieldEnum;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingAuthorDao;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor;
import com.shuidihuzhu.cf.service.message.AdminMsgClientService;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Created by Ahrievil on 2017/8/23
 */
@Service
public class AdminCrowdfundingAuthorBizImpl implements AdminCrowdfundingAuthorBiz {
    @Autowired
    private AdminCrowdfundingAuthorDao crowdfundingAuthorDao;
    @Autowired
    private Producer producer;

    @Autowired
    private AuthorFeignClient authorFeignClient;

    @Autowired
    private AdminMsgClientService msgClientService;

    @Override
    public CrowdfundingAuthor get(Integer crowdfundingId) {
        return crowdfundingAuthorDao.get(crowdfundingId);
    }

    @Override
    public int add(CrowdfundingAuthor crowdfundingAuthor) {
        return crowdfundingAuthorDao.add(crowdfundingAuthor);
    }

    @Override
    public List<CrowdfundingAuthor> getByName(String name) {
        return crowdfundingAuthorDao.getByName(name);
    }

    @Override
    public int update(CrowdfundingAuthor crowdfundingAuthor) {

        producer.send(new Message<>(MQTopicCons.CF,
                MQTagCons.CF_BASE_INFO_CHANGE_TAG, MQTagCons.CF_BASE_INFO_CHANGE_TAG + "-" +
                crowdfundingAuthor.getCrowdfundingId()
                + "-" + System.currentTimeMillis(), crowdfundingAuthor.getCrowdfundingId(), DelayLevel.S5));


        authorFeignClient.doubleWritePatient(crowdfundingAuthor, Lists.newArrayList(
                CfMaterialRequiredFieldEnum.PATIENT_NAME, CfMaterialRequiredFieldEnum.PATIENT_CRYPTO_ID_CARD));

        msgClientService.sendCasePatientChangeMsg(crowdfundingAuthor.getCrowdfundingId());

        return crowdfundingAuthorDao.update(crowdfundingAuthor);
    }

    @Override
    public Map<Integer, CrowdfundingAuthor> getByInfoIdList(List<Integer> infoIdList) {
        if(CollectionUtils.isEmpty(infoIdList)){
            return Collections.emptyMap();
        }
        List<CrowdfundingAuthor> crowdfundingAuthorList =new ArrayList<>();
        List<List<Integer>> splitList = Lists.partition(infoIdList, 500);//分割
        for (List<Integer> split : splitList) {//分批查询
            List<CrowdfundingAuthor> infos =  crowdfundingAuthorDao.getByInfoIdList(split);
            if (infos != null && infos.size() > 0) {
                crowdfundingAuthorList.addAll(infos);
            }
        }

        Map<Integer, CrowdfundingAuthor> crowdfundingAuthorMap = Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(crowdfundingAuthorList)){
            for (CrowdfundingAuthor crowdfundingAuthor : crowdfundingAuthorList) {
                if(crowdfundingAuthor != null){
                    crowdfundingAuthorMap.put(crowdfundingAuthor.getCrowdfundingId(), crowdfundingAuthor);
                }
            }
        }
        return crowdfundingAuthorMap;
    }

    @Override
    public String selectNameByCfId(int crowdfundingId) {
        return crowdfundingAuthorDao.selectNameByCfId(crowdfundingId);
    }

    @Override
    public int getApplyCount(String name, Date startTime, Date endTime) {
        return crowdfundingAuthorDao.getApplyCount(name,startTime,endTime);
    }

    @Override
    public List<CrowdfundingAuthor> selectByCaseIdList(List<Integer> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        List<CrowdfundingAuthor> crowdfundingAuthorList =new ArrayList<>();
        List<List<Integer>> splitList = Lists.partition(list, 500);//分割
        for (List<Integer> split : splitList) {//分批查询
            List<CrowdfundingAuthor> infos =  crowdfundingAuthorDao.selectByCaseIdList(split);
            if (infos != null && infos.size() > 0) {
                crowdfundingAuthorList.addAll(infos);
            }
        }
        return crowdfundingAuthorList;
    }

    @Override
    public List<CrowdfundingAuthor> selectByNameList(List<String> nameList) {
        if (CollectionUtils.isEmpty(nameList)) {
            return Lists.newArrayList();
        }
        List<String> names = new ArrayList<String>(new HashSet<String>(nameList));//去重
        List<CrowdfundingAuthor> crowdfundingAuthorList =new ArrayList<>();
        List<List<String>> splitList = Lists.partition(names, 500);//分割
        for (List<String> split : splitList) {//分批查询
            List<CrowdfundingAuthor> infos =  crowdfundingAuthorDao.selectByNameList(split);
            if (infos != null && infos.size() > 0) {
                crowdfundingAuthorList.addAll(infos);
            }
        }
        return crowdfundingAuthorList;
    }

    @Override
    public List<CrowdfundingAuthor> selectByIdCardList(List<String> idCardList) {
        if (CollectionUtils.isEmpty(idCardList)) return Lists.newArrayList();
        return crowdfundingAuthorDao.selectByIdCardList(idCardList);
    }

    @Override
    public boolean isFaceIdSuccess(int caseId) {

        CrowdfundingAuthor author = get(caseId);
        return isFaceIdSuccess(author);
    }

    @Override
    public boolean isFaceIdSuccess(CrowdfundingAuthor author) {
        return author != null && author.getFaceIdResult() == 20;
    }


}
