package com.shuidihuzhu.cf.biz.crmUserManage.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crmUserManage.CrmUserManageRecordBiz;
import com.shuidihuzhu.cf.dao.crmUserManage.UserManageLogDao;
import com.shuidihuzhu.cf.model.CrmUserManage.UserManage;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Slf4j
@Service
public class CrmUserManageRecordBizImpl implements CrmUserManageRecordBiz {

    @Autowired
    private UserManageLogDao manageRecordDao;
    @Autowired
    private Environment environment;


    @Override
    public int addUserManageRecords(List<UserManage.UserManageLog> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return 0;
        }

        int num = manageRecordDao.addUserManageLogs(recordList);
        log.info("添加操作记录.records:{}", recordList);
        return num;
    }

    @Override
    public int addCaseQualityRecord(String uuid, String personId, int raiseCaseQuality, int userId) {
        UserManage.UserManageLog manageLog = new UserManage.UserManageLog();
        manageLog.setCryptoMobile("");
        manageLog.setPersonId(StringUtils.trimToEmpty(personId));
        manageLog.setUuid(StringUtils.trimToEmpty(uuid));
        manageLog.setCryptoIdCard("");
        manageLog.setUserName("");
        manageLog.setVersion(UUID.randomUUID().toString());
        manageLog.setOperateSource(UserManage.UserMergeType.OPERATE.getCode());
        manageLog.setOperateId(userId);
        manageLog.setOperateComment("");
        manageLog.setExtComment("修改发起案例的紧急程度： " + UserManage.RaiseCaseQuality.valueOfCode(raiseCaseQuality));

        return addUserManageRecords(Lists.newArrayList(manageLog));
    }

    @Override
    public List<UserManage.UserManageLog> buildRelationRecords(List<UserManage.UserAccount> newAddAccounts,
                                                                String version, String extComment) {

        if(!environment.acceptsProfiles("production")){
            return Lists.newArrayList();
        }

        List<UserManage.UserManageLog> records = Lists.newArrayList();

        for (UserManage.UserAccount account : newAddAccounts) {

            UserManage.UserManageLog record = UserManage.UserManageLog.buildRecordFromAccount(account);
            record.setVersion(version);
            record.setOperateSource(UserManage.UserMergeType.SYSTEM_RULE_1.getCode());
            record.setOperateId(0);
            record.setOperateComment("");
            record.setExtComment(extComment);
            records.add(record);
        }
        return records;
    }


}
