package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfTagItem;

import java.util.List;

/**
 * Created by ahrievil on 2017/3/27.
 */
public interface CfTagItemBiz {
    List<CfTagItem> getList();
    int addOtherTag(List<String> list);
    List<CfTagItem> getByTagName(List<String> others);
    List<CfTagItem> getByInfoId(int infoId);
    List<CfTagItem> getByInfoIdOther(int infoId);
    List<CfTagItem> selectAllZore();
    int insertOne(CfTagItem cfTagItem);
}
