package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.CaseReportDealStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.CaseReportFollowStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCrowdfundingReportChild;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReportLabel;
import com.shuidihuzhu.cf.model.crowdfunding.report.AdminCfReportRiskCheck;
import com.shuidihuzhu.cf.model.crowdfunding.report.AdminCfReportRiskTagLabel;
import com.shuidihuzhu.cf.vo.crowdfunding.CrowdfundingReportVo;
import com.shuidihuzhu.cf.vo.crowdfunding.ReportMailVo;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface AdminCrowdfundingReportBiz {

    void add(CrowdfundingReport cr);

	void addRealNameReport(CrowdfundingReport crowdfundingReport);

	CrowdfundingReport query(int caseId, int reportId);

	CrowdfundingReport getByInfoId(Integer activityId);

	List<CrowdfundingReport> getListByInfoId(Integer activityId);

	List<CrowdfundingReport> getByInfoIds(List<Integer> activityId);

	List<CrowdfundingReport> getByInfoIdsV2(List<Integer> activityId);

	Map<Integer, CrowdfundingReport> getMapByInfoIds(List<Integer> activityId);

	List<CrowdfundingReport> getListByReportIds(List<Integer> reportIds);

    int updateDealStatus(int dealStatus, int id, int operatorId);

	int updateHandleAndConnectStatus(int id, int handleStatus, int connectStatus, long newOperatorId, int dealstatus);

	int updateReportListOperator(List<Integer> reportIds, int targetUserId);

	List<CrowdfundingReport> getListByPage(List<Integer> activityId, Integer current, Integer pageSize,
										   Integer crowdfundingStatus, Integer reportDealStatus, String caseType);

	List<CrowdfundingReportVo> getCrowdfundingReportVo(List<CrowdfundingReport> crowdfundingReportList,
													   List<CrowdfundingInfo> crowdfundingInfoList,
													   int caseStatus);

	List<CrowdfundingReport> selectByActivityIds(List<Integer> list);

    int newMailSender(int timer);
	//批量更新举报处理状态和案例跟进状态
	int updateReportStatusList(List<Integer> reportIds);

	//批量更新新增状态
	int updateReportIsNewStatus(List<Integer> reportIds);

	List<CrowdfundingReport> getListByCreateTime(int sortType);

	List<CrowdfundingReport> getListByCreateTimeAndInfoid(List<Integer> infoids, int sortType);

	List<CrowdfundingReport> getIsHaveNewReport(List<Integer> infoids);

	List<CrowdfundingReport> getListByInfoIdAndPage(int pageSize, int current, int infoId);

	//批量更新举报的处理状态
	int updateReportListDealStatus(List<Integer> reportIds, CaseReportDealStatus caseReportDealStatus, int operatorId);

	//批量更改举报的跟进状态
	int updateReportListFollowStatus(List<Integer> reportIds, CaseReportFollowStatusEnum caseReportFollowStatusEnum);

	Map<String, Object> getAddTrustMirror(String infoUuid);

	List<AdminCrowdfundingReportChild> getCaseReportCount(List<Integer> caseIds);

	List<CrowdfundingReport> getFirstCreateTimeByInfoIds(List<Integer> infoids);

	List<CrowdfundingReport> getLastCreateTimeByInfoIds(List<Integer> infoids);

	void checkReportByTimes();

	List<CrowdfundingReportLabel> getReportLabels(List<Integer> reportIds);

	boolean canHandleReport(int reportId, int userId);

	void updateReportLabel(int userId, List<CrowdfundingReportLabel> newLabelList);

	List<CrowdfundingReport> selectReportByCaseIdAndCreateTime(int caseId, Date createTime, int dealStatus);

	List<CrowdfundingReport> listByUserId(long reportUserId);

	int countByUserId(long reportUserId);

	int updateRiskLabel(String riskLabel,  int reportId);

    List<CrowdfundingReport> getListByInfoIdAndName(int caseId, String userName);

	int addLabel(int reportId,String reportTypes);

	int countByInfoId(int caseId);

	int addOrUpdateLabel(int reportId, String reportTypes, String details);

	List<CrowdfundingReportLabel> getReportLabelsModify(List<Integer> reportIds);

	int addReportRiskCheck(AdminCfReportRiskCheck adminCfReportRiskCheck);

	List<AdminCfReportRiskCheck> getReportRiskCheckByCaseId(int caseId);

	List<AdminCfReportRiskTagLabel> getReportRiskTagLabel(int caseId);

	int addCfReportRiskTagLabel(AdminCfReportRiskTagLabel adminCfReportRiskTagLabel);

	int updateEncryptMobileById(int id, String encryptMobile);

}
