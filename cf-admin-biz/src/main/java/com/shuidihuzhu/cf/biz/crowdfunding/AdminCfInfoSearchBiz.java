package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.vo.CaseSearchVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoVo;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

public interface AdminCfInfoSearchBiz {

    public List<CrowdfundingInfoVo> approveSearch(String mobile, String name, Integer id,
                                                  String title, String caseUserId, Integer refuseCountHandle,
                                                  Integer status, Integer isContact, Integer finished,
                                                  Integer operationStatus, Integer dataStatus, Integer sortHandle,
                                                  String startTime, String endTime, Integer handle,
                                                  Integer pageNum, Integer pageSize);

    public Pair<Long, List<CrowdfundingInfoVo>> approveSearchFromEs(String mobile, String name, Integer id,
                                                                    String title, String caseUserId, Integer refuseCountHandle,
                                                                    Integer status, Integer isContact, Integer finished,
                                                                    Integer operationStatus, Integer dataStatus, Integer sortHandle,
                                                                    String startTime, String endTime, Integer handle,
                                                                    Integer pageNum, Integer pageSize, String content,int creditStatus, int deferContactReasonType,
                                                                    int fuwuType);

    Pair<Long, List<CrowdfundingInfoVo>> caseSearchByEs(CaseSearchVo caseSearchVo);

    public List<CrowdfundingInfoVo> contactSearch(String mobile,
                                                  String name, Integer id,
                                                  String title, String caseUserId,
                                                  Integer callStatus, Integer isContact,
                                                  Integer sortHandle, String startTime,
                                                  String endTime, Integer handle,
                                                  Integer current, Integer pageSize);

    public Pair<Long, List<CrowdfundingInfoVo>> contactSearchFromEs(String mobile,
                                                                    String name, Integer id,
                                                                    String title, String caseUserId,
                                                                    Integer callStatus, Integer isContact,
                                                                    Integer sortHandle, String startTime,
                                                                    String endTime, Integer handle,
                                                                    Integer current, Integer pageSize,
                                                                    String content);

    public List<CrowdfundingInfoVo> reserveApproveSearch(String mobile, String name, Integer id,
                                                  String title, Integer pageNum, Integer pageSize);
}
