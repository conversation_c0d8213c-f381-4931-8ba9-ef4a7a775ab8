package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.CfFakeShareRecordBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfFakeShareRecordDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfFakeShareRecord;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by Ahrievil on 2017/8/31
 */
@Service
public class CfFakeShareRecordBizImpl implements CfFakeShareRecordBiz{

    @Autowired
    private CfFakeShareRecordDao cfFakeShareRecordDao;
    @Override
    public int insert(CfFakeShareRecord cfFakeShareRecord) {
        return cfFakeShareRecordDao.insert(cfFakeShareRecord);
    }

    @Override
    public int insertList(List<CfFakeShareRecord> list) {
        if (CollectionUtils.isEmpty(list)) return 0;
        return cfFakeShareRecordDao.insertList(list);
    }

    @Override
    public List<CfFakeShareRecord> selectByLimit(int start, int size) {
        return cfFakeShareRecordDao.selectByLimit(start, size);
    }
}
