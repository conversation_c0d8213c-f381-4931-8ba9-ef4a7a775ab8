package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdFundingProgressVo;
import com.shuidihuzhu.cf.vo.crowdfunding.AdminCrowdfundingProgress;
import com.shuidihuzhu.cf.vo.crowdfunding.CfProgressVo;
import com.shuidihuzhu.common.web.util.admin.BasicExample;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * Created by chao on 16/7/12.
 */
public interface AdminCrowdFundingProgressBiz {

    List<CfProgressVo> getProgressOfActivity(List<CrowdFundingProgress> crowdFundingProgressList);

    List<CrowdFundingProgress> queryAllByBasicExample(BasicExample basicExample, int current, int pageSize);

    List<CrowdFundingProgress> queryAllByBasicExample(BasicExample basicExample);

    Integer selectCountByFiveMin(Timestamp begin, Timestamp end);

    int updateImageUrls(String imageUrls, int id, int crowdfundingId);

    int updateImageUrlsByNoIsDelete(String imageUrls, int id, int crowdfundingId);

    CrowdFundingProgress getActivityProgressById(long id);

    int updateContent(long id, String content);

    List<CrowdFundingProgress> getByCreateTime(Timestamp startTime, Timestamp endTime);

    List<CrowdFundingProgress> getListByIds(List<Integer> ids);

    Map<Integer, CrowdFundingProgress> getMapByIds(List<Integer> ids);

    AdminCrowdfundingProgress getProgressById(Integer progresssId);

    int insertInCrowdfundingProgress(CrowdFundingProgress crowdFundingProgress);

    int delProgressById(long id);

    int reviveProgressById(long id);

}
