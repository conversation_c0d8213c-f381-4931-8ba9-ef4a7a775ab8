package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.admin.util.AdminListUtil;
import com.shuidihuzhu.cf.biz.crowdfunding.CfCommitVerifyItemBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseReasonTagBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfRefuseReasonTagDao;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonTag;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * Created by Ahrievil on 2017/7/26
 */
@Service
@Slf4j
public class CfRefuseReasonTagBizImpl implements CfRefuseReasonTagBiz {

    @Autowired
    private CfRefuseReasonTagDao cfRefuseReasonTagDao;

    @Override
    public int insertOne(CfRefuseReasonTag cfRefuseReasonTag) {
        return cfRefuseReasonTagDao.insertOne(cfRefuseReasonTag);
    }

    @Override
    public List<CfRefuseReasonTag> selectAllWithUuid(int start, int size, String infoUuid) {
        return cfRefuseReasonTagDao.selectAllWithUuid(start, size, infoUuid);
    }

    @Override
    public List<CfRefuseReasonTag> selectByDataType(int dataType) {
        return cfRefuseReasonTagDao.selectSortTagByDataType(dataType, 0);
    }

    @Override
    public CfRefuseReasonTag selectById(int id) {
        return cfRefuseReasonTagDao.selectById(id);
    }


    @Override
    public int updateReasonIds(String reasonIds, int id) {
        return cfRefuseReasonTagDao.updateTagStateById(id, reasonIds, null);
    }

    @Override
    public List<CfRefuseReasonTag> selectByTagIds(Set<Integer> set) {
        if (CollectionUtils.isEmpty(set))
            return Collections.emptyList();
        List<CfRefuseReasonTag> cfRefuseReasonTags = cfRefuseReasonTagDao.selectByTagIds(set);
        return cfRefuseReasonTags != null ? cfRefuseReasonTags : Collections.emptyList() ;
    }

    @Override
    public CfRefuseReasonTag selectByTagId(int id) {
        return cfRefuseReasonTagDao.selectById(id);
    }

    @Override
    public void addReasonTag(int userId, int dataType, String describe) {
        log.info("添加理由分类 userId:{} dataType:{}, describe:{}", userId, dataType, describe);

        List<CfRefuseReasonTag> allReasonTags = cfRefuseReasonTagDao.selectSortTagByDataType(dataType, null);

        int maxDateStep = CollectionUtils.isEmpty(allReasonTags) ? 1 : allReasonTags.get(allReasonTags.size() - 1).getDataStep() + 1;


        CfRefuseReasonTag cfRefuseReasonTag = new CfRefuseReasonTag(describe, dataType, "");
        // 新增的理由放最后
        cfRefuseReasonTag.setDataStep(maxDateStep);
        insertOne(cfRefuseReasonTag);
    }

    /**
     {@link com.shuidihuzhu.cf.model.crowdfunding.AdminSmsTemplateSettingsInfo.SmsOperateType}
     */
    @Override
    public void updateReasonTagDataStep(int userId, int upId, int downId, int operateType) {
        log.info("修改理由分类的优先级. userId:{} upId:{} downId:{} operateType:{}", userId, upId, downId, operateType);

        CfRefuseReasonTag upTag = selectById(upId);
        CfRefuseReasonTag downTag = selectById(downId);
        if (upTag == null || downTag == null) {
            throw new RuntimeException("前端传过来的上移，下移的id不对");
        }
        cfRefuseReasonTagDao.updateTagStateById(upTag.getId(), null, downTag.getDataStep());
        cfRefuseReasonTagDao.updateTagStateById(downTag.getId(), null, upTag.getDataStep());
    }

}
