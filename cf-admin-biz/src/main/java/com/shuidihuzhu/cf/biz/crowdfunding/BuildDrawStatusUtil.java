package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.AdminCfCaseStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminCfDrawCashConstant;
import com.shuidihuzhu.cf.enums.crowdfunding.CfDrawCashConstant;
import com.shuidihuzhu.cf.finance.model.drawcash.CfDrawCash;
import com.shuidihuzhu.cf.finance.model.vo.CfDrawCashApplyVo;
import com.shuidihuzhu.cf.vo.crowdfunding.CaseStatusBase;

public class BuildDrawStatusUtil {

    public static void buildDrawStatus(CaseStatusBase reportCaseVo, CfDrawCashApplyVo drawCashApplyVo) {
        if (null == reportCaseVo || null == drawCashApplyVo) {
            return;
        }
        if (drawCashApplyVo.getApplyStatus() > AdminCfDrawCashConstant.ApplyStatus.UNSUBMIT.getCode()) {
            //3.已申请提现
            reportCaseVo.setCaseStatus(AdminCfCaseStatus.DRAWCASH_SUBMIT.getValue());
        }
        if (drawCashApplyVo.getApplyStatus() == AdminCfDrawCashConstant.ApplyStatus.APPROVE_SUCCESS.getCode()) {
            if (drawCashApplyVo.getDrawStatus() == CfDrawCashConstant.DrawStatus.UNBUILD.getCode()) {
                //4.提现成功还未建立
                reportCaseVo.setCaseStatus(AdminCfCaseStatus.DRAWCASH_APPROVE.getValue());
            } else if (drawCashApplyVo.getDrawStatus() == CfDrawCashConstant.DrawStatus.HANDLE_SUCCESS.getCode()
                    || drawCashApplyVo.getDrawStatus() == CfDrawCashConstant.DrawStatus.HANDLE_PARTIAL_SUCCESS.getCode()
                    || drawCashApplyVo.getDrawStatus() == CfDrawCashConstant.DrawStatus.HANDLING.getCode()
                    || drawCashApplyVo.getDrawStatus() == CfDrawCashConstant.DrawStatus.MANUAL_SUCCESS.getCode()
            ) {
                //6.已打款&部分打款成功&打款成功
                reportCaseVo.setCaseStatus(AdminCfCaseStatus.DRAWCASH_SUCCESS.getValue());
            } else if (drawCashApplyVo.getDrawStatus() == CfDrawCashConstant.DrawStatus.UNHANDLE.getCode() ||
                    drawCashApplyVo.getDrawStatus() == CfDrawCashConstant.DrawStatus.HANDLE_FAILED.getCode()) {
                //5.已生成&打款失败
                reportCaseVo.setCaseStatus(AdminCfCaseStatus.DRAWCASH_BUILD.getValue());
            }
        }
    }

}
