package com.shuidihuzhu.cf.biz.admin;

import com.shuidihuzhu.cf.param.InitialAuditCreateOrderParam;

import java.util.Map;

/**
 * <AUTHOR>
 * @DATE 2019/6/25
 */
public interface ChuciAnalyticsBiz {


    /**
     * 提交初审信息
     * @param caseId
     */
    void initialVerifySubmit(InitialAuditCreateOrderParam initialAuditCreateOrderParam);


    /**
     * 初审操作信息
     * @param caseId
     * @param workOrderId
     * @param operatorId
     * @param result
     */
    void initialVerify(int caseId, long workOrderId, long operatorId, int result);


    /**
     * 驳回工单处理组上报
     * @param caseId
     * @param oper
     */
    void rejectWorkOrderTransfer(int caseId,long workOrderId,String oper);

    /**
     * 自动进入1v1服务的打点上报文章版本
     *
     * @param caseId
     */
    void content1v1Verify(InitialAuditCreateOrderParam initialAuditCreateOrderParam);


}
