package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.admin.AdminWorkOrderBiz;
import com.shuidihuzhu.cf.biz.admin.AdminWorkOrderRecordBiz;
import com.shuidihuzhu.cf.biz.admin.UserCommentBiz;
import com.shuidihuzhu.cf.biz.admin.exception.ServiceRuntimeException;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.biz.es.CfSearch;
import com.shuidihuzhu.cf.constants.AdminUserIDConstants;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminWorkOrderCaseDao;
import com.shuidihuzhu.cf.diff.Diff;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.enums.admin.UserCommentSourceEnum;
import com.shuidihuzhu.cf.enums.admin.errors.WorkOrderErrorEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminWorkOrderCaseConst;
import com.shuidihuzhu.cf.enums.crowdfunding.CfCallOutConditionTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingOperationEnum;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrderRecord;
import com.shuidihuzhu.cf.model.admin.UserComment;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderCase;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderCaseRecord;
import com.shuidihuzhu.cf.service.workorder.WorkOrderPermissionService;
import com.shuidihuzhu.cf.vo.crowdfunding.AdminApproveVo;
import com.shuidihuzhu.client.cf.search.model.CfWorkOrderIndexSearchParam;
import com.shuidihuzhu.client.cf.search.model.enums.CfWorkOrderIndexSortEnum;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Ahrievil
 */
@Slf4j
@Service
public class AdminWorkOrderCaseBizImpl implements AdminWorkOrderCaseBiz {

    @Autowired
    private AdminWorkOrderCaseDao adminWorkOrderCaseDao;
    @Autowired
    private AdminWorkOrderBiz adminWorkOrderBiz;
    @Autowired
    private AdminWorkOrderRecordBiz adminWorkOrderRecordBiz;
    @Autowired
    private AdminWorkOrderCaseRecordBiz adminWorkOrderCaseRecordBiz;
    @Autowired
    private UserCommentBiz userCommentBiz;

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler cfRedissonHandler;


    @Autowired
    private CfSearch cfSearch;



    private static boolean unHandle(AdminWorkOrderCase v) {
        int status = v.getStatus();
        return status == AdminWorkOrderCaseConst.Status.PROCESSING.getCode() ||
                status == AdminWorkOrderCaseConst.Status.CREATE.getCode();
    }

    @Override
    public int insertOne(AdminWorkOrderCase adminWorkOrderCase) {
        adminWorkOrderCase.setApproveResult(StringUtils.trimToEmpty(adminWorkOrderCase.getApproveResult()));
        int i = adminWorkOrderCaseDao.insertOne(adminWorkOrderCase);
        AdminWorkOrderCaseRecord record = new AdminWorkOrderCaseRecord(adminWorkOrderCase, 0);
        adminWorkOrderCaseRecordBiz.insertOne(record);
        return i;
    }

    @Override
    public int insertList(List<AdminWorkOrderCase> adminWorkOrderCases) {
        if (CollectionUtils.isEmpty(adminWorkOrderCases)) {
            return 0;
        }
        int i = adminWorkOrderCaseDao.insertList(adminWorkOrderCases);
        List<AdminWorkOrderCaseRecord> recordList = adminWorkOrderCases.stream()
                .map(val -> new AdminWorkOrderCaseRecord(val, 0)).collect(Collectors.toList());
        adminWorkOrderCaseRecordBiz.insertList(recordList);
        return i;
    }

    @Override
    public AdminWorkOrderCase selectById(long id) {
        return adminWorkOrderCaseDao.selectById(id);
    }



    @Override
    public AdminWorkOrderCase selectByCaseId(int caseId) {
        return adminWorkOrderCaseDao.selectByCaseId(caseId);
    }

    @Override
    public List<AdminWorkOrderCase> selectByCaseIdAndTypeAndStatusAndTime(int caseId, AdminWorkOrderCaseConst.CaseType type,
                                                                          AdminWorkOrderCaseConst.Status status,
                                                                          Timestamp createTime, Timestamp endTime) {
        return adminWorkOrderCaseDao.selectByCaseIdAndTypeAndStatusAndTime(caseId, type == null? null:type.getCode(),
                status == null? null: status.getCode(),
                createTime, endTime);
    }

    @Override
    public List<AdminWorkOrderCase> selectUnHandleCaseTaskByTime(AdminWorkOrderConst.Type type, AdminWorkOrderConst.Task task,
                                                                 Timestamp startTime, Timestamp endTime) {
        return adminWorkOrderCaseDao.selectUnHandleCaseTaskByTime(type.getCode(), task.getCode(), startTime, endTime);
    }


    @Override
    public List<AdminWorkOrderCase> assigningCaseApproveTask(AdminWorkOrderConst.Type type,
                                                             AdminWorkOrderConst.Task task,
                                                             AdminWorkOrderConst.TaskType taskType,
                                                             int userId, int count,int channel) {
        String lockName = "admin-work-order-case-assigning-case-approve-task-lock";
        String identifier = "";
        try {
            identifier = cfRedissonHandler.tryLock(lockName, 5 * 1000, 60 * 1000);
            if (StringUtils.isNotBlank(identifier)) {
                List<AdminWorkOrderCase> adminWorkOrderCases = this.selectUnHandleCaseTaskByCount(type, task, taskType, count,channel);
                List<Long> workCaseIdList = adminWorkOrderCases.stream().map(AdminWorkOrderCase::getId).collect(Collectors.toList());
                //将对应的工单映射表更新为处理中
                this.updateStatusByIdList(AdminWorkOrderCaseConst.Status.PROCESSING, workCaseIdList);
                List<Long> workOrderIds = adminWorkOrderCases.stream().map(AdminWorkOrderCase::getWorkOrderId).collect(Collectors.toList());
                //将对应的工单表更新为处理中
                adminWorkOrderBiz.updateWithOperatorIds(workOrderIds, userId, AdminWorkOrderConst.Status.HANDLING.getCode());
                List<AdminWorkOrder> adminWorkOrders = adminWorkOrderBiz.selectByIds(workOrderIds);
                Map<Long, Integer> collect = adminWorkOrders.stream().collect(Collectors.toMap(AdminWorkOrder::getId, AdminWorkOrder::getOperatorId));
                //插入工单日志
                List<AdminWorkOrderRecord> adminWorkOrderRecordList = adminWorkOrders.stream().map(AdminWorkOrderRecord::new).collect(Collectors.toList());
                this.adminWorkOrderRecordBiz.insertList(adminWorkOrderRecordList);
                List<AdminWorkOrderCaseRecord> recordList = adminWorkOrderCases.stream().map(val -> {
                    val.setStatus(AdminWorkOrderCaseConst.Status.PROCESSING.getCode());
                    return new AdminWorkOrderCaseRecord(val, collect.get(val.getWorkOrderId()));
                }).collect(Collectors.toList());
                //插入工单案例映射日志
                adminWorkOrderCaseRecordBiz.insertList(recordList);
                return adminWorkOrderCases;
            } else {
                throw new ServiceRuntimeException(WorkOrderErrorEnum.ASSIGNING_TASK_FAILED);
            }
        } catch (Exception e) {
            log.error("AdminWorkOrderCaseBizImpl assigningCaseApproveTask error", e);
            return Collections.emptyList();
        } finally {
            if (StringUtils.isNotBlank(identifier)){
                cfRedissonHandler.unLock(lockName, identifier);
            }
        }
    }

    //按照任务了类型和数量分配任务
    public List<AdminWorkOrderCase> selectUnHandleCaseTaskByCount(AdminWorkOrderConst.Type type,
                                                                  AdminWorkOrderConst.Task task,
                                                                  AdminWorkOrderConst.TaskType taskType,
                                                                  int count,int channel) {
        return adminWorkOrderCaseDao.selectUnHandleCaseTaskByCount(type.getCode(), task.getCode(), taskType.getCode(), count,channel);
    }



    @Override
    public int updateStatusById(long id, AdminWorkOrderCaseConst.Status status) {
        return adminWorkOrderCaseDao.updateStatusById(id, status.getCode());
    }

    @Override
    public int update(AdminWorkOrderCase adminWorkOrderCase) {
        //更新案例
        return adminWorkOrderCaseDao.update(adminWorkOrderCase);
    }

    @Override
    public int updateCallStatusById(long id, CfCallOutConditionTypeEnum cfCallOutConditionTypeEnum) {
        return adminWorkOrderCaseDao.updateCallStatusById(id, cfCallOutConditionTypeEnum.getValue());
    }

    @Override
    public void completeTask(AdminWorkOrderCase adminWorkOrderCase, String comment) {
        if (adminWorkOrderCase == null) {
            return;
        }
        adminWorkOrderBiz.updateChangeable(adminWorkOrderCase.getWorkOrderId(),
                AdminWorkOrderConst.Status.FINISHED, AdminWorkOrderConst.Result.HANDLE_SUCCESS, comment);
    }

    @Override
    public int updateStatusByIdList(AdminWorkOrderCaseConst.Status status, List<Long> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        return adminWorkOrderCaseDao.updateStatusByIdList(status.getCode(), list);
    }



    @Override
    public int updateOrderCaseStatus(int status, int workOrderId) {
        return adminWorkOrderCaseDao.updateOrderCaseStatus(status,workOrderId);
    }

    @Override
    public List<AdminWorkOrderCase> selectByCaseIdList(List<Long> workOrderIds) {
        if (CollectionUtils.isEmpty(workOrderIds)) {
            return Lists.newArrayList();
        }
        return adminWorkOrderCaseDao.selectByCaseIdList(workOrderIds);
    }




    @Diff(diffMethod = "selectPageFromEs", diffCompare = "com.shuidihuzhu.cf.diff.SelectPageCompare")
    @Override
    public List<AdminWorkOrderCase> selectPage(int operatorId, int channel, int current, int pageSize) {

        PageHelper.startPage(current, pageSize);
        return adminWorkOrderCaseDao.selectPage(operatorId,channel,AdminWorkOrderCaseConst.Status.PROCESSING.getCode(),
                AdminWorkOrderCaseConst.CaseType.CASE_APPROVE.getCode());
    }

    @Override
    public Pair<Long, List<AdminWorkOrderCase>> selectPageFromEs(int operatorId, int channel, int current, int pageSize) {
        CfWorkOrderIndexSearchParam searchParam = new CfWorkOrderIndexSearchParam();
        searchParam.setAwoOrderTypes(Lists.newArrayList(AdminWorkOrderConst.Type.CASE_APPROVE.getCode()));

        if (operatorId > 0) {
            searchParam.setAwoOperatorIds(Lists.newArrayList((long)operatorId));
        }

        if (channel == 1) {
            searchParam.setCfChannels(Lists.newArrayList("cf_volunteer"));
        } else if (channel == 2) {
            searchParam.setCfChannelsMustNot(Lists.newArrayList("cf_volunteer"));
        }

        searchParam.setAwocStatuses(Lists.newArrayList(AdminWorkOrderCaseConst.Status.PROCESSING.getCode()));
        searchParam.setAwocTypes(Lists.newArrayList(AdminWorkOrderCaseConst.CaseType.CASE_APPROVE.getCode()));

        searchParam.setSortEnum(CfWorkOrderIndexSortEnum.AWOC_ID_ASC);
        searchParam.setFrom((current - 1) * pageSize);
        searchParam.setSize(pageSize);

        Pair<Long, List<AdminWorkOrder>> pair = cfSearch.cfWorkOrderIndexSearch(searchParam);
        if (CollectionUtils.isEmpty(pair.getRight())) {
            return Pair.of(0L, Lists.newArrayList());
        }

        List<Long> workOrderIds = pair.getRight().stream().map(x -> x.getId()).collect(Collectors.toList());
        List<AdminWorkOrderCase> cases = selectByCaseIdList(workOrderIds);
        return Pair.of(pair.getLeft(), cases);
    }


    @Override
    @Deprecated
    public Response updateWorkOrderCaseApprove(int caseId, String infoUuid, int userId, String comment,
                                               CrowdfundingOperationEnum crowdfundingOperationEnum) {
        log.info("CfDealWithStatusController updateWorkOrderCaseApprove infoUuid:{}, userId:{}, operation:{}",
                infoUuid, userId, crowdfundingOperationEnum);
        //首先查询案例对应的材料审核工单
        List<AdminWorkOrderCase> adminWorkOrderCases = getUnHandleAdminWorkOrderCases(caseId);
        log.info("updateWorkOrderCaseApprove infoUuid:{}, userId:{}, size{}, list{}",
                infoUuid, userId, CollectionUtils.size(adminWorkOrderCases), JSON.toJSONString(adminWorkOrderCases));
        //兼容未进入工单的案例
        if (org.springframework.util.CollectionUtils.isEmpty(adminWorkOrderCases) || adminWorkOrderCases.size() > 1) {
            log.info("updateWorkOrderCaseApprove Error! infoUuid:{}, errorParam:{}", infoUuid, JSON.toJSON(adminWorkOrderCases));
            return null;
        }
        long workOrderId = adminWorkOrderCases.get(0).getWorkOrderId();
        log.info("updateWorkOrderCaseApprove a workOrderId:{}", workOrderId);

        AdminWorkOrder adminWorkOrder = adminWorkOrderBiz.selectById(workOrderId);
        //更新工单状态
        adminWorkOrderBiz.updateChangeable(adminWorkOrder.getId(), AdminWorkOrderConst.Status.FINISHED,
                AdminWorkOrderConst.Result.HANDLE_SUCCESS, comment);
        //更新工单任务对应的映射表任务状态
        AdminWorkOrderCase adminWorkOrderCase1 = this.selectById(adminWorkOrderCases.get(0).getId());
        adminWorkOrderCase1.setStatus(AdminWorkOrderCaseConst.Status.COMPLETE.getCode());
        adminWorkOrderCase1.setApproveResult(changToResult(crowdfundingOperationEnum));
        int update = this.update(adminWorkOrderCase1);
        //添加工单任务日志
        log.info("updateWorkOrderCaseApprove c update res:{}", update);

        List<CrowdfundingOperationEnum> operationEnums = Lists.newArrayList(CrowdfundingOperationEnum.DEFER_APPROVE, CrowdfundingOperationEnum.NEVER_PROCESSING, CrowdfundingOperationEnum.DEFER_CONTACT);
        if(operationEnums.contains(crowdfundingOperationEnum)){
            UserCommentSourceEnum.CommentType commentType = UserCommentSourceEnum.CommentType.getCommetType(crowdfundingOperationEnum);
            UserComment userComment = new UserComment(UserCommentSourceEnum.CASE_APPROVE, workOrderId, commentType, userId, commentType.getDesc(), "", "材料审核工单处理时间");
            userCommentBiz.insert(userComment);
        }

        AdminWorkOrderCaseRecord record = new AdminWorkOrderCaseRecord(adminWorkOrderCase1, adminWorkOrder.getOperatorId());
        adminWorkOrderCaseRecordBiz.insertOne(record);
        log.info("updateWorkOrderCaseApprove done");
        return null;
    }

    /**
     * 获取 待领取 和处理中的材料审核工单
     * @param caseId
     * @return
     */
    @NotNull
    private List<AdminWorkOrderCase> getUnHandleAdminWorkOrderCases(int caseId) {
        List<AdminWorkOrderCase> adminWorkOrderCases = this.selectByCaseIdAndTypeAndStatusAndTime(
                caseId, AdminWorkOrderCaseConst.CaseType.CASE_APPROVE, null,
                null, null);
        if (CollectionUtils.isEmpty(adminWorkOrderCases)) {
            return adminWorkOrderCases;
        }
        adminWorkOrderCases = adminWorkOrderCases.stream()
                .filter(AdminWorkOrderCaseBizImpl::unHandle)
        .collect(Collectors.toList());
        return adminWorkOrderCases;
    }

    @Override
    public void onCaseEnd(long caseId) {

        // 获取对应案例首次沟通工单
        AdminWorkOrderCase adminWorkOrderCase = selectByCaseId((int) caseId);
        if (adminWorkOrderCase == null) {
            log.info("该案例没有首次沟通工单 {}", caseId);
            return;
        }
        long workOrderId = adminWorkOrderCase.getWorkOrderId();
        AdminWorkOrder adminWorkOrder = adminWorkOrderBiz.selectById(workOrderId);

        if (adminWorkOrder == null) {
            log.info("案例结束自动结束首次沟通工单 无工单 caseId={}", caseId);
            return;
        }
        if (adminWorkOrder.getOrderStatus() != AdminWorkOrderConst.Result.INIT.getCode()
                && adminWorkOrder.getOrderStatus() != AdminWorkOrderConst.Result.HANDLING.getCode()
        ) {
            log.info("案例结束自动结束首次沟通工单 已处理 caseId={}", caseId);
            return;
        }
        adminWorkOrderCase.setStatus(AdminWorkOrderCaseConst.Status.SYSTEM_COMPLETE.getCode());
        adminWorkOrderCase.setApproveResult(AdminWorkOrderCaseConst.ApproveResult.NEVER_PROCESSING.getCode() + "");
        update(adminWorkOrderCase);

        AdminWorkOrderCaseRecord caseRecord = new AdminWorkOrderCaseRecord(adminWorkOrderCase,
                AdminUserIDConstants.SYSTEM);
        adminWorkOrderCaseRecordBiz.insertOne(caseRecord);
        adminWorkOrderBiz.updateChangeable(
                workOrderId,
                AdminWorkOrderConst.Status.SHUTDOWN,
                AdminWorkOrderConst.Result.SYSTEM_COMPLETE,
                AdminWorkOrderConst.Result.SYSTEM_COMPLETE.getWord()
        );
        log.info("案例结束自动结束首次沟通工单 自动结束 caseId={}, workOrderId={}, order={}",
                caseId,
                workOrderId,
                JSON.toJSONString(adminWorkOrder));
    }

    private String changToResult(CrowdfundingOperationEnum crowdfundingOperationEnum) {
        switch (crowdfundingOperationEnum){
            // 延后审核
            case DEFER_APPROVE:
                return AdminWorkOrderCaseConst.ApproveResult.DEFER_APPROVE.getCode()+"";
            //延后电话联系
            case DEFER_CONTACT:
                return AdminWorkOrderCaseConst.ApproveResult.DEFER_CONTACT.getCode()+"";
            //不再处理
            case NEVER_PROCESSING:
                return AdminWorkOrderCaseConst.ApproveResult.NEVER_PROCESSING.getCode()+"";
            default:
                return "";
        }
    }


}
