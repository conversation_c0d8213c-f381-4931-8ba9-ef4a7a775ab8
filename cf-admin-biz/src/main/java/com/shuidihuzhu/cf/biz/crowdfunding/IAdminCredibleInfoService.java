package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfCredibleInfoDO;
import com.shuidihuzhu.cf.model.crowdfunding.CfCredibleInfoWorkOrderDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2020/1/10 下午2:37
 * @desc
 */
public interface IAdminCredibleInfoService {
    int insert(CfCredibleInfoDO credibleInfoDO);
    int insertOne(int caseId, long userId, long subId, int type, int auditStatus, long operatorId);
    List<CfCredibleInfoDO> queryByCaseId(int caseId);
    CfCredibleInfoDO queryBySubId(long id, int type);
    CfCredibleInfoDO queryById(long id);
    int updateAuditInfo(long subId, int auditStatus,int type);
    int updateSubmitInfo(long subId, int auditStatus,int type);
    int delete(long subId,int type);

    int updateAuditStatusById(long id,int auditStatus);

    CfCredibleInfoDO getLastOneByCaseId(int caseId, int type);

    List<CfCredibleInfoDO> getListByOperatorId(long operatorId, int type);

    List<CfCredibleInfoWorkOrderDO> getByCaseId(int caseId);

}
