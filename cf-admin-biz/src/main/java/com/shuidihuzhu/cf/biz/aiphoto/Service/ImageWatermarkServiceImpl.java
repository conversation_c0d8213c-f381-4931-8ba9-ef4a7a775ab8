package com.shuidihuzhu.cf.biz.aiphoto.Service;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.aiphoto.ImageWatermarkService;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingAttachmentBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.material.model.CfBasicLivingGuardModel;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.model.admin.UgcWorkOrderVO;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCfFundUseAuditInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CfAttachmentVo;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.QueryListResultVo;
import com.shuidihuzhu.cf.model.report.AdminCfReportAddTrustVo;
import com.shuidihuzhu.cf.vo.CfFirsApproveMaterialVO;
import com.shuidihuzhu.cf.vo.admin.CfFirstApproveExt;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditCaseDetail;
import com.shuidihuzhu.cf.vo.crowdfunding.CfProgressVo;
import com.shuidihuzhu.cf.vo.crowdfunding.WorkOrderUgcBaseInfoVo;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderTypeFeignClient;
import com.shuidihuzhu.client.cf.workorder.model.QueryListParam;
import com.shuidihuzhu.client.cf.workorder.model.enums.ClassifyTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OneTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@RefreshScope
@Service
public class ImageWatermarkServiceImpl implements ImageWatermarkService {

    @Autowired
    private AdminCrowdfundingAttachmentBiz attachmentBiz;
    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private CfWorkOrderTypeFeignClient cfWorkOrderTypeFeignClient;

    private List<Integer> PROGRESS_ORDER_TYPE = Lists.newArrayList();

    @PostConstruct
    public void init() {
        PROGRESS_ORDER_TYPE = Optional.ofNullable(cfWorkOrderTypeFeignClient.getByOneLevel(OneTypeEnum.progress.getType()))
                .filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList());
    }

    @Override
    public void fillBasicLivingWaterMark(int caseId, InitialAuditCaseDetail.CfBasicLivingGuardView guardView) {

        if (guardView == null) {
            return;
        }
        Map<String, Integer> waterMappings = attachmentBiz.getImagesTWatermark(caseId,
                Lists.newArrayList(AttachmentTypeEnum.ATTACH_ALLOWANCE_IMG, AttachmentTypeEnum.ATTACH_POVERTY_IMG));

        if (StringUtils.isNotBlank(guardView.getAllowanceImg())) {
            guardView.setAllowanceImgWatermark(getImageWatermark(waterMappings, guardView.getAllowanceImg()));
        }

        if (StringUtils.isNotBlank(guardView.getPovertyImg())) {
            guardView.setPovertyImgWatermark(getImageWatermark(waterMappings, guardView.getPovertyImg()));
        }
    }

    @Override
    public void fillFirstMedicalWaterMark(int caseId, InitialAuditCaseDetail.FirstApproveCaseInfo approveCaseInfo) {

        if (approveCaseInfo == null || StringUtils.isBlank(approveCaseInfo.getImageUrl())) {
            return;
        }
        Map<String, Integer> waterMappings = attachmentBiz.getImagesTWatermark(caseId,
                Lists.newArrayList(AttachmentTypeEnum.ATTACH_FIRST_APPROVE_MEDICAL));
        approveCaseInfo.setImageUrlWatermark(getImageWatermark(waterMappings, approveCaseInfo.getImageUrl()));
    }

    @Override
    public void fillCaseBaseWaterMark(int caseId, InitialAuditCaseDetail.CaseBaseInfo caseBaseInfo) {

        if (caseBaseInfo == null || CollectionUtils.isEmpty(caseBaseInfo.getAttachments())) {
            return;
        }
        Map<String, Integer> waterMappings = attachmentBiz.getImagesTWatermark(caseId,
                Lists.newArrayList(AttachmentTypeEnum.ATTACH_CF, AttachmentTypeEnum.ATTACH_USER_STAGE_CF));

        for (CrowdfundingAttachmentVo attachmentVo : caseBaseInfo.getAttachments()) {
            if (attachmentVo == null || StringUtils.isBlank(attachmentVo.getUrl())) {
                continue;
            }
            attachmentVo.setWatermark(getImageWatermark(waterMappings, attachmentVo.getUrl()));

        }
    }

    // 0 表示未知
    @Override
    public int getImageWatermark(Map<String, Integer> watermarks, String image) {
        if (MapUtils.isEmpty(watermarks)) {
            return 0;
        }
        Integer mark = watermarks.get(image);
        if (mark != null) {
            return mark;
        }
        String path = getImagePath(image);
        if (StringUtils.isBlank(path)) {
            return 0;
        }

        for (Map.Entry<String, Integer> watermark : watermarks.entrySet()) {
            if (isPathEqual(watermark.getKey(), path)) {
                return watermark.getValue();
            }
        }

        return 0;
    }

    private boolean isPathEqual(String source, String path) {
        try {
            return Objects.equals(new URL(source).getPath(), path);
        } catch (Throwable e) {
            log.warn("获取图片的path失败。source:{} path:{}", source, path, e);
            return false;
        }
    }

    private String getImagePath(String image) {
        if (StringUtils.isBlank(image)) {
            return "";
        }

        try {
            return new URL(image).getPath();
        } catch (Throwable e) {
            log.warn("获取图片的path失败. image:{}", image, e);
            return "";
        }
    }

    @Override
    public void fillUgcBaseContentWatermark(int caseId, WorkOrderUgcBaseInfoVo ugcBaseVo) {

        if (ugcBaseVo == null || StringUtils.isBlank(ugcBaseVo.getAttachmentUrls())) {
            return;
        }

        ugcBaseVo.setWatermarks(getImageWatermarkList(caseId, ugcBaseVo.getAttachmentUrls(),
                Lists.newArrayList(AttachmentTypeEnum.ATTACH_CF, AttachmentTypeEnum.ATTACH_USER_STAGE_CF)));
    }

    @Override
    public void fillUgcProgressWatermark(int caseId, UgcWorkOrderVO progressVo) {
        if (progressVo == null || StringUtils.isBlank(progressVo.getAttachmentUrls())) {
            return ;
        }

        progressVo.setWatermarks(getImageWatermarkList(caseId, progressVo.getAttachmentUrls(),
                Lists.newArrayList(AttachmentTypeEnum.ATTACH_PROGRESS)));
    }

    private List<Integer> getImageWatermarkList(int caseId, String images, List<AttachmentTypeEnum> imageTypes) {
        List<Integer> watermarks = Lists.newArrayList();
        if (StringUtils.isBlank(images) || CollectionUtils.isEmpty(imageTypes)) {
            return watermarks;
        }

        return getImageWatermarkList(images, attachmentBiz.getImagesTWatermark(caseId, imageTypes));
    }

    private List<Integer> getImageWatermarkList(String images,  Map<String, Integer> waterMappings) {
        List<Integer> watermarks = Lists.newArrayList();
        if (StringUtils.isBlank(images)) {
            return watermarks;
        }

        List<String> imageUrls = Splitter.on(CfRefuseReasonEntity.CHINESE_COMMA).splitToList(images);
        for (String image : imageUrls) {
            watermarks.add(getImageWatermark(waterMappings, image));
        }

        return watermarks;
    }

    @Override
    public void fillUgcManageWatermark(List<Integer> ids, List<CfAttachmentVo> attachmentVos) {
        if (CollectionUtils.isEmpty(ids) || CollectionUtils.isEmpty(attachmentVos)) {
            return;
        }

        Map<Integer, Integer> watermarks = attachmentBiz.getImageWatermarkByIds(ids);

        for (CfAttachmentVo vo : attachmentVos) {
            vo.setWatermark(Objects.requireNonNullElse(watermarks.get(vo.getId()), 0));
        }
    }

    @Override
    public void fillFundUseAuditWatermark(List<AdminCfFundUseAuditInfo> auditInfoList) {
        if (CollectionUtils.isEmpty(auditInfoList)) {
            return;
        }

        List<Integer> caseIds = Lists.newArrayList();
        for (AdminCfFundUseAuditInfo auditInfo : auditInfoList) {
            if (auditInfo != null && StringUtils.isNotBlank(auditInfo.getFundUseImageMaterial())) {
                caseIds.add(auditInfo.getCrowdfundingId());
            }
        }

        if (CollectionUtils.isEmpty(caseIds)) {
            return;
        }

        Map<String, Integer> watermarkMapping = attachmentBiz.getImageUrlWatermarkByAttachments(attachmentBiz
                .getListByInfoIdListAndType(caseIds, AttachmentTypeEnum.ATTACH_FUND_USE));
        for (AdminCfFundUseAuditInfo auditInfo : auditInfoList) {
            auditInfo.setWatermarks(getImageWatermarkList(auditInfo.getFundUseImageMaterial(), watermarkMapping));
        }
    }

    @Override
    public void fillAddTrustWatermark(AdminCfReportAddTrustVo trustVo) {
        if (trustVo == null || StringUtils.isBlank(trustVo.getImageUrls()) || StringUtils.isBlank(trustVo.getInfoUuid())) {
            return ;
        }
        CrowdfundingInfo caesInfo = crowdfundingInfoBiz.getFundingInfo(trustVo.getInfoUuid());
        if (caesInfo != null) {
            trustVo.setWatermarks(getImageWatermarkList(caesInfo.getId(), trustVo.getImageUrls(),
                    Lists.newArrayList(AttachmentTypeEnum.ATTACH_REPORT_TRUST)));
        }
    }

    @Override
    public void fillProgressWatermarks(List<CfProgressVo> progressVos) {

        if (CollectionUtils.isEmpty(progressVos)) {
            return;
        }

        List<Integer> caseIds = progressVos.stream().map(CfProgressVo::getActivityId).collect(Collectors.toList());
        Map<String, Integer> urlTWatermarks = attachmentBiz.getImagesTWatermark(caseIds, Lists.newArrayList(AttachmentTypeEnum.ATTACH_PROGRESS));

        for (CfProgressVo vo : progressVos) {
            vo.setWatermarks(getImageWatermarkList(vo.getImageUrls(), urlTWatermarks));
        }
    }

    @Override
    public void fillOrderListProgressWatermarks(List<QueryListResultVo> resultList) {

        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        List<Integer> caseIds = resultList.stream()
                .filter(item->PROGRESS_ORDER_TYPE.contains(item.getOrderType()))
                .map(QueryListResultVo::getCaseId).collect(Collectors.toList());


        Map<String, Integer> watermarkMapping = attachmentBiz.getImagesTWatermark(caseIds,
                Lists.newArrayList(AttachmentTypeEnum.ATTACH_PROGRESS));

        for (QueryListResultVo vo : resultList) {
            if (PROGRESS_ORDER_TYPE.contains(vo.getOrderType())) {
                vo.setWatermarks(getImageWatermarkList(vo.getAttachmentUrls(), watermarkMapping));
            }
        }
    }

    @Override
    public InitialAuditCaseDetail.CfBasicLivingGuardView getLivingGuardView(int caseId, CfBasicLivingGuardModel model) {
        if (model == null) {
            return null;
        }
        InitialAuditCaseDetail.CfBasicLivingGuardView livingGuardView = new InitialAuditCaseDetail.CfBasicLivingGuardView(model);
        fillBasicLivingWaterMark(caseId, livingGuardView);

        return livingGuardView;
    }

    @Override
    public CfFirstApproveExt getFirstApproveExt(int caseId, CfFirsApproveMaterialVO vo) {
        if (vo == null) {
            return null;
        }

        CfFirstApproveExt ext = new CfFirstApproveExt(vo);

        if (StringUtils.isNotBlank(ext.getPreAuditImageUrl())) {
            Map<String, Integer> waterMappings = attachmentBiz.getImagesTWatermark(caseId,
                    Lists.newArrayList(AttachmentTypeEnum.ATTACH_FIRST_APPROVE_MEDICAL));
            ext.setAuditImageUrlWatermark(getImageWatermark(waterMappings, vo.getPreAuditImageUrl()));
        }
        return ext;
    }


}
