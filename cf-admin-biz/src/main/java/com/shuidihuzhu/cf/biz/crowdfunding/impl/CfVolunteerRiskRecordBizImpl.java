package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.CfVolunteerRiskRecordBiz;
import com.shuidihuzhu.cf.client.adminpure.model.initial.CfVolunteerRiskRecord;
import com.shuidihuzhu.cf.dao.crowdfunding.CfVolunteerRiskRecordDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2022/1/11 10:45
 * @Description:
 */
@Service
public class CfVolunteerRiskRecordBizImpl implements CfVolunteerRiskRecordBiz {

    @Resource
    private CfVolunteerRiskRecordDao cfVolunteerRiskRecordDao;

    @Override
    public int insert(CfVolunteerRiskRecord cfVolunteerRiskRecord) {
        return cfVolunteerRiskRecordDao.insert(cfVolunteerRiskRecord);
    }

    @Override
    public List<CfVolunteerRiskRecord> getByCaseIdList(List<Integer> caseIdList) {
        return cfVolunteerRiskRecordDao.getByCaseIdList(caseIdList);
    }

    @Override
    public List<CfVolunteerRiskRecord> getByCaseId(int caseId) {
        return cfVolunteerRiskRecordDao.getByCaseId(caseId);
    }
}
