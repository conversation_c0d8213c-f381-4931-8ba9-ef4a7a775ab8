package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification;
import com.shuidihuzhu.common.web.util.admin.BasicExample;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/10/18.
 */
public interface AdminCrowdFundingVerificationBiz {

    List<CrowdFundingVerification> selectByPage(BasicExample basicExample, int current, int pageSize);

    CrowdFundingVerification getById(long id);

    /**
     * 通过案例id查出证实人数
     * @param crowdfundingInfoIds
     * @return
     */
    Map<String,Integer> getVerify(List<String> crowdfundingInfoIds);

    /**
     * 查询证实用户大于指定时间的证实id，限制证实条数
     * @param userId
     * @param gtDate
     * @param amount
     * @return
     */
    boolean isVerifySomeTimes(long userId, Date gtDate, int amount);

    List<CrowdFundingVerification> getListByIds(List<Integer> ids);

    int updateValid(int valid, int id);

}
