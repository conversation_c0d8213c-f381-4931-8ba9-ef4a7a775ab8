package com.shuidihuzhu.cf.biz.mask;

import com.shuidihuzhu.cf.enums.maskcode.MaskTypeEnum;
import com.shuidihuzhu.cf.model.admin.AdminMaskParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * @Description: 掩码查看服务
 * @Author: panghairui
 * @Date: 2022/7/5 8:01 下午
 */
@Slf4j
@Service
public class CfAdminMaskService {

    @Resource
    private AdminViewMaskBiz adminViewMaskBiz;

    /**
     * 掩码查看分派策略
     */
    private final static Map<Integer, Function<AdminMaskParam, String>> maskStrategy = new HashMap<>();

    /**
     * 初始化掩码策略
     */
    @PostConstruct
    public void initMaskStrategy() {
        maskStrategy.put(MaskTypeEnum.VIEW_RAISE_MOBILE.getCode(), param -> adminViewMaskBiz.viewRaiseMaskMobile(param));
        maskStrategy.put(MaskTypeEnum.VIEW_RAISE_ID_CARD.getCode(), param -> adminViewMaskBiz.viewRaiseMaskIdCard(param));
        maskStrategy.put(MaskTypeEnum.VIEW_PATIENT_ID_CARD.getCode(), param -> adminViewMaskBiz.viewPatientMaskIdCardV2(param));
        maskStrategy.put(MaskTypeEnum.VIEW_PAYEE_ID_CARD.getCode(), param -> adminViewMaskBiz.viewPayeeMaskIdCard(param));
        maskStrategy.put(MaskTypeEnum.VIEW_PAYEE_BANK_CARD.getCode(), param -> adminViewMaskBiz.viewPayeeMaskBankCard(param));
        maskStrategy.put(MaskTypeEnum.VIEW_CONFIRM_ID_CARD.getCode(), param -> adminViewMaskBiz.viewConfirmMaskIdCard(param));
        maskStrategy.put(MaskTypeEnum.VIEW_CONTRARY_PAYEE.getCode(), param -> adminViewMaskBiz.viewContraryBankCard(param));
    }

    public List<AdminMaskParam> viewMaskInfo(List<AdminMaskParam> adminMaskParam) {

        adminMaskParam.forEach(data -> {
            // 根据传进来的枚举，走特定的方法，查看掩码信息
            Function<AdminMaskParam, String> result = maskStrategy.get(data.getMaskCodeType());
            if (result != null) {
                // 执行方法
                String resultStr = result.apply(data);
                data.setResult(resultStr);
            }
        });

        return adminMaskParam;

    }

}
