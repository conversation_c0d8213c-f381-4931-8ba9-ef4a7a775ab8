package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.CfSensitiveWordRecordEnum;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.deliver.CrowdFundingVerificationDeliver;
import com.shuidihuzhu.cf.model.crowdfunding.deliver.CrowdfundingCommentDeliver;
import com.shuidihuzhu.cf.model.crowdfunding.deliver.CrowdfundingOrderDeliver;

import java.sql.Timestamp;
import java.util.List;
import java.util.Set;

public interface CfSensitiveWordRecordBiz {

    List<CfSensitiveWordRecord> selectByUserId(Set<Long> set);

    List<CfSensitiveWordRecord> selectByIds(List<Long> list);

    CfSensitiveWordRecord selectById(long id);

    List<Long> selectIdByTime(Timestamp beginTime, Timestamp endTime, int start, int size);

    void buildCommentOne(CrowdfundingCommentDeliver comment);

    void buildVerificationOne(CrowdFundingVerificationDeliver verification, boolean fix);

    void buildProgressOne(CrowdFundingProgress progress);

    void buildOneOrder(CrowdfundingOrderDeliver order);

    Long selectByBizIdAndBizType(long bizId, int bizType);


    List<CfSensitiveWordRecord> listByBizIdAndBizType(long bizId, CfSensitiveWordRecordEnum.BizType bizType);
}
