package com.shuidihuzhu.cf.biz.crowdfunding.workflow;

import com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowFreeRecord;

import java.util.Date;
import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-02-19 17:12
 **/
public interface WorkFlowFreeRecordBiz {

    void batchAdd(List<WorkFlowFreeRecord> records);

    int count(List<Long> userIds, Integer orgId, Date startTime, Date endTime);
}
