package com.shuidihuzhu.cf.biz.admin.exception;


import com.shuidihuzhu.cf.enums.admin.interfaces.ICommonError;

/**
 * @Author: wuxinlong
 * @Since: 2016-11-09
 */
public class ServiceRuntimeException extends RuntimeException {
	
	private static final long serialVersionUID = 1L;
	
	private ICommonError errorEnum;

	public ServiceRuntimeException(ICommonError errorEnum) {
		this.errorEnum = errorEnum;
	}

	public ICommonError getErrorEnum() {
		return this.errorEnum;
	}
}
