package com.shuidihuzhu.cf.biz.risk.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.risk.IUserBehaviorService;
import com.shuidihuzhu.cf.enums.crowdfunding.ClewtrackPrimaryChannelEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.UserBehaviorEnum;
import com.shuidihuzhu.cf.model.crowdfunding.AdminUserBehaviorDetail;
import com.shuidihuzhu.cf.model.crowdfunding.UserInfoDetail;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewBaseInfoDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2019/10/12 下午5:55
 * @desc 登记线索
 */
@Service
public class UserBehaviorRegisterClueServiceImpl implements IUserBehaviorService {

    @Autowired
    private CfClewtrackFeignClient cfClewtrackFeignClient;

    @Override
    public UserBehaviorEnum getBehavior() {
        return UserBehaviorEnum.REGISTER_CLUE;
    }

    @Override
    public boolean allowNoUserId() {
        return true;
    }

    @Override
    public List<AdminUserBehaviorDetail> buildBehaviorDetail(String mobile, long userId, String name, String identity, UserInfoModel userInfo, UserBehaviorEnum behaviorEnum) {
        if (StringUtils.isBlank(mobile)) {
            return Lists.newArrayList();
        }
        List<CfClewBaseInfoDO> cfClewBaseInfoDOS = cfClewtrackFeignClient.getClewBaseInfoByMobileLimit(mobile, 1000).getData();
        if(CollectionUtils.isEmpty(cfClewBaseInfoDOS)){
            return Lists.newArrayList();
        }

        UserInfoDetail userInfoDetail = new UserInfoDetail();
        userInfoDetail.setMobile(mobile);

        List<AdminUserBehaviorDetail> clueDetails = Lists.newArrayList();
        for (CfClewBaseInfoDO cfClewBaseInfoDO : cfClewBaseInfoDOS){

            ClewtrackPrimaryChannelEnum primaryChannelEnum = ClewtrackPrimaryChannelEnum.parse(cfClewBaseInfoDO.getPrimaryChannel());

            StringBuilder sb = new StringBuilder();
            sb.append(Objects.nonNull(primaryChannelEnum) ? primaryChannelEnum.getChinese() : "").append(REDEX);

            AdminUserBehaviorDetail clueDetail = new AdminUserBehaviorDetail();
            clueDetail.setTime(cfClewBaseInfoDO.getCreateTime());
            clueDetail.setBehaviorType(UserBehaviorEnum.REGISTER_CLUE.getKey());
            clueDetail.setUserInfoDetail(userInfoDetail);
            clueDetail.setUrl(Lists.newArrayList());
            clueDetail.setBehavoir(Lists.newArrayList(sb.toString().split(REDEX)));

            clueDetails.add(clueDetail);
        }

        return clueDetails;
    }
}
