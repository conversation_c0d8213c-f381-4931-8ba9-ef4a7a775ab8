package com.shuidihuzhu.cf.biz.risk.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.risk.IUserBehaviorService;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.UserBehaviorEnum;
import com.shuidihuzhu.cf.model.crowdfunding.AdminUserBehaviorDetail;
import com.shuidihuzhu.cf.model.crowdfunding.UserInfoDetail;
import com.shuidihuzhu.cipher.ShuidiCipher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2019/10/14 上午11:38
 * @desc 注册行为
 */
@Service
public class UserBehaviorRegisterServiceImpl implements IUserBehaviorService {

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Override
    public UserBehaviorEnum getBehavior() {
        return UserBehaviorEnum.REGISTER;
    }

    @Override
    public List<AdminUserBehaviorDetail> buildBehaviorDetail(String mobile, long userId, String name, String identity, UserInfoModel userInfo, UserBehaviorEnum behaviorEnum) {

        UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByUserId(userId);

        if(Objects.isNull(userInfoModel)){
            return Lists.newArrayList();
        }

        List<AdminUserBehaviorDetail> registerDetails = Lists.newArrayList();


        UserInfoDetail userInfoDetail = new UserInfoDetail();
        String cryptoMobile = userInfoModel.getCryptoMobile();
        String userMobile = shuidiCipher.decrypt(cryptoMobile);
        userInfoDetail.setMobile(userMobile);

        StringBuilder sb = new StringBuilder();
        sb.append("注册水滴筹账号,绑定手机号:").append(userMobile).append(REDEX);

        AdminUserBehaviorDetail registerDetail = new AdminUserBehaviorDetail();
        registerDetail.setTime(new Date(userInfoModel.getCreateTime()));
        registerDetail.setBehaviorType(UserBehaviorEnum.REGISTER.getKey());
        registerDetail.setUserInfoDetail(userInfoDetail);
        registerDetail.setUrl(Lists.newArrayList());
        registerDetail.setBehavoir(Lists.newArrayList(sb.toString().split(REDEX)));

        registerDetails.add(registerDetail);

        return registerDetails;
    }
}
