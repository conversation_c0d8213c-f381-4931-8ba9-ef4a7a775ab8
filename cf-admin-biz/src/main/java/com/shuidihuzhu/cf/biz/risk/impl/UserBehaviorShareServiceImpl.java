package com.shuidihuzhu.cf.biz.risk.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Ordering;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.admin.util.DistinctUtil;
import com.shuidihuzhu.cf.biz.risk.IUserBehaviorService;
import com.shuidihuzhu.cf.client.feign.*;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICfInfoXXXRecordDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.UserBehaviorEnum;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @time 2019/10/12 上午11:06
 * @desc 转发行为
 */
@Service
public class UserBehaviorShareServiceImpl implements IUserBehaviorService {

    @Autowired
    private ICfInfoXXXRecordDelegate cfInfoXXXRecordDelegate;

    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;

    @Autowired
    private CfUserInfoFeignClient cfUserInfoFeignClient;

    @Override
    public UserBehaviorEnum getBehavior() {
        return UserBehaviorEnum.SHARE;
    }

    @Override
    public List<AdminUserBehaviorDetail> buildBehaviorDetail(String mobile, long userId, String name, String identity, UserInfoModel userInfo, UserBehaviorEnum behaviorEnum) {

        List<CfInfoShareRecord> shareRecords = cfInfoXXXRecordDelegate.findCfInfoShareRecord(userId, "", "");
        if(CollectionUtils.isEmpty(shareRecords)){
            return Lists.newArrayList();
        }

        UserInfoDetail userInfoDetail = new UserInfoDetail();
        userInfoDetail.setUserId(userId);
        userInfoDetail.setNickName(Objects.nonNull(userInfo) ? userInfo.getNickname() : "");

        List<AdminUserBehaviorDetail> shareDetails = Lists.newArrayList();

        Ordering<CfInfoShareRecord> ordering = Ordering.natural().onResultOf(CfInfoShareRecord::getId);

        List<CfInfoShareRecord> distinctShardRecords = shareRecords
                .stream()
                .sorted(ordering)
                .filter(DistinctUtil.distinctByKey(CfInfoShareRecord::getInfoId))
                .collect(Collectors.toList());


        for (CfInfoShareRecord shareRecord : distinctShardRecords){

            int caseId = shareRecord.getInfoId();
            CrowdfundingInfo caseInfo = crowdfundingFeignClient.getCaseInfoById(caseId).getData();

            CfUserCaseInfoDO data = cfUserInfoFeignClient.getUserCaseInfo(caseId, userId).getData();

            Optional<CfUserCaseInfoDO> dataOptional = Optional.ofNullable(data);
            StringBuilder sb = new StringBuilder();
            sb.append("案例id:").append(caseId).append(REDEX);
            sb.append("案例标题:").append(Objects.nonNull(caseInfo) ? caseInfo.getTitle() : "").append(REDEX);
            sb.append("带来的帮助次数:").append(dataOptional.map(CfUserCaseInfoDO::getShareContributeDonator).orElse(0)).append(REDEX);
            sb.append("带来的捐款:").append(dataOptional.map(CfUserCaseInfoDO::getShareContributeAmount).orElse(0));

            AdminUserBehaviorDetail shareDetail = new AdminUserBehaviorDetail();
            shareDetail.setTime(shareRecord.getDateCreated());
            shareDetail.setBehaviorType(UserBehaviorEnum.SHARE.getKey());
            shareDetail.setUserInfoDetail(userInfoDetail);
            shareDetail.setUrl(Lists.newArrayList());
            shareDetail.setBehavoir(Lists.newArrayList(sb.toString().split(REDEX)));
            shareDetails.add(shareDetail);
        }


        return shareDetails;
    }



}
