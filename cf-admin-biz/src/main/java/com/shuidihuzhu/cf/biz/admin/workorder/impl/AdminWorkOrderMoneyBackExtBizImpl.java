package com.shuidihuzhu.cf.biz.admin.workorder.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.admin.workorder.AdminWorkOrderMoneyBackExtBiz;
import com.shuidihuzhu.cf.dao.admin.workorder.AdminWorkOrderMoneyBackExtDAO;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrderMoneyBackExt;
import com.shuidihuzhu.cf.model.admin.vo.AdminWorkOrderMoneyBackExtVO;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.common.web.util.MoneyUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 返还款相关信息工单表单业务实现类
 */
@Slf4j
@Service
public class AdminWorkOrderMoneyBackExtBizImpl implements AdminWorkOrderMoneyBackExtBiz {

    @Resource
    private AdminWorkOrderMoneyBackExtDAO adminWorkOrderMoneyBackExtDAO;
    @Resource
    private OldShuidiCipher oldShuidiCipher;

    @Override
    public boolean save(AdminWorkOrderMoneyBackExt record) {
        if (record == null) {
            return false;
        }
        try {
            return adminWorkOrderMoneyBackExtDAO.save(record) > 0;
        } catch (Exception e) {
            log.error("保存返还款相关信息工单表单失败", e);
            return false;
        }
    }

    @Override
    public boolean batchSave(List<AdminWorkOrderMoneyBackExt> records) {
        if (CollectionUtils.isEmpty(records)) {
            return false;
        }
        try {
            return adminWorkOrderMoneyBackExtDAO.batchSave(records) > 0;
        } catch (Exception e) {
            log.error("批量保存返还款相关信息工单表单失败", e);
            return false;
        }
    }

    @Override
    public boolean batchSaveByVOList(List<AdminWorkOrderMoneyBackExtVO> voList, Long workOrderId) {
        if (CollectionUtils.isEmpty(voList) || workOrderId == null || workOrderId <= 0) {
            return false;
        }
        
        List<AdminWorkOrderMoneyBackExt> extList = new ArrayList<>(voList.size());
        for (AdminWorkOrderMoneyBackExtVO vo : voList) {
            AdminWorkOrderMoneyBackExt ext = new AdminWorkOrderMoneyBackExt();
            ext.setCaseId(vo.getCaseId());
            ext.setAmount(Optional.ofNullable(vo.getAmountStr())
                            .filter(StringUtils::isNotBlank)
                    .map(r -> MoneyUtil.changeYuanStrToFen(r, RoundingMode.HALF_UP)).orElse(0L));
            ext.setWorkOrderId(workOrderId);
            ext.setEncryptMobile(Optional.ofNullable(vo.getMobile())
                            .filter(StringUtils::isNotBlank)
                    .map(r -> oldShuidiCipher.aesEncrypt(r)).orElse(""));
            extList.add(ext);
        }
        
        return batchSave(extList);
    }

    @Override
    public List<AdminWorkOrderMoneyBackExt> findByWorkOrderId(Long workOrderId) {
        if (workOrderId == null || workOrderId <= 0) {
            return Lists.newArrayList();
        }
        try {
            return adminWorkOrderMoneyBackExtDAO.findByWorkOrderId(workOrderId);
        } catch (Exception e) {
            log.error("根据工单ID查询返还款相关信息工单表单失败, workOrderId: {}", workOrderId, e);
            return Lists.newArrayList();
        }
    }

    @Override
    public List<AdminWorkOrderMoneyBackExt> findByCaseId(Integer caseId) {
        if (caseId == null || caseId <= 0) {
            return Lists.newArrayList();
        }
        try {
            return adminWorkOrderMoneyBackExtDAO.findByCaseId(caseId);
        } catch (Exception e) {
            log.error("根据案例ID查询返还款相关信息工单表单失败, caseId: {}", caseId, e);
            return Lists.newArrayList();
        }
    }

    @Override
    public AdminWorkOrderMoneyBackExt findByWorkOrderIdAndCaseId(Long workOrderId, Integer caseId) {
        if (workOrderId == null || workOrderId <= 0 || caseId == null || caseId <= 0) {
            return null;
        }
        try {
            return adminWorkOrderMoneyBackExtDAO.findByWorkOrderIdAndCaseId(workOrderId, caseId);
        } catch (Exception e) {
            log.error("根据工单ID和案例ID查询返还款相关信息工单表单失败, workOrderId: {}, caseId: {}", workOrderId, caseId, e);
            return null;
        }
    }
} 