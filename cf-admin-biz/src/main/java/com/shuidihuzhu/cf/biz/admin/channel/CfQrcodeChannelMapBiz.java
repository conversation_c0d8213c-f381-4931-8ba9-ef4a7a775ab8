package com.shuidihuzhu.cf.biz.admin.channel;

import com.shuidihuzhu.cf.model.admin.channel.CfQrcodeChannel;

import java.util.List;
import java.util.Map;

/**
 * Created by wangsf on 17/2/16.
 */
public interface CfQrcodeChannelMapBiz {

	int add(CfQrcodeChannel qrcodeChannel);

	List<CfQrcodeChannel> listByQrcodeIds(List<Integer> ids);

	Map<Integer, CfQrcodeChannel> getMapByIds(List<Integer> ids);

	int update(int qrcodeId, int channelId);

	CfQrcodeChannel getByQrcodeId(int qrcodeId);

}
