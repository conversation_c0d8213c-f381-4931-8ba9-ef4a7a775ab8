package com.shuidihuzhu.cf.biz.risk.impl;

import com.google.common.collect.Maps;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.admin.util.admin.AdminCfIdCardUtil;
import com.shuidihuzhu.cf.biz.risk.ISimpleExportService;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingUserDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cipher.ShuidiCipher;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

/**
 * Created by sven on 2019/6/24.
 *
 * <AUTHOR>
 */
@Service
public class BaseInfoSimpleExportServiceImpl implements ISimpleExportService {

    private final static DateFormat dateFormat = new SimpleDateFormat("yyyy");

    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Resource
    private ICrowdfundingUserDelegate crowdfundingUserDelegate;

    @Resource
    private IRiskDelegate riskDelegate;
    @Autowired
    private ShuidiCipher shuidiCipher;


    private final static String TITLE = "求助标题";
    private final static String CONTENT = "求助文章";
    private final static String RAISER = "发起人姓名";
    private final static String RELATIONSHIP = "发起人与患者关系";
    private final static String AUTHOR_NAME = "患者姓名";
    private final static String RECIVER_NAME = "收款人姓名";
    private final static String RAISER_AGE = "发起人年龄";
    private final static String AUTHOR_AGE = "患者年龄";
    private final static String RECIVER_AGE = "收款人年龄";

    @Override
    public Map<String, String> getDetail(CfInfoSimpleModel cfInfoSimpleModel) {

        CrowdfundingInfo crowdfundingInfo = crowdfundingDelegate.getFundingInfoById(cfInfoSimpleModel.getId());
        CfFirsApproveMaterial cfFirsApproveMaterial = riskDelegate.getCfFirsApproveMaterialByInfoId(cfInfoSimpleModel.getId());
        CrowdfundingAuthor crowdfundingAuthor = crowdfundingUserDelegate.getCrowdfundingAuthor(cfInfoSimpleModel.getId());
        // 获取收款人姓名
        String payeeName = "";
        String payeeAge = "";
        if (crowdfundingInfo.getRelationType() != CrowdfundingRelationType.LOCATION_HOSPITAL_ACCOUNT) {
            payeeName = crowdfundingInfo.getPayeeName();
            if (StringUtils.isNotBlank(crowdfundingInfo.getPayeeIdCard())) {
                payeeAge = AdminCfIdCardUtil.getAge(shuidiCipher.decrypt(crowdfundingInfo.getPayeeIdCard())) + "";
            }
        } else {
            payeeName = "收款人为对公账号";
        }

        Map<String, String> map = Maps.newHashMap();

        map.put(TITLE, crowdfundingInfo.getTitle());
        map.put(CONTENT, crowdfundingInfo.getContent());
        map.put(RAISER, cfFirsApproveMaterial ==null? UNKOWN: cfFirsApproveMaterial.getSelfRealName());
        map.put(AUTHOR_NAME, getAuthorName(cfFirsApproveMaterial, crowdfundingAuthor));
        map.put(RELATIONSHIP, getRelationshipStr(cfFirsApproveMaterial == null ? UserRelTypeEnum.NOTHING.getValue() : cfFirsApproveMaterial.getUserRelationType()));

        map.put(RECIVER_NAME, payeeName);

        map.put(RAISER_AGE, ""+getAgeByEncrpytIdCard(cfFirsApproveMaterial.getSelfCryptoIdcard()));
        map.put(AUTHOR_AGE, ""+getAgeByEncrpytIdCard(getAuthorEncrpytIdCard(cfFirsApproveMaterial, crowdfundingAuthor)));
        map.put(RECIVER_AGE, payeeAge);

        return map;
    }

    @Override
    public String getCategory() {
        return "基本case_info";
    }

    /**
     * 1,以前患者信息为准
     * 2,患者信息没有就以前置信息为准
     * 3,都没有返回UNKOWN
     *
     * @param cfFirsApproveMaterial
     * @param crowdfundingAuthor
     * @return
     */
    private String getAuthorName(CfFirsApproveMaterial cfFirsApproveMaterial, CrowdfundingAuthor crowdfundingAuthor){

        if(crowdfundingAuthor != null){
            return crowdfundingAuthor.getName();
        }

        if(cfFirsApproveMaterial != null){
            return cfFirsApproveMaterial.getPatientRealName();
        }

        return UNKOWN;
    }

    private String getAuthorEncrpytIdCard(CfFirsApproveMaterial cfFirsApproveMaterial, CrowdfundingAuthor crowdfundingAuthor){
        if(crowdfundingAuthor != null && StringUtils.isNotEmpty(crowdfundingAuthor.getCryptoIdCard())){
            return crowdfundingAuthor.getCryptoIdCard();
        }
        if(cfFirsApproveMaterial != null && StringUtils.isNotEmpty(cfFirsApproveMaterial.getPatientCryptoIdcard())){
            return cfFirsApproveMaterial.getPatientCryptoIdcard();
        }
        return UNKOWN;
    }

    private String getRelationshipStr(int code){
        for(UserRelTypeEnum userRelTypeEnum : UserRelTypeEnum.values()){
            if(userRelTypeEnum.getValue() == code){
                return userRelTypeEnum.getMsg();
            }
        }
        throw new IllegalArgumentException(""+code);
    }

    private int getAgeByEncrpytIdCard(String encryptIdCard){

        String idCard = shuidiCipher.decrypt(encryptIdCard);
        if(StringUtils.isEmpty(idCard) || !StringUtils.isNumeric(idCard)){
            return 0;
        }
        String birthYearStr = "";
        if(idCard.length() > 16){
            birthYearStr = idCard.substring(6,6+4);
        } else if(idCard.length() == 15){
            birthYearStr = "19"+idCard.substring(6,6+2);
        } else {
            return 0;
        }

        int currentYear = Integer.valueOf(dateFormat.format(new Date()));
        return currentYear - Integer.valueOf(birthYearStr);
    }

    @Override
    public int getOrder() {
        return 1;
    }
}
