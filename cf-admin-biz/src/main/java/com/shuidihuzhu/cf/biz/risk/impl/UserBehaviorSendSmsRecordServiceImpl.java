package com.shuidihuzhu.cf.biz.risk.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.risk.IUserBehaviorService;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.client.base.utils.ResultUtils;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonActionConst;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonRecord;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonRecordClient;
import com.shuidihuzhu.cf.enums.crowdfunding.UserBehaviorEnum;
import com.shuidihuzhu.cf.model.crowdfunding.AdminUserBehaviorDetail;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.UserInfoDetail;
import com.shuidihuzhu.cf.vo.message.SmsRecord;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class UserBehaviorSendSmsRecordServiceImpl implements IUserBehaviorService {

    @Autowired
    private WonRecordClient wonRecordClient;

    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Override
    public UserBehaviorEnum getBehavior() {
        return UserBehaviorEnum.SEND_SMS_RECORD;
    }

    @Override
    public boolean allowNoUserId() {
        return true;
    }

    @Override
    public List<AdminUserBehaviorDetail> buildBehaviorDetail(String mobile, long userId, String name, String identity,
                                                             UserInfoModel userInfo, UserBehaviorEnum behaviorEnum) {
        if (StringUtils.isBlank(mobile)) {
            return Lists.newArrayList();
        }
        OperationResult<List<WonRecord>> listResult = wonRecordClient.listByBizIdAndActionIds(oldShuidiCipher.aesEncrypt(mobile),
                Lists.newArrayList(WonActionConst.SMS_RECORD));

        if (ResultUtils.isFail(listResult)) {
            return Lists.newArrayList();
        }
        List<WonRecord> listData = listResult.getData();

        List<AdminUserBehaviorDetail> verifyDetails = Lists.newArrayList();

        for (WonRecord wonRecord : listData) {
            SmsRecord smsRecord = wonRecord.getExt("data", SmsRecord.class);

            List<String> lines = Lists.newArrayList();
            int caseId = wonRecord.getCaseId();
            lines.add(buildLine("案例id", caseId > 0 ? caseId : ""));
            lines.add(buildLine("模板名称", StringUtils.trimToEmpty(smsRecord.getModelName())));
            lines.add(buildLine("短信内容", smsRecord.getSmsContent()));
            lines.add(buildLine("操作人", wonRecord.getNameWithOrg()));

            UserInfoDetail userInfoDetail = new UserInfoDetail();
            userInfoDetail.setMobile(shuidiCipher.decrypt(smsRecord.getCryptoMobile()));

            Map<String,Object> extMap = Maps.newHashMap();
            extMap.put("caseId", caseId);
            if (caseId > 0) {
                CrowdfundingInfo fundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
                if (fundingInfo != null) {
                    extMap.put("infoUuid", fundingInfo.getInfoId());
                }
            }

            AdminUserBehaviorDetail verifyDetail = new AdminUserBehaviorDetail();
            verifyDetail.setTime(wonRecord.getActionTime());
            verifyDetail.setBehaviorType(getBehavior().getKey());
            verifyDetail.setUserInfoDetail(userInfoDetail);
            verifyDetail.setUrl(Lists.newArrayList());
            verifyDetail.setBehavoir(lines);
            verifyDetail.setExtInfo(extMap);
            verifyDetails.add(verifyDetail);
        }

        return verifyDetails;
    }

    private String buildLine(String tag, Object content) {
        return tag + "：" + content;
    }
}
