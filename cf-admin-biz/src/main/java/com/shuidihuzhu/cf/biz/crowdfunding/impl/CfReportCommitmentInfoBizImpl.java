package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.CfReportCommitmentInfoBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfReportCommitmentInfoDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportCommitmentInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: lix<PERSON><PERSON><PERSON>
 * @create: 2020-02-12 16:08
 **/
@Service
public class CfReportCommitmentInfoBizImpl implements CfReportCommitmentInfoBiz {
    @Autowired
    private CfReportCommitmentInfoDao cfReportCommitmentInfoDao;

    @Override
    public int insertOne(CfReportCommitmentInfo cfReportCommitmentInfo) {
        return cfReportCommitmentInfoDao.insertOne(cfReportCommitmentInfo);
    }

    @Override
    public CfReportCommitmentInfo findByIncrTrustId(long incrTrustId) {
        return cfReportCommitmentInfoDao.findByIncrTrustId(incrTrustId);
    }
}
