package com.shuidihuzhu.cf.biz.admin.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfInitialAuditCheckInfoRefDAO;
import com.shuidihuzhu.cf.domain.cf.AdminCfInitialAuditCheckInfoRefDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AdminCfInitialAuditCheckInfoRefService {

    @Resource
    private AdminCfInitialAuditCheckInfoRefDAO checkInfoRefDAO;

    public AdminCfInitialAuditCheckInfoRefDO getByHospitalName(String refName) {
        AdminCfInitialAuditCheckInfoRefDO refDO = null;
        try {
            refDO = checkInfoRefDAO.getByRefNameAndType(refName, 1);
        } catch (Exception e) {
            log.error("", e);
        }
        return refDO;
    }

    @Async
    public void batchInsertHospitalName(List<String> refNameList) {
        List<AdminCfInitialAuditCheckInfoRefDO> savedList = Lists.newArrayList();
        refNameList.forEach(s -> {
            AdminCfInitialAuditCheckInfoRefDO refDO = new AdminCfInitialAuditCheckInfoRefDO();
            refDO.setReferenceName(s);
            refDO.setReferenceType(1);
            savedList.add(refDO);
        });
        try {
            checkInfoRefDAO.batchInsert(savedList);
        } catch (Exception e) {
            log.error("", e);
        }
    }
}
