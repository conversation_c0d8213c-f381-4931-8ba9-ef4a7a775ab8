package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.vo.crowdfunding.HandleHospital;

import java.util.List;

/**
 * Created by ahrievil on 2017/5/5.
 */
public interface HandleHospitalBiz {
    int insert(HandleHospital handleHospital);

    int insertInit(List<HandleHospital> handleHospitals);

    List<HandleHospital> selectAll(int offSet, int count);

    int update(HandleHospital handleHospital);

    int selectMax();

    int updateValid(int disable, int id);
}
