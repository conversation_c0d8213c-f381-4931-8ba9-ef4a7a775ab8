package com.shuidihuzhu.cf.biz.admin.channel;

import com.shuidihuzhu.cf.model.admin.channel.CfChannelGroup;

import java.util.List;
import java.util.Map;

/**
 * Created by wangsf on 17/2/16.
 */
public interface CfChannelGroupBiz {

	List<CfChannelGroup> listGroups(int anchorId, int limit);

	List<CfChannelGroup> listGroupsByPage(int page, int size);

	List<CfChannelGroup> listByIds(List<Integer> ids);

	Map<Integer, CfChannelGroup> getMapByIds(List<Integer> ids);

	CfChannelGroup getById(int groupId);

    int insertOne(CfChannelGroup cfChannelGroup);
}
