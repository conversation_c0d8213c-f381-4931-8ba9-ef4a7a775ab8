package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReason;

import java.util.List;

/**
 * Created by ahrievil on 2017/1/17.
 */
public interface CfRefuseReasonBiz {
	int editReason(int id, String content);

	List<CfRefuseReason> getRefuseReasons();

	int editFrequency(int id);

	String findText(int id);

	int insertReason(int id, String reason);

	int emptyFrequency();

	List<CfRefuseReason> selectAll();

	void sendFrequency();

	int selectWithItem(int id);

    int deleteById(int id);

    int insertOne(CfRefuseReason cfRefuseReason);
}
