package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.AdminUgcOperateRecordDO;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/11/4 上午10:12
 * @desc
 */
public interface IUgcOperateRecordService {
    int insert(AdminUgcOperateRecordDO ugcOperateRecordDO);
    int insertUgcRecord(long caseId, int bizType, long bizId, int operateType, int adminUserId);
    List<AdminUgcOperateRecordDO> query(long caseId, int bizType, long bizId);
}
