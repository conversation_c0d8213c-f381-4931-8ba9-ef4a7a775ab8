package com.shuidihuzhu.cf.biz.crowdfunding.workflow;

import com.shuidihuzhu.cf.model.crowdfunding.workflow.*;

import java.util.*;

/**
 * @author: fengxuan
 * @create 2020-02-14 11:49
 **/
public interface StaffStatusBiz {

    boolean add(WorkFlowStaffStatus staffStatus);

    Map<Long, List<WorkFlowStaffStatusRecord>> listTodayRecord(List<Long> userIds);

    boolean changeStatus(WorkFlowStaffStatus staffStatus);

    boolean autoOffline();

    List<StaffStatusAndNum> groupByStatusAndOrgType(Date date);

    List<WorkFlowStaffStatus> listByUserIds(List<Long> userIds);

    List<WorkFlowStaffStatus> listByUserIds(List<Long> userIds,int staffStatus);


    WorkFlowStaffStatus findByUserId(long userId);
}
