package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfReportCommunicaterDO;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/12/13 下午4:55
 * @desc
 */
public interface IReportCommunicaterListService {
    int insert(CfReportCommunicaterDO communicaterDO);
    List<CfReportCommunicaterDO> query(int caseId, int reportId, int type);
    CfReportCommunicaterDO queryById(long id, int caseId, int reportId);
    CfReportCommunicaterDO queryByMobile(int caseId, int reportId, String mobile);

    int delete(long id, int caseId, int reportId);
}
