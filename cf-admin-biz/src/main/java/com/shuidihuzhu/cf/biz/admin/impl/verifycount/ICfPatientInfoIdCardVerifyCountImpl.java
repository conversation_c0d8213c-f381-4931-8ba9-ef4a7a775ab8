package com.shuidihuzhu.cf.biz.admin.impl.verifycount;

import com.shuidihuzhu.cf.biz.admin.verifycount.VerifyCountRoute;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.admin.AdminVerifyTypeEnum;
import com.shuidihuzhu.cf.vo.admin.VerifyCountVo;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2020/11/4
 */
@Service
@RefreshScope
public class ICfPatientInfoIdCardVerifyCountImpl implements VerifyCountRoute {

    //校验时最多校验的次数
    private static final String KEY_ID_VERIFY_TIME_VALIDATE = "cf-patient-id-v-val-time-";
    //提交时最多校验的次数
    private static final String KEY_ID_VERIFY_TIME_SUBMIT = "cf-patient-id-v-sub-time-";

    @Value("${patient.idcard.verify.validate-max-time:5}")
    private int patientIdCardVerifyValidateMaxTime = 5;
    @Value("${patient.idcard.verify.submit-max-time:5}")
    private int patientIdCardVerifySubmitMaxTime = 5;

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler cfRedissonHandler;

    @Override
    public String getKey() {
        return "ICfPatientInfoIdCardVerify";
    }

    @Override
    public String getName() {
        return "发起案例，或者修改案例信息的时候，验证患者身份证";
    }

    @Override
    public AdminVerifyTypeEnum getType() {
        return AdminVerifyTypeEnum.ID_CARD_VERIFY;
    }

    @Override
    public int getLimit() {
        return Math.min(patientIdCardVerifyValidateMaxTime, patientIdCardVerifySubmitMaxTime) ;
    }

    @Override
    public VerifyCountVo getCurrentCountVo(long userId) {
        VerifyCountVo verifyCountVo = initVerifyCountVo();
        verifyCountVo.setCurrentCount(Math.max(getCount(redisKeySubmit(userId)), getCount(redisKeyNoSubmit(userId))));
        return verifyCountVo;
    }

    private int getCount(String key){
        Integer verifyCount = this.cfRedissonHandler.get(key, Integer.class);
        return verifyCount == null ? 0 : verifyCount;
    }

    private String redisKeySubmit(long userId){
        return KEY_ID_VERIFY_TIME_SUBMIT + userId ;
    }

    private String redisKeyNoSubmit(long userId) {
        return KEY_ID_VERIFY_TIME_VALIDATE + userId;
    }

    @Override
    public boolean clear(long userId) {
        boolean clearSubmit = this.cfRedissonHandler.del(redisKeySubmit(userId));
        boolean clearnoSubmit = this.cfRedissonHandler.del(redisKeyNoSubmit(userId));
        return clearSubmit && clearnoSubmit;
    }

    @Override
    public void innerTest(long userId) {
        cfRedissonHandler.incrAndSetTimeWhenNotExists(redisKeySubmit(userId), RedissonHandler.ONE_DAY);
        cfRedissonHandler.incrAndSetTimeWhenNotExists(redisKeyNoSubmit(userId), RedissonHandler.ONE_DAY);
    }
}
