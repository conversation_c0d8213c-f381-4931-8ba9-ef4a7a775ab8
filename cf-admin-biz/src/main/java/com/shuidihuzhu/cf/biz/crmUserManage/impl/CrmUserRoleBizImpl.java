package com.shuidihuzhu.cf.biz.crmUserManage.impl;


import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.admin.util.admin.AdminCfIdCardUtil;
import com.shuidihuzhu.cf.biz.crmUserManage.CrmUserManageRecordBiz;
import com.shuidihuzhu.cf.biz.crmUserManage.CrmUserManageService;
import com.shuidihuzhu.cf.biz.crmUserManage.CrmUserRoleBiz;
import com.shuidihuzhu.cf.biz.crmUserManage.UserManageUtil;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingAuthorBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoPayeeBiz;
import com.shuidihuzhu.cf.dao.crmUserManage.UserRoleDao;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType;
import com.shuidihuzhu.cf.model.CrmUserManage.UserManage;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoPayee;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.admin.model.CrmUserManage;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


@Slf4j
@Service
public class CrmUserRoleBizImpl implements CrmUserRoleBiz {

    @Autowired
    private UserRoleDao userRoleDao;

    @Autowired
    private AdminCrowdfundingInfoPayeeBiz infoPayeeBiz;
    @Autowired
    private IRiskDelegate riskDelegate;
    @Autowired
    private AdminCrowdfundingAuthorBiz cfAuthorBiz;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private CrmUserManageRecordBiz manageRecordBiz;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;
    @Autowired
    private CrmUserManageService crmUserManageService;

    // 有对应的案例发起时更新数据
    @Override
    @Async("crmUserManageRoleExecutor")
    public void onCaseRaise(CrmUserManage.ClewCaseRaiseInfo raiseInfo, CfFirsApproveMaterial material) {

        log.info("案例发起时更新用户对应的角色关系 param:{}", raiseInfo);
        String idCard = shuidiCipher.decrypt(UserManageUtil.queryRaiseCryptoIdCard(material));
        String name = UserManageUtil.queryRaiseUserName(material);

        updateUserRole(raiseInfo.getSourceMobile(), idCard, name);

        if (StringUtils.isNotBlank(raiseInfo.getRaiseCaseMobile()) && !Objects.equals(raiseInfo.getSourceMobile(), raiseInfo.getRaiseCaseMobile())) {
            updateUserRole(raiseInfo.getRaiseCaseMobile(), idCard, name);
        }
    }

    @Override
    @Async("crmUserManageRoleExecutor")
    public void updateCrmManageUserRoleMapping(int caseId) {

        List<UserManage.UserRoleModel> userRoleModels = userRoleDao.selectByCaseIdAndUserRole(caseId,
                Lists.newArrayList(UserManage.UserRoleEnum.PATIENT.getCode(), UserManage.UserRoleEnum.PAYEE.getCode()));

        List<UserManage.UserRoleModel> patientRoleModels = Lists.newArrayList();
        List<UserManage.UserRoleModel> payeeRoleModels = Lists.newArrayList();
        for (UserManage.UserRoleModel roleModel : userRoleModels) {
            if (roleModel.getUserRole() == UserManage.UserRoleEnum.PATIENT.getCode()) {
                patientRoleModels.add(roleModel);
            } else if (roleModel.getUserRole() == UserManage.UserRoleEnum.PATIENT.getCode()) {
                payeeRoleModels.add(roleModel);
            }
        }

        List<UserManage.UserRoleModel> mappingRecordList = Lists.newArrayList();
        // 患者
        mappingRecordList.addAll(buildRoleMapping(caseId, getPatientIdCard(caseId), UserManage.UserRoleEnum.PATIENT,
                patientRoleModels));
        // 收款人关系
        mappingRecordList.addAll(buildRoleMapping(caseId, getPayeeIdCard(caseId), UserManage.UserRoleEnum.PAYEE,
                payeeRoleModels));

        addUserRoleMappings(mappingRecordList);
    }

    private List<UserManage.UserRoleModel> buildRoleMapping(int caseId, String idCard, UserManage.UserRoleEnum roleEnum,
                                                            List<UserManage.UserRoleModel> userRoleModels) {

        List<UserManage.UserRoleModel> mappingRecordList = Lists.newArrayList();

        if (StringUtils.isBlank(idCard)) {
            userRoleDao.deleteRoleByCaseIdAndRole(caseId, roleEnum.getCode());
            log.info("crm用户管理-删除以前的映射关系。caseId:{} roleEnum:{}", caseId, roleEnum);
            return mappingRecordList;
        }

        String upperCryptoIdCard = oldShuidiCipher.aesEncrypt(AdminCfIdCardUtil.convertLastCharUpper(idCard));
        //判断身份证是否有变化
       if (needUpdateMapping(userRoleModels, upperCryptoIdCard)) {
            // 删除以前的关系
            userRoleDao.deleteRoleByCaseIdAndRole(caseId, roleEnum.getCode());
            List<UserManage.UserAccount> userAccounts = crmUserManageService.selectByIdCards(Lists.newArrayList(upperCryptoIdCard));
            for (UserManage.UserAccount account : userAccounts) {
                mappingRecordList.add(new UserManage.UserRoleModel(account.getCryptoMobile(), caseId, UserManage.UserRoleEnum.PATIENT.getCode()));
            }
        }

        return mappingRecordList;
    }

    private boolean needUpdateMapping(List<UserManage.UserRoleModel> userRoleModels, String cryptoIdCard) {

        if (CollectionUtils.isEmpty(userRoleModels)) {
            return true;
        }

        List<String> cryptoMobiles = userRoleModels.stream().map(UserManage.UserRoleModel::getCryptoMobile).collect(Collectors.toList());

        List<UserManage.UserAccount> userAccountList = crmUserManageService.selectByMobiles(cryptoMobiles);

        if (CollectionUtils.isEmpty(userAccountList)) {
            log.error("通过手机号不能找到身份证信息 mobiles:{}", cryptoMobiles);
            return true;
        }

        return !Objects.equals(userAccountList.get(0).getCryptoIdCard(), cryptoIdCard);
    }


    private String getPatientIdCard(int caseId) {
        CrowdfundingAuthor author = cfAuthorBiz.get(caseId);
        if (author != null) {
            return shuidiCipher.decrypt(author.getCryptoIdCard());
        }

        CfFirsApproveMaterial material = riskDelegate.getCfFirsApproveMaterialByInfoId(caseId);
        if (material != null) {
            return shuidiCipher.decrypt(material.getPatientCryptoIdcard());
        }

        return "";
    }

    private String getPayeeIdCard(int caseId) {
        CrowdfundingInfo info = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if (info == null || info.getRelationType() == CrowdfundingRelationType.charitable_organization
            || info.getRelationType() == CrowdfundingRelationType.LOCATION_HOSPITAL_ACCOUNT) {

            return "";
        }

        List<CrowdfundingInfoPayee> payeeList = infoPayeeBiz.selectByInfoUuidList(Lists.newArrayList(info.getInfoId()));
        if (CollectionUtils.isNotEmpty(payeeList)) {
            return shuidiCipher.decrypt(payeeList.get(0).getIdCard());
        }
        return "";
    }

    @Override
    public void updateUserRole(String mobile, String idCard, String name) {

        String cryptoMobile = oldShuidiCipher.aesEncrypt(mobile);

        String cryptoIdCard = oldShuidiCipher.aesEncrypt(idCard);
        String oppositeCryptoIdCard = oldShuidiCipher.aesEncrypt(AdminCfIdCardUtil.flipLastChar(idCard));

        // 查询所有的发起人
        Set<Integer> raiseCaseIds = Sets.newHashSet();
        fillRaiseCaseIds(cryptoIdCard, raiseCaseIds);
        if (!Objects.equals(cryptoIdCard, oppositeCryptoIdCard)) {
            fillRaiseCaseIds(oppositeCryptoIdCard, raiseCaseIds);
        }

        // 查询所有的患者
        Set<Integer> patientCaseIds = Sets.newHashSet();
        fillRaiseAndPatientCaseIds(cryptoIdCard, raiseCaseIds, patientCaseIds);
        if (!Objects.equals(cryptoIdCard, oppositeCryptoIdCard)) {
            fillRaiseAndPatientCaseIds(oppositeCryptoIdCard, raiseCaseIds, patientCaseIds);
        }

        // 查询所有的收款人
        Set<Integer> payeeCaseIds = Sets.newHashSet();
        fillPayeeInfoCaseIds(cryptoIdCard, payeeCaseIds);
        if (!Objects.equals(cryptoIdCard, oppositeCryptoIdCard)) {
            fillPayeeInfoCaseIds(oppositeCryptoIdCard, payeeCaseIds);
        }

        List<UserManage.UserRoleModel> mappingRecordList = Lists.newArrayList();
        mappingRecordList.addAll(generateRecords(raiseCaseIds, cryptoMobile, UserManage.UserRoleEnum.RAISE));
        mappingRecordList.addAll(generateRecords(patientCaseIds, cryptoMobile, UserManage.UserRoleEnum.PATIENT));
        mappingRecordList.addAll(generateRecords(payeeCaseIds, cryptoMobile, UserManage.UserRoleEnum.PAYEE));

        userRoleDao.deleteUserRolesByMobiles(Lists.newArrayList(cryptoMobile));
        log.info("删除crm用户管理的角色关系 mobile:{} cryptoMobile:{}", mobile, cryptoMobile);

        addUserRoleMappings(mappingRecordList);
    }


    private void addUserRoleMappings(List<UserManage.UserRoleModel> mappingRecordList) {
        if (CollectionUtils.isNotEmpty(mappingRecordList)) {
            Lists.partition(mappingRecordList, 100).forEach(itemList->
                    userRoleDao.addUserRoles(itemList));

            log.info("新增案例用户的角色.param:{}", mappingRecordList);
        }
    }

    private List<UserManage.UserRoleModel> generateRecords(Set<Integer> caseIds, String cryptoMobile, UserManage.UserRoleEnum roleEnum) {

        List<UserManage.UserRoleModel> roleModelList = Lists.newArrayList();

        if (CollectionUtils.isEmpty(caseIds)) {
            return Lists.newArrayList();
        }

        for (Integer caseId : caseIds) {
            roleModelList.add(new UserManage.UserRoleModel(cryptoMobile, caseId, roleEnum.getCode()));
        }

        return roleModelList;
    }

    private void fillRaiseCaseIds(String cryptoIdCard, Set<Integer> raiseCaseIds) {

        CfFirsApproveMaterial param = new CfFirsApproveMaterial();
        param.setSelfCryptoIdcard(cryptoIdCard);
        List<CfFirsApproveMaterial> firstApproveMaterials = riskDelegate.getCfFirsApproveMaterialListByParam(param);


        for (CfFirsApproveMaterial material : firstApproveMaterials) {
            if (material.getInfoId() != 0) {
                raiseCaseIds.add(material.getInfoId());
            }
        }
    }

    private void fillRaiseAndPatientCaseIds(String cryptoIdCard, Set<Integer> raiseCaseIds,
                                            Set<Integer> patientCaseIds) {

        CfFirsApproveMaterial param = new CfFirsApproveMaterial();
        param.setPatientCryptoIdcard(cryptoIdCard);
        List<CfFirsApproveMaterial> firstApproveMaterials = riskDelegate.getCfFirsApproveMaterialListByParam(param);


        for (CfFirsApproveMaterial material : firstApproveMaterials) {
            if (material.getInfoId() == 0) {
                continue;
            }

            patientCaseIds.add(material.getInfoId());
            if (material.getUserRelationType() == UserRelTypeEnum.SELF.getValue()) {
                raiseCaseIds.add(material.getInfoId());
            }
        }
    }

    private void fillPayeeInfoCaseIds(String cryptoIdCard, Set<Integer> payeeCaseIds) {

        payeeCaseIds.addAll(infoPayeeBiz.selectCaseIdsByPayeeIdCard(cryptoIdCard));

    }

    private String getLockName(int caseId) {
        return String.format(UserManage.LOCK_NAME_UPDATE_ROLE, caseId);
    }
}
