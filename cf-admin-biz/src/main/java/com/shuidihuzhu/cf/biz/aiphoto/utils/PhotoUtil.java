package com.shuidihuzhu.cf.biz.aiphoto.utils;

import com.baidu.aip.client.BaseClient;
import com.baidu.aip.face.AipFace;
import com.baidu.aip.face.MatchRequest;
import com.baidu.aip.ocr.AipOcr;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Maps;
import com.qcloud.cos.utils.IOUtils;
import com.shuidihuzhu.cf.admin.util.admin.IntegerUtil;
import com.shuidihuzhu.cf.model.aiphoto.PhotoAiInfoModel;
import lombok.extern.slf4j.Slf4j;
import me.xdrop.fuzzywuzzy.FuzzySearch;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 照片AI识别工具类
 *
 * 主要功能：
 * 1. 人脸检测与识别：检测照片中的人脸，进行人脸相似度对比
 * 2. OCR文字识别：识别身份证上的姓名、身份证号码等文字信息
 * 3. 图像处理：图像旋转校正、人脸区域裁剪、格式转换等
 * 4. 模糊匹配：使用模糊搜索算法计算文字相似度
 * 5. 综合评分：根据人脸相似度和OCR匹配度给出综合识别结果
 *
 * 技术栈：
 * - 百度AI开放平台：人脸识别API、OCR识别API
 * - FuzzyWuzzy：模糊字符串匹配算法
 * - Java图像处理：BufferedImage、ImageIO等
 *
 * 识别流程：
 * 1. 图片预处理：检测人脸并进行旋转校正
 * 2. 人脸对比：提取人脸特征进行相似度计算
 * 3. OCR识别：识别身份证文字信息
 * 4. 模糊匹配：计算文字信息匹配度
 * 5. 综合判断：根据各项得分给出最终识别结果
 *
 * 状态码定义：
 * 0 - 识别成功
 * 1 - 图片相似度过高（疑似同一张图片）
 * 2 - 照片中未检测到人脸
 * 3 - 人脸相似度不足
 * 4 - OCR识别信息匹配度不足
 *
 * @Author: liujiawei
 * @Date: 2018/7/21  15:17
 */
@Slf4j
public class PhotoUtil {
    protected static final Logger LOGGER = Logger.getLogger(BaseClient.class);

    static {
        LOGGER.setLevel(Level.ERROR);
    }

    /**
     * 获取新版本AI识别结果
     * 综合使用人脸识别和OCR技术对手持身份证照片进行身份验证
     *
     * 识别流程：
     * 1. 检测两张照片是否都包含人脸
     * 2. 进行人脸相似度对比（裁剪后的人脸 + 原图人脸）
     * 3. 进行OCR识别获取身份证信息
     * 4. 根据各项得分综合判断识别结果
     *
     * 状态码说明：
     * 0 - 识别成功
     * 1 - 图片相似度过高（疑似同一张图片）
     * 2 - 照片中未检测到人脸
     * 3 - 人脸相似度不足
     * 4 - OCR识别信息匹配度不足
     *
     * @param clientFace 百度人脸识别客户端
     * @param clientOcr 百度OCR识别客户端
     * @param fileLive 手持身份证照片URL
     * @param fileIdCard 身份证照片URL
     * @return PhotoAiInfoModel 包含完整识别结果的模型对象
     */
    public static PhotoAiInfoModel getNewVersionResult(AipFace clientFace, AipOcr clientOcr,
                                                       String fileLive, String fileIdCard) {

        // 初始化AI识别结果模型
        PhotoAiInfoModel photoAiInfoModel = new PhotoAiInfoModel();

        // 参数校验：检查照片URL是否为空
        if (StringUtils.isBlank(fileLive) || StringUtils.isBlank(fileIdCard)) {
            log.warn("getNewVersionResult 图片为空 {}, {}", fileLive, fileIdCard);
            return photoAiInfoModel;
        }

        // 初始化图片相似度得分（当前版本设为0，图片相似度检测功能已注释）
        photoAiInfoModel.setImageSimilarityScore(0);

        // 以下为图片相似度检测逻辑（已注释，备用）
        // 如果两张图片相似度>=85，认为是同一张图片，直接返回状态码1
//        int imageSimilarityResult = ImageSimilarity.getImageSimilarity(fileLive, fileIdCard);
//        photoAiInfoModel.setImageSimilarityScore(imageSimilarityResult);
//        if (imageSimilarityResult >= 85) {
//            photoAiInfoModel.setStatus(1);
//            return photoAiInfoModel;
//        }
        // ==================== 第一步：人脸检测 ====================
        // 检测两张照片是否都包含可识别的人脸
        boolean bFace = false;

        // 检测身份证照片中的人脸，类型标记为"CERT"（证件照）
        Map<String, Object> result = getRightFaceFile(clientFace, fileIdCard, "CERT");
        BufferedImage idCardRoate = (BufferedImage) result.get("rotateStream");  // 获取旋转校正后的图片
        bFace = (boolean) result.get("right");  // 获取人脸检测结果

        // 如果身份证照片中未检测到人脸，返回状态码2
        if (!bFace) {
            photoAiInfoModel.setStatus(2);
            return photoAiInfoModel;
        }

        // 检测手持身份证照片中的人脸，类型标记为"LIVE"（活体照片）
        Map<String, Object> result1 = getRightFaceFile(clientFace, fileLive, "LIVE");
        bFace = (boolean) result1.get("right");  // 获取人脸检测结果
        BufferedImage liveRoate = (BufferedImage) result1.get("rotateStream");  // 获取旋转校正后的图片

        // 如果手持身份证照片中未检测到人脸，返回状态码2
        if (!bFace) {
            photoAiInfoModel.setStatus(2);
            return photoAiInfoModel;
        }
        // ==================== 第二步：人脸相似度对比 ====================
        // 初始化人脸相似度得分变量
        int iCompareFace = 0;                    // 裁剪后人脸的相似度得分
        int iCompareFaceFromOriginFile = 0;      // 原图人脸的相似度得分

        // 从身份证照片中提取人脸区域并转换为Base64字符串
        String idcardCrop = getFaceString(clientFace, fileIdCard, "CERT", idCardRoate);

        // 从手持身份证照片中提取人脸区域并转换为Base64字符串
        String liveCrop = getFaceString(clientFace, fileLive, "LIVE", liveRoate);

        // 对比裁剪后的人脸相似度（精确度较高）
        iCompareFace = (int) getFacecomparion(clientFace, liveCrop, idcardCrop);

        // 如果裁剪后人脸相似度小于60分，进行原图人脸对比（容错处理）
        if (iCompareFace < 60) {
            // 使用原始图片进行人脸对比，阈值相对较低（55分）
            iCompareFaceFromOriginFile = (int) getFacecomparionFromOriginFile(clientFace, fileLive, fileIdCard);

            // 如果两种对比方式得分都不足，认为人脸不匹配，返回状态码3
            if (iCompareFaceFromOriginFile < 55) {
                photoAiInfoModel.setStatus(3);
                photoAiInfoModel.setShotsScore(iCompareFace);              // 记录裁剪人脸得分
                photoAiInfoModel.setArtworkScore(iCompareFaceFromOriginFile); // 记录原图人脸得分
                return photoAiInfoModel;
            }
        }

        // 保存人脸相似度得分到结果模型
        photoAiInfoModel.setShotsScore(iCompareFace);              // 裁剪后人脸相似度得分
        photoAiInfoModel.setArtworkScore(iCompareFaceFromOriginFile); // 原图人脸相似度得分
        // ==================== 第三步：OCR身份证信息识别 ====================

        // 调用OCR识别方法，同时识别两张照片中的身份证信息
        // 返回数组包含：[姓名匹配度, 身份证号匹配度, 身份证姓名, 身份证号码, 手持身份证姓名, 手持身份证号码]
        String[] resFromOcr = getOcrNewResultFromGeneralOcr(clientOcr, fileLive, fileIdCard);

        // 设置OCR识别结果到模型对象
        photoAiInfoModel.setNameScore(IntegerUtil.parseInt(resFromOcr[0]));     // 姓名匹配度得分
        photoAiInfoModel.setIdScore(IntegerUtil.parseInt(resFromOcr[1]));       // 身份证号匹配度得分
        photoAiInfoModel.setName(resFromOcr[2]);                               // 身份证上识别的姓名
        photoAiInfoModel.setIdNumber(resFromOcr[3]);                           // 身份证上识别的身份证号码
        photoAiInfoModel.setHoldIdCardName(StringUtils.defaultIfEmpty(resFromOcr[4],"无法识别"));      // 手持身份证上识别的姓名
        photoAiInfoModel.setHoldIdCardIdNumber(StringUtils.defaultIfEmpty(resFromOcr[5],"无法识别"));  // 手持身份证上识别的身份证号码

        // ==================== 第四步：综合判断识别结果 ====================

        // 如果姓名匹配度>=80分 或 身份证号匹配度>=80分，认为识别成功（状态码默认为0）
        if ((photoAiInfoModel.getNameScore() >= 80) || (photoAiInfoModel.getIdScore() >= 80)) {
            return photoAiInfoModel;
        } else {
            // 如果OCR信息匹配度都不足80分，设置状态码为4（身份证信息匹配度不足）
            photoAiInfoModel.setStatus(4);
        }

        return photoAiInfoModel;
    }

    private static String getNameFromGeneralOcr(List<String> ocrFromIdcard) {
        String name = "";
        String nameIndexStr = "名";
        int len1 = ocrFromIdcard.size();
        for (String temp : ocrFromIdcard) {
            if (temp.contains(nameIndexStr)) {
                name = temp.substring(temp.indexOf(nameIndexStr) + nameIndexStr.length());
                return name;
            }
        }
        if (len1 > 0) {
            String temp = ocrFromIdcard.get(0);
            if (temp.contains(nameIndexStr)) {
                name = temp.substring(temp.indexOf(nameIndexStr) + nameIndexStr.length());
            } else {
                if (temp.length() <= 4) {
                    name = temp;
                }
            }
        }
        return name;
    }

    private static String getIdnumberFromGeneralOcr(List<String> ocrFromIdcard) {
        String idcard = "";
        String nameIndexStr = "号码";
        for (String temp : ocrFromIdcard) {
            if (temp.contains(nameIndexStr)) {
                idcard = temp.substring(temp.indexOf(nameIndexStr) + nameIndexStr.length());
                return idcard;
            }
            String res = getIdCardFromOcr(temp);
            int len2 = res.length();
            if (len2 >= 10) {
                return res;
            }
        }
        return idcard;
    }


    /**
     * 通过OCR技术识别两张照片中的身份证信息并进行对比
     *
     * 识别流程：
     * 1. 分别对手持身份证照片和身份证照片进行OCR文字识别
     * 2. 从身份证照片中提取姓名和身份证号码
     * 3. 在手持身份证照片的OCR结果中查找匹配的姓名和身份证号码
     * 4. 使用模糊匹配算法计算匹配度得分
     * 5. 同时提取手持身份证照片中的姓名和身份证号码
     *
     * @param client 百度OCR识别客户端
     * @param fileLive 手持身份证照片URL
     * @param fileIdCard 身份证照片URL
     * @return String[] 长度为6的数组，包含：
     *         [0] 姓名匹配度得分（0-100）
     *         [1] 身份证号匹配度得分（0-100）
     *         [2] 身份证照片中识别的姓名
     *         [3] 身份证照片中识别的身份证号码
     *         [4] 手持身份证照片中识别的姓名
     *         [5] 手持身份证照片中识别的身份证号码
     */
    private static String[] getOcrNewResultFromGeneralOcr(AipOcr client, String fileLive, String fileIdCard) {
        // 初始化返回结果数组
        int size = 6;
        String[] result = new String[size];
        result[0] = "0";        // 姓名匹配度得分，默认0
        result[1] = "0";        // 身份证号匹配度得分，默认0
        result[2] = "无法识别";  // 身份证照片姓名，默认无法识别
        result[3] = "无法识别";  // 身份证照片身份证号，默认无法识别

        // ==================== 第一步：OCR文字识别 ====================

        // 对手持身份证照片进行OCR识别
        Stopwatch stopwatch = Stopwatch.createStarted();
        List<String> ocrFromLive = getOcrFromLive(client, fileLive);
        stopwatch.stop();
        log.debug("手持身份证OCR识别耗时:{}", stopwatch);

        // 对身份证照片进行OCR识别
        stopwatch.reset().start();
        List<String> ocrFromIdcard = getOcrFromLive(client, fileIdCard);
        stopwatch.stop();
        log.debug("身份证照片OCR识别耗时:{}", stopwatch);

        // ==================== 第二步：提取身份证信息 ====================

        // 从身份证照片OCR结果中提取姓名
        String name = getNameFromGeneralOcr(ocrFromIdcard);
        // 姓名长度校验（正常姓名应该在1-5个字符之间）
        if ((name.length() < 1) || (name.length() > 5)) {
            return result;  // 姓名不合法，返回默认结果
        }

        // 从身份证照片OCR结果中提取身份证号码
        String idcardNumber = getIdnumberFromGeneralOcr(ocrFromIdcard);
        // 身份证号码长度校验（应该在10-20位之间）
        if ((idcardNumber.length() < 10) || (idcardNumber.length() > 20)) {
            return result;  // 身份证号码不合法，返回默认结果
        }

        // ==================== 第三步：计算匹配度得分 ====================

        // 在手持身份证OCR结果中查找姓名的最佳匹配得分
        int nameScore = getBestPartialratioSocre(ocrFromLive, name);
        // 在手持身份证OCR结果中查找身份证号码的最佳匹配得分
        int idcardNumberScore = getBestPartialratioSocre(ocrFromLive, idcardNumber);

        // ==================== 第四步：组装返回结果 ====================

        result[0] = nameScore + "";           // 姓名匹配度得分
        result[1] = idcardNumberScore + "";   // 身份证号匹配度得分
        result[2] = name;                     // 身份证照片中的姓名
        result[3] = idcardNumber;             // 身份证照片中的身份证号码
        result[4] = getNameFromGeneralOcr(ocrFromLive);      // 手持身份证照片中的姓名
        result[5] = getIdnumberFromGeneralOcr(ocrFromLive);  // 手持身份证照片中的身份证号码

        return result;
    }

    /**
     * 在OCR识别结果中查找目标字符串的最佳匹配得分
     * 使用模糊匹配算法计算相似度，返回最高的匹配得分
     *
     * @param ocrRes OCR识别结果列表，包含从图片中识别出的所有文字
     * @param target 目标字符串（要查找的姓名或身份证号码）
     * @return int 最佳匹配得分（0-100），100表示完全匹配
     */
    private static int getBestPartialratioSocre(List<String> ocrRes, String target) {
        int res = 0;    // 存储最高得分
        int tmp = 0;    // 临时得分变量

        // 遍历OCR识别结果中的每个文字片段
        for (String matchString : ocrRes) {
            // 只处理长度大于1的文字片段，过滤掉单个字符
            if (matchString.length() > 1) {
                // 使用FuzzySearch库计算部分匹配得分
                // partialRatio方法会计算两个字符串的相似度（0-100）
                tmp = FuzzySearch.partialRatio(matchString, target);

                // 保留最高得分
                if (tmp > res) {
                    res = tmp;
                }
            }
        }
        return res;
    }

    private static String getIdCardFromOcr(String input) {
        String regEx = "\\d{10,20}$";
        //Pattern是一个正则表达式经编译后的表现模式
        Pattern p = Pattern.compile(regEx);
        // 一个Matcher对象是一个状态机器，它依据Pattern对象做为匹配模式对字符串展开匹配检查。
        Matcher m = p.matcher(input);
        String result = "";
        if (m.find()) {
            result = input.substring(m.start(), m.end());
        }
        return result;
    }

    private static List<String> getOcrFromLive(AipOcr client, String file) {
        // 传入可选参数调用接口
        HashMap<String, String> options = new HashMap<String, String>();
        options.put("language_type", "CHN_ENG");
        options.put("detect_direction", "true");
        options.put("detect_language", "true");
        options.put("probability", "true");
        List<String> result = new ArrayList<>();
        // 参数为本地图片路径
        try {
            JSONObject res = client.basicGeneral(getStreamByte(file), options);
            JSONArray top = res.getJSONArray("words_result");
            int len1 = top.length();
            for (int i = 0; i < len1; i++) {
                log.debug("{}", top.getJSONObject(i).getString("words"));
                result.add(top.getJSONObject(i).getString("words"));
            }
            return result;
        } catch (Exception e) {
            log.debug(e.getMessage());
            return result;
        }
    }

    private static double[] getFaceLocation(JSONObject input) {
        int size = 5;
        double[] res = new double[size];
        try {
            if (input.get("result").toString().equals("null")) {
                res[0] = 0.0;
                res[1] = 0.0;
                res[2] = 0.0;
                res[3] = 0.0;
                res[4] = 0.0;
                return res;
            } else {
                double top = input.getJSONObject("result").getJSONArray("face_list").getJSONObject(0).getJSONObject("location").getDouble("top");
                double left = input.getJSONObject("result").getJSONArray("face_list").getJSONObject(0).getJSONObject("location").getDouble("left");
                double width = input.getJSONObject("result").getJSONArray("face_list").getJSONObject(0).getJSONObject("location").getDouble("width");
                double height = input.getJSONObject("result").getJSONArray("face_list").getJSONObject(0).getJSONObject("location").getDouble("height");
                double rotation = input.getJSONObject("result").getJSONArray("face_list").getJSONObject(0).getJSONObject("location").getDouble("rotation");
                res[0] = top;
                res[1] = left;
                res[2] = width;
                res[3] = height;
                res[4] = rotation;
                return res;
            }
        } catch (Exception e) {
            //
        }
        return res;
    }

    private static BufferedImage rotateImage(final BufferedImage bufferedimage,
                                             final int degree) {
        int w = bufferedimage.getWidth();
        int h = bufferedimage.getHeight();
        int type = bufferedimage.getColorModel().getTransparency();
        BufferedImage img;
        Graphics2D graphics2d;
        (graphics2d = (img = new BufferedImage(w, h, type))
                .createGraphics()).setRenderingHint(
                RenderingHints.KEY_INTERPOLATION,
                RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        graphics2d.rotate(Math.toRadians(degree), w / 2, h / 2);
        graphics2d.drawImage(bufferedimage, 0, 0, null);
        graphics2d.dispose();
        bufferedimage.getGraphics().dispose();
        return img;
    }

    /**
     * 检测照片中是否包含人脸并进行图像校正
     *
     * 功能说明：
     * 1. 调用百度人脸检测API检测照片中的人脸
     * 2. 如果检测到人脸，获取人脸的位置和旋转角度信息
     * 3. 根据检测到的旋转角度对原图进行校正
     * 4. 返回检测结果和校正后的图像
     *
     * @param client 百度人脸识别客户端
     * @param file1 图片文件URL
     * @param type 人脸类型标识（"CERT"-证件照，"LIVE"-活体照片）
     * @return Map 包含检测结果的映射
     *         - "right": Boolean 是否检测到人脸
     *         - "rotateStream": BufferedImage 旋转校正后的图像（检测失败时为null）
     */
    private static Map<String, Object> getRightFaceFile(AipFace client, String file1, String type) {
        // 设置人脸检测参数
        HashMap<String, String> options = new HashMap<String, String>();
        options.put("max_face_num", "1");    // 最多检测1个人脸
        options.put("face_type", type);      // 设置人脸类型（CERT或LIVE）

        // 初始化返回结果
        Map result = Maps.newHashMap();

        try {
            // 将图片转换为Base64格式
            String imageType = "BASE64";
            String image1 = getImageBinary(file1);

            // 调用百度人脸检测API并计时
            Stopwatch stopwatch = Stopwatch.createStarted();
            JSONObject res = client.detect(image1, imageType, options);
            stopwatch.stop();
            log.info("第一次人脸检测耗时:{}", stopwatch);

            // 检查API返回结果
            if ("null".equals(res.get("result").toString())) {
                // 未检测到人脸
                result.put("right", false);
                result.put("rotateStream", null);
                return result;
            } else {
                // 检测到人脸，获取人脸位置和角度信息
                double[] rect = getFaceLocation(res);
                if (rect.length == 5) {
                    // 获取旋转角度并取反（API返回的角度需要反向旋转）
                    int rotation = (int) rect[4];
                    rotation = rotation * (-1);

                    // 读取原始图像并进行旋转校正
                    BufferedImage originalImage = ImageIO.read(ImageSimilarity.getStream(file1));
                    BufferedImage rotateoriginalImage = rotateImage(originalImage, rotation);

                    // 返回成功结果
                    result.put("right", true);
                    result.put("rotateStream", rotateoriginalImage);
                    return result;
                }
            }
        } catch (Exception e) {
            // 捕获异常并记录日志
            log.debug("人脸检测异常：{}", e);
            result.put("right", false);
            result.put("rotateStream", null);
            return result;
        }

        // 默认返回检测失败结果
        result.put("right", false);
        result.put("rotateStream", null);
        return result;
    }

    /**
     * 从校正后的图像中提取人脸区域并转换为Base64字符串
     *
     * 功能说明：
     * 1. 对校正后的图像进行人脸检测
     * 2. 根据检测到的人脸位置信息计算裁剪区域
     * 3. 扩展裁剪区域边界以包含更多面部特征
     * 4. 裁剪人脸区域并转换为Base64字符串用于后续对比
     *
     * @param client 百度人脸识别客户端
     * @param file1 原始图片文件URL（用于重新读取原图进行裁剪）
     * @param type 人脸类型标识（"CERT"-证件照，"LIVE"-活体照片）
     * @param bufferedImage 已校正的图像对象
     * @return String 裁剪后的人脸区域Base64字符串，失败时返回空字符串
     */
    private static String getFaceString(AipFace client, String file1, String type, BufferedImage bufferedImage) {
        // 设置人脸检测参数
        HashMap<String, String> options = new HashMap<String, String>();
        options.put("max_face_num", "1");    // 最多检测1个人脸
        options.put("face_type", type);      // 设置人脸类型

        String imageType = "BASE64";
        String result = "";  // 存储最终的Base64结果

        try {
            // 将校正后的图像转换为Base64格式
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            ImageIO.write(bufferedImage, "jpg", out);
            String image1 = Base64.encodeBase64String(out.toByteArray()).trim();

            // 调用百度人脸检测API并计时
            Stopwatch stopwatch = Stopwatch.createStarted();
            JSONObject res = client.detect(image1, imageType, options);
            stopwatch.stop();
            log.debug("第二次人脸检测耗时:{}", stopwatch);
            // 检查是否检测到人脸
            if (!"null".equals(res.get("result").toString())) {
                // 获取人脸位置信息数组 [top, left, width, height, rotation]
                double[] rect = getFaceLocation(res);
                if (rect.length == 5) {
                    // 提取人脸位置和角度信息
                    double dtop = rect[0];        // 人脸框顶部Y坐标
                    double dleft = rect[1];       // 人脸框左侧X坐标
                    double dwidth = rect[2];      // 人脸框宽度
                    double dheight = rect[3];     // 人脸框高度
                    double drotation = rect[4];   // 旋转角度

                    // 处理旋转角度（API返回的角度需要反向）
                    int rotation = (int) drotation;
                    rotation = rotation * (-1);

                    // ==================== 扩展裁剪区域边界 ====================
                    // 向上扩展20%的高度，确保包含额头等面部特征
                    if ((dtop - dheight * 0.2) > 0) {
                        dtop = dtop - dheight * 0.2;
                    } else {
                        dtop = 0;  // 防止超出图像边界
                    }

                    // 向左扩展20%的宽度，确保包含完整面部轮廓
                    if ((dleft - dwidth * 0.2) > 0) {
                        dleft = dleft - dwidth * 0.2;
                    } else {
                        dleft = 0;  // 防止超出图像边界
                    }

                    // 获取原始图像尺寸用于边界检查
                    BufferedImage originalImage = ImageIO.read(ImageSimilarity.getStream(file1));
                    int allWidth = (originalImage.getWidth());
                    int allHeight = (originalImage.getHeight());

                    // 向右扩展40%的宽度，如果超出图像边界则调整
                    if ((dleft + dwidth * 1.4) > allWidth) {
                        dwidth = allWidth - dleft;  // 调整为不超出右边界
                    } else {
                        dwidth = dwidth * 1.4;      // 扩展40%
                    }

                    // 向下扩展40%的高度，如果超出图像边界则调整
                    if ((dtop + dheight * 1.4) > allHeight) {
                        dheight = allHeight - dtop;  // 调整为不超出下边界
                    } else {
                        dheight = dheight * 1.4;     // 扩展40%
                    }

                    // 转换为整数坐标
                    int top = (int) dtop;
                    int left = (int) dleft;
                    int width = (int) dwidth;
                    int height = (int) dheight;

                    // 裁剪人脸区域并转换为Base64字符串
                    result = getCropImageBinary(file1, top, left, width, height, rotation);
                }
            }
        } catch (Exception e) {
            log.debug("人脸检测异常：{}", e);
            return result;
        }finally {
            if (bufferedImage != null){
                bufferedImage.getGraphics().dispose();
            }
        }
        return result;
    }

    private static double getFacecomparion(AipFace client, String imageLive, String imageIdcard) {
        // image1/image2也可以为url或facetoken, 相应的imageType参数需要与之对应。
        MatchRequest req1 = new MatchRequest(imageLive, "BASE64", "LIVE", "NONE", "NONE");
        MatchRequest req2 = new MatchRequest(imageIdcard, "BASE64", "IDCARD", "NONE", "NONE");
        ArrayList<MatchRequest> requests = new ArrayList<MatchRequest>();
        requests.add(req1);
        requests.add(req2);
        try {
            Stopwatch stopwatch = Stopwatch.createStarted();
            JSONObject res = client.match(requests);
            stopwatch.stop();
            log.debug("第三次耗时:{}", stopwatch);
            if ("null".equals(res.get("result").toString())) {
                return 0;
            } else {
                return res.getJSONObject("result").getDouble("score");
            }
        } catch (Exception e) {
            log.debug(e.getMessage());
            return 0;
        }
    }


    private static double getFacecomparionFromOriginFile(AipFace client, String imageLiveFile, String imageIdcardFile) {

        String imageLive = getImageBinary(imageLiveFile);
        String imageIdcard = getImageBinary(imageIdcardFile);
        // image1/image2也可以为url或facetoken, 相应的imageType参数需要与之对应。
        MatchRequest req1 = new MatchRequest(imageLive, "BASE64", "LIVE", "NONE", "NONE");
        MatchRequest req2 = new MatchRequest(imageIdcard, "BASE64", "IDCARD", "NONE", "NONE");
        ArrayList<MatchRequest> requests = new ArrayList<MatchRequest>();
        requests.add(req1);
        requests.add(req2);
        try {
            Stopwatch stopwatch = Stopwatch.createStarted();
            JSONObject res = client.match(requests);
            stopwatch.stop();
            log.debug("第四次耗时:{}", stopwatch);
            if ("null".equals(res.get("result").toString())) {
                return 0;
            } else {
                return res.getJSONObject("result").getDouble("score");
            }
        } catch (Exception e) {
            log.debug(e.getMessage());
            return 0;
        }
    }

    private static String getImageBinary(String file) {
        try {
            return Base64.encodeBase64String(getStreamByte(file)).trim();
        } catch (Exception e) {
            log.error("PhotoUtil.{}", e.getMessage(), e);
        }
        return null;
    }

    private static byte[] getStreamByte(String fileURL) throws Exception {
        InputStream inputStream = null;
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        if (fileURL.startsWith("http") || fileURL.startsWith("https")) {
            URL url = new URL(fileURL);
            URLConnection urlConnection = url.openConnection();
            urlConnection.setConnectTimeout(5000);
            urlConnection.setReadTimeout(60000);
            inputStream = urlConnection.getInputStream();

        } else {
            inputStream = new FileInputStream(fileURL);
        }
        IOUtils.copy(inputStream, output);
        inputStream.close();
        return output.toByteArray();
    }

    private static String getCropImageBinary(String file, int top, int left, int width, int height, int rotation) {
        BufferedImage originalImage = null;
        try {
            originalImage = ImageIO.read(ImageSimilarity.getStream(file));
            BufferedImage croppedImage = originalImage.getSubimage(left, top, width, height);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(croppedImage, "jpg", baos);
            byte[] bytes = baos.toByteArray();
            return Base64.encodeBase64String(bytes).trim();
        } catch (Exception e) {
            log.warn("getCropImageBinary", e);
        }finally {
           if (originalImage != null){
               originalImage.getGraphics().dispose();
           }
        }
        return "";

    }
}
