package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity;

import java.util.List;

public interface CfRefuseUseSceneRankMappingBiz {

    void addUseSceneRankMapping(CfRefuseReasonEntity reasonEntity, List<Integer> addSceneIds, boolean needMaxRank);

    void deleteUseSceneRankMapping(List<Integer> mappingIds);

    void updateUseSceneRankMapping(CfRefuseReasonEntity entity, int deleteStatus);

    List<CfRefuseReasonEntity.CfRefuseReasonUseSceneRankMapping> selectByEntityId(int entityId);

    List<CfRefuseReasonEntity.CfRefuseReasonUseSceneRankMapping> selectByEntityIdsAndUseScene(List<Integer> entityIds, int useScene);

    void swapRankMapping(int upEntityId, int downEntityId, int useScene);

    List<CfRefuseReasonEntity> filterUseSceneSortEntityList(List<CfRefuseReasonEntity> entityList, int useScene);

    void fillReasonEntityUseSceneIds(List<CfRefuseReasonEntity> entityList);


}
