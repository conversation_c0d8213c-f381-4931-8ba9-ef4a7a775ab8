package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfRefuseReasonMsgBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfRefuseReasonMsgDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonMsg;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;
import java.util.Set;

/**
 * Created by Ahrievil on 2017/7/31
 */
@Service
public class AdminCfRefuseReasonMsgBizImpl implements AdminCfRefuseReasonMsgBiz {

    @Autowired
    private AdminCfRefuseReasonMsgDao adminCfRefuseReasonMsgDao;

    @Override
    public int insertOne(CfRefuseReasonMsg cfRefuseReasonMag) {
        return adminCfRefuseReasonMsgDao.insertOne(cfRefuseReasonMag);
    }

    @Override
    public CfRefuseReasonMsg selectByInfoUuidAndType(String infoUuid, int type) {
        return adminCfRefuseReasonMsgDao.selectByInfoUuidAndType(infoUuid, type);
    }

    @Override
    public CfRefuseReasonMsg selectByInfoIdAndType(String infoUuid, int type) {
        return adminCfRefuseReasonMsgDao.selectByInfoIdAndType(infoUuid, type);
    }

    @Override
    public int insertList(List<CfRefuseReasonMsg> list) {
        return adminCfRefuseReasonMsgDao.insertList(list);
    }

    @Override
    public List<String> selectWithTimeLimit(Timestamp begin, Timestamp end, int start, int size) {
        return adminCfRefuseReasonMsgDao.selectWithTimeLimit(begin, end, start, size);
    }

    @Override
    public int deleteByInfoUuid(String infoUuid) {
        return adminCfRefuseReasonMsgDao.deleteByInfoUuid(infoUuid);
    }

    @Override
    public int deleteByInfoUuidAndTypes(String infoUuid, Set<Integer> set) {
        if (CollectionUtils.isEmpty(set)) return 0;
        return adminCfRefuseReasonMsgDao.deleteByInfoUuidAndTypes(infoUuid, set);
    }

    @Override
    public List<CfRefuseReasonMsg> selectByInfoUuid(String infoUuid) {
        return adminCfRefuseReasonMsgDao.selectByInfoUuid(infoUuid);
    }

    @Override
    public List<CfRefuseReasonMsg> selectSimpleFieldsByTimeLimit(Timestamp begin, Timestamp end, int start, int size) {
        return adminCfRefuseReasonMsgDao.selectSimpleFieldsByTimeLimit(begin, end, start, size);
    }
}
