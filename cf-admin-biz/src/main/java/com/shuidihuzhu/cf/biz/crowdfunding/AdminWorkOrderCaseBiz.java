package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminWorkOrderCaseConst;
import com.shuidihuzhu.cf.enums.crowdfunding.CfCallOutConditionTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingOperationEnum;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderCase;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderCaseDo;
import com.shuidihuzhu.cf.vo.crowdfunding.AdminApproveVo;
import com.shuidihuzhu.common.web.model.Response;
import org.apache.commons.lang3.tuple.Pair;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR> Ahrievil
 */
public interface AdminWorkOrderCaseBiz {
	int insertOne(AdminWorkOrderCase adminWorkOrderCase);

	int insertList(List<AdminWorkOrderCase> adminWorkOrderCase);

	AdminWorkOrderCase selectById(long id);

	AdminWorkOrderCase selectByCaseId(int caseId);

	List<AdminWorkOrderCase> selectByCaseIdAndTypeAndStatusAndTime(int caseId, AdminWorkOrderCaseConst.CaseType type, AdminWorkOrderCaseConst.Status status,
																    Timestamp createTime, Timestamp endTime);

	List<AdminWorkOrderCase> selectUnHandleCaseTaskByTime(AdminWorkOrderConst.Type type, AdminWorkOrderConst.Task task,
	                                                      Timestamp startTime, Timestamp endTime);

	//分配案例审核的工单
	List<AdminWorkOrderCase> assigningCaseApproveTask(AdminWorkOrderConst.Type type,
										   AdminWorkOrderConst.Task task,
										   AdminWorkOrderConst.TaskType taskType,
										   int userId, int count,int channel);

	int updateStatusById(long id, AdminWorkOrderCaseConst.Status status);

	int update(AdminWorkOrderCase adminWorkOrderCase);

	int updateCallStatusById(long id, CfCallOutConditionTypeEnum cfCallOutConditionTypeEnum);

	void completeTask(AdminWorkOrderCase adminWorkOrderCase, String comment);


	int updateStatusByIdList(AdminWorkOrderCaseConst.Status status, List<Long> list);


	List<AdminWorkOrderCase> selectPage(int operatorId, int channel,int current, int pageSize);

	Pair<Long, List<AdminWorkOrderCase>> selectPageFromEs(int operatorId, int channel, int current, int pageSize);


	int updateOrderCaseStatus(int status,int workOrderId);

	List<AdminWorkOrderCase> selectByCaseIdList(List<Long> workOrderIds);

	Response updateWorkOrderCaseApprove(int caseId, String infoUuid, int userId, String comment,
										CrowdfundingOperationEnum crowdfundingOperationEnum);

	void onCaseEnd(long caseId);

}
