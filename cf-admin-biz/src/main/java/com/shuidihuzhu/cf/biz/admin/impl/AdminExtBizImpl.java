package com.shuidihuzhu.cf.biz.admin.impl;

import com.shuidihuzhu.cf.biz.admin.AdminExtBiz;
import com.shuidihuzhu.cf.dao.admin.AdminExtDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by Ahrievil on 2017/7/24
 */
@Service
public class AdminExtBizImpl implements AdminExtBiz {

    @Autowired
    private AdminExtDao adminExtDao;
    @Override
    public List<String> selectByInfoNotExistInExtTable() {
        return adminExtDao.selectByInfoNotExistInExtTable();
    }

    @Override
    public List<String> selectInfoUuidsInOperationRecord() {
        return adminExtDao.selectInfoUuidsInOperationRecord();
    }
}
