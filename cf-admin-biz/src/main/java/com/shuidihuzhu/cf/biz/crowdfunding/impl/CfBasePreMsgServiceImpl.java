package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.verify.client.menu.IdCardVerifyResultEnum;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.account.verify.client.model.VerifyIdcardVO;
import com.shuidihuzhu.cf.delegate.saas.AdminOrganization;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.OrganizationClientV1;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.crowdfunding.CfBasePreMsgService;
import com.shuidihuzhu.cf.dao.crowdfunding.CfBasePreMsgDao;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.facade.AdminApolloCofig;
import com.shuidihuzhu.cf.model.crowdfunding.CfBasePreMsg;
import com.shuidihuzhu.cf.model.crowdfunding.CfBasePreMsgRecord;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.crowdfunding.IdCardVerifyService;
import com.shuidihuzhu.cf.util.MobileUtil;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.admin.model.CfBasePreMsgStatusEnum;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfPremsgStatusChangeModel;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.IdCardUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2019/10/14
 */
@Slf4j
@Service
public class CfBasePreMsgServiceImpl implements CfBasePreMsgService {

    @Autowired
    private CfBasePreMsgDao cfBasePreMsgDao;

    @Autowired
    private SeaAccountClientV1 clientV1;

    @Autowired
    private OrganizationClientV1 orgClientV1;

    @Autowired
    private IdCardVerifyService idCardVerifyService;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private CfClewtrackFeignClient cfClewtrackFeignClient;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;



    @Override
    public CfBasePreMsg getMsg(String mobile) {

        CfBasePreMsg c = cfBasePreMsgDao.getCfBasePreMsg(mobile);

        if (c == null){
            return null;
        }

        c.setMobile(shuidiCipher.decrypt(c.getMobile()));

        if (StringUtils.isNotEmpty(c.getPatientCryptoIdcard())){
            c.setPatientCryptoIdcard(shuidiCipher.decrypt(c.getPatientCryptoIdcard()));
        }

        if (StringUtils.isNotEmpty(c.getInitiatorCryptoIdcard())){
            c.setInitiatorCryptoIdcard(shuidiCipher.decrypt(c.getInitiatorCryptoIdcard()));
        }
        fillUseTemplate(Lists.newArrayList(c));

        return c;
    }

    @Override
    public int updateMsgStatus(String mobile, int status, int caseId, String uuid) {

        CfBasePreMsg base = cfBasePreMsgDao.getCfBasePreMsg(mobile);

        if (base == null){
            return 0;
        }

        if (base.getMsgStatus() == CfBasePreMsgStatusEnum.confirm.getCode()){
            return 0;
        }

        int a = cfBasePreMsgDao.updateMsgStatusByMobile(mobile,status,caseId,uuid);

        /**
         * 来自1v1服务的初审录入，状态变化时需要修改线索任务的初审录入的状态
         */
        if(base.getTaskId() > 0 && a > 0){
            Response<String> response = cfClewtrackFeignClient.premsgStatusChangeNotify(CfPremsgStatusChangeModel.build(base.getTaskId(), status,base.getExchangeFlag(),base.getMobile()));
            log.info("PreMsgRecord status update taskId:{},status:{},response:{}", base.getTaskId(), status, response.getCode());
        }

        saveRecord(base,"用户已确认");

        return a;
    }

    private void fillUseTemplate(List<CfBasePreMsg> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Long> preIds = list.stream().map(CfBasePreMsg::getId).collect(Collectors.toList());

        List<CfBasePreMsg.UseTemplateRecord> allRecords = cfBasePreMsgDao.selectUseRecordByPreIds(preIds);

        Map<Long, CfBasePreMsg.UseTemplateRecord> maps = Maps.newHashMap();
        for (CfBasePreMsg.UseTemplateRecord record : allRecords)  {
            maps.put(record.getPreId(), record);
        }

        for (CfBasePreMsg preMsg : list) {
            preMsg.setUseRecord(maps.get(preMsg.getId()));
        }
    }

    @Override
    public List<CfBasePreMsgRecord> getRecords(long msgId) {
        return cfBasePreMsgDao.getRecords(msgId);
    }

    @Override
    public OpResult<List<CfBasePreMsg>> get1V1MsgList(String mobile, String startTime, String endTime) {
        if(StringUtils.isNotEmpty(mobile) && MobileUtil.illegal(mobile)){
            return OpResult.createFailResult(AdminErrorCode.MOBILE_FORMAT_ERROR);
        }

        if (StringUtils.isNotEmpty(mobile)){
            mobile = oldShuidiCipher.aesEncrypt(mobile);
        }
        List<CfBasePreMsg> list = cfBasePreMsgDao.get1V1MsgList(mobile,startTime,endTime);

        //手机号解密
        list.stream().forEach(r->r.setMobile(shuidiCipher.decrypt(r.getMobile())));
        fillUseTemplate(list);
        return OpResult.createSucResult(list);
    }

    /**
     * 参数校验
     * @param cfBasePreMsg
     * @return
     */
    private OpResult checkParam(CfBasePreMsg cfBasePreMsg, boolean clewTask){

        if (StringUtils.isEmpty(cfBasePreMsg.getMobile()) || MobileUtil.illegal(cfBasePreMsg.getMobile())){
            return OpResult.createFailResult(AdminErrorCode.MOBILE_FORMAT_ERROR);
        }

        /**
         * 在1v1服务的任务列表进行初审录入，需要传相应的线索任务id进行关联
         */
        if(clewTask && cfBasePreMsg.getTaskId() <= 0){
            return OpResult.createFailResult(AdminErrorCode.CLEW_TASK_ERROR);
        }

        /**
         * 在1v1服务的任务列表进行初审录入，不需要传疾病名和原因
         */
        if (!clewTask && (StringUtils.isEmpty(cfBasePreMsg.getDiseaseName()) || StringUtils.isEmpty(cfBasePreMsg.getReason()))){
            return OpResult.createFailResult(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        if (StringUtils.isNotEmpty(cfBasePreMsg.getInitiatorCryptoIdcard()) && IdCardUtil.illegal(cfBasePreMsg.getInitiatorCryptoIdcard())) {
            return OpResult.createFailResult(AdminErrorCode.IDCARD_ERROR_INITIATOR);
        }

        if (cfBasePreMsg.getPatientIdType() == UserIdentityType.identity.getCode()
                && StringUtils.isNotEmpty(cfBasePreMsg.getPatientCryptoIdcard())
                && IdCardUtil.illegal(cfBasePreMsg.getPatientCryptoIdcard())) {

            return OpResult.createFailResult(AdminErrorCode.IDCARD_ERROR_PATIENT);
        }


        if (cfBasePreMsg.getRelationType() != UserRelTypeEnum.SELF.getValue()
                && cfBasePreMsg.getPatientIdType() == UserIdentityType.identity.getCode()
                && StringUtils.isNotEmpty(cfBasePreMsg.getInitiatorCryptoIdcard())
                && StringUtils.isNotEmpty(cfBasePreMsg.getInitiatorName())
                && cfBasePreMsg.getInitiatorCryptoIdcard().equals(cfBasePreMsg.getPatientCryptoIdcard())
                && cfBasePreMsg.getInitiatorName().equals(cfBasePreMsg.getPatientName())
        )
        {
            return OpResult.createFailResult(AdminErrorCode.CASE_MSG_ERROR_TYPE);
        }

        //发起人不为空  校验身份证
        if (StringUtils.isNotEmpty(cfBasePreMsg.getInitiatorName())
                && StringUtils.isNotEmpty(cfBasePreMsg.getInitiatorCryptoIdcard())){
            VerifyIdcardVO verifyIdcardVO = idCardVerifyService.verfiy(cfBasePreMsg.getInitiatorName(), cfBasePreMsg.getInitiatorCryptoIdcard(), UserRelTypeEnum.SELF, 0);

            if(verifyIdcardVO.getCode() != IdCardVerifyResultEnum.MATCH){
                return OpResult.createFailResult(AdminErrorCode.IDCARD_FAILED_INITIATOR);
            }
        }

        //患者身份证做校验
        if (cfBasePreMsg.getPatientIdType() == UserIdentityType.identity.getCode()
         && StringUtils.isNotEmpty(cfBasePreMsg.getPatientName())
                && StringUtils.isNotEmpty(cfBasePreMsg.getPatientCryptoIdcard())){

            VerifyIdcardVO verifyIdcardVO = idCardVerifyService.verfiy(cfBasePreMsg.getPatientName(), cfBasePreMsg.getPatientCryptoIdcard(), UserRelTypeEnum.SELF, 0);
            if(verifyIdcardVO.getCode() != IdCardVerifyResultEnum.MATCH){
                return OpResult.createFailResult(AdminErrorCode.IDCARD_FAILED_PATIENT);
            }
        }

        return OpResult.createSucResult();
    }

    // https://wiki.shuiditech.com/pages/viewpage.action?pageId=433294191
    private boolean checkAllProperty(CfBasePreMsg cfBasePreMsg){

        return StringUtils.isNotEmpty(cfBasePreMsg.getTitle())
                && StringUtils.isNotEmpty(cfBasePreMsg.getContent())
                && cfBasePreMsg.getTargetAmount() > 0
                && StringUtils.isNotEmpty(cfBasePreMsg.getMobile());
    }


    private void setUserMsg(CfBasePreMsg cfBasePreMsg,int userId){

        AuthRpcResponse<String> authRpcResponse = clientV1.getMisByUserId(userId);
        log.info("addMsg getMisByUserId userid={} reuslt={}",userId,JSON.toJSONString(authRpcResponse));
        String userName = "";
        if (authRpcResponse != null){
            userName = authRpcResponse.getResult();
        }

        AuthRpcResponse<AdminOrganization> rpcResponse = orgClientV1.getUserOrgInfo(userId);
        log.info("addMsg getUserOrgInfo userid={} reuslt={}",userId,JSON.toJSONString(rpcResponse));
        String org = "";
        if (rpcResponse != null && rpcResponse.getResult() != null){
            org = rpcResponse.getResult().getName();
        }


        cfBasePreMsg.setOperatorId(userId);
        cfBasePreMsg.setOperatorOrg(org+"-"+userName);
    }


    private void saveRecord(CfBasePreMsg cfBasePreMsg,String mode){

        CfBasePreMsgRecord record = new CfBasePreMsgRecord();
        record.setMsgId(cfBasePreMsg.getId());
        record.setOperatorId(cfBasePreMsg.getOperatorId());
        record.setOperatorOrg(cfBasePreMsg.getOperatorOrg());
        record.setMode(mode);

        cfBasePreMsgDao.insertRecord(record);
    }
}
