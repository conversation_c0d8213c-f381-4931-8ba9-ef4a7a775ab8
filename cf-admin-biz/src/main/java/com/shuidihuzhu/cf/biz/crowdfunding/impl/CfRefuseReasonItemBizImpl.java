package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseReasonCommonBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseReasonItemBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfRefuseReasonItemDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonItem;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by ahrievil on 2017/1/17.
 */
@Service
public class CfRefuseReasonItemBizImpl implements CfRefuseReasonItemBiz {

    @Autowired
    private CfRefuseReasonItemDao cfRefuseReasonItemDao;

    @Autowired
    private CfRefuseReasonCommonBiz reasonCommonBiz;

    @Override
    public List<CfRefuseReasonItem> getRefuseItem() {
        List<CfRefuseReasonItem> refuseItem = cfRefuseReasonItemDao.getRefuseItem();
        return refuseItem != null ? refuseItem : Collections.EMPTY_LIST ;
    }

    @Override
    public CfRefuseReasonItem getContentById(int id){
        return cfRefuseReasonItemDao.getContentById(id);
    }

    @Override
    public List<CfRefuseReasonItem> selectSubGroupByType() {
        return cfRefuseReasonItemDao.selectSubGroupByType();
    }

    @Override
    public List<CfRefuseReasonItem> selectByIds(Set set) {
        if (CollectionUtils.isEmpty(set))
            return Collections.emptyList();
        List<CfRefuseReasonItem> cfRefuseReasonItems = cfRefuseReasonItemDao.selectByIds(set);
        return cfRefuseReasonItems != null ? cfRefuseReasonItems : Collections.emptyList();
    }

    @Override
    public List<CfRefuseReasonItem> selectByType(int type) {
        return cfRefuseReasonItemDao.selectByType(type);
    }

    @Override
    public int insertReasonItem(CfRefuseReasonItem reasonItem) {
        return cfRefuseReasonItemDao.insertReasonItem(reasonItem);
    }

    @Override
    public void fillReasonEntityItem(List<CfRefuseReasonEntity> entityList) {

        if (CollectionUtils.isEmpty(entityList)) {
            return;
        }

        Set<Integer> allItemIds = Sets.newHashSet();
        for (CfRefuseReasonEntity entity : entityList) {
            allItemIds.addAll(reasonCommonBiz.getIdListSplitterByComma(entity.getItemIds()));
        }
        List<CfRefuseReasonItem> allReasonItems = cfRefuseReasonItemDao.selectByIds(allItemIds);
        Map<Integer, String> itemIdToContents = Maps.newHashMap();
        for (CfRefuseReasonItem reasonItem : allReasonItems) {
            itemIdToContents.put(reasonItem.getId(), reasonItem.getContent());
        }

        for (CfRefuseReasonEntity entity : entityList) {
            List<String> itemList = Lists.newArrayList();
            List<Integer> itemIds = reasonCommonBiz.getIdListSplitterByComma(entity.getItemIds());
            for (Integer itemId : itemIds) {
                itemList.add(itemIdToContents.get(itemId));
            }
            entity.setRefuseReasonItems(itemList);
        }
    }

    @Override
    public List<CfRefuseReasonItem> selectByContent(String content) {
        if (StringUtils.isBlank(content)) {
            return Lists.newArrayList();
        }

        return cfRefuseReasonItemDao.selectByContent(content);
    }


    @Override
    public Map<Integer, CfRefuseReasonItem> selectMappingByIds(List<Integer> ids) {

        Map<Integer, CfRefuseReasonItem> result = Maps.newHashMap();
        if (CollectionUtils.isEmpty(ids)) {
            return result;
        }

        List<CfRefuseReasonItem> reasonItems = cfRefuseReasonItemDao.selectByIds(ids);

        for (CfRefuseReasonItem item : reasonItems) {
            result.put(item.getId(), item);
        }

        return result;
    }
}





















