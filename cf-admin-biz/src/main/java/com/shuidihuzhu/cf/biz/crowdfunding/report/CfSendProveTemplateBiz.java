package com.shuidihuzhu.cf.biz.crowdfunding.report;

import com.shuidihuzhu.cf.model.crowdfunding.CfSendProveTemplate;

import java.util.List;

public interface CfSendProveTemplateBiz {

    List<CfSendProveTemplate> findByCaseIdAndAuditStatus(int caseId, List<Integer> auditStatus);

    int batchInsert(List<CfSendProveTemplate> cfSendProveTemplates);

    List<CfSendProveTemplate> findByCaseIdAndProveId(int caseId, long proveId);

    int updateAuditStatus(int caseId, long proveId, long templateId, int auditStatus);

    int updateAllAuditStatus(int caseId, long proveId, int auditStatus);

    CfSendProveTemplate findByCaseIdAndProveIdAndTemplateId(int caseId, long proveId, long actionId, long templateId);

    int insertOne(CfSendProveTemplate cfSendProveTemplate);

    int updateAuditStatusAndContent(int caseId, long proveId, long templateId, int auditStatus, String content);


}
