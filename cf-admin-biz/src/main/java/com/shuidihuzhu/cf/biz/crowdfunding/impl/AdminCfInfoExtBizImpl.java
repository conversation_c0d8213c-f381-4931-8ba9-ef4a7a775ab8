package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.client.apipure.feign.dp.DpClewFeignClient;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfInfoExtDao;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.message.AdminMsgClientService;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.search.client.CfSearchClient;
import com.shuidihuzhu.client.cf.search.model.EsCaseLabelColum;
import com.shuidihuzhu.common.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class AdminCfInfoExtBizImpl implements AdminCfInfoExtBiz {

	@Autowired
	private AdminCfInfoExtDao cfInfoExtDao;

	@Autowired
	private AdminMsgClientService msgClientService;
	@Autowired
	private ShuidiCipher shuidiCipher;
	@Resource
	private DpClewFeignClient dpClewFeignClient;
	@Resource
	private CfSearchClient cfSearchClient;

	@Override
	public int updateDealWithStatus(String infoId, int status) {
		if(StringUtils.isBlank(infoId)){
			return -1;
		}
		return this.cfInfoExtDao.updateDealWithStatus(infoId, status);
	}

	@Override
	public int updateUserRefund(String infoUuid, int days) throws Exception {
		String refundEndTime = DateUtil.getNextDayStartTime(DateUtil.getCurrentDateStr(), days);
		return this.cfInfoExtDao.updateUserRefund(infoUuid, refundEndTime);
	}

	@Override
	public int updateCryptoRegisterMobile(String infoUuid, String cryptoRegisterMobile) {
		if(StringUtils.isEmpty(infoUuid) || StringUtils.isEmpty(cryptoRegisterMobile)){
			return 0;
		}
		return cfInfoExtDao.updateCryptoRegisterMobile(infoUuid, cryptoRegisterMobile);
	}

	@Override
	public int updateUserRefund(String infoUuid, String refundEndTime) throws Exception {
		if(StringUtils.isBlank(infoUuid) || StringUtils.isBlank(refundEndTime)) {
			return -1;
		}
		return this.cfInfoExtDao.updateUserRefund(infoUuid, refundEndTime);
	}

    @Override
    public int insertList(List<String> list) {
        return cfInfoExtDao.insertList(list);
    }

    @Override
    public List<CfInfoExt> selectByInfoUuidList(List<String> list) {
	    if (CollectionUtils.isEmpty(list)) {
	        return Lists.newArrayList();
        }
        return cfInfoExtDao.selectByInfoUuidList(list);
    }

	@Override
	public int updateSuggestStop(String infoUuid, boolean isSuggestStop) {
		return this.cfInfoExtDao.updateSuggestStop(infoUuid, isSuggestStop ? 1 : 0);
	}

	@Override
	public OpResult updateTransferStatusAndFinishStatus(String infoUuid) {
		int result = this.cfInfoExtDao.updateTransferStatusAndFinishStatus(infoUuid);
		return result>0?OpResult.createSucResult():OpResult.createFailResult(CfErrorCode.SYSTEM_ERROR);
	}

	@Override
	public  CfInfoExt selectByInfoUuidFromMaster(String infoUuid) {
		if (StringUtils.isEmpty(infoUuid)) {
			return null;
		}
		return cfInfoExtDao.selectByInfoUuidFromMaster(infoUuid);
	}
	@Override
	public List<CfInfoExt> getListByInfoUuids(List<String> infoUuids) {
		if (org.apache.commons.collections4.CollectionUtils.isEmpty(infoUuids)) {
			return Collections.emptyList();
		}
		else {
			List<CfInfoExt> cfInfoExt = new ArrayList<>();
			List<List<String>> splitList = Lists.partition(infoUuids, 500);//分割
			for (List<String> split : splitList) {//分批查询
				List<CfInfoExt> exts = cfInfoExtDao.getListByUuids(split);
				if (exts != null && exts.size() > 0) {
					cfInfoExt.addAll(exts);
				}
			}
			return cfInfoExt;
		}
	}
	@Override
	public CfInfoExt getByInfoUuid(String infoUuid) {
		if (StringUtils.isBlank(infoUuid)) {
			return null;
		}

		return this.cfInfoExtDao.getByInfoUuid(infoUuid);
	}
	@Override
	public int updateFromType(String infoId, int type) {
		if (StringUtils.isBlank(infoId)) {
			return -1;
		}
		return this.cfInfoExtDao.updateFromType(infoId, type);

	}
	@Override
	public Map<String, CfInfoExt> getMapByInfoUuids(List<String> infoUuids) {
		List<CfInfoExt> cfInfoExts = getListByInfoUuids(infoUuids);
		Map<String, CfInfoExt> map = Maps.newHashMap();
		if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(cfInfoExts)) {
			for (CfInfoExt cfInfoExt : cfInfoExts) {
				map.put(cfInfoExt.getInfoUuid(), cfInfoExt);
			}
		}
		return map;
	}
	@Override
	public boolean updateApproveStatus(CrowdfundingInfo info, FirstApproveStatusEnum statusEnum) {
		if(info == null || StringUtils.isEmpty(info.getInfoId()) || statusEnum == null){
			return false;
		}

		boolean result =  cfInfoExtDao.updateFirstApproveStatus(info.getInfoId(), statusEnum.getCode(),
				statusEnum == FirstApproveStatusEnum.APPLY_SUCCESS ? new Date() : null) > 0;

		if (statusEnum == FirstApproveStatusEnum.APPLY_SUCCESS) {
			Map<String, Object> caseTagParam = Map.of(
					EsCaseLabelColum.ID, info.getId(),
					EsCaseLabelColum.FIRST_APPROVE_TIME, new Date()
			);
			cfSearchClient.updateCfCase(caseTagParam);
		}

		msgClientService.sendCasePatientChangeMsg(info.getId());

		dpClewFeignClient.updateFirstApproveStatus(info.getId(), statusEnum.getCode());
		return result;
	}

	@Override
	public boolean updateFirstApproveTime(String infoUuid) {
		return cfInfoExtDao.updateFirstApproveTime(infoUuid) > 0;
	}

	@Override
	public void updateFinishStr(String caseUUid, String finishStr) {
		cfInfoExtDao.updateFinishStr(caseUUid,finishStr);
	}

	@Override
	public CfInfoExt getByCaseId(int caseId) {
		return this.cfInfoExtDao.getByCaseId(caseId);
	}

	@Override
	public int updateFinishStatus(String infoUuid, int finishStatus) {
		if (StringUtils.isBlank(infoUuid)) {
			return 0;
		}

		return cfInfoExtDao.updateFinishStatus(infoUuid, finishStatus);
	}

	@Override
	public int updateFinishStatusByCaseId(int caseId, int finishStatus) {
		if (caseId == 0) {
			return 0;
		}

		return cfInfoExtDao.updateFinishStatusByCaseId(caseId, finishStatus);
	}


	@Override
	public String getRegisterMobile(CfInfoExt cfInfoExt) {
		try {
			return shuidiCipher.decrypt(cfInfoExt.getCryptoRegisterMobile());
		} catch (Exception e) {
			log.error("", e);
			return "";
		}
	}

	@Override
	public Map<Integer, CfInfoExt> getMapByCaseIds(List<Integer> caseIds) {

		Map<Integer, CfInfoExt> caseMappings = Maps.newHashMap();
		if (CollectionUtils.isEmpty(caseIds)) {
			return caseMappings;
		}

		List<CfInfoExt> infoList = cfInfoExtDao.getListByCaseIds(caseIds);

		for (CfInfoExt infoExt : infoList) {
			caseMappings.put(infoExt.getCaseId(), infoExt);
		}

		return caseMappings;
	}

	@Override
	public int updateNoHandlingFeeByInfoUuid(String infoUuid, int noHandlingFee) {
		if (StringUtils.isBlank(infoUuid)) {
			return 0;
		}

		return cfInfoExtDao.updateNoHandlingFeeByInfoUuid(infoUuid, noHandlingFee);
	}
}
