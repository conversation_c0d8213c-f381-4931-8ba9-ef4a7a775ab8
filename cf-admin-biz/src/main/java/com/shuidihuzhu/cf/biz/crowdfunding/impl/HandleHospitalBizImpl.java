package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.HandleHospitalBiz;
import com.shuidihuzhu.cf.dao.stat.HandleHospitalDao;
import com.shuidihuzhu.cf.vo.crowdfunding.HandleHospital;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by ahrievil on 2017/5/5.
 */
@Service
public class HandleHospitalBizImpl implements HandleHospitalBiz {
    @Autowired
    private HandleHospitalDao handleHospitalDao;
    @Override
    public int insert(HandleHospital handleHospital) {
        return handleHospitalDao.insert(handleHospital);
    }

    @Override
    public int insertInit(List<HandleHospital> handleHospitals) {
        return handleHospitalDao.insertInit(handleHospitals);
    }

    @Override
    public List<HandleHospital> selectAll(int offSet, int count) {
        return handleHospitalDao.selectAll(offSet, count);
    }

    @Override
    public int update(HandleHospital handleHospital) {
        return handleHospitalDao.update(handleHospital);
    }

    @Override
    public int selectMax() {
        return handleHospitalDao.selectMax();
    }

    @Override
    public int updateValid(int disable, int id) {
        return handleHospitalDao.updateValid(disable, id);
    }
}
