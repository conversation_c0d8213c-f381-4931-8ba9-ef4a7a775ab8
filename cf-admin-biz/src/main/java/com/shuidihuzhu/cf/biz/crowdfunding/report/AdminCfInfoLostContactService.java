package com.shuidihuzhu.cf.biz.crowdfunding.report;

import com.shuidihuzhu.cf.domain.cf.CfInfoLostContactDO;
import com.shuidihuzhu.cf.response.OpResult;

/**
 * <AUTHOR>
 * @date 2018-07-26  17:24
 */
public interface AdminCfInfoLostContactService {

    CfInfoLostContactDO getLastByInfoUuid(String infoUuid);

    boolean insert(CfInfoLostContactDO cfInfoLostContactDO);

    boolean hasLost(String infoUuid);

    OpResult update(int userId, String infoUuid, boolean hasLost, String reason);

    boolean val2HasLost (int lost);

    /**
     * 是否已失联 1为失联 2为未失联
     * @param hasLost
     * @return
     */
    int hasLost2Val (Boolean hasLost);
}
