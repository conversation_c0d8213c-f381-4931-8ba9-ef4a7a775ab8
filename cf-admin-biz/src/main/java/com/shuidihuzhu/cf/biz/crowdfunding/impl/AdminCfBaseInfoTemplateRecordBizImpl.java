package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfBaseInfoTemplateRecordBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfBaseInfoTemplateRecordDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplateRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> Ahrievil
 * @date : 2018/6/27 19:29
 */
@Service
public class AdminCfBaseInfoTemplateRecordBizImpl implements AdminCfBaseInfoTemplateRecordBiz {

    @Autowired
    private AdminCfBaseInfoTemplateRecordDao adminCfBaseInfoTemplateRecordDao;

    @Override
    public List<CfBaseInfoTemplateRecord> selectByParam(CfBaseInfoTemplateRecord param) {
        if (param == null) {
            return Lists.newArrayList();
        }

        return adminCfBaseInfoTemplateRecordDao.selectByParam(param);
    }

}
