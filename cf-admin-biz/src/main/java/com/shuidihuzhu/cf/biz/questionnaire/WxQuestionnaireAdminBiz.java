package com.shuidihuzhu.cf.biz.questionnaire;

import com.shuidihuzhu.cf.model.common.BaseResult;
import com.shuidihuzhu.cf.model.questionnaire.*;
import com.shuidihuzhu.cf.vo.questionnaire.WxSdTagValueAdminVo;
import com.shuidihuzhu.cf.vo.questionnaire.WxSdTagAdminVo;
import com.shuidihuzhu.pay.common.model.PageRequest;
import com.shuidihuzhu.pay.common.model.PageResponse;

import java.util.List;

/**
 * Created by mhk on 2018/11/8.
 */
public interface WxQuestionnaireAdminBiz {

    /**
     *  查看问卷
     * @return
     */
    WxQuestionnaire getWxQuestionnaire(int id);

    /**
     *  添加问卷
     * @return
     */
    BaseResult<Boolean> addWxQuestionnaire(String name, String title, List<WxQuestion> wxQuestions, String createBy);

    /**
     *  编辑问卷
     * @return
     */
    BaseResult<Boolean> editWxQuestionnaire(int id, String name, String title, List<WxQuestion> wxQuestions, String updateBy);


    /**
     * 获取所有标签
     */
    List<WxSdTagAdminVo> getAllTagList();

    /**
     * 获取tag值
     * @return
     */
    List<WxSdTagValueAdminVo> getTagValues(int tagId);

    /**
     *  分页查询问卷
     * @return
     */
    PageResponse getWxQuestionnairePage(String name, String title, String tagName, String createBy, PageRequest pageRequest);
}
