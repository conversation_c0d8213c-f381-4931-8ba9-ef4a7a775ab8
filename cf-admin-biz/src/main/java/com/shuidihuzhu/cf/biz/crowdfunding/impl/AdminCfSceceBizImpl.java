package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfSceceBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfCaseSceneDao;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfSceneDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfCaseScene;
import com.shuidihuzhu.cf.model.crowdfunding.CfScene;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by wangsf on 17/4/24.
 */
@Service
public class AdminCfSceceBizImpl implements AdminCfSceceBiz {

	@Autowired
	private AdminCfCaseSceneDao cfCaseSceneDao;

	@Autowired
	private AdminCfSceneDao cfSceneDao;

	@Override
	public List<CfScene> listScenes() {
		return this.cfSceneDao.listScenes();
	}

	@Override
	public CfScene getScene(int sceneId) {
		if(sceneId <= 0){
			return null;
		}

		return this.cfSceneDao.queryById(sceneId);
	}

	@Override
	public CfCaseScene getByInfoUuid(String infoUuid) {
		if (StringUtils.isBlank(infoUuid)) {
			return null;
		}
		return this.cfCaseSceneDao.getByInfoUuid(infoUuid);
	}

	@Override
	public int insertOrUpdateCaseScene(CfCaseScene caseScene) {
		if(caseScene == null || StringUtils.isBlank(caseScene.getInfoUuid())
				|| caseScene.getSceneId() <= 0 || StringUtils.isBlank(caseScene.getSceneName())) {
			return -1;
		}

		return this.cfCaseSceneDao.insertOrUpdate(caseScene);
	}
}
