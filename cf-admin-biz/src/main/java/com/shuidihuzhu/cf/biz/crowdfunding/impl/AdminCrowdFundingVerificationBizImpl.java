package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdFundingVerificationBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdFundingVerificationDao;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdFundingVerificationSlaveDao;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification;
import com.shuidihuzhu.cf.vo.crowdfunding.CrowdfundingfundingVerificationVo;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.common.web.util.admin.BasicExample;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/10/18.
 */
@Service
public class AdminCrowdFundingVerificationBizImpl implements AdminCrowdFundingVerificationBiz {


    @Autowired
    private AdminCrowdFundingVerificationSlaveDao crowdFundingVerificationDao;
    @Autowired
    private AdminCrowdFundingVerificationDao adminCrowdFundingVerificationDao;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Override
    public List<CrowdFundingVerification> selectByPage(BasicExample basicExample, int current, int pageSize) {
        PageHelper.startPage(current, pageSize);
        List<CrowdFundingVerification> verifications = crowdFundingVerificationDao.selectByPage(basicExample);

        if(verifications!=null && verifications.size()>0) {
            for (CrowdFundingVerification verification:verifications) {
                if(!Strings.isNullOrEmpty(verification.getEncryptMobile())){
                    verification.setMobile(shuidiCipher.decrypt(verification.getEncryptMobile()));
                }else {
                    verification.setMobile("");
                }
            }
        }

        return verifications;
    }

    @Override
    public CrowdFundingVerification getById(long id) {
        CrowdFundingVerification crowdFundingVerification = crowdFundingVerificationDao.getById(id);

        if(crowdFundingVerification!=null&&!Strings.isNullOrEmpty(crowdFundingVerification.getEncryptMobile())){
            crowdFundingVerification.setMobile(shuidiCipher.decrypt(crowdFundingVerification.getEncryptMobile()));
        }else if(crowdFundingVerification!=null){
            crowdFundingVerification.setMobile("");
        }

        return crowdFundingVerification;
    }

    @Override
    public Map<String, Integer> getVerify(List<String> crowdfundingInfoIds) {
        if (CollectionUtils.isEmpty(crowdfundingInfoIds)) {
            return new HashMap<>();
        }
        List<CrowdfundingfundingVerificationVo> verify =new ArrayList<>();
        List<List<String>> splitList = Lists.partition(crowdfundingInfoIds, 500);//分割
        for (List<String> split : splitList) {//分批查询
            List<CrowdfundingfundingVerificationVo> vos = adminCrowdFundingVerificationDao.getVerify(split);
            if (vos != null && vos.size() > 0) {
                verify.addAll(vos);
            }
        }
        Map<String, Integer> verifyMap = new HashMap<>();
        for (CrowdfundingfundingVerificationVo verificationVo : verify) {
            int count = verificationVo.getCount();
            String crowdFundingInfoId = verificationVo.getCrowdFundingInfoId();
            verifyMap.put(crowdFundingInfoId, count);
        }
        return verifyMap;
    }

    @Override
    public boolean isVerifySomeTimes(long userId, Date gtDate, int amount) {
        List<Long> ids = crowdFundingVerificationDao.listIdByVerifyUserIdAndCreateTimeWithLimit(userId, gtDate, amount);
        return ids.size() >= amount;
    }

    @Override
    public List<CrowdFundingVerification> getListByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return crowdFundingVerificationDao.getListByIds(ids);
    }

    @Override
    public int updateValid(int valid, int id) {
        if (id <= 0) {
            return 0;
        }
        return adminCrowdFundingVerificationDao.updateValid(valid, id);
    }

}
