package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.ICfReportCallRecordService;
import com.shuidihuzhu.cf.dao.crowdfunding.CfReportCallRecordDAO;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportCallRecordDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2019/12/17 下午3:22
 * @desc
 */
@Service
public class CfReportCallRecordServiceImpl implements ICfReportCallRecordService {

    @Autowired
    private CfReportCallRecordDAO cfReportCallRecordDAO;
    @Override
    public int insert(CfReportCallRecordDO recordDO) {
        if(Objects.isNull(recordDO)){
            return 0;
        }
        return cfReportCallRecordDAO.insert(recordDO);
    }

    @Override
    public List<CfReportCallRecordDO> query(int caseId, int reportId, long addId) {
        if(caseId <= 0 || addId <= 0){
            return Lists.newArrayList();
        }
        return cfReportCallRecordDAO.query(caseId, reportId, addId);
    }
}
