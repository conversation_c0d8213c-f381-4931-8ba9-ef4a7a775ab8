package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.CfBlackListRecordBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfBlackListRecordDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfBlackListRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by Ahrievil on 2017/11/5
 */
@Service
public class CfBlackListRecordBizImpl implements CfBlackListRecordBiz {

    @Autowired
    private CfBlackListRecordDao cfBlackListRecordDao;

    @Override
    public int insertOne(CfBlackListRecord cfBlackListRecord) {
        return cfBlackListRecordDao.insertOne(cfBlackListRecord);
    }

    @Override
    public List<CfBlackListRecord> selectByUserId(int userId) {
        return cfBlackListRecordDao.selectByUserId(userId);
    }
}
