package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseUseSceneRankMappingBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfRefuseReasonEntityDao;
import com.shuidihuzhu.cf.dao.crowdfunding.CfRefuseReasonUseSceneRankMappingDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditOperationItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CfRefuseUseSceneRankMappingBizlmpl implements CfRefuseUseSceneRankMappingBiz {

    @Autowired
    private CfRefuseReasonEntityDao cfRefuseReasonEntityDao;
    @Autowired
    private CfRefuseReasonUseSceneRankMappingDao useSceneRankMappingDao;

    @Override
    public void addUseSceneRankMapping(CfRefuseReasonEntity reasonEntity, List<Integer> addSceneIds, boolean needMaxRank) {
        if (CollectionUtils.isEmpty(addSceneIds)) {
            return;
        }

        List<CfRefuseReasonEntity.CfRefuseReasonUseSceneRankMapping> rankMappingList = Lists.newArrayList();
        for (Integer sceneId : addSceneIds) {
            CfRefuseReasonEntity.CfRefuseReasonUseSceneRankMapping currMapping = new
                    CfRefuseReasonEntity.CfRefuseReasonUseSceneRankMapping();
            currMapping.setReasonEntityId(reasonEntity.getId());
            currMapping.setUseScene(sceneId);
            currMapping.setRank(needMaxRank ? maxRankByTagAndUseScene(reasonEntity.getTagId(), sceneId) : 1);
            rankMappingList.add(currMapping);
        }

        useSceneRankMappingDao.addUseSceneRankMappingList(rankMappingList);
    }

    @Override
    public void deleteUseSceneRankMapping(List<Integer> mappingIds) {
        if (CollectionUtils.isNotEmpty(mappingIds)) {
            useSceneRankMappingDao.updateDeleteOrRandByIds(mappingIds, 1, null);
        }
    }

    // 启用的时候 将位置排在最后
    @Override
    public void updateUseSceneRankMapping(CfRefuseReasonEntity entity, int deleteStatus) {


        if (deleteStatus != InitialAuditOperationItem.RejectOperation.ENABLE.getCode()) {
            return;
        }

        List<CfRefuseReasonEntity.CfRefuseReasonUseSceneRankMapping> allValidRankMapping = useSceneRankMappingDao
                .selectByEntityId(entity.getId());

        for (CfRefuseReasonEntity.CfRefuseReasonUseSceneRankMapping rankMapping : allValidRankMapping) {

            int rank =  maxRankByTagAndUseScene(entity.getTagId(), rankMapping.getUseScene());
            log.info("修改使用场景的优先级 id:{}, rank:{} ", rankMapping.getId(), rank);
                    useSceneRankMappingDao.updateDeleteOrRandByIds(Lists.newArrayList(rankMapping.getId()), null,
            rank);
        }
    }

    // 得到某个理由分类和场景下 排最后一个数据
    private int maxRankByTagAndUseScene(int tagId, int useScene) {
        List<Integer> entityIds = cfRefuseReasonEntityDao.selectByTagIdAndDeleteStatus(tagId,
                InitialAuditOperationItem.RejectOperation.ENABLE.getCode()).stream()
                .map(CfRefuseReasonEntity::getId).collect(Collectors.toList());

        int maxRank = 0;
        List<CfRefuseReasonEntity.CfRefuseReasonUseSceneRankMapping> useSceneRanks = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(entityIds)) {
            useSceneRanks = useSceneRankMappingDao.selectByEntityIdsAndUseScene(entityIds, useScene);
        }

        for (CfRefuseReasonEntity.CfRefuseReasonUseSceneRankMapping rankMapping : useSceneRanks) {
            if (rankMapping.getRank() > maxRank) {
                maxRank = rankMapping.getRank();
            }
        }

        return maxRank + 1;
    }

    @Override
    public List<CfRefuseReasonEntity.CfRefuseReasonUseSceneRankMapping> selectByEntityId(int entityId) {
        return useSceneRankMappingDao.selectByEntityId(entityId);
    }

    @Override
    public List<CfRefuseReasonEntity.CfRefuseReasonUseSceneRankMapping> selectByEntityIdsAndUseScene(List<Integer> entityIds, int useScene) {
        if (CollectionUtils.isEmpty(entityIds)) {
            return Lists.newArrayList();
        }

        return useSceneRankMappingDao.selectByEntityIdsAndUseScene(entityIds, useScene);
    }

    @Override
    public void swapRankMapping(int upEntityId, int downEntityId, int useScene) {
        List<CfRefuseReasonEntity.CfRefuseReasonUseSceneRankMapping> currentRankMappings =
                selectByEntityIdsAndUseScene(Lists.newArrayList(upEntityId, downEntityId), useScene);
        if (currentRankMappings.size() != 2) {
            throw new RuntimeException("必须是相同的使用场景才能上移或下移");
        }

        useSceneRankMappingDao.updateDeleteOrRandByIds(Lists.newArrayList(currentRankMappings.get(0).getId()),
                null, currentRankMappings.get(1).getRank());

        useSceneRankMappingDao.updateDeleteOrRandByIds(Lists.newArrayList(currentRankMappings.get(1).getId()),
                null, currentRankMappings.get(0).getRank());
    }

    @Override
    public List<CfRefuseReasonEntity> filterUseSceneSortEntityList(List<CfRefuseReasonEntity> entityList, int useScene) {

        if (CollectionUtils.isEmpty(entityList) || useScene == 0) {
            return entityList;
        }

        List<Integer> entityIds = entityList.stream().map(CfRefuseReasonEntity::getId).collect(Collectors.toList());
        List<CfRefuseReasonEntity.CfRefuseReasonUseSceneRankMapping> rankMappingList =
                selectByEntityIdsAndUseScene(entityIds, useScene);

        Map<Integer, CfRefuseReasonEntity> entityIdMap = Maps.newHashMap();
        for (CfRefuseReasonEntity entity : entityList) {
            entityIdMap.put(entity.getId(), entity);
        }

        rankMappingList = rankMappingList.stream().sorted(Comparator.comparing(CfRefuseReasonEntity
                .CfRefuseReasonUseSceneRankMapping::getRank)).collect(Collectors.toList());
        List<CfRefuseReasonEntity> hasSortedEntityList = Lists.newArrayList();
        for (CfRefuseReasonEntity.CfRefuseReasonUseSceneRankMapping rankMapping : rankMappingList) {
            hasSortedEntityList.add(entityIdMap.get(rankMapping.getReasonEntityId()));
        }

        return hasSortedEntityList;
    }

    @Override
    public void fillReasonEntityUseSceneIds(List<CfRefuseReasonEntity> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return;
        }

        List<Integer> entityIds = entityList.stream().map(CfRefuseReasonEntity::getId).collect(Collectors.toList());
        List<CfRefuseReasonEntity.CfRefuseReasonUseSceneRankMapping> sceneRankMappings = useSceneRankMappingDao
                .selectByEntityIdsAndUseScene(entityIds, null);

        Map<Integer, List<Integer>> entityIdToUseSceneMap = Maps.newHashMap();
        for (CfRefuseReasonEntity.CfRefuseReasonUseSceneRankMapping rankMapping : sceneRankMappings) {

            List<Integer> useSceneIds = entityIdToUseSceneMap.get(rankMapping.getReasonEntityId());
            if (useSceneIds == null) {
                useSceneIds = Lists.newArrayList();
                entityIdToUseSceneMap.put(rankMapping.getReasonEntityId(), useSceneIds);
            }
            useSceneIds.add(rankMapping.getUseScene());
        }

        for (CfRefuseReasonEntity reasonEntity : entityList) {
            reasonEntity.setUseSceneIds(entityIdToUseSceneMap.get(reasonEntity.getId()));
        }

    }
}


















