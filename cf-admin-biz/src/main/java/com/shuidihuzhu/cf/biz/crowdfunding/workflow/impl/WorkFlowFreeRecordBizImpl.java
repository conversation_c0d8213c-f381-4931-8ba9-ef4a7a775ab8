package com.shuidihuzhu.cf.biz.crowdfunding.workflow.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.workflow.WorkFlowFreeRecordBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.WorkFlowFreeRecordDao;
import com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowFreeRecord;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-02-19 17:16
 **/
@Service
public class WorkFlowFreeRecordBizImpl implements WorkFlowFreeRecordBiz {
    @Autowired
    private WorkFlowFreeRecordDao flowFreeRecordDao;

    @Override
    public void batchAdd(List<WorkFlowFreeRecord> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        flowFreeRecordDao.batchAdd(records);
    }

    @Override
    public int count(List<Long> userIds, Integer orgId, Date startTime, Date endTime) {
        return flowFreeRecordDao.count(userIds, orgId, startTime, endTime);
    }
}
