package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminGoodsGearBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminGoodsGearDao;
import com.shuidihuzhu.cf.model.goods.GoodsGear;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by ahrievil on 2017/6/26.
 */
@Service
public class AdminGoodsGearBizImpl implements AdminGoodsGearBiz {
    @Autowired
    private AdminGoodsGearDao goodsGearDao;

    @Override
    public int updateByGoodsGear(GoodsGear goodsGear) {
        return goodsGearDao.updateByGoodsGear(goodsGear);
    }

    @Override
    public int updateValid(int valid, int id) {
        return goodsGearDao.updateValid(valid, id);
    }
}
