package com.shuidihuzhu.cf.biz.risk.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.admin.util.AdminDateUtil;
import com.shuidihuzhu.cf.admin.util.TianRuCallRecordUtil;
import com.shuidihuzhu.cf.biz.call.CallRecordBiz;
import com.shuidihuzhu.cf.biz.risk.IUserBehaviorService;
import com.shuidihuzhu.cf.call.CallOutModel;
import com.shuidihuzhu.cf.delegate.SeaAccountDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.UserBehaviorEnum;
import com.shuidihuzhu.cf.model.crowdfunding.AdminUserBehaviorDetail;
import com.shuidihuzhu.cf.model.crowdfunding.UserInfoDetail;
import com.shuidihuzhu.cf.util.crowdfunding.TimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @time 2019/10/12 下午6:03
 * @desc 平台主动外呼
 */
@Service
@Slf4j
public class UserBehaviorPlatformCallServiceImpl implements IUserBehaviorService {

    @Autowired
    SeaAccountDelegate seaAccountDelegate;

    @Autowired
    private CallRecordBiz callRecordBiz;

    @Override
    public UserBehaviorEnum getBehavior() {
        return UserBehaviorEnum.PLATFORM_CALL;
    }

    @Override
    public List<AdminUserBehaviorDetail> buildBehaviorDetail(String mobile, long userId, String name, String identity, UserInfoModel userInfo, UserBehaviorEnum behaviorEnum) {

        UserInfoDetail userInfoDetail = new UserInfoDetail();
        userInfoDetail.setMobile(mobile);


        List<CallOutModel> callOutModelList = callRecordBiz.getAllCallOut(mobile);
        if (CollectionUtils.isEmpty(callOutModelList)) {
            return Lists.newArrayList();
        }


        List<AdminUserBehaviorDetail> callRecords = Lists.newArrayList();
        for (CallOutModel callOutModel : callOutModelList) {
            String callDuration = callOutModel.getCallDuration();
            long duration = Optional.ofNullable(callDuration).map(Long::valueOf).orElse(0L);
            StringBuilder sb = new StringBuilder();
            boolean callStatus = duration > 0;

            //呼出时间
            String callTime = callOutModel.getCallTime();
            //挂断时间
            String hangOnTime = "";
            if (StringUtils.isNotEmpty(callTime)) {
                hangOnTime = AdminDateUtil.formatter.parseDateTime(callTime).plusSeconds((int) duration).toString(AdminDateUtil.formatter);
            }

            //处理人的组织-名称
            String seatNumber = callOutModel.getSeatNumber();
            String operatorInfo = seaAccountDelegate.obtainOperatorNameAndOrg(seatNumber);

            sb.append("呼通与否:").append(callStatus ? "是" : "否").append(REDEX);
            sb.append("呼叫开始时间:").append(callTime).append(REDEX);
            sb.append("呼叫挂断时间:").append(hangOnTime).append(REDEX);
            sb.append("呼通时间:").append(TimeUtils.timeFormat((int) duration)).append(REDEX);
            sb.append("处理人:").append(operatorInfo);

            AdminUserBehaviorDetail callRecord = new AdminUserBehaviorDetail();
            callRecord.setTime(callOutModel.getCreateTime());
            callRecord.setBehaviorType(UserBehaviorEnum.PLATFORM_CALL.getKey());
            callRecord.setUrl(Lists.newArrayList());
            callRecord.setUserInfoDetail(userInfoDetail);
            if (StringUtils.isNotBlank(callOutModel.getRecordUrl())) {
                callRecord.setSubBehavoirDetails(Lists.newArrayList(TianRuCallRecordUtil.callRecordUrlInfo(callOutModel.getRecordUrl())));
            }
            callRecord.setBehavoir(Lists.newArrayList(sb.toString().split(REDEX)));

            callRecords.add(callRecord);
        }
        return callRecords;
    }

}
