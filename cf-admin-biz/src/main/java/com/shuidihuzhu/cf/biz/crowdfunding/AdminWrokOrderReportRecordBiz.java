package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderReportRecord;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderDataVo;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/10.
 */
public interface AdminWrokOrderReportRecordBiz {
    int insertAdminWorkOrderReportRecord(AdminWorkOrderReportRecord adminWorkOrderReportRecord);

    int getFollowCountByTime(String startTime, String endTime);


    List<AdminWorkOrderDataVo> getFollowCountByUserIds(int orderType, int orderTask, String startTime, String endTime, List<Integer> userIds);

}
