package com.shuidihuzhu.cf.biz.risk.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingOrderBiz;
import com.shuidihuzhu.cf.biz.risk.ISimpleExportService;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.finance.IFinanceDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.CfDrawCashConstant;
import com.shuidihuzhu.cf.finance.enums.NewCfCapitalEnum;
import com.shuidihuzhu.cf.finance.model.CfCapitalAccountRecord;
import com.shuidihuzhu.cf.finance.model.drawcash.AdminCfDrawCash;
import com.shuidihuzhu.cf.finance.model.vo.CfDrawCashApplyVo;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.finance.model.drawcash.CfDrawCash;
import com.shuidihuzhu.cf.model.crowdfunding.CfCapitalAccount;
import com.shuidihuzhu.cf.model.crowdfunding.CfDrawCashApplyRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.MoneyUtil;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

/**
 * Created by sven on 2019/6/28.
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MoneySimpleExportServiceImpl implements ISimpleExportService {
    private final static DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd HH:mm:ss");

    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;

    private final static String TARGET_AMOUNT = "筹款目标金额(元)";
    private final static String DRAW_CASH_REASON = "所需金额说明";
    private final static String DRAW_LAUNCH_AMOUNT = "已打款金额";
    private final static String REFUND_AMOUNT = "已整体退款金额";
    private final static String SINGLE_REFUND_AMOUNT = "个人退款金额";
    private final static String DRAW_TYPE = "打款方式";
    private final static String DONATE_AMOUNT = "捐款总金额";
    private final static String DONATE_PERSON_COUNT = "捐款总人次";
    private final static String DONATE_COUNT = "捐款总次数";


    @Autowired
    private IFinanceDelegate financeDelegate;

    @Autowired
    private AdminCrowdfundingOrderBiz adminCrowdfundingOrderBiz;

    @Override
    public Map<String, String> getDetail(CfInfoSimpleModel cfInfoSimpleModel) {

        CrowdfundingInfo crowdfundingInfo = crowdfundingDelegate.getFundingInfoById(cfInfoSimpleModel.getId());

        List<CrowdfundingOrder> orderList = Lists.newArrayList();
        List<CrowdfundingOrder> orderListAll = Lists.newArrayList();
        long donateAmount = 0;
        long donateCount = 0;
        long donatePersonCount = 0;
        int offset = 0;
        int limit = 200;
        do {
            orderList = adminCrowdfundingOrderBiz.getListByInfoId(cfInfoSimpleModel.getId(), offset, limit);
            offset += limit;
            if (CollectionUtils.isEmpty(orderList)) {
                break;
            }
            orderListAll.addAll(orderList);
        } while (!CollectionUtils.isEmpty(orderList));
        if (CollectionUtils.isNotEmpty(orderListAll)) {
            donateAmount += orderListAll.stream().map(CrowdfundingOrder::getAmount).reduce(Integer::sum).get();
        }
        donateCount = orderListAll.size();
        Set<Long> userIds = orderListAll.stream().map(CrowdfundingOrder::getUserId).collect(Collectors.toSet());
        donatePersonCount = userIds.size();

        Map<String,String> map = Maps.newHashMap();
        map.put(TARGET_AMOUNT, ""+crowdfundingInfo.getTargetAmount()/100);
        map.put(DRAW_CASH_REASON, getDrawCashApplyReason(cfInfoSimpleModel.getId()));
        map.put(DRAW_LAUNCH_AMOUNT, getHasDrawAmount(cfInfoSimpleModel.getInfoId()));
        map.put(REFUND_AMOUNT, getHasRefundAmount(cfInfoSimpleModel.getInfoId()));
        map.put(SINGLE_REFUND_AMOUNT, getSingleRefundAmount(cfInfoSimpleModel.getInfoId()));
        map.put(DRAW_TYPE, getDrawType(cfInfoSimpleModel.getId()));
        map.put(DONATE_AMOUNT, MoneyUtil.buildBalance(donateAmount));
        map.put(DONATE_PERSON_COUNT, donatePersonCount + "");
        map.put(DONATE_COUNT, donateCount + "");

        return map;
    }

    // 打款金额
    private String getHasDrawAmount(String infoUuid) {
        String result = "0";

        Response<List<CfCapitalAccountRecord>> cfCapitalAccountRecordsResponse =
                financeDelegate.getByInfoUuidAndBizType(infoUuid, NewCfCapitalEnum.BizType.DRAW_CASH.getCode());
        if (cfCapitalAccountRecordsResponse.notOk()) {
            return "获取资金数据失败";
        }
        List<CfCapitalAccountRecord> cfCapitalAccountRecords = cfCapitalAccountRecordsResponse.getData();
        if (CollectionUtils.isEmpty(cfCapitalAccountRecords)) {
            return result;
        }
        Response<CfCapitalAccount> cfCapitalAccountResponse = financeDelegate.getCfCapitalAccountByInfoUuid(infoUuid);
        if (cfCapitalAccountResponse.notOk()) {
            return "获取资金数据失败";
        }
        CfCapitalAccount cfCapitalAccount = cfCapitalAccountResponse.getData();

        if (cfCapitalAccount != null) {
            result += "打款金额：" + MoneyUtil.buildBalance(cfCapitalAccount.getDrawCashAmount()) + " ";
            result += "打款次数: " + cfCapitalAccount.getDrawCashNum() + " ";
            result += "线下打款金额: " + cfCapitalAccount.getDrawManualAmount() + " ";
            result += "线下打款次数: " + cfCapitalAccount.getDrawManualNum() + " ";
        }

        result += "打款明细：";
        for (CfCapitalAccountRecord record : cfCapitalAccountRecords) {
            result += String.format("金额:%s\n时间:%s",
                    MoneyUtil.buildBalance(record.getAmount()),
                    dateFormat.format(record.getCreateTime()));
        }

        return result;
    }

    private String getHasRefundAmount(String infoUuid) {
        String hasDrawAmount = "0";
        Response<CfCapitalAccount> cfCapitalAccountResponse = financeDelegate.getCfCapitalAccountByInfoUuid(infoUuid);
        if (cfCapitalAccountResponse.notOk()) {
            return "获取资金数据失败";
        }
        CfCapitalAccount cfCapitalAccount = cfCapitalAccountResponse.getData();
        if (cfCapitalAccount != null) {
            hasDrawAmount = MoneyUtil.buildBalance(cfCapitalAccount.getAllRefundAmount());
        }
        return hasDrawAmount;
    }
    private String getSingleRefundAmount(String infoUuid) {
        String hasDrawAmount = "0";
        Response<CfCapitalAccount> cfCapitalAccountResponse = financeDelegate.getCfCapitalAccountByInfoUuid(infoUuid);
        if (cfCapitalAccountResponse.notOk()) {
            return "获取资金数据失败";
        }
        CfCapitalAccount cfCapitalAccount = cfCapitalAccountResponse.getData();
        if (cfCapitalAccount != null) {
            hasDrawAmount = MoneyUtil.buildBalance(cfCapitalAccount.getSingleRefundAmount());
        }
        return hasDrawAmount;
    }


    private String getDrawCashApplyReason(int caseId) {
        String drawCashReason = "未提现申请";
        Response<CfDrawCashApplyVo> applyVoResponse = financeDelegate.getApplyInfo(caseId);
        if (applyVoResponse.notOk()) {
            return "获取资金数据失败";
        }
        CfDrawCashApplyVo drawCashApplyVo = applyVoResponse.getData();
        if (drawCashApplyVo != null) {
                drawCashReason = "提现原因：" + drawCashApplyVo.getUseOfFunds() + " ";
                drawCashReason += "患者当前状态：" + drawCashApplyVo.getPatientConditionNow();
        }
        return drawCashReason;
    }

    private String getDrawType(int caseId) {
        String drawType = "";
        Response<CfDrawCashApplyVo> response = financeDelegate.getApplyInfo(caseId);
        if (response.notOk()) {
            return "获取打款类型失败";
        }
        CfDrawCashApplyVo drawCashApplyVo = response.getData();
        if (drawCashApplyVo != null) {
            if (drawCashApplyVo.getThirdType() == CfDrawCashConstant.ThirdType.PA.getCode()) {
                drawType = "打款到个人账户";
            } else if (drawCashApplyVo.getThirdType() == CfDrawCashConstant.ThirdType.PA_DG.getCode()) {
                drawType = "打款到对公账户";
            }
        }
        return drawType;
    }

    @Override
    public String getCategory() {
        return "钱款";
    }

    @Override
    public int getOrder() {
        return 4;
    }
}
