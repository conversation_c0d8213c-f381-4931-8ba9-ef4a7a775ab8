package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.NoticeGrowthToolBiz;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.growthtool.model.CfCaseStatusChangeMqModel;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @DATE 2020/4/9
 */
@Service
@Slf4j
public class NoticeGrowthToolBizImpl implements NoticeGrowthToolBiz {

    @Autowired
    private AdminCfInfoExtBiz cfInfoExtBiz;

    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;

    @Autowired(required = false)
    private Producer producer;


    @Override
    public void notice(int caseId) {
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        noticeGrowthTool(crowdfundingInfo);
    }

    @Override
    public void notice(String infoId) {
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoId);
        noticeGrowthTool(crowdfundingInfo);
    }


    private void noticeGrowthTool(CrowdfundingInfo crowdfundingInfo){
        int caseId = 0;
        try {
            if (crowdfundingInfo == null){
                return;
            }
            caseId = crowdfundingInfo.getId();
            CfInfoExt cfInfoExt = cfInfoExtBiz.getByCaseId(caseId);

            CfCaseStatusChangeMqModel  c = new CfCaseStatusChangeMqModel();
            c.setCaseId(caseId);
            c.setFirstApproveStatusCode(cfInfoExt.getFirstApproveStatus());
            c.setMaterialAuditStatusCode(crowdfundingInfo.getStatus().value());
            c.setCaseEndStatus(crowdfundingInfo.getEndTime().before(new Date()));

            MessageResult messageResult =  producer.send(Message.of(MQTopicCons.CF,
                    CfClientMQTagCons.CASE_STATUS_NOTICE_GROWTH_TOOL,
                    System.currentTimeMillis()+"",c));
            log.info("notice c={} messageResult={}",c,messageResult);
        }catch (Exception e){
            log.error("notice caseid={}",caseId,e);
        }
    }
}
