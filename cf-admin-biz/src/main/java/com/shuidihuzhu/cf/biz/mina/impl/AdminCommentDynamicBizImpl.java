package com.shuidihuzhu.cf.biz.mina.impl;

import com.shuidihuzhu.cf.biz.mina.AdminCommentDynamicBiz;
import com.shuidihuzhu.cf.dao.mina.AdminCommentDynamicDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AdminCommentDynamicBizImpl implements AdminCommentDynamicBiz{
    @Autowired
    private AdminCommentDynamicDao adminCommentDynamicDao;

    @Override
    public int updateTopStatusByCommentId(int status, long commentId) {
        if (status < 0 || status > 1 || commentId < 0) {
            return 0;
        }
        return adminCommentDynamicDao.updateTopStatusByCommentId(status, commentId);
    }
}
