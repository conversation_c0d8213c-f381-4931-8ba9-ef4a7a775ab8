package com.shuidihuzhu.cf.biz.mina.impl;

import com.shuidihuzhu.cf.biz.mina.AdminKeywordBiz;
import com.shuidihuzhu.cf.dao.mina.AdminKeywordDao;
import com.shuidihuzhu.cf.vo.mina.AdminKeyword;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AdminKeywordBizImpl implements AdminKeywordBiz{
    @Autowired
    private AdminKeywordDao adminKeywordDao;

    @Override
    public int addOne(AdminKeyword adminKeyword) {
        if (StringUtils.isEmpty(adminKeyword.getKeyword())) {
            return 0;
        }
        return this.adminKeywordDao.addOne(adminKeyword);
    }
}
