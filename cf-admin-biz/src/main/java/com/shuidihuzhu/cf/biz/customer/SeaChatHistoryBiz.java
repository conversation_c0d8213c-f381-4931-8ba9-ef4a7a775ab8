package com.shuidihuzhu.cf.biz.customer;

import com.shuidihuzhu.cf.customer.CfChatRecordDO;
import com.shuidihuzhu.cf.customer.ChatHistoryModelVo;

import java.util.List;

public interface SeaChatHistoryBiz {

    List<String> getCidsByCryptoUserIds(List<String> cryptoAccountIds);

    List<String> getCidsByPhoneNumber(String phoneNumber);

    List<ChatHistoryModelVo> getChatHistoryByCids(String startTime, String endTime, List<String> cids);

    List<ChatHistoryModelVo> queryChatRecordByCid(String cid, int current, int pageSize);

    List<CfChatRecordDO> queryRecordByUserId(long userId);
}
