package com.shuidihuzhu.cf.labels;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.dao.labels.CaseLabelsManagementDao;
import com.shuidihuzhu.cf.model.CaseLabelsManagement;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.FoundationLabelInfo;
import com.shuidihuzhu.client.cf.growthtool.client.CfBdCaseInfoFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.FoundationModel;
import com.shuidihuzhu.common.web.model.Response;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2020/12/17  10:26 上午
 */
@Service
@RefreshScope
@Slf4j
public class CaseLabelsManagementService {
    @Autowired
    private CaseLabelsManagementDao caseLabelsManagementDao;

    @Autowired
    private CfBdCaseInfoFeignClient bdCaseInfoFeignClient;
    // 基金会标签信息Map
    Map<String, FoundationLabelInfo> foundationLabelInfoMap = new HashMap<>();

    /**
     * 基金会标签内容配置
     */
    @Value("${funding.foundation.label.config:[]}")
    public void setFoundationLabelConfig(String foundationLabelConfig) {
        if (StringUtils.isBlank(foundationLabelConfig)) {
            return;
        }
        foundationLabelInfoMap = JSONObject.parseArray(foundationLabelConfig, FoundationLabelInfo.class)
                .stream().collect(Collectors.toMap(FoundationLabelInfo::getFoundationLabelName, Function.identity()));
    }

    public int add(String infoUuid, String labelsManagement) {
        if (StringUtils.isAnyEmpty(infoUuid, labelsManagement)) {
            return 0;
        }
        return caseLabelsManagementDao.add(infoUuid, labelsManagement);
    }

    public String get(String infoUuid) {
        if (StringUtils.isEmpty(infoUuid)) {
            return Strings.EMPTY;
        }
        return caseLabelsManagementDao.get(infoUuid);
    }

    public int update(String infoUuid, String labelsManagement) {
        if (StringUtils.isAnyEmpty(infoUuid, labelsManagement)) {
            return 0;
        }
        String labelsManagement1 = this.getLabelsManagement(infoUuid, labelsManagement);
        return caseLabelsManagementDao.update(infoUuid, labelsManagement1);
    }

    public int updateWithoutHandleChaopu(String infoUuid, String labelsManagement) {
        if (StringUtils.isAnyEmpty(infoUuid, labelsManagement)) {
            return 0;
        }
        return caseLabelsManagementDao.update(infoUuid, labelsManagement);
    }

    private String getLabelsManagement(String infoUuid,String labelsManagementStr) {
        String caseLabelsManagement = this.get(infoUuid);
        CaseLabelsManagement source = null;
        if (StringUtils.isNotBlank(caseLabelsManagement)) {
            try {
                source = JSON.parseObject(caseLabelsManagement, CaseLabelsManagement.class);
            } catch (Exception e) {
                log.error("标签未获取到 caseLabelsManagement:{}", caseLabelsManagement, e);
            }
        }

        CaseLabelsManagement target = null;
        if (StringUtils.isNotBlank(labelsManagementStr)) {
            try {
                target = JSON.parseObject(labelsManagementStr, CaseLabelsManagement.class);
            } catch (Exception e) {
                log.error("标签未获取到 caseLabelsManagement:{}", caseLabelsManagement, e);
            }
        }
        source = Objects.nonNull(source) ? source : new CaseLabelsManagement(1, 1, 1);
        // 单独处理基金会标签
        target.setChaoPuLabel(source.getChaoPuLabel());
        target.setGuRaoLabel(source.getGuRaoLabel());
        target.setZhongAiLabel(source.getZhongAiLabel());
        target.setJinShiLabel(source.getJinShiLabel());
        target.setOrgLabel(source.getOrgLabel());
        return JSON.toJSONString(target);
    }


    public void addLabelForCase(CrowdfundingInfo crowdfundingInfo) {
        String caseLabelsManagement = get(crowdfundingInfo.getInfoId());
        if (StringUtils.isBlank(caseLabelsManagement)) {
            CaseLabelsManagement labelsManagement = new CaseLabelsManagement(1, 1, 1);
            caseLabelsManagement = JSON.toJSONString(labelsManagement);
            add(crowdfundingInfo.getInfoId(), caseLabelsManagement);
        }
        CaseLabelsManagement labelsManagement = JSON.parseObject(caseLabelsManagement, CaseLabelsManagement.class);
        String infoId = crowdfundingInfo.getInfoId();
        try {
            Response<FoundationModel> labelResponse = bdCaseInfoFeignClient.getCaseFoundation(crowdfundingInfo.getId());
            log.debug("getCaseFoundation infoId:{},labelResponse:{}", infoId, labelResponse);
            if (labelResponse.notOk() || labelResponse.getData() == null) {
                return;
            }

            String foundation = labelResponse.getData().getFoundationName();
            if(StringUtils.isBlank(foundation)){
                return;
            }
            final FoundationLabelInfo foundationLabelInfo = foundationLabelInfoMap.get(foundation);
            if (foundationLabelInfo == null) {
                throw new RuntimeException("未支持的标签配置");
            }

            // 设置对应基金会标签为展示
            labelsManagement.setOrgLabel(foundationLabelInfo.getLabel());

            updateWithoutHandleChaopu(crowdfundingInfo.getInfoId(), JSON.toJSONString(labelsManagement));
        } catch (Exception e){
            log.error("init audit operate error msg:",e);
        }
    }
    // 更新基金会label
    public void updateOrgLabel(String infoId, int orgLabelId) {
        String caseLabelsManagement = get(infoId);
        if (StringUtils.isBlank(caseLabelsManagement)) {
            CaseLabelsManagement labelsManagement = new CaseLabelsManagement(1, 1, 1);
            caseLabelsManagement = JSON.toJSONString(labelsManagement);
            add(infoId, caseLabelsManagement);
        }
        CaseLabelsManagement labelsManagement = JSON.parseObject(caseLabelsManagement, CaseLabelsManagement.class);
        // 设置对应基金会标签为展示
        labelsManagement.setOrgLabel(orgLabelId);
        log.info("updateOrgLabel infoId:{},orgLabelId:{}", infoId, orgLabelId);
        updateWithoutHandleChaopu(infoId, JSON.toJSONString(labelsManagement));
    }
}
