package com.shuidihuzhu.cf.delegate.saas;

import com.google.common.collect.Maps;
import com.shuidihuzhu.client.auth.saas.feign.OrganizationFeignClient;
import com.shuidihuzhu.client.auth.saas.feign.UserRoleFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthOrganizationDto;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthRoleDto;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SeaRoleClientV1 {

    @Autowired
    private UserRoleFeignClient roleFeignClient;
    @Autowired
    private OrganizationFeignClient orgFeignClient;

    public Map<Integer, List<String>> selectRoleNameByUserIds(List<Integer> userIds) {
        Map<Integer, List<String>> roleMapping = Maps.newHashMap();

        Response<AuthOrganizationDto> orgDto =  orgFeignClient.getOrg();
        if (orgDto == null || orgDto.getData() == null) {
            return roleMapping;
        }
        for (Integer userId : userIds) {
            Response<List<AuthRoleDto>> roleDto =  roleFeignClient
                    .queryRoleByUserIdAndOrgId(Long.valueOf(userId), orgDto.getData().getId());

            if (roleDto == null || CollectionUtils.isEmpty(roleDto.getData())) {
                continue;
            }

            roleMapping.put(userId, roleDto.getData().stream()
                    .filter(item -> null != item)
                    .map(AuthRoleDto::getRoleName)
                    .collect(Collectors.toList()));
        }
        return null;
    }


    public boolean validUserRole(long userId, long roleId) {
        Response<Boolean> response = roleFeignClient.validUserRole(userId, roleId);
        log.debug("validUserRole userId:{}, roleId:{}, result:{}", userId, roleId, response);

        if (Objects.isNull(response) || Objects.isNull(response.getData())) {
            return false;
        }

        return response.getData().booleanValue();
    }
}
