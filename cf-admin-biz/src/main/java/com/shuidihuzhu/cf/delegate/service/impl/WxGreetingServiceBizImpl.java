package com.shuidihuzhu.cf.delegate.service.impl;

import com.shuidihuzhu.cf.delegate.service.WxGreetingServiceBiz;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.wx.grpc.client.SendGreetingAsyncResponse;
import com.shuidihuzhu.wx.grpc.client.WxGreetingGrpcClient;
import com.shuidihuzhu.wx.grpc.client.feign.WxGreetingServiceClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/5/4 7:46 PM
 */
@Slf4j
@Service
@RefreshScope
public class WxGreetingServiceBizImpl implements WxGreetingServiceBiz {

    @Resource
    private WxGreetingGrpcClient wxGreetingGrpcClient;
    @Resource
    private WxGreetingServiceClient wxGreetingServiceClient;


    @Override
    public Boolean sendGreetingAsync(String openId, String key, int thirdType) {
        Response<Boolean> response = wxGreetingServiceClient.sendGreetingAsync(openId, key, thirdType);
        return Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(false);
    }
}
