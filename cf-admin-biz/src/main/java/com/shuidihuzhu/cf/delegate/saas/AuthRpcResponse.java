package com.shuidihuzhu.cf.delegate.saas;


import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.common.web.enums.MyErrorCode;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Data
public class AuthRpcResponse<T> implements Serializable {

    /**
     * 执行结果
     */
    private boolean success;
    /**
     * 执行结果为false，将产生code和msg
     */
    private int code;
    private String msg;

    private T result;

    public AuthRpcResponse() {
    }

    public AuthRpcResponse(boolean success, int code, String msg, T result) {
        this.success = success;
        this.code = code;
        this.msg = msg;
        this.result = result;
    }

    public static <T> AuthRpcResponse<T> makeSuccess() {
        return new AuthRpcResponse<>(true, 0, "", null);
    }

    public static <T> AuthRpcResponse<T> makeSuccess(T result) {
        return new AuthRpcResponse<>(true, 0, "", result);
    }


    public static <T> AuthRpcResponse<T> makeFailed(MyErrorCode errorCode) {
        return makeFailed(errorCode.getCode(), errorCode.getMsg());
    }

    public static <T> AuthRpcResponse<T> makeFailed(int code, String msg) {
        return new AuthRpcResponse<>(false, code, StringUtils.trimToEmpty(msg), null);
    }

    public static <T> AuthRpcResponse<T> makeSuccess(int code, String msg, T result) {
        return new AuthRpcResponse<>(true, code, StringUtils.trimToEmpty(msg), result);
    }

    public static <T> AuthRpcResponse<T> makeFailed(int code, String msg, T result) {
        return new AuthRpcResponse<>(false, code, StringUtils.trimToEmpty(msg), result);
    }



    public T getData(Class<T> claz) {
        if (result == null) {
            return null;
        }
        return (T) JSONObject.parseObject(JSONObject.toJSONString(result), claz);
    }
}
