package com.shuidihuzhu.cf.delegate.service;

import com.shuidihuzhu.wx.grpc.client.WxConfigsResponse;
import com.shuidihuzhu.wx.grpc.model.SimpleWxConfig;
import com.shuidihuzhu.wx.grpc.model.WxConfigModel;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/5/4 4:07 PM
 */
public interface WxConfigServiceBiz {

    List<WxConfigModel> getWxConfigByConditon(int pageSize, int current, String description,
                                              Integer wxType, Integer bizType);

    long getWxConfigCountByCondition(String description, Integer wxType, Integer bizType);

    List<WxConfigModel> getWxConfigByThirdTypes(List<Integer> thirdTypes);

    @Deprecated
    WxConfigsResponse getConfigsByGroupId(int pageSize, int current, long groupId);

    @Deprecated
    long getWxConfigCountByGroupId(long groupId);

    @Deprecated
    WxConfigsResponse getRepeatWxConfig(List<WxConfigModel> wxMpConfigs);

    @Deprecated
    void addWxConfigBatch(List<WxConfigModel> wxConfigModels);

    @Deprecated
    void updateWxConfig(WxConfigModel wxMpConfig);

    List<WxConfigModel> getWxConfigByBizType(int bizType);

    List<WxConfigModel> getAllConfigsByGroupId(int groupId);

    WxConfigModel getWxConfigByAppId(String appid);

    WxConfigModel getWxConfigByThirdType(int thirdType);

    List<SimpleWxConfig> getSimpleWxConfigList(List<Integer> thirdTypeCodes);

}
