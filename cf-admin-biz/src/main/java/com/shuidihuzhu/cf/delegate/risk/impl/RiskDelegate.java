package com.shuidihuzhu.cf.delegate.risk.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.admin.util.AdminListUtil;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.feign.CfFirstApproveFeignClient;
import com.shuidihuzhu.cf.client.feign.cipher.constants.DesensitizeEnum;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.client.ugc.model.domain.risk.RiskControlWordDO;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordCheckContext;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordResult;
import com.shuidihuzhu.cf.client.ugc.service.RiskControlWordCheckFeignV2Client;
import com.shuidihuzhu.cf.client.ugc.service.RiskControlWordManageClient;
import com.shuidihuzhu.cf.client.ugc.utils.UgcResponseUtils;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.domain.CaseRaiseRiskDO;
import com.shuidihuzhu.cf.domain.CfCaseRiskDO;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CfInfoMirrorRecordEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfOperatingRecordEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.EditMaterialType;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveIdcardVerifyStatusEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.risk.CfCaseRiskTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.risk.StatRiskHandleStatusEnum;
import com.shuidihuzhu.cf.enums.risk.UgcTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.risk.verify.RiskUgcVerifyModel;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.AdminImageService;
import com.shuidihuzhu.client.cf.api.model.enums.ParamTypeEnum;
import com.shuidihuzhu.client.cf.risk.client.CfApiChaifenRiskClient;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2019/6/12 5:14 PM
 */
@Service
@Slf4j
@RefreshScope
public class RiskDelegate implements IRiskDelegate {
    @Autowired
    private CfApiChaifenRiskClient cfRiskClient;

    @Autowired
    private RiskControlWordCheckFeignV2Client riskControlWordCheckFeignV2Client;

    @Autowired
    private RiskControlWordManageClient riskControlWordManageClient;

    @Autowired
    private CfFirstApproveFeignClient firstApproveClient;

    @Autowired
    private AdminImageService imageService;

    @Autowired
    private MaskUtil maskUtil;

//    @Autowired
//    private CrowdfundingReportBiz crowdfundingReportBiz;
    @Override
    public OpResult<CfCaseRiskDO> getByInfoUuid(String infoUuid, CfCaseRiskTypeEnum caseRiskTypeEnum) {
        Response<String> response = cfRiskClient.getByInfoUuidAndCaseRiskType(infoUuid,caseRiskTypeEnum.getType());
        if(response.ok()){
            return OpResult.createSucResult(AdminListUtil.getModelFromResponse(response, CfCaseRiskDO.class));
        }
        log.error("getByInfoUuid fegin接口返回失败:{}", JSON.toJSONString(response));
        return OpResult.createFailResult(CfErrorCode.SYSTEM_STRING_TO_RESPONSE_ERROR,response.getMsg());
        //return cfCaseRiskFacade.getByInfoUuid(infoUuid, caseRiskTypeEnum);
    }

    @Override
    public OpResult handleInfoRisk(String infoUuid) {
        Response response = cfRiskClient.handleInfoRisk(infoUuid);
        if(response.ok()){
            return OpResult.createSucResult();
        }
        log.error("handleInfoRisk fegin接口返回失败:{}", JSON.toJSONString(response));
        return OpResult.createFailResult(CfErrorCode.SYSTEM_STRING_TO_RESPONSE_ERROR,response.getMsg());
        //return cfCaseRiskFacade.handleInfoRisk(infoUuid);
    }

    @Override
    public OpResult handleDrawCaskRisk(String infoUuid) {
        Response response = cfRiskClient.handleDrawCaskRisk(infoUuid);
        if(response.ok()){
            return OpResult.createSucResult();
        }
        log.error("handleDrawCaskRisk fegin接口返回失败:{}", JSON.toJSONString(response));
        return OpResult.createFailResult(CfErrorCode.SYSTEM_STRING_TO_RESPONSE_ERROR,response.getMsg());
        //return cfCaseRiskFacade.handleDrawCaskRisk(infoUuid);
    }

    @Override
    public OpResult onCaseEnd(String infoUuid) {
        Response response = cfRiskClient.onCaseEnd(infoUuid);
        if(response.ok()){
            return OpResult.createSucResult();
        }
        log.error("onCaseEnd fegin接口返回失败:{}", JSON.toJSONString(response));
        return OpResult.createFailResult(CfErrorCode.SYSTEM_STRING_TO_RESPONSE_ERROR,response.getMsg());
        //return cfCaseRiskFacade.onCaseEnd(infoUuid);
    }

    @Override
    public OpResult onCaseFirstApproveHasDelay72(String infoUuid) {
        Response response = cfRiskClient.onCaseFirstApproveHasDelay72(infoUuid);
        if(response.ok()){
            return OpResult.createSucResult();
        }
        log.error("onCaseFirstApproveHasDelay72 fegin接口返回失败:{}", JSON.toJSONString(response));
        return OpResult.createFailResult(CfErrorCode.SYSTEM_STRING_TO_RESPONSE_ERROR,response.getMsg());
        //return cfCaseRiskFacade.onCaseFirstApproveHasDelay72(infoUuid);
    }

    @Override
    public OpResult onCaseInfoPassed(String infoUuid, CrowdfundingInfo info) {
        Response response = cfRiskClient.onCaseInfoPassed(JSON.toJSONString(info), infoUuid,ParamTypeEnum.CrowdfundingInfo);
        if(response.ok()){
            return OpResult.createSucResult();
        }
        log.error("onCaseInfoPassed fegin接口返回失败:{}", JSON.toJSONString(response));
        return OpResult.createFailResult(CfErrorCode.SYSTEM_STRING_TO_RESPONSE_ERROR,response.getMsg());
        //return cfCaseRiskFacade.onCaseInfoPassed(infoUuid, info);
    }


    @Override
    public OpResult<CfCaseRiskDO> doVerify(CfCaseRiskDO v, CfCaseRiskTypeEnum typeEnum, String dataLevel) {
        Response response = cfRiskClient.doVerify(JSON.toJSONString(v), typeEnum.getType(), dataLevel,ParamTypeEnum.CfCaseRiskDO);
        if(response.ok()){
            return OpResult.createSucResult(AdminListUtil.getModelFromResponse(response, CfCaseRiskDO.class));
        }
        log.error("doVerify fegin接口返回失败:{}", JSON.toJSONString(response));
        return OpResult.createFailResult(CfErrorCode.SYSTEM_STRING_TO_RESPONSE_ERROR,response.getMsg());
        //return cfCaseRiskFacade.doVerify(v, typeEnum, dataLevel);
    }


    @Override
    public void onRemark(int caseId, int remarkCode) {
        cfRiskClient.onRemark(caseId, remarkCode);
        //cfCaseRiskFacade.onRemark(caseId, remarkCode);
    }

    @Override
    public List<CfCaseRiskDO> listByConditionAll(int verified, Boolean passed, int type, StatRiskHandleStatusEnum handleStatus) {
        Response<List<String>> response = cfRiskClient.listByConditionAll(verified, passed, type,ParamTypeEnum.StatRiskHandleStatusEnum,JSON.toJSONString(handleStatus));
        if(response.ok()){
            return AdminListUtil.getModelListFromResponse(response,CfCaseRiskDO.class);
        }
        log.error("listByConditionAll fegin接口返回失败:{}", JSON.toJSONString(response));
        return Lists.newArrayList();
        //return cfCaseRiskService.listByConditionAll(verified, passed, type, handleStatus);
    }

    @Override
    public CaseRaiseRiskDO getByInfoUuid(String infoUuid) {
        Response<String> response = cfRiskClient.getByInfoUuid(infoUuid);
        if(response.ok()){
            return AdminListUtil.getModelFromResponse(response,CaseRaiseRiskDO.class);
        }
        log.error("getByInfoUuid fegin接口返回失败:{}", JSON.toJSONString(response));
        throw new RuntimeException(CfErrorCode.SYSTEM_STRING_TO_RESPONSE_ERROR.getMsg());
    }


    @Override
    public OpResult<Integer> saveRaiseRisk(CaseRaiseRiskDO raiseRiskDO) {
        Response<Integer> response = cfRiskClient.saveRaiseRisk(JSON.toJSONString(raiseRiskDO),ParamTypeEnum.CaseRaiseRiskDO);
        if(response.ok()){
            return OpResult.createSucResult(response.getData());
        }
        log.error("saveRaiseRisk fegin接口返回失败:{}", JSON.toJSONString(response));
        return OpResult.createFailResult(CfErrorCode.SYSTEM_STRING_TO_RESPONSE_ERROR,response.getMsg());
        //return caseRaiseRiskService.saveRaiseRisk(raiseRiskDO);
    }

    @Override
    public OpResult handleRiskPassed(String infoUuid) {
        Response response = cfRiskClient.handleRiskPassed(infoUuid);
        if(response.ok()){
            return OpResult.createSucResult();
        }
        log.error("handleRiskPassed fegin接口返回失败:{}", JSON.toJSONString(response));
        return OpResult.createFailResult(CfErrorCode.SYSTEM_STRING_TO_RESPONSE_ERROR,response.getMsg());
        //return caseRaiseRiskFacade.handleRiskPassed(infoUuid);
    }

    @Override
    public OpResult handleRiskPassed(String infoUuid, int adminUserId) {
        Response response = cfRiskClient.handleRiskPassed(infoUuid, adminUserId);
        if(response.ok()){
            return OpResult.createSucResult();
        }
        log.error("handleRiskPassed fegin接口返回失败:{}", JSON.toJSONString(response));
        return OpResult.createFailResult(CfErrorCode.SYSTEM_STRING_TO_RESPONSE_ERROR,response.getMsg());
    }

    @Override
    public CfFaceIdLivingVerifyInfo getCfFaceIdLivingVerifyInfoByInfoUuid(String infoUuid) {
        Response<String> response = cfRiskClient.getCfFaceIdLivingVerifyInfoByInfoUuid(infoUuid);
        if(response.ok()){
            return AdminListUtil.getModelFromResponse(response,CfFaceIdLivingVerifyInfo.class);
        }
        log.error("getCfFaceIdLivingVerifyInfoByInfoUuid fegin接口返回失败:{}", JSON.toJSONString(response));
        return null;
    }

    @Override
    public IdcardVerifyWhiteList getByNameAndIdcard(String name, String idCard) {
        Response<String> response = cfRiskClient.getByNameAndIdcard(name,idCard);
        if(response.ok() && response.getData() != null){
            return AdminListUtil.getModelFromResponse(response,IdcardVerifyWhiteList.class);
        }
        log.error("getByNameAndIdcard fegin接口返回失败:{}", JSON.toJSONString(response));
        return null;
    }

    @Override
    public int addIdcardVerifyWhiteList(String name, String idCard) {
        Response<Integer> response = cfRiskClient.addIdcardVerifyWhiteList(name,idCard);
        return response.ok() ? response.getData() : 0;
        //return cfFirstApproveIdCardWhiteListBiz.add(name, idCard);
    }

    @Override
    public int addIdcardVerifyWhiteListV2(String name, String idCard, String images, int reason, String otherReason, String operator) {
        Response<Integer> response = cfRiskClient.addIdcardVerifyWhiteListV2(name, idCard, images, reason, otherReason, operator);
        return response.ok() ? response.getData() : 0;
    }

    @Override
    public List<IdcardVerifyWhiteListRecord> whiteListOperationRecord(int id) {
        Response<String> response = cfRiskClient.whiteListOperationRecord(id);
        if (response.notOk() || StringUtils.isBlank(response.getData())) {
            return null;
        }
        List<IdcardVerifyWhiteListRecord> records = null;
        try {
            records = JSON.parseArray(response.getData(), IdcardVerifyWhiteListRecord.class);
        } catch (JSONException e) {
            log.error("parse json error", e);
        }
        return records;
    }

    @Override
    public int deleteFirstApproveWhiteIdById(int id) {
        Response<Integer> response = cfRiskClient.deleteFirstApproveWhiteIdById(id);
        return response.ok() ? response.getData() : 0;
        //return cfFirstApproveIdCardWhiteListBiz.deleteFirstApproveWhiteIdById(id);
    }

    @Override
    public PageInfo<IdcardVerifyWhiteList> selectAllWhiteIdCardList(int current, int pageSize, String name, String idCard) {
        Response<String> response = cfRiskClient.selectAllWhiteIdCardListAll(current,pageSize, name, idCard);
        if(response.ok() && response.getData() != null){
            IdcardVerifyWhiteModel result = AdminListUtil.getModelFromResponse(response,IdcardVerifyWhiteModel.class);
            PageInfo pageInfo = new PageInfo();
            pageInfo.setPageSize(pageSize);
            pageInfo.setPageNum(current);
            pageInfo.setTotal(result.getTotal());
            pageInfo.setList(result.getIdcardVerifyWhiteList());
            return pageInfo;
        }
        return new PageInfo(Lists.newArrayList());
        //return cfFirstApproveIdCardWhiteListBiz.selectAllWhiteIdCardList(current, pageSize);
    }

    @Override
    public CfErrorCode verifyIdcard(String selfRealName, String selfIdcard, String otherRealName, String otherIdcard, int patientIdType, UserRelTypeEnum relType, long userId) {
        Response<String> response = cfRiskClient.verifyIdcard(selfRealName, selfIdcard, otherRealName, otherIdcard, patientIdType, relType, userId);
        if(response.ok()){
            return AdminListUtil.getModelFromResponse(response,CfErrorCode.class);
        }
        log.error("verifyIdcard fegin接口返回失败:{}", JSON.toJSONString(response));
        return CfErrorCode.ADD_CROWDFUNDING_VERIFY_SUCCESS_SYSTEM_ERROR;
    }

    @Override
    public boolean isSuccess(CfErrorCode cfErrorCode) {
        if (cfErrorCode == CfErrorCode.SUCCESS ||
                cfErrorCode == CfErrorCode.ADD_CROWDFUNDING_VERIFY_SUCCESS_IN_WHITELIST ||
                cfErrorCode == CfErrorCode.ADD_CROWDFUNDING_VERIFY_SUCCESS_SYSTEM_ERROR) {
            return true;
        } else {
            return false;
        }
        //return cfFirstApproveBiz.isSuccess(cfErrorCode);
    }

    @Override
    public int updateStatusByCaseId(int caseId, FirstApproveIdcardVerifyStatusEnum statusEnum) {
        if (caseId <= 0 || statusEnum == null) {
            return 0;
        }
        FeignResponse<Integer> response = firstApproveClient.updateStatusByCaseId(caseId, statusEnum.getCode());
        return response.ok() ? response.getData() : 0;
        //return cfFirstApproveBiz.updateStatus(id, statusEnum);
    }

    @Override
    public CfFirsApproveMaterial getCfFirsApproveMaterialByInfoId(int infoId) {
//        CfFirsApproveMaterial cfFirsApproveMaterial = new CfFirsApproveMaterial();
//        cfFirsApproveMaterial.setInfoId(infoId);
//        Response<List<String>> response = cfRiskClient.getCfFirsApproveMaterialListByParam(JSON.toJSONString(cfFirsApproveMaterial),ParamTypeEnum.CfFirsApproveMaterial);
//        if(response.ok()){
//            List<CfFirsApproveMaterial> list = AdminListUtil.getModelListFromResponse(response,CfFirsApproveMaterial.class);
//            return CollectionUtils.isEmpty(list) ? new CfFirsApproveMaterial() : list.get(0);
//        }
//        log.error("getCfFirsApproveMaterialByInfoId fegin接口返回失败:{}", JSON.toJSONString(response));
//
//        return null;
        FeignResponse<CfFirsApproveMaterial> result = firstApproveClient.getCfFirstApproveMaterialByCaseId(infoId);
        if (result == null || result.getData() == null) {
            log.warn("getCfFirsApproveMaterialByInfoId caseId:{} feign:{}", infoId, JSON.toJSONString(result));
            return null;
        }
        //替换图片域名
        CfFirsApproveMaterial material = result.getData();
        material.setImageUrl(imageService.convertMultiUrl(material.getImageUrl()));
        material.setPovertyImageUrl(imageService.convertMultiUrl(material.getPovertyImageUrl()));
        return result.getData();
    }

    @Override
    public List<CfFirsApproveMaterial> getCfFirsApproveMaterialListByParam(CfFirsApproveMaterial cfFirsApproveMaterial) {
        Response<List<String>> response = cfRiskClient.getCfFirsApproveMaterialListByParam(JSON.toJSONString(cfFirsApproveMaterial),ParamTypeEnum.CfFirsApproveMaterial);
        if(response.ok()){
            List<CfFirsApproveMaterial> materialList = AdminListUtil.getModelListFromResponse(response, CfFirsApproveMaterial.class);
            materialList.forEach(material -> {
                //替换图片域名
                material.setImageUrl(imageService.convertMultiUrl(material.getImageUrl()));
                material.setPovertyImageUrl(imageService.convertMultiUrl(material.getPovertyImageUrl()));
            });
            return materialList;
        }
        log.error("getCfFirsApproveMaterialListByParam fegin接口返回失败:{}", JSON.toJSONString(response));
        return Lists.newArrayList();
    }

    @Override
    public Map<Integer, CfFirsApproveMaterial> getMapByInfoIds(List<Integer> infoIds) {
        Response<Map<Integer, String>> response = cfRiskClient.getMapByInfoIds(infoIds);
        Map<Integer, CfFirsApproveMaterial> resultMap = Maps.newHashMap();
        if(response.ok()){
            Map<Integer, String> map = response.getData();
            for (Map.Entry<Integer, String> entry : map.entrySet()) {
                CfFirsApproveMaterial cfFirsApproveMaterial = JSON.parseObject(entry.getValue(),CfFirsApproveMaterial.class);
                //替换图片域名
                if (cfFirsApproveMaterial != null) {
                    cfFirsApproveMaterial.setImageUrl(imageService.convertMultiUrl(cfFirsApproveMaterial.getImageUrl()));
                    cfFirsApproveMaterial.setPovertyImageUrl(imageService.convertMultiUrl(cfFirsApproveMaterial.getPovertyImageUrl()));
                }
                resultMap.put(entry.getKey(),cfFirsApproveMaterial);
            }
            return resultMap;
        }
        log.error("getMapByInfoIds fegin接口返回失败:{}", JSON.toJSONString(response));
        return Maps.newHashMap();
    }

    @Override
    public int updateRejectTypeByInfoId(int infoId, int rejectType, String rejectMessage) {
        Response<Integer> response = cfRiskClient.updateRejectTypeByInfoId(infoId, rejectType, rejectMessage);
        return response.ok() ? response.getData() : 0;
        //return cfFirstApproveBiz.updateRejectTypeByInfoId(infoId, rejectType, rejectMessage);
    }

    @Override
    public String getCanEditMsg(int rejectType, boolean moreThan50w) {
        EditMaterialType e = EditMaterialType.valueOfCode(rejectType);
        if (e == null) {
            return "医疗材料";
        }
        switch (e) {
            case ALL:
                return moreThan50w ? "患者姓名和身份证号、医疗材料、筹款金额50万及以上原因说明 " : "患者姓名和身份证号、医疗材料 ";
            case POVERTY:
                return "贫困证明材料";
            case TARGET_AMOUNT_DESC:
                return moreThan50w ? "筹款金额50万及以上原因说明" : "";
            case IMAGE_URL:
            default:
                return "医疗材料";
        }
        //return cfFirstApproveBiz.getCanEditMsg(rejectType, moreThan50w);
    }

    @Override
    public List<CfSuspectedCaseInfo> getCfSuspectedCaseInfoListBySerch(CfSuspectedCaseInfoModel cfSuspectedCaseInfoModel) {
        Response<List<String>> response = cfRiskClient.getCfSuspectedCaseInfoListBySerch(JSON.toJSONString(cfSuspectedCaseInfoModel), ParamTypeEnum.CfSuspectedCaseInfoModel);
        if(response.ok()){
            return AdminListUtil.getModelListFromResponse(response,CfSuspectedCaseInfo.class);
        }
        log.error("getCfSuspectedCaseInfoListBySerch fegin接口返回失败:{}", JSON.toJSONString(response));
        return Lists.newArrayList();
    }

    @Override
    public int addCfSuspectedCaseInfo(CfSuspectedCaseInfo cfSuspectedCaseInfo) {
        Response<Integer> response = cfRiskClient.addCfSuspectedCaseInfo(JSON.toJSONString(cfSuspectedCaseInfo), ParamTypeEnum.CfSuspectedCaseInfo);
        return response.ok() ? response.getData() : 0;
        //return cfSuspectedCaseBiz.add(cfSuspectedCaseInfo);
    }

    @Override
    public List<CfSuspectedCaseInfo> getByLikeIdNumber(String idNumber) {
        Response<List<String>> response = cfRiskClient.getByLikeIdNumber(idNumber);
        if(response.ok()){
            List<CfSuspectedCaseInfo> result = AdminListUtil.getModelListFromResponse(response,CfSuspectedCaseInfo.class);
            result.stream().forEach(item -> {
                if (StringUtils.isNotBlank(item.getIdNumber())) {
                    item.setIdNumberMask(maskUtil.buildByDecryptStrAndType(item.getIdNumber(), DesensitizeEnum.IDCARD));
                }
                if (StringUtils.isNotBlank(item.getMobile())) {
                    item.setMobileMask(maskUtil.buildByDecryptPhone(item.getMobile()));
                }
            }
            );
            return result;
        }
        log.error("getByLikeIdNumber fegin接口返回失败:{}", JSON.toJSONString(response));
        return Lists.newArrayList();
    }

    @Override
    public int updateCfSuspectedCaseInfo(CfSuspectedCaseInfo cfSuspectedCaseInfo) {
        Response<Integer> response = cfRiskClient.updateCfSuspectedCaseInfo(JSON.toJSONString(cfSuspectedCaseInfo), ParamTypeEnum.CfSuspectedCaseInfo);
        return response.ok() ? response.getData() : 0;
        //return cfSuspectedCaseBiz.update(cfSuspectedCaseInfo);
    }

    @Override
    public int deleteCfSuspectedCaseInfo(int id) {
        Response<Integer> response = cfRiskClient.deleteCfSuspectedCaseInfo(id);
        return response.ok() ? response.getData() : 0;
        //return cfSuspectedCaseBiz.delete(id);
    }

    @Override
    public List<CrowdfundingReport> getCrowdfundingReportListByInfoId(Integer activityId) {
        //return crowdfundingReportBiz.getListByInfoId(activityId);
        Response<List<String>> response = cfRiskClient.getCrowdfundingReportListByInfoId(activityId);
        if(response.ok()){
            return AdminListUtil.getModelListFromResponse(response,CrowdfundingReport.class);
        }
        log.error("getCrowdfundingReportListByInfoId fegin接口返回失败:{}", JSON.toJSONString(response));
        return Lists.newArrayList();
    }

    @Override
    public CfOperatingRecord before(String infoUuid, long userId, String userName, CfOperatingRecordEnum.Type type, CfOperatingRecordEnum.Role role, List<CrowdfundingReport> crowdfundingReports) {
        //return cfReportMirrorService.before(infoUuid, userId, userName, type, role, crowdfundingReports);
        Response<String> response = cfRiskClient.before(infoUuid, userId, userName,role.getCode(), type.getCode(), AdminListUtil.getListStringFromListModel(crowdfundingReports), ParamTypeEnum.CrowdfundingReport);
        if(response.ok()){
            return AdminListUtil.getModelFromResponse(response,CfOperatingRecord.class);
        }
        log.error("before fegin接口返回失败:{}", JSON.toJSONString(response));
        return null;
    }

    @Override
    public void after(CfOperatingRecord cfOperatingRecord, List<CrowdfundingReport> crowdfundingReports) {
        //cfReportMirrorService.after(cfOperatingRecord, crowdfundingReports);
        try {
            CfOperatingRecordEnum.Role role = CfOperatingRecordEnum.Role.getByCode(cfOperatingRecord.getRole());
            CfOperatingRecordEnum.Type type = CfOperatingRecordEnum.Type.getByCode(cfOperatingRecord.getType());
            switch (type) {
                case SUBMIT_ADD_TRUST:
                case SEND_ADD_TRUST:
                case REFUSE_ADD_TRUST:
                case PASS_ADD_TRUST:
                    cfRiskClient.saveCfReportMirrorRecord(cfOperatingRecord.getInfoUuid(), cfOperatingRecord.getId(),
                            role.getCode(), CfInfoMirrorRecordEnum.Type.AFTER.getCode(),AdminListUtil.getListStringFromListModel(crowdfundingReports),ParamTypeEnum.CrowdfundingReport);
                case SUBMIT_HOSPITAL_AUDIT:
                    //下发医院核实
                case SEND_HOSPITAL_AUDIT:
                    //医院核实信息被驳回
                case REFUSE_HOSPITAL_AUDIT:
                    //医院核实信息通过
                case PASS_HOSPITAL_AUDIT:
                    cfRiskClient.saveCfHospitalAuditMirror(cfOperatingRecord.getInfoUuid(), cfOperatingRecord.getId(),
                            role.getCode(),CfInfoMirrorRecordEnum.Type.AFTER.getCode());
                    break;
            }
        } catch (Exception e) {
            log.error("CfReportMirrorService after error:", e);
        }
    }


    @Override
    public int updateValid(int valid, int id) {
        Response<Integer> response = cfRiskClient.updateValid(valid, id);
        return response.ok() ? response.getData() : 0;
        //return crowdFundingVerificationBiz.updateValid(valid, id);
    }


    @Override
    public void deleteCaseVerificationCache(String infoUuid) {
        //crowdFundingVerificationBiz.deleteCaseVerificationCache(infoUuid);
        Response response = cfRiskClient.deleteCaseVerificationCache(infoUuid);
        if(response.notOk()){
            log.error("deleteCaseVerificationCache fegin接口返回失败:{}",response);
        }
    }
    @Override
    public OpResult<RiskWordResult> isHit(RiskWordCheckContext ctx) {
        RpcResult<RiskWordResult> check = riskControlWordCheckFeignV2Client.check(ctx);
        return UgcResponseUtils.transformRpcResult2Op(check);
    }

    @Override
    public List<RiskControlWordDO> searchByTypeAll(int type, long category, String key) {
        Response<List<RiskControlWordDO>> r = riskControlWordManageClient.searchByTypeAll(type, category, key);
        if (r.notOk()) {
            return Lists.newArrayList();
        }
        return r.getData();
    }

    @Override
    public OpResult<RiskUgcVerifyModel> addVerify(RiskUgcVerifyModel model) {
        //return riskUgcVerifyFacade.addVerify(model);
        Response<String> response = cfRiskClient.addVerify(JSON.toJSONString(model),ParamTypeEnum.RiskUgcVerifyModel);
        if(response.ok()){
            return OpResult.createSucResult(AdminListUtil.getModelFromResponse(response,RiskUgcVerifyModel.class));
        }
        log.error("addVerify fegin接口返回失败:{}", JSON.toJSONString(response));
        return OpResult.createFailResult(CfErrorCode.SYSTEM_STRING_TO_RESPONSE_ERROR,response.getMsg());
    }

    @Override
    public OpResult deleteVerify(long caseId, UgcTypeEnum typeEnum, long ugcId) {
        Response<String> response = cfRiskClient.deleteVerify(caseId, typeEnum.getValue(), ugcId);
        if(response.ok()){
            return OpResult.createSucResult();
        }
        log.warn("deleteVerify fegin接口返回失败:{}", JSON.toJSONString(response));
        return OpResult.createFailResult(CfErrorCode.SYSTEM_STRING_TO_RESPONSE_ERROR,response.getMsg());
        //return riskUgcVerifyFacade.deleteVerify(caseId, typeEnum, ugcId);
    }

    @Override
    public OpResult saveCache(UgcTypeEnum ugcTypeEnum, long ugcId, long caseId, boolean isSafe) {
        //return riskUgcVerifyFacade.saveCache(ugcTypeEnum, ugcId, caseId, isSafe);
        Response<String> response = cfRiskClient.saveCache(caseId, ugcTypeEnum.getValue(), ugcId,isSafe);
        if(response.ok()){
            return OpResult.createSucResult();
        }
        log.error("saveCache fegin接口返回失败:{}", JSON.toJSONString(response));
        return OpResult.createFailResult(CfErrorCode.SYSTEM_STRING_TO_RESPONSE_ERROR,response.getMsg());
    }

    @Override
    public boolean isSafeSingle(UgcTypeEnum ugcTypeEnum, long ugcId, long caseId) {
        //return riskUgcVerifyFacade.isSafeSingle(ugcTypeEnum, ugcId, caseId);
        Response<Boolean> response = cfRiskClient.isSafeSingle(caseId, ugcTypeEnum.getValue(), ugcId);
        if(response.ok()){
            return response.getData();
        }
        log.error("isSafeSingle fegin接口返回失败:{}", JSON.toJSONString(response));
        return false;
    }

}
