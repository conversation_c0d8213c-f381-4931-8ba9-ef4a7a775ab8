package com.shuidihuzhu.cf.delegate.service.impl;

import com.shuidihuzhu.cf.delegate.service.WxUserEventStatusServiceBiz;
import com.shuidihuzhu.wx.grpc.client.WxUserEventStatusGrpcClient;
import com.shuidihuzhu.wx.grpc.client.feign.WxUserEventStatusServiceClient;
import com.shuidihuzhu.wx.grpc.model.WxMpSubscribeModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/5/5 5:36 PM
 */
@Slf4j
@Service
@RefreshScope
public class WxUserEventStatusServiceBizImpl implements WxUserEventStatusServiceBiz {

    @Resource
    private WxUserEventStatusServiceClient wxUserEventStatusServiceClient;


    @Override
    public WxMpSubscribeModel getSubscribeByUserId(long userId, int thirdType) {
        return wxUserEventStatusServiceClient.getSubscribeByUserId(userId, thirdType);
    }
}
