package com.shuidihuzhu.cf.delegate.other;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.mina.*;
import com.shuidihuzhu.cf.model.miniprogram.*;
import com.shuidihuzhu.msg.model.AppPushTemplate;
import com.shuidihuzhu.msg.model.AppPushTemplateParam;
import com.shuidihuzhu.msg.model.PushRecord;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * @author: wanghui
 * @create: 2019/6/13 11:08 AM
 */
public interface IMiniAppDelegate {
    int addCfKeywordPhaseRelation(CfKeywordPhaseRelation cfKeywordPhaseRelation);

    CfKeywordPhaseRelation selectCfKeywordPhaseRelationByPhaseId(int phaseId);

    int insertCfMinaMajorDiseaseInfo(CfMinaMajorDiseaseInfo cfMinaMajorDiseaseInfo);

    int insertCfMinaMajorDiseaseMenu(CfMinaMajorDiseaseMenu cfMinaMajorDiseaseMenu);

    int insertCfMinaMajorDiseaseTitleContent(CfMinaMajorDiseaseTitleContent cfMinaMajorDiseaseTitleContent);
    CfMinaQuiz getCfMinaQuizById(Integer id);

    List<CfMinaQuiz> findCfMinaQuizListByTitle(String title, Integer offset, Integer pageSize);

    Integer countByTitle(String title);

    Integer saveMinaQuiz(CfMinaQuiz cfMinaQuiz);

    Integer saveMinaQuizQuestion(CfMinaQuizQuestion cfMinaQuizQuestion);

    Integer saveMinaQuizAnswerBatch(List<CfMinaQuizAnswer> cfMinaQuizAnswerList);

    Integer saveQuizResult(CfMinaQuizResult cfMinaQuizResult);

    CfMinaQuiz getBySortAndIsNew(Integer isNew, Integer sort);

    Integer updateIsNewById(Integer isNew, Integer id);

    Integer updateSortById(Integer sort, Integer id);

    Integer updateReleaseTimeById(Date releaseTime, Integer id);

    Integer updateShowTypeById(Integer showType, Integer id);

    Integer updateIsDeleteById(Integer isDelete, Integer id);

    List<CfMinaQuizQuestion> findQuestion(Integer minaQuizId);

    List<CfMinaQuizAnswer> findAnswerByQuestionIds(List<Integer> questionIds);

    List<CfMinaQuizResult> findResultByQuizId(Integer minaQuizId);

    Integer updateAnswerDeleteByQuestionIds(Integer isDelete, List<Integer> questionIds);

    Integer updateQuestionDeleteByMinaQuizId(Integer isDelete, Integer minaQuizId);

    Integer updateResultDeleteByMinaQuizId(Integer isDelete, Integer minaQuizId);

    Integer updateQuizInfoById(CfMinaQuiz cfMinaQuiz);

    Integer insertPushConfig(CfMinaQuizPushConfig cfMinaQuizPushConfig);

    Integer updateStatusById(Integer status, Integer id);

    List<CfMinaQuizPushConfig> findPushConfig(Integer offset, Integer limit);

    CfMinaQuizPushConfig getPushConfigById(Integer id);

    Integer updatePushConfigById(CfMinaQuizPushConfig pushConfig);

    List<CfMiniQuizPush> findByQuizIdWithUserId(Integer minaQuizId, List<Long> userIds);

    List<CfMinaQuizRecord> findByUserIds(Integer minaQuizId, List<Long> userIds);

    Integer insertPush(CfMiniQuizPush cfMiniQuizPush);

    Integer countAllPushConfig();

    Integer insertResultConfig(CfMinaQuizResultConfig cfMinaQuizResultConfig);

    List<CfMinaQuizResultConfig> findResultConfigByResultIds(List<Integer> quizResultIds);

    int insertCfPublishPhase(CfPublishPhase cfPublishPhase);

    CfPublishPhase selectCfPublishPhaseByPhaseId(int phaseId);

    List<CfPublishPhase> getCfPublishPhaseListPage(int pageNum, int pageSize);

    List<CfPublishPhase> listCfPublishPhaseByTimeLimit(Timestamp time, int limit);

    List<CfPublishPhase> listCfPublishPhaseByPhaseIds(List<Integer> phaseIds);
    PushRecord buildFirstApproveRefuse(long userId, String nickName,
                                       String infoUuid, String failMsg, String url, String imageUrl, AppPushTemplate appPushTemplate,
                                       List<AppPushTemplateParam> appPushTemplateParamList, int subBizType);

    List<PushRecord> newInfoApplySuccess(CrowdfundingInfo crowdfundingInfo);

    List<PushRecord> newNeedModifiedSuccess(CrowdfundingInfo crowdfundingInfo);
    CfTopicComment selectCfTopicCommentById(long id);
    int deleteCfTopicCommentById(long id);

    int deleteCfTopicById(int id);

    int updateCfTopicById(int id, CfTopic cfTopic);

    CfTopic selectCfTopicById(int id);

    List<CfTopic> selectCfTopicByPhaseId(int phaseId);

    List<CfTopic> listCfTopicByIds(List<Integer> topicIds);

    CfTopicKeyword selectCfTopicKeywordById(int id);

    List<CfTopicKeyword> listCfTopicKeywordListByIds(List<Integer> ids);

    CfTopicKeyword selectCfTopicKeywordByKeyword(String keyword);
}
