package com.shuidihuzhu.cf.delegate.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.delegate.service.WxMenuServiceBiz;
import com.shuidihuzhu.wx.grpc.client.AddWxMenuResponse;
import com.shuidihuzhu.wx.grpc.client.EditWxMenuResponse;
import com.shuidihuzhu.wx.grpc.client.MenuDetailResponse;
import com.shuidihuzhu.wx.grpc.client.WxMenuGrpcClient;
import com.shuidihuzhu.wx.grpc.client.feign.WxMenuServiceClient;
import com.shuidihuzhu.wx.grpc.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/5/4 2:41 PM
 */
@Slf4j
@Service
@RefreshScope
public class WxMenuServiceBizImpl implements WxMenuServiceBiz {

    @Resource
    private WxMenuServiceClient wxMenuServiceClient;
    @Resource
    private WxMenuGrpcClient wxMenuGrpcClient;

    @Override
    public List<WxGroupMenuConfigVO> getWxMenuConfigList(List<Integer> groupIds) {

        if (CollectionUtils.isEmpty(groupIds)) {
            return Lists.newArrayList();
        }

        return wxMenuServiceClient.getWxMenuConfigList(groupIds);
    }

    @Override
    public List<WxMenuModel> getMenuList(Integer current, Integer pageSize) {
        return wxMenuServiceClient.getMenuList(current, pageSize);
    }

    @Override
    public MenuDetailResponseDto getMenuDetail(String menuId) {
        return wxMenuServiceClient.getMenuDetail(menuId);
    }

    @Override
    public WxGroupMenuConfigModel getWxGroupMenuConfig(int groupId) {
        return wxMenuServiceClient.getWxGroupMenuConfig(groupId);
    }

    @Override
    public List<WxMenuModel> getMenuListByName(Integer current, Integer pageSize, String menuName) {
        return wxMenuServiceClient.getMenuListByName(current, pageSize, menuName);
    }

    @Override
    public Integer menuCount() {
        return wxMenuServiceClient.menuCount();
    }

    @Override
    public CommonAddResponseModel createWxMenu(int groupId, String menuId, List<PersonalizeMenuConfigModel> menuList) {
        return wxMenuServiceClient.createWxMenu(groupId, menuId, menuList);
    }

    @Override
    public WxMenuResponseModel addMenu(String menuName, String menuConfig) {
        return wxMenuServiceClient.addMenu(menuName, menuConfig);
    }

    @Override
    public WxMenuResponseModel editMenu(String menuId, String menuName, String menuConfig) {
        return wxMenuServiceClient.editMenu(menuId, menuName, menuConfig);
    }
}
