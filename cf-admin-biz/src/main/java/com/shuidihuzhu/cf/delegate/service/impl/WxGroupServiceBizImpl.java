package com.shuidihuzhu.cf.delegate.service.impl;

import com.shuidihuzhu.cf.delegate.service.WxGroupServiceBiz;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.wx.grpc.client.GroupListResponse;
import com.shuidihuzhu.wx.grpc.client.WxGroup;
import com.shuidihuzhu.wx.grpc.client.WxGroupGrpcClient;
import com.shuidihuzhu.wx.grpc.client.common.CommonResponse;
import com.shuidihuzhu.wx.grpc.client.feign.WxGroupServiceClient;
import com.shuidihuzhu.wx.grpc.enums.WxMpGroupTypeEnum;
import com.shuidihuzhu.wx.grpc.model.WxConfigModel;
import com.shuidihuzhu.wx.grpc.model.WxGroupModel;
import com.shuidihuzhu.wx.grpc.model.WxMpGroupMapModel;
import com.shuidihuzhu.wx.grpc.util.WxRpcUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/5/4 3:21 PM
 */
@Slf4j
@Service
@RefreshScope
public class WxGroupServiceBizImpl implements WxGroupServiceBiz {

    @Resource
    private WxGroupServiceClient wxGroupServiceClient;

    @Override
    public List<WxMpGroupMapModel> getGroupDetail(int groupId) {
        return wxGroupServiceClient.getGroupDetail(groupId);
    }

    @Override
    public List<WxGroupModel> getAllGroup(WxMpGroupTypeEnum type) {
        return wxGroupServiceClient.getAllGroup(type);
    }

    @Override
    public List<WxGroupModel> getGroupList(Integer current, Integer pageSize, WxMpGroupTypeEnum type) {
        return wxGroupServiceClient.getGroupList(current, pageSize, type);
    }

    @Override
    public Integer getGroupCount(WxMpGroupTypeEnum type) {
        return wxGroupServiceClient.getGroupCount(type);
    }

    @Override
    public List<WxGroupModel> getRepeatGroupInfo(WxGroupModel wxGroupModel) {
        return wxGroupServiceClient.getRepeatGroupInfo(wxGroupModel);
    }

    @Override
    public Boolean addGroup(String name, WxMpGroupTypeEnum type, int isMain, int bizType) {
        Response<Boolean> response = wxGroupServiceClient.addGroup(name, type, isMain, bizType);
        return Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);
    }

    @Override
    public int getGroupId(String name, int type) {
        return wxGroupServiceClient.getGroupId(name, type);
    }

    @Override
    public Boolean addGroupMap(Integer groupId, List<WxConfigModel> wxConfigModels) {
        Response<Boolean> response = wxGroupServiceClient.addGroupMap(groupId, wxConfigModels);
        return Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);
    }

    @Override
    public Boolean updateGroupById(WxGroupModel wxGroupModel) {
        Response<Boolean> response = wxGroupServiceClient.updateGroupById(wxGroupModel);
        return Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);
    }

    @Override
    public Boolean deleteGroupMapByGroupId(int groupId) {
        Response<Boolean> response = wxGroupServiceClient.deleteGroupMapByGroupId(groupId);
        return Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);
    }

    @Override
    public List<WxGroupModel> getGroupsListByCondition(int current, int pageSize, String description, Integer bizType) {
        return wxGroupServiceClient.getGroupsListByCondition(current, pageSize, description, bizType);
    }

    @Override
    public int getCountByCondition(String description, Integer bizType) {
        return wxGroupServiceClient.getCountByCondition(description, bizType);
    }
}
