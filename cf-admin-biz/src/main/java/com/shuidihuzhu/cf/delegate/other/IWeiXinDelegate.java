package com.shuidihuzhu.cf.delegate.other;

import com.shuidi.weixin.common.exception.WxErrorException;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.msg.model.WxCustomRecord;

import java.util.List;
import java.util.Map;

/**
 * @author: wanghui
 * @create: 2019/6/12 9:00 PM
 */
public interface IWeiXinDelegate {
    OpResult sendByEmail(List<String> emailList, String content);

    OpResult sendByGroup(String groupId, String content);

    String generateQrcode(String scene);
    String sendTemplateMessage(String openId, String templateId, String url, Map<String, Object> dataMap, int type, int businessType, int subBusinessType) throws WxErrorException;

    Response saveWxCustomRecord(List<WxCustomRecord> wxCustomRecordList, String businessInfo);
}
