package com.shuidihuzhu.cf.delegate;

import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 【加密用老加密，解密用新解密】
 * <AUTHOR>
 */
@Service
public class EncryptDelegate {

    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    public String encrypt(String content){
        return oldShuidiCipher.aesEncrypt(content);
    }

    public String decrypt(String content){
        return shuidiCipher.decrypt(content);
    }


}
