package com.shuidihuzhu.cf.delegate.finance;

import com.shuidihuzhu.cf.domain.caseinfo.CfInfoCapitalCompensationDO;
import com.shuidihuzhu.cf.finance.model.CfCapitalAccountRecord;
import com.shuidihuzhu.cf.finance.model.drawcash.CfDrawCashDetail;
import com.shuidihuzhu.cf.finance.model.drawcash.CfDrawCashLaunchRecord;
import com.shuidihuzhu.cf.finance.model.drawcash.CfDrawCashLimit;
import com.shuidihuzhu.cf.finance.model.financestate.FinanceState;
import com.shuidihuzhu.cf.finance.model.po.CfCashPauseBooleanPo;
import com.shuidihuzhu.cf.finance.model.vo.CfDrawCashApplyVo;
import com.shuidihuzhu.cf.finance.model.vo.CfLaunchRecordVo;
import com.shuidihuzhu.cf.finance.model.vo.draw.CfAmountInfoVo;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCfRefund;
import com.shuidihuzhu.cf.model.crowdfunding.CfCapitalAccount;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefund;
import com.shuidihuzhu.common.web.model.Response;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

/**
 * @author: wanghui
 * @create: 2019/6/12 5:02 PM
 */
public interface IFinanceDelegate {
    Response<CfCapitalAccount> getCfCapitalAccountByInfoUuid(String infoUuid);

    Response<Map<String, CfCapitalAccount>> getCfCapitalAccountMapByInfoUuids(List<String> infoUuids);

    Response<CfInfoCapitalCompensationDO> getCfInfoCapitalCompensationDO(int caseId);

    Response<List<CfCapitalAccountRecord>> getByInfoUuidAndBizType(String infoUuid, int bizTypeCode);

    Response<List<CfDrawCashLaunchRecord>> getLaunchRecordByBankCard(String payeeBankCard);

    Response<List<CfDrawCashDetail>> getDrawCashDetailsByIds(List<Long> drawCashDetailIds);

    Response<List<AdminCfRefund>> getRefundByInfoUuids(List<String> infoUuids);

    Response<CfRefund> getCfRefundByInfoUuid(String infoUuid);

    Response<Void> updatePayeeName( int caseId,  String name, String idCardEncrypt);

    Response<CfDrawCashApplyVo> getApplyInfo(int caseId);

    Response<Map<Integer, CfDrawCashApplyVo>> getApplyInfoMap(List<Integer> caseIds);

    Response<Map<Integer, CfCashPauseBooleanPo>> checkPauseDrawCash(List<Integer> infoIdLis);

    Response<List<CfLaunchRecordVo>> getLaunchRecord(int caseId);

    Map<String, Object> getCapitalData(String infoUuid);

    FinanceState getFinanceState(int caseId);

    /**
     * 筹款人看到的金额详情
     *
     * @param infoUuid
     * @return
     */
    CfAmountInfoVo getFundraiserAmountInfo(String infoUuid);


    CfDrawCashLimit.CfAmountCaseLimitVo computeCashLimit(int caseId, int totalLimitAmount);

    String confirmDrawCashLimit(CfDrawCashLimit cashLimit);


}
