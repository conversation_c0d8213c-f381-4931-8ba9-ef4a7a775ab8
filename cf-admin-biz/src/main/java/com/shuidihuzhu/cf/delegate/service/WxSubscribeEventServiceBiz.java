package com.shuidihuzhu.cf.delegate.service;

import com.shuidihuzhu.wx.grpc.client.WxSubscribeModel;

import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/5/5 4:23 PM
 */
public interface WxSubscribeEventServiceBiz {

    List<WxSubscribeModel> batchCheckSubscribeByUserId(Long userId, List<Integer> thirdTypes);

    @Deprecated
    List<WxSubscribeModel> batchCheckSubscribeByUserIds(List<Long> userIds, Integer thirdTypes);

    Map<Long, List<WxSubscribeModel>> batchCheckSubscribeByUserIds(List<Long> userIds, List<Integer> thirdTypes);

    Boolean checkSubscribeByUserId(Long userId, int code);

}
