package com.shuidihuzhu.cf.delegate.crowdfunding.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.admin.util.AdminListUtil;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingOperationDelegate;
import com.shuidihuzhu.cf.domain.RemarkDO;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CfOperatingRecordEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.CrowdfundingInitialAuditInfo;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cf.model.timeline.TimeLineModel;
import com.shuidihuzhu.cf.vo.RemarkVO;
import com.shuidihuzhu.client.cf.api.chaifenbeta.crowdfunding.CrowdfundingOperationFeignClient;
import com.shuidihuzhu.client.cf.api.chaifenbeta.remark.RemarkServiceFeignClient;
import com.shuidihuzhu.client.cf.api.chaifenbeta.timeline.TimeLineServiceFeignClient;
import com.shuidihuzhu.client.cf.api.model.enums.ParamTypeEnum;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2019/6/12 3:12 PM
 */
@Service
@Slf4j
@RefreshScope
public class CrowdfundingOperationDelegate implements ICrowdfundingOperationDelegate {
    @Value("${cfbiz.unuse-cf-biz:false}")
    private boolean unuseCfBiz;
    private static final int defaultValue = 0;
    @Autowired
    private RemarkServiceFeignClient remarkServiceFeignClient;

    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;
    @Autowired
    private CrowdfundingOperationFeignClient crowdfundingOperationFeignClient;

    @Override
    public int insertCrowdfundingApprove(CrowdfundingApprove crowdfundingApprove) {
        FeignResponse<Integer> feignResponse = FeignResponse.fallback();
        try {
            feignResponse = crowdfundingFeignClient.insertCrowdfundingApprove(crowdfundingApprove);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + " insertCrowdfundingApprove err:", e);
        }
        log.info(this.getClass().getSimpleName() + " insertCrowdfundingApprove  request:{}  response:{}",
                JSON.toJSONString(crowdfundingApprove), feignResponse);
        return feignResponse.ok() ? feignResponse.getData() : defaultValue;
    }

    @Override
    public List<CrowdfundingApprove> getListByCrowdfundingId(Integer crowdfundingId) {
        FeignResponse<List<CrowdfundingApprove>> response = FeignResponse.fallback();
        try {
            response = crowdfundingFeignClient.getListByCrowdfundingId(crowdfundingId);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + " getListByCrowdfundingId err:", e);
        }
        log.info(this.getClass().getSimpleName() + " insertCrowdfundingApprove  request:{}  response:{}", crowdfundingId, response);
        return response.ok() ? response.getData() : Lists.newArrayList();
    }

    @Override
    public CrowdfundingApprove getLastWithCommentByCrowdfundingId(Integer crowdfundingId) {
        FeignResponse<CrowdfundingApprove> response = FeignResponse.fallback();
        try {
            response = crowdfundingFeignClient.getLastWithCommentByCrowdfundingId(crowdfundingId);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + " getLastWithCommentByCrowdfundingId err:", e);
        }
        log.info(this.getClass().getSimpleName() + " getLastWithCommentByCrowdfundingId  request:{}  response:{}", crowdfundingId, response);
        return response.ok() ? response.getData() : null;
    }

    @Deprecated
    @Override
    public void addApprove(CrowdfundingInfo crowdfundingInfo, String operation, String comment, long userId) {
        FeignResponse feignResponse = FeignResponse.fallback();
        try {
            feignResponse = crowdfundingFeignClient.addApprove(crowdfundingInfo, operation, comment, userId);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + " addApprove err:", e);
        }
        log.info(this.getClass().getSimpleName() + " addApprove  request:{}  response:{}",
                String.join("&", crowdfundingInfo.toString(), operation, comment, String.valueOf(userId)),
                feignResponse);
    }

    @Override
    public void saveRemarkDO(RemarkDO remarkDO) {
        String remarkDOStr = JSON.toJSONString(remarkDO);
        remarkServiceFeignClient.save(remarkDOStr, ParamTypeEnum.RemarkDO);
    }

    @Override
    public List<RemarkDO> listRemarkDOByCaseId(long caseId) {
        Response<List<String>> feignResponse = remarkServiceFeignClient.listByCaseId(caseId);
        List<RemarkDO> remarkDOList = Lists.newArrayList();
        if (feignResponse.ok()) {
            remarkDOList = AdminListUtil.getModelListFromResponse(feignResponse, RemarkDO.class);
        }
        return remarkDOList;
    }

    @Override
    public List<RemarkDO> listByCaseIdAndRemarkType(long caseId, int remarkType) {
        Response<List<String>> feignResponse = remarkServiceFeignClient.listByCaseIdAndRemarkType(caseId, remarkType);
        List<RemarkDO> remarkDOList = Lists.newArrayList();
        if (feignResponse.ok()) {
            remarkDOList = AdminListUtil.getModelListFromResponse(feignResponse, RemarkDO.class);
        }
        return remarkDOList;
    }

    @Override
    public List<RemarkDO> listByCaseIdAndRemarkTypes(long caseId, List<Integer> remarkTypes) {
        Response<List<String>> feignResponse = remarkServiceFeignClient.listByCaseIdAndRemarkTypes(caseId, remarkTypes);
        List<RemarkDO> remarkDOList = Lists.newArrayList();
        if (feignResponse.ok()) {
            remarkDOList = AdminListUtil.getModelListFromResponse(feignResponse, RemarkDO.class);
        }
        return remarkDOList;
    }

    @Override
    public RemarkDO getLastByCaseIdAndRemarkTypes(long caseId, List<Integer> remarkTypes) {
        Response<String> response = remarkServiceFeignClient.getLastByCaseIdAndRemarkTypes(caseId, remarkTypes);
        return response.ok() ? AdminListUtil.getModelFromResponse(response, RemarkDO.class) : null;
    }

    @Override
    public RemarkDO getRemarkDOById(long id) {
        Response<String> response = remarkServiceFeignClient.getById(id);
        return response.ok() ? AdminListUtil.getModelFromResponse(response, RemarkDO.class) : null;
    }

    @Override
    public RemarkVO convertVO(RemarkDO remarkDO) {
        String remarkDOStr = JSON.toJSONString(remarkDO);
        Response<String> response = remarkServiceFeignClient.convertVO(remarkDOStr, ParamTypeEnum.RemarkDO);
        return response.ok() ? AdminListUtil.getModelFromResponse(response, RemarkVO.class) : null;
    }

    @Override
    public void saveTimeLineModel(TimeLineModel timeLineModel) {
    }

    @Override
    public List<TimeLineModel> listTimeLineModelByCaseId(long caseId) {
        List<TimeLineModel> timeLineModels = Lists.newArrayList();

        return timeLineModels;
    }

    @Override
    public TimeLineModel getTimeLineModelById(long id) {

        return   null;
    }

    @Override
    public CfOperatingRecord saveCfOperatingRecord(String infoUuid, long userId, String userName, CfOperatingRecordEnum.Type type, CfOperatingRecordEnum.Role role) {
        if (type == null || role == null) {
            return null;
        }
        Response<String> response = crowdfundingOperationFeignClient.saveCfOperatingRecord(infoUuid, userId, userName, type.getCode(), role.getCode());
        return response.ok() ? AdminListUtil.getModelFromResponse(response, CfOperatingRecord.class) : null;
    }

    @Override
    public CfOperatingRecord getLastOneByType(String infoUuid, CfOperatingRecordEnum.Type type) {
        if (type == null) {
            return null;
        }
        Response<String> response = crowdfundingOperationFeignClient.getLastOneByType(infoUuid, type.getCode());
        return response.ok() ? AdminListUtil.getModelFromResponse(response, CfOperatingRecord.class) : null;
    }

    @Override
    public int insertOperatingRecord(CfOperatingRecord operatingRecord) {
        Response<Integer> response = crowdfundingOperationFeignClient.insertOperatingRecord(JSON.toJSONString(operatingRecord),
                ParamTypeEnum.CfOperatingRecord);
        return response.ok() ? response.getData() : defaultValue;
    }

    @Override
    public int addCrowdfundingOperation(CrowdfundingOperation crowdfundingOperation) {
        Response<Integer> response = crowdfundingOperationFeignClient.addCrowdfundingOperation(JSON.toJSONString(crowdfundingOperation),
                ParamTypeEnum.CrowdfundingOperation);
        return response.ok() ? response.getData() : defaultValue;
    }

    @Override
    public int updateCrowdfundingOperation(CrowdfundingOperation crowdfundingOperation) {
        Response<Integer> response = crowdfundingOperationFeignClient.updateCrowdfundingOperation(JSON.toJSONString(crowdfundingOperation),
                ParamTypeEnum.CrowdfundingOperation);
        return response.ok() ? response.getData() : defaultValue;
    }

    @Override
    public CrowdfundingOperation getByInfoId(String infoId) {
        Response<String> response = crowdfundingOperationFeignClient.getByInfoId(infoId);
        return response.ok() ? AdminListUtil.getModelFromResponse(response, CrowdfundingOperation.class) : null;
    }

    @Override
    public int updateReportStatus(int reportStatus, String infoUuid) {
        Response<Integer> response = crowdfundingOperationFeignClient.updateReportStatus(reportStatus, infoUuid);
        return response.ok() ? response.getData() : defaultValue;
    }

    @Override
    public int insertCrowdfundingInitialAuditInfo(CrowdfundingInitialAuditInfo param) {
        Response<Integer> response = crowdfundingOperationFeignClient.insertOrUpdateCrowdfundingInitialAuditInfo(JSON.toJSONString(param),
                ParamTypeEnum.CrowdfundingInitialAuditInfo);
        if (response.ok()){
            return response.getData();
        }
        throw new RuntimeException(CfErrorCode.SYSTEM_STRING_TO_RESPONSE_ERROR.getMsg());
    }

    @Override
    public CrowdfundingInitialAuditInfo selectCrowdfundingInitialAuditInfoByCaseId(int caseId) {
        Response<String> response = crowdfundingOperationFeignClient.selectCrowdfundingInitialAuditInfoByCaseId(caseId);
        if (response.ok()){
            return AdminListUtil.getModelFromResponse(response,CrowdfundingInitialAuditInfo.class);
        }
        throw new RuntimeException(CfErrorCode.SYSTEM_STRING_TO_RESPONSE_ERROR.getMsg());
    }

    @Override
    public int updateInitialAuditInfo(CrowdfundingInitialAuditInfo param) {
        Response<Integer> response = crowdfundingOperationFeignClient.insertOrUpdateCrowdfundingInitialAuditInfo(JSON.toJSONString(param),
                ParamTypeEnum.CrowdfundingInitialAuditInfo);
        if (response.ok()){
            return response.getData();
        }
        throw new RuntimeException(CfErrorCode.SYSTEM_STRING_TO_RESPONSE_ERROR.getMsg());
    }

    @Override
    public InitialAuditItem.RejectReasonSet parseRejectDetail(String rejectDetail, int caseId) {
        Response<String> response = crowdfundingOperationFeignClient.parseRejectDetail(rejectDetail, caseId);
        if (response.ok()){
            return AdminListUtil.getModelFromResponse(response,InitialAuditItem.RejectReasonSet.class);
        }
        throw new RuntimeException(CfErrorCode.SYSTEM_STRING_TO_RESPONSE_ERROR.getMsg());
    }

    @Override
    public CrowdfundingStatus checkDataUpdateCaseStatus(CrowdfundingInfo crowdfundingInfo, CfInfoExt cfInfoExt) {
        Response<String> response = crowdfundingOperationFeignClient.checkDataUpdateCaseStatus(JSON.toJSONString(cfInfoExt),
                ParamTypeEnum.CfInfoExt,
                crowdfundingInfo.getInfoId(),
                crowdfundingInfo.getId(),
                crowdfundingInfo.getStatus().value());
        if (response.ok()){
            return AdminListUtil.getModelFromResponse(response,CrowdfundingStatus.class);
        }
        throw new RuntimeException(CfErrorCode.SYSTEM_STRING_TO_RESPONSE_ERROR.getMsg());
    }

    @Override
    public int updateCaseInitialSubmit(int caseId, Boolean updateBaseInfo, Boolean updateFirstApprove, Boolean creditInfo) {
        Response<Integer> response = crowdfundingOperationFeignClient.updateCaseInitialSubmitV2(caseId, updateBaseInfo, updateFirstApprove, creditInfo);
        return response.ok()?response.getData():0;
    }


}
