package com.shuidihuzhu.cf.delegate.ai;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Stopwatch;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.shuidihuzhu.alps.feign.ocean.OceanApiClient;
import com.shuidihuzhu.alps.feign.ocean.OceanApiRequest;
import com.shuidihuzhu.alps.feign.ocean.OceanApiResponse;
import com.shuidihuzhu.cf.dao.ai.AiUgcRiskCheckRecordDao;
import com.shuidihuzhu.cf.domain.risk.AiUgcRiskCheckRecordDO;
import com.shuidihuzhu.cf.enhancer.subject.aiagent.AiAgent;
import com.shuidihuzhu.cf.enhancer.subject.aiagent.AiAgentImpl;
import com.shuidihuzhu.cf.enums.risk.UgcTypeEnum;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Import;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RefreshScope
@Import(value = AiAgentImpl.class)
public class AiUgcRiskDelegate {

    @Resource
    private AiAgent oceanApiClient;
    public static String TAG = "ai-dnn-ranker";
    public static String USER_ID = "10007";
    public static String TOKEN = "7ecf004d8b3d3683";

    @Autowired
    private AiUgcRiskCheckRecordDao aiUgcRiskCheckRecordDao;

    @Value("${apollo.ai-ugc-check.threshold:0.8}")
    public double threshold;

    public boolean checkHit(long bizId, UgcTypeEnum ugcTypeEnum, String content) {
        final AiUgcRiskResult result = check(bizId, content);
        if (result == null) {
            return false;
        }
        // 判断百分比
        final double score = result.getScore();
        record(bizId, ugcTypeEnum, content, score);
        return score >= threshold;
    }

    private void record(long bizId, UgcTypeEnum ugcTypeEnum, String content, double score) {
        final AiUgcRiskCheckRecordDO v = new AiUgcRiskCheckRecordDO();
        v.setBizId(bizId);
        v.setBizType(ugcTypeEnum.getValue());
        v.setScore(score);
        aiUgcRiskCheckRecordDao.insert(v);
    }

    private AiUgcRiskResult check(long bizId, String content) {
        log.info("AiUgcRiskDelegate in bizId {}, content {}", bizId, content);

        OceanApiRequest req = new OceanApiRequest();
        req.setTag(TAG);
        req.setUserId(USER_ID);
        req.setToken(TOKEN);

        final AiUgcRiskCheckParam param = new AiUgcRiskCheckParam();
        param.setBizId(bizId);
        param.setText(content);
        param.setStrategy("torch_similarity");
        req.setBody(JSON.toJSONString(param));
        final Stopwatch stopwatch = Stopwatch.createStarted();
        Response<OceanApiResponse> resp = oceanApiClient.agent(req, null);
        final long duration = stopwatch.stop().elapsed(TimeUnit.MILLISECONDS);
        log.info("AiUgcRiskDelegate duration {}ms, req {}, resp {}, ", duration, req, resp);
        if (resp == null || resp.notOk() || resp.getData() == null) {
            log.warn("AiUgcRiskDelegate ai error {}", resp);
            return null;
        }
        OceanApiResponse data = resp.getData();
        String body = data.getBody();
        return JSON.parseObject(body, AiUgcRiskResult.class);
    }

}
