package com.shuidihuzhu.cf.delegate.finance.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.admin.util.AdminListUtil;
import com.shuidihuzhu.cf.delegate.finance.IFinanceDelegate;
import com.shuidihuzhu.cf.domain.caseinfo.CfInfoCapitalCompensationDO;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.finance.client.feign.*;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.model.CfCapitalAccountRecord;
import com.shuidihuzhu.cf.finance.model.drawcash.CfDrawCashDetail;
import com.shuidihuzhu.cf.finance.model.drawcash.CfDrawCashLaunchRecord;
import com.shuidihuzhu.cf.finance.model.drawcash.CfDrawCashLimit;
import com.shuidihuzhu.cf.finance.model.financestate.FinanceState;
import com.shuidihuzhu.cf.finance.model.po.CfCashPauseBooleanPo;
import com.shuidihuzhu.cf.finance.model.vo.CfDrawCashApplyVo;
import com.shuidihuzhu.cf.finance.model.vo.CfLaunchRecordVo;
import com.shuidihuzhu.cf.finance.model.vo.draw.CfAmountInfoVo;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCfRefund;
import com.shuidihuzhu.cf.model.crowdfunding.CfCapitalAccount;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefund;
import com.shuidihuzhu.client.cf.api.chaifenbeta.cffinance.CfFinanceFeignClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2019/6/12 5:03 PM
 */
@Slf4j
@Service
@RefreshScope
public class FinanceDelegate implements IFinanceDelegate {
    @Autowired
    private CfFinanceFeignClient cfFinanceFeignClient;
    @Autowired
    private CfFinanceDrawCashFeignClient cfFinanceDrawCashFeignClient;
    @Autowired
    private CfFinanceCapitalAccountFeignClient cfFinanceCapitalAccountFeignClient;
    @Autowired
    private CfFinanceRefundFeignClient cfFinanceRefundFeignClient;
    @Autowired
    private CfFinancePauseFeignClient cfFinancePauseFeignClient;
    @Autowired
    private CfFinanceReadFeignClient cfFinanceReadFeignClient;
    @Autowired
    private CfFinanceFundStateFeignClient cfFinanceFundStateFeignClient;
    @Autowired
    private CfFinanceFeignClientV2 cfFinanceFeignClientV2;
    @Autowired
    private CfDrawCashFeignClient cashFeignClient;

    @Override
    public Response<CfCapitalAccount> getCfCapitalAccountByInfoUuid(String infoUuid) {
        try {
            FeignResponse<CfCapitalAccount> response = cfFinanceCapitalAccountFeignClient.capitalAccountGetByInfoUuid(infoUuid);
            if (response == null || response.notOk()) {
                log.error("capitalAccountGetByInfoUuid finance服务异常");
                return NewResponseUtil.makeError(AdminErrorCode.FINANCE_FEIGN_ERROR, null);
            } else {
                CfCapitalAccount cfCapitalAccount = response.getData();
                return ResponseUtil.makeSuccess(cfCapitalAccount);
            }
        } catch (Exception e) {
            log.error("capitalAccountGetByInfoUuid finance服务异常,", e);
            return NewResponseUtil.makeError(AdminErrorCode.FINANCE_FEIGN_ERROR, null);
        }
    }

    public Response<Map<String, CfCapitalAccount>> getCfCapitalAccountMapByInfoUuids(List<String> infoUuids) {
        try {
            FeignResponse<Map<String, CfCapitalAccount>> response =
                    cfFinanceCapitalAccountFeignClient.capitalAccountGetMapByInfoUuids(infoUuids);
            if (response == null || response.notOk()) {
                log.error("finance服务异常");
                return NewResponseUtil.makeError(AdminErrorCode.FINANCE_FEIGN_ERROR, Collections.EMPTY_MAP);
            }
            Map<String, CfCapitalAccount> cfCapitalAccountMap = Maps.newHashMap();
            if (response.getData() != null) {
                cfCapitalAccountMap.putAll(response.getData());
            }
            return ResponseUtil.makeSuccess(cfCapitalAccountMap);
        } catch (Exception e) {
            log.error("finance服务异常", e);
            return NewResponseUtil.makeError(AdminErrorCode.FINANCE_FEIGN_ERROR, Collections.EMPTY_MAP);
        }
    }

    @Override
    public Response<CfInfoCapitalCompensationDO> getCfInfoCapitalCompensationDO(int caseId) {
        try {
            Response<String> response = cfFinanceFeignClient.getCfInfoCapitalCompensationDO(caseId);
            if (response == null || response.notOk()) {
                log.error("getCfInfoCapitalCompensationDO finance服务异常，");
                return NewResponseUtil.makeError(AdminErrorCode.FINANCE_FEIGN_ERROR, null);
            }
            CfInfoCapitalCompensationDO result =
                    AdminListUtil.getModelFromResponse(response, CfInfoCapitalCompensationDO.class);
            return ResponseUtil.makeSuccess(result);
        } catch (Exception e) {
            log.error("getCfInfoCapitalCompensationDO finance服务异常，");
            return NewResponseUtil.makeError(AdminErrorCode.FINANCE_FEIGN_ERROR, null);
        }
    }

    @Override
    public Response<List<CfCapitalAccountRecord>> getByInfoUuidAndBizType(String infoUuid, int bizTypeCode) {
        try {
            FeignResponse<List<CfCapitalAccountRecord>> response =
                    cfFinanceCapitalAccountFeignClient.getRecordListByInfoUuidAndBizType(infoUuid, bizTypeCode);
            if (response == null || response.notOk()) {
                log.error("getRecordListByInfoUuidAndBizType finance服务异常，");
                return NewResponseUtil.makeError(AdminErrorCode.FINANCE_FEIGN_ERROR, Collections.EMPTY_LIST);
            }
            List<CfCapitalAccountRecord> result = Lists.newArrayList();
            if (response.getData() != null) {
                result.addAll(response.getData());
            }
            return ResponseUtil.makeSuccess(result);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + " getRecordListByInfoUuidAndBizType err:", e);
            return NewResponseUtil.makeError(AdminErrorCode.FINANCE_FEIGN_ERROR, Collections.EMPTY_LIST);
        }
    }

    @Override
    public Response<List<AdminCfRefund>> getRefundByInfoUuids(List<String> infoUuids) {
        try {
            List<AdminCfRefund> cfRefunds = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(infoUuids)) {
                FeignResponse<List<AdminCfRefund>> response = cfFinanceRefundFeignClient.getListByInfoUuids(infoUuids);
                if (response == null || response.notOk()) {
                    log.error("finance服务异常");
                    return NewResponseUtil.makeError(AdminErrorCode.FINANCE_FEIGN_ERROR, Collections.EMPTY_LIST);
                }
                if (CollectionUtils.isNotEmpty(response.getData())) {
                    cfRefunds.addAll(response.getData());
                }
            }
            return NewResponseUtil.makeSuccess(cfRefunds);
        } catch (Exception e) {
            log.error("finance服务异常,", e);
            return NewResponseUtil.makeError(AdminErrorCode.FINANCE_FEIGN_ERROR, Collections.EMPTY_LIST);
        }
    }

    @Override
    public Response<List<CfDrawCashLaunchRecord>> getLaunchRecordByBankCard(String payeeBankCard) {
        try {
            FeignResponse<List<CfDrawCashLaunchRecord>> response = cfFinanceDrawCashFeignClient.getLaunchByBankCard(payeeBankCard);
            if (response == null || response.notOk()) {
                log.error(" getLaunchByBankCard finance服务异常");
                return NewResponseUtil.makeError(AdminErrorCode.FINANCE_FEIGN_ERROR, Collections.EMPTY_LIST);
            } else {
                List<CfDrawCashLaunchRecord> launchRecords = Lists.newArrayList();
                if (response.getData() != null) {
                    launchRecords.addAll(response.getData());
                }
                return NewResponseUtil.makeSuccess(launchRecords);
            }
        } catch (Exception e) {
            log.error("getLaunchByBankCard finance服务异常,", e);
            return NewResponseUtil.makeError(AdminErrorCode.FINANCE_FEIGN_ERROR, Collections.EMPTY_LIST);
        }
    }

    @Override
    public Response<CfRefund> getCfRefundByInfoUuid(String infoUuid) {
        try {
            FeignResponse<CfRefund> refundFeignResponse = cfFinanceRefundFeignClient.getByInfoUuid(infoUuid);
            if (refundFeignResponse == null || refundFeignResponse.notOk()) {
                log.error("finance服务异常");
                return NewResponseUtil.makeError(AdminErrorCode.FINANCE_FEIGN_ERROR, null);
            }
            CfRefund cfRefund = refundFeignResponse.getData();
            return NewResponseUtil.makeSuccess(cfRefund);
        } catch (Exception e) {
            log.error("finance服务异常，", e);
            return NewResponseUtil.makeError(AdminErrorCode.FINANCE_FEIGN_ERROR, null);
        }
    }

    @Override
    public Response<List<CfDrawCashDetail>> getDrawCashDetailsByIds(List<Long> drawCashDetailIds) {
        try {
            FeignResponse<List<CfDrawCashDetail>> response = cfFinanceDrawCashFeignClient.getDrawCashDetailByIds(drawCashDetailIds);
            if (response == null || response.notOk()) {
                log.error("finance服务异常");
                return NewResponseUtil.makeError(AdminErrorCode.FINANCE_FEIGN_ERROR, Collections.EMPTY_LIST);
            } else {
                List<CfDrawCashDetail> details = Lists.newArrayList();
                if (response.getData() != null) {
                    details.addAll(response.getData());
                }
                return NewResponseUtil.makeSuccess(details);
            }
        } catch (Exception e) {
            log.error("finance服务异常,", e);
            return NewResponseUtil.makeError(AdminErrorCode.FINANCE_FEIGN_ERROR, Collections.EMPTY_LIST);
        }
    }

    @Override
    public Response<Void> updatePayeeName(int caseId, String name, String idCardEncrypt) {
        try {
            FeignResponse<Void> response = cfFinanceDrawCashFeignClient.updatePayeeName(caseId, name, idCardEncrypt);
            if (response == null || response.notOk()) {
                log.error("finance服务异常");
                return NewResponseUtil.makeError(AdminErrorCode.FINANCE_FEIGN_ERROR, null);
            } else {
                return NewResponseUtil.makeSuccess(null);
            }
        } catch (Exception e) {
            log.error("finance服务异常,", e);
            return NewResponseUtil.makeError(AdminErrorCode.FINANCE_FEIGN_ERROR, null);
        }
    }

    @Override
    public Response<CfDrawCashApplyVo> getApplyInfo(int caseId) {
        try {
            FeignResponse<CfDrawCashApplyVo> response = cfFinanceDrawCashFeignClient.getApplyInfo(caseId);
            log.debug("caseId:{} response:{}", caseId, JSON.toJSONString(response));
            if (response == null || response.notOk()) {
                log.error("finance服务异常");
                return NewResponseUtil.makeError(AdminErrorCode.FINANCE_FEIGN_ERROR, null);
            } else {
                return NewResponseUtil.makeSuccess(response.getData());
            }
        } catch (Exception e) {
            log.error("finance服务异常,", e);
            return NewResponseUtil.makeError(AdminErrorCode.FINANCE_FEIGN_ERROR, null);
        }
    }

    @Override
    public Response<Map<Integer, CfDrawCashApplyVo>> getApplyInfoMap(List<Integer> caseIds) {
        if (CollectionUtils.isEmpty(caseIds)) {
            return NewResponseUtil.makeSuccess(Maps.newHashMap());
        }
        try {
            FeignResponse<List<CfDrawCashApplyVo>> response = cfFinanceDrawCashFeignClient.getApplyInfoList(caseIds);
            if (response == null || response.notOk()) {
                log.error("finance服务异常");
                return NewResponseUtil.makeError(CfErrorCode.USER_IN_BLACKLIST, Maps.newHashMap());
            }
            List<CfDrawCashApplyVo> cfDrawCashApplyVoList = response.getData();
            Map<Integer, CfDrawCashApplyVo> drawCashApplyVoMap = cfDrawCashApplyVoList.stream()
                    .collect(Collectors.toMap(CfDrawCashApplyVo::getCaseId, Function.identity(), (a, b) -> b));
            return NewResponseUtil.makeSuccess(drawCashApplyVoMap);
        } catch (Exception e) {
            log.error("finance服务异常,", e);
            return NewResponseUtil.makeError(CfErrorCode.USER_IN_BLACKLIST, Maps.newHashMap());
        }
    }

    @Override
    public Response<Map<Integer, CfCashPauseBooleanPo>> checkPauseDrawCash(List<Integer> infoIdLis) {
        if (CollectionUtils.isEmpty(infoIdLis)) {
            return NewResponseUtil.makeSuccess(Maps.newHashMap());
        }
        try {
            FeignResponse<Map<Integer, CfCashPauseBooleanPo>> response = cfFinancePauseFeignClient.checkPauseDrawCashList(infoIdLis);
            if (response == null || response.notOk()) {
                log.error("finance服务异常");
                return NewResponseUtil.makeError(CfErrorCode.USER_IN_BLACKLIST, Maps.newHashMap());
            }
            Map<Integer, CfCashPauseBooleanPo> map = response.getData();
            if (null == map) {
                map = Maps.newHashMap();
            }
            return NewResponseUtil.makeSuccess(map);
        } catch (Exception e) {
            log.error("finance服务异常,", e);
            return NewResponseUtil.makeError(CfErrorCode.USER_IN_BLACKLIST, Maps.newHashMap());
        }
    }

    @Override
    public Response<List<CfLaunchRecordVo>> getLaunchRecord(int caseId) {
        try {
            FeignResponse<List<CfLaunchRecordVo>> response = cfFinanceDrawCashFeignClient.getLaunchRecord(caseId);
            log.debug("caseId:{} response:{}", caseId, JSON.toJSONString(response));
            if (response == null || response.notOk()) {
                log.error("finance服务异常");
                return NewResponseUtil.makeError(AdminErrorCode.FINANCE_FEIGN_ERROR, Lists.newArrayList());
            } else {
                return NewResponseUtil.makeSuccess(response.getData());
            }
        } catch (Exception e) {
            log.error("finance服务异常,", e);
            return NewResponseUtil.makeError(AdminErrorCode.FINANCE_FEIGN_ERROR, Lists.newArrayList());
        }
    }

    @Override
    public Map<String, Object> getCapitalData(String infoUuid) {
        Map<String, Object> result = Maps.newHashMap();
        result.put("cfCapitalStatus", 0);
        result.put("cfCapitalDetailBaseList", Lists.newArrayList());
        result.put("cfCapitalDetailOtherList", Lists.newArrayList());
        if (StringUtils.isBlank(infoUuid)) {
            return Maps.newHashMap();
        }
        try {
            FeignResponse<Map<String, Object>> response = cfFinanceReadFeignClient.getCapitalData(infoUuid);
            log.info("获取资金信息记录：{}", JSON.toJSONString(response));
            if (response == null || response.notOk()) {
                log.error("无法获取资金信息记录 infoUuid:{}", infoUuid);
            } else {
                return response.getData();
            }
        } catch (Exception e) {
            log.error("资金信息记录获取异常", e);
        }
        return result;
    }

    /**
     * 获取资金状态兼容版
     *
     * @param caseId
     * @return
     */
    @Override
    public FinanceState getFinanceState(int caseId) {

        FeignResponse<FinanceState> response = cfFinanceFundStateFeignClient.financeState(caseId);
        log.info("查询到资金状态:{}", JSON.toJSONString(response));
        if (response.notOk()) {
            log.error("资金状态查询异常caseId:{}", caseId);
            return null;
        }
        return response.getData();
    }

    /**
     * 筹款人看到的金额详情
     *
     * @param infoUuid
     * @return
     */
    @Override
    public CfAmountInfoVo getFundraiserAmountInfo(String infoUuid) {
        if (StringUtils.isBlank(infoUuid)) {
            return null;
        }
        if ("6e625377-582d-4e54-931f-49866a307629".equals(infoUuid)) {
            CfAmountInfoVo cfAmountInfoVo = new CfAmountInfoVo();
            cfAmountInfoVo.setShowDrawCashButton(cfAmountInfoVo.isCanSplitDrawForC());
            return cfAmountInfoVo;
        }
        com.shuidihuzhu.cf.finance.client.response.model.FeignResponse<CfAmountInfoVo> response
                = cfFinanceFeignClientV2.getFundraiserAmountInfo(infoUuid);
        CfAmountInfoVo cfAmountInfoVo = Optional.ofNullable(response)
                .filter(com.shuidihuzhu.cf.finance.client.response.model.FeignResponse::ok)
                .map(com.shuidihuzhu.cf.finance.client.response.model.FeignResponse::getData)
                .orElse(null);
        if (cfAmountInfoVo != null) {
            // 将展示 是否提现的按钮 替换为 是否支持 C端随筹随取
            cfAmountInfoVo.setShowDrawCashButton(cfAmountInfoVo.isCanSplitDrawForC());
        }
        return cfAmountInfoVo;
    }


    @Override
    public CfDrawCashLimit.CfAmountCaseLimitVo computeCashLimit(int caseId, int totalLimitAmount) {

        FeignResponse<CfDrawCashLimit.CfAmountCaseLimitVo> response =  cashFeignClient
                .computeCashLimit(new CfDrawCashLimit.CfAmountCaseLimitParam(caseId, totalLimitAmount));

        if (response == null || Objects.equals(response.getMsg(), "fallback")) {
            throw new RuntimeException("接口调用失败， 待会再试");
        }

        if (response.notOk() || response.getData() == null) {
            throw new RuntimeException(response.getMsg());
        }

        return response.getData();
     }

     public String confirmDrawCashLimit(CfDrawCashLimit cashLimit) {
        if (cashLimit.getCurLimitAmount() < 0) {
            return "";
        }
        FeignResponse<String> result =  cfFinanceDrawCashFeignClient.insertOrUpdateCashLimit(cashLimit);
        if (result == null || Objects.equals(result.getMsg(), "fallback")) {
            return "接口调用失败， 待会再试";
        }
        return result.getMsg();
     }
}
