package com.shuidihuzhu.cf.delegate.volunteer;

import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteerVo;
import com.shuidihuzhu.client.cf.growthtool.model.PageReturnModel;
import com.shuidihuzhu.client.cf.growthtool.model.VolunteerSearchModel;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2019/9/25 5:18 PM
 */
public interface IVolunteerDelegate {

    Response<Integer> addVolunteer(CrowdfundingVolunteer crowdfundingVolunteer);

    PageReturnModel<CrowdfundingVolunteer> getVolunteer(VolunteerSearchModel volunteerSearchModel, boolean needPage, Integer pageNo, Integer pageSize);

    CrowdfundingVolunteerVo getCrowdfundingVolunteerVoById(Long id);

    Response<Integer> updateVolunteerInfoById(CrowdfundingVolunteer crowdfundingVolunteer);

    Integer updateQrCode(String qrCode,
                         Long id);

    Boolean checkIsExistByTypeOrIdentity(Integer volunteerType,
                                         String idCardNumber,
                                         Long vid);

    Boolean checkIsExistByTypeOrEmail(Integer volunteerType,
                                      String email,
                                      Long vid);

    Integer updateApplyStatusById(Long id,
                                  Integer applyStatus,
                                  String operatorName,
                                  Integer operatorUserId,
                                  String angelUrl,
                                  String refuseReasons,
                                  String qrCode);

    List<String> getVolunteerUniqueByMobileAndWorkStatus(int workStatus,
                                                         String mobile);

    List<CrowdfundingVolunteer> getCfVolunteerDOByUniqueCodes(List<String> uniqueCodes);

    CrowdfundingVolunteer getVolunteerByUniqueCode(String uniqueCode);

    Boolean checkRegister(String mobile);
}
