package com.shuidihuzhu.cf.delegate.crowdfunding;

import com.shuidihuzhu.cf.domain.RemarkDO;
import com.shuidihuzhu.cf.enums.crowdfunding.CfOperatingRecordEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingOperationEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.CrowdfundingInitialAuditInfo;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cf.model.timeline.TimeLineModel;
import com.shuidihuzhu.cf.vo.RemarkVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: wanghui
 * @create: 2019/6/12 3:11 PM
 */
public interface ICrowdfundingOperationDelegate {
    int insertCrowdfundingApprove(CrowdfundingApprove crowdfundingApprove);

    /**
     * @deprecated use {@link com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService#listByCaseId(int)} instead
     */
    @Deprecated
    List<CrowdfundingApprove> getListByCrowdfundingId(Integer crowdfundingId);

    CrowdfundingApprove getLastWithCommentByCrowdfundingId(Integer crowdfundingId);

    /**
     * @deprecated use {@link com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService#add(int, int, String, String)} instead
     */
    @Deprecated
    void addApprove(CrowdfundingInfo crowdfundingInfo, String operation, String comment, long userId);

    void saveRemarkDO(RemarkDO remarkDO);

    List<RemarkDO> listRemarkDOByCaseId(long caseId);

    List<RemarkDO> listByCaseIdAndRemarkType(long caseId, int remarkType);

    List<RemarkDO> listByCaseIdAndRemarkTypes(long caseId, List<Integer> remarkTypes);

    RemarkDO getLastByCaseIdAndRemarkTypes(long caseId, List<Integer> remarkTypes);

    RemarkDO getRemarkDOById(long id);

    RemarkVO convertVO(RemarkDO remarkDO);

    void saveTimeLineModel(TimeLineModel timeLineModel);

    List<TimeLineModel> listTimeLineModelByCaseId(long caseId);

    TimeLineModel getTimeLineModelById(long id);

    CfOperatingRecord saveCfOperatingRecord(String infoUuid, long userId, String userName, CfOperatingRecordEnum.Type type,
                           CfOperatingRecordEnum.Role role);

    CfOperatingRecord getLastOneByType(String infoUuid, CfOperatingRecordEnum.Type type);

    /**
     * 插入操作记录
     * @param operatingRecord
     * @return
     */
    int insertOperatingRecord(CfOperatingRecord operatingRecord);

    int addCrowdfundingOperation(CrowdfundingOperation crowdfundingOperation);

    int updateCrowdfundingOperation(CrowdfundingOperation crowdfundingOperation);

    CrowdfundingOperation getByInfoId(String infoId);

    int updateReportStatus(int reportStatus, String infoUuid);


    int insertCrowdfundingInitialAuditInfo(CrowdfundingInitialAuditInfo param);

    CrowdfundingInitialAuditInfo selectCrowdfundingInitialAuditInfoByCaseId(int caseId);

    int updateInitialAuditInfo(CrowdfundingInitialAuditInfo param);

    InitialAuditItem.RejectReasonSet parseRejectDetail(String rejectDetail, int caseId);

    CrowdfundingStatus checkDataUpdateCaseStatus(CrowdfundingInfo crowdfundingInfo, CfInfoExt cfInfoExt);

    int updateCaseInitialSubmit(int caseId, Boolean updateBaseInfo,
                                Boolean updateFirstApprove, Boolean creditInfo);
}
