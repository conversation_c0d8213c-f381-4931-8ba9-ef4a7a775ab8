package com.shuidihuzhu.cf.delegate;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.feign.PreposeMaterialClient;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.delegate.risk.impl.RiskDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.baseservice.pay.enums.BankCardVerifyEnum;
import com.shuidihuzhu.client.cf.growthtool.client.ClewPreproseMaterialFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.CfCaseSpecialPrePoseDetail;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-01-06 14:49
 **/
@Service
@Slf4j
public class PreposeMaterialDelegate {

    @Autowired
    PreposeMaterialClient preposeMaterialClient;
    @Autowired
    RiskDelegate riskDelegate;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private ClewPreproseMaterialFeignClient preproseFeignClient;

    public PreposeMaterialModel.MaterialInfoVo queryByCaseId(int caseId) {
        CfFirsApproveMaterial firsApproveMaterial = riskDelegate.getCfFirsApproveMaterialByInfoId(caseId);
        if (firsApproveMaterial == null) {
            log.warn("firsApproveMaterial is null, caseId:{}", caseId);
            return null;
        }
        String patientRealName = firsApproveMaterial.getPatientRealName();
        int patientIdType = firsApproveMaterial.getPatientIdType();
        BankCardVerifyEnum.UserIdentityType userIdentityType = BankCardVerifyEnum.UserIdentityType.fromCode(patientIdType);
        String patientCard = "";
        switch (userIdentityType) {
            case IDENTITY:
                patientCard = shuidiCipher.decrypt(firsApproveMaterial.getPatientCryptoIdcard());
                break;
            case BIRTH:
                patientCard = firsApproveMaterial.getPatientBornCard();
                break;
            default:
                patientCard = shuidiCipher.decrypt(firsApproveMaterial.getPatientCryptoIdcard());
                break;
        }

        RpcResult<PreposeMaterialModel.MaterialInfoVo> materialInfoVoRpcResult = preposeMaterialClient.selectLatelyByIdCard(patientRealName, patientCard, patientIdType);
        if (materialInfoVoRpcResult.isFail() || materialInfoVoRpcResult.getData() == null) {
            log.warn("获取前置报备信息位空 patientRealName={}, patientCard={},materialInfoVoRpcResult:{}",patientRealName,patientCard,materialInfoVoRpcResult);
            return null;
        }
        return materialInfoVoRpcResult.getData();
    }

    public boolean isCaseSpecialPrePose(int caseId) {
        CfCaseSpecialPrePoseDetail prepose = getSpecialPrePoseDetailByCaseId(caseId);

        return prepose != null && prepose.getSpecialPrePose() == 1;
    }

    public CfCaseSpecialPrePoseDetail getSpecialPrePoseDetailByCaseId(int caseId) {
        List<CfCaseSpecialPrePoseDetail> resp = getSpecialPrePoseDetailByCaseIds(Lists.newArrayList(caseId));

        return CollectionUtils.isNotEmpty(resp) ? resp.get(0) : new CfCaseSpecialPrePoseDetail();
    }

    public List<CfCaseSpecialPrePoseDetail> getSpecialPrePoseDetailByCaseIds(List<Integer> caseIds) {

        if (CollectionUtils.isEmpty(caseIds)) {
            return Lists.newArrayList();
        }

        Response<List<CfCaseSpecialPrePoseDetail>> resp = preproseFeignClient.getSpecialPrePoseDetail(caseIds);
        log.info("查询案例的特殊报备情况caseIds:{} result:{}", caseIds, JSON.toJSONString(resp));

        return resp != null && resp.getData() != null ? resp.getData() : Lists.newArrayList();
    }
}
