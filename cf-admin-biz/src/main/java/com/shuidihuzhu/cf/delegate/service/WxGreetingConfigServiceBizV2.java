package com.shuidihuzhu.cf.delegate.service;

import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.wx.grpc.client.common.CommonResponse;
import com.shuidihuzhu.wx.grpc.client.common.GreetingMatchMethod;
import com.shuidihuzhu.wx.grpc.client.common.QueryAttachHistoryV2;
import com.shuidihuzhu.wx.grpc.model.GreetingWxGroupAttachV2Dto;
import com.shuidihuzhu.wx.grpc.model.QueryAttachHistoryV2Dto;
import com.shuidihuzhu.wx.grpc.model.WxGreetingConfigPo;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/5/4 8:01 PM
 */
public interface WxGreetingConfigServiceBizV2 {

    List<WxGreetingConfigPo> queryGreetingV2(int current, int pageSize, String keyword);

    Response<Boolean> addGreetingV2(List<WxGreetingConfigPo> wxGreetingConfigs);

    Response<Boolean> modifyGreetingV2(List<WxGreetingConfigPo> wxGreetingConfigs);

    List<WxGreetingConfigPo> listGreetingV2(int current, int pageSize);

    Integer totalCountV2();

    Integer totalCountByCondition(String keyword);

    Response<Boolean> addAttachV2(int wxGroupId, int contentId, int subBizType, List<GreetingMatchMethod> greetingMatchMethods);

    List<GreetingWxGroupAttachV2Dto> queryAttachV2(int groupId);

    List<WxGreetingConfigPo> queryByContentId(Integer contentId);

    List<QueryAttachHistoryV2Dto> queryAttachHistoryV2(int current, int pageSize, Integer thirdType, String attachScene);

    Integer totalAttachV2(Integer thirdType, String attachScene);

    void addAttachKeyItem(String attachKey, String attachScene);

}
