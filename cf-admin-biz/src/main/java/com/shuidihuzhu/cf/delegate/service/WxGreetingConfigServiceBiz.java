package com.shuidihuzhu.cf.delegate.service;

import com.shuidihuzhu.cf.vo.wx.greeting.AttachKeyVO;
import com.shuidihuzhu.wx.grpc.client.common.Greeting;
import com.shuidihuzhu.wx.grpc.client.common.WxConfig;
import com.shuidihuzhu.wx.grpc.model.AttacheKeyItemDto;
import com.shuidihuzhu.wx.grpc.model.WxConfigModel;

import java.util.List;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/5/4 7:12 PM
 */
public interface WxGreetingConfigServiceBiz {

    List<WxConfigModel> queryWxMpList();

    Greeting queryById(int id);

    List<AttacheKeyItemDto> queryAttachKeys();

    void delGreeting(int greetingId);

    void delAttach(int attachId);

}
