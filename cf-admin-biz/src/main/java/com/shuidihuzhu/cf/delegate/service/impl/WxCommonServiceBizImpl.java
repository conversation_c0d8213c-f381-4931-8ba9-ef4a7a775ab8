package com.shuidihuzhu.cf.delegate.service.impl;

import com.shuidihuzhu.cf.delegate.service.WxCommonServiceBiz;
import com.shuidihuzhu.wx.grpc.client.WxCommonGrpcClient;
import com.shuidihuzhu.wx.grpc.client.feign.WxCommonServiceClient;
import com.shuidihuzhu.wx.grpc.model.WxTagModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/5/4 4:04 PM
 */
@Slf4j
@Service
@RefreshScope
public class WxCommonServiceBizImpl implements WxCommonServiceBiz {

    @Resource
    private WxCommonServiceClient wxCommonServiceClient;

    @Override
    public List<WxTagModel> getTagList(int groupId) {
        return wxCommonServiceClient.getTagList(groupId);
    }
}
