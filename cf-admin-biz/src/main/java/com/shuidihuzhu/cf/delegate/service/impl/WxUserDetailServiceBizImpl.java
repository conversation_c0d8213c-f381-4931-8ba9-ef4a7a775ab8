package com.shuidihuzhu.cf.delegate.service.impl;

import com.shuidihuzhu.account.model.service.WxUserDetailModel;
import com.shuidihuzhu.cf.delegate.service.WxUserDetailServiceBiz;
import com.shuidihuzhu.client.account.v1.accountservice.WxUserDetail;
import com.shuidihuzhu.client.grpc.account.v1.WxUserDetailGrpClient;
import com.shuidihuzhu.client.grpc.account.v1.feign.WxUserDetailServiceClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/5/6 5:19 PM
 */
@Slf4j
@Service
@RefreshScope
public class WxUserDetailServiceBizImpl implements WxUserDetailServiceBiz {

    @Resource
    private WxUserDetailServiceClient wxUserDetailServiceClient;


    @Override
    public List<WxUserDetailModel> getByUserIdBatch(Set<Long> userIds) {
        return wxUserDetailServiceClient.getByUserIdBatch(userIds);
    }
}
