package com.shuidihuzhu.cf.delegate.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.delegate.service.WxSubscribeEventServiceBiz;
import com.shuidihuzhu.wx.grpc.client.WxSubscribeEventGrpcClient;
import com.shuidihuzhu.wx.grpc.client.WxSubscribeModel;
import com.shuidihuzhu.wx.grpc.client.feign.WxSubscribeEventServiceClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/5/5 4:23 PM
 */
@Slf4j
@Service
@RefreshScope
public class WxSubscribeEventServiceBizImpl implements WxSubscribeEventServiceBiz {

    @Resource
    private WxSubscribeEventGrpcClient wxSubscribeEventGrpcClient;
    @Resource
    private WxSubscribeEventServiceClient wxSubscribeEventServiceClient;

    /**
     * TODO 等用户中心补充
     */
    @Override
    public List<WxSubscribeModel> batchCheckSubscribeByUserId(Long userId, List<Integer> thirdTypes) {
        List<com.shuidihuzhu.wx.grpc.model.WxSubscribeModel> wxSubscribeModel = wxSubscribeEventServiceClient.batchCheckSubscribeByUserId(userId, thirdTypes);
        if (CollectionUtils.isEmpty(wxSubscribeModel)) {
            return Lists.newArrayList();
        }
        return wxSubscribeModel.stream().map(model -> {
            return WxSubscribeModel.newBuilder()
                    .setIsSubscribe(model.getIsSubscribe())
                    .setEventKey(model.getEventKey())
                    .setThirdType(model.getThirdType())
                    .setUserId(model.getUserId())
                    .setSubscribeTime(model.getSubscribeTime())
                    .build();
        }).collect(Collectors.toList());
    }

    @Override
    public List<WxSubscribeModel> batchCheckSubscribeByUserIds(List<Long> userIds, Integer thirdType) {
        List<com.shuidihuzhu.wx.grpc.model.WxSubscribeModel> wxSubscribeModel = wxSubscribeEventServiceClient.batchCheckSubscribeByUserIds(userIds, thirdType);
        if (CollectionUtils.isEmpty(wxSubscribeModel)) {
            return Lists.newArrayList();
        }
        return wxSubscribeModel.stream().map(model -> {
            return WxSubscribeModel.newBuilder()
                    .setIsSubscribe(model.getIsSubscribe())
                    .setEventKey(model.getEventKey())
                    .setThirdType(model.getThirdType())
                    .setUserId(model.getUserId())
                    .setSubscribeTime(model.getSubscribeTime())
                    .build();
        }).collect(Collectors.toList());
    }

    @Override
    public Map<Long, List<WxSubscribeModel>> batchCheckSubscribeByUserIds(List<Long> userIds, List<Integer> thirdTypes) {
        Map<Long, List<com.shuidihuzhu.wx.grpc.model.WxSubscribeModel>> listMap = wxSubscribeEventServiceClient.batchCheckSubscribeByUserIds(userIds, thirdTypes);
        return listMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().stream().map(wxSubscribeModel -> {
            WxSubscribeModel model = WxSubscribeModel.newBuilder().build();
            BeanUtils.copyProperties(wxSubscribeModel, model);
            return model;
        }).collect(Collectors.toList())));
    }

    @Override
    public Boolean checkSubscribeByUserId(Long userId, int code) {
        return wxSubscribeEventServiceClient.checkSubscribeByUserId(userId, code);
    }
}
