package com.shuidihuzhu.cf.delegate.saas;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthGroupDto;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

@Data
public class AdminOrganization {

    private int id;

    private String name;

    private long parentOrgId;

    List<AdminOrganization> chidrenOrgs;


    public static AdminOrganization convertFromAuthGroupDto(AuthGroupDto dto) {
        if (dto == null) {
            return null;
        }

        AdminOrganization org = new AdminOrganization();
        if (dto.getGroupBizId() != null) {
            org.setId(Math.toIntExact(dto.getGroupBizId()));
        }
        org.setName(dto.getGroupName());
        if (dto.getParentId() != null) {
            org.setParentOrgId(dto.getParentId());
        }

        if (CollectionUtils.isNotEmpty(dto.getChidrenGroups())) {
            List<AdminOrganization> chidrenOrgs = Lists.newArrayList();
            for (AuthGroupDto subOrg : dto.getChidrenGroups()) {
                chidrenOrgs.add(convertFromAuthGroupDto(subOrg));
            }
            org.setChidrenOrgs(chidrenOrgs);
        }

        return org;
    }

    public static List<AdminOrganization> convertFromAuthGroupDto(List<AuthGroupDto> dtoList) {
        List<AdminOrganization>  result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(dtoList)) {
            return result;
        }

        for (AuthGroupDto org : dtoList) {
            result.add(convertFromAuthGroupDto(org));
        }

        return result;
    }


    public static void main(String[] args) {
        AuthGroupDto dt1 = new AuthGroupDto();


        AuthGroupDto dt12 = new AuthGroupDto();
        AuthGroupDto dt121 = new AuthGroupDto();
        dt121.setGroupBizId(121l);
        dt121.setGroupName("121");

        AuthGroupDto dt122 = new AuthGroupDto();
        dt122.setGroupBizId(122l);
        dt122.setGroupName("122");

        dt12.setChidrenGroups(Lists.newArrayList(dt121, dt122));
        dt12.setGroupName("abc");

        dt1.setGroupName("dt1");
        dt1.setChidrenGroups(Lists.newArrayList(dt12));

        System.out.println(JSON.toJSONString(convertFromAuthGroupDto(dt1)));

    }
}
