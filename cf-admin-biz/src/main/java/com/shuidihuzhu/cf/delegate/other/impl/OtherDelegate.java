package com.shuidihuzhu.cf.delegate.other.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.admin.util.AdminListUtil;
import com.shuidihuzhu.cf.delegate.other.IOtherDelegate;
import com.shuidihuzhu.cf.domain.dedicated.CfUserVolunteerRelationDO;
import com.shuidihuzhu.cf.model.crowdfunding.CfTouFangSign;
import com.shuidihuzhu.cf.service.notice.urgeraise.UrgeRaiseMsgType;
import com.shuidihuzhu.cf.vo.AnchorPageBigIntVo;
import com.shuidihuzhu.client.cf.api.chaifenbeta.other.OtherFeginClient;
import com.shuidihuzhu.client.cf.api.model.enums.ParamTypeEnum;
import com.shuidihuzhu.client.cf.growthtool.client.CfGrowthtoolFeginClient;
import com.shuidihuzhu.client.cf.growthtool.client.CfGrowthtoolVolunteerFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.CfVolunteerMaterialDO;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @author: wanghui
 * @create: 2019/6/13 11:38 AM
 */
@Slf4j
@Service
@RefreshScope
public class OtherDelegate implements IOtherDelegate {
    @Autowired
    private CfGrowthtoolFeginClient cfGrowthtoolFeginClient;
    @Autowired
    private OtherFeginClient otherFeginClient;
    @Autowired
    private CfGrowthtoolVolunteerFeignClient cfGrowthtoolVolunteerFeignClient;

    private static final int defaultValue = 0;
    @Override
    public int insertCfTouFangSignList(List<CfTouFangSign> cfTouFangSigns) {
        Response<Integer> response = cfGrowthtoolFeginClient.insertCfTouFangSignList(AdminListUtil.getListStringFromListModel(cfTouFangSigns),
                ParamTypeEnum.CfTouFangSign);
        return response.ok() ? response.getData() : defaultValue;
    }


    @Override
    public Response<Integer> insertCfTransformArticle(String title, String url, String imgUrl) {
        return otherFeginClient.insertCfTransformArticle(title, url, imgUrl);
    }

    @Override
    public Response<Integer> deleteCfTransformArticle(long id) {
        return otherFeginClient.deleteCfTransformArticle(id);
    }

    @Override
    public Response<AnchorPageBigIntVo> listCfTransformArticleByAnchor(long anchorId, int size) {
        Response<String> response = otherFeginClient.listCfTransformArticleByAnchor(anchorId, size);
        if(response.ok()){
            return NewResponseUtil.makeSuccess(AdminListUtil.getModelFromResponse(response,AnchorPageBigIntVo.class));
        }
        log.error("listCfTransformArticleByAnchor fegin接口返回失败:{}", JSON.toJSONString(response));
        return ResponseUtil.makeError(ErrorCode.SYSTEM_STRING_TO_RESPONSE_ERROR);
    }

    @Override
    public Response listCfTransformArticleByPage(int current, int pageSize) {
        return otherFeginClient.listCfTransformArticleByPage(current, pageSize);
    }

    @Override
    public Response listByPageAndTitleKey(int current, int pageSize, String titleKey) {
        return otherFeginClient.listByPageAndTitleKey(current, pageSize, titleKey);
    }


    @Override
    public boolean checkCouldSend(long userId, UrgeRaiseMsgType rightMsgType) {
        Response<Boolean> response = otherFeginClient.checkCouldSend(userId, JSON.toJSONString(rightMsgType),ParamTypeEnum.UrgeRaiseMsgType);
        if(response.ok()){
            return response.getData();
        }
        log.error("checkCouldSend fegin接口返回失败 :{}", JSON.toJSONString(response));
        return false;
    }

    @Override
    public CfUserVolunteerRelationDO getAccountUserAndVolunteerRelationByPhone(String phone) {
        Response<String> response = cfGrowthtoolVolunteerFeignClient.getVolunteerRelationByMobile(phone);
        if(response.ok()){
            return AdminListUtil.getModelFromResponse(response, CfUserVolunteerRelationDO.class);
        }
        log.error("getAccountUserAndVolunteerRelationByPhone fegin接口返回失败 :{}", JSON.toJSONString(response));
        return null;
    }

    @Override
    public CfVolunteerMaterialDO getVolunteerMateri(String uniqueCode) {
        if (StringUtils.isBlank(uniqueCode)) return null;
        Response<CfVolunteerMaterialDO> response = cfGrowthtoolVolunteerFeignClient.getVolunteerMateriByUniqueCode(uniqueCode);
        return response.ok()?response.getData():null;
    }

    @Override
    public int addCfVolunteerMaterial(CfVolunteerMaterialDO materialDO) {
        Response<Integer> response = cfGrowthtoolVolunteerFeignClient.addCfVolunteerMaterial(JSON.toJSONString(materialDO),ParamTypeEnum.CfVolunteerMaterialDO);
        return response.ok() ? response.getData() : defaultValue;
    }

    @Override
    public int updateCfVolunteerMaterial(CfVolunteerMaterialDO materialDO) {
        Response<Integer> response = cfGrowthtoolVolunteerFeignClient.updateCfVolunteerMaterial(JSON.toJSONString(materialDO),ParamTypeEnum.CfVolunteerMaterialDO);
        return response.ok() ? response.getData() : defaultValue;
    }

    @Override
    public Map<String, Object> selectFromName(int cityId, String userInput, int pageSize, int current) {
        Response<Map<String, Object>> response = otherFeginClient.selectFromName(cityId, userInput, pageSize, current);
        if(response.ok()){
            return response.getData();
        }else{
            log.error("selectFromName fegin接口返回失败 :{}", JSON.toJSONString(response));
            Map<String, Object> result = Maps.newHashMap();
            result.put("cfHospitals", Lists.newArrayList());
            Map<String, Object> pagination = Maps.newHashMap();
            pagination.put("current", current);
            pagination.put("pageSize", pageSize);
            pagination.put("total", 0);
            result.put("pagination", pagination);
            return result;
        }
    }


}
