package com.shuidihuzhu.cf.delegate.crowdfunding.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.admin.util.AdminListUtil;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingCityBiz;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingUserDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.material.credit.CreditSupplementModel;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.CfHospitalNormalService;
import com.shuidihuzhu.cf.vo.CrowdfundingUserVo;
import com.shuidihuzhu.cf.vo.approve.TreatmentVO;
import com.shuidihuzhu.client.cf.api.chaifenbeta.crowdfunding.CrowdfundingUserFeignClient;
import com.shuidihuzhu.client.cf.api.model.enums.ParamTypeEnum;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @author: wanghui
 * @create: 2019/6/6 5:21 PM
 */
@Service
@Slf4j
@RefreshScope
public class CrowdfundingUserDelegate implements ICrowdfundingUserDelegate {
    @Value("${cfbiz.unuse-cf-biz:false}")
    private boolean unuseCfBiz;
    private static final int defaultValue = 0;
    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;
    @Autowired
    private CrowdfundingUserFeignClient crowdfundingUserFeignClient;

    @Autowired
    private CfHospitalNormalService cfHospitalNormalService;

    @Resource
    private AdminCrowdfundingCityBiz adminCrowdfundingCityBiz;

    @Override
    public CrowdfundingUserVo getUserInfo(List<CrowdfundingUser> crowdfundingUserList) {
        Response<String> response = crowdfundingUserFeignClient.getUserInfo(AdminListUtil.getListStringFromListModel(crowdfundingUserList), ParamTypeEnum.CrowdfundingUser);
        return response.ok() ? AdminListUtil.getModelFromResponse(response, CrowdfundingUserVo.class) : null;
    }


    @Override
    public List<CfCreditSupplement> selectByInfoUuid(String infoUuid) {
        Response<List<String>> response = crowdfundingUserFeignClient.selectByInfoUuid(infoUuid);
        return response.ok() ? AdminListUtil.getModelListFromResponse(response, CfCreditSupplement.class) : Lists.newArrayList();
    }

    @Override
    public CrowdfundingAuthor getCrowdfundingAuthor(Integer crowdfundingId) {
        FeignResponse<CrowdfundingAuthor> feignResponse = FeignResponse.fallback();
        try {
            feignResponse = crowdfundingFeignClient.getCrowdfundingAuthorByCaseId(crowdfundingId);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + " getCrowdfundingAuthorByCaseId err:", e);
        }
        log.info(this.getClass().getSimpleName() + " getCrowdfundingAuthorByCaseId  request:{}  response:{}",
                crowdfundingId, feignResponse);
        return feignResponse.ok() ? feignResponse.getData() : null;
    }

    @Override
    public Map<Integer, CrowdfundingAuthor> getByInfoIdList(List<Integer> infoIdList) {
        FeignResponse<Map<Integer, CrowdfundingAuthor>> feignResponse = FeignResponse.fallback();
        try {
            feignResponse = crowdfundingFeignClient.getCrowdfundingAuthorByInfoIdList(infoIdList);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + " getCrowdfundingAuthorByInfoIdList err:", e);
        }
        log.info(this.getClass().getSimpleName() + " getCrowdfundingAuthorByInfoIdList  request:{}  response:{}",
                infoIdList, feignResponse);
        return feignResponse.ok() ? feignResponse.getData() : Maps.newHashMap();
    }


    @Override
    public CrowdfundingTreatment getCrowdfundingTreatment(Integer crowdfundingId) {
        FeignResponse<CrowdfundingTreatment> feignResponse = FeignResponse.fallback();
        try {
            feignResponse = crowdfundingFeignClient.getCrowdfundingTreatment(crowdfundingId);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + " getCrowdfundingTreatment err:", e);
        }
        log.info(this.getClass().getSimpleName() + " getCrowdfundingTreatment  request:{}  response:{}",
                crowdfundingId, feignResponse);
        return feignResponse.ok() ? feignResponse.getData() : null;
    }

    @Override
    public TreatmentVO getCrowdfundingTreatmentVO(int crowdfundingId) {
        CrowdfundingTreatment t = getCrowdfundingTreatment(crowdfundingId);
        if (t == null) {
            return null;
        }
        TreatmentVO v = new TreatmentVO();
        BeanUtils.copyProperties(t, v);

        promoteHospital(v.getHospitalCityId(), new HospitalPromoterCallback() {
            @Override
            public void setCity(String city) {
                v.setHospitalCityName(city);
            }

            @Override
            public void setProvince(String province) {
                v.setHospitalProvinceName(province);
            }
        });

        promoteHospital(v.getDiagnoseHospitalCityId(), new HospitalPromoterCallback() {
            @Override
            public void setCity(String city) {
                v.setDiagnoseHospitalCityName(city);
            }

            @Override
            public void setProvince(String province) {
                v.setDiagnoseHospitalProvinceName(province);
            }
        });

        v.setHospitalAcceptToPublic(cfHospitalNormalService.validateHospitalAcceptToPublic(t.getHospitalId(),t.getHospitalCode()));
        v.setDiagnoseHospitalAcceptToPublic(cfHospitalNormalService.validateHospitalAcceptToPublic(t.getDiagnoseHospitalId(),null));
        return v;
    }

    private void promoteHospital(int cityId, HospitalPromoterCallback promoter){
        if (cityId <= 0) {
            return;
        }
        CrowdfundingCity city = adminCrowdfundingCityBiz.getById(cityId);
        if (city == null) {
            return;
        }
        promoter.setCity(city.getName());
        int parentId = city.getParentId();
        if (parentId <= 0) {
            return;
        }
        CrowdfundingCity province = adminCrowdfundingCityBiz.getById(parentId);
        if (province == null) {
            return;
        }
        promoter.setProvince(province.getName());
    }

    interface HospitalPromoterCallback {
        void setCity(String city);
        void setProvince(String province);
    }

    @Override
    public int updateCrowdfundingTreatment(CrowdfundingTreatment crowdfundingTreatment) {
        FeignResponse<Integer> feignResponse = FeignResponse.fallback();
        try {
            feignResponse = crowdfundingFeignClient.updateCrowdfundingTreatment(crowdfundingTreatment);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + " updateCrowdfundingTreatment err:", e);
        }
        log.info(this.getClass().getSimpleName() + " updateCrowdfundingTreatment  request:{}  response:{}",
                JSON.toJSONString(crowdfundingTreatment), feignResponse);
        return feignResponse.ok() ? feignResponse.getData() : defaultValue;
    }

    @Override
    public Map<Integer, CrowdfundingTreatment> getCrowdfundingTreatmentMapByInfoIdList(List<Integer> infoIdList) {
        FeignResponse<Map<Integer, CrowdfundingTreatment>> feignResponse = FeignResponse.fallback();
        try {
            feignResponse = crowdfundingFeignClient.getCrowdfundingTreatmentMapByInfoIdList(infoIdList);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + " getCrowdfundingTreatmentMapByInfoIdList err:", e);
        }
        log.info(this.getClass().getSimpleName() + " getCrowdfundingTreatmentMapByInfoIdList  request:{}  response:{}",
                infoIdList, feignResponse);
        return feignResponse.ok() ? feignResponse.getData() : Maps.newHashMap();
    }


    @Override
    public OpResult<CreditSupplementModel> getCreditSupplementByCaseId(int caseId) {
        FeignResponse<CreditSupplementModel> feignResponse = FeignResponse.fallback();
        try {
            feignResponse = crowdfundingFeignClient.getCreditSupplementByCaseId(caseId);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + " getCreditSupplementByCaseId err:", e);
        }
        log.info(this.getClass().getSimpleName() + " getCreditSupplementByCaseId  request:{}  response:{}",
                caseId, feignResponse);

        return feignResponse.ok() ? OpResult.createSucResult(feignResponse.getData()) : OpResult.createFailResult(AdminErrorCode.SYSTEM_OPERATION_FAILED);
    }
}
