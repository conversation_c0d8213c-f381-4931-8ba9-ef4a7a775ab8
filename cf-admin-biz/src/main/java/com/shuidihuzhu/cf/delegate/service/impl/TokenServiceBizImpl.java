package com.shuidihuzhu.cf.delegate.service.impl;

import com.shuidihuzhu.cf.delegate.service.TokenServiceBiz;
import com.shuidihuzhu.client.grpc.account.v1.TokenGrpcClient;
import com.shuidihuzhu.client.grpc.account.v1.feign.TokenServiceClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/5/5 8:23 PM
 */
@Slf4j
@Service
@RefreshScope
public class TokenServiceBizImpl implements TokenServiceBiz {
    @Resource
    private TokenServiceClient tokenServiceClient;


    @Override
    public void adminExpiresSdToken(String sdUserToken) {
        tokenServiceClient.adminExpiresSdToken(sdUserToken);
    }
}
