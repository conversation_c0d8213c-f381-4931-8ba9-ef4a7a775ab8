package com.shuidihuzhu.cf.delegate.service;

import com.shuidihuzhu.account.model.UserInfoModel;

import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/5/6 10:43 AM
 */
public interface UserInfoServiceBiz {

    UserInfoModel getUserInfoByUserId(long userId);

    void adminClearUserAllCache(long accountId);

    List<UserInfoModel> getUserInfoByUserIdBatch(List<Long> userIdList);

    UserInfoModel getUserInfoByCryptoMobile(String aesEncryptMobile);

    UserInfoModel getUserInfoByMobile(String mobile);

    void updateNicknameAndHeadImgUrl(long userId, String nickName, String headImgUrl);

    @Deprecated
    Date getUserCreateTime(long userId);

    UserInfoModel getByUnionId(String unionId);

}
