package com.shuidihuzhu.cf.delegate.service.impl;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.client.grpc.account.v1.UserInfoGrpcClient;
import com.shuidihuzhu.client.grpc.account.v1.feign.UserInfoServiceClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/5/6 10:43 AM
 */
@Slf4j
@Service
@RefreshScope
public class UserInfoServiceBizImpl implements UserInfoServiceBiz {


    @Resource
    private UserInfoGrpcClient userInfoGrpcClient;
    @Resource
    private UserInfoServiceClient userInfoServiceClient;


    @Override
    public UserInfoModel getUserInfoByUserId(long userId) {
        return userInfoServiceClient.getUserInfoByUserId(userId);
    }

    @Override
    public void adminClearUserAllCache(long accountId) {
        userInfoServiceClient.adminClearUserAllCache(accountId);
    }

    @Override
    public List<UserInfoModel> getUserInfoByUserIdBatch(List<Long> userIdList) {
        List<UserInfoModel> result = new ArrayList<>(userIdList.size());
        Lists.partition(userIdList, 1000).forEach(item -> {
            result.addAll(userInfoServiceClient.getUserInfoByUserIdBatchV2(item));
        });
        return result;
    }

    @Override
    public UserInfoModel getUserInfoByCryptoMobile(String aesEncryptMobile) {
        return userInfoServiceClient.getUserInfoByCryptoMobile(aesEncryptMobile);
    }

    @Override
    public UserInfoModel getUserInfoByMobile(String mobile) {
        return userInfoServiceClient.getUserInfoByMobile(mobile);
    }

    @Override
    public void updateNicknameAndHeadImgUrl(long userId, String nickName, String headImgUrl) {
        userInfoServiceClient.updateNicknameAndHeadImgUrl(userId, nickName, headImgUrl);
    }

    @Override
    public Date getUserCreateTime(long userId) {
        return userInfoGrpcClient.getUserCreateTime(userId);
    }

    @Override
    public UserInfoModel getByUnionId(String unionId) {
        return userInfoServiceClient.getByUnionId(unionId);
    }
}
