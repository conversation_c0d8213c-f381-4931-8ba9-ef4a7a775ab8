package com.shuidihuzhu.cf.delegate.other.impl;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.admin.util.AdminListUtil;
import com.shuidihuzhu.cf.delegate.other.IWeiXinDelegate;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.api.chaifenbeta.wxgrpcdelegate.WeiXinFeginClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.msg.model.WxCustomRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @author: wanghui
 * @create: 2019/6/12 9:01 PM
 */
@Slf4j
@Service
@RefreshScope
public class WeiXinDelegate implements IWeiXinDelegate {
    @Autowired
    private WeiXinFeginClient weiXinFeginClient;

    @Override
    public OpResult sendByEmail(List<String> emailList, String content) {
        Response response = weiXinFeginClient.sendByEmail(emailList, content);
        if(response.ok()){
            return OpResult.createSucResult();
        }
        log.error("sendByEmail fegin接口返回失败:{}", JSON.toJSONString(response));
        return OpResult.createFailResult(CfErrorCode.SYSTEM_STRING_TO_RESPONSE_ERROR,response.getMsg());
    }

    @Override
    public OpResult sendByGroup(String groupId, String content) {
        Response response = weiXinFeginClient.sendByGroup(groupId, content);
        if(response.ok()){
            return OpResult.createSucResult();
        }
        log.error("sendByGroup fegin接口返回失败:{}", JSON.toJSONString(response));
        return OpResult.createFailResult(CfErrorCode.SYSTEM_STRING_TO_RESPONSE_ERROR,response.getMsg());
    }

    @Override
    public String generateQrcode(String scene) {
        Response<String> response = weiXinFeginClient.generateQrcode(scene);
        if(response.ok()){
            return response.getData();
        }
        log.error("generateQrcode fegin接口返回失败:{}", JSON.toJSONString(response));
        return null;
    }

    @Override
    public String sendTemplateMessage(String openId, String templateId, String url, Map<String, Object> dataMap, int type, int businessType, int subBusinessType) {
        //此方法调用的消息系统接口已下线,此方法不可使用
        return null;
    }
    @Override
    public Response saveWxCustomRecord(List<WxCustomRecord> wxCustomRecordList, String businessInfo) {
        //此方法调用的消息系统接口已下线,此方法不可使用
        return null;
    }
}
