package com.shuidihuzhu.cf.delegate.saas;


import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.client.auth.saas.feign.PermissionFeignClient;
import com.shuidihuzhu.client.auth.saas.feign.TokenFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.client.auth.saas.model.param.PermissionParam;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Set;

@Slf4j
@Service
public class SeaAuthClientV1 {

    @Autowired
    private PermissionFeignClient permissionFeignClient;

    @Autowired
    private TokenFeignClient tokenFeignClient;

    public AuthRpcResponse<Boolean> hasPermissionWithUser(int userId, String permission) {

        PermissionParam permissionParam = PermissionParam.builder()
                .userId(Long.valueOf(userId))
                .appCode(AuthSaasContext.getAuthAppCode())
                .permissions(Lists.newArrayList(permission))
                .build();
        Response<Boolean> authRpcResponse = permissionFeignClient.validUserPermission(permissionParam);

        return AuthRpcResponse.makeSuccess(authRpcResponse.getCode(), authRpcResponse.getMsg(), authRpcResponse.getData());
    }

    public AuthRpcResponse<Set<Integer>> getUserIdsByPermission(String permission) {

        Response<List<AuthUserDto>> response = permissionFeignClient.getUsersByPermission(permission);
        if (response == null || CollectionUtils.isEmpty(response.getData())) {
            return AuthRpcResponse.makeFailed(AdminErrorCode.SYSTEM_NOT_EXIST_RECORD_ERROR);
        }
        Set<Integer> userIds = Sets.newHashSet();
        response.getData().forEach(item->{
            userIds.add(Math.toIntExact(item.getUserId()));
        });
        return AuthRpcResponse.makeSuccess(response.getCode(), response.getMsg(), userIds);
    }

    public AuthRpcResponse<Set<String>> validUserPermissions(Integer userId, List<String> permissions) {
        PermissionParam permissionParam = PermissionParam.builder()
                .userId(Long.valueOf(userId))
                .appCode(AuthSaasContext.getAuthAppCode())
                .permissions(Lists.newArrayList(permissions))
                .build();
        Response<Set<String>> authRpcResponse = permissionFeignClient.validUserPermissions(permissionParam);
        if (authRpcResponse == null || CollectionUtils.isEmpty(authRpcResponse.getData())) {
            return AuthRpcResponse.makeFailed(AdminErrorCode.SYSTEM_NOT_EXIST_RECORD_ERROR);
        }

        return AuthRpcResponse.makeSuccess(authRpcResponse.getCode(), authRpcResponse.getMsg(), authRpcResponse.getData());
    }

    public  AuthRpcResponse<Integer> checkAuthentication(String token) {

        Response<Long> result = tokenFeignClient.verifyAuthentication(token);
        if (result == null || result.getData() == null) {
            return AuthRpcResponse.makeFailed(-1, "参数无效");
        }
        return AuthRpcResponse.makeSuccess(Math.toIntExact(result.getData()));
    }




}
