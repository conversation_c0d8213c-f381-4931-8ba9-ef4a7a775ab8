package com.shuidihuzhu.cf.delegate.other;

import com.shuidihuzhu.cf.domain.dedicated.CfUserVolunteerRelationDO;
import com.shuidihuzhu.cf.model.crowdfunding.CfTouFangSign;
import com.shuidihuzhu.cf.service.notice.urgeraise.UrgeRaiseMsgType;
import com.shuidihuzhu.client.cf.growthtool.model.CfVolunteerMaterialDO;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;
import java.util.Map;

/**
 * @author: wanghui
 * @create: 2019/6/13 11:38 AM
 */
public interface IOtherDelegate {
    int insertCfTouFangSignList(List<CfTouFangSign> cfTouFangSigns);
    Response insertCfTransformArticle(String title, String url, String imgUrl);

    Response deleteCfTransformArticle(long id);

    Response listCfTransformArticleByAnchor(long anchorId, int size);

    Response listCfTransformArticleByPage(int current, int pageSize);

    Response listByPageAndTitleKey(int current, int pageSize, String titleKey);
    boolean checkCouldSend(long userId, UrgeRaiseMsgType rightMsgType);

    CfUserVolunteerRelationDO getAccountUserAndVolunteerRelationByPhone(String phone);

    CfVolunteerMaterialDO getVolunteerMateri(String uniqueCode);

    int addCfVolunteerMaterial(CfVolunteerMaterialDO materialDO);

    int updateCfVolunteerMaterial(CfVolunteerMaterialDO materialDO);

    Map<String, Object> selectFromName(int cityId, String userInput, int pageSize, int current);

}
