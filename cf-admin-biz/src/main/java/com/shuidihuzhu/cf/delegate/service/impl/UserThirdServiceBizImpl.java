package com.shuidihuzhu.cf.delegate.service.impl;

import com.shuidihuzhu.account.model.UserThirdModel;
import com.shuidihuzhu.cf.delegate.service.UserThirdServiceBiz;
import com.shuidihuzhu.client.grpc.account.v1.UserThirdGrpcClient;
import com.shuidihuzhu.client.grpc.account.v1.feign.UserThirdServiceClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/5/6 4:57 PM
 */
@Slf4j
@Service
@RefreshScope
public class UserThirdServiceBizImpl implements UserThirdServiceBiz {

    @Resource
    private UserThirdServiceClient userThirdServiceClient;


    @Override
    public UserThirdModel getThirdModelWithUserId(long userId, int thirdType) {
        return userThirdServiceClient.getThirdModelWithUserId(userId, thirdType);
    }

    @Override
    public void updateUserThird(String openId, String unionId, long userId, String nickName, String headImgUrl) {
        userThirdServiceClient.updateUserThird(openId, unionId, userId, nickName, headImgUrl);
    }

    @Override
    public List<UserThirdModel> getByUserId(long userId) {
        return userThirdServiceClient.getByUserId(userId);
    }
}
