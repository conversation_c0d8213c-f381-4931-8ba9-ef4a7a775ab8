package com.shuidihuzhu.cf.delegate.risk;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.client.ugc.model.domain.risk.RiskControlWordCategoryDO;
import com.shuidihuzhu.cf.client.ugc.model.domain.risk.RiskControlWordDO;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordCheckContext;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordResult;
import com.shuidihuzhu.cf.domain.CaseRaiseRiskDO;
import com.shuidihuzhu.cf.domain.CfCaseRiskDO;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CfOperatingRecordEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveIdcardVerifyStatusEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.risk.CfCaseRiskTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.risk.StatRiskHandleStatusEnum;
import com.shuidihuzhu.cf.enums.risk.UgcTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.risk.verify.RiskUgcVerifyModel;
import com.shuidihuzhu.cf.response.OpResult;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: wanghui
 * @create: 2019/6/12 5:14 PM
 */
public interface IRiskDelegate {
    OpResult<CfCaseRiskDO> getByInfoUuid(String infoUuid, CfCaseRiskTypeEnum caseRiskTypeEnum);


    /**
     * 材料异常
     * 处理异常案例
     * @param infoUuid
     * @return
     */
    OpResult handleInfoRisk(String infoUuid);

    /**
     * 提现审核异常
     * 处理异常案例
     * @param infoUuid
     * @return
     */
    OpResult handleDrawCaskRisk(String infoUuid);

    /**
     * 案例结束时
     * 转发开始小于72小时
     * 获取材料审核风险数据
     * @param infoUuid
     * @return
     */
    OpResult onCaseEnd(String infoUuid);

    /**
     * 转发开始72小时的时候check risk
     * @param infoUuid
     * @return
     */
    OpResult onCaseFirstApproveHasDelay72(String infoUuid);

    /**
     * 材料审核通过时调用
     * 尝试发送材料审核通过消息
     * 若风控数据审核不过 则等数据审核通过时再发
     * @param infoUuid
     * @param info 可以传null 里面自己查询
     * @return
     */
    OpResult onCaseInfoPassed(String infoUuid, CrowdfundingInfo info);


    /**
     * 用于测试
     * @param v
     * @param typeEnum
     * @param dataLevel
     * @return
     */
    OpResult<CfCaseRiskDO> doVerify(CfCaseRiskDO v, CfCaseRiskTypeEnum typeEnum, String dataLevel);


    void onRemark(int caseId, int remarkCode);

    List<CfCaseRiskDO> listByConditionAll(int verified,
                                          Boolean passed,
                                          int type,
                                          StatRiskHandleStatusEnum handleStatus);
    CaseRaiseRiskDO getByInfoUuid(String infoUuid);


    OpResult<Integer> saveRaiseRisk(CaseRaiseRiskDO raiseRiskDO);

    OpResult handleRiskPassed(String infoUuid);

    OpResult handleRiskPassed(String infoUuid, int adminUserId);

    CfFaceIdLivingVerifyInfo getCfFaceIdLivingVerifyInfoByInfoUuid(String infoUuid);

    IdcardVerifyWhiteList getByNameAndIdcard(String name, String idCard);

    int addIdcardVerifyWhiteList(String name, String idCard);

    int addIdcardVerifyWhiteListV2(String name, String idCard, String images, int reason, String otherReason, String operator);

    List<IdcardVerifyWhiteListRecord> whiteListOperationRecord(int id);

    int deleteFirstApproveWhiteIdById(int id);

    PageInfo<IdcardVerifyWhiteList> selectAllWhiteIdCardList(int current, int pageSize, String name, String idCard);

    CfErrorCode verifyIdcard(String selfRealName,
                             String selfIdcard,
                             String otherRealName,
                             String otherIdcard,
                             int patientIdType,
                             UserRelTypeEnum relType,
                             long userId);
    boolean isSuccess(CfErrorCode cfErrorCode);

    int updateStatusByCaseId(int caseId, FirstApproveIdcardVerifyStatusEnum statusEnum);

    CfFirsApproveMaterial getCfFirsApproveMaterialByInfoId(int infoId);

    List<CfFirsApproveMaterial> getCfFirsApproveMaterialListByParam(CfFirsApproveMaterial cfFirsApproveMaterial);
    Map<Integer, CfFirsApproveMaterial> getMapByInfoIds(List<Integer> infoIds);

    int updateRejectTypeByInfoId(int infoId, int rejectType, String rejectMessage);

    String getCanEditMsg(int rejectType, boolean moreThan50w);

    List<CfSuspectedCaseInfo> getCfSuspectedCaseInfoListBySerch(CfSuspectedCaseInfoModel cfSuspectedCaseInfoModel);

    int addCfSuspectedCaseInfo(CfSuspectedCaseInfo cfSuspectedCaseInfo);

    List<CfSuspectedCaseInfo> getByLikeIdNumber(String idNumber);

    int updateCfSuspectedCaseInfo(CfSuspectedCaseInfo cfSuspectedCaseInfo);

    int deleteCfSuspectedCaseInfo(int id);

    List<CrowdfundingReport> getCrowdfundingReportListByInfoId(Integer activityId);

    CfOperatingRecord before(String infoUuid, long userId, String userName,
                             CfOperatingRecordEnum.Type type, CfOperatingRecordEnum.Role role, List<CrowdfundingReport> crowdfundingReports);

    void after(CfOperatingRecord cfOperatingRecord, List<CrowdfundingReport> crowdfundingReports);


    int updateValid(int valid, int id);

    void deleteCaseVerificationCache(String infoUuid);


    OpResult<RiskWordResult> isHit(RiskWordCheckContext ctx);

    List<RiskControlWordDO> searchByTypeAll(int type, long category, String key);

    OpResult<RiskUgcVerifyModel> addVerify(RiskUgcVerifyModel model);

    OpResult deleteVerify(long caseId, UgcTypeEnum typeEnum, long ugcId);

    OpResult saveCache(UgcTypeEnum ugcTypeEnum, long ugcId, long caseId, boolean isSafe);

    boolean isSafeSingle(UgcTypeEnum ugcTypeEnum, long ugcId, long caseId);

    default Set<String> getHitWords(String progressContent) {

        RiskWordCheckContext ctx = RiskWordCheckContext.builder()
                .content(progressContent)
                .useScenes(Lists.newArrayList(RiskControlWordCategoryDO.RiskWordUseScene.PUBLISH_UGC.getCode()))
                .isCheckAll(true)
                .build();
        OpResult<RiskWordResult> checkResult = isHit(ctx);
        if (checkResult.isFail()) {
            return Collections.emptySet();
        }
        RiskWordResult riskWordResult = checkResult.getData();
        if (riskWordResult.isPassed()) {
            return Collections.emptySet();
        }
        return Sets.newHashSet(checkResult.getData().getHitWords());
    }
}
