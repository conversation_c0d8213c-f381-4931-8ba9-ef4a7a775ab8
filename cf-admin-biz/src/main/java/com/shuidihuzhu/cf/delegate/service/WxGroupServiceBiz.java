package com.shuidihuzhu.cf.delegate.service;

import com.shuidihuzhu.wx.grpc.client.common.CommonResponse;
import com.shuidihuzhu.wx.grpc.enums.WxMpGroupTypeEnum;
import com.shuidihuzhu.wx.grpc.model.WxConfigModel;
import com.shuidihuzhu.wx.grpc.model.WxGroupModel;
import com.shuidihuzhu.wx.grpc.model.WxMpGroupMapModel;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/5/4 3:21 PM
 */
public interface WxGroupServiceBiz {

    List<WxMpGroupMapModel> getGroupDetail(int groupId);

    List<WxGroupModel> getAllGroup(WxMpGroupTypeEnum type);

    List<WxGroupModel> getGroupList(Integer current, Integer pageSize, WxMpGroupTypeEnum type);

    Integer getGroupCount(WxMpGroupTypeEnum type);

    List<WxGroupModel> getRepeatGroupInfo(WxGroupModel wxGroupModel);

    Boolean addGroup(String name, WxMpGroupTypeEnum type, int isMain, int bizType);

    int getGroupId(String name, int type);

    Boolean addGroupMap(Integer groupId, List<WxConfigModel> wxConfigModels);

    Boolean updateGroupById(WxGroupModel wxGroupModel);

    Boolean deleteGroupMapByGroupId(int groupId);

    List<WxGroupModel> getGroupsListByCondition(int current, int pageSize, String description, Integer bizType);

    int getCountByCondition(String description, Integer bizType);

}
