package com.shuidihuzhu.cf.delegate.ai;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.shuidihuzhu.alps.feign.ocean.OceanApiClient;
import com.shuidihuzhu.alps.feign.ocean.OceanApiRequest;
import com.shuidihuzhu.alps.feign.ocean.OceanApiResponse;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfAiMaterialsSmartAIResultDao;
import com.shuidihuzhu.cf.enhancer.subject.aiagent.AiAgent;
import com.shuidihuzhu.cf.model.ai.CfAiMaterialsSmartAIResult;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.ai.AiConditionEnum;
import com.shuidihuzhu.cf.model.crowdfunding.ai.LayOutField;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditSearchService;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditCaseDetail;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.frame.client.api.basic.BasicStoreManageClient;
import com.shuidihuzhu.frame.client.vo.BasicStoreData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: wangpeng
 * @Date: 2022/12/8 11:55
 * @Description:
 */
@Slf4j
@Service
@RefreshScope
public class AiAIContentWorkOrderResultDelegate {

    @Resource
    private InitialAuditSearchService initialAuditSearchService;

    @Resource
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;

    @Resource
    private AiAgent oceanApiClient;

    @Resource
    private BasicStoreManageClient basicStoreManageClient;

    @Resource
    private CfAiMaterialsSmartAIResultDao cfAiMaterialsSmartAIResultDao;

    @Value("${ai-content-smart-switch:true}")
    private boolean aiContentSmartSwitch;

    public static String TAG = "ai-chou-classification";
    public static String TAG_DISEASE = "ai-chou-ner";
    public static String USER_ID = "10007";
    public static String TOKEN = "d5cdb7934bde6021";
    public static String TOKEN_DISEASE = "0a7843a03efd651a";
    public static long BASIC_STORE_ID = 20201217202216L;

    public void aiContentWorkResult(int caseId, int workOrderType, long workOrderId) {
        log.info("aiContentWorkResult {} {}", caseId, workOrderId);
        if (!aiContentSmartSwitch) {
            return;
        }
        if (workOrderType != WorkOrderType.ai_content.getType()) {
            return;
        }
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if (Objects.isNull(crowdfundingInfo)) {
            return;
        }

        InitialAuditCaseDetail.CaseBaseInfo baseInfo = initialAuditSearchService.getBaseInfo(crowdfundingInfo);
        if (Objects.isNull(baseInfo)) {
            return;
        }
        AiAIContentParam param = new AiAIContentParam();
        param.setPid(String.valueOf(workOrderId));
        param.setTitle(baseInfo.getTitle());
        param.setContent(baseInfo.getContent());
        OceanApiRequest oceanApiRequest = new OceanApiRequest();
        oceanApiRequest.setTag(TAG);
        oceanApiRequest.setUserId(USER_ID);
        oceanApiRequest.setToken(TOKEN);
        oceanApiRequest.setBody(JSONObject.toJSONString(param));
        Response<OceanApiResponse> agent = oceanApiClient.agent(oceanApiRequest, null);
        String contentAiResult = Optional.ofNullable(agent)
                .filter(Response::ok)
                .map(Response::getData)
                .map(OceanApiResponse::getBody)
                .orElse("");
        log.info("aiContentWorkResult contentAiResult {} {}", agent, oceanApiRequest);

        OceanApiRequest oceanApiRequestDisease = new OceanApiRequest();
        oceanApiRequestDisease.setTag(TAG_DISEASE);
        oceanApiRequestDisease.setUserId(USER_ID);
        oceanApiRequestDisease.setToken(TOKEN_DISEASE);
        oceanApiRequestDisease.setBody(JSONObject.toJSONString(param));
        Response<OceanApiResponse> agentContent = oceanApiClient.agent(oceanApiRequestDisease, null);
        String contentIDiseaseName = Optional.ofNullable(agentContent)
                .filter(Response::ok)
                .map(Response::getData)
                .map(OceanApiResponse::getBody)
                .orElse("");
        log.info("aiContentWorkResult agentContent {} {}", agentContent, oceanApiRequestDisease);

        param.setContent(baseInfo.getTitle());
        oceanApiRequestDisease.setBody(JSONObject.toJSONString(param));
        Response<OceanApiResponse> agentTitle = oceanApiClient.agent(oceanApiRequestDisease, null);
        String titleAIDiseaseName = Optional.ofNullable(agentTitle)
                .filter(Response::ok)
                .map(Response::getData)
                .map(OceanApiResponse::getBody)
                .orElse("");
        log.info("aiContentWorkResult agentTitle {} {}", agentContent, oceanApiRequestDisease);

        String titleDisease = parseDisease(titleAIDiseaseName);
        String contentDisease = parseDisease(contentIDiseaseName);
        String workOrderResult = parseContentWorkOrderResult(contentAiResult);

        CfAiMaterialsSmartAIResult aiMaterialSmartAIResult = new CfAiMaterialsSmartAIResult();
        aiMaterialSmartAIResult.setCaseId(caseId);
        aiMaterialSmartAIResult.setWorkOrderId(workOrderId);
        aiMaterialSmartAIResult.setAiContentWorkOrderResult(StringUtils.isEmpty(workOrderResult) ? "[]" : workOrderResult);
        aiMaterialSmartAIResult.setTitleDiseaseName(titleDisease);
        aiMaterialSmartAIResult.setContentDiseaseName(contentDisease);
        cfAiMaterialsSmartAIResultDao.insert(aiMaterialSmartAIResult);
    }

    private String parseDisease(String diseaseNameJson) {
        if (StringUtils.isEmpty(diseaseNameJson)) {
            return "";
        }
        AiAIContentDiseaseResult aiAIContentDiseaseResult = JSONObject.parseObject(diseaseNameJson, AiAIContentDiseaseResult.class);
        if (Objects.isNull(aiAIContentDiseaseResult)) {
            return "";
        }
        List<AiAIContentDiseaseResult.AiAIContentDiseaseListResult> contentDiseaseResultDis = aiAIContentDiseaseResult.getDis();
        if (CollectionUtils.isEmpty(contentDiseaseResultDis)) {
            return "";
        }
        Set<String> strings = contentDiseaseResultDis.stream()
                .map(AiAIContentDiseaseResult.AiAIContentDiseaseListResult::getEntity)
                .collect(Collectors.toSet());
        return Joiner.on(",").join(strings);
    }

    private String parseContentWorkOrderResult(String contentAiResult) {
        if (StringUtils.isEmpty(contentAiResult)) {
            return "";
        }
        JSONObject jsonObject = JSONObject.parseObject(contentAiResult);
        if (MapUtils.isEmpty(jsonObject)) {
            return "";
        }
        Response<BasicStoreData> basicStoreDataResponse = basicStoreManageClient.get(BASIC_STORE_ID);
        JSONObject basicStoreJSON = Optional.ofNullable(basicStoreDataResponse)
                .filter(Response::ok)
                .map(Response::getData)
                .map(BasicStoreData::getContent)
                .orElse(null);
        if (MapUtils.isEmpty(basicStoreJSON)) {
            return "";
        }
        List<LayOutField> fieldList = new ArrayList<>();
        List<AiConditionEnum> conditionEnumList = Arrays.asList(AiConditionEnum.values());
        Map<String, AiConditionEnum> collect = conditionEnumList.stream()
                .collect(Collectors.toMap(AiConditionEnum::getDesc, Function.identity(), (x, y) -> x));
        for (Map.Entry<String, Object> entry : basicStoreJSON.entrySet()) {
            String basicStoreValue = String.valueOf(entry.getValue());
            Object o = jsonObject.get(basicStoreValue);
            if (Objects.isNull(o)) {
                continue;
            }
            String aiValue = String.valueOf(o);
            AiConditionEnum aiConditionEnum = collect.get(aiValue);
            LayOutField layOutField = new LayOutField();
            layOutField.setFieldKey(entry.getKey());
            layOutField.setFieldName(basicStoreValue);
            layOutField.setFieldType("");
            layOutField.setFieldValue(Objects.isNull(aiConditionEnum) ? aiValue : String.valueOf(aiConditionEnum.getCode()));
            fieldList.add(layOutField);
        }

        if (CollectionUtils.isEmpty(fieldList)) {
            return "";
        }
        return JSONObject.toJSONString(fieldList);
    }

}
