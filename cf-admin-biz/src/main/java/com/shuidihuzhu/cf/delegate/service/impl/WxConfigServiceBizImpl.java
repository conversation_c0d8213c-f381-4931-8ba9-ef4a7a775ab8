package com.shuidihuzhu.cf.delegate.service.impl;

import com.shuidihuzhu.cf.delegate.service.WxConfigServiceBiz;
import com.shuidihuzhu.wx.grpc.client.WxConfigListResponse;
import com.shuidihuzhu.wx.grpc.client.WxConfigServiceGrpcClient;
import com.shuidihuzhu.wx.grpc.client.WxConfigsResponse;
import com.shuidihuzhu.wx.grpc.client.common.WxConfig;
import com.shuidihuzhu.wx.grpc.client.feign.WxConfigServiceClient;
import com.shuidihuzhu.wx.grpc.model.SimpleWxConfig;
import com.shuidihuzhu.wx.grpc.model.WxConfigModel;
import com.shuidihuzhu.wx.grpc.util.WxRpcUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/5/4 4:07 PM
 */
@Slf4j
@Service
@RefreshScope
public class WxConfigServiceBizImpl implements WxConfigServiceBiz {

    @Resource
    private WxConfigServiceClient wxConfigServiceClient;
    @Resource
    private WxConfigServiceGrpcClient wxConfigServiceGrpcClient;

    @Override
    public List<WxConfigModel> getWxConfigByConditon(int pageSize, int current, String description, Integer wxType, Integer bizType) {
        return wxConfigServiceClient.getWxConfigByConditon(pageSize, current, description, wxType, bizType);
    }

    @Override
    public long getWxConfigCountByCondition(String description, Integer wxType, Integer bizType) {
        return wxConfigServiceClient.getWxConfigCountByCondition(description, wxType, bizType);
    }

    @Override
    public List<WxConfigModel> getWxConfigByThirdTypes(List<Integer> thirdTypes) {
        return wxConfigServiceClient.getWxConfigByThirdTypes(thirdTypes);
    }

    @Override
    public WxConfigsResponse getConfigsByGroupId(int pageSize, int current, long groupId) {
        return wxConfigServiceGrpcClient.getConfigsByGroupId(pageSize, current, groupId);
    }

    @Override
    public long getWxConfigCountByGroupId(long groupId) {
        return wxConfigServiceGrpcClient.getWxConfigCountByGroupId(groupId).getTotal();
    }

    @Override
    public WxConfigsResponse getRepeatWxConfig(List<WxConfigModel> wxMpConfigs) {
        return wxConfigServiceGrpcClient.getRepeatWxConfig(wxMpConfigs);
    }

    @Override
    public void addWxConfigBatch(List<WxConfigModel> wxConfigModels) {
        wxConfigServiceGrpcClient.addWxConfigBatch(wxConfigModels);
    }

    @Override
    public void updateWxConfig(WxConfigModel wxMpConfig) {
        wxConfigServiceGrpcClient.updateWxConfig(wxMpConfig);
    }

    @Override
    public List<WxConfigModel> getWxConfigByBizType(int bizType) {
        return wxConfigServiceClient.getWxConfigByBizType(bizType);
    }

    @Override
    public List<WxConfigModel> getAllConfigsByGroupId(int groupId) {
        return wxConfigServiceClient.getAllConfigsByGroupId(groupId);
    }

    @Override
    public WxConfigModel getWxConfigByAppId(String appid) {
        return wxConfigServiceClient.getWxConfigByAppId(appid);
    }

    @Override
    public WxConfigModel getWxConfigByThirdType(int thirdType) {
        return wxConfigServiceClient.getWxConfigByThirdType(thirdType);
    }

    @Override
    public List<SimpleWxConfig> getSimpleWxConfigList(List<Integer> thirdTypeCodes) {
        return wxConfigServiceClient.getSimpleWxConfigList(thirdTypeCodes);
    }
}
