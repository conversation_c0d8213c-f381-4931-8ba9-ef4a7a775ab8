package com.shuidihuzhu.cf.delegate.other.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.admin.util.AdminListUtil;
import com.shuidihuzhu.cf.delegate.other.IMiniAppDelegate;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.mina.*;
import com.shuidihuzhu.cf.model.miniprogram.*;
import com.shuidihuzhu.client.cf.api.chaifenbeta.other.MiniAppFeignClient;
import com.shuidihuzhu.client.cf.api.model.enums.ParamTypeEnum;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.msg.model.AppPushTemplate;
import com.shuidihuzhu.msg.model.AppPushTemplateParam;
import com.shuidihuzhu.msg.model.PushRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * @author: wanghui
 * @create: 2019/6/13 11:08 AM
 */
@Service
@Slf4j
@RefreshScope
public class MiniAppDelegate implements IMiniAppDelegate {
    private static int defauleValue = 0;

    @Autowired
    private MiniAppFeignClient miniAppFeignClient;


    @Override
    public int addCfKeywordPhaseRelation(CfKeywordPhaseRelation cfKeywordPhaseRelation) {
        Response<Integer> response = miniAppFeignClient.addCfKeywordPhaseRelation(JSON.toJSONString(cfKeywordPhaseRelation),
                ParamTypeEnum.CfKeywordPhaseRelation);
        return response.ok()?response.getData():defauleValue;
    }

    @Override
    public CfKeywordPhaseRelation selectCfKeywordPhaseRelationByPhaseId(int phaseId) {
        Response<String> response = miniAppFeignClient.selectCfKeywordPhaseRelationByPhaseId(phaseId);
        if(response.ok()){
            return AdminListUtil.getModelFromResponse(response,CfKeywordPhaseRelation.class);
        }
        throw new RuntimeException(CfErrorCode.SYSTEM_STRING_TO_RESPONSE_ERROR.getMsg());
    }

    @Override
    public int insertCfMinaMajorDiseaseInfo(CfMinaMajorDiseaseInfo cfMinaMajorDiseaseInfo) {
        Response<Integer> response = miniAppFeignClient.insertCfMinaMajorDiseaseInfo(JSON.toJSONString(cfMinaMajorDiseaseInfo), ParamTypeEnum.CfMinaMajorDiseaseInfo);
        return response.ok()?response.getData():defauleValue;
    }

    @Override
    public int insertCfMinaMajorDiseaseMenu(CfMinaMajorDiseaseMenu cfMinaMajorDiseaseMenu) {
        Response<Integer> response = miniAppFeignClient.insertCfMinaMajorDiseaseMenu(JSON.toJSONString(cfMinaMajorDiseaseMenu), ParamTypeEnum.CfMinaMajorDiseaseMenu);
        return response.ok()?response.getData():defauleValue;
    }


    @Override
    public int insertCfMinaMajorDiseaseTitleContent(CfMinaMajorDiseaseTitleContent cfMinaMajorDiseaseTitleContent) {
        Response<Integer> response = miniAppFeignClient.insertCfMinaMajorDiseaseTitleContent(JSON.toJSONString(cfMinaMajorDiseaseTitleContent), ParamTypeEnum.CfMinaMajorDiseaseTitleContent);
        return response.ok()?response.getData():defauleValue;
    }


    @Override
    public CfMinaQuiz getCfMinaQuizById(Integer id) {
        Response<String> response = miniAppFeignClient.getCfMinaQuizById(id);
        return response.ok()?AdminListUtil.getModelFromResponse(response,CfMinaQuiz.class):null;
    }

    @Override
    public List<CfMinaQuiz> findCfMinaQuizListByTitle(String title, Integer offset, Integer pageSize) {
        Response<List<String>> response = miniAppFeignClient.findCfMinaQuizListByTitle(title, offset, pageSize);
        return response.ok()?AdminListUtil.getModelListFromResponse(response,CfMinaQuiz.class):null;
    }

    @Override
    public Integer countByTitle(String title) {
        Response<Integer> response = miniAppFeignClient.countByTitle(title);
        return response.ok()?response.getData():defauleValue;
    }

    @Override
    public Integer saveMinaQuiz(CfMinaQuiz cfMinaQuiz) {
        Response<Integer> response = miniAppFeignClient.saveMinaQuiz(JSON.toJSONString(cfMinaQuiz),ParamTypeEnum.CfMinaQuiz);
        return response.ok()?response.getData():defauleValue;
    }

    @Override
    public Integer saveMinaQuizQuestion(CfMinaQuizQuestion cfMinaQuizQuestion) {
        Response<Integer> response = miniAppFeignClient.saveMinaQuizQuestion(JSON.toJSONString(cfMinaQuizQuestion), ParamTypeEnum.CfMinaQuizQuestion);
        return response.ok()?response.getData():defauleValue;
    }

    @Override
    public Integer saveMinaQuizAnswerBatch(List<CfMinaQuizAnswer> cfMinaQuizAnswerList) {
        Response<Integer> response = miniAppFeignClient.saveMinaQuizAnswerBatch(AdminListUtil.getListStringFromListModel(cfMinaQuizAnswerList), ParamTypeEnum.CfMinaQuizAnswer);
        return response.ok()?response.getData():defauleValue;
    }

    @Override
    public Integer saveQuizResult(CfMinaQuizResult cfMinaQuizResult) {
        Response<Integer> response = miniAppFeignClient.saveQuizResult(JSON.toJSONString(cfMinaQuizResult), ParamTypeEnum.CfMinaQuizResult);
        return response.ok()?response.getData():defauleValue;
    }

    @Override
    public CfMinaQuiz getBySortAndIsNew(Integer isNew, Integer sort) {
        Response<String> response = miniAppFeignClient.getBySortAndIsNew(isNew, sort);
        return response.ok()?AdminListUtil.getModelFromResponse(response,CfMinaQuiz.class):null;
    }

    @Override
    public Integer updateIsNewById(Integer isNew, Integer id) {
        Response<Integer> response = miniAppFeignClient.updateIsNewById(isNew, id);
        return response.ok()?response.getData():defauleValue;
    }

    @Override
    public Integer updateSortById(Integer sort, Integer id) {
        Response<Integer> response = miniAppFeignClient.updateSortById(sort, id);
        return response.ok()?response.getData():defauleValue;
    }

    @Override
    public Integer updateReleaseTimeById(Date releaseTime, Integer id) {
        Response<Integer> response = miniAppFeignClient.updateReleaseTimeById(releaseTime.getTime(), id);
        return response.ok()?response.getData():defauleValue;
    }

    @Override
    public Integer updateShowTypeById(Integer showType, Integer id) {
        Response<Integer> response = miniAppFeignClient.updateShowTypeById(showType, id);
        return response.ok()?response.getData():defauleValue;
    }

    @Override
    public Integer updateIsDeleteById(Integer isDelete, Integer id) {
        Response<Integer> response = miniAppFeignClient.updateIsDeleteById(isDelete, id);
        return response.ok()?response.getData():defauleValue;
    }

    @Override
    public List<CfMinaQuizQuestion> findQuestion(Integer minaQuizId) {
        Response<List<String>> response = miniAppFeignClient.findQuestion(minaQuizId);
        return response.ok()?AdminListUtil.getModelListFromResponse(response,CfMinaQuizQuestion.class): Lists.newArrayList();
    }

    @Override
    public List<CfMinaQuizAnswer> findAnswerByQuestionIds(List<Integer> questionIds) {
        Response<List<String>> response = miniAppFeignClient.findAnswerByQuestionIds(questionIds);
        return response.ok()?AdminListUtil.getModelListFromResponse(response,CfMinaQuizAnswer.class): Lists.newArrayList();
    }

    @Override
    public List<CfMinaQuizResult> findResultByQuizId(Integer minaQuizId) {
        Response<List<String>> response = miniAppFeignClient.findResultByQuizId(minaQuizId);
        return response.ok()?AdminListUtil.getModelListFromResponse(response,CfMinaQuizResult.class): Lists.newArrayList();
    }

    @Override
    public Integer updateAnswerDeleteByQuestionIds(Integer isDelete, List<Integer> questionIds) {
        Response<Integer> response = miniAppFeignClient.updateAnswerDeleteByQuestionIds(isDelete, questionIds);
        return response.ok()?response.getData():defauleValue;
    }

    @Override
    public Integer updateQuestionDeleteByMinaQuizId(Integer isDelete, Integer minaQuizId) {
        Response<Integer> response = miniAppFeignClient.updateQuestionDeleteByMinaQuizId(isDelete, minaQuizId);
        return response.ok()?response.getData():defauleValue;
    }

    @Override
    public Integer updateResultDeleteByMinaQuizId(Integer isDelete, Integer minaQuizId) {
        Response<Integer> response = miniAppFeignClient.updateResultDeleteByMinaQuizId(isDelete, minaQuizId);
        return response.ok()?response.getData():defauleValue;
    }

    @Override
    public Integer updateQuizInfoById(CfMinaQuiz cfMinaQuiz) {
        Response<Integer> response = miniAppFeignClient.updateQuizInfoById(JSON.toJSONString(cfMinaQuiz), ParamTypeEnum.CfMinaQuiz);
        return response.ok()?response.getData():defauleValue;
    }

    @Override
    public Integer insertPushConfig(CfMinaQuizPushConfig cfMinaQuizPushConfig) {
        Response<Integer> response = miniAppFeignClient.insertPushConfig(JSON.toJSONString(cfMinaQuizPushConfig), ParamTypeEnum.CfMinaQuizPushConfig);
        return response.ok()?response.getData():defauleValue;
    }

    @Override
    public Integer updateStatusById(Integer status, Integer id) {
        Response<Integer> response = miniAppFeignClient.updateStatusById(status, id);
        return response.ok()?response.getData():defauleValue;
    }

    @Override
    public List<CfMinaQuizPushConfig> findPushConfig(Integer offset, Integer limit) {
        Response<List<String>> response = miniAppFeignClient.findPushConfig(offset, limit);
        return response.ok()?AdminListUtil.getModelListFromResponse(response,CfMinaQuizPushConfig.class):Lists.newArrayList();
    }

    @Override
    public CfMinaQuizPushConfig getPushConfigById(Integer id) {
        Response<String> response = miniAppFeignClient.getPushConfigById(id);
        return response.ok()?AdminListUtil.getModelFromResponse(response,CfMinaQuizPushConfig.class):null;
    }

    @Override
    public Integer updatePushConfigById(CfMinaQuizPushConfig pushConfig) {
        Response<Integer> response = miniAppFeignClient.updatePushConfigById(JSON.toJSONString(pushConfig), ParamTypeEnum.CfMinaQuizPushConfig);
        return response.ok()?response.getData():defauleValue;
    }

    @Override
    public List<CfMiniQuizPush> findByQuizIdWithUserId(Integer minaQuizId, List<Long> userIds) {
        Response<List<String>> response = miniAppFeignClient.findByQuizIdWithUserId(minaQuizId, userIds);
        return response.ok()?AdminListUtil.getModelListFromResponse(response,CfMiniQuizPush.class):null;
    }

    @Override
    public List<CfMinaQuizRecord> findByUserIds(Integer minaQuizId, List<Long> userIds) {
        Response<List<String>> response = miniAppFeignClient.findByUserIds(minaQuizId, userIds);
        return response.ok()?AdminListUtil.getModelListFromResponse(response,CfMinaQuizRecord.class):null;
    }

    @Override
    public Integer insertPush(CfMiniQuizPush cfMiniQuizPush) {
        Response<Integer> response = miniAppFeignClient.insertPush(JSON.toJSONString(cfMiniQuizPush), ParamTypeEnum.CfMiniQuizPush);
        return response.ok()?response.getData():defauleValue;
    }

    @Override
    public Integer countAllPushConfig() {
        Response<Integer> response = miniAppFeignClient.countAllPushConfig();
        return response.ok()?response.getData():defauleValue;
    }

    @Override
    public Integer insertResultConfig(CfMinaQuizResultConfig cfMinaQuizResultConfig) {
        Response<Integer> response = miniAppFeignClient.insertResultConfig(JSON.toJSONString(cfMinaQuizResultConfig), ParamTypeEnum.CfMinaQuizResultConfig);
        return response.ok()?response.getData():defauleValue;
    }

    @Override
    public List<CfMinaQuizResultConfig> findResultConfigByResultIds(List<Integer> quizResultIds) {
        Response<List<String>> response = miniAppFeignClient.findResultConfigByResultIds(quizResultIds);
        return response.ok()?AdminListUtil.getModelListFromResponse(response,CfMinaQuizResultConfig.class):null;
    }


    @Override
    public int insertCfPublishPhase(CfPublishPhase cfPublishPhase) {
        Response<Integer> response = miniAppFeignClient.insertCfPublishPhase(JSON.toJSONString(cfPublishPhase), ParamTypeEnum.CfPublishPhase);
        return response.ok()?response.getData():defauleValue;
    }

    @Override
    public CfPublishPhase selectCfPublishPhaseByPhaseId(int phaseId) {
        Response<String> response = miniAppFeignClient.selectCfPublishPhaseByPhaseId(phaseId);
        return response.ok()?AdminListUtil.getModelFromResponse(response,CfPublishPhase.class):null;
    }

    @Override
    public List<CfPublishPhase> getCfPublishPhaseListPage(int pageNum, int pageSize) {
        Response<String> response = miniAppFeignClient.getCfPublishPhaseListPage(pageNum, pageSize);
        if (response.ok()){
            CfPublishPhaseResultModel cfPublishPhaseResultModel = AdminListUtil.getModelFromResponse(response, CfPublishPhaseResultModel.class);
            Page page = new Page();
            page.addAll(cfPublishPhaseResultModel.getCfPublishPhases());
            page.setTotal(cfPublishPhaseResultModel.getTotal());
            page.setPageNum(pageNum);
            page.setPageSize(pageSize);
            return page;
        }
        return Lists.newArrayList();
    }

    @Override
    public List<CfPublishPhase> listCfPublishPhaseByTimeLimit(Timestamp time, int limit) {
        Response<List<String>> response = miniAppFeignClient.listCfPublishPhaseByTimeLimit(time, limit);
        return response.ok()?AdminListUtil.getModelListFromResponse(response,CfPublishPhase.class):null;
    }

    @Override
    public List<CfPublishPhase> listCfPublishPhaseByPhaseIds(List<Integer> phaseIds) {
        Response<List<String>> response = miniAppFeignClient.listCfPublishPhaseByPhaseIds(phaseIds);
        return response.ok()?AdminListUtil.getModelListFromResponse(response,CfPublishPhase.class):null;
    }

    @Override
    public PushRecord buildFirstApproveRefuse(long userId, String nickName, String infoUuid, String failMsg, String url, String imageUrl, AppPushTemplate appPushTemplate, List<AppPushTemplateParam> appPushTemplateParamList, int subBizType) {
        Response<PushRecord> response = miniAppFeignClient.buildFirstApproveRefuse(userId, nickName, infoUuid, failMsg, url, imageUrl, appPushTemplate.getTemplateTitle(),appPushTemplate.getId(), appPushTemplateParamList, subBizType);
        return response.ok()?response.getData():null;
    }

    @Override
    public List<PushRecord> newInfoApplySuccess(CrowdfundingInfo crowdfundingInfo) {
        Response<List<PushRecord>> response = miniAppFeignClient.newInfoApplySuccess(JSON.toJSONString(crowdfundingInfo), ParamTypeEnum.CrowdfundingInfo);
        return response.ok()?response.getData():Lists.newArrayList();
    }

    @Override
    public List<PushRecord> newNeedModifiedSuccess(CrowdfundingInfo crowdfundingInfo) {
        Response<List<PushRecord>> response = miniAppFeignClient.newNeedModifiedSuccess(JSON.toJSONString(crowdfundingInfo), ParamTypeEnum.CrowdfundingInfo);
        return response.ok()?response.getData():Lists.newArrayList();
    }

    @Override
    public CfTopicComment selectCfTopicCommentById(long id) {
        Response<String> response = miniAppFeignClient.selectCfTopicCommentById(id);
        return response.ok()?AdminListUtil.getModelFromResponse(response,CfTopicComment.class):null;
    }

    @Override
    public int deleteCfTopicCommentById(long id) {
        Response<Integer> response = miniAppFeignClient.deleteCfTopicCommentById(id);
        return response.ok()?response.getData():defauleValue;
    }

    @Override
    public int deleteCfTopicById(int id) {
        Response<Integer> response = miniAppFeignClient.deleteCfTopicById(id);
        return response.ok()?response.getData():defauleValue;
    }

    @Override
    public int updateCfTopicById(int id, CfTopic cfTopic) {
        Response<Integer> response = miniAppFeignClient.updateCfTopicById(id, JSON.toJSONString(cfTopic), ParamTypeEnum.CfTopic);
        return response.ok()?response.getData():defauleValue;
    }

    @Override
    public CfTopic selectCfTopicById(int id) {
        Response<String> response = miniAppFeignClient.selectCfTopicById(id);
        return response.ok()?AdminListUtil.getModelFromResponse(response,CfTopic.class):null;
    }

    @Override
    public List<CfTopic> selectCfTopicByPhaseId(int phaseId) {
        Response<List<String>> response = miniAppFeignClient.selectCfTopicByPhaseId(phaseId);
        return response.ok()?AdminListUtil.getModelListFromResponse(response,CfTopic.class):Lists.newArrayList();
    }

    @Override
    public List<CfTopic> listCfTopicByIds(List<Integer> topicIds) {
        Response<List<String>> response = miniAppFeignClient.listCfTopicByIds(topicIds);
        return response.ok()?AdminListUtil.getModelListFromResponse(response,CfTopic.class):Lists.newArrayList();
    }

    @Override
    public CfTopicKeyword selectCfTopicKeywordById(int id) {
        Response<String> response = miniAppFeignClient.selectCfTopicKeywordById(id);
        return response.ok()?AdminListUtil.getModelFromResponse(response,CfTopicKeyword.class):null;
    }

    @Override
    public List<CfTopicKeyword> listCfTopicKeywordListByIds(List<Integer> ids) {
        Response<List<String>> response = miniAppFeignClient.listCfTopicKeywordListByIds(ids);
        return response.ok()?AdminListUtil.getModelListFromResponse(response,CfTopicKeyword.class):Lists.newArrayList();
    }

    @Override
    public CfTopicKeyword selectCfTopicKeywordByKeyword(String keyword) {
        Response<String> response = miniAppFeignClient.selectCfTopicKeywordByKeyword(keyword);
        return response.ok()?AdminListUtil.getModelFromResponse(response,CfTopicKeyword.class):null;
    }
}
