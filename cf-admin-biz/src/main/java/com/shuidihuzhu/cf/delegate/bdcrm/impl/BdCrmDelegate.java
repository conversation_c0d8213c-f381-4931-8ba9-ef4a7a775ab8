package com.shuidihuzhu.cf.delegate.bdcrm.impl;

import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.admin.util.AdminListUtil;
import com.shuidihuzhu.cf.delegate.bdcrm.IBdCrmDelegate;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackBdCrmFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.VolunteerInfoUuidModel;
import com.shuidihuzhu.common.web.model.Response;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2019/9/23 2:55 PM
 */
@Service
public class BdCrmDelegate implements IBdCrmDelegate {
    @Autowired
    private CfClewtrackBdCrmFeignClient cfClewtrackBdCrmFeignClient;

    @Override
    public List<VolunteerInfoUuidModel> getVolunteerInfoUuidModelbyUniqueCodeListWithTime(List<String> uniqueCodeList, String startTime, String endTime) {
        List<VolunteerInfoUuidModel> list = AdminListUtil.getList(50, uniqueCodeList, new Function<List<String>, List<VolunteerInfoUuidModel>>() {
            @Nullable
            @Override
            public List<VolunteerInfoUuidModel> apply(@Nullable List<String> input) {
                Response<List<VolunteerInfoUuidModel>> listResponse = cfClewtrackBdCrmFeignClient.getVolunteerInfoUuidModelbyUniqueCodeListWithTime(input, startTime, endTime);
                return listResponse.ok() ? listResponse.getData() : Lists.newArrayList();
            }
        });
        return list;
    }

    @Override
    public List<VolunteerInfoUuidModel> getVolunteerInfoUuidModelbyVolunteerTypeWithTime(Integer volunteerType, String startTime, String endTime) {
        Response<List<VolunteerInfoUuidModel>> listResponse = cfClewtrackBdCrmFeignClient.getVolunteerInfoUuidModelbyVolunteerTypeWithTime(volunteerType, startTime, endTime);
        return listResponse.ok() ? listResponse.getData() : Lists.newArrayList();
    }
}
