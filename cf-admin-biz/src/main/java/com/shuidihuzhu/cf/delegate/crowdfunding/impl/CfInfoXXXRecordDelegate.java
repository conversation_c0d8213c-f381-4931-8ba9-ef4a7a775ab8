package com.shuidihuzhu.cf.delegate.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.admin.util.AdminListUtil;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICfInfoXXXRecordDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecordCountModel;
import com.shuidihuzhu.client.cf.api.chaiku.CfInfoShareRecordFeignClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * @author: wanghui
 * @create: 2019/7/5 4:25 PM
 */
@Service
@Slf4j
public class CfInfoXXXRecordDelegate implements ICfInfoXXXRecordDelegate {

    @Autowired
    private CfInfoShareRecordFeignClient cfInfoShareRecordFeignClient;
    @Override
    public List<CfInfoShareRecordCountModel> getShareCount(List<Integer> infoIds) {
        Response<List<String>> response = cfInfoShareRecordFeignClient.getShareCount(infoIds);
        return response.ok()? AdminListUtil.getModelListFromResponse(response,CfInfoShareRecordCountModel.class): Lists.newArrayList();
    }

    @Override
    public List<CfInfoShareRecord> findCfInfoShareRecord(Long userId, String startTime, String endTime) {

        Response<List<String>> response = cfInfoShareRecordFeignClient.findCfInfoShareRecord(userId, startTime, endTime);

        return response.ok()? AdminListUtil.getModelListFromResponse(response,CfInfoShareRecord.class): Lists.newArrayList();
    }

    @Override
    public int getCountByInfoId(int infoId) {
        // 时间是 当前日期+1天   这样dateCreated<T+1   相当于该条件没有
        Response<Integer> response = cfInfoShareRecordFeignClient.getCountByInfoId(infoId, DateUtil.addDay(DateUtil.getCurrentDate(), 1).getTime());
        return response.ok()?response.getData():0;
    }
    @Override
    public List<CfInfoShareRecord> getByInfoId(int caseId,  long startTime, long endTime,  int offset,  int limit){

        Response<List<String>> response =  cfInfoShareRecordFeignClient.getByInfoId(caseId,startTime,endTime,offset,limit);
        return response.ok()?AdminListUtil.getModelListFromResponse(response,CfInfoShareRecord.class): Lists.newArrayList();

    }

    @Override
    public List<CfInfoShareRecord> getListByInfoIdAndUserIds(int infoId, List<Long> userIds) {
        Response<List<CfInfoShareRecord>> response = cfInfoShareRecordFeignClient.getListByInfoIdAndUserIds(infoId, userIds);
        return Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(Lists.newArrayList());
    }
}
