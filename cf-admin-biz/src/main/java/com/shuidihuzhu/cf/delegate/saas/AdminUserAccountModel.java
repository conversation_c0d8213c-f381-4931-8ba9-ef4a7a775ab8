package com.shuidihuzhu.cf.delegate.saas;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;


@Data
public class AdminUserAccountModel {

    private int id;
    private String mis;
    private String name;
    private String mobile;

    private String mail;
    private String idCard;
    private NumberMaskVo idCardMask;
    private Integer status;
    private Integer accountType;
    private String phoneSeatNum;


    public static AdminUserAccountModel convertFromAuthUser(AuthUserDto userDto, ShuidiCipher shuidiCipher) {
        if (userDto == null) {
            return null;
        }
        AdminUserAccountModel wrapperDTO = new AdminUserAccountModel();
        wrapperDTO.setId(Math.toIntExact(userDto.getUserId()));
        wrapperDTO.setName(userDto.getUserName());
        wrapperDTO.setMis(userDto.getLoginName());
        wrapperDTO.setMobile(shuidiCipher.decrypt(userDto.getCryptoMobile()));

        wrapperDTO.setMail(shuidiCipher.decrypt(userDto.getCryptoEmail()));
        wrapperDTO.setIdCard(shuidiCipher.decrypt(userDto.getCryptoIdCard()));
        wrapperDTO.setStatus(userDto.getStatus());
        wrapperDTO.setAccountType(userDto.getAccountType());
        wrapperDTO.setPhoneSeatNum(userDto.getPhoneSeatNum());

        return wrapperDTO;
    }


    public static AdminUserAccountModel convertFromAuthUserNotCover(AuthUserDto userDto, ShuidiCipher shuidiCipher) {
        if (userDto == null) {
            return null;
        }
        AdminUserAccountModel wrapperDTO = new AdminUserAccountModel();
        wrapperDTO.setId(Math.toIntExact(userDto.getUserId()));
        wrapperDTO.setName(userDto.getUserName());
        wrapperDTO.setMis(userDto.getLoginName());
        //获取非掩码mobile
        wrapperDTO.setMobile(userDto.getCryptoMobile());
        wrapperDTO.setMail(shuidiCipher.decrypt(userDto.getCryptoEmail()));
        wrapperDTO.setIdCard(shuidiCipher.decrypt(userDto.getCryptoIdCard()));
        wrapperDTO.setStatus(userDto.getStatus());
        wrapperDTO.setAccountType(userDto.getAccountType());
        wrapperDTO.setPhoneSeatNum(userDto.getPhoneSeatNum());

        return wrapperDTO;
    }

    public static List<AdminUserAccountModel> convertFromAuthUserList(List<AuthUserDto> userDtos, ShuidiCipher shuidiCipher) {
        List<AdminUserAccountModel> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(userDtos)) {
            return result;
        }

        for (AuthUserDto userDto : userDtos) {
            result.add(convertFromAuthUser(userDto, shuidiCipher));
        }

        return result;
    }

    public static List<AdminUserAccountModel> convertFromAuthUserList(List<AuthUserDto> userDtos, Integer isCoverKey, ShuidiCipher shuidiCipher) {
        List<AdminUserAccountModel> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(userDtos)) {
            return result;
        }
        for (AuthUserDto userDto : userDtos) {
            if (isCoverKey == 0) {
                result.add(convertFromAuthUserNotCover(userDto, shuidiCipher));
            }
            if (isCoverKey == 1) {
                result.add(convertFromAuthUser(userDto, shuidiCipher));
            }
        }
        return result;
    }


    public static Map<Integer, AdminUserAccountModel> convertFromUserWrapper(Map<Integer, AuthUserDto> userDtoMap, ShuidiCipher shuidiCipher) {
        Map<Integer, AdminUserAccountModel> wrapperDTOMap = Maps.newHashMap();

        if (MapUtils.isEmpty(userDtoMap)) {
            return wrapperDTOMap;
        }

        for (Map.Entry<Integer, AuthUserDto> entry : userDtoMap.entrySet()) {
            wrapperDTOMap.put(entry.getKey(), convertFromAuthUser(entry.getValue(), shuidiCipher));
        }

        return wrapperDTOMap;
    }



}
