package com.shuidihuzhu.cf.delegate.order;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrderModelResult;
import com.shuidihuzhu.common.web.model.Response;

/**
 * @Author: l<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020/10/28 11:15
 */
public interface CfOrderTidbFeignClientDelegate {
    Response<CrowdfundingOrderModelResult> selectByUserIdAndThirdType(Long userId,
                                                                      Integer caseId,
                                                                      Integer thirdType,
                                                                      Integer current,
                                                                      Integer pageSize);

    Response<CrowdfundingOrderModelResult> selectByCaseIdAndTimeV1(int caseId,
                                                                   Long begin,
                                                                   Long end,
                                                                   int current,
                                                                   int pageSize);
}
