package com.shuidihuzhu.cf.delegate.order.impl;

import com.shuidihuzhu.cf.delegate.order.CfOrderTidbFeignClientDelegate;
import com.shuidihuzhu.cf.finance.client.feign.order.CfOrderTidbFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrderModelResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: lianghongchao
 * @Date: 2020/10/28 11:17
 */
@Slf4j
@Service
public class CfOrderTidbFeignClientDelegateImpl implements CfOrderTidbFeignClientDelegate {
    @Autowired
    private CfOrderTidbFeignClient cfOrderTidbFeignClient;

    @Override
    public Response<CrowdfundingOrderModelResult> selectByUserIdAndThirdType(Long userId, Integer caseId,
                                                                             Integer thirdType, Integer current,
                                                                             Integer pageSize) {
        try {
            FeignResponse<CrowdfundingOrderModelResult> modelResultFeignResponse =
                    cfOrderTidbFeignClient.selectByUserIdAndThirdType(userId, caseId, thirdType, current, pageSize);
            return NewResponseUtil.makeSuccess(modelResultFeignResponse.getData());
        } catch (Exception e) {
            log.error("", e);
        }
        return NewResponseUtil.makeFail(null);
    }

    @Override
    public Response<CrowdfundingOrderModelResult> selectByCaseIdAndTimeV1(int caseId, Long begin, Long end,
                                                                          int current, int pageSize) {
        try {
            FeignResponse<CrowdfundingOrderModelResult> modelResultFeignResponse =
                    cfOrderTidbFeignClient.selectByCaseIdAndTimeV1(caseId, begin, end, current, pageSize);
            return NewResponseUtil.makeSuccess(modelResultFeignResponse.getData());
        } catch (Exception e) {
            log.error("", e);
        }
        return NewResponseUtil.makeFail(null);
    }
}
