package com.shuidihuzhu.cf.delegate.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.cf.model.task.CfInfoTask;
import com.shuidihuzhu.cf.param.DepositPayeeAccountParam;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.admin.BasicExample;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @author: wanghui
 * @create: 2019/6/12 2:06 PM
 */
public interface ICrowdfundingDelegate {
    CfBaseInfoTemplateRecord selectByInfoUuid(String infoUuid);



    void addCrowdfundingInfoTag(CrowdfundingInfo info, CfFinishStatus cfFinishStatus);

    CrowdfundingIdCase getCrowdfundingIdCaseByInfoId(int caseId);
    boolean updateCrowdfundingIdCaseStatus(int caseId, CrowdfundingIdCaseStatusEnum crowdfundingIdCaseStatusEnum);
    void sendIdCaseFailMsg(CrowdfundingInfo crowdfundingInfo);
    List<CrowdfundingIdCase> getByCryptoIdCard(String cryptoIdCard);

    List<CrowdfundingAttachmentVo> getFundingAttachment(int parentId);

    Map<Integer, List<CrowdfundingAttachmentVo>> getByInfoIdList(List<Integer> infoIdList, AttachmentTypeEnum type);

    List<CrowdfundingAttachmentVo> getAttachmentsByType(int parentId, AttachmentTypeEnum type);

    int addCrowdfundingAttachmentList(List<CrowdfundingAttachment> crowdfundingAttachmentList);

    int addCrowdfundingAttachment(CrowdfundingAttachment crowdfundingAttachment);

    int deleteByParentIdAndType(int parentId, List<AttachmentTypeEnum> typeList);

    int addForAdmin(List<CrowdfundingAttachment> crowdfundingAttachmentList);

    int updateCrowdfundingInfo(CrowdfundingInfo crowdfundingInfo);

    CrowdfundingInfo getCrowdfundingInfoByInfoId(String infoId);

    CrowdfundingInfo getFundingInfoById(Integer id);

    Map<Integer, CrowdfundingInfo> getMapByIds(List<Integer> ids);

    int updateEndTime(int id, Date endTime);

    boolean updateBeginAndEndTime(int infoId, Date beginTime, Date endTime);

    int updateCrowdfundingStatus(int id, CrowdfundingStatus newStatus, CrowdfundingStatus oldStatus);

    CrowdfundingInfo getFirstCrowdfundingInfoByUserId(long userId);

    List<CrowdfundingInfo> selectCrowdfundingInfoListByUserId(long userId);

    int updateCrowdfundingInfoStatusByInfoId(String infoUuId, int type, CrowdfundingInfoStatusEnum status);

    List<CrowdfundingInfoStatus> getCrowdfundingInfoStatusListByInfoUuid(String infoUuid);


    void afterCfOperatingRecord(CfOperatingRecord cfOperatingRecord);
    CfOperatingRecord before(CrowdfundingInfo crowdfundingInfo,long userId, String userName,
                             CfOperatingRecordEnum.Type type, CfOperatingRecordEnum.Role role);
    CfOperatingRecord beforeCrowdfundingInfoComment(CrowdfundingInfo crowdfundingInfo,long userId, String userName,
                             CfOperatingRecordEnum.Type type, CfOperatingRecordEnum.Role role, String comment);

    List<CrowdfundingInfo> getCrowdfundingInfoByUserId(long userId);

    void pushLaunchMsg(CrowdfundingInfo crowdfundingInfo, int platform) throws Exception;

    CfInfoSimpleModel getCfInfoSimpleModel(String infoUuid);

    CfInfoSimpleModel getCfInfoSimpleModelById(Integer infoId);

    List<CfInfoSimpleModel> getCfInfoSimpleModelListByIds(List<Integer> ids);

    List<CrowdFundingProgress> getActivityProgress( Integer activityId,  List<Integer> types,  Integer offset, Integer limit);

    List<CrowdFundingProgress> queryByType(int caseId, int pType);

    CrowdFundingProgress getActivityProgressById( Integer id);

    int updateType(int id, int fromType, int toType);

    void addProgress(CrowdFundingProgress progress);

    int updateForDelete(int crowdfundingId, int id);

    boolean updateImageUrls(int id, String imageUrls);

    boolean deleteMaskImageUrls(int processId, String imageUrls);

    int updateCrowdfundingInfoPayee(CrowdfundingInfoPayee crowdfundingInfoPayee);

    int addCrowdfundingInfoPayee(CrowdfundingInfoPayee crowdfundingInfoPayee);

    CrowdfundingInfoPayee getCrowdfundingInfoPayeeByInfoUuid(String infoUuid);

    CrowdfundingInfoHospitalPayee getCrowdfundingInfoHospitalPayeeByInfoUuid(String infoUuid);

    int addCrowdfundingInfoHospitalPayee(CrowdfundingInfoHospitalPayee crowdfundingInfoHospitalPayee);

    int updateCrowdfundingInfoHospitalPayee(CrowdfundingInfoHospitalPayee crowdfundingInfoHospitalPayee);

    Response<Void> checkOrOpenWithDrawAcct(DepositPayeeAccountParam depositPayeeAccountParam);

    CfCharityPayee getCfCharityPayeeByUUid(String uuid);

    int subtractAmount(int id, int amount);


    Map<Integer, CfInfoStat> mapByIds(List<Integer> ids);

    CfInfoStat getById(Integer id);

    int updateTimes(String infoUuid, CfTaskEnum.Rule cfTaskTypeEnum);

    void publishHospitalTask(CrowdfundingInfo crowdfundingInfo);

    CrowdfundingInfo getCaseInfoById(int caseId);

    void sendPayeeInfoRelation(CrowdfundingInfo crowdfundingInfo);
}
