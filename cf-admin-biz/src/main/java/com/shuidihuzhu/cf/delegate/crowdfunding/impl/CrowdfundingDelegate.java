package com.shuidihuzhu.cf.delegate.crowdfunding.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.admin.util.AdminListUtil;
import com.shuidihuzhu.cf.biz.admin.exception.ServiceRuntimeException;
import com.shuidihuzhu.cf.client.feign.CfUserStatFeignClient;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.enums.admin.errors.CommonErrorEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.finance.client.feign.deposit.CfDepositAccountFeignClient;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.cf.param.DepositPayeeAccountParam;
import com.shuidihuzhu.client.cf.api.chaifenbeta.crowdfunding.CrowdfundingChaiFenV2FeignClient;
import com.shuidihuzhu.client.cf.api.client.CfImageMaskFeignClient;
import com.shuidihuzhu.client.cf.api.model.enums.ParamTypeEnum;
import com.shuidihuzhu.client.model.enums.ImageMaskBizEnum;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2019/6/12 2:07 PM
 */
@Service
@Slf4j
@RefreshScope
public class CrowdfundingDelegate implements ICrowdfundingDelegate {

    @Resource
    private CrowdfundingFeignClient crowdfundingFeignClient;
    @Resource
    private CfUserStatFeignClient cfUserStatFeignClient;
    @Resource
    private CrowdfundingChaiFenV2FeignClient crowdfundingChaiFenFeignClient;
    @Autowired
    private CfDepositAccountFeignClient cfDepositAccountFeignClient;
    @Resource
    private CfImageMaskFeignClient cfImageMaskFeignClient;

    private static final int defaultValue = 0;
    @Value("${cfbiz.unuse-cf-biz:false}")
    private boolean unuseCfBiz;

    @Override
    public CfBaseInfoTemplateRecord selectByInfoUuid(String infoUuid) {
        Response<String> response = crowdfundingChaiFenFeignClient.selectByInfoUuid(infoUuid);
        return response.ok() ? AdminListUtil.getModelFromResponse(response, CfBaseInfoTemplateRecord.class) : null;
    }




    @Override
    public void addCrowdfundingInfoTag(CrowdfundingInfo info, CfFinishStatus cfFinishStatus) {
        crowdfundingChaiFenFeignClient.addCrowdfundingInfoTag(JSON.toJSONString(info),
                ParamTypeEnum.CrowdfundingInfo, cfFinishStatus.getValue());
    }

    @Override
    public CrowdfundingIdCase getCrowdfundingIdCaseByInfoId(int caseId) {
        Response<String> response = crowdfundingChaiFenFeignClient.getCrowdfundingIdCaseByInfoId(caseId);
        return response.ok() ? AdminListUtil.getModelFromResponse(response, CrowdfundingIdCase.class) : null;
    }


    @Override
    public boolean updateCrowdfundingIdCaseStatus(int caseId, CrowdfundingIdCaseStatusEnum crowdfundingIdCaseStatusEnum) {
        Response<Boolean> response = crowdfundingChaiFenFeignClient.updateCrowdfundingIdCaseStatus(caseId, crowdfundingIdCaseStatusEnum.getCode());
        return response.ok() ? response.getData() : false;
    }

    @Override
    public void sendIdCaseFailMsg(CrowdfundingInfo crowdfundingInfo) {
        crowdfundingChaiFenFeignClient.sendIdCaseFailMsg(JSON.toJSONString(crowdfundingInfo), ParamTypeEnum.CrowdfundingInfo);
    }

    @Override
    public List<CrowdfundingIdCase> getByCryptoIdCard(String cryptoIdCard) {
        Response<List<String>> response = crowdfundingChaiFenFeignClient.getByCryptoIdCard(cryptoIdCard);
        return response.ok() ? AdminListUtil.getModelListFromResponse(response, CrowdfundingIdCase.class) : Lists.newArrayList();
    }

    @Override
    public List<CrowdfundingAttachmentVo> getFundingAttachment(int parentId) {
        Response<List<String>> response = crowdfundingChaiFenFeignClient.getFundingAttachment(parentId);
        return response.ok() ? AdminListUtil.getModelListFromResponse(response, CrowdfundingAttachmentVo.class) : Lists.newArrayList();
    }

    @Override
    public Map<Integer, List<CrowdfundingAttachmentVo>> getByInfoIdList(List<Integer> infoIdList, AttachmentTypeEnum typeEnum) {
        Response<String> response = crowdfundingChaiFenFeignClient.getByInfoIdList(infoIdList, typeEnum.value());
        if (response.ok()) {
            try {
                return JSON.parseObject(response.getData(), new TypeReference<Map<Integer, List<CrowdfundingAttachmentVo>>>() {});//已检查过
            } catch (Exception e) {
                log.error(this.getClass().getSimpleName() + " getByInfoIdList  反序列化失败 response:{}", JSON.toJSONString(response), e);
            }
        }
        return Maps.newHashMap();
    }

    @Override
    public List<CrowdfundingAttachmentVo> getAttachmentsByType(int parentId, AttachmentTypeEnum type) {
        Response<List<String>> response = crowdfundingChaiFenFeignClient.getAttachmentsByType(parentId, type.value());
        return response.ok() ? AdminListUtil.getModelListFromResponse(response, CrowdfundingAttachmentVo.class) : Lists.newArrayList();
    }

    @Override
    public int addCrowdfundingAttachmentList(List<CrowdfundingAttachment> crowdfundingAttachmentList) {
        Response<Integer> response = crowdfundingChaiFenFeignClient.addCrowdfundingAttachmentList(
                AdminListUtil.getListStringFromListModel(crowdfundingAttachmentList),
                ParamTypeEnum.CrowdfundingAttachment);
        return response.ok() ? response.getData() : defaultValue;
    }

    @Override
    public int addCrowdfundingAttachment(CrowdfundingAttachment crowdfundingAttachment) {
        Response<Integer> response = crowdfundingChaiFenFeignClient.addCrowdfundingAttachment(JSON.toJSONString(crowdfundingAttachment),
                ParamTypeEnum.CrowdfundingAttachment);
        return response.ok() ? response.getData() : defaultValue;
    }

    @Override
    public int deleteByParentIdAndType(int parentId, List<AttachmentTypeEnum> typeList) {
        List<Integer> typeValueList = typeList.stream().map(AttachmentTypeEnum::value).collect(Collectors.toList());
        Response<Integer> response = crowdfundingChaiFenFeignClient.deleteByParentIdAndType(parentId, typeValueList);
        return response.ok() ? response.getData() : defaultValue;
    }

    @Override
    public int addForAdmin(List<CrowdfundingAttachment> crowdfundingAttachmentList) {
        Response<Integer> response = crowdfundingChaiFenFeignClient.addForAdmin(AdminListUtil.getListStringFromListModel(crowdfundingAttachmentList),
                ParamTypeEnum.CrowdfundingAttachment);
        return response.ok() ? response.getData() : defaultValue;
    }

    @Override
    public int updateCrowdfundingInfo(CrowdfundingInfo crowdfundingInfo) {
        Response<Integer> response = crowdfundingChaiFenFeignClient.updateCrowdfundingInfoTitle(JSON.toJSONString(crowdfundingInfo),
                ParamTypeEnum.CrowdfundingInfo);
        return response.ok() ? response.getData() : defaultValue;
    }

    @Override
    public CrowdfundingInfo getCrowdfundingInfoByInfoId(String infoId) {
        FeignResponse<CrowdfundingInfo> crowdfundingByuuid = FeignResponse.fallback();
        try {
            crowdfundingByuuid = crowdfundingFeignClient.getCrowdfundingByuuid(infoId);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + "crowdfundingByuuid err:", e);
        }
        log.info(this.getClass().getSimpleName() + "getCrowdfundingByuuid request:{} response:{}",
                infoId, JSON.toJSONString(crowdfundingByuuid));
        return crowdfundingByuuid.getData();
    }

    @Override
    public CrowdfundingInfo getFundingInfoById(Integer id) {
        Response<String> response = crowdfundingChaiFenFeignClient.getFundingInfoById(id);
        if (response.ok()) {
            return AdminListUtil.getModelFromResponse(response, CrowdfundingInfo.class);
        }
        throw new RuntimeException("调用cf-api获取cfInfo失败");
    }

    @Override
    public Map<Integer, CrowdfundingInfo> getMapByIds(List<Integer> ids) {
        Response<String> response = crowdfundingChaiFenFeignClient.getMapByIds(ids);
        if (response.ok()) {
            try {
                return JSON.parseObject(response.getData(), new TypeReference<Map<Integer, CrowdfundingInfo>>() {});//已检查过
            } catch (Exception e) {
                log.error(this.getClass().getSimpleName() + " getMapByIds  反序列化失败 response:{}", JSON.toJSONString(response), e);
            }
        }
        throw new ServiceRuntimeException(CommonErrorEnum.OUT_SERVICE_REQUEST_ERROR);
    }

    @Override
    public int updateEndTime(int id, Date endTime) {
        Response<Integer> response = crowdfundingChaiFenFeignClient.updateEndTime(id,
                endTime.getTime());
        return response.ok() ? response.getData() : defaultValue;
    }

    @Override
    public boolean updateBeginAndEndTime(int infoId, Date beginTime, Date endTime) {
        if (beginTime == null || endTime == null) {
            return false;
        }
        Response<Boolean> response = crowdfundingChaiFenFeignClient.updateBeginAndEndTime(infoId,
                beginTime.getTime(), endTime.getTime());
        return response.ok() ? response.getData() : false;
    }

    @Override
    public int updateCrowdfundingStatus(int id, CrowdfundingStatus newStatus, CrowdfundingStatus oldStatus) {
        if (newStatus == null || oldStatus == null) {
            return defaultValue;
        }
        Response<Integer> response = crowdfundingChaiFenFeignClient.updateCrowdfundingStatus(id,
                newStatus.value(), oldStatus.value());
        return response.ok() ? response.getData() : defaultValue;
    }

    @Override
    public CrowdfundingInfo getFirstCrowdfundingInfoByUserId(long userId) {
        Response<String> response = crowdfundingChaiFenFeignClient.getFirstCrowdfundingInfoByUserId(userId);
        return response.ok() ? AdminListUtil.getModelFromResponse(response, CrowdfundingInfo.class) : null;
    }

    @Override
    public List<CrowdfundingInfo> selectCrowdfundingInfoListByUserId(long userId) {
        Response<List<String>> response = crowdfundingChaiFenFeignClient.selectCrowdfundingInfoListByUserId(userId);
        return response.ok() ? AdminListUtil.getModelListFromResponse(response, CrowdfundingInfo.class) : Lists.newArrayList();
    }

    @Override
    public int updateCrowdfundingInfoStatusByInfoId(String infoUuId, int type, CrowdfundingInfoStatusEnum status) {
        Response<Integer> response = crowdfundingChaiFenFeignClient.updateCrowdfundingInfoStatusByInfoId(infoUuId, type, status.getCode());
        return response.ok() ? response.getData() : defaultValue;
    }

    @Override
    public List<CrowdfundingInfoStatus> getCrowdfundingInfoStatusListByInfoUuid(String infoUuid) {
        Response<List<String>> response = crowdfundingChaiFenFeignClient.getCrowdfundingInfoStatusListByInfoUuid(infoUuid);
        return response.ok() ? AdminListUtil.getModelListFromResponse(response, CrowdfundingInfoStatus.class) : Lists.newArrayList();
    }


    @Override
    public void afterCfOperatingRecord(CfOperatingRecord cfOperatingRecord) {
        crowdfundingChaiFenFeignClient.afterCfOperatingRecord(JSON.toJSONString(cfOperatingRecord), ParamTypeEnum.CfOperatingRecord);
    }

    @Override
    public CfOperatingRecord before(CrowdfundingInfo crowdfundingInfo, long userId, String userName, CfOperatingRecordEnum.Type type, CfOperatingRecordEnum.Role role) {
        if (type == null || role == null) {
            return null;
        }
        Response<String> response = crowdfundingChaiFenFeignClient.before(JSON.toJSONString(crowdfundingInfo), ParamTypeEnum.CrowdfundingInfo, userId,
                userName, type.getCode(), role.getCode());
        return response.ok() ? AdminListUtil.getModelFromResponse(response, CfOperatingRecord.class) : null;
    }

    @Override
    public CfOperatingRecord beforeCrowdfundingInfoComment(CrowdfundingInfo crowdfundingInfo, long userId, String userName, CfOperatingRecordEnum.Type type, CfOperatingRecordEnum.Role role, String comment) {
        if (type == null || role == null) {
            return null;
        }
        Response<String> response = crowdfundingChaiFenFeignClient.beforeCrowdfundingInfoComment(JSON.toJSONString(crowdfundingInfo),
                ParamTypeEnum.CrowdfundingInfo, userId, userName, type.getCode(), role.getCode(), comment);
        return response.ok() ? AdminListUtil.getModelFromResponse(response, CfOperatingRecord.class) : null;
    }

    @Override
    public List<CrowdfundingInfo> getCrowdfundingInfoByUserId(long userId) {
        Response<List<String>> response = crowdfundingChaiFenFeignClient.getCrowdfundingInfoByUserId(userId);
        return response.ok() ? AdminListUtil.getModelListFromResponse(response, CrowdfundingInfo.class) : Lists.newArrayList();
    }

    @Override
    public void pushLaunchMsg(CrowdfundingInfo crowdfundingInfo, int platform) throws Exception {
        crowdfundingChaiFenFeignClient.pushLaunchMsg(JSON.toJSONString(crowdfundingInfo), ParamTypeEnum.CrowdfundingInfo, platform);
    }

    @Override
    public CfInfoSimpleModel getCfInfoSimpleModel(String infoUuid) {
        Response<String> response = crowdfundingChaiFenFeignClient.getCfInfoSimpleModel(infoUuid);
        return response.ok() ? AdminListUtil.getModelFromResponse(response, CfInfoSimpleModel.class) : null;
    }

    @Override
    public CfInfoSimpleModel getCfInfoSimpleModelById(Integer infoId) {
        Response<String> response = crowdfundingChaiFenFeignClient.getCfInfoSimpleModelById(infoId);
        return response.ok() ? AdminListUtil.getModelFromResponse(response, CfInfoSimpleModel.class) : null;
    }

    @Override
    public List<CfInfoSimpleModel> getCfInfoSimpleModelListByIds(List<Integer> ids) {
        Response<List<String>> response = crowdfundingChaiFenFeignClient.getCfInfoSimpleModelListByIds(ids);
        return response.ok() ? AdminListUtil.getModelListFromResponse(response, CfInfoSimpleModel.class) : Lists.newArrayList();
    }

    @Override
    public List<CrowdFundingProgress> getActivityProgress(Integer activityId, List<Integer> types, Integer offset, Integer limit) {
        Response<List<String>> response = crowdfundingChaiFenFeignClient.getActivityProgress(activityId, types, offset, limit);
        return response.ok() ? AdminListUtil.getModelListFromResponse(response, CrowdFundingProgress.class) : Lists.newArrayList();
    }

    @Override
    public List<CrowdFundingProgress> queryByType(int caseId, int pType) {
        Response<List<String>> response = crowdfundingChaiFenFeignClient.queryByType(caseId, pType);
        return response.ok() ? AdminListUtil.getModelListFromResponse(response, CrowdFundingProgress.class) : Lists.newArrayList();
    }

    @Override
    public CrowdFundingProgress getActivityProgressById(Integer id) {
        Response<String> response = crowdfundingChaiFenFeignClient.getActivityProgressById(id);
        return response.ok() ? AdminListUtil.getModelFromResponse(response, CrowdFundingProgress.class) : null;
    }

    @Override
    public int updateType(int id, int fromType, int toType) {
        Response<Integer> response = crowdfundingChaiFenFeignClient.updateType(id, fromType, toType);
        return response.ok() ? response.getData() : defaultValue;
    }

    @Override
    public void addProgress(CrowdFundingProgress progress) {
        crowdfundingChaiFenFeignClient.addProgress(JSON.toJSONString(progress), ParamTypeEnum.CrowdFundingProgress);
    }

    @Override
    public int updateForDelete(int crowdfundingId, int id) {
        Response<Integer> response = crowdfundingChaiFenFeignClient.updateForDelete(crowdfundingId, id);
        return response.ok() ? response.getData() : defaultValue;
    }

    @Override
    public boolean updateImageUrls(int id, String imageUrls) {
        Response<Boolean> response = crowdfundingChaiFenFeignClient.updateImageUrls(id, imageUrls);
        return response.ok() ? response.getData() : false;
    }

    @Override
    public boolean deleteMaskImageUrls(int processId, String imageUrl) {

        Response<Integer> response = cfImageMaskFeignClient.deleteMaskProgressImage(Lists.newArrayList((long) processId), Lists.newArrayList(ImageMaskBizEnum.CF_PROGRESS_IMAGE.getCode()), imageUrl);

        return response.ok() && Objects.nonNull(response.getData()) && response.getData() > 0;
    }

    @Override
    public int updateCrowdfundingInfoPayee(CrowdfundingInfoPayee crowdfundingInfoPayee) {
        Response<Integer> response = crowdfundingChaiFenFeignClient.updateCrowdfundingInfoPayee(JSON.toJSONString(crowdfundingInfoPayee),
                ParamTypeEnum.CrowdfundingInfoPayee);
        return response.ok() ? response.getData() : defaultValue;
    }

    @Override
    public int addCrowdfundingInfoPayee(CrowdfundingInfoPayee crowdfundingInfoPayee) {
        Response<Integer> response = crowdfundingChaiFenFeignClient.addCrowdfundingInfoPayee(JSON.toJSONString(crowdfundingInfoPayee),
                ParamTypeEnum.CrowdfundingInfoPayee);
        return response.ok() ? response.getData() : defaultValue;
    }

    @Override
    public CrowdfundingInfoPayee getCrowdfundingInfoPayeeByInfoUuid(String infoUuid) {
        Response<String> response = crowdfundingChaiFenFeignClient.getCrowdfundingInfoPayeeByInfoUuid(infoUuid);
        return response.ok() ? AdminListUtil.getModelFromResponse(response, CrowdfundingInfoPayee.class) : null;
    }

    @Override
    public CrowdfundingInfoHospitalPayee getCrowdfundingInfoHospitalPayeeByInfoUuid(String infoUuid) {
        Response<String> response = crowdfundingChaiFenFeignClient.getCrowdfundingInfoHospitalPayeeByInfoUuid(infoUuid);
        return response.ok() ? AdminListUtil.getModelFromResponse(response, CrowdfundingInfoHospitalPayee.class) : null;
    }

    @Override
    public int addCrowdfundingInfoHospitalPayee(CrowdfundingInfoHospitalPayee crowdfundingInfoHospitalPayee) {
        Response<Integer> response = crowdfundingChaiFenFeignClient.addCrowdfundingInfoHospitalPayee(JSON.toJSONString(crowdfundingInfoHospitalPayee), ParamTypeEnum.CrowdfundingInfoHospitalPayee);
        return response.ok() ? response.getData() : defaultValue;
    }

    @Override
    public int updateCrowdfundingInfoHospitalPayee(CrowdfundingInfoHospitalPayee crowdfundingInfoHospitalPayee) {
        Response<Integer> response = crowdfundingChaiFenFeignClient.updateCrowdfundingInfoHospitalPayee(JSON.toJSONString(crowdfundingInfoHospitalPayee), ParamTypeEnum.CrowdfundingInfoHospitalPayee);
        return response.ok() ? response.getData() : defaultValue;
    }

    @Override
    public Response<Void> checkOrOpenWithDrawAcct(DepositPayeeAccountParam depositPayeeAccountParam) {
        com.shuidihuzhu.cf.finance.client.response.FeignResponse<Void> response = cfDepositAccountFeignClient.openPayeeAccount(depositPayeeAccountParam.getInfoUuid(), depositPayeeAccountParam.getPayeeName()
                , depositPayeeAccountParam.getBankCard(), depositPayeeAccountParam.getBankName()
                , depositPayeeAccountParam.getIdCard(), depositPayeeAccountParam.getCnapsBranchId()
                , depositPayeeAccountParam.getBankBranchName(), depositPayeeAccountParam.getUserId()
                , depositPayeeAccountParam.getPlatform(), depositPayeeAccountParam.getClientIp());
        return NewResponseUtil.makeResponse(response.getCode(), response.getMsg(), response.getData());
    }

    @Override
    public CfCharityPayee getCfCharityPayeeByUUid(String uuid) {
        Response<String> response = crowdfundingChaiFenFeignClient.getCfCharityPayeeByUUid(uuid);
        return response.ok() ? AdminListUtil.getModelFromResponse(response, CfCharityPayee.class) : null;
    }

    @Override
    public int subtractAmount(int id, int amount) {
        Response<Integer> response = crowdfundingChaiFenFeignClient.subtractAmount(id, amount);
        return response.ok() ? response.getData() : defaultValue;
    }

    @Override
    public Map<Integer, CfInfoStat> mapByIds(List<Integer> ids) {
        Response<String> response = crowdfundingChaiFenFeignClient.mapByIds(ids);
        if (response.ok()) {
            try {
              return JSON.parseObject(response.getData(), new TypeReference<Map<Integer, CfInfoStat>>() {
                });
            } catch (Exception e) {
                log.error(this.getClass().getSimpleName() + " mapByIds  反序列化失败 response:{}", JSON.toJSONString(response), e);
            }
        }
        return Maps.newHashMap();
    }

    @Override
    public CfInfoStat getById(Integer id) {
        FeignResponse<CfInfoStat> cfInfoStatById = cfUserStatFeignClient.getCfInfoStatById(id);
        return cfInfoStatById.ok() ? cfInfoStatById.getData() : null;
    }

    @Override
    public int updateTimes(String infoUuid, CfTaskEnum.Rule cfTaskTypeEnum) {
        Response<Integer> response = crowdfundingChaiFenFeignClient.updateTimes(infoUuid, cfTaskTypeEnum.getCode());
        return response.ok() ? response.getData() : defaultValue;
    }

    @Override
    public void publishHospitalTask(CrowdfundingInfo crowdfundingInfo) {
        crowdfundingChaiFenFeignClient.publishHospitalTask(JSON.toJSONString(crowdfundingInfo), ParamTypeEnum.CrowdfundingInfo);
    }

    @Override
    public CrowdfundingInfo getCaseInfoById(int caseId) {
        FeignResponse<CrowdfundingInfo> response = FeignResponse.fallback();
        try {
            response = crowdfundingFeignClient.getCaseInfoById(caseId);
        }catch (Exception e){
            log.error(this.getClass().getSimpleName()+" getCaseInfoById err:",e);
        }
        return response.ok()?response.getData():null;
    }

    @Override
    public void sendPayeeInfoRelation(CrowdfundingInfo crowdfundingInfo) {
        Response<Void> voidResponse = crowdfundingChaiFenFeignClient.sendPayeeInfoRelation(JSON.toJSONString(crowdfundingInfo), ParamTypeEnum.CrowdfundingInfo);
        if (voidResponse.notOk()) {
            log.error("ICrowdfundingDelegate sendPayeeInfoRelation is error {} {}", voidResponse.getCode(), voidResponse.getMsg());
        }
    }

}
