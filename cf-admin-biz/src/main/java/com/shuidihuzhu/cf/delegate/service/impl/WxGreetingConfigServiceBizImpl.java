package com.shuidihuzhu.cf.delegate.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.delegate.service.WxGreetingConfigServiceBiz;
import com.shuidihuzhu.cf.vo.wx.greeting.AttachKeyVO;
import com.shuidihuzhu.wx.grpc.client.WxGreetingConfigGrpcClient;
import com.shuidihuzhu.wx.grpc.client.common.AttachKeyItem;
import com.shuidihuzhu.wx.grpc.client.common.Greeting;
import com.shuidihuzhu.wx.grpc.client.common.WxConfig;
import com.shuidihuzhu.wx.grpc.client.feign.WxGreetingConfigServiceClient;
import com.shuidihuzhu.wx.grpc.model.AttacheKeyItemDto;
import com.shuidihuzhu.wx.grpc.model.WxConfigModel;
import com.shuidihuzhu.wx.grpc.util.WxRpcUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/5/4 7:12 PM
 */
@Slf4j
@Service
@RefreshScope
public class WxGreetingConfigServiceBizImpl implements WxGreetingConfigServiceBiz {

    @Resource
    private WxGreetingConfigGrpcClient wxGreetingConfigGrpcClient;
    @Resource
    private WxGreetingConfigServiceClient wxGreetingConfigServiceClient;


    @Override
    public List<WxConfigModel> queryWxMpList() {
        return wxGreetingConfigServiceClient.queryWxMpList();
    }

    @Override
    public Greeting queryById(int id) {
        return wxGreetingConfigGrpcClient.queryById(id);
    }

    @Override
    public List<AttacheKeyItemDto> queryAttachKeys() {
        return wxGreetingConfigServiceClient.queryAttachKeys();
    }

    @Override
    public void delGreeting(int greetingId) {
        wxGreetingConfigServiceClient.delGreeting(greetingId);
    }

    @Override
    public void delAttach(int attachId) {
        wxGreetingConfigServiceClient.delAttach(attachId);
    }


}
