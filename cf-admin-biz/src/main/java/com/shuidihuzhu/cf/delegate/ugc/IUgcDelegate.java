package com.shuidihuzhu.cf.delegate.ugc;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingComment;
import com.shuidihuzhu.cf.model.miniprogram.CfCommentDynamic;
import com.shuidihuzhu.cf.model.miniprogram.CfTopicShareCommentCount;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2019/6/12 5:06 PM
 */
public interface IUgcDelegate {

    int insertCfTopicShareCommentCount(CfTopicShareCommentCount cfTopicShareCommentCount);

    List<CfTopicShareCommentCount> listByTopicIds(List<Integer> topicIds);

    CrowdfundingComment getByIdNoCareDeleted(long i);

    void removeCrowdfundingCommentById(Long id);

    CfCommentDynamic selectCfCommentDynamicByCommentId(long commentId);

    List<CfCommentDynamic> listByIdsAndStatus(List<Integer> topicIds, int topStauts);

    List<CfCommentDynamic> listByCommentIds(List<Long> commentIds);

    List<CrowdfundingComment> getListByIdNoCareDeleted( List<Long> idList);

    List<CrowdfundingComment> getByPageNoCareCommentIdList( List<Long> parentIdList
            , Integer offset
            , Integer limit
            , Integer type);

    Integer countCrowdFundingVerificationByInfoUuid(String crowdFundingInfoId);

    List<CrowdFundingVerification> queryAllCrowdFundingVerificationByInfoUuid(String crowdFundingInfoId);
}
