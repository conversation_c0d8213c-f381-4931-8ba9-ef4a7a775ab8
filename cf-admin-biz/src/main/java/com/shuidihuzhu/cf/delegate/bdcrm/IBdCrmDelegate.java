package com.shuidihuzhu.cf.delegate.bdcrm;


import com.shuidihuzhu.client.cf.growthtool.model.VolunteerInfoUuidModel;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2019/9/23 2:52 PM
 */
public interface IBdCrmDelegate {

    List<VolunteerInfoUuidModel> getVolunteerInfoUuidModelbyUniqueCodeListWithTime(List<String> uniqueCodeList, String startTime, String endTime);

    List<VolunteerInfoUuidModel> getVolunteerInfoUuidModelbyVolunteerTypeWithTime(Integer volunteerType, String startTime, String endTime);
}
