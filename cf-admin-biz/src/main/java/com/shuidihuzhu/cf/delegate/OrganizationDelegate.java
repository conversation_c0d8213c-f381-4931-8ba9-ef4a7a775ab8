package com.shuidihuzhu.cf.delegate;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.delegate.saas.AdminOrganization;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.OrgMembersResult;
import com.shuidihuzhu.cf.delegate.saas.OrganizationClientV1;
import com.shuidihuzhu.cf.delegate.saas.SimpleOrgVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class OrganizationDelegate {

    private static final int MAX_ORGANIZATION = 128;

    @Resource
    private OrganizationClientV1 organizationClientV1;

    /**
     * 获取组织快照
     *
     * @param operatorId
     * @return
     */
    public String getSimpleOrganization(Integer operatorId) {
        String org = "";
        if (operatorId == null || operatorId <= 0) {
            return org;
        }

        org = organizationClientV1.getUserRelationOrgName(operatorId).getResult();
        if (StringUtils.isEmpty(org)) {
            return "";
        }
        if (StringUtils.length(org) <= MAX_ORGANIZATION) {
            return org;
        }

        org = StringUtils.right(org, MAX_ORGANIZATION);
        return org;
    }

    public Map<Integer, String> getSimpleOrganizations(List<Integer> operatorIds) {
        HashMap<Integer, String> result = Maps.newHashMap();
        AuthRpcResponse<Map<Integer, List<AdminOrganization>>> res = organizationClientV1.getUserOrgs(operatorIds);
        if (!res.isSuccess()) {
            return result;
        }
        Map<Integer, List<AdminOrganization>> orgsMap = res.getResult();
        for (Integer userId : orgsMap.keySet()) {
            result.put(userId, orgsMap.get(userId).stream().skip(1).map(AdminOrganization::getName).collect(Collectors.joining("-")));
        }

        return result;
    }

    public List<Integer> getSubOrgs(int orgId) {
        AuthRpcResponse<OrgMembersResult> response = organizationClientV1.getOrgMebmbers(orgId);
        if (response == null || response.getResult() == null || CollectionUtils.isEmpty(response.getResult().getSubOrgs())) {
            return Lists.newArrayList();
        }

        return response.getResult().getSubOrgs().stream().map(SimpleOrgVo::getOrgId).collect(Collectors.toList());
    }

    public List<Long> getAllUsersByOrgId(int orgId) {
        AuthRpcResponse<OrgMembersResult> response = organizationClientV1.getOrgMebmbers(orgId);
        if (response == null || response.getResult() == null) {
            return Lists.newArrayList();
        }

        return getAllUsersByOrgId(response.getResult()).stream().distinct().collect(Collectors.toList());
    }

    private List<Long> getAllUsersByOrgId(OrgMembersResult result) {
        CopyOnWriteArrayList<Long> userIds = new CopyOnWriteArrayList<>();
        if (result == null) {
            return userIds;
        }

        if (CollectionUtils.isNotEmpty(result.getMembers())) {
            result.getMembers().forEach(x -> userIds.add((long) x.getId()));
        }

        if (CollectionUtils.isNotEmpty(result.getSubOrgs())) {
            result.getSubOrgs().parallelStream().forEach(x -> userIds.addAll(getAllUsersByOrgId(x.getOrgId())));
        }

        return userIds;
    }
}
