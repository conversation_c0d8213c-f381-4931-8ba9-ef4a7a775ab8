package com.shuidihuzhu.cf.delegate.volunteer.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.AccountPlatform;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.delegate.volunteer.IVolunteerDelegate;
import com.shuidihuzhu.client.cf.growthtool.client.CfGrowthtoolFeginClient;
import com.shuidihuzhu.client.cf.growthtool.client.CfGrowthtoolVolunteerFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteerVo;
import com.shuidihuzhu.client.cf.growthtool.model.PageReturnModel;
import com.shuidihuzhu.client.cf.growthtool.model.VolunteerSearchModel;
import com.shuidihuzhu.client.grpc.account.v1.UserRegGrpcClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.msg.enums.AccountThirdTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wanghui
 * @create: 2019/9/25 5:24 PM
 */
@Service
@Slf4j
public class VolunteerDelegate implements IVolunteerDelegate {
    @Autowired
    private CfGrowthtoolVolunteerFeignClient cfGrowthtoolVolunteerFeignClient;
    @Autowired
    private CfGrowthtoolFeginClient cfGrowthtoolFeginClient;
    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Autowired
    private UserRegGrpcClient userRegGrpcClient;

    @Override
    public Response<Integer> addVolunteer(CrowdfundingVolunteer crowdfundingVolunteer) {
        return cfGrowthtoolVolunteerFeignClient.addVolunteer(crowdfundingVolunteer);
    }

    @Override
    public PageReturnModel<CrowdfundingVolunteer> getVolunteer(VolunteerSearchModel volunteerSearchModel, boolean needPage, Integer pageNo, Integer pageSize) {
        Response<PageReturnModel<CrowdfundingVolunteer>> response = cfGrowthtoolVolunteerFeignClient.getVolunteer(volunteerSearchModel, needPage, pageNo, pageSize);
        return response.ok()?response.getData():null;
    }

    @Override
    public CrowdfundingVolunteerVo getCrowdfundingVolunteerVoById(Long id) {
        Response<CrowdfundingVolunteerVo> response = cfGrowthtoolVolunteerFeignClient.getCrowdfundingVolunteerVoById(id);
        return response.ok()?response.getData():null;
    }

    @Override
    public Response<Integer> updateVolunteerInfoById(CrowdfundingVolunteer crowdfundingVolunteer) {
        return cfGrowthtoolVolunteerFeignClient.updateVolunteerInfoById(crowdfundingVolunteer);
    }

    @Override
    public Integer updateQrCode(String qrCode, Long id) {
        Response<Integer> response = cfGrowthtoolVolunteerFeignClient.updateQrCode(qrCode, id);
        return response.ok()?response.getData():0;
    }

    @Override
    public Boolean checkIsExistByTypeOrIdentity(Integer volunteerType, String idCardNumber, Long vid) {
        Response<Boolean> response = cfGrowthtoolVolunteerFeignClient.checkIsExistByTypeOrIdentity(volunteerType, idCardNumber, vid);
        return response.ok()?response.getData():false;
    }

    @Override
    public Boolean checkIsExistByTypeOrEmail(Integer volunteerType, String email, Long vid) {
        Response<Boolean> response = cfGrowthtoolVolunteerFeignClient.checkIsExistByTypeOrEmail(volunteerType, email, vid);
        return response.ok()?response.getData():false;
    }

    @Override
    public Integer updateApplyStatusById(Long id, Integer applyStatus, String operatorName, Integer operatorUserId, String angelUrl, String refuseReasons, String qrCode) {
        Response<Integer> response = cfGrowthtoolVolunteerFeignClient.updateApplyStatusById(id, applyStatus, operatorName, operatorUserId, angelUrl, refuseReasons, qrCode);
        return response.ok()?response.getData():0;
    }

    @Override
    public List<String> getVolunteerUniqueByMobileAndWorkStatus(int workStatus, String mobile) {
        Response<List<String>> response = cfGrowthtoolVolunteerFeignClient.getVolunteerUniqueByMobileAndWorkStatus(workStatus, mobile);
        return response.ok()?response.getData(): Lists.newArrayList();
    }

    @Override
    public List<CrowdfundingVolunteer> getCfVolunteerDOByUniqueCodes(List<String> uniqueCodes) {
        Response<List<CrowdfundingVolunteer>> response = cfGrowthtoolVolunteerFeignClient.getCfVolunteerDOByUniqueCodes(uniqueCodes);
        return response.ok()?response.getData():Lists.newArrayList();
    }
    @Override
    public CrowdfundingVolunteer getVolunteerByUniqueCode(String uniqueCode){
        Response<CrowdfundingVolunteer> response = cfGrowthtoolFeginClient.getVolunteerByUniqueCode(uniqueCode);
        return response.ok()?response.getData():null;
    }

    @Override
    public Boolean checkRegister(String mobile) {
        UserInfoModel userInfoByMobile = userInfoServiceBiz.getUserInfoByMobile(mobile);
        if(userInfoByMobile == null){
            userRegGrpcClient.regWithMobile(mobile, AccountThirdTypeEnum.EMPTY.getCode(), AccountPlatform.DEFAULT,null,0L);
            log.info("userRegGrpcClient.regWithMobile mobile:{}",mobile);
            return true;
        }
        return false;
    }
}
