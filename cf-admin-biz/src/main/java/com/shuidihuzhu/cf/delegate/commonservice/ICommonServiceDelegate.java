package com.shuidihuzhu.cf.delegate.commonservice;

import com.shuidihuzhu.cf.domain.visitconfig.VisitConfigLogDO;
import com.shuidihuzhu.cf.enums.crowdfunding.BankCardVerifyStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingType;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.enums.visitconfig.VisitConfigLogActionInfoEnum;
import com.shuidihuzhu.cf.enums.visitconfig.VisitConfigLogActionTypeEnum;
import com.shuidihuzhu.cf.enums.visitconfig.VisitConfigSourceEnum;
import com.shuidihuzhu.cf.model.crowdfunding.BankCardVerifyRecord;
import com.shuidihuzhu.cf.model.crowdfunding.BankCardVerifyResult;
import com.shuidihuzhu.cf.model.crowdfunding.CfPageGlobalConfig;
import com.shuidihuzhu.cf.model.graytest.CfGrayTestSwitch;
import com.shuidihuzhu.cf.model.rule.RuleCollectionDetail;
import com.shuidihuzhu.cf.model.rule.RuleCondition;
import com.shuidihuzhu.cf.model.rule.RuleModel;
import com.shuidihuzhu.cf.response.OpResult;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2019/6/12 10:13 AM
 */
public interface ICommonServiceDelegate {

    BankCardVerifyResult verify(String holderName, UserIdentityType identityType, String idCard, String bankCardNum, long userId);

    BankCardVerifyResult verify(String holderName, UserIdentityType identityType, String idCard, String bankCardNum, int crowdfundingId, long userId, boolean updateStatus);

    BankCardVerifyStatus getStatusByThreeElements(String cryptoHolderName, String cryptoIdCard, String cryptoBankCard);

    BankCardVerifyResult getResultByThreeElements(String cryptoHolderName, String cryptoIdCard, String cryptoBankCard);

    int getGrayTestBySelfTag(String code, long userId, String selfTag);

    int getGrayTestBySelfTagWithCode(String code, long userId, String selfTag);

    int getGrayTestByOpenId(String code, String openId, long userId);

    int getGrayTestByOpenIdWithCode(String code, String openId, long userId);

    boolean delete(String code);

    boolean add(int bizType, String code, String casePercentage, String description, boolean isRecordResult);

    boolean updateCasePercentage(String code, String casePercentage);

    boolean updateIfRecordResult(String code, boolean isRecordResult);

    List<CfGrayTestSwitch> getByPage(int start, int size);

    CfGrayTestSwitch getByCode(String code);

    int total();

    List<CfGrayTestSwitch> getLikeCode(String code);

    String queryByKey(String key, boolean useCache);

    String queryByKeyWithLocalCache(String key);

    String queryValueByKey(String key);

    int saveRedisKv(String key, String value);

    int updateRedisKv(String key, String value);

    int queryIntByKey(String key, boolean useCache);

    OpResult pin(int caseId, VisitConfigSourceEnum sourceEnum, List<VisitConfigLogActionInfoEnum> actionInfoEnumList, int operatorId);

    List<VisitConfigLogDO> listOperatorLog(String infoUuid);

    List<VisitConfigLogDO> listByCondition(String infoUuid, List<VisitConfigLogActionTypeEnum> actionTypeEnums, List<VisitConfigSourceEnum> sourceEnums);

    List<VisitConfigLogDO> list(String infoUuid);

    OpResult<List<RuleModel>> listByAdminUserId(int adminUserId);

    OpResult<List<RuleCondition>> listByActivityId(long activityId);

    OpResult<RuleCollectionDetail> saveDetail(RuleCollectionDetail detail);

    OpResult<RuleCondition> saveCondition(RuleCondition condition);

//    OpResult removeCondition(long id);

    /**
     * 获取规则详情并展示
     * @param activityId
     * @return
     */
    RuleModel getById(long activityId);

    void clearCache(long activityId);

    RuleCondition getRuleConditionById(long id);


    boolean add(List<CfPageGlobalConfig> configs, int globalType);


    List<CfPageGlobalConfig> getListByType(Integer globalType);

    String getContribute(String infoId, String param, int crowdfundingType);
    String getContribute(String infoId, String param, CrowdfundingType crowdfundingType);
    String getMineRaise(String param);
    String compactOssUrl(String url);
    String pageDomain();

    void touchSetCanShareAndDonateWithAdminUser(int caseId,int adminUserId);
    boolean canDonate(int caseId);
}
