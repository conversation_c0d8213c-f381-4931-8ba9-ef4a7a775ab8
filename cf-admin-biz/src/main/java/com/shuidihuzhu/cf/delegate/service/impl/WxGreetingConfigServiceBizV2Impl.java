package com.shuidihuzhu.cf.delegate.service.impl;

import com.shuidihuzhu.cf.delegate.service.WxGreetingConfigServiceBizV2;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.wx.grpc.client.WxGreetingConfigGrpcClientV2;
import com.shuidihuzhu.wx.grpc.client.common.CommonResponse;
import com.shuidihuzhu.wx.grpc.client.common.GreetingMatchMethod;
import com.shuidihuzhu.wx.grpc.client.common.GreetingWxGroupAttachV2;
import com.shuidihuzhu.wx.grpc.client.common.QueryAttachHistoryV2;
import com.shuidihuzhu.wx.grpc.client.feign.WxGreetingConfigServiceClientV2;
import com.shuidihuzhu.wx.grpc.enums.GreetingMatchRuleEnum;
import com.shuidihuzhu.wx.grpc.model.GreetingMatchMethodModel;
import com.shuidihuzhu.wx.grpc.model.GreetingWxGroupAttachV2Dto;
import com.shuidihuzhu.wx.grpc.model.QueryAttachHistoryV2Dto;
import com.shuidihuzhu.wx.grpc.model.WxGreetingConfigPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/5/4 8:01 PM
 */
@Slf4j
@Service
@RefreshScope
public class WxGreetingConfigServiceBizV2Impl implements WxGreetingConfigServiceBizV2 {

    @Resource
    private WxGreetingConfigServiceClientV2 wxGreetingConfigServiceClientV2;


    @Override
    public List<WxGreetingConfigPo> queryGreetingV2(int current, int pageSize, String keyword) {
        return wxGreetingConfigServiceClientV2.queryGreetingV2(current, pageSize, keyword);
    }

    @Override
    public Response<Boolean> addGreetingV2(List<WxGreetingConfigPo> wxGreetingConfigs) {
        return wxGreetingConfigServiceClientV2.addGreetingV2(wxGreetingConfigs);
    }

    @Override
    public Response<Boolean> modifyGreetingV2(List<WxGreetingConfigPo> wxGreetingConfigs) {
        return wxGreetingConfigServiceClientV2.modifyGreetingV2(wxGreetingConfigs);
    }

    @Override
    public List<WxGreetingConfigPo> listGreetingV2(int current, int pageSize) {
        return wxGreetingConfigServiceClientV2.listGreetingV2(current, pageSize);
    }

    @Override
    public Integer totalCountV2() {
        return wxGreetingConfigServiceClientV2.totalCountV2();
    }

    @Override
    public Integer totalCountByCondition(String keyword) {
        return wxGreetingConfigServiceClientV2.totalCountByCondition(keyword);
    }

    @Override
    public Response<Boolean> addAttachV2(int wxGroupId, int contentId, int subBizType, List<GreetingMatchMethod> greetingMatchMethods) {

        List<GreetingMatchMethodModel> greetingMatchMethodModels = greetingMatchMethods.stream().map(greetingMatchMethod -> {
            GreetingMatchMethodModel greetingMatchMethodModel = new GreetingMatchMethodModel();
            greetingMatchMethodModel.setKey(greetingMatchMethod.getKey());
            greetingMatchMethodModel.setMatchRule(GreetingMatchRuleEnum.of(greetingMatchMethod.getMatchRuleValue()));
            greetingMatchMethodModel.setSubBizType(greetingMatchMethod.getSubBizType());
            return greetingMatchMethodModel;
        }).collect(Collectors.toList());
        return wxGreetingConfigServiceClientV2.addAttachV2(wxGroupId, contentId, subBizType, greetingMatchMethodModels);
    }

    @Override
    public List<GreetingWxGroupAttachV2Dto> queryAttachV2(int groupId) {
        return wxGreetingConfigServiceClientV2.queryAttachV2(groupId);
    }

    @Override
    public List<WxGreetingConfigPo> queryByContentId(Integer contentId) {
        return wxGreetingConfigServiceClientV2.queryByContentId(contentId);
    }

    @Override
    public List<QueryAttachHistoryV2Dto> queryAttachHistoryV2(int current, int pageSize, Integer thirdType, String attachScene) {
        return wxGreetingConfigServiceClientV2.queryAttachHistoryV2(current, pageSize, thirdType, attachScene);
    }

    @Override
    public Integer totalAttachV2(Integer thirdType, String attachScene) {
        return wxGreetingConfigServiceClientV2.totalAttachV2(thirdType, attachScene);
    }

    @Override
    public void addAttachKeyItem(String attachKey, String attachScene) {
        wxGreetingConfigServiceClientV2.addAttachKeyItem(attachKey, attachScene);
    }
}
