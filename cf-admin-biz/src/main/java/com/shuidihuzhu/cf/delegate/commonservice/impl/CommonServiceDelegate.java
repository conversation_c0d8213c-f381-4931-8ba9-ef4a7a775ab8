package com.shuidihuzhu.cf.delegate.commonservice.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.admin.util.AdminListUtil;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCaseVisitConfigBiz;
import com.shuidihuzhu.cf.client.feign.CfGrayTestFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.delegate.commonservice.ICommonServiceDelegate;
import com.shuidihuzhu.cf.domain.visitconfig.VisitConfigLogDO;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.BankCardVerifyStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingType;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.enums.visitconfig.VisitConfigLogActionInfoEnum;
import com.shuidihuzhu.cf.enums.visitconfig.VisitConfigLogActionTypeEnum;
import com.shuidihuzhu.cf.enums.visitconfig.VisitConfigSourceEnum;
import com.shuidihuzhu.cf.enums.wx.WxBotSendMsgTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.BankCardVerifyResult;
import com.shuidihuzhu.cf.model.crowdfunding.CfPageGlobalConfig;
import com.shuidihuzhu.cf.model.graytest.CfGrayTestSwitch;
import com.shuidihuzhu.cf.model.rule.RuleCollectionDetail;
import com.shuidihuzhu.cf.model.rule.RuleCondition;
import com.shuidihuzhu.cf.model.rule.RuleModel;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.api.chaifenbeta.commontool.CfRedisKvBizFeignClient;
import com.shuidihuzhu.client.cf.api.chaifenbeta.commontool.CfUrlConfigFeignClient;
import com.shuidihuzhu.client.cf.api.chaifenbeta.commontool.CommonServiceFeignClient;
import com.shuidihuzhu.client.cf.api.chaifenbeta.crowdfunding.BankCardVerifyRecordFeignClient;
import com.shuidihuzhu.client.cf.api.chaifenbeta.visitconfig.VisitConfigLogServiceFeignClient;
import com.shuidihuzhu.client.cf.api.model.enums.ParamTypeEnum;
import com.shuidihuzhu.client.cf.risk.model.enums.RiskOperateSourceEnum;
import com.shuidihuzhu.client.cf.risk.model.enums.UserOperationEnum;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2019/6/12 10:16 AM
 */
@Slf4j
@Service
@RefreshScope
public class CommonServiceDelegate implements ICommonServiceDelegate {
    private static final int defaultValue = 0;
    @Autowired
    CfGrayTestFeignClient cfGrayTestFeignClient;
    @Autowired
    private CfRedisKvBizFeignClient cfRedisKvBizFeignClient;
    @Autowired
    private VisitConfigLogServiceFeignClient visitConfigLogServiceFeignClient;
    @Autowired
    private BankCardVerifyRecordFeignClient bankCardVerifyRecordFeignClient;
    @Autowired
    private CfUrlConfigFeignClient cfUrlConfigFeignClient;
    @Value("${cfbiz.unuse-cf-biz:false}")
    private boolean unuseCfBiz;

    @Autowired
    private CommonServiceFeignClient commonServiceFeignClient;
    @Resource
    private AdminCaseVisitConfigBiz adminCaseVisitConfigBiz;

    @Override
    public BankCardVerifyResult verify(String holderName, UserIdentityType identityType, String idCard, String bankCardNum, long userId) {
        Response<String> response = bankCardVerifyRecordFeignClient.verify(holderName, identityType.getCode(), idCard, bankCardNum, userId);
        return response.ok() ? AdminListUtil.getModelFromResponse(response, BankCardVerifyResult.class) : null;
    }

    @Override
    public BankCardVerifyResult verify(String holderName, UserIdentityType identityType, String idCard, String bankCardNum, int crowdfundingId, long userId, boolean updateStatus) {
        Response<String> response = bankCardVerifyRecordFeignClient.verify(holderName, identityType.getCode(), idCard, bankCardNum, crowdfundingId, userId, updateStatus);
        return response.ok() ? JSON.parseObject(response.getData(), BankCardVerifyResult.class) : null;//已检查过
    }

    @Override
    public BankCardVerifyStatus getStatusByThreeElements(String cryptoHolderName, String cryptoIdCard, String cryptoBankCard) {
        Response<String> response = bankCardVerifyRecordFeignClient.getStatusByThreeElements(cryptoHolderName, cryptoIdCard, cryptoBankCard);
        return response.ok() ? AdminListUtil.getModelFromResponse(response, BankCardVerifyStatus.class) : null;
    }

    @Override
    public BankCardVerifyResult getResultByThreeElements(String cryptoHolderName, String cryptoIdCard, String cryptoBankCard) {
        Response<String> response = bankCardVerifyRecordFeignClient.getResultByThreeElements(cryptoHolderName, cryptoIdCard, cryptoBankCard);
        return response.ok() ? AdminListUtil.getModelFromResponse(response, BankCardVerifyResult.class) : null;
    }


    @Override
    public int getGrayTestBySelfTag(String code, long userId, String selfTag) {
        FeignResponse<Integer> feignResponse = FeignResponse.fallback();
        try {
            feignResponse = cfGrayTestFeignClient.getGrayTestBySelfTag(code, userId, selfTag);
            log.info(this.getClass().getSimpleName() + " getGrayTestBySelfTag request:{} response:{}",
                    String.join("&", String.valueOf(code), String.valueOf(userId), selfTag), feignResponse);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + " getGrayTestBySelfTag err:", e);
        }
        return feignResponse.ok() ? feignResponse.getData() : defaultValue;
    }

    @Override
    public int getGrayTestBySelfTagWithCode(String code, long userId, String selfTag) {
        FeignResponse<Integer> feignResponse = FeignResponse.fallback();
        try {
            feignResponse = cfGrayTestFeignClient.getGrayTestBySelfTagWithCode(code, userId, selfTag);
            log.info(this.getClass().getSimpleName() + " getGrayTestBySelfTagWithCode request:{} response:{}",
                    String.join("&", String.valueOf(code), String.valueOf(userId), selfTag), feignResponse);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + " getGrayTestBySelfTagWithCode err:", e);
        }
        return feignResponse.ok() ? feignResponse.getData() : defaultValue;
    }

    @Override
    public int getGrayTestByOpenId(String code, String openId, long userId) {
        FeignResponse<Integer> feignResponse = FeignResponse.fallback();
        try {
            feignResponse = cfGrayTestFeignClient.getGrayTestByOpenId(code, openId, userId);
            log.info(this.getClass().getSimpleName() + " getGrayTestByOpenId request:{} response:{}",
                    String.join("&", String.valueOf(code), openId, String.valueOf(userId)), feignResponse);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + " getGrayTestByOpenId err:", e);
        }
        return feignResponse.ok() ? feignResponse.getData() : defaultValue;
    }

    @Override
    public int getGrayTestByOpenIdWithCode(String code, String openId, long userId) {
        FeignResponse<Integer> feignResponse = FeignResponse.fallback();
        try {
            feignResponse = cfGrayTestFeignClient.getGrayTestByOpenIdWithCode(code, openId, userId);
            log.info(this.getClass().getSimpleName() + " getGrayTestByOpenIdWithCode request:{} response:{}",
                    String.join("&", String.valueOf(code), openId, String.valueOf(userId)), feignResponse);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + " getGrayTestByOpenIdWithCode err:", e);
        }
        return feignResponse.ok() ? feignResponse.getData() : defaultValue;
    }

    @Override
    public boolean delete(String code) {
        FeignResponse<Integer> feignResponse = FeignResponse.fallback();
        try {
            feignResponse = cfGrayTestFeignClient.delete(code);
            log.info(this.getClass().getSimpleName() + " delete request:{} response:{}",
                    code, feignResponse);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + " delete err:", e);
        }
        return feignResponse.ok() ? (feignResponse.getData() > 0 ? true : false) : false;
    }

    @Override
    public boolean add(int bizType, String code, String casePercentage, String description, boolean isRecordResult) {
        FeignResponse<Integer> feignResponse = FeignResponse.fallback();
        try {
            feignResponse = cfGrayTestFeignClient.add(bizType, code, casePercentage, description, isRecordResult);
            log.info(this.getClass().getSimpleName() + " add request:{} response:{}",
                    String.join("&", String.valueOf(bizType), code, casePercentage, description, String.valueOf(isRecordResult)), feignResponse);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + " add err:", e);
        }
        return feignResponse.ok() ? (feignResponse.getData() > 0 ? true : false) : false;
    }

    @Override
    public boolean updateCasePercentage(String code, String casePercentage) {
        FeignResponse<Integer> feignResponse = FeignResponse.fallback();
        try {
            feignResponse = cfGrayTestFeignClient.updateCasePercentage(code, casePercentage);
            log.info(this.getClass().getSimpleName() + " updateCasePercentage request:{} response:{}",
                    String.join("&", code, casePercentage), feignResponse);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + " updateCasePercentage err:", e);
        }
        return feignResponse.ok() ? (feignResponse.getData() > 0 ? true : false) : false;
    }

    @Override
    public boolean updateIfRecordResult(String code, boolean isRecordResult) {
        FeignResponse<Integer> feignResponse = FeignResponse.fallback();
        try {
            feignResponse = cfGrayTestFeignClient.updateIfRecordResult(code, isRecordResult);
            log.info(this.getClass().getSimpleName() + " updateIfRecordResult request:{} response:{}",
                    String.join("&", code, String.valueOf(isRecordResult)), feignResponse);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + " updateIfRecordResult err:", e);
        }
        return feignResponse.ok() ? (feignResponse.getData() > 0 ? true : false) : false;
    }

    @Override
    public List<CfGrayTestSwitch> getByPage(int start, int size) {
        FeignResponse<List<CfGrayTestSwitch>> feignResponse = FeignResponse.fallback();
        try {
            feignResponse = cfGrayTestFeignClient.getByPage(start, size);
            log.info(this.getClass().getSimpleName() + " getByPage request:{} response:{}",
                    String.join("&", String.valueOf(start), String.valueOf(size)), feignResponse);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + " getByPage err:", e);
        }
        return feignResponse.ok() ? feignResponse.getData() : Lists.newArrayList();
    }

    @Override
    public CfGrayTestSwitch getByCode(String code) {
        FeignResponse<CfGrayTestSwitch> feignResponse = FeignResponse.fallback();
        try {
            feignResponse = cfGrayTestFeignClient.getByCode(code);
            log.info(this.getClass().getSimpleName() + " getByCode request:{} response:{}", code, feignResponse);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + " getByCode err:", e);
        }
        return feignResponse.ok() ? feignResponse.getData() : null;
    }

    @Override
    public int total() {
        FeignResponse<Integer> feignResponse = FeignResponse.fallback();
        try {
            feignResponse = cfGrayTestFeignClient.total();
            log.info(this.getClass().getSimpleName() + " total request:{} response:{}", "", feignResponse);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + " total err:", e);
        }
        return feignResponse.ok() ? feignResponse.getData() : defaultValue;
    }

    @Override
    public List<CfGrayTestSwitch> getLikeCode(String code) {
        FeignResponse<List<CfGrayTestSwitch>> feignResponse = FeignResponse.fallback();
        try {
            feignResponse = cfGrayTestFeignClient.getLikeCode(code);
            log.info(this.getClass().getSimpleName() + " getLikeCode request:{} response:{}", code, feignResponse);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + " getLikeCode err:", e);
        }
        return feignResponse.ok() ? feignResponse.getData() : Lists.newArrayList();
    }

    @Override
    public String queryByKey(String key, boolean useCache) {
        Response<String> feignResponse = cfRedisKvBizFeignClient.queryByKey(key, useCache);
        return feignResponse.ok() ? feignResponse.getData() : "";
    }

    @Override
    public String queryByKeyWithLocalCache(String key) {
        Response<String> feignResponse = cfRedisKvBizFeignClient.queryByKeyWithLocalCache(key);
        return feignResponse.ok() ? feignResponse.getData() : "";
    }

    @Override
    public String queryValueByKey(String key) {
        Response<String> feignResponse = cfRedisKvBizFeignClient.queryValueByKey(key);
        return feignResponse.ok() ? feignResponse.getData() : "";
    }

    @Override
    public int saveRedisKv(String key, String value) {
        Response<Boolean> feignResponse = cfRedisKvBizFeignClient.saveRedisKv(key, value);
        return feignResponse.ok() ? (feignResponse.getData() ? 1 : defaultValue) : defaultValue;
    }

    @Override
    public int updateRedisKv(String key, String value) {
        Response<Boolean> feignResponse = cfRedisKvBizFeignClient.updateRedisKv(key, value);
        return feignResponse.ok() ? (feignResponse.getData() ? 1 : defaultValue) : defaultValue;
    }

    @Override
    public int queryIntByKey(String key, boolean useCache) {
        Response<Integer> feignResponse = cfRedisKvBizFeignClient.queryIntByKey(key, useCache);
        return feignResponse.ok() ? feignResponse.getData() : defaultValue;
    }

    @Override
    public OpResult pin(int caseId, VisitConfigSourceEnum sourceEnum, List<VisitConfigLogActionInfoEnum> actionInfoEnumList, int operatorId) {
        List<String> actionInfoEnumValueList = actionInfoEnumList.stream()
                .map(VisitConfigLogActionInfoEnum::getValue).collect(Collectors.toList());
        Response<String> response = visitConfigLogServiceFeignClient.pin(caseId, sourceEnum.getValue(), actionInfoEnumValueList, operatorId);
        return response.ok()? OpResult.createSucResult(null):OpResult.createFailResult(CfErrorCode.getByCode(response.getCode()));
    }

    @Override
    public List<VisitConfigLogDO> listOperatorLog(String infoUuid) {
        Response<List<String>> feignResponse = visitConfigLogServiceFeignClient.listOperatorLog(infoUuid);
        List<VisitConfigLogDO> visitConfigLogDOS = Lists.newArrayList();
        if (feignResponse.ok()) {
            visitConfigLogDOS = AdminListUtil.getModelListFromResponse(feignResponse, VisitConfigLogDO.class);
        }
        return visitConfigLogDOS;
    }

    @Override
    public List<VisitConfigLogDO> listByCondition(String infoUuid, List<VisitConfigLogActionTypeEnum> actionTypeEnums, List<VisitConfigSourceEnum> sourceEnums) {
        List<Integer> actionTypeEnumValues = actionTypeEnums.stream().map(VisitConfigLogActionTypeEnum::getValue).collect(Collectors.toList());
        List<Integer> sourceEnumValues = sourceEnums.stream().map(VisitConfigSourceEnum::getValue).collect(Collectors.toList());
        Response<List<String>> feignResponse = visitConfigLogServiceFeignClient.listByCondition(infoUuid, actionTypeEnumValues, sourceEnumValues);
        List<VisitConfigLogDO> visitConfigLogDOS = Lists.newArrayList();
        if (feignResponse.ok()) {
            visitConfigLogDOS = AdminListUtil.getModelListFromResponse(feignResponse, VisitConfigLogDO.class);
        }
        return visitConfigLogDOS;
    }

    @Override
    public List<VisitConfigLogDO> list(String infoUuid) {
        Response<List<String>> feignResponse = visitConfigLogServiceFeignClient.list(infoUuid);
        List<VisitConfigLogDO> visitConfigLogDOS = Lists.newArrayList();
        if (feignResponse.ok()) {
            visitConfigLogDOS = AdminListUtil.getModelListFromResponse(feignResponse, VisitConfigLogDO.class);
        }
        return visitConfigLogDOS;
    }


    @Override
    public OpResult<List<RuleModel>> listByAdminUserId(int adminUserId) {
        Response<List<String>> response = commonServiceFeignClient.listByAdminUserId(adminUserId);
        if (response.ok()){
            return OpResult.createSucResult(AdminListUtil.getModelListFromResponse(response,RuleModel.class));
        }else {
            return OpResult.createFailResult(CfErrorCode.SYSTEM_STRING_TO_RESPONSE_ERROR);
        }
    }

    @Override
    public OpResult<List<RuleCondition>> listByActivityId(long activityId) {
        Response<List<String>> response = commonServiceFeignClient.listByActivityId(activityId);
        if (response.ok()){
            return OpResult.createSucResult(AdminListUtil.getModelListFromResponse(response,RuleCondition.class));
        }else {
            return OpResult.createFailResult(CfErrorCode.SYSTEM_STRING_TO_RESPONSE_ERROR);
        }

    }

    @Override
    public OpResult<RuleCollectionDetail> saveDetail(RuleCollectionDetail detail) {
        Response<String> response = commonServiceFeignClient.saveDetail(JSON.toJSONString(detail), ParamTypeEnum.RuleCollectionDetail);
        if (response.ok()){
            return OpResult.createSucResult(AdminListUtil.getModelFromResponse(response, RuleCollectionDetail.class));
        }else {
            return OpResult.createFailResult(CfErrorCode.SYSTEM_STRING_TO_RESPONSE_ERROR);
        }
    }

    @Override
    public OpResult<RuleCondition> saveCondition(RuleCondition condition) {
        Response<String> response = commonServiceFeignClient.saveCondition(JSON.toJSONString(condition), ParamTypeEnum.RuleCondition);
        if (response.ok()){
            return OpResult.createSucResult(AdminListUtil.getModelFromResponse(response,RuleCondition.class));
        }else {
            return OpResult.createFailResult(CfErrorCode.SYSTEM_STRING_TO_RESPONSE_ERROR);
        }
    }

//    @Override
//    public OpResult removeCondition(long id) {
//        Response<String> response = commonServiceFeignClient.removeCondition(id);
//        return response.ok() ? AdminListUtil.getModelFromResponse(response, OpResult.class) : null;
//    }

    @Override
    public RuleModel getById(long activityId) {
        Response<String> response = commonServiceFeignClient.getById(activityId);
        return response.ok()? AdminListUtil.getModelFromResponse(response,RuleModel.class):null;
    }

    @Override
    public void clearCache(long activityId) {
        commonServiceFeignClient.clearCache(activityId);
    }


    @Override
    public RuleCondition getRuleConditionById(long id) {
        Response<String> response = commonServiceFeignClient.getRuleConditionById(id);
        return response.ok()? AdminListUtil.getModelFromResponse(response,RuleCondition.class):null;
    }


    @Override
    public boolean add(List<CfPageGlobalConfig> configs, int globalType) {
        Response<Boolean> response = commonServiceFeignClient.addCfPageGlobalConfig(AdminListUtil.getListStringFromListModel(configs),
                ParamTypeEnum.CfPageGlobalConfig, globalType);
        return response.ok()? response.getData():false;
    }

    @Override
    public List<CfPageGlobalConfig> getListByType(Integer globalType) {
        Response<List<String>> response = commonServiceFeignClient.getListByType(globalType);
        return response.ok()? AdminListUtil.getModelListFromResponse(response,CfPageGlobalConfig.class):Lists.newArrayList();
    }

    @Override
    public String getContribute(String infoId, String param, int crowdfundingType) {
        Response<String> response = cfUrlConfigFeignClient.getContribute(infoId, param, crowdfundingType);
        return response.getData();
    }

    @Override
    public String getContribute(String infoId, String param, CrowdfundingType crowdfundingType) {
        Response<String> response = cfUrlConfigFeignClient.getContribute(infoId, param, crowdfundingType.value());
        return response.getData();
    }

    @Override
    public String getMineRaise(String param) {
        Response<String> response = cfUrlConfigFeignClient.getMineRaise(param);
        return response.getData();
    }

    @Override
    public String compactOssUrl(String url) {
        Response<String> response = cfUrlConfigFeignClient.compactOssUrl(url);
        return response.getData();
    }

    @Override
    public String pageDomain() {
        Response<String> response = cfUrlConfigFeignClient.pageDomain();
        if (response.ok()) {
            return response.getData();
        } else {
            return null;
        }
    }

    @Override
    public void touchSetCanShareAndDonateWithAdminUser(int caseId, int adminUserId) {
        Response response = commonServiceFeignClient.touchSetCanShareAndDonateWithAdminUser(caseId, adminUserId);
        log.info("touchSetCanShareAndDonateWithAdminUser caseId {}, adminUserId {}, response {}",
                caseId, adminUserId, response);
        if (response == null || response.notOk()) {
            log.error("touchSetCanShareAndDonateWithAdminUser error caseId {}, adminUserId {}, response {}",
                    caseId, adminUserId, response);

            Response<Void> voidResponseOrder = adminCaseVisitConfigBiz.judeRiskCase(caseId, adminUserId, true, 0, UserOperationEnum.ORDER, RiskOperateSourceEnum.FIRST_APPROVE, null);
            Response<Void> voidResponseShare = adminCaseVisitConfigBiz.judeRiskCase(caseId, adminUserId, true, 0, UserOperationEnum.SHARE, RiskOperateSourceEnum.FIRST_APPROVE, null);
            if (voidResponseOrder.notOk() || voidResponseShare.notOk()) {
                String content = "【初审审核通过】解禁可捐可转异常，请关注！。\n案例Id:" + caseId + "\n" + "操作人Id:" + adminUserId
                        + "\n" + "解禁捐款异常信息：" + voidResponseOrder + "\n解禁转发异常信息：" + voidResponseShare;
                AlarmBotService.sentText(WxBotSendMsgTypeEnum.INITIAL_SMART_AUDIT_SUCCESS.getBotKey(), content, null, null);
            }
        }
    }

    @Override
    public boolean canDonate(int caseId) {
        Response<Boolean> response = commonServiceFeignClient.canDonate(caseId);
        return response.ok()? response.getData():false;
    }
}
