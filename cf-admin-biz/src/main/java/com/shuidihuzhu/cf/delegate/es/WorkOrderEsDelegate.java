package com.shuidihuzhu.cf.delegate.es;

import com.shuidihuzhu.client.cf.search.client.CfSearchClient;
import com.shuidihuzhu.client.cf.search.model.CfWorkOrderIndexSearchResult;
import com.shuidihuzhu.client.cf.search.model.SearchRpcResult;
import com.shuidihuzhu.client.cf.search.param.CfWorkOrderV2IndexSearchParam;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class WorkOrderEsDelegate {

    @Resource
    private CfSearchClient cfSearchClient;

    public Response<CfWorkOrderIndexSearchResult> getWorkOrderIdList(CfWorkOrderV2IndexSearchParam param) {
        SearchRpcResult<CfWorkOrderIndexSearchResult> result = cfSearchClient.cfWorkOrderV2IndexSearch(param);
        if (result == null || result.getCode() != 0) {
            return NewResponseUtil.makeFail("WorkOrderEsDelegate es 查询失败");
        }
        return NewResponseUtil.makeSuccess(result.getData());
    }
}
