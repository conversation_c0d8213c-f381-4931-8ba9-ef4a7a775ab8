package com.shuidihuzhu.cf.delegate.ugc.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.admin.util.AdminListUtil;
import com.shuidihuzhu.cf.delegate.ugc.IUgcDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingComment;
import com.shuidihuzhu.cf.model.miniprogram.CfCommentDynamic;
import com.shuidihuzhu.cf.model.miniprogram.CfTopicShareCommentCount;
import com.shuidihuzhu.client.cf.api.chaifenbeta.ugc.CfUgcServiceFeignClient;
import com.shuidihuzhu.client.cf.api.model.enums.ParamTypeEnum;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2019/6/12 5:09 PM
 */
@Service
@Slf4j
@RefreshScope
public class UgcDelegate implements IUgcDelegate {
    @Autowired
    private CfUgcServiceFeignClient cfUgcServiceFeignClient;


    @Override
    public int insertCfTopicShareCommentCount(CfTopicShareCommentCount cfTopicShareCommentCount) {
        Response<Integer> response = cfUgcServiceFeignClient.insertCfTopicShareCommentCount(JSON.toJSONString(cfTopicShareCommentCount), ParamTypeEnum.CfTopicShareCommentCount);
        return response.ok() ? response.getData() : 0;
        //return cfTopicShareCommentBiz.insertOne(cfTopicShareCommentCount);
    }

    @Override
    public List<CfTopicShareCommentCount> listByTopicIds(List<Integer> topicIds) {
        Response<List<String>> response = cfUgcServiceFeignClient.listByTopicIds(topicIds);
        if(response.ok()){
            return AdminListUtil.getModelListFromResponse(response,CfTopicShareCommentCount.class);
        }
        log.error("listByTopicIds fegin接口返回失败:{}", JSON.toJSONString(response));
        return Lists.newArrayList();
    }

    @Override
    public CrowdfundingComment getByIdNoCareDeleted(long i) {
        Response response = cfUgcServiceFeignClient.getByIdNoCareDeletedV2(i);
        if(response.ok()){
            return AdminListUtil.getModelFromResponse(response,CrowdfundingComment.class);
        }
        log.error("getByIdNoCareDeleted fegin接口返回失败:{}", JSON.toJSONString(response));
        return null;
  }

    @Override
    public void removeCrowdfundingCommentById(Long id) {
        cfUgcServiceFeignClient.removeCrowdfundingCommentByIdV2(id);
        //crowdfundingCommentBiz.removeById(id);
    }

    @Override
    public CfCommentDynamic selectCfCommentDynamicByCommentId(long commentId) {
        Response response = cfUgcServiceFeignClient.selectCfCommentDynamicByCommentId(commentId);
        if(response.ok()){
            return AdminListUtil.getModelFromResponse(response,CfCommentDynamic.class);
        }
        log.error("selectCfCommentDynamicByCommentId fegin接口返回失败:{}", JSON.toJSONString(response));
        return null;
  }

    @Override
    public List<CfCommentDynamic> listByIdsAndStatus(List<Integer> topicIds, int topStauts) {
        //return cfCommentDynamicBiz.listByIdsAndStatus(topicIds, topStauts);
        Response response = cfUgcServiceFeignClient.listByIdsAndStatus(topicIds,topStauts);
        if(response.ok()){
            return AdminListUtil.getModelListFromResponse(response,CfCommentDynamic.class);
        }
        log.error("listByIdsAndStatus fegin接口返回失败:{}", JSON.toJSONString(response));
        return Lists.newArrayList();
    }

    @Override
    public List<CfCommentDynamic> listByCommentIds(List<Long> commentIds) {
        Response response = cfUgcServiceFeignClient.listByCommentIds(commentIds);
        if(response.ok()){
            return AdminListUtil.getModelListFromResponse(response,CfCommentDynamic.class);
        }
        log.error("listByCommentIds fegin接口返回失败:{}", JSON.toJSONString(response));
        return Lists.newArrayList();
    }

    @Override
    public List<CrowdfundingComment> getListByIdNoCareDeleted(List<Long> idList) {
        Response response = cfUgcServiceFeignClient.getListByIdNoCareDeletedV2(idList);
        if(response.ok()){
            return AdminListUtil.getModelListFromResponse(response,CrowdfundingComment.class);
        }
        log.error("getListByIdNoCareDeleted fegin接口返回失败:{}", JSON.toJSONString(response));
        return Lists.newArrayList();
    }

    @Override
    public List<CrowdfundingComment> getByPageNoCareCommentIdList(List<Long> parentIdList, Integer offset, Integer limit, Integer type) {
        Response response = cfUgcServiceFeignClient.getByPageNoCareCommentIdList(parentIdList,offset,limit,type);
        if(response.ok()){
            return AdminListUtil.getModelListFromResponse(response,CrowdfundingComment.class);
        }
        log.error("getByPageNoCareCommentIdList fegin接口返回失败:{}", JSON.toJSONString(response));
        return Lists.newArrayList();
    }
    @Override
    public Integer countCrowdFundingVerificationByInfoUuid(String crowdFundingInfoId){
        Response<Integer> response = cfUgcServiceFeignClient.countCrowdFundingVerificationByInfoUuid(crowdFundingInfoId);
        return response.ok()?response.getData():0;
    }
    @Override
    public List<CrowdFundingVerification> queryAllCrowdFundingVerificationByInfoUuid(String crowdFundingInfoId){
        Response<List<String>> response = cfUgcServiceFeignClient.queryAllCrowdFundingVerificationByInfoUuid(crowdFundingInfoId);
        return response.ok()?AdminListUtil.getModelListFromResponse(response,CrowdFundingVerification.class):Lists.newArrayList();
    }
}
