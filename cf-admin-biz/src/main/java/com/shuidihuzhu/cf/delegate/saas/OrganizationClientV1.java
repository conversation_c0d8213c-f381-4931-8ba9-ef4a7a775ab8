package com.shuidihuzhu.cf.delegate.saas;


import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.client.auth.saas.feign.GroupFeignClient;
import com.shuidihuzhu.client.auth.saas.feign.UserGroupFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthGroupDto;
import com.shuidihuzhu.client.auth.saas.model.dto.GroupMembersResultDto;
import com.shuidihuzhu.client.auth.saas.model.dto.SimpleGroupVo;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OrganizationClientV1 {

    @Autowired
    private UserGroupFeignClient userGroupFeignClient;
    @Autowired
    private GroupFeignClient groupFeignClient;

    public AuthRpcResponse<Map<Integer, List<AdminOrganization>>> getUserOrgs(List<Integer> userIds) {
        Response<Map<Long, List<AuthGroupDto>>> result =  userGroupFeignClient.getUsersOrgs(userIds.stream()
                .map(Long::valueOf).collect(Collectors.toList()));

        if (result == null || MapUtils.isEmpty(result.getData())) {
            return AuthRpcResponse.makeFailed(AdminErrorCode.SYSTEM_NOT_EXIST_RECORD_ERROR);
        }

        Map<Integer, List<AdminOrganization>> orgResult = Maps.newHashMap();

        for (Map.Entry<Long, List<AuthGroupDto>> entry : result.getData().entrySet()) {
            orgResult.put(Math.toIntExact(entry.getKey()), AdminOrganization.convertFromAuthGroupDto(entry.getValue()));
        }

        return AuthRpcResponse.makeSuccess(orgResult);
    }

    public AuthRpcResponse<String> getUserRelationOrgName(long userId) {
        Response<String> result = userGroupFeignClient.getGroupNameByUserId(userId);
        return AuthRpcResponse.makeSuccess(result.getCode(), result.getMsg(), result.getData());
    }

    public AuthRpcResponse<AdminOrganization> getUserOrgInfo(long userId) {
        Response<AuthGroupDto> response = userGroupFeignClient.selectByUserId(userId);

        return AuthRpcResponse.makeSuccess(response.getCode(), response.getMsg(), AdminOrganization.convertFromAuthGroupDto(response.getData()));

    }

    public AuthRpcResponse<OrgMembersResult> getOrgMebmbers(int orgId) {
        Response<AuthGroupDto> res = groupFeignClient.selectByGroupBizId(Long.valueOf(orgId));
        if (res == null || res.getData() == null) {
            return AuthRpcResponse.makeFailed(AdminErrorCode.SYSTEM_NOT_EXIST_RECORD_ERROR);
        }

        Response<GroupMembersResultDto> result = userGroupFeignClient.getGroupMebmbers(res.getData().getId());

        return AuthRpcResponse.makeSuccess(result.getCode(), result.getMsg(), convertFromGroupMembers(result.getData()));
    }


    private OrgMembersResult convertFromGroupMembers(GroupMembersResultDto groupDto) {

        if (groupDto == null) {
            return null;
        }

        OrgMembersResult membersResult = new OrgMembersResult();
        membersResult.setOrgId(Math.toIntExact(groupDto.getGroupBizId()));
        membersResult.setOrgName(groupDto.getGroupName());
        membersResult.setMemberCount(groupDto.getMemberCount());
        membersResult.setMembers(groupDto.getMembers());

        List<SimpleOrgVo> subOrgs = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(groupDto.getSubGroups())) {
            for (SimpleGroupVo groupVo : groupDto.getSubGroups()) {

                SimpleOrgVo orgVo = new SimpleOrgVo();
                orgVo.setOrgId(Math.toIntExact(groupVo.getGroupBizId()));
                orgVo.setOrgName(groupVo.getGroupName());
                subOrgs.add(orgVo);
            }
        }

        membersResult.setSubOrgs(subOrgs);

        return membersResult;
    }


}

