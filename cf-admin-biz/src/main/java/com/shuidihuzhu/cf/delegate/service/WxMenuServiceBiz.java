package com.shuidihuzhu.cf.delegate.service;

import com.shuidihuzhu.wx.grpc.model.*;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/5/4 2:39 PM
 */
public interface WxMenuServiceBiz {

    List<WxGroupMenuConfigVO> getWxMenuConfigList(List<Integer> groupIds);

    List<WxMenuModel> getMenuList(Integer current, Integer pageSize);

    MenuDetailResponseDto getMenuDetail(String menuId);

    WxGroupMenuConfigModel getWxGroupMenuConfig(int groupId);

    List<WxMenuModel> getMenuListByName(Integer current, Integer pageSize, String menuName);

    Integer menuCount();

    CommonAddResponseModel createWxMenu(int groupId, String menuId, List<PersonalizeMenuConfigModel> menuList);

    WxMenuResponseModel addMenu(String menuName, String menuConfig);

    WxMenuResponseModel editMenu(String menuId, String menuName, String menuConfig);

}
