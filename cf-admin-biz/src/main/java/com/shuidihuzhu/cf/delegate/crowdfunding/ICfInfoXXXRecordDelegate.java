package com.shuidihuzhu.cf.delegate.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfInfoMirrorRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecordCountModel;

import java.util.List;
import java.util.Set;

/**
 * @author: wanghui
 * @create: 2019/7/5 4:24 PM
 */
public interface ICfInfoXXXRecordDelegate {
    List<CfInfoShareRecordCountModel> getShareCount(List<Integer> infoIds);

    int getCountByInfoId(int infoId);

    List<CfInfoShareRecord> findCfInfoShareRecord(Long userId, String startTime, String endTime);


    List<CfInfoShareRecord> getByInfoId(int caseId,  long startTime, long endTime,  int offset,  int limit);

    List<CfInfoShareRecord> getListByInfoIdAndUserIds(int infoId, List<Long> userIds);

}
