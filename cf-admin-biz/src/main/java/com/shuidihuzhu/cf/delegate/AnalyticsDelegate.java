package com.shuidihuzhu.cf.delegate;

import com.google.common.collect.Maps;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AnalyticsDelegate {

    @Autowired
    private Analytics analytics;

    private static final String BIZ = "cf";

    /**
     *
     * @param event event名字 找产品要
     * @param id
     * @param isLoginId id 是否是userId
     * @param keyValues     keyValues the key value pairs to add
     */
    public void track(String event, Object id, boolean isLoginId, Object... keyValues) {
        Map<String, Object> ext = parse(keyValues);
        track(event, id, isLoginId, ext);
    }

    public void track(String event, Object id, boolean isLoginId, Map<String, Object> ext){
//        try {
//            analytics.track(String.valueOf(id), isLoginId, BIZ, event, ext);
//        } catch (Exception e) {
//            log.error("Analytics 统计异常 event:{},err:", event, e);
//        }
    }

    private static Map<String, Object> parse(Object[] keyValues) {
        if (keyValues.length == 0) {
            return Maps.newHashMap();
        }
        if (keyValues.length % 2 == 1) {
            throw new IllegalArgumentException("size must be even, it is a set of key=value pairs");
        }
        HashMap<String, Object> map = Maps.newHashMapWithExpectedSize(keyValues.length / 2);
        for (int i = 0; i < keyValues.length; i += 2) {
            map.put(String.valueOf(keyValues[i]), keyValues[i + 1]);
        }
        return map;
    }

}
