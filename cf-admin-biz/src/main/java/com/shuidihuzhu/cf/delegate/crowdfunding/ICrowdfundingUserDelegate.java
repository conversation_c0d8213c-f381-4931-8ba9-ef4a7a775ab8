package com.shuidihuzhu.cf.delegate.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfCreditSupplement;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingTreatment;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingUser;
import com.shuidihuzhu.cf.model.crowdfunding.material.credit.CreditSupplementModel;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.vo.CrowdfundingUserVo;
import com.shuidihuzhu.cf.vo.approve.TreatmentVO;

import java.util.List;
import java.util.Map;

/**
 * @author: wanghui
 * @create: 2019/6/6 5:21 PM
 */
public interface ICrowdfundingUserDelegate {
    CrowdfundingUserVo getUserInfo(List<CrowdfundingUser> crowdfundingUserList);
    List<CfCreditSupplement> selectByInfoUuid(String infoUuid);
    CrowdfundingAuthor getCrowdfundingAuthor(Integer crowdfundingId);

    Map<Integer, CrowdfundingAuthor> getByInfoIdList(List<Integer> infoIdList);

    CrowdfundingTreatment getCrowdfundingTreatment(Integer crowdfundingId);

    TreatmentVO getCrowdfundingTreatmentVO(int crowdfundingId);

    int updateCrowdfundingTreatment(CrowdfundingTreatment crowdfundingTreatment);

    Map<Integer, CrowdfundingTreatment> getCrowdfundingTreatmentMapByInfoIdList(List<Integer> infoIdList);

    OpResult<CreditSupplementModel> getCreditSupplementByCaseId(int caseId);

}
