package com.shuidihuzhu.cf.admin.cache;

import com.shuidihuzhu.cf.admin.constant.AsyncPoolConstants;
import com.shuidihuzhu.cf.enhancer.subject.cache.AbstractCache;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class SimpleAbstractCache<K, V> extends AbstractCache<K, V> {

    @Resource(name = AsyncPoolConstants.LOCAL_CACHE_ASYNC_POOL)
    private Executor defaultExecutor;

    @Override
    protected Executor initExecutor() {
        return defaultExecutor;
    }

    @Override
    protected String getName() {
        return this.getClass().getSimpleName();
    }

    @Override
    protected V getValue(final K key) {
        try {
            return super.getValue(key);
        } catch (ExecutionException e) {
            log.error("SimpleAbstractCache getValue error", e);
        }
        return null;
    }
}
