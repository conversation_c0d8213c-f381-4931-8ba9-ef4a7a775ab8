package com.shuidihuzhu.cf.admin.util;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2018-12-03  16:22
 */
public class StringCountUtils {

    public static int countHanZi(String content){
        int count = 0;
        if (!StringUtils.isBlank(content)){
            char[] chars = content.toCharArray();
            for (char aChar : chars) {
                if (Character.toString(aChar).matches("[\\u4E00-\\u9FA5]+")) {
                    count++;
                }
            }
        }
        return count;
    }
}
