package com.shuidihuzhu.cf.admin.delegate;

import com.shuidihuzhu.cf.response.OpResult;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2018-10-11  14:34
 */
public interface SeaUserAuthDelegate {
    /**
     * 某用户是否拥有某接口权限
     * @param userId
     * @param permission
     * @return
     */
    OpResult<Boolean> hasPermission(Integer userId, String permission);

    boolean hasPermissionSimple(Integer userId, String permission);

    /**
     * 略
     * @param roleId
     * @param current
     * @param pageSize
     * @return
     */

    /**
     * 获取拥有某接口权限的所有用户id
     * @param permission
     * @return
     */
    OpResult<Set<Integer>> listUserByPermission(String permission);
}
