package com.shuidihuzhu.cf.admin.util;

import com.shuidihuzhu.msg.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/3/21
 */
public class AdminDateUtil {

    private static final long ONE_DAYS_MILLS = 24 * 3600 * 1000;

    public static DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");

    public static AdminDateUtil INSTANCE = new AdminDateUtil();

    private AdminDateUtil() {
    }

    public static long convertToMills(String str, String format) {
        long mills = 0L;
        Date date = com.shuidihuzhu.common.util.DateUtil.getStrToDate(format, str);
        if (date != null) {
            mills = date.getTime();
        }
        return mills;
    }

    public static boolean isTimeExtendLimit(String start, String end, int limit) {
        if (StringUtils.isBlank(start) || StringUtils.isBlank(end)) {
            return false;
        }

        try {
            Date startDate = DateUtil.getStr2LDate(start);
            Date endDate = DateUtil.getStr2LDate(end);

            return endDate.getTime() - startDate.getTime() > ONE_DAYS_MILLS * limit;
        } catch (Exception e) {
            return false;
        }
    }

    public static boolean isEndTimeExtendToday(String end) {
        if (StringUtils.isBlank(end)) {
            return false;
        }

        try {

            Date endDate = DateUtil.getStr2LDate(end);
            return endDate.getTime() > new DateTime().withHourOfDay(23).withMinuteOfHour(59)
                    .withSecondOfMinute(59).withMillisOfSecond(999).getMillis();
        } catch (Exception e) {
            return false;
        }
    }

}
