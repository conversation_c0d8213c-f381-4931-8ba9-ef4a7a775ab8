package com.shuidihuzhu.cf.admin.cache;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.config.WorkOrderConfigFeignClient;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class WorkOrderDoneListCache extends SimpleAbstractCache<Byte, Map<Integer, List<Integer>>> {

    @Resource
    private WorkOrderConfigFeignClient workOrderConfigFeignClient;

    public List<Integer> getDoneList(Integer orderType) {
        byte a = 1;
        Map<Integer, List<Integer>> map = getValue(a);
        return map.getOrDefault(orderType, Lists.newArrayList());
    }

    @Override
    protected Map<Integer, List<Integer>> loadValue(Byte key) {
        Response<Map<Integer, List<Integer>>> resp = workOrderConfigFeignClient.getDoneListConfig();
        if (resp == null || resp.notOk()) {
            log.error("{} getDoneListConfig error resp {}", getName(), resp);
            throw new RuntimeException(getName() + " getDoneListConfig error resp " + resp);
        }
        return resp.getData();
    }

    @Override
    protected int getRefreshAfterWriteSecond() {
        return 600;
    }
}
