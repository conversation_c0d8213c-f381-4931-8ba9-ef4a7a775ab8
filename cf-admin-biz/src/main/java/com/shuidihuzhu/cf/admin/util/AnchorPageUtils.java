package com.shuidihuzhu.cf.admin.util;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.vo.AnchorPageBigInt2VO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-08-04  13:31
 */
public class AnchorPageUtils {

    /**
     * 取前一页就取 10个
     * 取下一页就取 10 + 1 个 同时判断是否返回11个来设置hasMore
     * @param size
     * @param isPre 是否是向前翻页
     * @param promoter
     * @param anchorPromoter
     * @param <T>
     * @return
     */
    public static <T> AnchorPageBigInt2VO<T> list(int size, boolean isPre, DataPromoter<T> promoter,
                                                  AnchorPromoter<T> anchorPromoter){
        List<T> actualList = promoter.promote(isPre ? size : size + 1);

        if (CollectionUtils.isEmpty(actualList)) {
            return createEmptyAnchorPageVO();
        }

        List<T> list = Lists.newArrayList(actualList);

        // 如果是向前翻页需要倒序 因为查询的时候取了反序 这里抵消一下
        if (isPre) {
            list = Lists.reverse(list);
        }

        /*
          结果集合大小大于 传入size则一定 还有更多
         */
        boolean hasMore = isPre || CollectionUtils.size(list) > size;

        /*
          返回前端实际结果 应该和前端传入size相同
         */
        if (!isPre && hasMore) {
            list.remove(size);
        }
        T lastData = list.get(CollectionUtils.size(list) - 1);
        long nextAnchor = anchorPromoter.anchor(lastData);
        T firstData = list.get(0);
        long preAnchor = anchorPromoter.anchor(firstData);

        AnchorPageBigInt2VO<T> v = new AnchorPageBigInt2VO<>();
        v.setList(list);
        v.setHasNext(hasMore);
        v.setSize(size);
        v.setPreAnchor(preAnchor);
        v.setNextAnchor(nextAnchor);
        return v;
    }

    private static <T> AnchorPageBigInt2VO<T> createEmptyAnchorPageVO() {
        return new AnchorPageBigInt2VO<>();
    }

    public interface DataPromoter<T>{

        /**
         * 获取数据方法
         * @param realSize 真正执行查询时候的size 不能用前端传的size
         * @return
         */
        List<T> promote(int realSize);
    }

    public interface AnchorPromoter<T>{

        /**
         * 获取model的锚点
         * @return 锚点
         */
        long anchor(T lastItem);
    }
}
