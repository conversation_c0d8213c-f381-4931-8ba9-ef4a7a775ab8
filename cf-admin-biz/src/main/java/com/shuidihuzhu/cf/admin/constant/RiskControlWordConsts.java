package com.shuidihuzhu.cf.admin.constant;

import org.apache.commons.lang.ArrayUtils;

/**
 * <AUTHOR>
 * @date 2019-01-29  16:26
 */
public interface RiskControlWordConsts {

    /**
     * 敏感词分类
     */
    long[] SENSITIVE_CATEGORY =
            {3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23};

    /**
     * 禁止词分类
     */
    long[] PROHIBITION_CATEGORY = {24};

    long[] RISK_CONTROL_CATEGORY = ArrayUtils.addAll(SENSITIVE_CATEGORY, PROHIBITION_CATEGORY);

}
