package com.shuidihuzhu.cf.admin.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.google.common.collect.ImmutableSet;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;

/**
 * <AUTHOR>
 * @date 2018-12-23  16:02
 */
@Slf4j
public class MQLogUtils {

    private static final ImmutableSet<String> WHITE_SET = ImmutableSet.of(
            "msgId",
            "keys",
            "topic",
            "tags"
    );

    private static final PropertyFilter FILTER = (source, name, value) -> WHITE_SET.contains(name);

    public static void logWithLogger(Logger logger, ConsumerMessage message, Object ext) {
        String msgJson = JSON.toJSONString(message, FILTER);
        logger.info("CONSUMER message={}, ext={}, payload={}",
                msgJson,
                JSON.toJSONString(ext),
                JSON.toJSONString(message.getPayload())
        );
    }

    public static void logWithSelf(Object self, ConsumerMessage message, Object ext) {
        String msgJson = JSON.toJSONString(message, FILTER);
        log.info("CONSUMER className={}, message={}, ext={}, payload={}",
                getClassName(self),
                msgJson,
                JSON.toJSONString(ext),
                JSON.toJSONString(message.getPayload())
        );
    }

    public static void logErrorWithLogger(Logger logger, ConsumerMessage message, Exception exception, Object ext) {
        String msgJson = JSON.toJSONString(message, FILTER);
        logger.error("ERROR message={}, ext={}, payload={}",
                msgJson,
                JSON.toJSONString(ext),
                JSON.toJSONString(message.getPayload()),
                exception
        );
    }

    public static void logErrorWithSelf(Object self, ConsumerMessage message, Exception exception, Object ext) {
        String msgJson = JSON.toJSONString(message, FILTER);
        log.error("ERROR className={}, message={}, ext={}, payload={}",
                getClassName(self),
                msgJson,
                JSON.toJSONString(ext),
                JSON.toJSONString(message.getPayload()),
                exception
        );
    }

    @NotNull
    private static String getClassName(Object obj) {
        Class clazz = obj.getClass();
        String name = clazz.getName();
        int index = name.indexOf("$");
        if (index < 0) {
            return name;
        }
        return name.substring(0, index);
    }
}
