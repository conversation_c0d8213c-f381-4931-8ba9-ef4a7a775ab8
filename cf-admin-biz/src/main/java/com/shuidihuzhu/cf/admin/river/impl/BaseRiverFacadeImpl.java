package com.shuidihuzhu.cf.admin.river.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.admin.river.IRiverAssembleInterface;
import com.shuidihuzhu.cf.admin.river.RiverHelpService;
import com.shuidihuzhu.cf.admin.river.RiverReviewService;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.admin.river.*;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonTag;
import com.shuidihuzhu.cf.model.river.RiverUsageTypeEnum;
import com.shuidihuzhu.cf.vo.approve.RiverDetailVO;
import com.shuidihuzhu.cf.vo.approve.RiverHandleParamVO;
import com.shuidihuzhu.cf.vo.approve.RiverHandleVO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Service
public abstract class BaseRiverFacadeImpl<DetailVO> implements IRiverAssembleInterface<DetailVO> {

    @Autowired
    private RiverReviewService riverReviewService;

    @Autowired
    private RiverHelpService riverHelpService;

    @Override
    public String getRemarkContent(RiverHandleParamVO param) {
        return riverHelpService.getRemarkContent(param);
    }

    @Override
    public boolean remark2HomePage(RiverHandleParamVO param) {
        return false;
    }

    @Override
    public RiverUsageTypeEnum getUsageType() {
        return RiverUsageTypeEnum.DEFAULT;
    }

    @Override
    public Response<Void> handleWithWorkOrder(RiverHandleParamVO param) {
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public DetailVO getInfo(int caseId, RiverHandleParamVO param) {
        return null;
    }

    @Override
    public DetailVO getInfo(int caseId) {
        return null;
    }

    @Override
    public int getHistoryDataType() {
        return 0;
    }

    @Override
    public Class<DetailVO> getSnapshotClazz() {
        return null;
    }

    @Override
    public void handleSnapshot(DetailVO detailVO) {

    }

    @Override
    public Response<Void> onSubmit(int caseId) {
        return riverHelpService.onSubmit(this, caseId);
    }

    @Override
    public Response<Void> handle(RiverHandleParamVO param) {
        return riverHelpService.handle(this, param);
    }

    @Override
    public RiverDetailVO<DetailVO> getDetail(int caseId, long workOrderId) {
        return riverHelpService.getDetail(this, caseId, workOrderId);
    }

    @Override
    public void onHandle(RiverHandleParamVO param) {

    }

    @Override
    public void handleSendRefuseMsg(RiverHandleParamVO param, List<String> rejectLocations, Set<String> rejectReasons) {
    }

    @Override
    public RiverHandleVO getHandleInfo(int caseId) {
        return riverHelpService.getHandleInfo(this, caseId);
    }

    @Override
    public List<CfRefuseReasonTag> getRefuseReasonTags() {
        return Lists.newArrayList();
    }

    @Override
    public OperationActionTypeEnum getWorkOrderSnapshotActionType() {
        return OperationActionTypeEnum.RIVER_WORK_ORDER_SNAPSHOT;
    }

    @Override
    public int getRefuseOptionDataType() {
        return 0;
    }

    @Override
    public CfRefuseReasonEntity.RejectOptionUseSceneEnum getRejectOptionUseSceneEnum() {
        return null;
    }

    @Override
    public void onSaveRemark(RiverHandleParamVO param, String content) {

    }
}
