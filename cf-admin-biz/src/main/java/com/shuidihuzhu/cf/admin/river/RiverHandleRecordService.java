package com.shuidihuzhu.cf.admin.river;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingDetailSendMsgTemplateBiz;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationRecordDTO;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.model.river.RiverReviewDO;
import com.shuidihuzhu.cf.model.river.RiverUsageTypeEnum;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.cf.vo.approve.RiverHandleParamVO;
import com.shuidihuzhu.cf.vo.approve.RiverOperationRecordModel;
import com.shuidihuzhu.cf.vo.approve.RiverHandleRecordVO;
import com.shuidihuzhu.cf.vo.approve.RiverSendSmsParamVO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.msg.model.SmsTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 保存材料审核记录
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class RiverHandleRecordService {


    @Autowired
    private RiverReviewService riverReviewService;

    @Autowired
    private CommonOperationRecordClient commonOperationRecordClient;

    @Autowired
    private ApproveRemarkOldService approveRemarkOldService;

    @Autowired
    private AdminCrowdfundingDetailSendMsgTemplateBiz sendMsgTemplateBiz;

    private static final String SMS_REMARK_FORMAT = "发送短信:\n短信内容: %s\n发送手机号: 【%s】";

    public Response<List<RiverHandleRecordVO>> list(int caseId, RiverUsageTypeEnum usageType) {
        RiverReviewDO riverReviewDO = riverReviewService.get(caseId, usageType);
        if (riverReviewDO == null) {
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        List<OperationRecordDTO> records = commonOperationRecordClient.listByBizIdAndActionTypeEnums(riverReviewDO.getId(),
                OperationActionTypeEnum.RIVER_APPROVE_TYPES);
        List<RiverHandleRecordVO> vos = records.stream().map(this::parseVO).sorted(Comparator.comparing(RiverHandleRecordVO::getActionTime)).collect(Collectors.toList());
        return NewResponseUtil.makeSuccess(vos);
    }

    public RiverOperationRecordModel parse2RiverRecord(OperationRecordDTO recordDTO) {
        RiverOperationRecordModel handleRecord = new RiverOperationRecordModel();
        handleRecord.setOperationRecord(recordDTO);
        Map<String, String> extMap = recordDTO.getExtMap();
        RiverHandleParamVO handleParam = JSON.parseObject(extMap.get("handleParam"), RiverHandleParamVO.class);//已检查过
        handleRecord.setHandleParam(handleParam);
        handleRecord.setContent(extMap.get("remark"));
        return handleRecord;
    }

    public RiverHandleRecordVO parseVO(OperationRecordDTO recordDTO) {
        return convertRemarkVO(parse2RiverRecord(recordDTO));
    }

    public RiverHandleRecordVO convertRemarkVO(RiverOperationRecordModel recordVO) {

        RiverHandleRecordVO remark = new RiverHandleRecordVO();
        RiverHandleParamVO handleParam = recordVO.getHandleParam();
        fillHandleParam2Remark(handleParam, remark);

        remark.setContent(recordVO.getContent());

        OperationRecordDTO operationRecord = recordVO.getOperationRecord();
        remark.setActionTime(operationRecord.getActionTime());
        remark.setOperator(operationRecord.getNameWithOrg());
        return remark;
    }

    private void fillHandleParam2Remark(RiverHandleParamVO handleParam, RiverHandleRecordVO remark) {
        if (handleParam != null) {
            remark.setWorkOrderId(handleParam.getWorkOrderId());
            remark.setComment(handleParam.getHandleComment());
            remark.setHandleResult(handleParam.getHandleType().getMsg());
        }
    }

    public void save(RiverHandleParamVO param, String content) {
        RiverReviewDO riverReviewDO = riverReviewService.get(param.getCaseId(), param.getUsageTypeEnum());
        // 保存记录
        commonOperationRecordClient.create()
                .buildBasicPlatform(riverReviewDO.getId(), param.getOperatorId(), param.getHandleType().getOperationActionTypeEnum())
                .buildExt("handleParam", JSON.toJSONString(param))
                .buildExt("remark", content)
                .save();
    }

    public void saveSendSmsLog(RiverSendSmsParamVO param, int userId) {
        // 从modelNum里解析内容
        param = processContent(param);

        String remark = String.format(SMS_REMARK_FORMAT, param.getContent(), param.getMobile());
        approveRemarkOldService.add(param.getCaseId(), userId, remark);

        RiverHandleParamVO handleParam = new RiverHandleParamVO();
        handleParam.setOperatorId(param.getOperatorId());
        handleParam.setCaseId(param.getCaseId());
        handleParam.setWorkOrderId(param.getWorkOrderId());
        handleParam.setHandleType(RiverHandleParamVO.HandleType.SEND_SMS);
        handleParam.setUsageTypeEnum(RiverUsageTypeEnum.CREDIT_INFO);
        save(handleParam, remark);
    }

    private RiverSendSmsParamVO processContent(RiverSendSmsParamVO param) {
        String message = param.getContent();
        if (StringUtils.isNotEmpty(message)) {
            return param;
        }
        String modelNum = param.getModelNum();
        try {
            List<SmsTemplate> smsTemplates = sendMsgTemplateBiz.getTemplateByModelNum(modelNum);
            String smsMsg = (CollectionUtils.isEmpty(smsTemplates) || smsTemplates.size() > 1) ?
                    "" : smsTemplates.get(0).getText();
            param.setContent(smsMsg);
        } catch (Exception e) {
            log.error("解析短信内容出错 param:{}", param);
        }
        return param;
    }
}
