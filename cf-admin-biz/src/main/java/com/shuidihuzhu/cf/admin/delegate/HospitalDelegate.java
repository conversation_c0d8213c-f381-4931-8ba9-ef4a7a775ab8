package com.shuidihuzhu.cf.admin.delegate;

import com.shuidihuzhu.algo.medicare.client.innerapi.CMMHospitalInnerApi;
import com.shuidihuzhu.algo.medicare.dto.CmmMedicareHospitalDto;
import com.shuidihuzhu.algo.medicare.dto.HospitalParamVo;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class HospitalDelegate {

    @Autowired
    private CMMHospitalInnerApi cmmHospitalInnerApi;

    /**
     * 医院信息验真
     * @param hospitalName
     * @return
     */
    @Nullable
    public Boolean checkInfo(String hospitalName) {
        HospitalParamVo v = new HospitalParamVo();
        v.setHospitalName(hospitalName);
        Response<List<CmmMedicareHospitalDto>> resp = cmmHospitalInnerApi.queryCfHospitalInfoListByName(hospitalName);
        log.info("check hospital resp:{}", resp);
        if (resp == null || resp.getCode() != 0) {
            log.info("check hospital error resp:{}", resp);
            return null;
        }
        List<CmmMedicareHospitalDto> data = resp.getData();
        return CollectionUtils.isNotEmpty(data);
    }

}
