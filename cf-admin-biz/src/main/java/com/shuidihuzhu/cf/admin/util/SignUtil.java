package com.shuidihuzhu.cf.admin.util;

import org.apache.commons.codec.digest.DigestUtils;

import java.io.UnsupportedEncodingException;
import java.util.*;

public class SignUtil {
	
	private final static String charset = "utf-8";
    public static final String CONST_SEA_APPTOKEN= "344905a385a5454e824565ad2ef23ff6";
    public static final String CONST_SEA_APPSECRETKEY = "f382b12d4fec4d4098086f3e904fc2ce";


    /**
	 * 生成签名方法
	 * @param token
	 * @param secretkey
	 * @param params
	 * @return
	 */
	public static String generateSign(String token, String secretkey, Map<String, String> params) {
		// Step1. 过滤参数，除去空值和sign
		Map<String, String> paramsNew = paramFilter(params);
		// Step2. 对参数key做排序，拼接源串
		String preSignStr = createLinkString(paramsNew);
		// Step3. 生成签名 
		return sign(preSignStr, secretkey, charset);
	}
	
	
	/**
	 * 验证签名方法
	 * @param token
	 * @param secretkey
	 * @param params
	 * @param sign
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	public static boolean verify(String token, String secretkey, Map<String, String> params, String sign) {
		String mysign = generateSign(token, secretkey, params);
		if (mysign.equals(sign)) {
			return true;
		} else {
			return false;
		}
	}

	
	
	/**
	 * 除去数组中的空值和签名参数
	 * @param sArray 签名参数组
	 * @return 去掉空值与签名参数后的新签名参数组
	 */
	private static Map<String, String> paramFilter(Map<String, String> sArray) {
		Map<String, String> result = new HashMap<String, String>();
		if (sArray == null || sArray.size() <= 0) {
			return result;
		}

		String value = null;
		for (String key : sArray.keySet()) {
			value = sArray.get(key);
			if (value == null || "".equals(value) || "sign".equalsIgnoreCase(key)) {
				continue;
			}
			result.put(key, value);
		}
		return result;
	}

	/**
	 * 把数组所有元素按key进行字典升序排列，并按照“参数=参数值”的模式用“&”字符拼接成字符串
	 * @param params 需要排序并参与字符拼接的参数组
	 * @return 拼接后字符串
	 */
	private static String createLinkString(Map<String, String> params) {
		List<String> keys = new ArrayList<String>(params.keySet());
		Collections.sort(keys);
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < keys.size(); i++) {
			String key = keys.get(i);
			String value = params.get(key);
			if (i == keys.size() - 1) {
				// 拼接时，不包括最后一个&字符
				sb.append(key).append("=").append(value);
			} else {
				sb.append(key).append("=").append(value).append("&");
			}
		}
		return sb.toString();
	}
	
	/**
	 * @param content
	 * @param charset
	 * @return
	 * @throws RuntimeException
	 */
	private static byte[] getContentBytes(String content, String charset) throws RuntimeException {
		if (charset == null || charset.equals("")) {
			return content.getBytes();
		}
		try {
			return content.getBytes(charset);
		} catch (UnsupportedEncodingException e) {
			throw new RuntimeException("MD5签名过程中出现错误,指定的编码集不对,您目前指定的编码集是:" + charset);
		}
	}
	
	
	
	/**
	 * 生成 签名字符串
	 * 
	 * @param text 需要签名的字符串
	 * @param key 密钥
	 * @param input_charset 编码格式
	 * @return 签名结果
	 */
	private static String sign(String text, String key, String input_charset) {
		text = text + key;
		return DigestUtils.md5Hex(getContentBytes(text, input_charset));
	}
	
	
	public static void main(String[] args) {
		
		Map<String, String> params = new HashMap<String, String>();
		params.put("token", "344905a385a5454e824565ad2ef23ff6");
		params.put("biz", "cf");
//		params.put("sign", "a7959a87889d1ba8d4be8254b14ce68d");
		
		String sign = SignUtil.generateSign("344905a385a5454e824565ad2ef23ff6", "f382b12d4fec4d4098086f3e904fc2ce", params);
		System.out.println(sign);
		//a7959a87889d1ba8d4be8254b14ce68d
		System.out.println(SignUtil.verify("344905a385a5454e824565ad2ef23ff6", "f382b12d4fec4d4098086f3e904fc2ce", params, sign));
		Map<String, String> params1 = new HashMap<String, String>();
		params.put("biz", "cf");
		System.out.println(SignUtil.verify("344905a385a5454e824565ad2ef23ff6", "f382b12d4fec4d4098086f3e904fc2ce", params1, sign));

	}

}
