package com.shuidihuzhu.cf.admin.util;

import com.google.common.collect.Maps;

import com.shuidihuzhu.cf.enums.crowdfunding.NewCfRefundConstant;

import java.util.Map;

/**
 * Created by lixuan on 2017/9/11.
 */
public class CfRefundUtil {

    private static Map<Integer, NewCfRefundConstant.RefundType> enumMap = Maps.newHashMap();

    static {
        for (NewCfRefundConstant.RefundType tmpEnum : NewCfRefundConstant.RefundType.values()) {
            enumMap.put(tmpEnum.getCode(), tmpEnum);
        }
    }

    public static NewCfRefundConstant.RefundType fromCode(int code) {
        return enumMap.get(code);
    }
}
