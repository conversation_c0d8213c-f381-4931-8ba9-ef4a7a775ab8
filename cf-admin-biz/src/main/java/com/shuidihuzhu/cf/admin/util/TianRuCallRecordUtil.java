package com.shuidihuzhu.cf.admin.util;

import com.google.common.collect.Maps;
import com.shuidihuzhu.common.web.util.MD5Util;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * @author: fengxuan
 * @create 2019-11-20 19:36
 **/
public class TianRuCallRecordUtil {

    //http://docs.ti-net.com.cn/index.php?m=content&c=index&a=lists&catid=34
    public static Map<String, Object> callRecordUrlInfo(String url) {
        if (StringUtils.isBlank(url)) {
            return Maps.newHashMap();
        }
        int seed = 11;
        Map<String, Object> map = Maps.newHashMap();
        StringBuilder stringBuilder = new StringBuilder();
        String completeUrl = stringBuilder.append(url)
                .append("?enterpriseId=").append("3004582")
                .append("&userName=").append("fengxuan")
                .append("&seed=").append(seed)
                .append("&pwd=").append(MD5Util.getMD5HashValue(MD5Util.getMD5HashValue("kf123456") + String.valueOf(seed)))
                .toString();

        map.put("phoneRecordUrl", completeUrl);
        return map;
    }

    /**
     * 拼接可以访问的录音url
     * @param url
     * @return
     */
    public static String processAudioUrl(String url) {
        if (StringUtils.isEmpty(url)) {
            return "";
        }
        int seed = 11;
        return url +
                "?enterpriseId=" + "3004582" +
                "&userName=" + "fengxuan" +
                "&seed=" + seed +
                "&pwd=" + MD5Util.getMD5HashValue(MD5Util.getMD5HashValue("kf123456") + seed);
    }

}
