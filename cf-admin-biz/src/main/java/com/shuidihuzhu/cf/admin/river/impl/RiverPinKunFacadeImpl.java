package com.shuidihuzhu.cf.admin.river.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.admin.river.IRiverAdapter;
import com.shuidihuzhu.cf.admin.river.IRiverAssembleInterface;
import com.shuidihuzhu.cf.admin.river.RiverHelpService;
import com.shuidihuzhu.cf.admin.river.RiverReviewService;
import com.shuidihuzhu.cf.biz.admin.UserCommentBiz;
import com.shuidihuzhu.cf.biz.aiphoto.ImageWatermarkService;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.feign.CfRaiseMaterialClient;
import com.shuidihuzhu.cf.client.material.model.CfBasicLivingGuardModel;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.enums.admin.UserCommentSourceEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoDataStatusTypeEnum;
import com.shuidihuzhu.cf.model.admin.UserComment;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonTag;
import com.shuidihuzhu.cf.model.river.RiverReviewDO;
import com.shuidihuzhu.cf.model.river.RiverUsageTypeEnum;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditCaseDetail;
import com.shuidihuzhu.cf.vo.approve.InitialAuditAdditionInfoVO;
import com.shuidihuzhu.cf.vo.approve.RiverHandleParamVO;
import com.shuidihuzhu.cf.vo.approve.RiverHandleVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 贫困审核处理类
 * <AUTHOR>
 */
@Slf4j
@Service
public class RiverPinKunFacadeImpl extends BaseRiverFacadeImpl<InitialAuditAdditionInfoVO>
        implements IRiverAssembleInterface<InitialAuditAdditionInfoVO> {

    @Autowired
    private RiverReviewService riverReviewService;

    @Autowired
    private RiverHelpService riverHelpService;

    @Autowired
    private CfRaiseMaterialClient cfRaiseMaterialClient;

    @Autowired
    private UserCommentBiz commentBiz;

    @Autowired
    private ImageWatermarkService watermarkService;

    @Override
    public RiverUsageTypeEnum getUsageType() {
        return RiverUsageTypeEnum.PIN_KUN;
    }

    @Override
    public boolean remark2HomePage(RiverHandleParamVO param) {
        return param.getHandleType() != RiverHandleParamVO.HandleType.HANDLE_LATER;
    }

    @Override
    public InitialAuditAdditionInfoVO getInfo(int caseId, RiverHandleParamVO param) {
        return getInfo(caseId);
    }

    @Override
    public InitialAuditAdditionInfoVO getInfo(int caseId) {
        InitialAuditAdditionInfoVO view = new InitialAuditAdditionInfoVO();
        RpcResult<CfBasicLivingGuardModel> resp = cfRaiseMaterialClient.selectLivingGuard(caseId);
        if (resp.isFail()) {
            return null;
        }

        InitialAuditCaseDetail.CfBasicLivingGuardView guardView =
                new InitialAuditCaseDetail.CfBasicLivingGuardView(resp.getData());
        // 填充水印
        watermarkService.fillBasicLivingWaterMark(caseId, guardView);

        view.setDiBaoAndPinKunInfo(guardView);
        return view;
    }

    @Override
    public int getHistoryDataType() {
        return getRefuseOptionDataType();
    }

    @Override
    public Class<InitialAuditAdditionInfoVO> getSnapshotClazz() {
        return InitialAuditAdditionInfoVO.class;
    }

    @Override
    public RiverHandleVO getHandleInfo(int caseId) {
        return riverHelpService.getHandleInfo(this, caseId);
    }

    @Override
    public List<CfRefuseReasonTag> getRefuseReasonTags() {
        return riverHelpService.getRefuseReasonTags(this);
    }

    @Override
    public int getRefuseOptionDataType() {
        return CrowdfundingInfoDataStatusTypeEnum.PIN_KUN_HU.getCode();
    }

    @Override
    public CfRefuseReasonEntity.RejectOptionUseSceneEnum getRejectOptionUseSceneEnum() {
        return CfRefuseReasonEntity.RejectOptionUseSceneEnum.INITIAL_AUDIT;
    }

    @Override
    public OperationActionTypeEnum getWorkOrderSnapshotActionType() {
        return OperationActionTypeEnum.PIN_KUN_RIVER_WORK_ORDER_SNAPSHOT;
    }

    @Override
    public void onHandle(RiverHandleParamVO param) {
        RiverHandleParamVO.HandleType handleType = param.getHandleType();
        if (handleType == RiverHandleParamVO.HandleType.REJECT) {
            riverHelpService.addRejectHistory(this, param);
        }
    }

    @Override
    public void onSaveRemark(RiverHandleParamVO param, String content) {
//        UserComment comment = new UserComment();
//        comment.setOperatorId(param.getOperatorId());
//        comment.setCaseId(param.getCaseId());
//        comment.setCommentSource(UserCommentSourceEnum.INITIAL_AUDIT.getCode());
//        comment.setCommentType(0);
//        comment.setOperateMode(param.getHandleType().getMsg());
//        comment.setWorkOrderId(param.getWorkOrderId());
//        comment.setComment(param.getHandleComment());
//        comment.setOperateDesc(content);
//        commentBiz.insert(comment);
    }

    public String getComment(RiverHandleParamVO param) {
        if (Objects.isNull(param) || param.getHandleType() == null) {
            return StringUtils.EMPTY;
        }
        List<String> lines = Lists.newArrayList();
        lines.add("贫困信息:" + param.getHandleType().getMsg());

        // 驳回原因
        List<Integer> rejectIds = param.getRejectIds();
        if (param.getHandleType() == RiverHandleParamVO.HandleType.REJECT) {
            List<String> rejectReasonList = riverHelpService.getRejectReasonList(rejectIds);
            lines.addAll(rejectReasonList);
        }
//        if (param.getHandleType() == RiverHandleParamVO.HandleType.STOP_CASE) {
//            lines.add("风险案例");
//        }
//        int callStatus = param.getCallStatus();
//        if (callStatus != 0) {
//            lines.add("呼通状态:" + (param.getCallStatus() == 1 ? "呼通" : "未呼通"));
//        }

        return StringUtils.join(lines, "\n");
    }

    public <InfoVO> void saveOrderSnapshot(RiverHandleParamVO param) {
        riverHelpService.saveOrderSnapshot(this, param);
    }

}
