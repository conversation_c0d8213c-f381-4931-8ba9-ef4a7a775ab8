package com.shuidihuzhu.cf.admin.delegate;

import com.alibaba.fastjson.JSON;
import com.baidu.aip.ocr.AipOcr;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.qcloud.cos.utils.IOUtils;
import com.shuidihuzhu.cf.vo.ocr.OCRBaiDuResponse;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OCRBaiDuDelegate {

    //设置APPID/AK/SK免费账号
//    private static final String APP_ID_OCR = "10948171";
//    private static final String API_KEY_OCR = "EQY2swgAqqQrk0kzV1QYPcre";
//    private static final String SECRET_KEY_OCR = "GKXEUP3r8uxBCfVAhs4a7ZfIyu7IU4vS";

    //设置APPID/AK/SK付费账号
    public static final String APP_ID_OCR = "44282476";
    public static final String API_KEY_OCR = "npXG6BTKSdAyitbe4PHVB6WE";
    public static final String SECRET_KEY_OCR = "pL2lGmchuMPsteBWBGKzO30udpNPPrgg";

    private final AipOcr client =  new AipOcr(APP_ID_OCR, API_KEY_OCR, SECRET_KEY_OCR);

    @PostConstruct
    public void init(){
        client.setConnectionTimeoutInMillis(5000);
        client.setSocketTimeoutInMillis(60000);
    }

    /**
     * 获取百度ocr client
     *
     * @return
     */
    public AipOcr getOCRClient() {
//        AipOcr client =  new AipOcr(APP_ID_OCR, API_KEY_OCR, SECRET_KEY_OCR);
//        client.setConnectionTimeoutInMillis(5000);
//        client.setSocketTimeoutInMillis(60000);
        return client;
    }

    public OCRBaiDuResponse accurateGeneral(String file) {
        AipOcr client = getOCRClient();
        // 传入可选参数调用接口
        HashMap<String, String> options = Maps.newHashMap();
        options.put("language_type", "CHN_ENG");
//        options.put("detect_direction", "true");
//        options.put("detect_language", "true");
//        options.put("probability", "true");
        try {
            JSONObject res = client.accurateGeneral(getStreamByte(file), options);
            String jsonString = res.toString();
            log.info("ocr baidu response:{}", jsonString);
            return JSON.parseObject(jsonString, OCRBaiDuResponse.class);
        } catch (Exception e) {
            log.debug(e.getMessage());
            log.error("ocr baidu error", e);
            return null;
        }
    }

    public List<String> accurateGeneralList(String file) {
        OCRBaiDuResponse ocrBaiDuResponse = accurateGeneral(file);
        return Optional.ofNullable(ocrBaiDuResponse)
                .map(OCRBaiDuResponse::getWords_result)
                .orElse(Lists.newArrayList())
                .stream()
                .map(OCRBaiDuResponse.Result::getWords)
                .collect(Collectors.toList());
    }


    private static byte[] getStreamByte(String fileURL) throws Exception {
        InputStream inputStream = null;
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        if (fileURL.startsWith("http") || fileURL.startsWith("https")) {
            URL url = new URL(fileURL);
            URLConnection urlConnection =  url.openConnection();
            urlConnection.setConnectTimeout(5000);
            urlConnection.setReadTimeout(60000);

            inputStream = urlConnection.getInputStream();
        } else {
            inputStream = new FileInputStream(fileURL);
        }
        IOUtils.copy(inputStream, output);
        return output.toByteArray();
    }

}
