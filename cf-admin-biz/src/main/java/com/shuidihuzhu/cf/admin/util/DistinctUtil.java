package com.shuidihuzhu.cf.admin.util;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.function.Function;
import java.util.function.Predicate;

/**
 * @author: fengxuan
 * @create 2019-12-02 17:54
 **/
@Slf4j
public class DistinctUtil {

    public static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> concurrentHashMap = Maps.newConcurrentMap();
        return t -> concurrentHashMap.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }
}
