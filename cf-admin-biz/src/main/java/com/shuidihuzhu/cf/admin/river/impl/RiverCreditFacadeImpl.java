package com.shuidihuzhu.cf.admin.river.impl;

import com.shuidihuzhu.cf.admin.river.*;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseReasonEntityBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseReasonTagBiz;
import com.shuidihuzhu.cf.client.feign.cipher.constants.DesensitizeEnum;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonTag;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cf.model.river.RiverReviewDO;
import com.shuidihuzhu.cf.model.river.RiverUsageTypeEnum;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditSearchService;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditCaseDetail;
import com.shuidihuzhu.cf.vo.approve.CreditPageInfoVO;
import com.shuidihuzhu.cf.vo.approve.RiverHandleParamVO;
import com.shuidihuzhu.cf.vo.approve.RiverHandleVO;
import com.shuidihuzhu.client.cf.workorder.CfCreditWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.CreditHandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * 增信审核处理类
 * <AUTHOR>
 */
@Slf4j
@Service
public class RiverCreditFacadeImpl extends BaseRiverFacadeImpl<CreditPageInfoVO>
        implements IRiverAssembleInterface<CreditPageInfoVO> {

    @Autowired
    private InitialAuditSearchService initialAuditSearchService;

    @Autowired
    private RiverReviewService riverReviewService;

    @Autowired
    private CfCreditWorkOrderClient cfCreditWorkOrderClient;

    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Autowired
    private RiverHelpService riverHelpService;

    @Resource
    private CfWorkOrderClient workOrderClient;

    @Autowired
    private CfRefuseReasonEntityBiz entityBiz;

    @Autowired
    private CfRefuseReasonTagBiz reasonTagBiz;

    @Autowired
    private MaskUtil maskUtil;

    @Override
    public RiverUsageTypeEnum getUsageType() {
        return RiverUsageTypeEnum.CREDIT_INFO;
    }

    @Override
    public boolean remark2HomePage(RiverHandleParamVO param) {
        return param.getHandleType() != RiverHandleParamVO.HandleType.HANDLE_LATER;
    }

    @Override
    public CreditPageInfoVO getInfo(int caseId, RiverHandleParamVO param) {
        CreditPageInfoVO view = getInfo(caseId);
        // TODO DELETE 兼容前端用
        if (param != null) {
            view.setPass(param.getHandleType() == RiverHandleParamVO.HandleType.PASS ? 1 : 0);
        }
        return view;
    }

    @Override
    public CreditPageInfoVO getInfo(int caseId) {
        CreditPageInfoVO view = new CreditPageInfoVO();
        RiverReviewDO riverReviewDO = riverReviewService.get(caseId, getUsageType());
        if (riverReviewDO == null) {
            return null;
        }
        String rejectDetailString = riverReviewDO.getRejectDetail();

        Set<Integer> rejectSets = riverReviewService.parseRejectSets(rejectDetailString);
        view.setRejectSets(rejectSets);

        CrowdfundingInfo fundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);

        view.setCreditInfo(initialAuditSearchService.getCreditInfoVO(caseId));
        view.setPropose(initialAuditSearchService.getLastPrepose(caseId));
        view.setBaseInfo(initialAuditSearchService.getBaseInfo(fundingInfo));
        view.setFirstApproveInfo(initialAuditSearchService.getFirstApproveCaseInfo(fundingInfo));
        view.setCheckInfo(initialAuditSearchService.getCheckInfo(caseId));
        view.setReviewDO(riverReviewDO);
        return view;
    }

    @Override
    public int getHistoryDataType() {
        return InitialAuditItem.CREDIT_INFO;
    }

    @Override
    public Class<CreditPageInfoVO> getSnapshotClazz() {
        return CreditPageInfoVO.class;
    }

    @Override
    public void handleSnapshot(CreditPageInfoVO creditPageInfoVO) {
        InitialAuditCaseDetail.CaseBaseInfo baseInfo = creditPageInfoVO.getBaseInfo();
        InitialAuditCaseDetail.FirstApproveCaseInfo firstApproveInfo = creditPageInfoVO.getFirstApproveInfo();

        baseInfo.setMobileMask(maskUtil.buildByDecryptPhone(baseInfo.getMobile()));
        baseInfo.setMobile(null);
        firstApproveInfo.setPatientBornCardMask(maskUtil.buildByDecryptStrAndType(firstApproveInfo.getPatientBornCard(), DesensitizeEnum.IDCARD));
    }

    @Override
    public Response<Void> handleWithWorkOrder(RiverHandleParamVO param) {
        RiverHandleParamVO.HandleType handleType = param.getHandleType();
        HandleResultEnum workHandleResult = handleType.getOrderResult();

        //其他状态工单无需处理
        if (workHandleResult == null) {
            return NewResponseUtil.makeSuccess(null);
        }

        CreditHandleOrderParam handleOrderParam = new CreditHandleOrderParam();
        handleOrderParam.setCaseId(param.getCaseId());
        handleOrderParam.setWorkOrderId(param.getWorkOrderId());
        handleOrderParam.setUserId(param.getOperatorId());
        handleOrderParam.setHandleResult(workHandleResult.getType());
        handleOrderParam.setOrderType(param.getOrderType());
        handleOrderParam.setCallStatus(String.valueOf(param.getCallStatus()));
        handleOrderParam.setOperComment(param.getHandleComment());
        Response response = cfCreditWorkOrderClient.handleCredit(handleOrderParam);
        if (response.ok() && handleType == RiverHandleParamVO.HandleType.REJECT) {
            riverHelpService.addRejectHistory(this, param);
        }
        if (response.notOk()) {
            if (response.getCode() == 2001) {
                return NewResponseUtil.makeFail(response.getMsg());
            }
            Response<WorkOrderVO> workOrderVOResponse = workOrderClient.getWorkOrderById(param.getWorkOrderId());
            if (workOrderVOResponse.ok()) {
                int handleResult = workOrderVOResponse.getData().getHandleResult();
                if (handleResult == HandleResultEnum.manual_lock.getType()) {
                    return NewResponseUtil.makeFail("该工单已手动关闭，请返回任务中心");
                }
            }
            return NewResponseUtil.makeFail("工单操作异常");
        }
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public RiverHandleVO getHandleInfo(int caseId) {
        // 取增信审核信息
        RiverHandleVO handleInfo = getSelfHandleInfo(caseId);
        if (handleInfo != null) {
            return handleInfo;
        }

        // 取初审信息
        return initialAuditSearchService.getCreditHandleInfoVO(caseId);
    }

    @Override
    public List<CfRefuseReasonTag> getRefuseReasonTags() {
        int dataType = getRefuseOptionDataType();
        CfRefuseReasonEntity.RejectOptionUseSceneEnum sceneEnum = getRejectOptionUseSceneEnum();

        List<CfRefuseReasonTag> reasonTags = reasonTagBiz.selectByDataType(dataType);

        for (CfRefuseReasonTag reasonTag : reasonTags) {
            reasonTag.setEntityList(
                    entityBiz.queryRefuseEntityByDelStatus(
                            reasonTag.getId(), 0,
                            sceneEnum.getCode(),
                            true));
        }
        return reasonTags;
    }

    @Override
    public int getRefuseOptionDataType() {
        return InitialAuditItem.CREDIT_INFO;
    }

    @Override
    public CfRefuseReasonEntity.RejectOptionUseSceneEnum getRejectOptionUseSceneEnum() {
        return CfRefuseReasonEntity.RejectOptionUseSceneEnum.CREDIT_AUDIT;
    }

    private RiverHandleVO getSelfHandleInfo(int caseId) {
        return riverHelpService.getHandleInfo(this, caseId);
    }

}
