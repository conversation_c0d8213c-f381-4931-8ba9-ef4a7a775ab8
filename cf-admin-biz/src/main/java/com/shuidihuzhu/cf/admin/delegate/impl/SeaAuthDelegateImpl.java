package com.shuidihuzhu.cf.admin.delegate.impl;

import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAuthClientV1;
import com.shuidihuzhu.cf.admin.delegate.SeaUserAuthDelegate;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.response.OpResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2018-10-11  14:34
 * 新权限系统管理
 */
@Service
@Slf4j
public class SeaAuthDelegateImpl implements SeaUserAuthDelegate {

    @Autowired
    private SeaAuthClientV1 seaAuthClientV1;


    @Override
    public OpResult<Boolean> hasPermission(Integer userId, String permission) {
        log.info("userId: {}, permission: {}", userId, permission);
        OpResult<Boolean> result = makeResult(seaAuthClientV1.hasPermissionWithUser(userId, permission));
        if (result.isFail()) {
            return OpResult.createFailResult(CfErrorCode.SYSTEM_ERROR);
        }
        return OpResult.createSucResult(result.getData());
    }

    @Override
    public boolean hasPermissionSimple(Integer userId, String permission) {
        OpResult<Boolean> booleanOpResult = hasPermission(userId, permission);
        if (booleanOpResult.isFailOrNullData()) {
            return false;
        }
        return booleanOpResult.getData();
    }

    @Override
    public OpResult<Set<Integer>> listUserByPermission(String permission) {
        log.info("listUserByPermission {}, {}, {}", permission);
        return makeResult(seaAuthClientV1.getUserIdsByPermission(permission));
    }


    private <T> OpResult<T> makeResult(AuthRpcResponse<T> response){
        log.info("{}", response);
        if (response == null) {
            return OpResult.createFailResult(CfErrorCode.SYSTEM_ERROR);
        }
        if (!response.isSuccess()) {
            return OpResult.createFailResult(CfErrorCode.SYSTEM_ERROR);
        }
        return OpResult.createSucResult(response.getResult());
    }

}
