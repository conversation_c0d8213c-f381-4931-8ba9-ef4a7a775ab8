package com.shuidihuzhu.cf.admin.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.activity.enums.ActivityTypeEnum;
import com.shuidihuzhu.cf.enums.activity.EnumPropertyVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.jetbrains.annotations.NotNull;

import javax.annotation.Nullable;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class EnumPropertyUtils {

//    public static List<EnumPropertyVO> parseEnum(String className) {
//        Class<?> aClass = null;
//        try {
//            aClass = Class.forName(className);
//        } catch (ClassNotFoundException e) {
//            log.error("getActivityEnumProperty error", e);
//            return Lists.newArrayList();
//        }
//        return parseEnum((Class<? extends Enum<?>>) aClass);
//    }

    public static List<EnumPropertyVO> parseEnum(Class<? extends Enum<?>> clazz) {
        List<Enum<?>> enumList = getEnums(clazz);
        return parseEnum(enumList);
    }

    @NotNull
    public static List<Enum<?>> getEnums(Class<? extends Enum<?>> clazz) {
        if (clazz == null) {
            throw new IllegalArgumentException("Type argument cannot be null");
        }
        Enum<?>[] enums = clazz.getEnumConstants();
        if (enums == null) {
            throw new IllegalArgumentException(clazz.getSimpleName() + " does not represent an enum type.");
        }

        return Lists.newArrayList(enums);
    }

    public static EnumPropertyVO parseEnum(Enum<?> e) {
        if (e == null) {
            return null;
        }

        //执行枚举方法获得枚举实例对应的值
        EnumPropertyVO result = new EnumPropertyVO();

        SerializeConfig config = new SerializeConfig();
        //noinspection unchecked
        config.configEnumAsJavaBean(e.getClass());
        String json = JSON.toJSONString(e, config);
        JSONObject jsonObject = JSON.parseObject(json);
        result.setData(jsonObject);

        Integer code = jsonObject.getInteger("code");
        if (code != null) {
            result.setValue(code);
        }

        Integer value = jsonObject.getInteger("value");
        if (value != null) {
            result.setValue(value);
        }

        String msg = jsonObject.getString("msg");
        if (msg != null) {
            result.setContent(msg);
        }


        String name = e.name();
        result.setName(name);

        return result;
    }

    @NotNull
    public static List<EnumPropertyVO> parseEnum(List<? extends Enum<?>> enums) {
        List<EnumPropertyVO> results = Lists.newArrayList();
        for (Enum<?> e : enums) {
            if (e == null) {
                return results;
            }

            EnumPropertyVO result = parseEnum(e);
            results.add(result);
        }
        return results;
    }

    @Nullable
    private static Object getField(Object t, String name) {
        try {
            return PropertyUtils.getProperty(t, name);
        } catch (Exception e) {
            log.info("get error parse enum", e);
        }
        return null;
    }

    public static void main(String[] args) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        List<EnumPropertyVO> enumPropertyVOS = parseEnum(ActivityTypeEnum.class);
        System.out.println("enumPropertyVOS = " + enumPropertyVOS);

//        List<EnumPropertyVO> b = parseEnum("com.shuidihuzhu.cf.activity.enums.ActivityTypeEnum");
//        System.out.println("enumPropertyVOS = " + b);


//        SerializeConfig config = new SerializeConfig();
//        config.configEnumAsJavaBean(ActivityWaveTypeEnum.class);
//        String json = JSON.toJSONString(ActivityWaveTypeEnum.KEY_AREA_CITY, config);
//        System.out.println("json = " + json);

        ArrayList<ActivityTypeEnum> enums = Lists.newArrayList(ActivityTypeEnum.KEY_AREA, ActivityTypeEnum.PERSONAL, ActivityTypeEnum.PUBLIC);
        List<EnumPropertyVO> s = parseEnum(enums);
        System.out.println("s = " + s);

    }


}
