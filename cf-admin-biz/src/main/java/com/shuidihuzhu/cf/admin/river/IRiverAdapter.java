package com.shuidihuzhu.cf.admin.river;

import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity;
import com.shuidihuzhu.cf.model.river.RiverUsageTypeEnum;
import com.shuidihuzhu.cf.vo.approve.RiverHandleParamVO;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface IRiverAdapter<DetailVO> {

    /**
     *
     * @return 材料类型
     */
    RiverUsageTypeEnum getUsageType();

    /**
     * 处理工单
     */
    Response<Void> handleWithWorkOrder(RiverHandleParamVO param);

    /**
     * @return 获取拼好的备注内容
     */
    String getRemarkContent(RiverHandleParamVO param);

    /**
     * @return 获取材料详情数据 用于工单处理快照
     */
    DetailVO getInfo(int caseId, RiverHandleParamVO param);

    /**
     * @return 获取材料详情数据
     */
    DetailVO getInfo(int caseId);

    /**
     * @return 获取老驳回历史记录类型
     */
    int getHistoryDataType();

    /**
     * @return 获取快照model class 用于快照详情解析
     */
    Class<DetailVO> getSnapshotClazz();

    /**
     * 处理快照
     */
    void handleSnapshot(DetailVO detailVO);
    /**
     * @return 是否备注到四大详情页
     */
    boolean remark2HomePage(RiverHandleParamVO param);

    void onHandle(RiverHandleParamVO param);

    /**
     * 处理驳回消息发送
     * @param param 处理参数
     * @param rejectLocationEnums 驳回位置枚举列表
     * @param rejectReasonSets 驳回原因内容列表
     */
    void handleSendRefuseMsg(RiverHandleParamVO param, List<String> rejectLocationEnums, Set<String> rejectReasonSets);

    /**
     * 获取记录工单快照的actionType
     * @return
     */
    OperationActionTypeEnum getWorkOrderSnapshotActionType();

    int getRefuseOptionDataType();

    CfRefuseReasonEntity.RejectOptionUseSceneEnum getRejectOptionUseSceneEnum();

    void onSaveRemark(RiverHandleParamVO param, String content);
}
