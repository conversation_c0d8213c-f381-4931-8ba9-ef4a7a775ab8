package com.shuidihuzhu.cf.admin.util;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.model.crowdfunding.CfSuspectedCaseInfo;
import com.shuidihuzhu.common.web.model.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * Created by Ahrievil on 2017/7/25
 */
public class AdminListUtil {

    @FunctionalInterface
    public interface Function<T> {
        List<T> getList(int i, int size);
    }

    public static <T> List<T> getList(int size, Function<T> function) {
        List<T> list = Lists.newArrayList();
        List<T> mid;
        for (int i = 0; ; i += size) {
            mid = function.getList(i, size);
            list.addAll(mid);
            if (mid.size() < size) {
                mid.clear();
                break;
            }
            mid.clear();
        }
        return list;
    }

    /**
     * 适用场景：根据xxList 去获取另外一个list,防止批量数量过多造成存储系统压力
     * @param limit
     * @param list
     * @param function
     * @param <T>
     * @param <F>
     * @return
     */
    public static <T,F> List<T> getList(int limit, List<F> list, com.google.common.base.Function<List<F>,List<T>> function){
        if(limit <= 0 || CollectionUtils.isEmpty(list)){
            return Lists.newArrayList();
        }

        if(list.size() <= limit){
            return function.apply(list);
        }

        List<List<F>> lists = Lists.partition(list, limit);

        List<T> result = Lists.newArrayList();

        for(List<F> partition : lists){
            List<T> partitionResult = function.apply(partition);
            if(CollectionUtils.isEmpty(partitionResult)){
                continue;
            }
            result.addAll(partitionResult);
        }

        return result;
    }

    public static <T> T getModelFromResponse(Response<String> feignResponse, Class<T> modelClazz){
        if(feignResponse.ok() && StringUtils.isNotBlank(feignResponse.getData())){
            return JSON.parseObject(feignResponse.getData(),modelClazz);//已检查过
        }
        return null;
    }

    public static <T> List<T> getModelListFromResponse(Response<List<String>> feignResponse, Class<T> modelClazz){
        if(feignResponse.ok() && CollectionUtils.isNotEmpty(feignResponse.getData())){
            List<T> retList = Lists.newArrayList();
            for(String string : feignResponse.getData()){
                retList.add(JSON.parseObject(string,modelClazz));//已检查过
            }
            return retList;
        }
        return Lists.newArrayList();
    }

    public static <T> List<String> getListStringFromListModel(List<T> list){
        List<String> retList = Lists.newArrayList();
        if(CollectionUtils.isEmpty(list)){
            return retList;
        }
        for(T item : list){
            retList.add(JSON.toJSONString(item));
        }
        return retList;
    }
}
