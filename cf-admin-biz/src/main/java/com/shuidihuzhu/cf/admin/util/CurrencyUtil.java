
package com.shuidihuzhu.cf.admin.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 由于Java的简单类型不能够精确的对浮点数进行运算， 这个工具类提供精确的浮点数运算，包括加减乘除和四舍五入。
 * <AUTHOR>
 * @version v1.0
 * @since v7.0.0
 * 2018年3月23日 上午10:26:41
 */
public final class CurrencyUtil {
	/**
	 * 默认除法运算精度
	 */
	private static final int DEF_DIV_SCALE = 2;

	/**
	 * 这个类不能实例化
	 */
	private CurrencyUtil() {
	}

	/**
	 * 提供精确的加法运算。
	 * 
	 * @param v1
	 *            被加数
	 * @param v2
	 *            加数
	 * @return 两个参数的和
	 */
	public static Double add(double v1, double v2) {
		BigDecimal b1 = new BigDecimal(Double.toString(v1));
		BigDecimal b2 = new BigDecimal(Double.toString(v2));
		return b1.add(b2).setScale(2, RoundingMode.HALF_UP).doubleValue();
	}

	/**
	 * 提供精确的加法运算。
	 *
	 * @param v1
	 *            被加数
	 * @param v2
	 *            加数
	 * @param scale 保留几位小数
	 * @return 两个参数的和
	 */
	public static Double add(double v1, double v2, int scale) {
		BigDecimal b1 = new BigDecimal(Double.toString(v1));
		BigDecimal b2 = new BigDecimal(Double.toString(v2));
		return b1.add(b2).setScale(scale,RoundingMode.HALF_UP).doubleValue();
	}
	
	/**
	 * 提供精确的减法运算。
	 * 
	 * @param v1
	 *            被减数
	 * @param v2
	 *            减数
	 * @return 两个参数的差
	 */
	public static double sub(double v1, double v2) {
		BigDecimal b1 = new BigDecimal(Double.toString(v1));
		BigDecimal b2 = new BigDecimal(Double.toString(v2));
		return b1.subtract(b2).setScale(2,RoundingMode.HALF_UP).doubleValue();
	}
	
	/**
	 * 提供精确的乘法运算。
	 * 
	 * @param v1
	 *            被乘数
	 * @param v2
	 *            乘数
	 * @return 两个参数的积
	 */
	public static Double mul(double v1, double v2) {
		BigDecimal b1 = new BigDecimal(Double.toString(v1));
		BigDecimal b2 = new BigDecimal(Double.toString(v2));
		return b1.multiply(b2).setScale(2,RoundingMode.HALF_UP).doubleValue();
	}

	/**
	 * 提供精确的乘法运算。
	 *
	 * @param v1
	 *            被乘数
	 * @param v2
	 *            乘数
	 * @param scale 保留的位数
	 * @return 两个参数的积
	 */
	public static Double mul(double v1, double v2, int scale) {
		BigDecimal b1 = new BigDecimal(Double.toString(v1));
		BigDecimal b2 = new BigDecimal(Double.toString(v2));
		return b1.multiply(b2).setScale(scale,RoundingMode.HALF_UP).doubleValue();
	}

	/**
	 * 元转分，原则上最多接收保留两位的数字，如若超过两位则进行四舍五入
	 * @param yuan
	 * @return int
	 */
	public static int yuan2Fen(double yuan){
		BigDecimal bigDecimal = new BigDecimal(yuan);
		return bigDecimal.multiply(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP).intValue();
	}

	/**
	 * 元转分，原则上最多接收保留两位的数字，如若超过两位则进行四舍五入
	 * @param yuan
	 * @return int
	 */
	public static int yuan2Fen(BigDecimal yuan){
		return yuan.multiply(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP).intValue();
	}

	/**
	 * 分转元，原则上最多保留两位的数字，如若超过两位则进行四舍五入
	 * @param fen
	 * @return int
	 */
	public static double fen2Yuan(int fen){
		BigDecimal bigDecimal = new BigDecimal(fen);
		return bigDecimal.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).doubleValue();
	}

	/**
	 * 分转元，原则上最多保留两位的数字，如若超过两位则进行四舍五入
	 * @param fen
	 * @return long
	 */
	public static double fen2Yuan(long fen){
		BigDecimal bigDecimal = new BigDecimal(fen);
		return bigDecimal.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).doubleValue();
	}

	/**
	 * 千克转克，原则上最多接收保留两位的数字，如若超过两位则进行四舍五入
	 * @param kg
	 * @return int
	 */
	public static int kg2K(double kg){
		BigDecimal bigDecimal = new BigDecimal(kg);
		return bigDecimal.multiply(new BigDecimal(1000)).setScale(0, RoundingMode.HALF_UP).intValue();
	}

	/**
	 * 克转千克，原则上最多保留两位的数字，如若超过两位则进行四舍五入
	 * @param g
	 * @return int
	 */
	public static double k2Kg(int g){
		BigDecimal bigDecimal = new BigDecimal(g);
		return bigDecimal.divide(new BigDecimal(1000), 3, RoundingMode.HALF_UP).doubleValue();
	}

	/**
	 * 提供（相对）精确的除法运算，当发生除不尽的情况时， 精确到小数点以后10位，以后的数字四舍五入。
	 *
	 * @param v1
	 *            被除数
	 * @param v2
	 *            除数
	 * @return 两个参数的商
	 */
	public static Double div(double v1, double v2) {
		return div(v1, v2, DEF_DIV_SCALE);
	}


	/**
	 * 提供（相对）精确的除法运算。 当发生除不尽的情况时，由scale参数指定精度，以后的数字四舍五入。
	 * 
	 * @param v1
	 *            被除数
	 * @param v2
	 *            除数
	 * @param scale
	 *            表示表示需要精确到小数点以后几位。
	 * @return 两个参数的商
	 */
	public static double div(double v1, double v2, int scale) {
		if (scale < 0) {
			throw new IllegalArgumentException(
					"The scale must be a positive integer or zero");
		}
		BigDecimal b1 = new BigDecimal(Double.toString(v1));
		BigDecimal b2 = new BigDecimal(Double.toString(v2));
		return b1.divide(b2, scale, RoundingMode.HALF_UP).doubleValue();
	}

	/**
	 * 提供精确的小数位四舍五入处理。
	 * 
	 * @param v
	 *            需要四舍五入的数字
	 * @param scale
	 *            小数点后保留几位
	 * @return 四舍五入后的结果
	 */
	public static double round(double v, int scale) {
		if (scale < 0) {
			throw new IllegalArgumentException(
					"The scale must be a positive integer or zero");
		}
		BigDecimal b = new BigDecimal(Double.toString(v));
		BigDecimal one = new BigDecimal("1");
		return b.divide(one, scale, RoundingMode.HALF_UP).doubleValue();
	}
	
}