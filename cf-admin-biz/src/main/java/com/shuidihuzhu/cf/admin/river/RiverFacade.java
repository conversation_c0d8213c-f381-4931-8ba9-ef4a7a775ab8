package com.shuidihuzhu.cf.admin.river;

import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonTag;
import com.shuidihuzhu.cf.vo.approve.RiverDetailVO;
import com.shuidihuzhu.cf.vo.approve.RiverHandleVO;
import com.shuidihuzhu.cf.vo.approve.RiverHandleParamVO;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface RiverFacade<DetailVO> {

    /**
     * 材料提交调用
     *
     * @param caseId
     * @return
     */
    Response<Void> onSubmit(int caseId);

    Response<Void> handle(RiverHandleParamVO param);

    RiverDetailVO<DetailVO> getDetail(int caseId, long workOrderId);

    RiverHandleVO getHandleInfo(int caseId);

    List<CfRefuseReasonTag> getRefuseReasonTags();

}
