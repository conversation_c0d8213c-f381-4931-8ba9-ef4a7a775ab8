package com.shuidihuzhu.cf.admin.river.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.admin.river.*;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfMaterialVerityHistoryBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseReasonEntityBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseReasonTagBiz;
import com.shuidihuzhu.cf.client.subject.caseend.CaseEndClient;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationRecordDTO;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.crowdfunding.CfFinishStatus;
import com.shuidihuzhu.cf.model.crowdfunding.CfMaterialVerityHistory;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonTag;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.river.*;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditRejectSettingsService;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditOperationItem;
import com.shuidihuzhu.cf.vo.approve.RiverDetailVO;
import com.shuidihuzhu.cf.vo.approve.RiverHandleParamVO;
import com.shuidihuzhu.cf.vo.approve.RiverHandleVO;
import com.shuidihuzhu.cf.vo.approve.RiverOperationRecordModel;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class RiverHelpServiceImpl implements RiverHelpService {

    @Autowired
    private RiverReviewService riverReviewService;

    @Autowired
    private CfMaterialVerityHistoryBiz cfMaterialVerityHistoryBiz;

    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Autowired
    private CommonOperationRecordClient commonOperationRecordClient;

    @Autowired
    private InitialAuditRejectSettingsService settingsService;

    @Autowired
    private ApproveRemarkOldService approveRemarkOldService;

    @Autowired
    private CaseEndClient caseEndClient;

    @Autowired
    private CfRefuseReasonEntityBiz reasonEntityBiz;

    @Autowired
    private RiverHandleRecordService riverHandleRecordService;

    @Autowired
    private Producer producer;

    @Autowired
    private CfRefuseReasonEntityBiz entityBiz;

    @Autowired
    private CfRefuseReasonTagBiz reasonTagBiz;

    private static final String KEY_HANDLE_PARAM = "handleParam";

    private static final String KEY_INFO = "info";

    @Override
    public <InfoVO> Response<Void> onSubmit(IRiverAdapter<InfoVO> riverAdapter, int caseId) {
        RiverReviewDO riverReviewDO = riverReviewService.get(caseId, riverAdapter.getUsageType());
        // 已通过不修改状态
        if (riverReviewDO != null && riverReviewDO.getInfoStatus() == RiverStatusEnum.PASS.getValue()) {
            return NewResponseUtil.makeSuccess(null);
        }
        riverReviewDO = riverReviewService.saveWithReturn(caseId, riverAdapter.getUsageType(), RiverStatusEnum.SUBMITTED, "");
        if (riverReviewDO != null) {
            commonOperationRecordClient.create()
                    .buildBasicUser(riverReviewDO.getId(), OperationActionTypeEnum.RIVER_SUBMIT).save();
        }
        return NewResponseUtil.makeSuccess(null);
    }

    /**
     * 处理审核逻辑
     */
    @Override
    public <InfoVO> Response<Void> handle(IRiverAdapter<InfoVO> adapter, RiverHandleParamVO param) {

        RiverHandleParamVO.HandleType handleType = param.getHandleType();
        if (handleType == null) {
            return NewResponseUtil.makeSuccess(null);
        }
        // 解析处理参数
        Map<Integer, List<RiverRejectReasonVO>> rejectDetail = riverReviewService.parseRejectIds2Detail(param.getRejectIds());
        param.setRejectDetail(rejectDetail);

        Response<Void> resp = actualHandle(param);
        if (resp.notOk()) {
            return resp;
        }
        // 记录日志
        logHandle(adapter, param);

        // 处理工单
        Response<Void> workOrderResponse = adapter.handleWithWorkOrder(param);
        if (workOrderResponse.notOk()) {
            return workOrderResponse;
        }

        sendAuditOperateMq(param);

        // 处理监听
        adapter.onHandle(param);

        // 驳回位置
        List<String> rejectLocationMsg = rejectDetail.keySet()
                .stream()
                .map(RiverRejectLocationEnum::parse)
                .map(RiverRejectLocationEnum::getName)
                .collect(Collectors.toList());

        // 驳回原因
        Set<String> rejectReasonSets = rejectDetail.values()
                .stream()
                .flatMap(Collection::stream)
                .map(RiverRejectReasonVO::getMsg)
                .collect(Collectors.toSet());

        adapter.handleSendRefuseMsg(param, rejectLocationMsg, rejectReasonSets);
        return resp;
    }

    private void sendAuditOperateMq(RiverHandleParamVO param) {
        RiverAuditOperation body = new RiverAuditOperation();
        body.setCaseId(param.getCaseId());
        body.setUsageType(param.getUsageTypeEnum());
        Message msg = new Message(MQTopicCons.CF, MQTagCons.CF_RIVER_AUDIT_OPERATION,
                MQTagCons.CF_RIVER_AUDIT_OPERATION + "_" +param.getUsageTypeEnum()+"_"+ param.getWorkOrderId() + System.currentTimeMillis(),
                body);
        MessageResult result = producer.send(msg);

        log.info("审核消息的发送.msg:{} result:{}", msg, result);
    }

    /**
     * 1. 备注保存到四大详情页
     * 2. 备注保存在初审详情页
     * 3. 保存工单快照用于查看已完成工单详情
     */
    private <InfoVO> void logHandle(IRiverAdapter<InfoVO> adapter, RiverHandleParamVO param) {
        String content = adapter.getRemarkContent(param);
        if (adapter.remark2HomePage(param)){
            approveRemarkOldService.add(param.getCaseId(), param.getOperatorId(), content);
        }

        // 操作历史记录
        riverHandleRecordService.save(param, content);

        adapter.onSaveRemark(param, content);

        // 仅需要保存快照的操作记录快照
        if(param.getHandleType().isShootOrderSnapshot()){
            // 保存工单快照
            saveOrderSnapshot(adapter, param);
        }

        // 整体保存一个记录
        if (param.getUsageTypeEnum() == RiverUsageTypeEnum.CREDIT_INFO) {
            cfMaterialVerityHistoryBiz.totalRecordCreditAudit(param);
        }
    }

    @Override
    public <InfoVO> void saveOrderSnapshot(IRiverAdapter<InfoVO> adapter, RiverHandleParamVO param) {
        OperationActionTypeEnum riverWorkOrderSnapshot = adapter.getWorkOrderSnapshotActionType();
        commonOperationRecordClient.create()
                .buildBasicPlatform(param.getWorkOrderId(), param.getOperatorId(), riverWorkOrderSnapshot)
                .buildExt(KEY_HANDLE_PARAM, JSON.toJSONString(param))
                .buildExt(KEY_INFO, JSON.toJSONString(adapter.getInfo(param.getCaseId(), param)))
                .save();
    }


    /**
     * 1. 处理材料状态
     * 2. 处理其他动作 如停止筹款
     */
    private Response<Void> actualHandle(RiverHandleParamVO param) {
        RiverHandleParamVO.HandleType handleType = param.getHandleType();

        // 处理停止筹款 停止动作不处理材料状态
        if (handleType == RiverHandleParamVO.HandleType.STOP_CASE) {
            Response<Object> stopResp = caseEndClient.stopCase(param.getCaseId(), CfFinishStatus.FINISH_BY_SHUIDI, param.getOperatorId(), param.getStopReason());
            return NewResponseUtil.makeResponse(stopResp.getCode(), stopResp.getMsg(), null);
        }

        // 处理材料审核
        return riverReviewService.handle(param);
    }


    /**
     * 保存材料驳回记录 用于案例详情页展示驳回记录与次数
     */
    @Override
    public <InfoVO> void addRejectHistory(IRiverAdapter<InfoVO> adapter, RiverHandleParamVO paramVO) {

        int dataType = adapter.getHistoryDataType();
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(paramVO.getCaseId());

        String materialInfo = JSON.toJSONString(adapter.getInfo(paramVO.getCaseId()));

        CfMaterialVerityHistory verityHistory = new CfMaterialVerityHistory();
        verityHistory.setCaseId(crowdfundingInfo.getId());
        verityHistory.setInfoId(crowdfundingInfo.getInfoId());
        verityHistory.setHandleType(CfMaterialVerityHistory.REJECT_TYPE);
        verityHistory.setMaterialId(dataType);
        verityHistory.setRefuseIds(Joiner.on(",").join(paramVO.getRejectIds()));
        verityHistory.setMaterialInfo(materialInfo);
        verityHistory.setMaterialPicInfo("");
        verityHistory.setMaterialInfoExt("");
        verityHistory.setOperatorId(paramVO.getOperatorId());
        verityHistory.setOperatorType(0);
        verityHistory.setComment(paramVO.getHandleComment());
        verityHistory.setWorkOrderId(paramVO.getWorkOrderId());

        verityHistory.setOperatorDetail(cfMaterialVerityHistoryBiz.queryOperateDetail(paramVO.getOperatorId()));
        verityHistory.setMaterialOpTimeType(0);
        verityHistory.setMaterialOpTime("");

        cfMaterialVerityHistoryBiz.insertList(Lists.newArrayList(verityHistory));
    }

    /**
     * 用于审核详情页查询详情
     * 审核详情/工单快照
     */
    @Override
    public <InfoVO> RiverDetailVO<InfoVO> getDetail(IRiverAdapter<InfoVO> adapter, int caseId, long workOrderId) {
        RiverReviewDO riverReviewDO = riverReviewService.get(caseId, adapter.getUsageType());
        boolean passed = false;
        List<Integer> rejectIds = Lists.newArrayList();
        if (riverReviewDO != null) {
            passed = riverReviewDO.getInfoStatus() == RiverStatusEnum.PASS.getValue();
            rejectIds = Lists.newArrayList(riverReviewService.parseRejectSets(riverReviewDO.getRejectDetail()));

        }
        // 若是已处理工单需要查工单快照
        if (workOrderId <= 0) {
            return new RiverDetailVO<>(adapter.getInfo(caseId), null, passed, rejectIds);
        }

        OperationRecordDTO record = commonOperationRecordClient.getLastByBizIdAndActionTypes(workOrderId, adapter.getWorkOrderSnapshotActionType());
        if (record == null) {
            return new RiverDetailVO<>(adapter.getInfo(caseId), null, passed, rejectIds);
        }
        Map<String, String> extMap = record.getExtMap();
        RiverHandleParamVO handleParam = JSON.parseObject(extMap.get(KEY_HANDLE_PARAM), RiverHandleParamVO.class);//已检查过
        InfoVO info = JSON.parseObject(extMap.get(KEY_INFO), adapter.getSnapshotClazz());//已检查过
        adapter.handleSnapshot(info);
        // 若有快照取快照里的通过状态
        passed = Objects.nonNull(handleParam) && handleParam.getHandleType() == RiverHandleParamVO.HandleType.PASS;

        return new RiverDetailVO<>(info, handleParam, passed, rejectIds);
    }

    @Override
    public <InfoVO> RiverHandleVO getHandleInfo(IRiverAdapter<InfoVO> adapter, int caseId) {
        RiverHandleVO view = new RiverHandleVO();

        RiverReviewDO riverReviewDO = riverReviewService.get(caseId, adapter.getUsageType());

        if (riverReviewDO == null) {
            return null;
        }
        long riverId = riverReviewDO.getId();

        // 材料当前状态
        int status = riverReviewDO.getInfoStatus();
        view.setStatus(status);

        // 最新一次提交时间
        OperationRecordDTO lastSubmitRecord = commonOperationRecordClient.getLastByBizIdAndActionTypes(riverId,
                OperationActionTypeEnum.RIVER_SUBMIT);
        view.setSubmitTime(Optional.ofNullable(lastSubmitRecord).map(OperationRecordDTO::getActionTime).orElse(null));

        // 查询驳回记录
        List<OperationRecordDTO> rejectRecords = commonOperationRecordClient.listByBizIdAndActionTypes(riverId,
                RiverHandleParamVO.HandleType.REJECT.getOperationActionTypeEnum());
        int rejectCount = CollectionUtils.size(rejectRecords);
        view.setRejectCount(rejectCount);

        if (status != RiverStatusEnum.PASS.getValue() && status != RiverStatusEnum.REJECT.getValue()){
            return view;
        }

        // 是否通过
        boolean passed = status == RiverStatusEnum.PASS.getValue();
        if (passed) {
            OperationRecordDTO passRecord = commonOperationRecordClient.getLastByBizIdAndActionTypes(riverId,
                    RiverHandleParamVO.HandleType.PASS.getOperationActionTypeEnum());
            view.setOperationTime(Optional.ofNullable(passRecord).map(OperationRecordDTO::getActionTime).orElse(new Date()));
            return view;
        }

        if (rejectCount > 0) {
            OperationRecordDTO lastRejectRecord = rejectRecords.get(rejectCount - 1);
            String nameWithOrg = lastRejectRecord.getNameWithOrg();
            view.setOperatorDetail(nameWithOrg);
            String rejectReason = getRejectReasonFromOperationRecord(lastRejectRecord);
            view.setRejectReason(rejectReason);
            view.setOperationTime(lastRejectRecord.getActionTime());
        }

        return view;
    }

    @Override
    public List<String> getRejectReasonList(List<Integer> rejectIds){
        List<String> lines = Lists.newArrayList();
        if (CollectionUtils.isEmpty(rejectIds)){
            return lines;
        }
        List<InitialAuditOperationItem.SortedReasonEntity> sortedReasonEntitys =
                settingsService.selectSortEntity(rejectIds);

        for (InitialAuditOperationItem.SortedReasonEntity reasonEntity : sortedReasonEntitys)  {
            int line = 0;
            for (CfRefuseReasonEntity currentEntity : reasonEntity.getSortedReasonEntities()) {
                lines.add(++line + "、" + currentEntity.getContent());
            }
        }
        return lines;
    }

    /**
     * 增信审核
     * 审核结果: 通过/驳回/停止筹款/稍后处理 （若是驳回展示驳回原因）
     * 1、请如实填写家庭经济情况
     * 2、请如实填写家庭金融资产情况
     * 呼通状态: 呼通
     * 风险案例 （若是停止筹款则展示）
     */
    @Override
    public String getRemarkContent(RiverHandleParamVO param) {
        List<String> lines = Lists.newArrayList();
        lines.add(param.getUsageTypeEnum().getUsageName());
        lines.add("审核结果:" + param.getHandleType().getMsg());

        // 驳回原因
        List<Integer> rejectIds = param.getRejectIds();
        if (param.getHandleType() == RiverHandleParamVO.HandleType.REJECT) {
            List<String> rejectReasonList = getRejectReasonList(rejectIds);
            lines.addAll(rejectReasonList);
        }
        if (param.getHandleType() == RiverHandleParamVO.HandleType.STOP_CASE) {
            lines.add("风险案例");
        }
        int callStatus = param.getCallStatus();
        if (callStatus != 0) {
            lines.add("呼通状态:" + (param.getCallStatus() == 1 ? "呼通" : "未呼通"));
        }

        return StringUtils.join(lines, "\n");
    }

    @Override
    public Response<Void> reprocess(int orderType, int caseId) {
        RiverUsageTypeEnum usageTypeEnum = RiverHandleParamVO.getUsageTypeByOrderType(orderType);
        if (usageTypeEnum == null){
            return NewResponseUtil.makeSuccess(null);
        }
        Response<Void> res = riverReviewService.save(caseId, usageTypeEnum, RiverStatusEnum.SUBMITTED, "");
        log.info("reprocess usageType:{}, caseId:{}, result:{}", usageTypeEnum, caseId, res);
        return res;
    }

    @NotNull
    private String getRejectReasonFromOperationRecord(OperationRecordDTO lastRejectRecord) {
        RiverOperationRecordModel riverHandleRecordVO = riverHandleRecordService.parse2RiverRecord(lastRejectRecord);
        List<Integer> rejectIds = riverHandleRecordVO.getHandleParam().getRejectIds();
        if (rejectIds == null) {
            return "";
        }
        List<CfRefuseReasonEntity> refuseReasonEntities = reasonEntityBiz.selectByReasonIds(Sets.newHashSet(rejectIds), null);
        StringBuilder reason = new StringBuilder();
        for (CfRefuseReasonEntity reasonEntity : refuseReasonEntities) {
            reason.append(reasonEntity.getContent()).append("\n");
        }
        return reason.toString();
    }

    @Override
    public <DetailVO> List<CfRefuseReasonTag> getRefuseReasonTags(IRiverAdapter<DetailVO> adapter) {
        int dataType = adapter.getRefuseOptionDataType();
        CfRefuseReasonEntity.RejectOptionUseSceneEnum sceneEnum = adapter.getRejectOptionUseSceneEnum();

        List<CfRefuseReasonTag> reasonTags = reasonTagBiz.selectByDataType(dataType);

        for (CfRefuseReasonTag reasonTag : reasonTags) {
            reasonTag.setEntityList(
                    entityBiz.queryRefuseEntityByDelStatus(
                            reasonTag.getId(), 0,
                            sceneEnum.getCode(),
                            true));
        }
        return reasonTags;
    }

}
