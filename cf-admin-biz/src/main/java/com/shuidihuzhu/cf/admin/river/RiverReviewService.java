package com.shuidihuzhu.cf.admin.river;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseReasonEntityBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseReasonItemBiz;
import com.shuidihuzhu.cf.client.feign.RiverReviewFeignClient;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonItem;
import com.shuidihuzhu.cf.model.river.*;
import com.shuidihuzhu.cf.vo.approve.RiverHandleParamVO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 材料状态处理
 * <AUTHOR>
 */
@Slf4j
@Service
public class RiverReviewService {

    @Autowired
    private RiverReviewFeignClient riverReviewFeignClient;

    @Autowired
    private CfRefuseReasonEntityBiz cfRefuseReasonEntityBiz;

    @Autowired
    private CfRefuseReasonItemBiz cfRefuseReasonItemBiz;

    public Response<Void> save(int caseId, RiverUsageTypeEnum usageTypeEnum, RiverStatusEnum statusEnum, String rejectDetail) {
        Response<Void> saveResp = riverReviewFeignClient.save(caseId, usageTypeEnum.getValue(), statusEnum.getValue(), rejectDetail);
        log.info("river save caseId: {}, type:{}, status:{}, response:{}, rejectDetail:{}",
                caseId, usageTypeEnum, statusEnum, saveResp, rejectDetail);
        return saveResp;
    }

    @Nullable
    public RiverReviewDO saveWithReturn(int caseId, RiverUsageTypeEnum usageTypeEnum, RiverStatusEnum statusEnum, String rejectDetail) {
        Response<RiverReviewDO> saveResp = riverReviewFeignClient.saveWithReturn(caseId, usageTypeEnum.getValue(), statusEnum.getValue(), rejectDetail);
        log.info("river save caseId: {}, type:{}, status:{}, response:{}, rejectDetail:{}",
                caseId, usageTypeEnum, statusEnum, saveResp, rejectDetail);
        if (saveResp == null || saveResp.notOk() || saveResp.getData() == null) {
            log.error("river save error caseId: {}, type:{}, status:{}, response:{}, rejectDetail:{}",
                    caseId, usageTypeEnum, statusEnum, saveResp, rejectDetail);
            return null;
        }
        return saveResp.getData();
    }

    public RiverReviewDO get(int caseId, RiverUsageTypeEnum usageTypeEnum) {
        return Optional.ofNullable(riverReviewFeignClient.getByCaseIdAndUsageType(caseId, usageTypeEnum.getValue()))
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);
    }

    /**
     * 解析驳回位置
     * @param itemIds
     * @return
     */
    private Stream<Integer> parseLocationItemIds(String itemIds) {
        String[] itemIdArr = StringUtils.split(itemIds, ",");
        return Arrays.stream(itemIdArr)
                .map(Integer::valueOf)
                .map(v -> cfRefuseReasonItemBiz.getContentById(v))
                .filter(Objects::nonNull)
                .map(CfRefuseReasonItem::getContent)
                .map(RiverRejectLocationEnum::getByName)
                .map(RiverRejectLocationEnum::getValue);
    }

    /**
     * 驳回位置为key 填充驳回原因
     * @param rejectDetail
     * @param reasonEntity
     * @param locationKey
     */
    private void fillReason(Map<Integer, List<RiverRejectReasonVO>> rejectDetail, CfRefuseReasonEntity reasonEntity, Integer locationKey) {
        List<RiverRejectReasonVO> list = rejectDetail.computeIfAbsent(locationKey, k -> Lists.newArrayList());
        RiverRejectReasonVO reasonVO = new RiverRejectReasonVO();
        reasonVO.setId(reasonEntity.getId());
        reasonVO.setMsg(reasonEntity.getContent());
        list.add(reasonVO);
    }


    public Response<Void> handle(RiverHandleParamVO param) {
        RiverHandleParamVO.HandleType handleType = param.getHandleType();
        //处理工单
        if (handleType == RiverHandleParamVO.HandleType.PASS) {
            return save(param.getCaseId(), param.getUsageTypeEnum(), RiverStatusEnum.PASS, "");
        }

        if (handleType == RiverHandleParamVO.HandleType.REJECT) {
            String rejectDetailString = JSON.toJSONString(param.getRejectDetail());
            return save(param.getCaseId(), param.getUsageTypeEnum(), RiverStatusEnum.REJECT, rejectDetailString);
        }

        if (handleType == RiverHandleParamVO.HandleType.HANDLE_LATER) {
            return NewResponseUtil.makeSuccess(null);
        }

        return NewResponseUtil.makeSuccess(null);
    }

    @NotNull
    public Set<Integer> parseRejectSets(String rejectDetailString) {
        if (StringUtils.isEmpty(rejectDetailString)) {
            return Sets.newHashSet();
        }
        TypeReference<Map<Integer, List<RiverRejectReasonVO>>> type = new TypeReference<Map<Integer, List<RiverRejectReasonVO>>>() {
        };
        Map<Integer, List<RiverRejectReasonVO>> rejectDetail = JSON.parseObject(rejectDetailString, type);//已检查过
        return rejectDetail.values()
                .stream()
                .flatMap(Collection::stream)
                .map(RiverRejectReasonVO::getId)
                .collect(Collectors.toSet());
    }

    public Map<Integer, List<RiverRejectReasonVO>> parseRejectIds2Detail(List<Integer> rejectIds) {
        Map<Integer, List<RiverRejectReasonVO>> rejectDetail = Maps.newHashMap();

        if (CollectionUtils.isEmpty(rejectIds)) {
            log.debug("refuse 驳回没有驳回id rejectIds:{}", rejectIds);
            return rejectDetail;
        }

        // 驳回子项
        List<CfRefuseReasonEntity> reasonEntities = cfRefuseReasonEntityBiz.selectByIds(rejectIds);
        cfRefuseReasonEntityBiz.frequencyPlusOne(Sets.newHashSet(rejectIds));

//             * 驳回位置为key 填充驳回原因
        for (CfRefuseReasonEntity reasonEntity : reasonEntities) {
            String itemIds = reasonEntity.getItemIds();
            parseLocationItemIds(itemIds).forEach(locationKey -> fillReason(rejectDetail, reasonEntity, locationKey));
        }
        return rejectDetail;
    }
}
