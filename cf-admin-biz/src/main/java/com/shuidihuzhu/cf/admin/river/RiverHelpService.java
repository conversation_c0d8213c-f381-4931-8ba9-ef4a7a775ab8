package com.shuidihuzhu.cf.admin.river;

import com.shuidihuzhu.cf.admin.river.impl.RiverPinKunFacadeImpl;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonTag;
import com.shuidihuzhu.cf.model.river.RiverUsageTypeEnum;
import com.shuidihuzhu.cf.vo.approve.RiverDetailVO;
import com.shuidihuzhu.cf.vo.approve.RiverHandleParamVO;
import com.shuidihuzhu.cf.vo.approve.RiverHandleVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface RiverHelpService {

    <DetailVO> Response<Void> onSubmit(IRiverAdapter<DetailVO> adapter, int caseId);

    <DetailVO> Response<Void> handle(IRiverAdapter<DetailVO> adapter, RiverHandleParamVO param);

    <InfoVO> void saveOrderSnapshot(IRiverAdapter<InfoVO> adapter, RiverHandleParamVO param);

    <DetailVO> void addRejectHistory(IRiverAdapter<DetailVO> adapter, RiverHandleParamVO paramVO);

    <DetailVO> RiverDetailVO<DetailVO> getDetail(IRiverAdapter<DetailVO> adapter, int caseId, long workOrderId);

    <DetailVO> RiverHandleVO getHandleInfo(IRiverAdapter<DetailVO> adapter, int caseId);

    List<String> getRejectReasonList(List<Integer> rejectIds);

    String getRemarkContent(RiverHandleParamVO param);

    Response<Void> reprocess(int orderType, int caseId);

    <DetailVO> List<CfRefuseReasonTag> getRefuseReasonTags(IRiverAdapter<DetailVO> riverPinKunFacade);
}
