package com.shuidihuzhu.cf.admin.util;

import com.shuidihuzhu.client.cf.search.model.CfCaseIndexSearchParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/5/29 17:13
 */
@Slf4j
public class BeanTrimUtil {

    public static <T> T trimBean(T model) {
        //int a = 1/0;
        Class<?> clazz = model.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            if (Objects.equals(field.getType(), String.class)) {
                try {
                    field.setAccessible(true);
                    String value = (String) field.get(model);
                    if (Modifier.isFinal(field.getModifiers())) {
                        continue;
                    }
                    field.set(model, StringUtils.trimToNull(value));
                } catch (IllegalAccessException e) {
                    log.error("", e);
                }
            }
        }

        return model;
    }

    public static void main(String[] args) {
        CfCaseIndexSearchParam cfCaseIndexSearchParam = new CfCaseIndexSearchParam();
        cfCaseIndexSearchParam.setConfirmedHospital(" asd df ");
        cfCaseIndexSearchParam.setContent("OA嗯我 ");
        trimBean(cfCaseIndexSearchParam);
        System.out.println(cfCaseIndexSearchParam);
    }
}
