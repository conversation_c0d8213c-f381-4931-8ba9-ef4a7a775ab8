package com.shuidihuzhu.cf.export.impl;

import com.shuidihuzhu.cf.data.platform.model.SmartExportParam;
import com.shuidihuzhu.cf.enums.export.CfWorkOrderFlowV2DownloadField;
import com.shuidihuzhu.cf.export.IDataExportStrategy;
import com.shuidihuzhu.cf.finance.enums.CfOrderDownloadEnum;
import com.shuidihuzhu.common.util.DateUtil;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2022/11/8 15:17
 * @Description:
 */
@Component
public class CfWorkOrderFlowV2DataExportStrategy extends IDataExportStrategy {
    private static final List<SmartExportParam.Header> HEADERS = new ArrayList<>(24);

    static {
        for (CfWorkOrderFlowV2DownloadField value : CfWorkOrderFlowV2DownloadField.values()) {
            HEADERS.add(SmartExportParam.Header.builder().key(value.name()).name(value.getDesc()).build());
        }
    }

    @Override
    protected List<SmartExportParam.Header> allHeaders() {
        return HEADERS;
    }

    @Override
    protected String filename() {
        return "工单数据详细统计" + DateUtil.getDate2All(new Date());
    }
}
