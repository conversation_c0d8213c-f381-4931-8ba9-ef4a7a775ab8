package com.shuidihuzhu.cf.export;

import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.data.platform.client.ExportLargeExcelClient;
import com.shuidihuzhu.cf.data.platform.model.SmartExportParam;
import com.shuidihuzhu.cf.finance.model.CfReminderWord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: wangpeng
 * @Date: 2022/11/8 15:09
 * @Description:
 */
@Slf4j
public abstract class IDataExportStrategy {
    @Qualifier("com.shuidihuzhu.cf.data.platform.client.ExportLargeExcelClient")
    @Autowired
    private ExportLargeExcelClient exportLargeExcelClient;

    /**
     * 返回全部表头
     *
     * @return -
     */
    protected abstract List<SmartExportParam.Header> allHeaders();

    /**
     * 返回导出Excel表头
     *
     * @return -
     */
    protected List<SmartExportParam.Header> headers(List<String> headerKeyFilter) {
        if (CollectionUtils.isEmpty(headerKeyFilter)) {
            return allHeaders();
        }

        return allHeaders().stream().filter(r -> headerKeyFilter.contains(r.getKey())).collect(Collectors.toList());
    }

    /**
     * 下载文件名
     *
     * @return -
     */
    protected abstract String filename();

    public CfReminderWord<Void> export(long userId, List data, List<String> headerKeyFilter) {
        if (userId <= 0) {
            return new CfReminderWord<>("userId为空");
        }
        if (CollectionUtils.isEmpty(data)) {
            return new CfReminderWord<>("导出数据为空");
        }
        List<SmartExportParam.Header> headers = headers(headerKeyFilter);
        if (CollectionUtils.isEmpty(headers)) {
            return new CfReminderWord<>("表头不存在");
        }

        SmartExportParam smartExportParam = new SmartExportParam();
        smartExportParam.setHeader(headers);
        smartExportParam.setContent(data);
        smartExportParam.setFileName(filename());
        RpcResult<Void> voidRpcResult = exportLargeExcelClient.writeExcelSmart(userId, smartExportParam);
        if (Objects.isNull(voidRpcResult) || voidRpcResult.isFail()) {
            return new CfReminderWord<>("导出失败");
        }
        return CfReminderWord.successCfReminderWord();
    }
}
