package com.shuidihuzhu.cf.export.impl;

import com.shuidihuzhu.cf.data.platform.model.SmartExportParam;
import com.shuidihuzhu.cf.enums.export.CfWorkOrderFlowV3DownloadField;
import com.shuidihuzhu.cf.enums.export.ReceivingGoodsDownloadField;
import com.shuidihuzhu.cf.export.IDataExportStrategy;
import com.shuidihuzhu.common.util.DateUtil;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/6/9 11:20 AM
 */
@Component
public class ReceivingGoodsExportStrategy extends IDataExportStrategy {

    private static final List<SmartExportParam.Header> HEADERS = new ArrayList<>(7);

    static {
        for (ReceivingGoodsDownloadField value : ReceivingGoodsDownloadField.values()) {
            HEADERS.add(SmartExportParam.Header.builder().key(value.name()).name(value.getDesc()).build());
        }
    }

    @Override
    protected List<SmartExportParam.Header> allHeaders() {
        return HEADERS;
    }

    @Override
    protected String filename() {
        return "收货信息统计" + DateUtil.getDate2All(new Date());
    }
}
