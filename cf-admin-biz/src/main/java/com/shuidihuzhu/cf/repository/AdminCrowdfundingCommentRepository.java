package com.shuidihuzhu.cf.repository;

import com.shuidihuzhu.cf.dao.tdsql.TdAdminCrowdfundingCommentDao;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingComment;
import com.shuidihuzhu.common.web.util.admin.BasicExample;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/27  14:58
 */
@Service
@RefreshScope
public class AdminCrowdfundingCommentRepository {

    @Autowired
    private TdAdminCrowdfundingCommentDao tdAdminCrowdfundingCommentDao;

    public List<CrowdfundingComment> getByPage(BasicExample basicExample) {
        return tdAdminCrowdfundingCommentDao.getByPage(basicExample);
    }

    public List<CrowdfundingComment> getCommentByParentIdFromTiDb(long parentId, int limit) {
        return tdAdminCrowdfundingCommentDao.getCommentByParentIdFromTiDb(parentId, limit);
    }

    public List<CrowdfundingComment> getCommentByUserIdFromTiDb(long userId, int limit) {
        return tdAdminCrowdfundingCommentDao.getCommentByUserIdFromTiDb(userId, limit);
    }

    public List<CrowdfundingComment> getCommentByUserIdAndTypeFromTiDb(long userId, int type, int limit) {
        return tdAdminCrowdfundingCommentDao.getCommentByUserIdAndTypeFromTiDb(userId, type, limit);
    }

    public Integer selectCountByMin(Timestamp begin, Timestamp end) {
        return tdAdminCrowdfundingCommentDao.selectCountByMin(begin, end);
    }

}
