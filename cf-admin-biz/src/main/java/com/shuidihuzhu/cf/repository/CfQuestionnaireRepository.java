package com.shuidihuzhu.cf.repository;

import com.shuidihuzhu.cf.dao.tdsql.TdCfQuestionnaireDao;
import com.shuidihuzhu.cf.model.admin.CfQuestionnaire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/27  14:58
 */
@Service
@RefreshScope
public class CfQuestionnaireRepository {

    @Autowired
    private TdCfQuestionnaireDao tdCfQuestionnaireDao;

    public List<CfQuestionnaire> getList(String qid, long userId, String qname, String name, String channel,
                                         String source, int status, String encryptMobile, String startTime, String endTime,
                                         int pageSize, boolean isPre, long anchor, long recordId, long cfQid, int size) {
        return tdCfQuestionnaireDao.getList(qid, userId, qname, name, channel, source, status, encryptMobile, startTime, endTime, pageSize, isPre, anchor, recordId, cfQid, size);
    }

    public int total(String qid, long userId, String qname, String name, String channel,
                     String source, int status, String encryptMobile, String startTime, String endTime, long recordId) {
        return tdCfQuestionnaireDao.total(qid, userId, qname, name, channel, source, status, encryptMobile, startTime, endTime, recordId);
    }

}
