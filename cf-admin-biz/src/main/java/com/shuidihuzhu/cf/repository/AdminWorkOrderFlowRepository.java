package com.shuidihuzhu.cf.repository;

import com.shuidihuzhu.cf.dao.tdsql.TdAdminWorkOrderFlowDao;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowParam;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/27  14:58
 */
@Service
@RefreshScope
public class AdminWorkOrderFlowRepository {

    @Autowired
    private TdAdminWorkOrderFlowDao tdAdminWorkOrderFlowDao;

    public int countFinishFlowByParam(AdminWorkOrderFlowParam.SearchParam searchParam) {
        return tdAdminWorkOrderFlowDao.countFinishFlowByParam(searchParam);
    }

    public List<AdminWorkOrderFlowView> selectFinishFlowByParam(AdminWorkOrderFlowParam.SearchParam searchParam) {
        return tdAdminWorkOrderFlowDao.selectFinishFlowByParam(searchParam);
    }


}
