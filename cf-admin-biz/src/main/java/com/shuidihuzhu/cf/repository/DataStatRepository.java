package com.shuidihuzhu.cf.repository;

import com.shuidihuzhu.cf.dao.tdsql.TdDataStatDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/5/27  14:58
 */
@Service
@RefreshScope
public class DataStatRepository {

    @Autowired
    private TdDataStatDao tdDataStatDao;

    public long getPaySuccessCountByTime(Date startTime, Date endTime) {
        return tdDataStatDao.getPaySuccessCountByTime(startTime, endTime);
    }

}
