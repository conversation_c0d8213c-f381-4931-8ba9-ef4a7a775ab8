package com.shuidihuzhu.cf.facade.eagle;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.client.dataservice.biservice.v1.BiServiceClient;
import com.shuidihuzhu.client.model.Response;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.Data;
import lombok.extern.log4j.Log4j;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/4/29
 */
@Slf4j
@Service
public class EagleRecordService {

    private static final String DEFAULT_PROJECT_ID = "33";
    private static final String MODULE = "HAWKEYE";

    @Resource
    private BiServiceClient biServiceClient;

    public PageResult<EagleRecord> getRecords() {
        return getRecords(DEFAULT_PROJECT_ID);
    }

    public PageResult<EagleRecord> getRecords(String projectId) {
        Date endTime = new Date();
        return getRecords(projectId, DateUtils.addDays(endTime, -7), endTime);
    }

    public PageResult<EagleRecord> getRecords(String projectId, Date updateBeginTime, Date updateEndTime) {
        return getRecords(projectId, updateBeginTime, updateEndTime, 1, 10);
    }

    public PageResult<EagleRecord> getRecords(Date updateBeginTime, Date updateEndTime, int pageNum, int pageSize) {
        return getRecords(DEFAULT_PROJECT_ID, updateBeginTime, updateEndTime, pageNum, pageSize);
    }

    public PageResult<EagleRecord> getRecords(String projectId , Date updateBeginTime, Date updateEndTime, int pageNum, int pageSize) {
        PageResult<EagleRecord> result = new PageResult();

        projectId = Optional.ofNullable(projectId).orElse(DEFAULT_PROJECT_ID);
        String startTime = Objects.isNull(updateBeginTime) ? null : DateUtil.formatDateTime(updateBeginTime);
        String endTime = Objects.isNull(updateEndTime) ? null : DateUtil.formatDateTime(updateEndTime);

        Response response = biServiceClient.getRecord(StringUtils.EMPTY, MODULE, projectId, null, startTime, endTime, pageNum, pageSize);
        log.info("鹰眼变更记录查询结果:{}", JSON.toJSONString(response));
        if (Objects.isNull(response) || Objects.isNull(response.getData())) {
            return result;
        }

        JSONObject jsonObject = (JSONObject) JSON.toJSON(response.getData());

        long total = jsonObject.getLong("totalRecord");
        result.setPageList(jsonObject.getJSONArray("list").toJavaList(EagleRecord.class));
        result.setHasNext(pageNum * pageSize < total);
        return result;

    }

    @Data
    public static class EagleRecord {
        private String user;
        private String before;
        private String after;
        private String ip;
        private String time;
    }
}
