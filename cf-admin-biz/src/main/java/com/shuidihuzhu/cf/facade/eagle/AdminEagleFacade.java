package com.shuidihuzhu.cf.facade.eagle;

import com.shuidihuzhu.cf.model.eagle.EagleResult;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2018-10-25  14:48
 * 鹰眼 ABTest 外观封装
 * https://wiki.shuiditech.com/pages/viewpage.action?pageId=17990332
 */
public interface AdminEagleFacade {

    /**
     * 注意: 鹰眼变量创建成String类型 才能使用这个方法
     * 鹰眼配置的如果是Boolean的 true 这种 不能使用此方法
     * @param userId
     * @param key
     * @param defValue
     * @return
     */
    String getStringByKey(long userId, String key, String defValue);

    /**
     * 鹰眼值中带有 \n 等换行符的需要用此方法
     *
     * @param userId
     * @param key
     * @param defValue
     * @return
     */
    String getStringByKeyWithUnescape(long userId, String key, String defValue);

    /**
     * 注意: 鹰眼变量创建成Boolean类型 才能使用这个方法
     * 鹰眼配置的如果是String的 "true" 这种 不能使用此方法
     * @param userId
     * @param key
     * @param defValue
     * @return
     */
    Boolean getBooleanByKey(long userId, String key, Boolean defValue);

    /**
     *
     * @param userId
     * @param key
     * @param defValue
     * @return
     */
    Integer getIntegerByKey(long userId, String key, Integer defValue);

    /**
     *
     * @param userId
     * @param key
     * @param defValue
     * @return
     */
    Number getNumberByKey(long userId, String key, Number defValue);


    /* ----------------------- 以下方法全部过时 ---------------------------------- */

    /**
     * 获取某实验信息
     *
     * @deprecated
     * use
     * {@link AdminEagleFacade#getStringByResult(EagleResult, String, String)}
     * or
     * {@link AdminEagleFacade#getBooleanByKey(long, String, Boolean)}
     * or
     * {@link AdminEagleFacade#getIntegerByKey(long, String, Integer)}
     * or
     * {@link AdminEagleFacade#getNumberByKey(long, String, Number)}
     * instead
     *
     * @param param
     * @return
     */
    @Deprecated
    @NotNull EagleResult get(AdminEagleParam param);

    /**
     *
     * @deprecated
     * use
     * {@link AdminEagleFacade#getStringByResult(EagleResult, String, String)}
     * or
     * {@link AdminEagleFacade#getBooleanByKey(long, String, Boolean)}
     * or
     * {@link AdminEagleFacade#getIntegerByKey(long, String, Integer)}
     * or
     * {@link AdminEagleFacade#getNumberByKey(long, String, Number)}
     * instead
     *
     * @param userId
     * @param key
     * @return
     */
    @Deprecated
    @NotNull EagleResult get(long userId, String key);

    /**
     * 获取某实验version
     *
     * @param param
     * @return
     */
    @Deprecated
    @NotNull String getVersion(AdminEagleParam param);

    /**
     * 是否是传入version
     *
     * @param param
     * @param targetVersion
     * @return
     */
    @Deprecated
    boolean isVersion(AdminEagleParam param, String targetVersion);

    /**
     * 复用一次获取的结果时适用此方法
     * @param result
     * @param key
     * @param defValue
     * @return
     */
    @Deprecated
    Boolean getBooleanByResult(EagleResult result, String key, Boolean defValue);

    /**
     * 复用一次获取的结果时适用此方法
     * @param result
     * @param key
     * @param defValue
     * @return
     */
    @Deprecated
    String getStringByResult(EagleResult result, String key, String defValue);

    /**
     * 带有 \n 等换行符的值需要用此方法
     *
     * @deprecated use {@link AdminEagleFacade#getStringByKeyWithUnescape(long, String, String)} instead
     * @param userId
     * @param key
     * @param defValue
     * @return
     */
    @Deprecated
    String getFromKeyWithUnescape(long userId, String key, String defValue);

    /**
     * @deprecated use {@link AdminEagleFacade#getStringByKey(long, String, String)} instead
     * @param userId
     * @param key
     * @param defValue
     * @return
     */
    @Deprecated
    String getFromKey(long userId, String key, String defValue);
}
