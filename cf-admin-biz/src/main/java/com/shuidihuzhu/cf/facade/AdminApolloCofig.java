package com.shuidihuzhu.cf.facade;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;

/**
 * <AUTHOR>
 * @DATE 2019/8/9
 */
public final class AdminApolloCofig {


    public final static String shouci_create_switch = "shouci_create_switch";


    public final static String per_msg_url = "per_msg_url";


    public final static String ugc_create_switch = "ugc_create_switch";


    public final static String ugc_progress_create_switch = "ugc_progress_create_switch";


    public final static String questionnaire_id = "questionnaire_id";

    public final static String questionnaire_url = "questionnaire_url";

    public final static String questionnaire_user_url = "questionnaire_user_url";

    /**
     * 筹款金额  单位分
     */
    public final static String cf_amount_fen = "cf_amount_fen";

    /**
     *  材料审核工单开关  old 生成老工单  new 生成新工单  默认old
     */
    public final static String cailiao_create_switch = "cailiao_create_switch";

    /**
     * 捐转任务捐单数量
     */
    public final static String cf_donation_count = "cf_donation_count";

    /**
     * 捐转任务2次生成时间
     */
    public final static String cf_juanzhuan_time = "cf_juanzhuan_time";

    /**
     * 捐转统计组织id
     */
    public final static String cf_juanzhuan_stat_org = "cf_juanzhuan_stat_org";

    /**
     * 捐转特种兵
     */
    public final static String cf_juanzhuan_tzb = "cf_juanzhuan_tzb";


    /**
     * 捐转渠道
     */
    public final static String cf_juanzhuan_channel = "cf_juanzhuan_channel";



    public static String getValueFromApollo(String key,String defaultValue){
        Config config = ConfigService.getAppConfig();
        String value = config.getProperty(key, defaultValue);
        return value;
    }
}
