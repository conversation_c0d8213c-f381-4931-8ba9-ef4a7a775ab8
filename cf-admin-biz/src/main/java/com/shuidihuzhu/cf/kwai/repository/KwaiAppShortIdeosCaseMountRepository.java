package com.shuidihuzhu.cf.kwai.repository;

import com.shuidihuzhu.cf.model.kwai.KwaiAppShortIdeosCaseMount;
import com.shuidihuzhu.cf.model.kwai.KwaiAppShortIdeosCaseMountDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/27  15:41
 */
public interface KwaiAppShortIdeosCaseMountRepository {


    int save(KwaiAppShortIdeosCaseMount caseMount);


    int saveBatch(List<KwaiAppShortIdeosCaseMount> caseMounts);

    int remove(KwaiAppShortIdeosCaseMount caseMount);

    int removeBatch(KwaiAppShortIdeosCaseMount caseMount);


    List<KwaiAppShortIdeosCaseMount> getList(KwaiAppShortIdeosCaseMount caseMount);

    List<String> getInfoUuidList(KwaiAppShortIdeosCaseMount caseMount);

}
