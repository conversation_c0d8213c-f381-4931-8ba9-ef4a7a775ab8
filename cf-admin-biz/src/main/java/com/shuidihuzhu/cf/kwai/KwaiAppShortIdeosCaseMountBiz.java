package com.shuidihuzhu.cf.kwai;

import com.shuidihuzhu.cf.model.kwai.KwaiAppShortIdeosCaseMountDto;
import com.shuidihuzhu.cf.model.kwai.PageInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/27  15:41
 */
public interface KwaiAppShortIdeosCaseMountBiz {


    int save(KwaiAppShortIdeosCaseMountDto dto);


    int saveBatch(List<KwaiAppShortIdeosCaseMountDto> dtoList,long adminLongUserId);

    int remove(KwaiAppShortIdeosCaseMountDto dto);

    int removeBatch(KwaiAppShortIdeosCaseMountDto dto);


    List<KwaiAppShortIdeosCaseMountDto> getList(KwaiAppShortIdeosCaseMountDto dto);



    List<String> getInfoUuidList(KwaiAppShortIdeosCaseMountDto dto);



}
