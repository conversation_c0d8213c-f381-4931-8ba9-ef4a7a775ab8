package com.shuidihuzhu.cf.kwai.impl;

import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimap;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.mapper.KwaiAppShortIdeosCaseMountMapper;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.domain.approve.AdminApproveExt;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.kwai.KwaiAppShortIdeosCaseMountBiz;
import com.shuidihuzhu.cf.kwai.repository.KwaiAppShortIdeosCaseMountRepository;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport;
import com.shuidihuzhu.cf.model.kwai.KwaiAppShortIdeosCaseMount;
import com.shuidihuzhu.cf.model.kwai.KwaiAppShortIdeosCaseMountDto;
import com.shuidihuzhu.cf.model.kwai.PageInfo;
import com.shuidihuzhu.cf.util.MobileUtil;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.ValidationException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/7/27  15:42
 */
@Service
public class KwaiAppShortIdeosCaseMountBizImpl implements KwaiAppShortIdeosCaseMountBiz {


    @Autowired
    private KwaiAppShortIdeosCaseMountRepository kwaiAppShortIdeosCaseMountRepository;

    @Autowired
    private KwaiAppShortIdeosCaseMountMapper kwaiAppShortIdeosCaseMountMapper;

    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;

    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Autowired
    private MaskUtil maskUtil;

    @Override
    public int save(KwaiAppShortIdeosCaseMountDto dto) {
        KwaiAppShortIdeosCaseMount caseMount = kwaiAppShortIdeosCaseMountMapper.toEntity(dto);
        if (Objects.isNull(caseMount)) {
            throw new ValidationException("数据不能为空");
        }

        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfo(dto.getInfoUuid());
        if (Objects.isNull(crowdfundingInfo)) {
            throw new ValidationException("案例不存在");
        }

        if (MobileUtil.illegal(dto.getMobile())) {
            throw new ValidationException("手机号格式错误");
        }

        //加密手机号
        encryptMobile(caseMount);

        return kwaiAppShortIdeosCaseMountRepository.save(caseMount);
    }

    @Override
    public int saveBatch(List<KwaiAppShortIdeosCaseMountDto> dtoList, long adminLongUserId) {

        List<KwaiAppShortIdeosCaseMount> caseMountList = kwaiAppShortIdeosCaseMountMapper.mapEntity(dtoList);
        if (CollectionUtils.isEmpty(caseMountList)) {
            throw new ValidationException("数据不能为空");
        }

        List<String> infoUuidList = caseMountList.stream().map(KwaiAppShortIdeosCaseMount::getInfoUuid).collect(Collectors.toList());

        List<CrowdfundingInfo> crowdfundingInfoList = adminCrowdfundingInfoBiz.getListByInfoUuIds(infoUuidList);
        if (CollectionUtils.isEmpty(crowdfundingInfoList)) {
            throw new ValidationException("案例不存在");
        }

        Set<String> infoUuidSet = crowdfundingInfoList.stream().map(CrowdfundingInfo::getInfoId).collect(Collectors.toSet());

        Multimap<String, String> multimap = ArrayListMultimap.create();
        for (KwaiAppShortIdeosCaseMount caseMount : caseMountList) {
            if (MobileUtil.illegal(caseMount.getMobile())) {
                multimap.put(caseMount.getMobile(), caseMount.getInfoUuid());
            }
            if (!infoUuidSet.contains(caseMount.getInfoUuid())) {
                multimap.put(caseMount.getMobile(), caseMount.getInfoUuid());
            }
        }

        caseMountList = caseMountList.stream().filter(v -> !MobileUtil.illegal(v.getMobile()) && infoUuidSet.contains(v.getInfoUuid())).collect(Collectors.toList());

        //加密手机号 + 填充创建人
        encryptMobileBatch(caseMountList, adminLongUserId);

        return kwaiAppShortIdeosCaseMountRepository.saveBatch(caseMountList);
    }

    @Override
    public int remove(KwaiAppShortIdeosCaseMountDto dto) {
        KwaiAppShortIdeosCaseMount caseMount = kwaiAppShortIdeosCaseMountMapper.toEntity(dto);
        if (Objects.isNull(caseMount)) {
            throw new ValidationException("数据不能为空");
        }

        //判断id是否为空
        caseMount.validateId();

        return kwaiAppShortIdeosCaseMountRepository.remove(caseMount);
    }

    @Override
    public int removeBatch(KwaiAppShortIdeosCaseMountDto dto) {
        KwaiAppShortIdeosCaseMount caseMount = kwaiAppShortIdeosCaseMountMapper.toEntity(dto);
        if (Objects.isNull(caseMount)) {
            throw new ValidationException("数据不能为空");
        }

        //判断ids是否为空
        caseMount.splitter();

        return kwaiAppShortIdeosCaseMountRepository.removeBatch(caseMount);
    }

    @Override
    public List<KwaiAppShortIdeosCaseMountDto> getList(KwaiAppShortIdeosCaseMountDto dto) {

        KwaiAppShortIdeosCaseMount caseMount = kwaiAppShortIdeosCaseMountMapper.toEntity(dto);
        if (Objects.isNull(caseMount)) {
            throw new ValidationException("数据不能为空");
        }

        //加密手机号
        encryptMobile(caseMount);

        List<KwaiAppShortIdeosCaseMount> list = kwaiAppShortIdeosCaseMountRepository.getList(caseMount);
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }

        decryptMobileBatch(list);

        Map<String, Object> pageMap = list.stream().map(KwaiAppShortIdeosCaseMount::getPageMap).findFirst().orElse(null);

        List<KwaiAppShortIdeosCaseMountDto> caseMountDtoList = kwaiAppShortIdeosCaseMountMapper.mapDto(list);
        for (KwaiAppShortIdeosCaseMountDto caseMountDto : caseMountDtoList) {
            caseMountDto.setMobileMask(maskUtil.buildByEncryptPhone(caseMountDto.getEncryptMobile()));
            caseMountDto.setMobile(null);
            caseMountDto.setPageMap(pageMap);
        }

        return caseMountDtoList;
    }

    @Override
    public List<String> getInfoUuidList(KwaiAppShortIdeosCaseMountDto dto) {

        KwaiAppShortIdeosCaseMount caseMount = kwaiAppShortIdeosCaseMountMapper.toEntity(dto);
        if (Objects.isNull(caseMount)) {
            throw new ValidationException("数据不能为空");
        }

        //判断手机号是否为空
        caseMount.validateEncryptMobile();

        List<String> infoUuidList = kwaiAppShortIdeosCaseMountRepository.getInfoUuidList(caseMount);
        if (CollectionUtils.isEmpty(infoUuidList)) {
            return null;
        }
        return infoUuidList;
    }


    private void encryptMobile(KwaiAppShortIdeosCaseMount caseMount) {
        String encryptMobile = oldShuidiCipher.aesEncrypt(caseMount.getMobile());
        caseMount.setEncryptMobile(encryptMobile);
    }

    private void encryptMobileBatch(List<KwaiAppShortIdeosCaseMount> caseMountList, long adminLongUserId) {
        for (KwaiAppShortIdeosCaseMount caseMount : caseMountList) {
            String encryptMobile = oldShuidiCipher.aesEncrypt(caseMount.getMobile());
            caseMount.setEncryptMobile(encryptMobile);
            if (adminLongUserId > 0L) {
                caseMount.setOperatorId(adminLongUserId);
            }
        }

    }

    private void decryptMobileBatch(List<KwaiAppShortIdeosCaseMount> caseMountList) {
        for (KwaiAppShortIdeosCaseMount caseMount : caseMountList) {
            AuthRpcResponse<String> authRpcResponse = seaAccountClientV1.getNameByLongUserId(caseMount.getOperatorId());
            caseMount.setOperatorName(authRpcResponse.getResult());
            caseMount.setCreateTimeDesc(DateUtil.formatDateTime(caseMount.getCreateTime()));
        }

    }


}
