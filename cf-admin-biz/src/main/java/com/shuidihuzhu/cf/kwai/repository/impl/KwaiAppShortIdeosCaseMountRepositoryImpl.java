package com.shuidihuzhu.cf.kwai.repository.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.mapper.KwaiAppShortIdeosCaseMountMapper;
import com.shuidihuzhu.cf.dao.kwai.KwaiAppShortIdeosCaseMountDao;
import com.shuidihuzhu.cf.kwai.repository.KwaiAppShortIdeosCaseMountRepository;
import com.shuidihuzhu.cf.model.kwai.KwaiAppShortIdeosCaseMount;
import com.shuidihuzhu.cf.model.kwai.KwaiAppShortIdeosCaseMountDo;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.common.web.util.admin.PageUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/7/29  11:00
 */
@Repository
@RefreshScope
public class KwaiAppShortIdeosCaseMountRepositoryImpl implements KwaiAppShortIdeosCaseMountRepository {

    @Resource
    private KwaiAppShortIdeosCaseMountDao kwaiAppShortIdeosCaseMountDao;

    @Autowired
    private KwaiAppShortIdeosCaseMountMapper kwaiAppShortIdeosCaseMountMapper;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Override
    public int save(KwaiAppShortIdeosCaseMount caseMount) {
        KwaiAppShortIdeosCaseMountDo caseMountDo = kwaiAppShortIdeosCaseMountMapper.toDo(caseMount);
        return kwaiAppShortIdeosCaseMountDao.add(caseMountDo);
    }

    @Override
    public int saveBatch(List<KwaiAppShortIdeosCaseMount> caseMounts) {
        List<KwaiAppShortIdeosCaseMountDo> caseMountDoList = kwaiAppShortIdeosCaseMountMapper.mapDo(caseMounts);
        int res = 0;
        for (KwaiAppShortIdeosCaseMountDo caseMountDo : caseMountDoList) {
            int add = kwaiAppShortIdeosCaseMountDao.add(caseMountDo);
            res += add;
        }

        return res;
    }

    @Override
    public int remove(KwaiAppShortIdeosCaseMount caseMount) {

        return kwaiAppShortIdeosCaseMountDao.delete(caseMount.getId());
    }

    @Override
    public int removeBatch(KwaiAppShortIdeosCaseMount caseMount) {

        return kwaiAppShortIdeosCaseMountDao.deleteBatch(caseMount.getIdList());
    }

    @Override
    public List<KwaiAppShortIdeosCaseMount> getList(KwaiAppShortIdeosCaseMount caseMount) {
        PageHelper.startPage(caseMount.getCurrent(), caseMount.getPageSize());
        List<KwaiAppShortIdeosCaseMountDo> doList = kwaiAppShortIdeosCaseMountDao.getList(caseMount.getInfoUuid(), caseMount.getEncryptMobile());
        if (CollectionUtils.isEmpty(doList)) {
            return Lists.newArrayList();
        }

        Map<String, Object> map = PageUtil.transform2PageMap(doList);

        List<KwaiAppShortIdeosCaseMount> list = kwaiAppShortIdeosCaseMountMapper.fromData(doList);
        for (KwaiAppShortIdeosCaseMount mount : list) {
            mount.setPageMap(map);
        }

        return list;
    }

    @Override
    public List<String> getInfoUuidList(KwaiAppShortIdeosCaseMount caseMount) {
        return kwaiAppShortIdeosCaseMountDao.getInfoUuidList(caseMount.getEncryptMobile());
    }
}
