package com.shuidihuzhu.cf.von;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.workorder.helper.convertor.VonCommonInfoAutoConvertor;
import com.shuidihuzhu.client.cf.workorder.helper.model.OrderInfoVO;
import com.shuidihuzhu.client.cf.workorder.read.BasicWorkOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @Author: wangpeng
 * @Date: 2023/2/23 16:20
 * @Description:
 */
@Service
public class TargetAmountReasonableAuditVonConvertor extends VonCommonInfoAutoConvertor<TargetAmountReasonableAuditVonView, Object> {

    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Override
    public OrderInfoVO<TargetAmountReasonableAuditVonView, Object> toView(BasicWorkOrder basicWorkOrder) {
        OrderInfoVO<TargetAmountReasonableAuditVonView, Object> targetAmountReasonableAuditVonViewObjectOrderInfoVO = super.toView(basicWorkOrder);
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(basicWorkOrder.getCaseId());
        if (Objects.nonNull(crowdfundingInfo)) {
            TargetAmountReasonableAuditVonView bizExtInfo = targetAmountReasonableAuditVonViewObjectOrderInfoVO.getBizExtInfo();
            bizExtInfo.setTitle(crowdfundingInfo.getTitle());
            bizExtInfo.setContent(crowdfundingInfo.getContent());
        }
        return targetAmountReasonableAuditVonViewObjectOrderInfoVO;
    }

    @Override
    protected Class<TargetAmountReasonableAuditVonView> getOrderBizExtInfoClazz() {
        return TargetAmountReasonableAuditVonView.class;
    }
}
