package com.shuidihuzhu.cf.von;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.shuidihuzhu.cf.dao.approve.CfServiceSubsidyRecordDao;
import com.shuidihuzhu.cf.delegate.OrganizationDelegate;
import com.shuidihuzhu.cf.domain.approve.SubsidyApplyRecordDO;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.service.workorder.WorkOrderRemarkService;
import com.shuidihuzhu.client.cf.admin.model.RefuseModel;
import com.shuidihuzhu.client.cf.admin.model.ServiceChargeSubsidyVonHandleParam;
import com.shuidihuzhu.client.cf.admin.model.SubsidyConst;
import com.shuidihuzhu.client.cf.workorder.ext.WorkOrderExtFeignClient;
import com.shuidihuzhu.client.cf.workorder.helper.adapter.BaseVonAdapter;
import com.shuidihuzhu.client.cf.workorder.helper.convertor.IVonInfoConvertor;
import com.shuidihuzhu.client.cf.workorder.model.HandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.read.BasicWorkOrder;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.constant.CfClientMQTopicCons;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 服务费补贴工单自定义Adapter
 * @Author: panghairui
 * @Date: 2023/10/16 4:31 PM
 */
@Slf4j
@Service
public class ServiceChargeSubsidyAdapter extends BaseVonAdapter<ServiceChargeSubsidyVonHandleParam> {

    @Resource
    private ServiceChargeSubsidyConvertor serviceChargeSubsidyConvertor;

    @Resource
    private WorkOrderExtFeignClient workOrderExtFeignClient;

    @Resource
    private WorkOrderRemarkService workOrderRemarkService;

    @Resource
    private CfServiceSubsidyRecordDao cfServiceSubsidyRecordDao;

    @Resource
    private Producer producer;

    @Resource
    private OrganizationDelegate organizationDelegate;

    @Override
    protected Response<Object> onOrderHandleStartCheck(HandleOrderParam param, BasicWorkOrder orderInfo) {

        if (HandleResultEnum.getFromType(param.getHandleResult()) == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        return NewResponseUtil.makeSuccess();
    }

    @Override
    protected Response<Object> onOrderHandleSuccess(HandleOrderParam<ServiceChargeSubsidyVonHandleParam> param, BasicWorkOrder orderInfo) {

        log.info("ServiceChargeSubsidyAdapter onOrderHandleSuccess param {}", JSONObject.toJSONString(param));

        // 稍后处理不走下面逻辑
        if (param.getHandleResult() == HandleResultEnum.later_doing.getType()) {
            return NewResponseUtil.makeSuccess();
        }

        // 保存驳回项及扩展信息
        saveWorkOrderExt(param);

        // 备注文案
        String comment = getComment(param);
        String prefix = "服务费退回补贴审核【工单id:" + param.getWorkOrderId() + "】";

        // 保存申请记录
        saveApplyRecord(param, comment, prefix);

        // 发消息通知审核状态
        ServiceChargeSubsidyVonHandleParam subsidyVonHandleParam = buildMqParam(param.getExtParam(), orderInfo);
        subsidyVonHandleParam.setHandleResult(param.getHandleResult());
        sendWorkOrderProcessMq(subsidyVonHandleParam);

        return NewResponseUtil.makeSuccess();
    }

    public static void main(String[] args) {
        JSONArray jsonArray = (JSONArray) JSONObject.parse("[{\"refuseId\":\"5\",\"refuseReason\":\"关系证明有误，患者或持证人与其姓名或身份证件号码不一致；\"},{\"refuseId\":\"4\",\"refuseReason\":\"材料不足，【最低生活保障证】或【农村五保供养证】持证人非患者本人。若患者家庭成员为五保户、低保户持证人，请补充上传患者与持证人关系证明。\"},{\"refuseId\":\"3\",\"refuseReason\":\"证件日期有误，请重新上传带有患者姓名的【最低生活保障证】或【农村五保供养证】证明材料，要求日期需要早于案例发起时间。\"}]");
        for (int i = 0; i < jsonArray.size(); i++) {
            Gson gson = new Gson();
            RefuseModel refuseModel = gson.fromJson(jsonArray.getString(i), RefuseModel.class);
            System.out.println(1);
        }
    }

    private String getComment(HandleOrderParam<ServiceChargeSubsidyVonHandleParam> param) {

        ServiceChargeSubsidyVonHandleParam serviceChargeSubsidyVonHandleParam = param.getExtParam();

        List<RefuseModel> refuseModels = Lists.newArrayList();
        JSONArray jsonArray = (JSONArray) JSONObject.parse(serviceChargeSubsidyVonHandleParam.getRefuseModel());
        if (CollectionUtils.isNotEmpty(jsonArray)) {
            for (int i = 0; i < jsonArray.size(); i++) {
                Gson gson = new Gson();
                RefuseModel refuseModel = gson.fromJson(jsonArray.getString(i), RefuseModel.class);
                refuseModels.add(refuseModel);
            }
        }

        StringBuilder comment;
        if (HandleResultEnum.getFromType(param.getHandleResult()) == HandleResultEnum.audit_pass) {
            comment = new StringBuilder("工单审核通过\n");
        } else if (HandleResultEnum.getFromType(param.getHandleResult()) == HandleResultEnum.audit_reject) {
            comment = new StringBuilder("工单审核驳回\n驳回具体原因:\n");
            for (RefuseModel refuseModel : refuseModels) {
                comment.append(refuseModel.getRefuseReason()).append("\n");
            }
        } else {
            comment = new StringBuilder("工单被处理，处理状态:\n" + HandleResultEnum.getFromType(param.getHandleResult()).getMsg());
        }
        return comment.toString();
    }

    private void saveApplyRecord(HandleOrderParam<ServiceChargeSubsidyVonHandleParam> param, String comment, String prefix) {

        String remark = prefix + "：" + comment;
        String auditResult = HandleResultEnum.getMsgByType(param.getHandleResult());
        String organization = organizationDelegate.getSimpleOrganization((int) param.getUserId());

        // 保存申请记录
        SubsidyApplyRecordDO subsidyApplyRecordDO = SubsidyApplyRecordDO.builder()
                .caseId((long) param.getCaseId())
                .workOrderId(param.getWorkOrderId())
                .auditResult(auditResult)
                .handleRemark(remark)
                .operatorId(param.getUserId())
                .organization(organization)
                .build();
        cfServiceSubsidyRecordDao.insertSubsidyApplyRecord(subsidyApplyRecordDO);

        // 保存操作记录
        workOrderRemarkService.addFlowComment(param.getCaseId(), (int) param.getUserId(),
                param.getWorkOrderId(),
                prefix, comment);
    }

    private ServiceChargeSubsidyVonHandleParam buildMqParam(ServiceChargeSubsidyVonHandleParam subsidyVonHandleParam, BasicWorkOrder orderInfo) {
        return subsidyVonHandleParam;
    }

    @Override
    public Integer getOrderTypeCode() {
        return 121;
    }

    @Override
    protected Class<ServiceChargeSubsidyVonHandleParam> getOrderHandlerExtParamClass() {
        return ServiceChargeSubsidyVonHandleParam.class;
    }

    @Override
    public IVonInfoConvertor<?> getInfoConvertor() {
        return serviceChargeSubsidyConvertor;
    }

    private void sendWorkOrderProcessMq(ServiceChargeSubsidyVonHandleParam serviceChargeSubsidyVonHandleParam) {
        Message msg =  new Message(CfClientMQTopicCons.CF, CfClientMQTagCons.CF_SERVICE_SUBSIDY_PROCESS,
                "" + System.currentTimeMillis(),
                serviceChargeSubsidyVonHandleParam);
        MessageResult result = producer.send(msg);
        log.info("服务费补贴工单处理 msg:{} result:{}", msg, result);
    }

    private void saveWorkOrderExt(HandleOrderParam<ServiceChargeSubsidyVonHandleParam> param) {

        if (Objects.isNull(param.getExtParam())) {
            log.info("ServiceChargeSubsidyAdapter saveWorkOrderExt ext is null {}", param.getWorkOrderId());
            return;
        }

        List<WorkOrderExt> workOrderExtList = Lists.newArrayList(
                WorkOrderExt.create(param.getWorkOrderId(), SubsidyConst.refuseModel, param.getExtParam().getRefuseModel())
        );

        workOrderExtFeignClient.saveByList(param.getWorkOrderId(), workOrderExtList);

    }

}
