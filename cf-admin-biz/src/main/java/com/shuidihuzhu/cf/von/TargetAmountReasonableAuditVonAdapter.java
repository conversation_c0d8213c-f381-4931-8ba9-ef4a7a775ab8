package com.shuidihuzhu.cf.von;

import com.shuidihuzhu.cf.biz.admin.UserCommentBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.BackgroundLogEnum;
import com.shuidihuzhu.cf.enums.admin.UserCommentSourceEnum;
import com.shuidihuzhu.cf.model.admin.UserComment;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.CfInitialAuditHandleV2ConsumerService;
import com.shuidihuzhu.client.cf.workorder.helper.adapter.BaseVonAdapter;
import com.shuidihuzhu.client.cf.workorder.helper.convertor.IVonInfoConvertor;
import com.shuidihuzhu.client.cf.workorder.model.HandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.cf.workorder.read.BasicWorkOrder;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Author: wangpeng
 * @Date: 2023/2/23 15:56
 * @Description: 目标金额审核工单相关操作
 */
@Service
public class TargetAmountReasonableAuditVonAdapter extends BaseVonAdapter<TargetAmountReasonableAuditVonHandleParam> {

    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Resource
    private UserCommentBiz userCommentBiz;

    @Resource
    private AdminApproveService adminApproveService;

    @Resource
    private CfInitialAuditHandleV2ConsumerService cfInitialAuditHandleV2ConsumerService;

    @Resource
    private TargetAmountReasonableAuditVonConvertor targetAmountReasonableAuditVonConvertor;


    @Override
    protected Response<Object> onOrderHandleStartCheck(HandleOrderParam param, BasicWorkOrder orderInfo) {
        if (HandleResultEnum.getFromType(param.getHandleResult()) == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(param.getCaseId());
        if (Objects.isNull(crowdfundingInfo)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        return NewResponseUtil.makeSuccess();
    }

    @Override
    protected Response<Object> onOrderHandleSuccess(HandleOrderParam<TargetAmountReasonableAuditVonHandleParam> param, BasicWorkOrder orderInfo) {
        HandleResultEnum handleResultEnum = HandleResultEnum.getFromType(param.getHandleResult());
        if (HandleResultEnum.later_doing == handleResultEnum) {
            return NewResponseUtil.makeSuccess();
        }
        String remark = param.getExtParam().getRemark();
        UserComment comment = new UserComment();
        comment.setOperatorId(param.getUserId());
        comment.setCaseId(param.getCaseId());
        comment.setCommentSource(UserCommentSourceEnum.INITIAL_AUDIT.getCode());
        comment.setCommentType(UserCommentSourceEnum.CommentType.INITIAL_AUDIT_PASS.getCode());
        comment.setOperateMode(handleResultEnum.getMsg());
        comment.setWorkOrderId(param.getWorkOrderId());
        comment.setComment(remark);
        comment.setOperateDesc("目标金额" + handleResultEnum.getMsg());
        userCommentBiz.insert(comment);

        adminApproveService.addComment(param.getExtParam().getInfoId(), remark, (int) param.getUserId(), "", BackgroundLogEnum.REMARK);

        cfInitialAuditHandleV2ConsumerService.saveInitialAuditSnapshotV2(param.getWorkOrderId(), param.getCaseId());

        return NewResponseUtil.makeSuccess();
    }

    //测试
    @Nullable
    @Override
    public Integer getOrderTypeCode() {
        return 1000000;
    }

    @Override
    public IVonInfoConvertor<?> getInfoConvertor() {
        return targetAmountReasonableAuditVonConvertor;
    }

    @Override
    protected Class<TargetAmountReasonableAuditVonHandleParam> getOrderHandlerExtParamClass() {
        return TargetAmountReasonableAuditVonHandleParam.class;
    }
}
