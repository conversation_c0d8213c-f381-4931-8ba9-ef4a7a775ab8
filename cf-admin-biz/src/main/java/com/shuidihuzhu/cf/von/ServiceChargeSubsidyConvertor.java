package com.shuidihuzhu.cf.von;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.admin.CfChannelService;
import com.shuidihuzhu.client.cf.clewtrack.model.CfUserInvitedLaunchCaseRecordModel;
import com.shuidihuzhu.client.cf.clewtrack.model.ChannelRefine;
import com.shuidihuzhu.client.cf.clewtrack.param.ChannelRefineDTO;
import com.shuidihuzhu.client.cf.growthtool.client.CfChannelFeignClient;
import com.shuidihuzhu.client.cf.workorder.helper.convertor.VonCommonInfoAutoConvertor;
import com.shuidihuzhu.client.cf.workorder.helper.model.OrderInfoVO;
import com.shuidihuzhu.client.cf.workorder.read.BasicWorkOrder;
import com.shuidihuzhu.common.web.model.Response;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/10/16 4:39 PM
 */
@Service
public class ServiceChargeSubsidyConvertor extends VonCommonInfoAutoConvertor<ServiceChargeSubsidyVonView, Object> {

    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;
    @Resource
    private CfChannelService cfChannelService;

    @Override
    public OrderInfoVO<ServiceChargeSubsidyVonView, Object> toView(BasicWorkOrder basicWorkOrder) {
        OrderInfoVO<ServiceChargeSubsidyVonView, Object> serviceChargeSubsidyVonViewObjectOrderInfoVO = super.toView(basicWorkOrder);
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(basicWorkOrder.getCaseId());
        if (Objects.nonNull(crowdfundingInfo)) {
            ServiceChargeSubsidyVonView bizExtInfo = serviceChargeSubsidyVonViewObjectOrderInfoVO.getBizExtInfo();
            bizExtInfo.setTitle(crowdfundingInfo.getTitle());
            bizExtInfo.setCaseUuid(crowdfundingInfo.getInfoId());
            bizExtInfo.setChannelStr(getChannelStr(crowdfundingInfo));
        }
        return serviceChargeSubsidyVonViewObjectOrderInfoVO;
    }

    @Override
    protected Class<ServiceChargeSubsidyVonView> getOrderBizExtInfoClazz() {
        return ServiceChargeSubsidyVonView.class;
    }

    private String getChannelStr(CrowdfundingInfo crowdfundingInfo) {

        ChannelRefine.ChannelRefineResuleEnum channelRefineResuleEnum = cfChannelService.getCfChannel(crowdfundingInfo);
        if (Objects.isNull(channelRefineResuleEnum)) {
            return "";
        }

        return channelRefineResuleEnum.getChannelDesc();
    }
}
