package com.shuidihuzhu.cf.diff;

import brave.Tracing;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @date 2019/3/1
 */

@Slf4j
@Aspect
//@Component(value="adminDiffAspect")
public class DiffAspect {

    @Autowired
    private Tracing tracing;

    @Resource
    private MeterRegistry meterRegistry;

    @Pointcut("@annotation(com.shuidihuzhu.cf.diff.Diff)")
    public void diffPointCut() {
    }


    @Around("diffPointCut()")
    public Object methodsAnnotatedWithDiff(final ProceedingJoinPoint joinPoint) throws Throwable {
        // 首先执行原方法
        Object result = joinPoint.proceed();

        // 新方法执行逻辑
        diffMethodHandle(joinPoint, result);

        return result;
    }

    public void diffMethodHandle(final ProceedingJoinPoint joinPoint, Object result) {

        // 全局开关控制
        try {
            MetaHolder metaHolder = DiffUtil.createMetaHolder(joinPoint);

            //没有找到新方法直接退出
            if (metaHolder.getDiffMethod() == null) {
                return;
            }

            //diff功能流量控制

            String methodName = DiffUtil.getMethodName(metaHolder.getMethod());
            //使用methodName做为线程池的唯一标识
            ThreadPoolExecutor executor = ThreadPoolUtil.getThreadPool(methodName);

            DiffRunnable diffRunnable = new DiffRunnable(result, metaHolder, meterRegistry);
            executor.submit(tracing.currentTraceContext().wrap(diffRunnable));
        } catch (Throwable e) {
            log.error("[DiffUtil.diffMethodHandle] exception.", e);
        }
    }
}
