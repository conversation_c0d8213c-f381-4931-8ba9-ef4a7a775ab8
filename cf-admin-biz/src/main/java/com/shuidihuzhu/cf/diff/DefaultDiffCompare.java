package com.shuidihuzhu.cf.diff;

import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.json.JSONObject;
import org.skyscreamer.jsonassert.JSONCompare;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.JSONCompareResult;

/**
 * <AUTHOR>
 * @date 2019/3/1
 */
@Slf4j
public class DefaultDiffCompare implements IDiffCompare {

    @Override
    public DiffResult compare(Object expected, Object actual) {
        return compare(expected, actual, JSONCompareMode.STRICT_ORDER);
    }

    public DiffResult compare(Object expected, Object actual, JSONCompareMode mode) {
        DiffResult diffResult = new DiffResult();

        if (expected == null || actual == null) {
            diffResult.setMessage("expected or actual is null");
            return diffResult;
        }

        try {
            String expectedjson = JSON.toJSONString(expected, SerializerFeature.WriteMapNullValue);
            JSONObject expectedJsonObject = new JSONObject(expectedjson);
            String actualjson = JSON.toJSONString(actual, SerializerFeature.WriteMapNullValue);
            JSONObject actualJsonObject = new JSONObject(actualjson);

            JSONCompareResult jsonCompareResult = JSONCompare.compareJSON(expectedJsonObject, actualJsonObject, mode);

            diffResult.setPassed(jsonCompareResult.passed());
            diffResult.setMessage(jsonCompareResult.getMessage());
        } catch (Exception e) {
            log.error("[DefaultDiffCompare.compare] exception.", e);
        }

        return diffResult;
    }
}
