package com.shuidihuzhu.cf.diff;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.extern.slf4j.Slf4j;

import org.json.JSONArray;
import org.json.JSONObject;
import org.skyscreamer.jsonassert.JSONCompare;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.JSONCompareResult;


/**
 * @author: wanghui
 * @create: 2019/3/5 8:24 PM
 */
@Slf4j
public class CrowdfundingOrderFeignSearchCompare implements IDiffCompare {
    private final static String JSONARRAY_PREFIX = "[";
    private final static String JSONOBJECT_PREFIX = "{";
    @Override
    public DiffResult compare(Object expected, Object actual) {
        return compare(expected, actual, JSONCompareMode.STRICT_ORDER);
    }

    public DiffResult compare(Object expected, Object actual, JSONCompareMode mode) {
        DiffResult diffResult = new DiffResult();
        log.debug(this.getClass().getSimpleName()+"  param expected:{},actual:{}",expected,actual);
        if (expected == null || actual == null) {
            diffResult.setPassed(expected==actual);
            diffResult.setMessage("expected or actual is null");
            return diffResult;
        }
        String expectedjson = JSON.toJSONString(expected, SerializerFeature.WriteMapNullValue);
        String actualjson = JSON.toJSONString(actual, SerializerFeature.WriteMapNullValue);
        if (expectedjson.equals(actualjson)) {
            diffResult.setPassed(true);
            return diffResult;
        }
        JSONCompareResult jsonCompareResult = new JSONCompareResult();
        try {
            if (expectedjson.startsWith(JSONARRAY_PREFIX) && actualjson.startsWith(JSONARRAY_PREFIX)){
                JSONArray expectedJsonObject = new JSONArray(expectedjson);
                JSONArray actualJsonObject = new JSONArray(actualjson);
                jsonCompareResult = JSONCompare.compareJSON(expectedJsonObject, actualJsonObject, mode);
            }else if (expectedjson.startsWith(JSONOBJECT_PREFIX) && actualjson.startsWith(JSONOBJECT_PREFIX)){
                JSONObject expectedJsonObject = new JSONObject(expectedjson);
                JSONObject actualJsonObject = new JSONObject(actualjson);
                jsonCompareResult = JSONCompare.compareJSON(expectedJsonObject, actualJsonObject, mode);
            }else {
                if (!expectedjson.equals(actualjson)){
                    jsonCompareResult.fail("expected:"+expected+"  actual:"+actual);
                }
            }
        } catch (Exception e) {
            if (expectedjson.length()!=actualjson.length()){
                jsonCompareResult.fail("expected:"+expected+"  actual:"+actual);
            }
        }
        diffResult.setPassed(jsonCompareResult.passed());
        diffResult.setMessage(jsonCompareResult.getMessage());
        return diffResult;
    }
}
