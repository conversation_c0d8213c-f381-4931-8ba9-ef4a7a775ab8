package com.shuidihuzhu.cf.diff;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.vo.crowdfunding.WorkOrderFirstApprove;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/3/26
 */
@Slf4j
public class GetFirstUGCCompare implements IDiffCompare {

    @Override
    public DiffResult compare(Object expected, Object actual) {
        DiffResult diffResult = new DiffResult();

        if (expected == null || actual == null) {
            diffResult.setMessage("expected or actual is null");
            return diffResult;
        }

        try {
            List<WorkOrderFirstApprove> expectedWorkOrders = (List<WorkOrderFirstApprove>) expected;
            Pair<Long, List<WorkOrderFirstApprove>> pair = (Pair<Long, List<WorkOrderFirstApprove>>) actual;
            List<WorkOrderFirstApprove> actualInfos = pair.getRight();

            List<Integer> expectIds = expectedWorkOrders.stream().map(x -> x.getWorkOrderId()).collect(Collectors.toList());
            List<Integer> actualIds = actualInfos.stream().map(x -> x.getWorkOrderId()).collect(Collectors.toList());

            if (expectIds.size() != actualIds.size()) {
                diffResult.setMessage("结果长度不一致. expectIds:" + expectIds + ", actualIds:" + actualIds);
                return diffResult;
            }

            List<Integer> diff = Lists.newArrayList(expectIds);
            diff.removeAll(actualIds);

            if (CollectionUtils.isNotEmpty(diff)) {
                diffResult.setMessage("结果内容不一致. diff:" + diff + ", expectIds:" + expectIds + ", actualIds:" + actualIds);
                return diffResult;
            }
        } catch (Exception e) {
            log.error("[GetFirstUGCCompare.compare] exception.", e);
        }
        diffResult.setPassed(true);
        return diffResult;
    }
}
