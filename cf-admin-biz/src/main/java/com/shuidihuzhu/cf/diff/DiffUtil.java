package com.shuidihuzhu.cf.diff;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.reflect.MethodSignature;

/**
 * <AUTHOR>
 * @date 2019/3/1
 */

@Slf4j
public class DiffUtil {

    private static final ConcurrentHashMap<String, IDiffCompare> diffComparePools = new ConcurrentHashMap<>();

    private static final String defaultDiffCompare = "com.shuidihuzhu.cf.diff.DefaultDiffCompare";

    public static IDiffCompare getDiffCompare(String diffCompareName) {

        if (StringUtils.isBlank(diffCompareName)) {
            diffCompareName = defaultDiffCompare;
        }

        IDiffCompare diffCompare = diffComparePools.get(diffCompareName);
        if (diffCompare != null) {
            return diffCompare;
        }

        synchronized (DiffUtil.class) {
            if (!diffComparePools.containsKey(diffCompareName)) {
                diffComparePools.put(diffCompareName, createDiffCompare(diffCompareName));
            }
        }

        return diffComparePools.get(diffCompareName);
    }

    private static IDiffCompare createDiffCompare(String diffCompareName) {
        IDiffCompare diffCompare = null;

        if (StringUtils.equals(defaultDiffCompare, diffCompareName)) {
            diffCompare = new DefaultDiffCompare();
            return diffCompare;
        }

        try {
            Class cls = Class.forName(diffCompareName);
            diffCompare = IDiffCompare.class.isAssignableFrom(cls) ? (IDiffCompare) cls.newInstance() : new DefaultDiffCompare();
        } catch (Exception e) {
            diffCompare = new DefaultDiffCompare();
        }

        return diffCompare;
    }

    public static MetaHolder createMetaHolder(final ProceedingJoinPoint joinPoint) {
        Method method = getMethodFromTarget(joinPoint);
        Object obj = joinPoint.getTarget();
        Object[] args = joinPoint.getArgs();
        Object proxy = joinPoint.getThis();
        Diff diff = method.getAnnotation(Diff.class);

        MetaHolder.Builder builder = MetaHolder.builder()
                .args(args).method(method).obj(obj).proxyObj(proxy)
                .joinPoint(joinPoint).diff(diff);

        setDiffMethod(builder, obj.getClass(), method);

        return builder.build();
    }

    private static MetaHolder.Builder setDiffMethod(MetaHolder.Builder builder, Class<?> declaringClass,
                                                    Method commandMethod) {

        String diffMethodName = commandMethod.getAnnotation(Diff.class).diffMethod();

        // 如果diffMethodName为空，直接跳过
        if (StringUtils.isEmpty(diffMethodName)) {
            return builder;
        }

        Class<?>[] parameterTypes = commandMethod.getParameterTypes();
        Method diffMethod = getMethod(declaringClass, diffMethodName, parameterTypes);
        builder.diffMethod(diffMethod);

        return builder;
    }

    private static Method getMethod(Class<?> type, String name, Class<?>... parameterTypes) {
        Method[] methods = type.getDeclaredMethods();
        for (Method method : methods) {
            if (method.getName().equals(name) && Arrays.equals(method.getParameterTypes(), parameterTypes)) {
                return method;
            }
        }
        Class<?> superClass = type.getSuperclass();
        if (superClass != null && !superClass.equals(Object.class)) {
            return getMethod(superClass, name, parameterTypes);
        } else {
            return null;
        }
    }


    public static String getMethodName(Method method) {

        if (method == null) {
            return StringUtils.EMPTY;
        }

        StringBuilder methodName = new StringBuilder();
        methodName.append(method.getDeclaringClass().getName()).append(".").append(method.getName());
        return methodName.toString();
    }

    public static Method getMethodFromTarget(JoinPoint joinPoint) {
        Method method = null;
        if (joinPoint.getSignature() instanceof MethodSignature) {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();

            method = getDeclaredMethod(joinPoint.getTarget().getClass(), signature.getName(),
                    getParameterTypes(joinPoint));
        }
        return method;
    }

    /**
     * Gets declared method from specified type by mame and parameters types.
     *
     * @param type           the type
     * @param methodName     the name of the method
     * @param parameterTypes the parameter array
     * @return a {@link Method} object or null if method doesn't exist
     */
    public static Method getDeclaredMethod(Class<?> type, String methodName, Class<?>... parameterTypes) {
        Method method = null;
        try {
            method = type.getDeclaredMethod(methodName, parameterTypes);
        } catch (NoSuchMethodException e) {
            Class<?> superclass = type.getSuperclass();
            if (superclass != null) {
                method = getDeclaredMethod(superclass, methodName, parameterTypes);
            }
        } catch (Exception e) {
            log.error("getDeclaredMethod error.", e);
        }
        return method;
    }

    public static Class[] getParameterTypes(JoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        return method.getParameterTypes();
    }
}
