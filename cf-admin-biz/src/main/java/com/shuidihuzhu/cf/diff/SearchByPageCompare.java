package com.shuidihuzhu.cf.diff;

import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.skyscreamer.jsonassert.JSONCompareMode;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/3/1
 */
@Slf4j
public class SearchByPageCompare implements IDiffCompare{

    @Override
    public DiffResult compare(Object expected, Object actual) {
        return compare(expected, actual, JSONCompareMode.STRICT_ORDER);
    }

    public DiffResult compare(Object expected, Object actual, JSONCompareMode mode) {
        DiffResult diffResult = new DiffResult();

        if (expected == null || actual == null) {
            diffResult.setMessage("expected or actual is null");
            return diffResult;
        }

        try {
            List<CrowdfundingInfoVo> expectedInfos = (List<CrowdfundingInfoVo>) expected;
            Pair<Long, List<CrowdfundingInfoVo>> pair = (Pair<Long, List<CrowdfundingInfoVo>>) actual;
            List<CrowdfundingInfoVo> actualInfos = pair.getRight();

            List<Integer> expectIds = expectedInfos.stream().map(x -> x.getId()).collect(Collectors.toList());
            List<Integer> actualIds = actualInfos.stream().map(x -> x.getId()).collect(Collectors.toList());

            if (expectIds.size() != actualIds.size()) {
                diffResult.setMessage("结果长度不一致");
            }

            expectIds.removeAll(actualIds);

            if (CollectionUtils.isNotEmpty(expectIds)) {
                diffResult.setMessage("结果内容不一致");
            }
        } catch (Exception e) {
            log.error("[DefaultDiffCompare.compare] exception.", e);
        }
        diffResult.setPassed(true);
        return diffResult;
    }
}
