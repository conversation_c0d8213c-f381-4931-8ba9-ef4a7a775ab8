package com.shuidihuzhu.cf.diff;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

import com.alibaba.fastjson.JSON;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2019/3/1
 */
@Slf4j
public class DiffRunnable implements Runnable {


    private final Object result;
    private final MetaHolder metaHolder;
    private MeterRegistry meterRegistry;

    public DiffRunnable(Object result, MetaHolder metaHolder, MeterRegistry meterRegistry) {
        this.result = result;
        this.metaHolder = metaHolder;
        this.meterRegistry = meterRegistry;
    }

    @Override
    public void run() {

        // 执行配置的新服务
        Object diffMethodResult = execute(metaHolder.getObj(), metaHolder.getDiffMethod(), metaHolder.getArgs());

        // 对比新旧服务结果差异
        DiffResult diffResult = assertEquals(result, diffMethodResult);

        // 测试用途，打印debug级别日志
        log.debug(" DiffRunnable  diffMethodName:{}, diffResult:{}, args:{}", metaHolder.getDiffMethod().getName(), diffResult, metaHolder.getArgs());
        if (diffResult == null) {
            return;
        }
        meterRegistry.counter("cf-admin-api-diff", metaHolder.getDiffMethod().getName(), "total").increment();
        if (diffResult.isPassed()) {
            meterRegistry.counter("cf-admin-api-diff", metaHolder.getDiffMethod().getName(), "success").increment();
        } else {
            meterRegistry.counter("cf-admin-api-diff", metaHolder.getDiffMethod().getName(), "fail").increment();
            log.info(" DiffRunnable  diffMethodName:{}, diffResult:{}, args:{}", metaHolder.getDiffMethod().getName(), diffResult, metaHolder.getArgs());
        }
    }

    private Object execute(Object obj, Method diffMethod, Object... args) {

        if (diffMethod == null) {
            return null;
        }

        Object result = null;
        try {
            diffMethod.setAccessible(true);
            result = diffMethod.invoke(obj, args);
        } catch (IllegalAccessException | InvocationTargetException e) {
            log.error("[DiffRunnable] error. methodName:{}, args:{}", diffMethod.getName(), args, e);
        }

        return result;
    }

    private DiffResult assertEquals(Object expected, Object actual) {

        String diffCompareName = metaHolder.getDiff().diffCompare();

        IDiffCompare diffCompare = DiffUtil.getDiffCompare(diffCompareName);
        return diffCompare.compare(expected, actual);
    }
}
