package com.shuidihuzhu.cf.diff;

import java.lang.reflect.Method;
import org.aspectj.lang.JoinPoint;

/**
 * <AUTHOR>
 * @date 2019/3/1
 */
public class MetaHolder {
    private final Method method;
    private final Method diffMethod;
    private final Object obj;
    private final Object proxyObj;
    private final Object[] args;
    private final JoinPoint joinPoint;
    private final Diff diff;

    public static Builder builder() {
        return new Builder();
    }

    private MetaHolder(Builder builder) {
        this.method = builder.method;
        this.diffMethod = builder.diffMethod;
        this.obj = builder.obj;
        this.proxyObj = builder.proxyObj;
        this.args = builder.args;
        this.joinPoint = builder.joinPoint;
        this.diff = builder.diff;
    }

    public Method getMethod() {
        return method;
    }

    public Method getDiffMethod() {
        return diffMethod;
    }

    public Object getObj() {
        return obj;
    }

    public Object getProxyObj() {
        return proxyObj;
    }

    public Object[] getArgs() {
        return args;
    }

    public JoinPoint getJoinPoint() {
        return joinPoint;
    }

    public Diff getDiff() {
        return diff;
    }

    public static final class Builder {

        private Method method;
        private Method diffMethod;
        private Object obj;
        private Object proxyObj;
        private Object[] args;
        private JoinPoint joinPoint;
        private Diff diff;

        public Builder method(Method method) {
            this.method = method;
            return this;
        }

        public Builder diffMethod(Method diffMethod) {
            this.diffMethod = diffMethod;
            return this;
        }

        public Builder obj(Object obj) {
            this.obj = obj;
            return this;
        }

        public Builder proxyObj(Object proxyObj) {
            this.proxyObj = proxyObj;
            return this;
        }

        public Builder args(Object[] args) {
            this.args = args;
            return this;
        }

        public Builder joinPoint(JoinPoint joinPoint) {
            this.joinPoint = joinPoint;
            return this;
        }

        public Builder diff(Diff diff) {
            this.diff = diff;
            return this;
        }

        public MetaHolder build() {
            return new MetaHolder(this);
        }
    }
}
