package com.shuidihuzhu.cf.diff;

import brave.Tracing;

import javax.annotation.Resource;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2019/3/1
 */
public class ThreadPoolUtil {
    private static final ConcurrentHashMap<String, ThreadPoolExecutor> threadPools = new ConcurrentHashMap<>();

    public static ThreadPoolExecutor getThreadPool(final String threadPoolName) {
        ThreadPoolExecutor executorService = threadPools.get(threadPoolName);
        if (executorService != null) {
            return executorService;
        }

        synchronized (ThreadPoolUtil.class) {
            if (!threadPools.containsKey(threadPoolName)) {
                threadPools.put(threadPoolName, createThreadPool(threadPoolName));
            }
        }

        return threadPools.get(threadPoolName);
    }


    private static ThreadPoolExecutor createThreadPool(final String threadPoolName) {
        final ThreadFactory threadFactory = getThreadFactory(threadPoolName);
        final int keepAliveTime = 0;
        final int dynamicCoreSize = 20;
        final int maxQueueSize = 5;
        final BlockingQueue<Runnable> workQueue = getBlockingQueue(maxQueueSize);
        return new ThreadPoolExecutor(dynamicCoreSize, dynamicCoreSize, keepAliveTime, TimeUnit.MINUTES, workQueue,
                threadFactory);
    }

    private static ThreadFactory getThreadFactory(final String threadPoolName) {
        return new ThreadFactory() {
            private final AtomicInteger threadNumber = new AtomicInteger(0);

            @Override
            public Thread newThread(Runnable r) {
                Thread thread = new Thread(r, "diff-" + threadPoolName + "-" + threadNumber.incrementAndGet());
                thread.setDaemon(true);
                return thread;
            }
        };
    }

    public static BlockingQueue<Runnable> getBlockingQueue(int maxQueueSize) {
        /*
         * We are using SynchronousQueue if maxQueueSize <= 0 (meaning a queue is not wanted).
         * <p>
         * SynchronousQueue will do a handoff from calling thread to worker thread and not allow queuing which is what we want.
         * <p>
         * Queuing results in added latency and would only occur when the thread-pool is full at which point there are latency issues
         * and rejecting is the preferred solution.
         */
        if (maxQueueSize <= 0) {
            return new SynchronousQueue<Runnable>();
        } else {
            return new LinkedBlockingQueue<Runnable>(maxQueueSize);
        }
    }
}
