package com.shuidihuzhu.cf.adminfeign.aifeign;

//
//@FeignClient(name = "cf-photo-mgr",
//        configuration = AiDiagnosisPicCheckConfiguration.class,
//        fallback = AiDiagnosisPicCheckClient.AiDiagnosisPicCheckClientFallback.class)
public interface AiDiagnosisPicCheckClient {

//    @PostMapping(path = "/search")
//    String checkDiagnosisPicRepeat(@RequestParam("data") String data);
//
//    @PostMapping(path = "/ImageUpdate")
//    void updateDiagnosisPic(@RequestParam("data") String data);
//
//    @Slf4j
//    class AiDiagnosisPicCheckClientFallback  implements AiDiagnosisPicCheckClient {
//
//        @Override
//        public String checkDiagnosisPicRepeat(String data) {
//            log.info("ai患者诊断材料-相似性判断接口被降级 data:{}", data);
//            return "";
//        }
//
//        @Override
//        public void updateDiagnosisPic(String data) {
//            log.info("ai患者诊断材料-更新接口被降级 data:{}", data);
//        }
//    }
}
