package com.shuidihuzhu.cf.adminfeign;

import com.shuidihuzhu.cf.model.clew.CFClewInfoModelResult;
import com.shuidihuzhu.cf.model.clew.CFClewRegisterModel;
import com.shuidihuzhu.frame.client.response.FeignResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @author: wanghui
 * @create: 2019/1/18 4:40 PM
 */
@FeignClient(name = "cf-clewtrack-api",
        configuration = CrowdfundingVolunteerClewFeignClientConfiguration.class,
        fallback = CrowdfundingVolunteerClewFeignClient.CrowdfundingVolunteerClewFeignClientFallback.class)
public interface CrowdfundingVolunteerClewFeignClient {

    @PostMapping(path = "/innerapi/cf-clewtrack-api/get-clewlist")
    public FeignResponse<CFClewInfoModelResult<CFClewRegisterModel>> getClewlist(@RequestParam(value = "uniqueCode") String uniqueCode,
                                                                                 @RequestParam(value = "clewStatus", required = false) Integer clewStatus,
                                                                                 @RequestParam(value = "registerStartTime", required = false) String registerStartTime,
                                                                                 @RequestParam(value = "registerEndTime", required = false) String registerEndTime,
                                                                                 @RequestParam(value = "phone", required = false) String phone,
                                                                                 @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                                                 @RequestParam(value = "pageNo", defaultValue = "1", required = false) Integer pageNo);
    @PostMapping(path = "/innerapi/cf-clewtrack-api/add-clew")
    FeignResponse<Boolean> addClew(@RequestParam(value = "uniqueCode") String uniqueCode,
                                   @RequestParam(value = "phone") String phone,
                                   @RequestParam(value = "diseaseName", defaultValue = "", required = false) String diseaseName,
                                   @RequestParam(value = "fundraisingObject", defaultValue = "", required = false) String fundraisingObject,
                                   @RequestParam(value = "registerName")String registerName,
                                   @RequestParam(value = "specialMark", defaultValue = "", required = false) String specialMark,
                                   @RequestParam(value = "channel",defaultValue = "",required = false)String channel);

    class CrowdfundingVolunteerClewFeignClientFallback implements CrowdfundingVolunteerClewFeignClient {

        @Override
        public FeignResponse<CFClewInfoModelResult<CFClewRegisterModel>> getClewlist(String uniqueCode, Integer clewStatus, String registerStartTime, String registerEndTime, String phone, Integer pageSize, Integer pageNo) {
            return FeignResponse.fallback();
        }

        @Override
        public FeignResponse<Boolean> addClew(String uniqueCode, String phone, String diseaseName, String fundraisingObject, String registerName, String specialMark,String channel) {
            return FeignResponse.fallback();
        }
    }
}
