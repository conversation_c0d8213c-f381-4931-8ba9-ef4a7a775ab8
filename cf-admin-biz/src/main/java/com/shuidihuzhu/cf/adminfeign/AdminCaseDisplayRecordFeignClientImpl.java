package com.shuidihuzhu.cf.adminfeign;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.adminpure.feign.AdminCaseDisplayRecordFeignClient;
import com.shuidihuzhu.cf.client.adminpure.model.AdminCaseDisplayRecordVo;
import com.shuidihuzhu.cf.client.adminpure.model.CaseDisplaySetting;
import com.shuidihuzhu.cf.client.apipure.feign.CfCaseDisplaySettingFeignClient;
import com.shuidihuzhu.cf.client.apipure.model.crowdfunding.CaseDisplaySettingVo;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.record.AdminCaseDisplayRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
public class AdminCaseDisplayRecordFeignClientImpl implements AdminCaseDisplayRecordFeignClient {
    @Autowired
    private AdminCaseDisplayRecordService adminCaseDisplayRecordService;

    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;

    @Autowired
    private CfCaseDisplaySettingFeignClient cfCaseDisplaySettingFeignClient;
    @Override
    public OperationResult<Integer> countCaseDisplayRecord(String infoUuid) {
        return OperationResult.success(adminCaseDisplayRecordService.searchRecordOfChannel(infoUuid, 0, AdminCaseDisplayRecordVo.UPDATE_CHANNEL_TYPE.WHALE.getType()).size());
    }
}
