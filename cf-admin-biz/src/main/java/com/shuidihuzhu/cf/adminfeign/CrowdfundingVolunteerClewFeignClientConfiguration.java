package com.shuidihuzhu.cf.adminfeign;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @author: wanghui
 * @create: 2019/1/18 4:42 PM
 */
@Configuration
public class CrowdfundingVolunteerClewFeignClientConfiguration {
    @Bean
    public CrowdfundingVolunteerClewFeignClient.CrowdfundingVolunteerClewFeignClientFallback crowdfundingVolunteerClewFeignClientFallback(){
        return new CrowdfundingVolunteerClewFeignClient.CrowdfundingVolunteerClewFeignClientFallback();
    }
}
