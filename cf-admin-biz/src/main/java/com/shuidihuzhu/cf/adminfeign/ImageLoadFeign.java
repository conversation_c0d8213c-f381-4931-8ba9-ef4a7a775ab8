package com.shuidihuzhu.cf.adminfeign;

import feign.RequestLine;
import feign.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;

import java.net.URI;

/**
 * <AUTHOR>
 */
public interface ImageLoadFeign {

    @RequestLine("GET")
    @GetMapping
    Response getImage(URI baseUri);

    @Component
    class Fallback implements ImageLoadFeign{

        @Override
        public Response getImage(URI baseUri) {
            return null;
        }
    }
}
