package com.shuidihuzhu.cf.mq.impl;

import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.model.admin.CfPushDynamicMsgModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.mq.IAdminCommonMessageHelperService;
import com.shuidihuzhu.cf.mq.producer.MessageBuilder;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * @author: wanghui
 * @create: 2019/6/12 11:05 AM
 */
@Slf4j
@Service
public class AdminCommonMessageHelperService implements IAdminCommonMessageHelperService {

    @Autowired(required = false)
    private Producer producer;

    public static <T> MessageBuilder<T> builder() {
        return MessageBuilder.create();
    }

    /**
     * 同步发送
     *
     * @param message
     * @return
     */
    @Override
    public OpResult<MessageResult> send(Message message) {
        log.info("send {}", message);
        MessageResult sendResult = producer.send(message);
        if (sendResult == null) {
            log.error("sendResult null {}", message);
            return OpResult.createFailResult(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        ResultStatus status = sendResult.getStatus();
        if (status == null) {
            log.error("sendResult status null {}, {}", message, sendResult);
            return OpResult.createFailResult(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (status == ResultStatus.FAIL) {
            log.error("sendResult status fail {}", message, sendResult);
            return OpResult.createFailResult(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        return OpResult.createSucResult(sendResult);
    }


    /**
     * 动态发布通知捐款人
     * @param crowdFundingProgress
     * @return
     */
    @Override
    public Message getCfPublishProgressNoticeMessage(CrowdFundingProgress crowdFundingProgress) {
        return MessageBuilder.create()
                .setTopic(MQTopicCons.CF)
                .setTags(MQTagCons.CF_PUBLISH_PROGRESS_NOTICE)
                .setPayload(crowdFundingProgress)
                .build();
    }

    @Override
    public Message getCfAuditPassToDonorMsg(CrowdfundingInfo crowdfundingInfo) {
        return MessageBuilder.create()
                .setTopic(MQTopicCons.CF)
                .setTags(MQTagCons.CF_AUDIT_PASS_TO_DONOR_MSG)
                .setPayload(crowdfundingInfo)
                .setDelayLevel(getExpectTimeByAdjustedTime())
                .build();
    }
    @Override
    public Message getCfRecoverCaseMsg(CrowdfundingInfo crowdfundingInfo) {
        return MessageBuilder.create()
                .setTopic(MQTopicCons.CF)
                .setTags(com.shuidihuzhu.cf.constants.admin.MQTagCons.CF_RECOVER_CASE)
                .setPayload(crowdfundingInfo)
                .build();
    }

    /**
     * 下发动态审核MQ
     *
     * @param cfPushDynamicMsgModel
     * @return
     */
    @Override
    public Message getCfPushDynamicMsg(CfPushDynamicMsgModel cfPushDynamicMsgModel) {
        return MessageBuilder.create()
                .setTopic(MQTopicCons.CF)
                .setTags(com.shuidihuzhu.cf.constants.admin.MQTagCons.CF_PUSH_DYNAMIC_MSG)
                .setPayload(cfPushDynamicMsgModel)
                .build();
    }


    public DelayLevel getExpectTimeByAdjustedTime() {
        return DelayLevel.S1;
    }
}
