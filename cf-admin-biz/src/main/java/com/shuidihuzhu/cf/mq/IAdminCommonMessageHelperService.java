package com.shuidihuzhu.cf.mq;

import com.shuidihuzhu.cf.model.admin.CfPushDynamicMsgModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;

/**
 * @author: wanghui
 * @create: 2019/6/12 11:04 AM
 */
public interface IAdminCommonMessageHelperService {
    /**
     * 同步发送
     *
     * @param message
     * @return
     */
    OpResult<MessageResult> send(Message message);
    /**
     * 动态发布通知捐款人
     * @param crowdFundingProgress
     * @return
     */
    Message getCfPublishProgressNoticeMessage(CrowdFundingProgress crowdFundingProgress);
    Message getCfAuditPassToDonorMsg(CrowdfundingInfo crowdfundingInfo);

    Message getCfRecoverCaseMsg(CrowdfundingInfo crowdfundingInfo);

    Message getCfPushDynamicMsg(CfPushDynamicMsgModel cfPushDynamicMsgModel);
}
