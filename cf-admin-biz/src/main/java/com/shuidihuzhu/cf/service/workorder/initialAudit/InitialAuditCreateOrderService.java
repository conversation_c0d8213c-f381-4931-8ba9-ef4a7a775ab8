package com.shuidihuzhu.cf.service.workorder.initialAudit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.admin.constant.AdminWonActionConst;
import com.shuidihuzhu.cf.biz.admin.UserCommentBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseReasonEntityBiz;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.feign.CfMaterialWriteClient;
import com.shuidihuzhu.cf.client.material.feign.CfRaiseMaterialClient;
import com.shuidihuzhu.cf.client.material.model.CfMaterialAddOrUpdateVo;
import com.shuidihuzhu.cf.client.material.model.MaterialPlanVersion;
import com.shuidihuzhu.cf.client.material.model.RaiseBasicInfoModel;
import com.shuidihuzhu.cf.client.material.model.materialField.MaterialExtKeyConst;
import com.shuidihuzhu.cf.client.adminpure.enums.ContentTo1V1ReasonEnum;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonRecordClient;
import com.shuidihuzhu.cf.constants.AdminUserIDConstants;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.dao.crowdfunding.CfAiMaterialsDao;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enhancer.mq.MQHelperService;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cf.enums.InitialAudit.InitialAuditNoSmartReason;
import com.shuidihuzhu.cf.enums.InitialAudit.InitialAuditRiskWorkOrderReason;
import com.shuidihuzhu.cf.enums.InitialAudit.TargetAmountAuditWorkOrderScene;
import com.shuidihuzhu.cf.enums.NotifyOnlineVolunteerEventEnum;
import com.shuidihuzhu.cf.enums.admin.UserCommentSourceEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.enums.wx.WxBotSendMsgTypeEnum;
import com.shuidihuzhu.cf.event.NotifyOnlineVolunteerEvent;
import com.shuidihuzhu.cf.facade.eagle.AdminEagleFacade;
import com.shuidihuzhu.cf.model.admin.UserComment;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonTag;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.ai.AiConditionEnum;
import com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterials;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterialsResult;
import com.shuidihuzhu.cf.model.crowdfunding.ai.LayOutField;
import com.shuidihuzhu.cf.model.crowdfunding.caseRepeat.InitialRepeatCaseView;
import com.shuidihuzhu.cf.model.river.RiverUsageTypeEnum;
import com.shuidihuzhu.cf.param.InitialAuditCreateOrderParam;
import com.shuidihuzhu.cf.risk.client.aegis.EngineAnalysisClient;
import com.shuidihuzhu.cf.risk.client.risk.AccidentCaseClient;
import com.shuidihuzhu.cf.risk.client.rpc.DiseaseClassifyFeignClientV2;
import com.shuidihuzhu.cf.risk.client.rpc.DiseaseClient;
import com.shuidihuzhu.cf.risk.model.aegis.RiskAnalysisDto;
import com.shuidihuzhu.cf.risk.model.aegis.RiskObject;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseAmountReasonableTypeEnum;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyEnum;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyResultEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.TriggerTimingEnum;
import com.shuidihuzhu.cf.risk.model.risk.Participate;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseClassifyVOV2;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyRequest;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyResponse;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyResultInfo;
import com.shuidihuzhu.cf.service.ApplicationUtils;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.cf.service.workorder.CfAiMaterialsService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.ai.AiRuleJudgeService;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditOperationItem;
import com.shuidihuzhu.cf.vo.approve.RiverHandleParamVO;
import com.shuidihuzhu.cf.vo.crowdfunding.InitialAuditSmartRejectVo;
import com.shuidihuzhu.client.cf.api.client.CfGovMarkFeignClient;
import com.shuidihuzhu.client.cf.api.model.GovCooperationMark;
import com.shuidihuzhu.client.cf.api.model.enums.GovProjectEnum;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackTaskFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfUserInvitedLaunchCaseRecordModel;
import com.shuidihuzhu.client.cf.clewtrack.model.ChannelRefine;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderRecordClient;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCoreFeignClient;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCreateParam;
import com.shuidihuzhu.client.cf.workorder.ext.WorkOrderExtFeignClient;
import com.shuidihuzhu.client.cf.workorder.ext.WorkOrderExtVO;
import com.shuidihuzhu.client.cf.workorder.model.ChuciWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderRecord;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.*;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import com.shuidihuzhu.data.analytics.javasdk.core.UserTagTypeEnum;
import com.shuidihuzhu.data.servicelog.meta.cf.ContentLimitTo1v1Service;
import com.shuidihuzhu.data.servicelog.meta.cf.InitialSmartAuditDiseaseNormSnapshot;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import static com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditCreateOrder.AEGIS_MODEL_GUID;

/**
 * @Author: wangpeng
 * @Date: 2021/4/22 16:05
 * @Description:
 */
@Slf4j
@Service
@RefreshScope
public class InitialAuditCreateOrderService {

    /**
     * 生成高风险工单开关
     */
    @Value("${direct.create.high.risk:true}")
    private boolean direct_create_high_risk;
    /**
     * 智能审核2.0开关
     */
    @Value("${apollo.smart.audit:false}")
    private boolean apollo_smart_audit;
    /**
     * 初审自动审核通过开关
     */
    @Value("${smart.second.work.order.again.audit:false}")
    private boolean smartSecondWorkOrderAgainAudit;
    /**
     * 初审工单进入1v1服务开关
     */
    @Value("${initial.audit.work.order.to.1V1:false}")
    private boolean initialAuditWorkOrderTo1V1;

    @Value("${ignore.initial.work.order.to.1V1.userIds:0}")
    private String ignoreInitialWorkOrderTo1V1UserIds;
    /**
     * 初审工单进入1v1服务开关
     */
    @Value("${initial.audit.work.order.to.1V1.content.length:200}")
    private int initialAuditWorkOrderTo1V1ContentLength;
    /**
     * 初审工单进入1v1服务延时消息兜底策略时间
     */
    @Value("${initial.audit.work.order.to.1V1.delay.consumer.time.30:1800000}")
    private long initialAuditWorkOrderTo1V1DelayConsumerTime30;


    @Value("${initial.audit.day.begin.second:32400}")
    private long dayBeginSecond = 9 * 3600;
    @Value("${initial.audit.day.end.second:77400}")
    private long dayEndSecond = 21 * 3600 + 1800;

    @Value("${in.day.time.delay.second:3600}")
    private long inDayTimeDelaySecond;

    @Value("${not.in.day.time.delay.clock:17}")
    private int notInDayTimeDelayClock;

    private static final List<Integer> ORDER_TYPES = Lists.newArrayList(WorkOrderType.yiliaoshenhe.getType(),
            WorkOrderType.highriskshenhe.getType(), WorkOrderType.ai_photo.getType(), WorkOrderType.ai_content.getType(),
            WorkOrderType.ai_erci.getType());

    @Resource
    private CfWorkOrderClient workOrderClient;
    @Autowired
    private InitialAuditCreateOrder initialAuditCreateOrder;
    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;
    @Resource
    private CfWorkOrderRecordClient cfWorkOrderRecordClient;
    @Autowired
    private CfAiMaterialsService cfAiMaterialsService;
    @Autowired
    private InitialAuditOperateService auditOperateService;
    @Autowired
    private UserCommentBiz userCommentBiz;
    @Autowired
    private CfRefuseReasonEntityBiz entityBiz;
    @Autowired
    private CfRefuseReasonEntityBiz cfRefuseReasonEntityBiz;
    @Autowired
    private AdminApproveService adminApproveService;
    @Autowired
    private ApproveRemarkOldService approveRemarkOldService;
    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Resource
    private CfMaterialWriteClient cfMaterialWriteClient;
    @Resource
    private CfClewtrackTaskFeignClient cfClewtrackTaskFeignClient;
    @Resource
    private Analytics analytics;
    @Resource
    private AdminCfInfoExtBiz adminCfInfoExtBiz;
    @Autowired
    private DiseaseClassifyFeignClientV2 diseaseClassifyFeignClientV2;
    @Resource
    private CfAiMaterialsDao cfAiMaterialsDao;
    @Resource
    private MsgClientV2Service msgClientV2Service;
    @Resource
    private InitialAuditTargetAmountReasonableService initialAuditTargetAmountReasonableService;
    @Resource
    private WorkOrderExtFeignClient workOrderExtFeignClient;
    @Resource
    private WonRecordClient wonRecordClient;
    @Autowired
    private ApplicationContext applicationContext;
    @Resource
    private CfGovMarkFeignClient cfGovMarkFeignClient;

    /**
     * 创建初审工单
     */
    public void createInitialAuditOrder(InitialAuditCreateOrderParam param) {
        log.info("InitialAuditCreateOrderService.createInitialAuditOrder.{} is begin param: {}", param.getCaseId(), JSONObject.toJSONString(param));
        // 获取案例id，是否首次创建初审工单，风控限制表述 0；1；
        int caseId = param.getCaseId();
        boolean first = param.isFirst();
        int condition = param.getCondition();

        // 获取案例信息，如果案例信息为空，直接返回
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
        if (Objects.isNull(crowdfundingInfo)) {
            log.info("InitialAuditCreateOrderService.createInitialAuditOrder.{} crowdfundingInfo is null {}", caseId, caseId);
            return;
        }
        // 风控判断是否可以创建初审工单，如果被拦截，就直接返回
        boolean checkCanCreate = initialAuditCreateOrder.checkCanCreate(caseId, condition);
        if (!checkCanCreate) {
            log.info("InitialAuditCreateOrderService.createInitialAuditOrder.{} is fail, because risk limit : {}", caseId, param);
            return;
        }
        /**
         * 获取五种工单中最新的一个
         * yiliaoshenhe(16, "医疗审核", "chucishenhe:yiliao", true)
         * highriskshenhe(18, "高风险工单", "chucishenhe:highrisk", true)
         * ai_photo(56, "图片录入工单", "chucishenhe:ai_photo", false)
         * ai_content(55, "文章录入工单", "chucishenhe:ai_content", true)
         * ai_erci(57, "二次审核工单", "chucishenhe:ai_erci", true)
         */
        /**
         *         select *
         *         from work_order
         *         where
         *             `case_id` = #{caseId}
         *             and order_type in
         *             <foreach collection="orderTypes" item="orderType" open="(" separator="," close=")">
         *                 #{orderType}
         *             </foreach>
         *             and `is_delete` = 0
         *         order by id desc
         *         limit 1
         */
        Response<WorkOrderVO> lastWorkOrder = workOrderClient.getLastWorkOrderByTypes(caseId, ORDER_TYPES);
        // 如果已经创建过了，并且first=true，就不再创建
        if (Objects.nonNull(lastWorkOrder) && lastWorkOrder.ok() && Objects.nonNull(lastWorkOrder.getData()) && first) {
            log.info("InitialAuditCreateOrderService.createInitialAuditOrder.{} is fail, because creat order is not first, {}", caseId, caseId);
            return;
        }
        // 处理1V1
        boolean handleTo1V1Service = handleTo1V1Service(param, crowdfundingInfo);
        if (handleTo1V1Service) {
            log.info("InitialAuditCreateOrderService.createInitialAuditOrder.{} is to 1v1 {}", caseId, param);
            return;
        }
        // 创建目标金额审核工单
        boolean targetAmountReasonable = initialAuditTargetAmountReasonableService.createTargetAmountReasonableWorkOrder(crowdfundingInfo, TargetAmountAuditWorkOrderScene.FIRST.getType(), param.isCheckTargetAmount());
        log.info("initialAuditTargetAmountReasonableService.createTargetAmountReasonableWorkOrder结果：{}", targetAmountReasonable);
        // 如果创建了目标金额审核工单，就不再创建其他工单
        if (targetAmountReasonable) {
            log.info("InitialAuditCreateOrderService.createInitialAuditOrder.{} 创建目标金额审核工单", caseId);
            return;
        }
        // 创建二次审核工单
        boolean authenticityCase = initialAuditCreateOrder.authenticityCase(crowdfundingInfo);
        if (authenticityCase) {
            // 为了在初审详情页显示策略结果，临时方案
            wonRecordClient.create()
                    .buildBasic(caseId, AdminWonActionConst.HIGH_RISK_V2_FLAG)
                    .buildCaseId(caseId)
                    .save();
            log.info("InitialAuditCreateOrderService.createInitialAuditOrder.{} InitialAuditCreateOrderService authenticityCase create {}", caseId, caseId);
            return;
        }
        // 生成高风险工单
        boolean riskWorkOrder = initialAuditCreateOrder.createHighRiskWorkOrder(caseId, condition);
        if (riskWorkOrder) {
            log.info("InitialAuditCreateOrderService.createInitialAuditOrder.{} 生成高风险工单", caseId);
            return;
        }

        // 判断是否走智能审核
        InitialAuditSmartRejectVo initialAuditSmartRejectVo = initialAuditCreateOrder.judgeInitialReviewInformation(caseId);
        InitialAuditNoSmartReason initialAuditNoSmartReason = initialAuditSmartRejectVo.getInitialAuditNoSmartReason();
        log.info("InitialAuditCreateOrderService.createInitialAuditOrder.{} createInitialAuditOrder caseId:{}, judgeInitialReviewInformation:{}, apollo_smart_audit:{}, unLimitInitialAudit:{}", caseId, caseId, initialAuditNoSmartReason, apollo_smart_audit, param.isUnLimitInitialAudit());
        if (apollo_smart_audit 
            && Objects.equals(initialAuditNoSmartReason, InitialAuditNoSmartReason.SUCCESS) 
            && !param.isUnLimitInitialAudit()
            && noneSuiJiuYiCase(caseId)) {
            initialAuditCreateOrder.createSmartAuditWorkOrder(caseId);
            return;
        }
        ChuciWorkOrder chuciWorkOrder = new ChuciWorkOrder();
        chuciWorkOrder.setCaseId(caseId);
        chuciWorkOrder.setOrderType(WorkOrderType.ai_erci.getType());
        param.setChuciWorkOrder(chuciWorkOrder);
        param.setDiseaseName(initialAuditSmartRejectVo.getDiseaseName());
        param.setNoSmartAuditReason(!apollo_smart_audit ? InitialAuditNoSmartReason.SWITCH_OFF.getMsg() :
                param.isUnLimitInitialAudit() ? InitialAuditNoSmartReason.UN_LIMIT_INITIAL_AUDIT.getMsg() :
                initialAuditNoSmartReason.getMsg());
        initialAuditCreateOrder.create(param);
        log.info("InitialAuditCreateOrderService.createInitialAuditOrder.{}, judgeInitialReviewInformation:{}, apollo_smart_audit:{}", caseId, initialAuditNoSmartReason, apollo_smart_audit);
    }

    /**
     * 有穗救宜标记案例不走智能审核
     */
    private boolean noneSuiJiuYiCase(int caseId) {
        // 获取穗救宜标记
        Response<GovCooperationMark> response = cfGovMarkFeignClient.getMarkRecord(caseId, GovProjectEnum.SUI_JIU_YI.getCode());
        GovCooperationMark govCooperationMark = Optional.ofNullable(response)
            .filter(f -> f.ok())
            .map(Response::getData)
            .orElse(null);
        if (Objects.nonNull(govCooperationMark)) {
            log.info("noneSuiJiuYiCase is null caseId:{}", caseId);
            return govCooperationMark.getMarkType() != 1;
        }
        log.info("noneSuiJiuYiCase caseId:{}, govCooperationMark:{}", caseId, JSONObject.toJSONString(govCooperationMark));
        return true;
    }

    public void targetAmountWorkHandleInitialAudit(int caseId, long workOrderId, int handleResult) {
        if (handleResult == HandleResultEnum.later_doing.getType()) {
            return;
        }
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
        if (Objects.isNull(crowdfundingInfo)) {
            return;
        }
        Response<WorkOrderExtVO> orderExtVOResponse = workOrderExtFeignClient.getLastByName(workOrderId, OrderExtName.targetAmountAuditWorkOrderScene.name());
        int targetAmountAuditWorkOrderScene = Optional.ofNullable(orderExtVOResponse)
                .filter(Response::ok)
                .map(Response::getData)
                .map(WorkOrderExtVO::getValue)
                .map(Integer::valueOf)
                .orElse(0);
        if (targetAmountAuditWorkOrderScene == 0) {
            log.info("createInitialAuditOrder targetAmountWorkHandleInitialAudit targetAmountAuditWorkOrderScene {}", orderExtVOResponse);
            return;
        }
        if (handleResult == HandleResultEnum.audit_pass.getType() && targetAmountAuditWorkOrderScene == TargetAmountAuditWorkOrderScene.FIRST.getType()) {
            InitialAuditCreateOrderParam orderParam = InitialAuditCreateOrderParam.builder()
                    .caseId(caseId)
                    .condition(0)
                    .first(true)
                    .to1V1(true)
                    .checkTargetAmount(false)
                    .build();
            createInitialAuditOrder(orderParam);
        }
        if (handleResult == HandleResultEnum.audit_pass.getType() && targetAmountAuditWorkOrderScene == TargetAmountAuditWorkOrderScene.REJECT.getType()) {
            initialAuditCreateOrder.createChuci(caseId, 0, false);
        }
        if (handleResult == HandleResultEnum.audit_reject.getType()) {
            applicationContext.publishEvent(new NotifyOnlineVolunteerEvent(NotifyOnlineVolunteerEventEnum.TARGET_AMOUNT_WORK_ORDER_REJECT_EVENT, caseId));
            boolean authenticityCase = initialAuditCreateOrder.authenticityCase(crowdfundingInfo);
            if (authenticityCase) {
                // 为了在初审详情页显示策略结果，临时方案
                wonRecordClient.create()
                        .buildBasic(caseId, AdminWonActionConst.HIGH_RISK_V2_FLAG)
                        .buildCaseId(caseId)
                        .save();
                log.info("InitialAuditCreateOrderService targetAmountWorkHandleInitialAudit authenticityCase create {}", caseId);
                return;
            }
            boolean riskWorkOrder = initialAuditCreateOrder.createHighRiskWorkOrder(caseId, 0);
            if (riskWorkOrder) {
                return;
            }

            InitialAuditSmartRejectVo initialAuditSmartRejectVo = InitialAuditSmartRejectVo.builder()
                    .initialAuditNoSmartReason(InitialAuditNoSmartReason.TARGET_AMOUNT_WORK_ORDER)
                    .diseaseName("")
                    .build();
            initialAuditCreateOrder.createAiErCi(caseId, 0, initialAuditSmartRejectVo);
        }
    }

    /**
     * 智能审核-生成二次审核工单逻辑
     *
     * @param caseId
     */
    public void smartSecondReviewWorkOrder(int caseId) {
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
        if (Objects.isNull(crowdfundingInfo)) {
            return;
        }
        //生成二次审核工单
        ChuciWorkOrder chuciWorkOrder = new ChuciWorkOrder();
        chuciWorkOrder.setCaseId(caseId);
        chuciWorkOrder.setOrderType(WorkOrderType.ai_erci.getType());
        chuciWorkOrder.setSmartWorkOrderRealCreateTime(getSmartWorkOrderRealCreateTime(caseId));
        chuciWorkOrder.setSecondWorkOrderSource(SecondWorkOrderTypeSourceEnum.smart_second_work_order.name());

        // 自动通过
        boolean canAgainAudit = canAgainAudit(caseId);
        if (canAgainAudit) {
            chuciWorkOrder.setOperatorId(AdminUserIDConstants.SYSTEM);
            chuciWorkOrder.setHandleResult(HandleResultEnum.doing.getType());
        }
        InitialAuditCreateOrderParam orderParam = InitialAuditCreateOrderParam.builder()
                .caseId(caseId)
                .chuciWorkOrder(chuciWorkOrder)
                .noSmartAuditReason(InitialAuditNoSmartReason.SUCCESS.getMsg())
                .condition(0)
                .build();
        Response<Long> response = initialAuditCreateOrder.create(orderParam);
        log.info("SecondReviewWorkOrderConsumer 生成二次审核工单 chuciWorkOrder:{}", chuciWorkOrder);
        if (Objects.isNull(response) || response.notOk() || Objects.isNull(response.getData()) || response.getData() <= 0) {
            log.info("SecondReviewWorkOrderConsumer 生成二次审核工单 workOrderId is null:{}", chuciWorkOrder);
            return;
        }
        Long workOrderId = response.getData();
        try {
            snapShotDiseaseName(caseId, workOrderId);
        } catch (Exception e) {
            log.info("InitialAuditCreateOrderService snapShotDiseaseName is error", e);
        }
        // 操作备注,智能审核结果成功的不走这个备注，为了避免和出现两次操作备注
        saveSmartAuditOperation(caseId, workOrderId);

        if (!canAgainAudit) {
            return;
        }

        log.info("SecondReviewWorkOrderConsumer auto pass handleWorkOrder ~~~");

        handleWorkOrder(caseId, workOrderId);

        // 工单操作日志
        WorkOrderRecord record = new WorkOrderRecord();
        record.setCaseId(caseId);
        record.setOperatorId(0);
        record.setWorkOrderId(workOrderId);
        record.setWorkOrderType(WorkOrderType.ai_erci.getType());
        record.setOperateDesc("处理工单");
        record.setComment("系统自动审核通过");
        record.setOperateMode(4);
        cfWorkOrderRecordClient.insertWorkOrderRecordList(Lists.newArrayList(record));
    }

    public void snapShotDiseaseName(int caseId, long workOrderId) {
        CfAiMaterialsResult aiMaterialsResult = cfAiMaterialsDao.getResultByCaseId(caseId);
        if (Objects.isNull(aiMaterialsResult)) {
            return;
        }
        String workOrderIds = aiMaterialsResult.getWorkOrderIds();
        List<String> workOrderIdList = Splitter.on("---").splitToList(workOrderIds);
        List<CfAiMaterials> list = new ArrayList<>();
        workOrderIdList.forEach(w -> {
            list.addAll(cfAiMaterialsDao.getByWorkOrderId(Long.parseLong(w)));
        });
        List<LayOutField> fields = list.stream()
                .flatMap(r -> r.getFields().stream())
                .collect(Collectors.toList());
        Set<String> diseaseNameList = new HashSet<>();
        Set<String> diseaseNameInContentList = Splitter.on(",").splitToList(fields.stream()
                .filter(r -> "diseaseNameInContent".equals(r.getFieldKey()))
                .map(LayOutField::getFieldValue)
                .findAny()
                .orElse("")
                .replace("，", ","))
                .stream()
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toSet());
        Set<String> diseaseNameInTitleList = Splitter.on(",").splitToList(fields.stream()
                .filter(r -> "diseaseNameInTitle".equals(r.getFieldKey()))
                .map(LayOutField::getFieldValue)
                .findAny()
                .orElse("")
                .replace("，", ","))
                .stream()
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toSet());
        Set<String> diseaseList = Splitter.on(",").splitToList(fields.stream()
                .filter(r -> "diseaseNameInMd".equals(r.getFieldKey()))
                .map(LayOutField::getFieldValue)
                .findAny()
                .orElse("")
                .replace("，", ","))
                .stream()
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toSet());
        diseaseNameList.addAll(diseaseNameInContentList);
        diseaseNameList.addAll(diseaseNameInTitleList);
        diseaseNameList.addAll(diseaseList);
        List<String> strings = new ArrayList<>(diseaseNameList);
        Response<List<DiseaseClassifyVOV2>> response = diseaseClassifyFeignClientV2.diseaseNorm(strings);
        log.info("InitialAuditCreateOrderService snapShotDiseaseName diseaseNorm : {} {}", strings, response);
        Map<String, List<String>> map = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(Lists.newArrayList())
                .stream()
                .collect(Collectors.toMap(DiseaseClassifyVOV2::getDisease, DiseaseClassifyVOV2::getNorm, (x, y) -> x));
        log.info("InitialAuditCreateOrderService snapShotDiseaseName diseaseNorm  collect : {} {}", strings, map);

        Map<String, Object> mapDiseaseName = new HashMap<>();
        mapDiseaseName.put("diseaseNameInContent", diseaseNameInContentList);
        mapDiseaseName.put("diseaseNameInTitle", diseaseNameInTitleList);
        mapDiseaseName.put("diseaseNameInMd", diseaseList);

        InitialSmartAuditDiseaseNormSnapshot smartAuditDiseaseNormSnapshot = new InitialSmartAuditDiseaseNormSnapshot();
        smartAuditDiseaseNormSnapshot.setInfo_id((long) caseId);
        smartAuditDiseaseNormSnapshot.setWork_order_id(workOrderId);
        smartAuditDiseaseNormSnapshot.setDisease_name(JSONObject.toJSONString(mapDiseaseName));
        smartAuditDiseaseNormSnapshot.setNorm_disease_name(JSONObject.toJSONString(map));

        smartAuditDiseaseNormSnapshot.setUser_tag(String.valueOf(caseId));
        smartAuditDiseaseNormSnapshot.setUser_tag_type(UserTagTypeEnum.userid);
        analytics.track(smartAuditDiseaseNormSnapshot);

    }

    private String getSmartWorkOrderRealCreateTime(int caseId) {
        Response<WorkOrderVO> lastWorkOrder = workOrderClient.getLastWorkOrder(caseId, WorkOrderType.ai_content.getType());
        if (Objects.isNull(lastWorkOrder) || lastWorkOrder.notOk() || Objects.isNull(lastWorkOrder.getData())) {
            return "";
        }
        return DateUtil.getDate2LStr(lastWorkOrder.getData().getCreateTime());
    }

    /**
     * 提交初审工单通过逻辑
     */
    private void handleWorkOrder(int caseId, long workOrderId) {

        boolean specialReport = false;
        CfAiMaterials cfAiMaterials = cfAiMaterialsService.getByCaseId(caseId, CfAiMaterials.tType);
        if (Objects.nonNull(cfAiMaterials)) {
            String specialReport1 = cfAiMaterials.getFields()
                    .stream()
                    .filter(f -> StringUtils.equals(f.getFieldKey(), "specialReport"))
                    .map(LayOutField::getFieldValue)
                    .findFirst()
                    .orElse("");
            specialReport = StringUtils.equals(specialReport1, String.valueOf(AiConditionEnum.shi.getCode()));
        }

        // 为了回显驳回信息
        CfAiMaterialsResult cfAiMaterialsResult = cfAiMaterialsService.getResultByCaseId(caseId);
        List<Integer> rejectReasonDiBao = getDiBaoOrPinKunRejectReason(cfAiMaterialsResult, InitialAuditOperateService.DI_BAO_TAG);

        RiverHandleParamVO diBaoParamVO = RiverHandleParamVO.builder()
                .caseId(caseId)
                .usageTypeEnum(RiverUsageTypeEnum.DI_BAO)
                .handleComment("")
                .handleType(CollectionUtils.isEmpty(rejectReasonDiBao) ? RiverHandleParamVO.HandleType.PASS : RiverHandleParamVO.HandleType.REJECT)
                .workOrderId(workOrderId)
                .orderType(WorkOrderType.ai_erci.getType())
                .rejectIds(rejectReasonDiBao)
                .operatorId(AdminUserIDConstants.SYSTEM)
                .callStatus(0)
                .rejectDetail(Maps.newHashMap())
                .build();

        String diBaoComment = Objects.equals(diBaoParamVO.getHandleType(), RiverHandleParamVO.HandleType.PASS) ? "低保信息:通过" : "";

        InitialAuditOperationItem.HandleCaseInfoParam handleCaseInfoParam = InitialAuditOperationItem
                .HandleCaseInfoParam
                .builder()
                .caseId(caseId)
                .handleType(InitialAuditOperationItem.HandleTypeEnum.SUBMIT.getCode())
                .orderType(WorkOrderType.ai_erci.getType())
                .workOrderId(workOrderId)
                .pinKunHandleParam(null)
                .pinKunComment("")
                .diBaoHandleParam(diBaoParamVO)
                .diBaoComment(diBaoComment)
                .passIds(Arrays.asList(InitialAuditOperateService.BASE_INFO_TAG, InitialAuditOperateService.CREDIT_TAG, InitialAuditOperateService.FIRST_APPROVE_TAG))
                .callStatus(0)
                .userCallStatus(0)
                .callComment("")
                .userId(AdminUserIDConstants.SYSTEM)
                .handleComment("")
                .systemAutoAudit(1)
                .specialReport(specialReport)
                .build();
        try {
            auditOperateService.handleWorkOrder(handleCaseInfoParam);
        } catch (Exception e) {
            String content = "【初审】自动审核通过异常，请及时处理。\n案例Id:" + handleCaseInfoParam.getCaseId() + "\n" + "工单Id:" + handleCaseInfoParam.getWorkOrderId()
                    + "\n" + "异常信息：" + e.getMessage();
            AlarmBotService.sentText(WxBotSendMsgTypeEnum.INITIAL_SMART_AUDIT_SUCCESS.getBotKey(), content, null, null);
            log.info("InitialAuditCreateOrderService smartSecondReviewWorkOrder handleWorkOrder is error {}", handleCaseInfoParam, e);
        } finally {
            autoSmartCheck(handleCaseInfoParam);
        }
    }

    private void autoSmartCheck(InitialAuditOperationItem.HandleCaseInfoParam handleCaseInfoParam) {
        log.info("InitialAuditCreateOrderService autoSmartCheck start {}", handleCaseInfoParam);
        Response<WorkOrderVO> workOrderVOResponse = workOrderClient.getWorkOrderById(handleCaseInfoParam.getWorkOrderId());
        if (Objects.isNull(workOrderVOResponse.getData()) || workOrderVOResponse.notOk() || Objects.isNull(workOrderVOResponse.getData())) {
            return;
        }
        WorkOrderVO data = workOrderVOResponse.getData();
        CfInfoExt cfInfoExt = adminCfInfoExtBiz.getByCaseId(handleCaseInfoParam.getCaseId());
        if (Objects.isNull(cfInfoExt)) {
            return;
        }

        if (data.getHandleResult() == HandleResultEnum.smart_audit_pass.getType() && cfInfoExt.getFirstApproveStatus() == FirstApproveStatusEnum.APPLY_SUCCESS.getCode()) {
            return;
        }
        String content = "【初审】自动审核通过异常，触发自动回收逻辑，请校验！！！\n案例Id:" + handleCaseInfoParam.getCaseId() + "\n" + "工单Id:" + handleCaseInfoParam.getWorkOrderId();
        AlarmBotService.sentText(WxBotSendMsgTypeEnum.INITIAL_SMART_AUDIT_SUCCESS.getBotKey(), content, null, null);
        Response<Long> assignOrderResponse = workOrderClient.callbackAndAssignOrder(handleCaseInfoParam.getWorkOrderId(), 0, 0);
        log.info("InitialAuditCreateOrderService autoSmartCheck end {} {}", handleCaseInfoParam, assignOrderResponse);

    }

    private List<Integer> getDiBaoOrPinKunRejectReason(CfAiMaterialsResult cfAiMaterialsResult, int auditType) {
        if (Objects.isNull(cfAiMaterialsResult)) {
            return Collections.emptyList();
        }
        String rejectResult = cfAiMaterialsResult.getRejectResult();
        List<String> list = Splitter.on(",").splitToList(rejectResult);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<CfRefuseReasonTag> refuseListByType = cfRefuseReasonEntityBiz.getRefuseListByType(auditType, 1);
        if (CollectionUtils.isEmpty(refuseListByType)) {
            return Collections.emptyList();
        }
        CfRefuseReasonTag cfRefuseReasonTag = refuseListByType.get(0);
        List<CfRefuseReasonEntity> entityList = cfRefuseReasonTag.getEntityList();
        if (CollectionUtils.isEmpty(entityList)) {
            return Collections.emptyList();
        }
        return entityList.stream()
                .map(CfRefuseReasonEntity::getId)
                .filter(f -> list.contains(String.valueOf(f)))
                .collect(Collectors.toList());
    }

    private boolean canAgainAudit(int caseId) {
        if (!smartSecondWorkOrderAgainAudit) {
            return false;
        }
        CfAiMaterialsResult cfAiMaterialsResult = cfAiMaterialsService.getResultByCaseId(caseId);
        if (Objects.isNull(cfAiMaterialsResult)) {
            return false;
        }
        String rejectResult = cfAiMaterialsResult.getRejectResult();
        List<String> list = Splitter.on(",").splitToList(rejectResult);
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        return AiRuleJudgeService.passIds
                .stream()
                .filter(f -> !Objects.equals(f, InitialAuditOperateService.PIN_KUN_TAG) && !Objects.equals(f, InitialAuditOperateService.DI_BAO_TAG))
                .allMatch(f -> list.contains(f.toString()));
    }


    private void saveSmartAuditOperation(int caseId, long workOrderId) {
        CfAiMaterialsResult cfAiMaterialsResult = cfAiMaterialsService.getResultByCaseId(caseId);
        if (Objects.isNull(cfAiMaterialsResult)) {
            return;
        }
        List<String> rejectResultsList = Splitter.on(",").splitToList(cfAiMaterialsResult.getRejectResult());
        boolean smartResultPass = AiRuleJudgeService.passIds
                .stream()
                .filter(f -> !Objects.equals(f, InitialAuditOperateService.PIN_KUN_TAG) && !Objects.equals(f, InitialAuditOperateService.DI_BAO_TAG))
                .allMatch(f -> rejectResultsList.contains(f.toString()));

        UserCommentSourceEnum.CommentType commentType = UserCommentSourceEnum.CommentType.INITIAL_AUDIT_PASS;;
        String operateDesc = "";

        // 智能审核驳回
        if (StringUtils.equals(cfAiMaterialsResult.getStopResult(), "215")) {
            commentType = UserCommentSourceEnum.CommentType.INITIAL_AUDIT_END_CASE;
            operateDesc = "智能审核停止筹款" + "<br>" + InitialAuditOperateService.seriousIllnessDes;
        } else if (!smartResultPass) {
            // 审核驳回
            List<Integer> collect = rejectResultsList.stream()
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            List<String> cfRefuseReasonEntities = entityBiz.selectByIds(collect).stream()
                    .map(CfRefuseReasonEntity::getContent)
                    .collect(Collectors.toList());
            String join = Joiner.on("<br>").join(cfRefuseReasonEntities);
            commentType = UserCommentSourceEnum.CommentType.INITIAL_AUDIT_REJECT;
            operateDesc = "智能审核驳回" + "<br>" + join;
        }

        // 通过的不在这里添加操作备注，避免生成两次
        if (StringUtils.isEmpty(operateDesc)) {
            return;
        }

        UserComment commentOne = new UserComment();
        commentOne.setOperatorId(0);
        commentOne.setCaseId(caseId);
        commentOne.setCommentSource(UserCommentSourceEnum.INITIAL_AUDIT.getCode());
        commentOne.setCommentType(commentType.getCode());
        commentOne.setOperateMode(commentType.getDesc());
        commentOne.setWorkOrderId(workOrderId);
        commentOne.setComment("");
        commentOne.setOperateDesc(operateDesc);
        userCommentBiz.insert(commentOne);
    }

    /**
     * 文章大于200字走1v1服务
     *
     * @param param
     * @param crowdfundingInfo
     * @return
     */
    public boolean handleTo1V1Service(InitialAuditCreateOrderParam param, CrowdfundingInfo crowdfundingInfo) {
        if (!param.isTo1V1()) {
            log.info("InitialAuditCreateOrderService handleTo1V1Service not to 1v1");
            return false;
        }
        int caseId = param.getCaseId();
        if (!initialAuditWorkOrderTo1V1) {
            log.info("InitialAuditCreateOrderService handleTo1V1Service switch is close {}", initialAuditWorkOrderTo1V1);
            return false;
        }

        if (StringUtils.isNotBlank(ignoreInitialWorkOrderTo1V1UserIds)
                && ignoreInitialWorkOrderTo1V1UserIds.contains("#" + crowdfundingInfo.getUserId() + "#")) {
            log.info("InitialAuditCreateOrderService handleTo1V1Service userIds in list. userId: {}", crowdfundingInfo.getUserId());
            return false;
        }

        String title = crowdfundingInfo.getTitle();
        if (title.contains("水滴内部测试") && title.contains("石家庄职场")) {
            return false;
        }

        Map<Integer, CfUserInvitedLaunchCaseRecordModel> recordModelMap = adminApproveService.getCaseChannelRecordMap(Lists.newArrayList(caseId));
        CfUserInvitedLaunchCaseRecordModel recordModel = recordModelMap.get(caseId);
        if (Objects.nonNull(recordModel) &&
                !ChannelRefine.ChannelRefineResuleEnum.parse(recordModel.getChannel()).equals(ChannelRefine.ChannelRefineResuleEnum.YONGHU_ZIZHU) &&
                !ChannelRefine.ChannelRefineResuleEnum.parse(recordModel.getChannel()).equals(ChannelRefine.ChannelRefineResuleEnum.WAIHU_YINDAO) &&
                !ChannelRefine.ChannelRefineResuleEnum.parse(recordModel.getChannel()).equals(ChannelRefine.ChannelRefineResuleEnum.WEIXIN_1V1)) {
            log.info("InitialAuditCreateOrderService handleTo1V1Service channel:{} caseId:{}", recordModel, caseId);
            return false;
        }

        ContentLimitTo1v1Service contentLimitTo1v1Service = new ContentLimitTo1v1Service();
        contentLimitTo1v1Service.setCase_id(String.valueOf(caseId));
        contentLimitTo1v1Service.setKey(ContentTo1V1ReasonEnum.CONTENT_TO_1V1.name());
        contentLimitTo1v1Service.setValue(String.valueOf(true));
        contentLimitTo1v1Service.setUser_tag(String.valueOf(caseId));
        contentLimitTo1v1Service.setUser_tag_type(UserTagTypeEnum.userid);
        analytics.track(contentLimitTo1v1Service);

        Response<Boolean> noFuWuCase = cfClewtrackTaskFeignClient.noFuwuCase(crowdfundingInfo);
        if (Objects.isNull(noFuWuCase) || noFuWuCase.notOk() || Objects.isNull(noFuWuCase.getData()) || !noFuWuCase.getData()) {
            log.info("InitialAuditCreateOrderService noFuwuCase {} crowdfundingInfo:{}", noFuWuCase, crowdfundingInfo);
            return false;
        }

        // 操作日志&短信&标记&发送MQ兜底
        approveRemarkOldService.add(caseId, AdminUserIDConstants.SYSTEM, "自发起案例，优先流转微信1v1再服务，延迟生成预审工单");
        send1V1Msg(crowdfundingInfo);
        CfMaterialAddOrUpdateVo build = CfMaterialAddOrUpdateVo.builder()
                .materialName(MaterialExtKeyConst.INIT_AUDIT_CONTENT_TO_1V1)
                .materialValue(String.valueOf(true))
                .materialLabel("")
                .materialExt("")
                .caseId(caseId)
                .build();
        cfMaterialWriteClient.addOrUpdateByFields(caseId, Collections.singletonList(build));

        return true;
    }

    private void send1V1Msg(CrowdfundingInfo crowdfundingInfo) {
        UserInfoModel userAccount = userInfoServiceBiz.getUserInfoByUserId(crowdfundingInfo.getUserId());
        if (Objects.isNull(userAccount)) {
            return;
        }

        msgClientV2Service.sendSmsMsg(isInDayTime() ? "CRC1874" : "QGJ3027", Lists.newArrayList(userAccount.getCryptoMobile()), true);
    }

    private boolean isInDayTime() {
        int curSecond = new DateTime().getSecondOfDay();
        return curSecond >= dayBeginSecond && curSecond <= dayEndSecond;
    }
}
