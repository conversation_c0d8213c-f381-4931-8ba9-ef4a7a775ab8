package com.shuidihuzhu.cf.service.stream;

import com.shuidihuzhu.cf.model.broadcasting.OrderAssignStreamVO;
import com.shuidihuzhu.cf.service.stream.manager.StreamData;
import com.shuidihuzhu.cf.service.stream.manager.StreamDataManageService;
import com.shuidihuzhu.cf.service.stream.manager.StreamStringManageService;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class StreamBizService {

    @Autowired
    private StreamDataManageService streamV2Service;

    @Autowired
    private StreamStringManageService streamStringManageService;

    public void onAssignSuccess(WorkOrderVO vo) {
        StreamData streamData = StreamData.create(StreamActionConst.ASSIGN, null);
        streamV2Service.pushBroadcast(StreamActionConst.Subject.operator_group_assign, vo.getOperatorId(), NewResponseUtil.makeSuccess(streamData));
    }

    public void pushReportSchedulePageShow(long userId) {
        streamStringManageService.pushBroadcast(StreamActionConst.Subject.operator_string, userId,
                NewResponseUtil.makeSuccess(StreamActionConst.REPORT_SCHEDULE_PAGE_SHOW));
    }

    public void pushWorkOrderPageShow(long userId) {
        if (userId <= 0) {
            log.info("pushWorkOrderPageShow userId empty");
            return;
        }
        streamStringManageService.pushBroadcast(StreamActionConst.Subject.cailiao_fuwu, userId,
                NewResponseUtil.makeSuccess(StreamActionConst.PAGE_SHOW));
    }

    public void pushReportPending(long userId) {
        if (userId <= 0) {
            log.info("pushWorkOrderPageShow userId empty");
            return;
        }
        streamStringManageService.pushBroadcast(StreamActionConst.Subject.report_pending_entry, userId,
                NewResponseUtil.makeSuccess(StreamActionConst.REPORT_PENDING_ENTRY_PAGE_SHOW));
    }

}
