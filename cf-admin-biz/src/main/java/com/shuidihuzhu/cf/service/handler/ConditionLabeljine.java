package com.shuidihuzhu.cf.service.handler;

import com.shuidihuzhu.cf.facade.AdminApolloCofig;
import com.shuidihuzhu.cf.model.event.CailiaoCondition;
import com.shuidihuzhu.cf.model.event.CailiaoConditionEvent;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderLevel;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @DATE 2020/3/20
 * 高金额案例：筹款金额≥x万元的案例
 */
@Component
@Slf4j
public class ConditionLabeljine implements ConditionLabel{


    @Override
    @EventListener(classes = CailiaoConditionEvent.class)
    public void onApplicationEvent(CailiaoConditionEvent event) {
        String amount = AdminApolloCofig.getValueFromApollo(AdminApolloCofig.cf_amount_fen,"5000000");
        int a = event.getAmount();
        log.info("ConditionLabel caseId={} amount={} new amount ={}",event.getCaseId(),amount,a);
        if (a >= Integer.valueOf(amount)){
            setResults("筹款金额="+amount,event);
            updateLevelIfHigherAndCaiLiao4(event, OrderLevel.A);
            return;
        }
    }

    @Override
    public int getConditionCode() {
        return  CailiaoCondition.condition_3.getCode();
    }
}
