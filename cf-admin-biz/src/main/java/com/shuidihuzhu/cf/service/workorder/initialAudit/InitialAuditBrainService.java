package com.shuidihuzhu.cf.service.workorder.initialAudit;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.client.adminpure.enums.WorkOrderExtContentTypeEnum;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.feign.CfRaiseMaterialClient;
import com.shuidihuzhu.cf.client.material.feign.PreposeMaterialClient;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.client.material.model.RaiseBasicInfoModel;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationRecord;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.delegate.PreposeMaterialDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.enums.InitialAudit.InitialAuditJudgeNameConsistencyEnum;
import com.shuidihuzhu.cf.risk.client.rpc.DiseaseClient;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseAmountReasonableTypeEnum;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyEnum;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseInfoVo;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyLog;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyRequest;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyResponse;
import com.shuidihuzhu.cf.service.workorder.WorkOrderExtService;
import com.shuidihuzhu.cf.vo.admin.initialAudit.DiseaseStrategyResult;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditCaseDetail;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialDiseaseParam;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialJudgeConsistency;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialReportErrorParam;
import com.shuidihuzhu.cf.vo.approve.InitialAuditOCRResultVO;
import com.shuidihuzhu.client.cf.growthtool.model.CfCaseSpecialPrePoseDetail;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RefreshScope
@Slf4j
public class InitialAuditBrainService {

    @Autowired
    private DiseaseClient diseaseClient;
    @Autowired
    private IRiskDelegate firstApproveBiz;
    @Autowired
    private CommonOperationRecordClient commonOperationRecordClient;
    @Autowired
    private CfRaiseMaterialClient raiseClient;
    @Autowired
    private WorkOrderExtService workOrderExtService;
    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;
    @Autowired
    private PreposeMaterialDelegate preposeDelegate;
    @Autowired
    private PreposeMaterialClient preposeClient;


    public InitialAuditCaseDetail judgeNameConsistency(long workOrderId, int caseId,
                                                       InitialAuditCaseDetail caseDetail) {
        if (workOrderId <= 0) {
            return caseDetail;
        }
        //患者姓名
        String name = Objects.requireNonNullElse(caseDetail.getFirstApproveCaseInfo().getPatientRealName(),"");
        //标题
        String title = Objects.requireNonNullElse(caseDetail.getCaseBaseInfo().getTitle(), "");
        //文章
        String content = Objects.requireNonNullElse(caseDetail.getCaseBaseInfo().getContent(), "");

        Map<Integer, String> map = Maps.newHashMap();

        String titleTips = "";
        //图文信息文案
        if (title.contains(name) || content.contains(name)) {
            map.put(InitialAuditJudgeNameConsistencyEnum.TITLE_ARTICLE_JUDGE_NAME_SUC.getCode(), InitialAuditJudgeNameConsistencyEnum.getByValue(InitialAuditJudgeNameConsistencyEnum.TITLE_ARTICLE_JUDGE_NAME_SUC.getCode()));
            titleTips = InitialAuditJudgeNameConsistencyEnum.getByValue(InitialAuditJudgeNameConsistencyEnum.TITLE_ARTICLE_JUDGE_NAME_SUC.getCode());
        } else {
            map.put(InitialAuditJudgeNameConsistencyEnum.TITLE_ARTICLE_JUDGE_NAME_FAIL.getCode(), InitialAuditJudgeNameConsistencyEnum.getByValue(InitialAuditJudgeNameConsistencyEnum.TITLE_ARTICLE_JUDGE_NAME_FAIL.getCode()));
            titleTips = InitialAuditJudgeNameConsistencyEnum.getByValue(InitialAuditJudgeNameConsistencyEnum.TITLE_ARTICLE_JUDGE_NAME_FAIL.getCode());
        }
        commonOperationRecordClient.create()
                .buildBasicPlatform(workOrderId, 0, OperationActionTypeEnum.INITIAL_CONTENT_NAME_JUDGE)
                .buildCaseId(caseId).buildRemark(
                JSON.toJSONString(new InitialReportErrorParam.OperatorDetails().builder()
                        .patientName(name).tips(titleTips).build())).save();

        String medicalTips = "";
        //医疗材料文案
        InitialAuditOCRResultVO ocrResultVO = caseDetail.getFirstApproveCaseInfo().getInitialAuditOCRResultVO();
        if (ocrResultVO != null && ocrResultVO.isSamePatientName()) {
            map.put(InitialAuditJudgeNameConsistencyEnum.OCR_JUDGE_NAME_SUC.getCode(), InitialAuditJudgeNameConsistencyEnum.getByValue(InitialAuditJudgeNameConsistencyEnum.OCR_JUDGE_NAME_SUC.getCode()));
            medicalTips = InitialAuditJudgeNameConsistencyEnum.getByValue(InitialAuditJudgeNameConsistencyEnum.OCR_JUDGE_NAME_SUC.getCode());
        } else {
            map.put(InitialAuditJudgeNameConsistencyEnum.OCR_JUDGE_NAME_FAIL.getCode(), InitialAuditJudgeNameConsistencyEnum.getByValue(InitialAuditJudgeNameConsistencyEnum.OCR_JUDGE_NAME_FAIL.getCode()));
            medicalTips = InitialAuditJudgeNameConsistencyEnum.getByValue(InitialAuditJudgeNameConsistencyEnum.OCR_JUDGE_NAME_FAIL.getCode());
        }

        commonOperationRecordClient.create()
                .buildBasicPlatform(workOrderId, 0, OperationActionTypeEnum.INITIAL_MEDICAL_NAME_JUDGE)
                .buildCaseId(caseId).buildRemark(
                JSON.toJSONString(new InitialReportErrorParam.OperatorDetails().builder()
                        .patientName(name).tips(medicalTips).build())).save();

        caseDetail.setJudgeConsistency(new InitialJudgeConsistency(map));
        return caseDetail;
    }

    private void reject() {

    }

    public DiseaseStrategyResult selectDiseaseStrategySnapshot(int caseId, int userId, long workOrderId, Integer strategyType) {
        DiseaseStrategyResult result = workOrderExtService.getByClazz(workOrderId, WorkOrderExtContentTypeEnum.INITIAL_DISEASE_STRATEGY,
                DiseaseStrategyResult.class);

        if (result != null) {
            fullBeforeStrategyType(caseId, result, workOrderId);
            return result;
        }
        InitialDiseaseParam param = new InitialDiseaseParam();
        param.setCaseId(caseId);
        param.setWorkOrderId(workOrderId);
        param.setUserId(userId);
        if (strategyType == null) {
            strategyType = InitialDiseaseParam.DiseaseType.FIRST.getCode();
        }
        param.setStrategyType(strategyType);
        Response<WorkOrderVO> orderVOResponse = cfWorkOrderClient.getWorkOrderById(workOrderId);
        if (Objects.isNull(orderVOResponse) || Objects.isNull(orderVOResponse.getData())) {
            return null;
        }
        int orderType = orderVOResponse.getData().getOrderType();
        param.setReasonAmountType(WorkOrderType.highriskshenhe.getType() == orderType ? DiseaseAmountReasonableTypeEnum.HIGH_RIS_INITIAL_DETAIL.getCode() : DiseaseAmountReasonableTypeEnum.INITIAL_DETAIL.getCode());
        return judgeDiseaseStrategy(param);
    }

    /**
     * 兼容老案例不存在执行方式的问题
     * @param caseId
     * @param result
     * @param workOrderId
     */
    private void fullBeforeStrategyType(int caseId, DiseaseStrategyResult result, long workOrderId) {
        if (result.getBeforeStrategyType() != 0) {
            return;
        }
        List<DiseaseStrategyLog> diseaseStrategyLogs = selectDiseaseStrategyLog(caseId);
        if (CollectionUtils.isEmpty(diseaseStrategyLogs)){
            return;
        }
        List<DiseaseStrategyLog> currentDiseaseStrategyLogs = diseaseStrategyLogs.stream()
                .filter(v -> v.getWorkOrderId() == workOrderId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(currentDiseaseStrategyLogs)) {
            findBeforeStrategyType(result, currentDiseaseStrategyLogs);
            return;
        }
        findBeforeStrategyType(result, diseaseStrategyLogs);
    }

    private void findBeforeStrategyType(DiseaseStrategyResult result, List<DiseaseStrategyLog> diseaseStrategyLogs) {
        for (DiseaseStrategyLog diseaseStrategyLog : diseaseStrategyLogs) {
            if ( diseaseStrategyLog.getOtherInfo() == null) {
                continue;
            }
            Object executeStrategyEnum = diseaseStrategyLog.getOtherInfo().get("executeStrategyEnum");
            if (executeStrategyEnum == null) {
                continue;
            }
            int executeStrategy = (Integer) executeStrategyEnum;
            if (executeStrategy == DiseaseStrategyEnum.AMOUNT_REASONABLE.getCode()) {
                continue;
            }
            result.setBeforeStrategyType(executeStrategy);
            return;
        }
    }


    public DiseaseStrategyResult judgeDiseaseStrategy(InitialDiseaseParam param) {
        // 判断工单的类型
        if (!canQueryStrategy(param.getWorkOrderId())) {
            log.info("当前的工单类型不调用智能审核.workOrderId:{}", param.getWorkOrderId());
            return null;
        }

        DiseaseStrategyRequest request = new DiseaseStrategyRequest();
        request.setCaseId(param.getCaseId());
        request.setUserId(param.getUserId());
        request.setWorkOrderId(param.getWorkOrderId());

        // 运营手动输入疾病的情况
        List<String> diseaseResourceList = Lists.newArrayList();
        if (StringUtils.isNotBlank(param.getInputDisease())) {
            diseaseResourceList = Arrays.asList(StringUtils.split(param.getInputDisease(), ",，"));
            request.setDiseaseNameList(diseaseResourceList);
        }

        // 特殊发起的情况
        request.setCanRaiseDiseaseNameList(param.getCanRaiseDiseases());
        request.setSpecialDiseaseNameList(param.getSpecialDiseases());
        request.setSpecialRaiseChoiceInfo(param.getTreatmentInfo());
        request.setSpecialDiseaseInfo(param.getMultipleTreatmentInfo());
        request.setExecuteStrategyEnum(param.getStrategyType());
        request.setReasonAmountType(param.getReasonAmountType());

        Response<DiseaseStrategyResponse> response = diseaseClient.diseaseStrategy(request);
        log.info("调用疾病是否可发的接口。param:{} result:{}", JSON.toJSONString(request), JSON.toJSONString(response));
        if (response == null || response.getData() == null) {
            return null;
        }

        DiseaseStrategyResult strategyResult = workOrderExtService.getByClazz(param.getWorkOrderId(),
                WorkOrderExtContentTypeEnum.INITIAL_DISEASE_STRATEGY, DiseaseStrategyResult.class);
        strategyResult = initStrategyResult(request, strategyResult);
        setSpecialDiseaseChoiceInfoList(response, strategyResult);
        setDiseaseInfoList(request, diseaseResourceList, response, strategyResult);
        //写入治疗方案显示
        strategyResult.setTreatmentInfo(param.getTreatmentInfo());
        strategyResult.setStrategyType(param.getStrategyType());
        fillStrategyFitDesc(request, response.getData(), strategyResult);
        if (response.getData().getDiseaseAmountStrategyResult() != null && response.getData().getDiseaseAmountStrategyResult().getResult() != 0) {
            strategyResult.setTreatmentInfo("");
        }
        //兼容显示的结果
        if (request.getExecuteStrategyEnum() != DiseaseStrategyEnum.AMOUNT_REASONABLE.getCode()) {
            strategyResult.setBeforeStrategyType(request.getExecuteStrategyEnum());
        }
        //修改结果类型
        if (response.getData().getDiseaseAmountStrategyResult() != null ) {
            strategyResult.setStrategyType(DiseaseStrategyEnum.AMOUNT_REASONABLE.getCode());
        }
        workOrderExtService.save(param.getCaseId(), param.getWorkOrderId(),
                WorkOrderExtContentTypeEnum.INITIAL_DISEASE_STRATEGY, strategyResult);

        return strategyResult;
    }

    private boolean canQueryStrategy(long workOrderId) {
        Response<WorkOrderVO> workOrderVO = cfWorkOrderClient.getWorkOrderById(workOrderId);
        if (workOrderVO == null || workOrderVO.getData() == null) {
            log.info("没有查询到工单数据 workOrderId:{}", workOrderId);
            return true;
        }

        return  !(workOrderVO.getData().getOrderType() == WorkOrderType.yiliaoshenhe.getType()
                || workOrderVO.getData().getOrderType() == WorkOrderType.bu_chong_yi_yuan_xin_xi.getType());
    }

    @NotNull
    private DiseaseStrategyResult initStrategyResult(DiseaseStrategyRequest request, DiseaseStrategyResult strategyResult) {
        if (strategyResult == null){
            strategyResult = new DiseaseStrategyResult();
        } else {
            if (request.getExecuteStrategyEnum() != DiseaseStrategyEnum.AMOUNT_REASONABLE.getCode()){
                strategyResult.setAmountReasonableResultHit("");
                strategyResult.setAmountReasonableResult(null);
                strategyResult.setAmountReasonableResultDesc("");
            }
        }
        return strategyResult;
    }

    private void setDiseaseInfoList(DiseaseStrategyRequest request, List<String> diseaseResourceList, Response<DiseaseStrategyResponse> response, DiseaseStrategyResult strategyResult) {
        if (CollectionUtils.isNotEmpty(response.getData().getDiseaseInfoList())) {
            compatibilityRaiseInfoList(response.getData().getDiseaseInfoList(), strategyResult.getDiseaseInfoList(), diseaseResourceList);
            strategyResult.setDiseaseInfoList(getSortDiseaseVo(diseaseResourceList, response.getData().getDiseaseInfoList()));
        }
        if (CollectionUtils.isEmpty(strategyResult.getDiseaseInfoList()) || request.getExecuteStrategyEnum() != DiseaseStrategyEnum.AMOUNT_REASONABLE.getCode()){
            strategyResult.setMultipleDiseaseChoiceInfoList(Lists.newArrayList());
        }
    }

    private void setSpecialDiseaseChoiceInfoList(Response<DiseaseStrategyResponse> response, DiseaseStrategyResult strategyResult) {
        if (response.getData().getDiseaseRaiseStrategyResult() != null) {
            strategyResult.setSpecialDiseaseChoiceInfoList(response.getData().getDiseaseRaiseStrategyResult().getSpecialDiseaseChoiceInfoList());
        } else {
            strategyResult.setSpecialDiseaseChoiceInfoList(Lists.newArrayList());
        }
        if (response.getData().getDiseaseAmountStrategyResult() != null) {
            strategyResult.setSpecialDiseaseChoiceInfoList(response.getData().getDiseaseAmountStrategyResult().getSpecialDiseaseChoiceInfoList());
            strategyResult.setMultipleDiseaseChoiceInfoList(response.getData().getDiseaseAmountStrategyResult().getMulitipleDiseaseChoiceInfoList());
        }
    }

    private void compatibilityRaiseInfoList(List<DiseaseInfoVo> diseaseInfoVos,
                                            List<DiseaseInfoVo> beforeDiseaseInfoList,
                                            List<String> diseaseResourceList) {
        if (CollectionUtils.isEmpty(diseaseInfoVos)  || CollectionUtils.isEmpty(beforeDiseaseInfoList)){
            return;
        }
        if (diseaseInfoVos.size() == beforeDiseaseInfoList.size()) {
            return;
        }
        //将发起存在的疾病信息兼容老的疾病信息
        List<String> diseaseNameList = diseaseInfoVos.stream().map(DiseaseInfoVo::getDiseaseName).collect(Collectors.toList());
        for (DiseaseInfoVo diseaseInfoVo : beforeDiseaseInfoList) {
            if (!diseaseResourceList.contains(diseaseInfoVo.getDiseaseName()) || diseaseNameList.contains(diseaseInfoVo.getDiseaseName())){
                continue;
            }
            diseaseInfoVos.add(diseaseInfoVo);
        }
    }

    private List<DiseaseInfoVo> getSortDiseaseVo(List<String> diseaseNameList,
                                                 List<DiseaseInfoVo> diseaseInfoList) {

        if (CollectionUtils.isEmpty(diseaseNameList) || CollectionUtils.isEmpty(diseaseInfoList)) {
            return diseaseInfoList;
        }

        List<DiseaseInfoVo> result = Lists.newArrayList();
        Set<String> hasAdds = Sets.newHashSet();
        for (String diseaseName : diseaseNameList) {
            for (DiseaseInfoVo infoVo : diseaseInfoList) {

                if (Objects.equals(Objects.requireNonNullElse(diseaseName, "").trim(),
                        Objects.requireNonNullElse(infoVo.getDiseaseName(), "").trim())) {
                    result.add(infoVo);
                    hasAdds.add(Objects.requireNonNullElse(diseaseName, "").trim());
                    break;
                }
            }
        }

        if (result.size() != diseaseInfoList.size()) {
            for (DiseaseInfoVo infoVo : diseaseInfoList) {
                if (!hasAdds.contains(Objects.requireNonNullElse(infoVo.getDiseaseName(), "").trim())) {
                    result.add(infoVo);
                }
            }
        }
        return result;
    }

    private void fillStrategyFitDesc(DiseaseStrategyRequest request, DiseaseStrategyResponse response,
                             DiseaseStrategyResult strategyResult) {

        // 关于文案展示
        if (request.getExecuteStrategyEnum() == DiseaseStrategyEnum.USER_WRITE.getCode()) {
            strategyResult.clearContentInfo();
            InitialAuditJudgeNameConsistencyEnum.fillStrategyFitDescByUserWrite(strategyResult, response);
        }

        if (request.getExecuteStrategyEnum() == DiseaseStrategyEnum.MANUAL_WRITE.getCode()
            || request.getExecuteStrategyEnum() == DiseaseStrategyEnum.OCR.getCode()) {
            strategyResult.clearContentInfo();
            InitialAuditJudgeNameConsistencyEnum.fillStrategyFitDescByManual(strategyResult, response);
        }
        InitialAuditJudgeNameConsistencyEnum.fillAmountReasonInfo(strategyResult, response);
    }

    public List<DiseaseStrategyLog> selectDiseaseStrategyLog(int caseId) {
        Response<List<DiseaseStrategyLog>> diseaseResult = diseaseClient.strategyResult(caseId);

        return diseaseResult != null ? diseaseResult.getData() : Lists.newArrayList();
    }

    public void recordErrorLog(InitialReportErrorParam errorParam) {

        OperationRecord record = commonOperationRecordClient.create()
                .buildBasicPlatform(errorParam.getWorkOrderId(), Math.toIntExact(errorParam.getUserId()),
                        OperationActionTypeEnum.OCR_APPROVE_REPORT_ERROR)
                .buildCaseId(errorParam.getCaseId());

        RpcResult<RaiseBasicInfoModel> basicInfo = raiseClient.selectRaiseBasicInfo(errorParam.getCaseId());

        String diseaseName = basicInfo != null && basicInfo.getData() != null ?
                basicInfo.getData().getDiseaseName() : "";

        String remark = JSON.toJSONString(
                new InitialReportErrorParam.OperatorDetails().builder()
                .errorSource(errorParam.getErrorSource())
                .patientName(errorParam.getPatientName())
                .tips(errorParam.getTips())
                .errorContent(errorParam.getErrorContent())
                .imgUrl(errorParam.getImageUrl())
                .diseaseName(diseaseName)
                        .build());

        log.info("保存报错动作的信息 msg:{}", record);

        record.buildRemark(remark).save();
    }

    public RaiseBasicInfoModel selectRaiseBasicInfo(int caseId) {
        RpcResult<RaiseBasicInfoModel> modelResult = raiseClient.selectRaiseBasicInfo(caseId);

        if (modelResult != null && modelResult.getData() != null) {
            return modelResult.getData();
        }

        log.info("不能知道案例在发起时填写的疾病信息 从增长组取.caseId:{}", caseId);
        CfCaseSpecialPrePoseDetail prePoseDetail = preposeDelegate.getSpecialPrePoseDetailByCaseId(caseId);
        if (prePoseDetail == null ||  prePoseDetail.getPreposeId() == 0) {
            return null;
        }

        RpcResult<PreposeMaterialModel.MaterialInfoVo> materialResult = preposeClient.selectMaterialsById(prePoseDetail
                .getPreposeId());
        if (materialResult == null || materialResult.getData() == null) {
            return null;
        }

        // 优先用医疗材料中的疾病，没有填写的话就用患者自述疾病
        RaiseBasicInfoModel basicResult = new RaiseBasicInfoModel();
        if (StringUtils.isNotBlank(materialResult.getData().getMedicalDisease())) {
            basicResult.setDiseaseName(materialResult.getData().getMedicalDisease());
            return basicResult;
        }
        if (StringUtils.isNotBlank(materialResult.getData().getPatientRpDisease())) {
            basicResult.setDiseaseName(materialResult.getData().getPatientRpDisease());
            return basicResult;
        }

        return null;
    }
}
