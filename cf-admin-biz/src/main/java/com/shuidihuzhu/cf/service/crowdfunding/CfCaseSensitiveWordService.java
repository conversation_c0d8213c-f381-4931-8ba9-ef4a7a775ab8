package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.admin.util.AdminListUtil;
import com.shuidihuzhu.cf.biz.admin.AdminCfOperatingRecordBiz;
import com.shuidihuzhu.cf.util.wordfilter.SensitivewordFilter;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.cf.biz.crowdfunding.CfBlacklistWordBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.CfBlacklistWordTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> Ahrievil
 */
@Slf4j
@Service
public class CfCaseSensitiveWordService {

    private static final String KEY = "cf-blacklist-word-case";

    private SensitivewordFilter sensitivewordFilter;
    @Autowired
    private CfBlacklistWordBiz cfBlacklistWordBiz;

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    public Set<String> get(String target) {
        String set = null;
        try {
            set = redissonHandler.get(KEY, String.class);
        } catch (Exception e) {
            log.error("CfCaseSensitiveWordService get error msg", e);
        }
        if (StringUtils.isBlank(set)) {
            setCache();
            reload();
        }
        if(sensitivewordFilter == null) {
            sensitivewordFilter = SensitivewordFilter.build(this.getDiseaseList());
        }
        return sensitivewordFilter.getSensitiveWord(target, 1);
    }

    private void setCache() {
        try {
            redissonHandler.setEX(KEY, "miao", 60 * 60 * 1000L);
        } catch (Exception e) {
            log.error("CfCaseSensitiveWordService setCache error msg", e);
        }
    }

    private void reload() {
        if (sensitivewordFilter == null) {
            build();
        } else {
            sensitivewordFilter.reload(this.getDiseaseList());
        }
    }

    private void build() {
        if(sensitivewordFilter == null) {
            sensitivewordFilter = SensitivewordFilter.build(this.getDiseaseList());
        }
    }

    private Set<String> getDiseaseList() {
        List<String> list = AdminListUtil.getList(3000,
                (start, size) -> cfBlacklistWordBiz.selectAllWordsLimit(CfBlacklistWordTypeEnum.CROWDFUNDING, start, size));
        return Sets.newHashSet(list);
    }

    public int getWordSize() {
        return this.sensitivewordFilter.getSensitiveWordSize();
    }

}
