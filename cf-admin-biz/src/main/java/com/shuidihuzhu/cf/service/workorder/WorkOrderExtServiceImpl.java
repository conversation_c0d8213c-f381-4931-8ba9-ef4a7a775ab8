package com.shuidihuzhu.cf.service.workorder;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.adminpure.enums.WorkOrderExtContentTypeEnum;
import com.shuidihuzhu.cf.dao.admin.workorder.AdminWorkOrderExtDAO;
import com.shuidihuzhu.cf.domain.cf.WorkOrderExt;
import com.shuidihuzhu.cf.model.admin.workorder.FirsApproveMaterialWithAmount;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.vo.approve.CfHospitalAuditInfoNew;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018-12-06  15:17
 */
@Service
@Slf4j
public class WorkOrderExtServiceImpl implements WorkOrderExtService {

    @Resource
    private AdminWorkOrderExtDAO adminWorkOrderExtDAO;

    @Override
    public void save(long workOrderId, WorkOrderExtContentTypeEnum type, Object data) {
        save(0, workOrderId, type, data);
    }

    @Override
    public void save(int caseId, long workOrderId, WorkOrderExtContentTypeEnum type, Object data) {
        WorkOrderExt ext = new WorkOrderExt();
        ext.setCaseId(caseId);
        ext.setWorkOrderId(workOrderId);
        ext.setContent(JSON.toJSONString(data));
        ext.setContentType(type.getValue());
        boolean res = adminWorkOrderExtDAO.save(ext) > 0;
        if (!res) {
            log.info("{}", workOrderId);
        }
    }

    @Override
    public <T> T getByClazz(long workOrderId, WorkOrderExtContentTypeEnum typeEnum, Class<T> clazz) {
        WorkOrderExt ext = getWorkOrderExt(workOrderId, typeEnum);
        if (ext == null) {
            return null;
        }
        String content = ext.getContent();
        return JSON.parseObject(content, clazz);//已检查过
    }

    @Override
    public <T> T getByCaseIdAndClazz(int caseId, WorkOrderExtContentTypeEnum typeEnum, Class<T> clazz) {
        WorkOrderExt ext = adminWorkOrderExtDAO.listNearlyByCaseIdAndType(caseId, typeEnum.getValue());
        if (ext == null) {
            return null;
        }
        String content = ext.getContent();
        return JSON.parseObject(content, clazz);//已检查过
    }


    @Override
    public <T> List<T> listByCaseIdAndClazz(int caseId, WorkOrderExtContentTypeEnum typeEnum, Class<T> clazz) {
        List<WorkOrderExt> exts = listByCaseIdAndType(caseId, typeEnum);
        if (CollectionUtils.isEmpty(exts)) {
            return Lists.newArrayList();
        }
        return exts.stream()
                .map(v -> JSON.parseObject(v.getContent(), clazz))//已检查过
                .collect(Collectors.toList());
    }

    @Override
    public WorkOrderExt getWorkOrderExt(long wordOrderId, WorkOrderExtContentTypeEnum contentType) {
        return adminWorkOrderExtDAO.get(wordOrderId, contentType.getValue());
    }

    @Override
    public FirsApproveMaterialWithAmount getFirstApproveWorkOrderExt(int wordOrderId) {

        // 获取新版数据
        FirsApproveMaterialWithAmount r1 = getByClazz(wordOrderId, WorkOrderExtContentTypeEnum.FIRST_APPROVE, FirsApproveMaterialWithAmount.class);
        if (r1 != null) {
            return r1;
        }

        // 没有新版 获取老版
        CfFirsApproveMaterial r2 = getByClazz(wordOrderId, WorkOrderExtContentTypeEnum.DEFAULT, CfFirsApproveMaterial.class);
        if (r2 == null) {
            return null;
        }
        FirsApproveMaterialWithAmount v = new FirsApproveMaterialWithAmount();
        v.setCfFirsApproveMaterial(r2);
        return v;
    }

    @Override
    public void saveFirstApproveWorkOrderExt(long workOrderId, CfFirsApproveMaterial data, int targetAmount) {
        FirsApproveMaterialWithAmount v = new FirsApproveMaterialWithAmount();
        v.setCfFirsApproveMaterial(data);
        v.setTargetAmount(targetAmount);
        save(workOrderId, WorkOrderExtContentTypeEnum.FIRST_APPROVE, v);
    }

    @Override
    public void saveHospitalAuditSnapshot(int caseId, long workOrderId, CfHospitalAuditInfoNew data) {
        long id= workOrderId;
        //快照表有唯一索引  如果工单id空  创建唯一值   此处容易重复  近5年无问题
        if (workOrderId == 0){
            LocalDateTime time=LocalDateTime.now();
            int a = time.getNano()/10000;
            id = Long.parseLong(""+caseId+a+data.getAuditStatus());
        }

        save(caseId, id, WorkOrderExtContentTypeEnum.HOSPITAL_AUDIT_SNAPSHOT, data);


    }

    @Override
    public List<CfHospitalAuditInfoNew> listHospitalAuditSnapshot(int caseId) {
        return listByCaseIdAndClazz(caseId, WorkOrderExtContentTypeEnum.HOSPITAL_AUDIT_SNAPSHOT, CfHospitalAuditInfoNew.class);
    }

    @Override
    public CfHospitalAuditInfoNew getWorkOrderHospitalAuditSnapshot(long wordOrderId) {
        return getByClazz(wordOrderId, WorkOrderExtContentTypeEnum.HOSPITAL_AUDIT_SNAPSHOT, CfHospitalAuditInfoNew.class);
    }

    @Override
    public List<WorkOrderExt> listByCaseIdAndType(int caseId, WorkOrderExtContentTypeEnum contentType) {
        return adminWorkOrderExtDAO.listByCaseIdAndType(caseId, contentType.getValue());
    }
}
