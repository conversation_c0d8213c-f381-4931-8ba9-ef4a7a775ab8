package com.shuidihuzhu.cf.service.tag.impl;

import com.google.common.base.Splitter;
import com.shuidihuzhu.cf.enums.CaseLabelConditionEnum;
import com.shuidihuzhu.cf.enums.maskcode.MaskTypeEnum;
import com.shuidihuzhu.cf.model.admin.AdminMaskParam;
import com.shuidihuzhu.cf.model.label.CfCaseLabelCondition;
import com.shuidihuzhu.cf.model.label.CfCaseLabelRule;
import com.shuidihuzhu.cf.model.label.CfCaseMetricsInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.function.Function;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/8/9 8:11 PM
 */
@Slf4j
@Service
public class LabelRuleProcessService {

    /**
     * 规则分派策略
     */
    private final static Map<String, BiFunction<CfCaseLabelCondition, CfCaseMetricsInfo, Boolean>> conditionStrategy = new HashMap<>();

    /**
     * 初始化分派策略
     */
    @PostConstruct
    public void initMaskStrategy() {
        conditionStrategy.put(CaseLabelConditionEnum.TARGET_AMOUNT_CONDITION.getDescription(), this::processRangeCondition);
        conditionStrategy.put(CaseLabelConditionEnum.NORM_DISEASE_CONDITION.getDescription(), this::processContainsCondition);
        conditionStrategy.put(CaseLabelConditionEnum.PATIENT_AGE_CONDITION.getDescription(), this::processRangeCondition);
        conditionStrategy.put(CaseLabelConditionEnum.CASE_TYPE_CONDITION.getDescription(), this::processContainsCondition);
        conditionStrategy.put(CaseLabelConditionEnum.PATIENT_IDENTITY_CONDITION.getDescription(), this::processContainsCondition);
        conditionStrategy.put(CaseLabelConditionEnum.CASE_PROVINCE_CONDITION.getDescription(), this::processContainsCondition);
        conditionStrategy.put(CaseLabelConditionEnum.USER_RELATION_TYPE.getDescription(), this::processContainsCondition);
        conditionStrategy.put(CaseLabelConditionEnum.REPEAT_LABEL.getDescription(), this::processContainsCondition);
        conditionStrategy.put(CaseLabelConditionEnum.MEDICAL_CITY.getDescription(), this::processContainsCondition);
        conditionStrategy.put(CaseLabelConditionEnum.PREDICT_DONATE_COUNT.getDescription(), this::processRangeCondition);
        conditionStrategy.put(CaseLabelConditionEnum.MAJOR_CASE_TAG.getDescription(), this::processContainsCondition);
        conditionStrategy.put(CaseLabelConditionEnum.PATIENT_PROFESSION.getDescription(), this::processContainsCondition);
    }

    public Boolean processRule(CfCaseLabelRule cfCaseLabelRule, CfCaseMetricsInfo cfCaseMetricsInfo) {

        // 遍历条件，如果有一个不满足，直接返回false
        List<CfCaseLabelCondition> conditionList = cfCaseLabelRule.getConditionList();
        for (CfCaseLabelCondition cfCaseLabelCondition : conditionList) {
            if (StringUtils.isBlank(cfCaseLabelCondition.getConditionName())) {
                return false;
            }
            BiFunction<CfCaseLabelCondition, CfCaseMetricsInfo, Boolean> function = conditionStrategy.get(cfCaseLabelCondition.getConditionName());
            if (function == null) {
                return false;
            }
            Boolean conditionResult = function.apply(cfCaseLabelCondition, cfCaseMetricsInfo);
            if (!conditionResult) {
                return false;
            }
        }

        return true;

    }

    private Boolean processContainsCondition(CfCaseLabelCondition cfCaseLabelCondition, CfCaseMetricsInfo cfCaseMetricsInfo) {

        try {
            String fieldName = CaseLabelConditionEnum.getFieldName(cfCaseLabelCondition.getConditionName());
            if (StringUtils.isBlank(fieldName)) {
                return false;
            }
            List<String> targetList = (List<String>) cfCaseMetricsInfo.getFieldValue(fieldName);
            if (CollectionUtils.isEmpty(targetList)) {
                return false;
            }

            Map<String, Object> conditionDetail = cfCaseLabelCondition.getConditionDetail();
            if (conditionDetail == null) {
                return false;
            }

            String content = (String) conditionDetail.get("content");
            if (StringUtils.isBlank(content)) {
                return false;
            }

            return judgeContains(targetList, content);

        } catch (Exception e) {
            log.error("processContainsCondition error, cfCaseLabelCondition:{}", cfCaseLabelCondition, e);
        }

        return false;

    }

    private Boolean processRangeCondition(CfCaseLabelCondition cfCaseLabelCondition, CfCaseMetricsInfo cfCaseMetricsInfo) {

        try {
            String fieldName = CaseLabelConditionEnum.getFieldName(cfCaseLabelCondition.getConditionName());
            if (StringUtils.isBlank(fieldName)) {
                return false;
            }
            Integer targetNum = (Integer) cfCaseMetricsInfo.getFieldValue(fieldName);
            if (targetNum == null) {
                return false;
            }

            Map<String, Object> conditionDetail = cfCaseLabelCondition.getConditionDetail();
            if (conditionDetail == null) {
                return false;
            }

            String minOperate = (String) conditionDetail.get("minOperate");
            Integer minNum = (Integer) conditionDetail.get("minNum");
            String maxOperate = (String) conditionDetail.get("maxOperate");
            Integer maxNum = (Integer) conditionDetail.get("maxNum");
            if (StringUtils.isBlank(minOperate) || minNum == null
                    || StringUtils.isBlank(maxOperate) || maxNum == null) {
                return false;
            }

            return judgeSize(targetNum, minNum, minOperate) && judgeSize(targetNum, maxNum, maxOperate);

        } catch (Exception e) {
            log.error("processRangeCondition error, cfCaseLabelCondition:{}", cfCaseLabelCondition, e);
        }

        return false;
    }

    private Boolean judgeContains(List<String> targetList, String content) {
        List<String> contentList = Splitter.on(",").splitToList(content);
        for (String target : targetList) {
            if (contentList.contains(target)) {
                return true;
            }
        }
        return false;
    }

    private Boolean judgeSize(Integer targetNum, Integer compareNum, String operate) {
        if (">".equals(operate)) {
            return targetNum > compareNum;
        } else if (">=".equals(operate)) {
            return targetNum >= compareNum;
        } else if ("<".equals(operate)) {
            return targetNum < compareNum;
        } else if ("<=".equals(operate)) {
            return targetNum <= compareNum;
        } else {
            return false;
        }
    }

    public static void main(String[] args) throws NoSuchFieldException, IllegalAccessException {
        CfCaseMetricsInfo cfCaseMetricsInfo = new CfCaseMetricsInfo();
        cfCaseMetricsInfo.setAge(18);
        Object value = cfCaseMetricsInfo.getFieldValue("age");
        System.out.println(value);

    }

}
