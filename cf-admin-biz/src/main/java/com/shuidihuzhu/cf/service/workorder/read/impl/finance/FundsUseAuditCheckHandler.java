package com.shuidihuzhu.cf.service.workorder.read.impl.finance;

import com.shuidihuzhu.cf.service.workorder.read.WorkOrderReadService;
import com.shuidihuzhu.cf.service.workorder.read.impl.AbstractFinanceWorkOrderCheckHandler;
import com.shuidihuzhu.client.cf.workorder.helper.Von;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.cf.workorder.read.BasicWorkOrder;
import com.shuidihuzhu.common.web.model.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 患者姓名 patientName
 * 资金用途描述   fundUseContent
 * 花费票据图片   fundUseImageMaterial
 * 案例是否被举报  hasReport
 */
@Component
public class FundsUseAuditCheckHandler extends AbstractFinanceWorkOrderCheckHandler {

    @Resource
    private WorkOrderReadService workOrderReadService;

    @Override
    public WorkOrderType getWorkOrderType() {
        return WorkOrderType.funduseshenhe;
    }

    @Override
    public Map<String, Object> getExtInfo(long workOrderId, int orderType) {
        Response<BasicWorkOrder> response =
                Von.read().getOrderBasicInfoById(workOrderId);
        if (response.ok() && response.getData() != null) {
            BasicWorkOrder basicWorkOrder = response.getData();
            List<WorkOrderExt> workOrderExt = basicWorkOrder.getBizExtList();
            if (CollectionUtils.isNotEmpty(workOrderExt)) {
                Optional<WorkOrderExt> fundUseProgressIdOp = workOrderExt.stream()
                        .filter(r -> StringUtils.equals(r.getExtName(), "fundUseProgressId")).findFirst();
                if (fundUseProgressIdOp.isPresent()
                        && StringUtils.isNotBlank(fundUseProgressIdOp.get().getExtValue())) {
                    return workOrderReadService.getFundUseAuditInfo4Transfer(workOrderId, orderType, Integer.parseInt(fundUseProgressIdOp.get().getExtValue()));
                }
            }
        }
        return Collections.emptyMap();
    }
}
