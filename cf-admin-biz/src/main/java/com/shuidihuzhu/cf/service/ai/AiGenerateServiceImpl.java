package com.shuidihuzhu.cf.service.ai;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.admin.constant.AsyncPoolConstants;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.dao.ai.AiFeedbackDao;
import com.shuidihuzhu.cf.dao.ai.AiGenerateDao;
import com.shuidihuzhu.cf.dao.ai.AiPromptConfigDao;
import com.shuidihuzhu.cf.dao.ai.AiStagingDao;
import com.shuidihuzhu.cf.enums.ai.AiGenerateEnum;
import com.shuidihuzhu.cf.enums.ai.AiModelEnum;
import com.shuidihuzhu.cf.model.ai.AiGenerateRecord;
import com.shuidihuzhu.cf.model.ai.AiPromptConfig;
import com.shuidihuzhu.cf.model.ai.DO.AiStagingDO;
import com.shuidihuzhu.cf.model.ai.DO.FeedbackDO;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.util.crowdfunding.CharacterUtil;
import com.shuidihuzhu.client.cf.admin.enums.AiMockDataEnum;
import com.shuidihuzhu.client.cf.admin.model.*;
import com.shuidihuzhu.client.model.ChatChunk;
import com.shuidihuzhu.client.model.ChatCompletionChunk;
import com.shuidihuzhu.client.model.ChatParam;
import com.shuidihuzhu.client.model.ChatStreamResult;
import com.shuidihuzhu.client.model.enums.AiPromptBizEnum;
import com.shuidihuzhu.client.util.AiBaseFieldMapping;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2024/5/29 7:33 PM
 */
@Slf4j
@Service
public class AiGenerateServiceImpl implements AiGenerateService {

    @Resource
    private WenXinAiModel wenXinAiModel;
    @Resource
    private DouBaoAiModel douBaoAiModel;
    @Resource
    private ZhiPuAiModel zhiPuAiModel;
    @Resource
    private MoonShotAiModel moonShotAiModel;
    @Resource
    private TongYiAiModel tongYiAiModel;
    @Resource
    private DeepSeekR1Model deepSeekR1Model;

    @Resource
    private AiFeedbackDao aiFeedbackDao;
    @Resource
    private AiStagingDao aiStagingDao;
    @Resource
    private AiGenerateDao aiGenerateDao;
    @Resource
    private AiPromptConfigDao aiPromptConfigDao;
    @Resource
    private Producer producer;

    @Resource(name = AsyncPoolConstants.AI_GENERATE_ASYNC_POOL)
    private Executor executor;

    /**
     * 用哪个大模型
     */
    private final static Map<Integer, Function<AIGenerateParam, List<AiGenerateResult>>> aiModelStrategy = new HashMap<>();
    private final static Map<Integer, Function<String, Flux<ChatChunk<ChatCompletionChunk>>>> aiStreamStrategy = new HashMap<>();

    /**
     * 初始化掩码策略
     */
    @PostConstruct
    public void initAiModelStrategy() {

        aiModelStrategy.put(AiModelEnum.WEN_XIN.getCode(), param -> wenXinAiModel.generateContent(param));
        aiModelStrategy.put(AiModelEnum.MOON_SHOT.getCode(), param -> moonShotAiModel.generateContent(param));
        aiModelStrategy.put(AiModelEnum.TONG_YI.getCode(), param -> tongYiAiModel.generateContent(param));
        aiModelStrategy.put(AiModelEnum.DEEPSEEK_R1.getCode(), param -> deepSeekR1Model.generateContent(param));

        aiStreamStrategy.put(AiModelEnum.WEN_XIN.getCode(), prompt -> wenXinAiModel.stream(prompt));
        aiStreamStrategy.put(AiModelEnum.MOON_SHOT.getCode(), prompt -> moonShotAiModel.stream(prompt));
        aiStreamStrategy.put(AiModelEnum.TONG_YI.getCode(), prompt -> tongYiAiModel.stream(prompt));
        aiStreamStrategy.put(AiModelEnum.DEEPSEEK_R1.getCode(), prompt -> deepSeekR1Model.stream(prompt));
    }


    @Override
    public void generate(AIGenerateParam aiGenerateParam) {

        if (Objects.isNull(aiGenerateParam)) {
            return ;
        }

        List<CompletableFuture<Void>> futures = Lists.newArrayList();

        // 异步生成内容
        aiModelStrategy.forEach((key, function) -> {
            aiGenerateParam.setModelType(key);
            CompletableFuture<Void> future = CompletableFuture.supplyAsync(() -> function.apply(aiGenerateParam), executor)
                    .thenAccept(results -> {
                        String json = CollectionUtils.isEmpty(results) ? "" : JSONObject.toJSONString(results);
                        // 保存处理结果
                        AiGenerateRecord aiGenerateRecord = AiGenerateRecord.builder()
                                .aiGenerateResults(json)
                                .uuid(aiGenerateParam.getUuid())
                                .generateType(aiGenerateParam.getGenerateType())
                                .modelType(key)
                                .clewId(aiGenerateParam.getClewId())
                                .operatorId(aiGenerateParam.getOperatorId())
                                .build();
                        aiGenerateDao.insert(aiGenerateRecord);
                    })
                    .exceptionally(e -> {
                        log.error("AiGenerateServiceImpl generate error", e);
                        return null;
                    });
            futures.add(future);
        });

        // 等待所有的CompletableFuture任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        // 检查一下，补空数据
        List<AiGenerateRecord> aiGenerateRecords = aiGenerateDao.selectRecordByUuid(aiGenerateParam.getUuid());
        List<Integer> modelTypes = aiGenerateRecords.stream().map(AiGenerateRecord::getModelType).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(aiGenerateRecords) || aiGenerateRecords.size() != 4) {
            AiModelEnum[] values = AiModelEnum.values();
            for (AiModelEnum value : values) {
                if (modelTypes.contains(value.getCode())) {
                    continue;
                }
                AiGenerateRecord aiGenerateRecord = AiGenerateRecord.builder()
                        .aiGenerateResults("")
                        .uuid(aiGenerateParam.getUuid())
                        .generateType(aiGenerateParam.getGenerateType())
                        .modelType(value.getCode())
                        .clewId(aiGenerateParam.getClewId())
                        .operatorId(aiGenerateParam.getOperatorId())
                        .build();
                aiGenerateDao.insert(aiGenerateRecord);
            }
        }


    }

    @Override
    public AiGenerateForwardResult generateForwardToSendMq(AiGenerateForwardParam aiGenerateForwardParam) {

        if (Objects.isNull(aiGenerateForwardParam) || Objects.isNull(aiGenerateForwardParam.getModelType())) {
            log.info("AiGenerateServiceImpl generateForwardToSendMq aiGenerateForwardParam is null");
            return getAiGenerateForwardResult(aiGenerateForwardParam, "");
        }

        // 后端先上线，要支持老的转发场景
        if (Objects.isNull(aiGenerateForwardParam.getGenerateType())) {
            aiGenerateForwardParam.setGenerateType(AiGenerateEnum.FORWARD.getCode());
        }

        // 1. 获取AI模型策略函数
        Function<AIGenerateParam, List<AiGenerateResult>> function = aiModelStrategy.get(aiGenerateForwardParam.getModelType());
        if (Objects.isNull(function)) {
            log.warn("无法获取AI模型策略函数，modelType: {}", aiGenerateForwardParam.getModelType());
            return getAiGenerateForwardResult(aiGenerateForwardParam, "");
        }

        // 2. 准备AI生成参数
        AIGenerateParam aiGenerateParam = AIGenerateParam.builder()
                .generateType(aiGenerateForwardParam.getGenerateType())
                .modelType(aiGenerateForwardParam.getModelType())
                .bizType(aiGenerateForwardParam.getBizType())
                .aiGenerateBaseInfo(aiGenerateForwardParam.getAiGenerateBaseInfo())
                .build();

        // 3. 调用AI模型生成结果
        List<AiGenerateResult> aiGenerateResults = function.apply(aiGenerateParam);
        if (CollectionUtils.isEmpty(aiGenerateResults)) {
            log.warn("AI生成结果为空，参数: {}", aiGenerateParam);
            return getAiGenerateForwardResult(aiGenerateForwardParam, "");
        }

        // 4. 构建返回对象
        String forwardContent = aiGenerateResults.get(0).getAiGenerateInfo();
        return getAiGenerateForwardResult(aiGenerateForwardParam, forwardContent);

    }

    /**
     * 发送AI转发结果到消息队列
     */
    private AiGenerateForwardResult getAiGenerateForwardResult(AiGenerateForwardParam param, String forwardContent) {
        AiGenerateForwardResult result = AiGenerateForwardResult.builder()
                .caseId(param.getCaseId())
                .modelType(param.getModelType())
                .msgId(param.getMsgId())
                .forwardContent(forwardContent)
                .build();

        return result;
    }


    @Override
    public Flux<ChatChunk<ChatStreamResult>> streamGenerate(AIGenerateParam aiGenerateParam) {

        // 获取配置的提示词
        String prompt = getPromptConfig(aiGenerateParam);

        // 填充提示词变量
        prompt = fillVariables(prompt, aiGenerateParam);

        // 脱敏映射
        Map<String, String> reflectMap = desensitization(aiGenerateParam);
        String realPrompt = reflectPrompt(reflectMap, prompt);

        // 用来保存ai生成的内容
        StringBuilder generateContent = new StringBuilder();

        // 流式生成内容
        Function<String, Flux<ChatChunk<ChatCompletionChunk>>> function = aiStreamStrategy.get(aiGenerateParam.getModelType());
        return Flux.create(sink -> {
            // 流式请求
            Flux<ChatChunk<ChatCompletionChunk>> chatCompletionChunkFlux = function.apply(realPrompt);
            chatCompletionChunkFlux = chatCompletionChunkFlux.doOnError(e -> {
                log.error("AiGenerateServiceImpl.streamGenerate [接收结果异常] generateContent:{}", generateContent.toString());
                sink.next(ChatChunk.error(1, "生成回答失败,接收结果异常"));
                sink.complete();
            });

            // 消费大模型的消息
            Disposable disposable = chatCompletionChunkFlux.subscribe(chunk -> {
                if (chunk == null) {
                    log.error("AiGenerateServiceImpl.streamGenerate [生成回答失败] chunk:{}", chunk);
                    return;
                }

                StringBuilder curFullContent = new StringBuilder();
                curFullContent.append(generateContent.toString());
                curFullContent = parseDesensitizationContent(curFullContent, reflectMap);
                // 流式结束
                if (chunk.isEnd()) {
                    // 保存生成内容
                    saveGenerateRecord(curFullContent.toString(), aiGenerateParam);

                    ChatStreamResult chatStreamResult = new ChatStreamResult();
                    sink.next(ChatChunk.end(chatStreamResult));
                    sink.complete();
                    return;
                }

                ChatCompletionChunk chatCompletionChunk = chunk.getData();
                if (Objects.isNull(chatCompletionChunk)) {
                    log.error("AiGenerateServiceImpl.streamGenerate [生成回答失败] chunk:{}", chunk);
                    return;
                }

                ChatCompletionChunk.Choice choice = chatCompletionChunk.getData();
                if (null != choice) {
                    generateContent.append(choice.getMessage());
                    curFullContent.append(choice.getMessage());
                    ChatStreamResult chatStreamResult = new ChatStreamResult();
                    chatStreamResult.setReplyContent(choice.getMessage());
                    chatStreamResult.setReplyReasonContent(choice.getReasoningMessage());
                    chatStreamResult.setReplyContentFull(curFullContent.toString());
                    chatStreamResult.setReflectMap(reflectMap);
                    sink.next(ChatChunk.success(chatStreamResult));
                }
            });
        });
    }

    @Override
    public Flux<ChatChunk<ChatStreamResult>> stream(ChatParam chatParam) {

        if (Objects.isNull(chatParam) || chatParam.getModelNum() == null) {
            log.info("AiGenerateServiceImpl stream param or modelNum is null {}", chatParam);
            return errorChunk();
        }

        Function<String, Flux<ChatChunk<ChatCompletionChunk>>> function = aiStreamStrategy.get(chatParam.getModelNum());
        if (Objects.isNull(function)) {
            log.info("AiGenerateServiceImpl stream modelNum is error {}", chatParam);
            return errorChunk();
        }

        // 用来保存ai生成的内容
        StringBuilder generateContent = new StringBuilder();

        return Flux.create(sink -> {
            // 流式请求
            Flux<ChatChunk<ChatCompletionChunk>> chatCompletionChunkFlux = function.apply(chatParam.getPrompt());
            chatCompletionChunkFlux = chatCompletionChunkFlux.doOnError(e -> {
                log.error("AiGenerateServiceImpl.stream [接收结果异常] generateContent:{}", generateContent.toString());
                sink.next(ChatChunk.error(1, "生成回答失败,接收结果异常"));
                sink.complete();
            });

            // 消费大模型的消息
            Disposable disposable = chatCompletionChunkFlux.subscribe(chunk -> {
                if (chunk == null) {
                    log.error("AiGenerateServiceImpl.stream [生成回答失败] chunk:{}", chunk);
                    return;
                }

                StringBuilder curFullContent = new StringBuilder();
                curFullContent.append(generateContent.toString());
                // 流式结束
                if (chunk.isEnd()) {
                    ChatStreamResult chatStreamResult = new ChatStreamResult();
                    sink.next(ChatChunk.end(chatStreamResult));
                    sink.complete();
                    return;
                }

                ChatCompletionChunk chatCompletionChunk = chunk.getData();
                if (Objects.isNull(chatCompletionChunk)) {
                    log.error("AiGenerateServiceImpl.stream [生成回答失败] chunk:{}", chunk);
                    return;
                }

                ChatCompletionChunk.Choice choice = chatCompletionChunk.getData();
                if (null != choice) {
                    generateContent.append(choice.getMessage());
                    curFullContent.append(choice.getMessage());
                    ChatStreamResult chatStreamResult = new ChatStreamResult();
                    chatStreamResult.setReplyContent(choice.getMessage());
                    chatStreamResult.setReplyReasonContent(choice.getReasoningMessage());
                    chatStreamResult.setReplyContentFull(curFullContent.toString());
                    sink.next(ChatChunk.success(chatStreamResult));
                }
            });
        });
    }


    @Override
    public void saveOrUpdatePromptConfig(AiPromptConfig aiPromptConfig) {

        if (Objects.isNull(aiPromptConfig)) {
            return;
        }
        int modelType;
        try {
            modelType = Integer.parseInt(aiPromptConfig.getModelTypeStr());
        } catch (NumberFormatException e) {
            log.info("ModelTypeStr parseInt error:{},{}", e, aiPromptConfig);
            return;
        }
        aiPromptConfig.setModelType(modelType);
        AiPromptConfig oldPromptConfig = aiPromptConfigDao.selectByGenerateType(aiPromptConfig.getGenerateType(), aiPromptConfig.getModelType(), aiPromptConfig.getBizType());
        if (Objects.isNull(oldPromptConfig)) {
            aiPromptConfigDao.insert(aiPromptConfig);
        } else {
            aiPromptConfigDao.updatePrompt(aiPromptConfig);
        }

    }

    @Override
    public AiPromptConfig queryPromptConfig(Integer generateType, String modelTypeStr, Integer bizType) {
        int modelType;
        try {
            modelType = Integer.parseInt(modelTypeStr);
        } catch (NumberFormatException e) {
            log.info("ModelTypeStr parseInt error:{},{}", e, modelTypeStr);
            return null;
        }
        return aiPromptConfigDao.selectByGenerateType(generateType, modelType, bizType);
    }

    @Override
    public AiPromptConfig queryToPromptConfig(Integer generateType, Integer modelType, Integer bizType) {
        return aiPromptConfigDao.selectByGenerateType(generateType, modelType, bizType);
    }

    @Override
    public List<AiGenerateRecordVO> queryGenerateRecord(QueryParam queryParam) {

        if (Objects.isNull(queryParam) || StringUtils.isBlank(queryParam.getUuid())) {
            return Lists.newArrayList();
        }

        List<AiGenerateRecord> aiGenerateRecords = aiGenerateDao.selectRecordByUuid(queryParam.getUuid());
        if (CollectionUtils.isEmpty(aiGenerateRecords)) {
            return Lists.newArrayList();
        }
        aiGenerateRecords = aiGenerateRecords.stream()
                .collect(Collectors.toMap(AiGenerateRecord::getModelType, record -> record, (oldValue, newValue) -> newValue))
                .values()
                .stream()
                .collect(Collectors.toList());

        List<AiGenerateRecordVO> aiGenerateRecordVOS = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(aiGenerateRecords)) {
            aiGenerateRecords.forEach(record -> {
                AiGenerateRecordVO vo = new AiGenerateRecordVO();
                vo.setModelType(record.getModelType());
                vo.setGenerateType(record.getGenerateType());
                vo.setClewId(record.getClewId());
                vo.setUuid(record.getUuid());
                vo.setAiGenerateResults(JSONObject.parseArray(record.getAiGenerateResults(), AiGenerateResult.class));
                aiGenerateRecordVOS.add(vo);
            });
        }
        return aiGenerateRecordVOS;
    }

    @Override
    public void stagingScheme(StagingParam stagingParam, long userId) {

        if (Objects.isNull(stagingParam)) {
            return;
        }

        // 保存或更新暂存记录
        AiStagingDO aiStagingDO = buildAiStagingDO(stagingParam, userId);

        AiStagingDO oldStagingDO = aiStagingDao.selectStagingInfo(stagingParam.getClewId(), stagingParam.getStagingType());
        if (Objects.nonNull(oldStagingDO)) {
            aiStagingDao.update(aiStagingDO);
        } else {
            aiStagingDao.insert(aiStagingDO);
        }
    }

    @Override
    public StagingParam queryStagingScheme(QueryParam queryParam) {
        if (Objects.isNull(queryParam)) {
            return null;
        }

        AiStagingDO aiStagingDO = aiStagingDao.selectStagingInfo(queryParam.getClewId(), queryParam.getStagingType());
        if (Objects.isNull(aiStagingDO)) {
            return null;
        }

        return buildStagingParam(aiStagingDO);
    }

    @Override
    public void feedback(FeedbackParam feedbackParam, long userId) {

        if (Objects.isNull(feedbackParam)) {
            return;
        }

        // 保存反馈记录
        FeedbackDO feedbackDO = buildFeedbackDO(feedbackParam, userId);
        aiFeedbackDao.insert(feedbackDO);
    }

    private Flux<ChatChunk<ChatStreamResult>> errorChunk() {
        ChatChunk errorChunk = new ChatChunk();
        errorChunk.setCode(-1);
        errorChunk.setMsg("fallback");
        errorChunk.setEnd(true);
        return Flux.just(errorChunk);
    }

    public static void main(String[] args) {
        StringBuilder content = new StringBuilder("龢龘是一个好孩子，他今年16岁了。");
        Map<String, String> reflectMap = Map.of("18", "16", "庞海瑞", "龢龘龗");
        content = parseDesensitizationContent(content, reflectMap);
        System.out.println(1);
    }

    private static StringBuilder parseDesensitizationContent(StringBuilder content, Map<String, String> reflectMap) {

        String contentStr = content.toString();
        for (Map.Entry<String, String> entry : reflectMap.entrySet()) {
            contentStr = StringUtil.replace(replaceCharacters(contentStr), entry.getValue(), entry.getKey());
        }

        return new StringBuilder(contentStr);
    }

    public static String replaceCharacters(String content) {
        content = CharacterUtil.replaceCharactersV1(content);
        return CharacterUtil.replaceCharactersV2(content);
    }

    private String reflectPrompt(Map<String, String> reflectMap, String realPrompt) {
        for (Map.Entry<String, String> entry : reflectMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            if (key != null && value != null) {
                realPrompt = realPrompt.replace(key, value);
            }
        }
        return realPrompt;
    }

    private Map<String, String> desensitization(AIGenerateParam aiGenerateParam) {

        if (Objects.isNull(aiGenerateParam)) {
            return Maps.newHashMap();
        }

        AiGenerateBaseInfo aiGenerateBaseInfo = aiGenerateParam.getAiGenerateBaseInfo();
        if (Objects.isNull(aiGenerateBaseInfo)) {
            return Maps.newHashMap();
        }

        Map<String, String> reflectMap = Maps.newConcurrentMap();

        Field[] allFields = aiGenerateBaseInfo.getClass().getDeclaredFields();
        for (Field field : allFields) {
            try {
                AiBaseFieldMapping fieldMapping = field.getAnnotation(AiBaseFieldMapping.class);
                if (fieldMapping == null
                        || fieldMapping.aiMockData() == null
                        || fieldMapping.aiMockData() == AiMockDataEnum.DEFAULT) {
                    continue;
                }

                field.setAccessible(true);

                Object value = field.get(aiGenerateBaseInfo); // 获取字段的值
                if (value == null) {
                    continue;
                }

                if (fieldMapping.aiMockData() == AiMockDataEnum.AGE_MOCK) {
                    Integer age = (Integer) value;
                    reflectMap.put(String.valueOf(age), String.valueOf(age + 2));
                    field.set(aiGenerateBaseInfo, age + 2);
                    continue;
                }

                if (fieldMapping.aiMockData() == AiMockDataEnum.PATIENT_NAME_MOCK
                        || fieldMapping.aiMockData() == AiMockDataEnum.RAISER_NAME_MOCK) {
                    String[] mockData = fieldMapping.aiMockData().getMockData();
                    int index = (int) (Math.random() * mockData.length);
                    String name = mockData[index];
                    reflectMap.put(String.valueOf(value), name);
                    field.set(aiGenerateBaseInfo, mockData[index]);
                    continue;
                }

                String[] mockData = fieldMapping.aiMockData().getMockData();
                int index = (int) (Math.random() * mockData.length);
                reflectMap.put(String.valueOf(value), mockData[index]);
                field.set(aiGenerateBaseInfo, mockData[index]);


            } catch (Exception e) {
                log.error("通过注解拿取对象值错误。field:{}", field, e);
            }

        }

        return reflectMap;

    }

    private String fillVariables(String prompt, AIGenerateParam aiGenerateParam) {
        if (StringUtils.isEmpty(prompt) || Objects.isNull(aiGenerateParam)) {
            return "";
        }

        AiGenerateBaseInfo aiGenerateBaseInfo = aiGenerateParam.getAiGenerateBaseInfo();
        if (Objects.isNull(aiGenerateBaseInfo)) {
            return "";
        }

        try {
            Field[] fields = AiGenerateBaseInfo.class.getDeclaredFields(); // 获取所有字段
            for (Field field : fields) {
                AiBaseFieldMapping mapping = field.getAnnotation(AiBaseFieldMapping.class);
                if (mapping != null) {
                    field.setAccessible(true); // 确保可以访问私有字段
                    Object value = field.get(aiGenerateBaseInfo); // 获取字段的值
                    if (value != null) { // 确保值不为空
                        // 根据字段名进行特殊处理
                        switch (field.getName()) {
                            case "diseaseProcess":
                                if (!"本人".equals(aiGenerateBaseInfo.getRaisePatientRelation())) {
                                    value = "我的" + aiGenerateBaseInfo.getRaisePatientRelation() + aiGenerateBaseInfo.getPatientName() + value;
                                }
                                break;
                            default:
                                // 对于其他字段名，不做特殊处理
                                break;
                        }
                        String param = "${" + field.getName() + "}";
                        value = value + "";
                        prompt = prompt.replace(param, (String) value);
                    }
                }
            }
            return parseVariables(prompt);
        } catch (Exception e) {
            log.error("fillVariables error ", e);
        }

        return "";

    }

    private String parseVariables(String prompt) {
        // 使用 StringBuilder 来构建新的字符串
        StringBuilder resultBuilder = new StringBuilder();

        // 将字符串按行分割并迭代每一行
        String[] lines = prompt.split("\\r?\\n");
        for (String line : lines) {
            // 如果行不包含 "${"，则添加到结果中
            if (!line.contains("${")) {
                resultBuilder.append(line).append(System.lineSeparator());
            }
        }

        // 将StringBuilder转换回字符串
        return resultBuilder.toString();
    }

    private String getPromptConfig(AIGenerateParam aiGenerateParam) {

        if (Objects.isNull(aiGenerateParam) || Objects.isNull(aiGenerateParam.getGenerateType())) {
            return "";
        }

        AiPromptConfig aiPromptConfig = aiPromptConfigDao.selectByGenerateType(aiGenerateParam.getGenerateType(), aiGenerateParam.getModelType(), AiPromptBizEnum.OFFLINE.getCode());
        if (Objects.isNull(aiPromptConfig)) {
            return "";
        }
        return aiPromptConfig.getPrompt();
    }

    private void saveGenerateRecord(String generateStr, AIGenerateParam aiGenerateParam) {
        AiGenerateRecord aiGenerateRecord = AiGenerateRecord.builder()
                .aiGenerateResults(generateStr)
                .uuid(aiGenerateParam.getUuid())
                .generateType(aiGenerateParam.getGenerateType())
                .modelType(aiGenerateParam.getModelType())
                .clewId(aiGenerateParam.getClewId())
                .operatorId(aiGenerateParam.getOperatorId())
                .build();
        aiGenerateDao.insert(aiGenerateRecord);
    }

    private StagingParam buildStagingParam(AiStagingDO aiStagingDO) {
        return StagingParam.builder()
                .clewId(aiStagingDO.getClewId())
                .stagingType(aiStagingDO.getStagingType())
                .stagingBaseInfo(aiStagingDO.getStagingBaseInfo())
                .build();
    }

    private FeedbackDO buildFeedbackDO(FeedbackParam feedbackParam, long userId) {
        return FeedbackDO.builder()
                .feedbackInfo(feedbackParam.getFeedbackInfo())
                .feedbackType(feedbackParam.getFeedbackType())
                .operatorId(userId)
                .build();
    }

    private AiStagingDO buildAiStagingDO(StagingParam stagingParam, long userId) {
        return AiStagingDO.builder()
                .clewId(stagingParam.getClewId())
                .stagingBaseInfo(stagingParam.getStagingBaseInfo())
                .stagingType(stagingParam.getStagingType())
                .operatorId(userId)
                .build();
    }


}
