package com.shuidihuzhu.cf.service.sensitive.adapter;

import com.shuidihuzhu.cf.enums.crowdfunding.CfSensitiveWordRecordEnum;
import com.shuidihuzhu.cf.enums.risk.UgcTypeEnum;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfSensitiveWordRecordVo;

/**
 * <AUTHOR>
 */
public interface ISensitiveAdapter<T> {

    /**
     * 获取需要检测的内容
     * @param t
     * @return
     */
    String getContent(T t);

    /**
     * 创建对应命中记录
     *
     * @return
     */
    CfSensitiveWordRecordVo buildRecord(T t, String hitWord);

    /**
     * 获取ugc记录id eg: crowdfundingOrderId
     * @param data
     * @return
     */
    long getBizId(T data);

    int getCaseId(T data);

    /**
     * 获取ugc内容类型
     * @return
     */
    UgcTypeEnum getUgcTypeEnum();

    CfSensitiveWordRecordEnum.BizType getSensitiveRecordBizType(T data);

    long getUserId(T data);

    /**
     * 填充筹款信息
     *
     * @param cfSensitiveWordRecord
     * @param crowdfundingInfo
     */
    default void fillCaseInfo(T data, CfSensitiveWordRecordVo cfSensitiveWordRecord, CrowdfundingInfo crowdfundingInfo) {
        if (crowdfundingInfo != null) {
            cfSensitiveWordRecord.setInfoUuid(crowdfundingInfo.getInfoId());
            cfSensitiveWordRecord.setInfoId(crowdfundingInfo.getId());
        } else {
            cfSensitiveWordRecord.setInfoUuid("");
            int caseId = getCaseId(data);
            cfSensitiveWordRecord.setInfoId(caseId);
        }
    }

    /**
     * 是否执行自动处理
     * @return
     */
    default boolean needAutoHandle(){
        return false;
    }

    /**
     * 唯一标识
     *
     * @param t
     * @return
     */
    String getUniquelyIdentifies(T t);

    boolean needAiCheck(T data);
}