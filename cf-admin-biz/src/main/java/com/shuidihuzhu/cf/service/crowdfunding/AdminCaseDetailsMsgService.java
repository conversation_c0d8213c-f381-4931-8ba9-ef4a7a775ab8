package com.shuidihuzhu.cf.service.crowdfunding;

import com.alibaba.fastjson.JSONArray;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfRepeatInfoBiz;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.feign.AuthorFeignClient;
import com.shuidihuzhu.cf.client.feign.CfUserInfoFeignClient;
import com.shuidihuzhu.cf.client.material.feign.CfRaiseMaterialClient;
import com.shuidihuzhu.cf.client.material.feign.PreposeMaterialClient;
import com.shuidihuzhu.cf.client.material.model.CfBasicLivingGuardModel;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCaseDetailsMsgDao;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingUserDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.labels.CaseLabelsManagementService;
import com.shuidihuzhu.cf.model.CaseLabelsManagement;
import com.shuidihuzhu.cf.model.CaseLabelsManagementContent;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminCaseDetailsMsgVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.cf.risk.client.rpc.DiseaseClient;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.baseservice.pay.enums.BankCardVerifyEnum;
import com.shuidihuzhu.client.baseservice.verify.v1.enums.UserRelTypeEnum;
import com.shuidihuzhu.client.cf.admin.model.AdminCaseDetailsMsg;
import com.shuidihuzhu.client.cf.admin.model.SeaCaseLabelType;
import com.shuidihuzhu.client.util.TreatmentParse;
import com.shuidihuzhu.common.web.model.IdcardInfoExtractor;
import com.shuidihuzhu.common.web.model.Response;
import joptsimple.internal.Strings;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/11/25  2:10 下午
 */
@Slf4j
@Service
public class AdminCaseDetailsMsgService {
    @Autowired
    private AdminCaseDetailsMsgDao adminCaseDetailsMsgDao;

    @Autowired
    private CaseLabelsManagementService caseLabelsManagementService;

    @Autowired
    private AdminCfRepeatInfoBiz repeatInfoBizService;

    @Autowired
    private AuthorFeignClient authorFeignClient;

    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private ICrowdfundingUserDelegate crowdfundingUserDelegate;

    @Autowired
    private DiseaseClient diseaseClient;

    @Autowired
    private CfRaiseMaterialClient raiseMaterialClient;

    @Resource
    private CfUserInfoFeignClient userInfoFeignClient;

    @Resource
    private PreposeMaterialClient preposeMaterialClient;

    private static final int ZERO = 0;

    private static final int ONE = 1;

    public AdminCaseDetailsMsg getAdminCaseDetailsMsg(String infoUuid) {
        return adminCaseDetailsMsgDao.getByInfoUuid(infoUuid);
    }

    public void caseDetailsMsgAddAndUpdate(int id, int caseId, String infoUuid, String headPictureUrl, String carouselText, String caseLabel, String caseLabelSort, String caseLabelSwitch, int userId) {
        AdminCaseDetailsMsg adminCaseDetailsMsg = adminCaseDetailsMsgDao.getByInfoUuid(infoUuid);
        if (Objects.isNull(adminCaseDetailsMsg)) {
            adminCaseDetailsMsgDao.addCaseDetailsMsg(caseId, infoUuid, headPictureUrl, carouselText, caseLabel, caseLabelSort, userId);
        } else {
            adminCaseDetailsMsgDao.updateCaseDetailsMsg(id, headPictureUrl, carouselText, caseLabel, caseLabelSort, userId);
        }

        if (StringUtils.isNotBlank(caseLabelSwitch)) {
            String caseLabelsManagement = caseLabelsManagementService.get(infoUuid);
            if (StringUtils.isEmpty(caseLabelsManagement)) {
                caseLabelsManagementService.add(infoUuid, caseLabelSwitch);
            } else {
                caseLabelsManagementService.update(infoUuid, caseLabelSwitch);
            }
        }
    }

    public AdminCaseDetailsMsgVo getAdminCaseDetailsMsgVoByInfoUuid(int caseId, AdminCaseDetailsMsg adminCaseDetailsMsg) {
        AdminCaseDetailsMsgVo adminCaseDetailsMsgVo = new AdminCaseDetailsMsgVo();
        //获取图文信息中展示的图片
        List<CrowdfundingAttachmentVo> attachmentVos = repeatInfoBizService.getFundingAttachmentWithRepeatInfo(caseId);
        if (Objects.nonNull(adminCaseDetailsMsg)) {
            adminCaseDetailsMsgVo.setId(adminCaseDetailsMsg.getId());
            adminCaseDetailsMsgVo.setInfoUuid(adminCaseDetailsMsg.getInfoUuid());
            adminCaseDetailsMsgVo.setCarouselText(adminCaseDetailsMsg.getCarouselText());
            adminCaseDetailsMsgVo.setHeadPictureUrl(adminCaseDetailsMsg.getHeadPictureUrl());
            adminCaseDetailsMsgVo.setAttachmentVoList(attachmentVos);
            return adminCaseDetailsMsgVo;
        } else {
            AdminCaseDetailsMsgVo caseDetailsMsgVo = new AdminCaseDetailsMsgVo();
            caseDetailsMsgVo.setAttachmentVoList(attachmentVos);
            return caseDetailsMsgVo;
        }
    }

    public void getCaseLabelContent(CrowdfundingInfo crowdfundingInfo, AdminCaseDetailsMsgVo adminCaseDetailsMsgVo, AdminCaseDetailsMsg adminCaseDetailsMsg) {
        int caseId = crowdfundingInfo.getId();
        String infoUuid = crowdfundingInfo.getInfoId();
        boolean status = crowdfundingInfo.getStatus() == CrowdfundingStatus.CROWDFUNDING_STATED;

        CaseLabelsManagement caseLabelsManagement = this.getLabelsManagement(infoUuid);
        log.info("标签开关信息 caseLabelsManagement:{}", caseLabelsManagement);
        List<SeaCaseLabelType> seaCaseLabelTypes = this.getCaseDetailsMsg(adminCaseDetailsMsg, caseLabelsManagement, caseId, status, crowdfundingInfo.getInfoId());
        if (CollectionUtils.isNotEmpty(seaCaseLabelTypes)) {
            adminCaseDetailsMsgVo.setCaseLabel(seaCaseLabelTypes);
            adminCaseDetailsMsgVo.setCaseLabelSort(Splitter.on(",").splitToList(adminCaseDetailsMsg.getCaseLabelSort()));
            return;
        }

        List<SeaCaseLabelType> caseLabelTypes = Lists.newArrayList();
        LabelData labelData = null;

        FeignResponse<CfFirsApproveMaterial> materialRes = authorFeignClient.getAuthorInfoByInfoId(caseId);
        CfFirsApproveMaterial firsApproveMaterial = Optional.ofNullable(materialRes).filter(FeignResponse::ok).map(FeignResponse::getData).orElse(null);
        if (Objects.nonNull(firsApproveMaterial) && StringUtils.isNotEmpty(firsApproveMaterial.getPatientCryptoIdcard())) {
            String idCard = shuidiCipher.decrypt(firsApproveMaterial.getPatientCryptoIdcard());

            labelData = queryMaterialInfoVo(idCard, firsApproveMaterial.getPatientRealName());

            IdcardInfoExtractor idcardInfoExtractor = new IdcardInfoExtractor(idCard);
            if (idcardInfoExtractor.getYear() > 0) {
                caseLabelTypes.add(new SeaCaseLabelType("性别", "sex", idcardInfoExtractor.getGender(), caseLabelsManagement.getSex()));
                caseLabelTypes.add(new SeaCaseLabelType("年龄", "age", LocalDate.now().getYear() - idcardInfoExtractor.getYear() + "岁", caseLabelsManagement.getAge()));
                caseLabelTypes.add(new SeaCaseLabelType("家乡", "home", StringUtils.isNotEmpty(idcardInfoExtractor.getProvince()) ?
                        idcardInfoExtractor.getProvince() : null, StringUtils.isNotEmpty(idcardInfoExtractor.getProvince()) ? caseLabelsManagement.getHome() : -1));
            }
        }

        if (CollectionUtils.isEmpty(caseLabelTypes)) {
            //出生证or没有获取到初审信息
            caseLabelTypes.add(new SeaCaseLabelType("性别", "sex", null, -1));
            caseLabelTypes.add(new SeaCaseLabelType("年龄", "age", null, -1));
            caseLabelTypes.add(new SeaCaseLabelType("家乡", "home", null, -1));
        }

        if (Objects.nonNull(labelData)) {
            if (labelData.isIcu()) {
                caseLabelTypes.add(new SeaCaseLabelType("ICU", "treatmentLocation", "ICU", caseLabelsManagement.getTreatmentLocation() == 0 ? 1 : caseLabelsManagement.getTreatmentLocation()));
            }
        }

        CfBasicLivingGuardModel basicLivingGuardModel = this.selectLivingGuard(caseId);
        if (Objects.nonNull(basicLivingGuardModel)) {
            int livingAllowance = Objects.isNull(basicLivingGuardModel.getLivingAllowance()) ? 0 : basicLivingGuardModel.getLivingAllowance();
            int hasPoverty = Objects.isNull(basicLivingGuardModel.getHasPoverty()) ? 0 : basicLivingGuardModel.getHasPoverty();
            if (hasPoverty == 1) {
                caseLabelTypes.add(new SeaCaseLabelType("建档立卡脱贫户 ", "poorHouseholds", "建档立卡脱贫户", 1));
            } else if (livingAllowance == 1) {
                caseLabelTypes.add(new SeaCaseLabelType("低保户", "subsistenceAllowance", "低保户", 1));
            }
        }

        caseLabelTypes.add(new SeaCaseLabelType("职业", "occupation", null, -1));
        caseLabelTypes.add(new SeaCaseLabelType("家庭情况", "familySituation", null, -1));
        boolean difficultyPerson = caseLabelsManagement.getDifficultyPerson() != 0;
        if (difficultyPerson) {
            caseLabelTypes.add(new SeaCaseLabelType("困难申报人员", "difficultyPerson", "困难申报人员", caseLabelsManagement.getDifficultyPerson()));
        }

        //材审是否通过
        if (status) {
            //疾病
            String diseaseName = this.getDiseaseName(caseId);
            caseLabelTypes.add(new SeaCaseLabelType("疾病", "disease", diseaseName, caseLabelsManagement.getDisease() == 0 ? 1 : caseLabelsManagement.getDisease()));
        } else {
            caseLabelTypes.add(new SeaCaseLabelType("疾病", "disease", Strings.EMPTY, caseLabelsManagement.getDisease() == 0 ? 1 : caseLabelsManagement.getDisease()));
        }


        // 对应案例存在医护证实人员,展示医护标签开关
        buildMedicalCaseLabe(infoUuid, caseLabelsManagement, caseLabelTypes, null);

        adminCaseDetailsMsgVo.setCaseLabel(caseLabelTypes);
        List<String> labelSort = caseLabelTypes.stream()
                .map(SeaCaseLabelType::getLabelEnglish)
                .collect(Collectors.toList());
        adminCaseDetailsMsgVo.setCaseLabelSort(labelSort);
    }

    //获取疾病名
    private String getDiseaseName(int caseId) {
        String diseaseName = null;
        CrowdfundingTreatment treatment = crowdfundingUserDelegate.getCrowdfundingTreatment(caseId);
        if (Objects.nonNull(treatment)) {
            Response<String> diseaseProjects = diseaseClient.maxFeeOfDiseaseProjects(treatment.getDiseaseName(), caseId);
            if (diseaseProjects.ok() && StringUtils.isNotEmpty(diseaseProjects.getData())) {
                diseaseName = diseaseProjects.getData();
            } else {
                diseaseName = treatment.getDiseaseName();
            }
        }
        return diseaseName == null ? Strings.EMPTY : diseaseName;
    }

    private CaseLabelsManagement getLabelsManagement(String infoUuid) {
        String caseLabelsManagement = caseLabelsManagementService.get(infoUuid);
        CaseLabelsManagement labelsManagement = null;
        if (StringUtils.isNotBlank(caseLabelsManagement)) {
            try {
                labelsManagement = JSON.parseObject(caseLabelsManagement, CaseLabelsManagement.class);
            } catch (Exception e) {
                log.error("标签未获取到 caseLabelsManagement:{}", caseLabelsManagement, e);
            }
        }
        return Objects.nonNull(labelsManagement) ? labelsManagement : new CaseLabelsManagement(1, 1, 1);
    }

    private CfBasicLivingGuardModel selectLivingGuard(int caseId) {
        RpcResult<CfBasicLivingGuardModel> result = raiseMaterialClient.selectLivingGuard(caseId);
        return result != null && result.getData() != null ? result.getData() : null;
    }

    private List<SeaCaseLabelType> getCaseDetailsMsg(AdminCaseDetailsMsg adminCaseDetailsMsg, CaseLabelsManagement caseLabelsManagement, int caseId, boolean status, String infoUuid) {
        List<SeaCaseLabelType> caseLabelTypes = Lists.newArrayList();
        CaseLabelsManagementContent content = null;
        if (Objects.nonNull(adminCaseDetailsMsg) && StringUtils.isNotEmpty(adminCaseDetailsMsg.getCaseLabel())) {
            //标签内容
            try {
                content = JSON.parseObject(adminCaseDetailsMsg.getCaseLabel(), CaseLabelsManagementContent.class);
            } catch (Exception e) {
                log.error("标签未获取到 content:{}", adminCaseDetailsMsg.getCaseLabel(), e);
            }
        }

        if (Objects.nonNull(content)) {

            FeignResponse<CfFirsApproveMaterial> materialRes = authorFeignClient.getAuthorInfoByInfoId(caseId);
            CfFirsApproveMaterial firsApproveMaterial = Optional.ofNullable(materialRes).filter(FeignResponse::ok).map(FeignResponse::getData).orElse(null);
            if (Objects.nonNull(firsApproveMaterial) && StringUtils.isNotEmpty(firsApproveMaterial.getPatientCryptoIdcard())) {
                String idCard = shuidiCipher.decrypt(firsApproveMaterial.getPatientCryptoIdcard());

                LabelData labelData = queryMaterialInfoVo(idCard, firsApproveMaterial.getPatientRealName());
                if (Objects.nonNull(labelData)) {
                    if (labelData.isIcu()) {
                        caseLabelTypes.add(new SeaCaseLabelType("ICU", "treatmentLocation", "ICU", caseLabelsManagement.getTreatmentLocation() == 0 ? 1 : caseLabelsManagement.getTreatmentLocation()));
                    }
                }
            }

            caseLabelTypes.add(new SeaCaseLabelType("性别", "sex", content.getSex(), caseLabelsManagement.getSex()));
            caseLabelTypes.add(new SeaCaseLabelType("年龄", "age", content.getAge(), caseLabelsManagement.getAge()));
            caseLabelTypes.add(new SeaCaseLabelType("家乡", "home", content.getHome(), caseLabelsManagement.getHome()));
            caseLabelTypes.add(new SeaCaseLabelType("职业", "occupation", content.getOccupation(), caseLabelsManagement.getOccupation() == 0 ? -1 : caseLabelsManagement.getOccupation()));
            caseLabelTypes.add(new SeaCaseLabelType("家庭情况", "familySituation", content.getFamilySituation(), caseLabelsManagement.getFamilySituation() == 0 ? -1 : caseLabelsManagement.getFamilySituation()));
            // 医护标签
            buildMedicalCaseLabe(infoUuid, caseLabelsManagement, caseLabelTypes, content);

            if (caseLabelsManagement.getSubsistenceAllowance() != 0) {
                caseLabelTypes.add(new SeaCaseLabelType("低保户", "subsistenceAllowance", content.getSubsistenceAllowance(), caseLabelsManagement.getSubsistenceAllowance()));
            }
            if (caseLabelsManagement.getPoorHouseholds() != 0) {
                caseLabelTypes.add(new SeaCaseLabelType("脱贫户/脱贫人口 ", "poorHouseholds", content.getPoorHouseholds(), caseLabelsManagement.getPoorHouseholds()));
            }
            boolean difficultyPerson = caseLabelsManagement.getDifficultyPerson() != 0;
            if (difficultyPerson) {
                caseLabelTypes.add(new SeaCaseLabelType("困难申报人员", "difficultyPerson", "困难申报人员", caseLabelsManagement.getDifficultyPerson()));
            }
            if (status) {
                String diseaseName = null;
                if (StringUtils.isNotEmpty(content.getDisease())) {
                    diseaseName = content.getDisease();
                } else {
                    diseaseName = this.getDiseaseName(caseId);
                }
                caseLabelTypes.add(new SeaCaseLabelType("疾病", "disease", diseaseName, caseLabelsManagement.getDisease() == 0 ? 1 : caseLabelsManagement.getDisease()));
            } else {
                caseLabelTypes.add(new SeaCaseLabelType("疾病", "disease", Strings.EMPTY, caseLabelsManagement.getDisease() == 0 ? 1 : caseLabelsManagement.getDisease()));
            }


        }
        return caseLabelTypes;
    }

    private void buildMedicalCaseLabe(String infoUuid, CaseLabelsManagement caseLabelsManagement, List<SeaCaseLabelType> caseLabelTypes, CaseLabelsManagementContent content) {
        // 对应案例存在医护证实人员,展示医护标签开关
        FeignResponse<Long> response = userInfoFeignClient.getMedicalCountByCaseInfoId(infoUuid);
        if (response.ok()) {

            Long count = response.getData();

            // 不存在医护证实, 如果之前医护标签被设置关闭,则重置为打开
            if (count == ZERO) {
                if (-1 == caseLabelsManagement.getMedical()) {
                    caseLabelsManagement.setMedical(ONE);
                    String json = JSON.toJSONString(caseLabelsManagement);
                    log.info("set medical default when labe is -1: {}", json);
                    caseLabelsManagementService.update(infoUuid, json);
                }
            }

            if (count > ZERO) {
                if (Objects.nonNull(content)) {
                    caseLabelTypes.add(new SeaCaseLabelType("医护", "medical", StringUtils.isEmpty(content.getMedical()) ? "医护证实" : content.getMedical(), caseLabelsManagement.getMedical() == ZERO ? ONE : caseLabelsManagement.getMedical()));
                } else {
                    caseLabelTypes.add(new SeaCaseLabelType("医护", "medical", "医护证实", caseLabelsManagement.getMedical() == ZERO ? ONE : caseLabelsManagement.getMedical()));
                }
            }
        }
    }

    private LabelData queryMaterialInfoVo(String patientCard, String patientRealName) {

        RpcResult<PreposeMaterialModel.MaterialInfoVo> result = preposeMaterialClient.selectLatelyByIdCard(patientRealName, patientCard, BankCardVerifyEnum.UserIdentityType.IDENTITY.getCode());

        PreposeMaterialModel.MaterialInfoVo materialInfoVo = Optional.ofNullable(result).filter(RpcResult::isSuccess).map(RpcResult::getData).orElse(null);

        if (Objects.isNull(materialInfoVo)) {
            return null;
        }

        //治疗方案
        LabelData treatmentInfo = getTreatmentInfoLabels(materialInfoVo.getTreatmentInfo());

        //患者自述的疾病治疗方案
        LabelData rpTreatmentInfo = getTreatmentInfoLabels(materialInfoVo.getRpTreatmentInfo());

        return LabelData.builder()
                .icu(treatmentInfo.isIcu() | rpTreatmentInfo.isIcu())
                .build();
    }

    private LabelData getTreatmentInfoLabels(String info) {
        LabelData labelData = new LabelData();
        if (StringUtils.isEmpty(info)) {
            return labelData;
        }

        List<String> treatmentInfos = TreatmentParse.parseTreatmentInfo(info);

        for (String treatmentInfo : treatmentInfos) {
            if (treatmentInfo.contains("ICU")) {
                labelData.setIcu(true);
            }
        }
        return labelData;
    }


    @Data
    static class TreatmentInfo {
        private List<SpecialDiseaseChoiceInfo> info;
    }

    @Data
    static class SpecialDiseaseChoiceInfo {
        private int treatmentType;
        private String showValue;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    static class LabelData {

        /**
         * icu
         */
        private boolean icu;

    }
}
