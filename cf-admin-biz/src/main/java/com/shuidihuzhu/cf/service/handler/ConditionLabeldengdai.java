package com.shuidihuzhu.cf.service.handler;

import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingOperationEnum;
import com.shuidihuzhu.cf.model.event.CailiaoCondition;
import com.shuidihuzhu.cf.model.event.CailiaoConditionEvent;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderLevel;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @DATE 2020/3/20
 * f.延后等待审核：命中手动打上“延后等待审核’标签的案例
 */
@Component
@Slf4j
public class ConditionLabeldengdai implements ConditionLabel{

    @Override
    @EventListener(classes = CailiaoConditionEvent.class)
    public void onApplicationEvent(CailiaoConditionEvent event) {
        log.info("ConditionLabel caseId={} operation={}",event.getCaseId(),event.getOperation());
        if (CrowdfundingOperationEnum.DEFER_APPROVE.value() == event.getOperation()){
            setResults("延后等待审核",event);
            return;
        }
    }

    @Override
    public int getConditionCode() {
        return CailiaoCondition.condition_5.getCode();
    }
}
