package com.shuidihuzhu.cf.service.workorder.initialAudit.ai.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.dao.crowdfunding.CfAiMaterialsDao;
import com.shuidihuzhu.cf.model.crowdfunding.ai.AiConditionEnum;
import com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterials;
import com.shuidihuzhu.cf.model.crowdfunding.ai.LayOutField;
import com.shuidihuzhu.cf.service.workorder.initialAudit.ai.AiCondition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @DATE 2021/2/26
 */
@Service("aiMixDiseaseName")
public class AiMixDiseaseNameCondition implements AiCondition {

    @Autowired
    private CfAiMaterialsDao cfAiMaterialsDao;

    @Override
    public boolean check(int caseId, String inputValue) {

        CfAiMaterials m1 = cfAiMaterialsDao.getByCaseId(caseId,CfAiMaterials.tType);
        Optional<String> o1 = Optional.ofNullable(m1).map(CfAiMaterials::getFields).orElse(Lists.newArrayList()).stream().filter(r->inputValue.equals(r.getFieldKey())).map(LayOutField::getFieldValue).findFirst();

        if (o1.isPresent() && AiConditionEnum.wu.getStringCode().equals(o1.get())){
            CfAiMaterials m2 = cfAiMaterialsDao.getByCaseId(caseId,CfAiMaterials.jType);
            List<LayOutField> fields = Optional.ofNullable(m2).map(CfAiMaterials::getFields).orElse(Lists.newArrayList());
            Optional<String> content = fields.stream().filter(r->"contentHasDiseaseName".equals(r.getFieldKey())).map(LayOutField::getFieldValue).findFirst();
            Optional<String> title = fields.stream().filter(r->"titleHasDiseaseName".equals(r.getFieldKey())).map(LayOutField::getFieldValue).findFirst();
            return AiConditionEnum.wu.getStringCode().equals(content.get()) && AiConditionEnum.wu.getStringCode().equals(title.get());
        }

        return false;
    }
}
