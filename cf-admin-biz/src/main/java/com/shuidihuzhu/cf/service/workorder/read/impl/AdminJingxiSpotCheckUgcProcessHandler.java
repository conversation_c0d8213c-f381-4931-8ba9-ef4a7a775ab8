package com.shuidihuzhu.cf.service.workorder.read.impl;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdFundingProgressBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfSensitiveWordRecordBiz;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress;
import com.shuidihuzhu.cf.service.workorder.read.AdminJingxiSpotCheckHandler;
import com.shuidihuzhu.cf.service.workorder.read.AdminJingxiSpotCheckService;
import com.shuidihuzhu.client.cf.workorder.helper.Von;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.cf.workorder.read.BasicWorkOrder;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class AdminJingxiSpotCheckUgcProcessHandler extends AdminJingxiSpotCheckHandler{

    @Autowired
    AdminJingxiSpotCheckService adminJingxiSpotCheckService;
    @Autowired
    private AdminCrowdFundingProgressBiz adminCrowdFundingProgressBiz;
    @Override
    public Map<String, Object> getExtInfo(long workOrderId, int orderType) {
        final Response<BasicWorkOrder> orderResp = Von.read().getOrderBasicInfoById(workOrderId);
        final BasicWorkOrder basicWorkOrder = orderResp.getData();
        final List<WorkOrderExt> bizExtList = basicWorkOrder.getBizExtList();
        Map<String, String> extMap = adminJingxiSpotCheckService.getJingxiExtMap(bizExtList);
        Long wordId = adminJingxiSpotCheckService.getLong(extMap, "wordId");
        final HashMap<String, Object> result = Maps.newHashMap();
        if (wordId == null) {
            return result;
        }
        final CrowdFundingProgress progress = adminCrowdFundingProgressBiz.getActivityProgressById(wordId);
        result.put("progressContent", progress.getContent());
        result.put("progressImages", progress.getImageUrls());
        return result;
    }

    @Override
    public WorkOrderType getWorkOrderType() {
        return WorkOrderType.ugcprogress;
    }
}
