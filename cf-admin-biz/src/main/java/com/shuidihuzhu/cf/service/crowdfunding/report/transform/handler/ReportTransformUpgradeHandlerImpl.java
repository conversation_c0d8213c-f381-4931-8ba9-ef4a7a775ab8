package com.shuidihuzhu.cf.service.crowdfunding.report.transform.handler;

import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.cf.service.crowdfunding.report.transform.ReportTransformParam;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class ReportTransformUpgradeHandlerImpl extends ReportTransformBaseHandlerImpl implements IReportTransformHandler {
    @Autowired
    private ApproveRemarkOldService approveRemarkOldService;

    @Override
    protected void saveRemark(ReportTransformParam p) {
        approveRemarkOldService.add(p.getCaseId(), p.getOperatorId(), "升级二线", "将工单升级二线");
    }

    @Override
    protected OrderExtName getOperateCommentExt() {
        return OrderExtName.endDealUpgradeTime;
    }

    @Override
    protected String getComment() {
        return "升级二线工单";
    }

    @Override
    protected HandleResultEnum getHandleResult() {
        return HandleResultEnum.end_deal_upgrade;
    }

    @Override
    protected Response<Void> makeHasNoFinishError() {
        return NewResponseUtil.makeError(AdminErrorCode.REPORT_EXIST_NOT_FINISH);
    }

    @Override
    protected Response<Void> makePermissionError() {
        return NewResponseUtil.makeError(AdminErrorCode.REPORT_WORK_ORDER_NOT_UPGRADE);
    }

    @Override
    protected WorkOrderType getOrderType() {
        return WorkOrderType.up_grade_second;
    }
}
