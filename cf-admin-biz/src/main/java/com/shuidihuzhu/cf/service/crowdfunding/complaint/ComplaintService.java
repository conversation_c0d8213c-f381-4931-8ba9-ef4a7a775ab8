package com.shuidihuzhu.cf.service.crowdfunding.complaint;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.admin.UserCommentBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.IUgcOperateRecordService;
import com.shuidihuzhu.cf.client.apipure.enums.complaint.ComplaintBizTypeEnum;
import com.shuidihuzhu.cf.client.apipure.enums.complaint.ComplaintResultEnum;
import com.shuidihuzhu.cf.client.apipure.enums.complaint.ComplaintTypeEnum;
import com.shuidihuzhu.cf.client.apipure.feign.ComplaintFeignClient;
import com.shuidihuzhu.cf.client.apipure.feign.ComplaintVerifyResultFeignClient;
import com.shuidihuzhu.cf.client.apipure.model.crowdfunding.complaint.CfComplaintDO;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.dto.ComplaintVerifyDTO;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.admin.UserCommentSourceEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminUGCTask;
import com.shuidihuzhu.cf.enums.crowdfunding.UgcBizType;
import com.shuidihuzhu.cf.enums.crowdfunding.UgcManageEnum;
import com.shuidihuzhu.cf.enums.risk.UgcTypeEnum;
import com.shuidihuzhu.cf.model.admin.UserComment;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdFundingVerificationView;
import com.shuidihuzhu.cf.model.risk.verify.RiskUgcVerifyModel;
import com.shuidihuzhu.cf.util.crowdfunding.LocalDateTimeUtils;
import com.shuidihuzhu.cf.vo.crowdfunding.CfVerificationVo;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCoreFeignClient;
import com.shuidihuzhu.client.cf.workorder.model.HandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.QueryListResult;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.cf.workorder.read.WorkOrderReadFeignClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ComplaintService {


    @Resource
    private ComplaintFeignClient complaintFeignClient;

    @Resource
    private IUgcOperateRecordService ugcOperateRecordService;

    @Resource
    private UserCommentBiz userCommentBiz;

    @Resource
    private IRiskDelegate riskDelegate;

    @Resource
    private WorkOrderReadFeignClient workOrderReadFeignClient;

    @Resource
    private WorkOrderCoreFeignClient workOrderCoreFeignClient;

    @Resource
    private ComplaintVerifyResultFeignClient complaintVerifyResultFeignClient;

    public List<CfComplaintDO> listByBizIdAndBizTypeAndComplaintResult(Long bizId, ComplaintBizTypeEnum bizType, ComplaintResultEnum complaintResult) {
        OperationResult<List<CfComplaintDO>> operationResult = complaintFeignClient.listByBizIdAndBizTypeAndComplaintResult(bizId,
                bizType.getCode(),
                complaintResult.getCode());
        if (operationResult.isSuccess()) {
            return operationResult.getData();
        }
        return Lists.newArrayList();
    }

    public void buildComplaintInfo(CfVerificationVo vo, CrowdFundingVerification verification) {

        List<CfComplaintDO> cfComplaintDOS = listByBizIdAndBizTypeAndComplaintResult(Long.valueOf(verification.getId()), ComplaintBizTypeEnum.VERIFICATION, ComplaintResultEnum.YES);

        List<CfVerificationVo.ComplaintInfo> complaintInfoList = cfComplaintDOS.stream().map(e -> {

            CfVerificationVo.ComplaintInfo complaintInfo = new CfVerificationVo.ComplaintInfo();
            complaintInfo.setComplaintUserId(e.getComplaintUserId());
            complaintInfo.setComplaintMsg(ComplaintTypeEnum.parse(e.getComplaintType()).getDesc());
            String complaintTime = LocalDateTimeUtils.localDateTime2String(e.getComplaintTime());
            complaintInfo.setComplaintTime(complaintTime);

            return complaintInfo;
        }).collect(Collectors.toList());

        vo.setComplaintInfoList(complaintInfoList);
        vo.setComplaintInfoSize(complaintInfoList.size());
    }

    public void buildComplaintInfo(CrowdFundingVerificationView crowdFundingVerificationView, int verificationId) {

        List<CfComplaintDO> cfComplaintDOS = listByBizIdAndBizTypeAndComplaintResult((long) verificationId, ComplaintBizTypeEnum.VERIFICATION, ComplaintResultEnum.YES);

        List<CfVerificationVo.ComplaintInfo> complaintInfoList = cfComplaintDOS.stream().map(e -> {

            CfVerificationVo.ComplaintInfo complaintInfo = new CfVerificationVo.ComplaintInfo();
            complaintInfo.setComplaintUserId(e.getComplaintUserId());
            complaintInfo.setComplaintMsg(ComplaintTypeEnum.parse(e.getComplaintType()).getDesc());
            String complaintTime = LocalDateTimeUtils.localDateTime2String(e.getComplaintTime());
            complaintInfo.setComplaintTime(complaintTime);

            return complaintInfo;
        }).collect(Collectors.toList());

        crowdFundingVerificationView.setComplaintInfoList(complaintInfoList);
        crowdFundingVerificationView.setComplaintInfoSize(complaintInfoList.size());
    }

    /**
    * @description: 投诉工单审核
    * @demand:
    * @author: sunpeifu
    * @date: 2021/7/13 14:47
    */
    public Response verify(ComplaintVerifyDTO complaintVerifyDTO, int adminUserId) {

        AdminUGCTask.Result handleTypeEnum = AdminUGCTask.Result.getByCode(complaintVerifyDTO.getHandleType());
        if (handleTypeEnum == null || handleTypeEnum == AdminUGCTask.Result.NO || StringUtils.isEmpty(complaintVerifyDTO.getReason())) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<Long> ids = Arrays.stream(complaintVerifyDTO.getUgcWorkOrderId().split(",")).map(Long::valueOf).collect(Collectors.toList());

        Response<List<QueryListResult>> response = workOrderReadFeignClient.getListByOrderIdListOld(ids);
        if (response == null || response.notOk() || CollectionUtils.isEmpty(response.getData())){
            return NewResponseUtil.makeError(AdminErrorCode.WORK_ORDER_NOT_FOUND);
        }

        List<QueryListResult> list = response.getData();
        long dealId = 0;

        for (QueryListResult data : list) {
            String desc = "客服操作";
            AdminUGCTask.Content contentType = AdminUGCTask.Content.getByCode(Integer.valueOf(data.getContentType()));

            switch (contentType) {
                case VERIFICATION:
                    if (StringUtils.isNotEmpty(data.getVerificationId())){
                        dealId = newHandleVerification(Long.valueOf(data.getVerificationId()),data.getCaseId(),adminUserId,handleTypeEnum ,data.getCaseUuid());
                        desc += contentType.getWord() + ",操作的内容为[" + dealId + "]";

                        // 设置审核状态为 -> 已审核
                        saveOrderUpdateVerifyResult(Long.valueOf(data.getVerificationId()) , ComplaintBizTypeEnum.VERIFICATION.getCode(), 1);
                        break;
                    }
                default:
            }

            //增加UGC图文审核记录
            UserComment userComment = new UserComment(data.getCaseId(), UserCommentSourceEnum.UGC,
                    UserCommentSourceEnum.CommentType.getCommetType(contentType), adminUserId, complaintVerifyDTO.getReason(), handleTypeEnum.getWord(), desc);
            userCommentBiz.add(userComment);

            if (data.getWorkOrderId() != 0){
                HandleOrderParam handleOrderParam = new HandleOrderParam();
                handleOrderParam.setWorkOrderId(data.getWorkOrderId());
                handleOrderParam.setOrderType(WorkOrderType.ugc_complaint_verify.getType());
                handleOrderParam.setHandleResult(handleTypeEnum == AdminUGCTask.Result.PASS_AND_SHOW ? HandleResultEnum.pass_show.getType():HandleResultEnum.only_self.getType());
                handleOrderParam.setOperComment(complaintVerifyDTO.getReason());
                handleOrderParam.setUserId(adminUserId);
                workOrderCoreFeignClient.handle(handleOrderParam);
            }
        }

        return NewResponseUtil.makeSuccess(null);

    }

    public long newHandleVerification(long verificationId, int caseId, int adminUserId , AdminUGCTask.Result taskResult,String caseInfoId) {

        switch (taskResult) {
            case ONLY_SELF:
                RiskUgcVerifyModel riskUgcVerifyModel = new RiskUgcVerifyModel(caseId, UgcTypeEnum.VERIFICATION, verificationId, "运营操作添加");
                riskDelegate.addVerify(riskUgcVerifyModel);
                ugcOperateRecordService.insertUgcRecord(caseId, UgcBizType.VERIFICATION.getKey(), verificationId, UgcManageEnum.VERIFY_SEE_ONESELF.getKey(),adminUserId);
                break;
            case PASS_AND_SHOW:
                riskDelegate.deleteVerify(caseId, UgcTypeEnum.VERIFICATION, verificationId);
                ugcOperateRecordService.insertUgcRecord(caseId, UgcBizType.VERIFICATION.getKey(), verificationId, UgcManageEnum.VERIFY_SHOW.getKey(),adminUserId);
                break;
            default:
        }
        riskDelegate.deleteCaseVerificationCache(caseInfoId);
        return verificationId;
    }

    public void saveOrderUpdateVerifyResult(long bizId, Integer biztype,Integer result){
        log.info("saveOrderUpdateVerifyResult bizId: {} , bizType: {} , result : {}", bizId , biztype ,result);
        OperationResult<Boolean> response = complaintVerifyResultFeignClient.saveOrUpdateResult(bizId, biztype, result);
        log.info("saveOrderUpdateVerifyResult response: {}" , response);
    }
}
