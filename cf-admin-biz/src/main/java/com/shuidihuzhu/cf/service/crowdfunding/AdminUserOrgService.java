package com.shuidihuzhu.cf.service.crowdfunding;

import com.shuidihuzhu.cf.delegate.saas.OrganizationClientV1;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2019/11/4 上午11:10
 * @desc
 */
@Service
public class AdminUserOrgService {
    private static final int MAX_ORGANIZATION = 128;

    @Resource
    private OrganizationClientV1 organizationClientV1;

    public String getOrganization(Integer operatorId) {
        String org = "";
        if (Objects.isNull(operatorId) || operatorId <= 0) {
            return org;
        }

        org = organizationClientV1.getUserRelationOrgName(operatorId).getResult();
        if (StringUtils.isEmpty(org)) {
            return "";
        }
        if (StringUtils.length(org) <= MAX_ORGANIZATION) {
            return org;
        }

        org = StringUtils.left(org, MAX_ORGANIZATION);
        return org;
    }
}
