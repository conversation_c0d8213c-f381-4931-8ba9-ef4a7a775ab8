package com.shuidihuzhu.cf.service.tag;

import com.shuidihuzhu.cf.model.label.CfCaseLabelInfo;
import com.shuidihuzhu.client.cf.admin.model.ThreeBodyTag;
import com.shuidihuzhu.client.cf.growthtool.model.CaseLabelParam;
import com.shuidihuzhu.client.cf.growthtool.model.RuleJudge;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;
import java.util.Map;

/**
 * @Description: 案例标签服务
 * @Author: panghairui
 * @Date: 2023/8/9 10:41 AM
 */
public interface CfCaseLabelService {

    /**
     * 绑定案例标签
     */
    void bindingCaseLabel(Integer caseId);

    /**
     * 待录入案例标签提醒
     */
    Boolean remindCaseLabel(RuleJudge ruleJudge);

    String judgeCaseLabel(RuleJudge ruleJudge);

    /**
     * 根据案例查询绑定标签
     */
    ThreeBodyTag getCaseLabel(Integer caseId, Integer predictDonateCount);

    Boolean judgeMajorCase(CaseLabelParam caseLabelParam);

    /**
     * 获取所有标签
     */
    List<ThreeBodyTag> getAllLabelInfo();

    /**
     * 根据案例批量查询绑定标签
     */
    List<ThreeBodyTag> getCaseLabelByCaseIds(List<Integer> caseIds);

    /**
     * 添加/更新 案例标签
     */
    Response<Long> addOrUpdateCaseLabel(CfCaseLabelInfo cfCaseLabelInfo);

    /**
     * 查询案例标签
     */
    Map<String, Object> queryCaseLabel(String labelName, Integer status, Integer pageNum, Integer pageSize);

    /**
     * 根据id查询案例标签
     */
    CfCaseLabelInfo queryCaseLabelById(Long id);

    /**
     * 标签启用/禁用
     */
    Boolean enableCaseLabel(Long id, Integer status, Long userId);

}
