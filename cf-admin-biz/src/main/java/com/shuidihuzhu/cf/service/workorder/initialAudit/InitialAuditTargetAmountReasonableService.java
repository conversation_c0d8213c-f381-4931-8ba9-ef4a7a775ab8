package com.shuidihuzhu.cf.service.workorder.initialAudit;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.admin.TargetAmountAuditRecordBiz;
import com.shuidihuzhu.cf.biz.admin.UserCommentBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.feign.CfRaiseMaterialClient;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.client.material.model.RaiseBasicInfoModel;
import com.shuidihuzhu.cf.constants.AdminUserIDConstants;
import com.shuidihuzhu.cf.delegate.PreposeMaterialDelegate;
import com.shuidihuzhu.cf.enums.BackgroundLogEnum;
import com.shuidihuzhu.cf.enums.EvaluatingTheCostOfIllnessEnum;
import com.shuidihuzhu.cf.enums.NotifyOnlineVolunteerEventEnum;
import com.shuidihuzhu.cf.enums.admin.UserCommentSourceEnum;
import com.shuidihuzhu.cf.event.NotifyOnlineVolunteerEvent;
import com.shuidihuzhu.cf.model.admin.UserComment;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.record.TargetAmountAuditRecord;
import com.shuidihuzhu.cf.mq.impl.AdminCommonMessageHelperService;
import com.shuidihuzhu.cf.risk.client.rpc.DiseaseClient;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseAmountReasonableTypeEnum;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyEnum;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyResultEnum;
import com.shuidihuzhu.cf.risk.model.risk.DiseaseAmountResultRecord;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyRequest;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyResponse;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyResultInfo;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.client.auth.saas.feign.UserFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.client.cf.growthtool.client.CfGrowthtoolFeginClient;
import com.shuidihuzhu.client.cf.growthtool.client.ClewPreproseMaterialFeignClient;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCoreFeignClient;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCreateParam;
import com.shuidihuzhu.client.cf.workorder.model.HandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderListParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderLevel;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.cf.workorder.read.BasicWorkOrder;
import com.shuidihuzhu.client.cf.workorder.read.WorkOrderReadFeignClient;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.model.CfPublicAuditAction;
import com.shuidihuzhu.client.model.builder.MessageBuilder;
import com.shuidihuzhu.client.model.enums.PublicAuditTypeEnum;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: wangpeng
 * @Date: 2023/1/13 14:22
 * @Description:
 */
@Slf4j
@Service
@RefreshScope
public class InitialAuditTargetAmountReasonableService {

    @Value("${target.amount.reasonable.limit.amount:500000}")
    private int targetAmountReasonableLimitAmount;

    @Value("${target.amount.reasonable.start.time:9}")
    private int targetAmountReasonableStartTime;

    @Value("${target.amount.reasonable.end.time.v2:21}")
    private int targetAmountReasonableEndTimeV2;

    @Resource
    private WorkOrderCoreFeignClient workOrderCoreFeignClient;
    @Resource
    private DiseaseClient diseaseClient;
    @Resource
    private CfRaiseMaterialClient cfRaiseMaterialClient;
    @Resource
    private WorkOrderReadFeignClient workOrderReadFeignClient;
    @Resource
    private AdminApproveService adminApproveService;
    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;
    @Resource
    private UserCommentBiz userCommentBiz;
    @Resource
    private CfInitialAuditHandleV2ConsumerService cfInitialAuditHandleV2ConsumerService;
    @Resource
    private TargetAmountAuditRecordBiz targetAmountAuditRecordBiz;
    @Resource
    private AdminCommonMessageHelperService adminCommonMessageHelperService;
    @Resource
    private UserFeignClient userFeignClient;
    @Autowired
    private ApplicationContext applicationContext;
    @Resource
    private PreposeMaterialDelegate preposeMaterialDelegate;
    @Resource
    private CfGrowthtoolFeginClient cfGrowthtoolFeginClient;
    @Resource
    private ClewPreproseMaterialFeignClient clewPreproseMaterialFeignClient;

    public boolean createTargetAmountReasonableWorkOrder(CrowdfundingInfo crowdfundingInfo, int targetAmountAuditWorkOrderScene, boolean checkTargetAmount) {
        if (!checkTargetAmount) {
            log.info("checkTargetAmount为false");
            return false;
        }
        RpcResult<RaiseBasicInfoModel> rpcResult = cfRaiseMaterialClient.selectRaiseBasicInfo(crowdfundingInfo.getId());
        String diseaseName = Optional.ofNullable(rpcResult)
                .filter(RpcResult::isSuccess)
                .map(RpcResult::getData)
                .map(RaiseBasicInfoModel::getDiseaseName)
                .orElse(null);
        if (StringUtils.isBlank(diseaseName)) {
            log.info("diseaseName为空");
            return false;
        }

        int targetAmount = crowdfundingInfo.getTargetAmount();
        List<String> diseaseNameList = List.of(StringUtils.split(rpcResult.getData().getDiseaseName(), ",，"));
        DiseaseStrategyRequest request = new DiseaseStrategyRequest();
        request.setCaseId(crowdfundingInfo.getId());
        request.setDiseaseNameList(diseaseNameList);
        request.setExecuteStrategyEnum(DiseaseStrategyEnum.AMOUNT_REASONABLE.getCode());
        request.setReasonAmountType(DiseaseAmountReasonableTypeEnum.TARGET_AMOUNT_REASONABLE.getCode());
        Response<DiseaseStrategyResponse> diseaseStrategyResponse = diseaseClient.diseaseStrategy(request);
        int integer = Optional.ofNullable(diseaseStrategyResponse)
                .filter(Response::ok)
                .map(Response::getData)
                .map(DiseaseStrategyResponse::getDiseaseAmountStrategyResult)
                .map(DiseaseStrategyResultInfo::getResult)
                .orElse(DiseaseStrategyResultEnum.DiseaseAmountStrategyInitialEnum.NOT_REASONABLE.getCode());
        long currentTimeMillis = System.currentTimeMillis();
        String strDateStart = DateUtil.getDateStart(currentTimeMillis);
        Date dateStart = DateUtil.getStr2SDate(strDateStart);
        Timestamp startTime = com.shuidihuzhu.common.web.util.DateUtil.addHours(new Timestamp(dateStart.getTime()), targetAmountReasonableStartTime);
        Timestamp endTime = com.shuidihuzhu.common.web.util.DateUtil.addHours(new Timestamp(dateStart.getTime()), targetAmountReasonableEndTimeV2);
        endTime = com.shuidihuzhu.common.web.util.DateUtil.addMinutes(endTime, 30);

        if (targetAmount <= targetAmountReasonableLimitAmount && integer == DiseaseStrategyResultEnum.DiseaseAmountStrategyInitialEnum.NOT_REASONABLE.getCode() && (endTime.getTime() < currentTimeMillis || currentTimeMillis < startTime.getTime())) {
            log.info("开始执行自动审核逻辑");
            WorkOrderCreateParam createParam = new WorkOrderCreateParam();
            createParam.setOrderType(WorkOrderType.target_amount_reasonable_audit.getType());
            createParam.setCaseId(crowdfundingInfo.getId());
            createParam.setOrderlevel(OrderLevel.D.getType());
            createParam.addExt(OrderExtName.targetAmountAuditWorkOrderScene, targetAmountAuditWorkOrderScene);
            createParam.setOperatorId(AdminUserIDConstants.SYSTEM);
            createParam.setHandleResult(HandleResultEnum.smart_audit_pass.getType());
            Response<Long> longResponse = workOrderCoreFeignClient.create(createParam);
            long workOrderId = Optional.ofNullable(longResponse)
                    .filter(Response::ok)
                    .map(Response::getData)
                    .orElse(0L);
            if (workOrderId > 0L) {
                approveAfterSendMQ(crowdfundingInfo.getId(), workOrderId, HandleResultEnum.smart_audit_pass.getType(), AdminUserIDConstants.SYSTEM);
            } else {
                log.error("createTargetAmountReasonableWorkOrder fail, createParam: {}", createParam);
            }
            UserComment comment = new UserComment();
            comment.setOperatorId(AdminUserIDConstants.SYSTEM);
            comment.setCaseId(crowdfundingInfo.getId());
            comment.setCommentSource(UserCommentSourceEnum.INITIAL_AUDIT.getCode());
            comment.setCommentType(UserCommentSourceEnum.CommentType.SUBMIT_VALUE_REMARK.getCode());
            comment.setOperateMode("夜间21:30以后至日间9点之前，低于等于50万目标金额审核工单自动审核通过。");
            comment.setWorkOrderId(workOrderId);
            comment.setComment("");
            comment.setOperateDesc("");
            userCommentBiz.insert(comment);
            return false;
        }

        PreposeMaterialModel.MaterialInfoVo materialInfoVo = preposeMaterialDelegate.queryByCaseId(crowdfundingInfo.getId());
        // 是否命中试点，默认为false
        boolean highRiskGreyResult = false;

        if (materialInfoVo != null) {
            // 查询是否命中试点
            Response<Boolean> highRiskGrey = cfGrowthtoolFeginClient.isHighRiskGrey(materialInfoVo.getVolunteerUniqueCode());
            log.info("是否命中试点,ighRiskGrey: {}, materialInfoVo.getVolunteerUniqueCode(): {}", highRiskGrey, materialInfoVo.getVolunteerUniqueCode());
            highRiskGreyResult = Optional.ofNullable(highRiskGrey)
                    .filter(Response::ok)
                    .map(Response::getData)
                    .orElse(false);
        }

        // 如果需要试点，才执行下面的逻辑
        if (highRiskGreyResult) {
            log.info("命中试点，执行升级目标金额审核工单先评估疾病花费, case_id: {}", crowdfundingInfo.getId());
            /**
             * 提交审核的时间在：10:00-19:00：需要单独判断时间
             * 满足如下条件之一则自动升级目标金额审核工单先评估疾病花费：疾病花费-应扣减金额 < 5万 或者 应扣减金额/疾病花费 > 0.4
             */
            List<Integer> evaluatingTheCostOfIllness = evaluatingDiseaseSpendingStrategies(crowdfundingInfo);

            // 如果触发了评估疾病花费策略
            if (!evaluatingTheCostOfIllness.isEmpty()) {
                WorkOrderCreateParam workOrderCreateParam = new WorkOrderCreateParam();
                workOrderCreateParam.setOrderType(WorkOrderType.target_amount_reasonable_audit.getType());
                workOrderCreateParam.setCaseId(crowdfundingInfo.getId());
                workOrderCreateParam.setOrderlevel(OrderLevel.D.getType());
                workOrderCreateParam.addExt(OrderExtName.targetAmountAuditWorkOrderScene, targetAmountAuditWorkOrderScene);

                // 添加额外字段 评估疾病花费策略，这个额外字段插入到了 sea-stat1 的 work_order_ext 表
                // 查询通过 com.shuidihuzhu.workorder.dao.WorkOrderDaoExt#listOrderExtByIdsAndExtNames 传入work_order_id以及ext_name
                String code = joinWithComma(evaluatingTheCostOfIllness);
                log.info("触发评估疾病花费策略，code: {}", code);
                workOrderCreateParam.addExt(OrderExtName.evaluatingTheCostOfIllness, code);
                // 分配到von-type:target_amount_medical
                workOrderCreateParam.setAssignGroupPermission("von-type:target_amount_medical");

                workOrderCoreFeignClient.create(workOrderCreateParam);
                applicationContext.publishEvent(new NotifyOnlineVolunteerEvent(NotifyOnlineVolunteerEventEnum.TARGET_AMOUNT_WORK_ORDER_CRATE_EVENT, crowdfundingInfo.getId()));
                return true;
            }
        }

        if (targetAmount > targetAmountReasonableLimitAmount || integer == DiseaseStrategyResultEnum.DiseaseAmountStrategyInitialEnum.NOT_REASONABLE.getCode()) {
            WorkOrderCreateParam workOrderCreateParam = new WorkOrderCreateParam();
            workOrderCreateParam.setOrderType(WorkOrderType.target_amount_reasonable_audit.getType());
            workOrderCreateParam.setCaseId(crowdfundingInfo.getId());
            workOrderCreateParam.setOrderlevel(OrderLevel.D.getType());
            workOrderCreateParam.addExt(OrderExtName.targetAmountAuditWorkOrderScene, targetAmountAuditWorkOrderScene);
            workOrderCreateParam.setAssignGroupPermission(targetAmount > targetAmountReasonableLimitAmount ? "von-type:target_amount_medical" : "von-type:target_amount_basic");
            workOrderCoreFeignClient.create(workOrderCreateParam);
            applicationContext.publishEvent(new NotifyOnlineVolunteerEvent(NotifyOnlineVolunteerEventEnum.TARGET_AMOUNT_WORK_ORDER_CRATE_EVENT, crowdfundingInfo.getId()));
            return true;
        }

        return false;
    }

    /**
     * 将 List<Integer> 转换为用英文逗号分隔的字符串
     *
     * @param list 整型列表
     * @return 逗号分隔的字符串，如果 list 为空则返回空字符串
     */
    private static String joinWithComma(List<Integer> list) {
        if (list == null || list.isEmpty()) {
            return "";
        }
        return list.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(","));
    }

    /**
     * 是否在人工审核时间内：10:00 到 19:00
     */
    private boolean isInManualAuditWindow() {
        long currentTimeMillis = System.currentTimeMillis();

        // 获取当天 00:00:00 的时间
        String todayStartStr = DateUtil.getDateStart(currentTimeMillis);
        Date todayStartDate = DateUtil.getStr2SDate(todayStartStr);

        // 构造人工审核时间区间：10:00 ~ 19:00
        Timestamp auditStartTime = com.shuidihuzhu.common.web.util.DateUtil.addHours(new Timestamp(todayStartDate.getTime()), 10);
        Timestamp auditEndTime = com.shuidihuzhu.common.web.util.DateUtil.addHours(new Timestamp(todayStartDate.getTime()), 19);

        // 判断当前是否在区间内
        return currentTimeMillis >= auditStartTime.getTime()
                && currentTimeMillis <= auditEndTime.getTime();
    }

    /**
     * 评估疾病花费策略
     * 1.提交人工审核时间内：10:00 到 19:00
     * 2.升级策略：疾病花费-应扣减金额 < 5万 或者 应扣减金额/疾病花费 > 0.4
     * 2.1 疾病花费=max(疾病小工具建议最大花费，目标金额)
     * 2.2 应扣减金额=（人身险已赔付金额+事故(已赔付/垫付金额)+家庭金融资产+其他平台筹款筹得款项)
     */
    public List<Integer> evaluatingDiseaseSpendingStrategies(CrowdfundingInfo crowdfundingInfo) {
        List<Integer> res = new ArrayList<>();
        // 1.不在提交人工审核时间内：10:00 到 19:00 直接不触发评估疾病花费策略
        if (!isInManualAuditWindow()) {
            log.info("不在提交人工审核时间内：10:00 到 19:00 直接不触发评估疾病花费策略, case_id: {}", crowdfundingInfo.getId());
            return res;
        }

        // 2.1 计算疾病花费
        // 目标金额，单位是分
        long targetAmount = crowdfundingInfo.getTargetAmount();
        // 疾病小工具建议最大花费，单位是元
        long adviseMaxAmount = 0;
        Response<List<DiseaseAmountResultRecord>> amountResultRecordByCaseId = diseaseClient.getAmountResultRecordByCaseId(crowdfundingInfo.getId());
        List<DiseaseAmountResultRecord> diseaseAmountResultRecords = Optional.ofNullable(amountResultRecordByCaseId)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(Lists.newArrayList());
        // 计算疾病花费，单位是分
        long sicknessExpenses = 0;
        if (!diseaseAmountResultRecords.isEmpty()) {
            DiseaseAmountResultRecord newest = diseaseAmountResultRecords.get(diseaseAmountResultRecords.size() - 1);
            log.info("疾病小工具建议最大花费取DiseaseAmountResultRecord下标为：{}的，值为：{}元", diseaseAmountResultRecords.size() - 1, newest.getAdviseMaxAmount());
            adviseMaxAmount = newest.getAdviseMaxAmount();
        }

        // 疾病花费 = max(疾病小工具建议最大花费，目标金额)
        sicknessExpenses = Math.max(adviseMaxAmount * 100, targetAmount);
        log.info("疾病花费（单位，分） = max(疾病小工具建议最大花费，目标金额) = max({}, {}) = {}", adviseMaxAmount * 100.0, targetAmount, sicknessExpenses);

        // 2.2 计算应扣减金额
        // 家庭金融资产 对应crowdfunding_material_attribute表字段 financial_asset_amount 金融资产价值，单位 元
        long financialAssetsAmount = 0L;
        // 其他平台筹款筹得款项 对应crowdfunding_material_attribute表字段 supply_amount 其他渠道救助金额，单位 元
        long raiseAmount = 0L;
        // 事故(已赔付/垫付金额) 单位 元
        long paidAmount = 0L;
        // 人身险已赔付金额（新字段）单位 元
        long amountPaidForLifeInsurance = 0L;

        // 提取四项金额（单位：元）并转换为“分”
        Response<PreposeMaterialModel.MaterialInfoVo> materialInfoVoResponse = clewPreproseMaterialFeignClient.selectMaterialByCaseIdForQC(crowdfundingInfo.getId());
        PreposeMaterialModel.MaterialInfoVo materialInfoVo = Optional.ofNullable(materialInfoVoResponse)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);
        if (materialInfoVo != null) {
            if (materialInfoVo.getFinancialAssetsAmount() != null && materialInfoVo.getFinancialAssetsAmount() >= 0) {
                financialAssetsAmount = materialInfoVo.getFinancialAssetsAmount().longValue() * 100;
            }
            if (materialInfoVo.getRaiseAmount() != null && materialInfoVo.getRaiseAmount() >= 0) {
                raiseAmount = materialInfoVo.getRaiseAmount().longValue() * 100;
            }
            if (materialInfoVo.getPaidAmount() != null && materialInfoVo.getPaidAmount() >= 0) {
                paidAmount = materialInfoVo.getPaidAmount().longValue() * 100;
            }
            if (materialInfoVo.getPersonalPaidAmount() != null && materialInfoVo.getPersonalPaidAmount() >= 0) {
                amountPaidForLifeInsurance = materialInfoVo.getPersonalPaidAmount().longValue() * 100;
            }
        }

        // 应扣减金额 = 人身险已赔付 + 事故(已赔付/垫付金额) + 家庭金融资产 + 其他平台筹款筹得款项，单位分
        long amountToBeDeducted = amountPaidForLifeInsurance + paidAmount + financialAssetsAmount + raiseAmount;
        log.info("应扣减金额（单位分） =（人身险已赔付金额+事故(已赔付/垫付金额)+家庭金融资产+其他平台筹款筹得款项）= {} + {} + {} + {} = {}",
                amountPaidForLifeInsurance, paidAmount, financialAssetsAmount, raiseAmount, amountToBeDeducted);

        if (sicknessExpenses == 0) {
            log.info("疾病花费为0不走新策略, case_id: {}", crowdfundingInfo.getId());
            res.add(EvaluatingTheCostOfIllnessEnum.NONE.getCode());
            return res;
        }

        if (sicknessExpenses - amountToBeDeducted < 5000000) {
            log.info("触发升级策略：疾病花费 - 应扣减金额 < 5万元，case_id: {}", crowdfundingInfo.getId());
            res.add(EvaluatingTheCostOfIllnessEnum.AMOUNT_AFTER_DEDUCTION_LESS_THAN_5W.getCode());
        }

        // 如果触发升级策略 扣除可承担部分后目标金额低——应扣减金额/疾病花费>0.4
        BigDecimal deducted = BigDecimal.valueOf(amountToBeDeducted);
        BigDecimal expenses = BigDecimal.valueOf(sicknessExpenses);
        // 保留 4 位小数，四舍五入
        BigDecimal ratio = deducted.divide(expenses, 4, RoundingMode.HALF_UP);

        if (ratio.compareTo(BigDecimal.valueOf(0.4)) > 0) {
            log.info("触发升级策略 扣除可承担部分后目标金额低——应扣减金额/疾病花费>0.4, case_id: {}", crowdfundingInfo.getId());
            res.add(EvaluatingTheCostOfIllnessEnum.DEDUCTION_RATIO_GREATER_THAN_40.getCode());
        }

        // 返回list
        return res;
    }

    public Response<PageResult<WorkOrderVO>> getOrderList(WorkOrderListParam workOrderListParam) {
        return workOrderReadFeignClient.getOrderListByListParam(workOrderListParam);
    }

    public boolean handle(long workOrderId, int handleResult, String remark, long operatorId) {
        HandleResultEnum handleResultEnum = HandleResultEnum.getFromType(handleResult);
        if (Objects.isNull(handleResultEnum)) {
            return false;
        }
        Response<BasicWorkOrder> basicWorkOrderResponse = workOrderReadFeignClient.getByOrderId(workOrderId);
        BasicWorkOrder workOrder = Optional.ofNullable(basicWorkOrderResponse)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);
        if (Objects.isNull(workOrder)) {
            return false;
        }
        int caseId = workOrder.getCaseId();
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
        if (Objects.isNull(crowdfundingInfo)) {
            return false;
        }
        HandleOrderParam handleOrderParam = new HandleOrderParam();
        handleOrderParam.setWorkOrderId(workOrderId);
        handleOrderParam.setOrderType(WorkOrderType.target_amount_reasonable_audit.getType());
        handleOrderParam.setHandleResult(handleResult);
        handleOrderParam.setUserId(operatorId);
        Response<Void> handleWorkOrder = workOrderCoreFeignClient.handle(handleOrderParam);
        if (handleWorkOrder.notOk()) {
            log.info("InitialAuditTargetAmountReasonableService handle work not ok {} {}", workOrderId, handleWorkOrder);
            return false;
        }
        if (handleResultEnum == HandleResultEnum.later_doing) {
            return true;
        }

        UserComment comment = new UserComment();
        comment.setOperatorId(operatorId);
        comment.setCaseId(workOrder.getCaseId());
        comment.setCommentSource(UserCommentSourceEnum.INITIAL_AUDIT.getCode());
        comment.setCommentType(UserCommentSourceEnum.CommentType.INITIAL_AUDIT_PASS.getCode());
        comment.setOperateMode(handleResultEnum.getMsg());
        comment.setWorkOrderId(workOrderId);
        comment.setComment(remark);
        comment.setOperateDesc("目标金额" + handleResultEnum.getMsg());
        userCommentBiz.insert(comment);

        // 添加目标金额审核工单操作记录
        TargetAmountAuditRecord record = new TargetAmountAuditRecord();
        record.setCaseId(caseId);
        record.setWorkOrderId(workOrder.getId());
        record.setRejectReason(remark);
        if (remark.contains("合理目标金额：") && handleResult == HandleResultEnum.audit_reject.getType()) {
            int minCostAmount = (int) (Double.parseDouble(remark.substring(remark.indexOf("：") + 1, remark.indexOf("～"))) * 10000);
            int maxCostAmount = (int) (Double.parseDouble(remark.substring(remark.indexOf("～") + 1, remark.indexOf("万"))) * 10000);
            record.setMinCostAmount(minCostAmount);
            record.setMaxCostAmount(maxCostAmount);
            record.setReject(true);
        }

        // 发送目标金额审核通过MQ
        approveAfterSendMQ(caseId, workOrderId, handleResult, operatorId);

        targetAmountAuditRecordBiz.add(record);

        adminApproveService.addComment(crowdfundingInfo.getInfoId(), remark, (int) operatorId, "", BackgroundLogEnum.REMARK);

        cfInitialAuditHandleV2ConsumerService.saveInitialAuditSnapshotV2(workOrderId, caseId);

        return true;
    }

    /**
     * 目标金额工单审核后发送MQ
     *
     * @param caseId
     * @param workOrderId
     * @param handleResult
     * @param operatorId
     */
    private void approveAfterSendMQ(int caseId, long workOrderId, int handleResult, long operatorId) {
        if (handleResult != HandleResultEnum.audit_pass.getType() && handleResult != HandleResultEnum.smart_audit_pass.getType()) {
            return;
        }
        Response<AuthUserDto> result = userFeignClient.getValidAuthUserById(operatorId);
        AuthUserDto authUserDto = Optional.ofNullable(result)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);
        if (authUserDto == null) {
            log.warn("InitialAuditTargetAmountReasonableService approveAfterSendMQ authUserDto is null {}", operatorId);
            return;
        }
        CfPublicAuditAction payload = CfPublicAuditAction.builder()
                .workOrderId(workOrderId)
                .caseId(caseId)
                .operatorId(operatorId)
                .operatorName(authUserDto.getUserName())
                .auditType(PublicAuditTypeEnum.TARGET_AMOUNT_AUDIT.getCode()).build();

        Message<CfPublicAuditAction> message = MessageBuilder.createWithPayload(payload)
                .setTags(CfClientMQTagCons.CF_PUBLIC_AUDIT_INFO)
                .addKey(CfClientMQTagCons.CF_PUBLIC_AUDIT_INFO, caseId)
                .build();
        adminCommonMessageHelperService.send(message);
    }

}
