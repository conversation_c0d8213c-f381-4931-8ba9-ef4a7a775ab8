package com.shuidihuzhu.cf.service.cfOperatingProfile;

import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileSettings;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CfOperatingProfileSettingsExtBiz {

    void unbindRelation(int userId, CfOperatingProfileSettings.ProfileExtName extName, List<Integer> values);

    int insertExtList(List<CfOperatingProfileSettings.ProfileSettingsExt> list);

    List<CfOperatingProfileSettings.ProfileSettingsExt> selectByProfileIdsAndNames(List<Long> profileIds,
                                                                                   List<String> names);

    int deleteExtByProfileIdsAndNames(List<Long> profileIds, List<String> names);

    List<CfOperatingProfileSettings.ProfileSettingsExt> selectByNameAndValues(String name, List<String> values);

}
