package com.shuidihuzhu.cf.service.admin.dream;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.shuidihuzhu.cf.model.admin.vo.JobStatusVo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @package: com.shuidihuzhu.cf.service.admin.dream
 * @Author: liujiawei
 * @Date: 2018-12-02  17:44
 */
@Service
@Slf4j
@RefreshScope
public class AdminCheckTaskPublishService {

    @Autowired
    private DiscoveryClient discoveryClient;

    @Autowired
    private RestTemplate restTemplate;

    public boolean checkPublish(int min) {
        List<JobStatusVo> nowJobs = getRunningJob(min);
        if (CollectionUtils.isEmpty(nowJobs)) {
            return true;
        }
        List<JobStatusVo> shortTimeJobs = Lists.newArrayList();
        nowJobs.forEach(item -> {
            List<String> times = JSON.parseArray(item.getCorn(),String.class);
            long date1 = DateUtil.getDatetimeFromString(times.get(1)).toDate().getTime();
            long date2 = DateUtil.getDatetimeFromString(times.get(0)).toDate().getTime();
            if (date1 - date2 < 1 * 60 * 60 * 1000L ) {
                shortTimeJobs.add(item);
            }
        });
        nowJobs.removeAll(shortTimeJobs);
        if (CollectionUtils.isEmpty(nowJobs)) {
            return true;
        }
        return false;
    }

    private List<JobStatusVo> getRunningJob(int minute) {
        List<JobStatusVo> jobStatusVolist = new ArrayList<>();
        List<ServiceInstance> instances = discoveryClient.getInstances("cf-task");
        if (instances == null || instances.size() == 0) {
            return Lists.newArrayList();
        }
        //当前正在执行的job
        for (ServiceInstance instance : instances) {
            String url = instance.getUri().toString();
            String runningjobsUrl = url + "/admin/get/running-jobs";
            String runningjobsInfo = restTemplate.getForObject(runningjobsUrl, String.class);
            if (!StringUtils.isEmpty(runningjobsInfo)) {
                List<JobStatusVo> jobStatusVosTmp = JSON.parseArray(runningjobsInfo, JobStatusVo.class);
                for (JobStatusVo job : jobStatusVosTmp) {
                    job.setIp(instance.getHost());
                    jobStatusVolist.add(job);
                }
            }
        }
        List<String> jobsName = jobStatusVolist.stream().map(JobStatusVo::getName).collect(Collectors.toList());
        //1分钟内将要执行的job
        for (ServiceInstance instance : instances) {
            String url = instance.getUri().toString();
            String todojobsUrl = url + "/admin/get/todo-jobs?min=" + minute;
            String todojobsInfo = restTemplate.getForObject(todojobsUrl, String.class);
            if (!StringUtils.isEmpty(todojobsInfo)) {
                List<JobStatusVo> list = JSON.parseArray(todojobsInfo, JobStatusVo.class);
                for (JobStatusVo job : list) {
                    job.setIp(instance.getHost());
                    if (!jobsName.contains(job.getName())) {
                        jobStatusVolist.add(job);
                    }
                }
            }
        }
        log.info("runningJobInfo:{}", jobStatusVolist);
        return jobStatusVolist;
    }
}
