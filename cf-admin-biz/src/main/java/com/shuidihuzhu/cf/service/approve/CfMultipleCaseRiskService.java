package com.shuidihuzhu.cf.service.approve;

import com.shuidihuzhu.cf.dto.CfMultipleCaseRiskDto;
import com.shuidihuzhu.cf.finance.model.finance.drawcash.CfDrawCashRecordV2;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface CfMultipleCaseRiskService {
    void judgeAmountRisk(Long workOrderId, int caseId);
    CfMultipleCaseRiskDto getRecordByWorkOrderId(long workOrderId);
    Date getEarliestCreateTime(List<CrowdfundingInfo> sameIdCardCaseList);
    CfMultipleCaseRiskDto getLatestRecordByCaseId(int caseId);
}
