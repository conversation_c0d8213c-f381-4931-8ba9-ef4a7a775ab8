package com.shuidihuzhu.cf.service.workorder.read;

import com.shuidihuzhu.cf.client.base.page.v1.model.PaginationListVO;
import com.shuidihuzhu.cf.model.crowdfunding.vo.QueryListResultVo;
import com.shuidihuzhu.cf.param.workorder.WorkOrderListQueryOldParam;
import com.shuidihuzhu.client.cf.workorder.model.QueryListParam;
import com.shuidihuzhu.client.cf.workorder.model.QueryListResult;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface WorkOrderReadService {

    /**
     *
     * @param workOrderId
     * @return can not handle
     */
    boolean checkCanNotHandle(long workOrderId, int operatorId);

    List<QueryListResultVo> getQueryListResultVos(QueryListParam queryListParam,  List<QueryListResult> list);

    Response<PaginationListVO<QueryListResultVo>> getWorkOrderList(WorkOrderListQueryOldParam queryListParam);

    /**
     * 鲸息同步数据
     *
     * @param workOrderId -
     * @param orderType -
     * @param fundUseProgressId -
     * @return -
     */
    Map<String, Object> getFundUseAuditInfo4Transfer(long workOrderId, int orderType, int fundUseProgressId);
}
