package com.shuidihuzhu.cf.service.gift;

import com.shuidihuzhu.client.model.ReceivingGoodsDto;
import com.shuidihuzhu.client.model.ReceivingGoodsInfo;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Author: pangh<PERSON><PERSON>
 * @Date: 2023/6/8 9:10 PM
 */
public interface SeaDonationGiftService {

    void seaAddReceivingInfo(ReceivingGoodsInfo receivingGoodsInfo, long userId, int registrationType);

    Map<String, Object> seaQueryReceivingInfo(String receivingName, String receivingPhone, String receivingAddress, String beginTime, String endTime, int current);

    Boolean seaExportReceivingExcel(String beginTime, String endTime, long userId);

}
