package com.shuidihuzhu.cf.service;

import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.Callable;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class RedisLockService {

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler cfRedissonHandler;

    public <T> T callWithLock(@NonNull Callable<T> callable, @NonNull String lockKey, long leaseTimeMillis) {
        String lock = null;
        try {
            lock = cfRedissonHandler.tryLock(lockKey, leaseTimeMillis);
        } catch (InterruptedException e) {
            log.error("getLock lockKey:{}, className:{}", lockKey, callable.getClass().getName(), e);
        }
        if (StringUtils.isEmpty(lock)) {
            return null;
        }
        T result = null;
        try {
            result = callable.call();
        } catch (Exception e) {
            log.error("call lockKey:{}, className:{}", lockKey, callable.getClass().getName(), e);
        }

        try {
            cfRedissonHandler.unLock(lockKey, lock);
        } catch (Exception e) {
            log.error("releaseControl lockKey:{}, lock:{}, className:{}", lockKey, lock, callable.getClass().getName(), e);
        }
        return result;
    }

    public <T> Callable<T> lockProxy(@NonNull Callable<T> callable, @NonNull String lockKey, long leaseTimeMillis) {
        return () -> callWithLock(callable, lockKey, leaseTimeMillis);
    }

}

