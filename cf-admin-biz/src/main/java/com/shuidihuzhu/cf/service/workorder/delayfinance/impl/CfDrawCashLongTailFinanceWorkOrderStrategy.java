package com.shuidihuzhu.cf.service.workorder.delayfinance.impl;

import com.shuidihuzhu.cf.finance.model.vo.drawcash.CfDrawCashLongTailVo;
import com.shuidihuzhu.cf.service.workorder.delayfinance.delegate.IFinanceWorkOrderDelegate;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-06-15 3:36 PM
 **/
@Service
public class CfDrawCashLongTailFinanceWorkOrderStrategy extends AbstractFinanceWorkOrderStrategy<CfDrawCashLongTailVo> {

    @Resource
    private IFinanceWorkOrderDelegate iFinanceWorkOrderDelegate;

    @Override
    public int orderType() {
        return WorkOrderType.long_tail.getType();
    }

    @Override
    public List<CfDrawCashLongTailVo> getBusinessExt(List<Long> financeBusinessIds) {
        return iFinanceWorkOrderDelegate.getDrawCashLongTailVo(financeBusinessIds);
    }
}
