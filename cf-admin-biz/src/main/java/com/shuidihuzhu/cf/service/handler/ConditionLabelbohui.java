package com.shuidihuzhu.cf.service.handler;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingOperationBiz;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation;
import com.shuidihuzhu.cf.model.event.CailiaoCondition;
import com.shuidihuzhu.cf.model.event.CailiaoConditionEvent;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderLevel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @DATE 2020/3/20
 * 多次驳回：材料审核的驳回次数＞=3次的案例
 */
@Component
@Slf4j
public class ConditionLabelbohui implements ConditionLabel{

    @Autowired
    private AdminCrowdfundingOperationBiz adminCrowdfundingOperationBiz;

    @Override
    @EventListener(classes = CailiaoConditionEvent.class)
    public void onApplicationEvent(CailiaoConditionEvent event) {

        CrowdfundingOperation crowdfundingOperation = adminCrowdfundingOperationBiz.getByInfoId(event.getCaseUuid());
        log.info("ConditionLabel caseId={} refuse={}",event.getCaseId(),crowdfundingOperation.getRefuseCount());
        if (crowdfundingOperation != null && crowdfundingOperation.getRefuseCount()>=3){
            setResults("多次驳回,次数="+crowdfundingOperation.getRefuseCount(),event);
            updateLevelIfHigherAndCaiLiao4(event, OrderLevel.C);
            return;
        }
    }

    @Override
    public int getConditionCode() {
        return  CailiaoCondition.condition_2.getCode();
    }
}
