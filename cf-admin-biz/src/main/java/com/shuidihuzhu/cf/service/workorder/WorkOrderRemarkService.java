package com.shuidihuzhu.cf.service.workorder;

import com.shuidihuzhu.cf.dao.workorder.WorkOrderRemarkDAO;
import com.shuidihuzhu.cf.delegate.OrganizationDelegate;
import com.shuidihuzhu.cf.domain.workorder.WorkOrderRemarkDO;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingApprove;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * todo 整合到 admin_crowdfunding_approve
 * <AUTHOR>
 * @date 2019-04-30  14:19
 */
@Service
public class WorkOrderRemarkService {

    private static final String SPLIT = "：";

    @Autowired
    private WorkOrderRemarkDAO workOrderRemarkDAO;

    @Resource
    private OrganizationDelegate organizationDelegate;

    public List<WorkOrderRemarkDO> listByCaseId(int caseId) {
        return workOrderRemarkDAO.listByCaseId(caseId);
    }

    public List<CrowdfundingApprove> listFlowApproveByCaseId(int caseId) {
        List<WorkOrderRemarkDO> dos = workOrderRemarkDAO.listByCaseId(caseId);
        if (CollectionUtils.isEmpty(dos)) {
            return Collections.emptyList();
        }
        return dos.stream()
                .map(v -> {
                    CrowdfundingApprove a = new CrowdfundingApprove();
                    a.setWorkOrderId(v.getWorkOrderId());
                    a.setComment(v.getContent());
                    a.setCrowdfundingId(v.getCaseId());
                    a.setOprid(v.getOperatorId());
                    a.setOprtime(v.getCreateTime());
                    a.setOrganization(v.getOrganization());
                    return a;
                })
                .collect(Collectors.toList());
    }

    public void addFlowComment(int caseId, int userId, long workOrderId, String prefixComment, String comment) {
        add(caseId, userId, workOrderId, prefixComment, comment);
    }

    public void add(int caseId, int operatorId, long workOrderId, String prefix, String content) {
        add(caseId, operatorId, workOrderId, prefix + SPLIT + content);
    }

    private void add(int caseId, int operatorId, long workOrderId, String content) {
        WorkOrderRemarkDO v = new WorkOrderRemarkDO();
        v.setCaseId(caseId);
        v.setOperatorId(operatorId);
        v.setWorkOrderId(workOrderId);
        v.setContent(content);
        v.setOrganization(getOrganization(operatorId));
        workOrderRemarkDAO.insert(v);
    }

    /**
     * 获取组织快照
     * @param operatorId
     * @return
     */
    private String getOrganization(Integer operatorId) {
        return organizationDelegate.getSimpleOrganization(operatorId);
    }
}
