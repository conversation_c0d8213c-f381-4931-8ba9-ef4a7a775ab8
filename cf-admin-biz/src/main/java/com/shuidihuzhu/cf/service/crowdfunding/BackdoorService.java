package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CfFinishStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoStatusEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.model.crowdfunding.BaseInfoModel;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoStatus;
import com.shuidihuzhu.cf.model.river.RiverUsageTypeEnum;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditOperateService;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditOperationItem;
import com.shuidihuzhu.cf.vo.approve.RiverHandleParamVO;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCoreFeignClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.cf.workorder.read.WorkOrderReadFeignClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2018/12/13
 */
@Service
public class BackdoorService {
    @Autowired
    private CfWorkOrderClient workOrderClient;
    @Autowired
    private InitialAuditOperateService initialAuditOperateService;
    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;
    @Autowired
    private AdminCfInfoExtBiz adminCfInfoExtBiz;

    @Autowired
    private ICrowdfundingDelegate crowdfundingInfoBiz;

    public Response getInfo(int caseId){

        CrowdfundingInfo info = crowdfundingDelegate.getFundingInfoById(caseId);

        if (info == null){
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }

        List<CrowdfundingInfoStatus> list = crowdfundingDelegate.getCrowdfundingInfoStatusListByInfoUuid(info.getInfoId());

        CfInfoExt cfInfoExt = adminCfInfoExtBiz.getByInfoUuid(info.getInfoId());

        BaseInfoModel baseInfoModel = new BaseInfoModel();

        baseInfoModel.setCrowdfundingInfo(info);
        baseInfoModel.setList(list);

        if (cfInfoExt != null) {
            int finishStatus = cfInfoExt.getFinishStatus();
            for (CfFinishStatus cfFinishStatus : CfFinishStatus.values()) {
                if (cfFinishStatus.getValue() == finishStatus) {
                    if (info.getEndTime().getTime() <= System.currentTimeMillis()
                            && finishStatus == CfFinishStatus.NOT_FINISH.getValue()) {
                        baseInfoModel.setFinishMsg(CfFinishStatus.EXPIRE.getDescription());
                    } else {
                        baseInfoModel.setFinishMsg(cfFinishStatus.getDescription());
                    }
                }
            }
            baseInfoModel.setFirstApproveStatus(cfInfoExt.getFirstApproveStatus());
        }

        return NewResponseUtil.makeSuccess(baseInfoModel);
    }



    public Response changeInfoStatus(int caseId,int status){

      int result =  crowdfundingDelegate.updateCrowdfundingStatus(caseId, CrowdfundingStatus.fromValue(status),CrowdfundingStatus.APPROVE_PENDING);
      if (result > 0){
          return NewResponseUtil.makeSuccess(result);
      }

        return NewResponseUtil.makeFail("只能把未提交状态修改为提交状态");
    }


    public boolean changeInformationStatus(String caseUuid,int type){

     return crowdfundingDelegate.updateCrowdfundingInfoStatusByInfoId(caseUuid,type, CrowdfundingInfoStatusEnum.SUBMITTED)>0;
    }

    public void auditPassWorkOrder(int caseId, int userId, String infoUuid) {
        if (StringUtils.isNotEmpty(infoUuid)) {
            CrowdfundingInfo crowdfundingInfoByInfoId = crowdfundingInfoBiz.getCrowdfundingInfoByInfoId(infoUuid);
            if (crowdfundingInfoByInfoId != null) {
                caseId = crowdfundingInfoByInfoId.getId();
            }
        }
        Response<WorkOrderVO> lastWorkOrderByTypes = workOrderClient.getLastWorkOrderByTypes(caseId, Lists.newArrayList(
                WorkOrderType.ai_erci.getType(),
                WorkOrderType.ai_photo.getType(),
                WorkOrderType.ai_content.getType(),
                WorkOrderType.highriskshenhe.getType(),
                WorkOrderType.target_amount_reasonable_audit.getType()
        ));
        WorkOrderVO data = lastWorkOrderByTypes.getData();
        if (data == null) {
            return;
        }

        long workOrderId = data.getWorkOrderId();
        workOrderClient.assignWorkOrder(workOrderId, userId, userId);

        RiverHandleParamVO dibaoHandleParam = new RiverHandleParamVO();
        dibaoHandleParam.setHandleType(RiverHandleParamVO.HandleType.PASS);
        dibaoHandleParam.setUsageTypeEnum(RiverUsageTypeEnum.DI_BAO);

        InitialAuditOperationItem.HandleCaseInfoParam param = new InitialAuditOperationItem.HandleCaseInfoParam();
        param.setCaseId(caseId);
        param.setHandleType(1);
        param.setPassIds(Lists.newArrayList(1, 100, 20));
        param.setUserId(userId);
        param.setDiBaoHandleParam(dibaoHandleParam);
        param.setOrderType(data.getOrderType());
        param.setWorkOrderId(workOrderId);
        initialAuditOperateService.handleWorkOrder(param);
    }
}
