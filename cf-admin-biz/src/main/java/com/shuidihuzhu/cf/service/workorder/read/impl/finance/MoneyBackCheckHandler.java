package com.shuidihuzhu.cf.service.workorder.read.impl.finance;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import com.shuidihuzhu.cf.service.workorder.read.impl.AbstractFinanceWorkOrderCheckHandler;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

/**
 * 返还款ID    id
 * 付款账户    backAccountNo
 * 付款方      backAccountName
 * 交易金额     amountStr eg: 400.00 （单位：元）
 * 用途       purpose
 * 匹配状态     followStatusDesc eg: 带匹配
 */
@Component
public class MoneyBackCheckHandler extends AbstractFinanceWorkOrderCheckHandler {
    @Override
    public WorkOrderType getWorkOrderType() {
        return WorkOrderType.money_back;
    }

    @Override
    public Map<String, Object> getExtInfo(long workOrderId, int orderType) {
        Map<String, Object> res = super.getExtInfo(workOrderId, orderType);
        // 返还款 ID
        updateMap(res, "id", "refundId");
        // 付款账户
        if (res.get("backAccountNoMask") != null) {
            NumberMaskVo backAccountNoMask = JSON.parseObject(JSON.toJSONString(res.get("backAccountNoMask")), NumberMaskVo.class);
            res.put("payAcc", Optional.ofNullable(backAccountNoMask).map(NumberMaskVo::getEncryptNumber).orElse(""));
        } else {
            res.put("payAcc", "");
        }
        // 付款方
        updateMap(res, "backAccountName", "payParty");
        // 交易金额
        updateMap(res, "amountStr", "transAmt");
        // 用途
        updateMap(res, "purpose", "purp");
        // 匹配状态
        updateMap(res, "followStatusDesc", "matchSt");
        return res;
    }
}
