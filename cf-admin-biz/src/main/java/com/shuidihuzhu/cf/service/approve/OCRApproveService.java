package com.shuidihuzhu.cf.service.approve;

import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationRecordDTO;
import com.shuidihuzhu.cf.risk.model.risk.diease.InfoReasonableAmountResultVo;
import com.shuidihuzhu.cf.risk.model.risk.diease.SpecialDiseaseChoiceInfoVo;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditCaseDetail;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface OCRApproveService {

    /**
     * 填充ocr识别结果
     * @param firstApproveCaseInfo
     * @param caseId
     * @param workOrderId
     */
    InitialAuditCaseDetail.FirstApproveCaseInfo fillResult(InitialAuditCaseDetail.FirstApproveCaseInfo firstApproveCaseInfo, int caseId, long workOrderId);

    Response<String> ocrDiseaseNames(int caseId, long workOrderId, String resourceUrl, List<String> urls, int adminUserId);

    Response<SpecialDiseaseChoiceInfoVo> analyseDiseaseNames(int caseId, long workOrderId, String diseaseNames,
                                                             int adminUserId, String specialRaiseChoiceInfo);

    Response<SpecialDiseaseChoiceInfoVo> analyseSpecialRaise(int caseId, long workOrderId, String diseaseNames,
                                                             int adminUserId);

    List<OperationRecordDTO> listCaseAnalyseHistory(int caseId);

    OperationRecordDTO getLastAnalyseByWorkOrder(long workOrderId);

    Response<InfoReasonableAmountResultVo> decideInfoAmount(int caseId, long workOrderId, int workOrderType, int adminUserId,
                                                            String ocrDiseaseNames, List<String> classifyDiseaseNames,
                                                            String resourceUrl, List<String> croppedUrls,
                                                            String otherInfo,
                                                            String submitDiseaseNames,
                                                            String specialRaiseChoiceInfo,
                                                            int handleResult);

    Response<Void> saveDiseaseToolRecordWithoutChoice(String diseaseClassNameList,
                                                      String submitDiseaseNames, int workOrderType, long workOrderId,
                                                      int caseId, long userId, int handleResult);
}
