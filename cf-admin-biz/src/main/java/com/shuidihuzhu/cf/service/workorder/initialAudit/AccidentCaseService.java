package com.shuidihuzhu.cf.service.workorder.initialAudit;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationRecordDTO;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.model.crowdfunding.caseRepeat.InitialRepeatCaseView;
import com.shuidihuzhu.cf.risk.client.risk.AccidentCaseClient;
import com.shuidihuzhu.cf.risk.model.enums.risk.TriggerTimingEnum;
import com.shuidihuzhu.cf.risk.model.risk.Participate;
import com.shuidihuzhu.common.web.model.Response;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Author: wangpeng
 * @Date: 2021/8/13 11:03
 * @Description:
 */
@Service
@Slf4j
public class AccidentCaseService {

    @Resource
    private CommonOperationRecordClient commonOperationRecordClient;
    @Resource
    private AccidentCaseClient accidentCaseClient;


    public List<Integer> accidentCase(int caseId, long workOrderId, int operatorId) {
        // 先查快照数据～
        OperationRecordDTO recordDTO = commonOperationRecordClient.getLastByBizIdAndActionTypes(workOrderId, OperationActionTypeEnum.ACCIDENT_CASE);
        if (Objects.nonNull(recordDTO)) {
            log.info("可以找到工单id的快照数据.worOrderId:{} param:{}", workOrderId, recordDTO);
            return JSON.parseArray(recordDTO.getRemark(), Integer.class);
        }
        Participate participate = Participate.builder()
                .caseId(caseId)
                .operatorId(operatorId)
                .workOrderId(workOrderId)
                .triggerTiming(TriggerTimingEnum.PREJUDICATION_DETAIL.getDesc())
                .build();
        Response<List<Integer>> listResponse = accidentCaseClient.followAccidentCaseStrategy(participate);
        if (Objects.nonNull(listResponse) && listResponse.ok() && CollectionUtils.isNotEmpty(listResponse.getData())) {
            return listResponse.getData();
        }
        return new ArrayList<>();
    }

    /**
     * 保存快照
     * @param
     */
    public void saveInitialRepeatSnapshot(List<Integer> accidentList, long workOrderId, int operatorId) {

        log.info("保存初审事故案例的快照. workOrderId:{}, accidentList:{}", workOrderId, accidentList);
        commonOperationRecordClient.create()
                .buildBasicPlatform(workOrderId, operatorId, OperationActionTypeEnum.ACCIDENT_CASE)
                .buildRemark(JSON.toJSONString(Objects.requireNonNullElse(accidentList, Lists.newArrayList())))
                .save();
    }
}
