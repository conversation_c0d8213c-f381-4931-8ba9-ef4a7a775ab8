package com.shuidihuzhu.cf.service.cfOperatingProfile.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoStatusBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfMaterialVerityHistoryBiz;
import com.shuidihuzhu.cf.dao.cfOperatingProfile.CfCaseOperateStatusDao;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoDataStatusTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoStatusEnum;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfCaseOperateStatus;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoStatus;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.cf.service.cfOperatingProfile.CfOperateStatusBiz;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.regex.Pattern;

@Service
@Slf4j
public class CfOperateStatusBizImpl implements CfOperateStatusBiz {

    @Autowired
    private AdminCrowdfundingInfoStatusBiz infoStatusBiz;
    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private CfCaseOperateStatusDao operateStatusDao;
    @Autowired
    private CfMaterialVerityHistoryBiz verityHistoryBiz;
    @Autowired
    private ApproveRemarkOldService remarkOldService;


    public int getRelationVideoOperateType(String infoUuid) {

        CrowdfundingInfo info = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (info == null) {
            log.error("案例不存在infoUuid:{}", infoUuid);
            return 0;
        }

        List<CfCaseOperateStatus> hasOperates = operateStatusDao.selectByIdAndOperateType(info.getId(),
                Lists.newArrayList(CfCaseOperateStatus.OperateStatus.PAYEE_RELATION.getCode(),
                CfCaseOperateStatus.OperateStatus.CANCEL_PAYEE_RELATION.getCode()));
        if (CollectionUtils.isNotEmpty(hasOperates) && hasOperates.size() > 1) {
            log.error("下发收款人关系视频状态有多条.caseId:{}", info.getId());
            return 0;
        }

        // 判断是否可以开启 下发收款人关系的通道     判断用户提交的材料的状态
        if (CollectionUtils.isEmpty(hasOperates) || hasOperates.get(0).getOperateType() ==
                CfCaseOperateStatus.OperateStatus.CANCEL_PAYEE_RELATION.getCode()) {
            CrowdfundingInfoStatus infoStatus = infoStatusBiz.getByInfoUuidAndType(infoUuid, CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT.getCode());

            if (infoStatus == null || infoStatus.getStatus() != CrowdfundingInfoStatusEnum.PASSED.getCode()) {
                return CfCaseOperateStatus.OperateStatus.PAYEE_RELATION.getCode();
            }
        } else {
            return CfCaseOperateStatus.OperateStatus.CANCEL_PAYEE_RELATION.getCode();
        }

        return 0;
    }

    @Override
    public AdminErrorCode operateRelationVideo(int userId, String infoUuid, int operateType, String operateComment) {

        CrowdfundingInfo info = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (info == null || validateComment(operateComment)) {
            log.error("案例不存在infoUuid:{} comment:{}", infoUuid, operateComment);
            return AdminErrorCode.SYSTEM_PARAM_ERROR;
        }

        if (operateType != CfCaseOperateStatus.OperateStatus.PAYEE_RELATION.getCode()
                && operateType != CfCaseOperateStatus.OperateStatus.CANCEL_PAYEE_RELATION.getCode()) {
            log.info("提交或取消收款人视频上传操作码错误。infoUuid:{} operateType:{}", infoUuid, operateType);
            return AdminErrorCode.SYSTEM_PARAM_ERROR;
        }

        int canOperateCode = getRelationVideoOperateType(infoUuid);
        if (operateType != canOperateCode) {
            log.info("提交或取消收款人视频上传操作码错误。infoUuid:{} operateType:{} canOperateCode:{}", infoUuid, operateType, canOperateCode);
            return AdminErrorCode.SYSTEM_PARAM_ERROR;
        }

        List<CfCaseOperateStatus> hasOperates = operateStatusDao.selectByIdAndOperateType(info.getId(),
                Lists.newArrayList(CfCaseOperateStatus.OperateStatus.PAYEE_RELATION.getCode(),
                        CfCaseOperateStatus.OperateStatus.CANCEL_PAYEE_RELATION.getCode()));

        if (CollectionUtils.isEmpty(hasOperates)) {
            CfCaseOperateStatus operateStatus = new CfCaseOperateStatus();

            operateStatus.setCaseId(info.getId());
            operateStatus.setOperateId(userId);
            operateStatus.setOperateComment(StringUtils.trimToEmpty(operateComment));
            operateStatus.setOperateType(operateType);
            operateStatus.setOrganization(verityHistoryBiz.queryOperateDetail(userId));

            operateStatusDao.insertOne(operateStatus);
            log.info("添加下发收款人关系通道的记录.record:{}", operateStatus);
        } else {

            hasOperates.get(0).setOperateId(userId);
            hasOperates.get(0).setOperateComment(StringUtils.trimToEmpty(operateComment));
            hasOperates.get(0).setOperateType(operateType);
            hasOperates.get(0).setOrganization(verityHistoryBiz.queryOperateDetail(userId));
            operateStatusDao.updateById(hasOperates.get(0));
            log.info("修改下发收款人关系通道的记录.record:{}", hasOperates.get(0));
        }

        String prefix = operateType ==
                CfCaseOperateStatus.OperateStatus.PAYEE_RELATION.getCode() ?
                "下发收款人关系视频上传入口: " : "取消收款人关系视频上传入口: ";
        // 插入记录 同步到三大详情页里
        remarkOldService.add(info.getId(), userId, prefix + operateComment);


        return AdminErrorCode.SUCCESS;
    }


    private boolean validateComment(String comment) {

        return StringUtils.isBlank(comment) || comment.length() < 5 || comment.length() > 300;
    }

    @Override
    public CfCaseOperateStatus selectLastOperateReason(String infoUuid) {

        CrowdfundingInfo info = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (info == null) {
            log.error("案例不存在infoUuid:{} comment", infoUuid);
            return null;
        }

        List<CfCaseOperateStatus> operateStatusList = operateStatusDao.selectByIdAndOperateType(info.getId(),
                Lists.newArrayList(CfCaseOperateStatus.OperateStatus.PAYEE_RELATION.getCode()));

        return CollectionUtils.isEmpty(operateStatusList) ? null : operateStatusList.get(0);
    }

}

