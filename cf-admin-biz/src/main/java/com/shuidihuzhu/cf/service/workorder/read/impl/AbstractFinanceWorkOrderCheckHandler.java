package com.shuidihuzhu.cf.service.workorder.read.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.service.workorder.delayfinance.IBaseFinanceWorkOrderStrategy;
import com.shuidihuzhu.cf.service.workorder.delayfinance.impl.AbstractFinanceWorkOrderStrategy;
import com.shuidihuzhu.cf.service.workorder.read.AdminJingxiSpotCheckHandler;
import com.shuidihuzhu.client.cf.workorder.helper.Von;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.read.BasicWorkOrder;
import com.shuidihuzhu.common.web.model.Response;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 资金工单业务字段查询
 */
public abstract class AbstractFinanceWorkOrderCheckHandler extends AdminJingxiSpotCheckHandler {
    private static final String SOURCE_ID_EXT_NAME = "businessId";
    @Autowired
    private List<AbstractFinanceWorkOrderStrategy<?>> financeWorkOrderStrategies;

    private Map<Integer, AbstractFinanceWorkOrderStrategy<?>> baseFinanceWorkOrderStrategyMap;

    @PostConstruct
    public void init() {
        baseFinanceWorkOrderStrategyMap = financeWorkOrderStrategies
                .stream().collect(Collectors.toMap(IBaseFinanceWorkOrderStrategy::orderType, Function.identity()));
    }


    @Override
    public Map<String, Object> getExtInfo(long workOrderId, int orderType) {
        // 查询工单ext
        final Response<BasicWorkOrder> orderResp = Von.read().getOrderBasicInfoById(workOrderId);
        if (orderResp.notOk() || Objects.isNull(orderResp.getData())) {
            return Collections.emptyMap();
        }
        BasicWorkOrder basicWorkOrder = orderResp.getData();
        List<WorkOrderExt> workOrderExt =
                basicWorkOrder.getBizExtList();
        if (CollectionUtils.isEmpty(workOrderExt)) {
            return Collections.emptyMap();
        }

        Map<String, Object> res = buildGeneralInfo(basicWorkOrder);
        Optional<WorkOrderExt> idExt = workOrderExt.stream()
                .filter(r -> StringUtils.equals(r.getExtName(), SOURCE_ID_EXT_NAME))
                .findFirst();
        if (idExt.isEmpty()) {
            return Collections.emptyMap();
        }
        String idStr = idExt.get().getExtValue();
        if (StringUtils.isBlank(idStr)) {
            return Collections.emptyMap();
        }
        Object source = getSource(Long.parseLong(idStr), orderType);
        if (source == null) {
            return Collections.emptyMap();
        }
        res.putAll(toMap(source));
        return res;
    }

    protected Map<String, Object> buildGeneralInfo(BasicWorkOrder basicWorkOrder) {
        Map<String, Object> res = new HashMap<>();
        if (basicWorkOrder == null) {
            return res;
        }

        res.put("type", basicWorkOrder.getBizExtList().stream()
                .filter(ext -> StringUtils.equals(ext.getExtName(), OrderExtName.riskLabelMarkWorkOrderScene.getName()))
                .findFirst());
        return res;
    }
    protected Object getSource(long sourceId, int orderType) {
        List<?> businessExtList
                = baseFinanceWorkOrderStrategyMap.get(orderType).getBusinessExt(Lists.newArrayList(sourceId));
        return CollectionUtils.isNotEmpty(businessExtList) ? businessExtList.get(0) : null;
    }

    protected Map<String, Object> toMap(Object source) {
        if (source == null) {
            return Collections.emptyMap();
        }
        String jsonString = JSON.toJSONString(source);
        return JSON.parseObject(jsonString, new TypeReference<>(){});
    }

    protected void updateMap(Map<String, Object> map, String sourceKey, String targetKey) {
        Object removed = map.remove(sourceKey);
        if (removed != null) {
            map.put(targetKey, removed);
        }
    }
}
