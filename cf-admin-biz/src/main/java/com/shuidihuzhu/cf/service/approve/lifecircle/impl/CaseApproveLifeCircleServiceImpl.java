package com.shuidihuzhu.cf.service.approve.lifecircle.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.*;
import com.shuidihuzhu.cf.admin.river.RiverReviewService;
import com.shuidihuzhu.cf.biz.admin.AdminWorkOrderBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.biz.crowdfunding.report.CfSendProveBiz;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationRecordDTO;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfHospitalAuditDao;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdFundingProgressDao;
import com.shuidihuzhu.cf.dao.crowdfunding.NewAdminCfFundUseAuditDao;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingOperationDelegate;
import com.shuidihuzhu.cf.delegate.finance.impl.FinanceDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.enums.approve.ApproveLifeCircleNodeStatusEnum;
import com.shuidihuzhu.cf.enums.approve.FundUseRejectReasonEnum;
import com.shuidihuzhu.cf.enums.approve.UseProgressStatusEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceReadFeignClient;
import com.shuidihuzhu.cf.finance.client.feign.CfPayeeFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.model.CfFinanceStateModel;
import com.shuidihuzhu.cf.finance.model.financestate.FinanceState;
import com.shuidihuzhu.cf.finance.model.financestate.FinanceStateEnum;
import com.shuidihuzhu.cf.finance.model.po.CfPayeeInfoChangeView;
import com.shuidihuzhu.cf.finance.model.vo.CfDrawCashApplyVo;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.admin.CfHospitalAuditInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.CrowdfundingInitialAuditInfo;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfRefuseReasonItemRecord;
import com.shuidihuzhu.cf.model.river.RiverReviewDO;
import com.shuidihuzhu.cf.model.river.RiverStatusEnum;
import com.shuidihuzhu.cf.model.river.RiverUsageTypeEnum;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.approve.lifecircle.CaseApproveLifeCircleService;
import com.shuidihuzhu.cf.vo.approve.ApproveLifeCircleVO;
import com.shuidihuzhu.cf.vo.crowdfunding.AdminCrowdfundingProgress;
import com.shuidihuzhu.client.cf.admin.model.CfCaseApproveDetail;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 案例审核生命周期
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class CaseApproveLifeCircleServiceImpl implements CaseApproveLifeCircleService {

    @Autowired
    private AdminCfInfoExtBiz adminCfInfoExtBiz;

    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Autowired
    private AdminWorkOrderBiz adminWorkOrderBiz;

    @Autowired
    private CfFinanceReadFeignClient cfFinanceReadFeignClient;

    @Autowired
    private AdminCfHospitalAuditDao adminCfHospitalAuditDao;

    @Autowired
    private FinanceDelegate financeDelegate;

    @Autowired
    private CfPayeeFeignClient cfPayeeFeignClient;

    @Autowired
    private AdminCrowdFundingProgressDao adminCrowdFundingProgressDao;

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @Autowired
    private AdminTaskUgcBiz adminTaskUgcBiz;

    @Resource
    private AdminCrowdfundingInfoStatusBiz crowdfundingInfoStatusBiz;

    @Autowired
    private RiverReviewService riverReviewService;

    @Autowired
    private CommonOperationRecordClient commonOperationRecordClient;

    @Autowired
    private ICrowdfundingOperationDelegate cfOperationDelegate;

    @Autowired
    private AdminCfRefuseReasonMsgBiz refuseReasonMsgBiz;

    @Autowired
    private AdminCfReportAddTrustBiz adminCfReportAddTrustBiz;

    @Autowired
    private IAdminCredibleInfoService adminCredibleInfoService;
    @Autowired
    private CfSendProveBiz cfSendProveBiz;

    @Autowired
    private NewAdminCfFundUseAuditDao newAdminCfFundUseAuditDao;

    private static ImmutableMap<CrowdfundingInfoStatusEnum, String> HOSPITAL_STATUS_MSG_MAP = ImmutableMap.of(
            CrowdfundingInfoStatusEnum.UN_SAVE, "已取消",
            CrowdfundingInfoStatusEnum.UN_SUBMITTED, "下发",
            CrowdfundingInfoStatusEnum.PASSED, "通过",
            CrowdfundingInfoStatusEnum.REJECTED, "驳回",
            CrowdfundingInfoStatusEnum.SUBMITTED, "待审核"
    );

    private static ImmutableList<Integer> CASE_APPROVE_TYPES = ImmutableList.of(
            WorkOrderType.cailiao_0.getType(),
            WorkOrderType.cailiao_1.getType(),
            WorkOrderType.cailiao_3.getType()
    );

    /**
     * 初审工单类型
     */
    private ImmutableList<Integer> CHU_SHEN_ORDER_TYPES = ImmutableList.of(
            WorkOrderType.yiliaoshenhe.getType(),
            WorkOrderType.highriskshenhe.getType(),
            WorkOrderType.ai_erci.getType()
    );

    private ImmutableList<Integer> AI_CHU_SHEN_ORDER_TYPES = ImmutableList.of(
            WorkOrderType.ai_content.getType(),
            WorkOrderType.ai_photo.getType()
    );

    @Override
    public OpResult<List<ApproveLifeCircleVO>> getLifeCircle(int caseId) {

        CrowdfundingInfo fundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
        if (fundingInfo == null) {
            return OpResult.createFailResult(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        String infoId = fundingInfo.getInfoId();

        List<ApproveLifeCircleVO> dataList = Stream.of(

                //目标金额审核
                getItemTargetAmountAudit(caseId),

                // 初审
                getItemInitialAudit(caseId),

                // 图文处理
                getItemBaseInfo(caseId),

                // 增信审核
                getCreditApprove(caseId),

                // 材料审核
                getItemInfoApprove(fundingInfo),

                // 医院核实
                getItemHospitalAudit(infoId),

                // 补充证明
                getAddTrustStatus(infoId,caseId),

                // 提现审核
                getItemDrawCash(fundingInfo),

                // 修改收款人审核
                getItemPayeeInfo(fundingInfo),

                // 资金用途审核
                getItemUseProgress(fundingInfo),

                // 资金状态节点
                getFinanceState(fundingInfo)
        )
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return OpResult.createSucResult(dataList);
    }

    private ApproveLifeCircleVO getAddTrustStatus(String infoUuid, int caseId) {
        CfSendProve cfSendProve = cfSendProveBiz.getLastOneByCaseId(caseId);
        CfCredibleInfoDO cfCredibleInfoDO;
        if (Objects.isNull(cfSendProve)) {
            CfReportAddTrust cfReportAddTrust = adminCfReportAddTrustBiz.getByInfoUuid(infoUuid);
            if (Objects.isNull(cfReportAddTrust)) {
                return null;
            }
            if (cfReportAddTrust.getAuditStatus() == AddTrustAuditStatusEnum.CANCEL.getCode()) {
                return null;
            }
            cfCredibleInfoDO = adminCredibleInfoService.queryBySubId(
                    cfReportAddTrust.getId(), CredibleTypeEnum.SUPPLY_VERFIFY.getKey());
        } else {
            cfCredibleInfoDO = adminCredibleInfoService.queryBySubId(
                    cfSendProve.getId(), CredibleTypeEnum.HELP_PROVE.getKey());
        }

        int auditStatus = cfCredibleInfoDO.getAuditStatus();
        AddTrustAuditStatusEnum addTrustAuditStatusEnum = AddTrustAuditStatusEnum.getByCode(auditStatus);
        String name = "举报补充证明";
        String status = "";
        long time = 0L;
        switch (addTrustAuditStatusEnum) {
            case UN_SUBMITTED:
                status = "已下发";
                time = cfCredibleInfoDO.getSendTime().getTime();
                break;
            case SUBMITTED:
                status = "已提交";
                time = cfCredibleInfoDO.getSubmitTime() == null ? 0 : cfCredibleInfoDO.getSubmitTime().getTime();
                break;
            case REJECTED:
                status = "审核驳回";
                time = cfCredibleInfoDO.getAuditTime() == null ? 0 : cfCredibleInfoDO.getAuditTime().getTime();
                break;
            case PASSED:
                status = "审核通过";
                time = cfCredibleInfoDO.getAuditTime() == null ? 0 : cfCredibleInfoDO.getAuditTime().getTime();
                break;
            case CANCEL:
                status = "已撤回";
                time = cfCredibleInfoDO.getUpdateTime() == null ? 0 : cfCredibleInfoDO.getUpdateTime().getTime();
                break;
            default:
        }
        return createVO(name, status, time, addTrustAuditStatusEnum, Lists.newArrayList());
    }

    private ApproveLifeCircleVO getCreditApprove(int caseId) {
        RiverReviewDO riverReview = riverReviewService.get(caseId, RiverUsageTypeEnum.CREDIT_INFO);

        // 单独审核的增信
        if (riverReview == null) {
            return null;
        }
        RiverStatusEnum status = RiverStatusEnum.parse(riverReview.getInfoStatus());
        ArrayList<OperationActionTypeEnum> types = Lists.newArrayList(OperationActionTypeEnum.RIVER_APPROVE_TYPES);
        types.add(OperationActionTypeEnum.RIVER_SUBMIT);
        OperationRecordDTO record = commonOperationRecordClient.getLastByBizIdAndActionTypes(riverReview.getId(), types);
        if (record == null) {
            return createVO("增信审核");
        }
        return createVO("增信审核", status.getMsg(), record.getActionTime().getTime(), status, Lists.newArrayList());
    }

    @Override
    public Map<Integer, CfCaseApproveDetail> getCaseApproveDetail(List<Integer> caseIds) {

        Map<Integer, CfCaseApproveDetail> result = Maps.newHashMap();
        if (CollectionUtils.isEmpty(caseIds)) {
            return result;
        }

        List<CrowdfundingInfo> allInfos = adminCrowdfundingInfoBiz.selectByCaseIdList(caseIds);
        for (CrowdfundingInfo info : allInfos) {
            CfCaseApproveDetail approveDetail = new CfCaseApproveDetail();
            approveDetail.setCaseId(info.getId());
            approveDetail.setApproveStatus(info.getStatus() != null ? info.getStatus().value() :  -1);

            ApproveLifeCircleVO circleVO = getItemInfoApprove(info);
            if (circleVO != null) {
                approveDetail.setRejectMsg(circleVO.getRejectMsgs());
                if (StringUtils.isNotBlank(circleVO.getTime())) {
                    approveDetail.setTime(DateUtil.getDateFromLongString(circleVO.getTime()));
                }
            }
            result.put(info.getId(), approveDetail);
        }

        return result;
    }

    /**
     * 资金用途审核
     *
     * @param fundingInfo
     * @return
     */
    private ApproveLifeCircleVO getItemUseProgress(CrowdfundingInfo fundingInfo) {
        AdminCrowdfundingProgress p = adminCrowdFundingProgressDao.getLastOneByCaseId(fundingInfo.getId());
        AdminCrowdfundingProgress newFundUse = newAdminCfFundUseAuditDao.getLastOneByCaseId(fundingInfo.getId());
        String name = "资金用途审核";
        if (Objects.isNull(p) && Objects.isNull(newFundUse)) {
            return null;
        }
        UseProgressStatusEnum statusEnum = null;
        String fundUseRejectedReason = "";
        long updateTime = 0L;
        if (Objects.nonNull(p)) {
            statusEnum = UseProgressStatusEnum.parse(p.getStatus());
            fundUseRejectedReason = p.getFundUseRejectedReason();
            updateTime = p.getUpdateTime().getTime();
        }
        if (Objects.nonNull(newFundUse)) {
            updateTime = newFundUse.getUpdateTime().getTime();
            statusEnum = UseProgressStatusEnum.parse(newFundUse.getStatus());
            String fundUseRejectedReasonNew = newFundUse.getFundUseRejectedReason();
            fundUseRejectedReason = StringUtils.equals(fundUseRejectedReasonNew, String.valueOf(FundUseRejectReasonEnum.REASON_ONE.getCode())) ?
                    FundUseRejectReasonEnum.REASON_ONE.getMsg() : fundUseRejectedReasonNew;
        }

        List<String> rejectMsgs = statusEnum == UseProgressStatusEnum.AUDIT_REJECTED_STATUS ?
                Lists.newArrayList(fundUseRejectedReason) : Lists.newArrayList();

        return createVO(name, statusEnum.getMsg(), updateTime, statusEnum, rejectMsgs);
    }

    /**
     * 获取资金状态
     * @param crowdfundingInfo
     * @return
     */
    private ApproveLifeCircleVO getFinanceState(CrowdfundingInfo crowdfundingInfo) {
        ApproveLifeCircleVO lifeCircleVO = new ApproveLifeCircleVO();
        FeignResponse<CfFinanceStateModel> feignResponse =
                cfFinanceReadFeignClient.getFinanceState(crowdfundingInfo.getId());
        if (feignResponse == null || feignResponse.getData() == null) {
            lifeCircleVO.setName("资金状态");
            lifeCircleVO.setNodeStatus(ApproveLifeCircleNodeStatusEnum.PROCESSING);
            lifeCircleVO.setStatus("获取状态超时");
            lifeCircleVO.setTime("");
            return lifeCircleVO;
        }
        CfFinanceStateModel stateModel = feignResponse.getData();
        long time = 0;
        if (stateModel.getTime() != null) {
            time = stateModel.getTime().getTime();
        }

        return createVO(stateModel.getName(),
                stateModel.getStatus(), time,
                ApproveLifeCircleNodeStatusEnum.valueOf(stateModel.getNodeStatus()));
    }

    private ApproveLifeCircleVO getItemPayeeInfo(CrowdfundingInfo fundingInfo) {
        CfInfoSimpleModel m = new CfInfoSimpleModel();
        m.setId(fundingInfo.getId());
        m.setInfoId(fundingInfo.getInfoId());
        FeignResponse<CfPayeeInfoChangeView> resp = cfPayeeFeignClient.getPayeeInfoRecord(m);
        CfPayeeInfoChangeView payeeInfoChangeView = getResponse(resp);
        String name = "修改收款人审核";
        if (payeeInfoChangeView == null) {
            return null;
        }
        int status = payeeInfoChangeView.getStatus();
        CfPayeeInfoChangeEnum.TypeRecordStatusEnum statusEnum = CfPayeeInfoChangeEnum.TypeRecordStatusEnum.getEnumByCode(status);
        if (statusEnum == null) {
            return null;
        }
        long time ;
        if (statusEnum == CfPayeeInfoChangeEnum.TypeRecordStatusEnum.APPROVING) {
            time = payeeInfoChangeView.getApplyTime();
        } else {
            time = payeeInfoChangeView.getUpdateTime();
        }

        List<String> rejectMsgs = statusEnum == CfPayeeInfoChangeEnum.TypeRecordStatusEnum.REJECTED ?
                Lists.newArrayList(payeeInfoChangeView.getRejectReasons()) : Lists.newArrayList();
        return createVO(name, statusEnum.getDesc(), time, statusEnum, rejectMsgs);
    }

    private ApproveLifeCircleVO getItemDrawCash(CrowdfundingInfo fundingInfo) {
        // 先判断是否处于资金提现流程
        FinanceState financeState = financeDelegate.getFinanceState(fundingInfo.getId());
        String name = "提现审核";
        if (financeState == null) {
            return createVO(name);
        }
        FinanceStateEnum.FinanceStatusEnum financeStatusEnum = financeState.getFinanceStatus();
        if (FinanceStateEnum.FinanceStatusEnum.IN_REFUND.equals(financeStatusEnum)) {
            log.warn("caseId:{} 处于退款态", fundingInfo.getId());
            return createVO(name);
        }
        Response<CfDrawCashApplyVo> response = financeDelegate.getApplyInfo(fundingInfo.getId());
        if (response.notOk()) {
            return createVO("获取资金数据失败");
        }

        CfDrawCashApplyVo drawCashApplyVo = response.getData();
        if (drawCashApplyVo == null) {
            return createVO(name);
        }
        CfDrawCashConstant.ApplyStatus applyStatus = CfDrawCashConstant.ApplyStatus.getByCode(drawCashApplyVo.getApplyStatus());

        List<String> rejectMsgs = applyStatus == CfDrawCashConstant.ApplyStatus.APPROVE_REJECT ?
                Lists.newArrayList(StringUtils.trimToEmpty(drawCashApplyVo.getRejectReason())) : Lists.newArrayList();

        switch (applyStatus) {
            case SUBMIT_APPROVE_PENDING:
                return createVO(name, applyStatus.getMsg(), drawCashApplyVo.getApplyTime().getTime(), applyStatus, rejectMsgs);
            case APPROVE_SUCCESS:
            case APPROVE_REJECT:
                return createVO(name, applyStatus.getMsg(), drawCashApplyVo.getApplyAuditTime() == null ? 0 : drawCashApplyVo.getApplyAuditTime().getTime(), applyStatus, rejectMsgs);
            case EMPTY_VALUE:
            case UNSUBMIT:
            default:
                return createVO(name);
        }
    }

    private ApproveLifeCircleVO getItemHospitalAudit(String infoId) {

        CfHospitalAuditInfoExt cfHospitalAuditInfo = adminCfHospitalAuditDao.getByInfoUuidNoCareDelete(infoId);
        if (cfHospitalAuditInfo == null) {
            return null;
        }
        int auditStatus = cfHospitalAuditInfo.getAuditStatus();
        CrowdfundingInfoStatusEnum statusEnum = CrowdfundingInfoStatusEnum.getByCode(auditStatus);
        String s = HOSPITAL_STATUS_MSG_MAP.get(statusEnum);

        // 取消算完成
        if (auditStatus == CrowdfundingInfoStatusEnum.UN_SAVE.getCode()) {
            return createVO("医院核实", s, cfHospitalAuditInfo.getUpdateTime().getTime(), ApproveLifeCircleNodeStatusEnum.PASSED);
        }

        List<String> rejectMsgs = statusEnum == CrowdfundingInfoStatusEnum.REJECTED ?
                Lists.newArrayList(StringUtils.trimToEmpty(cfHospitalAuditInfo.getReason())) : Lists.newArrayList();

        return createVO("医院核实", s, cfHospitalAuditInfo.getUpdateTime().getTime(), statusEnum, rejectMsgs);
    }

    /**
     * 材料审核
     *
     * @return
     */
    private ApproveLifeCircleVO getItemInfoApprove(CrowdfundingInfo fundingInfo) {
        String name = "材料审核";
        CrowdfundingStatus status = fundingInfo.getStatus();
        long time = 0;

        if (status == CrowdfundingStatus.APPROVE_PENDING) {
            return createVO(name);
        }

        List<CrowdfundingInfoStatus> crowdfundingInfoStatusList = crowdfundingInfoStatusBiz.getByInfoUuid(fundingInfo.getInfoId());

        if (CollectionUtils.isEmpty(crowdfundingInfoStatusList)){
            return createVO(name);
        }

        if (status.equals(CrowdfundingStatus.CROWDFUNDING_STATED)){
            time = fundingInfo.getBeginTime().getTime();
        }

        if (status.equals(CrowdfundingStatus.APPROVE_DENIED)){

            Optional<CrowdfundingInfoStatus> optional = crowdfundingInfoStatusList.stream().filter(r->r.getStatus() == CrowdfundingInfoStatusEnum.REJECTED.getCode()).sorted(Comparator.comparing(CrowdfundingInfoStatus::getLastModified).reversed()).findFirst();
            if (optional.isPresent()){
                time = optional.get().getLastModified().getTime();
            }
        }

        if (status.equals(CrowdfundingStatus.SUBMITTED)){

            Optional<CrowdfundingInfoStatus> optional = crowdfundingInfoStatusList.stream().filter(r->r.getStatus() == CrowdfundingInfoStatusEnum.SUBMITTED.getCode()).sorted(Comparator.comparing(CrowdfundingInfoStatus::getLastModified).reversed()).findFirst();
            if (optional.isPresent()){
                time = optional.get().getLastModified().getTime();
            }
        }

        return createVO(name, status.getApproveMsg(), time, status, getApproveRejectMsg(crowdfundingInfoStatusList));
    }

    private List<String> getApproveRejectMsg(List<CrowdfundingInfoStatus> infoStatusList) {
        List<String> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(infoStatusList)) {
            return result;
        }

        for (CrowdfundingInfoStatus infoStatus : infoStatusList) {

            if (infoStatus == null || infoStatus.getStatus() !=  CrowdfundingInfoStatusEnum.REJECTED.getCode()) {
                continue;
            }

            CfRefuseReasonMsg reasonMsgs = refuseReasonMsgBiz.selectByInfoIdAndType(infoStatus.getInfoUuid(), infoStatus.getType());
            if (reasonMsgs == null || StringUtils.isBlank(reasonMsgs.getItemReason())) {
                continue;
            }

            List<CfRefuseReasonItemRecord> rejectDetails = JSON.parseObject(reasonMsgs.getItemReason(), new TypeReference<List<CfRefuseReasonItemRecord>>(){});//已检查过
            if (CollectionUtils.isEmpty(rejectDetails)) {
                continue;
            }

            for (CfRefuseReasonItemRecord reasonItem : rejectDetails) {
                if (MapUtils.isEmpty(reasonItem.getReason())) {
                    continue;
                }
                for (Map.Entry<Integer, String> value : reasonItem.getReason().entrySet()) {
                    result.add(value.getValue());
                }
            }
        }

        return result;
    }

    /**
     * 图文处理项
     *
     * @param caseId
     * @return
     */
    private ApproveLifeCircleVO getItemBaseInfo(int caseId) {
        // 获取最新的一条图文工单

        ApproveLifeCircleVO lastOrder = getNewItemBaseInfo(caseId);
        if (lastOrder != null) {
            return lastOrder;
        }

        List<AdminWorkOrder> adminWorkOrders = adminWorkOrderBiz.selectUgcBaseInfoByPage(0,
                AdminWorkOrderConst.Type.UGC,
                ImmutableList.of(AdminWorkOrderConst.Task.INFO_BASE_WORD),
                1,
                1,
                caseId,
                null, null, null, null, null);

        // 没有图文工单 不展示此项
        if (CollectionUtils.isEmpty(adminWorkOrders)) {
            return null;
        }
        AdminWorkOrder adminWorkOrder = adminWorkOrders.get(0);
        AdminTaskUgc adminTaskUgc = adminTaskUgcBiz.selectByWorkOrderId(adminWorkOrder.getId());
        AdminUGCTask.Result result = AdminUGCTask.Result.getByCode(adminTaskUgc.getResult());
        return createVO("图文处理", result.getWord(), adminWorkOrder.getUpdateTime().getTime(), result,
                getBaseInfoRejectMsg(adminWorkOrder, result));
    }

    private ApproveLifeCircleVO getNewItemBaseInfo(int caseId) {

        Response<WorkOrderVO> lastOrderResp = cfWorkOrderClient.getLastWorkOrderByTypes(caseId,
                Lists.newArrayList(WorkOrderType.content.getType()));
        if (lastOrderResp == null || lastOrderResp.getData() == null) {
            return null;
        }
        WorkOrderVO workOrder = lastOrderResp.getData();
        HandleResultEnum result = HandleResultEnum.getFromType(workOrder.getHandleResult());
        String nodeMsg = "";
        long handleTime = 0;
        if (result != null) {
            switch (result) {
                case undoing:
                case doing:
                    nodeMsg = "待审核";
                    handleTime = workOrder.getCreateTime() != null ? workOrder.getCreateTime().getTime() : 0;
                    break;
                case audit_pass:
                    nodeMsg = "审核通过";
                    handleTime = workOrder.getUpdateTime() != null ? workOrder.getUpdateTime().getTime() : 0;

                    break;
                case audit_reject:
                    nodeMsg = "审核驳回";
                    handleTime = workOrder.getUpdateTime() != null ? workOrder.getUpdateTime().getTime() : 0;

                    break;
            }
        }

        if (workOrder != null) {
            return createVO("图文处理", nodeMsg, handleTime, result, Lists.newArrayList());
        }

        return null;
    }



    // 图文节点 --停止筹款的原因
    private List<String> getBaseInfoRejectMsg(AdminWorkOrder adminWorkOrder, AdminUGCTask.Result result) {

        if (result != AdminUGCTask.Result.SUGGEST_STOP_CROWDFUNDING) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(adminWorkOrder.getComment());
    }

    private ApproveLifeCircleVO getItemTargetAmountAudit(int caseId) {
        Response<WorkOrderVO> lastWorkOrder = cfWorkOrderClient.getLastWorkOrder(caseId, WorkOrderType.target_amount_reasonable_audit.getType());
        WorkOrderVO workOrderVO = Optional.ofNullable(lastWorkOrder)
                .map(Response::getData)
                .orElse(null);
        if (Objects.isNull(workOrderVO)) {
            return null;
        }
        Set<Integer> auditEndedSituations = Sets.newHashSet(
                HandleResultEnum.audit_reject.getType(),
                HandleResultEnum.audit_pass.getType(),
                HandleResultEnum.smart_audit_pass.getType());
        String name = "目标金额审核";
        String status = auditEndedSituations.contains(workOrderVO.getHandleResult())? "审核完成" : "待审核";
        ApproveLifeCircleNodeStatusEnum nodeStatusEnum = auditEndedSituations.contains(workOrderVO.getHandleResult())? ApproveLifeCircleNodeStatusEnum.PASSED : ApproveLifeCircleNodeStatusEnum.PROCESSING;
        return createVO(name, status, workOrderVO.getUpdateTime().getTime(), nodeStatusEnum);
    }
    /**
     * 获取初审信息
     *
     * @param caseId
     * @return
     */
    private ApproveLifeCircleVO getItemInitialAudit(int caseId) {
        CfInfoExt ext = adminCfInfoExtBiz.getByCaseId(caseId);
        FirstApproveStatusEnum firstApproveStatusEnum = FirstApproveStatusEnum.parse(ext.getFirstApproveStatus());

        String name = "初次审核";
        if (firstApproveStatusEnum.getCode() == FirstApproveStatusEnum.DEFAULT.getCode()) {
            return null;
        }
        Response<WorkOrderVO> lastWorkOrderResp = cfWorkOrderClient.getLastWorkOrderByTypes(caseId, CHU_SHEN_ORDER_TYPES);
        if (lastWorkOrderResp.notOk()) {
            return null;
        }
        WorkOrderVO workOrder = lastWorkOrderResp.getData();
        if (workOrder == null) {
            Response<WorkOrderVO> aiLastWorkOrderResp = cfWorkOrderClient.getLastWorkOrderByTypes(caseId, AI_CHU_SHEN_ORDER_TYPES);
            WorkOrderVO workOrderVO = Optional.ofNullable(aiLastWorkOrderResp)
                    .map(Response::getData)
                    .orElse(null);
            if (Objects.isNull(workOrderVO)) {
                return createVO("前置审核", "未开始", 0, ApproveLifeCircleNodeStatusEnum.TODO);
            }
            return createVO("前置审核", firstApproveStatusEnum.getApproveMsg(), 0, ApproveLifeCircleNodeStatusEnum.PROCESSING);
        }

        List<String> rejectMsgs = getInitialAuditRejectMsg(caseId, firstApproveStatusEnum);

        return createVO(name, firstApproveStatusEnum.getApproveMsg(), workOrder.getUpdateTime().getTime(), firstApproveStatusEnum, rejectMsgs);
    }

    // 初审节点的——驳回理由
    private List<String> getInitialAuditRejectMsg(int caseId, FirstApproveStatusEnum firstStatus) {

        List<String> result = Lists.newArrayList();

        if (firstStatus != FirstApproveStatusEnum.APPLY_FAIL) {
            return result;
        }

        CrowdfundingInitialAuditInfo initialAuditInfo = cfOperationDelegate.selectCrowdfundingInitialAuditInfoByCaseId(caseId);
        if (initialAuditInfo == null || StringUtils.isBlank(initialAuditInfo.getRejectDetail())) {
            return result;
        }

        Map<Integer, List<InitialAuditItem.RejectReason>> rejectDetailMap = JSON.parseObject(initialAuditInfo.getRejectDetail(),
                new TypeReference<Map<Integer, List<InitialAuditItem.RejectReason>>>(){});//已检查过

        for (Map.Entry<Integer, List<InitialAuditItem.RejectReason>> entry : rejectDetailMap.entrySet()) {
            if (CollectionUtils.isNotEmpty(entry.getValue())) {
                entry.getValue().stream().forEach(item->{result.add(item.getRejectMsg());});
            }
        }

        return result;
    }

    @NotNull
    private ApproveLifeCircleVO createVO(String name) {
        return createVO(name, "未提交", 0, ApproveLifeCircleNodeStatusEnum.TODO);
    }

    @NotNull
    private ApproveLifeCircleVO createVO(String name, String status, long time, ApproveLifeCircleNodeStatusEnum nodeStatusEnum) {
        return new ApproveLifeCircleVO(name, status, time > 0 ? DateUtil.getYmdhmsFromTimestamp(time) : "", nodeStatusEnum, Lists.newArrayList());
    }

    @NotNull
    private ApproveLifeCircleVO createVO(String name, String status, long time, Object statusEnum, List<String> rejectMsgs) {
        return new ApproveLifeCircleVO(name, status, time > 0 ? DateUtil.getYmdhmsFromTimestamp(time) : "", ApproveLifeCircleNodeStatusEnum.trans(statusEnum), rejectMsgs);
    }

    private static <T> T getResponse(FeignResponse<T> feignResponse) {
        if (feignResponse == null || feignResponse.notOk()) {
            log.warn("response error {}", JSON.toJSONString(feignResponse));
            return null;
        }
        return feignResponse.getData();
    }
}
