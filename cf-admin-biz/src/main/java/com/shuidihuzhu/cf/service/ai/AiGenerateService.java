package com.shuidihuzhu.cf.service.ai;



import com.shuidihuzhu.cf.model.ai.AiPromptConfig;
import com.shuidihuzhu.client.cf.admin.model.*;
import com.shuidihuzhu.client.model.ChatChunk;
import com.shuidihuzhu.client.model.ChatParam;
import com.shuidihuzhu.client.model.ChatStreamResult;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2024/5/29 7:32 PM
 */
public interface AiGenerateService {

    /**
     * 生成内容
     */
    void generate(AIGenerateParam aiGenerateParam);

    AiGenerateForwardResult generateForwardToSendMq(AiGenerateForwardParam aiGenerateForwardParam);

    /**
     * 流式生成内容
     */
    Flux<ChatChunk<ChatStreamResult>> streamGenerate(AIGenerateParam aiGenerateParam);

    Flux<ChatChunk<ChatStreamResult>> stream(ChatParam chatParam);

    /**
     * 保存提示词配置
     */
    void saveOrUpdatePromptConfig(AiPromptConfig aiPromptConfig);

    AiPromptConfig queryPromptConfig(Integer generateType, String modelType, Integer bizType);

    AiPromptConfig queryToPromptConfig(Integer generateType, Integer modelType, Integer bizType);

    /**
     * 查询生成记录
     */
    List<AiGenerateRecordVO> queryGenerateRecord(QueryParam queryParam);

    /**
     * 保存暂存方案
     */
    void stagingScheme(StagingParam stagingParam, long userId);

    /**
     * 查询暂存方案
     */
    StagingParam queryStagingScheme(QueryParam queryParam);

    /**
     * 反馈问题
     */
    void feedback(FeedbackParam feedbackParam, long userId);

}
