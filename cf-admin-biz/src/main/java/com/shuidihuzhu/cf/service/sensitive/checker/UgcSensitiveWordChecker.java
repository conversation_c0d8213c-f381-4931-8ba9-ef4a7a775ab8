package com.shuidihuzhu.cf.service.sensitive.checker;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.ugc.model.domain.risk.RiskControlWordCategoryDO;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordCheckContext;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordResult;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.sensitive.adapter.ISensitiveAdapter;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service
public class UgcSensitiveWordChecker implements ISensitiveChecker {

    @Resource
    private IRiskDelegate riskDelegate;

    @Override
    public AdminWorkOrderConst.Task getTask() {
        return AdminWorkOrderConst.Task.UGC_USE_SCENE_FILTER;
    }

    @Override
    public <T> OpResult<RiskWordResult> isHit(T data, ISensitiveAdapter<T> adapter) {

        String content = adapter.getContent(data);
        RiskWordCheckContext ctx = RiskWordCheckContext.builder()
                .content(content)
                .useScenes(Lists.newArrayList(RiskControlWordCategoryDO.RiskWordUseScene.PUBLISH_UGC.getCode()))
                .isCheckAll(true)
                .build();
        return riskDelegate.isHit(ctx);

    }
}
