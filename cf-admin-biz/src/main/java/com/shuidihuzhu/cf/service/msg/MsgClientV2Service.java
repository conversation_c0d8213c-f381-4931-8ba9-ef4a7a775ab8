package com.shuidihuzhu.cf.service.msg;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.client.baseservice.msg.v2.MsgClientV2;
import com.shuidihuzhu.msg.vo.MessageFeedBack;
import com.shuidihuzhu.msg.vo.MsgResponse;
import com.shuidihuzhu.msg.vo.rpc.MsgRecord;
import com.shuidihuzhu.msg.vo.rpc.MsgRecordBatch;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class MsgClientV2Service {

    @Autowired
    private MsgClientV2 msgClientV2;

    /**
     * 发送微信不带变量消息
     *
     * @param modelNum
     */
    public void sendWxMsg(String modelNum, List<Long> userIdList) {
        List<MsgRecord> recordList = this.buildWxMsgRecord(userIdList, 0);
        Lists.partition(recordList, 500).forEach(v -> {
            MsgRecordBatch msgRecordBatch = MsgRecordBatch.build(modelNum, this.getBusinessInfo(modelNum), v);
            MsgResponse<List<MessageFeedBack>> msgResponse = msgClientV2.saveBatchV2(msgRecordBatch);
            log.info("发送微信不带变量消息 modelNum:{}, msgRecordBatch:{}, msgResponse:{}", modelNum, msgRecordBatch, JSON.toJSONString(msgResponse));
        });
    }

    /**
     * 发送短信不带变量消息
     * crypto 手机号是否加密
     *
     * @param modelNum
     */
    public void sendSmsMsg(String modelNum, List<String> mobileList, boolean crypto) {
        List<MsgRecord> recordList = this.buildSmsMsgRecord(mobileList, crypto);
        Lists.partition(recordList, 500).forEach(v -> {
            MsgRecordBatch msgRecordBatch = MsgRecordBatch.build(modelNum, this.getBusinessInfo(modelNum), v);
            MsgResponse<List<MessageFeedBack>> msgResponse = msgClientV2.saveBatchV2(msgRecordBatch);
            log.info("发送短信不带变量消息 modelNum:{}, msgRecordBatch:{}, msgResponse:{}", modelNum, msgRecordBatch, JSON.toJSONString(msgResponse));
        });
    }

    /**
     * 发送APP不带变量消息 （极光）
     *
     * @param modelNum
     */
    public void sendAppMsg(String modelNum, List<Long> userIdList) {
        List<MsgRecord> recordList = this.buildAppMsgRecord(userIdList);
        Lists.partition(recordList, 500).forEach(v -> {
            MsgRecordBatch msgRecordBatch = MsgRecordBatch.build(modelNum, this.getBusinessInfo(modelNum), v);
            MsgResponse<List<MessageFeedBack>> msgResponse = msgClientV2.saveBatchV2(msgRecordBatch);
            log.info("发送App不带变量消息 modelNum:{}, msgRecordBatch:{}, msgResponse:{}", modelNum, msgRecordBatch, JSON.toJSONString(msgResponse));
        });
    }

    /**
     * 发送微信带变量消息
     *
     * @param modelNum
     * @param wxMsgMap
     */
    public void sendWxParamsMsg(String modelNum, Map<Long, Map<Integer, String>> wxMsgMap) {
        List<MsgRecord> recordList = this.buildWxParamsMsgRecord(wxMsgMap, 0);
        Lists.partition(recordList, 500).forEach(v -> {
            MsgRecordBatch msgRecordBatch = MsgRecordBatch.build(modelNum, this.getBusinessInfo(modelNum), v);
            MsgResponse<List<MessageFeedBack>> msgResponse = msgClientV2.saveBatchV2(msgRecordBatch);
            log.info("发送微信带变量消息 modelNum:{}, msgRecordBatch:{}, msgResponse:{}", modelNum, msgRecordBatch, JSON.toJSONString(msgResponse));
        });
    }

    /**
     * 发送短信带变量消息
     * crypto 手机号是否加密
     *
     * @param modelNum
     * @param smsMsgMap
     */
    public void sendSmsParamsMsg(String modelNum, Map<String, Map<Integer, String>> smsMsgMap, boolean crypto) {
        List<MsgRecord> recordList = this.buildSmsParamsMsgRecord(smsMsgMap, crypto);
        Lists.partition(recordList, 500).forEach(v -> {
            MsgRecordBatch msgRecordBatch = MsgRecordBatch.build(modelNum, this.getBusinessInfo(modelNum), v);
            MsgResponse<List<MessageFeedBack>> msgResponse = msgClientV2.saveBatchV2(msgRecordBatch);
            log.info("发送短信带变量消息 modelNum:{}, msgRecordBatch:{}, msgResponse:{}", modelNum, msgRecordBatch, JSON.toJSONString(msgResponse));
        });
    }


    /**
     * 发送微信不带变量消息 指定公众号
     *
     * @param modelNum
     */
    public void sendWxMsg(String modelNum, List<Long> userIdList, int userThirdType) {
        List<MsgRecord> recordList = this.buildWxMsgRecord(userIdList, userThirdType);
        Lists.partition(recordList, 500).forEach(v -> {
            MsgRecordBatch msgRecordBatch = MsgRecordBatch.build(modelNum, this.getBusinessInfo(modelNum), v);
            MsgResponse<List<MessageFeedBack>> msgResponse = msgClientV2.saveBatchV2(msgRecordBatch);
            log.info("发送微信不带变量消息 modelNum:{}, msgRecordBatch:{}, msgResponse:{}", modelNum, msgRecordBatch, JSON.toJSONString(msgResponse));
        });
    }

    /**
     * 发送微信带变量消息 指定公众号
     *
     * @param modelNum
     * @param wxMsgMap
     */
    public void sendWxParamsMsg(String modelNum, Map<Long, Map<Integer, String>> wxMsgMap, int userThirdType) {
        List<MsgRecord> recordList = this.buildWxParamsMsgRecord(wxMsgMap, userThirdType);
        Lists.partition(recordList, 500).forEach(v -> {
            MsgRecordBatch msgRecordBatch = MsgRecordBatch.build(modelNum, this.getBusinessInfo(modelNum), v);
            MsgResponse<List<MessageFeedBack>> msgResponse = msgClientV2.saveBatchV2(msgRecordBatch);
            log.info("发送微信带变量消息 modelNum:{}, msgRecordBatch:{}, msgResponse:{}", modelNum, msgRecordBatch, JSON.toJSONString(msgResponse));
        });
    }

    /**
     * 发送APP带变量消息 (极光)
     *
     * @param modelNum
     * @param appMsgMap
     */
    public void sendAppParamsMsg(String modelNum, Map<Long, Map<Integer, String>> appMsgMap) {
        List<MsgRecord> recordList = this.buildAppParamsMsgRecord(appMsgMap);
        Lists.partition(recordList, 500).forEach(v -> {
            MsgRecordBatch msgRecordBatch = MsgRecordBatch.build(modelNum, this.getBusinessInfo(modelNum), v);
            MsgResponse<List<MessageFeedBack>> msgResponse = msgClientV2.saveBatchV2(msgRecordBatch);
            log.info("发送APP带变量消息 modelNum:{}, msgRecordBatch:{}, msgResponse:{}", modelNum, msgRecordBatch, JSON.toJSONString(msgResponse));
        });
    }

    private List<MsgRecord> buildWxMsgRecord(List<Long> userIdList, int userThirdType) {
        List<MsgRecord> recordList = Lists.newArrayList();
        for (Long userId : userIdList) {
            if (Objects.isNull(userId) || userId == 0) {
                continue;
            }
            MsgRecord msgRecord = new MsgRecord();
            msgRecord.setUserId(userId);
            if (userThirdType != 0) {
                msgRecord.setUserThirdType(userThirdType);
            }
            recordList.add(msgRecord);
        }
        return recordList;
    }

    private List<MsgRecord> buildSmsMsgRecord(List<String> mobileList, boolean crypto) {
        List<MsgRecord> recordList = Lists.newArrayList();
        for (String mobile : mobileList) {
            if (StringUtils.isEmpty(mobile)) {
                continue;
            }
            MsgRecord msgRecord = new MsgRecord();
            if (crypto) {
                msgRecord.setCryptoMobile(mobile);
            } else {
                msgRecord.buildmobile(mobile);
            }
            recordList.add(msgRecord);
        }
        return recordList;
    }

    private List<MsgRecord> buildAppMsgRecord(List<Long> userIdList) {
        List<MsgRecord> recordList = Lists.newArrayList();
        for (Long userId : userIdList) {
            if (Objects.isNull(userId) || userId == 0) {
                continue;
            }
            MsgRecord msgRecord = new MsgRecord();
            msgRecord.setUserId(userId);

            recordList.add(msgRecord);
        }
        return recordList;
    }

    private List<MsgRecord> buildWxParamsMsgRecord(Map<Long, Map<Integer, String>> wxMsgMap, int userThirdType) {
        List<MsgRecord> recordList = Lists.newArrayList();
        for (Map.Entry<Long, Map<Integer, String>> entry : wxMsgMap.entrySet()) {
            Long userId = entry.getKey();
            Map<Integer, String> paramsMap = entry.getValue();
            if (Objects.isNull(userId) || userId == 0 || MapUtils.isEmpty(paramsMap)) {
                continue;
            }
            MsgRecord msgRecord = new MsgRecord();
            msgRecord.setUserId(userId);
            msgRecord.setParams(paramsMap);
            if (userThirdType != 0) {
                msgRecord.setUserThirdType(userThirdType);
            }
            recordList.add(msgRecord);
        }
        return recordList;
    }

    private List<MsgRecord> buildSmsParamsMsgRecord(Map<String, Map<Integer, String>> wxMsgMap, boolean crypto) {
        List<MsgRecord> recordList = Lists.newArrayList();
        for (Map.Entry<String, Map<Integer, String>> entry : wxMsgMap.entrySet()) {
            String mobile = entry.getKey();
            Map<Integer, String> paramsMap = entry.getValue();
            if (StringUtils.isEmpty(mobile) || MapUtils.isEmpty(paramsMap)) {
                continue;
            }
            MsgRecord msgRecord = new MsgRecord();
            if (crypto) {
                msgRecord.setCryptoMobile(mobile);
            } else {
                msgRecord.buildmobile(mobile);
            }
            msgRecord.setParams(paramsMap);
            recordList.add(msgRecord);
        }
        return recordList;
    }

    private List<MsgRecord> buildAppParamsMsgRecord(Map<Long, Map<Integer, String>> appMsgMap) {
        List<MsgRecord> recordList = Lists.newArrayList();
        for (Map.Entry<Long, Map<Integer, String>> entry : appMsgMap.entrySet()) {
            Long userId = entry.getKey();
            Map<Integer, String> paramsMap = entry.getValue();
            if (Objects.isNull(userId) || userId == 0 || MapUtils.isEmpty(paramsMap)) {
                continue;
            }
            MsgRecord msgRecord = new MsgRecord();
            msgRecord.setUserId(userId);
            msgRecord.setParams(paramsMap);

            recordList.add(msgRecord);
        }
        return recordList;
    }

    private String getBusinessInfo(String modelNum) {
        return modelNum + "_" + System.currentTimeMillis();
    }

}
