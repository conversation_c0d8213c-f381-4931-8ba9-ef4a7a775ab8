package com.shuidihuzhu.cf.service.sensitive.checker;

import com.alibaba.druid.sql.visitor.functions.Char;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.sensitive.SensitiveSpecialSymbolLogBiz;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordResult;
import com.shuidihuzhu.cf.constants.admin.PatternCons;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.sensitive.adapter.ISensitiveAdapter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2020-03-26
 */
@Service
@RefreshScope
public class SpecialSymbolChecker implements ISensitiveChecker {


    private static Pattern PATTERN_NUMBER;

    private static Pattern PATTERN_ALL;

    @Value("${sensitive.number-limit-count:3}")
    private long NUMBER_LIMIT_COUNT;

    @Value("${sensitive.number-symbol:}")
    public void setNumberSymbol(String numberSymbol) {
        PATTERN_NUMBER = Pattern.compile(numberSymbol);
    }

    @Value("${sensitive.other-char-symbol:}")
    public void setOtherCharSymbol(String otherCharSymbol) {
        PATTERN_ALL = Pattern.compile(otherCharSymbol);

    }


    @Override
    public AdminWorkOrderConst.Task getTask() {
        return AdminWorkOrderConst.Task.SPECIAL_SYMBOL;
    }

    @Resource
    private AdminCrowdfundingInfoBiz infoBiz;
    @Resource
    private SensitiveSpecialSymbolLogBiz specialSymbolLogBiz;

    @Override
    public <T> OpResult<RiskWordResult> isHit(T data, ISensitiveAdapter<T> adapter) {
        //内容发布者不是当下内容所在案例的发起人
        long userId = adapter.getUserId(data);
        long caseUserId = getCaseUserId(adapter.getCaseId(data));
        if (userId == caseUserId) {
            return OpResult.createSucResult(
                    new RiskWordResult(true, "", Lists.newArrayList()));
        }
        OpResult<RiskWordResult> result =  isContainsSpecialSymbolChecker(adapter.getContent(data));
        if (!result.getData().isPassed()) {
            saveLog(data, adapter, result.getData().getHitWords());
        }
        return result;
    }

    @Async
    public <T> void saveLog(T data, ISensitiveAdapter<T> adapter, List<String> hitWords) {
        specialSymbolLogBiz.insert(adapter.getBizId(data), adapter.getCaseId(data),
                adapter.getSensitiveRecordBizType(data), adapter.getContent(data),
                JSON.toJSONString(hitWords));
    }


    /**
     *
     * @param caseId    案例id
     * @return 获取当前案例的UserId
     */
    private long getCaseUserId(int caseId) {
        if (caseId <= 0) {
            return 0;
        }
        CrowdfundingInfo crowdfundingInfo = infoBiz.getFundingInfoById(caseId);
        return crowdfundingInfo == null ? 0 : crowdfundingInfo.getUserId();
    }

    /**
     *
     * 以下字符连续出现5个及以上（重复字符计算多次），
     * 且数字类字符出现5个及以上（重复字符计算多次），
     * 且去重的数字类字符出现3个及以上
     * @param content   需要匹配的内容
     * @return 匹配结果
     */
    private OpResult<RiskWordResult> isContainsSpecialSymbolChecker(String content) {
        //验证是否有超过5个以上的字符
        RiskWordResult r = match(PATTERN_ALL, PatternCons.BLANK.matcher(content).replaceAll(""));
        List<String> numHitWords = r.getHitWords();
        if (r.isPassed() && CollectionUtils.isEmpty(numHitWords)) {
            return OpResult.createSucResult(new RiskWordResult(true, content, Lists.newArrayList()));
        }
        boolean isCountLimt = false;
        //判断去重的数字类字符出现3个及以上
        for (String hitWord : numHitWords) {
            // 匹配数字字符
            List<String> numberHitWords= matchGetList(PATTERN_NUMBER, hitWord);
            if (CollectionUtils.isEmpty(numberHitWords)){
                continue;
            }
            String numberHitWord = Joiner.on("").join(numberHitWords);
            //对数字类型进行去重
            if (numberHitWord.length() < NUMBER_LIMIT_COUNT){
                continue;
            }
            List<Character> charList = Lists.newArrayList();
            for (char c : numberHitWord.toCharArray()) {
                charList.add(c);
            }
            long vailtCount = charList.stream().distinct().count();
            if (vailtCount < NUMBER_LIMIT_COUNT) {
                continue;
            }
            isCountLimt = true;
            break;
        }

        if (!isCountLimt) {
            return OpResult.createSucResult(new RiskWordResult(true, content, Lists.newArrayList()));
        }
        return OpResult.createSucResult(r);
    }

    /**
     *   全角转化为全角
     */
    public static String ToDBC(String input) {
        char[] c = input.toCharArray();
        for (int i = 0; i < c.length; i++) {
            if (c[i] == 12288) {
                //全角空格为12288，半角空格为32
                c[i] = (char) 32;
                continue;
            }
            if (c[i] > 65280 && c[i] < 65375) {
                //其他字符半角(33-126)与全角(65281-65374)的对应关系是：均相差65248
                c[i] = (char) (c[i] - 65248);
            }
        }
        return new String(c);
    }


    /**
     *
     * @param pattern           正则表达式
     * @param input             匹配字符串
     * @return 匹配结果
     */
    private RiskWordResult match(Pattern pattern, String input) {
        List<String> hitWords = matchGetList(pattern, input);
        return new RiskWordResult(CollectionUtils.isEmpty(hitWords), input, hitWords);
    }

    private  List<String> matchGetList(Pattern pattern, String input) {
        Matcher matcher = pattern.matcher(input);
        List<String> hitWords = Lists.newArrayList();
        if (!matcher.find()) {
            return hitWords;
        }
        matcher.reset();
        while (matcher.find()){
            String g = matcher.group();
            hitWords.add(g);
        }
        return hitWords;
    }


    public static void main(String[] args) {
        //System.out.println(JSON.toJSONString(isContainsSpecialSymbolChecker("122222我12345你├┝┞┟┠┡┢┣│他\u2026\u00b7丰富asdas").getData()));
    }

}
