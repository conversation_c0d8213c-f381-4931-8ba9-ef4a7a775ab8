package com.shuidihuzhu.cf.service.rejectmanager.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.model.admin.common.SupplyProgressReasonItem;
import com.shuidihuzhu.cf.service.rejectmanager.ISeaReasonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2020-01-09 19:28
 **/
@Slf4j
@Service("supplyProgressRejectService")
public class SupplyProgressReasonService implements ISeaReasonService<SupplyProgressReasonItem> {


    @Override
    public List<SupplyProgressReasonItem> listAllRejectInfo() {
        return SupplyProgressReasonItem.all_rejectItems;
    }

    @Override
    public List<SupplyProgressReasonItem> listRejectByIds(List<Integer> rejectIds) {
        if (CollectionUtils.isEmpty(rejectIds)) {
            return Lists.newArrayList();
        }
        return SupplyProgressReasonItem.all_rejectItems
                .stream()
                .filter(item -> rejectIds.contains(item.getId()))
                .collect(Collectors.toList());
    }

    @Override
    public boolean validateRejectIds(List<Integer> rejectIds) {
        rejectIds = Optional.ofNullable(rejectIds).orElse(Lists.newArrayList());
        return SupplyProgressReasonItem.all_rejectIds.containsAll(rejectIds);
    }
}
