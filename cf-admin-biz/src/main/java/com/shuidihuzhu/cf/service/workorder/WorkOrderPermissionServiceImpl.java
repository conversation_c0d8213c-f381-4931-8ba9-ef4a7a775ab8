package com.shuidihuzhu.cf.service.workorder;

import com.shuidihuzhu.cf.admin.delegate.SeaUserAuthDelegate;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.enums.sona.RoleEnum;
import com.shuidihuzhu.cf.response.OpResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2018-09-29  16:00
 */
@Service
@Slf4j
public class WorkOrderPermissionServiceImpl implements WorkOrderPermissionService {

    @Resource
    private SeaUserAuthDelegate seaUserAuthDelegate;

    @Override
    public Set<Integer> getAllReportUserIds() {
        OpResult<Set<Integer>> listOpResult = seaUserAuthDelegate.listUserByPermission(
                RoleEnum.REPORT_TRANSFER.getPermission());
        if (listOpResult.isFail()) {
            return Collections.emptySet();
        }
        return listOpResult.getData();
    }


    private int getTaskTypeByRole(RoleEnum role) {
        if (role == RoleEnum.FIRST_APPROVE_A) {
            return AdminWorkOrderConst.TaskType.HEAD_CASE.getCode();
        }
        if (role == RoleEnum.FIRST_APPROVE_B) {
            return AdminWorkOrderConst.TaskType.WAIST_CASE.getCode();
        }
        if (role == RoleEnum.FIRST_APPROVE_C) {
            return AdminWorkOrderConst.TaskType.TAIL_CASE.getCode();
        }
        return 0;
    }

    private RoleEnum getRoleByTaskType(int taskType) {
        AdminWorkOrderConst.TaskType type = AdminWorkOrderConst.TaskType.getByCode(taskType);
        if (type == null) {
            return RoleEnum.DEFAULT;
        }
        if (type == AdminWorkOrderConst.TaskType.HEAD_CASE) {
            return RoleEnum.FIRST_APPROVE_A;
        }
        if (type == AdminWorkOrderConst.TaskType.WAIST_CASE) {
            return RoleEnum.FIRST_APPROVE_B;
        }
        if (type == AdminWorkOrderConst.TaskType.TAIL_CASE) {
            return RoleEnum.FIRST_APPROVE_C;
        }
        return RoleEnum.DEFAULT;
    }

}
