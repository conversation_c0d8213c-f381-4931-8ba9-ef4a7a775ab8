package com.shuidihuzhu.cf.service.workorder.initialAudit.ai.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.material.model.CfPropertyInsuranceInfoModel;
import com.shuidihuzhu.cf.dao.crowdfunding.CfAiMaterialsDao;
import com.shuidihuzhu.cf.model.crowdfunding.ai.AiConditionEnum;
import com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterials;
import com.shuidihuzhu.cf.model.crowdfunding.ai.LayOutField;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditSearchService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.ai.AiCondition;
import com.shuidihuzhu.cf.vo.approve.CreditInfoVO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2021/2/26
 */
@Slf4j
@Service("aiMixHouse")
public class AiMixHouseCondition implements AiCondition {


    @Autowired
    private CfAiMaterialsDao cfAiMaterialsDao;

    @Autowired
    private InitialAuditSearchService initialAuditSearchService;


    @Override
    public boolean check(int caseId, String inputValue) {

        CreditInfoVO creditInfoVO = initialAuditSearchService.getCreditInfoVO(caseId);
        if (Objects.isNull(creditInfoVO)) {
            log.info("aiMixHouseCondition creditInfoVO is null {}", caseId);
            return false;
        }
        if (Objects.isNull(creditInfoVO.getHouseProperty()) && Objects.isNull(creditInfoVO.getSelfBuiltHouse())) {
            log.info("aiMixHouseCondition creditInfoVO is null {}", caseId);
            return false;
        }
        CreditInfoVO.HousePropertyInfoVO houseProperty = creditInfoVO.getHouseProperty();
        CreditInfoVO.HousePropertyInfoVO selfBuiltHouse = creditInfoVO.getSelfBuiltHouse();
        int houseTotalCount = Objects.nonNull(houseProperty) && Objects.nonNull(houseProperty.getTotalCount()) ? houseProperty.getTotalCount() : 0;
        int selfTotalCount = Objects.nonNull(selfBuiltHouse) && Objects.nonNull(selfBuiltHouse.getTotalCount()) ? selfBuiltHouse.getTotalCount() : 0;

        AiContentWorkOrderHouse aiContentWorkOrderHouse = covertAiContentWorkOrderHouse(caseId, creditInfoVO);
        if (Objects.isNull(aiContentWorkOrderHouse)) {
            log.info("aiMixHouseCondition aiContentWorkOrderHouse is null {}", caseId);
            return false;
        }

        String hasDescHouse = aiContentWorkOrderHouse.getHasDescHouse();

        // 有无描述房产 : 无，直接过审
        if (StringUtils.equals(AiConditionEnum.wu.getStringCode(), hasDescHouse)) {
            return false;
        }
        // 13:自建房；14：其他房产；15：未知
        if (StringUtils.equals(inputValue, "13") && aiContentWorkOrderHouse.getHouseTypeList().contains("13")) {
            return checkHouseCount(aiContentWorkOrderHouse.getHasSelfBuildingHouseCount(), aiContentWorkOrderHouse.getHasSelfBuildingHouseValue(), selfTotalCount, aiContentWorkOrderHouse.getSelfBuildingHouseCount(), aiContentWorkOrderHouse.isInRangeSelf());
        }
        if (StringUtils.equals(inputValue, "14") && aiContentWorkOrderHouse.getHouseTypeList().contains("14")) {
            return checkHouseCount(aiContentWorkOrderHouse.getHasOtherHouseCount(), aiContentWorkOrderHouse.getHasOtherHouseValue(), houseTotalCount, aiContentWorkOrderHouse.getOtherHouseCount(), aiContentWorkOrderHouse.isInRangeOther());
        }
        if (StringUtils.equals(inputValue, "15") && aiContentWorkOrderHouse.getHouseTypeList().contains("15")) {
            return checkHouseCount(aiContentWorkOrderHouse.getHasUnknownHouseCount(), aiContentWorkOrderHouse.getHasUnknownHouseValue(), houseTotalCount + selfTotalCount, aiContentWorkOrderHouse.getUnknownHouseCount(), aiContentWorkOrderHouse.isInRangeUnknown());
        }
        return false;
    }


    private boolean checkHouseCount(String hasCount, String hasValue, Integer userCount, Integer workOrderHouseCount, boolean inRange) {
        if (Objects.equals(userCount, 0)) {
            return true;
        }
        if (StringUtils.equals(hasCount, AiConditionEnum.wu.getStringCode()) && StringUtils.equals(hasValue, AiConditionEnum.you.getStringCode())) {
            return !inRange;
        }
        if (StringUtils.equals(hasCount, AiConditionEnum.you.getStringCode()) && StringUtils.equals(hasValue, AiConditionEnum.wu.getStringCode())) {
            return !Objects.equals(userCount, workOrderHouseCount);
        }
        if (StringUtils.equals(hasCount, AiConditionEnum.you.getStringCode()) && StringUtils.equals(hasValue, AiConditionEnum.you.getStringCode())) {
            return !inRange || !Objects.equals(userCount, workOrderHouseCount);
        }
        return false;
    }

    @Data
    public static class AiContentWorkOrderHouse {
        private String hasDescHouse;
        private String houseType;
        private List<String> houseTypeList;
        private String hasSelfBuildingHouseCount;
        private int selfBuildingHouseCount;
        private String hasOtherHouseCount;
        private int otherHouseCount;
        private String hasUnknownHouseCount;
        private int unknownHouseCount;
        private String hasSelfBuildingHouseValue;
        private double selfBuildingHouseValue;
        private String hasOtherHouseValue;
        private double otherHouseValue;
        private String hasUnknownHouseValue;
        private double unknownHouseValue;
        private boolean inRangeSelf;
        private boolean inRangeOther;
        private boolean inRangeUnknown;
    }


    private AiContentWorkOrderHouse covertAiContentWorkOrderHouse(int caseId, CreditInfoVO creditInfoVO) {
        CfAiMaterials z = cfAiMaterialsDao.getByCaseId(caseId, CfAiMaterials.zType);
        List<LayOutField> layOutFields = Optional.ofNullable(z)
                .map(CfAiMaterials::getFields)
                .orElse(Lists.newArrayList());
        if (CollectionUtils.isEmpty(layOutFields)) {
            return null;
        }
        Map<String, String> collect = layOutFields.stream()
                .collect(Collectors.toMap(LayOutField::getFieldKey, LayOutField::getFieldValue, (x, y) -> x));
        String jsonString = JSONObject.toJSONString(collect);
        AiContentWorkOrderHouse aiContentWorkOrderHouse = JSONObject.parseObject(jsonString, AiContentWorkOrderHouse.class);
        if (Objects.isNull(aiContentWorkOrderHouse)) {
            return null;
        }
        List<String> list = StringUtils.isNotEmpty(aiContentWorkOrderHouse.getHouseType()) ? Splitter.on(",").splitToList(aiContentWorkOrderHouse.getHouseType()) : new ArrayList<>();
        aiContentWorkOrderHouse.setHouseTypeList(list);
        CreditInfoVO.HousePropertyInfoVO selfBuiltHouse = creditInfoVO.getSelfBuiltHouse();
        CreditInfoVO.HousePropertyInfoVO houseProperty = creditInfoVO.getHouseProperty();
        aiContentWorkOrderHouse.setInRangeSelf(judeInRangeSelfAndOther(selfBuiltHouse, (int) aiContentWorkOrderHouse.getSelfBuildingHouseValue()));
        aiContentWorkOrderHouse.setInRangeOther(judeInRangeSelfAndOther(houseProperty, (int) aiContentWorkOrderHouse.getOtherHouseValue()));
        aiContentWorkOrderHouse.setInRangeUnknown(judeInRangeUnknown(selfBuiltHouse, houseProperty, (int) aiContentWorkOrderHouse.getUnknownHouseValue()));

        return aiContentWorkOrderHouse;
    }

    private boolean judeInRangeSelfAndOther(CreditInfoVO.HousePropertyInfoVO housePropertyInfoVO, int workOrderValue) {
        if (Objects.isNull(housePropertyInfoVO)) {
            return false;
        }
        Integer totalValueUserDefined = housePropertyInfoVO.getTotalValueUserDefined();
        if (Objects.nonNull(totalValueUserDefined) && totalValueUserDefined > 0) {
            return 0 <= workOrderValue && workOrderValue <= totalValueUserDefined;
        }
        if (Objects.isNull(housePropertyInfoVO.getTotalValueRangeType())) {
            return false;
        }
        CfPropertyInsuranceInfoModel.HouseValueRange houseValueRange = CfPropertyInsuranceInfoModel.HouseValueRange.valueOfCode(housePropertyInfoVO.getTotalValueRangeType());
        if (Objects.isNull(houseValueRange)) {
            return false;
        }
        return houseValueRange.getFrom() <= workOrderValue && workOrderValue <= houseValueRange.getTo();
    }

    private boolean judeInRangeUnknown(CreditInfoVO.HousePropertyInfoVO houseSelf, CreditInfoVO.HousePropertyInfoVO houseOther, int workOrderValue) {
        int low = 0;
        int high = 0;
        if (Objects.isNull(houseSelf) || Objects.isNull(houseOther)) {
            return false;
        }
        Integer totalValueUserDefinedSelf = houseSelf.getTotalValueUserDefined();
        if (Objects.nonNull(totalValueUserDefinedSelf) && totalValueUserDefinedSelf > 0) {
            low = low + totalValueUserDefinedSelf;
            high = high + totalValueUserDefinedSelf;
        }
        Integer totalValueUserDefinedOther = houseOther.getTotalValueUserDefined();
        if (Objects.nonNull(totalValueUserDefinedOther) && totalValueUserDefinedOther > 0) {
            low = low + totalValueUserDefinedOther;
            high = high + totalValueUserDefinedOther;
        }

        if (Objects.nonNull(houseSelf.getTotalValueRangeType())) {
            CfPropertyInsuranceInfoModel.HouseValueRange houseValueRange = CfPropertyInsuranceInfoModel.HouseValueRange.valueOfCode(houseSelf.getTotalValueRangeType());
            if (Objects.nonNull(houseValueRange) && Objects.nonNull(houseValueRange.getTo())) {
                low = low + houseValueRange.getFrom();
                high = high + houseValueRange.getTo();
            }
        }
        if (Objects.nonNull(houseOther.getTotalValueRangeType())) {
            CfPropertyInsuranceInfoModel.HouseValueRange houseValueRange = CfPropertyInsuranceInfoModel.HouseValueRange.valueOfCode(houseOther.getTotalValueRangeType());
            if (Objects.nonNull(houseValueRange) && Objects.nonNull(houseValueRange.getTo())) {
                low = low + houseValueRange.getFrom();
                high = high + houseValueRange.getTo();
            }
        }
        return low <= workOrderValue && workOrderValue <= high;
    }
}
