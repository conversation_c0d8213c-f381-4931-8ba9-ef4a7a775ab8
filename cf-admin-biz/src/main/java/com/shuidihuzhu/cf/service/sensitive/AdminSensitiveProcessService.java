package com.shuidihuzhu.cf.service.sensitive;

import com.shuidihuzhu.cf.client.feign.CfCommonFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @time 2019/12/9 上午11:59
 * @desc
 */
@Service
public class AdminSensitiveProcessService {

    @Autowired
    private CfCommonFeignClient cfCommonFeignClient;

    public String sensitiveProcess(String source){

        if(StringUtils.isEmpty(source)){
            return source;
        }

        FeignResponse<String> res = cfCommonFeignClient.sensitiveProcess(source);

        return res.getData();
    }
}
