package com.shuidihuzhu.cf.service.cfOperatingProfile;

import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfCaseOperateStatus;

public interface CfOperateStatusBiz {

    int getRelationVideoOperateType(String infoUuid);

    AdminErrorCode operateRelationVideo(int userId, String infoUuid, int operateType, String operateComment);

    CfCaseOperateStatus selectLastOperateReason(String infoUuid);
}
