package com.shuidihuzhu.cf.service.crowdfunding.strategy;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportProblemLabel;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfReportProblemRaiserLabel;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2019-12-16 16:50
 **/
public interface IReportLabelCreateStrategy {

    List<CfReportProblemRaiserLabel> create(int caseId, List<CfReportProblemLabel> allLabels);

}
