package com.shuidihuzhu.cf.service.search;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-01-16  16:42
 */
public interface EsSearchService {

    List<Integer> searchUgcWordOrder(int orderType,
                                     List<Integer> orderTasks,
                                     int operatorId,
                                     List<Integer> contentTypes,
                                     Integer result,
                                     Date startDate,
                                     Date endDate,
                                     String hitWords,
                                     long current,
                                     int size, boolean isPre);

}
