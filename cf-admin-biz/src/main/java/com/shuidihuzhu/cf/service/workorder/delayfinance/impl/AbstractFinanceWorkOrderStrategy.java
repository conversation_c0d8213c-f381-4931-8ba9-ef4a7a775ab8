package com.shuidihuzhu.cf.service.workorder.delayfinance.impl;

import com.shuidihuzhu.cf.service.workorder.delayfinance.IBaseFinanceWorkOrderStrategy;
import com.shuidihuzhu.cf.service.workorder.delayfinance.delegate.IFinanceWorkOrderDelegate;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-12-01 4:04 下午
 **/
@Slf4j
public abstract class AbstractFinanceWorkOrderStrategy<T> implements IBaseFinanceWorkOrderStrategy {

    @Resource
    protected IFinanceWorkOrderDelegate financeWorkOrderDelegate;

    private List<Long> getFinanceBusinessIds(List<? extends WorkOrderVO> workOrderVoList) {
        if (CollectionUtils.isEmpty(workOrderVoList)) {
            return Collections.emptyList();
        }
        return workOrderVoList
                .stream()
                .map(WorkOrderVO::getFinanceBusinessId).distinct()
                .filter(r -> r > 0)
                .collect(Collectors.toList());
    }

    /**
     * 获取资金业务数据
     *
     * @param financeBusinessIds -
     * @return -
     */
    public abstract List<T> getBusinessExt(List<Long> financeBusinessIds);

    @Override
    public void fillBusinessExt(List<? extends WorkOrderVO> workOrderVoList) {
        if (CollectionUtils.isEmpty(workOrderVoList)) {
            return;
        }
        if (workOrderVoList.stream().anyMatch(r -> r.getOrderType() != orderType())) {
            return;
        }
        List<Long> financeBusinessIds = getFinanceBusinessIds(workOrderVoList);
        if (CollectionUtils.isEmpty(financeBusinessIds)) {
            return;
        }
        List<T> businessExtList = getBusinessExt(financeBusinessIds);
        if (CollectionUtils.isEmpty(businessExtList)) {
            return;
        }
        Map<Long, T> businessExtMap = businessExtList
                .stream().collect(Collectors.toMap(r -> {
                    try {
                        Method method = r.getClass().getMethod("getId");
                        Object invoke = method.invoke(r);
                        if (Objects.isNull(invoke)) {
                            return 0L;
                        }
                        return Long.parseLong(String.valueOf(invoke));
                    } catch (Exception e) {
                        log.error("get id error", e);
                        return 0L;
                    }
                }, Function.identity(), (a, b) -> b));
        workOrderVoList.forEach(item -> {
            T ext = businessExtMap.get(item.getFinanceBusinessId());
            if (Objects.nonNull(ext)) {
                item.setBusinessExt(ext);
            }
        });
    }
}
