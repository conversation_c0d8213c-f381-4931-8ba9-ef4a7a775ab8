package com.shuidihuzhu.cf.service;

import com.alibaba.druid.pool.DruidDataSource;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.dao.admin.CfScheduleAlarmDataSourceDao;
import com.shuidihuzhu.cf.domain.cf.CfScheduleAlarmDataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/9/28
 */
@Slf4j
@Service
public class CfScheduleAlarmDataSourceService {

    @Resource
    private Environment environment;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    @Qualifier("shuidiCfTdDataSourceJdbcTemplate")
    private JdbcTemplate defaultJdbcTemplate;

    @Resource
    private CfScheduleAlarmDataSourceDao dataSourceDao;

    private Map<String, JdbcTemplate> projectJdbcTemplateMap = Maps.newHashMap();
    private Map<String, JdbcTemplate> jdbcTemplateMap = Maps.newHashMap();


    public boolean add(long userId, String dataSourceName, String configProperty) {
        CfScheduleAlarmDataSource dataSource = new CfScheduleAlarmDataSource();
        dataSource.setName(dataSourceName);
        dataSource.setConfigProperty(configProperty);
        dataSource.setOperatorId(userId);

        int num = dataSourceDao.insert(dataSource);
        return num > 0;
    }

    public List<CfScheduleAlarmDataSource> getAll() {
        return dataSourceDao.getAll();
    }

    public Map<String, String> getDataSource() {
//        if (MapUtils.isEmpty(jdbcTemplateMap)) {
//            return Maps.newHashMap();
//        }

        Map<String, String> dataSourceNameMap = Maps.newHashMap();
        dataSourceNameMap.putAll(projectJdbcTemplateMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getKey)));
        dataSourceNameMap.putAll(jdbcTemplateMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getKey)));

        return dataSourceNameMap;
    }

    public JdbcTemplate getJdbcTemplate(String dataSourceName) {
        if (StringUtils.isBlank(dataSourceName)) {
            return defaultJdbcTemplate;
        }

        JdbcTemplate jdbcTemplate = projectJdbcTemplateMap.get(dataSourceName);
        if (jdbcTemplate == null) {
            jdbcTemplate = jdbcTemplateMap.getOrDefault(dataSourceName, defaultJdbcTemplate);
        }
        return jdbcTemplate;
    }

    public void initProjectDataSources() {
        //初始化项目中自带的dataSource
        Map<String, DataSource> dataSourceMap = applicationContext.getBeansOfType(DataSource.class);
        dataSourceMap.remove("multipleDataSource");

        dataSourceMap.entrySet().stream().forEach(entry -> {
            if (!projectJdbcTemplateMap.containsKey(entry.getKey())) {
                projectJdbcTemplateMap.put(entry.getKey(), new JdbcTemplate(entry.getValue()));
            }
        });
    }


    public void initDataSources() {
        jdbcTemplateMap.clear();
        List<CfScheduleAlarmDataSource> dataSourceConfigs = getAll();
        if (CollectionUtils.isEmpty(dataSourceConfigs)) {
            return;
        }

        dataSourceConfigs.forEach(dataSourceConfig -> {
            DataSource dataSource = initDataSource(dataSourceConfig.getConfigProperty());
            if (dataSource != null) {
                jdbcTemplateMap.put(dataSourceConfig.getName(), new JdbcTemplate(dataSource));
            }
        });
    }

    private DataSource initDataSource(String prefix) {
        if (StringUtils.isBlank(prefix)) {
            return null;
        }

        try {

            String name = environment.getProperty(prefix + ".name");
            String url  = environment.getProperty(prefix + ".url");
            String username = environment.getProperty(prefix + ".username");
            String password = environment.getProperty(prefix + ".password");

            if (StringUtils.isBlank(username)) {
                username = environment.getProperty("spring.datasource.username");
            }

            if (StringUtils.isBlank(password)) {
                password = environment.getProperty("spring.datasource.password");
            }

            if (StringUtils.isAnyBlank(url, username, password)) {
                log.warn("数据源信息不全, prefix:{}", prefix);
                return null;
            }


            DruidDataSource dataSource = new DruidDataSource();
            dataSource.setName(name);
            dataSource.setUrl(url);
            dataSource.setUsername(username);
            dataSource.setPassword(password);
            dataSource.init();
            return dataSource;
        } catch (Exception e) {
            log.warn("initDataSource error, prefix:{}", prefix, e);
        }
        return null;
    }


    @PostConstruct
    public void init() {
        initProjectDataSources();

        initDataSources();
    }
}
