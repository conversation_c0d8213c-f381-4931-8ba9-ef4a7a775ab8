package com.shuidihuzhu.cf.service.amount;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.amount.AmountReasonableBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.adminpure.enums.AmountReasonableCommonRuleLimitEnum;
import com.shuidihuzhu.cf.client.adminpure.enums.AmountReasonableTaskPlan;
import com.shuidihuzhu.cf.client.adminpure.enums.AmountReasonableTaskStatus;
import com.shuidihuzhu.cf.client.adminpure.enums.AmountReasonableTaskType;
import com.shuidihuzhu.cf.client.adminpure.model.amount.CfAmountReasonableTask;
import com.shuidihuzhu.cf.client.adminpure.model.amount.CfAmountReasonableTaskWorkOrder;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceRefundFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.cfOperatingProfile.CfOperatingCaseLabelBiz;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.cf.vo.amount.AmountReasonableTaskMqVo;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCoreFeignClient;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCreateParam;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderLevel;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.util.BeanUtils;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

/**
 * @Author: wangpeng
 * @Date: 2022/8/24 19:39
 * @Description:
 */
@Slf4j
@Service
public class AmountReasonableService {

    @Resource
    private Producer producer;

    @Resource
    private CfFinanceRefundFeignClient cfFinanceRefundFeignClient;

    @Resource
    private CfOperatingCaseLabelBiz cfOperatingCaseLabelBiz;

    @Resource
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;

    @Resource
    private AmountReasonableBiz amountReasonableBiz;

    @Resource
    private MsgClientV2Service msgClientV2Service;

    @Resource
    private WorkOrderCoreFeignClient workOrderCoreFeignClient;

    private final Pattern pattern = Pattern.compile(".*(承诺|愿意|若).{0,40}(退回|退还|退给).*");


    public Integer getCommonRuleResult(int caseId, int taskType) {
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if (Objects.isNull(crowdfundingInfo)) {
            return AmountReasonableCommonRuleLimitEnum.HAS_COMMIT_REFUND.getValue();
        }
        boolean hasCommitRefund = pattern.matcher(crowdfundingInfo.getContent()).find();
        if (!hasCommitRefund) {
            return AmountReasonableCommonRuleLimitEnum.HAS_COMMIT_REFUND.getValue();
        }
        FeignResponse<Boolean> existRefund = cfFinanceRefundFeignClient.getExistRefund(caseId);
        Boolean existRefundBool = Optional.ofNullable(existRefund)
                .map(FeignResponse::getData)
                .orElse(false);
        if (existRefundBool) {
            return AmountReasonableCommonRuleLimitEnum.EXIST_REFUND.getValue();
        }
        boolean patientDieLabel = cfOperatingCaseLabelBiz.selectHasLabelContent(caseId, "患者去世");
        if (patientDieLabel) {
            return AmountReasonableCommonRuleLimitEnum.PATIENT_DIE_LABEL.getValue();
        }
        List<CfAmountReasonableTask> reasonableTaskList = amountReasonableBiz.getByCaseIdAndTaskType(caseId, taskType);
        if (CollectionUtils.isNotEmpty(reasonableTaskList)) {
            return AmountReasonableCommonRuleLimitEnum.NOT_FIRST_TASK.getValue();
        }
        return AmountReasonableCommonRuleLimitEnum.SUCCESS.getValue();
    }

    public void firstIssueTask(int caseId, int taskType) {
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if (Objects.isNull(crowdfundingInfo)) {
            return;
        }
        CfAmountReasonableTask cfAmountReasonableTask = new CfAmountReasonableTask();
        cfAmountReasonableTask.setCaseId(caseId);
        cfAmountReasonableTask.setTaskType(taskType);
        cfAmountReasonableTask.setTaskInfoId(UUID.randomUUID().toString());
        int insertAmountReasonableTask = amountReasonableBiz.insertAmountReasonableTask(cfAmountReasonableTask);
        if (insertAmountReasonableTask == 0 || cfAmountReasonableTask.getId() == 0) {
            log.info("firstIssueTask insert fail {}", cfAmountReasonableTask);
            return;
        }
        Map<Integer, String> params = Maps.newHashMap();
        params.put(1, cfAmountReasonableTask.getTaskInfoId());
        params.put(2, AmountReasonableTaskType.getByCode(cfAmountReasonableTask.getTaskType()).getDesc());
        Map<Long, Map<Integer, String>> mapMap = Maps.newHashMap();
        mapMap.put(crowdfundingInfo.getUserId(), params);
        String wxNum = "HKM3377";
        msgClientV2Service.sendWxParamsMsg(wxNum, mapMap);

        AmountReasonableTaskMqVo taskMqVo = new AmountReasonableTaskMqVo();
        taskMqVo.setCaseId(cfAmountReasonableTask.getCaseId());
        taskMqVo.setTaskInfoId(cfAmountReasonableTask.getTaskInfoId());
        taskMqVo.setTaskType(taskType);
        taskMqVo.setDelayReason(1);
        Message message = Message.ofDelay(MQTopicCons.CF, MQTagCons.CF_AMOUNT_REASONABLE_TASK, MQTagCons.CF_AMOUNT_REASONABLE_TASK + "_" + cfAmountReasonableTask.getId(), taskMqVo, 15, TimeUnit.DAYS);
        producer.send(message);
    }

    public void submitTask(CfAmountReasonableTask cfAmountReasonableTask) {
        log.info("AmountReasonableService submitTask {}", cfAmountReasonableTask);
        CfAmountReasonableTask task = amountReasonableBiz.getByTaskInfoId(cfAmountReasonableTask.getTaskInfoId());
        if (Objects.isNull(task)) {
            return;
        }
        cfAmountReasonableTask.setId(task.getId());
        int updateCfAmountReasonableTask = amountReasonableBiz.updateCfAmountReasonableTask(cfAmountReasonableTask);
        if (updateCfAmountReasonableTask == 0) {
            return;
        }
        // 生成工单
        WorkOrderCreateParam workOrderCreateParam = new WorkOrderCreateParam();
        workOrderCreateParam.setOrderType(WorkOrderType.fund_use_amount_reasonable.getType());
        workOrderCreateParam.setCaseId(cfAmountReasonableTask.getCaseId());
        workOrderCreateParam.setOrderlevel(OrderLevel.D.getType());
        workOrderCreateParam.addExt(OrderExtName.amountReasonableTaskId, task.getId());
        Response<Long> longResponse = workOrderCoreFeignClient.create(workOrderCreateParam);
        long workOrderId = Optional.ofNullable(longResponse)
                .map(Response::getData)
                .orElse(0L);
        if (workOrderId == 0) {
            return;
        }
        CfAmountReasonableTaskWorkOrder cfAmountReasonableTaskWorkOrder = new CfAmountReasonableTaskWorkOrder();
        BeanUtils.copyProperties(cfAmountReasonableTask, cfAmountReasonableTaskWorkOrder);
        cfAmountReasonableTaskWorkOrder.setWorkOrderId(workOrderId);
        cfAmountReasonableTaskWorkOrder.setId(0);
        cfAmountReasonableTaskWorkOrder.setTaskId(task.getId());
        amountReasonableBiz.insertAmountReasonableTaskWorkOrder(cfAmountReasonableTaskWorkOrder);

    }
}
