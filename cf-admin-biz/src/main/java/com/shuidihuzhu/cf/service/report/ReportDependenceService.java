package com.shuidihuzhu.cf.service.report;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingReportBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.IAdminCredibleInfoService;
import com.shuidihuzhu.cf.biz.crowdfunding.report.CfSendProveBiz;
import com.shuidihuzhu.cf.client.adminpure.model.report.ReportDependenceInfoVo;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.enums.crowdfunding.AddTrustAuditStatusEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CredibleTypeEnum;
import com.shuidihuzhu.cf.enums.report.ReportPayMethodEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfCredibleInfoDO;
import com.shuidihuzhu.cf.model.crowdfunding.CfSendProve;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/11/17  8:48 下午
 */
@Service
@Slf4j
public class ReportDependenceService {

    @Resource
    private CfWorkOrderClient cfWorkOrderClient;

    @Resource
    private ReportScheduleService reportScheduleService;

    @Resource
    private CfSendProveBiz cfSendProveBiz;

    @Resource
    private IAdminCredibleInfoService adminCredibleInfoService;

    public OperationResult<ReportDependenceInfoVo> getReportDependenceInfo(int caseId) {

        Response<WorkOrderVO> response = cfWorkOrderClient.getLastWorkOrderByTypes(caseId, WorkOrderType.REPORT_TYPES);
        WorkOrderVO workOrderVO = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(null);
        //案例没有举报工单
        if (Objects.isNull(workOrderVO)) {
            return OperationResult.success(ReportDependenceInfoVo.builder()
                    .isReport(false)
                    .isReportComplete(null)
                    .isSendProxyOrder(null)
                    .isConfirm(null)
                    .isSplitDraw(null)
                    .build());
        }

        boolean isReportComplete = workOrderVO.getHandleResult() == HandleResultEnum.end_deal.getType() || workOrderVO.getHandleResult() == HandleResultEnum.noneed_deal.getType();
        if (isReportComplete) {
            //举报是否被标记为分批打款
            ReportPayMethodEnum payMethodEnum = reportScheduleService.getPayMethodByCaseId(caseId);
            boolean isSplitDraw = Optional.ofNullable(payMethodEnum).map(ReportPayMethodEnum::getCode).orElse(0) == ReportPayMethodEnum.PAY_IN_BATCH.getCode();
            return OperationResult.success(ReportDependenceInfoVo.builder()
                    .isReport(true)
                    .isReportComplete(true)
                    .isSendProxyOrder(null)
                    .isConfirm(null)
                    .isSplitDraw(isSplitDraw)
                    .build());
        }

        //是否下发代录入
        CfSendProve cfSendProve = cfSendProveBiz.getLastOneByCaseId(caseId);
        boolean isSendProxyOrder = Objects.nonNull(cfSendProve);
        if (!isSendProxyOrder) {
            return OperationResult.success(ReportDependenceInfoVo.builder()
                    .isReport(true)
                    .isReportComplete(false)
                    .isSendProxyOrder(false)
                    .isConfirm(null)
                    .isSplitDraw(null)
                    .build());
        }

        //用户是否确认
        CfCredibleInfoDO cfCredibleInfoDO = adminCredibleInfoService.getLastOneByCaseId(caseId, CredibleTypeEnum.HELP_PROVE.getKey());
        int auditStatus = Optional.ofNullable(cfCredibleInfoDO).map(CfCredibleInfoDO::getAuditStatus).orElse(0);
        boolean isConfirm = auditStatus == AddTrustAuditStatusEnum.SUBMITTED.getCode() ||
                auditStatus == AddTrustAuditStatusEnum.PASSED.getCode() ||
                auditStatus == AddTrustAuditStatusEnum.REJECTED.getCode();
        return OperationResult.success(ReportDependenceInfoVo.builder()
                .isReport(true)
                .isReportComplete(false)
                .isSendProxyOrder(true)
                .isConfirm(isConfirm)
                .isSplitDraw(null)
                .build());
    }
}
