package com.shuidihuzhu.cf.service.workorder.delayfinance.impl;

import com.shuidihuzhu.cf.finance.model.vo.CfEndCaseWorkOrderVo;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 随筹随取资金池工单
 *
 * <AUTHOR>
 * @since 2022-12-01 3:35 下午
 **/
@Service
public class EndCaseFinanceWorkOrderStrategy extends AbstractFinanceWorkOrderStrategy<CfEndCaseWorkOrderVo> {

    @Override
    public List<CfEndCaseWorkOrderVo> getBusinessExt(List<Long> financeBusinessIds) {
        return this.financeWorkOrderDelegate.getEndCaseVo(financeBusinessIds);
    }

    @Override
    public int orderType() {
        return WorkOrderType.end_case_handle.getType();
    }
}
