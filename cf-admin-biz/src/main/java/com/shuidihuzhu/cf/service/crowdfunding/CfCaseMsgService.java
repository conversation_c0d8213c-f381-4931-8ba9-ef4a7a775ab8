package com.shuidihuzhu.cf.service.crowdfunding;

import com.shuidihuzhu.cf.dao.crowdfunding.CfCaseMsgDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfCaseMsg;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @DATE 2020/10/19
 */
@Service
public class CfCaseMsgService {

    @Autowired
    private CfCaseMsgDao cfCaseMsgDao;


    public int saveMsg(int caseId,
                       int msgId,
                       String msgValue){

        CfCaseMsg cfCaseMsg = new CfCaseMsg();
        cfCaseMsg.setCaseId(caseId);
        cfCaseMsg.setMsgId(msgId);
        cfCaseMsg.setMsgValue(msgValue);

        CfCaseMsg msg = cfCaseMsgDao.queryByCaeId(caseId,msgId);
        int value = 0;
        if (msg == null){
            value = cfCaseMsgDao.insert(cfCaseMsg);
        }else {
            value = cfCaseMsgDao.update(cfCaseMsg);
        }
        return value;
    }


    public CfCaseMsg getMsg(int caseId, int msgId){

        CfCaseMsg msg = cfCaseMsgDao.queryByCaeId(caseId,msgId);

        return msg;
    }
}
