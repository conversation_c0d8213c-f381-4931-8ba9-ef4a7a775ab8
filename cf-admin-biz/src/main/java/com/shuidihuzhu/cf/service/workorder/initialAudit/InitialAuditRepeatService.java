package com.shuidihuzhu.cf.service.workorder.initialAudit;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.client.subject.caseend.CaseEndClient;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.client.base.enums.BaseErrorCodeEnum;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.feign.CfMaterialReadClient;
import com.shuidihuzhu.cf.client.material.model.CfPropertyInsuranceInfoModel;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationRecordDTO;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.domain.caseinfo.CaseEndRecordDO;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.enums.report.ReportPayMethodEnum;
import com.shuidihuzhu.cf.facade.AdminApproveFacade;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.caseRepeat.InitialRepeatCaseView;
import com.shuidihuzhu.cf.risk.client.aegis.EngineAnalysisClient;
import com.shuidihuzhu.cf.risk.model.aegis.RiskObject;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cf.service.report.ReportScheduleService;
import com.shuidihuzhu.cf.vo.approve.CreditInfoVO;
import com.shuidihuzhu.client.cf.clewtrack.model.CfUserInvitedLaunchCaseRecordModel;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class InitialAuditRepeatService {

    @Autowired
    private AdminCrowdfundingInfoBiz infoBiz;
    @Autowired
    private IRiskDelegate riskDelegate;
    @Autowired
    private AdminCfInfoExtBiz infoExtBiz;
    @Autowired
    private AdminApproveService adminApproveService;
    @Autowired
    private CfFirstApproveOperatorBiz firstApproveBiz;
    @Autowired
    private EngineAnalysisClient analysisClient;
    @Autowired
    private AdminApproveFacade approveFacade;
    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Autowired
    private SeaAccountClientV1 accountClientV1;
    @Autowired
    private CfMaterialVerityHistoryBiz verityHistoryBiz;
    @Autowired
    private CommonOperationRecordClient commonOperationClient;
    @Autowired
    private CfWorkOrderClient workOrderClient;
    @Autowired
    private CfMaterialReadClient cfMaterialReadClient;
    @Autowired
    private ReportScheduleService reportScheduleService;
    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;
    @Autowired
    private AdminCrowdfundingReportBiz adminCrowdfundingReportBiz;
    @Autowired
    private MaskUtil maskUtil;
    @Autowired
    private AdminCfRepeatInfoBiz cfRepeatInfoBiz;

    @Resource
    private CaseEndClient caseEndClient;

    private static String MODEL_ID = "8416765febc945b991f9ff947297881a";

    /**
     * 初审详情页使用
     */
    private static final int CHU_CI_DETAIL = 0;
    /**
     * 除了初审详情页的其他页面
     */
    private static final int NO_CHU_CI_DETAIL = 1;

    @Value("${apollo.no-show.repeat.case.white-list:}")
    private String whiteList;

    @Autowired
    private ApplicationService applicationService;

    public List<InitialRepeatCaseView> queryRepeatCaseDetail(int baseCaseId, long workOrderId, long userId, int tag) {
        if (tag == NO_CHU_CI_DETAIL) {
            return getReportRepeatDetail(baseCaseId, workOrderId, userId);
        }

        // 测试环境白名单不查重复案例
        if (StringUtils.isNotBlank(whiteList) && applicationService.isDevelopment()) {
            List<String> whiteList = Lists.newArrayList(this.whiteList.split(","));
            if (whiteList.contains(String.valueOf(userId))) {
                return Lists.newArrayList();
            }
        }

        // 先查快照数据～
        OperationRecordDTO operationRecordDTO = commonOperationClient.getLastByBizIdAndActionTypes(workOrderId,
                OperationActionTypeEnum.INITIAL_CASE_REPEAT_SNAPSHOT);

        if (Objects.nonNull(operationRecordDTO)) {
            log.info("可以找到工单id的快照数据.worOrderId:{} param:{}", workOrderId, operationRecordDTO);
            List<InitialRepeatCaseView> initialRepeatCaseViewList = JSON.parseArray(operationRecordDTO.getRemark(), InitialRepeatCaseView.class);
            if (CollectionUtils.isEmpty(initialRepeatCaseViewList)) {
                return initialRepeatCaseViewList;
            }

            return fillInitialRepeatCaseViewList(initialRepeatCaseViewList);
        }

        List<InitialRepeatCaseView> repeatResult = Lists.newArrayList();
        List<InitialRepeatCaseView> allResult = Lists.newArrayList();

        Map<Integer, Set<InitialRepeatCaseView.RepeatType>> repeatTypeMappings = getCaseRepeatClassify(baseCaseId, workOrderId, userId);
        if (MapUtils.isEmpty(repeatTypeMappings)) {
            return allResult;
        }

        List<Integer> allCaseIds = Lists.newArrayList();
        allCaseIds.add(baseCaseId);
        allCaseIds.addAll(repeatTypeMappings.keySet());

        Map<Integer, CrowdfundingInfo> infoMappings = infoBiz.getMapByIds(allCaseIds);
        Map<Integer, CfFirsApproveMaterial> firstMaterialMappings = riskDelegate.getMapByInfoIds(allCaseIds);
        Map<Integer, CfInfoExt> infoExtMappings = infoExtBiz.getMapByCaseIds(allCaseIds);
        Map<Integer, CfUserInvitedLaunchCaseRecordModel> caseRaiseMappings = adminApproveService.getCaseChannelRecordMap(allCaseIds);

        for (Integer caseId : allCaseIds) {
            Set<InitialRepeatCaseView.RepeatType> repeatTypes = repeatTypeMappings.get(caseId);

            InitialRepeatCaseView caseView = new InitialRepeatCaseView(caseId, repeatTypes);

            fillCrowdfundingInfo(infoMappings.get(caseId), caseView);
            fillCfInfoExt(infoExtMappings.get(caseId), caseView);
            fillFirstMaterial(firstMaterialMappings.get(caseId), caseView);
            fillChannel(caseRaiseMappings.get(caseId), caseView);
            fillCaseRiskLevel(caseId, caseView);
            fillCreditVO(caseId, caseView);

            // 基本的案例放在第一个位置
            if (caseId.equals(baseCaseId)) {
                caseView.setRepeatTypeDesc("当前案例");
                allResult.add(caseView);
            } else {
                repeatResult.add(caseView);
            }
        }

        if (CollectionUtils.isNotEmpty(repeatResult)) {
            repeatResult = repeatResult.stream().sorted(Comparator.comparing(InitialRepeatCaseView::getCaseCreateTime).reversed())
                    .collect(Collectors.toList());
            allResult.addAll(repeatResult);
        }

        return fillInitialRepeatCaseViewList(allResult);
    }

    /**
     * 兼容适配逻辑
     * 考虑前端开发量,尽量前端不改
     */
    private List<InitialRepeatCaseView> getReportRepeatDetail(int baseCaseId, long workOrderId, long userId) {
        PageInfo<AdminCfRepeatView> repeatViewPageInfo = cfRepeatInfoBiz.selectRepeatInfoPageByCaseId(baseCaseId, 1, 20);
        List<AdminCfRepeatView> list = repeatViewPageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        List<Integer> caseIds = list.stream()
                .map(AdminCfRepeatView::getCaseId)
                .collect(Collectors.toList());
        return promoteRepeatDetail(caseIds);
    }

    private List<InitialRepeatCaseView> promoteRepeatDetail(List<Integer> allCaseIds) {
        ArrayList<InitialRepeatCaseView> result = Lists.newArrayList();
        Map<Integer, CrowdfundingInfo> infoMappings = infoBiz.getMapByIds(allCaseIds);
        Map<Integer, CfFirsApproveMaterial> firstMaterialMappings = riskDelegate.getMapByInfoIds(allCaseIds);
        Map<Integer, CfInfoExt> infoExtMappings = infoExtBiz.getMapByCaseIds(allCaseIds);
        Map<Integer, CfUserInvitedLaunchCaseRecordModel> caseRaiseMappings = adminApproveService.getCaseChannelRecordMap(allCaseIds);

        for (Integer caseId : allCaseIds) {
            InitialRepeatCaseView caseView = new InitialRepeatCaseView();
            caseView.setCaseId(caseId);

            fillCrowdfundingInfo(infoMappings.get(caseId), caseView);
            fillCfInfoExt(infoExtMappings.get(caseId), caseView);
            fillFirstMaterial(firstMaterialMappings.get(caseId), caseView);
            fillChannel(caseRaiseMappings.get(caseId), caseView);
            fillCaseRiskLevel(caseId, caseView);
            fillCreditVO(caseId, caseView);
            result.add(caseView);
        }
        return result;
    }

    private void fillCrowdfundingInfo(CrowdfundingInfo info, InitialRepeatCaseView caseView) {
        if (info == null) {
            return ;
        }

        caseView.setCaseId(info.getId());
        caseView.setInfoUuid(info.getInfoId());
        caseView.setTitle(info.getTitle());
        caseView.setContent(info.getContent());
        caseView.setCaseCreateTime(info.getCreateTime() != null ? info.getCreateTime().getTime() : 0);

        caseView.setAmount(info.getAmount() / 100);
        caseView.setTargetAmount(info.getTargetAmount() / 100);

        caseView.setMaterialReviewStatus(info.getStatus().value());
    }

    private void fillCfInfoExt(CfInfoExt infoExt, InitialRepeatCaseView caseView) {

        if (infoExt == null) {
            return ;
        }

        caseView.setFinishStatus(infoExt.getFinishStatus());
        caseView.setInitialStatus(FirstApproveStatusEnum.parse(infoExt.getFirstApproveStatus()).getCode());

        if (caseView.getFinishStatus() > 0) {
            CaseEndRecordDO caseEndRecordDO = caseEndClient.getLastRecordByCaseId(caseView.getCaseId());
            String reason = Optional.ofNullable(caseEndRecordDO)
                    .map(CaseEndRecordDO::getDescription)
                    .orElse("");
            caseView.setFinishReason(reason);

        }
    }

    private void fillFirstMaterial(CfFirsApproveMaterial material, InitialRepeatCaseView caseView) {

        if (material == null) {
            return;
        }

        caseView.setSelfRealName(material.getSelfRealName());
        caseView.setSelfIdcard(firstApproveBiz.getIdcardWithWildchar(material.getSelfCryptoIdcard()));

        UserInfoModel model = userInfoServiceBiz.getUserInfoByUserId(material.getUserId());
        if (model != null) {
            caseView.setSelfMobileMask(maskUtil.buildByEncryptPhone(model.getCryptoMobile()));
        }

        caseView.setPatientRealName(material.getPatientRealName());
        caseView.setPatientIdcard(firstApproveBiz.getIdcardWithWildchar(material.getPatientCryptoIdcard()));
        caseView.setPatientBornCard(material.getPatientBornCard());
        caseView.setPatientIdType(material.getPatientIdType());

        caseView.setUserRelationType(material.getUserRelationType());
    }

    private Map<Integer, Set<InitialRepeatCaseView.RepeatType>> getCaseRepeatClassify(int caseId, long workOrderId, long userId) {
        Map<Integer, Set<InitialRepeatCaseView.RepeatType>> repeatClassifyMapping = Maps.newHashMap();
        InitialRepeatCaseView.InitialRepeatRiskParam riskQueryParam = getRepeatRiskParam(caseId, workOrderId, userId);
        if (riskQueryParam == null) {
            log.info("不能查到用户的身份证类型 caseId:{}", caseId);
            return repeatClassifyMapping;
        }

        Response<Map<String, RiskObject>> result = analysisClient.analyze(MODEL_ID, "" + caseId, JSON.toJSONString(riskQueryParam));
        log.info("查询案例初审相关的重复情况.caseId:{} result:{}", caseId, JSON.toJSONString(result));
        if (result == null || MapUtils.isEmpty(result.getData())) {
            return repeatClassifyMapping;
        }

        for (Map.Entry<String, RiskObject> entry :  result.getData().entrySet()) {
            InitialRepeatCaseView.RepeatType repeatType = InitialRepeatCaseView.RepeatType.parseByRiskMark(entry.getKey());

            List<Integer> repeatCaseIds = getValidRepeatCaseIds(caseId, repeatType, entry);

            // 过期预审中案例 直接系统结束
            if (repeatType == InitialRepeatCaseView.RepeatType.INITIAL_AUDIT_PAST_DUE) {
                finishCaseIds(caseId, repeatCaseIds);
                continue;
            }

            for (Integer curCaseId : repeatCaseIds) {
                Set<InitialRepeatCaseView.RepeatType> repeatTypes = repeatClassifyMapping.get(curCaseId);
                if (repeatTypes == null) {
                    repeatTypes = Sets.newHashSet();
                }
                repeatTypes.add(repeatType);

                repeatClassifyMapping.put(curCaseId, repeatTypes);
            }
        }

        return repeatClassifyMapping;
    }

    private List<Integer> getValidRepeatCaseIds(int caseId, InitialRepeatCaseView.RepeatType repeatType,
                                                Map.Entry<String, RiskObject> entry) {

        List<Integer> repeatCaseIds = Lists.newArrayList();
        RiskObject riskObject = entry.getValue();
        if (repeatType == null || riskObject == null || !riskObject.isRiskFlag()
                || StringUtils.isBlank(riskObject.getRiskResult())) {
            log.info("案例没有初审重复的类型.caseId:{} riskMark:{}", caseId, JSON.toJSONString(entry));
            return repeatCaseIds;
        }

        repeatCaseIds = JSON.parseArray(riskObject.getRiskResult(), Integer.class);
        if (CollectionUtils.isEmpty(repeatCaseIds)) {
            log.error("返回空的重复的案例字段.caseId:{} repeatCaseIds:{}", caseId, repeatCaseIds);
            return repeatCaseIds;
        }

        return repeatCaseIds;
    }

    private InitialRepeatCaseView.InitialRepeatRiskParam getRepeatRiskParam(int caseId, long workOrderId, long userId) {

        CfFirsApproveMaterial currentMaterial = riskDelegate.getCfFirsApproveMaterialByInfoId(caseId);

        InitialRepeatCaseView.InitialRepeatRiskParam param = InitialRepeatCaseView.InitialRepeatRiskParam.convertFromFirstMaterial(currentMaterial);
        if (param != null) {
            param.setWorkOrderId(workOrderId);
            param.setCallUserId(userId);
            AuthRpcResponse<String> result = accountClientV1.getMisByLongUserId(userId);
            if (result != null) {
                param.setCallName(result.getResult());
            }

            param.setCallOrg(verityHistoryBiz.queryOperateDetail((int) userId));
        }

        return param;
    }

    private void fillChannel(CfUserInvitedLaunchCaseRecordModel channelModel, InitialRepeatCaseView caseView) {

        if (channelModel == null) {
            return;
        }

        caseView.setChannel(adminApproveService.getGuideUserLaunchChannel(channelModel));
    }

    private void fillCaseRiskLevel(int caseId, InitialRepeatCaseView caseView) {
        boolean lowRisk = true;
        Response<WorkOrderVO> lastWorkOrder = workOrderClient.getLastWorkOrderByTypes(caseId, Collections.singletonList(WorkOrderType.highriskshenhe.getType()));
        if (Objects.nonNull(lastWorkOrder) && lastWorkOrder.ok() && Objects.nonNull(lastWorkOrder.getData())) {
            lowRisk = false;
        }
        if (!lowRisk) {
            return;
        }
        caseView.setCaseRiskLevel(1);
    }

    private void fillCreditVO(int caseId, InitialRepeatCaseView caseView) {
        RpcResult<CfPropertyInsuranceInfoModel> resp = cfMaterialReadClient.selectCfPropertyInsuranceInfo(caseId);
        //只有接口返回空  或者 fallback的时候  才需要刷新
        if (resp == null || resp.getCode() == BaseErrorCodeEnum.FALL_BACK.getCode()) {
            throw new RuntimeException("增信信息请求失败,请刷新后重试");
        }
        caseView.setCreditInfoVO(Optional.of(resp)
                .filter(RpcResult::isSuccess)
                .map(RpcResult::getData)
                .map(CreditInfoVO::build)
                .orElse(null));

    }

    private void finishCaseIds(int baseCaseId, List<Integer> caseIds) {
        if (CollectionUtils.isEmpty(caseIds)) {
            return;
        }

        for (Integer caseId: caseIds) {
            log.info("初审系统结束案例caseId:{}", caseId);
            approveFacade.stopCase(caseId, 0, "系统主动结束", 0, "用户已重新发起，结束7天未通过预审案例", "");
        }
    }

    private List<InitialRepeatCaseView> fillInitialRepeatCaseViewList(List<InitialRepeatCaseView> initialRepeatCaseViewList) {

        List<Integer> caseIds = initialRepeatCaseViewList.stream().map(InitialRepeatCaseView::getCaseId).collect(Collectors.toList());
        //举报信息
        Map<Integer, List<CrowdfundingReport>> countCrowdfundingReportMap = countCrowdfundingReportMap(caseIds);
        //举报工单信息
        Map<Integer, WorkOrderVO> getWorkOrderHandleResultMap = getWorkOrderHandleResultMap(caseIds);
        //举报标记打款信息
        Map<Integer, Integer> getPayMethodMap = getPayMethodMap(caseIds);

        for (InitialRepeatCaseView initialRepeatCaseView : initialRepeatCaseViewList) {
            int caseId = initialRepeatCaseView.getCaseId();
            InitialRepeatCaseView.ReportInfo reportInfo = new InitialRepeatCaseView.ReportInfo();

            Integer code = getPayMethodMap.get(caseId);
            reportInfo.setPayMethod(code);

            List<CrowdfundingReport> reportList = countCrowdfundingReportMap.get(caseId);
            if (CollectionUtils.isNotEmpty(reportList)) {
                reportInfo.setReportNumber(reportList.size());
            }

            WorkOrderVO workOrderVO = getWorkOrderHandleResultMap.get(caseId);
            if (Objects.nonNull(workOrderVO)) {
                AuthRpcResponse<String> authRpcResponse = accountClientV1.getNameByLongUserId(workOrderVO.getOperatorId());
                String userName = Optional.ofNullable(authRpcResponse).map(AuthRpcResponse::getResult).orElse(StringUtils.EMPTY);
                reportInfo.setOperatorName(userName);
                reportInfo.setHandleResult(workOrderVO.getHandleResult());
            }
            initialRepeatCaseView.setReportInfo(reportInfo);

            if(StringUtils.isNotBlank(initialRepeatCaseView.getSelfMobile())){
                initialRepeatCaseView.setSelfMobileMask(maskUtil.buildByDecryptPhone(initialRepeatCaseView.getSelfMobile()));
                initialRepeatCaseView.setSelfMobile(null);
            }
        }
        return initialRepeatCaseViewList;
    }

    private Map<Integer, WorkOrderVO> getWorkOrderHandleResultMap(List<Integer> caseIds) {
        Map<Integer, WorkOrderVO> map = Maps.newHashMap();
        for (Integer caseId : caseIds) {
            Response<WorkOrderVO> response = cfWorkOrderClient.getLastWorkOrderByTypes(caseId, WorkOrderType.REPORT_TYPES);
            WorkOrderVO workOrderVO = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(null);
            map.put(caseId, workOrderVO);
        }
        return map;
    }

    private Map<Integer, List<CrowdfundingReport>> countCrowdfundingReportMap(List<Integer> caseIds) {
        List<CrowdfundingReport> reportList = adminCrowdfundingReportBiz.getByInfoIdsV2(caseIds);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(reportList)) {
            return Maps.newHashMap();
        }
        return reportList.stream().collect(Collectors.groupingBy(CrowdfundingReport::getActivityId));
    }

    private Map<Integer, Integer> getPayMethodMap(List<Integer> caseIds) {
        Map<Integer, Integer> map = Maps.newHashMap();
        for (Integer caseId : caseIds) {
            ReportPayMethodEnum payMethodEnum = reportScheduleService.getPayMethodByCaseId(caseId);
            map.put(caseId, Optional.ofNullable(payMethodEnum).map(ReportPayMethodEnum::getCode).orElse(0));
        }
        return map;
    }

    // 保存接口
    public void saveInitialRepeatSnapshot(InitialRepeatCaseView.InitialRepeatSnapshot snapshot) {

        log.info("保存案例初审重复的快照. workOrderId:{}, caseViews:{}", snapshot.getWorkOrderId(), snapshot.getCaseView());
        commonOperationClient.create()
                .buildBasicPlatform(snapshot.getWorkOrderId(), snapshot.getUserId(), OperationActionTypeEnum.INITIAL_CASE_REPEAT_SNAPSHOT)
                .buildRemark(JSON.toJSONString(Objects.requireNonNullElse(snapshot.getCaseView(), Lists.newArrayList())))
                .save();
    }

}
