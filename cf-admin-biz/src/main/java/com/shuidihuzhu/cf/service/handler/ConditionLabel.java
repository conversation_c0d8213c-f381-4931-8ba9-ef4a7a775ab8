package com.shuidihuzhu.cf.service.handler;

import com.alibaba.excel.event.Order;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.model.event.CailiaoConditionEvent;
import com.shuidihuzhu.cf.model.event.CailiaoConditionResult;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderLevel;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2020/3/19
 */
public interface ConditionLabel {

    void onApplicationEvent(CailiaoConditionEvent event);

    int getConditionCode();

    default int getOrderType(WorkOrderType workOrderType,int old){

        if (old == 0){
            return workOrderType.getType();
        }
        if (old == WorkOrderType.cailiao_4.getType()){
            return old;
        }

        if (old == WorkOrderType.cailiao_5.getType() && workOrderType.equals(WorkOrderType.cailiao_4)){
           return workOrderType.getType();
        }

        return old;
    }

    default void setResults(String msg,CailiaoConditionEvent event){

        List<CailiaoConditionResult> list = event.getResults();
        if (CollectionUtils.isEmpty(list)){
            list = Lists.newArrayList();
        }
        CailiaoConditionResult r = new CailiaoConditionResult();
        r.setCode(getConditionCode());
        r.setMsg(msg);
        list.add(r);

        event.setOrderType(getOrderType(WorkOrderType.cailiao_4,event.getOrderType()));
        event.setResults(list);
    }

    /**
     * 如果是重服务工单 并且目标优先级更高则更新优先级
     * @param event
     * @param targetLevel
     */
    default void updateLevelIfHigherAndCaiLiao4(CailiaoConditionEvent event, OrderLevel targetLevel){
        int orderType = event.getOrderType();
        if (orderType != WorkOrderType.cailiao_4.getType()) {
            return;
        }
        OrderLevel orderLevel = OrderLevel.getFromType(orderType);
        if (orderLevel == null || OrderLevel.isHigher(orderLevel, targetLevel)){
            event.setOrderLevel(targetLevel.getType());
        }
    }
}
