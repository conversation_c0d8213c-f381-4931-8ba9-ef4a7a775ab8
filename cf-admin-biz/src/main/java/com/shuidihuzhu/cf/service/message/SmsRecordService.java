package com.shuidihuzhu.cf.service.message;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingDetailSendMsgTemplateBiz;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonActionConst;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonRecord;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonRecordClient;
import com.shuidihuzhu.cf.vo.message.SmsRecord;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.msg.model.SmsTemplate;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class SmsRecordService {

    @Autowired
    private WonRecordClient wonRecordClient;

    @Autowired
    private AdminCrowdfundingDetailSendMsgTemplateBiz adminCrowdfundingDetailSendMsgTemplateBiz;

    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    public OperationResult<WonRecord> save(int caseId, int operatorId, String mobile,
                                           @Nullable String modelNum,
                                           @Nullable String smsContent) {
        String modelName = null;

        String cryptoMobile = oldShuidiCipher.aesEncrypt(mobile);
        SmsRecord v = new SmsRecord();
        v.setModelName(modelName);
        v.setCryptoMobile(cryptoMobile);
        v.setSmsContent(smsContent);
        v.setModelNum(modelNum);

        // 补足参数
        v = promoteModel(v);

        return wonRecordClient.create()
                .buildCaseId(caseId)
                .buildBizId(cryptoMobile)
                .buildOperatorId(operatorId)
                .buildActionId(WonActionConst.SMS_RECORD)
                .buildExtValue("data", v)
                .save();
    }

    private SmsRecord promoteModel(SmsRecord v) {
        String modelNum = v.getModelNum();
        if (StringUtils.isBlank(modelNum)) {
            return v;
        }

        List<SmsTemplate> templates = adminCrowdfundingDetailSendMsgTemplateBiz.getTemplateByModelNum(modelNum);
        if (CollectionUtils.isEmpty(templates) || CollectionUtils.size(templates) > 1) {
            return v;
        }
        SmsTemplate smsTemplate = templates.get(0);

        String modelName = v.getModelName();
        if (StringUtils.isBlank(modelName)) {
            v.setModelName(smsTemplate.getTitle());
        }
        String smsContent = v.getSmsContent();
        if (StringUtils.isBlank(smsContent)) {
            v.setSmsContent(smsTemplate.getText());
        }
        return v;
    }

}
