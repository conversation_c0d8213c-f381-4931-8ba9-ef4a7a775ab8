package com.shuidihuzhu.cf.service.credit.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.feign.CfMaterialReadClient;
import com.shuidihuzhu.cf.client.material.feign.CfRaiseMaterialClient;
import com.shuidihuzhu.cf.client.material.feign.PreposeMaterialClient;
import com.shuidihuzhu.cf.client.material.model.CfInsuranceBasicLivingVo;
import com.shuidihuzhu.cf.client.material.model.CfPropertyInsuranceInfoModel;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.domain.label.risk.RiskLabelMarkRecord;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.credit.CfIncreaseCreditService;
import com.shuidihuzhu.cf.service.label.risk.RiskLabelService;
import com.shuidihuzhu.cf.vo.approve.CreditInfoNewVO;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.client.cf.admin.model.CfCreditInfo;
import com.shuidihuzhu.client.cf.admin.model.CfUserIdentity;
import com.shuidihuzhu.client.cf.search.client.CfSearchClient;
import com.shuidihuzhu.client.cf.search.model.SearchRpcResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/3/14 5:49 PM
 */
@Slf4j
@Service
public class CfIncreaseCreditServiceImpl implements CfIncreaseCreditService {
    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;

    @Resource
    private CfRaiseMaterialClient cfRaiseMaterialClient;

    @Resource
    private RiskLabelService riskLabelService;

    @Resource
    private CfSearchClient cfSearchClient;

    @Override
    public CfCreditInfo queryCreditInfo(CfUserIdentity cfUserIdentity) {

        // 先根据收款人手机号匹配最新结束案例
        Integer caseId = matchCaseIdByPayeeMobile(cfUserIdentity.getPayeeMobile());
        if (Objects.nonNull(caseId)) {
            log.info("queryCreditInfo matchCaseIdByPayeeMobile caseId:{}", caseId);
            return queryCreditInfo(caseId);
        }

        // 匹配不到再根据unionId匹配最新结束案例
        caseId = matchCaseIdByUnionId(cfUserIdentity.getRaiseUnionId());
        if (Objects.nonNull(caseId)) {
            log.info("queryCreditInfo matchCaseIdByUnionId caseId:{}", caseId);
            return queryCreditInfo(caseId);
        }

        return null;
    }

    private Integer matchCaseIdByPayeeMobile(String mobile) {

        if (StringUtils.isEmpty(mobile)) {
            return null;
        }

        // 查 ES
        SearchRpcResult<List<Integer>> result = cfSearchClient.crowdfundingInfoIndexByMobileSearch(mobile);
        if (result == null || result.getCode() != 0 || CollectionUtils.isEmpty(result.getData())) {
            return null;
        }

        return filterNewEndCase(result.getData());
    }

    private Integer matchCaseIdByUnionId(String unionId) {

        if (StringUtils.isEmpty(unionId)) {
            return null;
        }

        UserInfoModel userInfoModel = userInfoServiceBiz.getByUnionId(unionId);
        if (Objects.isNull(userInfoModel) || userInfoModel.getUserId() == 0L) {
            return null;
        }

        List<CrowdfundingInfo> crowdfundingInfos = adminCrowdfundingInfoBiz.selectByUserId(userInfoModel.getUserId());
        if (CollectionUtils.isEmpty(crowdfundingInfos)) {
            return null;
        }

        List<Integer> caseIds = crowdfundingInfos.stream().map(CrowdfundingInfo::getId).collect(Collectors.toList());
        return filterNewEndCase(caseIds);
    }

    public CfCreditInfo queryCreditInfo(Integer caseId) {

        // 查材料表增信信息
        RpcResult<CfInsuranceBasicLivingVo> rpcResult = cfRaiseMaterialClient.selectInsuranceBaseLiving(caseId);
        CfInsuranceBasicLivingVo insuranceBasicLivingVo = Optional.ofNullable(rpcResult)
                .filter(RpcResult::isSuccess)
                .map(RpcResult::getData)
                .orElse(null);
        if (Objects.isNull(insuranceBasicLivingVo)) {
            return null;
        }

        CfCreditInfo cfCreditInfo = CfCreditInfo.build(insuranceBasicLivingVo);
        if (Objects.isNull(cfCreditInfo)) {
            return null;
        }
        cfCreditInfo.setCaseId(caseId);

        // 补充风险标签
        fillRiskTag(cfCreditInfo);

        log.info("queryCreditInfo cfCreditInfo {}", JSONObject.toJSONString(cfCreditInfo));
        return cfCreditInfo;
    }

    private void fillRiskTag(CfCreditInfo cfCreditInfo) {
        List<String> tags = riskLabelService.getRiskPrimaryLabels(cfCreditInfo.getCaseId(), 1);
        if (CollectionUtils.isEmpty(tags) || tags.size() == 0) {
            tags = riskLabelService.getRiskPrimaryLabels(cfCreditInfo.getCaseId(), 0);
        }

        // 去重
        tags = tags.stream().distinct().collect(Collectors.toList());

        cfCreditInfo.setRiskLabels(tags);
    }

    private Integer filterNewEndCase(List<Integer> caseIds) {

        List<CrowdfundingInfo> crowdfundingInfos = adminCrowdfundingInfoBiz.getListByIds(caseIds);
        if (CollectionUtils.isEmpty(crowdfundingInfos)) {
            return null;
        }

        long nowTime = DateUtil.getCurrentTimestamp().getTime();
        crowdfundingInfos = crowdfundingInfos.stream()
                .filter(f -> Objects.nonNull(f.getEndTime()))
                .filter(f -> f.getEndTime().getTime() < nowTime)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(crowdfundingInfos)) {
            return null;
        }

        crowdfundingInfos.sort((o1, o2) -> {
            return Long.compare(o2.getEndTime().getTime(), o1.getEndTime().getTime());
        });

        return crowdfundingInfos.get(0).getId();
    }
}
