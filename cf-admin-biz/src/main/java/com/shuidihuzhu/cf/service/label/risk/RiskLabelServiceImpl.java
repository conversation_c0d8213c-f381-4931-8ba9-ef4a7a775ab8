package com.shuidihuzhu.cf.service.label.risk;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonRecord;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonRecordClient;
import com.shuidihuzhu.cf.dao.crowdfunding.CfRefuseReasonEntityRiskLabelDao;
import com.shuidihuzhu.cf.dao.labels.risk.RiskLabelMarkDAO;
import com.shuidihuzhu.cf.dao.labels.risk.RiskLabelMarkRelDAO;
import com.shuidihuzhu.cf.delegate.SeaAccountDelegate;
import com.shuidihuzhu.cf.domain.label.core.LabelNodeModel;
import com.shuidihuzhu.cf.domain.label.risk.*;
import com.shuidihuzhu.cf.enums.CaseRiskTypeEnum;
import com.shuidihuzhu.cf.enums.InitialAudit.CfRefuseReasonRiskLabelRelateEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntityRiskLabel;
import com.shuidihuzhu.cf.service.label.core.service.LabelService;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCoreFeignClient;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCreateParam;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderLevel;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@RefreshScope
@Service
public class RiskLabelServiceImpl implements RiskLabelService {

    @Autowired
    private LabelService labelService;

    @Autowired
    private RiskLabelMarkDAO riskLabelMarkDAO;

    @Autowired
    private RiskLabelMarkRelDAO riskLabelMarkRelDAO;

    @Autowired
    private SeaAccountDelegate seaAccountDelegate;

    @Resource
    private CfRefuseReasonEntityRiskLabelDao cfRefuseReasonEntityRiskLabelDao;

    @Autowired
    private WonRecordClient wonRecordClient;

    @Resource
    private WorkOrderCoreFeignClient workOrderCoreFeignClient;

    @Value("${audit.mark.risk.label.work.order.switch:1,2,3}")
    private String auditMarkWorkOrderSwitch;

    @Value("${audit.mark.risk.label.work-order.human.mark:1,3,14}")
    private String auditMarkWorkOrderHumanMark;

    private static final long MARK_FIX_RECORD_ACTION_ID = 114L;

    @Override
    public Response<List<Label>> getLabelByUuidList(List<String> uuidList) {
        return NewResponseUtil.makeSuccess(labelService.getLabelByUuidList(uuidList));
    }

    @Override
    public Response<LabelNodeModel> getLabelNodeById(long id) {
        final LabelNodeModel view = labelService.getEnableLabelNodeById(id);
        return NewResponseUtil.makeSuccess(view);
    }

    @Override
    public Response<List<RiskLabelMarkRecord>> getMarkRecord(int caseId, int markType) {
        List<RiskLabelMarkRecordDO> recordList = riskLabelMarkDAO.getAllByCaseIdAndMarkType(caseId, markType);
        List<RiskLabelMarkRecord> views = getRiskLabelMarkRecords(recordList);
        return NewResponseUtil.makeSuccess(views);
    }

    @Override
    public List<String> getRiskPrimaryLabels(int caseId, int markType) {
        List<RiskLabelMarkRecordDO> recordList = riskLabelMarkDAO.getAllByCaseIdAndMarkType(caseId, markType);
        return getRiskPrimaryLabelNames(recordList);
    }

    private List<String> getRiskPrimaryLabelNames(List<RiskLabelMarkRecordDO> recordList) {
        List<String> riskLabels = Lists.newArrayList();
        Map<Long, Label> labelMap = Maps.newHashMap();
        for (RiskLabelMarkRecordDO record : recordList) {
            List<RiskLabelMarkRecordRelDO> relList = riskLabelMarkRelDAO.getAllByRecordId(record.getId());
            for (RiskLabelMarkRecordRelDO rel : relList) {
                Label label = labelMap.get(rel.getLabelId());
                if (label == null) {
                    label = labelService.getLabelById(rel.getLabelId());
                    labelMap.put(label.getId(), label);
                }

                // 获取一级标签
                String labelTag = getPrimaryLabel(label);
                riskLabels.add(labelTag);
            }
        }
        return riskLabels;
    }

    private String getPrimaryLabel(Label labelTag) {
        Label parentLabel = labelService.getLabelById(labelTag.getParentId());
        if (parentLabel.getParentId() == 0) {
            return labelTag.getName();
        }
        return getPrimaryLabel(parentLabel);
    }

    @NotNull
    private List<RiskLabelMarkRecord> getRiskLabelMarkRecords(List<RiskLabelMarkRecordDO> recordList) {
        List<RiskLabelMarkRecord> views = Lists.newArrayList();
        Map<Long, Label> labelMap = Maps.newHashMap();
        for (RiskLabelMarkRecordDO record : recordList) {
            final RiskLabelMarkRecord view = new RiskLabelMarkRecord();
            List<RiskLabelMarkRecordRelDO> relList = riskLabelMarkRelDAO.getAllByRecordId(record.getId());
            List<RiskLabelMarkRecordRel> relVOList = Lists.newArrayList();
            for (RiskLabelMarkRecordRelDO rel : relList) {
                final RiskLabelMarkRecordRel relVO = new RiskLabelMarkRecordRel();
                Label label = labelMap.get(rel.getLabelId());
                if (label == null) {
                    label = labelService.getLabelById(rel.getLabelId());
                    labelMap.put(label.getId(), label);
                }
                relVO.setLabelId(label.getId());
                relVO.setLabel(label);
                relVO.setDescription(rel.getDescription());
                relVO.setRiskCatchStatus(rel.getRiskCatchStatus());
                relVO.setLabelMarkRecordId(record.getId());
                relVOList.add(relVO);
            }
            view.setRecord(record);
            view.setLabelRelList(relVOList);
            views.add(view);
        }
        return views;
    }

    @Override
    public boolean relatedRefuseEntity(long id) {
        return CollectionUtils.isNotEmpty(cfRefuseReasonEntityRiskLabelDao.selectByRiskLabelId(id));
    }

    @Override
    public Response<RiskLabelMarkRecord> getLastMarkFixRecord(int caseId) {
        RiskLabelMarkRecordDO record = riskLabelMarkDAO.getLastByCaseIdAndMarkType(caseId,
                RiskLabelConst.RiskMarkType.FIX.getValue());
        if (record == null) {
            return NewResponseUtil.makeSuccess();
        }
        List<RiskLabelMarkRecord> views = getRiskLabelMarkRecords(Lists.newArrayList(record));
        return NewResponseUtil.makeSuccess(views.get(0));
    }

    @Override
    public Response<List<WonRecord>> getMarkFixOperateRecordByCaseId(int caseId) {
        final OperationResult<List<WonRecord>> resp = wonRecordClient.listByBizId(caseId, MARK_FIX_RECORD_ACTION_ID);
        return NewResponseUtil.makeSuccess(resp.getData());
    }

    private void saveOperateRecord(long id, long operatorId, String comment){
        wonRecordClient.create()
                .buildBasic(id, MARK_FIX_RECORD_ACTION_ID)
                .buildRemark(comment)
                .buildOperatorId((int) operatorId)
                .save();
    }

    @Override
    public List<LabelNodeModel> getSimpleLabelNodeByIdList(List<Long> labelIdList) {
        return labelService.getSimpleEnableLabelNodeByIdList(labelIdList);
    }

    @Override
    public List<Label> getLabelByIds(List<Long> ids) {
        return labelService.getLabelByIds(ids);
    }

    @Override
    public boolean mark(RiskLabelMarkParam param) {
        final RiskLabelMarkRecordDO markData = param.getMarkData();
        final String nameWithOrg = seaAccountDelegate.getNameWithOrgByLongUserId(markData.getOperatorId());
        markData.setOperatorOrgName(nameWithOrg);
        markData.setMarkDesc(StringUtils.defaultString(markData.getMarkDesc()));
        int res = riskLabelMarkDAO.add(markData);
        if (res <= 0) {
            return false;
        }

        final List<RiskLabelMarkRecordRelDO> labelRelList = param.getLabelRelList();
        if (CollectionUtils.isEmpty(labelRelList)) {
            return true;
        }
        for (RiskLabelMarkRecordRelDO rel : labelRelList) {
            rel.setLabelMarkRecordId(markData.getId());
            rel.setDescription(StringUtils.defaultString(rel.getDescription()));
        }
        int r = riskLabelMarkRelDAO.addList(labelRelList);
        if (r <= 0) {
            return false;
        }
        return true;
    }

    @Override
    public boolean judgeNewRiskLabel(Integer caseId) {

        RiskLabelMarkRecordDO riskLabelMarkRecordDO = riskLabelMarkDAO.getNewByCaseId(caseId);
        if (Objects.isNull(riskLabelMarkRecordDO)) {
            return false;
        }

        return riskLabelMarkRecordDO.getCaseRiskType() == CaseRiskTypeEnum.NORM_DISEASE_CONDITION.getCode();
    }

    @Override
    public void auditMark(RiskLabelAuditMarkParam auditMarkParam) {
        log.info("RiskLabelService auditMark {} ", auditMarkParam);
        if (StringUtils.isEmpty(auditMarkWorkOrderSwitch)) {
            return;
        }
        List<String> list = Splitter.on(",").splitToList(auditMarkWorkOrderSwitch);
        boolean workOrderSwitch = list.contains(String.valueOf(auditMarkParam.getScene()));
        if (workOrderSwitch) {
            return;
        }

        List<Integer> rejectIdList = CollectionUtils.isNotEmpty(auditMarkParam.getRejectIdList()) ? auditMarkParam.getRejectIdList() : Collections.singletonList(0);
        List<CfRefuseReasonEntityRiskLabel> reasonEntityRiskLabelList = cfRefuseReasonEntityRiskLabelDao.selectByRefuseEntityIdList(rejectIdList);
        List<Long> humanMark = reasonEntityRiskLabelList.stream()
                .filter(f -> f.getRiskLabelRelated() == CfRefuseReasonRiskLabelRelateEnum.HUMAN.getType())
                .map(CfRefuseReasonEntityRiskLabel::getRiskLabelId)
                .collect(Collectors.toList());
        List<CfRefuseReasonEntityRiskLabel> autoMark = reasonEntityRiskLabelList.stream()
                .filter(f -> f.getRiskLabelRelated() == CfRefuseReasonRiskLabelRelateEnum.AUTO.getType())
                .collect(Collectors.toList());
        List<String> humanMarkScene = Splitter.on(",").splitToList(auditMarkWorkOrderHumanMark);
        if (CollectionUtils.isNotEmpty(humanMark) || humanMarkScene.contains(String.valueOf(auditMarkParam.getScene()))) {
            // 生成工单
            WorkOrderCreateParam workOrderCreateParam = new WorkOrderCreateParam();
            workOrderCreateParam.setOrderType(WorkOrderType.risk_label_mark.getType());
            workOrderCreateParam.setCaseId(auditMarkParam.getCaseId());
            workOrderCreateParam.setOrderlevel(OrderLevel.D.getType());
            workOrderCreateParam.addExt(OrderExtName.riskLabelMarkWorkOrderScene, auditMarkParam.getScene());
            workOrderCreateParam.addExt(OrderExtName.riskLabelMarkWorkOrderAuditOrderId, auditMarkParam.getOrderId());
            workOrderCreateParam.addExt(OrderExtName.riskLabelMarkWorkOrderRiskLabelId, CollectionUtils.isNotEmpty(humanMark) ? Joiner.on(",").join(humanMark) : "");
            workOrderCoreFeignClient.create(workOrderCreateParam);
        }
        if (CollectionUtils.isNotEmpty(autoMark)) {
            // 自动标记
            RiskLabelMarkParam param = new RiskLabelMarkParam();
            RiskLabelMarkRecordDO riskLabelMarkRecordDO = new RiskLabelMarkRecordDO();
            riskLabelMarkRecordDO.setCaseId(auditMarkParam.getCaseId());
            riskLabelMarkRecordDO.setScene(auditMarkParam.getScene());
            riskLabelMarkRecordDO.setOrderId(auditMarkParam.getOrderId());
            riskLabelMarkRecordDO.setMarkDesc("");
            riskLabelMarkRecordDO.setOperatorId(auditMarkParam.getOperatorId());
            riskLabelMarkRecordDO.setOperatorOrgName(auditMarkParam.getOperatorOrgName());
            List<RiskLabelMarkRecordRelDO> labelRelList = new ArrayList<>();
            autoMark.forEach(f -> {
                RiskLabelMarkRecordRelDO recordRelDO = new RiskLabelMarkRecordRelDO();
                recordRelDO.setLabelId(f.getRiskLabelId());
                labelRelList.add(recordRelDO);
            });
            param.setMarkData(riskLabelMarkRecordDO);
            param.setLabelRelList(labelRelList);
            mark(param);
        }

    }

}
