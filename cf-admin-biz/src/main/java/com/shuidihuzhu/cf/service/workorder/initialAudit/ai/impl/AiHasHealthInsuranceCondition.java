package com.shuidihuzhu.cf.service.workorder.initialAudit.ai.impl;

import com.shuidihuzhu.cf.client.material.model.CfPropertyInsuranceInfoModel;
import com.shuidihuzhu.cf.model.crowdfunding.ai.AiConditionEnum;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditSearchService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.ai.AiCondition;
import com.shuidihuzhu.cf.vo.approve.CreditInfoVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @DATE 2020/12/21
 */
@Service("aiHasHealthInsuranceCondition")
public class AiHasHealthInsuranceCondition implements AiCondition {

    @Autowired
    private InitialAuditSearchService initialAuditSearchService;


    @Override
    public boolean check(int caseId, String inputValue) {

        if (StringUtils.isEmpty(inputValue) || inputValue.equals(AiConditionEnum.wumiaoshu.getCode()+"")){
            return false;
        }

        CreditInfoVO creditInfoVO = initialAuditSearchService.getCreditInfoVO(caseId);
        //0 没有 1有
        Integer c = creditInfoVO.getLifeInsurance();
        if (Objects.isNull(c)) {
            return false;
        }

        if (inputValue.equals(AiConditionEnum.fou.getCode()+"")){
            return !c.equals(Integer.valueOf(0));
        }

        if (inputValue.equals(AiConditionEnum.shi.getCode()+"")){
            return !c.equals(Integer.valueOf(1));
        }

        return false;
    }

}
