package com.shuidihuzhu.cf.service.workorder.initialAudit.ai.impl;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.admin.river.impl.RiverDiBaoFacadeImpl;
import com.shuidihuzhu.cf.dao.crowdfunding.CfAiMaterialsDao;
import com.shuidihuzhu.cf.model.crowdfunding.ai.AiConditionEnum;
import com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterials;
import com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterialsResult;
import com.shuidihuzhu.cf.model.crowdfunding.ai.LayOutField;
import com.shuidihuzhu.cf.service.workorder.initialAudit.ai.AiRuleJudgeService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditOperateService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.ai.AiCondition;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditCaseDetail;
import com.shuidihuzhu.cf.vo.approve.InitialAuditAdditionInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2020/12/21
 */
@Slf4j
@Service("aiHasLivingAllowance")
public class AiHasLivingAllowanceCondition implements AiCondition {

    @Autowired
    private RiverDiBaoFacadeImpl riverDiBaoFacade;

    @Autowired
    private CfAiMaterialsDao cfAiMaterialsDao;

    @Override
    public boolean check(int caseId, CfAiMaterialsResult result) {
        // 用户是否选择低保
        InitialAuditAdditionInfoVO initialAuditAdditionInfoVO = riverDiBaoFacade.getInfo(caseId);
        Integer livingAllowance = Optional.ofNullable(initialAuditAdditionInfoVO)
                .map(InitialAuditAdditionInfoVO::getDiBaoAndPinKunInfo)
                .map(InitialAuditCaseDetail.CfBasicLivingGuardView::getLivingAllowance)
                .orElse(0);

        CfAiMaterials cfAiMaterials = cfAiMaterialsDao.getByCaseId(caseId, CfAiMaterials.zType);
        CfAiMaterials cfAiMaterialsImage = cfAiMaterialsDao.getByCaseId(caseId, CfAiMaterials.tType);
        List<LayOutField> layOutFields = Optional.ofNullable(cfAiMaterials)
                .map(CfAiMaterials::getFields)
                .orElse(Lists.newArrayList());
        List<LayOutField> layOutFieldImageList = Optional.ofNullable(cfAiMaterialsImage)
                .map(CfAiMaterials::getFields)
                .orElse(Lists.newArrayList());
        if (CollectionUtils.isEmpty(layOutFields) && CollectionUtils.isEmpty(layOutFieldImageList)) {
            return false;
        }
        layOutFields.addAll(layOutFieldImageList);
        log.info("aiHasLivingAllowanceCondition engine start {}, {}, {}", caseId, result, layOutFields);
        if (livingAllowance == 0){
            livingAllowanceNo(layOutFields, result);
        } else {
            String hasLivingAllowanceInputFieldValue = layOutFields
                    .stream()
                    .filter(r -> StringUtils.equals(r.getFieldKey(), "hasLivingAllowance"))
                    .map(LayOutField::getFieldValue)
                    .findFirst()
                    .orElse("");
            String hasLivingAllowanceImageInputFieldValue = layOutFields
                    .stream()
                    .filter(r -> StringUtils.equals(r.getFieldKey(), "hasLivingAllowanceImage"))
                    .map(LayOutField::getFieldValue)
                    .findFirst()
                    .orElse("");
            hasLivingAllowanceImageNoHandle(hasLivingAllowanceInputFieldValue, hasLivingAllowanceImageInputFieldValue, result);
            hasLivingAllowanceImageYesHandle(hasLivingAllowanceInputFieldValue, hasLivingAllowanceImageInputFieldValue, layOutFields, result);
        }
        log.info("aiHasLivingAllowanceCondition engine end {}, {}", caseId, result);
        return false;
    }

    private String convertRejectResult(String rejectResult, boolean rejectBaseInfo, List<String> rejectItems) {
        List<String> list = Splitter.on(",").splitToList(rejectResult);
        List<String> collect = list.stream()
                .filter(f -> !StringUtils.equals(f, String.valueOf(InitialAuditOperateService.DI_BAO_TAG)))
                .collect(Collectors.toList());
        if (rejectBaseInfo) {
            collect = collect.stream()
                    .filter(i -> !StringUtils.equals(i, String.valueOf(InitialAuditOperateService.BASE_INFO_TAG)))
                    .collect(Collectors.toList());
        }
        collect.addAll(rejectItems);
        return Joiner.on(",").join(collect);

    }

    private String convertRejectField(String rejectField, List<String> rejectItems) {
        List<String> list = Splitter.on(",").splitToList(rejectField);
        rejectItems.addAll(list);
        return Joiner.on(",").join(rejectItems);

    }

    private boolean smartIsPassNoContainDiBaoPinKun(String rejectResult) {
        List<String> list = Splitter.on(",").splitToList(rejectResult);
        return CollectionUtils.isNotEmpty(list) && AiRuleJudgeService.passIds
                .stream()
                .filter(f -> !Objects.equals(f, InitialAuditOperateService.PIN_KUN_TAG) && !Objects.equals(f, InitialAuditOperateService.DI_BAO_TAG))
                .allMatch(f -> list.contains(f.toString()));
    }

    /**
     * 用户选择：否 & 文章工单低保选择：是；驳回：325，352
     *
     * @param layOutFields 字段列表
     * @param result 驳回结果
     */
    private void livingAllowanceNo(List<LayOutField> layOutFields, CfAiMaterialsResult result) {
        String inputFieldValue = layOutFields
                .stream()
                .filter(r -> StringUtils.equals(r.getFieldKey(), "hasLivingAllowance"))
                .map(LayOutField::getFieldValue)
                .findFirst()
                .orElse("");
        if (StringUtils.equals(String.valueOf(AiConditionEnum.shi.getCode()), inputFieldValue)) {
            result.setRejectResult(convertRejectResult(result.getRejectResult(), true, Lists.newArrayList("325", "352")));
            result.setRejectField(convertRejectField(result.getRejectField(), Lists.newArrayList("hasLivingAllowance")));
        }
    }

    /**
     * 前提-用户选择：是 & 图片工单低保材料选择：否：
     *      文章工单低保选择：是 || 无描述；驳回：353
     *      文章工单低保选择：否；驳回：325，352
     *
     * @param hasLivingAllowanceInputFieldValue 文章工单低保选择
     * @param hasLivingAllowanceImageInputFieldValue 图片是否有低保材料
     * @param result 驳回结果
     */
    private void hasLivingAllowanceImageNoHandle(String hasLivingAllowanceInputFieldValue, String hasLivingAllowanceImageInputFieldValue, CfAiMaterialsResult result) {
        if (!StringUtils.equals(String.valueOf(AiConditionEnum.fou.getCode()), hasLivingAllowanceImageInputFieldValue)) {
            return;
        }
        if (StringUtils.equals(String.valueOf(AiConditionEnum.shi.getCode()), hasLivingAllowanceInputFieldValue) || StringUtils.equals(String.valueOf(AiConditionEnum.wumiaoshu.getCode()), hasLivingAllowanceInputFieldValue)) {
            result.setRejectResult(convertRejectResult(result.getRejectResult(), false, Lists.newArrayList( "353")));
            result.setRejectField(convertRejectField(result.getRejectField(), Lists.newArrayList("hasLivingAllowanceImage")));
            return;
        }

        if (StringUtils.equals(String.valueOf(AiConditionEnum.fou.getCode()), hasLivingAllowanceInputFieldValue)) {
            boolean pass = smartIsPassNoContainDiBaoPinKun(result.getRejectResult());
            if (pass) {
                result.setRejectResult(convertRejectResult(result.getRejectResult(), false, Lists.newArrayList( "353")));
            } else {
                result.setRejectResult(convertRejectResult(result.getRejectResult(), true, Lists.newArrayList("325", "352")));
            }
            result.setRejectField(convertRejectField(result.getRejectField(), Lists.newArrayList("hasLivingAllowanceImage")));
        }

    }

    /**
     *  前提-用户选择：是 & 图片工单低保材料选择：是：
     *
     * @param hasLivingAllowanceInputFieldValue 文章工单低保选择
     * @param hasLivingAllowanceImageInputFieldValue 图片是否有低保材料
     * @param layOutFields 所有字段
     * @param result 驳回结果
     */
    private void hasLivingAllowanceImageYesHandle(String hasLivingAllowanceInputFieldValue, String hasLivingAllowanceImageInputFieldValue, List<LayOutField> layOutFields, CfAiMaterialsResult result) {
        if (!StringUtils.equals(String.valueOf(AiConditionEnum.shi.getCode()), hasLivingAllowanceImageInputFieldValue)) {
            return;
        }
        String hasLivingAllowanceImageName = layOutFields
                .stream()
                .filter(r -> StringUtils.equals(r.getFieldKey(), "hasLivingAllowanceImageName"))
                .map(LayOutField::getFieldValue)
                .findFirst()
                .orElse("");
        if (StringUtils.equals(String.valueOf(AiConditionEnum.shi.getCode()), hasLivingAllowanceImageName) && StringUtils.equals(String.valueOf(AiConditionEnum.fou.getCode()), hasLivingAllowanceInputFieldValue)) {
            result.setRejectField(convertRejectField(result.getRejectField(), Lists.newArrayList("hasLivingAllowanceImage")));
            result.setRejectResult(convertRejectResult(result.getRejectResult(), true, Lists.newArrayList("325", "352")));
        }
        if (StringUtils.equals(String.valueOf(AiConditionEnum.fou.getCode()), hasLivingAllowanceImageName)) {
            if (StringUtils.equals(String.valueOf(AiConditionEnum.shi.getCode()), hasLivingAllowanceInputFieldValue)) {
                result.setRejectField(convertRejectField(result.getRejectField(), Lists.newArrayList("hasLivingAllowanceImage")));
                result.setRejectResult(convertRejectResult(result.getRejectResult(), true, Lists.newArrayList("325", "352")));
            }
            if (StringUtils.equals(String.valueOf(AiConditionEnum.wumiaoshu.getCode()), hasLivingAllowanceInputFieldValue)) {
                result.setRejectField(convertRejectField(result.getRejectField(), Lists.newArrayList("hasLivingAllowanceImage")));
                result.setRejectResult(convertRejectResult(result.getRejectResult(), false, Lists.newArrayList( "353")));
            }
            if (StringUtils.equals(String.valueOf(AiConditionEnum.fou.getCode()), hasLivingAllowanceInputFieldValue)) {
                boolean pass = smartIsPassNoContainDiBaoPinKun(result.getRejectResult());
                if (pass) {
                    result.setRejectField(convertRejectField(result.getRejectField(), Lists.newArrayList("hasLivingAllowanceImage")));
                    result.setRejectResult(convertRejectResult(result.getRejectResult(), false, Lists.newArrayList( "353")));
                } else {
                    result.setRejectField(convertRejectField(result.getRejectField(), Lists.newArrayList("hasLivingAllowanceImage")));
                    result.setRejectResult(convertRejectResult(result.getRejectResult(), true, Lists.newArrayList("325", "352")));
                }
            }
        }

    }

    @Override
    public boolean check(int caseId, String inputValue) {
        return false;
    }
}
