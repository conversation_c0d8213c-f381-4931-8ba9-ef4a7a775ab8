package com.shuidihuzhu.cf.service.workorder.read.impl.finance;

import com.shuidihuzhu.cf.enums.crowdfunding.CfDrawCashConstant;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceReadFeignClient;
import com.shuidihuzhu.cf.finance.client.model.DrawCashApply4JingXiVo;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.model.finance.drawcash.CfDrawCashApplyV2;
import com.shuidihuzhu.cf.finance.model.vo.drawcash.CfDrawCashApplyV2Vo;
import com.shuidihuzhu.cf.service.workorder.read.impl.AbstractFinanceWorkOrderCheckHandler;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 付款类型 drawCashType
 * 付款申请金额   amount (分)
 * 收款方性质    thirdType
 * 筹款结束原因 stopCfReason
 * 患者目前病情 patientConditionNow
 * 付款资金用途；useOfFunds
 * 驳回 rejectedRecords
 *
 */
@Component
public class DrawCashApplyCheckHandler extends AbstractFinanceWorkOrderCheckHandler {

    @Resource
    private CfFinanceReadFeignClient cfFinanceReadFeignClient;
    @Override
    public WorkOrderType getWorkOrderType() {
        return WorkOrderType.draw_cash_apply;
    }

    @Override
    protected Map<String, Object> toMap(Object source) {
        if (source == null) {
            return Collections.emptyMap();
        }
        CfDrawCashApplyV2Vo vo = (CfDrawCashApplyV2Vo) source;
        String traceNo = vo.getTraceNo();
        FeignResponse<DrawCashApply4JingXiVo> response =
                cfFinanceReadFeignClient.getDrawCashApplyDetail4JingXi(traceNo);
        Map<String, Object> result = super.toMap(source);
        DrawCashApply4JingXiVo responseData = null;
        if (response.ok() && response.getData() != null) {
            responseData = response.getData();
            Map<String, Object> map = super.toMap(responseData);
            result.putAll(map);
        }
        // 付款类型
        updateMap(result, "drawCashType", "payType");
        // 付款申请金额
        updateMap(result, "amount", "applyAmt");
        // 收款方性质
        updateMap(result, "thirdType", "payeeNat");
        // 筹款结束原因
        updateMap(result, "stopCfReason", "fundEndRsn");
        // 患者目前病情
        updateMap(result, "patientConditionNow", "patCurCond");
        // 付款资金用途
        updateMap(result, "useOfFunds", "payPurp");
        // 驳回记录
        StringBuilder rejectRecordStr = new StringBuilder();
        if (responseData != null) {
            List<CfDrawCashApplyV2> rejectedRecords = responseData.getRejectedRecords();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            rejectedRecords.sort((o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime()));
            for (CfDrawCashApplyV2 rejectedRecord : rejectedRecords) {
                rejectRecordStr.append("用户提交时间：");
                rejectRecordStr.append(sdf.format(rejectedRecord.getCreateTime())).append("\n");
                rejectRecordStr.append("用户提交资金用途：");
                rejectRecordStr.append(rejectedRecord.getUseOfFunds()).append("\n");
                rejectRecordStr.append("用户提交患者病情：");
                rejectRecordStr.append(rejectedRecord.getPatientConditionNow()).append("\n");
                rejectRecordStr.append("驳回原因：");
                rejectRecordStr.append(rejectedRecord.getReason()).append("\n");
                rejectRecordStr.append("驳回时间：");
                rejectRecordStr.append(sdf.format(rejectedRecord.getAuditTime())).append("\n");
                rejectRecordStr.append("操作人：");
                rejectRecordStr.append(rejectedRecord.getAuditUserName()).append("\n");
                rejectRecordStr.append("\n");
            }
        }
        result.put("rejRecInq", rejectRecordStr.toString());
        result.remove("rejectedRecords");
        return result;
    }
}
