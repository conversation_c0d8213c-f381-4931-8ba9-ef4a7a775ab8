package com.shuidihuzhu.cf.service.ai;

import com.shuidihuzhu.cf.model.datautilapi.DataAddressByIdCard;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.client.dataservice.datautil.v1.DataUtilApiClient;
import com.shuidihuzhu.client.model.Response;
import com.shuidihuzhu.msg.enums.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2022/11/22 8:19 下午
 */
@Slf4j
@Service
public class CfAddressDataQueryServiceImpl implements CfAddressDataQueryService {


    @Resource
    private DataUtilApiClient dataUtilApiClient;

    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    private static final String COUNTRY = "country";

    private static final String PROVINCE = "province";

    private static final String CITY = "city";

    private static final String COUNTY = "county";

    private static final String LON = "lon";

    private static final String LAT = "lat";
    @Override
    public DataAddressByIdCard queryAddressByIdCard(String idCard) {
        if (StringUtils.isEmpty(idCard)) {
            return null;
        }
        Response<Map<String, String>> result = dataUtilApiClient.queryAddressByIdCard(idCard);
        log.info("{} queryAddressByIdCard({}) result:{}", this.getClass().getSimpleName(), oldShuidiCipher.aesEncrypt(idCard), result);
        return dealData(result);
    }

    @Override
    public DataAddressByIdCard queryAddressByIp(String ip) {
        if (StringUtils.isEmpty(ip)) {
            return null;
        }
        Response<Map<String, String>> result = dataUtilApiClient.queryAddressByIp(ip);
        log.info("{} queryAddressByIp({}) result:{}", this.getClass().getSimpleName(), ip, result);
        return dealData(result);
    }

    @Override
    public DataAddressByIdCard queryAddressByMobile(String mobile) {
        if (StringUtils.isEmpty(mobile)) {
            return null;
        }
        Response<Map<String, String>> result = dataUtilApiClient.queryAddressByPhone(mobile);
        log.info("{} queryAddressByMobile({}) result:{}", this.getClass().getSimpleName(), oldShuidiCipher.aesEncrypt(mobile), result);
        return dealData(result);
    }

    @Override
    public DataAddressByIdCard queryAddressByCityCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        Response<Map<String, String>> result = dataUtilApiClient.queryAddressByCityCode(code);
        log.info("{} queryAddressByCityCode({}) result:{}", this.getClass().getSimpleName(), code, result);
        return dealData(result);
    }

    @Override
    public DataAddressByIdCard queryAddressByCityName(String provinceName, String cityName) {
        if (StringUtils.isEmpty(provinceName) || StringUtils.isEmpty(cityName)) {
            return null;
        }
        Response<Map<String, String>> result = dataUtilApiClient.queryAddressByName(provinceName, cityName);
        log.info("{} queryAddressByIdCard({},{}) result:{}", this.getClass().getSimpleName(), provinceName, cityName, result);
        return dealData(result);
    }

    private DataAddressByIdCard dealData(Response<Map<String, String>> data) {
        Map<String, String> resultMap = Optional.ofNullable(data)
                .filter(v -> v.getCode() == ErrorCode.SUCCESS.getCode())
                .map(Response::getData)
                .orElse(null);

        if (MapUtils.isEmpty(resultMap)) {
            return null;
        }

        return DataAddressByIdCard.builder()
                .country(resultMap.get(COUNTRY))
                .province(resultMap.get(PROVINCE))
                .city(resultMap.get(CITY))
                .county(resultMap.get(COUNTY))
                .lon(resultMap.get(LON))
                .lat(resultMap.get(LAT))
                .build();
    }
}
