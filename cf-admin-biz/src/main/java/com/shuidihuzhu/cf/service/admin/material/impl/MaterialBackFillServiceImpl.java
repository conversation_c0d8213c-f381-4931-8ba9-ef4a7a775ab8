package com.shuidihuzhu.cf.service.admin.material.impl;

import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.materialAudit.BackFillBasicInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.HospitalInfo;
import com.shuidihuzhu.cf.service.admin.material.MaterialBackFillService;
import com.shuidihuzhu.client.cf.api.client.MaterialFillBackFeignClient;
import com.shuidihuzhu.client.cf.api.model.BackFillVO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/12/8 10:36 AM
 */
@Slf4j
@Service
public class MaterialBackFillServiceImpl implements MaterialBackFillService {

    @Resource
    private MaterialFillBackFeignClient materialFillBackFeignClient;
    @Resource
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;

    @Override
    public BackFillVO backFillInfo(Integer caseId) {

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if (Objects.isNull(crowdfundingInfo)) {
            return null;
        }

        Response<BackFillVO> response = materialFillBackFeignClient.queryBackFillVO(crowdfundingInfo);
        return Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);

    }

    @Override
    public HospitalInfo selectHospital(Integer cityId, String userInput, Integer pageSize, Integer current) {
        Response<HospitalInfo> response = materialFillBackFeignClient.getHospital(cityId, userInput, pageSize, current);
        return Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);
    }

}
