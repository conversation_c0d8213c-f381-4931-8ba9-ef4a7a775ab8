package com.shuidihuzhu.cf.service.approve;

import com.shuidihuzhu.cf.vo.approve.ApproveAuditParam;
import com.shuidihuzhu.common.web.model.Response;


/**
 * @Author: wangpeng
 * @Date: 2023/3/23 15:14
 * @Description: 材审相关核心接口
 */
public interface ApproveAuditService {

    /**
     * 材审审核逻辑
     * @param approveAuditParam 各种需要的参数
     * @return 是否成功
     */
    Response<?> handleLogic(ApproveAuditParam approveAuditParam);
}
