package com.shuidihuzhu.cf.service.approve.remark;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.admin.delegate.SeaUserAccountDelegate;
import com.shuidihuzhu.cf.client.subject.caseend.CaseEndClient;
import com.shuidihuzhu.cf.domain.caseinfo.CaseEndRecordDO;
import com.shuidihuzhu.cf.enums.BackgroundLogEnum;
import com.shuidihuzhu.cf.enums.approve.ApproveCommentTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfFinishStatus;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingApprove;
import com.shuidihuzhu.cf.service.workorder.WorkOrderRemarkService;
import com.shuidihuzhu.cf.service.workorder.flow.WorkOrderFlowCommentsComparator;
import com.shuidihuzhu.cf.service.workorder.flow.WorkOrderFlowGroupCommentsComparator;
import com.shuidihuzhu.cf.vo.crowdfunding.CrowdfundingApproveCommentVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ApproveRemarkService {

    private static final WorkOrderFlowGroupCommentsComparator C = new WorkOrderFlowGroupCommentsComparator();
    private static final WorkOrderFlowCommentsComparator COMPARATOR = new WorkOrderFlowCommentsComparator();

    @Autowired
    private WorkOrderRemarkService workOrderRemarkService;

    @Autowired
    private SeaUserAccountDelegate seaUserAccountDelegate;

    @Autowired
    private ApproveRemarkOldService approveRemarkOldService;

    @Resource
    private CaseEndClient caseEndClient;

    private List<CrowdfundingApprove> listByCaseId(int caseId) {

        return listByCaseId(caseId, null);
    }


    private List<CrowdfundingApprove> listByCaseId(int caseId, ApproveCommentTypeEnum filterType) {

        List<CrowdfundingApprove> approves = Lists.newArrayList();

        if (!ApproveCommentTypeEnum.WORKORDER.equals(filterType)) {
            approves = approveRemarkOldService.listByCaseId(caseId);
        }

        List<CrowdfundingApprove> flowApproves = workOrderRemarkService.listFlowApproveByCaseId(caseId);

        // 添加用户主动筹款原因记录
        CrowdfundingApprove crowdfundingApprove = getUserStopCaseReason(caseId);
        if (Objects.nonNull(crowdfundingApprove)) {
            approves.add(crowdfundingApprove);
        }

        CollectionUtils.addAll(approves, flowApproves);
        return approves;
    }

    private CrowdfundingApprove getUserStopCaseReason(int caseId) {
        CaseEndRecordDO caseEndRecordDO = caseEndClient.getLastRecordByCaseId(caseId);
        if (Objects.isNull(caseEndRecordDO)) {
            return null;
        }
        if (!Objects.equals(caseEndRecordDO.getFinishStatus(), CfFinishStatus.FINISH_BY_RAISER.getValue())) {
            return null;
        }
        CrowdfundingApprove crowdfundingApprove = new CrowdfundingApprove();
        crowdfundingApprove.setWorkOrderId(0);
        crowdfundingApprove.setComment(StringUtils.isEmpty(caseEndRecordDO.getDescription()) ? "未填写" : "用户主动停止筹款；停止筹款原因：" + caseEndRecordDO.getDescription());
        crowdfundingApprove.setCrowdfundingId(caseEndRecordDO.getCaseId());
        crowdfundingApprove.setOprid(102);
        try {
            DateFormat formatDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date parse = formatDate.parse(caseEndRecordDO.getCreateTime());
            crowdfundingApprove.setOprtime(parse);
        } catch (ParseException e) {
            log.error("ApproveRemarkService getUserStopCaseReason parse date error {} {}", caseId, e.getMessage());
        }
        crowdfundingApprove.setOrganization(caseEndRecordDO.getOperatorName());
        return crowdfundingApprove;
    }


    public Set<String> listOrgByCaseId(int caseId) {
        return listByCaseId(caseId).stream()
                .map(CrowdfundingApprove::getOrganization)
                .collect(Collectors.toSet());
    }

    public Map<Integer, AdminUserAccountModel> listUserMapByCaseId(int caseId) {
        List<Integer> userIds = listByCaseId(caseId).stream()
                .map(CrowdfundingApprove::getOprid)
                .filter(Objects::nonNull)
                .map(Math::toIntExact)
                .distinct()
                .collect(Collectors.toList());
        return seaUserAccountDelegate.getUserByOperators(userIds);
    }

    public List<List<CrowdfundingApproveCommentVo>> listCommentByCaseIdGrouped(int caseId) {
        return listByCaseIdGrouped(caseId, null, null, null, ApproveCommentTypeEnum.COMMENT);
    }

    public List<List<CrowdfundingApproveCommentVo>> listByCaseIdGrouped(
            int caseId,
            Integer operatorId,
            String org,
            String key,
            ApproveCommentTypeEnum filterType) {
        List<CrowdfundingApprove> list = listByCaseId(caseId, filterType);

        List<CrowdfundingApproveCommentVo> vos = processList(operatorId, org, key, filterType, list);

        return grouping(vos);
    }

    @NotNull
    private List<CrowdfundingApproveCommentVo> processList(Integer operatorId, String org, String key, ApproveCommentTypeEnum filterType, List<CrowdfundingApprove> approves) {
        approves = approves.stream()
                .filter(v -> {
                    if (operatorId == null || operatorId <= 0) {
                        return true;
                    }
                    return operatorId.equals(v.getOprid());
                })
                .filter(v -> {
                    if (org == null) {
                        return true;
                    }
                    return StringUtils.equals(org, v.getOrganization());
                })
                .filter(v -> {
                    if (StringUtils.isEmpty(key)) {
                        return true;
                    }
                    return StringUtils.contains(v.getComment(), key);
                })
                .filter(v -> filterApprove(v, filterType))
                .collect(Collectors.toList());

        return approveRemarkOldService.convert2VO(approves)
                .stream()
                .peek(v -> {
                    String comment = v.getComment();
                    boolean isRemark = isRemark(comment);
                    ApproveCommentTypeEnum type = isRemark ? ApproveCommentTypeEnum.REMARK : ApproveCommentTypeEnum.COMMENT;
                    v.setTypeEnum(type);
                })
                .collect(Collectors.toList());
    }

    private boolean isRemark(String comment) {
        return StringUtils.length(comment) >= 2 && StringUtils.equals(BackgroundLogEnum.REMARK.getMessage(), comment.substring(0, 2));
    }

    private boolean filterApprove(CrowdfundingApprove v, ApproveCommentTypeEnum filterType) {
        if (filterType == null) {
            return true;
        }

        if (filterType == ApproveCommentTypeEnum.WORKORDER) {
            return true;
        }

        String comment = v.getComment();
        boolean isRemark = isRemark(comment);
        ApproveCommentTypeEnum typeEnum = isRemark ? ApproveCommentTypeEnum.REMARK : ApproveCommentTypeEnum.COMMENT;
        return filterType == typeEnum;
    }

    private List<List<CrowdfundingApproveCommentVo>> grouping(List<CrowdfundingApproveCommentVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        List<List<CrowdfundingApproveCommentVo>> result = Lists.newArrayList();

        // 普通评论
        list.stream()
                .filter(v -> v.getWorkOrderId() <= 0)
                .forEach(v -> result.add(Lists.newArrayList(v)));

        // 信息流转
        Collection<List<CrowdfundingApproveCommentVo>> values = list.stream()
                .filter(v -> v.getWorkOrderId() > 0)
                .sorted(COMPARATOR)
                .collect(Collectors.groupingBy(CrowdfundingApproveCommentVo::getWorkOrderId))
                .values();
        result.addAll(values);

        // 排序
        result.sort(C);
        return result;
    }

}
