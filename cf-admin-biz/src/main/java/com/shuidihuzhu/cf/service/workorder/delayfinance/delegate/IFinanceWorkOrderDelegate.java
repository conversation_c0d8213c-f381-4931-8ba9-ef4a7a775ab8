package com.shuidihuzhu.cf.service.workorder.delayfinance.delegate;

import com.shuidihuzhu.cf.finance.model.vo.CfChangePayeeInfoVo;
import com.shuidihuzhu.cf.finance.model.vo.CfEndCaseWorkOrderVo;
import com.shuidihuzhu.cf.finance.model.vo.CfMoneyBackQueryVo;
import com.shuidihuzhu.cf.finance.model.vo.CfPromptDrawCashRecordVo;
import com.shuidihuzhu.cf.finance.model.vo.PrecipitationModelVo;
import com.shuidihuzhu.cf.finance.model.vo.drawcash.CaseSecondRecordV2Vo;
import com.shuidihuzhu.cf.finance.model.vo.drawcash.CfDrawCashApplyV2Vo;
import com.shuidihuzhu.cf.finance.model.vo.drawcash.CfDrawCashLongTailVo;
import com.shuidihuzhu.cf.finance.model.vo.refund.CfRefundApplyVo;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-12-01 3:52 下午
 **/
public interface IFinanceWorkOrderDelegate {

    /**
     * 获取筹款人整体退款信息
     *
     * @param ids   -
     * @return  -
     */
    List<CfRefundApplyVo> getRefundApplyVo(List<Long> ids);

    /**
     * 获取返还款数据
     *
     * @param ids   -
     * @return  -
     */
    List<CfMoneyBackQueryVo> getMoneyBackVo(List<Long> ids);

    /**
     * 获取沉淀资金数据
     *
     * @param ids   -
     * @return  -
     */
    List<PrecipitationModelVo> getPrecipitationVo(List<Long> ids);

    /**
     * 获取随筹随取数据
     *
     * @param ids   -
     * @return  -
     */
    List<CfEndCaseWorkOrderVo> getEndCaseVo(List<Long> ids);

    /**
     * 获取催提现数据
     *
     * @param ids   -
     * @return  -
     */
    List<CfPromptDrawCashRecordVo> getPromptDrawCashRecordVo(List<Long> ids);

    /**
     * 获取提现审核
     *
     * @param ids -
     * @return -
     */
    List<CfDrawCashApplyV2Vo> getDrawCashApplyVo(List<Long> ids);

    /**
     * 获取长尾案例
     *
     * @param ids -
     * @return -
     */
    List<CfDrawCashLongTailVo> getDrawCashLongTailVo(List<Long> ids);

    /**
     * 获取二次收口
     *
     * @param ids -
     * @return -
     */
    List<CaseSecondRecordV2Vo> getSecondRecordVo(List<Long> ids);

    /**
     * 获取修改收款人审核
     *
     * @param ids -
     * @return -
     */
    List<CfChangePayeeInfoVo> getChangePayeeInfoVo(List<Long> ids);
}
