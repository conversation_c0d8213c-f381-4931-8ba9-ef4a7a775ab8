package com.shuidihuzhu.cf.service.workorder.initialAudit.ai.impl;

import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.service.workorder.initialAudit.ai.AiCondition;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @DATE 2020/12/18
 */
@Service("aiNameCondition")
public class AiNameCondition implements AiCondition {


    @Resource
    private IRiskDelegate riskDelegate;

    @Override
    public boolean check(int caseId,String inputValue) {

        if (StringUtils.isEmpty(inputValue)){
            return true;
        }

        CfFirsApproveMaterial material = riskDelegate.getCfFirsApproveMaterialByInfoId(caseId);

        return material != null && !material.getPatientRealName().equals(inputValue);
    }
}
