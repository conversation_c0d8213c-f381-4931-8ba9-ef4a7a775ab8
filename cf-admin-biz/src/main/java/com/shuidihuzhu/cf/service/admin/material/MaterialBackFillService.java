package com.shuidihuzhu.cf.service.admin.material;

import com.shuidihuzhu.cf.model.crowdfunding.vo.HospitalInfo;
import com.shuidihuzhu.client.cf.api.model.BackFillVO;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.RequestParam;
import com.shuidihuzhu.common.web.model.Response;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/12/8 10:36 AM
 */
public interface MaterialBackFillService {

    BackFillVO backFillInfo(Integer caseId);

    HospitalInfo selectHospital(Integer cityId, String userInput, Integer pageSize, Integer current);

}
