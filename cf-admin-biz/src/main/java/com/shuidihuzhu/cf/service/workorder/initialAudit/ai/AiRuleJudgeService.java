package com.shuidihuzhu.cf.service.workorder.initialAudit.ai;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseReasonTagBiz;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.dao.crowdfunding.CfAiMaterialsDao;
import com.shuidihuzhu.cf.dao.crowdfunding.CfRefuseReasonEntityDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonTag;
import com.shuidihuzhu.cf.model.crowdfunding.ai.*;
import com.shuidihuzhu.cf.risk.client.rpc.DiseaseClient;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyEnum;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyResultEnum;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyRequest;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyResponse;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditOperateService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2020/12/17
 */
@Slf4j
@Service
public class AiRuleJudgeService {

    //通过驳回项
    public final static List<Integer> passIds = Lists.newArrayList(InitialAuditOperateService.BASE_INFO_TAG, InitialAuditOperateService.FIRST_APPROVE_TAG, InitialAuditOperateService.CREDIT_TAG,
            InitialAuditOperateService.PIN_KUN_TAG, InitialAuditOperateService.DI_BAO_TAG);
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private CfAiMaterialsDao cfAiMaterialsDao;
    @Autowired
    private DiseaseClient diseaseClient;
    @Autowired(required = false)
    private Producer producer;
    @Autowired
    private CfRefuseReasonEntityDao cfRefuseReasonEntityDao;
    @Autowired
    private CfRefuseReasonTagBiz cfRefuseReasonTagBiz;

    public void Judge(long id1, long id2, int caseId) {
        log.info("AiRuleJudgeService Judge id1={} id2={} caseId={}", id1, id2, caseId);
        List<CfAiMaterials> list = cfAiMaterialsDao.getByWorkOrderId(id1);
        list.addAll(cfAiMaterialsDao.getByWorkOrderId(id2));

        CfAiMaterialsResult result = new CfAiMaterialsResult();
        result.setCaseId(caseId);
        result.setWorkOrderIds(id1 + "---" + id2);
        result.setLabel("-1");
        result.setStopResult("-1");
        result.setRejectResult("-1");
        result.setRejectField("-1");

        limitEnterAiEngine(list, result, caseId);
        cfAiMaterialsDao.saveResult(result);

        MessageResult messageResult = producer.send(Message.of(MQTopicCons.CF,
                MQTagCons.SECOND_REVIEW_WORK_ORDER,
                result.getId() + "", result));
        log.info("AiRuleJudgeService send message={} messageResult={}", result, messageResult);
    }

    private void limitEnterAiEngine(List<CfAiMaterials> list, CfAiMaterialsResult result, int caseId) {
        List<LayOutField> fields = list.stream()
                .flatMap(r -> r.getFields().stream())
                .collect(Collectors.toList());
        String specialReport = fields.stream()
                .filter(r -> "specialReport".equals(r.getFieldKey()))
                .map(LayOutField::getFieldValue)
                .findAny()
                .orElse("");

        // 是否特殊报备，选择”否“，根据图片录入工单中”医疗材料中的疾病名称“字段判断疾病是否可发：
        if (StringUtils.equals(specialReport, String.valueOf(AiConditionEnum.fou.getCode()))) {
            log.info("AiRuleJudgeService limitEnterAiEngine specialReport fou {} {} ", caseId, specialReport);
            String disease = fields.stream()
                    .filter(r -> "diseaseNameInMd".equals(r.getFieldKey()))
                    .map(LayOutField::getFieldValue)
                    .findAny()
                    .orElse("");
            if (stopCase(Collections.singletonList(disease), caseId)) {
                result.setStopResult("215");
            } else {
                engine(fields, result, caseId);
            }
            return;
        }
        // 是否特殊报备，选择”是“：根据文章录入工单做判断（先判断文章/标题中有没有疾病名称，有的话再继续判断是否可发）
        String titleHasDiseaseName = fields.stream()
                .filter(r -> "titleHasDiseaseName".equals(r.getFieldKey()))
                .map(LayOutField::getFieldValue)
                .findAny()
                .orElse("");
        String contentHasDiseaseName = fields.stream()
                .filter(r -> "contentHasDiseaseName".equals(r.getFieldKey()))
                .map(LayOutField::getFieldValue)
                .findAny()
                .orElse("");
        // 标题中是否有疾病名称选择”否“且文章中是否有疾病名称选择”否“：驳回339
        if (StringUtils.equals(titleHasDiseaseName, String.valueOf(AiConditionEnum.wu.getCode())) && StringUtils.equals(contentHasDiseaseName, String.valueOf(AiConditionEnum.wu.getCode()))) {
            log.info("AiRuleJudgeService limitEnterAiEngine is reject {} {}", caseId, list);
            result.setRejectResult("339");
            //驳回字段
            result.setRejectField("titleHasDiseaseName,contentHasDiseaseName");
            return;
        }
        String diseaseNameInContent = fields.stream()
                .filter(r -> "diseaseNameInContent".equals(r.getFieldKey()))
                .map(LayOutField::getFieldValue)
                .findAny()
                .orElse("");
        String diseaseNameInTitle = fields.stream()
                .filter(r -> "diseaseNameInTitle".equals(r.getFieldKey()))
                .map(LayOutField::getFieldValue)
                .findAny()
                .orElse("");
        if (stopCase(Arrays.asList(diseaseNameInContent, diseaseNameInTitle), caseId)) {
            result.setStopResult("215");
        } else {
            engine(fields, result, caseId);
        }
    }

    private boolean stopCase(List<String> diseaseList, int caseId) {

        DiseaseStrategyRequest request = new DiseaseStrategyRequest();
        request.setDiseaseNameList(diseaseList);
        request.setExecuteStrategyEnum(DiseaseStrategyEnum.MANUAL_WRITE.getCode());
        request.setCaseId(caseId);
        Response<DiseaseStrategyResponse> response = diseaseClient.diseaseStrategy(request);
        log.info("diseaseStrategy caseid={} request={} response={}", caseId, request, response);
        return Optional.ofNullable(response).filter(Response::ok).map(Response::getData)
                .map(DiseaseStrategyResponse::getDiseaseRaiseStrategyResult)
                .filter(r -> r.getResult() == DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.ALL_DISEASE_CAN_NOT_RAISE.getCode() || r.getResult() == DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.SPECIAL_DISEASE_CAN_NOT_RAISE.getCode())
                .isPresent();

    }


    private void engine(List<LayOutField> fields, CfAiMaterialsResult result, int caseId) {

        log.info("AiRuleJudgeService engine is begin {}, {}", caseId, fields);
        List<AiRules> aiRules = AiRules.getAiCheckRule();

        Map<String, LayOutField> map = fields.stream().collect(Collectors.toMap(LayOutField::getFieldKey, Function.identity(), (o1, o2) -> o2));
        StringJoiner sb = new StringJoiner(",");
        List<String> baseRejectResult = aiRules.stream()
                .filter(r -> {
                    LayOutField field = map.get(r.getParam());
                    if (field != null) {
                        if (StringUtils.isNotEmpty(r.getSpecialCheck())) {
                            boolean flag = applicationContext.getBean(r.getSpecialCheck(), AiCondition.class).check(caseId, field.getFieldValue());
                            if (flag) {
                                sb.add(field.getFieldKey());
                            }
                            return flag;
                        } else {
                            boolean flag = calculate(r.getRule(), r.getCondition(), field.getFieldValue());
                            if (flag) {
                                sb.add(field.getFieldKey());
                            }
                            return flag;
                        }
                    }
                    return false;
                })
                .map(AiRules::getValue).collect(Collectors.toList());

        List<AiRules> aiMixRules = AiRules.getAiMixCheckRule();
        List<String> mixRejectResult = aiMixRules.stream()
                .filter(r -> {
                    boolean flag;
                    if (StringUtils.isEmpty(r.getRule())) {
                        flag = applicationContext.getBean(r.getSpecialCheck(), AiCondition.class).check(caseId, r.getCondition());
                    } else {
                        flag = applicationContext.getBean(r.getSpecialCheck(), AiCondition.class).check(caseId, result);
                    }
                    if (flag && StringUtils.isNotEmpty(r.getCondition())) {
                        sb.add(r.getCondition());
                    }
                    return flag;
                }).map(AiRules::getValue).filter(StringUtils::isNotEmpty).collect(Collectors.toList());

        List<String> singleResult = AiRules.getAiSingleCheckRule()
                .stream()
                .map(f -> {
                    AiRules check = applicationContext.getBean(f.getSpecialCheck(), AiSingleCondition.class).check(caseId);
                    return check.getValue();
                })
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());

        List<String> rejectResult = Lists.newArrayList();
        baseRejectResult.addAll(mixRejectResult);
        baseRejectResult.addAll(singleResult);
        baseRejectResult.forEach(r -> {
            if (r.contains(",")) {
                rejectResult.addAll(Splitter.on(",").splitToList(r));
            } else {
                rejectResult.add(r);
            }
        });
        String reject = Joiner.on(",").join(passIds);
        log.info(" engine caseId={} rejectResult={}", caseId, rejectResult);
        if (CollectionUtils.isNotEmpty(rejectResult)) {

            List<CfRefuseReasonEntity> entities = cfRefuseReasonEntityDao.selectByReasonIds(rejectResult.stream().map(Integer::valueOf).collect(Collectors.toSet()), 0);
            List<CfRefuseReasonTag> refuseReasonTags = cfRefuseReasonTagBiz.selectByTagIds(entities.stream().map(CfRefuseReasonEntity::getTagId).distinct().collect(Collectors.toSet()));
            Set<Integer> typeSet = refuseReasonTags.stream().map(CfRefuseReasonTag::getDataType).collect(Collectors.toSet());
            List<String> pass = passIds.stream().filter(r -> !typeSet.contains(r)).map(String::valueOf).collect(Collectors.toList());

            rejectResult.addAll(pass);
            reject = Joiner.on(",").join(rejectResult);
        }
        result.setRejectResult(reject);
        //驳回字段
        result.setRejectField(sb.toString());

        applicationContext.getBean("aiHasLivingAllowance", AiCondition.class).check(caseId, result);
    }


    private boolean calculate(String rule, String p1, String p2) {

        switch (rule) {
            case "=":
                return p1.equals(p2);
            default:
                return false;
        }
    }

}
