package com.shuidihuzhu.cf.service.ai;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.ai.alps.client.common.ErnieConstants;
import com.shuidihuzhu.ai.alps.client.feign.ChatGptOverseaClient;
import com.shuidihuzhu.ai.alps.client.model.completions.v3.ChatCompletionRequestV3;
import com.shuidihuzhu.client.cf.admin.model.AIGenerateParam;
import com.shuidihuzhu.client.model.ChatChunk;
import com.shuidihuzhu.client.model.ChatCompletionChunk;
import com.shuidihuzhu.client.model.ChatStreamResult;
import com.shuidihuzhu.common.web.model.Response;
import org.springframework.http.HttpHeaders;
import org.springframework.http.client.reactive.ClientHttpConnector;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * @Description: 文心一言
 * @Author: panghairui
 * @Date: 2024/5/29 7:36 PM
 */
@Service
public class WenXinAiModel extends BaseModel {

    @Resource
    private ChatGptOverseaClient chatGptOverseaClient;

    private static final String MODEL_NAME = "ERNIE-4.0-8K";

    @Override
    protected String callModelApi(AIGenerateParam aiGenerateParam, String prompt) {
        ChatCompletionRequestV3 chatCompletionRequestV3 = new ChatCompletionRequestV3();
        chatCompletionRequestV3.setRequestBody(getRequestBody(prompt));
        chatCompletionRequestV3.setAppCode("1004");
        chatCompletionRequestV3.setExt(getExt());

        Response<JSONObject> response = chatGptOverseaClient.innerErnieBotChatCompletions(chatCompletionRequestV3);
        JSONObject responseJson = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);
        if (Objects.isNull(responseJson)) {
            return "";
        }

        return responseJson.getString("result");
    }

    @Override
    protected Flux<ChatChunk<ChatCompletionChunk>> stream(String prompt) {
        return super.streamGenerate(MODEL_NAME, prompt);
    }

    private String getExt() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("model", ErnieConstants.ERNIE_4_0_8K_TUPLE2.getT1());
        jsonObject.put("uri", ErnieConstants.ERNIE_4_0_8K_TUPLE2.getT2());
        return jsonObject.toJSONString();
    }

    private JSONObject getRequestBody(String prompt) {

        // 创建一个JSONArray来存放消息
        JSONArray messagesArray = new JSONArray();

        // 构建第一个消息对象
        JSONObject messageObject = new JSONObject();
        messageObject.put("role", "user");
        messageObject.put("content", prompt);

        // 将消息对象添加到消息数组
        messagesArray.add(messageObject);

        // 创建最外层的JSONObject，并添加消息数组
        JSONObject mainObject = new JSONObject();
        mainObject.put("messages", messagesArray);

        return mainObject;

    }
}
