package com.shuidihuzhu.cf.service.risk.repeat;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoSearchBiz;
import com.shuidihuzhu.cf.client.feign.CfFirstApproveFeignClient;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CaseSearchVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoVo;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderMQ;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.w3c.dom.stylesheets.LinkStyle;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: wangpeng
 * @Date: 2022/3/10 16:11
 * @Description:
 */
@Slf4j
@Service
public class CfRepeatCaseService {

    @Resource
    private CrowdfundingFeignClient crowdfundingFeignClient;

    @Resource
    private CfFirstApproveFeignClient firstApproveClient;

    @Resource
    private AdminCfInfoSearchBiz adminCfInfoSearchBiz;

    @Resource
    private ShuidiCipher shuidiCipher;

    @Autowired(required = false)
    private Producer producer;

    public void getRepeatCaseIdList(String infoUuid) { FeignResponse<CrowdfundingInfo> feignResponse = crowdfundingFeignClient.getCrowdfundingByuuid(infoUuid);
        CrowdfundingInfo crowdfundingInfo = Optional.ofNullable(feignResponse)
                .map(FeignResponse::getData)
                .orElse(null);
        if (Objects.isNull(crowdfundingInfo)) {
            return;
        }
        int caseId = crowdfundingInfo.getId();
        FeignResponse<CfFirsApproveMaterial> approveMaterialFeignResponse = firstApproveClient.getCfFirstApproveMaterialByCaseId(caseId);
        CfFirsApproveMaterial cfFirsApproveMaterial = Optional.ofNullable(approveMaterialFeignResponse)
                .map(FeignResponse::getData)
                .orElse(null);
        if (Objects.isNull(cfFirsApproveMaterial)) {
            return;
        }
        CaseSearchVo caseSearchVo = new CaseSearchVo();
        if (StringUtils.isNotEmpty(cfFirsApproveMaterial.getPatientCryptoIdcard())) {
            String decrypt = shuidiCipher.decrypt(cfFirsApproveMaterial.getPatientCryptoIdcard());
            caseSearchVo.setPatientIdCard(decrypt);
        }
        if (StringUtils.isEmpty(cfFirsApproveMaterial.getPatientCryptoIdcard()) && StringUtils.isNotEmpty(cfFirsApproveMaterial.getPatientBornCard())) {
            caseSearchVo.setPatientBornCard(cfFirsApproveMaterial.getPatientBornCard());
        }
        if (StringUtils.isEmpty(cfFirsApproveMaterial.getPatientCryptoIdcard()) && StringUtils.isEmpty(cfFirsApproveMaterial.getPatientBornCard()) &&
                StringUtils.isNotEmpty(cfFirsApproveMaterial.getSelfCryptoIdcard())) {
            String decrypt = shuidiCipher.decrypt(cfFirsApproveMaterial.getSelfCryptoIdcard());
            caseSearchVo.setSelfIdCard(decrypt);
        }
        Pair<Long, List<CrowdfundingInfoVo>> caseSearchByEs = adminCfInfoSearchBiz.caseSearchByEs(caseSearchVo);
        if (Objects.nonNull(caseSearchByEs) && CollectionUtils.isNotEmpty(caseSearchByEs.getRight())) {
            // 第一个案例是当前案例
            List<Integer> caseIdListRepeat = caseSearchByEs.getRight()
                    .stream()
                    .map(CrowdfundingInfoVo::getId)
                    .filter(f -> !Objects.equals(f, caseId))
                    .collect(Collectors.toList());
            caseIdListRepeat.add(caseId);
            producer.send(new Message<>(MQTopicCons.CF, CfClientMQTagCons.CF_REPEAT_CASE_PAY_ORDER, CfClientMQTagCons.CF_REPEAT_CASE_PAY_ORDER + "_" + System.currentTimeMillis(), caseIdListRepeat));
        }
    }
}
