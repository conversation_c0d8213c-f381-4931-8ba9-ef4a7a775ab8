package com.shuidihuzhu.cf.service.crowdfunding;

import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.client.cf.growthtool.client.CfGrowthtoolVolunteerCardConfigFeignClient;
import com.shuidihuzhu.client.cf.growthtool.enums.VolunteerCardEnums;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteerCardConfigDO;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;


/**
 * <AUTHOR>
 * @date 2021/10/13下午7:33
 * @desc 
 */
@Slf4j
@Service
public class CrowdfundingVolunteerCardConfigService {

    @Autowired
    private CfGrowthtoolVolunteerCardConfigFeignClient volunteerCardConfigFeignClient;

    @Autowired
    private ApproveRemarkOldService approveRemarkOldService;

    public CrowdfundingVolunteerCardConfigDO getVolunteerCardConfig(CrowdfundingVolunteerCardConfigDO param){

        return volunteerCardConfigFeignClient.getCardConfig(param).getData();
    }

    public Response<String> showVolunteerCardConfig(CrowdfundingVolunteerCardConfigDO volunteerCardConfig) {
        volunteerCardConfig.setStatus(VolunteerCardEnums.VolunteerCardStatusEnums.show.getCode());
        if (volunteerCardConfig.getConfigType() == VolunteerCardEnums.VolunteerCardTypeEnums.single_card.getCode()) {
            String content = Optional.ofNullable(volunteerCardConfig.getReasonDesc()).orElse("") + ", 申请显示卡片";
            approveRemarkOldService.add(volunteerCardConfig.getCaseId(), Optional.ofNullable(volunteerCardConfig.getOperateUserId()).orElse(0L).intValue(), content);
        }
        return volunteerCardConfigFeignClient.updateCardConfig(volunteerCardConfig);
    }

    public Response<String> hideVolunteerCardConfig(CrowdfundingVolunteerCardConfigDO volunteerCardConfig) {
        volunteerCardConfig.setStatus(VolunteerCardEnums.VolunteerCardStatusEnums.hide.getCode());
        if (volunteerCardConfig.getConfigType() == VolunteerCardEnums.VolunteerCardTypeEnums.single_card.getCode()) {
            String content = Optional.ofNullable(volunteerCardConfig.getReasonDesc()).orElse("") + ", 申请隐藏卡片";
            approveRemarkOldService.add(volunteerCardConfig.getCaseId(), Optional.ofNullable(volunteerCardConfig.getOperateUserId()).orElse(0L).intValue(), content);
        }
        return volunteerCardConfigFeignClient.updateCardConfig(volunteerCardConfig);
    }
}
