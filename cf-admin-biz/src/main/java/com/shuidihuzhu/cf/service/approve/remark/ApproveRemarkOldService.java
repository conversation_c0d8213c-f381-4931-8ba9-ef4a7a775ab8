package com.shuidihuzhu.cf.service.approve.remark;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.admin.delegate.SeaUserAccountDelegate;
import com.shuidihuzhu.cf.biz.admin.AdminApproveExtBiz;
import com.shuidihuzhu.cf.dao.approve.AdminApproveDAO;
import com.shuidihuzhu.cf.delegate.OrganizationDelegate;
import com.shuidihuzhu.cf.domain.approve.AdminApproveExt;
import com.shuidihuzhu.cf.enums.approve.ApproveSourceTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingApprove;
import com.shuidihuzhu.cf.vo.crowdfunding.CrowdfundingApproveCommentVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 老审核评论类
 * <AUTHOR>
 */
@Slf4j
@Service
public class ApproveRemarkOldService {

    private static final String SPLIT = ":";

    @Resource
    private OrganizationDelegate organizationDelegate;

    @Autowired
    private AdminApproveDAO adminApproveDAO;

    @Autowired
    private SeaUserAccountDelegate seaUserAccountDelegate;

    @Autowired
    private AdminApproveExtBiz adminApproveExtBiz;

    /**
     * 用于替代 {@link com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingOperationDelegate#getListByCrowdfundingId(Integer)}
     * @param caseId
     * @return
     */
    public List<CrowdfundingApprove> listByCaseId(int caseId) {
        return listByCaseIdAndSourceTypes(caseId, ApproveSourceTypeEnum.Pages.MAIN_PAGE);
    }

    public CrowdfundingApprove getLastReportRemarkByCaseId(int caseId) {
        return adminApproveDAO.getLastByCaseIdAndOrgKey(caseId, "举报组");
    }

    public CrowdfundingApprove getById(int id) {
        return adminApproveDAO.getById(id);
    }

    /**
     * 建议使用{@link ApproveSourceTypeEnum.Pages} 或在此类中定义
     * @param caseId
     * @param sourceTypeEnums
     * @return
     */
    public List<CrowdfundingApprove> listByCaseIdAndSourceTypes(int caseId, ApproveSourceTypeEnum...sourceTypeEnums) {
        if (ArrayUtils.isEmpty(sourceTypeEnums)) {
            return Lists.newArrayList();
        }
        List<Integer> sourceTypes = Arrays.stream(sourceTypeEnums)
                .map(ApproveSourceTypeEnum::getValue)
                .collect(Collectors.toList());
        return adminApproveDAO.listByCaseIdAndSourceTypes(caseId, sourceTypes);
    }

    public void add(int caseId, int operatorId, String prefix, String content) {
        add(caseId, operatorId, prefix, content, ApproveSourceTypeEnum.DEFAULT);
    }

    public void add(int caseId, int operatorId, String prefix, String content, ApproveSourceTypeEnum sourceTypeEnum) {
        add(caseId, operatorId, prefix + SPLIT + content, sourceTypeEnum);
    }

    public void add(int caseId, int operatorId, String content) {
        add(caseId, operatorId, content, ApproveSourceTypeEnum.DEFAULT);
    }

    public void add(int caseId, int operatorId, String content, ApproveSourceTypeEnum sourceTypeEnum) {
        CrowdfundingApprove v = new CrowdfundingApprove();
        v.setCrowdfundingId(caseId);
        v.setOprid(operatorId);
        v.setComment(content);
        v.setOrganization(getOrganization(operatorId));
        v.setSourceType(sourceTypeEnum.getValue());
        v.setStatus(0);
        adminApproveDAO.insert(v);
    }

    /**
     * 获取组织快照
     * @param operatorId
     * @return
     */
    private String getOrganization(Integer operatorId) {
        return organizationDelegate.getSimpleOrganization(operatorId);
    }

    public List<CrowdfundingApproveCommentVo> convert2VO(List<CrowdfundingApprove> approves) {
        if (CollectionUtils.isEmpty(approves)) {
            return Lists.newArrayList();
        }
        List<Integer> operatorIds = approves.stream()
                .map(CrowdfundingApprove::getOprid)
                .map(Math::toIntExact)
                .collect(Collectors.toList());
        Map<Integer, String> accountMap = seaUserAccountDelegate.getByOperators(operatorIds);
        List<Integer> collect = approves.stream()
                .map(CrowdfundingApprove::getId)
                .collect(Collectors.toList());
        Map<Integer, AdminApproveExt> adminApproveExtMap = adminApproveExtBiz.getApproveExtList(collect, AdminApproveExt.APPROVE_STATUS);
        return approves.stream().map(v -> convert(v, accountMap, adminApproveExtMap)).collect(Collectors.toList());
    }

    private CrowdfundingApproveCommentVo convert(CrowdfundingApprove a, Map<Integer, String> operatorMap, Map<Integer, AdminApproveExt> adminApproveExtMap) {
        CrowdfundingApproveCommentVo v = new CrowdfundingApproveCommentVo();
        v.setComment(a.getComment());
        v.setOprtime(a.getOprtime());
        int operatorId = Math.toIntExact(a.getOprid());
        String operator = operatorMap.get(operatorId);
        v.setOperator(StringUtils.isEmpty(operator) ? "system" : operator);
        v.setOprid(operatorId);
        v.setOrganization(a.getOrganization());
        v.setWorkOrderId(a.getWorkOrderId());
        int approveId = Objects.nonNull(a.getId()) ? a.getId() : 0;
        v.setApproveId(approveId);
        AdminApproveExt adminApproveExt = adminApproveExtMap.get(approveId);
        v.setApproveStatus(Objects.nonNull(adminApproveExt) ? Integer.parseInt(adminApproveExt.getExtValue()) : 0);
        return v;
    }
}
