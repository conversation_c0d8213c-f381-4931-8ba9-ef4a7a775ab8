package com.shuidihuzhu.cf.service.gift.impl;

import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.enums.export.CfWorkOrderFlowV3DownloadField;
import com.shuidihuzhu.cf.enums.export.ReceivingGoodsDownloadField;
import com.shuidihuzhu.cf.export.impl.ReceivingGoodsExportStrategy;
import com.shuidihuzhu.cf.finance.model.CfReminderWord;
import com.shuidihuzhu.cf.service.gift.SeaDonationGiftService;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.api.client.DonationGiftFeignClient;
import com.shuidihuzhu.client.cf.api.model.enums.RegistrationTypeEnum;
import com.shuidihuzhu.client.model.ReceivingGoodsDto;
import com.shuidihuzhu.client.model.ReceivingGoodsInfo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/6/8 9:10 PM
 */
@Slf4j
@Service
public class SeaDonationGiftServiceImpl implements SeaDonationGiftService {

    @Resource
    private ShuidiCipher shuidiCipher;
    @Resource
    private DonationGiftFeignClient donationGiftFeignClient;
    @Resource
    private ReceivingGoodsExportStrategy receivingGoodsExportStrategy;
    @Resource
    private MaskUtil maskUtil;

    @Override
    public void seaAddReceivingInfo(ReceivingGoodsInfo receivingGoodsInfo, long userId, int registrationType) {
        donationGiftFeignClient.seaAddReceivingInfo(receivingGoodsInfo, ContextUtil.getAdminLongUserId(), RegistrationTypeEnum.SEA_BACK_REGISTRATION.getCode());
    }

    @Override
    public Map<String, Object> seaQueryReceivingInfo(String receivingName, String receivingPhone, String receivingAddress,
                                                     String beginTime, String endTime, int current) {
        Response<Map<String, Object>> response = donationGiftFeignClient.seaQueryReceivingInfo(receivingName, receivingPhone, receivingAddress,
                beginTime, endTime,  current);
        Map<String, Object> resultMap = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);
        if (Objects.isNull(resultMap)) {
            return Maps.newHashMap();
        }
        Object detailsJson = resultMap.get("detail");
        String detailsStr = JSONUtils.toJSONString(detailsJson);
        List<ReceivingGoodsDto> details = JSON.parseObject(detailsStr, new TypeReference<List<ReceivingGoodsDto>>() {
        });
        if (CollectionUtils.isNotEmpty(details)) {
            details.forEach(detail -> {
                detail.setReceivingPhoneMask(maskUtil.buildByDecryptPhone(detail.getReceivingPhone()));
                detail.setReceivingPhone(null);
            });
            resultMap.put("detail", details);
        }
        return resultMap;
    }

    @Override
    public Boolean seaExportReceivingExcel(String beginTime, String endTime, long adminUserId) {

        Response<List<ReceivingGoodsDto>> response = donationGiftFeignClient.seaQueryReceivingInfoByTime(beginTime, endTime);
        List<ReceivingGoodsDto> receivingGoodsDtos = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);
        if (CollectionUtils.isEmpty(receivingGoodsDtos)) {
            return false;
        }

        List<Map<ReceivingGoodsDownloadField, String>> data = Lists.newArrayList();
        for (ReceivingGoodsDto receivingGoodsDto : receivingGoodsDtos) {

            RegistrationTypeEnum registrationTypeEnum = RegistrationTypeEnum.getByCode(receivingGoodsDto.getRegistrationType());
            String registrationType = Optional.ofNullable(registrationTypeEnum).map(RegistrationTypeEnum::getDesc).orElse("");

            Map<ReceivingGoodsDownloadField, String> map = Maps.newHashMap();
            map.put(ReceivingGoodsDownloadField.ID, String.valueOf(receivingGoodsDto.getId()));
            map.put(ReceivingGoodsDownloadField.RECEIVING_NAME, receivingGoodsDto.getReceivingName());
            map.put(ReceivingGoodsDownloadField.RECEIVING_PHONE, shuidiCipher.decrypt(receivingGoodsDto.getReceivingPhone()));
            map.put(ReceivingGoodsDownloadField.RECEIVING_ADDRESS, receivingGoodsDto.getReceivingAddress());
            map.put(ReceivingGoodsDownloadField.CERTIFICATE_NICKNAME, receivingGoodsDto.getCertificateNickname());
            map.put(ReceivingGoodsDownloadField.CREATE_TIME, DateUtil.getChDateForTimeStamp(receivingGoodsDto.getCreateTime()));
            map.put(ReceivingGoodsDownloadField.REGISTRATION_TYPE, registrationType);
            data.add(map);
        }

        CfReminderWord<Void> export = receivingGoodsExportStrategy.export(adminUserId, data, Arrays.stream(ReceivingGoodsDownloadField.values()).map(ReceivingGoodsDownloadField::name).collect(Collectors.toList()));
        return export.isSuccessFlag();
    }
}
