package com.shuidihuzhu.cf.service.bigdata.Impl;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.dto.PageSerializable;
import com.shuidihuzhu.cf.model.bi.PotraitModel;
import com.shuidihuzhu.cf.service.bigdata.BiClientService;
import com.shuidihuzhu.client.dataservice.biservice.v1.BiServiceClient;
import com.shuidihuzhu.client.model.Response;
import com.shuidihuzhu.client.model.TagPortraitDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @package: com.shuidihuzhu.cf.service.bigdata.Impl
 * @Author: liu<PERSON>aw<PERSON>
 * @Date: 2019-03-17  19:05
 */
@Slf4j
@Service
public class BiClientServiceImpl implements BiClientService {
    @Autowired
    private BiServiceClient biServiceClient;
    private static final int MAX_RETRIES = 5;

    @Override
    public PotraitModel getPotraitById(String potraitId) {
        if (StringUtils.isBlank(potraitId)) {
            return null;
        }
        List<PotraitModel> potraitModels = getPotrait("", potraitId, 1).getData();
        if (CollectionUtils.isNotEmpty(potraitModels)) {
            return potraitModels.get(0);
        }
        return null;
    }

    @Override
    public PageSerializable<PotraitModel> getPotrait(String potraitName, String potraitId, Integer pageNum, Integer pageSize) {
        if (StringUtils.isBlank(potraitName)) {
            potraitName = "";
        }
        if (StringUtils.isBlank(potraitId)) {
            potraitId = "";
        }
        return getPotrait(potraitName, potraitId, pageNum, pageSize, 1);
    }

    private PageSerializable<PotraitModel> getPotrait(String potraitName, String potraitId, int retries) {
        return getPotrait(potraitName, potraitId, 0, 0, retries);
    }

    private PageSerializable<PotraitModel> getPotrait(String potraitName, String potraitId, Integer pageNum, Integer pageSize, int retries) {
        List<PotraitModel> potraitModels = Lists.newArrayList();
        try {
            Response<PageInfo<TagPortraitDO>> response = biServiceClient.findPortraitByName(potraitName, potraitId, pageNum, pageSize);
            if (response != null) {
                if (response.getCode() == 0) {
                    PageInfo<TagPortraitDO> pageInfo = response.getData();
                    if (pageInfo != null) {
                        pageInfo = response.getData();
                        List<TagPortraitDO> tagPortraitDOList = pageInfo.getList();
                        if (CollectionUtils.isNotEmpty(tagPortraitDOList)) {
                            tagPortraitDOList.forEach(item -> {
                                PotraitModel potraitModel = new PotraitModel(item.getPortraitId(), item.getTaskName(), item.getComputeNum().intValue(), item.getComputeLastTime(), 1);
                                potraitModels.add(potraitModel);
                            });
                        }
                    }
                    return new PageSerializable<>(pageInfo, potraitModels);
                }
                return new PageSerializable<>(null, potraitModels);
            } else {
                log.error("拉取大数据人群包异常,接口返回code：{},msg", response.getCode(), response.getMsg());
                if (retries > MAX_RETRIES) {
                    log.error("大数据异常，重试失败");
                    return new PageSerializable<>();
                }
                log.error("大数据异常，重试次数：{}", retries);
                return getPotrait(potraitName, potraitId, ++retries);
            }
        } catch (Exception e) {
            log.error("拉取大数据人群包异常.", e);
            if (retries > MAX_RETRIES) {
                log.error("大数据异常，重试失败");
                return new PageSerializable<>();
            }
            return getPotrait(potraitName, potraitId, ++retries);
        }
    }

}
