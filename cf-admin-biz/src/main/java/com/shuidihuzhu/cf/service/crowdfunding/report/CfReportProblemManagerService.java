package com.shuidihuzhu.cf.service.crowdfunding.report;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.google.common.collect.*;
import com.shuidihuzhu.cf.biz.crowdfunding.CfReportModuleOperationLogBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfReportProblemBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfReportProblemOperationLogBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfReportProblemRelationshipBiz;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminCfReportProblemClassifyUseStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminReportPronlemUseEnum;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileSettings;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.*;
import com.shuidihuzhu.cf.service.cfOperatingProfile.CfOperatingProfileSettingsExtBiz;
import com.shuidihuzhu.cf.service.crowdfunding.SeaAccountService;
import com.shuidihuzhu.cf.vo.crowdfunding.AdminCfReportProblemClassifyVo;
import com.shuidihuzhu.cf.vo.crowdfunding.AdminCfReportProblemClassifyAndModuleVo;
import com.shuidihuzhu.cf.vo.crowdfunding.AdminCfReportProblemModuleVo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * @author: fengxuan
 * @create 2019-12-12 10:12
 * problem label, problem , problem relationship manager
 **/
@Slf4j
@Service
public class CfReportProblemManagerService {

    @Autowired
    private CfReportProblemBiz cfReportProblemBiz;

    @Autowired
    private CfReportProblemRelationshipBiz relationshipBiz;
    @Autowired
    private CfOperatingProfileSettingsExtBiz settingsExtBiz;
    @Autowired
    private CfReportModuleOperationLogBiz cfReportModuleOperationLogBiz;
    @Autowired
    private SeaAccountService seaAccountService;
    @Autowired
    private CfReportProblemOperationLogBiz cfReportProblemOperationLogBiz;
    @Autowired
    private CfReportProblemManagerLogService cfReportProblemManagerLogService;

    public Response<Boolean> createProblem(CfReportProblemParam problemParam) {
        boolean isNoChoiceType = CfReportProblem.noChoiceProblemType(problemParam.getProblemType());

        if (!isNoChoiceType) {
            problemParam.setAnswerType(CfReportProblem.ReportAnswerType.zhifuchuan.getCode());
        } else {
            problemParam.setChoiceDescribes(Lists.newArrayList());
        }

        boolean insertSuc = cfReportProblemBiz.insert(problemParam);
        //新建时,非输入的需要保存备选信息到 relationship
        if (insertSuc && !isNoChoiceType) {
            for (String choiceDescribe : problemParam.getChoiceDescribes()) {
                CfReportProblemRelationship relationship = new CfReportProblemRelationship();
                relationship.setProblemId(problemParam.getId());
                relationship.setProblem(problemParam.getProblem());
                relationship.setContent(choiceDescribe);
                relationshipBiz.insert(relationship);
            }
        }
        if (insertSuc) {
            try {
                cfReportProblemManagerLogService.addProblemLog(problemParam);
            }catch (Exception e){
                log.error("addProblemLog error", e);
            }
        }
        return NewResponseUtil.makeSuccess(true);
    }




    public Response<Boolean> updateProblem(int userId, CfReportProblemParam problemParam) {
        boolean isNoChoiceType = CfReportProblem.noChoiceProblemType(problemParam.getProblemType());

        if (!isNoChoiceType) {
            problemParam.setAnswerType(CfReportProblem.ReportAnswerType.zhifuchuan.getCode());
        } else {
            problemParam.setChoiceDescribes(Lists.newArrayList());
        }

        boolean updateSuc = false;
        boolean changeProblemName = false;
        CfReportProblem oldProblem = cfReportProblemBiz.findProblemById(problemParam.getId());
        if (oldProblem == null) {
            log.warn("更新时id传入有误 problemParam:{}", JSON.toJSONString(problemParam));
            return NewResponseUtil.makeFail("id传入有误");
        }
        //修改了问题名称
        changeProblemName = !oldProblem.getProblem().equals(problemParam.getProblem());

        boolean addOrDeleteChoices = false;
        //修改了备选项
        Set<String> oldContents = relationshipBiz.listRelationByProblemIds(Lists.newArrayList(problemParam.getId()))
                .stream()
                .map(CfReportProblemRelationship::getContent)
                .collect(Collectors.toSet());
        Set<String> newContents = Sets.newHashSet(problemParam.getChoiceDescribes());
        //删除备选项
        Sets.SetView<String> deleteContents = Sets.difference(oldContents, newContents);
        //新增备选项
        Sets.SetView<String> addContents = Sets.difference(newContents, oldContents);
        List<String> deleteContentList = Lists.newArrayList(deleteContents);
        deleteContentList = deleteContentList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if ((!deleteContents.isEmpty() && !CollectionUtils.isEmpty(deleteContentList)) || !addContents.isEmpty()) {
            log.info("CfReportProblemParam:{},删除备选项:{},新增备选项:{}", JSON.toJSONString(problemParam), JSON.toJSONString(deleteContents), JSON.toJSONString(addContents));
            addOrDeleteChoices = true;
        }

        updateSuc = cfReportProblemBiz.updateById(problemParam);
        if (updateSuc && changeProblemName) {
            // 修改了问题名称
            settingsExtBiz.unbindRelation(userId, CfOperatingProfileSettings.ProfileExtName.NAME, Lists.newArrayList(problemParam.getId()));

            relationshipBiz.onlyChangeProblemName(problemParam.getId(), problemParam.getProblem());
        }
        if (addOrDeleteChoices && updateSuc) {
            //逻辑删除之前的绑定关系,并重新生成选项
            relationshipBiz.deleteByProblemIdAndContent(userId, problemParam.getId(), Lists.newArrayList(deleteContents));

            for (String choiceDescribe : addContents) {
                CfReportProblemRelationship relationship = new CfReportProblemRelationship();
                relationship.setProblemId(problemParam.getId());
                relationship.setProblem(problemParam.getProblem());
                relationship.setContent(choiceDescribe);
                updateSuc = updateSuc && relationshipBiz.insert(relationship);
            }
        }
        try {
            cfReportProblemManagerLogService.updateProblemLog(problemParam, oldProblem, deleteContentList, Lists.newArrayList(addContents));
        } catch (Exception e) {
            log.error("updateProblemLog error", e);
        }
        return NewResponseUtil.makeSuccess(updateSuc);
    }



    //问题列表
    public Response<CfReportProblemPageResult> listProblem(String problemDesc, int current, int pageSize,
                                                           Integer id, Integer isUse, Integer collation) {
        id = (id == null) ? -1 : id;
        isUse = (isUse == null || AdminReportPronlemUseEnum.ALL.getType() == isUse) ? -1 : isUse;
        List<CfReportProblem> cfReportProblems =
                cfReportProblemBiz.listForManager(problemDesc, current, pageSize, id, isUse, collation);
        CfReportProblemPageResult pageResult = buildPage(current, pageSize, cfReportProblems);

        List<CfReportProblemVo> list = getCfReportProblemVos(cfReportProblems);
        pageResult.setList(list);
        return NewResponseUtil.makeSuccess(pageResult);
    }


    //点查
    public Response<CfReportProblemVo> getByProblemId(int problemId) {
        CfReportProblem problem = cfReportProblemBiz.findProblemById(problemId);
        if (problem == null) {
            return NewResponseUtil.makeSuccess(null);
        }
        List<CfReportProblemVo> cfReportProblemVos = getCfReportProblemVos(Lists.newArrayList(problem));
        if (CollectionUtils.isEmpty(cfReportProblemVos)) {
            log.warn("获取单个举报问题异常,problemId:{}", problemId);
            return NewResponseUtil.makeFail("获取单个举报问题异常");
        }
        return NewResponseUtil.makeSuccess(cfReportProblemVos.get(0));
    }


    //获取同标签下其他问题信息
    public Response<List<CfReportProblemVo>> listSameLabelProblem(int problemId, int labelId) {
        //check problemid and labelid should be match
        CfReportProblem problem = cfReportProblemBiz.findProblemById(problemId);
        if (problem == null || problem.getLabelId() != labelId) {
            log.warn("入参的问题和标签id不匹配,problemId:{},labelId:{}", problemId, labelId);
            return NewResponseUtil.makeFail("入参的问题和标签id不匹配");
        }
        List<CfReportProblem> cfReportProblems = cfReportProblemBiz.listSameLabelProblems(labelId);
        //需要排除当前的问题
        List<CfReportProblem> filterProblems = cfReportProblems.stream()
                .filter(item -> problemId != item.getId())
                .collect(Collectors.toList());

        return NewResponseUtil.makeSuccess(getCfReportProblemVos(filterProblems));
    }


    @NotNull
    private List<CfReportProblemVo> getCfReportProblemVos(List<CfReportProblem> cfReportProblems) {
        if (CollectionUtils.isEmpty(cfReportProblems)) {
            return Lists.newArrayList();
        }
        List<Integer> labelIds = cfReportProblems.stream().map(CfReportProblem::getLabelId).collect(Collectors.toList());

        //获取标签信息
        List<CfReportProblemLabel> labels = cfReportProblemBiz.listProblemLabelsByIds(labelIds,null);

        //根据 nextProblemIds 获取relationship
        List<CfReportProblemRelationship> relationships = Lists.newArrayList();
        List<Integer> problemIds = cfReportProblems.stream().map(CfReportProblem::getId).collect(Collectors.toList());
        relationships.addAll(relationshipBiz.listByNextProblemIds(problemIds));
        relationships.addAll(relationshipBiz.listRelationByProblemIds(problemIds));

        return CfReportProblemVo.fillExtInfo(cfReportProblems, relationships, labels);
    }


    //绑定问题
    public Response<Boolean> bindProblem(List<CfReportProblemParam.BindParam> bindParams) {
        //check exist all problemId
        Set<Integer> problemIds = Sets.newHashSet();
        Set<Integer> problemIdSet = bindParams.stream().map(CfReportProblemParam.BindParam::getProblemId).collect(Collectors.toSet());
        problemIds.addAll(problemIdSet);
        Set<Integer> nextProblemSet = bindParams.stream().map(CfReportProblemParam.BindParam::getNextProblemId).collect(Collectors.toSet());
        problemIds.addAll(nextProblemSet);
        List<CfReportProblem> cfReportProblems = cfReportProblemBiz.listByIds(Lists.newArrayList(problemIds));
//        if (cfReportProblems.size() != problemIds.size()) {
//            log.warn("exist problemId not in table, problemIds:{},bindParams:{}", problemIds, bindParams);
//            return NewResponseUtil.makeFail("参数错误");
//        }

        Map<Integer, CfReportProblem> id2ProblemInfo = Maps.uniqueIndex(cfReportProblems.stream().filter(item -> problemIdSet.contains(item.getId())).collect(Collectors.toList()), CfReportProblem::getId);
        Map<Integer, CfReportProblem> nextProblemId2ProblemInfo = Maps.uniqueIndex(cfReportProblems.stream().filter(item -> nextProblemSet.contains(item.getId())).collect(Collectors.toList()), CfReportProblem::getId);

        Map<Integer, List<CfReportProblemRelationship>> problem2Relationship = relationshipBiz
                .listRelationByProblemIds(Lists.newArrayList(problemIdSet))
                .stream()
                .collect(Collectors.groupingBy(CfReportProblemRelationship::getProblemId));

        Map<Integer, List<CfReportProblemParam.BindParam>> problemId2BindParam = bindParams
                .stream()
                .collect(Collectors.groupingBy(CfReportProblemParam.BindParam::getProblemId));

        boolean result = true;
        for (Integer problemId : id2ProblemInfo.keySet()) {
            Set<Integer> hadRelationNextProblemId = problem2Relationship
                    .getOrDefault(problemId, Lists.newArrayList())
                    .stream()
                    .map(CfReportProblemRelationship::getNextProblemId)
                    .filter(item -> item > 0)
                    .collect(Collectors.toSet());

            Set<Integer> nextProblemId = problemId2BindParam
                    .getOrDefault(problemId, Lists.newArrayList())
                    .stream()
                    .map(CfReportProblemParam.BindParam::getNextProblemId)
                    .filter(item -> item > 0)
                    .collect(Collectors.toSet());

            Sets.SetView<Integer> needUnBindNextProblemIds = Sets.difference(hadRelationNextProblemId, nextProblemId);
            Sets.SetView<Integer> needNewAddNextProblemIds = Sets.difference(nextProblemId, hadRelationNextProblemId);

            printBindInfo(cfReportProblems, problemId, needUnBindNextProblemIds, needNewAddNextProblemIds, nextProblemId);

            relationshipBiz.deleteByProblemIdAndNextProblemId(problemId, Lists.newArrayList(needUnBindNextProblemIds));
            for (Integer needNewAddNextProblemId : needNewAddNextProblemIds) {
                List<CfReportProblemParam.BindParam> bindParamList = problemId2BindParam.getOrDefault(problemId, Lists.newArrayList());
                for (CfReportProblemParam.BindParam bindParam : bindParamList) {
                    if (bindParam.getNextProblemId() == needNewAddNextProblemId) {
                        result = result && relationshipBiz.bindRelationship(bindParam, id2ProblemInfo.get(bindParam.getProblemId()), nextProblemId2ProblemInfo.get(bindParam.getNextProblemId()));
                    }
                }
            }
        }
        return NewResponseUtil.makeSuccess(result);
    }


    private void printBindInfo(List<CfReportProblem> cfReportProblems, Integer problemId, Sets.SetView<Integer> needUnBindNextProblemIds, Sets.SetView<Integer> needNewAddNextProblemIds, Set<Integer> nextProblemId) {
        List<String> unBindProblems = cfReportProblems.stream()
                .filter(item -> needUnBindNextProblemIds.contains(item.getId()))
                .map(CfReportProblem::getProblem)
                .collect(Collectors.toList());

        List<String> addNewNextProblems = cfReportProblems.stream()
                .filter(item -> needNewAddNextProblemIds.contains(item.getId()))
                .map(CfReportProblem::getProblem)
                .collect(Collectors.toList());

        List<String> allProblems = cfReportProblems.stream()
                .filter(item -> nextProblemId.contains(item.getId()))
                .map(CfReportProblem::getProblem).collect(Collectors.toList());

        log.info("problemId:{},新增绑定:{},删除绑定:{}", problemId, addNewNextProblems, unBindProblems);
        cfReportProblemManagerLogService.bindProblemLog(allProblems, problemId);
    }


    private static CfReportProblemPageResult buildPage(int current, int pageSize, List<CfReportProblem> cfReportProblems) {
        CfReportProblemPageResult pageResult = new CfReportProblemPageResult();
        Page page;
        if (cfReportProblems instanceof Page) {
            page = (Page) cfReportProblems;
            pageResult.setPageSize(page.getPageSize());
            pageResult.setCurrent(page.getPageNum());
            pageResult.setTotal(page.getTotal());
        } else {
            pageResult.setPageSize(pageSize);
            pageResult.setCurrent(current);
            pageResult.setTotal(0);
        }
        return pageResult;
    }



    public boolean createModule(int id, String moduleName) {
        CfReportProblemLabel cfReportProblemLabel = new CfReportProblemLabel();
        cfReportProblemLabel.setLabelDesc(moduleName);
        cfReportProblemLabel.setLabelLevel(2);
        cfReportProblemLabel.setParentId(id);
        return cfReportProblemBiz.insertLabel(cfReportProblemLabel);
    }

    public boolean updateUseStatus(int id, int isUse) {
        int result = cfReportProblemBiz.updateUseStatusById(isUse, id);
        if (result > 0) {
            cfReportProblemManagerLogService.addReportProblemOperationLog( AdminCfReportProblemClassifyUseStatus.getDescription(isUse), id);
        }
        return result == 1;
    }

    /**
     * 增加/更新分类
     * @param status
     * @param classifyName
     * @param sort
     * @param id
     * @return
     */
    public Response<Boolean> addOrUpdateClassify(int status, String classifyName,
                                                 int sort, int id) {
        CfReportProblemLabel oldCfReportProblemLabel = (status == 0) ? null : cfReportProblemBiz.getById(id);
        int result = (status == 0) ? cfReportProblemBiz.addProblemClassify(classifyName, sort, 1) : cfReportProblemBiz.updateLabel(classifyName, sort, id);
        if (result > 0){
            cfReportProblemManagerLogService.addOrUpdateClassifyLog(status, classifyName, sort, id, result, oldCfReportProblemLabel);
        }
        return NewResponseUtil.makeSuccess(result > 0);
    }


    /**
     * 分类信息是否启用
     * @param id
     * @param isUse
     * @return
     */
    public Response<Integer> updateProblemClassifyUseStatus(int id, int isUse) {
        if (isUse == AdminCfReportProblemClassifyUseStatus.NOT_USE.getType()) {
            List<CfReportProblemLabel> cfReportProblemLabels = cfReportProblemBiz.getByParentIdAndIsUse(id, AdminCfReportProblemClassifyUseStatus.IS_USE.getType());
            if (CollectionUtils.isNotEmpty(cfReportProblemLabels)) {
                return NewResponseUtil.makeError(AdminErrorCode.CF_REPORT_PROBLEM_CLASSIFY_ERROR);
            }
        }
        int result = cfReportProblemBiz.updateIsUse(isUse, id);
        if (result > 0) {
            cfReportProblemManagerLogService.addReportProblemClassifyOperationLog(AdminCfReportProblemClassifyUseStatus.getDescription(isUse), id);
        }
        return NewResponseUtil.makeSuccess(result);
    }


    /**
     * 分类操作&模块操作日志信息
     * @param id
     * @return
     */
    public Response<List<CfReportModuleOperationLog>> getModuleAndClassifyOperationLog(int id) {
        List<CfReportModuleOperationLog> cfReportProblemOperationLogs = cfReportModuleOperationLogBiz.getByLabelId(id);
        return NewResponseUtil.makeSuccess(cfReportProblemOperationLogs);
    }


    /**
     * 返回全部分类信息
     * @return
     */
    public Response<List<AdminCfReportProblemClassifyVo>> getClassifyAll() {
        List<AdminCfReportProblemClassifyVo> adminCfReportProblemClassifyVos = Lists.newArrayList();
        List<CfReportProblemLabel> cfReportProblemLabels = cfReportProblemBiz.listLabels(1, null,null);
        for (CfReportProblemLabel cfReportProblemLabel : cfReportProblemLabels) {
            adminCfReportProblemClassifyVos.add(AdminCfReportProblemClassifyVo.buildVo(cfReportProblemLabel));
        }
        adminCfReportProblemClassifyVos = adminCfReportProblemClassifyVos.stream()
                .sorted(Comparator.comparing(AdminCfReportProblemClassifyVo::getIsUse).reversed()
                        .thenComparing(AdminCfReportProblemClassifyVo::getId)).collect(Collectors.toList());
        return NewResponseUtil.makeSuccess(adminCfReportProblemClassifyVos);
    }


    /**
     * 增加/更新模块
     * @param status
     * @param moduleName
     * @param isMandatory
     * @param classifyId
     * @param sort
     * @param id
     * @return
     */
    public Response<Boolean> addOrUpdateModule(int status, String moduleName, int isMandatory, int classifyId,
                                               int sort, int id) {
        CfReportProblemLabel oldCfReportProblemLabel = (status == 0) ? null : cfReportProblemBiz.getById(id);
        int result = (status == 0) ? cfReportProblemBiz.addProblemModule(moduleName, isMandatory, classifyId, sort) :cfReportProblemBiz.updateProblemModule(classifyId, moduleName, sort, isMandatory, id);
        if (result > 0){
            cfReportProblemManagerLogService.addOrUpdateModuleLog(status, moduleName, isMandatory,
                    classifyId, sort, id, result, oldCfReportProblemLabel);
        }
        return NewResponseUtil.makeSuccess(result > 0);
    }


    /**
     * 模块信息是否启用
     * @param id
     * @param isUse
     * @return
     */
    public Response<Integer> updateModuleUseStatus(int id, int isUse) {
        if (isUse == AdminCfReportProblemClassifyUseStatus.NOT_USE.getType()) {
            List<CfReportProblem> cfReportProblemList =
                    cfReportProblemBiz.listByLabelIdAndIsUse(id, AdminCfReportProblemClassifyUseStatus.IS_USE.getType());
            if (CollectionUtils.isNotEmpty(cfReportProblemList)) {
                return NewResponseUtil.makeError(AdminErrorCode.CF_REPORT_PROBLEM_MODULE_ERROR);
            }
        }
        int result = cfReportProblemBiz.updateIsUse(isUse, id);
        if (result > 0) {
            cfReportProblemManagerLogService.addReportProblemClassifyOperationLog(AdminCfReportProblemClassifyUseStatus.getDescription(isUse), id);
        }
        return NewResponseUtil.makeSuccess(result);
    }


    /**
     * 返回全部分类&模块信息
     * @return
     */
    public Response<List<AdminCfReportProblemClassifyAndModuleVo>> getProblemModuleAll() {
        List<CfReportProblemLabel> allLabels = cfReportProblemBiz.listLabels(null, null,null);
        List<CfReportProblemLabel> problemClassifyLists = allLabels.stream().filter(t -> t.getParentId() == 0).collect(Collectors.toList());
        List<CfReportProblemLabel> problemModuleLists = allLabels.stream().filter(t -> t.getParentId() != 0).collect(Collectors.toList());
        List<AdminCfReportProblemClassifyAndModuleVo> adminCfReportProblemModuleVos = Lists.newArrayList();
        problemClassifyLists.forEach(problemClassify -> {
            if (problemClassify != null) {
                List<CfReportProblemLabel> cfReportProblemLabels = problemModuleLists.stream().filter(t -> problemClassify.getId() == t.getParentId()).collect(Collectors.toList());
                buildList(cfReportProblemLabels, problemClassify, adminCfReportProblemModuleVos);
            }
        });

        return NewResponseUtil.makeSuccess(adminCfReportProblemModuleVos);
    }

    //组装数据
    private void buildList(List<CfReportProblemLabel> cfReportProblemLabels,
                           CfReportProblemLabel reportProblemLabel,
                           List<AdminCfReportProblemClassifyAndModuleVo> adminCfReportProblemModuleVos) {
        List<AdminCfReportProblemModuleVo> adminCfReportProblemClassifyVos = Lists.newArrayList();
        for (CfReportProblemLabel cfReportProblemLabel : cfReportProblemLabels) {
            adminCfReportProblemClassifyVos.add(AdminCfReportProblemModuleVo.buildVo(cfReportProblemLabel));
        }
        adminCfReportProblemClassifyVos =
                adminCfReportProblemClassifyVos.stream().sorted(Comparator.comparing(AdminCfReportProblemModuleVo::getIsUse).reversed().thenComparing(AdminCfReportProblemModuleVo::getId)).collect(Collectors.toList());
        AdminCfReportProblemClassifyAndModuleVo adminCfReportProblemModuleVo =
                AdminCfReportProblemClassifyAndModuleVo.buildVo(reportProblemLabel.getLabelDesc(), reportProblemLabel.getId(),
                        adminCfReportProblemClassifyVos);
        adminCfReportProblemModuleVos.add(adminCfReportProblemModuleVo);
    }

    //返回日志信息
    public Response<List<CfReportProblemOperationLog>> getProblemOperationLog(int id){
        List<CfReportProblemOperationLog> cfReportProblemOperationLogs = cfReportProblemOperationLogBiz.getByProblemId(id);
        return NewResponseUtil.makeSuccess(cfReportProblemOperationLogs);
    }

}
