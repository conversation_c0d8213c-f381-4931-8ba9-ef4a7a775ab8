package com.shuidihuzhu.cf.service.tog.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.model.tog.GovMarkInfoVO;
import com.shuidihuzhu.cf.service.tog.GovMarkService;
import com.shuidihuzhu.client.cf.api.client.CfGovMarkFeignClient;
import com.shuidihuzhu.client.cf.api.model.GovCooperationMark;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2025/3/31 15:42
 */
@Slf4j
@Service
public class GovMarkServiceImpl implements GovMarkService {

    @Resource
    private CfGovMarkFeignClient cfGovMarkFeignClient;

    @Override
    public List<GovMarkInfoVO> listAllGovInfoByCaseId(Integer caseId) {

        Response<List<GovCooperationMark>> response = cfGovMarkFeignClient.listMarkRecordsByAllGov(caseId);
        List<GovCooperationMark> govCooperationMarkList = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);
        if (CollectionUtils.isEmpty(govCooperationMarkList)) {
            log.info("GovMarkServiceImpl govCooperationMarkList is null {}", caseId);
            return Lists.newArrayList();
        }

        return buildGovList(govCooperationMarkList);

    }

    private List<GovMarkInfoVO> buildGovList(List<GovCooperationMark> govCooperationMarkList) {
        if (CollectionUtils.isEmpty(govCooperationMarkList)) {
            return Lists.newArrayList();
        }

        return govCooperationMarkList.stream()
                .map(this::convertToGovMarkInfoVO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private GovMarkInfoVO convertToGovMarkInfoVO(GovCooperationMark mark) {
        if (mark == null) {
            return null;
        }

        return GovMarkInfoVO.builder()
                .govCode(mark.getGovCode())
                .showType(mark.getMarkType() == 1)
                .attributes(mark.getAttributes())
                .build();
    }


}
