package com.shuidihuzhu.cf.service.approve;

import com.shuidihuzhu.cf.vo.approve.MultipleCaseImagesVo;
import com.shuidihuzhu.common.web.model.Response;

import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ApproveService {

    Response<Map<String, Object>> detail(String infoUuid, Integer reportWorkOrderId);

    Response<MultipleCaseImagesVo> getMultipleCaseImages(long workOrderId, int caseId);
}
