package com.shuidihuzhu.cf.service.workorder.initialAudit.ai.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.dao.crowdfunding.CfAiMaterialsDao;
import com.shuidihuzhu.cf.delegate.crowdfunding.impl.CrowdfundingDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterials;
import com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterialsResult;
import com.shuidihuzhu.cf.model.crowdfunding.ai.LayOutField;
import com.shuidihuzhu.cf.service.workorder.initialAudit.ai.AiCondition;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Optional;

/**
 * <AUTHOR>
 * @DATE 2020/12/21
 */
@Service("aiMixLabel")
@Slf4j
public class AiMixLabelCondition implements AiCondition {

    @Autowired
    private CfAiMaterialsDao cfAiMaterialsDao;

    @Resource
    private CrowdfundingDelegate crowdfundingDelegate;

    private static int wan_yuan = 10000*100;

    @Override
    public boolean check(int caseId, String inputValue) {
        return false;
    }

    @Override
    public boolean check(int caseId,CfAiMaterialsResult result) {

        CfAiMaterials m1 = cfAiMaterialsDao.getByCaseId(caseId,CfAiMaterials.jType);
        Optional<String> o1 = Optional.ofNullable(m1).map(CfAiMaterials::getFields).orElse(Lists.newArrayList()).stream().filter(r->"targetAmountInDesc".equals(r.getFieldKey())).map(LayOutField::getFieldValue).findFirst();

        if (o1.isPresent()){
            CrowdfundingInfo c = crowdfundingDelegate.getFundingInfoById(caseId);
//            int amount = o1.map(Integer::valueOf).map(r-> r * wan_yuan).get();
            int amount = new BigDecimal(o1.get()).multiply(new BigDecimal(wan_yuan)).intValue();
            log.info("AiMixLabelCondition amount={} target={}",amount,c.getTargetAmount());
            if (amount < c.getTargetAmount()){
                result.setLabel("请确认是否需要将目标金额修改为文章中描述的目标金额");
                return true;
            }
        }
        return false;
    }

}
