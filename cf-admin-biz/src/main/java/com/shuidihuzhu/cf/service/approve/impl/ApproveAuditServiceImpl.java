package com.shuidihuzhu.cf.service.approve.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.admin.util.AdminListUtil;
import com.shuidihuzhu.cf.biz.admin.UserCommentBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.client.feign.CaseInfoApproveStageFeignClient;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonActionConst;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonRecord;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonRecordClient;
import com.shuidihuzhu.cf.client.ugc.wonrecord.model.WonExtUpdateByIdParam;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.GeneralConstant;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingOperationDelegate;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.delegate.shorturl.ShortUrlDelegate;
import com.shuidihuzhu.cf.enhancer.mq.MQHelperService;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cf.enums.BackgroundLogEnum;
import com.shuidihuzhu.cf.enums.admin.UserCommentSourceEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceFeignClientV2;
import com.shuidihuzhu.cf.finance.client.response.model.FeignResponse;
import com.shuidihuzhu.cf.finance.model.vo.DonationAmountInFenVo;
import com.shuidihuzhu.cf.model.admin.UserComment;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.mq.impl.AdminCommonMessageHelperService;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.risk.model.admin.qc.QcZhuDongCreateParam;
import com.shuidihuzhu.cf.service.FinanceApproveService;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cf.service.admin.CaseInfoApproveSubItemService;
import com.shuidihuzhu.cf.service.approve.ApproveAuditService;
import com.shuidihuzhu.cf.service.approve.ApproveControlService;
import com.shuidihuzhu.cf.service.approve.ApproveSnapshotService;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.cf.service.crowdfunding.*;
import com.shuidihuzhu.cf.service.message.VolunteerHelpService;
import com.shuidihuzhu.cf.service.notice.workwx.WorkWeiXinContentBuilder;
import com.shuidihuzhu.cf.util.crowdfunding.CrowdfundingUtil;
import com.shuidihuzhu.cf.vo.approve.ApproveAuditMsgV2;
import com.shuidihuzhu.cf.vo.approve.ApproveAuditParam;
import com.shuidihuzhu.cf.vo.crowdfunding.AdminApproveVo;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.api.chaifenbeta.ugc.CfUgcServiceFeignClient;
import com.shuidihuzhu.client.cf.growthtool.enums.QyWxMsgTypeEnum;
import com.shuidihuzhu.client.cf.growthtool.model.BdQywxGroupMessageModel;
import com.shuidihuzhu.client.cf.workorder.CfCailiaoWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.CailiaoHandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.AuditStatus;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.model.builder.MessageBuilder;
import com.shuidihuzhu.client.model.event.InfoApproveEvent;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * @Author: wangpeng
 * @Date: 2023/3/23 15:14
 * @Description:
 */
@Slf4j
@Service
@RefreshScope
public class ApproveAuditServiceImpl implements ApproveAuditService {
    @Autowired
    private ApproveRemarkOldService approveRemarkOldService;
    @Resource
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;
    @Resource
    private ICrowdfundingOperationDelegate crowdfundingOperationDelegate;
    @Resource
    private SeaAccountClientV1 seaAccountClientV1;
    @Resource
    private AdminCfInfoExtBiz adminCfInfoExtBiz;
    @Resource
    private CfCommitVerifyItemBiz cfCommitVerifyItemBiz;
    @Resource
    private AdminCrowdfundingOperationBiz adminCrowdfundingOperationBiz;
    @Resource
    private AdminApproveService adminApproveService;
    @Resource
    private FinanceApproveService financeApproveService;
    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;
    @Resource
    private AdminCrowdfundingAuthorBiz adminCrowdfundingAuthorBiz;
    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Resource
    private VolunteerHelpService volunteerHelpService;
    @Resource
    private UserCommentBiz userCommentBiz;
    @Resource
    private CfCailiaoWorkOrderClient workOrderClient;
    @Resource
    private CaseInfoApproveSubItemService caseInfoApproveSubItemService;
    @Resource
    private ApproveControlService approveControlService;
    @Resource
    private AdminCommonMessageHelperService adminCommonMessageHelperService;
    @Resource
    private CfWorkOrderClient cfWorkOrderClient;
    @Resource
    private CaseInfoApproveStageFeignClient caseInfoApproveStageFeignClient;
    @Resource
    private ShuidiCipher shuidiCipher;
    @Resource
    private ShortUrlDelegate shortUrlDelegate;
    @Resource
    private CfContentImageService cfContentImageService;
    @Resource
    private MQHelperService mqHelperService;
    @Resource
    private AdminCrowdfundingInfoStatusBiz adminCrowdfundingInfoStatusBiz;
    @Resource
    private ApproveSnapshotService approveSnapshotService;
    @Resource
    private WonRecordClient wonRecordClient;

    @Autowired
    private CfUgcServiceFeignClient cfUgcServiceFeignClient;

    @Autowired
    private CfFinanceFeignClientV2 cfFinanceFeignClientV2;

    @Resource
    private AdminCrowdfundingInfoPayeeBiz adminCrowdfundingInfoPayeeBiz;

    @Override
    public Response<?> handleLogic(ApproveAuditParam param) {
        CrowdfundingInfo crowdfundingInfo = param.getCrowdfundingInfo();
        AdminApproveVo adminApproveVo = param.getAdminApproveVo();
        int userId = param.getUserId();
        Set<Integer> refuseDataTypeSet = param.getRefuseDataTypeSet();
        String commentText = param.getCommentText();
        int caseId = crowdfundingInfo.getId();
        String infoUuid = crowdfundingInfo.getInfoId();
        String refuseComment = "";
        boolean createFuWuWorkOrder = whetherCreateFuWu(adminApproveVo);

        AdminUserAccountModel userAccount = seaAccountClientV1.getValidUserAccountById(userId).getResult();
        CfInfoExt cfInfoExt = adminCfInfoExtBiz.getByInfoUuid(crowdfundingInfo.getInfoId());
        CfOperatingRecord cfOperatingRecord = crowdfundingDelegate.before(crowdfundingInfo, userId, userAccount.getName(), CfOperatingRecordEnum.Type.CASE_DATA_APPROVE, CfOperatingRecordEnum.Role.OPERATOR);

        long handleRecordId = qcAndRecord(crowdfundingInfo, adminApproveVo, refuseDataTypeSet, userId);

        boolean handleZhuDongOrder = handleZhuDongOrder(adminApproveVo, crowdfundingInfo, handleRecordId, userId);
        if (handleZhuDongOrder) {
            return NewResponseUtil.makeSuccess();
        }
        try {
            handlePassIds(adminApproveVo, crowdfundingInfo, userId, param.isPayeeRepeatWarn());
        } catch (Exception e) {
            log.error("CrowdfundingApproveController doApprove pass error!", e);
        }
        try {
            refuseComment = handleRefuseIds(refuseDataTypeSet, crowdfundingInfo, adminApproveVo, userId, commentText);
        } catch (Exception e) {
            log.error("CrowdfundingApproveController doApprove refuse error!", e);
        }
        CrowdfundingStatus crowdfundingStatus = crowdfundingOperationDelegate.checkDataUpdateCaseStatus(crowdfundingInfo, cfInfoExt);
        try {
            refuseComment = handleRefuseCommentEmpty(refuseComment, crowdfundingStatus);
            coreHandleApprove(param, crowdfundingStatus, refuseComment);
        } catch (Exception e) {
            log.error("", e);
        }
        log.info("客服后台log：do-approve status:{};dataStatus:{};infoStatus:{}", crowdfundingInfo.getStatus(), crowdfundingInfo.getDataStatus(), crowdfundingInfo.getEndTime());
        approveAfterSendMQ(caseId, userId, crowdfundingStatus, adminApproveVo, createFuWuWorkOrder, refuseComment, userAccount.getName(), crowdfundingInfo, cfOperatingRecord);

        Map<String, Object> result = Maps.newHashMap();
        result.put("dataTypeList", adminApproveService.getCrowdfundingInfoStatusInfo(infoUuid));
        return NewResponseUtil.makeSuccess(result);
    }

    /**
     * 兼容驳回内容为空情况
     *
     * @param refuseComment 驳回内容
     * @param crowdfundingStatus 案例状态
     * @return
     */
    private String handleRefuseCommentEmpty(String refuseComment, CrowdfundingStatus crowdfundingStatus) {
        if (crowdfundingStatus == CrowdfundingStatus.APPROVE_DENIED && StringUtils.isEmpty(refuseComment)) {
           return "增信驳回,案例基本材料全部通过";
        }
        return refuseComment;
    }

    /**
     * 材审审核后下游逻辑，发送mq
     * 后续有业务可以在如下两个mq的消费者加
     *
     * @param caseId 案例Id
     * @param userId 用户Id
     * @param crowdfundingStatus 案例状态
     * @param adminApproveVo 参数
     * @param createFuWuWorkOrder 是否创建主动服务工单
     * @param refuseComment 驳回内容
     * @param userName 操作人姓名
     * @param crowdfundingInfo 案例信息
     * @param cfOperatingRecord 操作记录
     */
    private void approveAfterSendMQ(int caseId, int userId, CrowdfundingStatus crowdfundingStatus, AdminApproveVo adminApproveVo,
                                    boolean createFuWuWorkOrder, String refuseComment, String userName, CrowdfundingInfo crowdfundingInfo, CfOperatingRecord cfOperatingRecord) {
        List<Integer> passIds = adminApproveVo.getPassIds();
        List<Integer> refuseIds = adminApproveVo.getRefuseIds();
        long yanhouWorkOrderId = adminApproveVo.getYanhouWorkOrderId();
        // 发送材料审核通过/驳回消息
        InfoApproveEvent payload = new InfoApproveEvent(caseId, crowdfundingStatus.value(), userId, passIds, refuseIds, createFuWuWorkOrder, adminApproveVo.getOrderType(), adminApproveVo.getFollowLabel(), yanhouWorkOrderId);
        Message<InfoApproveEvent> message = MessageBuilder.createWithPayload(payload)
                .setTags(CfClientMQTagCons.INFO_APPROVE_MSG)
                .addKey(CfClientMQTagCons.INFO_APPROVE_MSG, caseId)
                .build();
        adminCommonMessageHelperService.send(message);
        // 发送材审状态消息到企业微信
        sendApproveMsgToQyWx(crowdfundingStatus, caseId);

        ApproveAuditMsgV2 approveAuditMsgV2 = ApproveAuditMsgV2.builder()
                .adminApproveVo(adminApproveVo)
                .caseId(caseId)
                .userId(userId)
                .status(crowdfundingStatus.value())
                .refuseComment(refuseComment)
                .userName(userName)
                .beforeCrowdfundingInfo(crowdfundingInfo)
                .cfOperatingRecord(cfOperatingRecord)
                .build();
        Message<ApproveAuditMsgV2> messageV2 = MessageBuilder.createWithPayload(approveAuditMsgV2)
                .setTags(MQTagCons.INFO_APPROVE_MSG_V2)
                .addKey(MQTagCons.INFO_APPROVE_MSG_V2, caseId)
                .setTopic(MQTopicCons.CF)
                .build();
        adminCommonMessageHelperService.send(messageV2);
    }

    private void sendApproveMsgToQyWx(CrowdfundingStatus crowdfundingStatus, Integer caseId) {
        try {
            CrowdfundingInfo crowdfundingInfo = crowdfundingDelegate.getCaseInfoById(caseId);
            int verificationCount = cfUgcServiceFeignClient.countCrowdFundingVerificationByInfoUuid(crowdfundingInfo.getInfoId()).getData();
            String messageContent = "";
            String picUrl = null;
            if (Objects.equals(crowdfundingStatus, CrowdfundingStatus.CROWDFUNDING_STATED)) {
                //获取已筹金额
                com.shuidihuzhu.cf.finance.client.response.model.FeignResponse<DonationAmountInFenVo> feignResponse
                        = cfFinanceFeignClientV2.getDonationAmountInFen(crowdfundingInfo.getInfoId());
                DonationAmountInFenVo donationAmountInFenVo = Optional.ofNullable(feignResponse)
                        .filter(FeignResponse::ok)
                        .map(FeignResponse::getData)
                        .orElse(null);
                long amount = Optional.ofNullable(donationAmountInFenVo).map(DonationAmountInFenVo::getDonationAmountInFen).orElse(0);
                if (amount > 0 && verificationCount >= 3) {
                    String longUrl = "https://www.shuidichou.com/raise/withdraw/apply?infoUuid=" + crowdfundingInfo.getInfoId() + "&channel=qiwei";
                    String shortUrl = shortUrlDelegate.process(longUrl);
                    messageContent = "@筹款人【审核结果通知】您的材料审核已通过！可以随时申请提现了。如有需要点击这里申请：" + shortUrl;
                } else if (amount > 0) {
                    messageContent = "@筹款人【审核结果通知】您的材料审核已通过！3位好友证实就可以发起提现申请了。\n\uD83D\uDC49请将案例和下面的指引图片分享给亲友，让他们了解该如何帮您证实~";
                    picUrl = "https://image.shuidichou.com/img/ck/20250423/e3a6a1f0-6984-4275-a8e4-805ceb600e2a.jpeg";
                } else {
                    messageContent = "@筹款人【审核结果通知】您的材料审核已通过，但现在案例筹款金额为0，建议尽快多多转发，以便尽快获得筹款。";
                }
            }
            if (Objects.equals(crowdfundingStatus, CrowdfundingStatus.APPROVE_DENIED)) {
                // 发送企业微信群消息
                String materialLink = "https://www.shuidichou.com/raise/supplement/list?infoUuid=" + crowdfundingInfo.getInfoId() + "&channel=qiwei";
                String process = shortUrlDelegate.process(materialLink);
                messageContent = "@筹款人【审核结果通知】您的材料审核被驳回，请点击此处了解驳回原因并修改：" + process + "，我们的筹款顾问随时准备协助您，确保您的材料审核顺利进行。";
            }
            sendMqMessage(crowdfundingInfo, messageContent, picUrl);
        } catch (Exception e) {
            log.error("sendAproveMsgToQyWx error", e);
        }

    }

    private void sendMqMessage(CrowdfundingInfo info, String messageContent, String picUrl) {
        BdQywxGroupMessageModel messageModel = new BdQywxGroupMessageModel();
        messageModel.setMsgContent(messageContent);
        messageModel.setCaseId(info.getId());
        messageModel.setMsgType(QyWxMsgTypeEnum.MATERIAL_REVIEW.getCode());
        messageModel.setPicUr(picUrl);
        log.info("sendMqMessage send messageModel:{}", messageModel);
        Message message = new Message(MQTopicCons.CF, CfClientMQTagCons.GROWTHTOOL_BD_QYWX_GROUP_MSG,
                CfClientMQTagCons.GROWTHTOOL_BD_QYWX_GROUP_MSG + "_" + info.getId(), messageModel);
        adminCommonMessageHelperService.send(message);
    }

    /**
     * 材审审核核心逻辑：处理工单、修改状态、给用户发消息、保存日志等
     *
     * @param param 处理参数
     * @param crowdfundingStatus 案例状态
     * @param refuseComment 驳回内容
     */
    private void coreHandleApprove(ApproveAuditParam param, CrowdfundingStatus crowdfundingStatus, String refuseComment) {
        CrowdfundingInfo crowdfundingInfo = param.getCrowdfundingInfo();
        AdminApproveVo adminApproveVo = param.getAdminApproveVo();
        int userId = param.getUserId();
        List<Integer> refuseIds = adminApproveVo.getRefuseIds();
        List<Integer> passIds = adminApproveVo.getPassIds();
        String commentText = param.getCommentText();

        Date beginTime = new Date();
        Date endTime = DateUtils.addDays(beginTime, 30);
        crowdfundingInfoBiz.doApprove(crowdfundingInfo.getId(), crowdfundingStatus, beginTime, endTime);

        long workOrderId = adminApproveVo.getYanhouWorkOrderId();
        int orderType = adminApproveVo.getOrderType();


        if (crowdfundingStatus == CrowdfundingStatus.APPROVE_DENIED) {
            //只有勾选了才发消息
            if (adminApproveVo.isSendMsgFlag()) {
                adminApproveService.caseRefuseHandle(crowdfundingInfo, refuseComment);
            }
        } else if (crowdfundingStatus == CrowdfundingStatus.CROWDFUNDING_STATED) {
            adminApproveService.casePassHandle(crowdfundingInfo);
            crowdfundingDelegate.updateTimes(crowdfundingInfo.getInfoId(), CfTaskEnum.Rule.REPLENISH_MATERIAL);
        }

        updateWorkOrderCaseApprove(crowdfundingInfo, crowdfundingStatus, userId, refuseIds, passIds, commentText, refuseComment, workOrderId, orderType, adminApproveVo);
        if (adminApproveVo.getFollowLabel() > 0) {
            financeApproveService.addApprove(crowdfundingInfo, "内容", "接通状态：[" + label(adminApproveVo.getFollowLabel()) + "]" + adminApproveVo.getFollowMsg() + ":" + adminApproveVo.getFollowRemark(), userId);
        }
        if (adminApproveVo.getReasonCode() > 0) {
            financeApproveService.addApprove(crowdfundingInfo, "内容", "关单原因：[" + reason(adminApproveVo.getReasonCode()) + "]" + adminApproveVo.getReasonMsg() + ":" + adminApproveVo.getReasonRemark(), userId);
        }
        if (StringUtils.isNotEmpty(adminApproveVo.getFundUseRemark())) {
            approveRemarkOldService.add(param.getCrowdfundingInfo().getId(), param.getUserId(), adminApproveVo.getFundUseRemark());
        }
        // 释放锁
        if (crowdfundingStatus == CrowdfundingStatus.APPROVE_DENIED || crowdfundingStatus == CrowdfundingStatus.CROWDFUNDING_STATED) {
            approveControlService.releaseControl(adminApproveVo.getControlRecordId());
        }
    }

    private boolean whetherCreateFuWu(AdminApproveVo adminApproveVo) {
        long workOrderId = adminApproveVo.getYanhouWorkOrderId();
        int orderType = adminApproveVo.getOrderType();
        //只有处理的材审工单是处理中或稍后处理的时候才创建服务工单
        if (workOrderId > 0 && orderType == WorkOrderType.cailiao_5.getType()) {
            Response<WorkOrderVO> response = cfWorkOrderClient.getWorkOrderById(workOrderId);
            WorkOrderVO v = Optional.ofNullable(response).filter(Response::ok)
                    .map(Response::getData)
                    .filter(r -> (r.getHandleResult() == HandleResultEnum.doing.getType() || r.getHandleResult() == HandleResultEnum.later_doing.getType()))
                    .orElse(null);
            boolean createFuWuWorkOrder = Objects.nonNull(adminApproveVo.getCreateFuWuWorkOrder()) ? adminApproveVo.getCreateFuWuWorkOrder() : true;
            return v != null && createFuWuWorkOrder;
        }
        return false;
    }

    /**
     * 材审审核前，质检、快照、工单操作记录保存
     *
     * @param crowdfundingInfo 案例
     * @param adminApproveVo 审核参数
     * @param refuseDataTypeSet 驳回项
     * @param userId 操作人
     * @return
     */
    private long qcAndRecord(CrowdfundingInfo crowdfundingInfo, AdminApproveVo adminApproveVo, Set<Integer> refuseDataTypeSet, int userId) {
        long yanhouWorkOrderId = adminApproveVo.getYanhouWorkOrderId();
        int caseId = crowdfundingInfo.getId();
        List<Integer> passIds = adminApproveVo.getPassIds();

        // 审核前各项材料状态
        List<CrowdfundingInfoStatus> preInfoStatusList = adminCrowdfundingInfoStatusBiz.getByCaseId(caseId);

        // 保存工单处理记录
        OperationResult<WonRecord> handleRecordResp = wonRecordClient.create()
                .buildBasic(yanhouWorkOrderId, WonActionConst.ORDER_HANDLE_RECORD)
                .buildCaseId(caseId)
                .save();
        WonRecord handleRecord = handleRecordResp.getData();

        long handleRecordId = handleRecord.getId();
        // 保存处理快照
        approveSnapshotService.onApproveHandle(handleRecordId, caseId, yanhouWorkOrderId, adminApproveVo.getOrderType());

        // 用于处理材料审核子类型
        caseInfoApproveSubItemService.process(caseId, adminApproveVo, adminApproveVo.getSubItemStatus());

        // 所有需要质检的模块
        Set<Integer> types2NeedQc = preInfoStatusList.stream()
                .filter(type -> type.getStatus() == CrowdfundingInfoStatusEnum.SUBMITTED.getCode())
                .map(CrowdfundingInfoStatus::getType)
                .collect(Collectors.toSet());
        types2NeedQc.addAll(passIds);
        types2NeedQc.addAll(refuseDataTypeSet);
        wonRecordClient.updateExtByRecordId(WonExtUpdateByIdParam.create(handleRecordId, "types2NeedQc", types2NeedQc));
        return handleRecordId;
    }

    /**
     * 处理主动服务工单逻辑
     *
     * @param adminApproveVo 处理参数
     * @param crowdfundingInfo 案例信息
     * @param handleRecordId 工单日志id
     * @param userId 操作人
     * @return
     */
    private boolean handleZhuDongOrder(AdminApproveVo adminApproveVo, CrowdfundingInfo crowdfundingInfo, long handleRecordId, int userId) {
        List<Integer> passIds = adminApproveVo.getPassIds();
        List<Integer> refuseIds = adminApproveVo.getRefuseIds();
        long yanhouWorkOrderId = adminApproveVo.getYanhouWorkOrderId();
        if (CollectionUtils.isEmpty(passIds) && CollectionUtils.isEmpty(refuseIds)) {
            log.info("CrowdfundingApproveController doApprove passIds:{}， refuseIds:{}", passIds, refuseIds);
            //未提交结果的时候 有服务工单就要处理服务工单
            if (adminApproveVo.getOrderType() == WorkOrderType.cailiao_fuwu.getType() && yanhouWorkOrderId > 0) {
                CailiaoHandleOrderParam p = new CailiaoHandleOrderParam();
                p.setWorkOrderId(yanhouWorkOrderId);
                p.setOrderType(adminApproveVo.getOrderType());
                int handleResult = adminApproveVo.getHandleResult();
                p.setHandleResult(handleResult);
                p.setReasonCode(adminApproveVo.getReasonCode() + "");
                p.setReasonMsg(adminApproveVo.getReasonMsg());
                p.setFollowLabel(adminApproveVo.getFollowLabel() + "");
                p.setFollowMsg(adminApproveVo.getFollowMsg());
                p.setFollowRemark(adminApproveVo.getFollowRemark());
                p.setReasonRemark(adminApproveVo.getReasonRemark());
                Response response = workOrderClient.handleCailiao(p);
                log.info("handleCailiao param={} response={}", adminApproveVo, JSON.toJSONString(response));

                // 创建主动服务质检工单
                delayCreateQcZhuDongOrder(yanhouWorkOrderId, handleRecordId, handleResult, null);

                if (adminApproveVo.getReasonCode() > 0) {
                    financeApproveService.addApprove(crowdfundingInfo, "内容", "关单原因：[" + reason(adminApproveVo.getReasonCode()) + "]" + adminApproveVo.getReasonMsg() + ":" + adminApproveVo.getReasonRemark(), userId);
                }
            }

            if ((adminApproveVo.getOrderType() == WorkOrderType.cailiao_fuwu.getType() || adminApproveVo.getOrderType() == WorkOrderType.cailiao_5.getType()) && yanhouWorkOrderId > 0 && adminApproveVo.getFollowLabel() > 0) {
                financeApproveService.addApprove(crowdfundingInfo, "内容", "接通状态：[" + label(adminApproveVo.getFollowLabel()) + "]" + adminApproveVo.getFollowMsg() + ":" + adminApproveVo.getFollowRemark(), userId);
            }

            return true;
        }
        return false;
    }

    /**
     * 处理通过id不为空的情况
     *
     * @param adminApproveVo 审核参数
     * @param crowdfundingInfo 案例信息
     * @param userId 审核人
     */
    private void handlePassIds(AdminApproveVo adminApproveVo, CrowdfundingInfo crowdfundingInfo, int userId, boolean payeeRepeatWarn) {
        List<Integer> passIds = adminApproveVo.getPassIds();
        String infoUuid = crowdfundingInfo.getInfoId();
        int caseId = crowdfundingInfo.getId();
        if (CollectionUtils.isEmpty(passIds)) {
           return;
        }
        // 如果是通过图文 则将暂存区内容写入原库/admin/crowdfunding/approve/detail
        if (passIds.contains(CrowdfundingInfoDataStatusTypeEnum.BASE_INFO_SUBMIT.getCode())) {
            Response<Void> voidResponse = caseInfoApproveStageFeignClient.commitStage(caseId);
            log.info("stage write resp code:{}", voidResponse.getCode());
            cfContentImageService.close(userId, crowdfundingInfo);
        }
        // 某些材料通过审核后删除掉驳回的项
        Set<Integer> passTypeIds = Sets.newHashSet(passIds);

        // 更新通过的财审模块状态
        adminApproveService.updateDataStatusByInfoUuidAndType(infoUuid, CrowdfundingInfoStatusEnum.PASSED, passTypeIds);
        // 在这里进行飞书群通知

        // 在这里进行收款人异常飞书群通知
        feishuGroupNotification(passTypeIds, crowdfundingInfo, payeeRepeatWarn);

        adminApproveService.deleteRefuseByInfoUuidAndTypes(infoUuid, passTypeIds);
        // 加备注
        List<CfCommitVerifyItem> dataTypeList = AdminListUtil.getList(10,
                (start, size) -> cfCommitVerifyItemBiz.selectAll(start, size));
        String collect = dataTypeList.stream().filter(value -> passIds.contains(value.getId()))
                .map(CfCommitVerifyItem::getDescribe).collect(Collectors.joining(","));
        String comment = "审核通过的材料[" + collect + "];";
        financeApproveService.addApprove(crowdfundingInfo, BackgroundLogEnum.PASS.getMessage(), comment, userId);
    }

    /**
     * 收款人模块审核通过时通知飞书群
     */
    private void feishuGroupNotification(Set<Integer> passTypeIds, CrowdfundingInfo crowdfundingInfo, boolean payeeRepeatWarn) {
        // 日志前缀
        String logPrefix = "ApproveAuditServiceImpl.feishuGroupNotification";

        log.info("{} payeeRepeatWarn: {}", logPrefix, payeeRepeatWarn);

        if (crowdfundingInfo == null) {
            return;
        }

        Integer caseId = crowdfundingInfo.getId();
        log.info("{} caseId:{}", logPrefix, caseId);

        // 防止 passTypeIds 为空
        if (CollectionUtils.isEmpty(passTypeIds)) {
            return;
        }

        // 如果是多收款人，并且收款人模块审核通过了，则发送飞书群通知
        if (payeeRepeatWarn && passTypeIds.contains(CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT.getCode())) {
            String content = new StringBuilder()
                    .append("<at id=all></at>，案例ID ")
                    .append(caseId)
                    .append(" 收款人系统提示异常请复审，即刻执行。")
                    .toString();

            AlarmBotService.sentMarkDown("ea969363-0465-4e49-b20a-b6a3d1608344", content);
        }
    }

    /**
     * 处理驳回项不为空的情况
     *
     * @param refuseDataTypeSet 驳回项
     * @param crowdfundingInfo 案例信息
     * @param adminApproveVo 审核参数
     * @param userId 审核人
     * @param commentText 评论
     * @return
     */
    private String handleRefuseIds(Set<Integer> refuseDataTypeSet, CrowdfundingInfo crowdfundingInfo, AdminApproveVo adminApproveVo, int userId, String commentText) {
        String infoUuid = crowdfundingInfo.getInfoId();
        List<Integer> refuseIds = adminApproveVo.getRefuseIds();
        String refuseComment = "";
        if (CollectionUtils.isEmpty(refuseDataTypeSet)) {
            return refuseComment;
        }
        adminApproveService.deleteRefuseByInfoUuidAndTypes(infoUuid, refuseDataTypeSet);
        refuseComment = adminApproveService.refuseReasonHandle(refuseIds, commentText, crowdfundingInfo, adminApproveVo.getSuggestModifyDetails());
        adminCrowdfundingOperationBiz.addRefuseCount(userId, 1, infoUuid);

        // 加备注
        financeApproveService.addApprove(crowdfundingInfo, BackgroundLogEnum.REFUSE.getMessage(), refuseComment, userId);
        return refuseComment;
    }

    private void delayCreateQcZhuDongOrder(long yanhouWorkOrderId, long handleRecordId, int handleResult, @Nullable CrowdfundingStatus crowdfundingStatus) {
        QcZhuDongCreateParam qcZhuDongCreateParam = new QcZhuDongCreateParam();
        qcZhuDongCreateParam.setWorkOrderId(yanhouWorkOrderId);
        Integer submitCrowdfundingStatus = Optional.ofNullable(crowdfundingStatus).map(CrowdfundingStatus::value).orElse(-1);
        qcZhuDongCreateParam.setSubmitCrowdfundingStatus(submitCrowdfundingStatus);
        qcZhuDongCreateParam.setSubmitHandleResult(handleResult);
        qcZhuDongCreateParam.setHandleRecordId(handleRecordId);
        mqHelperService.builder()
                .setTags(MQTagCons.WORK_ORDER_HANDLE_ZHU_DONG)
                .addKey(MQTagCons.WORK_ORDER_HANDLE_ZHU_DONG, yanhouWorkOrderId, System.currentTimeMillis())
                .setDelayTime(30_000)
                .setPayload(JSON.toJSON(qcZhuDongCreateParam))
                .send();
    }
    
    //更改材料审核工单的状态
    private void updateWorkOrderCaseApprove(CrowdfundingInfo crowdfundingInfo, CrowdfundingStatus crowdfundingStatus, int userId, List<Integer> refuseId, List<Integer> passIds, String comment, String refuseComment,
                                            long workOrderId, int orderType, AdminApproveVo adminApproveVo) {
        //获取审核材料当前对应的工单
        //首先查询案例对应的材料审核工单
        int caseId = crowdfundingInfo.getId();
        log.info("handleCailiao caseId={} orderType={},workOrderId={},crowdfundingStatus={}", caseId, orderType, workOrderId, crowdfundingStatus);
        if (workOrderId == 0) {
            cfWorkOrderClient.closeOrderBycaseIdAndType(caseId, WorkOrderType.cailiao_fuwu.getType(), HandleResultEnum.exception_done.getType(),
                    userId, "让他去修改关闭");
        }

        if (orderType == WorkOrderType.cailiao_fuwu.getType() && workOrderId > 0) {
            CailiaoHandleOrderParam param = new CailiaoHandleOrderParam();
            param.setWorkOrderId(workOrderId);
            param.setOrderType(orderType);
            param.setHandleResult(adminApproveVo.getHandleResult());
            param.setReasonCode(adminApproveVo.getReasonCode() + "");
            param.setReasonMsg(adminApproveVo.getReasonMsg());
            param.setFollowLabel(adminApproveVo.getFollowLabel() + "");
            param.setFollowMsg(adminApproveVo.getFollowMsg());
            param.setFollowRemark(adminApproveVo.getFollowRemark());
            param.setReasonRemark(adminApproveVo.getReasonRemark());
            Response response = workOrderClient.handleCailiao(param);
            log.info("handleCailiao param={} response={}", param, JSON.toJSONString(response));
        } else {
            if (workOrderId > 0 && orderType > 0 && CollectionUtils.isNotEmpty(refuseId)) {
                //只要有被驳回的材料审核项，就关闭延后审核工单
                CailiaoHandleOrderParam param = new CailiaoHandleOrderParam();
                param.setWorkOrderId(workOrderId);
                param.setOrderType(orderType);
                param.setHandleResult(HandleResultEnum.audit_reject.getType());
                param.setMaterialAuditStatus(com.shuidihuzhu.client.cf.workorder.model.enums.AuditStatus.AUDIT_REFUSE.getCode());
                Response response = workOrderClient.handleCailiao(param);
                log.info("handleCailiao param={} response={}", param, JSON.toJSONString(response));
            } else if (workOrderId > 0 && orderType > 0 && crowdfundingStatus == CrowdfundingStatus.CROWDFUNDING_STATED) {
                //材料审核项全部通过，关闭延后审核工单
                CailiaoHandleOrderParam param = new CailiaoHandleOrderParam();
                param.setWorkOrderId(workOrderId);
                param.setOrderType(orderType);
                param.setHandleResult(HandleResultEnum.audit_pass.getType());
                param.setMaterialAuditStatus(AuditStatus.AUDIT_PASS.getCode());
                Response response = workOrderClient.handleCailiao(param);
                log.info("handleCailiao param={} response={}", param, JSON.toJSONString(response));
            }
        }

        String result = "通过";
        //判断条件 如果驳回id集合不为空 否则为审核通过
        if (!CollectionUtils.isEmpty(refuseId)) {
            //驳回
            result = "驳回";
        }

        if (CollectionUtils.isNotEmpty(refuseId)) {
            UserComment userComment = new UserComment(UserCommentSourceEnum.CASE_APPROVE, workOrderId,
                    UserCommentSourceEnum.CommentType.CASE_APPROVE_REJECT, userId, UserCommentSourceEnum.CommentType.CASE_APPROVE_REJECT.getDesc(), "", "材料审核驳回时间");
            userCommentBiz.insert(userComment);
        }

        if (CollectionUtils.isNotEmpty(passIds)) {
            UserComment userComment = new UserComment(UserCommentSourceEnum.CASE_APPROVE, workOrderId,
                    UserCommentSourceEnum.CommentType.CASE_APPROVE_PASS, userId, UserCommentSourceEnum.CommentType.CASE_APPROVE_PASS.getDesc(), "", "材料审核通过时间");
            userCommentBiz.insert(userComment);
        }

        //工单结束后发送企业微信给线下BD
        sendMsg2BD(result, comment, crowdfundingInfo, refuseComment);

    }

    private void sendMsg2BD(String result, String comment, CrowdfundingInfo crowdfundingInfo, String refuseComment) {

        String infoUuid = crowdfundingInfo.getInfoId();
        int caseId = crowdfundingInfo.getId();
        try {
            CfInfoExt cfInfoExt = adminCfInfoExtBiz.getByInfoUuid(infoUuid);
            if (cfInfoExt == null) {
                return;
            }
            String volunteerUniqueCode = cfInfoExt.getVolunteerUniqueCode();
            //不是志愿者不发送
            if (StringUtils.isEmpty(volunteerUniqueCode)) {
                return;
            }

            CrowdfundingAuthor author = adminCrowdfundingAuthorBiz.get(caseId);

            if (author == null) {
                log.info("author == null caseId={}", caseId);
                return;
            }
            UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByUserId(crowdfundingInfo.getUserId());

            String mobile = shuidiCipher.decrypt(userInfoModel.getCryptoMobile());
            String caseUrl = shortUrlDelegate.process(GeneralConstant.caseBaseUrl + crowdfundingInfo.getInfoId());
            WorkWeiXinContentBuilder cb = WorkWeiXinContentBuilder.create()
                    .subject("【水滴筹】案例材料审核处理结果")
                    .payload("案例名称", "<a href=\"" + caseUrl + "\">" + crowdfundingInfo.getTitle() + "</a>")
                    .payload("发起人手机号", CrowdfundingUtil.getTelephoneMask(mobile))
                    .payload("患者姓名", CrowdfundingUtil.getNameMask(author.getName()))
                    .payload("审核结果", result);

            if ("驳回".equals(result)) {
                cb.payload("驳回原因", refuseComment.replaceAll("\\[([^]]+)]", ""));
            }
            cb.payload("审核时间", DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));

            String content = cb.build();
            OpResult op = volunteerHelpService.send(volunteerUniqueCode, content);

            log.info("sendByUser volunteerUniqueCode={},code={},msg={}", volunteerUniqueCode, op.getErrorCode(), op.getMessage());
        } catch (Exception e) {
            log.error("sendMsg2BD error caseId={}", caseId, e);
        }
    }

    /**
     * 1.患者去世、2.筹款人要求退款、3.案例超时未提交材审或联系不上用户、4.其他
     *
     * @param a
     * @return
     */
    private String reason(int a) {
        switch (a) {
            case 1:
                return "患者去世";
            case 2:
                return "筹款人要求退款";
            case 3:
                return "案例超时未提交材审或联系不上用户";
            case 4:
                return "其他";
            default:
                return "";
        }
    }

    /**
     * 1: 其他
     * 2. 约定时间沟通、
     * 3. 首呼未呼通、
     * 4. 1次复拨未呼通、
     * 5. 2次复拨未呼通、
     * 6. 已完成指导、
     * 7. 患者去世待处理、
     * 8. 要求退款
     *
     * @param a
     * @return
     */
    private String label(int a) {
        switch (a) {
            case 1:
                return "其他";
            case 2:
                return "约定时间沟通";
            case 3:
                return "首呼未呼通";
            case 4:
                return "1次复拨未呼通";
            case 5:
                return "2次复拨未呼通";
            case 6:
                return "已完成指导";
            case 7:
                return "患者去世待处理";
            case 8:
                return "要求退款";
            case 9:
                return "驳回未致电";
            default:
                return "";
        }
    }
    
}
