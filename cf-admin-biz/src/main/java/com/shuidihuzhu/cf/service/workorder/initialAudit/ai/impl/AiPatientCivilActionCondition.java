package com.shuidihuzhu.cf.service.workorder.initialAudit.ai.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.dao.crowdfunding.CfAiMaterialsDao;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.BaseInfoTemplateConst;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.ai.AiConditionEnum;
import com.shuidihuzhu.cf.model.crowdfunding.ai.AiRules;
import com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterials;
import com.shuidihuzhu.cf.model.crowdfunding.ai.LayOutField;
import com.shuidihuzhu.cf.service.workorder.initialAudit.ai.AiSingleCondition;
import com.shuidihuzhu.cipher.ShuidiCipher;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @Author: wangpeng
 * @Date: 2021/9/8 20:09
 * @Description:
 */
@Service("aiPatientCivilActionCondition")
public class AiPatientCivilActionCondition implements AiSingleCondition {

    @Resource
    private CfAiMaterialsDao cfAiMaterialsDao;

    @Resource
    private IRiskDelegate riskDelegate;

    @Autowired
    private ShuidiCipher shuidiCipher;

    @Override
    public AiRules check(int caseId) {
        AiRules aiRules = new AiRules();
        CfAiMaterials cfAiMaterials = cfAiMaterialsDao.getByCaseId(caseId, CfAiMaterials.jType);
        if (Objects.isNull(cfAiMaterials)) {
            return aiRules;
        }
        String patientCivilAction = Optional.of(cfAiMaterials)
                .map(CfAiMaterials::getFields)
                .orElse(Lists.newArrayList())
                .stream()
                .filter(f -> StringUtils.equals(f.getFieldKey(), "patientCivilAction"))
                .map(LayOutField::getFieldValue)
                .findAny()
                .orElse("");
        if (StringUtils.isEmpty(patientCivilAction)) {
            return aiRules;
        }
        CfFirsApproveMaterial cfFirsApproveMaterial = riskDelegate.getCfFirsApproveMaterialByInfoId(caseId);
        if (Objects.isNull(cfFirsApproveMaterial)) {
            return aiRules;
        }

        String no = String.valueOf(AiConditionEnum.fou.getCode());
        if (StringUtils.equals(patientCivilAction, no) && Objects.equals(BaseInfoTemplateConst.CfBaseInfoRelationshipEnum.SELF.getCode(), cfFirsApproveMaterial.getUserRelationTypeForC())) {
            aiRules.setValue("553");
            return aiRules;
        }
        String patientIdCard = shuidiCipher.decrypt(cfFirsApproveMaterial.getPatientCryptoIdcard());
        String selfIdCard = shuidiCipher.decrypt(cfFirsApproveMaterial.getSelfCryptoIdcard());
        if (StringUtils.equals(patientCivilAction, no) && !Objects.equals(BaseInfoTemplateConst.CfBaseInfoRelationshipEnum.SELF.getCode(), cfFirsApproveMaterial.getUserRelationTypeForC()) && StringUtils.equals(patientIdCard, selfIdCard)) {
            aiRules.setValue("554");
            return aiRules;
        }
        return aiRules;
    }
}
