package com.shuidihuzhu.cf.service.report.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.IAdminCredibleInfoService;
import com.shuidihuzhu.cf.enhancer.model.response.EhResponse;
import com.shuidihuzhu.cf.enhancer.utils.EhResponseUtils;
import com.shuidihuzhu.cf.enums.crowdfunding.AddTrustAuditStatusEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CredibleTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfCredibleInfoDO;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.report.schedule.ReportPendingEntryVO;
import com.shuidihuzhu.cf.service.report.ReportPendingEntryService;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.msg.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RefreshScope
public class ReportPendingEntryServiceImpl implements ReportPendingEntryService {

    @Autowired
    private IAdminCredibleInfoService credibleInfoService;

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Override
    public EhResponse<List<ReportPendingEntryVO>> getListByOperatorId(int adminUserId) {
        List<CfCredibleInfoDO> cfCredibleInfoDOS = credibleInfoService.getListByOperatorId(adminUserId, CredibleTypeEnum.HELP_PROVE.getKey());
        if(CollectionUtils.isEmpty(cfCredibleInfoDOS)){
            return EhResponseUtils.success();
        }
        cfCredibleInfoDOS = cfCredibleInfoDOS.stream().filter(v -> v.getAuditStatus() == AddTrustAuditStatusEnum.UN_SUBMITTED.getCode() ||
                v.getAuditStatus() == AddTrustAuditStatusEnum.SUBMITTED.getCode()).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(cfCredibleInfoDOS)){
            return EhResponseUtils.success();
        }

        List<ReportPendingEntryVO> reportPendingEntryVOList = Lists.newArrayList();
        for (CfCredibleInfoDO credibleInfoDO:cfCredibleInfoDOS) {
            Response<WorkOrderVO> response =  cfWorkOrderClient.getLastWorkOrderByTypes(credibleInfoDO.getCaseId(),WorkOrderType.REPORT_TYPES);
            WorkOrderVO workOrderVO = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(null);
            if(Objects.isNull(workOrderVO)){
                continue;
            }

            if (workOrderVO.getOperatorId() != adminUserId) {
                continue;
            }

            CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(workOrderVO.getCaseId());
            if(Objects.isNull(crowdfundingInfo)){
                continue;
            }
            ReportPendingEntryVO reportPendingEntryVO = ReportPendingEntryVO.builder()
                    .id(credibleInfoDO.getId())
                    .caseId(workOrderVO.getCaseId())
                    .infoUuid(crowdfundingInfo.getInfoId())
                    .orderResult(workOrderVO.getHandleResult())
                    .orderResultDesc(HandleResultEnum.getFromType(workOrderVO.getHandleResult()).getShowMsg())
                    .orderType(workOrderVO.getOrderType())
                    .targetTime(DateUtil.getDate2LStr(credibleInfoDO.getSubmitTime()))
                    .workOrderId(workOrderVO.getWorkOrderId())
                    .workOrderStatus(1)
                    .build();
            reportPendingEntryVOList.add(reportPendingEntryVO);
        }

        List<ReportPendingEntryVO> vos = reportPendingEntryVOList.stream()
                .sorted(Comparator.comparing(ReportPendingEntryVO::getTargetTime))
                .collect(Collectors.toList());
        return EhResponseUtils.success(vos);
    }

    @Override
    public EhResponse<Boolean> judge(long id) {
        CfCredibleInfoDO credibleInfoDO = credibleInfoService.queryById(id);

        if (Objects.isNull(credibleInfoDO)) {
            return EhResponseUtils.failWithMessage("举报待录入未查询到");
        }

        if (credibleInfoDO.getAuditStatus() == AddTrustAuditStatusEnum.PASSED.getCode()) {
            return EhResponseUtils.success(false, "举报待录入已经审核完成");
        }

        return EhResponseUtils.success(true);
    }
}
