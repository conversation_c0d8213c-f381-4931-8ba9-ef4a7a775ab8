package com.shuidihuzhu.cf.service.sensitive.adapter;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.CfSensitiveWordRecordEnum;
import com.shuidihuzhu.cf.enums.risk.UgcTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.deliver.CrowdfundingOrderDeliver;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfSensitiveWordRecordVo;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2019-03-14  15:01
 */
@Service
public class SensitiveOrderAdapter implements ISensitiveAdapter<CrowdfundingOrderDeliver> {


    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Override
    public String getContent(CrowdfundingOrderDeliver v) {
        return v.getComment();
    }

    @Override
    public CfSensitiveWordRecordVo buildRecord(CrowdfundingOrderDeliver v, String hitWord) {
        return buildRecordByOrder(v, hitWord);
    }

    @Override
    public long getBizId(CrowdfundingOrderDeliver data) {
        return data.getId();
    }

    @Override
    public int getCaseId(CrowdfundingOrderDeliver data) {
        return data.getCrowdfundingId();
    }

    @Override
    public UgcTypeEnum getUgcTypeEnum() {
        return UgcTypeEnum.ORDER;
    }

    @Override
    public CfSensitiveWordRecordEnum.BizType getSensitiveRecordBizType(CrowdfundingOrderDeliver order) {
        return CfSensitiveWordRecordEnum.BizType.ORDER;
    }

    @Override
    public long getUserId(CrowdfundingOrderDeliver data) {
        return data.getUserId();
    }

    @Override
    public String getUniquelyIdentifies(CrowdfundingOrderDeliver crowdfundingOrder) {
        return crowdfundingOrder.getUniquelyIdentifies();
    }

    @Override
    public boolean needAiCheck(CrowdfundingOrderDeliver data) {
        return false;
    }

    @NotNull
    private CfSensitiveWordRecordVo buildRecordByOrder(CrowdfundingOrderDeliver order, String sensitiveWord) {
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(order.getCrowdfundingId());
        CfSensitiveWordRecordVo cfSensitiveWordRecord = new CfSensitiveWordRecordVo();
        fillCaseInfo(order, cfSensitiveWordRecord, crowdfundingInfo);
        cfSensitiveWordRecord.setUserId(order.getUserId());
        cfSensitiveWordRecord.setBizType(getSensitiveRecordBizType(order).value());
        cfSensitiveWordRecord.setBizId(order.getId());
        cfSensitiveWordRecord.setParentBizId(-1);
        cfSensitiveWordRecord.setBizTime(new Timestamp(System.currentTimeMillis()));
        cfSensitiveWordRecord.setSensitiveWord(sensitiveWord);
        cfSensitiveWordRecord.setContent(order.getComment());
        return cfSensitiveWordRecord;
    }

}
