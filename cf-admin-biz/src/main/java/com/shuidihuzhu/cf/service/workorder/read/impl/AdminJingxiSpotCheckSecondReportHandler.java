package com.shuidihuzhu.cf.service.workorder.read.impl;

import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class AdminJingxiSpotCheckSecondReportHandler extends AdminJingxiSpotCheckFirstReportHandler{
    @Override
    public WorkOrderType getWorkOrderType() {
        return WorkOrderType.up_grade_second;
    }
}
