package com.shuidihuzhu.cf.service.sensitive.checker;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.enums.crowdfunding.CfSensitiveWordRecordEnum;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordResult;
import com.shuidihuzhu.cf.service.sensitive.adapter.ISensitiveAdapter;
import com.shuidihuzhu.cf.service.sensitive.processor.SensitiveProcessService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2019-04-11  14:46
 *
 * 敏感时期 动态评论 未命中的全部人工审核
 */
@Service
public class VerificationCommentChecker implements ISensitiveChecker {

    @Resource
    private SensitiveProcessService sensitiveProcessService;

    @Override
    public AdminWorkOrderConst.Task getTask() {
        return AdminWorkOrderConst.Task.SENSITIVE_OTHERS;
    }

    @Override
    public <T> OpResult<RiskWordResult> isHit(T data, ISensitiveAdapter<T> adapter) {
        RiskWordResult result = new RiskWordResult(isPassed(data, adapter), adapter.getContent(data), Lists.newArrayList());
        return OpResult.createSucResult(result);
    }

    private <T> boolean isPassed(T data, ISensitiveAdapter<T> adapter) {
        // 仅动态评论
        CfSensitiveWordRecordEnum.BizType sensitiveRecordBizType = adapter.getSensitiveRecordBizType(data);
        return sensitiveRecordBizType != CfSensitiveWordRecordEnum.BizType.COMMENT_PROGRESS;
    }
}
