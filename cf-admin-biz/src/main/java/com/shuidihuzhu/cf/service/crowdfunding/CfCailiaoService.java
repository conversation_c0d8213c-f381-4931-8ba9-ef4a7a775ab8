package com.shuidihuzhu.cf.service.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoStatusBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingOperationBiz;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.dao.workorder.WorkOrderFollowLabelDao;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingUserDelegate;
import com.shuidihuzhu.cf.domain.workorder.FollowLabelDO;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingOperationEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingTreatment;
import com.shuidihuzhu.cf.model.event.CailiaoCondition;
import com.shuidihuzhu.cf.model.event.CailiaoConditionEvent;
import com.shuidihuzhu.cf.model.event.CailiaoConditionResult;
import com.shuidihuzhu.cf.risk.client.rpc.DiseaseClassifyFeignClientV2;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistCallPhaseEnum;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseClassifyVOV2;
import com.shuidihuzhu.cf.service.approve.CfMultipleCaseRiskService;
import com.shuidihuzhu.client.cf.search.client.CfSearchClient;
import com.shuidihuzhu.client.cf.workorder.CfCailiaoWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.CailiaoDealResult;
import com.shuidihuzhu.client.cf.workorder.model.CailiaoWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderMQ;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderLevel;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnCloudPlatform;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2020/3/18
 */
@Service
@Slf4j
@RefreshScope
public class CfCailiaoService {

    @Autowired
    private CfCailiaoWorkOrderClient workOrderClient;
    @Autowired(required = false)
    private Producer producer;
    @Autowired
    private CfAdminLimitService limitService;
    @Autowired
    private AdminCrowdfundingOperationBiz adminCrowdfundingOperationBiz;
    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;
    @Autowired
    private DiseaseClassifyFeignClientV2 diseaseClassifyClientV2;
    @Autowired
    private ICrowdfundingUserDelegate crowdfundingUserDelegate;
    @Autowired
    private AdminCrowdfundingInfoStatusBiz infoStatusBiz;
    @Autowired
    private AdminCrowdfundingInfoBiz infoBiz;
    @Autowired
    private CfWorkOrderClient orderClient;
    @Resource
    private WorkOrderFollowLabelDao workOrderFollowLabelDao;
    @Autowired
    private CfMultipleCaseRiskService cfMultipleCaseRiskService;
    @Value("#{'${apollo.material.target.disease:}'.split(',')}")
    private List<String> targetDiseases = Lists.newArrayList("肺癌", "乳腺癌", "胃癌", "肝癌", "肠癌");
    @Value("${apollo.material.ext.collect:0}")
    private int extMaterialCollect = 0;
    @Value("${apollo.material.day.begin.second:32400}")
    private long dayBeginSecond = 9 * 3600 ;
    @Value("${apollo.material.day.end.second:77400}")
    private long dayEndSecond = 21 * 3600  + 1800 ;

    @Value("${apollo.material.collect.case.create.time:1623221168575}")
    private long caseCreateTime = 1623221168575l;

    @Value("${apollo.need.mark.material.type:1}")
    private int needMarkMaterialType = 1;

    public static void main(String[] args) {
        System.out.println(System.currentTimeMillis());
    }
    // 重服务 和 普通服务的工单
    public void createWorkOrder(CailiaoConditionEvent event){
       log.info("createWorkOrder event={}",event);
       CailiaoWorkOrder c = new CailiaoWorkOrder();
       c.setCaseId(event.getCaseId());
       c.setOrderType(getMaterialOrderType(event.getCaseId()));
       c.setOrderlevel(event.getOrderLevel());

       if (CollectionUtils.isNotEmpty(event.getResults())){
           c.setConditionResult(JSON.toJSONString(event.getResults()));
       }

       if (StringUtils.isEmpty(event.getComment())){
           c.setOperComment(event.getComment());
       }

       createWorkOrder(c);
   }

   public int createFuwuWorkOrder(int caseId,long flowOrderId,String comment, int followLabel){


       CrowdfundingOperation crowdfundingOperation = adminCrowdfundingOperationBiz.getByCaseId(caseId);

       if (crowdfundingOperation.getFuwuType() != 1) {
           return 1001;
       }

       if (crowdfundingOperation.getOperation() == CrowdfundingOperationEnum.NEVER_PROCESSING.value()
               || crowdfundingOperation.getOperation() == CrowdfundingOperationEnum.DEFER_CONTACT.value()){
           return 1002;
       }
       if (flowOrderId > 0){
           Response<WorkOrderVO> response = cfWorkOrderClient.getLastWorkOrderByTypes(caseId,Lists.newArrayList(WorkOrderType.cailiao_4.getType(),WorkOrderType.cailiao_5.getType()));
           WorkOrderVO vo = Optional.ofNullable(response)
                   .filter(Response::ok)
                   .map(Response::getData)
                   .filter(r-> HandleResultEnum.unDoResult().stream().collect(Collectors.toSet()).contains(r.getHandleResult()))
                   .orElse(null);
           if (vo != null){
               return 1003;
           }
       }

       CailiaoWorkOrder c = new CailiaoWorkOrder();

       c.setCaseId(caseId);
       c.setOrderType(WorkOrderType.cailiao_fuwu.getType());
       c.setOrderlevel(OrderLevel.C.getType());
       if (flowOrderId > 0){
           c.setFlowOrderId(flowOrderId+"");
       }
       c.setOperComment(comment);
       Response<WorkOrderVO> workOrderVOResponse = orderClient.getLastWorkOrderByTypes(caseId, Collections.singletonList(WorkOrderType.cailiao_5.getType()));
       Long operatorId = Optional.ofNullable(workOrderVOResponse)
               .map(Response::getData)
               .map(WorkOrderVO::getOperatorId)
               .orElse(0L);
       if (operatorId > 0) {
           c.setVonPriorAssignOperatorId(operatorId);
       }

       Long workOrderId = createWorkOrder(c);
       // 普通服务工单生成主动服务工单打跟进标签
       if (workOrderId != 0 && c.getOrderType() == WorkOrderType.cailiao_fuwu.getType()) {
           generateFollowTag(workOrderId, followLabel);
       }

       return 0;
   }

    public Long createWorkOrder(CailiaoWorkOrder c){

        log.info("CailiaoWorkOrder ={} ",c);
        int caseId = c.getCaseId();
        int orderType = c.getOrderType();

        if(limitService.caseInLimit(caseId, BlacklistCallPhaseEnum.SUBMIT_MATERIAL_REVIEW)){
            return 0L;
        }

        Response<Long> response = workOrderClient.createCailiao(c);
        log.info("createCailiao c={} response={}", c, JSON.toJSONString(response));
        //工单已经存在不用重试
        if (response == null || (response.notOk() && response.getCode() != 1006)) {
            sendMQ(caseId, orderType);
        }
        long workOrderId = response == null || response.getData() == null ? 0L : response.getData();
        cfMultipleCaseRiskService.judgeAmountRisk(workOrderId, caseId);
        return Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(0L);

    }

    private void generateFollowTag(Long workOrderId, int followLabel) {

        log.info("generateFollowTag workOrderId:{} followLabel:{}", workOrderId, followLabel);
        FollowLabelDO followLabelDO = workOrderFollowLabelDao.selectByWorkOrderId(workOrderId);
        if (Objects.isNull(followLabelDO)) {
            followLabelDO = new FollowLabelDO();
            followLabelDO.setFollowLabel(followLabel);
            followLabelDO.setWorkOrderId(workOrderId);
            workOrderFollowLabelDao.addFollowLabel(followLabelDO);
        } else if (followLabel != 0) {
            workOrderFollowLabelDao.updateByWorkOrderId(followLabel, workOrderId);
        }

    }

    public void sendDealMQ( int caseId, int cfDealWithStatus) {
        CailiaoDealResult c = new CailiaoDealResult();
        c.setCaseId(caseId);
        c.setDealResult(cfDealWithStatus);
        Message msg = new Message(MQTopicCons.CF, WorkOrderMQ.cailiao_deal_result_msg, caseId+"",c);
        MessageResult result = producer.send(msg);
        log.info("材料处理消息. msg:{}, result:{}", msg, result);
    }

    private void sendMQ(int caseId,int orderType){

        CailiaoWorkOrder cailiaoWorkOrder  = new CailiaoWorkOrder();
        cailiaoWorkOrder.setCaseId(caseId);
        cailiaoWorkOrder.setOrderType(orderType);

        Message msg =  new Message(MQTopicCons.CF, WorkOrderMQ.auto_cailiao_create_mq,
                "" + caseId,
                cailiaoWorkOrder, DelayLevel.S30);

        MessageResult result = producer.send(msg);

        log.info("cailiaoWorkOrder caseId={} result={}",caseId,result);
    }

    public int getMaterialOrderType(int caseId) {

        return isExtMaterialCollect() && isInDayTime() && isInTargetDisease(caseId)
                ? WorkOrderType.cailiao_4.getType() : WorkOrderType.cailiao_5.getType();
    }

    public boolean isNeedMarkMaterialType() {
        return needMarkMaterialType == 1;
    }

    public boolean isExtMaterialCollect() {
        return extMaterialCollect == 1;
    }

    private boolean isInDayTime() {
        int curSecond = new DateTime().getSecondOfDay();
        return curSecond >= dayBeginSecond && curSecond <= dayEndSecond;
    }

    private boolean isInTargetDisease(int caseId) {

        CrowdfundingTreatment treatment = crowdfundingUserDelegate.getCrowdfundingTreatment(caseId);
        if (treatment == null || StringUtils.isBlank(treatment.getDiseaseName())) {
            log.info("不能找到案例上的疾病名称 caseId:{} result:{}", caseId, JSON.toJSONString(treatment));
            return false;
        }
        String disease = treatment.getDiseaseName();
        Response<List<DiseaseClassifyVOV2>> diseaseClassifys = diseaseClassifyClientV2.diseaseNorm(Lists.newArrayList(disease));
        log.info("调用疾病归一的接口。param:{} result:{}", disease, JSON.toJSONString(diseaseClassifys));
        if (diseaseClassifys == null || CollectionUtils.isEmpty(diseaseClassifys.getData())) {
            for (String target : targetDiseases) {
                if (disease.contains(target)) {
                    return true;
                }
            }

            return false;
        }

        List<String> normList = diseaseClassifys.getData().get(0).getNorm();
        for (String norm : normList) {
            for (String target : targetDiseases) {
                if (norm.contains(target)) {
                    return true;
                }
            }
        }

        return false;
    }

    public boolean needCollectElement(String infoUuid, long workOrderId) {

        CrowdfundingInfo caseInfo = infoBiz.getFundingInfo(infoUuid);
        if (caseInfo == null || caseInfo.getCreateTime() == null || caseInfo.getCreateTime().getTime() < caseCreateTime) {
            log.info("案例的创建时间小于给定值 caseId:{} caseInfo:{}", infoUuid, JSON.toJSONString(caseInfo));
            return false;
        }

        Response<WorkOrderVO> result = orderClient.getWorkOrderById(workOrderId);
        return result != null && result.getData() != null
                && result.getData().getOrderType() == WorkOrderType.cailiao_4.getType();
    }


}
