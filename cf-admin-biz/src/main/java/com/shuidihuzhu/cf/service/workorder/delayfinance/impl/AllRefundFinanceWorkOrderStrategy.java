package com.shuidihuzhu.cf.service.workorder.delayfinance.impl;

import com.shuidihuzhu.cf.finance.model.vo.refund.CfRefundApplyVo;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.cf.workorder.read.WorkOrderReadFeignClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 筹款人申请退款
 *
 * <AUTHOR>
 * @since 2022-12-01 2:19 下午
 **/
@Service
public class AllRefundFinanceWorkOrderStrategy extends AbstractFinanceWorkOrderStrategy<CfRefundApplyVo> {

    @Override
    public List<CfRefundApplyVo> getBusinessExt(List<Long> financeBusinessIds) {
        return this.financeWorkOrderDelegate.getRefundApplyVo(financeBusinessIds);
    }

    @Override
    public int orderType() {
        return WorkOrderType.all_refund_audit.getType();
    }
}
