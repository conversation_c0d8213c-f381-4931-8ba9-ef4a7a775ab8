package com.shuidihuzhu.cf.service.workorder.imagePublic.impl;

import com.shuidihuzhu.cf.client.adminpure.model.caseinfo.CfCasePublicInfo;
import com.shuidihuzhu.cf.client.adminpure.model.caseinfo.CfCasePublicInfoImageAIRecord;
import com.shuidihuzhu.cf.dao.crowdfunding.CfCasePublicInfoDao;
import com.shuidihuzhu.cf.dao.crowdfunding.CfCasePublicInfoImageAIRecordDao;
import com.shuidihuzhu.cf.service.workorder.imagePublic.CfCasePublicInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2021/8/20 16:36
 * @Description:
 */
@Service
public class CfCasePublicInfoServiceImpl implements CfCasePublicInfoService {

    @Resource
    private CfCasePublicInfoDao cfCasePublicInfoDao;

    @Resource
    private CfCasePublicInfoImageAIRecordDao cfCasePublicInfoImageAIRecordDao;


    @Override
    public int addCasePublicInfo(CfCasePublicInfo cfCasePublicInfo) {
        return cfCasePublicInfoDao.addCasePublicInfo(cfCasePublicInfo);
    }

    @Override
    public List<CfCasePublicInfo> getListByIdList(List<Long> caseIdList) {
        return cfCasePublicInfoDao.getListByIdList(caseIdList);
    }

    @Override
    public List<CfCasePublicInfo> getListByInfoUuidAndType(String infoUuid) {
        return cfCasePublicInfoDao.getListByInfoUuidAndType(infoUuid);
    }

    @Override
    public int update(CfCasePublicInfo cfCasePublicInfo) {
        return cfCasePublicInfoDao.update(cfCasePublicInfo);
    }

    @Override
    public int deleteByCaseIdAndType(int caseId, int type) {
        return cfCasePublicInfoDao.deleteByCaseIdAndType(caseId, type);
    }

    @Override
    public int addAi(CfCasePublicInfoImageAIRecord cfCasePublicInfoImageAIRecord) {
        return cfCasePublicInfoImageAIRecordDao.add(cfCasePublicInfoImageAIRecord);
    }

    @Override
    public CfCasePublicInfoImageAIRecord getById(long id) {
        return cfCasePublicInfoImageAIRecordDao.getById(id);
    }

    @Override
    public int updateCfCasePublicInfoImageAIRecord(CfCasePublicInfoImageAIRecord cfCasePublicInfoImageAIRecord) {
        return cfCasePublicInfoImageAIRecordDao.update(cfCasePublicInfoImageAIRecord);
    }
}
