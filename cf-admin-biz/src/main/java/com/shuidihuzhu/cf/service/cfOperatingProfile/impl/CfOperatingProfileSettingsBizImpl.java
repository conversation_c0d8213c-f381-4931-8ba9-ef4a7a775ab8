package com.shuidihuzhu.cf.service.cfOperatingProfile.impl;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.biz.crowdfunding.CfReportProblemBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfReportProblemRelationshipBiz;
import com.shuidihuzhu.cf.dao.cfOperatingProfile.CfOperatingProfileSettingsDao;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileLog;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileResult;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileSettings;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportProblem;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportProblemLabel;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportProblemRelationship;
import com.shuidihuzhu.cf.service.cfOperatingProfile.CfOperatingProfileLogBiz;
import com.shuidihuzhu.cf.service.cfOperatingProfile.CfOperatingProfileSettingsBiz;
import com.shuidihuzhu.cf.service.cfOperatingProfile.CfOperatingProfileSettingsExtBiz;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileLog.OperateType.DATA_STATUS_SET;

@Slf4j
@Service
public class CfOperatingProfileSettingsBizImpl implements CfOperatingProfileSettingsBiz {

    @Autowired
    private CfOperatingProfileLogBiz profileLogBiz;
    @Autowired
    private CfOperatingProfileSettingsDao profileSettingsDao;
    @Autowired
    private CfReportProblemBiz problemBiz;
    @Autowired
    private CfReportProblemRelationshipBiz  problemRelationshipBiz;
    @Autowired
    private CfOperatingProfileSettingsExtBiz settingsExtBiz;

    public List<CfOperatingProfileResult> queryAllSettingsByType(int profileType) {
        List<CfOperatingProfileSettings> allProfileSettings = profileSettingsDao.selectValidByProfileType(profileType);

        List<CfOperatingProfileResult> profileResult = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(allProfileSettings)) {

            allProfileSettings.sort(Comparator.comparing(CfOperatingProfileSettings::getRank));

            for (CfOperatingProfileSettings settings : allProfileSettings) {
                if (settings.getParentId() == 0) {
                    CfOperatingProfileResult curProfile = new CfOperatingProfileResult();
                    curProfile.setParentSettings(settings);

                    List<CfOperatingProfileSettings> subSettings = Lists.newArrayList();
                    for (CfOperatingProfileSettings findSubSetting : allProfileSettings) {
                        if (findSubSetting.getParentId() == settings.getId()) {
                            subSettings.add(findSubSetting);
                        }
                    }

                    fillPropertyAndProblem(subSettings);
                    curProfile.setSubSettings(subSettings);
                    profileResult.add(curProfile);
                }
            }
        }

        return profileResult;
    }

    public List<CfOperatingProfileSettings> querySettingsByTypeAndDataStatus(int parentId, int dataStatus, int profileType) {
        List<CfOperatingProfileSettings> allSettings = profileSettingsDao.selectByParentIdAndDataStatus(parentId, dataStatus, profileType);


        // 填充弃用的状态
        if (CollectionUtils.isNotEmpty(allSettings) && dataStatus == CfOperatingProfileLog.OperateType.DISABLE.getCode()) {
            if (parentId != 0) {
                for (CfOperatingProfileSettings settings : allSettings) {
                    settings.setCanDelete(settings.getUserSize() == 0 ? 1 : 0);
                    settings.setCanDisable(1);
                }
            } else {
                for (CfOperatingProfileSettings settings : allSettings) {

                    List<CfOperatingProfileSettings> enableSettings = profileSettingsDao
                            .selectByParentIdAndDataStatus(settings.getId(), CfOperatingProfileLog.OperateType.ENABLE.getCode(), profileType);
                    List<CfOperatingProfileSettings> disableSettings = profileSettingsDao
                            .selectByParentIdAndDataStatus(settings.getId(), CfOperatingProfileLog.OperateType.DISABLE.getCode(), profileType);

                    settings.setCanDisable(CollectionUtils.isEmpty(enableSettings) ? 1 : 0);
                    settings.setCanDelete(CollectionUtils.isEmpty(enableSettings) && CollectionUtils.isEmpty(disableSettings) ? 1 : 0);
                }
            }
        }

        fillPropertyAndProblem(allSettings);

        return allSettings;
    }

    public AdminErrorCode createProfileSetting(int userId, int parentId, String content, int profileType, Set<String> propertyList) {

        log.info("用户创建分类标签 userId:{} parentId:{} content:{} profileType:{} propertyList:{}",
                userId, parentId, content, profileType, propertyList);

        if (StringUtils.isBlank(content) || content.trim().length() > 50) {
            return AdminErrorCode.SYSTEM_PARAM_ERROR;
        }
        AdminErrorCode errorCode = validateCreateSettings(parentId, content, profileType);
        if (errorCode != AdminErrorCode.SUCCESS) {
            return errorCode;
        }

        CfOperatingProfileSettings settings = new CfOperatingProfileSettings();
        settings.setParentId(parentId);
        settings.setContent(content);
        settings.setProfileType(profileType);
        List<CfOperatingProfileSettings>  allSettings = profileSettingsDao.selectByParentIdAndDataStatus(parentId,
                CfOperatingProfileLog.OperateType.DISABLE.getCode(), profileType);

        if (CollectionUtils.isEmpty(allSettings)) {
            settings.setRank(1);
        } else {
            settings.setRank(allSettings.get(allSettings.size()-1).getRank() + 1);
        }

        settings.setIsDelete(CfOperatingProfileLog.OperateType.DISABLE.getCode());
        profileSettingsDao.addProfile(settings);

        insertPropertyList(settings.getId(), propertyList);

        profileLogBiz.insertOperateLog(userId, settings.getId(), CfOperatingProfileLog.OperateType.ADD.getCode(), null);

        return AdminErrorCode.SUCCESS;
    }

    private AdminErrorCode validateCreateSettings(int parentId, String content, int profileType) {

        List<CfOperatingProfileSettings> existSettings = profileSettingsDao.selectByParentIdAndContent(parentId, content, profileType);
        for (CfOperatingProfileSettings settings : existSettings) {
            if (settings.getIsDelete() != CfOperatingProfileLog.OperateType.DELETE.getCode()) {
                return parentId == 0 ?  AdminErrorCode.FIRST_LABLES_REPEAT : AdminErrorCode.SECOND_LABLES_REPEAT;
            }
        }
        return AdminErrorCode.SUCCESS;
    }

    public AdminErrorCode changeRank(int userId, int upId, int downId, int operateType) {

        log.info("改变标签的顺序. userId:{} upId:{} downId:{} operateType:{}", userId, upId, downId, operateType);
        List<CfOperatingProfileSettings> settingsList = profileSettingsDao.selectByIds(Lists.newArrayList(upId, downId));

        if (CollectionUtils.isEmpty(settingsList) || settingsList.size() != 2) {
            return AdminErrorCode.SYSTEM_PARAM_ERROR;
        }

        int upRank = getRankById(settingsList, upId);
        int downRank = getRankById(settingsList, downId);

        profileSettingsDao.updateDataRank(upId, downRank);
        profileSettingsDao.updateDataRank(downId, upRank);

        profileLogBiz.insertOperateLog(userId, operateType == CfOperatingProfileLog.OperateType.DOWN.getCode() ? upId : downId,
                operateType, null);

        return AdminErrorCode.SUCCESS;
    }

    private int getRankById(List<CfOperatingProfileSettings> settingsList, int id) {

        for (CfOperatingProfileSettings settings : settingsList) {
            if (settings.getId() == id) {
                return settings.getRank();
            }
        }

        throw new RuntimeException("不能找到CfOperatingProfileSettings. id:" + id);
    }

    public AdminErrorCode changeDataStatus(int userId, int id, int dataStatus) {

        List<CfOperatingProfileSettings> settingsList = profileSettingsDao.selectByIds(Lists.newArrayList(id));
        if (CollectionUtils.isEmpty(settingsList) || !DATA_STATUS_SET.contains(dataStatus)) {
            return AdminErrorCode.SYSTEM_PARAM_ERROR;
        }

        AdminErrorCode validCode = canOperateDataStatus(settingsList.get(0), dataStatus);
        if (validCode != AdminErrorCode.SUCCESS) {
            return validCode;
        }
        List<CfOperatingProfileSettings>  allSettings = profileSettingsDao.selectByParentIdAndDataStatus(
                settingsList.get(0).getParentId(), dataStatus, settingsList.get(0).getProfileType());

        int rank = CollectionUtils.isEmpty(allSettings) ? 1 : allSettings.get(allSettings.size() - 1).getRank() + 1;
        profileSettingsDao.updateDataStatusRank(id, dataStatus, rank);
        profileLogBiz.insertOperateLog(userId, id, dataStatus, null);
        return AdminErrorCode.SUCCESS;
    }

    private AdminErrorCode canOperateDataStatus(CfOperatingProfileSettings settings, int dataStatus) {

        // 一级标签
        if (settings.getParentId() == 0) {

            if (dataStatus == CfOperatingProfileLog.OperateType.DISABLE.getCode()
                && CollectionUtils.isNotEmpty(profileSettingsDao.selectByParentIdAndDataStatus(
                        settings.getId(), CfOperatingProfileLog.OperateType.ENABLE.getCode(), settings.getProfileType()))) {
                return AdminErrorCode.SECOND_LABELS_EXIST_EABLE;
            }

            if (dataStatus == CfOperatingProfileLog.OperateType.DELETE.getCode()) {
                List<CfOperatingProfileSettings> enableSettings = profileSettingsDao
                        .selectByParentIdAndDataStatus(settings.getId(), CfOperatingProfileLog.OperateType.ENABLE.getCode(),
                                settings.getProfileType());

                List<CfOperatingProfileSettings> disableSettings = profileSettingsDao
                        .selectByParentIdAndDataStatus(settings.getId(), CfOperatingProfileLog.OperateType.DISABLE.getCode(),
                                settings.getProfileType());

                if (CollectionUtils.isNotEmpty(enableSettings) || CollectionUtils.isNotEmpty(disableSettings)) {
                    return AdminErrorCode.CANNOT_DELETE;
                }
            }

        } else {
            if (dataStatus == CfOperatingProfileLog.OperateType.DELETE.getCode() && settings.getUserSize() > 0) {
                return AdminErrorCode.CANNOT_DELETE;
            }
        }

        return AdminErrorCode.SUCCESS;
    }

    @Override
    public void updateUseSize(Collection<Integer> ids, int size) {
        profileSettingsDao.updateUseSize(ids, size);
    }

    @Override
    public List<CfOperatingProfileSettings> selectByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }

        List<CfOperatingProfileSettings> result = profileSettingsDao.selectByIds(ids);
        fillPropertyAndProblem(result);
        return result;
    }

    @Override
    public List<CfOperatingProfileSettings.ReportProblemSettingsResult> selectReportSettingsByType(int type)  {

        switch(CfOperatingProfileSettings.ReportSearchType.parseCode(type)) {
            case MODULE:
                return selectSettingsByModule();
            case NAME:
                return selectSettingsByName(null);
            case REMARK:
                return selectSettingsByRemark();
            default:
                break;
        }

        return Lists.newArrayList();
    }

    private List<CfOperatingProfileSettings.ReportProblemSettingsResult> selectSettingsByModule() {

        List<CfOperatingProfileSettings.ReportProblemSettingsResult> settingsResults = Lists.newArrayList();
        List<CfReportProblemLabel> allProblemLabels = problemBiz.listLabels(2, null,1);

        for (CfReportProblemLabel label : allProblemLabels) {
            settingsResults.add(new
                    CfOperatingProfileSettings.ReportProblemSettingsResult(label.getLabelDesc(), label.getId()));
        }

        return settingsResults;
    }


    private List<CfOperatingProfileSettings.ReportProblemSettingsResult> selectSettingsByName(List<Integer> problemIds) {

        List<CfOperatingProfileSettings.ReportProblemSettingsResult> settingsResults = Lists.newArrayList();

        List<CfReportProblem> allProblems = CollectionUtils.isEmpty(problemIds) ?
                problemBiz.listForManager(null,1) : problemBiz.listByIds(problemIds);

        if (CollectionUtils.isEmpty(allProblems)) {
            return settingsResults;
        }

        Map<Integer, List<CfOperatingProfileSettings.ReportProblemName>> problemMappings = Maps.newTreeMap();
        Set<Integer> allLabelIds = Sets.newHashSet();
        for (CfReportProblem problem : allProblems) {
            allLabelIds.add(problem.getLabelId());

            List<CfOperatingProfileSettings.ReportProblemName> problemValues = problemMappings.get(problem.getLabelId());
            if (problemValues == null) {
                problemValues = Lists.newArrayList();
            }
            problemValues.add(new CfOperatingProfileSettings.ReportProblemName(problem.getProblem(), problem.getId()));

            problemMappings.put(problem.getLabelId(), problemValues);
        }

        List<CfReportProblemLabel> allLabels = problemBiz.listProblemLabelsByIds(Lists.newArrayList(allLabelIds),1);

        Map<Integer, String> reportMap = allLabels.stream().collect(Collectors.toMap(CfReportProblemLabel::getId,
                CfReportProblemLabel::getLabelDesc, (before, after) -> before));

        for (Map.Entry<Integer, List<CfOperatingProfileSettings.ReportProblemName>> entry : problemMappings.entrySet()) {

            String content = reportMap.get(entry.getKey());
            if (StringUtils.isBlank(content)) {
                continue;
            }

            CfOperatingProfileSettings.ReportProblemSettingsResult settingsResult = new CfOperatingProfileSettings
                    .ReportProblemSettingsResult(content, entry.getKey());
            settingsResult.setProblemNames(entry.getValue());
            settingsResults.add(settingsResult);

        }

        return settingsResults;
    }

    private List<CfOperatingProfileSettings.ReportProblemSettingsResult> selectSettingsByRemark() {

        List<CfReportProblemRelationship> allRelationShip = problemRelationshipBiz.listByNextProblemIds(Lists.newArrayList(0));

        Map<Integer, List<CfOperatingProfileSettings.ReportRemark>> remarkMappings = Maps.newHashMap();
        List<Integer> problemIds = Lists.newArrayList();
        for (CfReportProblemRelationship ship : allRelationShip) {
            List<CfOperatingProfileSettings.ReportRemark> remarkList = remarkMappings.get(ship.getProblemId());
            if (remarkList == null) {
                remarkList = Lists.newArrayList();
            }
            remarkList.add(new CfOperatingProfileSettings.ReportRemark(ship.getContent(), ship.getId()));

            problemIds.add(ship.getProblemId());
            remarkMappings.put(ship.getProblemId(), remarkList);
        }

        List<CfOperatingProfileSettings.ReportProblemSettingsResult> settingsResults = selectSettingsByName(problemIds);

        for (CfOperatingProfileSettings.ReportProblemSettingsResult settingsResult : settingsResults) {
            if (CollectionUtils.isEmpty(settingsResult.getProblemNames())) {
                continue;
            }
            for (CfOperatingProfileSettings.ReportProblemName problem : settingsResult.getProblemNames()) {

                problem.setReportRemarks(remarkMappings.get(problem.getProblemId()));
            }
        }

        return settingsResults;
    }

    @Override
    public void updateExtMappings(CfOperatingProfileSettings.ProfileProblemSettings settings) {

        log.info("更新系统自动标记条件. param:{}", settings);

        if (settings == null) {
            return ;
        }

        List<CfOperatingProfileSettings.ProfileSettingsExt> allSettingExts = Lists.newArrayList();
        List<String> comments = Lists.newArrayList();
        if (settings.getModuleId() != 0) {
            allSettingExts.add(new CfOperatingProfileSettings.ProfileSettingsExt(settings.getProfileId(),
                    CfOperatingProfileSettings.ProfileExtName.MODULE.getName(),
                    String.valueOf(settings.getModuleId()), settings.getModuleContent()));
            comments.add(settings.getModuleContent());
        }

        if (settings.getProblemId() != 0) {
            allSettingExts.add(new CfOperatingProfileSettings.ProfileSettingsExt(settings.getProfileId(),
                    CfOperatingProfileSettings.ProfileExtName.NAME.getName(),
                    String.valueOf(settings.getProblemId()), settings.getProblemContent()));
            comments.add(settings.getProblemContent());
        }

        if (settings.getRemarkId() != 0) {
            allSettingExts.add(new CfOperatingProfileSettings.ProfileSettingsExt(settings.getProfileId(),
                    CfOperatingProfileSettings.ProfileExtName.REMARK.getName(),
                    String.valueOf(settings.getRemarkId()), settings.getRemark()));
            comments.add(settings.getRemark());
        }

        updateExtMappings(settings.getProfileId(),
                Lists.newArrayList(
                        CfOperatingProfileSettings.ProfileExtName.MODULE.getName(),
                        CfOperatingProfileSettings.ProfileExtName.NAME.getName(),
                        CfOperatingProfileSettings.ProfileExtName.REMARK.getName()),
                allSettingExts
                );

        String comment = "系统标记条件: " + Joiner.on("-").join(comments);
        profileLogBiz.insertOperateLog(settings.getUserId(), (int)settings.getProfileId(),
                CfOperatingProfileLog.OperateType.ALTER_SYSTEM_MARK.getCode(), comment);
    }

    private void updateExtMappings(long profileId,
                                   List<String> names, List<CfOperatingProfileSettings.ProfileSettingsExt> allSettingExt) {

        if (CollectionUtils.isEmpty(names)) {
            return ;
        }

        // 删除以前的关联
        settingsExtBiz.deleteExtByProfileIdsAndNames(Lists.newArrayList(profileId), names);

        // 更新现在
        settingsExtBiz.insertExtList(allSettingExt);
    }

    // 填充属性和自动配置的功能
    private void fillPropertyAndProblem(List<CfOperatingProfileSettings> subSettings) {

        if (CollectionUtils.isEmpty(subSettings)) {
            return;
        }

        List<Long> profileIds = Lists.newArrayList();
        for (CfOperatingProfileSettings settings : subSettings) {
            profileIds.add(Long.valueOf(settings.getId()));
        }

        List<CfOperatingProfileSettings.ProfileSettingsExt> allExts = settingsExtBiz.selectByProfileIdsAndNames(profileIds,
                Lists.newArrayList(
                        CfOperatingProfileSettings.ProfileExtName.PROFILE_LABELS.getName(),
                        CfOperatingProfileSettings.ProfileExtName.MODULE.getName(),
                        CfOperatingProfileSettings.ProfileExtName.NAME.getName(),
                        CfOperatingProfileSettings.ProfileExtName.REMARK.getName())
                );

        if (CollectionUtils.isEmpty(allExts)) {
            return;
        }

        for (CfOperatingProfileSettings settings : subSettings) {

            List<String> contents = Lists.newArrayList();
            CfOperatingProfileSettings.ProfileProblemSettings problemSettings = new
                    CfOperatingProfileSettings.ProfileProblemSettings();
            for (CfOperatingProfileSettings.ProfileSettingsExt ext : allExts) {
                if (ext.getProfileId() != settings.getId()) {
                    continue;
                }
                switch (CfOperatingProfileSettings.ProfileExtName.parseName(ext.getExtName())) {
                    case PROFILE_LABELS:
                        contents.add(ext.getExtValue());
                        break;
                    case MODULE:
                        problemSettings.setModuleId(Integer.valueOf(ext.getExtValue()));
                        problemSettings.setModuleContent(ext.getExtInfo());
                        break;
                    case NAME:
                        problemSettings.setProblemId(Integer.valueOf(ext.getExtValue()));
                        problemSettings.setProblemContent(ext.getExtInfo());
                        break;
                    case REMARK:
                        problemSettings.setRemarkId(Integer.valueOf(ext.getExtValue()));
                        problemSettings.setRemark(ext.getExtInfo());
                        break;
                    default:
                        break;
                }
            }

            fillReportProblemType(problemSettings);
            settings.setPropertyList(contents);
            settings.setProblemSettings(problemSettings);
        }
    }

    // 设置 系统标记条件的类型 便于前端展示
    private void fillReportProblemType(CfOperatingProfileSettings.ProfileProblemSettings problemSettings) {
        if (problemSettings == null) {
            return;
        }

        if (problemSettings.getRemarkId() != 0) {
            problemSettings.setType(CfOperatingProfileSettings.ReportSearchType.REMARK.getCode());
        } else if (problemSettings.getProblemId() != 0) {
            problemSettings.setType(CfOperatingProfileSettings.ReportSearchType.NAME.getCode());
        } else if (problemSettings.getModuleId() != 0) {
            problemSettings.setType(CfOperatingProfileSettings.ReportSearchType.MODULE.getCode());
        }
    }

    private void insertPropertyList(long profileId, Set<String> propertyList) {

        log.info("处理标签配置的属性 profileId:{} propertyList:{}", profileId, propertyList);
        // 删除以前的关联
        settingsExtBiz.deleteExtByProfileIdsAndNames(Lists.newArrayList(profileId),
                Lists.newArrayList(CfOperatingProfileSettings.ProfileExtName.PROFILE_LABELS.getName()));

        if (CollectionUtils.isEmpty(propertyList)) {
            return;
        }

        List<CfOperatingProfileSettings.ProfileSettingsExt> allSettingExts = Lists.newArrayList();
        for (String property : propertyList) {
            allSettingExts.add(new CfOperatingProfileSettings.ProfileSettingsExt(profileId,
                    CfOperatingProfileSettings.ProfileExtName.PROFILE_LABELS.getName(),
                    property, ""));
        }

        settingsExtBiz.insertExtList(allSettingExts);
    }

}
