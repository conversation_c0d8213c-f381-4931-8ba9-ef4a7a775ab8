package com.shuidihuzhu.cf.service.label.core.service;

import com.google.common.collect.HashMultimap;
import com.google.common.collect.Multimap;
import com.shuidihuzhu.cf.domain.label.core.LabelConst;

/**
 * 标签状态机
 * <AUTHOR>
 */
public class LabelStatusMachine {

    private LabelStatusMachine() {
    }

    private static Multimap<Integer, Integer> stateMachineMap = HashMultimap.create();

    static {

        // 初始化
        stateMachineMap.put(LabelConst.LabelStatusEnum.INIT.getValue(), LabelConst.LabelStatusEnum.ENABLE.getValue());
        stateMachineMap.put(LabelConst.LabelStatusEnum.INIT.getValue(), LabelConst.LabelStatusEnum.DISABLE.getValue());
        stateMachineMap.put(LabelConst.LabelStatusEnum.INIT.getValue(), LabelConst.LabelStatusEnum.REMOVE.getValue());

        // 启用
        stateMachineMap.put(LabelConst.LabelStatusEnum.ENABLE.getValue(), LabelConst.LabelStatusEnum.DISABLE.getValue());

        // 弃用
    }

    public static boolean validate(int source, int target) {
        return stateMachineMap.containsEntry(source, target);
    }

}
