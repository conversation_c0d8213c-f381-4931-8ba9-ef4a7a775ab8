package com.shuidihuzhu.cf.service.wx;

import com.shuidihuzhu.cf.admin.delegate.SeaUserAccountDelegate;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.delegate.OrganizationDelegate;
import com.shuidihuzhu.cf.enums.wx.WxBotSendMsgTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.param.WxAlarmBotParam;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: wangpeng
 * @Date: 2021/3/2 19:27
 * @Description: 企业微信发消息内容处理handler
 */
@Component
public class WxAlarmBotService {

    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Resource
    private OrganizationDelegate organizationDelegate;

    @Resource
    private SeaUserAccountDelegate seaUserAccountDelegate;

    public String getAlarmMsgContent(WxAlarmBotParam wxAlarmBotParam) {
        if (wxAlarmBotParam.getCode() == WxBotSendMsgTypeEnum.APPROVE_NOTICE.getCode()) {
            return getAlarmContentApproveNotice(wxAlarmBotParam);
        }
        return "";
    }

    private String getAlarmContentApproveNotice(WxAlarmBotParam wxAlarmBotParam) {
        StringBuilder stringBuilder = new StringBuilder();
        String infoUuid = wxAlarmBotParam.getInfoUuid();
        if (StringUtils.isEmpty(infoUuid)) {
            return stringBuilder.toString();
        }
        CrowdfundingInfo fundingInfo = adminCrowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (Objects.isNull(fundingInfo)) {
            return stringBuilder.toString();
        }
        String organization = organizationDelegate.getSimpleOrganization(wxAlarmBotParam.getOperatorId());
        Map<Integer, String> accountMap = seaUserAccountDelegate.getByOperators(Collections.singletonList(wxAlarmBotParam.getOperatorId()));
        int caseId = fundingInfo.getId();
        int amount = fundingInfo.getAmount() / 100;

        stringBuilder.append("【高金额案例材审通过复审通知】\n");
        stringBuilder.append("案例id：");
        stringBuilder.append(caseId);
        stringBuilder.append("\n");
        stringBuilder.append("当前筹款金额：");
        stringBuilder.append(amount);
        stringBuilder.append("元\n");
        stringBuilder.append("工单操作人：");
        stringBuilder.append(organization);
        stringBuilder.append(MapUtils.isNotEmpty(accountMap) && StringUtils.isNotEmpty(accountMap.get(wxAlarmBotParam.getOperatorId())) ?
                "-" + accountMap.get(wxAlarmBotParam.getOperatorId()) :
                "");
        return stringBuilder.toString();
    }
}
