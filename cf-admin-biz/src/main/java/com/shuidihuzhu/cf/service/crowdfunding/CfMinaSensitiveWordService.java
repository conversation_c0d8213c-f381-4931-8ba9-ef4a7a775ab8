package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.base.Splitter;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.admin.util.AdminListUtil;
import com.shuidihuzhu.cf.util.wordfilter.SensitivewordFilter;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.cf.biz.crowdfunding.CfBlacklistWordBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.CfBlacklistWordTypeEnum;
import com.shuidihuzhu.common.web.util.http.HttpResponseModel;
import com.shuidihuzhu.common.web.util.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> Ahrievil
 */
@Slf4j
@Service
public class CfMinaSensitiveWordService {

    private static final String KEY = "cf-blacklist-word-mina";
    //敏感词url
    private final static String URL_SENSITIVE = "https://sdchou-static.oss-cn-beijing.aliyuncs.com/file/sensitiveWord.txt";

    private SensitivewordFilter sensitivewordFilter;

    @Autowired
    private CfBlacklistWordBiz cfBlacklistWordBiz;

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    public Set<String> get(String target) {
        String set = null;
        try {
            set = redissonHandler.get(KEY, String.class);
        } catch (Exception e) {
            log.error("CfMinaSensitiveWordService get error msg", e);
        }
        if (StringUtils.isBlank(set)) {
            setCache();
            reload();
        }
        if(sensitivewordFilter == null) {
            sensitivewordFilter = SensitivewordFilter.build(getWordSet(URL_SENSITIVE));
        }
        return sensitivewordFilter.getSensitiveWord(target, 1);
    }

    private void setCache() {
        try {
            redissonHandler.setEX(KEY, "miao", 60 * 60 * 1000L);
        } catch (Exception e) {
            log.error("CfMinaSensitiveWordService setCache error msg", e);
        }
    }

    public void reload() {
        if (sensitivewordFilter == null) {
            build();
        } else {
            sensitivewordFilter.reload(getWordSet(URL_SENSITIVE));
        }
    }

    private void build() {
        if(sensitivewordFilter == null) {
            sensitivewordFilter = SensitivewordFilter.build(getWordSet(URL_SENSITIVE));
        }
    }

    private Set<String> getDiseaseList() {
        List<String> list = AdminListUtil.getList(3000,
                (start, size) -> cfBlacklistWordBiz.selectAllWordsLimit(CfBlacklistWordTypeEnum.MINA, start, size));
        return Sets.newHashSet(list);
    }

    private Set<String> getWordSet(String url) {
        Set<String> wordSet = Sets.newHashSet();
        HttpResponseModel httpGet = HttpUtil.httpGet(url);
        if (httpGet.getStatusCode() == HttpStatus.SC_OK) {
            List<String> keyList = Splitter.on("\n").splitToList(httpGet.getBodyString());
            Set<String> keySet = Sets.newHashSet(keyList);
            wordSet.addAll(keySet);
        }
        return wordSet;
    }


    public int getWordSize() {
        return this.sensitivewordFilter.getSensitiveWordSize();
    }
}
