package com.shuidihuzhu.cf.service;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.clinet.event.center.enums.BizParamEnum;
import com.shuidihuzhu.cf.clinet.event.center.enums.UserOperationTypeEnum;
import com.shuidihuzhu.cf.clinet.event.center.model.CfUserEvent;
import com.shuidihuzhu.cf.delegate.shorturl.ShortUrlDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.util.crowdfunding.AdminAppPushTemplateUtil;
import com.shuidihuzhu.client.cf.growthtool.enums.QyWxMsgTypeEnum;
import com.shuidihuzhu.client.cf.growthtool.model.BdQywxGroupMessageModel;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.constant.CfClientMQTopicCons;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/12/18
 */
@Slf4j
@Service
public class EventCenterPublishService {

    @Autowired
    private Producer producer;

    @Autowired
    private ShortUrlDelegate shortUrlDelegate;

    private static final String USER_EVENT_TAG = com.shuidihuzhu.cf.clinet.event.center.constants.MQTagCons.USER_EVENT_FOR_EVENT_CENTER;
    private static final String USER_EVENT_TOPIC = com.shuidihuzhu.cf.clinet.event.center.constants.MQTopicCons.CF;

    /**
     * 案例资金用途
     * @param crowdfundingInfo
     * @param processId
     */
    public void sendFundAuditPass(CrowdfundingInfo crowdfundingInfo, Integer processId) {
        if (processId == null || processId <= 0) {
            return;
        }
        int caseId = crowdfundingInfo.getId();
        CfUserEvent cfUserEvent = new CfUserEvent();
        cfUserEvent.setType(UserOperationTypeEnum.FUND_AUDIT_PASS.getCode());
        cfUserEvent.setCaseId(caseId);
        cfUserEvent.setDataMap(ImmutableMap.of(
                BizParamEnum.INFO_UUID.getDesc(), crowdfundingInfo.getInfoId(),
                BizParamEnum.PROGRESS_ID.getDesc(), String.valueOf(processId)));
        sendEventCenterMq(new Message<>(USER_EVENT_TOPIC, USER_EVENT_TAG, USER_EVENT_TAG + "_fundAuditPass_" + caseId, cfUserEvent));
    }

    /**
     * 案例资金用途拒绝
     * @param crowdfundingInfo
     * @param fundAuditRejectedReason
     */
    public void sendFundAuditRejected(CrowdfundingInfo crowdfundingInfo, String fundAuditRejectedReason, int fundUseProgressId) {
        int caseId = crowdfundingInfo.getId();
        CfUserEvent cfUserEvent = new CfUserEvent();
        cfUserEvent.setType(UserOperationTypeEnum.FUND_AUDIT_REJECTED.getCode());
        cfUserEvent.setCaseId(caseId);
        cfUserEvent.setDataMap(ImmutableMap.of(
                BizParamEnum.INFO_UUID.getDesc(), crowdfundingInfo.getInfoId(),
                BizParamEnum.FUND_AUDIT_REJECTED_REASON.getDesc(), AdminAppPushTemplateUtil.limitComment(fundAuditRejectedReason),
                BizParamEnum.FUND_USE_PROGRESS_ID.getDesc(), String.valueOf(fundUseProgressId)));
        sendEventCenterMq(new Message<>(USER_EVENT_TOPIC, USER_EVENT_TAG, USER_EVENT_TAG + "_fundAuditRejected_" + caseId, cfUserEvent));
    }

    /**
     * 发送选择了不符合大病筹而停止的消息
     */
    public void sendNoMatchSeriousIllness(CrowdfundingInfo crowdfundingInfo) {
        int caseId = crowdfundingInfo.getId();
        CfUserEvent cfUserEvent = new CfUserEvent();
        cfUserEvent.setCaseId(caseId);
        cfUserEvent.setUserId(crowdfundingInfo.getUserId());
        cfUserEvent.setType(UserOperationTypeEnum.CASE_STOP_END.getCode());
        cfUserEvent.setDataMap(ImmutableMap.of(
                BizParamEnum.INFO_UUID.getDesc(), crowdfundingInfo.getInfoId(),
                BizParamEnum.REJECT_ITEM.getDesc(), "noMatchSeriousIllness"));
        log.info("caseId:{} sendNoMatchSeriousIllness to user", caseId);
        sendEventCenterMq(new Message<>(USER_EVENT_TOPIC, USER_EVENT_TAG, USER_EVENT_TAG + "_notMatchSeriousIllness_" + caseId, cfUserEvent));
    }

    /**
     * 初审通过
     */
    public void sendFirstApprove(CrowdfundingInfo crowdfundingInfo){
        long userId = crowdfundingInfo.getUserId();
        int caseId = crowdfundingInfo.getId();

        CfUserEvent cfUserEvent = new CfUserEvent();
        Map<String,String> dataMap = Maps.newHashMap();
        dataMap.put(BizParamEnum.INFO_UUID.getDesc(),crowdfundingInfo.getInfoId());
        cfUserEvent.setUserId(userId);
        cfUserEvent.setCaseId(caseId);
        cfUserEvent.setType(UserOperationTypeEnum.CASE_AUDIT_PASS.getCode());
        cfUserEvent.setDataMap(dataMap);
        sendEventCenterMq(new Message<>(USER_EVENT_TOPIC, USER_EVENT_TAG, USER_EVENT_TAG + "_" + caseId, cfUserEvent));
    }

    /**
     * 材料审核驳回
     */
    public void sendMaterialReviewOverrule(CrowdfundingInfo crowdfundingInfo,String comment){
        long userId = crowdfundingInfo.getUserId();
        int caseId = crowdfundingInfo.getId();

        CfUserEvent cfUserEvent = new CfUserEvent();
        Map<String,String> dataMap = Maps.newHashMap();
        dataMap.put(BizParamEnum.INFO_UUID.getDesc(),crowdfundingInfo.getInfoId());
        dataMap.put(BizParamEnum.MATERIAL_REVIEW_OVERRULE_COMMENT.getDesc(),comment);
        cfUserEvent.setUserId(userId);
        cfUserEvent.setCaseId(caseId);
        cfUserEvent.setType(UserOperationTypeEnum.MATERIAL_REVIEW_OVERRULE.getCode());
        cfUserEvent.setDataMap(dataMap);
        sendEventCenterMq(new Message<>(USER_EVENT_TOPIC, USER_EVENT_TAG, USER_EVENT_TAG + "_" + caseId, cfUserEvent));
    }

    /**
     * 举报工单处理完成
     * @param workOrderId
     */
    @Deprecated
    public void sendReportWorkOrderEnd(long workOrderId, String smsText) {
        CfUserEvent cfUserEvent = new CfUserEvent();
        Map<String,String> dataMap = Maps.newHashMap();
        dataMap.put(BizParamEnum.WORK_ORDER_ID.getDesc(), String.valueOf(workOrderId));
        dataMap.put(BizParamEnum.MSG_TEXT.getDesc(), smsText);
        cfUserEvent.setDataMap(dataMap);
        cfUserEvent.setType(UserOperationTypeEnum.REPORT_WORK_ORDER_END.getCode());
        sendEventCenterMq(new Message<>(USER_EVENT_TOPIC, USER_EVENT_TAG, USER_EVENT_TAG + "_reportWorkOrderEnd_" + workOrderId, cfUserEvent));
    }

    /**
     * 举报工单处理完成
     * @param workOrderId
     */
    public void sendReportWorkOrderEndV2(long workOrderId, int caseId) {
        CfUserEvent cfUserEvent = new CfUserEvent();
        Map<String,String> dataMap = Maps.newHashMap();
        dataMap.put(BizParamEnum.WORK_ORDER_ID.getDesc(), String.valueOf(workOrderId));
        cfUserEvent.setDataMap(dataMap);
        cfUserEvent.setCaseId(caseId);
        cfUserEvent.setType(UserOperationTypeEnum.REPORT_WORK_ORDER_END.getCode());
        sendEventCenterMq(new Message<>(USER_EVENT_TOPIC, USER_EVENT_TAG, USER_EVENT_TAG + "_reportWorkOrderEnd_" + workOrderId, cfUserEvent));
    }

    private void sendEventCenterMq(Message message) {
        if (producer == null) {
            return;
        }
        try {
            producer.send(message);
        } catch (Exception e) {
            log.error("sendEventCenterMq error. message:{}", message, e);
        }
    }

}
