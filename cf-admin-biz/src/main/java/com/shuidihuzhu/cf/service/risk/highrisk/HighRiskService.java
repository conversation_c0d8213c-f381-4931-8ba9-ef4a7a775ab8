package com.shuidihuzhu.cf.service.risk.highrisk;

import com.shuidihuzhu.cf.client.adminpure.model.rule.EconomyModel;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.HighRiskJudgeResult;
import com.shuidihuzhu.cf.vo.approve.CreditInfoVO;

/**
 * <AUTHOR>
 */
public interface HighRiskService {

    HighRiskJudgeResult judgeRisk(int credit, long workOrderId, CreditInfoVO creditInfoVO);

    EconomyModel judgeManual(int caseId, Long workOrderId, int userId, EconomyModel economyModel);

    Integer getMResult(int caseId);

}
