package com.shuidihuzhu.cf.service.ai;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.ai.alps.client.model.aihub.StreamChatRequest;
import com.shuidihuzhu.cf.dao.ai.AiPromptConfigDao;
import com.shuidihuzhu.cf.enums.ai.AiGenerateEnum;
import com.shuidihuzhu.cf.model.ai.AiPromptConfig;
import com.shuidihuzhu.cf.util.crowdfunding.CharacterUtil;
import com.shuidihuzhu.client.cf.admin.enums.AiMockDataEnum;
import com.shuidihuzhu.client.cf.admin.model.AIGenerateParam;
import com.shuidihuzhu.client.cf.admin.model.AiGenerateBaseInfo;
import com.shuidihuzhu.client.cf.admin.model.AiGenerateResult;
import com.shuidihuzhu.client.model.ChatChunk;
import com.shuidihuzhu.client.model.ChatCompletionChunk;
import com.shuidihuzhu.client.model.ChatStreamResult;
import com.shuidihuzhu.client.util.AiBaseFieldMapping;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.client.reactive.ClientHttpConnector;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.lang.reflect.Field;

/**
 * @Description: 大模型父类
 * @Author: panghairui
 * @Date: 2024/5/29 5:22 PM
 */
@Slf4j
@Service
public abstract class BaseModel {

    private WebClient webClient;
    @PostConstruct
    public void init() {
        ClientHttpConnector connector = new ReactorClientHttpConnector();
        this.webClient = WebClient.builder().clientConnector(connector).defaultHeader(HttpHeaders.CONTENT_TYPE, "application/json").build();
    }

    @Resource
    private AiPromptConfigDao aiPromptConfigDao;

    /**
     * 获取提示词 有特殊需求的话，子类自己实现
     */
    protected String getPrompt(AIGenerateParam aiGenerateParam, Integer step) {

        Integer generateType = aiGenerateParam.getGenerateType();
        if (generateType == AiGenerateEnum.ARTICLE.getCode()) {
            return articlePrompt(aiGenerateParam, step);
        } else if (generateType == AiGenerateEnum.TITLE.getCode()) {
            return titlePrompt(aiGenerateParam);
        } else if (generateType == AiGenerateEnum.FORWARD.getCode()) {

            if (Objects.nonNull(aiGenerateParam.getBizType()) && aiGenerateParam.getBizType() == 1) {
                return forWardQueryPrompt(aiGenerateParam);
            }

            return forWardPrompt(aiGenerateParam);
        } else if (generateType == AiGenerateEnum.ACKNOWLEDGMENTS.getCode()) {
            return acknowledgmentPrompt(aiGenerateParam);
        }

        return "";
    }

    /**
     * 调用大模型 子类必须实现
     */
    protected abstract String callModelApi(AIGenerateParam aiGenerateParam, String prompt);

    protected abstract Flux<ChatChunk<ChatCompletionChunk>> stream(String prompt);

    /**
     * 解析大模型的结果
     */
    protected static List<AiGenerateResult> parseResult(String aiResult, Integer type) {

        List<AiGenerateResult> aiGenerateResults = Lists.newArrayList();
        if (StringUtils.isBlank(aiResult)) {
            return aiGenerateResults;
        }

        // 使用分号分割字符串
        String[] titlesArray = aiResult.split(";|；");

        // 打印分割后的每个标题
        for (String title : titlesArray) {
            if (title.length() < 5) continue;
            if (type == AiGenerateEnum.TITLE.getCode() && title.length() > 60) {
                continue;
            }
            aiGenerateResults.add(new AiGenerateResult(title));
        }

        return aiGenerateResults;
    }

    public List<AiGenerateResult> generateContent(AIGenerateParam aiGenerateParam) {

        AiGenerateBaseInfo curAiInfo = new AiGenerateBaseInfo();
        BeanUtils.copyProperties(aiGenerateParam.getAiGenerateBaseInfo(), curAiInfo);
        aiGenerateParam.setAiGenerateBaseInfoContext(curAiInfo);

        // 脱敏数据映射
        Boolean isSame = false;
        if (Objects.nonNull(curAiInfo.getPatientName()) && Objects.nonNull(curAiInfo.getRaiseName())) {
            isSame = curAiInfo.getPatientName().equals(curAiInfo.getRaiseName());
        }
        Map<String, String> reflectMap = dataDesensitization(curAiInfo, isSame);
        log.info("generateContent reflectMap {}", JSONObject.toJSONString(reflectMap));

        // 文章生成，得调用三次
        if (aiGenerateParam.getGenerateType() == AiGenerateEnum.ARTICLE.getCode()) {

            String prompt1 = getPrompt(aiGenerateParam, 1);
            String aiResult1 = callModelApi(aiGenerateParam, prompt1);
            aiGenerateParam.setContext(aiResult1);

            String prompt2 = getPrompt(aiGenerateParam, 2);
            String aiResult2 = callModelApi(aiGenerateParam, prompt2);
            aiGenerateParam.setContext(aiResult1 + "\n" + aiResult2);

            String prompt3 = getPrompt(aiGenerateParam, 3);
            String aiResult3 = callModelApi(aiGenerateParam, prompt3);

            return restoreMockData(reflectMap, Lists.newArrayList(new AiGenerateResult(aiResult1 + "\n" + aiResult2 + "\n" + aiResult3)));

        }

        String prompt = getPrompt(aiGenerateParam, 0);
        if (StringUtils.isBlank(prompt)) {
            return Lists.newArrayList();
        }

        String aiResult = callModelApi(aiGenerateParam, prompt);
        if (StringUtils.isBlank(aiResult)) {
            return Lists.newArrayList();
        }

        return restoreMockData(reflectMap, parseResult(aiResult, aiGenerateParam.getGenerateType()));
    }

    public Flux<ChatChunk<ChatCompletionChunk>> streamGenerate(String modelName, String prompt) {

        StreamChatRequest streamChatRequest = new StreamChatRequest();
        streamChatRequest.setAppCode("1004");
        streamChatRequest.setModel(modelName);
        streamChatRequest.setPrompt(prompt);

        return webClient.post()
                .uri("http://ai-hub.ai-alps/innerapi/ai-hub/chat/proxy")
                .bodyValue(streamChatRequest)
//                .header("Authorization", generateAuthorization())
                .retrieve()
                .bodyToFlux(String.class)
                .onErrorResume(WebClientResponseException.class, ex -> {
                    HttpStatus status = ex.getStatusCode();
                    String res = ex.getResponseBodyAsString();
                    log.error("streamGenerate [调用大模型失败] {} {}", status, res);
                    return Mono.error(new RuntimeException(res));
                }).mapNotNull(chunk -> {

                    if (chunk == null) {
                        log.error("streamGenerate [生成回答失败, chunk为空]");
                        return ChatChunk.error(1, "调用大模型失败");
                    }

                    ChatCompletionChunk parsedChunk = JSON.parseObject(chunk, ChatCompletionChunk.class);

                    ChatCompletionChunk.Choice choice = parsedChunk.getData();
                    if (choice == null) {
                        log.error("streamGenerate [生成回答失败, choice为空]");
                        return ChatChunk.error(1, "调用大模型失败");
                    }

                    if (choice.getEnd()) {
                        ChatCompletionChunk chatStreamResult = new ChatCompletionChunk();
                        return ChatChunk.end(chatStreamResult);
                    }

                    return ChatChunk.success(parsedChunk);
                });
    }

    private Map<String, String> dataDesensitization(AiGenerateBaseInfo aiGenerateBaseInfo, Boolean isSame) {

        Map<String, String> reflectMap = Maps.newConcurrentMap();

        Field[] allFields = aiGenerateBaseInfo.getClass().getDeclaredFields();
        for (Field field : allFields) {
            try {
                AiBaseFieldMapping fieldMapping = field.getAnnotation(AiBaseFieldMapping.class);
                if (fieldMapping == null
                        || fieldMapping.aiMockData() == null
                        || fieldMapping.aiMockData() == AiMockDataEnum.DEFAULT) {
                    continue;
                }

                field.setAccessible(true);

                Object value = field.get(aiGenerateBaseInfo); // 获取字段的值
                if (value == null) {
                    continue;
                }

                if (fieldMapping.aiMockData() == AiMockDataEnum.AGE_MOCK) {
                    Integer age = (Integer) value;
                    reflectMap.put(String.valueOf(age), String.valueOf(age + 2));
                    field.set(aiGenerateBaseInfo, age + 2);
                    continue;
                }

                if (fieldMapping.aiMockData() == AiMockDataEnum.PATIENT_NAME_MOCK
                        || fieldMapping.aiMockData() == AiMockDataEnum.RAISER_NAME_MOCK) {
                    String[] mockData = fieldMapping.aiMockData().getMockData();
                    int index = (int) (Math.random() * mockData.length);
                    String name = mockData[index];
                    if (isSame) {
                        name = AiMockDataEnum.RAISER_NAME_MOCK.getMockData()[0];
                    }
                    reflectMap.put(String.valueOf(value), name);
                    field.set(aiGenerateBaseInfo, name);
                    continue;
                }

                String[] mockData = fieldMapping.aiMockData().getMockData();
                int index = (int) (Math.random() * mockData.length);
                reflectMap.put(String.valueOf(value), mockData[index]);
                field.set(aiGenerateBaseInfo, mockData[index]);


            } catch (Exception e) {
                log.error("通过注解拿取对象值错误。field:{}", field, e);
            }

        }

        return reflectMap;
    }



    /**
     * 还原数据
     */
    private List<AiGenerateResult> restoreMockData(Map<String, String> reflectMap, List<AiGenerateResult> aiGenerateResults) {

        for (AiGenerateResult result : aiGenerateResults) {
            for (Map.Entry<String, String> entry : reflectMap.entrySet()) {
                result.setAiGenerateInfo(StringUtil.replace(replaceCharacters(result.getAiGenerateInfo()), entry.getValue(), entry.getKey()));
            }
        }
        log.info("generateContent restoreMockData reflectMap {} aiGenerateResults {}", JSONObject.toJSONString(reflectMap), JSONObject.toJSONString(aiGenerateResults));

        return aiGenerateResults;

    }

    public static String replaceCharacters(String content) {
        content = CharacterUtil.replaceCharactersV1(content);
        return CharacterUtil.replaceCharactersV2(content);
    }

    private String articlePrompt(AIGenerateParam aiGenerateParam, Integer step) {

        AiGenerateBaseInfo aiGenerateBaseInfo = aiGenerateParam.getAiGenerateBaseInfoContext();
        if (Objects.isNull(aiGenerateBaseInfo)) {
            return "";
        }

        String stylePrompt = getContentStyle(aiGenerateBaseInfo.getPatientIdentity()) + "\n";
        String baseInfoPrompt = "本人".equals(aiGenerateBaseInfo.getRaisePatientRelation()) ? "" : "我为我的" + aiGenerateBaseInfo.getRaisePatientRelation() + aiGenerateBaseInfo.getPatientName() + "筹款\n";
        try {
            baseInfoPrompt += getBaseInfoPrompt(aiGenerateBaseInfo);
        } catch (Exception e) {
            log.error("articlePrompt error", e);
        }
        if (StringUtils.isBlank(baseInfoPrompt)) {
            return "";
        }

        String prePrompt = "";
        if (step == 1) {
            prePrompt = "## 人设\n" +
                    "  描述: 你是一位能够通过真实故事打动人心的筹款文章生成专家。\n" +
                    "  任务: 利用关键信息创作感人的筹款文章开头。\n" +
                    "## 背景\n" +
                    "  精心设计的筹款文章开头可以立即吸引读者的关注，并激发他们的同情心。\n" +
                    "## 目标\n" +
                    "  - 生成一个具有强烈情感吸引力的筹款文章开头，不仅让读者了解情况，还让他们感受到参与的紧迫性。\n" +
                    stylePrompt +
                    "## 约束\n" +
                    "  - 请以发起人为第一视角编写文章。\n" +
                    "  - 控制在100字以内。\n" +
                    "  - 请确保在文章中使用我提供的地址信息’吉林省长春市幸福乡南部家园’，不得进行任何形式的篡改。\n" +
                    "  - 使用简洁而富有感染力的语言。\n" +
                    "  - 必须使用中文。\n" +
                    "## 能力\n" +
                    "  - 创造一个情感上引人入胜的开头，能够即刻抓住读者的情感，并引起读者的同情。\n" +
                    "## 工作流程\n" +
                    "  - 接收关键的患者信息，转化为一个具有感染力的文章开头。\n" +
                    "  - 重点介绍患者的基本情况、疾病或事故的严重性以及筹款的紧迫性。\n" +
                    "## 患者信息\n" + baseInfoPrompt;
        } else if (step == 2) {
            prePrompt = "## 人设\n" +
                    "  描述: 你是一位擅长将详细的医疗信息和感人故事融合的筹款文章生成专家。\n" +
                    "  任务: 利用提供的患者关键信息和文章开头，生成筹款文章的中间部分，详细描述病情和治疗过程。\n" +
                    "## 背景\n" +
                    "  筹款文章的中间部分需要详细阐述患者的医疗状况和治疗计划，以及治疗所需的资金和家庭经济状况，以增强读者的理解和同情。\n" +
                    "## 目标\n" +
                    stylePrompt +
                    "  - 生成具体详尽但易于理解的中间内容，描述患者的医疗历程和治疗需求，强调筹款的必要性。\n" +
                    "  - 在读者心中构建患者的生动形象，加深他们对患者处境的理解和同情。\n" +
                    "## 约束\n" +
                    "  - 请以发起人为第一视角编写文章。\n" +
                    "  - 中间部分应包括具体的治疗方案、事故类型及其对家庭的影响。\n" +
                    "  - 文本应保持流畅，避免过于复杂的医疗术语，使非专业读者也能易于理解。\n" +
                    "  - 不需要生成文章结尾。\n" +
                    "  - 使用中文，并控制在300字以内。\n" +
                    "## 能力\n" +
                    "  - 结合技术性描述与人文关怀，平衡医疗信息的准确性与故事的情感表达。\n" +
                    "  - 能够根据患者实际情况，适当强调治疗的紧迫性和对家庭的影响。\n" +
                    "## 工作流程\n" +
                    "  - 接收文章开头和患者的详细信息（包括医疗状况、治疗计划、家庭经济情况）。\n" +
                    "  - 基于这些信息，生成描述治疗方案和病情挑战的中间段落。\n" +
                    "## 患者信息\n" + baseInfoPrompt
            + "## 文章开头\n" + aiGenerateParam.getContext();
        } else if (step == 3) {
            prePrompt = "## 人设\n" +
                    "  描述: 你是一位精通于通过强调社会责任和激发行动的呼吁来结束筹款文章的生成专家。\n" +
                    "  任务: 利用已提供的文章内容和患者信息，生成一个具有强烈呼吁性和情感吸引力的结尾。\n" +
                    "## 背景\n" +
                    "  结尾部分是筹款文章的关键，需要强化读者的情感投入，并鼓励他们采取实际行动，如捐款或分享信息。\n" +
                    "## 目标\n" +
                    stylePrompt +
                    "  - 生成一个情感上引人入胜的结尾，能够激发读者的同情心和行动欲望。\n" +
                    "  - 强调每一份捐助的重要性，无论大小，都对患者的生活产生深远影响。\n" +
                    "## 约束\n" +
                    "  - 请以发起人为第一视角编写结尾。\n" +
                    "  - 必须紧扣文章的主题和情感调性。\n" +
                    "  - 使用简洁、激励性的语言。\n" +
                    "  - 保持在100字以内。\n" +
                    "  - 使用中文。\n" +
                    "  - 结尾中不要使用“让我们携手”、“让我们共同见证”等群体呼吁的话语。\n" +
                    "## 能力\n" +
                    "  - 能够创造性地将患者的情况与社会责任感结合起来，形成强烈的呼吁。\n" +
                    "  - 熟练掌握结尾的情感设计，确保读者感受到参与的紧迫性。\n" +
                    "  - 强调患者家属的无奈和紧急情况，呼吁社会各界的爱心人士进行转发和捐款。不要使用“让我们携手”或“让我们共同见证”等群体呼吁的话语。\n" +
                    "## 工作流程\n" +
                    "  - 综合考虑文章的整体内容和患者的具体信息，包括已筹集的资金情况和剩余需求。\n" +
                    "  - 生成一个结合感谢、呼吁和前瞻的结尾。\n" +
                    "## 患者信息\n" + baseInfoPrompt
                    + "## 文章内容\n" + aiGenerateParam.getContext();
        }

        return prePrompt;
    }

    private String titlePrompt(AIGenerateParam aiGenerateParam) {

        AiGenerateBaseInfo aiGenerateBaseInfo = aiGenerateParam.getAiGenerateBaseInfoContext();
        if (Objects.isNull(aiGenerateBaseInfo)) {
            return "";
        }

        String baseInfoPrompt =  "本人".equals(aiGenerateBaseInfo.getRaisePatientRelation()) ? "" : "我为我的" + aiGenerateBaseInfo.getRaisePatientRelation() + aiGenerateBaseInfo.getPatientName() + "筹款\n";
        try {
            baseInfoPrompt += getBaseInfoPrompt(aiGenerateBaseInfo);
        } catch (Exception e) {
            log.error("articlePrompt error", e);
        }
        if (StringUtils.isBlank(baseInfoPrompt)) {
            return "";
        }

        return "# 角色\n" +
                "你是一个拥有近30年工作经验的资深文章编辑，专门负责筹款文章的标题编写。你的任务是利用你敏锐的热点嗅觉和丰富的编辑经验，为大病患者筹款的文章编写吸引眼球的标题。这些患者通常因无法承担高昂的医疗费用而通过在线平台发起筹款。有效的标题能够吸引更多的阅读者并激发他们的捐款意愿。\n" +
                "\n" +
                "# 规则\n" +
                "1. 标题需要包含大于3项的关键信息, 结构可自行组织: \n" +
                "          `\n" +
                "        a. 患者基本信息: [患者地域、患者职业、患者疾病 、患者姓名、患者关系]\n" +
                "        b. 患者疾病信息: [疾病未来花费金额、ICU、病情严重程度]\n" +
                "        c. 描述重症的关键词: [ICU、休克、重度昏迷、开颅、截肢、紧急救援、惨烈车祸、与时间赛跑等]\n" +
                "        d. 结尾渲染求助情绪: [生命之光即将熄灭、迫切需要生命支持、生死攸关、拯救生命的最后希望、火速拯救、生死关头、仅剩一线生机、迫切需要紧急救援、生命垂危...]\n" +
                "        `\n" +
                "2. 更能吸引筹款的关键词: [大学生、留守儿童、退伍老兵、艺术家、老师、单亲家庭] \n" +
                "3. 标题中可以选择增加图标: 🆘 🏥 🚑 🙏 ❗\n" +
                "\n" +
                "# 约束\n" +
                "1. 标题长度要求: [[[大于 10 个汉字， 小于 30 个汉字]]]。\n" +
                "2. 保持事实性：标题需根据输入的信息来生成，真实的反映情况，避免夸张或不实之词，以免引起不良效果。\n" +
                "3. 只返回标题，且要求按照输出格式输出\n" +
                "\n" +
                "# 工作流程\n" +
                "1. 接收患者的筹款文章信息。\n" +
                "2. 基于患者信息和病情，构思吸引力强的筹款文章标题。\n" +
                "3. 审核标题，确保其符合上述规则并具有吸引力。\n" +
                "4. 按照输出格式，返回筹款标题。\n" +
                "\n" +
                "# 输出格式\n" +
                "输出创作的文章标题Top4，每个标题以英文分号;分隔，例如：\n" +
                "    `\n" +
                "    文章标题1;文章标题2;文章标题3;...\n" +
                "    `\n" +
                "\n" +
                "# 标题例子\n" +
                "比较好的标题示例: \n" +
                "XX地单亲家庭女儿突遭车祸ICU救治中，28岁少女急需40万救命！\n" +
                "XX地张三母亲患肺癌急需手术救治，盼助！\n" +
                "甘肃庆城县新生儿在ICU挣扎求生，急需大家点亮新生希望！\n" +
                "贵州父亲张三患尿毒症，脓毒血症，家庭贫困急需救助！\n" +
                "7岁女儿患再障贫血急需二次移植，护士妈妈含泪求助\n" +
                "青海18岁张三确诊脑部多个肿瘤，病情加重救救她🙏\n" +
                "🆘丈夫李四【多发性骨髓瘤】费用高昂！盼援助！\n" +
                "青海求助 ❗父亲【张三】不幸发生车祸需手术，求助\n" +
                "沈阳空航天大学19岁张三患重症，治疗费用告急！\n" +
                "不能让精神抑郁症失去自我！”希望能在尊贵的月里好转\n" +
                "紧急求助🆘本人身患听神经瘤，急需10万治疗资金！\n" +
                "乐东呼吁🆘急需手术救救我身患肿瘤6个月的儿子🙏\n" +
                "淄博市《19岁山东张三ICU抢救》请您帮帮他！！\n" +
                "与时间赛跑，西藏两岁多孩童遭牦牛袭击急转成都保眼！\n" +
                "\n" +
                "# 筹款信息\n" + baseInfoPrompt;
    }

    private String acknowledgmentPrompt(AIGenerateParam aiGenerateParam) {
        AiGenerateBaseInfo aiGenerateBaseInfo = aiGenerateParam.getAiGenerateBaseInfoContext();
        if (Objects.isNull(aiGenerateBaseInfo)) {
            return "";
        }

        AiPromptConfig aiPromptConfig = aiPromptConfigDao.selectByGenerateType(aiGenerateParam.getGenerateType(), aiGenerateParam.getModelType(), aiGenerateParam.getBizType());
        if (Objects.isNull(aiPromptConfig)) {
            return "";
        }

        String baseInfoPrompt = "";
        try {
            baseInfoPrompt = getBaseInfoPrompt(aiGenerateBaseInfo);
        } catch (Exception e) {
            log.error("articlePrompt error", e);
        }

        return aiPromptConfig.getPrompt() + baseInfoPrompt;
    }

    private String forWardQueryPrompt(AIGenerateParam aiGenerateParam) {

        AiGenerateBaseInfo aiGenerateBaseInfo = aiGenerateParam.getAiGenerateBaseInfoContext();
        if (Objects.isNull(aiGenerateBaseInfo)) {
            return "";
        }

        String baseInfoPrompt = "本人".equals(aiGenerateBaseInfo.getRaisePatientRelation()) ? "" : "我为我的" + aiGenerateBaseInfo.getRaisePatientRelation() + aiGenerateBaseInfo.getPatientName() + "筹款\n";
        try {
            baseInfoPrompt += getBaseInfoPrompt(aiGenerateBaseInfo);
        } catch (Exception e) {
            log.error("articlePrompt error", e);
        }
        if (StringUtils.isBlank(baseInfoPrompt)) {
            return "";
        }

        AiPromptConfig aiPromptConfig = aiPromptConfigDao.selectByGenerateType(aiGenerateParam.getGenerateType(), aiGenerateParam.getModelType(), aiGenerateParam.getBizType());
        if (Objects.isNull(aiPromptConfig)) {
            return "";
        }

        return aiPromptConfig.getPrompt() + baseInfoPrompt;
    }

    private String forWardPrompt(AIGenerateParam aiGenerateParam) {
        AiGenerateBaseInfo aiGenerateBaseInfo = aiGenerateParam.getAiGenerateBaseInfoContext();
        if (Objects.isNull(aiGenerateBaseInfo)) {
            return "";
        }

        String baseInfoPrompt = "本人".equals(aiGenerateBaseInfo.getRaisePatientRelation()) ? "" : "我为我的" + aiGenerateBaseInfo.getRaisePatientRelation() + aiGenerateBaseInfo.getPatientName() + "筹款\n";
        try {
            baseInfoPrompt += getBaseInfoPrompt(aiGenerateBaseInfo);
        } catch (Exception e) {
            log.error("articlePrompt error", e);
        }
        if (StringUtils.isBlank(baseInfoPrompt)) {
            return "";
        }
        return "# 角色\n" +
                "您是一位拥有近30年编辑经验的资深文章编辑，擅长捕捉社会热点，专注于根据筹款案例信息撰写引人入胜的转发语，以此吸引更多读者的关注和参与。\n" +
                "\n" +
                "\n" +
                "# 背景\n" +
                "1. 许多重病患者因医疗费用高昂而难以承担，常通过网络平台向社会公众筹款。这些筹款通常包括标题、文章、图片和医疗动态等内容。经过平台审核无误后，患者的故事会被发布在线上，患者及其家人通过社交媒体等途径进行分享，这时一个精心设计的转发语显得尤为重要。\n" +
                "2. 您的任务是编写这些筹款患者的转发语。一个好的转发语能极大提高文章的点击率，从而增加患者故事的曝光度和捐款的可能性。\n" +
                "3. 有效的转发语应具备以下特点：\n" +
                "`\n" +
                "    1. 身份和关系明确：指明患者的身份以及与转发者的关系，比如老师、学生、医护、职员等。\n" +
                "    2. 突出疾病情况：描述患者被确诊的疾病，强调对其生活和工作的影响。\n" +
                "    3. 求助内容明确：清晰表达求助的内容，如转发、捐款、证实等。\n" +
                "    4. 增加氛围情感化语言：使用感恩、感谢的词语表达对支持的期待和感激之情，同时通过对患者的描述唤起共鸣和同情心。\n" +
                "    5. 呼吁广泛：呼吁更多人参与支持，扩大转发范围，提高筹款效率。\n" +
                "    6. 强调患者职业价值观：强调患者的奉献精神和对工作的热爱，增加共鸣和感召力。\n" +
                "    7. 使用实例证实：通过具体的案例描述，使转发内容更具说服力和真实性\n" +
                "`\n" +
                "# 规则\n" +
                "1. 转发语的编写应遵循以下规则:\n" +
                "`\n" +
                "     1. 依据`使用场景`表达尊敬的称呼，比如: 亲朋好友们， 尊敬的校友们， 尊敬的领导，各位同事，各位邻居等\n" +
                "     2. 增加渲染氛围的描述，对事项做简短说明。[生命之光即将熄灭, 迫切需要生命支持,生死攸关,拯救生命的最后希望，火速拯救,生死关头，仅剩一线生机,迫切需要紧急救援,生命垂危...]\n" +
                "     3. 介绍个人/患者信息，介绍患者的正向价值观，与下一点形成对比。\n" +
                "     4. 介绍疾病和病情信息，介绍治疗费用和家庭困难。\n" +
                "     5. 邀请帮忙【转发】【捐款】\n" +
                "`\n" +
                "2. 需要突出患者的身份以及对应的行为正向情感: [学生, 老师, 医护, 职员, 农民等可自行扩充]\n" +
                "3. 适当增加一个或多个感谢表情如: \uD83D\uDE4F, ❤️... 可自行扩充\n" +
                "4. 转发语汉字长度要求!!!: [[[必须大于 60 个汉字， 小于 100 个汉字]]]\n" +
                "5. 注意转发语不能违反事实，避免引起不良效果\n" +
                "\n" +
                "# 工作流程\n" +
                "1. 获取你需要的患者筹款信息，关键词\n" +
                "2. 提取创作标题的关键信息，尝试去构造案例的转发语， 不需要输出\n" +
                "3. 通过辩证的方式，对转发语进行检查是否满足[[[规则]]] 每一项的要求，对转发语进行优化以满足要求\n" +
                "4. 按照特定的输出格式，输出患者的文章转发语\n" +
                "\n" +
                "# 输出格式\n" +
                "以英文分号分隔，输出至少2个优化后的转发语，例如：\n" +
                "“文章转发语1;文章转发语2;...”\n" +
                "\n" +
                "# 优秀转发语示例\n" +
                "“各位好心人，您好！在生活的大海中，一场疾病就像是一道巨浪，将我们推向绝境。我是花名，我的丈夫张三患上了一\n" +
                "种疾病，需要长期治疗，但我们一家无法负担。现在我们急需您的帮助，治疗费用高昂，我们无法独自应对。每一份\n" +
                "转发，每一份爱心都是对我们最大的支持，让我们一起为张三的康复而努力！感谢您的关注和帮助！”\n" +
                "“亲爱的朋友们，这是一个朴实的农民在与病魔抗争。他的生命正处在辛勤劳作的时期，却因病痛而需要我们的帮助。让我们共同发起筹款，为他的治疗筹集资金，期待他早日康复，回到田野和生活的道路上。”\n" +
                "“一滴水也能汇成海洋，一份爱心也能温暖人心。我们的老师正在与病魔抗争，需要我们共同为他提供帮助和支持。让我们发起筹款，为他的治疗筹集资金，让我们的关心和关爱汇聚成力量，期待他早日康复。”\n" +
                "“医护人员，他们是生命的守护者，用自己的专业和爱心，为我们筑起健康的防线。在这个特殊的时刻，他们更需要我们的支持和关心。请大家关注医护人员筹款，献出自己的一份力量，让我们的医护人员感受到社会的关爱和温暖。”\n" + baseInfoPrompt;
    }

    private String getBaseInfoPrompt(AiGenerateBaseInfo aiGenerateBaseInfo) throws IllegalAccessException {
        StringBuilder description = new StringBuilder();
        Field[] fields = AiGenerateBaseInfo.class.getDeclaredFields(); // 获取所有字段

        for (Field field : fields) {
            AiBaseFieldMapping mapping = field.getAnnotation(AiBaseFieldMapping.class);
            if (mapping != null) {
                field.setAccessible(true); // 确保可以访问私有字段
                Object value = field.get(aiGenerateBaseInfo); // 获取字段的值
                if (value != null) { // 确保值不为空
                    // 根据字段名进行特殊处理
                    switch (field.getName()) {
                        case "diseaseProcess":
                            if (!"本人".equals(aiGenerateBaseInfo.getRaisePatientRelation())) {
                                value = "我的" + aiGenerateBaseInfo.getRaisePatientRelation() + aiGenerateBaseInfo.getPatientName() + value;
                            }
                            break;
                        default:
                            // 对于其他字段名，不做特殊处理
                            break;
                    }
                    description.append("  - ").append(mapping.aiPrompt()).append("：").append(value).append("\n");
                }
            }
        }
        return description.toString();

    }

    private String getContentStyle(String patientStanding) {

        if ("普通".equals(patientStanding)) {
            return "- 我希望这篇文章更加通俗易懂，更符合普通人的写作风格，让读者感觉更亲切和真实。\n";
        }
        if ("教师".equals(patientStanding)) {
            return "- 我希望这篇文章表达优美，言辞流畅，思维清晰，富有教育者的风范。\n";
        }
        if ("现役军人（患者本人、配偶、父母、子女及其配偶）".equals(patientStanding)) {
            return "- 我希望这篇文章刚毅坚定，语言简洁有力，充满军人的果断与决心。\n";
        }
        if ("医生/护士(在职)".equals(patientStanding)) {
            return "- 我希望这篇文章准确清晰，严谨专业，让人感受到医者的责任与温暖。\n";
        }

        return "";
    }

}
