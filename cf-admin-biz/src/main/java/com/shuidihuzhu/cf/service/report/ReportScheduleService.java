package com.shuidihuzhu.cf.service.report;

import com.shuidihuzhu.cf.enhancer.model.response.EhResponse;
import com.shuidihuzhu.cf.enums.report.ReportPayMethodEnum;
import com.shuidihuzhu.cf.model.admin.WashPaymentMethod;
import com.shuidihuzhu.cf.model.report.schedule.ReportScheduleVO;
import com.shuidihuzhu.common.web.model.Response;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ReportScheduleService {
    EhResponse<Void> add(int adminUserId, int caseId, Date targetTime);

    EhResponse<Void> update(int adminUserId, int id, Date targetTime);

    EhResponse<Void> remove(int adminUserId, int id);

    EhResponse<ReportScheduleVO> getByCaseId(int caseId);

    EhResponse<Void> done(int adminUserId, int id);

    EhResponse<List<ReportScheduleVO>> getListByOperatorId(int adminUserId);

    void onDelayHandle(String json);

    Response<Void> markPayMethod(int caseId, int payMethod, long operatorId, int legalLetter);

    ReportPayMethodEnum getPayMethodByCaseId(int caseId);

    Response<Integer> washTheData(List<WashPaymentMethod> dataList);
}
