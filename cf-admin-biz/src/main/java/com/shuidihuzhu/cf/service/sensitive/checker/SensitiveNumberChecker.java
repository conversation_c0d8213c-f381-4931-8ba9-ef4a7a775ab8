package com.shuidihuzhu.cf.service.sensitive.checker;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.constants.admin.PatternCons;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordResult;
import com.shuidihuzhu.cf.service.sensitive.adapter.ISensitiveAdapter;
import com.shuidihuzhu.cf.service.sensitive.processor.SensitiveProcessService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2019-04-11  15:07
 */
@Service
@RefreshScope
public class SensitiveNumberChecker implements ISensitiveChecker {

    @Value("${sensitive.is-check-sensitive-number:true}")
    private boolean isCheckSensitiveNumber;

    @Resource
    private SensitiveProcessService sensitiveProcessService;

    @Override
    public AdminWorkOrderConst.Task getTask() {
        return AdminWorkOrderConst.Task.SENSITIVE_NUMBER;
    }

    @Override
    public <T> OpResult<RiskWordResult> isHit(T data, ISensitiveAdapter<T> adapter) {
        return isHitSensitiveNumber(adapter.getContent(data));
    }

    private OpResult<RiskWordResult> isHitSensitiveNumber(String content) {
        if (!isCheckSensitiveNumber){
            return OpResult.createFailResult(AdminErrorCode.SENSITIVE_CHECK_OK);
        }

        // 在计算敏感号码策略的时候，去掉空格
        content = PatternCons.BLANK.matcher(content).replaceAll("");

        ArrayList<String> hitWords = Lists.newArrayList();

        // 字母策略
        RiskWordResult r1 = match(PatternCons.WX_PATTERN, content);
        if (r1 != null) {
            hitWords.addAll(r1.getHitWords());
        }

        // 数字策略1
        RiskWordResult r2 = match(PatternCons.SENSITIVE_NUMBER_PATTERN, content);
        if (r2 != null) {
            hitWords.addAll(r2.getHitWords());
        }

        // 数字策略2
//        String onlyNumber = PatternCons.UNLIKE_NUMBERS.matcher(content).replaceAll("");
//        int onlyNumberLength = StringUtils.length(onlyNumber);
//        if (onlyNumberLength >= 10 && onlyNumberLength <= 19) {
//            hitWords.add(onlyNumber);
//        }

        boolean isHit = CollectionUtils.isNotEmpty(hitWords);
        RiskWordResult r = new RiskWordResult(!isHit, content, hitWords);
        return OpResult.createSucResult(r);
    }

    private RiskWordResult match(Pattern pattern, String input) {
        Matcher matcher = pattern.matcher(input);
        List<String> hitWords = Lists.newArrayList();
        if (!matcher.find()) {
            return null;
        }
        matcher.reset();
        while (matcher.find()){
            String g = matcher.group();
            hitWords.add(g);
        }
        return new RiskWordResult(CollectionUtils.isEmpty(hitWords), input,hitWords);
    }

}
