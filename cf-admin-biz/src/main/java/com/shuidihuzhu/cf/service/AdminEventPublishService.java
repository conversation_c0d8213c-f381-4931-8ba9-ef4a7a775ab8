package com.shuidihuzhu.cf.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEvent;
import org.springframework.stereotype.Service;

/**
 * 发送spring事件
 */
@Service
public class AdminEventPublishService {

    @Autowired
    ApplicationContext applicationContext;


    public void publish(ApplicationEvent event){
        applicationContext.publishEvent(event);
    }
}
