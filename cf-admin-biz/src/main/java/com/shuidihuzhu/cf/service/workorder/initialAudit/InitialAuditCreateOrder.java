package com.shuidihuzhu.cf.service.workorder.initialAudit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.admin.constant.AdminWonActionConst;
import com.shuidihuzhu.cf.biz.admin.ChuciAnalyticsBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfMaterialVerityHistoryBiz;
import com.shuidihuzhu.cf.biz.risk.BlackListHighRiskWorkOrderRecordBiz;
import com.shuidihuzhu.cf.client.adminpure.enums.WorkOrderExtContentTypeEnum;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.feign.CfFirstApproveFeignClient;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.material.feign.CfMaterialReadClient;
import com.shuidihuzhu.cf.client.material.feign.CfMaterialWriteClient;
import com.shuidihuzhu.cf.client.material.feign.CfRaiseMaterialClient;
import com.shuidihuzhu.cf.client.material.model.*;
import com.shuidihuzhu.cf.client.material.model.materialField.MaterialExtKeyConst;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonRecordClient;
import com.shuidihuzhu.cf.dao.crowdfunding.CfUpgradeWorkOrderRecordDao;
import com.shuidihuzhu.cf.delegate.PreposeMaterialDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingOperationDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.enums.InitialAudit.InitialAuditNoSmartReason;
import com.shuidihuzhu.cf.enums.InitialAudit.InitialAuditRiskWorkOrderReason;
import com.shuidihuzhu.cf.enums.InitialAudit.TargetAmountAuditWorkOrderScene;
import com.shuidihuzhu.cf.enums.NotifyOnlineVolunteerEventEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.event.NotifyOnlineVolunteerEvent;
import com.shuidihuzhu.cf.model.admin.CfUpgradeWorkOrderRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CfMaterialVerityHistory;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.caseRepeat.InitialRepeatCaseView;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.CrowdfundingInitialAuditInfo;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cf.model.risk.BlackListHighRiskWorkOrderRecord;
import com.shuidihuzhu.cf.param.InitialAuditCreateOrderParam;
import com.shuidihuzhu.cf.risk.client.aegis.EngineAnalysisClient;
import com.shuidihuzhu.cf.risk.client.risk.AccidentCaseClient;
import com.shuidihuzhu.cf.risk.client.risk.BlacklistVerifyClient;
import com.shuidihuzhu.cf.risk.client.rpc.DiseaseClassifyFeignClientV2;
import com.shuidihuzhu.cf.risk.client.rpc.DiseaseClient;
import com.shuidihuzhu.cf.risk.model.admin.blacklist.BlacklistHitHighRiskDto;
import com.shuidihuzhu.cf.risk.model.aegis.RiskAnalysisDto;
import com.shuidihuzhu.cf.risk.model.aegis.RiskObject;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseAmountReasonableTypeEnum;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyEnum;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyResultEnum;
import com.shuidihuzhu.cf.risk.model.enums.highrisk.HighRiskAutoEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistCallPhaseEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.TriggerTimingEnum;
import com.shuidihuzhu.cf.risk.model.risk.DiseaseAmountResultRecord;
import com.shuidihuzhu.cf.risk.model.risk.Participate;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseClassifyVOV2;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyRequest;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyResponse;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyResultInfo;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.HighRiskJudgeConst;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.HighRiskJudgeResult;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cf.service.crowdfunding.CfAdminLimitService;
import com.shuidihuzhu.cf.service.risk.highrisk.HighRiskService;
import com.shuidihuzhu.cf.service.workorder.WorkOrderExtService;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditCaseDetail;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditOperationItem;
import com.shuidihuzhu.cf.vo.approve.CreditInfoVO;
import com.shuidihuzhu.cf.vo.crowdfunding.InitialAuditSmartRejectVo;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import com.shuidihuzhu.client.cf.clewtrack.model.CfUserInvitedLaunchCaseRecordModel;
import com.shuidihuzhu.client.cf.clewtrack.model.ChannelRefine.ChannelRefineResuleEnum;
import com.shuidihuzhu.client.cf.growthtool.client.CfAppPushCrmMsgFeignClient;
import com.shuidihuzhu.client.cf.growthtool.client.CfBdCaseInfoFeignClient;
import com.shuidihuzhu.client.cf.growthtool.client.CfGrowthtoolVolunteerFeignClient;
import com.shuidihuzhu.client.cf.growthtool.client.ClewPreproseMaterialFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.growthtool.model.ReportRelation;
import com.shuidihuzhu.client.cf.workorder.CfChuciWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCoreFeignClient;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCreateParam;
import com.shuidihuzhu.client.cf.workorder.model.ChuciWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderLevel;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditOperateService.HIGH_RISK_EMERGENCY;

/**
 * <AUTHOR>
 * @DATE 2019/6/27
 */
@Service
@RefreshScope
@Slf4j
public class InitialAuditCreateOrder {

    public static Set<HighRiskAutoEnum> UPGRADE_HIT_CODES = Sets.newHashSet(HighRiskAutoEnum.GFXY001, HighRiskAutoEnum.GFXY005, HighRiskAutoEnum.GFXY003, HighRiskAutoEnum.GFXY007);

    public static final String AEGIS_MODEL_GUID = "8416765febc945b991f9ff947297881a";
    private static final List<Integer> ORDER_TYPES = Lists.newArrayList(WorkOrderType.yiliaoshenhe.getType(),
            WorkOrderType.highriskshenhe.getType(), WorkOrderType.ai_photo.getType(), WorkOrderType.ai_content.getType(),
            WorkOrderType.ai_erci.getType(), WorkOrderType.shenhe.getType());
    @Autowired
    private AlarmClient alarmClient;
    @Autowired
    private ClewPreproseMaterialFeignClient clewPreproseMaterialFeignClient;
    @Autowired
    private CfBdCaseInfoFeignClient cfBdCaseInfoFeignClient;
    @Autowired
    private CfFirstApproveFeignClient firstApproveClient;
    @Autowired
    CfMaterialReadClient materialReadClient;
    @Autowired
    PreposeMaterialDelegate preposeMaterialDelegate;
    @Autowired
    CrowdfundingFeignClient crowdfundingFeignClient;
    @Autowired
    private CfChuciWorkOrderClient chuciWorkOrderClient;
    @Autowired
    private CfWorkOrderClient workOrderClient;
    @Autowired
    private ChuciAnalyticsBiz chuciAnalyticsBiz;
    @Autowired
    private AdminApproveService adminApproveService;
    @Autowired
    private ICrowdfundingOperationDelegate crowdfundingOperationDelegate;
    @Autowired
    private CfGrowthtoolVolunteerFeignClient cfGrowthtoolVolunteerFeignClient;
    @Autowired
    private WorkOrderExtService workOrderSnapshotService;
    @Autowired
    private CfAppPushCrmMsgFeignClient appPushCrmMsgFeignClient;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private CfAdminLimitService cfAdminLimitService;
    @Autowired
    private CfRaiseMaterialClient cfRaiseMaterialClient;
    @Resource
    private IRiskDelegate riskDelegate;
    @Autowired
    private DiseaseClient diseaseClient;
    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;
    @Autowired
    private InitialAuditSearchService auditSearchService;
    @Autowired
    private DiseaseClassifyFeignClientV2 diseaseClassifyFeignClientV2;
    @Autowired
    private CfMaterialWriteClient cfMaterialWriteClient;
    @Autowired
    private EngineAnalysisClient engineAnalysisClient;
    @Resource
    private CfMaterialReadClient cfMaterialReadClient;
    @Resource
    private AccidentCaseClient accidentCaseClient;
    @Resource
    private HighRiskService highRiskService;
    @Resource
    private WonRecordClient wonRecordClient;
    @Resource
    private BlacklistVerifyClient blacklistVerifyClient;
    @Resource
    private BlackListHighRiskWorkOrderRecordBiz blackListHighRiskWorkOrderRecordBiz;
    @Resource
    private InitialAuditOperateService initialAuditOperateService;
    @Resource
    private CfMaterialVerityHistoryBiz cfMaterialVerityHistoryBiz;
    @Resource
    private InitialAuditTargetAmountReasonableService initialAuditTargetAmountReasonableService;
    @Resource
    private WorkOrderCoreFeignClient workOrderCoreFeignClient;
    @Resource
    private CfUpgradeWorkOrderRecordDao cfUpgradeWorkOrderRecordDao;
    @Autowired
    private ApplicationContext applicationContext;
    @Value("${apollo.admin.high-risk.v2.enable:true}")
    private boolean highRiskV2Enable;
    @Value("${xian.xia.bd.smart.audit:false}")
    private boolean xianXiaBdSmartAudit;
    @Value("${can.not.create.dianhua.work.channel:''}")
    private String can_not_create_dianhua_work_channel;
    /**
     *  填写了初审时填写增信材料是否直接生成高风险工单,true直接生成
     */
    @Value("${direct.create.high.risk:true}")
    private boolean direct_create_high_risk;
    /**
     * 图片工单驳回项id
     */
    @Value("#{'${apollo.single.work.order.turn.down.ids:}'.split(',')}")
    private List<Integer> singleTurnDownIds;
    /**
     * 文章工单驳回项id
     */
    @Value("#{'${apollo.double.work.order.turn.down.ids:}'.split(',')}")
    private List<Integer> doubleTurnDownIds;
    /**
     *  智能审核2.0开关
     */
    @Value("${apollo.smart.audit:false}")
    private boolean apollo_smart_audit;

    @Value("#{'${apollo.target.amount.reasonable.reject.ids:}'.split(',')}")
    private List<Integer> targetAmountReasonableRejectIds;

    @Value("${authenticity.case.work.order.assign.group.id:0}")
    private long authenticityCaseWorkOrderAssignGroupId;

    /**
     * 驳回再生成工单才走这个地方
     *
     * @param caseId    案例Id
     * @param condition 创建工单条件   0  无   1  解锁黑名单
     * @param checkTargetAmount 是否检查目标金额
     */
    public void createChuci(int caseId, int condition, boolean checkTargetAmount) {
        log.info("初审工单驳回 caseId:{}, direct_create_high_risk:{}", caseId, direct_create_high_risk);

        // 根据案例ID获取众筹信息
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
        if (Objects.isNull(crowdfundingInfo)) {
            // 若未查到众筹信息，直接返回，不生成工单
            return;
        }

        // 判断案例是否为真实性案例（如果是，直接返回）不再走了，不用看
        boolean authenticityCase = authenticityCase(crowdfundingInfo);
        if (authenticityCase) {
            return;
        }

        /**
         * 获取当前案例的最后一条指定类型工单
         * yiliaoshenhe(16, "医疗审核", "chucishenhe:yiliao", true)
         * highriskshenhe(18, "高风险工单", "chucishenhe:highrisk", true)
         * ai_photo(56, "图片录入工单", "chucishenhe:ai_photo", false)
         * ai_content(55, "文章录入工单", "chucishenhe:ai_content", true)
         * ai_erci(57, "二次审核工单", "chucishenhe:ai_erci", true)
         * shenhe(5, "审核工单", "chucishenhe:shenhe", true)
         */
        Response<WorkOrderVO> lastWorkOrder = workOrderClient.getLastWorkOrderByTypes(caseId, ORDER_TYPES);
        WorkOrderVO workOrderVO = Optional.ofNullable(lastWorkOrder)
                .map(Response::getData)
                .orElse(null);

        // 如果目标金额不合理，就尝试创建目标金额审核工单，不再创建其他工单
        // checkTargetAmount 为 false 则不创建目标金额审核工单
        // 补充：查询最近工单的案例材料审核操作历史记录表，如果拒绝原因是目标金额不合理，则创建目标金额审核工单
        boolean rejectInitialTargetAmount = rejectInitialTargetAmount(workOrderVO, crowdfundingInfo, checkTargetAmount);
        if (rejectInitialTargetAmount) {
            return;
        }

        // 如果命中高风险策略就返回true，并尝试生成高风险工单，不再创建其他工单
        // condition == 0 会进行检查是否可以创建初审工单
        boolean rejectCreateHighOrMedical = createHighRiskWorkOrder(caseId, 0);
        if (rejectCreateHighOrMedical) {
            return;
        }

        // 如果上一次工单是医疗审核工单，则再次创建医疗审核工单，不再创建其他工单
        if (Objects.nonNull(workOrderVO) && workOrderVO.getOrderType() == WorkOrderType.yiliaoshenhe.getType()) {
            log.info("驳回创建医疗审核工单 : {}, {}", caseId, workOrderVO.getWorkOrderId());
            createYiliaoWork(caseId);
            return;
        }

        // 判断是否走智能审核
        InitialAuditSmartRejectVo initialAuditSmartRejectVo = judgeInitialReviewInformation(caseId);
        InitialAuditNoSmartReason initialAuditNoSmartReason = initialAuditSmartRejectVo.getInitialAuditNoSmartReason();

        // 如果 ${apollo.smart.audit:false} 并且原因是 SUCCESS(0, "智能审核流程")，则走智能审核，并且返回
        if (apollo_smart_audit && Objects.equals(initialAuditNoSmartReason, InitialAuditNoSmartReason.SUCCESS)) {
            createSmartAudit(caseId, condition);
            return;
        }

        // 否则，创建二次审核工单，如果 condition == 0 会进行检查是否可以创建初审工单
        createAiErCi(caseId, condition, initialAuditSmartRejectVo);
    }


    /**
     * 判断是否为真实性案例，如果是则直接创建AI二次审核工单并返回 true，表示拦截后续流程
     *
     * @param crowdfundingInfo 众筹案例信息
     * @return true 表示为真实性案例，已生成二次审核工单；false 表示不是，流程可继续
     */
    public boolean authenticityCase(CrowdfundingInfo crowdfundingInfo) {
        // 判断 materialPlanId 是否为 PLAN_140（即：真实性增信材料专用模板）
        // 如果不是 PLAN_140，说明不是真实性案例，返回 false，流程继续
        if (crowdfundingInfo.getMaterialPlanId() != MaterialPlanVersion.PLAN_140.getCode()) {
            return false;
        }

        // 以下为处理真实性案例的逻辑

        // 构建初审工单对象，指定分配组、案例ID 和工单类型（二次审核）
        ChuciWorkOrder chuciWorkOrder = new ChuciWorkOrder();
        chuciWorkOrder.setAssignGroupId(authenticityCaseWorkOrderAssignGroupId); // 分配到真实性案例专属工单处理组
        chuciWorkOrder.setCaseId(crowdfundingInfo.getId()); // 设置案例ID
        chuciWorkOrder.setOrderType(WorkOrderType.ai_erci.getType()); // 设置工单类型为“二次审核”

        // 构建初审工单创建参数，包括工单本体、案例ID、条件和智能拒绝原因（标注为真实性案例）
        InitialAuditCreateOrderParam orderParam = InitialAuditCreateOrderParam.builder()
                .caseId(crowdfundingInfo.getId())
                .chuciWorkOrder(chuciWorkOrder)
                .condition(0) // condition 为 0，表示无特殊条件（例如解锁黑名单等）
                .noSmartAuditReason(InitialAuditNoSmartReason.AUTHENTICITY_CASE.getMsg()) // 设置智能拒绝原因（真实性案例）
                .build();

        // 调用创建工单方法，实际落库或发送工单
        create(orderParam);

        // 返回 true，表示已经拦截后续流程并创建了二次审核工单
        return true;
    }

    /**
     * 判断是否因为“目标金额不合理”而拦截创建后续工单
     *
     * @param workOrderVO        当前案例的最近一条工单
     * @param crowdfundingInfo   当前案例的众筹信息
     * @param checkTargetAmount  是否需要检查目标金额（控制是否生成目标金额合理性工单）
     * @return true 表示需要拦截流程并生成“目标金额合理性”工单；false 表示不需要拦截，流程可继续
     */
    private boolean rejectInitialTargetAmount(WorkOrderVO workOrderVO, CrowdfundingInfo crowdfundingInfo, boolean checkTargetAmount) {
        // 如果配置中的 Apollo 值 targetAmountReasonableRejectIds 为空（代表无配置），直接放行
        // 例如配置为：apollo.target.amount.reasonable.reject.ids=10,12,13
        if (CollectionUtils.isEmpty(targetAmountReasonableRejectIds)) {
            return false;
        }

        // 如果当前案例没有最近的工单信息，放行
        if (Objects.isNull(workOrderVO)) {
            return false;
        }

        // 查询该工单对应的“基本信息校验记录”，字段标签为 STAT_BASE_INFO_TAG
        /**
         * SELECT *
         * FROM
         *   cf_material_verity_history
         * WHERE
         *   `case_id` = #{caseId}
         *   AND `work_order_id` = #{workOrderId}
         *   AND `material_id` = 101
         * ORDER BY
         *   id DESC
         * LIMIT 1;
         *
         * CREATE TABLE `cf_material_verity_history` (
         *   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
         *   `case_id` int(11) NOT NULL DEFAULT '0' COMMENT '案例id',
         *   `approve_control_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '对应cf_case_approve_control_record的id',
         *   `info_id` varchar(50) NOT NULL DEFAULT '' COMMENT '案例uuid',
         *   `handle_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '操作的类型 1通过  2驳回',
         *   `material_id` int(11) NOT NULL DEFAULT '0' COMMENT '处理的是哪项材料（6项材料对应）cf_commit_verify_item表id',
         *   `refuse_ids` varchar(128) NOT NULL DEFAULT '' COMMENT '驳回的具体原因id用逗号分隔 与material_id 对应，当操作类型为通过时，这里没有值',
         *   `material_info` text COMMENT '具体的材料信息，与material_id 对应',
         *   `material_pic_info` text COMMENT '案例的图片信息，与case_id 对应',
         *   `material_info_ext` text COMMENT '材料的扩展信息，与case_id material_id对应',
         *   `operator_id` int(11) NOT NULL DEFAULT '0' COMMENT '当前的处理人',
         *   `operator_detail` varchar(128) NOT NULL DEFAULT '' COMMENT '组织-处理人',
         *   `material_op_time` varchar(30) NOT NULL DEFAULT '' COMMENT '用户材料的操作时间',
         *   `material_op_time_type` int(11) NOT NULL DEFAULT '0' COMMENT '用户材料的操作类型',
         *   `operator_type` int(11) NOT NULL DEFAULT '0' COMMENT '当前处理人的组织 1表示水滴筹-筹款顾问-石家庄顾问团队-材料审核组',
         *   `comment` varchar(5000) NOT NULL DEFAULT '' COMMENT '处理时，运营手动输入的评论',
         *   `work_order_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '工单id',
         *   `is_delete` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否逻辑删除 0有效，1删除',
         *   `create_time` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
         *   `update_time` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
         *   `material_info_encrypt` text COMMENT '加密--具体的材料信息，与material_id 对应',
         *   `material_info_ext_encrypt` text COMMENT '加密--材料的扩展信息，与case_id material_id对应',
         * ) ENGINE = InnoDB AUTO_INCREMENT = 20710041 DEFAULT CHARSET = utf8mb4 COMMENT = '案例材料审核操作历史记录表'
         */
        CfMaterialVerityHistory cfMaterialVerityHistory = cfMaterialVerityHistoryBiz.selectLatestByWorkOrder(
                workOrderVO.getCaseId(),
                workOrderVO.getWorkOrderId(),
                InitialAuditOperateService.STAT_BASE_INFO_TAG // 统计用初审图文驳回记录 101
        );

        // 如果没有查到这条记录，也放行
        if (Objects.isNull(cfMaterialVerityHistory)) {
            return false;
        }

        // 获取被拒绝的原因ID（逗号分隔的字符串形式，例如 "10,15,99"）
        String refuseIds = cfMaterialVerityHistory.getRefuseIds();

        // 如果拒绝原因为空，也放行
        if (StringUtils.isEmpty(refuseIds)) {
            return false;
        }

        // 拒绝原因中的ID是否包含在配置的目标金额合理性拦截ID中（有交集就拦截）
        long count = Splitter.on(",").splitToList(refuseIds)
                .stream()
                // ${apollo.target.amount.reasonable.reject.ids:}'.split(',')
                .filter(f -> targetAmountReasonableRejectIds.contains(Integer.valueOf(f))) // 若命中配置ID，则计数
                .count();

        // 没有命中任何拦截ID，也放行
        if (count == 0) {
            return false;
        }

        // 命中了拦截ID，尝试生成目标金额合理性工单，并返回其结果（true 表示已生成工单，流程应中断）
        return initialAuditTargetAmountReasonableService.createTargetAmountReasonableWorkOrder(
                crowdfundingInfo,
                // REJECT(2, "驳回")
                TargetAmountAuditWorkOrderScene.REJECT.getType(), // 场景：拒绝后重新审核
                checkTargetAmount // 控制是否真实生成工单
        );
    }

    /**
     * 通用生成高风险工单
     */
    public boolean createHighRiskWorkOrder(int caseId, int condition) {
        boolean canCreateHighRisk = canCreateHighRisk(caseId);
        log.info("createInitialAuditOrder caseId:{}, createHighRiskOrder:{}, credit:{}", caseId, direct_create_high_risk, canCreateHighRisk);
        InitialAuditCreateOrderParam createOrderParam = InitialAuditCreateOrderParam.builder()
                .caseId(caseId)
                .realRisk(canCreateHighRisk)
                .condition(condition)
                .build();
        // 命中高风险原因
        StringJoiner riskWorkOrderReason = new StringJoiner("||");

        // 上次是高风险工单
        Response<WorkOrderVO> lastWorkOrder = workOrderClient.getLastWorkOrderByTypes(caseId, ORDER_TYPES);
        WorkOrderVO workOrderVO = Optional.ofNullable(lastWorkOrder)
                .map(Response::getData)
                .orElse(null);
        if (Objects.nonNull(workOrderVO) && workOrderVO.getOrderType() == WorkOrderType.highriskshenhe.getType()) {
            log.info("驳回创建上次高风险订单 : {}, {}", caseId, workOrderVO.getWorkOrderId());
            riskWorkOrderReason.add(InitialAuditRiskWorkOrderReason.LAST_IS_RISK.getMsg());
        }
        // 开关是否打开
        if (direct_create_high_risk) {
            riskWorkOrderReason.add(InitialAuditRiskWorkOrderReason.RISK_SWITCH.getMsg());
        }
        // 增信是否高风险
        if (canCreateHighRisk) {
            riskWorkOrderReason.add(InitialAuditRiskWorkOrderReason.CREDIT_RISK.getMsg());
        }
        // 是否事故案例
        boolean mistakeCaseRiskWorkOrder = accidentCaseHighWorkOrder(caseId);
        if (mistakeCaseRiskWorkOrder) {
            riskWorkOrderReason.add(InitialAuditRiskWorkOrderReason.MISTAKE_CASE.getMsg());
        }
        // 是否重复案例近期高风险
        boolean repeatRiskWorkOrder = repeatRiskWorkOrder(caseId);
        if (repeatRiskWorkOrder) {
            riskWorkOrderReason.add(InitialAuditRiskWorkOrderReason.REPEAT_RISK.getMsg());
        }
        // 黑名单高风险 blacklistHitHighRiskDto 用的话需要判空 🌟
        BlacklistHitHighRiskDto blacklistHitHighRiskDto = blackListHighRiskWorkOrder(createOrderParam);
        if (Objects.nonNull(blacklistHitHighRiskDto)) {
            riskWorkOrderReason.add(InitialAuditRiskWorkOrderReason.BLACK_LIST.getMsg());
        }

        // 如果命中策略生成高风险工单
        String hitHighRiskReason = riskWorkOrderReason.toString();
        if (StringUtils.isNotBlank(hitHighRiskReason)) {

            createOrderParam.setRiskWorkOrderReason(hitHighRiskReason);
            log.info("createInitialAuditOrder caseId:{}, reason:{}", caseId, createOrderParam.getRiskWorkOrderReason());
            long workOrderId = createHighRiskWork(createOrderParam);

            // 夜间生成高风险工单发送消息给顾问
            LocalTime currentTime = LocalTime.now();
            LocalTime start = LocalTime.of(23, 50);
            LocalTime end = LocalTime.of(8, 0);
            if (currentTime.isAfter(start) || currentTime.isBefore(end) || currentTime.equals(start) || currentTime.equals(end)) {
                sendMsgToVolunteerAtNight(createOrderParam);
            }

            // 生成高风险工单后一些其他的操作
            dealHighRisk(workOrderId, createOrderParam, blacklistHitHighRiskDto);

            // 命中策略自动升应急组
            if (canUpgrade(caseId)) {
                initialAuditOperateService.upgradeEmergency(workOrderId, 0, "", "命中高风险策略，自动升级应急组");
            }
        }

        return StringUtils.isNotBlank(hitHighRiskReason);
    }

    private void dealHighRisk(long workOrderId, InitialAuditCreateOrderParam orderParam, BlacklistHitHighRiskDto blacklistHitHighRiskDto) {

        if (workOrderId == 0 || Objects.isNull(blacklistHitHighRiskDto)) {
            log.info("InitialAuditCreateOrder blackListHighRiskWork createHighRiskWork is fail {}", orderParam);
            return ;
        }

        // 黑名单命中记录保存
        BlackListHighRiskWorkOrderRecord blackListHighRiskWorkOrderRecord = new BlackListHighRiskWorkOrderRecord();
        blackListHighRiskWorkOrderRecord.setBlackListId(blacklistHitHighRiskDto.getBlackListId());
        blackListHighRiskWorkOrderRecord.setWorkOrderId(workOrderId);
        blackListHighRiskWorkOrderRecord.setCaseId(orderParam.getCaseId());
        blackListHighRiskWorkOrderRecord.setHitIdCard(blacklistHitHighRiskDto.getHitIdCard());
        blackListHighRiskWorkOrderRecord.setHitType(blacklistHitHighRiskDto.getHitType());
        blackListHighRiskWorkOrderRecordBiz.insert(blackListHighRiskWorkOrderRecord);
    }

    private void sendMsgToVolunteerAtNight(InitialAuditCreateOrderParam orderParam) {
        FeignResponse<CrowdfundingInfo> response = crowdfundingFeignClient.getCaseInfoById(orderParam.getCaseId());
        if (response == null || response.notOk()) {
            return;
        }
        CrowdfundingInfo crowdfundingInfo = response.getData();

        //获取首次审核资料
        FeignResponse<CfFirsApproveMaterial> materialRsp = firstApproveClient.getCfFirstApproveMaterialByCaseId(orderParam.getCaseId());
        if(materialRsp.notOk() || materialRsp.getData() == null) {
            return;
        }
        CfFirsApproveMaterial firsApproveMaterial = materialRsp.getData();

        String content = "您提交审核的案例需做进一步核实\n"
                + "您为患者：" + firsApproveMaterial.getPatientRealName()
                + "发起的案例已于" + DateUtil.getLong2LStr(System.currentTimeMillis())
                + "提交审核，信息需做进一步核实，审核人员将于早8:30后优先进行审核，请告知筹款人注意接听电话";


        //线下顾问发起
        Response<CfBdCaseInfoDo> bdCaseResponse = cfBdCaseInfoFeignClient.getBdCaseInfoByInfoUuid(crowdfundingInfo.getInfoId());
        if (bdCaseResponse != null && bdCaseResponse.getData() != null) {
            CfBdCaseInfoDo bdCaseInfoDo = bdCaseResponse.getData();
            if (StringUtils.isNotEmpty(bdCaseInfoDo.getUniqueCode())) {
                appPushCrmMsgFeignClient.sendAppPushCrmByUniqueCode(bdCaseInfoDo.getUniqueCode(), "您提交审核的案例需做进一步核实", "您提交审核的案例需做进一步核实", content);
            }
            if (StringUtils.isNotEmpty(bdCaseInfoDo.getMisId())) {
                //发飞书消息
                alarmClient.sendByUser(Lists.newArrayList(bdCaseInfoDo.getMisId()), content);
            }
        } else {    // 线上 1v1 发起
            Response<ReportRelation> reportRelationResponse = clewPreproseMaterialFeignClient.getByCaseId(orderParam.getCaseId());
            ReportRelation reportRelation = Optional.ofNullable(reportRelationResponse).map(Response::getData).orElse(null);
            if (reportRelation != null) {
                if (StringUtils.isNotEmpty(reportRelation.getUniqueCode())) {
                    appPushCrmMsgFeignClient.sendAppPushCrmByUniqueCode(reportRelation.getUniqueCode(), "您提交审核的案例需做进一步核实", "您提交审核的案例需做进一步核实", content);
                }
                if (StringUtils.isNotEmpty(reportRelation.getMis())) {
                    //发飞书消息
                    alarmClient.sendByUser(Lists.newArrayList(reportRelation.getMis()), content);
                }
            }
        }
    }
    private boolean repeatRiskWorkOrder(int caseId) {
        CfFirsApproveMaterial firsApproveMaterial = riskDelegate.getCfFirsApproveMaterialByInfoId(caseId);
        if (Objects.isNull(firsApproveMaterial)) {
            log.info("createLowRiskWorkOrder firsApproveMaterial is null {}", caseId);
            return false;
        }

        InitialRepeatCaseView.InitialRepeatRiskParam param = InitialRepeatCaseView.InitialRepeatRiskParam.convertFromFirstMaterial(firsApproveMaterial);
        RiskAnalysisDto analysisDto = new RiskAnalysisDto();
        analysisDto.setReqId(String.valueOf(caseId));
        analysisDto.setModelGuid(AEGIS_MODEL_GUID);
        analysisDto.setEventInfo(param);
        Response<Map<String, RiskObject>> analyze = engineAnalysisClient.analyze(analysisDto);
        if (Objects.isNull(analyze) || MapUtils.isEmpty(analyze.getData())) {
            return false;
        }
        List<Integer> allCaseIds = analyze.getData()
                .values()
                .stream()
                .map(RiskObject::getRiskResult)
                .filter(StringUtils::isNotEmpty)
                .flatMap(m -> {
                    JSONArray jsonArray = JSONObject.parseArray(m);
                    List<Object> list = CollectionUtils.isEmpty(jsonArray) ? new ArrayList<>() : jsonArray;
                    return list.stream().map(String::valueOf);
                })
                .collect(Collectors.toList())
                .stream()
                .map(Integer::valueOf)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(allCaseIds)) {
            log.info("createLowRiskWorkOrder search is empty {}", caseId);
            return false;
        }

        for (Integer caseInfoId : allCaseIds) {
            Response<WorkOrderVO> lastWorkOrder = workOrderClient.getLastWorkOrderByTypes(caseInfoId, Collections.singletonList(WorkOrderType.highriskshenhe.getType()));
            if (Objects.nonNull(lastWorkOrder) && lastWorkOrder.ok() && Objects.nonNull(lastWorkOrder.getData())) {
                log.info("createInitialAuditOrder createLowRiskWorkOrder {}", lastWorkOrder.getData());
                return true;
            }
        }

        log.info("createLowRiskWorkOrder no high risk work order {}", caseId);
        return false;
    }

    private boolean accidentCaseHighWorkOrder(int caseId) {
        Participate participate = Participate.builder()
                .caseId(caseId)
                .triggerTiming(TriggerTimingEnum.PREJUDICATION_BEFORE.getDesc())
                .build();
        Response<List<Integer>> followAccidentCaseStrategy = accidentCaseClient.followAccidentCaseStrategy(participate);
        if (Objects.isNull(followAccidentCaseStrategy) || followAccidentCaseStrategy.notOk() || CollectionUtils.isEmpty(followAccidentCaseStrategy.getData())) {
            return false;
        }
        log.info("accidentCaseHighWorkOrder mistakeCaseRiskWorkOrder is accident {}, {}", caseId, followAccidentCaseStrategy);
//        InitialAuditCreateOrderParam createOrderParam = InitialAuditCreateOrderParam.builder()
//                .caseId(caseId)
//                .realRisk(false)
//                .condition(0)
//                .riskWorkOrderReason(InitialAuditRiskWorkOrderReason.MISTAKE_CASE.getMsg())
//                .build();
//        createHighRiskWork(createOrderParam);
        return true;
    }

    /**
     * 本模块只有提交了增信才有可能生成高风险工单
     */
    public void createAiErCi(int caseId, int condition, InitialAuditSmartRejectVo initialAuditSmartRejectVo) {
        log.info("create work order direct caseId:{}", caseId);
        ChuciWorkOrder w = new ChuciWorkOrder();
        w.setCaseId(caseId);
        w.setOrderType(WorkOrderType.ai_erci.getType());
        InitialAuditCreateOrderParam orderParam = InitialAuditCreateOrderParam.builder()
                .caseId(caseId)
                .chuciWorkOrder(w)
                .condition(condition)
                .noSmartAuditReason(!apollo_smart_audit ? InitialAuditNoSmartReason.SWITCH_OFF.getMsg() : initialAuditSmartRejectVo.getInitialAuditNoSmartReason().getMsg())
                .diseaseName(initialAuditSmartRejectVo.getDiseaseName())
                .build();
        create(orderParam);
    }


    /**
     * 创建医疗审核工单
     */
    public void createYiliaoWork(int caseId) {
        log.info("create yiliaoshenhe work caseId:{}", caseId);
        ChuciWorkOrder w = new ChuciWorkOrder();
        w.setCaseId(caseId);
        w.setOrderType(WorkOrderType.yiliaoshenhe.getType());
        InitialAuditCreateOrderParam orderParam = InitialAuditCreateOrderParam.builder()
                .caseId(caseId)
                .chuciWorkOrder(w)
                .condition(0)
                .build();
        create(orderParam);
        applicationContext.publishEvent(new NotifyOnlineVolunteerEvent(NotifyOnlineVolunteerEventEnum.MEDICAL_WORK_ORDER_CRATE_EVENT, caseId));
    }

    public long createHighRiskWork(InitialAuditCreateOrderParam orderParam) {
        log.info("create highRisk work :{}", orderParam);
        ChuciWorkOrder w = new ChuciWorkOrder();
        w.setCaseId(orderParam.getCaseId());
        w.setOrderType(WorkOrderType.highriskshenhe.getType());
        w.setRealRiskLevel(orderParam.isRealRisk() ? "1" : "0");
        orderParam.setChuciWorkOrder(w);
        Response<Long> response = create(orderParam);

        // 初审有高风险工单的给案例打标签
        CfMaterialAddOrUpdateVo build = CfMaterialAddOrUpdateVo.builder()
                .materialName(MaterialExtKeyConst.init_audit_create_work_order_case_risk)
                .materialValue(String.valueOf(0))
                .materialLabel("")
                .materialExt("")
                .caseId(orderParam.getCaseId())
                .build();
        cfMaterialWriteClient.addOrUpdateByFields(orderParam.getCaseId(), Collections.singletonList(build));
        return Optional.ofNullable(response)
                .map(Response::getData)
                .orElse(0L);
    }

    public void createYiYuanBuChongWork(int caseId) {
        log.info("create createYiYuanBuChongWork work caseId:{}", caseId);
        ChuciWorkOrder w = new ChuciWorkOrder();
        w.setCaseId(caseId);
        w.setOrderType(WorkOrderType.bu_chong_yi_yuan_xin_xi.getType());

        try {
            //创建补充医院信息工单
            Response<Long> response = chuciWorkOrderClient.createChuci(w);
            log.info("createYiYuanBuChongWork caseId={} response={}", caseId, JSON.toJSONString(response));
        } catch (Exception e) {
            log.error("createYiYuanBuChongWork caseId={}", caseId, e);
        }
    }

    public Response<Long> create(InitialAuditCreateOrderParam param) {
        int caseId = param.getCaseId();
        int condition = param.getCondition();
        ChuciWorkOrder chuciWorkOrder = param.getChuciWorkOrder();
        try {
            //创建工单前  先检查处理状态   如果是审核通过   不再创建工单
            CrowdfundingInitialAuditInfo auditInfo = crowdfundingOperationDelegate.selectCrowdfundingInitialAuditInfoByCaseId(caseId);
            if (auditInfo != null) {
                if (auditInfo.getBaseInfo() == InitialAuditItem.MaterialStatus.PASS.getCode() && auditInfo.getFirstApproveInfo() == InitialAuditItem.MaterialStatus.PASS.getCode()
                        && auditInfo.getCreditInfo() == InitialAuditItem.MaterialStatus.PASS.getCode()) {
                    log.info("no createChuci auditInfo={}", auditInfo);
                    return NewResponseUtil.makeSuccess(0L);
                }
            }
            //检查是否案例未被“限制预审通过”命中or被“限制预审通过”命中但已经操作解锁
            boolean canCreate = checkCanCreate(caseId, condition);
            if (!canCreate) {
                log.info("初审创建工单被风控拦截 caseId:{}", caseId);
                return NewResponseUtil.makeSuccess(0L);
            }
            //创建初审工单
            Response<Long> response = chuciWorkOrderClient.createChuci(chuciWorkOrder);
            log.info("createChuci caseId={} response={}", caseId, JSON.toJSONString(response));

            param.setWorkOrderId(Objects.nonNull(response.getData()) ? response.getData() : 0L);
            chuciAnalyticsBiz.initialVerifySubmit(param);
            chuciAnalyticsBiz.content1v1Verify(param);

            return response;

        } catch (Exception e) {
            log.error("createChuci caseId={}", caseId, e);
        }
        return NewResponseUtil.makeSuccess(0L);
    }

    public boolean checkCanCreate(int caseId, int condition) {
        boolean result = true;
        boolean flag = cfAdminLimitService.caseInLimit(caseId, BlacklistCallPhaseEnum.SUBMIT_PRE_TRIAL);
        if (flag) {
            result = false;
            log.info("初审创建工单被风控拦截 A caseId:{}", caseId);
        }
        // 非解锁才需检查
        // condition == 0 会调用 checkCanCreateChuCi(caseId) 进行检查是否可以创建初审工单
        if (condition == 0) {
            boolean canCreate = cfAdminLimitService.checkCanCreateChuCi(caseId);
            if (!canCreate) {
                log.info("初审创建工单被风控拦截 B caseId:{}", caseId);
                result = false;
            }
        }
        return result;
    }


    /**
     * BD或者微信1v1发起的案例
     */
    private ChannelRefineResuleEnum getChannel(int caseId) {
        //获取渠道
        Map<Integer, CfUserInvitedLaunchCaseRecordModel> recordModelMap = adminApproveService.getCaseChannelRecordMap(Lists.newArrayList(caseId));
        CfUserInvitedLaunchCaseRecordModel recordModel = recordModelMap.get(caseId);
        if (recordModel != null) {
            log.info("create chushen work channel:{},serviceUserInfo:{},caseId:{}", recordModel.getChannel(), recordModel.getServiceUserInfo(shuidiCipher), caseId);
            return ChannelRefineResuleEnum.parse(recordModel.getChannel());
        }
        return null;
    }

    private Set<ChannelRefineResuleEnum> parseApolloConfig() {
        log.debug("channel:{}", can_not_create_dianhua_work_channel);
        return Splitter.on(",")
                .splitToList(Optional.ofNullable(can_not_create_dianhua_work_channel).orElse(""))
                .stream()
                .map(ChannelRefineResuleEnum::parse)
                .collect(Collectors.toSet());
    }


    /**
     * @return 驳回项是否包含增信信息, true包含, false不包含
     */
    private boolean boHuiContainsCredit(WorkOrderVO workOrderVO) {
        if (workOrderVO.getHandleResult() != HandleResultEnum.audit_reject.getType()) {
            return false;
        }
        long workOrderId = workOrderVO.getWorkOrderId();
        InitialAuditCaseDetail.CreditInfo creditInfo = workOrderSnapshotService.getByClazz(workOrderId,
                WorkOrderExtContentTypeEnum.INITIAL_AUDIT_CREDIT_INFO, InitialAuditCaseDetail.CreditInfo.class);
        log.info("hasRejectCreditInfo,caseId:{}, data:{}", workOrderId, JSON.toJSONString(creditInfo));
        if (creditInfo != null && CollectionUtils.isNotEmpty(creditInfo.getRejectIds())) {
            return true;
        }
        return false;
    }

    public boolean canCreateHighRisk(int caseId) {
        if (highRiskV2Enable) {
            boolean b = canCreateHighRiskV2(caseId);
            // 保存记录 标识走了高风险新规则
            wonRecordClient.create()
                    .buildBasic(caseId, AdminWonActionConst.HIGH_RISK_V2_FLAG)
                    .buildCaseId(caseId)
                    .save();
            log.info("canCreateHighRisk v2 caseId {} res {}", caseId, b);
            return b;
        }
        boolean res = canCreateHighRiskV1(caseId);
        log.info("canCreateHighRisk v1 caseId {} res {}", caseId, res);
        return res;
    }

    /**
     * @return true 生成高风险工单
     */
    public boolean canCreateHighRiskV2(int caseId) {
        RpcResult<CfPropertyInsuranceInfoModel> insuranceInfo = materialReadClient.selectCfPropertyInsuranceInfo(caseId);
        if (insuranceInfo.getData() == null) {
            log.warn("获取增信信息结果异常,insuranceInfo:{}", insuranceInfo);
        }
        // 获取走策略后的案例最大花费
        int maxAmount = 0;
        Response<List<DiseaseAmountResultRecord>> amountResultRecordByCaseId = diseaseClient.getAmountResultRecordByCaseId(caseId);
        List<DiseaseAmountResultRecord> diseaseAmountResultRecords = Optional.ofNullable(amountResultRecordByCaseId)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(Lists.newArrayList());
        if(CollectionUtils.isNotEmpty(diseaseAmountResultRecords)){
            // 取diseaseAmountResultRecords最新一条，version=2的数据
            DiseaseAmountResultRecord latestRecord = diseaseAmountResultRecords.stream()
                    .filter(record -> record.getVersion() == 2)
                    .max(Comparator.comparing(DiseaseAmountResultRecord::getCreateTime))
                    .orElse(null);
            if (latestRecord != null) {
                maxAmount = latestRecord.getAdviseMaxAmount();
                log.info("获取走策略后的案例最大花费 maxAmount:{}", maxAmount);
            }
        }
        boolean raiseHighRisk = false;
        CfPropertyInsuranceInfoModel data = insuranceInfo.getData();
        if (data != null) {
            CreditInfoVO raiseInfo = CreditInfoVO.build(data);
            raiseInfo.setCaseId(caseId);
            raiseInfo.setMaxTreatmentCost(maxAmount);
            HighRiskJudgeResult result = highRiskService.judgeRisk(HighRiskJudgeConst.Source.CREDIT, 0, raiseInfo);
            raiseHighRisk = result.isHighRisk();
        }
        log.info("raiseHighRisk:{}", raiseHighRisk);

        PreposeMaterialModel.MaterialInfoVo materialInfoVo = preposeMaterialDelegate.queryByCaseId(caseId);
        boolean preposeHighRisk = false;
        if (materialInfoVo != null) {
            CreditInfoVO preposeInfo = CreditInfoVO.build(materialInfoVo);
            preposeInfo.setCaseId(caseId);
            preposeInfo.setMaxTreatmentCost(maxAmount);
            HighRiskJudgeResult preposeResult = highRiskService.judgeRisk(HighRiskJudgeConst.Source.PREPOSE, 0, preposeInfo);
            preposeHighRisk = preposeResult.isHighRisk();
        }
        if (raiseHighRisk || preposeHighRisk) {
            log.info("可以生成高风险工单 raiseHighRisk:{}, preposeHighRisk: {}, preposeMaterialRisk:{}", raiseHighRisk, preposeHighRisk, materialInfoVo);
            return true;
        }
        log.info("不能生成高风险工单 raiseHighRisk:{}, preposeHighRisk: {}, preposeMaterialRisk:{}", raiseHighRisk, preposeHighRisk, materialInfoVo);
        return false;
    }

    /**
     * @return true 生成高风险工单
     */
    public boolean canCreateHighRiskV1(int caseId) {
        RpcResult<CfPropertyInsuranceInfoModel> insuranceInfo = materialReadClient.selectCfPropertyInsuranceInfo(caseId);
        if (insuranceInfo.getData() == null) {
            log.warn("获取增信信息结果异常,insuranceInfo:{}", insuranceInfo);
        }
        Integer insuranceRisk = Optional.ofNullable(insuranceInfo.getData()).map(CfPropertyInsuranceInfoModel::getRisk).orElse(0);
        log.info("获取增信信息insuranceInfo:{}", insuranceInfo);

        PreposeMaterialModel.MaterialInfoVo materialInfoVo = preposeMaterialDelegate.queryByCaseId(caseId);
        Integer preposeMaterialRiskLevel = Optional.ofNullable(materialInfoVo)
                .map(PreposeMaterialModel.MaterialInfoVo::getRiskLevel)
                .orElse(0);
        if (insuranceRisk.equals(1) || preposeMaterialRiskLevel.equals(1)) {
            log.info("可以生成高风险工单insuranceRisk:{},preposeMaterialRisk:{}", insuranceRisk, materialInfoVo);
            return true;
        }
        return false;
    }

    public void createSmartAudit(int caseId, int condition) {
        Response<WorkOrderVO> aiErCiWorkOrder = workOrderClient.getLastWorkOrder(caseId, WorkOrderType.ai_erci.getType());
        WorkOrderVO aiErCiData = Optional.ofNullable(aiErCiWorkOrder)
                .map(Response::getData)
                .orElse(null);
        log.info("InitialAuditCreateOrder createSmartAudit caseId : {}, condition : {}", caseId, condition);
        if (Objects.isNull(aiErCiData)) {
            //首次走智能审核
            createSmartAuditWorkOrder(caseId);
            return;
        }
        InitialAuditSmartRejectVo build = InitialAuditSmartRejectVo.builder()
                .initialAuditNoSmartReason(InitialAuditNoSmartReason.LAST_NO_SMART_WORK_ORDER)
                .build();
        if (aiErCiData.getHandleResult() != HandleResultEnum.audit_reject.getType()) {
            createAiErCi(caseId, condition, build);
            return;
        }
        //获取该案例是否生成过 图片录入工单
        Response<WorkOrderVO> aiPhotoWorkOrder = workOrderClient.getLastWorkOrder(caseId, WorkOrderType.ai_photo.getType());
        Response<WorkOrderVO> aiContentWorkOrder = workOrderClient.getLastWorkOrder(caseId, WorkOrderType.ai_content.getType());
        WorkOrderVO aiPhotoData = Optional.ofNullable(aiPhotoWorkOrder)
                .map(Response::getData)
                .orElse(null);
        //获取该案例是否生成过 求助说明录入工单
        WorkOrderVO aiContentData = Optional.ofNullable(aiContentWorkOrder)
                .map(Response::getData)
                .orElse(null);
        if (Objects.isNull(aiPhotoData) || Objects.isNull(aiContentData)) {
            createAiErCi(caseId, condition, build);
            return;
        }
        //非首次提交
        if (aiPhotoData.getHandleResult() == HandleResultEnum.done.getType() && aiContentData.getHandleResult() == HandleResultEnum.done.getType()) {
            notFirstCreateSmartAuditWorkOrder(caseId, aiErCiData.getWorkOrderId());
            return;
        }
        createAiErCi(caseId, condition, build);
    }

    /**
     * 智能审核2.0  非首次提交
     *
     * @param caseId
     */
    private void notFirstCreateSmartAuditWorkOrder(int caseId, long workOrderId) {
        InitialAuditOperationItem.RejectOptionSet rejectOptionSet = auditSearchService.queryRejectList(caseId, workOrderId);
        ChuciWorkOrder chuciWorkOrder = new ChuciWorkOrder();
        if (Objects.isNull(rejectOptionSet) || CollectionUtils.isEmpty(rejectOptionSet.getRejectIds())) {
            log.info("notFirstCreateSmartAuditWorkOrder rejectIds is empty {}", caseId);
            return;
        }
        List<Integer> rejectResultList = rejectOptionSet.getRejectIds();
        int createResult = 0;
        for (Integer id : doubleTurnDownIds) {
            if (rejectResultList.contains(id)) {
                //生成文章工单
                chuciWorkOrder.setCaseId(caseId);
                chuciWorkOrder.setOrderType(WorkOrderType.ai_content.getType());
                InitialAuditCreateOrderParam orderParam = InitialAuditCreateOrderParam.builder()
                        .caseId(caseId)
                        .chuciWorkOrder(chuciWorkOrder)
                        .condition(0)
                        .build();
                create(orderParam);
                log.info("非首次生成文章工单 chuciWorkOrder:{}", chuciWorkOrder);
                createResult++;
                break;
            }
        }
        for (Integer id : singleTurnDownIds) {
            if (rejectResultList.contains(id)) {
                //生成图片工单
                chuciWorkOrder.setCaseId(caseId);
                chuciWorkOrder.setOrderType(WorkOrderType.ai_photo.getType());
                InitialAuditCreateOrderParam orderParam = InitialAuditCreateOrderParam.builder()
                        .caseId(caseId)
                        .chuciWorkOrder(chuciWorkOrder)
                        .condition(0)
                        .build();
                create(orderParam);
                log.info("非首次生成图片工单 chuciWorkOrder:{}", chuciWorkOrder);
                createResult++;
                break;
            }
        }
        if (createResult > 0) {
            return;
        }
        chuciWorkOrder.setCaseId(caseId);
        chuciWorkOrder.setOrderType(WorkOrderType.ai_content.getType());
        InitialAuditCreateOrderParam orderParam = InitialAuditCreateOrderParam.builder()
                .caseId(caseId)
                .chuciWorkOrder(chuciWorkOrder)
                .condition(0)
                .build();
        create(orderParam);
    }

    /**
     * 智能审核2.0 生成智能审核工单
     *
     * @param caseId
     */
    public void createSmartAuditWorkOrder(int caseId) {
        log.info("生成图片and文章工单createSmartAudit智能审核 caseId:{}", caseId);
        ChuciWorkOrder chuciWorkOrder = new ChuciWorkOrder();
        chuciWorkOrder.setCaseId(caseId);
        // ai_photo(56, "图片录入工单", "chucishenhe:ai_photo", false)
        chuciWorkOrder.setOrderType(WorkOrderType.ai_photo.getType());
        //生成图片录入工单
        InitialAuditCreateOrderParam orderParam = InitialAuditCreateOrderParam.builder()
                .caseId(caseId)
                .chuciWorkOrder(chuciWorkOrder)
                .condition(0)
                .build();
        create(orderParam);
        // ai_content(55, "文章录入工单", "chucishenhe:ai_content", true)
        chuciWorkOrder.setOrderType(WorkOrderType.ai_content.getType());
        //生成求助说明录入工单
        orderParam.setChuciWorkOrder(chuciWorkOrder);
        create(orderParam);
    }

    /**
     * 智能审核2.0 判断是否走智能审核
     *
     * @param caseId
     * @return
     */
    public InitialAuditSmartRejectVo judgeInitialReviewInformation(int caseId) {
        InitialAuditSmartRejectVo initialAuditSmartRejectVo = new InitialAuditSmartRejectVo();
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
        if (Objects.isNull(crowdfundingInfo)) {
            initialAuditSmartRejectVo.setInitialAuditNoSmartReason(InitialAuditNoSmartReason.CROWDFUNDING_INFO);
            return initialAuditSmartRejectVo;
        }

        //计算结束回调和再次提交 按照真实渠道去判断
        //线下BD提交，走开关控制
        ChannelRefineResuleEnum channel = getChannel(caseId);
        if (ChannelRefineResuleEnum.XIANXIA_BD.equals(channel) && !xianXiaBdSmartAudit) {
            initialAuditSmartRejectVo.setInitialAuditNoSmartReason(InitialAuditNoSmartReason.XIAN_XIA_BD);
            return initialAuditSmartRejectVo;
        }

        //是否可以生成高风险工单
        if (canCreateHighRisk(caseId)) {
            log.info("可以生成高风险工单 caseId:{}", caseId);
            initialAuditSmartRejectVo.setInitialAuditNoSmartReason(InitialAuditNoSmartReason.HIGH_RISK_WORK_ORDER);
            return initialAuditSmartRejectVo;
        }

        //是否为重复发起
        CfFirsApproveMaterial material = riskDelegate.getCfFirsApproveMaterialByInfoId(caseId);
        //初审是否使用出生证
        if (Objects.isNull(material) || UserIdentityType.birth.getCode() == material.getPatientIdType()) {
            log.info("初审使用出生证 caseId:{}", caseId);
            initialAuditSmartRejectVo.setInitialAuditNoSmartReason(InitialAuditNoSmartReason.BIRTH_CARD_CASE);
            return initialAuditSmartRejectVo;
        }

        InitialAuditSmartRejectVo judgeRepeatCase = judgeRepeatCase(caseId, material);
        if (Objects.nonNull(judgeRepeatCase.getInitialAuditNoSmartReason())) {
            return judgeRepeatCase;
        }

        //1.低保或贫困信息任意一个模块为是
        //2.目标金额系统判断为不合理或需要选择治疗方案
        //3.用户预审阶段选择使用出生证发起
        RpcResult<CfBasicLivingGuardModel> resp = cfRaiseMaterialClient.selectLivingGuard(caseId);
        if (resp.isFail()) {
            initialAuditSmartRejectVo.setInitialAuditNoSmartReason(InitialAuditNoSmartReason.LIVING_ALLOWANCE_AND_POVERTY_EMPTY);
            return initialAuditSmartRejectVo;
        }
        //是否享受低保 0 否 1 是
        //是否脱贫户/脱贫人口 0 否 1 是
        InitialAuditCaseDetail.CfBasicLivingGuardView guardView = new InitialAuditCaseDetail.CfBasicLivingGuardView(resp.getData());
        if (Objects.nonNull(guardView.getHasPoverty()) && guardView.getHasPoverty() == 1) {
            log.info("享有低保or脱贫户/脱贫人口 caseId:{}", caseId);
            initialAuditSmartRejectVo.setInitialAuditNoSmartReason(InitialAuditNoSmartReason.LIVING_ALLOWANCE_AND_POVERTY);
            return initialAuditSmartRejectVo;
        }

        //目标金额系统判断为不合理或需要选择治疗方案
        RpcResult<RaiseBasicInfoModel> rpcResult = cfRaiseMaterialClient.selectRaiseBasicInfo(caseId);
        log.info("rpcResult.data:{} caseId:{}", JSON.toJSONString(rpcResult.getData()), caseId);
        if (rpcResult.isFail() || rpcResult.getData() == null || StringUtils.isBlank(rpcResult.getData().getDiseaseName())) {
            initialAuditSmartRejectVo.setInitialAuditNoSmartReason(InitialAuditNoSmartReason.DISEASE_NAME_EMPTY);
            return initialAuditSmartRejectVo;
        }
        //写入list
        List<String> diseaseNameList = List.of(Objects.requireNonNull(StringUtils.split(rpcResult.getData().getDiseaseName(), ",，")));

        Response<List<DiseaseClassifyVOV2>> listResponse = diseaseClassifyFeignClientV2.diseaseNorm(diseaseNameList);
        List<DiseaseClassifyVOV2> diseaseClassifyVOV2s = Optional.ofNullable(listResponse)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(Lists.newArrayList());
        List<String> norm = CollectionUtils.isNotEmpty(diseaseClassifyVOV2s) ? diseaseClassifyVOV2s.stream()
                .map(DiseaseClassifyVOV2::getNorm)
                .flatMap(List::stream)
                .collect(Collectors.toList())
                : new ArrayList<>();
        if (CollectionUtils.isEmpty(norm)) {
            log.info("患者疾病不在疾病库范围内 caseId : {}", caseId);
            initialAuditSmartRejectVo.setInitialAuditNoSmartReason(InitialAuditNoSmartReason.DISEASE_NAME_EXIT_SYSTEM);
            initialAuditSmartRejectVo.setDiseaseName(Joiner.on(",").join(diseaseNameList));
            return initialAuditSmartRejectVo;
        }
        initialAuditSmartRejectVo.setInitialAuditNoSmartReason(InitialAuditNoSmartReason.SUCCESS);
        return initialAuditSmartRejectVo;

    }

    public InitialAuditSmartRejectVo judgeRepeatCase(int caseId, CfFirsApproveMaterial material) {
        InitialAuditSmartRejectVo initialAuditSmartRejectVo = new InitialAuditSmartRejectVo();
        if (Objects.isNull(material)) {
            return initialAuditSmartRejectVo;
        }
        InitialRepeatCaseView.InitialRepeatRiskParam param = InitialRepeatCaseView.InitialRepeatRiskParam.convertFromFirstMaterial(material);
        RiskAnalysisDto analysisDto = new RiskAnalysisDto();
        analysisDto.setReqId(String.valueOf(caseId));
        analysisDto.setModelGuid(AEGIS_MODEL_GUID);
        analysisDto.setEventInfo(param);
        Response<Map<String, RiskObject>> analyze = engineAnalysisClient.analyze(analysisDto);
        if (Objects.isNull(analyze) || analyze.notOk() || MapUtils.isEmpty(analyze.getData())) {
            return initialAuditSmartRejectVo;
        }
        RiskObject riskObject = analyze.getData().get(InitialRepeatCaseView.RepeatType.INITIAL_AUDIT_PASS_CURRENT_PERIOD.getRiskMark());
        if (Objects.nonNull(riskObject) && CollectionUtils.isNotEmpty(JSONObject.parseArray(riskObject.getRiskResult()))) {
            log.info("为重复发起 caseId:{}, {}", caseId, riskObject);
            initialAuditSmartRejectVo.setInitialAuditNoSmartReason(InitialAuditNoSmartReason.REPEAT_CASE);
            return initialAuditSmartRejectVo;
        }
        RiskObject riskObjectStop = analyze.getData().get(InitialRepeatCaseView.RepeatType.CASE_REPEAT_NEAR_STOP_THEN_RE.getRiskMark());
        if (Objects.nonNull(riskObjectStop) && CollectionUtils.isNotEmpty(JSONObject.parseArray(riskObjectStop.getRiskResult()))) {
            List<Integer> stopCaseIdList = JSONObject.parseArray(riskObjectStop.getRiskResult())
                    .stream()
                    .map(String::valueOf)
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            Map<Integer, CfUserInvitedLaunchCaseRecordModel> caseRaiseMappings = adminApproveService.getCaseChannelRecordMap(stopCaseIdList);
            long count = Optional.of(caseRaiseMappings.values())
                    .orElse(new HashSet<>())
                    .stream()
                    .map(adminApproveService::getGuideUserLaunchChannel)
                    .filter(f -> StringUtils.equals(f, "用户自主发起"))
                    .count();
            if (count == 0) {
                log.info("为重复发起 caseId:{}, {}, {}", caseId, riskObjectStop, caseRaiseMappings);
                initialAuditSmartRejectVo.setInitialAuditNoSmartReason(InitialAuditNoSmartReason.REPEAT_CASE);
                return initialAuditSmartRejectVo;
            }
        }
        return initialAuditSmartRejectVo;
    }

    public BlacklistHitHighRiskDto blackListHighRiskWorkOrder(InitialAuditCreateOrderParam orderParam) {
        Response<BlacklistHitHighRiskDto> blacklistHitHighRiskDtoResponse = blacklistVerifyClient.queryBlackValidHighRisk(orderParam.getCaseId());
        log.info("InitialAuditCreateOrder blackListHighRiskWorkOrder {} {}", orderParam, blacklistHitHighRiskDtoResponse);
        BlacklistHitHighRiskDto blacklistHitHighRiskDto = Optional.ofNullable(blacklistHitHighRiskDtoResponse)
                .map(Response::getData)
                .orElse(null);
        if (Objects.isNull(blacklistHitHighRiskDto) || blacklistHitHighRiskDto.getHitType() == 0) {
            return null;
        }

        return blacklistHitHighRiskDto;
    }

    /**
     * @param caseId
     * @return true 升级应急组
     */
    public boolean canUpgrade(int caseId) {
        RpcResult<CfPropertyInsuranceInfoModel> insuranceInfo = materialReadClient.selectCfPropertyInsuranceInfo(caseId);
        if (insuranceInfo.getData() == null) {
            log.warn("获取增信信息结果异常,insuranceInfo:{}", insuranceInfo);
        }

        CfPropertyInsuranceInfoModel data = insuranceInfo.getData();
        if (data != null) {
            CreditInfoVO raiseInfo = CreditInfoVO.build(data);
            raiseInfo.setCaseId(caseId);
            HighRiskJudgeResult result = highRiskService.judgeRisk(HighRiskJudgeConst.Source.CREDIT, 0, raiseInfo);
            for (HighRiskAutoEnum upgradeHitCode : UPGRADE_HIT_CODES) {
                if (result.getHitCodes().contains(upgradeHitCode.getRuleCode())) {
                    log.info("升级应急组, caseId: {}, raiseInfo: {}", caseId, raiseInfo);
                    return true;
                }
            }
        }
        return false;
    }
}
