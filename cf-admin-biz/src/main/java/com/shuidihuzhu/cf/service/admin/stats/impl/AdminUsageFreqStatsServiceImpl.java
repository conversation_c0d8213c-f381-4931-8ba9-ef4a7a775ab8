package com.shuidihuzhu.cf.service.admin.stats.impl;

import com.shuidihuzhu.cf.dao.admin.stats.AdminUsageFreqStatsDao;
import com.shuidihuzhu.cf.enums.admin.stats.UsageFreqStatsBizTypeEnum;
import com.shuidihuzhu.cf.model.admin.stats.AdminUsageFreqStats;
import com.shuidihuzhu.cf.service.admin.stats.AdminUsageFreqStatsService;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/6/10 11:41
 */
@Slf4j
@Service
public class AdminUsageFreqStatsServiceImpl implements AdminUsageFreqStatsService {

    private static final String UNIQUE_KEY_PREFIX = "cf_admin_usage_freq_";
    private static final long WAIT_TIME_OUT = 60 * 1000;
    private static final long LEAVE_TIME_OUT = 10 * 1000;

    @Autowired
    private AdminUsageFreqStatsDao adminUsageFreqStatsDao;

    @Resource(name = "cf2RedissonHandler")
    private RedissonHandler redissonHandler;

    @Override
    public void incrementUsageFreq(String statsKey, String statsDim, UsageFreqStatsBizTypeEnum bizTypeEnum) {
        AdminUsageFreqStats usageFreqStats = adminUsageFreqStatsDao.getByUniqueCondition(statsKey, statsDim, bizTypeEnum.getCode());
        if (usageFreqStats == null) {
            String key = UNIQUE_KEY_PREFIX+statsKey+statsDim+bizTypeEnum.getCode();
            String identify = null;
            try {
                identify = redissonHandler.tryLock(key, WAIT_TIME_OUT, LEAVE_TIME_OUT);
                if (StringUtils.isNotBlank(identify)) {
                    usageFreqStats = adminUsageFreqStatsDao.getByUniqueCondition(statsKey, statsDim, bizTypeEnum.getCode());
                    //insert
                    if (usageFreqStats == null) {
                        insertBySelective(statsKey, statsDim, bizTypeEnum);
                        //update
                    } else {
                        adminUsageFreqStatsDao.updateByUniqueCondition(statsKey, statsDim, bizTypeEnum.getCode(), 1);
                    }
                } else {
                    log.error("记录功能使用频率获取redis锁失败，statsKey:{}, statsDim:{}, bizTypeEnum:{}", statsKey, statsDim, bizTypeEnum);
                }
            } catch (InterruptedException e) {
                log.error("", e);
            } finally {
                if (StringUtils.isNotBlank(identify)) {
                    redissonHandler.unLock(key, identify);
                }
            }
            //update
        } else {
            adminUsageFreqStatsDao.updateByUniqueCondition(statsKey, statsDim, bizTypeEnum.getCode(), 1);
        }
    }

    @Override
    public void incrementUsageFreqBatch(List<String> statsKeys, String statsDim, UsageFreqStatsBizTypeEnum bizTypeEnum) {
        List<AdminUsageFreqStats> adminUsageFreqStats = adminUsageFreqStatsDao.getByUniqueConditions(statsKeys, statsDim, bizTypeEnum.getCode());
        Map<String, AdminUsageFreqStats> statsMap = adminUsageFreqStats.stream().collect(Collectors.toMap(AdminUsageFreqStats::getStatsKey, Function.identity()));
        //更新已经存在的值
        Set<String> intersection = statsMap.keySet();
        if (CollectionUtils.isNotEmpty(intersection)) {
            adminUsageFreqStatsDao.updateByUniqueConditions(new ArrayList<>(intersection), statsDim, bizTypeEnum.getCode(), 1);
        }
        //处理第一次新增的值
        Collection<String> subtracts = CollectionUtils.subtract(statsKeys, intersection);
        for (String statsKey : subtracts) {
            incrementUsageFreq(statsKey, statsDim, bizTypeEnum);
        }
    }

    private int insertBySelective(String statsKey, String statsDim, UsageFreqStatsBizTypeEnum bizTypeEnum){
        AdminUsageFreqStats adminUsageFreqStats = new AdminUsageFreqStats();
        adminUsageFreqStats.setStatsKey(statsKey);
        adminUsageFreqStats.setStatsDim(statsDim);
        adminUsageFreqStats.setBizType(bizTypeEnum.getCode());
        adminUsageFreqStats.setStatsValue(1L);
        return adminUsageFreqStatsDao.insertSelective(adminUsageFreqStats);
    }

}
