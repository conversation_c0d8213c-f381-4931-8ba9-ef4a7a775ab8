package com.shuidihuzhu.cf.service.workorder;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.client.feign.RiverReviewFeignClient;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.river.*;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditCreateOrder;
import com.shuidihuzhu.client.cf.workorder.*;
import com.shuidihuzhu.client.cf.workorder.model.CreditWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.*;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Optional;

/**
 * @author: fengxuan
 * @create 2020-01-09 10:17
 **/
@Service
@Slf4j
public class WorkOrderCreditService {

    @Autowired
    CfCreditWorkOrderClient creditWorkOrderClient;

    @Autowired
    InitialAuditCreateOrder createOrder;

    @Autowired
    AdminCfInfoExtBiz adminCfInfoExtBiz;

    @Autowired
    RiverReviewFeignClient riverReviewFeignClient;

    @Autowired
    private CfWorkOrderClient workOrderClient;

    @Autowired
    private CfWorkOrderTypeFeignClient cfWorkOrderTypeFeignClient;

    private List<Integer> creditWorkOrderTypes = Lists.newArrayList();

    @PostConstruct
    public void init() {
        creditWorkOrderTypes =  Optional.ofNullable(cfWorkOrderTypeFeignClient.getByOneLevel(OneTypeEnum.cailiao.getType()))
                .filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList());
    }
    /**
     * 创建增信工单
     */
    public void create(int caseId) {
        //检查增信审核状态是否是已提交
        boolean hasCommitCredit = hasCommitCreditSupply(caseId);
        //检查初审状态
        boolean firstApprovePassed = checkFirstApproveStatus(caseId);
        log.info("创建初审工单前的判断firstApprovePassed:{},hasCommitCredit:{}", firstApprovePassed, hasCommitCredit);
        if (firstApprovePassed && hasCommitCredit) {
            Response<WorkOrderVO> creditWorkOrderResponse = workOrderClient.getLastWorkOrderByTypes(caseId, creditWorkOrderTypes);
            if (creditWorkOrderResponse.notOk()) {
                log.warn("调用工单接口失败,caseId:{},response:{}", caseId, creditWorkOrderResponse);
                throw new RuntimeException(String.format("获取工单信息失败,caseId:%d", caseId));
            }
            int orderType;
            if (creditWorkOrderResponse.getData() != null && creditWorkOrderResponse.getData().getOrderType() == WorkOrderType.zengxinrisk.getType()) {
                orderType = WorkOrderType.zengxinrisk.getType();
            } else {
                boolean canCreateHighRisk = createOrder.canCreateHighRisk(caseId);
                orderType = canCreateHighRisk ? WorkOrderType.zengxinrisk.getType() : WorkOrderType.zengxinnormal.getType();
            }
            CreditWorkOrder creditWorkOrder = new CreditWorkOrder();
            creditWorkOrder.setCaseId(caseId);
            creditWorkOrder.setOrderType(orderType);
            creditWorkOrderClient.createCredit(creditWorkOrder);
        }
    }


    private boolean checkFirstApproveStatus(int caseId) {
        CfInfoExt cfInfoExt = adminCfInfoExtBiz.getByCaseId(caseId);
        if (cfInfoExt == null) {
            log.warn("存在案例,但是cfInfoExt没数据,caseId:{}", caseId);
            return false;
        }
        return FirstApproveStatusEnum.isPassed(FirstApproveStatusEnum.parse(cfInfoExt.getFirstApproveStatus()));
    }


    private boolean hasCommitCreditSupply(int caseId) {
        Response<RiverReviewDO> creditInfoResponse = riverReviewFeignClient.getByCaseIdAndUsageType(caseId, RiverUsageTypeEnum.CREDIT_INFO.getValue());
        if (creditInfoResponse.notOk()) {
            log.warn("调用riverReviewFeignClient失败,response:{}", creditInfoResponse);
            throw new RuntimeException("调用riverReviewFeignClient失败");
        }
        return Optional.ofNullable(creditInfoResponse.getData())
                .map(item -> item.getInfoStatus() == RiverStatusEnum.SUBMITTED.getValue())
                .orElse(false);
    }


}
