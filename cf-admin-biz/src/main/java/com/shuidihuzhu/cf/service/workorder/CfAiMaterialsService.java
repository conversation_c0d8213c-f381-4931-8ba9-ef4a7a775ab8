package com.shuidihuzhu.cf.service.workorder;

import com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterials;
import com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterialsResult;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2021/4/22 20:27
 * @Description:
 */
@Service
public interface CfAiMaterialsService {

    CfAiMaterialsResult getResultByCaseId(int caseId);

    CfAiMaterials getByCaseId(int caseId, int materialsType);
}
