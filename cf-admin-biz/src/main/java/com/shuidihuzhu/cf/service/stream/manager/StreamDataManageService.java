package com.shuidihuzhu.cf.service.stream.manager;

import com.shuidihuzhu.cf.service.stream.StreamActionConst;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class StreamDataManageService extends BaseStreamService<Response<StreamData>> {

    protected Response<StreamData> createRetryResult() {
        StreamData v = new StreamData();
        v.setFlag(StreamActionConst.RETRY);
        return NewResponseUtil.makeSuccess(v);
    }
}
