package com.shuidihuzhu.cf.service.record;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingAttachmentBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.IUgcOperateRecordDAO;
import com.shuidihuzhu.cf.dao.record.CfCrowdfundingAttachmentRecordDao;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.UgcBizType;
import com.shuidihuzhu.cf.enums.record.RecordBizTypeEnum;
import com.shuidihuzhu.cf.enums.record.RecordOperateTypeEnum;
import com.shuidihuzhu.cf.enums.record.RecordPageTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.AdminUgcOperateRecordDO;
import com.shuidihuzhu.cf.model.crowdfunding.CfAttachmentVo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.record.CfCrowdfundingAttachmentRecordDo;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditOperationItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2021/9/1  3:59 下午
 */
@Service
@Slf4j
public class CfCrowdfundingAttachmentRecordService {
    @Resource
    private CfCrowdfundingAttachmentRecordDao cfCrowdfundingAttachmentRecordDao;

    @Autowired
    private AdminCrowdfundingAttachmentBiz adminCrowdfundingAttachmentBiz;

    @Autowired
    private CfAllOperateRecordService cfAllOperateRecordService;

    private static final String IMAGE = "https://image.shuidichou.com";
    private static final String IMAGES = "https://images.shuidichou.com";
    private static final String OSS = "https://oss.shuidichou.com";

    public void insertBatchAudit(int caseId) {

        List<CrowdfundingAttachment> attachmentList = adminCrowdfundingAttachmentBiz.getAttachmentsByTypes(caseId, Lists.newArrayList(AttachmentTypeEnum.ATTACH_CF.value()));
        if (CollectionUtils.isEmpty(attachmentList)) {
            log.info("没有查询到图片 caseId:{}", caseId);
            return;
        }

        attachmentList = attachmentList.stream().filter(v -> v.getIsDelete() == 0).collect(Collectors.toList());

        List<Integer> ids = attachmentList.stream().map(CrowdfundingAttachment::getId).collect(Collectors.toList());

        cfAllOperateRecordService.insertBatch(0, caseId, RecordBizTypeEnum.OPERATE_IMAGE.getCode()
                , Joiner.on(",").join(ids), RecordOperateTypeEnum.IMAGE_DEL_IMAGE.getCode(), RecordPageTypeEnum.EXAMINATION_PASSED.getCode(), "初审通过");

        List<CfCrowdfundingAttachmentRecordDo> cfCrowdfundingAttachmentRecordDos = cfCrowdfundingAttachmentRecordDao.getListByCaseId(caseId, AttachmentTypeEnum.ATTACH_CF.value());

        List<Long> attachmentIds = cfCrowdfundingAttachmentRecordDos.stream().map(CfCrowdfundingAttachmentRecordDo::getAttachmentId).collect(Collectors.toList());

        List<CfCrowdfundingAttachmentRecordDo> crowdfundingAttachmentRecordDoList = Lists.newArrayList();

        for (CrowdfundingAttachment attachment : attachmentList) {
            if (attachmentIds.contains((long) attachment.getId())) {
                continue;
            }
            CfCrowdfundingAttachmentRecordDo cfCrowdfundingAttachmentRecordDo = CfCrowdfundingAttachmentRecordDo
                    .build(caseId, attachment.getId(), 0, AttachmentTypeEnum.ATTACH_CF.value());
            crowdfundingAttachmentRecordDoList.add(cfCrowdfundingAttachmentRecordDo);
        }
        if (CollectionUtils.isNotEmpty(crowdfundingAttachmentRecordDoList)) {
            cfCrowdfundingAttachmentRecordDao.insertBatch(crowdfundingAttachmentRecordDoList);
        }
    }

    public void insertBatch(InitialAuditOperationItem.EditBaseInfo editBaseInfo) {

        if (Objects.isNull(editBaseInfo)) {
            return;
        }

        int caseId = editBaseInfo.getCaseId();
        String delImgUrls = editBaseInfo.getDelImgUrls();

        List<CrowdfundingAttachment> attachmentList = adminCrowdfundingAttachmentBiz.getAttachmentsByTypes(caseId, Lists.newArrayList(AttachmentTypeEnum.ATTACH_CF.value()));
        if (CollectionUtils.isEmpty(attachmentList)) {
            log.info("没有查询到图片 caseId:{}",caseId);
            return;
        }

        Map<String, CrowdfundingAttachment> attachmentMap = Maps.newHashMap();

        for (CrowdfundingAttachment attachment : attachmentList) {
            String url = attachment.getUrl();
            if (url.contains(IMAGE)) {
                url = url.replace(IMAGE, IMAGES);
            }
            if (url.contains(OSS)) {
                url = url.replace(OSS, IMAGES);
            }
            attachmentMap.put(url, attachment);
        }

        List<CfCrowdfundingAttachmentRecordDo> cfCrowdfundingAttachmentRecordDos = Lists.newArrayList();

        if (StringUtils.isNotEmpty(delImgUrls)) {
            List<String> delImgUrlList = Splitter.on(",").splitToList(delImgUrls);
            for (String delImgUrl : delImgUrlList) {
                CrowdfundingAttachment crowdfundingAttachment = attachmentMap.get(delImgUrl);
                if (Objects.isNull(crowdfundingAttachment)) {
                    continue;
                }
                CfCrowdfundingAttachmentRecordDo cfCrowdfundingAttachmentRecordDo = CfCrowdfundingAttachmentRecordDo
                        .build(caseId, crowdfundingAttachment.getId(), 1, AttachmentTypeEnum.ATTACH_CF.value());
                cfCrowdfundingAttachmentRecordDos.add(cfCrowdfundingAttachmentRecordDo);
                attachmentList.remove(crowdfundingAttachment);
            }
        }

        if (CollectionUtils.isNotEmpty(attachmentList)) {
            for (CrowdfundingAttachment crowdfundingAttachment : attachmentList) {
                if (Objects.isNull(crowdfundingAttachment)) {
                    continue;
                }
                CfCrowdfundingAttachmentRecordDo cfCrowdfundingAttachmentRecordDo = CfCrowdfundingAttachmentRecordDo
                        .build(caseId, crowdfundingAttachment.getId(), 0, AttachmentTypeEnum.ATTACH_CF.value());
                cfCrowdfundingAttachmentRecordDos.add(cfCrowdfundingAttachmentRecordDo);
            }
        }

        if (CollectionUtils.isNotEmpty(cfCrowdfundingAttachmentRecordDos)) {
            cfCrowdfundingAttachmentRecordDao.insertBatch(cfCrowdfundingAttachmentRecordDos);
        }
    }

    public List<CfAttachmentVo> filterByCaseId(int caseId, List<CfAttachmentVo> attachmentVos) {
        List<CfAttachmentVo> attachmentVoList = Lists.newArrayList();
        List<CfCrowdfundingAttachmentRecordDo> cfCrowdfundingAttachmentRecordDos = cfCrowdfundingAttachmentRecordDao.getListByCaseId(caseId, AttachmentTypeEnum.ATTACH_CF.value());
        if (CollectionUtils.isEmpty(cfCrowdfundingAttachmentRecordDos)) {
            return attachmentVos;
        }

        Map<Long, List<CfCrowdfundingAttachmentRecordDo>> longListMap = cfCrowdfundingAttachmentRecordDos.stream().collect(Collectors.groupingBy(CfCrowdfundingAttachmentRecordDo::getAttachmentId));

        for (CfAttachmentVo cfAttachmentVo : attachmentVos) {
            List<CfCrowdfundingAttachmentRecordDo> recordDos = longListMap.get((long) cfAttachmentVo.getId());
            if (CollectionUtils.isEmpty(recordDos)) {
                attachmentVoList.add(cfAttachmentVo);
                continue;
            }
            recordDos.sort(Comparator.comparing(CfCrowdfundingAttachmentRecordDo::getCreateTime).reversed());

            if (recordDos.get(0).getValid() == 1) {
                attachmentVoList.add(cfAttachmentVo);
            }
        }
        return attachmentVoList;
    }

}
