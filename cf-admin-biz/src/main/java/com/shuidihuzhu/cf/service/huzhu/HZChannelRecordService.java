package com.shuidihuzhu.cf.service.huzhu;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.model.admin.channel.ChannelRecord;
import com.shuidihuzhu.cf.model.admin.channel.ChannelRecordView;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.admin.BasicExample;
import com.shuidihuzhu.hz.client.hz.admin.model.AdminChannelRecordDto;
import com.shuidihuzhu.hz.client.hz.admin.service.AdminChannelRecordService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by wuxinlong on 5/27/16.
 */

@Service
public class HZChannelRecordService {
    @Autowired
    private AdminChannelRecordService recordService;

    public ChannelRecord getByChannel(String channel) {

        Response<AdminChannelRecordDto> recordDtoResponse = recordService.getByChannel(channel);
        if (null == recordDtoResponse || 0 != recordDtoResponse.getCode() || null == recordDtoResponse.getData()) {
            return null;
        }

        return this.toChannelRecord(recordDtoResponse.getData());
    }

    public ChannelRecord getById(int id) {
        Response<AdminChannelRecordDto> recordDtoResponse = recordService.getById(id);
        if (null == recordDtoResponse || 0 != recordDtoResponse.getCode() || null == recordDtoResponse.getData()) {
            return null;
        }

        AdminChannelRecordDto recordDto = recordDtoResponse.getData();

        return this.toChannelRecord(recordDto);
    }

    public List<ChannelRecord> getAll(Integer size, Integer offset) {

        Response<List<AdminChannelRecordDto>> listResponse = recordService.getAll(size, offset);
        if (null == listResponse || 0 != listResponse.getCode() || CollectionUtils.isEmpty(listResponse.getData())) {
            return Lists.newArrayList();
        }

        List<AdminChannelRecordDto> recordDtoList = listResponse.getData();

        List<ChannelRecord> channelRecordList = Lists.newArrayList();
        for (AdminChannelRecordDto recordDto : recordDtoList) {
            if (null != recordDto) {
                channelRecordList.add(this.toChannelRecord(recordDto));
            }
        }

        return channelRecordList;
    }

//    public List<ChannelRecord> getByDescr(String descr, Integer size, Integer offset) {
//
//        recordService.getByDescr(descr, size, offset);
//    }

    public int getAllCount() {
        Response<Integer> integerResponse = recordService.getAllCount();
        if (null == integerResponse || 0 != integerResponse.getCode()) {
            return 0;
        }

        return integerResponse.getData() == null ? 0 : integerResponse.getData().intValue();
    }

    //    public int getCountByDescr(String search) {
//        return recordService.getCountByDescr(search);
//    }
//
//
//    public int insertSelective(ChannelRecord channelRecord) {
//        return recordService.insertSelective(channelRecord);
//    }
//
//    public int updateByPrimaryKeySelective(ChannelRecord qrRcord) {
//        return recordService.updateByPrimaryKeySelective(qrRcord);
//    }
//
//    public int update(ChannelRecord channelRecord) {
//        return recordService.updateByPrimaryKey(channelRecord);
//    }
//
    public List<ChannelRecordView> getByDesc(String search, int size, int offset) {
        Response<List<AdminChannelRecordDto>> response = recordService.getByDesc(search, size, offset);
        if (null == response || 0 != response.getCode() || CollectionUtils.isEmpty(response.getData())) {
            return Lists.newArrayList();
        }
        List<AdminChannelRecordDto> recordDtoList = response.getData();

        List<ChannelRecordView> channelRecordList = Lists.newArrayList();
        for (AdminChannelRecordDto recordDto : recordDtoList) {
            if (null != recordDto) {
                channelRecordList.add(this.toChannelRecordView(recordDto));
            }
        }

        return channelRecordList;
    }

    private ChannelRecord toChannelRecord(AdminChannelRecordDto recordDto) {
        if (null == recordDto) {
            return null;
        }
        ChannelRecord channelRecord = new ChannelRecord();
        channelRecord.setId(recordDto.getId());
        channelRecord.setChannel(recordDto.getChannel());
        channelRecord.setDescr(recordDto.getDescr());
        channelRecord.setType(recordDto.getType());
        channelRecord.setUrl(recordDto.getUrl());
        channelRecord.setOprid(recordDto.getOprid());
        channelRecord.setAtime(recordDto.getAtime());
        channelRecord.setWxMpType(recordDto.getWxMpType());
        channelRecord.setThirdType(recordDto.getThirdType());
        channelRecord.setCfChannel(recordDto.getCfChannel());
        channelRecord.setCfChannelGroupId(recordDto.getCfChannelGroupId());

        return channelRecord;
    }

    private ChannelRecordView toChannelRecordView(AdminChannelRecordDto recordDto) {
        if (null == recordDto) {
            return null;
        }
        ChannelRecordView channelRecordView = new ChannelRecordView();
        channelRecordView.setId(recordDto.getId());
        channelRecordView.setChannel(recordDto.getChannel());
        channelRecordView.setDescr(recordDto.getDescr());
        channelRecordView.setType(recordDto.getType());
        channelRecordView.setUrl(recordDto.getUrl());
        channelRecordView.setOprid(recordDto.getOprid());
        channelRecordView.setAtime(recordDto.getAtime());
        channelRecordView.setWxMpType(recordDto.getWxMpType());
//        channelRecordView.setGroup(recordDto.get());
//        channelRecordView.setOperator(recordDto.getOp());
//        channelRecordView.setCfChannelGroup(recordDto.getCfChannelGroupId());

        return channelRecordView;
    }
}
