package com.shuidihuzhu.cf.service.workorder.delayfinance.impl;

import com.shuidihuzhu.cf.service.workorder.delayfinance.IBaseFinanceWorkOrderStrategy;
import com.shuidihuzhu.cf.service.workorder.delayfinance.IDelayFinanceWorkOrderService;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-12-01 4:13 下午
 **/
@Service
public class DelayFinanceWorkOrderServiceImpl implements IDelayFinanceWorkOrderService {

    @Autowired
    private List<IBaseFinanceWorkOrderStrategy> baseFinanceWorkOrderStrategies;

    private Map<Integer, IBaseFinanceWorkOrderStrategy> baseFinanceWorkOrderStrategyMap;

    @PostConstruct
    public void init() {
        baseFinanceWorkOrderStrategyMap = baseFinanceWorkOrderStrategies
                .stream().collect(Collectors.toMap(IBaseFinanceWorkOrderStrategy::orderType, Function.identity()));
    }

    @Override
    public void fillWorkOrderData(List<? extends WorkOrderVO> resultList) {
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }
        resultList = resultList
                .stream()
                .filter(r -> WorkOrderType.DELAY_FINANCE_WORK_ORDER_LIST.contains(r.getOrderType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        Map<Integer, ? extends List<? extends WorkOrderVO>> orderTypeMap = resultList
                .stream()
                .collect(Collectors.groupingBy(WorkOrderVO::getOrderType));

        // 工厂
        orderTypeMap.forEach((orderType, data) -> {
            baseFinanceWorkOrderStrategyMap.get(orderType).fillBusinessExt(data);
        });
    }
}
