package com.shuidihuzhu.cf.service;

import com.shuidihuzhu.client.cf.growthtool.client.CfHospitalNormalFeignClient;
import com.shuidihuzhu.client.model.hospital.CfHospitalNormal;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020/7/9
 */
@Slf4j
@Service
public class CfHospitalNormalService {

    @Autowired
    private CfHospitalNormalFeignClient cfHospitalNormalFeignClient;

    /**
     * -1:无法判断该医院是否接受对公打款，请人工核实
     * 0:标识支持对公打款
     * 1:标识不支持对公打款
     * @param hospitalId
     * @return
     */
    public int validateHospitalAcceptToPublic(int hospitalId,String hospitalCode) {

        if (hospitalId <= 0 && (StringUtils.isBlank(hospitalCode) || "hospitalCode".equals(hospitalCode))) {
            return -1;
        }

        if (StringUtils.isNotEmpty(hospitalCode) && !"hospitalCode".equals(hospitalCode)){
            Response<CfHospitalNormal> r = cfHospitalNormalFeignClient.getByCfHospitalCode(hospitalCode);
            return Optional.ofNullable(r).filter(Response::ok).map(Response::getData).filter(Objects::nonNull).map(CfHospitalNormal::getAcceptToPublic).orElse(-1);
        }

        Response<CfHospitalNormal> response = cfHospitalNormalFeignClient.getByCfHospitalId(hospitalId);
        if (response == null || response.getData() == null) {
            return -1;
        }

        return response.getData().getAcceptToPublic();
    }
}
