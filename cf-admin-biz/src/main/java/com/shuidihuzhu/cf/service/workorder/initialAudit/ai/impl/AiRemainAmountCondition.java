package com.shuidihuzhu.cf.service.workorder.initialAudit.ai.impl;

import com.shuidihuzhu.cf.client.material.model.CfPropertyInsuranceInfoModel;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditSearchService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.ai.AiCondition;
import com.shuidihuzhu.cf.vo.approve.CreditInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @DATE 2020/12/21
 */
@Service("aiRemainAmount")
public class AiRemainAmountCondition implements AiCondition {

    @Autowired
    private InitialAuditSearchService initialAuditSearchService;


    @Override
    public boolean check(int caseId, String inputValue) {
        CreditInfoVO creditInfoVO = initialAuditSearchService.getCreditInfoVO(caseId);

        String total = Optional.ofNullable(creditInfoVO.getOtherPlatform()).map(CfPropertyInsuranceInfoModel.RaiseOnOtherPlatform::getRemainAmount).map(String::valueOf).orElse("");

        return !inputValue.equals(total);
    }
}
