package com.shuidihuzhu.cf.service.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Service
@Slf4j
public class AiDiagnosisPicCheckService {

//    @Autowired
//    private AiDiagnosisPicCheckClient client;
//
//    public List<CfDiagnosisPicRepeatResp> checkDiagnosisPicRepeat(List<PicCheckObject> picCheckObjects) {
//
//        if (CollectionUtils.isEmpty(picCheckObjects)) {
//            return Lists.newArrayList();
//        }
//
//        long now = System.currentTimeMillis();
//        log.info("ai患者诊断材料-相似性判断服务发起");
//        List<CfDiagnosisPicRepeatResp> checkResp = Lists.newArrayList();
//        String result = "";
//        int caseId = picCheckObjects.get(0).getCaseId();
//        try {
//            result = client.checkDiagnosisPicRepeat(JSON.toJSONString(picCheckObjects));
//            List<CfDiagnosisPicRepeatResp> resp = JSON.parseArray(result, CfDiagnosisPicRepeatResp.class);
//            if (!CollectionUtils.isEmpty(resp)) {
//                for (CfDiagnosisPicRepeatResp picRepeat : resp) {
//                    if (picRepeat != null && !CollectionUtils.isEmpty(picRepeat.getCaseIds())) {
//                        picRepeat.getCaseIds().remove(caseId);
//                        if (!CollectionUtils.isEmpty(picRepeat.getCaseIds())) {
//                            checkResp.add(picRepeat);
//                        }
//                    }
//                }
//            }
//        } catch (Exception e) {
//            log.error("ai患者诊断材料-相似性判断服务异常, picCheckObjects:{}", picCheckObjects, e);
//        }
//
//        log.info("ai患者诊断材料-相似性判断服务结束 param: {} result: {}，cost: {} ms", picCheckObjects,
//                result, System.currentTimeMillis() - now);
//
//        return checkResp;
//    }
//
//
//    public void updateDiagnosisPic(List<PicCheckObject> picCheckObjects) {
//
//        if (CollectionUtils.isEmpty(picCheckObjects)) {
//            return;
//        }
//
//        try {
//            client.updateDiagnosisPic(JSON.toJSONString(picCheckObjects));
//        } catch (Exception e) {
//            log.error("ai患者诊断材料-更新接口异常, picCheckObjects:{} , picUrl:{} ", picCheckObjects, "", e);
//        }
//        log.info("ai患者诊断材料-更新接口 param:{}", picCheckObjects);
//    }

    @AllArgsConstructor
    @Data
    public static class PicCheckObject {
        @JSONField(name = "case_id")
        private int caseId;
        @JSONField(name = "pic_url")
        private String picUrl;
        private int id;
    }

    @Data
    @AllArgsConstructor
    public static class CfDiagnosisPicRepeatResp implements Serializable {
        private static final long serialVersionUID = 823759265521821426L;
        @JSONField(name = "case_ids")
        private Set<Integer> caseIds;
        @JSONField(name = "score")
        private String score;
        @JSONField(name = "pic_url")
        private String picUrl;

        private int id;
        public CfDiagnosisPicRepeatResp() {
        }
    }

    @Data
    @AllArgsConstructor
    public static class CfPicCheckResp implements Serializable {
        private static final long serialVersionUID = -7663347129522078414L;
        // 图片是否p过
        private int ps;
        // 图片是否有水印
        private int watermark;

        public CfPicCheckResp() {
        }
    }


    public static void main(String[] args) {

        String url = "http://192.168.118.76:8080/search";

        long cur = System.currentTimeMillis();
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
        headers.setContentType(type);
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());

        LinkedMultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        List<PicCheckObject> l = new ArrayList<>();
        PicCheckObject a1 = new PicCheckObject(324731, "http://cf.alioss.shuidichou.com/img/ck/20180608/ba4afc0c-6296-4f54-92b5-4e315460e362", 1);
        PicCheckObject a2 = new PicCheckObject(324732, "http://cf.alioss.shuidichou.com/img/ck/20180421/d9c57c17-585d-413b-ae0c-dd376d43efec", 2);
        PicCheckObject a3 = new PicCheckObject(324733, "http://cf.alioss.shuidichou.com/img/ck/20180120/c3edcd35-faa7-44a3-bead-9fedb2d8a2d2abcdeasf", 3);
        l.add(a1);
        l.add(a2);
        l.add(a3);

        map.add("data", JSON.toJSONString(l));
        String result = restTemplate.postForObject(url, map, String.class);
        System.out.println(result);

        List<CfDiagnosisPicRepeatResp> res = JSON.parseArray(result, CfDiagnosisPicRepeatResp.class);
        for (CfDiagnosisPicRepeatResp c1 : res) {
//                CfDiagnosisPicRepeatResp c = JSON.parseObject(c1.toJSONString(), CfDiagnosisPicRepeatResp.class);
                System.out.println(c1.getPicUrl() + "\t" + c1.getScore() + "\t" + c1.getId());
//            }
        }

//        restTemplate.postForObject(url2, map, String.class);

        System.out.println(System.currentTimeMillis() - cur);
//        System.out.println(JSON.toJSONString(l));
    }
}
