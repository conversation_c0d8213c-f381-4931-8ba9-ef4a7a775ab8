package com.shuidihuzhu.cf.service.approve.impl;

import com.alibaba.fastjson.JSON;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfQuestionnaireBiz;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationRecordDTO;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonRecordClient;
import com.shuidihuzhu.cf.client.ugc.wonrecord.model.WonExtUpdateByIdParam;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.admin.workorder.imageContent.CPublishImageContent;
import com.shuidihuzhu.cf.model.crowdfunding.CfMaterialVerityHistory;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cf.service.apollo.ApolloService;
import com.shuidihuzhu.cf.service.approve.ApproveService;
import com.shuidihuzhu.cf.service.approve.ApproveSnapshotService;
import com.shuidihuzhu.cf.util.crowdfunding.CosUploadUtil;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 老代码迁移controller -> service 不是我写的
 *
 * <AUTHOR>
 */
@Service
@RefreshScope
@Slf4j
public class ApproveSnapshotServiceImpl implements ApproveSnapshotService {

    @Autowired
    private CfQuestionnaireBiz biz;
    @Autowired
    private AdminCrowdfundingInfoBiz fundingBiz;

    @Autowired
    private CommonOperationRecordClient commonOperateClient;

    @Autowired
    private AdminApproveService adminApproveService;

    @Autowired
    private ApproveService approveService;

    @Autowired
    private WonRecordClient wonRecordClient;

    @Autowired
    private CosUploadUtil cosUploadUtil;

    @Autowired
    private ApolloService apolloService;

    private final static Set<String> NEED_SET_NULL_FIELDS = Sets.newHashSet(
            "creditInfo", "cfCreditSupplements", "cfCapitalDetail", "cfCapitalDetailBaseList",
            "cfCapitalDetailOtherList", "diBaoAndPinKunInfo", "diBaoHandleInfo", "drawCashData", "mirrorRecord",
            "pinKunHandleInfo", "requiredCodeList", "supplyProgressButtonInfo", "volunteer", "approveHistoryOverview");

    @Override
    public Response<String> selectHandleSnapshot(int caseId, long workOrderId) {
        CrowdfundingInfo info = fundingBiz.getFundingInfoById(caseId);
        if (info == null) {
            return NewResponseUtil.makeFail("案例不存在");
        }

        CPublishImageContent cPublishImageContent = adminApproveService.queryPublishImageContent(caseId);
        Response<Map<String, Object>> result = approveService.detail(info.getInfoId(), null);
        if (result == null || result.notOk() || result.getData() == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
        }
        Map<String, Object> resultMap = result.getData();
        OperationRecordDTO recordDTO = commonOperateClient.getLastByBizIdAndActionTypes(workOrderId,
                OperationActionTypeEnum.MATERIAL_INITIAL_OPERATE_DETAIL);
        resultMap.put("currentMaterialStatus", recordDTO == null ? null :
                JSON.parseArray(recordDTO.getRemark(), CfMaterialVerityHistory.CfMaterialVerityHistoryVo.class));

        // 字段太多 太长无法全部存在db里， 需要将质检用不到的数据清空
        for (String fieldName : NEED_SET_NULL_FIELDS) {
            resultMap.put(fieldName, null);
        }

        resultMap.put("cPublishImageContent", cPublishImageContent);

        return NewResponseUtil.makeSuccess(JSON.toJSONString(result.getData()));
    }

    @Async("asyncSaveSnapshot")
    @Override
    public void onApproveHandle(long handleRecordId, int caseId, long workOrderId, int orderType) {
        log.info("onApproveHandle {} {}", caseId, handleRecordId);
        if (WorkOrderType.cailiao_4.getType() != orderType
                && WorkOrderType.cailiao_5.getType() != orderType
                && WorkOrderType.cailiao_fuwu.getType() != orderType) {
            return;
        }
        try {
            Thread.sleep(6000L);
        } catch (InterruptedException e) {
            log.error("", e);
        }

        boolean s = saveSnapshot(handleRecordId, caseId, workOrderId);
        if (s) {
            return;
        }
        try {
            RetryerBuilder.<Boolean>newBuilder()
                    .withWaitStrategy(WaitStrategies.fibonacciWait(5000, 12, TimeUnit.SECONDS))
                    .withStopStrategy(StopStrategies.stopAfterDelay(1, TimeUnit.MINUTES))
                    .retryIfException()
                    .retryIfResult(success -> !success)
                    .build()
                    .call(() -> saveSnapshot(handleRecordId, caseId, workOrderId));
        } catch (Exception e) {
            log.error("saveSnapshotWithRetry error handleRecordId {}, caseId {}, workOrderId {}", handleRecordId, caseId, workOrderId, e);
        }
    }


    public boolean saveSnapshot(long recordId, int caseId, long workOrderId) {
        try {
            String snapshot = "";
            Response<String> stringResponse = selectHandleSnapshot(caseId, workOrderId);
            if (stringResponse.ok()) {
                snapshot = stringResponse.getData();
            }
            if (StringUtils.isEmpty(snapshot)) {
                log.info("get snapshot empty !!!");
                return false;
            }
            snapshot = apolloService.getCosSwitch() ? cosUploadUtil.uploadTextV1(snapshot, null) : cosUploadUtil.uploadText(snapshot, null);
            OperationResult<Boolean> updateResp = wonRecordClient.updateExtByRecordId(WonExtUpdateByIdParam.create(recordId, "snapshot", snapshot));
            log.info("ApproveSnapshotServiceImpl snapshot {} {} {} {} {}", recordId, caseId, workOrderId, snapshot, updateResp);
            return true;
        } catch (Exception e) {
            log.warn("ApproveSnapshotServiceImpl error", e);
        }
        return false;
    }
}
