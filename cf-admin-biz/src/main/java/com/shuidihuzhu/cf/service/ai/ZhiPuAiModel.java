package com.shuidihuzhu.cf.service.ai;

import com.shuidihuzhu.ai.alps.client.feign.AihubFeignClient;
import com.shuidihuzhu.ai.alps.client.model.aihub.ZhipuChatRequest;
import com.shuidihuzhu.client.cf.admin.model.AIGenerateParam;
import com.shuidihuzhu.client.model.ChatChunk;
import com.shuidihuzhu.client.model.ChatCompletionChunk;
import com.shuidihuzhu.common.web.model.Response;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2024/6/2 10:01 PM
 */
@Service
public class ZhiPuAiModel extends BaseModel {

    @Resource
    private AihubFeignClient aihubFeignClient;

    private static final String MODEL_NAME = "glm-4";

    @Override
    protected String callModelApi(AIGenerateParam aiGenerateParam, String prompt) {

        ZhipuChatRequest zhipuChatRequest = new ZhipuChatRequest();
        zhipuChatRequest.setModel(MODEL_NAME);
        zhipuChatRequest.setPrompt(prompt);
        zhipuChatRequest.setAppCode("1004");

        Response<String> response = aihubFeignClient.zhipuChat(zhipuChatRequest);
        String result = Optional.ofNullable(response)
                .filter(f -> f.ok())
                .map(f -> f.getData())
                .orElse("");

        return result;
    }

    @Override
    protected Flux<ChatChunk<ChatCompletionChunk>> stream(String prompt) {
        return super.streamGenerate(MODEL_NAME, prompt);
    }
}
