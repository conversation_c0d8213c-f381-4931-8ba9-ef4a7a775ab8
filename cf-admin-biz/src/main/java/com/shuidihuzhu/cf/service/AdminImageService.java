package com.shuidihuzhu.cf.service;

import com.shuidihuzhu.cf.constants.admin.CosConstants;
import com.shuidihuzhu.cf.store.enums.CfDomainEnum;
import com.shuidihuzhu.cf.store.model.AnalysisUrl;
import com.shuidihuzhu.cf.store.plugins.CosDomainPlugins;
import com.shuidihuzhu.cf.store.plugins.CosPlugins;
import com.shuidihuzhu.infra.starter.cos.enums.Font;
import com.shuidihuzhu.infra.starter.cos.model.TextWaterMark;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Slf4j
@Service
public class AdminImageService {
    @Autowired
    private CosDomainPlugins cosDomainPlugins;
    @Autowired
    private CosPlugins cosPlugins;

    /**
     * 替换单个url为cdn-domain
     */
    public String convertSingleUrl(String url) {
        if (StringUtils.isBlank(url)) {
            return url;
        }
        try {
            //解析url
            AnalysisUrl analysisUrl = AnalysisUrl.parse(url);
            //判断host是否需要替换
            String host = analysisUrl.getHost();
            CfDomainEnum cfDomain = CfDomainEnum.getByDomain(host);
            if (cfDomain == null || cfDomain == CfDomainEnum.COS_IMAGE) {
                return url;
            }
            //替换为apollo配置的url
            return cosDomainPlugins.convertCdnDomain(analysisUrl).toUrlString();
        } catch (Exception e) {
            log.error("", e);
        }
        return url;
    }

    public String convertSingleOriginUrl(String url) {
        if (StringUtils.isBlank(url)) {
            return url;
        }
        try {
            //解析url
            AnalysisUrl analysisUrl = AnalysisUrl.parse(url);
            //判断host是否需要替换
            String host = analysisUrl.getHost();
            CfDomainEnum cfDomain = CfDomainEnum.getByDomain(host);
            if (cfDomain == null) {
                return url;
            }
            //替换为apollo配置的url
            return cosDomainPlugins.convertOriginDomain(analysisUrl).toUrlString();
        } catch (Exception e) {
            log.error("", e);
        }
        return url;
    }

    /**
     * 替换逗号分割的url
     */
    public String convertMultiUrl(String imgsStr) {
        if (StringUtils.isBlank(imgsStr)) {
            return imgsStr;
        }
        String[] imgArray = StringUtils.split(imgsStr, ",");
        if (imgArray.length < 1) {
            return imgsStr;
        }
        try {
            for (int i = 0; i < imgArray.length; i++) {
                String url = imgArray[i];
                //解析url
                AnalysisUrl analysisUrl = AnalysisUrl.parse(url);
                //判断host是否需要替换
                String host = analysisUrl.getHost();
                CfDomainEnum cfDomain = CfDomainEnum.getByDomain(host);
                if (cfDomain == null || cfDomain == CfDomainEnum.COS_IMAGES) {
                    continue;
                }
                //替换为apollo配置的url
                imgArray[i] = cosDomainPlugins.convertCdnDomain(analysisUrl).toUrlString();
            }
            return StringUtils.join(imgArray, ",");
        } catch (Exception e) {
            log.error("", e);
        }
        return imgsStr;
    }

    /**
     * 只作用于cos
     * 生成动态水印
     */
    public String dynamicWaterMark(String url, String textWaterMark) {
        if (StringUtils.isAnyBlank(url, textWaterMark)) {
            return url;
        }
        AnalysisUrl analysisUrl = AnalysisUrl.parse(url);
        //判断host是否需要替换
        String host = analysisUrl.getHost();
        CfDomainEnum cfDomain = CfDomainEnum.getByDomain(host);
        String bucket = null;
        if (Objects.isNull(cfDomain)) {
            if ("cf-risk-img.shuidichou.com".equals(host)){
                bucket = CosConstants.CF_RISK_BUCKET;
            }
        } else {
            switch (cfDomain){
                case COS_IMAGE:
                    bucket = CosConstants.CF_IMAGES_BUCKET;
                    break;
                case COS_IMAGES:
                    bucket = CosConstants.CF_IMAGE_BUCKET;
                    break;
                default:
                    break;
            }
        }

        if (StringUtils.isBlank(bucket)){
            return null;
        }
        return cosPlugins.dynamicWaterMark(bucket, url, generateWaterMark(textWaterMark));
    }

    private TextWaterMark generateWaterMark(String text){
        TextWaterMark textWaterMark = new TextWaterMark(text, Font.SIM_SUN, 25);
        textWaterMark.setBatch(1);
        textWaterMark.setDegree(315);
        textWaterMark.setDissolve(30);
        textWaterMark.setFill("#E1FFFF");
        return textWaterMark;
    }

}