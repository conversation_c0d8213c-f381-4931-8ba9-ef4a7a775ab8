package com.shuidihuzhu.cf.service.stream.manager;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class StreamData {

    @ApiModelProperty("动作标志")
    private String flag;

    @ApiModelProperty("扩展数据")
    private Object extData;

    public static StreamData create(String flag, Object extData){
        StreamData v = new StreamData();
        v.setFlag(flag);
        v.setExtData(extData);
        return v;
    }
}
