package com.shuidihuzhu.cf.service.ai;

import com.shuidihuzhu.common.web.model.Response;

import java.util.Map;

/**
 * @Description: Ai图片掩码服务
 * @Author: panghairui
 * @Date: 2022/9/8 2:52 下午
 */
public interface AiImageMaskService {

    /**
     * 发送掩码通知消息
     */
    void sendImageMaskMq(Integer caseId, Integer bizType);

    /**
     * 发送掩码通知消息
     */
    void sendImageMaskMqByBizId(Integer caseId, Integer bizType, Long bizId);

    /**
     * 查询ugc掩码图片
     */
    Response<Map<String, Object>> queryUgcMaskAttachment(String infoId, String param);

}
