package com.shuidihuzhu.cf.service.workorder.delayfinance.impl;

import com.shuidihuzhu.cf.finance.model.vo.CfPromptDrawCashRecordVo;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 催提现工单
 *
 * <AUTHOR>
 * @since 2022-12-01 3:45 下午
 **/
@Service
public class PromptDrawCashFinanceWorkOrderStrategy extends AbstractFinanceWorkOrderStrategy<CfPromptDrawCashRecordVo> {


    @Override
    public List<CfPromptDrawCashRecordVo> getBusinessExt(List<Long> financeBusinessIds) {
        return this.financeWorkOrderDelegate.getPromptDrawCashRecordVo(financeBusinessIds);
    }

    @Override
    public int orderType() {
        return WorkOrderType.prompt_draw_cash.getType();
    }
}
