package com.shuidihuzhu.cf.service.ai;

import com.shuidihuzhu.ai.alps.client.feign.AihubFeignClient;
import com.shuidihuzhu.ai.alps.client.model.aihub.QwenChatRequest;
import com.shuidihuzhu.client.cf.admin.model.AIGenerateParam;
import com.shuidihuzhu.client.model.ChatChunk;
import com.shuidihuzhu.client.model.ChatCompletionChunk;
import com.shuidihuzhu.common.web.model.Response;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2024/6/4 2:04 PM
 */
@Service
public class TongYiAiModel extends BaseModel {

    @Resource
    private AihubFeignClient aihubFeignClient;

    private static final String MODEL_NAME = "qwen-max-longcontext";

    @Override
    protected String callModelApi(AIGenerateParam aiGenerateParam, String prompt) {

        QwenChatRequest qwenChatRequest = new QwenChatRequest();
        qwenChatRequest.setModel(MODEL_NAME);
        qwenChatRequest.setAppCode("1004");
        qwenChatRequest.setPrompt(prompt);
        Response<String> response = aihubFeignClient.qwenChat(qwenChatRequest);
        return Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse("");
    }

    public String callModelApiByModelName(AIGenerateParam aiGenerateParam, String prompt, String modelName) {

        QwenChatRequest qwenChatRequest = new QwenChatRequest();
        qwenChatRequest.setModel(modelName);
        qwenChatRequest.setAppCode("1004");
        qwenChatRequest.setPrompt(prompt);
        Response<String> response = aihubFeignClient.qwenChat(qwenChatRequest);
        return Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse("");
    }

    @Override
    protected Flux<ChatChunk<ChatCompletionChunk>> stream(String prompt) {
        return super.streamGenerate(MODEL_NAME, prompt);
    }
}
