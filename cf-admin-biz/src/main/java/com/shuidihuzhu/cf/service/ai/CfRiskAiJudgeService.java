package com.shuidihuzhu.cf.service.ai;

import com.shuidihuzhu.cf.enums.ai.AiRiskBizEnum;
import com.shuidihuzhu.cf.model.ai.AiRiskConfigResult;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2025/5/20 16:39
 */
public interface CfRiskAiJudgeService {

    void aiJudgeRisk(CrowdfundingInfo crowdfundingInfo, String judgeContent,  AiRiskBizEnum bizEnum);

    AiRiskConfigResult actionAiRisk(String actionType, String sceneType,
                                    String riskFactor, String judgePrompt);

}
