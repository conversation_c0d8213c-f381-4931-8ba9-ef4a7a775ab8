package com.shuidihuzhu.cf.service.crowdfunding;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.apipure.feign.CfRiskWorkOrderFeignClient;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.client.base.utils.ResultUtils;
import com.shuidihuzhu.cf.client.feign.CfRiskAssembleFeignClient;
import com.shuidihuzhu.cf.client.material.feign.CfFirstApproveClient;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoPayee;
import com.shuidihuzhu.cf.risk.client.aegis.EngineAnalysisClient;
import com.shuidihuzhu.cf.risk.client.risk.BlacklistVerifyClient;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistCallPhaseEnum;
import com.shuidihuzhu.cf.risk.model.risk.BlacklistPayeeLimitDto;
import com.shuidihuzhu.cf.risk.model.risk.BlacklistPreTrialAdoptionDto;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Optional;

/**
 * <AUTHOR>
 * @DATE 2020/8/20
 */
@Service
@RefreshScope
@Slf4j
public class CfAdminLimitService {

    @Autowired
    private BlacklistVerifyClient verifyClient;

    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;

    @Autowired
    private IRiskDelegate riskDelegate;

    @Autowired
    private ShuidiCipher shuidiCipher;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;

    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Autowired
    private EngineAnalysisClient engineAnalysisClient;

    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Autowired
    private CfFirstApproveClient cfFirstApproveClient;

    @Autowired
    private CfRiskWorkOrderFeignClient cfRiskWorkOrderFeignClient;

    @Autowired
    private CfRiskAssembleFeignClient cfRiskAssembleFeignClient;

    @Value("${apollo.risk.check-initial-order-create:true}")
    private boolean checkInitialOrderCreate;

    public boolean caseInLimit(int caseId, BlacklistCallPhaseEnum phaseEnum){

        boolean result = false;

        CrowdfundingInfo c = crowdfundingInfoBiz.getFundingInfoById(caseId);
        CfFirsApproveMaterial material = riskDelegate.getCfFirsApproveMaterialByInfoId(caseId);
        if (material == null){
            log.info("caseInLimit caseId={} material=null",caseId);
            return result;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (phaseEnum.equals(BlacklistCallPhaseEnum.SUBMIT_PRE_TRIAL)){
            BlacklistPreTrialAdoptionDto dto = new BlacklistPreTrialAdoptionDto();
            dto.setCaseId(caseId);
            dto.setBlacklistCallPhase(phaseEnum.getCode());
            dto.setCaseInitiateTime(sdf.format(c.getCreateTime()));
            UserInfoModel model = userInfoServiceBiz.getUserInfoByUserId(c.getUserId());
            dto.setInitiatorMobile(shuidiCipher.decrypt(model.getCryptoMobile()));
            dto.setUserId(c.getUserId());
            if (StringUtils.isNotEmpty(material.getSelfCryptoIdcard())){
                dto.setInitiatorIdCard(shuidiCipher.decrypt(material.getSelfCryptoIdcard()));
            }
            if (UserIdentityType.identity.getCode() == material.getPatientIdType()){
                dto.setPatientIdNumber(shuidiCipher.decrypt(material.getPatientCryptoIdcard()));
            }else {
                dto.setPatientIdNumber(material.getPatientBornCard());
            }
            if (StringUtils.isEmpty(dto.getInitiatorIdCard())){
                dto.setInitiatorIdCard(dto.getPatientIdNumber());
            }
            dto.setPatientIdType(material.getPatientIdType());
            Response<Boolean> response = verifyClient.preTrialAdoption(dto);
            result = Optional.ofNullable(response).map(Response::getData).orElse(false);
            log.debug("caseInLimit dto={},response={}",dto,response);
            log.info("caseInLimit dto={},response={}",dto.getCaseId(),response);

        }
        CrowdfundingRelationType relationType = c.getRelationType();
        log.info("caseInLimit caseId={} RelationType={}",caseId, relationType);
        if (phaseEnum.equals(BlacklistCallPhaseEnum.SUBMIT_MATERIAL_REVIEW)
                && (relationType.equals(CrowdfundingRelationType.other)|| relationType.equals(CrowdfundingRelationType.self))){

            BlacklistPayeeLimitDto dto = new BlacklistPayeeLimitDto();
            dto.setCaseId(caseId);
            dto.setBlacklistCallPhase(phaseEnum.getCode());
            dto.setCaseInitiateTime(sdf.format(c.getCreateTime()));

            CrowdfundingInfoPayee payee = crowdfundingDelegate.getCrowdfundingInfoPayeeByInfoUuid(c.getInfoId());
            if (payee == null){
                log.info("caseInLimit caseId={} payee=null ",caseId);
                return false;
            }
            dto.setPayeeMobile(shuidiCipher.decrypt(payee.getMobile()));
            dto.setPayeeIdCard(shuidiCipher.decrypt(payee.getIdCard()));

            Response<Boolean> response = verifyClient.payeeLimit(dto);
            result = Optional.ofNullable(response).map(Response::getData).orElse(false);
            log.debug("caseInLimit dto={},response={}",dto,response);
            log.info("caseInLimit dto={},response={}",dto.getCaseId(),response);

        }

        if (phaseEnum.equals(BlacklistCallPhaseEnum.SUBMIT_MATERIAL_REVIEW)){
            CrowdfundingInfoPayee payee = crowdfundingDelegate.getCrowdfundingInfoPayeeByInfoUuid(c.getInfoId());
            if (payee == null){
                log.info("caseInLimit caseId={} payee=null ",caseId);
                return false;
            }

            int payeeType = isPerson(relationType) ? 0 : 1;
            OperationResult<Boolean> hitResult = cfRiskAssembleFeignClient.checkIdentity(caseId, phaseEnum.getCode(), payeeType, "");
            if (hitResult != null && hitResult.isSuccess()) {
                Boolean hit = hitResult.getData();
                if (hit) {
                    return true;
                }
            }
        }

        return result;
    }

    private boolean isPerson(CrowdfundingRelationType relationType) {
        return relationType != CrowdfundingRelationType.LOCATION_HOSPITAL_ACCOUNT
                && relationType != CrowdfundingRelationType.charitable_organization;
    }

    public boolean checkCanCreateChuCi(int caseId) {
        OperationResult<Boolean> result = cfRiskWorkOrderFeignClient.checkCanCreateChuCi(caseId);
        if (ResultUtils.isFail(result)) {
            log.warn("checkCanCreate rpc fall caseId:{}", caseId);
            return true;
        }
        return result.getData();
    }

}
