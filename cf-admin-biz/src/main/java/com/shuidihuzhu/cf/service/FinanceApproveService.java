package com.shuidihuzhu.cf.service;

import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingOperationDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingApprove;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * Created by ahrievil on 2017/5/17.
 */
@Slf4j
@Service
public class FinanceApproveService {

    @Autowired
    private ICrowdfundingOperationDelegate crowdfundingOperationDelegate;

    public void addApprove(CrowdfundingInfo crowdfundingInfo, String operation, String comment, int userId){
        try {
            CrowdfundingApprove crowdfundingApprove = new CrowdfundingApprove();
            crowdfundingApprove.setCrowdfundingId(crowdfundingInfo.getId());
            crowdfundingApprove.setComment(operation + ":" + comment);
            crowdfundingApprove.setOprid(userId);
            crowdfundingApprove.setOprtime(new Date());
            crowdfundingApprove.setStatus(crowdfundingInfo.getStatus().value());
            crowdfundingOperationDelegate.insertCrowdfundingApprove(crowdfundingApprove);
        } catch (Exception e) {
            log.error("AdminApproveService addApprove error!", e);
        }
    }
}
