package com.shuidihuzhu.cf.service.sensitive.checker;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.primitives.Chars;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.sensitive.SensitiveSpecialSymbolLogBiz;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordResult;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.sensitive.adapter.ISensitiveAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.context.scope.refresh.RefreshScopeRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2020-03-26
 */
@Slf4j
@Service
@RefreshScope
public class IllegalSnippetChecker implements ISensitiveChecker {

    @Resource
    private AdminCrowdfundingInfoBiz infoBiz;
    @Resource
    private SensitiveSpecialSymbolLogBiz specialSymbolLogBiz;

    /**
     * 语言类字符unicode regular：汉语、藏语、维吾尔语、日语、韩语、俄语、
     */
    @Value("${sensitive.can-enter.language-character:''}")
    private String languageCharacter;
    /**
     * 英文字符类unicode regular
     */
    @Value("${sensitive.can-enter.english-character:''}")
    private String englishCharacter;
    /**
     * 数字类unicode regular
     */
    @Value("${sensitive.can-enter.number-character:''}")
    private String numberCharacter;
    /**
     * 下划线unicode regular
     */
    @Value("${sensitive.illegal-snippet.underline:''}")
    private String underlineCharacter;
    /**
     * 数字类型字符连续次数限制
     */
    @Value("${sensitive.illegal-snippet.number.limit:5}")
    private int numberContinuousLimit;
    /**
     * 数字类型字符连续次数去重后限制
     */
    @Value("${sensitive.illegal-snippet.number.repeat-limit:4}")
    private int numberContinuousRepeatLimit;
    /**
     * 字母类型字符连续次数限制
     */
    @Value("${sensitive.illegal-snippet.number-letter-combine.limit:6}")
    private int numberLetterCombineContinuousLimit;
    /**
     * 数字、字母、下划线类型字符连续次数去重后限制
     */
    @Value("${sensitive.illegal-snippet.number-letter-combine.repeat-limit:3}")
    private int numberLetterCombineContinuousRepeatLimit;
    /**
     * 数字、字母、下划线类型字符次数限制
     */
    @Value("${sensitive.illegal-snippet.number-letter-combine.repeat-limit-v2:4}")
    private int numberLetterCombineContinuousRepeatLimitV2;
    /**
     * 年份枚举
     */
    @Value("${sensitive.illegal-snippet.years-enums:''}")
    private String yearsEnums;

    /**
     * 语言类字符unicode regular：汉语、维吾尔语
     */
    @Value("${sensitive.can-enter.chinese-uyghur-language-character:''}")
    private String chineseUyghurLanguageCharacter;

    /**
     * 内容中，仅包含非【中文汉字】且非【维吾尔文字符】外的字母或字符，且内容长度≤3
     */
    @Value("${sensitive.illegal-snippet.number-chinese-uyghur-limit:3}")
    private int chineseUyghurLanguageRepeatLimit;


    private static Pattern numberPattern;
    private static Pattern numberLetterCombinePattern;
    private static Pattern numberLetterCombinePatternNoLimit;
    private static Pattern addForHelpNumberPattern;
    private static String outKeepPattern;
    private static String chineseUyghurPattern;
    private static String chineseUyghurNumberEnglishPattern;

    /**
     * 纯英文或者年份数字正则(支持重叠匹配)
     */
    private static Pattern pureEnglishOrYearsOverlapPattern;

    @PostConstruct
    public void init() {
        setPattern();
    }

    public static Pattern getPureEnglishOrYearsOverlapPattern() {
        return pureEnglishOrYearsOverlapPattern;
    }

    private void setPattern(){
        outKeepPattern = "[^" + languageCharacter + englishCharacter + numberCharacter + underlineCharacter + "]";
        numberPattern = Pattern.compile("["+numberCharacter+"]{"+ numberContinuousLimit +",}");
        numberLetterCombinePattern = Pattern.compile("["+numberCharacter+englishCharacter+underlineCharacter+"]{"+ numberLetterCombineContinuousLimit +",}");
        numberLetterCombinePatternNoLimit = Pattern.compile("["+numberCharacter+englishCharacter+underlineCharacter+"]");
        pureEnglishOrYearsOverlapPattern = Pattern.compile("(?=(^[a-zA-Z]+$|"+yearsEnums+"))");
        addForHelpNumberPattern = Pattern.compile("[加➕+].*(?:帮助|帮到你|帮你)");
        chineseUyghurPattern = "[^" + chineseUyghurLanguageCharacter + "]";
        chineseUyghurNumberEnglishPattern = "[^" + chineseUyghurLanguageCharacter + englishCharacter + numberCharacter +"]";
    }

    @EventListener(RefreshScopeRefreshedEvent.class)
    public void onRefresh(RefreshScopeRefreshedEvent event) {
        log.info("Refresh IllegalSnippetChecker regular success, event:{}", event);
        setPattern();
    }

    @Override
    public AdminWorkOrderConst.Task getTask() {
        return AdminWorkOrderConst.Task.SENSITIVE_NUMBER;
    }

    @Override
    public <T> OpResult<RiskWordResult> isHit(T data, ISensitiveAdapter<T> adapter) {
        //内容发布者不是当下内容所在案例的发起人
        long userId = adapter.getUserId(data);
        long caseUserId = getCaseUserId(adapter.getCaseId(data));
        if (userId == caseUserId) {
            log.info("内容发布者是当下内容所在案例的发起人, issueUserId:{}, caseUserId:{}", userId, caseUserId);
            return OpResult.createSucResult(
                    new RiskWordResult(true, "", Lists.newArrayList()));
        }
        OpResult<RiskWordResult> result = symbolChecker(adapter.getContent(data));
        if (!result.getData().isPassed()) {
            saveLog(data, adapter, result.getData().getHitWords());
        }
        return result;
    }

    @Async
    public <T> void saveLog(T data, ISensitiveAdapter<T> adapter, List<String> hitWords) {
        specialSymbolLogBiz.insert(adapter.getBizId(data), adapter.getCaseId(data),
                adapter.getSensitiveRecordBizType(data), adapter.getContent(data),
                JSON.toJSONString(hitWords));
    }


    /**
     *
     * @param caseId    案例id
     * @return 获取当前案例的UserId
     */
    private long getCaseUserId(int caseId) {
        if (caseId <= 0) {
            return 0;
        }
        CrowdfundingInfo crowdfundingInfo = infoBiz.getFundingInfoById(caseId);
        return crowdfundingInfo == null ? 0 : crowdfundingInfo.getUserId();
    }

    /**
     * 数字字符连续累计出现{@link #numberContinuousLimit}个及以上（重复字符计算多次），
     * 或 数字/字母/下划线类字符累计出现{@link #numberLetterCombineContinuousLimit}个及以上（重复字符计算多次）
     * @param contentOrigin 需要匹配的内容
     * @return 匹配结果
     */
    private OpResult<RiskWordResult> symbolChecker(String contentOrigin) {

        List<String> addForHelpNumberMatch = collectFinalHitWords(match(addForHelpNumberPattern, contentOrigin), numberLetterCombineContinuousRepeatLimitV2, numberLetterCombinePatternNoLimit);
        if (CollectionUtils.isNotEmpty(addForHelpNumberMatch)) {
            return OpResult.createSucResult(new RiskWordResult(false, contentOrigin, addForHelpNumberMatch));
        }

        //内容仅保留：【文字字符】、【数字字符】、【字母字符】、【中划线】、【下划线】
        String content = contentOrigin.replaceAll(outKeepPattern, StringUtils.EMPTY);
        List<String> numberMatch = collectFinalHitWords(match(numberPattern, content), numberContinuousRepeatLimit);
        if (CollectionUtils.isNotEmpty(numberMatch)) {
            return OpResult.createSucResult(new RiskWordResult(false, contentOrigin, numberMatch));
        }
        List<String> combineMatch = collectFinalHitWords(match(numberLetterCombinePattern, content), numberLetterCombineContinuousRepeatLimit);
        if (CollectionUtils.isNotEmpty(combineMatch)) {
            return OpResult.createSucResult(new RiskWordResult(false, contentOrigin, combineMatch));
        }
        List<String> chineseUyghurMatch = collectFinalHitWords(contentOrigin.replaceAll(chineseUyghurPattern, StringUtils.EMPTY), chineseUyghurLanguageRepeatLimit, contentOrigin);
        if (CollectionUtils.isNotEmpty(chineseUyghurMatch)) {
            return OpResult.createSucResult(new RiskWordResult(false, contentOrigin, chineseUyghurMatch));
        }
        List<String> chineseUyghurNumberEnglishMatch = collectFinalHitWords(contentOrigin.replaceAll(chineseUyghurNumberEnglishPattern, StringUtils.EMPTY), contentOrigin);
        if (CollectionUtils.isNotEmpty(chineseUyghurNumberEnglishMatch)) {
            return OpResult.createSucResult(new RiskWordResult(false, contentOrigin, chineseUyghurNumberEnglishMatch));
        }

        return OpResult.createSucResult(new RiskWordResult(true, contentOrigin, Lists.newArrayList()));
    }

    private List<String> collectFinalHitWords(List<String> hitMatches, int repeatLimit){
        ArrayList<String> finalHits = Lists.newArrayListWithCapacity(hitMatches.size());
        for (String match : hitMatches) {
            if (Sets.newHashSet(Chars.asList(match.toCharArray())).size() >= repeatLimit) {
                finalHits.add(match);
            }
        }
        return finalHits;
    }

    private List<String> collectFinalHitWords(String hitMatches, int repeatLimit, String contentOrigin) {
        ArrayList<String> finalHits = Lists.newArrayListWithCapacity(1);
        if (StringUtils.isEmpty(hitMatches) && StringUtils.length(contentOrigin) <= repeatLimit) {
            finalHits.add(contentOrigin);
        }
        return finalHits;
    }

    private List<String> collectFinalHitWords(String hitMatches, String contentOrigin) {
        ArrayList<String> finalHits = Lists.newArrayListWithCapacity(1);
        if (StringUtils.isEmpty(hitMatches)) {
            finalHits.add(contentOrigin);
        }
        return finalHits;
    }

    private List<String> collectFinalHitWords(List<String> hitMatches, int repeatLimit, Pattern pattern){
        ArrayList<String> finalHits = Lists.newArrayListWithCapacity(hitMatches.size());
        for (String match : hitMatches) {
            List<String> hitWord = match(pattern, match);
            if (hitWord.size() >= repeatLimit) {
                finalHits.add(match);
            }
        }
        return finalHits;
    }

    /**
     * @param pattern           正则表达式
     * @param input             匹配字符串
     * @return List<String> 匹配结果
     */
    private List<String> match(Pattern pattern, String input) {
        Matcher matcher = pattern.matcher(input);
        List<String> hitWords = Lists.newArrayList();
        while (matcher.find()) {
            hitWords.add(matcher.group());
        }
        return hitWords;
    }

    /**
     * 仅供后门使用
     * @param data
     * @param adapter
     * @param <T>
     * @return
     */
    public <T> OpResult<RiskWordResult> backDoorIsHit(T data, ISensitiveAdapter<T> adapter) {
        return symbolChecker(adapter.getContent(data));
    }

}
