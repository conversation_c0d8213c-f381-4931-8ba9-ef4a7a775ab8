package com.shuidihuzhu.cf.service.dingding;

import brave.Tracing;
import com.google.common.collect.Lists;
import com.shuidi.weixin.common.util.StringUtils;
import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Created by wangsf on 17/9/17.
 */
@Service
public class AdminDingdingBizImpl implements AdminDingdingBiz {

//	private static final String DINGDING_TOKEN = "c0a1b203c8601cf12a5fafee5d420aa228207dd90a1623c209e20c3f41f2fe05";
	private static final String URL = "http://dingbot.shuiditech.com:8099/dingding-bot/dingdingtalkbot/send-msg";

	private static final Logger LOGGER = LoggerFactory.getLogger(AdminDingdingBiz.class);

	@Autowired
	private Tracing tracing;
	private static ExecutorService executorService ;
	@PostConstruct
	public void init() {
		executorService = tracing.currentTraceContext().executorService(Executors.newFixedThreadPool(3));
	}
	private OkHttpClient okHttpClient = new OkHttpClient();

	@Override
	public void sendMsg(int botId, String message, boolean atAll, List<String> mobiles) {
		LOGGER.info("botId={};sendMsg:{};atAll:{};mobiles:{}", botId, message, atAll, mobiles);

		if(StringUtils.isEmpty(message)) {
			return;
		}

		RequestBody requestBody = new FormBody.Builder()
				.add("botId", botId + "")
				.add("msg", message)
				.add("isAtAll", atAll + "")
				.add("atMobiles", String.join(",", mobiles))
				.build();

		final Request request = new Request.Builder()
				.url(URL)
				.post(requestBody)
				.build();

		Runnable runnable=new Runnable() {
			@Override
			public void run() {
				try {
					long start = System.currentTimeMillis();
					okHttpClient.newCall(request).execute();
					long cost = System.currentTimeMillis() - start;
					LOGGER.info("sendMsg Success. cost={}ms", cost);
				} catch (IOException e) {
					LOGGER.error("SendMsg Error!", e);
				}
			}
		};
		executorService.execute(runnable);
	}

	public static void main(String[] args) {
		AdminDingdingBiz dingdingBiz = new AdminDingdingBizImpl();
		dingdingBiz.sendMsg(55, "【水滴筹积分异常报警】你好我是报警机器人。测试测试", true, Lists.newArrayList());
	}
}
