package com.shuidihuzhu.cf.service.workorder.delayfinance;

import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-12-01 2:16 下午
 **/
public interface IBaseFinanceWorkOrderStrategy {

    /**
     * 填充资金业务数据
     *
     * @param workOrderVoList -
     */
    void fillBusinessExt(List<? extends WorkOrderVO> workOrderVoList);

    /**
     * 工单类型
     *
     * @return -
     */
    int orderType();

}
