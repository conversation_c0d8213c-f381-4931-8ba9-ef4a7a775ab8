package com.shuidihuzhu.cf.service.tag;

import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditOperationMqVO;
import com.shuidihuzhu.client.cf.admin.model.ThreeBodyTag;
import com.shuidihuzhu.client.cf.growthtool.model.RuleJudge;

import java.util.List;

/**
 * @Description: 三体案例服务
 *
 * @Author: panghairui
 * @Date: 2022/8/24 7:28 下午
 */
public interface CfThreeBodyService {

    /**
     * 保存案例命中三体情况
     */
    void saveThreeBodyCaseTab(Integer caseId);

    /**
     * 查询案例是否为三体案例
     */
    Boolean judgeIsThreeBodyTag(Integer caseId);

    /**
     * 查询多个案例是否是三体案例
     */
    List<ThreeBodyTag> judgeThreeBodyByCaseIds(List<Integer> caseIds);

    /**
     * 判断规则项是否满足三体策略
     */
    Boolean judgeThreeBodyRule(RuleJudge ruleJudge);

}
