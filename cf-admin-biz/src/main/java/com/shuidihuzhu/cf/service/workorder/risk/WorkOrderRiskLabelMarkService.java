package com.shuidihuzhu.cf.service.workorder.risk;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.model.admin.FundUseWorkOrderVO;
import com.shuidihuzhu.cf.model.admin.workorder.RiskLabelMarkWorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.ext.WorkOrderExtFeignClient;
import com.shuidihuzhu.client.cf.workorder.ext.WorkOrderExtVO;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderListParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.read.WorkOrderReadFeignClient;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: wangpeng
 * @Date: 2022/12/4 16:03
 * @Description:
 */
@Service
public class WorkOrderRiskLabelMarkService {

    @Resource
    private WorkOrderReadFeignClient workOrderReadFeignClient;

    @Resource
    private WorkOrderExtFeignClient workOrderExtFeignClient;

    public Response<PageResult<RiskLabelMarkWorkOrderVO>> riskLabelMarkWorkOrderList(WorkOrderListParam workOrderListParam) {
        PageResult<RiskLabelMarkWorkOrderVO> pageMarkResult = new PageResult<>();

        Response<PageResult<WorkOrderVO>> pageResultResponse = workOrderReadFeignClient.getOrderListByListParam(workOrderListParam);
        if (pageResultResponse.notOk() || Objects.isNull(pageResultResponse.getData()) || CollectionUtils.isEmpty(pageResultResponse.getData().getPageList())) {
            return NewResponseUtil.makeSuccess(pageMarkResult);
        }
        PageResult<WorkOrderVO> pageResult = pageResultResponse.getData();
        List<WorkOrderVO> pageList = pageResult.getPageList();
        List<Long> workOrderIdList = pageList.stream()
                .map(WorkOrderVO::getWorkOrderId)
                .collect(Collectors.toList());
        Response<List<WorkOrderExtVO>> listByNameAndIdListScene = workOrderExtFeignClient.getListByNameAndIdList(workOrderIdList, OrderExtName.riskLabelMarkWorkOrderScene.name());
        Map<Long, WorkOrderExtVO> extSceneMap = Optional.ofNullable(listByNameAndIdListScene)
                .map(Response::getData)
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.toMap(WorkOrderExtVO::getWorkOrderId, Function.identity(), (x, y) -> x));

        Response<List<WorkOrderExtVO>> listByNameAndIdListLabelId = workOrderExtFeignClient.getListByNameAndIdList(workOrderIdList, OrderExtName.riskLabelMarkWorkOrderRiskLabelId.name());
        Map<Long, WorkOrderExtVO> extLabelIdMap = Optional.ofNullable(listByNameAndIdListLabelId)
                .map(Response::getData)
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.toMap(WorkOrderExtVO::getWorkOrderId, Function.identity(), (x, y) -> x));
        Response<List<WorkOrderExtVO>> listByNameAndIdListAuditOrderId = workOrderExtFeignClient.getListByNameAndIdList(workOrderIdList, OrderExtName.riskLabelMarkWorkOrderAuditOrderId.name());
        Map<Long, WorkOrderExtVO> extAuditOrderIdMap = Optional.ofNullable(listByNameAndIdListAuditOrderId)
                .map(Response::getData)
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.toMap(WorkOrderExtVO::getWorkOrderId, Function.identity(), (x, y) -> x));
        List<RiskLabelMarkWorkOrderVO> orderVOList = Lists.newArrayList();
        for (WorkOrderVO workOrderVO : pageList) {
            RiskLabelMarkWorkOrderVO riskLabelMarkWorkOrderVO = new RiskLabelMarkWorkOrderVO();
            BeanUtils.copyProperties(workOrderVO, riskLabelMarkWorkOrderVO);
            WorkOrderExtVO workOrderExtVOLabelId = extLabelIdMap.get(workOrderVO.getWorkOrderId());
            WorkOrderExtVO workOrderExtVOScene = extSceneMap.get(workOrderVO.getWorkOrderId());
            WorkOrderExtVO workOrderExtVOAuditOrderId = extAuditOrderIdMap.get(workOrderVO.getWorkOrderId());
            riskLabelMarkWorkOrderVO.setRiskLabelMarkWorkOrderRiskLabelId(Objects.isNull(workOrderExtVOLabelId) ? "" : workOrderExtVOLabelId.getValue());
            riskLabelMarkWorkOrderVO.setAuditWorkOrderId(Objects.nonNull(workOrderExtVOAuditOrderId) && StringUtils.isNotEmpty(workOrderExtVOAuditOrderId.getValue()) ? Long.parseLong(workOrderExtVOAuditOrderId.getValue()) : 0);
            riskLabelMarkWorkOrderVO.setRiskLabelMarkWorkOrderScene(Objects.nonNull(workOrderExtVOScene) && StringUtils.isNotEmpty(workOrderExtVOScene.getValue()) ? Integer.parseInt(workOrderExtVOScene.getValue()) : 0);
            orderVOList.add(riskLabelMarkWorkOrderVO);
        }
        pageResult.setHasNext(pageResult.isHasNext());
        pageMarkResult.setPageList(orderVOList);
        return NewResponseUtil.makeSuccess(pageMarkResult);
    }
}
