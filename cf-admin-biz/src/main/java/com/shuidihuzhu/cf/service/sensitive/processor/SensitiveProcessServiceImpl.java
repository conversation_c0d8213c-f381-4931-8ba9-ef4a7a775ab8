package com.shuidihuzhu.cf.service.sensitive.processor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.biz.admin.AdminWorkOrderBiz;
import com.shuidihuzhu.cf.biz.admin.UserCommentBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminTaskUgcBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingVolunteerBiz;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordResult;
import com.shuidihuzhu.cf.constants.AdminUserIDConstants;
import com.shuidihuzhu.cf.dao.crowdfunding.CfSensitiveWordRecordDao;
import com.shuidihuzhu.cf.delegate.ai.AiUgcRiskDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.enums.admin.UserCommentSourceEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminUGCTask;
import com.shuidihuzhu.cf.enums.crowdfunding.CfBlacklistEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfSensitiveWordRecordEnum;
import com.shuidihuzhu.cf.enums.risk.UgcTypeEnum;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.admin.UserComment;
import com.shuidihuzhu.cf.model.crowdfunding.AdminTaskUgc;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfSensitiveWordRecordVo;
import com.shuidihuzhu.cf.model.risk.verify.RiskUgcVerifyModel;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.crowdfunding.SensitiveWordService;
import com.shuidihuzhu.cf.service.risk.dark.DarkListService;
import com.shuidihuzhu.cf.service.sensitive.adapter.ISensitiveAdapter;
import com.shuidihuzhu.cf.service.sensitive.checker.*;
import com.shuidihuzhu.cf.service.sensitive.checker.word.ProhibitionWordChecker;
import com.shuidihuzhu.cf.service.sensitive.checker.word.SensitiveWordChecker;
import com.shuidihuzhu.client.cf.workorder.CfUgcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.UgcWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import com.shuidihuzhu.data.analytics.javasdk.core.UserTagTypeEnum;
import com.shuidihuzhu.data.servicelog.meta.cf.CreateUgcOrder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-03-14  14:41
 */
@Service
@RefreshScope
@Slf4j
public class SensitiveProcessServiceImpl implements SensitiveProcessService {

    @Autowired
    private CfSensitiveWordRecordDao cfSensitiveWordRecordDao;

    @Autowired
    private AdminWorkOrderBiz adminWorkOrderBiz;

    @Autowired
    private AdminTaskUgcBiz adminTaskUgcBiz;

    @Resource
    private IRiskDelegate riskDelegate;

    @Resource
    private ProhibitionWordChecker prohibitionWordChecker;

    @Resource
    private RiskChannelChecker riskChannelChecker;

    @Resource
    private VerificationCommentChecker verificationCommentChecker;

    @Resource
    private SpecialSymbolChecker specialSymbolChecker;

    @Resource
    private SensitiveNumberChecker sensitiveNumberChecker;

    @Resource
    private IllegalSnippetChecker illegalSnippetChecker;

    @Resource
    private SensitiveWordChecker sensitiveWordChecker;

    @Resource
    private CanEnterCharacterChecker canEnterCharacterChecker;

    @Resource
    private AiUgcRiskChecker aiUgcRiskChecker;

    @Resource
    private UserCommentBiz userCommentBiz;

    @Resource
    private SensitiveWordService sensitiveWordService;

    @Resource
    private CrowdfundingVolunteerBiz crowdfundingVolunteerBiz;

    @Resource
    private CfUgcWorkOrderClient ugcWorkOrderClient;

    @Resource
    private UgcSensitiveWordChecker ugcWordChecker;

    @Resource
    private ManyVerificationChecker verificationChecker;

    /**
     * 是否自动处理工单
     */
    @Value("${sensitive.is-auto-handle-work-order: true}")
    private boolean isAutoHandleWorkOrder;

    private ImmutableList<ISensitiveChecker> checkers;

    @Autowired
    private DarkListService darkListService;

    @Autowired
    private Analytics analytics;

    @Autowired
    private AiUgcRiskDelegate aiUgcRiskDelegate;

    /**
     * 初始化检查项目 有顺序
     */
    @PostConstruct
    public void init(){
        checkers = ImmutableList.of(
                //非法字符检测
                canEnterCharacterChecker,
                // 敏感渠道
                riskChannelChecker,
                // 敏感号码
//                sensitiveNumberChecker,
                //特殊符号
//                specialSymbolChecker,
                //敏感账号策略(沿用敏感渠道的自动处理逻辑，所以类型还是使用敏感号码的类型)
                illegalSnippetChecker,
//                // 禁止词
//                prohibitionWordChecker,
//                // 敏感词
//                sensitiveWordChecker,

                //异常多次证实识别策略（https://wiki.shuiditech.com/pages/viewpage.action?pageId=680034425）
//                verificationChecker,

                //禁止词、敏感词
                ugcWordChecker,

                // 敏感时期 动态评论全量人工
                verificationCommentChecker,
                // ai
                aiUgcRiskChecker
        );
    }

    /**
     * 依次检查各种情况风险。
     * 0. 敏感渠道
     * 1. 敏感号码
     * 2. 禁止词
     * 3. 敏感词
     * 命中一个创建工单并返回 不会检查其他项目
     *
     * @param data
     * @param adapter
     * @param <T>
     */
    @Override
    public <T> void process(T data, ISensitiveAdapter<T> adapter) {
        if (data == null) {
            log.info("data null ugcType: {}", adapter.getUgcTypeEnum());
            return;
        }
        long bizId = adapter.getBizId(data);
        long caseId = adapter.getCaseId(data);

        UgcTypeEnum ugcTypeEnum = adapter.getUgcTypeEnum();

        log.info("process caseId: {}, ugcTypeEnum: {}, bizId: {}", caseId, ugcTypeEnum, bizId);

        // 先获取所有checker检查结果
        Map<ISensitiveChecker, OpResult<RiskWordResult>> resultMap = Maps.newHashMap();
        for (ISensitiveChecker checker : checkers) {
            OpResult<RiskWordResult> hitResult = checker.isHit(data, adapter);
            resultMap.put(checker, hitResult);
        }
        // 按顺序判断是否命中 并生成工单
        for (ISensitiveChecker checker : checkers) {
            boolean hit = processRecord(data, adapter, checker, resultMap);
            log.info("checker hit: {}, task: {}, caseId: {}, ugcTypeEnum: {}, bizId: {}",
                    hit, checker.getTask(), caseId, ugcTypeEnum, bizId);
            if (hit) {
                return;
            }
        }
        this.track(bizId, adapter.getUniquelyIdentifies(data), adapter.getUserId(data), "无需生成工单");
        log.info("pass caseId: {}, ugcTypeEnum: {}, bizId: {}", caseId, ugcTypeEnum, bizId);
    }

    @Override
    public void handleWorkOrder(AdminWorkOrder adminWorkOrder, AdminUGCTask.Result result) {
        long workOrderId = adminWorkOrder.getId();
        AdminTaskUgc adminTaskUgc = adminTaskUgcBiz.selectByWorkOrderId(workOrderId);
        int contentType = adminTaskUgc.getContentType();
        AdminUGCTask.Content contentTypeEnum = AdminUGCTask.Content.getByCode(contentType);

        ContextUtil.setAdminUserId(AdminUserIDConstants.SYSTEM);
        String reason = "自动处理";
        String comment = reason;

        log.info("系统自动处理ugc工单 内容类型: {}, 处理结果: {}, 工单ID: {}", contentTypeEnum.getWord(), result.getWord(), workOrderId);

        // 系统领取工单
        adminWorkOrderBiz.onHandleOrder(adminWorkOrder);

        // 处理工单
        switch (contentTypeEnum) {
            case VERIFICATION:
                sensitiveWordService.handleVerification(adminWorkOrder, Maps.newHashMap(), result, comment, reason);
                break;
            case COMMENT_ORDER:
            case COMMENT_PROGRESS:
                sensitiveWordService.handleComment(adminWorkOrder, Maps.newHashMap(), result, comment, reason, contentTypeEnum);
                break;
            default:
                break;
        }

        //增加UGC图文审核记录
        UserComment userComment = new UserComment(
                adminTaskUgc.getCaseId(),
                UserCommentSourceEnum.UGC,
                UserCommentSourceEnum.CommentType.getCommetType(contentTypeEnum),
                ContextUtil.getAdminUserId(),
                reason,
                result.getWord(),
                comment);
        userCommentBiz.add(userComment);
    }

    /**
     * 检查禁止词是否命中
     * 1.获取UGC内容
     * 2.检查是否命中
     * 3.存储命中记录
     * 4.创建工单
     *
     * @param data
     * @param adapter
     * @param <T>
     * @return 是否命中
     */
    @Override
    public <T> boolean processRecord(T data, ISensitiveAdapter<T> adapter, ISensitiveChecker sensitiveWorkChecker,
                                     Map<ISensitiveChecker, OpResult<RiskWordResult>> resultMap){
        if (data == null) {
            return false;
        }
        AdminWorkOrderConst.Task task = sensitiveWorkChecker.getTask();

        OpResult<RiskWordResult> hit = resultMap.get(sensitiveWorkChecker);

        if(hit.isSuccessWithNonNullData() && hit.getData().isPassed()){
            log.info("isHit 未命中 ugcType: {}, checkItem: {}, bizId: {}, hit: {}",
                    adapter.getUgcTypeEnum(),
                    task,
                    adapter.getBizId(data),
                    hit);
            return false;
        }

        RiskWordResult hitResult;
        if (hit.isFailOrNullData()) {

            // 请求失败按命中处理生成工单人工审核
            hitResult = new RiskWordResult(false, adapter.getContent(data), Lists.newArrayList());
        } else {
            hitResult = hit.getData();
        }
        log.info("isHit 命中 ugcType: {}, checkItem: {}, bizId: {}, hitResult: {}",
                adapter.getUgcTypeEnum(),
                task,
                adapter.getBizId(data),
                hitResult);

        Set<String> hitSets = Sets.newHashSet();
        Set<ISensitiveChecker> hitCheckerSet = Sets.newHashSet();
        Set<Integer> allHitTaskCode = Sets.newHashSet();
        for (Map.Entry<ISensitiveChecker, OpResult<RiskWordResult>> resultEntry : resultMap.entrySet()) {
            final OpResult<RiskWordResult> value = resultEntry.getValue();
            if (value.isSuccessWithNonNullData()) {

                // 需要所有命中词的标红
                final RiskWordResult valueHitResult = value.getData();
                final List<String> hitWords = valueHitResult.getHitWords();
                if (CollectionUtils.isNotEmpty(hitWords)) {
                    hitSets.addAll(hitWords);
                }

                // 记录所有命中策略
                if(!valueHitResult.isPassed()){
                    hitCheckerSet.add(resultEntry.getKey());
                    allHitTaskCode.add(resultEntry.getKey().getTask().getCode());
                }
            }

        }
        // 存储记录
        String hitWord = StringUtils.join(hitSets, ",");
        CfSensitiveWordRecordVo record = adapter.buildRecord(data, hitWord);
        boolean isVerification = adapter.getUgcTypeEnum() == UgcTypeEnum.VERIFICATION;
        blackListFilter(record, isVerification);
        saveBatch(Lists.newArrayList(record));

        // 创建工单
        int mode = record.getMode();
        if (mode == CfBlacklistEnum.Mode.ONLY_HIMSELF.getValue()) {
            log.info("黑名单中 record {}", record);
            this.track(adapter.getBizId(data), adapter.getUniquelyIdentifies(data), adapter.getUserId(data), "在黑名单中");
            return true;
        }

        if (record.getInfoId() == 0) {
            log.error("生成ugc工单错误  caseId=0 record {}", record);
            this.track(adapter.getBizId(data), adapter.getUniquelyIdentifies(data), adapter.getUserId(data), "生成ugc工单错误");
            return true;
        }
        //生成新工单
        UgcWorkOrder workOrder = new UgcWorkOrder();
        workOrder.setCaseId(record.getInfoId());
        workOrder.setOrderType(WorkOrderType.ugcpinglun.getType());
        workOrder.setExtId(record.getBizId()+"");
        workOrder.setContentType(record.getBizType()+"");
        workOrder.setWordId(record.getId()+"");
        workOrder.setHitSensitiveStrategyCode(String.valueOf(StringUtils.join(allHitTaskCode, ",")));

        Response<Long> response =  ugcWorkOrderClient.createUgc(workOrder);
        log.info("createUgc workOrder={},response={}",workOrder, JSON.toJSONString(response));


        AdminWorkOrder ugcOrder = null;

        // 设置仅本人可见
        CfSensitiveWordRecordEnum.BizType bizTypeEnum = CfSensitiveWordRecordEnum.BizType.getByValue(record.getBizType());
        UgcTypeEnum ugcTypeEnum = bizTypeEnum.getUgcTypeEnum();
        addVerify(record.getInfoId(), ugcTypeEnum, record.getBizId());

        long newWorkOrderId = 0;
        if (response != null && response.ok()) {
            newWorkOrderId = response.getData();
            this.track(adapter.getBizId(data), adapter.getUniquelyIdentifies(data), adapter.getUserId(data), "生成工单");
        }

        // 自动处理逻辑
        autoHandleWorkOrder(ugcOrder, data, adapter, sensitiveWorkChecker, hitResult,newWorkOrderId, allHitTaskCode);
        return true;
    }

    /**
     * <a href="https://wiki.shuiditech.com/pages/viewpage.action?pageId=168887958">自动处理WIKI</a>
     * @param <T>
     * @param ugcOrder
     * @param data
     * @param adapter
     * @param sensitiveWorkChecker
     * @param hitResult
     * @param allHitTaskCode
     */
    private <T> void autoHandleWorkOrder(AdminWorkOrder ugcOrder,
                                         T data,
                                         ISensitiveAdapter<T> adapter,
                                         ISensitiveChecker sensitiveWorkChecker,
                                         RiskWordResult hitResult,
                                         long newWorkOrderId,
                                         Set<Integer> allHitTaskCode) {
        if (!isAutoHandleWorkOrder) {
            return;
        }

        // 命中ai直接自动处理自己可见
        if (allHitTaskCode.contains(AdminWorkOrderConst.Task.AI_RISK.getCode())){
            closeWorkOrder(newWorkOrderId,AdminUGCTask.Result.ONLY_SELF);
            return;
        }

        // 仅限敏感号码需要处理
        AdminWorkOrderConst.Task task = sensitiveWorkChecker.getTask();
        if (task != AdminWorkOrderConst.Task.SENSITIVE_NUMBER) {
            return;
        }

        // 部分类型ugc需要自动处理
        boolean needAutoHandle = adapter.needAutoHandle();
        if (!needAutoHandle) {
            return;
        }

        // 志愿者自动通过
        long userId = adapter.getUserId(data);
        boolean isVolunteer = crowdfundingVolunteerBiz.checkVolunteerByUserId(userId);
        if (isVolunteer) {
//            closeWorkOrder(newWorkOrderId,AdminUGCTask.Result.PASS_AND_SHOW);
//            if(ugcOrder != null){
//                handleWorkOrder(ugcOrder, AdminUGCTask.Result.PASS_AND_SHOW);
//            }
            return;
        }

        //如果命中的内容中只要有一组为纯字母,或存在1900~2025这一组四位数中的任一一个，则走人工处理策略
        List<String> hitWords = hitResult.getHitWords();
        Pattern pureEnglishOrYearsOverlapPattern = IllegalSnippetChecker.getPureEnglishOrYearsOverlapPattern();
        for (String hitWord : hitWords) {
            Matcher matcher = pureEnglishOrYearsOverlapPattern.matcher(hitWord);
            if (matcher.find()) {
                log.info("满足纯英文或者年份数字，走人工审核，不自动处理，caseId:{}, userId:{}, hitWord:{}", adapter.getCaseId(data), userId, hitWord);
                return;
            }
        }

        // 自动处理为仅本人可见
        closeWorkOrder(newWorkOrderId,AdminUGCTask.Result.ONLY_SELF);
        if (ugcOrder != null){
            handleWorkOrder(ugcOrder, AdminUGCTask.Result.ONLY_SELF);
        }
    }


    private void closeWorkOrder(long orderId,AdminUGCTask.Result result){
        String reason = "自动处理";
        Response response = sensitiveWordService.handleUgcWorkId(result.getCode(),reason,AdminUserIDConstants.SYSTEM,orderId+"",0);
        log.info("closeWorkOrder orderId={} reason={} result={} response={}",orderId,reason,result,JSON.toJSONString(response));
    }

    /**
     * 保存命中记录
     *
     * @param cfSensitiveWordRecordVos
     * @return
     */
    private int saveBatch(List<CfSensitiveWordRecordVo> cfSensitiveWordRecordVos) {
        if (CollectionUtils.isEmpty(cfSensitiveWordRecordVos)) {
            return 0;
        }
        return cfSensitiveWordRecordDao.saveBatch(cfSensitiveWordRecordVos);
    }


    /**
     * 设置本人可见
     * @param caseId
     * @param ugcTypeEnum
     * @param bizId
     */
    private void addVerify(int caseId, UgcTypeEnum ugcTypeEnum, long bizId) {
        RiskUgcVerifyModel v = new RiskUgcVerifyModel(caseId, ugcTypeEnum, bizId, "UGC风控词过滤");
        OpResult<RiskUgcVerifyModel> addRes = riskDelegate.addVerify(v);
        if (addRes.isFail()) {
            log.warn("{}, {}, {}, {}", addRes, caseId, ugcTypeEnum, bizId);
        }
    }

    /**
     * 是否在黑名单，如果在就设置mode & contentValid
     *
     * @param recordVo
     * @param isVerification
     */
    private void blackListFilter(CfSensitiveWordRecordVo recordVo, boolean isVerification) {
        long userId = recordVo.getUserId();
        boolean passed = darkListService.checkUGCPassed(userId, isVerification);
        if (passed) {
            return;
        }
        recordVo.setMode(1);
        recordVo.setContentValid(0);
    }


    private void track(long bizId, String getUniquelyIdentifies, long userId, String operateContent) {
        CreateUgcOrder createUgcOrder = new CreateUgcOrder();
        try {
            createUgcOrder.setWork_order_id(bizId);

            createUgcOrder.setUser_tag(String.valueOf(userId));
            createUgcOrder.setUser_tag_type(UserTagTypeEnum.userid);
            createUgcOrder.setUniquely_identifies(StringUtils.defaultString(getUniquelyIdentifies));
            createUgcOrder.setOperate_content(StringUtils.defaultString(operateContent));

            analytics.track(createUgcOrder);
            log.info("大数据打点上报,ugc工单成功:{}", JSONObject.toJSONString(createUgcOrder));
        } catch (Exception e) {
            log.error("大数据打点上报异常,ugc工单成功:{}", JSONObject.toJSONString(createUgcOrder));
        }
    }
}
