package com.shuidihuzhu.cf.service.report;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.IAdminCredibleInfoService;
import com.shuidihuzhu.cf.biz.crowdfunding.report.AdminCfReportDisposeActionBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.report.CfSendProveBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.report.CfSendProveTemplateBiz;
import com.shuidihuzhu.cf.client.adminpure.constants.MQTagPureAdminCons;
import com.shuidihuzhu.cf.client.adminpure.constants.ReportCons;
import com.shuidihuzhu.cf.client.adminpure.model.report.ReportInfoVO;
import com.shuidihuzhu.cf.client.adminpure.model.report.ReportOperationPayload;
import com.shuidihuzhu.cf.client.adminpure.model.report.ReportProveActionDetailVO;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.delegate.SeaAccountDelegate;
import com.shuidihuzhu.cf.enhancer.mq.MQHelperService;
import com.shuidihuzhu.cf.enums.crowdfunding.AddTrustAuditStatusEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CredibleTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfCredibleInfoDO;
import com.shuidihuzhu.cf.model.crowdfunding.CfSendProve;
import com.shuidihuzhu.cf.model.crowdfunding.CfSendProveTemplate;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfReportAddTrustDisposeVo;
import com.shuidihuzhu.cf.model.report.CfReportDisposeAction;
import com.shuidihuzhu.cf.vo.report.ReportProveInfoVO;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ReportOperationService {

    @Autowired
    private MQHelperService mqHelperService;

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @Autowired
    private SeaAccountDelegate seaAccountDelegate;

    @Autowired
    private CfSendProveTemplateBiz cfSendProveTemplateBiz;

    @Autowired
    private CfSendProveBiz cfSendProveBiz;

    @Autowired
    private IAdminCredibleInfoService adminCredibleInfoService;

    @Autowired
    private AdminCfReportDisposeActionBiz adminCfReportDisposeActionBiz;

    @Autowired
    private CfReportCredibleInfoService cfReportCredibleInfoService;

    public OperationResult<Void> operation(int caseId, @ReportCons.ActionType int actionType, long operatorId) {
        log.info("report operation caseId {}, actionType {}, operatorId {}", caseId, actionType, operatorId);
        try {
            ReportOperationPayload payload = new ReportOperationPayload();
            payload.setCaseId(caseId);
            payload.setActionType(actionType);
            payload.setOperatorId((int) operatorId);
            payload.setActionTime(new Date());
            OperationResult<ReportInfoVO> infoResp = getInfo(caseId);
            if (infoResp == null || infoResp.isFail()) {
                log.info("info fail");
                return OperationResult.fail();
            }
            payload.setReportInfoVO(infoResp.getData());
            mqHelperService.builder()
                    .setTags(MQTagPureAdminCons.REPORT_OPERATION)
                    .addKey(caseId, System.currentTimeMillis())
                    .setPayload(JSON.toJSONString(payload))
                    .send();
        } catch (Exception e) {
            log.error("report operation error caseId {}, actionType {}, operatorId {}",
                    caseId, actionType, operatorId, e);
        }
        return OperationResult.success();
    }

    public OperationResult<ReportInfoVO> getInfo(int caseId) {
        ReportInfoVO v = new ReportInfoVO();
        v.setCaseId(caseId);
        Response<WorkOrderVO> workOrderRes = cfWorkOrderClient.getLastWorkOrderByTypes(caseId, WorkOrderType.REPORT_TYPES);
        if (workOrderRes == null || workOrderRes.notOk()) {
            return OperationResult.fail();
        }
        WorkOrderVO order = workOrderRes.getData();
        if (order == null) {
            return OperationResult.success(v);
        }
        long operatorId = order.getOperatorId();
        String name = seaAccountDelegate.getNameByUserId((int) operatorId);
        int status = getStatus(order);

        v.setCreateTime(order.getCreateTime());
        v.setFinishTime(order.getFinishTime());

        promoteGhost(v, caseId);

        v.setOperatorName(name);
        v.setOperatorId((int) operatorId);
        v.setReportStatus(status);
        return OperationResult.success(v);
    }

    private void promoteGhost(ReportInfoVO v, int caseId) {
        CfCredibleInfoDO lastCredit = adminCredibleInfoService.getLastOneByCaseId(caseId, CredibleTypeEnum.HELP_PROVE.getKey());
        if (lastCredit == null) {
            return;
        }
        v.setGhostInputStatusMsg(AddTrustAuditStatusEnum.getByCode(lastCredit.getAuditStatus()).getMsg());
//        List<CfSendProveTemplate> proves = cfSendProveTemplateBiz.findByCaseIdAndProveId(caseId, lastCredit.getSubId());
//        if (CollectionUtils.isEmpty(proves)) {
//            return;
//        }
        List<CfReportAddTrustDisposeVo> lastActionByCaseId = cfReportCredibleInfoService.getLastActionByCaseId(caseId);
        if (CollectionUtils.isEmpty(lastActionByCaseId)) {
            return;
        }
        List<ReportProveActionDetailVO> actionList = lastActionByCaseId.stream()
                .map(this::mapVO)
                .collect(Collectors.toList());
        v.setGhostInputActionNames(actionList);

        // 最新可信内容
        promoteLastContent(v, caseId, lastCredit.getSubId());
    }

    private void promoteLastContent(ReportInfoVO v, int caseId, Long subId) {
        // 图片
        final ReportProveInfoVO proveInfo = cfReportCredibleInfoService.getProveInfo(caseId, subId);
        final CfSendProve cfSendProve = proveInfo.getCfSendProve();
        final String pictureUrl = cfSendProve.getPictureUrl();
        if (StringUtils.isNotBlank(pictureUrl)) {
            final List<String> picUrlList = Splitter.on(",").splitToList(pictureUrl);
            v.setLatestImageList(picUrlList);
        }

        final List<String> lines = Lists.newArrayList();
        String SPLIT = "\n";
        //模板
        final List<CfSendProveTemplate> templateList = proveInfo.getTemplate();
        if (CollectionUtils.isNotEmpty(templateList)) {
            for (CfSendProveTemplate t : templateList) {
                lines.add("");
                lines.add(t.getTitle());
                lines.add(t.getContent());
                lines.add(t.getCommitmentContent());
            }
        }
        // 文本 & 图片
        lines.add("");
        final List<CfReportAddTrustDisposeVo> proveDisposeAction = proveInfo.getProveDisposeAction();
        if (CollectionUtils.isNotEmpty(proveDisposeAction)) {
            for (CfReportAddTrustDisposeVo a : proveDisposeAction) {
                int index = 1;
                lines.add(a.getActionClassify());
                for (CfReportAddTrustDisposeVo.CfReportDisposeActionInfo disposeActionInfo : a.getDisposeActionInfos()) {
                    lines.add(index + "、" + disposeActionInfo.getDisposeAction());
                }
            }
        }
        final String collect = String.join(SPLIT, lines);
        v.setLatestContent(collect);
    }

    private ReportProveActionDetailVO mapVO(CfReportAddTrustDisposeVo a) {
        if (a == null) {
            return null;
        }
        ReportProveActionDetailVO b = new ReportProveActionDetailVO();
        b.setActionClassify(a.getActionClassify());
        List<CfReportAddTrustDisposeVo.CfReportDisposeActionInfo> disposeActionInfos = a.getDisposeActionInfos();
        if (CollectionUtils.isEmpty(disposeActionInfos)) {
            return b;
        }
        List<String> actions = disposeActionInfos.stream()
                .map(CfReportAddTrustDisposeVo.CfReportDisposeActionInfo::getDisposeAction).collect(Collectors.toList());
        b.setDisposeAction(actions);
        return b;
    }

    /**
     * https://wiki.shuiditech.com/pages/viewpage.action?pageId=754452763
     * 根据当前案例id对应的举报工单类型及举报工单状态做判断：
     * <p>
     * 优先判断当前案例下最新一条举报工单状态是否为“未分配”：
     * 若是：则对应小鲸鱼侧举报工单状态为：待处理
     * 若否：
     * 判断当前案例下最新一条举报工单状态是否为处理中、稍后处理、达成一致：
     * 若是：则对应小鲸鱼侧举报工单状态为：处理中
     * 若否：
     * 判断案例下最新一条举报工单是否为首次举报工单或历史举报工单或二线工单：
     * 若是：
     * 判断该工单状态是否为“转入失联”或“升级二线”：
     * 若是：则对应小鲸鱼侧举报工单状态为：处理中
     * 若否：即首次/历史/二线工单状态为无需处理或结束处理；则对应小鲸鱼侧举报工单状态为：处理完成；
     * 若否：即该案例为工单状态为“结束处理”或“无需处理”的失联工单：
     * 则对应小鲸鱼侧举报工单状态为：处理完成；
     *
     * @param order
     * @return
     */
    private int getStatus(WorkOrderVO order) {
        if (order == null) {
            return 0;
        }
        if (order.getOperatorId() <= 0) {
            return 1;
        }
        HandleResultEnum resultEnum = HandleResultEnum.getFromType(order.getHandleResult());
        if (resultEnum == HandleResultEnum.doing
                || resultEnum == HandleResultEnum.later_doing
                || resultEnum == HandleResultEnum.reach_agree) {
            return 2;
        }
        int orderType = order.getOrderType();
        if (orderType != WorkOrderType.casefirstreport.getType()
                && orderType != WorkOrderType.casehistoryreport.getType()
                && orderType != WorkOrderType.up_grade_second.getType()) {
            return 3;
        }
        if (resultEnum == HandleResultEnum.end_deal_lost
                || resultEnum == HandleResultEnum.end_deal_upgrade) {
            return 2;
        }
        return 3;
    }
}
