package com.shuidihuzhu.cf.service.admin;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingAuthorBiz;
import com.shuidihuzhu.cf.client.feign.CfFirstApproveFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.dao.admin.AdminFrameEnginebasicStoreDao;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingInfoDao;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingRecommendCaseDao;
import com.shuidihuzhu.cf.dao.crowdfunding.CfRecommendHistoryDao;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.enums.admin.AdminCrowdfundingInfoTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.client.cf.growthtool.client.CfGrowthtoolFeginClient;
import com.shuidihuzhu.client.cf.growthtool.model.BdCrmVolunteerOrgnizationSimpleModel;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.pf.common.v2.model.pagehelper.PaginationListVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @author: wanghui
 * @create: 2018/10/22 2:12 PM
 * @desc:  首页案例 更新service
 */
@Service
@Slf4j
public class AdminPageRecommendCaseService {

    @Autowired
    private AdminCrowdfundingInfoDao crowdfundingInfoDao;

    @Autowired
    private AdminCrowdfundingRecommendCaseDao crowdfundingRecommendCaseDao;

    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;

    @Autowired
    private AdminCfInfoExtBiz adminCfInfoExtBiz;

    @Resource
    private CfFirstApproveFeignClient cfFirstApproveFeignClient;

    @Resource
    private AdminCrowdfundingAuthorBiz adminCrowdfundingAuthorBiz;

    @Autowired
    private CfGrowthtoolFeginClient cfGrowthtoolFeginClient;

    /**
     * 检查案例是否在首页推荐展示
     * @param infoUuid
     * @return
     */
    public boolean isCaseInRecommendList(String infoUuid) {
        Integer type = crowdfundingRecommendCaseDao.getTypeByInfoIdAndTypes(infoUuid, AdminCrowdfundingInfoTypeEnum.RECOMMEND_LIST);
        return type != null;
    }


    public Response<Void> addCaseBatch(List<AdminCrowdfundingRecommendCaseDO> list, long userId, int type, int sort) {

        if (CollectionUtils.isEmpty(list)) {
            return NewResponseUtil.makeFail("数据为空");
        }

        for (AdminCrowdfundingRecommendCaseDO caseDO : list) {
            addCase(caseDO.getInfoId(), type, sort, (int) userId);
        }

        return NewResponseUtil.makeSuccess();
    }

    /**
     * 新增案例白名单
     * @param infoUuid
     * @param type
     * @return
     */
    public Response<Void> addCase(String infoUuid, int type, int sort, int userId) {
        //案例信息
        CrowdfundingInfo caseInfo = crowdfundingInfoDao.getByInfoId(infoUuid);
        if(caseInfo == null){
            return NewResponseUtil.makeFail("创建失败 案例uuid输入错误");
        }
        //案例结束
        if(caseInfo.getEndTime().before(new Date())){
            return NewResponseUtil.makeFail("创建失败 案例已结束");
        }
        CfInfoExt cfInfoExt = adminCfInfoExtBiz.getByCaseId(caseInfo.getId());
        if(cfInfoExt == null){
            return NewResponseUtil.makeFail("创建失败 案例uuid输入错误");
        }

        //校验该案例是否已经添加过
        AdminCrowdfundingRecommendCaseDO recommendCaseDO = crowdfundingRecommendCaseDao.getByUuid(infoUuid, type);
        if(recommendCaseDO != null){
            return NewResponseUtil.makeFail("创建失败 此案例uuid已经添加");
        }

        //校验案例状态 初审通过
        if(cfInfoExt.getFirstApproveStatus() != FirstApproveStatusEnum.APPLY_SUCCESS.getCode()){
            return NewResponseUtil.makeFail("创建失败 此案例未通过审批");
        }

        AuthRpcResponse<String> userName = seaAccountClientV1.getNameByUserId(userId);
        String name = "";
        if(userName.isSuccess()){
            name = userName.getData(String.class);
        }

        AdminCrowdfundingRecommendCaseDO caseDO = new AdminCrowdfundingRecommendCaseDO();
        caseDO.setInfoId(infoUuid);
        caseDO.setType(type);
        caseDO.setSort(sort);

        caseDO.setCaseStatus(caseInfo.getStatus()== CrowdfundingStatus.FINISHED ? 1 : 0);
        caseDO.setCaseId(Long.valueOf(caseInfo.getId()));
        caseDO.setCreator(name);
        caseDO.setUserId(userId);

        String patientName = StringUtils.EMPTY;
        if (caseInfo.getStatus() == CrowdfundingStatus.CROWDFUNDING_STATED) {
            //材料审核通过查询患者表
            CrowdfundingAuthor crowdfundingAuthor = adminCrowdfundingAuthorBiz.get(caseInfo.getId());
            if (Objects.nonNull(crowdfundingAuthor)) {
                patientName = crowdfundingAuthor.getName();
            }
        } else {
            //材料审核未通过查询初审表
            FeignResponse<CfFirsApproveMaterial> feignResponse = cfFirstApproveFeignClient.getCfFirstApproveMaterialByCaseId(caseInfo.getId());
            CfFirsApproveMaterial cfFirsApproveMaterial = Optional.ofNullable(feignResponse).filter(FeignResponse::ok).map(FeignResponse::getData).orElse(null);
            if (Objects.nonNull(cfFirsApproveMaterial)) {
                patientName = cfFirsApproveMaterial.getPatientRealName();
            }
        }

        caseDO.setPatientName(patientName);

        int num = crowdfundingRecommendCaseDao.insert(caseDO);
        if(num > 0){
            return NewResponseUtil.makeSuccess(null);
        }
        return NewResponseUtil.makeFail("创建失败");
    }

    /**
     * 白名单列表
     * @param infoUuid
     * @param creator
     * @param status
     * @param limit
     * @param current
     * @return
     */
    public Response<PaginationListVO<AdminCrowdfundingRecommendCaseDO>> getCaseList(String infoUuid, String creator,
                                                                                    Integer status, Integer limit, Integer current) {
        Integer offset = (current-1) * limit;
        List<AdminCrowdfundingRecommendCaseDO> caseDOS = crowdfundingRecommendCaseDao.getCaseList(infoUuid,creator,status,
                limit,offset,AdminCrowdfundingInfoTypeEnum.TYPE_1.getType());
        fill(caseDOS);
        List<Long> count =crowdfundingRecommendCaseDao.getCaseListCount(infoUuid,creator,status,AdminCrowdfundingInfoTypeEnum.TYPE_1.getType());
        PaginationListVO<Long> pageResult = PaginationListVO.createWithList(count);
        return NewResponseUtil.makeSuccess(PaginationListVO.create(caseDOS, pageResult.getPagination()));
    }

    /**
     * 删除案例
     * @param id
     * @param userId
     * @return
     */
    public Response<Void> delete(long id, int userId) {
        AdminCrowdfundingRecommendCaseDO caseDO = crowdfundingRecommendCaseDao.getById(id);
        if(caseDO == null){
            return NewResponseUtil.makeFail("案例不存在");
        }

        log.info("delete recommend case id:{},userId:{}",id,userId);
        int num = crowdfundingRecommendCaseDao.delete(id);
        if(num > 0){
            return NewResponseUtil.makeSuccess(null);
        }
        return NewResponseUtil.makeFail("删除失败");
    }

    /**
     * 结束案例
     * @param caseId
     */
    public void endCase(int caseId) {
        CfInfoExt cfInfoExt = adminCfInfoExtBiz.getByCaseId(caseId);
        AdminCrowdfundingRecommendCaseDO recommendCaseDO = crowdfundingRecommendCaseDao.getByUuid(cfInfoExt.getInfoUuid(), AdminCrowdfundingInfoTypeEnum.TYPE_1.getType());
        if(recommendCaseDO == null || recommendCaseDO.getCaseStatus() == 1){
            return;
        }
        int num = crowdfundingRecommendCaseDao.updateStatus(recommendCaseDO.getId(),1);
        log.info("recommend case end caseId:{},num:{}",caseId,num);
    }

    private void fill(List<AdminCrowdfundingRecommendCaseDO> caseDOS) {
        if (CollectionUtils.isEmpty(caseDOS)) {
            return;
        }

        for (AdminCrowdfundingRecommendCaseDO caseDO : caseDOS) {
            if (Objects.isNull(caseDO)) {
                continue;
            }
            CfInfoExt cfInfoExt = adminCfInfoExtBiz.getByInfoUuid(caseDO.getInfoId());
            if (Objects.isNull(cfInfoExt) || !"BD".equals(cfInfoExt.getPrimaryChannel())) {
                continue;
            }

            Response<List<BdCrmVolunteerOrgnizationSimpleModel>> response = cfGrowthtoolFeginClient.getBdCrmVolunteerOrgnizationSimpleModelByUniqueCode(cfInfoExt.getVolunteerUniqueCode());
            List<BdCrmVolunteerOrgnizationSimpleModel> list = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList());

            Optional<String> optional = list.stream().map(BdCrmVolunteerOrgnizationSimpleModel::getMisName).findFirst();

            optional.ifPresent(caseDO::setAdviserName);

        }

    }
}
