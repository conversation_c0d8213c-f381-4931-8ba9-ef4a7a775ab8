package com.shuidihuzhu.cf.service.workorder.delayfinance.impl;

import com.shuidihuzhu.cf.finance.model.vo.drawcash.CaseSecondRecordV2Vo;
import com.shuidihuzhu.cf.service.workorder.delayfinance.delegate.IFinanceWorkOrderDelegate;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-06-15 3:37 PM
 **/
@Service
public class CfSecondAuditFinanceWorkOrderStrategy extends AbstractFinanceWorkOrderStrategy<CaseSecondRecordV2Vo> {

    @Resource
    private IFinanceWorkOrderDelegate iFinanceWorkOrderDelegate;

    @Override
    public int orderType() {
        return WorkOrderType.second_pause.getType();
    }

    @Override
    public List<CaseSecondRecordV2Vo> getBusinessExt(List<Long> financeBusinessIds) {
        return iFinanceWorkOrderDelegate.getSecondRecordVo(financeBusinessIds);
    }
}
