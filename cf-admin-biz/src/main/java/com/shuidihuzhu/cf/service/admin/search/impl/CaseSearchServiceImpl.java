package com.shuidihuzhu.cf.service.admin.search.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.admin.util.admin.AdminCfIdCardUtil;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.admin.search.CaseSearchService;
import com.shuidihuzhu.cf.vo.admin.SimilarCaseInfo;
import com.shuidihuzhu.client.cf.search.client.CfSearchClient;
import com.shuidihuzhu.client.cf.search.model.SearchRpcResult;
import com.shuidihuzhu.client.cf.search.model.SimilarCaseSearchParam;
import com.shuidihuzhu.common.web.util.DateUtil;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2024/11/13 3:47 PM
 */
@Slf4j
@Service
public class CaseSearchServiceImpl implements CaseSearchService {

    @Resource
    private CfSearchClient cfSearchClient;

    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Override
    public List<SimilarCaseInfo> similarCaseSearch(String diseaseName, String idCard) {

        // 搜索相似案例
        int age = AdminCfIdCardUtil.getIdCardAge(idCard);
        SimilarCaseSearchParam similarCaseSearchParam = SimilarCaseSearchParam.builder()
                .age(age == -1 ? 0 : age)
                .diseaseNameList(List.of(Objects.requireNonNull(StringUtils.split(diseaseName, ",，"))))
                .fromBeginTime(getStrTimestamp(DateUtil.getCurrentTimestamp(), -365))
                .toBeginTime(getStrTimestamp(DateUtil.getCurrentTimestamp(), 0))
                .build();
        SearchRpcResult<List<Integer>> searchRpcResult = cfSearchClient.searchSimilarCase(similarCaseSearchParam);
        List<Integer> caseIds = Optional.ofNullable(searchRpcResult)
                .map(SearchRpcResult::getData)
                .orElse(Lists.newArrayList());
        if (CollectionUtils.isEmpty(caseIds) || caseIds.size() == 0 || caseIds.size() > 10) {
            return Lists.newArrayList();
        }

        // 查出相似案例信息
        List<CrowdfundingInfo> crowdfundingInfos = adminCrowdfundingInfoBiz.selectByCaseIdList(caseIds);
        if (CollectionUtils.isEmpty(crowdfundingInfos)) {
            return Lists.newArrayList();
        }

        // 按照捐单量排个序
        crowdfundingInfos.sort(Comparator.comparing(CrowdfundingInfo::getDonationCount).reversed());

        return crowdfundingInfos.stream()
                .map(crowdfundingInfo -> new SimilarCaseInfo(crowdfundingInfo.getId(), crowdfundingInfo.getContent(), crowdfundingInfo.getDonationCount()))
                .collect(Collectors.toList());
    }

    private String getStrTimestamp(Timestamp diffTimestamp,int addDay) {
        return String.valueOf(DateUtil.addDays(diffTimestamp, addDay).getTime());
    }

}
