package com.shuidihuzhu.cf.service.stream.manager;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.enhancer.mq.MaliMQComponent;
import com.shuidihuzhu.cf.model.broadcasting.PushBroadcastingV2;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.context.request.async.DeferredResult;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class BaseStreamService<T> {

    private final Map<String, Map<String, DeferredResult<T>>> subjectMaps = Maps.newConcurrentMap();

    protected boolean enableAutoConvertKey2String(){
        return true;
    }

    public DeferredResult<T> getDeferredResult(String subject, Object key) {
        DeferredResult<T> dr =
                new DeferredResult<>(30000L, this::createRetryResult);

        if (key == null) {
            log.warn("deferredResult param err,key={}", key);
            dr.setErrorResult("retry");
            return dr;
        }
        String stringKey = String.valueOf(key);

        log.info("Deferred get key={}", stringKey);

        dr.onCompletion(() -> {
            getResultMap(subject).remove(stringKey);
            log.info("Deferred subject {}, key={} onCompletion", subject, stringKey);
        });

        dr.onTimeout(() -> {
            getResultMap(subject).remove(stringKey);
            log.info("Deferred subject {}, key={} onTimeout", subject, stringKey);
        });

        Map<String, DeferredResult<T>> resultMap = getResultMap(subject);
        resultMap.put(stringKey, dr);

        return dr;
    }

    @NotNull
    private Map<String, DeferredResult<T>> getResultMap(String subject) {
        return subjectMaps.computeIfAbsent(subject, k -> Maps.newConcurrentMap());
    }

    public void pushBroadcast(String subject, Object key, T data) {
        log.info("push key {}, value {}", key, data);
        String stringKey = String.valueOf(key);
        PushBroadcastingV2 pushBroadcasting = PushBroadcastingV2.builder()
                .subject(subject)
                .key(stringKey)
                .value(JSON.toJSONString(data))
                .build();
        MaliMQComponent.builder()
                .setTags(MQTagCons.COMMON_STREAM_V2)
                .addKey(MQTagCons.COMMON_STREAM_V2, System.currentTimeMillis())
                .setPayload(pushBroadcasting)
                .send();
    }

    public void pushSingle(String subject, Object key, T data) {
        String stringKey = String.valueOf(key);
        Map<String, DeferredResult<T>> resultMap = getResultMap(subject);
        DeferredResult<T> dr = resultMap.get(stringKey);
        if (dr == null) {
            log.debug("链接不在此pod上");
            return;
        }
        dr.setResult(data);
        log.debug("推送成功 key {}, data {}", key, data);
    }

    protected abstract T createRetryResult();

}
