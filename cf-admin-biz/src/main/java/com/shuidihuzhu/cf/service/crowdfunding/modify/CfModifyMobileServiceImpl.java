package com.shuidihuzhu.cf.service.crowdfunding.modify;

import com.shuidihuzhu.cf.client.adminpure.model.caseinfo.CfModifyMobileRecord;
import com.shuidihuzhu.cf.dao.crowdfunding.CfModifyMobileRecordDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2021/9/14 21:26
 * @Description:
 */
@Service
public class CfModifyMobileServiceImpl implements CfModifyMobileService {
    @Resource
    private CfModifyMobileRecordDao cfModifyMobileRecordDao;

    @Override
    public int addRecord(CfModifyMobileRecord cfModifyMobileRecord) {
        return cfModifyMobileRecordDao.insert(cfModifyMobileRecord);
    }

    @Override
    public List<CfModifyMobileRecord> getByCaseId(int caseId) {
        return cfModifyMobileRecordDao.selectByCaseId(caseId);
    }
}
