package com.shuidihuzhu.cf.service.label.core.service;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.dao.labels.core.LabelDAO;
import com.shuidihuzhu.cf.domain.label.core.LabelDO;
import com.shuidihuzhu.cf.domain.label.core.LabelNodeModel;
import com.shuidihuzhu.cf.domain.label.risk.Label;
import com.shuidihuzhu.cf.service.label.risk.LabelMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class LabelServiceImpl implements LabelService{

    @Autowired
    private LabelDAO labelDAO;

    @Override
    public LabelNodeModel getEnableLabelNodeById(long id) {
        final Label label = getLabelById(id);
        return getEnableLabelNode(label);
    }

    @Override
    public List<LabelNodeModel> getSimpleEnableLabelNodeByIdList(List<Long> labelIdList) {
        if (CollectionUtils.isEmpty(labelIdList)) {
            return Lists.newArrayList();
        }
        final List<Label> labels = getLabelByIds(labelIdList);
        final List<LabelNodeModel> views = Lists.newArrayList();
        for (Label label : labels) {
            views.add(getEnableLabelNode(label));
        }
        return views;
    }

    private LabelNodeModel getEnableLabelNode(Label d) {
        final LabelNodeModel vo = new LabelNodeModel();
        vo.setLabel(d);
        List<Label> childs = getEnableChildListById(d.getId());
        List<LabelNodeModel> childNodeList = Lists.newArrayList();
        childs.sort(Comparator.comparing(Label::getSeq));
        for (Label child : childs) {
            childNodeList.add(getEnableLabelNode(child));
        }
        vo.setChildLabelNodeList(childNodeList);
        return vo;
    }

    private List<Label> getEnableChildListById(long id) {
        return LabelMapper.INSTANCE.toLabelList(labelDAO.getEnableChildListById(id));
    }

    private Label getByUuid(String uuid) {
        LabelDO labelDO = labelDAO.getByUuid(uuid);
        return LabelMapper.INSTANCE.toLabel(labelDO);
    }

    @Override
    public List<Label> getLabelByIds(List<Long> ids) {
        return LabelMapper.INSTANCE.toLabelList(labelDAO.getLabelByIds(ids));
    }

    @Override
    public Label getLabelById(Long id) {
        LabelDO labelDO = labelDAO.getById(id);
        return LabelMapper.INSTANCE.toLabel(labelDO);
    }

    @Override
    public List<Label> getLabelByUuidList(List<String> uuidList) {
        List<LabelDO> list = labelDAO.getLabelByUuidList(uuidList);
        return LabelMapper.INSTANCE.toLabelList(list);
    }

}
