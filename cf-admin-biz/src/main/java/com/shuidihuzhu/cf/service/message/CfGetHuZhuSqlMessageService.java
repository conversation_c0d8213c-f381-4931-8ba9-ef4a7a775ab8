package com.shuidihuzhu.cf.service.message;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.model.message.HuZhuSqlRecord;
import com.shuidihuzhu.common.web.util.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.stereotype.Service;

import java.util.*;


@Slf4j
@Service

public class CfGetHuZhuSqlMessageService {
    //请求互助
    private static final String TOKEN = "687403CA-7ED3-4CE7-928F-048369E43980";
    private static final String URL = "https://inner-sea.shuidihuzhu.com/admin/market/hivetask/query";

    /**
     * 在创建子任务的时候,把互助返回的记录下来
     *
     * @param id
     * @return
     */
    public HuZhuSqlRecord getHuZhuSqlRecord(int id) {
        if (id <= 0) {
            return null;
        }
        try {
            List<NameValuePair> nameValuePairList = Lists.newArrayList();
            nameValuePairList.add(new BasicNameValuePair("id", String.valueOf(id)));
            nameValuePairList.add(new BasicNameValuePair("token", TOKEN));
            String sqlString = HttpUtil.httpPostBodyString(URL, nameValuePairList, "utf-8",
                    5000, 5000, 5000);
            if (StringUtils.isEmpty(sqlString)) {
                //第一次不成功重发
                if (StringUtils.isEmpty(sqlString)) {
                    log.info("请求互助的接口超时无响应,id:{}", id);
                }
            }
            return this.formatHuZhuSqlRecord(sqlString);
        } catch (Exception e) {
            log.error("请求互助的接口出错,id:{}", id, e);
        }
        return null;
    }

    /**
     * 将互助返回的数据转为对象
     *
     * @param sqlString
     * @return
     */
    private HuZhuSqlRecord formatHuZhuSqlRecord(String sqlString) {
        try {
            JSONObject dataJson = JSONArray.parseObject(sqlString);//已检查过
            HuZhuSqlRecord huZhuSqlRecord = JSON.parseObject(dataJson.getJSONObject("data").toJSONString(), HuZhuSqlRecord.class);//已检查过
            huZhuSqlRecord.setSqlId(huZhuSqlRecord.getId());
            huZhuSqlRecord.setId(0);
            huZhuSqlRecord.setSqlCreateTime(huZhuSqlRecord.getCreateTime());
            huZhuSqlRecord.setCreateTime(null);
            huZhuSqlRecord.setSqlUpdateTime(huZhuSqlRecord.getUpdateTime());
            huZhuSqlRecord.setUpdateTime(null);
            //将\n\t换成空格
            huZhuSqlRecord.setHiveSql(huZhuSqlRecord.getHiveSql().
                    replaceAll("\n", " ").replace("\t", " "));
            return huZhuSqlRecord;
        } catch (Exception e) {
            log.error("互助返回的数据转为对象出错", e);
        }
        return null;
    }

}
