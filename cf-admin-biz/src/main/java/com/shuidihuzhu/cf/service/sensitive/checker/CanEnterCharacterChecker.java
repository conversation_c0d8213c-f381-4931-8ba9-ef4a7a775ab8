package com.shuidihuzhu.cf.service.sensitive.checker;

import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordResult;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.sensitive.adapter.ISensitiveAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.context.scope.refresh.RefreshScopeRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Collections;

/**
 * 可以输入的UGC字符检测器
 * <AUTHOR>
 * @date 2020-05-09
 */
@Slf4j
@Service
@RefreshScope
public class CanEnterCharacterChecker implements ISensitiveChecker {

    /**
     * 语言类字符unicode regular：汉语、藏语、维吾尔语、日语、韩语、俄语、
     */
    @Value("${sensitive.can-enter.language-character:''}")
    private String languageCharacter;
    /**
     * 空格不可见字符类unicode regular：空白字符or不可见字符、控制字符、不可见格式化指示符、保留供私人代码点
     */
    @Value("${sensitive.can-enter.space-empty-character:''}")
    private String spaceEmptyCharacter;
    /**
     * 特殊字符类unicode regular
     */
    @Value("${sensitive.can-enter.special-symbol-character:''}")
    private String specialSymbolCharacter;
    /**
     * 英文字符类unicode regular
     */
    @Value("${sensitive.can-enter.english-character:''}")
    private String englishCharacter;
    /**
     * 数字类unicode regular
     */
    @Value("${sensitive.can-enter.number-character:''}")
    private String numberCharacter;
    /**
     * 匹配emoji官方V13版本所有表情符的unicode字素
     */
    @Value("${sensitive.can-enter.emoji-character:''}")
    private String emojiCharacter;
    /**
     * 字符类字符unicode regular
     */
    @Value("${sensitive.can-enter.symbol-character:''}")
    private String symbolCharacter;

    private static String regular;

    @EventListener(RefreshScopeRefreshedEvent.class)
    public void onRefresh(RefreshScopeRefreshedEvent event) {
        log.info("Refresh canEnterCharacterChecker regular success, event:{}", event);
        regular = getCanEnterCharacters();
    }

    @PostConstruct
    public void init(){
        regular = getCanEnterCharacters();
    }

    public String getCanEnterCharacters() {
        return "["+languageCharacter+spaceEmptyCharacter+specialSymbolCharacter+
                englishCharacter+numberCharacter+emojiCharacter+symbolCharacter+"]";
    }

    @Override
    public AdminWorkOrderConst.Task getTask() {
        return AdminWorkOrderConst.Task.ILLEGAL_SYMBOL;
    }

    @Override
    public <T> OpResult<RiskWordResult> isHit(T data, ISensitiveAdapter<T> adapter) {
        String content = adapter.getContent(data);
        if (StringUtils.isNotEmpty(content)) {
            String leftover = content.replaceAll(regular, "");
            if (StringUtils.isNotEmpty(leftover)) {
                log.info("Illegal character found, content is {} and illegal char is {}", content, leftover);
                return OpResult.createSucResult(new RiskWordResult(false, content, new ArrayList<>(Sets.newHashSet(leftover.split("\\b{g}")))));
            }
        }

        return OpResult.createSucResult(new RiskWordResult(true, "", Collections.emptyList()));
    }

}
