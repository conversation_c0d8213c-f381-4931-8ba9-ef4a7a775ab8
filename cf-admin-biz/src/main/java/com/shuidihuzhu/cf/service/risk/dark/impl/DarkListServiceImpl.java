package com.shuidihuzhu.cf.service.risk.dark.impl;

import com.shuidihuzhu.cf.client.feign.UgcVerifyFeignClient;
import com.shuidihuzhu.cf.service.risk.dark.DarkListService;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistVerifyTypeEnum
 * com.shuidihuzhu.cf.risk.model.enums.risk.LimitActionEnum
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DarkListServiceImpl implements DarkListService {
//
//    @Autowired
//    private BlacklistVerifyClient blacklistVerifyClient;

    @Autowired
    private UgcVerifyFeignClient ugcVerifyFeignClient;

    @Override
    public boolean checkUGCPassed(long userId, boolean isVerification) {
        log.info("checkUGCPassed in userId:{}", userId);
        Response<Boolean> booleanResponse;
        if (isVerification) {
            booleanResponse = ugcVerifyFeignClient.checkVerifyDark(userId);
        }else {
            booleanResponse = ugcVerifyFeignClient.checkUgcDark(userId);
        }
        if (booleanResponse == null || booleanResponse.notOk()) {
            return true;
        }
        return booleanResponse.getData();
    }
//
//    private DarkListResult commonVerify(long userId, List<BlacklistVerifyDto> paramList, LimitActionEnum targetCheckAction) {
//        paramList = paramList.stream().filter(Objects::nonNull).collect(Collectors.toList());
//        DarkListResult darkListResult = new DarkListResult();
//        darkListResult.setPassed(true);
//        long targetCheckActionId = targetCheckAction.getId();
//
//        Response<List<BlacklistVerifyDto>> verifyResponse = blacklistVerifyClient.verify(paramList);
//        darkListResult.setResourceResponse(verifyResponse);
//        log.info("verify service {}", verifyResponse);
//        if (verifyResponse == null || verifyResponse.notOk()) {
//            log.warn("verify service fail {}", verifyResponse);
//            return darkListResult;
//        }
//        List<BlacklistVerifyDto> resultList = verifyResponse.getData();
//        if (CollectionUtils.isEmpty(resultList)) {
//            log.error("verify service error resultList {}", verifyResponse);
//            return darkListResult;
//        }
//        List<BlacklistVerifyDto> hitList = resultList.stream()
//                .filter(BlacklistVerifyDto::isHit)
//                .filter(v -> v.getLimitActionIds().contains(targetCheckActionId))
//                .collect(Collectors.toList());
//        darkListResult.setHitList(hitList);
//
//        boolean passed = CollectionUtils.isEmpty(hitList);
//        log.info("verify result userId:{}, passed:{}, verifyResponse:{}", userId, passed, verifyResponse);
//        if (passed) {
//            darkListResult.setPassed(true);
//            return darkListResult;
//        }
//        darkListResult.setPassed(false);
//
//        return darkListResult;
//    }
//
//    @Nullable
//    private BlacklistVerifyDto createVerifyParam(BlacklistVerifyTypeEnum verifyTypeEnum, Object verifyData) {
//        if (verifyData == null) {
//            return null;
//        }
//        BlacklistVerifyDto p = new BlacklistVerifyDto();
//        p.setVerifyData(String.valueOf(verifyData));
//        p.setVerifyType(verifyTypeEnum.getCode());
//        return p;
//    }
}
