package com.shuidihuzhu.cf.service.record;

import com.shuidihuzhu.cf.dao.record.CfImageMarkPsRecordDao;
import com.shuidihuzhu.cf.model.record.CfImageMarkPsRecord;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2022/12/26 14:39
 * @Description:
 */
@Service
public class CfImageMarkPsRecordService {

    @Resource
    private CfImageMarkPsRecordDao cfImageMarkPsRecordDao;

    public int insertCfImageMarkPsRecord(CfImageMarkPsRecord cfImageMarkPsRecord) {
        return cfImageMarkPsRecordDao.insert(cfImageMarkPsRecord);
    }

    public CfImageMarkPsRecord getByAttachmentId(int attachmentId) {
        return cfImageMarkPsRecordDao.getByAttachmentId(attachmentId);
    }

    public List<CfImageMarkPsRecord> getByCaseId(int caseId) {
        return cfImageMarkPsRecordDao.getByCaseId(caseId);
    }

    public int updateRecognitionPsById(long id, int recognitionPs, long operatorId) {
        return cfImageMarkPsRecordDao.updateById(id, recognitionPs, operatorId);
    }
}
