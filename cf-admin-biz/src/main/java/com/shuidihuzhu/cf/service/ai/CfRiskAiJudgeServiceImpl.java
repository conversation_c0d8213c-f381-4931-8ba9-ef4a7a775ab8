package com.shuidihuzhu.cf.service.ai;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.feign.CfMaterialReadClient;
import com.shuidihuzhu.cf.client.material.model.CfPropertyInsuranceInfoModel;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.dao.ai.AiJudgeRiskRecordDao;
import com.shuidihuzhu.cf.dao.ai.AiJudgeRiskStrategyConfigDao;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cf.enums.ai.AiModelEnum;
import com.shuidihuzhu.cf.enums.ai.AiRiskBizEnum;
import com.shuidihuzhu.cf.model.ai.AiRiskConfigResult;
import com.shuidihuzhu.cf.model.ai.AiRiskJudgeRecord;
import com.shuidihuzhu.cf.model.ai.AiRiskJudgeResult;
import com.shuidihuzhu.cf.model.ai.AiRiskStrategyConfig;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.cf.vo.approve.CreditInfoNewVO;
import com.shuidihuzhu.client.cf.admin.model.AIGenerateParam;
import com.shuidihuzhu.client.cf.admin.model.AiGenerateResult;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import com.shuidihuzhu.client.cf.growthtool.client.CfBdCaseInfoFeignClient;
import com.shuidihuzhu.client.cf.growthtool.client.CfGrowthtoolOrgFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.OrgInfoModel;
import com.shuidihuzhu.client.model.ChatChunk;
import com.shuidihuzhu.client.model.ChatCompletionChunk;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2025/5/20 16:39
 */
@Slf4j
@Service
public class CfRiskAiJudgeServiceImpl implements CfRiskAiJudgeService {

    @Resource
    private CfMaterialReadClient cfMaterialReadClient;

    @Resource
    private CfGrowthtoolOrgFeignClient cfGrowthtoolOrgFeignClient;

    @Resource
    private CfBdCaseInfoFeignClient cfBdCaseInfoFeignClient;

    @Resource
    private AiJudgeRiskStrategyConfigDao aiJudgeRiskStrategyConfigDao;

    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Resource
    private DeepSeekR1Model deepSeekR1Model;

    @Resource
    private TongYiAiModel tongYiAiModel;

    @Resource
    private WenXinAiModel wenXinAiModel;

    @Resource
    private AiJudgeRiskRecordDao aiJudgeRiskRecordDao;

    @Autowired
    private ApplicationService applicationService;

    private final static Map<Integer, BiFunction<AIGenerateParam, String, String>> aiModelStrategy = new HashMap<>();

    @PostConstruct
    public void initAiModelStrategy() {
        aiModelStrategy.put(AiModelEnum.WEN_XIN.getCode(), (param, prompt) -> wenXinAiModel.callModelApi(param, prompt));
        aiModelStrategy.put(AiModelEnum.TONG_YI.getCode(), (param, prompt) -> tongYiAiModel.callModelApi(param, prompt));
        aiModelStrategy.put(AiModelEnum.DEEPSEEK_R1.getCode(), (param, prompt) -> deepSeekR1Model.callModelApi(param, prompt));
    }

    @Override
    /**
     * AI风险评估服务实现类
     */
    public void aiJudgeRisk(CrowdfundingInfo crowdfundingInfo, String judgeContent, AiRiskBizEnum bizEnum) {
        log.info("开始AI风险评估 infoUuid: {}", crowdfundingInfo.getInfoId());

        if (applicationService.isDevelopment()) {
            return;
        }

        // 获取风险策略配置
        List<AiRiskStrategyConfig> riskStrategies = fetchEnabledRiskStrategies(bizEnum);
        if (riskStrategies.isEmpty()) {
            return;
        }

        // 构建风险评估记录
        AiRiskJudgeRecord riskRecord = buildRiskJudgeRecord(crowdfundingInfo, judgeContent, bizEnum);

        // 执行风险评估
        executeRiskAssessment(riskStrategies, riskRecord);

        log.info("AI风险评估完成，infoUuid: {}", crowdfundingInfo.getInfoId());
    }

    @Override
    public AiRiskConfigResult actionAiRisk(String actionType, String sceneType, String riskFactor, String judgePrompt) {

        log.info("CfRiskAiJudgeServiceImpl actionAiRisk {} {} {} {}", actionType, sceneType, riskFactor, judgePrompt);
        if ("查询".equals(actionType)) {
            AiRiskStrategyConfig aiRiskStrategyConfig = aiJudgeRiskStrategyConfigDao.selectConfigByScene(sceneType);
            if (Objects.isNull(aiRiskStrategyConfig)) {
                return null;
            }
            return AiRiskConfigResult.builder()
                    .sceneType(aiRiskStrategyConfig.getSceneType())
                    .judgePrompt(aiRiskStrategyConfig.getJudgePrompt())
                    .riskFactor(aiRiskStrategyConfig.getRiskFactor())
                    .build();
        } else if ("更新".equals(actionType)) {
            if (StringUtils.isEmpty(judgePrompt) || StringUtils.isEmpty(riskFactor)) {
                return null;
            }
            aiJudgeRiskStrategyConfigDao.updateBySceneType(sceneType, judgePrompt, riskFactor);
        }

        return null;
    }

    /**
     * 获取众筹信息
     */
    private CrowdfundingInfo getCrowdfundingInfo(String infoUuid) {
        CrowdfundingInfo info = adminCrowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (info == null) {
            log.info("众筹信息不存在，infoUuid: {}", infoUuid);
            return null;
        }
        return info;
    }

    /**
     * 获取所有启用的风险策略配置
     */
    private List<AiRiskStrategyConfig> fetchEnabledRiskStrategies(AiRiskBizEnum bizEnum) {
        List<AiRiskStrategyConfig> strategies = aiJudgeRiskStrategyConfigDao.selectAllEnableConfig(bizEnum.getCode());
        if (CollectionUtils.isEmpty(strategies)) {
            log.info("未配置有效的AI风险评估策略");
            return Collections.emptyList();
        }
        return strategies;
    }

    /**
     * 构建风险评估记录
     */
    private AiRiskJudgeRecord buildRiskJudgeRecord(CrowdfundingInfo crowdfundingInfo, String judgeContent, AiRiskBizEnum bizEnum) {
        String creditContent = parseCredit(crowdfundingInfo);

        return AiRiskJudgeRecord.builder()
                .caseId(crowdfundingInfo.getId())
                .infoId(crowdfundingInfo.getInfoId())
                .judgeType(bizEnum.getMsg())
                .judgeContent(judgeContent)
                .creditContent(creditContent)
                .bdOrg("")
                .build();
    }

    /**
     * 执行风险评估
     */
    private void executeRiskAssessment(List<AiRiskStrategyConfig> strategies, AiRiskJudgeRecord riskRecord) {
        for (AiRiskStrategyConfig strategy : strategies) {
            try {

                if (strategy.getSceneType().equals("资产瞒报")) {
                    judgeByAiAssetUnderreporting(strategy, riskRecord);
                } else {
                    judgeByAiRisk(strategy, riskRecord);
                }
            } catch (Exception e) {
                log.error("风险策略评估异常，策略场景: {}, 异常: {}", strategy.getSceneType(), e.getMessage(), e);
            }
        }
    }

    private void judgeByAiAssetUnderreporting(AiRiskStrategyConfig strategy, AiRiskJudgeRecord riskRecord) {
        log.info("执行风险策略评估，策略场景: {}", strategy.getSceneType());

        Integer modelType = strategy.getModelType();
        if (Objects.isNull(modelType)) {
            log.info("未配置模型，策略场景: {}", strategy.getSceneType());
            return;
        }

        BiFunction<AIGenerateParam, String, String> function = aiModelStrategy.get(modelType);
        if (Objects.isNull(function)) {
            log.info("不支持的模型，策略场景: {}", strategy.getSceneType());
            return;
        }

        String modelResult = function.apply(null, constructPrompt(strategy.getJudgePrompt(), riskRecord.getCreditContent()));

        // 解析大模型结果
        AiRiskJudgeResult aiRiskJudgeResult = null;
        try {
            // 处理可能带有 ```json 标记的情况
            String cleanJson = modelResult;

            // 检查并移除 ```json 和 ``` 标记
            if (cleanJson.contains("```json")) {
                cleanJson = cleanJson.substring(cleanJson.indexOf("```json") + 7);
                if (cleanJson.contains("```")) {
                    cleanJson = cleanJson.substring(0, cleanJson.indexOf("```"));
                }
            }

            // 去除前后空白
            cleanJson = cleanJson.trim();

            // 解析 JSON
            JSONObject jsonObject = JSONObject.parseObject(cleanJson);
            aiRiskJudgeResult = new AiRiskJudgeResult();

            // 安全地获取字段值
            if (Objects.nonNull(jsonObject) && jsonObject.containsKey("isHitRisk")) {
                aiRiskJudgeResult.setIsHitRisk(jsonObject.getString("isHitRisk"));
            }
            if (Objects.nonNull(jsonObject) && jsonObject.containsKey("judgeReason")) {
                aiRiskJudgeResult.setJudgeReason(jsonObject.getString("judgeReason"));
            }
        } catch (Exception e) {
            log.error("解析 AI 风险判断结果失败: {}", modelResult, e);
        }

        riskRecord.setHitRiskFactor("");
        riskRecord.setSceneType(strategy.getSceneType());
        riskRecord.setModelType(strategy.getModelType());
        riskRecord.setJudgeContent(riskRecord.getCreditContent());
        if (Objects.nonNull(aiRiskJudgeResult)) {
            riskRecord.setHitRiskResult(StringUtils.isEmpty(aiRiskJudgeResult.getIsHitRisk()) ? "" : aiRiskJudgeResult.getIsHitRisk());
            riskRecord.setJudgeReason(StringUtils.isEmpty(aiRiskJudgeResult.getJudgeReason()) ? "" : aiRiskJudgeResult.getJudgeReason());
        } else {
            riskRecord.setHitRiskResult("未识别");
            riskRecord.setJudgeReason("未识别");
        }

        // 命中风险则发消息
        if (!"未识别".equals(riskRecord.getHitRiskResult()) && !"无".equals(riskRecord.getHitRiskResult())) {

            // 保存风险检测结果
            aiJudgeRiskRecordDao.insert(riskRecord);

            AlarmBotService.sentMarkDown("57c52f5d-875e-421b-8e92-069a5e1b3596",
                    new StringBuilder().append("**风险命中播报**\n")
                            .append("**案例id**: ").append(riskRecord.getCaseId()).append("\n")
                            .append("**案例链接**: ").append("[案例链接](").append("https://www.shuidichou.com/cf/contribute/").append(riskRecord.getInfoId()).append(")\n")
                            .append("**触发时间**: ").append(DateUtil.getCurrentDateStr()).append("\n")
                            .append("**错误标签**: ").append(riskRecord.getHitRiskResult()).append("\n")
                            .append("**模型判断依据**: ").append(riskRecord.getJudgeReason()).append("\n")
                            .append("**案例内容**: \n").append(riskRecord.getJudgeContent()).append("\n")
                            .toString()
            );
        }

    }

    /**
     * 根据AI风险策略进行评估
     */
    private void judgeByAiRisk(AiRiskStrategyConfig strategy, AiRiskJudgeRecord riskRecord) {
        log.info("执行风险策略评估，策略场景: {}", strategy.getSceneType());

        // 判断是否命中风险因子
        String hitRiskFactor = judgeHitRiskFactor(strategy.getRiskFactor(), riskRecord.getJudgeContent());
        if (StringUtils.isEmpty(hitRiskFactor)) {
            return;
        }
        log.info("命中风险因子，策略场景: {}, 命中因素: {}, 原内容: {}",
                strategy.getSceneType(), hitRiskFactor, riskRecord.getJudgeContent());

        if (excludeStrategy(strategy.getSceneType(), hitRiskFactor, strategy.getRiskFactor(), riskRecord.getJudgeContent())) {
            log.info("排除命中风险因子，策略场景: {}, 命中因素: {}, 原内容: {}",
                    strategy.getSceneType(), hitRiskFactor, riskRecord.getJudgeContent());
            return;
        }

        // 命中风险因子后，大模型判断是否有风险
        Integer modelType = strategy.getModelType();
        if (Objects.isNull(modelType)) {
            log.info("未配置模型，策略场景: {}", strategy.getSceneType());
            return;
        }

        BiFunction<AIGenerateParam, String, String> function = aiModelStrategy.get(modelType);
        if (Objects.isNull(function)) {
            log.info("不支持的模型，策略场景: {}", strategy.getSceneType());
            return;
        }

        String modelResult = function.apply(null, constructPrompt(strategy.getJudgePrompt(), riskRecord.getJudgeContent()));

        // 解析大模型结果
        AiRiskJudgeResult aiRiskJudgeResult = null;
        try {
            // 处理可能带有 ```json 标记的情况
            String cleanJson = modelResult;

            // 检查并移除 ```json 和 ``` 标记
            if (cleanJson.contains("```json")) {
                cleanJson = cleanJson.substring(cleanJson.indexOf("```json") + 7);
                if (cleanJson.contains("```")) {
                    cleanJson = cleanJson.substring(0, cleanJson.indexOf("```"));
                }
            }

            // 去除前后空白
            cleanJson = cleanJson.trim();

            // 解析 JSON
            JSONObject jsonObject = JSONObject.parseObject(cleanJson);
            aiRiskJudgeResult = new AiRiskJudgeResult();

            // 安全地获取字段值
            if (Objects.nonNull(jsonObject) && jsonObject.containsKey("isHitRisk")) {
                aiRiskJudgeResult.setIsHitRisk(jsonObject.getString("isHitRisk"));
            }
            if (Objects.nonNull(jsonObject) && jsonObject.containsKey("judgeReason")) {
                aiRiskJudgeResult.setJudgeReason(jsonObject.getString("judgeReason"));
            }
        } catch (Exception e) {
            log.warn("解析 AI 风险判断结果失败: {}", modelResult, e);
        }

        riskRecord.setHitRiskFactor(hitRiskFactor);
        riskRecord.setSceneType(strategy.getSceneType());
        riskRecord.setModelType(strategy.getModelType());
        if (Objects.nonNull(aiRiskJudgeResult)) {
            riskRecord.setHitRiskResult(StringUtils.isEmpty(aiRiskJudgeResult.getIsHitRisk()) ? "" : aiRiskJudgeResult.getIsHitRisk());
            riskRecord.setJudgeReason(StringUtils.isEmpty(aiRiskJudgeResult.getJudgeReason()) ? "" : aiRiskJudgeResult.getJudgeReason());
        } else {
            riskRecord.setHitRiskResult("未识别");
            riskRecord.setJudgeReason("未识别");
        }

        // 保存风险检测结果
        riskRecord.setBdOrg("");
        aiJudgeRiskRecordDao.insert(riskRecord);

        // 命中风险则发消息
        if ("有风险".equals(riskRecord.getHitRiskResult())) {
            AlarmBotService.sentMarkDown("80678c59-ae11-4761-94a7-ceceac6c2e1f",
                    new StringBuilder().append("**风险命中播报**\n")
                            .append("**案例id**: ").append(riskRecord.getCaseId()).append("\n")
                            .append("**案例链接**: ").append("[案例链接](").append("https://www.shuidichou.com/cf/contribute/").append(riskRecord.getInfoId()).append(")\n")
                            .append("**触发时间**: ").append(DateUtil.getCurrentDateStr()).append("\n")
                            .append("**触发的风险因子**: ").append(riskRecord.getHitRiskFactor()).append("\n")
                            .append("**模型判断依据**: ").append(riskRecord.getJudgeReason()).append("\n")
                            .append("**").append(riskRecord.getJudgeType()).append("**\n").append(highlightRiskContent(riskRecord.getJudgeContent(), riskRecord.getHitRiskFactor())).append("\n")
                            .toString()
            );
        }

    }

    private Boolean excludeStrategy(String sceneType, String hitRiskFactor, String riskFactors, String content) {

        if ("天花".equals(hitRiskFactor)) {
            Pattern PATTERN = Pattern.compile("每天花|一天花|天花板|几天花|天花光");
            return PATTERN.matcher(content).find();
        }

        return false;
    }

    /**
     * 将judgeContent中匹配riskContent的部分用**包围
     * @param judgeContent 待处理的判断内容
     * @param riskContent 风险关键词，用逗号分隔
     * @return 处理后的字符串
     */
    public static String highlightRiskContent(String judgeContent, String riskContent) {
        // 参数校验
        if (judgeContent == null || judgeContent.isEmpty()) {
            return judgeContent;
        }
        if (riskContent == null || riskContent.isEmpty()) {
            return judgeContent;
        }

        // 按逗号分割riskContent
        String[] riskWords = riskContent.split(",");

        // 去除空白字符并过滤空字符串
        List<String> riskWordList = Arrays.stream(riskWords)
                .map(String::trim)
                .filter(word -> !word.isEmpty())
                .collect(Collectors.toList());

        String result = judgeContent;

        // 遍历每个风险词汇，进行替换
        for (String riskWord : riskWordList) {
            // 转义特殊字符
            String escapedWord = escapeRegex(riskWord);
            // 使用负向前瞻和负向后顾，避免重复包围已经被**包围的内容
            String regex = "(?<!\\*\\*)" + escapedWord + "(?!\\*\\*)";
            result = result.replaceAll(regex, "**" + riskWord + "**");
        }

        return result;
    }

    /**
     * 转义正则表达式特殊字符
     * @param input 输入字符串
     * @return 转义后的字符串
     */
    private static String escapeRegex(String input) {
        return input.replaceAll("([\\\\\\[\\]{}()*+?.^$|])", "\\\\$1");
    }

    // 测试方法
    public static void main(String[] args) {
        // 测试用例1
        String judgeContent1 = "这个产品存在安全风险，可能导致用户信息泄露和系统崩溃";
        String riskContent1 = "安全风险,信息泄露,系统崩溃";
        System.out.println("原文: " + judgeContent1);
        System.out.println("风险词: " + riskContent1);
        System.out.println("结果: " + highlightRiskContent(judgeContent1, riskContent1));
        System.out.println();

        // 测试用例2
        String judgeContent2 = "该方案涉及数据安全、隐私保护和合规性问题";
        String riskContent2 = "数据安全, 隐私保护 , 合规性";
        System.out.println("原文: " + judgeContent2);
        System.out.println("风险词: " + riskContent2);
        System.out.println("结果: " + highlightRiskContent(judgeContent2, riskContent2));
        System.out.println();

        // 测试用例3 - 边界情况
        String judgeContent3 = "正常的业务流程";
        String riskContent3 = "风险,问题,异常";
        System.out.println("原文: " + judgeContent3);
        System.out.println("风险词: " + riskContent3);
        System.out.println("结果: " + highlightRiskContent(judgeContent3, riskContent3));
    }

    private String constructPrompt(String basePrompt, String content) {
        return new StringBuilder().append(basePrompt)
                .append("\n标题和文章及案例信息提供如下:\n")
                .append(content)
                .toString();
    }

    /**
     * 判断是否命中风险因素
     */
    private String judgeHitRiskFactor(String riskFactors, String content) {
        if (StringUtils.isEmpty(riskFactors) || StringUtils.isEmpty(content)) {
            return "";
        }

        Set<String> riskFactorList = new HashSet<>(Splitter.on(',').trimResults().omitEmptyStrings().splitToList(riskFactors));
        List<String> hitFactors = riskFactorList.stream()
                .filter(content::contains)
                .collect(Collectors.toList());

        return String.join(",", hitFactors);
    }


    /**
     * 解析增信信息
     */
    private String parseCredit(CrowdfundingInfo crowdfundingInfo) {

        RpcResult<CfPropertyInsuranceInfoModel> result =
                cfMaterialReadClient.selectCfPropertyInsuranceInfo(crowdfundingInfo.getId());
        CfPropertyInsuranceInfoModel cfPropertyInsuranceInfoModel = Optional.ofNullable(result)
                .filter(RpcResult::isSuccess)
                .map(RpcResult::getData)
                .orElse(null);
        if (Objects.isNull(cfPropertyInsuranceInfoModel)) {
            return "";
        }

        StringBuilder creditContent = new StringBuilder()
                .append("标题：").append(StringUtils.defaultString(crowdfundingInfo.getTitle())).append("\n")
                .append("文章：").append(StringUtils.defaultString(crowdfundingInfo.getContent())).append("\n");

        // 商品房信息
        CfPropertyInsuranceInfoModel.HousePropertyInfo houseProperty = cfPropertyInsuranceInfoModel.getHouseProperty();
        if (Objects.nonNull(houseProperty)) {
            if (Objects.nonNull(houseProperty.getTotalCount())) {
                String houseCount = PreposeMaterialModel.HouseNumEnum.valueOfCode(houseProperty.getTotalCount()).getDesc();
                String houseTotalValue = buildValue(houseProperty.getPureValueUserDefined(),
                        houseProperty.getPureValueRangeType(), new CreditInfoNewVO.HouseFunction());
                creditContent.append("商品房房产数量：").append(houseCount).append("\n")
                        .append("商品房房产价值：").append(houseTotalValue).append("\n");
            }
            if (Objects.nonNull(houseProperty.getSaleCount())) {
                String houseSaleCount = PreposeMaterialModel.HouseNumEnum.valueOfCode(houseProperty.getSaleCount()).getDesc();
                String houseSaleValue = buildValue(houseProperty.getSaleValueUserDefined(),
                        houseProperty.getSaleValueRangeType(), new CreditInfoNewVO.HouseFunction());
                creditContent.append("商品房变卖房产数量：").append(houseSaleCount).append("\n")
                        .append("商品房变卖房产价值：").append(houseSaleValue).append("\n");
            }
        }

        // 自建房信息
        CfPropertyInsuranceInfoModel.HousePropertyInfo selfBuiltHouse = cfPropertyInsuranceInfoModel.getSelfBuiltHouse();
        if (Objects.nonNull(selfBuiltHouse)) {
            if (Objects.nonNull(selfBuiltHouse.getTotalCount())) {
                String houseCount = PreposeMaterialModel.HouseNumEnum.valueOfCode(selfBuiltHouse.getTotalCount()).getDesc();
                String houseTotalValue = buildValue(selfBuiltHouse.getTotalValueUserDefined(),
                        selfBuiltHouse.getTotalValueRangeType(), new CreditInfoNewVO.HouseFunction());
                creditContent.append("自建房房产数量：").append(houseCount).append("\n")
                        .append("自建房房产价值：").append(houseTotalValue).append("\n");
            }
            if (Objects.nonNull(selfBuiltHouse.getSaleCount())) {
                String houseSaleCount = PreposeMaterialModel.HouseNumEnum.valueOfCode(selfBuiltHouse.getSaleCount()).getDesc();
                String houseSaleValue = buildValue(selfBuiltHouse.getSaleValueUserDefined(),
                        selfBuiltHouse.getSaleValueRangeType(), new CreditInfoNewVO.HouseFunction());
                creditContent.append("自建房变卖房产数量：").append(houseSaleCount).append("\n")
                        .append("自建房变卖房产价值：").append(houseSaleValue).append("\n");
            }
        }

        return creditContent.toString();
    }

    /**
     * 解析众筹内容
     */
    private String parseContent(CrowdfundingInfo info) {
        return new StringBuilder()
                .append("标题：").append(StringUtils.defaultString(info.getTitle())).append("\n")
                .append("文章：").append(StringUtils.defaultString(info.getContent())).append("\n")
                .toString();
    }

    private static String buildValue(Integer userDefined, Integer rangeType, CreditInfoNewVO.ParseFunction parseFunction){
        if (userDefined != null && userDefined >= 0) {
            BigDecimal bg = new BigDecimal(userDefined);
            double f = bg.divide(BigDecimal.valueOf(10000), 2,  RoundingMode.HALF_DOWN).doubleValue();
            return f + "万元";
        }
        if (rangeType == null) {
            return "";
        }
        if (rangeType <= 0) {
            log.error("rangeType 不合法:{}", rangeType);
            return "";
        }
        CfPropertyInsuranceInfoModel.IValueRange range = parseFunction.parse(rangeType);
        return range.getDesc();
    }

    /**
     * 获取发起BD组织路径
     */
    private String getRaiseBdOrg(String infoUuid) {
        // 获取BD案例信息
        CfBdCaseInfoDo bdCaseInfo = getBdCaseInfo(infoUuid);
        if (bdCaseInfo == null) {
            return "";
        }

        // 获取组织信息
        OrgInfoModel orgInfo = getOrgInfo(bdCaseInfo.getOrgId());
        if (orgInfo == null) {
            return "";
        }

        return orgInfo.getOrgName();
    }

    /**
     * 获取BD案例信息
     */
    private CfBdCaseInfoDo getBdCaseInfo(String infoUuid) {
        Response<CfBdCaseInfoDo> response = cfBdCaseInfoFeignClient.getBdCaseInfoByInfoUuid(infoUuid);
        CfBdCaseInfoDo bdCaseInfo = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);

        if (bdCaseInfo == null) {
            log.info("未找到BD案例信息，infoUuid: {}", infoUuid);
        }

        return bdCaseInfo;
    }

    /**
     * 获取组织信息
     */
    private OrgInfoModel getOrgInfo(Integer orgId) {
        Response<OrgInfoModel> response = cfGrowthtoolOrgFeignClient.getByOrgId(orgId);
        OrgInfoModel orgInfo = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);

        if (orgInfo == null) {
            log.info("未找到组织信息，orgId: {}", orgId);
        }

        return orgInfo;
    }


}
