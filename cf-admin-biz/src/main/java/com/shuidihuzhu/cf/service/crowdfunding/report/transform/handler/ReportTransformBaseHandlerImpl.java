package com.shuidihuzhu.cf.service.crowdfunding.report.transform.handler;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowStaffStatus;
import com.shuidihuzhu.cf.service.crowdfunding.report.CfReportService;
import com.shuidihuzhu.cf.service.crowdfunding.report.transform.ReportTransformParam;
import com.shuidihuzhu.client.cf.workorder.CfReportWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderStaffClient;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 移植逻辑 from {@link CfReportService#reportWorkOrderUpgrade(long, int, int, int, String, String)}
 * <AUTHOR>
 */
@Slf4j
public abstract class ReportTransformBaseHandlerImpl implements IReportTransformHandler {

    @Autowired
    private CfWorkOrderStaffClient cfWorkOrderStaffClient;

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @Autowired
    private CfReportService cfReportService;

    @Autowired
    private CfReportWorkOrderClient reportWorkOrderClient;

    @Override
    public Response<Void> handle(ReportTransformParam p) {
        int operatorId = p.getOperatorId();
        int caseId = p.getCaseId();
        List<String> orderTypeList = Lists.newArrayList();
        WorkOrderType orderType = getOrderType();
        int orderTypeInt = orderType.getType();
        orderTypeList.add(Integer.toString(orderTypeInt));
        String orderTypeStr = String.join(",", orderTypeList);

        //校验操作人员是否有操作工单权限
        Response<List<StaffStatus>> staffTypeResponse = cfWorkOrderStaffClient.getStaffType(orderTypeStr, operatorId);
        if (staffTypeResponse.ok() && CollectionUtils.isNotEmpty(staffTypeResponse.getData())) {
            List<StaffStatus> staffStatuses = staffTypeResponse.getData();
            StaffStatus staffStatus = staffStatuses.get(0);
            if (staffStatus.getStaffStatus() == WorkFlowStaffStatus.StaffStatusEnum.online.getCode()) {
                return makePermissionError();
            }
        }
        List<Integer> orderTypes = Lists.newArrayList(getOrderType().getType());
        Response<List<WorkOrderVO>> workOrderResponse = cfWorkOrderClient.listByCaseIdAndTypeAndResult(caseId,
                orderTypes, List.of(HandleResultEnum.undoing.getType(), HandleResultEnum.doing.getType(),
                        HandleResultEnum.reach_agree.getType()));
        if (workOrderResponse.ok() && CollectionUtils.isNotEmpty(workOrderResponse.getData())) {
            return makeHasNoFinishError();
        }

        handleTransform(p);
        return NewResponseUtil.makeSuccess(null);
    }

    private void handleTransform(ReportTransformParam p) {
        int caseId = p.getCaseId();
        long workOrderId = p.getWorkOrderId();
        WorkOrderType orderType = getOrderType();
        int orderTypeInt = orderType.getType();
        // 执行转换
//        cfReportService.reportWorkOrderUpgrade(p.get, caseId, operatorId, orderType.getType(), reason,comment);
        int handleResult = getHandleResult().getType();
        String operComment = getOperateCommentExt().getDesc();
        String comment = getComment();
        //变更当前工单的状态
        ReportHandleOrderParam reportHandleOrderParam = new ReportHandleOrderParam();
        reportHandleOrderParam.setCaseId(caseId);
        reportHandleOrderParam.setWorkOrderId(workOrderId);
        reportHandleOrderParam.setHandleResult(handleResult);
        reportHandleOrderParam.setOperComment(operComment);
        reportHandleOrderParam.setUserId(p.getOperatorId());
        reportHandleOrderParam.setOrderType(orderTypeInt);
        Response response = reportWorkOrderClient.handleReport(reportHandleOrderParam);
        log.info("reportWorkOrderUpgrade handlereport response:{}", JSON.toJSONString(response));

        if (response.ok()) {
            //查询工单扩展信息
            Response<List<WorkOrderExt>> workOrderExtResponse = cfWorkOrderClient.listExtInfos(List.of(workOrderId),
                    OrderExtName.reportId.getName());
            log.info("reportWorkOrderUpgrade listExtInfos response:{}", JSON.toJSONString(workOrderExtResponse));
            if (workOrderExtResponse.ok() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(workOrderExtResponse.getData())) {
                //取最后一条关联的举报id
                List<WorkOrderExt> workOrderExtList = workOrderExtResponse.getData();
                WorkOrderExt workOrderExt = workOrderExtList.get(workOrderExtList.size() - 1);
                //生成工单
                ReportWorkOrder reportWorkOrder = createReport(caseId, workOrderExt, orderTypeInt, comment, workOrderId);
                Response<Long> createRes = reportWorkOrderClient.createReport(reportWorkOrder);
                log.info("reportWorkOrderUpgrade createRes caseId:{},reportId:{},result:{}", caseId,
                        workOrderExt.getExtValue(), createRes.getCode());
                cfReportService.sendCheckHandleMq(createRes.getData());
            }
        }
        //添加操作备注
        saveRemark(p);
    }

    public ReportWorkOrder createReport(int caseId, WorkOrderExt workOrderExt, int orderTypeInt, String comment, long workOrderId) {
        ReportWorkOrder workOrder = new ReportWorkOrder();
        workOrder.setCaseId(caseId);
        workOrder.setReportId(Integer.parseInt(workOrderExt.getExtValue()));
        workOrder.setOrderType(orderTypeInt);
        workOrder.setHandleResult(HandleResultEnum.undoing.getType());
        workOrder.setOperatorId(0L);
        workOrder.setDealOperatorId(0L);
        workOrder.setComment(comment);
        return workOrder;
    }

    protected abstract void saveRemark(ReportTransformParam p);

    protected abstract OrderExtName getOperateCommentExt();

    protected abstract String getComment();

    protected abstract HandleResultEnum getHandleResult();

    protected abstract Response<Void> makeHasNoFinishError();

    protected abstract Response<Void> makePermissionError();

    protected abstract WorkOrderType getOrderType();

}
