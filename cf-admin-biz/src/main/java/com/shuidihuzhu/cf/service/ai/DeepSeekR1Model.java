package com.shuidihuzhu.cf.service.ai;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.ai.alps.client.feign.AihubFeignClient;
import com.shuidihuzhu.ai.alps.client.feign.ChatGptOverseaClient;
import com.shuidihuzhu.ai.alps.client.model.aihub.DoubaoChatRequest;
import com.shuidihuzhu.ai.alps.client.model.aihub.QwenChatRequest;
import com.shuidihuzhu.ai.alps.client.model.aihub.StreamChatRequest;
import com.shuidihuzhu.ai.alps.client.model.completions.v2.ChatCompletionResponseV2;
import com.shuidihuzhu.ai.alps.client.model.completions.v2.ChoiceV2;
import com.shuidihuzhu.ai.alps.client.model.completions.v3.ChatCompletionRequestV3;
import com.shuidihuzhu.cf.model.ai.StreamChatGptRequest;
import com.shuidihuzhu.client.cf.admin.model.AIGenerateParam;
import com.shuidihuzhu.client.model.ChatChunk;
import com.shuidihuzhu.client.model.ChatCompletionChunk;
import com.shuidihuzhu.common.web.model.Response;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.client.reactive.ClientHttpConnector;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2025/2/19 17:08
 */
@Slf4j
@Service
public class DeepSeekR1Model extends BaseModel {

    @Resource
    private AihubFeignClient aihubFeignClient;
    @Resource
    private ChatGptOverseaClient chatGptOverseaClient;

    private static final String MODEL_NAME = "deepseek-r1-distill-qwen-32b";

    @Override
    public String callModelApi(AIGenerateParam aiGenerateParam, String prompt) {

        JSONObject messageObject = new JSONObject();
        messageObject.put("role", "user");
        messageObject.put("content", prompt);

        JSONObject requestBody = new JSONObject();
        requestBody.put("model", MODEL_NAME);
        requestBody.put("messages", Lists.newArrayList(messageObject));
        requestBody.put("stream", false);

        ChatCompletionRequestV3 chatCompletionRequestV3 = new ChatCompletionRequestV3();
        chatCompletionRequestV3.setAppCode("1004");
        chatCompletionRequestV3.setRequestBody(requestBody);

        Response<ChatCompletionResponseV2> response = chatGptOverseaClient.innerChatCompletionsV3(chatCompletionRequestV3);
        ChatCompletionResponseV2 chatCompletionResponseV2 = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);
        log.info("DeepSeekR1Model callModelApi chatCompletionRequestV3 {} chatCompletionResponseV2 {}", chatCompletionRequestV3, chatCompletionResponseV2);
        if (Objects.isNull(chatCompletionResponseV2)) {
            return "";
        }

        List<ChoiceV2> choiceV2List = chatCompletionResponseV2.getChoices();
        if (CollectionUtils.isEmpty(choiceV2List)) {
            return "";
        }

        ChoiceV2.ResMessage message = choiceV2List.get(0).getMessage();
        if (Objects.isNull(message)) {
            return "";
        }

        return message.getContent();
    }

    private WebClient webClient;
    @PostConstruct
    public void init() {
        ClientHttpConnector connector = new ReactorClientHttpConnector();
        this.webClient = WebClient.builder().clientConnector(connector).defaultHeader(HttpHeaders.CONTENT_TYPE, "application/json").build();
    }

    @Override
    protected Flux<ChatChunk<ChatCompletionChunk>> stream(String prompt) {

        StreamChatGptRequest.RequestBody requestBody = new StreamChatGptRequest.RequestBody();
        requestBody.setModel(MODEL_NAME);
        requestBody.setStream(true);
        requestBody.setMessages(Lists.newArrayList(
                StreamChatGptRequest.Message.builder()
                        .content(prompt)
                        .role("user")
                        .build()
        ));
        StreamChatGptRequest streamChatGptRequest = new StreamChatGptRequest();
        streamChatGptRequest.setAppCode("1004");
        streamChatGptRequest.setRequestBody(requestBody);

        return webClient.post()
                .uri("https://api.shuiditech.com/api/inner/ai/gpt/chat/completions/v3/stream")
                .bodyValue(streamChatGptRequest)
//                .header("Authorization", generateAuthorization())
                .retrieve()
                .bodyToFlux(String.class)
                .onErrorResume(WebClientResponseException.class, ex -> {
                    HttpStatus status = ex.getStatusCode();
                    String res = ex.getResponseBodyAsString();
                    log.error("streamGenerate [调用大模型失败] {} {}", status, res);
                    return Mono.error(new RuntimeException(res));
                }).mapNotNull(chunk -> {

                    if (chunk == null) {
                        log.error("streamGenerate [生成回答失败, chunk为空]");
                        return ChatChunk.error(1, "调用大模型失败1");
                    }

                    if ("[DONE]".equals(chunk)) {
                        ChatCompletionChunk chatStreamResult = new ChatCompletionChunk();
                        return ChatChunk.end(chatStreamResult);
                    }

                    JSONObject jsonObject = JSON.parseObject(chunk);
                    JSONArray choices = jsonObject.getJSONArray("choices");
                    if (Objects.isNull(choices)) {
                        log.error("streamGenerate [生成回答失败, choice为空]");
                        return ChatChunk.error(1, "调用大模型失败2");
                    }

                    JSONObject item = choices.getJSONObject(0);
                    String finishReason = Optional.ofNullable(item.getString("finish_reason")).orElse("");
                    if ("stop".equals(finishReason)) {
                        ChatCompletionChunk chatStreamResult = new ChatCompletionChunk();
                        return ChatChunk.success(chatStreamResult);
                    }

                    JSONObject delta = item.getJSONObject("delta");
                    if (Objects.isNull(delta)) {
                        log.error("streamGenerate [生成回答失败, delta为空]");
                        return ChatChunk.error(1, "调用大模型失败3");
                    }

                    ChatCompletionChunk.Choice choice = new ChatCompletionChunk.Choice();
                    choice.setEnd(false);
                    choice.setMessage(Optional.ofNullable(delta.getString("content")).orElse(""));
                    choice.setReasoningMessage(Optional.ofNullable(delta.getString("reasoning_content")).orElse(""));

                    ChatCompletionChunk parsedChunk = new ChatCompletionChunk();
                    parsedChunk.setData(choice);

                    return ChatChunk.success(parsedChunk);
                });
    }
}
