package com.shuidihuzhu.cf.service.workorder.initialAudit;

import com.google.common.collect.ImmutableList;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfVolunteerRiskRecordBiz;
import com.shuidihuzhu.cf.client.adminpure.model.initial.CfVolunteerRiskRecord;
import com.shuidihuzhu.cf.client.adminpure.model.initial.PatientToVolunteerConst;
import com.shuidihuzhu.cf.client.adminpure.model.initial.PatientToVolunteerParam;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.model.ChannelRefine;
import com.shuidihuzhu.client.cf.clewtrack.param.ChannelRefineDTO;
import com.shuidihuzhu.client.cf.growthtool.client.CfChannelFeignClient;
import com.shuidihuzhu.client.cf.growthtool.client.CfGrowthtoolVolunteerFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.growthtool.model.VolunteerLevelEnum;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @Author: wangpeng
 * @Date: 2022/1/11 11:32
 * @Description:
 */
@Service
@Slf4j
public class InitialAuditRiskService {

    @Resource
    private CfGrowthtoolVolunteerFeignClient cfGrowthtoolVolunteerFeignClient;

    @Resource
    private CfVolunteerRiskRecordBiz cfVolunteerRiskRecordBiz;

    @Resource
    private ShuidiCipher shuidiCipher;

    @Resource
    private OldShuidiCipher oldShuidiCipher;

    @Resource
    private IRiskDelegate firstApproveBiz;
    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;
    @Resource
    private CfChannelFeignClient cfChannelFeignClient;
    @Autowired
    private Environment environment;

    public int patientToVolunteer(PatientToVolunteerParam param) {
        int caseId = param.getCaseId();
        String idCard = "";
        String name = "";
        if (param.getUseType() == PatientToVolunteerConst.Source.APPROVE || param.getUseType() == PatientToVolunteerConst.Source.ISSUE) {
            idCard = shuidiCipher.decrypt(param.getPayeeIdCard());
            name = param.getPayeeName();
        }
        if (param.getUseType() == PatientToVolunteerConst.Source.INITIAL_AUDIT) {
            CfFirsApproveMaterial firsApproveMaterial = firstApproveBiz.getCfFirsApproveMaterialByInfoId(caseId);
            idCard = Objects.nonNull(firsApproveMaterial) ?
                    firsApproveMaterial.getPatientIdType() == UserIdentityType.identity.getCode() ?
                        Objects.requireNonNullElse(shuidiCipher.decrypt(firsApproveMaterial.getPatientCryptoIdcard()), "") :
                        firsApproveMaterial.getPatientBornCard()
                    : "";
            name = Objects.nonNull(firsApproveMaterial) ? firsApproveMaterial.getPatientRealName() : "";
        }
        if (StringUtils.isEmpty(idCard) || StringUtils.isEmpty(name)) {
            return 0;
        }
        Response<List<CrowdfundingVolunteer>> listResponse = cfGrowthtoolVolunteerFeignClient.getCrowdfundingVounteerByIdCardList(Collections.singletonList(idCard));
        String finalName = name;
        CrowdfundingVolunteer crowdfundingVolunteer = Optional.ofNullable(listResponse)
                .map(Response::getData)
                .orElse(Collections.emptyList())
                .stream()
                .filter(f -> StringUtils.equals(finalName, f.getVolunteerName()))
                .findAny()
                .orElse(null);
        if (Objects.isNull(crowdfundingVolunteer) || crowdfundingVolunteer.getLevel() == 0) {
            return 0;
        }

        CfVolunteerRiskRecord cfVolunteerRiskRecord = new CfVolunteerRiskRecord();
        cfVolunteerRiskRecord.setCaseId(param.getCaseId());
        cfVolunteerRiskRecord.setUseType(param.getUseType());
        cfVolunteerRiskRecord.setRiskType(crowdfundingVolunteer.getLevel());
        cfVolunteerRiskRecord.setPatientName(finalName);
        cfVolunteerRiskRecord.setPatientIdCard(oldShuidiCipher.aesEncrypt(idCard));
        cfVolunteerRiskRecord.setYearAmount(param.getYearAmount());
        cfVolunteerRiskRecordBiz.insert(cfVolunteerRiskRecord);
        sendAlarmBot(cfVolunteerRiskRecord);
        return crowdfundingVolunteer.getLevel();
    }

    /**
     * 报警code
     */
    private static final List<Integer> NEED_SEND_ALARM_CODE = ImmutableList.of(PatientToVolunteerConst.Source.ISSUE, PatientToVolunteerConst.Source.APPROVE);

    private void sendAlarmBot(CfVolunteerRiskRecord cfVolunteerRiskRecord) {
        if (!environment.acceptsProfiles(Profiles.of("production"))) {
            return;
        }
        // 材审/修改收款人命中报警
        if (Objects.isNull(cfVolunteerRiskRecord)) {
            return;
        }
        try {
            if (NEED_SEND_ALARM_CODE.contains(cfVolunteerRiskRecord.getUseType())) {
                int level = cfVolunteerRiskRecord.getRiskType();
                int caseId = cfVolunteerRiskRecord.getCaseId();
                int useType = cfVolunteerRiskRecord.getUseType();
                String type = PatientToVolunteerConst.Source.ISSUE == useType ? "提交下发修改收款人信息"
                        :  PatientToVolunteerConst.Source.APPROVE == useType ? "材审提交收款人信息" : "";
                CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
                if (Objects.isNull(crowdfundingInfo)) {
                    return;
                }
                ChannelRefineDTO refineDTO = new ChannelRefineDTO();
                refineDTO.setInfoId((long)caseId);
                refineDTO.setChannel(crowdfundingInfo.getChannel());
                refineDTO.setUserId(crowdfundingInfo.getUserId());
                Response<String> response = cfChannelFeignClient.getChannelByInfoIdWithUserIdAndOldChannel(refineDTO);
                log.info("channel refineDTO={} response={}",refineDTO,response);
                String channel = Optional.ofNullable(response)
                        .filter(Response::ok)
                        .map(Response::getData)
                        .orElse("");
                ChannelRefine.ChannelRefineResuleEnum channelRefineResuleEnum = ChannelRefine.ChannelRefineResuleEnum.parse(channel);
                String channelStr = Optional.ofNullable(channelRefineResuleEnum).map(ChannelRefine.ChannelRefineResuleEnum::getChannelDesc).orElse("");
                String patientName = cfVolunteerRiskRecord.getPatientName();
                VolunteerLevelEnum parse = VolunteerLevelEnum.parseByLevel(level);
                if (Objects.nonNull(parse)) {
                    String comment = "【案例收款人为" + parse.getDesc() +
                            "】，请关注关系证明材料 \n" +
                            "案例ID: " + caseId + "\n" +
                            "触发时间: " + DateUtil.formatDateTime(new Date()) + "\n" +
                            "触发时机:" + type + "\n" +
                            "引导用户发起渠道：" + channelStr + "\n" +
                            "风险类型：收款人为" + parse.getDesc() + "\n" +
                            "入参姓名: " + patientName;
                    AlarmBotService.sentText("dfa2caf4-fba6-42ec-8ec8-7fe81d928521", comment, null, null);
                }
            }
        } catch (Exception e) {
            log.error("发送收款人报警消息失败, caseId:{}", cfVolunteerRiskRecord.getCaseId(), e);
        }
    }
}
