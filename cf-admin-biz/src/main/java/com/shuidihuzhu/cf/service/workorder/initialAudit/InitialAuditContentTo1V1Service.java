package com.shuidihuzhu.cf.service.workorder.initialAudit;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.client.adminpure.model.initial.Initial1v1AssignInfo;
import com.shuidihuzhu.cf.client.adminpure.model.initial.InitialAuditNoWorkRejectCaseParam;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.feign.CfMaterialReadClient;
import com.shuidihuzhu.cf.client.material.model.materialField.MaterialExtKeyConst;
import com.shuidihuzhu.cf.constants.AdminUserIDConstants;
import com.shuidihuzhu.cf.delegate.SeaAccountDelegate;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cf.enums.wx.WxBotSendMsgTypeEnum;
import com.shuidihuzhu.cf.model.river.RiverUsageTypeEnum;
import com.shuidihuzhu.cf.param.InitialAuditCreateOrderParam;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditOperationItem;
import com.shuidihuzhu.cf.vo.approve.RiverHandleParamVO;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.ChuciWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Author: wangpeng
 * @Date: 2021/8/9 11:48
 * @Description:
 */
@Slf4j
@Service
@RefreshScope
public class InitialAuditContentTo1V1Service {

    private static final List<Integer> ORDER_TYPES = Lists.newArrayList(WorkOrderType.highriskshenhe.getType(), WorkOrderType.ai_photo.getType(), WorkOrderType.ai_content.getType(), WorkOrderType.ai_erci.getType());

    @Resource
    private CfMaterialReadClient cfMaterialReadClient;
    @Resource
    private CfWorkOrderClient workOrderClient;
    @Resource
    private InitialAuditCreateOrderService initialAuditCreateOrderService;
    @Resource
    private InitialAuditCreateOrder initialAuditCreateOrder;
    @Resource
    private InitialAuditOperateService initialAuditOperateService;
    @Autowired
    private ApproveRemarkOldService approveRemarkOldService;
    @Autowired
    private SeaAccountDelegate seaAccountDelegate;

    public void rejectCaseToContent1V1(InitialAuditNoWorkRejectCaseParam param) {
        log.info("InitialAuditContentTo1V1Service rejectCaseToContent1V1 is success {}", param);
        int caseId = param.getCaseId();
        List<Integer> rejectIdList = param.getRejectIdList();

        ChuciWorkOrder chuciWorkOrder = new ChuciWorkOrder();
        chuciWorkOrder.setCaseId(caseId);
        chuciWorkOrder.setOrderType(WorkOrderType.shenhe.getType());
        chuciWorkOrder.setOperatorId(AdminUserIDConstants.SYSTEM);
        chuciWorkOrder.setHandleResult(HandleResultEnum.later_doing.getType());
        InitialAuditCreateOrderParam orderParam = InitialAuditCreateOrderParam.builder()
                .caseId(caseId)
                .chuciWorkOrder(chuciWorkOrder)
                .condition(0)
                .build();
        Response<Long> longResponse = initialAuditCreateOrder.create(orderParam);
        if (Objects.isNull(longResponse) || longResponse.notOk() || Objects.isNull(longResponse.getData())) {
            log.info("InitialAuditContentTo1V1Service rejectCaseToContent1V1 create work order fail {}", param);
            return;
        }
        Long workOrderId = longResponse.getData();
        if (workOrderId <= 0) {
            log.info("InitialAuditContentTo1V1Service rejectCaseToContent1V1 create work order fail {}", param);
            return;
        }

        // 为了回显驳回信息
        RiverHandleParamVO diBaoParamVO = RiverHandleParamVO.builder()
                .caseId(caseId)
                .usageTypeEnum(RiverUsageTypeEnum.DI_BAO)
                .handleComment("")
                .handleType(RiverHandleParamVO.HandleType.PASS)
                .workOrderId(workOrderId)
                .orderType(WorkOrderType.ai_erci.getType())
                .rejectIds(new ArrayList<>())
                .operatorId(AdminUserIDConstants.SYSTEM)
                .callStatus(0)
                .rejectDetail(Maps.newHashMap())
                .build();

        String diBaoComment = Objects.equals(diBaoParamVO.getHandleType(), RiverHandleParamVO.HandleType.PASS) ? "低保信息:通过" : "";

        InitialAuditOperationItem.HandleCaseInfoParam handleCaseInfoParam = InitialAuditOperationItem
                .HandleCaseInfoParam
                .builder()
                .caseId(caseId)
                .handleType(InitialAuditOperationItem.HandleTypeEnum.SUBMIT.getCode())
                .orderType(WorkOrderType.shenhe.getType())
                .workOrderId(workOrderId)
                .pinKunHandleParam(null)
                .pinKunComment("")
                .diBaoHandleParam(diBaoParamVO)
                .diBaoComment(diBaoComment)
                .passIds(Arrays.asList(InitialAuditOperateService.CREDIT_TAG, InitialAuditOperateService.FIRST_APPROVE_TAG))
                .rejectIds(rejectIdList)
                .callStatus(0)
                .userCallStatus(0)
                .callComment("")
                .userId(AdminUserIDConstants.SYSTEM)
                .handleComment("")
                .build();
        try {
            initialAuditOperateService.handleWorkOrder(handleCaseInfoParam);
        } catch (Exception e) {
            String content = "【初审】自动进入1v1的案例自动审核驳回异常，请及时处理\n案例Id:" + handleCaseInfoParam.getCaseId() + "\n" + "工单Id:" + handleCaseInfoParam.getWorkOrderId()
                    + "\n" + "异常信息：" + e.getMessage();
            AlarmBotService.sentText(WxBotSendMsgTypeEnum.INITIAL_SMART_AUDIT_SUCCESS.getBotKey(), content, null, null);
            log.info("InitialAuditContentTo1V1Service rejectCaseToContent1V1 is error {}", handleCaseInfoParam, e);
        }
    }


    public void recordInitialClewAssign(Initial1v1AssignInfo param) {
        if (param == null) {
            log.info("生成代办，组长在分配通知时,参数错误");
        }

        approveRemarkOldService.add(param.getCaseId(), AdminUserIDConstants.SYSTEM,
                "当前1v1服务人员：" + seaAccountDelegate.getNameByLongUserId(param.getOperateId()));
    }
}
