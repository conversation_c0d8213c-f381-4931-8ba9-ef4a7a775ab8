package com.shuidihuzhu.cf.service.sensitive.processor;

import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordResult;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminUGCTask;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.sensitive.adapter.ISensitiveAdapter;
import com.shuidihuzhu.cf.service.sensitive.checker.ISensitiveChecker;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-03-14  14:40
 */
public interface SensitiveProcessService {

    /**
     * 处理ugc内容 决定是否创建工单
     * @param data
     * @param sensitiveProcessor
     * @param <T>
     */
    <T> void process(T data, ISensitiveAdapter<T> sensitiveProcessor);

    void handleWorkOrder(AdminWorkOrder adminWorkOrder, AdminUGCTask.Result result);

    <T> boolean processRecord(T data,
                              ISensitiveAdapter<T> adapter,
                              ISensitiveChecker sensitiveWorkChecker,
                              Map<ISensitiveChecker, OpResult<RiskWordResult>>resultMap);

}
