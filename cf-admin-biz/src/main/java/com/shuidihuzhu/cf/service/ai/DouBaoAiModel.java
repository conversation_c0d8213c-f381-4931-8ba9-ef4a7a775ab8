package com.shuidihuzhu.cf.service.ai;

import com.shuidihuzhu.ai.alps.client.feign.AihubFeignClient;
import com.shuidihuzhu.ai.alps.client.model.aihub.DoubaoChatRequest;
import com.shuidihuzhu.client.cf.admin.model.AIGenerateParam;
import com.shuidihuzhu.client.model.ChatChunk;
import com.shuidihuzhu.client.model.ChatCompletionChunk;
import com.shuidihuzhu.common.web.model.Response;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * @Description: 豆包
 * @Author: panghairui
 * @Date: 2024/5/31 4:26 PM
 */
@Service
public class DouBaoAiModel extends BaseModel {

    @Resource
    private AihubFeignClient aihubFeignClient;

    private static final String MODEL_NAME = "ep-20240515083244-6z9bh";

    @Override
    protected String callModelApi(AIGenerateParam aiGenerateParam, String prompt) {

        DoubaoChatRequest doubaoChatRequest = new DoubaoChatRequest();
        doubaoChatRequest.setAppCode("1004");
        doubaoChatRequest.setEndpoint(MODEL_NAME);
        doubaoChatRequest.setPrompt(prompt);

        Response<String> response = aihubFeignClient.doubaoChat(doubaoChatRequest);
        String json = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse("");
        if ("".equals(json)) {
            return "";
        }

        return json;
    }

    @Override
    protected Flux<ChatChunk<ChatCompletionChunk>> stream(String prompt) {
        return super.streamGenerate(MODEL_NAME, prompt);
    }
}
