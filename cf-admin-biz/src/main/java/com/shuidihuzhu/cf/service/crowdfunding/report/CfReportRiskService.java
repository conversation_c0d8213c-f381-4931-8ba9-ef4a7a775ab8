package com.shuidihuzhu.cf.service.crowdfunding.report;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingReportDao;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminCfReportLabelStatusEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfReportInfoSourceEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfReportPageEnum;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileResult;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileSettings;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.service.cfOperatingProfile.impl.CfOperatingProfileSettingsBizImpl;
import com.shuidihuzhu.cf.vo.crowdfunding.*;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther: subing
 * @Date: 2020/3/11
 */
@Service
@Slf4j
public class CfReportRiskService {
    @Autowired
    private ICfReportAnswerService cfReportAnswerService;
    @Autowired
    private CfOperatingProfileSettingsBizImpl cfOperatingProfileSettingsBiz;
    @Autowired
    private AdminCrowdfundingReportBiz adminCrowdfundingReportBiz;
    @Autowired
    private AdminCfRiskLabelBiz adminCfRiskLabelBiz;
    @Autowired
    private IReportCommunicaterListService iReportCommunicaterListService;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private IReportCommunicaterListService reportCommunicaterListService;
    @Autowired
    private ICfFundraiserCommunicateService fundraiserCommunicateService;
    @Autowired
    private CfReportProblemManagerService cfReportProblemManagerService;
    @Autowired
    private AdminCrowdfundingReportDao adminCrowdfundingReportDao;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;


    /**
     * 质疑人列表
     *
     * @param caseId
     * @return
     */
    public List<CfQuestionerVo> getQuestionerList(int caseId) {
        List<CfQuestionerVo> cfQuestionerVos = Lists.newArrayList();
        List<CrowdfundingReport> crowdfundingReports = adminCrowdfundingReportBiz.getListByInfoId(caseId);
        List<Integer> reportIds = crowdfundingReports.stream().map(CrowdfundingReport::getId).collect(Collectors.toList());
        reportIds.forEach(reportId -> {
            CrowdfundingReport crowdfundingReport = crowdfundingReports.stream().filter(t -> t.getId() == reportId).collect(Collectors.toList()).get(0);
            CfQuestionerVo cfQuestionerVo = new CfQuestionerVo();
            cfQuestionerVo.setReportContent(crowdfundingReport.getContent());
            cfQuestionerVo.setImgUrls(crowdfundingReport.getImageUrls());
            List<CfReportCommunicaterDO> reportCommunicaterDOS = reportCommunicaterListService.query(caseId, reportId, CfReportPageEnum.QUESTIONER.getKey());
            List<AdminReportProblemAnswerDetail> answerDetails =
                    cfReportAnswerService.query(caseId, reportId, CfReportPageEnum.QUESTIONER.getKey());
            answerDetails.forEach(answerDetail -> {
                CfReportInfoSourceEnum cfReportInfoSourceEnum = CfReportInfoSourceEnum.parse(answerDetail.getInfoSourceKey());
                if (cfReportInfoSourceEnum != null){
                    answerDetail.setInfoSourceValue(cfReportInfoSourceEnum.getValue());
                }
            });
            cfQuestionerVo.setCommunication(answerDetails);
            List<String> cfQuestionerReportVos = Lists.newArrayList();
            reportCommunicaterDOS.forEach(reportCommunicaterDO -> {
                cfQuestionerReportVos.add(shuidiCipher.decrypt(reportCommunicaterDO.getMobile()));
            });
            cfQuestionerVo.setPhoneList(cfQuestionerReportVos);
            cfQuestionerVos.add(cfQuestionerVo);
        });
        return cfQuestionerVos;
    }


    /**
     * 捐款人列表
     *
     * @param caseId
     * @return
     */
    public List<CfFundraiserVo> getCfFundraiserVoList(int caseId) {
        List<CfFundraiserVo> cfFundraiserVos = Lists.newArrayList();
        List<CfFundraiserCommunicateDO> cfFundraiserCommunicateDOS = fundraiserCommunicateService.query(caseId);
        cfFundraiserCommunicateDOS.forEach(cfFundraiserCommunicateDO -> {
            CfFundraiserCommunicateDO communicateDO = fundraiserCommunicateService.queryById(cfFundraiserCommunicateDO.getId());
            if (!Objects.isNull(communicateDO) && !StringUtils.isEmpty(communicateDO.getAnswerIds())) {
                String answerIds = communicateDO.getAnswerIds();
                List<String> answerIdList = Lists.newArrayList(answerIds.split(","));
                List<Long> answers = Lists.newArrayList();
                for (String answerId : answerIdList) {
                    answers.add(Long.valueOf(answerId));
                }
                List<AdminReportProblemAnswerDetail> answerDetails = cfReportAnswerService.queryByIds(answers);
                CfFundraiserVo cfFundraiserVo = new CfFundraiserVo();
                cfFundraiserVo.setCommunication(answerDetails);
                cfFundraiserVo.setPhone(shuidiCipher.decrypt(cfFundraiserCommunicateDO.getMobile()));
                if (CollectionUtils.isNotEmpty(answerDetails)) {
                    cfFundraiserVo.setRelation(answerDetails.get(0).getRelationValue());
                }
                cfFundraiserVos.add(cfFundraiserVo);
            }
        });
        return cfFundraiserVos;
    }

    /**
     * 返回风险标签
     *
     * @param caseId
     * @param status
     * @return
     */
    public List<CfOperatingProfileResult> getRiskLabelList(int caseId, int status) {
        List<CfOperatingProfileResult> cfOperatingProfileResults =
                cfOperatingProfileSettingsBiz.queryAllSettingsByType(2);
        if (status == AdminCfReportLabelStatusEnum.ALL_LABEL.getType()) {
            return cfOperatingProfileResults;
        } else {
            List<AdminReportProblemAnswerDetail> adminReportProblemAnswerDetails =
                    cfReportAnswerService.queryByCaseId(caseId);
            Set<String> labelSets = Sets.newHashSet();
            adminReportProblemAnswerDetails.forEach(adminReportProblemAnswerDetail -> {
                labelSets.addAll(getLabelContent(answerLabel(adminReportProblemAnswerDetail.getAnswerDetail(), cfOperatingProfileResults)));
            });
            List<CfOperatingProfileResult> results = Lists.newArrayList();
            for (CfOperatingProfileResult cfOperatingProfileResult : cfOperatingProfileResults) {
                List<CfOperatingProfileSettings> cfOperatingProfileSettings = cfOperatingProfileResult.getSubSettings();
                if (status == AdminCfReportLabelStatusEnum.PERSON_LABEL.getType()) {
                    cfOperatingProfileSettings =
                            cfOperatingProfileSettings.stream()
                                    .filter(t -> (t != null) && (t.getProblemSettings() != null) && StringUtils.isAllBlank(t.getProblemSettings().getProblemContent(),  t.getProblemSettings().getModuleContent()))
                                    .collect(Collectors.toList());
                } else {
                    cfOperatingProfileSettings =
                            cfOperatingProfileSettings.stream().filter(t -> labelSets.contains(t.getContent())).collect(Collectors.toList());
                }
                if (CollectionUtils.isEmpty(cfOperatingProfileSettings)) {
                    continue;
                }
                CfOperatingProfileResult profileResult = new CfOperatingProfileResult();
                profileResult.setParentSettings(cfOperatingProfileResult.getParentSettings());
                profileResult.setSubSettings(cfOperatingProfileSettings);
                results.add(profileResult);
            }
            return results;
        }
    }

    private List<CfRiskSelectedVo> getAutoLabel(int caseId) {
        List<CfOperatingProfileResult> cfOperatingProfileResults =
                getRiskLabelList(caseId, AdminCfReportLabelStatusEnum.AUTO_LABEL.getType());
        List<CfRiskSelectedVo> cfRiskSelectedVos = Lists.newArrayList();
        cfOperatingProfileResults.forEach(cfOperatingProfileResult -> {
            List<CfOperatingProfileSettings> cfOperatingProfileSettings =
                    cfOperatingProfileResult.getSubSettings();
            if (CollectionUtils.isNotEmpty(cfOperatingProfileSettings)) {
                cfOperatingProfileSettings.forEach(cfOperatingProfileSetting -> {
                    CfRiskSelectedVo cfRiskSelectedVo = new CfRiskSelectedVo();
                    cfRiskSelectedVo.setId(cfOperatingProfileSetting.getId());
                    cfRiskSelectedVo.setParentId(cfOperatingProfileSetting.getParentId());
                    cfRiskSelectedVo.setPropertyList(cfOperatingProfileSetting.getPropertyList());
                    cfRiskSelectedVo.setContent(cfOperatingProfileSetting.getContent());
                    cfRiskSelectedVos.add(cfRiskSelectedVo);
                });
            }
        });
        return cfRiskSelectedVos;
    }

    /**
     * 返回案例选中匹配的风险标签
     *
     * @param caseId
     * @return
     */
    public List<CfRiskSelectedVo> getCaseRiskLabel(int caseId) {
        String label = adminCfRiskLabelBiz.getByActivityId(caseId);
        if (StringUtils.isBlank(label)) {
            return getAutoLabel(caseId);
        }
        List<CfRiskSelectedVo> cfRiskSelectedVos = JSONObject.parseObject(label, new TypeReference<List<CfRiskSelectedVo>>() {
        });
        //检查标签是否被删除
        List<CfOperatingProfileResult> cfOperatingProfileResults =
                cfOperatingProfileSettingsBiz.queryAllSettingsByType(2);
        List<CfRiskSelectedVo> riskSelectedVos = Lists.newArrayList();
        cfOperatingProfileResults.forEach(cfOperatingProfileResult -> {
            List<CfOperatingProfileSettings> cfOperatingProfileSettings = cfOperatingProfileResult.getSubSettings();
            if (CollectionUtils.isNotEmpty(cfOperatingProfileSettings)) {
                cfRiskSelectedVos.forEach(cfRiskSelectedVo -> {
                    List<CfOperatingProfileSettings> profileSettings = cfOperatingProfileSettings.stream()
                            .filter(t -> StringUtils.equals(cfRiskSelectedVo.getContent(), t.getContent())
                                    && StringUtils.equals(Joiner.on(",").join(CollectionUtils.isNotEmpty(cfRiskSelectedVo.getPropertyList()) ? cfRiskSelectedVo.getPropertyList() : Lists.newArrayList()),
                                    Joiner.on(",").join(CollectionUtils.isNotEmpty(t.getPropertyList()) ? t.getPropertyList() : Lists.newArrayList()))).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(profileSettings)) {
                        List<CfRiskSelectedVo> cfRiskSelectedVoList = riskSelectedVos.stream().filter(t -> t.getId() == cfRiskSelectedVo.getId()).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(cfRiskSelectedVoList)) {
                            riskSelectedVos.add(cfRiskSelectedVo);
                        }
                    }
                });
            }
        });
        return riskSelectedVos;
    }

    public int addCaseRiskLabel(String labelJson, int caseId) {
        String isEmpty = adminCfRiskLabelBiz.getByActivityId(caseId);
        return StringUtils.isBlank(isEmpty) ? adminCfRiskLabelBiz.add(caseId, labelJson) :
                adminCfRiskLabelBiz.updateRiskLabel(caseId, labelJson);
    }

    public int addReportRiskLabel(int reportId, String label) {
        return adminCrowdfundingReportBiz.updateRiskLabel(label, reportId);
    }

    /**
     * 返回匹配的标签
     *
     * @param caseId
     * @param crowdfundingReport
     * @return
     */
    public List<CfReportLabelVo> getLabelList(int caseId,
                                              CrowdfundingReport crowdfundingReport, List<CfOperatingProfileResult> cfOperatingProfileResults) {
        int reportId = crowdfundingReport.getId();
        List<AdminReportProblemAnswerDetail> answerDetails =
                cfReportAnswerService.query(caseId, reportId, CfReportPageEnum.QUESTIONER.getKey());
        if (StringUtils.isNotBlank(crowdfundingReport.getRiskLabel())) {
            List<CfReportLabelVo> cfReportLabelVos = JSONObject.parseObject(crowdfundingReport.getRiskLabel(), new TypeReference<List<CfReportLabelVo>>() {
            });
            List<String> labels = getLabelContent(cfReportLabelVos);
            List<String> labelStrings = Lists.newArrayList();
            cfOperatingProfileResults.forEach(cfOperatingProfileResult -> {
                List<CfOperatingProfileSettings> cfOperatingProfileSettings =
                        cfOperatingProfileResult.getSubSettings();
                labelStrings.addAll(cfOperatingProfileSettings.stream().filter(t -> (labels.contains(t.getContent()))).map(CfOperatingProfileSettings::getContent).collect(Collectors.toList()));
            });
            cfReportLabelVos = cfReportLabelVos.stream().filter(t -> labelStrings.contains(t.getContent())).collect(Collectors.toList());
            return CollectionUtils.isNotEmpty(cfReportLabelVos) ? cfReportLabelVos : null;
        }
        Set<CfReportLabelVo> labelSet = Sets.newHashSet();
        answerDetails.forEach(answerDetail -> {
            labelSet.addAll(answerLabel(answerDetail.getAnswerDetail(), cfOperatingProfileResults));
        });
        return CollectionUtils.isNotEmpty(labelSet) ? Lists.newArrayList(labelSet) : null;
    }

    private List<String> getLabelContent(List<CfReportLabelVo> cfReportLabelVos) {
        if (CollectionUtils.isEmpty(cfReportLabelVos)) {
            return Lists.newArrayList();
        }
        return cfReportLabelVos.stream().map(CfReportLabelVo::getContent).collect(Collectors.toList());
    }


    /**
     * 保存质疑方或者筹款方举报问题的答案时字段生成默认标签
     *
     * @param reportId
     * @param caseId
     * @param answer
     * @return
     */
    public int saveLabel(int reportId, int caseId, String answer) {
        List<CfOperatingProfileResult> cfOperatingProfileResults =
                cfOperatingProfileSettingsBiz.queryAllSettingsByType(2);
        CrowdfundingReport crowdfundingReport = adminCrowdfundingReportBiz.query(caseId, reportId);
        if (crowdfundingReport == null) {
            return 0;
        }
        Set<CfReportLabelVo> labelSet = Sets.newHashSet();
        if (StringUtils.isBlank(crowdfundingReport.getRiskLabel())) {
            List<CfReportLabelVo> answerLabels = answerLabel(answer, cfOperatingProfileResults);
            labelSet.addAll(answerLabels);
            if (CollectionUtils.isNotEmpty(labelSet)) {
                updateRiskLabel(answerLabels, caseId);
                return adminCrowdfundingReportBiz.updateRiskLabel(JSONObject.toJSONString(labelSet), reportId);
            }
            return 0;
        }
        List<CfReportLabelVo> answerLabels = answerLabel(answer, cfOperatingProfileResults);
        List<CfReportLabelVo> cfReportLabelVos =
                JSONObject.parseObject(crowdfundingReport.getRiskLabel(), new TypeReference<List<CfReportLabelVo>>() {
                });
        if (CollectionUtils.isNotEmpty(cfReportLabelVos)) {
            labelSet.addAll(cfReportLabelVos);
        }
        labelSet.addAll(answerLabels);
        updateRiskLabel(answerLabels, caseId);
        return adminCrowdfundingReportBiz.updateRiskLabel(JSONObject.toJSONString(labelSet), reportId);
    }

    public void updateRiskLabel(List<CfReportLabelVo> answerLabels, int caseId) {
        String label = adminCfRiskLabelBiz.getByActivityId(caseId);
        List<CfRiskSelectedVo> cfRiskSelectedVoList = Lists.newArrayList();
        if (StringUtils.isBlank(label)) {
            answerLabels.forEach(answerLabel -> {
                cfRiskSelectedVoList.add(CfRiskSelectedVo.buildVo(answerLabel));
            });
            adminCfRiskLabelBiz.add(caseId, JSONObject.toJSONString(cfRiskSelectedVoList));
            return;
        }
        List<CfRiskSelectedVo> cfRiskSelectedVos = JSONObject.parseObject(label, new TypeReference<List<CfRiskSelectedVo>>() {
        });
        answerLabels.forEach(answerLabel -> {
            List<CfRiskSelectedVo> riskSelectedVos = cfRiskSelectedVos.stream().filter(t -> StringUtils.equals(t.getContent(), answerLabel.getContent())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(riskSelectedVos)) {
                cfRiskSelectedVos.add(CfRiskSelectedVo.buildVo(answerLabel));
            }
        });
        adminCfRiskLabelBiz.updateRiskLabel(caseId, JSONObject.toJSONString(cfRiskSelectedVos));
    }

    /**
     * 自动匹配标签
     *
     * @param answer
     * @param cfOperatingProfileResults
     * @return
     */
    public List<CfReportLabelVo> answerLabel(String answer, List<CfOperatingProfileResult> cfOperatingProfileResults) {
        if (StringUtils.isEmpty(answer)) {
            return Lists.newArrayList();
        }
        List<AdminReportProblemAnswer> answerDetails = JSON.parseObject(answer, new TypeReference<List<AdminReportProblemAnswer>>() {
        });
        List<CfReportLabelVo> labels = Lists.newArrayList();
        if (CollectionUtils.isEmpty(answerDetails)) {
            return Lists.newArrayList();
        }

        for (AdminReportProblemAnswer problemAnswer : answerDetails) {

            if (CollectionUtils.isEmpty(problemAnswer.getProblemLabelAnswers())) {
                return Lists.newArrayList();
            }

            for (AdminReportProblemLabelAnswer labelAnswer : problemAnswer.getProblemLabelAnswers()) {

                if (CollectionUtils.isEmpty(labelAnswer.getLabelAnswers()) && labelAnswer.getDirectShowLabel() == null) {
                    continue;
                } else if (CollectionUtils.isEmpty(labelAnswer.getLabelAnswers()) && labelAnswer.getDirectShowLabel() != null) {
                    log.info("first:{}, second:{}, third:{}", problemAnswer.getSecondLabelDesc(), labelAnswer.getDirectShowLabel().getPrefixProblem(), labelAnswer.getDirectShowLabel().getAnswer());
                    labels.addAll(addLabel(problemAnswer.getSecondLabelDesc(), labelAnswer.getDirectShowLabel().getPrefixProblem(), labelAnswer.getDirectShowLabel().getAnswer(), cfOperatingProfileResults));
                    continue;
                }
                for (List<AdminReportProblemLabel> labelAnswerList : labelAnswer.getLabelAnswers()) {
                    for (AdminReportProblemLabel problemLabel : labelAnswerList) {

                        CfReportProblem.ReportAnswerType answerType = CfReportProblem.ReportAnswerType.findByCode(problemLabel.getPrefixAnswerType());
                        String answerItem = problemLabel.getAnswer();
                        //0:必填
                        if (0 == problemLabel.getMustAnswer() && StringUtils.isEmpty(answerItem)) {
                            return Lists.newArrayList();
                        }

                        if (answerType == CfReportProblem.ReportAnswerType.shuzhi && !NumberUtils.isDigits(answerItem) && !"不知道".equals(answerItem)) {
                            return Lists.newArrayList();
                        }
                        log.info("first:{}, second:{}, third:{}", problemAnswer.getSecondLabelDesc(), problemLabel.getPrefixProblem(), answerItem);
                        List<CfReportLabelVo> label = addLabel(problemAnswer.getSecondLabelDesc(), problemLabel.getPrefixProblem(), answerItem, cfOperatingProfileResults);
                        if (CollectionUtils.isNotEmpty(label)) {
                            labels.addAll(label);
                        }

                    }
                }
            }
        }
        return labels;
    }

    public List<CfReportLabelVo> addLabel(String moduleContent, String nameContent,
                                          String remarkContent, List<CfOperatingProfileResult> cfOperatingProfileResults) {
        List<CfReportLabelVo> labels = Lists.newArrayList();
        for (CfOperatingProfileResult cfOperatingProfileResult : cfOperatingProfileResults) {
            List<CfOperatingProfileSettings> profileSettings = cfOperatingProfileResult.getSubSettings();
            for (CfOperatingProfileSettings cfOperatingProfileSettings : profileSettings) {
                CfOperatingProfileSettings.ProfileProblemSettings profileProblemSettings =
                        cfOperatingProfileSettings.getProblemSettings();
                if (profileProblemSettings == null) {
                    continue;
                }
                String profileModuleContent = profileProblemSettings.getModuleContent();
                String profileNameContent = profileProblemSettings.getProblemContent();
                String profileRemarkContent = profileProblemSettings.getRemark();
                if (StringUtils.equals(moduleContent, profileModuleContent)
                        && StringUtils.equals(nameContent, profileNameContent) &&
                        StringUtils.equals(remarkContent, profileRemarkContent)) {
                    labels.add(new CfReportLabelVo(cfOperatingProfileSettings.getId(), cfOperatingProfileSettings.getParentId(), cfOperatingProfileSettings.getContent(), cfOperatingProfileSettings.getPropertyList()));
                } else if (StringUtils.equals(moduleContent, profileModuleContent)
                        && StringUtils.equals(nameContent, profileNameContent) && StringUtils.isBlank(profileRemarkContent)) {
                    labels.add(new CfReportLabelVo(cfOperatingProfileSettings.getId(), cfOperatingProfileSettings.getParentId(), cfOperatingProfileSettings.getContent(), cfOperatingProfileSettings.getPropertyList()));
                } else if (StringUtils.equals(moduleContent, profileModuleContent)
                        && StringUtils.isBlank(profileRemarkContent) && StringUtils.isBlank(profileNameContent)) {
                    labels.add(new CfReportLabelVo(cfOperatingProfileSettings.getId(), cfOperatingProfileSettings.getParentId(), cfOperatingProfileSettings.getContent(), cfOperatingProfileSettings.getPropertyList()));
                }
            }
        }
        return labels;
    }
}
