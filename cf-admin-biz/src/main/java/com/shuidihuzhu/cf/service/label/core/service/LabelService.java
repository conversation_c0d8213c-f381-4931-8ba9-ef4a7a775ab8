package com.shuidihuzhu.cf.service.label.core.service;


import com.shuidihuzhu.cf.domain.label.core.LabelDO;
import com.shuidihuzhu.cf.domain.label.core.LabelNodeModel;
import com.shuidihuzhu.cf.domain.label.risk.Label;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface LabelService {

    /**
     *
     * 根据标签id获取该标签信息与对应子标签树数据
     * @param id 标签id
     * @return 该标签信息与对应子标签树数据
     */
    LabelNodeModel getEnableLabelNodeById(long id);

    List<LabelNodeModel> getSimpleEnableLabelNodeByIdList(List<Long> labelIdList);

    /**
     * 根据标签id列表获取标签数据
     * @param ids
     * @return
     */
    List<Label> getLabelByIds(List<Long> ids);

    Label getLabelById(Long id);

    List<Label> getLabelByUuidList(List<String> uuidList);
}
