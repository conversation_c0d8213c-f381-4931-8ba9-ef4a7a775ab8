package com.shuidihuzhu.cf.service.crowdfunding.strategy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.biz.crowdfunding.ICfReportAnswerService;
import com.shuidihuzhu.cf.enums.crowdfunding.CfReportPageEnum;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfReportProblemRaiserLabel;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @author: fengxuan
 * @create 2019-12-16 16:50
 **/
@Service
@Slf4j
public class ReportLabelCreateStrategyImpl implements IReportLabelCreateStrategy {

    @Autowired
    private ICfReportAnswerService cfReportAnswerService;

    //治疗花费-医保报销  属于病情情况,  用于多位患者-其它使用用途 属于款项使用
    static final List<String> basic_second_labels = Lists.newArrayList(
            "房屋信息",
            "车辆信息",
            "房屋信息",
            "收入信息",
            "敏感职业",

            "治疗花费",
            "去世/在世",
            "医保报销",

            "用于多位患者",
            "用于丧葬费",
            "偿还医院欠款",
            "偿还个人欠款",
            "不符合平台规定用途",
            "其它使用用途"

    );


    static final Map<String, List<String>> special_second_label = Maps.newHashMap();

    static final List<Answer> label_Create_ByProblem = Lists.newArrayList();

    static {
        special_second_label.put("事故赔偿", Lists.newArrayList("事故赔偿", "平台多次筹款", "政府救助", "其他救助"));
        special_second_label.put("偿还个人欠款", Lists.newArrayList("负债"));
        special_second_label.put("舆情关注", Lists.newArrayList("舆情关注", "事故赔偿", "平台多次筹款", "政府救助",
                "其他救助", "商业保险", "有价证券", "理财产品", "存款", "其它金融资产", "负债",
                "其他实物资产"));

        Answer answer = new Answer();
        answer.setLabelDesc("其他举报");
        answer.setProblemDesc("举报类型");
        answer.setAnswers(Lists.newArrayList("筹款人外国籍", "其它", "海外就医"));
        label_Create_ByProblem.add(answer);
    }


    @Data
    static class Answer {
        String labelDesc;
        String problemDesc;
        List<String> answers;
    }

    @Data
    static class ProblemAndAnswer {
        String problemDesc;
        String answer;
    }


    @Override
    public List<CfReportProblemRaiserLabel> create(int caseId, List<CfReportProblemLabel> allLabels) {
        List<String> defaultLabels = Lists.newArrayList();
        //获取质疑人答案中的标签
        List<String> reportLabels = reporterLabels(caseId);

        List<ProblemAndAnswer> parserAnswer = getParserAnswer(caseId);

        List<String> specialLabelList = handleSpecialLabel(reportLabels, parserAnswer);

        defaultLabels.addAll(reportLabels);
        defaultLabels.addAll(specialLabelList);

        return createRaiserLabel(allLabels, Sets.newHashSet(defaultLabels));

    }

    @NotNull
    private List<String> handleSpecialLabel(List<String> reportLabelSet, List<ProblemAndAnswer> parserAnswer) {
        List<String> specialLabelList = Lists.newArrayList();

        special_second_label.keySet()
                .stream()
                .filter(reportLabelSet::contains)
                .forEach(item -> {
                    reportLabelSet.remove(item);
                    specialLabelList.addAll(special_second_label.get(item));
                });


        parserAnswer.forEach(item -> {
            for (Answer answer : label_Create_ByProblem) {
                if (answer.getProblemDesc().equals(item.getProblemDesc()) &&
                        answer.getAnswers().contains(item.getAnswer())) {
                    specialLabelList.add(answer.getLabelDesc());
                }
            }
        });
        return specialLabelList;
    }

    @NotNull
    private List<CfReportProblemRaiserLabel> createRaiserLabel(List<CfReportProblemLabel> allLabels, Set<String> defaultLabels) {
        List<CfReportProblemRaiserLabel> raiserLabels = Lists.newArrayList();
        allLabels.stream().filter(item -> defaultLabels.contains(item.getLabelDesc()))
                .forEach(item -> {
                    CfReportProblemRaiserLabel raiserLabel = new CfReportProblemRaiserLabel();
                    BeanUtils.copyProperties(item, raiserLabel);
                    if (defaultLabels.contains(item.getLabelDesc())) {
                        raiserLabel.setNeedAsk(true);
                    }
                    raiserLabels.add(raiserLabel);
                });
        return raiserLabels;
    }


    //质疑人回答过的问题标签,去重
    //这里面之所以用字符串匹配，就是为了避免线上和线下导入的数据不一致
    private List<String> reporterLabels(int caseId) {
        List<AdminReportProblemAnswerDetail> answerDetails = Lists.newArrayList();
        AdminReportProblemAnswerDetail adminReportProblemAnswerDetail =
                cfReportAnswerService.queryLastByCaseId(caseId, CfReportPageEnum.FUNDRAISER.getKey());
        if (adminReportProblemAnswerDetail != null) {
            answerDetails.add(adminReportProblemAnswerDetail);
        }
        if(CollectionUtils.isEmpty(answerDetails)){
            return Lists.newArrayList();
        }
        Set<String> labelSet = Sets.newHashSet();
        for (AdminReportProblemAnswerDetail problemAnswerDetail : answerDetails){
            if(Objects.isNull(problemAnswerDetail) || StringUtils.isEmpty(problemAnswerDetail.getAnswerDetail())){
                continue;
            }

            List<AdminReportProblemAnswer> answerDetailList = JSON.parseObject(problemAnswerDetail.getAnswerDetail(), new TypeReference<List<AdminReportProblemAnswer>>(){});//已检查过
            if(CollectionUtils.isEmpty(answerDetailList)){
                continue;
            }

            for (AdminReportProblemAnswer problemAnswer : answerDetailList){
                labelSet.add(problemAnswer.getSecondLabelDesc());
            }
        }

        return Lists.newArrayList(labelSet);
    }

    //质疑人答过的问题,(问题描述+答案)
    //这里面之所以用字符串匹配，就是为了避免线上和线下导入的数据不一致
    private List<ProblemAndAnswer> getParserAnswer(int caseId) {

        List<AdminReportProblemAnswerDetail> answerDetails = cfReportAnswerService.queryByType(caseId, CfReportPageEnum.QUESTIONER.getKey());
        if(CollectionUtils.isEmpty(answerDetails)){
            return Lists.newArrayList();
        }

        List<ProblemAndAnswer> problemAndAnswers = Lists.newArrayList();
        for (AdminReportProblemAnswerDetail problemAnswerDetail : answerDetails){
            if(Objects.isNull(problemAnswerDetail) || StringUtils.isEmpty(problemAnswerDetail.getAnswerDetail())){
                continue;
            }

            List<AdminReportProblemAnswer> answerDetailList = JSON.parseObject(problemAnswerDetail.getAnswerDetail(), new TypeReference<List<AdminReportProblemAnswer>>(){});//已检查过
            if(CollectionUtils.isEmpty(answerDetailList)){
                continue;
            }

            for (AdminReportProblemAnswer problemAnswer : answerDetailList){
                if("其他举报".equals(problemAnswer.getSecondLabelDesc()) && CollectionUtils.isNotEmpty(problemAnswer.getProblemLabelAnswers())){
                    List<AdminReportProblemLabelAnswer> labelAnswers = problemAnswer.getProblemLabelAnswers();
                    for (AdminReportProblemLabelAnswer reportProblemLabel : labelAnswers){
                        for (List<AdminReportProblemLabel> problemLabels : reportProblemLabel.getLabelAnswers()){
                            for (AdminReportProblemLabel problemLabel : problemLabels){
                                if(StringUtils.isNotEmpty(problemLabel.getAnswer())){
                                    ProblemAndAnswer problemAndAnswer = new ProblemAndAnswer();
                                    problemAndAnswer.setProblemDesc(problemLabel.getPrefixProblem());
                                    problemAndAnswer.setAnswer(problemLabel.getAnswer());
                                    problemAndAnswers.add(problemAndAnswer);
                                }
                            }
                        }
                    }
                }
            }
        }
        return problemAndAnswers;
    }
}
