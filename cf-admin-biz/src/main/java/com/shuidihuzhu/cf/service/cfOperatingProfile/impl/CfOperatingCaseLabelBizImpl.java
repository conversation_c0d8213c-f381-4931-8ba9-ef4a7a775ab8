package com.shuidihuzhu.cf.service.cfOperatingProfile.impl;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.dao.cfOperatingProfile.CfOperatingCaseLabelDao;
import com.shuidihuzhu.cf.dao.cfOperatingProfile.CfOperatingCaseLabelLogDao;
import com.shuidihuzhu.cf.dao.cfOperatingProfile.CfOperatingProfileSettingsDao;
import com.shuidihuzhu.cf.delegate.OrganizationDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingCaseLabel;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingCaseLabelLog;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileLog;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileSettings;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.cf.service.cfOperatingProfile.CfOperatingCaseLabelBiz;
import com.shuidihuzhu.cf.service.cfOperatingProfile.CfOperatingProfileSettingsBiz;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CfOperatingCaseLabelBizImpl implements CfOperatingCaseLabelBiz {

    @Autowired
    private CfOperatingProfileSettingsBiz profileSettingsBiz;
    @Autowired
    private CfOperatingCaseLabelDao caseLabelDao;
    @Autowired
    private ApproveRemarkOldService remarkOldService;
    @Autowired
    private CfOperatingCaseLabelLogDao caseLabelLogDao;
    @Autowired
    private OrganizationDelegate orgService;
    @Autowired
    private CfOperatingProfileSettingsDao cfOperatingProfileSettingsDao;


    @Override
    public List<CfOperatingProfileSettings> selectCaseLabels(int caseId) {

        List<CfOperatingCaseLabel> caseLabels = caseLabelDao.selectOperatingCaseLabels(caseId);

        if (CollectionUtils.isEmpty(caseLabels)) {
            return Lists.newArrayList();
        }

        List<Integer> labelIds = caseLabels.stream().map(CfOperatingCaseLabel::getCaseLabelId).collect(Collectors.toList());

        return profileSettingsBiz.selectByIds(labelIds);
    }

    @Override
    public List<CfOperatingCaseLabel> selectOperatingCaseLabels(int caseId) {
        return caseLabelDao.selectOperatingCaseLabels(caseId);
    }

    @Override
    public CfOperatingProfileSettings selectById(int id) {
        return cfOperatingProfileSettingsDao.selectById(id);
    }



    @Override
    public AdminErrorCode addCaseLabels(CfOperatingCaseLabel.QueryParam queryParam) {

        log.info("操作案例的标签 param:{}", queryParam);
        if (CollectionUtils.isEmpty(queryParam.getLabelIds())) {
            queryParam.setLabelIds(Sets.newHashSet());
        }

        if (CollectionUtils.isEmpty(queryParam.getOldLabelIds())) {
            queryParam.setOldLabelIds(Sets.newHashSet());
        }


        List<CfOperatingCaseLabel> caseLabels = caseLabelDao.selectOperatingCaseLabels(queryParam.getCaseId());

        log.info("当前案例的文案 caseId:{} labelId:{}", queryParam.getCaseId(), caseLabels);
        if (!canModifyCaseLabels(queryParam.getOldLabelIds(), caseLabels)) {
            return AdminErrorCode.CONCURRENT_MODIFY_CASE_LABELS;
        }

        modifyCaseLabels(queryParam, caseLabels);

        return AdminErrorCode.SUCCESS;
    }

    private void modifyCaseLabels(CfOperatingCaseLabel.QueryParam queryParam, List<CfOperatingCaseLabel> caseLabels) {
        // 找到删除和增加的标签数
        Set<Integer> labelIds = queryParam.getLabelIds();
        List<Long> deleteIds = Lists.newArrayList();
        List<Integer> deleteLabelIds = Lists.newArrayList();
        for (CfOperatingCaseLabel label : caseLabels) {
            if (labelIds.contains(label.getCaseLabelId())) {
                labelIds.remove(label.getCaseLabelId());
            } else {
                deleteIds.add(label.getId());
                deleteLabelIds.add(label.getCaseLabelId());

            }
        }

        if (CollectionUtils.isNotEmpty(deleteIds)) {
            log.info("用户删除了这几个标签 userId:{} caseId:{} labelId:{}", queryParam.getUserId(), queryParam.getCaseId(),
                    deleteLabelIds);
            caseLabelDao.deleteCaseLabelByIds(deleteIds);
            profileSettingsBiz.updateUseSize(deleteLabelIds, -1);
        }

        if (CollectionUtils.isNotEmpty(labelIds)) {
            log.info("用户增加了这几个标签 userId:{} caseId:{} labelId:{}", queryParam.getUserId(), queryParam.getCaseId(),
                    labelIds);
            List<CfOperatingCaseLabel> addLabels = Lists.newArrayList();
            for (Integer id : labelIds) {
                addLabels.add(new CfOperatingCaseLabel(queryParam.getCaseId(), id, queryParam.getUserId()));
            }
            caseLabelDao.addCaseLabels(addLabels);
            profileSettingsBiz.updateUseSize(labelIds, 1);
        }

        addCaseLabelsLog(queryParam, deleteLabelIds, labelIds);
        recordComment(queryParam.getUserId(), queryParam.getCaseId(), deleteLabelIds, labelIds);
    }

    private boolean canModifyCaseLabels(Set<Integer> oldLabels, List<CfOperatingCaseLabel> caseLabels) {

        if (CollectionUtils.isEmpty(oldLabels)) {
            return CollectionUtils.isEmpty(caseLabels);
        }

        Set<Integer> curLabels = Sets.newHashSet();
        for (CfOperatingCaseLabel label : caseLabels) {
            curLabels.add(label.getCaseLabelId());
        }

        if (oldLabels.size() != curLabels.size()) {
            return false;
        }

        for (Integer label : oldLabels) {
            if (!curLabels.contains(label)) {
                return false;
            }
        }

        return true;
    }


    private void recordComment(int userId, int caseId, List<Integer> deleteLabelIds, Set<Integer> addLabelIds) {
        if (CollectionUtils.isEmpty(deleteLabelIds) && CollectionUtils.isEmpty(addLabelIds)) {
            return;
        }
        List<Integer> allLabelIds = Lists.newArrayList();
        allLabelIds.addAll(deleteLabelIds);
        allLabelIds.addAll(addLabelIds);

        List<CfOperatingProfileSettings> profileSettings =  profileSettingsBiz.selectByIds(allLabelIds);
        Map<Integer, String> map = Maps.newHashMap();
        for (CfOperatingProfileSettings settings : profileSettings) {
            map.put(settings.getId(), settings.getContent());
        }

        StringBuilder content = new StringBuilder();
        if (CollectionUtils.isNotEmpty(addLabelIds)) {
            content.append("添加标签: ");
            appendLabelText(addLabelIds, map, content);
        }

        if (CollectionUtils.isNotEmpty(deleteLabelIds)) {
            content.append("; ");
            content.append("删除标签: ");
            appendLabelText(deleteLabelIds, map, content);
        }

        remarkOldService.add(caseId, userId, content.toString());
    }

    private void appendLabelText(Collection<Integer> ids,  Map<Integer, String> map, StringBuilder content) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        for (Integer id : ids) {
            content.append(Strings.nullToEmpty(map.get(id)) + "、");
        }
        content.delete(content.length() - 1, content.length());
    }

    private void addCaseLabelsLog(CfOperatingCaseLabel.QueryParam queryParam,
                                  List<Integer> deleteLabelIds, Set<Integer> addLabelIds) {

        List<CfOperatingCaseLabelLog> addLogs= Lists.newArrayList();

        String orgName =  orgService.getSimpleOrganization(queryParam.getUserId());

        if (CollectionUtils.isNotEmpty(deleteLabelIds)) {
            for (Integer id : deleteLabelIds) {
                addLogs.add(buildOperatingLog(queryParam, id, CfOperatingProfileLog.OperateType.DELETE.getCode(), orgName));
            }
        }

        if (CollectionUtils.isNotEmpty(addLabelIds)) {
            for (Integer id : addLabelIds) {
                addLogs.add(buildOperatingLog(queryParam, id, CfOperatingProfileLog.OperateType.ADD.getCode(), orgName));
            }
        }

        if (CollectionUtils.isNotEmpty(addLogs)) {
            caseLabelLogDao.addCaseLabelLog(addLogs);
        }
    }

    private CfOperatingCaseLabelLog buildOperatingLog(CfOperatingCaseLabel.QueryParam queryParam,
                                                int labelId, int operateType, String orgName) {
        CfOperatingCaseLabelLog labelLog = new CfOperatingCaseLabelLog();
        labelLog.setCaseId(queryParam.getCaseId());
        labelLog.setCaseLabelId(labelId);
        labelLog.setOperateId(queryParam.getUserId());
        labelLog.setOperateSource(queryParam.getSource());
        labelLog.setOperateType(operateType);
        labelLog.setOrganization(orgName);

        return labelLog;
    }

    @Override
    public boolean selectHasLabelContent(int caseId, String content) {
        List<CfOperatingProfileSettings> profileSettings = selectCaseLabels(caseId);

        for (CfOperatingProfileSettings setting : profileSettings) {
            if (Objects.equals(setting.getContent(), content)) {
                return true;
            }
        }

        return false;
    }

}
