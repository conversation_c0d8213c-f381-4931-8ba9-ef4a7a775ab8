package com.shuidihuzhu.cf.service.crowdfunding;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.delegate.other.IMiniAppDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CfMinaQuizShowTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfMinaQuizTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.MsgRecordConstants;
import com.shuidihuzhu.cf.model.Pagination;
import com.shuidihuzhu.cf.model.mina.*;
import com.shuidihuzhu.cf.vo.mina.*;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.msg.enums.RecordConstant;
import com.shuidihuzhu.wx.enums.AccountThirdTypeEnum;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by dongcf on 2018/1/29
 */
@Service
@ConfigurationProperties("cf-mina-quiz")
public class CfMinaQuizConfigService {

    private static final String APP_ID = "wx78ac35c026decfcd";

    private static final String[] OPTION_SEQUENCE = {"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K"};

    @Setter
    private String whiteList;

    @Autowired
    private IMiniAppDelegate miniAppDelegate;

    /**
     * 获取测试列表
     */
    public Map<String, Object> findQuizList(String title, Integer current, Integer pageSize) {
        Map<String, Object> result = Maps.newHashMap();
        int offset = (current - 1) * pageSize;
        List<CfMinaQuiz> cfMinaQuizList = this.miniAppDelegate.findCfMinaQuizListByTitle(title, offset, pageSize);
        if (CollectionUtils.isEmpty(cfMinaQuizList)) {
            result.put("pagination", new Pagination(current, pageSize, 0));
            result.put("minaQuizList", Collections.EMPTY_LIST);
        }
        Integer total = this.miniAppDelegate.countByTitle(title);
        List<CfMinaQuizVo> cfMinaQuizVoList = Lists.newArrayList();
        for (CfMinaQuiz quiz : cfMinaQuizList) {
            CfMinaQuizVo vo = new CfMinaQuizVo();
            vo.setId(quiz.getId());
            vo.setTitle(quiz.getTitle());
            vo.setTitleImgUrl(quiz.getTitleImgUrl());
            vo.setShowType(quiz.getShowType());
            vo.setShowTypeDesc(CfMinaQuizShowTypeEnum.getByType(quiz.getShowType()).getDesc());
            vo.setQuizType(quiz.getQuizType());
            vo.setQuizTypeDesc(CfMinaQuizTypeEnum.getByType(quiz.getQuizType()).getDesc());
            vo.setReleaseTime(quiz.getReleaseTime());
            vo.setReleaseTimeStr(DateUtil.formatDateTime(quiz.getReleaseTime()));
            vo.setSort(quiz.getSort());
            vo.setNew(quiz.isNew());
            cfMinaQuizVoList.add(vo);
        }
        result.put("pagination", new Pagination(current, pageSize, total));
        result.put("minaQuizList", cfMinaQuizVoList);
        return result;
    }

    /**
     * 保存题目及答案
     */
    public int saveQuiz(CfMinaQuizFormVo cfMinaQuizFormVo) {
        if (cfMinaQuizFormVo == null) {
            return 0;
        }
        CfMinaQuiz cfMinaQuiz = new CfMinaQuiz();
        cfMinaQuiz.setAppId("wx78ac35c026decfcd");
        cfMinaQuiz.setTitle(cfMinaQuizFormVo.getTitle());
        cfMinaQuiz.setTitleImgUrl(cfMinaQuizFormVo.getTitleImgUrl());
        cfMinaQuiz.setQuizType(cfMinaQuizFormVo.getQuizType());
        cfMinaQuiz.setShareTitle(cfMinaQuizFormVo.getShareTitle());
        cfMinaQuiz.setShareImgUrl(cfMinaQuizFormVo.getShareImgUrl());
        cfMinaQuiz.setResultDefaultImgUrl(cfMinaQuizFormVo.getResultDefaultImgUrl());
        cfMinaQuiz.setResultDefaultShareImgUrl(cfMinaQuizFormVo.getResultDefaultShareImgUrl());
        cfMinaQuiz.setIsDelete(1);//默认插入无效
        if (cfMinaQuizFormVo.getId() != null) {
            CfMinaQuiz checkMinaQuiz = this.miniAppDelegate.getCfMinaQuizById(cfMinaQuizFormVo.getId());
            if (checkMinaQuiz != null) {
                cfMinaQuiz.setId(cfMinaQuizFormVo.getId());
                this.miniAppDelegate.updateQuizInfoById(cfMinaQuiz);
                //把答案，问题，结果老数据置为无效
                List<CfMinaQuizQuestion> questionList = this.miniAppDelegate.findQuestion(cfMinaQuiz.getId());
                if (CollectionUtils.isNotEmpty(questionList)) {
                    List<Integer> questionIds = Lists.newArrayList();
                    for (CfMinaQuizQuestion question : questionList) {
                        questionIds.add(question.getId());
                    }
                    this.miniAppDelegate.updateAnswerDeleteByQuestionIds(1, questionIds);
                }
                this.miniAppDelegate.updateQuestionDeleteByMinaQuizId(1, cfMinaQuiz.getId());
                this.miniAppDelegate.updateResultDeleteByMinaQuizId(1, cfMinaQuiz.getId());
            } else {
                this.miniAppDelegate.saveMinaQuiz(cfMinaQuiz);
            }
        } else {
            this.miniAppDelegate.saveMinaQuiz(cfMinaQuiz);
        }
        if (cfMinaQuiz.getId() > 0) {
            List<CfMinaQuizQuestionFormVo> questionFormVoList = cfMinaQuizFormVo.getCfMinaQuizQuestionFormVoList();
            for (CfMinaQuizQuestionFormVo vo : questionFormVoList) {
                CfMinaQuizQuestion question = new CfMinaQuizQuestion();
                question.setMinaQuizId(cfMinaQuiz.getId());
                question.setQuestion(vo.getQuestion());
                question.setQuestionImgUrl("");
                this.miniAppDelegate.saveMinaQuizQuestion(question);
                if (question.getId() > 0) {
                    List<CfMinaQuizAnswerFormVo> answerFormVoList = vo.getCfMinaQuizAnswerFormVoList();
                    if (CollectionUtils.isNotEmpty(answerFormVoList)) {
                        List<CfMinaQuizAnswer> answerList = Lists.newArrayList();
                        for (int i = 0; i < answerFormVoList.size(); i++) {
                            CfMinaQuizAnswerFormVo answerVo = answerFormVoList.get(i);
                            CfMinaQuizAnswer answer = new CfMinaQuizAnswer();
                            answer.setMinaQuizQuestionId(question.getId());
                            answer.setOptionSequence(OPTION_SEQUENCE[i]);
                            answer.setAnswer(answerVo.getAnswer());
                            answer.setScore(answerVo.getScore());
                            answerList.add(answer);
                        }
                        this.miniAppDelegate.saveMinaQuizAnswerBatch(answerList);
                    }
                }
            }

            List<CfMinaQuizResultFormVo> cfMinaQuizResultFormVoList = cfMinaQuizFormVo.getCfMinaQuizResultFormVoList();
            if (CollectionUtils.isNotEmpty(cfMinaQuizResultFormVoList)) {
                for (CfMinaQuizResultFormVo vo : cfMinaQuizResultFormVoList) {
                    CfMinaQuizResult result = new CfMinaQuizResult();
                    result.setBeginScore(vo.getBeginScore());
                    result.setEndScore(vo.getEndScore() + 1);
                    result.setMinaQuizId(cfMinaQuiz.getId());
                    if (StringUtils.isNotBlank(vo.getImgUrl())) {
                        result.setImgUrl(vo.getImgUrl());
                    } else if (StringUtils.isNotBlank(cfMinaQuizFormVo.getResultDefaultImgUrl())) {
                        result.setImgUrl(cfMinaQuizFormVo.getResultDefaultImgUrl());
                    } else {
                        result.setImgUrl("");
                    }
                    if (StringUtils.isNotBlank(vo.getShareImgUrl())) {
                        result.setShareImgUrl(vo.getShareImgUrl());
                    } else if (StringUtils.isNotBlank(cfMinaQuizFormVo.getResultDefaultShareImgUrl())) {
                        result.setShareImgUrl(cfMinaQuizFormVo.getResultDefaultShareImgUrl());
                    } else {
                        result.setShareImgUrl("");
                    }
                    result.setResult(vo.getResult());
                    result.setScorePrefix(vo.getScorePrefix());
                    result.setScoreSuffix(vo.getScoreSuffix());
                    this.miniAppDelegate.saveQuizResult(result);

                    if (CollectionUtils.isNotEmpty(vo.getCfMinaQuizResultConfigVoList())) {
                        for (CfMinaQuizResultConfigVo configVo : vo.getCfMinaQuizResultConfigVoList()) {
                            CfMinaQuizResultConfig config = new CfMinaQuizResultConfig();
                            config.setMinaQuizResultId(result.getId());
                            config.setButtonText(configVo.getButtonText());
                            config.setButtonStyle("");
                            config.setActionType(configVo.getActionType());
                            config.setActionUrl(configVo.getActionUrl());
                            config.setAppId(configVo.getAppId());
                            this.miniAppDelegate.insertResultConfig(config);
                        }
                    }
                }
            }
        }
        return 1;
    }

    /**
     * 更新测试记录状态
     */
    public Map<String, Object> modifyStatus(Integer minaQuizId, Integer showType, Integer isNew, Integer sort, String relaseTimeStr) {
        Map<String, Object> result = Maps.newHashMap();
        result.put("code", AdminErrorCode.SUCCESS.getCode());
        result.put("msg", "操作成功");
        CfMinaQuiz cfMinaQuiz = this.miniAppDelegate.getCfMinaQuizById(minaQuizId);
        if (cfMinaQuiz == null) {
            result.put("code", AdminErrorCode.SYSTEM_PARAM_ERROR.getCode());
            result.put("msg", AdminErrorCode.SYSTEM_PARAM_ERROR.getMsg());
            return result;
        }
        if (CfMinaQuizShowTypeEnum.SELECT_LIST == CfMinaQuizShowTypeEnum.getByType(showType)) {
            if (isNew == 1) {// 1:为新标
                if (sort < 1 || sort > 5) {
                    result.put("code", AdminErrorCode.SYSTEM_PARAM_ERROR.getCode());
                    result.put("msg", "置顶排序范围请选择1～5");
                    return result;
                }
                CfMinaQuiz chectSort = this.miniAppDelegate.getBySortAndIsNew(isNew, sort);
                if (chectSort != null) {
                    result.put("code", AdminErrorCode.SUCCESS.getCode());
                    result.put("msg", "注意：测试发布后，会替换目前排序位置相同的测试");
                    this.miniAppDelegate.updateIsNewById(0, chectSort.getId());
                    this.miniAppDelegate.updateSortById(0, chectSort.getId());
                }
            } else {
                sort = 0;
            }
            this.miniAppDelegate.updateIsNewById(isNew, minaQuizId);
            this.miniAppDelegate.updateSortById(sort, minaQuizId);
            if (StringUtils.isNotBlank(relaseTimeStr)) {
                Date releaseTime = DateUtil.parseDateTime(relaseTimeStr);
                this.miniAppDelegate.updateReleaseTimeById(releaseTime, minaQuizId);
            }
            this.miniAppDelegate.updateShowTypeById(showType, minaQuizId);
            this.miniAppDelegate.updateIsDeleteById(1, minaQuizId);//置为无效记录，待到发布时间时，更新为有效记录

            return result;
        } else if (CfMinaQuizShowTypeEnum.SOLD_OUT == CfMinaQuizShowTypeEnum.getByType(showType)) {
            this.miniAppDelegate.updateShowTypeById(showType, minaQuizId);
            this.miniAppDelegate.updateIsDeleteById(1, minaQuizId);//置为无效记录
        } else {
            this.miniAppDelegate.updateShowTypeById(showType, minaQuizId);
            this.miniAppDelegate.updateIsDeleteById(0, minaQuizId);
        }
        return result;
    }

    /**
     * 组装小测试信息
     *
     * @param minaQuizId
     * @return
     */
    public CfMinaQuizFormVo getQuizInfo(Integer minaQuizId) {
        CfMinaQuiz cfMinaQuiz = this.miniAppDelegate.getCfMinaQuizById(minaQuizId);
        if (cfMinaQuiz == null) {
            return null;
        }
        CfMinaQuizFormVo cfMinaQuizFormVo = new CfMinaQuizFormVo();
        cfMinaQuizFormVo.setId(cfMinaQuiz.getId());
        cfMinaQuizFormVo.setId(cfMinaQuiz.getId());
        cfMinaQuizFormVo.setTitle(cfMinaQuiz.getTitle());
        cfMinaQuizFormVo.setTitleImgUrl(cfMinaQuiz.getTitleImgUrl());
        cfMinaQuizFormVo.setQuizType(cfMinaQuiz.getQuizType());
        cfMinaQuizFormVo.setShareTitle(cfMinaQuiz.getShareTitle());
        cfMinaQuizFormVo.setShareImgUrl(cfMinaQuiz.getShareImgUrl());
        cfMinaQuizFormVo.setResultDefaultImgUrl(cfMinaQuiz.getResultDefaultImgUrl());
        cfMinaQuizFormVo.setResultDefaultShareImgUrl(cfMinaQuiz.getResultDefaultShareImgUrl());

        List<CfMinaQuizQuestion> questionList = this.miniAppDelegate.findQuestion(minaQuizId);
        //封装问题与答案
        if (CollectionUtils.isNotEmpty(questionList)) {
            Map<Integer, List<CfMinaQuizAnswer>> answerGroupMap = Maps.newHashMap();
            List<Integer> questionIds = Lists.newArrayList();
            for (CfMinaQuizQuestion question : questionList) {
                questionIds.add(question.getId());
            }
            List<CfMinaQuizAnswer> answerList = this.miniAppDelegate.findAnswerByQuestionIds(questionIds);
            if (CollectionUtils.isNotEmpty(answerList)) {
                for (CfMinaQuizAnswer answer : answerList) {
                    Integer questionId = answer.getMinaQuizQuestionId();
                    if (answerGroupMap.containsKey(questionId)) {
                        answerGroupMap.get(questionId).add(answer);
                    } else {
                        List<CfMinaQuizAnswer> list = Lists.newArrayList();
                        list.add(answer);
                        answerGroupMap.put(questionId, list);
                    }
                }
            }
            List<CfMinaQuizQuestionFormVo> questionFormVoList = Lists.newArrayList();
            for (CfMinaQuizQuestion question : questionList) {
                CfMinaQuizQuestionFormVo questionFormVo = new CfMinaQuizQuestionFormVo();
                questionFormVo.setQuestion(question.getQuestion());
                List<CfMinaQuizAnswer> answers = answerGroupMap.get(question.getId());
                if (CollectionUtils.isNotEmpty(answers)) {
                    List<CfMinaQuizAnswerFormVo> answerFormVoList = Lists.newArrayList();
                    for (CfMinaQuizAnswer answer : answers) {
                        CfMinaQuizAnswerFormVo answerFormVo = new CfMinaQuizAnswerFormVo();
                        answerFormVo.setAnswer(answer.getAnswer());
                        answerFormVo.setScore(answer.getScore());
                        answerFormVoList.add(answerFormVo);
                    }
                    questionFormVo.setCfMinaQuizAnswerFormVoList(answerFormVoList);
                }
                questionFormVoList.add(questionFormVo);
            }
            cfMinaQuizFormVo.setCfMinaQuizQuestionFormVoList(questionFormVoList);
        }

        //封装每个结果的操作配置
        List<CfMinaQuizResult> quizResultList = this.miniAppDelegate.findResultByQuizId(minaQuizId);
        if (CollectionUtils.isNotEmpty(quizResultList)) {
            List<Integer> quizResultIds = Lists.newArrayList();
            for (CfMinaQuizResult result : quizResultList) {
                quizResultIds.add(result.getId());
            }
            Map<Integer, List<CfMinaQuizResultConfig>> resultConfigGroupMap = Maps.newHashMap();
            List<CfMinaQuizResultConfig> resultConfigList = this.miniAppDelegate.findResultConfigByResultIds(quizResultIds);
            if (CollectionUtils.isNotEmpty(resultConfigList)) {
                for (CfMinaQuizResultConfig config : resultConfigList) {
                    Integer quizResultId = config.getMinaQuizResultId();
                    if (resultConfigGroupMap.containsKey(quizResultId)) {
                        resultConfigGroupMap.get(quizResultId).add(config);
                    } else {
                        List<CfMinaQuizResultConfig> list = Lists.newArrayList();
                        list.add(config);
                        resultConfigGroupMap.put(quizResultId, list);
                    }
                }
            }
            List<CfMinaQuizResultFormVo> resultFormVoList = Lists.newArrayList();
            for (CfMinaQuizResult result : quizResultList) {
                CfMinaQuizResultFormVo resultFormVo = new CfMinaQuizResultFormVo();
                resultFormVo.setMinaQuizId(result.getMinaQuizId());
                resultFormVo.setBeginScore(result.getBeginScore());
                resultFormVo.setEndScore(result.getEndScore() - 1);
                resultFormVo.setScorePrefix(result.getScorePrefix());
                resultFormVo.setScoreSuffix(result.getScoreSuffix());
                resultFormVo.setImgUrl(result.getImgUrl());
                resultFormVo.setShareImgUrl(result.getShareImgUrl());
                resultFormVo.setResult(result.getResult());
                //封装每个答案的操作
                List<CfMinaQuizResultConfig> resultConfigs = resultConfigGroupMap.get(result.getId());
                if (CollectionUtils.isNotEmpty(resultConfigs)) {
                    List<CfMinaQuizResultConfigVo> resultConfigVoList = Lists.newArrayList();
                    for (CfMinaQuizResultConfig config : resultConfigs){
                        CfMinaQuizResultConfigVo configVo = new CfMinaQuizResultConfigVo();
                        configVo.setMinaQuizResultId(config.getMinaQuizResultId());
                        configVo.setButtonText(config.getButtonText());
                        configVo.setActionType(config.getActionType());
                        configVo.setActionUrl(config.getActionUrl());
                        configVo.setAppId(config.getAppId());
                        resultConfigVoList.add(configVo);
                    }
                    resultFormVo.setCfMinaQuizResultConfigVoList(resultConfigVoList);
                }
                resultFormVoList.add(resultFormVo);
            }
            cfMinaQuizFormVo.setCfMinaQuizResultFormVoList(resultFormVoList);
        }
        return cfMinaQuizFormVo;
    }


    public Map<String, Object> findMessageConfig(Integer current, Integer pageSize) {
        Map<String, Object> result = Maps.newHashMap();
        int offset = (current - 1) * pageSize;
        List<CfMinaQuizPushConfig> pushConfigList = this.miniAppDelegate.findPushConfig(offset, pageSize);
        if (CollectionUtils.isEmpty(pushConfigList)) {
            result.put("pagination", new Pagination(current, pageSize, 0));
            result.put("configVoList", Collections.EMPTY_LIST);
            return result;
        }
        List<CfMinaQuizPushConfigVo> configVoList = Lists.newArrayList();
        Integer total = this.miniAppDelegate.countAllPushConfig();
        for (CfMinaQuizPushConfig config : pushConfigList) {
            CfMinaQuizPushConfigVo configVo = new CfMinaQuizPushConfigVo();
            configVo.setId(config.getId());
            if (StringUtils.isNotBlank(config.getTemplateKeywordOne())) {
                JSONObject jsonObject = JSONObject.parseObject(config.getTemplateKeywordOne());//已检查过
                configVo.setKeywordOne(jsonObject.get("value").toString());
            }
            configVo.setStatus(config.getStatus());
            if (config.getStatus().intValue() == 0) {
                configVo.setSendTime(DateUtil.formatDateTime(config.getUpdateTime()));
            }
            configVoList.add(configVo);
        }
        result.put("pagination", new Pagination(current, pageSize, total));
        result.put("configVoList", configVoList);
        return result;
    }


    /**
     * @return
     */
    public Map<String, Object> saveMessage(Integer pushConfigId, String templateId, String templateUrl, String pushTarget, Integer emphasisKeyword, String keywordFirst,
                                           String keywordOne, String keywordTwo, String keywordThree, String keywordFour, String keywordFive, String keywordSix,
                                           String keywordRemark, Integer minaQuizId, String customUrl) {
        Map<String, Object> result = Maps.newHashMap();
        result.put("code", AdminErrorCode.SUCCESS.getCode());
        result.put("msg", "操作成功");
        CfMinaQuizPushConfig pushConfig = new CfMinaQuizPushConfig();
        pushConfig.setBusinessType(RecordConstant.BIZ_TYPE_WX_APP_AIXINCHOU);
        pushConfig.setPushTarget(pushTarget);
        pushConfig.setUserThirdType(AccountThirdTypeEnum.NEIHAN_TEST_SP.getCode());
        pushConfig.setSubBusinessType(MsgRecordConstants.SUB_BIZ_TYPE_MINA_QUIZ);
        pushConfig.setTemplateId(templateId);
        pushConfig.setTemplateName("小测试更新通知");
        if (StringUtils.isBlank(templateUrl)) {
            pushConfig.setTemplateUrl("pages/questionTemp?questionId=" + minaQuizId);
        } else {
            pushConfig.setTemplateUrl(templateUrl);
        }
        pushConfig.setCustomUrl(customUrl);
        pushConfig.setTemplateFirst(keywordFirst);
        pushConfig.setTemplateKeywordOne(keywordOne);
        pushConfig.setTemplateKeywordTwo(keywordTwo);
        pushConfig.setTemplateKeywordThree(keywordThree);
        pushConfig.setTemplateKeywordFour(keywordFour);
        pushConfig.setTemplateKeywordFive(keywordFive);
        pushConfig.setTemplateKeywordSix(keywordSix);
        pushConfig.setTemplateRemark(keywordRemark);
        pushConfig.setAppEmphasisKeyword(emphasisKeyword);
        pushConfig.setMinaQuizId(minaQuizId);
        pushConfig.setMiniProgram("");
        pushConfig.setStatus(0);//未发送
        pushConfig.setVisitTimes(0);


        if (pushConfigId != null && pushConfigId.intValue() > 0) {
            CfMinaQuizPushConfig checkPushConfig = this.miniAppDelegate.getPushConfigById(pushConfigId);
            if (checkPushConfig != null) {
                if (pushConfig.getStatus().intValue() == 1) {
                    result.put("code", AdminErrorCode.SYSTEM_PARAM_ERROR.getCode());
                    result.put("msg", "已发送的消息不可编辑");
                    return result;
                }
                pushConfig.setId(pushConfigId);
                this.miniAppDelegate.updatePushConfigById(pushConfig);
                return result;
            }
        }
        this.miniAppDelegate.insertPushConfig(pushConfig);
        return result;
    }

    public CfMinaQuizPushConfigVo getMessageConfig(Integer pushConfigId) {
        CfMinaQuizPushConfig pushConfig = this.miniAppDelegate.getPushConfigById(pushConfigId);
        if (pushConfig == null) {
            return null;
        }
        CfMinaQuizPushConfigVo configVo = new CfMinaQuizPushConfigVo();
        configVo.setId(pushConfig.getId());
        configVo.setTemplateId(pushConfig.getTemplateId());
        configVo.setTemplateUrl("");//依需求，暂定返回空，保存时给默认值
        configVo.setPushTarget(pushConfig.getPushTarget());
        configVo.setEmphasisKeyword(pushConfig.getAppEmphasisKeyword());
        configVo.setKeywordFirst(pushConfig.getTemplateFirst());
        configVo.setKeywordOne(pushConfig.getTemplateKeywordOne());
        configVo.setKeywordTwo(pushConfig.getTemplateKeywordTwo());
        configVo.setKeywordThree(pushConfig.getTemplateKeywordThree());
        configVo.setKeywordFour(pushConfig.getTemplateKeywordFour());
        configVo.setKeywordFive(pushConfig.getTemplateKeywordFive());
        configVo.setKeywordSix(pushConfig.getTemplateKeywordSix());
        configVo.setKeywordRemark(pushConfig.getTemplateRemark());
        configVo.setCustomUrl(pushConfig.getCustomUrl());
        configVo.setMinaQuizId(pushConfig.getMinaQuizId());

        return configVo;
    }
}
