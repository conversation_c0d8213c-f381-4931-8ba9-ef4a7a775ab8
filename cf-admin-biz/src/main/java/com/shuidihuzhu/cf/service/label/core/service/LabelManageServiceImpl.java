package com.shuidihuzhu.cf.service.label.core.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.admin.util.NanoIdUtils;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonRecord;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonRecordClient;
import com.shuidihuzhu.cf.dao.labels.core.LabelDAO;
import com.shuidihuzhu.cf.domain.label.core.LabelConst;
import com.shuidihuzhu.cf.domain.label.core.LabelDO;
import com.shuidihuzhu.cf.domain.label.core.LabelNodeModel;
import com.shuidihuzhu.cf.domain.label.risk.Label;
import com.shuidihuzhu.cf.service.label.risk.LabelMapper;
import com.shuidihuzhu.cf.service.label.risk.RiskLabelService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class LabelManageServiceImpl implements LabelManageService {

    @Autowired
    private LabelService labelService;

    @Autowired
    private RiskLabelService riskLabelService;

    @Resource
    private LabelDAO labelDAO;

    @Autowired
    private WonRecordClient wonRecordClient;

    private static final String PATH_NAME_SPLIT = "/";
    private static final String PATH_ID_SPLIT = ",";

    private List<Label> getEnableChildListById(long id) {
        return LabelMapper.INSTANCE.toLabelList(labelDAO.getEnableChildListById(id));
    }

    private List<Label> getChildListByIdAndLabelStatus(long id, List<Integer> labelStatusList) {
        return LabelMapper.INSTANCE.toLabelList(labelDAO.getChildListByIdAndStatus(id, labelStatusList));
    }

    @Override
    public Response<Long> add(long parentId, String name, String labelDesc, int seq, int riskLevel, long operatorId) {
        // 重排序空出目标seq 不关心重排是否成功
        reSort(parentId, seq);

        // 检查重名
        final List<Label> allShowChildList = getChildListByIdAndLabelStatus(parentId, LabelConst.LabelStatusEnum.getAllShowList());
        final Set<String> childNameSet = allShowChildList.stream().map(Label::getName).collect(Collectors.toSet());
        if (childNameSet.contains(name)) {
            return NewResponseUtil.makeFail("当前层级风险标签存在重复项");
        }

        final Label parent = getLabelById(parentId);
        final LabelDO labelDO = new LabelDO();
        labelDO.setUuid(NanoIdUtils.randomNanoId());
        labelDO.setName(StringUtils.defaultString(name));
        labelDO.setPathName(buildParentNamePath(parent));
        labelDO.setLabelDesc(StringUtils.defaultString(labelDesc));
        labelDO.setSeq(seq);
        labelDO.setLabelStatus(LabelConst.LabelStatusEnum.INIT.getValue());
        labelDO.setRiskLevel(riskLevel);
        labelDO.setParentId(parentId);
        labelDO.setParentPath(buildParentIdPath(parent));
        labelDAO.add(labelDO);
        saveOperateRecord(labelDO.getId(), operatorId, "新增");
        return NewResponseUtil.makeSuccess(labelDO.getId());
    }

    private Label getLabelById(long id) {
        return labelService.getLabelById(id);
    }

    @NotNull
    private String buildParentNamePath(Label parent) {
        if (parent == null) {
            return PATH_NAME_SPLIT;
        }
        final String parentPathName = parent.getPathName();
        if (StringUtils.isEmpty(parentPathName)) {
            return parent.getName() + PATH_NAME_SPLIT;
        }
        return parentPathName + parent.getName() + PATH_NAME_SPLIT;
    }

    @NotNull
    private String buildParentIdPath(Label parent) {
        if (parent == null) {
            return PATH_ID_SPLIT;
        }
        final String parentParentPath = parent.getParentPath();
        if (StringUtils.isEmpty(parentParentPath)) {
            return parent.getId() + PATH_ID_SPLIT;
        }
        return parentParentPath + parent.getId() + PATH_ID_SPLIT;
    }

    /**
     * 根据目标seq重排序 空出目标seq
     * 若有相同seq标签则顺位后移
     */
    private boolean reSort(long parentId, int targetSeq) {
        final List<Label> lateralList = getChildListByIdAndLabelStatus(parentId, LabelConst.LabelStatusEnum.getAllShowList());
        final List<Label> sortLateralList = lateralList.stream()
                .sorted(Comparator.comparing(Label::getSeq)).collect(Collectors.toList());
        int lastSeq = targetSeq;
        boolean startReSort = false;
        for (Label lateral : sortLateralList) {
            if (lateral.getSeq() == lastSeq) {
                // 代表遍历到了与目标seq相同的标签
                startReSort = true;
            }
            // 这里使用小于等于+开始重排标记判断有自修复效果
            if (lateral.getSeq() <= lastSeq && startReSort) {
                lastSeq = lateral.getSeq() + 1;
                int res = labelDAO.updateSeqById(lateral.getId(), lateral.getSeq(), lastSeq);
                if (res <= 0) {
                    log.error("标签修改seq顺序失败 原数据 {}, 目标seq {}, 同级列表数据 {}", lateral, lastSeq, lateralList);
                    return false;
                }
            }
        }
        return true;
    }

    @Override
    public Response<Void> updateStatusByLabelId(long id, int labelStatus, long operatorId, String reason) {
        final Label label = getLabelById(id);


        Response<Void> validResp = validStatusChange(label, labelStatus);
        if (validResp.notOk()) {
            return NewResponseUtil.makeFail(validResp.getMsg());
        }

        String comment = LabelConst.LabelStatusEnum.getOperateMsg(labelStatus);
        if (labelStatus == LabelConst.LabelStatusEnum.DISABLE.getValue()) {
            comment += "，弃用原因:" + reason;
        }
        saveOperateRecord(id, operatorId, comment);

        // 修改状态
        int res = labelDAO.updateStatusByLabelId(id, label.getLabelStatus(), labelStatus);
        if (res > 0) {
            return NewResponseUtil.makeSuccess();
        } else {
            return NewResponseUtil.makeFail("修改不成功");
        }
    }

    private Response<Void> validStatusChange(Label label, int targetLabelStatus) {
        final boolean validate = LabelStatusMachine.validate(label.getLabelStatus(), targetLabelStatus);
        if (!validate) {
            return NewResponseUtil.makeFail("状态机校验不通过");
        }

        // 如果启用要校验上级已启用
        if (targetLabelStatus == LabelConst.LabelStatusEnum.ENABLE.getValue()) {
            final Label parentLabel = getLabelById(label.getParentId());
            if (parentLabel.getLabelStatus() != LabelConst.LabelStatusEnum.ENABLE.getValue()) {
                return NewResponseUtil.makeFail("请先启用上级风险标签");
            }
            return NewResponseUtil.makeSuccess();
        }

        /*
        - 点击弃用时，系统需要判断
  - 是否下级的风险标签还未弃用，如果是，则弹框提示：请先弃用下级的风险标签
  - 该风险标签是否关联了驳回项，如果是，则弹框提示：该风险标签已关联了驳回项，暂无法弃用。
  - 如果以上两个判断均为否，则弹出框，填写弃用原因，必填，字符限制200个字。
         */
        final List<Label> childLabel = getChildListByIdAndLabelStatus(label.getId(), LabelConst.LabelStatusEnum.getAllShowList());
        if (targetLabelStatus == LabelConst.LabelStatusEnum.DISABLE.getValue()) {
            final Label enableLabel = childLabel.stream()
                    .filter(v -> v.getLabelStatus() == LabelConst.LabelStatusEnum.ENABLE.getValue())
                    .findAny()
                    .orElse(null);
            if (enableLabel != null) {
                return NewResponseUtil.makeFail("请先弃用下级的风险标签");
            }
            final boolean related = riskLabelService.relatedRefuseEntity(label.getId());
            if (related) {
                return NewResponseUtil.makeFail("该风险标签已关联了驳回项，暂无法弃用。");
            }
        }

        /*
        - 需要先保证下级的风险标签已删除，然后才可以删除上一级的风险标签
- 如果下级的标签未删除，操作上一级标签删除时，弹框提示：请先删除下级的风险标签
         */
        if (targetLabelStatus == LabelConst.LabelStatusEnum.REMOVE.getValue()) {
            final HashSet<Integer> allShowStatusSet = Sets.newHashSet(LabelConst.LabelStatusEnum.getAllShowList());
            final Label enableLabel = childLabel.stream()
                    .filter(v -> allShowStatusSet.contains(v.getLabelStatus()))
                    .findAny()
                    .orElse(null);
            if (enableLabel != null) {
                return NewResponseUtil.makeFail("请先删除下级的风险标签");
            }
        }

        return NewResponseUtil.makeSuccess();
    }

    @Override
    public Response<Void> updateLabelDescByLabelId(long id, String labelDesc, long operatorId) {
        final Label label = getLabelById(id);
        labelDAO.updateLabelDesc(id, labelDesc);
        saveOperateRecord(id, operatorId, "编辑场景说明：由'" + label.getLabelDesc() + "'改为'" + labelDesc + "'");
        return NewResponseUtil.makeSuccess();
    }

    @Override
    public Response<Void> updateLabelSeqByLabelId(long id, int seq, long operatorId) {
        final Label label = getLabelById(id);
        reSort(label.getParentId(), seq);
        labelDAO.updateSeqById(id, label.getSeq(), seq);
        String comment = seq > label.getSeq() ? "下移" : "上移";
        saveOperateRecord(id, operatorId, comment);
        return NewResponseUtil.makeSuccess();
    }

    @Override
    public Response<LabelNodeModel> searchLabel(long rootId, String nameKey, Integer riskLevel, Integer labelStatus) {
        final Label rootLabel = getLabelById(rootId);
        if (rootLabel == null) {
            return NewResponseUtil.makeFail("找不到对应的根节点");
        }
        String parentPathKey = buildParentIdPath(rootLabel);
        ArrayList<Integer> labelStatusList;
        if (labelStatus == null) {
            labelStatusList = Lists.newArrayList(
                    LabelConst.LabelStatusEnum.INIT.getValue(),
                    LabelConst.LabelStatusEnum.ENABLE.getValue(),
                    LabelConst.LabelStatusEnum.DISABLE.getValue()
            );
        } else {
            labelStatusList = Lists.newArrayList(labelStatus);
        }
        List<LabelDO> list = labelDAO.searchLabel(parentPathKey, nameKey, labelStatusList, riskLevel);
        final Set<LabelNodeModel> rootNodeSet = buildRootNodeSetByLabelList(LabelMapper.INSTANCE.toLabelList(list));
        if (CollectionUtils.size(rootNodeSet) > 1) {
            log.error("查询数据异常 {}", rootNodeSet);
            return NewResponseUtil.makeFail("查询数据异常");
        }
        return NewResponseUtil.makeSuccess(rootNodeSet.stream().findFirst().orElse(null));
    }

    @Override
    public Response<List<WonRecord>> getOperateRecordById(long id) {
        final OperationResult<List<WonRecord>> resp = wonRecordClient.listByBizId(id, 113L);
        return NewResponseUtil.makeSuccess(resp.getData());
    }

    @Override
    public Response<Void> validLabelStatusChange(long id, int labelStatus) {
        final Label label = getLabelById(id);
        return validStatusChange(label, labelStatus);
    }

    private void saveOperateRecord(long id, long operatorId, String comment){
        wonRecordClient.create()
                .buildBasic(id, 113L)
                .buildRemark(comment)
                .buildOperatorId((int) operatorId)
                .save();
    }

    private Set<LabelNodeModel> buildRootNodeSetByLabelList(List<Label> list) {
        Map<Long, LabelNodeModel> nodeMap = new HashMap<>();

        // Create the nodes for each label in the list
        // 创建每一个条目的node
        Set<LabelNodeModel> labelNodeSet = Sets.newHashSet();
        for (Label label : list) {
            final LabelNodeModel node = new LabelNodeModel();
            node.setLabel(label);
            node.setChildLabelNodeList(Lists.newArrayList());
            nodeMap.put(label.getId(), node);
            labelNodeSet.add(node);
        }
        final Set<Long> rooIdSet = buildNodeSetByLabelSet(labelNodeSet, nodeMap, Sets.newHashSet());
        return rooIdSet.stream().map(nodeMap::get).collect(Collectors.toSet());
    }

    private Set<Long> buildNodeSetByLabelSet(Set<LabelNodeModel> nodeSet,
                                             Map<Long, LabelNodeModel> nodeMap,
                                             Set<Long> rootIdSet) {
        // 遍历条目node创建对应父node到map中 去重
        // map key为labelId value为对应的node数据
        Set<LabelNodeModel> continueFindParentSet = Sets.newHashSet();
        for (LabelNodeModel node : nodeSet) {
            final Label label = node.getLabel();
            final long parentId = label.getParentId();
            if (parentId <= 0) {
                rootIdSet.add(label.getId());
                continue;
            }
            LabelNodeModel parentNode = nodeMap.get(parentId);
            if (parentNode == null) {
                final Label parentLabel = getLabelById(parentId);
                parentNode = new LabelNodeModel();
                parentNode.setChildLabelNodeList(Lists.newArrayList(node));
                parentNode.setLabel(parentLabel);
                nodeMap.put(parentLabel.getId(), parentNode);
            } else {
                if (!parentNode.getChildLabelNodeList().contains(node)){
                    parentNode.getChildLabelNodeList().add(node);
                    parentNode.getChildLabelNodeList().sort(Comparator.comparing(v -> v.getLabel().getSeq()));
                }
            }
            continueFindParentSet.add(parentNode);
        }
        // 最终得到多个无父node的node列表返回
        if (CollectionUtils.isEmpty(continueFindParentSet)) {
            return rootIdSet;
        }
        // 递归 遍历条目父node创建对应父父node到map中 去重
        return buildNodeSetByLabelSet(continueFindParentSet, nodeMap, rootIdSet);
    }

}
