package com.shuidihuzhu.cf.service.workorder.delayfinance.impl;

import com.shuidihuzhu.cf.finance.model.vo.drawcash.CfDrawCashApplyV2Vo;
import com.shuidihuzhu.cf.service.workorder.delayfinance.delegate.IFinanceWorkOrderDelegate;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-06-15 3:32 PM
 **/
@Service
public class CfDrawCashApplyFinanceWorkOrderStrategy extends AbstractFinanceWorkOrderStrategy<CfDrawCashApplyV2Vo> {

    @Resource
    private IFinanceWorkOrderDelegate iFinanceWorkOrderDelegate;
    @Override
    public int orderType() {
        return WorkOrderType.draw_cash_apply.getType();
    }

    @Override
    public List<CfDrawCashApplyV2Vo> getBusinessExt(List<Long> financeBusinessIds) {
        return iFinanceWorkOrderDelegate.getDrawCashApplyVo(financeBusinessIds);
    }
}
