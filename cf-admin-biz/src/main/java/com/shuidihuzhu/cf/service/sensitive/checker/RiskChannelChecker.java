package com.shuidihuzhu.cf.service.sensitive.checker;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.enums.risk.UgcTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.admin.AdminPageRecommendCaseService;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordResult;
import com.shuidihuzhu.cf.service.sensitive.adapter.ISensitiveAdapter;
import com.shuidihuzhu.cf.service.sensitive.processor.SensitiveProcessService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2019-04-11  14:46
 */
@Service
public class RiskChannelChecker implements ISensitiveChecker {

    @Resource
    private SensitiveProcessService sensitiveProcessService;

    @Resource
    private AdminPageRecommendCaseService adminPageRecommendCaseService;

    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Override
    public AdminWorkOrderConst.Task getTask() {
        return AdminWorkOrderConst.Task.SENSITIVE_CHANNEL;
    }

    @Override
    public <T> OpResult<RiskWordResult> isHit(T data, ISensitiveAdapter<T> adapter) {
        RiskWordResult r = new RiskWordResult(isPassed(data, adapter), adapter.getContent(data), Lists.newArrayList());
        return OpResult.createSucResult(r);
    }

    private <T> boolean isPassed(T data, ISensitiveAdapter<T> adapter) {
        UgcTypeEnum ugcTypeEnum = adapter.getUgcTypeEnum();
        if (ugcTypeEnum != UgcTypeEnum.VERIFICATION) {
            return true;
        }
        int caseId = adapter.getCaseId(data);
        CfInfoSimpleModel simpleModel = crowdfundingDelegate.getCfInfoSimpleModelById(caseId);
        // 请求失败从严
        if (simpleModel == null) {
            return false;
        }
        boolean inRecommendList = adminPageRecommendCaseService.isCaseInRecommendList(simpleModel.getInfoId());
        return !inRecommendList;
    }
}
