package com.shuidihuzhu.cf.service.health;

import com.google.common.collect.Sets;
import com.shuidihuzhu.eb.grafana.configuration.plugin.health.IRequiredCheckDataSourceService;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * <AUTHOR>
 * @time 2018/12/2 下午4:36
 * @desc
 */
@Service
public class RequiredCheckDataSourceServiceImpl implements IRequiredCheckDataSourceService {
    private static final String CF_DB_MASTER = "crowdfundingDataSource";
    private static final String CF_DB_SLAVE = "crowdfundingSlaveDataSource";
    private static final String ADMIN_DB_MASTER = "shuidiCfAdminDataSource";

    /**
     * 这里面的才是健康检查需要的，其他一律返回up
     * @return
     */
    public static Set<String> requiredDBList(){
        return Sets.newHashSet(CF_DB_MASTER, CF_DB_SLAVE, ADMIN_DB_MASTER);
    }



    @Override
    public Set<String> requiredDbDataSource() {
        return requiredDBList();
    }

    @Override
    public Set<String> requiredRedissonDataSource() {
        return Sets.newHashSet();
    }
}
