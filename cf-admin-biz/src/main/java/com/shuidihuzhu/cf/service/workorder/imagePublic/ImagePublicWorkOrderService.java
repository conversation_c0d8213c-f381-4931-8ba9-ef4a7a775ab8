package com.shuidihuzhu.cf.service.workorder.imagePublic;

import com.shuidihuzhu.alps.feign.ocean.OceanApiMQResponse;
import com.shuidihuzhu.cf.param.workorder.ImagePublicHandleParam;
import com.shuidihuzhu.client.model.event.InfoApproveEvent;
import com.shuidihuzhu.common.web.model.Response;

import java.util.*;

/**
 * @Author: wangpeng
 * @Date: 2021/8/19 11:49
 * @Description:
 */

public interface ImagePublicWorkOrderService {

    void createWorkOrder(InfoApproveEvent infoApproveEvent);

    Response<Void> submitPublicWorkOrder(ImagePublicHandleParam imagePublicHandleParam);

    void autoCloseWorkOrder(int caseId);

    Map<Long, List<String>> getImagePublicByWorKOrderIdList(List<Long> workOrderIdList);

    Map<Long, List<String>> getMarkImagePublicByWorKOrderIdList(List<Long> workOrderIdList);

    void submitAiMarkImage(OceanApiMQResponse apiMQResponse);
}
