package com.shuidihuzhu.cf.service.workorder.initialAudit;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.biz.crowdfunding.CfAdminOperationRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseReasonEntityBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseReasonItemBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseReasonTagBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.CfOperationRecordEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonItem;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonTag;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditOperationItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
@Deprecated
public class InitialAuditRejectSettingsService {

    public static final String ENTITY_SPLIT = ",";

    @Autowired
    private CfRefuseReasonEntityBiz entityBiz;

    @Autowired
    private CfRefuseReasonTagBiz reasonTagBiz;

    @Autowired
    private CfRefuseReasonItemBiz reasonItemBiz;

    @Autowired
    private CfAdminOperationRecordBiz operationRecordBiz;

    // 初次审核 驳回项的 启用 弃用 删除
    public String handleRejectOption(int entityId, int rejectOptionType, int userId) {

        log.info("驳回项配置的状态修改: userId:{}, rejectOptionType:{}, entityId:{}", userId, rejectOptionType, entityId);

        int theEntityId=entityId;
        String errMsg = "";
        CfRefuseReasonEntity rejectOption = entityBiz.selectById(entityId, null);
        if (rejectOption == null) {
            log.error("不能找到驳回子项 entityId:{}",theEntityId);
            errMsg = "不能找到驳回子项";
            return errMsg;
        }

        CfRefuseReasonTag reasonTag = reasonTagBiz.selectByTagId(rejectOption.getTagId());
        if (reasonTag == null) {
            log.error("不能找到第一级驳回项 tagId:{}", rejectOption.getTagId());
            errMsg = "不能找到第一级驳回项";
            return errMsg;
        }

        InitialAuditOperationItem.RejectOperation operation = InitialAuditOperationItem.RejectOperation.codeOf(rejectOptionType);
        if (operation == null) {
            log.error("操作的类型不合法. rejectOptionType:{}", rejectOptionType);
            errMsg = "操作的类型不合法";
            return errMsg;
        }

        if (operation == InitialAuditOperationItem.RejectOperation.DELETE && rejectOption.getFrequency() > 0) {
            log.error("驳回子项已经被使用, 不能被删除 entityId:{}, frequency:{}", theEntityId,
                    rejectOption.getFrequency());
            errMsg = "驳回子项已经被使用, 不能被删除";
            return errMsg;
        }

        entityBiz.updateDeleteStatus(entityId, rejectOptionType);
        fixTagItemIds(reasonTag, "" + entityId, rejectOptionType);

        operationRecordBiz.addOneOperationRecord("", userId, CfOperationRecordEnum.MODIFY_INITIAL_AUDIT_REJECT_OPTION.value(),
                operation.getDesc() + "_" + entityId);

        return errMsg;
    }

    private void fixTagItemIds(CfRefuseReasonTag reasonTag, String entityId, int rejectOptionType) {

        if (rejectOptionType == InitialAuditOperationItem.RejectOperation.DELETE.getCode()) {
            return;
        }

        List<String> reasonIds = Lists.newArrayList();
        if (StringUtils.isNotBlank(reasonTag.getReasonIds())) {
            reasonIds = Lists.newArrayList(Splitter.on(ENTITY_SPLIT).splitToList(reasonTag.getReasonIds()));
        }
        if (rejectOptionType == InitialAuditOperationItem.RejectOperation.ENABLE.getCode() && !reasonIds.contains(entityId)) {
            reasonIds.add(entityId);
        }

        if (rejectOptionType == InitialAuditOperationItem.RejectOperation.DISABLE.getCode()) {
            reasonIds.remove(entityId);
        }

        reasonTagBiz.updateReasonIds(Joiner.on(ENTITY_SPLIT).join(reasonIds), reasonTag.getId());
    }

    public List<CfRefuseReasonEntity> queryRefuseEntity(int tagId, int rejectOptionType) {
        List<CfRefuseReasonEntity> entityList = entityBiz.selectByTagIdAndDeleteStatus(tagId, rejectOptionType);

        if (CollectionUtils.isEmpty(entityList)) {
            return entityList;
        }

        fillReasonItem(entityList);

        // 排序
        if (rejectOptionType == InitialAuditOperationItem.RejectOperation.ENABLE.getCode()) {
            entityList = getSortedReasonEntitys(tagId, entityList);
        }

        return entityList;
    }

    private List<CfRefuseReasonEntity> getSortedReasonEntitys(int tagId, List<CfRefuseReasonEntity> entityList) {
        CfRefuseReasonTag cfRefuseReasonTag = reasonTagBiz.selectByTagId(tagId);
        List<String> reasonIds = Splitter.on(ENTITY_SPLIT).splitToList(cfRefuseReasonTag.getReasonIds());

        List<CfRefuseReasonEntity> sortedEntityList = Lists.newArrayList();
        for (String reasonId : reasonIds) {
            for (CfRefuseReasonEntity entity : entityList) {
                if (reasonId.equals("" + entity.getId())) {
                    sortedEntityList.add(entity);
                    break;
                }
            }
        }

        return sortedEntityList;
    }

    private void fillReasonItem(List<CfRefuseReasonEntity> entityList) {

        if (CollectionUtils.isEmpty(entityList)) {
            return;
        }

        Set<Integer> itemIds = Sets.newHashSet();
        for (CfRefuseReasonEntity reasonEntity : entityList) {
            if (StringUtils.isBlank(reasonEntity.getItemIds())) {
                continue;
            }

            itemIds.addAll(Lists.transform(Splitter.on(ENTITY_SPLIT)
                    .splitToList(reasonEntity.getItemIds()), Integer::parseInt));
        }

        List<CfRefuseReasonItem> reasonItems = reasonItemBiz.selectByIds(itemIds);
        Map<Integer, CfRefuseReasonItem> reasonItemMap = Maps.newHashMap();
        for (CfRefuseReasonItem reasonItem : reasonItems) {
            reasonItemMap.put(reasonItem.getId(), reasonItem);
        }

        for (CfRefuseReasonEntity reasonEntity : entityList) {
            if (StringUtils.isBlank(reasonEntity.getItemIds())) {
                continue;
            }

            reasonEntity.setRefuseReasonItems(Lists.newArrayList());
            List<Integer> items = Lists.transform(Splitter.on(ENTITY_SPLIT).splitToList(reasonEntity.getItemIds()), Integer::parseInt);
            for (Integer id : items) {
                if (reasonItemMap.containsKey(id)) {
                    reasonEntity.getRefuseReasonItems().add(reasonItemMap.get(id).getContent());
                }
            }
        }
    }


    public List<CfRefuseReasonTag> queryDataTypeList(int dataType) {

        List<CfRefuseReasonTag> reasonTags = reasonTagBiz.selectByDataType(dataType);

        for (CfRefuseReasonTag reasonTag : reasonTags) {
            reasonTag.setEntityList(
                    entityBiz.queryRefuseEntityByDelStatus(
                            reasonTag.getId(), 0,
                    CfRefuseReasonEntity.RejectOptionUseSceneEnum.INITIAL_AUDIT.getCode(), true));
//                    queryRefuseEntity(reasonTag.getId(),
//                    InitialAuditOperationItem.RejectOperation.ENABLE.getCode()));
        }

        return reasonTags;
    }


    public void prepareRefuseItem(int type) {

        List<CfRefuseReasonItem> reasonItems = buildRefuseItems(type);
        for (CfRefuseReasonItem item : reasonItems) {
            reasonItemBiz.insertReasonItem(item);
        }

        List<CfRefuseReasonTag> reasonTags = buildReasonTags(type);
        for (CfRefuseReasonTag tag : reasonTags) {
            reasonTagBiz.insertOne(tag);
        }

        buildRefuseEntity(reasonTags, reasonItems);
    }

    private List<CfRefuseReasonItem> buildRefuseItems(int type) {
        List<CfRefuseReasonItem> itemList = Lists.newArrayList();

        CfRefuseReasonItem picItem = new CfRefuseReasonItem();
        picItem.setContent("医疗材料");
        picItem.setType(type);
        picItem.setGroupRank(1);
        picItem.setProType(0);

        CfRefuseReasonItem authorItem = new CfRefuseReasonItem();
        authorItem.setContent("患者信息");
        authorItem.setType(type);
        authorItem.setGroupRank(2);
        authorItem.setProType(0);

        CfRefuseReasonItem povertyItem = new CfRefuseReasonItem();
        povertyItem.setContent("贫困证明");
        povertyItem.setType(type);
        povertyItem.setGroupRank(3);
        povertyItem.setProType(0);

        CfRefuseReasonItem descItem = new CfRefuseReasonItem();
        descItem.setContent("所需金额说明");
        descItem.setType(type);
        descItem.setGroupRank(4);
        descItem.setProType(0);

        CfRefuseReasonItem endCaseItem = new CfRefuseReasonItem();
        endCaseItem.setContent("停止筹款");
        endCaseItem.setType(type);
        endCaseItem.setGroupRank(100);
        endCaseItem.setProType(0);

        itemList.add(picItem);
        itemList.add(authorItem);
        itemList.add(povertyItem);
        itemList.add(descItem);
        itemList.add(endCaseItem);
        return itemList;
    }


    private List<CfRefuseReasonTag> buildReasonTags(int type) {

        List<CfRefuseReasonTag> reasonTags = Lists.newArrayList();

        CfRefuseReasonTag picTag = new CfRefuseReasonTag();
        picTag.setDescribe("医疗材料");
        picTag.setDataStep(1);
        picTag.setDataType(type);
        picTag.setReasonIds("");

        CfRefuseReasonTag authorTag = new CfRefuseReasonTag();
        authorTag.setDescribe("患者信息");
        authorTag.setDataStep(2);
        authorTag.setDataType(type);
        authorTag.setReasonIds("");

        CfRefuseReasonTag povertyTag = new CfRefuseReasonTag();
        povertyTag.setDescribe("贫困证明");
        povertyTag.setDataStep(3);
        povertyTag.setDataType(type);
        povertyTag.setReasonIds("");

        CfRefuseReasonTag descTag = new CfRefuseReasonTag();
        descTag.setDescribe("所需金额说明");
        descTag.setDataStep(4);
        descTag.setDataType(type);
        descTag.setReasonIds("");

        CfRefuseReasonTag endCaseTag = new CfRefuseReasonTag();
        endCaseTag.setDescribe("停止筹款");
        endCaseTag.setDataStep(100);
        endCaseTag.setDataType(type);
        endCaseTag.setReasonIds("");

        reasonTags.add(picTag);
        reasonTags.add(authorTag);
        reasonTags.add(povertyTag);
        reasonTags.add(descTag);
        reasonTags.add(endCaseTag);

        return reasonTags;
    }


    private void buildRefuseEntity(List<CfRefuseReasonTag> reasonTags, List<CfRefuseReasonItem> reasonItems) {

        SETTINGS.put(0, picContents);
        SETTINGS.put(1, authorContents);
        SETTINGS.put(2, povertyContents);
        SETTINGS.put(3, descContents);
        SETTINGS.put(4, endCaseContents);

        for (int i = 0; i < reasonTags.size(); ++i) {

            List<CfRefuseReasonEntity> cfRefuseReasonEntityList = getRefuseEntitys(
                    reasonTags.get(i).getId(), reasonItems.get(i).getId(), SETTINGS.get(i));

            List<Integer> reasonIds = Lists.newArrayList();
            for (CfRefuseReasonEntity insertEntity : cfRefuseReasonEntityList) {
                entityBiz.insertOne(insertEntity);
                reasonIds.add(insertEntity.getId());
            }
            reasonTagBiz.updateReasonIds(Joiner.on(",").join(reasonIds), reasonTags.get(i).getId());
        }
    }

    Map<Integer, List<String>> SETTINGS = Maps.newHashMap();


    List<String> picContents = Lists.newArrayList(
            "您提交医疗材料不符合标准，请您上传：例如，诊断证明、核磁、病理等检查报告、住院证明、病危通知书等其中一张带有患者姓名和疾病名称或者病情描述的医疗材料。",
            "您提交的医疗材料上的患者名字，与基本信息提交填写的患者姓名不一致，请重新上传患者的医疗材料。",
            "您提交的医疗材料上没有患者姓名或患者姓名不清晰，请您重新上传带有患者姓名且字迹清晰的医疗材料。",
            "请重新上传没有水印的医疗材料图片。",

            "您提交的照片不是医疗材料，请您重新上传带有患者姓名的诊断证明、病案首页、住院证明或检查报告等其中一张医疗材料图片。",
            "您提交的医疗材料上没有患者所患疾病信息，请您重新上传带有患者姓名、疾病名称且字迹清晰的医疗材料。",
            "您提交的医疗材料有涂改痕迹，请重新上传带有患者姓名的诊断证明、住院证明、病案首页或检查报告等其中一张医疗材料图片。",
            "您上传的图片不能成功展示，请您重新上传。"
    );

    List<String> authorContents = Lists.newArrayList(
"请重新填写患者信息，与提交的医疗材料患者姓名保持一致。"
    );

    List<String> povertyContents = Lists.newArrayList(
            "请您重新上传患者的建档立卡贫困证明资料");

    List<String> descContents = Lists.newArrayList(
            "请按照实际情况填写您的目标金额原因，需要包含患者疾病名称（或病情）+后续所需金额（需与目标金额一致）+患者家庭经济情况"
    );

    List<String> endCaseContents = Lists.newArrayList(
            "很抱歉，您的筹款不在水滴筹服务范围内，已经给您停止筹款，如有疑问请拨打客服热线400-686-1179",
            "很抱歉，您所患疾病不符合水滴筹大病筹款服务范围内，已经给您停止筹款，如有疑问拨打客服热线400-686-1179",
            "很抱歉，水滴筹目前仅支持大病筹款，您的筹款不在服务范围内，已经给您停止筹款，如有疑问拨打客服热线400-686-1179");

    List<CfRefuseReasonEntity>  getRefuseEntitys(int tagId, int itemId, List<String> contentList) {

        List<CfRefuseReasonEntity> reasonEntityList = Lists.newArrayList();

        for (String content : contentList) {
            CfRefuseReasonEntity reasonEntity = new CfRefuseReasonEntity();
            reasonEntity.setTagId(tagId);
            reasonEntity.setItemIds("" + itemId);
            reasonEntity.setFrequency(0);
            reasonEntity.setContent(content);
            reasonEntity.setSuggestModifyIds("");
            reasonEntityList.add(reasonEntity);
        }

        return reasonEntityList;
    }



    public List<InitialAuditOperationItem.SortedReasonEntity> selectSortEntity(List<Integer> entityIds) {

        List<InitialAuditOperationItem.SortedReasonEntity> reasonEntities = Lists.newArrayList();
        List<CfRefuseReasonEntity> entitys = entityBiz.selectByIds(entityIds);


        if (CollectionUtils.isEmpty(entitys)) {
            return reasonEntities;
        }

        List<CfRefuseReasonTag> reasonTags = reasonTagBiz.selectByTagIds(entitys.stream()
                .map(CfRefuseReasonEntity::getTagId).collect(Collectors.toSet()));

        Collections.sort(reasonTags, new Comparator<CfRefuseReasonTag>() {
            @Override
            public int compare(CfRefuseReasonTag o1, CfRefuseReasonTag o2) {
                if (o1.getDataType() != o2.getDataType()) {
                    return o1.getDataType() - o2.getDataType();
                }
                return o1.getDataStep() - o2.getDataStep();
            }
        });

        Map<Integer, List<CfRefuseReasonEntity>> reasonEntityMap = Maps.newTreeMap();
        for (CfRefuseReasonTag reasonTag : reasonTags) {
            List<Integer> sortEntityIds = Lists.transform(Splitter.on(ENTITY_SPLIT)
                    .splitToList(reasonTag.getReasonIds()), Integer::parseInt);
            for (Integer entityId : sortEntityIds) {
                for (CfRefuseReasonEntity entity : entitys) {
                    if (entityId.equals(entity.getId())) {
                        List<CfRefuseReasonEntity> refuseReasonList =  reasonEntityMap.get(reasonTag.getDataType());
                        if (refuseReasonList == null) {
                            refuseReasonList = Lists.newArrayList();
                            reasonEntityMap.put(reasonTag.getDataType(), refuseReasonList);
                        }
                        refuseReasonList.add(entity);
                        break;
                    }
                }
            }
        }

        reasonEntityMap.forEach((key, value)->{reasonEntities.add(new InitialAuditOperationItem.SortedReasonEntity(key, value));});

        return reasonEntities;
    }

    private List<Integer> selectSortEntityIds(List<Integer> dataTypes) {

        List<Integer> sortEntityIds = Lists.newArrayList();

        List<CfRefuseReasonTag> allSortReasonTags = Lists.newArrayList();
        dataTypes.forEach((dataType)->{allSortReasonTags.addAll(reasonTagBiz.selectByDataType(dataType));});

        allSortReasonTags.forEach((reasonTag)->{sortEntityIds.addAll(Lists.transform(Splitter.on(ENTITY_SPLIT)
                .splitToList(reasonTag.getReasonIds()), Integer::parseInt));});

        return sortEntityIds;
    }

    private List<CfRefuseReasonTag> selectSortReasonTags(int dataType) {

        List<CfRefuseReasonTag> reasonTags = reasonTagBiz.selectByDataType(dataType);

        // 排序
        Collections.sort(reasonTags, new Comparator<CfRefuseReasonTag>() {
            @Override
            public int compare(CfRefuseReasonTag o1, CfRefuseReasonTag o2) {
                return o1.getDataStep() - o2.getDataStep();
            }
        });

        return reasonTags;
    }

}






































