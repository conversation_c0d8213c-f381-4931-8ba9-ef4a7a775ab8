package com.shuidihuzhu.cf.service.report;

/**
 * @Author: wangpeng
 * @Date: 2022/7/22 15:29
 * @Description:
 */
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCredibleInfoWorkOrderDAO;
import com.shuidihuzhu.cf.model.crowdfunding.CfCredibleInfoDO;
import com.shuidihuzhu.cf.model.crowdfunding.CfCredibleInfoWorkOrderDO;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCoreFeignClient;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCreateParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderLevel;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * @Author: wangpeng
 * @Date: 2022/7/21 14:15
 * @Description:
 */
@Slf4j
@Service
public class CfReportWorkOrderService {

    @Resource
    private CfWorkOrderClient cfWorkOrderClient;

    @Resource
    private WorkOrderCoreFeignClient workOrderCoreFeignClient;

    @Resource
    private AdminCredibleInfoWorkOrderDAO adminCredibleInfoWorkOrderDAO;

    public void createInsteadInputWorkOrder(CfCredibleInfoDO cfCredibleInfoDO) {
        int caseId = Objects.requireNonNullElse(cfCredibleInfoDO.getCaseId(), 0);
        //获取最近的举报工单
        Response<WorkOrderVO> response = cfWorkOrderClient.getLastWorkOrderByTypes(caseId, WorkOrderType.REPORT_TYPES);
        WorkOrderVO workOrderVO = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(null);
        if (Objects.isNull(workOrderVO)) {
            return;
        }
        WorkOrderCreateParam workOrderCreateParam = new WorkOrderCreateParam();
        workOrderCreateParam.setOrderType(WorkOrderType.report_instead_input.getType());
        workOrderCreateParam.setCaseId(caseId);
        workOrderCreateParam.setOrderlevel(OrderLevel.D.getType());
        Response<Long> workOrderResponse = workOrderCoreFeignClient.create(workOrderCreateParam);
        log.info("CfReportWorkOrderService createInsteadInputWorkOrder {}", workOrderResponse);
        long workOrderId = Optional.ofNullable(workOrderResponse)
                .map(Response::getData)
                .orElse(0L);
        if (workOrderId == 0L) {
            return;
        }
        CfCredibleInfoWorkOrderDO cfCredibleInfoWorkOrderDO = new CfCredibleInfoWorkOrderDO();
        cfCredibleInfoWorkOrderDO.setCaseId(caseId);
        cfCredibleInfoWorkOrderDO.setWorkOrderId(workOrderId);
        cfCredibleInfoWorkOrderDO.setCredibleInfoId(cfCredibleInfoDO.getId());
        adminCredibleInfoWorkOrderDAO.insert(cfCredibleInfoWorkOrderDO);
    }
}

