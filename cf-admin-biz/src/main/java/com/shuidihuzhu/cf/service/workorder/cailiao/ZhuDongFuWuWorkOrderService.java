package com.shuidihuzhu.cf.service.workorder.cailiao;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingOperationBiz;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationRecordDTO;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingOperationEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.event.OrderZhuDongFuWuCheckPayload;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation;
import com.shuidihuzhu.cf.model.event.CailiaoCondition;
import com.shuidihuzhu.cf.model.event.CailiaoConditionResult;
import com.shuidihuzhu.cf.service.crowdfunding.CfCailiaoService;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.CailiaoWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderLevel;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

/**
 * <AUTHOR>
 */
@RefreshScope
@Service
@Slf4j
@Deprecated
public class ZhuDongFuWuWorkOrderService {

    @Value("${apollo.work-order.zhu-dong-fu-wu-amount:10000}")
    private int zhuDongFuWuAmount;

    @Autowired(required = false)
    private Producer producer;

    @Autowired
    private CommonOperationRecordClient commonOperationRecordClient;

    @Autowired
    private AdminCrowdfundingOperationBiz crowdfundingOperationBiz;

    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;

    @Autowired
    private CfCailiaoService cfCailiaoService;

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    private static List<Integer> NO_NEED_HANDLE_STATUS = Lists.newArrayList(CrowdfundingOperationEnum.NEVER_PROCESSING.value(), CrowdfundingOperationEnum.DEFER_CONTACT.value());

    /**
     * 初审通过
     * @param fundingInfo
     */
    public void onInitialApprovePassed(CrowdfundingInfo fundingInfo) {
//        int caseId = fundingInfo.getId();
//        OrderZhuDongFuWuCheckPayload.FlowTypeEnum flowTypeEnum = OrderZhuDongFuWuCheckPayload.FlowTypeEnum.HIGH_AMOUNT;
//        sendCheckMsg(caseId, 1, flowTypeEnum.getValue(), 1);
    }

    /**
     * 材审驳回
     * @param fundingInfo
     */
    public void onCaseApproveRefuse(CrowdfundingInfo fundingInfo) {
//        int caseId = fundingInfo.getId();
//        OrderZhuDongFuWuCheckPayload.FlowTypeEnum flowTypeEnum = OrderZhuDongFuWuCheckPayload.FlowTypeEnum.CASE_INFO_REFUSE;
//        boolean canCreate = isCanCreate(fundingInfo, flowTypeEnum.getValue(), null);
//        if (!canCreate) {
//            return;
//        }
//        sendCheckMsg(caseId, 1, flowTypeEnum.getValue(), 1);
    }


    public void onUserSubmit(int caseId) {
//        log.info("onUserSubmit {}", caseId);
//        closeLastOrderByCaseId(caseId);
    }

    public void onCaseApprovePassed(CrowdfundingInfo cfCase) {
//        int caseId = cfCase.getId();
//        log.info("onCaseApprovePassed {}", caseId);
//        closeLastOrderByCaseId(caseId);
    }

    public void onCaseNoProcess(CrowdfundingInfo crowdfundingInfo) {
//        int caseId = crowdfundingInfo.getId();
//        log.info("onCaseNoProcess {}", caseId);
//        closeLastOrderByCaseId(caseId);
    }



    @NotNull
    private CailiaoConditionResult getCaiLiaoConditionResult(CailiaoCondition condition_12) {
        CailiaoConditionResult e = new CailiaoConditionResult();
        e.setCode(condition_12.getCode());
        e.setMsg(CailiaoCondition.condition_12.getMsg());
        return e;
    }

    private boolean isCanCreate(CrowdfundingInfo cfInfo, int flowType, LocalDateTime sendTime) {
        int caseId = cfInfo.getId();
        CrowdfundingOperation crowdfundingOperation = crowdfundingOperationBiz.getByInfoId(cfInfo.getInfoId());
        if (NO_NEED_HANDLE_STATUS.contains(crowdfundingOperation.getOperation())) {
            log.info("isCanCreate false no need");
            return false;
        }
        CrowdfundingStatus status = cfInfo.getStatus();

        // 上次发送校验时间之后有提交或驳回则不继续check 防止并发问题+10s延时
        OperationRecordDTO lastRecord = commonOperationRecordClient.getLastByBizIdAndActionTypes(caseId,
                OperationActionTypeEnum.SUBMIT_INFO_APPROVE, OperationActionTypeEnum.CASE_INFO_REFUSE_RECORD_EVERY_TIME);
        if (lastRecord != null && sendTime != null) {
            Instant lastActionInstant = lastRecord.getActionTime().toInstant();
            LocalDateTime lastActionTime = LocalDateTime.ofInstant(lastActionInstant, ZoneId.systemDefault());
            if (lastActionTime.isAfter(sendTime.plusSeconds(10))) {
                log.info("onMessageConsumer false 上次发送校验时间之后有提交或驳回则不继续check");
                return false;
            }
        }

        if (flowType == OrderZhuDongFuWuCheckPayload.FlowTypeEnum.HIGH_AMOUNT.getValue()) {
            return status == CrowdfundingStatus.APPROVE_PENDING;
        }
        if (flowType == OrderZhuDongFuWuCheckPayload.FlowTypeEnum.CASE_INFO_REFUSE.getValue()) {
            log.info("isCanCreate case info refuse");
            return status == CrowdfundingStatus.APPROVE_DENIED;
        }
        return false;
    }

    private void sendCheckMsg(int caseId, int delayDay, int flowType, int sendCount) {
//        OrderZhuDongFuWuCheckPayload payload = new OrderZhuDongFuWuCheckPayload();
//        payload.setCaseId(caseId);
//        payload.setFlowType(flowType);
//        payload.setSendCount(sendCount);
//
//        LocalDateTime nowTime = LocalDateTime.now();
//        long scheduleTimeStamp = nowTime.plus(delayDay, ChronoUnit.DAYS).toEpochSecond(ZoneOffset.ofHours(8));
//        payload.setSendTime(nowTime);
//
//        Message<OrderZhuDongFuWuCheckPayload> msg = new Message<>(MQTopicCons.CF, MQTagCons.CF_ZHU_DONG_FU_WU_CHECK,
//                MQTagCons.CF_ZHU_DONG_FU_WU_CHECK + "_" + System.currentTimeMillis(),
//                payload);
//        msg.setScheduleTimeStamp(scheduleTimeStamp);
//        MessageResult result = producer.send(msg);
//        log.info("sendCheckMsg caseId {}, delayDay {}, result {}", caseId, delayDay, result);
    }

    private void closeLastOrderByCaseId(int caseId) {
        Response<WorkOrderVO> lastWorkOrderResp = cfWorkOrderClient.getLastWorkOrder(caseId, WorkOrderType.cailiao_zhu_dong_fu_wu.getType());
        if (lastWorkOrderResp == null || lastWorkOrderResp.notOk()) {
            log.info("失败");
            return ;
        }
        WorkOrderVO lastWorkOrder = lastWorkOrderResp.getData();
        if (lastWorkOrder == null) {
            log.info("无工单");
            return ;
        }
        if (!HandleResultEnum.unDoResult().contains(lastWorkOrder.getHandleResult())){
            log.info("已处理");
            return ;
        }
        closeWorkorderByOrderId(caseId);
    }

    private void closeWorkorderByOrderId(int caseId){
        Response response = cfWorkOrderClient.closeOrderBycaseIdAndType(caseId,
                WorkOrderType.cailiao_zhu_dong_fu_wu.getType(),
                HandleResultEnum.exception_done.getType(),
                0, "");

        log.info("closeWorkorder response={}",response);
    }
}
