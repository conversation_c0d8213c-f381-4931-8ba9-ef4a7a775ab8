package com.shuidihuzhu.cf.service.sensitive.adapter;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.CfSensitiveWordRecordEnum;
import com.shuidihuzhu.cf.enums.risk.UgcTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.deliver.CrowdFundingVerificationDeliver;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfSensitiveWordRecordVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2019-03-14  14:57
 */
@Service
public class SensitiveVerificationAdapter implements ISensitiveAdapter<CrowdFundingVerificationDeliver> {

    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Override
    public String getContent(CrowdFundingVerificationDeliver v) {
        return v.getDescription();
    }

    @Override
    public CfSensitiveWordRecordVo buildRecord(CrowdFundingVerificationDeliver v, String hitWord) {
        return buildRecordByVerification(v, hitWord);
    }

    @Override
    public long getBizId(CrowdFundingVerificationDeliver data) {
        return data.getId();
    }

    @Override
    public int getCaseId(CrowdFundingVerificationDeliver data) {
        String infoUuid = data.getCrowdFundingInfoId();
        CfInfoSimpleModel fundingInfo = crowdfundingDelegate.getCfInfoSimpleModel(infoUuid);
        return fundingInfo.getId();
    }

    @Override
    public UgcTypeEnum getUgcTypeEnum() {
        return UgcTypeEnum.VERIFICATION;
    }

    @Override
    public boolean needAutoHandle() {
        return true;
    }

    @Override
    public String getUniquelyIdentifies(CrowdFundingVerificationDeliver crowdFundingVerificationDeliver) {
        return crowdFundingVerificationDeliver.getUniquelyIdentifies();
    }

    @Override
    public boolean needAiCheck(CrowdFundingVerificationDeliver data) {
        return true;
    }

    @Override
    public CfSensitiveWordRecordEnum.BizType getSensitiveRecordBizType(CrowdFundingVerificationDeliver verification) {
        return CfSensitiveWordRecordEnum.BizType.VERIFICATION;
    }

    @Override
    public long getUserId(CrowdFundingVerificationDeliver data) {
        return data.getVerifyUserId();
    }


    private CfSensitiveWordRecordVo buildRecordByVerification(CrowdFundingVerificationDeliver verification, String hitWord) {
        String infoUuid = verification.getCrowdFundingInfoId();
        CrowdfundingInfo fundingInfo = adminCrowdfundingInfoBiz.getFundingInfo(infoUuid);
        CfSensitiveWordRecordVo v = new CfSensitiveWordRecordVo();
        fillCaseInfo(verification, v, fundingInfo);
        v.setUserId(verification.getVerifyUserId());
        v.setBizType(getSensitiveRecordBizType(verification).value());
        v.setBizId(verification.getId());
        v.setParentBizId(-1);
        v.setSensitiveWord(hitWord);
        v.setBizTime(new Timestamp(System.currentTimeMillis()));
        v.setContent(verification.getDescription());
        return v;
    }

}
