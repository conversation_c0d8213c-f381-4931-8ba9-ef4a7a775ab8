package com.shuidihuzhu.cf.service.risk.highrisk;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.adminpure.model.rule.EconomyModel;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.feign.CfMaterialReadClient;
import com.shuidihuzhu.cf.client.material.model.CfPropertyInsuranceInfoModel;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.risk.client.rpc.PreposeMaterialRiskClient;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.HighRiskJudgeConst;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.HighRiskJudgeInfoModel;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.HighRiskJudgeResult;
import com.shuidihuzhu.cf.vo.approve.CreditInfoVO;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class HighRiskServiceImpl implements HighRiskService {

    @Resource
    private PreposeMaterialRiskClient preposeMaterialRiskClient;

    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Autowired
    private CfMaterialReadClient materialReadClient;


    @Override
    public HighRiskJudgeResult judgeRisk(int source, long workOrderId, CreditInfoVO creditInfoVO) {
        if (creditInfoVO == null) {
            log.warn("judge risk v2 null data");
            return new HighRiskJudgeResult();
        }
        log.debug("judge start risk v2 source {}, workOrderId {}, caseId {}, data {}",
                source, workOrderId, creditInfoVO.getCaseId(), JSON.toJSONString(creditInfoVO));

        CrowdfundingInfo fundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(creditInfoVO.getCaseId());
        int targetAmount = fundingInfo.getTargetAmount();
        HighRiskJudgeInfoModel info = creditInfo2Info(source, creditInfoVO, targetAmount);
        info.setWorkOrderId(workOrderId);
        Response<HighRiskJudgeResult> result = preposeMaterialRiskClient.judgeHighRiskV2(info);
        if (result == null || result.notOk()) {
            log.error("judgeRisk fail caseId{}, result {}", creditInfoVO.getCaseId(), result);
            // 出错默认低风险
            return new HighRiskJudgeResult();
        }
        HighRiskJudgeResult data = result.getData();

        log.info("judge end risk v2 source {}, workOrderId {}, caseId {}, res {}",
                source, workOrderId, creditInfoVO.getCaseId(), data);
        return data;
    }

    private HighRiskJudgeInfoModel creditInfo2Info(int source, CreditInfoVO creditInfoVO, int targetAmount) {
        HighRiskJudgeInfoModel i = new HighRiskJudgeInfoModel();
        i.setSource(source);
        // 案例ID
        i.setCaseId(creditInfoVO.getCaseId());
        i.setTargetAmountInYuan(targetAmount);

        // 其他房
        CreditInfoVO.HousePropertyInfoVO otherHouseProperty = creditInfoVO.getHouseProperty();
        Integer otherHouseAmountInYuan = null;
        Integer houseNetAmountInYuan = null;
        Integer otherHouseCount = null;
        Integer otherSaleCount = null;
        Integer otherHouseSaleAmountInYuan = null;
        if (otherHouseProperty != null) {
            otherHouseAmountInYuan = getValue(
                    otherHouseProperty.getTotalValueUserDefined(),
                    otherHouseProperty.getTotalValueRangeType(),
                    new CreditInfoVO.HouseFunction()
            );
            houseNetAmountInYuan = getValue(
                    otherHouseProperty.getPureValueUserDefined(),
                    otherHouseProperty.getPureValueRangeType(),
                    new CreditInfoVO.HouseFunction()
            );
            otherHouseCount = otherHouseProperty.getTotalCount();
            otherSaleCount = otherHouseProperty.getSaleCount();
            otherHouseSaleAmountInYuan = getValue(
                    otherHouseProperty.getSaleValueUserDefined(),
                    otherHouseProperty.getSaleValueRangeType(),
                    new CreditInfoVO.HouseFunction()
            );
        }

        // 自建房
        CreditInfoVO.HousePropertyInfoVO selfHouseProperty = creditInfoVO.getSelfBuiltHouse();
        Integer selfHouseAmountInYuan = null;
        Integer selfHouseCount = null;
        Integer selfSaleCount = null;
        Integer selfHouseSaleAmountInYuan = null;
        if (selfHouseProperty != null) {
            selfHouseAmountInYuan = getHouseTotalValue(selfHouseProperty);
            selfHouseCount = selfHouseProperty.getTotalCount();
            selfSaleCount = selfHouseProperty.getSaleCount();
            selfHouseSaleAmountInYuan = getValue(
                    selfHouseProperty.getSaleValueUserDefined(),
                    selfHouseProperty.getSaleValueRangeType(),
                    new CreditInfoVO.HouseFunction()
            );
        }
        // 房屋总价值
        i.setHouseAmountInYuan(plus(selfHouseAmountInYuan, otherHouseAmountInYuan));
        // 房屋总净值
        i.setHouseNetAssetsInYuan(plus(selfHouseAmountInYuan, houseNetAmountInYuan));
        // 其他房屋价值
        i.setOtherHouseAmountInYuan(otherHouseAmountInYuan);

        Integer houseCount = plus(selfHouseCount, otherHouseCount);
        // 房屋数量
        i.setHouseCount(houseCount);
        i.setHouseOtherCount(otherHouseCount);

        // 房屋变卖数
        Integer houseSellCount = plus(selfSaleCount, otherSaleCount);
        i.setHouseSellCount(houseSellCount);

        // 房屋变卖价值
        Integer houseSellAmountInYuan = plus(selfHouseSaleAmountInYuan, otherHouseSaleAmountInYuan);
        i.setHouseSellAmountInYuan(houseSellAmountInYuan);


        CreditInfoVO.CarPropertyInfoVO carProperty = creditInfoVO.getCarProperty();
        Integer carSaleCount = null;
        Integer carTotalAmountInYuan = null;
        Integer carTotalSellAmountInYuan = null;
        if (carProperty != null) {
            carSaleCount = carProperty.getSaleCount();
            carTotalAmountInYuan = getValue(
                    carProperty.getTotalValueUserDefined(),
                    carProperty.getTotalValueRangeType(),
                    new CreditInfoVO.CarFunction()
            );
            carTotalSellAmountInYuan = getValue(
                    carProperty.getSaleValueUserDefined(),
                    carProperty.getSaleValueRangeType(),
                    new CreditInfoVO.CarFunction()
            );
        }
        // 车产总值
        i.setCarAmountInYuan(carTotalAmountInYuan);
        // 车产变卖数
        i.setCarSellCount(carSaleCount);
        // 车产变卖总值
        i.setCarSellAmountInYuan(carTotalSellAmountInYuan);

        Integer homeIncomeAmountInYuan = getValue(
                creditInfoVO.getHomeIncomeUserDefined(),
                creditInfoVO.getHomeIncomeRangeType(),
                new CreditInfoVO.HomeIncomeFunction()
        );
        // 家庭年收入
        i.setYearIncome(homeIncomeAmountInYuan);

        // 金融资产总值
        Integer monetaryAssertInYuan = getValue(
                creditInfoVO.getHomeStockUserDefined(),
                creditInfoVO.getHomeStockRangeType(),
                new CreditInfoVO.HomeStockFunction()
        );
        i.setMonetaryAssertInYuan(monetaryAssertInYuan);

        // 负债总金额
        Integer debtInYuan = getValue(
                Optional.ofNullable(creditInfoVO.getHomeDebtDecimalAmount()).map(BigDecimal::intValue).orElse(null),
                creditInfoVO.getHomeDebtRangeType(),
                new CreditInfoVO.HomeDebtFunction()
        );
        i.setDebtInYuan(debtInYuan);

        // 剩余款项
        Integer remainAmountInYuan = Optional.ofNullable(creditInfoVO.getOtherPlatform())
                .map(CfPropertyInsuranceInfoModel.RaiseOnOtherPlatform::getRemainAmount)
                .orElse(null);
        i.setRemainAmountInYuan(remainAmountInYuan);

        // 人身险
        i.setPersonalInsurance(creditInfoVO.getLifeInsurance() == null ? null : (creditInfoVO.getLifeInsurance() == 1));
        i.setInsurancePaidAmount(creditInfoVO.getPaidInsuranceAmount());
        i.setInsurancePayoutStatus(creditInfoVO.getInsuranceCompensationStatus());

        // 是否是普通案例
        i.setNormalCase(creditInfoVO.getAccidentType() == null ? null :
                (creditInfoVO.getAccidentType() == PreposeMaterialModel.AccidentType.DEFAULT.getCode()));

        i.setAccidentResponsibility(creditInfoVO.getAccidentDuty());

        i.setAccidentPaidAmount(creditInfoVO.getPaidAmount());

        if(remainAmountInYuan != null) {
            i.setOtherPlatformFundraisingAmount(remainAmountInYuan * 100);
        }

        // 单位元转分
        if(creditInfoVO.getMaxTreatmentCost() != null) {
            i.setMaxTreatmentCost(creditInfoVO.getMaxTreatmentCost() * 100);
        }

        //患者身份
        i.setPatientIdentity(creditInfoVO.getPatientIdentity());
        i.setPatientMaritalStatus(creditInfoVO.getPatientMaritalStatus());
        i.setMarriedChildrenCount(creditInfoVO.getMarriedChildrenCount());
        i.setMarriedChildrenStatus(creditInfoVO.getMarriedChildrenStatus());
        i.setPatientParentStatus(creditInfoVO.getPatientParentStatus());

        i.setAuthenticityIndicator(creditInfoVO.getAuthenticityIndicator());
        i.setPaidAmount(creditInfoVO.getPaidAmount());
        return i;
    }

    @Override
    public EconomyModel judgeManual(int caseId, Long workOrderId, int userId, EconomyModel economyModel) {
        HighRiskJudgeInfoModel info = economy2Info(caseId, economyModel);
        info.setSource(HighRiskJudgeConst.Source.MANUAL);
        info.setCaseId(caseId);
        info.setWorkOrderId(workOrderId);
        info.setOperatorId((long) userId);
        Response<HighRiskJudgeResult> result = preposeMaterialRiskClient.judgeHighRiskV2(info);
        if (result.notOk()) {
            log.warn("judgeManual error {}", economyModel);
            return economyModel;
        }
        HighRiskJudgeResult data = result.getData();
        economyModel.setResult(data.getRiskLabels());
        economyModel.setConvertResult(data.getRiskTips());
        economyModel.setHighRisk(data.isHighRisk());
        economyModel.setHitCodes(data.getHitCodes());
        economyModel.setRiskRuleVersion(2);
        return economyModel;
    }

    @Override
    public Integer getMResult(int caseId) {

        RpcResult<CfPropertyInsuranceInfoModel> rpcResult = materialReadClient.selectCfPropertyInsuranceInfo(caseId);

        CfPropertyInsuranceInfoModel infoModel = Optional.ofNullable(rpcResult)
                .filter(RpcResult::isSuccess)
                .map(RpcResult::getData)
                .orElse(null);

        if (infoModel == null) {
            log.info("getMResult infoModel is null caseId {}", caseId);
            // 取不到写死返回值150万
            return 1500000;
        }

        return infoModel.getNetWorthThreshold();
    }

    /**
     * {"targetAmount":300000,"spentAmount":120000,"selfHouseNum":3,"selfHouseValue":120000,"selfHouseSellingCount":2,"isIdle":"","houseNum":3,"houseAmount":30000,"otherHouseSellingCount":2,"houseLiveStatus":"","carNum":3,"carAmount":30000,"carUseStatus":2,"carBrandTypes":[0],"userDefinedCarBrands":"asdsadas","financeAmount":120000,"debtAmount":120000,"homeIncomeRange":120000,"otherPlatform":1,"remainAmount":330000,"hasPersonalInsurance":1,"insurancePayoutStatus":1,"insurancePayoutCTC":2,"accidentStatus":1,"accidentPayoutStatus":1,"accidentPayoutCTC":2}
     */
    private HighRiskJudgeInfoModel economy2Info(int caseId, EconomyModel v) {
        HighRiskJudgeInfoModel i = new HighRiskJudgeInfoModel();

        // 案例ID
        i.setCaseId(caseId);

        // 房屋总价值
        i.setHouseAmountInYuan(plus(v.getHouseAmount(), v.getSelfHouseValue()));
        // 房产总净值
        if(v.getHouseNetValue() != null){
            i.setHouseNetAssetsInYuan(plus(v.getHouseNetValue(), v.getSelfHouseValue()));
        }

        // 其他房产总价值
        i.setOtherHouseAmountInYuan(v.getHouseAmount());

        Integer houseCount = plus(v.getHouseNum(), v.getSelfHouseNum());
        // 房屋数量
        i.setHouseCount(houseCount);

        // 房屋变卖数
        Integer houseSellCount = plus(v.getSelfHouseSellingCount(), v.getOtherHouseSellingCount());
        i.setHouseSellCount(houseSellCount);

        // 房屋变卖价值

        // 车产总值
        i.setCarAmountInYuan(v.getCarAmount());
        // 车产变卖数
        // 车产变卖总值
        // 车产总数
        i.setCarCount(v.getCarNum());

        // 家庭年收入
        i.setYearIncome(v.getHomeIncomeRange());

        // 金融资产总值
        i.setMonetaryAssertInYuan(v.getFinanceAmount());

        // 负债总金额
        i.setDebtInYuan(v.getDebtAmount());

        // 剩余款项
        i.setRemainAmountInYuan(v.getRemainAmount());

        // 人身险
        i.setPersonalInsurance(v.getHasPersonalInsurance() == 1);

        // 是否是普通案例
        i.setNormalCase(v.getAccidentStatus() == 0);

        // 目标金额
        i.setTargetAmountInYuan(v.getTargetAmount());

        // 有闲置房产
        i.setHouseUnused(v.getHouseLiveStatus() == 1 || v.getIsIdle() == 1);

        // 是否有闲置车辆
        i.setCarUnused(v.getCarUseStatus() == 2);

        // 是否有名车
        i.setCarIsFamous(v.getCarBrandTypes().contains(2));

        // 人身险赔付状态
        i.setInsurancePayoutStatus(v.getInsurancePayoutStatus());
        i.setInsurancePayoutCTC(v.getInsurancePayoutCTC());

        // 事故赔付状态
        i.setAccidentPayoutStatus(v.getAccidentPayoutStatus());
        i.setAccidentPayoutCTC(v.getAccidentPayoutCTC());

        // 患者已婚子女
        i.setMarriedChildrenStatus(v.getMarriedChildrenStatus());
        i.setMarriedChildrenCount(v.getMarriedChildrenCount());
        // 患者父母
        i.setPatientMaritalStatus(v.getPatientMaritalStatus());
        i.setPatientParentStatus(v.getPatientParentStatus());
        
        // 小工具专有字段
        i.setMarriedChildrenInfoList(v.getMarriedChildrenInfoList());
        i.setPatientParentInfoList(v.getPatientParentInfoList());

        return i;
    }

    /**
     * 解析值
     * 优先用户输入 否则取区间最大值
     */
    private Integer getValue(Integer userDefined, Integer rangeType, CreditInfoVO.ParseFunction parseFunction) {
        if (userDefined != null) {
            return userDefined;
        }
        if (rangeType == null) {
            return null;
        }
        if (rangeType <= 0) {
            log.warn("rangeType 不合法:{}", rangeType);
            return null;
        }
        CfPropertyInsuranceInfoModel.IValueRange range = parseFunction.parse(rangeType);
        return range.getTo();
    }

    private Integer getHouseTotalValue(CfPropertyInsuranceInfoModel.HousePropertyInfo propertyInfo) {
        if (propertyInfo == null) {
            return null;
        }
        return getValue(
                propertyInfo.getTotalValueUserDefined(),
                propertyInfo.getTotalValueRangeType(),
                new CreditInfoVO.HouseFunction()
        );
    }


    private Integer plus(Integer a, Integer b) {
        if (a == null) {
            return b;
        }
        if (b == null) {
            return a;
        }
        return a + b;
    }
}
