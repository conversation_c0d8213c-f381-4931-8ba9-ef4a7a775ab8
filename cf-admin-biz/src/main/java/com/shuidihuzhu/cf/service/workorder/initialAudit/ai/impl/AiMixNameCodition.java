package com.shuidihuzhu.cf.service.workorder.initialAudit.ai.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.dao.crowdfunding.CfAiMaterialsDao;
import com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterials;
import com.shuidihuzhu.cf.model.crowdfunding.ai.LayOutField;
import com.shuidihuzhu.cf.service.workorder.initialAudit.ai.AiCondition;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @DATE 2020/12/21
 */
@Service("aiMixName")
@Slf4j
public class AiMixNameCodition implements AiCondition {

    @Autowired
    private CfAiMaterialsDao cfAiMaterialsDao;


    @Override
    public boolean check(int caseId, String inputValue) {

        CfAiMaterials m1 = cfAiMaterialsDao.getByCaseId(caseId,CfAiMaterials.jType);
        Optional o1 = Optional.ofNullable(m1).map(CfAiMaterials::getFields).orElse(Lists.newArrayList()).stream().filter(r->inputValue.equals(r.getFieldKey())).map(LayOutField::getFieldValue).findFirst();
        if (o1.isPresent()){
            CfAiMaterials m2 = cfAiMaterialsDao.getByCaseId(caseId,CfAiMaterials.tType);
            Optional o2 = Optional.ofNullable(m2).map(CfAiMaterials::getFields).orElse(Lists.newArrayList()).stream().filter(r->"patientNameInMd".equals(r.getFieldKey())).map(LayOutField::getFieldValue).findFirst();
            if (o2.isPresent()){
                log.info("caseId={} o1={},o2={}",caseId,o1.get(),o2.get());
                return !o1.get().equals(o2.get());
            }
        }
        return false;
    }


}
