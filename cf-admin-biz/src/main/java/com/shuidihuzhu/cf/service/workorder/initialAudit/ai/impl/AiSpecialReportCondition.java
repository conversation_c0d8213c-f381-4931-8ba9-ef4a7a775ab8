package com.shuidihuzhu.cf.service.workorder.initialAudit.ai.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.dao.crowdfunding.CfAiMaterialsDao;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.ai.AiConditionEnum;
import com.shuidihuzhu.cf.model.crowdfunding.ai.AiRules;
import com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterials;
import com.shuidihuzhu.cf.model.crowdfunding.ai.LayOutField;
import com.shuidihuzhu.cf.service.workorder.initialAudit.ai.AiSingleCondition;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: wangpeng
 * @Date: 2021/8/24 15:03
 * @Description:
 */
@Service("aiSpecialReportCondition")
public class AiSpecialReportCondition implements AiSingleCondition {

    @Resource
    private CfAiMaterialsDao cfAiMaterialsDao;
    @Resource
    private IRiskDelegate firstApproveBiz;

    @Override
    public AiRules check(int caseId) {
        AiRules aiRules = new AiRules();
        CfAiMaterials imageWorkOrder = cfAiMaterialsDao.getByCaseId(caseId, CfAiMaterials.tType);
        if (Objects.isNull(imageWorkOrder)) {
            return aiRules;
        }
        List<LayOutField> layOutFields = Optional.of(imageWorkOrder)
                .map(CfAiMaterials::getFields)
                .orElse(Lists.newArrayList());
        if (CollectionUtils.isEmpty(layOutFields)) {
            return aiRules;
        }
        Map<String, String> collect = layOutFields.stream()
                .collect(Collectors.toMap(LayOutField::getFieldKey, LayOutField::getFieldValue, (x, y) -> x));
        String jsonString = JSONObject.toJSONString(collect);
        AiImageWorkOrderSpecialReport aiContentWorkOrderHouse = JSONObject.parseObject(jsonString, AiImageWorkOrderSpecialReport.class);
        if (Objects.isNull(aiContentWorkOrderHouse)) {
            return aiRules;
        }
        String yes = String.valueOf(AiConditionEnum.shi.getCode());
        String no = String.valueOf(AiConditionEnum.fou.getCode());
        String you = String.valueOf(AiConditionEnum.you.getCode());
        String wu = String.valueOf(AiConditionEnum.wu.getCode());
        if (StringUtils.equals(aiContentWorkOrderHouse.getSpecialReport(), no)) {
            if (StringUtils.equals(AiConditionEnum.wu.getStringCode(), aiContentWorkOrderHouse.getHasMedicalData())) {
                aiRules.setValue("315");
                return aiRules;
            }
            return aiRules;
        }
        CfFirsApproveMaterial material = firstApproveBiz.getCfFirsApproveMaterialByInfoId(caseId);
        String patientName = Objects.nonNull(material) ? material.getPatientRealName() : "";
        boolean patientNameEqual = StringUtils.equals(patientName, aiContentWorkOrderHouse.getSpecialReportPatientName());
        if (StringUtils.equals(aiContentWorkOrderHouse.getSpecialReportTypeValid(), yes) && StringUtils.equals(aiContentWorkOrderHouse.getSpecialReportReasonValid(), yes) &&
                StringUtils.equals(aiContentWorkOrderHouse.getSpecialReportContainMaterial(), yes) &&StringUtils.equals(aiContentWorkOrderHouse.getSpecialReportHasPatientName(), you)) {
            if (patientNameEqual) {
                return aiRules;
            }
            aiRules.setValue("318");
            return aiRules;
        }
        if (StringUtils.equals(aiContentWorkOrderHouse.getSpecialReportTypeValid(), no) && StringUtils.equals(aiContentWorkOrderHouse.getSpecialReportContainMaterial(), yes) &&StringUtils.equals(aiContentWorkOrderHouse.getSpecialReportHasPatientName(), you)) {
            if (patientNameEqual) {
                aiRules.setValue("317");
                return aiRules;
            }
            aiRules.setValue("318");
            return aiRules;
        }
        if (StringUtils.equals(aiContentWorkOrderHouse.getSpecialReportContainMaterial(), no)) {
            aiRules.setValue("315");
            return aiRules;
        }
        if (StringUtils.equals(aiContentWorkOrderHouse.getSpecialReportContainMaterial(), yes) && StringUtils.equals(aiContentWorkOrderHouse.getSpecialReportHasPatientName(), wu)) {
            aiRules.setValue("320");
            return aiRules;
        }
        if (StringUtils.equals(aiContentWorkOrderHouse.getSpecialReportReasonValid(), no) && StringUtils.equals(aiContentWorkOrderHouse.getSpecialReportContainMaterial(), yes) && StringUtils.equals(aiContentWorkOrderHouse.getSpecialReportHasPatientName(), you)) {
            if (patientNameEqual) {
                aiRules.setValue("317");
                return aiRules;
            }
            aiRules.setValue("318");
            return aiRules;
        }
        return aiRules;
    }

    @Data
    public static class AiImageWorkOrderSpecialReport {
        /**
         * 是否特殊报备
         */
        private String specialReport;
        /**
         * 特殊报备类型是否正确
         */
        private String specialReportTypeValid;
        /**
         * 特殊报备原因是否正确
         */
        private String specialReportReasonValid;
        /**
         * 特殊报备类型是否包含医疗材料
         */
        private String specialReportContainMaterial;
        /**
         * 特殊报备类型是否包含患者名称
         */
        private String specialReportHasPatientName;
        /**
         * 特殊报备类型患者名称
         */
        private String specialReportPatientName;
        /**
         * 有无医疗材料
         */
        private String hasMedicalData;
    }
}
