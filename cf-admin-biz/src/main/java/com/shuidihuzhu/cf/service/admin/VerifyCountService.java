package com.shuidihuzhu.cf.service.admin;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.admin.verifycount.VerifyCountRoute;
import com.shuidihuzhu.cf.vo.admin.VerifyCountVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/11/4
 */
@Service
@Slf4j
public class VerifyCountService {


    @Autowired
    private List<VerifyCountRoute> verifyCountRouteList;
    private static Map<String, VerifyCountRoute> strategyModelMap;

    @PostConstruct
    private void init(){
        //初始化所有的策略
        strategyModelMap = verifyCountRouteList.stream()
                .collect(Collectors.toMap(VerifyCountRoute::getKey, Function.identity()));
    }

    public List<VerifyCountVo> getAll(long userId) {
        //2.获取redis次数
        List<VerifyCountVo> verifyCountVoList = Lists.newArrayList();
        for (VerifyCountRoute verifyCountRoute : verifyCountRouteList){
            verifyCountVoList.add(verifyCountRoute.getCurrentCountVo(userId));
        }
        return verifyCountVoList;
    }

    public boolean clear(long userId, String key) {
        VerifyCountRoute verifyCountRoute = strategyModelMap.get(key);
        if (verifyCountRoute == null) {
            log.error("verifyCountRoute is null, key:{}", key);
            return false;
        }
        return verifyCountRoute.clear(userId);
    }

    public void innerTest(long userId, String key) {
        VerifyCountRoute verifyCountRoute = strategyModelMap.get(key);
        if (verifyCountRoute == null) {
            log.error("verifyCountRoute is null, key:{}", key);
            return;
        }
         verifyCountRoute.innerTest(userId);
    }
}
