package com.shuidihuzhu.cf.service.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.risk.client.risk.BlacklistVerifyClient;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistVerifyTypeEnum;
import com.shuidihuzhu.cf.risk.model.risk.BlacklistVerifyDto;
import com.shuidihuzhu.client.cf.risk.client.CfRiskBlackListClient;
import com.shuidihuzhu.client.cf.risk.client.CfRiskClient;
import com.shuidihuzhu.client.cf.risk.client.CfRiskPlatformClient;
import com.shuidihuzhu.client.cf.risk.model.enums.CfRiskBlackListEnum;
import com.shuidihuzhu.client.cf.risk.model.enums.PhoneOperateEnum;
import com.shuidihuzhu.client.cf.risk.model.enums.UserOperationEnum;
import com.shuidihuzhu.client.cf.risk.model.result.RiskRpcResponse;
import com.shuidihuzhu.client.cf.risk.model.result.UserOperatorValidUnit;
import com.shuidihuzhu.client.param.RiskOperateLimitParam;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2018/12/13 下午8:27
 * @desc
 */
@Service
@Slf4j
@RefreshScope
public class CfRiskService {
    @Autowired
    private CfRiskClient cfRiskClient;
    @Autowired
    private CfRiskPlatformClient cfRiskPlatformClient;
    @Autowired
    private BlacklistVerifyClient blacklistVerifyClient;
    @Autowired
    private CfRiskBlackListClient cfRiskBlackListClient;


    @Value("${cf-risk.read-new-table:false}")
    private boolean readNewTable;

    public void addOperateLimit(int caseId, Boolean share, Boolean donate, Boolean show, String showSelfTitle,
                                String showOtherTitle, Integer operateSource, String operator, Integer operatorId) {
        //设置是否能转发或者捐款
        Map<String, Boolean> limits = Maps.newHashMap();
        limits.put(String.valueOf(UserOperationEnum.SHARE.getCode()), share);
        limits.put(String.valueOf(UserOperationEnum.ORDER.getCode()), donate);
        limits.put(String.valueOf(UserOperationEnum.SHOW.getCode()), show);

        RiskOperateLimitParam limitParam = new RiskOperateLimitParam();
        limitParam.setCaseId(caseId);
        limitParam.setLimits(limits);
        limitParam.setOperateSource(operateSource);
        limitParam.setOperator(operator);
        limitParam.setOperatorId(operatorId);
        limitParam.setShowSelfTitle(showSelfTitle);
        limitParam.setShowOtherTitle(showOtherTitle);

        try {
            RiskRpcResponse response = cfRiskPlatformClient.addLimit(limitParam);
            log.info("CfRiskService.addLimit SUCCESS. param:{}, result:{}", JSON.toJSONString(limitParam), JSON.toJSONString(response));
        } catch (Exception e){
            log.error("CfRiskService.addLimit EXCEPTION. param:{}", JSON.toJSONString(limitParam), e);
        }
    }

    public boolean operatorValid(long userId, int caseId, UserOperationEnum operationEnum) {
        return queryOperateValid(userId, caseId, operationEnum);
    }

    public boolean queryBlackValid(long userId, CfRiskBlackListEnum.LimitType limitType){
        Response<Boolean> response = blacklistVerifyClient.queryBlackValid(userId, limitType.getValue());

        if(Objects.nonNull(response) && ErrorCode.SUCCESS.getCode() == response.getCode() && Objects.nonNull(response.getData()) && response.getData()){
            return true;
        }

        return false;
    }

    private boolean queryOperateValid(long userId, int caseId, UserOperationEnum operationEnum){
        try {
            Response<UserOperatorValidUnit> response = cfRiskClient.queryOperateValid(userId, caseId, operationEnum);

            boolean action = Objects.nonNull(response) && 0 == response.getCode() && Objects.nonNull(response.getData()) && response.getData().isResult();

            log.info("CfRiskService.call risk queryOperateValid SUCCESS userId:{},caseId:{},action:{},operation:{},result:{}", userId, caseId, action, operationEnum.getMsg(), JSON.toJSONString(response.getData()));

            if(Objects.nonNull(response) && 0 == response.getCode() && Objects.nonNull(response.getData()) && !response.getData().isResult()){
                return false;
            }

            return true;

        } catch (Exception e){
            log.error("CfRiskService.call risk queryOperateValid EXCEPTION userId:{},caseId:{},operation:{}", userId, caseId, operationEnum.getMsg(), e);
            return true;
        }
    }

    //查询该手机号是否在黑名单内，若在，该举报不暂停打款
    //false表示在黑名单内
    public boolean queryPhoneValid(String mobilePhone, PhoneOperateEnum opEnum) {
        if (StringUtils.isEmpty(mobilePhone) || Objects.isNull(opEnum)) {
            return true;
        }
        try {
            Response<Boolean> response = cfRiskBlackListClient.queryPhoneValid(StringUtils.trim(mobilePhone), opEnum);

            boolean action = Objects.nonNull(response) && !response.getData() ? false : true;
            log.info("CfRiskService.queryPhoneValid SUCCESS. phone:{},opEnum:{},action:{}", mobilePhone, opEnum.getValue(), action);
            if (Objects.nonNull(response) && !response.getData()) {
                return false;
            }
            return true;
        } catch (Exception e) {
            log.info("CfRiskService.queryPhoneValid EXCEPTION. phone:{},opEnum:{}", mobilePhone, opEnum.getValue(), e);
        }
        return true;
    }
}
