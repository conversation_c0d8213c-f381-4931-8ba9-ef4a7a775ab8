package com.shuidihuzhu.cf.service.alarm.impl;

import com.shuidihuzhu.cf.dao.admin.CfScheduleAlarmDao;
import com.shuidihuzhu.cf.service.CfScheduleAlarmService;
import com.shuidihuzhu.cf.service.alarm.CfRobotSqlAlarmService;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description: 机器人播报服务
 * @Author: panghairui
 * @Date: 2022/1/10 2:23 下午
 */
@Slf4j
@Service
public class CfRobotSqlAlarmServiceImpl implements CfRobotSqlAlarmService {

    @Resource
    private AlarmClient alarmClient;

    @Resource
    private CfScheduleAlarmService cfScheduleAlarmService;

    @Override
    public void parseTRobotAlarmSql(String content, String sendName) {

        try {
            // 如果是数字，按播报id执行
            if (isNumeric(content)) {

                long alarmId = Long.parseLong(content);
                if (alarmId <= 0) {
                    return;
                }
                log.info("CfRobotSqlAlarmServiceImpl parseTRobotAlarmSql alarmId is {}", alarmId);

                // 执行
                cfScheduleAlarmService.trigger(alarmId);
                return;
            }

            // 按播报title执行
            log.info("CfRobotSqlAlarmServiceImpl parseTRobotAlarmSql title is {}", content);
            cfScheduleAlarmService.triggerByTitle(content);

        } catch (Exception e) {
            String alarmStr = "您的机器人播报执行有误 content:" + content;
            alarmClient.sendByUser(List.of(sendName, "panghairui"), alarmStr);
            log.warn("CfRobotSqlAlarmServiceImpl parseTRobotAlarmSql exception ", e);
        }

    }

    private boolean isNumeric(String str) {
        for (int i = 0; i < str.length(); i++) {
            if (!Character.isDigit(str.charAt(i))) {
                return false;
            }
        }
        return true;
    }
}
