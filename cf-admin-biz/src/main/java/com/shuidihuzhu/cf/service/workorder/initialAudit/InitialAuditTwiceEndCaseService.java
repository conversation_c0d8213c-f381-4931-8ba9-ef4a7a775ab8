package com.shuidihuzhu.cf.service.workorder.initialAudit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseReasonEntityBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseReasonItemBiz;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingOperationDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enums.admin.UserCommentSourceEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonItem;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.CrowdfundingInitialAuditInfo;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditOperationItem;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.admin.model.InitialAuditTwiceEndCaseInfo;
import com.shuidihuzhu.client.cf.workorder.CfChuciWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderRecordClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderTypeFeignClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderRecord;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.ClassifyTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OneTypeEnum;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.constant.CfClientMQTopicCons;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;


@Service
@Slf4j
@RefreshScope
public class InitialAuditTwiceEndCaseService {

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;
    @Autowired
    private ICrowdfundingOperationDelegate crowdfundingOperationDelegate;
    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;
    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Autowired
    private Producer producer;
    @Autowired
    private InitialAuditOperateService auditOperateService;
    @Autowired
    private IRiskDelegate riskDelegate;
    @Autowired
    private CfRefuseReasonEntityBiz entityBiz;
    @Autowired
    private CfRefuseReasonItemBiz reasonItemBiz;
    @Autowired
    private CfWorkOrderRecordClient recordClient;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private CfWorkOrderTypeFeignClient cfWorkOrderTypeFeignClient;

    @Value("${approve.can-twice-end-case-desc:您好，筹款在进一步审核中，请您耐心等待}")
    private String canTwiceEndCaseDesc = "您好，筹款在进一步审核中，请您耐心等待";


    public boolean canTwiceEndCase(int caseId, long workOrderId) {

        // 判断是否是最后一个初审的工单
        Response<WorkOrderVO> orderVOResponse = cfWorkOrderClient.getLastWorkOrderByTypes(caseId,
                Optional.ofNullable(cfWorkOrderTypeFeignClient.getByOneLevel(OneTypeEnum.chuci.getType()))
                .filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList()));

        if (orderVOResponse == null || orderVOResponse.getData() == null
                || orderVOResponse.getData().getWorkOrderId() != workOrderId) {
            log.info("案例不能不能二次结束，当前工单不是caseId的最后一个工单.caseId:{} workOrderId:{} " +
                    "orderVOResponse:{}", caseId, workOrderId, orderVOResponse);
            return false;
        }

        CrowdfundingInitialAuditInfo initialAuditInfo = crowdfundingOperationDelegate.selectCrowdfundingInitialAuditInfoByCaseId(caseId);
        if (initialAuditInfo == null || !initialAuditInfo.getRejectDetail().contains(canTwiceEndCaseDesc)) {

            return false;
        }

        log.info("案例可以二次结束筹款");

        return true;
    }

    public void twiceEndCase(InitialAuditOperationItem.HandleCaseInfoParam param) {

        if (!canTwiceEndCase(param.getCaseId(), param.getWorkOrderId())) {
            throw new RuntimeException("案例不能二次结束筹款，请刷新页面");
        }

        CrowdfundingInitialAuditInfo initialAuditInfo = crowdfundingOperationDelegate.selectCrowdfundingInitialAuditInfoByCaseId(param.getCaseId());
        if (initialAuditInfo == null) {
            throw new RuntimeException("案例不能二次结束筹款，请刷新页面");
        }

        Map<Integer, List<InitialAuditItem.RejectReason>> rejectDetailMap = getRejectDetail(param.getRejectIds().get(0));
        initialAuditInfo.setRejectDetail(JSON.toJSONString(rejectDetailMap));
        crowdfundingOperationDelegate.updateInitialAuditInfo(initialAuditInfo);
        log.info("二次结束案例更新初审数据.param:{}", initialAuditInfo);

        CrowdfundingInfo info = adminCrowdfundingInfoBiz.getFundingInfoById(param.getCaseId());
        List<String> allRejectMsgs = getRejectMsg(rejectDetailMap);

        // 记录日志
        auditOperateService.insertUserCommentLog(param,
                UserCommentSourceEnum.CommentType.INITIAL_AUDIT_TWICE_END_CASE,  "1、" + allRejectMsgs.get(0),
                info != null ? info.getInfoId() : "");

        insertWorkRecordList(param);
        // 发消息
        sendTwiceEndCaseMq(info, Sets.newHashSet(allRejectMsgs));
    }

    private void insertWorkRecordList(InitialAuditOperationItem.HandleCaseInfoParam param) {

        WorkOrderRecord record = new WorkOrderRecord();
        record.setCaseId(param.getCaseId());
        record.setOperatorId(param.getUserId());
        record.setWorkOrderId(param.getWorkOrderId());
        record.setWorkOrderType(param.getOrderType());
        record.setOperateDesc("处理工单");
        record.setComment("二次结束筹款");
        record.setOperateMode(4);

        recordClient.insertWorkOrderRecordList(Lists.newArrayList(record));

        log.info("保存工单日志 param:{}", record);

    }

    private List<String> getRejectMsg(Map<Integer, List<InitialAuditItem.RejectReason>> rejectDetailMap) {
        if (MapUtils.isEmpty(rejectDetailMap)) {
            return Lists.newArrayList();
        }


        List<String> rejectMsgs = Lists.newArrayList();
        for (Map.Entry<Integer, List<InitialAuditItem.RejectReason>> entry : rejectDetailMap.entrySet()) {
            for (InitialAuditItem.RejectReason rejectReason : entry.getValue()) {
                rejectMsgs.add(rejectReason.getRejectMsg());
            }
        }
        return rejectMsgs;
    }

    private Map<Integer, List<InitialAuditItem.RejectReason>> getRejectDetail(int rejectId) {

        Map<Integer, List<InitialAuditItem.RejectReason>> rejectDetail = Maps.newHashMap();

        List<CfRefuseReasonEntity> reasonEntities = entityBiz.selectByIds(Lists.newArrayList(rejectId));
        entityBiz.frequencyPlusOne(Sets.newHashSet(rejectId));
        if (CollectionUtils.isEmpty(reasonEntities)) {
            throw new RuntimeException("只能二次结束案例");
        }

        CfRefuseReasonEntity reasonEntity = reasonEntities.get(0);

        CfRefuseReasonItem reasonItem = reasonItemBiz.getContentById(Integer.valueOf(reasonEntity.getItemIds()));
        InitialAuditItem.EditMaterialType materialType = auditOperateService.getEditTypeByContent(reasonItem.getContent());
        if (!InitialAuditItem.EditMaterialType.SUGGEST_END_CASE.equals(materialType)) {
            throw new RuntimeException("只能二次结束案例");
        }

        List<InitialAuditItem.RejectReason> rejectMsg = Lists.newArrayList(
                new InitialAuditItem.RejectReason(reasonEntity.getId(), reasonEntity.getContent()));
        rejectDetail.put(materialType.getCode(), rejectMsg);

        return rejectDetail;
    }


    private void sendTwiceEndCaseMq(CrowdfundingInfo info, Set<String> rejectMsgs) {

        InitialAuditTwiceEndCaseInfo endCaseInfo = new InitialAuditTwiceEndCaseInfo();
        endCaseInfo.setCaseId(info.getId());
        endCaseInfo.setInfoUuid(info.getInfoId());
        endCaseInfo.setTitle(info.getTitle());
        endCaseInfo.setTargetAmount(info.getTargetAmount());
        endCaseInfo.setApproveCode(FirstApproveStatusEnum.APPLY_FAIL.getCode());
        endCaseInfo.setApproveStr(FirstApproveStatusEnum.APPLY_FAIL.getApproveMsg());
        endCaseInfo.setRejectMsgs(rejectMsgs);

        CfFirsApproveMaterial material = riskDelegate.getCfFirsApproveMaterialByInfoId(info.getId());
        if (material != null) {
            endCaseInfo.setPatientName(material.getPatientRealName());
        }

        UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByUserId(info.getUserId());
        if (userInfoModel != null) {
            endCaseInfo.setMobile(shuidiCipher.decrypt(userInfoModel.getCryptoMobile()));
        }

        endCaseInfo.setCaseRaiseTime(info.getCreateTime() != null ? info.getCreateTime().getTime() : 0);
        endCaseInfo.setInitialRejectTime(new Date().getTime());

        Message msg = new Message(CfClientMQTopicCons.CF, CfClientMQTagCons.INITIAL_AUDIT_TWICE_END_CASE_MSG,
                "" + System.currentTimeMillis(), endCaseInfo);
        MessageResult msgResult = producer.send(msg);

        log.info("二次结束案例的消息发送。param:{} result:{}", msg, msgResult);
    }


}
