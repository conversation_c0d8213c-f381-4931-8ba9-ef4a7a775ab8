package com.shuidihuzhu.cf.service.cfOperatingProfile;

import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileResult;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileSettings;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

public interface CfOperatingProfileSettingsBiz {

    List<CfOperatingProfileResult> queryAllSettingsByType(int profileType);
    List<CfOperatingProfileSettings> querySettingsByTypeAndDataStatus(int parentId, int dataStatus, int profileType);

    AdminErrorCode createProfileSetting(int userId, int parentId, String content, int profileType, Set<String> propertyList);

    AdminErrorCode changeRank(int userId, int upId, int downId, int operateType);

    AdminErrorCode changeDataStatus(int userId, int id, int dataStatus);

    void updateUseSize(Collection<Integer> ids, int size);

    List<CfOperatingProfileSettings> selectByIds(List<Integer> ids);

    List<CfOperatingProfileSettings.ReportProblemSettingsResult> selectReportSettingsByType(int type);

    void updateExtMappings(CfOperatingProfileSettings.ProfileProblemSettings settings);
}
