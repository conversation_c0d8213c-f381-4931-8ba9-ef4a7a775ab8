package com.shuidihuzhu.cf.service.workorder.flow;

import com.shuidihuzhu.cf.vo.crowdfunding.CrowdfundingApproveCommentVo;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-04-29  15:44
 */
public class WorkOrderFlowGroupCommentsComparator implements Comparator<List<CrowdfundingApproveCommentVo>> {

    private static final WorkOrderFlowCommentsComparator C = new WorkOrderFlowCommentsComparator();

    @Override
    public int compare(List<CrowdfundingApproveCommentVo> o1, List<CrowdfundingApproveCommentVo> o2) {
        if (CollectionUtils.isEmpty(o1)) {
            return 0;
        }
        if (CollectionUtils.isEmpty(o2)) {
            return 0;
        }

        // 默认o1 o2都已按时间顺序排过序
        CrowdfundingApproveCommentVo v1 = o1.get(0);
        CrowdfundingApproveCommentVo v2 = o2.get(0);
        return v1.getOprtime().compareTo(v2.getOprtime());
    }
}
