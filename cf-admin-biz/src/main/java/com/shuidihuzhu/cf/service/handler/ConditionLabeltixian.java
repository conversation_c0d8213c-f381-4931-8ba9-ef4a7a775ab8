package com.shuidihuzhu.cf.service.handler;

import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileSettings;
import com.shuidihuzhu.cf.model.event.CailiaoCondition;
import com.shuidihuzhu.cf.model.event.CailiaoConditionEvent;
import com.shuidihuzhu.cf.service.cfOperatingProfile.CfOperatingCaseLabelBiz;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @DATE 2020/3/18
 * 着急提现（手动标记）：手动打上“着急提现”标签的案例
 */
@Component
@Slf4j
public class ConditionLabeltixian implements ConditionLabel{

    //着急提现标签id
    private static final int label_id = 76;

    @Autowired
    private CfOperatingCaseLabelBiz caseLabelBiz;

    @EventListener(classes = CailiaoConditionEvent.class)
    @Override
    public void onApplicationEvent(CailiaoConditionEvent event) {

        List<CfOperatingProfileSettings> list = caseLabelBiz.selectCaseLabels(event.getCaseId());
        log.info("ConditionLabel caseId={} list={}",event.getCaseId(),list);
        Optional<CfOperatingProfileSettings> optional = list.stream().filter(r->r.getId()==label_id).findAny();

        if (optional.isPresent()){
            setResults("着急提现标签",event);
            return;
        }
    }

    @Override
    public int getConditionCode() {
        return CailiaoCondition.condition_1.getCode();
    }
}
