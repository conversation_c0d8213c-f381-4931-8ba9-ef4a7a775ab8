package com.shuidihuzhu.cf.service.crowdfunding;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CfContentImageStatusEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020/4/8
 */
@Slf4j
@Service
public class CfContentImageService {

    private static final String EDIT_COMMENT = "图文混排编辑";
    private static final String FINISH_COMMENT = "退出图文混排";

    @Resource
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;

    @Resource
    private ApproveRemarkOldService approveRemarkOldService;

    @Autowired
    private AdminCfInfoExtBiz cfInfoExtBiz;


    public Response close(long contextUserId, CrowdfundingInfo info) {

        if (info.getContentImageStatus() != CfContentImageStatusEnum.ING.getStatus()) {
            log.info("案例[{}]不能结束图文混排，当前状态:[{}]", info.getId(), info.getContentImageStatus());
            return NewResponseUtil.makeError(AdminErrorCode.CF_CONTENT_IMAGE_STATUS_VALID_FAIL);
        }

        boolean suc = crowdfundingInfoBiz.updateContentImageStatus(info.getId(), info.getContentImageStatus(), CfContentImageStatusEnum.FINISH.getStatus());
        log.info("案例[{}]结束图文混排，sourceStatus:[{}], result:[{}]", info.getId(), info.getContentImageStatus(), suc);
        if (suc) {
            approveRemarkOldService.add(info.getId(), Math.toIntExact(contextUserId), FINISH_COMMENT);
        }
        return NewResponseUtil.makeSuccess(null);
    }

    public Response save(long contextUserId, CrowdfundingInfo info, String contentImage) {
        int sourceStatus = info.getContentImageStatus();
        if (sourceStatus != CfContentImageStatusEnum.INIT.getStatus()
                && sourceStatus != CfContentImageStatusEnum.ING.getStatus()) {
            log.info("案例[{}]不能编辑图文混排，当前状态:[{}]", info.getId(), info.getContentImageStatus());
            return NewResponseUtil.makeError(AdminErrorCode.CF_CONTENT_IMAGE_STATUS_VALID_FAIL);
        }

        CfInfoExt cfInfoExt = cfInfoExtBiz.selectByInfoUuidFromMaster(info.getInfoId());
        boolean casePass = (cfInfoExt != null && FirstApproveStatusEnum.isPassed(FirstApproveStatusEnum.parse(cfInfoExt.getFirstApproveStatus()))) ;
        if (!casePass) {
            log.info("案例[{}]不能编辑图文混排，初审没过, cfInfoExt:[{}]", info.getId(), cfInfoExt);
            return NewResponseUtil.makeError(AdminErrorCode.CF_INITIAL_AUDIT_VALID_FAIL);
        }

        boolean suc = crowdfundingInfoBiz.updateContentImage(info.getId(), contentImage, sourceStatus, CfContentImageStatusEnum.ING.getStatus());
        log.info("案例[{}]编辑图文混排，sourceStatus:[{}], result:[{}]", info.getId(), sourceStatus, suc);
        if (suc) {
            approveRemarkOldService.add(info.getId(), Math.toIntExact(contextUserId), EDIT_COMMENT);
        }
        return NewResponseUtil.makeSuccess(null);
    }
}
