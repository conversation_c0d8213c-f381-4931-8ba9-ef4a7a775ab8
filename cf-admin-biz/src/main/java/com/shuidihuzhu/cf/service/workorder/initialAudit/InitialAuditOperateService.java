package com.shuidihuzhu.cf.service.workorder.initialAudit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.admin.river.RiverHelpService;
import com.shuidihuzhu.cf.admin.river.RiverReviewService;
import com.shuidihuzhu.cf.admin.river.impl.RiverDiBaoFacadeImpl;
import com.shuidihuzhu.cf.admin.river.impl.RiverPinKunFacadeImpl;
import com.shuidihuzhu.cf.biz.admin.ChuciAnalyticsBiz;
import com.shuidihuzhu.cf.biz.admin.UserCommentBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.client.apipure.enums.complaint.ComplaintBizTypeEnum;
import com.shuidihuzhu.cf.client.feign.CaseInfoApproveStageFeignClient;
import com.shuidihuzhu.cf.client.feign.CfUserInfoFeignClient;
import com.shuidihuzhu.cf.client.feign.CfVerificationFeignClient;
import com.shuidihuzhu.cf.client.material.utils.MaterialCollectionUtils;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.client.subject.caseend.CaseEndClient;
import com.shuidihuzhu.cf.client.ugc.caseprocessstatus.model.CaseProcessStatusEnum;
import com.shuidihuzhu.cf.client.ugc.caseprocessstatus.service.CaseProcessStatusClient;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonRecordClient;
import com.shuidihuzhu.cf.constants.AdminUserIDConstants;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCaseDetailsMsgDao;
import com.shuidihuzhu.cf.dao.crowdfunding.CfAiMaterialsDao;
import com.shuidihuzhu.cf.dao.crowdfunding.CfAiMaterialsSmartAIResultDao;
import com.shuidihuzhu.cf.dao.crowdfunding.CfUpgradeWorkOrderRecordDao;
import com.shuidihuzhu.cf.delegate.SeaAccountDelegate;
import com.shuidihuzhu.cf.delegate.commonservice.ICommonServiceDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingOperationDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.BackgroundLogEnum;
import com.shuidihuzhu.cf.enums.InitialAudit.InitialAuditNoSmartReason;
import com.shuidihuzhu.cf.enums.InitialAudit.InitialAuditRiskWorkOrderReason;
import com.shuidihuzhu.cf.enums.NotifyOnlineVolunteerEventEnum;
import com.shuidihuzhu.cf.enums.admin.UserCommentSourceEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.enums.risk.UgcTypeEnum;
import com.shuidihuzhu.cf.enums.visitconfig.VisitConfigLogActionInfoEnum;
import com.shuidihuzhu.cf.enums.visitconfig.VisitConfigSourceEnum;
import com.shuidihuzhu.cf.event.NotifyOnlineVolunteerEvent;
import com.shuidihuzhu.cf.labels.CaseLabelsManagementService;
import com.shuidihuzhu.cf.model.ShowFundationLabel;
import com.shuidihuzhu.cf.model.admin.CfUpgradeWorkOrderRecord;
import com.shuidihuzhu.cf.model.admin.UserComment;
import com.shuidihuzhu.cf.model.ai.CfAiMaterialsSmartAIResult;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterials;
import com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterialsResult;
import com.shuidihuzhu.cf.model.crowdfunding.ai.LayOutField;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.CrowdfundingInitialAuditInfo;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cf.model.risk.verify.RiskUgcVerifyModel;
import com.shuidihuzhu.cf.model.river.RiverReviewDO;
import com.shuidihuzhu.cf.model.river.RiverStatusEnum;
import com.shuidihuzhu.cf.model.river.RiverUsageTypeEnum;
import com.shuidihuzhu.cf.param.InitialAuditCreateOrderParam;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.risk.client.rpc.DiseaseClassifyFeignClientV2;
import com.shuidihuzhu.cf.risk.client.rpc.DiseaseClient;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistCallPhaseEnum;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseClassifyVOV2;
import com.shuidihuzhu.cf.risk.model.risk.diease.RiskDiseaseDataVO;
import com.shuidihuzhu.cf.service.CfCaseWorkOrderService;
import com.shuidihuzhu.cf.service.EventCenterPublishService;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cf.service.crowdfunding.*;
import com.shuidihuzhu.cf.service.crowdfunding.complaint.ComplaintService;
import com.shuidihuzhu.cf.service.record.CfCrowdfundingAttachmentRecordService;
import com.shuidihuzhu.cf.service.workorder.read.WorkOrderReadService;
import com.shuidihuzhu.cf.util.CaseUtils;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditCaseDetail;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditOperationItem;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditOperationMqVO;
import com.shuidihuzhu.cf.vo.approve.InitialAuditAdditionInfoVO;
import com.shuidihuzhu.cf.vo.approve.RiverDetailVO;
import com.shuidihuzhu.cf.vo.approve.RiverHandleParamVO;
import com.shuidihuzhu.cf.vo.crowdfunding.InitialAuditSmartRejectVo;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import com.shuidihuzhu.client.cf.admin.model.AdminCaseDetailsMsg;
import com.shuidihuzhu.client.cf.api.client.CfGovMarkFeignClient;
import com.shuidihuzhu.client.cf.api.model.GovCooperationMark;
import com.shuidihuzhu.client.cf.api.model.enums.GovProjectEnum;
import com.shuidihuzhu.client.cf.clewtrack.model.CfUserInvitedLaunchCaseRecordModel;
import com.shuidihuzhu.client.cf.clewtrack.model.ChannelRefine;
import com.shuidihuzhu.client.cf.workorder.*;
import com.shuidihuzhu.client.cf.workorder.CfChuciWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfUgcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderRecordClient;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCoreFeignClient;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCreateParam;
import com.shuidihuzhu.client.cf.workorder.helper.Von;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.*;
import com.shuidihuzhu.common.util.BeanUtils;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import com.shuidihuzhu.msg.model.SmsTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class InitialAuditOperateService {

    public final static String seriousIllnessDes = "很抱歉，您的筹款已停止，原因为患者疾病不符合水滴筹大病筹款服务范围内";
    @Autowired
    private CfWorkOrderTypeFeignClient cfWorkOrderTypeFeignClient;
    public final static int BASE_INFO_TAG = 1;
    public final static int FIRST_APPROVE_TAG = 100;
    /**
     * 统计用初审图文驳回记录
     */
    public final static int STAT_BASE_INFO_TAG = 101;
    public final static int CREDIT_TAG = CrowdfundingInfoDataStatusTypeEnum.CREDIT_INFO_NEW.getCode();
    public final static int PIN_KUN_TAG = CrowdfundingInfoDataStatusTypeEnum.PIN_KUN_HU.getCode();
    public final static int DI_BAO_TAG = CrowdfundingInfoDataStatusTypeEnum.DI_BAO.getCode();
    private final static int FIFTY_W = 50000000;
    private static final String NO_HANDLE_ALL_TYPE_TIPS = " 未给出审核结果,不可提交";
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private SensitiveWordService senstiveWorkService;
    @Autowired
    private ICrowdfundingDelegate crowdfundingInfoBiz;
    @Autowired
    private UserCommentBiz commentBiz;
    @Autowired
    private ICrowdfundingOperationDelegate crowdfundingOperationDelegate;
    @Autowired
    private CfRefuseReasonEntityBiz entityBiz;
    @Autowired
    private CfChuciWorkOrderClient chuciWorkOrderClient;
    @Autowired
    private CfRefuseReasonItemBiz reasonItemBiz;
    @Autowired
    private Producer producer;
    @Autowired
    private AdminCfInfoExtBiz adminCfInfoExtBiz;
    @Autowired
    private ICommonServiceDelegate commonServiceDelegate;
    @Autowired
    private IRiskDelegate riskDelegate;
    @Autowired
    private AdminCrowdFundingProgressBiz progressBiz;
    @Autowired
    private CfFirstApproveOperatorBiz firstApproveOperatorBiz;
    @Autowired
    private InitialAuditSearchService initialAuditSearchService;
    @Autowired
    private CfAdminOperationRecordBiz cfAdminOperationRecordBiz;
    @Autowired
    private CfMaterialVerityHistoryBiz verityHistoryBiz;
    @Autowired
    private InitialAuditRejectSettingsService settingsService;
    @Autowired
    private CaseEndClient caseEndClient;
    @Autowired
    private AdminApproveService approveService;
    @Autowired
    private AdminCrowdfundingDetailSendMsgTemplateBiz sendMsgTemplateBiz;
    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @Resource
    private CfUgcWorkOrderClient cfUgcWorkOrderClient;
    @Autowired
    private CfSupplyProgressService supplyProgressService;
    @Autowired
    private CfCaseWorkOrderService caseWorkOrderService;
    @Value("${approve.first-approve.success.progress:医疗材料已公示}")
    private String progressContent;
    @Value("${apollo.validate.operate.all:1}")
    private int validateOperateAll;
    @Autowired
    private InitialAuditCreateOrder initialAuditCreateOrder;
    @Resource
    private CfContentImageService cfContentImageService;
    //允许创建医疗工单https://wiki.shuiditech.com/pages/viewpage.action?pageId=343540918
    @Value("${chushen-allow-create-yiliaoshenhe:true}")
    private boolean allowCreateYiliaoWork;
    @Autowired
    private CaseProcessStatusClient caseProcessStatusClient;
    @Autowired
    private CommonOperationRecordClient commonOperationRecordClient;
    @Autowired
    private CaseInfoApproveStageFeignClient caseInfoApproveStageFeignClient;
    @Autowired
    private EventCenterPublishService eventCenterPublishService;
    @Autowired
    private RiverHelpService riverHelpService;
    @Autowired
    private RiverPinKunFacadeImpl riverPinKunFacade;
    @Autowired
    private RiverDiBaoFacadeImpl riverDiBaoFacade;
    @Autowired
    private RiverReviewService riverReviewService;
    @Autowired
    private CfAdminLimitService cfAdminLimitService;
    @Autowired
    private CfAiMaterialsDao cfAiMaterialsDao;
    @Autowired
    private AdminCaseDetailsMsgDao adminCaseDetailsMsgDao;
    @Autowired
    private DiseaseClassifyFeignClientV2 diseaseClassifyFeignClientV2;
    @Autowired
    private DiseaseClient diseaseClient;
    @Resource
    private IUgcOperateRecordService ugcOperateRecordService;

    @Resource
    private CfUserInfoFeignClient userInfoFeignClient;

    @Resource
    private ComplaintService complaintService;

    @Resource
    private WorkOrderReadService workOrderReadService;

    @Autowired
    private CfCrowdfundingAttachmentRecordService cfCrowdfundingAttachmentRecordService;

    @Resource
    private WorkOrderCoreFeignClient workOrderCoreFeignClient;

    @Resource
    private WonRecordClient wonRecordClient;

    @Resource
    private SeaAccountDelegate seaAccountDelegate;

    @Autowired
    private CaseLabelsManagementService caseLabelsManagementService;

    @Resource
    private CfVerificationFeignClient verificationFeignClient;

    @Resource
    private CfWorkOrderRecordClient cfWorkOrderRecordClient;
    @Resource
    private ChuciAnalyticsBiz chuciAnalyticsBiz;
    @Resource
    private CfUpgradeWorkOrderRecordDao cfUpgradeWorkOrderRecordDao;
    @Resource
    private CfAiMaterialsSmartAIResultDao cfAiMaterialsSmartAIResultDao;
    @Resource
    private CfInitialAuditHandleV2ConsumerService cfInitialAuditHandleV2ConsumerService;
    @Resource
    private AlarmClient alarmClient;

    private static int MAX_USER_COMMENT_LENGTH = 1900;

    public static final String HIGH_RISK_EMERGENCY = "von-assign-group:high-risk-emergency";
    public static final String HIGH_RISK_NORMAL = "von-assign-group:high-risk-normal";
    public static List<Integer> chushenWorkReProcessList = Lists.newArrayList();
    private ShowFundationLabel showFundationLabelConfig;

    @Resource
    private CfGovMarkFeignClient cfGovMarkFeignClient;

    @PostConstruct
    public void init() {
        //医院信息工地不影响案例的流程   要排除一下
        chushenWorkReProcessList = Optional.ofNullable(cfWorkOrderTypeFeignClient.getByOneLevel(OneTypeEnum.chuci.getType()))
                .filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList())
                .stream().filter(r -> WorkOrderType.bu_chong_yi_yuan_xin_xi.getType() != r)
                .collect(Collectors.toList());
    }

    @Value("${funding.show.foundation.label.config:}")
    public void setLabelShowConfig(String jsonConfig){
        if (jsonConfig == null) {
            return;
        }
        showFundationLabelConfig = JSON.parseObject(jsonConfig, ShowFundationLabel.class);
    }

    public static InitialAuditItem.EditMaterialType getEditTypeByContent(String content) {
        return InitialAuditItem.EditMaterialType.getEditTypeByDbContent(content);
    }

    public CrowdfundingInfo editCaseBaseInfo(InitialAuditOperationItem.EditBaseInfo editBaseInfo,String changeModify) {

        CrowdfundingInfo cf = crowdfundingInfoBiz.getFundingInfoById(editBaseInfo.getCaseId());
        if (cf == null) {
            int caseId = editBaseInfo.getCaseId();
            log.error("初审工单修改案例，不能找到案例 caseId:{}", caseId);
            return null;
        }

        //添加记录
        cfCrowdfundingAttachmentRecordService.insertBatch(editBaseInfo);

//        adminCaseDetailsMsgService.deleteHeadPictureUrl(editBaseInfo);
        senstiveWorkService.editCaseBaseInfo(cf, editBaseInfo.getTitle(), editBaseInfo.getContent(),
                editBaseInfo.getImgUrls(), editBaseInfo.getUserId());

        // 添加ugc的评论
        UserComment comment = new UserComment();
        comment.setOperatorId(editBaseInfo.getUserId());
        comment.setCaseId(editBaseInfo.getCaseId());
        comment.setCommentSource(UserCommentSourceEnum.INITIAL_AUDIT.getCode());
        comment.setCommentType(UserCommentSourceEnum.CommentType.INITIAL_AUDIT_EDIT.getCode());
        comment.setOperateMode(UserCommentSourceEnum.CommentType.INITIAL_AUDIT_EDIT.getDesc());
        comment.setWorkOrderId(editBaseInfo.getWorkOrderId());
        comment.setComment(subStringByMaxLen(StringUtils.trimToEmpty(editBaseInfo.getReason()), MAX_USER_COMMENT_LENGTH));
        comment.setOperateDesc(subStringByMaxLen(StringUtils.defaultString(changeModify), MAX_USER_COMMENT_LENGTH));
        commentBiz.insert(comment);

        // 详情页的备注
        approveService.addComment(cf.getInfoId(), StringUtils.defaultString(changeModify),
                editBaseInfo.getUserId(), "初次审核", BackgroundLogEnum.INITIAL_AUDIT_HANDLE);

        // 需要读主库
        return crowdfundingInfoBiz.getFundingInfoById(editBaseInfo.getCaseId());
    }

    public Response<Void> handleWorkOrder(InitialAuditOperationItem.HandleCaseInfoParam handleCaseInfoParam) {

        // 自动审核的不校验
        if (handleCaseInfoParam.getSystemAutoAudit() == 0) {
            boolean canNotHandle = workOrderReadService.checkCanNotHandle(handleCaseInfoParam.getWorkOrderId(), handleCaseInfoParam.getUserId());
            if (canNotHandle) {
                return NewResponseUtil.makeError(AdminErrorCode.ORDER_HAS_CALLBACK);
            }
        }

        log.info("初次审核工单处理 处理参数param:{}", handleCaseInfoParam);
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(handleCaseInfoParam.getCaseId());
        CrowdfundingInitialAuditInfo initialAuditInfo = crowdfundingOperationDelegate.selectCrowdfundingInitialAuditInfoByCaseId(handleCaseInfoParam.getCaseId());
        log.info("初次审核工单处理 案例:{}初审状态auditInfo:{}", handleCaseInfoParam.getCaseId(), initialAuditInfo);

        // 对数据做基本的检验
        validateParam(handleCaseInfoParam, crowdfundingInfo, initialAuditInfo);

        //增加工单是否可以处理校验
        Response<WorkOrderVO> orderVOResponse = cfWorkOrderClient.getWorkOrderById(handleCaseInfoParam.getWorkOrderId());
        WorkOrderVO vo = Optional.ofNullable(orderVOResponse).filter(Response::ok).map(Response::getData).orElse(null);
        if (vo == null || (vo.getHandleResult() != HandleResultEnum.doing.getType() && vo.getHandleResult() != HandleResultEnum.later_doing.getType())) {
            throw new RuntimeException("工单已处理完成，请关闭后刷新页面");
        }

        // 处理公约2.0 低保 贫困信息
        RiverHandleParamVO pinKunHandleParam = handleCaseInfoParam.getPinKunHandleParamWithLoaded();
        if (pinKunHandleParam != null) {
            riverPinKunFacade.handle(pinKunHandleParam);
            handleCaseInfoParam.setPinKunComment(riverPinKunFacade.getComment(pinKunHandleParam));
        }
        RiverHandleParamVO diBaoHandleParam = handleCaseInfoParam.getDiBaoHandleParamWithLoaded();
        if (diBaoHandleParam != null) {
            riverDiBaoFacade.handle(diBaoHandleParam);
            handleCaseInfoParam.setDiBaoComment(riverDiBaoFacade.getComment(diBaoHandleParam));
        }

        //判断是否能生成医疗工单
        boolean canCreateYilicao = canCreateYiliaoWork(handleCaseInfoParam);
        log.debug("canCreateYilicao result:{} param:{}", canCreateYilicao, handleCaseInfoParam);

        actualHandleWorkOrder(handleCaseInfoParam, initialAuditInfo, crowdfundingInfo, canCreateYilicao);

        // 操作类型 用于 调用工单和记录日志
        InitialAuditOperationItem.CaseInitialAuditResult auditResult = queryCaseInitialAuditResult(
                handleCaseInfoParam, initialAuditInfo);
        //增加UGC前置审核记录
        insertUserCommentLog(handleCaseInfoParam, auditResult.getOperateType(), getOperateMsg(initialAuditInfo, handleCaseInfoParam),
                crowdfundingInfo.getInfoId());

        //和工单中心交互
        boolean contactWithWorkOrderOk = contactWithWorkOrder(handleCaseInfoParam, auditResult, canCreateYilicao);

        InitialAuditOperationMqVO initialAuditOperationMqVO = new InitialAuditOperationMqVO();
        BeanUtils.copyProperties(handleCaseInfoParam, initialAuditOperationMqVO);
        initialAuditOperationMqVO.setCaseInitialAuditResult(auditResult);
        initialAuditOperationMqVO.setCanCreateMedicalWorkOrder(canCreateYilicao);
        initialAuditOperationMqVO.setContactWithWorkOrderOk(contactWithWorkOrderOk);
        Message<InitialAuditOperationMqVO> message = new Message<>(MQTopicCons.CF, MQTagCons.CF_INITIAL_AUDIT_HANDLE_V2, MQTagCons.CF_INITIAL_AUDIT_HANDLE_V2 + "_" + handleCaseInfoParam.getWorkOrderId(), initialAuditOperationMqVO);
        producer.send(message);
        return NewResponseUtil.makeSuccess(null);
    }

    private void saveAiErForceOp(InitialAuditOperationItem.HandleCaseInfoParam handleParam) {
        if (handleParam.isAiErForceOp()) {
            log.info("工单没有打过电话强制提交结果。workOrderId:{} userId:{}", handleParam.getWorkOrderId(),
                    handleParam.getUserId());
            commonOperationRecordClient.create()
                    .buildBasicPlatform(handleParam.getWorkOrderId(), ContextUtil.getAdminUserId(), OperationActionTypeEnum.CF_AI_ER_FORCE_OP)
                    .buildCaseId(handleParam.getCaseId())
                    .save();
        }
    }

    private void actualHandleWorkOrder(InitialAuditOperationItem.HandleCaseInfoParam handleCaseInfoParam,
                                       CrowdfundingInitialAuditInfo initialAuditInfo,
                                       CrowdfundingInfo crowdfundingInfo,
                                       boolean canCreateYilicao) {

        if (unableInitialAudit(handleCaseInfoParam)) {
            log.info("操作类型是回访处理或延后处理不需要处理案例的初审状态, caseId:{}, workOrderId:{}", handleCaseInfoParam.getCaseId(), handleCaseInfoParam.getWorkOrderId());
            return;
        }

        handleInitialStatus(handleCaseInfoParam, initialAuditInfo);
        boolean casePass = isCasePass(initialAuditInfo);
        //创建医疗工单，不要改变案例初审状态
        if (!canCreateYilicao) {
            FirstApproveStatusEnum statusEnum = casePass ? FirstApproveStatusEnum.APPLY_SUCCESS : FirstApproveStatusEnum.APPLY_FAIL;
            adminCfInfoExtBiz.updateApproveStatus(crowdfundingInfo, statusEnum);
        }

        CfOperationRecordEnum typeEnum = casePass ? CfOperationRecordEnum.FIRST_APPROVE_SUCCESS : CfOperationRecordEnum.FIRST_APPROVE_FAIL;
        cfAdminOperationRecordBiz.addOneOperationRecord(crowdfundingInfo.getInfoId(), handleCaseInfoParam.getUserId(), typeEnum.value(), handleCaseInfoParam.getCallComment());

        int caseId = crowdfundingInfo.getId();
        if (casePass) {

            commonServiceDelegate.touchSetCanShareAndDonateWithAdminUser(crowdfundingInfo.getId(), handleCaseInfoParam.getUserId());
            commonServiceDelegate.pin(caseId,
                    VisitConfigSourceEnum.FIRST_APPROVE,
                    ImmutableList.of(
                            VisitConfigLogActionInfoEnum.UNLOCK_SHARE,
                            VisitConfigLogActionInfoEnum.UNLOCK_DONATION
                    ),
                    ContextUtil.getAdminUserId());

            // sendProgressAfterInitialAuditPass(crowdfundingInfo, handleCaseInfoParam);

            Date now = new Date();
            //默认都是开始筹款30天
            crowdfundingInfoBiz.updateBeginAndEndTime(crowdfundingInfo.getId(), now, DateUtils.addDays(now, 30));
            //消息发给事件中心
            eventCenterPublishService.sendFirstApprove(crowdfundingInfo);
            //保存案例头图
            caseProcessStatusClient.update(caseId, CaseProcessStatusEnum.CAN_SHARE);
            //发穗救易案例消息
            sendSuiJiuYiMsgByFeiShu(caseId);

            cfContentImageService.close(handleCaseInfoParam.getUserId(), crowdfundingInfo);

            //重复发起案例自动标记举报
            sendRepeatCaseAutoReportMq(new Message<>(MQTopicCons.CF, MQTagCons.REPEAT_THE_CASE_AUTO_REPORT, MQTagCons.REPEAT_THE_CASE_AUTO_REPORT + "_" + caseId, crowdfundingInfo));
            //潮普案例打标签
            addLabelForCase(crowdfundingInfo);
        } else {
            commonOperationRecordClient.create()
                    .buildBasicPlatform(caseId, ContextUtil.getAdminUserId(), OperationActionTypeEnum.REFUSE_INITIAL_AUDIT)
                    .save();
            sendMsgToVolunteer(handleCaseInfoParam);
        }

        // 发mq消息,创建医疗工单前需要给用户发送消息
        if (!canCreateYilicao) {
            sendCaseOperateMsg(handleCaseInfoParam, initialAuditInfo);
            handleCaseEnd(handleCaseInfoParam, crowdfundingInfo);
        } else {
            //给用户发送type为13的消息
            eventCenterPublishService.sendNoMatchSeriousIllness(crowdfundingInfo);
        }

        if (initialAuditInfo.getCreditInfo() == InitialAuditItem.MaterialStatus.REJECT.getCode()) {
            // 审核记录
            addHistory(handleCaseInfoParam, initialAuditInfo, crowdfundingInfo, InitialAuditOperateService.CREDIT_TAG);
        }
        if (initialAuditInfo.getFirstApproveInfo() == InitialAuditItem.MaterialStatus.REJECT.getCode()) {
            // 审核记录
            addHistory(handleCaseInfoParam, initialAuditInfo, crowdfundingInfo, InitialAuditOperateService.FIRST_APPROVE_TAG);
        }
        if (initialAuditInfo.getBaseInfo() == InitialAuditItem.MaterialStatus.REJECT.getCode()) {
            // 审核记录
            addHistory(handleCaseInfoParam, initialAuditInfo, crowdfundingInfo, InitialAuditOperateService.STAT_BASE_INFO_TAG);
        }

        // 所有项的处理情况
        verityHistoryBiz.totalRecordInitialAudit(handleCaseInfoParam);
    }

    private void sendSuiJiuYiMsgByFeiShu(Integer caseId) {

        Response<GovCooperationMark> response = cfGovMarkFeignClient.getMarkRecord(caseId, GovProjectEnum.SUI_JIU_YI.getCode());
        GovCooperationMark govCooperationMark = Optional.ofNullable(response)
                        .filter(Response::ok)
                        .map(Response::getData)
                        .orElse(null);
        if (Objects.isNull(govCooperationMark)) {
            return;
        }

        if (govCooperationMark.getMarkType() == 1) {
            AlarmBotService.sentText("da0bfea9-9aad-46db-8ac3-3e174a4432a4", "2G-广州渠道发起1个案例，案例ID："+ caseId + "，请前往SEA后台打标", null, null);
        }

    }

    @Async("materialVerityHistoryExecutor")
    public void addHistory(InitialAuditOperationItem.HandleCaseInfoParam handleCaseInfoParam, CrowdfundingInitialAuditInfo initialAuditInfo, CrowdfundingInfo crowdfundingInfo, int dataType) {
        handleCaseInfoParam = JSON.parseObject(JSON.toJSONString(handleCaseInfoParam), InitialAuditOperationItem.HandleCaseInfoParam.class);//已检查过
        handleCaseInfoParam.setRejectIds(getRejectIds(dataType, handleCaseInfoParam.getRejectIds()));

        InitialAuditCaseDetail detail = initialAuditSearchService.queryInitialAuditInfoRealTime(handleCaseInfoParam.getCaseId());
        verityHistoryBiz.insertByType(handleCaseInfoParam, crowdfundingInfo.getInfoId(),
                JSON.toJSONString(detail),
                isCasePass(initialAuditInfo) ? CfMaterialVerityHistory.PASS_TYPE : CfMaterialVerityHistory.REJECT_TYPE,
                dataType);
    }

    /**
     * 保存案例头图
     *
     * @param caseId
     * @param caseBaseInfo
     */
    private void saveCaseHeadPictureUrl(int caseId, InitialAuditCaseDetail.CaseBaseInfo caseBaseInfo) {
        if (StringUtils.isBlank(caseBaseInfo.getHeadPictureUrl())) {
            return;
        }
        String headPictureUrl = caseBaseInfo.getHeadPictureUrl();
        String infoUuid = caseBaseInfo.getInfoUUid();
        AdminCaseDetailsMsg adminCaseDetailsMsg = adminCaseDetailsMsgDao.getByCaseId(caseId);
        if (Objects.isNull(adminCaseDetailsMsg)) {
            adminCaseDetailsMsgDao.addCaseDetailsMsg(caseId, infoUuid, headPictureUrl, "", "", "", 0);
        } else {
            adminCaseDetailsMsgDao.updateHeadPictureUrl(caseId, headPictureUrl);
        }
    }

    private void sendMsgToVolunteer(InitialAuditOperationItem.HandleCaseInfoParam handleCaseInfoParam) {
        int caseId = handleCaseInfoParam.getCaseId();
        if (handleCaseInfoParam.getOrderType() == WorkOrderType.ai_erci.getType()) {
            applicationContext.publishEvent(new NotifyOnlineVolunteerEvent(NotifyOnlineVolunteerEventEnum.ER_CI_WORK_ORDER_REJECT_EVENT, caseId));
        }

        if (handleCaseInfoParam.getOrderType() == WorkOrderType.yiliaoshenhe.getType()) {
            applicationContext.publishEvent(new NotifyOnlineVolunteerEvent(NotifyOnlineVolunteerEventEnum.MEDICAL_WORK_ORDER_REJECT_EVENT, caseId));
        }
    }

    private void sendRepeatCaseAutoReportMq(Message message) {
        if (producer == null) {
            return;
        }
        try {
            producer.send(message);
        } catch (Exception e) {
            log.error("sendRepeatCaseAutoReportMq error. message:{}", message, e);
        }
    }

    private void addLabelForCase(CrowdfundingInfo crowdfundingInfo){
        caseLabelsManagementService.addLabelForCase(crowdfundingInfo);
    }

    private void handleCaseEnd(InitialAuditOperationItem.HandleCaseInfoParam handleCaseInfoParam,
                               CrowdfundingInfo cf) {
        if (handleCaseInfoParam.getHandleType() != InitialAuditOperationItem.HandleTypeEnum.END_CASE.getCode()) {
            return;
        }

        caseEndClient.stopCase(cf.getId(), CfFinishStatus.FINISH_BY_SHUIDI, ContextUtil.getAdminUserId(), "初审停止筹款");

        Message msg = new Message(MQTopicCons.CF, MQTagCons.WORK_ORDER_END_CASE, "" + handleCaseInfoParam.getWorkOrderId(), handleCaseInfoParam, DelayLevel.S5);
        MessageResult result = producer.send(msg);
        log.info("工单停止筹款发送消息. msg:{} result:{}", msg, result);
    }


    public InitialAuditCaseDetail prepareCaseDetailSnapshot(InitialAuditOperationItem.HandleCaseInfoParam handleCaseInfoParam) {

        InitialAuditCaseDetail caseDetail = initialAuditSearchService.queryCaseDetail(handleCaseInfoParam.getWorkOrderId(),
                handleCaseInfoParam.getCaseId());

        InitialAuditCaseDetail.CaseBaseInfo caseBaseInfo = caseDetail.getCaseBaseInfo();
        caseBaseInfo.setCallStatus(handleCaseInfoParam.getCallStatus());
        caseBaseInfo.setPass(handleCaseInfoParam.getPassIds().contains(BASE_INFO_TAG) ? 1 : 0);
        caseBaseInfo.setRejectIds(getRejectIds(InitialAuditOperateService.BASE_INFO_TAG,
                handleCaseInfoParam.getRejectIds()));

        InitialAuditCaseDetail.FirstApproveCaseInfo firstApproveCaseInfo = caseDetail.getFirstApproveCaseInfo();
        firstApproveCaseInfo.setPass(handleCaseInfoParam.getPassIds().contains(FIRST_APPROVE_TAG) ? 1 : 0);
        firstApproveCaseInfo.setRejectIds(getRejectIds(InitialAuditOperateService.FIRST_APPROVE_TAG,
                handleCaseInfoParam.getRejectIds()));

        InitialAuditCaseDetail.CreditInfo creditInfo = caseDetail.getCreditInfo();
        if (creditInfo != null) {
            creditInfo.setPass(handleCaseInfoParam.getPassIds().contains(CREDIT_TAG) ? 1 : 0);
            creditInfo.setRejectIds(getRejectIds(InitialAuditOperateService.CREDIT_TAG,
                    handleCaseInfoParam.getRejectIds()));
        }

        return caseDetail;
    }

    private List<Integer> getRejectIds(int dataType, List<Integer> rejectIds) {

        List<Integer> result = Lists.newArrayList();

        // BUG-FIX 图文的驳回项 没有材料id 101
        if (dataType == InitialAuditOperateService.STAT_BASE_INFO_TAG) {
            dataType = InitialAuditOperateService.BASE_INFO_TAG;
        }

        List<CfRefuseReasonTag> reasonTags = settingsService.queryDataTypeList(dataType);
        if (CollectionUtils.isEmpty(rejectIds) || CollectionUtils.isEmpty(reasonTags)) {
            return result;
        }

        Set<Integer> totalEntityIds = Sets.newHashSet();
        reasonTags.stream()
                .map(CfRefuseReasonTag::getEntityList)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .map(CfRefuseReasonEntity::getId)
                .forEach(totalEntityIds::add);

        rejectIds.stream()
                .filter(totalEntityIds::contains)
                .forEach(result::add);
        return result;
    }

    // 与工单中心的交互
    private boolean contactWithWorkOrder(InitialAuditOperationItem.HandleCaseInfoParam handleCaseInfoParam, InitialAuditOperationItem.CaseInitialAuditResult auditResult, boolean canCreateYilicao) {

        ChuciHandleOrderParam workOrderParam = new ChuciHandleOrderParam();

        workOrderParam.setWorkOrderId(handleCaseInfoParam.getWorkOrderId());
        workOrderParam.setUserId(handleCaseInfoParam.getUserId());
        workOrderParam.setOperComment(handleCaseInfoParam.getHandleComment());
        workOrderParam.setCaseId(handleCaseInfoParam.getCaseId());
        workOrderParam.setOrderType(handleCaseInfoParam.getOrderType());
        if (handleCaseInfoParam.getCallStatus() != 0) {
            workOrderParam.setCallStatus("" + handleCaseInfoParam.getCallStatus());
        }
        if (handleCaseInfoParam.getUserCallStatus() != 0) {
            workOrderParam.setUserCallStatus(handleCaseInfoParam.getUserCallStatus() + "");
        }

        int handleResult = handleCaseInfoParam.getSystemAutoAudit() == 1 ? HandleResultEnum.smart_audit_pass.getType() : auditResult.getHandleResult();
        workOrderParam.setHandleResult(handleResult);
        Response<Map<Integer, Long>> result = chuciWorkOrderClient.hanldeChuciV2(workOrderParam);
        log.info("初次审核调用工单系统. workOrderParam:{}, Response:{} ", workOrderParam, JSON.toJSONString(result));
        saveAiErForceOp(handleCaseInfoParam);
        if (result.notOk()) {
            if (result.getCode() == 2001) {
                throw new RuntimeException(result.getMsg());
            }
            throw new RuntimeException("此任务工单已自动异常关闭，无需处理");
        }
        return result.ok();
    }

    // 给C端发消息
    private void sendCaseOperateMsg(InitialAuditOperationItem.HandleCaseInfoParam handleParam, CrowdfundingInitialAuditInfo initialAuditInfo) {
        InitialAuditItem.InitialAuditOperation auditOperation = new InitialAuditItem.InitialAuditOperation();
        auditOperation.setCaseId(handleParam.getCaseId());
        auditOperation.setWorkOrderId(handleParam.getWorkOrderId());
        List<Integer> passIds = Lists.newArrayList();

        // 通过的
        if (initialAuditInfo.getBaseInfo() == InitialAuditItem.MaterialStatus.PASS.getCode()) {
            passIds.add(InitialAuditItem.CASE_BASE_INFO_MATERIAL);
        }
        if (initialAuditInfo.getFirstApproveInfo() == InitialAuditItem.MaterialStatus.PASS.getCode()) {
            passIds.add(InitialAuditItem.FIRST_APPROVE_INFO);
        }
        if (initialAuditInfo.getCreditInfo() == InitialAuditItem.MaterialStatus.PASS.getCode()) {
            passIds.add(InitialAuditItem.CREDIT_INFO);
        }
        auditOperation.setPassIds(passIds);

        auditOperation.setRejectItems(crowdfundingOperationDelegate
                .parseRejectDetail(initialAuditInfo.getRejectDetail(), handleParam.getCaseId()).getRejectMsgs());
        Message msg = new Message(MQTopicCons.CF, com.shuidihuzhu.cf.constants.MQTagCons.CF_INITIAL_AUDIT_OPERATION_MSG,
                "" + handleParam.getWorkOrderId(),
                auditOperation, DelayLevel.S5);
        MessageResult result = producer.send(msg);
        log.info("初审操作消息发送. msg:{} result:{}", msg, result);

        // 前置召回的mq
        if (!isCasePass(initialAuditInfo) && !auditOperation.getRejectItems().containsKey(InitialAuditItem.EditMaterialType.SUGGEST_END_CASE.getCode())) {
            firstApproveOperatorBiz.sendReCallMsgAfterReject(handleParam.getCaseId(), handleParam.getWorkOrderId(),
                    MQTagCons.ADMIN_INITIAL_AUDIT_RECALL_SUBMIT_AFTER_REJECT);
        }
    }

    private boolean unableInitialAudit(InitialAuditOperationItem.HandleCaseInfoParam handleCaseInfoParam) {

        return handleCaseInfoParam.getHandleType() == InitialAuditOperationItem.HandleTypeEnum.RETURN_VISIT.getCode()
                || handleCaseInfoParam.getHandleType() == InitialAuditOperationItem.HandleTypeEnum.DELAY_HANDLE.getCode();
    }

    // 查询操作的类型 handleResult--用于调用双哥      OperateType --记录comment
    private InitialAuditOperationItem.CaseInitialAuditResult queryCaseInitialAuditResult(
            InitialAuditOperationItem.HandleCaseInfoParam handleCaseInfoParam,
            CrowdfundingInitialAuditInfo initialAuditInfo) {

        InitialAuditOperationItem.CaseInitialAuditResult auditResult = new InitialAuditOperationItem.CaseInitialAuditResult();
        InitialAuditOperationItem.HandleTypeEnum currentOperate = InitialAuditOperationItem.HandleTypeEnum
                .parseCode(handleCaseInfoParam.getHandleType());

        switch (currentOperate) {
            case RETURN_VISIT:
                auditResult.setHandleResult(HandleResultEnum.return_call.getType());
                auditResult.setOperateType(UserCommentSourceEnum.CommentType.INITIAL_AUDIT_RETURN_VISIT);
                break;
            case DELAY_HANDLE:
                auditResult.setHandleResult(HandleResultEnum.later_doing.getType());
                auditResult.setOperateType(UserCommentSourceEnum.CommentType.INITIAL_AUDIT_DELAY_HANDLE);
                break;
            case END_CASE:
                auditResult.setHandleResult(HandleResultEnum.stop_case.getType());
                auditResult.setOperateType(UserCommentSourceEnum.CommentType.INITIAL_AUDIT_END_CASE);
                break;
            case SUBMIT:
                if (handleCaseInfoParam.getOrderType() == WorkOrderType.bohui.getType()) {
                    auditResult.setHandleResult(HandleResultEnum.done.getType());
                    auditResult.setOperateType(UserCommentSourceEnum.CommentType.REJECT_SHUTDOWN);
                } else {
                    auditResult.setOperateType(isCasePass(initialAuditInfo) ?
                            UserCommentSourceEnum.CommentType.INITIAL_AUDIT_PASS : UserCommentSourceEnum.CommentType.INITIAL_AUDIT_REJECT);
                    auditResult.setHandleResult(isCasePass(initialAuditInfo) ?
                            HandleResultEnum.audit_pass.getType() : HandleResultEnum.audit_reject.getType());
                }

                break;
            default:
                throw new RuntimeException("操作类型不合法");
        }

        return auditResult;
    }

    private String getOperateMsg(CrowdfundingInitialAuditInfo initialAuditInfo, InitialAuditOperationItem.HandleCaseInfoParam handleCaseInfoParam) {

        List<String> operateMsg = Lists.newArrayList();

        if (handleCaseInfoParam.getHandleType() == InitialAuditOperationItem.HandleTypeEnum.RETURN_VISIT.getCode()) {
            return "";
        }

        if (initialAuditInfo.getBaseInfo() == InitialAuditItem.MaterialStatus.PASS.getCode() &&
                initialAuditInfo.getFirstApproveInfo() == InitialAuditItem.MaterialStatus.PASS.getCode() &&
                initialAuditInfo.getCreditInfo() == InitialAuditItem.MaterialStatus.PASS.getCode()
        ) {

            operateMsg.add(handleCaseInfoParam.getSystemAutoAudit() == 1 ? "系统自动审核通过" : "初审通过");
            //增加记录呼通状态
            if (handleCaseInfoParam.getCallStatus() > 0) {
                String call = handleCaseInfoParam.getCallStatus() == 1 ? "呼通" : "未呼通";
                operateMsg.add("呼通状态:" + call);
            }

            return Joiner.on("<br>").join(operateMsg);
        }

        //驳回工单时   仅记录呼通状态 不记录驳回项
        if (handleCaseInfoParam.getOrderType() == WorkOrderType.bohui.getType()) {
            if (handleCaseInfoParam.getCallStatus() > 0) {
                return "呼通状态:" + (handleCaseInfoParam.getCallStatus() == 1 ? "呼通" : "未呼通");
            }
            return "";
        }

        if (initialAuditInfo.getBaseInfo() == InitialAuditItem.MaterialStatus.PASS.getCode()) {
            operateMsg.add("图文信息：通过");
        }

        if (initialAuditInfo.getFirstApproveInfo() == InitialAuditItem.MaterialStatus.PASS.getCode()) {
            operateMsg.add("前置信息：通过");
        }

        if (initialAuditInfo.getCreditInfo() == InitialAuditItem.MaterialStatus.PASS.getCode()) {
            operateMsg.add("增信信息：通过");
        }

        if (CollectionUtils.isNotEmpty(handleCaseInfoParam.getRejectIds())) {
            List<InitialAuditOperationItem.SortedReasonEntity> sortedReasonEntitys =
                    settingsService.selectSortEntity(handleCaseInfoParam.getRejectIds());

            for (InitialAuditOperationItem.SortedReasonEntity reasonEntity : sortedReasonEntitys) {

                if (handleCaseInfoParam.getHandleType() != InitialAuditOperationItem.HandleTypeEnum.END_CASE.getCode()) {
                    int dataType = reasonEntity.getDataType();
                    String msg = "";
                    if (dataType == BASE_INFO_TAG) {
                        msg = "图文信息：驳回";
                    } else if (dataType == FIRST_APPROVE_TAG) {
                        msg = "前置信息：驳回";
                    } else if (dataType == InitialAuditItem.CREDIT_INFO) {
                        msg = "增信信息：驳回";
                    }
                    operateMsg.add(msg);
                }
                int line = 0;
                for (CfRefuseReasonEntity currentEntity : reasonEntity.getSortedReasonEntities()) {
                    if (currentEntity.isCustomType() && MapUtils.isNotEmpty(handleCaseInfoParam.getCustomRefuseReason())) {
                        operateMsg.add(++line + "、" + Objects.requireNonNullElse(handleCaseInfoParam.getCustomRefuseReason().get(currentEntity.getId()), currentEntity.getContent()));
                    } else {
                        operateMsg.add(++line + "、" + currentEntity.getContent());
                    }
                }
            }
        }

        CollectionUtils.addIgnoreNull(operateMsg, handleCaseInfoParam.getDiBaoComment());
        CollectionUtils.addIgnoreNull(operateMsg, handleCaseInfoParam.getPinKunComment());

        //回访工单时   增加记录呼通状态
        if (handleCaseInfoParam.getCallStatus() > 0) {
            String call = handleCaseInfoParam.getCallStatus() == 1 ? "呼通" : "未呼通";
            operateMsg.add("呼通状态:" + call);
        }
        return Joiner.on("<br>").join(operateMsg);
    }

    public boolean isCasePass(CrowdfundingInitialAuditInfo initialAuditInfo) {
        if (initialAuditInfo.getBaseInfo() != InitialAuditItem.MaterialStatus.PASS.getCode()) {
            return false;
        }
        if (initialAuditInfo.getFirstApproveInfo() != InitialAuditItem.MaterialStatus.PASS.getCode()) {
            return false;
        }
        return initialAuditInfo.getCreditInfo() == InitialAuditItem.MaterialStatus.PASS.getCode() ||
                initialAuditInfo.getCreditInfo() == InitialAuditItem.MaterialStatus.DEFAULT.getCode();
    }

    private void handleInitialStatus(InitialAuditOperationItem.HandleCaseInfoParam handleCaseInfoParam,
                                     CrowdfundingInitialAuditInfo auditInfo) {

        auditInfo.setWorkOrderId(handleCaseInfoParam.getWorkOrderId());
        handlePassIds(handleCaseInfoParam, auditInfo);
        handleRejectIds(handleCaseInfoParam, auditInfo);

        crowdfundingOperationDelegate.updateInitialAuditInfo(auditInfo);
        log.info("更新案例的初审状态 auditInfo:{}", JSON.toJSONString(auditInfo));
    }

    private void handlePassIds(InitialAuditOperationItem.HandleCaseInfoParam handleCaseInfoParam,
                               CrowdfundingInitialAuditInfo auditInfo) {

        if (CollectionUtils.isNotEmpty(handleCaseInfoParam.getPassIds())) {

            if (handleCaseInfoParam.getPassIds().contains(BASE_INFO_TAG)) {
                auditInfo.setBaseInfo(InitialAuditItem.MaterialStatus.PASS.getCode());

                try {
                    cfCrowdfundingAttachmentRecordService.insertBatchAudit(auditInfo.getCaseId());
                } catch (Exception e) {
                    log.error("保存图片日志失败 caseId:{}", handleCaseInfoParam.getCaseId(), e);
                }

                // 初审图文通过时 写入暂存图文到原表
                caseInfoApproveStageFeignClient.commitStage(auditInfo.getCaseId());
            }

            if (handleCaseInfoParam.getPassIds().contains(FIRST_APPROVE_TAG)) {
                auditInfo.setFirstApproveInfo(InitialAuditItem.MaterialStatus.PASS.getCode());
            }

            if (handleCaseInfoParam.getPassIds().contains(CREDIT_TAG)) {
                auditInfo.setCreditInfo(InitialAuditItem.MaterialStatus.PASS.getCode());
            }
        }
    }

    private void handleRejectIds(InitialAuditOperationItem.HandleCaseInfoParam handleCaseInfoParam,
                                 CrowdfundingInitialAuditInfo auditInfo) {

        Map<Integer, List<InitialAuditItem.RejectReason>> rejectDetail = Maps.newHashMap();

        if (CollectionUtils.isEmpty(handleCaseInfoParam.getRejectIds())) {
            auditInfo.setRejectDetail(JSON.toJSONString(rejectDetail));
            return;
        }

        // 驳回子项
        List<CfRefuseReasonEntity> reasonEntities = entityBiz.selectByIds(handleCaseInfoParam.getRejectIds());
        entityBiz.frequencyPlusOne(Sets.newHashSet(handleCaseInfoParam.getRejectIds()));
        //理由id--->C端驳回位置
        Map<Integer, List<InitialAuditItem.EditMaterialType>> allTypeMappings = getMaterialTypeMapping(reasonEntities);
        for (CfRefuseReasonEntity reasonEntity : reasonEntities) {

            List<InitialAuditItem.EditMaterialType> materialTypes = allTypeMappings.get(reasonEntity.getId());
            if (CollectionUtils.isEmpty(materialTypes)) {
                continue;
            }

            for (InitialAuditItem.EditMaterialType materialType : materialTypes) {

                String content = MapUtils.isEmpty(handleCaseInfoParam.getCustomRefuseReason()) ? reasonEntity.getContent() : Objects.requireNonNullElse(handleCaseInfoParam.getCustomRefuseReason().get(reasonEntity.getId()), reasonEntity.getContent());
                MaterialCollectionUtils.putValueToList(rejectDetail, materialType.getCode(),
                        new InitialAuditItem.RejectReason(reasonEntity.getId(), content));

                if (InitialAuditItem.EditMaterialType.isCaseBaseInfo(materialType)) {
                    auditInfo.setBaseInfo(InitialAuditItem.MaterialStatus.REJECT.getCode());
                }

                if (InitialAuditItem.EditMaterialType.isFirstApproveInfo(materialType)) {
                    auditInfo.setFirstApproveInfo(InitialAuditItem.MaterialStatus.REJECT.getCode());
                }

                if (InitialAuditItem.EditMaterialType.isCreditInfo(materialType)) {
                    auditInfo.setCreditInfo(InitialAuditItem.MaterialStatus.REJECT.getCode());
                }
            }
        }

        auditInfo.setRejectDetail(JSON.toJSONString(rejectDetail));
    }

    private Map<Integer, List<InitialAuditItem.EditMaterialType>> getMaterialTypeMapping(List<CfRefuseReasonEntity> reasonEntitys) {

        Map<Integer, List<InitialAuditItem.EditMaterialType>> allMaterialTypes = Maps.newHashMap();
        if (CollectionUtils.isEmpty(reasonEntitys)) {
            log.error("驳回理由为空");
            return allMaterialTypes;
        }

        List<CfRefuseReasonItem> reasonItems = reasonItemBiz.selectByIds(getAllRejectIds(reasonEntitys));
        if (CollectionUtils.isEmpty(reasonItems)) {
            log.error("驳回理由关联了错误的驳回位置.ids:{}", JSON.toJSONString(reasonEntitys));
            return allMaterialTypes;
        }

        for (CfRefuseReasonEntity item : reasonEntitys) {
            Set<Integer> itemIds = Sets.newHashSet(AdminWorkOrderFlow.getIdListSplitterByComma(item.getItemIds()));

            for (CfRefuseReasonItem reasonItem : reasonItems) {
                if (!itemIds.contains(reasonItem.getId())) {
                    continue;
                }

                InitialAuditItem.EditMaterialType materialType = getEditTypeByContent(reasonItem.getContent());
                if (materialType == null) {
                    log.error("当前驳回文案不能找到对应驳回位置.item:{}", JSON.toJSONString(reasonItem));
                    continue;
                }

                MaterialCollectionUtils.putValueToList(allMaterialTypes, item.getId(), materialType);
            }
        }

        return allMaterialTypes;
    }

    private Set<Integer> getAllRejectIds(List<CfRefuseReasonEntity> reasonEntitys) {
        Set<Integer> rejectSets = Sets.newHashSet();
        for (CfRefuseReasonEntity item : reasonEntitys) {
            rejectSets.addAll(AdminWorkOrderFlow.getIdListSplitterByComma(item.getItemIds()));
        }
        return rejectSets;
    }

    // 检验前端的数据
    private void validateParam(InitialAuditOperationItem.HandleCaseInfoParam handleCaseInfoParam, CrowdfundingInfo cf,
                               CrowdfundingInitialAuditInfo initialAuditInfo) {

        if (cf == null || cf.getEndTime().before(new Date())) {
            log.info("案例已结束,不处理工单.caseId:{}", handleCaseInfoParam.getCaseId());
            throw new RuntimeException("案例已经结束，不处理工单，请刷新页面");
        }

        if (initialAuditInfo == null) {
            throw new RuntimeException("案例不能找到CrowdfundingInitialAuditInfo。caseId:" + handleCaseInfoParam.getCaseId());
        }
        if (isCasePass(initialAuditInfo) && (CollectionUtils.isNotEmpty(handleCaseInfoParam.getRejectIds()) ||
                CollectionUtils.isNotEmpty(handleCaseInfoParam.getPassIds()))) {
            throw new RuntimeException("案例已是审核通过，不能在继续通过或驳回或结束筹款");
        }

        // 对提交的类型检验
        validateHandleType(handleCaseInfoParam);

        // 对工单的类型检验
        validateOrderType(handleCaseInfoParam);

        // 不能同时提交和 通过同一项 且 所有模块必须要有给出审核结果
        validatePassAndRejectIds(handleCaseInfoParam, initialAuditInfo);

        // 对于患者身份证相关的检验
        validatePatientCard(handleCaseInfoParam, cf);
    }

    private void validatePatientCard(InitialAuditOperationItem.HandleCaseInfoParam handleCaseInfoParam, CrowdfundingInfo cf) {
        CfFirsApproveMaterial material = riskDelegate.getCfFirsApproveMaterialByInfoId(cf.getId());
        if (material == null) {
            log.error("初审处理不能找到案例的身份证信息:{}", cf.getId());
            throw new RuntimeException("不能找到初审信息");
        }

        if (material.getStatus() == FirstApproveIdcardVerifyStatusEnum.NOT_MATCH.getCode()
                && handleCaseInfoParam.getHandleType() == InitialAuditOperationItem.HandleTypeEnum.SUBMIT.getCode()
                && CollectionUtils.isEmpty(handleCaseInfoParam.getRejectIds())) {
            throw new RuntimeException("患者的身份证检验不通过，不允许审核通过");
        }
    }

    private void validateHandleType(InitialAuditOperationItem.HandleCaseInfoParam handleCaseInfoParam) {
        if (handleCaseInfoParam.getHandleType() == InitialAuditOperationItem.HandleTypeEnum.RETURN_VISIT.getCode()
                || handleCaseInfoParam.getHandleType() == InitialAuditOperationItem.HandleTypeEnum.DELAY_HANDLE.getCode()) {
            if (CollectionUtils.isNotEmpty(handleCaseInfoParam.getPassIds())
                    || CollectionUtils.isNotEmpty(handleCaseInfoParam.getRejectIds())) {
                throw new RuntimeException("点击回访处理或稍后处理时，不能通过或驳回案例");
            }
        }

        if (handleCaseInfoParam.getHandleType() == InitialAuditOperationItem.HandleTypeEnum.END_CASE.getCode()) {
            if (CollectionUtils.isEmpty(handleCaseInfoParam.getRejectIds())
                    || CollectionUtils.isNotEmpty(handleCaseInfoParam.getPassIds())) {
                throw new RuntimeException("停止筹款不能选择通过项，需要选择停止筹款的驳回项");
            }
        }

        if (handleCaseInfoParam.getHandleType() == InitialAuditOperationItem.HandleTypeEnum.SUBMIT.getCode()
                && handleCaseInfoParam.getOrderType() != WorkOrderType.bohui.getType()) {
            if (CollectionUtils.isEmpty(handleCaseInfoParam.getPassIds()) &&
                    CollectionUtils.isEmpty(handleCaseInfoParam.getRejectIds())) {
                throw new RuntimeException("提交需要选择驳回或通过项");

            }
        }
    }

    private void validateOrderType(InitialAuditOperationItem.HandleCaseInfoParam handleCaseInfoParam) {
        if (handleCaseInfoParam.getOrderType() == WorkOrderType.bohui.getType()) {

            if (handleCaseInfoParam.getHandleType() == InitialAuditOperationItem.HandleTypeEnum.RETURN_VISIT.getCode()) {
                throw new RuntimeException("驳回工单不能点击回访处理");
            }

            if (handleCaseInfoParam.getHandleType() == InitialAuditOperationItem.HandleTypeEnum.SUBMIT.getCode()
                    && (CollectionUtils.isNotEmpty(handleCaseInfoParam.getPassIds())
                    || CollectionUtils.isNotEmpty(handleCaseInfoParam.getRejectIds()))) {
                throw new RuntimeException("驳回工单不能通过或驳回案例");
            }

            return;
        }

        // 回访工单
        if (handleCaseInfoParam.getOrderType() == WorkOrderType.huifang.getType() &&
                handleCaseInfoParam.getHandleType() == InitialAuditOperationItem.HandleTypeEnum.RETURN_VISIT.getCode()) {
            throw new RuntimeException("回访工单不能在点击回访");
        }
    }

    private void validatePassAndRejectIds(InitialAuditOperationItem.HandleCaseInfoParam handleParam,
                                          CrowdfundingInitialAuditInfo initialInfo) {
        if (handleParam.getOrderType() == WorkOrderType.bohui.getType()
                || handleParam.getHandleType() != InitialAuditOperationItem.HandleTypeEnum.SUBMIT.getCode()) {
            return;
        }

        Set<Integer> rejectTypes = entityBiz.queryDataTypeByRejectIds(handleParam.getRejectIds());
        List<Integer> passTypes = Objects.requireNonNullElse(handleParam.getPassIds(), Lists.newArrayList());
        for (int passId : passTypes) {
            if (rejectTypes.contains(passId)) {
                throw new RuntimeException("不能同时通过和驳回同一项材料");
            }
        }

        if (validateOperateAll != 1) {
            return;
        }

        // 判断是否某一个模块没有给出审核结果
        Set<Integer> allHandleTypes = Sets.newHashSet(rejectTypes);
        allHandleTypes.addAll(passTypes);

        // initialInfo 已做非空校验
        if (!allHandleTypes.contains(BASE_INFO_TAG) && initialInfo.getBaseInfo() ==
                InitialAuditItem.MaterialStatus.SUBMIT.getCode()) {
            throw new RuntimeException(NO_HANDLE_ALL_TYPE_TIPS);
        }
        if (!allHandleTypes.contains(FIRST_APPROVE_TAG) && initialInfo.getFirstApproveInfo() ==
                InitialAuditItem.MaterialStatus.SUBMIT.getCode()) {
            throw new RuntimeException(NO_HANDLE_ALL_TYPE_TIPS);
        }
        if (!allHandleTypes.contains(CREDIT_TAG) && initialInfo.getCreditInfo() ==
                InitialAuditItem.MaterialStatus.SUBMIT.getCode()) {
            throw new RuntimeException(NO_HANDLE_ALL_TYPE_TIPS);
        }
//        if (handleParam.getPinKunHandleParamWithLoaded() == null) {
//            RiverReviewDO pinKun = riverReviewService.get(initialInfo.getCaseId(), RiverUsageTypeEnum.PIN_KUN);
//            if (pinKun != null && pinKun.getInfoStatus() == RiverStatusEnum.SUBMITTED.getValue()) {
//                throw new RuntimeException(NO_HANDLE_ALL_TYPE_TIPS);
//            }
//        }
        if (handleParam.getDiBaoHandleParamWithLoaded() == null) {
            RiverReviewDO diBao = riverReviewService.get(initialInfo.getCaseId(), RiverUsageTypeEnum.DI_BAO);
            if (diBao != null && diBao.getInfoStatus() == RiverStatusEnum.SUBMITTED.getValue()) {
                throw new RuntimeException(NO_HANDLE_ALL_TYPE_TIPS);
            }
        }
    }

    public void insertUserCommentLog(InitialAuditOperationItem.HandleCaseInfoParam handleCaseInfoParam,
                                     UserCommentSourceEnum.CommentType operateType, String operateDesc,
                                     String infoUuid) {
        UserComment comment = new UserComment();
        comment.setOperatorId(handleCaseInfoParam.getUserId());
        comment.setCaseId(handleCaseInfoParam.getCaseId());
        comment.setCommentSource(UserCommentSourceEnum.INITIAL_AUDIT.getCode());
        comment.setCommentType(operateType.getCode());
        comment.setOperateMode(operateType.getDesc());
        comment.setWorkOrderId(handleCaseInfoParam.getWorkOrderId());
        comment.setComment(subStringByMaxLen(getHandleComment(handleCaseInfoParam), MAX_USER_COMMENT_LENGTH));
        comment.setOperateDesc(subStringByMaxLen(operateDesc, MAX_USER_COMMENT_LENGTH));
        commentBiz.insert(comment);

        approveService.addComment(infoUuid, queryDetailComment(handleCaseInfoParam, operateDesc), handleCaseInfoParam.getUserId(),
                    "初次审核" + "【工单id：" + handleCaseInfoParam.getWorkOrderId() + "】",
                    BackgroundLogEnum.INITIAL_AUDIT_HANDLE);
    }

    private String queryDetailComment(InitialAuditOperationItem.HandleCaseInfoParam handleCaseInfoParam, String operateDesc) {
        String delayHandle = handleCaseInfoParam.getHandleType() == InitialAuditOperationItem.HandleTypeEnum.DELAY_HANDLE.getCode() ? InitialAuditOperationItem.HandleTypeEnum.DELAY_HANDLE.getDesc() + " " :  "";
        String handleComment = getHandleComment(handleCaseInfoParam);
        String result = delayHandle + StringUtils.trimToEmpty(operateDesc) + (StringUtils.isNotEmpty(handleComment) ? "\n\n" + handleComment : "");
        return StringUtils.replace(result, "<br>", " ");
    }

    private String getHandleComment(InitialAuditOperationItem.HandleCaseInfoParam handleCaseInfoParam) {
        return StringUtils.trimToEmpty(handleCaseInfoParam.getHandleComment()) + " "
                + StringUtils.trimToEmpty(handleCaseInfoParam.getCallComment());
    }

    private void sendProgressAfterInitialAuditPass(CrowdfundingInfo crowdfundingInfo, InitialAuditOperationItem.HandleCaseInfoParam handleCaseInfoParam) {

        CfFirsApproveMaterial material = riskDelegate.getCfFirsApproveMaterialByInfoId(crowdfundingInfo.getId());
        if (material == null) {
            log.error("初审处理不能找到CfFirsApproveMaterial.caseId:{}", crowdfundingInfo.getId());
            return;
        }

        if (handleCaseInfoParam.isSpecialReport()) {
            log.info("案例走了特殊报备流程业务人员并标记，不发动态.caseId:{}", crowdfundingInfo.getId());
            return;
        }

        CrowdFundingProgress progress = new CrowdFundingProgress();
        progress.setImageUrls(material.getImageUrl());
        progress.setContent(progressContent);
        progress.setUserId(crowdfundingInfo.getUserId());
        progress.setActivityId(crowdfundingInfo.getId());
        progress.setTitle("");
        progress.setType(CrowdFundingProgressType.PROGRESS.value());
        progressBiz.insertInCrowdfundingProgress(progress);

        if (crowdfundingInfo.getTargetAmount() > FIFTY_W) {
            progress.setImageUrls("");
            progress.setContent("筹款金额大于50万原因说明: " + material.getTargetAmountDesc());
            progressBiz.insertInCrowdfundingProgress(progress);
        }
    }

    public void sendSmsMessage(InitialAuditOperationItem.InitialAuditSmsMsg initialMsg) {

        log.info("初审短信发送 msg:{}", initialMsg);

        // 发短信
        approveService.sendCaseApproveSmsWithRecord(initialMsg.getMobile(), initialMsg.getContent(), initialMsg.getModelNum(),
                initialMsg.getCaseId(), initialMsg.getUserId(), initialMsg.getParamMap());

        // log
        smsMsgLog(initialMsg);
    }

    private void smsMsgLog(InitialAuditOperationItem.InitialAuditSmsMsg initialMsg) {
        InitialAuditOperationItem.HandleCaseInfoParam param = new
                InitialAuditOperationItem.HandleCaseInfoParam();
        param.setUserId(initialMsg.getUserId());
        param.setCaseId(initialMsg.getCaseId());
        param.setWorkOrderId(initialMsg.getWorkOrderId());

        String smsMsg = getSmsContent(initialMsg);
        if (org.apache.commons.collections.MapUtils.isNotEmpty(initialMsg.getParamMap())) {
            for (Map.Entry<Integer, String> entry : initialMsg.getParamMap().entrySet()) {
                smsMsg = smsMsg.replace("{" + entry.getKey() + "}", entry.getValue());
            }
        }
        insertUserCommentLog(param,
                UserCommentSourceEnum.CommentType.SEND_SMS, "发送短信：<br>短信内容: " + smsMsg
                        + "<br>发送手机号：【" + initialMsg.getMobile() + "】", initialMsg.getInfoId());
    }

    private String getSmsContent(InitialAuditOperationItem.InitialAuditSmsMsg initialMsg) {

        if (StringUtils.isNotBlank(initialMsg.getContent())) {
            return initialMsg.getContent();
        }

        List<SmsTemplate> smsTemplates = sendMsgTemplateBiz.getTemplateByModelNum(initialMsg.getModelNum());

        return (org.apache.commons.collections.CollectionUtils.isEmpty(smsTemplates) || smsTemplates.size() > 1) ? "" : smsTemplates.get(0).getText();
    }

    public Response<Long> reprocess(long workOrderId, long operatorId, String comment) {

        // 检查是否可以重新审核
        Response<Boolean> checkCanReprocessResp = cfWorkOrderClient.checkCanReprocess(workOrderId);
        if (checkCanReprocessResp.notOk()) {
            return NewResponseUtil.makeResponse(checkCanReprocessResp.getCode(), checkCanReprocessResp.getMsg(), null);
        }

        Boolean canReprocess = checkCanReprocessResp.getData();
        if (!canReprocess) {
            return NewResponseUtil.makeError(AdminErrorCode.CAN_NOT_REPROCESS);
        }

        Response<WorkOrderVO> workOrderVOResponse = cfWorkOrderClient.getWorkOrderById(workOrderId);
        if (workOrderVOResponse.notOk()) {
            return NewResponseUtil.makeResponse(workOrderVOResponse.getCode(), workOrderVOResponse.getMsg(), null);
        }
        WorkOrderVO workOrderVO = workOrderVOResponse.getData();

        int caseId = workOrderVO.getCaseId();
        int orderType = workOrderVO.getOrderType();

        CrowdfundingInfo fundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        // 初审工单需检查案例结束状态
        if (chushenWorkReProcessList.contains(orderType)) {
            Boolean hasEnd = CaseUtils.hasEnd(fundingInfo);
            if (hasEnd) {
                // 前端不支持 特定code 做个性化错误提示弹窗 要去掉公共弹窗必须返回0
                return NewResponseUtil.makeResponse(0, AdminErrorCode.WORK_ORDER_REPROCESS_FAIL_ON_CASE_END.getMsg(), 0L);
            }
        }

        if (chushenWorkReProcessList.contains(orderType)) {
            //如果重新审核后，在触发生成工单时被命中了“限制预审通过”，则也需要进入案例风险命中库，
            // 且重新审核失败，需要错误提示：案例被“限制预审通过”策略命中，
            // 无法生成预审工单，请联系风控解锁后方可再次操作；
            if (cfAdminLimitService.caseInLimit(caseId, BlacklistCallPhaseEnum.SUBMIT_PRE_TRIAL)) {
                return NewResponseUtil.makeError(AdminErrorCode.ORDER_LIMIT_ERROR);
            }
            crowdfundingOperationDelegate.updateCaseInitialSubmit(caseId, true, true, true);
            riverReviewService.save(caseId, RiverUsageTypeEnum.DI_BAO, RiverStatusEnum.SUBMITTED, "");
            riverReviewService.save(caseId, RiverUsageTypeEnum.PIN_KUN, RiverStatusEnum.SUBMITTED, "");
        }

        riverHelpService.reprocess(orderType, caseId);
        if (orderType == WorkOrderType.xiafaprogress.getType()) {
            boolean doBeforeProgress = supplyProgressService.doBeforeReprocess(workOrderVO, operatorId);
            if (!doBeforeProgress) {
                log.warn("下发动态工单重审失败,workOrderVO:{}", workOrderVO);
                return NewResponseUtil.makeFail("下发动态工单重审失败");
            }
        }

        // 说明是ugc评论 且生成的是医护证实工单
        if (WorkOrderType.ugcpinglun.getType() == orderType && StringUtils.isNotEmpty(workOrderVO.getVerificationId())) {
            log.info("重新审核医护证实工单");
            String verificationId = workOrderVO.getVerificationId();
            Response<List<CrowdFundingVerification>> verifyResponse = verificationFeignClient.getVerificationList(Lists.newArrayList(Long.valueOf(verificationId)));
            if (Objects.isNull(verifyResponse.getData())) {
                log.info("重新审核获取证实信息失败");
                return NewResponseUtil.makeFail("重新审核获取证实信息失败");
            }

            List<CrowdFundingVerification> verificationList = verifyResponse.getData();

            if (CollectionUtils.isEmpty(verificationList)) {
                log.info("重新审核获取证实信息列表为空");
                return NewResponseUtil.makeFail("重新审核获取证实信息列表为空");
            }

            CrowdFundingVerification verification = verificationList.get(0);

            if (Objects.isNull(verification)) {
                log.info("重新审核获取证实信息为空");
                return NewResponseUtil.makeFail("重新审核获取证实信息为空");
            }

            long verifyUserId = verification.getVerifyUserId();
            FeignResponse<UserRealInfo> userRealInfoResponse = userInfoFeignClient.getByUserId(verifyUserId);

            if (Objects.isNull(userRealInfoResponse.getData())) {
                log.info("重新审核获取证实用户响应信息失败");
                return NewResponseUtil.makeFail("重新审核获取证实用户响应信息失败");
            }

            UserRealInfo userRealInfo = userRealInfoResponse.getData();
            if (Objects.isNull(userRealInfo)) {
                return NewResponseUtil.makeFail("重新审核获取用户信息为空");
            }

            // 重新审核需要设置工单快照逻辑
            UgcWorkOrder ugcWorkOrder = new UgcWorkOrder();
            ugcWorkOrder.setId(workOrderId);
            if (StringUtils.isEmpty(ugcWorkOrder.getMedicalStatus())) {
                ugcWorkOrder.setMedicalStatus(String.valueOf(userRealInfo.getMedicalStatus()));
            }
            cfUgcWorkOrderClient.ugcCreateWorkOrderExt(ugcWorkOrder);
        }

        //
        if (WorkOrderType.ugc_complaint_verify.getType() == orderType && StringUtils.isNotEmpty(workOrderVO.getVerificationId())) {

            Response<WorkOrderVO> response = cfWorkOrderClient.getWorkOrderById(workOrderId);
            log.info("重新审核获取工单信息 response :{}", response);
            if (Objects.isNull(response) || Objects.isNull(response.getData())){
                return NewResponseUtil.makeFail("重新审核获查询工单失败");
            }

            WorkOrderVO data = response.getData();
            String verificationId = data.getVerificationId();

            // 设置审核状态为 未审核
            complaintService.saveOrderUpdateVerifyResult(Long.valueOf(verificationId) , ComplaintBizTypeEnum.VERIFICATION.getCode(), 0);

            // 重新审核时 ,重置证实为仅自己可见
            RiskUgcVerifyModel riskUgcVerifyModel = new RiskUgcVerifyModel(caseId, UgcTypeEnum.VERIFICATION, Long.valueOf(workOrderVO.getVerificationId()), "运营操作添加");
            riskDelegate.addVerify(riskUgcVerifyModel);
            ugcOperateRecordService.insertUgcRecord(caseId, UgcBizType.VERIFICATION.getKey(), Long.valueOf(workOrderVO.getVerificationId()), UgcManageEnum.VERIFY_SEE_ONESELF.getKey(),Long.valueOf(operatorId).intValue());

        }


        return cfWorkOrderClient.reprocessWorkOrder(workOrderId, operatorId, comment);
    }

    //初审&&停止筹款,选择不符合大病筹款服务&&控制开关 -> 是否生成医疗工单
    private boolean canCreateYiliaoWork(InitialAuditOperationItem.HandleCaseInfoParam handleCaseInfoParam) {
        List<Integer> rejectIds = handleCaseInfoParam.getRejectIds();
        Integer rejectId = null;
        boolean seriousIllness = false;
        if (CollectionUtils.isNotEmpty(rejectIds) && rejectIds.size() == 1) {
            rejectId = rejectIds.get(0);
        }

        if (rejectId != null) {
            CfRefuseReasonEntity reasonEntity = entityBiz.selectById(rejectId, null);
            seriousIllness = Optional.ofNullable(reasonEntity)
                    .flatMap(item -> Optional.ofNullable(item.getContent()))
                    .orElse("")
                    .contains(seriousIllnessDes);
        }


        boolean chuShenEndHandler = handleCaseInfoParam.getHandleType() == InitialAuditOperationItem.HandleTypeEnum.END_CASE.getCode()
                && (handleCaseInfoParam.getOrderType() == WorkOrderType.shenhe.getType() ||
                handleCaseInfoParam.getOrderType() == WorkOrderType.dianhuashenhe.getType() ||
                handleCaseInfoParam.getOrderType() == WorkOrderType.bohui.getType() ||
                handleCaseInfoParam.getOrderType() == WorkOrderType.ai_erci.getType()
        );

        if (allowCreateYiliaoWork && seriousIllness && chuShenEndHandler) {
            return true;
        }
        return false;
    }

    public void addComment(int userId, int caseId, String commentDesc, String infoUuid, long workOrderId, String ct, String operateMsg, int commentTypeCode) {

        UserCommentSourceEnum.CommentType typeFromCode = UserCommentSourceEnum.CommentType.getCommetTypefromCode(commentTypeCode);
        if (Objects.isNull(typeFromCode)) {
            return;
        }
        UserComment comment = new UserComment();
        comment.setOperatorId(userId);
        comment.setCaseId(caseId);
        comment.setCommentSource(UserCommentSourceEnum.INITIAL_AUDIT.getCode());
        comment.setCommentType(typeFromCode.getCode());
        comment.setOperateMode(typeFromCode.getDesc());
        comment.setWorkOrderId(workOrderId);
        comment.setComment(ct);
        comment.setOperateDesc(commentDesc);
        commentBiz.insert(comment);

        approveService.addComment(infoUuid, commentDesc, userId, operateMsg, BackgroundLogEnum.REMARK);

    }


    public OpResult<Void> submit(List<CfAiMaterials> aiMaterials, int userId) {

        if (CollectionUtils.isEmpty(aiMaterials)) {
            return OpResult.createFailResult(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        CfAiMaterials cfAiMaterials = aiMaterials.get(0);

        long workOrderId = cfAiMaterials.getWorkOrderId();
        Response<WorkOrderVO> orderVOResponse = cfWorkOrderClient.getWorkOrderById(workOrderId);
        if (Objects.isNull(orderVOResponse) || orderVOResponse.notOk() || Objects.isNull(orderVOResponse.getData())) {
            return OpResult.createFailResult(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        WorkOrderVO workOrder = orderVOResponse.getData();
        if (workOrder.getOperatorId() != userId) {
            return OpResult.createFailResult(AdminErrorCode.ORDER_HAS_CALLBACK);
        }
        if (cfAiMaterials.getHandleResult() != HandleResultEnum.later_doing.getType()
                && cfAiMaterials.getHandleResult() != HandleResultEnum.done.getType()) {
            return OpResult.createFailResult(AdminErrorCode.SYSTEM_PARAM_ERROR_VALID_DATA_RANGE);
        }

        int caseId = cfAiMaterials.getCaseId();

        //先入库   防止工单处理过快产生空指针
        if (cfAiMaterials.getHandleResult() == HandleResultEnum.done.getType()) {
            cfAiMaterialsDao.insert(aiMaterials);
            Set<String> logSet = Sets.newHashSet("existMultiPatientDesc", "venerealDiseaseDesc", "accidentDesc");
            aiMaterials.stream().flatMap(r -> r.getFields().stream().filter(rr -> logSet.contains(rr.getFieldKey()))).forEach(v -> saveComment(userId, caseId, workOrderId, v.getFieldValue(), ""));
            //处理成功保存  处理时快照
            cfInitialAuditHandleV2ConsumerService.saveInitialAuditSnapshotV2(workOrderId, caseId);

            RiverDetailVO<InitialAuditAdditionInfoVO> diBaoDetail = riverDiBaoFacade.getDetail(caseId, workOrderId);

            commonOperationRecordClient.create()
                    .buildBasicPlatform(workOrderId, userId, OperationActionTypeEnum.DI_BAO_RIVER_WORK_ORDER_SNAPSHOT)
                    .buildExt("info", JSON.toJSONString(diBaoDetail.getInfo()))
                    .save();

            InitialAuditSmartRejectVo initialAuditSmartRejectVo = autoEnterHuman(aiMaterials, false);
            if (Objects.nonNull(initialAuditSmartRejectVo.getInitialAuditNoSmartReason())) {
                return toHuman(cfAiMaterials.getWorkOrderId(), cfAiMaterials.getWorkOrderType(), initialAuditSmartRejectVo, AdminUserIDConstants.SYSTEM, cfAiMaterials.getCaseId(), "自动进入人工审核");
            }
        }

        ChuciHandleOrderParam param = new ChuciHandleOrderParam();
        param.setWorkOrderId(workOrderId);
        param.setHandleResult(cfAiMaterials.getHandleResult());
        param.setOrderType(cfAiMaterials.getWorkOrderType());
        param.setUserId(userId);

        Response<Map<Integer, Long>> response = chuciWorkOrderClient.hanldeChuciV2(param);
        log.info("hanldeChuciV2 param={} response={}", param, response);

        if (response.notOk()) {
            if (Objects.equals(workOrder.getHandleResult(), HandleResultEnum.exception_done.getType())) {
                return OpResult.createFailResult(AdminErrorCode.SYSTEM_OPERATION_FAILED, "工单自动异常关闭");
            }
            return OpResult.createFailResult(AdminErrorCode.SYSTEM_OPERATION_FAILED, response.getMsg());
        }

        return OpResult.createFailResult(AdminErrorCode.SUCCESS);
    }

    public InitialAuditSmartRejectVo autoEnterHuman(List<CfAiMaterials> aiMaterials, boolean limitContentWorkOrder) {
        CfAiMaterials cfAiMaterials = aiMaterials.get(0);
        InitialAuditSmartRejectVo initialAuditSmartRejectVo = new InitialAuditSmartRejectVo();
        int caseId = cfAiMaterials.getCaseId();
        CfFirsApproveMaterial material = riskDelegate.getCfFirsApproveMaterialByInfoId(caseId);
        InitialAuditSmartRejectVo judgeRepeatCase = initialAuditCreateOrder.judgeRepeatCase(caseId, material);
        if (Objects.nonNull(judgeRepeatCase.getInitialAuditNoSmartReason())) {
            log.info("重复案例自动进入人工审核 {}", JSONObject.toJSONString(aiMaterials));
            return judgeRepeatCase;
        }

        String diseaseNameListStr = aiMaterials.stream()
                .flatMap(f -> f.getFields()
                        .stream().filter(fl -> StringUtils.equals("diseaseNameInMd", fl.getFieldKey())))
                .map(LayOutField::getFieldValue)
                .filter(StringUtils::isNotEmpty)
                .findFirst()
                .orElse("");
        if (StringUtils.isEmpty(diseaseNameListStr)) {
            return initialAuditSmartRejectVo;
        }
        List<String> diseaseNameList = Splitter.on("，").splitToList(diseaseNameListStr);
        if (CollectionUtils.isEmpty(diseaseNameList)) {
            return initialAuditSmartRejectVo;
        }

        // 图文工单疾病归一
        Response<List<DiseaseClassifyVOV2>> listResponse = diseaseClassifyFeignClientV2.diseaseNorm(diseaseNameList);
        List<String> norm = Optional.ofNullable(listResponse)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(Lists.newArrayList())
                .stream()
                .map(DiseaseClassifyVOV2::getNorm)
                .flatMap(List::stream)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(norm)) {
            log.info("医疗材料中的疾病不在疾病库中 {}", JSONObject.toJSONString(aiMaterials));
            initialAuditSmartRejectVo.setInitialAuditNoSmartReason(InitialAuditNoSmartReason.MATERIAL_DISEASE_NAME_EXIT_SYSTEM);
            initialAuditSmartRejectVo.setDiseaseName(Joiner.on(",").join(diseaseNameList));
            initialAuditSmartRejectVo.setRemarkMsg(InitialAuditNoSmartReason.MATERIAL_DISEASE_NAME_EXIT_SYSTEM.getMsg() + "：" + Joiner.on(",").join(diseaseNameList));
            return initialAuditSmartRejectVo;
        }

        // 图文工单疾病归一是否特殊可发
        Response<List<RiskDiseaseDataVO>> riskDiseaseDataResponse = diseaseClient.getRiskDiseaseDataByClassNameList(norm);
        log.info("autoEnterHuman specialRaiseChoiceInfo {}, {}", riskDiseaseDataResponse, diseaseNameList);
        if (Objects.isNull(riskDiseaseDataResponse) || riskDiseaseDataResponse.notOk() || CollectionUtils.isEmpty(riskDiseaseDataResponse.getData())) {
            return initialAuditSmartRejectVo;
        }
        List<RiskDiseaseDataVO> data = riskDiseaseDataResponse.getData();
        long countSpecialRaise = data.stream()
                .filter(f -> f.getRaiseType() == RiskDiseaseDataVO.DiseaseRaiseTypeEnum.SPECIAL_RAISE.getCode())
                .count();
        if (countSpecialRaise == 0) {
            return initialAuditSmartRejectVo;
        }
        List<RiskDiseaseDataVO> canRaiseList = data.stream()
                .filter(f -> f.getRaiseType() == RiskDiseaseDataVO.DiseaseRaiseTypeEnum.CAN_RAISE.getCode())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(canRaiseList)) {
            log.info("医疗材料中的疾病包含特殊可发且不包含可发疾病 {}", JSONObject.toJSONString(aiMaterials));
            initialAuditSmartRejectVo.setInitialAuditNoSmartReason(InitialAuditNoSmartReason.MATERIAL_DISEASE_NAME_SPECIAL);
            initialAuditSmartRejectVo.setDiseaseName(Joiner.on(",").join(norm));
            initialAuditSmartRejectVo.setRemarkMsg(InitialAuditNoSmartReason.MATERIAL_DISEASE_NAME_SPECIAL.getMsg() + "：" + Joiner.on(",").join(diseaseNameList));
            return initialAuditSmartRejectVo;
        }
        if (!limitContentWorkOrder) {
            return initialAuditSmartRejectVo;
        }
        CfAiMaterials materialsTwoType = cfAiMaterialsDao.getByCaseId(caseId, CfAiMaterials.jType);
        if (Objects.isNull(materialsTwoType) || CollectionUtils.isEmpty(materialsTwoType.getFields())) {
            log.info("医疗材料中的疾病包含特殊可发且文章工单疾病为空 {}", JSONObject.toJSONString(aiMaterials));
            initialAuditSmartRejectVo.setInitialAuditNoSmartReason(InitialAuditNoSmartReason.MATERIAL_DISEASE_NAME_SPECIAL);
            initialAuditSmartRejectVo.setDiseaseName(Joiner.on(",").join(norm));
            initialAuditSmartRejectVo.setRemarkMsg(InitialAuditNoSmartReason.MATERIAL_DISEASE_NAME_SPECIAL.getMsg() + "：" + Joiner.on(",").join(diseaseNameList));
            return initialAuditSmartRejectVo;
        }

        List<String> titleAndContentDiseaseList = materialsTwoType.getFields()
                .stream()
                .filter(f -> StringUtils.equals("diseaseNameInTitle", f.getFieldKey()) || StringUtils.equals("diseaseNameInContent", f.getFieldKey()))
                .map(LayOutField::getFieldValue)
                .filter(StringUtils::isNotEmpty)
                .map(m -> Splitter.on("，").splitToList(m))
                .flatMap(List::stream)
                .collect(Collectors.toList());
        // 文章工单疾病归一
        Response<List<DiseaseClassifyVOV2>> listResponseTitle = diseaseClassifyFeignClientV2.diseaseNorm(titleAndContentDiseaseList);
        List<String> normTitleAndContentDiseaseList = Optional.ofNullable(listResponseTitle)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(Lists.newArrayList())
                .stream()
                .map(DiseaseClassifyVOV2::getNorm)
                .flatMap(List::stream)
                .collect(Collectors.toList());

        long countCanRaise = canRaiseList.stream()
                .filter(f -> normTitleAndContentDiseaseList.contains(f.getDiseaseClassName()))
                .count();
        if (countCanRaise > 0) {
            return initialAuditSmartRejectVo;
        }
        log.info("医疗材料中的疾病包含特殊可发且包含可发且与文章工单疾病名称不一致 {}", JSONObject.toJSONString(aiMaterials));
        initialAuditSmartRejectVo.setInitialAuditNoSmartReason(InitialAuditNoSmartReason.MATERIAL_DISEASE_NAME_SPECIAL);
        initialAuditSmartRejectVo.setDiseaseName(Joiner.on(",").join(norm));
        initialAuditSmartRejectVo.setRemarkMsg(InitialAuditNoSmartReason.MATERIAL_DISEASE_NAME_SPECIAL.getMsg() + "：" + Joiner.on(",").join(diseaseNameList));
        return initialAuditSmartRejectVo;
    }


    public List<CfAiMaterials> listAiMaterials(long workOrderId) {
        return cfAiMaterialsDao.getByWorkOrderId(workOrderId);
    }


    public OpResult<Void> toHuman(long workOrderId, int orderType, InitialAuditSmartRejectVo initialAuditSmartRejectVo, int userId, int caseId, String mode) {

        if (workOrderId < 0 || caseId < 0 || (orderType != WorkOrderType.ai_content.getType() && orderType != WorkOrderType.ai_photo.getType())) {
            return OpResult.createFailResult(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        int type = WorkOrderType.ai_content.getType();
        if (WorkOrderType.ai_content.getType() == orderType) {
            type = WorkOrderType.ai_photo.getType();
        }
        ;
        Response<WorkOrderVO> response = cfWorkOrderClient.getLastWorkOrder(caseId, type);

        WorkOrderVO vo = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(null);

        if (vo == null) {
            return OpResult.createFailResult(AdminErrorCode.SYSTEM_NO_ID);
        }

        ChuciHandleOrderParam param = new ChuciHandleOrderParam();
        param.setWorkOrderId(workOrderId);
        param.setHandleResult(HandleResultEnum.manual_lock.getType());
        param.setOrderType(orderType);
        Response<Map<Integer, Long>> r = chuciWorkOrderClient.hanldeChuciV2(param);
        if (r.notOk()) {
            return OpResult.createFailResult(AdminErrorCode.CASE_MSG_NO_CHANGE);
        }

        log.info("getLastWorkOrder caseid={} type={} vo={}", caseId, type, vo);

        if (HandleResultEnum.unDoResult().stream().collect(Collectors.toSet()).contains(vo.getHandleResult())) {
            cfWorkOrderClient.closeOrderBycaseIdAndType(caseId, vo.getOrderType(), HandleResultEnum.exception_done.getType(), userId, "");
        }

        //创建二次审核工单
        ChuciWorkOrder chuciWorkOrder = new ChuciWorkOrder();
        chuciWorkOrder.setCaseId(caseId);
        chuciWorkOrder.setOrderType(WorkOrderType.ai_erci.getType());
        chuciWorkOrder.setSmartWorkOrderRealCreateTime(DateUtil.formatDateTime(vo.getCreateTime()));
        InitialAuditCreateOrderParam orderParam = InitialAuditCreateOrderParam.builder()
                .condition(0)
                .caseId(caseId)
                .chuciWorkOrder(chuciWorkOrder)
                .diseaseName(userId == AdminUserIDConstants.SYSTEM ? initialAuditSmartRejectVo.getDiseaseName() : "")
                .noSmartAuditReason(userId == AdminUserIDConstants.SYSTEM ? initialAuditSmartRejectVo.getInitialAuditNoSmartReason().getMsg() : InitialAuditNoSmartReason.ENTER_PEOPLE_AUDIT.getMsg())
                .build();
        initialAuditCreateOrder.create(orderParam);

        saveComment(userId, caseId, workOrderId, initialAuditSmartRejectVo.getRemarkMsg(), mode);

        //处理成功保存  处理时快照
        cfInitialAuditHandleV2ConsumerService.saveInitialAuditSnapshotV2(workOrderId, caseId);

        return OpResult.createSucResult();

    }


    public CfAiMaterialsResult getResult(int caseId, long workOrderId, int orderType) {

        Response<List<WorkOrderExt>> workOrderExt = cfWorkOrderClient.queryAllWorkExtIgnoreDelete(workOrderId, Lists.newArrayList(OrderExtName.secondWorkOrderSource.getName()));
        boolean secondWorkOrderSource = Objects.nonNull(workOrderExt) && workOrderExt.ok() && CollectionUtils.isNotEmpty(workOrderExt.getData());
        if (!secondWorkOrderSource) {
            return null;
        }

        CfAiMaterialsResult result = cfAiMaterialsDao.getResultByCaseId(caseId);
        //如果是二次审核工单要判断是不是人工审核  如果转人工审核就不带驳回结果
        if (result != null && WorkOrderType.ai_erci.getType() == orderType) {

            Response<WorkOrderVO> response = cfWorkOrderClient.getLastWorkOrderByTypes(caseId, Lists.newArrayList(WorkOrderType.ai_photo.getType(), WorkOrderType.ai_content.getType()));

            WorkOrderVO vo = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(null);

            if (vo != null && (vo.getHandleResult() == HandleResultEnum.exception_done.getType() || vo.getHandleResult() == HandleResultEnum.manual_lock.getType())) {
                result.setRejectField("-1");
                result.setRejectResult("-1");
                result.setStopResult("-1");
            }
        }

        return result;
    }

    public void saveCommentV2(int userId, int caseId, long workOrderId, String remark, String mode, String commentContent) {
        //操作日志
        UserComment comment = new UserComment();
        comment.setOperatorId(userId);
        comment.setCaseId(caseId);
        comment.setCommentSource(UserCommentSourceEnum.INITIAL_AUDIT.getCode());
        comment.setCommentType(UserCommentSourceEnum.CommentType.SUBMIT_VALUE_REMARK.getCode());
        comment.setOperateMode(StringUtils.isNotEmpty(mode) ? mode : UserCommentSourceEnum.CommentType.SUBMIT_VALUE_REMARK.getDesc());
        comment.setWorkOrderId(workOrderId);
        comment.setComment(commentContent);
        comment.setOperateDesc(StringUtils.isNotEmpty(remark) ? remark : "");
        commentBiz.insert(comment);

    }

    private void saveComment(int userId, int caseId, long workOrderId, String remark, String mode) {
        //操作日志
        saveCommentV2(userId, caseId, workOrderId, remark, mode, "");
    }

    public boolean validateErCi(InitialAuditOperationItem.HandleCaseInfoParam handleParam) {

        if (handleParam.getOrderType() != WorkOrderType.ai_erci.getType()
                || CollectionUtils.isEmpty(handleParam.getRejectIds())) {
            return true;
        }

        if (handleParam.isAiErForceOp()) {
            log.info("二次审核工单 不检验电话强制通过 workOrderId:{}", handleParam.getWorkOrderId());
            return true;
        }

        // 判断渠道
        Map<Integer, CfUserInvitedLaunchCaseRecordModel> recordModelMap = approveService
                .getCaseChannelRecordMap(Lists.newArrayList(handleParam.getCaseId()));

        log.info("查询案例的发起渠道。caseId:{} result:{}", handleParam.getCaseId(), JSON.toJSONString(recordModelMap));

        CfUserInvitedLaunchCaseRecordModel recordModel = recordModelMap.get(handleParam.getCaseId());
        if (recordModel == null) {
            return true;
        }

        ChannelRefine.ChannelRefineResuleEnum resultEnum = ChannelRefine.ChannelRefineResuleEnum.parse(recordModel.getChannel());
        if (resultEnum != ChannelRefine.ChannelRefineResuleEnum.YONGHU_ZIZHU &&
                resultEnum != ChannelRefine.ChannelRefineResuleEnum.WEIXIN_1V1) {
            return true;
        }

        return caseWorkOrderService.hasCallPhone(handleParam.getWorkOrderId(), handleParam.getUserId());

    }

    private static String subStringByMaxLen(String str, int len) {
        if (StringUtils.isBlank(str) || str.length() <= len) {
            return str;
        }
        return str.substring(0, len) + "...";
    }

    public static void main(String[] args) {
        System.out.println(subStringByMaxLen("abc", 12));
        System.out.println(subStringByMaxLen("abc", 1));
    }

    /**
     * 升级应急组
     */
    public Response<Void> upgradeEmergency(long workOrderId, long operatorId, String reasonCode, String reasonMsg) {
        Response<WorkOrderVO> orderResp = cfWorkOrderClient.getWorkOrderById(workOrderId);
        int caseId = orderResp.getData().getCaseId();
        String permission = HIGH_RISK_EMERGENCY;
        String remark = "升级应急组:" + reasonMsg;
        Response<Void> resp = Von.services().getAssign()
                .updateAssignGroupWithPermission(workOrderId, WorkOrderType.highriskshenhe.getType(), permission,
                        operatorId, remark);
        if (resp.notOk()) {
            log.info("调用工单 升级应急组失败 resp {}", resp);
            if (resp.getCode() == 2017) {
                return NewResponseUtil.makeFail("该工单已经升级应急组，不可再次升级");
            }
            return NewResponseUtil.makeRelayFail(resp);
        }
        String operatorName = seaAccountDelegate.getNameByLongUserId(operatorId);
        wonRecordClient.create()
                .buildBasic(workOrderId, 108)
                .buildCaseId(caseId)
                .buildExtValue("caseId", caseId)
                .buildExtValue("workOrderId", workOrderId)
                .buildExtValue("operatorName", operatorName)
                .buildExtValue("reasonCode", reasonCode)
                .buildExtValue("reasonMsg", reasonMsg)
                .save();

        saveCommentV2((int) operatorId, caseId, workOrderId, "升级应急组", "升级应急组", "升级原因:" + reasonMsg);
        CfUpgradeWorkOrderRecord cfUpgradeWorkOrderRecord = new CfUpgradeWorkOrderRecord();
        cfUpgradeWorkOrderRecord.setCaseId(caseId);
        cfUpgradeWorkOrderRecord.setWorkOrderId(workOrderId);
        cfUpgradeWorkOrderRecord.setWorkOrderType(orderResp.getData().getOrderType());
        cfUpgradeWorkOrderRecord.setOperatorId(operatorId);
        cfUpgradeWorkOrderRecord.setUpgradeWorkOrderId(0);
        cfUpgradeWorkOrderRecord.setReason(reasonMsg);
        cfUpgradeWorkOrderRecord.setUpgradeType(1);
        cfUpgradeWorkOrderRecordDao.insert(cfUpgradeWorkOrderRecord);
        applicationContext.publishEvent(new NotifyOnlineVolunteerEvent(NotifyOnlineVolunteerEventEnum.INITIAL_AUDIT_EMERGENCY_EVENT, caseId));
        return NewResponseUtil.makeSuccess();
    }

    /**
     * 低风险工单升级应急组\高风险组
     */
    public Response<Void> lowUpgradeEmergency(long workOrderId, long operatorId, int upgradeType, String reasonMsg) {
        Response<WorkOrderVO> orderResp = cfWorkOrderClient.getWorkOrderById(workOrderId);
        WorkOrderVO workOrderVO = Optional.ofNullable(orderResp)
                .map(Response::getData)
                .orElse(null);
        if (Objects.isNull(workOrderVO)) {
            return NewResponseUtil.makeFail("工单数据为空");
        }
        HandleOrderParam handleOrderParam = new HandleOrderParam();
        handleOrderParam.setWorkOrderId(workOrderId);
        handleOrderParam.setOrderType(workOrderVO.getOrderType());
        handleOrderParam.setHandleResult(HandleResultEnum.upgrade_handle.getType());
        handleOrderParam.setUserId(operatorId);
        Response<Void> handle = workOrderCoreFeignClient.handle(handleOrderParam);
        if (handle.notOk()) {
            return NewResponseUtil.makeFail("工单处理失败");
        }
        int caseId = workOrderVO.getCaseId();
        WorkOrderCreateParam workOrderCreateParam = new WorkOrderCreateParam();
        workOrderCreateParam.setOrderType(WorkOrderType.highriskshenhe.getType());
        workOrderCreateParam.setCaseId(caseId);
        workOrderCreateParam.setOrderlevel(OrderLevel.A.getType());
        workOrderCreateParam.setAssignGroupPermission(upgradeType == 1 ? HIGH_RISK_EMERGENCY : HIGH_RISK_NORMAL);
        Response<Long> workResponse = workOrderCoreFeignClient.create(workOrderCreateParam);
        long highWorkOrderId = Optional.ofNullable(workResponse)
                .map(Response::getData)
                .orElse(0L);
        if (highWorkOrderId == 0) {
            return NewResponseUtil.makeFail("升级失败");
        }
        WorkOrderRecord record = new WorkOrderRecord();
        record.setCaseId(caseId);
        record.setOperatorId(operatorId);
        record.setWorkOrderId(workOrderId);
        record.setWorkOrderType(workOrderVO.getOrderType());
        record.setOperateDesc("处理工单");
        if (upgradeType == 1) {
            record.setComment("升级应急组，【新工单id：" + highWorkOrderId + "】");
            saveCommentV2((int) operatorId, caseId, workOrderId, "升级应急组", "升级应急组", "升级原因:" + reasonMsg);
            applicationContext.publishEvent(new NotifyOnlineVolunteerEvent(NotifyOnlineVolunteerEventEnum.INITIAL_AUDIT_EMERGENCY_EVENT, caseId));
        } else {
            record.setComment("升级高风险，【新工单id：" + highWorkOrderId + "】");
            saveCommentV2((int) operatorId, caseId, workOrderId, "升级高风险", "升级高风险", "升级原因:" + reasonMsg);
        }
        record.setOperateMode(4);
        cfWorkOrderRecordClient.insertWorkOrderRecordList(Lists.newArrayList(record));

        CfUpgradeWorkOrderRecord cfUpgradeWorkOrderRecord = new CfUpgradeWorkOrderRecord();
        cfUpgradeWorkOrderRecord.setCaseId(caseId);
        cfUpgradeWorkOrderRecord.setWorkOrderId(workOrderId);
        cfUpgradeWorkOrderRecord.setWorkOrderType(workOrderVO.getOrderType());
        cfUpgradeWorkOrderRecord.setOperatorId(operatorId);
        cfUpgradeWorkOrderRecord.setUpgradeWorkOrderId(highWorkOrderId);
        cfUpgradeWorkOrderRecord.setReason(reasonMsg);
        cfUpgradeWorkOrderRecord.setUpgradeType(upgradeType);
        cfUpgradeWorkOrderRecordDao.insert(cfUpgradeWorkOrderRecord);

        InitialAuditCreateOrderParam createOrderParam = InitialAuditCreateOrderParam.builder()
                .caseId(caseId)
                .workOrderId(highWorkOrderId)
                .riskWorkOrderReason(InitialAuditRiskWorkOrderReason.HUMAN_UPGRADE.getMsg())
                .build();
        try {

            chuciAnalyticsBiz.initialVerifySubmit(createOrderParam);
        } catch (Exception e) {
            log.info("lowUpgradeEmergency initialVerifySubmit error {} {}", createOrderParam, e);
        }

        return NewResponseUtil.makeSuccess();
    }

    public CfAiMaterialsSmartAIResult getSmartAIResultByWorkOrderId(long workOrderId) {
        return cfAiMaterialsSmartAIResultDao.getByWorkOrderId(workOrderId);
    }


}