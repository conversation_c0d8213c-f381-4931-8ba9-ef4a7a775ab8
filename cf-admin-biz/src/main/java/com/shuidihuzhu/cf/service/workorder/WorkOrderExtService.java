package com.shuidihuzhu.cf.service.workorder;

import com.shuidihuzhu.cf.client.adminpure.enums.WorkOrderExtContentTypeEnum;
import com.shuidihuzhu.cf.domain.cf.WorkOrderExt;
import com.shuidihuzhu.cf.model.admin.workorder.FirsApproveMaterialWithAmount;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.vo.approve.CfHospitalAuditInfoNew;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-12-06  15:17
 */
public interface WorkOrderExtService {

    WorkOrderExt getWorkOrderExt(long wordOrderId, WorkOrderExtContentTypeEnum contentType);

    void save(long workOrderId, WorkOrderExtContentTypeEnum type, Object data);

    void save(int caseId, long workOrderId, WorkOrderExtContentTypeEnum type, Object data);

    <T> T getByClazz(long workOrderId, WorkOrderExtContentTypeEnum typeEnum, Class<T> clazz);

    <T> T getByCaseIdAndClazz(int caseId, WorkOrderExtContentTypeEnum typeEnum, Class<T> clazz);

    <T> List<T> listByCaseIdAndClazz(int caseId, WorkOrderExtContentTypeEnum typeEnum, Class<T> clazz);

    /**
     * 根据工单id获取前置审核工单附加信息
     * 获取工单附加信息的时候优先取表中的。
     * 如果表中没有则取最新的案例信息
     * @param wordOrderId
     * @return
     */
    FirsApproveMaterialWithAmount getFirstApproveWorkOrderExt(int wordOrderId);

    /**
     * 保存首次审核工单附加数据
     * @param workOrderId
     * @param data
     */
    void saveFirstApproveWorkOrderExt(long workOrderId, CfFirsApproveMaterial data, int targetAmount);

    void saveHospitalAuditSnapshot(int caseId, long workOrderId, CfHospitalAuditInfoNew data);

    List<CfHospitalAuditInfoNew> listHospitalAuditSnapshot(int caseId);

    CfHospitalAuditInfoNew getWorkOrderHospitalAuditSnapshot(long wordOrderId);

    List<WorkOrderExt> listByCaseIdAndType(int caseId, WorkOrderExtContentTypeEnum contentType);
}
