package com.shuidihuzhu.cf.service.sensitive.checker;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.admin.exception.ManyVerifyInfoException;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdFundingVerificationBiz;
import com.shuidihuzhu.cf.client.feign.CfPlatformEsFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordResult;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.enums.crowdfunding.CfSensitiveWordRecordEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.IdcardVerifyStatus;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification;
import com.shuidihuzhu.cf.model.crowdfunding.UserRealInfo;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.risk.client.aegis.EngineAnalysisClient;
import com.shuidihuzhu.cf.risk.model.aegis.RiskAnalysisDto;
import com.shuidihuzhu.cf.risk.model.aegis.RiskObject;
import com.shuidihuzhu.cf.service.sensitive.adapter.ISensitiveAdapter;
import com.shuidihuzhu.cf.vo.sensitive.ExpVerifyMostTimes;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020-11-30
 */
@Slf4j
@Service
public class ManyVerificationChecker implements ISensitiveChecker {

    @Resource
    private AdminCrowdFundingVerificationBiz verificationBiz;
    @Resource
    private EngineAnalysisClient analysisClient;
    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Resource
    private CfPlatformEsFeignClient platformEsFeignClient;

    @Override
    public AdminWorkOrderConst.Task getTask() {
        return AdminWorkOrderConst.Task.EXP_VERIFY_MANY_TIMES;
    }

    @Override
    public <T> OpResult<RiskWordResult> isHit(T data, ISensitiveAdapter<T> adapter) {
        if (adapter.getSensitiveRecordBizType(data) == CfSensitiveWordRecordEnum.BizType.VERIFICATION) {
            if (log.isDebugEnabled()) {
                log.debug("receive verify check:{}", JSON.toJSONString(data));
            }
            CrowdFundingVerification verification = (CrowdFundingVerification) data;
            //查询用户手机号
            UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByUserId(verification.getVerifyUserId());
            if (userInfoModel == null || StringUtils.isBlank(userInfoModel.getCryptoMobile())) {
                return OpResult.createSucResult(new RiskWordResult(true, "", Collections.emptyList()));
            }
            //查询证实用户身份证号
            UserRealInfo userRealInfo = new UserRealInfo();
            userRealInfo.setUserId(verification.getVerifyUserId());
            userRealInfo.setIdcardVerifyStatus(IdcardVerifyStatus.HANDLE_SUCCESS.getCode());
            FeignResponse<List<UserRealInfo>> response = platformEsFeignClient.queryUserRealInfo(userRealInfo);
            if (response.notOk() || CollectionUtils.isEmpty(response.getData())) {
                throw new ManyVerifyInfoException("query user real info failed, wait next time");
            }
            //查询用户当日证实数量
            boolean verifySomeTimes = verificationBiz.isVerifySomeTimes(verification.getVerifyUserId(),
                    DateUtil.getStartCalendar(new Date()).getTime(), 11);
            RiskAnalysisDto riskAnalysisDto = new RiskAnalysisDto("cee1374bf85f434fa10d0b1a3bcfec37", null,
                    new ExpVerifyMostTimes(verification.getVerifyUserId(), adapter.getCaseId(data), verifySomeTimes,
                            userInfoModel.getCryptoMobile(), response.getData().get(0).getCryptoIdCard()));
            Response<Map<String, RiskObject>> analyze = analysisClient.analyze(riskAnalysisDto);
            log.info("engine analyze param:{}, resp:{}", riskAnalysisDto, analyze);
            Boolean isHit = Optional.ofNullable(analyze).map(Response::getData)
                    .map(riskObject -> riskObject.get("exp_verify_most_times"))
                    .map(RiskObject::isRiskFlag).orElse(false);
            if (isHit) {
                return OpResult.createSucResult(new RiskWordResult(false, "", Collections.emptyList()));
            }
        }

        return OpResult.createSucResult(new RiskWordResult(true, "", Collections.emptyList()));
    }

}
