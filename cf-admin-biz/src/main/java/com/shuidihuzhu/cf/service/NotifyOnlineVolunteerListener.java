package com.shuidihuzhu.cf.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.enums.NotifyOnlineVolunteerEventEnum;
import com.shuidihuzhu.cf.event.NotifyOnlineVolunteerEvent;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.CrowdfundingInitialAuditInfo;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.client.cf.api.chaifenbeta.crowdfunding.CrowdfundingOperationFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.client.ShuidiChouQyWxFeignClient;
import com.shuidihuzhu.client.cf.growthtool.client.ClewPreproseMaterialFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.ReportRelation;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class NotifyOnlineVolunteerListener implements ApplicationListener<NotifyOnlineVolunteerEvent> {
    // 通知文案
    private final static String emergencyQiYeWechatMsg = "【审核提醒】案例ID：{0}预计30-40分钟能有审核结果，请耐心等待，如有疑问，请联系班长进【线上发起业务风控对接群】联系应急组，值班时间8：30-23：00。【消息通知时间：{1}】";
    private final static String workOrderCreateMsg = "【审核提醒】案例ID：{0}案例仍在审核中，催审请经班长至【业务风控对接群】联系医学组值班人员（值班时间：9:00-23:00）";
    private final static String workOrderRejectMsg = "【审核驳回/停止】案例ID：{0}案例未过审，请查看驳回原因，如有疑问，请补充证明材料、病情和治疗信息经班长至【业务风控对接群】联系医学组值班人员（值班时间：9:00-23:00）【消息通知时间：{1}】";
    private final static SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
    // 企微水滴筹爱心平台主体 ID
    private final static String LOVE_PLATFORM_CORP_ID = "wwf67888370c3563f8";
    @Resource
    private ShuidiChouQyWxFeignClient shuidiChouQyWxFeignClient;
    @Autowired
    private ClewPreproseMaterialFeignClient clewPreproseMaterialFeignClient;
    @Autowired
    private CrowdfundingOperationFeignClient crowdfundingOperationFeignClient;
    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;
    @Override
    public void onApplicationEvent(NotifyOnlineVolunteerEvent event) {
        if (event.getEventEnum() == null || event.getCaseId() <= 0) {
            return;
        }
        int caseId = event.getCaseId();
        String caseIdStr = String.valueOf(caseId);
        // 初审工单升级应急组
        if (event.getEventEnum() == NotifyOnlineVolunteerEventEnum.INITIAL_AUDIT_EMERGENCY_EVENT) {
            sendQiYeWechatToOnlineVolunteer(caseId, emergencyQiYeWechatMsg, caseIdStr, simpleDateFormat.format(new Date()));
        }

        // 目标金额工单生成
        if (event.getEventEnum() == NotifyOnlineVolunteerEventEnum.TARGET_AMOUNT_WORK_ORDER_CRATE_EVENT) {
            sendQiYeWechatToOnlineVolunteer(caseId, workOrderCreateMsg, caseIdStr);
        }

        // 医疗审核工单生成
        if (event.getEventEnum() == NotifyOnlineVolunteerEventEnum.MEDICAL_WORK_ORDER_CRATE_EVENT) {
            sendQiYeWechatToOnlineVolunteer(caseId, workOrderCreateMsg, caseIdStr);
        }

        // 目标金额审核工单驳回
        if (event.getEventEnum() == NotifyOnlineVolunteerEventEnum.TARGET_AMOUNT_WORK_ORDER_REJECT_EVENT) {
            processTargetAmountWorkOrderReject(caseId);
        }

        // 二次审核工单驳回
        if (event.getEventEnum() == NotifyOnlineVolunteerEventEnum.ER_CI_WORK_ORDER_REJECT_EVENT) {
            processErCiWorkOrderReject(caseId);
        }

        // 医疗审核工单驳回或停止
        if (event.getEventEnum() == NotifyOnlineVolunteerEventEnum.MEDICAL_WORK_ORDER_REJECT_EVENT) {
            sendQiYeWechatToOnlineVolunteer(caseId, workOrderRejectMsg, caseIdStr, simpleDateFormat.format(new Date()));
        }
    }


    // 发送企业微信给线上顾问
    private void sendQiYeWechatToOnlineVolunteer(int caseId, String content, Object... params) {
        if (StringUtils.isEmpty(content)) {
            return;
        }
        Response<ReportRelation> clewPreproseResponse = clewPreproseMaterialFeignClient.getByCaseId(caseId);
        if (clewPreproseResponse == null || clewPreproseResponse.getData() == null) {
            return;
        }
        ReportRelation reportRelation = clewPreproseResponse.getData();
        if (StringUtils.isNotEmpty(reportRelation.getUniqueCode())) {
            return;
        }
        String mis = reportRelation.getMis();
        MessageFormat messageFormat = new MessageFormat(content);
        shuidiChouQyWxFeignClient.sendShuidiChouMsgByCorp(mis, messageFormat.format(params), LOVE_PLATFORM_CORP_ID);
    }

    // 判断驳回项是否有「目标金额」
    private boolean targetAmountIsRejected(int caseId) {
        Response<String> response = crowdfundingOperationFeignClient.selectCrowdfundingInitialAuditInfoByCaseId(caseId);
        if (response == null || response.notOk()) {
            return false;
        }

        String data = response.getData();
        CrowdfundingInitialAuditInfo crowdfundingInitialAuditInfo = JSON.parseObject(data, CrowdfundingInitialAuditInfo.class);
        String rejectDetail = crowdfundingInitialAuditInfo.getRejectDetail();
        if (StringUtils.isEmpty(rejectDetail)) {
            return false;
        }
        Map<Integer, List<InitialAuditItem.RejectReason>> rejectDetailMap = JSON.parseObject(crowdfundingInitialAuditInfo.getRejectDetail(), new TypeReference<Map<Integer, List<InitialAuditItem.RejectReason>>>() {});
        return rejectDetailMap != null && rejectDetailMap.containsKey(10);
    }

    private void processTargetAmountWorkOrderReject(int caseId) {
        Response<WorkOrderVO> response = cfWorkOrderClient.getLastWorkOrderByTypes(caseId, Lists.newArrayList(WorkOrderType.ai_erci.getType()));
        if (response == null || response.getData() == null) {
            return;
        }
        WorkOrderVO workOrderVO = response.getData();
        if (workOrderVO.getHandleResult() == HandleResultEnum.audit_reject.getType()) {
            if (targetAmountIsRejected(caseId)) {
                sendQiYeWechatToOnlineVolunteer(caseId, workOrderRejectMsg, String.valueOf(caseId), simpleDateFormat.format(new Date()));
            }
        }
    }
    private void processErCiWorkOrderReject(int caseId) {
        if (!targetAmountIsRejected(caseId)) {
            return;
        }
        Response<WorkOrderVO> response = cfWorkOrderClient.getLastWorkOrderByTypes(caseId, Lists.newArrayList(WorkOrderType.target_amount_reasonable_audit.getType()));
        if (response == null || response.getData() == null) {
            return;
        }
        WorkOrderVO workOrderVO = response.getData();
        if (workOrderVO.getHandleResult() == HandleResultEnum.audit_reject.getType()) {
            sendQiYeWechatToOnlineVolunteer(caseId, workOrderRejectMsg, String.valueOf(caseId), simpleDateFormat.format(new Date()));
        }
    }
}
