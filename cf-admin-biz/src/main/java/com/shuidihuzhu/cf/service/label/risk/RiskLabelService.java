package com.shuidihuzhu.cf.service.label.risk;

import com.shuidihuzhu.cf.client.ugc.wonrecord.WonRecord;
import com.shuidihuzhu.cf.domain.label.core.LabelNodeModel;
import com.shuidihuzhu.cf.domain.label.risk.Label;
import com.shuidihuzhu.cf.domain.label.risk.RiskLabelAuditMarkParam;
import com.shuidihuzhu.cf.domain.label.risk.RiskLabelMarkParam;
import com.shuidihuzhu.cf.domain.label.risk.RiskLabelMarkRecord;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface RiskLabelService {

    List<LabelNodeModel> getSimpleLabelNodeByIdList(List<Long> labelIdList);

    /**
     * 根据标签id列表获取标签数据
     * @param ids
     * @return
     */
    List<Label> getLabelByIds(List<Long> ids);

    boolean mark(RiskLabelMarkParam param);

    boolean judgeNewRiskLabel(Integer caseId);

    void auditMark(RiskLabelAuditMarkParam auditMarkParam);

    Response<List<Label>> getLabelByUuidList(List<String> uuidList);


    /**
     *
     * 根据标签id获取该标签信息与对应子标签树数据
     * @param id 标签id
     * @return 该标签信息与对应子标签树数据
     */
    Response<LabelNodeModel> getLabelNodeById(long id);

    Response<List<RiskLabelMarkRecord>> getMarkRecord(int caseId, int markType);

    List<String> getRiskPrimaryLabels(int caseId, int markType);

    /**
     * 风险标签是否关联驳回项
     *
     * @param id 风险标签Id
     * @return true 关联 false 未关联
     */
    boolean relatedRefuseEntity(long id);

    Response<RiskLabelMarkRecord> getLastMarkFixRecord(int caseId);

    Response<List<WonRecord>> getMarkFixOperateRecordByCaseId(int caseId);
}
