package com.shuidihuzhu.cf.service.label.risk;

import com.shuidihuzhu.cf.domain.label.core.LabelDO;
import com.shuidihuzhu.cf.domain.label.risk.Label;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface LabelMapper {

    LabelMapper INSTANCE = Mappers.getMapper(LabelMapper.class);

    Label toLabel(LabelDO labelDO);

    List<Label> toLabelList(List<LabelDO> list);

}
