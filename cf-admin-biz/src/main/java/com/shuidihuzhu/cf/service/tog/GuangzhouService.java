package com.shuidihuzhu.cf.service.tog;

import com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord;
import com.shuidihuzhu.cf.model.tog.GuangzhouMarkShowVO;

public interface GuangzhouService {

    void addOrUpdateGuangzhouLabel(int caseId);

    void addOrUpdateGuangzhouLabel(int caseId, int workOrderType, long workOrderId);

    /**
     * 查询广州标签和上一次对c端是否展示标签
     * @param caseId
     * @return
     */
    GuangzhouMarkShowVO getGuangzhouMarkShowVO(int caseId);

    /**
     * 更新c端是否展示广州标签
     * @param caseId
     * @param showStatusToC
     * @param reason
     * @return
     */
    boolean updateShowStatusToC(int caseId, int showStatusToC, String reason, int adminUserId);





}
