package com.shuidihuzhu.cf.service.cfOperatingProfile;

import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingCaseLabel;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileSettings;

import java.util.List;
import java.util.Set;

public interface CfOperatingCaseLabelBiz {


    List<CfOperatingProfileSettings> selectCaseLabels(int caseId);

    List<CfOperatingCaseLabel> selectOperatingCaseLabels(int caseId);

    CfOperatingProfileSettings selectById(int id);

    AdminErrorCode addCaseLabels(CfOperatingCaseLabel.QueryParam queryParam);

    boolean selectHasLabelContent(int caseId, String content);
}
