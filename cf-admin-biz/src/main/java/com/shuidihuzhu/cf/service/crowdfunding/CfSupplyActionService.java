package com.shuidihuzhu.cf.service.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import com.shuidihuzhu.cf.delegate.saas.AdminOrganization;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.OrganizationClientV1;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdFundingProgressBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminPushDynamicMsgService;
import com.shuidihuzhu.cf.biz.crowdfunding.supply.CfSupplyActionBiz;
import com.shuidihuzhu.cf.client.feign.AuthorFeignClient;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationRecordDTO;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.clinet.event.center.enums.BizParamEnum;
import com.shuidihuzhu.cf.clinet.event.center.enums.UserOperationTypeEnum;
import com.shuidihuzhu.cf.clinet.event.center.model.CfUserEvent;
import com.shuidihuzhu.cf.delegate.OrganizationDelegate;
import com.shuidihuzhu.cf.delegate.SeaAccountDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdFundingProgressType;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyAction.ActionType;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyAction.SupplyHandleStatus;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfInfoSupplyActionVo;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.cf.vo.crowdfunding.CfSupplyProgressVo;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.admin.enums.CfFieldDesc;
import com.shuidihuzhu.client.cf.admin.enums.CfProgressReasonEnum;
import com.shuidihuzhu.client.cf.admin.enums.CfProgressReasonRelationEnum;
import com.shuidihuzhu.client.cf.admin.model.CfInfoSupplyField;
import com.shuidihuzhu.client.cf.admin.model.CfItemField;
import com.shuidihuzhu.client.cf.admin.model.CfSupplyActionVo;
import com.shuidihuzhu.client.cf.workorder.CfUgcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCreateParam;
import com.shuidihuzhu.client.cf.workorder.helper.Von;
import com.shuidihuzhu.client.cf.workorder.model.UgcHandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.UgcWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderLevel;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.SetUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
@Slf4j
public class CfSupplyActionService {

    @Autowired
    private CfSupplyActionBiz cfSupplyActionBiz;

    @Autowired
    private CommonOperationRecordClient commonOperationRecordClient;

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @Autowired
    private AdminPushDynamicMsgService dynamicMsgService;

    @Autowired
    AdminCrowdFundingProgressBiz adminCrowdFundingProgressBiz;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;

    @Resource
    private ApproveRemarkOldService remarkOldService;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private CfUgcWorkOrderClient ugcWorkOrderClient;

    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;

    @Autowired
    private SeaAccountDelegate seaAccountDelegate;

    @Resource
    private OrganizationClientV1 organizationClientV1;

    @Autowired
    private OrganizationDelegate organizationDelegate;

    @Autowired(required = false)
    private Producer producer;

    @Autowired
    private AuthorFeignClient authorFeignClient;

    @Resource
    private IRiskDelegate riskDelegate;

    @Autowired
    private CfTemplateFieldService cfTemplateFieldService;

    @Autowired
    private MaskUtil maskUtil;


    //备注中的额外字段信息
    private static final String OPERATION_RECORD_REJECT = "rejectRecord";

    private static final String OPERATION_RECORD_EXT_ACTION_ID = "supplyActionId";

    private static final String USER_EVENT_TAG = com.shuidihuzhu.cf.clinet.event.center.constants.MQTagCons.USER_EVENT_FOR_EVENT_CENTER;
    private static final String USER_EVENT_TOPIC = com.shuidihuzhu.cf.clinet.event.center.constants.MQTopicCons.CF;

    public Response<CfSupplyActionVo> getByActionId(long actionId){

        CfSupplyActionVo vo = new CfSupplyActionVo();

        CfInfoSupplyAction cfInfoSupplyAction = cfSupplyActionBiz.getById(actionId);

        if (cfInfoSupplyAction == null){
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }
        vo.setActionStatus(cfInfoSupplyAction.getHandleStatus());

        List<CfInfoSupplyField> list =  cfSupplyActionBiz.getByActionId(actionId);


        //为c端逻辑兼容   去掉content
        vo.setList(list.stream().filter(r->r.getOperationType() == CfInfoSupplyField.serviceType).filter(r-> !r.getField().equals(CfFieldDesc.content.getField())).map(r->{r.setActionId(0);return r;}).collect(Collectors.toList()));
        //为c端逻辑兼容   如果没有imgUrls 补全
        Optional optional = vo.getList().stream().filter(r->r.getField().equals(CfFieldDesc.imgUrls.getField())).findAny();
        if (!optional.isPresent()){
            CfInfoSupplyField c = new CfInfoSupplyField();
            c.setOperationType(CfInfoSupplyField.serviceType);
            c.setField(CfFieldDesc.imgUrls.getField());
            c.setFieldName(CfFieldDesc.imgUrls.getFieldName());
            vo.getList().add(c);
        }

        List<Long> fids = list.stream().filter(r->r.getFieldId() > 0).map(CfInfoSupplyField::getFieldId).collect(Collectors.toList());

        //如果是模板需要查询
        if (CollectionUtils.isNotEmpty(fids)){

            List<CfItemField> fields = cfTemplateFieldService.getCfItemFieldByIds(fids);

            Map<Long,CfItemField> map = fields.stream().collect(Collectors.toMap(CfItemField::getId,Function.identity()));

            vo.getList().stream().forEach(r->{
                r.setCfItemField( Optional.ofNullable(map.get(r.getFieldId())).orElse(null));
            });
            vo.setContent(cfTemplateFieldService.getById(Long.valueOf(cfInfoSupplyAction.getSupplyReason())).getTemplateValue());
        }

        vo.setReasonCode(Integer.valueOf(cfInfoSupplyAction.getSupplyReason()));
        vo.setImgUrls(cfInfoSupplyAction.getImgUrls());

        return NewResponseUtil.makeSuccess(vo);
    }

    public OpResult<String> doSubmit(CfSupplyActionVo cfSupplyActionVo){
        log.debug("doSubmit cfSupplyActionVo={}",JSON.toJSONString(cfSupplyActionVo));
        //校验是否能提交生成工单
        long supplyActionId = cfSupplyActionVo.getActionId();
        CfInfoSupplyAction cfInfoSupplyAction = cfSupplyActionBiz.getById(supplyActionId);
        if (cfInfoSupplyAction == null || !CfInfoSupplyAction.canSubmit(cfInfoSupplyAction.getHandleStatus())) {
            log.info("下发动态无效,supplyAction:{}", JSON.toJSONString(cfSupplyActionVo));
            return OpResult.createFailResult(AdminErrorCode.SYSTEM_NO_ID);
        }
        int caseId = cfInfoSupplyAction.getCaseId();

        CrowdfundingInfo c = getCrowdfundingInfo(caseId);

        if (c == null){
            return OpResult.createFailResult(AdminErrorCode.CF_NOT_FOUND);
        }
        List<CfInfoSupplyField> list = null;
        if (cfInfoSupplyAction.getUseTemplate() == 0){

            OpResult<List<CfInfoSupplyField>> opResult = checkFeild(Integer.valueOf(cfInfoSupplyAction.getSupplyReason()),cfSupplyActionVo.getList());
            if (opResult.isFail()){
                return OpResult.createFailResult(opResult.getErrorCode());
            }
            list = opResult.getData();
        }else {
            OpResult<List<CfInfoSupplyField>> opResult = checkTemplate(Long.valueOf(cfInfoSupplyAction.getSupplyReason()),cfSupplyActionVo.getList());
            if (opResult.isFail()){
                return OpResult.createFailResult(opResult.getErrorCode());
            }
            list = opResult.getData();
        }

        if (StringUtils.isNotEmpty(cfSupplyActionVo.getImgUrls())){
            String urls = cfInfoSupplyAction.getImgUrls();
            List<String> urlList = Splitter.on(",").splitToList(urls);
            if (urlList.size()>16){
                return OpResult.createFailResult(AdminErrorCode.SYSTEM_PARAM_ERROR);
            }
        }

        log.info("下发动态supplyActionId:{}", supplyActionId);

        WorkOrderCreateParam createParam = new WorkOrderCreateParam();
        createParam.setCaseId(caseId);
        createParam.setOrderType(WorkOrderType.xiafaprogress.getType());
        createParam.setOrderlevel(OrderLevel.edium.getType());
        createParam.addExt("supplyActionId", supplyActionId);
        createParam.addExt("reasonCode", cfInfoSupplyAction.getSupplyReason());
        createParam.addExt("isNeiShenSupply", cfInfoSupplyAction.isNeiShenSupply());
        createParam.addExt("vonPriorAssignOperatorId", cfInfoSupplyAction.getSupplyUserId());

        Response<Long> response = Von.core().create(createParam);

        if (response.notOk()) {
            log.error("调用工单系统异常 caseid={}",caseId);
            return OpResult.createFailResult(AdminErrorCode.SYSTEM_TASK_IS_RUNNING);
        }
        //修改下发状态,最后修改下发状态,防止调用工单系统失败,下发状态直接卡在待审核但无工单
        cfSupplyActionBiz.updateForSubmit(supplyActionId, SupplyHandleStatus.wait_audit.getCode(),cfSupplyActionVo.getContent(),cfSupplyActionVo.getImgUrls());

        //增加内容和图片
        setField(list,getCfInfoSupplyField(cfSupplyActionVo.getContent(),CfFieldDesc.content,CfInfoSupplyField.userType));
        setField(list,getCfInfoSupplyField(cfSupplyActionVo.getImgUrls(),CfFieldDesc.imgUrls,CfInfoSupplyField.userType));

        cfSupplyActionBiz.insertCfInfoSupplyFields(list,supplyActionId,CfInfoSupplyField.userType);

        return OpResult.createSucResult();
    }

    public Response<Boolean> doSupply(CfInfoSupplyAction supplyAction) {

        int adminUserId = supplyAction.getSupplyUserId();
        int caseId = supplyAction.getCaseId();

        CrowdfundingInfo c = getCrowdfundingInfo(caseId);

        if (c == null){
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }

//        if (c.getStatus() == CrowdfundingStatus.CROWDFUNDING_STATED) {
//            return NewResponseUtil.makeFail("案例审核通过,无法下发!");
//        }

        Set<Long> set = getNoRepeat(caseId);

        if (set.contains(Long.valueOf(supplyAction.getSupplyReason()))){
            return NewResponseUtil.makeFail("已存在动态,无法下发!");
        }

        OpResult<List<CfInfoSupplyField>> opResult = checkTemplate(Long.valueOf(supplyAction.getSupplyReason()),supplyAction.getSupplyFields());
        if (opResult.isFail()){
            return NewResponseUtil.makeError(opResult.getErrorCode());
        }

        if (StringUtils.isNotEmpty(supplyAction.getImgUrls())){
            String urls = supplyAction.getImgUrls();
            List<String> urlList = Splitter.on(",").splitToList(urls);
            if (urlList.size()>=9){
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
            }
        }

        supplyAction.setActionType(ActionType.progress_new.getCode());
        supplyAction.setHandleStatus(SupplyHandleStatus.init.getCode());

        setUserAndOrg(adminUserId,supplyAction);

        //默认全部使用模板
        supplyAction.setUseTemplate(1);
        boolean insert = cfSupplyActionBiz.insert(supplyAction);

        if (!insert) {
            return NewResponseUtil.makeFail("下发失败");
        }

        //插入对应模块
        List<CfInfoSupplyField> list = opResult.getData();

        setField(list,getCfInfoSupplyField(supplyAction.getComment(),CfFieldDesc.content,CfInfoSupplyField.serviceType));
        setField(list,getCfInfoSupplyField(supplyAction.getImgUrls(),CfFieldDesc.imgUrls,CfInfoSupplyField.serviceType));

        cfSupplyActionBiz.insertCfInfoSupplyFields(list,supplyAction.getId());

        commonOperationRecordClient
                .create()
                .buildBasicPlatform(supplyAction.getId(), adminUserId, OperationActionTypeEnum.SUPPLY_PROGRESS)
                .save();
        String name = cfTemplateFieldService.getById(Long.valueOf(supplyAction.getSupplyReason())).getTemplateName();
        String caseComment = String.format("下发动态[下发id:%d],下发原因:\n%s\n下发备注:%s", supplyAction.getId(), name, supplyAction.getComment());
        //案例备注
        remarkOldService.add(caseId, adminUserId, caseComment);
        //调用暂停打款接口
        Response pauseResponse = dynamicMsgService.pauseDrawCashByDynamic(caseId, adminUserId);

        if (pauseResponse.notOk()) {
            log.warn("暂停打款失败,caseId:{},response:{}", caseId, pauseResponse);
        }
        supplyAction.setInfoUUId(c.getInfoId());
        sendMessage(supplyAction,UserOperationTypeEnum.DYNAMIC_GENERATION_MODIFICATION_DYNAMIC.getCode());

        return NewResponseUtil.makeSuccess(true);

    }

    private void setField( List<CfInfoSupplyField> list,CfInfoSupplyField c){
        if (CollectionUtils.isEmpty(list) || c == null){
            return;
        }
        list.add(c);

    }

    private CfInfoSupplyField getCfInfoSupplyField(String value, CfFieldDesc fieldDesc,int operationType){

        if (StringUtils.isBlank(value)){
            return null;
        }

        CfInfoSupplyField c = new CfInfoSupplyField();
        c.setFieldValue(value);
        c.setField(fieldDesc.getField());
        c.setFieldName(fieldDesc.getFieldName());
        c.setOperationType(operationType);
        return c;
    }




    private void setUserAndOrg(int adminUserId,CfInfoSupplyAction supplyAction){
        AuthRpcResponse<AdminOrganization> userOrgInfo = organizationClientV1.getUserOrgInfo(adminUserId);
        if (userOrgInfo.isSuccess()){
            AdminOrganization adminOrganization = userOrgInfo.getResult();
            supplyAction.setSupplyOrgId(adminOrganization != null ? adminOrganization.getId() : 0);
            supplyAction.setOrgForSearch(3);
            supplyAction.setSupplyOrgName(organizationDelegate.getSimpleOrganization(adminUserId));
        }
        supplyAction.setOperator(seaAccountDelegate.getNameByUserId(adminUserId));
    }

    public List<CfInfoSupplyAction> getActionList(int caseId){

        List<CfInfoSupplyAction> infoSupplyActions = cfSupplyActionBiz.listByCaseIdAndType(caseId,ActionType.progress_new.getCode());

        infoSupplyActions.stream().filter(r->r.getActionType()==ActionType.progress_new.getCode())
                .forEach(r->{
                    if (r.getUseTemplate()==0){
                        r.setSupplyReason(CfProgressReasonEnum.getByCode(Integer.valueOf(r.getSupplyReason())).getName());
                    }else {
                        r.setSupplyReason(cfTemplateFieldService.getById(Long.valueOf(r.getSupplyReason())).getTemplateName());
                    }
                    r.setOperator(seaAccountDelegate.getNameByUserId(r.getSupplyUserId()));
                });

        return infoSupplyActions;
    }

    public Map<Long,CfInfoSupplyAction> getActionMap(List<Long> ids){

        if (CollectionUtils.isEmpty(ids)){
            return Maps.newHashMap();
        }

        List<CfInfoSupplyAction> infoSupplyActions = cfSupplyActionBiz.listByIds(ids);

        return infoSupplyActions.stream().collect(Collectors.toMap(CfInfoSupplyAction::getId, Function.identity()));
    }

    public List<CfInfoSupplyAction> getDetailList(int caseId){

        List<CfInfoSupplyAction> result = Lists.newArrayList();

        CrowdfundingInfo c = getCrowdfundingInfo(caseId);
        if (c == null){
            log.info("getDeatilList caseId={} c=null",caseId);
            return result;
        }

        List<CfInfoSupplyAction> infoSupplyActions = cfSupplyActionBiz.listByCaseIdAndType(caseId,ActionType.progress_new.getCode());

        result.addAll(infoSupplyActions.stream().filter(r->r.getHandleStatus() == SupplyHandleStatus.init.getCode())
                .map(r->{
                    r.setInfoUUId(c.getInfoId());
                    r.setOperator(seaAccountDelegate.getNameByUserId(r.getSupplyUserId()));
                    return r;
                })
                .collect(Collectors.toList()));

        Response<List<WorkOrderVO>> response =  cfWorkOrderClient.queryByCaseAndTypes(caseId,Lists.newArrayList(WorkOrderType.xiafaprogress.getType()));

        Optional.ofNullable(response).filter(Response::ok)
                .map(Response::getData).get().stream()
                .filter(r->r.getSupplyActionId() > 0)
                .forEach(r->{
                    CfInfoSupplyAction supply = cfSupplyActionBiz.getById(r.getSupplyActionId());
                    CfInfoSupplyAction a = new CfInfoSupplyAction();
                    a.setWorkOrderId(r.getWorkOrderId());
                    a.setWorkOrderStatus(r.getHandleResult());
                    a.setUpdateTime(r.getUpdateTime());
                    a.setCreateTime(supply.getCreateTime());
                    a.setInfoUUId(c.getInfoId());
                    a.setCaseId(c.getId());
                    a.setId(r.getSupplyActionId());
                    a.setOperator(seaAccountDelegate.getNameByUserId(supply.getSupplyUserId()));
                    a.setSupplyOrgName(supply.getSupplyOrgName());
                    if (r.getOperatorId() > 0){
                        a.setHandleOperatorName(seaAccountDelegate.getNameByUserId(Long.valueOf(r.getOperatorId()).intValue()));
                        a.setHandleOperatorOrgName(organizationDelegate.getSimpleOrganization(Long.valueOf(r.getOperatorId()).intValue()));
                    }
                    result.add(a);
                });



        return result;
    }

    public Response<CfInfoSupplyActionVo> getCfInfoSupplyActionVo(long actionId,long workOrderId){

        CfInfoSupplyActionVo vo = new CfInfoSupplyActionVo();

        //下发动态信息
        CfInfoSupplyAction supplyAction = cfSupplyActionBiz.getById(actionId);
        if (supplyAction == null){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_NO_ID);
        }

        CrowdfundingInfo c = getCrowdfundingInfo(supplyAction.getCaseId());

        if (c == null){
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }


        List<CfInfoSupplyField> list = cfSupplyActionBiz.getByActionId(actionId);
        List<Long> fids = list.stream().filter(r->r.getFieldId() > 0).map(CfInfoSupplyField::getFieldId).collect(Collectors.toList());
        //如果是模板需要查询
        if (CollectionUtils.isNotEmpty(fids)){

            List<CfItemField> fields = cfTemplateFieldService.getCfItemFieldByIds(fids);

            Map<Long,CfItemField> map = fields.stream().collect(Collectors.toMap(CfItemField::getId,Function.identity()));

            list.stream().forEach(r->{
                r.setCfItemField( Optional.ofNullable(map.get(r.getFieldId())).orElse(null));
            });
        }

        supplyAction.setOperator(seaAccountDelegate.getNameByUserId(supplyAction.getSupplyUserId()));
        supplyAction.setSupplyFields(list);
        supplyAction.setUserSupplyFields(list.stream().filter(r->r.getOperationType()==CfInfoSupplyField.userType).collect(Collectors.toList()));
        supplyAction.setServiceSupplyFields(list.stream().filter(r->r.getOperationType()==CfInfoSupplyField.serviceType).collect(Collectors.toList()));
        vo.setAction(supplyAction);
        if (supplyAction.getUseTemplate() == 0){
            vo.setTemplate(CfProgressReasonEnum.getByCode(Integer.valueOf(supplyAction.getSupplyReason())).getTemplate());
            supplyAction.setSupplyReason(CfProgressReasonEnum.getByCode(Integer.valueOf(supplyAction.getSupplyReason())).getName());
        }else {
            CfItemTemplate template = cfTemplateFieldService.getById(Long.valueOf(supplyAction.getSupplyReason()));
            vo.setTemplate(template.getTemplateValue());
            supplyAction.setSupplyReason(template.getTemplateName());
        }



        //工单信息
        if (workOrderId > 0){
            Response<WorkOrderVO> response = cfWorkOrderClient.getWorkOrderById(workOrderId);
            WorkOrderVO workOrderVO = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(null);
            if (workOrderVO != null){
                vo.setWorkOrderId(workOrderId);
                vo.setTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(workOrderVO.getUpdateTime()));
                if (workOrderVO.getOperatorId()>0){
                    vo.setName(seaAccountDelegate.getNameByUserId(Long.valueOf(workOrderVO.getOperatorId()).intValue()));
                }
                vo.setStatus(workOrderVO.getHandleResult());
            }
        }

        //案例信息
        vo.setCaseId(c.getId());
        vo.setTitle(c.getTitle());
        vo.setInfoUuid(c.getInfoId());

        FeignResponse<CfFirsApproveMaterial> feignResponse = authorFeignClient.getAuthorInfoByInfoId(c.getId());
        if (feignResponse.getData() != null){
            CfFirsApproveMaterial material = feignResponse.getData();
            vo.setInitiator(material.getSelfRealName());
            if (StringUtils.isEmpty(vo.getInitiator())){
                vo.setInitiator(material.getPatientRealName());
            }
        }
        UserInfoModel userInfo = userInfoServiceBiz.getUserInfoByUserId(c.getUserId());
        if (userInfo != null){
            NumberMaskVo numberMaskVo = maskUtil.buildByEncryptPhone(userInfo.getCryptoMobile());
            if(numberMaskVo != null){
                vo.setInitiatorPhoneMask(numberMaskVo);
            }
            vo.setInitiatorPhoneMask(numberMaskVo);
        }
        if (StringUtils.isNotEmpty(c.getPayeeName()) && StringUtils.isNotEmpty(c.getPayeeMobile())){
            vo.setReceiver(c.getPayeeName());
            NumberMaskVo numberMaskVo = maskUtil.buildByEncryptPhone(c.getPayeeMobile());
            if(numberMaskVo != null){
                vo.setReceiverPhoneMask(numberMaskVo);
            }
            vo.setReceiverPhoneMask(numberMaskVo);
        }
        return NewResponseUtil.makeSuccess(vo);
    }


    public Response<Boolean> doUpdate(CfInfoSupplyAction supplyAction){

        if (supplyAction.getHandleStatus() == SupplyHandleStatus.pass.getCode()){
            return pass(supplyAction);
        }

        if (supplyAction.getHandleStatus() == SupplyHandleStatus.cancel.getCode()){
            return cancel(supplyAction);
        }

        if (supplyAction.getHandleStatus() == SupplyHandleStatus.reject.getCode()){
            return reject(supplyAction);
        }

        return NewResponseUtil.makeError(AdminErrorCode.OPERATION_FAILED);

    }


    public Response<Boolean> pass(CfInfoSupplyAction supplyAction) {

        if (supplyAction.getWorkOrderId() <= 0){
            return NewResponseUtil.makeFail("工单id为空");
        }

        //检查是否能操作当前下发
        long supplyActionId = supplyAction.getId();
        int adminUserId = supplyAction.getSupplyUserId();

        CfInfoSupplyAction cfInfoSupplyAction = cfSupplyActionBiz.getById(supplyActionId);

        Response<Void> checkResp = checkStatus(cfInfoSupplyAction);
        if (checkResp.notOk()) {
            return NewResponseUtil.makeRelayFail(checkResp);
        }

        int caseId = cfInfoSupplyAction.getCaseId();

        CrowdfundingInfo c = getCrowdfundingInfo(caseId);
        if (c == null){
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }

        //修改下发动态状态
        cfSupplyActionBiz.updateHandleStatus(supplyActionId, SupplyHandleStatus.pass.getCode());

        // 将填充后的资金进展progress 入 crowdfunding_progress表
        CrowdFundingProgress crowdFundingProgress = new CrowdFundingProgress();
        crowdFundingProgress.setUserId(c.getUserId());
        crowdFundingProgress.setImageUrls(cfInfoSupplyAction.getImgUrls());
        crowdFundingProgress.setContent(cfInfoSupplyAction.getComment());
        crowdFundingProgress.setType(CrowdFundingProgressType.PROGRESS_NEEDS_AUDIT.value());
        crowdFundingProgress.setTitle("");
        crowdFundingProgress.setActivityId(caseId);
        adminCrowdFundingProgressBiz.insertInCrowdfundingProgress(crowdFundingProgress);

        if(canStopCash(caseId)){
            //调用取消暂停打款接口
            Response recoverPauseResponse = dynamicMsgService.recoverPauseDrawCashByDynamic(caseId);
            if (recoverPauseResponse.notOk()) {
                log.warn("取消暂停打款失败,caseId:{},response:{}", caseId, recoverPauseResponse);
            }
        }

        //处理新工单
        UgcHandleOrderParam ugcHandleOrderParam = new UgcHandleOrderParam();
        ugcHandleOrderParam.setWorkOrderIds(Lists.newArrayList(supplyAction.getWorkOrderId()));
        ugcHandleOrderParam.setWorkOrderId(supplyAction.getWorkOrderId());
        ugcHandleOrderParam.setOrderType(WorkOrderType.xiafaprogress.getType());
        ugcHandleOrderParam.setHandleResult(HandleResultEnum.audit_pass.getType());
        ugcHandleOrderParam.setUserId(adminUserId);
        ugcHandleOrderParam.setOperComment("");
        Response workResponse = ugcWorkOrderClient.handleUgc(ugcHandleOrderParam);
        if (workResponse.notOk()) {
            return NewResponseUtil.makeFail(workResponse.getMsg());
        }

        //添加日志
        commonOperationRecordClient
                .create()
                .buildBasicPlatform(supplyActionId, adminUserId, OperationActionTypeEnum.SUPPLY_PROGRESS_PASS)
                .buildExtValue(OPERATION_RECORD_EXT_ACTION_ID, String.valueOf(supplyActionId))
                .save();
        //案例备注
        remarkOldService.add(caseId, adminUserId, String.format("下发动态审核[工单id:%d],下发审核通过", supplyAction.getWorkOrderId()));

        return NewResponseUtil.makeSuccess(true);

    }

    private boolean canStopCash(int caseId){

        List<CfInfoSupplyAction> infoSupplyActions = cfSupplyActionBiz.listByCaseIdAndTypes(caseId,Lists.newArrayList(ActionType.progress.getCode(),ActionType.progress_new.getCode()));
        Optional optional = infoSupplyActions.stream()
                .filter(r->r.getHandleStatus() == SupplyHandleStatus.init.getCode()
                        || r.getHandleStatus() == SupplyHandleStatus.wait_audit.getCode()
                        || r.getHandleStatus() == SupplyHandleStatus.reject.getCode()).findAny();

        return !optional.isPresent();

    }

    //撤销
    public Response<Boolean> cancel(CfInfoSupplyAction supplyAction) {

        long supplyId = supplyAction.getId();
        int adminUserId = supplyAction.getSupplyUserId();
        //校验是否可以撤销
        CfInfoSupplyAction cfInfoSupplyAction = cfSupplyActionBiz.getById(supplyId);
        if (cfInfoSupplyAction == null) {
            return NewResponseUtil.makeFail("下发不存在!");
        }
        int caseId = cfInfoSupplyAction.getCaseId();

        if (!CfInfoSupplyAction.canSubmit(cfInfoSupplyAction.getHandleStatus())) {
            return NewResponseUtil.makeFail("当前下发为无法撤销该下发");
        }

        cfSupplyActionBiz.updateHandleStatus(supplyId, SupplyHandleStatus.cancel.getCode());

        commonOperationRecordClient
                .create()
                .buildBasicPlatform(supplyId, adminUserId, OperationActionTypeEnum.CANCEL_SUPPLY_PROGRESS)
                .save();
        //案例备注
        remarkOldService.add(caseId, adminUserId, String.format("下发动态[下发id:%d],下发已撤销", supplyId));

        if(canStopCash(caseId)){
            //调用取消暂停打款接口
            Response recoverPauseResponse = dynamicMsgService.recoverPauseDrawCashByDynamic(caseId);
            if (recoverPauseResponse.notOk()) {
                log.warn("取消暂停打款失败,caseId:{},response:{}", caseId, recoverPauseResponse);
            }
        }

        return NewResponseUtil.makeSuccess(true);
    }

    private Response<Void> checkStatus(CfInfoSupplyAction action) {
        if (action == null){
            return NewResponseUtil.makeFail("找不到此条下发动态");
        }
        int handleStatus = action.getHandleStatus();
        if (handleStatus == SupplyHandleStatus.pass.getCode() || handleStatus == SupplyHandleStatus.reject.getCode()) {
            return NewResponseUtil.makeFail("当前下发动态已审核完成");
        }
        if (handleStatus != SupplyHandleStatus.wait_audit.getCode()){
            return NewResponseUtil.makeFail("当前下发动态不是待审核");
        }
        return NewResponseUtil.makeSuccess();
    }

    //审核驳回
    public Response<Boolean> reject(CfInfoSupplyAction supplyAction) {

        if (supplyAction.getWorkOrderId() <= 0
                || StringUtils.isBlank(supplyAction.getComment())
                || CollectionUtils.isEmpty(supplyAction.getSupplyFields())){
            return NewResponseUtil.makeFail("驳回内容为空");
        }

        long supplyActionId = supplyAction.getId();
        int adminUserId = supplyAction.getSupplyUserId();

        CfInfoSupplyAction cfInfoSupplyAction = cfSupplyActionBiz.getById(supplyActionId);
        Response<Void> checkResp = checkStatus(cfInfoSupplyAction);
        if (checkResp.notOk()) {
            return NewResponseUtil.makeRelayFail(checkResp);
        }
        List<CfInfoSupplyField> list = null;
        if (cfInfoSupplyAction.getUseTemplate() == 0){
            OpResult<List<CfInfoSupplyField>> opResult =  checkFeild(Integer.valueOf(cfInfoSupplyAction.getSupplyReason()),supplyAction.getSupplyFields());
            if (opResult.isFail()){
                return NewResponseUtil.makeError(opResult.getErrorCode());
            }
            list = opResult.getData();
        }else {
            OpResult<List<CfInfoSupplyField>> opResult = checkTemplate(Long.valueOf(cfInfoSupplyAction.getSupplyReason()),supplyAction.getSupplyFields());
            if (opResult.isFail()){
                return NewResponseUtil.makeError(opResult.getErrorCode());
            }
            list = opResult.getData();
        }

        if (StringUtils.isNotEmpty(supplyAction.getImgUrls())){
            String urls = supplyAction.getImgUrls();
            List<String> urlList = Splitter.on(",").splitToList(urls);
            if (urlList.size()>=9){
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
            }
        }

        int caseId = cfInfoSupplyAction.getCaseId();
        //修改下发状态 为 审核驳回
        cfSupplyActionBiz.updateForSubmit(supplyActionId, SupplyHandleStatus.reject.getCode(),supplyAction.getComment(),supplyAction.getImgUrls());

        setField(list,getCfInfoSupplyField(supplyAction.getComment(),CfFieldDesc.content,CfInfoSupplyField.serviceType));
        setField(list,getCfInfoSupplyField(supplyAction.getImgUrls(),CfFieldDesc.imgUrls,CfInfoSupplyField.serviceType));
        cfSupplyActionBiz.insertCfInfoSupplyFields(list,supplyActionId);
        //发送消息
        CrowdfundingInfo c = getCrowdfundingInfo(caseId);
        cfInfoSupplyAction.setInfoUUId(c.getInfoId());
        sendMessage(cfInfoSupplyAction,UserOperationTypeEnum.DYNAMIC_MODIFICATION_REVIEW_REJECTED.getCode());

        //处理新工单
        UgcHandleOrderParam ugcHandleOrderParam = new UgcHandleOrderParam();
        ugcHandleOrderParam.setWorkOrderId(supplyAction.getWorkOrderId());
        ugcHandleOrderParam.setOrderType(WorkOrderType.xiafaprogress.getType());
        ugcHandleOrderParam.setHandleResult(HandleResultEnum.audit_reject.getType());
        ugcHandleOrderParam.setUserId(adminUserId);
        Response workResponse = ugcWorkOrderClient.handleUgc(ugcHandleOrderParam);
        if (workResponse.notOk()) {
            return NewResponseUtil.makeFail(workResponse.getMsg());
        }
        //添加驳回操作记录
        supplyAction.setServiceSupplyFields(list);
        List<CfInfoSupplyField> supplyFields = cfSupplyActionBiz.getByActionId(supplyActionId);
        supplyAction.setUserSupplyFields(supplyFields.stream().filter(r->r.getOperationType()==CfInfoSupplyField.userType).collect(Collectors.toList()));
        setUserAndOrg(supplyAction.getSupplyUserId(),supplyAction);
        supplyAction.setUpdateTime(new Date());
        commonOperationRecordClient
                .create()
                .buildBasicPlatform(supplyActionId, adminUserId, OperationActionTypeEnum.SUPPLY_PROGRESS_REJECT)
                .buildExtValue(OPERATION_RECORD_REJECT, JSON.toJSONString(supplyAction))
                .save();
        //案例备注
        remarkOldService.add(caseId, adminUserId, String.format("下发动态审核[工单id:%d],下发驳回,动态内容:%s", supplyAction.getWorkOrderId(), supplyAction.getComment()));

        return NewResponseUtil.makeSuccess(true);
    }

    public Set<Integer> getAlreadyCode(int caseId){
        List<CfInfoSupplyAction> list = cfSupplyActionBiz.listByCaseIdAndType(caseId,ActionType.progress_new.getCode());

        return list.stream().filter(r->r.getHandleStatus() != SupplyHandleStatus.cancel.getCode()).map(CfInfoSupplyAction::getSupplyReason).map(Integer::valueOf)
                .filter(r->!r.equals(CfProgressReasonEnum.r_21.getCode())).collect(Collectors.toSet());
    }

    public Set<Long> getNoRepeat(int caseId){
        List<CfInfoSupplyAction> list = cfSupplyActionBiz.listByCaseIdAndType(caseId,ActionType.progress_new.getCode());
        List<Long> tempIds = list.stream().filter(r->r.getHandleStatus() != SupplyHandleStatus.cancel.getCode()).map(CfInfoSupplyAction::getSupplyReason).map(Long::valueOf).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tempIds)){
            return SetUtils.EMPTY_SET;
        }

        List<CfItemTemplate> l = cfTemplateFieldService.getTemplatesByIds(tempIds);

        return l.stream().filter(r-> r.getRepeatSend() == 1).map(CfItemTemplate::getId).collect(Collectors.toSet());
    }

    public List<CfInfoSupplyAction> getRecord(long actionId){

        List<OperationRecordDTO> list = commonOperationRecordClient.listByBizIdAndActionTypes(actionId,OperationActionTypeEnum.SUPPLY_PROGRESS_REJECT);

        return list.stream().map(OperationRecordDTO::getExtMap).map(r->JSON.parseObject(r.get(OPERATION_RECORD_REJECT),CfInfoSupplyAction.class)).collect(Collectors.toList());
    }

    public List<CfSupplyProgressVo.ProgressWorkOrderInfo> showProgress(int caseId) {

        List<CfInfoSupplyAction> supplyActions = cfSupplyActionBiz.listByCaseIdAndType(caseId, ActionType.progress_new.getCode());
        Optional<CfInfoSupplyAction> supplyInfo = supplyActions.stream().sorted(Comparator.comparing(CfInfoSupplyAction::getId)).findFirst();
        //查询下发时间点之后的动态工单
        Response<List<WorkOrderVO>> ugcProgressWorkResponse = cfWorkOrderClient.listByCaseIdAndTypeAndResult(caseId, Lists.newArrayList(WorkOrderType.ugcprogress.getType()), Lists.newArrayList());

        List<WorkOrderVO> sortedWorkOrderLists = ugcProgressWorkResponse.getData().stream()
                .filter(item -> item.getCreateTime().after(supplyInfo.get().getCreateTime()))
                .sorted(Comparator.comparing(WorkOrderVO::getCreateTime))
                .collect(Collectors.toList());

        Map<Long, Integer> workId2WordId = Maps.newHashMap();
        Response<List<WorkOrderExt>> listResponse = cfWorkOrderClient.listExtInfos(sortedWorkOrderLists.stream().map(WorkOrderVO::getWorkOrderId).collect(Collectors.toList()), OrderExtName.wordId.getName());
        if (listResponse.ok()) {
            workId2WordId.putAll(listResponse.getData().stream().collect(Collectors.toMap(WorkOrderExt::getWorkOrderId, item -> Integer.valueOf(item.getExtValue()), (before, after) -> before)));
        }

        Map<Integer, CrowdFundingProgress> map = adminCrowdFundingProgressBiz.getMapByIds(Lists.newArrayList(workId2WordId.values()));


        List<CfSupplyProgressVo.ProgressWorkOrderInfo> workOrderInfoList =sortedWorkOrderLists.stream().map(item -> {
            CfSupplyProgressVo.ProgressWorkOrderInfo workInfo = new CfSupplyProgressVo.ProgressWorkOrderInfo();
            BeanUtils.copyProperties(item, workInfo);

            Integer wordId = workId2WordId.get(item.getWorkOrderId());
            CrowdFundingProgress record = map.get(wordId);

            if (record != null) {
                workInfo.setContent(record.getContent());
                workInfo.setImgUrls(record.getImageUrls());
                //敏感词
                Set<String> sensitiveWords = riskDelegate.getHitWords(record.getContent());
                workInfo.setSensitiveWord(Joiner.on(",").join(sensitiveWords));
            }
            return workInfo;
        }).collect(Collectors.toList());

        return workOrderInfoList;

    }


    private CrowdfundingInfo getCrowdfundingInfo(int caseId){
        FeignResponse<CrowdfundingInfo> feignResponse = crowdfundingFeignClient.getCaseInfoById(caseId);

        CrowdfundingInfo c = Optional.ofNullable(feignResponse)
                .filter(FeignResponse::ok)
                .map(FeignResponse::getData)
                .orElse(null);

        return c;
    }

    public void sendMessage(CfInfoSupplyAction cfInfoSupplyAction,int type) {
        if (cfInfoSupplyAction == null) {
            return;
        }
        CfUserEvent cfUserEvent = new CfUserEvent();
        Map<String, String> dataMap = Maps.newHashMap();
        dataMap.put(BizParamEnum.INFO_UUID.getDesc(), cfInfoSupplyAction.getInfoUUId());
        dataMap.put(BizParamEnum.CHANGES_RELEASE_DYNAMICS_ACTION_ID.getDesc(),String.valueOf(cfInfoSupplyAction.getId()));
        cfUserEvent.setType(type);
        cfUserEvent.setCaseId(cfInfoSupplyAction.getCaseId());
        cfUserEvent.setDataMap(dataMap);
        String keys = USER_EVENT_TAG + "_caseId_" + cfInfoSupplyAction.getCaseId() + "_actionId_"+ cfInfoSupplyAction.getId();
        sendEventCenterMq(new Message<>(USER_EVENT_TOPIC, USER_EVENT_TAG, keys , cfUserEvent));
    }

    private void sendEventCenterMq(Message message) {
        if (producer == null) {
            return;
        }
        try {
            MessageResult r = producer.send(message);
            log.info("sendEventCenterMq message={} result={}",message,r);
        } catch (Exception e) {
            log.error("sendEventCenterMq error. message:{}", message, e);
        }
    }

    @Deprecated
    private OpResult<List<CfInfoSupplyField>> checkFeild(int reasonCode,List<CfInfoSupplyField> list){

        List<CfInfoSupplyField> result = Lists.newArrayList();

        CfProgressReasonEnum reasonEnum = CfProgressReasonEnum.getByCode(reasonCode);

        if (reasonEnum == null || CollectionUtils.isEmpty(list)){
            return OpResult.createFailResult(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<CfProgressReasonRelationEnum> relationEnums = CfProgressReasonRelationEnum.getByCfProgressReasonEnum(reasonEnum);
        Map<String,CfInfoSupplyField> fieldMap = list.stream().collect(Collectors.toMap(CfInfoSupplyField::getField,Function.identity()));

        for (CfProgressReasonRelationEnum r : relationEnums){

            CfInfoSupplyField cf = fieldMap.get(r.getFieldDesc().getField());
            if (cf == null || StringUtils.isBlank(cf.getFieldValue())){
                log.error("reasonCode={} r={}",reasonCode,r.getFieldDesc());
                return OpResult.createFailResult(AdminErrorCode.INPUT_FORMAT_ERROR);
            }

            if (r.getFieldDesc().getLimit() > 0 && cf.getFieldValue().length() > r.getFieldDesc().getLimit() ){
                return OpResult.createFailResult(AdminErrorCode.SYSTEM_PARAM_TOO_LONG);
            }

            result.add(cf);
        }
        return OpResult.createSucResult(result);
    }

    private OpResult<List<CfInfoSupplyField>> checkTemplate(long tempId,List<CfInfoSupplyField> list){

        List<CfInfoSupplyField> result = Lists.newArrayList();

        CfItemTemplate template = cfTemplateFieldService.getById(tempId);
        if (template == null || template.getDelete() == 1){
            return OpResult.createFailResult(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<CfTemplateField> tfs = cfTemplateFieldService.getFieldByTempId(tempId);

        if (CollectionUtils.isEmpty(tfs)){
            return OpResult.createFailResult(AdminErrorCode.SYSTEM_PARAM_IS_NULL);
        }

        List<CfItemField> fields = cfTemplateFieldService.getCfItemFieldByIds(tfs.stream().map(CfTemplateField::getFieldId).collect(Collectors.toList()));

        if (CollectionUtils.isEmpty(fields)){
            return OpResult.createFailResult(AdminErrorCode.SYSTEM_PARAM_IS_NULL);
        }

        Map<Long,CfInfoSupplyField> fieldMap = list.stream().collect(Collectors.toMap(CfInfoSupplyField::getFieldId,Function.identity(),(o1,o2)->o2));

        for (CfItemField f : fields){

            CfInfoSupplyField cf = fieldMap.get(f.getId());
            if (cf == null || StringUtils.isBlank(cf.getFieldValue())){
                log.error("cf = null reasonCode={} id={}",tempId,f.getId());
                return OpResult.createFailResult(AdminErrorCode.INPUT_FORMAT_ERROR);
            }

            if (cf.getFieldValue().length() > f.getFieldLength() ){
                return OpResult.createFailResult(AdminErrorCode.SYSTEM_PARAM_TOO_LONG);
            }

            result.add(cf);
        }

        return OpResult.createSucResult(result);
    }

    public Long selectLatelyInitActionId(int caseId) {
        List<CfInfoSupplyAction> supplyActions =  cfSupplyActionBiz.listByCaseIdAndHandleStatus(caseId, null);

        return CollectionUtils.isNotEmpty(supplyActions)
                && supplyActions.get(0).getHandleStatus() == SupplyHandleStatus.init.getCode() ?
                supplyActions.get(0).getId() : null;
    }
}
