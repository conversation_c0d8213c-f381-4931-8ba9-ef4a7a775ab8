package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.client.feign.CaseInfoFeignClient;
import com.shuidihuzhu.cf.client.feign.CfCustomRelationClient;
import com.shuidihuzhu.cf.delegate.SeaAccountDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingUserDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.BaseInfoTemplateConst;
import com.shuidihuzhu.cf.enums.crowdfunding.CfCustomRelationEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfVersion;
import com.shuidihuzhu.cf.enums.crowdfunding.RelationShowTypeEnum;
import com.shuidihuzhu.cf.model.CfCustomRelation;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.vo.crowdfunding.CfCustomRelationCaseVo;
import com.shuidihuzhu.cf.vo.crowdfunding.CfCustomRelationHistoryVo;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Created by dongcf on 2020/7/24
 */
@Service
public class CfCustomRelationService {

    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Resource
    private ICrowdfundingUserDelegate crowdfundingUserDelegate;

    @Resource
    private IRiskDelegate riskDelegate;

    @Autowired
    private CaseInfoFeignClient caseInfoFeignClient;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;

    @Autowired
    private CfCustomRelationClient customRelationClient;

    @Autowired
    private SeaAccountDelegate seaAccountDelegate;

    public Response<CfCustomRelationCaseVo> getCaseInfo(int caseId) {
        CrowdfundingInfo crowdfundingInfo = crowdfundingDelegate.getFundingInfoById(caseId);
        if(crowdfundingInfo == null) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        CfCustomRelationCaseVo caseVo = new CfCustomRelationCaseVo();

        CfFirsApproveMaterial cfFirsApproveMaterial = riskDelegate.getCfFirsApproveMaterialByInfoId(caseId);
        if(cfFirsApproveMaterial == null){
            caseVo.setModifyFlag(false);
            return NewResponseUtil.makeSuccess(caseVo);
        }

        //自定义关系列表
        Map<Integer, String> relationMap = Maps.newHashMap();
        for (CfCustomRelationEnum relationEnum : CfCustomRelationEnum.values()) {
            if(relationEnum == CfCustomRelationEnum.SELF || relationEnum == CfCustomRelationEnum.OTHER) {
                continue;
            }
            relationMap.put(relationEnum.getType(), relationEnum.getDesc());
        }
        caseVo.setCustomRelationMap(relationMap);


        CrowdfundingAuthor crowdfundingAuthor = crowdfundingUserDelegate.getCrowdfundingAuthor(caseId);
        //患者姓名
        String authorName = getAuthorName(cfFirsApproveMaterial, crowdfundingAuthor);
        caseVo.setPatientName(authorName);

        //筹款人昵称
        UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByUserId(crowdfundingInfo.getUserId());
        if(userInfoModel != null) {
            caseVo.setCrowdFounderNickname(userInfoModel.getNickname());
        }

        //处理原始关系
        Response<CfInfoExt> infoExtResponse = caseInfoFeignClient.getCfExt(caseId);
        if(infoExtResponse.ok() && infoExtResponse.getData() != null) {
            CfInfoExt cfInfoExt = infoExtResponse.getData();
            if(CfVersion.definition_3100.getCode() == cfInfoExt.getCfVersion()) {
                BaseInfoTemplateConst.CfBaseInfoRelationshipEnum relation = BaseInfoTemplateConst.CfBaseInfoRelationshipEnum.getByCode(cfFirsApproveMaterial.getUserRelationTypeForC());
                caseVo.setOriginRelationType(relation.getCode());
                caseVo.setOriginRelation(relation.getWord());
            } else {
                UserRelTypeEnum userRelTypeEnum = UserRelTypeEnum.getUserRelTypeEnum(cfFirsApproveMaterial.getUserRelationType());
                caseVo.setOriginRelationType(userRelTypeEnum.getValue());
                caseVo.setOriginRelation(userRelTypeEnum.getMsg());
            }
        }

        Response<CfCustomRelation> customRelationRsp = customRelationClient.getLast(caseId);
        if(customRelationRsp.ok() && customRelationRsp.getData() != null) {
            caseVo.setShowType(customRelationRsp.getData().getShowType());
            return NewResponseUtil.makeSuccess(caseVo);
        }


        if(StringUtils.isNotBlank(caseVo.getPatientName()) &&
                caseVo.getOriginRelationType() > 0) {
            caseVo.setShowType(RelationShowTypeEnum.PATIENT_RELATION.getType());
            return NewResponseUtil.makeSuccess(caseVo);
        }

        caseVo.setShowType(RelationShowTypeEnum.FOUNDER_NICKNAME.getType());
        return NewResponseUtil.makeSuccess(caseVo);
    }


    public Response<Void> add(int caseId, int userId, int showType, int relationType, String remark) {
        CfCustomRelationEnum customRelationEnum = CfCustomRelationEnum.getByType(relationType);
        if(customRelationEnum == CfCustomRelationEnum.OTHER) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        CfCustomRelation relation = new CfCustomRelation();
        relation.setCaseId(caseId);
        relation.setRelationType(relationType);
        relation.setRelationDesc(customRelationEnum.getDesc());
        relation.setOperateUserId(userId);
        relation.setShowType(showType);
        relation.setRemark(remark);

        customRelationClient.save(relation);
        return NewResponseUtil.makeSuccess(null);
    }

    public List<CfCustomRelationHistoryVo> find(int caseId) {
        Response<List<CfCustomRelation>> relationListRsp = customRelationClient.find(caseId);
        if(relationListRsp.notOk() || CollectionUtils.isEmpty(relationListRsp.getData())) {
            return Collections.emptyList();
        }

        Response<CfCustomRelationCaseVo> caseVoResponse = getCaseInfo(caseId);
        if(caseVoResponse.notOk()) {
            return Collections.emptyList();
        }

        CfCustomRelationCaseVo caseVo = caseVoResponse.getData();

        List<CfCustomRelation> relationList = relationListRsp.getData();
        List<CfCustomRelationHistoryVo> historyVoList = Lists.newArrayList();
        for (int i = 0; i < relationList.size(); i++) {
            CfCustomRelationHistoryVo historyVo = new CfCustomRelationHistoryVo();
            CfCustomRelation relation = relationList.get(i);
            historyVo.setRemark(relation.getRemark());
            historyVo.setOperatorName(seaAccountDelegate.getNameWithOrgByUserId(relation.getOperateUserId()));
            historyVo.setOperateTime(relation.getCreateTime().getTime());

            if(i > 0) {
                CfCustomRelation beforeRelation = relationList.get(i - 1);
                historyVo.setBeforeDesc(handleShowDesc(caseVo, beforeRelation));
                historyVo.setAfterDesc(handleShowDesc(caseVo, relation));
                historyVoList.add(historyVo);
                continue;

            }

            historyVo.setAfterDesc(handleShowDesc(caseVo, relation));

            if(StringUtils.isBlank(caseVo.getPatientName())) {
                historyVo.setBeforeDesc(caseVo.getCrowdFounderNickname() + "发起筹款");
                historyVoList.add(historyVo);
                continue;
            }
            //本人
            if(caseVo.getOriginRelationType() == CfCustomRelationEnum.SELF.getType()) {
                historyVo.setBeforeDesc(caseVo.getPatientName() + "发起筹款");
                historyVoList.add(historyVo);
                continue;
            }

            historyVo.setBeforeDesc(caseVo.getPatientName() + "的亲友发起筹款");
            historyVoList.add(historyVo);
        }

        return historyVoList;
    }


    private String handleShowDesc(CfCustomRelationCaseVo caseVo, CfCustomRelation customRelation) {
        //展示昵称
        if(customRelation.getShowType() == RelationShowTypeEnum.FOUNDER_NICKNAME.getType()) {
            return caseVo.getCrowdFounderNickname() + "发起筹款";
        }
        //展示患者关系
        //本人
        if(customRelation.getRelationType() == CfCustomRelationEnum.SELF.getType()) {
            if(StringUtils.isBlank(caseVo.getPatientName())) {
                return caseVo.getCrowdFounderNickname() + "发起筹款";
            }
            return caseVo.getPatientName() + "发起筹款";
        }
        //非本人
        if(StringUtils.isBlank(caseVo.getPatientName())) {
            return caseVo.getCrowdFounderNickname() + "发起筹款";
        }
        return caseVo.getPatientName() + "的" + customRelation.getRelationDesc() + "发起筹款";
    }

    private String getAuthorName(CfFirsApproveMaterial cfFirsApproveMaterial, CrowdfundingAuthor crowdfundingAuthor) {

        if(crowdfundingAuthor != null) {
            return crowdfundingAuthor.getName();
        }

        if(cfFirsApproveMaterial != null) {
            return cfFirsApproveMaterial.getPatientRealName();
        }

        return "";
    }
}
