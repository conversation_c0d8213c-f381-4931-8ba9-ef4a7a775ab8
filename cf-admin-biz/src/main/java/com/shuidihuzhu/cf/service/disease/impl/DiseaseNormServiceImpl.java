package com.shuidihuzhu.cf.service.disease.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.feign.CfRaiseMaterialClient;
import com.shuidihuzhu.cf.client.material.model.RaiseBasicInfoModel;
import com.shuidihuzhu.cf.risk.client.rpc.DiseaseClassifyFeignClientV2;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseClassifyVOV2;
import com.shuidihuzhu.cf.service.disease.DiseaseNormService;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description: 疾病归一服务
 * @Author: panghairui
 * @Date: 2022/8/24 2:56 下午
 */
@Slf4j
@Service
public class DiseaseNormServiceImpl implements DiseaseNormService {

    @Resource
    private CfRaiseMaterialClient cfRaiseMaterialClient;
    @Resource
    private DiseaseClassifyFeignClientV2 diseaseClassifyFeignClientV2;

    @Override
    public List<String> getDiseaseNormByCaseId(Integer caseId) {

        // 取疾病名
        RpcResult<RaiseBasicInfoModel> rpcResult = cfRaiseMaterialClient.selectRaiseBasicInfo(caseId);
        if (rpcResult.isFail() || Objects.isNull(rpcResult.getData()) || StringUtils.isBlank(rpcResult.getData().getDiseaseName())) {
            return new ArrayList<>();
        }
        log.info("DiseaseNormServiceImpl getDiseaseNormByCaseId rpcResult data:{} caseId:{}", JSON.toJSONString(rpcResult.getData()), caseId);

        // 疾病归一
        List<String> diseaseNameList = List.of(StringUtils.split(rpcResult.getData().getDiseaseName(), ",，"));
        if (CollectionUtils.isEmpty(diseaseNameList)) {
            return new ArrayList<>();
        }
        Response<List<DiseaseClassifyVOV2>> listResponse = diseaseClassifyFeignClientV2.diseaseNorm(diseaseNameList);
        List<DiseaseClassifyVOV2> diseaseClassifyVOV2s = Optional.ofNullable(listResponse)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(Lists.newArrayList());
        List<String> norm = CollectionUtils.isNotEmpty(diseaseClassifyVOV2s)
                ? diseaseClassifyVOV2s.stream()
                .map(DiseaseClassifyVOV2::getNorm)
                .flatMap(List::stream)
                .collect(Collectors.toList())
                : new ArrayList<>();

        log.info("DiseaseNormServiceImpl getDiseaseNormByCaseId norm is {}", norm);
        return norm;
    }
}
