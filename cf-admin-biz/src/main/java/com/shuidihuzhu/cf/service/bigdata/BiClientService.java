package com.shuidihuzhu.cf.service.bigdata;

import com.shuidihuzhu.cf.dto.PageSerializable;
import com.shuidihuzhu.cf.model.bi.PotraitModel;

import java.util.List;

/**
 * @package: com.shuidihuzhu.cf.service.bigdata
 * @Author: l<PERSON>jiawei
 * @Date: 2019-03-17  19:04
 */
public interface BiClientService {

    PotraitModel getPotraitById(String potraitId);

    PageSerializable<PotraitModel> getPotrait(String potraitName, String potraitId, Integer pageNum, Integer pageSize);
}
