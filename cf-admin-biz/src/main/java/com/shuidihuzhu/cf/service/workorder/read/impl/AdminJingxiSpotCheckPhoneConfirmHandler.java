package com.shuidihuzhu.cf.service.workorder.read.impl;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.service.workorder.read.AdminJingxiSpotCheckHandler;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
@Component
@Slf4j
public class AdminJingxiSpotCheckPhoneConfirmHandler extends AdminJingxiSpotCheckHandler {
    @Override
    public Map<String, Object> getExtInfo(long workOrderId, int orderType) {
        return Maps.newHashMap();
    }

    @Override
    public WorkOrderType getWorkOrderType() {
        return WorkOrderType.heshi;
    }
}
