package com.shuidihuzhu.cf.service.workorder.imagePublic;

import com.shuidihuzhu.cf.client.adminpure.model.caseinfo.CfCasePublicInfo;
import com.shuidihuzhu.cf.client.adminpure.model.caseinfo.CfCasePublicInfoImageAIRecord;

import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2021/8/20 16:36
 * @Description:
 */
public interface CfCasePublicInfoService {
    int addCasePublicInfo(CfCasePublicInfo cfCasePublicInfo);

    List<CfCasePublicInfo> getListByIdList(List<Long> idList);

    List<CfCasePublicInfo> getListByInfoUuidAndType(String infoUuid);

    int update(CfCasePublicInfo cfCasePublicInfo);

    int deleteByCaseIdAndType(int caseId, int type);

    int addAi(CfCasePublicInfoImageAIRecord cfCasePublicInfoImageAIRecord);

    CfCasePublicInfoImageAIRecord getById(long id);

    int updateCfCasePublicInfoImageAIRecord(CfCasePublicInfoImageAIRecord cfCasePublicInfoImageAIRecord);

}
