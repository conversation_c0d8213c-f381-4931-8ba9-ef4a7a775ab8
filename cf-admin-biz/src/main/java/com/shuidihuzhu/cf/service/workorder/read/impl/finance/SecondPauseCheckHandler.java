package com.shuidihuzhu.cf.service.workorder.read.impl.finance;

import com.shuidihuzhu.cf.service.workorder.read.impl.AbstractFinanceWorkOrderCheckHandler;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import org.springframework.stereotype.Component;

/**
 * 进入收口时间      createTime
 * 收口原因         pauseReason
 */
@Component
public class SecondPauseCheckHandler extends AbstractFinanceWorkOrderCheckHandler {
    @Override
    public WorkOrderType getWorkOrderType() {
        return WorkOrderType.second_pause;
    }
}
