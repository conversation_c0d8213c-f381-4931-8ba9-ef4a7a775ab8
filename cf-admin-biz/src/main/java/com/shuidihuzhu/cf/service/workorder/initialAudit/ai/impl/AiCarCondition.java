package com.shuidihuzhu.cf.service.workorder.initialAudit.ai.impl;

import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditSearchService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.ai.AiCondition;
import com.shuidihuzhu.cf.vo.approve.CreditInfoVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @DATE 2020/12/21
 */
@Service("aiCarCondition")
@Deprecated
public class AiCarCondition implements AiCondition {

    @Autowired
    private InitialAuditSearchService initialAuditSearchService;


    @Override
    public boolean check(int caseId, String inputValue) {

        if (StringUtils.isEmpty(inputValue)){
            return true;
        }
        CreditInfoVO creditInfoVO = initialAuditSearchService.getCreditInfoVO(caseId);

        String total = Optional.ofNullable(creditInfoVO.getCarProperty()).map(CreditInfoVO.CarPropertyInfoVO::getTotalCount).map(String::valueOf).orElse("");

        return !inputValue.equals(total);
    }
}
