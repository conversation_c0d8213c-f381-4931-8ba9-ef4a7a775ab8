package com.shuidihuzhu.cf.service.crowdfunding.report.transform;

import com.shuidihuzhu.cf.service.crowdfunding.report.transform.handler.IReportTransformHandler;
import com.shuidihuzhu.cf.service.crowdfunding.report.transform.handler.ReportTransformLostHandlerImpl;
import com.shuidihuzhu.cf.service.crowdfunding.report.transform.handler.ReportTransformUpgradeHandlerImpl;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class ReportTransformService {

    @Autowired
    private ReportTransformUpgradeHandlerImpl reportTransformUpgradeHandler;

    @Autowired
    private ReportTransformLostHandlerImpl reportTransformLostHandler;

    public IReportTransformHandler getHandler(int orderType) {
        if (orderType == WorkOrderType.up_grade_second.getType()) {
            return reportTransformUpgradeHandler;
        }
        if (orderType == WorkOrderType.lost_report.getType()) {
            return reportTransformLostHandler;
        }
        return null;
    }
}
