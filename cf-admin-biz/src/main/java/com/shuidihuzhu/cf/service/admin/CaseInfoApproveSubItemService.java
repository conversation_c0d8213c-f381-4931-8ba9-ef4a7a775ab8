package com.shuidihuzhu.cf.service.admin;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.dao.approve.ApproveSubItemDAO;
import com.shuidihuzhu.cf.domain.approve.ApproveSubItemDO;
import com.shuidihuzhu.cf.enums.approve.ApproveSubItemEnum;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.vo.crowdfunding.AdminApproveVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-03-28  17:12
 * <p>
 * 材料审核子项审核类
 */
@Service
@Slf4j
public class CaseInfoApproveSubItemService {

    @Resource
    private ApproveSubItemDAO dao;

    /**
     * 返回子项审核详情
     *
     * @param caseId
     * @return Map: {key: {@link ApproveSubItemEnum#name()}, value: {@link ApproveSubItemDO#isPassed()}}
     */
    public Map<String, Boolean> getSubApproveItemStatus(int caseId) {
        List<ApproveSubItemDO> list = dao.listByCaseId(caseId);
        if (CollectionUtils.isEmpty(list)) {
            return getDefaultInfo();
        }
        Map<String, Boolean> info = getDefaultInfo();
        for(ApproveSubItemDO v : list){
            String name = ApproveSubItemEnum.parse(v.getCode()).name();
            info.put(name, v.isPassed());
        }
        return info;
    }

    /**
     * 材料审核 子项 中间件
     * @param caseId
     * @param adminApproveVo
     * @param updateSubItemStatus
     * @return
     */
    public AdminApproveVo process(int caseId, AdminApproveVo adminApproveVo, Map<String, Boolean> updateSubItemStatus) {
        if (MapUtils.isEmpty(updateSubItemStatus)) {
            return adminApproveVo;
        }
        Context context = new Context(caseId, updateSubItemStatus);
        process(context);

        return adminApproveVo;
    }

    private Context process(Context ctx){
        log.info("process start {}", ctx);

        int caseId = ctx.getCaseId();
        Map<String, Boolean> updateSubItemStatus = ctx.getUpdateSubItemStatus();

        if (MapUtils.isEmpty(updateSubItemStatus)) {
            return ctx;
        }

        // 更新子项状态
        update(caseId, updateSubItemStatus);

        log.info("process end {}", ctx);
        return ctx;
    }

    /**
     * @param caseId
     * @param subItemStatus
     * @return
     */
    private void update(int caseId, Map<String, Boolean> subItemStatus) {
        for (String key : subItemStatus.keySet()) {
            Boolean v = subItemStatus.get(key);
            if (v == null) {
                continue;
            }
            updateSingle(caseId, key, v);
        }
    }

    private OpResult<Object> updateSingle(int caseId, String k, Boolean v) {
        return updateSingle(caseId, ApproveSubItemEnum.valueOf(k), v);
    }

    private OpResult<Object> updateSingle(int caseId, ApproveSubItemEnum codeEnum, Boolean passed) {
        log.info("updateSingle  caseId: {}, codeEnum: {}, passed: {}", caseId, codeEnum, passed);
        if (passed == null) {
            log.error("passed == null caseId: {}, codeEnum: {}", caseId, codeEnum);
            return null;
        }
        int code = codeEnum.getValue();
        ApproveSubItemDO a = dao.getByCaseIdAndCode(caseId, code);
        if (a == null) {
            a = new ApproveSubItemDO();
            a.setCaseId(caseId);
            a.setCode(codeEnum.getValue());
            a.setPassed(passed);
            int res = dao.insert(a);
            boolean success = res >= 1;
            if (!success) {
                log.error("insert fail caseId: {}, codeEnum: {}, passed: {}, res: {}", caseId, codeEnum, passed, res);
            }
            return OpResult.createStorageResult(success, res);
        }
        int res = dao.updatePassed(caseId, code, passed);
        boolean success = res >= 1;
        if (!success) {
            log.warn("update fail caseId: {}, codeEnum: {}, passed: {}, res: {}", caseId, codeEnum, passed, res);
        }
        return OpResult.createStorageResult(success, res);
    }

    /**
     * 获取默认值
     * @return
     */
    private Map<String, Boolean> getDefaultInfo() {
        HashMap<String, Boolean> info = Maps.newHashMap();
        for(ApproveSubItemEnum e : ApproveSubItemEnum.values()){
            info.put(e.name(), false);
        }
        return info;
    }

    @AllArgsConstructor
    @Data
    private static class Context {

        private int caseId;

        /**
         * 要修改子项
         * Map: {key: {@link ApproveSubItemEnum#name()}, value: {@link ApproveSubItemDO#isPassed()}}
         */
        private Map<String, Boolean> updateSubItemStatus;

    }
}
