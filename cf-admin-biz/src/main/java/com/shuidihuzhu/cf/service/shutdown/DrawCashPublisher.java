package com.shuidihuzhu.cf.service.shutdown;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.event.DrawCashEvent;

import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @time 2018/11/27 下午4:18
 * @desc
 */
@Service
@Slf4j
public class DrawCashPublisher implements ApplicationEventPublisherAware {

    private ApplicationEventPublisher applicationEventPublisher;

    @Override
    public void setApplicationEventPublisher(ApplicationEventPublisher applicationEventPublisher) {
        this.applicationEventPublisher = applicationEventPublisher;
    }

    public void publishEvent(DrawCashEvent drawCashEvent){
        applicationEventPublisher.publishEvent(drawCashEvent);
        log.info("DrawCashPublisher.publish DrawCashEvent:{}", JSON.toJSONString(drawCashEvent));
    }
}
