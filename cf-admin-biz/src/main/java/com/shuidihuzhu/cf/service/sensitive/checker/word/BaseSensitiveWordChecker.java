package com.shuidihuzhu.cf.service.sensitive.checker.word;

import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordCheckContext;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordResult;
import com.shuidihuzhu.cf.service.sensitive.adapter.ISensitiveAdapter;
import com.shuidihuzhu.cf.service.sensitive.processor.SensitiveProcessService;
import com.shuidihuzhu.cf.service.sensitive.checker.ISensitiveChecker;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2019-04-11  15:54
 *
 * 敏感词禁止词基类
 */
@Deprecated
public abstract class BaseSensitiveWordChecker implements ISensitiveChecker {

    @Resource
    private SensitiveProcessService sensitiveProcessService;

    @Resource
    private IRiskDelegate riskDelegate;

    @Override
    public <T> OpResult<RiskWordResult> isHit(T data, ISensitiveAdapter<T> adapter) {
        String content = adapter.getContent(data);
        RiskWordCheckContext ctx = RiskWordCheckContext.builder()
                .content(content)
                .categorys(getCheckWordCategoryArray())
                .isCheckAll(true)
                .build();
        return riskDelegate.isHit(ctx);
    }

    /**
     * 获取风控词分类id数组
     * @return
     */
    protected abstract long[] getCheckWordCategoryArray();

}
