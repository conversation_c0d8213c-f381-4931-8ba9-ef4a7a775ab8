package com.shuidihuzhu.cf.service.apollo;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020-12-04
 */

@Service
@RefreshScope
@Data
@Slf4j
public class ApolloService {
    @Value("${apollo.growthtool.caseinit.msg.switch:0}")
    private int caseInitMsgSwitch;

    @Value("${apollo.cos.switch:false}")
    private boolean cosSwitch;


    public int getCaseInitMsgSwitch() {
        return caseInitMsgSwitch;
    }

    public boolean getCosSwitch() {
        return cosSwitch;
    }
}
