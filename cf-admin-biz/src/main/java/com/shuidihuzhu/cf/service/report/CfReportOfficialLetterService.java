package com.shuidihuzhu.cf.service.report;

import com.shuidihuzhu.cf.admin.delegate.SeaUserAccountDelegate;
import com.shuidihuzhu.cf.biz.crowdfunding.report.CfReportOfficialLetterBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfReportOfficialLetterLogDao;
import com.shuidihuzhu.cf.delegate.OrganizationDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.CfReportOfficialLetterStatusEnum;
import com.shuidihuzhu.cf.model.report.CfReportOfficialLetter;
import com.shuidihuzhu.cf.model.report.CfReportOfficialLetterLog;
import com.shuidihuzhu.cf.vo.report.CfReportOfficialLetterLogVo;
import com.shuidihuzhu.common.web.util.ContextUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: lixia<PERSON><PERSON>
 * @create: 2020-05-21 14:54
 **/
@Service
public class CfReportOfficialLetterService {

    @Autowired
    private CfReportOfficialLetterBiz cfReportOfficialLetterBiz;
    @Autowired
    private CfReportOfficialLetterLogDao cfReportOfficialLetterLogDao;
    @Autowired
    private OrganizationDelegate organizationDelegate;
    @Autowired
    private SeaUserAccountDelegate seaUserAccountDelegate;

    public void addOfficialLetter(CfReportOfficialLetter cfReportOfficialLetter, int userId) {
        int count = cfReportOfficialLetterBiz.insertOne(cfReportOfficialLetter);
        if (count > 0) {
            CfReportOfficialLetterLog cfReportOfficialLetterLog = new CfReportOfficialLetterLog(
                    cfReportOfficialLetter.getId(), userId, "创建");
            cfReportOfficialLetterLogDao.insertOne(cfReportOfficialLetterLog);
        }
    }

    public List<CfReportOfficialLetter> officialLetterList(int caseId) {
        return cfReportOfficialLetterBiz.getByCaseId(caseId);
    }

    public void editOfficialLetter(CfReportOfficialLetter cfReportOfficialLetter) {
        CfReportOfficialLetter officialLetter = cfReportOfficialLetterBiz.getById(cfReportOfficialLetter.getId());
        String comment = this.getComment(officialLetter, cfReportOfficialLetter);
        int count = cfReportOfficialLetterBiz.update(cfReportOfficialLetter);
        if (count > 0) {
            int userId = ContextUtil.getAdminUserId();
            CfReportOfficialLetterLog cfReportOfficialLetterLog = new CfReportOfficialLetterLog(
                    cfReportOfficialLetter.getId(), userId, comment);
            cfReportOfficialLetterLogDao.insertOne(cfReportOfficialLetterLog);
        }
    }

    private String getComment(CfReportOfficialLetter old, CfReportOfficialLetter current) {
        StringBuilder stringBuilder = new StringBuilder();
        if (!old.getLetterType().equals(current.getLetterType())) {
            stringBuilder.append("编辑公函类型为:").append(current.getLetterType()).append(";");
        }
        if (!old.getNum().equals(current.getNum())) {
            stringBuilder.append("编辑快递编号为:").append(current.getNum()).append(";");
        }
        if (!old.getImages().equals(current.getImages())) {
            stringBuilder.append("编辑图片:").append(";");
        }
        if (old.getLetterStatus() != (current.getLetterStatus())) {
            CfReportOfficialLetterStatusEnum cfReportOfficialLetterStatusEnum = CfReportOfficialLetterStatusEnum.getByCode(current.getLetterStatus());
            stringBuilder.append("编辑公函状态为:").append(cfReportOfficialLetterStatusEnum.getDesc()).append(";");
        }
        if (!old.getComment().equals(current.getComment())) {
            stringBuilder.append("编辑备注为:").append(current.getComment()).append(";");
        }
        return stringBuilder.toString();
    }

    public CfReportOfficialLetter officialLetterInfo(long id) {
        return cfReportOfficialLetterBiz.getById(id);
    }

    public List<CfReportOfficialLetterLogVo> getRecord(long letterId) {
        List<CfReportOfficialLetterLog> cfReportOfficialLetterLogs = cfReportOfficialLetterLogDao.getByLetterId(letterId);
        List<Integer> userIds = cfReportOfficialLetterLogs.stream().map(CfReportOfficialLetterLog::getUserId).map(Long::intValue).collect(Collectors.toList());
        Map<Integer, String> operators = seaUserAccountDelegate.getByOperators(userIds);

        return cfReportOfficialLetterLogs.stream().map(cfReportOfficialLetterLog -> {
            CfReportOfficialLetterLogVo cfReportOfficialLetterLogVo = new CfReportOfficialLetterLogVo();
            BeanUtils.copyProperties(cfReportOfficialLetterLog, cfReportOfficialLetterLogVo);
            String simpleOrganization = organizationDelegate.getSimpleOrganization((int) cfReportOfficialLetterLog.getUserId());
            String name = operators.get((int) cfReportOfficialLetterLog.getUserId());
            cfReportOfficialLetterLogVo.setMis(simpleOrganization);
            cfReportOfficialLetterLogVo.setName(name);
            return cfReportOfficialLetterLogVo;
        }).collect(Collectors.toList());
    }
}
