package com.shuidihuzhu.cf.service.rejectmanager;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-01-09 19:22
 **/
public interface ISeaReasonService<R> {
    /**
     * 获取所有的驳回信息
     */
    List<R> listAllRejectInfo();

    /**
     * 根据前端传入的驳回id,返回驳回详情
     */
    List<R> listRejectByIds(List<Integer> rejectIds);

    /**
     * 校验前端驳回时传入的驳回项是否合法,true合法
     */
    boolean validateRejectIds(List<Integer> rejectIds);

}
