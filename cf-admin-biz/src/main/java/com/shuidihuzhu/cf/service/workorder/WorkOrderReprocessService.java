package com.shuidihuzhu.cf.service.workorder;

import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class WorkOrderReprocessService {

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    public Response<Long> reprocessWorkOrder(long workOrderId, long operatorId, String comment) {
        return cfWorkOrderClient.reprocessWorkOrder(workOrderId, operatorId, comment);
    }

}
