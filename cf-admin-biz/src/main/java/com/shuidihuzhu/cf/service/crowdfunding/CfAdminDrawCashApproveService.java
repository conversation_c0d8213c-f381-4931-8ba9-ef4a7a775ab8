package com.shuidihuzhu.cf.service.crowdfunding;

import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.mq.IAdminCommonMessageHelperService;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import brave.Tracing;

/**
 * @Author: duchao
 * @Date: 2018/6/11 下午4:28
 */
@Service
public class CfAdminDrawCashApproveService {

	private static final Logger LOGGER = LoggerFactory.getLogger(CfAdminDrawCashApproveService.class);

	@Autowired
	private IRiskDelegate riskDelegate;
	@Resource
	private IAdminCommonMessageHelperService adminCommonMessageHelperService;

	@Autowired
	private Tracing tracing;
	private static ExecutorService executorService;
	@PostConstruct
	public void init() {
		executorService = tracing.currentTraceContext().executorService(Executors.newFixedThreadPool(10));
	}

	/**
	 * 向捐款人发送[筹款审核通过]消息.
	 *
	 * @param crowdfundingInfo 案例
	 */
	public void publishCfAuditPassToDonor(CrowdfundingInfo crowdfundingInfo) {
		Runnable runnable = new Runnable() {
			@Override
			public void run() {
				try {
					if (crowdfundingInfo.getEndTime().getTime() <= System.currentTimeMillis()){
						return;
					}
					if (crowdfundingInfo.getAmount() >= 30 * 1000000) {
						return;
					}
					if (!CollectionUtils.isEmpty(riskDelegate.getCrowdfundingReportListByInfoId(crowdfundingInfo.getId()))) {
						return;
					}
					Message msg = adminCommonMessageHelperService.getCfAuditPassToDonorMsg(crowdfundingInfo);
					adminCommonMessageHelperService.send(msg);
				} catch (Exception e) {
					LOGGER.error("向捐款人发送[筹款审核通过]消息失败", e);
				}
			}
		};
		executorService.execute(runnable);
	}

}
