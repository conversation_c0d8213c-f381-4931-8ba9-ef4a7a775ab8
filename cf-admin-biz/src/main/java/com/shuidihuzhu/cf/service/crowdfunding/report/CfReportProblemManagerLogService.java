package com.shuidihuzhu.cf.service.crowdfunding.report;

import com.google.common.base.Joiner;
import com.shuidihuzhu.cf.biz.crowdfunding.CfReportModuleOperationLogBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfReportProblemBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfReportProblemOperationLogBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminCfReportProblemModuleMandatoryStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.CfReportPageEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportProblem;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportProblemLabel;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfReportProblemParam;
import com.shuidihuzhu.cf.service.crowdfunding.SeaAccountService;
import com.shuidihuzhu.common.web.util.ContextUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Auther: subing
 * @Date: 2020/4/23
 */
@Service
public class CfReportProblemManagerLogService {
    @Autowired
    private SeaAccountService seaAccountService;
    @Autowired
    private CfReportProblemOperationLogBiz cfReportProblemOperationLogBiz;
    @Autowired
    private CfReportModuleOperationLogBiz cfReportModuleOperationLogBiz;
    @Autowired
    private CfReportProblemBiz cfReportProblemBiz;


    /**
     * 创建问题日志
     * @param problemParam
     */
    public void addProblemLog(CfReportProblemParam problemParam){
        if (problemParam == null){
            return;
        }
        CfReportPageEnum showLocation = CfReportPageEnum.parse(problemParam.getShowLocation());
        CfReportPageEnum mustUse = CfReportPageEnum.parse(problemParam.getMustUse());

        String mustAnswer = problemParam.getMustAnswer() == 1 ? "是" : "不是";
        String directShow = problemParam.getDirectShow() == 0 ? "是" : "不是";
        String isNeedVerify = problemParam.getIsNeedVerify() == 1 ? "开" : "关";
        List<String> choiceDescribes = problemParam.getChoiceDescribes();
        String problemType = CfReportProblem.ReportProblemType.findByCode(problemParam.getProblemType()) != null ?
                CfReportProblem.ReportProblemType.findByCode(problemParam.getProblemType()).getDesc() : "";
        String choiceDescribe = CollectionUtils.isEmpty(choiceDescribes) ? "" : Joiner.on(",").join(choiceDescribes);
        String action = "新建问题名称为" + problemParam.getProblem() + ";展示位置为" + showLocation.getValue() + ";问题类型为"
                + problemType + ";备选项为" + choiceDescribe + ";是否必填为" + mustAnswer + ";必填项作用于为" + mustUse.getValue()
                + ";是否常驻" + directShow + "沟通反馈为不知道需要再次沟通核实" + isNeedVerify + ";问题顺序为" + problemParam.getSort();
        addReportProblemOperationLog(action, problemParam.getId());
    }

    /**
     * 更新问题日志
     * @param problemParam
     * @param oldProblemParam
     * @param deleteContent
     * @param addContent
     */
    public void updateProblemLog(CfReportProblemParam problemParam, CfReportProblem oldProblemParam,
                                  List<String> deleteContent, List<String> addContent) {
        if (problemParam == null || oldProblemParam == null){
            return;
        }
        StringBuffer stringBuffer = new StringBuffer();
        if (!StringUtils.equals(problemParam.getProblem(), oldProblemParam.getProblem())) {
            stringBuffer.append("编辑问题名称为").append(problemParam.getProblem()).append(";");
        }
        if (problemParam.getShowLocation() != oldProblemParam.getShowLocation()) {
            CfReportPageEnum showLocation = CfReportPageEnum.parse(problemParam.getShowLocation());
            stringBuffer.append("编辑展示位置为").append(showLocation.getValue()).append(";");
        }
        deleteContent = deleteContent.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteContent)) {
            stringBuffer.append("删除备选项：").append(Joiner.on(",").join(deleteContent)).append(";");
        }
        if (CollectionUtils.isNotEmpty(addContent)) {
            stringBuffer.append("增加备选项：").append(Joiner.on(",").join(addContent)).append(";");
        }
        if (problemParam.getProblemType() != oldProblemParam.getProblemType()) {
            String problemType =  CfReportProblem.ReportProblemType.findByCode(problemParam.getProblemType()) != null ?
                    CfReportProblem.ReportProblemType.findByCode(problemParam.getProblemType()).getDesc() : "";
            stringBuffer.append("编辑问题类型为")
                    .append(problemType).append(";");
        }
        if (problemParam.getMustAnswer() != oldProblemParam.getMustAnswer()) {
            String mustAnswer = problemParam.getMustAnswer() == 1 ? "是" : "不是";
            stringBuffer.append("编辑是否必填为").append(mustAnswer).append(";");
        }
        if (problemParam.getMustUse() != oldProblemParam.getMustUse()) {
            CfReportPageEnum mustUse = CfReportPageEnum.parse(problemParam.getMustUse());
            stringBuffer.append("编辑必填项作用于为").append(mustUse.getValue()).append(";");
        }
        if (problemParam.getDirectShow() != oldProblemParam.getDirectShow()) {
            String directShow = problemParam.getDirectShow() == 0 ? "是" : "不是";
            stringBuffer.append("编辑是否常驻").append(directShow).append(";");
        }
        if (problemParam.getIsNeedVerify() != oldProblemParam.getIsNeedVerify()) {
            String isNeedVerify = problemParam.getIsNeedVerify() == 1 ? "开" : "关";
            stringBuffer.append("编辑沟通反馈为不知道需要再次沟通核实").append(isNeedVerify).append(";");
        }
        if (problemParam.getSort() != oldProblemParam.getSort()) {
            stringBuffer.append("编辑问题顺序为").append(problemParam.getSort()).append(";");
        }
        addReportProblemOperationLog(stringBuffer.toString(), problemParam.getId());
    }

    //保存问题操作日志
    public void addReportProblemOperationLog(String action, int id) {
        if (StringUtils.isBlank(action)){
            return;
        }
        int adminUserId = ContextUtil.getAdminUserId();
        if (adminUserId < 0) {
            return;
        }
        String operator = seaAccountService.getOrganization(adminUserId) + seaAccountService.getName(adminUserId);
        cfReportProblemOperationLogBiz.add(id, action, operator);
    }


    //保存问题分类&模块操作日志
    public void addReportProblemClassifyOperationLog(String action, int id) {
        if (StringUtils.isBlank(action)){
            return;
        }
        int adminUserId = ContextUtil.getAdminUserId();
        if (adminUserId < 0) {
            return;
        }
        String operator = seaAccountService.getOrganization(adminUserId) + seaAccountService.getName(adminUserId);
        cfReportModuleOperationLogBiz.addLog(id, action, operator);
    }


    //新建分类&编辑分类日志
    public void addOrUpdateClassifyLog(int status, String classifyName,
                                       int sort, int id, int result, CfReportProblemLabel oldCfReportProblemLabel){
        String action = "";
        if (status == 0){
            action = "新建分类名称为" + classifyName + ";顺序为" + sort;
            addReportProblemClassifyOperationLog(action, result);
            return;
        }
        if (oldCfReportProblemLabel != null){
            StringBuffer stringBuffer = new StringBuffer();
            if (!StringUtils.equals(classifyName, oldCfReportProblemLabel.getLabelDesc())){
                stringBuffer.append("编辑分类名称为").append(classifyName).append(";");
            }
            if (sort != oldCfReportProblemLabel.getSort()){
                stringBuffer.append("编辑顺序为").append(sort).append(";");
            }
            addReportProblemClassifyOperationLog(stringBuffer.toString(), id);
        }
    }

    //新建模块&编辑模块日志
    public void addOrUpdateModuleLog(int status, String moduleName, int isMandatory, int classifyId,
                                     int sort, int id, int result, CfReportProblemLabel oldCfReportProblemLabel){
        CfReportProblemLabel cfReportProblemLabel =
                Optional.ofNullable(cfReportProblemBiz.getById(classifyId)).orElseGet(CfReportProblemLabel::new);
        if (status == 0){
            String action =
                    "新建模块分类为" + StringUtils.trimToEmpty(cfReportProblemLabel.getLabelDesc()) + ";模块名称为" + moduleName +
                            "顺序为" + sort + ";是否必填:" + AdminCfReportProblemModuleMandatoryStatus.getDescription(isMandatory);
            addReportProblemClassifyOperationLog(action, result);
            return;
        }
        if (oldCfReportProblemLabel != null){
            StringBuffer stringBuffer = new StringBuffer();
            if (classifyId != oldCfReportProblemLabel.getParentId()){
                stringBuffer.append("编辑模块分类为").append(StringUtils.trimToEmpty(cfReportProblemLabel.getLabelDesc())).append(";");
            }
            if (!StringUtils.equals(moduleName, oldCfReportProblemLabel.getLabelDesc())){
                stringBuffer.append("编辑模块名称为").append(moduleName).append(";");
            }
            if (sort != oldCfReportProblemLabel.getSort()){
                stringBuffer.append("编辑顺序为").append(sort).append(";");
            }
            if (isMandatory != oldCfReportProblemLabel.getIsMandatory()){
                stringBuffer.append("编辑是否必填")
                        .append(AdminCfReportProblemModuleMandatoryStatus.getDescription(isMandatory)).append(";");
            }
            addReportProblemClassifyOperationLog(stringBuffer.toString(), id);
        }
    }

    //绑定信息操作日志
    public void bindProblemLog(List<String> cfReportProblemList, int id) {
        if (id < 0){
            return;
        }
        String action = CollectionUtils.isEmpty(cfReportProblemList) ? "" : "绑定下一级问题关联" + Joiner.on("、").join(cfReportProblemList);
        addReportProblemOperationLog(action, id);
    }
}
