package com.shuidihuzhu.cf.service.label.core.service;

import com.shuidihuzhu.cf.client.ugc.wonrecord.WonRecord;
import com.shuidihuzhu.cf.domain.label.core.LabelNodeModel;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface LabelManageService {


    Response<Long> add(long parentId, String name, String labelDesc, int seq, int riskLevel, long operatorId);

    Response<Void> updateStatusByLabelId(long id, int labelStatus, long operatorId, String reason);

    Response<Void> updateLabelDescByLabelId(long id, String labelDesc, long operatorId);

    Response<Void> updateLabelSeqByLabelId(long id, int seq, long operatorId);

    Response<LabelNodeModel> searchLabel(long rootId, String nameKey, Integer riskLevel, Integer labelStatus);

    Response<List<WonRecord>> getOperateRecordById(long id);

    Response<Void> validLabelStatusChange(long id, int labelStatus);
}
