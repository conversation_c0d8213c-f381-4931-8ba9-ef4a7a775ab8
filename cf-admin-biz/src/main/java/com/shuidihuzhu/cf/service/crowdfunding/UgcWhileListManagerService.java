package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.risk.client.CfUgcWhileListClient;
import com.shuidihuzhu.client.cf.risk.model.CfUgcWhileListInfo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * @author: fengxuan
 * @create 2019-11-15 10:33
 **/
@Service
@Slf4j
public class UgcWhileListManagerService {

    @Autowired
    CfUgcWhileListClient cfUgcWhileListClient;


    public Response<Boolean> add(CfUgcWhileListInfo whileListInfo, int adminUserId) {

        Response<Void> addResult = cfUgcWhileListClient.add(whileListInfo, adminUserId);
        if (addResult.ok()) {
            return ResponseUtil.makeSuccess(true);
        } else {
            log.warn("add error!whileListInfo:{}", whileListInfo);
            return ResponseUtil.makeFail(addResult.getMsg());
        }
    }


    public Response<Boolean> edit(long whileListId, String validTime, int adminUserId, String operatorName) {
        Response<Void> modifyValidTime = cfUgcWhileListClient.modifyValidTime(whileListId, validTime, adminUserId, operatorName);
        if (modifyValidTime.ok()) {
            return ResponseUtil.makeSuccess(true);
        } else {
            log.error("");
            return ResponseUtil.makeFail("修改失败");
        }
    }


    public List<CfUgcWhileListInfo> listAllValid() {
        List<CfUgcWhileListInfo> whileListDOList = Lists.newArrayList();
        long id = 0L;
        int limit = 1000;
        boolean hasNext = true;
        while (hasNext) {
            List<CfUgcWhileListInfo> ugcWhileListDOS = Optional.ofNullable(cfUgcWhileListClient.listAllValid(id, limit)).map(Response::getData).orElse(Lists.newArrayList());
            if (limit > ugcWhileListDOS.size()) {
                hasNext = false;
            }
            id = ugcWhileListDOS.stream().mapToLong(CfUgcWhileListInfo::getId).max().orElse(limit);
            whileListDOList.addAll(ugcWhileListDOS);
        }
        return whileListDOList;
    }


}
