package com.shuidihuzhu.cf.service.workorder.read.impl.finance;

import com.shuidihuzhu.cf.service.workorder.read.impl.AbstractFinanceWorkOrderCheckHandler;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 案例余额 surplusAmount 分
 * 材审通过时间 materialPassTime
 * 证实人数 verityUserCount
 * 案例结束时间 endTime
 */
@Component
public class PromptDrawCashCheckHandler extends AbstractFinanceWorkOrderCheckHandler {
    @Override
    public WorkOrderType getWorkOrderType() {
        return WorkOrderType.prompt_draw_cash;
    }

    @Override
    protected Map<String, Object> toMap(Object source) {
        Map<String, Object> res = super.toMap(source);
        // 案例余额
        updateMap(res, "surplusAmount", "caseBal");
        // 证实人数
        updateMap(res, "verityUserCount", "confirmedPeople");
        // 材审通过时间
        updateMap(res, "materialPassTime", "materialTime");

        return res;
    }
}
