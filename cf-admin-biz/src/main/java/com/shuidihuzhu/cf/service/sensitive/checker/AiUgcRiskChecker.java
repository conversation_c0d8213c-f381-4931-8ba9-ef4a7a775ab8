package com.shuidihuzhu.cf.service.sensitive.checker;

import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordResult;
import com.shuidihuzhu.cf.delegate.ai.AiUgcRiskDelegate;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.enums.risk.UgcTypeEnum;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.sensitive.adapter.ISensitiveAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Collections;

/**
 */
@Slf4j
@RefreshScope
@Service
public class AiUgcRiskChecker implements ISensitiveChecker {

    @Autowired
    private AiUgcRiskDelegate aiUgcRiskDelegate;

    @Value("${apollo.ai-ugc-risk.enable:true}")
    private boolean aiEnable;

    @Override
    public AdminWorkOrderConst.Task getTask() {
        return AdminWorkOrderConst.Task.AI_RISK;
    }

    @Override
    public <T> OpResult<RiskWordResult> isHit(T data, ISensitiveAdapter<T> adapter) {
        if (!aiEnable) {
            return OpResult.createSucResult(new RiskWordResult(true, "", Collections.emptyList()));
        }
        boolean needAiCheck = adapter.needAiCheck(data);
        if (!needAiCheck) {
            return OpResult.createSucResult(new RiskWordResult(true, "", Collections.emptyList()));
        }
        final boolean hit = aiUgcRiskDelegate.checkHit(adapter.getBizId(data), adapter.getUgcTypeEnum(), adapter.getContent(data));
        return OpResult.createSucResult(new RiskWordResult(!hit, "", Collections.emptyList()));
    }

}
