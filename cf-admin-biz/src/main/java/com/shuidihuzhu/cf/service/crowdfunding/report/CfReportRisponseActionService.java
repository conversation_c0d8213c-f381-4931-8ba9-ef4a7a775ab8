package com.shuidihuzhu.cf.service.crowdfunding.report;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfReportAddTrustBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.report.*;
import com.shuidihuzhu.cf.dao.crowdfunding.CfReportDisposeActionTemplateDao;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.AddTrustAuditStatusEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminCfReportDisposeShowStatusEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminCfReportDisposeUseStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfSendProve;
import com.shuidihuzhu.cf.model.crowdfunding.CfSendProveTemplate;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfReportAddTrustDisposeVo;
import com.shuidihuzhu.cf.model.report.*;
import com.shuidihuzhu.cf.vo.report.CfReportDisposeActionVo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Auther: subing
 * @Date: 2020/4/17
 */
@Service
@Slf4j
public class CfReportRisponseActionService {
    @Autowired
    private AdminCfReportActionClassifyBiz adminCfReportActionClassifyBiz;
    @Autowired
    private AdminCfReportDisposeActionBiz adminCfReportDisposeActionBiz;
    @Autowired
    private AdminCfReportClassifyRelationshipBiz adminCfReportClassifyRelationshipBiz;
    @Autowired
    private AdminCfReportProveDisposeActionBiz adminCfReportProveDisposeActionBiz;
    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private AdminCfReportAddTrustBiz adminCfReportAddTrustBiz;
    @Autowired
    private CfReportDisposeActionTemplateDao cfReportDisposeActionTemplateDao;
    @Autowired
    private CfSendProveTemplateBiz cfSendProveTemplateBiz;
    @Autowired
    private CfSendProveBiz cfSendProveBiz;


    /**
     * 增加/更新动作分类
     * @param status
     * @param actionClassify
     * @param id
     * @return
     */
    public Response<Integer> addOrUpdateActionClassify(int status, String actionClassify, long id) {
        if (StringUtils.length(actionClassify) > 20){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_TOO_LONG);
        }
        int result = (status == 0) ? adminCfReportActionClassifyBiz.add(actionClassify) :
                adminCfReportActionClassifyBiz.updateActionClassify(actionClassify, id);
        return NewResponseUtil.makeSuccess(result);
    }


    /**
     * 动作分类是否启用
     * @param isUse
     * @param id
     * @return
     */
    public Response<Integer> updateActionClassifyUseStatus(int isUse, long id) {
        if (isUse == AdminCfReportDisposeUseStatusEnum.NO_USE.getType()) {
            List<CfReportDisposeAction> cfReportDisposeActions = adminCfReportDisposeActionBiz.getByActionClassifyId(id);
            if (CollectionUtils.isNotEmpty(cfReportDisposeActions)) {
                cfReportDisposeActions = cfReportDisposeActions.stream().filter(t -> t.getIsUse() == AdminCfReportDisposeUseStatusEnum.IS_USE.getType()).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(cfReportDisposeActions)) {
                    return NewResponseUtil.makeError(AdminErrorCode.CF_REPORT_ACTION_CLASSIFY_ERROR);
                }
            }
        }
        int result = adminCfReportActionClassifyBiz.updateIsUse(isUse, id);
        return NewResponseUtil.makeSuccess(result);
    }


    /**
     * 动作分类列表
     * @return
     */
    public Response<List<CfReportActionClassify>> getActionClassifyList() {
        List<CfReportActionClassify> cfReportActionClassifies = adminCfReportActionClassifyBiz.getAll();
        cfReportActionClassifies =
                cfReportActionClassifies.stream().sorted(Comparator.comparing(CfReportActionClassify::getIsUse).reversed().thenComparing(CfReportActionClassify::getId)).collect(Collectors.toList());
        return NewResponseUtil.makeSuccess(cfReportActionClassifies);
    }


    /**
     * 增加/更新处理动作
     * @param actionClassifyId
     * @param disposeAction
     * @param id
     * @param isHelp
     * @param hasTemplate
     * @param title
     * @param content
     * @param commitmentContent
     * @return
     */
    public void addOrUpdateDisposeAction(long actionClassifyId, String disposeAction, long id,
                                         boolean isHelp, boolean hasTemplate, String title, String content,
                                         String commitmentContent) {
        if (id == 0) {
            CfReportDisposeAction cfReportDisposeAction = new CfReportDisposeAction();
            cfReportDisposeAction.setActionClassifyId(actionClassifyId);
            cfReportDisposeAction.setDisposeAction(disposeAction);
            cfReportDisposeAction.setHelp(isHelp);
            cfReportDisposeAction.setHasTemplate(hasTemplate);
            var result = adminCfReportDisposeActionBiz.add(cfReportDisposeAction);
            if (result > 0) {
                CfReportDisposeActionTemplate cfReportDisposeActionTemplate = new CfReportDisposeActionTemplate(cfReportDisposeAction.getId(),
                        title, content, commitmentContent);
                cfReportDisposeActionTemplateDao.addTemplate(cfReportDisposeActionTemplate);
            }
        } else {
            CfReportDisposeActionTemplate cfReportDisposeActionTemplate = new CfReportDisposeActionTemplate(id,
                    title, content, commitmentContent);
            adminCfReportDisposeActionBiz.updateDisposeAction(disposeAction, actionClassifyId, id, isHelp, hasTemplate);
            var reportDisposeActionTemplate = cfReportDisposeActionTemplateDao.selectByActionId(id);
            if (Objects.nonNull(reportDisposeActionTemplate)){
                cfReportDisposeActionTemplateDao.updateByActionId(cfReportDisposeActionTemplate);
            }else {
                cfReportDisposeActionTemplateDao.addTemplate(cfReportDisposeActionTemplate);
            }
        }
    }


    /**
     * 处理动作是否启用
     * @param isUse
     * @param id
     * @return
     */
    public Response<Integer> updateDisposeActionUseStatus(int isUse, long id) {
        int result = adminCfReportDisposeActionBiz.updateIsUse(isUse, id);
        return NewResponseUtil.makeSuccess(result);
    }


    /**
     * 处理动作列表
     * @param status
     * @return
     */
    public Response<List<CfReportDisposeActionVo>> getDisposeActionList(int status,int caseId) {
        List<CfReportDisposeAction> cfReportDisposeActions = (status == AdminCfReportDisposeShowStatusEnum.ALL.getType()) ?
                adminCfReportDisposeActionBiz.getAll() :
                adminCfReportDisposeActionBiz.getByUse(AdminCfReportDisposeUseStatusEnum.IS_USE.getType());

        List<CfReportActionClassify> cfReportActionClassifies = (status == AdminCfReportDisposeShowStatusEnum.ALL.getType()) ?
                adminCfReportActionClassifyBiz.getAll() : adminCfReportActionClassifyBiz.getByUse(AdminCfReportDisposeUseStatusEnum.IS_USE.getType());

        List<CfReportDisposeActionVo> cfReportDisposeActionVos = Lists.newArrayList();
        for (CfReportActionClassify cfReportActionClassify : cfReportActionClassifies) {
            if (cfReportActionClassify != null) {
                List<CfReportDisposeAction> cfReportDisposeActionList = Lists.newArrayList();
                if (status == AdminCfReportDisposeShowStatusEnum.ALL.getType()) {
                    cfReportDisposeActionList = cfReportDisposeActions.stream()
                            .filter(t -> t != null && t.getActionClassifyId() == cfReportActionClassify.getId())
                            .sorted(Comparator.comparing(CfReportDisposeAction::getIsUse).reversed().thenComparing(CfReportDisposeAction::getId))
                            .collect(Collectors.toList());
                } else if (status == AdminCfReportDisposeShowStatusEnum.IS_USE.getType()) {
                    cfReportDisposeActionList = cfReportDisposeActions.stream()
                            .filter(t -> t != null && t.getActionClassifyId() == cfReportActionClassify.getId() && t.getIsUse() == AdminCfReportDisposeUseStatusEnum.IS_USE.getType()).collect(Collectors.toList());
                }
                //查询模板信息
                List<CfReportDisposeActionExt> cfReportDisposeActionExts = this.getCfReportDisposeActionExts(cfReportDisposeActionList,caseId);
                CfReportDisposeActionVo cfReportDisposeActionVo =
                        CfReportDisposeActionVo.buildVoForExt(cfReportActionClassify.getActionClassify(), cfReportActionClassify.getId(), cfReportDisposeActionExts);
                cfReportDisposeActionVos.add(cfReportDisposeActionVo);
            }
        }
        cfReportDisposeActionVos = cfReportDisposeActionVos.stream().sorted(Comparator.comparing(CfReportDisposeActionVo::getActionClassifyId)).collect(Collectors.toList());
        return NewResponseUtil.makeSuccess(cfReportDisposeActionVos);
    }

    @NotNull
    private List<CfReportDisposeActionExt> getCfReportDisposeActionExts(List<CfReportDisposeAction> cfReportDisposeActionList,int caseId) {
        if (CollectionUtils.isEmpty(cfReportDisposeActionList)) {
            return Collections.emptyList();
        }
        //查询案例已选择的处理动作
        List<Long> chooseActionIds = this.getChooseActionIds(caseId);

        //查询处理动作对应的模板
        List<Long> actionIds = cfReportDisposeActionList.stream().map(CfReportDisposeAction::getId).collect(Collectors.toList());
        List<CfReportDisposeActionTemplate> cfReportDisposeActionTemplates = cfReportDisposeActionTemplateDao.selectByActionIds(actionIds);
        Map<Long, CfReportDisposeActionTemplate> disposeActionTemplateMap = cfReportDisposeActionTemplates.stream()
                .collect(Collectors.toMap(CfReportDisposeActionTemplate::getActionId, Function.identity(), (k1, k2) -> k2));
        //查询案例审核通过的模板
        List<CfSendProveTemplate> cfSendProveTemplates = cfSendProveTemplateBiz.findByCaseIdAndAuditStatus(caseId, List.of(AddTrustAuditStatusEnum.PASSED.getCode()));
        Map<Long, CfSendProveTemplate> cfSendProveTemplateMap = cfSendProveTemplates.stream()
                .collect(Collectors.toMap(CfSendProveTemplate::getActionId, Function.identity(), (k1, k2) -> k2));
        CfSendProve cfSendProve = cfSendProveBiz.getLastOneByCaseId(caseId);

        return cfReportDisposeActionList.stream().map(cfReportDisposeAction -> {
            CfReportDisposeActionExt cfReportDisposeActionExt = new CfReportDisposeActionExt();
            BeanUtils.copyProperties(cfReportDisposeAction, cfReportDisposeActionExt);
            CfReportDisposeActionTemplate cfReportDisposeActionTemplate = disposeActionTemplateMap.get(cfReportDisposeActionExt.getId());
            if (Objects.nonNull(cfReportDisposeActionTemplate)) {
                cfReportDisposeActionExt.setTitle(cfReportDisposeActionTemplate.getTitle());
                cfReportDisposeActionExt.setContent(cfReportDisposeActionTemplate.getContent());
                cfReportDisposeActionExt.setCommitmentContent(cfReportDisposeActionTemplate.getCommitmentContent());
            }
            /**
             * 判断是否需要禁用
             * 1.已选择并且审核通过的带模板的处理动作禁用
             * 2.已选择的并且不支持待录入、没有模板的处理动作,根据图片审核状态禁用（默认不支持待录入并且没有模板的动作是上传图片）
             */
            var cfSendProveTemplate = cfSendProveTemplateMap.get(cfReportDisposeActionExt.getId());
            if (Objects.nonNull(cfSendProveTemplate)) {
                cfReportDisposeActionExt.setChecked(true);
            }
            if (chooseActionIds.contains(cfReportDisposeAction.getId())) {
                if (Objects.nonNull(cfSendProve) && cfSendProve.getPictureAuditStatus() == AddTrustAuditStatusEnum.PASSED.getCode()) {
                    cfReportDisposeActionExt.setChecked(true);
                }
            }
            return cfReportDisposeActionExt;
        }).collect(Collectors.toList());
    }

    private List<Long> getChooseActionIds(int caseId) {
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        List<Long> chooseActionId = Lists.newArrayList();
        if (Objects.nonNull(crowdfundingInfo)) {
            CfReportProveDisposeAction cfReportProveDisposeAction = adminCfReportProveDisposeActionBiz.findByInfoUuid(crowdfundingInfo.getInfoId());
            List<CfReportAddTrustDisposeVo> cfReportAddTrustDisposeVos = Lists.newArrayList();
            if (Objects.nonNull(cfReportProveDisposeAction)) {
                try {
                    cfReportAddTrustDisposeVos = JSONObject.parseObject(cfReportProveDisposeAction.getDisposeAction(), new TypeReference<>() {
                    });
                } catch (Exception e) {
                    log.error("cfReportAddTrustDisposeVos parse error", e);
                }
            }
            if (CollectionUtils.isEmpty(cfReportAddTrustDisposeVos)) {
                return chooseActionId;
            }
            for (CfReportAddTrustDisposeVo cfReportAddTrustDisposeVo : cfReportAddTrustDisposeVos) {
                List<CfReportAddTrustDisposeVo.CfReportDisposeActionInfo> disposeActionInfos = cfReportAddTrustDisposeVo.getDisposeActionInfos();
                if (CollectionUtils.isEmpty(disposeActionInfos)) {
                    return chooseActionId;
                }
                for (CfReportAddTrustDisposeVo.CfReportDisposeActionInfo disposeActionInfo : disposeActionInfos) {
                    chooseActionId.add(disposeActionInfo.getId());
                }
            }
        }
        return chooseActionId;
    }


    /**
     * 增加/更新标签关联举报
     * @param param
     * @param labelId
     * @return
     */
    public Response<Integer> addOrUpdateLabel(String param, int labelId) {
        List<CfReportClassifyRelationship> relationships = JSONObject.parseObject(param, new TypeReference<List<CfReportClassifyRelationship>>() {
        });
        relationships.forEach(t -> {
            t.setLabelId(labelId);
        });
        List<CfReportClassifyRelationship> cfReportClassifyRelationships = adminCfReportClassifyRelationshipBiz.getByLabelId(labelId);
        int result = CollectionUtils.isEmpty(cfReportClassifyRelationships) ? adminCfReportClassifyRelationshipBiz.add(relationships) : updateLabel(relationships, labelId);
        return NewResponseUtil.makeSuccess(result);
    }

    private int updateLabel(List<CfReportClassifyRelationship> relationships, int labelId) {
        adminCfReportClassifyRelationshipBiz.deleteRelationshipByLabelId(labelId);
        return adminCfReportClassifyRelationshipBiz.add(relationships);
    }


    public Response<List<CfReportClassifyRelationship>> getSelectedInfo(int labelId) {
        List<CfReportClassifyRelationship> cfReportClassifyRelationships = adminCfReportClassifyRelationshipBiz.getByLabelId(labelId);
        return NewResponseUtil.makeSuccess(cfReportClassifyRelationships);
    }


    /**
     * 案例对应选中的全部动作分类与处理动作
     * @param caseId
     * @param type
     * @return
     */
    public Response<List<CfReportDisposeActionVo>> getListByCase(int caseId, int type, String labelIds) {
        if (StringUtils.isBlank(labelIds)){
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        Set<Integer> labels = Splitter.on(',').splitToList(labelIds).stream().map(Integer::valueOf).collect(Collectors.toSet());
        List<CfReportClassifyRelationship> cfReportClassifyRelationships =
                adminCfReportClassifyRelationshipBiz.getByTypeAndLabelIds(type, Lists.newArrayList(labels));
        List<CfReportActionClassify> cfReportActionClassifies =
                adminCfReportActionClassifyBiz.getByUse(AdminCfReportDisposeUseStatusEnum.IS_USE.getType());
        List<CfReportDisposeAction> cfReportDisposeActions =
                adminCfReportDisposeActionBiz.getByUse(AdminCfReportDisposeUseStatusEnum.IS_USE.getType());

        //查询案例审核通过的模板
        List<CfSendProveTemplate> cfSendProveTemplates = cfSendProveTemplateBiz.findByCaseIdAndAuditStatus(caseId, List.of(AddTrustAuditStatusEnum.PASSED.getCode()));
        Map<Long, CfSendProveTemplate> cfSendProveTemplateMap = cfSendProveTemplates.stream()
                .collect(Collectors.toMap(CfSendProveTemplate::getActionId, Function.identity(), (k1, k2) -> k2));


        List<CfReportDisposeActionVo> actionVos = Lists.newArrayList();
        cfReportClassifyRelationships.forEach(relationship -> {
            long actionClassifyId = relationship.getActionClassifyId();
            List<CfReportActionClassify> classifies =
                    cfReportActionClassifies.stream().filter(t -> t.getId() == actionClassifyId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(classifies)) {
                CfReportActionClassify cfReportActionClassify = classifies.get(0);
                String disposeActionStringId = Optional.ofNullable(relationship.getDisposeActionId()).orElse("");
                List<String> disposeActionStringIds = Arrays.asList(disposeActionStringId.split(","));
                List<CfReportDisposeActionExt> disposeActionList = Lists.newArrayList();
                disposeActionStringIds.forEach(disposeActionId -> {
                    long id = Long.parseLong(disposeActionId);
                    List<CfReportDisposeAction> disposeActions =
                            cfReportDisposeActions.stream().filter(t -> t.getId() == id).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(disposeActions)) {
                        CfReportDisposeAction cfReportDisposeAction = disposeActions.get(0);
                        CfReportDisposeActionExt cfReportDisposeActionExt = new CfReportDisposeActionExt();
                        BeanUtils.copyProperties(cfReportDisposeAction,cfReportDisposeActionExt);
                        var cfSendProveTemplate = cfSendProveTemplateMap.get(cfReportDisposeActionExt.getId());
                        if (Objects.nonNull(cfSendProveTemplate)){
                            cfReportDisposeActionExt.setChecked(true);
                        }
                        disposeActionList.add(cfReportDisposeActionExt);
                    }
                });
                CfReportDisposeActionVo cfReportDisposeActionVo = CfReportDisposeActionVo.buildVoForExt(cfReportActionClassify.getActionClassify(), cfReportActionClassify.getId(), disposeActionList);
                mergeInfo(actionVos, cfReportDisposeActionVo);
                filterList(actionVos);
            }
        });
        return NewResponseUtil.makeSuccess(actionVos);
    }


    /**
     * 过滤掉取消关联信息
     * @param actionVos
     */
    private void filterList(List<CfReportDisposeActionVo> actionVos){
        actionVos.forEach(actionVo -> {
            List<CfReportDisposeAction> disposeActions =  actionVo.getCfReportDisposeActions();
            if (CollectionUtils.isNotEmpty(disposeActions)){
                disposeActions = disposeActions.stream().filter(t -> t.getActionClassifyId() == actionVo.getActionClassifyId()).collect(Collectors.toList());
                actionVo.setCfReportDisposeActions(disposeActions);
            }
        });
    }

    /**
     * 重复的list合并
     *
     * @param actionVos
     * @param cfReportDisposeActionVo
     */
    private void mergeInfo(List<CfReportDisposeActionVo> actionVos,
                           CfReportDisposeActionVo cfReportDisposeActionVo) {
        if (CollectionUtils.isEmpty(actionVos)) {
            actionVos.add(cfReportDisposeActionVo);
            return;
        }
        List<CfReportDisposeActionVo> cfReportDisposeActionVos =
                actionVos.stream().filter(t -> t.getActionClassifyId() == cfReportDisposeActionVo.getActionClassifyId()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(cfReportDisposeActionVos)) {
            actionVos.add(cfReportDisposeActionVo);
            return;
        }
        for (CfReportDisposeActionVo actionVo : actionVos) {
            if (actionVo.getActionClassifyId() == cfReportDisposeActionVo.getActionClassifyId()) {
                List<CfReportDisposeAction> cfReportDisposeActions = CollectionUtils.isEmpty(actionVo.getCfReportDisposeActions())
                        ? Lists.newArrayList() : actionVo.getCfReportDisposeActions();
                List<CfReportDisposeAction> disposeActions = CollectionUtils.isEmpty(actionVo.getCfReportDisposeActions()) ?
                        Lists.newArrayList() : cfReportDisposeActionVo.getCfReportDisposeActions();
                List<CfReportDisposeAction> newDisposeActions = Lists.newArrayList();
                newDisposeActions.addAll(disposeActions);
                for (CfReportDisposeAction disposeAction : newDisposeActions) {
                    List<CfReportDisposeAction> actions = cfReportDisposeActions.stream().filter(t -> disposeAction != null &&
                            t.getId() == disposeAction.getId()).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(actions)) {
                        cfReportDisposeActions.add(disposeAction);
                    }
                }
            }
        }
    }


    /**
     * 更新案例处理动作
     *
     * @param caseId
     * @param param
     * @param type
     * @return
     */
    public Response<Integer> updateCaseInfo(int caseId, String param, int type, long trustId) {
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }
        adminCfReportProveDisposeActionBiz.add(new CfReportProveDisposeAction(param, crowdfundingInfo.getInfoId(), type, trustId));
        return NewResponseUtil.makeSuccess(1);
    }


}
