package com.shuidihuzhu.cf.service.crowdfunding.report.transform.handler;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.cf.service.crowdfunding.report.transform.ReportTransformParam;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderTypeFeignClient;
import com.shuidihuzhu.client.cf.workorder.model.ReportWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderTypeRecord;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ReportTransformLostHandlerImpl extends ReportTransformBaseHandlerImpl implements IReportTransformHandler {

    @Autowired
    private ApproveRemarkOldService approveRemarkOldService;

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @Autowired
    private CfWorkOrderTypeFeignClient cfWorkOrderTypeFeignClient;

    @Override
    public ReportWorkOrder createReport(int caseId, WorkOrderExt workOrderExt, int orderTypeInt, String comment, long workOrderId) {
        Response<WorkOrderVO> response = cfWorkOrderClient.getWorkOrderById(workOrderId);
        long operatorId = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .map(WorkOrderVO::getOperatorId)
                .orElse(0L);

        ReportWorkOrder workOrder = new ReportWorkOrder();
        workOrder.setCaseId(caseId);
        workOrder.setReportId(Integer.parseInt(workOrderExt.getExtValue()));
        workOrder.setOrderType(orderTypeInt);
        workOrder.setHandleResult(HandleResultEnum.doing.getType());
        workOrder.setOperatorId(operatorId);
        workOrder.setDealOperatorId(0L);
        workOrder.setComment(comment);

        log.info("生成举报工单 workOrder:{}", JSON.toJSONString(workOrder));
        return workOrder;
    }

    @Override
    protected void saveRemark(ReportTransformParam p) {
        Response<WorkOrderVO> workOrderVOResponse = cfWorkOrderClient.getWorkOrderById(p.getWorkOrderId());
        String orderTypeMsg = "";
        if (workOrderVOResponse.ok() && workOrderVOResponse.getData() != null) {
            WorkOrderVO workOrderVO = workOrderVOResponse.getData();
            Response<WorkOrderTypeRecord> resp = cfWorkOrderTypeFeignClient.getByOrderTypeCode(workOrderVO.getOrderType());
            if(resp.ok() && resp.getData() != null) {
                orderTypeMsg = resp.getData().getMsg();
            } else {
                log.error("cfWorkOrderTypeFeignClient获取工单类型失败,orderType:{}", workOrderVO.getOrderType());
            }
        }
        String operation = "从【" + orderTypeMsg + "】" + "转入失联工单:" + p.getReason() + p.getComment();
        approveRemarkOldService.add(p.getCaseId(), p.getOperatorId(), "转入失联", operation);
    }

    @Override
    protected OrderExtName getOperateCommentExt() {
        return OrderExtName.endDealLostTime;
    }

    @Override
    protected String getComment() {
        return "转入失联工单";
    }

    @Override
    protected HandleResultEnum getHandleResult() {
        return HandleResultEnum.end_deal_lost;
    }

    @Override
    protected Response<Void> makeHasNoFinishError() {
        return NewResponseUtil.makeError(AdminErrorCode.REPORT_LOST_EXIST_NOT_FINISH);
    }

    @Override
    protected Response<Void> makePermissionError() {
        return NewResponseUtil.makeError(AdminErrorCode.REPORT_WORK_ORDER_NOT_LOST);
    }

    @Override
    protected WorkOrderType getOrderType() {
        return WorkOrderType.lost_report;
    }
}
