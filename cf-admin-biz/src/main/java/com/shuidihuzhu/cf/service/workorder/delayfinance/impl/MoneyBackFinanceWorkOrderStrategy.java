package com.shuidihuzhu.cf.service.workorder.delayfinance.impl;

import com.shuidihuzhu.cf.finance.model.vo.CfMoneyBackQueryVo;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 返还款工单
 *
 * <AUTHOR>
 * @since 2022-12-01 3:36 下午
 **/
@Service
public class MoneyBackFinanceWorkOrderStrategy extends AbstractFinanceWorkOrderStrategy<CfMoneyBackQueryVo> {


    @Override
    public List<CfMoneyBackQueryVo> getBusinessExt(List<Long> financeBusinessIds) {
        return this.financeWorkOrderDelegate.getMoneyBackVo(financeBusinessIds);
    }

    @Override
    public int orderType() {
        return WorkOrderType.money_back.getType();
    }
}
