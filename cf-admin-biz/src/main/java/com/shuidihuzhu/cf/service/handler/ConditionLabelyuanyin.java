package com.shuidihuzhu.cf.service.handler;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.admin.util.AdminListUtil;
import com.shuidihuzhu.cf.biz.crowdfunding.CfMaterialVerityHistoryBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseReasonEntityBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoDataStatusTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfMaterialVerityHistory;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity;
import com.shuidihuzhu.cf.model.event.CailiaoCondition;
import com.shuidihuzhu.cf.model.event.CailiaoConditionEvent;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderLevel;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2020/3/20
 * 命中特殊驳回原因案例：具体驳回原因见评论中附件（注意：只判断上一次的驳回理由有没有命中即可）
 */
@Component
@Slf4j
public class ConditionLabelyuanyin implements ConditionLabel {

//    private final static Set<Integer> rejectSet = Sets.newHashSet(
//            238, 172, 171, 170, 142, 76,
//            47, 237, 154, 167, 231, 99, 100, 147,
//            148, 105, 149, 108, 151, 111, 113, 114
//    );


    @Autowired
    private CfRefuseReasonEntityBiz reasonEntityBiz;

    private static final Set<Integer> MATERIAL_IDS = Sets.newHashSet(
            CrowdfundingInfoDataStatusTypeEnum.BASE_INFO_SUBMIT.getCode(),
            CrowdfundingInfoDataStatusTypeEnum.PATIENT_INFO_SUBMIT.getCode(),
            CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT.getCode(),
            CrowdfundingInfoDataStatusTypeEnum.TREATMENT_INFO_SUBMIT.getCode(),
            CrowdfundingInfoDataStatusTypeEnum.CREDIT_SUPPLEMENT_INFO_SUBMIT.getCode(),
            CrowdfundingInfoDataStatusTypeEnum.ID_VERIFY.getCode());

    @Autowired
    private CfMaterialVerityHistoryBiz cfMaterialVerityHistoryBiz;

    @Override
    @EventListener(classes = CailiaoConditionEvent.class)
    public void onApplicationEvent(CailiaoConditionEvent event) {

        int caseId = event.getCaseId();

       List<CfMaterialVerityHistory> list = MATERIAL_IDS.stream()
               .map(r->cfMaterialVerityHistoryBiz.selectLatestMaterial(caseId,r))
               .filter(Objects::nonNull)
               .collect(Collectors.toList());

       if (CollectionUtils.isEmpty(list)){
           log.info("reject no caseid={}",caseId);
           return;
       }

       //上一次审核时间
       long lastTime = list.stream()
               .max(Comparator.comparing(CfMaterialVerityHistory::getCreateTime))
               .map(CfMaterialVerityHistory::getCreateTime)
               .map(Date::getTime)
               .get();

        log.info("reject no caseid={} lastTime={}",caseId,lastTime);

        //上一次驳回的总类型
        List<String> result = list.stream().filter(r->r.getCreateTime().getTime()==lastTime)
                .filter(r->r.getHandleType()==CfMaterialVerityHistory.REJECT_TYPE)
                .map(CfMaterialVerityHistory::getRefuseIds)
                .collect(Collectors.toList());

        log.info("reject no caseid={} result={}",caseId,result);

        //没有驳回
        if (CollectionUtils.isEmpty(result)){
            return;
        }

        result.stream().forEach(r->{
           Optional<Integer> optional = Splitter.on(",").splitToList(r).stream()
                    .map(Integer::valueOf)
                    .filter(i -> isWeightService(i))
                    .findAny();
           if (optional.isPresent()){
               setResults("驳回原因 id="+optional.get(),event);
               updateLevelIfHigherAndCaiLiao4(event, OrderLevel.D);
               return;
           }
        });
    }


    private boolean isWeightService(int rejectId) {
        Set<Integer> rejectIds = Sets.newHashSet();
        try {
            rejectIds = caseAttributeCache.get("weight_service_ids");
        } catch (Throwable e) {
            log.info("查找重服务库异常 rejectId:{}", rejectId, e);
        }

        return CollectionUtils.isNotEmpty(rejectIds) && rejectIds.contains(rejectId);
    }

    @Override
    public int getConditionCode() {
        return CailiaoCondition.condition_4.getCode();
    }


    private LoadingCache<String, Set<Integer>> caseAttributeCache =
            CacheBuilder.newBuilder().maximumSize(100)
                    // 每10分钟refresh一次，但是是读触发的；如果没有读，则不会操作
                    .refreshAfterWrite(10, TimeUnit.MINUTES).build(new CacheLoader<String, Set<Integer>>() {
                @Override
                public Set<Integer> load(String key) {
                    return getWeightServiceIds();
                }});


    public Set<Integer> getWeightServiceIds(){

        Set<Integer> rejectSet = Sets.newHashSet();

        List<CfRefuseReasonEntity> reasonEntities = AdminListUtil.getList(100, reasonEntityBiz::selectAll);

        for (CfRefuseReasonEntity entity : reasonEntities) {
            if (entity.getIsDelete() == 0 && entity.getWeightServiceLib() == 1) {
                rejectSet.add(entity.getId());
            }
        }

        log.info("重服务库的驳回id:{}", JSON.toJSONString(rejectSet));

        return rejectSet;
    }
}
