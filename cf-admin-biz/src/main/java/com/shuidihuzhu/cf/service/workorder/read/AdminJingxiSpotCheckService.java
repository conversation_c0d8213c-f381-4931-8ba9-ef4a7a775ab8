package com.shuidihuzhu.cf.service.workorder.read;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdFundingProgressBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderFlowBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfSensitiveWordRecordBiz;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminUGCTask;
import com.shuidihuzhu.cf.model.crowdfunding.CfSensitiveWordRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowParam;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView;
import com.shuidihuzhu.cf.service.workorder.read.impl.AbstractFinanceWorkOrderCheckHandler;
import com.shuidihuzhu.client.cf.workorder.helper.Von;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.cf.workorder.read.BasicWorkOrder;
import com.shuidihuzhu.common.web.model.Response;
import org.springframework.beans.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AdminJingxiSpotCheckService {

    @Autowired
    private CfSensitiveWordRecordBiz sensitiveWordRecordBiz;

    @Autowired
    private AdminCrowdFundingProgressBiz adminCrowdFundingProgressBiz;

    @Autowired
    private AdminWorkOrderFlowBiz adminWorkOrderFlowBiz;

    @Autowired
    private List<AdminJingxiSpotCheckHandler> adminJingxiSpotCheckHandlers;

    Map<Integer, AdminJingxiSpotCheckHandler> workOrderCheckHandlerMap;

    @PostConstruct
    public void init() {
        workOrderCheckHandlerMap = adminJingxiSpotCheckHandlers.stream()
                .collect(Collectors.toMap(r -> r.getWorkOrderType().getType(), Function.identity()));
    }


    public OperationResult<Map<String, Object>> getExtInfo(long workOrderId, int orderType) {
        if (orderType != WorkOrderType.ugcpinglun.getType()
                && orderType != WorkOrderType.ugcprogress.getType()) {
            return OperationResult.success();
        }
        final Response<BasicWorkOrder> orderResp = Von.read().getOrderBasicInfoById(workOrderId);
        final BasicWorkOrder basicWorkOrder = orderResp.getData();
        if (orderType == WorkOrderType.ugcpinglun.getType()) {
            return processUgcPinglun(basicWorkOrder);
        }
        if (orderType == WorkOrderType.ugcprogress.getType()) {
            return processUgcProgress(basicWorkOrder);
        }
        return OperationResult.fail("不支持的工单类型");
    }

    public OperationResult<Map<String, Object>> getExtInfoV2(long workOrderId, int orderType) {
        if(workOrderId <= 0 || orderType <= 0){
            return OperationResult.fail("参数错误");
        }
        AdminJingxiSpotCheckHandler adminJingxiSpotCheckHandler = workOrderCheckHandlerMap.get(orderType);
        if (adminJingxiSpotCheckHandler == null) {
            log.warn("鲸息同步数据 handler not found, orderType:{} workOrderId:{}", orderType, workOrderId);
            return OperationResult.success();
        }
        Map<String, Object> result = adminJingxiSpotCheckHandler.getExtInfo(workOrderId, orderType);
        return OperationResult.success(result);
    }

    public OperationResult<PageInfo<com.shuidihuzhu.cf.client.adminpure.model.caseinfo.AdminWorkOrderFlowView>> getTransferInfo(String searchParamJson){
        List<com.shuidihuzhu.cf.client.adminpure.model.caseinfo.AdminWorkOrderFlowView> view = Lists.newArrayList();
        if(StringUtils.isBlank(searchParamJson)){
            return OperationResult.fail("参数错误");
        }
        AdminWorkOrderFlowParam.SearchParam searchParam = JSONObject.parseObject(searchParamJson, AdminWorkOrderFlowParam.SearchParam.class);
        if(searchParam == null){
            return OperationResult.fail("参数错误");
        }
        PageInfo<AdminWorkOrderFlowView> pageInfo = adminWorkOrderFlowBiz.selectAdminWorkOrderByParam(searchParam);
        if(pageInfo == null || pageInfo.getList() == null || pageInfo.getList().size() <= 0){
            return OperationResult.success();
        }
        PageInfo<com.shuidihuzhu.cf.client.adminpure.model.caseinfo.AdminWorkOrderFlowView> result = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, result);

        List<AdminWorkOrderFlowView> list = pageInfo.getList();
        for (AdminWorkOrderFlowView adminWorkOrderFlowView : list) {
            com.shuidihuzhu.cf.client.adminpure.model.caseinfo.AdminWorkOrderFlowView target = new com.shuidihuzhu.cf.client.adminpure.model.caseinfo.AdminWorkOrderFlowView();
            BeanUtils.copyProperties(adminWorkOrderFlowView, target);
            target.setCaseId(adminWorkOrderFlowView.getCaseId());
            target.setCaseInfoId(adminWorkOrderFlowView.getCaseInfoId());
            target.setCaseTitle(adminWorkOrderFlowView.getCaseTitle());
            view.add(target);
        }
        result.setList(view);
        return OperationResult.success(result);
    }

    private OperationResult<Map<String, Object>> processUgcProgress(BasicWorkOrder basicWorkOrder) {
        final List<WorkOrderExt> bizExtList = basicWorkOrder.getBizExtList();
        Map<String, String> extMap = getJingxiExtMap(bizExtList);
        Long wordId = getLong(extMap, "wordId");
        if (wordId == null) {
            return OperationResult.success();
        }
        final CrowdFundingProgress progress = adminCrowdFundingProgressBiz.getActivityProgressById(wordId);
        final HashMap<String, Object> result = Maps.newHashMap();
        result.put("progressContent", progress.getContent());
        result.put("progressImages", progress.getImageUrls());
        return OperationResult.success(result);
    }


    private OperationResult<Map<String, Object>> processUgcPinglun(BasicWorkOrder basicWorkOrder) {
        final List<WorkOrderExt> bizExtList = basicWorkOrder.getBizExtList();
        Map<String, String> extMap = getJingxiExtMap(bizExtList);
        Long wordId = getLong(extMap, "wordId");
        if (wordId == null) {
            return OperationResult.success();
        }
        final Integer contentType = getInt(extMap, "contentType");
        final CfSensitiveWordRecord cfSensitiveWordRecord = sensitiveWordRecordBiz.selectById(wordId);
        final HashMap<String, Object> result = Maps.newHashMap();
        result.put("contentDetail", cfSensitiveWordRecord.getContent());
        result.put("contentType", AdminUGCTask.Content.getByCode(contentType).getWord());
        return OperationResult.success(result);
    }


    public Long getLong(Map<String, String> extMap, String key) {
        final String s = extMap.get(key);
        if (s == null) {
            return null;
        }
        return Long.valueOf(s);
    }

    public Integer getInt(Map<String, String> extMap, String key) {
        final String s = extMap.get(key);
        if (s == null) {
            return null;
        }
        return Integer.valueOf(s);
    }

    public Map<String, String> getJingxiExtMap(List<WorkOrderExt> bizExtList) {
        return bizExtList.stream()
                .collect(Collectors.toMap(WorkOrderExt::getExtName, WorkOrderExt::getExtValue, (a, b) -> b));
    }
}
