package com.shuidihuzhu.cf.service.workorder.delayfinance.impl;

import com.shuidihuzhu.cf.finance.model.vo.PrecipitationModelVo;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 沉淀资金工单
 *
 * <AUTHOR>
 * @since 2022-12-01 3:34 下午
 **/
@Service
public class PrecipitationFinanceWorkOrderStrategy extends AbstractFinanceWorkOrderStrategy<PrecipitationModelVo> {

    @Override
    public List<PrecipitationModelVo> getBusinessExt(List<Long> financeBusinessIds) {
        return this.financeWorkOrderDelegate.getPrecipitationVo(financeBusinessIds);
    }

    @Override
    public int orderType() {
        return WorkOrderType.precipitation.getType();
    }
}
