package com.shuidihuzhu.cf.service.markfollowuptime;

import com.shuidihuzhu.cf.enhancer.model.response.EhResponse;
import com.shuidihuzhu.cf.enums.report.ReportPayMethodEnum;
import com.shuidihuzhu.cf.model.markfollowuptime.CfMarkFollowDo;
import com.shuidihuzhu.cf.model.markfollowuptime.CfMarkFollowRecordVO;
import com.shuidihuzhu.cf.model.markfollowuptime.CfMarkFollowVO;
import com.shuidihuzhu.cf.model.report.schedule.ReportScheduleVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/13  3:30 下午
 */
public interface CfMarkFollowService {
    EhResponse<Void> add(int adminUserId, int orderType,long bizId, Date targetTime);

    EhResponse<Void> update(int adminUserId, int id, Date targetTime);

    EhResponse<Void> remove(int adminUserId, int id);

    EhResponse<ReportScheduleVO> getByCaseId(int caseId);

    EhResponse<Void> done(int adminUserId, int id);

    int updateOperatorIdByBizId(long bizId, long adminUserId);

    EhResponse<CfMarkFollowRecordVO> getListByWorkOrderId(long workOrderId);

    void onDelayHandle(String json);

    EhResponse<CfMarkFollowVO> judgeMarkFollowTime(long workOrderId);


    List<CfMarkFollowDo> getByBizIds(List<Long> bizIds);

    EhResponse<List<CfMarkFollowVO>> getListByOperatorId(int adminUserId);

    EhResponse<Boolean> judge(long workOrderId, int adminUserId);

}
