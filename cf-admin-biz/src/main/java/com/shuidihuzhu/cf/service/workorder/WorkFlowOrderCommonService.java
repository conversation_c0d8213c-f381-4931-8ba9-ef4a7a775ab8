package com.shuidihuzhu.cf.service.workorder;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderFlowBiz;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderFlow;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import static com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst.Status.CREATED;

@Service
@Slf4j
public class WorkFlowOrderCommonService {


    @Autowired
    private Producer producer;

    public void sendRefundFlowOrderHandleSuccMsg(AdminWorkOrder adminWorkOrder, AdminWorkOrderFlow oldFlowOrder) {

        // 发消息
        Message msg =  new Message<>(MQTopicCons.CF,
                MQTagCons.ADMIN_DONOR_REFUND_APPLY_48_HOUR,
                MQTagCons.ADMIN_DONOR_REFUND_APPLY_48_HOUR + "_" + oldFlowOrder.getId(),
                new AdminWorkOrderFlowBiz.WorkOrderNoticeRefundObj(generateWorkFlowId(oldFlowOrder.getCreateTime(), oldFlowOrder.getId()),
                        adminWorkOrder.getOrderStatus()), DelayLevel.S5);
        MessageResult sendResult = producer.send(msg);
        log.info("信息流转工单给退款发消息 msg:{}, result:{}", msg, sendResult);
    }


    public String queryPriorityLevelDesc(int level) {

        switch (level) {
            case 0:
                return "低";
            case 1:
                return "中";
            case 2:
                return "高";
            case 3:
                return "极高";
        }

        return "";
    }

    // "201903191234" ----> 1234
    public int decodeFromFlowIdString (String workFlowId) {
        if (StringUtils.isBlank(workFlowId)) {
            return 0;
        }
        int flowId = -1;
        try {
            flowId = Integer.parseInt(workFlowId.substring(8));
        } catch (Exception e) {
            log.warn("用户输入的信息流转工单workFlowId:{}错误", workFlowId);
        }

        return flowId;
    }


    public List<Integer> generateCurPageIds(List<Integer> ids, int pageNum, int pageSize) {

        int startPos = (pageNum - 1) * pageSize;
        int endPos = startPos + pageSize;
        endPos = endPos > ids.size() ? ids.size() : endPos;

        if (startPos >= ids.size()) {
            return Lists.newArrayList();
        }

        return ids.subList(startPos, endPos);
    }

    public String queryOrderStatusDesc(int code) {
        AdminWorkOrderConst.Status status = AdminWorkOrderConst.Status.getByCode(code);

        if (status == null) {
            log.info("信息传递工单状态不合法:{}", code);
            return "error";
        }

        if (status != CREATED) {
            return status.getWord();
        }

        return "待处理";
    }

    public String generateWorkFlowId(Date date, long id) {
        return DateUtil.getDate2YMDStr(date) + String.format("%06d", id);
    }


}
