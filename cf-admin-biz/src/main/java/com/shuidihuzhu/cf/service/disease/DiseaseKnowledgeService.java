package com.shuidihuzhu.cf.service.disease;


import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.dao.disease.DiseaseKnowledgeDao;
import com.shuidihuzhu.cf.dao.disease.DiseaseKnowledgeRecordDao;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.model.disease.DiseaseKnowledge;
import com.shuidihuzhu.cf.model.disease.DiseaseKnowledgeRecord;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.admin.PageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DiseaseKnowledgeService {

    @Resource
    private DiseaseKnowledgeDao diseaseKnowledgeDao;

    @Resource
    private DiseaseKnowledgeRecordDao diseaseKnowledgeRecordDao;

    @Resource
    private SeaAccountClientV1 seaAccountClientV1;

    public Response<Integer> insert(String diseaseNorm, String diseaseIntro, String cureAndCost, String prognosis, long adminUserId) {

        DiseaseKnowledge diseaseKnowledge = diseaseKnowledgeDao.select(diseaseNorm);
        if (Objects.nonNull(diseaseKnowledge)) {
            return NewResponseUtil.makeFail("该疾病已存在");
        }

        DiseaseKnowledge knowledge = new DiseaseKnowledge();
        knowledge.setDiseaseNorm(diseaseNorm);
        knowledge.setDiseaseIntro(diseaseIntro);
        knowledge.setCureAndCost(cureAndCost);
        knowledge.setPrognosis(prognosis);

        int res = diseaseKnowledgeDao.insert(knowledge);
        if (res > 0) {
            insertRecord(knowledge.getId(), adminUserId, "添加疾病科普信息");
        }

        return NewResponseUtil.makeSuccess(res);
    }

    public Response<Integer> update(long id, String diseaseIntro, String cureAndCost, String prognosis, long adminUserId) {


        int res = diseaseKnowledgeDao.update(id, diseaseIntro, cureAndCost, prognosis);
        if (res > 0) {
            insertRecord(id, adminUserId, "修改疾病科普信息");
        }
        return NewResponseUtil.makeSuccess(res);
    }

    public Response<Integer> delete(long id, long adminUserId) {
        int res = diseaseKnowledgeDao.delete(id);
        if (res > 0) {
            insertRecord(id, adminUserId, "删除疾病科普信息");
        }
        return NewResponseUtil.makeSuccess(res);
    }

    public Response<Map<String, Object>> selectList(String diseaseNorm, int current, int pageSize) {
        PageHelper.startPage(current, pageSize);
        List<DiseaseKnowledge> diseaseKnowledgeList = diseaseKnowledgeDao.selectList(diseaseNorm);
        Map<String, Object> result = Maps.newHashMap();
        result.put("pagination", PageUtil.transform2PageMap(diseaseKnowledgeList));
        result.put("list", diseaseKnowledgeList);
        return NewResponseUtil.makeSuccess(result);
    }

    public Response<DiseaseKnowledge> select(String diseaseNorm) {
        return NewResponseUtil.makeSuccess(diseaseKnowledgeDao.select(diseaseNorm));
    }

    public void insertRecord(long scienceId, long operator, String remark) {
        diseaseKnowledgeRecordDao.insert(scienceId, operator, remark);
    }

    public Response<List<DiseaseKnowledgeRecord>> selectRecordList(int scienceId) {
        List<DiseaseKnowledgeRecord> diseaseKnowledgeRecordList = diseaseKnowledgeRecordDao.selectList(scienceId);

        if (CollectionUtils.isEmpty(diseaseKnowledgeRecordList)) {
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }

        for (DiseaseKnowledgeRecord d : diseaseKnowledgeRecordList) {
            AuthRpcResponse<String> authRpcResponse = seaAccountClientV1.getNameByLongUserId(d.getOperator());
            String userName = Optional.ofNullable(authRpcResponse).map(AuthRpcResponse::getResult).orElse(StringUtils.EMPTY);
            d.setOperatorName(userName);
            d.setTime(DateUtil.formatDateTime(d.getCreateTime()));
        }

        return NewResponseUtil.makeSuccess(diseaseKnowledgeRecordList);
    }
}
