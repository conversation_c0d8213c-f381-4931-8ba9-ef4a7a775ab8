package com.shuidihuzhu.cf.service.workorder.read.impl.finance;

import com.shuidihuzhu.cf.service.workorder.read.impl.AbstractFinanceWorkOrderCheckHandler;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 修改类型 changeType
 * 修改提交时间 updateTime
 */
@Component
public class ModifyPayeeInfoCheckHandler extends AbstractFinanceWorkOrderCheckHandler {
    @Override
    public WorkOrderType getWorkOrderType() {
        return WorkOrderType.modify_payee_info;
    }

    @Override
    public Map<String, Object> getExtInfo(long workOrderId, int orderType) {
        Map<String, Object> res = super.getExtInfo(workOrderId, orderType);
        // 修改类型
        updateMap(res, "changeType", "modType");
        // 修改提交时间
        updateMap(res, "updateTime", "modSubTm");
        // 修改前
        if (res.containsKey("personPayeeInfo")) {
            updateMap(res, "personPayeeInfo", "befMod");
        } else if (res.containsKey("hospitalPayee")) {
            updateMap(res, "hospitalPayee", "befMod");
        }
        // 修改后
        updateMap(res, "payeeInfoChange", "aftMod");
        return res;
    }
}
