package com.shuidihuzhu.cf.service.crowdfunding.report;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimaps;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.enums.crowdfunding.CfReportPageEnum;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfReportProblemDefaultVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfReportProblemRaiserLabel;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfReportProblemVo;
import com.shuidihuzhu.cf.service.crowdfunding.strategy.IReportLabelCreateStrategy;
import com.shuidihuzhu.cf.service.report.CfReportCommunicationStrategyService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2019-12-11 15:17
 **/
@Service
@Slf4j
public class CfReportProblemService {

    //标签对应的模块可以重复添加
    private static final List<String> CAN_REPEAT_LABEL = Lists.newArrayList("负债", "平台多次筹款", "政府救助");

    @Autowired
    private CfReportProblemBiz cfReportProblemBiz;

    @Autowired
    private CfReportProblemRelationshipBiz relationshipBiz;

    @Autowired
    private IReportLabelCreateStrategy createStrategy;

    @Autowired
    private AdminCrowdfundingReportBiz adminCrowdfundingReportBiz;

    @Autowired
    private ICfReportAnswerService cfReportAnswerService;

    @Autowired
    private CfReportCommunicationStrategyService cfReportCommunicationStrategyService;

    @Autowired
    private ICfFundraiserCommunicateService iCfFundraiserCommunicateService;


    public Response<List<CfReportProblemLabel>> listAllLabel(Integer level, Integer parentId, boolean isFundraiser,
                                                             int caseId, long followId, long reportWorkOrderId, int userId) {
        List<CfReportProblemLabel> labels = isFundraiser ?
                cfReportCommunicationStrategyService.getAllLabels(caseId, followId, reportWorkOrderId, userId)
                :cfReportProblemBiz.listLabels(level, parentId,null);
        labels = labels.stream().sorted(Comparator.comparing(CfReportProblemLabel::getLabelLevel)
                .thenComparing(CfReportProblemLabel::getSort).thenComparing(CfReportProblemLabel::getId)).collect(Collectors.toList());
        return NewResponseUtil.makeSuccess(labels);
    }


    public Response<List<CfReportProblemDefaultVo>> listDirectShowProblem(List<Integer> labels, int reportPage) {
        List<CfReportProblemLabel> problemLabelList = cfReportProblemBiz.listProblemLabelsByIds(labels,1);
        if (CollectionUtils.isEmpty(problemLabelList)) {
            return NewResponseUtil.makeFail("没找到标签信息");
        }
        List<Integer> showLocations = Lists.newArrayList();
        if (reportPage == CfReportPageEnum.QUESTIONER.getKey() || reportPage == CfReportPageEnum.FUNDRAISER.getKey()) {
            showLocations.add(reportPage);
            showLocations.add(CfReportPageEnum.QUESTIONER_AND_FUNDRAISER.getKey());
        }
        List<CfReportProblem> cfReportProblems = cfReportProblemBiz.listDirectShow(labels,showLocations);
        //获取下拉框信息
        List<CfReportProblemRelationship> relationships = relationshipBiz.listRelationByProblemIds(cfReportProblems.stream().map(CfReportProblem::getId).distinct().collect(Collectors.toList()));
        //填充信息
        List<CfReportProblemVo> problemVoList = CfReportProblemVo.fillExtInfo(cfReportProblems, relationships, problemLabelList);
        ImmutableListMultimap<Integer, CfReportProblemVo> labelId2Problem = Multimaps.index(problemVoList, CfReportProblemVo::getLabelId);
        Map<Integer, String> labelId2Desc = problemLabelList.stream().collect(Collectors.toMap(CfReportProblemLabel::getId, CfReportProblemLabel::getLabelDesc, (before, after) -> after));
        List<CfReportProblemDefaultVo> problemDefaultVos = Lists.newArrayList();
        //标签-List<problem>
        for (Integer labelId : labelId2Problem.keySet()) {
            CfReportProblemDefaultVo item = new CfReportProblemDefaultVo();
            String labelDesc = labelId2Desc.get(labelId);
            item.setCanRepeat(CAN_REPEAT_LABEL.contains(labelDesc));
            item.setLabelDesc(labelDesc);
            item.setLabelId(labelId);
            item.setDefaultProblems(labelId2Problem.get(labelId).stream()
                    .sorted(Comparator.comparing(CfReportProblem::getSort).thenComparing(CfReportProblem::getId)).collect(Collectors.toList()));
            problemDefaultVos.add(item);
        }
        return NewResponseUtil.makeSuccess(problemDefaultVos);
    }


    public Response<List<CfReportProblemVo>> listRelateProblem(int problemId, String content) {
        //获取相关问题
        List<Integer> problemIds = relationshipBiz.obtainRelationProblemId(problemId, content);
        List<CfReportProblem> problemList = cfReportProblemBiz.listByIds(problemIds);
        List<CfReportProblemLabel> labels = cfReportProblemBiz.listProblemLabelsByIds(problemList.stream().map(CfReportProblem::getLabelId).distinct().collect(Collectors.toList()),null);
        //获取下拉框信息
        List<CfReportProblemRelationship> relationships = relationshipBiz.listRelationByProblemIds(problemIds);
        List<CfReportProblemVo> problemVoList = CfReportProblemVo.fillExtInfo(problemList, relationships, labels);
        problemVoList =  problemVoList.stream().sorted(Comparator.comparing(CfReportProblem::getSort).thenComparing(CfReportProblem::getId)).collect(Collectors.toList());
        return NewResponseUtil.makeSuccess(problemVoList);
    }


    public Response<List<CfReportProblemRaiserLabel>> listRaiserProblemLabel(int caseId) {
        List<CfReportProblemLabel> allLabels = cfReportProblemBiz.listLabels(null, null,null);
        List<CfReportProblemRaiserLabel> raiserLabels = createStrategy.create(caseId, allLabels);
        return NewResponseUtil.makeSuccess(raiserLabels);
    }


    public Response<Boolean> checkCreateRaiserProblem(int caseId) {
        //检查是否存在未处理的问题
        List<CrowdfundingReport> reportList = adminCrowdfundingReportBiz.getListByInfoId(caseId);
        boolean existUnHandler = reportList.stream().anyMatch(item -> item.getHandleStatus() == 1);
        if (existUnHandler) {
            return NewResponseUtil.makeFail("存在[未处理]状态质疑内容，无法请求问题");
        }
        return NewResponseUtil.makeSuccess(true);
    }

    public Response<List<AdminReportProblemAnswer>> getLastFundraiserFollowRecord(int caseId, int followId) {
        CfFundraiserCommunicateDO cfFundraiserCommunicateDO = iCfFundraiserCommunicateService.queryById(followId);
        if (Objects.isNull(cfFundraiserCommunicateDO) || StringUtils.isBlank(cfFundraiserCommunicateDO.getAnswerIds())){
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        String answerIds = cfFundraiserCommunicateDO.getAnswerIds();
        List<Long> answerIdList =
                Splitter.on(",").splitToList(answerIds).stream().map(Long::valueOf).sorted(Comparator.comparing(Long::longValue).reversed()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(answerIdList)) {
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        long id = answerIdList.get(0);
        AdminReportProblemAnswerDetail adminReportProblemAnswerDetail = cfReportAnswerService.queryById(id);
        if (adminReportProblemAnswerDetail == null || adminReportProblemAnswerDetail.getAnswerDetail() == null) {
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        List<AdminReportProblemAnswer> adminReportProblemAnswers = JSON.parseObject(adminReportProblemAnswerDetail.getAnswerDetail(), new TypeReference<List<AdminReportProblemAnswer>>() {});//已检查过
        checkIsNeedVerify(adminReportProblemAnswers);
        return NewResponseUtil.makeSuccess(adminReportProblemAnswers);
    }

    public void checkIsNeedVerify(List<AdminReportProblemAnswer> answerDetails) {
        if (CollectionUtils.isEmpty(answerDetails)) {
            return;
        }
        List<CfReportProblem> cfReportProblems = cfReportProblemBiz.listForManager(null,null);
        if (CollectionUtils.isEmpty(cfReportProblems)) {
            return;
        }
        for (AdminReportProblemAnswer adminReportProblemAnswer : answerDetails) {
            if (Objects.isNull(adminReportProblemAnswer) ||
                    CollectionUtils.isEmpty(adminReportProblemAnswer.getProblemLabelAnswers())) {
                continue;
            }
            List<AdminReportProblemLabelAnswer> adminReportProblemLabelAnswers = adminReportProblemAnswer.getProblemLabelAnswers();
            List<AdminReportProblemLabelAnswer> adminReportProblemLabelAnswerList = Lists.newArrayList();
            if (CollectionUtils.isEmpty(adminReportProblemLabelAnswers)) {
                continue;
            }
            for (AdminReportProblemLabelAnswer answer : adminReportProblemLabelAnswers) {
                setIsNeedVerify(answer, cfReportProblems, adminReportProblemLabelAnswerList);
            }
            adminReportProblemAnswer.setProblemLabelAnswers(adminReportProblemLabelAnswerList);
        }
    }

    private void setIsNeedVerify(AdminReportProblemLabelAnswer answer, List<CfReportProblem> cfReportProblems,
                                 List<AdminReportProblemLabelAnswer> adminReportProblemLabelAnswerList) {
        if (Objects.isNull(answer)) {
            return;
        }
        AdminReportProblemLabel adminReportProblemLabelAnswer = answer.getDirectShowLabel();
        if (!Objects.isNull(adminReportProblemLabelAnswer)) {
            List<CfReportProblem> cfReportProblemList = cfReportProblems.stream().filter(t -> adminReportProblemLabelAnswer.getPrefixId() != null && t.getId() == adminReportProblemLabelAnswer.getPrefixId()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(cfReportProblemList)) {
                CfReportProblem cfReportProblem = cfReportProblemList.get(0);
                adminReportProblemLabelAnswer.setIsNeedVerify(cfReportProblem.getIsNeedVerify());
                adminReportProblemLabelAnswerList.add(answer);
            }
        }
        List<List<AdminReportProblemLabel>> list = answer.getLabelAnswers();
        List<List<AdminReportProblemLabel>> newList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (List<AdminReportProblemLabel> adminReportProblemLabels : list) {
            if (CollectionUtils.isEmpty(adminReportProblemLabels)) {
                continue;
            }
            List<AdminReportProblemLabel> adminReportProblemLabelList = Lists.newArrayList();
            for (AdminReportProblemLabel adminReportProblemLabel : adminReportProblemLabels) {
                if (!Objects.isNull(adminReportProblemLabel)) {
                    List<CfReportProblem> cfReportProblemList = cfReportProblems.stream().filter(t -> adminReportProblemLabel.getPrefixId() != null && t.getId() == adminReportProblemLabel.getPrefixId()).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(cfReportProblemList)) {
                        CfReportProblem cfReportProblem = cfReportProblemList.get(0);
                        adminReportProblemLabel.setIsNeedVerify(cfReportProblem.getIsNeedVerify());
                        adminReportProblemLabelList.add(adminReportProblemLabel);
                    }
                }
            }
            newList.add(adminReportProblemLabelList);
        }
        answer.setLabelAnswers(newList);
        adminReportProblemLabelAnswerList.add(answer);
    }

}
