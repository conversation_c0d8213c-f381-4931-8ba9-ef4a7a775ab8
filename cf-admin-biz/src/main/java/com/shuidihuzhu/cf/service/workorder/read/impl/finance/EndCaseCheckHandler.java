package com.shuidihuzhu.cf.service.workorder.read.impl.finance;

import com.shuidihuzhu.cf.service.workorder.read.impl.AbstractFinanceWorkOrderCheckHandler;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 案例余额 surplusAmount eg: 1231313 (单位：分）
 */
@Component
public class EndCaseCheckHandler extends AbstractFinanceWorkOrderCheckHandler {
    @Override
    public WorkOrderType getWorkOrderType() {
        return WorkOrderType.end_case_handle;
    }

    @Override
    protected Map<String, Object> toMap(Object source) {
        Map<String, Object> res = super.toMap(source);
        // 案例余额
        updateMap(res, "surplusAmount", "caseBal");
        return res;
    }
}
