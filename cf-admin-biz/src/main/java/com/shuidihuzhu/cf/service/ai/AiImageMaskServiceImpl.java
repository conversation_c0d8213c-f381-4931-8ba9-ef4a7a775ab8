package com.shuidihuzhu.cf.service.ai;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingAttachmentBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.saas.AdminOrganization;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminAttachmentTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.client.cf.api.client.CfImageMaskFeignClient;
import com.shuidihuzhu.client.model.ImageMaskInform;
import com.shuidihuzhu.client.model.MaskAttachmentVo;
import com.shuidihuzhu.client.model.enums.ImageMaskBizEnum;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import com.shuidihuzhu.msg.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2022/9/8 2:52 下午
 */
@Slf4j
@Service
public class AiImageMaskServiceImpl implements AiImageMaskService {

    @Resource
    private Producer producer;
    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;
    @Resource
    private AdminCrowdfundingAttachmentBiz adminCrowdfundingAttachmentBiz;
    @Resource
    private CfImageMaskFeignClient cfImageMaskFeignClient;

    private final static List<Integer> ugcTypes = Lists.newArrayList(ImageMaskBizEnum.CF_DETAIL_IMAGE.getCode(), ImageMaskBizEnum.CF_MEDICAL_IMAGE.getCode());

    @Override
    public void sendImageMaskMq(Integer caseId, Integer bizType) {
        sendImageMaskMqByBizId(caseId, bizType, null);
    }

    @Override
    public void sendImageMaskMqByBizId(Integer caseId, Integer bizType, Long bizId) {

        ImageMaskInform imageMaskInform = ImageMaskInform.builder()
                .caseId(caseId)
                .bizType(bizType)
                .bizId(bizId)
                .build();

        Message msg =  new Message(MQTopicCons.CF, MQTagCons.CF_IMAGE_MASK_TO_AI,
                "" + System.currentTimeMillis(),
                imageMaskInform);
        MessageResult result = producer.send(msg);
        log.info("图片掩码通知消息发送 msg:{} result:{}", msg, result);

    }

    @Override
    public Response<Map<String, Object>> queryUgcMaskAttachment(String infoId, String param) {
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfo(infoId);
        if (Objects.isNull(crowdfundingInfo)) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }

        CfAttachmentFilter cfAttachmentFilter = null;
        try {
            cfAttachmentFilter = JSON.parseObject(param, CfAttachmentFilter.class);
        } catch (Exception e) {
            log.error("筛选失败 infoUuid:{}", infoId, e);
        }

        List<CfAttachmentVo> attachmentVos = Lists.newArrayList();
        List<CrowdfundingAttachment> fundingAttachment = adminCrowdfundingAttachmentBiz.queryAttachment(crowdfundingInfo.getId());
        for (CrowdfundingAttachment attachment : fundingAttachment) {
            if (adminCrowdfundingAttachmentBiz.canNotShowInUgcManage(attachment.getType())) {
                continue;
            }

            CfAttachmentVo attachmentVo = new CfAttachmentVo();
            BeanUtils.copyProperties(attachment, attachmentVo);
            attachmentVos.add(attachmentVo);
        }
        Map<Integer, CfAttachmentVo> fundingAttachmentMap = attachmentVos.stream().collect(Collectors.toMap(CfAttachmentVo::getId, Function.identity()));

        // 查询掩码图片
        List<Long> bizIds = attachmentVos.stream().map(CrowdfundingAttachment::getId).map(Integer::longValue).collect(Collectors.toList());
        Response<List<MaskAttachmentVo>> response = cfImageMaskFeignClient.queryMaskImage(bizIds, ugcTypes);
        List<MaskAttachmentVo> maskAttachmentVos = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(Lists.newArrayList());

        // 替换内容
        List<CfAttachmentVo> cfAttachmentVoList = Lists.newArrayList();
        for (MaskAttachmentVo maskAttachmentVo : maskAttachmentVos) {
            if (bizIds.contains(maskAttachmentVo.getBizId())) {
                Integer bizId = Math.toIntExact(maskAttachmentVo.getBizId());
                CfAttachmentVo cfAttachmentVo = fundingAttachmentMap.get(bizId);
                cfAttachmentVo.setIsDelete(maskAttachmentVo.getIsDelete());
                cfAttachmentVo.setUrl(maskAttachmentVo.getAiImageUrl());
                cfAttachmentVo.setAiFinishTime(maskAttachmentVo.getAiFinishTime());
                cfAttachmentVoList.add(cfAttachmentVo);
            }
        }

        // 根据上传时间排序
        Collections.sort(cfAttachmentVoList, new Comparator<CfAttachmentVo>() {
            @Override
            public int compare(CfAttachmentVo o1, CfAttachmentVo o2) {
                if (o1.getCreateTime() == o2.getCreateTime()) {
                    return 0;
                } else if (o1.getCreateTime().getTime() < o2.getCreateTime().getTime()) {
                    return -1;
                } else {
                    return 1;
                }
            }
        });

        // 筛选条件
        if (Objects.nonNull(cfAttachmentFilter)) {
            Integer isDelete = cfAttachmentFilter.getIsDelete();
            Integer type = cfAttachmentFilter.getType();
            String uploadTimeStart = cfAttachmentFilter.getUploadTimeStart();
            String uploadTimeEnd = cfAttachmentFilter.getUploadTimeEnd();

            if (Objects.nonNull(isDelete)) {
                cfAttachmentVoList = cfAttachmentVoList.stream().filter(v -> v.getIsDelete() == isDelete).collect(Collectors.toList());
            }

            if (Objects.nonNull(type)) {
                AttachmentTypeEnum attachmentTypeEnum  = AttachmentTypeEnum.getAttachmentTypeEnum(type);
                cfAttachmentVoList = cfAttachmentVoList.stream().filter(v -> v.getTypeValue() == attachmentTypeEnum.value()).collect(Collectors.toList());
            }

            if (StringUtils.isNotEmpty(uploadTimeStart) && StringUtils.isNotEmpty(uploadTimeEnd)) {
                cfAttachmentVoList = cfAttachmentVoList.stream().filter(v -> v.getCreateTime().getTime() >= com.shuidihuzhu.msg.util.DateUtil.getStr2LDate(uploadTimeStart).getTime()
                        && v.getCreateTime().getTime() <= DateUtil.getStr2LDate(uploadTimeEnd).getTime()).collect(Collectors.toList());
            }
        }

        Map<Integer, String> attachmentMap = Maps.newHashMap(AdminAttachmentTypeEnum.emnuMap);
        for(Map.Entry<Integer, String> entry : attachmentMap.entrySet()) {
            entry.setValue(entry.getValue() + "(掩码后)");
        }

        Map<String, Object> result = Maps.newHashMap();
        result.put("attachmentMap", attachmentMap);
        result.put("fundingAttachment", cfAttachmentVoList);
        return NewResponseUtil.makeSuccess(result);

    }


}
