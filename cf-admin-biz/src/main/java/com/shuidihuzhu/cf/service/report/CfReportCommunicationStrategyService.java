package com.shuidihuzhu.cf.service.report;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableBiMap;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.biz.crowdfunding.CfReportProblemBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.ICfFundraiserCommunicateService;
import com.shuidihuzhu.cf.biz.crowdfunding.ICfReportAnswerService;
import com.shuidihuzhu.cf.biz.crowdfunding.report.AdminCfReportStrategyCallRecordBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.CfReportPageEnum;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.report.CfReportMandatoryConfig;
import com.shuidihuzhu.cf.service.crowdfunding.SeaAccountService;
import com.shuidihuzhu.cf.vo.report.CfReportCommunicationStrategyVo;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.EncryptUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * https://wiki.shuiditech.com/pages/viewpage.action?pageId=*********
 *
 * @Auther: subing
 * @Date: 2020/6/23
 */
@Service
@RefreshScope
@Slf4j
public class CfReportCommunicationStrategyService {
    @Value("${apollo.report.communication.strategy:''}")
    private String communicationStrategy;
    @Value("${apollo.report.communication.keyword:不知道}")
    private String keyword;

    @Autowired
    private ICfReportAnswerService iCfReportAnswerService;
    @Autowired
    private CfReportProblemBiz cfReportProblemBiz;
    @Autowired
    private ICfFundraiserCommunicateService iCfFundraiserCommunicateService;
    @Autowired
    private AdminCfReportStrategyCallRecordBiz adminCfReportStrategyCallRecordBiz;
    @Autowired
    private SeaAccountService seaAccountService;


    private List<CfReportMandatoryConfig> getConfig() {
        List<CfReportMandatoryConfig> cfReportMandatoryConfigs = Lists.newArrayList();
        try {
            cfReportMandatoryConfigs = JSON.parseObject(communicationStrategy, new TypeReference<List<CfReportMandatoryConfig>>() {
            });
        } catch (Exception e) {
            log.error("", e);
        }
        return cfReportMandatoryConfigs;
    }

    public List<CfReportProblemLabel> getAllLabels(int caseId, long followId, long workOrderId, int userId) {
        //A
        Set<String> questionerLabels = getInfoByQuestioner(caseId);
        Set<String> questionerLabels1 = Sets.newHashSet(questionerLabels);
        //B
        Set<String> allFundraiserLabels = getInfoByAllFundraiser(caseId);
        //C
        Set<String> unknownInfoByFundraiserLabels = getUnknownInfoByFundraiser(caseId);
        //AUB
        questionerLabels.addAll(allFundraiserLabels);
        //（AUB）-C
        questionerLabels.removeAll(unknownInfoByFundraiserLabels);
        //（AUB）-C】UD
        Set<String> currentRoleInfo = Sets.newHashSet();
        boolean status = true;
        if (followId > 0) {
            CfReportCommunicationStrategyVo cfReportCommunicationStrategyVo = getCurrentRoleInfo(followId);
            if (cfReportCommunicationStrategyVo.isNewStrategy()) {
                currentRoleInfo.addAll(cfReportCommunicationStrategyVo.getLabels());
                questionerLabels.addAll(currentRoleInfo);
            } else {
                questionerLabels.clear();
                questionerLabels1.clear();
                allFundraiserLabels.clear();
                unknownInfoByFundraiserLabels.clear();
                currentRoleInfo.clear();
            }
            status = cfReportCommunicationStrategyVo.isNewStrategy();
        }
        List<CfReportProblemLabel> cfReportProblemLabels = cfReportProblemBiz.listLabels(null, null, 1);
        List<CfReportProblemLabel> firstLevelLabels = cfReportProblemLabels.stream().filter(t -> t.getLabelLevel() == 1).collect(Collectors.toList());
        List<CfReportProblemLabel> secondLevelLabels = cfReportProblemLabels.stream().filter(t -> t.getLabelLevel() == 2).collect(Collectors.toList());
        for (CfReportProblemLabel cfReportProblemLabel : secondLevelLabels) {
            cfReportProblemLabel.setIsMandatory(0);
            if (questionerLabels.contains(cfReportProblemLabel.getLabelDesc()) && status) {
                cfReportProblemLabel.setIsMandatory(1);
            }
        }
        adminCfReportStrategyCallRecordBiz.addInfo(caseId, workOrderId, Joiner.on(",").join(questionerLabels1),
                Joiner.on(",").join(allFundraiserLabels), Joiner.on(",").join(unknownInfoByFundraiserLabels),
                Joiner.on(",").join(currentRoleInfo), seaAccountService.getName(userId), seaAccountService.getOrganization(userId));
        firstLevelLabels.addAll(secondLevelLabels);
        return firstLevelLabels;
    }

    //条件a
    private Set<String> getInfoByQuestioner(int caseId) {
        List<AdminReportProblemAnswerDetail> adminReportProblemAnswerDetails =
                iCfReportAnswerService.queryByCaseIdAndType(caseId, CfReportPageEnum.QUESTIONER.getKey());
        Set<String> labels = Sets.newHashSet();
        this.addLabels(labels, adminReportProblemAnswerDetails, false, true);
        return labels;
    }

    //条件b
    private Set<String> getInfoByAllFundraiser(int caseId) {
        List<AdminReportProblemAnswerDetail> details =
                iCfReportAnswerService.queryByType(caseId, CfReportPageEnum.FUNDRAISER.getKey());
        Set<String> labels = Sets.newHashSet();
        this.addLabels(labels, details, false, false);
        return labels;
    }

    //条件c
    private Set<String> getUnknownInfoByFundraiser(int caseId) {
        List<AdminReportProblemAnswerDetail> details =
                iCfReportAnswerService.queryByType(caseId, CfReportPageEnum.FUNDRAISER.getKey());
        Set<String> labels = Sets.newHashSet();
        this.addLabels(labels, details, true, false);
        return labels;
    }

    //条件d
    private CfReportCommunicationStrategyVo getCurrentRoleInfo(long followId) {
        CfFundraiserCommunicateDO cfFundraiserCommunicateDO = iCfFundraiserCommunicateService.queryById(followId);
        Set<String> labels = Sets.newHashSet();
        //无筹款人沟通记录（第一次沟通）
        if (cfFundraiserCommunicateDO == null || StringUtils.isBlank(cfFundraiserCommunicateDO.getAnswerIds())) {
            return new CfReportCommunicationStrategyVo(labels, true);
        }
        String answerIds = cfFundraiserCommunicateDO.getAnswerIds();
        List<Long> answerIdList =
                Splitter.on(",").splitToList(answerIds).stream().map(Long::valueOf).sorted(Comparator.comparing(Long::longValue).reversed()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(answerIdList)) {
            return new CfReportCommunicationStrategyVo(labels, true);
        }
        long id = answerIdList.get(0);
        AdminReportProblemAnswerDetail detail = iCfReportAnswerService.queryById(id);
        if (Objects.isNull(detail)) {
            return new CfReportCommunicationStrategyVo(labels, true);
        }
        //有筹款人沟通记录（非第一次沟通，上一次沟通走的新策略或者老策略）
        if (detail.isNewStrategy()){
            labels = StringUtils.isBlank(detail.getMandatoryInfo()) ? labels :
                    JSON.parseObject(detail.getMandatoryInfo(), new TypeReference<Set<String>>(){});
            return new CfReportCommunicationStrategyVo(labels, true);
        }else {
            return new CfReportCommunicationStrategyVo(labels, false);
        }
    }


    private void addUnKnownLabels(AdminReportProblemAnswer adminReportProblemAnswer, Set<String> labels) {
        if (adminReportProblemAnswer == null || CollectionUtils.isEmpty(adminReportProblemAnswer.getProblemLabelAnswers())) {
            return;
        }
        List<AdminReportProblemLabelAnswer> answers = adminReportProblemAnswer.getProblemLabelAnswers();
        boolean status = true;
        for (AdminReportProblemLabelAnswer answer : answers) {
            if (Objects.isNull(answer)) {
                continue;
            }
            AdminReportProblemLabel adminReportProblemLabel = answer.getDirectShowLabel();
            if (Objects.nonNull(adminReportProblemLabel) &&
                    StringUtils.equals(adminReportProblemLabel.getAnswer(), keyword)) {
                status = false;
                break;
            }
            List<List<AdminReportProblemLabel>> lists = answer.getLabelAnswers();
            if (CollectionUtils.isEmpty(lists)) {
                continue;
            }
            for (List<AdminReportProblemLabel> adminReportProblemLabels : lists) {
                if (CollectionUtils.isEmpty(adminReportProblemLabels)) {
                    continue;
                }
                List<AdminReportProblemLabel> problemLabels =
                        adminReportProblemLabels.stream().filter(t -> StringUtils.equals(t.getAnswer(), keyword))
                                .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(problemLabels)){
                    status = false;
                    break;
                }
            }
            if (!status){
                break;
            }
        }
        if (status) {
            labels.add(adminReportProblemAnswer.getSecondLabelDesc());
        }
    }


    private void addAllLabels(AdminReportProblemAnswer adminReportProblemAnswer, Set<String> labels,
                              List<CfReportMandatoryConfig> cfReportMandatoryConfigs, boolean isUseConfig) {
        if (Objects.isNull(adminReportProblemAnswer)){
            return;
        }
        String label = adminReportProblemAnswer.getSecondLabelDesc() == null ? "" :
                adminReportProblemAnswer.getSecondLabelDesc();
        boolean status = isUseConfig ? labels.addAll(getLabels(label, cfReportMandatoryConfigs)) : labels.add(label);
        log.info("addAllLabels status {}", status);
    }

    private void addLabels(Set<String> labels,
                           List<AdminReportProblemAnswerDetail> adminReportProblemAnswerDetails, boolean isUnknown,
                           boolean isUseConfig) {
        if (CollectionUtils.isEmpty(adminReportProblemAnswerDetails)) {
            return;
        }
        List<CfReportMandatoryConfig> cfReportMandatoryConfigs = getConfig();
        for (AdminReportProblemAnswerDetail adminReportProblemAnswerDetail : adminReportProblemAnswerDetails) {
            if (Objects.isNull(adminReportProblemAnswerDetail)) {
                continue;
            }
            String answerDetail = adminReportProblemAnswerDetail.getAnswerDetail();
            if (StringUtils.isNotBlank(answerDetail)) {
                List<AdminReportProblemAnswer> adminReportProblemAnswers =
                        JSON.parseObject(answerDetail, new TypeReference<List<AdminReportProblemAnswer>>() {
                        });
                if (CollectionUtils.isEmpty(adminReportProblemAnswers)) {
                    continue;
                }
                for (AdminReportProblemAnswer adminReportProblemAnswer : adminReportProblemAnswers) {
                    if (Objects.isNull(adminReportProblemAnswer)) {
                        continue;
                    }
                    if (isUnknown) {
                        addUnKnownLabels(adminReportProblemAnswer, labels);
                    } else {
                        addAllLabels(adminReportProblemAnswer, labels, cfReportMandatoryConfigs, isUseConfig);
                    }
                }
            }
        }
    }


    private Set<String> getLabels(String label, List<CfReportMandatoryConfig> cfReportMandatoryConfigs) {
        Set<String> labels = Sets.newHashSet();
        cfReportMandatoryConfigs.forEach(cfReportMandatoryConfig -> {
            if (StringUtils.equals(cfReportMandatoryConfig.getCommunicationContent(), label)) {
                labels.addAll(Splitter.on(",").splitToList(cfReportMandatoryConfig.getMandatoryModule()));
            }
        });
        return labels;
    }
}
