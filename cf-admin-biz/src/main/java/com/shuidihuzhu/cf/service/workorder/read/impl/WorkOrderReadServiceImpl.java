package com.shuidihuzhu.cf.service.workorder.read.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.admin.UserCommentBiz;
import com.shuidihuzhu.cf.biz.aiphoto.ImageWatermarkService;
import com.shuidihuzhu.cf.biz.amount.AmountReasonableBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.biz.fundUse.CfFundUseProgressBiz;
import com.shuidihuzhu.cf.client.adminpure.model.amount.CfAmountReasonableTaskWorkOrder;
import com.shuidihuzhu.cf.client.apipure.enums.complaint.ComplaintBizTypeEnum;
import com.shuidihuzhu.cf.client.apipure.enums.complaint.ComplaintResultEnum;
import com.shuidihuzhu.cf.client.apipure.enums.complaint.ComplaintTypeEnum;
import com.shuidihuzhu.cf.client.apipure.feign.ComplaintFeignClient;
import com.shuidihuzhu.cf.client.apipure.model.crowdfunding.complaint.CfComplaintDO;
import com.shuidihuzhu.cf.client.base.page.v1.model.PaginationListVO;
import com.shuidihuzhu.cf.client.feign.CfFirstApproveFeignClient;
import com.shuidihuzhu.cf.client.feign.CfUserInfoFeignClient;
import com.shuidihuzhu.cf.client.feign.CfVerificationFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminTwModifyDao;
import com.shuidihuzhu.cf.delegate.EncryptDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.es.WorkOrderEsDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.ReportFollowActionEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminUGCTask;
import com.shuidihuzhu.cf.enums.crowdfunding.CaseReportDealStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.CfSensitiveWordRecordEnum;
import com.shuidihuzhu.cf.model.admin.TwModifyRecordDO;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCfFundUseAuditInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoStat;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyAction;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyProgress;
import com.shuidihuzhu.cf.model.crowdfunding.CfSensitiveWordRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport;
import com.shuidihuzhu.cf.model.crowdfunding.UserRealInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.QueryListResultVo;
import com.shuidihuzhu.cf.param.workorder.WorkOrderListQueryOldParam;
import com.shuidihuzhu.cf.risk.client.rpc.DiscussionCommentClient;
import com.shuidihuzhu.cf.risk.model.CommentVO;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.cf.service.crowdfunding.CfSupplyActionService;
import com.shuidihuzhu.cf.service.crowdfunding.CfSupplyProgressService;
import com.shuidihuzhu.cf.service.crowdfunding.complaint.ComplaintService;
import com.shuidihuzhu.cf.service.workorder.delayfinance.IDelayFinanceWorkOrderService;
import com.shuidihuzhu.cf.service.workorder.imagePublic.ImagePublicWorkOrderService;
import com.shuidihuzhu.cf.service.workorder.promoteBill.CfPromoteBillService;
import com.shuidihuzhu.cf.service.workorder.read.WorkOrderReadService;
import com.shuidihuzhu.cf.util.crowdfunding.LocalDateTimeUtils;
import com.shuidihuzhu.cf.vo.crowdfunding.AdminCrowdfundingProgress;
import com.shuidihuzhu.cf.vo.crowdfunding.CfVerificationVo;
import com.shuidihuzhu.client.cf.search.model.CfWorkOrderIndexSearchResult;
import com.shuidihuzhu.client.cf.search.model.CfWorkOrderModel;
import com.shuidihuzhu.client.cf.search.param.CfWorkOrderV2IndexSearchParam;
import com.shuidihuzhu.client.cf.search.param.table.CrowdfundingInfoTableParam;
import com.shuidihuzhu.client.cf.search.param.table.WorkOrderExtTableParam;
import com.shuidihuzhu.client.cf.search.param.table.WorkOrderTableParam;
import com.shuidihuzhu.client.cf.workorder.CfJuanzhuanWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfUgcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.QueryListParam;
import com.shuidihuzhu.client.cf.workorder.model.QueryListResult;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.cf.workorder.read.BasicWorkOrder;
import com.shuidihuzhu.client.cf.workorder.read.WorkOrderReadFeignClient;
import com.shuidihuzhu.client.cf.workorder.v2.client.WorkOrderStatV2FeignClient;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.frame.client.api.bc.CfTrustDonateAuditClient;
import com.shuidihuzhu.frame.client.api.mina.HopeTreeClient;
import com.shuidihuzhu.frame.client.model.mina.hopetree.HopeTreeStateRecordDO;
import com.shuidihuzhu.frame.client.model.publictrust.CommentContentRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RefreshScope
public class WorkOrderReadServiceImpl implements WorkOrderReadService {

    private static final SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private EncryptDelegate encryptDelegate;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;

    @Autowired
    private WorkOrderEsDelegate workOrderEsDelegate;

    @Autowired
    private WorkOrderReadFeignClient workOrderReadFeignClient;

    @Autowired
    private UserCommentBiz commentBiz;

    @Autowired
    private CfSensitiveWordRecordBiz sensitiveWordRecordBiz;

    @Autowired
    private AdminCrowdfundingCityBiz adminCrowdfundingCityBiz;

    @Autowired
    private AdminCrowdFundingProgressBiz adminCrowdFundingProgressBiz;

    @Resource
    private IRiskDelegate riskDelegate;

    @Resource
    private CfSupplyProgressService cfSupplyProgressService;

    @Autowired
    private DiscussionCommentClient discussionCommentClient;

    @Autowired
    private ImageWatermarkService watermarkService;

    @Autowired
    private CfSupplyActionService cfSupplyActionService;

    @Autowired
    private CfJuanzhuanWorkOrderClient juanzhuanWorkOrderClient;

    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Resource(name = "cf2RedissonHandler")
    private RedissonHandler cf2RedissonHandler;

    @Autowired
    private HopeTreeClient hopeTreeClient;

    @Autowired
    private CfFundUseProgressBiz cfFundUseProgressBiz;

    @Autowired
    private AdminCrowdfundingAuthorBiz adminCrowdfundingAuthorBiz;

    @Autowired
    private AdminCrowdfundingReportBiz adminCrowdfundingReportBiz;
    @Autowired
    private CfFirstApproveFeignClient cfFirstApproveFeignClient;

    @Value("${fund.use.work.order.patient.name:true}")
    private boolean fund_use_work_order_patient_name;

    @Autowired
    private CfUgcWorkOrderClient ugcWorkOrderClient;

    @Resource
    private CfVerificationFeignClient cfVerificationFeignClient;

    @Resource
    private CfUserInfoFeignClient cfUserInfoFeignClient;

    @Autowired
    private CfPromoteBillService promoteBillService;
    @Autowired
    private CfTrustDonateAuditClient cfTrustDonateAuditClient;

    @Resource
    private ComplaintFeignClient complaintFeignClient;

    @Resource
    private ComplaintService complaintService;
    @Resource
    private ImagePublicWorkOrderService imagePublicWorkOrderService;

    @Autowired
    private ApplicationService applicationService;

    @Resource
    private AdminTwModifyDao adminTwModifyDao;

    @Resource
    private WorkOrderStatV2FeignClient workOrderStatV2FeignClient;

    @Resource
    private AmountReasonableBiz amountReasonableBiz;
    @Resource
    private IDelayFinanceWorkOrderService delayFinanceWorkOrderService;

    // 工单生成数量
    private static final Integer WORK_ORDER_COUNT_THRESHOLD = 4;

    @Override
    public boolean checkCanNotHandle(long workOrderId, int operatorId) {
        if (applicationService.isDevelopment()){
            return false;
        }
        // 工单id为0 允许处理
        if (workOrderId <= 0) {
            return false;
        }
        Response<BasicWorkOrder> orderResp = workOrderReadFeignClient.getByOrderId(workOrderId);
        if (NewResponseUtil.isNotOk(orderResp)) {
            log.warn("查询工单失败 {}", orderResp);
            return true;
        }
        log.debug("查询工单成功 {}, {}, {}", workOrderId, operatorId, orderResp);
        BasicWorkOrder order = orderResp.getData();
        return order.getOperatorId() != operatorId;
    }

    @Override
    public List<QueryListResultVo> getQueryListResultVos(QueryListParam queryListParam, List<QueryListResult> list) {

        Map<Long, CfSensitiveWordRecord> ugcMap = getUgcList(list, queryListParam);

        Map<Long, CommentVO> pingyiMap = getPingyiList(list, queryListParam);

        Map<Integer, CrowdFundingProgress> progressMap = getProgressList(list, queryListParam);

        Map<Long, CfInfoSupplyProgress> supplyProgressMap = cfSupplyProgressService.getSupplyProgressList(list, queryListParam);

        Map<Long, CfInfoSupplyAction> actionMap = getActionMap(list, queryListParam);

        Map<Integer, QueryListResultVo> juanzhanMap = getJuanzhuan(list, queryListParam);

        //希望树动态
        Map<Long, HopeTreeStateRecordDO> stateMap = getHopeTreeState(list, queryListParam);

        Map<Integer, AdminCfFundUseAuditInfo> fundUseAuditInfo = getFundUseAuditInfo(list, queryListParam);

        // 证实记录
        Map<Integer, CrowdFundingVerification> verificationMap = getVerification(list, queryListParam);

        //放心捐留言
        Map<Long, CommentContentRecord> contentRecordMap = getTrustDonateMsg(list, queryListParam);

        //图片公示审核工单
        Map<Long, QueryListResultVo> picturePublicityReview = getPicturePublicityReview(list, queryListParam);
        Map<Long, CfAmountReasonableTaskWorkOrder> amountReasonableMap = getAmountReasonable(list, queryListParam);

        //图文审核工单生成原因
        Map<Long, Integer> twWorkOrderReason = getTwWorkOrderReason(list, queryListParam);

        //批量获取下发是否能重审
        List<Integer> caseIds = Optional.ofNullable(list).orElse(Lists.newArrayList()).stream().map(QueryListResult::getCaseId).collect(Collectors.toList());
        Map<Integer, Boolean> caseIdTCanReprocess = cfSupplyProgressService.batchObtainReprocessStatus(caseIds);

        Response<List<Integer>> workOrderResponse = workOrderStatV2FeignClient.listCaseIdsByTypeAndCount(caseIds, WorkOrderType.cailiao_5.getType(), WORK_ORDER_COUNT_THRESHOLD);
        List<Integer> taggingCaseIds = Objects.nonNull(workOrderResponse) && workOrderResponse.ok() && CollectionUtils.isNotEmpty(workOrderResponse.getData())
                ? workOrderResponse.getData() : new ArrayList<>();

        List<QueryListResultVo> resultList = Optional.ofNullable(list).orElse(Lists.newArrayList()).stream().map(r -> {

            QueryListResultVo vo = new QueryListResultVo();
            BeanUtils.copyProperties(r, vo);
            if (r.getOrderType() == WorkOrderType.ugcprogress.getType() && org.apache.commons.lang3.StringUtils.isNotBlank(r.getContentType())) {
                vo.setContentTypeStr(AdminUGCTask.Content.getByCode(Integer.valueOf(r.getContentType())).getWord());
                vo.setContentType(r.getContentType());
            }

            if (r.getOrderType() == WorkOrderType.ugcpinglun.getType() && org.apache.commons.lang3.StringUtils.isNotEmpty(r.getWordId())) {

                if (AdminUGCTask.Content.PINGYI.getCode() == Integer.valueOf(r.getContentType())) {

                    CommentVO commentVO = pingyiMap.get(Long.valueOf(r.getWordId()));
                    if (commentVO != null) {
                        vo.setContent(commentVO.getContent());
                        vo.setUserId(commentVO.getUserId());
                        vo.setSensitiveWord(commentBiz.formatHitWordsTDisplay(commentVO.getSensitiveWord()));
                        vo.setContentTypeStr(AdminUGCTask.Content.getByCode(Integer.valueOf(r.getContentType())).getWord());
                    }
                } else {
                    CfSensitiveWordRecord record = ugcMap.get(Long.valueOf(r.getWordId()));
                    if (record != null) {
                        vo.setContent(record.getContent());
                        vo.setUserId(record.getUserId());
                        vo.setSensitiveWord(record.getSensitiveWord());
                        vo.setContentTypeStr(AdminUGCTask.Content.getByCode(Integer.valueOf(r.getContentType())).getWord());
                    }
                }
            }

            if (r.getOrderType() == WorkOrderType.ugcprogress.getType() && org.apache.commons.lang3.StringUtils.isNotEmpty(r.getWordId())) {
                CrowdFundingProgress record = progressMap.get(Integer.valueOf(r.getWordId()));
                if (record != null) {
                    vo.setContent(record.getContent());
                    vo.setUserId(record.getUserId());
                    vo.setAttachmentUrls(record.getImageUrls());
                    Set<String> sensitiveWords = riskDelegate.getHitWords(record.getContent());
                    vo.setSensitiveWord(Joiner.on(",").join(sensitiveWords));
                }
            }

            List<CrowdfundingCity> provinceList = adminCrowdfundingCityBiz.getProvince();
            final Map<String, String> provinceMap = provinceList.stream().collect(Collectors.toMap(e -> e.getCode(), e -> e.getName(), (a, b) -> b));
            if (r.getOrderType() == WorkOrderType.ugcpinglun.getType()){
                // 获取工单信息
                Response<List<WorkOrderVO>> ugcResponse= ugcWorkOrderClient.ugcOrderlistByIds(Arrays.asList(r.getWorkOrderId()));
                if (Objects.nonNull(ugcResponse) && Objects.nonNull(ugcResponse.getData())) {
                    List<WorkOrderVO> data = ugcResponse.getData();
                    if (CollectionUtils.isNotEmpty(data)) {
                        WorkOrderVO workOrderVO = data.get(0);
                        String verificationId = workOrderVO.getVerificationId();
                        // 获取证实信息
                        if (StringUtils.isNotEmpty(verificationId)) {
                            long id = Long.parseLong(verificationId);
                            // 获取证实信息
                            Response<List<CrowdFundingVerification>> verificationResponse = cfVerificationFeignClient.getVerificationList(Arrays.asList(id));
                            if (Objects.nonNull(verificationResponse) && Objects.nonNull(verificationResponse.getData())) {
                                List<CrowdFundingVerification> verificationList = verificationResponse.getData();
                                if (CollectionUtils.isNotEmpty(verificationList)) {
                                    CrowdFundingVerification verification = verificationList.get(0);
                                    if (Objects.nonNull(verification)) {
                                        vo.setMedicalImageList(verification.getMedicalImageList());
                                        vo.setHospitalName(verification.getHospitalName());
                                        vo.setVerifyName(verification.getUserName());
                                        if (Objects.nonNull(verification.getProvinceCode())) {
                                            vo.setProvinceName(provinceMap.get(String.valueOf(verification.getProvinceCode())));
                                        }
                                        vo.setMedicalWorkType(workOrderVO.getMedicalWorkType());
                                        vo.setUserId(verification.getVerifyUserId());

                                        FeignResponse<UserRealInfo> userRealInfoFeignResponse = cfUserInfoFeignClient.getByUserId(verification.getVerifyUserId());
                                        if (Objects.nonNull(userRealInfoFeignResponse) && Objects.nonNull(userRealInfoFeignResponse.getData())) {

                                            UserRealInfo userRealInfo = userRealInfoFeignResponse.getData();
                                            if (StringUtils.isNotEmpty(workOrderVO.getMedicalStatus())) {
                                                vo.setIsMedical(Integer.valueOf(workOrderVO.getMedicalStatus()));
                                            } else {
                                                vo.setIsMedical(userRealInfo.getMedicalStatus());
                                            }
                                            vo.setMedicalVerifyResultImageUrl(userRealInfo.getMedicalImageUrl());
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if (r.getOrderType() == WorkOrderType.ugc_complaint_verify.getType()){
                String verificationId = r.getVerificationId();
                if(StringUtils.isNotEmpty(verificationId)){

                    Long bizId = Long.valueOf(verificationId);

                    CrowdFundingVerification verification = verificationMap.get(bizId.intValue());
                    if(Objects.nonNull(verification)){
                        vo.setUserId(verification.getVerifyUserId());
                        vo.setContent(verification.getDescription());
                    }

                    List<CfComplaintDO> cfComplaintDOS = complaintService.listByBizIdAndBizTypeAndComplaintResult(bizId, ComplaintBizTypeEnum.VERIFICATION, ComplaintResultEnum.YES);
                    List<CfVerificationVo.ComplaintInfo> complaintInfoList = cfComplaintDOS.stream().map(e -> {

                        CfVerificationVo.ComplaintInfo complaintInfo = new CfVerificationVo.ComplaintInfo();
                        complaintInfo.setComplaintUserId(e.getComplaintUserId());
                        complaintInfo.setComplaintMsg(ComplaintTypeEnum.parse(e.getComplaintType()).getDesc());
                        String complaintTime = LocalDateTimeUtils.localDateTime2String(e.getComplaintTime());
                        complaintInfo.setComplaintTime(complaintTime);

                        return complaintInfo;
                    }).collect(Collectors.toList());
                    vo.setComplaintInfoList(complaintInfoList);
                    vo.setComplaintInfoSize(complaintInfoList.size());

                    // 敏感词
                    List<CfSensitiveWordRecord> cfSensitiveWordRecords = sensitiveWordRecordBiz.listByBizIdAndBizType(bizId, CfSensitiveWordRecordEnum.BizType.VERIFICATION);
                    if(CollectionUtils.isNotEmpty(cfSensitiveWordRecords)){
                        CfSensitiveWordRecord record = cfSensitiveWordRecords.get(0);
                        if (record != null) {
                            vo.setContent(record.getContent());
                            vo.setSensitiveWord(record.getSensitiveWord());
                            vo.setContentTypeStr(AdminUGCTask.Content.getByCode(Integer.valueOf(r.getContentType())).getWord());
                        }
                    }

                    if (StringUtils.isNotEmpty(r.getContentType())){
                        vo.setContentTypeStr(AdminUGCTask.Content.getByCode(Integer.valueOf(r.getContentType())).getWord());
                    }
                }
            }

            //放心捐留言
            if(r.getOrderType() == WorkOrderType.fxj_publicity_msg_audit.getType() || r.getOrderType() == WorkOrderType.fxj_comment_msg_audit.getType()){
                String commentId = r.getTrustDonateMsgId();
                if(StringUtils.isNotBlank(commentId)){
                    Long bizId = Long.valueOf(commentId);
                    CommentContentRecord record = contentRecordMap.get(bizId);
                    if(record != null){
                        vo.setContent(record.getContent());
                        QueryListResultVo.CommentInfo commentInfo = new QueryListResultVo.CommentInfo();
                        commentInfo.setCommentId(record.getId());
                        commentInfo.setUserId(record.getUserId());
                        commentInfo.setModuleType(record.getModuleType());
                        commentInfo.setNickName(record.getNickname());
                        commentInfo.setHeadUrl(record.getHeadUrl());
                        commentInfo.setSupport(record.getSupport());
                        Set<String> sensitiveWords = riskDelegate.getHitWords(record.getContent());
                        vo.setSensitiveWord(Joiner.on(",").join(sensitiveWords));
                        vo.setCommentInfo(commentInfo);
                    }
                }
            }

                //希望树动态
            if (r.getOrderType() == WorkOrderType.ugcprogress.getType() && org.apache.commons.lang3.StringUtils.isNotBlank(r.getHopeTreeStateId())) {
                HopeTreeStateRecordDO record = stateMap.get(Long.valueOf(r.getHopeTreeStateId()));
                if (record != null) {
                    vo.setContent(record.getContent());
                    vo.setUserId(record.getUserId());
                    vo.setAttachmentUrls(record.getImageUrl());
                    Set<String> sensitiveWords = riskDelegate.getHitWords(record.getContent());
                    vo.setSensitiveWord(Joiner.on(",").join(sensitiveWords));
                }
            }

            if (r.getOrderType() == WorkOrderType.funduseshenhe.getType() || r.getOrderType() == WorkOrderType.report_split_draw.getType()) {
                AdminCfFundUseAuditInfo adminCfFundUseAuditInfo = fundUseAuditInfo.get(r.getFundUseProgressId());
                if (Objects.nonNull(adminCfFundUseAuditInfo)){
                    vo.setContent(adminCfFundUseAuditInfo.getFundUseContent());
                    vo.setAttachmentUrls(adminCfFundUseAuditInfo.getFundUseImageMaterial());
                    vo.setPatientName("暂无患者姓名");
                    if (StringUtils.isEmpty(adminCfFundUseAuditInfo.getPatientName()) && fund_use_work_order_patient_name) {
                        FeignResponse<CfFirsApproveMaterial> cfFirstApproveMaterialByCaseId = cfFirstApproveFeignClient.getCfFirstApproveMaterialByCaseId(vo.getCaseId());
                        if (Objects.nonNull(cfFirstApproveMaterialByCaseId) && cfFirstApproveMaterialByCaseId.ok() && Objects.nonNull(cfFirstApproveMaterialByCaseId.getData())) {
                            CfFirsApproveMaterial data = cfFirstApproveMaterialByCaseId.getData();
                            vo.setPatientName(data.getPatientRealName());
                        }
                    } else {
                        vo.setPatientName(adminCfFundUseAuditInfo.getPatientName());
                    }
                    vo.setHasReport(adminCfFundUseAuditInfo.isHasReport());
                    vo.setDrawFinishTime(adminCfFundUseAuditInfo.getDrawCashTime());
                    vo.setFundUseRejectedReason(adminCfFundUseAuditInfo.getFundUseRejectedReason());
                }
            }
            if (r.getOrderType() == WorkOrderType.fund_use_amount_reasonable.getType()) {
                CfAmountReasonableTaskWorkOrder cfAmountReasonableTaskWorkOrder = amountReasonableMap.get(r.getWorkOrderId());
                if (Objects.nonNull(cfAmountReasonableTaskWorkOrder)) {
                    vo.setAmountReasonableTaskContent(cfAmountReasonableTaskWorkOrder.getContent());
                    vo.setAmountReasonableTaskImages(cfAmountReasonableTaskWorkOrder.getImages());
                    vo.setAmountReasonableTaskType(cfAmountReasonableTaskWorkOrder.getTaskType());
                    vo.setAmountReasonableTaskPlan(cfAmountReasonableTaskWorkOrder.getTaskPlan());
                    vo.setAmountReasonableTaskAmountStart(cfAmountReasonableTaskWorkOrder.getAmountStart());
                    vo.setAmountReasonableTaskAmountEnd(cfAmountReasonableTaskWorkOrder.getAmountEnd());
                    vo.setAmountReasonableAfterDays(cfAmountReasonableTaskWorkOrder.getTaskAfterDays());
                }
            }

            if (r.getOrderType() == WorkOrderType.picture_publicity_review.getType()){
                QueryListResultVo queryListResultVo = picturePublicityReview.get(r.getWorkOrderId());
                if (Objects.nonNull(queryListResultVo)) {
                    vo.setTreatmentImages(queryListResultVo.getTreatmentImages());
                    vo.setTreatmentMarkImages(queryListResultVo.getTreatmentMarkImages());
                }
            }

            if (r.getOrderType() == WorkOrderType.content.getType()){
                Integer integer = twWorkOrderReason.get(r.getWorkOrderId());
                if (Objects.nonNull(integer)) {
                    vo.setTwWorkOrderReason(integer);
                }
            }

            //需要再次根据下发状态判断是否能重新审核
            if (r.getOrderType() == WorkOrderType.xiafaprogress.getType()) {
                CfInfoSupplyProgress supplyProgress = supplyProgressMap.get(r.getSupplyProgressId());
                if (supplyProgress != null) {
                    vo.setContent(supplyProgress.getContent());
                    vo.setAttachmentUrls(supplyProgress.getImgUrls());
                    Set<String> sensitiveWords = riskDelegate.getHitWords(supplyProgress.getContent());
                    vo.setSensitiveWord(Joiner.on(",").join(sensitiveWords));
                }
                if (r.isCanReprocess() && r.getSupplyProgressId() > 0) {
                    boolean canReprocess = caseIdTCanReprocess.getOrDefault(r.getCaseId(), false);
                    vo.setCanReprocess(canReprocess);
                }
                //新动态
                long supplyActionId = r.getSupplyActionId();
                CfInfoSupplyAction action = actionMap.get(supplyActionId);
                if (action != null) {
                    vo.setContent(action.getComment());
                    vo.setAttachmentUrls(action.getImgUrls());
                    Set<String> sensitiveWords = riskDelegate.getHitWords(action.getComment());
                    vo.setSensitiveWord(Joiner.on(",").join(sensitiveWords));
                }
            }

            QueryListResultVo v = juanzhanMap.get(r.getCaseId());
            if (v != null) {
                int nowShareCount = Optional.ofNullable(v.getNowShareCount()).orElse(0);
                int nowDonationCount = Optional.ofNullable(v.getNowDonationCount()).orElse(0);
                vo.setShareCount(nowShareCount - Optional.ofNullable(v.getShareCount()).orElse(0));
                vo.setDonationCount(nowDonationCount - Optional.ofNullable(v.getDonationCount()).orElse(0));
                vo.setNowDonationCount(nowDonationCount);
                vo.setNowShareCount(nowShareCount);
            }
            // 获取长时时间未触达提醒
            var followTip = cf2RedissonHandler.get(ReportFollowActionEnum.REPORT_FOLLOW_ACTION_PROMPT.name()
                    + r.getWorkOrderId(), String.class);
            if (org.apache.commons.lang3.StringUtils.isNotBlank(followTip)) {
                vo.setFollowTip(followTip);
            }

            if (CollectionUtils.isNotEmpty(taggingCaseIds) && taggingCaseIds.contains(r.getCaseId())) {
                vo.setRejectToMuch(true);
            }

            return vo;
        }).collect(Collectors.toList());

        promoteBillService.fillPromoteUploadBillInfo(resultList);

        watermarkService.fillOrderListProgressWatermarks(resultList);
        // 非实时资金工单
        delayFinanceWorkOrderService.fillWorkOrderData(resultList);

        return resultList;
    }

    private Map<Long, CommentContentRecord> getTrustDonateMsg(List<QueryListResult> list, QueryListParam queryListParam) {
        if (! queryListParam.getOrderType().contains(String.valueOf(WorkOrderType.fxj_comment_msg_audit.getType()))
                && ! queryListParam.getOrderType().contains(String.valueOf(WorkOrderType.fxj_publicity_msg_audit.getType()))){
            return Maps.newHashMap();
        }
        List<String> commentIds = list.stream()
                .map(QueryListResult::getTrustDonateMsgId)
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(commentIds)){
            return Maps.newHashMap();
        }
        List<Long> trustDonateMsgIds = commentIds.stream()
                .map(Long::parseLong)
                .collect(Collectors.toList());

        log.info("获取放心捐留言信息入参  requestParams: {}",trustDonateMsgIds);
        List<CommentContentRecord> contentRecords = cfTrustDonateAuditClient.findById(trustDonateMsgIds);
        if (CollectionUtils.isEmpty(contentRecords)){
            log.info("获取放心捐留言响应  contentRecords size: {}",contentRecords.size());
            return Maps.newHashMap();
        }

        Map<Long, CommentContentRecord> result = contentRecords.stream().collect(Collectors.toMap(CommentContentRecord::getId, Function.identity()));
        return result;
    }


    private Map<Integer, CrowdFundingVerification> getVerification(List<QueryListResult> list, QueryListParam queryListParam) {
        if (! queryListParam.getOrderType().contains(WorkOrderType.ugc_complaint_verify.getType() + "")){
            return Maps.newHashMap();
        }

        List<String> verificationIds = list.stream()
                .map(QueryListResult::getVerificationId)
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(verificationIds)){
            return Maps.newHashMap();
        }

        List<Long> verificationIdList = verificationIds.stream()
                .map(Long::parseLong)
                .collect(Collectors.toList());

        log.info("获取证实信息入参  requestParams: {}",verificationIdList);
        Response<List<CrowdFundingVerification>> verificationResponse = cfVerificationFeignClient.getVerificationList(verificationIdList);
        if (Objects.isNull(verificationResponse) || Objects.isNull(verificationResponse.getData()) || CollectionUtils.isEmpty(verificationResponse.getData())){
            log.info("获取证实信息响应  verificationResponse: {}",verificationResponse);
            return Maps.newHashMap();
        }

        List<CrowdFundingVerification> data = verificationResponse.getData();
        Map<Integer, CrowdFundingVerification> result = data.stream().collect(Collectors.toMap(CrowdFundingVerification::getId, Function.identity()));
        return result;
    }

    @Override
    public Response<PaginationListVO<QueryListResultVo>> getWorkOrderList(WorkOrderListQueryOldParam wrapParam) {
        //如果是手机号搜索 直接验证用户是否存在
        UserInfoModel userInfoModel;
        long userId = 0;
        QueryListParam queryListParam = wrapParam.getQueryListParam();
        if (StringUtils.isNotBlank(queryListParam.getMobile())) {
            String aesEncryptMobile = encryptDelegate.encrypt(queryListParam.getMobile());
            userInfoModel = userInfoServiceBiz.getUserInfoByCryptoMobile(aesEncryptMobile);
            if (Objects.isNull(userInfoModel)) {
                return createEmptyResult();
            } else {
                userId = userInfoModel.getUserId();
            }
        }
        CfWorkOrderV2IndexSearchParam param = buildOrderListEsParam(wrapParam, userId);
        Response<CfWorkOrderIndexSearchResult> esResponse = workOrderEsDelegate.getWorkOrderIdList(param);
        CfWorkOrderIndexSearchResult esData = esResponse.getData();
        if (esResponse.notOk() || esData == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
        }
        List<CfWorkOrderModel> models = esData.getModels();
        if (CollectionUtils.isEmpty(models)) {
            return createEmptyResult();
        }
        List<Long> orderIdList = models.stream().map(CfWorkOrderModel::getWorkOrderId).collect(Collectors.toList());
        Response<List<QueryListResult>> orderResult = workOrderReadFeignClient.getListByOrderIdListOld(orderIdList);
        if (orderResult.notOk()) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
        }
        List<QueryListResult> orderList = orderResult.getData();
        QueryListParam oldParam = wrapParam.getQueryListParam();
        //按照workorderId倒排
        orderList = orderList.stream().sorted(Comparator.comparing(QueryListResult::getWorkOrderId).reversed()).collect(Collectors.toList());
        List<QueryListResultVo> resultList = getQueryListResultVos(oldParam, orderList);

        PaginationListVO<QueryListResultVo> vo = PaginationListVO.create(resultList, wrapParam.getCurrent(), wrapParam.getPageSize(), esData.getTotal());
        return NewResponseUtil.makeSuccess(vo);
    }

    @Override
    public Map<String, Object> getFundUseAuditInfo4Transfer(long workOrderId, int orderType, int fundUseProgressId) {
        List<QueryListResult> list = Lists.newArrayList();
        QueryListResult one = new QueryListResult();
        one.setFundUseProgressId(fundUseProgressId);
        list.add(one);
        QueryListParam queryListParam = new QueryListParam();
        queryListParam.setOrderType(orderType + "");
        Map<Integer, AdminCfFundUseAuditInfo> fundUseAuditInfo = this.getFundUseAuditInfo(list, queryListParam);
        AdminCfFundUseAuditInfo adminCfFundUseAuditInfo = fundUseAuditInfo.get(fundUseProgressId);
        if (Objects.isNull(adminCfFundUseAuditInfo)) {
            return Collections.emptyMap();
        }
        Response<List<QueryListResult>> orderExtData = workOrderReadFeignClient.getListByOrderIdListOld(Lists.newArrayList(workOrderId));
        if (orderExtData != null && orderExtData.getData() != null) {
            QueryListResult queryListResult = orderExtData.getData().stream().filter(order -> order.getWorkOrderId() == workOrderId).findFirst().orElse(null);
            if (queryListResult != null) {
                adminCfFundUseAuditInfo.setRiskLabelMarkWorkOrderScene(queryListResult.getRiskLabelMarkWorkOrderScene());
            }
        }
        if (StringUtils.isEmpty(adminCfFundUseAuditInfo.getPatientName())) {
            FeignResponse<CfFirsApproveMaterial> response = cfFirstApproveFeignClient.getCfFirstApproveMaterialByCaseId(adminCfFundUseAuditInfo.getCrowdfundingId());
            if (response != null && response.getData() != null) {
                adminCfFundUseAuditInfo.setPatientName(response.getData().getPatientRealName());
            }
        }
        String jsonString = JSON.toJSONString(adminCfFundUseAuditInfo);
        return JSON.parseObject(jsonString, new TypeReference<>(){});
    }

    private CfWorkOrderV2IndexSearchParam buildOrderListEsParam(WorkOrderListQueryOldParam queryListParam, long userId) {
        QueryListParam p = queryListParam.getQueryListParam();
        List<Integer> types = Lists.newArrayList();
        if (StringUtils.isNotEmpty(p.getOrderType())) {
            types = Arrays.stream(p.getOrderType().split(",")).map(Integer::valueOf).collect(Collectors.toList());
        }

        WorkOrderTableParam woTableParam = new WorkOrderTableParam();
        if (p.getWorkOrderId() > 0) {
            woTableParam.setIds(Lists.newArrayList(p.getWorkOrderId()));
        }
        if (p.getCaseId() > 0) {
            long caseId = p.getCaseId();
            woTableParam.setCaseIds(Lists.newArrayList(caseId));
        }
        if (p.getOperId() > 0) {
            woTableParam.setOperatorIds(Lists.newArrayList(p.getOperId()));
        }
        if (p.getHandleResult() >= 0) {
            woTableParam.setHandleResult(Lists.newArrayList(p.getHandleResult()));
        }
        if (CollectionUtils.isNotEmpty(types)){
            woTableParam.setOrderTypes(types);
        }
        long startCreateTime = promoteStrTime(p.getStartCreateTime());
        if (startCreateTime > 0) {
            woTableParam.setCreateStartTime(startCreateTime);
        }
        long endCreateTime = promoteStrTime(p.getEndCreateTime());
        if (endCreateTime > 0) {
            woTableParam.setCreateEndTime(endCreateTime);
        }
        long startHandleTime = promoteStrTime(p.getStartHandleTime());
        if (startHandleTime > 0) {
            woTableParam.setHandleStartTime(startHandleTime);
        }
        long endHandleTime = promoteStrTime(p.getEndHandleTime());
        if (endHandleTime > 0) {
            woTableParam.setHandleEndTime(endHandleTime);
        }
        long startDoneTime = promoteStrTime(p.getStartDoneTime());
        if (startDoneTime > 0) {
            woTableParam.setFinishStartTime(startDoneTime);
        }
        long endDoneTime = promoteStrTime(p.getEndDoneTime());
        if (endDoneTime > 0) {
            woTableParam.setFinishEndTime(endDoneTime);
        }

        CrowdfundingInfoTableParam crowdfundingInfoTableParam = new CrowdfundingInfoTableParam();
        if (userId > 0) {
            crowdfundingInfoTableParam.setUserId(userId);
        }

        if (StringUtils.isNotBlank(p.getAmount())) {
            WorkOrderExtTableParam amount = WorkOrderExtTableParam.create("amount", p.getAmount());
            List<WorkOrderExtTableParam> workOrderExtTableParamList = queryListParam.getQueryListParam().getWorkOrderExtTableParamList();
            if (CollectionUtils.isEmpty(workOrderExtTableParamList)) {
                queryListParam.getQueryListParam().setWorkOrderExtTableParamList(ImmutableList.of(amount));
            } else {
                workOrderExtTableParamList.add(amount);
            }
        }

        CfWorkOrderV2IndexSearchParam searchParam = new CfWorkOrderV2IndexSearchParam();
        searchParam.setWoTableParam(woTableParam);
        searchParam.setWorkOrderExtTableParamList(queryListParam.getQueryListParam().getWorkOrderExtTableParamList());
        searchParam.setCiTableParam(crowdfundingInfoTableParam);
        searchParam.setFrom((queryListParam.getCurrent() - 1) * queryListParam.getPageSize());
        searchParam.setSize(queryListParam.getPageSize());
        return searchParam;
    }

    private static long promoteStrTime(String str) {
        if (StringUtils.isBlank(str)) {
            return 0;
        }
        try {
            return dateTimeFormat.parse(str).getTime();
        } catch (ParseException e) {
            log.error("promoteStrTime error str {}", str, e);
        }
        return 0;
    }

    private Response<PaginationListVO<QueryListResultVo>> createEmptyResult() {
        PaginationListVO<QueryListResultVo> result = PaginationListVO.createEmpty();
        return NewResponseUtil.makeSuccess(result);
    }

    private Map<Long, CfSensitiveWordRecord> getUgcList(List<QueryListResult> list, QueryListParam queryListParam) {

        if (!queryListParam.getOrderType().contains(WorkOrderType.ugcpinglun.getType() + "")) {
            return Maps.newHashMap();
        }

        List<Long> ids = list.stream()
                .filter(r -> org.apache.commons.lang3.StringUtils.isNotBlank(r.getWordId()))
                .filter(r -> AdminUGCTask.Content.getByCode(Integer.valueOf(r.getContentType())) != AdminUGCTask.Content.PINGYI)
                .map(r -> Long.valueOf(r.getWordId()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(ids)) {
            return Maps.newHashMap();
        }

        List<CfSensitiveWordRecord> records = sensitiveWordRecordBiz.selectByIds(ids);

        Map<Long, CfSensitiveWordRecord> map = records.stream().collect(Collectors.toMap(CfSensitiveWordRecord::getId, Function.identity()));

        return map;
    }


    private Map<Long, CommentVO> getPingyiList(List<QueryListResult> list, QueryListParam queryListParam) {
        if (!queryListParam.getOrderType().contains(WorkOrderType.ugcpinglun.getType() + "")) {
            return Maps.newHashMap();
        }

        List<Long> ids = list.stream()
                .filter(r -> org.apache.commons.lang3.StringUtils.isNotBlank(r.getWordId()))
                .filter(r -> AdminUGCTask.Content.getByCode(Integer.valueOf(r.getContentType())) == AdminUGCTask.Content.PINGYI)
                .map(r -> Long.valueOf(r.getWordId()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(ids)) {
            return Maps.newHashMap();
        }

        Response<List<CommentVO>> response = discussionCommentClient.findById(ids);
        List<CommentVO> commentVOS = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(Lists.newArrayList());

        return commentVOS.stream().collect(Collectors.toMap(CommentVO::getId, Function.identity()));
    }


    private Map<Integer, CrowdFundingProgress> getProgressList(List<QueryListResult> list, QueryListParam queryListParam) {

        if (!queryListParam.getOrderType().contains(WorkOrderType.ugcprogress.getType() + "") || CollectionUtils.isEmpty(list)) {
            return Maps.newHashMap();
        }
        Map<Integer, CrowdFundingProgress> map = adminCrowdFundingProgressBiz.getMapByIds(list.stream()
                .filter(item -> org.apache.commons.lang3.StringUtils.isNotBlank(item.getWordId()))
                .map(r -> Integer.valueOf(r.getWordId())).collect(Collectors.toList()));

        return map;
    }

    private Map<Long, CfInfoSupplyAction> getActionMap(List<QueryListResult> list, QueryListParam queryListParam) {

        if (!queryListParam.getOrderType().contains(WorkOrderType.xiafaprogress.getType() + "") || CollectionUtils.isEmpty(list)) {
            return Maps.newHashMap();
        }

        return cfSupplyActionService.getActionMap(list.stream().filter(r -> r.getSupplyActionId() > 0).map(QueryListResult::getSupplyActionId).collect(Collectors.toList()));

    }

    private Map<Integer, QueryListResultVo> getJuanzhuan(List<QueryListResult> list, QueryListParam queryListParam) {
        if (!(queryListParam.getOrderType().contains(WorkOrderType.d0_1v1.getType() + "")
                || queryListParam.getOrderType().contains(WorkOrderType.d1_1v1.getType() + "")
                || queryListParam.getOrderType().contains(WorkOrderType.d2_1v1.getType() + "")
                || queryListParam.getOrderType().contains(WorkOrderType.d0_1v1_1.getType() + "")
                || queryListParam.getOrderType().contains(WorkOrderType.d0_1v1_tzb.getType() + ""))
                || CollectionUtils.isEmpty(list)) {
            return Maps.newHashMap();
        }
        List<Integer> caseIds = list.stream().map(QueryListResult::getCaseId).collect(Collectors.toList());

        Response<Map<Integer, WorkOrderVO>> response = juanzhuanWorkOrderClient.getD0ShareAndDonation(caseIds);

        Map<Integer, WorkOrderVO> map = response.getData();

        Map<Integer, CfInfoStat> statMap = crowdfundingDelegate.mapByIds(caseIds);

        Map<Integer, QueryListResultVo> result = Maps.newHashMap();

        list.stream().forEach(r -> {
            QueryListResultVo vo = new QueryListResultVo();
            WorkOrderVO w = map.get(r.getCaseId());
            if (w != null) {
                vo.setShareCount(w.getShareCount());
                vo.setDonationCount(w.getDonationCount());
            }
            CfInfoStat s = statMap.get(r.getCaseId());
            if (s != null) {
                vo.setNowShareCount(s.getShareCount());
                vo.setNowDonationCount(s.getDonationCount());
            }
            result.put(r.getCaseId(), vo);
        });

        return result;
    }


    /**
     * 希望树动态
     */
    private Map<Long, HopeTreeStateRecordDO> getHopeTreeState(List<QueryListResult> list, QueryListParam queryListParam) {
        if (!queryListParam.getOrderType().contains(WorkOrderType.ugcprogress.getType() + "")) {
            return Maps.newHashMap();
        }

        List<Long> ids = list.stream()
                .filter(r -> org.apache.commons.lang3.StringUtils.isNotBlank(r.getHopeTreeStateId()))
                .filter(r -> AdminUGCTask.Content.getByCode(Integer.valueOf(r.getContentType())) == AdminUGCTask.Content.HOPE_TREE_STATE)
                .map(r -> Long.valueOf(r.getHopeTreeStateId()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(ids)) {
            return Maps.newHashMap();
        }

        Response<List<HopeTreeStateRecordDO>> records = hopeTreeClient.getListById(ids);
        if (records.notOk() || CollectionUtils.isEmpty(records.getData())) {
            return Maps.newHashMap();
        }

        Map<Long, HopeTreeStateRecordDO> map = records.getData().stream().collect(Collectors.toMap(HopeTreeStateRecordDO::getId, Function.identity()));

        return map;
    }


    /**
     * 资金用途
     *
     * @param list
     * @param queryListParam
     * @return
     */
    private Map<Integer, AdminCfFundUseAuditInfo> getFundUseAuditInfo(List<QueryListResult> list, QueryListParam queryListParam) {
        if (!queryListParam.getOrderType().contains(WorkOrderType.funduseshenhe.getType()+"") && !queryListParam.getOrderType().contains(WorkOrderType.report_split_draw.getType()+"")){
            return Maps.newHashMap();
        }

        List<Integer> fundUseProgressIds = list.stream()
                .filter(r -> Objects.nonNull(r.getFundUseProgressId()) && r.getFundUseProgressId() > 0)
                .map(WorkOrderVO::getFundUseProgressId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(fundUseProgressIds)){
            return Maps.newHashMap();
        }

        List<AdminCrowdfundingProgress> crowdfundingProgresses = cfFundUseProgressBiz.selectByProgressIdList(fundUseProgressIds);
        if(CollectionUtils.isEmpty(crowdfundingProgresses)) {
            return Maps.newHashMap();
        }

        Set<Integer> caseIdSet = crowdfundingProgresses.stream()
                .map(AdminCrowdfundingProgress::getCrowdfundingId)
                .collect(Collectors.toSet());

        Map<Integer, CrowdfundingAuthor> crowdfundingAuthorMap = adminCrowdfundingAuthorBiz.getByInfoIdList(new ArrayList<>(caseIdSet));
        Map<Integer, List<CrowdfundingReport>> reportListMap = adminCrowdfundingReportBiz.getByInfoIdsV2(new ArrayList<>(caseIdSet))
                .stream()
                .filter(item -> item.getDealStatus() == CaseReportDealStatus.HANDLEING.getValue()
                        || item.getDealStatus() == CaseReportDealStatus.FINISH.getValue())
                .collect(Collectors.groupingBy(CrowdfundingReport::getActivityId));

        Map<Integer, AdminCfFundUseAuditInfo> map = new HashMap<>();

        for (AdminCrowdfundingProgress adminCrowdfundingProgress : crowdfundingProgresses) {

            CrowdfundingAuthor author = crowdfundingAuthorMap.get(adminCrowdfundingProgress.getCrowdfundingId());
            List<CrowdfundingReport> crowdfundingReports = reportListMap.get(adminCrowdfundingProgress.getCrowdfundingId());

            AdminCfFundUseAuditInfo adminCfFundUseAuditInfo = new AdminCfFundUseAuditInfo();
            adminCfFundUseAuditInfo.setProgressId(adminCrowdfundingProgress.getId());
            adminCfFundUseAuditInfo.setCrowdfundingId(adminCrowdfundingProgress.getCrowdfundingId());
            adminCfFundUseAuditInfo.setPatientName(Objects.nonNull(author) && org.apache.commons.lang3.StringUtils.isNotEmpty(author.getName()) ? author.getName() : "");
            adminCfFundUseAuditInfo.setFundUseContent(adminCrowdfundingProgress.getContent());
            adminCfFundUseAuditInfo.setFundUseImageMaterial(adminCrowdfundingProgress.getImageUrls());
            adminCfFundUseAuditInfo.setHasReport(CollectionUtils.isNotEmpty(crowdfundingReports));
            Timestamp timestamp = StringUtils.isNotEmpty(adminCrowdfundingProgress.getDrawFinishTimeStr()) ? new Timestamp(DateUtil.stringToDate(adminCrowdfundingProgress.getDrawFinishTimeStr(), "yyyy-MM-dd HH:mm:ss").getTime()) : null;
            adminCfFundUseAuditInfo.setDrawCashTime(timestamp);
            adminCfFundUseAuditInfo.setFundUseRejectedReason(adminCrowdfundingProgress.getFundUseRejectedReason());

            map.put(adminCrowdfundingProgress.getId(), adminCfFundUseAuditInfo);

        }

        return map;
    }

    private Map<Long, QueryListResultVo> getPicturePublicityReview(List<QueryListResult> list, QueryListParam queryListParam) {
        if (!queryListParam.getOrderType().contains(String.valueOf(WorkOrderType.picture_publicity_review.getType()))){
            return Maps.newHashMap();
        }
        Map<Long, QueryListResultVo> map = new HashMap<>();
        List<Long> workOrderIdList = list.stream()
                .map(QueryListResult::getWorkOrderId)
                .collect(Collectors.toList());

        Map<Long, List<String>> imagePublicByWorKOrderIdList = imagePublicWorkOrderService.getImagePublicByWorKOrderIdList(workOrderIdList);
        Map<Long, List<String>> markImagePublicByWorKOrderIdList = imagePublicWorkOrderService.getMarkImagePublicByWorKOrderIdList(workOrderIdList);
        for (Long workOrderId : workOrderIdList) {
            QueryListResultVo vo = new QueryListResultVo();
            List<String> imageList = imagePublicByWorKOrderIdList.get(workOrderId);
            List<String> markImageList = markImagePublicByWorKOrderIdList.get(workOrderId);
            vo.setTreatmentImages(JSONObject.toJSONString(imageList));
            vo.setTreatmentMarkImages(JSONObject.toJSONString(markImageList));
            map.put(workOrderId, vo);
        }
        return map;
    }

    private Map<Long, Integer> getTwWorkOrderReason(List<QueryListResult> list, QueryListParam queryListParam) {
        Map<Long, Integer> map = new HashMap<>();
        List<Long> workOrderIdList = list.stream()
                .filter(f -> f.getOrderType() == WorkOrderType.content.getType())
                .map(QueryListResult::getWorkOrderId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(workOrderIdList)) {
            return map;
        }
        List<TwModifyRecordDO> twModifyRecordDOS = adminTwModifyDao.selectByWorkOrderIdList(workOrderIdList);
        if (CollectionUtils.isEmpty(twModifyRecordDOS)) {
            return map;
        }
        Map<Long, TwModifyRecordDO> modifyRecordDOMap = twModifyRecordDOS.stream()
                .collect(Collectors.toMap(TwModifyRecordDO::getWorkOrderId, Function.identity(), (x, y) -> x));
        for (Long workOrderId : workOrderIdList) {
            TwModifyRecordDO twModifyRecordDO = modifyRecordDOMap.get(workOrderId);
            map.put(workOrderId, Objects.nonNull(twModifyRecordDO) ? 1 :0);
        }
        return map;
    }

    private Map<Long, CfAmountReasonableTaskWorkOrder> getAmountReasonable(List<QueryListResult> list, QueryListParam queryListParam) {
        if (!queryListParam.getOrderType().contains(String.valueOf(WorkOrderType.fund_use_amount_reasonable.getType()))){
            return Maps.newHashMap();
        }
        List<Long> workOrderIdList = list.stream()
                .map(QueryListResult::getWorkOrderId)
                .collect(Collectors.toList());
        List<CfAmountReasonableTaskWorkOrder> reasonableTaskWorkOrderList = amountReasonableBiz.selectByWorkOrderIdList(workOrderIdList);
        if (CollectionUtils.isEmpty(reasonableTaskWorkOrderList)) {
            return new HashMap<>();
        }
        return reasonableTaskWorkOrderList.stream()
                .collect(Collectors.toMap(CfAmountReasonableTaskWorkOrder::getWorkOrderId, Function.identity(), (x, y) -> x));
    }

}
