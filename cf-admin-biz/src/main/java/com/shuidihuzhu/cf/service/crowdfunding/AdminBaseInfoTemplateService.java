package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfBaseInfoTemplatizeBiz;
import com.shuidihuzhu.cf.client.feign.CaseInfoFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.BaseInfoTemplateConst;
import com.shuidihuzhu.cf.model.admin.common.PageReturn;
import com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplateOperatorHistory;
import com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplatize;
import com.shuidihuzhu.cf.vo.crowdfunding.CfBaseInfoTemplatizeVo;
import com.shuidihuzhu.client.model.CommonPageModel;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.admin.PageUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> Ahrievil
 * @date : 2018/9/10 19:36
 */
@Service
public class AdminBaseInfoTemplateService {

    private static Pattern PATTERN = Pattern.compile ("##.{4,5}##");

    @Autowired
    private AdminCfBaseInfoTemplatizeBiz adminCfBaseInfoTemplatizeBiz;
    @Autowired
    private CaseInfoFeignClient caseInfoClient;

    public Response getList(String context, int contentType, int channelType, int relationType, int current, int pageSize,
                            int useScene) {

        List<Integer> ids = null;
        if (useScene >= 0) {
            FeignResponse<List<Integer>> allTemplateIds =  caseInfoClient.selectTemplateIdsByUseScene(useScene);
            if (allTemplateIds != null && CollectionUtils.isNotEmpty(allTemplateIds.getData())) {
                ids = allTemplateIds.getData();
            }
        }

        List<CfBaseInfoTemplatize> cfBaseInfoTemplatizes = adminCfBaseInfoTemplatizeBiz
                .selectByPage(context, contentType, relationType, channelType, current, pageSize, ids);

        fillUseSceneMappings(cfBaseInfoTemplatizes);
        Map<String, Object> result = Maps.newHashMap();
        result.put("list", cfBaseInfoTemplatizes);
        result.put("pagination", PageUtil.transform2PageMap(cfBaseInfoTemplatizes));
        return NewResponseUtil.makeSuccess(result);
    }

    public Response detail(long id) {
        CfBaseInfoTemplatize cfBaseInfoTemplatize = adminCfBaseInfoTemplatizeBiz.selectById(id);
        if (Objects.isNull(cfBaseInfoTemplatize)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        fillUseSceneMappings(Lists.newArrayList(cfBaseInfoTemplatize));

        List<String> placeHolder = this.getPlaceHolder(cfBaseInfoTemplatize.getContent());
        return NewResponseUtil.makeSuccess(new CfBaseInfoTemplatizeVo(cfBaseInfoTemplatize, placeHolder));
    }

    private void fillUseSceneMappings(List<CfBaseInfoTemplatize> templates) {

        if (CollectionUtils.isEmpty(templates)) {
            return;
        }
        List<Integer> ids = templates.stream().map(CfBaseInfoTemplatize::getId).collect(Collectors.toList());

        FeignResponse<List<CfBaseInfoTemplatize.TemplateUseSceneMapping>> result =  caseInfoClient.selectUseMappingByTemplateIds(ids);
        if (result == null || CollectionUtils.isEmpty(result.getData())) {
            return;
        }

        Map<Integer, List<Integer>> templateMappings = Maps.newHashMap();
        for (CfBaseInfoTemplatize.TemplateUseSceneMapping sceneMapping : result.getData()) {
            List<Integer> useScenes = templateMappings.get(sceneMapping.getTemplateId());
            if (useScenes == null) {
                useScenes = Lists.newArrayList();
            }
            useScenes.add(sceneMapping.getUseScene());
            templateMappings.put(sceneMapping.getTemplateId(), useScenes);
        }

        for (CfBaseInfoTemplatize templatize : templates) {
            templatize.setUseScenes(templateMappings.get(templatize.getId()));
        }
    }

    public Response inputParamCheck(CfBaseInfoTemplatize cfBaseInfoTemplatize) {
        if (Objects.isNull(cfBaseInfoTemplatize)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        String content = cfBaseInfoTemplatize.getContent();
        if (StringUtils.isBlank(content)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        BaseInfoTemplateConst.ChannelType channelType = BaseInfoTemplateConst.ChannelType.getByCode(cfBaseInfoTemplatize.getChannelType());
        if (Objects.isNull(channelType)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (StringUtils.isNotBlank(cfBaseInfoTemplatize.getApplicableDiseases())) {
            List<String> diseases = Stream.of(cfBaseInfoTemplatize.getApplicableDiseases().split("，")).collect(Collectors.toList());
            // 是否存在重复的疾病
            if (diseases.size() != diseases.stream().distinct().count()) {
                return NewResponseUtil.makeError(AdminErrorCode.DISEASE_CAN_NOT_REPEATED);
            }
        }
        List<String> placeHolder = this.getPlaceHolder(cfBaseInfoTemplatize.getContent());
        if (channelType == BaseInfoTemplateConst.ChannelType.BASE_INFO) {
            BaseInfoTemplateConst.ContentType contentType = BaseInfoTemplateConst.ContentType.getByCode(cfBaseInfoTemplatize.getContentType());
            if (Objects.isNull(contentType)) {
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
            }

            // 图文模版需要有使用场景
            if (CollectionUtils.isEmpty(cfBaseInfoTemplatize.getUseScenes())) {
                return NewResponseUtil.makeError(AdminErrorCode.NEED_HAVE_USE_SCENE);
            }

            switch (contentType) {
                case TITLE:
//                    TODO
//                    if (content.length() > 25) {
//                        return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
//                    }
                    break;
                case CONTENT:
                    if (content.length() > 3000) {
                        return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
                    }
                    if (!placeHolder.contains(BaseInfoTemplateConst.DISEASE)) {
                        return NewResponseUtil.makeError(AdminErrorCode.CONTENT_TEMPLATE_NO_DISEASE);
                    }
                    break;
                default:
            }
            if (cfBaseInfoTemplatize.getMinAge() > cfBaseInfoTemplatize.getMaxAge()) {
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
            }
        } else if (channelType == BaseInfoTemplateConst.ChannelType.SHARE) {
            if (content.length() > 3000) {
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
            }
        }
        List<Integer> relationshipCodeList = Stream.of(BaseInfoTemplateConst.CfBaseInfoRelationshipEnum.values())
                .map(BaseInfoTemplateConst.CfBaseInfoRelationshipEnum::getCode).collect(Collectors.toList());
        if (! relationshipCodeList.contains(cfBaseInfoTemplatize.getRelationType())) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<String> staticPlaceHolder = BaseInfoTemplateConst.getPlaceHolder();
        if (! staticPlaceHolder.containsAll(placeHolder)) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_BASE_INFO_TEMPLATE_CONTENT_ERROR);
        }
        return null;
    }

    public Response addOrUpdate(CfBaseInfoTemplatize cfBaseInfoTemplatize) {
        CfBaseInfoTemplateOperatorHistory operatorHistory = new CfBaseInfoTemplateOperatorHistory();
        operatorHistory.setOperatorId(cfBaseInfoTemplatize.getOperatorId());
        operatorHistory.setOperatorName(cfBaseInfoTemplatize.getOperatorName());

        if (cfBaseInfoTemplatize.getId() == 0) {
            // 插入的逻辑
            adminCfBaseInfoTemplatizeBiz.insertOne(cfBaseInfoTemplatize);
            operatorHistory.setRemark("新建模版");
        } else {
            // 更新的逻辑
            operatorHistory.setRemark("更新模版");
            adminCfBaseInfoTemplatizeBiz.update(cfBaseInfoTemplatize);
        }
        operatorHistory.setCfBaseTemplateId(cfBaseInfoTemplatize.getId());
        adminCfBaseInfoTemplatizeBiz.insertOperatorHistory(operatorHistory);
        caseInfoClient.updateTemplateUseSceneMapping(cfBaseInfoTemplatize.getId(), cfBaseInfoTemplatize.getUseScenes());
        return NewResponseUtil.makeSuccess(null);
    }

    public Response delete(long id) {
        adminCfBaseInfoTemplatizeBiz.delete(id);
        return NewResponseUtil.makeSuccess(null);
    }

    public Response getRelationInfo() {
        Map<Integer, String> relationMap = Stream.of(BaseInfoTemplateConst.CfBaseInfoRelationshipEnum.values()).collect(
                Collectors.toMap(BaseInfoTemplateConst.CfBaseInfoRelationshipEnum::getCode,
                        BaseInfoTemplateConst.CfBaseInfoRelationshipEnum::getWord));
        return NewResponseUtil.makeSuccess(relationMap);
    }

    private List<String> getPlaceHolder(String content) {
        Matcher match = PATTERN.matcher (content);
        List<String> placeHolder = Lists.newArrayList();
        while (match.find ()) {
            placeHolder.add(match.group());
        }
        return placeHolder;
    }

    public static void main(String[] args) {
        String string = "我叫##患者姓名##，##患者家乡##人，我有一个好丈夫好孩子，我们一家人本平凡和谐地过着日子，勤勤恳恳，谁知天有不测风云，##确诊时间##，在##看病医院##，我被确诊为了##疾病名称##，作为家里的半边天，我却倒下了，除了配合治疗我却什么都做不了，我知道丈夫和孩子身上扛着巨大的压力，看到他们为我跑前跑后，我真的心里满怀愧疚，恨自己身体不争气没能让家里享福，事大力薄，我真切需要得到广大爱心人士的关心和援助，前期家里已花费了##已花费金额##元，医生说后续的治疗还需要##筹款金额##，这个数字对我们来说实在压力有点大，亲戚朋友都在尽力帮忙但还是差很多，丈夫孩子和我一起加油鼓劲，更加坚定了我要治好病的决心，也不忍心两家父母这么大年纪还在为我担惊受怕，我要尽快把病治好，继续承担起家庭的责任。在这里我真诚地向大家求助，希望好心人可以施以援手，给我力量与活下去的勇气，就是一个转发一次鼓励对我也是莫大的支持！谢谢各位好心人，谢谢各位的无私帮助与恩德，救命之恩我们会牢记心中，没齿不忘！";
        Pattern pattern = Pattern.compile ("##.{4,5}##");
        Matcher match = pattern.matcher (string);
        List<String> placeHolder = Lists.newArrayList();
        while (match.find ()) {
            placeHolder.add(match.group());
        }
        List<String> staticPlaceHolder = BaseInfoTemplateConst.getPlaceHolder();
        System.out.println(staticPlaceHolder.containsAll(placeHolder));
    }

    public Response<CommonPageModel<CfBaseInfoTemplatize>> get1v1TemplateList(String context, Integer relationType, String diseaseName, Integer age, int current, int pageSize) {
        List<Integer> ids = Lists.newArrayList();
        CommonPageModel<CfBaseInfoTemplatize> commonPageModel = new CommonPageModel<>();
        // 查询1v1代录入模版id
        FeignResponse<List<Integer>> allTemplateIds =  caseInfoClient.selectTemplateIdsByUseScene(1);
        if (allTemplateIds != null && CollectionUtils.isNotEmpty(allTemplateIds.getData())) {
            ids = allTemplateIds.getData();
        }

        int contentType = BaseInfoTemplateConst.ContentType.CONTENT.getCode();
        int channelType = BaseInfoTemplateConst.ChannelType.BASE_INFO.getCode();
        int count = adminCfBaseInfoTemplatizeBiz.get1v1TemplateCount(context, contentType, relationType, channelType, ids, diseaseName, age);
        if (count == 0) {
            return NewResponseUtil.makeSuccess(commonPageModel);
        }
        commonPageModel.setCount(count);

        List<CfBaseInfoTemplatize> cfBaseInfoTemplateList = adminCfBaseInfoTemplatizeBiz
                .get1v1TemplateList(context, contentType, relationType, channelType, ids, diseaseName, age, (current - 1) * pageSize, pageSize);

        fillUseSceneMappings(cfBaseInfoTemplateList);
        commonPageModel.setList(cfBaseInfoTemplateList);
        return NewResponseUtil.makeSuccess(commonPageModel);
    }
}
