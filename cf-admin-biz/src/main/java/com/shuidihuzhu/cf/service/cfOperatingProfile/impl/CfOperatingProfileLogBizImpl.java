package com.shuidihuzhu.cf.service.cfOperatingProfile.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.CfMaterialVerityHistoryBiz;
import com.shuidihuzhu.cf.dao.cfOperatingProfile.CfOperatingProfileLogDao;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileLog;
import com.shuidihuzhu.cf.service.cfOperatingProfile.CfOperatingProfileLogBiz;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class CfOperatingProfileLogBizImpl implements CfOperatingProfileLogBiz {

    @Autowired
    private CfOperatingProfileLogDao profileLogDao;
    @Autowired
    private CfMaterialVerityHistoryBiz verityHistoryBiz;

    @Override
    public void insertOperateLog(int userId, int businessId, int operateType, String comment) {
        CfOperatingProfileLog log = new CfOperatingProfileLog();
        log.setBusinessId(businessId);
        log.setOperateId(userId);
        log.setOperateType(operateType);

        CfOperatingProfileLog.OperateType type = CfOperatingProfileLog.OperateType.valueOfCode(operateType);
        log.setOperateComment(StringUtils.isNotBlank(comment) ? comment : (type == null ? "" : type.getDesc()));

        insertOperateRecord(Lists.newArrayList(log));
    }

    public void insertOperateRecord(List<CfOperatingProfileLog> logs) {
        log.info("配置文件操作日志入库.msg:{}", logs);

        if (CollectionUtils.isEmpty(logs)) {
            return;
        }
        for (CfOperatingProfileLog profileLog : logs) {
            profileLog.setOperateName(verityHistoryBiz.queryOperateDetail(profileLog.getOperateId()));
        }
        profileLogDao.insertOperateRecord(logs);
    }

    public List<CfOperatingProfileLog.CfOperatingProfileLogView> selectLogByLogType(long businessId) {

        List<CfOperatingProfileLog> profileLogs = profileLogDao.selectLogByLogType(businessId);

        List<CfOperatingProfileLog.CfOperatingProfileLogView> viewList = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(profileLogs)) {
           for (CfOperatingProfileLog log : profileLogs) {
               viewList.add(new CfOperatingProfileLog.CfOperatingProfileLogView(
                       log.getOperateName(), log.getOperateComment(), log.getCreateTime().getTime()));
           }
        }

        return viewList;
    }

}
