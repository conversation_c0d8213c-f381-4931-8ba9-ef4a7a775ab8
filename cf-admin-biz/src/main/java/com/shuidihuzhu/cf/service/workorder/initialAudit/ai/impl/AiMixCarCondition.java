package com.shuidihuzhu.cf.service.workorder.initialAudit.ai.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.material.model.CfPropertyInsuranceInfoModel;
import com.shuidihuzhu.cf.dao.crowdfunding.CfAiMaterialsDao;
import com.shuidihuzhu.cf.model.crowdfunding.ai.AiConditionEnum;
import com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterials;
import com.shuidihuzhu.cf.model.crowdfunding.ai.LayOutField;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditSearchService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.ai.AiCondition;
import com.shuidihuzhu.cf.vo.approve.CreditInfoVO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2021/2/26
 */
@Slf4j
@Service("aiMixCar")
public class AiMixCarCondition implements AiCondition {

    @Autowired
    private CfAiMaterialsDao cfAiMaterialsDao;

    @Autowired
    private InitialAuditSearchService initialAuditSearchService;


    @Override
    public boolean check(int caseId, String inputValue) {

        CreditInfoVO creditInfoVO = initialAuditSearchService.getCreditInfoVO(caseId);
        if (Objects.isNull(creditInfoVO) || Objects.isNull(creditInfoVO.getCarProperty())) {
            log.info("aiMixCarCondition creditInfoVO is null {}", creditInfoVO);
            return false;
        }
        CreditInfoVO.CarPropertyInfoVO carProperty = creditInfoVO.getCarProperty();
        Integer totalCount = carProperty.getTotalCount();

        AiContentWorkOrderCar contentWorkOrderCar = covertAiContentWorkOrderCar(caseId, carProperty);
        if (Objects.isNull(contentWorkOrderCar)) {
            log.info("aiMixCarCondition contentWorkOrderCar is null {}", caseId);
            return false;
        }
        String hasCarDesc = contentWorkOrderCar.getHasCarDesc();
        String hasCar = contentWorkOrderCar.getHasCarCount();
        Integer carCount = contentWorkOrderCar.getCarCount();
        String hasCarValue = contentWorkOrderCar.getHasCarValue();
        boolean inRange = contentWorkOrderCar.isInRange();

        // 有无描述车产 : 无，直接过审
        if (StringUtils.equals(AiConditionEnum.wu.getStringCode(), hasCarDesc)) {
            return false;
        }
        if (StringUtils.equals(hasCar, AiConditionEnum.wu.getStringCode()) && StringUtils.equals(hasCarValue, AiConditionEnum.wu.getStringCode())) {
            return totalCount == 0;
        }
        if (StringUtils.equals(hasCar, AiConditionEnum.wu.getStringCode()) && StringUtils.equals(hasCarValue, AiConditionEnum.you.getStringCode())) {
            if (Objects.equals(totalCount, 0)) {
                return true;
            }
            return !inRange;
        }
        if (StringUtils.equals(hasCar, AiConditionEnum.you.getStringCode()) && StringUtils.equals(hasCarValue, AiConditionEnum.wu.getStringCode())) {
            return !Objects.equals(totalCount, carCount);
        }
        if (StringUtils.equals(hasCar, AiConditionEnum.you.getStringCode()) && StringUtils.equals(hasCarValue, AiConditionEnum.you.getStringCode())) {
            if (!Objects.equals(totalCount,  carCount)) {
                return true;
            }
            return !inRange;
        }

        return false;
    }

    @Data
    public static class AiContentWorkOrderCar {
        private String hasCarDesc;
        private String hasCarCount;
        private int carCount;
        private String hasCarValue;
        private double carValue;
        private boolean inRange;
    }

    private AiContentWorkOrderCar covertAiContentWorkOrderCar(int caseId, CreditInfoVO.CarPropertyInfoVO carProperty) {
        CfAiMaterials z = cfAiMaterialsDao.getByCaseId(caseId, CfAiMaterials.zType);
        List<LayOutField> layOutFields = Optional.ofNullable(z)
                .map(CfAiMaterials::getFields)
                .orElse(Lists.newArrayList());
        if (CollectionUtils.isEmpty(layOutFields)) {
            return null;
        }
        Map<String, String> collect = layOutFields.stream()
                .collect(Collectors.toMap(LayOutField::getFieldKey, LayOutField::getFieldValue, (x, y) -> x));
        String jsonString = JSONObject.toJSONString(collect);
        AiContentWorkOrderCar contentWorkOrderCar = JSONObject.parseObject(jsonString, AiContentWorkOrderCar.class);
        if (Objects.isNull(contentWorkOrderCar)) {
            return null;
        }
        Integer totalValueUserDefined = carProperty.getTotalValueUserDefined();
        if (Objects.nonNull(totalValueUserDefined) && totalValueUserDefined > 0) {
            contentWorkOrderCar.setInRange(contentWorkOrderCar.getCarValue() <= Double.valueOf(totalValueUserDefined));
            return contentWorkOrderCar;
        }
        if (Objects.isNull(carProperty.getTotalValueRangeType())) {
            return contentWorkOrderCar;
        }
        CfPropertyInsuranceInfoModel.CarValueRange carValueRange = CfPropertyInsuranceInfoModel.CarValueRange.valueOfCode(carProperty.getTotalValueRangeType());
        if (Objects.isNull(carValueRange)) {
            return contentWorkOrderCar;
        }
        Double from = Double.valueOf(carValueRange.getFrom());
        Double to = Double.valueOf(carValueRange.getTo());
        contentWorkOrderCar.setInRange(contentWorkOrderCar.getCarValue() >= from && contentWorkOrderCar.getCarValue() <= to);
        return contentWorkOrderCar;
    }
}
