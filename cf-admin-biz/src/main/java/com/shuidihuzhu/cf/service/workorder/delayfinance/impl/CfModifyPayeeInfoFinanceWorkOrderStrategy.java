package com.shuidihuzhu.cf.service.workorder.delayfinance.impl;

import com.shuidihuzhu.cf.finance.model.vo.CfChangePayeeInfoVo;
import com.shuidihuzhu.cf.service.workorder.delayfinance.delegate.IFinanceWorkOrderDelegate;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-06-15 3:38 PM
 **/
@Service
public class CfModifyPayeeInfoFinanceWorkOrderStrategy extends AbstractFinanceWorkOrderStrategy<CfChangePayeeInfoVo> {

    @Resource
    private IFinanceWorkOrderDelegate iFinanceWorkOrderDelegate;

    @Override
    public int orderType() {
        return WorkOrderType.modify_payee_info.getType();
    }

    @Override
    public List<CfChangePayeeInfoVo> getBusinessExt(List<Long> financeBusinessIds) {
        return iFinanceWorkOrderDelegate.getChangePayeeInfoVo(financeBusinessIds);
    }
}
