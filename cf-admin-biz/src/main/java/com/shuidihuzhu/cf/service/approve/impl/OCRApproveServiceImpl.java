package com.shuidihuzhu.cf.service.approve.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.shuidihuzhu.alps.feign.python.ExtractCFFeignClient;
import com.shuidihuzhu.cf.admin.delegate.HospitalDelegate;
import com.shuidihuzhu.cf.admin.delegate.OCRBaiDuDelegate;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.disease.impl.DiseaseAnalyseRecordService;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationRecordDTO;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.delegate.SeaAccountDelegate;
import com.shuidihuzhu.cf.dto.DiseaseAnalyseRecordDto;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.risk.client.rpc.DiseaseClassifyFeignClientV2;
import com.shuidihuzhu.cf.risk.client.rpc.DiseaseClient;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseAmountReasonableTypeEnum;
import com.shuidihuzhu.cf.risk.model.request.DecideReasonableInfo;
import com.shuidihuzhu.cf.risk.model.risk.DiseaseAmountResultRecord;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseClassifyVOV2;
import com.shuidihuzhu.cf.risk.model.risk.diease.InfoReasonableAmountResultVo;
import com.shuidihuzhu.cf.risk.model.risk.diease.JudgeDiseaseInfoVO;
import com.shuidihuzhu.cf.risk.model.risk.diease.SpecialDiseaseChoiceInfoVo;
import com.shuidihuzhu.cf.service.approve.OCRApproveService;
import com.shuidihuzhu.cf.service.notice.workwx.WorkWeiXinContentBuilder;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditCaseDetail;
import com.shuidihuzhu.cf.vo.approve.InitialAuditOCRResultVO;
import com.shuidihuzhu.cf.vo.approve.OcrAnalyseVO;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RefreshScope
public class OCRApproveServiceImpl implements OCRApproveService {

    @Autowired
    private DiseaseAnalyseRecordService diseaseAnalyseRecordService;

    @Autowired
    private CommonOperationRecordClient commonOperationRecordClient;

    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Autowired
    private HospitalDelegate hospitalDelegate;

    @Autowired
    private OCRBaiDuDelegate ocrBaiDuDelegate;

    @Resource
    private AlarmClient alarmClient;

    @Autowired
    private SeaAccountDelegate organizationDelegate;

    @Autowired
    private DiseaseClient diseaseClient;

    @Autowired
    private DiseaseClassifyFeignClientV2 diseaseClassifyFeignClient;

    private static final long ONE_YEAR_S = 365 * 24 * 60 * 60 * 1000L;

    @Value("${disease.SpecialName:烧伤}")
    private String diseaseSpecialName;

    @Autowired
    private ExtractCFFeignClient extractCFFeignClient;

    @Autowired
    private SeaAccountDelegate seaAccountDelegate;

    private static final Integer CAN_RAISE = 1;

    @Override
    public InitialAuditCaseDetail.FirstApproveCaseInfo fillResult(InitialAuditCaseDetail.FirstApproveCaseInfo firstApproveCaseInfo, int caseId, long workOrderId) {
        if (workOrderId <= 0) {
            return firstApproveCaseInfo;
        }

        OperationRecordDTO record = commonOperationRecordClient.getLastByBizIdAndActionTypes(workOrderId, OperationActionTypeEnum.OCR_APPROVE);
        if (record != null) {
            String resultJson = record.getExtMap().get("result");
            InitialAuditOCRResultVO result = JSON.parseObject(resultJson, InitialAuditOCRResultVO.class);
            firstApproveCaseInfo.setInitialAuditOCRResultVO(result);
            return firstApproveCaseInfo;
        }
        CrowdfundingInfo fundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
        String infoUuid = fundingInfo.getInfoId();
        String patientRealName = firstApproveCaseInfo.getPatientRealName();
        String imageUrl = firstApproveCaseInfo.getImageUrl();
        InitialAuditOCRResultVO result = getResult(caseId, infoUuid, patientRealName, imageUrl);
        if (result != null) {
            commonOperationRecordClient.create()
                    .buildBizId(workOrderId)
                    .buildActionType(OperationActionTypeEnum.OCR_APPROVE)
                    .buildExt("result", JSON.toJSONString(result))
                    .buildExt("caseId", String.valueOf(caseId))
                    .buildExt("workOrderId", String.valueOf(workOrderId))
                    .buildExt("infoUuid", infoUuid)
                    .buildExt("imageUrl", imageUrl)
                    .buildExt("patientRealName", patientRealName)
                    .save();
        }
        firstApproveCaseInfo.setInitialAuditOCRResultVO(result);
        return firstApproveCaseInfo;
    }

    @Override
    public Response<String> ocrDiseaseNames(int caseId, long workOrderId, String resourceUrl, List<String> urls, int adminUserId) {
        // ocr识别结果list
        List<String> diseaseList = Lists.newArrayList();
        for (String url : urls) {
            List<String> words = ocrBaiDuDelegate.accurateGeneralList(url);
            CollectionUtils.addAll(diseaseList, words);
        }

        String result = StringUtils.join(diseaseList, ",");
        return NewResponseUtil.makeSuccess(result);
    }

    @Override
    public Response<SpecialDiseaseChoiceInfoVo> analyseDiseaseNames(int caseId,
                                                                    long workOrderId,
                                                                    String submitDiseaseNames,
                                                                    int adminUserId,
                                                                    String specialRaiseChoiceInfo) {
        List<String> diseaseResourceList = Arrays.asList(StringUtils.split(submitDiseaseNames, ",，"));
        if (CollectionUtils.sizeIsEmpty(diseaseResourceList)) {
            return NewResponseUtil.makeFail("疾病名称不能为空");
        }
        commonOperationRecordClient.create()
                .buildBasic(workOrderId, OperationActionTypeEnum.TARGET_AMOUNT_CHECK_BAIDU_OCR)
                .buildCaseId(caseId)
                .save();

        Response<List<DiseaseClassifyVOV2>> listResponse = getDiseaseMergeOneResult(diseaseResourceList);
        if (listResponse == null || listResponse.notOk()) {
            return NewResponseUtil.makeFail("疾病归一出错");
        }
        List<DiseaseClassifyVOV2> classifyList = listResponse.getData();



        String showClassifyString = classifyList.stream()
                .map(v -> "【"+v.getDisease() + ":" + v.getNorm() + "】")
                .collect(Collectors.joining(","));
        // 归一后疾病list 过滤空
        long resultCount = CollectionUtils.size(classifyList.stream()
                .map(DiseaseClassifyVOV2::getNorm)
                .filter(CollectionUtils::isNotEmpty)
                .collect(Collectors.toList()));
        String nameWithOrgByUserId = organizationDelegate.getNameWithOrgByUserId(adminUserId);

        List<String> diseaseClassNameList = CollectionUtils.isEmpty(classifyList) ?
                Lists.newArrayList() : classifyList.stream().map(DiseaseClassifyVOV2::getNorm)
                .reduce(Lists.newArrayList() , (all, v) -> {all.addAll(v); return all;});
        DecideReasonableInfo decideReasonableInfo = new DecideReasonableInfo();
        decideReasonableInfo.setDiseaseNameList(diseaseClassNameList);
        decideReasonableInfo.setSpecialRaiseChoiceInfo(specialRaiseChoiceInfo);
        Response<SpecialDiseaseChoiceInfoVo> response = diseaseClient.specialChoiceInfo(decideReasonableInfo);
        if (response.notOk()) {
            return response;
        }
        SpecialDiseaseChoiceInfoVo resultVo = response.getData();
        resultVo.setDiseaseClassNameList(diseaseClassNameList);
        return NewResponseUtil.makeSuccess(resultVo);
    }

    private Response<List<DiseaseClassifyVOV2>> getDiseaseMergeOneResult(List<String> diseaseResourceList) {
        return diseaseClassifyFeignClient.diseaseNorm(diseaseResourceList);
    }

    @Override
    public Response<SpecialDiseaseChoiceInfoVo> analyseSpecialRaise(int caseId, long workOrderId, String diseaseNames, int adminUserId) {
        List<String> diseaseResourceList = Arrays.asList(StringUtils.split(diseaseNames, ",，"));
        if (CollectionUtils.sizeIsEmpty(diseaseResourceList)) {
            return NewResponseUtil.makeFail("疾病名称不能为空");
        }
        commonOperationRecordClient.create()
                .buildBasic(workOrderId, OperationActionTypeEnum.TARGET_AMOUNT_CHECK_BAIDU_OCR)
                .buildCaseId(caseId)
                .save();

        Response<List<DiseaseClassifyVOV2>> listResponse = getDiseaseMergeOneResult(diseaseResourceList);
        if (listResponse == null || listResponse.notOk()) {
            return NewResponseUtil.makeFail("疾病归一出错");
        }
        List<DiseaseClassifyVOV2> classifyList = listResponse.getData();
        List<DiseaseClassifyVOV2> classifyListNotFilter = listResponse.getData();


        String showClassifyString = classifyList.stream()
                .map(v -> "【"+v.getDisease() + ":" + v.getNorm() + "】")
                .collect(Collectors.joining(","));
        // 归一后疾病list 过滤空
        long resultCount = CollectionUtils.size(classifyList.stream()
                .map(DiseaseClassifyVOV2::getNorm)
                .filter(CollectionUtils::isNotEmpty)
                .collect(Collectors.toList()));

        String nameWithOrgByUserId = organizationDelegate.getNameWithOrgByUserId(adminUserId);

        //疾病归一后的操作
        afterDiseaseDeal(caseId, workOrderId, diseaseNames, diseaseResourceList, resultCount, showClassifyString, nameWithOrgByUserId);
        List<String> diseaseClassNameList = CollectionUtils.isEmpty(classifyListNotFilter) ? Lists.newArrayList()
                : classifyListNotFilter.stream().map(DiseaseClassifyVOV2::getNorm)
                .reduce(Lists.newArrayList() , (all, v) -> {all.addAll(v); return all;});
        Response<SpecialDiseaseChoiceInfoVo> response = diseaseClient.specialRaiseChoiceInfo(diseaseClassNameList);
        if (response.notOk()) {
            return response;
        }
        SpecialDiseaseChoiceInfoVo resultVo = response.getData();
        resultVo.setDiseaseClassNameList(diseaseClassNameList);
        return NewResponseUtil.makeSuccess(resultVo);
    }

    private void afterDiseaseDeal(int caseId,
                                  long workOrderId,
                                  String diseaseNames,
                                  List<String> diseaseResourceList,
                                  long resultCount,
                                  String showClassifyString,
                                  String nameWithOrgByUserId) {
        // 其中是否有未归一到的疾病
        boolean hasDiseaseNoClassify = CollectionUtils.size(diseaseResourceList) != resultCount;
        String errorMsg = hasDiseaseNoClassify? "归一结果不全" : "";

        DiseaseAnalyseRecordDto recordDto = DiseaseAnalyseRecordDto.builder()
                .caseId(caseId)
                .workOrderId(workOrderId)
                .inputDiseaseName(diseaseNames)
                .outputNormalizedResult(showClassifyString)
                .errorMsg(errorMsg)
                .operator(nameWithOrgByUserId)
                .hasError(hasDiseaseNoClassify ? 1 : 0)
                .build();
        diseaseAnalyseRecordService.insertOne(recordDto);

        if (hasDiseaseNoClassify) {
            String content = WorkWeiXinContentBuilder.create()
                    .subject("【疾病归一结果报错】")
                    .payload("案例ID", caseId)
                    .payload("工单ID", workOrderId)
                    .payload("入参疾病名称", diseaseNames)
                    .payload("出参归一结果", showClassifyString)
                    .payload("报错类型", errorMsg)
                    .payload("报错时间", DateUtil.getCurrentDateTimeStr())
                    .payload("操作人", nameWithOrgByUserId)
                    .build();
            alarmClient.sendByGroup("wx-alarm-prod-20200313-0001", content);
        }
    }

    @Override
    public List<OperationRecordDTO> listCaseAnalyseHistory(int caseId) {
        return commonOperationRecordClient.listByCaseIdAndActionTypes(caseId,
                Lists.newArrayList(OperationActionTypeEnum.TARGET_AMOUNT_CHECK_DISEASE_CLASSIFY.getValue()));
    }

    @Override
    public OperationRecordDTO getLastAnalyseByWorkOrder(long workOrderId) {
        return commonOperationRecordClient.getLastByBizIdAndActionTypes(workOrderId,
                OperationActionTypeEnum.TARGET_AMOUNT_CHECK_DISEASE_CLASSIFY);
    }

    @Override
    public Response<InfoReasonableAmountResultVo> decideInfoAmount(int caseId, long workOrderId, int workOrderType,
                                                                   int adminUserId, String ocrDiseaseNames,
                                                                   List<String> classifyDiseaseNames,
                                                                   String resourceUrl, List<String> croppedUrls,
                                                                   String otherInfo,
                                                                   String submitDiseaseNames,
                                                                   String specialRaiseChoiceInfo,
                                                                   int handleResult) {
        List<String> diseaseResourceList = Arrays.asList(StringUtils.split(submitDiseaseNames, ",， 。、；;"));
        String nameWithOrgByUserId = organizationDelegate.getNameWithOrgByUserId(adminUserId);
        // 归一后疾病list 不过滤空
        InfoReasonableAmountResultVo info;
        Response<List<DiseaseClassifyVOV2>> listResponse = getDiseaseMergeOneResult(diseaseResourceList);
        if (listResponse == null || listResponse.notOk()) {
            return NewResponseUtil.makeFail("疾病归一出错");
        }
        List<DiseaseClassifyVOV2> classifyList = listResponse.getData();
        long resultCount = CollectionUtils.size(classifyList.stream()
                .map(DiseaseClassifyVOV2::getNorm)
                .filter(CollectionUtils::isNotEmpty)
                .collect(Collectors.toList()));
        info = resultCount > 0 ? decideInfoAmountReasonable(submitDiseaseNames, workOrderId, nameWithOrgByUserId,
                classifyDiseaseNames, caseId, otherInfo, specialRaiseChoiceInfo, diseaseResourceList) : null;
        if (info == null) {
            // 保存归一失败疾病的操作记录
            if(handleResult == HandleResultEnum.doing.getType() || handleResult == HandleResultEnum.later_doing.getType()){
                saveToolRecordWithoutDiseaseNormName(caseId, workOrderId, workOrderType, adminUserId, submitDiseaseNames);
            }
            return NewResponseUtil.makeFail("疾病归一失败");
        }
        OcrAnalyseVO view = OcrAnalyseVO.create(info);
        view.setSubmitDiseaseNames(submitDiseaseNames);
        commonOperationRecordClient.create()
                .buildBasic(workOrderId, OperationActionTypeEnum.TARGET_AMOUNT_CHECK_DISEASE_CLASSIFY)
                .buildCaseId(caseId)
                .buildOperatorId(adminUserId)
                .buildExtValue("caseId", caseId)
                .buildExtValue("workOrderId", workOrderId)

                .buildExtValue("resourceUrl", resourceUrl)
                .buildExtValue("croppedUrls", StringUtils.join(croppedUrls, ","))
                .buildExtValue("ocrDiseaseNames", StringUtils.trimToEmpty(ocrDiseaseNames))
                .buildExtValue("submitDiseaseNames", submitDiseaseNames)
                //1.7 移除材料
                //.buildExtValue("imageType", type.getMsg())
                .buildExtValue("hasMatchAllDisease", CollectionUtils.size(info.getDiseaseInfoList()) == resultCount)
                .buildExtValue("isOcrAnalyse", CollectionUtils.isNotEmpty(croppedUrls))
                .buildExtValue("classifyDiseaseNames", classifyList.stream()
                        .map(v -> "【"+v.getDisease() + "】:" + Joiner.on(",").join(v.getNorm()))
                        .collect(Collectors.joining("\n")))
                .buildExtValue("hasDiseaseNoClassify", CollectionUtils.size(diseaseResourceList) != resultCount)
                .buildExtValue("info", JSON.toJSONString(view))
                .buildExtValue("otherInfo", StringUtils.trimToEmpty(info.getOtherInfo()))
                .save();

        // 保存选择了疾病治疗方案的情况的操作记录
        if(handleResult == HandleResultEnum.doing.getType() || handleResult == HandleResultEnum.later_doing.getType()) {
            saveToolRecordWithChoice(caseId, workOrderId, workOrderType, adminUserId, view);
        }
        return NewResponseUtil.makeSuccess(view);
    }

    private void saveToolRecordWithoutDiseaseNormName(int caseId, long workOrderId, int workOrderType, int adminUserId, String submitDiseaseNames) {
        DiseaseAmountResultRecord record = new DiseaseAmountResultRecord();
        buildBaseRecord(caseId, workOrderId, workOrderType, adminUserId, record);
        record.setDiseaseName(submitDiseaseNames);
        record.setDiseaseNormName("");
        record.setTreatmentInfo("归一失败");
        record.setTargetAmountInFen(0);
        record.setCalculateDiseaseName("");
        record.setAdviseMinAmount(0);
        record.setAdviseMaxAmount(0);
        diseaseClient.saveToolRecord(record);
    }

    private void saveToolRecordWithChoice(int caseId, long workOrderId, int workOrderType, int adminUserId, OcrAnalyseVO view) {
        DiseaseAmountResultRecord record = new DiseaseAmountResultRecord();
        List<JudgeDiseaseInfoVO> judgeDiseaseInfoList = view.getJudgeDiseaseInfoList();
        if(CollectionUtils.isEmpty(judgeDiseaseInfoList)){
            log.info("judgeDiseaseInfoList is empty,caseId:{}, workOrderId:{}, workOrderType:{}, userId:{}",
                    caseId, workOrderId, workOrderType, adminUserId);
            return;
        }
        List<String> diseaseName = Lists.newArrayList();
        List<String> diseaseNormName = Lists.newArrayList();
        List<String> treatmentInfo = Lists.newArrayList();
        handleListInfo(judgeDiseaseInfoList, diseaseName, treatmentInfo, diseaseNormName);
        buildBaseRecord(caseId, workOrderId, workOrderType, adminUserId, record);
        record.setDiseaseNormName(StringUtils.join(diseaseNormName, ","));
        record.setDiseaseName(StringUtils.join(diseaseName, ","));
        record.setTreatmentInfo(StringUtils.join(treatmentInfo, ";"));
        record.setTargetAmountInFen(view.getTargetAmountInFen());
        record.setCalculateDiseaseName(view.getCalculateDiseaseNames());
        record.setAdviseMinAmount((int) (view.getAdviseMinAmount() * 10000));
        record.setAdviseMaxAmount((int) (view.getAdviseMaxAmount() * 10000));
        log.info("saveToolRecordWithChoice record:{}", record);
        diseaseClient.saveToolRecord(record);
    }

    private void buildBaseRecord(int caseId, long workOrderId, int workOrderType, long adminUserId, DiseaseAmountResultRecord record) {
        record.setCaseId(caseId);
        record.setWorkOrderId(workOrderId);
        record.setWorkOrderTypeCode(workOrderType);
        record.setReasonAmountType(DiseaseAmountReasonableTypeEnum.DISEASE_CALCULATE_TOOL.getCode());
        record.setOperator(seaAccountDelegate.getNameWithOrgByLongUserId(adminUserId));
        record.setOperatorId(adminUserId);
    }

    @Override
    public Response<Void> saveDiseaseToolRecordWithoutChoice(String diseaseClassNames,
                                                             String submitDiseaseNames, int workOrderType, long workOrderId,
                                                             int caseId, long userId, int handleResult) {
        if(handleResult != HandleResultEnum.doing.getType() && handleResult != HandleResultEnum.later_doing.getType()){
            log.info("handleResult is not doing or later_doing,caseId:{}, workOrderId:{}, workOrderType:{}, userId:{}",
                    caseId, workOrderId, workOrderType, userId);
            return NewResponseUtil.makeSuccess();
        }
        // 保存仅填写疾病情况的操作记录
        DiseaseAmountResultRecord record = new DiseaseAmountResultRecord();
        saveOnlyChoiceDiseaseRecord(diseaseClassNames, submitDiseaseNames, workOrderType, workOrderId, caseId, userId, record);
        return NewResponseUtil.makeSuccess();
    }

    private void saveOnlyChoiceDiseaseRecord(String diseaseClassNames, String submitDiseaseNames, int workOrderType, long workOrderId, int caseId, long userId, DiseaseAmountResultRecord record) {
        buildBaseRecord(caseId, workOrderId, workOrderType, userId, record);
        record.setDiseaseName(submitDiseaseNames);
        record.setTreatmentInfo(diseaseClassNames);
        record.setDiseaseNormName(record.getTreatmentInfo());
        record.setTargetAmountInFen(0);
        record.setCalculateDiseaseName("");
        record.setAdviseMinAmount(0);
        record.setAdviseMaxAmount(0);
        diseaseClient.saveToolRecord(record);
    }

    private static void handleListInfo(List<JudgeDiseaseInfoVO> judgeDiseaseInfoList, List<String> diseaseName,
                                       List<String> treatmentInfo, List<String> diseaseNormName) {
        for(JudgeDiseaseInfoVO judgeDiseaseInfoVO : judgeDiseaseInfoList){
            String diseaseClassName = judgeDiseaseInfoVO.getDiseaseClassName();
            if(StringUtils.isBlank(diseaseClassName)){
                treatmentInfo.add("归一失败");
                continue;
            }
            diseaseName.add(judgeDiseaseInfoVO.getDiseaseName());
            diseaseNormName.add(judgeDiseaseInfoVO.getDiseaseClassName());
            List<String> treatment = Lists.newArrayList();
            treatment.add(judgeDiseaseInfoVO.getDiseaseClassName());
            treatment.add(judgeDiseaseInfoVO.getTreatMethod());
            treatment.add(judgeDiseaseInfoVO.getRaise() == CAN_RAISE ? "可发起" : "不可发起");
            treatmentInfo.add(StringUtils.join(treatment, "-"));
        }
    }

    private InfoReasonableAmountResultVo decideInfoAmountReasonable(String submitDiseaseNames,
                                                                    long workOrderId,
                                                                    String nameWithOrgByUserId,
                                                                    List<String> classifyDiseaseNames,
                                                                    int caseId, String otherInfo,
                                                                    String specialRaiseChoiceInfo,
                                                                    List<String> diseaseResourceList) {
        String extContent = "疾病名称：" + submitDiseaseNames + "\n工单ID：" + workOrderId + "\n操作人" + nameWithOrgByUserId;
        DecideReasonableInfo decideReasonableInfo = new DecideReasonableInfo();
        decideReasonableInfo.setDiseaseNameList(classifyDiseaseNames);
        decideReasonableInfo.setExtContent(extContent);
        decideReasonableInfo.setCaseId(caseId);
        decideReasonableInfo.setSpecialDiseaseInfo(otherInfo);
        decideReasonableInfo.setSpecialRaiseChoiceInfo(specialRaiseChoiceInfo);
        decideReasonableInfo.setDiseaseResourceList(diseaseResourceList);
        Response<InfoReasonableAmountResultVo> infoResp =
                diseaseClient.decideInfoAmountReasonable(decideReasonableInfo);
        if (infoResp == null || infoResp.notOk()) {
            return null;
        }
        return infoResp.getData();
    }

    private InitialAuditOCRResultVO getResult(int caseId, String infoUuid, String patientRealName, String imageUrl) {
//        Response<CfExtractModel> cfExtractModelResponse = extractCFFeignClient.extractCf(infoUuid, String.valueOf(caseId), patientRealName, "", imageUrl);
//        log.info("OCRApproveServiceImpl response {}", cfExtractModelResponse);
//        if (cfExtractModelResponse == null) {
//            log.warn("OCRApproveServiceImpl response null or fallback caseId:{}", caseId);
//            return null;
//        }
//        if (cfExtractModelResponse.notOk()) {
//            log.warn("OCRApproveServiceImpl code not 0 caseId:{}, response:{}", caseId, cfExtractModelResponse);
//            return null;
//        }
//        CfExtractModel data = cfExtractModelResponse.getData();
//        if (data == null) {
//            log.warn("OCRApproveServiceImpl response data null caseId:{}, response:{}", caseId, cfExtractModelResponse);
//            return null;
//        }
//        InitialAuditOCRResultVO view = new InitialAuditOCRResultVO();
//        String hospital_info = data.getHospital_info();
//        view.setHospitalName(hospital_info);
//
//        List<CfExtractDateInfo> date_info = data.getDate_info();
//        if (CollectionUtils.isNotEmpty(date_info)) {
//            Date date = date_info
//                    .stream()
//                    .filter(Objects::nonNull)
//                    .map(CfExtractDateInfo::getDate)
//                    .map(DateUtil::getDateFromShortString)
//                    .filter(Objects::nonNull)
//                    .min(Comparator.comparing(v -> v))
//                    .orElse(null);
//            view.setDate(date);
//            view.setDateStr(DateUtil.formatDate(date));
//        }
//
//        String name = data.getName();
//        String[] split = StringUtils.split(name, "|");
//        if (ArrayUtils.isNotEmpty(split)) {
//            name = split[0];
//        }
//        view.setPatientName(name);
//
//        view.setSamePatientName(StringUtils.equals(patientRealName, name));
//        if (view.getDate() != null) {
//            view.setNewlyDate(new Date(System.currentTimeMillis() - ONE_YEAR_S).before(view.getDate()));
//        }
//        if (StringUtils.isNotBlank(hospital_info)) {
//            Boolean checkResult = hospitalDelegate.checkInfo(hospital_info);
//            view.setHospitalExist(checkResult);
//        }
//        view.setDepartmentName(data.getDepartment());
//        return view;
        return null;
    }

}
