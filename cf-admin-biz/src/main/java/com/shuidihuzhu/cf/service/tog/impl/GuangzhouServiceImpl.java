package com.shuidihuzhu.cf.service.tog.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.adminpure.enums.WorkOrderExtContentTypeEnum;
import com.shuidihuzhu.cf.client.feign.CaseInfoApproveStageFeignClient;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.domain.caseinfo.CaseInfoApproveStageDO;
import com.shuidihuzhu.cf.labels.CaseLabelsManagementService;
import com.shuidihuzhu.cf.model.CaseLabelsManagement;
import com.shuidihuzhu.cf.model.admin.workorder.imageContent.CfImageContentAuditView;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.FoundationLabelInfo;
import com.shuidihuzhu.cf.model.tog.GuangzhouMarkShowVO;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.cf.service.tog.GuangzhouService;
import com.shuidihuzhu.cf.service.workorder.WorkOrderExtService;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditCaseDetail;
import com.shuidihuzhu.client.cf.api.client.CfGovMarkFeignClient;
import com.shuidihuzhu.client.cf.api.client.CfGuangzhouLabelFeignClient;
import com.shuidihuzhu.client.cf.api.model.CfLabelShowToCEnum;
import com.shuidihuzhu.client.cf.api.model.GovCooperationMark;
import com.shuidihuzhu.client.cf.api.model.GuangZhouExtInfo;
import com.shuidihuzhu.client.cf.api.model.GuangzhouMarkRecord;
import com.shuidihuzhu.client.cf.api.model.enums.GovProjectEnum;
import com.shuidihuzhu.client.cf.api.model.enums.GuangzhouDifficultPersonEnum;
import com.shuidihuzhu.client.cf.api.model.enums.GuangzhouShowToCEnum;
import com.shuidihuzhu.client.cf.api.model.enums.GuangzhouUserInfoCShowEnum;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackApiClient;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewBaseInfoDO;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewTogAttachInfo;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RefreshScope
public class GuangzhouServiceImpl implements GuangzhouService {

    @Resource
    private CfGuangzhouLabelFeignClient cfGuangzhouLabelFeignClient;

    @Resource
    private CaseLabelsManagementService caseLabelsManagementService;

    @Autowired
    private ApproveRemarkOldService remarkOldService;

    @Autowired
    private WorkOrderExtService workOrderExtService;

    @Autowired
    private CaseInfoApproveStageFeignClient caseInfoApproveStageFeignClient;

    @Resource
    private CfClewtrackFeignClient cfClewtrackFeignClient;

    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;

    @Autowired
    private CfClewtrackApiClient cfClewtrackApiClient;

    @Autowired
    private CfGovMarkFeignClient cfGovMarkFeignClient;

    // 生成工单时刻需要重新审核是否广州案例的工单类型(初审)
    public static final List<Integer> Guangzhou_chuci_WORK_ORDER_LIST = ImmutableList.of(
            WorkOrderType.ai_erci.getType(),
            WorkOrderType.highriskshenhe.getType(),
            WorkOrderType.ai_content.getType()
    );

    // 生成工单时刻需要重新审核是否广州案例的工单类型(图文审核)
    public static final List<Integer> Guangzhou_tuwen_WORK_ORDER_LIST = ImmutableList.of(
            WorkOrderType.content.getType()
    );

    // 基金会标签信息Map
    Map<Integer, FoundationLabelInfo> foundationLabelInfoMap = Maps.newHashMap();

    /**
     * 基金会标签内容配置
     */
    @Value("${funding.foundation.label.config:[]}")
    public void setFoundationLabelConfig(String foundationLabelConfig) {
        if (StringUtils.isBlank(foundationLabelConfig)) {
            return;
        }
        foundationLabelInfoMap = JSONObject.parseArray(foundationLabelConfig, FoundationLabelInfo.class)
                .stream().collect(Collectors.toMap(FoundationLabelInfo::getLabel, Function.identity()));
    }


    @Override
    public void addOrUpdateGuangzhouLabel(int caseId) {

        log.info("saveGuangzhouLabel caseId:{}", caseId);

        if (caseId <= 0) {
            log.error("saveGuangzhouLabel caseId is invalid");
            return;
        }

        GuangzhouMarkRecord guangzhouMarkRecord = new GuangzhouMarkRecord();
        guangzhouMarkRecord.setCaseId(caseId);

        // 新增或更新广州标签
        cfGuangzhouLabelFeignClient.addOrUpdateRecordByCondition(guangzhouMarkRecord);
    }

    @Override
    public void addOrUpdateGuangzhouLabel(int caseId, int workOrderType, long workOrderId) {

        log.info("saveGuangzhouLabelWithWorkOrder caseId:{}, workOrderType:{}, workOrderId:{}", caseId, workOrderType, workOrderId);

        if (caseId <= 0 || workOrderId <= 0 || workOrderType <= 0) {
            return;
        }

        GuangzhouMarkRecord guangzhouMarkRecord = new GuangzhouMarkRecord();
        guangzhouMarkRecord.setCaseId(caseId);
        String content = "";

        // 补全对应工单类型的文章内容
        if(Guangzhou_chuci_WORK_ORDER_LIST.contains(workOrderType)){
            content = getContentByChuciWorkOrder(workOrderId, caseId);
        } else if(Guangzhou_tuwen_WORK_ORDER_LIST.contains(workOrderType)){
            content = getContentByTuwenWorkOrder(workOrderId, caseId);
        }
        guangzhouMarkRecord.setContent(content);

        // 新增或更新广州标签
        cfGuangzhouLabelFeignClient.addOrUpdateRecordByCondition(guangzhouMarkRecord);
    }

    private String getContentByChuciWorkOrder(long workOrderId, int caseId) {
        InitialAuditCaseDetail.CaseBaseInfo baseInfo = getBaseInfoSnapshot(workOrderId);
        log.info("getContentByChuciWorkOrder workOrderId:{}, baseInfo:{}", workOrderId, baseInfo);
        if (baseInfo == null) {
            baseInfo = getBaseInfo(caseId);
            log.info("getContentByChuciWorkOrder caseId:{}, baseInfo:{}", caseId, baseInfo);
        }
        return baseInfo.getContent();
    }

    private InitialAuditCaseDetail.CaseBaseInfo getBaseInfoSnapshot(long workOrderId) {
        return workOrderExtService.getByClazz(workOrderId,
                WorkOrderExtContentTypeEnum.INITIAL_AUDIT_CASE_BASE_INFO, InitialAuditCaseDetail.CaseBaseInfo.class);
    }

    public InitialAuditCaseDetail.CaseBaseInfo getBaseInfo(int caseId) {
        Response<CaseInfoApproveStageDO> stageInfoResp = caseInfoApproveStageFeignClient.getStageInfo(caseId);
        log.info("getBaseInfo caseId:{}, stageInfoResp:{}", caseId, stageInfoResp);
        CaseInfoApproveStageDO stageInfo = stageInfoResp.getData();

        InitialAuditCaseDetail.CaseBaseInfo caseBaseInfo = new InitialAuditCaseDetail.CaseBaseInfo();

        if (stageInfoResp.ok()){
            if (stageInfo != null) {
                // 有暂存图文
                caseBaseInfo.setTitle(stageInfo.getTitle());
                caseBaseInfo.setContent(stageInfo.getContent());
            }
        }
        return caseBaseInfo;
    }


    private String getContentByTuwenWorkOrder(long workOrderId, int caseId) {
        CfImageContentAuditView auditView = workOrderExtService.getByClazz(workOrderId,
                WorkOrderExtContentTypeEnum.IMAGE_CONTENT_AUDIT, CfImageContentAuditView.class);
        log.info("getContentByTuwenWorkOrder workOrderId:{}, auditView:{}", workOrderId, auditView);
        if (auditView == null) {
            InitialAuditCaseDetail.CaseBaseInfo baseInfo = getBaseInfo(caseId);
            log.info("getContentByTuwenWorkOrder caseId:{}, baseInfo:{}", caseId, baseInfo);
            if(baseInfo != null){
                return baseInfo.getContent();
            }
        }
        return auditView.getCaseBaseInfo().getContent();
    }

    @Override
    public GuangzhouMarkShowVO getGuangzhouMarkShowVO(int caseId) {
        log.info("getGuangzhouMarkShowVO caseId:{}", caseId);

        GuangzhouMarkShowVO guangzhouMarkShowVO = new GuangzhouMarkShowVO();
        // 默认赋值非广州标签，c端不展示
        guangzhouMarkShowVO.setShowStatusToC(GuangzhouShowToCEnum.NO_SHOW.getCode());
        guangzhouMarkShowVO.setGuangzhouLabel(false);
        guangzhouMarkShowVO.setHcbg(0);
        guangzhouMarkShowVO.setJtrks(0);
        guangzhouMarkShowVO.setSfknqz(GuangzhouDifficultPersonEnum.UNKNOWN.getCode());
        guangzhouMarkShowVO.setUserGrant(GuangzhouShowToCEnum.DEFAULT.getCode());

        //    入口判断：以代录入ID来源均为【广州2G的】的案例，都需要在案例详情页有打标入口
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        Response<GovCooperationMark> resp = cfGovMarkFeignClient.getMarkRecord(caseId, GovProjectEnum.SUI_JIU_YI.getCode());
        if (resp.notOk() || resp.getData() == null) {
            log.info("getClewTogAttachByCaseId is null, caseId:{}", caseId);
            return guangzhouMarkShowVO;
        }
        String attributes = resp.getData().getAttributes();
        if (StringUtils.isBlank(attributes)) {
            return guangzhouMarkShowVO;
        }
        GuangZhouExtInfo attachInfo = JSONObject.parseObject(attributes, GuangZhouExtInfo.class);
        guangzhouMarkShowVO.setGuangzhouLabel(true);
        guangzhouMarkShowVO.setHcbg(attachInfo.getHasEconomicReport());
        guangzhouMarkShowVO.setJtrks(attachInfo.getFamilyMembers());
        guangzhouMarkShowVO.setSfknqz(attachInfo.getIsDifficultGroup());
        guangzhouMarkShowVO.setUserGrant(attachInfo.getUserGrantShow());

        // 查询上次c端打标结果
        String labelInfoStr = caseLabelsManagementService.get(crowdfundingInfo.getInfoId());
        if (StringUtils.isBlank(labelInfoStr)) {
            return guangzhouMarkShowVO;
        }
        CaseLabelsManagement label = JSONObject.parseObject(labelInfoStr, CaseLabelsManagement.class);
        if (label.getOrgLabel() == 0) {
            return guangzhouMarkShowVO;
        }
        switch (label.getOrgLabel()) {
            // case值对应CfLabelShowToCEnum
            case -1:
                guangzhouMarkShowVO.setShowStatusToC(GuangzhouShowToCEnum.NO_SHOW.getCode());
                break;
            case 7:
                guangzhouMarkShowVO.setShowStatusToC(GuangzhouShowToCEnum.SHOW_SJY.getCode());
                break;
            case 8:
                guangzhouMarkShowVO.setShowStatusToC(GuangzhouShowToCEnum.NO_SHOW_SJY.getCode());
                break;
            case 9:
                guangzhouMarkShowVO.setShowStatusToC(GuangzhouShowToCEnum.SHOW_JJHC.getCode());
                break;
            default:
                break;
        }
        return guangzhouMarkShowVO;
    }

    @Override
    public boolean updateShowStatusToC(int caseId, int showStatusToC, String reason, int adminUserId) {
        log.info("updateShowStatusToC caseId:{}, showStatusToC:{}, reason:{}", caseId, showStatusToC, reason);
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if (crowdfundingInfo == null) {
            log.error("updateShowStatusToC crowdfundingInfo is null, caseId:{}", caseId);
            return false;
        }
        String infoId = crowdfundingInfo.getInfoId();

        // 更新c端标签展示表
        String showStatus = "不展示";
        if (showStatusToC == GuangzhouShowToCEnum.NO_SHOW.getCode()){
            // 设置c端标签为不展示
            caseLabelsManagementService.updateOrgLabel(infoId, CfLabelShowToCEnum.MANUAL_DEFAULT.getCode());
        }
        if (showStatusToC == GuangzhouShowToCEnum.SHOW_SJY.getCode()) {
            caseLabelsManagementService.updateOrgLabel(infoId, CfLabelShowToCEnum.SUIJIUYI.getCode());
            showStatus = "展示穗救易标签";
        }
        if (showStatusToC == GuangzhouShowToCEnum.NO_SHOW_SJY.getCode()) {
            caseLabelsManagementService.updateOrgLabel(infoId, CfLabelShowToCEnum.SUIJIUYI_NO.getCode());
            showStatus = "展示穗救易标签";
        }
        if (showStatusToC == GuangzhouShowToCEnum.SHOW_JJHC.getCode()) {
            // 设置c端标签为展示
            caseLabelsManagementService.updateOrgLabel(infoId, CfLabelShowToCEnum.SUIJIUYI_REPORT.getCode());
            showStatus = "展示经济核查标识";
        }
        // 记日志(后台案例详情页通用模块展示)
        String comment = "选择" + showStatus + "\n"
                + "原因：" + reason;
        remarkOldService.add(caseId, adminUserId, comment);
        return true;
    }


    private boolean isGuangzhouClew(String mobile) {
        // 查询所有线索
        List<CfClewBaseInfoDO> cfClewBaseInfos = getClewBaseInfos(mobile);
        if (CollectionUtils.isEmpty(cfClewBaseInfos)) {
            return false;
        }
        log.info("GuangzhouService isGuangzhouClew cfClewBaseInfos:{}", JSONObject.toJSONString(cfClewBaseInfos));
        List<String> primaryChannelList = cfClewBaseInfos.stream()
                .filter(cfClewBaseInfoDO -> cfClewBaseInfoDO.getPrimaryChannel().equals("tog_gzmzj"))
                .sorted(Comparator.comparing(CfClewBaseInfoDO::getId).reversed())
                .map(CfClewBaseInfoDO::getPrimaryChannel)
                .collect(Collectors.toList());

        return CollectionUtils.isNotEmpty(primaryChannelList);
    }

    private List<CfClewBaseInfoDO> getClewBaseInfos(String mobile) {

        // 请求线索
        Response<List<CfClewBaseInfoDO>> cfClewBaseInfos = cfClewtrackFeignClient.getClewBaseInfoByMobile(mobile);
        if (Objects.isNull(cfClewBaseInfos) || cfClewBaseInfos.notOk() || CollectionUtils.isEmpty(cfClewBaseInfos.getData())) {
            log.info("GuangzhouService getClewBaseInfos error cfClewBaseInfos is null mobile:{}", mobile);
            return new ArrayList<>();
        }
        return cfClewBaseInfos.getData();
    }
}
