package com.shuidihuzhu.cf.service.message;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.mq.model.CfPatientChangeModel;
import com.shuidihuzhu.client.baseservice.msg.v2.MsgClientV2;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import com.shuidihuzhu.msg.vo.Response;
import com.shuidihuzhu.msg.vo.rpc.MsgRecord;
import com.shuidihuzhu.msg.vo.rpc.MsgRecordBatch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @time 2019/3/8 下午3:54
 * @desc
 */
@Slf4j
@Service
public class AdminMsgClientService {
    @Autowired(required = false)
    private Producer producer;

    public void sendCasePatientChangeMsg(int caseId) {
        CfPatientChangeModel changeModel = new CfPatientChangeModel();
        changeModel.setCaseId(caseId);

        Message msg = new Message(MQTopicCons.CF,  MQTagCons.ALL_CF_PATIENT_CHANGE_INFO,
                System.currentTimeMillis() + "_" + caseId, changeModel, DelayLevel.S1);

        MessageResult msgResult = producer.send(msg);
        log.info("病患接口数据变更 msg:{} result:{}", msg, msgResult);
    }

}
