package com.shuidihuzhu.cf.service.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingOperationBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderCaseBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfAdminOperationRecordBiz;
import com.shuidihuzhu.cf.constants.AdminUserIDConstants;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.finance.impl.FinanceDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefund;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation;
import com.shuidihuzhu.cf.model.event.CailiaoConditionEvent;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.FinanceApproveService;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cf.service.workorder.cailiao.ZhuDongFuWuWorkOrderService;
import com.shuidihuzhu.cf.util.CaseUtils;
import com.shuidihuzhu.client.cf.workorder.CfCailiaoWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.CailiaoHandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class DealWithStatusService {

    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;
    @Autowired
    private AdminApproveService adminApproveService;
    @Autowired
    private FinanceApproveService financeApproveService;
    @Autowired
    private CfAdminOperationRecordBiz cfAdminOperationRecordBiz;
    @Autowired
    private AdminWorkOrderCaseBiz adminWorkOrderCaseBiz;

    @Autowired
    private AdminCrowdfundingOperationBiz adminCrowdfundingOperationBiz;
    @Autowired
    private FinanceDelegate financeDelegate;

    @Autowired
    private CfCailiaoWorkOrderClient workOrderClient;

    @Autowired
    private CfCailiaoService cfCailiaoService;

    @Autowired
    private ZhuDongFuWuWorkOrderService zhuDongFuWuWorkOrderService;


    public Response addWithWorkType(Integer status, String comment, int userId, CrowdfundingInfo crowdfundingInfo, int orderType, long workId, int deferContactReasonType) {
        CfDealWithStatusEnum cfDealWithStatusEnum = CfDealWithStatusEnum.getByValue(status);
        List<CfDealWithStatusEnum> needCloseWorkStatus = Lists.newArrayList(CfDealWithStatusEnum.DEFERRED_APPROVE, CfDealWithStatusEnum.DEFERRED_CONTACT, CfDealWithStatusEnum.DISREGARD);
        //对延后处理、延后电话联系、不再处理直接关闭工单
        if (needCloseWorkStatus.contains(cfDealWithStatusEnum)) {
            CailiaoHandleOrderParam cailiaoHandleOrderParam = new CailiaoHandleOrderParam();
            cailiaoHandleOrderParam.setHandleResult(HandleResultEnum.manual_lock.getType());
            cailiaoHandleOrderParam.setOrderType(orderType);
            cailiaoHandleOrderParam.setUserId(userId);
            cailiaoHandleOrderParam.setOperComment(getFinalComment(status, deferContactReasonType, comment));
            cailiaoHandleOrderParam.setWorkOrderId(workId);
            Response response = workOrderClient.handleCailiao(cailiaoHandleOrderParam);
            log.info("addWithWorkType delete old workOrder result:{},workId:{}", response.ok(), workId);

        }
        //对于延后处理的操作，由于已将工单关闭，可以生成延后审核工单
         return add(status, comment, userId, crowdfundingInfo, deferContactReasonType);
    }

    @NotNull
    public Response add(Integer status, String comment, int userId, CrowdfundingInfo crowdfundingInfo, int deferContactReasonType) {
        comment = getFinalComment(status, deferContactReasonType, comment);
        String infoUuid = crowdfundingInfo.getInfoId();
        CfDealWithStatusEnum cfDealWithStatusEnum = CfDealWithStatusEnum.getByValue(status);
        Response<String> checkResult = checkHandleNoLongerProcessWithMsg(crowdfundingInfo, cfDealWithStatusEnum);
        if (checkResult.notOk()) {
            log.info("add checkResult notOk infoUuid:{}", infoUuid);
            return checkResult;
        }
        financeApproveService.addApprove(crowdfundingInfo, cfDealWithStatusEnum.getMessage(), comment, userId);
        // 操作记录入库
        CfOperationRecordEnum oprRecordStatus = cfDealWithStatusEnum.getOperationRecordEnum();
        CrowdfundingOperationEnum crowdfundingOperationEnum = cfDealWithStatusEnum.getOperationEnum();
        if (cfDealWithStatusEnum == CfDealWithStatusEnum.DEFERRED_APPROVE && crowdfundingInfo.getStatus() == CrowdfundingStatus.SUBMITTED){
            cfCailiaoService.createWorkOrder(new CailiaoConditionEvent(this,crowdfundingInfo.getId(), crowdfundingInfo.getInfoId()));
        }
        //如果是延后电话联系和 不再处理  直接关闭 未分配的工单
        if ((cfDealWithStatusEnum == CfDealWithStatusEnum.DISREGARD || cfDealWithStatusEnum == CfDealWithStatusEnum.DEFERRED_CONTACT || cfDealWithStatusEnum == CfDealWithStatusEnum.DEFERRED_APPROVE) && crowdfundingInfo.getStatus() == CrowdfundingStatus.SUBMITTED){
            cfCailiaoService.sendDealMQ(crowdfundingInfo.getId(),cfDealWithStatusEnum.getValue());
        }
        if (cfDealWithStatusEnum == CfDealWithStatusEnum.DISREGARD || cfDealWithStatusEnum == CfDealWithStatusEnum.DEFERRED_CONTACT){
            zhuDongFuWuWorkOrderService.onCaseNoProcess(crowdfundingInfo);
        }

        this.adminApproveService.dealWithChangeOperation(infoUuid, crowdfundingOperationEnum, comment, userId, deferContactReasonType);

        CfOperatingRecordEnum.Type type = oprRecordStatus.getNewType();
        if(type != null){
            adminApproveService.saveOperatingRecord(userId, infoUuid, type);
        }
        cfAdminOperationRecordBiz.addOneOperationRecord(infoUuid, userId, oprRecordStatus.value(), comment);
        //更新对应工单
        log.info("DealWithStatusService A infoUuid:{}", infoUuid);
        Response response = adminWorkOrderCaseBiz.updateWorkOrderCaseApprove(crowdfundingInfo.getId() ,infoUuid, userId, comment, crowdfundingOperationEnum);
        if (response != null) {
            return response;
        }

        log.info(
                "客服后台log：deal-with operationTime:{};operator:{};operationReason:{};operationType:{};infoId:{};status:{};dataStatus:{};infoStatus:{}",
                DateUtil.formatDateTime(new Date()), userId, cfDealWithStatusEnum.getMessage(),
                cfDealWithStatusEnum.getMessage(), infoUuid, crowdfundingInfo.getStatus(),
                crowdfundingInfo.getDataStatus(), crowdfundingInfo.getEndTime().before(new Date()) ? "结束" : "未结束");
        Map<String, Object> result = Maps.newHashMap();
        result.put("status", status);
        result.put("list", adminApproveService.getCommentVoList(crowdfundingInfo.getId()));
        return NewResponseUtil.makeSuccess(result);
    }

    private String getFinalComment(Integer status, int deferContactReasonType, String comment) {
        if (status == null || status != CfDealWithStatusEnum.DEFERRED_CONTACT.getValue()) {
            return comment;
        }

        DeferContactReasonTypeEnum reasonTypeEnum = DeferContactReasonTypeEnum.parse(deferContactReasonType);
        if (reasonTypeEnum == null) {
            return comment;
        }

        return StringUtils.isBlank(comment) ? reasonTypeEnum.getDesc() : reasonTypeEnum.getDesc() + "-" + comment;
    }


    /**
     * 检查执行操作为不再处理的时候
     * 是否符合不再处理条件。并返回信息
     * @param crowdfundingInfo
     * @param cfDealWithStatusEnum
     * @return
     */
    private Response<String> checkHandleNoLongerProcessWithMsg(CrowdfundingInfo crowdfundingInfo, CfDealWithStatusEnum cfDealWithStatusEnum) {
        log.info("checkHandleNoLongerProcessWithMsg caseId:{}, status:{}", crowdfundingInfo.getId(), cfDealWithStatusEnum);

        // 仅处理不再处理才往下走
        if (cfDealWithStatusEnum != CfDealWithStatusEnum.DISREGARD) {
            return NewResponseUtil.makeSuccess("");
        }

        // 案例未结束 不可以执行 不再处理
        if (!CaseUtils.hasEnd(crowdfundingInfo, false)) {
            log.info("case no end caseId:{}, status:{}", crowdfundingInfo.getId(), JSON.toJSONString(crowdfundingInfo));
            return NewResponseUtil.makeError(AdminErrorCode.NO_LONGER_PROCESS_CHECK_CASE_NOT_END);
        }

        // 案例已筹金额为空可以执行 不再处理
        boolean emptyAmount = crowdfundingInfo.getAmount() <= 0;
        if (emptyAmount) {
            String msg = "案例结束&筹款金额为0";
            log.info(msg);
            return NewResponseUtil.makeSuccess(msg);
        }

        // 案例已申请退款 可以执行 不再处理
        Response<CfRefund> cfRefundResponse = financeDelegate.getCfRefundByInfoUuid(crowdfundingInfo.getInfoId());
        if (cfRefundResponse.notOk()) {
            return NewResponseUtil.makeError(AdminErrorCode.FINANCE_FEIGN_ERROR, null);
        }

        if (cfRefundResponse.ok() && cfRefundResponse.getData() != null) {
            String msg = "案例结束&筹款用户已经申请退款的案例";
            log.info(msg);
            return NewResponseUtil.makeSuccess(msg);
        }

        // 都不满足不可以执行 不再处理
        log.info("no match 不执行 '不再处理'");
        return NewResponseUtil.makeError(AdminErrorCode.NO_LONGER_PROCESS_CHECK_AMOUNT_NOT_EMPTY);
    }

    /**
     * 案例结束 或 筹款人发起退款时 尝试自动设置案例不再处理
     * @return
     */
    public OpResult touchHandleNoLongerProcess(int caseId){
        CrowdfundingInfo fundingInfo = crowdfundingDelegate.getFundingInfoById(caseId);
        String infoId = fundingInfo.getInfoId();

        CrowdfundingOperation crowdfundingOperation = adminCrowdfundingOperationBiz.getByInfoId(infoId);
        Integer operation = crowdfundingOperation.getOperation();
        CrowdfundingOperationEnum crowdfundingOperationEnum = CrowdfundingOperationEnum.fromValue(operation);
        // 检查是否已经是不再处理
        if (crowdfundingOperationEnum == CrowdfundingOperationEnum.NEVER_PROCESSING) {
            log.info("touchHandleNoLongerProcess has never processing caseId:{}", caseId);
            return OpResult.createSucResult();
        }

        // 检查是否可以自动处理 并获取备注内容
        Response<String> checkResult = checkHandleNoLongerProcessWithMsg(fundingInfo, CfDealWithStatusEnum.DISREGARD);
        if (checkResult.notOk()) {
            log.info("touchHandleNoLongerProcess checkResult notOk caseId:{}", caseId);
            return OpResult.createSucResult();
        }
        String comment = checkResult.getData();
        add(CfDealWithStatusEnum.DISREGARD.getValue(), comment, AdminUserIDConstants.SYSTEM, fundingInfo, 0);
        return OpResult.createSucResult();
    }

}
