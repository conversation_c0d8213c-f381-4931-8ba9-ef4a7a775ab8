package com.shuidihuzhu.cf.service.workorder.read.impl.finance;

import com.shuidihuzhu.cf.service.workorder.read.impl.AbstractFinanceWorkOrderCheckHandler;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 案例余额 surplusAmount eg: 1231.00 （单位：元）
 */
@Component
public class PrecipitationCheckHandler extends AbstractFinanceWorkOrderCheckHandler {
    @Override
    public WorkOrderType getWorkOrderType() {
        return WorkOrderType.precipitation;
    }
    @Override
    protected Map<String, Object> toMap(Object source) {
        Map<String, Object> res = super.toMap(source);
        // 案例余额
        updateMap(res, "surplusAmount", "caseBal");
        return res;
    }
}
