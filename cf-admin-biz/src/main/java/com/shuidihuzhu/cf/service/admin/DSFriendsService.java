package com.shuidihuzhu.cf.service.admin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.enums.crowdfunding.FriendsBizTypeEnum;
import com.shuidihuzhu.client.account.v1.accountservice.MobileUserIdResponse;
import com.shuidihuzhu.client.dataservice.faceApi.v1.FaceApiClient;
import com.shuidihuzhu.client.grpc.account.v1.SimpleUserAccountGrpcClient;
import com.shuidihuzhu.client.model.FriendDO;
import com.shuidihuzhu.client.model.RealtimeDO;
import com.shuidihuzhu.client.model.Response;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: zhengqiu
 **/
@Service
@Slf4j
@RefreshScope
public class DSFriendsService {

    @Autowired
    private FaceApiClient faceApiClient;

    public Set<Long> getUserAllFriends(long userId) {
        Set<Long> friendsIds = Sets.newHashSet();
        int maxCount = 200;
        String nextId = "";
        try {
            while (true) {
                Response<RealtimeDO> friendResponse = faceApiClient.getFriend(userId, "friend", nextId, "friend_ids", maxCount);
                if (null == friendResponse || friendResponse.getCode() != 0) {
                    log.debug("getFriend failure,userId:{}", userId);
                    break;
                }
                RealtimeDO dataObj = friendResponse.getData();
                if (null == dataObj) {
                    log.debug("getFriend failure,Data is empty,userId:{}, nextId={}", userId, nextId);
                    break;
                }
                JSONObject dataJson = JSONObject.parseObject(JSON.toJSONString(dataObj));
                Set<Long> friendSet = this.getFriendsUserIdSet(dataJson, maxCount);
                if (CollectionUtils.isEmpty(friendSet)) {
                    break;
                }
                friendsIds.addAll(friendSet);
                if (friendSet.size() < maxCount) {
                    break;
                }
                nextId = dataObj.getNextId();
                if (StringUtils.isEmpty(nextId)) {
                    break;
                }
            }
        } catch (Exception e) {
            log.error("userId:{}, 请求大数据出错", userId, e);
        }
        log.debug("userId:{},所有好友数量为: count={}", userId, friendsIds.size());
        return friendsIds;
    }

    private Set<Long> getFriendsUserIdSet(JSONObject jsonObject, int maxCount) {
        if (null == jsonObject) {
            return Sets.newHashSet();
        }

        //朋友圈,会话,其他
        Set<Long> friendsUserIdSet = Sets.newHashSet();

        try {
            JSONObject dataJsonObject = jsonObject.getJSONObject("friend");
            if (null == dataJsonObject){
                log.debug("Failed to resolve buddy data,friend is empty");
                return friendsUserIdSet;
            }
            String friendStrS = dataJsonObject.getString("friend_ids");
            if (StringUtils.isEmpty(friendStrS)) {
                log.debug("Failed to resolve buddy data,friend_ids is empty");
                return friendsUserIdSet;
            }
            List<String> userIdStrList = Arrays.asList(friendStrS.split(","));
            if (CollectionUtils.isEmpty(userIdStrList)) {
                log.debug("请求大数据的接口获得用户,好友关系为空");
                return friendsUserIdSet;
            }
            for (String userIdStr : userIdStrList) {
                if (friendsUserIdSet.size() >= maxCount) {
                    break;
                }
                if (StringUtils.isEmpty(userIdStr)) {
                    continue;
                }
                try {
                    friendsUserIdSet.add(Long.parseLong(userIdStr.trim()));
                } catch (Exception e) {
                    log.error("用户好友id异常,userId:{},好友的userIdString:{};",jsonObject.get("userId"),userIdStr,e);
                }
            }
        } catch (Exception e) {
            log.error("解析好友数据失败", e);
        }
        return friendsUserIdSet;
    }



    /**
     * 获取二度好友
     * @param userId
     * @return
     */
    public Set<Long> getFriendsUserIdSecondDegree(long userId){
        Set<Long> friendSetSecond = Sets.newHashSet();
        // 获取一度好友集合
        Set<Long> friendUserIdSet = getUserAllFriends(userId);
        friendSetSecond.addAll(friendUserIdSet);
        for (Long id : friendUserIdSet) {
            Set<Long> friendsUserIdSecondSet = getUserAllFriends(id);
            friendSetSecond.addAll(friendsUserIdSecondSet);
        }
        return friendSetSecond;
    }
}

