package com.shuidihuzhu.cf.service.stream;

/**
 * <AUTHOR>
 */
public interface StreamActionConst {

    /**
     * 重试
     */
    String RETRY = "retry";

    /**
     * 举报提醒时间到了
     */
    String REPORT_SCHEDULE_PAGE_SHOW = "report_schedule_page_show";

    /**
     * 提醒时间到了
     */
    String PAGE_SHOW = "page_show";


    /**
     * 举报待录入提醒时间到了
     */
    String REPORT_PENDING_ENTRY_PAGE_SHOW = "report_pending_entry_page_show";

    /**
     * 工单被分配
     */
    String ASSIGN = "assign";

    @interface Subject {
        String operator_group_assign = "operator_group_assign";
        String operator_string = "operator_string";
        String report_pending_entry = "report_pending_entry";
        String cailiao_fuwu = "cailiao_fuwu";
    }

}
