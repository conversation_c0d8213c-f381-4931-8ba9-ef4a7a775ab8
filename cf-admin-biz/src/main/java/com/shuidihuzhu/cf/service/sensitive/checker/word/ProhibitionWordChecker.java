package com.shuidihuzhu.cf.service.sensitive.checker.word;

import com.shuidihuzhu.cf.admin.constant.RiskControlWordConsts;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.service.sensitive.checker.ISensitiveChecker;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019-04-11  16:15
 */
@Service
public class ProhibitionWordChecker extends BaseSensitive<PERSON>ordChecker implements ISensitiveChecker {

    @Override
    protected long[] getCheckWordCategoryArray() {
        return RiskControlWordConsts.PROHIBITION_CATEGORY;
    }

    @Override
    public AdminWorkOrderConst.Task getTask() {
        return AdminWorkOrderConst.Task.FORBIDDEN_WORD;
    }
}
