package com.shuidihuzhu.cf.service.admin;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.clewtrack.model.ChannelRefine;
import com.shuidihuzhu.client.cf.clewtrack.param.ChannelRefineDTO;
import com.shuidihuzhu.client.cf.growthtool.client.CfChannelFeignClient;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * @Description: 案例归属渠道
 * @Author: panghairui
 * @Date: 2023/10/17 2:56 PM
 */
@Slf4j
@Service
public class CfChannelService {

    @Resource
    private CfChannelFeignClient cfChannelFeignClient;

    public ChannelRefine.ChannelRefineResuleEnum getCfChannel(CrowdfundingInfo crowdfundingInfo) {
        ChannelRefineDTO refineDTO = buildChannelDTO(crowdfundingInfo);
        Response<String> response = cfChannelFeignClient.getChannelByInfoIdWithUserIdAndOldChannel(refineDTO);
        log.info("CfChannelService refineDTO={} response={}", refineDTO, response);
        String channel = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse("");
        if (StringUtils.isBlank(channel)) {
            log.info("CfChannelService 无渠道信息 {}", crowdfundingInfo.getId());
            return null;
        }

        ChannelRefine.ChannelRefineResuleEnum channelRefineResuleEnum = ChannelRefine.ChannelRefineResuleEnum.parse(channel);
        if (Objects.isNull(channelRefineResuleEnum)) {
            log.info("CfChannelService 无渠道信息 {}", crowdfundingInfo.getId());
            return null;
        }

        return channelRefineResuleEnum;
    }

    private ChannelRefineDTO buildChannelDTO(CrowdfundingInfo crowdfundingInfo) {
        ChannelRefineDTO refineDTO = new ChannelRefineDTO();
        refineDTO.setInfoId((long) crowdfundingInfo.getId());
        refineDTO.setChannel(crowdfundingInfo.getChannel());
        refineDTO.setUserId(crowdfundingInfo.getUserId());
        return refineDTO;
    }
}
