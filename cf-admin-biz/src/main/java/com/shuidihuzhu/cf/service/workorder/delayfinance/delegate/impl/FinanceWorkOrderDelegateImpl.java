package com.shuidihuzhu.cf.service.workorder.delayfinance.delegate.impl;

import com.shuidihuzhu.cf.finance.client.feign.CfFinanceReadFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.model.vo.CfChangePayeeInfoVo;
import com.shuidihuzhu.cf.finance.model.vo.CfEndCaseWorkOrderVo;
import com.shuidihuzhu.cf.finance.model.vo.CfMoneyBackQueryVo;
import com.shuidihuzhu.cf.finance.model.vo.CfPromptDrawCashRecordVo;
import com.shuidihuzhu.cf.finance.model.vo.PrecipitationModelVo;
import com.shuidihuzhu.cf.finance.model.vo.drawcash.CaseSecondRecordV2Vo;
import com.shuidihuzhu.cf.finance.model.vo.drawcash.CfDrawCashApplyV2Vo;
import com.shuidihuzhu.cf.finance.model.vo.drawcash.CfDrawCashLongTailVo;
import com.shuidihuzhu.cf.finance.model.vo.refund.CfRefundApplyVo;
import com.shuidihuzhu.cf.service.workorder.delayfinance.delegate.IFinanceWorkOrderDelegate;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2022-12-01 3:55 下午
 **/
@Service
public class FinanceWorkOrderDelegateImpl implements IFinanceWorkOrderDelegate {

    @Autowired
    private CfFinanceReadFeignClient cfFinanceReadFeignClient;

    @Override
    public List<CfRefundApplyVo> getRefundApplyVo(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        FeignResponse<List<CfRefundApplyVo>> response = cfFinanceReadFeignClient.getRefundApplyByIds(ids);
        if (Objects.isNull(response) || response.notOk()) {
            return Collections.emptyList();
        }
        return response.getData();
    }

    @Override
    public List<CfMoneyBackQueryVo> getMoneyBackVo(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        FeignResponse<List<CfMoneyBackQueryVo>> response = cfFinanceReadFeignClient.getMoneyBackByIds(ids);
        if (Objects.isNull(response) || response.notOk()) {
            return Collections.emptyList();
        }
        return response.getData();
    }

    @Override
    public List<PrecipitationModelVo> getPrecipitationVo(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        FeignResponse<List<PrecipitationModelVo>> response = cfFinanceReadFeignClient.getPrecipitationByIds(ids);
        if (Objects.isNull(response) || response.notOk()) {
            return Collections.emptyList();
        }
        return response.getData();
    }

    @Override
    public List<CfEndCaseWorkOrderVo> getEndCaseVo(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        FeignResponse<List<CfEndCaseWorkOrderVo>> response = cfFinanceReadFeignClient.getEndCaseByIds(ids);
        if (Objects.isNull(response) || response.notOk()) {
            return Collections.emptyList();
        }
        return response.getData();
    }

    @Override
    public List<CfPromptDrawCashRecordVo> getPromptDrawCashRecordVo(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        FeignResponse<List<CfPromptDrawCashRecordVo>> response = cfFinanceReadFeignClient.getPromptDrawCashRecordByIds(ids);
        if (Objects.isNull(response) || response.notOk()) {
            return Collections.emptyList();
        }
        return response.getData();
    }

    @Override
    public List<CfDrawCashApplyV2Vo> getDrawCashApplyVo(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        FeignResponse<List<CfDrawCashApplyV2Vo>> response = cfFinanceReadFeignClient.getDrawCashApplyVoByIds(ids);
        if (Objects.isNull(response) || response.notOk()) {
            return Collections.emptyList();
        }
        return response.getData();
    }

    @Override
    public List<CfDrawCashLongTailVo> getDrawCashLongTailVo(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        FeignResponse<List<CfDrawCashLongTailVo>> response = cfFinanceReadFeignClient.getDrawCashLongTailVoByIds(ids);
        if (Objects.isNull(response) || response.notOk()) {
            return Collections.emptyList();
        }
        return response.getData();
    }

    @Override
    public List<CaseSecondRecordV2Vo> getSecondRecordVo(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        FeignResponse<List<CaseSecondRecordV2Vo>> response = cfFinanceReadFeignClient.getSecondRecordVoByIds(ids);
        if (Objects.isNull(response) || response.notOk()) {
            return Collections.emptyList();
        }
        return response.getData();
    }

    @Override
    public List<CfChangePayeeInfoVo> getChangePayeeInfoVo(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        FeignResponse<List<CfChangePayeeInfoVo>> response = cfFinanceReadFeignClient.getChangePayeeInfoVoByIds(ids);
        if (Objects.isNull(response) || response.notOk()) {
            return Collections.emptyList();
        }
        return response.getData();
    }
}
