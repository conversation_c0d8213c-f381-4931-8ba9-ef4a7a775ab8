package com.shuidihuzhu.cf.service.crowdfunding;

import com.shuidihuzhu.cf.dao.crowdfunding.AdminBlessingCardDao;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminBlessingCardVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/25  10:51 上午
 */
@Slf4j
@Service
public class AdminBlessingCardService {
    @Autowired
    private AdminBlessingCardDao adminBlessingCardDao;

    public int blessingCardAddAndUpdate(int id, String image, String text, int amount, int userId) {
        if (id <= 0) {
            adminBlessingCardDao.addBlessingCard(image, text, amount, userId);
        } else {
            adminBlessingCardDao.updateBlessingCard(id, image, text, amount, userId);
        }
        AdminBlessingCardVo adminBlessingCardVo = adminBlessingCardDao.getIdByText(text);
        if (adminBlessingCardVo == null) {
            return 0;
        }
        return adminBlessingCardVo.getId();
    }

    public void blessingCardDel(int id, int userId) {
        if (id <= 0) {
            return;
        }
        adminBlessingCardDao.delBlessingCard(id, userId);
    }

    public List<AdminBlessingCardVo> getList() {
        return adminBlessingCardDao.getList();
    }

    public List<AdminBlessingCardVo> selectListByImageOrText(String image,String text,int amount){
        return adminBlessingCardDao.selectListByImageOrText(image,text,amount);
    }
}
