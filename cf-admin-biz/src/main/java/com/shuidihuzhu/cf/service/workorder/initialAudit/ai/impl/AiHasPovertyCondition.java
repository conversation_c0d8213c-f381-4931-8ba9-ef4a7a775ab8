package com.shuidihuzhu.cf.service.workorder.initialAudit.ai.impl;

import com.shuidihuzhu.cf.admin.river.impl.RiverDiBaoFacadeImpl;
import com.shuidihuzhu.cf.model.crowdfunding.ai.AiConditionEnum;
import com.shuidihuzhu.cf.service.workorder.initialAudit.ai.AiCondition;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditCaseDetail;
import com.shuidihuzhu.cf.vo.approve.InitialAuditAdditionInfoVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @DATE 2020/12/21
 */
@Service("aiHasPoverty")
public class AiHasPovertyCondition implements AiCondition {

    @Autowired
    private RiverDiBaoFacadeImpl riverDiBaoFacade;


    @Override
    public boolean check(int caseId, String inputValue) {

        if (StringUtils.isEmpty(inputValue) || inputValue.equals(AiConditionEnum.wumiaoshu.getCode()+"")){
            return false;
        }

        InitialAuditAdditionInfoVO vo = riverDiBaoFacade.getInfo(caseId);

        Integer c = Optional.ofNullable(vo).map(InitialAuditAdditionInfoVO::getDiBaoAndPinKunInfo).map(InitialAuditCaseDetail.CfBasicLivingGuardView::getHasPoverty).orElse(0);

        if (inputValue.equals(AiConditionEnum.fou.getCode()+"")){
            return !c.equals(0);
        }

        if (inputValue.equals(AiConditionEnum.shi.getCode()+"")){
            return !c.equals(1);
        }


        return false;
    }
}
