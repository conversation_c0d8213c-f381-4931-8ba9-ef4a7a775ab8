package com.shuidihuzhu.cf.service.workorder.flow;

import com.shuidihuzhu.cf.vo.crowdfunding.CrowdfundingApproveCommentVo;

import java.util.Comparator;

/**
 * <AUTHOR>
 * @date 2019-04-29  15:58
 */
public class WorkOrderFlowCommentsComparator implements Comparator<CrowdfundingApproveCommentVo> {

    @Override
    public int compare(CrowdfundingApproveCommentVo o1, CrowdfundingApproveCommentVo o2) {
        return o1.getOprtime().compareTo(o2.getOprtime());
    }
}
