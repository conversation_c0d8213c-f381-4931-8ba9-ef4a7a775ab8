package com.shuidihuzhu.cf.service.admin;

import com.shuidihuzhu.cf.model.crowdfunding.AdminCfInfoMirrorRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoMirrorRecord;
import com.shuidihuzhu.cipher.ShuidiCipher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AdminCfInfoMirrorRecordService {
    @Autowired
    private ShuidiCipher shuidiCipher;

    public AdminCfInfoMirrorRecord.AdminPayeeInfo buildAdminPayeeInfo(CfInfoMirrorRecord.PayeeInfo payeeInfo) {
        if (payeeInfo == null) {
            return null;
        }
        AdminCfInfoMirrorRecord record = new AdminCfInfoMirrorRecord();
        AdminCfInfoMirrorRecord.AdminPayeeInfo adminPayeeInfo = record.new AdminPayeeInfo();
        BeanUtils.copyProperties(payeeInfo, adminPayeeInfo);
        return this.set(adminPayeeInfo, payeeInfo);
    }

    public AdminCfInfoMirrorRecord.AdminPayeeInfo set(AdminCfInfoMirrorRecord.AdminPayeeInfo adminPayeeInfo,
                                                      CfInfoMirrorRecord.PayeeInfo payeeInfo) {
        if (payeeInfo == null) {
            return null;
        }
        try {
            adminPayeeInfo.setDecodeIdCard(shuidiCipher.decrypt(payeeInfo.getIdCard()));
        } catch (Exception e) {
            log.error("", e);
        }
        try {
            adminPayeeInfo.setDecodeMobile(shuidiCipher.decrypt(payeeInfo.getMobile()));
        } catch (Exception e) {
            log.error("", e);
        }
        try {
            adminPayeeInfo.setDecodeBankCard(shuidiCipher.decrypt(payeeInfo.getBankCard()));
        } catch (Exception e) {
            log.error("", e);
        }
        return adminPayeeInfo;
    }

    public AdminCfInfoMirrorRecord.AdminPatientInfo buildAdminPatientInfo(CfInfoMirrorRecord.PatientInfo patientInfo) {
        if(patientInfo == null){
            return null;
        }
        AdminCfInfoMirrorRecord record = new AdminCfInfoMirrorRecord();
        AdminCfInfoMirrorRecord.AdminPatientInfo adminPatientInfo = record.new AdminPatientInfo();
        BeanUtils.copyProperties(patientInfo, adminPatientInfo);
        return this.setPatientInfo(adminPatientInfo, patientInfo);
    }

    public AdminCfInfoMirrorRecord.AdminPatientInfo setPatientInfo(AdminCfInfoMirrorRecord.AdminPatientInfo adminPatientInfo,
                                                        CfInfoMirrorRecord.PatientInfo patientInfo) {
        if (patientInfo == null) {
            return adminPatientInfo;
        }
        try {
            adminPatientInfo.setDecodeIdCard(shuidiCipher.decrypt(patientInfo.getIdCard()));
        } catch (Exception e) {
            log.error("", e);
        }
        return adminPatientInfo;
    }
}
