package com.shuidihuzhu.cf.service.shutdown;

import brave.Tracing;
import com.google.common.collect.Maps;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.constants.admin.GeneralConstant;
import com.shuidihuzhu.cf.enums.shutdown.BizTypeEnum;
import com.shuidihuzhu.cf.enums.shutdown.ExecuteStatusEnum;
import com.shuidihuzhu.cf.event.DrawCashEvent;
import com.shuidihuzhu.infra.gracefulshutdown.service.OrderedShutdown;

import org.springframework.context.ApplicationListener;
import org.springframework.core.Ordered;
import org.springframework.util.StopWatch;

import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @time 2018/11/27 上午11:54
 * @desc
 */
@Slf4j
public class DrawCashShutdown implements OrderedShutdown, ApplicationListener<DrawCashEvent> {
    private Map<BizTypeEnum, AtomicInteger> runingStatus = Maps.newConcurrentMap();

    @Resource
    private Tracing tracing;

    @Override
    public void pause() throws InterruptedException {

    }

    @Override
    public void shutdown(Integer delay) throws InterruptedException {
        GeneralConstant.graceful_shutdown_forbidden_operate = true;

        log.info("DrawCashShutdown shutdown start...runingStatus:{}", JSON.toJSONString(runingStatus));

        int runCount = 0;
        for (AtomicInteger atomicInteger : runingStatus.values()){
            if(atomicInteger.get() > 0){
                runCount++;
            }
        }

        if(runingStatus.size() <= 0 || runCount <= 0){
            return;
        }

        CountDownLatch countDownLatch = new CountDownLatch(runCount);

        ExecutorService executorService = tracing.currentTraceContext().executorService(Executors.newFixedThreadPool(runCount));

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        for (BizTypeEnum bizType : runingStatus.keySet()){
            if(runingStatus.get(bizType).get() > 0){
                executorService.execute(() -> {
                    threadRun(bizType, runingStatus);
                    countDownLatch.countDown();
                });
            }
        }

        try {

            log.info("DrawCashShutdown CountDownLatch await start...");
            countDownLatch.await();
            stopWatch.stop();
            log.info("DrawCashShutdown CountDownLatch await end...use {}Seconds", stopWatch.getTotalTimeSeconds());

        } catch (Exception e) {
            e.printStackTrace();
        }

        executorService.shutdown();

        this.runingStatus.clear();
    }

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE;
    }

    @Override
    public void onApplicationEvent(DrawCashEvent drawCashEvent) {
        BizTypeEnum bizType = drawCashEvent.getBizType();
        ExecuteStatusEnum executeStatus = drawCashEvent.getExecuteStatus();

        log.info("DrawCashShutdown receive DrawCashEvent, original:{}", JSON.toJSONString(runingStatus));
        stat(bizType, executeStatus);
        log.info("DrawCashShutdown receive DrawCashEvent, target:{}", JSON.toJSONString(runingStatus));
    }

    private void stat(BizTypeEnum bizType, ExecuteStatusEnum executeStatus){
        if(runingStatus.containsKey(bizType)){
            if(ExecuteStatusEnum.start == executeStatus){
                runingStatus.get(bizType).incrementAndGet();
            } else {
                runingStatus.get(bizType).decrementAndGet();
            }
        } else {
            if(ExecuteStatusEnum.start == executeStatus){
                runingStatus.put(bizType, new AtomicInteger(1));
            } else {
                runingStatus.put(bizType, new AtomicInteger(0));
            }
        }
    }

    private void threadRun(BizTypeEnum bizType, Map<BizTypeEnum, AtomicInteger> runingStatus){
        try {
            log.info("DrawCashShutdown thread pool shutdown start... operation:{}, count:{}", bizType.getChinese(), runingStatus.get(bizType));
            Thread.sleep(bizType.getTimeout());
            log.info("DrawCashShutdown thread pool shutdown end... operation:{}, count:{}", bizType.getChinese(), runingStatus.get(bizType));

        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
}
