package com.shuidihuzhu.cf.service.sensitive.checker;

import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordResult;
import com.shuidihuzhu.cf.service.sensitive.adapter.ISensitiveAdapter;

/**
 * <AUTHOR>
 * @date 2019-04-11  14:45
 *
 * 各种字符类检查项
 */
public interface ISensitiveChecker {

    AdminWorkOrderConst.Task getTask();

    <T> OpResult<RiskWordResult> isHit(T data, ISensitiveAdapter<T> adapter);
}
