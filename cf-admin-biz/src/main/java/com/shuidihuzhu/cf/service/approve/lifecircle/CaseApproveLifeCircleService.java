package com.shuidihuzhu.cf.service.approve.lifecircle;

import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.vo.approve.ApproveLifeCircleVO;
import com.shuidihuzhu.client.cf.admin.model.CfCaseApproveDetail;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface CaseApproveLifeCircleService {

    OpResult<List<ApproveLifeCircleVO>> getLifeCircle(int caseId);

    Map<Integer, CfCaseApproveDetail> getCaseApproveDetail(List<Integer> caseIds);
}
