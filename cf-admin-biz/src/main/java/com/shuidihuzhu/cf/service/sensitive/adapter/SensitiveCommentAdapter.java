package com.shuidihuzhu.cf.service.sensitive.adapter;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingOrderBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdFundingProgressDao;
import com.shuidihuzhu.cf.enums.crowdfunding.CfSensitiveWordRecordEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingCommentType;
import com.shuidihuzhu.cf.enums.risk.UgcTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfSensitiveWordRecordVo;
import com.shuidihuzhu.cf.model.crowdfunding.deliver.CrowdfundingCommentDeliver;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2019-03-14  14:45
 */
@Service
public class SensitiveCommentAdapter implements ISensitiveAdapter<CrowdfundingCommentDeliver> {

    @Autowired
    private AdminCrowdFundingProgressDao adminCrowdFundingProgressDao;

    @Autowired
    private AdminCrowdfundingOrderBiz adminCrowdfundingOrderBiz;

    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Override
    public String getContent(CrowdfundingCommentDeliver v) {
        return v.getContent();
    }

    @Override
    public CfSensitiveWordRecordVo buildRecord(CrowdfundingCommentDeliver v, String hitWord) {
        return buildRecordByComment(v, hitWord);
    }

    @Override
    public long getBizId(CrowdfundingCommentDeliver data) {
        return data.getId();
    }

    @Override
    public int getCaseId(CrowdfundingCommentDeliver data) {
        return data.getCrowdfundingId();
    }

    @Override
    public UgcTypeEnum getUgcTypeEnum() {
        return UgcTypeEnum.COMMENT;
    }

    @Override
    public boolean needAutoHandle() {
        return true;
    }

    @Override
    public String getUniquelyIdentifies(CrowdfundingCommentDeliver crowdfundingComment) {
        return crowdfundingComment.getUniquelyIdentifies();
    }

    /**
     * https://wdh.feishu.cn/wiki/wikcnSxa4FZzw24EjJTSvy1Fs1b
     * 筹款人对订单评论不过ai
     */
    @Override
    public boolean needAiCheck(CrowdfundingCommentDeliver data) {
        if(data.getType() != CrowdfundingCommentType.CONTRIBUTE_RECORD.value()){
            return true;
        }
        final CrowdfundingInfo crowdfundingInfo = getCrowdfundingInfo(data);
        if (crowdfundingInfo == null) {
            return true;
        }
        if (data.getUserId() == crowdfundingInfo.getUserId()){
            return false;
        }
        return true;
    }

    @Override
    public CfSensitiveWordRecordEnum.BizType getSensitiveRecordBizType(CrowdfundingCommentDeliver comment) {
        Integer type = comment.getType();
        CfSensitiveWordRecordEnum.BizType bizType = null;
        if (type == CrowdfundingCommentType.CONTRIBUTE_RECORD.value()) {
            bizType = CfSensitiveWordRecordEnum.BizType.COMMENT_ORDER;
        } else if (type == CrowdfundingCommentType.CROWDFUNDING_TRENDS.value()) {
            bizType = CfSensitiveWordRecordEnum.BizType.COMMENT_PROGRESS;
        }
        return bizType;
    }

    @Override
    public long getUserId(CrowdfundingCommentDeliver data) {
        return data.getUserId();
    }

    @NotNull
    private CfSensitiveWordRecordVo buildRecordByComment(CrowdfundingCommentDeliver comment, String hitsWord) {
        String content = comment.getContent();
        CfSensitiveWordRecordVo cfSensitiveWordRecord = new CfSensitiveWordRecordVo();
        CrowdfundingInfo crowdfundingInfo = getCrowdfundingInfo(comment);
        CfSensitiveWordRecordEnum.BizType bizType = getSensitiveRecordBizType(comment);
        fillCaseInfo(comment, cfSensitiveWordRecord, crowdfundingInfo);
        cfSensitiveWordRecord.setUserId(comment.getUserId());
        cfSensitiveWordRecord.setBizType(bizType.value());
        cfSensitiveWordRecord.setBizId(comment.getId());
        cfSensitiveWordRecord.setParentBizId(comment.getParentId());
        cfSensitiveWordRecord.setSensitiveWord(hitsWord);
        cfSensitiveWordRecord.setBizTime(new Timestamp(System.currentTimeMillis()));
        cfSensitiveWordRecord.setContent(content);
        return cfSensitiveWordRecord;
    }

    @Nullable
    private CrowdfundingInfo getCrowdfundingInfo(CrowdfundingCommentDeliver comment) {
        CrowdfundingInfo crowdfundingInfo = null;

        long parentId = comment.getParentId();
        if (comment.getType() == CrowdfundingCommentType.CONTRIBUTE_RECORD.value()) {
            crowdfundingInfo = getCrowdfundingInfoByOrderComment(parentId);
        } else if (comment.getType() == CrowdfundingCommentType.CROWDFUNDING_TRENDS.value()) {
            crowdfundingInfo = getCrowdfundingInfoByProgressComment(Math.toIntExact(parentId));
        }
        return crowdfundingInfo;
    }


    /**
     * @param parentId
     * @return
     */
    private CrowdfundingInfo getCrowdfundingInfoByProgressComment(Integer parentId) {
        CrowdFundingProgress progress = adminCrowdFundingProgressDao.getActivityProgressById(parentId);
        if (progress == null) {
            return null;
        }
        return adminCrowdfundingInfoBiz.getFundingInfoById(progress.getActivityId());
    }

    private CrowdfundingInfo getCrowdfundingInfoByOrderComment(Long parentId) {
        CrowdfundingOrder order = this.adminCrowdfundingOrderBiz.getById(parentId);
        if (order == null) {
            return null;
        }
        return this.adminCrowdfundingInfoBiz.getFundingInfoById(order.getCrowdfundingId());
    }
}
