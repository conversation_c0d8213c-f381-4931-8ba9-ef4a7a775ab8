package com.shuidihuzhu.cf.service.workorder.juanzhan;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.activity.feign.CfActivityFeignClient;
import com.shuidihuzhu.cf.activity.model.ActivityDetail;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingOrderBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingReportBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfFirstApproveOperatorBiz;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.delegate.SeaAccountDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICfInfoXXXRecordDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.facade.AdminApolloCofig;
import com.shuidihuzhu.cf.model.admin.workorder.JuanzhanOrderCreate;
import com.shuidihuzhu.cf.model.admin.workorder.JuanzhuanCaseDetail;
import com.shuidihuzhu.cf.model.admin.workorder.JuanzhuanDetailVo;
import com.shuidihuzhu.cf.model.admin.workorder.JuanzhuanOrderDetail;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewTrackCallRobotClient;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackTaskFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CallRobotModel;
import com.shuidihuzhu.client.cf.clewtrack.model.CfUserInvitedLaunchCaseRecordModel;
import com.shuidihuzhu.client.cf.clewtrack.model.ClewDonationTransferModel;
import com.shuidihuzhu.client.cf.workorder.CfJuanzhuanWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderTypeFeignClient;
import com.shuidihuzhu.client.cf.workorder.model.JuanzhuanHandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.JuanzhuanWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.*;
import com.shuidihuzhu.client.feign.CfClewtrackClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2020/5/8
 */
@Service
@Slf4j
public class JuanzhuanWorkOrderService {

    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Autowired
    private AdminCfInfoExtBiz adminCfInfoExtBiz;

    @Autowired
    private AdminCrowdfundingReportBiz reportBiz;

    @Autowired
    private AdminCrowdfundingOrderBiz adminCrowdfundingOrderBiz;

    @Autowired
    private CfJuanzhuanWorkOrderClient client;

    @Autowired
    private SeaAccountDelegate seaAccountDelegate;

    @Autowired
    private CfActivityFeignClient activityFeignClient;

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @Autowired(required = false)
    private Producer producer;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;

    @Autowired
    private ICfInfoXXXRecordDelegate cfInfoXXXRecordDelegate;

    @Autowired
    private CfClewtrackFeignClient clewtrackFeignClient;


    @Autowired
    private IRiskDelegate riskDelegate;

    @Autowired
    private CfClewTrackCallRobotClient cfClewTrackCallRobotClient;

    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private AdminApproveService adminApproveService;

    @Autowired
    private CfFirstApproveOperatorBiz cfFirstApproveOperatorBiz;

    @Autowired
    private CfClewtrackClient cfClewtrackClient;

    @Autowired
    private CfClewtrackTaskFeignClient taskFeignClient;

    @Autowired
    private CfWorkOrderTypeFeignClient cfWorkOrderTypeFeignClient;

    @Autowired
    private MaskUtil maskUtil;
    public void caseEnd4Close(int caseId){
        if (caseId <= 0){
            return;
        }
        client.closeJuanzhuanOrder(caseId,"案例结束关闭");
    }

    public void donation4Close(int caseId){

        //已达标关闭，指的是当前任务的案例，已达到当天设置的捐单标准，无需再拨打；
        CfInfoStat cfInfoStat = crowdfundingDelegate.getById(caseId);

        if (cfInfoStat == null){
            log.info("donation4Close cfInfoStat=null caseId={}",caseId);
            return;
        }

        String count =  AdminApolloCofig.getValueFromApollo(AdminApolloCofig.cf_donation_count,"550");
        if(cfInfoStat.getDonationCount() >= Integer.valueOf(count)){
            log.info("juanzhuan close caseId={} count={} getDonationCount={}",caseId,count,cfInfoStat.getDonationCount());
            //查询所有处理中的工单
            Response<List<WorkOrderVO>> response = cfWorkOrderClient.listByCaseIdAndTypeAndResult(caseId,
                    Optional.ofNullable(cfWorkOrderTypeFeignClient.getByOneLevel(OneTypeEnum.juanzhuan.getType()))
                    .filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList()),
                    HandleResultEnum.unDoResult());

            List<WorkOrderVO> list = Optional.ofNullable(response)
                    .filter(Response::ok)
                    .map(Response::getData)
                    .orElse(Lists.newArrayList());

            if (CollectionUtils.isEmpty(list)){
                return;
            }

            list.stream().filter(r->JuanzhuanWorkOrder.d0_show_name_2.equals(r.getShowName())
                            || JuanzhuanWorkOrder.d1_show_name_2.equals(r.getShowName())
                            || JuanzhuanWorkOrder.d2_show_name_2.equals(r.getShowName()))
                    .forEach(r->{

                        JuanzhuanHandleOrderParam param = new JuanzhuanHandleOrderParam();
                        param.setWorkOrderId(r.getWorkOrderId());
                        param.setOrderType(r.getOrderType());
                        param.setHandleResult(HandleResultEnum.exception_done.getType());
                        param.setOperComment("已达标关闭");
                        param.setShareCount(cfInfoStat.getShareCount()+"");
                        param.setDonationCount(cfInfoStat.getDonationCount()+"");
                        param.setAmount(cfInfoStat.getAmount()+"");
                        client.hanldeJuanzhuan(param);

                    });
        }
    }

    public void report4Close(int caseId){
        if (caseId <= 0){
            return;
        }
        client.closeJuanzhuanOrder(caseId,"举报关闭");
    }

    public void rob4Close(int caseId){
        if (caseId <= 0){
            return;
        }
        client.closeJuanzhuanOrder(caseId,"线下抢单");
    }

    public OpResult<JuanzhuanDetailVo> getJuanzhuanDetailVo(int caseId,long workOrderId){
        JuanzhuanDetailVo jv = new JuanzhuanDetailVo();

        OpResult<JuanzhuanCaseDetail> caseDetailOpResult = getJuanzhuanCaseDetail(caseId,workOrderId);

        if (caseDetailOpResult.isFail()){
            return OpResult.createFailResult(caseDetailOpResult.getErrorCode());
        }

        JuanzhuanCaseDetail caseDetail = caseDetailOpResult.getData();
        caseDetail.setPhoneMask(maskUtil.buildByDecryptPhone(caseDetail.getPhone()));
        caseDetail.setPhone(null);
        caseDetail.setNewPhoneMask(maskUtil.buildByDecryptPhone(caseDetail.getNewPhone()));
        caseDetail.setNewPhone(null);
        jv.setCaseDetail(caseDetail);

        //查询所有处理完成的
        Response<List<WorkOrderVO>> response = cfWorkOrderClient.listByCaseIdAndTypeAndResult(caseId,
                Optional.ofNullable(cfWorkOrderTypeFeignClient.getByOneLevel(OneTypeEnum.juanzhuan.getType()))
                .filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList()),
                Lists.newArrayList(HandleResultEnum.done.getType(), HandleResultEnum.later_doing.getType(), HandleResultEnum.exception_done.getType()));

        List<WorkOrderVO> orderVOs = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(Lists.newArrayList());

        List<WorkOrderVO> historyHandleVos = new ArrayList<>(orderVOs);

        Optional<WorkOrderVO> laterDoingWorkVo = orderVOs.stream()
                .filter(item -> item.getWorkOrderId() == workOrderId && item.getHandleResult() == HandleResultEnum.later_doing.getType())
                .findFirst();

        //历史任务记录
        if (CollectionUtils.isNotEmpty(historyHandleVos)){

            List<Integer> operatorIds = historyHandleVos.stream().map(WorkOrderVO::getOperatorId).map(Long::intValue).collect(Collectors.toList());
            Map<Integer,String> userMap = seaAccountDelegate.getNameByUserIds(operatorIds);

            List<Integer> caseIds = historyHandleVos.stream().map(WorkOrderVO::getCaseId).collect(Collectors.toList());

            Map<Integer, CfInfoStat> statMap = crowdfundingDelegate.mapByIds(caseIds);

            List<JuanzhuanOrderDetail> orderDetails = historyHandleVos.stream().map(orderVO -> {
                JuanzhuanOrderDetail orderDetail = new JuanzhuanOrderDetail();
                BeanUtils.copyProperties(orderVO, orderDetail);
                CfInfoStat cs = statMap.get(orderDetail.getCaseId());
                if (cs != null){
                    orderDetail.setAmountNow(cs.getAmount() - Optional.ofNullable(orderVO.getAmount()).orElse(0));
                    orderDetail.setDonationCountNow(cs.getDonationCount() - Optional.ofNullable(orderVO.getDonationCount()).orElse(0));
                    orderDetail.setShareCountNow(cs.getShareCount() - Optional.ofNullable(orderVO.getShareCount()).orElse(0));
                }
                orderDetail.setUserName(userMap.get(Long.valueOf(orderDetail.getOperatorId()).intValue()));
                return orderDetail;
            }).collect(Collectors.toList());

            jv.setOrderDetails(orderDetails);
        }

        if (laterDoingWorkVo.isPresent()) {
            WorkOrderVO workOrderVO = laterDoingWorkVo.get();
            JuanzhuanOrderDetail orderDetail = new JuanzhuanOrderDetail();
            BeanUtils.copyProperties(workOrderVO, orderDetail);
            jv.setLaterDoingDetail(orderDetail);
        }

        //只能外呼展示
        JuanzhuanCaseDetail juanzhuanCaseDetail = jv.getCaseDetail();
        String phone = juanzhuanCaseDetail.getPhone();
        if (StringUtils.isEmpty(phone)){
            phone = juanzhuanCaseDetail.getNewPhone();
        }
        phone = shuidiCipher.encrypt(phone);
        Response<CallRobotModel> callRobotModelResponse = cfClewTrackCallRobotClient.queryCallRobotInfoByTaskId(workOrderId+"",phone);
        log.info("queryCallRobotInfoByTaskId workOrderId={} phone={} callRobotModelResponse={}",workOrderId,phone,callRobotModelResponse);
        CallRobotModel ct = Optional.ofNullable(callRobotModelResponse)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);
        jv.setCallDetail(ct);

        return OpResult.createSucResult(jv);
    }



    private OpResult<JuanzhuanCaseDetail> getJuanzhuanCaseDetail(int caseId, long workOrderId){

        //案例信息
        JuanzhuanCaseDetail caseDetail = new JuanzhuanCaseDetail();
        RpcResult<ActivityDetail> rpcResult = activityFeignClient.getActivityForRaiser(caseId);

        Optional<ActivityDetail> activityDetailOpt = Optional.ofNullable(rpcResult)
                .filter(RpcResult::isSuccess)
                .map(RpcResult::getData);

        String activityName = activityDetailOpt
                .map(ActivityDetail::getActivityName)
                .orElse("");
        caseDetail.setActivityName(activityName);

        if (activityDetailOpt.isPresent()) {
            ActivityDetail activityDetail = activityDetailOpt.get();
            caseDetail.setActivityStatus(activityDetail.getStatus().getCode());
            caseDetail.setActivityAmount(activityDetail.getCurrentMoney());
        }


        CrowdfundingInfo c = crowdfundingDelegate.getCaseInfoById(caseId);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        caseDetail.setCaseId(caseId);
        caseDetail.setInfoId(c.getInfoId());
        caseDetail.setTitle(c.getTitle());
        caseDetail.setContent(c.getContent());
        caseDetail.setCaseEnd(c.getEndTime().before(new Date())?"已结束":"未结束");
        caseDetail.setCailiaoStatus(c.getStatus().getApproveMsg());
        caseDetail.setCreateTime(sdf.format(c.getCreateTime()));
        caseDetail.setTargetAmount(c.getTargetAmount());

        CfInfoStat cfInfoStat = crowdfundingDelegate.getById(caseId);
        caseDetail.setAmount(cfInfoStat.getAmount());
        caseDetail.setDonationCount(cfInfoStat.getDonationCount());
        caseDetail.setShareCount(cfInfoStat.getShareCount());

        CfInfoExt cfInfoExt = adminCfInfoExtBiz.getByCaseId(caseId);
        if (FirstApproveStatusEnum.APPLY_SUCCESS.getCode() == cfInfoExt.getFirstApproveStatus()){
            caseDetail.setChuciTime(sdf.format(cfInfoExt.getFirstApproveTime()));
        }

        List<CrowdfundingAttachmentVo> attachmentsByType = crowdfundingDelegate.getAttachmentsByType(caseId, AttachmentTypeEnum.ATTACH_CF);
        List<String> urls = attachmentsByType.stream().map(CrowdfundingAttachmentVo::getUrl).collect(Collectors.toList());
        caseDetail.setPic(StringUtils.join(urls, ","));

        UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByUserId(c.getUserId());
        caseDetail.setPhone(shuidiCipher.decrypt(userInfoModel.getCryptoMobile()));

        Response<ClewDonationTransferModel> modelResponse = clewtrackFeignClient.getClew4DonationTransferByInfoId(c.getInfoId());
        log.debug("getClew4DonationTransferByInfoId infoid={} result={} phone={}",c.getInfoId(), JSON.toJSONString(modelResponse),caseDetail.getPhone());
        ClewDonationTransferModel model = Optional.ofNullable(modelResponse).filter(Response::ok)
                .map(Response::getData).filter(ClewDonationTransferModel::getIsExistFuwuTask).orElse(null);

        //1）如果原手机号与案例发起手机号一致，显示案例发起手机号以及新手机号；
        //2）如果新手机号与案例发起手机号一致，显示案例发起手机号以及原手机号；
        if (model != null){
            if (model.getPhone().equals(caseDetail.getPhone())){
                caseDetail.setNewPhone(model.getExchangePhone());
            }
            if (model.getExchangePhone().equals(caseDetail.getPhone())){
                caseDetail.setNewPhone(model.getPhone());
            }
        }

        List<CfInfoShareRecord> records = cfInfoXXXRecordDelegate.getByInfoId(caseId,c.getCreateTime().getTime(),System.currentTimeMillis(),0,1);
        String firstTime = Optional.ofNullable(records)
                .filter(CollectionUtils::isNotEmpty)
                .map(r->r.get(0))
                .map(CfInfoShareRecord::getDateCreated)
                .map(r->sdf.format(r)).orElse("");

        caseDetail.setFirstTime(firstTime);

        caseDetail.setTargetDonationCount(Integer.valueOf(AdminApolloCofig.getValueFromApollo(AdminApolloCofig.cf_donation_count,"100")));

        Response<WorkOrderVO> response = cfWorkOrderClient.getWorkOrderById(workOrderId);
        WorkOrderVO workOrderVO = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);

        if (workOrderVO != null){
            caseDetail.setShowName(workOrderVO.getShowName());
        }

        CfFirsApproveMaterial material = riskDelegate.getCfFirsApproveMaterialByInfoId(caseId);
        if (material != null){
            caseDetail.setPatientName(material.getPatientRealName());
        }


        String channel = getRaiseChannel(caseId);
        // bd发起的不生成
        boolean isBd = StringUtils.equals("线下筹款顾问", channel);

        boolean is1v1fuwu = checkIs1v1fuwu(caseId);

        caseDetail.setNoService(!isBd && !is1v1fuwu);

        return OpResult.createSucResult(caseDetail);
    }


    public boolean canCreate(JuanzhanOrderCreate create){
        int caseId = create.getCaseId();
        boolean isSecond = create.isCreateSecond();
        boolean isBegin = create.isCreateBegin();

        //案例状态：案例未结束 且 初审通过。
        CfInfoExt cfInfoExt = adminCfInfoExtBiz.getByCaseId(caseId);
        if (FirstApproveStatusEnum.APPLY_SUCCESS.getCode() != cfInfoExt.getFirstApproveStatus()){
            log.info("juanzhuan caseId={} getFirstApproveStatus={}",caseId,cfInfoExt.getFirstApproveStatus());
            return false;
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingDelegate.getCaseInfoById(caseId);
        create.setInfoId(crowdfundingInfo.getInfoId());
        if (crowdfundingInfo.getEndTime().before(new Date())){
            log.info("juanzhuan caseId={} getEndTime={}",caseId,crowdfundingInfo.getEndTime());
            return false;
        }



        //（4）风控：案例未被举报。
        CrowdfundingReport report = reportBiz.getByInfoId(caseId);
        if (report !=null){
            log.info("juanzhuan caseId={} report={}",caseId,report.getId());
            return false;
        }

        if (isBegin) {
            String channel = getRaiseChannel(caseId);
            // bd发起的不生成
            if (StringUtils.equals("线下筹款顾问", channel)) {
                log.info("juanzhuan 线下筹款顾问发起 不生成捐转工单 caseId:{}", caseId);
                return false;
            }

            boolean is1v1fuwu = checkIs1v1fuwu(caseId);
            if (!is1v1fuwu && !create.isFromPaySuccess()) {
                // 非bd 也 非1v1 生成捐转工单(原首次逻辑转化)
                // 首次的逻辑是初审通过触发 而非第一笔捐单
                log.info("juanzhuan 非bd 也 非1v1 生成捐转工单 caseId:{}", caseId);
                return true;
            }

            //案例来源：1v1服务任务对应发起的案例
            Response<ClewDonationTransferModel> response = clewtrackFeignClient.getClew4DonationTransferByInfoId(crowdfundingInfo.getInfoId());
            ClewDonationTransferModel model = Optional.ofNullable(response).filter(Response::ok)
                    .map(Response::getData).filter(ClewDonationTransferModel::getIsExistFuwuTask).orElse(null);
            if (model != null && create.isFromPaySuccess()){
                log.info("juanzhuan caseId={} response={}",caseId,response);
                return true;
            }
            return false;
        }

        if (isSecond){
            //生成第二次任务  需要判断捐单量是否达标
            CfInfoStat cfInfoStat = crowdfundingDelegate.getById(caseId);
            String count =  AdminApolloCofig.getValueFromApollo(AdminApolloCofig.cf_donation_count,"120");
            if(cfInfoStat.getDonationCount() >= Integer.valueOf(count)){
                log.info("juanzhuan caseId={} count={} getDonationCount={}",caseId,count,cfInfoStat.getDonationCount());
                return false;
            }
            return true;
        }

        return true;
    }


    public void createJuanzhuanD0OnPaySuccess(long orderId, int caseId){

        //第一笔捐单
        List<CrowdfundingOrder> orders = adminCrowdfundingOrderBiz.getByPage(caseId,0,1);

        if (CollectionUtils.isEmpty(orders)) {
            //生成延时消息1min，防止主从延时
            sendDelayMq(orderId, caseId);
            return;
        }

        if (orderId != orders.get(0).getId().longValue()){
            log.info("juanzhuan caseId={} id={} fisrt={}",caseId,orderId, CollectionUtils.isEmpty(orders) ? null: orders.get(0).getId());
            return ;
        }

        createD0(orderId, caseId, true);

    }

    public void createD0(long orderId, int caseId, boolean fromPaySuccess) {
        JuanzhanOrderCreate create = new JuanzhanOrderCreate();
        create.setOrderId(orderId);
        create.setCaseId(caseId);
        create.setOrderType(WorkOrderType.d0_1v1.getType());
        create.setOrderlevel(getOrderLevel());
        create.setShowName(JuanzhuanWorkOrder.d0_show_name_1);
        create.setCreateSecond(false);
        create.setCreateBegin(true);
        create.setFromPaySuccess(fromPaySuccess);


        if(createJuanzhuan(create)){
            //创建一次任务
            create4one(caseId,create.getOrderType());
            //创建二次任务
            create4two(caseId,create.getOrderType());
        }
    }

    public Boolean getSpecialChannel(String infoUuid) {

        String channel = AdminApolloCofig.getValueFromApollo(AdminApolloCofig.cf_juanzhuan_channel,"wx");

        Set<String> s = Splitter.on(",").splitToList(channel).stream().collect(Collectors.toSet());

        Response<Map<String, Boolean>> response = clewtrackFeignClient.checkClewChannel4DonationTransferByInfoId(infoUuid,s);
        log.info("checkClewChannel4DonationTransferByInfoId infoUuid={},response={}",infoUuid,response);
        Map<String, Boolean> map = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);

        if (MapUtils.isEmpty(map)){
            return false;
        }
        return map.keySet().stream().filter(r->s.contains(r)).findAny().isPresent();
    }

    public boolean createJuanzhuan(JuanzhanOrderCreate create){

        if (!canCreate(create)){
            return false;
        }
        JuanzhuanWorkOrder order = new JuanzhuanWorkOrder();
        BeanUtils.copyProperties(create,order);

        if (order.getOrderType() == WorkOrderType.d0_1v1.getType() && getSpecialChannel(create.getInfoId())){
            order.setOrderType(WorkOrderType.d0_1v1_tzb.getType());
            create.setOrderType(WorkOrderType.d0_1v1_tzb.getType());
        }


        //捐转工单迁移  在创建d0工单之前   访问线索平台确认是否可以创建
        if (WorkOrderType.d0_1v1_tzb.getType() == order.getOrderType() || WorkOrderType.d0_1v1.getType() == order.getOrderType()){
            int caseId = order.getCaseId();
            String channel = WorkOrderType.d0_1v1_tzb.getType() == order.getOrderType()?"special":"normal";
            Response<Boolean> response = taskFeignClient.donateServiceDiversion(caseId,channel);
            log.info("donateServiceDiversion caseId={} channel={} response={}",caseId,channel,response);
            boolean o = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(true);
            if (!o){
                return false;
            }
        }

        Response<Long> response = client.createJuanzhuan(order);
        log.info("createJuanzhuan order={},response={}",order,response);

        if (response != null && response.ok()){
            Response<Void> r = clewtrackFeignClient.notifyCloseFuwuTaskByInfoId(create.getInfoId());
            log.info("notifyCloseFuwuTaskByInfoId infoid={},Response={}",create.getInfoId(),r.getCode());
        }

        return true;
    }

    private void create4one(int caseId,int orderType){
        LocalDateTime now = LocalDateTime.now();

        LocalDateTime d1 = LocalDateTime.of(LocalDate.now().plusDays(1),LocalTime.of(9,0));

        //生成d1 第一次
        JuanzhanOrderCreate order = new JuanzhanOrderCreate();
        order.setCaseId(caseId);
        order.setOrderType(WorkOrderType.d1_1v1.getType());
        order.setShowName(JuanzhuanWorkOrder.d1_show_name_1);
        order.setOrderlevel(OrderLevel.high.getType());
        order.setCreateSecond(false);
        order.setOrderExtType(JuanzhuanWorkOrder.ext_type_common);
        if (orderType == WorkOrderType.d0_1v1_tzb.getType()){
            order.setOrderExtType(JuanzhuanWorkOrder.ext_type_tzb);
        }
        sendMQ(order,now, d1);

        //生成d2 第一次
        LocalDateTime d2 = LocalDateTime.of(LocalDate.now().plusDays(2),LocalTime.of(9,0));
        order.setOrderType(WorkOrderType.d2_1v1.getType());
        order.setShowName(JuanzhuanWorkOrder.d2_show_name_1);
        sendMQ(order,now,d2);

    }

    private void create4two(int caseId,int orderType){

        LocalDateTime now = LocalDateTime.now();

        String time = AdminApolloCofig.getValueFromApollo(AdminApolloCofig.cf_juanzhuan_time,"16:30:00");
        LocalDateTime end = LocalDateTime.parse(LocalDate.now()+" "+time, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        JuanzhanOrderCreate order = new JuanzhanOrderCreate();
        order.setCaseId(caseId);
        order.setOrderType(WorkOrderType.d0_1v1_1.getType());
        order.setShowName(JuanzhuanWorkOrder.d0_show_name_2);
        order.setOrderlevel(OrderLevel.edium.getType());
        order.setCreateSecond(true);

        order.setOrderExtType(JuanzhuanWorkOrder.ext_type_common);
        if (orderType == WorkOrderType.d0_1v1_tzb.getType()){
            order.setOrderExtType(JuanzhuanWorkOrder.ext_type_tzb);
        }

        if (now.isBefore(end)){
            //d0天 二次任务
            sendMQ(order, now, end);
        }

        //d1天 二次任务
        LocalDateTime d1 = LocalDateTime.parse(LocalDate.now().plusDays(1)+" "+time, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        order.setOrderType(WorkOrderType.d1_1v1.getType());
        order.setShowName(JuanzhuanWorkOrder.d1_show_name_2);
        order.setOrderlevel(OrderLevel.edium.getType());
        sendMQ(order,now,d1);

        //d2天 二次任务
        LocalDateTime d2 = LocalDateTime.parse(LocalDate.now().plusDays(2)+" "+time, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        order.setOrderType(WorkOrderType.d2_1v1.getType());
        order.setShowName(JuanzhuanWorkOrder.d2_show_name_2);
        order.setOrderlevel(OrderLevel.edium.getType());
        sendMQ(order,now,d2);

    }

    private void sendMQ(JuanzhanOrderCreate order,LocalDateTime now,LocalDateTime end){

        Duration duration = Duration.between(now,end);

        long time = duration.getSeconds();

        if (time <= 0){
            log.info("create2follow order={},now={},end={}",order,now,end);
            return;
        }

        long second = RandomUtils.nextLong(1,600)+duration.getSeconds();

        MessageResult messageResult = producer.send(Message.ofDelay(MQTopicCons.CF, MQTagCons.juanzhuan_workorder_create,order.getCaseId()+"_"+second,order,second,TimeUnit.SECONDS));

        log.info("create2follow order={},messageResult={}",order,messageResult);

    }


    private void sendDelayMq(long orderId, int caseId) {
        JuanzhanOrderCreate juanzhanOrderCreate = new JuanzhanOrderCreate();
        juanzhanOrderCreate.setCaseId(caseId);
        juanzhanOrderCreate.setOrderId(orderId);
        MessageResult messageResult = producer.send(Message.ofDelay(MQTopicCons.CF, MQTagCons.juanzhuan_workorder_handle_delay, caseId + "_" + System.currentTimeMillis(), juanzhanOrderCreate, 1, TimeUnit.MINUTES));
        log.info("sendDelayMq order={},messageResult={}", juanzhanOrderCreate, messageResult);
    }


    private int getOrderLevel(){

        LocalDateTime now = LocalDateTime.now();

        LocalDateTime start = LocalDateTime.of(LocalDate.now(), LocalTime.of(8,30));

        LocalDateTime end = LocalDateTime.of(LocalDate.now(), LocalTime.of(20,30));

        if (now.isAfter(start) && now.isBefore(end)){
            return OrderLevel.high.getType();
        }

        return OrderLevel.edium.getType();
    }

    private String getRaiseChannel(int caseId) {
        Map<Integer, CfUserInvitedLaunchCaseRecordModel> caseRecordMap = adminApproveService
                .getCaseChannelRecordMap(Lists.newArrayList(caseId));

        if(MapUtils.isEmpty(caseRecordMap) || !caseRecordMap.containsKey(caseId)){
            return "";
        }

        CfUserInvitedLaunchCaseRecordModel caseRecordModel = caseRecordMap.get(caseId);
        return cfFirstApproveOperatorBiz.getGuideUserLaunchChannel(caseRecordModel.getServiceUserInfo(shuidiCipher));
    }

    private boolean checkIs1v1fuwu(int caseId){
        try {
            Response<Boolean> response =  cfClewtrackClient.checkFuwuByCaseid(caseId);

            log.info("checkClewtrack caseId={}, response={}",caseId, JSON.toJSONString(response));

            if (response != null && response.getCode() == 0){

                Boolean result = response.getData();

                if (result != null){
                    return result;
                }
            }
        }catch (Exception e){
            log.error("checkClewtrack caseId={}",caseId,e);
        }
        return false;
    }

}
