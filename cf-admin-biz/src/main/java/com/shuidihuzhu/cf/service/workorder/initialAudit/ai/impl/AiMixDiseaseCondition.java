package com.shuidihuzhu.cf.service.workorder.initialAudit.ai.impl;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.dao.crowdfunding.CfAiMaterialsDao;
import com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterials;
import com.shuidihuzhu.cf.model.crowdfunding.ai.LayOutField;
import com.shuidihuzhu.cf.risk.client.rpc.DiseaseClassifyFeignClientV2;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseClassifyVOV2;
import com.shuidihuzhu.cf.service.workorder.initialAudit.ai.AiCondition;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2020/12/21
 */
@Service("aiMixDisease")
@Slf4j
public class AiMixDiseaseCondition implements AiCondition {

    @Autowired
    private CfAiMaterialsDao cfAiMaterialsDao;

    @Autowired
    private DiseaseClassifyFeignClientV2 diseaseClassifyFeignClientV2;

    @Override
    public boolean check(int caseId, String inputValue) {

        CfAiMaterials m1 = cfAiMaterialsDao.getByCaseId(caseId, CfAiMaterials.jType);
        Optional<String> o1 = Optional.ofNullable(m1).map(CfAiMaterials::getFields).orElse(Lists.newArrayList()).stream().filter(r -> inputValue.equals(r.getFieldKey())).map(LayOutField::getFieldValue).findFirst();
        if (o1.isPresent()) {
            CfAiMaterials m2 = cfAiMaterialsDao.getByCaseId(caseId, CfAiMaterials.tType);
            Optional<String> o2 = Optional.ofNullable(m2).map(CfAiMaterials::getFields).orElse(Lists.newArrayList()).stream().filter(r -> "diseaseNameInMd".equals(r.getFieldKey())).map(LayOutField::getFieldValue).findFirst();
            //都存在  调用疾病归一接口拿到结果后判断是否一致
            if (o2.isPresent()) {
                String d1 = o1.get().replace("，", ",");
                String d2 = o2.get().replace("，", ",");
                List<String> diseaseList = Lists.newArrayList();
                List<String> o1List = Splitter.on(",").splitToList(d1);
                diseaseList.addAll(o1List);
                List<String> o2List = Splitter.on(",").splitToList(d2);
                diseaseList.addAll(o2List);
                log.info("diseaseNorm caseId={} o1List={} o2List={}", caseId, o1List, o2List);
                //用上述中疾病名称字段中每个疾病做完全匹配；
                //若有任意一个疾病完全匹配一致：则判断为一致；
                Set<String> o2Set = new HashSet<>(o2List);
                Optional<String> ro = o1List.stream()
                        .filter(o2Set::contains)
                        .findAny();
                if (ro.isPresent()) {
                    return false;
                }
                Response<List<DiseaseClassifyVOV2>> response = diseaseClassifyFeignClientV2.diseaseNorm(diseaseList);
                log.info("diseaseNorm caseId={} diseaseList={} response={}", caseId, diseaseList, response);
                Map<String, DiseaseClassifyVOV2> map = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList()).stream().collect(Collectors.toMap(DiseaseClassifyVOV2::getDisease, Function.identity()));
                //获取每个疾病的归一结果
                Set<String> set = o1List.stream()
                        .flatMap(r -> Optional.ofNullable(map.get(r))
                                .map(DiseaseClassifyVOV2::getNorm)
                                .orElse(Collections.emptyList())
                                .stream())
                        .collect(Collectors.toSet());

                Optional<String> result = o2List.stream()
                        .flatMap(r -> Optional.ofNullable(map.get(r))
                                .map(DiseaseClassifyVOV2::getNorm)
                                .orElse(Collections.emptyList())
                                .stream())
                        .filter(set::contains)
                        .findAny();

                return !result.isPresent();
            }
        }

        return false;
    }
}
