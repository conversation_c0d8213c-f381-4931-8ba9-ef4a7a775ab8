package com.shuidihuzhu.cf.service.approve;

import com.shuidihuzhu.cf.domain.approve.ApproveControlRecordDO;
import com.shuidihuzhu.cf.enums.approve.ApproveControlSourceTypeEnum;
import com.shuidihuzhu.cf.vo.approve.ApproveControlRecordVO;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.common.web.model.Response;

/**
 * <AUTHOR>
 */
public interface ApproveControlService {

    /**
     * 检查并尝试获取锁
     * @param caseId
     * @param operatorId
     * @param sourceType
     * @param workOrderId
     * @return
     */
    Response<ApproveControlRecordDO> touch2GetControl(int caseId, int operatorId, ApproveControlSourceTypeEnum sourceType, long workOrderId);

    /**
     * 检查是否可以提交审核
     * @return
     */
    Response<ApproveControlRecordDO> checkLock(int caseId, int operatorId);

    /**
     * 获取详情
     * @param caseId
     * @return
     */
    ApproveControlRecordDO getControlInfo(int caseId);

    ApproveControlRecordVO trans2VO(ApproveControlRecordDO record);

    Response<Void> giveUpOperation(int controlRecordId, int operatorId);

    Response<Void> forceReleaseControl(int controlRecordId, int operatorId);

    /**
     * 用户提交材料调用
     * @param caseId
     * @return
     */
    Response<Void> onUserSubmit(int caseId);

    void releaseControlByWorkOrder(WorkOrderVO workOrderVO);

    void onWorkOrderAssign(WorkOrderVO workOrderVO);

    void releaseControl(long controlRecordId);

}
