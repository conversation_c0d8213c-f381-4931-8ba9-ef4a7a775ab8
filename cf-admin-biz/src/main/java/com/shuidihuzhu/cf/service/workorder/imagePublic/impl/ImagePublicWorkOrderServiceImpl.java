package com.shuidihuzhu.cf.service.workorder.imagePublic.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.alps.feign.ocean.*;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingAttachmentBiz;
import com.shuidihuzhu.cf.client.adminpure.enums.CfCasePublicInfoImageHandleStatusEnum;
import com.shuidihuzhu.cf.client.adminpure.enums.CfCasePublicInfoTypeEnum;
import com.shuidihuzhu.cf.client.adminpure.model.caseinfo.CfCasePublicInfo;
import com.shuidihuzhu.cf.client.adminpure.model.caseinfo.CfCasePublicInfoImageAIRecord;
import com.shuidihuzhu.cf.client.apipure.feign.CrowdfundingOrderPureFeignClient;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.constants.AdminUserIDConstants;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfFinishStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.model.ai.AiImageMarkResponseBody;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment;
import com.shuidihuzhu.cf.param.workorder.ImagePublicHandleParam;
import com.shuidihuzhu.cf.param.workorder.ImagePublicWorkOrderConfigParam;
import com.shuidihuzhu.cf.service.ai.AiImageMaskServiceImpl;
import com.shuidihuzhu.cf.service.workorder.imagePublic.CfCasePublicInfoService;
import com.shuidihuzhu.cf.service.workorder.imagePublic.ImagePublicWorkOrderService;
import com.shuidihuzhu.cf.store.enums.CfDomainEnum;
import com.shuidihuzhu.cf.store.model.AnalysisUrl;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCoreFeignClient;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCreateParam;
import com.shuidihuzhu.client.cf.workorder.ext.WorkOrderExtFeignClient;
import com.shuidihuzhu.client.cf.workorder.model.HandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.model.enums.ImageMaskBizEnum;
import com.shuidihuzhu.client.model.event.InfoApproveEvent;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: wangpeng
 * @Date: 2021/8/19 11:49
 * @Description:
 */
@Slf4j
@Service
@RefreshScope
public class ImagePublicWorkOrderServiceImpl implements ImagePublicWorkOrderService {

    @Resource
    private AdminCfInfoExtBiz adminCfInfoExtBiz;
    @Resource
    private WorkOrderCoreFeignClient workOrderCoreFeignClient;
    @Resource
    private CfWorkOrderClient cfWorkOrderClient;
    @Resource
    private CfCasePublicInfoService cfCasePublicInfoService;
    @Resource
    private AdminCrowdfundingAttachmentBiz adminCrowdfundingAttachmentBiz;
    @Resource
    private WorkOrderExtFeignClient workOrderExtFeignClient;
    @Resource
    private CrowdfundingOrderPureFeignClient crowdfundingOrderPureFeignClient;
    @Resource
    private OceanApiClient oceanApiClient;
    @Resource
    private AiImageMaskServiceImpl aiImageMaskService;


    @Value("${image.public.work.order.config:[]}")
    private String imagePublicWorkOrderConfig;
    @Value("${image.public.work.order.ai.switch:true}")
    private boolean imagePublicWorkOrderAISwitch;

    private static final List<Integer> ORDER_TYPES = Lists.newArrayList(WorkOrderType.highriskshenhe.getType(), WorkOrderType.ai_photo.getType(), WorkOrderType.ai_content.getType(), WorkOrderType.ai_erci.getType());

    private static final List<Integer> attachmentType = Arrays.asList(AttachmentTypeEnum.ATTACH_TREATMENT.value(), AttachmentTypeEnum.ATTACH_TREATMENT_VERIFY.value(),
            AttachmentTypeEnum.ATTACH_TREATMENT_NOTE.value(), AttachmentTypeEnum.ATTACH_IN_HOSPITAL.value(),
            AttachmentTypeEnum.ATTACH_LEAVE_HOSPITAL.value());

    @Override
    public void createWorkOrder(InfoApproveEvent infoApproveEvent) {
        log.info("imagePublicWorkOrderService createWorkOrder is begin {}", infoApproveEvent);
        int caseId = infoApproveEvent.getCaseId();
        if (infoApproveEvent.getOrderType() != WorkOrderType.cailiao_4.getType() && infoApproveEvent.getOrderType() != WorkOrderType.cailiao_5.getType()) {
            log.info("imagePublicWorkOrderService createWorkOrder work type is limit {}", infoApproveEvent);
            return;
        }
        if (infoApproveEvent.getStatus() != CrowdfundingStatus.CROWDFUNDING_STATED.value()) {
            log.info("imagePublicWorkOrderService createWorkOrder reject {}", infoApproveEvent);
            return;
        }
        CfInfoExt cfInfoExt = adminCfInfoExtBiz.getByCaseId(caseId);
        if (Objects.isNull(cfInfoExt)) {
            log.info("imagePublicWorkOrderService createWorkOrder cfInfoExt is null {}", infoApproveEvent);
            return;
        }
        int finishStatus = cfInfoExt.getFinishStatus();
        if (finishStatus != CfFinishStatus.NOT_FINISH.getValue()) {
            log.info("imagePublicWorkOrderService createWorkOrder case is end {}", infoApproveEvent);
            return;
        }

        List<CrowdfundingAttachment> attachmentsByCaseIdsWithDelete = adminCrowdfundingAttachmentBiz.getAttachmentsByTypes(caseId, attachmentType);
        if (CollectionUtils.isEmpty(attachmentsByCaseIdsWithDelete)) {
            log.info("imagePublicWorkOrderService createWorkOrder attachment is empty {}", infoApproveEvent);
            return;
        }

        // AI开关打开，所有的掩码功能都走AI掩码
        if (imagePublicWorkOrderAISwitch) {
            // 发消息保存掩码图片
            aiImageMaskService.sendImageMaskMq(caseId, ImageMaskBizEnum.CF_MEDICAL_IMAGE.getCode());
            log.info("AiImageMaskServiceImpl sendImageMaskMq {} {}", caseId, ImageMaskBizEnum.CF_MEDICAL_IMAGE.getDesc());

            log.info("imagePublicWorkOrderService mark image to AI {}", infoApproveEvent);
            syncImageAIMark(cfInfoExt, attachmentsByCaseIdsWithDelete);
            return;
        }

        List<ImagePublicWorkOrderConfigParam> imagePublicWorkOrderConfigParams = JSONObject.parseArray(imagePublicWorkOrderConfig, ImagePublicWorkOrderConfigParam.class);
        if (CollectionUtils.isEmpty(imagePublicWorkOrderConfigParams)) {
            log.info("imagePublicWorkOrderService createWorkOrder config is empty {}", infoApproveEvent);
            return;
        }

        ImagePublicWorkOrderConfigParam param = handleWorkOrderConfigRule(imagePublicWorkOrderConfigParams, caseId);
        if (Objects.isNull(param)) {
            log.info("imagePublicWorkOrderService createWorkOrder config is limit {}", infoApproveEvent);
            return;
        }

        autoCloseWorkOrder(caseId);

        List<Integer> attachmentIdList = attachmentsByCaseIdsWithDelete.stream()
                .map(CrowdfundingAttachment::getId)
                .collect(Collectors.toList());
        WorkOrderCreateParam workOrderCreateParam = new WorkOrderCreateParam();
        workOrderCreateParam.setOrderType(WorkOrderType.picture_publicity_review.getType());
        workOrderCreateParam.setCaseId(caseId);
        workOrderCreateParam.setOrderlevel(param.getWorkOrderLevel());
        workOrderCreateParam.addExt(OrderExtName.picturePublicityReviewAttachment, attachmentIdList);
        Response<Long> longResponse = workOrderCoreFeignClient.create(workOrderCreateParam);
        log.info("imagePublicWorkOrderService createWorkOrder success {}, {}", infoApproveEvent, longResponse);

    }

    @Override
    public Response<Void> submitPublicWorkOrder(ImagePublicHandleParam imagePublicHandleParam) {
        int caseId = imagePublicHandleParam.getCaseId();
        CfInfoExt cfInfoExt = adminCfInfoExtBiz.getByCaseId(caseId);
        if (Objects.isNull(cfInfoExt)) {
            log.info("imagePublicWorkOrderService submitPublicWorkOrder cfInfoExt is null {}", imagePublicHandleParam);
            return NewResponseUtil.makeError(AdminErrorCode.ADD_TRUST_STATUS_ERROR);
        }
        int finishStatus = cfInfoExt.getFinishStatus();
        if (finishStatus != CfFinishStatus.NOT_FINISH.getValue()) {
            autoCloseWorkOrder(caseId);
            log.info("imagePublicWorkOrderService submitPublicWorkOrder case is end {}", imagePublicHandleParam);
            return NewResponseUtil.makeError(AdminErrorCode.HOSPITAL_AUDIT_HAS_AUTO_CLOSE);
        }

        List<CfCasePublicInfo> casePublicInfos = cfCasePublicInfoService.getListByInfoUuidAndType(cfInfoExt.getInfoUuid());

        if (CollectionUtils.isNotEmpty(casePublicInfos)) {
            cfCasePublicInfoService.deleteByCaseIdAndType(caseId, CfCasePublicInfoTypeEnum.TREATMENT_IMAGE.getCode());
        }

        List<ImagePublicHandleParam.ImagePublicHandleImageListParam> markImageList = imagePublicHandleParam.getMarkImageList();
        List<Long> idList = new ArrayList<>();
        for (ImagePublicHandleParam.ImagePublicHandleImageListParam imageListParam : markImageList) {
            CfCasePublicInfo cfCasePublicInfo = new CfCasePublicInfo();
            cfCasePublicInfo.setCaseId(imagePublicHandleParam.getCaseId());
            cfCasePublicInfo.setInfoUuid(cfInfoExt.getInfoUuid());
            cfCasePublicInfo.setImageUrl(imageListParam.getImgUrl());
            cfCasePublicInfo.setType(CfCasePublicInfoTypeEnum.TREATMENT_IMAGE.getCode());
            cfCasePublicInfo.setSwitchInfo(imageListParam.getHandleStatus() == CfCasePublicInfoImageHandleStatusEnum.NO_PUBLIC.getCode() ? 1 : 0);
            cfCasePublicInfo.setImgHandleStatus(imageListParam.getHandleStatus());
            cfCasePublicInfo.setOperateId(imagePublicHandleParam.getOperatorId());
            cfCasePublicInfoService.addCasePublicInfo(cfCasePublicInfo);
            idList.add(cfCasePublicInfo.getId());
        }
        String extValue = JSONObject.toJSONString(idList);
        workOrderExtFeignClient.addByNameValue(imagePublicHandleParam.getWorkOrderId(), OrderExtName.picturePublicityReviewMarkImage.name(), extValue);
        HandleOrderParam handleOrderParam = new HandleOrderParam();
        handleOrderParam.setWorkOrderId(imagePublicHandleParam.getWorkOrderId());
        handleOrderParam.setOrderType(WorkOrderType.picture_publicity_review.getType());
        handleOrderParam.setHandleResult(HandleResultEnum.done.getType());
        handleOrderParam.setUserId(imagePublicHandleParam.getOperatorId());
        workOrderCoreFeignClient.handle(handleOrderParam);

        return NewResponseUtil.makeSuccess(null);
    }


    private ImagePublicWorkOrderConfigParam handleWorkOrderConfigRule(List<ImagePublicWorkOrderConfigParam> imagePublicWorkOrderConfigParams, int caseId) {
        int orderCount = 0;
        OperationResult<Integer> integerOperationResult = crowdfundingOrderPureFeignClient.countPeopleByCaseId(caseId);
        if (Objects.nonNull(integerOperationResult) && integerOperationResult.isSuccess() && Objects.nonNull(integerOperationResult.getData())) {
            orderCount = integerOperationResult.getData();
        }
        long time = 0L;
        long currentTimeMillis = System.currentTimeMillis();
        long finishTime = 0;
        Response<WorkOrderVO> lastWorkOrderByTypes = cfWorkOrderClient.getLastWorkOrderByTypes(caseId, ORDER_TYPES);
        if (Objects.nonNull(lastWorkOrderByTypes) && lastWorkOrderByTypes.ok() && Objects.nonNull(lastWorkOrderByTypes.getData()) && Objects.nonNull(lastWorkOrderByTypes.getData().getHandleTime())) {
            finishTime = lastWorkOrderByTypes.getData().getFinishTime().getTime();
            time = currentTimeMillis - finishTime ;

        }
        log.info("imagePublicWorkOrderService createWorkOrder handleWorkOrderConfigRule {} {} {} {}", orderCount, time, currentTimeMillis, finishTime);
        for (ImagePublicWorkOrderConfigParam param : imagePublicWorkOrderConfigParams) {
            Integer orderPeopleNumberLow = param.getOrderPeopleNumberLow();
            Integer orderPeopleNumberHigh = param.getOrderPeopleNumberHigh();
            Integer auditTimeLow = param.getAuditTimeLow();
            Integer auditTimeHigh = param.getAuditTimeHigh();
            if (Objects.nonNull(orderPeopleNumberLow) && orderCount < orderPeopleNumberLow) {
                continue;
            }
            if (Objects.nonNull(orderPeopleNumberHigh) && orderCount >= orderPeopleNumberHigh) {
                continue;
            }
            if (Objects.nonNull(auditTimeLow) && time < auditTimeLow) {
                continue;
            }
            if (Objects.nonNull(auditTimeHigh) && time >= auditTimeHigh) {
                continue;
            }
            return param;
        }
        return null;
    }

    @Override
    public void autoCloseWorkOrder(int caseId) {
        Response<List<WorkOrderVO>> listResponse = cfWorkOrderClient.queryByCaseAndTypes(caseId, Collections.singletonList(WorkOrderType.picture_publicity_review.getType()));
        if (Objects.nonNull(listResponse) && listResponse.ok() && CollectionUtils.isNotEmpty(listResponse.getData())) {
            listResponse.getData()
                    .stream()
                    .filter(f -> f.getHandleResult() == HandleResultEnum.undoing.getType() || f.getHandleResult() == HandleResultEnum.doing.getType())
                    .forEach(f -> {
                        HandleOrderParam handleOrderParam = new HandleOrderParam();
                        handleOrderParam.setWorkOrderId(f.getWorkOrderId());
                        handleOrderParam.setOrderType(WorkOrderType.picture_publicity_review.getType());
                        handleOrderParam.setHandleResult(HandleResultEnum.exception_done.getType());
                        handleOrderParam.setOperComment("系统自动关闭");
                        handleOrderParam.setUserId(AdminUserIDConstants.SYSTEM);
                        workOrderCoreFeignClient.handle(handleOrderParam);
                    });
            log.info("imagePublicWorkOrderService createWorkOrder auto close work order, {}, {}", caseId, listResponse.getData());
        }
    }

    @Override
    public Map<Long, List<String>> getImagePublicByWorKOrderIdList(List<Long> workOrderIdList) {

        // 图片公式审核工单和 业务/风控 确认过，无使用了。可下掉查询相关逻辑
        return new HashMap<>();

//        Response<List<WorkOrderExt>> listResponse = cfWorkOrderClient.listExtInfos(workOrderIdList, OrderExtName.picturePublicityReviewAttachment.name());
//        if (Objects.isNull(listResponse) || listResponse.notOk() || CollectionUtils.isEmpty(listResponse.getData())) {
//            return new HashMap<>();
//        }
//        Map<Long, List<String>> map = new HashMap<>();
//        for (WorkOrderExt workOrderExt : listResponse.getData()) {
//            if (StringUtils.isEmpty(workOrderExt.getExtValue())) {
//                continue;
//            }
//            List<Integer> attachmentIdList = JSONObject.parseArray(workOrderExt.getExtValue(), Integer.class);
//            List<CrowdfundingAttachment> attachmentsByIdList = adminCrowdfundingAttachmentBiz.getAttachmentsByIdList(attachmentIdList, 0);
//            List<String> collect = Optional.ofNullable(attachmentsByIdList)
//                    .orElse(new ArrayList<>())
//                    .stream()
//                    .map(CrowdfundingAttachment::getUrl)
//                    .collect(Collectors.toList());
//
//            map.put(workOrderExt.getWorkOrderId(), collect);
//        }
//        return map;
    }

    @Override
    public Map<Long, List<String>> getMarkImagePublicByWorKOrderIdList(List<Long> workOrderIdList) {
        Response<List<WorkOrderExt>> listResponse = cfWorkOrderClient.listExtInfos(workOrderIdList, OrderExtName.picturePublicityReviewMarkImage.name());
        if (Objects.isNull(listResponse) || listResponse.notOk() || CollectionUtils.isEmpty(listResponse.getData())) {
            return new HashMap<>();
        }
        Map<Long, List<String>> map = new HashMap<>();
        for (WorkOrderExt workOrderExt : listResponse.getData()) {
            if (StringUtils.isEmpty(workOrderExt.getExtValue())) {
                continue;
            }
            List<Long> idList = JSONObject.parseArray(workOrderExt.getExtValue(), Long.class);
            List<CfCasePublicInfo> cfCasePublicInfos = cfCasePublicInfoService.getListByIdList(idList);
            List<String> collect = Optional.ofNullable(cfCasePublicInfos)
                    .orElse(new ArrayList<>())
                    .stream()
                    .map(CfCasePublicInfo::getImageUrl)
                    .collect(Collectors.toList());

            map.put(workOrderExt.getWorkOrderId(), collect);
        }
        return map;
    }

    @Override
    public void submitAiMarkImage(OceanApiMQResponse apiMQResponse) {
        String body = apiMQResponse.getBody();
        List<AiImageMarkResponseBody> imageMarkResponseBodyList = new ArrayList<>();
        try {
            imageMarkResponseBodyList = JSONObject.parseArray(body, AiImageMarkResponseBody.class);
        } catch (Exception e) {
            log.info("imagePublicWorkOrderService submitAiMarkImage parseArray error {}", apiMQResponse);
        }
        if (CollectionUtils.isEmpty(imageMarkResponseBodyList)) {
            log.info("imagePublicWorkOrderService submitAiMarkImage aiImageMarkResponseBody is null {}", apiMQResponse);
            return;
        }
        AiImageMarkResponseBody aiImageMarkResponseBody = imageMarkResponseBodyList.get(0);
        CfCasePublicInfoImageAIRecord publicInfoServiceById = cfCasePublicInfoService.getById(Long.parseLong(aiImageMarkResponseBody.getImageId()));
        if (Objects.isNull(publicInfoServiceById)) {
            log.info("imagePublicWorkOrderService submitAiMarkImage publicInfoServiceById is null {}", apiMQResponse);
            return;
        }

        List<CfCasePublicInfo> casePublicInfos = cfCasePublicInfoService.getListByInfoUuidAndType(publicInfoServiceById.getInfoUuid());

        if (CollectionUtils.isNotEmpty(casePublicInfos)) {
            cfCasePublicInfoService.deleteByCaseIdAndType(publicInfoServiceById.getCaseId(), CfCasePublicInfoTypeEnum.TREATMENT_IMAGE.getCode());
        }
        for (AiImageMarkResponseBody markResponseBody : imageMarkResponseBodyList) {
            long imageId = Long.parseLong(markResponseBody.getImageId());
            if (imageId <= 0) {
                log.info("imagePublicWorkOrderService submitAiMarkImage imageId is zero {}", markResponseBody);
                continue;
            }
            CfCasePublicInfoImageAIRecord publicInfoImageAIRecord = cfCasePublicInfoService.getById(imageId);
            if (Objects.isNull(publicInfoImageAIRecord)) {
                log.info("imagePublicWorkOrderService submitAiMarkImage publicInfoImageAIRecord is null {}", imageId);
                continue;
            }
            publicInfoImageAIRecord.setImageUrlAi(markResponseBody.getUrlMosaic());
            publicInfoImageAIRecord.setResultCode(apiMQResponse.getResultCode());
            publicInfoImageAIRecord.setFinishAiTime(new Date());
            publicInfoImageAIRecord.setImgHandleStatus(markResponseBody.getStatus());
            cfCasePublicInfoService.updateCfCasePublicInfoImageAIRecord(publicInfoImageAIRecord);

            CfCasePublicInfo cfCasePublicInfo = new CfCasePublicInfo();
            cfCasePublicInfo.setCaseId(publicInfoImageAIRecord.getCaseId());
            cfCasePublicInfo.setInfoUuid(publicInfoImageAIRecord.getInfoUuid());
            cfCasePublicInfo.setImageUrl(StringUtils.isEmpty(publicInfoImageAIRecord.getImageUrlAi()) ? publicInfoImageAIRecord.getImageUrl() : publicInfoImageAIRecord.getImageUrlAi());
            cfCasePublicInfo.setType(CfCasePublicInfoTypeEnum.TREATMENT_IMAGE.getCode());
            cfCasePublicInfo.setSwitchInfo(publicInfoImageAIRecord.getImgHandleStatus() == CfCasePublicInfoImageHandleStatusEnum.NO_PUBLIC.getCode() ? 1 : 0);
            cfCasePublicInfo.setImgHandleStatus(publicInfoImageAIRecord.getImgHandleStatus());
            cfCasePublicInfo.setOperateId(AdminUserIDConstants.SYSTEM);
            cfCasePublicInfoService.addCasePublicInfo(cfCasePublicInfo);

            log.info("imagePublicWorkOrderService submitAiMarkImage success {}", imageId);

        }

    }

    private void syncImageAIMark(CfInfoExt cfInfoExt, List<CrowdfundingAttachment> crowdfundingAttachmentList) {
        List<Map<String, Object>> urlList = new ArrayList<>();
        for (CrowdfundingAttachment crowdfundingAttachment : crowdfundingAttachmentList) {

            CfCasePublicInfoImageAIRecord cfCasePublicInfoImageAIRecord = new CfCasePublicInfoImageAIRecord();
            cfCasePublicInfoImageAIRecord.setCaseId(cfInfoExt.getCaseId());
            cfCasePublicInfoImageAIRecord.setInfoUuid(cfInfoExt.getInfoUuid());
            AnalysisUrl analysisUrl = AnalysisUrl.parse(crowdfundingAttachment.getUrl());
            if (CfDomainEnum.COS_IMAGES.getDomain().equals(analysisUrl.getHost()) || CfDomainEnum.OSS_OSS.getDomain().equals(analysisUrl.getHost())) {
                analysisUrl.setHost(CfDomainEnum.COS_IMAGE.getDomain());
            }
            cfCasePublicInfoImageAIRecord.setImageUrl(analysisUrl.toUrlString());
            cfCasePublicInfoService.addAi(cfCasePublicInfoImageAIRecord);

            Map<String, Object> map = new HashMap<>();
            map.put("id", cfCasePublicInfoImageAIRecord.getId());
            map.put("url", cfCasePublicInfoImageAIRecord.getImageUrl());
            map.put("bizId", 0);
            map.put("bizType", 0);
            urlList.add(map);
        }

        Map<String, Object> body = new HashMap<>();
        body.put("url", urlList);
        body.put("userId", "10007");
        body.put("token", "b87ffe8f80e1a59a");
        OceanApiRequest oceanApiRequest = new OceanApiRequest();
        oceanApiRequest.setTag("ai-info-mosaic");
        oceanApiRequest.setUserId("10007");
        oceanApiRequest.setToken("aca42fbdd89614e4");
        oceanApiRequest.setBody(JSONObject.toJSONString(body));
        Response<OceanAsynApiResponse> oceanApiResponse = oceanApiClient.agentAsyn(oceanApiRequest);
        log.info("imagePublicWorkOrderService to ai result {}", oceanApiResponse);
    }

}

