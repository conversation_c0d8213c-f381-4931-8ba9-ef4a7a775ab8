package com.shuidihuzhu.cf.service;

import com.shuidihuzhu.cf.dao.oneservice.TidbDirectQueryDao;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cf.repository.DataStatRepository;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.DoubleAdder;
import java.util.concurrent.atomic.LongAdder;

/**
 * <AUTHOR>
 * @date 2021/8/30
 */

@Slf4j
@Service
@RefreshScope
public class CfBaoFeiStatService {

    @Value("${apollo.bao.fei.robot.key:0c3b6576-fa8e-4f97-807a-5bcee6007658}")
    private String robotKey;

    @Autowired
    private ApplicationService applicationService;

    @Resource
    private DataStatRepository dataStatRepository;
    @Resource
    private TidbDirectQueryDao tidbDirectQueryDao;

    private static java.text.NumberFormat nf = java.text.NumberFormat.getInstance();

    static {
        // 不使用千分位，即展示为11672283.234，而不是11,672,283.234
        nf.setGroupingUsed(false);
        // 设置数的小数部分所允许的最小位数
        nf.setMinimumFractionDigits(0);
        // 设置数的小数部分所允许的最大位数
        nf.setMaximumFractionDigits(2);
        // 设置数的整数部分所允许的最大位数
        nf.setMaximumIntegerDigits(20);
    }

    public void baofeiStat() {

        Date today = DateUtils.addDays(new Date(), -1);

        String today_str = DateUtil.formatDate(today);
        String yesterday_str = DateUtil.formatDate(DateUtils.addDays(today, -1));
        String one_week_last_str = DateUtil.formatDate(DateUtils.addWeeks(today, -1));
        String four_week_last_str = DateUtil.formatDate(DateUtils.addWeeks(today, -4));
        Date firstDayOfMonth = DateUtil.getFirstDayOfMonth(today);
        String first_day_of_money_str = DateUtil.formatDate(firstDayOfMonth);


        Pair<Long, Double> today_pair = getBaoFei(today_str, "=");
        Pair<Long, Double> yesterday_pair = getBaoFei(yesterday_str, "=");
        Pair<Long, Double> one_week_last_pair = getBaoFei(one_week_last_str, "=");
        Pair<Long, Double> four_week_last_pair = getBaoFei(four_week_last_str, "=");
        Pair<Long, Double> first_day_of_money_pair = getBaoFei(first_day_of_money_str, ">=");


        Pair<Double, Double> ad_today_pair = getAd(today_str,"=");
        Pair<Double, Double> ad_yesterday_pair = getAd(yesterday_str, "=");
        Pair<Double, Double> ad_one_week_last_pair = getAd(one_week_last_str, "=");
        Pair<Double, Double> ad_four_week_last_pair = getAd(four_week_last_str, "=");
        Pair<Double, Double> ad_first_day_of_money_pair = getAd(first_day_of_money_str, ">=");


        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("**").append("筹转保保费情况数据统计(当日、一天前、一周前、四周前、当月累计)").append("** ").append("\n").append("\n");

        stringBuilder.append("**").append("日期:").append(today_str).append("**").append("\n");
        appendBizDate(stringBuilder, today_pair, getPaySuccessCountByTime(today), ad_today_pair);

        stringBuilder.append("**").append("日期:").append(yesterday_str).append("**").append("\n");
        appendBizDate(stringBuilder, yesterday_pair, getPaySuccessCountByTime(DateUtils.addDays(today, -1)), ad_yesterday_pair);

        stringBuilder.append("**").append("日期:").append(one_week_last_str).append("**").append("\n");
        appendBizDate(stringBuilder, one_week_last_pair, getPaySuccessCountByTime(DateUtils.addWeeks(today, -1)), ad_one_week_last_pair);

        stringBuilder.append("**").append("日期:").append(four_week_last_str).append("**").append("\n");
        appendBizDate(stringBuilder, four_week_last_pair, getPaySuccessCountByTime(DateUtils.addWeeks(today, -4)), ad_four_week_last_pair);

        stringBuilder.append("**").append("本月合计").append("**").append("\n");
        Date startTime = DateUtils.truncate(firstDayOfMonth, Calendar.DAY_OF_MONTH);
        Date endTime = DateUtils.addSeconds(DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH), -1);
        appendBizDate(stringBuilder, first_day_of_money_pair, getPaySuccessCountByTime(startTime, endTime), ad_first_day_of_money_pair);


        AlarmBotService.sentMarkDown(robotKey, stringBuilder.toString());
    }

    private void appendBizDate(StringBuilder stringBuilder, Pair<Long, Double> baoFeiPair, long paySuccessOrderCount, Pair<Double, Double> adPair) {
        stringBuilder.append("<font color=\"warning\">")
                .append("订单量:").append(baoFeiPair.getLeft())
                .append(", 规模保费:").append(nf.format(baoFeiPair.getRight()))
                .append("</font>")
                .append("\n");
        stringBuilder.append("<font color=\"warning\">")
                .append("cpc:").append(nf.format(adPair.getLeft()))
                .append(", 广告收入:").append(nf.format(adPair.getRight()))
                .append("</font>")
                .append("\n");
        stringBuilder.append("<font color=\"warning\">")
                .append(" 筹款捐单:").append(paySuccessOrderCount)
                .append(", 每捐单保费:").append(nf.format(paySuccessOrderCount > 0 ? baoFeiPair.getRight() / paySuccessOrderCount : 0))
                .append("</font>")
                .append("\n");
    }

    /**
     * 获取保费收入信息
     * left:订单量，right：保费
     * @param dateTimeStr
     * @param operator
     * @return
     */
    private Pair<Long, Double> getBaoFei(String dateTimeStr, String operator) {
        if (applicationService.isDevelopment()) {
            return Pair.of(0L, 0D);
        }
        List<Map<String, Object>> list = tidbDirectQueryDao.getBaoFei(operator, dateTimeStr);

        LongAdder orderNum = new LongAdder();
        DoubleAdder preminum = new DoubleAdder();
        for (Map<String, Object> map : list) {
            orderNum.add(MapUtils.getLong(map, "order_num"));
            preminum.add(MapUtils.getDouble(map, "scale_premium"));
        }
        return Pair.of(orderNum.longValue(), preminum.doubleValue());
    }

    /**
     * 获取广告收入信息
     * left：cpc，right：广告收入
     * @param dateTimeStr
     * @param operator
     * @return
     */
    private Pair<Double, Double> getAd(String dateTimeStr, String operator) {
        List<Map<String, Object>> list = tidbDirectQueryDao.getAd(operator, dateTimeStr);
        LongAdder clickPvNum = new LongAdder();
        DoubleAdder incomeNum = new DoubleAdder();
        for (Map<String, Object> map : list) {
            clickPvNum.add(MapUtils.getLong(map, "click_pv"));
            incomeNum.add(MapUtils.getDouble(map, "income"));
        }
        double cpc =  clickPvNum.longValue() > 0 ? incomeNum.doubleValue() / clickPvNum.longValue() : 0D;
        return Pair.of(cpc, incomeNum.doubleValue());
    }

    private long getPaySuccessCountByTime(Date date) {

        Date startTime = DateUtils.truncate(date, Calendar.DAY_OF_MONTH);
        Date endTime = DateUtils.addSeconds(DateUtils.addDays(startTime, 1), -1);
        return getPaySuccessCountByTime(startTime, endTime);
    }

    private long getPaySuccessCountByTime(Date startTime, Date endTime) {
        try {
            return dataStatRepository.getPaySuccessCountByTime(startTime, endTime);
        } catch (Exception e) {
            log.warn("获取支付成功订单量异常 startTime:{}, endTime:{}", startTime, endTime, e);
        }
        return 0L;
    }
}
