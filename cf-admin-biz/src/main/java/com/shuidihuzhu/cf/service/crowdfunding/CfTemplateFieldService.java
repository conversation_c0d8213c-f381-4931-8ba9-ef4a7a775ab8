package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.dao.crowdfunding.CfTemplateFieldDao;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CfItemFieldVo;
import com.shuidihuzhu.cf.model.crowdfunding.CfItemTemplate;
import com.shuidihuzhu.cf.model.crowdfunding.CfTemplateField;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.admin.model.CfItemField;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2020/11/26
 */
@Service
public class CfTemplateFieldService {

    @Autowired
    private CfTemplateFieldDao templateFieldDao;

    @Autowired
    private CommonOperationRecordClient commonOperationRecordClient;


    public int addField(CfItemField cfItemField,int userId){

        int flag = templateFieldDao.addField(cfItemField);

        if (flag > 0){
            commonOperationRecordClient
                    .create()
                    .buildBasicPlatform(cfItemField.getId(), userId, OperationActionTypeEnum.CF_ITEM_FIELD_RECORD)
                    .buildRemark("新增")
                    .save();
        }

        return flag;
    }

    public int updateField(long id,int delete,int userId){

        int flag = templateFieldDao.updateField(id,delete);

        if (flag > 0){
            commonOperationRecordClient
                    .create()
                    .buildBasicPlatform(id, userId, OperationActionTypeEnum.CF_ITEM_FIELD_RECORD)
                    .buildRemark(delete==1 ? "弃用" : "启用")
                    .save();
        }

        return flag;
    }


    public List<CfItemFieldVo> listCfItemFields(int delete){

        List<CfItemField> list = templateFieldDao.listCfItemFields(delete);

        if (CollectionUtils.isEmpty(list)){
            return Lists.newArrayList();
        }

        List<Long> fieldIds = list.stream().map(CfItemField::getId).collect(Collectors.toList());

        List<CfTemplateField> cfTemplateFieldList = templateFieldDao.getFieldIds(fieldIds);

        Map<Long,List<CfTemplateField>> map = cfTemplateFieldList.stream().collect(Collectors.groupingBy(CfTemplateField::getFieldId));

        return list.stream().map(r->{
            CfItemFieldVo vo = new CfItemFieldVo();
            vo.setId(r.getId());
            vo.setField(r.getField());
            List<CfTemplateField> l = Optional.ofNullable(map.get(r.getId())).orElse(Lists.newArrayList());
            vo.setTempDesc(l.stream().map(CfTemplateField::getTemplateName).collect(Collectors.joining(",")));
            return vo;
        }).collect(Collectors.toList());

    }

    public List<CfItemField> getCfItemFieldByIds(List<Long> ids){
       return templateFieldDao.getCfItemFieldByIds(ids);
    }

    public int addTemplate(CfItemTemplate cfItemTemplate, int userId){

        //默认否
        if (cfItemTemplate.getRepeatSend() == 0){
            cfItemTemplate.setRepeatSend(1);
        }
        int flag = templateFieldDao.addTemplate(cfItemTemplate);

        if (flag > 0){

            if (CollectionUtils.isNotEmpty(cfItemTemplate.getFieldIds())){

                List<CfTemplateField> list = cfItemTemplate.getFieldIds().stream()
                        .map(r->{
                            CfTemplateField c = new CfTemplateField();
                            c.setFieldId(r);
                            c.setTemplateId(cfItemTemplate.getId());
                            c.setTemplateName(cfItemTemplate.getTemplateName());
                            return c;
                        }).collect(Collectors.toList());

                templateFieldDao.addTemplateField(list);
            }
            if (!cfItemTemplate.isUpdateOperation()){

                commonOperationRecordClient
                        .create()
                        .buildBasicPlatform(cfItemTemplate.getId(), userId, OperationActionTypeEnum.CF_ITEM_TEMPLATE_RECORD)
                        .buildRemark("新增")
                        .save();
            }

        }

        if (cfItemTemplate.isUpdateOperation()){

            templateFieldDao.updateTemplate(cfItemTemplate.getUpdateId(),2);

            CfItemTemplate c = templateFieldDao.getById(cfItemTemplate.getUpdateId());

            commonOperationRecordClient
                    .create()
                    .buildBasicPlatform(cfItemTemplate.getId(), userId, OperationActionTypeEnum.CF_ITEM_TEMPLATE_RECORD)
                    .buildExtValue("originalId",cfItemTemplate.getUpdateId())
                    .buildRemark(buildRemark(c))
                    .save();
        }

        return flag;
    }


    private String buildRemark(CfItemTemplate c){

        StringBuilder sb = new StringBuilder();
        sb.append("修改模板：修改前内容为：");
        sb.append("分类原因：").append(templateFieldDao.getById(c.getParentId()).getTemplateName()).append(";");
        sb.append("下发原因：").append(c.getTemplateName()).append(";");
        sb.append("模板内容：").append(c.getTemplateValue()).append(";");
        List<CfTemplateField> list = templateFieldDao.getFieldByTempId(c.getId());
        List<CfItemField> fields =templateFieldDao.getCfItemFieldByIds(list.stream().map(CfTemplateField::getFieldId).collect(Collectors.toList()));
        sb.append("模板包含字段：").append(fields.stream().map(CfItemField::getField).collect(Collectors.joining(","))).append(";");
        sb.append("同一案例是否支持多次下发该模板：").append(c.getRepeatSend()==1?"否":"是").append(";");

        return sb.toString();
    }


    public OpResult<Integer> updateTemplate(long id, int delete, int userId){

        CfItemTemplate template = templateFieldDao.getById(id);

        if (template.getLevel() == 0 && delete == 1){
            List<CfItemTemplate> list = templateFieldDao.getTemplatesByParentId(0,id);
            if (CollectionUtils.isNotEmpty(list)){
                return OpResult.createFailResult(AdminErrorCode.CF_REPORT_PROBLEM_CLASSIFY_ERROR);
            }
        }

        int flag = templateFieldDao.updateTemplate(id,delete);

        if (flag > 0){
            commonOperationRecordClient
                    .create()
                    .buildBasicPlatform(id, userId, OperationActionTypeEnum.CF_ITEM_TEMPLATE_RECORD)
                    .buildRemark(delete==1 ? "弃用" : "启用")
                    .save();
        }

        return OpResult.createSucResult(flag);
    }

    public List<CfItemTemplate> getTemplates(int delete){

        List<CfItemTemplate> list = templateFieldDao.getTemplates(delete);

        return list;
    }


    public List<CfItemTemplate> getTemplatesByParentId(int delete,long id){

        List<CfItemTemplate> list = templateFieldDao.getTemplatesByParentId(delete,id);

        List<Long> tIds = list.stream().map(CfItemTemplate::getId).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(tIds)){

            List<CfTemplateField> tfList = templateFieldDao.getFieldByTempIds(tIds);

            Map<Long,List<CfTemplateField>> map = tfList.stream().collect(Collectors.groupingBy(CfTemplateField::getTemplateId));

            list.stream().forEach(r->{
                r.setFieldIds(Optional.ofNullable(map.get(Long.valueOf(r.getId()))).orElse(Lists.newArrayList()).stream().map(CfTemplateField::getFieldId).collect(Collectors.toList()));
            });
        }


        return list;
    }


    public List<CfItemTemplate> getTemplatesByIds(List<Long> ids){
        return templateFieldDao.getTemplatesByIds(ids);
    }


    public CfItemTemplate getById(long id){
        return templateFieldDao.getById(id);
    }

    public List<CfTemplateField> getFieldByTempId(long tempId){
        return templateFieldDao.getFieldByTempId(tempId);
    }

    public List<CfItemField> getFieldsByTempId(long tempId){
        List<CfTemplateField> list = getFieldByTempId(tempId);

        if (CollectionUtils.isEmpty(list)){
            return Lists.newArrayList();
        }

       return getCfItemFieldByIds(list.stream().map(CfTemplateField::getFieldId).collect(Collectors.toList()));

    }
}
