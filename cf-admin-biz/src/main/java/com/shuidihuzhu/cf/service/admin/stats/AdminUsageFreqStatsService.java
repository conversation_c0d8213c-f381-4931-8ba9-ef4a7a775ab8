package com.shuidihuzhu.cf.service.admin.stats;

import com.shuidihuzhu.cf.enums.admin.stats.UsageFreqStatsBizTypeEnum;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/10 11:39
 */
@Validated
public interface AdminUsageFreqStatsService {

    /**
     * 执行增加计数操作
     * @param statsKey
     * @param statsDim
     * @param bizTypeEnum
     */
    void incrementUsageFreq(@NotBlank(message = "statsKey不能为空") String statsKey,
                            @NotNull(message = "statsDim不能为空") String statsDim,
                            @NotNull(message = "bizTypeEnum不能为空")UsageFreqStatsBizTypeEnum bizTypeEnum);

    /**
     * 执行批量增加计数操作
     * @param statsKeys
     * @param statsDim
     * @param bizTypeEnum
     */
    void incrementUsageFreqBatch(@NotEmpty(message = "statsKeys不能为空") List<String> statsKeys,
                                 @NotNull(message = "statsDim不能为空") String statsDim,
                                 @NotNull(message = "bizTypeEnum不能为空")UsageFreqStatsBizTypeEnum bizTypeEnum);

}
