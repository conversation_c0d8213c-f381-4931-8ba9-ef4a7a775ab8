package com.shuidihuzhu.cf.service.admin;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingAttachmentBiz;
import com.shuidihuzhu.cf.dao.admin.CfMaterialsPicDao;
import com.shuidihuzhu.cf.delegate.SeaAccountDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.model.admin.CfMaterialsPic;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2021/3/17
 */
@Service
public class CfMaterialsPicService {

    @Autowired
    private CfMaterialsPicDao cfMaterialsPicDao;

    @Autowired
    private AdminCrowdfundingAttachmentBiz adminCrowdfundingAttachmentBiz;

    @Autowired
    private SeaAccountDelegate accountDelegate;

    public List<CfMaterialsPic> getByCaseId(int caseId){


        List<CfMaterialsPic> list = cfMaterialsPicDao.getByCaseId(caseId);

        List<CrowdfundingAttachment> attachments = adminCrowdfundingAttachmentBiz.getAttachmentsByTypes(caseId,Lists.newArrayList(AttachmentTypeEnum.ATTACH_TREATMENT_VERIFY.value(),
                AttachmentTypeEnum.ATTACH_TREATMENT.value(), AttachmentTypeEnum.ATTACH_MEDICAL_RECORD_HOME.value(),
                AttachmentTypeEnum.ATTACH_PASS_HOSPITAL.value(), AttachmentTypeEnum.ATTACH_TREATMENT_NOTE.value(),
                AttachmentTypeEnum.ATTACH_INSPECTION_REPORT.value(),AttachmentTypeEnum.ATTACH_IN_HOSPITAL.value(),
                AttachmentTypeEnum.ATTACH_LEAVE_HOSPITAL.value(),AttachmentTypeEnum.ATTACH_FIRST_APPROVE_MEDICAL.value()));

        Map<String,CfMaterialsPic> map = Maps.newHashMap();

        if (CollectionUtils.isNotEmpty(list)){

            map.putAll(list.stream().collect(Collectors.toMap(CfMaterialsPic::getPicUrl, Function.identity(),(o1,o2)->o2)));
            List<CrowdfundingAttachment> other = attachments.stream().filter(r-> map.get(r.getUrl()) != null).collect(Collectors.toList());
            //全部填写了
            if (CollectionUtils.isEmpty(other)){
                return Lists.newArrayList();
            }
        }

        return attachments.stream().map(r->{

            CfMaterialsPic pic = new CfMaterialsPic();
            pic.setCaseId(r.getParentId());
            pic.setPicSource("诊断证明-证明相关照片");
            if (r.getType().equals(AttachmentTypeEnum.ATTACH_FIRST_APPROVE_MEDICAL)){
                pic.setPicSource("前置信息-医疗材料");
            }
            pic.setPicUrl(r.getUrl());
            CfMaterialsPic materialsPic = Optional.ofNullable(map.get(r.getUrl())).orElse(null);
            if (materialsPic != null){
                pic.setPicType(materialsPic.getPicType());
                pic.setUserName(materialsPic.getUserName());
                pic.setLock(1);
            }

            return pic;

        }).collect(Collectors.toList());

    }

    public void save(List<CfMaterialsPic> list,int userId){

        String org = accountDelegate.getNameWithOrgByUserId(userId);
        list.stream().forEach(r->{
            if (StringUtils.isEmpty(r.getUserName())){
                r.setUserName(org);
            }
            //重复图片没有类型
            if (r.getRepeatPic() == 1){
                r.setPicType("");
            }
        });

        cfMaterialsPicDao.delete(list.get(0).getCaseId());
        cfMaterialsPicDao.insert(list);
    }
}
