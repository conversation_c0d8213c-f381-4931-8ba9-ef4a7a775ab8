package com.shuidihuzhu.cf.service;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.devops.cloud.app.activity.client.enums.ActivityEnvTypeEnum;
import com.shuidihuzhu.devops.cloud.app.activity.client.enums.AttentionTypeEnum;
import com.shuidihuzhu.devops.cloud.app.activity.client.enums.ClusterEnum;
import com.shuidihuzhu.devops.cloud.app.activity.client.param.CloudAppActivityListParam;
import com.shuidihuzhu.devops.cloud.app.activity.client.service.QueryCloudAppActivityService;
import com.shuidihuzhu.devops.cloud.app.activity.client.util.PagerData;
import com.shuidihuzhu.devops.cloud.app.activity.client.vo.CloudAppActivityVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/5/24
 */
@Slf4j
@Service
public class CloudAppActivityService {

    @Resource
    private QueryCloudAppActivityService queryCloudAppActivityService;

    public PageResult<CloudAppActivityVO> queryAppActivity(int projectId, int pageNum, String startTime, String endTime) {

        /**
         *   "projectId": 60, // 应用ID 必填
         *   "activityEnvType": "EMPTY",  // 环境
         *   "activityType": "DEPLOY",    // 变更分类
         *   "activityAction": "",  // 变更动作
         *   "attentionType": "ACTION_BY_CURRENT_APP",      // 关注范围 必填
         *   "cluster": "BEDIN_CLUSTER", // 集群编码
         *   "creator": "",              // 变更触发人
         *   "startTime": ""  // 开始时间
         *   "endTime": "",  // 结束时间
         *   "isPage": true,
         *   "pageNo": 1,
         *   "pageSize": 10, // 不可更改
         */

        CloudAppActivityListParam param = CloudAppActivityListParam.builder()
                .pageNo(pageNum)
                .pageSize(10) // 不支持修改 设不设置 都会使用 10
                .isPage(true) // 不支持修改 设不设置 都会使用 true
                .startTime(startTime)
                .endTime(endTime)
                .projectId(projectId) // 必填
                .attentionType(AttentionTypeEnum.ACTION_BY_CURRENT_APP) // 必填
                .activityEnvType(ActivityEnvTypeEnum.PROD)
//                .activityType(ActivityTypeEnum.BIZ)
//                .activityAction("cfEagerEyeReport")
//                .cluster(ClusterEnum.MAXWELL_CLUSTER)
//                .creator("xxx")
                .build();

        PageResult<CloudAppActivityVO> pageResult = new PageResult<>();
        Response<PagerData<CloudAppActivityVO>> response = queryCloudAppActivityService.query(param);
        log.info("应用动态查询, param:{}, response:{} ", JSON.toJSONString(param), JSON.toJSONString(response));
        if (Objects.isNull(response) || response.notOk() || Objects.isNull(response.getData())) {
            return pageResult;
        }

        PagerData<CloudAppActivityVO> pagerData = response.getData();
        pageResult.setPageList(pagerData.getList());
        pageResult.setHasNext(pagerData.getPageNo() * pagerData.getPageSize() < pagerData.getTotal());
        return pageResult;
    }
}
