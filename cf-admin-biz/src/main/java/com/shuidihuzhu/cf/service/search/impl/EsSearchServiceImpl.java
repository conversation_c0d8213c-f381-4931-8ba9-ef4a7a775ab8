package com.shuidihuzhu.cf.service.search.impl;

import com.shuidihuzhu.cf.service.search.EsSearchService;
import com.shuidihuzhu.client.dataservice.bi.v1.BiApiClient;
import com.shuidihuzhu.client.model.Response;
import com.shuidihuzhu.client.model.SearchDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-01-16  16:42
 */
@Slf4j
@Service
public class EsSearchServiceImpl implements EsSearchService {


    @Resource
    private BiApiClient biApiClient;

    private DateTimeFormatter formatter = DateTimeFormatter
            .ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS")
            .withZone(ZoneId.systemDefault());


    @Override
    public List<Integer> searchUgcWordOrder(final int orderType,
                                            final List<Integer> orderTasks,
                                            final int operatorId,
                                            final List<Integer> contentTypes,
                                            final Integer result,
                                            Date startDate,
                                            Date endDate,
                                            String hitWords,
                                            long start,
                                            int size, boolean isPre) {

        List<Integer> resList = null;
        try {
            String startTime = null;
            if (startDate != null) {
                startTime = formatter.format(startDate.toInstant());
            }
            String endTime = null;
            if (endDate != null) {
                endTime = formatter.format(endDate.toInstant());
            }

            SearchDto searchDto = new SearchDto();
            String querySql = sqlUgcWorkOrder(orderType,
                    orderTasks,
                    operatorId,
                    contentTypes,
                    result,
                    startTime,
                    endTime,
                    hitWords,
                    start,
                    size,
                    isPre);
            log.info("{}", querySql);
            searchDto.setQuerySql(querySql);
            Response response = biApiClient.esQueryCustom(searchDto);
            if (response == null) {
                log.error("数据接口返回null");
                return Collections.emptyList();
            }
            if (response.getCode() != 0) {
                log.error("数据接口code 非0");
                return Collections.emptyList();
            }
            List<Map> list = (List<Map>) response.getData();
            if (list == null) {
                log.error("数据接口返回null");
                return Collections.emptyList();
            }
            resList = list.stream()
                    .limit(size)
                    .map(map -> (Map) map.get("awo"))
                    .map(v -> (Integer) v.get("id"))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("数据获取异常", e);
            return Collections.emptyList();
        }
        return resList;
    }

    private String sqlUgcWorkOrder(final int orderType,
                                   final List<Integer> orderTasks,
                                   final int operatorId,
                                   final List<Integer> contentTypes,
                                   final Integer result,
                                   String startTime,
                                   String endTime,
                                   String hitWords,
                                   long start,
                                   int size, boolean isPre) {
        String res = new SQL() {{
            SELECT("awo.id");
            FROM("shuidi_crowdfunding_admin_task_ugc atu");
            INNER_JOIN("shuidi_crowdfunding_admin_work_order awo on awo.id = atu.work_order_id");
            if (orderType != 0) {
                WHERE("awo.order_type = " + orderType);
            }
            if (orderTasks != null && orderTasks.size() > 0) {
                WHERE("awo.order_task in (" + StringUtils.join(orderTasks, ",") + ")");
            }
            if (operatorId != 0) {
                WHERE("awo.operator_id = " + operatorId);
            }
            if (contentTypes != null && contentTypes.size() > 0) {
                WHERE("atu.content_type in (" + StringUtils.join(contentTypes, ",") + ")");
            }
            if (result != null) {
                WHERE("atu.result = " + result);
            }
            if (StringUtils.isNotBlank(startTime)) {
                WHERE("awo.update_time >= " + startTime);
            }
            if (StringUtils.isNotBlank(endTime)) {
                WHERE("awo.update_time <= " + endTime);
            }
            if (StringUtils.isNotBlank(hitWords)) {
                WHERE("atu.hit_words = '" + hitWords + "'");
            }
            if (start != 0) {
                if (isPre) {
                    WHERE("awo.id < " + start);
                } else {
                    WHERE("awo.id > " + start);
                }
            }
            ORDER_BY("awo.id" + (isPre ? " desc" : ""));
        }}.toString();
        return res;
    }
}
