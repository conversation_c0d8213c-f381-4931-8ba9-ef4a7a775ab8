package com.shuidihuzhu.cf.service.message;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingVolunteerBiz;
import com.shuidihuzhu.cf.delegate.other.IWeiXinDelegate;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import com.shuidihuzhu.client.cf.growthtool.client.CfAppPushCrmMsgFeignClient;
import com.shuidihuzhu.client.cf.growthtool.client.CfChannelFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2018-12-03  19:57
 */
@Service
@Slf4j
@RefreshScope
public class VolunteerHelpService {

    @Resource
    private AlarmClient alarmClient;

    @Autowired
    private CfAppPushCrmMsgFeignClient cfAppPushCrmMsgFeignClient;

    @Resource
    private CrowdfundingVolunteerBiz crowdfundingVolunteerBiz;

    @Value("${apollo.send.fumi.gw:false}")
    private boolean sendFumiToGw;

    public OpResult send(String volunteerUniqueCode, String content) {

        CrowdfundingVolunteer volunteer = crowdfundingVolunteerBiz.getByUniqueCode(volunteerUniqueCode);

        if (volunteer == null) {
            return OpResult.createFailResult(CfErrorCode.SYSTEM_PARAM_ERROR).buildMessage("没有找到筹款顾问信息");
        }
        if (volunteer.getWorkStatus() == CrowdfundingVolunteerEnum.workStatusEnum.DIMISSIOM.getValue()) {
            return OpResult.createFailResult(CfErrorCode.SYSTEM_PARAM_ERROR).buildMessage("顾问已离职");
        }
        try {
            if(sendFumiToGw) {
                alarmClient.sendByUser(Lists.newArrayList(volunteer.getMis()), content);
            }
            cfAppPushCrmMsgFeignClient.sendAppPushCrm(volunteer.getMobile(), content);
        }catch (Exception e){
            log.error(this.getClass().getSimpleName() + " sendAppPushCrm" + content, e);
        }
        return OpResult.createSucResult(null);
    }
}
