package com.shuidihuzhu.cf.service;


import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.client.base.page.v1.model.PaginationListVO;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonActionConst;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonRecord;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonRecordClient;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.dao.risk.BanShareWhiteListDao;
import com.shuidihuzhu.cf.model.risk.BanShareWhiteListDO;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.admin.PageUtil;
import jodd.util.StringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class BanShareWhiteListService {

    @Autowired
    private BanShareWhiteListDao banShareWhiteListDao;
    @Autowired
    private WonRecordClient wonRecordClient;

    private static final long ORDER_HANDLE_RECORD = 110L;
    private static final int VALID_STATE = 1;
    private static final int NOT_VALID_STATE = 2;


    public void addInfo(long cfUserId, int maxCount, String reason, String name, long operatorId) {

        //判断是否存过
        BanShareWhiteListDO shareWhiteListDO = banShareWhiteListDao.getByUserId(cfUserId);
        if (shareWhiteListDO != null) {
            return;
        }

        //保存数据
        BanShareWhiteListDO banShareWhiteListDO = BanShareWhiteListDO.builder()
                .userId(cfUserId)
                .maxCount(maxCount)
                .operateReason(reason)
                .validTime(getValidTime())
                .operatorName(name)
                .build();

        int res = banShareWhiteListDao.insert(banShareWhiteListDO);
        if (res > 0) {
            //存储操作信息
            wonRecordClient.create()
                    .buildBizId(banShareWhiteListDO.getId())
                    .buildActionId(ORDER_HANDLE_RECORD)
                    .buildOperatorId((int) operatorId)
                    .buildRemark("新增")
                    .buildActionTime(new Date())
                    .save();
        }

    }

    public void updateMaxCount(long id, int maxCount, long operatorId) {

        BanShareWhiteListDO banShareWhiteListDO = banShareWhiteListDao.getById(id);
        if (Objects.isNull(banShareWhiteListDO)) {
            return;
        }

        //修改阈值及有效期
        Date date = getValidTime();
        int res = banShareWhiteListDao.updateMaxCount(id, maxCount, date);
        if (res > 0) {
            //存储操作信息
            wonRecordClient.create()
                    .buildBasic(banShareWhiteListDO.getId(), ORDER_HANDLE_RECORD)
                    .buildActionTime(new Date())
                    .buildOperatorId((int) operatorId)
                    .buildRemark("编辑白名单阈值：" + banShareWhiteListDO.getMaxCount() + "-修改为：" + maxCount)
                    .save();
        }
    }

    public Response<PaginationListVO<BanShareWhiteListDO>> getList(long cfUserId, String name, int state, int pageSize, int current) {

        //分页放在最前面
        PageHelper.startPage(current, pageSize);

        List<BanShareWhiteListDO> banShareWhiteListDOList = banShareWhiteListDao.getByCondition(cfUserId, name, state);
        Map<String, Object> map = PageUtil.transform2PageMap(banShareWhiteListDOList);
        Object total = map.get("total");
        PaginationListVO<BanShareWhiteListDO> view = PaginationListVO.create(banShareWhiteListDOList, current, pageSize, Long.parseLong(String.valueOf(total)));
        return NewResponseUtil.makeSuccess(view);

    }

    public String getReason(long id) {
        BanShareWhiteListDO banShareWhiteListDO = banShareWhiteListDao.getById(id);
        if (Objects.isNull(banShareWhiteListDO)) {
            return StringUtils.EMPTY;
        }
        return banShareWhiteListDO.getOperateReason();
    }

    public List<WonRecord> getListRecord(long id) {
        OperationResult<List<WonRecord>> operationResult = wonRecordClient.listByBizIdAndActionId(id, ORDER_HANDLE_RECORD);
        List<WonRecord> wonRecords = Optional.ofNullable(operationResult)
                .filter(v -> v.getCode() == ErrorCode.SUCCESS.getCode())
                .map(OperationResult::getData)
                .orElse(Lists.newArrayList());
        return wonRecords;
    }

    public Integer getMaxCount(long cfUserId) {

        BanShareWhiteListDO banShareWhiteListDO = banShareWhiteListDao.getByUserIdAndState(cfUserId, VALID_STATE);
        if(banShareWhiteListDO == null){
            return null;
        }
        return banShareWhiteListDO.getMaxCount();
    }

    public void updateState() {
        List<BanShareWhiteListDO> banShareWhiteListDOList = banShareWhiteListDao.getAll();
        for (BanShareWhiteListDO banShareWhiteListDO : banShareWhiteListDOList) {
            if (banShareWhiteListDO.getValidTime().before(new Date())) {
                banShareWhiteListDao.updateState();
            }
        }
    }

    private Date getValidTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

}
