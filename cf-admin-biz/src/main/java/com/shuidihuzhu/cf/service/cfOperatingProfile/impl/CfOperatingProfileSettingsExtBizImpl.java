package com.shuidihuzhu.cf.service.cfOperatingProfile.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.dao.cfOperatingProfile.CfOperatingProfileSettingsExtDao;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileLog;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileSettings;
import com.shuidihuzhu.cf.service.cfOperatingProfile.CfOperatingProfileLogBiz;
import com.shuidihuzhu.cf.service.cfOperatingProfile.CfOperatingProfileSettingsExtBiz;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import static com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileLog.OperateType.CHANGE_PROBLEM_REMARK;

@Slf4j
@Service
public class CfOperatingProfileSettingsExtBizImpl implements CfOperatingProfileSettingsExtBiz {

    @Autowired
    private CfOperatingProfileSettingsExtDao settingsExtDao;
    @Autowired
    private CfOperatingProfileLogBiz logBiz;


    // 当举报问题类型的名称 或备注有改动时 需要解除
    @Override
    public void unbindRelation(int userId, CfOperatingProfileSettings.ProfileExtName extName, List<Integer> valueIds) {

        if (CollectionUtils.isEmpty(valueIds)) {
            return;
        }
        List<String> values = valueIds.stream().map(String::valueOf).collect(Collectors.toList());

        List<CfOperatingProfileSettings.ProfileSettingsExt>  allRelations = settingsExtDao
                .selectByNameAndValues(extName.getName(), values);

        log.info("标签和举报的问题的绑定关系. allRelations:{}", allRelations);
        if (CollectionUtils.isEmpty(allRelations))  {
            return ;
        }

        List<Long> profileIds = allRelations.stream().map(CfOperatingProfileSettings.ProfileSettingsExt::getProfileId).collect(Collectors.toList());

        // 删除以前的关联关系
        settingsExtDao.deleteExtByProfileIdsAndNames(profileIds,
                Lists.newArrayList(
                        CfOperatingProfileSettings.ProfileExtName.MODULE.getName(),
                        CfOperatingProfileSettings.ProfileExtName.NAME.getName(),
                        CfOperatingProfileSettings.ProfileExtName.REMARK.getName()));

        log.info("删除标签和举报的问题的绑定关系 profileIds:{}", profileIds);

        String comment = "举报问题配置修改-问题名称/备选";
        for (long id : profileIds) {
            logBiz.insertOperateLog(userId, (int)id, CfOperatingProfileLog.OperateType.CHANGE_PROBLEM_REMARK.getCode(), comment);
        }
    }

    @Override
    public int insertExtList(List<CfOperatingProfileSettings.ProfileSettingsExt> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0 ;
        }

        return settingsExtDao.insertExtList(list);
    }

    @Override
    public List<CfOperatingProfileSettings.ProfileSettingsExt> selectByProfileIdsAndNames(List<Long> profileIds, List<String> names) {

        return settingsExtDao.selectByProfileIdsAndNames(profileIds, names);
    }

    @Override
    public int deleteExtByProfileIdsAndNames(List<Long> profileIds, List<String> names) {
        if (CollectionUtils.isEmpty(profileIds) || CollectionUtils.isEmpty(names)) {
            return 0;
        }
        return settingsExtDao.deleteExtByProfileIdsAndNames(profileIds, names);
    }

    @Override
    public List<CfOperatingProfileSettings.ProfileSettingsExt> selectByNameAndValues(String name, List<String> values) {

        return settingsExtDao.selectByNameAndValues(name, values);
    }

}
