package com.shuidihuzhu.cf.service.workorder.read.impl.finance;

import com.shuidihuzhu.cf.service.workorder.read.impl.AbstractFinanceWorkOrderCheckHandler;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 案例余额  surplusAmount eg:12313.00元
 * 退款原因     refundMsgCodes
 * 退款详细说明   applyReason
 * 拒绝申请原因 无字段，统一拼接成remark
 * 审核备注     remark
 */
@Component
public class AllRefundAuditCheckHandler extends AbstractFinanceWorkOrderCheckHandler {
    @Override
    public WorkOrderType getWorkOrderType() {
        return WorkOrderType.all_refund_audit;
    }

    @Override
    protected Map<String, Object> toMap(Object source) {
        Map<String, Object> res = super.toMap(source);
        // 案例余额
        updateMap(res, "surplusAmountInFen", "caseBal");
        // 退款原因
        updateMap(res, "refundDesc", "refRsn");
        // 退款详细说明
        updateMap(res, "applyReason", "refDetExp");
        // 拒绝申请原因
        updateMap(res, "remark", "appRejRsn");
        // 审核备注
        res.put("audNote", res.get("appRejRsn"));
        return res;
    }
}
