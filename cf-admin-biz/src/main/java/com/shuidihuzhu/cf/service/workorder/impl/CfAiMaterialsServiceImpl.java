package com.shuidihuzhu.cf.service.workorder.impl;

import com.shuidihuzhu.cf.dao.crowdfunding.CfAiMaterialsDao;
import com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterials;
import com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterialsResult;
import com.shuidihuzhu.cf.service.workorder.CfAiMaterialsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2021/4/22 20:28
 * @Description:
 */
@Service
public class CfAiMaterialsServiceImpl implements CfAiMaterialsService {

    @Autowired
    private CfAiMaterialsDao aiMaterialsDao;

    @Override
    public CfAiMaterialsResult getResultByCaseId(int caseId) {
        return aiMaterialsDao.getResultByCaseId(caseId);
    }

    @Override
    public CfAiMaterials getByCaseId(int caseId, int materialsType) {
        return aiMaterialsDao.getByCaseId(caseId, materialsType);
    }

}
