package com.shuidihuzhu.cf.service.workorder.cailiao;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.alps.feign.fs.*;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.dao.ai.CfApproveAuditAiCallRecordDao;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.model.ai.CfApproveAuditAiCallRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.constant.CfClientMQTopicCons;
import com.shuidihuzhu.client.model.event.InfoApproveEvent;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.http.HttpUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: wangpeng
 * @Date: 2022/7/8 15:09
 * @Description:
 */
@Slf4j
@Service
@RefreshScope
public class CaiAuditRejectAICallService {

    @Value("#{'${apollo.cailiao.ai.call.reject.ids:}'}")
    private String aiCallRejectIds;

    private final static String CALL_URL = "https://venus.shuiditech.com/cti/callout";

    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;
    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Resource
    private CfApproveAuditAiCallRecordDao cfApproveAuditAiCallRecordDao;
    @Autowired(required = false)
    private Producer producer;
    @Resource
    private CfWorkOrderClient cfWorkOrderClient;
    @Autowired
    private ShuidiCipher shuidiCipher;

    private OkHttpClient okHttpClient = new OkHttpClient();


    /**
     * 材审驳回后打电话
     *
     * @param approveEvent
     */
    public void rejectAICallFirst(InfoApproveEvent approveEvent) {
        Date now = new Date();
        String strDateStart = DateUtil.getDateStart(System.currentTimeMillis());
        Date todayStart = DateUtil.getStr2SDate(strDateStart);
        Date startDate = DateUtils.addHours(todayStart, 8);
        Date endDate = DateUtils.addHours(todayStart, 22);
        if (approveEvent.getWorkOrderId() <= 0) {
            log.info("caiAuditRejectAICallService rejectAICall workOrderId is empty {}", approveEvent);
            return;
        }
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(approveEvent.getCaseId());
        if (Objects.isNull(crowdfundingInfo)) {
            log.info("caiAuditRejectAICallService rejectAICall crowdfundingInfo is empty {}", approveEvent);
            return;
        }
        UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByUserId(crowdfundingInfo.getUserId());
        if (Objects.isNull(userInfoModel) || StringUtils.isEmpty(userInfoModel.getCryptoMobile())) {
            log.info("caiAuditRejectAICallService rejectAICall mobile is empty {}", approveEvent);
            return;
        }
        if (now.getTime() < startDate.getTime() || now.getTime() > endDate.getTime()) {
            log.info("caiAuditRejectAICallService rejectAICall call time is limit {}", approveEvent);
            return;
        }
        List<Integer> refuseIds = approveEvent.getRefuseIds();
        if (CollectionUtils.isEmpty(refuseIds)) {
            log.info("caiAuditRejectAICallService rejectAICall refuseIds is empty {}", approveEvent);
            return;
        }
        JSONObject jsonObject = JSONObject.parseObject(aiCallRejectIds);
        if (Objects.isNull(jsonObject)) {
            return;
        }
        String botId = "";
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            String s = String.valueOf(entry.getValue());
            if (StringUtils.isEmpty(s)) {
                continue;
            }
            List<Integer> integers = Splitter.on(",").splitToList(s)
                    .stream()
                    .map(Integer::valueOf)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEqualCollection(refuseIds, integers)) {
                botId = entry.getKey();
                break;
            }
        }
        if (StringUtils.isEmpty(botId)) {
            log.info("caiAuditRejectAICallService rejectAICall botId is empty {}", approveEvent);
            return;
        }
        String callId = "C" + UUID.randomUUID().toString();
        CfApproveAuditAiCallRecord cfApproveAuditAiCallRecord = new CfApproveAuditAiCallRecord();
        cfApproveAuditAiCallRecord.setCallId(callId);
        cfApproveAuditAiCallRecord.setCaseId(approveEvent.getCaseId());
        cfApproveAuditAiCallRecord.setWorkOrderId(approveEvent.getWorkOrderId());
        cfApproveAuditAiCallRecord.setRejectIds(Joiner.on(",").join(refuseIds));
        cfApproveAuditAiCallRecord.setBotId(botId);
        cfApproveAuditAiCallRecordDao.insert(cfApproveAuditAiCallRecord);

        CalloutParam calloutParam = new CalloutParam();
        calloutParam.setCallNumber(shuidiCipher.decrypt(userInfoModel.getCryptoMobile()));
        calloutParam.setBotId(botId);
        calloutParam.setSceneCode("ai_meterial_review");
        calloutParam.setCallId(callId);
        MediaType JSON = MediaType.parse("application/json");
        RequestBody requestBody = RequestBody.create(JSON, JSONObject.toJSONString(calloutParam));
        Request request = new Request.Builder()
                .url(CALL_URL)
                .post(requestBody)
                .build();
        okhttp3.Response execute = null;
        try {
            execute = okHttpClient.newCall(request).execute();
            log.info("caiAuditRejectAICallService rejectAICall call res {} {}", request, execute);
        } catch (IOException e) {
            log.info("caiAuditRejectAICallService rejectAICall call error {} {}", request, e);
        }
        cfApproveAuditAiCallRecordDao.updateCallResCode(cfApproveAuditAiCallRecord.getId(), String.valueOf(execute));
    }

    public void updateCallIntention(CallbackBody callbackBody) {
        CfApproveAuditAiCallRecord auditAiCallRecord = cfApproveAuditAiCallRecordDao.getByCallId(callbackBody.getCallId());
        if (Objects.isNull(auditAiCallRecord)) {
            return;
        }
        cfApproveAuditAiCallRecordDao.updateCallIntention(auditAiCallRecord.getId(), callbackBody.getPreQuestion().getTitle());
    }

    public void aiAgainCall(CalloutRecordMq calloutRecordMq) {
        CfApproveAuditAiCallRecord auditAiCallRecord = cfApproveAuditAiCallRecordDao.getByCallId(calloutRecordMq.getCallId());
        if (Objects.isNull(auditAiCallRecord)) {
            return;
        }
        List<Integer> rejectList = Splitter.on(",").splitToList(auditAiCallRecord.getRejectIds())
                .stream()
                .map(Integer::valueOf)
                .collect(Collectors.toList());
        InfoApproveEvent infoApproveEvent = new InfoApproveEvent();
        infoApproveEvent.setCaseId(auditAiCallRecord.getCaseId());
        infoApproveEvent.setWorkOrderId(auditAiCallRecord.getWorkOrderId());
        infoApproveEvent.setRefuseIds(rejectList);
        List<CfApproveAuditAiCallRecord> aiCallRecordList = cfApproveAuditAiCallRecordDao.getByWorkOrderId(auditAiCallRecord.getWorkOrderId());
        if (aiCallRecordList.size() == 1 && calloutRecordMq.getCallState() == CallStateEnum.UNNORMAL_HANGUP.getCode()) {
            // 第二次复拨
            rejectAICallFirst(infoApproveEvent);
        }
        if (aiCallRecordList.size() == 2 && calloutRecordMq.getCallState() == CallStateEnum.UNNORMAL_HANGUP.getCode()) {
            // 第三次复拨,次日11点
            long currentTimeMillis = System.currentTimeMillis();
            String strDateStart = DateUtil.getDateStart(currentTimeMillis);
            Date todayStart = DateUtil.getStr2SDate(strDateStart);
            Date todayEnd = DateUtils.addDays(todayStart, 1);
            long delayTime = (todayEnd.getTime() - currentTimeMillis) + 11 * 60 * 60 * 1000;
            Message message = Message.ofDelay(CfClientMQTopicCons.CF, MQTagCons.CF_APPROVE_AI_CALL_THREE,
                    MQTagCons.CF_APPROVE_AI_CALL_THREE + auditAiCallRecord.getWorkOrderId(), auditAiCallRecord, delayTime, TimeUnit.MILLISECONDS);
            producer.send(message);
        }
    }

    public void updateCallTimeAndState(CalloutRecordMq calloutRecordMq) {
        CfApproveAuditAiCallRecord auditAiCallRecord = cfApproveAuditAiCallRecordDao.getByCallId(calloutRecordMq.getCallId());
        if (Objects.isNull(auditAiCallRecord)) {
            return;
        }
        String date = DateUtil.getDateStart(calloutRecordMq.getTimestamp());
        int callResult = calloutRecordMq.getCallState() > auditAiCallRecord.getCallResult() ? calloutRecordMq.getCallState() : auditAiCallRecord.getCallResult();
        if (calloutRecordMq.getCallState() == CallStateEnum.CALLOUT.getCode()) {
            cfApproveAuditAiCallRecordDao.updateCallStartTime(auditAiCallRecord.getId(), date, callResult);
        }

        if (calloutRecordMq.getCallState() == CallStateEnum.NORMAL_HANGUP.getCode() ||
                calloutRecordMq.getCallState() == CallStateEnum.UNNORMAL_HANGUP.getCode()) {
            cfApproveAuditAiCallRecordDao.updateCallEndTime(auditAiCallRecord.getId(), date, callResult);
        }

    }

    public void callThreeTimes(CfApproveAuditAiCallRecord cfApproveAuditAiCallRecord) {
        int caseId = cfApproveAuditAiCallRecord.getCaseId();
        Response<WorkOrderVO> lastWorkOrder = cfWorkOrderClient.getLastWorkOrder(caseId, WorkOrderType.cailiao_5.getType());
        WorkOrderVO workOrderVO = Optional.ofNullable(lastWorkOrder)
                .map(Response::getData)
                .orElse(null);
        if (Objects.nonNull(workOrderVO) && workOrderVO.getCreateTime().getTime() > cfApproveAuditAiCallRecord.getCreateTime().getTime()) {
            log.info("caiAuditRejectAICallService callThreeTimes work order limit {}", cfApproveAuditAiCallRecord);
            return;
        }
        List<Integer> rejectList = Splitter.on(",").splitToList(cfApproveAuditAiCallRecord.getRejectIds())
                .stream()
                .map(Integer::valueOf)
                .collect(Collectors.toList());
        InfoApproveEvent infoApproveEvent = new InfoApproveEvent();
        infoApproveEvent.setCaseId(cfApproveAuditAiCallRecord.getCaseId());
        infoApproveEvent.setWorkOrderId(cfApproveAuditAiCallRecord.getWorkOrderId());
        infoApproveEvent.setRefuseIds(rejectList);
        rejectAICallFirst(infoApproveEvent);
    }
}
