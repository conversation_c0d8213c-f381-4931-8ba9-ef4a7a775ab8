package com.shuidihuzhu.cf.service.workorder.promoteBill;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingAuthorBiz;
import com.shuidihuzhu.cf.client.feign.CfFirstApproveFeignClient;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdFundingProgressDao;
import com.shuidihuzhu.cf.dao.crowdfunding.CfPromoteBillDao;
import com.shuidihuzhu.cf.dao.crowdfunding.NewAdminCfFundUseAuditDao;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceDrawCashFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.model.finance.drawcash.CfCaseDrawCashInfo;
import com.shuidihuzhu.cf.model.admin.FundUseWorkOrderVO;
import com.shuidihuzhu.cf.model.admin.vo.PromoteBillHandleParam;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor;
import com.shuidihuzhu.cf.model.crowdfunding.vo.QueryListResultVo;
import com.shuidihuzhu.cf.vo.crowdfunding.AdminCrowdfundingProgress;
import com.shuidihuzhu.client.cf.growthtool.client.ClewPreproseMaterialFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.CfCaseSpecialPrePoseDetail;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCoreFeignClient;
import com.shuidihuzhu.client.cf.workorder.model.HandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CfPromoteBillService {

    @Autowired
    private CfPromoteBillDao promoteBillService;
    @Autowired
    private WorkOrderCoreFeignClient orderCoreFeignClient;
    @Autowired
    private CfFirstApproveFeignClient cfFirstApproveFeignClient;
    @Autowired
    private ClewPreproseMaterialFeignClient materialFeignClient;
    @Autowired
    private CfFinanceDrawCashFeignClient cashCaseFeignClient;
    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Autowired
    private AdminCrowdfundingAuthorBiz authorBiz;
    @Autowired
    private NewAdminCfFundUseAuditDao cfFundUseAuditDao;
    @Autowired
    private AdminCrowdFundingProgressDao fundingProgressDao;
    @Autowired
    private CfWorkOrderClient workOrderClient;
    @Autowired
    private MaskUtil maskUtil;

    public AdminErrorCode addResult(PromoteBillHandleParam handleParam) {

        AdminErrorCode exceptionResult = canExceptionClose(handleParam.getCaseId(), handleParam.getWorkOrderId());
        if (exceptionResult != AdminErrorCode.SUCCESS) {
            return exceptionResult;
        }

        handleWorkOrder(handleParam.getCaseId(), handleParam.getWorkOrderId(), handleParam.getOperateId(), HandleResultEnum.getFromType(handleParam.getHandleResult()));
        if (handleParam.getHandleResult() == HandleResultEnum.done.getType()) {
            handleParam.setComment(StringUtils.trimToEmpty(handleParam.getComment()));
            handleParam.setOtherReason(StringUtils.trimToEmpty(handleParam.getOtherReason()));
            handleParam.setUserNoFitReasonTypes(StringUtils.trimToEmpty(handleParam.getUserNoFitReasonTypes()));

            promoteBillService.addResult(handleParam);
        }

        return AdminErrorCode.SUCCESS;
    }

    public AdminErrorCode canExceptionClose(int caseId, long workOrderId) {


        Date firstUploadTime = getFirstUploadDate(caseId);
        if (firstUploadTime != null) {
            Response<WorkOrderVO> lastWorkOrder = workOrderClient.getWorkOrderById(workOrderId);
            if (lastWorkOrder != null && lastWorkOrder.getData() != null
                    && lastWorkOrder.getData().getHandleResult() == HandleResultEnum.doing.getType()) {
                handleWorkOrder(caseId, workOrderId, 0, HandleResultEnum.exception_done);
                log.info("自动异常关闭工单 workOrderId:{}", workOrderId);
            }

            return AdminErrorCode.WORK_ORDER_EXCEPTION_CLOSE;
        }

        return AdminErrorCode.SUCCESS;
    }

    public void closeLastPromoteWork(int caseId) {
        Response<WorkOrderVO> lastWorkOrder = workOrderClient.getLastWorkOrder(caseId,
                WorkOrderType.promote_raiser_upload_bill.getType());
        log.info("查找案例最后一个工单caseId:{} result:{}", caseId, JSON.toJSONString(lastWorkOrder));
        if (lastWorkOrder == null || lastWorkOrder.getData() == null ||
                lastWorkOrder.getData().getHandleResult() != HandleResultEnum.undoing.getType()) {
            return;
        }

        handleWorkOrder(caseId, lastWorkOrder.getData().getWorkOrderId(), 0, HandleResultEnum.exception_done);
    }

    private void handleWorkOrder(int caseId, long workOrderId, long userId, HandleResultEnum handleResult) {
        HandleOrderParam param = new HandleOrderParam();
        param.setCaseId(caseId);
        param.setWorkOrderId(workOrderId);
        param.setUserId(userId);
        param.setHandleResult(handleResult.getType());
        param.setOrderType(WorkOrderType.promote_raiser_upload_bill.getType());

        Response<Void> result = orderCoreFeignClient.handle(param);
        log.info("处理催用户上传票据工单处理结果param:{} result:{}", JSON.toJSONString(param), JSON.toJSONString(result));
    }


    public PromoteBillHandleParam selectLastByWorkOrderId(long workOrderId) {

        return promoteBillService.selectLastByWorkOrderId(workOrderId);
    }

    public Date getFirstUploadDate(int caseId) {
        AdminCrowdfundingProgress fundUse = fundingProgressDao.getFirstOneByCaseId(caseId);
        if (fundUse != null) {
            return fundUse.getCreateTime();
        }
        fundUse = cfFundUseAuditDao.getFirstOneByCaseId(caseId);
        return fundUse == null ? null : fundUse.getCreateTime();
    }

    public void fillPromoteUploadBillInfo(List<QueryListResultVo> resultList) {

        try {
            List<Integer> caseIds = getOrderTypeCaseIds(resultList, WorkOrderType.promote_raiser_upload_bill);
            if (CollectionUtils.isEmpty(caseIds)) {
                return;
            }

            Map<Integer, CfCaseSpecialPrePoseDetail> prePoseMapping = getPrePostMobileMapping(caseIds);
            Map<Integer, CfCaseDrawCashInfo> drawCashMapping = getDrawCashInfo(caseIds);
            Map<Integer, CfFirsApproveMaterial> materialMapping =  getMaterialMapping(caseIds);

            for (QueryListResultVo resultVo : resultList) {
                if (resultVo.getOrderType() != WorkOrderType.promote_raiser_upload_bill.getType()) {
                    continue;
                }

                CfCaseSpecialPrePoseDetail poseDetail = prePoseMapping.get(resultVo.getCaseId());
                if (poseDetail != null) {
                    resultVo.setPrePostMobileMask(maskUtil.buildByDecryptPhone(poseDetail.getMobile()));
                }

                CfCaseDrawCashInfo cashInfo = drawCashMapping.get(resultVo.getCaseId());
                if (cashInfo != null) {
                    resultVo.setFirstDrawSuccessTime(cashInfo.getFirstDrawSuccessTime());
                    resultVo.setFirstDrawAmount(cashInfo.getFirstDrawSuccessAmount());
                    resultVo.setTotalDrawAmount(cashInfo.getTotalDrawSuccessAmount());
                }

                CfFirsApproveMaterial approveMaterial = materialMapping.get(resultVo.getCaseId());
                if (approveMaterial != null) {
                    resultVo.setPatientName(approveMaterial.getPatientRealName());
                    resultVo.setRaiseName(StringUtils.isNoneBlank(approveMaterial.getSelfRealName()) ?
                            approveMaterial.getSelfRealName() : approveMaterial.getPatientRealName());
                    UserInfoModel userInfoByUserId = userInfoServiceBiz.getUserInfoByUserId(approveMaterial.getUserId());
                    if (userInfoByUserId != null) {
                        resultVo.setRaiseMobileMask(maskUtil.buildByEncryptPhone(userInfoByUserId.getCryptoMobile()));
                    } else {
                        CrowdfundingAuthor author = authorBiz.get(resultVo.getCaseId());
                        if (author != null) {
                            resultVo.setRaiseName(author.getName());
                        }
                    }
                }

                resultVo.setFirstUpdateBillTime(getFirstUploadDate(resultVo.getCaseId()));

            }

        } catch (Exception e) {
            log.error("数据的扩展值填充异常", e);
        }
    }

    public void fillPromoteUploadInfo(List<FundUseWorkOrderVO> orderVOList) {

        try {
            List<Integer> caseIds = getOrderTypeCaseId(orderVOList, WorkOrderType.promote_raiser_upload_bill);
            if (CollectionUtils.isEmpty(caseIds)) {
                return;
            }

            Map<Integer, CfCaseSpecialPrePoseDetail> prePoseMapping = getPrePostMobileMapping(caseIds);
            Map<Integer, CfCaseDrawCashInfo> drawCashMapping = getDrawCashInfo(caseIds);
            Map<Integer, CfFirsApproveMaterial> materialMapping =  getMaterialMapping(caseIds);

            for (FundUseWorkOrderVO resultVo : orderVOList) {
                if (resultVo.getOrderType() != WorkOrderType.promote_raiser_upload_bill.getType()) {
                    continue;
                }

                CfCaseSpecialPrePoseDetail poseDetail = prePoseMapping.get(resultVo.getCaseId());
                if (poseDetail != null) {
                    resultVo.setPrePostMobileMask(maskUtil.buildByDecryptPhone(poseDetail.getMobile()));
                    resultVo.setPrePostMobile(null);
                }

                CfCaseDrawCashInfo cashInfo = drawCashMapping.get(resultVo.getCaseId());
                if (cashInfo != null) {
                    resultVo.setFirstDrawSuccessTime(cashInfo.getFirstDrawSuccessTime());
                    resultVo.setFirstDrawAmount(cashInfo.getFirstDrawSuccessAmount());
                    resultVo.setTotalDrawAmount(cashInfo.getTotalDrawSuccessAmount());
                }

                CfFirsApproveMaterial approveMaterial = materialMapping.get(resultVo.getCaseId());
                if (approveMaterial != null) {
                    resultVo.setPatientName(approveMaterial.getPatientRealName());
                    resultVo.setRaiseName(StringUtils.isNoneBlank(approveMaterial.getSelfRealName()) ?
                            approveMaterial.getSelfRealName() : approveMaterial.getPatientRealName());
                    UserInfoModel userInfoByUserId = userInfoServiceBiz.getUserInfoByUserId(approveMaterial.getUserId());
                    if (userInfoByUserId != null) {
                        resultVo.setRaiseMobileMask(maskUtil.buildByEncryptPhone(userInfoByUserId.getCryptoMobile()));
                        resultVo.setRaiseMobile(null);

                    } else {
                        CrowdfundingAuthor author = authorBiz.get(resultVo.getCaseId());
                        if (author != null) {
                            resultVo.setRaiseName(author.getName());
                        }
                    }
                }
                resultVo.setFirstUpdateBillTime(getFirstUploadDate(resultVo.getCaseId()));

            }

        } catch (Exception e) {
            log.error("数据的扩展值填充异常", e);
        }

    }

    private Map<Integer, CfFirsApproveMaterial> getMaterialMapping(List<Integer> caseIds) {

        Map<Integer, CfFirsApproveMaterial> result = Maps.newHashMap();
        for (Integer caseId : caseIds) {
          com.shuidihuzhu.cf.client.response.FeignResponse<CfFirsApproveMaterial> material
                  = cfFirstApproveFeignClient.getCfFirstApproveMaterialByCaseId(caseId);
          if (material != null && material.getData() != null) {
              result.put(caseId, material.getData());
          }
        }

        return result;
    }


    private Map<Integer, CfCaseDrawCashInfo> getDrawCashInfo(List<Integer> caseIds) {

        FeignResponse<List<CfCaseDrawCashInfo>> result = cashCaseFeignClient.selectDrawCashInfo(caseIds);
        if (result == null || CollectionUtils.isEmpty(result.getData())) {
            log.info("没有找到案例的提现数据.caseId:{} result:{}", JSON.toJSONString(caseIds), JSON.toJSONString(result));
            return Maps.newHashMap();
        }

        return result.getData()
                .stream().collect(Collectors.toMap(CfCaseDrawCashInfo::getCaseId, Function.identity(), (a, b) -> a));

    }

    private Map<Integer, CfCaseSpecialPrePoseDetail> getPrePostMobileMapping(List<Integer> caseIds) {

        Response<List<CfCaseSpecialPrePoseDetail>> prePoseRes = materialFeignClient.getSpecialPrePoseDetail(caseIds);
        if (prePoseRes == null || CollectionUtils.isEmpty(prePoseRes.getData())) {
            return Maps.newHashMap();
        }

       return prePoseRes.getData()
                .stream().collect(Collectors.toMap(CfCaseSpecialPrePoseDetail::getCaseId, Function.identity(), (a, b) -> a));
    }

    private List<Integer> getOrderTypeCaseIds(List<QueryListResultVo> resultList, WorkOrderType orderType) {
        List<Integer> caseIds = Lists.newArrayList();

        if (CollectionUtils.isEmpty(resultList)) {
            return caseIds;
        }

        for (QueryListResultVo vo : resultList) {
            if (vo.getOrderType() == orderType.getType()) {
                caseIds.add(vo.getCaseId());
            }
        }

        return caseIds;
    }


    private List<Integer> getOrderTypeCaseId(List<FundUseWorkOrderVO> resultList, WorkOrderType orderType) {
        List<Integer> caseIds = Lists.newArrayList();

        if (CollectionUtils.isEmpty(resultList)) {
            return caseIds;
        }

        for (FundUseWorkOrderVO vo : resultList) {
            if (vo.getOrderType() == orderType.getType()) {
                caseIds.add(vo.getCaseId());
            }
        }

        return caseIds;
    }

}
