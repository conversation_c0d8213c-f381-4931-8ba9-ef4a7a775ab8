package com.shuidihuzhu.cf.service.tag.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.admin.util.admin.AdminCfIdCardUtil;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.feign.CfMaterialReadClient;
import com.shuidihuzhu.cf.client.material.feign.CfMaterialWriteClient;
import com.shuidihuzhu.cf.client.material.model.CfMaterialAddOrUpdateVo;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.client.material.model.materialField.MaterialExtKeyConst;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.datautilapi.DataAddressByIdCard;
import com.shuidihuzhu.cf.model.threebody.ThreeBodyRuleConfig;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.ai.CfAddressDataQueryService;
import com.shuidihuzhu.cf.service.disease.impl.DiseaseNormServiceImpl;
import com.shuidihuzhu.cf.service.tag.CfThreeBodyService;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditOperationMqVO;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.admin.enums.ThreeBodyCaseTabEnum;
import com.shuidihuzhu.client.cf.admin.model.ThreeBodyTag;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackJudgeConformThreeBodyClient;
import com.shuidihuzhu.client.cf.clewtrack.model.ChannelRefine;
import com.shuidihuzhu.client.cf.clewtrack.param.ChannelRefineDTO;
import com.shuidihuzhu.client.cf.growthtool.client.CfChannelFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.CfThreeBodyInfo;
import com.shuidihuzhu.client.cf.growthtool.model.RuleJudge;
import com.shuidihuzhu.client.dataservice.datautil.v1.DataUtilApiClient;
import com.shuidihuzhu.client.dataservice.faceApi.v1.FaceApiClient;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2022/8/24 7:29 下午
 */
@Slf4j
@Service
@RefreshScope
public class CfThreeBodyServiceImpl implements CfThreeBodyService {

    @Resource
    private DiseaseNormServiceImpl diseaseNormService;
    @Resource
    private CfChannelFeignClient cfChannelFeignClient;
    @Autowired
    private IRiskDelegate riskDelegate;
    @Resource
    private ShuidiCipher shuidiCipher;
    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;
    @Resource
    private CfMaterialWriteClient cfMaterialWriteClient;
    @Resource
    private CfMaterialReadClient cfMaterialReadClient;

    /**
     * 高质量案例疾病名
     */
    @Value("${apollo.admin.high.quality.disease:[]}")
    private List<String> highQualityDisease;

    @Value("${apollo.admin.high.quality.amount-v1:10000000}")
    private int highQualityAmountV1;

    @Value("${apollo.admin.high.quality.amount-v2:50000000}")
    private int highQualityAmountV2;

    @Value("${apollo.admin.high.quality.age:18}")
    private int highQualityAge;

    private static ThreeBodyRuleConfig threeBodyRuleConfig = null;
    @Value("${apollo.three-body.rule.config:{}}")
    public void setRuleConfig(String ruleConfig) {
        if (StringUtils.isBlank(ruleConfig)) {
            return;
        }
        try {
            threeBodyRuleConfig = JSON.parseObject(ruleConfig, ThreeBodyRuleConfig.class);
        } catch (Exception e) {
            log.error("CfThreeBodyServiceImpl setRuleConfig error", e);
        }
    }

    @Resource
    private CfAddressDataQueryService cfAddressDataQueryService;
    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Resource
    private FaceApiClient faceApiClient;
    @Resource
    private CfClewtrackJudgeConformThreeBodyClient cfClewtrackJudgeConformThreeBodyClient;

    @Override
    public void saveThreeBodyCaseTab(Integer caseId) {
        if (Objects.isNull(threeBodyRuleConfig)) {
            log.info("saveThreeBodyCaseTab apollo解析失败");
            return ;
        }

        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
        if (Objects.isNull(crowdfundingInfo)) {
            log.info("saveThreeBodyCaseTab 无案例信息 {}", caseId);
            return ;
        }

        // 确认渠道是否符合线上渠道
        if (!judgeChannel(crowdfundingInfo)) {
            return ;
        }

        // 确认案例是否命中三体
        if (judgeHitThreeBody(crowdfundingInfo)) {
            log.info("saveThreeBodyCaseTab judgeHitThreeBody is hit {}", crowdfundingInfo.getId());
            tagThreeBody(ThreeBodyCaseTabEnum.THREE_BODY_CASE.getDesc(), crowdfundingInfo.getId());
        }
    }

    @Override
    public Boolean judgeIsThreeBodyTag(Integer caseId) {

        RpcResult<Map<String, List<String>>> mapRpcResult = cfMaterialReadClient.selectValueByFields(caseId, List.of(MaterialExtKeyConst.CASE_THREE_BODY_TAG));
        if (Objects.nonNull(mapRpcResult) && mapRpcResult.isSuccess() && MapUtils.isNotEmpty(mapRpcResult.getData())) {

            List<String> hitString = Optional.ofNullable(mapRpcResult.getData().get(MaterialExtKeyConst.CASE_THREE_BODY_TAG)).orElse(new ArrayList<>());
            if (CollectionUtils.isEmpty(hitString)) {
                return false;
            }

            for (String hit : hitString) {
                if (ThreeBodyCaseTabEnum.judgeThreeBodyCase(hit)) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public List<ThreeBodyTag> judgeThreeBodyByCaseIds(List<Integer> caseIds) {

        List<ThreeBodyTag> threeBodyTags = Lists.newArrayList();

        caseIds.forEach(caseId -> {

            ThreeBodyTag threeBodyTag = ThreeBodyTag.builder().caseId(caseId).build();

            RpcResult<Map<String, List<String>>> mapRpcResult = cfMaterialReadClient.selectValueByFields(caseId, List.of(MaterialExtKeyConst.CASE_THREE_BODY_TAG));
            List<String> hitString = Optional.ofNullable(mapRpcResult)
                    .filter(RpcResult::isSuccess)
                    .map(RpcResult::getData)
                    .map(f -> f.get(MaterialExtKeyConst.CASE_THREE_BODY_TAG))
                    .orElse(new ArrayList<>());
            for (String hit : hitString) {
                if (ThreeBodyCaseTabEnum.judgeThreeBodyCase(hit)) {
                    threeBodyTag.setThreeBodyTag(true);
                    threeBodyTag.setCaseLabel("三体案例");
                } else if (StringUtils.isNotBlank(hit)) {
                    threeBodyTag.setCaseLabel(hit);
                }
                threeBodyTag.setHitReasonDec(hit);
            }

            if (Objects.isNull(threeBodyTag.getThreeBodyTag())) {
                threeBodyTag.setThreeBodyTag(false);
            }
            if (Objects.isNull(threeBodyTag.getHitReasonDec())) {
                threeBodyTag.setHitReasonDec("");
            }
            threeBodyTags.add(threeBodyTag);
        });

        return threeBodyTags;
    }

    @Override
    public Boolean judgeThreeBodyRule(RuleJudge ruleJudge) {

        if (Objects.isNull(ruleJudge) || Objects.isNull(threeBodyRuleConfig)) {
            return false;
        }

        if (Objects.isNull(ruleJudge.getInputContent()) || Objects.isNull(ruleJudge.getRuleId())) {
            return false;
        }

        Map<String, String> inputContent = ruleJudge.getInputContent();
        String targetAmount = inputContent.get("targetAmount");
        if (StringUtils.isBlank(targetAmount) || !StringUtils.isNumeric(targetAmount)) {
            log.info("judgeThreeBodyRule targetAmount is error {}", ruleJudge);
            return false;
        }
        Integer targetAmountInt = Integer.parseInt(targetAmount) * 100;

        // 不同三体规则走不同判断
        switch (ruleJudge.getRuleId()) {
            // 年龄和目标金额规则
            case 1:
                String patientIdCard = Optional.ofNullable(inputContent.get("patientIdCard")).orElse("");
                return judgeAgeThreeBodyRule(patientIdCard) && judgeTotalAmountAnd(targetAmountInt);
            // 疾病和目标金额规则
            case 2:
                String classifyDiseases = Optional.ofNullable(inputContent.get("classifyDiseases")).orElse("");
                return judgeDiseaseThreeBodyRule(classifyDiseases) && judgeTotalAmountAnd(targetAmountInt);
            // 案例类型和目标金额规则
            case 3:
                String accidentType = Optional.ofNullable(inputContent.get("accidentType")).orElse("");
                String accidentTypeDesc = PreposeMaterialModel.AccidentType.valueOfCode(Integer.parseInt(accidentType)).getDesc();
                return judgeCaseTypeThreeBodyRule(accidentTypeDesc) && judgeTotalAmountAnd(targetAmountInt);
            // 患者身份和目标金额规则
            case 4:
                String patientIdentity = Optional.ofNullable(inputContent.get("patientIdentity")).orElse("");
                String patientIdentityDesc = PreposeMaterialModel.PatientIdentity.valueOfCode(Integer.parseInt(patientIdentity)).getDesc();
                return judgePatientIdentityThreeBodyRule(patientIdentityDesc) && judgeTotalAmountAnd(targetAmountInt);
            // 所在地规则
            case 5:
                String raiseMobile = Optional.ofNullable(inputContent.get("raiseMobile")).orElse("");
                String selfCryptoIdCard = Optional.ofNullable(inputContent.get("selfCryptoIdcard")).orElse("");
                String patientCryptoIdCard = Optional.ofNullable(inputContent.get("patientIdCard")).orElse("");
                return judgeRegionByNumber(raiseMobile, selfCryptoIdCard, patientCryptoIdCard) && judgeTotalAmountAnd(targetAmountInt);
            // 目标金额规则
            case 6:
                return judgeTotalAmountOr(targetAmountInt);
            default:
                return false;
        }

    }

    /**
     * 判断案例是否命中三体
     */
    private Boolean judgeHitThreeBody(CrowdfundingInfo crowdfundingInfo) {

        // 判断目标金额是否符合规则
        if (judgeTotalAmountOr(crowdfundingInfo.getTargetAmount())) {
            log.info("judgeHitThreeBody judgeTotalAmountOr is hit {}", crowdfundingInfo.getId());
            return true;
        }
        if (!judgeTotalAmountAnd(crowdfundingInfo.getTargetAmount())) {
            log.info("judgeHitThreeBody judgeTotalAmountAnd is hit {}", crowdfundingInfo.getId());
            return false;
        }

        // 判断归一疾病是否符合规则
        List<String> normResult = diseaseNormService.getDiseaseNormByCaseId(crowdfundingInfo.getId());
        for (String disease : normResult) {
            if (judgeDiseaseThreeBodyRule(disease)) {
                log.info("judgeHitThreeBody judgeDiseaseThreeBodyRule is hit {}", crowdfundingInfo.getId());
                return true;
            }
        }

        // 判断患者身份是否符合规则
        Response<CfThreeBodyInfo> response = cfClewtrackJudgeConformThreeBodyClient.getThreeBodyInfo(crowdfundingInfo.getId());
        CfThreeBodyInfo cfThreeBodyInfo = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);
        if (Objects.nonNull(cfThreeBodyInfo) && judgePatientIdentityThreeBodyRule(cfThreeBodyInfo.getIdentityType())) {
            log.info("judgeHitThreeBody judgePatientIdentityThreeBodyRule is hit {}", crowdfundingInfo.getId());
            return true;
        }

        // 判断案例类型是否符合规则
        if (Objects.nonNull(cfThreeBodyInfo) && judgeCaseTypeThreeBodyRule(cfThreeBodyInfo.getCaseType())) {
            log.info("judgeHitThreeBody judgeCaseTypeThreeBodyRule is hit {}", crowdfundingInfo.getId());
            return true;
        }

        // 判断所在地是否符合规则
        if (Objects.nonNull(cfThreeBodyInfo) && judgeRegionThreeBodyRule(cfThreeBodyInfo.getProvince())) {
            log.info("judgeHitThreeBody judgeRegionThreeBodyRule is hit {}", crowdfundingInfo.getId());
            return true;
        }
        CfFirsApproveMaterial material = riskDelegate.getCfFirsApproveMaterialByInfoId(crowdfundingInfo.getId());
        if (Objects.nonNull(material) && judgeRegionByMaterial(material)) {
            log.info("judgeHitThreeBody judgeRegionThreeBodyRule is hit {}", crowdfundingInfo.getId());
            return true;
        }

        // 判断年龄是否符合规则
        if (Objects.nonNull(material)) {
            String idCard = StringUtils.isBlank(material.getPatientCryptoIdcard()) ?
                    material.getPatientCryptoIdcard() :
                    shuidiCipher.decrypt(material.getPatientCryptoIdcard());
            log.info("judgeHitThreeBody material idCard {} patientIdType {}", idCard, material.getPatientIdType());
            return UserIdentityType.birth.getCode() == material.getPatientIdType()
                    || judgeAgeThreeBodyRule(idCard);
        }

        log.info("judgeHitThreeBody three body no hit {}", crowdfundingInfo.getId());
        return false;
    }

    private Boolean judgeRegionByMaterial(CfFirsApproveMaterial material) {

        String patientIdCard = material.getPatientCryptoIdcard();
        if (StringUtils.isNotBlank(patientIdCard)) {
            patientIdCard = shuidiCipher.decrypt(patientIdCard);
        }

        String cryptoIdCard = material.getUserRelationTypeForC() == UserRelTypeEnum.SELF.getValue()
                ? material.getPatientCryptoIdcard()
                : material.getSelfCryptoIdcard();
        if (StringUtils.isNotBlank(cryptoIdCard)) {
            cryptoIdCard = shuidiCipher.decrypt(cryptoIdCard);
        }

        UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByUserId(material.getUserId());
        String mobile = Objects.nonNull(userInfoModel) ? shuidiCipher.decrypt(userInfoModel.getCryptoMobile()) : "";

        return judgeRegionByNumber(mobile, cryptoIdCard, patientIdCard);
    }

    /**
     * 判断是否是线上渠道
     */
    private Boolean judgeChannel(CrowdfundingInfo crowdfundingInfo) {
        ChannelRefineDTO refineDTO = buildChannelDTO(crowdfundingInfo);
        Response<String> response = cfChannelFeignClient.getChannelByInfoIdWithUserIdAndOldChannel(refineDTO);
        log.info("saveThreeBodyCaseTab judgeChannel refineDTO={} response={}", refineDTO, response);

        String channel = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse("");
        if (StringUtils.isBlank(channel)) {
            log.info("saveThreeBodyCaseTab 无渠道信息 {}", crowdfundingInfo.getId());
            return false;
        }

        ChannelRefine.ChannelRefineResuleEnum channelRefineResuleEnum = ChannelRefine.ChannelRefineResuleEnum.parse(channel);
        if (Objects.isNull(channelRefineResuleEnum)) {
            log.info("saveThreeBodyCaseTab 无渠道信息 {}", crowdfundingInfo.getId());
            return false;
        }

        return StringUtils.equalsAny(channelRefineResuleEnum.getChannelDesc(),
                ChannelRefine.ChannelRefineResuleEnum.WAIHU_YINDAO.getChannelDesc(),
                ChannelRefine.ChannelRefineResuleEnum.YONGHU_ZIZHU.getChannelDesc(),
                ChannelRefine.ChannelRefineResuleEnum.WEIXIN_1V1.getChannelDesc());
    }

    /**
     * 所在三体所在地规则
     * 所在地 in (regionConfig)
     * idCard 和 mobile 需要解密后的
     */
    private Boolean judgeRegionByNumber(String raiseMobile, String selfCryptoIdCard, String patientIdCard) {

        boolean result = false;

        // 发起人手机号是否符合三体规则
        if (StringUtils.isNotBlank(raiseMobile)) {
            DataAddressByIdCard dataAddressByIdCard = cfAddressDataQueryService.queryAddressByMobile(raiseMobile);
            result = Objects.nonNull(dataAddressByIdCard) && judgeRegionThreeBodyRule(dataAddressByIdCard.getProvince());
        }

        // 发起人身份证号是否符合三体规则
        if (StringUtils.isNotBlank(selfCryptoIdCard)) {
            DataAddressByIdCard dataAddressByIdCard = cfAddressDataQueryService.queryAddressByIdCard(selfCryptoIdCard);
            result = result || (Objects.nonNull(dataAddressByIdCard) && judgeRegionThreeBodyRule(dataAddressByIdCard.getProvince()));
        }

        // 患者身份证号是否符合三体规则
        if (StringUtils.isNotBlank(patientIdCard)) {
            DataAddressByIdCard dataAddressByIdCard = cfAddressDataQueryService.queryAddressByIdCard(patientIdCard);
            result = result || (Objects.nonNull(dataAddressByIdCard) && judgeRegionThreeBodyRule(dataAddressByIdCard.getProvince()));
        }

        return result;
    }

    private Boolean judgeRegionThreeBodyRule(String province) {
        if (StringUtils.isBlank(province)) {
            log.info("judgeRegionThreeBodyRule province is null");
            return false;
        }

        List<String> regions = Lists.newArrayList(threeBodyRuleConfig.getRegionConfig().split(","));
        return regions.contains(province);
    }

    /**
     * 判断三体患者身份规则
     * 患者身份 in (patientIdentityConfig)
     */
    private Boolean judgePatientIdentityThreeBodyRule(String patientIdentity) {
        if (StringUtils.isBlank(patientIdentity)) {
            log.info("judgePatientIdentityThreeBodyRule patientIdentity is null");
            return false;
        }

        List<String> patientIdentityList = Lists.newArrayList(threeBodyRuleConfig.getPatientIdentityConfig().split(","));
        return patientIdentityList.contains(patientIdentity);
    }

    /**
     * 判断三体案例类型规则
     * 案例类型 in (caseTypeConfig)
     */
    private Boolean judgeCaseTypeThreeBodyRule(String caseType) {

        if (StringUtils.isBlank(caseType)) {
            log.info("judgeCaseTypeThreeBodyRule caseType is null");
            return false;
        }

        List<String> caseTypeList = Lists.newArrayList(threeBodyRuleConfig.getCaseTypeConfig().split(","));
        return caseTypeList.contains(caseType);
    }

    /**
     * 判断三体疾病规则
     * 疾病名 in (diseaseConfig)
     */
    private Boolean judgeDiseaseThreeBodyRule(String classifyDiseases) {

        if (StringUtils.isBlank(classifyDiseases)) {
            log.info("judgeDiseaseThreeBodyRule classifyDiseases is null");
            return false;
        }

        List<String> diseaseList = Lists.newArrayList(threeBodyRuleConfig.getDiseaseConfig().split(","));
        return diseaseList.contains(classifyDiseases);
    }

    /**
     * 校验三体年龄规则
     * 年龄 > ageConfig
     * idCard 需要解密后的
     */
    private Boolean judgeAgeThreeBodyRule(String idCard) {

        if (StringUtils.isBlank(idCard)) {
            log.info("judgeAgeThreeBodyRule idCard is null");
            return false;
        }

        int age = AdminCfIdCardUtil.getIdCardAge(idCard);
        return age >= 0 && age <= threeBodyRuleConfig.getAgeConfig();
    }


    /**
     * 判断三体目标金额规则1
     * targetAmount >= totalAmountOrConfig
     */
    private Boolean judgeTotalAmountOr(Integer targetAmount) {
        return threeBodyRuleConfig.getTotalAmountOrConfig() <= targetAmount;
    }

    /**
     * 判断三体目标金额规则2
     * targetAmount >= totalAmountAndConfig
     */
    private Boolean judgeTotalAmountAnd(Integer targetAmount) {
        return threeBodyRuleConfig.getTotalAmountAndConfig() <= targetAmount;
    }

    private void tagThreeBody(String tagReason, Integer caseId) {

        CfMaterialAddOrUpdateVo build = CfMaterialAddOrUpdateVo
                .builder()
                .caseId(caseId)
                .materialName(MaterialExtKeyConst.CASE_THREE_BODY_TAG)
                .materialValue(tagReason)
                .materialLabel("")
                .materialExt("")
                .build();
        RpcResult<String> stringRpcResult = cfMaterialWriteClient.addOrUpdateByFields(caseId, Collections.singletonList(build));
        log.info("CfInitialAuditHandleV2ConsumerService saveInitialAuditSnapshot addOrUpdateFirstApprove stringRpcResult : {}", stringRpcResult);
    }

    private boolean judgeAgeRule(CfFirsApproveMaterial material, CrowdfundingInfo crowdfundingInfo) {

        boolean ruleFlag = lessThanAgeRule(material) && crowdfundingInfo.getTargetAmount() >= highQualityAmountV1;

        return ruleFlag || (bigThanAgeRule(material) && crowdfundingInfo.getTargetAmount() >= highQualityAmountV2);
    }

    private boolean bigThanAgeRule(CfFirsApproveMaterial material) {
        String idCard = material.getPatientCryptoIdcard();
        if (StringUtils.isBlank(idCard)) {
            log.info("saveThreeBodyCaseTab 无患者身份证信息 {}", material.getInfoId());
            return false;
        }
        return AdminCfIdCardUtil.getAge(shuidiCipher.decrypt(idCard)) > highQualityAge;
    }

    private boolean lessThanAgeRule(CfFirsApproveMaterial material) {

        // 出生证发起一定满足小于18岁
        if (UserIdentityType.birth.getCode() == material.getPatientIdType()) {
            return true;
        }

        String idCard = material.getPatientCryptoIdcard();
        if (StringUtils.isBlank(idCard)) {
            log.info("saveThreeBodyCaseTab 无患者身份证信息 {}", material.getInfoId());
            return false;
        }
        return AdminCfIdCardUtil.getAge(shuidiCipher.decrypt(idCard)) <= highQualityAge;
    }

    private ChannelRefineDTO buildChannelDTO(CrowdfundingInfo crowdfundingInfo) {
        ChannelRefineDTO refineDTO = new ChannelRefineDTO();
        refineDTO.setInfoId((long) crowdfundingInfo.getId());
        refineDTO.setChannel(crowdfundingInfo.getChannel());
        refineDTO.setUserId(crowdfundingInfo.getUserId());
        return refineDTO;
    }
}
