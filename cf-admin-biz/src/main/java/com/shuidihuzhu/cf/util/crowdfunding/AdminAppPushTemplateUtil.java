package com.shuidihuzhu.cf.util.crowdfunding;

import com.google.common.collect.ImmutableList;
import com.shuidihuzhu.common.web.model.AbstractModel;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.msg.model.AppPushTemplate;
import com.shuidihuzhu.msg.model.AppPushTemplateParam;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Author: <PERSON>
 * Date: 2017/3/2 15:28
 */
public final class AdminAppPushTemplateUtil {
	private static class AppPushTemplateBuilder extends AbstractModel {
		/***
		 * app push 模版 id
		 */
		private String templateId;

		/***
		 * app push 模版 名字
		 */
		private String templateName;

		/***
		 * app push 模版 title
		 */
		private String templateTitle;

		/***
		 * app push 模版 内容
		 */
		private String content;

		/***
		 * app push 模版 扩展字段
		 */
		private String ext;

		/***
		 * app push 模版 URL
		 */
		private String url;

		/***
		 * 是否有效
		 */
		private Integer valid;

		public AppPushTemplateBuilder setTemplateId(String templateId) {
			this.templateId = templateId;
			return this;
		}

		public AppPushTemplateBuilder setTemplateName(String templateName) {
			this.templateName = templateName;
			return this;
		}

		public AppPushTemplateBuilder setTemplateTitle(String templateTitle) {
			this.templateTitle = templateTitle;
			return this;
		}

		public AppPushTemplateBuilder setContent(String content) {
			this.content = content;
			return this;
		}

		public AppPushTemplateBuilder setExt(String ext) {
			this.ext = ext;
			return this;
		}

		public AppPushTemplateBuilder setUrl(String url) {
			this.url = url;
			return this;
		}

		public AppPushTemplateBuilder setValid(Integer valid) {
			this.valid = valid;
			return this;
		}

		public AppPushTemplate build() {
			return new AppPushTemplate(templateId, templateName, templateTitle, content, ext, url, valid);
		}
	}

	public static AppPushTemplate buildAppPushTemplate(String templateName, String templateTitle, String content, String ext, String url,
	                                                    Integer valid) {
		return new AppPushTemplateBuilder()
				.setTemplateId(buildTemplateId())
				.setTemplateName(templateName)
				.setTemplateTitle(templateTitle)
				.setContent(content)
				.setExt(ext)
				.setUrl(url)
				.setValid(valid)
				.build();
	}

	private static String buildTemplateId() {
		return DateUtil.getTimeConnStringFromTimestamp(DateUtil.nowTime()) +
		       UUID.randomUUID().toString().replaceAll("-", "").substring(0, 15);
	}


	public static class AppPushTemplateParamListBuilder {
		private List<AppPushTemplateParam> appPushTemplateParams;

		private AppPushTemplateParamListBuilder() {appPushTemplateParams = new ArrayList<>();}

		public AppPushTemplateParamListBuilder add(String key, String value) {
			AppPushTemplateParam appPushTemplateParam = new AppPushTemplateParam();
			appPushTemplateParam.setKey(key);
			appPushTemplateParam.setValue(value);
			this.appPushTemplateParams.add(appPushTemplateParam);
			return this;
		}

		public List<AppPushTemplateParam> build() {
			return ImmutableList.copyOf(appPushTemplateParams);
		}

		public static AppPushTemplateParamListBuilder create() {
			AppPushTemplateParamListBuilder appPushTemplateParamBuilder = new AppPushTemplateParamListBuilder();

			return appPushTemplateParamBuilder;
		}
	}


	public static List<AppPushTemplateParam> build(String k1, String v1) {
		AppPushTemplateParam param = new AppPushTemplateParam();
		param.setKey(k1);
		param.setValue(v1);
		return ImmutableList.of(param);
	}

	public static AppPushTemplateParamListBuilder createAppPushTemplateParamListBuilder(){
		return AppPushTemplateParamListBuilder.create();
	}

	public static String limitComment(String comment) {

		if (StringUtils.isBlank(comment)) {
			return comment;
		}

		// 如果字符串长度小于或等于20，直接返回原字符串
		if (StringUtils.length(comment) <= 20) {
			return comment;
		}

		// 截断字符串并添加"..."
		return comment.substring(0, 17) + "...";

	}

}
