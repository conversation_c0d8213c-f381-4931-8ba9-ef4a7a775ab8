package com.shuidihuzhu.cf.util.crowdfunding;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2024/11/22 5:41 PM
 */
public class CharacterUtil {

    public static String replaceCharactersV1(String content) {
        // 正则表达式匹配包含"龢"、"龘"、"龗"中任意一个或多个的情况
        String regex = "[龢龘龗]+";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(content);

        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String matchedGroup = matcher.group();
            // 将匹配到的字符串替换成"龢龘龗"，但是只要匹配到了任意一个字符
            if (matchedGroup.contains("龢") || matchedGroup.contains("龘") || matchedGroup.contains("龗")) {
                matcher.appendReplacement(sb, "龢龘龗");
            } else {
                // 如果没有匹配到这三个字符中的任何一个，则保持原样
                matcher.appendReplacement(sb, matchedGroup);
            }
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    public static String replaceCharactersV2(String content) {
        // 正则表达式匹配包含"龢"、"龘"、"龗"中任意一个或多个的情况
        String regex = "[龖骉齺]+";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(content);

        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String matchedGroup = matcher.group();
            // 将匹配到的字符串替换成"龢龘龗"，但是只要匹配到了任意一个字符
            if (matchedGroup.contains("龖") || matchedGroup.contains("骉") || matchedGroup.contains("齺")) {
                matcher.appendReplacement(sb, "龖骉齺");
            } else {
                // 如果没有匹配到这三个字符中的任何一个，则保持原样
                matcher.appendReplacement(sb, matchedGroup);
            }
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

}
