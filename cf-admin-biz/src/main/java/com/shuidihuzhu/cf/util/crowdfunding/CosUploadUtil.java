package com.shuidihuzhu.cf.util.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.http.HttpMethodName;
import com.qcloud.cos.model.GeneratePresignedUrlRequest;
import com.qcloud.cos.model.PutObjectResult;
import com.qcloud.cos.region.Region;
import com.shuidihuzhu.cf.constants.admin.CosConstants;
import com.shuidihuzhu.cf.store.plugins.CosPlugins;
import com.shuidihuzhu.client.cf.security.model.COSBucketEnum;
import com.shuidihuzhu.infra.starter.cos.configuration.CosClientWrapper;
import com.shuidihuzhu.pf.common.v2.cos.CosPlugin;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.UUID;

/**
 * @author: subing
 * @create: 2020/06/22
 */
@Slf4j
@Service
@Validated
public class CosUploadUtil {

//    private static final String BUCKET = CosConstants.CF_TASK_IMAGES_BUCKET;
    private static final String BUCKET = "cf-risk";
    private static final String BASE_DIR = "cf-risk-admin/txt";

    @Autowired
    private CosPlugins cosPlugin;

    @Resource(name = "cos-cf-risk")
    private CosClientWrapper cosClientWrapper;


    /**
     * 上传文本
     *
     * @param isContent 文本内容
     * @param directory 文本上传目录，首位需要'/' （可以为null）
     * @return cos 访问地址,如果返回为空串，说明没有上传成功
     */
    public String uploadText(@NotBlank(message = "文本内容不能为空") String isContent, @Nullable String directory) {
        try (InputStream is = IOUtils.toInputStream(isContent, StandardCharsets.UTF_8)) {
            String finalDir = BASE_DIR;
            if (StringUtils.isNotBlank(directory)) {
                finalDir = finalDir + directory;
            }
            return cosPlugin.uploadFile(is, BUCKET, finalDir, UUID.randomUUID().toString().replace("-",
                    "").toLowerCase() + ".txt");
        } catch (IOException e) {
            log.error("", e);
            throw new RuntimeException(e);
        }
    }

    public String uploadTextV1(@NotBlank(message = "文本内容不能为空") String isContent, @Nullable String directory) {
        try (InputStream is = IOUtils.toInputStream(isContent, StandardCharsets.UTF_8)) {
            String finalDir = BASE_DIR;
            if (StringUtils.isNotBlank(directory)) {
                finalDir = finalDir + directory;
            }

            String finalFileName = this.getFileName("", UUID.randomUUID().toString().replace("-",
                    "").toLowerCase() + ".txt", finalDir);
            PutObjectResult putObjectResult = cosClientWrapper.putObject(finalFileName, is, null);
            log.info("cos上传 putObjectResult:{}", JSON.toJSONString(putObjectResult));


            String finalUrl = "https://cf-risk-img.shuidichou.com" + finalFileName;

            return finalUrl;
        } catch (IOException e) {
            log.error("", e);
            throw new RuntimeException(e);
        }
    }

    public String getFileName(String originalFileName, String fileName, String directory) {
        String finalFileName = StringUtils.isNotBlank(fileName) ? fileName : this.generateFileName(StringUtils.trimToEmpty(originalFileName));
        finalFileName = directory + "/" + finalFileName;
        if (!StringUtils.startsWith(finalFileName, "/")) {
            finalFileName = "/" + finalFileName;
        }
        return finalFileName;
    }

    public String generateFileName(String originalFileName) {
        int index = originalFileName.lastIndexOf(".");
        String suffix = index > 0 ? originalFileName.substring(index) : "";
        String var10000 = StringUtils.replace(UUID.randomUUID().toString().toUpperCase(), "-", "");
        return var10000 + suffix;
    }
}
