package com.shuidihuzhu.cf.util.crowdfunding;

import org.apache.commons.lang.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

/**
 * @desc LocalDateTimeUtils
 * <AUTHOR>
 */
public class LocalDateTimeUtils {

    public interface PATTERN {
        String HH_MM_SS = "HH:mm:ss";
        String YYYY_MM_DD = "yyyy-MM-dd";
        String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";
        String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    }

    private static final DateTimeFormatter YYYY_MM_DD_HH_MM_SS = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter YYYYMMDDHHMMSS = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    private static final DateTimeFormatter YYYY_MM_DD = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter HH_MM_SS = DateTimeFormatter.ofPattern("HH:mm:ss");

    private static final Map<String, DateTimeFormatter> DATE_TIME_FORMATTER_MAP = new HashMap<>();

    static {
        DATE_TIME_FORMATTER_MAP.put(PATTERN.YYYY_MM_DD_HH_MM_SS, YYYY_MM_DD_HH_MM_SS);
        DATE_TIME_FORMATTER_MAP.put(PATTERN.YYYYMMDDHHMMSS, YYYYMMDDHHMMSS);
        DATE_TIME_FORMATTER_MAP.put(PATTERN.YYYY_MM_DD, YYYY_MM_DD);
        DATE_TIME_FORMATTER_MAP.put(PATTERN.HH_MM_SS, HH_MM_SS);
    }


    public static LocalDate str2LocalDate(String dateStr, String format, Locale locale) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        DateTimeFormatter fmt;
        if (locale == null) {
            fmt = DateTimeFormatter.ofPattern(format);
        } else {
            fmt = DateTimeFormatter.ofPattern(format, locale);
        }
        return LocalDate.parse(dateStr, fmt);
    }

    public static LocalDate str2LocalDate(String dateStr, String format) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern(format);
        return LocalDate.parse(dateStr, fmt);
    }

    public static String localDate2String(LocalDate localDate) {
        return YYYY_MM_DD.format(localDate);
    }

    public static String localDate2String(LocalDate localDate, String pattern) {
        if (localDate == null) {
            return "";
        }
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern);
        return dateTimeFormatter.format(localDate);
    }

    public static LocalDateTime str2LocalDateTime(String dateStr, String format) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern(format);
        return LocalDateTime.parse(dateStr, fmt);
    }

    public static String localDateTime2String(LocalDateTime localDateTime) {
        return YYYY_MM_DD_HH_MM_SS.format(localDateTime);
    }

    public static String localDateTime2String(LocalDateTime localDateTime, String format) {
        if (localDateTime == null || StringUtils.isBlank(format)) {
            return null;
        }
        DateTimeFormatter formatter = DATE_TIME_FORMATTER_MAP.get(format);
        if (formatter == null) {
            formatter = DateTimeFormatter.ofPattern(format);
        }
        return formatter.format(localDateTime);
    }

    public static String formatNow() {
        return YYYY_MM_DD_HH_MM_SS.format(LocalDateTime.now());
    }

    public static String formatNow(String pattern) {
        return DateTimeFormatter.ofPattern(pattern).format(LocalDateTime.now());
    }

    public static String localTime2String(LocalTime localTime) {
        return HH_MM_SS.format(localTime);
    }

    public static LocalTime string2LocalTime(String localTimeStr) {
        return LocalTime.parse(localTimeStr, HH_MM_SS);
    }
}