package com.shuidihuzhu.cf.util.crowdfunding;

import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2019/10/16 下午2:48
 * @desc
 */
public class TimeUtils {
    public static String timeFormat(Integer second){
        if(Objects.isNull(second)){
            return "未知";
        }

        StringBuilder sb = new StringBuilder();
        long days = second / ( 60 * 60 * 24);
        long hours = (second % ( 60 * 60 * 24)) / (60 * 60);
        long minutes = (second % ( 60 * 60)) /60;
        long seconds = second % 60;
        if(days>0){
            sb.append(days).append("d").append(hours).append("h").append(minutes).append("min").append(seconds).append("s");
        }else if(hours>0){
            sb.append(hours).append("h").append(minutes).append("min").append(seconds).append("s");
        }else if(minutes>0) {
            sb.append(minutes).append("min").append(seconds).append("s");
        }else {
            sb.append(seconds).append("s");
        }
        return sb.toString();
    }
}
