package com.shuidihuzhu.webcore;

import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.biz.message.AdminMulitMpSelectStrategy;
import com.shuidihuzhu.cf.service.approve.CfMultipleCaseRiskService;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.web.model.message.HasMulitMpUserInfo;
import com.shuidihuzhu.msg.model.WxRecord;
import com.shuidihuzhu.wx.biz.ShuidiWxService;
import com.shuidihuzhu.wx.model.WxMpConfig;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class MulitMpPushBizTest {

    @Autowired
    AdminMulitMpSelectStrategy adminMulitMpSelectStrategy;
    @Autowired
    ShuidiWxService shuidiWxService;
    @Autowired
    private CfMultipleCaseRiskService cfMultipleCaseRiskService;
    @Test
    public void mulitMpSelectStrategyTest (){
        List<WxRecord> wxRecords = Lists.newArrayList();
        addWxRecords(wxRecords);

        List<WxMpConfig> wxAppInfos = shuidiWxService.getByThirdTypeList(Arrays.asList(3,17,61,115));

        List<HasMulitMpUserInfo> hasMulitMpUserInfos =
                wxRecords.stream().map(wxRecord -> new MulitMpWxRecordAdapter(wxRecord)).collect(Collectors.toList());
        List<Boolean> booleans = adminMulitMpSelectStrategy.selectThenFill(hasMulitMpUserInfos, wxAppInfos);
        System.out.println(booleans);

    }

    private List<WxRecord> addWxRecords(List<WxRecord> wxRecords) {
        WxRecord wxRecord = new WxRecord();
        wxRecord.setBatchId(817906L);
        wxRecord.setUserId(269382215L);
        wxRecord.setType(0);
        wxRecord.setBusinessType(930);
        wxRecord.setBusinessTime(DateUtil.getCurTimestamp());
        wxRecord.setSubBusinessType(0);
        wxRecords.add(wxRecord);

//        for(long i = 0; i<= 500; i++){
//            WxRecord wxRecord3 = new WxRecord();
//            wxRecord3.setBatchId(817906L);
//            wxRecord3.setUserId(97L);
//            wxRecord3.setType(0);
//            wxRecord3.setBusinessType(930);
//            wxRecord3.setBusinessTime(DateUtil.getCurTimestamp());
//            wxRecord3.setSubBusinessType(0);
//            wxRecords.add(wxRecord3);
//        }
        return wxRecords;
    }

    static class MulitMpWxRecordAdapter implements HasMulitMpUserInfo {

        private WxRecord wxRecord;

        public MulitMpWxRecordAdapter(WxRecord wxRecord) {

            this.wxRecord = wxRecord;
        }

        @Override
        public void setOpenId(String openId) {
            wxRecord.setOpenId(openId);
        }

        @Override
        public String getOpenId() {
            return wxRecord.getOpenId();
        }

        @Override
        public Long getUserId() {
            return wxRecord.getUserId();
        }

        @Override
        public void setUserId(Long userId) {
            wxRecord.setUserId(userId);
        }

        @Override
        public int getThirdType() {
            return wxRecord.getUserThirdType();
        }

        @Override
        public void setThirdType(int thirdType) {
            wxRecord.setUserThirdType(thirdType);
        }

        @Override
        public WxRecord getUserInfo() {
            return wxRecord;
        }
    }

}
