package com.shuidihuzhu;

import com.google.common.collect.Maps;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.admin.Application;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.condition.PatternsRequestCondition;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class UrlPermissionCheckTest extends BaseTest {

    @Resource(name = "requestMappingHandlerMapping")
    private RequestMappingHandlerMapping handlerMapping;

    @Test
    public void getAllApi() {
        Map<String, String> urlPermissionMap = Maps.newHashMap();
        Map<RequestMappingInfo, HandlerMethod> map = this.handlerMapping.getHandlerMethods();
        Set<RequestMappingInfo> set = map.keySet();
        for (RequestMappingInfo info : set) {

            HandlerMethod handlerMethod = map.get(info);

            // springmvc的url地址，不包含项目名
            PatternsRequestCondition patternsCondition = info.getPatternsCondition();

            Optional<String> urlOptional = patternsCondition.getPatterns().stream().findFirst();
            if (urlOptional.isEmpty()) {
                continue;
            }
            Method method = handlerMethod.getMethod();
            RequiresPermission permissionAno = getPermissionAno(method);
            if (permissionAno == null) {
                continue;
            }
            String permission = permissionAno.value()[0];
            urlPermissionMap.put(urlOptional.get(), permission);
        }
        System.out.println("urlPermissionMap = " + urlPermissionMap);
        String[] urlsArr = StringUtils.split(URLS, "\n");
        Arrays.stream(urlsArr).forEach(v -> {
            System.out.println(String.format("%s|%s", v, urlPermissionMap.get(v)));
        });

    }

    private RequiresPermission getPermissionAno(Method method) {
        Class<RequiresPermission> ano = RequiresPermission.class;
        RequiresPermission methodAno = method.getAnnotation(ano);
        if (methodAno != null) {
            return methodAno;
        }
        return method.getDeclaringClass().getAnnotation(ano);
    }

    private static String URLS = "/admin/cf/approve/comment/list-by-condition\n" +
            "/innerapi/cf/admin/crm-user-manage/selectUserMapping\n" +
            "/admin/crowdfunding/approve/detail\n" +
            "/admin/cf/content/image/detail\n" +
            "/admin/crowdfunding/approve/comment\n" +
            "/admin/crowdfunding/approve/select-prepose-materials-by-caseId\n" +
            "/admin/workorder/cailiao/cailiao-orderlist\n" +
            "/admin/cf/initial-audit/query-case-detail\n" +
            "/admin/cf/initial-audit/select-raise-basic-info\n" +
            "/admin/workorder/image/content/query-image-content\n" +
            "/admin/crowdfunding/approve/query-reject-history\n" +
            "/admin/workorder/image/content/edit-image-content\n" +
            "/admin/cf/supply/progress/get-detail\n" +
            "/admin/cf/image/water-mark\n" +
            "/admin/cf/sensitive/getnewcomment\n" +
            "/admin/crowdfunding/repeat/get-repeat-list-v2\n" +
            "/innerapi/cf/admin/record/material/audit/selectHandleSnapshot\n" +
            "/admin/cf/custom-relation/get-case-info\n" +
            "/admin/workorder/ugc/ugc-orderlist\n" +
            "/admin/crowdfunding/case/query-search\n" +
            "/admin/cf/approve/comment/get-filter-data-by-case-id\n" +
            "/admin/crowdfunding/approve/approve-list\n" +
            "/admin/crowdfunding/crm-user-manage/query-user-data-by-clewId\n" +
            "/admin/crowdfunding/newreport/report-orderlist\n" +
            "/admin/cf/work-order-flow/get-my-create-list\n" +
            "/admin/crowdfunding/report/get-hospital-audit-info\n" +
            "/admin/cf/initial-audit/query-case-initial-repeat-info\n" +
            "/admin/cf/initial-audit/query-initial-audit-operation-history\n" +
            "/admin/crowdfunding/volunteer/volunteer-list\n" +
            "/admin/material/pic/get\n" +
            "/innerapi/cf/admin/crm-user-manage/bindUserMapping\n" +
            "/admin/crowdfunding/deal-with-status/add\n" +
            "/admin/cf/approve/interaction/detail\n" +
            "/admin/workorder/ugc/ugc-progress-orderlist\n" +
            "/innerapi/cf/admin/q/getByCard\n" +
            "/admin/crowdfunding/report/query-credible-info-list\n" +
            "/admin/cf/report/v2/risk/get-fundraiser-list\n" +
            "/admin/cf/report/v2/problem/query-fundraiser-communicater-list\n" +
            "/admin/cf/report/v2/problem/query-questioner-follow-record\n" +
            "/admin/crowdfunding/approve/approve-search\n" +
            "/admin/cf/work-order-flow/get-unhandled-by-caseId-mobile\n" +
            "/admin/crowdfunding/volunteer/detail\n" +
            "/admin/cf/work-order-flow/get-wait-for-handle-list\n" +
            "/admin/cf/report/v2/problem/query-fundraiser-follow-record\n" +
            "/admin/cf/work-order-flow/get-all-work-order-list\n" +
            "/admin/workorder/juanzhuan/juanzhuan-detail\n" +
            "/admin/cf/work-order-flow/get-work-order-detail\n" +
            "/admin/cf/report/v2/problem/when-new-query-fundraiser-page\n" +
            "/admin/crowdfunding/delete-comment/get-verification-v2\n" +
            "/admin/cf/initial-audit/pre-modify/select-pre-modify-history\n" +
            "/admin/crowdfunding/newreport/query-report-workorder\n" +
            "/admin/cf/report/v2/risk/get-questioner-list\n" +
            "/admin/workorder/get-workorder-list\n" +
            "/admin/crowdfunding/report/get-detail-report-list\n" +
            "/admin/crowdfunding/delete-comment/get-attachment\n" +
            "/admin/cf/initial-audit/edit-case-base-info-v2\n" +
            "/admin/crowdfunding/delete-comment/get-progress-v2\n" +
            "/admin/cf/work-order-flow/get-case-title-by-case-id\n" +
            "/admin/cf/progress/audit/get-list\n" +
            "/admin/workorder/cailiao/select-call-record-all\n" +
            "/admin/crowdfunding/user/manage/query-user-manage-detail-info\n" +
            "/admin/cf/work-order-flow/get-finish-handle-list\n" +
            "/admin/crowdfunding/delete-comment/get-order\n" +
            "/admin/cf/content/image/detail-supplement\n" +
            "/admin/crowdfunding/user/manage/query-user-behavior-detail-info\n" +
            "/admin/cf/modifymobile/query-case-baseinfo\n" +
            "/admin/crowdfunding/user/manage/query-user-manage-base-info\n" +
            "/admin/crowdfunding/approve/query-publish-image-content\n" +
            "/admin/crowdfunding/report/list-hospital-audit-remark\n" +
            "/admin/crowdfunding/report/list-hospital-audit-snapshot\n" +
            "/admin/cf/work-order-flow/get-work-flow-view-by-ids\n" +
            "/admin/crowdfunding/approve/pause\n" +
            "/admin/crowdfunding/approve/do-approve\n" +
            "/admin/cf/report/credible-info/prove-info-record\n" +
            "/admin/crowdfunding/approve/contact-search\n" +
            "/admin/cf/report/v2/problem/save-report-problem-answer\n" +
            "/admin/cf/initial-audit/pre-modify/select-idcard\n" +
            "/admin/crowdfunding/report/get-call-comment-record\n" +
            "/admin/cf/supply/progress/show-progress\n" +
            "/admin/crowdfunding/approve/select-reminder\n" +
            "/innerapi/cf/admin/crm-user-manage/buildMappingAtChangeMobile\n" +
            "/innerapi/cf/admin/flow/getFlowOrderResultByid\n" +
            "/admin/crowdfunding/approve/user-report-brief\n" +
            "/admin/crowdfunding/approve/contact-list\n" +
            "/admin/cf/suspected-case/get-list\n" +
            "/innerapi/cf/admin/pr/selectCase\n" +
            "/admin/workorder/chuci/chuci-orderlist\n" +
            "/admin/crowdfunding/case/case-details-msg/select\n" +
            "/admin/cf/report/credible-info/get-prove-info\n" +
            "/admin/cf/initial-audit/query-last-chushen-work-case-detail\n" +
            "/admin/cf/approve/control/get-control-info\n" +
            "/admin/cf/report/v2/problem/query-call-record\n" +
            "/admin/crowdfunding/report/query-hospital-check-info\n" +
            "/admin/cf/supply/progress/get-detail-list\n" +
            "/admin/crowdfunding/delete-comment/query-ugc-operate-record\n" +
            "/admin/cf/usercomment/getcommentbytype\n" +
            "/admin/cf/organization/search-organization-emplyees\n" +
            "/admin/cf/approve/credit/detail\n" +
            "/admin/cf/order/handle/get-order-list\n" +
            "/admin/workorder/image/content/query-operate-log\n" +
            "/admin/workorder/record/list-by-work-order-id\n" +
            "/admin/workorder/juanzhuan/juanzhuan-orderlist\n" +
            "/innerapi/cf/admin/supply/getByActionId\n" +
            "/admin/cf/activity/subsidy/list-subsidy-record-by-anchor\n" +
            "/admin/crowdfunding/report/query-add-trust-info\n" +
            "/admin/cf/supply/progress/get-reject-list\n" +
            "/admin/cf/work-order-flow/get-users-by-name-like\n" +
            "/admin/workorder/tw/tw-orderlist\n" +
            "\"\"\n" +
            "/admin/crowdfunding/volunteer/case-info\n" +
            "/admin/cf/report/v2/problem/query-questioner-answer-detail-in-fundraiser\n" +
            "/admin/cf/supply/progress/get-action-list\n" +
            "/admin/ugc/while-list/manager/get-reason\n" +
            "/admin/crowdfunding/user/manage/query-user-relation-detail-info\n" +
            "/admin/workorder/ugc/supply/progress/history\n" +
            "/admin/crowdfunding/report/get-add-trust-info\n" +
            "/admin/cf/pageconfig/list\n" +
            "/innerapi/cf/admin/get-url-watermark-by-types\n" +
            "/admin/cf/work/order/report/crowdfundingInfo-select\n" +
            "/admin/cf/initial-audit/select-disease-snapshot\n" +
            "/admin/crowdfunding/risk/history/search\n" +
            "/admin/cf/report/v2/problem/get-last-fundraiser-follow-record\n" +
            "/admin/crowdfunding/approve/add-reminder\n" +
            "/admin/cf/initial-audit/query-patient-idcard\n" +
            "/admin/crowdfunding/delete-comment/get-comment\n" +
            "/admin/cf/river/list-operation-record\n" +
            "/admin/crowdfunding/change-amount/get-info\n" +
            "/innerapi/cf/admin/initial-modify/select-id-card-for-pre-modify\n" +
            "/admin/crowdfunding/report/list-hospital-audit\n" +
            "/admin/cf/hospital-normal/get-hospitals\n" +
            "/admin/crowdfunding/report/get-add-trust-audit-record\n" +
            "/admin/crowdfunding/approve/get-report-comment-list\n" +
            "/admin/workorder/cailiao/select-call-record\n" +
            "/admin/cf/case/risk/abnormal/list-by-page-condition\n" +
            "/admin/cf/sensitive/get-my-only-baseInfo-list\n" +
            "/admin/cf/organization/get-organization-managers\n" +
            "/admin/cf/pr/select-case\n" +
            "/admin/crowdfunding/volunteer/get-nomask-case-info\n" +
            "/admin/cf/pr/compile-case\n" +
            "/admin/cf/visit-config/list\n" +
            "/admin/cf/initial-audit/judge-disease-strategy\n" +
            "/admin/crowdfunding/report/get-work-order-hospital-audit-snapshot\n" +
            "/admin/cf/order/handle/get-case-order\n" +
            "/admin/cf/ocr-approve/decide-info-amount";


}
