package com.shuidihuzhu.jingxi;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.BaseTest;
import com.shuidihuzhu.cf.service.workorder.read.impl.AbstractFinanceWorkOrderCheckHandler;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class WorkOrderCheckHandlerTest extends BaseTest {

    @Autowired
    private List<AbstractFinanceWorkOrderCheckHandler> workOrderCheckHandlers;

    Map<Integer, AbstractFinanceWorkOrderCheckHandler> workOrderCheckHandlerMap;

    @PostConstruct
    public void init() {
        workOrderCheckHandlerMap = workOrderCheckHandlers.stream()
                .collect(Collectors.toMap(r -> r.getWorkOrderType().getType(), Function.identity()));
    }


    /**
     * 19	2040510
     * 90	2040523
     * 91	2026073
     * 92	1606690
     * 93	1851323
     * 94	1602132
     * 95	2040384
     * 96	2037123
     * 97	1813562
     * 98	2020148
     */

    @Test
    public void getExtInfoTest() {
        HashMap<Integer, Long> orderMap = new HashMap<>() {{
            put(19, 2040510L);
            put(90, 2040523L);
            put(91, 2026073L);
            put(92, 1606690L);
            put(93, 1851323L);
            put(94, 1602132L);
            put(95, 2040384L);
            put(96, 2037123L);
            put(98, 2020148L);
        }};

        orderMap.forEach((orderType, workOrderId) -> {
            Map<String, Object> extInfo = this.workOrderCheckHandlerMap.get(orderType).getExtInfo(workOrderId, orderType);
            log.info("orderType:{} workOrderId:{}, ext:{}", orderType, workOrderId, JSON.toJSONString(extInfo));
        });

    }
}
