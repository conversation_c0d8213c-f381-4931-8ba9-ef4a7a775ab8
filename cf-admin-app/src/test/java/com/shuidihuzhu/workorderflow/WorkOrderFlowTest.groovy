package com.shuidihuzhu.workorderflow

import com.alibaba.fastjson.JSON
import com.shuidihuzhu.BaseTest
import com.shuidihuzhu.cf.biz.admin.AdminOrganizationBiz
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderFlowBiz
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst
import com.shuidihuzhu.cf.model.admin.AdminOrganization
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView
import com.shuidihuzhu.common.web.model.Response
import org.apache.commons.collections.CollectionUtils
import org.junit.Test

import javax.annotation.Resource

class WorkOrderFlowTest extends BaseTest {

    @Resource
    AdminOrganizationBiz organizationBiz
    @Resource
    AdminWorkOrderFlowBiz adminWorkOrderFlowBiz

    @Test
    void test() {
        AdminWorkOrderFlowView vo = new AdminWorkOrderFlowView();
        vo.setLevel(AdminWorkOrderConst.Level.MEDIUM.getCode());
        vo.setCaseId(3027071);
        vo.setCaseTitle('测试 title');
        vo.setMobile('12345671231');
        vo.setUserIdentity(AdminWorkOrderFlowView.WorkOrderFlowUserIdentity.DEFAULT.getCode());
        vo.setProblemContent("患者去世，用户主动停止筹款，需跟进");
        vo.setNewFirstClassifyId(3161);
        vo.setNewSecondClassifyId(3166);
        vo.setNewThirdClassifyId(3168);
        vo.setCreatorId(102);
        vo.setProblemType(getOrgIdByName("石家庄二线综合处理组"))

        Response result = adminWorkOrderFlowBiz.createWorkOrderFlow(vo)
        println(JSON.toJSONString(result))
    }


   def getOrgIdByName(String orgName) {
        List<AdminOrganization> orgList = organizationBiz.getAdminOrganizationByName(orgName);
        return CollectionUtils.isEmpty(orgList) ? 0 : orgList.get(0).getId();
    }
}
