package com.shuidihuzhu.cf;

import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingInfoSlaveDao;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cf.service.bigdata.BiClientService;
import com.shuidihuzhu.client.dataservice.biservice.v1.BiServiceClient;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2018-07-17  11:33
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class MsgTest {

    static {
        System.setProperty("spring.cloud.consul.host", "consul.zelda.shuiditech.com:80");
    }

    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Resource
    private AdminApproveService adminApproveService;



    @Autowired
    private BiClientService biClientService;
    @Autowired
    private BiServiceClient biServiceClient;

    // 976655
    private static final String INFO_UUID = "366e4bd8-c47e-4445-b7bc-3b7bac5afe51";
    private static final long USER_ID_A = 35572L;
    private static final long USER_ID_B = 244113933L;
    private static final long USER_ID_C = 269384744L;



    @Autowired
    private AdminCrowdfundingInfoSlaveDao crowdfundingInfoSlaveDao;

    @Test
    public void getFundingInfoListTest() {
        for (int i = 0; i < 100; i++) {
            CrowdfundingInfo fundingInfo = crowdfundingInfoSlaveDao.getFundingInfoById(i);
        }
        System.out.println("over");
    }





}
