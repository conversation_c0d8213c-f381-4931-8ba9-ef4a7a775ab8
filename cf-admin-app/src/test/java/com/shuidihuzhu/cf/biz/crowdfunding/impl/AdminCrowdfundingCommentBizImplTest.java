package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingCommentBiz;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingComment;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class AdminCrowdfundingCommentBizImplTest {

    @Autowired
    private AdminCrowdfundingCommentBiz adminCrowdfundingCommentBiz;

    @Test
    public void test_GetByIdNoCareDeleted() {

        CrowdfundingComment byIdNoCareDeleted = adminCrowdfundingCommentBiz.getByIdNoCareDeleted(27, 10);
        System.out.println(byIdNoCareDeleted);
    }

    @Test
    public void test_getCommentByParentId(){
        List<CrowdfundingComment> commentByParentId = adminCrowdfundingCommentBiz.getCommentByParentId(10, 26968);
        System.out.println(commentByParentId);
    }
}