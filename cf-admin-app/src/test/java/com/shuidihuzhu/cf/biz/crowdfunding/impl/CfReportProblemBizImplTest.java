package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.*;
import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.biz.crowdfunding.CfReportProblemBiz;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.*;
import com.shuidihuzhu.cf.service.crowdfunding.report.CfReportProblemManagerService;
import com.shuidihuzhu.cf.service.crowdfunding.report.CfReportProblemService;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2019-12-12 10:23
 **/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class CfReportProblemBizImplTest {

    @Autowired
    CfReportProblemBiz problemBiz;


    @Autowired
    CfReportProblemService problemService;

    @Autowired
    CfReportProblemManagerService managerService;

    static {
        System.setProperty("spring.cloud.consul.host", "consul.zelda.shuiditech.com:80");
    }

    Multimap<String, String> labelMap = ArrayListMultimap.create();
    List<String> firstLabels = Lists.newArrayList();

    @Before
    public void setUp() throws Exception {
        List<String> label1 = Lists.newArrayList("房屋信息", "车辆信息", "收入信息", "其他实物资产");
        labelMap.putAll("家庭经济情况", label1);
        List<String> label2 = Lists.newArrayList("商业保险", "有价证券", "理财产品", "存款", "其它金融资产", "负债");
        labelMap.putAll("金融资产", label2);
        List<String> label3 = Lists.newArrayList("事故赔偿", "平台多次筹款", "政府救助", "其他救助");
        labelMap.putAll("额外救助", label3);
        List<String> label4 = Lists.newArrayList("治疗花费", "去世/在世", "医保报销");
        labelMap.putAll("病情情况", label4);
        List<String> label5 = Lists.newArrayList("用于多位患者", "用于丧葬费", "偿还医院欠款", "偿还个人欠款", "不符合平台规定用途", "其它使用用途");
        labelMap.putAll("款项使用", label5);
        List<String> label6 = Lists.newArrayList("敏感身份", "敏感职业", "虚假信息", "舆情关注", "其他举报");
        labelMap.putAll("其他", label6);

        firstLabels = Lists.newArrayList("家庭经济情况", "金融资产",
                "额外救助", "病情情况", "款项使用", "其他");

    }

    @Test
    public void insertData() {
        insertLabel();
        testCreateProblem();
        bindRelationship();
    }


    @Test
    public void testUpdateProblem() {
        CfReportProblemParam problemParam = new CfReportProblemParam();
        problemParam.setProblemType(3);
        problemParam.setId(7);
        problemParam.setProblem("房贷");
        problemParam.setAnswerType(2);
        problemParam.setDirectShow(1);
        problemParam.setChoiceDescribes(Lists.newArrayList("有","无"));

        managerService.updateProblem(1, problemParam);

        //List<CfReportProblemParam.BindParam> bindParams = Lists.newArrayList(
        //        build(7, 8, "有", 3),
        //        build(7, 9, "有", 3),
        //        build(7, 10, "有", 3)
        //);
        //Response<Boolean> booleanResponse = managerService.bindProblem(bindParams);
        //log.info("booleanResponse:{}", JSON.toJSONString(booleanResponse));
    }

    public void insertLabel() {
        for (String firstLabel : firstLabels) {
            CfReportProblemLabel label = new CfReportProblemLabel();
            label.setLabelLevel(1);
            label.setLabelDesc(firstLabel);
            problemBiz.insertLabel(label);
        }
        for (String firstLabel : firstLabels) {
            for (String secondLabel : labelMap.get(firstLabel)) {
                CfReportProblemLabel firstProblemLabel = problemBiz.findLabelByDesc(firstLabel);
                CfReportProblemLabel second = new CfReportProblemLabel();
                second.setLabelLevel(2);
                second.setParentId(firstProblemLabel.getId());
                second.setLabelDesc(secondLabel);
                problemBiz.insertLabel(second);
            }
        }
    }

    @Test
    public void listLabels() {
        Response<List<CfReportProblemLabel>> listResponse = problemService.listAllLabel(null, null, false, 0, 0, 0, 0);
        log.info("listResponse:{}", JSON.toJSONString(listResponse));
    }


    public void testCreateProblem() {
        int choice = CfReportProblem.ReportProblemType.xiala.getCode();
        int text = CfReportProblem.ReportProblemType.shuru.getCode();
        int intType = CfReportProblem.ReportAnswerType.shuzhi.getCode();
        int zhifuchuan = CfReportProblem.ReportAnswerType.zhifuchuan.getCode();
        List<CfReportProblemParam> problem1 = Lists.newArrayList(
                buildProblemParam("房屋数量", text, intType, 0, null),
                buildProblemParam("房产类型", choice, zhifuchuan, 1, Lists.newArrayList("二手房", "商品房", "非商品房", "商铺")),
                buildProblemParam("产权", choice, zhifuchuan, 1, Lists.newArrayList("配偶", "父母", "子女", "祖父母或外祖父母", "孙子女或外孙子女", "非直系亲属")),
                buildProblemParam("关系", text, zhifuchuan, 1, Lists.newArrayList()),
                buildProblemParam("价格", text, intType, 1, Lists.newArrayList()),
                buildProblemParam("地址-国内外", choice, zhifuchuan, 1, Lists.newArrayList("国内", "国外")),
                buildProblemParam("是否有房贷", choice, zhifuchuan, 1, Lists.newArrayList("有", "无")),
                buildProblemParam("贷款总额", text, intType, 1, Lists.newArrayList()),
                buildProblemParam("贷款期数", text, intType, 1, null),
                buildProblemParam("能否变卖", choice, zhifuchuan, 1, Lists.newArrayList("能", "不能")),
                buildProblemParam("不能变卖原因", text, zhifuchuan, 1, Lists.newArrayList()),
                buildProblemParam("变卖进展", choice, zhifuchuan, 1, Lists.newArrayList("考虑中", "不考虑变卖", "变卖中", "已变卖")),
                buildProblemParam("不考虑变卖原因", text, zhifuchuan, 1, null),
                buildProblemParam("变卖金额", text, intType, 1, null)
        );
        for (CfReportProblemParam problemParam : problem1) {
            problemParam.setLabelId(7);
            managerService.createProblem(problemParam);
        }
    }


    public void bindRelationship() {
        List<CfReportProblemParam.BindParam> bindParams = Lists.newArrayList(
                build(1, 2, null, 1),
                build(1, 3, null, 1),
                build(1, 5, null, 1),
                build(1, 6, null, 1),

                build(6, 7, "国内", 3),
                build(3, 4, "非直系亲属", 3),
                build(7, 8, "有", 3),
                build(7, 9, "有", 3),
                build(7, 10, "有", 3),
                build(10, 11, "不能", 3),
                build(10, 12, "可以", 3),
                build(12, 13, "不考虑变卖", 3),
                build(12, 14, "变卖中", 3),
                build(12, 14, "已变卖", 3)
        );

        Response<Boolean> booleanResponse = managerService.bindProblem(bindParams);
        log.info("booleanResponse:{}", JSON.toJSONString(booleanResponse));
    }



    public CfReportProblemParam buildProblemParam(String problem, int problemType, int answerType, int directShow, List<String> choiceDescribes) {
        CfReportProblemParam problemParam = new CfReportProblemParam();
        problemParam.setProblemType(problemType);
        problemParam.setProblem(problem);
        problemParam.setAnswerType(answerType);
        problemParam.setDirectShow(directShow);
        problemParam.setChoiceDescribes(choiceDescribes);
        return problemParam;
    }


    @Test
    public void findById() {
        Response<CfReportProblemVo> problemVo = managerService.getByProblemId(7);
        log.info("listResponse:{}", JSON.toJSONString(problemVo));
    }

    @Test
    public void listByIds() {
        Response<List<CfReportProblemVo>> listResponse = problemService.listRelateProblem(6, "国内");
        log.info("listResponse:{}", JSON.toJSONString(listResponse));
    }

    @Test
    public void listDirectShow() {
        Response<List<CfReportProblemDefaultVo>> listResponse = problemService.listDirectShowProblem(Lists.newArrayList(7),1);
        log.info("listResponse:{}", JSON.toJSONString(listResponse));
    }


    @Test
    public void testListProblemByPage() {
        Response<CfReportProblemPageResult> pageResultResponse = managerService.listProblem("", 1, 20, 7,1, null);
        log.info("pageResultResponse:{}", JSON.toJSONString(pageResultResponse));
    }


    @Test
    public void testListSameLabelProblem() {
        Response<List<CfReportProblemVo>> listResponse = managerService.listSameLabelProblem(1, 7);
        log.info("listResponse:{}", JSON.toJSONString(listResponse));
    }

    public CfReportProblemParam.BindParam build(int problemId, int nextProblemId, String choice, int problemType) {
        CfReportProblemParam.BindParam bindParam = new CfReportProblemParam.BindParam();
        bindParam.setProblemId(problemId);
        bindParam.setNextProblemId(nextProblemId);
        bindParam.setChoice(choice);
        bindParam.setProblemType(problemType);
        return bindParam;
    }
}