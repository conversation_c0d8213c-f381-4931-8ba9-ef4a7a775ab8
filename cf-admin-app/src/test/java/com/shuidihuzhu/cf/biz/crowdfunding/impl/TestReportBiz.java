package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.admin.mq.CfFinanceBroadcastComsumer;
import com.shuidihuzhu.cf.admin.mq.casestatus.AdminWorkOrderCreateConsumer;
import com.shuidihuzhu.cf.admin.mq.record.RecordSubmitConsumer;
import com.shuidihuzhu.cf.admin.mq.record.SdDataSubmitConsumer;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingOperationEnum;
import com.shuidihuzhu.cf.finance.enums.BroadcastMsgEnum;
import com.shuidihuzhu.cf.finance.model.CfFinanceBroadcastMsg;
import com.shuidihuzhu.cf.model.admin.workorder.JuanzhuanDetailVo;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cf.model.event.CailiaoConditionEvent;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistCallPhaseEnum;
import com.shuidihuzhu.cf.service.AdminEventPublishService;
import com.shuidihuzhu.cf.service.crowdfunding.CfAdminLimitService;
import com.shuidihuzhu.cf.service.crowdfunding.CfCailiaoService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditCreateOrder;
import com.shuidihuzhu.cf.service.workorder.juanzhan.JuanzhuanWorkOrderService;
import com.shuidihuzhu.cf.vo.crowdfunding.WorkOrderFirstApprove;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewTrackCallRobotClient;
import com.shuidihuzhu.client.cf.growthtool.model.PreposeMaterialCommitNoticeModel;
import com.shuidihuzhu.client.cf.workorder.CfChuciWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.ChuciWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.model.event.WorkOrderResultChangeEvent;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@RefreshScope
public class TestReportBiz {



    @Autowired
    private AdminWorkOrderReportBiz adminWorkOrderReportBiz;

    @Autowired
    private AdminCrowdfundingReportBiz reportBiz;

    @Autowired
    private CfChuciWorkOrderClient chuciWorkOrderClient;

    @Autowired
    private AdminEventPublishService adminEventPublishService;

    @Autowired
    private RecordSubmitConsumer submitConsumer;

    @Autowired
    private CfCailiaoService cfCailiaoService;

    @Autowired
    private JuanzhuanWorkOrderService juanzhuanWorkOrderService;

    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Autowired
    private CfAdminLimitService limitService;

    @Autowired
    private CfLabelMarkBiz cfLabelMarkBiz;

    @Autowired
    private CfFinanceBroadcastComsumer cfFinanceBroadcastComsumer;

    static {
        System.setProperty("spring.cloud.consul.host","consul.bedin.shuiditech.com:80");
    }


    @Test
    public void test1(){
        List<AdminReportDataVo> list = adminWorkOrderReportBiz.getAdminReportDataVo(1,20,null,null,null,null,null,null,null,null,null,null);

        System.out.println("AdminReportDataVo"+list);
    }


    @Test
    public void testadd(){
        AdminWorkReportMap map = new AdminWorkReportMap(234234234234L,123,2323,"2323","323232");
        adminWorkOrderReportBiz.addAdminWorkReportMap(map);
    }

    @Test
    public void testdddd(){
        reportBiz.checkReportByTimes();
    }

    @Test
    public void  testadddd(){
        adminWorkOrderReportBiz.updateHandkeTime(Lists.newArrayList(10440L,10441L));
    }

    @Test
    public void testsort(){
        List<WorkOrderFirstApprove> firstApproves = Lists.newArrayList();

        WorkOrderFirstApprove f0 = new WorkOrderFirstApprove();
        f0.setChannelStr("lllll");
        f0.setWorkOrderId(10);

        WorkOrderFirstApprove f1 = new WorkOrderFirstApprove();
        f1.setChannelStr("cf_volunteer");
        f1.setWorkOrderId(8);
        WorkOrderFirstApprove f2 = new WorkOrderFirstApprove();
        f2.setChannelStr("cf_volun");
        f2.setWorkOrderId(7);
        firstApproves.add(f0);
        firstApproves.add(f1);
        firstApproves.add(f2);
        System.out.println("AdminReportDataVo   "+f1.getChannelStr().equals("线下"));
        List<WorkOrderFirstApprove> fi = firstApproves.stream().sorted(Comparator.comparing(WorkOrderFirstApprove::getChannelStr).reversed()).collect(Collectors.toList());

        System.out.println("AdminReportDataVo   "+firstApproves);
        System.out.println("AdminReportDataVo   "+fi);


    }

    @Test
    public void fefoo(){

        int caseId = 983306;

        //创建初审工单
        ChuciWorkOrder w = new ChuciWorkOrder();
        w.setCaseId(caseId);
        w.setOrderType(WorkOrderType.shenhe.getType());
        Response<Long> response = chuciWorkOrderClient.createChuci(w);
        log.info("createChuci caseId={} response={}",caseId,response.getCode());
        long a = response.getData();
        log.info("*********** {} ",a);
    }

    @Test
    public void fefeeeee(){

        ConsumerMessage<PreposeMaterialCommitNoticeModel> c = new ConsumerMessage<>();
        PreposeMaterialCommitNoticeModel o = new PreposeMaterialCommitNoticeModel();
        o.setPreposeMaterialId(100L);
        o.setSourceName("12222");
        o.setServiceChannel("走你");
        o.setOrgName("fffff");
        o.setSourceName("fdfdf");
        o.setUserEncryptPhone("*************");
        o.setPatientIdCard("afewfawefaewf");
        c.setPayload(o);

        submitConsumer.consumeMessage(c);
    }


    @Test
    public void eeat(){
        boolean f = PreposeMaterialCommitNoticeModel.ServiceChannelEnum.WEIXIN_1V1.getServiceChannelName().equals("微信1V1服务");
        log.info("*********** {} ",f);
    }

    @Test
    public void ceee(){
        CailiaoConditionEvent event = new CailiaoConditionEvent(this,1994476, "9bc39988-6589-40c7-b545-f94c0e7768bd");
        event.setAmount(100);
        event.setOperation(CrowdfundingOperationEnum.NONE_OPERATION.value());
        adminEventPublishService.publish(event);
        cfCailiaoService.createWorkOrder(event);
    }

    @Test
    public void eoeop(){
        OpResult<JuanzhuanDetailVo> f = juanzhuanWorkOrderService.getJuanzhuanDetailVo(2293099,1);

        log.info("*********** {} ",f);

    }

    @Test
    public void testccc(){
//        juanzhuanWorkOrderService.createJuanzhuanD0(739885056,2287000);

//        List<CrowdfundingOrder> orders = adminCrowdfundingOrderBiz.getByPage(2287000,0,1);
//
//        log.info("*********** {} ",orders);

        Map<Integer, CfInfoStat> m =crowdfundingDelegate.mapByIds(Lists.newArrayList(2288576,2287000,2288582));
        log.info("*********** {} ",JSON.toJSONString(m));

    }

    @Test
    public void fefeoo(){
        boolean b = limitService.caseInLimit(2293822, BlacklistCallPhaseEnum.SUBMIT_PRE_TRIAL);
        log.info("*********** {} ",b);

    }

    @Test
    public void fefeffaef(){
        CrowdfundingInfo c = crowdfundingDelegate.getCaseInfoById(2299549);
        cfLabelMarkBiz.addQQLabel(c);
    }
    @Test
    public void eoqwerqwr(){

        CfFinanceBroadcastMsg msg = new CfFinanceBroadcastMsg();
        msg.setCaseId(2299688);
        msg.setBizType(BroadcastMsgEnum.BizType.DRAW_FAILED_ONE);
        ConsumerMessage c = ConsumerMessage.dummyMessage();
        c.setPayload(msg );
        cfFinanceBroadcastComsumer.consumeMessage(c);
    }

    //68093
    @Test
    public void ocrrr(){

        WorkOrderResultChangeEvent d = new WorkOrderResultChangeEvent();
        d.setWorkOrderId(68093);
        d.setHandleResult(HandleResultEnum.undoing.getType());
        d.setOrderType(WorkOrderType.cailiao_5.getType());
        ConsumerMessage c = new ConsumerMessage();
        c.setPayload(d);
//        createConsumer.handle(c);
    }

}
