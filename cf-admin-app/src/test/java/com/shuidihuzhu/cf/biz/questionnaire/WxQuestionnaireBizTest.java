package com.shuidihuzhu.cf.biz.questionnaire;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.model.common.BaseResult;
import com.shuidihuzhu.cf.model.questionnaire.WxQuestion;
import com.shuidihuzhu.cf.vo.questionnaire.WxSdTagValueAdminVo;
import com.shuidihuzhu.cf.vo.questionnaire.WxSdTagAdminVo;
import com.shuidihuzhu.pay.common.model.PageRequest;
import com.shuidihuzhu.pay.common.model.PageResponse;
import com.shuidihuzhu.pay.common.util.PageUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class WxQuestionnaireBizTest {
    @Autowired
    WxQuestionnaireAdminBiz wxQuestionnaireBiz;

    @Test
    public void saveTest(){
        List<WxSdTagAdminVo> list = this.wxQuestionnaireBiz.getAllTagList();
        System.out.println(list);
        WxSdTagAdminVo wxSdTag = list.get(0);

        JSONArray jsonArray = getOptions(wxSdTag);

        WxQuestion question = new WxQuestion();
        question.setTagId(wxSdTag.getId());
        question.setTagName(wxSdTag.getTagName());
        question.setContent("这是问题1");
        question.setReplyType(1);
        question.setSequence(99);
        question.setOption(JSON.toJSONString(jsonArray));
        question.setRemark("备注备注备注备注备注备注");

        WxQuestion question2 = new WxQuestion();
        question2.setTagId(4);
        question2.setTagName("标签3");
        question2.setContent("这是问题2");
        question2.setReplyType(0);
        question2.setSequence(99);
        question2.setOption(JSON.toJSONString(jsonArray));
        question2.setRemark("备注备注备注备注备注备注3");

        List<WxQuestion> wxQuestions = Arrays.asList(question, question2);
        String s = JSON.toJSONString(wxQuestions);
        System.out.println(s);
        BaseResult<Boolean> result = wxQuestionnaireBiz.addWxQuestionnaire("测试问卷5", "测试问卷1测试问卷1测试问卷1",
                wxQuestions, "admin");
        Assert.assertTrue(result.getData());
    }

    @Test
    public void editTest(){
        List<WxSdTagAdminVo> list = this.wxQuestionnaireBiz.getAllTagList();
        System.out.println(list);
        WxSdTagAdminVo wxSdTag = list.get(1);

        JSONArray jsonArray = getOptions(wxSdTag);

        WxQuestion question = new WxQuestion();
        question.setTagId(wxSdTag.getId());
        question.setTagName(wxSdTag.getTagName());
        question.setContent("这是问题1111");
        question.setReplyType(1);
        question.setSequence(99);
        question.setOption(JSON.toJSONString(jsonArray));
        question.setRemark("备注备注备注备注备注备注");
        WxQuestion question2 = new WxQuestion();
        question2.setTagId(4);
        question2.setTagName("标签3");
        question2.setContent("这是问题2222");
        question2.setReplyType(1);
        question2.setSequence(99);
        question2.setOption(JSON.toJSONString(jsonArray));
        question2.setRemark("备注备注备注备注备注备注3");

        List<WxQuestion> wxQuestions = Arrays.asList(question, question2);
        String s = JSON.toJSONString(wxQuestions);

        BaseResult<Boolean> result = this.wxQuestionnaireBiz.editWxQuestionnaire(2, "2的页面新名称", "2的新标题", wxQuestions, "admin");
        Assert.assertTrue(result.getData());
    }

    private JSONArray getOptions(WxSdTagAdminVo wxSdTag) {
        JSONArray jsonArray = new JSONArray();
        List<WxSdTagValueAdminVo> tagValues = this.wxQuestionnaireBiz.getTagValues(wxSdTag.getId());
        tagValues.stream().forEach(tag -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("label", tag.getLabel());
            jsonObject.put("value", tag.getValue());
            jsonArray.add(jsonObject);
        });
        return jsonArray;
    }

    @Test
    public void getTagValuesTest(){
        List<WxSdTagValueAdminVo> tagValues = this.wxQuestionnaireBiz.getTagValues(-1);
        System.out.println(tagValues);
    }

    @Test
    public void  getWxQuestionnairePageTest(){
        PageRequest pageRequest = PageUtil.parseJsonString( "{\"pageType\":1,\"startId\":1,\"endId\":5,\"pageSize\":5,\"limit\":5}");
        PageResponse pageResponse = this.wxQuestionnaireBiz.getWxQuestionnairePage("", "", "", "", pageRequest);
      pageRequest = PageUtil.parseJsonString( "{\"pageType\":1,\"startId\":6,\"endId\":5,\"pageSize\":5,\"limit\":5}");
        PageResponse pageResponse2 = this.wxQuestionnaireBiz.getWxQuestionnairePage("", "", "", "", pageRequest);

        System.out.println(pageResponse);
    }
}
