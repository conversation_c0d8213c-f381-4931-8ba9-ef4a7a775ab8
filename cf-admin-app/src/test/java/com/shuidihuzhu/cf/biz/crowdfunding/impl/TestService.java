package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.admin.river.impl.RiverDiBaoFacadeImpl;
import com.shuidihuzhu.cf.biz.crowdfunding.workflow.WorkFlowAutoAssignRecordBiz;
import com.shuidihuzhu.cf.constants.AdminUserIDConstants;
import com.shuidihuzhu.cf.dao.crowdfunding.CfAiMaterialsDao;
import com.shuidihuzhu.cf.model.admin.CfMaterialsPic;
import com.shuidihuzhu.cf.model.crowdfunding.CfItemFieldVo;
import com.shuidihuzhu.cf.model.crowdfunding.CfItemTemplate;
import com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterials;
import com.shuidihuzhu.cf.model.crowdfunding.ai.LayOutField;
import com.shuidihuzhu.cf.model.river.RiverUsageTypeEnum;
import com.shuidihuzhu.cf.service.admin.CfMaterialsPicService;
import com.shuidihuzhu.cf.service.crowdfunding.CfTemplateFieldService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditCreateOrderService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.ai.AiRuleJudgeService;
import com.shuidihuzhu.cf.vo.approve.InitialAuditAdditionInfoVO;
import com.shuidihuzhu.cf.vo.approve.RiverDetailVO;
import com.shuidihuzhu.cf.vo.approve.RiverHandleParamVO;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackTaskFeignClient;
import com.shuidihuzhu.client.cf.growthtool.client.CfHospitalNormalFeignClient;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.model.hospital.CfHospitalNormal;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @DATE 2020/11/26
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@RefreshScope
public class TestService {

    static {
        System.setProperty("spring.cloud.consul.host","consul.bedin.shuiditech.com:80");
    }

    @Autowired
    private CfTemplateFieldService cfTemplateFieldService;

    @Autowired
    private CfAiMaterialsDao cfAiMaterialsDao;

    @Autowired
    private AiRuleJudgeService aiRuleJudgeService;

    @Autowired
    private CfClewtrackTaskFeignClient taskFeignClient;

    @Autowired
    private CfHospitalNormalFeignClient cfHospitalNormalFeignClient;

    @Autowired
    private WorkFlowAutoAssignRecordBiz workFlowAutoAssignRecordBiz;

    @Autowired
    private CfMaterialsPicService cfMaterialsPicService;
    @Autowired
    private InitialAuditCreateOrderService initialAuditCreateOrderService;

    @Test
    public void testField(){

//        CfItemField cfItemField = new CfItemField();
//        cfItemField.setField("测试1");
//        cfItemField.setFieldLength(100);
//        cfItemField.setFieldLimiter("12121");
//        cfItemField.setFieldType("fdfdfdfdf");
//        cfTemplateFieldService.addField(cfItemField,191);
//
//        cfTemplateFieldService.updateField(1,1,191);

        List<CfItemFieldVo> list = cfTemplateFieldService.listCfItemFields(0);

        log.info("list={}",list);
    }

    @Test
    public void testTemp(){

        CfItemTemplate c = new CfItemTemplate();
        c.setTemplateName("飞飞飞飞");
        c.setTemplateValue("f非法额法尔飞机覅而激发解放军");
        c.setParentId(1);
        c.setRepeatSend(2);
        c.setLevel(1);
        c.setFieldIds(Lists.newArrayList(2L,3L,4L,5L));
        cfTemplateFieldService.addTemplate(c,191);
    }

    @Test
    public void fefef(){

        List<CfAiMaterials> aiMaterials = Lists.newArrayList();

        CfAiMaterials c = new CfAiMaterials();
        c.setCaseId(2300320);
        c.setWorkOrderId(100);
        c.setWorkOrderType(1);
        c.setMaterialsType(11);
        List<LayOutField> layOutFields = Lists.newArrayList();
        LayOutField l = new LayOutField();
        l.setFieldKey("patientNameInMd");
        l.setFieldName("patientNameInMd");
        l.setFieldType("111");
        l.setFieldValue("gggggg");
        layOutFields.add(l);
        c.setFields(layOutFields);
        aiMaterials.add(c);
        cfAiMaterialsDao.insert(aiMaterials);
    }

    @Autowired
    private RiverDiBaoFacadeImpl riverDiBaoFacade;
    @Test
    public void jujij(){
        RiverHandleParamVO diBaoParamVO = RiverHandleParamVO.builder()
                .caseId(2878514)
                .usageTypeEnum(RiverUsageTypeEnum.DI_BAO)
                .handleComment("")
                .handleType(RiverHandleParamVO.HandleType.REJECT)
                .workOrderId(136590)
                .orderType(WorkOrderType.ai_erci.getType())
                .rejectIds(Arrays.asList(352))
                .operatorId(AdminUserIDConstants.SYSTEM)
                .callStatus(0)
                .rejectDetail(Maps.newHashMap())
                .build();
        Response<Void> handle = riverDiBaoFacade.handle(diBaoParamVO);
        System.out.println(handle);
        RiverDetailVO<InitialAuditAdditionInfoVO> detail = riverDiBaoFacade.getDetail(2878514, 136590);

        System.out.println(detail);
//        aiRuleJudgeService.Judge(136569,136568,2878514);
    }

    @Test
    public void fefe(){
        Response<Boolean> response = taskFeignClient.donateServiceDiversion(2864487,"special");
        log.info("response={}",response);
        boolean o = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(true);
        log.info("ooooo={}",o);

    }


    @Test
    public void fefaefaewfa(){
        Response<CfHospitalNormal> r = cfHospitalNormalFeignClient.getByCfHospitalCode("b903f868-4915-452a-8575-592a882a9dc5");
        log.info("ooooo={}", JSON.toJSONString(r));

    }

    @Test
    public void fefeiiiif(){
        workFlowAutoAssignRecordBiz.triggerAutoAssignReportOrderWithLock();
    }


    @Test
    public void efo2iuo(){

        List<CfMaterialsPic> list = cfMaterialsPicService.getByCaseId(2867029);
        System.out.println(JSON.toJSONString(list));

        list.stream().forEach(r->r.setPicType("入院记录"));

        cfMaterialsPicService.save(list,327);
    }
}
