package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.biz.call.impl.CallRecordBizImpl;
import com.shuidihuzhu.cf.call.CallInModel;
import com.shuidihuzhu.cf.call.CallOutModel;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class CallRecordBizTest {
    @Autowired
    private CallRecordBizImpl callRecordBiz;

    @Test
    public void getCallInModel() {
        List<CallInModel> callInModels = callRecordBiz.getCallInRecords("2019-01-13", "2019-01-15", "", 1, 1);
        Assert.assertNotNull(callInModels);
        Assert.assertEquals(callInModels.size(), 1);
    }

    @Test
    public void getCallOutModel() {
        List<CallOutModel> callOutModels = callRecordBiz.getCallOutRecords("2019-01-13", "2019-01-15", "", 1, 1);
        Assert.assertNotNull(callOutModels);
        Assert.assertEquals(callOutModels.size(), 1);
    }
}
