package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.biz.crowdfunding.CfCommitVerifyItemBiz;
import com.shuidihuzhu.cf.model.crowdfunding.CfCommitVerifyItem;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @DATE 2019/12/29
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class CfCommitVerifyItemBizImplTest {

    @Autowired
    private CfCommitVerifyItemBiz cfCommitVerifyItemBiz;

    @Test
    public void selectByIds() {
        Set<Integer> set = new HashSet<>();
        List<CfCommitVerifyItem> cfCommitVerifyItems = cfCommitVerifyItemBiz.selectByIds(set);

        assertTrue(cfCommitVerifyItems.isEmpty());
        set = IntStream.range(0, 10).boxed().collect(Collectors.toSet());
        cfCommitVerifyItems = cfCommitVerifyItemBiz.selectByIds(set);
        System.out.println("====" + cfCommitVerifyItems);
        assertFalse(cfCommitVerifyItems.isEmpty());
    }
}