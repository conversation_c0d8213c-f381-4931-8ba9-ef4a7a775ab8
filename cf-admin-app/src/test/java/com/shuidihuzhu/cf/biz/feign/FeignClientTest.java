package com.shuidihuzhu.cf.biz.feign;


import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.biz.crowdfunding.CfMaterialVerityHistoryBiz;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceCapitalAccountFeignClient;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceFundStateFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.model.CfFundState;
import com.shuidihuzhu.cf.model.crowdfunding.CfCapitalAccount;
import com.shuidihuzhu.cf.risk.client.aegis.EngineAnalysisClient;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditCreateOrder;
import com.shuidihuzhu.cf.vo.crowdfunding.InitialAuditSmartRejectVo;
import com.shuidihuzhu.client.cf.admin.client.CfAdminUgcServiceFeignClient;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @package: com.shuidihuzhu.feign
 * @Author: liujiawei
 * @Date: 2019-07-04  14:43
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class FeignClientTest {

    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }

    @Autowired
    private CfFinanceCapitalAccountFeignClient cfFinanceCapitalAccountFeignClient;
    @Autowired
    private CfFinanceFundStateFeignClient cfFinanceFundStateFeignClient;

    @Autowired
    private CfAdminUgcServiceFeignClient cfAdminUgcServiceFeignClient;
    @Autowired
    private EngineAnalysisClient engineAnalysisClient;
    @Autowired
    private IRiskDelegate riskDelegate;
    @Autowired
    private SeaAccountClientV1 accountClientV1;
    @Autowired
    private CfMaterialVerityHistoryBiz verityHistoryBiz;
    @Autowired
    private InitialAuditCreateOrder initialAuditCreateOrder;

    @Test
    public void testCapitalAccount() {
        String infoUuid = "138d7e6e-a643-411f-94d6-313fdb3a8692";
        FeignResponse<CfCapitalAccount> cfCapitalAccountFeignResponse = cfFinanceCapitalAccountFeignClient.capitalAccountGetByInfoUuid(infoUuid);
        System.out.println("-----------------------------------------------------");
        System.out.println(cfCapitalAccountFeignResponse.getData());


        String infoUuIdTest = "ppppppppppppppppqqqqqqqqqqqqqq";
        FeignResponse<Integer> integerFeignResponse = cfFinanceCapitalAccountFeignClient.saveCapitalAccount(infoUuIdTest);
        System.out.println("-----------------------------------------------------");
        System.out.println(integerFeignResponse.getData());

        FeignResponse<Map<String, CfCapitalAccount>> mapFeignResponse = cfFinanceCapitalAccountFeignClient.capitalAccountGetMapByInfoUuids(Lists.newArrayList(infoUuid, infoUuIdTest));
        System.out.println("-----------------------------------------------------");
        System.out.println(mapFeignResponse.getData());
    }

    @Test
    public void testFundState() {
        int infoId = 983416;
        FeignResponse<List<CfFundState>>  response = cfFinanceFundStateFeignClient.getListByInfoids(Lists.newArrayList(infoId));
        System.out.println("-----------------------------------------------------");
        System.out.println(response.getData());

        FeignResponse<CfFundState>  response1 = cfFinanceFundStateFeignClient.getByInfoid(infoId);
        System.out.println("-----------------------------------------------------");
        System.out.println(response1.getData());

        FeignResponse<List<CfFundState>> response2 = cfFinanceFundStateFeignClient.getByUpdateTime(new Date().getTime(), 0);
        System.out.println("-----------------------------------------------------");
        System.out.println(response2.getData());

    }

    @Test
    public void test_selectCommentCountByMin(){

        LocalDateTime startTime = LocalDateTime.now();
        LocalDateTime endTime = startTime.minusMonths(1);
        Timestamp begin = Timestamp.from(startTime.toInstant(ZoneOffset.ofHours(8)));
        Timestamp end = Timestamp.from(endTime.toInstant(ZoneOffset.ofHours(8)));
        Response<Integer> response = cfAdminUgcServiceFeignClient.selectCommentCountByMin(begin, end);
        System.out.println(response);
    }
    @Test
    public void testDrawCash() {
        InitialAuditSmartRejectVo initialAuditSmartRejectVo = initialAuditCreateOrder.judgeInitialReviewInformation(2880003);
        System.out.println(initialAuditSmartRejectVo);
    }

    @Test
    public void testWrite() {

    }

    @Test
    public void testRefund() {

    }
}
