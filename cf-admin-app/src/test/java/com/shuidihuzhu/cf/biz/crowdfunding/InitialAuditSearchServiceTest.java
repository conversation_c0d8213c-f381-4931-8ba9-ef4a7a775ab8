package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditCaseDetail;
import com.shuidihuzhu.cf.vo.approve.CreditInfoVO;
import com.shuidihuzhu.client.cf.growthtool.client.ClewPreproseMaterialFeignClient;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * Description:
 *
 * <AUTHOR>
 * @Create 2025/7/7 16:38
 * @Version 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class InitialAuditSearchServiceTest {

    @Resource
    private ClewPreproseMaterialFeignClient clewPreproseMaterialFeignClient;

    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }

    /**
     * 测试设置前置报备信息 com/shuidihuzhu/cf/service/workorder/initialAudit/InitialAuditSearchService.java:591
     */
    @Test
    public void test1() {
        InitialAuditCaseDetail caseDetail = new InitialAuditCaseDetail();
        Response<PreposeMaterialModel.MaterialInfoVo> materialInfoVoResponse = clewPreproseMaterialFeignClient.selectMaterialByCaseIdForQC(3174023);
        PreposeMaterialModel.MaterialInfoVo data = materialInfoVoResponse.getData();
        CreditInfoVO build = CreditInfoVO.build(data);
        log.info("build = " + build);
    }
}