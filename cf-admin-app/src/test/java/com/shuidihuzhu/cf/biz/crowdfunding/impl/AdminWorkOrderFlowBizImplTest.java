package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderFlowBiz;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderFlowRemindRecord;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowParam;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.*;

/**
 * @author: fengxuan
 * @create 2019-11-28 10:46
 **/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class AdminWorkOrderFlowBizImplTest {

    static {
        System.setProperty("spring.cloud.consul.host","consul.zelda.shuiditech.com:80");
    }

    @Autowired
    AdminWorkOrderFlowBiz adminWorkOrderFlowBiz;

    @Test
    public void getWaitForHandleListTest() {
        AdminWorkOrderFlowParam.SearchParam searchParam = new AdminWorkOrderFlowParam.SearchParam();
        Pair<PageInfo<AdminWorkOrderFlowView>, Integer> pair = adminWorkOrderFlowBiz.selectWaitForHandleFlowByParam(searchParam);
        log.info("totalNum:{}", pair.getRight());
        pair.getLeft().getList().forEach(JSON::toJSONString);
    }

    @Test
    public void addRemind() {
        AdminWorkOrderFlowRemindRecord remindRecord = new AdminWorkOrderFlowRemindRecord();
        boolean result = adminWorkOrderFlowBiz.addRemind(remindRecord, 352924435);
        log.info("result:{}", result);
    }

    @Test
    public void getAllDetail() {

    }

}