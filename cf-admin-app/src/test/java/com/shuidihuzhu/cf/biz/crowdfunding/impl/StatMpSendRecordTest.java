package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.wx.grpc.client.WxArticleRecordGrpcClient;
import com.shuidihuzhu.wx.grpc.model.StatMpSendRecord;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.List;

/**
 * Created by apple on 2017/11/30.
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class StatMpSendRecordTest {
    @Autowired
    private WxArticleRecordGrpcClient wxArticleRecordGrpcClient;

    @Test
    public void testSaveRecord() {
        StatMpSendRecord statMpSendRecord = new StatMpSendRecord();
        String mediaId = "test_mediaId";
        statMpSendRecord.setMediaId(mediaId);
        statMpSendRecord.setThirdType(33);
        String msgId = "1000001625";
        statMpSendRecord.setMsgId(msgId);
        String msgDataId = "test_msgDataId";
        statMpSendRecord.setMsgDataId(msgDataId);
        String errorMsg = "test_errorMsg";
        statMpSendRecord.setErrorMsg(errorMsg);
        wxArticleRecordGrpcClient.insertSendRecord(statMpSendRecord);
    }

    @Test
    public void testUpdateRecord() {
        StatMpSendRecord statMpSendRecord = new StatMpSendRecord();
        statMpSendRecord.setThirdType(33);
        String msgId = "1000001625";
        statMpSendRecord.setMsgId(msgId);
        statMpSendRecord.setTotalCount(100);
        statMpSendRecord.setFilterCount(10);
        statMpSendRecord.setSentCount(89);
        statMpSendRecord.setErrorCount(1);
        statMpSendRecord.setStatus("");
        statMpSendRecord.setMsgCreateTime(new Date());
        wxArticleRecordGrpcClient.updateSendRecord(statMpSendRecord);
    }

    @Test
    public void testQueryRecord() {
        List<StatMpSendRecord> list = wxArticleRecordGrpcClient.querySendRecord("1000001625", 33, 0, 10);
        log.info(list + "");
    }

    @Test
    public void testQueryRecordLast() {
        StatMpSendRecord list = wxArticleRecordGrpcClient.querySendRecordLast(
                "aZyGoUHWisYr41q-pahRv_ScoswLUlQuXiCy-We6buQ", 33);
        log.info(list + "");
    }
}