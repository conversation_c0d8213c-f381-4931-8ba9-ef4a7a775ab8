package com.shuidihuzhu.cf.biz.crowdfunding.report;

import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.admin.controller.api.crowdfunding.report.AdminCfInfoLostContactController;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.common.util.JsonUtil;
import com.shuidihuzhu.common.web.model.Response;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2018-07-27  14:33
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
public class AdminCfInfoLostContactServiceTest {

    @Resource
    private AdminCfInfoLostContactService adminCfInfoLostContactService;
    private static final String INFO_UUID = "d289bc9f-0b26-4799-a106-4c4bc419319d";

    @Test
    public void test(){
        boolean hasLost = adminCfInfoLostContactService.hasLost(INFO_UUID);
        System.out.println("hasLost = " + hasLost);
    }

    @Test
    public void testUpdate(){
        OpResult opResult = adminCfInfoLostContactService.update(103, INFO_UUID, true, "哈哈哈");
        System.out.println("opResult = " + opResult);
    }

}
