package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingCityBiz;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class CrowdfundingCityTest {

    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }

    @Autowired
    private AdminCrowdfundingCityBiz adminCrowdfundingCityBiz;

    @Test
    public void getProvinceTest() {
        List<CrowdfundingCity> province = adminCrowdfundingCityBiz.getProvince();//
        log.info("province: {}", province);
        CrowdfundingCity byId = adminCrowdfundingCityBiz.getById(1);
        log.info("byId: {}", byId);
        List<CrowdfundingCity> listByCode = adminCrowdfundingCityBiz.getListByCode(Lists.newArrayList(110000, 120000));
        log.info("listByCode: {}", listByCode);
        List<CrowdfundingCity> byIds = adminCrowdfundingCityBiz.getByIds(Lists.newArrayList(1, 2, 3));
        log.info("byIds: {}", byIds);
        CrowdfundingCity getByName = adminCrowdfundingCityBiz.getCityByName("北京市");//
        log.info("getByName: {}", getByName);
        List<CrowdfundingCity> countiesByName = adminCrowdfundingCityBiz.getCountiesByName("贺兰");//
        log.info("countiesByName: {}", countiesByName);
        List<CrowdfundingCity> children = adminCrowdfundingCityBiz.getChildren(1);
        log.info("children: {}", children);
        List<CrowdfundingCity> byRealParentId = adminCrowdfundingCityBiz.getByRealParentId(1);
        log.info("byRealParentId: {}", byRealParentId);
    }
}
