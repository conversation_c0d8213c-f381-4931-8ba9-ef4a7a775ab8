package com.shuidihuzhu.cf.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.biz.admin.AdminOperationRecordBiz;
import com.shuidihuzhu.cf.biz.aiphoto.PhotoAiService;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfPhotoAiBiz;
import com.shuidihuzhu.cf.model.admin.AdminLoginRecord;
import com.shuidihuzhu.cf.model.admin.PhotoStatus;
import com.shuidihuzhu.cf.model.aiphoto.PhotoAiInfoModel;
import com.shuidihuzhu.cf.model.crowdfunding.CfAIPhotoModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor;
import com.shuidihuzhu.cf.model.river.RiverUsageTypeEnum;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditOperateService;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditOperationItem;
import com.shuidihuzhu.cf.vo.approve.RiverHandleParamVO;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Author: liujiawei
 * @Date: 2018/7/21  22:58
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class AdminCrowdfundingInfoBizImplTest {
    @Autowired
    CfPhotoAiBiz cfPhotoAiBiz;

    @Autowired
    PhotoAiService photoAiService;
    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;
    @Autowired
    private AdminOperationRecordBiz operationRecordBiz;
    @Autowired
    private InitialAuditOperateService initialAuditOperateService;

    static {
        System.setProperty("spring.cloud.consul.host","consul.bedin.shuiditech.com:80");
    }

    @Test
    public void TestPhotoAiService() {

        PhotoStatus photoStatus = cfPhotoAiBiz.selectPhotoStatus(88888);
        CrowdfundingAuthor crowdfundingAuthor = new CrowdfundingAuthor();
        crowdfundingAuthor.setIdCard("220105200005070679");
        boolean s = idCardCheckJudge(photoStatus, crowdfundingAuthor);

        CfAIPhotoModel cfAIPhotoModel = new CfAIPhotoModel();
        cfAIPhotoModel.setParentId(88888);
        cfAIPhotoModel.setIdCardPhoto("http://cf-images.oss-cn-shanghai.aliyuncs.com/facetest/people/1-人.jpg");
        cfAIPhotoModel.setOnlyIdCardPhoto("http://cf-images.oss-cn-shanghai.aliyuncs.com/facetest/people/1-证件.jpg");
        PhotoAiInfoModel photoAiInfoModel = photoAiService.getPhotoAiInfo(cfAIPhotoModel);
        if (photoAiInfoModel != null) {
            photoAiService.savePhotoAiInfo(photoAiInfoModel);
            System.out.println(photoAiInfoModel);
        } else {
            System.out.println("photo not exist");
        }
    }

    @Resource
    private ShuidiCipher shuidiCipher;
    private boolean idCardCheckJudge(PhotoStatus photoStatus, CrowdfundingAuthor crowdfundingAuthor) {

        if (StringUtils.isBlank(photoStatus.getIdCardNumber())) {
            return StringUtils.isNotBlank(photoStatus.getIdNumber()) && photoStatus.getIdNumber().equals(crowdfundingAuthor.getIdCard());
        }

        if (StringUtils.isNotBlank(photoStatus.getIdCardNumber()) && !"无法识别".equals(photoStatus.getIdCardNumber())) {
            String realNumber = shuidiCipher.decrypt(photoStatus.getIdCardNumber());
            return StringUtils.isNotBlank(realNumber) && realNumber.equals(crowdfundingAuthor.getIdCard());
        }

        return false;
    }

    @Test
    public void TestGetPhotoAiStatus(){
        boolean AiPhotoExist  = cfPhotoAiBiz.checkAIPhotoExist(88888);
        if (AiPhotoExist){
            PhotoStatus photoStatus = cfPhotoAiBiz.selectPhotoStatus(88888);
            System.out.println("当前照片状态：");
            System.out.println(photoStatus);
        }else {
            System.out.println("当前照片未经过AI识别");
        }
    }
    @Test
    public void testCheckAIPhotoStatus(){
        cfPhotoAiBiz.updateArtificialRes(88888, 1);
        PhotoStatus photoStatus = cfPhotoAiBiz.selectPhotoStatus(88888);
        System.out.println(photoStatus);
    }

    @Test
    public void testFixAiPhoto(){

        CfAIPhotoModel cfAIPhotoModel = new CfAIPhotoModel();
        cfAIPhotoModel.setIdCardPhoto("https://oss.shuidichou.com/img/ck/20190305/1551788264682474Dn7ibHP1551788264682.jpg");
        cfAIPhotoModel.setOnlyIdCardPhoto("https://oss.shuidichou.com/img/ck/20190305/1551788205862K6R6MZ5Qf31551788205862.jpg");
        PhotoAiInfoModel photoAiInfoModel = photoAiService.getPhotoAiInfo(cfAIPhotoModel);
        System.out.println(photoAiInfoModel);
    }

    @Test
    public void testSelectOperate() {
        List<AdminLoginRecord> record1 =  operationRecordBiz.selectByDateAndOrg(12134, null);
        List<AdminLoginRecord> record2 =  operationRecordBiz.selectByDateAndOrg(20190708, null);
        List<AdminLoginRecord> record3 =  operationRecordBiz.selectByDateAndOrg(20190708, Lists.newArrayList(
                "水滴筹", "水滴筹开发", "后端"
        ));
        log.info("{} {} {}", record1, record2, record3);
    }

    @Test
    public void testSelectOperate1() {
        RiverHandleParamVO pinKunParamVO = RiverHandleParamVO.builder()
                .usageTypeEnum(RiverUsageTypeEnum.PIN_KUN)
                .handleType(RiverHandleParamVO.HandleType.STOP_CASE)
                .rejectIds(Lists.newArrayList())
                .build();

        RiverHandleParamVO diBaoParamVO = RiverHandleParamVO.builder()
                .usageTypeEnum(RiverUsageTypeEnum.DI_BAO)
                .handleType(RiverHandleParamVO.HandleType.STOP_CASE)
                .rejectIds(Lists.newArrayList())
                .build();
        InitialAuditOperationItem.HandleCaseInfoParam handleCaseInfoParam = InitialAuditOperationItem
                .HandleCaseInfoParam
                .builder()
                .caseId(2881076)
                .handleType(InitialAuditOperationItem.HandleTypeEnum.END_CASE.getCode())
                .orderType(WorkOrderType.ai_content.getType())
                .workOrderId(142446)
                .pinKunHandleParam(pinKunParamVO)
                .pinKunComment("")
                .diBaoHandleParam(diBaoParamVO)
                .diBaoComment("")
                .passIds(new ArrayList<>())
                .rejectIds(Collections.singletonList(215))
                .callStatus(0)
                .userCallStatus(0)
                .callComment("")
                .userId(81599)
                .handleComment("")
                .build();
        initialAuditOperateService.handleWorkOrder(handleCaseInfoParam);
    }
}
