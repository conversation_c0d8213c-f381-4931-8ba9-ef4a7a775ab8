package com.shuidihuzhu.cf.biz.crowdfunding.wp;

import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.alps.feign.ocean.OceanApiMQResponse;
import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.admin.mq.ImageAIRecognitionConsumer;
import com.shuidihuzhu.cf.biz.amount.AmountReasonableBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingAttachmentBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfClewChannelInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.image.AdminImageHandlerBiz;
import com.shuidihuzhu.cf.client.adminpure.enums.AmountReasonableTaskType;
import com.shuidihuzhu.cf.client.adminpure.model.amount.CfAmountReasonableTask;
import com.shuidihuzhu.cf.delegate.ai.AiAIContentWorkOrderResultDelegate;
import com.shuidihuzhu.cf.enums.InitialAudit.TargetAmountAuditWorkOrderScene;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.model.ai.AiImageMarkResponseBody;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.amount.AmountReasonableService;
import com.shuidihuzhu.cf.service.label.risk.RiskLabelService;
import com.shuidihuzhu.cf.service.workorder.cailiao.CaiAuditRejectAICallService;
import com.shuidihuzhu.cf.service.workorder.imagePublic.ImagePublicWorkOrderService;
import com.shuidihuzhu.cf.service.workorder.imagePublic.impl.ImagePublicWorkOrderServiceImpl;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditCreateOrderService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditTargetAmountReasonableService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.ai.impl.AiMixDiseaseCondition;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.model.event.InfoApproveEvent;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.A;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2021/1/27 15:45
 * @Description:
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class AdminCfWangPengBizTest {
    static {
        System.setProperty("spring.cloud.consul.host","consul.bedin.shuiditech.com:80");
    }

    @Resource
    private AdminImageHandlerBiz adminImageHandlerBiz;
    @Resource
    private ImagePublicWorkOrderService imagePublicWorkOrderService;
    @Resource
    private CfClewChannelInfoBiz cfClewChannelInfoBiz;
    @Resource
    private InitialAuditCreateOrderService initialAuditCreateOrderService;
    @Resource
    private CaiAuditRejectAICallService caiAuditRejectAICallService;
    @Resource
    private AmountReasonableService amountReasonableService;
    @Resource
    private AmountReasonableBiz amountReasonableBiz;
    @Resource
    private AiAIContentWorkOrderResultDelegate aiAIContentWorkOrderResultDelegate;
    @Resource
    private ImageAIRecognitionConsumer imageAIRecognitionConsumer;
    @Resource
    private AdminCrowdfundingAttachmentBiz adminCrowdfundingAttachmentBiz;
    @Resource
    private InitialAuditTargetAmountReasonableService initialAuditTargetAmountReasonableService;
    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Test
    public void test1() {
//        aiAIContentWorkOrderResultDelegate.aiContentWorkResult(1, WorkOrderType.ai_content.getType(), 1);
//        amountReasonableService.firstIssueTask(2979153, AmountReasonableTaskType.ACCIDENT_CASE.getCode());
//
//        amountReasonableService.getCommonRuleResult(2979153, AmountReasonableTaskType.ACCIDENT_CASE.getCode());

//        for (int i = 1; i<=5; i++) {
//            CfAmountReasonableTask taskById = amountReasonableBiz.getTaskById(i);
//            taskById.setContent("12"+i);
//            amountReasonableService.submitTask(taskById);
//        }

    }
    @Test
    public void test() {
        CrowdfundingInfo fundingInfoById = adminCrowdfundingInfoBiz.getFundingInfoById(3000701);

//        List<CrowdfundingAttachment> attachmentList = adminCrowdfundingAttachmentBiz.queryAttachment(2995535);
//        ConsumerMessage consumerMessage = new ConsumerMessage();
//        consumerMessage.setPayload(attachmentList);
//        imageAIRecognitionConsumer.consumeMessage(consumerMessage);
//        aiAIContentWorkOrderResultDelegate.aiContentWorkResult(2993693, WorkOrderType.ai_content.getType(), 1);

//        InfoApproveEvent infoApproveEvent = new InfoApproveEvent();
//        infoApproveEvent.setRefuseIds(Arrays.asList(471, 403));
//        infoApproveEvent.setCaseId(2923811);
//        infoApproveEvent.setWorkOrderId(2);
//        caiAuditRejectAICallService.rejectAICallFirst(infoApproveEvent);
////        initialAuditCreateOrderService.snapShotDiseaseName(2918920, 2323);
////        cfClewChannelInfoBiz.sendLiaoNingMission(2917395);
////        InfoApproveEvent infoApproveEvent = new InfoApproveEvent();
////        infoApproveEvent.setCaseId(2902167);
////        infoApproveEvent.setOrderType(WorkOrderType.cailiao_4.getType());
////        infoApproveEvent.setStatus(CrowdfundingStatus.CROWDFUNDING_STATED.value());
////        imagePublicWorkOrderService.createWorkOrder(infoApproveEvent);
//        OceanApiMQResponse oceanApiMQResponse = new OceanApiMQResponse();
//        AiImageMarkResponseBody aiImageMarkResponseBody = new AiImageMarkResponseBody();
//        aiImageMarkResponseBody.setImageId("8");
//        aiImageMarkResponseBody.setUrlMosaic("http");
//        aiImageMarkResponseBody.setStatus(2);
//        List<AiImageMarkResponseBody> list = new ArrayList<>();
//        list.add(aiImageMarkResponseBody);
//        String s = JSONObject.toJSONString(list);
//        oceanApiMQResponse.setBody(s);
//        imagePublicWorkOrderService.submitAiMarkImage(oceanApiMQResponse);

////        String imageHandlerWatermark = adminImageHandlerBiz.imageHandlerWatermark("https://images.shuidichou.com/-K3hURMln4RtlgiskH32bqAPsebzIwAHi1vTo0jIFMn_hasP_m_PY3x-Drxyf3UFt7gXAXYSfAV3nOBdgJu2pFsnxDozCCohUArgSe36VXhLM9Er5Rh5rFlKPIV7bDP88RCDzasR0Nf-ma3ffC1Jzm3JYoQZT5jdR9o8m1B5qhs1e46s9UgdrZhHl2AUuzPj4QjRZk2pD9TLZPnkqfPTgHG8pt3pBUrbx3jYhzcYDMLS-tSzykiudKySMT34RADFiEkF_Q4fuUDCnES_3c8PH33bNs8dsIzxc9Ne55epDhXPz9EsHl1W4lhRvXdKeP8VrGpH9ExufOKkJLCNztl8K2Ra6IvcNNbzRgO4XNzGkmSIEs3jg7ma62wwixtY-Y-Gu3tIy2wdt_s1lyPIfxJSxZzHVh2vGVaA87WbvLcw2v28yFVbegcjHL4cRpzlEJzf?waterMark=true");
////
////        System.out.println(imageHandlerWatermark);
    }
}
