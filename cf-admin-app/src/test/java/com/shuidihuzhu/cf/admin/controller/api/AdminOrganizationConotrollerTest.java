package com.shuidihuzhu.cf.admin.controller.api;

import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.biz.admin.AdminOrganizationBiz;
import com.shuidihuzhu.cf.model.admin.AdminOrganization;
import com.shuidihuzhu.cf.model.admin.AdminOrganizationUserMap;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.UUID;

/**
 * @Author: WangYing on 2018/9/17
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class AdminOrganizationConotrollerTest {

    @Autowired
    private AdminOrganizationBiz adminOrganizationBiz;

    @Test
    public void testAddOrganization() {
        String orgName = "测试新增组织" + UUID.randomUUID();
        int id = adminOrganizationBiz.addOrganizationNode(orgName, 0);
        AdminOrganization adminOrganization = adminOrganizationBiz.getAdminOrganizationById(id);
        Assert.assertEquals(orgName, adminOrganization.getName());
    }

    @Test
    public void testUpdateOrganization() {
        if (null != adminOrganizationBiz.getAdminOrganizationById(1)) {
            String orgName = "测试编辑组织" + UUID.randomUUID();
            adminOrganizationBiz.editOrganizationNode(1, orgName);
            AdminOrganization adminOrganization = adminOrganizationBiz.getAdminOrganizationById(1);
            Assert.assertEquals(orgName, adminOrganization.getName());
        }
    }

    @Test
    public void testDeleteOrganization() {
        if (null != adminOrganizationBiz.getAdminOrganizationById(1)) {
            adminOrganizationBiz.deleteOrganizationNode(1);
            Assert.assertNull(adminOrganizationBiz.getAdminOrganizationById(1));
        }
    }

    @Test
    public void testAddEmployeeOrganization() {
        adminOrganizationBiz.addEmployeeToNode(179, 1);
        AdminOrganizationUserMap organizationUserMap = adminOrganizationBiz.getAdminOrganizationUserMap(179, 1);
        Assert.assertNotNull(organizationUserMap);
    }

    @Test
    public void testDeleteEmployeeOrganization() {
        adminOrganizationBiz.deleteEmployeeFromNode(179, 1);
        Assert.assertNull(adminOrganizationBiz.getAdminOrganizationUserMap(179, 1));
    }

}
