package com.shuidihuzhu.cf.admin.delegate;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.biz.admin.AdminWorkOrderBiz;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminSensitiveVo;
import com.shuidihuzhu.cf.service.search.EsSearchService;
import com.shuidihuzhu.client.dataservice.bi.v1.BiApiClient;
import com.shuidihuzhu.client.model.Response;
import com.shuidihuzhu.client.model.SearchDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-05-07  17:22
 * @description
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class EsSearchDelegateTest {

    static {
        System.setProperty("spring.cloud.consul.host", "consul.zelda.shuiditech.com:80");
    }

    @Resource
    private BiApiClient biApiClient;

    @Resource
    private EsSearchService esSearchService;

    @Resource
    private AdminWorkOrderBiz adminWorkOrderBiz;

    private static final String SQL_A = "select awo.id from shuidi_crowdfunding_admin_work_order awo left join shuidi_crowdfunding_admin_task_ugc atu on awo.id = atu.work_order_id where awo.order_type = 1 and awo.order_task in (1,2) and awo.operator_id = 103 and atu.content_type in (2,4) and atu.result = 5 and awo.create_time between '2018-01-16T15:35:27.590' and '2019-01-16T15:35:27.590' and atu.hit_words like '%测试%' and awo.is_delete = 0 order by awo.id desc limit 10";

    @Test
    public void testA() {
        SearchDto searchDto = new SearchDto();
        searchDto.setQuerySql(SQL_A);
        Response response = biApiClient.esQueryCustom(searchDto);
        Object data = response.getData();
        System.out.println("JSON.toJSONString(response) = " + JSON.toJSONString(response));
    }

    @Test
    public void testB() {
        List<Integer> integers = esSearchService.searchUgcWordOrder(
                1,
                Lists.newArrayList(1, 2),
                103,
                Lists.newArrayList(2, 4),
                null,
                null,
                null,
                "测试",
                1,
                10, false);
        System.out.println("integers = " + integers);
    }

    @Test
    public void testC(){
        List<AdminSensitiveVo> adminSensitiveVos = adminWorkOrderBiz.searchUgcSensitiveByPage(
                1,
                10,
                false, 103,
                null,
                4,
                0,
                "测试",
                null,
                null
        );
        System.out.println("adminSensitiveVos = " + adminSensitiveVos);

    }



}
