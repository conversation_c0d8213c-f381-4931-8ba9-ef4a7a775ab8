package com.shuidihuzhu.cf.admin.delegate;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.sona.RoleEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoBlessing;
import com.shuidihuzhu.cf.model.crowdfunding.CfTransformArticleDo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.growthtool.client.CfGrowthtoolVolunteerFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-05-07  17:22
 * @description
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class SeaUserAuthDelegateTest {

    static {
        System.setProperty("spring.cloud.consul.host","consul.zelda.shuiditech.com:80");
    }

    @Resource
    private SeaUserAuthDelegate seaUserAuthDelegate;
    @Autowired
    private CfGrowthtoolVolunteerFeignClient cfGrowthtoolVolunteerFeignClient;


    @Test
    public void testB(){
//        OpResult opResult = seaUserAuthDelegate.deleteRole(103, RoleEnum.FIRST_APPROVE_A.getValue());
//        System.out.println("opResult = " + opResult);
        Response<String> crowdfundingVolunteerByMobile = cfGrowthtoolVolunteerFeignClient.getVolunteerRelationByMobile("uYtb+xyZWCJLeuGc3wCisA==");
        log.error(this.getClass().getSimpleName()+"  getCrowdfundingVolunteerByMobile result:{}", JSON.toJSONString(crowdfundingVolunteerByMobile));
    }
}
