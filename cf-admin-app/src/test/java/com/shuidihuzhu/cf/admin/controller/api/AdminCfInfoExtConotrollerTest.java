package com.shuidihuzhu.cf.admin.controller.api;

import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.biz.admin.AdminOrganizationBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.model.admin.AdminOrganization;
import com.shuidihuzhu.cf.model.admin.AdminOrganizationUserMap;
import com.shuidihuzhu.cf.response.OpResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.Map;
import java.util.UUID;

/**
 * @Author: WangYing on 2018/9/17
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class AdminCfInfoExtConotrollerTest {

    @Autowired
    private AdminCfInfoExtBiz cfInfoExtBiz;

    @Resource
    private ApplicationContext applicationContext;

    static {
        System.setProperty("spring.cloud.consul.host","consul.bedin.shuiditech.com:80");
    }

    @Test
    public void testUpdateTransferFinishStatus() {
        OpResult opResult = cfInfoExtBiz.updateTransferStatusAndFinishStatus("3f3cf681-7b32-41e7-a2d0-4cfe602985c");
        if (opResult.isSuccess()){
            System.out.println("update succ");
        }else {
            System.out.println("update error");
        }

        Map<String, DataSource> beansOfType = applicationContext.getBeansOfType(DataSource.class);

        System.out.println("end");
    }

}
