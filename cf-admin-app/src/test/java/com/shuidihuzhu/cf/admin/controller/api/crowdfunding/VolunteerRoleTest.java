package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAuthClientV1;
import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.enums.volunteer.RoleVolunteerEnum;
import com.shuidihuzhu.cf.model.volunteer.VolunteerTypeModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-05-29
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class VolunteerRoleTest {

    @Autowired
    private SeaAuthClientV1 seaAuthClientV1;

    /**
     * 权限控制列表
     */
    public static final List<String> VOLUNTEERPERMISSION = Lists.newArrayList(RoleVolunteerEnum.TEAM.getPermission(),
            RoleVolunteerEnum.SERVE_ADMIN.getPermission(),RoleVolunteerEnum.PART_TIME.getPermission(),
            RoleVolunteerEnum.WATER_DROP_VOLUNTEER.getPermission(),
            RoleVolunteerEnum.SERIOUS_ILLNESS_SALVAGE_ANGEL.getPermission());

    @Test
    public void getSeaRolePermission(){
        AuthRpcResponse<Set<String>> rpcResponse = seaAuthClientV1.validUserPermissions(14234,VOLUNTEERPERMISSION);
        Set<String> permissions = null;
        if (rpcResponse!=null && rpcResponse.getCode()==0){
            permissions = rpcResponse.getResult();
        }
        if (CollectionUtils.isEmpty(permissions)){
            return ;
        }
        List<Integer> values = permissions.stream().map(s -> RoleVolunteerEnum.parse(s).getValue()).collect(Collectors.toList());
        List<VolunteerTypeModel> volunteerTypeModelList = Lists.newArrayList();
        for (Integer value: values) {
            VolunteerTypeModel volunteerTypeModel = new VolunteerTypeModel();
            volunteerTypeModel.setVolunteerType(value);
            volunteerTypeModel.setVolunteerDesc(CrowdfundingVolunteerEnum.volunteerType.parse(value).getDesc());
            volunteerTypeModelList.add(volunteerTypeModel);
        }
        volunteerTypeModelList.stream().forEach(l-> System.err.println(l));
    }
}
