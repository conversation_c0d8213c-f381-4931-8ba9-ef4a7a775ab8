//package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;
//
//import com.google.common.collect.Maps;
//import com.shuidihuzhu.account.compatible.aiphoto.UserAccount;
//import com.shuidihuzhu.account.service.impl.util.AccountServiceUtil;
//import com.shuidihuzhu.cf.biz.admin.CfCallTrackingCluesBiz;
//import com.shuidihuzhu.cf.biz.admin.CfCallTrackingOperatorInfoBiz;
//import com.shuidihuzhu.cf.biz.admin.privilege.AdminAuthRoleUserMapBiz;
//import com.shuidihuzhu.cf.aiphoto.admin.CfCallTrackingClues;
//import com.shuidihuzhu.cf.aiphoto.admin.CfCallTrackingOperatorInfo;
//import com.shuidihuzhu.cf.admin.auth.aiphoto.RoleUserMapModel;
//import com.shuidihuzhu.common.web.util.DateUtil;
//import com.shuidihuzhu.cf.util.ListUtil;
//import com.shuidihuzhu.common.web.util.admin.PageUtil;
//import org.apache.commons.lang3.StringUtils;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.test.context.ContextConfiguration;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//
//import java.sql.Timestamp;
//import java.util.List;
//import java.util.Map;
//import java.util.Set;
//import java.util.function.Function;
//import java.util.stream.Collectors;
//
///**
// * Created by Ahrievil on 2017/10/19
// */
//@RunWith(SpringJUnit4ClassRunner.class)
//@ContextConfiguration(locations = {"classpath:ctx-app.xml",
//        "classpath:ctx-biz.xml", "classpath:springmvc-servlet.xml"})
//public class CfCallTrackingCluesControllerTest {
//
//    private static final Logger LOGGER = LoggerFactory.getLogger(CfCallTrackingCluesControllerTest.class);
//    @Autowired
//    CfCallTrackingCluesBiz cfCallTrackingCluesBiz;
//    @Autowired
//    private AccountServiceUtil accountServiceUtil;
//    @Autowired
//    private AdminAuthRoleUserMapBiz roleUserMapBiz;
//    @Autowired
//    private CfCallTrackingOperatorInfoBiz cfCallTrackingOperatorInfoBiz;
//
//    @Test
//    public void setMirrorData() throws Exception {
//        Timestamp time = new Timestamp(DateUtil.getCurrentDate().getTime());
//        List<CfCallTrackingClues> cfCallTrackingClues = cfCallTrackingCluesBiz
//                .approveSearch(DateUtil.addDays(time, -3), "", "", "", 1, 10, 1, "", null);
//        cfCallTrackingClues.forEach(System.out::println);//*************,"","","",null,"",null
//        boolean b = cfCallTrackingClues.stream().anyMatch(val -> StringUtils.isBlank(val.getMobile()));
//        System.out.println(b);
//        Map<String, Integer> collect = cfCallTrackingClues.stream().collect(Collectors.toMap(CfCallTrackingClues::getMobile, val -> {
//            String mobile = val.getMobile();
//            Integer userIdx = 0;
//            if (StringUtils.isNotBlank(mobile)) {
//                UserAccount byMobile = accountServiceUtil.getByMobile(mobile);
//                if (byMobile != null) {
//                    userIdx = byMobile.getUserId();
//                }
//            }
//            return userIdx;
//        }));
//        collect.forEach((key, value) -> System.out.println(key + " " + value));
//    }
//
//    @Test
//    public void testUserAccount() {
//        List<RoleUserMapModel> userAccountsByRoleId = roleUserMapBiz.getUserAccountsByRoleId(30, 1, 10);
//        List<Integer> userIds = userAccountsByRoleId.stream().map(RoleUserMapModel::getUserId).collect(Collectors.toList());
//        List<CfCallTrackingOperatorInfo> cfCallTrackingOperatorInfos = ListUtil.getList(3000, cfCallTrackingOperatorInfoBiz::selectAll);
//        Map<Integer, CfCallTrackingOperatorInfo> cfCallTrackingOperatorInfoMap = cfCallTrackingOperatorInfos.stream().collect(Collectors.toMap(CfCallTrackingOperatorInfo::getOperatorId, Function.identity()));
//        List<Integer> operatorIdList = cfCallTrackingOperatorInfos.stream().map(CfCallTrackingOperatorInfo::getOperatorId).collect(Collectors.toList());
//        userIds.removeAll(operatorIdList);
//        Set<CfCallTrackingOperatorInfo> cfCallTrackingOperatorInfos1 = userIds.stream().map(val -> new CfCallTrackingOperatorInfo(val, "")).collect(Collectors.toSet());
//        try {
//            cfCallTrackingOperatorInfoBiz.insertSet(cfCallTrackingOperatorInfos1);
//        } catch (Exception e) {
//            LOGGER.error("error msg", e);
//        }
//        List<CfCallTrackingOperatorInfo> collect = userAccountsByRoleId.stream().map(val -> {
//            int operatorId = val.getUserId();
//            String name = val.getAccount().getName();
//            CfCallTrackingOperatorInfo cfCallTrackingOperatorInfo = cfCallTrackingOperatorInfoMap.get(operatorId);
//            cfCallTrackingOperatorInfo.setName(name);
//            return cfCallTrackingOperatorInfo;
//        }).collect(Collectors.toList());
//        Map<String, Object> result = Maps.newHashMap();
//        result.put("pagination", PageUtil.transform2PageMap(userAccountsByRoleId));
//        result.put("data", collect);
//        collect.forEach(System.out::println);
//        System.out.println(collect.size());
//    }
//}