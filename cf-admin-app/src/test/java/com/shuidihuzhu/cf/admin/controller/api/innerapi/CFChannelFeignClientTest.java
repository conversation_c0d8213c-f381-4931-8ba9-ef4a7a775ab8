package com.shuidihuzhu.cf.admin.controller.api.innerapi;

import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.client.cf.admin.client.CFChannelFeignClient;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;

/**
 * @author: cuikexiang
 * @date: 2020/12/21
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class CFChannelFeignClientTest {
    @Autowired
    private CFChannelFeignClient cfChannelFeignClient;

    @Test
    public void testGetByName() {
        System.out.println(cfChannelFeignClient.getByName("test"));
    }

    @Test
    public void testGetById() {
        System.out.println(cfChannelFeignClient.getById(11));
    }

    @Test
    public void testListChannelGroupByIds() {
        System.out.println(cfChannelFeignClient.listChannelGroupByIds(Arrays.asList(1,2,3)));
    }

    public void testListGroupIdsByQrcodeIds() {
    }
}