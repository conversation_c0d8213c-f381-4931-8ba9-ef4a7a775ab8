package com.shuidihuzhu.cf.admin.mq;

import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.admin.mq.report.AdminUpdateCredibleInfoV2Consumer;
import com.shuidihuzhu.cf.biz.crowdfunding.report.CfSendProveBiz;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;


@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class AdminUpdateCredibleInfoV2ConsumerTest {
    @Autowired
    private CfSendProveBiz cfSendProveBiz;
    @Autowired
    private AdminUpdateCredibleInfoV2Consumer adminUpdateCredibleInfoV2Consumer;

    static {
        System.setProperty("spring.cloud.consul.host","consul.bedin.shuiditech.com:80");
    }


    @Test
    public void testMq(){
        var lastOneByCaseId = cfSendProveBiz.getLastOneByCaseId(2296640);

        ConsumerMessage consumerMessage = new ConsumerMessage();
        consumerMessage.setPayload(lastOneByCaseId);
        adminUpdateCredibleInfoV2Consumer.consumeMessage(consumerMessage);
    }
}