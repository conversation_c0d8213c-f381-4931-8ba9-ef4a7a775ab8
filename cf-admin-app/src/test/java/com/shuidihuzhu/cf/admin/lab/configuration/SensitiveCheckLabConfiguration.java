package com.shuidihuzhu.cf.admin.lab.configuration;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;
import java.sql.SQLException;

/**
 * 内部配置的都是各数据源从库，用于本地跑任务使用
 * <AUTHOR>
 * @date 2020/5/6 15:16
 */
@Configuration
public class SensitiveCheckLabConfiguration {

    @Bean("cf-slave")
    @ConditionalOnProperty(value = "cf-admin-api.test.online.slave.start", havingValue = "true")
    public DataSource cfSlave() throws SQLException {
        String ipPort = "*********:3306";
        String dbName = "shuidi_crowdfunding";
        DruidDataSource build = DruidDataSourceBuilder.create().build();
        build.setFilters("stat,wall,slf4j");
        build.setInitialSize(1);
        build.setMaxActive(1);
        build.setMaxWait(60000);
        build.setMinEvictableIdleTimeMillis(300000);
        build.setValidationQuery("ELECT 'x'");
        build.setPoolPreparedStatements(true);
        build.setMaxPoolPreparedStatementPerConnectionSize(10);
        build.setName(dbName);
        build.setUrl("jdbc:mysql://"+ ipPort +"/"+dbName+"?serverTimezone=Asia/Shanghai&autoReconnect=true&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&useSSL=false&useAffectedRows=true&nullCatalogMeansCurrent=true&useLocalSessionState=true");

        build.setUsername("cf_rd_r");
        build.setPassword("QidaIcuvaJ5CvAJt");
        return build;
    }

    @Bean("cf-order-slave")
    @ConditionalOnProperty(value = "cf-admin-api.test.online.slave.start", havingValue = "true")
    public DataSource cfOrderSlave() throws SQLException {
        String ipPort = "**********:3306";
        String dbName = "shuidi_cf_order";
        DruidDataSource build = DruidDataSourceBuilder.create().build();
        build.setFilters("stat,wall,slf4j");
        build.setInitialSize(1);
        build.setMaxActive(1);
        build.setMaxWait(60000);
        build.setMinEvictableIdleTimeMillis(300000);
        build.setValidationQuery("ELECT 'x'");
        build.setPoolPreparedStatements(true);
        build.setMaxPoolPreparedStatementPerConnectionSize(10);
        build.setName(dbName);
        build.setUrl("jdbc:mysql://"+ ipPort +"/"+dbName+"?serverTimezone=Asia/Shanghai&autoReconnect=true&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&useSSL=false&useAffectedRows=true&nullCatalogMeansCurrent=true&useLocalSessionState=true");

        build.setUsername("cf_order_new_rd_r");
        build.setPassword("xltD28EqcksM6DFw");
        return build;
    }

}
