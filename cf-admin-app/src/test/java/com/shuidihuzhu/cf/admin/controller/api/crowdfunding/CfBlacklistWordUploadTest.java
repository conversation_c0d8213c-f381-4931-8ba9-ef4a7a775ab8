package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.biz.crowdfunding.CfBlacklistWordBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.CfBlacklistWordTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfBlacklistWord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> Ahrievil
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class CfBlacklistWordUploadTest {

    @Autowired
    private CfBlacklistWordBiz cfBlacklistWordBiz;

    @Test
    public void testUpload() throws Exception {
        //String filePath = "/Users/<USER>/Desktop/小程序敏感词.txt";
        String crowdfundingInfoFilePath = "http://cf-images.oss-cn-shanghai.aliyuncs.com/file/sensitiveWord.txt";
        CfBlacklistWordTypeEnum cfBlacklistWordTypeEnum = CfBlacklistWordTypeEnum.CROWDFUNDING;
        URLConnection urlConnection = new URL(crowdfundingInfoFilePath).openConnection();
        InputStream inputStream = urlConnection.getInputStream();
        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
        try (Stream<String> lines = bufferedReader.lines()) {
            addIntoTable(cfBlacklistWordTypeEnum, lines);
        } catch (Exception e) {
            log.error("error", e);
        }
    }

    @Test
    public void testUploadMina() throws Exception {
        String filePath = "/Users/<USER>/Desktop/小程序敏感词.txt";
        CfBlacklistWordTypeEnum cfBlacklistWordTypeEnum = CfBlacklistWordTypeEnum.MINA;
        try (Stream<String> lines = Files.lines(Paths.get(filePath), Charset.defaultCharset())) {
            addIntoTable(cfBlacklistWordTypeEnum, lines);
        } catch (Exception e) {
            log.error("error", e);
        }
    }

    private void addIntoTable(CfBlacklistWordTypeEnum cfBlacklistWordTypeEnum, Stream<String> lines) {
        Set<String> wordSet = lines.collect(Collectors.toSet());
        log.info("wordSet size:{}", wordSet.size());
        List<CfBlacklistWord> list = wordSet.stream().filter(StringUtils::isNotBlank).map(val ->
                new CfBlacklistWord(val.trim().replaceAll("\\r", ""), cfBlacklistWordTypeEnum.getCode()))
                .collect(Collectors.toList());
        cfBlacklistWordBiz.addList(list);
        log.info("list size:{}", list.size());
    }
}
