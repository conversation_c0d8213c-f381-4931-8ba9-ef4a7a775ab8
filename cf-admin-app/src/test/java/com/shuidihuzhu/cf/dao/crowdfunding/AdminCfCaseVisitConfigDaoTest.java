package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.admin.util.ReadExcelUtil;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCaseVisitConfigBiz;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018-05-07  17:22
 * @description
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class AdminCfCaseVisitConfigDaoTest {

    @Resource
    private AdminCfCaseVisitConfigDao adminCfCaseVisitConfigDao;

    @Resource
    private AdminCaseVisitConfigBiz caseVisitConfigBiz;

    @Test
    public void testA(){
        int i = adminCfCaseVisitConfigDao.updateAbnormalHiddenAndHiddenTitle(973061, 1, null, null);
        System.out.println("i = " + i);

    }

    @Test
    public void testB() {
        caseVisitConfigBiz.updateCanShowByCaseId(143139,0);
        Date now = DateUtil.getCurrentDate();
        caseVisitConfigBiz.updateBannerTextAndStartEndTime(143139, "test",now,DateUtil.addDays(now,5));
    }
}
