package com.shuidihuzhu.cf.dao.workorder;

import com.shuidihuzhu.BaseTest;
import com.shuidihuzhu.cf.model.admin.workorder.imageContent.CfImageContentAuditInfo;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.Assert.*;

/**
 * @author: cuikexiang
 */
public class CfImageContentAuditDAOTest extends BaseTest {

    @Autowired
    private CfImageContentAuditDAO cfImageContentAuditDAO;

    @Test
    public void addImageContentInfo() {
        CfImageContentAuditInfo auditInfo = new CfImageContentAuditInfo();
        auditInfo.setCaseId(110);
        auditInfo.setWorkOrderId(0L);
        auditInfo.setInfoStatus(0);
        auditInfo.setRejectDetail("setRejectDetail");
        auditInfo.setOperatorId(110);
        auditInfo.setOperatorDetail("setOperatorDetail");
        int i = cfImageContentAuditDAO.addImageContentInfo(auditInfo);
        System.out.println(i);
        System.out.println(auditInfo);
    }
}