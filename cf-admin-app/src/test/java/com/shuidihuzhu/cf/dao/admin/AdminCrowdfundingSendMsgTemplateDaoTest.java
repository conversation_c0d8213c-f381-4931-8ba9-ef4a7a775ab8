package com.shuidihuzhu.cf.dao.admin;

import com.shuidihuzhu.BaseTest;
import com.shuidihuzhu.cf.model.crowdfunding.AdminSmsTemplateSettingsInfo;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.Assert.*;

/**
 * @author: cuikexiang
 */
public class AdminCrowdfundingSendMsgTemplateDaoTest extends BaseTest {

    @Autowired
    private AdminCrowdfundingSendMsgTemplateDao adminCrowdfundingSendMsgTemplateDao;
    @Test
    public void addSmsTemplate() {
        AdminSmsTemplateSettingsInfo.SmsSettings smsSettings = new AdminSmsTemplateSettingsInfo.SmsSettings();
        smsSettings.setModelNum("setModelNum");
        smsSettings.setSmsGroup(1);

        smsSettings.setPriority(0);
        smsSettings.setOperatorId(1);
        int i = adminCrowdfundingSendMsgTemplateDao.addSmsTemplate(smsSettings);
        System.out.println(i);
    }
}