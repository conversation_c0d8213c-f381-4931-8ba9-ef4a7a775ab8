package com.shuidihuzhu.cf.dao.risk;

import com.shuidihuzhu.BaseTest;
import com.shuidihuzhu.cf.model.risk.Dishonest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.Assert.*;

/**
 * @author: cuikexiang
 */
public class DishonestDaoTest extends BaseTest {

    @Autowired
    private DishonestDao dishonestDao;

    @Test
    public void getDishonestInfo() {
        Dishonest dishonest = dishonestDao.getDishonestInfo(2881096, 0, "杨春祥");
        System.out.println(dishonest);
    }
}