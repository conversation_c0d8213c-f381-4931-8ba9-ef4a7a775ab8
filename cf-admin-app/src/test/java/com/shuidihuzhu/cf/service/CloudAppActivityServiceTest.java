package com.shuidihuzhu.cf.service;

import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.service.risk.repeat.CfRepeatCaseService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2021/5/24
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
public class CloudAppActivityServiceTest {

    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }

    @Resource
    private CloudAppActivityService cloudAppActivityService;

    @Resource
    private CfRepeatCaseService cfRepeatCaseService;

    @Test
    public void test() {
        cfRepeatCaseService.getRepeatCaseIdList("2412e68a-3f85-4330-91a8-ffd78a42f082");
//        String startTime = "2021-05-24 00:00:00";
//        String endTime = "2021-05-24 15:50:00";
//        cloudAppActivityService.queryAppActivity(60, 1,startTime, endTime);

    }
}
