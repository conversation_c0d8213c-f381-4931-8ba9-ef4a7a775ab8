package com.shuidihuzhu.cf.service.admin;

import com.shuidihuzhu.cf.admin.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

/** 
* AdminPageRecommendCaseService Tester. 
* 
* <AUTHOR> 
* @since <pre>4月 27, 2020</pre> 
* @version 1.0 
*/ 
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
public class AdminPageRecommendCaseServiceTest { 

    @Autowired
    private AdminPageRecommendCaseService adminPageRecommendCaseService;
    @Autowired
    protected MockMvc mockMvc;

    @Before
    public void before() throws Exception { 
    } 
    
    @After
    public void after() throws Exception { 
    } 
    
    /** 
     * 
     * Method: modRecommendPositionCase(List<PageRecommendPositionModel> recommendPosition) 
     * 
     */ 
    @Test
    public void testModRecommendPositionCase() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
     * 
     * Method: modListPositionCase(List<PageListPositionModel> listPosition) 
     * 
     */ 
    @Test
    public void testModListPositionCase() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
     * 
     * Method: isCaseInRecommendList(String infoUuid) 
     * 
     */ 
    @Test
    public void testIsCaseInRecommendList() {
        boolean caseInRecommendList = adminPageRecommendCaseService.isCaseInRecommendList("67cdfaba-7dbc-40ed-957f-f8506eddc51d");
        Assert.assertTrue(caseInRecommendList);
    } 
    
    /** 
     * 
     * Method: updateContentByActivityId(String content, Integer activityId) 
     * 
     */ 
    @Test
    public void testUpdateContentByActivityId() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
     * 
     * Method: batchUpdateCrowdfundingRecommendCaseByIds(List<AdminCrowdfundingRecommendCaseDO> crowdfundingRecommendCaseDOs) 
     * 
     */ 
    @Test
    public void testBatchUpdateCrowdfundingRecommendCaseByIds() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    
    /** 
     * 
     * Method: getInfoIdsByIdsOrderByFieldIds(List<Integer> ids) 
     * 
     */ 
    @Test
    public void testGetInfoIdsByIdsOrderByFieldIds() throws Exception { 
        //TODO: Test goes here... 
                /* 
                try { 
                   Method method = AdminPageRecommendCaseService.getClass().getMethod("getInfoIdsByIdsOrderByFieldIds", List<Integer>.class); 
                   method.setAccessible(true); 
                   method.invoke(<Object>, <Parameters>); 
                } catch(NoSuchMethodException e) { 
                } catch(IllegalAccessException e) { 
                } catch(InvocationTargetException e) { 
                } 
                */ 
            }
    /** 
     * 
     * Method: getIdsByTypeOrderBySort(Integer type) 
     * 
     */ 
    @Test
    public void testGetIdsByTypeOrderBySort() throws Exception { 
        //TODO: Test goes here... 
                /* 
                try { 
                   Method method = AdminPageRecommendCaseService.getClass().getMethod("getIdsByTypeOrderBySort", Integer.class); 
                   method.setAccessible(true); 
                   method.invoke(<Object>, <Parameters>); 
                } catch(NoSuchMethodException e) { 
                } catch(IllegalAccessException e) { 
                } catch(InvocationTargetException e) { 
                } 
                */ 
            }
    /** 
     * 
     * Method: saveRecommendHistory(List<AdminCrowdfundingRecommendCaseDO> oldRecommends, List<AdminCrowdfundingRecommendCaseDO> newRecommends, AdminCrowdfundingInfoTypeEnum recommendType) 
     * 
     */ 
    @Test
    public void testSaveRecommendHistory() throws Exception { 
        //TODO: Test goes here... 
                /* 
                try { 
                   Method method = AdminPageRecommendCaseService.getClass().getMethod("saveRecommendHistory", List<AdminCrowdfundingRecommendCaseDO>.class, List<AdminCrowdfundingRecommendCaseDO>.class, AdminCrowdfundingInfoTypeEnum.class); 
                   method.setAccessible(true); 
                   method.invoke(<Object>, <Parameters>); 
                } catch(NoSuchMethodException e) { 
                } catch(IllegalAccessException e) { 
                } catch(InvocationTargetException e) { 
                } 
                */ 
            }
}
