package com.shuidihuzhu.cf.service.admin;

import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.model.message.HuZhuSqlRecord;
import com.shuidihuzhu.cf.service.message.CfGetHuZhuSqlMessageService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
public class AdminDownTaskServiceTest {

}
