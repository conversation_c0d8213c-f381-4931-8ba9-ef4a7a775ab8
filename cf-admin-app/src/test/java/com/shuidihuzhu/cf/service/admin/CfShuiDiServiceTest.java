package com.shuidihuzhu.cf.service.admin;

import com.shuidi.weixin.common.bean.result.WxError;
import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.wx.biz.ShuidiWxService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@Slf4j
public class CfShuiDiServiceTest {
    @Autowired
    private ShuidiWxService shuidiWxService;

    @Test
    public void testGetSql() {
        try {
            WxError wxError = shuidiWxService.messageMassDelete("3147488730", "0", 1);
            log.info("", wxError);
        } catch (Exception e) {
            log.error("", e);
        }
    }
}
