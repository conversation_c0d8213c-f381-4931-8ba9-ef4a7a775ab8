package com.shuidihuzhu.cf.service.admin;

import com.shuidi.weixin.common.api.WxConsts;
import com.shuidi.weixin.mp.bean.WxMpMassOpenIdsMessage;
import com.shuidi.weixin.mp.bean.result.WxMpMassSendResult;
import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.wx.biz.ShuidiWxService;

import org.assertj.core.util.Sets;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Set;

import lombok.extern.slf4j.Slf4j;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@Slf4j
public class MsgDataIdTest {
    //江南
    private String openidJiang = "oI_3ovlgEW0bhoEWTqk95A4fUvM4";
    private String openidGuYu = "oI_3ovgtCh2H_Sd4vbO9jfiYoaJU";
    private String openidBiu = "oI_3ovlurZCBbVA7_khPCqUnz58o";
    private String openidTest = "oI_3ovlurZCBbVA7_khPCqUnz85o";
    private int thirdType = 60;//为民筹公益
    private String mediaId = "oXQaj1n_rzHeBbIgHor8dp-uD3JY1t-gfn5V_R-Yeqk";
    @Autowired
    private ShuidiWxService shuidiWxService;

    @Test
    public void testGetMsgDataId() {
        Set<String> userOpenidSet = Sets.newHashSet();
//        PushWxArticleSubtask subtask = new PushWxArticleSubtask();
        userOpenidSet.add(openidGuYu);
        userOpenidSet.add(openidTest);
//        subtask.setThirdType(thirdType);
//        subtask.setArticleMediaId(mediaId);
//        subtask.setArticleTaskId(52);
//        subtask.setId(45);
        try {
            WxMpMassOpenIdsMessage wxMpMassOpenIdsMessage = new WxMpMassOpenIdsMessage();
            wxMpMassOpenIdsMessage.setMediaId(mediaId);
            wxMpMassOpenIdsMessage.setMsgType(WxConsts.MASS_MSG_NEWS);
            wxMpMassOpenIdsMessage.getToUsers().addAll(userOpenidSet);
            WxMpMassSendResult wxMpMassSendResult = shuidiWxService.massOpenIdsMessageSend(wxMpMassOpenIdsMessage, thirdType);
            log.info("");
        } catch (Exception e) {
            log.error("", e);
        }
//        mpNewsSendService.doSendMpNewsMessage(userOpenidSet, subtask);
    }

    @Test
    public void testJsonMsgDataId() {
        String reustString = "{\n" +
                "    \"errcode\": 0,\n" +
                "    \"errmsg\": \"send job submission success\",\n" +
                "    \"msg_id\": 3147483663,\n" +
                "    \"msg_data_id\": 2247483679\n" +
                "}";
        try {
            WxMpMassSendResult wxMpMassSendResult = WxMpMassSendResult.fromJson(reustString);
            log.info("", wxMpMassSendResult);
        } catch (Exception e) {
            log.error("", e);
        }
    }

}
