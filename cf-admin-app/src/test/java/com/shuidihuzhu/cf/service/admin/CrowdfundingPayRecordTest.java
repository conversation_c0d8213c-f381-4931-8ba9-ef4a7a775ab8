package com.shuidihuzhu.cf.service.admin;

import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.client.feign.CrowdfundingPayRecordFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.enums.crowdfunding.NewCfRefundConstant;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.List;

/**
 * @author: wanghui
 * @time: 2019/3/4 12:10 PM
 * @description:
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
public class CrowdfundingPayRecordTest {
    @Autowired
    CrowdfundingPayRecordFeignClient crowdfundingPayRecordFeignClient;

    /**
     * curl -d "orderIds=1&orderIds=2" http://api-zelda.shuiditech.com/innerapi/feign/getPaySuccessByOrderIds
     * curl -d "payUids=14c41c0a5fc1fed4742d651dfdc2641a&payUids=9f8560bcc5921eecdef40ce9f7035274" http://api-zelda.shuiditech.com/innerapi/feign/getPaySuccessByPayUids
     * curl -d "payUid=14c41c0a5fc1fed4742d651dfdc2641a" http://api-zelda.shuiditech.com/innerapi/feign/getByPayUid
     * curl -d "orderIds=1&orderIds=2&start=1&size=1" http://api-zelda.shuiditech.com/innerapi/feign/selectByOrderIdList
     * curl -d "orderIds=1&orderIds=2" http://api-zelda.shuiditech.com/innerapi/feign/getAllPaySuccessByOrderIds
     * curl -d "orderIds=1&orderIds=2&refundStatusList=UNHANDLE" http://api-zelda.shuiditech.com/innerapi/feign/getPaySuccessByOrderIdsAndRefundStatus
     * curl -d "payRecordId=1&refundStatus=0&refundTime=2019/3/4 11:43:49&refundAmount=1&refundReason=测试" http://api-zelda.shuiditech.com/innerapi/feign/updateRefundStatus
     * curl -d "refundStatus=1&orderIds=1" http://api-zelda.shuiditech.com/innerapi/feign/updateRefundStatusByOrderIds
     * curl -d "orderId=1" http://api-zelda.shuiditech.com/innerapi/feign/getByOrderId
     */
    @Test
    public void testCrowdfundingPayRecordFeignClient(){
        FeignResponse<List<CrowdfundingPayRecord>> getPaySuccessByOrderIds = crowdfundingPayRecordFeignClient.getPaySuccessByOrderIds(Lists.newArrayList(1L, 2L));
        log.debug("getPaySuccessByOrderIds result:{}",getPaySuccessByOrderIds);
        FeignResponse<List<CrowdfundingPayRecord>> getPaySuccessByPayUids = crowdfundingPayRecordFeignClient.getPaySuccessByPayUids(Lists.newArrayList("14c41c0a5fc1fed4742d651dfdc2641a", "9f8560bcc5921eecdef40ce9f7035274"));
        log.debug("getPaySuccessByPayUids result:{}",getPaySuccessByPayUids);
        FeignResponse<CrowdfundingPayRecord> getByPayUid = crowdfundingPayRecordFeignClient.getByPayUid("14c41c0a5fc1fed4742d651dfdc2641a");
        log.debug("getByPayUid result:{}",getByPayUid);
        FeignResponse<List<CrowdfundingPayRecord>> selectByOrderIdList = crowdfundingPayRecordFeignClient.selectByOrderIdList(Lists.newArrayList(1L, 2L), 1, 1);
        log.debug("selectByOrderIdList result:{}",selectByOrderIdList);
        FeignResponse<List<CrowdfundingPayRecord>> getAllPaySuccessByOrderIds = crowdfundingPayRecordFeignClient.getAllPaySuccessByOrderIds(Lists.newArrayList(1L, 2L));
        log.debug("getAllPaySuccessByOrderIds result:{}",getAllPaySuccessByOrderIds);
        FeignResponse<List<CrowdfundingPayRecord>> getPaySuccessByOrderIdsAndRefundStatus = crowdfundingPayRecordFeignClient.getPaySuccessByOrderIdsAndRefundStatus(Lists.newArrayList(1L, 2L), Lists.newArrayList(NewCfRefundConstant.RefundStatus.UNHANDLE, NewCfRefundConstant.RefundStatus.HANDLE_FAILED));
        log.debug("getPaySuccessByOrderIdsAndRefundStatus result:{}",getPaySuccessByOrderIdsAndRefundStatus);
//        FeignResponse<Integer> updateRefundStatus = crowdfundingPayRecordFeignClient.updateRefundStatus(1, 0, new Date(), 1, "测试");
//        log.debug("updateRefundStatus result:{}",updateRefundStatus);
        FeignResponse<Integer> updateRefundStatusByOrderIds = crowdfundingPayRecordFeignClient.updateRefundStatusByOrderIds(1, Lists.newArrayList(1L));
        log.debug("updateRefundStatusByOrderIds result:{}",updateRefundStatusByOrderIds);
        FeignResponse<CrowdfundingPayRecord> getByOrderId = crowdfundingPayRecordFeignClient.getByOrderId(1L);
        log.debug("getByOrderId result:{}",getByOrderId);

    }
}
