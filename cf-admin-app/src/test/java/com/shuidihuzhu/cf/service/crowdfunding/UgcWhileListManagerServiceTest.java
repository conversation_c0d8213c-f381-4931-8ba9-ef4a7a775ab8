package com.shuidihuzhu.cf.service.crowdfunding;

import com.shuidihuzhu.cf.admin.Application;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
/**
 * @author: fengxuan
 * @create 2019-11-15 17:14
 **/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class UgcWhileListManagerServiceTest {

    @Autowired
    UgcWhileListManagerService managerService;

    DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
    @Test
    public void listAllValid() {

    }

    @Test
    public void addImmediately() {
    }

    @Test
    public void modifyImmediately() throws InterruptedException {
    }


    @Test
    public void testArray() {
    }
}