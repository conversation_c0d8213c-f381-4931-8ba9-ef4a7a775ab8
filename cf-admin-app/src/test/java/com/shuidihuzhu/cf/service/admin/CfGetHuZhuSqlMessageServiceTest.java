package com.shuidihuzhu.cf.service.admin;

import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.model.message.HuZhuSqlRecord;
import com.shuidihuzhu.cf.service.message.CfGetHuZhuSqlMessageService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
public class CfGetHuZhuSqlMessageServiceTest {
    @Autowired
    private CfGetHuZhuSqlMessageService cfGetHuZhuSqlMessageService;

    @Test
    public void testFormatting() {
        cfGetHuZhuSqlMessageService.getHuZhuSqlRecord(1);
    }

    @Test
    /**
     * 如果把 Prefix 设为某个文件夹名，就可以罗列以此 Prefix 开头的文件，
     * 即该文件夹下递归的所有的文件和子文件夹（目录）。文件名在Contents中显示。 此时的路径以 / 结束
     *如果再把 Delimiter 设置为 “/” 时，返回值就只罗列该文件夹下的文件和子文件夹（目录），
     * 该文件夹下的子文件名（目录）返回在 CommonPrefixes 部分，子文件夹下递归的文件和文件夹不被显示。
     */
    public void testDownFiles() {
        //mpNewsSendService.downOssFiles("");
    }

    @Test
    public void testReadFiles() {
        // mpNewsSendService.readFile("2018/03/15/test/dst/");
    }

    @Test
    public void testTread() {
//        mpNewsSendService.doSendMpNewsMessage(Sets.newHashSet(), 11);
    }

    @Test
    public void testGetSql() {
        HuZhuSqlRecord huZhuSqlRecord = cfGetHuZhuSqlMessageService.getHuZhuSqlRecord(11);
        System.out.println(huZhuSqlRecord);
    }
}
