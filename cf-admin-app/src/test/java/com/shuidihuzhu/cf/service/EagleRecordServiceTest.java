package com.shuidihuzhu.cf.service;

import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.facade.eagle.EagleRecordService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2021/4/29
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
public class EagleRecordServiceTest {

    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }

    @Resource
    private EagleRecordService eagleRecordService;

    @Test
    public void test() {
        eagleRecordService.getRecords();
    }
}
