package com.shuidihuzhu.cf.service.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.model.crowdfunding.vo.workflow.WorkFlowOrgStaffVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.workflow.WorkFlowStaffVo;
import com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowStaffStatusRecord;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.junit.Assert.*;

/**
 * @author: fengxuan
 * @create 2020-02-14 22:01
 **/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class WorkFlowStaffServiceTest {

    @Autowired
    private WorkFlowStaffService staffService;

    static {
        System.setProperty("spring.cloud.consul.host","consul.zelda.shuiditech.com:80");
    }

    @Test
    public void addTest() {
        //staffService.changeStatus(55941, 55941, 1);
        List<WorkFlowOrgStaffVo> list = staffService.listOrgStaffStatus();
        System.out.println(JSON.toJSONString(list));
        System.out.println("====");
        int status = staffService.showStatus(55941);
        System.out.println("status: " + status);
        List<WorkFlowStaffStatusRecord> statusRecords = staffService.listStatusChangeRecords(55941);
        System.out.println("statusRecords: " + JSON.toJSONString(statusRecords));
        WorkFlowStaffVo workFlowStaffVo = staffService.searchStaffStatus(Lists.newArrayList(152), 0L, 1, 20,0);
        System.out.println("workFlowStaffVo: " + JSON.toJSONString(workFlowStaffVo));

    }

    @Test
    public void changeStatus() {
        staffService.changeStatus(55941, 55941, 2);
    }

    @Test
    public void listOrgStaffStatus() {
        List<WorkFlowOrgStaffVo> list = staffService.listOrgStaffStatus();
        System.out.println(JSON.toJSONString(list));
    }

    @Test
    public void searchStaffStatus() {
        WorkFlowStaffVo workFlowStaffVo = staffService.searchStaffStatus(Lists.newArrayList(152), 0L, 1, 20,0);
        System.out.println("workFlowStaffVo: " + JSON.toJSONString(workFlowStaffVo));
    }

    @Test
    public void showStatus() {
        staffService.showStatus(55941);
    }

    @Test
    public void listStatusChangeRecords() {
        List<WorkFlowStaffStatusRecord> statusRecords = staffService.listStatusChangeRecords(55941);
        System.out.println("statusRecords: " + JSON.toJSONString(statusRecords));
    }
}