package com.shuidihuzhu.cf.service.admin.stats;

import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.enums.admin.stats.UsageFreqStatsBizTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.List;

/** 
* AdminUsageFreqStatsServiceImpl Tester. 
* 
* <AUTHOR> 
* @since <pre>6月 10, 2020</pre> 
* @version 1.0 
*/ 
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
public class AdminUsageFreqStatsServiceImplTest { 

    @Autowired
    private AdminUsageFreqStatsService adminUsageFreqStatsService;
    @Autowired
    protected MockMvc mockMvc;

    @Before
    public void before() throws Exception { 
    } 
    
    @After
    public void after() throws Exception { 
    } 
    
    /** 
     * 
     * Method: incrementUsageFreq(String statsKey, String statsDim, UsageFreqStatsBizTypeEnum bizTypeEnum) 
     * 
     */ 
    @Test
    public void testIncrementUsageFreq() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
     * 
     * Method: incrementUsageFreqBatch(List<String> statsKeys, String statsDim, UsageFreqStatsBizTypeEnum bizTypeEnum) 
     * 
     */ 
    @Test
    public void testIncrementUsageFreqBatch() throws Exception {
        adminUsageFreqStatsService.incrementUsageFreqBatch(List.of("content", "patientName", "selfRealName", "amountEnd"),
                "", UsageFreqStatsBizTypeEnum.CASE_SEARCH);
    } 
    
    
    /** 
     * 
     * Method: insertBySelective(String statsKey, String statsDim, UsageFreqStatsBizTypeEnum bizTypeEnum) 
     * 
     */ 
    @Test
    public void testInsertBySelective() throws Exception { 
        //TODO: Test goes here... 
                /* 
                try { 
                   Method method = AdminUsageFreqStatsServiceImpl.getClass().getMethod("insertBySelective", String.class, String.class, UsageFreqStatsBizTypeEnum.class); 
                   method.setAccessible(true); 
                   method.invoke(<Object>, <Parameters>); 
                } catch(NoSuchMethodException e) { 
                } catch(IllegalAccessException e) { 
                } catch(InvocationTargetException e) { 
                } 
                */ 
            }
}
