package com.shuidihuzhu.cf.service;

import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.service.admin.CfHospitalAuditService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2020/2/3
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
public class EventCenterPublishServiceTest {

    static {
        System.setProperty("spring.cloud.consul.host", "consul.zelda.shuiditech.com:80");
    }

    @Autowired
    private EventCenterPublishService eventCenterPublishService;

//    @Test
//    public void test() {
//        boolean flag = eventCenterPublishService.getAuditPassDelay();
//        System.out.println("flag====" + flag);
//    }
}
