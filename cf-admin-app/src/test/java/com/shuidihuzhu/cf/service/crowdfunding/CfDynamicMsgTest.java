package com.shuidihuzhu.cf.service.crowdfunding;

import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminPushDynamicMsgService;
import com.shuidihuzhu.cf.model.admin.CfPushDynamicMsgModel;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditCreateOrderService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.ai.AiRuleJudgeService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.ai.impl.AiMixCarCondition;
import com.shuidihuzhu.cf.service.workorder.initialAudit.ai.impl.AiMixHouseCondition;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.env.Environment;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.sql.SQLException;

/**
 * @package: com.shuidihuzhu.cf.service.crowdfunding
 * @Author: liujiawei
 * @Date: 2020-01-11  16:31
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class CfDynamicMsgTest {

    @Autowired
    private AdminPushDynamicMsgService adminPushDynamicMsgService;

    @Autowired
    private Environment environment;

    @Autowired
    private AiMixHouseCondition aiMixHouseCondition;
    @Autowired
    private AiMixCarCondition aiMixCarCondition;
    @Autowired
    private AiRuleJudgeService aiRuleJudgeService;
    @Resource
    private InitialAuditCreateOrderService initialAuditCreateOrderService;

    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }

    @Test
    public void testSendMsg() {

        CfPushDynamicMsgModel cfPushDynamicMsgModel = new CfPushDynamicMsgModel(1993524, 1, 2, 3,0l);

//        adminPushDynamicMsgService.sendDynamicMsg(cfPushDynamicMsgModel);
    }

    @Test
    public void test() throws SQLException {
        initialAuditCreateOrderService.smartSecondReviewWorkOrder(2906834);
//        aiRuleJudgeService.Judge(220736,220735, 2895707);
//
//        boolean check = aiMixCarCondition.check(2895267, "");
//        System.out.println(check);
////        String prefix = "spring.datasource.druid.shuidi-cf-tidb";
//        String prefix = "spring.datasource.druid.cf-admin-api-cf.cf-admin-api";
//  ;
//        System.out.println(environment.getProperty(prefix + ".url"));
//        System.out.println(environment.getProperty(prefix + ".name"));
//        System.out.println(environment.getProperty(prefix + ".username"));
//        System.out.println(environment.getProperty(prefix + ".password"));
//        System.out.println(environment.getProperty(prefix + ".driverClass"));
//
//        DruidDataSource dataSource = new DruidDataSource();
//        dataSource.setName(environment.getProperty(prefix + ".name"));
//        dataSource.setUrl(environment.getProperty(prefix + ".url"));
//        dataSource.setUsername(environment.getProperty(prefix + ".username"));
//        dataSource.setPassword(environment.getProperty(prefix + ".password"));
//        dataSource.init();
//
//        System.out.println(dataSource.getUrl());
//        System.out.println(dataSource.getUsername());
    }

}
