package com.shuidihuzhu.cf.service.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminPushDynamicMsgService;
import com.shuidihuzhu.cf.biz.crowdfunding.supply.CfSupplyActionBiz;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.model.admin.vo.CfDynamicMsgVo;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyAction;
import com.shuidihuzhu.cf.model.param.SupplyActionSearchParam;
import com.shuidihuzhu.cf.model.param.SupplyProgressWorkHandleParam;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.vo.crowdfunding.CfSupplyProgressDetailListVo;
import com.shuidihuzhu.cf.vo.crowdfunding.CfSupplyProgressVo;
import com.shuidihuzhu.cf.vo.crowdfunding.CfSupplyProgressWorkDetailVo;
import com.shuidihuzhu.client.cf.admin.enums.CfProgressReasonEnum;
import com.shuidihuzhu.client.cf.admin.enums.CfProgressReasonRelationEnum;
import com.shuidihuzhu.client.cf.admin.model.CfInfoSupplyField;
import com.shuidihuzhu.client.cf.admin.model.CfSupplyActionVo;
import com.shuidihuzhu.client.cf.workorder.CfUgcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.UgcHandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2020-01-10 18:19
 **/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class CfSupplyProgressServiceTest {

    @Autowired
    CfSupplyProgressService cfSupplyProgressService;

    @Autowired
    AdminPushDynamicMsgService adminPushDynamicMsgService;

    @Autowired
    private CfUgcWorkOrderClient ugcWorkOrderClient;

    @Autowired
    CfSupplyActionBiz cfSupplyActionBiz;

    @Autowired
    private CfSupplyActionService cfSupplyActionService;

    @Resource
    private IRiskDelegate riskDelegate;

    static {
        System.setProperty("spring.cloud.consul.host","consul.bedin.shuiditech.com:80");
    }


    @Test
    public void uniteTest() {
        doSupply();
    }


    @Test
    public void getSupplyButtonInfo() {
        CfSupplyProgressVo.SupplyProgressButtonInfo supplyButtonInfo = cfSupplyProgressService.getSupplyButtonInfo(1994476);
        log.info("supplyButtonInfo:{}", JSON.toJSONString(supplyButtonInfo));
    }

    @Test
    public void doSupply() {
        CfInfoSupplyAction a = new CfInfoSupplyAction();
        a.setCaseId(2290525);
        a.setSupplyReason(CfProgressReasonEnum.r_2.getCode()+"");
        a.setComment("123891823918391823819");
        a.setSupplyUserId(73128);

        List<CfProgressReasonRelationEnum> list = CfProgressReasonRelationEnum.getByCfProgressReasonCode(CfProgressReasonEnum.r_2.getCode());

        List<CfInfoSupplyField> d = list.stream().map(r->{
            CfInfoSupplyField c = new CfInfoSupplyField();
            c.setField(r.getFieldDesc().getField());
            c.setFieldName(r.getFieldDesc().getFieldName());
            c.setFieldValue("112121");
            c.setOperationType(1);
            return c;
        }).collect(Collectors.toList());

        a.setSupplyFields(d);
        Response<Boolean> response=  cfSupplyActionService.doSupply(a);
        log.info("doSupply CfInfoSupplyAction={} response={}",a,response);
//        cfSupplyProgressService.doSupply(1995955, "1,2,3", "测试下发", 55941);
    }

    @Test
    public void doCancel() {
        cfSupplyProgressService.doCancel(1994476, 1, 55941);
    }

    @Test
    public void showRelationProgress() {
        Response<CfSupplyProgressVo> cfSupplyProgressVoResponse = cfSupplyProgressService.showRelationProgress(1995833, 13);
        log.info("supplyButtonInfo:{}", JSON.toJSONString(cfSupplyProgressVoResponse));
    }

    @Test
    public void getListTest() {
        Response<CfSupplyProgressWorkDetailVo> workDetailVoResponse = cfSupplyProgressService.viewWorkDetail(1995520, 24044, 2, CfSupplyProgressService.ViewPageSource.work_order_list);
        System.out.println(JSON.toJSONString(workDetailVoResponse));
    }

    @Test
    public void testList() {
        SupplyActionSearchParam searchParam = new SupplyActionSearchParam();
        //searchParam.setSubmitStartTime(DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss").parseDateTime("2020-01-04 00:00:00").toDate());
        searchParam.setHandleStatus(4);
        searchParam.setCurrent(1);
        searchParam.setPageSize(20);
        searchParam.setOrgId(3);
        Response<CfSupplyProgressDetailListVo> detailListVoResponse = cfSupplyProgressService.listSupplyActionSearchParam(searchParam);
        System.out.println(JSON.toJSONString(detailListVoResponse));
    }

    @Test
    public void testReason() {
        List<CfInfoSupplyAction> supplyActions = cfSupplyActionBiz.listCaseIdByReasonAndOrg(2, 0);
        System.out.println(JSON.toJSONString(supplyActions));
    }

    @Test
    public void hitWord() {
        Set<String> hitWords = riskDelegate.getHitWords("动态123");
        System.out.println(JSON.toJSONString(hitWords));
    }


    @Test
    public void pass() {
        SupplyProgressWorkHandleParam handleParam = new SupplyProgressWorkHandleParam();
        cfSupplyProgressService.pass(handleParam, 207);
        //处理新工单
        UgcHandleOrderParam ugcHandleOrderParam = new UgcHandleOrderParam();
        ugcHandleOrderParam.setWorkOrderId(handleParam.getWorkOrderId());
        ugcHandleOrderParam.setOrderType(WorkOrderType.xiafaprogress.getType());
        ugcHandleOrderParam.setHandleResult(HandleResultEnum.audit_pass.getType());
        ugcHandleOrderParam.setUserId(207);
        ugcHandleOrderParam.setOperComment("");
        Response workResponse = ugcWorkOrderClient.handleUgc(ugcHandleOrderParam);
    }

    @Test
    public void submit(){

        CfSupplyActionVo actionVo = new CfSupplyActionVo();
        String n = "{\"actionId\":\"320\",\"reasonCode\":7,\"actionStatus\":0,\"list\":[{\"field\":\"患者姓名\",\"fieldName\":\"患者姓名\",\"fieldValue\":\"嘿嘿\",\"operationType\":1,\"modify\":0,\"actionId\":0,\"fieldId\":1,\"cfItemField\":{\"id\":1,\"field\":\"患者姓名\",\"fieldType\":\"input\",\"fieldLimiter\":\"verifyName\",\"fieldLength\":5,\"fieldRequired\":\"100\",\"userId\":0}},{\"field\":\"患者年龄\",\"fieldName\":\"患者年龄\",\"fieldValue\":\"22\",\"operationType\":1,\"modify\":0,\"actionId\":0,\"fieldId\":2,\"cfItemField\":{\"id\":2,\"field\":\"患者年龄\",\"fieldType\":\"input\",\"fieldLimiter\":\"number\",\"fieldLength\":2,\"fieldRequired\":\"100\",\"userId\":0}},{\"field\":\"imgUrls\",\"fieldName\":\"动态信息的图片\",\"fieldValue\":null,\"operationType\":1,\"modify\":0,\"actionId\":0,\"fieldId\":0,\"cfItemField\":null}],\"content\":\"患者嘿嘿目前已提现udefied元，因udefied，无法提交纸质发票。现提交以下款项使用资料供说明医疗花费情况，并承诺所提交的资料真实、准确、完整，已将全部款项用于患者本人治疗。特此公示！\",\"imgUrls\":\"\"}";
        actionVo = JSON.parseObject(n,CfSupplyActionVo.class);
        OpResult<String> o = cfSupplyActionService.doSubmit(actionVo);
        log.info("actionVo={} o={}",actionVo,o);
    }

    @Test
    public void record(){
        List<CfInfoSupplyAction> list =  cfSupplyActionService.getRecord(148);
        log.info("list={} ",list);

    }

    @Test
    public void fdkf(){
        List<CfSupplyProgressVo.ProgressWorkOrderInfo> list = cfSupplyActionService.showProgress(2290574);
        log.info("list={} ",list);

    }
}