package com.shuidihuzhu.cfadmin;

import com.shuidi.weixin.mp.api.WxMpService;
import com.shuidi.weixin.mp.bean.result.WxMpMaterialFileBatchGetResult;
import com.shuidi.weixin.mp.bean.result.WxMpUser;
import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.common.web.util.ApplicationContextUtil;
import com.shuidihuzhu.wx.biz.ShuidiWxService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.*;
import java.net.URL;
import java.net.URLConnection;

/**
 * <AUTHOR> Ahrievil
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class UploadImgTest {


    @Autowired
    private ShuidiWxService shuidiWxService;


    @Test
    public void testUploadImg() throws Exception {
        File file = new File("/Users/<USER>/Desktop/水滴互助二维码.png");
        String image = this.addMaterialEverInter(file, "image");
        log.info("image:{}", image);
    }

    //Yswu8qDGjf6jUCpD-yAnPTnQL1vt51vfvPEKuETnjJ4
    //Yswu8qDGjf6jUCpD-yAnPTsBWEtaYU6GsNNFTe4h4Lc
    //Yswu8qDGjf6jUCpD-yAnPdvc2VfJeb2zyDzoBmtZaAY

    public String addMaterialEverInter(File file, String type) throws Exception {
        String accessToken="6_f-TlxyxSUWLUllVic0mMZFPGmdp9fafSnx3ch3y2P6I7IwZoYKfHe-2yyKdxRgOTnr8RN7r-XQCxlJy8VBahft2t_Ia18MVjfQ4BpZBkatOXeSjs0vaKBiqPCfoKGKytDP5e5ZbCcammCAqpNELhADAIZG";

        //上传素材
        String path="https://api.weixin.qq.com/cgi-bin/material/add_material?access_token="+accessToken+"&type="+type;
        String result =null;
        URL realUrl = new URL(path);

        URLConnection con= realUrl.openConnection();
        con.setDoInput(true);
        con.setDoOutput(true);
        con.setUseCaches(false); // post方式不能使用缓存
        // 设置请求头信息
        con.setRequestProperty("Connection", "Keep-Alive");
        con.setRequestProperty("Charset", "UTF-8");
        // 设置边界
        String BOUNDARY = "----------" + System.currentTimeMillis();
        con.setRequestProperty("Content-Type",
                "multipart/form-data; boundary="
                        + BOUNDARY);

        // 请求正文信息
        // 第一部分：
        StringBuilder sb = new StringBuilder();
        sb.append("--"); // 必须多两道线
        sb.append(BOUNDARY);
        sb.append("\r\n");
        sb.append("Content-Disposition: form-data;name=\"media\";filelength=\""+file.length()+"\";filename=\""
                + file.getName() + "\"\r\n");
        sb.append("Content-Type:application/octet-stream\r\n\r\n");
        byte[] head = sb.toString().getBytes("utf-8");
        // 获得输出流
        OutputStream out = new DataOutputStream(con.getOutputStream());
        // 输出表头
        out.write(head);
        // 文件正文部分
        // 把文件已流文件的方式 推入到url中
        DataInputStream in = new DataInputStream(new FileInputStream(file));
        int bytes = 0;
        byte[] bufferOut = new byte[1024];
        while ((bytes = in.read(bufferOut)) != -1) {
            out.write(bufferOut, 0, bytes);
        }
        in.close();
        // 结尾部分
        byte[] foot = ("\r\n--" + BOUNDARY + "--\r\n").getBytes("utf-8");// 定义最后数据分隔线
        out.write(foot);
        out.flush();
        out.close();
        StringBuffer buffer = new StringBuffer();
        BufferedReader reader = null;
        try {
            // 定义BufferedReader输入流来读取URL的响应
            reader = new BufferedReader(new InputStreamReader(con.getInputStream()));
            String line = null;
            while ((line = reader.readLine()) != null) {
                buffer.append(line);
            }
            if (result == null) {
                result = buffer.toString();
            }
        } catch (IOException e) {
            System.out.println("发送POST请求出现异常！" + e);
            e.printStackTrace();
            throw new IOException("数据读取异常");
        } finally {
            if (reader != null) {
                reader.close();
            }
        }
        return result;
    }
//
//    @Test
//    public void testDownload() throws Exception {
//        String mediaId = "Yswu8qDGjf6jUCpD-yAnPTsBWEtaYU6GsNNFTe4h4Lc";
//        File file = CFWxService.getInstance(3).downloadMedia(mediaId);
//        System.out.println(file.getName());
//    }

    @Test
    public void testGetWx() {
        WxMpUser wxMpUser = shuidiWxService.getWxUserByOpenId("oFP_8wsWtV8xIqCksdodH09CQfqA", 3);
        System.out.println(wxMpUser);


        wxMpUser = shuidiWxService.getWxUserByOpenId("oZtSx01bh78Nlpq9Y8IY5vclJK6Y", 116);
        System.out.println(wxMpUser);
    }

    @Test
    public void testGet() throws Exception {
        ShuidiWxService shuidiWxService = ApplicationContextUtil.getBean(ShuidiWxService.class);
        WxMpService wxService = shuidiWxService.getWxService(116);
        //WxMpMaterialFileBatchGetResult image1 = wxService.materialFileBatchGet("image", 1, 20);
        //WxMpMaterialCountResult wxMpMaterialCountResult = wxService.materialCount();
        WxMpMaterialFileBatchGetResult image = wxService.materialFileBatchGet("image", 1, 2000);
        log.info("image:{}", image);
        //for (int i = 0; i <= 1000; i+=20) {
        //    WxMpMaterialFileBatchGetResult image = wxService.materialFileBatchGet("image", i, 20);
        //    log.info("image:{}", image);
        //}
    }


}
