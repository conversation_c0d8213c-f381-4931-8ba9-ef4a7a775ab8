package com.shuidihuzhu.cfadmin;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidi.weixin.common.exception.WxErrorException;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.admin.constant.RiskControlWordConsts;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.ugc.model.domain.risk.RiskControlWordDO;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordCheckContext;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordResult;
import com.shuidihuzhu.cf.delegate.other.IOtherDelegate;
import com.shuidihuzhu.cf.delegate.other.IWeiXinDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.delegate.ugc.IUgcDelegate;
import com.shuidihuzhu.cf.domain.CaseRaiseRiskDO;
import com.shuidihuzhu.cf.domain.CfCaseRiskDO;
import com.shuidihuzhu.cf.domain.dedicated.CfUserVolunteerRelationDO;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CfOperatingRecordEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveIdcardVerifyStatusEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.risk.CfCaseRiskTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.risk.CfCaseRiskVerifiedEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.risk.StatRiskHandleStatusEnum;
import com.shuidihuzhu.cf.enums.risk.UgcTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.miniprogram.CfCommentDynamic;
import com.shuidihuzhu.cf.model.miniprogram.CfTopicShareCommentCount;
import com.shuidihuzhu.cf.model.risk.verify.RiskUgcVerifyModel;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.notice.urgeraise.UrgeRaiseMsgType;
import com.shuidihuzhu.client.cf.growthtool.model.CfVolunteerMaterialDO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.model.template.wx.WxMessageTemplate;
import com.shuidihuzhu.msg.model.WxRecordMetaTemplate;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @Auther: zgq
 * @Date: 2019-07-05 13:33
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class) // 指定我们SpringBoot工程的Application启动类
@EnableAutoConfiguration(exclude = {
        DataSourceAutoConfiguration.class
})
@WebAppConfiguration
@Slf4j
public class DelegateTwoTest {
    static {
        System.setProperty("spring.cloud.consul.host", "127.0.0.1");
    }
    @Resource
    private IRiskDelegate riskDelegate;

    @Resource
    private IUgcDelegate ugcDelegate;

    @Resource
    private IOtherDelegate otherDelegate;

    @Resource
    private IWeiXinDelegate weiXinDelegate;


    //@Autowired
//    TestRiskDelegate testRiskDelegate;

    @Test
    public void testIUgcDelegate() {
        CfTopicShareCommentCount cfTopicShareCommentCount =
                new CfTopicShareCommentCount(11112, 0, 0, 0);
        int m = ugcDelegate.insertCfTopicShareCommentCount(cfTopicShareCommentCount);
//        int m1 = testUgcDelegate.insertCfTopicShareCommentCount(cfTopicShareCommentCount);
//        if(m != m1){
//            log.error("insertCfTopicShareCommentCount失败");
//        }

        List<CfTopicShareCommentCount> list  = ugcDelegate.listByTopicIds(Lists.newArrayList(225,226));
        //List<CfTopicShareCommentCount> list1 = testUgcDelegate.listByTopicIds(Lists.newArrayList(225,226));

        CrowdfundingComment crowdfundingComment = ugcDelegate.getByIdNoCareDeleted(1L);
//        CrowdfundingComment crowdfundingComment1 = testUgcDelegate.getByIdNoCareDeleted(1);


        ugcDelegate.removeCrowdfundingCommentById(1000L);
        //testUgcDelegate.removeCrowdfundingCommentById(10000);


        CfCommentDynamic cfCommentDynamic = ugcDelegate.selectCfCommentDynamicByCommentId(412L);
//        CfCommentDynamic cfCommentDynamic1 = testUgcDelegate.selectCfCommentDynamicByCommentId(412L);


        List<CfCommentDynamic> cfCommentDynamics = ugcDelegate.listByIdsAndStatus(Lists.newArrayList(225),0);
//        List<CfCommentDynamic> cfCommentDynamics1 = testUgcDelegate.listByIdsAndStatus(Lists.newArrayList(225),0);


        List<CfCommentDynamic> cfCommentDynamics2 = ugcDelegate.listByCommentIds(Lists.newArrayList(412L));
//        List<CfCommentDynamic> cfCommentDynamics3 = testUgcDelegate.listByCommentIds(Lists.newArrayList(412L));

        List<CrowdfundingComment> list2 = ugcDelegate.getListByIdNoCareDeleted(Lists.newArrayList(414L));
//        List<CrowdfundingComment> list3 = testUgcDelegate.getListByIdNoCareDeleted(Lists.newArrayList(414));

        List<CrowdfundingComment> crowdfundingComments = ugcDelegate.getByPageNoCareCommentIdList(Lists.newArrayList(27311L),0,10,2);
//        List<CrowdfundingComment> crowdfundingComments1 = testUgcDelegate.getByPageNoCareCommentIdList(Lists.newArrayList(27311),0,10,2);
    }

    @Test
    public void testIWeiXinDelegate() {
        String s = weiXinDelegate.generateQrcode("cf_volunteer_e3vhfr464");
//        String s1 = testWeiXinDelegate.generateQrcode("cf_volunteer_e3vhfr464");
//        if(!s.equals(s1)){
//            log.error("generateQrcode接口错误");
//        }
        OpResult opResult = weiXinDelegate.sendByEmail(Lists.newArrayList("zhangguoqing"),"测试");
//        OpResult opResult1 = testWeiXinDelegate.sendByEmail(Lists.newArrayList("zhangguoqing"),"测试");

        OpResult opResult2 = weiXinDelegate.sendByGroup("cf-urgent-report","测试");
//        OpResult opResult3 = testWeiXinDelegate.sendByGroup("cf-urgent-report","测试");

//        testWeiXinDelegate.sendMsg4allDonor(10,1);

        Map<String, Object> map = Maps.newHashMap();
        map.put(WxMessageTemplate.KEY_FIRST, new WxRecordMetaTemplate("Hi" + "暑假到了。你准备无所事事混日子，还是已有计划趁着暑期锻炼自己？如果是后者，快来看看水滴筹新一期的活动吧~\n", "#0092FF"));
        map.put(WxMessageTemplate.KEY_KEYWORD1, new WxRecordMetaTemplate("「水滴筹暑期实践赞助活动」已启动", "#FF0000"));
        map.put(WxMessageTemplate.KEY_KEYWORD2, new WxRecordMetaTemplate("无论你是一个人，一个团队，还是一个社团，只要有想法，都可以参与活动\n", "#000000"));
        map.put(WxMessageTemplate.KEY_REMARK, new WxRecordMetaTemplate("➜ 点此立即参与 👑", "#0092FF"));
//        String m1 = "";
//        try {
//            m1 = testWeiXinDelegate.sendTemplateMessage("oFP_8wsHchw5C9ZkwmZDwjgVzGx0","XkevieQhze9nkhrTg1XwDwePZvyltcXwZz10gCnShmA","http://mp.weixin.qq.com/s/hH1sRZucQgRfZQSkOYQQAA",map,3,2,215);
//        } catch (WxErrorException e) {
//            e.printStackTrace();
//        }
    }
    @Test
    public void testNullResult(){
        RiskWordCheckContext ctx = RiskWordCheckContext.builder()
                .content("3242432")
                .categorys(RiskControlWordConsts.RISK_CONTROL_CATEGORY)
                .isCheckAll(true)
                .build();
        OpResult<RiskWordResult> opResult = riskDelegate.isHit(ctx);
        IdcardVerifyWhiteList idcardVerifyWhiteList1 = riskDelegate.getByNameAndIdcard("eq子","ZnRyL'onjUOJDgbewqQISZ/DnglIwpA=");
        RiskUgcVerifyModel riskUgcVerifyModel =
                new RiskUgcVerifyModel(12, UgcTypeEnum.ORDER, 1233, "运营操作添加");
        OpResult<RiskUgcVerifyModel> opResult2 = riskDelegate.addVerify(riskUgcVerifyModel);
//        OpResult<RiskControlWordCategoryDO> sss = riskDelegate.addRiskControlWordCategoryDO(132131,"测试赛3213");
//        OpResult<RiskWordSaveResult> opResult100 = riskDelegate.addAssemble(1,"32141");
//
//
//        List<CfTopicShareCommentCount> list  = testUgcDelegate.listByTopicIds(Lists.newArrayList(999999));
//        List<CfTopicShareCommentCount> list22  = ugcDelegate.listByTopicIds(Lists.newArrayList(999999));
//
//
//        CrowdfundingComment crowdfundingComment1 = testUgcDelegate.getByIdNoCareDeleted(132131312);
//        CrowdfundingComment crowdfundingComment20 = ugcDelegate.getByIdNoCareDeleted(132131312);
//
//        CfCommentDynamic cfCommentDynamic1 = testUgcDelegate.selectCfCommentDynamicByCommentId(3213122222223L);
//        List<CfCommentDynamic> cfCommentDynamics1 = testUgcDelegate.listByIdsAndStatus(Lists.newArrayList(99999999),10);
//        List<CfCommentDynamic> cfCommentDynamics3 = testUgcDelegate.listByCommentIds(Lists.newArrayList(41299999L));
//        List<CrowdfundingComment> list3 = testUgcDelegate.getListByIdNoCareDeleted(Lists.newArrayList(41499999));
//        List<CrowdfundingComment> crowdfundingComments1 = testUgcDelegate.getByPageNoCareCommentIdList(Lists.newArrayList(273999911),0,10,12);


//        CfOperatingRecord cfOperatingRecord1 = testRiskDelegate.before("999999",4324,
//                "张3424324三", CfOperatingRecordEnum.Type.PASS_HOSPITAL_AUDIT,
//                CfOperatingRecordEnum.Role.OPERATOR, null);


//        Map<String, Object> map1 = testOtherDelegate.selectFromName(323225,"武警北342432京市总队医院(内分泌科)",1,10);
//        Map<String, Object> map = otherDelegate.selectFromName(35324432,"武警北京市43243242总队医院(内分泌科)",1,10);
//        String s11 = testWeiXinDelegate.generateQrcode("43242424");
//        CaseRaiseRiskDO caseRaiseRiskDO1 = testRiskDelegate.getByInfoUuid("b4432424a982-44051b99d0bb");
//        CfFaceIdLivingVerifyInfo cfFaceIdLivingVerifyInfo1 = testRiskDelegate.getCfFaceIdLivingVerifyInfoByInfoUuid("cd9432426e24");
//        CfErrorCode cfErrorCode1 = testRiskDelegate.verifyIdcard("fdsfs",
//                "12",
//                "二期32",
//                "12",4324
//                ,
//                UserRelTypeEnum.SELF,
//                3424);
//        CfFirsApproveMaterial cfFirsApproveMaterial1 = testRiskDelegate.getCfFirsApproveMaterialByInfoId(222222222);
//        CfFirsApproveMaterial param = new CfFirsApproveMaterial();
//        param.setInfoId(1111111111);
//        List<CfFirsApproveMaterial> list5 = testRiskDelegate.getCfFirsApproveMaterialListByParam(param);
//        Map<Integer, CfFirsApproveMaterial> map22 = testRiskDelegate.getMapByInfoIds(Lists.newArrayList(2222222));
//        CfSuspectedCaseInfoModel cfSuspectedCaseInfoModel = new CfSuspectedCaseInfoModel();
//        cfSuspectedCaseInfoModel.setInfoUuid("3424243424");
//        List<CfSuspectedCaseInfo> list7 = testRiskDelegate.getCfSuspectedCaseInfoListBySerch(cfSuspectedCaseInfoModel);
//        List<CfSuspectedCaseInfo> list9 = testRiskDelegate.getByLikeIdNumber("432+942342");
//        List<CrowdfundingReport> list11 = testRiskDelegate.getCrowdfundingReportListByInfoId(999999);
//        List<RiskControlWordDO>  list1 = testRiskDelegate.searchByType(12222,222224,"测22试烤肉");
//        boolean b1 = testRiskDelegate.isSafeSingle(UgcTypeEnum.VERIFICATION,1111111,1111111);
//        List<RiskControlWordCategoryDO> list100 = testRiskDelegate.listRiskControlWordCategoryDOByType(999999);
//        List<RiskControlWordCategoryDO> list6 = testRiskDelegate.searchByCondition(********,"分类34242id=1");
//        CfUserVolunteerRelationDO cfUserVolunteerRelationDO1 = testOtherDelegate.getAccountUserAndVolunteerRelationByPhone("432432");
    }
    @Test
    public void testIOtherDelegatee() {
        List<CfTouFangSign> cfTouFangSigns = Lists.newArrayList();
        CfTouFangSign cfTouFangSign = new CfTouFangSign();
        cfTouFangSign.setMobile("***********");
        cfTouFangSign.setCryptoMobile("**************");
        cfTouFangSign.setReferer("自测");
        cfTouFangSign.setIsAuto(0);
        cfTouFangSign.setChannel("");
        cfTouFangSign.setSource("");
        cfTouFangSign.setMatch("");
        cfTouFangSign.setKeyword("");
        cfTouFangSign.setSemwp("");
        cfTouFangSign.setAccount("");
        cfTouFangSign.setKwid("");
        cfTouFangSign.setCreative("");
        cfTouFangSign.seteAdposition("");
        cfTouFangSign.setOpenId("");
        cfTouFangSign.setRelation("");
        cfTouFangSign.setPrimaryChannel("");
        cfTouFangSign.setHelp("");
        cfTouFangSign.setDisease("");
        cfTouFangSign.setClientIp(0);
        cfTouFangSigns.add(cfTouFangSign);
        int h = otherDelegate.insertCfTouFangSignList(cfTouFangSigns);
//        int h1 = testOtherDelegate.insertCfTouFangSignList(cfTouFangSigns);


        CfUserVolunteerRelationDO cfUserVolunteerRelationDO = otherDelegate.getAccountUserAndVolunteerRelationByPhone("pbA0b/YrprGQuDxoKGBqvQ==");
//        CfUserVolunteerRelationDO cfUserVolunteerRelationDO2 = testOtherDelegate.getAccountUserAndVolunteerRelationByPhone("pbA0b/YrprGQuDxoKGBqvQ==");

        CfVolunteerMaterialDO cfVolunteerMaterialDO = otherDelegate.getVolunteerMateri("e3vhfr464");
//        CfVolunteerMaterialDO cfVolunteerMaterialDO1 = testOtherDelegate.getVolunteerMateri("e3vhfr464");


        int s = otherDelegate.addCfVolunteerMaterial(cfVolunteerMaterialDO);
//        int s1 = testOtherDelegate.addCfVolunteerMaterial(cfVolunteerMaterialDO);

        int m = otherDelegate.updateCfVolunteerMaterial(cfVolunteerMaterialDO);
//        int m1 = testOtherDelegate.updateCfVolunteerMaterial(cfVolunteerMaterialDO);




        String str = "测试";
        Response response = otherDelegate.insertCfTransformArticle(str,str,str);
//        Response response1 = testOtherDelegate.insertCfTransformArticle(str,str,str);
//
        Response response4 = otherDelegate.listCfTransformArticleByAnchor(51,10);
//        Response response5 = testOtherDelegate.listCfTransformArticleByAnchor(********,10);
//
        Response response2 = otherDelegate.deleteCfTransformArticle(51);
//        Response response3 = testOtherDelegate.deleteCfTransformArticle(51);
//
//
//
//
        Response response6 = otherDelegate.listCfTransformArticleByPage(1,10);
//        Response response7 = testOtherDelegate.listCfTransformArticleByPage(1,10);
//
//
        Response response8 = otherDelegate.listByPageAndTitleKey(1,10,"");
//        Response response9 = testOtherDelegate.listByPageAndTitleKey(1,10,"");
//
        boolean bool = otherDelegate.checkCouldSend(23L,UrgeRaiseMsgType.RE_PRE_APPROVE);
//          boolean bool1 = testOtherDelegate.checkCouldSend(23L,UrgeRaiseMsgType.RE_PRE_APPROVE);
//
//
        Map<String, Object> map = otherDelegate.selectFromName(35,"武警北京市总队医院(内分泌科)",1,10);
//        Map<String, Object> map1 = testOtherDelegate.selectFromName(35,"武警北京市总队医院(内分泌科)",1,10);
//        int m =10;
    }
    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;

    @Test
    public void testIRiskDelegate() {
        RiskWordCheckContext ctx = RiskWordCheckContext.builder()
                .content("3242432")
                .categorys(RiskControlWordConsts.RISK_CONTROL_CATEGORY)
                .isCheckAll(true)
                .build();
 //       OpResult<RiskWordResult> opResult111 = riskDelegate.isHit(ctx);

 //       OpResult<CfCaseRiskDO> opResult = riskDelegate.getByInfoUuid("cd9b4f05-5841-4940-a306-eb9eee7c6e24",CfCaseRiskTypeEnum.CASE_INFO_RISK);
//        OpResult<CfCaseRiskDO> opResult1 = testRiskDelegate.getByInfoUuid("cd9b4f05-5841-4940-a306-eb9eee7c6e24",CfCaseRiskTypeEnum.CASE_INFO_RISK);
        OpResult<CfCaseRiskDO> opResult2 = riskDelegate.handleInfoRisk("07b0597c-c8dc-4b80-87d7-c9607f38bab0");
//        OpResult<CfCaseRiskDO> opResult3 = testRiskDelegate.handleInfoRisk("07b0597c-c8dc-4b80-87d7-c9607f38bab0");
//
        OpResult<CfCaseRiskDO> opResult4 = riskDelegate.handleDrawCaskRisk("07b0597c-c8dc-4b80-87d7-c9607f38bab0");
//        OpResult<CfCaseRiskDO> opResult5 = testRiskDelegate.handleDrawCaskRisk("b4d48b38-e4d2-4a22-a982-44051b99d0bb");

        OpResult<CfCaseRiskDO> opResult6 = riskDelegate.onCaseEnd("07b0597c-c8dc-4b80-87d7-c9607f38bab0");
//        OpResult<CfCaseRiskDO> opResult7 = testRiskDelegate.onCaseEnd("b4d48b38-e4d2-4a22-a982-44051b99d0bb");
//

        OpResult opResult8 = riskDelegate.onCaseFirstApproveHasDelay72("07b0597c-c8dc-4b80-87d7-c9607f38bab0");
//        OpResult opResult9 = testRiskDelegate.onCaseFirstApproveHasDelay72("b4d48b38-e4d2-4a22-a982-44051b99d0bb");

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo("b4d48b38-e4d2-4a22-a982-44051b99d0bb");
        OpResult opResult10 = riskDelegate.onCaseInfoPassed("07b0597c-c8dc-4b80-87d7-c9607f38bab0",crowdfundingInfo);
        //OpResult opResult11 = testRiskDelegate.onCaseInfoPassed("b4d48b38-e4d2-4a22-a982-44051b99d0bb",crowdfundingInfo);


        OpResult<CfCaseRiskDO> res = riskDelegate.getByInfoUuid("cd9b4f05-5841-4940-a306-eb9eee7c6e24", CfCaseRiskTypeEnum.parse(1));
// ？       OpResult<CfCaseRiskDO> opResult12 = riskDelegate.doVerify(res.getData(),CfCaseRiskTypeEnum.parse(1),"all");
//        OpResult<CfCaseRiskDO> opResult13 = testRiskDelegate.doVerify(res.getData(),CfCaseRiskTypeEnum.parse(1),"all");




        riskDelegate.onRemark(123,1);
//        testRiskDelegate.onRemark(123,1);




        List<CfCaseRiskDO> list = riskDelegate.listByConditionAll(CfCaseRiskVerifiedEnum.VERIFIED.getVerified(),
                false,
                1,
                StatRiskHandleStatusEnum.parse(1));
//        List<CfCaseRiskDO> list1 = testRiskDelegate.listByConditionAll(CfCaseRiskVerifiedEnum.VERIFIED.getVerified(),
//                false,
//                1,
//                StatRiskHandleStatusEnum.parse(1));
//
//
//
        CaseRaiseRiskDO caseRaiseRiskDO = riskDelegate.getByInfoUuid("b4d48b38-e4d2-4a22-a982-44051b99d0bb");

//
//
        OpResult<Integer> opResult14 = riskDelegate.saveRaiseRisk(caseRaiseRiskDO);
//        OpResult<Integer> opResult15 = testRiskDelegate.saveRaiseRisk(caseRaiseRiskDO);

        OpResult opResult16 = riskDelegate.handleRiskPassed("e304ffc1-bf66-40b3-b488-a58dd601f86f");
//        OpResult opResult17 = testRiskDelegate.handleRiskPassed("e304ffc1-bf66-40b3-b488-a58dd601f86f");
//
//
        CfFaceIdLivingVerifyInfo cfFaceIdLivingVerifyInfo = riskDelegate.getCfFaceIdLivingVerifyInfoByInfoUuid("cd9b4f05-5841-4940-a306-eb9eee7c6e24");
//        CfFaceIdLivingVerifyInfo cfFaceIdLivingVerifyInfo1 = testRiskDelegate.getCfFaceIdLivingVerifyInfoByInfoUuid("cd9b4f05-5841-4940-a306-eb9eee7c6e24");
//
//
        IdcardVerifyWhiteList idcardVerifyWhiteList = riskDelegate.getByNameAndIdcard("两片叶子","ZnRyLonjUOJDgbWd1g1Y5u5CNO1RNcQISZ/DnglIwpA=");
//        IdcardVerifyWhiteList idcardVerifyWhiteList1 = testRiskDelegate.getByNameAndIdcard("两片叶子","ZnRyLonjUOJDgbWd1g1Y5u5CNO1RNcQISZ/DnglIwpA=");
//
//
        int i = riskDelegate.addIdcardVerifyWhiteList("两片叶子","ZnRyLonjUOJDgbWd1g1Y5u5CNO1RNcQISZ/DnglIwpA=");
//        int i1 = testRiskDelegate.addIdcardVerifyWhiteList("两片叶子","ZnRyLonjUOJDgbWd1g1Y5u5CNO1RNcQISZ/DnglIwpA=");
//
//
//
        int i2 = riskDelegate.deleteFirstApproveWhiteIdById(idcardVerifyWhiteList.getId());
//        int i3 = testRiskDelegate.deleteFirstApproveWhiteIdById(idcardVerifyWhiteList.getId());
//

        PageInfo<IdcardVerifyWhiteList>  pageInfo = riskDelegate.selectAllWhiteIdCardList(1,10, "", "");
//        PageInfo<IdcardVerifyWhiteList>  pageInfo1 = testRiskDelegate.selectAllWhiteIdCardList(1,10);


        CfFirsApproveMaterial cfFirsApproveMaterial = riskDelegate.getCfFirsApproveMaterialByInfoId(972153);
//        CfFirsApproveMaterial cfFirsApproveMaterial1 = testRiskDelegate.getCfFirsApproveMaterialByInfoId(972153);
//
        CfErrorCode cfErrorCode = riskDelegate.verifyIdcard(cfFirsApproveMaterial.getSelfRealName(),
                "12",
                cfFirsApproveMaterial.getPatientRealName(),
                "12",
                cfFirsApproveMaterial.getPatientIdType(),
                UserRelTypeEnum.SELF,
                cfFirsApproveMaterial.getUserId());





//        int i5 = testRiskDelegate.updateStatus(13,FirstApproveIdcardVerifyStatusEnum.MATCH);
//
//
//
        CfFirsApproveMaterial param = new CfFirsApproveMaterial();
        param.setInfoId(972153);
        List<CfFirsApproveMaterial> list4 = riskDelegate.getCfFirsApproveMaterialListByParam(param);
//        List<CfFirsApproveMaterial> list5 = testRiskDelegate.getCfFirsApproveMaterialListByParam(param);
//
//
//
        Map<Integer, CfFirsApproveMaterial> map = riskDelegate.getMapByInfoIds(Lists.newArrayList(972153));
//        Map<Integer, CfFirsApproveMaterial> map1 = testRiskDelegate.getMapByInfoIds(Lists.newArrayList(972153));
//
//
//
        int i6 = riskDelegate.updateRejectTypeByInfoId(972153,1,"测试");
//        int i7 = testRiskDelegate.updateRejectTypeByInfoId(972153,1,"测试");



        String s = riskDelegate.getCanEditMsg(1,false);
//        String s1 = testRiskDelegate.getCanEditMsg(1,false);


        CfSuspectedCaseInfoModel cfSuspectedCaseInfoModel = new CfSuspectedCaseInfoModel();
        cfSuspectedCaseInfoModel.setLimit(1);
        cfSuspectedCaseInfoModel.setSize(10);
        List<CfSuspectedCaseInfo> list6 = riskDelegate.getCfSuspectedCaseInfoListBySerch(cfSuspectedCaseInfoModel);
//        List<CfSuspectedCaseInfo> list7 = testRiskDelegate.getCfSuspectedCaseInfoListBySerch(cfSuspectedCaseInfoModel);
//
//
//
        int i8 = riskDelegate.addCfSuspectedCaseInfo(list6.get(0));
//        int i9 = testRiskDelegate.addCfSuspectedCaseInfo(list6.get(0));


        List<CfSuspectedCaseInfo> list8 = riskDelegate.getByLikeIdNumber("n6txDAYbw6+9CB68ngfThpA765/7XDAQ//TdpYs3V4U=");
//        List<CfSuspectedCaseInfo> list9 = testRiskDelegate.getByLikeIdNumber("n6txDAYbw6+9CB68ngfThpA765/7XDAQ//TdpYs3V4U=");


        int m = riskDelegate.updateCfSuspectedCaseInfo(list6.get(0));
//        int m1 = testRiskDelegate.updateCfSuspectedCaseInfo(list6.get(0));


        int f = riskDelegate.deleteCfSuspectedCaseInfo(6);
//        int f1 = testRiskDelegate.deleteCfSuspectedCaseInfo(6);
//
//
        List<CrowdfundingReport> list10 = riskDelegate.getCrowdfundingReportListByInfoId(79);
//        List<CrowdfundingReport> list11 = testRiskDelegate.getCrowdfundingReportListByInfoId(79);
//
//






        CfOperatingRecord cfOperatingRecord = riskDelegate.before(crowdfundingInfo.getInfoId(), 3213,
                "张三", CfOperatingRecordEnum.Type.PASS_HOSPITAL_AUDIT,
                CfOperatingRecordEnum.Role.OPERATOR, null);
//        CfOperatingRecord cfOperatingRecord1 = testRiskDelegate.before(crowdfundingInfo.getInfoId(),4324,
//                "张三", CfOperatingRecordEnum.Type.PASS_HOSPITAL_AUDIT,
//                CfOperatingRecordEnum.Role.OPERATOR, null);
//
//        riskDelegate.after(cfOperatingRecord,null);



        int i22 = riskDelegate.updateValid(1,1835111);
//        int i1 = testRiskDelegate.updateValid(1,1835111);



        riskDelegate.deleteCaseVerificationCache("21432");

//        RiskWordCheckContext ctx = RiskWordCheckContext.builder()
//                .content("3242432")
//                .categorys(RiskControlWordConsts.RISK_CONTROL_CATEGORY)
//                .isCheckAll(true)
//                .build();
//        OpResult<RiskWordResult> opResult = riskDelegate.isHit(ctx);
//        OpResult<RiskWordResult> opResult1 = testRiskDelegate.isHit(ctx);



        List<RiskControlWordDO>  list32112 = riskDelegate.searchByTypeAll(1,24,"测试烤肉");
//        List<RiskControlWordDO>  list1 = testRiskDelegate.searchByType(1,24,"测试烤肉");
//
        RiskUgcVerifyModel riskUgcVerifyModel =
                new RiskUgcVerifyModel(12, UgcTypeEnum.ORDER, 1233, "运营操作添加");
        OpResult<RiskUgcVerifyModel> opResult211 = riskDelegate.addVerify(riskUgcVerifyModel);
//        OpResult<RiskUgcVerifyModel> opResult3 = testRiskDelegate.addVerify(riskUgcVerifyModel);


//？
        OpResult opResult422 = riskDelegate.deleteVerify(974617,UgcTypeEnum.VERIFICATION,1902922);
//        OpResult opResult5 = testRiskDelegate.deleteVerify(974617,UgcTypeEnum.VERIFICATION,1902922);



        OpResult opResult622 = riskDelegate.saveCache(UgcTypeEnum.VERIFICATION,1111111,1111111,false);
//        OpResult opResult7 = testRiskDelegate.saveCache(UgcTypeEnum.VERIFICATION,1111111,1111111,false);
//
//
//
        boolean b = riskDelegate.isSafeSingle(UgcTypeEnum.VERIFICATION,1111111,1111111);
//        boolean b1 = testRiskDelegate.isSafeSingle(UgcTypeEnum.VERIFICATION,1111111,1111111);
//
    }

}
