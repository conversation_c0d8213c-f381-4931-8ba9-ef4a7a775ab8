package com.shuidihuzhu.cfadmin;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.admin.util.AdminListUtil;
import com.shuidihuzhu.cf.delegate.commonservice.ICommonServiceDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingOperationDelegate;
import com.shuidihuzhu.cf.domain.RemarkDO;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CfOperatingRecordEnum;
import com.shuidihuzhu.cf.finance.client.feign.CfFinancePauseFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.enums.CfDrawCashPauseRecordEnum;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> Ahrievil
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class FeignTest {
    @Resource
    private CfFinancePauseFeignClient cfFinancePauseFeignClient;
    @Resource
    private ICrowdfundingOperationDelegate crowdfundingOperationDelegate;
    @Resource
    private ICommonServiceDelegate commonServiceDelegate;
    static {
        System.setProperty("spring.cloud.consul.host", "consul.zelda.shuiditech.com:80");
    }
    @Test
    public void testRecover() {
        //改为 cf-finance-api 的feign实现
        FeignResponse feignResponse = cfFinancePauseFeignClient.recoverBySourceType("6b3711d4-7244-46a4-95ac-cf1c8af8e48d\"",
                78,
                CfDrawCashPauseRecordEnum.PauseSourceTypeEnum.SYSTEM_RISK_CASE.getCode(),
                CfDrawCashPauseRecordEnum.RecordStatusEnum.RECOVER.getCode(),
                CfOperatingRecordEnum.Role.SYSTEM.getCode(), 0, "userAccount.getName()",
                "sea后台撤销人工打款-案例审核页");
        log.info("sea后台撤销人工打款-案例审核页\tinfoId:{}\tfeignResponse:{}", 78, JSON.toJSON(feignResponse));
    }

    @Test
    public void  feignlog(){
        List<RemarkDO> response = crowdfundingOperationDelegate.listRemarkDOByCaseId(972089);
    }
    @Test
    public void  testfeign(){
//        RemarkDO remarkDO = RemarkDO.builder()
//                .caseId(1234)
//                .remarkType(101)
//                .operatorId(103)
//                .build();
//        crowdfundingOperationDelegate.saveRemarkDO(remarkDO);
//        crowdfundingOperationDelegate.listRemarkDOByCaseId(1234);
//        crowdfundingOperationDelegate.listByCaseIdAndRemarkType(1234, 101);
//        crowdfundingOperationDelegate.listByCaseIdAndRemarkTypes(1234, Lists.newArrayList(101));
//        crowdfundingOperationDelegate.getLastByCaseIdAndRemarkTypes(1234, Lists.newArrayList(101));
//        crowdfundingOperationDelegate.getRemarkDOById(1);
//        crowdfundingOperationDelegate.convertVO(remarkDO);
//
//        TimeLineModel timeLineModel = TimeLineModel.create(973179).type(TimeLineOperateType.COMMON).build();
//        crowdfundingOperationDelegate.saveTimeLineModel(timeLineModel);
//        crowdfundingOperationDelegate.listTimeLineModelByCaseId(973179);
//        crowdfundingOperationDelegate.getTimeLineModelById(1);
//
//        CrowdfundingBlackList crowdfundingBlackList = new CrowdfundingBlackList();
//        crowdfundingBlackList.setUserId(6989);
//        crowdfundingBlackList.setContent("thread test");
//        crowdfundingBlackList.setLimitRange(2);
//        crowdfundingBlackList.setLimitType(1);
//        crowdfundingBlackList.setLimitPart(0);
//        crowdfundingBlackList.setMode(1);
//        crowdfundingBlackList.setContentValid(0);
//        cfBlackListDelegate.selectByUserId(6989);
//        cfBlackListDelegate.selectAllUserId(1, 10);
//        cfBlackListDelegate.selectAllUserIdInCache(1, 10);
//        cfBlackListDelegate.insert(crowdfundingBlackList);
//        crowdfundingBlackList.setId(1);
//        cfBlackListDelegate.update(crowdfundingBlackList);
//        cfBlackListDelegate.deleteById(2);
//        cfBlackListDelegate.deleteByUserId(1);
//
//        commonServiceDelegate.queryByKey("test", true);
//        commonServiceDelegate.queryByKeyWithLocalCache("test");
//        commonServiceDelegate.queryValueByKey("test");
//        commonServiceDelegate.saveRedisKv("test", "111");
//        commonServiceDelegate.updateRedisKv("test", "222");
//        commonServiceDelegate.queryIntByKey("test", true);
//
//        commonServiceDelegate.getGrayTestBySelfTag("test", 0, "");
//        commonServiceDelegate.getGrayTestBySelfTagWithCode("test", 0, "");
//        commonServiceDelegate.getGrayTestByOpenId("test", "",0);
//        commonServiceDelegate.getGrayTestByOpenIdWithCode("test", "",0);
//        commonServiceDelegate.delete("test");
//        commonServiceDelegate.add(1, "mydonation_sdb1_0920", "50,50", "我的捐款测试水滴保活动", true);
//        commonServiceDelegate.updateCasePercentage("mydonation_sdb1_0920", "50,50");
//        commonServiceDelegate.updateIfRecordResult("mydonation_sdb1_0920", true);
//        commonServiceDelegate.getByPage(1,10);
//        commonServiceDelegate.getByCode("mydonation_sdb1_0920");
//        commonServiceDelegate.total();
//        commonServiceDelegate.getLikeCode("mydonation_sdb1_0920");
//
//        BankCardVerifyRecord bankCardVerifyRecord = new BankCardVerifyRecord();
//        bankCardVerifyRecord.setCrowdfundingId(523);
//        bankCardVerifyRecord.setTradeNo("");
//        bankCardVerifyRecord.setThirdTradeNo("");
//        bankCardVerifyRecord.setCryptoHolderName("把本末");
//        bankCardVerifyRecord.setIdentityType(UserIdentityType.others);
//        bankCardVerifyRecord.setCryptoIdCard("37142519901128864x");
//        bankCardVerifyRecord.setCryptoBankCard("2222222222222222300");
//        bankCardVerifyRecord.setCryptoMobile("***********");
//        bankCardVerifyRecord.setVerifyStatus(BankCardVerifyStatus.pending);
//        bankCardVerifyRecord.setVerifyMessage("");
//        bankCardVerifyRecord.setVerifyMessage2("");
//        bankCardVerifyRecord.setDateCreated(new Timestamp(System.currentTimeMillis()));
//        bankCardVerifyRecord.setLastModified(new Timestamp(System.currentTimeMillis()));
////        INSERT INTO `shuidi_crowdfunding`.`crowdfunding_bank_card_verify_record`(`id`, `crowdfunding_id`, `trade_no`, `third_trade_no`, `crypto_holder_name`, `identity_type`, `crypto_id_card`, `crypto_bank_card`, `crypto_mobile`, `verify_status`, `verify_message`, `verify_message2`, `date_created`, `last_modified`) VALUES
//// (1, 523, '', '', '把本末', 0, '37142519901128864x', '2222222222222222300', NULL, 'pending', NULL, NULL, '2016-12-12 18:09:53.693601', '2016-12-12 18:09:53.693601');
//
//        commonServiceDelegate.add(bankCardVerifyRecord);
//        commonServiceDelegate.verify("把本末", UserIdentityType.others, "37142519901128864x","2222222222222222300",  0);
//        commonServiceDelegate.verify("把本末", UserIdentityType.others, "37142519901128864x","2222222222222222300",  523, 0, true);
//        commonServiceDelegate.getStatusByThreeElements("把本末","37142519901128864x","2222222222222222300");
//        commonServiceDelegate.getResultByThreeElements("把本末","37142519901128864x","2222222222222222300");

//        log.error(this.getClass()+"pin {}",commonServiceDelegate.pin(973430, VisitConfigSourceEnum.DEFAULT, Lists.newArrayList(VisitConfigLogActionInfoEnum.UNLOCK_SHARE,VisitConfigLogActionInfoEnum.CHANGE_TITLE), 0));
//        commonServiceDelegate.listOperatorLog("2732bda9-ba3c-4950-bb53-22bcb39f2f67");
//        commonServiceDelegate.listByCondition("d8c811a7-d811-4e05-bfb8-8d43007e54be", Lists.newArrayList(VisitConfigLogActionTypeEnum.OPERATOR),Lists.newArrayList(VisitConfigSourceEnum.DEFAULT));
//        commonServiceDelegate.list(null);
        Response response = NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        OpResult opResult = AdminListUtil.getModelFromResponse(response,OpResult.class);
        log.error(this.getClass()+"pin {}",opResult);

    }
}
