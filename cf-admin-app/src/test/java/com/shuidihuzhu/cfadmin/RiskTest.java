package com.shuidihuzhu.cfadmin;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.delegate.saas.AdminOrganization;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.OrganizationClientV1;
import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.biz.admin.UserCommentBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderFlowRecordBiz;
import com.shuidihuzhu.cf.dao.admin.AdminWorkOrderDao;
import com.shuidihuzhu.cf.dao.crowdfunding.CfCaseTagDao;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.enums.admin.UserCommentSourceEnum;
import com.shuidihuzhu.cf.enums.shutdown.BizTypeEnum;
import com.shuidihuzhu.cf.enums.shutdown.ExecuteStatusEnum;
import com.shuidihuzhu.cf.event.DrawCashEvent;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.admin.UserComment;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderFlowRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CfCaseTag;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.crowdfunding.CfRiskService;
import com.shuidihuzhu.cf.service.shutdown.DrawCashPublisher;
import com.shuidihuzhu.client.baseservice.msg.v2.MsgModelClient;
import com.shuidihuzhu.client.cf.risk.client.CfRiskClient;
import com.shuidihuzhu.client.cf.risk.client.CfRiskPlatformClient;
import com.shuidihuzhu.client.cf.risk.model.enums.UserOperationEnum;
import com.shuidihuzhu.client.cf.risk.model.result.RiskRpcResponse;
import com.shuidihuzhu.client.param.RiskOperateLimitParam;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.data.analytics.javasdk.core.HawkeyeServer;
import com.shuidihuzhu.data.analytics.javasdk.model.hawkeye.ExperimentVo;
import com.shuidihuzhu.msg.enums.RecordConstant;
import com.shuidihuzhu.msg.model.MsgModel;
import com.shuidihuzhu.msg.vo.MsgResponse;
import com.shuidihuzhu.msg.vo.Response;
import com.shuidihuzhu.msg.vo.rpc.MsgTemplateVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.joda.time.DateTime;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @time 2018/11/5 下午2:42
 * @desc
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class RiskTest {
    static {
        System.setProperty("spring.cloud.consul.host", "consul.zelda.shuiditech.com:80");
    }

    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;
    @Autowired
    CfRiskPlatformClient cfRiskPlatformClient;
    @Autowired
    CfRiskClient cfRiskClient;
    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;
    @Autowired
    private DrawCashPublisher drawCashPublisher;
    @Autowired
    private CfRiskService cfRiskService;
    @Autowired
    private HawkeyeServer hawkeye;
    @Autowired
    private AdminWorkOrderDao adminWorkOrderDao;
    @Autowired
    private UserCommentBiz userCommentBiz;
    @Resource
    private OrganizationClientV1 organizationClientV1;

    private static String first_data_210 = "firstData210";
    private static String actual_amount_210 = "actualAmount";
    private static String pay_detail_210 = "payDetail";
    private static String remark_date_210 = "remarkDate210";
    private static String pay_time_210 = "payTime";

    @Autowired
    private MsgModelClient msgModelClient;

    @Test
    public void selectSmsTemplage() {
        MsgResponse<List<MsgModel>> response = msgModelClient.getOthersMsgModel(1341, "message1341");

        Response response1 = msgModelClient.getTemplateList(response.getData().get(0).getId(), RecordConstant.TEMPLATE_SMS);

        MsgTemplateVo vo = (MsgTemplateVo) response1.getData();

    }

    @Test
    public void addProgressTest() {
        String infoUuid = "b0c22df6c8b2e3d319f2fc0172d5e8a6";
        boolean enable = false;

        if (StringUtils.isEmpty(infoUuid)) {
            System.out.println("*********************案例信息为空!*********************");
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingDelegate.getCrowdfundingInfoByInfoId(infoUuid);
        if (null == crowdfundingInfo || crowdfundingInfo.getId() <= 0) {
            System.out.println("*********************案例信息为空!*********************");
        }

        Map<String, Boolean> limits = Maps.newHashMap();
        limits.put(String.valueOf(UserOperationEnum.PROPRESS.getCode()), enable);

        RiskOperateLimitParam limitParam = new RiskOperateLimitParam();
        limitParam.setCaseId(crowdfundingInfo.getId());
        limitParam.setLimits(limits);

        RiskRpcResponse result = cfRiskPlatformClient.addLimit(limitParam);

        if (null != result && 0 == result.getCode()) {

            System.out.println("*********************禁止用户发布动态操作成功*********************");
        } else {
            System.out.println("*********************禁止用户发布动态操作失败*********************");
        }
    }

    @Test
    public void timeTest() {
        String date = "";

        Timestamp start = null;
        Timestamp end = null;

        date = "2018-11-16";
        if (StringUtils.isBlank(date)) {
            Calendar cal = Calendar.getInstance();
            SimpleDateFormat timeFormat = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
            start = DateUtil.getPGDetailImeStampFromString(timeFormat.format(cal.getTime()));
            cal.add(Calendar.DAY_OF_MONTH, 1);
            end = DateUtil.getPGDetailImeStampFromString(timeFormat.format(cal.getTime()));
            date = DateUtil.getCurrentDateStr();

            System.out.println("******************************************");
            System.out.println("modify start:" + start);
            System.out.println("modify end:" + end);
            System.out.println("******************************************");
        } else {
            start = DateUtil.getPGDetailImeStampFromString(date + " 00:00:00");
            end = DateUtil.addDays(start, 1);

            System.out.println("******************************************");
            System.out.println("modify start:" + start);
            System.out.println("modify end:" + end);
            System.out.println("******************************************");
        }


        Date d = null;
        try {
            d = DateUtils.parseDate(date, "yyyy-MM-dd");
        } catch (ParseException e) {
            NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        Date today = DateUtils.addDays(d, 1);

        Date yesterday = d;

        System.out.println("yesterday:" + yesterday);
        System.out.println("today:" + today);
    }

    @Test
    public void shutdownTest() {

        drawCashPublisher.publishEvent(new DrawCashEvent(BizTypeEnum.draw_launth, ExecuteStatusEnum.start));

        try {
            Thread.sleep(100000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        System.out.println("**********sleep end***********");

    }

    @Test
    public void unescapeJavaTest() {

        ExperimentVo.Experiment exp = hawkeye.getVars(33, "cf", "{}", "", Long.valueOf(244113546), "", first_data_210);
        Map<String, String> msg210 = Maps.newHashMap();
        if (null != exp && null != exp.getExtInfo()) {
            msg210 = (Map<String, String>) exp.getExtInfo();
        }

        System.out.println("************");

        System.out.println(JSON.toJSONString(exp));
        System.out.println(msg210.get(actual_amount_210));
        System.out.println(msg210.get(pay_time_210));
        System.out.println(msg210.get(pay_detail_210));
        System.out.println(msg210.get(remark_date_210));

        System.out.println("--------------");

        System.out.println(StringEscapeUtils.unescapeJava(msg210.get(actual_amount_210)));
        System.out.println(StringEscapeUtils.unescapeJava(msg210.get(pay_time_210)));
        System.out.println(StringEscapeUtils.unescapeJava(msg210.get(pay_detail_210)));
        System.out.println(StringEscapeUtils.unescapeJava(msg210.get(remark_date_210)));

        System.out.println("************");
    }

    @Test
    public void wordOrderTest() {

        int orderType = AdminWorkOrderConst.Type.UGC.getCode();
        List<Integer> taskTypes = Lists.newArrayList(AdminWorkOrderConst.Task.INFO_BASE_WORD.getCode());
        int orderStatus = AdminWorkOrderConst.Status.HANDLING.getCode();
        int operatorId = 155;

        List<AdminWorkOrder> workOrders = adminWorkOrderDao.getUnHandleTaskByOperator(orderType, taskTypes, orderStatus, operatorId);

        System.out.println("****************");
        System.out.println(JSON.toJSONString(workOrders));
        System.out.println("****************");
    }

    @Test
    public void commonUserTest() {
//        UserComment userComment = new UserComment(UserCommentSourceEnum.UGC, 123456, UserCommentSourceEnum.CommentType.UGC_6, 344, UserCommentSourceEnum.CommentType.UGC_6.getDesc(), "材料审核时间", "");
//        userCommentBiz.insert(userComment);
        List<Integer> workOrderIds = Lists.newArrayList(123, 456, 789);
        List<UserComment> userComments = null;
        for (Integer workOrderId : workOrderIds) {
            UserComment userComment = new UserComment(UserCommentSourceEnum.UGC, workOrderId, UserCommentSourceEnum.CommentType.UGC_6, 344, UserCommentSourceEnum.CommentType.UGC_6.getDesc(), "材料审核时间", "");
            userComments.add(userComment);
        }
        userCommentBiz.addList(userComments);
    }

    @Test
    public void time1Test() {
        Date date = new Date();
        DateTime dateTime = new DateTime();
        dateTime = dateTime.dayOfYear().roundFloorCopy();
        date = new Date(dateTime.getMillis());

        System.out.println("************");
        System.out.println(dateTime);
        System.out.println(date);
        System.out.println("************");

        dateTime = dateTime.dayOfMonth().roundFloorCopy();
        date = new Date(dateTime.getMillis());
        System.out.println(dateTime);
        System.out.println(date);
        System.out.println("***************");


    }


    @Test
    public void transferSQL() {
        adminCrowdfundingInfoBiz.getFundUseAuditInfoByUnionSelect(null,
                null, "2019-03-23 00:00:00", "2019-06-24 00:00:00",
                "2019-03-23 00:00:00", "2019-06-24 00:00:00",
                (1 - 1) * 10, 10, "");
        adminCrowdfundingInfoBiz.getFundUseAuditInfoByUnionSelect(null,
                null, "2019-03-23 00:00:00", "2019-06-24 00:00:00",
                "2019-03-23 00:00:00", "2019-06-24 00:00:00",
                (1 - 1) * 10, 10, "true");
        adminCrowdfundingInfoBiz.getFundUseAuditInfoByUnionSelect(null,
                null, "2019-03-23 00:00:00", "2019-06-24 00:00:00",
                "2019-03-23 00:00:00", "2019-06-24 00:00:00",
                (1 - 1) * 10, 10, "false");


        adminCrowdfundingInfoBiz.selectRecordNums(null, null,
                "2019-03-23 00:00:00", "2019-06-24 00:00:00",
                "2019-03-23 00:00:00", "2019-06-24 00:00:00", "false");

        adminCrowdfundingInfoBiz.selectRecordNums(null, null,
                "2019-03-23 00:00:00", "2019-06-24 00:00:00",
                "2019-03-23 00:00:00", "2019-06-24 00:00:00", "true");

        adminCrowdfundingInfoBiz.selectRecordNums(null, null,
                "2019-03-23 00:00:00", "2019-06-24 00:00:00",
                "2019-03-23 00:00:00", "2019-06-24 00:00:00", "");

    }

    @Autowired
    private CfCaseTagDao cfCaseTagDao;

    @Test
    public void cfCaseTagDaoTest() {
        List<CfCaseTag> tagList = new ArrayList<>();
        CfCaseTag tag2 = new CfCaseTag();
        tag2.setInfoId(2);
        tag2.setTagId(2);

        CfCaseTag tag1 = new CfCaseTag();
        tag1.setInfoId(1);
        tag1.setTagId(1);

        tagList.add(tag1);
        tagList.add(tag2);

        cfCaseTagDao.addCaseTags(tagList);
        log.info("test");
    }

    @Autowired
    private DiscoveryClient discoveryClient;

    @Test
    public void discoveryClientTest() {
        List<String> services = discoveryClient.getServices();
        for (String servieid : services) {
            if (servieid.startsWith("cf-")) {
                log.info("serviceid={}", servieid);
            }
        }
    }


    @Autowired
    private  AdminWorkOrderFlowRecordBiz   adminWorkOrderFlowRecordBiz;

    @Test
    public void insertOneTest(){
        AdminWorkOrderFlowRecord  record=new AdminWorkOrderFlowRecord();
        record.setWorkOrderId(42746);
        record.setProblemType(137);
        record.setProblemContent("000");
        record.setMobile("18515496613");
        record.setCaseId(0);
        record.setSecondClassifyId(131);

        AdminWorkOrderFlowRecord  record1=new AdminWorkOrderFlowRecord();
        record1.setWorkOrderId(42746);
        record1.setProblemType(137);
        record1.setProblemContent("111");
        record1.setMobile("18515496615");
        record1.setCaseId(0);
        record.setSecondClassifyId(131);
        AdminWorkOrderFlowRecord  record2=new AdminWorkOrderFlowRecord();
        record2.setWorkOrderId(42746);
        record2.setProblemType(137);
        record2.setProblemContent("2222");
        record2.setMobile("18515496614");
        record2.setCaseId(0);
        record.setSecondClassifyId(131);

        List<AdminWorkOrderFlowRecord> records=new ArrayList<>();
        records.add(record1);
        records.add(record2);
        adminWorkOrderFlowRecordBiz.insertOne(record);
        adminWorkOrderFlowRecordBiz.insertList(records);
    }
}
