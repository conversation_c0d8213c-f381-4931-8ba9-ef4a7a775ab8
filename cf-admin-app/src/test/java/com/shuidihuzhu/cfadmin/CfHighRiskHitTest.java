package com.shuidihuzhu.cfadmin;

import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditCreateOrder;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.StringJoiner;

/**
 * @Description: 高风险命中策略测试
 * @Author: panghairui
 * @Date: 2022/8/2 2:37 下午
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class CfHighRiskHitTest {

    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }

    @Resource
    private InitialAuditCreateOrder initialAuditCreateOrder;

    @Test
    public void testCreateHighRiskWorkOrder() {

        boolean a1 = initialAuditCreateOrder.createHighRiskWorkOrder(2974664, 0);
        // Assertions.assertThat(a1).isEqualTo(true);



    }

}
