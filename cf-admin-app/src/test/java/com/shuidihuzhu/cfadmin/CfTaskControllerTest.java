package com.shuidihuzhu.cfadmin;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.service.admin.VerifyCountService;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class CfTaskControllerTest {
    @Autowired
    private VerifyCountService verifyCountService;
    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }

    //校验时最多校验的次数
    private static final String KEY_ID_VERIFY_TIME_VALIDATE = "cf-patient-id-v-val-time-";
    //提交时最多校验的次数
    private static final String KEY_ID_VERIFY_TIME_SUBMIT = "cf-patient-id-v-sub-time-";
    @Resource(name = "cfRedissonHandler")
    private RedissonHandler cfRedissonHandler;

    @Test
    public void testInsert() {


    /*    for (int i=0;i<5; i++){
            cfRedissonHandler.incr(KEY_ID_VERIFY_TIME_VALIDATE + "********", RedissonHandler.ONE_DAY);
            cfRedissonHandler.incr(KEY_ID_VERIFY_TIME_SUBMIT + "********", RedissonHandler.ONE_DAY);
        }*/

        long userId = *********;
        //verifyCountService.clear(userId, "CfFirstApproveIdCardVerify");
        //verifyCountService.innerTest(userId, "payeeAccountIdCardVerify");
        System.out.println(JSON.toJSONString(verifyCountService.getAll(userId)));
    }



}
