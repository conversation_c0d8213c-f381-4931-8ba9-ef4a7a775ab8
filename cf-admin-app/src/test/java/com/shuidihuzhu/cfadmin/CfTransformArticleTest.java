package com.shuidihuzhu.cfadmin;

import com.shuidihuzhu.account.model.UserInfoParams;
import com.shuidihuzhu.cf.admin.Application;
//import com.shuidihuzhu.cf.biz.crowdfunding.CfInfoBlessingBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.other.IOtherDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoBlessing;
import com.shuidihuzhu.cf.model.crowdfunding.CfTransformArticleDo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-05-07  17:22
 * @description
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class CfTransformArticleTest {

    @Autowired
    private IOtherDelegate otherDelegate;

    @Autowired(required = false)
    private Producer producer;

    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;

//    @Autowired
//    private CfInfoBlessingBiz cfInfoBlessingBiz;

    private void log(String temp, Object ...data) {
        log.info(temp, data);
    }
//
//    @Test
//    public void testBless() {
//        List<CfInfoBlessing>  blessings = cfInfoBlessingBiz.getByInfoUUid("87648729-5b7f-4dad-a505-996e8affebcd", 0, 100);
//        System.out.println(blessings);
//    }

//    @Test
    public void testInsert() {
        CfTransformArticleDo domain = new CfTransformArticleDo();
        String url = "http://www.baidu.com";
        String imgUrl = "http://static.vsona.net/image-me/img_avater_xigua2.jpg";
        Response res = otherDelegate.insertCfTransformArticle("fakeTitle", url, imgUrl);
        log("insert result:{}", res);
    }

//    @Test
    public void testDelete() {
        Response res = otherDelegate.deleteCfTransformArticle(1);
        log("delete result:{}", res);
    }

//    @Test
    public void testGetList() {
        Response res = otherDelegate.listCfTransformArticleByPage(0, 3);
        log("getList result:{}", res);
    }

//    @Test
    public void testAll() {
        testInsert();
        testDelete();
        testGetList();
    }

    @Test
    public void testMQ() {
        CrowdFundingProgress progress = crowdfundingDelegate.getActivityProgressById(1321);

        producer.send(new Message(MQTopicCons.CF,
                                  MQTagCons.CF_PUBLISH_PROGRESS,
                                  MQTagCons.CF_PUBLISH_PROGRESS + "_" + progress.getId(),
                                  progress));

    }
}
