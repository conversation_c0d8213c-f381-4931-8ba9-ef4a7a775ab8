package com.shuidihuzhu.cfadmin;

import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.admin.mq.AdminAuditCaseLabelConsumer;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class CfCaseLabelConsumerTest {
    @Autowired
    private AdminAuditCaseLabelConsumer caseLabelConsumer;
    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }

    @Test
    public void testInsert() {
        ConsumerMessage<InitialAuditItem.InitialAuditOperation> mqMessage = new ConsumerMessage<>();
        InitialAuditItem.InitialAuditOperation  operation = new InitialAuditItem.InitialAuditOperation();
        operation.setCaseId(2877901);
        List<Integer> passIds = new ArrayList<>();
        passIds.add(1);
        passIds.add(100);
        passIds.add(20);
        operation.setPassIds(passIds);
        mqMessage.setPayload(operation);
        caseLabelConsumer.consumeMessage(mqMessage);
    }



}
