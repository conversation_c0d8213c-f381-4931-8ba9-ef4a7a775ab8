package com.shuidihuzhu.cfadmin;

import com.google.common.base.Splitter;
import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordResult;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.sensitive.adapter.SensitiveCommentAdapter;
import com.shuidihuzhu.cf.service.sensitive.checker.IllegalSnippetChecker;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: UGC敏感词过滤测试
 * @Author: panghairui
 * @Date: 2022/8/3 3:32 下午
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class UgcSensitiveTest {

    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }

    @Resource
    private IllegalSnippetChecker snippetChecker;

    @Test
    public void testHitAddForHelp() {
        // IllegalSnippetChecker snippetChecker = new IllegalSnippetChecker();
        List<String> contentList = Splitter.on(",")
                .splitToList("加她 F0禾55禾77禾55 忽略：禾 她可以帮助," +
                        "加她 F05禾91禾00禾01 她可以帮助," +
                        "加她 a95禾00禾49 她可以帮助," +
                        "加她 F0禾55禾66禾55 忽略：禾 她可以帮助," +
                        "加她 F9禾55禾99禾55 忽略：禾 她可以帮助," +
                        "加油❗您可＋V：klxh16 或许我可无偿帮助," +
                        "加她 R7四四33（汉字转换数字搜索）她可以帮助," +
                        "加她 F0禾44禾33 她可以帮助," +
                        "加我微信d13872835148会有更有效的帮助," +
                        "加:crhd77可以帮助," +
                        "加我微信13807016777我有帮助," +
                        "加我v15935366496对孙静的康复会有所帮助," +
                        "加她 R3七七44（汉字转换数字搜索）她可以帮助");

        List<OpResult<RiskWordResult>> results = new ArrayList<>();
        contentList.forEach(content -> {
            // results.add(snippetChecker.symbolChecker(content));
        });
        System.out.println(1);
    }

}
