package com.shuidihuzhu.cfadmin;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.admin.Application;
import com.shuidihuzhu.cf.delegate.commonservice.ICommonServiceDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICfInfoXXXRecordDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingOperationDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingUserDelegate;
import com.shuidihuzhu.cf.delegate.finance.IFinanceDelegate;
import com.shuidihuzhu.cf.delegate.other.IMiniAppDelegate;
import com.shuidihuzhu.cf.delegate.other.IOtherDelegate;
import com.shuidihuzhu.cf.delegate.other.IWeiXinDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.delegate.ugc.IUgcDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminCfPageTypeEnum;
import com.shuidihuzhu.cf.finance.enums.NewCfCapitalEnum;
import com.shuidihuzhu.cf.mq.IAdminCommonMessageHelperService;
import com.shuidihuzhu.msg.enums.RecordConstant;
import com.shuidihuzhu.msg.model.WxCustomRecord;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wanghui
 * @time: 2019/6/10 7:29 PM
 * @description: 测试 去除cf-biz 产生的 delegate
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class DelegateTest {
    @Resource
    private IAdminCommonMessageHelperService adminCommonMessageHelperService;
    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;
    @Resource
    private ICrowdfundingOperationDelegate crowdfundingOperationDelegate;
    @Resource
    private ICrowdfundingUserDelegate crowdfundingUserDelegate;
    @Resource
    private IFinanceDelegate financeDelegate;
    @Resource
    private IWeiXinDelegate weiXinDelegate;
    @Resource
    private IMiniAppDelegate miniAppDelegate;
    @Resource
    private IOtherDelegate otherDelegate;
    @Resource
    private IRiskDelegate riskDelegate;
    @Resource
    private ICommonServiceDelegate commonServiceDelegate;
    @Resource
    private IUgcDelegate ugcDelegate;
    @Resource
    private ICfInfoXXXRecordDelegate cfInfoXXXRecordDelegate;

    static {
//        System.setProperty("spring.cloud.consul.host", "consul.zelda.shuiditech.com:80");
        System.setProperty("spring.cloud.consul.host", "127.0.0.1");
    }

    @Test
    public void testDelegate() {

//        adminCommonMessageHelperService.send(Message message);
//        adminCommonMessageHelperService.getCfPublishProgressNoticeMessage(CrowdFundingProgress crowdFundingProgress);
//        adminCommonMessageHelperService.getCfAuditPassToDonorMsg(CrowdfundingInfo crowdfundingInfo);
//        String data = "[{\"id\":7062,\"jumperUrl\":\"https://www.shuidihuzhu.com/toufang/throwIn?typeFrom\u003dsdcUser\u0026channel\u003dsdc_left_slide_02\",\"jumperName\":\"\",\"jumperDesc\":\"\",\"globalType\":1,\"effectShow\":0,\"percent\":10,\"createTime\":1559887837000,\"updateTime\":1559887837000},{\"id\":7063,\"jumperUrl\":\"https://www.sdbao.com/sems/53?adPlace\u003d100\u0026baseProductNo\u003dax_bw_yl_0y_01\u0026channel\u003dcf\u0026subchannel\u003dBA_NO_C1_leftSwtich15_wutan\",\"jumperName\":\"\",\"jumperDesc\":\"\",\"globalType\":1,\"effectShow\":0,\"percent\":50,\"createTime\":1559887837000,\"updateTime\":1559887837000},{\"id\":7064,\"jumperUrl\":\"https://www.sdbao.com/sems/53?adPlace\u003d100\u0026baseProductNo\u003dax_bw_yl_0y_01\u0026channel\u003dcf\u0026subchannel\u003dBA_NO_C1_leftSwtich15_wutan1\",\"jumperName\":\"\",\"jumperDesc\":\"\",\"globalType\":1,\"effectShow\":0,\"percent\":5,\"createTime\":1559887837000,\"updateTime\":1559887837000},{\"id\":7065,\"jumperUrl\":\"https://www.sdbao.com/sems/53?adPlace\u003d201\u0026baseProductNo\u003dax_bw_yl_0y_01\u0026channel\u003dcf\u0026subchannel\u003dBA_NO_C1_leftSwtich15_fenping\",\"jumperName\":\"\",\"jumperDesc\":\"\",\"globalType\":1,\"effectShow\":0,\"percent\":5,\"createTime\":1559887837000,\"updateTime\":1559887837000},{\"id\":7066,\"jumperUrl\":\"https://www.sdbao.com/sems/53?adPlace\u003d202\u0026baseProductNo\u003dax_bw_yl_0y_01\u0026channel\u003dcf\u0026subchannel\u003dBA_NO_C1_leftSwtich15_kapian\",\"jumperName\":\"\",\"jumperDesc\":\"\",\"globalType\":1,\"effectShow\":0,\"percent\":5,\"createTime\":1559887837000,\"updateTime\":1559887837000},{\"id\":7067,\"jumperUrl\":\"https://www.sdbao.com/sems/53?adPlace\u003d200\u0026baseProductNo\u003dax_bw_yl_0y_01\u0026channel\u003dcf\u0026subchannel\u003dBA_NO_C1_leftSwtich15_xinfeng\",\"jumperName\":\"\",\"jumperDesc\":\"\",\"globalType\":1,\"effectShow\":0,\"percent\":5,\"createTime\":1559887837000,\"updateTime\":1559887837000},{\"globalType\":1,\"jumperName\":\"\",\"jumperDesc\":\"\",\"jumperUrl\":\"https://www.shuidichou.com/gongyi/case/PF343456721118707712?channel\u003dpf_wx_null_zuohua\",\"effectShow\":0,\"percent\":1},{\"globalType\":1,\"jumperName\":\"\",\"jumperDesc\":\"\",\"jumperUrl\":\"https://www.shuidichou.com/gongyi/case/PF343447720695959552?channel\u003dpf_wx_null_zuohua\",\"effectShow\":0,\"percent\":1},{\"globalType\":1,\"jumperName\":\"\",\"jumperDesc\":\"\",\"jumperUrl\":\"https://www.shuidichou.com/gongyi/case/PF343428523974737920?channel\u003dpf_wx_null_zuohua\",\"effectShow\":0,\"percent\":1},{\"globalType\":1,\"jumperName\":\"\",\"jumperDesc\":\"\",\"jumperUrl\":\"https://www.shuidichou.com/gongyi/case/PF343427820141174784?channel\u003dpf_wx_null_zuohua\",\"effectShow\":0,\"percent\":1},{\"globalType\":1,\"jumperName\":\"\",\"jumperDesc\":\"\",\"jumperUrl\":\"https://www.shuidichou.com/gongyi/case/PF343418138223730688?channel\u003dpf_wx_null_zuohua\",\"effectShow\":0,\"percent\":1},{\"globalType\":1,\"jumperName\":\"\",\"jumperDesc\":\"\",\"jumperUrl\":\"https://www.shuidichou.com/gongyi/case/PF343409888384303104?channel\u003dpf_wx_null_zuohua\",\"effectShow\":0,\"percent\":1},{\"globalType\":1,\"jumperName\":\"\",\"jumperDesc\":\"\",\"jumperUrl\":\"https://www.shuidichou.com/gongyi/case/PF343379611414958080?channel\u003dpf_wx_null_zuohua\",\"effectShow\":0,\"percent\":1},{\"globalType\":1,\"jumperName\":\"\",\"jumperDesc\":\"\",\"jumperUrl\":\"https://www.shuidichou.com/gongyi/case/PF343368928770179072?channel\u003dpf_wx_null_zuohua\",\"effectShow\":0,\"percent\":1},{\"globalType\":1,\"jumperName\":\"\",\"jumperDesc\":\"\",\"jumperUrl\":\"https://www.shuidichou.com/gongyi/case/PF343356356633325568?channel\u003dpf_wx_null_zuohua\",\"effectShow\":0,\"percent\":1},{\"globalType\":1,\"jumperName\":\"\",\"jumperDesc\":\"\",\"jumperUrl\":\"https://www.shuidichou.com/gongyi/case/PF343349235141353472?channel\u003dpf_wx_null_zuohua\",\"effectShow\":0,\"percent\":1},{\"globalType\":1,\"jumperName\":\"\",\"jumperDesc\":\"\",\"jumperUrl\":\"https://www.shuidichou.com/gongyi/case/PF343329189283254272?channel\u003dpf_wx_null_zuohua\",\"effectShow\":0,\"percent\":1},{\"globalType\":1,\"jumperName\":\"\",\"jumperDesc\":\"\",\"jumperUrl\":\"https://www.shuidichou.com/gongyi/case/PF343326667952893952?channel\u003dpf_wx_null_zuohua\",\"effectShow\":0,\"percent\":1},{\"globalType\":1,\"jumperName\":\"\",\"jumperDesc\":\"\",\"jumperUrl\":\"https://www.shuidichou.com/gongyi/case/PF343080895466455040?channel\u003dpf_wx_null_zuohua\",\"effectShow\":0,\"percent\":1},{\"globalType\":1,\"jumperName\":\"\",\"jumperDesc\":\"\",\"jumperUrl\":\"https://www.shuidichou.com/gongyi/case/PF343046290101243904?channel\u003dpf_wx_null_zuohua\",\"effectShow\":0,\"percent\":1},{\"globalType\":1,\"jumperName\":\"\",\"jumperDesc\":\"\",\"jumperUrl\":\"https://www.shuidichou.com/gongyi/case/PF343044673578729472?channel\u003dpf_wx_null_zuohua\",\"effectShow\":0,\"percent\":1},{\"globalType\":1,\"jumperName\":\"\",\"jumperDesc\":\"\",\"jumperUrl\":\"https://www.shuidichou.com/gongyi/case/PF343037902369861632?channel\u003dpf_wx_null_zuohua\",\"effectShow\":0,\"percent\":1},{\"globalType\":1,\"jumperName\":\"\",\"jumperDesc\":\"\",\"jumperUrl\":\"https://www.shuidichou.com/gongyi/case/PF343037152537370624?channel\u003dpf_wx_null_zuohua\",\"effectShow\":0,\"percent\":1},{\"globalType\":1,\"jumperName\":\"\",\"jumperDesc\":\"\",\"jumperUrl\":\"https://www.shuidichou.com/gongyi/case/PF343036757001912320?channel\u003dpf_wx_null_zuohua\",\"effectShow\":0,\"percent\":1},{\"globalType\":1,\"jumperName\":\"\",\"jumperDesc\":\"\",\"jumperUrl\":\"https://www.shuidichou.com/gongyi/case/PF343032856316002304?channel\u003dpf_wx_null_zuohua\",\"effectShow\":0,\"percent\":1},{\"globalType\":1,\"jumperName\":\"\",\"jumperDesc\":\"\",\"jumperUrl\":\"https://www.shuidichou.com/gongyi/case/PF343028840383180800?channel\u003dpf_wx_null_zuohua\",\"effectShow\":0,\"percent\":1}]";
        commonServiceDelegate.getListByType(AdminCfPageTypeEnum.leftPage.getCode());
//
//        crowdfundingDelegate.selectByInfoUuid("dac4fccb-1de4-4739-ba19-42cf14918923");
//        CfBaseInfoTemplateRecord cfBaseInfoTemplateRecord = new CfBaseInfoTemplateRecord();
//        cfBaseInfoTemplateRecord.setTitleId(1);
//        cfBaseInfoTemplateRecord.setContentId(56);
//        CrowdfundingUser crowdfundingInfo = new CrowdfundingUser();
//        crowdfundingInfo.setUserId(269384592);
//        crowdfundingInfo.setUserThirdType(3);
//        crowdfundingUserDelegate.getUserInfo(Lists.newArrayList(crowdfundingInfo));
//        crowdfundingUserDelegate.selectByInfoUuid("b4849f18-aae4-4624-b1c5-5183783ec511");
//        CfCreditSupplement cfCreditSupplement = new CfCreditSupplement();
////        (#{item.infoUuid},#{item.count},#{item.propertyType},#{item.totalValue},#{item.status},#{item.sellCount},#{item.convention},#{item.saleValue})
//        cfCreditSupplement.setInfoUuid("eef21217-b209-4506-861b-95ba581f06da");
//        cfCreditSupplement.setCount(1);
//        cfCreditSupplement.setPropertyType(2);
//        cfCreditSupplement.setTotalValue(1);
//        cfCreditSupplement.setStatus(0);
//        cfCreditSupplement.setSellCount(0);
//        cfCreditSupplement.setConvention(0);
//        cfCreditSupplement.setSaleValue(-1);
//
//        crowdfundingDelegate.getById(1);
//        crowdfundingDelegate.mapByIds(Lists.newArrayList(1,2,3));
//        crowdfundingDelegate.subtractAmount(1, 1);
////        (#{id},#{shareCount},#{donationCount},#{verifyUserCount},#{commentCount},#{amount},#{verifyFriendCount},#{verifyHospitalCount})
//        CfInfoStat cfInfoStat = new CfInfoStat();
//        cfInfoStat.setId(982464);
//        cfInfoStat.setShareCount(1);
//        cfInfoStat.setDonationCount(1);
//        cfInfoStat.setVerifyUserCount(0);
//        cfInfoStat.setCommentCount(0);
//        cfInfoStat.setAmount(100);
//        cfInfoStat.setVerifyFriendCount(0);
//        cfInfoStat.setVerifyHospitalCount(0);
//
//        crowdfundingDelegate.updateTimes("e57f935a-8fca-4721-979c-d2322c62f049", CfTaskEnum.Rule.SHARE_TO_MOMENTS);
////        CrowdfundingInfo crowdfundingInfo = new CrowdfundingInfo();
////        crowdfundingInfo.setInfoId("e57f935a-8fca-4721-979c-d2322c62f049");
////        crowdfundingInfo.setId(142522);
////        crowdfundingDelegate.publishHospitalTask(crowdfundingInfo);
//
//        crowdfundingOperationDelegate.saveCfOperatingRecord("82f39069-429e-4d18-b9f1-8b5938a77a7c", 97, "郑亮", CfOperatingRecordEnum.Type.OPERATOR_TYPE_PAUSE, CfOperatingRecordEnum.Role.OPERATOR);
//        crowdfundingOperationDelegate.getLastOneByType("82f39069-429e-4d18-b9f1-8b5938a77a7c", CfOperatingRecordEnum.Type.OPERATOR_TYPE_PAUSE);
//        CfOperatingRecord operatingRecord = new CfOperatingRecord();
//        operatingRecord.setInfoUuid("82f39069-429e-4d18-b9f1-8b5938a77a7c");
//        operatingRecord.setType(CfOperatingRecordEnum.Type.ADD_REMINDER.getCode());
//        operatingRecord.setRole(CfOperatingRecordEnum.Role.OPERATOR.getCode());
//        operatingRecord.setUserId(97);
//        operatingRecord.setUserName("郑亮");
//        operatingRecord.setComment("111");
//        crowdfundingOperationDelegate.insertOperatingRecord(operatingRecord);
//        CrowdfundingOperation crowdfundingOperation = new CrowdfundingOperation();
//        crowdfundingOperation.setInfoId("82f39069-429e-4d18-b9f1-8b5938a77a7c");
//        crowdfundingOperation.setReason("");
//        crowdfundingOperation.setOperatorId(97);
//        crowdfundingOperation.setOperation(CrowdfundingOperationEnum.NONE_OPERATION.value());
////        crowdfundingOperationDelegate.add(crowdfundingOperation);
//        crowdfundingOperation.setReason("modify");
//        crowdfundingOperation.setFollowType(1);
//        crowdfundingOperationDelegate.updateCrowdfundingOperation(crowdfundingOperation);
//        crowdfundingOperationDelegate.getByInfoId("82f39069-429e-4d18-b9f1-8b5938a77a7c");
//        crowdfundingOperationDelegate.updateReportStatus(1, "82f39069-429e-4d18-b9f1-8b5938a77a7c");
//        CrowdfundingApprove crowdfundingApprove = new CrowdfundingApprove();
//        crowdfundingApprove.setCrowdfundingId(976308);
//        crowdfundingApprove.setComment("图文-案例结束:123123123123312123123");
//        crowdfundingApprove.setOprid(191);
//        crowdfundingApprove.setOprtime(new Date());
//        crowdfundingApprove.setStatus(0);
//        crowdfundingOperationDelegate.insertCrowdfundingApprove(crowdfundingApprove);
//        crowdfundingOperationDelegate.getListByCrowdfundingId(976308);
//        crowdfundingOperationDelegate.getLastWithCommentByCrowdfundingId(976308);
//        crowdfundingOperationDelegate.addApprove(CrowdfundingInfo crowdfundingInfo, String operation, String comment, long userId);
////        crowdfundingOperationDelegate.addSystemApprove(CrowdfundingInfo crowdfundingInfo, String operation, String comment);
//
//        crowdfundingDelegate.getFundingAttachment(1);
////        crowdfundingDelegate.getByInfoIdList(List<Integer> infoIdList, AttachmentTypeEnum type);
//        crowdfundingDelegate.getAttachmentsByType(1, AttachmentTypeEnum.ATTACH_CF);
////        crowdfundingDelegate.add(List<CrowdfundingAttachment> crowdfundingAttachmentList);
////        crowdfundingDelegate.addOne(CrowdfundingAttachment crowdfundingAttachment);
////        crowdfundingDelegate.deleteByParentIdAndType(int parentId, List<com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum > typeList);
////        crowdfundingDelegate.addForAdmin(List<CrowdfundingAttachment> crowdfundingAttachmentList);
//
//        crowdfundingUserDelegate.getCrowdfundingAuthor(1);
////        crowdfundingUserDelegate.add(CrowdfundingAuthor crowdfundingAuthor);
////        crowdfundingUserDelegate.update(CrowdfundingAuthor crowdfundingAuthor);
//        crowdfundingUserDelegate.getByInfoIdList(Lists.newArrayList(1,2));

////        crowdfundingDelegate.add(CrowdfundingDreamIndividual crowdfundingDreamIndividual);
//        crowdfundingDelegate.getCrowdfundingInfoByInfoId("f4b17b1b-b128-409f-a233-3af0ccc371f2");
////        crowdfundingDelegate.add(CrowdfundingDreamOrganization crowdfundingDreamOrganization);
//        crowdfundingDelegate.getCrowdfundingDreamOrganizationByInfoId("f4b17b1b-b128-409f-a233-3af0ccc371f2");
//
////        crowdfundingDelegate.addTag(CrowdfundingInfo info, CfFinishStatus cfFinishStatus);
//
//        crowdfundingDelegate.getCrowdfundingIdCaseByInfoId(972532);
////        crowdfundingDelegate.insert(CrowdfundingIdCase crowdfundingIdCase);
////        crowdfundingDelegate.updateStatus(int caseId, CrowdfundingIdCaseStatusEnum crowdfundingIdCaseStatusEnum);
////        crowdfundingDelegate.sendIdCaseFailMsg(CrowdfundingInfo crowdfundingInfo);
//        crowdfundingDelegate.getByCryptoIdCard("LeMCKlJMRQDFlEBm5UaBD61mUNexJNNWvI0XLq41pfw=");
//
////        crowdfundingDelegate.add(CrowdfundingInfo crowdfundingInfo);
////        crowdfundingDelegate.update(CrowdfundingInfo crowdfundingInfo);
////        crowdfundingDelegate.selectByExample(BasicExample basicExample, Integer pageNum, Integer pageSize);
//        crowdfundingDelegate.getFundingInfoById(972532);
//        crowdfundingDelegate.getMapByIds(Lists.newArrayList(972532));
////        crowdfundingDelegate.updateEndTime(int id, Date endTime);
////        crowdfundingDelegate.updateBeginAndEndTime(int infoId, Date beginTime, java.util.Date endTime);
////        crowdfundingDelegate.updateStatus(int id, CrowdfundingStatus newStatus, CrowdfundingStatus oldStatus);
////        crowdfundingDelegate.getByPage(int current, int pageSize, Integer applyStatus, Integer drawStatus);
////        crowdfundingDelegate.insertIntoFromExcel(CrowdfundingInfo crowdfundingInfo);
////        crowdfundingDelegate.getId(Timestamp startTime, Timestamp endTime);
//        crowdfundingDelegate.getFirstCrowdfundingInfoByUserId(269384716);
//        crowdfundingDelegate.selectCrowdfundingInfoListByUserId(269384716);
////        crowdfundingDelegate.add(CrowdfundingInfoStatus crowdfundingInfoStatus);
////        crowdfundingDelegate.updateByInfoId(String infoUuId, int type, CrowdfundingInfoStatusEnum status);
//        crowdfundingDelegate.getCrowdfundingInfoPayeeByInfoUuid("e57f935a-8fca-4721-979c-d2322c62f049");
////        crowdfundingDelegate.after(CfOperatingRecord cfOperatingRecord, int status);
////        crowdfundingDelegate.after(CfOperatingRecord cfOperatingRecord);
////        crowdfundingDelegate.before(CrowdfundingInfo crowdfundingInfo,long userId, String userName, CfOperatingRecordEnum.Type type, CfOperatingRecordEnum.Role role);
////        crowdfundingDelegate.before(CrowdfundingInfo crowdfundingInfo,long userId, String userName, CfOperatingRecordEnum.Type type, CfOperatingRecordEnum.Role role, String comment);
//        crowdfundingDelegate.getCrowdfundingInfoByUserId(27135);
////        crowdfundingDelegate.createExtData(CrowdfundingInfo crowdfundingInfo, String selfTag, Integer userThirdType, String registerMobile);
////        crowdfundingDelegate.pushLaunchMsg(CrowdfundingInfo crowdfundingInfo, int platform) throws Exception;
////        crowdfundingDelegate.getCfInfoSimpleModel(String infoUuid);
////        crowdfundingDelegate.getCfInfoSimpleModelById(Integer infoId);
////        crowdfundingDelegate.getCfInfoSimpleModelListByIds(List<Integer> ids);
//
////        crowdfundingDelegate.add(CrowdfundingInfoPayee crowdfundingInfoPayee);
////        crowdfundingDelegate.update(CrowdfundingInfoPayee crowdfundingInfoPayee);
//        crowdfundingDelegate.getCrowdfundingInfoPayeeByInfoUuid("e57f935a-8fca-4721-979c-d2322c62f049");
////        crowdfundingDelegate.add(CrowdfundingInfoHospitalPayee crowdfundingInfoHospitalPayee);
////        crowdfundingDelegate.update(CrowdfundingInfoHospitalPayee crowdfundingInfoHospitalPayee);
//        crowdfundingDelegate.getCrowdfundingInfoHospitalPayeeByInfoUuid("e57f935a-8fca-4721-979c-d2322c62f049");
//        crowdfundingDelegate.getCfCharityPayeeByUUid("e57f935a-8fca-4721-979c-d2322c62f049");
//
////        crowdfundingDelegate.add(CrowdFundingProgress progress);
//        crowdfundingDelegate.getActivityProgress(25,  Lists.newArrayList(4),  0, 10);
//        crowdfundingDelegate.getActivityProgressById( 70);
//        crowdfundingDelegate.updateType(70, 4, 0);
////        crowdfundingDelegate.addProgress(CrowdFundingProgress progress);
//        crowdfundingDelegate.updateForDelete(25, 76);
//        crowdfundingDelegate.updateImageUrls(76, "http://cf.alioss.shuidihuzhu.com/img/ck/20160724/cd049bc4-b924-4334-af06-cce6abd5c485");
//
//        crowdfundingUserDelegate.getCrowdfundingTreatment(2);
//        crowdfundingUserDelegate.updateCrowdfundingTreatment();
////        crowdfundingUserDelegate.add(CrowdfundingTreatment crowdfundingTreatment);
////        crowdfundingUserDelegate.update(CrowdfundingTreatment crowdfundingTreatment);
//        crowdfundingUserDelegate.getCrowdfundingTreatmentMapByInfoIdList(Lists.newArrayList(2,21));
//
////        crowdfundingUserDelegate.getUserInfo(List<CrowdfundingUser> crowdfundingUserList);
//
//        crowdfundingUserDelegate.getCreditSupplementByCaseId(142522);
//
////        financeDelegate.save("e57f935a-8fca-4721-979c-d2322c62f049");
//        financeDelegate.getCfCapitalAccountByInfoUuid("e57f935a-8fca-4721-979c-d2322c62f049");
//
//
//        financeDelegate.getDrawCashApplyByDrawCashId(1081);
////        financeDelegate.updateCfDrawCash(long id, CfDrawCash cfDrawCash, CfPayeeMirrorRemarkEnum cfPayeeMirrorRemarkEnum, int infoId);
//
//        weiXinDelegate.sendByEmail(Lists.newArrayList("wanghui"), "测试");
////        weiXinDelegate.sendByUser(List<String> operatorNameList, String s);
////        weiXinDelegate.sendByGroup(String groupId, String content);
////
////        miniAppDelegate.addOne(CfKeywordPhaseRelation cfKeywordPhaseRelation);
//        miniAppDelegate.selectCfKeywordPhaseRelationByPhaseId(1);
//
////        miniAppDelegate.insertOne(CfMinaMajorDiseaseInfo cfMinaMajorDiseaseInfo);
////        miniAppDelegate.insertOne(CfMinaMajorDiseaseMenu cfMinaMajorDiseaseMenu);
////        miniAppDelegate.insert(CfMinaMajorDiseaseTitleContent cfMinaMajorDiseaseTitleContent);
//
//        miniAppDelegate.getCfMinaQuizById(1);
////        miniAppDelegate.findByTitle(String title, Integer offset, Integer pageSize);
//        miniAppDelegate.countByTitle("测测你能活到多少岁？");
////        miniAppDelegate.saveMinaQuiz(CfMinaQuiz cfMinaQuiz);
////        miniAppDelegate.saveMinaQuizQuestion(CfMinaQuizQuestion cfMinaQuizQuestion);
////        miniAppDelegate.saveMinaQuizAnswerBatch(List<CfMinaQuizAnswer> cfMinaQuizAnswerList);
////        miniAppDelegate.saveQuizResult(CfMinaQuizResult cfMinaQuizResult);
////        miniAppDelegate.getBySortAndIsNew(Integer isNew, Integer sort);
////        miniAppDelegate.updateIsNewById(Integer isNew, Integer id);
////        miniAppDelegate.updateSortById(Integer sort, Integer id);
////        miniAppDelegate.updateReleaseTimeById(Date releaseTime, Integer id);
////        miniAppDelegate.updateShowTypeById(Integer showType, Integer id);
////        miniAppDelegate.updateIsDeleteById(Integer isDelete, Integer id);
//        miniAppDelegate.findQuestion(1);
//        miniAppDelegate.findAnswerByQuestionIds(Lists.newArrayList(61,62));
//        miniAppDelegate.findResultByQuizId(1);
////        miniAppDelegate.updateAnswerDeleteByQuestionIds(Integer isDelete, List<Integer> questionIds);
////        miniAppDelegate.updateQuestionDeleteByMinaQuizId(Integer isDelete, Integer minaQuizId);
////        miniAppDelegate.updateResultDeleteByMinaQuizId(Integer isDelete, Integer minaQuizId);
////        miniAppDelegate.updateQuizInfoById(CfMinaQuiz cfMinaQuiz);
////        miniAppDelegate.insertPushConfig(CfMinaQuizPushConfig cfMinaQuizPushConfig);
////        miniAppDelegate.updateStatusById(Integer status, Integer id);
//        miniAppDelegate.findPushConfig(0, 10);
//        miniAppDelegate.getPushConfigById(1);
////        miniAppDelegate.updatePushConfigById(CfMinaQuizPushConfig pushConfig);
//        miniAppDelegate.findByQuizIdWithUserId(10, Lists.newArrayList(244112925L));
//        miniAppDelegate.findByUserIds(10, Lists.newArrayList(244112925L));
////        miniAppDelegate.insertPush(CfMiniQuizPush cfMiniQuizPush);
//        miniAppDelegate.countAllPushConfig();
////        miniAppDelegate.insertResultConfig(CfMinaQuizResultConfig cfMinaQuizResultConfig);
//        miniAppDelegate.findResultConfigByResultIds(Lists.newArrayList(191));
//
////        miniAppDelegate.insertOne(CfPublishPhase cfPublishPhase);
////        miniAppDelegate.deleteById(int id);
//        miniAppDelegate.selectCfPublishPhaseByPhaseId(1);
//        miniAppDelegate.getCfPublishPhaseListPage(0,10);
////        miniAppDelegate.listByTimeLimit(Timestamp time, int limit);
////        miniAppDelegate.listByPhaseIds(List<Integer> phaseIds);
//
////        miniAppDelegate.buildFirstApproveRefuse(long userId, String nickName, String infoUuid, String failMsg, String url, String imageUrl, AppPushTemplate appPushTemplate, List< AppPushTemplateParam > appPushTemplateParamList, int subBizType);
////        miniAppDelegate.newInfoApplySuccess(CrowdfundingInfo crowdfundingInfo);
//        CrowdfundingInfo crowdfundingInfo = new CrowdfundingInfo();
//        crowdfundingInfo.setUserId(27135);
//        crowdfundingInfo.setInfoId("e57f935a-8fca-4721-979c-d2322c62f049");
//        miniAppDelegate.newNeedModifiedSuccess(crowdfundingInfo);
//
//        miniAppDelegate.selectCfTopicCommentById(408);
////        miniAppDelegate.deleteById(long id);
////        miniAppDelegate.addOne(CfTopic cfTopic);
////        miniAppDelegate.deleteById(int id);
////        miniAppDelegate.updateById(int id, CfTopic cfTopic);
//        miniAppDelegate.selectCfTopicById(223);
//        miniAppDelegate.selectCfTopicByPhaseId(0);
//        miniAppDelegate.listCfTopicByIds(Lists.newArrayList(223,224));
////        miniAppDelegate.addOne(CfTopicKeyword cfTopicKeyword);
//        miniAppDelegate.selectCfTopicKeywordById(1);
//        miniAppDelegate.listCfTopicKeywordListByIds(Lists.newArrayList(1,2));
//        miniAppDelegate.selectCfTopicKeywordByKeyword("烩面");
//        CfTouFangSign cfTouFangSign = new CfTouFangSign();
//        cfTouFangSign.setMobile("13265508789");
//        cfTouFangSign.setCryptoMobile("VdACbosLXodtFOpXYCihOQ==");
//        cfTouFangSign.setDayKey("2018-02-04");
//        cfTouFangSign.setReferer("https://toufang.shuidichou.com/cf/brand-landingforadvisory/brand");
//        cfTouFangSign.setClientIp(0);
//        cfTouFangSign.setIsAuto(0);
//        cfTouFangSign.setChannel("");
//        cfTouFangSign.setSource("");
//        cfTouFangSign.setMatch("");
//        cfTouFangSign.setKeyword("");
//        cfTouFangSign.setSemwp("");
//        cfTouFangSign.setAccount("");
//        cfTouFangSign.setKwid("");
//        cfTouFangSign.setCreative("");
//        cfTouFangSign.seteAdposition("");
//        cfTouFangSign.setOpenId("");
////        otherDelegate.insertList(Lists.newArrayList(cfTouFangSign));
//
////        otherDelegate.insert(String title, String url, String imgUrl);
////        otherDelegate.delete(long id);
//        otherDelegate.listCfTransformArticleByAnchor(0,10);
//        otherDelegate.listCfTransformArticleByPage(1,10);
//        otherDelegate.listByPageAndTitleKey(1,10, "dddd");
//
//        commonServiceDelegate.getContribute("1", "2", 3);
//        commonServiceDelegate.getContribute("1", "2", CrowdfundingType.DREAM);
//        commonServiceDelegate.getMineRaise("2");
//        commonServiceDelegate.compactOssUrl("https://toufang.shuidichou.com/cf/brand-landingforadvisory/brand");
//        commonServiceDelegate.pageDomain();
//

//
////        weiXinDelegate.sendMsg4allDonor(int caseId, long reportTrustId);
//        weiXinDelegate.generateQrcode("wanghui");
////        weiXinDelegate.sendTemplateMessage(String openId, String templateId, String url, Map<String, Object> dataMap, int type, int businessType, int subBusinessType);
//
////        otherDelegate.checkCouldSend(long userId, UrgeRaiseMsgType rightMsgType);
//
//        riskDelegate.getByInfoUuid("cd9b4f05-5841-4940-a306-eb9eee7c6e24", CfCaseRiskTypeEnum.CASE_INFO_RISK);
////        riskDelegate.handleInfoRisk("e57f935a-8fca-4721-979c-d2322c62f049");
////        riskDelegate.handleDrawCaskRisk("e57f935a-8fca-4721-979c-d2322c62f049");
////        riskDelegate.onCaseEnd("e57f935a-8fca-4721-979c-d2322c62f049");
////        riskDelegate.onCaseFirstApproveHasDelay72("e57f935a-8fca-4721-979c-d2322c62f049");
////        riskDelegate.onCaseInfoPassed(String infoUuid, CrowdfundingInfo info);
////        riskDelegate.doVerify(CfCaseRiskDO v, CfCaseRiskTypeEnum typeEnum, String dataLevel);
////        riskDelegate.onRemark(int caseId, int remarkCode);
////        riskDelegate.listByConditionAll(int verified, Boolean passed,int type, StatRiskHandleStatusEnum handleStatus);
//        riskDelegate.getByInfoUuid("e304ffc1-bf66-40b3-b488-a58dd601f86f");
////        riskDelegate.saveRaiseRisk(CaseRaiseRiskDO raiseRiskDO);
//        riskDelegate.handleRiskPassed("e304ffc1-bf66-40b3-b488-a58dd601f86f");
//
////        riskDelegate.save(CfFaceIdLivingVerifyInfo cfFaceIdLivingVerifyInfo);
//        riskDelegate.getCfFaceIdLivingVerifyInfoByInfoUuid("b8ed0e88-a623-4a75-bc0f-0a65ebfb2398");
//        riskDelegate.getCfFaceIdLivingVerifyInfoByInfoUuidAndType("b8ed0e88-a623-4a75-bc0f-0a65ebfb2398", 1);
//
//        riskDelegate.addIdcardVerifyWhiteList("卫汉文","12321");
//        riskDelegate.getByNameAndIdcard("卫汉文","12321");
////        riskDelegate.deleteFirstApproveWhiteIdById(int id);
//        riskDelegate.selectAllWhiteIdCardList(0, 10);
////        riskDelegate.verifyIdcard(String selfRealName, String selfIdcard,  String otherRealName, String otherIdcard, int patientIdType, UserRelTypeEnum relType, long userId);boolean isSuccess(CfErrorCode cfErrorCode);
////        riskDelegate.add(CfFirsApproveMaterial cfFirsApproveMaterial);
//        riskDelegate.updateStatus(2, FirstApproveIdcardVerifyStatusEnum.CHILD);
//        riskDelegate.getCfFirsApproveMaterialByInfoId(0);
//        riskDelegate.getCfFirsApproveMaterialByInfoUuid("001be7d4-c93d-4fef-a197-d1d2e111445e");
////        riskDelegate.getCfFirsApproveMaterialListByParam(CfFirsApproveMaterial cfFirsApproveMaterial);
//        riskDelegate.getMapByInfoIds(Lists.newArrayList(972480));
//        riskDelegate.updateRejectTypeByInfoId(972480, 1, "");
//        riskDelegate.getCanEditMsg(CfFirstApproveBiz.EditMaterialType.DEFAULT.getCode(), false);
//        riskDelegate.getCfFirsApproveMaterialListByUserId(269381487);
//
//        riskDelegate.listCfSuspectedCaseInfoByPage(0,10);
////        riskDelegate.getCfSuspectedCaseInfoListBySerch(CfSuspectedCaseInfoModel cfSuspectedCaseInfoModel);
//        riskDelegate.getCfSuspectedCaseInfoById(1);
////        riskDelegate.add(CfSuspectedCaseInfo cfSuspectedCaseInfo);
//        riskDelegate.getByLikeIdNumber("BWzUBUV6at5LCH56+R/tTvaqIdOE56aV9Dm5D9VnGz0=");
////        riskDelegate.update(CfSuspectedCaseInfo cfSuspectedCaseInfo);
////        riskDelegate.delete(int id);
//
////        riskDelegate.add(CrowdfundingReport crowdfundingReport);
//        riskDelegate.getCrowdfundingReportListByInfoId(25);
////        riskDelegate.before(String infoUuid, long userId, String userName,CfOperatingRecordEnum.Type type, CfOperatingRecordEnum.Role role, List<CrowdfundingReport> crowdfundingReports);
////        riskDelegate.after(CfOperatingRecord cfOperatingRecord, List<CrowdfundingReport> crowdfundingReports);
//        riskDelegate.updateValid(1, 1835243);
////        riskDelegate.deleteCaseVerificationCache(String infoUuid);
//
////        riskDelegate.isHit(RiskWordCheckContexLt ctx);
//        long[] ca = {1L,2L};
//        riskDelegate.list(ca);
//        riskDelegate.searchByType(1, 1, "1");
////        riskDelegate.trans2ModelList(List<RiskControlWordDO> list);
//        riskDelegate.getTypeName(1);
//
////        riskDelegate.addVerify(RiskUgcVerifyModel model);
////        riskDelegate.deleteVerify(long caseId, UgcTypeEnum typeEnum, long ugcId);
////        riskDelegate.saveCache(com.shuidihuzhu.cf.enums.risk.UgcTypeEnum ugcTypeEnum, long ugcId, long caseId, boolean isSafe);
//        riskDelegate.isSafeSingle(UgcTypeEnum.COMMENT, 1, 1);
//
//
//        commonServiceDelegate.listByAdminUserId(155);
//        commonServiceDelegate.listByActivityId(1);
////        commonServiceDelegate.saveDetail(RuleCollectionDetail detail);
////        commonServiceDelegate.saveCondition(RuleCondition condition);
////        commonServiceDelegate.removeCondition(long id);
//        commonServiceDelegate.getById(1);
////        commonServiceDelegate.clearCache(long activityId);
////        commonServiceDelegate.filter(long activityId, T data);
//        commonServiceDelegate.getRuleConditionById(1);
//
////        ugcDelegate.insertOne(CfTopicShareCommentCount cfTopicShareCommentCount);
//        ugcDelegate.listByTopicIds(Lists.newArrayList(224,226));
//
//        ugcDelegate.getByIdNoCareDeleted(1);
////        ugcDelegate.removeById(Integer id);
////        ugcDelegate.addOne(CfCommentDynamic cfCommentDynamic);
//        ugcDelegate.selectCfCommentDynamicByCommentId(7);
//        ugcDelegate.listByIdsAndStatus(Lists.newArrayList(225), 0);
//        ugcDelegate.listByCommentIds(Lists.newArrayList(408L));
//        ugcDelegate.getListByIdNoCareDeleted( Lists.newArrayList(1,2));
//        ugcDelegate.getByPageNoCareCommentIdList( Lists.newArrayList(85,86) , 1 , 10 , 1);
    }

    @Test
    public void testICfinfoXXXRecordDelegate(){
//        log.error("  getShareCount   :"+cfInfoXXXRecordDelegate.getShareCount(Lists.newArrayList(983848,983841)));
//        log.error("  getByInfoUuid   :"+cfInfoXXXRecordDelegate.getByInfoUuid( "d6f7a4c2-c954-4456-a151-94f790b67acf", 2));
//        log.error("  getByOperationRecordId   :"+cfInfoXXXRecordDelegate.getByOperationRecordId(67446));
//        log.error("  getByOperationRecordIds   :"+cfInfoXXXRecordDelegate.getByOperationRecordIds(Lists.newArrayList(67446L,67444L)));
//        log.error("  getCapitalByInfoUuid   :"+cfInfoXXXRecordDelegate.getCapitalByInfoUuid("3a7179ed-531f-42f7-a8bb-b1722a3ce3e9"));
        log.error("  getCountByInfoId   :"+cfInfoXXXRecordDelegate.getCountByInfoId(983848));
    }
    @Test
    public void testcrowdfundingDelegate(){

    }
    @Test
    public void testIFinanceDelegate(){
        log.error("  getByInfoUuidAndBizType  :"+financeDelegate.getByInfoUuidAndBizType("3a7179ed-531f-42f7-a8bb-b1722a3ce3e9", NewCfCapitalEnum.BizType.DRAW_CASH.getCode()));

    }
    @Test
    public void testIWeiXinDelegate(){
        List<WxCustomRecord> array = Lists.newArrayList();
        WxCustomRecord
                wxCustomRecord = WxCustomRecord.buildText(-1, "",
                RecordConstant.MSG_TYPE_USER_NORMAL,
                1,
                RecordConstant.SUB_BIZ_TYPE_M_ACTIVITY,
                "");
        array.add(wxCustomRecord);
//        log.error("  saveWxCustomRecord  :"+weiXinDelegate.saveWxCustomRecord(array,""));
    }
    @Test
    public void testIUgcDelegate(){
        log.error("  countCrowdFundingVerificationByInfoUuid  :"+ugcDelegate.countCrowdFundingVerificationByInfoUuid("f494d9e3-c813-40ee-959a-0cad9fbc4e9f"));
        log.error("  queryAllCrowdFundingVerificationByInfoUuid  :"+ugcDelegate.queryAllCrowdFundingVerificationByInfoUuid("f494d9e3-c813-40ee-959a-0cad9fbc4e9f"));
    }

}
