package com.shuidihuzhu.cfadmin;

import com.google.common.collect.Lists;
import com.shuidihuzhu.BaseTest;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingAttachmentBiz;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class CrowdfundingAttachmentShardingTest extends BaseTest {
    @Autowired
    private AdminCrowdfundingAttachmentBiz crowdfundingAttachmentBiz;

    @Test
    public void testSharding() {
        int i = 0;
        while(i < 10) {
            crowdfundingAttachmentBiz.deleteByIds(Lists.newArrayList(1884006,1884005), 100);
            i++;
        }
    }
}
