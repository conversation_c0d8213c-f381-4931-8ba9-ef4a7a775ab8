package com.shuidihuzhu;

import com.shuidihuzhu.cf.admin.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2018-09-10  19:37
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
public class BaseTest {

    static {
        System.setProperty("spring.cloud.consul.host", "k8s-discovery-bedin.shuidi.io:80");
    }

    @Before
    public void baseBefore(){
        System.out.println("BaseTest.before");
    }

    @After
    public void baseAfter(){
        System.out.println("BaseTest.after");
    }
}
