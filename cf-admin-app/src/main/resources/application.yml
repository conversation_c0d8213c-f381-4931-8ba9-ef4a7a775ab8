server:
  port: 8084

mybatis:
  type-handlers-package: com.shuidihuzhu.cf.enums
spring:
  main:
    allow-circular-references: true
  config:
    use-legacy-processing: true

auth:
  saas:
    appCode: sl56s1sw
    login:
      interceptorEnable: true
      required-path:
        - /**
      white-list-path:
        - /innerapi/cf/*/**
        - /admin/cf/sea-frequency/add
        - /admin/cftask/check-allow-publish
        - /admin/stat/juanzhan-list-excel
        - /admin/cf/schedule/alarm/run
        - /admin/cf/schedule/alarm/init-datasource
        - /admin/workorder/create-er-ci-work-order-by-fei-shu
        - /admin/operation/back/audit-pass-work-order
        - /admin/smart-assistant/inner/ai-generate/*
        - /admin/cf/ai/backdoor/*
    permission:
      interceptorEnable: true
      required-path:
        - /**
      white-list-path:
        - /innerapi/cf/*/**
        - /admin/cf/sea-frequency/add
        - /admin/cftask/check-allow-publish
        - /admin/stat/juanzhan-list-excel
        - /admin/cf/schedule/alarm/run
        - /admin/cf/schedule/alarm/init-datasource
        - /admin/workorder/create-er-ci-work-order-by-fei-shu
        - /admin/operation/back/audit-pass-work-order
        - /admin/smart-assistant/inner/ai-generate/*
        - /admin/cf/ai/backdoor/*
