package com.shuidihuzhu.cf.admin.controller.api.workorder.von;

import com.shuidihuzhu.client.cf.workorder.helper.base.BaseOrderController;
import com.shuidihuzhu.client.cf.workorder.helper.service.IVonFacade;
import com.shuidihuzhu.common.web.model.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/innerapi/cf/admin/von")
public class VonTestInnerController extends BaseOrderController {

}
