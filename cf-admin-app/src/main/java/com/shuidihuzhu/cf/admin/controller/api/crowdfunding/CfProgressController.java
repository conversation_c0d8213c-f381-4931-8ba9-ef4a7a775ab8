package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import com.shuidihuzhu.cf.admin.util.admin.IntegerUtil;
import com.shuidihuzhu.cf.admin.util.lock.RedisDistributedLock;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingOrderBiz;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdFundingProgressType;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Controller
@RequestMapping(path = "/admin/cf/progress")
public class CfProgressController {
	private static final Logger LOGGER = LoggerFactory.getLogger(CfProgressController.class);

	@Autowired
	private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;
	@Autowired
	private ICrowdfundingDelegate crowdfundingDelegate;
	@Autowired
	private AdminCrowdfundingOrderBiz crowdfundingOrderBiz;
	@Resource
	private UserInfoServiceBiz userInfoServiceBiz;
	@Resource
	private ApplicationContext applicationContext;
	@Resource
	private MsgClientV2Service msgClientV2Service;

	@RequiresPermission("progress:push-official")
	@RequestMapping(path = "/push-official", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
	@ResponseBody
	public Response<Void> pushOfficial(String infoIds, String content, String imageUrls, Integer isSendMsg) {
		try {
			if (StringUtils.isBlank(infoIds) || StringUtils.isBlank(content)) {
				return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
			}
			List<Integer> infoIdList = Splitter.on(",")
					.splitToList(infoIds)
					.stream()
					.map(IntegerUtil::parseInt)
					.collect(Collectors.toList());
			List<CrowdfundingInfo> crowdfundingInfoList = this.adminCrowdfundingInfoBiz.getListByIds(infoIdList);
			for (CrowdfundingInfo crowdfundingInfo : crowdfundingInfoList) {
				if (crowdfundingInfo == null) {
					continue;
				}
				applicationContext.getBean(CfProgressController.class).saveProgress(crowdfundingInfo.getId(), content, imageUrls);
				if (isSendMsg != null && isSendMsg == 1) {
					launchSendMsg(crowdfundingInfo, content);
				}
			}
			return NewResponseUtil.makeSuccess(null);
		} catch (Exception e) {
			LOGGER.error("", e);
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
		}
	}

	@RedisDistributedLock(key = "progress_push_official_saveProgress_infoId_#{infoId}")
	public void saveProgress(Integer infoId, String content, String imageUrls) {
		CrowdFundingProgress crowdFundingProgress = new CrowdFundingProgress();
		crowdFundingProgress.setActivityId(infoId);
		crowdFundingProgress.setUserId(-1);
		crowdFundingProgress.setTitle("官方动态");
		crowdFundingProgress.setContent(content);
		crowdFundingProgress.setImageUrls(imageUrls);
		crowdFundingProgress.setType(CrowdFundingProgressType.OFFICIAL.value());
		this.crowdfundingDelegate.addProgress(crowdFundingProgress);
	}

	@RequiresPermission("progress:push-official-msg")
	@RequestMapping(path = "/push-official-msg", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
	@ResponseBody
	public Response pushOfficialMsg(Integer infoId, String content) {
		LOGGER.info("pushOfficialMsg infoId:{};content:{}", infoId, content);
		try {
			if (infoId == null || infoId <= 0 || StringUtils.isBlank(content)) {
				return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
			}
			CrowdfundingInfo crowdfundingInfo = this.adminCrowdfundingInfoBiz.getFundingInfoById(infoId);
			if (crowdfundingInfo == null) {
				return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
			}
			launchSendMsg(crowdfundingInfo, content);
			return NewResponseUtil.makeSuccess(null);
		} catch (Exception e) {
			LOGGER.error("", e);
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
		}
	}

	private void launchSendMsg(CrowdfundingInfo crowdfundingInfo, String content) {
		new Thread() {
			public void run() {
				sendMsg(crowdfundingInfo, content);
			}
		}.start();
	}

	private void sendMsg(CrowdfundingInfo crowdfundingInfo, String content) {
		int limit = 3000;
		int page = 0;
		Set<Long> userIdSet = Sets.newHashSet();
		while (true) {
			List<CrowdfundingOrder> crowdfundingOrderList = crowdfundingOrderBiz.getByPage(crowdfundingInfo.getId(), page * limit, limit);
			LOGGER.info("page :{};crowdfundingOrderList.size : {}", page, crowdfundingOrderList.size());
			if (CollectionUtils.isEmpty(crowdfundingOrderList)) {
				break;
			}
			for (CrowdfundingOrder crowdfundingOrder : crowdfundingOrderList) {
				if (crowdfundingOrder.getUserId() > 0) {
					userIdSet.add(crowdfundingOrder.getUserId());
				}
			}
			try {
				Thread.sleep(100L);
			} catch (InterruptedException e) {
				LOGGER.error("", e);
			}
			page++;
		}
		if (CollectionUtils.isNotEmpty(userIdSet)) {
			String title = StringUtils.trimToEmpty(crowdfundingInfo.getTitle());
			String msgContent = StringEscapeUtils
					.unescapeJava(content.length() > 50 ? content.substring(0, 50) + "..." : content);

			Map<Long, Map<Integer, String>> mapMap = Maps.newHashMap();
			List<UserInfoModel> userInfoModels = userInfoServiceBiz.getUserInfoByUserIdBatch(Lists.newArrayList(userIdSet));
			for (UserInfoModel userInfoModel : userInfoModels) {
				if (userInfoModel != null) {
					Map<Integer, String> params = Maps.newConcurrentMap();
					params.put(1, crowdfundingInfo.getInfoId());

					mapMap.put(userInfoModel.getUserId(), params);
				}
			}

			if (MapUtils.isNotEmpty(mapMap)) {
				String modelNum0 = "template905";
				msgClientV2Service.sendWxParamsMsg(modelNum0, mapMap);
			}
		}
	}
}
