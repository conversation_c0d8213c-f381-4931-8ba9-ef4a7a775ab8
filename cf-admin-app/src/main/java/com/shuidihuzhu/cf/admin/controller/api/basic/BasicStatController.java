package com.shuidihuzhu.cf.admin.controller.api.basic;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.delegate.AnalyticsDelegate;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("admin/cf/basic/stat")
public class BasicStatController {

    @Autowired
    private AnalyticsDelegate analyticsDelegate;

    @ApiOperation("sea后台统计接口")
    @PostMapping("save")
    public Response<Void> save(@RequestParam String event, @RequestParam int userId, @RequestParam String extString){
        Map<String, Object> ext = (Map<String, Object>) JSON.parse(extString); //已检查过
        analyticsDelegate.track(event, userId, false, ext);
        return NewResponseUtil.makeSuccess(null);
    }
}
