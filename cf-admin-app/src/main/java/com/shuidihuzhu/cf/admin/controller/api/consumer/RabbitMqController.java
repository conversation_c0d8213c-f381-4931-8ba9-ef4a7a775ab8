package com.shuidihuzhu.cf.admin.controller.api.consumer;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.shuidihuzhu.client.cf.admin.client.RabbitMqFeignClient;
import com.shuidihuzhu.client.cf.admin.model.RabbitMqFeignParam;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/3/5
 */
@Slf4j
@RestController
public class RabbitMqController implements ApplicationContextAware, InitializingBean, RabbitMqFeignClient {

    private static final Map<String, MessageListener> listenerMap = Maps.newHashMap();

    @Setter
    private ApplicationContext applicationContext;

    @Override
    public boolean consumer(@RequestBody Message message) {
        log.info("RabbitMqController.consumer message:{}", message);
        if (message == null || message.getPayload() == null || StringUtils.isBlank(message.getTags())) {
            return false;
        }
        return handleMsg(message.getTags(), message.getPayload());
    }

    @Override
    public boolean consumerMsg(@RequestBody RabbitMqFeignParam param) {
        log.info("RabbitMqController.consumerMsg message:{}", param);
        return handleMsg(param.getTags(), param.getData());
    }

    private boolean handleMsg(String tag, Object object) {
        if (StringUtils.isBlank(tag) || object == null) {
            return false;
        }
        MessageListener messageListener = listenerMap.get(tag);
        if (messageListener == null) {
            return false;
        }

        ConsumerMessage consumerMessage = new ConsumerMessage();
        Object actualObject = getActualParam(messageListener, object);
        if (object == null) {
            return false;
        }
        consumerMessage.setPayload(actualObject);

        ConsumeStatus consumeStatus = messageListener.consumeMessage(consumerMessage);
        return consumeStatus != null && consumeStatus == ConsumeStatus.CONSUME_SUCCESS;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Map<String, MessageListener> maps = applicationContext.getBeansOfType(MessageListener.class);
        RocketMQListener rocketMQListener = null;
        String[] tags = null;
        for (Map.Entry<String, MessageListener> entry : maps.entrySet()) {
            rocketMQListener = entry.getValue().getClass().getAnnotation(RocketMQListener.class);
            if (rocketMQListener == null || (tags = rocketMQListener.tags()) == null) {
                continue;
            }

            for (String tag : tags) {
                listenerMap.put(tag, entry.getValue());
            }
        }
    }

    private Object getActualParam(MessageListener messageListener, Object object) {
        Type[] types = messageListener.getClass().getGenericInterfaces();
        if (types == null || types.length != 1) {
            return null;
        }
        Type[] params = ((ParameterizedType) types[0]).getActualTypeArguments();
        if (params == null || params.length != 1) {
            return null;
        }

        //map类型
        if (params[0] instanceof ParameterizedType) {
            return object;
        }

        Class actualClass = (Class) params[0];
        if (actualClass == String.class || actualClass == Integer.class || actualClass == Long.class) {
            return object;
        }

        return JSON.parseObject(JSON.toJSONString(object), actualClass);//已检查过
    }
}
