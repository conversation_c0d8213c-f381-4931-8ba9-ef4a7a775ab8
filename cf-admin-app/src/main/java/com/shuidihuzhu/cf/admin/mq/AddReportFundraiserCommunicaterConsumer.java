package com.shuidihuzhu.cf.admin.mq;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.IReportCommunicaterListService;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.CfReportPageEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfReportRelationEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportCommunicaterDO;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2019/12/17 下午2:41
 * @desc
 */
@Slf4j
@Service
@RocketMQListener(id = "ADD_REPORT_FUNDRAISER_COMMUNICATER", tags = "ADD_REPORT_FUNDRAISER_COMMUNICATER", topic = MQTopicCons.CF)
public class AddReportFundraiserCommunicaterConsumer implements MessageListener<CrowdfundingInfo> {

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;

    @Autowired
    private IReportCommunicaterListService reportCommunicaterListService;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdfundingInfo> mqMessage) {
        if(Objects.isNull(mqMessage) || Objects.isNull(mqMessage.getPayload())){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        CrowdfundingInfo info = mqMessage.getPayload();
        int caseId = info.getId();
        long userId = info.getUserId();

        UserInfoModel userInfo = userInfoServiceBiz.getUserInfoByUserId(userId);
        if(Objects.isNull(userInfo) || StringUtils.isEmpty(userInfo.getCryptoMobile())){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        CfReportCommunicaterDO communicater = reportCommunicaterListService.queryByMobile(caseId, 0, userInfo.getCryptoMobile());
        if(Objects.nonNull(communicater)){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        CfReportCommunicaterDO communicaterDO = new CfReportCommunicaterDO();
        communicaterDO.setCaseId(caseId);
        communicaterDO.setReportId(0);
        communicaterDO.setType(CfReportPageEnum.FUNDRAISER.getKey());
        communicaterDO.setRelationKey(CfReportRelationEnum.RAISER.getKey());
        communicaterDO.setRelationValue(CfReportRelationEnum.RAISER.getValue());
        communicaterDO.setMobile(userInfo.getCryptoMobile());
        communicaterDO.setOperatorId(1L);
        communicaterDO.setManualAdd(false);

        reportCommunicaterListService.insert(communicaterDO);

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
