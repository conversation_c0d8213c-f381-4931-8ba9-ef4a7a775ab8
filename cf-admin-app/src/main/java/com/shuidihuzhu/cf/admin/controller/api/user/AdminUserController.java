package com.shuidihuzhu.cf.admin.controller.api.user;

import com.shuidihuzhu.cf.client.feign.cipher.constants.DesensitizeEnum;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/9/7
 */

@Slf4j
@RestController
@RequestMapping(path = "/admin/cf/account/")
public class AdminUserController {

    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;
    @Autowired
    private MaskUtil maskUtil;


    @PostMapping("list-by-name")
    @ResponseBody
    public Response<List<AdminUserAccountModel>> listByName(@RequestParam String name) {
        AuthRpcResponse<List<AdminUserAccountModel>> response = seaAccountClientV1.listByUserNameLike(name);
        List<AdminUserAccountModel> result = response.getResult();
        Optional.ofNullable(result)
                .filter(CollectionUtils::isNotEmpty)
                .ifPresent(r -> {
                    r.forEach(item -> {
                        item.setMobile(StringUtils.EMPTY);
                        String idCard = item.getIdCard();
                        if (StringUtils.isNotBlank(idCard)) {
                            item.setIdCard(null);
                            item.setIdCardMask(maskUtil.buildByDecryptStrAndType(idCard, DesensitizeEnum.IDCARD));
                        }
                    });
                });
        return NewResponseUtil.makeSuccess(result);
    }

    @PostMapping("list-by-mis")
    @ResponseBody
    public Response<List<AdminUserAccountModel>> listByMis(@RequestParam String mis,
                                                           @ApiParam("是否掩码 0否 1是") @RequestParam(value = "isCoverKey",required = false,defaultValue = "1" ) Integer isCoverKey) {
        AuthRpcResponse<List<AdminUserAccountModel>> accountsByMisLike = seaAccountClientV1.getUserAccountsByMisLike(mis,isCoverKey);
        Optional.ofNullable(accountsByMisLike)
                .filter(AuthRpcResponse::isSuccess)
                .filter(r -> CollectionUtils.isNotEmpty(r.getResult()))
                .ifPresent(r -> r.getResult().forEach(item -> item.setMobile(StringUtils.EMPTY)));
        return NewResponseUtil.makeSuccess(accountsByMisLike.getResult());
    }

}
