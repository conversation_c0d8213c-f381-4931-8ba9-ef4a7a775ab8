package com.shuidihuzhu.cf.admin.controller.api.workorder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.alps.feign.ocean.OceanApiClient;
import com.shuidihuzhu.alps.feign.ocean.OceanApiRequest;
import com.shuidihuzhu.alps.feign.ocean.OceanApiResponse;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseReasonEntityBiz;
import com.shuidihuzhu.cf.biz.risk.BlackListHighRiskWorkOrderRecordBiz;
import com.shuidihuzhu.cf.client.material.model.MaterialPlanVersion;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationRecordDTO;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.risk.BlackListHighRiskWorkOrderRecordDto;
import com.shuidihuzhu.cf.risk.client.rpc.DiseaseClient;
import com.shuidihuzhu.cf.risk.model.risk.DiseaseAmountResultRecord;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditOperationItem;
import com.shuidihuzhu.cf.vo.approve.InitialAuditOCRResultVO;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.admin.delegate.HospitalDelegate;
import com.shuidihuzhu.cf.admin.util.lock.RedisDistributedLock;
import com.shuidihuzhu.cf.biz.admin.UserCommentBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingAttachmentBiz;
import com.shuidihuzhu.cf.client.material.model.RaiseBasicInfoModel;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.delegate.SeaAccountDelegate;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.admin.AdminCfInitialAuditCheckInfoEnum;
import com.shuidihuzhu.cf.enums.admin.UserCommentSourceEnum;
import com.shuidihuzhu.cf.enums.approve.MaterialImageOCRLocationEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.enums.maskcode.MaskCodePageEnum;
import com.shuidihuzhu.cf.enums.wx.WxBotSendMsgTypeEnum;
import com.shuidihuzhu.cf.model.admin.UserComment;
import com.shuidihuzhu.cf.model.admin.workorder.CfAuditContentEditParam;
import com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterialsVo;
import com.shuidihuzhu.cf.model.crowdfunding.caseRepeat.InitialRepeatCaseView;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.cf.risk.client.risk.AccidentCaseClient;
import com.shuidihuzhu.cf.risk.model.enums.risk.TriggerTimingEnum;
import com.shuidihuzhu.cf.risk.model.risk.Participate;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyLog;
import com.shuidihuzhu.cf.service.notice.workwx.WorkWeiXinContentBuilder;
import com.shuidihuzhu.cf.service.resulthandler.ResultSensitiveHandler;
import com.shuidihuzhu.cf.service.workorder.initialAudit.*;
import com.shuidihuzhu.cf.vo.admin.initialAudit.*;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderTypeFeignClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.ClassifyTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OneTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@RefreshScope
@RestController
@Slf4j
@RequestMapping("admin/cf/initial-audit")
public class InitialAuditController {
    @Autowired
    private ApplicationService applicationService;
    @Autowired
    private InitialAuditSearchService searchService;
    @Autowired
    private InitialAuditOperateService operateService;
    @Autowired
    private InitialAuditRejectSettingsService rejectSettingsService;
    @Autowired
    private UserCommentBiz commentBiz;
    @Autowired
    private CommonOperationRecordClient commonOperationRecordClient;
    @Resource
    private AlarmClient alarmClient;
    @Autowired
    private SeaAccountDelegate organizationDelegate;
    @Autowired
    private HospitalDelegate hospitalDelegate;
    @Autowired
    private InitialAuditTwiceEndCaseService twiceEndCaseService;
    @Autowired
    private InitialAuditRepeatService initialAuditRepeatService;

    @Resource
    private AdminCrowdfundingAttachmentBiz adminCrowdfundingAttachmentBiz;
    @Autowired
    private InitialAuditBrainService auditBrainService;

    @Resource
    private CfWorkOrderClient workOrderClient;
    @Resource
    private AccidentCaseService accidentCaseService;
    @Resource
    private OceanApiClient oceanApiClient;
    @Resource
    private UserCommentBiz userCommentBiz;
    @Resource
    private CfRefuseReasonEntityBiz cfRefuseReasonEntityBiz;
    @Resource
    private DiseaseClient diseaseClient;
    @Resource
    private BlackListHighRiskWorkOrderRecordBiz blackListHighRiskWorkOrderRecordBiz;
    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Autowired
    private CfWorkOrderTypeFeignClient cfWorkOrderTypeFeignClient;

    @RequiresPermission("initial-audit:query-server-charge")
    @ApiOperation("查询案例收取服务费情况")
    @PostMapping("query-case-query-server-charge")
    public Response<Integer> queryCaseServerCharge(@RequestParam("caseId") int caseId) {

        long adminUserId = ContextUtil.getAdminLongUserId();
        if(adminUserId <= 0) {
            return NewResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        return searchService.queryCaseServiceCharge(caseId);

    }

    @RequiresPermission("initial-audit:case-base-info")
    @ApiOperation("案例基础信息-查询")
    @PostMapping("query-last-chushen-work-case-detail")
    @ResultSensitiveHandler
    public Response<InitialAuditCaseDetail> queryCaseDetailLastWorkOrder(@RequestParam("caseId") int caseId) {
        List<Integer> workTypeList = Optional.ofNullable(cfWorkOrderTypeFeignClient.getByOneLevel(OneTypeEnum.chuci.getType()))
                .filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList());
        Response<WorkOrderVO> lastWorkOrder = workOrderClient.getLastWorkOrderByTypes(caseId, workTypeList);
        if (lastWorkOrder.notOk()) {
            return NewResponseUtil.makeFail(lastWorkOrder.getMsg());
        }
        WorkOrderVO data = lastWorkOrder.getData();
        if (data == null) {
            return NewResponseUtil.makeSuccess(null);
        }
        //找到最新的初审工单 (电话审核、审核)
        InitialAuditCaseDetail initialAuditCaseDetail = searchService.queryCaseDetail(data.getWorkOrderId(), caseId);
        Optional.ofNullable(initialAuditCaseDetail)
                .ifPresent(r -> r.setPrePatientIdCard(StringUtils.EMPTY));
        Optional.ofNullable(initialAuditCaseDetail)
                .filter(r -> Objects.nonNull(r.getFirstApproveCaseInfo()))
                .filter(r -> Objects.nonNull(r.getFirstApproveCaseInfo().getSpecialPrePoseDetail()))
                .ifPresent(r -> r.getFirstApproveCaseInfo().getSpecialPrePoseDetail().setMobile(StringUtils.EMPTY));
        return NewResponseUtil.makeSuccess(initialAuditCaseDetail);
    }

    @RequiresPermission("initial-audit:query-case-detail")
    @ApiOperation("案例基础信息-查询")
    @PostMapping("query-case-detail")
    @ResultSensitiveHandler
    public Response<InitialAuditCaseDetail> queryCaseDetail(@RequestParam("workOrderId") long workOrderId, @RequestParam("caseId") int caseId) {
        InitialAuditCaseDetail initialAuditCaseDetail = searchService.queryCaseDetail(workOrderId, caseId);
        Optional.ofNullable(initialAuditCaseDetail)
                .ifPresent(r -> r.setPrePatientIdCard(StringUtils.EMPTY));
        Optional.ofNullable(initialAuditCaseDetail)
                .filter(r -> Objects.nonNull(r.getFirstApproveCaseInfo()))
                .filter(r -> Objects.nonNull(r.getFirstApproveCaseInfo().getSpecialPrePoseDetail()))
                .ifPresent(r -> r.getFirstApproveCaseInfo().getSpecialPrePoseDetail().setMobile(StringUtils.EMPTY));
        return ResponseUtil.makeSuccess(initialAuditCaseDetail);
    }

    @RequiresPermission("initial-audit:edit-case-base-info")
    @ApiOperation("案例基础信息-修改")
    @PostMapping("edit-case-base-info")
    public Response editCaseBase(@RequestParam("param") String param) {
        InitialAuditOperationItem.EditBaseInfo editBaseInfoParam;

        try {
            editBaseInfoParam = JSON.parseObject(param, InitialAuditOperationItem.EditBaseInfo.class);//已检查过
        } catch (Exception e) {
            log.error("初审工单-修改案例,参数解析错误。msg:{}", param);
            return ResponseUtil.makeFail("参数解析错误");
        }
        editBaseInfoParam.setUserId(ContextUtil.getAdminUserId());
        return ResponseUtil.makeSuccess(operateService.editCaseBaseInfo(editBaseInfoParam,""));
    }

    @RequiresPermission("initial-audit:edit-case-base-info-v2")
    @ApiOperation("案例基础信息-修改")
    @PostMapping("edit-case-base-info-v2")
    public Response<BaseInfoModel> editCaseBaseV2(@RequestParam("param") String param) {
        CfAuditContentEditParam editBaseInfoParam;

        try {
            editBaseInfoParam = JSON.parseObject(param, CfAuditContentEditParam.class);//已检查过
        } catch (Exception e) {
            log.error("初审工单-修改案例,参数解析错误。msg:{}", param);
            return ResponseUtil.makeFail("参数解析错误");
        }
        editBaseInfoParam.setUserId(ContextUtil.getAdminUserId());

        CrowdfundingInfo crowdfundingInfo = operateService.editCaseBaseInfo(editBaseInfoParam, editBaseInfoParam.getChangeModify());
        if(crowdfundingInfo == null){
            return ResponseUtil.makeFail("案例不存在");
        }

        List<CrowdfundingAttachment> attachments = adminCrowdfundingAttachmentBiz.getAttachmentsByType(crowdfundingInfo.getId(), AttachmentTypeEnum.ATTACH_CF);

        BaseInfoModel baseInfoModel = new BaseInfoModel();
        baseInfoModel.setCrowdfundingInfo(crowdfundingInfo);
        if(CollectionUtils.isNotEmpty(attachments)){
            baseInfoModel.setAttachments(attachments.stream().map(r->new CrowdfundingAttachmentVo(r)).collect(Collectors.toList()));
        }
        baseInfoModel.setHeadPictureUrl(editBaseInfoParam.getHeadPictureUrl());

        return NewResponseUtil.makeSuccess(baseInfoModel);
    }

    @RequiresPermission("initial-audit:get-case-refuse-list")
    @ApiOperation("案例-可驳回项查询")
    @PostMapping("get-case-refuse-list")
    public Response<InitialAuditOperationItem.RejectOptionSet> getCaseRefuseList(@RequestParam("caseId") int caseId,
                                      @RequestParam(required = false, name = "workOrderId", defaultValue = "0") long workOrderId) {
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
        if (Objects.isNull(crowdfundingInfo)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        InitialAuditOperationItem.RejectOptionSet rejectOptionSet = searchService.queryRejectList(caseId, workOrderId);
        authenticityCaseHandle(rejectOptionSet, crowdfundingInfo);
        return NewResponseUtil.makeSuccess(rejectOptionSet);
    }

    private void authenticityCaseHandle(InitialAuditOperationItem.RejectOptionSet rejectOptionSet, CrowdfundingInfo crowdfundingInfo) {
        if (Objects.isNull(rejectOptionSet)) {
            return;
        }
        if (CollectionUtils.isNotEmpty(rejectOptionSet.getCreditInfoRejectOption())) {
            List<CfRefuseReasonTag> creditListCon = convertRefuseList(rejectOptionSet.getCreditInfoRejectOption(), crowdfundingInfo);
            rejectOptionSet.setCreditInfoRejectOption(creditListCon);
        }
        if (CollectionUtils.isNotEmpty(rejectOptionSet.getBaseInfoRejectOption())) {
            List<CfRefuseReasonTag> contentListCon = convertRefuseList(rejectOptionSet.getBaseInfoRejectOption(), crowdfundingInfo);
            rejectOptionSet.setBaseInfoRejectOption(contentListCon);
        }
        if (CollectionUtils.isNotEmpty(rejectOptionSet.getDiBaoRejectOption())) {
            List<CfRefuseReasonTag> contentListCon = convertRefuseList(rejectOptionSet.getDiBaoRejectOption(), crowdfundingInfo);
            rejectOptionSet.setDiBaoRejectOption(contentListCon);
        }
        if (CollectionUtils.isNotEmpty(rejectOptionSet.getFirstApproveRejectOption())) {
            List<CfRefuseReasonTag> contentListCon = convertRefuseList(rejectOptionSet.getFirstApproveRejectOption(), crowdfundingInfo);
            rejectOptionSet.setFirstApproveRejectOption(contentListCon);
        }
    }

    private List<CfRefuseReasonTag> convertRefuseList(List<CfRefuseReasonTag> list, CrowdfundingInfo crowdfundingInfo) {
        List<Integer> relationEntities = cfRefuseReasonEntityBiz.getRefuseReasonEntitiesByMaterialPlanId(crowdfundingInfo.getMaterialPlanId());
        // 过滤掉驳回理由
        list.forEach(option -> {
            List<CfRefuseReasonEntity> restEntities = option.getEntityList()
                    .stream()
                    .filter(entity -> relationEntities.contains(entity.getId()))
                    .collect(Collectors.toList());
            option.setEntityList(restEntities);
            List<Integer> ids = restEntities.stream()
                    .map(CfRefuseReasonEntity::getId)
                    .collect(Collectors.toList());
            String join = Joiner.on(",").join(ids);
            option.setReasonIds(join);
        });
        list = list.stream().filter(option -> CollectionUtils.isNotEmpty(option.getEntityList())).collect(Collectors.toList());
        return list;
    }

    @RequiresPermission("initial-audit:handle-v2")
    @ApiOperation("案例信息-操作")
    @PostMapping("handle-initial-audit-v2")
    @RedisDistributedLock(key = "handle_initial_audit_v2_#{handleParam.caseId}")
    public Response  handleInitialAuditV2(@RequestBody() InitialAuditOperationItem.HandleCaseInfoParam handleParam) {

        handleParam.setUserId(ContextUtil.getAdminUserId());

        try {
            return operateService.handleWorkOrder(handleParam);
        } catch (Exception e) {
            log.info("初审处理异常:param:{}", handleParam, e);
            return ResponseUtil.makeFail(e.getMessage());
        }
    }

    @RequiresPermission("initial-audit:query-operation-history")
    @ApiOperation("案例-操作记录查询")
    @PostMapping("query-initial-audit-operation-history")
    public Response queryInitialAuditOperationHistory(@RequestParam("caseId") int caseId,
                                                      @RequestParam("current") int current,
                                                      @RequestParam("pageSize") int pageSize) {

        return ResponseUtil.makeSuccess(searchService.queryInitialAuditOperationHistory(caseId, current, pageSize));
    }

    @RequiresPermission("initial-audit:send-message")
    @PostMapping("send-message")
    @ApiOperation("案例-发短信")
    public Response sendSmsMessage(@RequestParam("param") String param) {
        InitialAuditOperationItem.InitialAuditSmsMsg initialMsg = null;

        try {
            initialMsg = JSON.parseObject(param, InitialAuditOperationItem.InitialAuditSmsMsg.class);//已检查过
        } catch (Exception e) {
            log.error("初审工单-发短信,参数解析错误。msg:{}", param);
            return ResponseUtil.makeFail("参数解析错误");
        }
        initialMsg.setUserId(ContextUtil.getAdminUserId());
        operateService.sendSmsMessage(initialMsg);
        return ResponseUtil.makeSuccess("");
    }


    // ***** 前置驳回项的配置页面 begin
    @RequiresPermission("operation:query-list")
    @ApiOperation("前置驳回项-查询")
    @PostMapping("operation/query-list")
    public Response queryRejectListByDateType(@RequestParam("dataType") int dateType) {

        return ResponseUtil.makeSuccess(rejectSettingsService.queryDataTypeList(dateType));
    }

    @RequiresPermission("operation:branch-page/query")
    @ApiOperation("前置驳回-启用项、弃用项 页签的查询")
    @PostMapping("operation/branch-page/query")
    public Response queryBranchPageQuery(@RequestParam("tagId") int tagId,
                                         @RequestParam("rejectOptionType") int rejectOptionType) {
        return ResponseUtil.makeSuccess(rejectSettingsService.queryRefuseEntity(tagId, rejectOptionType));
    }

    @RequiresPermission("operation:enable-or-disable")
    @ApiOperation("前置驳回-理由 启用项、弃用项  删除")
    @PostMapping("operation/enable-or-disable")
    public Response enableOrDisableOperation(@RequestParam("entityId") int entityId,
                                             @RequestParam("rejectOptionType") int rejectOptionType) {

        String msg = rejectSettingsService.handleRejectOption(entityId, rejectOptionType, ContextUtil.getAdminUserId());

        return StringUtils.isBlank(msg) ? ResponseUtil.makeSuccess("") : ResponseUtil.makeFail(msg);
    }

    @RequiresPermission("operation:verify-idcard")
    @ApiOperation("初审的-身份证校验")
    @PostMapping("/verify-idcard")
    public Response<Integer> verifyIdCard(@RequestParam("caseId") int caseId,
                                          @RequestParam(name = "workOrderId", required = false) long workOrderId) {
        log.info("初审的身份证校验. caseId:{}, userId:{}", caseId, ContextUtil.getAdminUserId());
        return searchService.verifyIdCard(caseId, ContextUtil.getAdminUserId(), workOrderId);
    }

    @RequiresPermission("operation:query-patient-idcard")
    @ApiOperation("初审的-查看患者身份证")
    @PostMapping("/query-patient-idcard")
    public Response queryPatientIdCard(@RequestParam("caseId") int caseId, @RequestParam("workOrderId") long workOrderId,
                                       MaskCodePageEnum maskCodePageEnum) {
        log.info("初审的身份证校验. caseId:{}, userId:{}", caseId, ContextUtil.getAdminUserId());
        return searchService.queryPatientIdCard(ContextUtil.getAdminUserId(), caseId, workOrderId, maskCodePageEnum);
    }

    @ApiOperation("重新审核")
    @RequiresPermission("work-order:initial-audit-reprocess")
    @PostMapping("reprocess")
    public Response<Long> reprocessWorkOrder(
            @ApiParam("工单id") @RequestParam long workOrderId,
            @ApiParam("领取人id") @RequestParam long userId,
            @ApiParam("评论") @RequestParam(required = false, defaultValue = "") String comment) {
        return operateService.reprocess(workOrderId, userId, comment);
    }

    @ApiOperation("前置信息验真")
    @RequiresPermission("work-order:initial-audit-reprocess")
    @PostMapping("check-info")
    public Response<List<AdminCfInitialAuditCheckInfoVO>> checkInfo(@ApiParam("案例id") @RequestParam("caseId") int caseId,
                                                                    @ApiParam("工单id") @RequestParam("checkType") int checkType,
                                                                    @ApiParam("待校验名称") @RequestParam("checkName") String checkName) {
        Boolean check = hospitalDelegate.checkInfo(checkName);
        if (check == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
        }
        int checkResult = check ? 1 : 0;
        AdminCfInitialAuditCheckInfoVO infoVO = new AdminCfInitialAuditCheckInfoVO(checkType, checkName, checkResult);
        return new Response<>(0, "", Lists.newArrayList(infoVO));
    }

    @ApiOperation("前置信息验真提交")
    @RequiresPermission("work-order:initial-audit-reprocess")
    @PostMapping("check-info-submit")
    public Response<Void> checkInfoSubmit(@ApiParam("工单id") @RequestParam long workOrderId,
                                          @ApiParam("案例id") @RequestParam("caseId") int caseId,
                                          @ApiParam("处理备注") @RequestParam("dataList") String dataList) {
        if (StringUtils.isEmpty(dataList)) {
            return new Response<>(0, "");
        }
        List<AdminCfInitialAuditCheckInfoVO> infoList = JSON.parseArray(dataList, AdminCfInitialAuditCheckInfoVO.class);//已检查过
        for (AdminCfInitialAuditCheckInfoVO checkInfo : infoList) {
            searchService.updateCheckInfo(caseId, checkInfo.getCheckType(), checkInfo.getCheckName());
        }
        // 保存操作记录
        String operateDesc = this.getOperateDesc(infoList);
        searchService.addOperationRecord(caseId, workOrderId, operateDesc);
        return new Response<>(0, "");
    }

    @ApiOperation("OCR识别报错")
    @PostMapping("report-error")
    @RequiresPermission("work-order:report-error")
    public Response<Void> reportError(@ApiParam("工单id") @RequestParam long workOrderId,
                                      @ApiParam("案例id") @RequestParam("caseId") int caseId,
                                      @ApiParam("图片url") @RequestParam("imageUrl") String imageUrl,
                                      @ApiParam("报错位置") @RequestParam("content") MaterialImageOCRLocationEnum locationEnum){
        // 添加ugc的评论
        UserComment comment = new UserComment();
        int adminUserId = ContextUtil.getAdminUserId();
        comment.setOperatorId(adminUserId);
        comment.setCaseId(caseId);
        comment.setCommentSource(UserCommentSourceEnum.INITIAL_AUDIT.getCode());
        comment.setCommentType(UserCommentSourceEnum.CommentType.INITIAL_AUDIT_OCR_REPORT_ERROR.getCode());
        comment.setOperateMode(UserCommentSourceEnum.CommentType.INITIAL_AUDIT_OCR_REPORT_ERROR.getDesc());
        comment.setWorkOrderId(workOrderId);
        comment.setComment("");
        comment.setOperateDesc(locationEnum.getShowMsg());
        commentBiz.insert(comment);

        commonOperationRecordClient.create()
                .buildBasicPlatform(workOrderId, adminUserId, OperationActionTypeEnum.OCR_APPROVE_REPORT_ERROR)
                .buildExt("location", locationEnum.getMsg())
                .buildExt("locationCode", String.valueOf(locationEnum.getCode()))
                .buildExt("url", imageUrl)
                .buildExt("caseId", String.valueOf(caseId))
                .save();

        String content = WorkWeiXinContentBuilder.create()
                .subject("【图片识别结果报错】")
                .payload("案例ID", caseId)
                .payload("报错来源", "初审详情页")
                .payload("工单ID", workOrderId)
                .payload("图片url", imageUrl)
                .payload("报错位置", locationEnum.getShowMsg())
                .payload("报错时间", DateUtil.getCurrentDateStr())
                .payload("报错人", organizationDelegate.getNameWithOrgByUserId(adminUserId))
                .build();

        alarmClient.sendByGroup("wx-alarm-prod-20200302-0002", content);

        return NewResponseUtil.makeSuccess(null);
    }

    @ApiOperation("ocr识别医疗材料中疾病名称&姓名一致性识别结果报错操作")
    @PostMapping("report-error-operate")
    public Response<Void> reportErrorOperate(@RequestBody InitialReportErrorParam errorParam) {

        UserComment comment = new UserComment();
        int adminUserId = ContextUtil.getAdminUserId();
        comment.setOperatorId(adminUserId);
        comment.setCaseId(errorParam.getCaseId());
        comment.setCommentSource(UserCommentSourceEnum.INITIAL_AUDIT.getCode());
        comment.setWorkOrderId(errorParam.getWorkOrderId());
        comment.setComment("");
        comment.setOperateDesc("");
        comment.setCommentType(errorParam.getErrorType());
        //当选择其他原因,原因内容为手动填写
        if(errorParam.getErrorType() == UserCommentSourceEnum.CommentType.ORDER_REASON.getCode()){
            if(StringUtils.length(errorParam.getErrorContent()) > 100){
                 return NewResponseUtil.makeFail("最多输入100字");
            }
            comment.setOperateMode(Objects.requireNonNullElse(errorParam.getErrorContent(), ""));
        }else {
            comment.setOperateMode(UserCommentSourceEnum.CommentType.getByValue(errorParam.getErrorType()));
        }

        log.info("ocr识别医疗材料中疾病名称&姓名一致性识别结果报错操作 comment:{}",comment);
        commentBiz.insert(comment);

        auditBrainService.recordErrorLog(errorParam);

//        String content = WorkWeiXinContentBuilder.create()
//                .subject("【图片识别结果报错】")
//                .payload("案例ID", caseId)
//                .payload("报错来源", "初审详情页")
//                .payload("工单ID", workOrderId)
//                .payload("图片url", imageUrl)
//                .payload("报错位置", locationEnum.getShowMsg())
//                .payload("报错时间", DateUtil.getCurrentDateStr())
//                .payload("报错人", organizationDelegate.getNameWithOrgByUserId(adminUserId))
//                .build();
//        alarmClient.sendByGroup("wx-alarm-prod-20200302-0002", content);

        return NewResponseUtil.makeSuccess(null);
    }

    private String getOperateDesc(List<AdminCfInitialAuditCheckInfoVO> infoList) {
        StringBuilder strBuilder = new StringBuilder();
        for (int i = 0; i < infoList.size(); i++) {
            AdminCfInitialAuditCheckInfoVO infoVO = infoList.get(i);
            strBuilder.append(AdminCfInitialAuditCheckInfoEnum.getDescByType(infoVO.getCheckType()));
            strBuilder.append("：");
            strBuilder.append(infoVO.getCheckName());
            strBuilder.append("---");
            strBuilder.append(infoVO.getCheckResult() == 1 ? "真医院名称" : "疑似假医院名称");
            if (i != infoList.size() - 1) {
                strBuilder.append("<br>");
            }
        }
        return strBuilder.toString();
    }

    @RequiresPermission("initial-audit:twice-end-case")
    @ApiOperation("二次结束案例")
    @PostMapping("twice-end-case")
    public Response<String> twiceEndCase(@RequestBody InitialAuditOperationItem.HandleCaseInfoParam param) {

        param.setUserId(ContextUtil.getAdminUserId());

        try {
            twiceEndCaseService.twiceEndCase(param);
        } catch (Exception e) {
            log.info("二次结束案例异常 param:{}", param, e);
            return ResponseUtil.makeFail(e.getMessage());
        }

        return ResponseUtil.makeSuccess("");
    }

    @RequiresPermission("initial-audit:can-twice-end-case")
    @ApiOperation("是否可以二次结束案例")
    @PostMapping("can-twice-end-case")
    public Response<Boolean> canTwiceEndCase(@RequestBody InitialAuditOperationItem.HandleCaseInfoParam param) {

        return ResponseUtil.makeSuccess(twiceEndCaseService.canTwiceEndCase(param.getCaseId(), param.getWorkOrderId()));
    }

    @RequiresPermission("initial-audit:query-case-initial-repeat-info")
    @ApiOperation("查询案例初审重复的情况")
    @PostMapping("query-case-initial-repeat-info")
    public Response<List<InitialRepeatCaseView>> queryRepeatCaseDetail(@RequestParam int caseId,
                                                                       @RequestParam(required = false, defaultValue = "0") long workOrderId
            , @RequestParam(required = false, defaultValue = "0") int tag) {

        return NewResponseUtil.makeSuccess(initialAuditRepeatService.queryRepeatCaseDetail(caseId, workOrderId, ContextUtil.getAdminLongUserId(), tag));
    }

    @RequiresPermission("initial-audit:save-initial-repeat-snapshot")
    @ApiOperation("初审处理是保存重复数据快照")
    @PostMapping("save-initial-repeat-snapshot")
    public Response<String> saveInitialRepeatSnapshot(@RequestParam String data) {
        InitialRepeatCaseView.InitialRepeatSnapshot snapshot = JSONObject.parseObject(data, InitialRepeatCaseView.InitialRepeatSnapshot.class);
        snapshot.setUserId(ContextUtil.getAdminUserId());
        initialAuditRepeatService.saveInitialRepeatSnapshot(snapshot);
        return ResponseUtil.makeSuccess("");
    }

    @PostMapping("select-disease-snapshot")
    public Response<DiseaseStrategyResult> selectDiseaseStrategySnapshot(int caseId, long workOrderId,
                                                                         @RequestParam(required = false) Integer strategyType) {

        return ResponseUtil.makeSuccess(auditBrainService.selectDiseaseStrategySnapshot(caseId,
                ContextUtil.getAdminUserId(), workOrderId, strategyType));
    }

    @ApiOperation("调用疾病策略的")
    @PostMapping("judge-disease-strategy")
    public Response<DiseaseStrategyResult> judgeDiseaseStrategy(@RequestBody InitialDiseaseParam diseaseParam) {
        diseaseParam.setUserId(ContextUtil.getAdminUserId());

        return ResponseUtil.makeSuccess(auditBrainService.judgeDiseaseStrategy(diseaseParam));
    }

    @ApiOperation("查找调用疾病策略的接口记录")
    @PostMapping("select-disease-strategy-log")
    @RequiresPermission("initial:disease-strategy-log")
    public Response<List<DiseaseStrategyLog>> selectDiseaseStrategyLog(int caseId) {
        return ResponseUtil.makeSuccess(auditBrainService.selectDiseaseStrategyLog(caseId));
    }

    @RequiresPermission("initial:user-first-commit-disease")
    @ApiOperation("查找用户第一次填写的疾病名称")
    @PostMapping("select-raise-basic-info")
    public Response<RaiseBasicInfoModel> selectRaiseBasicInfo(int caseId) {
        RaiseBasicInfoModel raiseBasicInfoModel = auditBrainService.selectRaiseBasicInfo(caseId);
        Optional.ofNullable(raiseBasicInfoModel)
                .ifPresent(r -> r.setSelfMobile(StringUtils.EMPTY));
        return ResponseUtil.makeSuccess(raiseBasicInfoModel);
    }



    @ApiOperation("填写的加操作备注")
    @PostMapping("add-comment")
    public Response<String> addComment(int userId,int caseId,String commentDesc,String infoUuid,long workOrderId,
                                       @RequestParam(value = "comment", defaultValue = "", required = false) String comment,
                                       @RequestParam(value = "operateMsg", defaultValue = "", required = false) String operateMsg,
                                       @RequestParam(value = "commentTypeCode", defaultValue = "0", required = false) int commentTypeCode) {
        operateService.addComment(userId,caseId,commentDesc,infoUuid,workOrderId, comment, operateMsg, commentTypeCode);
        return NewResponseUtil.makeSuccess();
    }

    @ApiOperation("前置图文信息切换")
    @PostMapping("query-ai-data")
    public Response<CfAiMaterialsVo> queryAiData(int caseId,@RequestParam(value = "workOrderId",required = false,defaultValue = "0") long workOrderId) {
        return ResponseUtil.makeSuccess(searchService.queryAiData(caseId,workOrderId));
    }


    @ApiOperation("判断是否可以提交")
    @PostMapping("can-submit-result")
    public Response<InitialAuditOperationItem.CanSubmitResult> validateErSubmit(@RequestBody InitialAuditOperationItem.HandleCaseInfoParam handleParam) {

        InitialAuditOperationItem.CanSubmitResult submitResult = new InitialAuditOperationItem.CanSubmitResult();
        submitResult.setCanSubmit(operateService.validateErCi(handleParam));

        return ResponseUtil.makeSuccess(submitResult);
    }

    @ApiOperation("是否为事故案例")
    @PostMapping("accident-case")
    public Response<List<Integer>> accidentCase(int caseId, long workOrderId) {
        return NewResponseUtil.makeSuccess(accidentCaseService.accidentCase(caseId, workOrderId, ContextUtil.getAdminUserId()));
    }

    @ApiOperation("是否为事故案例保存快照")
    @PostMapping("accident-case-snapshot")
    public Response<Void> accidentCase(@RequestParam(value = "accidentList") List<Integer> accidentList, long workOrderId) {
        accidentCaseService.saveInitialRepeatSnapshot(accidentList, workOrderId, ContextUtil.getAdminUserId());
        return NewResponseUtil.makeSuccess(null);
    }

    @ApiOperation("ocr识别医疗材料内容")
    @PostMapping("ocr-medical-material")
    public Response<OceanApiResponse> ocrMedicalMaterial(@RequestParam String url, @RequestParam long workOrderId) {
        OperationRecordDTO record = commonOperationRecordClient.getLastByBizIdAndActionTypes(workOrderId, OperationActionTypeEnum.OCR_APPROVE);
        if (record != null && MapUtils.isNotEmpty(record.getExtMap()) && StringUtils.isNotEmpty(record.getExtMap().get("result"))) {
            String resultJson = record.getExtMap().get("result");
            return NewResponseUtil.makeSuccess(JSON.parseObject(resultJson, OceanApiResponse.class));
        }
        if (StringUtils.isEmpty(url)) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        Map<String, String> body = new HashMap<>();
        body.put("url", url);
        body.put("userId", "10007");
        body.put("token", "b87ffe8f80e1a59a");
        body.put("type", "chou");
        OceanApiRequest oceanApiRequest = new OceanApiRequest();
        oceanApiRequest.setTag("ai-medical-info-extract");
        oceanApiRequest.setUserId("10007");
        oceanApiRequest.setToken("32b32cebc2a7cb87");
        oceanApiRequest.setBody(JSONObject.toJSONString(body));
        Response<OceanApiResponse> agent = oceanApiClient.agent(oceanApiRequest);
        if (Objects.nonNull(agent) && agent.ok()) {
            commonOperationRecordClient.create()
                    .buildBizId(workOrderId)
                    .buildActionType(OperationActionTypeEnum.OCR_APPROVE)
                    .buildExt("result", JSON.toJSONString(agent.getData()))
                    .buildExt("workOrderId", String.valueOf(workOrderId))
                    .buildExt("imageUrl", url)
                    .save();
        }
        return agent;
    }

    @ApiOperation("初审情况驳回项操作记录")
    @PostMapping("add-clear-reject-log")
    @RequiresPermission("initial-audit:add-clear-reject-log")
    public Response<Void> addClearRejectLog(@RequestParam int caseId, @RequestParam long workOrderId, @RequestParam String operateDesc) {
        UserComment comment = new UserComment();
        comment.setOperatorId(ContextUtil.getAdminLongUserId());
        comment.setCaseId(caseId);
        comment.setCommentSource(UserCommentSourceEnum.INITIAL_AUDIT.getCode());
        comment.setCommentType(UserCommentSourceEnum.CommentType.CLEAR_INITIAL_AUDIT_REJECT.getCode());
        comment.setOperateMode(UserCommentSourceEnum.CommentType.CLEAR_INITIAL_AUDIT_REJECT.getDesc());
        comment.setWorkOrderId(workOrderId);
        comment.setComment("");
        comment.setOperateDesc("【清空当前审核结果】：原因" + operateDesc);
        userCommentBiz.insert(comment);
        return NewResponseUtil.makeSuccess(null);
    }

    @ApiOperation("初审是否可以自动清空驳回项")
    @PostMapping("auto-clear-reject")
    @RequiresPermission("initial-audit:auto-clear-reject")
    public Response<Boolean> autoClearReject(@RequestParam int caseId, @RequestParam long workOrderId) {
        Response<List<WorkOrderVO>> listResponse = workOrderClient.queryByCaseAndTypes(caseId, Lists.newArrayList(WorkOrderType.highriskshenhe.getType(), WorkOrderType.shenhe.getType(), WorkOrderType.ai_erci.getType()));
        if (Objects.isNull(listResponse) || CollectionUtils.isEmpty(listResponse.getData())) {
            return NewResponseUtil.makeSuccess(false);
        }
        WorkOrderVO workOrderVO = listResponse.getData()
                .stream()
                .filter(f -> f.getWorkOrderId() != workOrderId)
                .findFirst()
                .orElse(null);
        if (Objects.isNull(workOrderVO) || !Objects.equals(workOrderVO.getOrderType(), WorkOrderType.shenhe.getType())) {
            return NewResponseUtil.makeSuccess(false);
        }
        return NewResponseUtil.makeSuccess(true);
    }

    @ApiOperation("初审查看目标合理性的计算逻辑")
    @PostMapping("get-disease-amount-reasonable-record")
    @RequiresPermission("initial-disease-amount-reasonable-record")
    public Response<List<DiseaseAmountResultRecord>> getDiseaseAmountReasonableRecord(@RequestParam int caseId) {
        return diseaseClient.getAmountResultRecordByCaseId(caseId);
    }

    @ApiOperation("获取初审自定义驳回项")
    @PostMapping("get-custom-refuse")
    @RequiresPermission("initial-audit:get-custom-refuse")
    public Response<List<CfRefuseReasonCustomEntity>> getCustomRefuse(@RequestParam long workOrderId) {
        return NewResponseUtil.makeSuccess(cfRefuseReasonEntityBiz.getCustomByWorkOrderId(workOrderId));
    }

    @ApiOperation("获取是否因为黑名单生成高风险工单")
    @PostMapping("get-blacklist-high-risk")
    @RequiresPermission("initial-audit:get-blacklist-high-risk")
    public Response<BlackListHighRiskWorkOrderRecordDto> getBlacklistHighRisk(@RequestParam long workOrderId) {
        return NewResponseUtil.makeSuccess(blackListHighRiskWorkOrderRecordBiz.initialHighRiskInfo(workOrderId));
    }
}
