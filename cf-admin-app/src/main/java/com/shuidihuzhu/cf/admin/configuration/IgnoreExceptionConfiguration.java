package com.shuidihuzhu.cf.admin.configuration;

import com.shuidihuzhu.common.web.aop.ShuidiExceptionHandler;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2019年02月28日10:06:24
 *
 * 请求参数的日志脱敏操作
 *
 */
@Slf4j
@Component
public class IgnoreExceptionConfiguration implements InitializingBean {

    @Override
    public void afterPropertiesSet() throws Exception {
        //告知前端，数字格式化异常
        ShuidiExceptionHandler.addIgnoreClass(NumberFormatException.class, ErrorCode.NUMBER_FORMAT_EXCEPTION);
    }

}
