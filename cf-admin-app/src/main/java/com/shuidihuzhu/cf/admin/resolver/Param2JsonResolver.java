package com.shuidihuzhu.cf.admin.resolver;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.admin.annotation.SdParamParser;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

/**
 * Author: wuxinlong
 * Date: 16/10/10 19:46
 */
public class Param2JsonResolver implements HandlerMethodArgumentResolver {
	private static final Logger logger = LoggerFactory.getLogger(Param2JsonResolver.class);

	@Override
	public boolean supportsParameter(MethodParameter parameter) {
		return parameter.hasParameterAnnotation(SdParamParser.class);
	}

	@Override
	public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer,
	                              NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
		SdParamParser sdParamParser = parameter.getParameterAnnotation(SdParamParser.class);
		String parameterName = parameter.getParameterName();
		Class<?> parameterClass = sdParamParser.className();
		Object ret = null;
		try {
			ret = JSON.parseObject((String) webRequest.getParameter(parameterName), parameterClass);//已检查过
		} catch (Exception e) {
			logger.error("参数解析错误:", e);
		}
		return ret;
	}
}
