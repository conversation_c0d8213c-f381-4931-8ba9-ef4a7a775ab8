package com.shuidihuzhu.cf.admin.mq.record;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfQuestionnaireBiz;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.model.QuestionnaireRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @DATE 2020/2/12
 */
@Service
@RocketMQListener(id = "chuci_"+com.shuidihuzhu.cf.constants.MQTagCons.CF_INITIAL_AUDIT_OPERATION_MSG,
        group = "record_chuci_audit_group",
        tags = com.shuidihuzhu.cf.constants.MQTagCons.CF_INITIAL_AUDIT_OPERATION_MSG,
        topic = MQTopicCons.CF)
@Slf4j
public class RecordInitialAuditConsumer implements MessageListener<InitialAuditItem.InitialAuditOperation> {

    @Autowired
    private CfQuestionnaireBiz cfQuestionnaireBiz;

    @Autowired
    private AdminCfInfoExtBiz cfInfoExtBiz;

    @Autowired
    private ApplicationService applicationService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<InitialAuditItem.InitialAuditOperation> mqMessage) {

        InitialAuditItem.InitialAuditOperation operation = mqMessage.getPayload();

        log.info("RecordInitialAuditConsumer operation={}",operation);

        if (operation == null){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        int caseId = operation.getCaseId();

        CfInfoExt ext = cfInfoExtBiz.getByCaseId(caseId);
        boolean casePass = (ext != null && FirstApproveStatusEnum.isPassed(FirstApproveStatusEnum.parse(ext.getFirstApproveStatus()))) ;

        if (!casePass){
            log.info("case no pass caseid={}",caseId);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        QuestionnaireRecord qr = cfQuestionnaireBiz.canSend(caseId,CfQuestionnaireBiz.chuci_source);

        if (qr.isFlag()){
            Date sendTime = getSendTime();
            long id = cfQuestionnaireBiz.save(qr.getUserId(),caseId,qr.getChannel(),qr.getCard(),qr.getMobile(),CfQuestionnaireBiz.chuci_source, sendTime);
            qr.setId(id);
            cfQuestionnaireBiz.sendDelayMQForMsg(qr, (sendTime.getTime() - System.currentTimeMillis())/ 1000L);
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private Date getSendTime() {

        if (applicationService.isDevelopment()) {
            return DateUtils.addMinutes(new Date(), 3);
        }

        Date now = new Date();
        Date todayZero = DateUtils.truncate(now, Calendar.DATE);
        Date today_06_00 = DateUtils.addHours(todayZero, 6);
        Date today_18_30 = DateUtils.addMinutes(DateUtils.addHours(todayZero, 18), 30);

        if (now.before(today_06_00)) {
            return DateUtils.addHours(todayZero, 9);
        } else if (now.before(today_18_30)) {
            return DateUtils.addHours(now, 3);
        } else {
            return DateUtils.addHours(DateUtils.addDays(todayZero, 1), 9);
        }
    }
}
