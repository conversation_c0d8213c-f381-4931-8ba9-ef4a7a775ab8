package com.shuidihuzhu.cf.admin.controller.api.risk;


import com.shuidihuzhu.cf.client.base.page.v1.model.PaginationListVO;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonRecord;
import com.shuidihuzhu.cf.delegate.SeaAccountDelegate;
import com.shuidihuzhu.cf.model.risk.BanShareWhiteListDO;
import com.shuidihuzhu.cf.service.BanShareWhiteListService;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;
import retrofit2.http.POST;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.List;

@Slf4j
@RefreshScope
@RestController
@Api("禁止转发白名单接口")
@RequestMapping(path = "/admin/crowdfunding/risk/ban-share-white-list")
public class BanShareWhiteListController {

    @Autowired
    private BanShareWhiteListService banShareWhiteListService;
    @Autowired
    private SeaAccountDelegate seaAccountDelegate;

    @PostMapping("/add")
    @ApiOperation("白名单添加接口")
    public Response<Void> insertInfo(@RequestParam("cfUserId") long cfUserId,@RequestParam("maxCount") int maxCount,
                                     @RequestParam("reason") @Min(value = 5, message = "请输入原因,要求必须输入5个以上汉字,不能纯字")
                                     @Max(value = 64) String reason) {

        if (cfUserId == 0L) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        //获取操作人id
        long operatorId = ContextUtil.getAdminLongUserId();
        //获取操作人姓名
        String name = seaAccountDelegate.getNameByLongUserId(operatorId);
        banShareWhiteListService.addInfo(cfUserId, maxCount, reason, name, operatorId);
        return NewResponseUtil.makeSuccess();
    }

    @PostMapping("/update-count")
    @ApiOperation("编辑阈值及更新有效时间")
    public Response<Void> updateMaxCount(long id, int maxCount) {

        long operatorId = ContextUtil.getAdminLongUserId();
        banShareWhiteListService.updateMaxCount(id, maxCount, operatorId);
        return NewResponseUtil.makeSuccess();
    }

    @PostMapping("/select-list")
    @ApiOperation("获取白名单列表")
    public Response<PaginationListVO<BanShareWhiteListDO>> selectInfo(@RequestParam(required = false, defaultValue = "0") long cfUserId,
                                                                      @RequestParam(required = false, defaultValue = "") String name,
                                                                      @RequestParam(required = false, defaultValue = "0") int state,
                                                                      @RequestParam(required = false, defaultValue = "1") int current,
                                                                      @RequestParam(required = false, defaultValue = "10") int pageSize) {
        if (cfUserId < 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        return banShareWhiteListService.getList(cfUserId, name, state, pageSize, current);
    }

    @PostMapping("/get-reason")
    @ApiOperation("获取添加原因")
    public Response<String> getReason(long id) {
        if (id < 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        return NewResponseUtil.makeSuccess(banShareWhiteListService.getReason(id));
    }

    @PostMapping("/get-list-record")
    @ApiOperation("查看操作日志")
    public Response<List<WonRecord>> getListRecord(long id) {

        if (id < 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        return NewResponseUtil.makeSuccess(banShareWhiteListService.getListRecord(id));
    }

}
