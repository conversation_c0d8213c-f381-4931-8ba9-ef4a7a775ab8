package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;


import com.shuidihuzhu.cf.model.crowdfunding.CfRule;
import com.shuidihuzhu.cf.service.crowdfunding.report.CfReportService;
import com.shuidihuzhu.common.web.model.Response;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@Slf4j
@RefreshScope
@RequestMapping("/innerapi/cf/config")
public class CfCaseVisitConfigFeignController {

	@Autowired
	private CfReportService cfReportService;


	@ApiOperation("修改或者更新一个案例的访问控制")
	@PostMapping(value = "/rule")
	public Response<String> update(@ApiParam("配置")@RequestBody CfRule cfRule){

		return cfReportService.rule(cfRule);

	}

}
