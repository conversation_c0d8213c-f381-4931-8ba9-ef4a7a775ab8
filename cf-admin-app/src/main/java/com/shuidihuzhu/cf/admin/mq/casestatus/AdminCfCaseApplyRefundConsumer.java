package com.shuidihuzhu.cf.admin.mq.casestatus;

import com.shuidihuzhu.cf.admin.mq.common.BaseMessageConsumer;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.service.crowdfunding.DealWithStatusService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@RocketMQListener(id = MQTagCons.CF_CASE_APPLY_REFUND,
        group = "cf-admin-" + MQTagCons.CF_CASE_APPLY_REFUND + "-deal",
        tags = MQTagCons.CF_CASE_APPLY_REFUND,
        topic = MQTopicCons.CF)
@Slf4j
public class AdminCfCaseApplyRefundConsumer extends BaseMessageConsumer<Integer> implements MessageListener<Integer> {

    @Autowired
    private DealWithStatusService dealWithStatusService;

    @Override
    protected boolean handle(ConsumerMessage<Integer> consumerMessage) {
        Integer caseId = consumerMessage.getPayload();
        try {
            dealWithStatusService.touchHandleNoLongerProcess(caseId);
        } catch (Exception e) {
            log.error("touchHandleNoLongerProcess fail", e);
        }

        return true;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
