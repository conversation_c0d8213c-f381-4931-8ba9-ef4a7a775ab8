package com.shuidihuzhu.cf.admin.controller.api.caseinfo;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.finance.IFinanceDelegate;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @date 2019-04-18  17:03
 *
 * 资金补偿信息管理
 * <a href="https://wiki.shuiditech.com/pages/viewpage.action?pageId=176128001">WIKI</a>
 */
@Api("资金补偿信息管理")
@RestController
@RequestMapping("/admin/cf/caseinfo/capital-compensation")
public class InfoCapitalCompensationController {

    @Resource
    private IFinanceDelegate financeDelegate;

    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;

    @ApiOperation("资金补偿信息-获取")
    @PostMapping("get")
    @RequiresPermission("capital-compensation:get")
    public Response get(@RequestParam String infoUuid) {
        CfInfoSimpleModel fundingInfo = crowdfundingDelegate.getCfInfoSimpleModel(infoUuid);
        if (fundingInfo == null) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        return financeDelegate.getCfInfoCapitalCompensationDO(fundingInfo.getId());
    }
}
