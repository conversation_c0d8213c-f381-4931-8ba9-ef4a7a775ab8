package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminTwModifyService;
import com.shuidihuzhu.client.cf.admin.client.CfEntranceDisplayFeignClient;
import com.shuidihuzhu.client.cf.admin.model.AdminEntranceStatus;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description: 入口是否展示
 * @Author: panghairui
 * @Date: 2022/3/9 3:03 下午
 */
@Slf4j
@Controller
public class AdminEntranceDisplayController implements CfEntranceDisplayFeignClient {

    @Resource
    private AdminTwModifyService adminTwModifyService;

    @Override
    @ResponseBody
    public Response<List<AdminEntranceStatus>> twEntranceIsDisplay(@RequestParam("infoIds") List<String> infoIds) {

        if (CollectionUtils.isEmpty(infoIds)) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR_IS_NULL);
        }

        List<AdminEntranceStatus> response = adminTwModifyService.twEntranceIsDisplay(infoIds);
        if (CollectionUtils.isEmpty(response)) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR_IS_NULL);
        }

        return NewResponseUtil.makeSuccess(response);
    }

}
