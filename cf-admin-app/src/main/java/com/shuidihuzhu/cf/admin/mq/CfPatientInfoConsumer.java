package com.shuidihuzhu.cf.admin.mq;


import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CfAIPhotoModel;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @Author: liujiawei
 * @Date: 2018/7/21  12:21
 */
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.CF_ADD_OR_UPDATE_PATIENT_MSG, tags = MQTagCons.CF_ADD_OR_UPDATE_PATIENT_MSG, topic = MQTopicCons.CF)
public class CfPatientInfoConsumer implements MessageListener<CfAIPhotoModel> {

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfAIPhotoModel> message) {
        // 转移到 AdminWorkOrderCreateConsumer 工单创建时调用ai接口
        return ConsumeStatus.CONSUME_SUCCESS;
    }

}