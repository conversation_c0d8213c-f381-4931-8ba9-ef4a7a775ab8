package com.shuidihuzhu.cf.admin.mq.ai;

import com.shuidihuzhu.alps.feign.config.OceanApiMqConfig;
import com.shuidihuzhu.alps.feign.fs.CallbackBody;
import com.shuidihuzhu.alps.feign.fs.CallbackQuestion;
import com.shuidihuzhu.alps.feign.fs.CalloutRecordMq;
import com.shuidihuzhu.cf.service.workorder.cailiao.CaiAuditRejectAICallService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Author: wangpeng
 * @Date: 2022/7/12 16:18
 * @Description:
 */
@Slf4j
@Service
@RocketMQListener(id = "AI_CALL_INTENTION" + OceanApiMqConfig.TOPIC,
        tags = "sdchou_caishen", topic = OceanApiMqConfig.TOPIC)
public class CfApproveAuditAICallIntentionConsumer implements MessageListener<CallbackBody> {

    @Resource
    private CaiAuditRejectAICallService caiAuditRejectAICallService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CallbackBody> mqMessage) {
        log.info("CfApproveAuditAICallIntentionConsumer is begin {}", mqMessage);
        CallbackBody payload = mqMessage.getPayload();
        if (Objects.isNull(payload) || StringUtils.isEmpty(payload.getCallId())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        CallbackQuestion preQuestion = payload.getPreQuestion();
        if (Objects.isNull(preQuestion) || StringUtils.isEmpty(preQuestion.getTitle())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        caiAuditRejectAICallService.updateCallIntention(payload);
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
