package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;


import com.shuidihuzhu.client.cf.admin.client.PrDetailsPageCollocateFeignClient;
import com.shuidihuzhu.client.cf.admin.model.PrDetailsPageVo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
public class PrDetailsPageCollocateFeignController implements PrDetailsPageCollocateFeignClient {

    @Override
    public Response<PrDetailsPageVo> prSelectCase(int caseId) {
        return NewResponseUtil.makeSuccess(null);
    }
}
