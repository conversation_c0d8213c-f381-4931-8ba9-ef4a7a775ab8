package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminReportStatService;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminReportStatServiceV2;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.model.report.ReportStatTotal;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2019/4/11
 */
@RestController
@RequestMapping(path = "/admin/cf/report-stat")
public class AdminReportStatController {

    @Autowired
    private AdminReportStatService reportStatService;
    @Autowired
    private AdminReportStatServiceV2 reportStatServiceV2;


    @RequestMapping(path = "all", method = RequestMethod.POST)
    @RequiresPermission("report:report-stat")
    public Response all() {
        return NewResponseUtil.makeSuccess(reportStatService.getReportStatTotal());
    }


    @RequestMapping(path = "user", method = RequestMethod.POST)
    @RequiresPermission("report:report-stat")
    public Response user() {
        return NewResponseUtil.makeSuccess(reportStatService.getReportStatUser());
    }

    @RequestMapping(path = "v2/all-data", method = RequestMethod.POST)
    @RequiresPermission("report:report-stat")
    public Response<ReportStatTotal.ReportWorkOrderTotalCount> allV2() {
        return NewResponseUtil.makeSuccess(reportStatServiceV2.queryReportTotalCount());
    }


    @RequestMapping(path = "v2/user-data", method = RequestMethod.POST)
    @RequiresPermission("report:report-stat")
    public Response<List<ReportStatTotal.ReportWorkOrderUserProcess>> userV2() {
        return NewResponseUtil.makeSuccess(reportStatServiceV2.queryReportUserCount());
    }

}
