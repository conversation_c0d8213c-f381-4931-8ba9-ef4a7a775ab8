package com.shuidihuzhu.cf.admin.util.excel.importfromexcel;

import com.shuidihuzhu.cf.admin.util.admin.IntegerUtil;
import com.shuidihuzhu.cf.admin.util.excel.importfromexcel.vo.CrowdfundingExcelVo;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by ahrievil on 2017/1/8.
 */
public class ExcelUtilForImport {

    public List<CrowdfundingExcelVo> readXlsx(InputStream is) throws Exception{
        XSSFWorkbook xssfWorkbook = new XSSFWorkbook(is);
        XSSFSheet xssfSheet = xssfWorkbook.getSheet("案例收集表");
        //XSSFWorkbook代表一个excel
        List<CrowdfundingExcelVo> result = new ArrayList<CrowdfundingExcelVo>();
            for(int rowNum = 2;rowNum <= xssfSheet.getLastRowNum();rowNum++){
                Row xssfRow = xssfSheet.getRow(rowNum);
                CrowdfundingExcelVo crowdfundingExcelVo = new CrowdfundingExcelVo();
                crowdfundingExcelVo.setLinNum(rowNum - 1);
                crowdfundingExcelVo.setPayeeName(getCellValue(xssfRow.getCell(6)).trim());
                crowdfundingExcelVo.setPayeeMobile(getCellValue(xssfRow.getCell(7)).trim());
                crowdfundingExcelVo.setTargetAmount(IntegerUtil.parseInt(getCellValue(xssfRow.getCell(8)).equals("")?"0":getCellValue(xssfRow.getCell(8)).trim())*100);
                crowdfundingExcelVo.setTitle(getCellValue(xssfRow.getCell(9)).trim());
                crowdfundingExcelVo.setContent(getCellValue(xssfRow.getCell(10)).trim());
                String[] attachmentsArray;
                if (!getCellValue(xssfRow.getCell(11)).trim().equals("")) {
                    attachmentsArray = getCellValue(xssfRow.getCell(11)).replace("，",",").trim().split(",");
                    List<String> attachmentsList = new ArrayList<>();
                    attachmentsList.addAll(Arrays.asList(attachmentsArray));
                    crowdfundingExcelVo.setAttachments(attachmentsList);
                }
                if (!crowdfundingExcelVo.getPayeeName().equals("")&&!crowdfundingExcelVo.getPayeeMobile().equals("")) {
                    result.add(crowdfundingExcelVo);
                }
            }
        return result;
    }
    public String getCellValue(Cell cell) {
        String cellValue = "";
        if (cell != null) {
            switch (cell.getCellType()) {
                case XSSFCell.CELL_TYPE_NUMERIC:
                    cell.setCellType(XSSFCell.CELL_TYPE_STRING);
                    cellValue = cell.getStringCellValue();
                    break;
                case XSSFCell.CELL_TYPE_STRING:
                    cellValue = cell.getStringCellValue();
                    break;
                case XSSFCell.CELL_TYPE_BOOLEAN:
                    cellValue = String.valueOf(cell.getBooleanCellValue());
                    break;
                case XSSFCell.CELL_TYPE_FORMULA:
                    cellValue = String.valueOf(cell.getCellFormula());
                    break;
                case XSSFCell.CELL_TYPE_BLANK:
                    cellValue = "";
                    break;
                case XSSFCell.CELL_TYPE_ERROR:
                    cellValue = "";
                    break;
                default:
                    cellValue = cell.toString().trim();
                    break;
            }
        }
        return cellValue.trim();
    }
}
