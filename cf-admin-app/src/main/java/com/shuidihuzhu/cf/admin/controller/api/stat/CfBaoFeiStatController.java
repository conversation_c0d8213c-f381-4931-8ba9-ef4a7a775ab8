package com.shuidihuzhu.cf.admin.controller.api.stat;

import com.shuidihuzhu.cf.service.CfBaoFeiStatService;
import com.shuidihuzhu.client.cf.admin.client.CfBaoFeiStatFeignClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2021/8/30
 */
@Slf4j
@RestController
public class CfBaoFeiStatController implements CfBaoFeiStatFeignClient {

    @Resource
    private CfBaoFeiStatService cfBaoFeiStatService;

    @Override
    public Response<Void> run() {
        cfBaoFeiStatService.baofeiStat();
        return NewResponseUtil.makeSuccess(null);
    }
}
