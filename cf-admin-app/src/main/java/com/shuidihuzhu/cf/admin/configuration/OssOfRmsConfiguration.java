package com.shuidihuzhu.cf.admin.configuration;

import com.shuidihuzhu.common.web.util.aliyun.OssClientWrapper;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/6/14 2:29 PM
 */
@Configuration
public class OssOfRmsConfiguration {

    @Bean("oss-aliyun.cf-api")
    @ConfigurationProperties("rms.oss.aliyun.cf-api.cf-admin-api")
    public OssClientWrapper ossClientWrapper() {
        return new OssClientWrapper();
    }

}
