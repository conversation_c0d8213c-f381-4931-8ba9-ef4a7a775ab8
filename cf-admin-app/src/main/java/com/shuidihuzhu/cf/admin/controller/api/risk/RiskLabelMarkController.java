package com.shuidihuzhu.cf.admin.controller.api.risk;

import com.shuidihuzhu.cf.client.ugc.wonrecord.WonRecord;
import com.shuidihuzhu.cf.domain.label.core.LabelNodeModel;
import com.shuidihuzhu.cf.domain.label.risk.Label;
import com.shuidihuzhu.cf.domain.label.risk.RiskLabelAuditMarkParam;
import com.shuidihuzhu.cf.domain.label.risk.RiskLabelMarkParam;
import com.shuidihuzhu.cf.domain.label.risk.RiskLabelMarkRecord;
import com.shuidihuzhu.cf.service.label.risk.RiskLabelService;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Api("风控标签标记")
@RestController
@Slf4j
@RequestMapping(path = "/admin/cf/risk-label")
public class RiskLabelMarkController {

    @Autowired
    private RiskLabelService riskLabelService;

    @ApiOperation("通用标签标记")
    @PostMapping("mark")
    public Response<Boolean> mark(@RequestBody RiskLabelMarkParam param){
        final long operatorId = ContextUtil.getAdminLongUserId();
        param.getMarkData().setOperatorId(operatorId);
        final boolean mark = riskLabelService.mark(param);
        return NewResponseUtil.makeSuccess(mark);
    }

    @ApiOperation("通用标签标记记录")
    @PostMapping("get-mark-record")
    public Response<List<RiskLabelMarkRecord>> getMarkRecord(
            @RequestParam int caseId,
            @RequestParam(required = false, defaultValue = "0") int markType){
        return riskLabelService.getMarkRecord(caseId, markType);
    }

    @ApiOperation("获取最新的标签修正信息")
    @PostMapping("get-last-mark-fix-record")
    public Response<RiskLabelMarkRecord> getLastMarkFixRecord(@RequestParam int caseId){
        return riskLabelService.getLastMarkFixRecord(caseId);
    }

    @ApiOperation("根据案例id查询标签修正记录")
    @PostMapping("get-mark-fix-operate-record-by-case-id")
    public Response<List<WonRecord>> getMarkFixOperateRecordByCaseId(@RequestParam int caseId) {
        return riskLabelService.getMarkFixOperateRecordByCaseId(caseId);
    }

    @ApiOperation("根据单个标签id获取标签节点树")
    @PostMapping("get-label-tree-by-id")
    public Response<LabelNodeModel> getLabelNodeById(@RequestParam long id){
        return riskLabelService.getLabelNodeById(id);
    }

    @ApiOperation("根据标签uuid列表查询标签列表")
    @PostMapping("get-label-list-by-uuid-list")
    public Response<List<Label>> getLabelByUuidList(@RequestParam List<String> uuidList){
        return riskLabelService.getLabelByUuidList(uuidList);
    }

    @ApiOperation("审核标记-生成标记工单")
    @PostMapping("audit-mark-work-order")
    @RequiresPermission("risk-label:audit-mark-work-order")
    public Response<Void> auditMarkWorkOrder(@RequestBody RiskLabelAuditMarkParam param) {
        riskLabelService.auditMark(param);
        return NewResponseUtil.makeSuccess();
    }

    @ApiOperation("根据标签id列表获取对应标签节点树")
    @PostMapping("get-simple-label-tree-by-label-id-list")
    public Response<List<LabelNodeModel>> getSimpleLabelTreeByLabelIdList(@RequestParam List<Long> labelIdList){
        final List<LabelNodeModel> labelNodeByIdList = riskLabelService.getSimpleLabelNodeByIdList(labelIdList);
        return NewResponseUtil.makeSuccess(labelNodeByIdList);
    }


}
