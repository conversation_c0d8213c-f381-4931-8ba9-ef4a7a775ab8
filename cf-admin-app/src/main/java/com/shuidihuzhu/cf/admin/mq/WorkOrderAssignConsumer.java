package com.shuidihuzhu.cf.admin.mq;

import com.shuidihuzhu.cf.biz.crowdfunding.WorkOrderAssignBiz;
import com.shuidihuzhu.cf.client.adminpure.constants.ReportCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.service.stream.*;
import com.shuidihuzhu.cf.service.report.ReportOperationService;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @DATE 2019/11/22
 */

@Service
@Slf4j
@RocketMQListener(id = CfClientMQTagCons.WORK_ORDER_ASSIGN_SUCC,
        group = "cf-admin-" + CfClientMQTagCons.WORK_ORDER_ASSIGN_SUCC,
        tags = CfClientMQTagCons.WORK_ORDER_ASSIGN_SUCC,
        topic = MQTopicCons.CF)
public class WorkOrderAssignConsumer implements MessageListener<WorkOrderVO> {

    @Autowired
    private WorkOrderAssignBiz workOrderAssignBiz;

    @Autowired
    private StreamBizService streamBizService;

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @Autowired
    private ReportOperationService reportOperationService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<WorkOrderVO> mqMessage) {

        log.info("WORK_ORDER_ASSIGN_SUCC ：{}", mqMessage.getPayload());

        WorkOrderVO vo = mqMessage.getPayload();
        if (vo == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        String key = workOrderAssignBiz.getKey(Long.valueOf(vo.getOperatorId()).intValue(), vo.getOrderType());//已检查过

        if (workOrderAssignBiz.getByKey(key) != null) {
            //返回信息
            workOrderAssignBiz.setResult(key);
        }

        // 新领单推送
        streamBizService.onAssignSuccess(vo);

        promoteReport(vo);


        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private void promoteReport(WorkOrderVO vo) {
        try {
            int orderType = vo.getOrderType();
            int caseId = vo.getCaseId();
            if (caseId <= 0 || orderType <= 0) {
                Response<WorkOrderVO> orderResp = cfWorkOrderClient.getWorkOrderById(vo.getWorkOrderId());
                WorkOrderVO order = orderResp.getData();
                caseId = order.getCaseId();
                orderType = order.getOrderType();
            }
            if (orderType == WorkOrderType.casefirstreport.getType() ||
                    orderType == WorkOrderType.casehistoryreport.getType()) {
                reportOperationService.operation(caseId, ReportCons.ActionType.OrderAssigned, vo.getOperatorId());
            }
        } catch (Exception exception) {
            log.error("reportOperationService.operation", exception);
        }
    }
}
