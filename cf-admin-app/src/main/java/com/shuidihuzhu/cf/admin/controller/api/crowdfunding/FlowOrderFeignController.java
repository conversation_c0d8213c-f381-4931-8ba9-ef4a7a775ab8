package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderFlowBiz;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowDetailView;
import com.shuidihuzhu.cf.service.crowdfunding.WorkFlowStaffService;
import com.shuidihuzhu.client.cf.admin.client.FlowOrderFeignClient;
import com.shuidihuzhu.client.cf.admin.model.FlowOrderResult;
import com.shuidihuzhu.client.cf.admin.model.WorkOrderFlowLogVo;
import com.shuidihuzhu.client.cf.admin.model.WorkOrderFlowMoneyBackExtVo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.shuidihuzhu.cf.model.admin.vo.AdminWorkOrderMoneyBackExtVO;
import com.google.common.collect.Lists;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2019/6/24
 */
@RestController
@Slf4j
public class FlowOrderFeignController implements FlowOrderFeignClient {

    @Autowired
    private AdminWorkOrderFlowBiz orderFlowBiz;

    @Autowired
    private WorkFlowStaffService staffService;
    @Autowired
    private ApplicationService applicationService;

    @Override
    public Response<FlowOrderResult> getFlowOrderResultByid(@RequestParam("id") long id){

        if (id <= 0){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }


        String idStr = id + "";

        if (idStr.length() <=8){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        long realId = Long.valueOf(idStr.substring(8,idStr.length()));

        AdminWorkOrderFlowDetailView view = orderFlowBiz.queryWorkOrderFlowDetailView(realId);

        if (view == null){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_NO_ID);
        }

        FlowOrderResult fr = new FlowOrderResult();
        BeanUtils.copyProperties(view,fr);

        fr.setSecondClassifyDesc(view.getNewClassifyDesc());
        fr.setSecondClassifyId(view.getNewSecondClassifyId());

        return NewResponseUtil.makeSuccess(fr);
    }

    @Override
    public Response<Boolean> autoOffline() {
        boolean result = staffService.autoOffline();
        return NewResponseUtil.makeSuccess(result);
    }

    @Override
    public Response<Void> recordFreeTime() {
        staffService.recordFreeTime();
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public Response<List<WorkOrderFlowLogVo>> getFlowLogsByFlowId(long id) {
        if (id <= 0){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<AdminWorkOrderFlowDetailView.WorkOrderFlowLog> workOrderFlowLogs = orderFlowBiz.queryWorkOrderFlowLog(id);
        List<WorkOrderFlowLogVo> res = new ArrayList<>();
        workOrderFlowLogs.forEach(flowLog -> {
            WorkOrderFlowLogVo obj = new WorkOrderFlowLogVo();
            BeanUtils.copyProperties(flowLog, obj);
            res.add(obj);
        });
        return NewResponseUtil.makeSuccess(res);
    }

    @Override
    public Response<WorkOrderFlowMoneyBackExtVo> getWorkOrderFlowMoneyBackExtVo(long id) {
        if (id <= 0){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        String idStr = id + "";

        if (idStr.length() <=8){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        long realId = Long.parseLong(idStr.substring(8));
        AdminWorkOrderFlowDetailView view = orderFlowBiz.queryWorkOrderFlowDetailView(realId);

        if (view == null){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_NO_ID);
        }

        int newSecondClassifyId = view.getNewSecondClassifyId();
        int moneyBackClassifyId = applicationService.isProduction() ? 3201 : 4156;

        if (newSecondClassifyId != moneyBackClassifyId){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_NO_ID);
        }

        WorkOrderFlowMoneyBackExtVo vo = new WorkOrderFlowMoneyBackExtVo();
        // 复制基本信息
        vo.setProblem(view.getProblemContent());
        List<WorkOrderFlowMoneyBackExtVo.MoneyBackExt> res = new ArrayList<>();
        vo.setMoneyBackExtList(res);
        List<AdminWorkOrderMoneyBackExtVO> moneyBackExtList = view.getMoneyBackExtList();
        if (CollectionUtils.isNotEmpty(moneyBackExtList)){
            for (AdminWorkOrderMoneyBackExtVO moneyBackExtVO : moneyBackExtList) {
                WorkOrderFlowMoneyBackExtVo.MoneyBackExt moneyBackExt = new WorkOrderFlowMoneyBackExtVo.MoneyBackExt();
                moneyBackExt.setCaseId(moneyBackExtVO.getCaseId());
                moneyBackExt.setAmountStr(moneyBackExtVO.getAmountStr());
                res.add(moneyBackExt);
            }
        }
        return NewResponseUtil.makeSuccess(vo);
    }
}
