package com.shuidihuzhu.cf.admin.controller.api.innerapi;

import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.admin.constant.AsyncPoolConstants;
import com.shuidihuzhu.cf.model.ai.AiPromptConfig;
import com.shuidihuzhu.cf.service.ai.AiGenerateServiceImpl;
import com.shuidihuzhu.client.cf.admin.client.CfAdminAiGenerateFeignClient;
import com.shuidihuzhu.client.cf.admin.model.*;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

@RestController
@Slf4j
public class CfAdminAiGenerateFeignController implements CfAdminAiGenerateFeignClient {

    @Resource(name = AsyncPoolConstants.AI_GENERATE_ASYNC_POOL)
    private Executor executor;

    @Resource
    private AiGenerateServiceImpl aiGenerateService;

    @Override
    public Response<Void> aiGenerateContent(AIGenerateParam aiGenerateParam) {
        log.info("CfAdminAiGenerateFeignController AI生成内容: {}", JSONObject.toJSONString(aiGenerateParam));
        aiGenerateParam.setOperatorId(0L);

        // 异步执行事件处理，不阻塞当前线程
        CompletableFuture.runAsync(() -> {
            try {
                // ai生成内容
                aiGenerateService.generate(aiGenerateParam);
            } catch (Throwable e) {
                // 处理异常，记录日志
                log.error("aiGenerateContent handle error", e);
            }
        }, executor);

        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public Response<List<AiGenerateRecordVO>> getGenerateContent(QueryParam queryParam) {
        log.info("CfAdminAiGenerateFeignController 获取ai生成内容: {}", JSONObject.toJSONString(queryParam));
        return NewResponseUtil.makeSuccess(aiGenerateService.queryGenerateRecord(queryParam));
    }

    @Override
    public Response<Void> stagingScheme(StagingParam stagingParam) {
        log.info("CfAdminAiGenerateFeignController 保存暂存方案: {}", JSONObject.toJSONString(stagingParam));
        aiGenerateService.stagingScheme(stagingParam, 0L);
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public Response<StagingParam> queryScheme(QueryParam queryParam) {
        log.info("CfAdminAiGenerateFeignController 查询暂存方案: {}", JSONObject.toJSONString(queryParam));
        return NewResponseUtil.makeSuccess(aiGenerateService.queryStagingScheme(queryParam));
    }

    @Override
    public Response<Void> userFeedback(FeedbackParam feedbackParam) {
        log.info("CfAdminAiGenerateFeignController 我要反馈: {}", JSONObject.toJSONString(feedbackParam));
        aiGenerateService.feedback(feedbackParam, 0L);
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public Response<AiGenerateForwardResult> forwardContent(AiGenerateForwardParam aiGenerateForwardParam) {
        log.info("CfAdminAiGenerateFeignController 获取转发语: {}", JSONObject.toJSONString(aiGenerateForwardParam));
        return NewResponseUtil.makeSuccess(aiGenerateService.generateForwardToSendMq(aiGenerateForwardParam));
    }

    @Override
    public Response<String> queryPromptConfigByAdmin(Integer generateType, Integer modelType, Integer bizType) {

        AiPromptConfig aiPromptConfig = aiGenerateService.queryToPromptConfig(generateType, modelType, bizType);
        if (Objects.isNull(aiPromptConfig)) {
            NewResponseUtil.makeSuccess("");
        }

        return NewResponseUtil.makeSuccess(aiPromptConfig.getPrompt());
    }
}
