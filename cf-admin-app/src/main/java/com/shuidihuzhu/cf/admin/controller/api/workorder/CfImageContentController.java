package com.shuidihuzhu.cf.admin.controller.api.workorder;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.model.admin.workorder.imageContent.CfImageContentEditParam;
import com.shuidihuzhu.cf.model.admin.workorder.imageContent.CfImageContentHandleParam;
import com.shuidihuzhu.cf.model.admin.workorder.imageContent.CfImageOperateLog;
import com.shuidihuzhu.cf.model.admin.workorder.imageContent.CfImageContentAuditView;
import com.shuidihuzhu.cf.service.message.SmsRecordService;
import com.shuidihuzhu.cf.service.workorder.imageContent.CfImageContentAuditService;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditCaseDetail;
import com.shuidihuzhu.cf.vo.approve.RiverSendSmsParamVO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(path="/admin/workorder/image/content")
@Slf4j
public class CfImageContentController {

    @Autowired
    private CfImageContentAuditService contentAuditService;

    @RequiresPermission("image-content:query")
    @ApiOperation("案例基础信息-查询")
    @PostMapping("query-image-content")
    public Response<CfImageContentAuditView> queryCaseDetail(@RequestParam("caseId") int caseId, @RequestParam("workOrderId") long workOrderId) {
        return ResponseUtil.makeSuccess(contentAuditService.queryImageContentView(caseId, workOrderId));
    }

    @RequiresPermission("image-content:handle-work-order")
    @PostMapping("handle-work-order")
    public Response<String> handleWorkOrder(@RequestParam("param") String param) {

        try {
            CfImageContentHandleParam handleParam = JSON.parseObject(param, CfImageContentHandleParam.class);
            handleParam.setUserId(ContextUtil.getAdminUserId());

            contentAuditService.handleImageTextAudit(handleParam);
        } catch (Exception e) {
            log.info("初审处理异常:param:{}", param, e);
            return ResponseUtil.makeFail(e.getMessage());
        }
        return ResponseUtil.makeSuccess("");
    }

    @RequiresPermission("image-content:edit")
    @PostMapping("edit-image-content")
    public Response<InitialAuditCaseDetail.CaseBaseInfo> editImageContent(@RequestBody CfImageContentEditParam param) {
        param.setUserId(ContextUtil.getAdminUserId());

        return ResponseUtil.makeSuccess(contentAuditService.editImageContent(param));
    }

    @RequiresPermission("image-content:query-operate-log")
    @PostMapping("query-operate-log")
    public Response<List<CfImageOperateLog>> queryCaseLog(@RequestParam("caseId") int caseId) {

        return ResponseUtil.makeSuccess(contentAuditService.queryOperateLog(caseId));
    }

    @RequiresPermission("image-content:send-sms")
    @ApiOperation("发送短信")
    @PostMapping("send-sms")
    public Response<String> sendSms(@RequestBody RiverSendSmsParamVO param){
        contentAuditService.sendSms(param);
        return NewResponseUtil.makeSuccess("");
    }
}
