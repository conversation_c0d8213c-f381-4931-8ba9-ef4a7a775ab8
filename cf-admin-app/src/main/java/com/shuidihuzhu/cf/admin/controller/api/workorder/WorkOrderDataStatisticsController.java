package com.shuidihuzhu.cf.admin.controller.api.workorder;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.crowdfunding.report.ReportWorkOrderFollowActionBiz;
import com.shuidihuzhu.cf.delegate.OrganizationDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.report.ReportWorkOrderCount;
import com.shuidihuzhu.cf.vo.AnchorPageBigInt2VO;
import com.shuidihuzhu.client.cf.workorder.CfReportWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderDataStatisticsClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderStaffClient;
import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderDataStatisticsView;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.StaffStatusEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.cf.workorder.model.vo.WorkOrderReportStatisticsVo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/6/4
 */

@Slf4j
@RestController
@RequestMapping(path="/admin/work-order/data-statistics")
public class WorkOrderDataStatisticsController {

    private static final List<Integer> ORGID_LIST = List.of(420, 421, 587);

    @Resource
    private OrganizationDelegate organizationDelegate;

    @Resource
    private CfWorkOrderDataStatisticsClient cfWorkOrderDataStatisticsClient;
    @Autowired
    private CfWorkOrderStaffClient cfWorkOrderStaffClient;
    @Autowired
    private ReportWorkOrderFollowActionBiz reportWorkOrderFollowActionBiz;
    @Autowired
    private CfReportWorkOrderClient cfReportWorkOrderClient;

    @RequiresPermission("data-statistics:get-org-data-stat")
    @ApiOperation("北斗数据-组织信息统计")
    @RequestMapping(path = "get-org-data-stat", method = RequestMethod.POST)
    public Response<List<WorkOrderDataStatisticsView>> getOrgDataStat(@ApiParam(name = "组织id") @RequestParam(name = "orgId") int orgId,
                                                                      @ApiParam(name = "子组织id") @RequestParam(name = "subOrgId", required = false, defaultValue = "0") int subOrgId,
                                 @ApiParam(name = "工单类型") @RequestParam(name = "orderTypes") List<Integer> orderTypes) {
        if (CollectionUtils.isEmpty(orderTypes)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<Integer> orgIds = getTargetOrgIds(orgId, subOrgId);
        if (CollectionUtils.isEmpty(orgIds)) {
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }

        return cfWorkOrderDataStatisticsClient.getOrgDataStat(orgIds, orderTypes);
    }

    @RequiresPermission("data-statistics:get-org-data-detail-stat-by-work-type")
    @ApiOperation("北斗数据-组织明细信息统计")
    @RequestMapping(path = "get-org-data-detail-stat-by-work-type", method = RequestMethod.POST)
    public Response<List<WorkOrderDataStatisticsView>> getOrgDataDetailStatByOrderType(@RequestParam(name = "orgId") int orgId,
                                                                                       @RequestParam(name = "orderTypes") List<Integer> orderTypes) {
        if (orgId < 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        return cfWorkOrderDataStatisticsClient.getDataDetailStatByOrderType(orgId, 0, orderTypes);
    }

    @RequiresPermission("data-statistics:get-user-data-stat")
    @ApiOperation("北斗数据-用户信息统计")
    @RequestMapping(path = "get-user-data-stat", method = RequestMethod.POST)
    public Response<AnchorPageBigInt2VO<WorkOrderDataStatisticsView>> getUserDataStat(@ApiParam(name = "组织id") @RequestParam(name = "orgId") int orgId,
                                                                                      @ApiParam(name = "子组织id") @RequestParam(name = "subOrgId") int subOrgId,
                                                                                      @ApiParam(name = "用户id") @RequestParam(name = "searchUserId", required = false, defaultValue = "0") long searchUserId,
                                                                                      @ApiParam(name = "工单类型") @RequestParam(name = "orderTypes") List<Integer> orderTypes) {
        if (CollectionUtils.isEmpty(orderTypes)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        AnchorPageBigInt2VO<WorkOrderDataStatisticsView> vo = new AnchorPageBigInt2VO<>();

        List<Long> allUserIds = getTargetUserIds(orgId, subOrgId, searchUserId);
        if (CollectionUtils.isEmpty(allUserIds)) {
            return NewResponseUtil.makeSuccess(vo);
        }

        Response<List<WorkOrderDataStatisticsView>> response = cfWorkOrderDataStatisticsClient.getUserDataStat(allUserIds, orderTypes);
        List<WorkOrderDataStatisticsView> views = response.getData();

        vo.setList(views);
        vo.setTotal(allUserIds.size());

        return NewResponseUtil.makeSuccess(vo);
    }

    @RequiresPermission("data-statistics:get-user-data-detail-stat-by-work-type")
    @ApiOperation("北斗数据-用户明细信息统计")
    @RequestMapping(path = "get-user-data-detail-stat-by-work-type", method = RequestMethod.POST)
    public Response<List<WorkOrderDataStatisticsView>> getUserDataDetailStatByOrderType(@RequestParam(name = "searchUserId") long searchUserId,
                                 @RequestParam(name = "orderTypes") List<Integer> orderTypes) {
        if (searchUserId < 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        return cfWorkOrderDataStatisticsClient.getDataDetailStatByOrderType(0, searchUserId, orderTypes);
    }

    private List<Integer> getTargetOrgIds(int orgId, int subOrgId) {
        List<Integer> orgIds = Lists.newArrayList();
        if (subOrgId > 0) {
            orgIds.add(subOrgId);
        } else if (orgId > 0) {
            orgIds = organizationDelegate.getSubOrgs(orgId);
        }
        return orgIds;
    }

    private List<Long> getTargetUserIds(int orgId, int subOrgId, long userId) {
        List<Long> userIds = Lists.newArrayList();
        if (userId > 0) {
            userIds.add(userId);
        } else if (subOrgId > 0) {
            userIds = organizationDelegate.getAllUsersByOrgId(subOrgId);
        } else if (orgId > 0) {
            userIds = organizationDelegate.getAllUsersByOrgId(orgId);
        }

        return userIds;
    }


    @RequiresPermission("data-statistics:report-get-user-data")
    @ApiOperation("北斗举报数据-用户信息统计")
    @RequestMapping(path = "report-get-user-data", method = RequestMethod.POST)
    public Response<List<WorkOrderReportStatisticsVo>> getReportUserData(@RequestParam(name = "searchUserId") long searchUserId,
                                                                         @RequestParam(name = "orderType") int orderType) {
        if (searchUserId < 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<Integer> orderTypes = Lists.newArrayList();
        if (orderType == 0) {
            orderTypes.add(WorkOrderType.casefirstreport.getType());
            orderTypes.add(WorkOrderType.casehistoryreport.getType());
        } else {
            orderTypes.add(orderType);
        }
        Set<Long> userIds = Sets.newHashSet();
        if (searchUserId > 0) {
            userIds.add(searchUserId);
        } else {
            for (Integer orgId : ORGID_LIST) {
                var usersByOrgId = organizationDelegate.getAllUsersByOrgId(orgId);
                userIds.addAll(usersByOrgId);
            }
        }
        //获取今日跟进数量
        Date dateOfZero = new Date(new DateTime().dayOfYear().roundFloorCopy().getMillis());
        log.info("用户数量:{}", userIds.size());

        Map<Long, Integer> reportWorkOrderCountMap = Maps.newHashMap();
        for (Long userId : userIds) {
            List<Long> workOrderIds = reportWorkOrderFollowActionBiz.getWorkOrderId(userId, dateOfZero, orderTypes);
            if (CollectionUtils.isNotEmpty(workOrderIds)) {
                Response<List<WorkOrderVO>> response = cfReportWorkOrderClient.queryReportByIds(workOrderIds);
                List<WorkOrderVO> workOrderVOList = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList());
                if (CollectionUtils.isNotEmpty(workOrderVOList)) {
                    workOrderVOList = workOrderVOList.stream().filter(v -> v.getHandleResult() == 1 || v.getHandleResult() == 3 || v.getHandleResult() == 16).collect(Collectors.toList());
                    reportWorkOrderCountMap.put(userId, workOrderVOList.size());
                }
            }
        }

        List<WorkOrderReportStatisticsVo> workOrderReportStatisticsVos = cfWorkOrderDataStatisticsClient.getReportUserDataStat(List.copyOf(userIds), orderTypes).getData();
        if (CollectionUtils.isEmpty(workOrderReportStatisticsVos)) {
            return NewResponseUtil.makeSuccess(Collections.emptyList());
        }
        log.info("返回数量:{}", workOrderReportStatisticsVos.size());
        workOrderReportStatisticsVos.parallelStream().forEach(workOrderReportStatisticsVo -> {
            setOnlineStatus(orderType, workOrderReportStatisticsVo);
            Integer reportWorkOrderCount = reportWorkOrderCountMap.get(workOrderReportStatisticsVo.getOperatorId());
            if (Objects.nonNull(reportWorkOrderCount)) {
                workOrderReportStatisticsVo.setFollowCount(reportWorkOrderCount);
            }
        });
        return NewResponseUtil.makeSuccess(workOrderReportStatisticsVos.stream()
                .sorted(Comparator.comparing(WorkOrderReportStatisticsVo::getOperatorId)).collect(Collectors.toList()));
    }

    private void setOnlineStatus(int orderType, WorkOrderReportStatisticsVo workOrderReportStatisticsVo) {
        if (orderType == 0) {
            StaffStatus firstStaffStatus = cfWorkOrderStaffClient.getStaffStatus(WorkOrderType.casefirstreport.getType(),
                    workOrderReportStatisticsVo.getOperatorId()).getData();
            StaffStatus historyStaffStatus = cfWorkOrderStaffClient.getStaffStatus(WorkOrderType.casehistoryreport.getType(),
                    workOrderReportStatisticsVo.getOperatorId()).getData();
            log.info("在线状态啊 firstStaffStatus:{} historyStaffStatus:{}",firstStaffStatus,historyStaffStatus);
            if ((firstStaffStatus != null && firstStaffStatus.getStaffStatus() == StaffStatusEnum.online.getType())
                    && (historyStaffStatus != null && historyStaffStatus.getStaffStatus() == StaffStatusEnum.online.getType())) {
                workOrderReportStatisticsVo.setOnlineStatus("在线");
            } else if ((firstStaffStatus != null && firstStaffStatus.getStaffStatus() == StaffStatusEnum.offline.getType())
                    && (historyStaffStatus != null && historyStaffStatus.getStaffStatus() == StaffStatusEnum.offline.getType())) {
                workOrderReportStatisticsVo.setOnlineStatus("离线");
            } else if ((firstStaffStatus != null && firstStaffStatus.getStaffStatus() == StaffStatusEnum.online.getType())
                    || (historyStaffStatus != null && historyStaffStatus.getStaffStatus() == StaffStatusEnum.online.getType())) {
                workOrderReportStatisticsVo.setOnlineStatus("部分在线");
            } else {
                workOrderReportStatisticsVo.setOnlineStatus("");
            }
            return;
        }
        StaffStatus staffStatus = cfWorkOrderStaffClient.getStaffStatus(orderType,
                workOrderReportStatisticsVo.getOperatorId()).getData();
        log.info("在线状态 staffStatus:{}",staffStatus);
        if (staffStatus != null) {
            StaffStatusEnum staffStatusEnum = StaffStatusEnum.getStaffStatusEnumFromType(staffStatus.getStaffStatus());
            workOrderReportStatisticsVo.setOnlineStatus(staffStatusEnum.getMsg());
        }
    }

    @RequiresPermission("data-statistics:report-get-user-data-by-order-type")
    @ApiOperation("北斗举报数据-用户明细信息统计")
    @RequestMapping(path = "report-get-user-data-by-order-type", method = RequestMethod.POST)
    public Response<List<WorkOrderReportStatisticsVo>> gerReportUserDataByOrderType(@RequestParam(name = "searchUserId") long searchUserId,
                                                                                    @RequestParam(name = "orderTypes") List<Integer> orderTypes) {
        if (searchUserId < 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        //获取今日跟进数量
        Date dateOfZero = new Date(new DateTime().dayOfYear().roundFloorCopy().getMillis());

        //选择了全部一线工单
        if (orderTypes.size() == 1 && orderTypes.get(0).equals(0)) {
            orderTypes = List.of(WorkOrderType.casefirstreport.getType(), WorkOrderType.casehistoryreport.getType());
        }

        List<WorkOrderReportStatisticsVo> workOrderReportStatisticsVos = cfWorkOrderDataStatisticsClient.getReportUserDataByOrderType(searchUserId, orderTypes).getData();
        if (CollectionUtils.isEmpty(workOrderReportStatisticsVos)) {
            return NewResponseUtil.makeSuccess(Collections.emptyList());
        }

        workOrderReportStatisticsVos.parallelStream().forEach(workOrderReportStatisticsVo -> {
            workOrderReportStatisticsVo.setOperatorId(searchUserId);
            setOnlineStatus(workOrderReportStatisticsVo.getOrderType(), workOrderReportStatisticsVo);
            List<Long> workOrderIds = reportWorkOrderFollowActionBiz.getWorkOrderId(searchUserId, dateOfZero, Lists.newArrayList(workOrderReportStatisticsVo.getOrderType()));
            if (CollectionUtils.isNotEmpty(workOrderIds)) {
                Response<List<WorkOrderVO>> response = cfReportWorkOrderClient.queryReportByIds(workOrderIds);
                List<WorkOrderVO> workOrderVOList = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList());
                if (CollectionUtils.isNotEmpty(workOrderVOList)) {
                    workOrderVOList = workOrderVOList.stream().filter(v -> v.getHandleResult() == 1 || v.getHandleResult() == 3 || v.getHandleResult() == 16).collect(Collectors.toList());
                    workOrderReportStatisticsVo.setFollowCount(workOrderVOList.size());
                }
            }
        });
        return NewResponseUtil.makeSuccess(workOrderReportStatisticsVos);
    }

}
