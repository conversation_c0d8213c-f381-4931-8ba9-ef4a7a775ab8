package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CfItemFieldVo;
import com.shuidihuzhu.cf.model.crowdfunding.CfItemTemplate;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.crowdfunding.CfSupplyActionService;
import com.shuidihuzhu.cf.service.crowdfunding.CfTemplateFieldService;
import com.shuidihuzhu.client.cf.admin.model.CfItemField;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE 2020/11/26
 */
@Api("下发动态模板相关")
@RestController
@RequestMapping("/admin/cf/supply")
public class CfTemplateFieldController {

    @Autowired
    private CfTemplateFieldService templateFieldService;

    @Autowired
    private CfSupplyActionService supplyActionService;

    @PostMapping("addField")
    @RequiresPermission("supply-temp:addField")
    public Response<String> addField(@RequestBody CfItemField cfItemField){

        if (StringUtil.isEmpty(cfItemField.getField()) || StringUtil.isEmpty(cfItemField.getFieldLimiter())
        || StringUtil.isEmpty(cfItemField.getFieldType()) || cfItemField.getFieldLength() <= 0){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        templateFieldService.addField(cfItemField,cfItemField.getUserId());

        return NewResponseUtil.makeSuccess("succ");
    }

    @PostMapping("updateField")
    @RequiresPermission("supply-temp:updateField")
    public Response<String> updateField(@RequestParam("userId") int userId,
                                        @RequestParam("id") long id,
                                        @RequestParam("delete") int delete){
        templateFieldService.updateField(id,delete,userId);
        return NewResponseUtil.makeSuccess("succ");

    }

    @PostMapping("listCfItemFields")
    @RequiresPermission("supply-temp:listCfItemFields")
    public Response<List<CfItemFieldVo>> listCfItemFields(@RequestParam("delete") int delete){
        List<CfItemFieldVo> list = templateFieldService.listCfItemFields(delete);
        return NewResponseUtil.makeSuccess(list);
    }


    @PostMapping("addTemplate")
    @RequiresPermission("supply-temp:addTemplate")
    public Response<String> addTemplate(@RequestBody CfItemTemplate cfItemTemplate){

        if (StringUtil.isEmpty(cfItemTemplate.getTemplateName()) || StringUtil.isEmpty(cfItemTemplate.getTemplateValue())){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        templateFieldService.addTemplate(cfItemTemplate,cfItemTemplate.getUserId());

        return NewResponseUtil.makeSuccess("succ");
    }

    @PostMapping("updateTemplate")
    @RequiresPermission("supply-temp:updateTemplate")
    public Response<String> updateTemplate(@RequestParam("userId") int userId,
                                        @RequestParam("id") long id,
                                        @RequestParam("delete") int delete){


        OpResult<Integer> opResult = templateFieldService.updateTemplate(id,delete,userId);
        if (opResult.isFail()){
            return NewResponseUtil.makeError(opResult.getErrorCode());
        }
        return NewResponseUtil.makeSuccess("succ");

    }


    @PostMapping("getTemplates")
    @RequiresPermission("supply-temp:getTemplates")
    public Response<List<CfItemTemplate>> getTemplates(@RequestParam("delete") int delete){
        List<CfItemTemplate> list = templateFieldService.getTemplates(delete);
        return NewResponseUtil.makeSuccess(list);
    }

    @PostMapping("getTemplatesById")
    @RequiresPermission("supply-temp:getTemplatesById")
    public Response<List<CfItemTemplate>> getTemplatesById(@RequestParam("delete") int delete,
                                                           @RequestParam("id") long id,
                                                           @RequestParam(value = "caseId",required = false,defaultValue = "0") int caseId){
        List<CfItemTemplate> list = templateFieldService.getTemplatesByParentId(delete,id);

        if (caseId > 0){

            Set<Long> set = supplyActionService.getNoRepeat(caseId);

            list.stream().forEach(r->{
                if (set.contains(r.getId())){
                    r.setDisabled(true);
                }
            });
        }

        return NewResponseUtil.makeSuccess(list);
    }


    @PostMapping("getFieldsByTempId")
    @RequiresPermission("supply-temp:getFieldsByTempId")
    public Response<List<CfItemField>> getFieldsByTempId(@RequestParam("tempId") long tempId){
        List<CfItemField> list = templateFieldService.getFieldsByTempId(tempId);
        return NewResponseUtil.makeSuccess(list);
    }

}
