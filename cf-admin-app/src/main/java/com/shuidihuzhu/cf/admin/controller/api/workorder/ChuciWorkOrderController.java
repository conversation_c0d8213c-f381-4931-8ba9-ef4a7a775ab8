package com.shuidihuzhu.cf.admin.controller.api.workorder;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.model.ai.CfAiMaterialsSmartAIResult;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterials;
import com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterialsResult;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.CfCaseWorkOrderService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditOperateService;
import com.shuidihuzhu.cf.vo.crowdfunding.InitialAuditSmartRejectVo;
import com.shuidihuzhu.client.cf.workorder.CfChuciWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.ChuciHandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderListParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OneTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2019/5/20
 */
@RestController
@RequestMapping(path="/admin/workorder/chuci")
@Slf4j
public class ChuciWorkOrderController {

    @Autowired
    private CfChuciWorkOrderClient chuciWorkOrderClient;

    @Autowired
    private ICrowdfundingDelegate biz;

    @Autowired
    private CfWorkOrderClient workOrderClient;

    @Autowired
    private CfCaseWorkOrderService workOrderService;

    @Autowired
    private InitialAuditOperateService initialAuditOperateService;

    @RequiresPermission("chuci:hanlde-shouci")
    @RequestMapping(path = "hanlde-shouci", method = RequestMethod.POST)
    public Response Handleshouci(@RequestParam("param") String param) {

        ChuciHandleOrderParam p = JSON.parseObject(param, ChuciHandleOrderParam.class);//已检查过

        Response response = chuciWorkOrderClient.hanldeChuci(p);

        return response;
    }

    @RequiresPermission("chuci:orderlist")
    @RequestMapping(path = "chuci-orderlist", method = RequestMethod.POST)
    public Response chuciOrderList(@RequestParam("param") String param) {

        WorkOrderListParam p = JSON.parseObject(param, WorkOrderListParam.class);//已检查过

        workOrderService.fillTimeIfNullByHandleResult(p, OneTypeEnum.chuci);

      return chuciWorkOrderClient.chuciOrderList(p);
    }

    @RequiresPermission("chuci:check")
    @RequestMapping(path = "chuci-check", method = RequestMethod.POST)
    public Response chuciCheck(@RequestParam("caseId") int caseId,
                               @RequestParam("workOrderId") long workOrderId,
                               @RequestParam("userId") long userId,
                               @RequestParam("orderType") int orderType,
                               @RequestParam("time") long time){

        if (caseId <= 0 || workOrderId <= 0 || orderType <=0 || time<=0){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        boolean isBuChongYiYuanType = orderType == WorkOrderType.bu_chong_yi_yuan_xin_xi.getType();

        CrowdfundingInfo cf = biz.getFundingInfoById(caseId);
        //如果案例关闭了 结束工单 (补充医院工单例外)
        if (cf.getEndTime().before(new Date()) && !isBuChongYiYuanType){
            hanlde(workOrderId,userId,orderType);
            return NewResponseUtil.makeSuccess(true);
        }

        Response<WorkOrderVO> response = workOrderClient.getWorkOrderById(workOrderId);
        //如果工单未处理才可以处理
        if (response != null && response.getData() != null){
            WorkOrderVO workOrderVO = response.getData();
            if (workOrderVO.getHandleResult() == HandleResultEnum.doing.getType() ||
                    workOrderVO.getHandleResult() == HandleResultEnum.later_doing.getType()){
                return NewResponseUtil.makeSuccess(false);
            }else {
                return NewResponseUtil.makeSuccess(true);
            }
        }

        return NewResponseUtil.makeSuccess(false);
    }


    @RequiresPermission("chuci:submit")
    @RequestMapping(path = "submit", method = RequestMethod.POST)
    public Response<String> submit(@RequestBody List<CfAiMaterials> aiMaterials) {
        int userId = ContextUtil.getAdminUserId();
        OpResult opResult = initialAuditOperateService.submit(aiMaterials,userId);
        return NewResponseUtil.makeFail(opResult.getErrorCode().getCode(),opResult.getMessage(),"");
    }


    @RequiresPermission("chuci:listAiMaterials")
    @RequestMapping(path = "listAiMaterials", method = RequestMethod.POST)
    public Response<List<CfAiMaterials>> list(@RequestParam("workOrderId") long workOrderId) {
        List<CfAiMaterials> list = initialAuditOperateService.listAiMaterials(workOrderId);
        return NewResponseUtil.makeSuccess(list);
    }


    @RequiresPermission("chuci:toHuman")
    @RequestMapping(path = "toHuman", method = RequestMethod.POST)
    public Response<String> toHuman(long workOrderId,int orderType,String remark,int userId,int caseId) {
        InitialAuditSmartRejectVo smartRejectVo = InitialAuditSmartRejectVo.builder()
                .remarkMsg(remark)
                .build();
        OpResult opResult = initialAuditOperateService.toHuman(workOrderId,orderType,smartRejectVo,userId,caseId, "进入人工审核");
        return NewResponseUtil.makeFail(opResult.getErrorCode().getCode(),opResult.getMessage(),"");
    }

    @RequiresPermission("chuci:getResult")
    @RequestMapping(path = "getResult", method = RequestMethod.POST)
    public Response<CfAiMaterialsResult> getResult(@RequestParam("caseId") int caseId,
                                                   @RequestParam(value = "workOrderId",required = false,defaultValue = "0") long workOrderId,
                                                   @RequestParam(value = "orderType",required = false, defaultValue = "0") int orderType){
        CfAiMaterialsResult result = initialAuditOperateService.getResult(caseId,workOrderId,orderType);
        return NewResponseUtil.makeSuccess(result);
    }

    @ApiOperation("升级应急组")
    @RequiresPermission("chuci:hanlde-shouci")
    @RequestMapping(path = "upgrade-emergency", method = RequestMethod.POST)
    public Response<Void> upgradeEmergency(
            @RequestParam("workOrderId") long workOrderId,
            @ApiParam("升级原因id") @RequestParam("reasonCode") String reasonCode,
            @ApiParam("升级原因内容") @RequestParam(value = "reasonMsg", required = false, defaultValue = "") String reasonMsg){
        long operatorId = ContextUtil.getAdminLongUserId();
        return initialAuditOperateService.upgradeEmergency(workOrderId, operatorId, reasonCode, reasonMsg);
    }

    @ApiOperation("低风险工单升级应急组")
    @RequiresPermission("chuci:low-upgrade-emergency")
    @RequestMapping(path = "low-upgrade-emergency", method = RequestMethod.POST)
    public Response<Void> lowUpgradeEmergency(
            @RequestParam("workOrderId") long workOrderId,
            @ApiParam("升级类型") @RequestParam("upgradeType") int upgradeType,
            @ApiParam("升级原因内容") @RequestParam(value = "reasonMsg", required = false, defaultValue = "") String reasonMsg){
        long operatorId = ContextUtil.getAdminLongUserId();
        return initialAuditOperateService.lowUpgradeEmergency(workOrderId, operatorId, upgradeType, reasonMsg);
    }

    @ApiOperation("智能审核AI团队的识别结果")
    @RequiresPermission("chuci:ai-smart-result")
    @RequestMapping(path = "ai-smart-result", method = RequestMethod.POST)
    public Response<CfAiMaterialsSmartAIResult> aiSmartResult(@ApiParam("工单Id") @RequestParam("workOrderId") long workOrderId) {
        CfAiMaterialsSmartAIResult smartAIResult = initialAuditOperateService.getSmartAIResultByWorkOrderId(workOrderId);
        return NewResponseUtil.makeSuccess(smartAIResult);
    }

    private void hanlde(long workOrderId,long userId,int orderType){

        ChuciHandleOrderParam p = new ChuciHandleOrderParam();
        p.setWorkOrderId(workOrderId);
        p.setOrderType(orderType);
        p.setHandleResult(HandleResultEnum.exception_done.getType());
        p.setUserId(userId);
        chuciWorkOrderClient.hanldeChuci(p);

    }

}
