package com.shuidihuzhu.cf.admin.mq;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CfBasePreMsgService;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.admin.model.CfBasePreMsgStatusEnum;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @DATE 2019/10/15
 */
@Service
@RocketMQListener(id = "cf-operation-pre-msg",
        group = "cf-operation-msg-group",
        tags = MQTagCons.CF_OPERATION_MSG,
        topic = MQTopicCons.CF)
@Slf4j
public class CfOperation4msgConsumer  implements MessageListener<CfOperatingRecord> {

    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;

    @Autowired
    private CfBasePreMsgService CfBasePreMsgService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfOperatingRecord> mqMessage) {

        if(mqMessage == null || mqMessage.getPayload() == null){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        log.info("CfOperation4msgConsumer message: {}",mqMessage.getPayload());

        CfOperatingRecord cfOperatingRecord = mqMessage.getPayload();

        CrowdfundingInfo c = crowdfundingDelegate.getCrowdfundingInfoByInfoId(cfOperatingRecord.getInfoUuid());

        if (c == null){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        UserInfoModel userInfoModel =  userInfoServiceBiz.getUserInfoByUserId(c.getUserId());

        String mobile = userInfoModel.getCryptoMobile();


        CfBasePreMsgService.updateMsgStatus(mobile, CfBasePreMsgStatusEnum.confirm.getCode(),c.getId(),c.getInfoId());


        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
