package com.shuidihuzhu.cf.admin.mq;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.biz.call.CallRecordBiz;
import com.shuidihuzhu.cf.call.CallInModel;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RocketMQListener(id = MQTagCons.CF_EXPORT_CLINK_CALL_IN_DAY,
        tags = MQTagCons.CF_EXPORT_CLINK_CALL_IN_DAY,
        topic = MQTopicCons.CF)
public class CfExportClinkCallInDayConsumer implements MessageListener<String> {

    @Autowired
    private CallRecordBiz callRecordBiz;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<String> mqMessage) {
        if (mqMessage == null || mqMessage.getPayload() == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        CallInModel callInModel = null;
        try {
            callInModel = JSON.parseObject(mqMessage.getPayload(), CallInModel.class);
        } catch (Exception e) {
            return ConsumeStatus.RECONSUME_LATER;
        }

        int res = callRecordBiz.insertCallInModel(callInModel);

        log.info("CfExportClinkCallInDayConsumer res:{} callInModel:{}",res,callInModel);

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
