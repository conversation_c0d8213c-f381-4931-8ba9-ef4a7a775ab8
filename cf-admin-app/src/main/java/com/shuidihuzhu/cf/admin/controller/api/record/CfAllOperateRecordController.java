package com.shuidihuzhu.cf.admin.controller.api.record;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.record.CfAllOperateRecordVo;
import com.shuidihuzhu.cf.service.record.CfAllOperateRecordService;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/9/1  4:04 下午
 */
@RestController
@RequestMapping(path = "admin/cf/record")
public class CfAllOperateRecordController {

    @Resource
    private CfAllOperateRecordService cfAllOperateRecordService;


    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @RequiresPermission("record:save-operate-record")
    @RequestMapping(path = "/save-operate-record", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response<Void> saveOperateRecord(@RequestParam(name = "infoUuid") String infoUuid,
                                            @RequestParam(name = "bizType") int bizType,
                                            @RequestParam(name = "bizId", required = false, defaultValue = "0") String bizId,
                                            @RequestParam(name = "operateType") int operateType,
                                            @RequestParam(name = "pageType") int pageType,
                                            @RequestParam(name = "content", required = false, defaultValue = "") String content) {

        int adminUserId = ContextUtil.getAdminUserId();

        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (Objects.isNull(crowdfundingInfo)) {
            return NewResponseUtil.makeError(ErrorCode.CF_NOT_FOUND);
        }

        cfAllOperateRecordService.insertBatch(adminUserId, crowdfundingInfo.getId(), bizType, bizId, operateType, pageType, content);

        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("record:query-operate-record")
    @RequestMapping(path = "/query-operate-record", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response<List<CfAllOperateRecordVo>> queryOperateRecord(@RequestParam(name = "infoUuid") String infoUuid,
                                                                   @RequestParam(name = "bizType") int bizType, @RequestParam(name = "bizId") long bizId) {

        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (Objects.isNull(crowdfundingInfo)) {
            return NewResponseUtil.makeError(ErrorCode.CF_NOT_FOUND);
        }

        List<CfAllOperateRecordVo> cfAllOperateRecordVos = cfAllOperateRecordService.getListByCaseId(crowdfundingInfo.getId(), bizType, bizId);

        return NewResponseUtil.makeSuccess(cfAllOperateRecordVos);
    }
}
