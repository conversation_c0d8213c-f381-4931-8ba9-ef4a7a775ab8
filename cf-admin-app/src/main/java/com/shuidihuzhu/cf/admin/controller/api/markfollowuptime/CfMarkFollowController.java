package com.shuidihuzhu.cf.admin.controller.api.markfollowuptime;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.enhancer.model.response.EhResponse;
import com.shuidihuzhu.cf.enhancer.utils.EhResponseUtils;
import com.shuidihuzhu.cf.model.markfollowuptime.CfMarkFollowRecordVO;
import com.shuidihuzhu.cf.model.markfollowuptime.CfMarkFollowVO;
import com.shuidihuzhu.cf.model.report.schedule.ReportScheduleVO;
import com.shuidihuzhu.cf.service.markfollowuptime.CfMarkFollowService;
import com.shuidihuzhu.common.web.util.ContextUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/12  8:24 下午
 */
@RestController
@RequestMapping(path = "/admin/cf/mark")
@Slf4j
public class CfMarkFollowController {
    @Autowired
    private CfMarkFollowService cfMarkFollowService;

    private static final SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequiresPermission("report-mark:get-mark-list")
    @ApiOperation("添加跟进")
    @PostMapping("add")
    public EhResponse<Void> add(@RequestParam("bizId") long bizId,
                                @RequestParam("orderType") int orderType,
                                @RequestParam("targetTime") String targetTime) {
        if (StringUtils.isEmpty(targetTime)) {
            return EhResponseUtils.success();
        }
        int adminUserId = ContextUtil.getAdminUserId();
        Date time = parseTime(targetTime);
        return cfMarkFollowService.add(adminUserId, orderType, bizId, time);
    }

    @RequiresPermission("report-mark:get-mark-list")
    @ApiOperation("修改跟进")
    @PostMapping("update")
    public EhResponse<Void> update(@RequestParam int id,
                                   @RequestParam String targetTime) {
        int adminUserId = ContextUtil.getAdminUserId();
        Date time = parseTime(targetTime);
        return cfMarkFollowService.update(adminUserId, id, time);
    }

    @RequiresPermission("report-mark:get-mark-list")
    @ApiOperation("删除跟进")
    @PostMapping("remove")
    public EhResponse<Void> remove(@RequestParam int id){
        int adminUserId = ContextUtil.getAdminUserId();
        return cfMarkFollowService.remove(adminUserId, id);
    }

    @RequiresPermission("report-mark:get-mark-list")
    @ApiOperation("处理跟进")
    @PostMapping("done")
    public EhResponse<Void> done(@RequestParam int id){
        int adminUserId = ContextUtil.getAdminUserId();
        return cfMarkFollowService.done(adminUserId, id);
    }

    /**
     * 1、某工单的状态的工单状态不是“提交结果”
     *
     * 2、某工单之前没有没有标记过
     *
     * 3、之前的标记时间被删除
     *
     * 4、之前的标记时间已经过期
     * @return
     */
    @RequiresPermission("report-mark:get-mark-list")
    @ApiOperation("判断是否可以标记跟进时间")
    @PostMapping("judge-mark-follow-time")
    public EhResponse<CfMarkFollowVO> judgeMarkFollowTime(@RequestParam long workOrderId) {
        return cfMarkFollowService.judgeMarkFollowTime(workOrderId);
    }

    @RequiresPermission("report-mark:get-mark-list")
    @ApiOperation("查询标记跟进时间列表")
    @PostMapping("get-list-by-work-order-id")
    public EhResponse<CfMarkFollowRecordVO> getListByWorkOrderId(@RequestParam long workOrderId) {
        return cfMarkFollowService.getListByWorkOrderId(workOrderId);
    }

    @RequiresPermission("report-mark:get-mark-list")
    @ApiOperation("查询操作人跟进列表")
    @PostMapping("get-list-by-operator-id")
    public EhResponse<List<CfMarkFollowVO>> getListByOperatorId() {
        int adminUserId = ContextUtil.getAdminUserId();
        return cfMarkFollowService.getListByOperatorId(adminUserId);
    }

    @RequiresPermission("report-mark:get-mark-list")
    @ApiOperation("判断")
    @PostMapping("judge")
    public EhResponse<Boolean> judge(@RequestParam long workOrderId) {
        int adminUserId = ContextUtil.getAdminUserId();
        return cfMarkFollowService.judge(workOrderId, adminUserId);
    }

    private Date parseTime(String targetTime) {
        try {
            return dateTimeFormat.parse(targetTime);
        } catch (ParseException e) {
            log.error("ReportScheduleController parseTime error", e);
            return null;
        }
    }

}
