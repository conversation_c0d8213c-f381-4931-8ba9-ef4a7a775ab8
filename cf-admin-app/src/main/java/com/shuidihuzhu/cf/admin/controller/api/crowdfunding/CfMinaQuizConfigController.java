package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.admin.util.admin.IntegerUtil;

import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.service.crowdfunding.CfMinaQuizConfigService;
import com.shuidihuzhu.cf.vo.mina.CfMinaQuizFormVo;
import com.shuidihuzhu.cf.vo.mina.CfMinaQuizPushConfigVo;
import com.shuidihuzhu.cf.vo.mina.CfMinaQuizQuestionFormVo;

import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * Created by dongcf on 2018/1/29
 */
@RestController
@RequestMapping(path = "/admin/cf/mina/quiz")
public class CfMinaQuizConfigController {

    private static final Logger LOGGER = LoggerFactory.getLogger(CfMinaQuizConfigController.class);

    @Autowired
    private CfMinaQuizConfigService cfMinaQuizConfigService;

    @RequiresPermission("quiz:find-list")
    @RequestMapping(path = "find-list", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    public Response findList(@RequestParam(value = "current", required = false, defaultValue = "1") Integer current,
                             @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
                             @RequestParam(value = "title", required = false) String title) {
        if (current < 1) {
            current = 1;
        }
        if (pageSize < 0) {
            pageSize = 10;
        }
        Map<String, Object> result = this.cfMinaQuizConfigService.findQuizList(title, current, pageSize);
        return NewResponseUtil.makeSuccess(result);
    }

    @RequiresPermission("quiz:save-quiz")
    @RequestMapping(path = "save-quiz", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    public Response saveQuiz(@RequestParam("param") String param) {
        LOGGER.info("CfMinaQuizConfigController saveQuiz param:{}", param);
        CfMinaQuizFormVo vo = JSONObject.parseObject(param, CfMinaQuizFormVo.class);//已检查过
        if (vo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (StringUtils.isBlank(vo.getTitle())) {
            return NewResponseUtil.makeResponse(AdminErrorCode.SYSTEM_PARAM_ERROR.getCode(), "测试标题不能为空", null);
        }

        if (CollectionUtils.isEmpty(vo.getCfMinaQuizQuestionFormVoList())) {
            return NewResponseUtil.makeResponse(AdminErrorCode.SYSTEM_PARAM_ERROR.getCode(), "题目不能为空", null);
        }

        List<CfMinaQuizQuestionFormVo> questionFormVoList = vo.getCfMinaQuizQuestionFormVoList();
        for (CfMinaQuizQuestionFormVo questionFormVo : questionFormVoList) {
            if (CollectionUtils.isEmpty(questionFormVo.getCfMinaQuizAnswerFormVoList())) {
                return NewResponseUtil.makeResponse(AdminErrorCode.SYSTEM_PARAM_ERROR.getCode(), "题目答案选项不能为空", null);
            }
            if (questionFormVo.getCfMinaQuizAnswerFormVoList().size() < 2) {
                return NewResponseUtil.makeResponse(AdminErrorCode.SYSTEM_PARAM_ERROR.getCode(), "题目答案选项不能少于两个", null);
            }
        }
        if (CollectionUtils.isEmpty(vo.getCfMinaQuizResultFormVoList())) {
            return NewResponseUtil.makeResponse(AdminErrorCode.SYSTEM_PARAM_ERROR.getCode(), "测试结果不能为空", null);
        }
        int result = this.cfMinaQuizConfigService.saveQuiz(vo);
        if (result == 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        return NewResponseUtil.makeResponse(AdminErrorCode.SUCCESS.getCode(), "保存成功", null);
    }

    @RequiresPermission("quiz:modify-status")
    @RequestMapping(value = "modify-status", method = RequestMethod.POST)
    public Response modifyStatus(@RequestParam("minaQuizId") Integer minaQuizId, @RequestParam("showType") Integer showType,
                                 @RequestParam(value = "new", required = false, defaultValue = "0") Boolean isNew,
                                 @RequestParam(value = "sort", required = false, defaultValue = "0") Integer sort,
                                 @RequestParam(value = "releaseTimeStr", required = false) String releaseTimeStr) {
        int newFlag = 0;
        if (isNew) {
            newFlag = 1;
        }
        Map<String, Object> result = this.cfMinaQuizConfigService.modifyStatus(minaQuizId, showType, newFlag, sort, releaseTimeStr);
        int code = IntegerUtil.parseInt(result.get("code").toString());
        String msg = String.valueOf(result.get("msg"));
        return NewResponseUtil.makeResponse(code, msg, null);
    }

    @RequiresPermission("quiz:get-quiz-info")
    @RequestMapping(value = "get-quiz-info", method = RequestMethod.POST)
    public Response getQuizInfo(@RequestParam("minaQuizId") Integer minaQuizId) {
        CfMinaQuizFormVo cfMinaQuizFormVo = this.cfMinaQuizConfigService.getQuizInfo(minaQuizId);
        if (cfMinaQuizFormVo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        return NewResponseUtil.makeSuccess(cfMinaQuizFormVo);
    }


    @RequiresPermission("quiz:find-message-config")
    @RequestMapping(value = "find-message-config", method = RequestMethod.POST)
    public Response findMessageConfig(@RequestParam(value = "current", required = false, defaultValue = "1") Integer current,
                                      @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize) {
        if (current < 1) {
            current = 1;
        }
        if (pageSize < 0) {
            pageSize = 10;
        }
        Map<String, Object> result = this.cfMinaQuizConfigService.findMessageConfig(current, pageSize);
        return NewResponseUtil.makeSuccess(result);
    }

    @RequiresPermission("quiz:get-message-config")
    @RequestMapping(value = "get-message-config", method = RequestMethod.POST)
    public Response getMessageConfig(@RequestParam("pushConfigId") Integer pushConfigId) {
        CfMinaQuizPushConfigVo configVo = this.cfMinaQuizConfigService.getMessageConfig(pushConfigId);
        if (configVo == null) {
            NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        return NewResponseUtil.makeSuccess(configVo);
    }

    @RequiresPermission("quiz:save-message")
    @RequestMapping(value = "save-message", method = RequestMethod.POST)
    public Response saveMessage(@RequestParam(value = "id", required = false, defaultValue = "0") Integer id,
                                @RequestParam("templateId") String templateId,@RequestParam("templateUrl") String templateUrl,
                                @RequestParam(value = "pushTarget", required = false, defaultValue = "") String pushTarget,
                                @RequestParam(value = "emphasisKeyword", required = false, defaultValue = "0") Integer emphasisKeyword,
                                @RequestParam(value = "keywordFirst", required = false, defaultValue = "") String keywordFirst,
                                @RequestParam(value = "keywordOne", required = false, defaultValue = "") String keywordOne,
                                @RequestParam(value = "keywordTwo", required = false, defaultValue = "") String keywordTwo,
                                @RequestParam(value = "keywordThree", required = false, defaultValue = "") String keywordThree,
                                @RequestParam(value = "keywordFour", required = false, defaultValue = "") String keywordFour,
                                @RequestParam(value = "keywordFive", required = false, defaultValue = "") String keywordFive,
                                @RequestParam(value = "keywordSix", required = false, defaultValue = "") String keywordSix,
                                @RequestParam(value = "keywordRemark", required = false, defaultValue = "") String keywordRemark,
                                @RequestParam("minaQuizId") Integer minaQuizId, @RequestParam(value = "customUrl", required = false, defaultValue = "") String customUrl) {

        Map<String, Object> result = this.cfMinaQuizConfigService.saveMessage(id, templateId, templateUrl, pushTarget, emphasisKeyword,
                keywordFirst, keywordOne, keywordTwo, keywordThree, keywordFour, keywordFive, keywordSix, keywordRemark, minaQuizId, customUrl);
        int code = IntegerUtil.parseInt(result.get("code").toString());
        String msg = String.valueOf(result.get("msg"));
        return NewResponseUtil.makeResponse(code, msg, null);
    }
}
