package com.shuidihuzhu.cf.admin.mq;

import com.shuidihuzhu.cf.biz.admin.AdminOrganizationBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderClassifySettingsBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderFlowBiz;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType;
import com.shuidihuzhu.cf.finance.enums.BroadcastMsgEnum;
import com.shuidihuzhu.cf.finance.model.CfFinanceBroadcastMsg;
import com.shuidihuzhu.cf.finance.mq.FinanceMQTagCons;
import com.shuidihuzhu.cf.model.admin.AdminOrganization;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderClassifySettings;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2020/11/18
 */
@Service
@Slf4j
@RocketMQListener(id = "personal-pay-fail",
        tags = FinanceMQTagCons.CF_FINANCE_BROADCAST_MSG,
        group = FinanceMQTagCons.CF_FINANCE_BROADCAST_MSG+"_pay_fail",
        topic = MQTopicCons.CF)
public class CfFinanceBroadcastComsumer implements MessageListener<CfFinanceBroadcastMsg> {

    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Autowired
    private AdminWorkOrderFlowBiz orderFlowBiz;

    @Autowired
    private AdminOrganizationBiz organizationBiz;

    @Autowired
    private AdminWorkOrderClassifySettingsBiz classifySettingsBiz;
    @Autowired
    private UserInfoServiceBiz userInfoServiceBiz;
    @Resource
    private ShuidiCipher shuidiCipher;

    // 水滴筹-运营-资金组
    private static final String ORG_NAME = "资金组";

    // 水滴筹-提现&退款  提现失败
    private static final String CLASSIFY_NAME = "提现失败";

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfFinanceBroadcastMsg> mqMessage) {

        CfFinanceBroadcastMsg msg = mqMessage.getPayload();

        if (msg == null){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        if (msg.getBizType() != BroadcastMsgEnum.BizType.DRAW_FAILED_ONE){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingDelegate.getCaseInfoById(msg.getCaseId());

        if (crowdfundingInfo == null){
            return ConsumeStatus.RECONSUME_LATER;
        }

        //不是个人打款不创建
        if ( crowdfundingInfo.getRelationType() == CrowdfundingRelationType.LOCATION_HOSPITAL_ACCOUNT
                || crowdfundingInfo.getRelationType() == CrowdfundingRelationType.charitable_organization ){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        AdminWorkOrderFlowView vo = new AdminWorkOrderFlowView();

        vo.setCaseId(crowdfundingInfo.getId());
        vo.setCaseTitle(StringUtils.trimToEmpty(crowdfundingInfo.getTitle()));
        vo.setMobile(shuidiCipher.decrypt(StringUtils.trimToEmpty(userInfoServiceBiz.getUserInfoByUserId(crowdfundingInfo.getUserId()).getCryptoMobile())));
        vo.setProblemContent("对私付款失败，先核实失败原因");

        vo.setProblemImg("");
        vo.setHandleImg("");

        vo.setUserIdentity(AdminWorkOrderFlowView.WorkOrderFlowUserIdentity.DEFAULT.getCode());
        vo.setLevel(AdminWorkOrderConst.Level.MEDIUM.getCode());
        // 分配的组织
        vo.setProblemType(getOrgIdByName(ORG_NAME));
        vo.setSecondClassifyId(getClassifyIdByName(CLASSIFY_NAME));


        Response result = orderFlowBiz.createWorkOrderFlow(vo);
        log.info("个人打款失败自动创建信息流转工单. result code: {}, message: {}", result.getCode(), result.getMsg());

        return ConsumeStatus.CONSUME_SUCCESS;

    }


    private int getOrgIdByName(String orgName) {
        List<AdminOrganization> orgList = organizationBiz.getAdminOrganizationByName(orgName);

        return CollectionUtils.isEmpty(orgList) ? 0 : orgList.get(0).getId();
    }

    private long getClassifyIdByName(String classfyName) {
        List<AdminWorkOrderClassifySettings> classifySettings = classifySettingsBiz.selectClassifySettingsByText(classfyName);
        return CollectionUtils.isEmpty(classifySettings) ? 0 : classifySettings.get(0).getId();
    }

}
