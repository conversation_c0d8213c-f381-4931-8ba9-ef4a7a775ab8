package com.shuidihuzhu.cf.admin.mq;

import com.google.common.collect.Lists;
import com.google.common.collect.Ordering;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.enhancer.model.response.EhResponse;
import com.shuidihuzhu.cf.enums.report.ReportPayMethodEnum;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceCapitalAccountFeignClient;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceDrawCashFeignClient;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceRefundFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.model.finance.drawcash.CfDrawCashRecordV2;
import com.shuidihuzhu.cf.finance.model.vo.refund.CfRefundApplyV2Vo;
import com.shuidihuzhu.cf.finance.mq.CfInfoLaunchSuccess;
import com.shuidihuzhu.cf.finance.mq.FinanceMQTagCons;
import com.shuidihuzhu.cf.model.AdminInfoLaunchSuccess;
import com.shuidihuzhu.cf.model.crowdfunding.CfCapitalAccount;
import com.shuidihuzhu.cf.model.report.schedule.ReportScheduleVO;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.cf.service.report.ReportScheduleService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/12/17  2:35 下午
 */
@Service
@Slf4j
@RocketMQListener(id = FinanceMQTagCons.INFO_LAUNCH_SUCCESS + "-cf-admin",
        group = "cf-admin" + FinanceMQTagCons.INFO_LAUNCH_SUCCESS + "-group",
        tags = FinanceMQTagCons.INFO_LAUNCH_SUCCESS,
        topic = MQTopicCons.CF)
public class AdminInfoLaunchSuccessConsumer implements MessageListener<CfInfoLaunchSuccess> {

    @Resource
    private ReportScheduleService reportScheduleService;

    @Resource
    private ApplicationService applicationService;

    @Resource
    private CfFinanceCapitalAccountFeignClient cfFinanceCapitalAccountFeignClient;

    @Resource
    private CfFinanceDrawCashFeignClient cfFinanceDrawCashFeignClient;

    @Resource
    private CfFinanceRefundFeignClient cfFinanceRefundFeignClient;

    @Autowired(required = false)
    private Producer producer;


    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfInfoLaunchSuccess> mqMessage) {
        log.info("AdminInfoLaunchSuccessConsumer start..");
        if (Objects.isNull(mqMessage) || Objects.isNull(mqMessage.getPayload())) {
            log.info("AdminInfoLaunchSuccessConsumer data null..");
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        CfInfoLaunchSuccess payload = mqMessage.getPayload();
        int caseId = (int) payload.getCaseId();
        String infoUuid = payload.caseUuId;

        //案例是分批打款案例
        ReportPayMethodEnum payMethodEnum = reportScheduleService.getPayMethodByCaseId(caseId);
        int code = Optional.ofNullable(payMethodEnum).map(ReportPayMethodEnum::getCode).orElse(0);
        if (code != ReportPayMethodEnum.PAY_IN_BATCH.getCode()) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        //未标记未来跟进时间
        EhResponse<ReportScheduleVO> ehResponse = reportScheduleService.getByCaseId(caseId);
        if (Objects.nonNull(ehResponse.getData())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        //案例的账户余额>0  资金
        FeignResponse<CfCapitalAccount> feignResponse = cfFinanceCapitalAccountFeignClient.capitalAccountGetByInfoUuid(infoUuid);
        CfCapitalAccount cfCapitalAccount = Optional.ofNullable(feignResponse).filter(FeignResponse::ok).map(FeignResponse::getData).orElse(null);
        if (Objects.nonNull(cfCapitalAccount)) {
            if (cfCapitalAccount.getSurplusAmount() <= 0) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
        }

        //未申请全部退款 资金
        FeignResponse<CfRefundApplyV2Vo> response = cfFinanceRefundFeignClient.getRefundApplyV2(caseId);
        CfRefundApplyV2Vo cfRefundApplyV2Vo = Optional.ofNullable(response).filter(FeignResponse::ok).map(FeignResponse::getData).orElse(null);
        if (Objects.nonNull(cfRefundApplyV2Vo) && cfRefundApplyV2Vo.getApplyStatus() == 2) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        long expectTime = 0;
        //90天后10点发送
        if (applicationService.isProduction()) {
            expectTime = new DateTime().plusDays(89).withHourOfDay(10).getMillis();
        } else {
            expectTime = new DateTime().plusDays(0).plusHours(0).plusMinutes(5).getMillis();
        }

        //获取最近打款成功时间作为标识
        FeignResponse<List<CfDrawCashRecordV2>> listFeignResponse = cfFinanceDrawCashFeignClient.getSuccessDrawCashRecordV2List(caseId);
        List<CfDrawCashRecordV2> cfDrawCashRecordV2List = Optional.ofNullable(listFeignResponse).filter(FeignResponse::ok).map(FeignResponse::getData).orElse(Lists.newArrayList());
        long timestamp = 0L;
        if (CollectionUtils.isNotEmpty(cfDrawCashRecordV2List)) {
            Ordering<CfDrawCashRecordV2> createTime = Ordering.natural().reverse().onResultOf(CfDrawCashRecordV2::getCreateTime);
            timestamp = cfDrawCashRecordV2List.stream().sorted(createTime).map(v -> v.getCreateTime().getTime()).findFirst().orElse(0L);
        }

        AdminInfoLaunchSuccess adminInfoLaunchSuccess = AdminInfoLaunchSuccess.builder()
                .caseId(caseId)
                .infoUuid(infoUuid)
                .timestamp(timestamp)
                .build();

        Message message = Message.ofSchedule(MQTopicCons.CF,
                MQTagCons.PUNCH_CREATE_MESSAGE_DELIVER_WORK_ORDER,
                MQTagCons.PUNCH_CREATE_MESSAGE_DELIVER_WORK_ORDER + "_" + caseId + "_" + System.currentTimeMillis(),
                adminInfoLaunchSuccess, expectTime / 1000);
        MessageResult messageResult = producer.send(message);
        log.info("AdminInfoLaunchSuccessConsumer producer result:{}", messageResult);
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
