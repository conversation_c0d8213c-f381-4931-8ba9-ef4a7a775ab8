package com.shuidihuzhu.cf.admin.util;

import org.apache.commons.lang3.StringUtils;

/**
 * 掩码工具类
 */
public class MaskUtil {

    /**
     * 获取手机号掩码 eg:18201534934 => 182****4934
     * @param phoneNumber
     * @return
     */
    public static String getTelephoneMask(String phoneNumber){
        return StringUtils.isNotBlank(phoneNumber) ?
                phoneNumber.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2") :
                phoneNumber;
    }

    /**
     * 字符串掩码，后四位变为*
     */
    public static String maskStrAfterFour(String str) {
        if (StringUtils.isEmpty(str) || str.length() < 4) {
            return "";
        }

        return str.substring(0, str.length() - 4) + "****";
    }

    public static String getNameMask(String patientName){
        if(StringUtils.isBlank(patientName) || patientName.length()<=1){
            return patientName;
        }

        return patientName.replaceAll("([\\d\\D]{1})(.*)", "$1"+createAsterisk(patientName.length()-1));
    }

    private static String createAsterisk(int length) {
        StringBuffer stringBuffer = new StringBuffer();
        for (int i = 0; i < length; i++) {
            stringBuffer.append("*");
        }
        return stringBuffer.toString();
    }

    public static String limitComment(String comment) {

        if (StringUtils.isBlank(comment)) {
            return comment;
        }

        // 如果字符串长度小于或等于20，直接返回原字符串
        if (StringUtils.length(comment) <= 20) {
            return comment;
        }

        // 截断字符串并添加"..."
        return comment.substring(0, 17) + "...";

    }
}
