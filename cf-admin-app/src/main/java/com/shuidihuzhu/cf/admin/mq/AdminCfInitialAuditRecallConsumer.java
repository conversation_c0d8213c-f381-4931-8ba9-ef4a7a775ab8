package com.shuidihuzhu.cf.admin.mq;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfFirstApproveOperatorBiz;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingOperationDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.CrowdfundingInitialAuditInfo;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditOperateService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
@RocketMQListener(id = MQTagCons.ADMIN_INITIAL_AUDIT_RECALL_SUBMIT_AFTER_REJECT,
        group = "cf-admin"+MQTagCons.ADMIN_INITIAL_AUDIT_RECALL_SUBMIT_AFTER_REJECT,
        tags = MQTagCons.ADMIN_INITIAL_AUDIT_RECALL_SUBMIT_AFTER_REJECT,
        topic = MQTopicCons.CF)
@Slf4j
public class AdminCfInitialAuditRecallConsumer  implements MessageListener<CfFirstApproveOperatorBiz.RecallAfterRejectObject> {

    @Autowired
    private InitialAuditOperateService auditOperateService;
    @Autowired
    private Producer producer;
    @Autowired
    private ICrowdfundingOperationDelegate crowdfundingOperationDelegate;
    @Autowired
    private AdminCrowdfundingInfoBiz fundingInfoBiz;
    @Autowired
    private AdminCfInfoExtBiz adminCfInfoExtBiz;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfFirstApproveOperatorBiz.RecallAfterRejectObject> mqMessage) {

        log.info("初次审核驳回-召回消息处理. msg：{}", mqMessage);

        if (mqMessage == null || mqMessage.getPayload() == null) {
            log.error("初次审核驳回-召回消息处理 消息体为空");
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        CfFirstApproveOperatorBiz.RecallAfterRejectObject recallObject = mqMessage.getPayload();
        // 案例是否被结束
        CrowdfundingInfo cfCase = fundingInfoBiz.getFundingInfoById(recallObject.getCaseId());
        if (cfCase == null || cfCase.getEndTime().before(new Date())) {
            log.info("\"初次审核驳回-不能找到案例或案例已结束，不发送召回消息。caseId:{}", recallObject.getCaseId());
            return  ConsumeStatus.CONSUME_SUCCESS;
        }

        // 首先判断案例的状态 如果还是驳回的状态
        String caseUUid = cfCase.getInfoId();
        CfInfoExt infoExt = adminCfInfoExtBiz.getByInfoUuid(caseUUid);
        if (infoExt == null || infoExt.getFirstApproveStatus() != FirstApproveStatusEnum.APPLY_FAIL.getCode()) {
            log.info("初次审核驳回-召回消息处理, 案例的状态不是审核失败，不发送消息。caseId:{}, firstApproveStatus:{}",
                    recallObject.getCaseId(), infoExt == null ? "null" : infoExt.getFirstApproveStatus());
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        CrowdfundingInitialAuditInfo initialAuditInfo = crowdfundingOperationDelegate.selectCrowdfundingInitialAuditInfoByCaseId(recallObject.getCaseId());
        if (initialAuditInfo == null || initialAuditInfo.getWorkOrderId() != recallObject.getUgcTaskId()) {
            log.info("初次审核驳回-召回消息处理, 最后处理的工单id不是当前工单id。caseId:{}, workId:{}, currentWorkId:{}",
                    recallObject.getCaseId(), recallObject.getUgcTaskId(), initialAuditInfo == null ? "null" :
                            initialAuditInfo.getWorkOrderId());

            return ConsumeStatus.CONSUME_SUCCESS;
        }

        // 案例结束就不处理
        InitialAuditItem.InitialAuditOperation auditOperation = new InitialAuditItem.InitialAuditOperation();
        auditOperation.setCaseId(recallObject.getCaseId());
        List<Integer> passIds = Lists.newArrayList();

        // 通过的
        if (initialAuditInfo.getBaseInfo() == InitialAuditItem.MaterialStatus.PASS.getCode()) {
            passIds.add(InitialAuditItem.CASE_BASE_INFO_MATERIAL);
        }
        if (initialAuditInfo.getFirstApproveInfo() == InitialAuditItem.MaterialStatus.PASS.getCode()) {
            passIds.add(InitialAuditItem.FIRST_APPROVE_INFO);
        }

        auditOperation.setPassIds(passIds);
        auditOperation.setRejectItems(crowdfundingOperationDelegate
                .parseRejectDetail(initialAuditInfo.getRejectDetail(), recallObject.getCaseId()).getRejectMsgs());

        InitialAuditItem.InitialAuditRecall auditRecall = new InitialAuditItem.InitialAuditRecall();
        auditRecall.setOperationDetail(auditOperation);
        auditRecall.setRecallType(recallObject.getAfterDay());

        Message msg =  new Message(MQTopicCons.CF,  com.shuidihuzhu.cf.constants.MQTagCons.CF_INITIAL_AUDIT_OPERATION_RECALL_MSG,
                "" + System.currentTimeMillis(),
                auditRecall);
        MessageResult result = producer.send(msg);

        log.info("初审操作召回消息的发送. msg:{} result:{}", msg, result);

        return ConsumeStatus.CONSUME_SUCCESS;
    }

}
