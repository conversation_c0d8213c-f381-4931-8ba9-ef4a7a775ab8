package com.shuidihuzhu.cf.admin.util;

import com.shuidihuzhu.cf.admin.util.admin.AdminProvinceUtil;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @time 2019/10/11 上午10:15
 * @desc
 */
public class OwnershipUtil {
    public static String getProvinceNameByIdcard(String idCard) {
        if (StringUtils.isEmpty(idCard)) {
            return "";
        }
        String code = idCard.substring(0,2);
        if(StringUtils.isEmpty(code) || !StringUtils.isNumeric(code)){
            return StringUtils.EMPTY;
        }

        return AdminProvinceUtil.getProvinceBycode(Integer.valueOf(code));
    }
}
