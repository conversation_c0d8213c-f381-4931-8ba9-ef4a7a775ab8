package com.shuidihuzhu.cf.admin.controller.api.workorder;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderStatClient;
import com.shuidihuzhu.client.cf.workorder.v2.client.WorkOrderStatV2FeignClient;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @DATE 2019/4/1
 */
@RestController
@RequestMapping(path="/admin/workorder/stat")
@Slf4j
public class WorkOrderStatController {

    @Autowired
    private CfWorkOrderStatClient cfWorkOrderStatClient;

    @Autowired
    private WorkOrderStatV2FeignClient workOrderStatV2FeignClient;

    @RequiresPermission("stat:workorder-stat-subsidy")
    @RequestMapping(path = "workorder-stat-subsidy", method = RequestMethod.POST)
    public Response workorderStatSubsidy(int oneLevel, String twoLevel, long operId) {

        return cfWorkOrderStatClient.workorderStatSubsidy(oneLevel,twoLevel,operId);
    }

    @RequiresPermission("stat:workorder-stat-common")
    @RequestMapping(path = "workorder-stat-common", method = RequestMethod.POST)
    public Response workorderStatCommon(int oneLevel, String twoLevel, long operId) {

        return workOrderStatV2FeignClient.list(oneLevel,twoLevel,operId);
    }

    @RequiresPermission("stat:workorder-stat-cailiao")
    @RequestMapping(path = "workorder-stat-cailiao", method = RequestMethod.POST)
    public Response workorderStat(int oneLevel, String twoLevel, long operId) {

       return cfWorkOrderStatClient.workorderStatOne(oneLevel,twoLevel,operId);
    }

    @RequiresPermission("stat:workorder-stat-shouci")
    @RequestMapping(path = "workorder-stat-shouci", method = RequestMethod.POST)
    public Response workorderStatShouci(int oneLevel, String twoLevel, long operId) {
        return cfWorkOrderStatClient.workorderStatShouci(oneLevel,twoLevel,operId);
    }

    @RequiresPermission("stat:workorder-stat-chuci")
    @RequestMapping(path = "workorder-stat-chuci", method = RequestMethod.POST)
    public Response workorderStatChuci(int oneLevel, String twoLevel, long operId) {
        return cfWorkOrderStatClient.workorderStatChuci(oneLevel,twoLevel,operId);
    }

    @RequiresPermission("stat:workorder-stat-dianhua")
    @RequestMapping(path = "workorder-stat-dianhua", method = RequestMethod.POST)
    public Response workorderStatDianhua(int oneLevel, String twoLevel, long operId) {

        return cfWorkOrderStatClient.workorderStatDianhua(oneLevel,twoLevel,operId);
    }

    @RequiresPermission("stat:workorder-stat-ugc")
    @RequestMapping(path = "workorder-stat-ugc", method = RequestMethod.POST)
    public Response workorderStatUgc(int oneLevel, String twoLevel, long operId) {

        return cfWorkOrderStatClient.workorderStatUgc(oneLevel,twoLevel,operId);
    }

    @RequiresPermission("stat:workorder-stat-progress")
    @RequestMapping(path = "workorder-stat-progress", method = RequestMethod.POST)
    public Response workorderStatProgres(int oneLevel, String twoLevel, long operId) {

        return cfWorkOrderStatClient.workorderStatUgcProgress(oneLevel,twoLevel,operId);
    }

    @RequiresPermission("stat:workorder-stat-wenjuan")
    @RequestMapping(path = "workorder-stat-wenjuan", method = RequestMethod.POST)
    public Response workorderWenjuanStat(int oneLevel, String twoLevel, long operId) {

        return cfWorkOrderStatClient.workorderStatWenjuan(oneLevel,twoLevel,operId);
    }

    @RequiresPermission("stat:workorder-stat-funduse")
    @RequestMapping(path = "workorder-stat-funduse", method = RequestMethod.POST)
    public Response workorderStatFundUse(int oneLevel, String twoLevel, long operId) {
        return cfWorkOrderStatClient.workorderStatFundUse(oneLevel, twoLevel, operId);
    }

    @RequiresPermission("stat:workorder-stat-report")
    @RequestMapping(path = "workorder-stat-report", method = RequestMethod.POST)
    public Response workorderReportStat(int oneLevel, String twoLevel, long operId) {
        return cfWorkOrderStatClient.workorderStatReport(oneLevel,twoLevel,operId);
    }
}
