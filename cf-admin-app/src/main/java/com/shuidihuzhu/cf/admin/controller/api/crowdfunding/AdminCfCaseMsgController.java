package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CfCaseMsg;
import com.shuidihuzhu.cf.service.crowdfunding.CfCaseMsgService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @DATE 2020/10/19
 */
@RestController
@Slf4j
@RequestMapping("/admin/cf/case")
public class AdminCfCaseMsgController {

    @Autowired
    private CfCaseMsgService cfCaseMsgService;

    @RequestMapping(value = "/save-msg", method = RequestMethod.POST)
    public Response<String> saveMsg(@RequestParam(value = "caseId") int caseId,
                                    @RequestParam(value = "msgId") int msgId,
                                    @RequestParam(value = "msgValue") String msgValue) {

        if (caseId <= 0 || msgId <=0 || StringUtils.isBlank(msgValue)){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        cfCaseMsgService.saveMsg(caseId, msgId, msgValue);

        return NewResponseUtil.makeSuccess("");
    }

    @RequestMapping(value = "/get-msg", method = RequestMethod.POST)
    public Response<CfCaseMsg> getMsg(@RequestParam(value = "caseId") int caseId,
                                      @RequestParam(value = "msgId") int msgId){

        if (caseId <= 0 || msgId <=0){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        CfCaseMsg msg = cfCaseMsgService.getMsg(caseId,msgId);

        return NewResponseUtil.makeSuccess(msg);
    }

}