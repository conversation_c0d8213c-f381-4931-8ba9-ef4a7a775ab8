package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.client.UserRelationV2FeignClient;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.po.UserRelationModelPo;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.biz.risk.impl.UserBehaviorOrderServiceImpl;
import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import com.shuidihuzhu.cf.client.feign.cipher.constants.DesensitizeEnum;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.admin.util.OwnershipUtil;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfHospitalAuditBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfRepeatInfoBiz;
import com.shuidihuzhu.cf.biz.risk.IAllUserBehaviorService;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.client.feign.AuthorFeignClient;
import com.shuidihuzhu.cf.client.feign.CfCommonFeignClient;
import com.shuidihuzhu.cf.client.feign.CfPlatformEsFeignClient;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationRecordDTO;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.constants.admin.PermissionConst;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingOperationDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.activity.EnumPropertyVO;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceReadFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.service.crowdfunding.CfRiskService;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfUserInvitedLaunchCaseRecordModel;
import com.shuidihuzhu.client.cf.clewtrack.param.ChannelRefineDTO;
import com.shuidihuzhu.client.cf.growthtool.client.CfChannelFeignClient;
import com.shuidihuzhu.client.cf.olap.client.CaseBaseDataFeignClient;
import com.shuidihuzhu.client.cf.olap.model.CfCaseBaseData;
import com.shuidihuzhu.client.cf.risk.client.CfRiskClient;
import com.shuidihuzhu.client.cf.risk.model.enums.CfRiskBlackListEnum;
import com.shuidihuzhu.client.cf.risk.model.enums.UserOperationEnum;
import com.shuidihuzhu.client.dataservice.ds.v1.DsApiClient;
import com.shuidihuzhu.client.model.PhoneNumberInfoDO;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @time 2019/10/9 下午4:59
 * @desc
 */
@Slf4j
@RestController
@RequestMapping(path = "/admin/crowdfunding/user/manage")
public class AdminUserManageController {

    @Resource
    private IRiskDelegate riskDelegate;

    @Autowired
    private CfRiskService cfRiskService;

    @Autowired
    private CfRiskClient cfRiskClient;

    @Autowired
    private AuthorFeignClient authorFeignClient;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;

    @Autowired
    private CfCommonFeignClient cfCommonFeignClient;

    @Autowired
    private CfPlatformEsFeignClient cfPlatformEsFeignClient;

    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;

    @Autowired
    private CfChannelFeignClient cfChannelFeignClient;

    @Autowired
    private AdminCfHospitalAuditBiz cfHospitalAuditBiz;

    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Autowired
    private CfFinanceReadFeignClient cfFinanceReadFeignClient;

    @Autowired
    private AdminCfRepeatInfoBiz cfRepeatInfoBiz;

    @Autowired
    private ICrowdfundingOperationDelegate cfOperationDelegate;

    @Autowired
    private IAllUserBehaviorService allUserBehaviorService;
    @Resource
    private UserRelationV2FeignClient userRelationV2FeignClient;

    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private OldShuidiCipher oldShuidiCipher;
    @Autowired
    DsApiClient dsApiClient;

    @Autowired
    private CfClewtrackFeignClient cfClewtrackFeignClient;

    @Autowired
    private CaseBaseDataFeignClient cfCaseBaseDataFeignClient;
    @Autowired
    private CommonOperationRecordClient commonOperateClient;
    @Autowired
    private MaskUtil maskUtil;
    @Resource
    private UserBehaviorOrderServiceImpl userBehaviorOrderService;

    @RequestMapping(path = "/query-user-manage-base-info", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("user-manage:query-user-manage-base-info")
    public Response<List<AdminUserCaseVo>> queryUserManageBaseInfo(@ApiParam("手机号") @RequestParam(name = "mobile", required = false) String mobile,
                                                                   @ApiParam("用户id") @RequestParam(name = "queryUserId", required = false) long queryUserId,
                                                                   @ApiParam("姓名") @RequestParam(name = "name", required = false) String name,
                                                                   @ApiParam("身份证号") @RequestParam(name = "identity", required = false) String identity){

        Response<Map<String, Object>> validateRes = validateBaseParam(mobile, queryUserId, name, identity);
        if(ErrorCode.SUCCESS.getCode() != validateRes.getCode()){
            return ResponseUtil.makeResponse(validateRes.getCode(), validateRes.getMsg(), null);
        }

        Map<String, Object> userIdMap = validateRes.getData();
        queryUserId = userIdMap != null ? (long)userIdMap.getOrDefault("queryUserId", queryUserId) : queryUserId;

        if(queryUserId <= 0 && StringUtils.isEmpty(name) && StringUtils.isEmpty(identity)){
            //兼容，当只有手机号查询，而用户没有注册的情况
            if (queryUserId == 0 && StringUtils.isNotBlank(mobile)) {
//                List<CfClewBaseInfoDO> data = cfClewtrackFeignClient.getClewBaseInfoByMobile(mobile).getData();
//                if (CollectionUtils.isNotEmpty(data)) {
                    AdminUserCaseVo adminUserCaseVo = new AdminUserCaseVo();
                    adminUserCaseVo.setUserId(queryUserId);
                    adminUserCaseVo.setMobileMask(maskUtil.buildByDecryptPhone(mobile));
                    return NewResponseUtil.makeSuccess(Lists.newArrayList(adminUserCaseVo));
//                }
            }
            return NewResponseUtil.makeFail("暂无数据");
        }

        List<CfFirsApproveMaterial> materials = Lists.newArrayList();
        List<UserRealInfo> userRealInfos = Lists.newArrayList();
        if(queryUserId > 0 || StringUtils.isNotEmpty(identity) || StringUtils.isNotEmpty(name)){

            CfFirsApproveMaterial materialParam = new CfFirsApproveMaterial();
            materialParam.setUserId(queryUserId);
            materialParam.setSelfRealName(name);
            materialParam.setSelfCryptoIdcard(oldShuidiCipher.aesEncrypt(identity));
            materials = Optional.ofNullable(cfPlatformEsFeignClient.queryCaseByRaiser(materialParam).getData()).orElse(Lists.newArrayList());

            UserRealInfo userRealInfo = new UserRealInfo();
            userRealInfo.setUserId(queryUserId);
            userRealInfo.setCryptoIdCard(oldShuidiCipher.aesEncrypt(identity));
            userRealInfo.setName(name);
            userRealInfo.setIdcardVerifyStatus(IdcardVerifyStatus.HANDLE_SUCCESS.getCode());
            userRealInfos = cfPlatformEsFeignClient.queryUserRealInfo(userRealInfo).getData();
        }

        List<AdminUserCaseVo> userCaseVos = caclUserCaseVo(materials, userRealInfos);

        for (AdminUserCaseVo caseVo : userCaseVos){

            /**
             * 查询作为发起人的案例
             */
            CfFirsApproveMaterial materialParamRaiser = new CfFirsApproveMaterial();
            materialParamRaiser.setUserId(caseVo.getUserId());
            materialParamRaiser.setSelfRealName(caseVo.getName());
            materialParamRaiser.setSelfCryptoIdcard(oldShuidiCipher.aesEncrypt(caseVo.getIdentity()));
            List<CfFirsApproveMaterial> caseRaisers = Optional.ofNullable(cfPlatformEsFeignClient.queryCaseByRaiser(materialParamRaiser).getData())
                    .orElse(Lists.newArrayList())
                    .stream()
                    .filter(f -> f.getInfoId() > 0)
                    .collect(Collectors.toList());
            int caseWithRaiser = CollectionUtils.isNotEmpty(caseRaisers) ? caseRaisers.size() : 0;

            /**
             * 查询作为患者的案例
             */
            CfFirsApproveMaterial materialParamAuthor = new CfFirsApproveMaterial();
            materialParamAuthor.setPatientRealName(caseVo.getName());
            materialParamAuthor.setPatientCryptoIdcard(oldShuidiCipher.aesEncrypt(caseVo.getIdentity()));
            List<CfFirsApproveMaterial> caseAuthors = Optional.ofNullable(cfPlatformEsFeignClient.queryCaseByAuthor(materialParamAuthor).getData())
                    .orElse(Lists.newArrayList())
                    .stream()
                    .filter(m -> m.getInfoId() > 0)
                    .collect(Collectors.toList());
            int caseWithAuthor = CollectionUtils.isNotEmpty(caseAuthors) ? caseAuthors.size() : 0;

            /**
             * 查询作为收款人的案例
             */
            CrowdfundingInfoPayee payeeInfo = new CrowdfundingInfoPayee();
            payeeInfo.setName(caseVo.getName());
            payeeInfo.setIdCard(oldShuidiCipher.aesEncrypt(caseVo.getIdentity()));
            List<CrowdfundingInfoPayee> casePayees = cfPlatformEsFeignClient.queryCaseByPayee(payeeInfo).getData();
            int caseWithPayee = CollectionUtils.isNotEmpty(casePayees) ? casePayees.size() : 0;

            caseVo.setCaseWithRaiser(caseWithRaiser);
            caseVo.setCaseWithAuthor(caseWithAuthor);
            caseVo.setCaseWithPayee(caseWithPayee);
            if(StringUtils.isNotBlank(caseVo.getIdentity())){
                caseVo.setIdentityMask(maskUtil.buildByDecryptStrAndType(caseVo.getIdentity(), DesensitizeEnum.IDCARD));
                caseVo.setIdentity(null);
            }
            if(StringUtils.isNotBlank(caseVo.getMobile())){
                caseVo.setMobileMask(maskUtil.buildByDecryptPhone(caseVo.getMobile()));
                caseVo.setMobile(null);
            }
        }
        if (CollectionUtils.isEmpty(userCaseVos)) {
            AdminUserCaseVo adminUserCaseVo = new AdminUserCaseVo();
            mobile = userIdMap != null ? (String) userIdMap.getOrDefault("queryMobile", mobile) : mobile;
            adminUserCaseVo.setMobile(null);
            adminUserCaseVo.setMobileMask(maskUtil.buildByDecryptPhone(mobile));
            adminUserCaseVo.setUserId(queryUserId);
            userCaseVos = Lists.newArrayList(adminUserCaseVo);
        }

        return NewResponseUtil.makeSuccess(userCaseVos);
    }


     private void maskUserAccountInfo(UserAccountInfo userAccountInfo) {
        Optional.ofNullable(userAccountInfo)
                .filter(r -> StringUtils.isNotBlank(r.getMobile()))
                .ifPresent(r -> {
                    String mobile = r.getMobile();
                    userAccountInfo.setMobileMask(maskUtil.buildByDecryptPhone(mobile));
                    userAccountInfo.setMobile(null);
                });
     }

     private void maskCaseCountDetailInfo(CaseCountDetailInfo info) {
        Optional.ofNullable(info)
                .filter(r -> CollectionUtils.isNotEmpty(r.getCaseDetails()))
                .ifPresent(r-> {
                    List<CaseCommonDetailInfo> caseDetails = r.getCaseDetails();
                    caseDetails.stream()
                            .filter(item -> StringUtils.isNotBlank(item.getRaiserMobile()))
                            .forEach(item -> {
                                item.setRaiseMobileMask(maskUtil.buildByDecryptPhone(item.getRaiserMobile()));
                                item.setRaiserMobile(null);
                            });
                });
     }

     private void maskUserIdentityInfo(UserIdentityInfo userIdentityInfo) {
        Optional.ofNullable(userIdentityInfo)
                .ifPresent(r -> {
                    String identity = r.getIdentity();
                    if (StringUtils.isNotBlank(identity)) {
                        r.setIdentity(null);
                        r.setIdentityMask(maskUtil.buildByDecryptStrAndType(identity, DesensitizeEnum.IDCARD));
                    }
                    List<String> idCards = r.getIdcards();
                    if (CollectionUtils.isNotEmpty(idCards)) {
                        List<NumberMaskVo> idCardsMask = idCards.stream().map(idCard ->
                                maskUtil.buildByDecryptStrAndType(idCard, DesensitizeEnum.IDCARD)
                        ).collect(Collectors.toList());
                        r.setIdCardsMask(idCardsMask);
                        r.setIdcards(null);
                    }
                });
     }

    @RequiresPermission(PermissionConst.USER_MODULE_BASIC)
    @RequestMapping(path = "/query-user-manage-detail-info", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response<AdminUserManageDetailInfo> queryUserManageBaseDeailInfo(@ApiParam("手机号") @RequestParam(name = "mobile") String mobile,
                                                                            @ApiParam("用户id") @RequestParam(name = "queryUserId", required = false, defaultValue = "0") long queryUserId,
                                                                            @ApiParam("姓名") @RequestParam(name = "name", required = false) String name,
                                                                            @ApiParam("身份证号") @RequestParam(name = "identity", required = false) String identity){

        if (queryUserId <= 0 || StringUtils.isEmpty(name) || StringUtils.isEmpty(identity)) {
            if (StringUtils.isNotBlank(mobile)) {
                //获取归属地
                AdminUserManageDetailInfo userManageDetailInfo = new AdminUserManageDetailInfo();
                UserAccountInfo userAccountInfo = new UserAccountInfo();
                userAccountInfo.setMobile(mobile);
                //获取归属地
                String mobileOwnerShip = getMobileInfo(mobile);
                userAccountInfo.setMobileOwnerShip(mobileOwnerShip);
                userAccountInfo.setUserId(queryUserId);
                userManageDetailInfo.setUserAccountInfo(userAccountInfo);
                CaseCountDetailInfo defaultDetail = new CaseCountDetailInfo();
                userManageDetailInfo.setRaiserCaseDetail(defaultDetail);
                userManageDetailInfo.setAuthorCaseDetail(defaultDetail);
                userManageDetailInfo.setPayeeCaseDetail(defaultDetail);
                this.maskUserAccountInfo(userAccountInfo);
                return ResponseUtil.makeSuccess(userManageDetailInfo);
            }
            return ResponseUtil.makeSuccess(null);
        }

        UserInfoModel userInfo = userInfoServiceBiz.getUserInfoByUserId(queryUserId);
        if(Objects.isNull(userInfo)){
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NOT_EXISTS);
        }

        /**
         * 查询作为发起人的案例
         */
        CfFirsApproveMaterial materialParamRaiser = new CfFirsApproveMaterial();
        materialParamRaiser.setUserId(queryUserId);
        materialParamRaiser.setSelfRealName(name);
        materialParamRaiser.setSelfCryptoIdcard(oldShuidiCipher.aesEncrypt(identity));
        List<CfFirsApproveMaterial> caseRaisers = Optional.ofNullable(cfPlatformEsFeignClient.queryCaseByRaiser(materialParamRaiser).getData())
                .orElse(Lists.newArrayList())
                .stream()
                .filter(f -> f.getInfoId() > 0)
                .collect(Collectors.toList());
        int caseWithRaiser = CollectionUtils.isNotEmpty(caseRaisers) ? caseRaisers.size() : 0;
        int raiserEndCount = 0;
        List<CaseCommonDetailInfo> caseDetailsRaiser = Lists.newArrayList();
        for (CfFirsApproveMaterial approveMaterial : caseRaisers){
            CrowdfundingInfo cfInfo = crowdfundingFeignClient.getCaseInfoById(approveMaterial.getInfoId()).getData();
            if(Objects.isNull(cfInfo)){
                log.info("发起人案例查找cfInfo为空,approveMaterial:{}", JSON.toJSONString(approveMaterial));
                continue;
            }

            if(cfInfo.getEndTime().before(new Date())){
                raiserEndCount++;
            }

            caseDetailsRaiser.add(buildCaseCommonDetail(cfInfo, approveMaterial));
        }

        /**
         * 查询作为患者的案例
         */
        CfFirsApproveMaterial materialParamAuthor = new CfFirsApproveMaterial();
        materialParamAuthor.setPatientRealName(name);
        materialParamAuthor.setPatientCryptoIdcard(oldShuidiCipher.aesEncrypt(identity));
        List<CfFirsApproveMaterial> caseAuthors = Optional.ofNullable(cfPlatformEsFeignClient.queryCaseByAuthor(materialParamAuthor).getData()).orElse(Lists.newArrayList())
                .stream()
                .filter(m -> m.getInfoId() > 0)
                .collect(Collectors.toList());

        int caseWithAuthor = CollectionUtils.isNotEmpty(caseAuthors) ? caseAuthors.size() : 0;
        int authorEndCount = 0;
        List<CaseCommonDetailInfo> caseDetailsAuthor = Lists.newArrayList();
        for (CfFirsApproveMaterial material : caseAuthors){
            int infoId = material.getInfoId();
            CrowdfundingInfo caseInfo = crowdfundingFeignClient.getCaseInfoById(infoId).getData();
            if(Objects.isNull(caseInfo)){
                log.info("患者案例查找cfInfo为空,approveMaterial:{}", JSON.toJSONString(material));
                continue;
            }

            if(caseInfo.getEndTime().before(new Date())){
                authorEndCount++;
            }

            caseDetailsAuthor.add(buildCaseCommonDetail(caseInfo, material));
        }

        /**
         * 查询作为收款人的案例
         */
        CrowdfundingInfoPayee payeeInfo = new CrowdfundingInfoPayee();
        payeeInfo.setName(name);
        payeeInfo.setIdCard(oldShuidiCipher.aesEncrypt(identity));
        List<CrowdfundingInfoPayee> casePayees = cfPlatformEsFeignClient.queryCaseByPayee(payeeInfo).getData();
        int caseWithPayee = CollectionUtils.isNotEmpty(casePayees) ? casePayees.size() : 0;
        int payeeEndCount = 0;
        List<CaseCommonDetailInfo> caseDetailsPayee = Lists.newArrayList();
        Set<String> idcardSet = Sets.newHashSet();
        if (casePayees != null) {
            for (CrowdfundingInfoPayee payee : casePayees){
                idcardSet.add(shuidiCipher.decrypt(payee.getBankCard()));
                CrowdfundingInfo caseInfo = crowdfundingFeignClient.getCaseInfoById(payee.getCaseId()).getData();
                CfFirsApproveMaterial material = authorFeignClient.getAuthorInfoByInfoId(payee.getCaseId()).getData();

                if(Objects.isNull(caseInfo) || Objects.isNull(material)){
                    log.info("收款人案例查找cfInfo为空,payee:{}", JSON.toJSONString(payee));
                    continue;
                }

                if(caseInfo.getEndTime().before(new Date())){
                    payeeEndCount++;
                }

                caseDetailsPayee.add(buildCaseCommonDetail(caseInfo, material));
            }
        }

        IdcardVerifyWhiteList idcardWhite = riskDelegate.getByNameAndIdcard(name, identity);

        UserAccountInfo userAccountInfo = new UserAccountInfo();
        userAccountInfo.setMobile(mobile);
        //获取归属地
        String mobileOwnerShip = "";
        if (StringUtils.isNotBlank(mobile)) {
            mobileOwnerShip = getMobileInfo(mobile);
        }
        userAccountInfo.setMobileOwnerShip(mobileOwnerShip);
        userAccountInfo.setUserId(queryUserId);
        userAccountInfo.setNickName(Objects.nonNull(userInfo) ? userInfo.getNickname() : "");
        userAccountInfo.setBlackUser(cfRiskService.queryBlackValid(queryUserId, CfRiskBlackListEnum.LimitType.UGC));
        userAccountInfo.setForbidRaise(!cfRiskService.operatorValid(queryUserId, 0, UserOperationEnum.RAISE));

        UserIdentityInfo userIdentityInfo = new UserIdentityInfo();
        userIdentityInfo.setIdentity(identity);
        userIdentityInfo.setIdentityOwnerShip(OwnershipUtil.getProvinceNameByIdcard(identity));
        userIdentityInfo.setName(name);
        userIdentityInfo.setIdcards(Lists.newArrayList(idcardSet));
        userIdentityInfo.setWhiteUser(Objects.nonNull(idcardWhite) ? true : false);

        CaseCountDetailInfo raiserCaseDetail = new CaseCountDetailInfo();
        raiserCaseDetail.setTotalCount(caseWithRaiser);
        raiserCaseDetail.setEndCount(raiserEndCount);
        raiserCaseDetail.setFundingCount(caseWithRaiser - raiserEndCount);
        raiserCaseDetail.setCaseDetails(caseDetailsRaiser);

        CaseCountDetailInfo authorCaseDetail = new CaseCountDetailInfo();
        authorCaseDetail.setTotalCount(caseWithAuthor);
        authorCaseDetail.setEndCount(authorEndCount);
        authorCaseDetail.setFundingCount(caseWithAuthor - authorEndCount);
        authorCaseDetail.setCaseDetails(caseDetailsAuthor);

        CaseCountDetailInfo payeeCaseDetail = new CaseCountDetailInfo();
        payeeCaseDetail.setTotalCount(caseWithPayee);
        payeeCaseDetail.setEndCount(payeeEndCount);
        payeeCaseDetail.setFundingCount(caseWithPayee - payeeEndCount);
        payeeCaseDetail.setCaseDetails(caseDetailsPayee);


        AdminUserManageDetailInfo userManageDetailInfo = new AdminUserManageDetailInfo();
        userManageDetailInfo.setUserAccountInfo(userAccountInfo);
        userManageDetailInfo.setUserIdentityInfo(userIdentityInfo);
        userManageDetailInfo.setRaiserCaseDetail(raiserCaseDetail);
        userManageDetailInfo.setAuthorCaseDetail(authorCaseDetail);
        userManageDetailInfo.setPayeeCaseDetail(payeeCaseDetail);

        this.maskUserAccountInfo(userManageDetailInfo.getUserAccountInfo());
        this.maskUserIdentityInfo(userManageDetailInfo.getUserIdentityInfo());
        this.maskCaseCountDetailInfo(userManageDetailInfo.getRaiserCaseDetail());
        this.maskCaseCountDetailInfo(userManageDetailInfo.getAuthorCaseDetail());
        this.maskCaseCountDetailInfo(userManageDetailInfo.getPayeeCaseDetail());



        return ResponseUtil.makeSuccess(userManageDetailInfo);
    }


    @ApiOperation("查询当前用户可查询行为")
    @PostMapping("get-owned-behavior-list")
    public OperationResult<List<EnumPropertyVO>> getOwnedBehaviorList() {
        List<EnumPropertyVO> results = allUserBehaviorService.getOwnedBehavior(ContextUtil.getAdminUserId());
        return OperationResult.success(results);
    }

    @ApiOperation("查询当前用户可查询模块")
    @PostMapping("get-owned-module-list")
    public OperationResult<List<EnumPropertyVO>> getOwnedModuleList() {
        List<EnumPropertyVO> results = allUserBehaviorService.getOwnedModule(ContextUtil.getAdminUserId());
        return OperationResult.success(results);
    }

    @RequestMapping(path = "/query-user-behavior-detail-info", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("user-manage:query-user-behavior-detail-info")
    public Response<List<AdminUserBehaviorDetail>> queryUserBehaviorDeailInfo(@ApiParam("手机号") @RequestParam(name = "mobile") String mobile,
                                                                            @ApiParam("用户id") @RequestParam(name = "queryUserId", required = false) long queryUserId,
                                                                            @ApiParam("姓名") @RequestParam(name = "name", required = false) String name,
                                                                            @ApiParam("身份证号") @RequestParam(name = "identity", required = false) String identity,
                                                                              @ApiParam("行为类型") @RequestParam(name = "behaviorType") int behaviorType){
        int adminUserId = ContextUtil.getAdminUserId();
        UserBehaviorEnum userBehaviorEnum = UserBehaviorEnum.parse(behaviorType);
        if(Objects.isNull(userBehaviorEnum)){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        UserInfoModel userInfoModel = null;
        if (queryUserId >= 0) {
            userInfoModel = userInfoServiceBiz.getUserInfoByUserId(queryUserId);
        }

        List<AdminUserBehaviorDetail> behaviorDetails = allUserBehaviorService.queryUserBehavior(adminUserId, mobile,
                queryUserId, name, identity, userInfoModel, userBehaviorEnum);
        behaviorDetails.forEach(r -> {
            UserInfoDetail userInfoDetail = r.getUserInfoDetail();
            if (Objects.nonNull(userInfoDetail)) {
                userInfoDetail.setMobile(StringUtils.EMPTY);
            }
        });
        return NewResponseUtil.makeSuccess(behaviorDetails);
    }

    @RequestMapping(path = "/query-order-comment", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("user-manage:query-user-behavior-detail-info")
    public Response<List<UserSubBehavoirDetail>> queryOrderComment(@ApiParam("订单id") @RequestParam(name = "orderId", required = false) long orderId) {
        return NewResponseUtil.makeSuccess(userBehaviorOrderService.getOrderComment(orderId));
    }

    @RequiresPermission(PermissionConst.USER_MODULE_BASIC)
    @RequestMapping(path = "/query-user-relation-detail-info", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response<List<UserRelationModel>> queryUserRelationDeailInfo(@ApiParam("用户id") @RequestParam(name = "queryUserId", required = false) long queryUserId){

        if(queryUserId <= 0){
            return ResponseUtil.makeSuccess(null);
        }

        UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByUserId(queryUserId);
        if(Objects.isNull(userInfoModel)){
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NOT_EXISTS);
        }

        List<UserRelationModelPo> relationModelPos = userRelationV2FeignClient.getByUserId(queryUserId,"0");
        if(CollectionUtils.isEmpty(relationModelPos)){
            return ResponseUtil.makeSuccess(Lists.newArrayList());
        }

        List<UserRelationModel> relationModels = Lists.newArrayList();
        for (UserRelationModelPo relationModelPo : relationModelPos){
            UserRelationModel userRelationModel = new UserRelationModel();
            BeanUtils.copyProperties(relationModelPo, userRelationModel);
            relationModels.add(userRelationModel);
        }

        return ResponseUtil.makeSuccess(relationModels);
    }

    /**
     * @deprecated v2接口上线之后这个就能下掉了。
     * @see {@link com.shuidihuzhu.cf.admin.controller.api.crowdfunding.AdminUserManageController#queryUserFriendRelationshipV2(long)}
     * @param queryUserId
     * @return
     */
    @RequestMapping(path = "/query-user-friend-relationship", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @Deprecated
    public Response<List<UserFriendRelationship>> queryUserFriendRelationship(@ApiParam("用户id") @RequestParam(name = "queryUserId", required = false) long queryUserId){

        if(queryUserId <= 0){
            return ResponseUtil.makeSuccess(null);
        }

        UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByUserId(queryUserId);
        if(Objects.isNull(userInfoModel)){
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NOT_EXISTS);
        }

        Set<Long> friendUserIdSet = cfCommonFeignClient.queryFriendRelationship(queryUserId).getData();
        if(CollectionUtils.isEmpty(friendUserIdSet)){
            return ResponseUtil.makeSuccess(Lists.newArrayList());
        }

        List<Long> friendUserIds = Lists.newArrayList(friendUserIdSet);

        List<UserFriendRelationship> userFriendRelationships = Lists.newArrayList();
        for (Long friendUserId : friendUserIds){
            UserFriendRelationship friendRelationship = new UserFriendRelationship();
            friendRelationship.setUserId(friendUserId);
            friendRelationship.setDegree(1);
            userFriendRelationships.add(friendRelationship);
        }

        return ResponseUtil.makeSuccess(userFriendRelationships);
    }

    @RequiresPermission(PermissionConst.USER_MODULE_BASIC)
    @RequestMapping(path = "/query-user-friend-relationship-v2", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response<UserFriendRelationshipResult> queryUserFriendRelationshipV2(@ApiParam("用户id") @RequestParam(name = "queryUserId", required = false) long queryUserId){

        if(queryUserId <= 0){
            return ResponseUtil.makeSuccess(null);
        }

        UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByUserId(queryUserId);
        if(Objects.isNull(userInfoModel)){
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NOT_EXISTS);
        }

        int friendsCountWhenRaise = getFriendsCountWhenRaise(queryUserId);


        Set<Long> friendUserIdSet = cfCommonFeignClient.queryFriendRelationship(queryUserId).getData();
        if(CollectionUtils.isEmpty(friendUserIdSet)){
            return ResponseUtil.makeSuccess(Lists.newArrayList());
        }

        List<Long> friendUserIds = Lists.newArrayList(friendUserIdSet);

        int currentFriendsCount = CollectionUtils.size(friendUserIds);

        List<UserFriendRelationship> userFriendRelationships = Lists.newArrayList();
        for (Long friendUserId : friendUserIds){
            UserFriendRelationship friendRelationship = new UserFriendRelationship();
            friendRelationship.setUserId(friendUserId);
            friendRelationship.setDegree(1);
            userFriendRelationships.add(friendRelationship);
        }
        UserFriendRelationshipResult  relationshipResult = UserFriendRelationshipResult.builder()
                .currentFriendsCount(currentFriendsCount)
                .friendsCountWhenRaise(friendsCountWhenRaise)
                .friendRelationshipList(userFriendRelationships).build();


        return ResponseUtil.makeSuccess(relationshipResult);
    }

    private int getFriendsCountWhenRaise(long queryUserId) {
        int friendsCountWhenRaise = 0;
        com.shuidihuzhu.cf.client.response.FeignResponse<CrowdfundingInfo> response = crowdfundingFeignClient.getLastByUserId(queryUserId);

        if(response == null || response.getData() == null){
            friendsCountWhenRaise = 0;
            return friendsCountWhenRaise;
        } else {
            int caseId = response.getData().getId();
            Response<CfCaseBaseData> caseBaseDataResponse = cfCaseBaseDataFeignClient.getCaseBaseData(caseId);
            if(caseBaseDataResponse == null || caseBaseDataResponse.getData() == null){
                friendsCountWhenRaise = 0;
                return  friendsCountWhenRaise;
            }

            CfCaseBaseData cfCaseBaseData = caseBaseDataResponse.getData();
            friendsCountWhenRaise = cfCaseBaseData.getFriendCount();
        }
        return friendsCountWhenRaise;
    }

    private CaseCommonDetailInfo buildCaseCommonDetail(CrowdfundingInfo cfInfo, CfFirsApproveMaterial material){
        int caseId = cfInfo.getId();
        String infoUuid = cfInfo.getInfoId();

        ChannelRefineDTO channelRefineDTO = new ChannelRefineDTO();
        channelRefineDTO.setInfoId(Long.valueOf(caseId));
        channelRefineDTO.setUserId(cfInfo.getUserId());
        channelRefineDTO.setChannel(cfInfo.getChannel());
        Response<List<CfUserInvitedLaunchCaseRecordModel>> channelRes = cfChannelFeignClient.getCfUserInvitedLaunchCaseRecordByInfoIds(Lists.newArrayList(channelRefineDTO));

        CfHospitalAuditInfo cfHospitalAuditInfo = cfHospitalAuditBiz.getByInfoUuid(infoUuid);
        CrowdfundingInfoPayee payee = crowdfundingDelegate.getCrowdfundingInfoPayeeByInfoUuid(infoUuid);
        UserInfoModel userInfo = userInfoServiceBiz.getUserInfoByUserId(cfInfo.getUserId());

        Integer repeatStatus = 0;
        AdminCfRepeatInfo repeatInfo = cfRepeatInfoBiz.buildRepeatInfo(caseId);
        if(repeatInfo != null) {
            Map<Integer, Set<Integer>> repeatCases = repeatInfo.getRepeatBaseInfoMap();
            Map<Integer, Integer> caseId2RepeatInfo = Maps.newHashMap();
            for (Map.Entry<Integer, Set<Integer>> entry : repeatCases.entrySet()) {
                repeatStatus = entry.getKey();
                Collection<Integer> caseIds = entry.getValue();
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(caseIds)) {
                    for (Integer i : caseIds) {
                        caseId2RepeatInfo.put(i, repeatStatus);
                    }
                }
            }

            Integer reasonCode = caseId2RepeatInfo.get(caseId);
            AdminCfRepeatInfo.RepeatReason reason = AdminCfRepeatInfo.RepeatReason.valueOfCode(Objects.nonNull(reasonCode) ? reasonCode : 0);
            repeatStatus = AdminCfRepeatView.RepeatReasonView.getReasonViewByRepeatReason(reason).getCode();
        }


        FeignResponse<Map<String, Object>> response = cfFinanceReadFeignClient.getCapitalData(infoUuid);
        int cfCapitalStatus = 0;
        if(response.ok() && MapUtils.isNotEmpty(response.getData())){
            cfCapitalStatus = Objects.nonNull(response.getData().get("cfCapitalStatus")) ? (Integer) response.getData().get("cfCapitalStatus") : 0;
        }

        List<CrowdfundingReport> reports = riskDelegate.getCrowdfundingReportListByInfoId(caseId);

        CrowdfundingOperation operation = cfOperationDelegate.getByInfoId(infoUuid);

        List<String> riskDetail = Lists.newArrayList();
        Map<Integer, Boolean> mapResponse = cfRiskClient.queryOperateMapDetail(caseId, Lists.newArrayList(UserOperationEnum.SHARE.getCode(),
                UserOperationEnum.SHOW.getCode(), UserOperationEnum.BANNER.getCode())).getData();
        for (Integer key : mapResponse.keySet()){
            boolean action = mapResponse.get(key);
            if(!action){
                riskDetail.add("禁止" + UserOperationEnum.parse(key).getMsg());
            }
        }

        CaseCommonDetailInfo caseCommonDetailInfo = new CaseCommonDetailInfo();
        caseCommonDetailInfo.setCaseId(caseId);
        caseCommonDetailInfo.setInfoUuid(infoUuid);
        caseCommonDetailInfo.setRepeatStatus(repeatStatus);
        caseCommonDetailInfo.setTitle(cfInfo.getTitle());
        caseCommonDetailInfo.setRaiserMobile(Objects.nonNull(userInfo) ? shuidiCipher.decrypt(userInfo.getCryptoMobile()) : "");
        caseCommonDetailInfo.setRaiserName(UserRelTypeEnum.SELF.getValue() == material.getUserRelationType() ? material.getPatientRealName() : material.getSelfRealName());
        caseCommonDetailInfo.setAuthorName(Objects.nonNull(material) ? material.getPatientRealName() : "");
        caseCommonDetailInfo.setPayeeName(Objects.nonNull(payee) ? payee.getName() : "");
        caseCommonDetailInfo.setAmount(cfInfo.getAmount());
        caseCommonDetailInfo.setTargetAmount(cfInfo.getTargetAmount());
        caseCommonDetailInfo.setAuditStatus(cfInfo.getStatus().value());
        caseCommonDetailInfo.setCfCapitalStatus(cfCapitalStatus);
        caseCommonDetailInfo.setStartTime(cfInfo.getCreateTime());
        caseCommonDetailInfo.setEndTime(cfInfo.getEndTime());
        caseCommonDetailInfo.setChannel(Objects.nonNull(channelRes) && CollectionUtils.isNotEmpty(channelRes.getData()) ? channelRes.getData().get(0).getServiceUserInfo(shuidiCipher) : "");
        caseCommonDetailInfo.setHospitalStatus(Objects.nonNull(cfHospitalAuditInfo) ? cfHospitalAuditInfo.getAuditStatus() : CrowdfundingInfoStatusEnum.UN_SAVE.getCode());
        caseCommonDetailInfo.setReportCount(reports.size());
        caseCommonDetailInfo.setReportStatus(Objects.nonNull(operation) ? operation.getReportStatus() : CaseReportStatusEnum.NO_REPORT.getValue());
        caseCommonDetailInfo.setRiskAction(String.join("/", riskDetail));

        return caseCommonDetailInfo;
    }

    public List<AdminUserCaseVo> caclUserCaseVo(List<CfFirsApproveMaterial> materials, List<UserRealInfo> userRealInfos){
        List<AdminUserCaseVo> userCaseVos = Lists.newArrayList();
        Set<String> set = Sets.newHashSet();
        for (CfFirsApproveMaterial material : materials){
            int userRelType = material.getUserRelationType();
            long userid = material.getUserId();
            String idcard = shuidiCipher.decrypt(UserRelTypeEnum.SELF.getValue() == userRelType ? material.getPatientCryptoIdcard() : material.getSelfCryptoIdcard());
            String nm = UserRelTypeEnum.SELF.getValue() == userRelType ? material.getPatientRealName() : material.getSelfRealName();

            if(set.contains(idcard + nm + userid)){
                continue;
            }
            set.add(idcard + nm + userid);

            UserInfoModel userInfo = userInfoServiceBiz.getUserInfoByUserId(userid);

            AdminUserCaseVo adminUserCaseVo = new AdminUserCaseVo();
            adminUserCaseVo.setMobile(Objects.nonNull(userInfo) ? shuidiCipher.decrypt(userInfo.getCryptoMobile()): "");
            adminUserCaseVo.setUserId(userid);
            adminUserCaseVo.setIdentity(idcard);
            adminUserCaseVo.setName(nm);
            userCaseVos.add(adminUserCaseVo);
        }

        if (userRealInfos != null) {
            for (UserRealInfo userRealInfo : userRealInfos){
                long userid = userRealInfo.getUserId();
                String idcard = shuidiCipher.decrypt(userRealInfo.getCryptoIdCard());
                String nm = userRealInfo.getName();

                if(set.contains(idcard + nm + userid)){
                    continue;
                }
                set.add(idcard + nm + userid);

                UserInfoModel userInfo = userInfoServiceBiz.getUserInfoByUserId(userid);

                AdminUserCaseVo adminUserCaseVo = new AdminUserCaseVo();
                adminUserCaseVo.setMobile(Objects.nonNull(userInfo) ? shuidiCipher.decrypt(userInfo.getCryptoMobile()) : "");
                adminUserCaseVo.setUserId(userid);
                adminUserCaseVo.setIdentity(idcard);
                adminUserCaseVo.setName(nm);
                userCaseVos.add(adminUserCaseVo);
            }
        }

        return userCaseVos;
    }


    public Response<Map<String, Object>> validateBaseParam(String mobile, long userId, String name, String identity){
        if(StringUtils.isEmpty(mobile) && userId <= 0 && StringUtils.isEmpty(name) && StringUtils.isEmpty(identity)){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        UserInfoModel userInfo = userInfoServiceBiz.getUserInfoByUserId(userId);
        if(StringUtils.isNotEmpty(mobile) && userId > 0){
            String accountMobile = Objects.nonNull(userInfo) ? shuidiCipher.decrypt(userInfo.getCryptoMobile()) : StringUtils.EMPTY;
            if(!mobile.equals(accountMobile)){
                return ResponseUtil.makeResponse(AdminErrorCode.MOBILE_PHONE_USER_NOT_MATCH.getCode(), AdminErrorCode.MOBILE_PHONE_USER_NOT_MATCH.getMsg(), "");
            }

            return ResponseUtil.makeSuccess(buildUserMap(userId, accountMobile));
        }

        if(StringUtils.isNotEmpty(mobile) && userId <= 0){
            UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByMobile(mobile);
            userId = Objects.nonNull(userInfoModel) ? userInfoModel.getUserId() : 0L;
            mobile = Optional.ofNullable(userInfoModel).map(UserInfoModel::getCryptoMobile).map(shuidiCipher::decrypt).orElse(mobile);
            return ResponseUtil.makeSuccess(buildUserMap(userId, mobile));
        }

        if(userId > 0 && Objects.nonNull(userInfo)){
            return ResponseUtil.makeSuccess(buildUserMap(userId, shuidiCipher.decrypt(userInfo.getCryptoMobile())));
        }

        return ResponseUtil.makeSuccess(Maps.newHashMap());
    }

    private Map<String, Object> buildUserMap(long userId, String mobile){
        Map<String, Object> map = Maps.newHashMap();
        map.put("queryUserId", userId);
        map.put("queryMobile", mobile);
        return map;
    }


    private String getMobileInfo(String mobile) {
        //获取归属地
        com.shuidihuzhu.client.model.Response<PhoneNumberInfoDO> phoneNumberInfo = dsApiClient.getPhoneNumberInfo(Long.valueOf(mobile));
        String mobileOwnerShip = "";
        if (phoneNumberInfo.getCode() == 0) {
            PhoneNumberInfoDO data = phoneNumberInfo.getData();
            String phoneProvince = Optional.ofNullable(data).map(PhoneNumberInfoDO::getPhoneProvince).orElse("");
            String phoneCity = Optional.ofNullable(data).map(PhoneNumberInfoDO::getPhoneCity).orElse("");
            mobileOwnerShip = phoneProvince + "-" + phoneCity;
        }
        return mobileOwnerShip;
    }

    @PostMapping("can-transfer")
    public Response<String> canTransfer(@RequestParam String mobile) {

        String result = allUserBehaviorService.queryHasRegister(mobile);
        return StringUtils.isBlank(result) ? ResponseUtil.makeSuccess("") :
                ResponseUtil.makeFail("转入手机号未注册水滴筹，无法转入资产");
    }

//    @RequiresPermission(PermissionConst.USER_TRANSFER_PROPERTY)
    @PostMapping("transfer/property")
    public Response<String> transferProperty(@RequestBody AdminUserBehaviorDetail.UserCaseProperty property) {
        property.setUserId(ContextUtil.getAdminUserId());

        AdminErrorCode errorCode = allUserBehaviorService.transferProperty(property);
        return NewResponseUtil.makeError(errorCode);
    }

    @PostMapping("select-transfer-logs")
    public Response<List<OperationRecordDTO>> selectTransferLog(@RequestParam("mobile") String mobile) {

        if (!StringUtils.isNumeric(mobile)) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        return NewResponseUtil.makeSuccess(commonOperateClient.listByBizIdAndActionTypes(Long.valueOf(mobile),
                OperationActionTypeEnum.USER_PROPERTY_TRANSFER));
    }


    @PostMapping("select-user-property")
    public Response<AdminUserBehaviorDetail.UserCaseProperty> selectUserProperty(@RequestParam("mobile") String mobile) {

        return NewResponseUtil.makeSuccess(allUserBehaviorService.selectUserProperty(mobile));
    }


}



