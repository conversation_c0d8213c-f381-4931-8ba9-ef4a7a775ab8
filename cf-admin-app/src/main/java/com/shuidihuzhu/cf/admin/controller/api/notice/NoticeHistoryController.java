//package com.shuidihuzhu.cf.admin.controller.api.notice;
//
//import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
//import com.shuidihuzhu.cf.enums.AdminErrorCode;
//import org.apache.commons.lang3.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.cloud.context.config.annotation.RefreshScope;
//import org.springframework.stereotype.Controller;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestMethod;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.ResponseBody;
//
//
//import com.shuidihuzhu.cf.admin.util.HuzhuRequestUtil;
//import com.shuidihuzhu.common.web.util.NewResponseUtil;
//
//
///**
// * @Author: wuxinlong
// * @Since: 2017-03-28
// */
//@RefreshScope
//@Controller
//@RequestMapping(path = "/admin/cf/notice/history")
//public class NoticeHistoryController {
//	private static final Logger LOGGER = LoggerFactory.getLogger(NoticeHistoryController.class);
//	@Value("${url.shuidi.api:}")
//	private String apiUrl;
//	private String baseUrl = null;
//
//	@RequestMapping(path = "list", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
//	@ResponseBody
//	@RequiresPermission("notice-history:see")
//	public Object list(@RequestParam(name = "searchInfo", required = false, defaultValue = "{}") String searchInfo,
//	                   @RequestParam(name = "pagination", required = false, defaultValue = "{}") String pagination) {
//		return HuzhuRequestUtil.getRequestResult(getBaseUrl(), "list", "searchInfo", searchInfo, "pagination", pagination);
//	}
//
//	@RequestMapping(path = "detail", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
//	@ResponseBody
//	@RequiresPermission("notice-history:see")
//	public Object detail(String uuid) {
//		if (StringUtils.isEmpty(uuid)) {
//			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
//		}
//		return HuzhuRequestUtil.getRequestResult(getBaseUrl(), "detail", "uuid", uuid);
//	}
//
//	@RequestMapping(path = "add-or-update", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
//	@ResponseBody
//	@RequiresPermission("notice-history:see")
//	public Object addOrUpdate(String data) {
//		if (StringUtils.isEmpty(data)) {
//			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
//		}
//		return HuzhuRequestUtil.getRequestResult(getBaseUrl(), "add-or-update", "data", data);
//	}
//
//	@RequestMapping(path = "clone", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
//	@ResponseBody
//	@RequiresPermission("notice-history:see")
//	public Object clone(String uuid) {
//		if (StringUtils.isEmpty(uuid)) {
//			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
//		}
//		return HuzhuRequestUtil.getRequestResult(getBaseUrl(), "clone", "uuid", uuid);
//	}
//
//	@RequestMapping(path = "test", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
//	@ResponseBody
//	@RequiresPermission("notice-history:see")
//	public Object test(String uuid, String mobiles, String noticeRequestParam) {
//		if (StringUtils.isEmpty(uuid) || StringUtils.isEmpty(mobiles)) {
//			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
//		}
//
//		return HuzhuRequestUtil.getRequestResult(getBaseUrl(), "test", "uuid", uuid,
//		                                         "mobiles", mobiles, "noticeRequestParam", noticeRequestParam);
//	}
//
//	@RequestMapping(path = "start", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
//	@ResponseBody
//	@RequiresPermission("notice-history:start")
//	public Object start(String uuid, String noticeRequestParam) {
//		if (StringUtils.isEmpty(uuid)) {
//			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
//		}
//
//		return HuzhuRequestUtil.getRequestResult(getBaseUrl(), "start", "uuid", uuid, "noticeRequestParam", noticeRequestParam);
//	}
//
//	@RequestMapping(path = "send-wxrecord", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
//	@ResponseBody
//	public Object doSend(String businessInfo) {
//		if (StringUtils.isEmpty(businessInfo)) {
//			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
//		}
//
//		return HuzhuRequestUtil.getRequestResult(getBaseUrl(), "send-wxrecord", "businessInfo", businessInfo, "oldStatus", "-1", "newStatus", "0");
//	}
//
//
//	@RequestMapping(path = "refresh", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
//	@ResponseBody
//	@RequiresPermission("notice-history:see")
//	public Object refresh(String uuid) {
//		if (StringUtils.isEmpty(uuid)) {
//			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
//		}
//
//		return HuzhuRequestUtil.getRequestResult(getBaseUrl(), "refresh", "uuid", uuid);
//	}
//
//	private String getBaseUrl() {
//		if (null == baseUrl) {
//			baseUrl = apiUrl + "/admin/notice/history/";
//		}
//
//		return baseUrl;
//	}
//}
