package com.shuidihuzhu.cf.admin.util;

import com.google.common.collect.Maps;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.admin.PageUtil;

import java.util.List;
import java.util.Map;

/**
 * @Author: wuxinlong
 * @Since: 2016-12-12
 */
public class DataWithPaginationUtil {
	public static Object handleListWithPagination(List<?> dataList) {
		Map<String, Object> page = PageUtil.transform2PageMap(dataList);
		Map<String, Object> result = Maps.newHashMap();
		result.put("data", dataList);
		result.put("pagination", page);
		return NewResponseUtil.makeSuccess(result);
	}

	public static Object handleListWithPagination(List<?> paginationList , List<?> dataList) {
		Map<String, Object> page = PageUtil.transform2PageMap(paginationList);
		return NewResponseUtil.makeSuccess(handleResult(dataList , page));
	}

	private static Map<String, Object> handleResult(List<?> dataList , Map<String, Object> page){
		Map<String, Object> result = Maps.newHashMap();
		result.put("data", dataList);
		result.put("pagination", page);
		return result;
	}
}
