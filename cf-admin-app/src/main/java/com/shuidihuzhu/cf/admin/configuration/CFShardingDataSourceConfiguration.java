package com.shuidihuzhu.cf.admin.configuration;

import com.shuidihuzhu.pay.common.interceptor.PageInterceptor;
import io.shardingjdbc.core.api.ShardingDataSourceFactory;
import io.shardingjdbc.core.api.config.ShardingRuleConfiguration;
import io.shardingjdbc.core.api.config.TableRuleConfiguration;
import io.shardingjdbc.core.api.config.strategy.InlineShardingStrategyConfiguration;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.Collections;
import java.util.Map;
import java.util.Properties;

/**
 * Created by zhouyou on 2017/12/11.
 */
@Configuration
public class CFShardingDataSourceConfiguration {

    @Resource(name = "crowdfundingDataSource")
    private DataSource crowdfundingDataSource;

    @Resource(name = "crowdfundingSlaveDataSource")
    private DataSource crowdfundingSlaveDataSource;

    @Bean
    public PageInterceptor pageInterceptor(){
        return new PageInterceptor();
    }

    @Bean("shardingCrowdfundingMaster")
    public DataSource shardingCrowdfundingMaster() throws SQLException {
        return buildDataSource(Collections.singletonMap("cf1", crowdfundingDataSource));
    }

    @Bean("shardingCrowdfundingSlave")
    public DataSource shardingCrowdfundingSlave() throws SQLException {
        return buildDataSource(Collections.singletonMap("cf1", crowdfundingSlaveDataSource));
    }

    private DataSource buildDataSource(Map<String, DataSource> dataSource) throws SQLException {
        TableRuleConfiguration capital = new TableRuleConfiguration();
        capital.setLogicTable("cf_capital_account_record");
        capital.setActualDataNodes("cf1.cf_capital_account_record_${0..31}");
        capital.setTableShardingStrategyConfig(new InlineShardingStrategyConfiguration("info_uuid", "cf_capital_account_record_${ (info_uuid.hashCode() & 0x7FFFFFFF) % 32 }"));

        TableRuleConfiguration pay = new TableRuleConfiguration();
        pay.setLogicTable("cf_pay_split_flow_record");
        pay.setActualDataNodes("cf1.cf_pay_split_flow_record_${0..31}");
        pay.setTableShardingStrategyConfig(new InlineShardingStrategyConfiguration("user_id", "cf_pay_split_flow_record_${user_id % 32}"));

        ShardingRuleConfiguration shardingRuleConfig = new ShardingRuleConfiguration();
        shardingRuleConfig.setDefaultDataSourceName("cf1");
        shardingRuleConfig.getTableRuleConfigs().add(capital);
        shardingRuleConfig.getTableRuleConfigs().add(pay);

        Properties prop = new Properties();
        prop.setProperty("sql.show", "false");
        prop.setProperty("executor.size", "20");
        return ShardingDataSourceFactory.createDataSource(dataSource, shardingRuleConfig, Collections.emptyMap(), prop);
    }

}
