package com.shuidihuzhu.cf.admin.mq;

import com.shuidihuzhu.cf.admin.mq.common.BaseMessageConsumer;
import com.shuidihuzhu.cf.constants.AdminUserIDConstants;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 工单自动回收mq
 * <AUTHOR>
 */
@Service
@Slf4j
@RocketMQListener(id = MQTagCons.CF_WORK_ORDER_RELEASE,
        tags = MQTagCons.CF_WORK_ORDER_RELEASE,
        topic = MQTopicCons.CF)
public class AdminReleaseWorkOrderConsumer extends BaseMessageConsumer<Long> implements MessageListener<Long> {

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @Override
    protected boolean handle(ConsumerMessage<Long> consumerMessage) {
        Long workOrderId = consumerMessage.getPayload();
        Response<Long> longResponse = cfWorkOrderClient.releaseWorkOrderSystem(workOrderId, AdminUserIDConstants.SYSTEM);
        return longResponse.ok();
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
