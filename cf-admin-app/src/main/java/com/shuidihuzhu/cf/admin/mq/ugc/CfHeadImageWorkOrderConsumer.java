package com.shuidihuzhu.cf.admin.mq.ugc;

import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminUGCTask;
import com.shuidihuzhu.client.cf.growthtool.client.ClewPreproseMaterialFeignClient;
import com.shuidihuzhu.client.cf.workorder.CfUgcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.UgcWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderLevel;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Author: wangpeng
 * @Date: 2021/3/17 19:50
 * @Description:
 */
@Service
@RocketMQListener(id = MQTagCons.CASE_HEAD_IMAGE_CREATE_WORK_ORDER,
        group = "cf-admin" + MQTagCons.CASE_HEAD_IMAGE_CREATE_WORK_ORDER,
        tags = MQTagCons.CASE_HEAD_IMAGE_CREATE_WORK_ORDER,
        topic = MQTopicCons.CF)
@Slf4j
public class CfHeadImageWorkOrderConsumer implements MessageListener<Integer> {

    @Resource
    private CfUgcWorkOrderClient cfUgcWorkOrderClient;

    @Resource
    private ClewPreproseMaterialFeignClient clewPreproseMaterialFeignClient;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<Integer> mqMessage) {
        Integer caseId = mqMessage.getPayload();

        Response<PreposeMaterialModel.MaterialInfoVo> materialInfoVoResponse = clewPreproseMaterialFeignClient.selectMaterialByCaseIdForQC(caseId);
        log.info("cfHeadImageWorkOrderConsumer creatHeadImgWorkOrder {}, {} ", caseId, materialInfoVoResponse);
        if (Objects.isNull(materialInfoVoResponse) || Objects.isNull(materialInfoVoResponse.getData())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        PreposeMaterialModel.MaterialInfoVo materialInfoVo = materialInfoVoResponse.getData();
        if (StringUtils.isEmpty(materialInfoVo.getHeadPictureUrl())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        //生成新工单
        UgcWorkOrder workOrder = new UgcWorkOrder();
        workOrder.setCaseId(caseId);
        workOrder.setOrderType(WorkOrderType.ugcprogress.getType());
        workOrder.setContentType(AdminUGCTask.Content.HEAD_IMAGE_URL.getCode() + "");
        workOrder.setOrderlevel(OrderLevel.low.getType());
        workOrder.setHeadImageUrl(materialInfoVo.getHeadPictureUrl());
        cfUgcWorkOrderClient.createUgc(workOrder);

        return ConsumeStatus.CONSUME_SUCCESS;
    }

}
