package com.shuidihuzhu.cf.admin.controller.api.risk;

import com.shuidihuzhu.cf.client.apipure.feign.SimpleBlacklistFeignClient;
import com.shuidihuzhu.cf.client.apipure.model.simpleblack.SimpleBlackModel;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Api("简单黑名单接口")
@RestController
@Slf4j
@RequestMapping("/admin/cf/risk/simple-blacklist")
public class SimpleBlacklistController {

    @Resource
    private SimpleBlacklistFeignClient simpleBlacklistFeignClient;

    private static final int SHARE_CONTENT_TEST = 1;

    @PostMapping("add")
    OperationResult<Void> add(@RequestParam("actionType") int actionType,
                              @RequestParam(value = "caseId") int caseId) {
        return simpleBlacklistFeignClient.add(actionType, caseId);
    }

    @PostMapping("get-by-case-id")
    OperationResult<SimpleBlackModel> getByCaseId(@RequestParam("actionType") int actionType,
                                                  @RequestParam(value = "caseId") int caseId) {
        return simpleBlacklistFeignClient.getByCaseId(actionType, caseId);
    }

    @ApiOperation("添加转发内容测试黑名单")
    @PostMapping("add-share-content")
    OperationResult<Void> addShareContent(@RequestParam(value = "caseId") int caseId) {
        return simpleBlacklistFeignClient.add(SHARE_CONTENT_TEST, caseId);
    }

    @ApiOperation("查询转发内容测试黑名单")
    @PostMapping("get-share-content-by-case-id")
    OperationResult<SimpleBlackModel> getShareContentByCaseId(@RequestParam(value = "caseId") int caseId) {
        return simpleBlacklistFeignClient.getByCaseId(SHARE_CONTENT_TEST, caseId);
    }

}
