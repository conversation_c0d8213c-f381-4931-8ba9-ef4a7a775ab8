package com.shuidihuzhu.cf.admin.controller.api.innerapi;

import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyAction;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.crowdfunding.CfSupplyActionService;
import com.shuidihuzhu.client.cf.admin.client.CfSupplyActionClient;
import com.shuidihuzhu.client.cf.admin.model.CfSupplyActionVo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @DATE 2020/7/16
 */
@RestController
public class CfSupplyActionController implements CfSupplyActionClient {

    @Autowired
    private CfSupplyActionService cfSupplyActionService;

    @Override
    public Response<CfSupplyActionVo> getByActionId(long actionId) {

        return cfSupplyActionService.getByActionId(actionId);
    }

    @Override
    public Response<String> submitAction(CfSupplyActionVo cfSupplyActionVo) {

        if (cfSupplyActionVo == null || CollectionUtils.isEmpty(cfSupplyActionVo.getList())){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        OpResult opResult = cfSupplyActionService.doSubmit(cfSupplyActionVo);

        return NewResponseUtil.makeError(opResult.getErrorCode());
    }

    @Override
    public Response<Long> getLastlyInitActionId(int caseId) {

        return NewResponseUtil.makeSuccess(cfSupplyActionService.selectLatelyInitActionId(caseId));
    }
}
