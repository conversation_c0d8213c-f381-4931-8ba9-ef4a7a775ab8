package com.shuidihuzhu.cf.admin.configuration;

import com.shuidihuzhu.cf.enhancer.subject.redislock.RedisLockAop;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 *
 */
@Slf4j
@Component
public class RedisLockConfiguration implements InitializingBean {

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler cfRedissonHandler;

    @Resource
    private RedisLockAop redisLockAop;

    @Override
    public void afterPropertiesSet() throws Exception {
        redisLockAop.setRedissonHandler(cfRedissonHandler);
    }

}
