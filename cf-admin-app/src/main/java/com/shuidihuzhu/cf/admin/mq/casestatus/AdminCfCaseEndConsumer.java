package com.shuidihuzhu.cf.admin.mq.casestatus;

import com.shuidihuzhu.cf.admin.mq.common.BaseMessageConsumer;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.CaseEndModel;
import com.shuidihuzhu.cf.service.admin.AdminPageRecommendCaseService;
import com.shuidihuzhu.cf.service.crowdfunding.DealWithStatusService;
import com.shuidihuzhu.cf.service.workorder.imagePublic.ImagePublicWorkOrderService;
import com.shuidihuzhu.cf.service.workorder.juanzhan.JuanzhuanWorkOrderService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@RocketMQListener(id = MQTagCons.CF_CASE_END,
        group = "cf-admin-" + MQTagCons.CF_CASE_END + "-deal",
        tags = MQTagCons.CF_CASE_END,
        topic = MQTopicCons.CF)
@Slf4j
public class AdminCfCaseEndConsumer extends BaseMessageConsumer<CaseEndModel> implements MessageListener<CaseEndModel> {

    @Autowired
    private DealWithStatusService dealWithStatusService;

    @Autowired
    private JuanzhuanWorkOrderService juanzhuanWorkOrderService;

    @Autowired
    private AdminPageRecommendCaseService recommendCaseService;

    @Resource
    private ImagePublicWorkOrderService imagePublicWorkOrderService;

    @Override
    protected boolean handle(ConsumerMessage<CaseEndModel> consumerMessage) {
        log.info("AdminCfCaseEndConsumer is receive {}", consumerMessage);
        CaseEndModel caseEndModel = consumerMessage.getPayload();
        int caseId = caseEndModel.getCaseId();

        try {
            imagePublicWorkOrderService.autoCloseWorkOrder(caseId);
        } catch (Exception e) {
            log.info("AdminCfCaseEndConsumer autoCloseWorkOrder is error ", e);
        }

        dealWithStatusService.touchHandleNoLongerProcess(caseId);

        juanzhuanWorkOrderService.caseEnd4Close(caseId);

        recommendCaseService.endCase(caseId);
        return true;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
