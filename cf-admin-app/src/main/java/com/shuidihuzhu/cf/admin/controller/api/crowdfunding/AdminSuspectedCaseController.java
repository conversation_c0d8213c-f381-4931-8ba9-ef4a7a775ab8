package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.client.feign.cipher.constants.DesensitizeEnum;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CfSuspectedCaseInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CfSuspectedCaseInfoModel;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @package: com.shuidihuzhu.cf.admin.controller.api.crowdfunding
 * @Author: liujiawei
 * @Date: 2019-03-20  00:48
 */
@Slf4j
@RestController
@RefreshScope
@RequestMapping(path = "admin/cf/suspected-case")
public class AdminSuspectedCaseController {
    @Autowired
    private IRiskDelegate riskDelegate;

    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    @Autowired
    private MaskUtil maskUtil;

    @RequestMapping(path = "get-list", method = RequestMethod.POST)
    @RequiresPermission("suspected-case:get-list")
    public Response getList(@RequestParam(name = "nickName", defaultValue = "", required = false) String nickName,
                            @RequestParam(name = "mobile", defaultValue = "", required = false) String mobile,
                            @RequestParam(name = "title", defaultValue = "", required = false) String title,
                            @RequestParam(name = "infoUuid", defaultValue = "") String infoUuid,
                            @RequestParam(name = "idNumber", defaultValue = "", required = false) String idNumber,
                            @RequestParam(name = "start", defaultValue = "", required = false) String start,
                            @RequestParam(name = "end", defaultValue = "", required = false) String end,
                            @RequestParam(name = "pageSise", defaultValue = "10") int pageSise,
                            @RequestParam(name = "page", defaultValue = "1") int page) {

        Map<String, Object> res = Maps.newHashMap();
        List<CfSuspectedCaseInfo> cfSuspectedCaseInfos = null;
        if (StringUtils.isNotBlank(idNumber)) {
             cfSuspectedCaseInfos = riskDelegate.getByLikeIdNumber(idNumber);
            res.put("list", cfSuspectedCaseInfos);
        } else {
            Date startDate = null;
            Date endDate = null;
            if (StringUtils.isNotBlank(end) && StringUtils.isNotBlank(start)) {
                startDate = DateUtil.getDateFromLongString(start);
                endDate = DateUtil.getDateFromLongString(end);
            }
            CfSuspectedCaseInfoModel cfSuspectedCaseInfoModel = new CfSuspectedCaseInfoModel();
            cfSuspectedCaseInfoModel.setStart(startDate);
            cfSuspectedCaseInfoModel.setEnd(endDate);
            cfSuspectedCaseInfoModel.setNickname(nickName);
            cfSuspectedCaseInfoModel.setMobile(mobile);
            cfSuspectedCaseInfoModel.setMobileMask(maskUtil.buildByDecryptPhone(mobile));
            cfSuspectedCaseInfoModel.setIdNumberMask(maskUtil.buildByDecryptStrAndType(idNumber, DesensitizeEnum.IDCARD));
            cfSuspectedCaseInfoModel.setEncryptMobile(oldShuidiCipher.aesEncrypt(mobile));
            cfSuspectedCaseInfoModel.setTitle(title);
            cfSuspectedCaseInfoModel.setInfoUuid(infoUuid);
            cfSuspectedCaseInfoModel.setSize(pageSise);
            cfSuspectedCaseInfoModel.setLimit((page - 1) * pageSise);
            cfSuspectedCaseInfos = riskDelegate.getCfSuspectedCaseInfoListBySerch(cfSuspectedCaseInfoModel);
            cfSuspectedCaseInfos.forEach(cfSuspectedCaseInfo -> {
                cfSuspectedCaseInfo.setMobileMask(maskUtil.buildByDecryptPhone(cfSuspectedCaseInfo.getMobile()));
                cfSuspectedCaseInfo.setMobile(null);
                cfSuspectedCaseInfo.setIdNumberMask(maskUtil.buildByDecryptStrAndType(cfSuspectedCaseInfo.getIdNumber(), DesensitizeEnum.IDCARD));
                cfSuspectedCaseInfo.setIdNumber(null);
            });
            res.put("list", cfSuspectedCaseInfos);
        }
        res.put("page", page);
        res.put("pageSize", cfSuspectedCaseInfos.size());
        return NewResponseUtil.makeSuccess(res);
    }

    @RequestMapping(path = "add", method = RequestMethod.POST)
    @RequiresPermission("suspected-case:add")
    public Response add(@RequestParam(name = "nickname", defaultValue = "") String nickname,
                        @RequestParam(name = "mobile", defaultValue = "") String mobile,
                        @RequestParam(name = "title", defaultValue = "") String title,
                        @RequestParam(name = "infoUuid", defaultValue = "") String infoUuid,
                        @RequestParam(name = "idNumber", defaultValue = "") String idNumber,
                        @RequestParam(name = "reason", defaultValue = "") String reason,
                        @RequestParam(name = "faithlessDate", defaultValue = "") String faithlessDate) {
        if (StringUtils.isBlank(infoUuid)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        Date faithlesDate = new Date();
        if (StringUtils.isNotBlank(faithlessDate)) {
            try {
                faithlesDate = DateUtil.getDateFromLongString(faithlessDate);
            } catch (Exception e) {
                log.error("日期转化异常", e);
            }
        }
        CfSuspectedCaseInfo cfSuspectedCaseInfo = new CfSuspectedCaseInfo();
        cfSuspectedCaseInfo.setNickname(nickname);
        cfSuspectedCaseInfo.setMobile(mobile);
        cfSuspectedCaseInfo.setTitle(title);
        cfSuspectedCaseInfo.setInfoUuid(infoUuid);
        cfSuspectedCaseInfo.setIdNumber(idNumber);
        cfSuspectedCaseInfo.setReason(reason);
        cfSuspectedCaseInfo.setFaithlessDate(faithlesDate);
        log.debug("失信人添加:{}", cfSuspectedCaseInfo);
        int re = riskDelegate.addCfSuspectedCaseInfo(cfSuspectedCaseInfo);
        if (re == 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
        }
        return NewResponseUtil.makeSuccess(AdminErrorCode.SUCCESS);
    }

    @RequestMapping(path = "update", method = RequestMethod.POST)
    @RequiresPermission("suspected-case:update")
    public Response update(@RequestParam(name = "nickname", defaultValue = "", required = false) String nickname,
                           @RequestParam(name = "mobile", defaultValue = "", required = false) String mobile,
                           @RequestParam(name = "title", defaultValue = "", required = false) String title,
                           @RequestParam(name = "infoUuid", defaultValue = "", required = false) String infoUuid,
                           @RequestParam(name = "idNumber", defaultValue = "", required = false) String idNumber,
                           @RequestParam(name = "reason", defaultValue = "", required = false) String reason,
                           @RequestParam(name = "faithlessDate", defaultValue = "", required = false) String faithlessDate,
                           @RequestParam(name = "id", defaultValue = "0") int id) {
        if (id <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        Date faithlesDate = null;
        if (StringUtils.isNotBlank(faithlessDate)) {
            try {
                faithlesDate = DateUtil.getDateFromLongString(faithlessDate);
            } catch (Exception e) {
                log.error("日期转化异常", e);
                faithlesDate = null;
            }
        }
        CfSuspectedCaseInfo cfSuspectedCaseInfo = new CfSuspectedCaseInfo();
        cfSuspectedCaseInfo.setId(id);
        cfSuspectedCaseInfo.setNickname(nickname);
        cfSuspectedCaseInfo.setMobile(mobile);
        cfSuspectedCaseInfo.setTitle(title);
        cfSuspectedCaseInfo.setInfoUuid(infoUuid);
        cfSuspectedCaseInfo.setIdNumber(idNumber);
        cfSuspectedCaseInfo.setReason(reason);
        cfSuspectedCaseInfo.setFaithlessDate(faithlesDate);
        log.debug("失信人更新:{}", cfSuspectedCaseInfo);
        riskDelegate.updateCfSuspectedCaseInfo(cfSuspectedCaseInfo);
        return NewResponseUtil.makeSuccess(AdminErrorCode.SUCCESS);
    }

    @RequestMapping(path = "delete", method = RequestMethod.POST)
    @RequiresPermission("suspected-case:delete")
    public Response delete(@RequestParam(name = "id", defaultValue = "0") int id) {
        if (id <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        int re = riskDelegate.deleteCfSuspectedCaseInfo(id);
        if (re == 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
        }
        return NewResponseUtil.makeSuccess(AdminErrorCode.SUCCESS);
    }
}
