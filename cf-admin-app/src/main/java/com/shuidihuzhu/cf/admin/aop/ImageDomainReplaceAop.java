package com.shuidihuzhu.cf.admin.aop;

import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.service.AdminImageService;
import com.shuidihuzhu.cf.store.enums.CfDomainEnum;
import com.shuidihuzhu.cf.store.model.AnalysisUrl;
import com.shuidihuzhu.cf.store.plugins.CosDomainPlugins;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

//@Aspect
//@Component
@Slf4j
public class ImageDomainReplaceAop {

    @Autowired
    private AdminImageService imageService;

    @Around("execution(* com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingAttachmentDao.*(..))")
    public Object attachmentAop(ProceedingJoinPoint joinPoint) throws Throwable {

        Object proceed = joinPoint.proceed();
        if (proceed == null) {
            return null;
        }
        if (proceed instanceof CrowdfundingAttachment) {
            CrowdfundingAttachment crowdfundingAttachment = (CrowdfundingAttachment) proceed;
            if (AttachmentTypeEnum.ATTACH_PAYEE_RELATION_VIDEO == crowdfundingAttachment.getType()
                    || AttachmentTypeEnum.ATTACH_VIDEO == crowdfundingAttachment.getType()) {
                return proceed;
            }
            String url = crowdfundingAttachment.getUrl();
            if (StringUtils.isBlank(url)) {
                return proceed;
            }
            String newUrl = imageService.convertSingleUrl(url);
            log.info("ImageDomainReplaceAop attachmentAop replaceUrl:{}，newUrl:{}", url, newUrl);
            crowdfundingAttachment.setUrl(newUrl);
            return crowdfundingAttachment;
        }
        if (proceed instanceof List) {
            List resultList = (List) proceed;
            if (CollectionUtils.isEmpty(resultList)) {
                return proceed;
            }
            //检查类型
            final Object first = resultList.get(0);
            if (!(first instanceof CrowdfundingAttachment)) {
                return proceed;
            }
            List<CrowdfundingAttachment> attachmentList = (List<CrowdfundingAttachment>) proceed;
            for (CrowdfundingAttachment crowdfundingAttachment : attachmentList) {
                if (AttachmentTypeEnum.ATTACH_PAYEE_RELATION_VIDEO == crowdfundingAttachment.getType()
                        || AttachmentTypeEnum.ATTACH_VIDEO == crowdfundingAttachment.getType()) {
                    continue;
                }
                String url = crowdfundingAttachment.getUrl();
                if (StringUtils.isBlank(url)) {
                    continue;
                }
                String newUrl = imageService.convertSingleUrl(url);
                log.info("ImageDomainReplaceAop attachmentAop replaceUrl:{}，newUrl:{}", url, newUrl);
                crowdfundingAttachment.setUrl(newUrl);

            }
            return attachmentList;
        }
        return proceed;
    }


    @Around("execution(* com.shuidihuzhu.cf.dao.admin.AdminWorkOrderFlowDao.*(..))")
    public Object workOrderFlowAop(ProceedingJoinPoint joinPoint) throws Throwable {

        Object proceed = joinPoint.proceed();
        if (proceed == null) {
            return null;
        }
        if (proceed instanceof AdminWorkOrderFlow) {
            AdminWorkOrderFlow workOrderFlow = (AdminWorkOrderFlow) proceed;
            String problemImg = workOrderFlow.getProblemImg();
            workOrderFlow.setProblemImg(imageService.convertMultiUrl(problemImg));

            String handleImg = workOrderFlow.getHandleImg();
            workOrderFlow.setHandleImg(imageService.convertMultiUrl(handleImg));
            return workOrderFlow;
        }
        if (proceed instanceof List) {
            List resultList = (List) proceed;
            if (CollectionUtils.isEmpty(resultList)) {
                return proceed;
            }
            Object first = resultList.get(0);
            if (!(first instanceof AdminWorkOrderFlow)) {
                return proceed;
            }
            List<AdminWorkOrderFlow> workOrderFlowList = (List<AdminWorkOrderFlow>) proceed;
            for (AdminWorkOrderFlow workOrderFlow : workOrderFlowList) {

                String problemImg = workOrderFlow.getProblemImg();
                workOrderFlow.setProblemImg(imageService.convertMultiUrl(problemImg));

                String handleImg = workOrderFlow.getHandleImg();
                workOrderFlow.setHandleImg(imageService.convertMultiUrl(handleImg));
            }
            return workOrderFlowList;
        }
        return proceed;
    }


    @Around("execution(* com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingInfoDao.*(..))")
    public Object adminCrowdfundingInfoAop(ProceedingJoinPoint joinPoint) throws Throwable {

        Object proceed = joinPoint.proceed();
        if (proceed == null) {
            return null;
        }
        if (proceed instanceof CrowdfundingInfo) {
            CrowdfundingInfo crowdfundingInfo = (CrowdfundingInfo) proceed;
            String titleImg = crowdfundingInfo.getTitleImg();
            if (StringUtils.isBlank(titleImg)){
                return proceed;
            }
            String newUrl = imageService.convertSingleUrl(titleImg);
            log.info("ImageDomainReplaceAop adminCrowdfundingInfoAop replaceUrl:{}，newUrl:{}", titleImg, newUrl);
            crowdfundingInfo.setTitleImg(newUrl);
            return crowdfundingInfo;
        }
        if (proceed instanceof List) {
            List resultList = (List) proceed;
            if (CollectionUtils.isEmpty(resultList)) {
                return proceed;
            }
            Object first = resultList.get(0);
            if (!(first instanceof CrowdfundingInfo)) {
                return proceed;
            }
            List<CrowdfundingInfo> crowdfundingInfoList = (List<CrowdfundingInfo>) proceed;
            for (CrowdfundingInfo info : crowdfundingInfoList) {
                String titleImg = info.getTitleImg();
                if (StringUtils.isBlank(titleImg)){
                    continue;
                }
                String newUrl = imageService.convertSingleUrl(titleImg);
                log.info("ImageDomainReplaceAop adminCrowdfundingInfoAop replaceUrl:{}，newUrl:{}", titleImg, newUrl);
                info.setTitleImg(newUrl);
            }
            return crowdfundingInfoList;
        }
        return proceed;
    }

    @Around("execution(* com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdFundingProgressDao.*(..))")
    public Object adminCrowdFundingProgressAop(ProceedingJoinPoint joinPoint) throws Throwable {

        Object proceed = joinPoint.proceed();
        if (proceed == null) {
            return null;
        }
        if (proceed instanceof CrowdFundingProgress) {
            CrowdFundingProgress progress = (CrowdFundingProgress) proceed;
            String imageUrls = progress.getImageUrls();
            if (StringUtils.isBlank(imageUrls)){
                return proceed;
            }
            String newUrl = imageService.convertMultiUrl(imageUrls);
            log.info("ImageDomainReplaceAop adminCrowdFundingProgressAop replaceUrl:{}，newUrl:{}", imageUrls, newUrl);
            progress.setImageUrls(newUrl);
            return progress;
        }
        if (proceed instanceof List) {
            List resultList = (List) proceed;
            if (CollectionUtils.isEmpty(resultList)) {
                return proceed;
            }
            Object first = resultList.get(0);
            if (!(first instanceof CrowdFundingProgress)) {
                return proceed;
            }
            List<CrowdFundingProgress> progressList = (List<CrowdFundingProgress>) proceed;
            for (CrowdFundingProgress progress : progressList) {
                String imageUrls = progress.getImageUrls();
                String newUrl = imageService.convertMultiUrl(imageUrls);
                progress.setImageUrls(newUrl);
            }
            return progressList;
        }
        return proceed;
    }

    @Around("execution(* com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingReportDao.*(..))")
    public Object adminCrowdfundingReportAop(ProceedingJoinPoint joinPoint) throws Throwable {

        Object proceed = joinPoint.proceed();
        if (proceed == null) {
            return null;
        }
        if (proceed instanceof CrowdfundingReport) {
            CrowdfundingReport report = (CrowdfundingReport) proceed;
            String imageUrls = report.getImageUrls();
            if (StringUtils.isBlank(imageUrls)){
                return proceed;
            }
            String newUrl = imageService.convertMultiUrl(imageUrls);
            log.info("ImageDomainReplaceAop adminCrowdfundingReportAop replaceUrl:{}，newUrl:{}", imageUrls, newUrl);
            report.setImageUrls(newUrl);
            return report;
        }
        if (proceed instanceof List) {
            List resultList = (List) proceed;
            if (CollectionUtils.isEmpty(resultList)) {
                return proceed;
            }
            Object first = resultList.get(0);
            if (!(first instanceof CrowdfundingReport)) {
                return proceed;
            }
            List<CrowdfundingReport> reportList = (List<CrowdfundingReport>) proceed;
            for (CrowdfundingReport report : reportList) {
                String imageUrls = report.getImageUrls();
                String newUrl = imageService.convertMultiUrl(imageUrls);
                report.setImageUrls(newUrl);
            }
            return reportList;
        }
        return proceed;
    }




}
