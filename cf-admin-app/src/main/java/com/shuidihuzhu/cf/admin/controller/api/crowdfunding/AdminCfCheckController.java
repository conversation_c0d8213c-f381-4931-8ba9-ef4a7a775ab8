package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceEmergencyFeignClient;
import com.shuidihuzhu.cf.finance.client.model.CheckEmergencyParam;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.baseservice.pay.model.CardBinResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2025/6/6 16:04
 */
@Slf4j
@RestController
@RequestMapping("/admin/cf/check/")
public class AdminCfCheckController {

    @Resource
    private CfFinanceEmergencyFeignClient cfFinanceEmergencyFeignClient;

    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @PostMapping("check-emergency")
    public Response<Void> checkEmergency(@RequestBody CheckEmergencyParam checkEmergencyParam) {

        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(checkEmergencyParam.getCaseId());
        if (Objects.isNull(crowdfundingInfo)) {
            return NewResponseUtil.makeError(CfErrorCode.CF_NOT_FOUND);
        }

        checkEmergencyParam.setInfoId(crowdfundingInfo.getInfoId());
        FeignResponse<CfErrorCode> response = cfFinanceEmergencyFeignClient.checkEmergencyError(checkEmergencyParam);
        CfErrorCode cfErrorCode = Optional.ofNullable(response)
                .filter(FeignResponse::ok)
                .map(FeignResponse::getData)
                .orElse(CfErrorCode.SUCCESS);

        if (cfErrorCode != CfErrorCode.SUCCESS) {
            return NewResponseUtil.makeError(cfErrorCode);
        }

        return NewResponseUtil.makeSuccess();
    }

}
