package com.shuidihuzhu.cf.admin.mq.ugc;

import com.shuidihuzhu.cf.biz.crowdfunding.CfSensitiveWordRecordBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.UserOperationStatConstant;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingComment;
import com.shuidihuzhu.cf.model.crowdfunding.deliver.CrowdfundingCommentDeliver;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;

/**
 * Created by sven on 18/8/2.
 *
 * <AUTHOR>
 */
@Service
@RocketMQListener(id = MQTagCons.CF_COMMENT_MSG,
        group = "cf-admin-comment-group",
        tags = MQTagCons.CF_COMMENT_MSG,
        topic = MQTopicCons.CF)
@Slf4j
public class CfCommentConsumer implements MessageListener<CrowdfundingCommentDeliver> {

    @Resource
    private CfSensitiveWordRecordBiz cfSensitiveWordRecordBiz;

    @Resource
    private MeterRegistry meterRegistry;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdfundingCommentDeliver> mqMessage) {

        if(mqMessage == null || mqMessage.getPayload() == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        //上报一次打点信息
        meterRegistry.counter(UserOperationStatConstant.USER_OPERATING_STAT,
                              UserOperationStatConstant.OPERATION, UserOperationStatConstant.COMMENT).increment();

        log.info("receive comment message: {}",mqMessage.getPayload());

        try {
            cfSensitiveWordRecordBiz.buildCommentOne(mqMessage.getPayload());
        } catch (Exception e){
            log.error("CfCommentConsumer {}", mqMessage.getPayload(), e);
            return ConsumeStatus.RECONSUME_LATER;
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
