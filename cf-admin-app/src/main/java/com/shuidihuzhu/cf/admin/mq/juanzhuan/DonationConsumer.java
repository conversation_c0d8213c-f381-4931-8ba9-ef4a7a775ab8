package com.shuidihuzhu.cf.admin.mq.juanzhuan;

import com.shuidihuzhu.cf.admin.mq.common.BaseMessageConsumer;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.service.workorder.juanzhan.JuanzhuanWorkOrderService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @DATE 2020/5/8
 */
@Slf4j
@Service
@RocketMQListener(  id = MQTagCons.CF_PAY_SUCCESS_MSG,
                    topic = MQTopicCons.CF,
                    tags = MQTagCons.CF_PAY_SUCCESS_MSG,
                    group = "juanzhuan_"+MQTagCons.CF_PAY_SUCCESS_MSG)
public class DonationConsumer  extends BaseMessageConsumer<CrowdfundingOrder> implements MessageListener<CrowdfundingOrder> {

    @Autowired
    private JuanzhuanWorkOrderService juanzhuanWorkOrderService;

    @Override
    protected boolean handle(ConsumerMessage<CrowdfundingOrder> consumerMessage) {
        CrowdfundingOrder c  = consumerMessage.getPayload();
        //创建d0工单
        juanzhuanWorkOrderService.createJuanzhuanD0OnPaySuccess(c.getId(),c.getCrowdfundingId());

        //已达标关闭
        juanzhuanWorkOrderService.donation4Close(c.getCrowdfundingId());

        return true;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }

}
