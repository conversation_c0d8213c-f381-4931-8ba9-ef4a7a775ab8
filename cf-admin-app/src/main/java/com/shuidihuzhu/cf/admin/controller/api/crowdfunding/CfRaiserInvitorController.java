package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.growthtool.client.CfInviteCaseFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.*;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @author: fengxuan
 * @create 2020-03-12 12:49
 **/
@RestController
@Slf4j
@RequestMapping("/admin/cf/invite")
public class CfRaiserInvitorController {

    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;

    @Autowired
    private CfInviteCaseFeignClient inviteCaseFeignClient;

    @ApiOperation("判断是否是推荐人案例")
    @PostMapping("is-raiser-invitor")
    @RequiresPermission("raiserInvitor:is-raiser-invitor")
    public Response<Boolean> isRaiserInvitor(@RequestParam("infoUuid") String infoUuid) {
        CrowdfundingInfo fundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (fundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        return inviteCaseFeignClient.isRaiserInvitor(fundingInfo.getUserId());
    }


    @ApiOperation("推荐人推荐状态信息")
    @PostMapping("get-raiser-invitor-status")
    @RequiresPermission("raiserInvitor:getStatus")
    public Response<CfRaiserInvitorStatus> getRaiserInvitorStatus(@RequestParam("infoUuid") String infoUuid) {
        CrowdfundingInfo fundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (fundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        return inviteCaseFeignClient.getRaiserInvitorStatus(fundingInfo.getUserId());
    }


    @ApiOperation("推荐人推荐案例信息")
    @PostMapping("list-invited-case")
    @RequiresPermission("raiserInvitor:list-invited-case")
    public Response<PageReturnModel<CfInvitedCaseInfo>> listInvitedCase(@RequestParam("infoUuid") String infoUuid,
                                                                        @RequestParam(name = "current",defaultValue = "1") int current,
                                                                        @RequestParam(name = "pageSize",defaultValue = "10") int pageSize) {
        CrowdfundingInfo fundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (fundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        return inviteCaseFeignClient.listInvitedCase(fundingInfo.getUserId(), current, pageSize);
    }


}
