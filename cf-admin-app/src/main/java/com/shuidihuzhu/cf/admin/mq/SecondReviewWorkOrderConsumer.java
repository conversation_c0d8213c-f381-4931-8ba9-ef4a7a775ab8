package com.shuidihuzhu.cf.admin.mq;


import com.shuidihuzhu.cf.admin.util.lock.RedisDistributedLock;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterialsResult;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditCreateOrderService;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * 二次审核工单
 * 智能审核2.0 wiki:https://wiki.shuiditech.com/pages/viewpage.action?pageId=693928457
 */
@Slf4j
@Service
@RefreshScope
@RocketMQListener(id = MQTagCons.SECOND_REVIEW_WORK_ORDER,
        group = "cf-admin" + MQTagCons.SECOND_REVIEW_WORK_ORDER,
        tags = MQTagCons.SECOND_REVIEW_WORK_ORDER,
        topic = MQTopicCons.CF)
public class SecondReviewWorkOrderConsumer implements MessageListener<CfAiMaterialsResult> {

    @Resource
    private InitialAuditCreateOrderService initialAuditCreateOrderService;
    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfAiMaterialsResult> mqMessage) {
        if (Objects.isNull(mqMessage) || Objects.isNull(mqMessage.getPayload())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        if (StringUtils.isNotEmpty(mqMessage.getMsgId())) {
            String lockName = "SECOND_REVIEW_WORK_ORDER_" + mqMessage.getMsgId();
            String identifier = "";
            try {
                identifier = redissonHandler.tryLock(lockName, 10 * 1000L);
                if (StringUtils.isEmpty(identifier)) {
                    log.info("SecondReviewWorkOrderConsumer tryLock is fail, lockName : {}", lockName);
                    return ConsumeStatus.CONSUME_SUCCESS;
                }
            } catch (InterruptedException e) {
                log.info("SecondReviewWorkOrderConsumer get lock is fail，identifier:{}", identifier);
            }
        }

        int caseId = mqMessage.getPayload().getCaseId();

        if (caseId <= 0) {
            log.info("SecondReviewWorkOrderConsumer caseId is null !!");
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        log.info("SecondReviewWorkOrderConsumer is begin create work order caseId : {}", caseId);
        try {
            initialAuditCreateOrderService.smartSecondReviewWorkOrder(caseId);
        } catch (Exception e) {
            log.info("SecondReviewWorkOrderConsumer is ex", e);
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }

}
