package com.shuidihuzhu.cf.admin.controller.api.innerapi;

import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingCaseLabel;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileSettings;
import com.shuidihuzhu.cf.service.cfOperatingProfile.CfOperatingCaseLabelBiz;
import com.shuidihuzhu.client.cf.admin.client.CfAdminCaseLabelFeignClient;
import com.shuidihuzhu.client.cf.admin.model.CfOperatingCaseLabelDTO;
import com.shuidihuzhu.client.cf.admin.model.OperatingProfileSettingsDTO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Author: wangpeng
 * @Date: 2021/5/14 00:05
 * @Description:
 */
@Slf4j
@RestController
public class CfAdminCaseLabelFeignController implements CfAdminCaseLabelFeignClient {

    @Autowired
    private CfOperatingCaseLabelBiz caseLabelBiz;


    @Override
    public Response<List<CfOperatingCaseLabelDTO>> getCaseLabelByCaseId(int caseId) {
        List<CfOperatingCaseLabel> cfOperatingCaseLabels = caseLabelBiz.selectOperatingCaseLabels(caseId);
        if (CollectionUtils.isEmpty(cfOperatingCaseLabels)) {
            log.info("CfAdminCaseLabelFeignController getCaseLabelByCaseId cfOperatingCaseLabels is empty {}", caseId);
            return NewResponseUtil.makeSuccess(new ArrayList<>());
        }
        List<CfOperatingCaseLabelDTO> list = new ArrayList<>();
        for (CfOperatingCaseLabel cfOperatingCaseLabel : cfOperatingCaseLabels) {
            CfOperatingCaseLabelDTO cfOperatingCaseLabelDTO = new CfOperatingCaseLabelDTO();
            BeanUtils.copyProperties(cfOperatingCaseLabel, cfOperatingCaseLabelDTO);
            CfOperatingProfileSettings cfOperatingProfileSettings = caseLabelBiz.selectById(cfOperatingCaseLabel.getCaseLabelId());
            if (Objects.nonNull(cfOperatingProfileSettings)) {
                cfOperatingCaseLabelDTO.setCaseLabelName(cfOperatingProfileSettings.getContent());
            }

            list.add(cfOperatingCaseLabelDTO);
        }
        return NewResponseUtil.makeSuccess(list);
    }

    @Override
    public Response<OperatingProfileSettingsDTO> getCaseLabelById(int id) {
        CfOperatingProfileSettings settings = caseLabelBiz.selectById(id);
        if (settings == null){
            NewResponseUtil.makeSuccess(null);
        }
        OperatingProfileSettingsDTO dto = new OperatingProfileSettingsDTO();
        BeanUtils.copyProperties(settings,dto);
        return NewResponseUtil.makeSuccess(dto);
    }
}
