package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.admin.util.BeanTrimUtil;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfVerificationSearchBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdFundingVerificationBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfVerificationOperateRecordBiz;
import com.shuidihuzhu.cf.model.crowdfunding.CfVerificationOperateRecordVo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.*;
import com.shuidihuzhu.common.web.constants.CrowdfundingCons;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@RefreshScope
@RestController
@RequestMapping(path = "/admin/crowdfunding/verification")
public class CrowdfundingVerificationQueryController {

    @Autowired
    private AdminCfVerificationSearchBiz adminCfVerificationSearchBiz;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;

    @Autowired
    private AdminCrowdFundingVerificationBiz adminCrowdFundingVerificationBiz;

    @Autowired
    private CfVerificationOperateRecordBiz cfVerificationOperateRecordBiz;

    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;


    @RequiresPermission("approve:search")
    @RequestMapping(path = "query-search", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation("证实查询")
    public Response<Map<String, Object>> approveSearch(String verificationSearch) {
        log.info("CrowdfundingVerificationQueryController verificationList query:{}", verificationSearch);

        VerificationSearchVo verificationSearchVo = null;
        try {
            verificationSearchVo = JSON.parseObject(verificationSearch, VerificationSearchVo.class);
        } catch (Exception e) {
            log.error("解析失败");
        }

        if (Objects.isNull(verificationSearchVo) || (Objects.isNull(verificationSearchVo.getVerifyUserId()) &&
                Objects.isNull(verificationSearchVo.getVerificationContent()) &&
                Objects.isNull(verificationSearchVo.getMobile()) &&
                Objects.isNull(verificationSearchVo.getUserName()) &&
                Objects.isNull(verificationSearchVo.getCaseId()))) {
            return NewResponseUtil.makeSuccess(Maps.newHashMap());
        }

        BeanTrimUtil.trimBean(verificationSearchVo);

        Long verifyUserId = verificationSearchVo.getVerifyUserId();
        String mobile = verificationSearchVo.getMobile();
        Integer caseId = verificationSearchVo.getCaseId();

        if (StringUtils.isNotEmpty(mobile)) {
            UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByMobile(mobile);
            if (Objects.nonNull(userInfoModel)) {
                long userId = userInfoModel.getUserId();
                if (Objects.isNull(verifyUserId)) {
                    verificationSearchVo.setVerifyUserId(userId);
                } else {
                    if (verifyUserId != userId) {
                        return NewResponseUtil.makeFail("证实人用户ID与证实人手机号对应不是同一个人");
                    }
                }
            }
        }

        if (Objects.nonNull(caseId) && caseId > 0) {
            CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
            if (Objects.isNull(crowdfundingInfo)) {
                return NewResponseUtil.makeFail("无对应筹款案例");
            }
            verificationSearchVo.setInfoUuid(crowdfundingInfo.getInfoId());
        }

        return NewResponseUtil.makeSuccess(doApproveSearch(verificationSearchVo));
    }

    private Map<String, Object> doApproveSearch(VerificationSearchVo verificationSearchVo) {
        Integer pageSize = verificationSearchVo.getPageSize();
        if (pageSize == null || pageSize < CrowdfundingCons.MIN_PAGE_SIZE || pageSize > CrowdfundingCons.MAX_PAGE_SIZE) {
            return Maps.newHashMap();
        }

        Map<String, Object> result = Maps.newHashMap();
        List<CrowdFundingVerificationView> crowdFundingVerificationViewList = Lists.newArrayList();

        Pair<Long, List<CrowdFundingVerificationView>> pair = adminCfVerificationSearchBiz.verificationSearchByEs(verificationSearchVo);
        crowdFundingVerificationViewList = pair.getRight();

        Map<String, Object> pageMap = Maps.newHashMap();
        pageMap.put("total", pair.getLeft());
        pageMap.put("current", (verificationSearchVo.getCurrent() == null || verificationSearchVo.getCurrent() < 1) ? 1 : verificationSearchVo.getCurrent());
        pageMap.put("pageSize", pageSize);

        result.put("pagination", pageMap);

        log.info("crowdfundingInfoVoList size:{}", crowdFundingVerificationViewList.size());

        result.put("data", crowdFundingVerificationViewList);
        return result;
    }


    @RequiresPermission("approve:verification-batch-deletion")
    @RequestMapping(path = "verification-batch-deletion", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation("证实批量删除")
    public Response<Integer> verificationBatchDeletion(String verificationIds) {
        log.info("CrowdfundingVerificationQueryController verificationList query:{}", verificationIds);
        if (StringUtils.isEmpty(verificationIds)) {
            return NewResponseUtil.makeFail("证实id为空");
        }

        List<String> verificationIdList = Splitter.on(",").splitToList(verificationIds);
        int userId = ContextUtil.getAdminUserId();
        int res = 0;
        for (String verificationId : verificationIdList) {
            if (StringUtils.isNumeric(verificationId)) {
                int r = adminCrowdFundingVerificationBiz.updateValid(0, Integer.parseInt(verificationId));
                cfVerificationOperateRecordBiz.addCfVerificationOperateRecord(Integer.parseInt(verificationId), "删除证实", userId);
                res += r;
            }
        }

        return NewResponseUtil.makeSuccess(res);
    }

    @RequiresPermission("approve:verification-deletion")
    @RequestMapping(path = "verification-deletion", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation("证实删除")
    public Response<Integer> verificationDeletion(int verificationId) {
        log.info("CrowdfundingVerificationQueryController verificationId query:{}", verificationId);
        if (verificationId <= 0) {
            return NewResponseUtil.makeFail("证实id为空");
        }

        return NewResponseUtil.makeSuccess(adminCrowdFundingVerificationBiz.updateValid(0, verificationId));
    }

    @RequiresPermission("approve:add-verification-operate-record")
    @RequestMapping(path = "add-verification-operate-record", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation("添加证实记录")
    public Response<Integer> addCfVerificationOperateRecord(int verificationId) {
        if (verificationId <= 0) {
            return NewResponseUtil.makeFail("证实id为空");
        }

        int userId = ContextUtil.getAdminUserId();
        return NewResponseUtil.makeSuccess(cfVerificationOperateRecordBiz.addCfVerificationOperateRecord(verificationId, "删除证实", userId));
    }

    @RequiresPermission("approve:get-verification-operate-record")
    @RequestMapping(path = "get-verification-operate-record", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation("查看证实记录")
    public Response<List<CfVerificationOperateRecordVo>> getCfVerificationOperateRecord(int verificationId) {
        if (verificationId <= 0) {
            return NewResponseUtil.makeFail("证实id为空");
        }

        return NewResponseUtil.makeSuccess(cfVerificationOperateRecordBiz.getCfVerificationOperateRecord(verificationId));
    }

    public static void main(String[] args) {
        VerificationSearchVo verificationSearchVo = JSON.parseObject("{\"pageSize\":20,\"current\":1,\"verifyUserId\":\"404293832\"}",VerificationSearchVo.class);
        System.out.println(verificationSearchVo);
    }

}
