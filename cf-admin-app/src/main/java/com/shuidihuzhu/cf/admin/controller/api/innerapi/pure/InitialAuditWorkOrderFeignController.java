package com.shuidihuzhu.cf.admin.controller.api.innerapi.pure;

import com.shuidihuzhu.cf.biz.crowdfunding.CfVolunteerRiskRecordBiz;
import com.shuidihuzhu.cf.client.adminpure.feign.InitialAuditWorkOrderFeignClient;
import com.shuidihuzhu.cf.client.adminpure.model.initial.CfVolunteerRiskRecord;
import com.shuidihuzhu.cf.client.adminpure.model.initial.Initial1v1AssignInfo;
import com.shuidihuzhu.cf.client.adminpure.model.initial.InitialAuditNoWorkRejectCaseParam;
import com.shuidihuzhu.cf.client.adminpure.model.initial.InitialAuditWorkOrderFeignParam;
import com.shuidihuzhu.cf.client.adminpure.model.initial.PatientToVolunteerParam;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.param.InitialAuditCreateOrderParam;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditContentTo1V1Service;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditCreateOrder;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditCreateOrderService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditRiskService;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: wangpeng
 * @Date: 2021/6/25 11:16
 * @Description:
 */
@RestController
public class InitialAuditWorkOrderFeignController implements InitialAuditWorkOrderFeignClient {

    @Autowired
    private InitialAuditCreateOrderService auditCreateOrderService;
    @Autowired
    private InitialAuditContentTo1V1Service initialAuditContentTo1V1Service;
    @Resource
    private CfWorkOrderClient cfWorkOrderClient;
    @Resource
    private InitialAuditRiskService initialAuditRiskService;
    @Resource
    private CfVolunteerRiskRecordBiz cfVolunteerRiskRecordBiz;

    @Override
    public OperationResult<Void> createWorkOrder(InitialAuditWorkOrderFeignParam param) {
        InitialAuditCreateOrderParam orderParam = InitialAuditCreateOrderParam.builder()
                .caseId(param.getCaseId())
                .condition(param.getCondition())
                .first(param.isFirst())
                .to1V1(param.isTo1V1())
                .build();
        auditCreateOrderService.createInitialAuditOrder(orderParam);
        return OperationResult.success(null);
    }

    @Override
    public OperationResult<Void> noWorkRejectCase(InitialAuditNoWorkRejectCaseParam param) {
        initialAuditContentTo1V1Service.rejectCaseToContent1V1(param);
        return OperationResult.success(null);
    }

    @Override
    public OperationResult<List<Long>> getWorkOrder(int caseId) {
        Response<List<WorkOrderVO>> listResponse = cfWorkOrderClient.queryByCaseAndTypes(caseId, List.of(WorkOrderType.highriskshenhe.getType(), WorkOrderType.ai_photo.getType(), WorkOrderType.ai_content.getType(), WorkOrderType.ai_erci.getType()));
        if (Objects.isNull(listResponse) || listResponse.notOk() || CollectionUtils.isEmpty(listResponse.getData())) {
            return OperationResult.success(new ArrayList<>());
        }
        return OperationResult.success(listResponse.getData()
                .stream()
                .map(WorkOrderVO::getWorkOrderId)
                .collect(Collectors.toList()));
    }

    @Override
    public OperationResult<Integer> patientToVolunteer(PatientToVolunteerParam param) {
        return OperationResult.success(initialAuditRiskService.patientToVolunteer(param));
    }

    @Override
    public OperationResult<List<CfVolunteerRiskRecord>> getVolunteerByCaseId(List<Integer> caseIdList) {
        return OperationResult.success(cfVolunteerRiskRecordBiz.getByCaseIdList(caseIdList));
    }

    @Override
    public OperationResult<String> initialClewAssign(Initial1v1AssignInfo param) {

        initialAuditContentTo1V1Service.recordInitialClewAssign(param);

        return OperationResult.success("success");
    }
}
