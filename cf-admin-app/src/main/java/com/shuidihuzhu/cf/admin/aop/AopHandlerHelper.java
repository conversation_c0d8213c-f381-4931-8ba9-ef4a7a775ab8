package com.shuidihuzhu.cf.admin.aop;

import com.shuidihuzhu.cf.admin.controller.api.crowdfunding.FirstApproveController;
import com.shuidihuzhu.cf.admin.controller.api.crowdfunding.SensitiveWordHandleController;
import com.shuidihuzhu.cf.admin.controller.api.workorder.InitialAuditController;
import com.shuidihuzhu.cf.model.crowdfunding.AdminUserComment;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminSensitiveVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminUserCommentVo;
import com.shuidihuzhu.cf.service.resulthandler.DefaultContentShowFactory;
import com.shuidihuzhu.cf.vo.AnchorPageBigInt2VO;
import com.shuidihuzhu.cf.vo.AnchorPageV2VO;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditCaseDetail;
import com.shuidihuzhu.cf.vo.crowdfunding.WorkOrderFirstApprove;
import com.shuidihuzhu.cf.vo.crowdfunding.WorkOrderUgcBaseInfoVo;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;

/**
 * @author: fengxuan
 * @create 2019-09-19 12:28
 **/
@Slf4j
public final class AopHandlerHelper {

    public static AopHandlerHelper INSTANCE = new AopHandlerHelper();

    private AopHandlerHelper() {
    }

    private static HandleByType ugcContentHandler = (Object result) -> {
        List<AdminSensitiveVo>  list = ((AnchorPageBigInt2VO<AdminSensitiveVo>)result).getList();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (AdminSensitiveVo adminSensitiveVo : list) {
            adminSensitiveVo.setContent(DefaultContentShowFactory.handle(adminSensitiveVo.getContent()));
            adminSensitiveVo.setCaseTitle(DefaultContentShowFactory.handle(adminSensitiveVo.getCaseTitle()));
        }
    };

    private static HandleByType ugcCommentContentHandler = (Object result) -> {
        AdminUserCommentVo data = (AdminUserCommentVo) result;
        data.setCaseTitle(DefaultContentShowFactory.handle(data.getCaseTitle()));
        data.setParentContent(DefaultContentShowFactory.handle(data.getParentContent()));

        List<AdminUserComment>  list = data.getComments();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (AdminUserComment adminSensitiveVo : list) {
            adminSensitiveVo.setContent(DefaultContentShowFactory.handle(adminSensitiveVo.getContent()));
        }
    };


    private static  HandleByType initialContentHandler = (Object result) -> {
        List<WorkOrderUgcBaseInfoVo> baseInfoList = (List<WorkOrderUgcBaseInfoVo>)((Map)result).get("data");
        if (CollectionUtils.isEmpty(baseInfoList)) {
            return;
        }
        for (WorkOrderUgcBaseInfoVo baseInfoVo : baseInfoList) {
            baseInfoVo.setContent(DefaultContentShowFactory.handle(baseInfoVo.getContent()));
            baseInfoVo.setTitle(DefaultContentShowFactory.handle(baseInfoVo.getTitle()));
            CrowdfundingInfo crowdfundingInfo = baseInfoVo.getCrowdfundingInfo();
            //UGC风控动态处理中这个信息为空
            if (crowdfundingInfo == null) {
                return;
            }
            crowdfundingInfo.setTitle(DefaultContentShowFactory.handle(crowdfundingInfo.getTitle()));
            crowdfundingInfo.setContent(DefaultContentShowFactory.handle(crowdfundingInfo.getContent()));
        }
    };


    private static HandleByType chushenDetailContentHandler = (Object result) -> {
        InitialAuditCaseDetail.CaseBaseInfo caseBaseInfo = ((InitialAuditCaseDetail) result).getCaseBaseInfo();
        if (caseBaseInfo == null) {
            return;
        }
        caseBaseInfo.setContent(DefaultContentShowFactory.handle(caseBaseInfo.getContent()));
        caseBaseInfo.setTitle(DefaultContentShowFactory.handle(caseBaseInfo.getTitle()));
    };

    private static HandleByType firstApproveContentHandler = (Object result) -> {
        List<WorkOrderFirstApprove> data = (List<WorkOrderFirstApprove>)((Map) result).get("data");
        if (CollectionUtils.isEmpty(data)) {
            return;
        }
        for (WorkOrderFirstApprove firstApprove : data) {
            firstApprove.setTitle(DefaultContentShowFactory.handle(firstApprove.getTitle()));
            firstApprove.setContent(DefaultContentShowFactory.handle(firstApprove.getContent()));
        }
    };


    public enum PathInfoEnum {
        //UGC评论详情
        UGC_COMMENT_CONTENT(SensitiveWordHandleController.class,"getComment", ugcCommentContentHandler),
        //UGC内容处理
        UGC_CONTENT(SensitiveWordHandleController.class,"getMyNewListV2", ugcContentHandler),
        //图文处理
        INITIAL_CONTENT(SensitiveWordHandleController.class,"getMyOnlyBaseInfoList", initialContentHandler),
        //动态处理
        PROGRESS_CONTENT(SensitiveWordHandleController.class,"getMyOnlyProgressList", initialContentHandler),
        //初审详情页
        CHUSHEN_DETAIL_CONTENT(InitialAuditController.class,"queryCaseDetail", chushenDetailContentHandler),
        //前置审核
        FIRST_APPROVE_CONTENT(FirstApproveController.class,"getMyFirstAprroveList", firstApproveContentHandler),
        ;
        private Class className;
        private String methodName;
        private HandleByType handleByType;

        PathInfoEnum(Class className, String methodName, HandleByType handleByType) {
            this.className = className;
            this.methodName = methodName;
            this.handleByType = handleByType;
        }
        public String getMethodName() {
            return methodName;
        }

        public Class getClassName() {
            return className;
        }

        public HandleByType getHandleByType() {
            return handleByType;
        }
    }

    public static PathInfoEnum findByPathName(String methodName, Class className) {
        for (PathInfoEnum value : PathInfoEnum.values()) {
            if (value.getMethodName().equals(methodName) && value.getClassName().equals(className)) {
                return value;
            }
        }
        return null;
    }


    private static void handledResult(Object ret, PathInfoEnum pathInfoEnum) {
        if (ret == null) {
            return;
        }
        pathInfoEnum.getHandleByType().handle(ret);
    }

    /**
     * @param ret controller return
     */
    public static void handleData(Object ret, Method method) {
        if (ret == null) {
            return;
        }
        Object realResult = ((Response) ret).getData();
        AopHandlerHelper.PathInfoEnum pathInfoEnum = findByPathName(method.getName(), method.getDeclaringClass());
        if (pathInfoEnum != null) {
            try {
                handledResult(realResult, pathInfoEnum);
            } catch (Exception e) {
                log.error("handle result error path:{}", method, e);
            }
        } else {
            log.warn("PathInfoEnum not contains method:{},you should check PathInfoEnum settings", method);
        }
    }

    @FunctionalInterface
    interface HandleByType {
        void handle(Object object);
    }

}
