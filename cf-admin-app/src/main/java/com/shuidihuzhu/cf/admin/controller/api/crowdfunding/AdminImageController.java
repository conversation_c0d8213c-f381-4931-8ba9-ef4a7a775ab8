package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.shuidihuzhu.cf.admin.util.UploadUtil;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingAttachmentBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.image.AdminImageHandlerBiz;
import com.shuidihuzhu.cf.client.feign.CfAttachmentFeignClient;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.OssPictureAttrDO;
import com.shuidihuzhu.cf.model.record.CfImageMarkPsRecord;
import com.shuidihuzhu.cf.service.apollo.ApolloService;
import com.shuidihuzhu.cf.service.record.CfImageMarkPsRecordService;
import com.shuidihuzhu.cf.store.enums.CfDomainEnum;
import com.shuidihuzhu.cf.store.model.AnalysisUrl;
import com.shuidihuzhu.cf.store.plugins.CosPlugins;
import com.shuidihuzhu.cf.vo.crowdfunding.AdminAiImageRecognitionVo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@RefreshScope
@RestController
@RequestMapping(path = "admin/cf/image")
public class AdminImageController {

    @Autowired
    private CosPlugins cosPlugins;

    /**
     * 临时授权url开关
     */
    @Value("${open-temporal-url:false}")
    private boolean openTemporalUrl;

    /**
     * 数据万象动态水印开关
     */
    @Value("${open-water-mark:false}")
    private boolean openWaterMark;

    /**
     * 下掉cdn开关
     */
    @Value("${cdn-demotion-switch:false}")
    private boolean cdnDemotionSwitch;

    /**
     * cos主节点北京域名
     */
    @Value("${cos-bucket-domain:cf-images-1254024480.cos.ap-beijing.myqcloud.com}")
    private String cosBucketDomainMaster;

    /**
     * cos备用重庆节点域名
     */
    @Value("${cos-bucket-domain-salve:cf-images-cq-1254024480.cos.ap-chongqing.myqcloud.com}")
    private String cosBucketDomainSalve;

    /**
     * cos主备开关；true:主节点北京；false:备用节点重庆
     */
    @Value("${cos-domain-master-salve-switch:true}")
    private boolean cosBucketDomainSalveMasterSwitch;

    /**
     * 全部替换为image域名开关
     */
    @Value("${replace-image-domain-switch:false}")
    private boolean replaceImageDomainSwitch;

    /**
     * 全部替换为image域名开关
     */
    @Value("${ai-image-recognition-ps-prob:0.0}")
    private double aiImageRecognitionPsProb;

    @Value("${ai-image-recognition-ps-switch:true}")
    private boolean aiImageRecognitionPsSwitch;

    @Resource
    private AdminImageHandlerBiz adminImageHandlerBiz;

    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Resource
    private AdminCrowdfundingAttachmentBiz adminCrowdfundingAttachmentBiz;

    @Resource
    private CfAttachmentFeignClient cfAttachmentFeignClient;

    @Resource
    private CfImageMarkPsRecordService cfImageMarkPsRecordService;

    @Autowired
    private UploadUtil uploadUtil;

    @Autowired
    private ApolloService apolloService;

    /**
     * 当前sea后台大部分的图片展示都会走这个接口，用这个接口返回的图片地址展示给用户
     *
     * @param urlList 图片地址集合
     * @return
     */
    @RequestMapping(path = "water-mark")
    public Response<List<String>> imgWaterMark(@RequestParam(value = "urlList") List<String> urlList) {
        if (CollectionUtils.isEmpty(urlList)) {
            return NewResponseUtil.makeSuccess(urlList);
        }
        // 域名替换为image域名开发
        if (replaceImageDomainSwitch) {
            urlList = urlList.stream()
                    .map(url -> {
                        AnalysisUrl analysisUrl = AnalysisUrl.parse(url);
                        if (CfDomainEnum.COS_IMAGES.getDomain().equals(analysisUrl.getHost()) || CfDomainEnum.OSS_OSS.getDomain().equals(analysisUrl.getHost())) {
                            analysisUrl.setHost(CfDomainEnum.COS_IMAGE.getDomain());
                        }
                        return analysisUrl.toUrlString();
                    })
                    .collect(Collectors.toList());
        }
        // 动态水印开关
        if (openWaterMark) {
            int userId = ContextUtil.getAdminUserId();
            List<String> stringList = urlList.stream()
                    .map(url -> adminImageHandlerBiz.imageHandlerWatermark(url, userId))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(stringList)) {
                return NewResponseUtil.makeSuccess(stringList);
            } else {
                return NewResponseUtil.makeSuccess(urlList);
            }
        }

        // cdn降级方案，只有水印开关关闭的时候才能走这逻辑
        if (cdnDemotionSwitch) {
            String cosDomain = cosBucketDomainSalveMasterSwitch ? cosBucketDomainMaster : cosBucketDomainSalve;
            urlList = urlList.stream()
                    .map(url -> {
                        AnalysisUrl analysisUrl = AnalysisUrl.parse(url);
                        if (CfDomainEnum.COS_IMAGES.getDomain().equals(analysisUrl.getHost()) || CfDomainEnum.COS_IMAGE.getDomain().equals(analysisUrl.getHost()) || CfDomainEnum.OSS_OSS.getDomain().equals(analysisUrl.getHost())) {
                            analysisUrl.setHost(cosDomain);
                        }
                        return analysisUrl.toUrlString();
                    })
                    .collect(Collectors.toList());
        }

        return NewResponseUtil.makeSuccess(urlList);
    }

    @RequestMapping("/get-temporal-urls")
    public Response<List<String>> getTemporalUrl(@RequestParam("urlList") List<String> urlList) {
        if (CollectionUtils.isEmpty(urlList) || !openTemporalUrl) {
            return NewResponseUtil.makeSuccess(urlList);
        }
        List<String> temporalUrlList = urlList.stream().map(url -> {
            final AnalysisUrl analysisUrl = AnalysisUrl.parse(url);
            if (!CfDomainEnum.COS_IMAGES.getDomain().equals(analysisUrl.getHost())){
                analysisUrl.setHost(CfDomainEnum.COS_IMAGES.getDomain());
            }
            if (analysisUrl.getQuery() != null) {
                analysisUrl.setQuery(null);
            }
            return apolloService.getCosSwitch() ? uploadUtil.getTemporalUrlByUrl("cf-image", analysisUrl.toUrlString()) : cosPlugins.getTemporalUrl("cf-image", analysisUrl.toUrlString());
        }).collect(Collectors.toList());
        return NewResponseUtil.makeSuccess(temporalUrlList);
    }

    @PostMapping("/ai-image-recognition")
    public Response<List<AdminAiImageRecognitionVo>> aiImageRecognition(@RequestParam(value = "infoId", required = false, defaultValue = "") String infoId,
                                                                        @RequestParam(value = "caseId", required = false, defaultValue = "-1") int caseId,
                                                                        @RequestParam("urlList") List<String> urlList) {
        if (!aiImageRecognitionPsSwitch) {
            return NewResponseUtil.makeSuccess(Collections.emptyList());
        }
        if (StringUtils.isEmpty(infoId) && caseId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (CollectionUtils.isEmpty(urlList)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
        if (Objects.isNull(crowdfundingInfo)) {
            crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfo(infoId);
        }
        if (Objects.isNull(crowdfundingInfo)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<CrowdfundingAttachment> attachmentList = adminCrowdfundingAttachmentBiz.queryAttachment(crowdfundingInfo.getId());
        List<Integer> attachmentIdList = attachmentList.stream()
                .map(CrowdfundingAttachment::getId)
                .collect(Collectors.toList());
        Response<List<OssPictureAttrDO>> listResponse = cfAttachmentFeignClient.getPicAttrsByIds(CollectionUtils.isEmpty(attachmentIdList) ? Collections.singletonList(0) : attachmentIdList);
        List<OssPictureAttrDO> ossPictureAttrDOS = Optional.ofNullable(listResponse)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(Collections.emptyList());
        Map<Integer, OssPictureAttrDO> ossPictureAttrDOMap = ossPictureAttrDOS.stream()
                .collect(Collectors.toMap(OssPictureAttrDO::getAttaId, Function.identity(), (x, y) -> x));
        Map<String, CrowdfundingAttachment> crowdfundingAttachmentMap = attachmentList.stream()
                .peek(m -> m.setUrl(AnalysisUrl.parse(m.getUrl()).getPath()))
                .collect(Collectors.toMap(CrowdfundingAttachment::getUrl, Function.identity(), (x, y) -> x));
        Map<Integer, CfImageMarkPsRecord> imageMarkPsRecordMap = cfImageMarkPsRecordService.getByCaseId(crowdfundingInfo.getId())
                .stream()
                .collect(Collectors.toMap(CfImageMarkPsRecord::getAttachmentId, Function.identity(), (x, y) -> x));
        List<AdminAiImageRecognitionVo> list = new ArrayList<>();
        for (String url : urlList) {
            AnalysisUrl parse = AnalysisUrl.parse(url);
            if (StringUtils.isEmpty(parse.getPath())) {
                continue;
            }
            AdminAiImageRecognitionVo vo = new AdminAiImageRecognitionVo();
            vo.setCaseId(crowdfundingInfo.getId());
            vo.setInfoUuid(crowdfundingInfo.getInfoId());
            vo.setUrl(url);
            CrowdfundingAttachment crowdfundingAttachment = crowdfundingAttachmentMap.get(parse.getPath());

            if (Objects.nonNull(crowdfundingAttachment)) {
                OssPictureAttrDO ossPictureAttrDO = ossPictureAttrDOMap.get(crowdfundingAttachment.getId());
                if (Objects.nonNull(ossPictureAttrDO)) {
                    vo.setPsProb(ossPictureAttrDO.getAiPsProb());
                    vo.setPsSoftware(ossPictureAttrDO.getAiPsSoftware());
                    vo.setNewAiPs(StringUtils.isNotEmpty(ossPictureAttrDO.getNewAiPs()) && "yes".equals(ossPictureAttrDO.getNewAiPs()));
                    if (StringUtils.isNotEmpty(ossPictureAttrDO.getAiPsProb())) {
                        vo.setAiPs(Double.parseDouble(ossPictureAttrDO.getAiPsProb()) > aiImageRecognitionPsProb);
                    }
                }
                CfImageMarkPsRecord cfImageMarkPsRecord = imageMarkPsRecordMap.get(crowdfundingAttachment.getId());
                if (Objects.nonNull(cfImageMarkPsRecord)) {
                    vo.setHumanPs(cfImageMarkPsRecord.getRecognitionPs());
                }
            }

            list.add(vo);
        }
        return NewResponseUtil.makeSuccess(list);
    }


    @PostMapping("/image-recognition-human")
    public Response<Void> imageRecognitionHuman(@RequestParam(value = "infoId", required = false, defaultValue = "") String infoId,
                                                @RequestParam(value = "caseId", required = false, defaultValue = "-1") int caseId,
                                                @RequestParam(value = "recognitionPs") int recognitionPs,
                                                @RequestParam("url") String url) {

        if (StringUtils.isEmpty(infoId) && caseId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (StringUtils.isEmpty(url)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
        if (Objects.isNull(crowdfundingInfo)) {
            crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfo(infoId);
        }
        if (Objects.isNull(crowdfundingInfo)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<CrowdfundingAttachment> attachmentList = adminCrowdfundingAttachmentBiz.queryAttachment(crowdfundingInfo.getId());
        if (CollectionUtils.isEmpty(attachmentList)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        String pathUrl = AnalysisUrl.parse(url).getPath();

        attachmentList = attachmentList.stream()
                .filter(f -> {
                    String path = AnalysisUrl.parse(f.getUrl()).getPath();
                    return StringUtils.equals(path, pathUrl);
                })
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(attachmentList)) {
            return NewResponseUtil.makeFail("未找到对应图片");
        }
        for (CrowdfundingAttachment attachment : attachmentList) {
            CfImageMarkPsRecord imageMarkPsRecord = cfImageMarkPsRecordService.getByAttachmentId(attachment.getId());
            if (Objects.isNull(imageMarkPsRecord)) {
                imageMarkPsRecord = new CfImageMarkPsRecord();
                imageMarkPsRecord.setCaseId(crowdfundingInfo.getId());
                imageMarkPsRecord.setRecognitionPs(recognitionPs);
                imageMarkPsRecord.setAttachmentId(attachment.getId());
                imageMarkPsRecord.setOperatorId(ContextUtil.getAdminLongUserId());
                cfImageMarkPsRecordService.insertCfImageMarkPsRecord(imageMarkPsRecord);
            } else {
                cfImageMarkPsRecordService.updateRecognitionPsById(imageMarkPsRecord.getId(), recognitionPs, ContextUtil.getAdminLongUserId());
            }
        }

        return NewResponseUtil.makeSuccess();
    }
}
