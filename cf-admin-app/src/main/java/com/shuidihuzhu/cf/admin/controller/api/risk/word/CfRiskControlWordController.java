package com.shuidihuzhu.cf.admin.controller.api.risk.word;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.client.ugc.model.constant.RiskControlWordTypeConsts;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskControlWordVO;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordSaveResult;
import com.shuidihuzhu.cf.client.ugc.service.RiskControlWordManageClient;
import com.shuidihuzhu.cf.client.ugc.model.domain.risk.RiskControlWordDO;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.client.base.page.v1.model.AnchorPageVO;
import com.shuidihuzhu.cf.enums.CfErrorCode;

import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.shuidihuzhu.common.web.util.admin.PageUtil.MAX_PAGE_SIZE;

/**
 * <AUTHOR>
 * @date 2018-08-17  14:12
 * https://wiki.shuiditech.com/pages/viewpage.action?pageId=48988297
 */
@Slf4j
@Api("风控词汇管理")
@RequestMapping("admin/cf/risk-control/word")
@RestController
public class CfRiskControlWordController {

    private static final String DEFAULT_PAGE_SIZE = "20";

    @Autowired
    private RiskControlWordManageClient riskControlWordManageClient;

    @RequiresPermission("risk-control:word-search")
    @ApiOperation("搜索词库")
    @PostMapping("search-by-condition")
    public Response<AnchorPageVO<RiskControlWordVO>> searchByCondition(@ApiParam("类型: {1: 禁止词, 2: 敏感词}")
                                      @RequestParam(required = false, defaultValue = "0") int type,
                                                                       @ApiParam("分类id")
                                      @RequestParam(required = false, defaultValue = "0") long categoryId,
                                                                       @ApiParam("关键字")
                                      @RequestParam(required = false) String key,
                                                                       @RequestParam(required = false, defaultValue = "0") long current,
                                                                       @RequestParam(defaultValue = "false") boolean isPre,
                                                                       @RequestParam(required = false, defaultValue = DEFAULT_PAGE_SIZE) Integer pageSize,
                                                                       @RequestParam(required = false, defaultValue = "0") int useScene) {
        if (pageSize > MAX_PAGE_SIZE) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        Response<AnchorPageVO<RiskControlWordDO>> r = riskControlWordManageClient.searchByType(type, categoryId, key, isPre, current, pageSize, useScene);
        if (r.notOk()) {
            log.error("searchByType fegin接口返回失败:{}", JSON.toJSONString(r));
            return NewResponseUtil.makeError(CfErrorCode.RPC_FAIL);
        }

        AnchorPageVO<RiskControlWordDO> res = r.getData();
        List<RiskControlWordDO> list = res.getList();
        List<RiskControlWordVO> voList = trans2ModelList(list);

        return NewResponseUtil.makeSuccess(new AnchorPageVO<>(res.getPreAnchor(), res.getNextAnchor(), res.isHasNext(), voList));
    }

    @RequiresPermission("risk-control:word-remove")
    @ApiOperation("删除")
    @PostMapping("remove")
    public Response<Void> remove(@RequestParam long id) {
        return riskControlWordManageClient.removeById(id);
    }

    @RequiresPermission("risk-control:word-add")
    @ApiOperation("添加 支持批量添加(','分割单词)")
    @PostMapping("add")
    public Response<RiskWordSaveResult> addAssemble(@RequestParam long categoryId,
                                                    @RequestParam String content) {

        return riskControlWordManageClient.addToCategoryAssembleV1(categoryId, content, ContextUtil.getAdminUserId());
    }

    @RequiresPermission("risk-control:word-add")
    @ApiOperation("添加 支持批量添加(','分割单词)")
    @PostMapping("add-v1")
    public Response<RiskWordSaveResult> addAssemble(@RequestParam("categoryIds") List<Long> categoryIds,
                                                    @RequestParam String content) {

        return riskControlWordManageClient.addToCategoryAssembleV2(categoryIds, content, ContextUtil.getAdminUserId());
    }


    @PostMapping("get-use-sence")
    public Response<List<String>> getUseSence(@RequestParam long categoryId){
        Response<List<String>> scene = riskControlWordManageClient.selectUseScene(categoryId);
        if (scene.notOk() || scene.getData() ==null){
            return NewResponseUtil.makeSuccess(null);
        }
        return NewResponseUtil.makeSuccess(scene.getData());
    }

    @NotNull
    private List<RiskControlWordVO> trans2ModelList(List<RiskControlWordDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(this::trans2Model).collect(Collectors.toList());
    }

    private RiskControlWordVO trans2Model(RiskControlWordDO d) {
        RiskControlWordVO v = new RiskControlWordVO();
        BeanUtils.copyProperties(d, v);
        v.setTypeName(getTypeName(v.getWordType()));
        return v;
    }

    private String getTypeName(int wordType) {
        if (wordType == RiskControlWordTypeConsts.PROHIBITION) {
            return "禁止词";
        }
        if (wordType == RiskControlWordTypeConsts.SENSITIVE) {
            return "敏感词";
        }

        if (wordType == RiskControlWordTypeConsts.KEYWORD) {
            return "关键词";
        }

        return "默认";
    }
}
