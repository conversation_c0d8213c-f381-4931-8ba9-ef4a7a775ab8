package com.shuidihuzhu.cf.admin.mq.casestatus;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.admin.mq.common.BaseMessageConsumer;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingOperationBiz;
import com.shuidihuzhu.cf.client.material.model.mq.MqTagsConstant;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderTypeFeignClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.ClassifyTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OneTypeEnum;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.model.event.WorkOrderResultChangeEvent;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @DATE 2020/2/20
 */

@Service
@Slf4j
@RocketMQListener(id ="Credit_" + CfClientMQTagCons.WORK_ORDER_STATUS_CHANGE,
        tags = CfClientMQTagCons.WORK_ORDER_STATUS_CHANGE,
        group = CfClientMQTagCons.WORK_ORDER_STATUS_CHANGE+"_credit_group",
        topic = MQTopicCons.CF)
public class updateCreditStatusConsumer extends BaseMessageConsumer<WorkOrderResultChangeEvent>
        implements MessageListener<WorkOrderResultChangeEvent> {

    @Autowired
    private CfWorkOrderClient workOrderClient;

    @Autowired
    private AdminCrowdfundingOperationBiz adminCrowdfundingOperationBiz;

    @Autowired
    private CfWorkOrderTypeFeignClient cfWorkOrderTypeFeignClient;


    @Override
    protected boolean handle(ConsumerMessage<WorkOrderResultChangeEvent> consumerMessage) {

        WorkOrderResultChangeEvent event = consumerMessage.getPayload();

        List<Integer> list = Optional.ofNullable(cfWorkOrderTypeFeignClient.getByOneLevel(OneTypeEnum.zengxin.getType()))
                .filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList());

        if (!list.contains(event.getOrderType())){
            return true;
        }

        if (event.getHandleResult() != HandleResultEnum.manual_lock.getType()){
            return true;
        }

        Response<WorkOrderVO> response = workOrderClient.getWorkOrderById(event.getWorkOrderId());

        WorkOrderVO workOrderVO = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);

        if (workOrderVO == null){
            log.info("workorderid={} workOrderVO=null",event.getWorkOrderId());
            return true;
        }

        int caseId = workOrderVO.getCaseId();
        //1代表是手动关闭
        adminCrowdfundingOperationBiz.updateCreditStatus(caseId,1);

        return true;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
