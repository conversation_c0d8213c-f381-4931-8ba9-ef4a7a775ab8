package com.shuidihuzhu.cf.admin.controller.api.experiment;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.model.label.CfCaseLabelInfo;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.cf.growthtool.client.CfExperimentFeignClient;
import com.shuidihuzhu.client.model.ExperimentHitResult;
import com.shuidihuzhu.client.model.ExperimentParam;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Description: 判断场景实验结果
 * @Author: panghairui
 * @Date: 2024/8/1 3:20 PM
 */
@Api("实验判断")
@RestController
@RequestMapping("/admin/cf/experiment")
public class CfExperimentController {

    @Resource
    private CfExperimentFeignClient cfExperimentFeignClient;

    @ApiOperation("根据场景判断实验命中状态")
    @PostMapping("scene-judge-experiment")
    public Response<ExperimentHitResult> sceneJudgeExperiment(@ApiParam("判断实验参数") @RequestBody ExperimentParam experimentParam) {
        return cfExperimentFeignClient.sceneJudgeExperiment(experimentParam);
    }

}
