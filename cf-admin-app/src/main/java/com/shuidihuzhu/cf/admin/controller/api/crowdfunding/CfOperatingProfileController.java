package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;


import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingCaseLabel;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileLog;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileResult;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileSettings;
import com.shuidihuzhu.cf.service.cfOperatingProfile.CfOperatingCaseLabelBiz;
import com.shuidihuzhu.cf.service.cfOperatingProfile.CfOperatingProfileLogBiz;
import com.shuidihuzhu.cf.service.cfOperatingProfile.CfOperatingProfileSettingsBiz;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RequestMapping(path = "/admin/crowdfunding/operating/settings")
@RestController
public class CfOperatingProfileController {

    @Autowired
    private CfOperatingProfileLogBiz profileLogBiz;
    @Autowired
    private CfOperatingProfileSettingsBiz profileSettingsBiz;
    @Autowired
    private CfOperatingCaseLabelBiz caseLabelBiz;

    // 列表查询
    @PostMapping("/select/all/by-profile-type")
    @RequiresPermission("operatingProfile:selectByType")
    public Response<List<CfOperatingProfileResult>> queryAllSettingsByType(@RequestParam  int profileType) {

        return NewResponseUtil.makeSuccess(profileSettingsBiz.queryAllSettingsByType(profileType));
    }

    @PostMapping("/select/sub-profile/by-data-status-profile-type")
    @RequiresPermission("operatingProfile:select-subProfile")
    public Response<List<CfOperatingProfileSettings>> querySettingsByTypeAndDataStatus(@RequestParam int parentId,
            @RequestParam int dataStatus, @RequestParam int profileType) {

        return NewResponseUtil.makeSuccess(profileSettingsBiz.querySettingsByTypeAndDataStatus(parentId, dataStatus, profileType));
    }

    @PostMapping("/select/operate-logs")
    @RequiresPermission("operatingProfile:select-operate-logs")
    public Response<List<CfOperatingProfileLog.CfOperatingProfileLogView>> queryOperateLogs(@RequestParam int id,
                                                                                            @RequestParam int profileType) {

        return NewResponseUtil.makeSuccess(profileLogBiz.selectLogByLogType(id));
    }

    @PostMapping("/up-or-down")
    @RequiresPermission("operatingProfile:up-or-down")
    public Response<String> upOrDown(@RequestParam int operateType, @RequestParam int downId, @RequestParam int upId) {

        AdminErrorCode errorCode = profileSettingsBiz.changeRank(ContextUtil.getAdminUserId(), upId, downId, operateType);
        return errorCode == AdminErrorCode.SUCCESS ? NewResponseUtil.makeSuccess("") : NewResponseUtil.makeError(errorCode);
    }


    @PostMapping("/operate/profile/data-status")
    @RequiresPermission("operatingProfile:getDataStatus")
    public Response<String> operateDataStatus(@RequestParam int id, @RequestParam int operateType) {

        AdminErrorCode errorCode = profileSettingsBiz.changeDataStatus(ContextUtil.getAdminUserId(), id, operateType);
        return errorCode == AdminErrorCode.SUCCESS ? NewResponseUtil.makeSuccess("") : NewResponseUtil.makeError(errorCode);
    }

    @PostMapping("/add/profile/by-type")
    @Deprecated
    @RequiresPermission("operatingProfile:addByType")
    public Response<String> createLabelSetting(@RequestParam int parentId, @RequestParam String content,
                                               @RequestParam int profileType)  {

        AdminErrorCode errorCode = profileSettingsBiz.createProfileSetting(ContextUtil.getAdminUserId(), parentId,
                content, profileType, null);
        return errorCode == AdminErrorCode.SUCCESS ? NewResponseUtil.makeSuccess("") : NewResponseUtil.makeError(errorCode);
    }

    @PostMapping("/add/profile/by-type-v1")
    @RequiresPermission("operatingProfile:addByTypeV1")
    public Response<String> createLabelSettingV1(@RequestBody CfOperatingProfileLog.AddTagsParam param)  {

        if (param == null) {
            return NewResponseUtil.makeError(AdminErrorCode.INPUT_FORMAT_ERROR);
        }
        AdminErrorCode errorCode = profileSettingsBiz.createProfileSetting(ContextUtil.getAdminUserId(), param.getParentId(),
                param.getContent(), param.getProfileType(), param.getPropertyList());
        return errorCode == AdminErrorCode.SUCCESS ? NewResponseUtil.makeSuccess("") : NewResponseUtil.makeError(errorCode);
    }

    @PostMapping("/add-or-update/case/labels")
    @RequiresPermission("operatingProfile:addOrUp-case-lables")
    public Response<String> addOrUpdateCaseLabels(@RequestParam String param) {

        CfOperatingCaseLabel.QueryParam queryParam = null;
        try {
            queryParam = JSON.parseObject(param, CfOperatingCaseLabel.QueryParam.class);//已检查过
        } catch (Exception e) {
            log.info("案例标签查询 参数解析错误 param:{}", param, e);
        }
        queryParam.setUserId(ContextUtil.getAdminUserId());

        AdminErrorCode errorCode = caseLabelBiz.addCaseLabels(queryParam);
        return errorCode == AdminErrorCode.SUCCESS ? NewResponseUtil.makeSuccess("") : NewResponseUtil.makeError(errorCode);
    }


    @PostMapping("/select/case-labels/by-case-id")
    @RequiresPermission("operatingProfile:select-case-lables")
    public Response<List<CfOperatingProfileSettings>> selectCaseLabels(@RequestParam int caseId) {
        return  NewResponseUtil.makeSuccess(caseLabelBiz.selectCaseLabels(caseId));
    }

    @PostMapping("/select/auto-system/by-type")
    @RequiresPermission("operatingProfile:select-auto-system")
    public Response<List<CfOperatingProfileSettings.ReportProblemSettingsResult>> selectSystemHandle(@RequestParam int type) {
        return  NewResponseUtil.makeSuccess(profileSettingsBiz.selectReportSettingsByType(type));
    }

    @PostMapping("/update/profile/auto-system")
    @RequiresPermission("operatingProfile:update-auto-system")
    public Response<String> updateExtMappings(@RequestParam String param) {
        CfOperatingProfileSettings.ProfileProblemSettings settings = null;

        try {
            settings = JSON.parseObject(param, CfOperatingProfileSettings.ProfileProblemSettings.class);//已检查过
        } catch (Exception e) {
            log.info("更新标签传入参数错误。param:{}", param, e);
            return NewResponseUtil.makeError(AdminErrorCode.INPUT_FORMAT_ERROR);
        }
        settings.setUserId(ContextUtil.getAdminUserId());
        profileSettingsBiz.updateExtMappings(settings);
        return  NewResponseUtil.makeSuccess("");
    }

}
