package com.shuidihuzhu.cf.admin.controller.api.workorder;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditCreateOrder;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditCreateOrderService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditTargetAmountReasonableService;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderListParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Author: wangpeng
 * @Date: 2023/1/16 11:42
 * @Description:
 */
@Api("目标合理性审核工单")
@Slf4j
@RestController
@RequestMapping(path = "/admin/workorder/target/amount/reasonable")
public class TargetAmountReasonableController {

    @Resource
    private InitialAuditTargetAmountReasonableService initialAuditTargetAmountReasonableService;
    @Resource
    private InitialAuditCreateOrderService initialAuditCreateOrderService;

    @RequiresPermission("targetAmountReasonable:order-list")
    @ApiOperation("任务处理中心列表")
    @PostMapping("order-list")
    public Response<PageResult<WorkOrderVO>> orderList(@RequestParam("param") String param) {
        WorkOrderListParam workOrderListParam = JSON.parseObject(param, WorkOrderListParam.class);
        if (Objects.isNull(workOrderListParam)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        return initialAuditTargetAmountReasonableService.getOrderList(workOrderListParam);
    }

    @RequiresPermission("targetAmountReasonable:handle")
    @ApiOperation("任务处理中心列表")
    @PostMapping("handle")
    public Response<Void> handle(@RequestParam("caseId") int caseId,
                                 @RequestParam("workOrderId") long workOrderId,
                                 @RequestParam("handleResult") int handleResult,
                                 @RequestParam("remark") String remark) {
        boolean handle = initialAuditTargetAmountReasonableService.handle(workOrderId, handleResult, remark, ContextUtil.getAdminLongUserId());
        if (!handle) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
        }
        initialAuditCreateOrderService.targetAmountWorkHandleInitialAudit(caseId, workOrderId, handleResult);
        return NewResponseUtil.makeSuccess();
    }
}
