package com.shuidihuzhu.cf.admin.controller.api.report.v2;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.admin.util.lock.RedisDistributedLock;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.report.CfReportActionClassify;
import com.shuidihuzhu.cf.model.report.CfReportClassifyRelationship;
import com.shuidihuzhu.cf.service.crowdfunding.report.CfReportRisponseActionService;
import com.shuidihuzhu.cf.vo.report.CfReportDisposeActionVo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/4/16
 */
@Api("举报处理动作配置")
@RestController
@RequestMapping(path = "/admin/cf/report/v2/dispose")
public class CfReportRisposeActionController {

    @Autowired
    private CfReportRisponseActionService cfReportRisponseActionService;

    @ApiOperation("增加/更新动作分类")
    @PostMapping(path = "action-classify/add-or-update")
    @RequiresPermission("dispose-report:add-or-update")
    public Response<Integer> addOrUpdateActionClassify(@RequestParam(value = "status") int status,
                                                       @RequestParam(value = "actionClassify") String actionClassify,
                                                       @RequestParam(value = "id", defaultValue = "0") long id) {
        return cfReportRisponseActionService.addOrUpdateActionClassify(status, actionClassify, id);
    }


    @ApiOperation("动作分类是否启用")
    @PostMapping(path = "action-classify/update-use-status")
    @RequiresPermission("dispose-report:action-classify/update-use-status")
    public Response<Integer> updateActionClassifyUseStatus(@RequestParam(value = "isUse") int isUse,
                                                           @RequestParam(value = "id") long id) {
        return cfReportRisponseActionService.updateActionClassifyUseStatus(isUse, id);
    }

    @ApiOperation("动作分类列表")
    @PostMapping(path = "action-classify/get-list")
    @RequiresPermission("dispose-report:action-classify/get-list")
    public Response<List<CfReportActionClassify>> getActionClassifyList() {
        return cfReportRisponseActionService.getActionClassifyList();
    }

    @ApiOperation("增加/更新处理动作接口")
    @PostMapping(path = "dispose-action/add-or-update")
    @RequiresPermission("dispose-report:dispose-action/add-or-update")
    public Response addOrUpdateDisposeAction(@RequestParam(value = "actionClassifyId") long actionClassifyId,
                                             @RequestParam(value = "disposeAction") String disposeAction,
                                             @RequestParam(value = "id", defaultValue = "0") long id,
                                             @RequestParam(value = "isHelp", defaultValue = "false") boolean isHelp,
                                             @RequestParam(value = "hasTemplate", defaultValue = "false") boolean hasTemplate,
                                             @RequestParam(value = "title", defaultValue = "") String title,
                                             @RequestParam(value = "content", defaultValue = "") String content,
                                             @RequestParam(value = "commitmentContent", defaultValue = "") String commitmentContent) {
        if (StringUtils.length(title) > 50 || StringUtils.length(content) > 500
                || StringUtils.length(commitmentContent) > 500 || StringUtils.length(disposeAction) > 500) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_TOO_LONG);
        }
        cfReportRisponseActionService.addOrUpdateDisposeAction(actionClassifyId, disposeAction, id, isHelp,
                hasTemplate, title, content, commitmentContent);
        return NewResponseUtil.makeSuccess(null);
    }

    @ApiOperation("处理动作是否启用接口")
    @PostMapping(path = "dispose-action/update-use-status")
    @RequiresPermission("dispose-report:dispose-action/update-use-status")
    public Response<Integer> updateDisposeActionUseStatus(@RequestParam(value = "isUse") int isUse,
                                                          @RequestParam(value = "id") long id) {
        return cfReportRisponseActionService.updateDisposeActionUseStatus(isUse, id);
    }

    @ApiOperation("处理动作列表")
    @PostMapping(path = "dispose-action/get-list")
    @RequiresPermission("dispose-report:dispose-action/update-use-status")
    public Response<List<CfReportDisposeActionVo>> getDisposeActionList(int status, @RequestParam(value = "caseId", defaultValue = "0") int caseId) {
        return cfReportRisponseActionService.getDisposeActionList(status, caseId);
    }


    @ApiOperation("增加/更新标签关联举报")
    @PostMapping(path = "label/add-or-update")
    @RequiresPermission("dispose-report:label/add-or-update")
    public Response<Integer> addOrUpdateLabel(String param, int labelId) {
        return cfReportRisponseActionService.addOrUpdateLabel(param,labelId);
    }

    @ApiOperation("标签对应选中的全部动作分类与处理动作")
    @PostMapping(path = "label/get-selected-info")
    @RequiresPermission("dispose-report:label/get-selected-info")
    public Response<List<CfReportClassifyRelationship>> getSelectedInfo(@RequestParam(value = "labelId") int labelId) {
        return cfReportRisponseActionService.getSelectedInfo(labelId);
    }


    @ApiOperation("案例对应选中的全部动作分类与处理动作")
    @PostMapping(path = "case/get-selected-info")
    @RequiresPermission("dispose-report:case/get-selected-info")
    public Response<List<CfReportDisposeActionVo>> getListByCase(@RequestParam(value = "caseId") int caseId,
                                                                 @RequestParam(value = "type") int type,
                                                                 @RequestParam(value = "labelIds", defaultValue = "")String labelIds) {
        return cfReportRisponseActionService.getListByCase(caseId, type, labelIds);
    }

    @ApiOperation("更新案例处理动作接口")
    @PostMapping(path = "case/update-case-info")
    @RequiresPermission("dispose-report:case/update-case-info")
    @RedisDistributedLock(key = "dispose_report_update-case-info_#{caseId}")
    public Response<Integer> updateCaseInfo(@RequestParam(value = "caseId") int caseId, String param,
                                            @RequestParam(value = "type") int type,
                                            @RequestParam(value = "trustId",defaultValue = "0") long trustId) {
        return cfReportRisponseActionService.updateCaseInfo(caseId, param, type, trustId);
    }


}
