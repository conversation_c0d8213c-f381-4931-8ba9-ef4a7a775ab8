package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.admin.util.AnchorPageUtils;
import com.shuidihuzhu.cf.domain.cf.CfScheduleAlarm;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.service.CfScheduleAlarmDataSourceService;
import com.shuidihuzhu.cf.service.CfScheduleAlarmService;
import com.shuidihuzhu.cf.vo.AnchorPageBigInt2VO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.quartz.CronExpression;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/7/21
 */
@RefreshScope
@Slf4j
@RestController
@RequestMapping(path = "/admin/cf/schedule/alarm")
public class CfScheduleAlarmController {

    @Resource
    private CfScheduleAlarmService cfScheduleAlarmService;

    @Resource
    private CfScheduleAlarmDataSourceService cfScheduleAlarmDataSourceService;

    private AlarmConfig alarmConfig;

    private Map<Long, Set<String>> userIdSignSetMap;

    @PostMapping("/run")
    public Response<Void> run() {
        cfScheduleAlarmService.run(new Date());
        return NewResponseUtil.makeSuccess(null);
    }

    @Value("${apollo.admin-api.alarm-config:{}}")
    public void setAlarmConfig(String alarmConfigJson) {
        alarmConfig = JSON.parseObject(alarmConfigJson, AlarmConfig.class);
        userIdSignSetMap = Maps.newHashMap();
        final Set<AlarmSignRel> userSignRelList = alarmConfig.getUserSignRelList();
        for (AlarmSignRel alarmSignRel : userSignRelList) {
            final Set<Long> userIdList = alarmSignRel.getUserIdList();
            for (Long userId : userIdList) {
                final Set<String> signSet = userIdSignSetMap.computeIfAbsent(userId, k -> Sets.newHashSet());
                signSet.add(alarmSignRel.getSignName());
            }
        }
    }

    @Data
    public static class AlarmConfig {
        private Set<AlarmSignRel> userSignRelList;
    }
    @Data
    public static class AlarmSignRel {
        private String signName;
        private Set<Long> userIdList;
    }

    /**
     * 返回当前用户是否 拒绝此sign权限
     */
    private boolean onRefused(long currentUserId, String sign) {
        final Set<String> signSet = getUserPermissionSet(currentUserId);
        return !signSet.contains(sign);
    }

    private Set<String> getUserPermissionSet(long currentUserId) {
        final Set<String> signSet = userIdSignSetMap.get(currentUserId);
        if (CollectionUtils.isEmpty(signSet)) {
            return Sets.newHashSet(String.valueOf(currentUserId));
        }
        final HashSet<String> tempSet = Sets.newHashSet(signSet);
        tempSet.add(String.valueOf(currentUserId));
        return tempSet;
    }

    @PostMapping("add-or-update")
    @RequiresPermission("schedule-alarm:add-or-update")
    public Response<Void> addOrUpdate(@ApiParam("周期报警信息") @RequestBody CfScheduleAlarm cfScheduleAlarm) {

        if (Objects.isNull(cfScheduleAlarm)) {
           return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        final Long id = cfScheduleAlarm.getId();
        final boolean isNew = id == null || id <= 0;
        if (!isNew) {
            final CfScheduleAlarm alarm = cfScheduleAlarmService.getById(id);
            if (onRefused(ContextUtil.getAdminLongUserId(), alarm.getSign())) {
                return NewResponseUtil.makeFail("您暂无此条目修改权限，请联系管理员。");
            }
        }

        if (Objects.isNull(cfScheduleAlarm.getContentType()) || StringUtils.isAnyBlank(cfScheduleAlarm.getTitle(), cfScheduleAlarm.getCrontab(), cfScheduleAlarm.getExecuteSql())) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        if (StringUtils.isBlank(cfScheduleAlarm.getDescription())) {
            cfScheduleAlarm.setDescription(StringUtils.EMPTY);
        }

        if (StringUtils.isAllBlank(cfScheduleAlarm.getRobotKey(), cfScheduleAlarm.getFeishuRobotKey())) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        cfScheduleAlarm.setFeishuRobotKey(StringUtils.defaultIfBlank(cfScheduleAlarm.getFeishuRobotKey(), ""));
        cfScheduleAlarm.setRobotKey(StringUtils.defaultIfBlank(cfScheduleAlarm.getRobotKey(), ""));

        if (!CronExpression.isValidExpression(cfScheduleAlarm.getCrontab())) {
            return NewResponseUtil.makeError(AdminErrorCode.CRONTAB_ERROR);
        }

        cfScheduleAlarm.setOperatorId((long) ContextUtil.getAdminUserId());

        // 如果是新建 并且未指定权限标签 则根据当前操作人选择一个 当前人拥有的权限插入
        if (isNew && StringUtils.isBlank(cfScheduleAlarm.getSign())) {
            String sign = getSignByUserId(ContextUtil.getAdminLongUserId());
            cfScheduleAlarm.setSign(sign);
        }

        boolean flag = false;
        if (isNew) {
            flag = cfScheduleAlarmService.insert(cfScheduleAlarm);
        }else {
            flag = cfScheduleAlarmService.update(cfScheduleAlarm);
        }

        return flag ? NewResponseUtil.makeSuccess(null) : NewResponseUtil.makeError(AdminErrorCode.SYSTEM_OPERATION_FAILED);
    }

    /**
     * 默认选择一个当前人已有权限权限
     * 排除掉 '' 的权限
     */
    private String getSignByUserId(long adminLongUserId) {
        final Set<String> signSet = getUserPermissionSet(adminLongUserId);
        return signSet.stream().filter(StringUtils::isNotEmpty)
                .findFirst().orElse(String.valueOf(adminLongUserId));
    }

    @PostMapping("select")
    @RequiresPermission("schedule-alarm:select")
    public Response<AnchorPageBigInt2VO<CfScheduleAlarm>> select(@RequestParam(name = "title", required = false) String title,
                                 @RequestParam(name = "desc", required = false) String desc,
                                 @RequestParam(name = "crontab", required = false) String crontab,
                                 @RequestParam(name = "robotKey", required = false) String robotKey,
                                 @RequestParam(name = "contentType", required = false) Integer contentType,
                                 @RequestParam(name = "dataSource", required = false) String dataSource,
                                 @RequestParam(name = "pageSize", required = false, defaultValue = "10") int pageSize,
                                 @RequestParam(name = "isPre", required = false, defaultValue = "false") boolean isPre,
                                 @RequestParam(name = "anchor", required = false, defaultValue = "0") long anchor,
                                 @RequestParam(name = "feishuRobotKey", required = false) String feishuRobotKey) {

        final long currentOperatorId = ContextUtil.getAdminLongUserId();
        final Set<String> signSet = getUserPermissionSet(currentOperatorId);
        AnchorPageBigInt2VO<CfScheduleAlarm> result = AnchorPageUtils.list(
                pageSize,
                isPre,
                realSize -> cfScheduleAlarmService.selectByAnchor(title, desc, crontab, robotKey, contentType, dataSource, realSize, isPre, anchor, feishuRobotKey, signSet),
                CfScheduleAlarm::getId
        );

        return NewResponseUtil.makeSuccess(result);
    }

    @PostMapping("delete")
    @RequiresPermission("schedule-alarm:delete")
    public Response<Void> delete(@RequestParam(name = "id") long id) {
        final CfScheduleAlarm alarm = cfScheduleAlarmService.getById(id);
        if (onRefused(ContextUtil.getAdminLongUserId(), alarm.getSign())) {
            return NewResponseUtil.makeFail("您暂无此条目修改权限，请联系管理员。");
        }

        boolean flag = cfScheduleAlarmService.delete(id, ContextUtil.getAdminUserId());
        return flag ? NewResponseUtil.makeSuccess(null) : NewResponseUtil.makeError(AdminErrorCode.SYSTEM_OPERATION_FAILED);
    }

    @PostMapping("trigger")
    public Response<Void> trigger(@RequestParam(name = "id") long id) {
        cfScheduleAlarmService.trigger(id);
        return NewResponseUtil.makeSuccess(null);
    }

    @PostMapping("get-datasource")
    @RequiresPermission("schedule-alarm:get-datasource")
    public Response<Map<String, String>> getDataSource() {
        return NewResponseUtil.makeSuccess(cfScheduleAlarmDataSourceService.getDataSource());
    }

    @PostMapping("init-datasource")
    @RequiresPermission("schedule-alarm:init-datasource")
    public Response<Void> initDataSource() {
        cfScheduleAlarmDataSourceService.initDataSources();
        return NewResponseUtil.makeSuccess(null);
    }

    @PostMapping("add-datasource")
    @RequiresPermission("schedule-alarm:add-datasource")
    public Response<Boolean> initDataSource(@RequestParam(name = "dataSourceName", required = false) String dataSourceName,
                                         @RequestParam(name = "configProperty", required = false) String configProperty) {
        if (StringUtils.isAnyBlank(dataSourceName, configProperty)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        long userId = ContextUtil.getAdminUserId();
        cfScheduleAlarmDataSourceService.add(userId, dataSourceName, configProperty);
        return NewResponseUtil.makeSuccess(false);
    }
}
