package com.shuidihuzhu.cf.admin.mq.record;

import com.shuidihuzhu.account.model.service.MobileUserIdModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CfQuestionnaireBiz;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.model.admin.CfQuestionnaire;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.growthtool.model.PreposeMaterialCommitNoticeModel;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.grpc.account.v1.feign.SimpleUserAccountServiceClient;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 接受前置报备消息
 * <AUTHOR>
 * @DATE 2019/12/11
 */
@Service
@RocketMQListener(id = CfClientMQTagCons.FIRST_COMMIT_PREPOSE_MATERIAL_MSG,
        group = "cf-admin" + CfClientMQTagCons.FIRST_COMMIT_PREPOSE_MATERIAL_MSG,
        tags = CfClientMQTagCons.FIRST_COMMIT_PREPOSE_MATERIAL_MSG,
        topic = MQTopicCons.CF)
@Slf4j
public class RecordSubmitConsumer implements MessageListener<PreposeMaterialCommitNoticeModel> {


    @Autowired(required = false)
    private Producer producer;

    @Autowired
    private CfQuestionnaireBiz cfQuestionnaireBiz;

    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;


    @Autowired
    private SimpleUserAccountServiceClient simpleUserAccountServiceClient;


    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<PreposeMaterialCommitNoticeModel> mqMessage) {


        PreposeMaterialCommitNoticeModel model = mqMessage.getPayload();

        if (model == null){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        //前置报备调查问卷线下筹款顾问渠道暂停发送
        if (PreposeMaterialCommitNoticeModel.ServiceChannelEnum.XIANXIA.getServiceChannelName().equals(model.getServiceChannel()) ){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        String card = cfQuestionnaireBiz.getByCard(oldShuidiCipher.aesEncrypt(model.getPatientIdCard()));

        //患者证件重复  不发送
        if (StringUtils.isNotEmpty(card)){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        long userId = 0;
        MobileUserIdModel response = simpleUserAccountServiceClient.getUserIdByMobile(shuidiCipher.decrypt(model.getUserEncryptPhone()));
        log.info("RecordSubmitConsumer mobile={} response={}",model.getUserEncryptPhone(),response);
        if (response != null){
            userId = response.getUserId();
        }

        //保存信息
        long id = save(model,userId);

        //发送24小时延迟消息
        send24MQ(id);

        return ConsumeStatus.CONSUME_SUCCESS;
    }


    private long save(PreposeMaterialCommitNoticeModel model,long userId){

        CfQuestionnaire q = new CfQuestionnaire();

        q.setRecordId(model.getPreposeMaterialId());
        q.setSource(model.getSourceName());
        q.setChannel(model.getServiceChannel());
        q.setSendTime(new Date());
        q.setCaseId(Optional.ofNullable(model.getInfoId()).map(Long::intValue).orElse(0));
        q.setOrg(Optional.ofNullable(model.getOrgName()).orElse("-"));
        q.setName(model.getSubmitter());
        q.setMobile(model.getUserEncryptPhone());
        q.setCard(oldShuidiCipher.aesEncrypt(model.getPatientIdCard()));
        q.setUserId(userId);
        q.setQid(cfQuestionnaireBiz.getQid());

        cfQuestionnaireBiz.save(q);

        return q.getId();
    }

    private void send24MQ(long id){

        Message message = Message.ofDelay(MQTopicCons.CF,
                MQTagCons.record_later_check, MQTagCons.record_later_check+"_"+id,id,24, TimeUnit.HOURS);

        producer.send(message);

    }
}
