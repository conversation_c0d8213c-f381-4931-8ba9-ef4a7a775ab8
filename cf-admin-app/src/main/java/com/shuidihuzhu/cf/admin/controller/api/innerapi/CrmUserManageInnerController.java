package com.shuidihuzhu.cf.admin.controller.api.innerapi;

import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.biz.crmUserManage.CrmUserManageBiz;
import com.shuidihuzhu.client.cf.admin.client.CrmUserManageClient;
import com.shuidihuzhu.client.cf.admin.model.CrmUserManage;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.Set;

@RestController
public class CrmUserManageInnerController implements CrmUserManageClient {

    @Autowired
    private CrmUserManageBiz crmUserManageBiz;

    @Override
    public Response<CrmUserManage.UserMapping> bindUserMapping(String mobile) {
        return NewResponseUtil.makeSuccess(crmUserManageBiz.bindUserMapping(mobile));
    }

    @Override
    public Response<Map<String, CrmUserManage.UserMapping>> batchBindUserMapping(Set<String> mobiles) {
        return NewResponseUtil.makeSuccess(crmUserManageBiz.batchBindUserMapping(mobiles));
    }

    @Override
    public Response<Map<String, CrmUserManage.UserMapping>> buildMappingAtChangeMobile(String oldMobile, String newRaiseMobile) {
        return NewResponseUtil.makeSuccess(crmUserManageBiz.mergeUserMapping(oldMobile, newRaiseMobile));
    }

    @Override
    public Response<Map<String, CrmUserManage.UserMapping>> selectUserMappings(Set<String> mobiles) {
        return NewResponseUtil.makeSuccess(crmUserManageBiz.selectUserMappings(mobiles));
    }

    @Override
    public Response<String> noticeClewCaseRaise(CrmUserManage.ClewCaseRaiseInfo raiseInfo) {
        crmUserManageBiz.noticeClewCaseRaise(raiseInfo);
        return NewResponseUtil.makeSuccess("");
    }
}
