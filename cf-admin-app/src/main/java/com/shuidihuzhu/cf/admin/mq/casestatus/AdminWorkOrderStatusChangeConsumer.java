package com.shuidihuzhu.cf.admin.mq.casestatus;

import com.shuidihuzhu.cf.admin.mq.common.BaseMessageConsumer;
import com.shuidihuzhu.cf.client.adminpure.constants.ReportCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.service.approve.ApproveControlService;
import com.shuidihuzhu.cf.service.report.ReportOperationService;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.model.event.WorkOrderResultChangeEvent;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 工单回收事件
 * <AUTHOR>
 */
@Service
@Slf4j
@RocketMQListener(id = CfClientMQTagCons.WORK_ORDER_STATUS_CHANGE,
        tags = CfClientMQTagCons.WORK_ORDER_STATUS_CHANGE,
        topic = MQTopicCons.CF)
public class AdminWorkOrderStatusChangeConsumer extends BaseMessageConsumer<WorkOrderResultChangeEvent>
        implements MessageListener<WorkOrderResultChangeEvent> {

    @Autowired
    private ApproveControlService approveControlService;

    @Resource
    private CfWorkOrderClient cfWorkOrderClient;

    @Override
    protected boolean handle(ConsumerMessage<WorkOrderResultChangeEvent> consumerMessage) {
        WorkOrderResultChangeEvent e = consumerMessage.getPayload();
        long workOrderId = e.getWorkOrderId();
        int handleResult = e.getHandleResult();

        Response<WorkOrderVO> r = cfWorkOrderClient.getWorkOrderById(workOrderId);
        if (r.notOk()) {
            return false;
        }
        WorkOrderVO workOrderVO = r.getData();

        // 工单分配
        if (handleResult == HandleResultEnum.doing.getType()) {
            approveControlService.onWorkOrderAssign(workOrderVO);

        }

        // 工单处理完成 释放锁
        if (!HandleResultEnum.unDoResult().contains(handleResult)) {
            approveControlService.releaseControlByWorkOrder(workOrderVO);
        }

        return true;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
