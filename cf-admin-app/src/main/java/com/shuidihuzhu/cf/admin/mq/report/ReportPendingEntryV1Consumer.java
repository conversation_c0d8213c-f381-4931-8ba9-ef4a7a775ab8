package com.shuidihuzhu.cf.admin.mq.report;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.IAdminCredibleInfoService;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.delegate.SeaAccountDelegate;
import com.shuidihuzhu.cf.enhancer.mq.BaseMessageConsumer;
import com.shuidihuzhu.cf.enums.crowdfunding.AddTrustAuditStatusEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CredibleTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfCredibleInfoDO;
import com.shuidihuzhu.cf.model.report.schedule.ReportPendingEntryPayload;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.cf.service.stream.StreamBizService;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderStaffClient;
import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.StaffStatusEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@RocketMQListener(id = MQTagCons.REPORT_SCHEDULE_PUSH_V2,
        group = "cf-admin-" + MQTagCons.REPORT_SCHEDULE_PUSH_V2,
        tags = MQTagCons.REPORT_SCHEDULE_PUSH_V2,
        topic = MQTopicCons.CF)
@Slf4j
public class ReportPendingEntryV1Consumer extends BaseMessageConsumer<ReportPendingEntryPayload> implements MessageListener<ReportPendingEntryPayload> {

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @Autowired
    private SeaAccountDelegate seaAccountDelegate;

    @Autowired
    private ApplicationService applicationService;

    @Autowired
    private IAdminCredibleInfoService credibleInfoService;

    @Autowired
    private StreamBizService streamService;

    @Autowired
    private CfWorkOrderStaffClient staffClient;

    @Autowired(required = false)
    private Producer producer;

    @Override
    protected boolean handle(ConsumerMessage<ReportPendingEntryPayload> consumerMessage) {
        log.info("ReportPendingEntryV1Consumer consumerMessage:{}", JSON.toJSONString(consumerMessage));
        if (Objects.isNull(consumerMessage) || Objects.isNull(consumerMessage.getPayload())) {
            return true;
        }
        ReportPendingEntryPayload reportPendingEntryPayload = consumerMessage.getPayload();

        long subId = reportPendingEntryPayload.getSubId();
        int credibleType = reportPendingEntryPayload.getCredibleType();
        String time = reportPendingEntryPayload.getTime();

        CfCredibleInfoDO cfCredibleInfoDO = credibleInfoService.queryBySubId(subId, credibleType);
        if (Objects.isNull(cfCredibleInfoDO)) {
            return true;
        }

        int caseId = Objects.isNull(cfCredibleInfoDO.getCaseId()) ? 0 : cfCredibleInfoDO.getCaseId();
        int auditStatus = Objects.isNull(cfCredibleInfoDO.getAuditStatus()) ? 0 : cfCredibleInfoDO.getAuditStatus();

        if(auditStatus == AddTrustAuditStatusEnum.PASSED.getCode()){
            return true;
        }

        Response<WorkOrderVO> response = cfWorkOrderClient.getLastWorkOrderByTypes(caseId, WorkOrderType.REPORT_TYPES);
        WorkOrderVO workOrderVO = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(null);
        if (Objects.isNull(workOrderVO)) {
            return true;
        }

        int orderType = workOrderVO.getOrderType();
        long operatorId = workOrderVO.getOperatorId();
        int handleResult = workOrderVO.getHandleResult();

        //获取人员是否在线
        Response<StaffStatus> staffStatusResponse = staffClient.getStaffStatus(orderType,operatorId);
        StaffStatus staffStatus = Optional.ofNullable(staffStatusResponse).filter(Response::ok).map(Response::getData).orElse(null);
        if(Objects.isNull(staffStatus)){
            return true;
        }

        int status = staffStatus.getStaffStatus();

        workTime(status,operatorId,subId,handleResult,time);

        return true;
    }


    private void workTime(int status,long operatorId, long subId, int handleResult,String time){

        ReportPendingEntryPayload reportPendingEntryPayload = ReportPendingEntryPayload.builder()
                .subId(subId)
                .credibleType(CredibleTypeEnum.HELP_PROVE.getKey())
                .time(time)
                .build();

        DelayLevel delayLevel = DelayLevel.M30;

        if (status == StaffStatusEnum.online.getType() ||
                status == StaffStatusEnum.stop.getType()) {
            //工作台提醒
            streamService.pushReportPending(operatorId);
        }

        //离线直接发群消息
        if (status == StaffStatusEnum.offline.getType() || handleResult == HandleResultEnum.undoing.getType()) {
            delayLevel = DelayLevel.S1;
        }

        producer.send(new Message(MQTopicCons.CF, com.shuidihuzhu.cf.constants.admin.MQTagCons.REPORT_SCHEDULE_PUSH_V1,
                subId + "_" + System.currentTimeMillis(), reportPendingEntryPayload, delayLevel));
    }


    @Override
    protected Logger getLogger() {
        return log;
    }
}
