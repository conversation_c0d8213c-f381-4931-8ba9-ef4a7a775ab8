package com.shuidihuzhu.cf.admin.configuration;

import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.enums.MyErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;

import javax.servlet.http.HttpServletRequest;

import static com.shuidihuzhu.common.web.enums.ErrorCode.SYSTEM_PARAM_ERROR;
import static org.springframework.http.HttpStatus.BAD_REQUEST;

/**
 * @author: cuikexiang
 */
@ControllerAdvice
@Slf4j
public class GlobalControllerExceptionHandler {
    @ExceptionHandler(MissingServletRequestParameterException.class)
    protected ResponseEntity<Object> handleMissingServletRequestParameter(MissingServletRequestParameterException ex, HttpServletRequest request) {
        MyErrorCode errorCode = ErrorCode.SYSTEM_PARAM_ERROR;
        HttpStatus status = BAD_REQUEST;
        return doExceptionHandler(ex, request, errorCode, status);
    }

    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    protected ResponseEntity<Object> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException ex, HttpServletRequest request) {
        MyErrorCode errorCode = ErrorCode.METHOD_ARGUMENT_TYPE_MISMATCH;
        HttpStatus status = BAD_REQUEST;
        return doExceptionHandler(ex, request, errorCode, status);
    }

    @ExceptionHandler(IllegalStateException.class)
    protected ResponseEntity<Object> handleIllegalStateException(IllegalStateException ex, HttpServletRequest request) {
        //判断来源
        boolean isParamException = judgeIllegalStateException((IllegalStateException) ex);
        if (!isParamException) {
            throw ex;
        }
        MyErrorCode errorCode = ErrorCode.SYSTEM_PARAM_ERROR;
        HttpStatus status = BAD_REQUEST;
        return doExceptionHandler(ex, request, errorCode, status);
    }


    private ResponseEntity<Object> doExceptionHandler(Exception ex, HttpServletRequest request, MyErrorCode errorCode, HttpStatus status) {
        log.warn("{} in {}", ex.getClass().getSimpleName(), request.getRequestURI());
        Response<String> response = NewResponseUtil.makeError(errorCode, ex.getMessage());
        return new ResponseEntity<>(response, status);
    }


    private boolean judgeIllegalStateException(IllegalStateException e) {
        try {
            StackTraceElement[] stackTraceElements = e.getStackTrace();
            //如果是spring 参数解析类中抛出的异常，则认定为参数错误
            if (stackTraceElements != null && stackTraceElements.length > 0) {
                StackTraceElement first = stackTraceElements[0];
                String className = first.getClassName();
                Class<?> type = Class.forName(className);
                //判断是否是HandlerMethodArgumentResolver的实现类
                return HandlerMethodArgumentResolver.class.isAssignableFrom(type);
            }
        } catch (Throwable throwable) {
            log.error("judgeIllegalStateException | error", throwable);
        }
        return false;
    }
}
