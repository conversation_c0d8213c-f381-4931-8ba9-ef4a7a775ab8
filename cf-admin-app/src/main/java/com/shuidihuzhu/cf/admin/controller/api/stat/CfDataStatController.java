package com.shuidihuzhu.cf.admin.controller.api.stat;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.stat.feign.ICfStatClient;
import com.shuidihuzhu.cf.client.stat.model.CfStatType;
import com.shuidihuzhu.cf.client.stat.model.CfStatUseRatioResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-02-24 17:02
 **/
@Api("cf-stat")
@RestController
@Slf4j
@RequestMapping("/admin/cf/stat")
public class CfDataStatController {

    @Autowired
    private ICfStatClient cfStatClient;

    @RequiresPermission("data-stat:get-all-data-types")
    @ApiOperation("所有能提供的查询场景")
    @PostMapping("get-all-data-types")
    public Response<List<CfStatType>> getAllDataTypes() {
        RpcResult<List<CfStatType>> allDataTypes = cfStatClient.getAllDataTypes();
        if (allDataTypes.isFail()) {
            log.warn("调用cf-stat getAllDataTypes接口失败,allDataTypes:{}", allDataTypes);
        }
        return NewResponseUtil.makeResponse(allDataTypes.getCode(), allDataTypes.getMsg(), allDataTypes.getData());
    }

    @RequiresPermission("data-stat:get-use-ratio")
    @ApiOperation("数据访问热度")
    @PostMapping("get-use-ratio")
    public Response<CfStatUseRatioResult> getUseRatio(@RequestParam("date") int date) {
        RpcResult<CfStatUseRatioResult> useRatio = cfStatClient.getUseRatio(date);
        if (useRatio.isFail()) {
            log.warn("调用cf-stat getUseRatio接口失败,useRatio:{}", useRatio);
        }
        return NewResponseUtil.makeResponse(useRatio.getCode(), useRatio.getMsg(), useRatio.getData());
    }

}
