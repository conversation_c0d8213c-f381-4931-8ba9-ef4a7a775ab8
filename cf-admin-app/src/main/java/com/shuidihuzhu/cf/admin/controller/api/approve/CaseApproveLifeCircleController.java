package com.shuidihuzhu.cf.admin.controller.api.approve;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.admin.util.result.ResultUtils;
import com.shuidihuzhu.cf.service.approve.lifecircle.CaseApproveLifeCircleService;
import com.shuidihuzhu.cf.vo.approve.ApproveLifeCircleVO;
import com.shuidihuzhu.common.web.model.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Api("案例审核生命周期")
@RequestMapping("admin/cf/approve/life-circle")
@RestController
public class CaseApproveLifeCircleController {

    @Autowired
    private CaseApproveLifeCircleService caseApproveLifeCircleService;

    @ApiOperation("获取案例审核生命周期")
    @PostMapping("get-life-circle")
    @RequiresPermission("approve-life-circle:get-life-circle")
    public Response<List<ApproveLifeCircleVO>> getLifeCircle(@RequestParam int caseId){

        return ResultUtils.transformOpResult2Response(caseApproveLifeCircleService.getLifeCircle(caseId));
    }
}
