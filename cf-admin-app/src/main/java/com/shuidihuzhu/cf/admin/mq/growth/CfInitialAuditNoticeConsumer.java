package com.shuidihuzhu.cf.admin.mq.growth;

import com.shuidihuzhu.cf.biz.crowdfunding.NoticeGrowthToolBiz;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @DATE 2020/4/9
 */

@Service
@RocketMQListener(id = "notice_growth_chuci",
        group = "notice_growth_" + com.shuidihuzhu.cf.constants.MQTagCons.CF_INITIAL_AUDIT_OPERATION_MSG,
        tags = com.shuidihuzhu.cf.constants.MQTagCons.CF_INITIAL_AUDIT_OPERATION_MSG,
        topic = MQTopicCons.CF)
@Slf4j
public class CfInitialAuditNoticeConsumer implements MessageListener<InitialAuditItem.InitialAuditOperation> {

    @Autowired
    private NoticeGrowthToolBiz growthToolBiz;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<InitialAuditItem.InitialAuditOperation> mqMessage) {

        if (mqMessage == null || mqMessage.getPayload() == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        int caseId = mqMessage.getPayload().getCaseId();

        growthToolBiz.notice(caseId);

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
