package com.shuidihuzhu.cf.admin.util.excel.importfromexcel.service;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.admin.util.admin.IntegerUtil;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.model.admin.*;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.common.web.util.DateUtil;
import jodd.util.StringUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Created by ahrievil on 2017/1/8.
 */
@Service
public class ImportFormExcelService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ImportFormExcelService.class);

    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;
    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    public List<CfAndroidChannel> readCsvToCfAndroidChannel(InputStream is) throws Exception {
        InputStreamReader read = new InputStreamReader(
                is,"Unicode");
        BufferedReader bufferedReader = new BufferedReader(read);
        //逐行读取的串值引用对象
        String line = bufferedReader.readLine();
        int lineNum = 1;
        int successCount = 0;
        List<CfAndroidChannel> list = Lists.newArrayList();
        //如果读取的行不为空,则进行判断处理
        while((line = bufferedReader.readLine()) != null){
            LOGGER.info("ImportFormExcelService readCsvToCfAndroidChannel line:{}", line);
            lineNum ++;
            String[] fields = line.split("\\t");
            if (fields.length != 7) {
                LOGGER.error("ImportFormExcelService readCsvToCfAndroidChannel error! lineNum = " + lineNum);
            } else {
                successCount++;
                LOGGER.info("ImportFormExcelService readCsvToCfAndroidChannel successCount:{}", successCount);
                CfAndroidChannel cfAndroidChannel = new CfAndroidChannel();
                cfAndroidChannel.setLineNum(++lineNum);
                cfAndroidChannel.setSelectDate(fields[0]);
                cfAndroidChannel.setChannelName(fields[1]);
                cfAndroidChannel.setNewUsers(IntegerUtil.parseInt(fields[2]));
                cfAndroidChannel.setActiveUsers(IntegerUtil.parseInt(fields[3]));
                cfAndroidChannel.setStartUps(IntegerUtil.parseInt(fields[4]));
                cfAndroidChannel.setTimes(fields[5]);
                DecimalFormat decimalFormat = new DecimalFormat("0.0000");
                String substring = fields[6].substring(0, fields[6].length() - 1);
                double v = Double.parseDouble(substring) / 100;
                String format = decimalFormat.format(v);
                cfAndroidChannel.setRetentionRate(Double.parseDouble(format));
                LOGGER.info("ImportFormExcelService readFromCsv cfAndroidChannel:{}", cfAndroidChannel);
                list.add(cfAndroidChannel);
            }
        }
        //关闭文件读取流
        bufferedReader.close();
        //返回替换后的文件
        return list;
    }

    public List<ExportClinkCallInDay> readCsvToExportClinkCallInDay(InputStream is) throws Exception {
        InputStreamReader read = new InputStreamReader(
                is,"gb2312");
        BufferedReader bufferedReader = new BufferedReader(read);
        //逐行读取的串值引用对象
        String line = bufferedReader.readLine();
        String header = "时间,汇总,客户电话,来电地区,热线号码,IVR,队列号,队列名,座席工号,座席姓名,座席电话,开始时间,接通时间,通话时长,接听状态,挂机方,总时长,录音,话费（元）,自定义字段,备注,是否在案例库,质检评分,质检备注";
        int lineNum = 1;
        int successCount = 0;
        List<ExportClinkCallInDay> list = Lists.newArrayList();
        if (!line.equals(header)) {
            return list;
        }
        //如果读取的行不为空,则进行判断处理
        while(StringUtil.isNotBlank(line = bufferedReader.readLine())){
            LOGGER.info("ImportFormExcelService readCsvToExportClinkCallInDay line:{}", line);
            lineNum ++;
            String[] fields = line.split(",");
            if (fields.length < 23) {
                LOGGER.error("ImportFormExcelService readCsvToExportClinkCallInDay error! lineNum = " + lineNum);
            } else {
                successCount++;
                LOGGER.info("ImportFormExcelService readCsvToExportClinkCallInDay successCount:{}", successCount);
                ExportClinkCallInDay exportClinkCallInDay = new ExportClinkCallInDay();
                exportClinkCallInDay.setLineNum(++lineNum);
                exportClinkCallInDay.setDayKey(DateUtil.getDateFromShortString(fields[0].replace("/","-")));
                exportClinkCallInDay.setSummary(fields[1]);
                exportClinkCallInDay.setCustomerCalls(fields[2]);
                exportClinkCallInDay.setRegion(fields[3]);
                exportClinkCallInDay.setHotlineNumber(fields[4]);
                exportClinkCallInDay.setIvr(fields[5]);
                exportClinkCallInDay.setQueueNum(fields[6]);
                exportClinkCallInDay.setQueueName(fields[7]);
                exportClinkCallInDay.setWorkNum(fields[8]);
                exportClinkCallInDay.setWorkName(fields[9]);
                exportClinkCallInDay.setWorkMobile(fields[10]);
                exportClinkCallInDay.setStartTime(fields[11]);
                exportClinkCallInDay.setBeginTime(fields[12]);
                exportClinkCallInDay.setContactTime(fields[13]);
                exportClinkCallInDay.setState(fields[14]);
                exportClinkCallInDay.setState2(fields[15]);
                exportClinkCallInDay.setTotalTime(fields[16]);
                exportClinkCallInDay.setRecording(fields[17]);
                exportClinkCallInDay.setCharges(fields[18]);
                exportClinkCallInDay.setCustom(fields[19]);
                exportClinkCallInDay.setComment(fields[20]);
                exportClinkCallInDay.setJudge(fields[21]);
                exportClinkCallInDay.setQualityInspection(fields[22]);
                if (fields.length == 24) {
                    exportClinkCallInDay.setQualityComment(fields[23]);
                }
                LOGGER.info("ImportFormExcelService readCsvToExportClinkCallInDay exportClinkCallInDay:{}", exportClinkCallInDay);
                list.add(exportClinkCallInDay);
            }
        }
        //关闭文件读取流
        bufferedReader.close();
        //返回替换后的文件
        return list;
    }

    public List<ExportClinkCalBusinessDay> readCsvToExportClinkCalBusinessDay(InputStream is) throws Exception {
        InputStreamReader read = new InputStreamReader(
                is,"gb2312");
        BufferedReader bufferedReader = new BufferedReader(read);
        //逐行读取的串值引用对象
        String line = bufferedReader.readLine();
        String header = "时间,备注,业务类型,姓名,电话1,手机1,业务状态,优先级,创建人,创建时间,修改人,最近修改时间,业务类型/问题类型,工单状态,处理人";
        int lineNum = 1;
        int successCount = 0;
        List<ExportClinkCalBusinessDay> list = Lists.newArrayList();
        if (!line.equals(header)) {
            return list;
        }
        //如果读取的行不为空,则进行判断处理
        while((line = bufferedReader.readLine()) != null){
            LOGGER.info("ImportFormExcelService readCsvToExportClinkCalBusinessDay line:{}", line);
            lineNum ++;
            String[] fields = line.split(",");
            if (fields.length < 14) {
                LOGGER.error("ImportFormExcelService readCsvToExportClinkCalBusinessDay error! lineNum = " + lineNum);
            } else {
                successCount++;
                LOGGER.info("ImportFormExcelService readCsvToExportClinkCalBusinessDay successCount:{}", successCount);
                System.out.println("successCount"+successCount);
                ExportClinkCalBusinessDay exportClinkCalBusinessDay = new ExportClinkCalBusinessDay();
                exportClinkCalBusinessDay.setLineNum(++lineNum);
                exportClinkCalBusinessDay.setDayKey(DateUtil.getDateFromShortString(fields[0].replace("/","-")));
                exportClinkCalBusinessDay.setComment(fields[1]);
                exportClinkCalBusinessDay.setBusinessTypes(fields[2]);
                exportClinkCalBusinessDay.setName(fields[3]);
                exportClinkCalBusinessDay.setPhone(fields[4]);
                exportClinkCalBusinessDay.setMobile(fields[5]);
                exportClinkCalBusinessDay.setServiceState(fields[6]);
                exportClinkCalBusinessDay.setLevel(fields[7]);
                exportClinkCalBusinessDay.setFounder(fields[8]);
                exportClinkCalBusinessDay.setCreateTime(fields[9]);
                exportClinkCalBusinessDay.setModifier(fields[10]);
                exportClinkCalBusinessDay.setUpdateTime(fields[11]);
                exportClinkCalBusinessDay.setTypes(fields[12]);
                exportClinkCalBusinessDay.setOrderStatus(fields[13]);
                if (fields.length == 15) {
                    exportClinkCalBusinessDay.setProcessor(fields[14]);
                }
                LOGGER.info("ImportFormExcelService readCsvToExportClinkCalBusinessDay exportClinkCalBusinessDay:{}", exportClinkCalBusinessDay);
                list.add(exportClinkCalBusinessDay);
            }
        }
        //关闭文件读取流
        bufferedReader.close();
        //返回替换后的文件
        return list;
    }

    public List<ExportClinkCallOutDay> readCsvToExportClinkCallOutDay(InputStream is) throws Exception {
        InputStreamReader read = new InputStreamReader(
                is,"gb2312");
        BufferedReader bufferedReader = new BufferedReader(read);
        //逐行读取的串值引用对象
        String line = bufferedReader.readLine();
        String header = "时间,汇总,客户电话,来电地区,中继号码,座席工号,座席姓名,座席电话,接通时间,接听状态,通话时长,话费（元）,总时长,呼叫类型,外呼任务,录音,备注,挂机方,呼叫情况,是否在案例库,质检评分";
        int lineNum = 1;
        int successCount = 0;
        List<ExportClinkCallOutDay> list = Lists.newArrayList();
        if (!line.equals(header)) {
            return list;
        }
        //如果读取的行不为空,则进行判断处理
        while((line = bufferedReader.readLine()) != null){
            LOGGER.info("ImportFormExcelService readCsvToExportClinkCallOutDay line:{}", line);
            lineNum ++;
            String[] fields = line.split(",");
            if (fields.length < 21) {
                LOGGER.error("ImportFormExcelService readCsvToExportClinkCallOutDay error! lineNum = " + lineNum);
            } else {
                successCount++;
                LOGGER.info("ImportFormExcelService readCsvToExportClinkCallOutDay successCount:{}", successCount);
                ExportClinkCallOutDay exportClinkCallOutDay = new ExportClinkCallOutDay();
                exportClinkCallOutDay.setLineNum(++lineNum);
                exportClinkCallOutDay.setDayKey(DateUtil.getDateFromShortString(fields[0].replace("/","-")));
                exportClinkCallOutDay.setSummary(fields[1]);
                exportClinkCallOutDay.setCustomerCalls(fields[2]);
                exportClinkCallOutDay.setRegion(fields[3]);
                exportClinkCallOutDay.setRelayNumber(fields[4]);
                exportClinkCallOutDay.setWorkNum(fields[5]);
                exportClinkCallOutDay.setWorkName(fields[6]);
                exportClinkCallOutDay.setWorkMobile(fields[7]);
                exportClinkCallOutDay.setAccessTime(fields[8]);
                exportClinkCallOutDay.setState(fields[9]);
                exportClinkCallOutDay.setContactTime(fields[10]);
                exportClinkCallOutDay.setCharges(fields[11]);
                exportClinkCallOutDay.setTotalCharges(fields[12]);
                exportClinkCallOutDay.setTotalTime(fields[13]);
                exportClinkCallOutDay.setCallType(fields[14]);
                exportClinkCallOutDay.setCallDuty(fields[15]);
                exportClinkCallOutDay.setRecording(fields[16]);
                exportClinkCallOutDay.setComment(fields[17]);
                exportClinkCallOutDay.setHangUp(fields[18]);
                exportClinkCallOutDay.setJudge(fields[19]);
                exportClinkCallOutDay.setQualityInspection(fields[20]);
                LOGGER.info("ImportFormExcelService readCsvToExportClinkCallOutDay exportClinkCallOutDay:{}", exportClinkCallOutDay);
                list.add(exportClinkCallOutDay);
            }
        }
        //关闭文件读取流
        bufferedReader.close();
        //返回替换后的文件
        return list;
    }

    public List<ExportMinaMajorDisease> readMinaMajorDiseaseData(InputStream is) throws Exception {
        InputStreamReader reader = new InputStreamReader(is, "UTF-8");
        BufferedReader bufferedReader = new BufferedReader(reader);
        String line = bufferedReader.readLine();
        String header = "标题,内容,分类,病种";
        int lineNum = 1;
        List<ExportMinaMajorDisease> list = Lists.newArrayList();
        if (!line.contains(header)) {
            return list;
        }
        while (StringUtils.isNotBlank(line = bufferedReader.readLine())) {
            LOGGER.info("ImportFormExcelService readMinaMajorDiseaseData line:{}", line);
            lineNum ++;
            String[] fields = line.split(",");
            if (fields.length < 4) {
                LOGGER.error("ImportFormExcelService readMinaMajorDiseaseData dataError lineNum:{}", lineNum);
            } else {
                ExportMinaMajorDisease exportMinaMajorDisease = new ExportMinaMajorDisease();
                exportMinaMajorDisease.setDiseaseName(fields[3]);
                exportMinaMajorDisease.setDiseaseReason(fields[2]);
                exportMinaMajorDisease.setTitle(fields[0]);
                exportMinaMajorDisease.setContent(fields[1]);
                LOGGER.info("ImportFormExcelService readMinaMajorDiseaseData exportMinaMajorDisease:{}", exportMinaMajorDisease);
                System.out.println(exportMinaMajorDisease);
                list.add(exportMinaMajorDisease);
            }
        }
        bufferedReader.close();
        return list;
    }

    public Map<String, List> readRegisterOutBoundFromCsvFile(InputStream inputStream) throws Exception {
        InputStreamReader inputStreamReader = new InputStreamReader(inputStream, "gb2312");
        BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
        String head = "任务名称,任务生成时间,业务结果,客户名称,分配座席,电话号码,呼叫结果,呼叫次数,最近呼叫时间,备注";
        Stream<String> allLine = bufferedReader.lines();
        String headLine = allLine.limit(1).collect(Collectors.toList()).get(0);
        List<String> originData = bufferedReader.lines().skip(1).map(line -> line.replaceAll("['=\"''\"''\\n''\\t''\\r']", ""))
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (!StringUtils.equals(head, headLine)) {
            return Maps.newHashMap();
        }
        int lineNum = 1;
        LOGGER.info("ImportFormExcelService readFromCsvFile hardLine:{}", headLine);
        List<CfCallTrackingClues> list = Lists.newArrayList();
        List<Integer> errorLineNum = Lists.newArrayList();
        for (String innerLine : originData) {
            LOGGER.info("ImportFormExcelService readFromCsvFile innerLine:{}", innerLine);
            lineNum ++;
            List<String> strings = Splitter.on(",").splitToList(innerLine);
            if (strings.stream().allMatch(StringUtils::isBlank)) {
                continue;
            }
            CfCallTrackingClues cfCallTrackingClues = new CfCallTrackingClues();
            String dataFormat = "yyyy-MM-dd HH:mm:ss";
            String dataFormat1 = "yyyy-MM-dd HH:mm";
            if (strings.size() < 10) {
                errorLineNum.add(lineNum);
                LOGGER.info("ImportFormExcelService readRegisterOutBoundFromCsvFile error! lineNum:{}", lineNum);
                LOGGER.info("ImportFormExcelService readFromCsvFile innerLine:{}", innerLine);
            } else {
                try {
                    cfCallTrackingClues.setResult(trimToBlank(strings.get(2)));
                    Timestamp buildTime;
                    try {
                        String date = trimToBlank(strings.get(1)).replaceAll("/", "-");
                        if (date.length() > 16) {
                            buildTime = DateUtil.getTimeStampByFormat(date, dataFormat);
                        } else {
                            buildTime = DateUtil.getTimeStampByFormat(date, dataFormat1);
                        }
                    } catch (Exception e) {
                        errorLineNum.add(lineNum);
                        LOGGER.error("error msg", e);
                        continue;
                    }
                    cfCallTrackingClues.setBuildTime(buildTime);
                    cfCallTrackingClues.setTaskName(trimToBlank(strings.get(0)));
                    cfCallTrackingClues.setCustomerName(trimToBlank(strings.get(3)));
                    cfCallTrackingClues.setPosition(trimToBlank(strings.get(4)));
                    String mobile = trimToBlank(strings.get(5));
                    cfCallTrackingClues.setMobile(mobile);
                    //加密手机号
                    cfCallTrackingClues.setCryptoMobile(oldShuidiCipher.aesEncrypt(mobile));
                    cfCallTrackingClues.setCallResult(trimToBlank(strings.get(6)));
                    String callCount = trimToBlank(strings.get(7));
                    Integer callCountInteger = 0;
                    try {
                        callCountInteger = Integer.valueOf(callCount);
                    } catch (Exception e) {
                        errorLineNum.add(lineNum);
                        LOGGER.error("numberFormat error", e);
                        LOGGER.info("ImportFormExcelService readRegisterOutBoundFromCsvFile error! lineNum:{}", lineNum);
                        LOGGER.info("ImportFormExcelService readFromCsvFile innerLine:{}", innerLine);
                        continue;
                    }
                    cfCallTrackingClues.setCallCount(callCountInteger);
                    cfCallTrackingClues.setCallTime(trimToBlank(strings.get(8)));
                    cfCallTrackingClues.setRemark(trimToBlank(strings.get(9)));
                    cfCallTrackingClues.setLineNum(lineNum);
                } catch (Exception e) {
                    errorLineNum.add(lineNum);
                    LOGGER.info("ImportFormExcelService readRegisterOutBoundFromCsvFile error! lineNum:{}", lineNum);
                    LOGGER.info("ImportFormExcelService readFromCsvFile innerLine:{}", innerLine);
                    continue;
                }
            }
            if (cfCallTrackingClues.getBuildTime() != null) {
                list.add(cfCallTrackingClues);
            }
        }
        try {
            List<Map<String, Object>> userMobileMap = Lists.newArrayList();
            Map<String, Integer> mobileUserIdMap = Maps.newHashMap();
            userMobileMap.forEach(val -> {
                Integer userId = (Integer) val.get("userId");
                String cryptoMobile = (String) val.get("cryptoMobile");
                mobileUserIdMap.put(cryptoMobile, userId);
            });
            list.forEach(val -> {
                Integer userId = mobileUserIdMap.get(val.getCryptoMobile());
                if (userId != null) {
                    val.setUserId(userId);
                }
            });
        } catch (Exception e) {
            LOGGER.error("error msg", e);
        }
        Map<String, List> result = Maps.newHashMap();
        result.put("data", list);
        result.put("errorMsg", errorLineNum);
        return result;
    }

    public String trimToBlank(String s) {
        return s == null ? "" : s.trim();
    }

    public static void main(String[] args) throws Exception {
//        File file = new File("/Users/<USER>/Desktop/10.12.csv");
//        InputStream is = new FileInputStream(file);
//        ImportFormExcelService importFormExcelService = new ImportFormExcelService();
//        Map<String, List> stringListMap = importFormExcelService.readRegisterOutBoundFromCsvFile(is);
//        List<CfCallTrackingClues> data = stringListMap.get("data");
//        List<Integer> errorLineNums = stringListMap.get("errorMsg");
////        data.forEach(System.out::println);
//        errorLineNums.forEach(System.out::println);
        String line = "捐款人";
        List<String> strings = Splitter.on(",").splitToList(line);
        System.out.println(strings.size());
        String ymdStrFromTimestamp = DateUtil.getYMDStrFromTimestamp(new Timestamp(System.currentTimeMillis()));
        System.out.println(ymdStrFromTimestamp);
        Object b = "heheda";
        String b1 = (String) b;
        System.out.println(b1);
    }
}
