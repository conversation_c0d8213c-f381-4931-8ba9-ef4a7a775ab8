package com.shuidihuzhu.cf.admin.controller.api.innerapi;

import com.google.common.base.Splitter;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.facade.AdminApolloCofig;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.admin.client.SpecialStaffFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackFeignClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2020/8/27
 */
@RestController
@Deprecated
public class SpecialStaffController implements SpecialStaffFeignClient {

    @Autowired
    private CfClewtrackFeignClient clewtrackFeignClient;

    @Autowired
    private CrowdfundingFeignClient feignClient;


    @Override
    public Response<Set<Long>> getStaff() {

        Set<Long> set = Sets.newHashSet();

        String tzb = AdminApolloCofig.getValueFromApollo(AdminApolloCofig.cf_juanzhuan_tzb,"");
        if (StringUtils.isEmpty(tzb)){
            return NewResponseUtil.makeSuccess(set);
        }
        set = Arrays.stream(tzb.split(",")).map(Long::valueOf).collect(Collectors.toSet());
        return NewResponseUtil.makeSuccess(set);
    }

    @Override
    public Response<Boolean> getSpecialChannel(int caseId) {

        FeignResponse<CrowdfundingInfo> feignResponse =  feignClient.getCaseInfoById(caseId);

        CrowdfundingInfo c = Optional.ofNullable(feignResponse)
                .filter(FeignResponse::ok)
                .map(FeignResponse::getData)
                .orElse(null);

        if (c == null){
            return NewResponseUtil.makeSuccess(false);
        }

        String channel = AdminApolloCofig.getValueFromApollo(AdminApolloCofig.cf_juanzhuan_channel,"wx");

        Set<String> s = Splitter.on(",").splitToList(channel).stream().collect(Collectors.toSet());

        Response<Map<String, Boolean>> response = clewtrackFeignClient.checkClewChannel4DonationTransferByInfoId(c.getInfoId(),s);

        Map<String, Boolean> map = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);

        if (MapUtils.isEmpty(map)){
            return NewResponseUtil.makeSuccess(false);
        }

        return NewResponseUtil.makeSuccess(map.keySet().stream().filter(r->s.contains(r)).findAny().isPresent());
    }
}
