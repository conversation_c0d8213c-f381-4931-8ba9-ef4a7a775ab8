package com.shuidihuzhu.cf.admin.mq.report;

import com.shuidihuzhu.cf.biz.crowdfunding.IAdminCredibleInfoService;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.crowdfunding.CredibleTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfCredibleInfoDO;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportAddTrust;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2020/1/10 下午5:37
 * @desc
 */
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.CF_UPDATE_CREDIBLE_STATUS, tags = MQTagCons.CF_UPDATE_CREDIBLE_STATUS, topic = MQTopicCons.CF)
public class AdminUpdateCredibleInfoConsumer implements MessageListener<CfReportAddTrust> {

    @Autowired
    private IAdminCredibleInfoService credibleInfoService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfReportAddTrust> mqMessage) {
        if(Objects.isNull(mqMessage) || Objects.isNull(mqMessage.getPayload())){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        CfReportAddTrust addTrust = mqMessage.getPayload();

        CfCredibleInfoDO cfCredibleInfoDO = credibleInfoService.queryBySubId(addTrust.getId(), CredibleTypeEnum.SUPPLY_VERFIFY.getKey());
        if(Objects.isNull(cfCredibleInfoDO) || (cfCredibleInfoDO.getAuditStatus() != CrowdfundingInfoStatusEnum.UN_SUBMITTED.getCode() && cfCredibleInfoDO.getAuditStatus() != CrowdfundingInfoStatusEnum.REJECTED.getCode())){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        credibleInfoService.updateSubmitInfo(addTrust.getId(), CrowdfundingInfoStatusEnum.SUBMITTED.getCode(),CredibleTypeEnum.SUPPLY_VERFIFY.getKey());

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
