package com.shuidihuzhu.cf.admin.controller.api.innerapi.pure;

import com.shuidihuzhu.cf.client.adminpure.feign.AdminKwaiShortIdeosCaseMountFeignClient;
import com.shuidihuzhu.cf.client.adminpure.model.kwai.KwaiShortIdeosCaseMountParam;
import com.shuidihuzhu.cf.client.adminpure.model.kwai.KwaiShortIdeosCaseMountVo;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.kwai.KwaiAppShortIdeosCaseMountBiz;
import com.shuidihuzhu.cf.model.kwai.KwaiAppShortIdeosCaseMountDto;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 */
@RestController
public class AdminKwaiShortIdeosCaseMountFeignController implements AdminKwaiShortIdeosCaseMountFeignClient {

    @Resource
    private KwaiAppShortIdeosCaseMountBiz kwaiAppShortIdeosCaseMountBiz;

    @Override
    public OperationResult<KwaiShortIdeosCaseMountVo> getInfoUuidList(KwaiShortIdeosCaseMountParam param) {

        KwaiAppShortIdeosCaseMountDto dto = KwaiAppShortIdeosCaseMountDto.builder()
                .encryptMobile(param.getEncryptMobile())
                .build();

        KwaiShortIdeosCaseMountVo vo = new KwaiShortIdeosCaseMountVo();
        try {
            List<String> infoUuidList = kwaiAppShortIdeosCaseMountBiz.getInfoUuidList(dto);
            vo.setInfoUuidList(infoUuidList);
        } catch (Exception e) {
            return OperationResult.fail(e.getMessage());
        }
        return OperationResult.success(vo);
    }
}
