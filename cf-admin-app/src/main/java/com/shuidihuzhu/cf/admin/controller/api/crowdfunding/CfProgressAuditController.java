package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.service.MobileUserIdModel;
import com.shuidihuzhu.cf.admin.util.ParamTimeRangeHandler;
import com.shuidihuzhu.cf.biz.aiphoto.ImageWatermarkService;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdFundingProgressBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingAuthorBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingReportBiz;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfFundUseAuditDao;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.approve.UseProgressStatusEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CaseReportDealStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.CfTaskEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdFundingProgressType;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCfFundUseAuditInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport;
import com.shuidihuzhu.cf.service.EventCenterPublishService;
import com.shuidihuzhu.cf.vo.crowdfunding.AdminCrowdfundingProgress;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.grpc.account.v1.feign.SimpleUserAccountServiceClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.MobileUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: lixuan
 * @date: 2018/3/25 11:01
 */
@Slf4j
@RestController
@RequestMapping(path = "/admin/cf/progress/audit")
public class CfProgressAuditController {
    private static final int AUDIT_SUCCESS = UseProgressStatusEnum.AUDIT_SUCCESS.getCode();
    private static final int AUDIT_REJECTED_STATUS = UseProgressStatusEnum.AUDIT_REJECTED_STATUS.getCode();
    private static final int ALL_STATUS = 3;


    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Autowired(required = false)
    private Producer producer;

    @Autowired
    private SimpleUserAccountServiceClient simpleUserAccountServiceClient;

    @Autowired
    private AdminCfFundUseAuditDao adminCfFundUseAuditDao;

    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Autowired
    private AdminCrowdFundingProgressBiz adminCrowdFundingProgressBiz;

    @Autowired
    private AdminCrowdfundingAuthorBiz adminCrowdfundingAuthorBiz;
    @Autowired
    private AdminCrowdfundingReportBiz adminCrowdfundingReportBiz;

    @Autowired
    private CommonOperationRecordClient commonOperationRecordClient;

    @Autowired
    private EventCenterPublishService eventCenterPublishService;

    @Autowired
    private ImageWatermarkService watermarkService;

    @PostMapping("/get")
    @RequiresPermission("progress-audit:get")
    public Response get(String infoUuid, String pwd) {
        if (!pwd.equals("ASEUbN7eSm6fHIni")) {
            return NewResponseUtil.makeSuccess(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (StringUtils.isBlank(infoUuid)) {
            return NewResponseUtil.makeSuccess(null);
        }

        CrowdfundingInfo cfInfo = this.crowdfundingDelegate.getCrowdfundingInfoByInfoId(infoUuid);
        if (cfInfo == null) {
            return NewResponseUtil.makeError(CfErrorCode.CF_NOT_FOUND);
        }

        List<Integer> types = ImmutableList.of(CrowdFundingProgressType.PROGRESS_NEEDS_AUDIT.value());
        List<CrowdFundingProgress> crowdFundingProgressList = crowdfundingDelegate.getActivityProgress(cfInfo.getId(),
                types,
                0,
                100);
        return NewResponseUtil.makeSuccess(crowdFundingProgressList);
    }


    @PostMapping("/update-type")
    @RequiresPermission("progress-audit:update-type")
    public Response get(String infoUuid, int progressId, String pwd) {
        if (!pwd.equals("ASEUbN7eSm6fHIni")) {
            return NewResponseUtil.makeSuccess(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (StringUtils.isBlank(infoUuid)) {
            return NewResponseUtil.makeSuccess(null);
        }

        CrowdfundingInfo cfInfo = this.crowdfundingDelegate.getCrowdfundingInfoByInfoId(infoUuid);
        if (cfInfo == null) {
            return NewResponseUtil.makeError(CfErrorCode.CF_NOT_FOUND);
        }

        CrowdFundingProgress progress = crowdfundingDelegate.getActivityProgressById(progressId);
        if (!progress.getActivityId().equals(cfInfo.getId())) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        int updateResult = crowdfundingDelegate.updateType(progress.getId(), CrowdFundingProgressType.PROGRESS_NEEDS_AUDIT.value(),
                CrowdFundingProgressType.PROGRESS.value());
        if (updateResult > 0 && producer != null) {
            producer.send(new Message(MQTopicCons.CF,
                    MQTagCons.CF_PUBLISH_PROGRESS,
                    MQTagCons.CF_PUBLISH_PROGRESS + "_" + progress.getId(),
                    progress));
            this.crowdfundingDelegate.updateTimes(infoUuid, CfTaskEnum.Rule.PUBLISH_PROCESS);
        }

        return NewResponseUtil.makeSuccess(null);
    }


    @ApiOperation(value = "风控增信多条件资金审核", response = AdminCfFundUseAuditInfo.class,
                responseContainer = "List")
    @PostMapping("/get-list")
    @RequiresPermission("progress-audit:get-list")
    public Response getFields(
            @RequestParam(required = false, name = "crowdfundingId", defaultValue = "") Integer crowdfundingId,
            @RequestParam(required = false, name = "userMobile") String userMobile,
            @RequestParam(required = false, name = "progressStatus", defaultValue = "3") Integer progressStatus,
            @RequestParam(required = false, name = "drawCashStartTime") String drawCashStartTime,
            @RequestParam(required = false, name = "drawCashEndTime") String drawCashEndTime,
            @RequestParam(required = false, name = "fundUseSubmitStartTime") String fundUseSubmitStartTime,
            @RequestParam(required = false, name = "fundUseSubmitEndTime") String fundUseSubmitEndTime,
            @RequestParam(required = false, name = "current", defaultValue = "1") Integer current,
            @RequestParam(required = false, name = "pageSize", defaultValue = "10") Integer pageSize,
            @ApiParam(value = "true 为有举报 false为没有举报 其他值均为全部") @RequestParam(required = false, name = "hasReport", defaultValue = "") String hasReport) {

        log.info("select by crowdfundingId {}, progressType {}, drawCashStartTime {}" +
                        "drawCashEndTime {} fundUseSubmitStartTime {} fundUseSubmitEndTime {} currentPage {} pageSize {}",
                crowdfundingId, progressStatus, drawCashStartTime, drawCashEndTime, fundUseSubmitStartTime, fundUseSubmitEndTime, current, pageSize);

        //判断时间
        Response fundUseSubmitStartTimeDateCheck = ParamTimeRangeHandler.illegalTimeRange(fundUseSubmitStartTime, fundUseSubmitEndTime);
        if (fundUseSubmitStartTimeDateCheck.notOk()) {
            return fundUseSubmitStartTimeDateCheck;
        }

        Pair<String, String> handleUseSubmitPair = ParamTimeRangeHandler.handleGetFieldsTime(crowdfundingId, fundUseSubmitStartTime, fundUseSubmitEndTime);
        fundUseSubmitStartTime = handleUseSubmitPair.getLeft();
        fundUseSubmitEndTime = handleUseSubmitPair.getRight();

        List<Integer> crowdfundingIds = Lists.newArrayList();
        if (!StringUtils.isBlank(userMobile)) {
            userMobile = userMobile.trim();
            if (MobileUtil.illegal(userMobile)) {
                return NewResponseUtil.makeError(AdminErrorCode.MOBILE_FORMAT_ERROR);
            }

            MobileUserIdModel userIdByMobile = simpleUserAccountServiceClient.getUserIdByMobile(userMobile);

            if (userIdByMobile == null) {
                return NewResponseUtil.makeError(AdminErrorCode.USER_MOBILE_NOT_EXISTS);
            }

            long userId = userIdByMobile.getUserId();
            List<CrowdfundingInfo> crowdfundingInfoList = crowdfundingDelegate.getCrowdfundingInfoByUserId(userId);
            if (CollectionUtils.isEmpty(crowdfundingInfoList)) {
                return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
            }
            List<Integer> cfIds = crowdfundingInfoList.stream().map(p -> p.getId()).collect(Collectors.toList());
            crowdfundingIds.addAll(cfIds);
            cfIds.clear();

            if (crowdfundingId != null) {
                CrowdfundingInfo crowdfundingInfo = crowdfundingDelegate.getFundingInfoById(crowdfundingId);
                if (crowdfundingInfo == null) {
                    log.info("crowdfundingId {} is error", crowdfundingId);
                    return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
                }
                if (!crowdfundingIds.contains(crowdfundingId)) {
                    log.info("crowdfundingId {} userMobile {} is not consistency", crowdfundingId, userMobile);
                    return NewResponseUtil.makeSuccess(null);
                }
            }
        } else {
            if (crowdfundingId != null) {
                crowdfundingIds.add(crowdfundingId);
            }
        }
        Response errorResponse = checkErrorResponse(progressStatus, drawCashStartTime, drawCashEndTime,
                                            fundUseSubmitStartTime, fundUseSubmitEndTime, current);
        if (errorResponse != null) {
            return errorResponse;
        }

        List<AdminCfFundUseAuditInfo> adminCfFundUseAuditInfoList = Lists.newArrayList();
        int selectRecords = 0;
        selectRecords = getSelectRecordsByConditions(crowdfundingIds, progressStatus, current, pageSize,
                                        drawCashStartTime, drawCashEndTime, fundUseSubmitStartTime,
                                        fundUseSubmitEndTime, adminCfFundUseAuditInfoList, selectRecords,hasReport);

        if (CollectionUtils.isEmpty(adminCfFundUseAuditInfoList)) {
            log.info("select results is empty");
            return NewResponseUtil.makeSuccess(null);
        }
        Map<String, Object> result = Maps.newHashMap();

        Pagination pagination = new Pagination(current, pageSize, selectRecords);
        result.put("pagination", pagination);

        List<AdminCfFundUseAuditInfo> showFundUseAuditInfos = fillAdminCfFundUseAuditInfoList(adminCfFundUseAuditInfoList, hasReport);
        result.put("fundUseAuditInfoList", showFundUseAuditInfos);
        return NewResponseUtil.makeSuccess(result);
    }

    /**
     *
     * @param progressStatus 0 待审核 1 审核通过 2 被拒绝 3 默认值 拉取所有状态
     * @param drawCashStartTime 打款开始时间
     * @param drawCashEndTime 打款结束时间
     * @param fundUseSubmitStartTime 资金用途 提交 开始时间
     * @param fundUseSubmitEndTime 资金用途 提交结束时间
     * @param current 当前页数
     * @return 存在错误字段参数 则返回 对应 错误响应 反之 不存在 则 返回 null
     */
    private Response checkErrorResponse(Integer progressStatus, String drawCashStartTime, String drawCashEndTime,
                                 String fundUseSubmitStartTime, String fundUseSubmitEndTime, Integer current) {
        if (current <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (progressStatus != null && progressStatus < 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR_VALID_DATA_RANGE);
        }
        AdminErrorCode cfErrorCodeByDrawCash = getCfErrorCode(drawCashStartTime, drawCashEndTime);
        if (cfErrorCodeByDrawCash.getCode() != AdminErrorCode.SUCCESS.getCode()) {
            return NewResponseUtil.makeError(cfErrorCodeByDrawCash);
        }
        AdminErrorCode cfErrorCodeByFundUseSubmitTime = getCfErrorCode(fundUseSubmitStartTime, fundUseSubmitEndTime);

        if (cfErrorCodeByFundUseSubmitTime.getCode() != AdminErrorCode.SUCCESS.getCode()) {
            return NewResponseUtil.makeError(cfErrorCodeByFundUseSubmitTime);
        }
        return null;
    }

    /**
     *
     * @param crowdfundingIds 筹款id 列表
     * @param progressStatus 0 待审核 1 审核通过 2 被拒绝 3 默认值 拉取所有状态
     * @param current 当前页数
     * @param pageSize 页码大小
     * @param drawCashStartDate 打款开始时间
     * @param drawCashEndDate 打款结束时间
     * @param fundUseSubmitStartDate 资金用途创建开始时间
     * @param fundUseSubmitEndDate 资金用途创建 结束时间
     * @param adminCfFundUseAuditInfoList 资金用途审核信息列表 根据多条件连接查询获的
     * @param selectRecords
     * @param hasReport null为全部
     * @return 返回查询记录总数
     */
    private int getSelectRecordsByConditions(List<Integer> crowdfundingIds, Integer progressStatus, Integer current, Integer pageSize,
                                             String drawCashStartDate, String drawCashEndDate, String fundUseSubmitStartDate, String fundUseSubmitEndDate,
                                             List<AdminCfFundUseAuditInfo> adminCfFundUseAuditInfoList, int selectRecords,
                                             String hasReport) {
        if (CollectionUtils.isEmpty(crowdfundingIds)) {
            crowdfundingIds = null;
        }
        if (progressStatus == ALL_STATUS) {
            progressStatus = null;
        }
        List<AdminCfFundUseAuditInfo> list = adminCrowdfundingInfoBiz.getFundUseAuditInfoByUnionSelect(crowdfundingIds,
                progressStatus, drawCashStartDate, drawCashEndDate, fundUseSubmitStartDate, fundUseSubmitEndDate,
                (current - 1) * 10, pageSize, hasReport);
        adminCfFundUseAuditInfoList.addAll(list);
        selectRecords = adminCrowdfundingInfoBiz.selectRecordNums(crowdfundingIds, progressStatus, drawCashStartDate,
                                                                    drawCashEndDate, fundUseSubmitStartDate, fundUseSubmitEndDate,hasReport);


        return selectRecords;
    }

    @ApiOperation("获取审核被拒绝原因")
    @PostMapping("/get-rejected-reason")
    @RequiresPermission("progress-audit:get-rejected-reason")
    public Response getAuditRejectedReason(@RequestParam(required = true, name = "progressId") Integer progressId) {
        if (progressId < 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR_VALID_DATA_RANGE);
        }
        AdminCrowdfundingProgress progress = adminCrowdFundingProgressBiz.getProgressById(progressId);
        if (progress == null) {
            log.info("progressId {} is error", progressId);
            return NewResponseUtil.makeError(AdminErrorCode.CF_PROGRESS_NOT_FOUND);
        }
        if (progress.getStatus() != AUDIT_REJECTED_STATUS) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_STATUS_NOT_UNIFY);
        }
        String auditRejectedReason = progress.getFundUseRejectedReason();
        auditRejectedReason = auditRejectedReason.trim();
        log.info("progressId {}  auditRejectedReason is {}", progressId, auditRejectedReason);
        return NewResponseUtil.makeSuccess(auditRejectedReason);
    }

    @ApiOperation("资金审核未通过")
    @PostMapping("/fund-audit-rejected")
    @RequiresPermission("progress-audit:fund-audit-rejected")
    public Response auditRejected(@RequestParam(required = true, name = "progressId") Integer progressId,
                                  @RequestParam(required = true, name = "fundAuditRejectedReason") String fundAuditRejectedReason) {
        Response result = getFundAuditRejectedResponse(progressId, fundAuditRejectedReason);
        if (result != null) {
            return result;
        }

        adminCfFundUseAuditDao.updateAuditStatusRejected(progressId, fundAuditRejectedReason);
        AdminCrowdfundingProgress progress = adminCrowdFundingProgressBiz.getProgressById(progressId);
        CrowdfundingInfo crowdfundingInfo = crowdfundingDelegate.getFundingInfoById(progress.getCrowdfundingId());

        eventCenterPublishService.sendFundAuditRejected(crowdfundingInfo, fundAuditRejectedReason, progressId);
        log.info("progress Id {} rejected Reason {}", progressId, fundAuditRejectedReason);

        commonOperationRecordClient.create()
                .buildBasicPlatform(crowdfundingInfo.getId(), ContextUtil.getAdminUserId(), OperationActionTypeEnum.REFUSE_FUND_FOR_USE_APPROVE)
                .save();
        return NewResponseUtil.makeSuccess(null, "进展状态更新成未通过");
    }

    /**
     *
     * @param progressId 资金进展Id
     * @param fundAuditRejectedReason 进展被拒绝原因
     * @return 资金进展id, 资金被拒绝原因 存在错误 返回 驳回响应 反之为 null
     */
    private Response getFundAuditRejectedResponse(Integer progressId, String fundAuditRejectedReason) {
        if (progressId == null || progressId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR_VALID_DATA_RANGE);
        }
        if (StringUtils.isBlank(fundAuditRejectedReason)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        AdminCrowdfundingProgress progress = adminCrowdFundingProgressBiz.getProgressById(progressId);
        if (progress == null) {
            log.info("progressId {} is error", progressId);
            return NewResponseUtil.makeError(AdminErrorCode.CF_PROGRESS_NOT_FOUND);
        }
        if (progress.getStatus() == AUDIT_REJECTED_STATUS) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_STATUS_NOT_UNIFY);
        }
        return null;
    }

    @ApiOperation("资金用途审核通过")
    @PostMapping("/fund-audit-pass")
    @RequiresPermission("progress-audit:fund-audit-pass")
    public Response auditPass(@RequestParam(required = true, name = "progressId") Integer progressId,
                              @RequestParam(required = true, name = "infoUuid") String infoUuid) {
        if (progressId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR_VALID_DATA_RANGE);
        }
        AdminCrowdfundingProgress adminCrowdfundingProgress = adminCrowdFundingProgressBiz.getProgressById(progressId);
        if (adminCrowdfundingProgress == null) {
            log.info("progressId {} is error", progressId);
            return NewResponseUtil.makeError(AdminErrorCode.CF_PROGRESS_NOT_FOUND);
        }
        CrowdfundingInfo crowdfundingInfo = crowdfundingDelegate.getCrowdfundingInfoByInfoId(infoUuid);
        if (crowdfundingInfo == null) {
            log.info("crowdfundingInfo infoUuid {}, progressId {}", infoUuid, progressId);
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }
        if (!adminCrowdfundingProgress.getCrowdfundingId().equals(crowdfundingInfo.getId())) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (adminCrowdfundingProgress.getStatus() == AUDIT_SUCCESS) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_STATUS_NOT_UNIFY);
        }
        int updateResult = adminCfFundUseAuditDao.updateAuditPass(progressId);
        // 将填充后的资金进展progress 入 crowdfunding_progress表
        CrowdFundingProgress crowdFundingProgress = fillCrowdFundingProgress(adminCrowdfundingProgress);
        int insertResult = adminCrowdFundingProgressBiz.insertInCrowdfundingProgress(crowdFundingProgress);
        if (updateResult > 0 && producer != null) {
            MessageResult messageResult = producer.send(new Message(MQTopicCons.CF,
                                                         MQTagCons.CF_PUBLISH_PROGRESS,
                                                    MQTagCons.CF_PUBLISH_PROGRESS + "_" + crowdFundingProgress.getId(),
                                                        crowdFundingProgress));
            this.crowdfundingDelegate.updateTimes(infoUuid, CfTaskEnum.Rule.PUBLISH_PROCESS);
            eventCenterPublishService.sendFundAuditPass(crowdfundingInfo, crowdFundingProgress.getId());
            log.info("{}", messageResult.toString());
        }
        log.info("progressId : {} update success, insert {} success", progressId, insertResult);
        return NewResponseUtil.makeSuccess(null, "资金用途审核通过");
    }


    /**
     *
     * @param adminCrowdfundingProgressVo 需要入crowdfunding_progress表的 记录
     * @return 填充 资金进展信息 填充后待入库
     */
    private CrowdFundingProgress fillCrowdFundingProgress(AdminCrowdfundingProgress adminCrowdfundingProgressVo) {
        CrowdFundingProgress crowdFundingProgress = new CrowdFundingProgress();
        crowdFundingProgress.setUserId(adminCrowdfundingProgressVo.getUserId());
        crowdFundingProgress.setImageUrls(adminCrowdfundingProgressVo.getImageUrls());
        crowdFundingProgress.setContent(adminCrowdfundingProgressVo.getContent());
        crowdFundingProgress.setType(CrowdFundingProgressType.PROGRESS_NEEDS_AUDIT.value());
        crowdFundingProgress.setTitle(adminCrowdfundingProgressVo.getTitle());
        crowdFundingProgress.setActivityId(adminCrowdfundingProgressVo.getCrowdfundingId());
        return crowdFundingProgress;
    }

    /**
     * 填充 资金用户审核信息
     * @param adminFundUseAuditInfoList 资金用途信息审核列表
     * @param hasReport
     * @return
     */
    private List<AdminCfFundUseAuditInfo> fillAdminCfFundUseAuditInfoList(List<AdminCfFundUseAuditInfo> adminFundUseAuditInfoList, String hasReport) {
        List<AdminCfFundUseAuditInfo> adminCfFundUseAuditInfoList = Lists.newArrayList();
        Map<Integer,List<CrowdfundingReport>>  infoIdReportMap = getReport(adminFundUseAuditInfoList);
        Map<Integer, CrowdfundingInfo> crowdfundingInfoMap = adminCrowdfundingInfoBiz.getMapByIds(adminFundUseAuditInfoList
                .stream().map(AdminCfFundUseAuditInfo::getCrowdfundingId).collect(Collectors.toList()));
        for (AdminCfFundUseAuditInfo adminFundUseAuditInfo : adminFundUseAuditInfoList) {
            CrowdfundingInfo crowdfundingInfo = crowdfundingInfoMap.get(adminFundUseAuditInfo.getCrowdfundingId());
            //可能存在null数据情况 排除掉
            if (crowdfundingInfo == null) {
                log.info("未匹配到对应的案例 getCrowdfundingId:{}",adminFundUseAuditInfo.getCrowdfundingId());
                continue;
            }
            adminFundUseAuditInfo.setUserId(crowdfundingInfo.getUserId());
            adminFundUseAuditInfo.setInfoUuid(crowdfundingInfo.getInfoId());

            if (adminFundUseAuditInfo.getFundUseContent() == null) {
                adminFundUseAuditInfo.setFundUseContent("");
            }
            if (adminFundUseAuditInfo.getFundUseImageMaterial() == null) {
                adminFundUseAuditInfo.setFundUseImageMaterial("");
            }
            adminFundUseAuditInfo.setUserMobile("***");
            Integer crowdfundingId = adminFundUseAuditInfo.getCrowdfundingId();
            CrowdfundingAuthor crowdfundingAuthor = adminCrowdfundingAuthorBiz.get(crowdfundingId);
            String patientName = "";
            if (crowdfundingAuthor == null || StringUtils.isBlank(crowdfundingAuthor.getName())) {
                patientName = "暂无患者姓名";
            } else {
                patientName = crowdfundingAuthor.getName();
            }
            patientName = StringUtils.trimToEmpty(patientName);

            adminFundUseAuditInfo.setPatientName(patientName);
            List<CrowdfundingReport> crowdfundingReportList = infoIdReportMap.get(adminFundUseAuditInfo.getCrowdfundingId());
            adminFundUseAuditInfo.setHasReport(CollectionUtils.isNotEmpty(crowdfundingReportList));

            // 解决查询列表与条目展示不一致的问题 再过滤一遍
            if (StringUtils.isNotBlank(hasReport)) {
                boolean hasR = StringUtils.equals(hasReport, "true");
                if (adminFundUseAuditInfo.isHasReport() == hasR) {
                    adminCfFundUseAuditInfoList.add(adminFundUseAuditInfo);
                }
            } else {
                adminCfFundUseAuditInfoList.add(adminFundUseAuditInfo);
            }
        }
        watermarkService.fillFundUseAuditWatermark(adminCfFundUseAuditInfoList);
        return adminCfFundUseAuditInfoList;
    }

    /**
     * 举报
     * @param adminFundUseAuditInfoList
     * @return
     */
    private Map<Integer,List<CrowdfundingReport>> getReport(List<AdminCfFundUseAuditInfo> adminFundUseAuditInfoList){
        if (CollectionUtils.isEmpty(adminFundUseAuditInfoList)) {
            return Maps.newHashMap();
        }
        List<CrowdfundingReport> crowdfundingReportList = adminCrowdfundingReportBiz.getByInfoIdsV2(adminFundUseAuditInfoList
                .stream().map(AdminCfFundUseAuditInfo::getCrowdfundingId).collect(Collectors.toList()));
        /**
         * 按照hr的要求将 未处理的举报视为无效举报，不统计
         */
        crowdfundingReportList = crowdfundingReportList.stream()
                .filter(item -> item.getDealStatus() == CaseReportDealStatus.HANDLEING.getValue()
                        || item.getDealStatus() == CaseReportDealStatus.FINISH.getValue())
                .collect(Collectors.toList());
        return crowdfundingReportList.stream().collect(Collectors.groupingBy(CrowdfundingReport::getActivityId));
    }
    /**
     *
     * @param startTime 日期筛选条件 开始时间
     * @param endTime 日期筛选条件 结束时间
     * @return AdminErrorCode 条件格式错误 返回 错误码
     */
    private AdminErrorCode getCfErrorCode(String startTime, String endTime) {
        if (startTime != null && endTime != null) {
            if (startTime.compareTo(endTime) > 0) {
                return  AdminErrorCode.SYSTEM_PARAM_ERROR;
            }
        }
        if (startTime != null && endTime == null) {
            return AdminErrorCode.SYSTEM_PARAM_ERROR;
        }
        if (startTime == null && endTime != null) {
            return AdminErrorCode.SYSTEM_PARAM_ERROR;
        }
        return AdminErrorCode.SUCCESS;
    }

    private static class Pagination {
        int current;
        int pageSize;
        int total;

        public Pagination(int current, int pageSize, int total) {
            this.current = current;
            this.pageSize = pageSize;
            this.total = total;
        }

        public Pagination() {

        }

        public int getCurrent() {
            return current;
        }

        public void setCurrent(int current) {
            this.current = current;
        }

        public int getPageSize() {
            return pageSize;
        }

        public void setPageSize(int pageSize) {
            this.pageSize = pageSize;
        }

        public int getTotal() {
            return total;
        }

        public void setTotal(int total) {
            this.total = total;
        }
    }

}
