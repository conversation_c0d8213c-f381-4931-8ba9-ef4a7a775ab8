package com.shuidihuzhu.cf.admin.controller.api.workorder;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.crowdfunding.CfQuestionnaireBiz;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.admin.CfQuestionnaire;
import com.shuidihuzhu.cf.model.admin.WenjuanWorkOrderVO;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.workorder.CfWenjuanWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.HandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderListParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.msg.util.MobileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2019/12/12
 */
@RestController
@RequestMapping(path="/admin/workorder/wenjuan")
@Slf4j
public class WenjuanWorkOrderController {

    @Autowired
    private CfWenjuanWorkOrderClient workOrderClient;

    @Autowired
    private CfQuestionnaireBiz questionnaireBiz;

    @Autowired
    private ShuidiCipher shuidiCipher;

    @RequiresPermission("wenjuan:handle")
    @RequestMapping(path = "hanlde-wenjuan", method = RequestMethod.POST)
    public Response handleWenjuan(@RequestParam("param") String param,
                                  @RequestParam("userId") int userId,
                                  @RequestParam("recordId") long recordId,
                                  @RequestParam(value = "comment",required = false,defaultValue = "") String comment) {

        HandleOrderParam p = JSON.parseObject(param, HandleOrderParam.class);//已检查过
        //保存处理人和备注
        questionnaireBiz.saveOperatorIdAndComment(recordId,userId,comment);

        Response response = workOrderClient.handleWenjuan(p);

        return response;
    }

    @RequiresPermission("wenjuan:orderlist")
    @RequestMapping(path = "wenjuan-orderlist",method = RequestMethod.POST)
    Response wenjuanOrderlist(@RequestParam("param") String param) {

        WorkOrderListParam p = JSON.parseObject(param, WorkOrderListParam.class);//已检查过

        Response<PageResult<WorkOrderVO>> response = workOrderClient.wenjuanOrderlist(p);

        if (response == null || response.notOk() || response.getData() == null) {
            return response;
        }

        PageResult<WorkOrderVO> pageResult = response.getData();

        List<WorkOrderVO> list = pageResult.getPageList();

        if (CollectionUtils.isEmpty(list)){
            return response;
        }

        List<Long> recordIds = list.stream().map(r->Long.valueOf(r.getRecordId())).collect(Collectors.toList());

        List<CfQuestionnaire> questionnaireList = questionnaireBiz.getByIds(recordIds);

        Map<Long,Long> map = questionnaireList.stream().collect(Collectors.toMap(CfQuestionnaire::getId,CfQuestionnaire::getRecordId,(o1, o2)->o2));


        List<WenjuanWorkOrderVO> wenjuanWorkOrderVOList = list.stream().map(r->{
            String mobile = r.getMobile();
            mobile = MobileUtil.mask(shuidiCipher.decrypt(mobile));
            r.setMobile(mobile);

            WenjuanWorkOrderVO  vo = new WenjuanWorkOrderVO();
            BeanUtils.copyProperties(r, vo);
            vo.setPreId(map.get(Optional.ofNullable(r.getRecordId()).map(Long::valueOf).orElse(0L)));

            return vo;
        }).collect(Collectors.toList());

        PageResult<WenjuanWorkOrderVO> result = new PageResult<>();
        result.setHasNext(pageResult.isHasNext());
        result.setPageList(wenjuanWorkOrderVOList);

        return NewResponseUtil.makeSuccess(result);

    }

    @RequiresPermission("wenjuan:check")
    @RequestMapping(path = "wenjuan-check",method = RequestMethod.POST)
    Response wenjuanCheck(@RequestParam("workOrderId") long workOrderId,
                          @RequestParam("recordId") long recordId) {

        if (recordId <= 0 || workOrderId <= 0 ){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        CfQuestionnaire cfQuestionnaire = questionnaireBiz.getById(recordId);

        //如果已经填写 自动关闭
        if (cfQuestionnaire != null && cfQuestionnaire.getQStatus() == CfQuestionnaire.q_status_submit){
            HandleOrderParam handleOrderParam = new HandleOrderParam();
            handleOrderParam.setWorkOrderId(workOrderId);
            handleOrderParam.setOrderType(WorkOrderType.wenjuan.getType());
            handleOrderParam.setHandleResult(HandleResultEnum.exception_done.getType());
            workOrderClient.handleWenjuan(handleOrderParam);
            return NewResponseUtil.makeSuccess(true);
        }

        return NewResponseUtil.makeSuccess(false);
    }

}
