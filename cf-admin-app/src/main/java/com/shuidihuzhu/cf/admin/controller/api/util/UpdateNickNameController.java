package com.shuidihuzhu.cf.admin.controller.api.util;

import com.shuidi.weixin.mp.bean.result.WxMpUser;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.model.UserThirdModel;

import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.delegate.service.UserThirdServiceBiz;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.wx.biz.ShuidiWxService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

/**
 * Created by chao on 2017/10/17.
 */
@Controller
@RequestMapping(path = "/admin/cf/")
public class UpdateNickNameController {

	@Resource
	private UserInfoServiceBiz userInfoServiceBiz;

	@Resource
	private UserThirdServiceBiz userThirdServiceBiz;

	@Autowired
	private ShuidiWxService shuidiWxService;

	@RequiresPermission("nickname:refresh")
	@RequestMapping(path = "refresh-nickname", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	@ResponseBody
	public Response refreshNickname(@RequestParam(required = false, defaultValue = "") String mobile) {
		int userId = ContextUtil.getAdminUserId();
		if (StringUtils.isEmpty(mobile) && userId <= 0) {
			return NewResponseUtil.makeSuccess(null);
		}
		UserInfoModel baseUserInfoModel = null;
		if (!StringUtils.isEmpty(mobile)) {
			baseUserInfoModel = userInfoServiceBiz.getUserInfoByMobile(mobile);
		} else if (userId > 0) {
			baseUserInfoModel = userInfoServiceBiz.getUserInfoByUserId(userId);
		}
		if (null != baseUserInfoModel) {
			doUpdate(1, baseUserInfoModel);
			doUpdate(899, baseUserInfoModel);
			doUpdate(115, baseUserInfoModel);
			doUpdate(116, baseUserInfoModel);
		}
		return NewResponseUtil.makeSuccess(null,"success");
	}

	private void doUpdate(int thirdType, UserInfoModel baseUserInfoModel) {
		UserThirdModel thirdModel =
				userThirdServiceBiz.getThirdModelWithUserId(baseUserInfoModel.getUserId(),
				                                         thirdType);
		if (thirdModel == null) {
			return;
		}
		WxMpUser wxMpUser = shuidiWxService.getWxUserByOpenId(thirdModel.getOpenId(), thirdType);
		if (null != wxMpUser) {
			boolean updateNickName = false;
			if (!StringUtils.isEmpty(wxMpUser.getNickname()) &&
			    !baseUserInfoModel.getNickname().equals(wxMpUser.getNickname())) {
				updateNickName = true;
			}
			if (!StringUtils.isEmpty(wxMpUser.getHeadImgUrl()) &&
			    !baseUserInfoModel.getHeadImgUrl().equals(wxMpUser.getHeadImgUrl())) {
				updateNickName = true;
			}
			if (updateNickName) {
				userThirdServiceBiz.updateUserThird(wxMpUser.getOpenId(),
				                                 wxMpUser.getUnionId(),
				                                 baseUserInfoModel.getUserId(),
				                                 wxMpUser.getNickname(),
				                                 wxMpUser.getHeadImgUrl());
				userInfoServiceBiz.updateNicknameAndHeadImgUrl(baseUserInfoModel.getUserId(),
				                              wxMpUser.getNickname(),
				                              wxMpUser.getHeadImgUrl());
			}
		}
	}
}
