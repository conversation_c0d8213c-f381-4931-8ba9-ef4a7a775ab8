package com.shuidihuzhu.cf.admin.controller.api.util;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.model.admin.WashPaymentMethod;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/24  3:21 下午
 */
@Slf4j
public class ExcelListener extends AnalysisEventListener<Object> {

    @Getter
    private List<Object> list = Lists.newArrayList();

    @Override
    public void invoke(Object data, AnalysisContext context) {
        list.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {

    }
}
