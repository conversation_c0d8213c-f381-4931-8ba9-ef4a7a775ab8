package com.shuidihuzhu.cf.admin.mq.ai;

import com.shuidihuzhu.alps.feign.config.OceanApiMqConfig;
import com.shuidihuzhu.alps.feign.fs.CalloutRecordMq;
import com.shuidihuzhu.cf.service.workorder.cailiao.CaiAuditRejectAICallService;
import com.shuidihuzhu.client.model.event.InfoApproveEvent;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Author: wangpeng
 * @Date: 2022/7/12 15:59
 * @Description:
 */
@Slf4j
@Service
@RocketMQListener(id = "AI_CTI", tags = "ai_meterial_review", topic = "AI_CTI")
public class CfApproveAuditAICallConsumer implements MessageListener<CalloutRecordMq> {

    @Resource
    private CaiAuditRejectAICallService caiAuditRejectAICallService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CalloutRecordMq> mqMessage) {
        log.info("CfApproveAuditAICallConsumer is begin {}", mqMessage);
        if (Objects.isNull(mqMessage.getPayload())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        caiAuditRejectAICallService.updateCallTimeAndState(mqMessage.getPayload());
        caiAuditRejectAICallService.aiAgainCall(mqMessage.getPayload());
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
