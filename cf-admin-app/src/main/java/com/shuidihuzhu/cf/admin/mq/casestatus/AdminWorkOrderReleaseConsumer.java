package com.shuidihuzhu.cf.admin.mq.casestatus;

import com.shuidihuzhu.cf.admin.mq.common.BaseMessageConsumer;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.service.approve.ApproveControlService;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.model.event.WorkOrderReleaseEvent;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 工单回收事件
 * <AUTHOR>
 */
@Service
@Slf4j
@RocketMQListener(id = CfClientMQTagCons.WORK_ORDER_RELEASE,
        tags = CfClientMQTagCons.WORK_ORDER_RELEASE,
        topic = MQTopicCons.CF)
public class AdminWorkOrderReleaseConsumer extends BaseMessageConsumer<WorkOrderReleaseEvent>
        implements MessageListener<WorkOrderReleaseEvent> {

    @Autowired
    private ApproveControlService approveControlService;

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @Override
    protected boolean handle(ConsumerMessage<WorkOrderReleaseEvent> consumerMessage) {
        WorkOrderReleaseEvent workOrderReleaseEvent = consumerMessage.getPayload();
        long workOrderId = workOrderReleaseEvent.getWorkOrderId();

        Response<WorkOrderVO> r = cfWorkOrderClient.getWorkOrderById(workOrderId);
        if (r.notOk()) {
            return false;
        }
        WorkOrderVO workOrderVO = r.getData();
        approveControlService.releaseControlByWorkOrder(workOrderVO);

        return true;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
