package com.shuidihuzhu.cf.admin.mq.amount;

import com.shuidihuzhu.cf.biz.amount.AmountReasonableBiz;
import com.shuidihuzhu.cf.client.adminpure.enums.AmountReasonableTaskStatus;
import com.shuidihuzhu.cf.client.adminpure.model.amount.CfAmountReasonableTask;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.enhancer.mq.DelayBaseMqConsumer;
import com.shuidihuzhu.cf.service.amount.AmountReasonableService;
import com.shuidihuzhu.cf.vo.amount.AmountReasonableTaskMqVo;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @Author: wangpeng
 * @Date: 2022/8/26 15:36
 * @Description:
 */
@Service
@RocketMQListener(id = "cf-admin-" + com.shuidihuzhu.cf.constants.admin.MQTagCons.CF_AMOUNT_REASONABLE_TASK,
        group = "cf-admin-" + com.shuidihuzhu.cf.constants.admin.MQTagCons.CF_AMOUNT_REASONABLE_TASK,
        tags = MQTagCons.CF_AMOUNT_REASONABLE_TASK,
        topic = MQTopicCons.CF)
@Slf4j
public class CfAmountReasonableTaskConsumer extends DelayBaseMqConsumer<AmountReasonableTaskMqVo> implements MessageListener<AmountReasonableTaskMqVo> {

    @Resource
    private AmountReasonableBiz amountReasonableBiz;
    @Resource
    private AmountReasonableService amountReasonableService;

    @Autowired
    private Producer producer;

    @Override
    protected boolean handle(ConsumerMessage<AmountReasonableTaskMqVo> mqMessage) {
        log.info("CfAmountReasonableTaskConsumer is begin {}", mqMessage);
        AmountReasonableTaskMqVo reasonableTaskMqVo = mqMessage.getPayload();
        if (Objects.isNull(reasonableTaskMqVo) || StringUtils.isEmpty(reasonableTaskMqVo.getTaskInfoId()) || reasonableTaskMqVo.getCaseId() == 0) {
            return true;
        }
        // 兼容历史消息
        if (reasonableTaskMqVo.isDelayContinue()) {
            reasonableTaskMqVo.setDelayContinue(false);
            reasonableTaskMqVo.setDelayCount(90, TimeUnit.DAYS);
            Message message = Message
                    .ofDelay(MQTopicCons.CF, MQTagCons.CF_AMOUNT_REASONABLE_TASK, MQTagCons.CF_AMOUNT_REASONABLE_TASK + "_" + reasonableTaskMqVo.getTaskWorkOrderId(), reasonableTaskMqVo,
                            reasonableTaskMqVo.nextDelaySeconds());
            producer.send(message);
            log.info("CfAmountReasonableTaskConsumer continue delay {}", reasonableTaskMqVo);
            return true;
        }

        List<CfAmountReasonableTask> amountReasonableTaskList = amountReasonableBiz.getByCaseIdAndTaskType(reasonableTaskMqVo.getCaseId(), reasonableTaskMqVo.getTaskType());
        if (amountReasonableTaskList.size() >= 4) {
            return true;
        }
        CfAmountReasonableTask cfAmountReasonableTask = amountReasonableBiz.getByTaskInfoId(reasonableTaskMqVo.getTaskInfoId());
        if (Objects.isNull(cfAmountReasonableTask)) {
            return true;
        }
        if (reasonableTaskMqVo.getDelayReason() == 1 && cfAmountReasonableTask.getTaskStatus() != AmountReasonableTaskStatus.NO_SUBMIT.getCode()) {
            return true;
        }

        amountReasonableService.firstIssueTask(reasonableTaskMqVo.getCaseId(), reasonableTaskMqVo.getTaskType());
        return true;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
