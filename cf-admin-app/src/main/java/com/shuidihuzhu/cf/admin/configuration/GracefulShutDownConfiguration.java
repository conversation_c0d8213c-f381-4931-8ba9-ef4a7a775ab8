package com.shuidihuzhu.cf.admin.configuration;

import com.shuidihuzhu.cf.service.shutdown.DrawCashShutdown;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @time 2018/11/27 上午11:46
 * @desc
 */
@Configuration
public class GracefulShutDownConfiguration {

    @Bean("drawCashShutdown")
    public DrawCashShutdown drawCashShutdown(){
        return new DrawCashShutdown();
    }
}
