package com.shuidihuzhu.cf.admin.controller.api.workorder;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderRecordClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderTypeFeignClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderRecord;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderTypeRecord;
import com.shuidihuzhu.client.cf.workorder.model.vo.WorkOrderRecordVO;
import com.shuidihuzhu.common.web.model.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Api("工单记录")
@Slf4j
@RestController
@RequestMapping(path="/admin/workorder/record")
public class WorkOrderRecordController {


    @Autowired
    private CfWorkOrderRecordClient cfWorkOrderRecordClient;

    @Autowired
    private CfWorkOrderTypeFeignClient cfWorkOrderTypeFeignClient;

    @RequiresPermission("record:list-by-work-order-id")
    @ApiOperation(value = "根据工单id获取工单记录", notes = "倒序")
    @PostMapping("list-by-work-order-id")
    public Response<List<WorkOrderRecordVO>> listByWorkOrderId(@RequestParam long workOrderId){
        return cfWorkOrderRecordClient.listByWorkOrderId(workOrderId);
    }


}
