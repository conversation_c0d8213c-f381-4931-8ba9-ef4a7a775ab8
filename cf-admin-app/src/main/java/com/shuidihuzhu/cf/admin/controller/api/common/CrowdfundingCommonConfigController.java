package com.shuidihuzhu.cf.admin.controller.api.common;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.apipure.feign.CrowdfundingCommonConfigClient;
import com.shuidihuzhu.cf.client.apipure.model.common.CrowdfundingCommonConfigParam;
import com.shuidihuzhu.cf.client.apipure.model.common.CrowdfundingCommonConfigVo;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * @Author: wangpeng
 * @Date: 2021/6/9 12:05
 * @Description:
 */
@RestController
@RequestMapping(path = "/admin/cf/common/config")
public class CrowdfundingCommonConfigController {

    @Autowired
    private CrowdfundingCommonConfigClient crowdfundingCommonConfigClient;
    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;

    @PostMapping("get")
    public Response<CrowdfundingCommonConfigVo> get(@RequestParam("caseId") int caseId) {
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if (Objects.isNull(crowdfundingInfo)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingCommonConfigParam commonConfigParam = CrowdfundingCommonConfigParam.builder()
                .infoUuid(crowdfundingInfo.getInfoId())
                .build();
        OperationResult<CrowdfundingCommonConfigVo> commonConfig = crowdfundingCommonConfigClient.getCommonConfig(commonConfigParam);
        if (Objects.isNull(commonConfig) || commonConfig.isFail() || Objects.isNull(commonConfig.getData())) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        return NewResponseUtil.makeSuccess(commonConfig.getData());
    }
}
