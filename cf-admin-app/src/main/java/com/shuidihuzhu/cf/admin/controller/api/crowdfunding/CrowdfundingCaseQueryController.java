package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingCityBiz;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.admin.util.AdminDateUtil;
import com.shuidihuzhu.cf.admin.util.BeanTrimUtil;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoSearchBiz;
import com.shuidihuzhu.cf.delegate.finance.IFinanceDelegate;
import com.shuidihuzhu.cf.enums.admin.stats.UsageFreqStatsBizTypeEnum;
import com.shuidihuzhu.cf.facade.CaseSearchFacade;
import com.shuidihuzhu.cf.model.crowdfunding.CfCapitalAccount;
import com.shuidihuzhu.cf.model.crowdfunding.vo.*;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cf.service.admin.stats.AdminUsageFreqStatsService;
import com.shuidihuzhu.client.cf.search.model.enums.ReportTypeEnum;
import com.shuidihuzhu.client.pf.rpc.client.v1.PFCityClient;
import com.shuidihuzhu.common.web.constants.CrowdfundingCons;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.admin.PageUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by wuxinlong on 7/11/16.
 */
@Slf4j
@RestController
@RequestMapping(path = "/admin/crowdfunding/case")
public class CrowdfundingCaseQueryController {

    @Autowired
    private PFCityClient pfCityClient;

    @Autowired
    private AdminUsageFreqStatsService adminUsageFreqStatsService;

    @Autowired
    private CaseSearchFacade caseSearchFacade;

    @Autowired
    private AdminCrowdfundingCityBiz adminCrowdfundingCityBiz;

    private static final List<String> caseSearchFieldNames;

    static {
        caseSearchFieldNames = Lists.newArrayList();
        for (Field declaredField : CaseSearchVo.class.getDeclaredFields()) {
            caseSearchFieldNames.add(declaredField.getName());
        }
    }

    /**
     * old: https://wiki.shuiditech.com/pages/viewpage.action?pageId=288851934
     * V1: https://wiki.shuiditech.com/pages/viewpage.action?pageId=510526686
     * @param caseSearchVo
     */
    @RequiresPermission("approve:search")
    @RequestMapping(path = "query-search", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation("案例查询")
    public Response<CaseListResponse> approveSearch(CaseSearchVo caseSearchVo) {
        log.info("CrowdfundingCaseQueryController caseList query:{}", caseSearchVo);
        return caseSearchFacade.approveSearch(caseSearchVo);
    }

    @RequiresPermission("approve:search")
    @RequestMapping(path = "/area/province-all", method = RequestMethod.GET, produces = "application/json;charset=UTF-8")
    @ApiOperation("获取所有省份")
    public Response<List<CityVo>> listAllProvince() {
        return NewResponseUtil.makeSuccess(
                    adminCrowdfundingCityBiz.getProvince().stream().map(city -> {
                    CityVo cityVo = new CityVo();
                    BeanUtils.copyProperties(city, cityVo);
                    return cityVo;
                }).collect(Collectors.toList())
        );
    }

    @RequiresPermission("approve:search")
    @RequestMapping(path = "/area/children-all", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation("根据父级id获取所有子区域")
    public Response<List<CityVo>> listAllChildren(
            @NotNull("父级id必传") @Min(value = 0, message = "父级id不能小于1")
                    @ApiParam("父级id") Integer parentId) {
        return NewResponseUtil.makeSuccess(
                adminCrowdfundingCityBiz.getChildren(parentId).stream().map(city -> {
                    CityVo cityVo = new CityVo();
                    BeanUtils.copyProperties(city, cityVo);
                    return cityVo;
                }).collect(Collectors.toList())
        );
    }

    @RequiresPermission("approve:search")
    @RequestMapping(path = "/query-search/options/report", method = RequestMethod.POST, produces = "application/json;charset=UTF-8", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    @ApiOperation("上报案例搜索查询框使用情况")
    public Response<List<CityVo>> report(@ApiParam(value = "搜索时使用的查询项name", allowableValues = "参考CaseSearchVo字段名称(current和pageSize除外)")
                                                     @NotEmpty(message = "上传内容不能为空") String options) {
        if (StringUtils.isBlank(options)) {
            return NewResponseUtil.makeFail("请不要传空字符串");
        }
        List<String> optionList = Lists.newArrayList(Splitter.on(",").trimResults().omitEmptyStrings().split(options));
        Collection<String> subtract = CollectionUtils.subtract(optionList, caseSearchFieldNames);
        if (CollectionUtils.isEmpty(optionList)) {
            return NewResponseUtil.makeFail("未找到有效的值："+ options);
        }
        if (CollectionUtils.isNotEmpty(subtract)) {
            return NewResponseUtil.makeFail("未知的查询name,"+ JSON.toJSONString(subtract));
        }
        adminUsageFreqStatsService.incrementUsageFreqBatch(optionList, "", UsageFreqStatsBizTypeEnum.CASE_SEARCH);
        return NewResponseUtil.makeSuccess(null);
    }

}
