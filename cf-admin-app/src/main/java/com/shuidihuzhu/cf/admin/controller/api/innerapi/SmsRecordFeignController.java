package com.shuidihuzhu.cf.admin.controller.api.innerapi;

import com.shuidihuzhu.cf.admin.pure.feign.SmsRecordFeignClient;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonRecord;
import com.shuidihuzhu.cf.service.message.SmsRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
public class SmsRecordFeignController implements SmsRecordFeignClient {

    @Autowired
    private SmsRecordService smsRecordService;

    @Override
    public OperationResult<Void> save(int caseId, int operatorId, String mobile, String modelNum, String smsContent) {
        OperationResult<WonRecord> result = smsRecordService.save(caseId, operatorId, mobile, modelNum, smsContent);
        return result.isSuccess() ? OperationResult.success() : OperationResult.failWithMsg(result.getMsg());
    }
}
