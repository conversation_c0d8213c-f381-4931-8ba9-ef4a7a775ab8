package com.shuidihuzhu.cf.admin.util;

import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.util.Date;


/**
 * @author: fengxuan
 * @create 2019-08-23 02:25
 **/
@Slf4j
public class ParamTimeRangeHandler {

    private static final int MAX_RANGE_DAYS = 7;

    public static final DateTimeFormatter FORMATTER = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");

    private ParamTimeRangeHandler() {
    }


    //成对出现,时间跨度小于7天，且使用“yyyy-MM-dd HH:mm:ss”格式
    public static Response reportWorkOrderList(String startTime, String endTime) {
        if (startTime == null && endTime == null) {
            return NewResponseUtil.makeSuccess(null);
        }
        //非成对出现
        if ((startTime != null && endTime == null) || (startTime == null && endTime != null)) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        DateTime startDate = FORMATTER.parseDateTime(startTime);
        DateTime endDate =FORMATTER.parseDateTime(endTime);
        if (startDate.isBefore(endDate.minusDays(7))) {
            return NewResponseUtil.makeFail("约定跟进时间范围大于7天");
        }
        return NewResponseUtil.makeSuccess(null);
    }

    //UGC风控-UGC内容处理-查询
    public static Pair<String, String> handleGetMyNewListV2(int caseId, String title, long commentUserId, String hitWords,
                                                            String startTime, String endTime) {
        if (!getMyNewListV2TNoNeedHandle(caseId, title, commentUserId, hitWords, startTime, endTime)) {
            log.debug("handleGetMyNewListV2 before handle startTime:{}, endTime:{}", startTime, endTime);
            //不符合条件，将时间范围设置为7天内
            DateTime now = DateTime.now();
            startTime = DateUtil.getDate2Str(DateUtil.DATETIME_PATTERN_2, now.minusDays(MAX_RANGE_DAYS).toDate());
            endTime = DateUtil.getDate2Str(DateUtil.DATETIME_PATTERN_2, now.toDate());
            log.debug("handleGetMyNewListV2 after handle startTime:{}, endTime:{}", startTime, endTime);
        }
        return ImmutablePair.of(startTime, endTime);
    }


    //UGC风控-前置审核材料-查询
    public static Pair<Long, Long> handleGetMyFirstAprroveList(Integer caseId, String raiserPhoneNum, long operatorStartDateMills,
                                                               long operatorEndDateMills) {
        if (!getMyFirstAprroveListNoNeedHandle(caseId, raiserPhoneNum, operatorStartDateMills, operatorEndDateMills)) {
            DateTime now = DateTime.now();
            operatorStartDateMills = now.minusDays(MAX_RANGE_DAYS).getMillis();
            operatorEndDateMills = now.getMillis();
        }
        return ImmutablePair.of(operatorStartDateMills, operatorEndDateMills);
    }


    //风控管理-UGC风控-图文处理 & 动态处理
    public static Pair<String, String> handleGetMyOnlyBaseInfoList(Integer caseId, String startTime, String endTime) {
        return handleSingleId(caseId == null ? 0 : caseId, startTime, endTime);
    }


    //风控管理-异常案例处理
    public static Pair<String, String> handleCaseVisitConfig(Integer caseId, String startTime, String endTime) {
        return handleSingleId(caseId == null ? 0 : caseId, startTime, endTime);
    }


    //风控管理-资金用途审核/提交时间默认设置
    public static Pair<String, String> handleGetFieldsTime(Integer caseId, String drawCashStartTime, String drawCashEndTime) {
        return handleSingleId(caseId == null ? 0 : caseId, drawCashStartTime, drawCashEndTime);
    }

    //风控管理-UGC风控-房车审核
    public static Pair<String, String> handGetCreditSupplementInfo(int caseId, String startTime, String endTime) {
        return handleSingleId(caseId, startTime, endTime);
    }

    private static boolean getMyFirstAprroveListNoNeedHandle(Integer caseId, String raiserPhoneNum, long operatorStartDateMills, long operatorEndDateMills) {
        if (caseId != null && caseId > 0) {
            return true;
        }
        if (StringUtils.isNotEmpty(raiserPhoneNum)) {
            return true;
        }
        if (operatorStartDateMills == 0 || operatorEndDateMills == 0) {
            return false;
        }
        return true;
    }


    private static boolean getMyNewListV2TNoNeedHandle(int caseId, String title, long commentUserId, String hitWords,
                                                       String startTime, String endTime) {
        if (caseId > 0) {
            return true;
        }
        if (!StringUtils.isEmpty(title)) {
            return true;
        }
        if (commentUserId > 0) {
            return true;
        }
        if (StringUtils.isNotEmpty(hitWords)) {
            return true;
        }
        //单点均未满足，未设置时间需要设置默认时间
        if (StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) {
            return false;
        }
        return true;
    }


    private static Pair<String, String> handleSingleId(int userId, String startTime, String endTime) {
        boolean timeRangeUnSet = StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime);
        if (timeRangeUnSet && userId <= 0) {
            log.debug("before handle startTime:{}, endTime:{}", startTime, endTime);
            //不符合条件，将时间范围设置为7天内
            DateTime now = DateTime.now();
            startTime = DateUtil.getDate2Str(DateUtil.DATETIME_PATTERN_2, now.minusDays(MAX_RANGE_DAYS).toDate());
            endTime = DateUtil.getDate2Str(DateUtil.DATETIME_PATTERN_2, now.toDate());
            log.debug("after handle startTime:{}, endTime:{}", startTime, endTime);
        }
        return ImmutablePair.of(startTime, endTime);
    }


    public static Response illegalTimeRangeForLong (Long startTime, Long endTime) {
        boolean startTimeSet = startTime != null && startTime > 0;
        boolean endTimeSet = endTime != null && endTime > 0;
        if (startTimeSet && endTimeSet) {
            return illegalTimeRange(DateUtil.getDate2LStr(new Date(startTime)), DateUtil.getDate2LStr(new Date(endTime)));
        }
        return NewResponseUtil.makeSuccess(null);
    }


    //1、startTime endTime成对设置 2、开始时间不能大于结束时间 3、开始时间不能超过今晚24点 4、两个时间差不能超过7天
    public static Response illegalTimeRange(String startTime, String endTime) {
        DateTime beginDate = null;
        DateTime endDate = null;
        //开始时间不能大于结束时间
        if (StringUtils.isNotEmpty(startTime) || StringUtils.isNotEmpty(endTime)) {
            try {
                beginDate = DateTime.parse(startTime, DateTimeFormat.forPattern(DateUtil.DATETIME_PATTERN_2));
                endDate = DateTime.parse(endTime, DateTimeFormat.forPattern(DateUtil.DATETIME_PATTERN_2));
                if (endDate.isAfter(DateTime.now().plusDays(1).withTimeAtStartOfDay())) {
                    return NewResponseUtil.makeError(AdminErrorCode.END_DAY_ERROR);
                }
                if (endDate.isBefore(beginDate)) {
                    return NewResponseUtil.makeError(AdminErrorCode.END_BEFORE_BEGIN_ERROR);
                }
                if (endDate.isAfter(beginDate.plusDays(MAX_RANGE_DAYS))) {
                    return NewResponseUtil.makeError(AdminErrorCode.DAY_RANGE_ERROR);
                }
            } catch (Exception e) {
                log.error("legalTimeRange error, beginTime:{}, endTime:{}", startTime, endTime, e);
                return NewResponseUtil.makeError(AdminErrorCode.DAY_FORMAT_ERROR);
            }
        }
        if (StringUtils.isNotEmpty(startTime) && !StringUtils.isNotEmpty(endTime)) {
            return NewResponseUtil.makeError(AdminErrorCode.DAY_FORMAT_ERROR);
        }
        if (!StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
            return NewResponseUtil.makeError(AdminErrorCode.DAY_FORMAT_ERROR);
        }
        return NewResponseUtil.makeSuccess(null);
    }


}
