package com.shuidihuzhu.cf.admin.mq.ugc;

import com.shuidi.weixin.common.util.StringUtils;
import com.shuidihuzhu.cf.biz.crowdfunding.CfSensitiveWordRecordBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.UserOperationStatConstant;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress;
import com.shuidihuzhu.cf.service.crowdfunding.CfSensitiveWordService;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 *
 * 发表动态成功
 *
 * Created by wangsf on 18/4/2.
 */
@Service
@RefreshScope
@Slf4j
@RocketMQListener(id = MQTagCons.CF_PUBLISH_PROGRESS, topic = MQTopicCons.CF, tags = MQTagCons.CF_PUBLISH_PROGRESS, group = "Cf-admin-ConsumerGroup_CF_PUBLISH_PROGRESS")
public class CfPublishProgressConsumer implements MessageListener<CrowdFundingProgress> {

    @Resource
	private CfSensitiveWordService cfSensitiveWordService;

    @Resource
	private CfSensitiveWordRecordBiz cfSensitiveWordRecordBiz;

	@Resource
	private MeterRegistry meterRegistry;

	@Override
	public ConsumeStatus consumeMessage(ConsumerMessage<CrowdFundingProgress> consumerMessage) {

	    if(consumerMessage == null || consumerMessage.getPayload() == null){
			return ConsumeStatus.CONSUME_SUCCESS;
		}

		//上报一次打点信息
		meterRegistry.counter(UserOperationStatConstant.USER_OPERATING_STAT,
				UserOperationStatConstant.OPERATION, UserOperationStatConstant.PROGRESS).increment();

		log.info("receive CrowdFundingProgress message: {}", consumerMessage.getPayload());

		CrowdFundingProgress progress = consumerMessage.getPayload();
		try {
			cfSensitiveWordRecordBiz.buildProgressOne(progress);
		} catch (Exception e) {
			log.error("CfPublishProgressConsumer 检查举报 {}", progress, e);
		}

		return ConsumeStatus.CONSUME_SUCCESS;
	}

}
