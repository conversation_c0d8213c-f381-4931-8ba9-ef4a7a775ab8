package com.shuidihuzhu.cf.admin.controller.api.util.vo;

import com.shuidihuzhu.cf.model.admin.MailTaskInfo;
import com.shuidihuzhu.cf.model.admin.MailTaskSql;

import java.util.Date;
import java.util.List;

/**
 * Created by wangsf on 17/4/7.
 */
public class MailTaskVo {
	private Integer id;
	private String mailReceivers;
	private String mailSubject;
	private String mailComment;
	private String descr;
	private Byte status;
	private Integer oprid;
	private Integer lastoprid;
	private Date lastoprtm;
	//    private String sqlText;
	private String dbName;

	private List<MailTaskSql> sqlList;

	public MailTaskVo() {
	}

	public MailTaskVo(MailTaskInfo mailTaskInfo) {
		if(mailTaskInfo != null) {
			this.id = mailTaskInfo.getId();
			this.mailReceivers = mailTaskInfo.getMailReceivers();
			this.mailSubject = mailTaskInfo.getMailSubject();
			this.mailComment = mailTaskInfo.getMailComment();
			this.descr = mailTaskInfo.getDescr();
			this.status = mailTaskInfo.getStatus();
			this.oprid = mailTaskInfo.getOprid();
			this.lastoprid = mailTaskInfo.getLastoprid();
			this.lastoprtm = mailTaskInfo.getLastoprtm();
			this.dbName = mailTaskInfo.getDbName();
		}
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getMailReceivers() {
		return mailReceivers;
	}

	public void setMailReceivers(String mailReceivers) {
		this.mailReceivers = mailReceivers;
	}

	public String getMailSubject() {
		return mailSubject;
	}

	public void setMailSubject(String mailSubject) {
		this.mailSubject = mailSubject;
	}

	public String getMailComment() {
		return mailComment;
	}

	public void setMailComment(String mailComment) {
		this.mailComment = mailComment;
	}

	public String getDescr() {
		return descr;
	}

	public void setDescr(String descr) {
		this.descr = descr;
	}

	public Byte getStatus() {
		return status;
	}

	public void setStatus(Byte status) {
		this.status = status;
	}

	public Integer getOprid() {
		return oprid;
	}

	public void setOprid(Integer oprid) {
		this.oprid = oprid;
	}

	public Integer getLastoprid() {
		return lastoprid;
	}

	public void setLastoprid(Integer lastoprid) {
		this.lastoprid = lastoprid;
	}

	public Date getLastoprtm() {
		return lastoprtm;
	}

	public void setLastoprtm(Date lastoprtm) {
		this.lastoprtm = lastoprtm;
	}

	public String getDbName() {
		return dbName;
	}

	public void setDbName(String dbName) {
		this.dbName = dbName;
	}

	public List<MailTaskSql> getSqlList() {
		return sqlList;
	}

	public void setSqlList(List<MailTaskSql> sqlList) {
		this.sqlList = sqlList;
	}

	@Override
	public String toString() {
		return "MailTaskVo{" +
				"id=" + id +
				", mailReceivers='" + mailReceivers + '\'' +
				", mailSubject='" + mailSubject + '\'' +
				", mailComment='" + mailComment + '\'' +
				", descr='" + descr + '\'' +
				", status=" + status +
				", oprid=" + oprid +
				", lastoprid=" + lastoprid +
				", lastoprtm=" + lastoprtm +
				", dbName='" + dbName + '\'' +
				", sqlList=" + sqlList +
				'}';
	}
}
