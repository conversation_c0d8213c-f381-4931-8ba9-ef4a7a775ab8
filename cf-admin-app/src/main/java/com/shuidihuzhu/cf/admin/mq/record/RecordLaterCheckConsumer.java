package com.shuidihuzhu.cf.admin.mq.record;

import com.shuidihuzhu.cf.biz.crowdfunding.CfQuestionnaireBiz;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.model.admin.CfQuestionnaire;
import com.shuidihuzhu.client.cf.growthtool.model.PreposeMaterialCommitNoticeModel;
import com.shuidihuzhu.client.cf.workorder.CfWenjuanWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WenjuanWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderLevel;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @DATE 2019/12/12
 */
@Service
@RocketMQListener(id = MQTagCons.record_later_check,
        group = "cf-admin" + MQTagCons.record_later_check,
        tags = MQTagCons.record_later_check,
        topic = MQTopicCons.CF)
@Slf4j
public class RecordLaterCheckConsumer implements MessageListener<Long> {

    @Autowired
    private CfQuestionnaireBiz cfQuestionnaireBiz;

    @Autowired
    private CfWenjuanWorkOrderClient cfWenjuanWorkOrderClient;


    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<Long> mqMessage) {

        long id = mqMessage.getPayload();

        CfQuestionnaire cfQuestionnaire = cfQuestionnaireBiz.getById(id);

        if (cfQuestionnaire == null){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        //如果已经提交  直接返回
        if(cfQuestionnaire.getQStatus() == CfQuestionnaire.q_status_submit){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        //1v1不生成工单
        if (cfQuestionnaire.getChannel().equals(PreposeMaterialCommitNoticeModel.ServiceChannelEnum.WEIXIN_1V1.getServiceChannelName())){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        WenjuanWorkOrder wenjuanWorkOrder = new WenjuanWorkOrder();
        wenjuanWorkOrder.setMobile(cfQuestionnaire.getMobile());
        wenjuanWorkOrder.setChannel(cfQuestionnaire.getChannel());
        wenjuanWorkOrder.setRecordId(id+"");
        wenjuanWorkOrder.setOrderType(WorkOrderType.wenjuan.getType());
        wenjuanWorkOrder.setCaseId(cfQuestionnaire.getCaseId());
        wenjuanWorkOrder.setOrderlevel(OrderLevel.edium.getType());

        Response<Long> response = cfWenjuanWorkOrderClient.createWenjuan(wenjuanWorkOrder);

        log.info("createWenjuan wenjuanWorkOrder={} response={}",wenjuanWorkOrder,response);

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
