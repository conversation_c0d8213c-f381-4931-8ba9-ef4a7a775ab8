package com.shuidihuzhu.cf.admin.controller.api.innerapi.mock;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.constants.AdminUserIDConstants;
import com.shuidihuzhu.cf.domain.approve.ApproveControlRecordDO;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.approve.ApproveControlFlowTypeEnum;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditOperateService;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditOperationItem;
import com.shuidihuzhu.client.cf.admin.client.AdminInnerMockClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 测试用内部接口 不会发布到线上
 *
 * <AUTHOR>
 */
@Profile("!production")
@RestController
@Slf4j
public class InnerMockController implements AdminInnerMockClient {

    @Autowired
    private InitialAuditOperateService initialAuditOperateService;

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    /**
     * 初审工单类型
     */
    private static final ImmutableList<Integer> SHEN_HE_ORDER_TYPES = ImmutableList.of(
            WorkOrderType.shenhe.getType(),
            WorkOrderType.dianhuashenhe.getType(),
            WorkOrderType.yiliaoshenhe.getType(),
            WorkOrderType.highriskshenhe.getType()
    );

    @Override
    public Response<Void> initialAuditApprove(int caseId) {

        InitialAuditOperationItem.HandleCaseInfoParam handleParam = new InitialAuditOperationItem.HandleCaseInfoParam();

        Response<List<WorkOrderVO>> listResponse = cfWorkOrderClient.listByCaseIdAndTypeAndResult(caseId,
                SHEN_HE_ORDER_TYPES, Lists.newArrayList(HandleResultEnum.unDoResult()));
        if (listResponse.notOk()) {
            return NewResponseUtil.makeResponse(listResponse.getCode(), listResponse.getMsg(), null);
        }
        List<WorkOrderVO> workOrders = listResponse.getData();

        int operatorId = AdminUserIDConstants.SYSTEM;

        // 有工单 领取并处理
        if (CollectionUtils.isNotEmpty(workOrders)) {

            WorkOrderVO workOrder = workOrders.get(0);

            // 有工单 判断工单状态
            long workOrderId = workOrder.getWorkOrderId();

            // 没人领取? 肯定没人领取 因为刚创建 就算被人领取 那就报错
            if (workOrder.getHandleResult() != HandleResultEnum.undoing.getType()) {
                return NewResponseUtil.makeResponse(AdminErrorCode.NO_MORE_WORK_ORDER.getCode(), "工单已被领取", null);
            }

            // 领取工单
            Response<Long> longResponse = cfWorkOrderClient.assignWorkOrderSystem(workOrderId, operatorId);
            if (longResponse.notOk()) {
                return NewResponseUtil.makeResponse(longResponse.getCode(), longResponse.getMsg(), null);
            }
            handleParam.setWorkOrderId(workOrderId);
            handleParam.setOrderType(workOrder.getOrderType());
        }

        // 没有工单 正常 单测创建案例不发mq 不会创建工单 就算发了mq也是异步查不到工单

        handleParam.setUserId(operatorId);
        handleParam.setCallComment("fake-call-comment");
        handleParam.setCallStatus(0);
        handleParam.setHandleComment("单元测试自动审核");
        handleParam.setHandleType(InitialAuditOperationItem.HandleTypeEnum.SUBMIT.getCode());
        handleParam.setUserCallStatus(0);
        handleParam.setCallUnicode("");
        handleParam.setPassIds(Lists.newArrayList(1, 100));
        handleParam.setRejectIds(Lists.newArrayList());
        handleParam.setCaseId(caseId);

        try {
            initialAuditOperateService.handleWorkOrder(handleParam);
        } catch (Exception e) {
            log.info("InnerMockController 初审处理异常:param:{}", handleParam, e);
            return NewResponseUtil.makeFail(e.getMessage());
        }

        return NewResponseUtil.makeSuccess(null);
    }
}
