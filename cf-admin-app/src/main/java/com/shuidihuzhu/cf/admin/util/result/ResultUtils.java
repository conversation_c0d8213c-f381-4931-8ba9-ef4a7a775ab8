package com.shuidihuzhu.cf.admin.util.result;

import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.common.web.enums.MyErrorCode;
import com.shuidihuzhu.common.web.model.Response;

/**
 * <AUTHOR>
 * @date 2018-05-23  18:19
 */
public class ResultUtils {

    public static <T> Response<T> transformOpResult2Response(OpResult<T> opResult) {
        Response<T> response = new Response<>();

        MyErrorCode errorCode = opResult.getErrorCode();
        response.setMsg(errorCode.getMsg());
        response.setCode(errorCode.getCode());
        response.setData(opResult.getData());
        return response;
    }

    /**
     * 可单独配置msg
     * @param opResult
     * @param <T>
     * @return
     */
    public static <T> Response<T> transformOpResult2RespWithMsg(OpResult<T> opResult) {
        return response(opResult);
    }

    /**
     * 可单独配置msg
     * @param opResult
     * @param <T>
     * @return
     */
    public static <T> Response<T> response(OpResult<T> opResult) {
        Response<T> response = new Response<>();
        response.setMsg(opResult.getMessage());
        response.setCode(opResult.getErrorCode().getCode());
        response.setData(opResult.getData());
        return response;
    }

}
