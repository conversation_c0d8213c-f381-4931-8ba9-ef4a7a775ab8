//package com.shuidihuzhu.cf.admin.controller.api.notice;
//
//import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.cloud.context.config.annotation.RefreshScope;
//import org.springframework.stereotype.Controller;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestMethod;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.ResponseBody;
//
//import com.shuidihuzhu.cf.admin.util.HuzhuRequestUtil;
//import com.shuidihuzhu.common.web.util.NewResponseUtil;
//
//
///**
// * @Author: wuxinlong
// * @Since: 2017-03-14
// */
//@RefreshScope
//@Controller
//@RequestMapping(path = "/admin/cf/notice/group")
//public class NoticeGroupController {
//	private static final Logger LOGGER = LoggerFactory.getLogger(NoticeGroupController.class);
//
//	@Value("${url.shuidi.api:}")
//	private String apiUrl;
//	private String baseUrl = null;
//
//	@RequestMapping(path = "add-or-update", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
//	@ResponseBody
//	@RequiresPermission("notice-group:addOrUpdate")
//	public Object addOrUpdate(String data, String noticeNoList) {
//		return HuzhuRequestUtil.getRequestResult(getBaseUrl(), "add-or-update", "data", data, "noticeNoList", noticeNoList);
//	}
//
//	@RequestMapping(path = "detail", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
//	@ResponseBody
//	@RequiresPermission("notice-group:addOrUpdate")
//	public Object detail(Integer id) {
//		if (null == id) {
//			return NewResponseUtil.makeFail("id不能为空");
//		}
//		return HuzhuRequestUtil.getRequestResult(getBaseUrl(), "detail", "id", "" + id);
//	}
//
//	@RequestMapping(path = "list", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
//	@ResponseBody
//	@RequiresPermission("notice-group:addOrUpdate")
//	public Object list(@RequestParam(name = "searchInfo", required = false, defaultValue = "{}") String searchInfo,
//	                   @RequestParam(name = "pagination", required = false, defaultValue = "{}") String pagination) {
//		return HuzhuRequestUtil.getRequestResult(getBaseUrl(), "list", "searchInfo", searchInfo, "pagination", pagination);
//	}
//
//	private String getBaseUrl() {
//		if (null == baseUrl) {
//			baseUrl = apiUrl + "/admin/notice/group/";
//		}
//
//		return baseUrl;
//	}
//}
