package com.shuidihuzhu.cf.admin.controller.api.innerapi;

import com.shuidihuzhu.cf.client.material.model.RaiseBasicInfoModel;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditBrainService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditSearchService;
import com.shuidihuzhu.client.cf.admin.client.CfAdminInitialAuditFeignClient;
import com.shuidihuzhu.client.cf.admin.model.InitialAuditCaseDetail;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
public class CfAdminInitialAuditFeignController implements CfAdminInitialAuditFeignClient {

    @Autowired
    private InitialAuditSearchService searchService;

    @Autowired
    private InitialAuditBrainService auditBrainService;

    @Override
    public Response<InitialAuditCaseDetail> queryCaseDetail(long workOrderId, int caseId) {
        return ResponseUtil.makeSuccess(searchService.queryCaseDetail(workOrderId, caseId));
    }

    @Override
    public Response<RaiseBasicInfoModel> selectRaiseBasicInfo(int caseId) {
        return ResponseUtil.makeSuccess(auditBrainService.selectRaiseBasicInfo(caseId));
    }
}
