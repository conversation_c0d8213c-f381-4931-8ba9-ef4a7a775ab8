package com.shuidihuzhu.cf.admin.controller.api.risk;

import com.shuidihuzhu.cf.client.ugc.wonrecord.WonRecord;
import com.shuidihuzhu.cf.domain.label.core.LabelNodeModel;
import com.shuidihuzhu.cf.service.label.core.service.LabelManageService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api("风控标签管理")
@RestController
@Slf4j
@RequestMapping(path = "/admin/cf/risk-label/manage")
public class RiskLabelManageController {

    @Autowired
    private LabelManageService labelManageService;

    @ApiOperation("搜索标签列表")
    @PostMapping("search-label")
    public Response<LabelNodeModel> searchLabel(@ApiParam("根标签id") @RequestParam long rootId,
                                                @ApiParam("搜索名字key") @RequestParam(required = false) String nameKey,
                                                @ApiParam("风险等级") @RequestParam(required = false) Integer riskLevel,
                                                @ApiParam("标签状态")@RequestParam(required = false) Integer labelStatus
                                      ) {
        return labelManageService.searchLabel(rootId, nameKey, riskLevel, labelStatus);
    }

    @ApiOperation("新增标签")
    @PostMapping("add")
    public Response<Long> add(@RequestParam long parentId,
                              @RequestParam @Size(max = 50, message = "名称最多可输入50个字符")
                                      String name,
                              @RequestParam @Size(max = 300, message = "场景描述最多可输入300个字符")
                                          String labelDesc,
                              @ApiParam("顺序 小的在前")
                              @RequestParam int seq,
                              @ApiParam("风险等级") @RequestParam int riskLevel) {
        final long operatorId = ContextUtil.getAdminLongUserId();
        return labelManageService.add(parentId, name, labelDesc, seq, riskLevel, operatorId);
    }

    @ApiOperation("校验标签是否可修改为目标状态")
    @PostMapping("valid-label-status-change")
    public Response<Void> validLabelStatusChange(@RequestParam long id,
                                                @ApiParam("启用状态{0: 初始化, 1: 启用, 2: 弃用, 10: 删除}")
                                                @RequestParam int labelStatus) {
        return labelManageService.validLabelStatusChange(id, labelStatus);
    }

    @ApiOperation("根据标签id修改标签状态")
    @PostMapping("update-label-status-by-label-id")
    public Response<Void> updateStatusByLabelId(@RequestParam long id,
                                                @ApiParam("启用状态{0: 初始化, 1: 启用, 2: 弃用, 10: 删除}")
                                                @RequestParam int labelStatus,
                                                @RequestParam(required = false, defaultValue = "") String reason) {
        final long operatorId = ContextUtil.getAdminLongUserId();
        return labelManageService.updateStatusByLabelId(id, labelStatus, operatorId, reason);
    }

    @ApiOperation("根据标签id修改标签场景描述")
    @PostMapping("update-label-desc-by-label-id")
    public Response<Void> updateLabelDescByLabelId(@RequestParam long id,
                                                   @RequestParam String labelDesc) {
        final long operatorId = ContextUtil.getAdminLongUserId();
        return labelManageService.updateLabelDescByLabelId(id, labelDesc, operatorId);
    }

    @ApiOperation("根据标签id修改标签顺序")
    @PostMapping("update-label-seq-by-label-id")
    public Response<Void> updateLabelSeqByLabelId(@RequestParam long id,
                                                  @RequestParam int seq) {
        final long operatorId = ContextUtil.getAdminLongUserId();
        return labelManageService.updateLabelSeqByLabelId(id, seq, operatorId);
    }

    @ApiOperation("根据标签id查询操作记录")
    @PostMapping("get-operate-record-by-id")
    public Response<List<WonRecord>> getOperateRecordById(@RequestParam long id) {
        return labelManageService.getOperateRecordById(id);
    }


}
