package com.shuidihuzhu.cf.admin.mq;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfRepeatInfoBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@RocketMQListener(id = MQTagCons.CF_DIAGNOSIS_INFO_CHANGE_TAG,
        group = "cf-admin-"  + MQTagCons.CF_DIAGNOSIS_INFO_CHANGE_TAG,
        tags = MQTagCons.CF_DIAGNOSIS_INFO_CHANGE_TAG,
        topic = MQTopicCons.CF)
@Slf4j
public class CfDiagnosisRepeatConsumer implements MessageListener<Integer> {

    @Autowired
    private AdminCfRepeatInfoBiz cfRepeatInfoBiz;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<Integer> mqMessage) {
//        log.info("诊断材料相似判断，收到的message {}", mqMessage.getPayload());
//        Integer caseId = mqMessage.getPayload();
//        if (caseId == null) {
//            log.error("诊断材料相似判断 caseId为空");
//            return ConsumeStatus.CONSUME_SUCCESS;
//        }
//
//        cfRepeatInfoBiz.handleDisgRepeatInfo(caseId);
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
