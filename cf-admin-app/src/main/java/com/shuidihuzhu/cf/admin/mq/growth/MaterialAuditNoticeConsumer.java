package com.shuidihuzhu.cf.admin.mq.growth;

import com.shuidihuzhu.cf.biz.crowdfunding.NoticeGrowthToolBiz;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.constant.CfClientMQTopicCons;
import com.shuidihuzhu.client.model.event.InfoApproveEvent;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @DATE 2020/4/9
 */
@Slf4j
@Service
@RocketMQListener(id = "notice_growth_cailiao",
        tags = CfClientMQTagCons.INFO_APPROVE_MSG,
        topic = CfClientMQTopicCons.CF,
        group = "notice_growth_" + CfClientMQTagCons.INFO_APPROVE_MSG)
public class MaterialAuditNoticeConsumer implements MessageListener<InfoApproveEvent> {

    @Autowired
    private NoticeGrowthToolBiz growthToolBiz;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<InfoApproveEvent> mqMessage) {

        if(Objects.isNull(mqMessage) || Objects.isNull(mqMessage.getPayload())){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        InfoApproveEvent approveEvent = mqMessage.getPayload();
        int caseId = approveEvent.getCaseId();

        growthToolBiz.notice(caseId);

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
