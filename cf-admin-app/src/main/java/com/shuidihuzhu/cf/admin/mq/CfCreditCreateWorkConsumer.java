package com.shuidihuzhu.cf.admin.mq;

import com.shuidihuzhu.cf.client.material.model.CfPropertyInsuranceInfoModel;
import com.shuidihuzhu.cf.client.material.model.mq.MqTagsConstant;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.service.workorder.WorkOrderCreditService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: fengxuan
 * @create 2020-01-02 16:54
 **/
@Service
@RocketMQListener(id = "cf_credit_create_work",
        group = "cf_credit_create_work_group",
        tags = MqTagsConstant.MQ_OLD_CASE_SUBMIT_PROPERTY,
        topic = MQTopicCons.CF)
@Slf4j
public class CfCreditCreateWorkConsumer implements MessageListener<CfPropertyInsuranceInfoModel> {

    @Autowired
    WorkOrderCreditService workOrderCreditService;


    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfPropertyInsuranceInfoModel> mqMessage) {
        log.info("receive MQ_OLD_CASE_SUBMIT_PROPERTY message: {}", mqMessage);
        if (mqMessage == null || mqMessage.getPayload() == null) {
            log.error("MQ_OLD_CASE_SUBMIT_PROPERTY消息体为空");
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        CfPropertyInsuranceInfoModel payload = mqMessage.getPayload();
        int caseId = payload.getCaseId();

        try {
            workOrderCreditService.create(caseId);
        } catch (Exception e) {
            log.error("创建增信工单失败,caseId:{}", caseId, e);
            return ConsumeStatus.RECONSUME_LATER;
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
