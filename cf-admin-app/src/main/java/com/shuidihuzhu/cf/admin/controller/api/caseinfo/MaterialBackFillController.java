package com.shuidihuzhu.cf.admin.controller.api.caseinfo;

import com.shuidihuzhu.cf.client.feign.cipher.constants.DesensitizeEnum;
import com.shuidihuzhu.cf.constants.crowdfunding.CrowdfundingCons;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.model.crowdfunding.CfHospital;
import com.shuidihuzhu.cf.model.crowdfunding.vo.HospitalInfo;
import com.shuidihuzhu.cf.service.admin.material.MaterialBackFillService;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.cf.api.model.BackFillVO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * @Description: 材审代录入回填信息
 * @Author: panghairui
 * @Date: 2023/12/7 8:38 PM
 */
@Slf4j
@RefreshScope
@RestController
@RequestMapping(path = "admin/cf/material")
public class MaterialBackFillController {

    @Resource
    private MaterialBackFillService materialBackFillService;
    @Resource
    private MaskUtil maskUtil;

    @ApiOperation(value = "回填代录入表单信息")
    @RequestMapping(path = "back-fill", method = RequestMethod.POST)
    public Response<BackFillVO> backFillInfo(@RequestParam("caseId") Integer caseId) {
        BackFillVO backFillVO = materialBackFillService.backFillInfo(caseId);
        Optional.ofNullable(backFillVO.getBackFillBasicInfo())
                .ifPresent(item -> {
                    String patientIdCard = item.getPatientIdCard();
                    // 身份证才掩码
                    if (StringUtils.isNotBlank(patientIdCard) && item.getPatientIdType() == UserIdentityType.identity) {
                        item.setPatientIdCardMask(maskUtil.buildByDecryptStrAndType(patientIdCard, DesensitizeEnum.IDCARD));
                        item.setPatientIdCard(null);
                    }
                    String raiserIdCard = item.getRaiserIdCard();
                    if (StringUtils.isNotBlank(raiserIdCard)) {
                        item.setRaiserIdCardMask(maskUtil.buildByDecryptStrAndType(raiserIdCard, DesensitizeEnum.IDCARD));
                        item.setRaiserIdCard(null);
                    }
                });
        Optional.ofNullable(backFillVO.getBackFillFirstApproveInfo())
                .ifPresent(item -> {
                    String realPatientIdCard = item.getRealPatientIdCard();
                    if (StringUtils.isNotBlank(realPatientIdCard)) {
                        item.setRealPatientIdCardMask(maskUtil.buildByDecryptStrAndType(realPatientIdCard, DesensitizeEnum.IDCARD));
                        item.setRealPatientIdCard(null);
                    }
                    String itemPhone = item.getPhone();
                    if (StringUtils.isNotBlank(itemPhone)) {
                        item.setPhoneMask(maskUtil.buildByDecryptPhone(itemPhone));
                        item.setPhone(null);
                    }
                });
        return NewResponseUtil.makeSuccess(backFillVO);
    }

    @ApiOperation(value = "获取医院信息")
    @RequestMapping(path = "get-hospital", method = RequestMethod.POST)
    public Response<HospitalInfo> getHospital(@RequestParam Integer cityId,
                                              @RequestParam String userInput,
                                              @RequestParam Integer pageSize,
                                              @RequestParam Integer current) {
        if (pageSize == null || pageSize < CrowdfundingCons.MIN_PAGE_SIZE
                || pageSize > CrowdfundingCons.MAX_PAGE_SIZE) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (cityId == null) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        HospitalInfo hospitalInfo = materialBackFillService.selectHospital(cityId, userInput, pageSize, current);
        if (hospitalInfo == null) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_ERROR);
        }

        Integer total = hospitalInfo.getPagination().getTotal();
        if (0 == total) {
            List<CfHospital> cfHospitalList = hospitalInfo.getCfHospitals();
            CfHospital cfHospital = new CfHospital();
            cfHospital.setId(0);
            cfHospital.setName(userInput);
            cfHospitalList.add(cfHospital);
            hospitalInfo.setCfHospitals(cfHospitalList);
        }
        return NewResponseUtil.makeSuccess(hospitalInfo);
    }

}
