package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.verify.client.menu.IdCardVerifyResultEnum;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.account.verify.client.model.VerifyIdcardVO;
import com.shuidihuzhu.cf.client.base.page.v1.model.PaginationListVO;
import com.shuidihuzhu.cf.client.feign.cipher.constants.DesensitizeEnum;
import com.shuidihuzhu.cf.delegate.saas.SeaRoleClientV1;
import com.shuidihuzhu.cf.enhancer.utils.MaskCodeUtil;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.model.admin.PhotoStatus;
import com.shuidihuzhu.cf.risk.client.rpc.PreposeMaterialRiskClient;
import com.shuidihuzhu.cf.risk.model.risk.HouseThresholdParam;
import com.shuidihuzhu.cf.service.approve.ApproveAuditService;
import com.shuidihuzhu.cf.vo.approve.ApproveAuditParam;
import com.shuidihuzhu.cf.vo.approve.MultipleCaseImagesVo;
import com.shuidihuzhu.cf.vo.approve.RecoverRepeatCaseVo;
import com.shuidihuzhu.cf.vo.crowdfunding.*;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.admin.util.AdminDateUtil;
import com.shuidihuzhu.cf.admin.util.AdminListUtil;
import com.shuidihuzhu.cf.admin.util.lock.RedisDistributedLock;
import com.shuidihuzhu.cf.biz.admin.AdminCfOperatingRecordBiz;
import com.shuidihuzhu.cf.biz.admin.common.OperationHistorySummaryBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.client.feign.CaseInfoApproveStageFeignClient;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.delegate.commonservice.ICommonServiceDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingOperationDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingUserDelegate;
import com.shuidihuzhu.cf.delegate.finance.IFinanceDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.domain.CaseRaiseRiskDO;
import com.shuidihuzhu.cf.domain.CfCaseRiskDO;
import com.shuidihuzhu.cf.domain.RemarkDO;
import com.shuidihuzhu.cf.domain.approve.ApproveControlRecordDO;
import com.shuidihuzhu.cf.domain.visitconfig.VisitConfigLogDO;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.BackgroundLogEnum;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.admin.CfInfoFromType;
import com.shuidihuzhu.cf.enums.admin.common.OperationType;
import com.shuidihuzhu.cf.enums.approve.ApproveControlFlowTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.enums.crowdfunding.risk.CaseRiskPassedEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.risk.CfCaseRiskTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.risk.CfCaseRiskVerifiedEnum;
import com.shuidihuzhu.cf.facade.AdminApproveFacade;
import com.shuidihuzhu.cf.finance.client.feign.CfFinancePauseFeignClient;
import com.shuidihuzhu.cf.finance.client.feign.CfPayeeFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.model.po.CfCashPauseBooleanPo;
import com.shuidihuzhu.cf.finance.model.po.CfPayeeInfoChangeView;
import com.shuidihuzhu.cf.model.admin.workorder.imageContent.CPublishImageContent;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.material.AuditSuggestModifyDetail;
import com.shuidihuzhu.cf.model.crowdfunding.materialAudit.AuditSuggestModifyParam;
import com.shuidihuzhu.cf.model.crowdfunding.materialAudit.CfCaseAuditVersion;
import com.shuidihuzhu.cf.model.crowdfunding.vo.*;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.FinanceApproveService;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cf.service.approve.ApproveControlService;
import com.shuidihuzhu.cf.service.approve.ApproveService;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.cf.service.crowdfunding.*;
import com.shuidihuzhu.cf.service.message.SmsRecordService;
import com.shuidihuzhu.cf.service.sensitive.AdminSensitiveProcessService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditSearchService;
import com.shuidihuzhu.cf.service.workorder.read.WorkOrderReadService;
import com.shuidihuzhu.cf.vo.admin.AdminReminderVo;
import com.shuidihuzhu.cf.vo.approve.TreatmentVO;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.api.client.CfMaterialFeignClient;
import com.shuidihuzhu.client.cf.api.model.MaterialVersion;
import com.shuidihuzhu.client.cf.growthtool.client.ClewPreproseMaterialFeignClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.constants.CrowdfundingCons;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.*;
import com.shuidihuzhu.common.web.util.admin.PageUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import com.shuidihuzhu.msg.model.SmsTemplate;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * Created by wuxinlong on 7/11/16.
 */
@Slf4j
@RefreshScope
@RestController
@RequestMapping(path = "/admin/crowdfunding/approve")
public class CrowdfundingApproveController {
    private static final Logger LOGGER = LoggerFactory.getLogger(CrowdfundingApproveController.class);
    @Autowired
    private ClewPreproseMaterialFeignClient clewPreproseMaterialFeignClient;
    @Autowired
    private AdminCfRepeatInfoBiz cfRepeatInfoBiz;
    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private ICrowdfundingOperationDelegate crowdfundingOperationDelegate;
    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;
    @Autowired
    private OperationHistorySummaryBiz operationHistorySummaryBiz;
    @Autowired
    private ICommonServiceDelegate commonServiceDelegate;
    @Autowired
    private CfAdminOperationRecordBiz cfAdminOperationRecordBiz;
    @Autowired
    private AdminCfInfoExtBiz adminCfInfoExtBiz;
    @Autowired
    private ICrowdfundingUserDelegate crowdfundingUserDelegate;
    @Autowired
    private CfCommitVerifyItemBiz cfCommitVerifyItemBiz;
    @Autowired
    private AdminApproveService adminApproveService;
    @Autowired
    private FinanceApproveService financeApproveService;
    @Autowired
    private AdminCrowdfundingDetailSendMsgTemplateBiz sendMsgTemplateBiz;
    @Autowired
    private CfRefuseReasonEntityBiz cfRefuseReasonEntityBiz;
    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;
    @Autowired
    private AdminCfReportAddTrustBiz cfReportAddTrustBiz;
    @Autowired
    private CfPhotoAiBiz cfPhotoAiBiz;
    @Resource
    private IRiskDelegate riskDelegate;
    @Resource
    private IdCardVerifyService idCardVerifyService;
    @Resource
    private AdminApproveFacade adminApproveFacade;
    @Autowired
    private AdminCfOperatingRecordBiz adminCfOperatingRecordBiz;
    @Autowired
    private AdminCaseVisitConfigBiz caseVisitConfigBiz;
    @Autowired
    private CfReportFollowCommentBiz cfReportFollowCommentBiz;
    @Autowired
    private SeaRoleClientV1 seaRoleClientV1;
    @Autowired
    private AdminCfInfoSearchBiz adminCfInfoSearchBiz;
    @Autowired
    private CfMaterialVerityHistoryBiz verityHistoryBiz;
    @Autowired
    private CfPayeeFeignClient cfPayeeFeignClient;
    @Autowired
    private AdminSensitiveProcessService adminSensitiveProcessService;
    @Autowired
    private CfMaterialFeignClient materialFeignClient;
    @Value("${diff.approve-search:true}")
    private boolean approveSearchDiff;
    @Value("${diff.contact-search:false}")
    private boolean contactSearchDiff;
    @Value("${diff.alternate-search:false}")
    private boolean alternateSearchDiff;
    @Resource
    private CfFinancePauseFeignClient cfFinancePauseFeignClient;

    @Autowired
    private InitialAuditSearchService initialAuditSearchService;

    @Autowired(required = false)
    private Producer producer;

    @Autowired
    private ApproveControlService approveControlService;

    @Autowired
    private ApproveRemarkOldService approveRemarkOldService;

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @Autowired
    private CommonOperationRecordClient commonOperationRecordClient;

    @Autowired
    private CaseInfoApproveStageFeignClient caseInfoApproveStageFeignClient;

    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    @Autowired
    private IFinanceDelegate financeDelegate;

    @Autowired
    private SmsRecordService smsRecordService;

    @Autowired
    private ApproveService ApproveService;

    @Autowired
    private CfCailiaoService cailiaoService;

    @Autowired
    private WorkOrderReadService workOrderReadService;

    @Resource
    private AdminCrowdfundingAttachmentBiz adminCrowdfundingAttachmentBiz;

    @Resource
    private ApproveAuditService approveAuditService;
    @Autowired
    private MaskUtil maskUtil;

    @Autowired
    private PreposeMaterialRiskClient preposeMaterialRiskClient;

    @Resource
    private AdminCrowdfundingInfoPayeeBiz adminCrowdfundingInfoPayeeBiz;

    @Resource
    private AdminCrowdfundingAuthorBiz adminCrowdfundingAuthorBiz;

    @RequiresPermission("contact:search")
    @RequestMapping(path = "contact-search", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response contactSearch(Integer current, Integer pageSize, @RequestParam(required = false) String mobile,
                                  @RequestParam(required = false) String name, @RequestParam(required = false) Integer id,
                                  @RequestParam(required = false) String title, @RequestParam(required = false) String caseUserId,
                                  @RequestParam(required = false) Integer callStatus, @RequestParam(required = false) Integer isContact,
                                  @RequestParam(required = false) Integer sortHandle, @RequestParam(required = false) String startTime,
                                  @RequestParam(required = false) String endTime, @RequestParam(required = false) Integer handle,
                                  @RequestParam(required = false) Integer isReported,
                                  @RequestParam(required = false) String content) {
        LOGGER.info(
                "contactList current:{}, pageSize:{}, mobile:{}, name:{}, id:{}, caseUserId:{}, title:{}, isContact:{}, "
                        + "callStatus:{}, sortHandle:{}, startTime:{}, endTime:{}, handle:{}, isReported:{}, content:{}",
                current, pageSize, mobile, name, id, caseUserId, title, isContact,
                callStatus, sortHandle, startTime, endTime, handle, isReported, content);

//        if (AdminDateUtil.isTimeExtendLimit(startTime, endTime, 7)) {
//            return NewResponseUtil.makeFail("时间范围超过一周,请重新选择");
//        }
        // https://wiki.shuiditech.com/pages/viewpage.action?pageId=288851934
        if (StringUtils.isBlank(title) && StringUtils.isBlank(mobile) && id == null && StringUtils.isBlank(name)
                && StringUtils.isBlank(caseUserId)) {
            if (StringUtils.isBlank(startTime) && StringUtils.isBlank(endTime)) {
                Date now = new Date();
                startTime = com.shuidihuzhu.common.util.DateUtil.getDate2LStr(DateUtils.addDays(now, -7));
                endTime = com.shuidihuzhu.common.util.DateUtil.getDate2LStr(now);
                log.info("运营查询设置默认的时间 start:{}, end:{}", startTime, endTime);
            }
        }

        Map<String, Object> result = doSearch(current, pageSize, mobile, name, id, title, caseUserId, callStatus, isContact,
                sortHandle, startTime, endTime, handle, isReported, content);

        return NewResponseUtil.makeSuccess(result);
    }

    private Map<String, Object> doSearch(Integer current, Integer pageSize, String mobile,
                                         String name, Integer id,
                                         String title, String caseUserId,
                                         Integer callStatus, Integer isContact,
                                         Integer sortHandle, String startTime,
                                         String endTime, Integer handle,
                                         Integer isReported, String content) {
        if (pageSize == null || pageSize < CrowdfundingCons.MIN_PAGE_SIZE
                || pageSize > CrowdfundingCons.MAX_PAGE_SIZE) {
            return Collections.EMPTY_MAP;
        }

        Map<String, Object> result = Maps.newHashMap();
        List<CrowdfundingInfoVo> crowdfundingInfoVoList = Lists.newArrayList();
        if (contactSearchDiff) {
            Pair<Long, List<CrowdfundingInfoVo>> pair = adminCfInfoSearchBiz.contactSearchFromEs(mobile, name, id, title, caseUserId,
                    callStatus, isContact, sortHandle, startTime, endTime, handle, current, pageSize, content);
            crowdfundingInfoVoList = pair.getRight();

            Map<String, Object> pageMap = Maps.newHashMap();
            pageMap.put("total", pair.getLeft());
            pageMap.put("current", (current == null || current < 1) ? 1 : current);
            pageMap.put("pageSize", pageSize);

            result.put("pagination", pageMap);

        } else {
            crowdfundingInfoVoList = adminCfInfoSearchBiz.contactSearch(mobile, name, id, title, caseUserId,
                    callStatus, isContact, sortHandle, startTime, endTime, handle, current, pageSize);
            result.put("pagination", PageUtil.transform2PageMap(crowdfundingInfoVoList));
        }

        List<String> infoUuids = crowdfundingInfoVoList.stream().map(CrowdfundingInfoVo::getInfoId).collect(Collectors.toList());

        Response<Map<String, CfCapitalAccount>> cfCapitalAccountMapResponse =
                financeDelegate.getCfCapitalAccountMapByInfoUuids(infoUuids);

        List<AdminCrowdfundingInfoView> crowdfundingInfoViewList = adminApproveService.getCrowdfundingInfoViewList(crowdfundingInfoVoList, cfCapitalAccountMapResponse.getData());

        result.put("data", crowdfundingInfoViewList);
        // fill线下渠道
        adminApproveService.fillOffLineBdChannel(crowdfundingInfoViewList);
        return result;
    }

    @RequiresPermission("contact:list")
    @RequestMapping(path = "contact-list", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response contactList(@ApiParam("当前页数") @RequestParam(defaultValue = "1") Integer current,
                                @ApiParam("页面规模") @RequestParam(defaultValue = "10") Integer pageSize) {
        LOGGER.info("contactList current:{}, pageSize:{}", current, pageSize);
        if (pageSize < CrowdfundingCons.MIN_PAGE_SIZE || pageSize > CrowdfundingCons.MAX_PAGE_SIZE) {
            pageSize = 10;
        }
        Map<String, Object> result = Maps.newHashMap();

        if (contactSearchDiff) {
            result = doSearch(current, pageSize, null, null, null, null, null,
                    null, null, null, null, null, null, null, null);
        } else {
            List<CrowdfundingInfoVo> crowdfundingInfos = crowdfundingInfoBiz.selectBaseContactListPages(current, pageSize);
            List<String> infoUuidList = crowdfundingInfos.stream().map(CrowdfundingInfoVo::getInfoId).collect(Collectors.toList());

            Response<Map<String, CfCapitalAccount>> cfCapitalAccountMapResponse =
                    financeDelegate.getCfCapitalAccountMapByInfoUuids(infoUuidList);
            if (cfCapitalAccountMapResponse.notOk()) {
                log.error("CfCapitalAccount 数据获取失败");
                return NewResponseUtil.makeError(AdminErrorCode.FINANCE_FEIGN_ERROR, null);
            }

            List<AdminCrowdfundingInfoView> crowdfundingInfoViewList =
                    adminApproveService.getBaseListPageView(crowdfundingInfos, cfCapitalAccountMapResponse.getData());

            Map<String, Object> map = PageUtil.transform2PageMap(crowdfundingInfos);
            //FIXME 上了es后修复这个magic number
            map.put("total", 708202);
            map.put("current", current);
            map.put("pageSize", pageSize);
            result.put("pagination", map);
            result.put("data", crowdfundingInfoViewList);
            // fill线下渠道
            adminApproveService.fillOffLineBdChannel(crowdfundingInfoViewList);
        }

        return NewResponseUtil.makeSuccess(result);
    }

    @RequiresPermission(value = "approve:list")
    @RequestMapping(path = "approve-list", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation("材料审核列表页")
    public Response approveList(@ApiParam("当前页数") @RequestParam(defaultValue = "1") Integer current,
                                @ApiParam("页面规模") @RequestParam(defaultValue = "10") Integer pageSize) {
        LOGGER.info("CrowdfundingApproveController approveList current:{}, pageSize:{}", current, pageSize);
        if (pageSize < CrowdfundingCons.MIN_PAGE_SIZE || pageSize > CrowdfundingCons.MAX_PAGE_SIZE) {
            pageSize = 10;
        }

        Map<String, Object> result = Maps.newHashMap();
        if (approveSearchDiff) {
            result = doApproveSearch2(current, pageSize, null, null, null, null,
                    null, null, null, null, null, null,
                    null, null, null, null, null, null, null, 0, 0, -1);
        } else {

            long currentTime = System.currentTimeMillis();
            List<CrowdfundingInfoVo> crowdfundingInfos = crowdfundingInfoBiz.selectBaseApproveListPages(current, pageSize);

            long getCfInfoCost = System.currentTimeMillis() - currentTime;

            List<String> infoUuidList = crowdfundingInfos.stream().map(CrowdfundingInfoVo::getInfoId).collect(Collectors.toList());
            Response<Map<String, CfCapitalAccount>> cfCapitalAccountMapResponse =
                    financeDelegate.getCfCapitalAccountMapByInfoUuids(infoUuidList);
            if (cfCapitalAccountMapResponse.notOk()) {
                log.error("CfCapitalAccount 数据获取失败");
                return NewResponseUtil.makeError(AdminErrorCode.FINANCE_FEIGN_ERROR, null);
            }

            List<AdminCrowdfundingInfoView> crowdfundingInfoViewList =
                    adminApproveService.getBaseListPageView(crowdfundingInfos, cfCapitalAccountMapResponse.getData());

            long batchGetViewCost = System.currentTimeMillis() - currentTime - getCfInfoCost;

            log.info("approveList getCfInfoCost {} batchGetViewCost {}", getCfInfoCost, batchGetViewCost);

            /**
             * 优化逻辑：
             * 1，不用pagehelper获取真正的total，避免一条sql语句被执行多次
             * 2，随意给一个假的totalCount
             */
            Map<String, Object> map = PageUtil.transform2PageMap(crowdfundingInfos);
            //FIXME 上了es后修复这个magic number
            map.put("total", 708202);
            map.put("current", current);
            map.put("pageSize", pageSize);
            result.put("pagination", map);
            result.put("data", crowdfundingInfoViewList);
        }

        return NewResponseUtil.makeSuccess(result);
    }


    @RequiresPermission("approve:search")
    @RequestMapping(path = "approve-search", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation("案例审核搜索")
    public Response approveSearch(@ApiParam("当前页数") @RequestParam(defaultValue = "1") Integer current,
                                  @ApiParam("页面规模") @RequestParam(defaultValue = "10") Integer pageSize,
                                  @RequestParam(required = false) @ApiParam("提交人手机号") String mobile,
                                  @RequestParam(required = false) @ApiParam("患者姓名") String name,
                                  @RequestParam(required = false) @ApiParam("案例id") Integer id,
                                  @RequestParam(required = false) @ApiParam("标题") String title,
                                  @RequestParam(required = false) @ApiParam("发起人userId") String caseUserId,
                                  @RequestParam(required = false) @ApiParam("驳回次数") Integer refuseCountHandle,
                                  @RequestParam(required = false) @ApiParam("案例状态: 0-审批中, 1-修改中,2-筹款中, 4-已提交") Integer status,
                                  @RequestParam(required = false) @ApiParam("是否沟通") Integer isContact,
                                  @RequestParam(required = false) @ApiParam("案例状态: 是否结束") Integer finished,
                                  @RequestParam(required = false) @ApiParam("操作状态: 0-未操作 1-已备注 2-已操作") Integer operationStatus,
                                  @RequestParam(required = false) @ApiParam("数据状态: ") Integer dataStatus,
                                  @RequestParam(required = false) @ApiParam("排序方式: 1-创建时间 2-筹款金额 3-操作时间 4-提审时间 6-驳回次数") Integer sortHandle,
                                  @RequestParam(required = false) @ApiParam("开始日期") String startTime,
                                  @RequestParam(required = false) @ApiParam("结束日期") String endTime,
                                  @RequestParam(required = false) @ApiParam("特殊操作类型: 3-延后等待审核 4-延后等待联系 5-不在处理案例") Integer handle,
                                  @RequestParam(required = false) @ApiParam("是否被举报") Integer isReported,
                                  @RequestParam(required = false) @ApiParam("筹款文字内容") String content,
                                  @RequestParam(required = false, defaultValue = "0") @ApiParam("增信手动关闭") int creditStatus,
                                  @RequestParam(required = false, defaultValue = "0") @ApiParam("延后电话联系原因类型") int deferContactReasonType,
                                  @RequestParam(required = false, defaultValue = "-1") @ApiParam("是否主动服务") int fuwuType
    ) {
        LOGGER.info(
                "CrowdfundingApproveController approveList current:{}, pageSize:{}, mobile:{}, name:{}, id:{}, title:{}, caseUserId:{}, "
                        + "status:{}, refuseCountHandle:{}, isContact:{}, finished:{}, operationStatus:{}, dataStatus:{}, sortHandle:{}, startTime:{}, endTime:{}, handle:{}, isReported:{}, content:{}, deferContactReasonType:{}",
                current, pageSize, mobile, name, id, title, caseUserId, status, refuseCountHandle, isContact, finished,
                operationStatus, dataStatus, sortHandle, startTime, endTime, handle, isReported, content, deferContactReasonType);

        if (AdminDateUtil.isTimeExtendLimit(startTime, endTime, 7)) {
            return NewResponseUtil.makeFail("时间范围超过一周,请重新选择");
        }

        if (AdminDateUtil.isEndTimeExtendToday(endTime)) {
            return NewResponseUtil.makeFail("结束时间不能晚于今天");
        }


        // https://wiki.shuiditech.com/pages/viewpage.action?pageId=288851934
        if (StringUtils.isBlank(title) && StringUtils.isBlank(mobile) && id == null && StringUtils.isBlank(name)
                && StringUtils.isBlank(caseUserId) && handle == null) {
            if (StringUtils.isBlank(startTime) && StringUtils.isBlank(endTime)) {
                Date now = new Date();
                startTime = com.shuidihuzhu.common.util.DateUtil.getDate2LStr(DateUtils.addDays(now, -7));
                endTime = com.shuidihuzhu.common.util.DateUtil.getDate2LStr(now);
                log.info("案例查询查询设置默认的时间 start:{}, end:{}", startTime, endTime);
            }
        }

        Map<String, Object> result = doApproveSearch2(current, pageSize, mobile, name, id, title, caseUserId, refuseCountHandle, status, isContact,
                finished, operationStatus, dataStatus, sortHandle, startTime, endTime, handle, isReported, content, creditStatus, deferContactReasonType, fuwuType);

        return NewResponseUtil.makeSuccess(result);
    }

    private Map<String, Object> doApproveSearch2(Integer current, Integer pageSize, String mobile,
                                                 String name, Integer id, String title, String caseUserId, Integer refuseCountHandle,
                                                 Integer status, Integer isContact, Integer finished, Integer operationStatus,
                                                 Integer dataStatus, Integer sortHandle, String startTime, String endTime,
                                                 Integer handle, Integer isReported, String content, int creditStatus, int deferContactReasonType,
                                                 int fuwuType) {
        if (pageSize == null || pageSize < CrowdfundingCons.MIN_PAGE_SIZE
                || pageSize > CrowdfundingCons.MAX_PAGE_SIZE) {
            return Collections.EMPTY_MAP;
        }
        long currentTime = System.currentTimeMillis();

        Map<String, Object> result = Maps.newHashMap();
        List<CrowdfundingInfoVo> crowdfundingInfoVoList = Lists.newArrayList();

        if (alternateSearchDiff) {
            crowdfundingInfoVoList = adminCfInfoSearchBiz.reserveApproveSearch(mobile, name, id, title, current, pageSize);
            result.put("pagination", PageUtil.transform2PageMap(crowdfundingInfoVoList));
        } else if (approveSearchDiff) {

            Pair<Long, List<CrowdfundingInfoVo>> pair = adminCfInfoSearchBiz.approveSearchFromEs(mobile, name, id, title, caseUserId,
                    refuseCountHandle, status, isContact, finished, operationStatus, dataStatus, sortHandle,
                    StringUtils.trimToNull(startTime), StringUtils.trimToNull(endTime), handle, current, pageSize, content, creditStatus, deferContactReasonType,
                    fuwuType);
            crowdfundingInfoVoList = pair.getRight();

            Map<String, Object> pageMap = Maps.newHashMap();
            pageMap.put("total", pair.getLeft());
            pageMap.put("current", (current == null || current < 1) ? 1 : current);
            pageMap.put("pageSize", pageSize);

            result.put("pagination", pageMap);

        } else {
            crowdfundingInfoVoList = adminCfInfoSearchBiz.approveSearch(mobile, name, id, title, caseUserId,
                    refuseCountHandle, status, isContact, finished, operationStatus, dataStatus, sortHandle,
                    StringUtils.trimToNull(startTime), StringUtils.trimToNull(endTime), handle, current, pageSize);
            result.put("pagination", PageUtil.transform2PageMap(crowdfundingInfoVoList));
        }

        log.info("crowdfundingInfoVoList size:{}", crowdfundingInfoVoList.size());
        long getCfInfoCost = System.currentTimeMillis() - currentTime;

        //获取自资金账户信息
        List<String> infoUuids = crowdfundingInfoVoList.stream().map(CrowdfundingInfoVo::getInfoId)
                .collect(Collectors.toList());
        Response<Map<String, CfCapitalAccount>> cfCapitalAccountMapResponse =
                financeDelegate.getCfCapitalAccountMapByInfoUuids(infoUuids);

        long capitalAccountGetMapCost = System.currentTimeMillis() - currentTime - getCfInfoCost;
        List<AdminCrowdfundingInfoView> crowdfundingInfoViewList = this.adminApproveService.
                getCrowdfundingInfoViewList(crowdfundingInfoVoList, cfCapitalAccountMapResponse.getData());

        long batchGetViewCost = System.currentTimeMillis() - currentTime - getCfInfoCost - capitalAccountGetMapCost;

        log.info("doApproveSearch2 getCfInfoCost:{} capitalAccountGetMapCost:{} batchGetViewCost:{}", getCfInfoCost, capitalAccountGetMapCost, batchGetViewCost);

        result.put("data", crowdfundingInfoViewList);
        return result;

    }

    @RequiresPermission("approve:ai-review")
    @RequestMapping(path = "ai-review", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "照片人工审核反馈")
    public Response ReviewPhoto(String infoUuid,
                                @ApiParam("审核结果")
                                @RequestParam(name = "reviewResult", defaultValue = "-1")
                                Integer reviewResult) {
        if (null == infoUuid || reviewResult < -1 || reviewResult > 1) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }
        CrowdfundingInfoVo crowdfundingInfoVo = crowdfundingInfoBiz.getFundingInfoVoByInfoUuid(infoUuid);
        int crowdfundingId = crowdfundingInfoVo.getId();
        cfPhotoAiBiz.updateArtificialRes(crowdfundingId, reviewResult);
        return NewResponseUtil.makeError(AdminErrorCode.SUCCESS);
    }

    @RequiresPermission("approve:multi-payee-info-query")
    @RequestMapping(path = "multi-payee-info-query", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "多收款人信息查询")
    public Response<List<MultiPayeeInfoQueryVo>> multiPayeeInfoQuery(@RequestParam("infoUuid") String infoUuid) {
        // 日志前缀
        String logPrefix = "CrowdfundingApproveController.multiPayeeInfoQuery";

        // 1. 参数判空
        if (StringUtils.isBlank(infoUuid)) {
            return NewResponseUtil.makeFail("参数 infoUuid 不能为空");
        }

        // 2. 查询当前案例
        CrowdfundingInfo currentCase = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (currentCase == null) {
            log.debug("{} crowdfundingInfoVo is null, infoUuid: {}", logPrefix, infoUuid);
            return NewResponseUtil.makeSuccess(Collections.emptyList());
        }

        int currentCaseId = currentCase.getId();
        String payeeIdCardEncrypt = currentCase.getPayeeIdCard();
        log.info("{} currentCaseId:{}, payeeIdCardEncrypt:{}", logPrefix, currentCaseId, payeeIdCardEncrypt);

        if (StringUtils.isBlank(payeeIdCardEncrypt)) {
            log.debug("{} payeeIdCardEncrypt is blank, infoUuid: {}", logPrefix, infoUuid);
            return NewResponseUtil.makeSuccess(Collections.emptyList());
        }

        // 3. 查询同身份证下的其他案例 ID，排除当前案例，限制最多 9 个
        List<Integer> otherCaseIds = Optional.ofNullable(
                        adminCrowdfundingInfoPayeeBiz.selectCaseIdsByPayeeIdCardOrderByCreateTimeDesc(payeeIdCardEncrypt, currentCaseId))
                .orElse(Lists.newArrayList());
        log.info("{} otherCaseIds: {} otherCaseIds.size(): {}", logPrefix, otherCaseIds, otherCaseIds.size());

        // 4. 查询其他案例详情（根据id排序）
        List<CrowdfundingInfo> otherInfos = CollectionUtils.isEmpty(otherCaseIds)
                ? Collections.emptyList()
                : crowdfundingInfoBiz.getListByIdsOrderById(otherCaseIds);

        // 5. 合并当前案例 + 其他案例，当前案例在第一个
        List<CrowdfundingInfo> allInfos = new LinkedList<>();
        if (currentCase != null) {
            allInfos.add(currentCase);
        }
        if (!CollectionUtils.isEmpty(otherInfos)) {
            allInfos.addAll(otherInfos);
        }
        log.debug("{} allInfos: {}", logPrefix, allInfos);

        if (CollectionUtils.isEmpty(allInfos)) {
            log.debug("{} allInfos is empty, infoUuid: {}", logPrefix, infoUuid);
            return NewResponseUtil.makeSuccess(Collections.emptyList());
        }

        // 7. 获取所有 caseId 列表
        List<Integer> caseIds = allInfos.stream()
                .map(CrowdfundingInfo::getId)
                .collect(Collectors.toList());
        log.info("{} caseIds: {}", logPrefix, caseIds);

        if (CollectionUtils.isEmpty(caseIds)) {
            log.debug("{} caseIds is empty, infoUuid: {}", logPrefix, infoUuid);
            return NewResponseUtil.makeSuccess(Collections.emptyList());
        }

        // 8. 查询患者信息（作者信息）
        List<CrowdfundingAuthor> authorList = adminCrowdfundingAuthorBiz.selectByCaseIdList(caseIds);
        log.debug("{} authorList: {}", logPrefix, authorList);

        // 转成 map 方便查找
        Map<Integer, CrowdfundingAuthor> authorMap = Optional.ofNullable(authorList)
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(CrowdfundingAuthor::getCrowdfundingId, Function.identity(), (a, b) -> a));
        log.info("{} authorMap: {}", logPrefix, authorMap);

        // 9. 封装 VO 列表
        List<MultiPayeeInfoQueryVo> res = new ArrayList<>();
        for (CrowdfundingInfo info : allInfos) {
            if (info == null) continue;

            MultiPayeeInfoQueryVo vo = new MultiPayeeInfoQueryVo();
            vo.setCaseId(info.getId());
            vo.setPayeeName(info.getPayeeName());
            vo.setPayeeBankCard(shuidiCipher.decrypt(info.getPayeeBankCard()));
            vo.setInfoId(info.getInfoId());

            CrowdfundingAuthor author = authorMap.get(info.getId());
            if (author != null && StringUtils.isNotBlank(author.getName())) {
                vo.setPatientName(author.getName());
            } else {
                vo.setPatientName("");
            }

            res.add(vo);
        }

        return NewResponseUtil.makeSuccess(res);
    }

    @RequiresPermission("approve:detail")
    @RequestMapping(path = "detail", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "案例详情审核")
    public Response<Map<String, Object>> detail(String infoUuid,
                                                @ApiParam("举报工单id为了判断高风险状态")
                                                @RequestParam(name = "reportId", required = false) Integer reportWorkOrderId) {

        Response<Map<String, Object>> result = ApproveService.detail(infoUuid, reportWorkOrderId);
        if (Objects.isNull(result) || result.notOk() || Objects.isNull(result.getData())) {
            return result;
        }
        Map<String, Object> detailMap = result.getData();
        // 前端确认该字段不再使用
        detailMap.put("cfCapitalDetail", null);
        detailMap.computeIfPresent("volunteer", (key, old) -> {
            AdminApproveService.SimpleVolunteerVo tmp = (AdminApproveService.SimpleVolunteerVo) old;
            if (StringUtils.isNotBlank(tmp.getMobile())) {
                tmp.setMobileMask(maskUtil.buildByDecryptPhone(tmp.getMobile()));
                tmp.setMobile(StringUtils.EMPTY);
                return tmp;
            }
            return old;
        });
        detailMap.computeIfPresent("photoStatus", (key, old) -> {
            PhotoStatus photoStatus = (PhotoStatus) old;
            if (StringUtils.isNotBlank(photoStatus.getIdNumber())) {
                photoStatus.setIdNumber(null);
            }
            return old;
        });

        detailMap.computeIfPresent("payeePhotoStatus", (key, old) -> {
            PhotoStatus photoStatus = (PhotoStatus) old;
            if (StringUtils.isNotBlank(photoStatus.getIdCardNumber())) {
                String idCardNumber = shuidiCipher.decrypt(photoStatus.getIdCardNumber());
                if (StringUtils.isEmpty(idCardNumber)) {
                    return photoStatus;
                }
                photoStatus.setIdNumberMask(maskUtil.buildByDecryptStrAndType(idCardNumber, DesensitizeEnum.IDCARD));
                photoStatus.setIdNumber(null);
                return photoStatus;
            }
            return old;
        });

        detailMap.computeIfPresent("idCaseData", (key, old) -> {
            CrowdfundingIdCase crowdfundingIdCase = (CrowdfundingIdCase) old;
            if (StringUtils.isNotBlank(crowdfundingIdCase.getIdCardVo())) {
                crowdfundingIdCase.setIdCardVo(null);
            }
            return old;
        });

        detailMap.computeIfPresent("cfCharityPayee", (key, old) ->
            CfCharityPayeeVO.build((CfCharityPayee) old, maskUtil)
        );

        Map<Integer, CfMaterialVerityHistory.HistoryOverviewVO> historyOverviewVOMap = (Map<Integer, CfMaterialVerityHistory.HistoryOverviewVO>) detailMap.get("approveHistoryOverview");
        if (MapUtils.isNotEmpty(historyOverviewVOMap)) {
            historyOverviewVOMap.forEach((key, value) -> {
                value.setRecordInfo(null);
            });
        }
        AdminCrowdfundingInfoView crowdfundingInfoView = (AdminCrowdfundingInfoView) detailMap.get("crowdfundingInfo");
        CrowdfundingAuthor crowdfundingAuthor = (CrowdfundingAuthor) detailMap.get("crowdfundingAuthor");
        if (Objects.isNull(crowdfundingInfoView) || Objects.isNull(crowdfundingAuthor)) {
            return result;
        }
        CrowdfundingInfoHospitalPayee crowdfundingInfoHospitalPayee = (CrowdfundingInfoHospitalPayee) detailMap.get("crowdfundingHospitalPayee");
        if (Objects.nonNull(crowdfundingInfoHospitalPayee)) {
            crowdfundingInfoHospitalPayee.setHospitalBankCard(MaskCodeUtil.maskDebitCard(crowdfundingInfoHospitalPayee.getHospitalBankCard()));
        }

        // 做一下脱敏
        crowdfundingInfoView.setPayeeIdCard(MaskCodeUtil.maskSelfCard(crowdfundingInfoView.getPayeeIdCard()));
        crowdfundingInfoView.setPayeeBankCard(MaskCodeUtil.maskDebitCard(crowdfundingInfoView.getPayeeBankCard()));
        crowdfundingAuthor.setIdCard(MaskCodeUtil.maskSelfCard(crowdfundingAuthor.getIdCard()));
        return result;
    }

    @RequiresPermission("approve:detail")
    @RequestMapping(path = "get-material-plan-id", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取案例版本号")
    public Response<Integer> getMaterialPlanId (int caseId) {
        if (caseId <= 0) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(CfErrorCode.CF_NOT_FOUND);
        }
        return NewResponseUtil.makeSuccess(crowdfundingInfo.getMaterialPlanId());
    }

    @ApiOperation("房产阈值M万")
    @PostMapping("/get-m")
    public Response<Integer> getM(@RequestParam String patientIdCard,
                                  @RequestParam String raiserIdCard,
                                  @RequestParam(required = false, defaultValue = "0") Integer caseId) {

        HouseThresholdParam houseThresholdParam = HouseThresholdParam.builder()
                .patientIdCard(patientIdCard)
                .raiserIdCard(raiserIdCard)
                .caseId(caseId)
                .build();

        Response<Integer> response = preposeMaterialRiskClient.getMResultByParam(houseThresholdParam);
        Integer result = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);
        return NewResponseUtil.makeSuccess(result);
    }

    @RequiresPermission("approve:detail")
    @RequestMapping(path = "need-collect", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "案例详情审核")
    public Response<Map<String, Object>> needCollect(String infoUuid, long workOrderId) {
        Map<String, Object> result = Maps.newHashMap();
        result.put("needSubmit", cailiaoService.needCollectElement(infoUuid, workOrderId));
        return NewResponseUtil.makeSuccess(result);
    }


    @RequiresPermission("approve:get-crowdfunding-treatment")
    @RequestMapping(path = "get-crowdfunding-treatment", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取诊断证明模块信息")
    public Response<TreatmentVO> getCrowdfundingTreatment(String infoUuid) {
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }
        TreatmentVO treatmentVO = crowdfundingUserDelegate.getCrowdfundingTreatmentVO(crowdfundingInfo.getId());
        return NewResponseUtil.makeSuccess(treatmentVO);
    }


    @RequiresPermission("approve:user-report-brief")
    @RequestMapping(path = "user-report-brief", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "举报用户简略信息")
    public Response<CfUserReportBrief> userReportBrief(@RequestParam("reportUserId") long reportUserId) {
        if (reportUserId <= 0) {
            return NewResponseUtil.makeSuccess(null);
        }
        CfUserReportBrief cfUserReportBrief = adminApproveService.getCfUserReportBrief(reportUserId);
        return NewResponseUtil.makeSuccess(cfUserReportBrief);
    }


    @RequiresPermission("approve:trustCorr")
    @RequestMapping(path = "trustCorr", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "增信重发流程")
    public Response trustCorr(String infoUuid) {

        Map<String, Object> result = Maps.newHashMap();
        CfReportAddTrust cfReportAddTrust = cfReportAddTrustBiz.getByInfoUuid(infoUuid);
        result.put("trustCorr", 0);
        if (cfReportAddTrust != null) {
            result.put("trustCorr", 99);
        }

        return NewResponseUtil.makeSuccess(result);
    }


    @RequiresPermission("approve:comment")
    @RequestMapping(path = "comment", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response comment(String infoId, String comment, int userId) {
        return adminApproveService.addComment(infoId, comment, userId, "", BackgroundLogEnum.REMARK);
    }

    @RequiresPermission("approve:edit-from")
    @RequestMapping(path = "edit-from", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response editFrom(String infoId, int userId, String from) {
        if (StringUtils.isEmpty(infoId) || StringUtils.isEmpty(from)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        CfInfoExt cfInfoExt = this.adminCfInfoExtBiz.getByInfoUuid(infoId);
        if (cfInfoExt != null && cfInfoExt.getFromType() == CfInfoFromType.BOT.getValue()) {
            return NewResponseUtil.makeError(AdminErrorCode.ADMIN_CASE_ORIGIN_HAS_BEEN_MARKED);
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoId);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }
        crowdfundingInfo.setFrom(from);
        crowdfundingInfoBiz.updateFrom(crowdfundingInfo);
        financeApproveService.addApprove(crowdfundingInfo, BackgroundLogEnum.EDIT_FROM.getMessage(), from, userId);
        // 记录该信息为运营人员添加
        this.adminCfInfoExtBiz.updateFromType(crowdfundingInfo.getInfoId(), CfInfoFromType.OPERATOR.getValue());

        // 操作记录入库
        adminApproveService.saveOperatingRecord(userId, infoId, CfOperatingRecordEnum.Type.MODIFY_SOURCE);
        cfAdminOperationRecordBiz.addOneOperationRecord(infoId, userId, CfOperationRecordEnum.EDIT_FROM.value(), "");
        LOGGER.info(
                "客服后台log：editFrom operationTime:{};operator:{};operationReason:{};operationType:{};infoId:{};status:{};dataStatus:{};infoStatus:{}",
                com.shuidihuzhu.common.web.util.DateUtil.formatDateTime(new Date()), userId,
                BackgroundLogEnum.EDIT_FROM.getMessage(), "修改来源", infoId, crowdfundingInfo.getStatus(),
                crowdfundingInfo.getDataStatus(), crowdfundingInfo.getEndTime().before(new Date()) ? "结束" : "未结束");
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("approve:edit-content")
    @RequestMapping(path = "edit-content", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response<Void> editContent(@RequestParam("infoId") String infoId,
                                      @RequestParam("userId") int userId,
                                      @RequestParam("content") String content,
                                      @RequestParam("title") String title,
                                      @RequestParam("comment") String comment) {
        if (StringUtils.isEmpty(infoId) || StringUtils.isEmpty(content) || StringUtils.isEmpty(title)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoId);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }
        AdminUserAccountModel userAccount = this.seaAccountClientV1.getValidUserAccountById(userId).getResult();
        crowdfundingInfo.setTitle(title);
        crowdfundingInfo.setContent(adminSensitiveProcessService.sensitiveProcess(content));
        crowdfundingInfo.setEncryptContent(oldShuidiCipher.aesEncrypt(content));

        CfOperatingRecord cfOperatingRecord = this.crowdfundingDelegate.before(crowdfundingInfo, userId,
                userAccount.getName(), CfOperatingRecordEnum.Type.MODIFY_CONTENT, CfOperatingRecordEnum.Role.OPERATOR);
        crowdfundingInfoBiz.updateTitleAndContent(crowdfundingInfo);
        int caseId = crowdfundingInfo.getId();
        caseInfoApproveStageFeignClient.saveStage(caseId, title, content, null);
        this.crowdfundingDelegate.afterCfOperatingRecord(cfOperatingRecord);
        // 操作记录入库
        cfAdminOperationRecordBiz.addOneOperationRecord(infoId, userId, CfOperationRecordEnum.EDIT_CONTENT.value(), "");
        // 有更新操作的话，新增操作备注
        if (StringUtils.isNotEmpty(comment)) {
            approveRemarkOldService.add(caseId, ContextUtil.getAdminUserId(), comment);
        }
//        if (StringUtils.isNotEmpty(imageIdList)) {
//            List<Integer> idList = Splitter.on(",").splitToList(imageIdList).stream()
//                    .map(Integer::valueOf)
//                    .collect(Collectors.toList());
//            adminCrowdfundingAttachmentBiz.deleteByIds(idList);
//
//        }
        LOGGER.info(
                "客服后台log：editContent operationTime:{};operator:{};operationReason:{};operationType:{};infoId:{};status:{};dataStatus:{};infoStatus:{}",
                com.shuidihuzhu.common.web.util.DateUtil.formatDateTime(new Date()), userId,
                BackgroundLogEnum.EDIT_CONTENT.getMessage(), "修改文章", infoId, crowdfundingInfo.getStatus(),
                crowdfundingInfo.getDataStatus(), crowdfundingInfo.getEndTime().before(new Date()) ? "结束" : "未结束");
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("approve:recover")
    @PostMapping("/get-all-duplicated-cases-before-recover")
    @ApiOperation("恢复筹款前，获取重复在筹案例")
    public Response<PaginationListVO<RecoverRepeatCaseVo>> getAllDuplicatedCasesBeforeRecover(@RequestParam("caseId") int caseId,
                                                                                              @RequestParam(value = "current", required = false, defaultValue = "1") int current,
                                                                                              @RequestParam(value = "pageSize", required = false, defaultValue = "10") int pageSize) {
        if (caseId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }
        PaginationListVO<RecoverRepeatCaseVo> res = cfRepeatInfoBiz.getRepeatCaseWhenRecovery(crowdfundingInfo, current, pageSize);
        return NewResponseUtil.makeSuccess(res);
    }

    @RequiresPermission("approve:recover")
    @RequestMapping(path = "recover", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response recover(String infoId, Integer userId,
                            @RequestParam(name = "commentText", required = false, defaultValue = "") String commentText) {
        if (null == infoId) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoId);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }
        //判断案例状态是否是平台主动停止
        CfInfoExt ext = this.adminCfInfoExtBiz.getByInfoUuid(infoId);
        if (!adminApproveFacade.ifCaseCanRecover(ext)) {
            return NewResponseUtil.makeError(AdminErrorCode.CASE_NOT_ALLOW_INITIAL);
        }

        int finishStatus = ext.getFinishStatus();
        if (finishStatus == CfFinishStatus.FINISH_BY_SHUIDI.getValue()) {//平台主动结束恢复筹款
            Message msg = new Message<>(MQTopicCons.CF,
                    MQTagCons.ADMIN_MQ_RESTORE_FUNDRAISING,
                    MQTagCons.ADMIN_MQ_RESTORE_FUNDRAISING + "_" + System.currentTimeMillis(),
                    ext);
            MessageResult sendResult = producer.send(msg);
            LOGGER.info("恢复筹款 send mq msg:{}, CfInfoExt:{},result:{}", msg, ext, sendResult);
        }
        AdminUserAccountModel userAccount = this.seaAccountClientV1.getValidUserAccountById(userId).getResult();
//        Date beginTime = crowdfundingInfo.getBeginTime();
//        Date endTime = DateUtils.addDays(beginTime, 30);
        CfOperatingRecord cfOperatingRecord = this.crowdfundingDelegate.before(crowdfundingInfo, userId,
                userAccount.getName(), CfOperatingRecordEnum.Type.INIT_CF, CfOperatingRecordEnum.Role.OPERATOR);
        // 执行修改案例结束状态与结束时间
//        caseEndClient.recover(crowdfundingInfo.getId(), userId, commentText);
//        List<CrowdfundingInfoStatus> crowdfundingInfoStatuses = crowdfundingDelegate.getCrowdfundingInfoStatusListByInfoUuid(infoId);
//        Set<Integer> typeSet = crowdfundingInfoStatuses.stream().map(CrowdfundingInfoStatus::getType)
//                .collect(Collectors.toSet());
//        adminApproveService.updateDataStatusByInfoUuidAndType(infoId, CrowdfundingInfoStatusEnum.UN_SUBMITTED, typeSet);
//        // 审核状态机
//        crowdfundingInfoBiz.updateStatus(crowdfundingInfo.getId(), CrowdfundingStatus.APPROVE_PENDING.value(),
//                crowdfundingInfo.getStatus().value());
//        adminApproveService.deleteRefuseMsgByInfoUuid(infoId);
        adminApproveFacade.recoverCase(crowdfundingInfo, ext, userId);
        this.crowdfundingDelegate.afterCfOperatingRecord(cfOperatingRecord);
        financeApproveService.addApprove(crowdfundingInfo, BackgroundLogEnum.CASE_RECOVER.getMessage(), commentText,
                userId);
        operationHistorySummaryBiz.addOperationHistorySummary(OperationType.CROWDFUNDING_RECOVER, userId,
                "筹款恢复, infoId=" + infoId);
        try {
            LOGGER.info("updateOperationStatus infoId:{};userId:{};commentText:{}", infoId, userId, commentText);
            adminApproveService.updateOperationStatus(infoId, CrowdfundingOperationEnum.OPERATED, commentText, userId);
        } catch (Exception e) {
            LOGGER.error("updateOperationStatus Error!", e);
        }

        // 通过高风险
        CaseRaiseRiskDO riskDO = riskDelegate.getByInfoUuid(infoId);
        if (riskDO != null) {
            riskDO.setPassed(1);
            riskDelegate.saveRaiseRisk(riskDO);
        }

        // 操作记录入库
        cfAdminOperationRecordBiz.addOneOperationRecord(infoId, userId, CfOperationRecordEnum.INITIALIZATION.value(),
                commentText);
        LOGGER.info(
                "客服后台log：recover operationTime:{};operator:{};operationReason:{};operationType:{};infoId:{};status:{};dataStatus:{};infoStatus:{}",
                com.shuidihuzhu.common.web.util.DateUtil.formatDateTime(new Date()), userId,
                BackgroundLogEnum.INITIALIZATION.getMessage(), BackgroundLogEnum.INITIALIZATION.getMessage(), infoId,
                crowdfundingInfo.getStatus(), crowdfundingInfo.getDataStatus(),
                crowdfundingInfo.getEndTime().before(new Date()) ? "结束" : "未结束");

        // TODO: 业务日志
        return NewResponseUtil.makeResponse(0, "请重新审核驳回的材料", null);
    }

    @RequiresPermission("approve:special-recover")
    @RequestMapping(path = "special-recover", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response<Void> specialRecover(String infoId,
                                         @RequestParam(name = "commentText", required = false, defaultValue = "") String commentText) {
        int adminUserId = ContextUtil.getAdminUserId();
        if (StringUtils.isBlank(infoId)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoId);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }
        CfInfoExt ext = this.adminCfInfoExtBiz.getByInfoUuid(infoId);
        if (!adminApproveFacade.ifCaseCanSpecialRecover(ext)) {
            return NewResponseUtil.makeError(AdminErrorCode.CASE_NOT_ALLOW_INITIAL);
        }

        int finishStatus = ext.getFinishStatus();
        if (finishStatus == CfFinishStatus.FINISH_BY_SHUIDI.getValue()) {//平台主动结束恢复筹款
            Message msg = new Message<>(MQTopicCons.CF, MQTagCons.ADMIN_MQ_RESTORE_FUNDRAISING, MQTagCons.ADMIN_MQ_RESTORE_FUNDRAISING + "_" + System.currentTimeMillis(), ext);
            MessageResult sendResult = producer.send(msg);
            LOGGER.info("恢复筹款 send mq msg:{}, CfInfoExt:{},result:{}", msg, ext, sendResult);
        }
        AdminUserAccountModel userAccount = this.seaAccountClientV1.getValidUserAccountById(adminUserId).getResult();
        CfOperatingRecord cfOperatingRecord = this.crowdfundingDelegate.before(crowdfundingInfo, adminUserId,
                userAccount.getName(), CfOperatingRecordEnum.Type.INIT_CF, CfOperatingRecordEnum.Role.OPERATOR);
        adminApproveFacade.recoverCase(crowdfundingInfo, ext, adminUserId);
        this.crowdfundingDelegate.afterCfOperatingRecord(cfOperatingRecord);
        financeApproveService.addApprove(crowdfundingInfo, BackgroundLogEnum.CASE_SPECIAL_RECOVER.getMessage(), commentText, adminUserId);
        operationHistorySummaryBiz.addOperationHistorySummary(OperationType.CROWDFUNDING_RECOVER, adminUserId, "筹款恢复, infoId=" + infoId);
        try {
            LOGGER.info("updateOperationStatus infoId:{};userId:{};commentText:{}", infoId, adminUserId, commentText);
            adminApproveService.updateOperationStatus(infoId, CrowdfundingOperationEnum.OPERATED, commentText, adminUserId);
        } catch (Exception e) {
            LOGGER.error("updateOperationStatus Error!", e);
        }
        // 通过高风险
        CaseRaiseRiskDO riskDO = riskDelegate.getByInfoUuid(infoId);
        if (riskDO != null) {
            riskDO.setPassed(1);
            riskDelegate.saveRaiseRisk(riskDO);
        }
        // 操作记录入库
        cfAdminOperationRecordBiz.addOneOperationRecord(infoId, adminUserId, CfOperationRecordEnum.INITIALIZATION.value(), "【特殊恢复筹款】" + commentText);
        commonOperationRecordClient.create()
                .buildBasicPlatform(crowdfundingInfo.getId(), adminUserId, OperationActionTypeEnum.CASE_SPECIAL_RECOVER)
                .buildRemark("【特殊恢复筹款】" + commentText)
                .save();
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("approve:id-verify")
    @RequestMapping(value = "/admin/crowdfunding/id-verify", method = RequestMethod.POST)
    public Response doIdVerify(@RequestParam("infoUuid") String infoUuid) {

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        CrowdfundingIdCase crowdfundingIdCase = crowdfundingDelegate.getCrowdfundingIdCaseByInfoId(crowdfundingInfo.getId());
        if (crowdfundingIdCase == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (crowdfundingIdCase.getStatus() == CrowdfundingIdCaseStatusEnum.VERIFY_SUCCESS.getCode()) {
            return Response.OK;
        }

        VerifyIdcardVO verifyIdcardVO = idCardVerifyService.verfiy(crowdfundingIdCase.getName(), crowdfundingIdCase.getIdCardVo(), UserRelTypeEnum.SELF, crowdfundingInfo.getUserId());
        if (verifyIdcardVO.getCode() == IdCardVerifyResultEnum.MATCH) {

            crowdfundingDelegate.updateCrowdfundingIdCaseStatus(crowdfundingInfo.getId(), CrowdfundingIdCaseStatusEnum.VERIFY_SUCCESS);

            crowdfundingDelegate.updateCrowdfundingInfoStatusByInfoId(crowdfundingInfo.getInfoId(), CrowdfundingInfoDataStatusTypeEnum.ID_VERIFY.getCode(), CrowdfundingInfoStatusEnum.PASSED);

            return Response.OK;
        } else {
            return NewResponseUtil.makeError(AdminErrorCode.OPERATION_FAILED);
        }

    }

    /**
     * 材审处理
     * - 参数检查
     * - 业务处理检查
     * - 审核数据处理
     * - 工单处理
     * - 处理通知
     */
    @RequiresPermission("approve:do-approve")
    @RedisDistributedLock(key = "do_approve_#{infoUuid}")
    @RequestMapping(path = "do-approve", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response<?> doApprove(
            @RequestParam(required = false, defaultValue = "false") boolean payeeRepeatWarn,
            @RequestParam String param,
            @RequestParam String infoUuid,
            @RequestParam int userId,
            @ApiParam("医院核实工单id") @RequestParam(required = false, defaultValue = "0") long hospitalAuditWorkOrderId,
            @RequestParam(required = false, defaultValue = "") String commentText) {
        // 根源：没有传递 yanhouWorkOrderId，也就是yanhouWorkOrderId为0
        LOGGER.info("CrowdfundingApproveController doApprove param:{}, infoUuid:{}, commentText:{}", param, infoUuid, commentText);
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        AdminApproveVo adminApproveVo;
        try {
            // 没有传递 yanhouWorkOrderId，也就是yanhouWorkOrderId为0
            adminApproveVo = JSON.parseObject(param, AdminApproveVo.class);
        } catch (Exception e) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_JSON_PARSE_ERROR);
        }
        if (adminApproveVo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        Set<Integer> refuseDataTypeSet = getRefuseDataTypeSet(adminApproveVo.getRefuseIds());
        Response<?> logicCheck = handleWithLogicCheck(userId, crowdfundingInfo, adminApproveVo, refuseDataTypeSet);
        if (logicCheck.notOk()) {
            return logicCheck;
        }
        //
        Response<?> checkRepeatLock = checkRepeatLock(userId, crowdfundingInfo.getId(), adminApproveVo);
        if (checkRepeatLock.notOk()) {
            return checkRepeatLock;
        }
        ApproveAuditParam approveAuditParam = ApproveAuditParam.builder()
                .userId(userId)
                .hospitalAuditWorkOrderId(hospitalAuditWorkOrderId)
                .commentText(commentText)
                .crowdfundingInfo(crowdfundingInfo)
                .adminApproveVo(adminApproveVo)
                .refuseDataTypeSet(refuseDataTypeSet)
                .payeeRepeatWarn(payeeRepeatWarn)
                .build();
        // 处理财审通过的地方
        return approveAuditService.handleLogic(approveAuditParam);
    }

    private Response<?> checkRepeatLock(int userId, int caseId, AdminApproveVo adminApproveVo) {
        // 检查重复处理锁
        Response<ApproveControlRecordDO> checkLockResult = approveControlService.checkLock(caseId, userId);
        if (checkLockResult.notOk()) {
            return checkLockResult;
        }
        ApproveControlRecordDO controlRecord = checkLockResult.getData();
        if (Objects.isNull(controlRecord)) {
            return NewResponseUtil.makeSuccess();
        }
        adminApproveVo.setControlRecordId(controlRecord.getId());
        // 自动领取的工单从控制表获取id
        if (controlRecord.getFlowType() != ApproveControlFlowTypeEnum.AUTO_WORK_ORDER_FLOW.getValue()) {
            return NewResponseUtil.makeSuccess();
        }
        Response<WorkOrderVO> workOrderResp = cfWorkOrderClient.getWorkOrderById(controlRecord.getWorkOrderId());
        if (workOrderResp.notOk()) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        WorkOrderVO data = workOrderResp.getData();
        adminApproveVo.setOrderType(data.getOrderType());
        adminApproveVo.setYanhouWorkOrderId(controlRecord.getWorkOrderId());
        return NewResponseUtil.makeSuccess();
    }

    @NotNull
    private Response<?> handleWithLogicCheck(int userId, CrowdfundingInfo crowdfundingInfo, AdminApproveVo adminApproveVo, Set<Integer> refuseDataTypeSet) {

        int caseId = crowdfundingInfo.getId();
        long yanhouWorkOrderId = adminApproveVo.getYanhouWorkOrderId();
        List<Integer> passIds = adminApproveVo.getPassIds();
        List<Integer> refuseIds = adminApproveVo.getRefuseIds();
        if (!initialAuditSearchService.initialPass(crowdfundingInfo.getInfoId())) {
            return NewResponseUtil.makeError(AdminErrorCode.OPERATE_MATERIAL_MUST_INITIAL_AUDIT);
        }
        // yanhouWorkOrderId为0，这里返回false，也就是可以继续处理
        boolean canNotHandle = workOrderReadService.checkCanNotHandle(yanhouWorkOrderId, userId);
        if (canNotHandle) {
            return NewResponseUtil.makeError(AdminErrorCode.ORDER_HAS_CALLBACK);
        }

        if (CollectionUtils.isNotEmpty(passIds) && CollectionUtils.isNotEmpty(refuseIds) && passIds.stream().anyMatch(refuseDataTypeSet::contains)) {
            return NewResponseUtil.makeError(AdminErrorCode.APPROVE_PASS_REFUSE);
        }

        if (!CollectionUtils.isEmpty(passIds) && !AdminListUtil.getList(10, (start, size) -> cfCommitVerifyItemBiz.selectAllType(start, size)).containsAll(passIds)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        if (refuseDataTypeSet.contains(CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT.getCode())) {
            CfInfoSimpleModel cfInfoSimpleModel = crowdfundingDelegate.getCfInfoSimpleModel(crowdfundingInfo.getInfoId());
            FeignResponse<CfPayeeInfoChangeView> changeViewFeignResponse = cfPayeeFeignClient.getPayeeInfoRecord(cfInfoSimpleModel);
            if (null != changeViewFeignResponse && null != changeViewFeignResponse.getData() && 1 == changeViewFeignResponse.getData().getIsChangePayee()) {
                return NewResponseUtil.makeError(AdminErrorCode.APPROVE_REFUSE_PAYEE);
            }
        }

        // 校验主动服务工单处理参数
        if (adminApproveVo.getOrderType() == WorkOrderType.cailiao_fuwu.getType() && yanhouWorkOrderId > 0 && adminApproveVo.getHandleResult() <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_IS_NULL);
        }

        Response<Void> limitContentWorkOrder = limitContentWorkOrder(caseId, passIds, refuseDataTypeSet);
        if (Objects.nonNull(limitContentWorkOrder)) {
            return limitContentWorkOrder;
        }
        return NewResponseUtil.makeSuccess();
    }

    private Set<Integer> getRefuseDataTypeSet(List<Integer> refuseIds) {
        if (CollectionUtils.isEmpty(refuseIds)) {
            return Collections.emptySet();
        }
        List<CfRefuseReasonEntity> refuseEntityList = adminApproveService.getRefuseEntityList(refuseIds);
        Set<String> itemIds = refuseEntityList.stream()
                .map(value -> Splitter.on(",").splitToList(value.getItemIds())).flatMap(Collection::stream)
                .collect(Collectors.toSet());
        List<CfRefuseReasonItem> refuseReasonItem = adminApproveService
                .getRefuseReasonItem(itemIds.stream().map(Integer::valueOf).collect(Collectors.toSet()));
        return refuseReasonItem.stream().map(CfRefuseReasonItem::getType).distinct().collect(Collectors.toCollection(Sets::newHashSet));
    }

    private Response<Void> limitContentWorkOrder(int caseId, List<Integer> passIdList, Set<Integer> refuseDataTypeSet) {
        if (!passIdList.contains(CrowdfundingInfoDataStatusTypeEnum.BASE_INFO_SUBMIT.getCode()) && !refuseDataTypeSet.contains(CrowdfundingInfoDataStatusTypeEnum.BASE_INFO_SUBMIT.getCode())) {
            return null;
        }
        Response<List<WorkOrderVO>> response = cfWorkOrderClient.listByCaseIdAndTypeAndResult(caseId, Lists.newArrayList(WorkOrderType.content.getType()), HandleResultEnum.unDoResult());
        List<WorkOrderVO> list = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(Lists.newArrayList());

        if (CollectionUtils.isNotEmpty(list)) {
            return NewResponseUtil.makeResponse(AdminErrorCode.CAILIAO_WORK_ORDER_LIMIT_CONTENT_WORK_ORDER.getCode(), AdminErrorCode.CAILIAO_WORK_ORDER_LIMIT_CONTENT_WORK_ORDER.getMsg(), null);
        }
        return null;
    }

    @RequiresPermission("approve:pause")
    @RequestMapping(path = "pause", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response pause(Integer id, Integer userId,
                          @RequestParam(name = "stopReasonId", required = false, defaultValue = "0") int stopReasonId,
                          @RequestParam(name = "commentText", required = false, defaultValue = "") String commentText,
                          @RequestParam(name = "operationRemark", required = false, defaultValue = "") String operationRemark) {
        LOGGER.info("CrowdfundingOperationController infoId={},userId={},operationStatus={}", id, userId);
        return adminApproveFacade.stopCase(id, userId, BackgroundLogEnum.OVER.getMessage(), stopReasonId, commentText, operationRemark);
    }


    @RequiresPermission("approve:verify-bank-card")
    @RequestMapping(path = "/verify-bank-card", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response verifyBankCardBase(String infoUuid, int userId) {
        LOGGER.info("verifyBankCardBase", infoUuid);
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }
        if (BankCardVerifyStatus.passed.equals(crowdfundingInfo.getBankCardVerifyStatus())) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_BANK_CARD_ALREADY_VERIFIED);
        }
        int crowdfundingId = crowdfundingInfo.getId();
        String cryptoHolderName = StringUtils.trimToEmpty(crowdfundingInfo.getPayeeName());
        UserIdentityType identityType = UserIdentityType.identity;
        String cryptoIdCard = null;
        try {
            cryptoIdCard = shuidiCipher.decrypt(crowdfundingInfo.getPayeeIdCard());
        } catch (Exception e) {
            cryptoIdCard = crowdfundingInfo.getPayeeIdCard();
        }
        String cryptoBankCard = null;
        try {
            cryptoBankCard = shuidiCipher.decrypt(crowdfundingInfo.getPayeeBankCard());
        } catch (Exception e) {
            cryptoBankCard = crowdfundingInfo.getPayeeBankCard();
        }
        if (StringUtils.isBlank(cryptoHolderName) || StringUtils.isBlank(cryptoIdCard)
                || StringUtils.isBlank(cryptoBankCard)) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_PARAM_ERROR_PAYEE);
        }
        BankCardVerifyResult bankCardVerifyResult = commonServiceDelegate.verify(cryptoHolderName, identityType,
                cryptoIdCard, cryptoBankCard, crowdfundingId, crowdfundingInfo.getUserId(), true);
        boolean result = false;
        if (bankCardVerifyResult != null && bankCardVerifyResult.isOk()) {
            result = true;
        }
        // 操作记录入库
        try {
            AdminUserAccountModel adminUserAccountModel = this.seaAccountClientV1.getValidUserAccountById(userId).getResult();
            String userName = adminUserAccountModel.getName();
            this.crowdfundingOperationDelegate.saveCfOperatingRecord(infoUuid, userId, userName, CfOperatingRecordEnum.Type.VERIFY_BARKCARD,
                    CfOperatingRecordEnum.Role.OPERATOR);
        } catch (Exception e) {
            LOGGER.error("", e);
        }
        cfAdminOperationRecordBiz.addOneOperationRecord(crowdfundingInfo.getInfoId(), userId,
                CfOperationRecordEnum.VERIFY_BANK_CARD.value(), "");
        LOGGER.info(
                "客服后台log：verifyBankCardBase operationTime:{};operator:{};operationReason:{};operationType:{};infoId:{};status:{};dataStatus:{};infoStatus:{}",
                com.shuidihuzhu.common.web.util.DateUtil.formatDateTime(new Date()), userId,
                BackgroundLogEnum.VERIFY_BANK_CARD.getMessage(), BackgroundLogEnum.VERIFY_BANK_CARD.getMessage(),
                crowdfundingInfo.getInfoId(), crowdfundingInfo.getStatus(), crowdfundingInfo.getDataStatus(),
                crowdfundingInfo.getEndTime().before(new Date()) ? "结束" : "未结束");
        return NewResponseUtil.makeSuccess(result);
    }

    @RequiresPermission("approve:send-message")
    @RequestMapping(path = "send-message", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response sendMessage(String infoId, String mobile, String message, Integer userId,
                                @RequestParam(required = false, name = "modelNum") String modelNum,
                                @RequestParam(required = false, name = "param", defaultValue = "") String param) {
        LOGGER.info("SendMessage infoId:{};mobile:{};message:{}", infoId, mobile, message);
        mobile = StringUtils.trimToEmpty(mobile);
        if (MobileUtil.illegal(mobile)) {
            return NewResponseUtil.makeError(AdminErrorCode.MOBILE_FORMAT_ERROR);
        }
        if ((StringUtils.isBlank(message) && StringUtils.isBlank(modelNum)) ||
                (StringUtils.isNotBlank(message) && StringUtils.isNotBlank(modelNum))) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_IS_NULL);
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoId);
        cfAdminOperationRecordBiz.addOneOperationRecord(infoId, userId, CfOperationRecordEnum.SEND_MESSAGE.value(), "");
        LOGGER.info(
                "客服后台log：SendMessage operationTime:{};operator:{};operationReason:{};operationType:{};infoId:{};status:{};dataStatus:{};infoStatus:{}",
                com.shuidihuzhu.common.web.util.DateUtil.formatDateTime(new Date()), userId,
                BackgroundLogEnum.SEND_MESSAGE.getMessage(), BackgroundLogEnum.SEND_MESSAGE.getMessage(), infoId,
                crowdfundingInfo.getStatus(), crowdfundingInfo.getDataStatus(),
                crowdfundingInfo.getEndTime().before(new Date()) ? "结束" : "未结束");

//        Response response = smsBiz.sendSms(mobile, message, RecordConstant.MSG_TYPE_USER,
//                RecordConstant.BIZ_TYPE_AIXINCHOU, RecordConstant.SUB_BIZ_TYPE_U_CF_NOTICE);

        // 检查是否下发的是举报增信的短信
        boolean result = adminApproveService.checkModelNum(modelNum, mobile, crowdfundingInfo);
        if (!result) {
            Map<Integer, String> paramMap = Maps.newHashMap();
            if (StringUtils.isNotEmpty(param)) {
                paramMap = JSON.parseObject(param, new TypeReference<Map<Integer, String>>() {});
            }
            adminApproveService.sendCaseApproveSms(mobile, message, modelNum, paramMap);
        }

        try {
            AdminUserAccountModel adminUserAccountModel = this.seaAccountClientV1.getValidUserAccountById(userId).getResult();
            String userName = adminUserAccountModel.getName();
            int caseId = crowdfundingInfo.getId();
            String smsContent = "";

            if (StringUtils.isNotBlank(message)) {
                // 添加备注
                String msgName = sendMsgTemplateBiz.getMsgTitleByContent(message);
                if (StringUtils.isBlank(msgName)) {
                    financeApproveService.addApprove(crowdfundingInfo, "发送短信", message + "\n 发送手机号为：【" + mobile + "】",
                            userId);
                } else {
                    financeApproveService.addApprove(crowdfundingInfo, "发送短信",
                            "标题为【" + msgName + "】" + "\n 发送手机号为：【" + mobile + "】", userId);
                }
                smsContent = message;
            } else {
                List<SmsTemplate> smsTemplates = sendMsgTemplateBiz.getTemplateByModelNum(modelNum);
                String smsMsg = (CollectionUtils.isEmpty(smsTemplates) || smsTemplates.size() > 1) ? "" : smsTemplates.get(0).getText();
                if (StringUtils.isNotEmpty(param)) {
                    Map<Integer, String> paramMap = JSON.parseObject(param, new TypeReference<Map<Integer, String>>() {
                    });
                    for (Map.Entry<Integer, String> entry : paramMap.entrySet()) {
                        smsMsg = smsMsg.replace("{" + entry.getKey() + "}", entry.getValue());
                    }
                }
                financeApproveService.addApprove(crowdfundingInfo, "发送短信", smsMsg + "\n 发送手机号为：【" + mobile + "】",
                        userId);
                smsContent = smsMsg;
            }

            // 保存短信记录供用户管理页面查询
            smsRecordService.save(caseId, userId, mobile, modelNum, smsContent);

            this.crowdfundingOperationDelegate.saveCfOperatingRecord(infoId, userId, userName, CfOperatingRecordEnum.Type.SEND_SMS,
                    CfOperatingRecordEnum.Role.OPERATOR);

        } catch (Exception e) {
            LOGGER.error("", e);
        }
        return ResponseUtil.makeSuccess("");
    }

    @RequiresPermission("approve:add-update-contact")
    @RequestMapping(path = "add-update-contact", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response addOrUpdateContact(String infoId, Integer userId, String contact) {
        LOGGER.info("addOrUpdateContact infoId:{};userId:{};contact:{}", infoId, userId, contact);
        if (StringUtils.isNotBlank(infoId) && StringUtils.isNotBlank(contact)) {
            CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoId);
            if (crowdfundingInfo == null) {
                return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
            }
            try {
                cfAdminOperationRecordBiz.addOneOperationRecord(infoId, userId,
                        CfOperationRecordEnum.ADD_UPDATE_CONTACT.value(), contact);
            } catch (Exception e) {
                LOGGER.error("addOrUpdateContact error", e);
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
            }
            LOGGER.info(
                    "客服后台log：addOrUpdateContact operationTime:{};operator:{};operationReason:{};operationType:{};infoId:{};status:{};dataStatus:{};infoStatus:{}",
                    com.shuidihuzhu.common.web.util.DateUtil.formatDateTime(new Date()), userId,
                    BackgroundLogEnum.ADD_UPDATE_CONTACT.getMessage(),
                    BackgroundLogEnum.ADD_UPDATE_CONTACT.getMessage(), infoId, crowdfundingInfo.getStatus(),
                    crowdfundingInfo.getDataStatus(), crowdfundingInfo.getEndTime().before(new Date()) ? "结束" : "未结束");
            return NewResponseUtil.makeSuccess(null, "success");
        } else {
            LOGGER.error("addOrUpdateContact error infoId:{};userId:{};contact:{}", infoId, userId, contact);
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
        }
    }

    @RequiresPermission("approve:update-treatment-name")
    @RequestMapping(path = "update-Treatment-name", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response updateTreatmentName(@RequestParam(required = false, name = "crowdfundingId") Integer crowdfundingId,
                                        @RequestParam(required = false, name = "diseaseName") String diseaseName,
                                        @RequestParam(required = false, name = "hospitalName") String hospitalName,
                                        @RequestParam(required = false, name = "diagnoseHospitalName") String diagnoseHospitalName,
                                        @RequestParam(required = false, name = "userId") Integer userId) {
        if (crowdfundingId == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (userId == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_NOT_LOGIN_ERROR);
        }
        AdminUserAccountModel userAccount = this.seaAccountClientV1.getValidUserAccountById(userId).getResult();
        if (userAccount == null) {
            return NewResponseUtil.makeError(AdminErrorCode.ADMIN_ACCOUNT_NOT_EXISTS);
        }
        CrowdfundingTreatment crowdfundingTreatment = this.crowdfundingUserDelegate.getCrowdfundingTreatment(crowdfundingId);
        if (crowdfundingTreatment == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = this.crowdfundingInfoBiz.getFundingInfoById(crowdfundingId);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        String changeDiseaseName = null;
        String changeHospitalName = null;
        String changeDiagnoseHospitalName = null;
        if (StringUtils.isNoneBlank(diseaseName)) {
            if (diseaseName.length() > 50) {
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_TOO_LONG);
            }
            String beforeDiseaseName = crowdfundingTreatment.getDiseaseName();
            crowdfundingTreatment.setDiseaseName(diseaseName);
            changeDiseaseName = "由\"" + beforeDiseaseName + "\"修改为\"" + diseaseName + "\"";
        }
        if (StringUtils.isNoneBlank(hospitalName)) {
            String beforeHospitalName = crowdfundingTreatment.getHospitalName();
            crowdfundingTreatment.setHospitalName(hospitalName);
            changeHospitalName = "由\"" + beforeHospitalName + "\"修改为\"" + hospitalName + "\"";
        }
        String beforeDiagnoseHospitalName = crowdfundingTreatment.getDiagnoseHospitalName();
        if (StringUtils.isNoneBlank(diagnoseHospitalName)) {
            beforeDiagnoseHospitalName = crowdfundingTreatment.getDiagnoseHospitalName();
            crowdfundingTreatment.setDiagnoseHospitalName(diagnoseHospitalName);
            changeDiagnoseHospitalName = "由\"" + beforeDiagnoseHospitalName + "\"修改为\"" + diagnoseHospitalName + "\"";
        }
        CfOperatingRecord cfOperatingRecord = null;
        try {
            cfOperatingRecord = this.crowdfundingDelegate.before(crowdfundingInfo, userId, userAccount.getName(),
                    CfOperatingRecordEnum.Type.UPDATE_TREATMENT_NAME, CfOperatingRecordEnum.Role.OPERATOR);
        } catch (Exception e) {
            LOGGER.error("error:", e);
        }
        try {
            this.crowdfundingUserDelegate.updateCrowdfundingTreatment(crowdfundingTreatment);
        } catch (Exception e) {
            LOGGER.error("CrowdfundingApproveController updateTreatmentName error:", e);
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        try {
            if (changeDiseaseName != null) {
                financeApproveService.addApprove(crowdfundingInfo, "疾病名称", changeDiseaseName, userId);
            }
            if (changeHospitalName != null) {
                if (StringUtils.isBlank(beforeDiagnoseHospitalName)) {
                    financeApproveService.addApprove(crowdfundingInfo, "就诊/确诊医院名称", changeHospitalName, userId);
                } else {
                    financeApproveService.addApprove(crowdfundingInfo, "就诊医院名称", changeHospitalName, userId);
                }
            }
            if (changeDiagnoseHospitalName != null) {
                financeApproveService.addApprove(crowdfundingInfo, "确诊医院名称", changeDiagnoseHospitalName, userId);
            }
            if (cfOperatingRecord != null) {
                this.crowdfundingDelegate.afterCfOperatingRecord(cfOperatingRecord);
            }
        } catch (Exception e) {
            LOGGER.error("error:", e);
        }
        return NewResponseUtil.makeSuccess("修改成功");
    }

    @ApiOperation("下发补充动态")
    @RequiresPermission("approve:push-supplement")
    @RequestMapping(path = "push-supplement", method = RequestMethod.POST)
    public Response pushSupplement(String infoUuid, String reason) {
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        approveRemarkOldService.add(crowdfundingInfo.getId(), ContextUtil.getAdminUserId(), "下发补充动态", reason);
        return NewResponseUtil.makeSuccess(null);
    }

    /**
     * 添加强提示
     * 临时接口,处理逻辑不加入到service层
     *
     * @param infoUuid
     * @param reason
     * @return
     */
    @ApiOperation("添加强提示内容")
    @RequiresPermission("approve:add-reminder")
    @RequestMapping(path = "add-reminder", method = RequestMethod.POST)
    public Response addReminder(String infoUuid, String reason) {
        if (StringUtils.isEmpty(infoUuid) || StringUtils.isEmpty(reason) || reason.length() > 50) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        int userId = ContextUtil.getAdminUserId();
        if (userId <= 0) {
            return NewResponseUtil.makeError(CfErrorCode.USER_ACCOUNT_NO_LOGIN);
        }
        AdminUserAccountModel userAccountModel = seaAccountClientV1.getValidUserAccountById(userId).getResult();
        if (null == userAccountModel) {
            return NewResponseUtil.makeError(CfErrorCode.USER_ACCOUNT_NOT_EXISTS);
        }
        List<CfOperatingRecord> operatingRecordList = adminCfOperatingRecordBiz.getAllOperateByInfoUuidAndType(infoUuid,
                CfOperatingRecordEnum.Type.ADD_REMINDER.getCode());
        for (CfOperatingRecord cfOperatingRecord : operatingRecordList) {
            adminCfOperatingRecordBiz.deleteById(infoUuid, cfOperatingRecord.getId());
        }

        //构建记录
        CfOperatingRecord operatingRecord = new CfOperatingRecord();
        operatingRecord.setInfoUuid(infoUuid);
        operatingRecord.setType(CfOperatingRecordEnum.Type.ADD_REMINDER.getCode());
        operatingRecord.setRole(CfOperatingRecordEnum.Role.OPERATOR.getCode());
        operatingRecord.setUserId(userId);
        operatingRecord.setUserName(userAccountModel.getName());
        operatingRecord.setComment(reason);

        crowdfundingOperationDelegate.insertOperatingRecord(operatingRecord);
        return NewResponseUtil.makeSuccess(operatingRecord);
    }

    /**
     * 查询强提示
     * 临时接口,处理逻辑不加入到service层
     *
     * @param infoUuid
     * @return
     */
    @ApiOperation("查询强提示内容")
    @RequiresPermission("approve:select-reminder")
    @RequestMapping(path = "select-reminder", method = RequestMethod.POST)
    public Response selectReminder(String infoUuid, int caseId) {
        if (StringUtils.isEmpty(infoUuid) || caseId <= 0) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<CfOperatingRecord> operatingRecordList = adminCfOperatingRecordBiz.getAllOperateByInfoUuidAndType(infoUuid,
                CfOperatingRecordEnum.Type.ADD_REMINDER.getCode());
        FeignResponse<CfCashPauseBooleanPo> feignResponse = cfFinancePauseFeignClient.checkPauseDrawCash(infoUuid, caseId);
        log.info("infoId:{}\tfeignResponse:{}", caseId, JSON.toJSON(feignResponse));
        boolean pauseDrawCash = feignResponse.notOk() || null == feignResponse.getData() ? false : feignResponse.getData().isPause();
        AdminReminderVo adminReminderVo = new AdminReminderVo();
        adminReminderVo.setOperatingRecordList(operatingRecordList);
        adminReminderVo.setIsdrawCashPause(pauseDrawCash);

        return NewResponseUtil.makeSuccess(adminReminderVo);
    }

    /**
     * 删除强提示
     * 临时接口,处理逻辑不加入到service层
     *
     * @param infoUuid
     * @param reminderId
     * @return
     */
    @ApiOperation("删除强提示内容")
    @RequiresPermission("approve:delete-reminder")
    @RequestMapping(path = "delete-reminder", method = RequestMethod.POST)
    public Response deleteReminder(String infoUuid, int reminderId) {
        if (StringUtils.isEmpty(infoUuid) || reminderId <= 0) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        int userId = ContextUtil.getAdminUserId();
        if (userId <= 0) {
            return NewResponseUtil.makeError(CfErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        AdminUserAccountModel userAccountModel = seaAccountClientV1.getValidUserAccountById(userId).getResult();
        if (null == userAccountModel) {
            return NewResponseUtil.makeError(CfErrorCode.USER_ACCOUNT_NOT_EXISTS);
        }
        int count = adminCfOperatingRecordBiz.deleteById(infoUuid, reminderId);

        if (count == 0) {
            return NewResponseUtil.makeSuccess(null);
        }

        //构建记录
        CfOperatingRecord operatingRecord = new CfOperatingRecord();
        operatingRecord.setInfoUuid(infoUuid);
        operatingRecord.setType(CfOperatingRecordEnum.Type.DELETE_REMINDER.getCode());
        operatingRecord.setRole(CfOperatingRecordEnum.Role.OPERATOR.getCode());
        operatingRecord.setUserId(userId);
        operatingRecord.setUserName(userAccountModel.getName());
        operatingRecord.setComment("删除强提示---" + reminderId);
        //借refuseCount 记录被删除的reminderId
        operatingRecord.setRefuseCount(reminderId);

        crowdfundingOperationDelegate.insertOperatingRecord(operatingRecord);
        return NewResponseUtil.makeSuccess(null);
    }


    @ApiOperation("标记筹款结束展示")
    @RequiresPermission("approve:show-finishStr")
    @RequestMapping(path = "show-finishStr", method = RequestMethod.POST)
    public Response showFinishStr(String infoUuid, String finishStr) {

        if (StringUtils.isEmpty(infoUuid) || StringUtils.isEmpty(finishStr)) {

            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        adminCfInfoExtBiz.updateFinishStr(infoUuid, finishStr);

        return NewResponseUtil.makeSuccess(null);

    }

    @ApiOperation("获取举报列与评论表")
    @RequiresPermission("approve:get-report-comment-list")
    @RequestMapping(path = "get-report-comment-list", method = RequestMethod.POST)
    public Response getReportList(int infoId) {
        Map<String, Object> result = Maps.newHashMap();
        //普通评论数据
        List<CrowdfundingApproveCommentVo> cfReportFollowList = Lists.newArrayList();
        Map<String, List<CrowdfundingApproveCommentVo>> commentsMap = adminApproveService.getCommentVoList(infoId);
        List<CfReportFollowComment> commentList = this.cfReportFollowCommentBiz.getCommentListByInfoId(infoId);
        cfReportFollowList.addAll(commentsMap.get("comments"));

        if (CollectionUtils.isNotEmpty(commentList)) {
            cfReportFollowList.addAll(commentList.stream().map(this::convertFollowVO).collect(Collectors.toList()));
        }

        //举报工单信息
        List<CfReportView> crowdfundingReportList = adminApproveService.getReportList(infoId);
        result.put("crowdfundingReportList", crowdfundingReportList);
        result.put("commentMap", this.buildResult(cfReportFollowList));
        result.put("operation", commentsMap.get("operation"));
        return NewResponseUtil.makeSuccess(result);

    }

    @ApiOperation("获取风险案例数据表")
    @RequestMapping(path = "get-remark-list", method = RequestMethod.POST)
    @RequiresPermission("approve:get-remark-list")
    public Response getRemarkLog(int infoId) {
        List<RemarkDO> remarkDOS101 = crowdfundingOperationDelegate.listByCaseIdAndRemarkTypes(infoId, Lists.newArrayList(101));
        List<CrowdfundingApproveCommentVo> voList101 = remarkDOS101.stream().map(this::convertVO).collect(Collectors.toList());

        List<RemarkDO> remarkDOS102 = crowdfundingOperationDelegate.listByCaseIdAndRemarkTypes(infoId, Lists.newArrayList(102));
        List<CrowdfundingApproveCommentVo> voList102 = remarkDOS102.stream().map(this::convertVO).collect(Collectors.toList());

        String infoUuid = crowdfundingInfoBiz.getFundingInfoById(infoId).getInfoId();

        List<VisitConfigLogDO> visitConfigLogDOS = commonServiceDelegate.listByCondition(infoUuid, Lists.newArrayList(),
                Lists.newArrayList());
        List<CrowdfundingApproveCommentVo> list = visitConfigLogDOS.stream().map(this::transformDO2VO).collect(Collectors.toList());

        CfRiskHintView cfRiskHintView = new CfRiskHintView();
        cfRiskHintView.setRemarkDOList101(voList101);
        cfRiskHintView.setRemarkDOList102(voList102);
        cfRiskHintView.setVisitConfigLogVOS(list);


        int lengt = remarkDOS101.size() + remarkDOS102.size() + list.size();
        cfRiskHintView.setRiskCommentCount(lengt);

        OpResult<CfCaseRiskDO> cfCaseRiskDOOpResult101 = riskDelegate.getByInfoUuid(infoUuid, CfCaseRiskTypeEnum.CASE_INFO_RISK);
        OpResult<CfCaseRiskDO> cfCaseRiskDOOpResult102 = riskDelegate.getByInfoUuid(infoUuid, CfCaseRiskTypeEnum.DRAW_CASH_RISK);
        AdminCfCaseVisitConfig cfCaseVisitConfig = caseVisitConfigBiz.get(infoId);

        cfRiskHintView.setRiskCommentCountCL(this.risk(cfCaseRiskDOOpResult101));
        cfRiskHintView.setRiskCommentCountTX(this.risk(cfCaseRiskDOOpResult102));
        cfRiskHintView.setRiskCommentCountYX(cfCaseVisitConfig == null ? 0 : 1);
        cfRiskHintView.sum();

        return NewResponseUtil.makeSuccess(cfRiskHintView);
    }

    private CrowdfundingApproveCommentVo transformDO2VO(VisitConfigLogDO d) {

        AdminUserAccountModel adminUserAccountModel = seaAccountClientV1.getValidUserAccountById(d.getOperatorId()).getResult();
        String name = "";
        if (adminUserAccountModel != null) {
            name = adminUserAccountModel.getName();
        }

        CrowdfundingApproveCommentVo v = new CrowdfundingApproveCommentVo();
        v.setComment(d.getActionInfo());
        v.setOperator(name);
        v.setOprtime(d.getCreateTime());
        return v;
    }

    private CrowdfundingApproveCommentVo convertVO(RemarkDO remarkDO) {
        AdminUserAccountModel adminUserAccountModel = seaAccountClientV1.getValidUserAccountById(
                Math.toIntExact(remarkDO.getOperatorId())).getResult();//已检查过
        String name = "";
        if (adminUserAccountModel != null) {
            name = adminUserAccountModel.getName();
        }
        CrowdfundingApproveCommentVo v = new CrowdfundingApproveCommentVo();
        v.setComment(remarkDO.getContent());
        v.setOperator(name);
        v.setOprtime(remarkDO.getCreateTime());
        return v;
    }

    private CrowdfundingApproveCommentVo convertFollowVO(CfReportFollowComment cfReportFollowComment) {
        CrowdfundingApproveCommentVo v = new CrowdfundingApproveCommentVo();
        v.setComment(cfReportFollowComment.getComment());
        v.setOperator(cfReportFollowComment.getOperatorName());
        v.setOprtime(cfReportFollowComment.getCreateTime());
        v.setOprid(cfReportFollowComment.getOperatorId());
        return v;
    }

    private int risk(OpResult<CfCaseRiskDO> singleResult) {
        if (singleResult.isFailOrNullData()) {
            return 0;
        }
        CfCaseRiskDO data = singleResult.getData();
        int verified = data.getVerified();
        if (verified == CfCaseRiskVerifiedEnum.WAITING_VERIFY.getVerified()) {
            return 0;
        }
        int passed = data.getPassed();
        if (passed == CaseRiskPassedEnum.PASSED.getValue()) {
            return 0;
        }

        return 1;
    }

    /**
     * 分组不同的请求评论
     *
     * @param commentVoList
     * @return
     */
    private Map<String, List<CrowdfundingApproveCommentVo>> buildResult(List<CrowdfundingApproveCommentVo> commentVoList) {
        Map<String, List<CrowdfundingApproveCommentVo>> resultMap = Maps.newHashMap();
        //举报
        List<CrowdfundingApproveCommentVo> juBaoCommentList = Lists.newArrayList();
        //二线客服
        List<CrowdfundingApproveCommentVo> erXianCommentList = Lists.newArrayList();
        //资金组
        List<CrowdfundingApproveCommentVo> ziJinCommentList = Lists.newArrayList();
        //一线客服
        List<CrowdfundingApproveCommentVo> yiXianCommentList = Lists.newArrayList();
        resultMap.put("juBaoCommentList", juBaoCommentList);
        resultMap.put("erXianCommentList", erXianCommentList);
        resultMap.put("ziJinCommentList", ziJinCommentList);
        resultMap.put("yiXianCommentList", yiXianCommentList);

        Set<Integer> userIdSet = commentVoList.stream().filter(item -> null != item)
                .map(CrowdfundingApproveCommentVo::getOprid).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(userIdSet)) {
            return resultMap;
        }
//        Map<Integer, List<AdminRoleModel>> userIdRolesMap = Maps.newHashMap();
//        try {
//            userIdRolesMap = seaRoleClientV1.getRoleMapByUserIds(Lists.newArrayList(userIdSet)).getResult();
//            LOGGER.debug("userIdRolesMap:{}", JSON.toJSONString(userIdRolesMap));
//        } catch (Exception e) {
//            LOGGER.error("", e);
//        }
//        if (null == userIdRolesMap || userIdRolesMap.isEmpty()) {
//            return resultMap;
//        }

        Map<Integer, List<String>> userIdRoleStrMap = seaRoleClientV1.selectRoleNameByUserIds(Lists.newArrayList(userIdSet));
        if (MapUtils.isEmpty(userIdRoleStrMap)) {
            return resultMap;
        }
//        for (Map.Entry<Integer, List<AdminRoleModel>> entry : userIdRolesMap.entrySet()) {
//            if (null == entry) {
//                continue;
//            }
//            List<AdminRoleModel> adminRoleModelList = entry.getValue();
//            Integer userId = entry.getKey();
//            if (null == userId || CollectionUtils.isEmpty(adminRoleModelList)) {
//                continue;
//            }
//            userIdRoleStrMap.put(userId, adminRoleModelList.stream()
//                    .filter(item -> null != item)
//                    .map(AdminRoleModel::getRoleName)
//                    .collect(Collectors.toList()));
//        }
        for (CrowdfundingApproveCommentVo crowdfundingApproveCommentVo : commentVoList) {
            if (null == crowdfundingApproveCommentVo) {
                continue;
            }
            List<String> roleStrList = userIdRoleStrMap.get(crowdfundingApproveCommentVo.getOprid());
            if (CollectionUtils.isEmpty(roleStrList)) {
                continue;
            }

            LOGGER.debug("userId:{}....roleStrList:{}",
                    crowdfundingApproveCommentVo.getOprid(), JSON.toJSONString(roleStrList));

            if (roleStrList.contains("客服二线")) {
                erXianCommentList.add(crowdfundingApproveCommentVo);
            }

            if (roleStrList.contains("资金组")) {
                ziJinCommentList.add(crowdfundingApproveCommentVo);
            }

            if (roleStrList.contains("举报组组长") || roleStrList.contains("举报组组员")
                    || roleStrList.contains("举报组实习生")) {
                juBaoCommentList.add(crowdfundingApproveCommentVo);
            }
            if (roleStrList.contains("一线客服")) {
                yiXianCommentList.add(crowdfundingApproveCommentVo);
            }
        }
        return resultMap;
    }

    @PostMapping("query-reject-history")
    @RequiresPermission("approve:query-reject-history")
    public Response queryRejectHistory(@RequestParam("infoId") String infoId,
                                       @RequestParam("materialId") int materialId,
                                       @RequestParam("current") int current,
                                       @RequestParam("size") int size,
                                       @RequestParam("userId") int userId) {
        PageInfo<CfMaterialVerityHistory.CfMaterialVerityHistoryVo> pageResult = verityHistoryBiz.queryMaterialVerityHistory(infoId, materialId, CfMaterialVerityHistory.REJECT_TYPE, current, size);
        Map<String, Object> result = new HashMap<>();
        result.put("total", pageResult.getTotal());
        result.put("list", pageResult.getList());
        return NewResponseUtil.makeSuccess(result);
    }

    @PostMapping("select-prepose-materials-by-caseId")
    @RequiresPermission("approve:select-prepose-materials-by-caseId")
    public Response<List<PreposeMaterialAdminModel>> selectMaterialByCaseId(int caseId) {
        return NewResponseUtil.makeSuccess(crowdfundingInfoBiz.selectMaterialByCaseId(caseId));
    }

    @PostMapping("query-material-op-detail")
    @RequiresPermission("approve:query-material-op-detail")
    public Response queryMaterialOpDetail(@RequestParam("infoUuid") String infoUuid) {
        return NewResponseUtil.makeSuccess(verityHistoryBiz.queryMaterialOpDetail(infoUuid));
    }

    @PostMapping("query-suggest-modify-detail")
    @RequiresPermission("approve:query-suggest-modify-detail")
    public Response<AuditSuggestModifyDetail> querySuggestModifyDetail(@RequestBody AuditSuggestModifyParam param) {

        AuditSuggestModifyDetail modifyDetail = null;
        try {
            modifyDetail = cfRefuseReasonEntityBiz.querySuggestModifyDetail(param);
        } catch (Exception e) {
            log.error("查询数据异常 param:{}", param, e);
            return NewResponseUtil.makeFail(e.getMessage());
        }

        return NewResponseUtil.makeSuccess(modifyDetail);
    }

    @PostMapping("query-case/material-audit-version")
    @RequiresPermission("approve:query-case/material-audit-version")
    public Response<CfCaseAuditVersion> queryCaseMaterialAuditVersion(int caseId) {

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.NOT_CASE_DATA);
        }

        Response<MaterialVersion> materialResult = materialFeignClient.queryMaterialVersion(crowdfundingInfo.getInfoId());
        if (materialResult != null && materialResult.ok() && materialResult.getData() != null) {
            return NewResponseUtil.makeSuccess(new CfCaseAuditVersion(materialResult.getData().isMaterial100() ? 1 : 0));
        }
        log.info("不能找到案例的版本用默认版本caseId:{}", caseId);
        return NewResponseUtil.makeSuccess(new CfCaseAuditVersion(0));
    }


    @PostMapping("query-publish-image-content")
    @RequiresPermission("approve:query-publish-image-content")
    public Response<CPublishImageContent> queryImageContent(@RequestParam("caseId") int caseId) {
        return NewResponseUtil.makeSuccess(adminApproveService.queryPublishImageContent(caseId));
    }

    /**
     * 材审环节增加删除图片功能
     */
    @PostMapping("del-case-image")
    @RequiresPermission("approve:del-case-image")
    public Response<Void> delCaseImage(@RequestParam(value = "imageIds", required = false, defaultValue = "") String imageIds,
                                       @RequestParam("reason") String reason,
                                       @RequestParam("comment") String comment,
                                       @RequestParam(value = "infoUuid", required = false, defaultValue = "") String infoUuid) {
        int adminUserId = ContextUtil.getAdminUserId();

        List<Integer> idList = Splitter.on(",").splitToList(imageIds).stream()
                .map(Integer::valueOf)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(idList) || StringUtils.isEmpty(infoUuid)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        CfInfoSimpleModel cfInfoSimpleModel = crowdfundingDelegate.getCfInfoSimpleModel(infoUuid);
        if (Objects.isNull(cfInfoSimpleModel)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<CrowdfundingAttachment> attachments = adminCrowdfundingAttachmentBiz.getAttachmentsByIdList(idList, cfInfoSimpleModel.getId());
        if (CollectionUtils.isEmpty(attachments)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<Integer> attachmentsImageIds = attachments.stream()
                .map(CrowdfundingAttachment::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(attachmentsImageIds)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        int caseId = attachments.get(0).getParentId();
        int num = adminCrowdfundingAttachmentBiz.deleteByIds(attachmentsImageIds, caseId);
        if (num > 0) {
            approveRemarkOldService.add(caseId, adminUserId, comment + "原因：" + reason);
        }

        return NewResponseUtil.makeSuccess(null);
    }

    @PostMapping("/multiple-case-images")
    @ApiOperation("材料审核详情页票据归纳")
    @RequiresPermission("approve:multiple-case-images")
    public Response<MultipleCaseImagesVo> multipleCaseImages(@RequestParam(value = "workOrderId", required = false, defaultValue = "0") long workOrderId,
                                                             @RequestParam(value = "caseId",required = false, defaultValue = "0") int caseId) {
        return ApproveService.getMultipleCaseImages(workOrderId, caseId);
    }

}
