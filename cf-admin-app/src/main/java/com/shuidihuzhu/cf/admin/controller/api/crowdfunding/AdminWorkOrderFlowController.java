package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.enhancer.subject.redislock.RedisLock;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.admin.controller.api.util.FindTimeRangeUtil;
import com.shuidihuzhu.cf.biz.admin.AdminOrganizationBiz;
import com.shuidihuzhu.cf.biz.admin.workorder.AdminWorkOrderMoneyBackExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.biz.crowdfunding.impl.AdminWorkOrderFlowBizImpl;
import com.shuidihuzhu.cf.biz.crowdfunding.workflow.WorkFlowAutoAssignRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.workflow.WorkFlowTypePropertyBiz;
import com.shuidihuzhu.cf.biz.websocket.AdminWebSocketHandler;
import com.shuidihuzhu.cf.biz.websocket.AdminWebSocketProducer;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.admin.vo.AdminWorkOrderMoneyBackExtVO;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderClassifySettings;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderFlowRemindRecord;
import com.shuidihuzhu.cf.model.crowdfunding.vo.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.workflow.WorkFlowOrgStaffVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.workflow.WorkFlowStaffVo;
import com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowStaffStatus;
import com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowStaffStatusRecord;
import com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowTypeProperty;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cf.service.crowdfunding.WorkFlowStaffService;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.*;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@RefreshScope
@RestController
@RequestMapping(path = "admin/cf/work-order-flow")
public class AdminWorkOrderFlowController {

    @Autowired
    private AdminWorkOrderFlowBiz orderFlowBiz;

    @Autowired
    private AdminWorkOrderFlowReportBiz reportBiz;

//    @Autowired
//    private SeaRoleClientV1 seaRoleClientV1;
    @Autowired
    private AdminOrganizationBiz orgBiz;
    @Autowired
    private SeaAccountClientV1 accountClientV1;

    @Autowired
    private AdminWebSocketProducer socketProducer;

    @Autowired
    private AdminWorkOrderClassifySettingsBiz classifySettingsBiz;

    @Autowired
    private AdminWorkOrderFlowReportV2Biz flowReportV2Biz;

    @Autowired
    private WorkFlowStaffService workFlowStaffService;

    @Autowired
    private WorkFlowAutoAssignRecordBiz assignRecordBiz;

    @Value("${diff.get-all-work-order-list:false}")
    private boolean getAllWorkOrderList;

    @Autowired
    private AdminApproveService adminApproveService;

    @Autowired
    private WorkFlowTypePropertyBiz workFlowTypePropertyBiz;

    @Resource(name = "cf2RedissonHandler")
    private RedissonHandler cf2RedissonHandler;

    @Value("${approve.get.wait.for.handle.list:10}")
    private int time;

    @Autowired
    private MaskUtil maskUtil;

    @Autowired
    private AdminWorkOrderMoneyBackExtBiz adminWorkOrderMoneyBackExtBiz;

    @RequestMapping(path = "create", method = RequestMethod.POST)
    @ApiOperation(value = "创建工单", notes = "")
    @RequiresPermission("work-order-flow:create")
    public Response create(@RequestParam("model") String model) {

        log.info("信息传递工单创建工单，param:{}", model);
        AdminWorkOrderFlowView vo = null;
        try {
            vo = JSON.parseObject(model, AdminWorkOrderFlowView.class);//已检查过
        } catch (Exception e) {
            log.warn("信息传递工单-创建 参数解析错误", e);
            return NewResponseUtil.makeError(AdminErrorCode.INPUT_FORMAT_ERROR);
        }
         // 校验返还款相关表单数据
        List<AdminWorkOrderMoneyBackExtVO> moneyBackExtList = vo.getMoneyBackExtList();
        if (CollectionUtils.isNotEmpty(moneyBackExtList)) {
            // 检查列表大小是否超过30
            if (moneyBackExtList.size() > 30) {
                return NewResponseUtil.makeFail("返还款相关表单数据不能超过30条");
            }
            
            // 检查每条数据的caseId是否为空
            for (AdminWorkOrderMoneyBackExtVO extVO : moneyBackExtList) {
                if (extVO.getCaseId() == null) {
                    return NewResponseUtil.makeFail("返还款相关表单数据中案例ID不能为空");
                }
                // 如果金额不为空，检测金额格式是否正确
                if (StringUtils.isNotBlank(extVO.getAmountStr()) && !NumberUtils.isCreatable(extVO.getAmountStr())) {
                    return NewResponseUtil.makeFail("返还款相关表单数据中金额格式不正确");
                }
            }
        }
        //创建前判断是否能生成，同案例+同问题类型+未处理完
        Response<String> existRelateFlow = orderFlowBiz.existRelateFlow(vo);
        if (existRelateFlow.notOk()) {
            return existRelateFlow;
        }
        vo.setCreatorId(ContextUtil.getAdminUserId());
        
        // 创建工单
        Response response = orderFlowBiz.createWorkOrderFlow(vo);
        if (response.notOk()) {
            return response;
        }
        
        // 创建成功后，处理返还款相关表单数据
        if (CollectionUtils.isNotEmpty(moneyBackExtList)) {
            Long workOrderId = vo.getWorkOrderId();
            adminWorkOrderMoneyBackExtBiz.batchSaveByVOList(moneyBackExtList, workOrderId);
        }
        
        return response;
    }


    // 只查看当前登入人创建的
    @RequestMapping(path = "get-my-create-list", method = RequestMethod.POST)
    @ApiOperation(value = "我的工单", notes = "")
    @RequiresPermission("work-order-flow:get-my-create-list")
    public Response getMyCreateWorkOrderList(@RequestParam("current") int current,
                                             @RequestParam("pageSize") int pageSize,
                                             @RequestParam("searchParam") String searchParam) {

        AdminWorkOrderFlowParam.SearchParam param = null;

        try {
            param = JSON.parseObject(searchParam, AdminWorkOrderFlowParam.SearchParam.class);//已检查过
        } catch (Exception e) {
            log.warn("信息传递工单——我的工单 参数解析错误", e);
            return NewResponseUtil.makeError(AdminErrorCode.INPUT_FORMAT_ERROR);
        }

        param.setCreatorId(ContextUtil.getAdminUserId());
        param.setPageNum(current);
        param.setPageSize(pageSize);

        //创建时间，时间限制
        Date CreateStartTime = param.getCreateStartTime();
        Date CreateEndTime = param.getCreateEndTime();
        boolean flagCreate = FindTimeRangeUtil.getFindTimeRangeUtil(CreateStartTime, CreateEndTime, 7);
        if (!flagCreate) {
            return NewResponseUtil.makeError(AdminErrorCode.DAY_RANGE_ERROR);
        }

        return NewResponseUtil.makeSuccess(buildResult(orderFlowBiz.selectCreateWorkOrderByParam(param)));
    }


    // 查看工单的处理人是userId
    @RequestMapping(path = "get-wait-for-handle-list", method = RequestMethod.POST)
    @ApiOperation(value = "待办工单", notes = "")
    @RequiresPermission("work-order-flow:get-wait-for-handle-list")
    public Response getWaitForHandleList(@RequestParam("current") int current,
                                         @RequestParam("pageSize") int pageSize,
                                         @RequestParam("searchParam") String searchParam) {

        int adminUserId = ContextUtil.getAdminUserId();

        AdminWorkOrderFlowParam.SearchParam param = null;

        try {
            param = JSON.parseObject(searchParam, AdminWorkOrderFlowParam.SearchParam.class);//已检查过
        } catch (Exception e) {
            log.warn("信息传递工单——待办工单 参数解析错误", e);
            return NewResponseUtil.makeError(AdminErrorCode.INPUT_FORMAT_ERROR);
        }

        param.setOperatorId(adminUserId);
        param.setPageNum(current);
        param.setPageSize(pageSize);

        //最快处理时间，时间限制
        Date updateStartTime = param.getUpdateStartTime();
        Date updateEndTime = param.getUpdateEndTime();
        boolean flagUpdate = FindTimeRangeUtil.getFindTimeRangeUtil(updateStartTime, updateEndTime, 7);
        if (!flagUpdate) {
            return NewResponseUtil.makeError(AdminErrorCode.DAY_RANGE_ERROR);
        }

        Pair<PageInfo<AdminWorkOrderFlowView>, Integer> detailAndRemindNums = orderFlowBiz.selectWaitForHandleFlowByParam(param);

        Map<String, Object> res = buildResult(detailAndRemindNums.getLeft());
        res.put("unHandleCount", orderFlowBiz.countUnHandleOrder(adminUserId));
        res.put("remindTotalNums", detailAndRemindNums.getRight());

        return NewResponseUtil.makeSuccess(res);
    }


    // AdminWorkOrderFlowView
    @RequestMapping(path = "get-finish-handle-list", method = RequestMethod.POST)
    @ApiOperation(value = "已办工单", notes = "")
    @RequiresPermission("work-order-flow:get-finish-handle-list")
    public Response getFinishHandleList(@RequestParam("current") int current,
                                        @RequestParam("pageSize") int pageSize,
                                        @RequestParam("searchParam") String searchParam) {

        AdminWorkOrderFlowParam.SearchParam param = null;

        try {
            param = JSON.parseObject(searchParam, AdminWorkOrderFlowParam.SearchParam.class);//已检查过
        } catch (Exception e) {
            log.warn("信息传递工单——已办工单 参数解析错误", e);
            return NewResponseUtil.makeError(AdminErrorCode.INPUT_FORMAT_ERROR);
        }

        param.setRecordOperatorId(ContextUtil.getAdminUserId());
        param.setPageNum(current);
        param.setPageSize(pageSize);

        //最快处理时间，时间限制
        Date updateStartTime = param.getUpdateStartTime();
        Date updateEndTime = param.getUpdateEndTime();
        boolean flagUpdate = FindTimeRangeUtil.getFindTimeRangeUtil(updateStartTime, updateEndTime, 7);
        if (!flagUpdate) {
            return NewResponseUtil.makeError(AdminErrorCode.DAY_RANGE_ERROR);
        }

        return NewResponseUtil.makeSuccess((buildResult(orderFlowBiz.selectHasHandleWorkOrderByParam(param))));
    }

    @RequestMapping(path = "get-all-work-order-list", method = RequestMethod.POST)
    @ApiOperation(value = "工单管理查询所有工单", notes = "")
    @RequiresPermission("work-order-flow:get-all-work-order-list")
    public Response getAllWorkOrderList(@RequestParam("current") int current,
                                        @RequestParam("pageSize") int pageSize,
                                        @RequestParam("searchParam") String searchParam) {

        AdminWorkOrderFlowParam.SearchParam param = null;

        try {
            param = JSON.parseObject(searchParam, AdminWorkOrderFlowParam.SearchParam.class);//已检查过
        } catch (Exception e) {
            log.warn("信息传递工单——查询所有工单 参数解析错误", e);
            return NewResponseUtil.makeError(AdminErrorCode.INPUT_FORMAT_ERROR);
        }

        param.setPageNum(current);
        param.setPageSize(pageSize);

        //创建时间，时间限制
        Date CreateStartTime = param.getCreateStartTime();
        Date CreateEndTime = param.getCreateEndTime();
        boolean flagCreate = FindTimeRangeUtil.getFindTimeRangeUtil(CreateStartTime, CreateEndTime, 7);
        if (!flagCreate) {
            return NewResponseUtil.makeError(AdminErrorCode.DAY_RANGE_ERROR);
        }

        //最快处理时间，时间限制
        Date updateStartTime = param.getUpdateStartTime();
        Date updateEndTime = param.getUpdateEndTime();
        boolean flagUpdate = FindTimeRangeUtil.getFindTimeRangeUtil(updateStartTime, updateEndTime, 7);
        if (!flagUpdate) {
            return NewResponseUtil.makeError(AdminErrorCode.DAY_RANGE_ERROR);
        }


        Map<String, Object> result = new HashMap<>();

        if (getAllWorkOrderList) {
            Pair<Long, List<AdminWorkOrderFlowView>> pair = orderFlowBiz.selectAdminWorkOrderByParamFromEs(param);
            result.put("total", pair.getLeft());
            result.put("list", pair.getRight());
        } else {
            result = buildResult(orderFlowBiz.selectAdminWorkOrderByParam(param));
        }

        return NewResponseUtil.makeSuccess(result);
    }

    @RequestMapping(path = "get-unhandled-by-caseId-mobile", method = RequestMethod.POST)
    @ApiOperation("创建工单中展示案例或者手机号相关案例信息")
    @RequiresPermission("work-order-flow:get-unhandled-by-caseId-mobile")
    public Response getUnHandleByCaseIdOrMobile(@RequestParam(value = "caseId", required = false) Integer caseId,
                                                @RequestParam(value = "mobile", required = false) String mobile,
                                                @RequestParam(value = "current") int current,
                                                @RequestParam(value = "pageSize")int pageSize) {
        if (caseId == null && StringUtils.isBlank(mobile)) {
            return NewResponseUtil.makeFail("手机号以及caseId没传");
        }
        PageInfo<AdminWorkOrderFlowView> pageInfo = orderFlowBiz.listUnHandleByCaseIdOrMobile(caseId, mobile, current, pageSize);
        Map<String, Object> result = new HashMap<>();
        result.put("total", pageInfo.getTotal());
        result.put("list", pageInfo.getList());
        return NewResponseUtil.makeSuccess(result);
    }


    @RequestMapping(path = "get-case-title-by-case-id", method = RequestMethod.POST)
    @ApiOperation(value = "案例id查询案例标题")
    @RequiresPermission("work-order-flow:get-case-title-by-case-id")
    public Response getCaseTitleByCaseId(@RequestParam("case_id") int caseId) {

        AdminWorkOrderFlowBizImpl.CaseTitleAndMobile res = orderFlowBiz.getTitleAndMobileByCaseId(caseId);
        if (res == null) {
            return NewResponseUtil.makeError(AdminErrorCode.NOT_CASE_DATA);
        }
        return NewResponseUtil.makeSuccess(res);
    }

    // AdminWorkOrderFlowDetailView
    @RequestMapping(path = "get-work-order-detail", method = RequestMethod.POST)
    @ApiOperation(value = "查看工单详情")
    @RequiresPermission("work-order-flow:get-work-order-detail")
    public Response getWorkOrderDetail(@RequestParam("id") long id) {

        return NewResponseUtil.makeSuccess(orderFlowBiz.queryWorkOrderFlowDetailView(id));
    }

    @RequestMapping(path = "handle-work-order", method = RequestMethod.POST)
    @ApiOperation(value = "处理-包括（跟进、处理完成、不需要处理、分配）")
    @RequiresPermission("work-order-flow:handle-work-order")
    public Response handleWorkOrder(@RequestParam("handleParam") String handleParam) {

        log.info("信息流转工单——操作工单 param:{}", handleParam);
        AdminWorkOrderFlowParam.HandleParam param = null;

        try {
            param = JSON.parseObject(handleParam, AdminWorkOrderFlowParam.HandleParam.class);//已检查过
        } catch (Exception e) {
            log.warn("信息传递工单——操作工单 参数解析错误", e);
            return NewResponseUtil.makeError(AdminErrorCode.INPUT_FORMAT_ERROR);
        }
        param.setUserId(ContextUtil.getAdminUserId());
        return orderFlowBiz.handleWorkFlowOrder(param);
    }


    //催单,查看最近的处理人,添加到remind表中
    @RequestMapping(path = "remind-work-order", method = RequestMethod.POST)
    @RequiresPermission("work-order-flow:remind-work-order")
    public Response<Boolean> remindWorkOrder(@RequestParam("param")String param) {
        int adminUserId = ContextUtil.getAdminUserId();
        AdminWorkOrderFlowRemindRecord remindRecord = JSON.parseObject(param, AdminWorkOrderFlowRemindRecord.class);//已检查过
        //check
        if (remindRecord == null || remindRecord.getFlowId() == 0 || StringUtils.isBlank(remindRecord.getWorkOrderFlowId())
            || StringUtils.isBlank(remindRecord.getComment())) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (remindRecord.getComment().length() > 200) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_TOO_LONG);
        }
        boolean insertResult = orderFlowBiz.addRemind(remindRecord, adminUserId);
        return NewResponseUtil.makeSuccess(insertResult);
    }


    @RequestMapping(path = "acquire-work-order-v2", method = RequestMethod.POST)
    @ApiOperation(value = "获取工单")
    @RequiresPermission("work-order-flow:acquire-work-order-v2")
    public Response<Integer> acquireWorkOrderV2() {
        log.info("信息传递工单领取:userId:{}", ContextUtil.getAdminUserId());
        //领取前判断员工状态
        int status = workFlowStaffService.showStatus(ContextUtil.getAdminUserId());

//        if(status == WorkFlowStaffStatus.StaffStatusEnum.PAUSE.getCode()) {
//            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR "暂停状态不可获取工单");
//        }

        if (status != WorkFlowStaffStatus.StaffStatusEnum.online.getCode()) {
            return NewResponseUtil.makeError(AdminErrorCode.NOT_ONLINE_FAIL);
        }
        return orderFlowBiz.AssigningTaskV2(ContextUtil.getAdminUserId());
    }


//    @RequestMapping(path = "get-role", method = RequestMethod.POST)
//    @ApiOperation(value = "查找可以分配的角色")
//    @RequiresPermission("work-order-flow:get-role")
//    public Response getRole() {
//
//        return NewResponseUtil.makeSuccess(seaRoleClientV1.getByRoleIds(AdminWorkOrderFlowBiz.SPECIFIC_ROLE_IDS).getResult());
//    }

    @RequestMapping(path = "get-people-by-problem-type", method = RequestMethod.POST)
    @ApiOperation(value = "查找组织下的人名")
    @RequiresPermission("work-order-flow:get-people-by-problem-type")
    public Response getPeopleByProblemType(@RequestParam("userId") int userId, @RequestParam("roleId") int roleId) {
        log.info("AdminWorkOrderFlowController getPeopleByProblemType userId:{}, roleId:{}", userId, roleId);
        return NewResponseUtil.makeSuccess(orgBiz.getOrganizationEmployees(roleId, 1, 1000));
    }

    @RequestMapping(path = "get-users-by-name-like", method = RequestMethod.POST)
    @ApiOperation(value = "模糊查找人名")
    @RequiresPermission("work-order-flow:get-users-by-name-like")
    public Response getUserAccountsByNameLike(@RequestParam("name") String name) {
        log.info("AdminWorkOrderFlowController getPeopleByProblemType name：{}", name);
        if (StringUtils.isEmpty(name)) {
            return NewResponseUtil.makeSuccess("");
        }
        AuthRpcResponse<List<AdminUserAccountModel>> response = accountClientV1.getUserAccountsByMisLike(name,1);

        return NewResponseUtil.makeSuccess(response == null ? "" : response.getResult());
    }


    private Map<String, Object> buildResult(PageInfo<AdminWorkOrderFlowView> pageResult) {
        Map<String, Object> result = new HashMap<>();
        result.put("total", pageResult.getTotal());
        result.put("list", pageResult.getList());
        Optional.ofNullable(pageResult.getList())
                .filter(CollectionUtils::isNotEmpty)
                .ifPresent(r -> r.forEach(item -> {
                    if (StringUtils.isNotBlank(item.getMobile())) {
                        item.setMobileMask(maskUtil.buildByDecryptPhone(item.getMobile()));
                        item.setMobile(StringUtils.EMPTY);
                    }
                }));

        return result;
    }

    @RequestMapping(path = "get-work-flow-report", method = RequestMethod.POST)
    @ApiOperation(value = "报表")
    @RequiresPermission("work-order-flow:get-work-flow-report")
    public Response getWorkFlowRepeat(@RequestParam(name = "current", defaultValue = "1") int current,
                                      @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                                      @RequestParam("searchParam") String searchParam) {

        AdminWorkOrderFlowStatistics.searchParam param = null;
        log.info("信息传递工单 报表查询 searchParam:{}", searchParam);
        try {
            param = JSON.parseObject(searchParam, AdminWorkOrderFlowStatistics.searchParam.class);//已检查过
        } catch (Exception e) {
            log.warn("信息传递工单——报表查询 参数解析错误", e);
            return NewResponseUtil.makeError(AdminErrorCode.INPUT_FORMAT_ERROR);
        }

        PageInfo<AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView> dataPage = reportBiz
                .countFlowReport(current, pageSize, param);
        Map result = new HashMap<>();
        result.put("total", dataPage.getTotal());
        result.put("list", dataPage.getList() == null ? Lists.newArrayList() : dataPage.getList());
        return NewResponseUtil.makeSuccess(result);
    }


    @RequestMapping(path = "get-work-flow-org-report", method = RequestMethod.POST)
    @ApiOperation(value = "工单数据统计-组织统计")
    @RedisLock(key = "get-work-flow-org-report_#{userId}", leaseTimeMills = 3000)
    @RequiresPermission("work-order-flow:get-work-flow-org-report")
    public Response getWorkFlowOrgReport(@RequestParam(name = "orgId", defaultValue = "0") int orgId,
                                         @RequestParam(name = "beginTime") String beginTime,
                                         @RequestParam(name = "endTime") String endTime,
                                         @RequestParam(name = "level", defaultValue = "-1") int level,
                                         @RequestParam(name = "firstLevelClassifyId", defaultValue = "0") long firstLevelClassifyId,
                                         @RequestParam(name = "memberType", defaultValue = "0", required = false) int memberType,
                                         @RequestParam(name = "current", defaultValue = "0", required = false) int current,
                                         @RequestParam(name = "pageSize", defaultValue = "0", required = false) int pageSize,
                                         @RequestParam(name = "userId") int userId) {

        List<Long> secondLevelClassifyIds = Lists.newArrayList();
        if (firstLevelClassifyId != 0) {
            List<AdminWorkOrderClassifySettings> classifySettings = classifySettingsBiz.selectChildClassifySettings(firstLevelClassifyId, 0);
            if (CollectionUtils.isEmpty(classifySettings)) {
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
            }

            secondLevelClassifyIds = classifySettings.stream().map(AdminWorkOrderClassifySettings::getId).collect(Collectors.toList());
        }

        Date begin = DateUtil.parseDateTime(beginTime);
        Date end = DateUtil.parseDateTime(endTime);

        boolean flagDeal = FindTimeRangeUtil.getFindTimeRangeUtil(begin, end, 7);
        if (!flagDeal) {
            return NewResponseUtil.makeError(AdminErrorCode.DAY_RANGE_ERROR);
        }

        if (!ObjectUtils.allNotNull(begin, end)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        Pair<Integer, List<AdminWorkOrderFlowStatistics.AdminWorkOrderFlowStatisticsView>> viewPair = flowReportV2Biz.countFlowReport(orgId, begin, end, level, secondLevelClassifyIds, memberType, current, pageSize, true);

        Pair<Long, List<AdminWorkOrder>> pair = flowReportV2Biz.countNoHandleMoreThan48Hour(orgId, level, secondLevelClassifyIds);

        Map result = new HashMap<>();
        result.put("noHandleMoreThan48Hour", pair.getLeft());
        result.put("noHandleMoreThan48HourIds", pair.getRight().stream().map(AdminWorkOrder::getId).collect(Collectors.toList()));
        result.put("date2StatisticsViews", viewPair.getRight());
        result.put("date2StatisticsViewsSize", viewPair.getLeft());

        return NewResponseUtil.makeSuccess(result);
    }

    @RequestMapping(path = "get-work-flow-view-by-ids", method = RequestMethod.POST)
    @ApiOperation(value = "报表中id跳转")
    @RequiresPermission("work-order-flow:get-work-flow-view-by-ids")
    public Response getWorkFlowViewByIds(@RequestParam("workOrderIds") String workOrderIds) {

        if (StringUtils.isEmpty(workOrderIds)) {
            return NewResponseUtil.makeSuccess("");
        }
        Set<Long> idSet = Sets.newHashSet();
        try {
            String[] ids = workOrderIds.split(",");
            for (String id : ids) {
                idSet.add(Long.valueOf(id));
            }
        } catch (Exception e) {
            log.warn("信息传递工单——报表查询 参数解析错误", e);
            return NewResponseUtil.makeError(AdminErrorCode.INPUT_FORMAT_ERROR);
        }

        AdminWorkOrderFlowParam.SearchParam param = new AdminWorkOrderFlowParam.SearchParam();
        param.setWorkOrderIds(Sets.newHashSet(idSet));

        PageInfo<AdminWorkOrderFlowView> result = orderFlowBiz.selectAdminWorkOrderByParam(param);
        result.setList(result.getList().stream().sorted(Comparator.comparing(AdminWorkOrderFlowView::getCreateTime)
                .reversed()).collect(Collectors.toList()));

        return NewResponseUtil.makeSuccess(buildResult(result));
    }

    @ResponseBody
    @RequestMapping(path = "/get-work-flow-summary-excel-V2", method = {RequestMethod.GET, RequestMethod.POST})
    @RequiresPermission("work-order-flow:get-work-flow-summary-excel-V2")
    public Response<Void> downloadDataSummaryExcelV2(HttpServletResponse response,
                                           @RequestParam(name = "orgId", defaultValue = "0") int orgId,
                                           @RequestParam(name = "beginTime") String beginTime,
                                           @RequestParam(name = "endTime") String endTime,
                                           @RequestParam(name = "level", defaultValue = "-1") int level,
                                           @RequestParam(name = "firstLevelClassifyId", defaultValue = "0") long firstLevelClassifyId,
                                           @RequestParam(name = "memberType", defaultValue = "0", required = false) int memberType) {
        List<Long> secondLevelClassifyIds = Lists.newArrayList();
        if (firstLevelClassifyId != 0) {
            List<AdminWorkOrderClassifySettings> classifySettings = classifySettingsBiz.selectChildClassifySettings(firstLevelClassifyId, 0);
            if (CollectionUtils.isEmpty(classifySettings)) {
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
            }

            secondLevelClassifyIds = classifySettings.stream().map(AdminWorkOrderClassifySettings::getId).collect(Collectors.toList());
        }

        Date begin = DateUtil.parseDateTime(beginTime);
        Date end = DateUtil.parseDateTime(endTime);
        if (!ObjectUtils.allNotNull(begin, end)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        boolean b = flowReportV2Biz.downloadDataSummaryExcelV2(response, orgId, begin, end, level, secondLevelClassifyIds, memberType, ContextUtil.getAdminLongUserId());
        if (!b) {
            return NewResponseUtil.makeFail("导出失败");
        }

        return NewResponseUtil.makeSuccess();
    }

    @RequestMapping(path = "get-similar-work-flow", method = RequestMethod.POST)
    @ApiOperation(value = "查找相似的工单")
    @RequiresPermission("work-order-flow:get-similar-work-flow")
    public Response getWorkFlowViewByIds(@RequestParam("caseId") int caseId, @RequestParam("secondClassifyId") int secondClassifyId) {

        return NewResponseUtil.makeSuccess(orderFlowBiz.querySimilarOrderByCaseIdAndSettingId(caseId, secondClassifyId));
    }

    @ResponseBody
    @RequestMapping(path = "/get-work-flow-data-excel-v3", method = {RequestMethod.GET, RequestMethod.POST})
    @RequiresPermission("work-order-flow:get-work-flow-data-excel-v3")
    public Response<Void> downloadDataDetailExcelV3(HttpServletResponse response,
                                          @RequestParam(name = "orgId", defaultValue = "0") int orgId,
                                          @RequestParam(value = "workOrderIds", defaultValue = "", required = false) String workOrderIds,
                                          @RequestParam(name = "beginTime", required = false) String beginTime,
                                          @RequestParam(name = "endTime", required = false) String endTime,
                                          @RequestParam(name = "level", defaultValue = "-1") int level,
                                          @RequestParam(name = "firstLevelClassifyId", defaultValue = "0") long firstLevelClassifyId,
                                          @RequestParam(name = "memberType", defaultValue = "0", required = false) int memberType,
                                          @RequestParam(name = "staffId", defaultValue = "0", required = false) int staffId,
                                          @RequestParam(name = "dataType", defaultValue = "1", required = false) int dataType) {

        List<Long> secondLevelClassifyIds = Lists.newArrayList();
        if (firstLevelClassifyId != 0) {
            List<AdminWorkOrderClassifySettings> classifySettings = classifySettingsBiz.selectChildClassifySettings(firstLevelClassifyId, 0);
            if (CollectionUtils.isEmpty(classifySettings)) {
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
            }

            secondLevelClassifyIds = classifySettings.stream().map(AdminWorkOrderClassifySettings::getId).collect(Collectors.toList());
        }

        Date begin = DateUtil.parseDateTime(beginTime);
        Date end = DateUtil.parseDateTime(endTime);

        boolean downloadDataDetailExcelV3 = flowReportV2Biz.downloadDataDetailExcelV3(response, orgId, begin, end, level, secondLevelClassifyIds, memberType, staffId, dataType, ContextUtil.getAdminLongUserId());
        if (!downloadDataDetailExcelV3) {
            return NewResponseUtil.makeFail("导出失败");
        }
        return NewResponseUtil.makeSuccess();
    }

    @RequestMapping(path = "/get-socket-size", method = RequestMethod.GET)
    @RequiresPermission("work-order-flow:get-socket-size")
    public Response getSocketSize() {

        return ResponseUtil.makeSuccess(AdminWebSocketHandler.getSocketSize());
    }

    @RequestMapping(path = "/send-socket-msg", method = RequestMethod.GET)
    @RequiresPermission("work-order-flow:send-socket-msg")
    public Response setSocketMsg(@RequestParam("sendUserId") int sendUserId,
                                 @RequestParam("msg") String msg) {

        socketProducer.sendWebSocketMessage(sendUserId, msg);

        return ResponseUtil.makeSuccess(AdminWebSocketHandler.getSocketSize());
    }


    /**
     * 创建工单时,创建人渠道信息
     * @return
     */
    @RequestMapping(path = "/list-creator-channel", method = RequestMethod.POST)
    @RequiresPermission("work-order-flow:list-creator-channel")
    public Response<List<String>> listCreatorChannel(@RequestParam("problemId")int problemId) {
        //先获取组织关系
        int adminUserId = ContextUtil.getAdminUserId();
        List<String> channels = Lists.newArrayList();
        try {
            channels = orderFlowBiz.listCreatorChannel(adminUserId, problemId);
        } catch (Exception e) {
            log.error("listCreatorChannel problemId:{},error", problemId, e);
            return ResponseUtil.makeError(ErrorCode.SYSTEM_UNRECOGNIZED_ERROR);
        }
        return ResponseUtil.makeSuccess(channels);
    }


    @RequestMapping(path = "/staff-manager/list-org-staff-status", method = RequestMethod.POST)
    @RequiresPermission("work-order-flow:staff-manager/list-org-staff-status")
    public Response<List<WorkFlowOrgStaffVo>> listOrgStaffStatus() {
        List<WorkFlowOrgStaffVo> workFlowOrgStaffVoList = workFlowStaffService.listOrgStaffStatus();
        return NewResponseUtil.makeSuccess(workFlowOrgStaffVoList);
    }

    @RequestMapping(path = "/report-handle-data", method = RequestMethod.POST)
    @RequiresPermission("work-order-flow:report-handle-data")
    public Response<Void> reportHandleData( @RequestParam @ApiParam(value = "上报的工单id") long workFlowId) {
        workFlowTypePropertyBiz.addFlowHandleRecord(workFlowId, ContextUtil.getAdminUserId());
        return NewResponseUtil.makeSuccess(null);
    }

    @RequestMapping(path = "/staff-manager/search-staff-status", method = RequestMethod.POST)
    @RequiresPermission("work-order-flow:staff-manager/search-staff-status")
    public Response<WorkFlowStaffVo> searchStaffStatus(@RequestParam(value = "lastOrgIds") String lastOrgIds,
                                                       @RequestParam(value = "flowUserId", required = false, defaultValue = "") Long flowUserId,
                                                       @RequestParam(value = "staffStatus",required = false,defaultValue = "0") int staffStatus,
                                                       @RequestParam(value = "current", defaultValue = "1") int current,
                                                       @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        List<Integer> orgIds = Splitter.on(',').splitToList(lastOrgIds).stream().map(Integer::valueOf).collect(Collectors.toList());
        WorkFlowStaffVo workFlowStaffVo = workFlowStaffService.searchStaffStatus(orgIds, flowUserId, current, pageSize,staffStatus);
        return NewResponseUtil.makeSuccess(workFlowStaffVo);
    }

    @RequestMapping(path = "/staff-manager/change-staff-status")
    @RequiresPermission("work-order-flow:staff-manager/change-staff-status")
    public Response<Boolean> changeStaffStatus(@RequestParam("operatedId") int operatedId, @RequestParam("status") int status) {
        int adminUserId = ContextUtil.getAdminUserId();
        Response<Void> changeStatus = workFlowStaffService.changeStatus(adminUserId, operatedId, status);
        if (changeStatus.notOk()) {
            return NewResponseUtil.makeFail(changeStatus.getMsg());
        }
        return NewResponseUtil.makeSuccess(true);
    }

    @RequestMapping(path = "/staff-manager/show-status")
    @RequiresPermission("work-order-flow:staff-manager/show-status")
    public Response<Integer> showStatus() {
        int adminUserId = ContextUtil.getAdminUserId();
        int status = workFlowStaffService.showStatus(adminUserId);
        return NewResponseUtil.makeSuccess(status);
    }

    @RequestMapping(path = "/staff-manager/list-status-change-records")
    @RequiresPermission("work-order-flow:staff-manager/list-status-change-records")
    public Response<List<WorkFlowStaffStatusRecord>> listStatusChangeRecords(@RequestParam("flowUserId") long flowUserId) {
        List<WorkFlowStaffStatusRecord> statusRecords = workFlowStaffService.listStatusChangeRecords(flowUserId);
        return NewResponseUtil.makeSuccess(statusRecords);
    }

    @RequestMapping(path = "/send-message")
    @RequiresPermission("work-order-flow:send-message")
    public Response<Void> sendMessage(String mobile, String message, Integer userId,
                                @RequestParam(required = false, name = "modelNum") String modelNum,
                                      @RequestParam(required = false, name = "param", defaultValue = "") String param) {
        log.info("信息传递工单 SendMessage mobile:{};message:{}", mobile, message);
        mobile = StringUtils.trimToEmpty(mobile);
        if (MobileUtil.illegal(mobile)) {
            return NewResponseUtil.makeError(AdminErrorCode.MOBILE_FORMAT_ERROR);
        }

        if ((StringUtils.isBlank(message) && StringUtils.isBlank(modelNum)) ||
                (StringUtils.isNotBlank(message) && StringUtils.isNotBlank(modelNum))) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_IS_NULL);
        }

        Map<Integer, String> paramMap = Maps.newHashMap();
        if (StringUtils.isNotEmpty(param)) {
            paramMap = JSON.parseObject(param, new TypeReference<Map<Integer, String>>() {});
        }

        adminApproveService.sendCaseApproveSmsWithRecord(mobile, message, modelNum, 0, userId, paramMap);
        return NewResponseUtil.makeSuccess(null);
    }
    @RequestMapping(path = "/add-org-no-handle-limit")
    @RequiresPermission("work-order-flow:add-org-no-handle-limit")
    public Response<String> addOrgNoHandleLimit(@RequestParam("orgId") int orgId, @RequestParam("limit") int limit) {
        assignRecordBiz.addOrgNoHandleLimit(orgId, limit);
        return NewResponseUtil.makeSuccess("");
    }


    @RequestMapping(path = "/select-property-by-org-id")
    @RequiresPermission("work-order-flow:select-property-by-org-id")
    public Response<WorkFlowTypeProperty.FlowTypePropertyEntity> selectPropertyByOrgId(@RequestParam("orgId") int orgId) {
        return NewResponseUtil.makeSuccess(assignRecordBiz.selectPropertyByOrgId(orgId));
    }



}
