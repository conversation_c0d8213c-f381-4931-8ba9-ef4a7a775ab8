package com.shuidihuzhu.cf.admin.mq.juanzhuan;

import com.shuidihuzhu.cf.admin.mq.common.BaseMessageConsumer;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.service.workorder.juanzhan.JuanzhuanWorkOrderService;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @DATE 2020/5/9
 */
@Service
@Slf4j
@RocketMQListener(id = "juanzhuan_"+ CfClientMQTagCons.GRABORDER_NOTICE_ADMINORDER,
        tags = CfClientMQTagCons.GRABORDER_NOTICE_ADMINORDER,
        group = "juanzhuang_GRABORDER_group",
        topic = MQTopicCons.CF)
public class JuanzhuanRobConsumer extends BaseMessageConsumer<String> implements MessageListener<String> {

    @Autowired
    private JuanzhuanWorkOrderService juanzhuanWorkOrderService;

    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Override
    protected boolean handle(ConsumerMessage<String> consumerMessage) {

        String infoId = consumerMessage.getPayload();

        CfInfoSimpleModel simpleModel = crowdfundingDelegate.getCfInfoSimpleModel(infoId);

        juanzhuanWorkOrderService.rob4Close(simpleModel.getId());

        return true;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
