package com.shuidihuzhu.cf.admin.mq.record;

import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.model.QuestionnaireRecord;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @DATE 2020/2/12
 */
@Service
@RocketMQListener(id = MQTagCons.record_1h_check,
        group = "cf-admin" + MQTagCons.record_1h_check,
        tags = MQTagCons.record_1h_check,
        topic = MQTopicCons.CF)
@Slf4j
public class RecordLaterSendConsumer implements MessageListener<QuestionnaireRecord> {

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<QuestionnaireRecord> mqMessage) {
        return ConsumeStatus.CONSUME_SUCCESS;
    }

}
