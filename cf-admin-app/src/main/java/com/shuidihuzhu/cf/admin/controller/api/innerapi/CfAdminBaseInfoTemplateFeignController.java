package com.shuidihuzhu.cf.admin.controller.api.innerapi;

import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplatize;
import com.shuidihuzhu.cf.service.crowdfunding.AdminBaseInfoTemplateService;
import com.shuidihuzhu.client.cf.admin.client.CfAdminBaseInfoTemplateFeignClient;
import com.shuidihuzhu.client.model.CommonPageModel;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

@Slf4j
@RestController
public class CfAdminBaseInfoTemplateFeignController implements CfAdminBaseInfoTemplateFeignClient {

    @Autowired
    private AdminBaseInfoTemplateService templateService;

    @Override
    public Response<CommonPageModel<CfBaseInfoTemplatize>> get1v1TemplateList(String context, Integer relationType, String diseaseName, Integer age, int current, int pageSize) {
        if (Objects.isNull(relationType)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        return templateService.get1v1TemplateList(context, relationType, diseaseName, age, current, pageSize);
    }
}
