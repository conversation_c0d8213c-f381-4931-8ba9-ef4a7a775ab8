package com.shuidihuzhu.cf.admin.mq;


import com.shuidihuzhu.cf.biz.admin.AdminOrganizationBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderClassifySettingsBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderFlowBiz;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.model.admin.AdminOrganization;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderClassifySettings;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView;
import com.shuidihuzhu.cf.service.workorder.FinanceWorkOrderBiz;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RocketMQListener(id = MQTagCons.ADMIN_FINANCE_OPERATER_GENERATE_FLOW_ORDER,
        group = "cf-admin"+MQTagCons.ADMIN_FINANCE_OPERATER_GENERATE_FLOW_ORDER,
        tags = MQTagCons.ADMIN_FINANCE_OPERATER_GENERATE_FLOW_ORDER,
        topic = MQTopicCons.CF)
@Slf4j
public class AdminFlowOrderGenerateConsumer implements MessageListener<FinanceWorkOrderBiz.AdminFlowOrderGenerate>  {

    @Autowired
    private AdminWorkOrderFlowBiz orderFlowBiz;

    @Autowired
    private AdminOrganizationBiz organizationBiz;

    @Autowired
    private AdminWorkOrderClassifySettingsBiz classifySettingsBiz;

    // 水滴筹-运营-资金组
    private static final String ORG_NAME = "资金组";

    // 水滴筹-提现&退款  提现失败
    private static final String CLASSIFY_NAME = "提现失败";

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<FinanceWorkOrderBiz.AdminFlowOrderGenerate> mqMessage) {

        log.info("收到打款失败创建工单的请求 param:{}", mqMessage.getPayload());
        AdminWorkOrderFlowView vo = new AdminWorkOrderFlowView();


        FinanceWorkOrderBiz.AdminFlowOrderGenerate flowOrder = mqMessage.getPayload();

        vo.setCaseId(flowOrder.getCaseId());
        vo.setCaseTitle(StringUtils.trimToEmpty(flowOrder.getCaseTitle()));
        vo.setMobile(StringUtils.trimToEmpty(flowOrder.getMobile()));
        vo.setProblemContent(StringUtils.trimToEmpty(flowOrder.getProblemDesc()));

        vo.setProblemImg("");
        vo.setHandleImg("");

        vo.setUserIdentity(AdminWorkOrderFlowView.WorkOrderFlowUserIdentity.DEFAULT.getCode());

        // 分配的组织
        vo.setProblemType(getOrgIdByName(ORG_NAME));
        vo.setSecondClassifyId(getClassifyIdByName(CLASSIFY_NAME));


        Response result = orderFlowBiz.createWorkOrderFlow(vo);
        log.info("打款失败自动创建信息流转工单. result code: {}, message: {}", result.getCode(), result.getMsg());
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private int getOrgIdByName(String orgName) {
        List<AdminOrganization> orgList = organizationBiz.getAdminOrganizationByName(orgName);

        return CollectionUtils.isEmpty(orgList) ? 0 : orgList.get(0).getId();
    }

    private long getClassifyIdByName(String classfyName) {
        List<AdminWorkOrderClassifySettings> classifySettings = classifySettingsBiz.selectClassifySettingsByText(classfyName);
        return CollectionUtils.isEmpty(classifySettings) ? 0 : classifySettings.get(0).getId();
    }


}
