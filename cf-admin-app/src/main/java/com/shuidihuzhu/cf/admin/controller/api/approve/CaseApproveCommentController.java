package com.shuidihuzhu.cf.admin.controller.api.approve;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.service.approve.remark.SubsidyApplyService;
import com.shuidihuzhu.cf.vo.crowdfunding.SubsidyApplyRecordVO;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.admin.delegate.SeaUserAccountDelegate;
import com.shuidihuzhu.cf.biz.admin.AdminApproveExtBiz;
import com.shuidihuzhu.cf.delegate.OrganizationDelegate;
import com.shuidihuzhu.cf.domain.approve.AdminApproveExt;
import com.shuidihuzhu.cf.domain.approve.AdminApproveOperateRecord;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.approve.ApproveCommentTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingApprove;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkService;
import com.shuidihuzhu.cf.vo.crowdfunding.CrowdfundingApproveCommentVo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Api("案例审核评论接口")
@RequestMapping("admin/cf/approve/comment")
@RestController
public class CaseApproveCommentController {

    @Autowired
    private ApproveRemarkService approveRemarkService;
    @Resource
    private SubsidyApplyService subsidyApplyService;

    @Autowired
    private ApproveRemarkOldService approveRemarkOldService;

    @Autowired
    private AdminApproveExtBiz adminApproveExtBiz;

    @Autowired
    private OrganizationDelegate organizationDelegate;

    @Autowired
    private SeaUserAccountDelegate seaUserAccountDelegate;

    private static final String EMPTY_ORG = "EMPTY_ORG";

    @ApiOperation("获取该案例所有评论筛选数据")
    @PostMapping(path = "get-filter-data-by-case-id")
    @RequiresPermission("approve-comment:get-filter-data-by-case-id")
    Response<ApproveCommentFilterVO> getFilterDataByCaseId(@RequestParam int caseId) {
        Set<String> orgs = approveRemarkService.listOrgByCaseId(caseId);
        Map<Integer, AdminUserAccountModel> userMap = approveRemarkService.listUserMapByCaseId(caseId);
        if (MapUtils.isNotEmpty(userMap)) {
            userMap.values().forEach(r -> {
                r.setIdCard(StringUtils.EMPTY);
                r.setMobile(StringUtils.EMPTY);
                r.setMail(StringUtils.EMPTY);
            });
        }
        return NewResponseUtil.makeSuccess(new ApproveCommentFilterVO(orgs, userMap));
    }

    @PostMapping(path = "list-by-condition")
    @RequiresPermission("approve-comment:list-by-condition")
    Response<List<List<CrowdfundingApproveCommentVo>>> listByCondition(
            @RequestParam int caseId,
            @RequestParam(required = false) Integer operatorId,
            @ApiParam(value = "筛选组织", example = "org=EMPTY_ORG,为仅查看组织为空的") @RequestParam(required = false) String org,
            @ApiParam("搜索内容") @RequestParam(required = false) String key,
            @RequestParam(required = false) ApproveCommentTypeEnum filterType) {
        // 传空查全部
        if (StringUtils.isEmpty(org)) {
            org = null;
        }
        // 传empty 查空组织
        if (StringUtils.equalsIgnoreCase(org, EMPTY_ORG)) {
            org = "";
        }
        List<List<CrowdfundingApproveCommentVo>> lists = approveRemarkService.listByCaseIdGrouped(caseId, operatorId, org, key, filterType);
        return NewResponseUtil.makeSuccess(lists);
    }

    @ApiOperation(value = "获取服务费补贴审核记录")
    @PostMapping(path = "list-subsidy-apply-record")
    @RequiresPermission("approve-comment:list-subsidy-apply-record")
    Response<List<SubsidyApplyRecordVO>> listSubsidyApplyRecord(@RequestParam Integer caseId) {
        if (Objects.isNull(caseId)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        return NewResponseUtil.makeSuccess(subsidyApplyService.listSubsidyApplyRecords(caseId));
    }

    @PostMapping(path = "update-approve-status")
    @RequiresPermission("approve-comment:update-approve-status")
    public Response<Void> updateApproveStatus(
            @RequestParam int approveId,
            @RequestParam int approveStatus,
            @RequestParam(required = false) String approveStatusComment) {
        CrowdfundingApprove approve = approveRemarkOldService.getById(approveId);
        if (Objects.isNull(approve)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        // 添加/更新状态
        List<AdminApproveExt> adminApproveExtList = adminApproveExtBiz.listByApproveIdAndExtName(approveId, AdminApproveExt.APPROVE_STATUS);
        if (CollectionUtils.isNotEmpty(adminApproveExtList)) {
            AdminApproveExt adminApproveExt = adminApproveExtList.get(0);
            adminApproveExtBiz.updateExtValue(adminApproveExt.getId(), String.valueOf(approveStatus));
        } else {
            AdminApproveExt adminApproveExt = new AdminApproveExt();
            adminApproveExt.setApproveId(approveId);
            adminApproveExt.setExtName(AdminApproveExt.APPROVE_STATUS);
            adminApproveExt.setExtValue(String.valueOf(approveStatus));
            adminApproveExtBiz.insert(adminApproveExt);
        }

        // 添加操作记录
        int adminUserId = ContextUtil.getAdminUserId();
        String simpleOrganization = organizationDelegate.getSimpleOrganization(adminUserId);
        Map<Integer, String> byOperators = seaUserAccountDelegate.getByOperators(Collections.singletonList(adminUserId));
        String operator = byOperators.get(adminUserId);
        AdminApproveOperateRecord adminApproveOperateRecord = AdminApproveOperateRecord.builder()
                .operateReason(approveStatusComment)
                .operateType(approveStatus)
                .operateOrgName(simpleOrganization)
                .operateName(operator)
                .operateId(adminUserId)
                .operateTime(new Date())
                .build();
        String jsonString = JSONObject.toJSONString(adminApproveOperateRecord);
        AdminApproveExt adminApproveExtRecord = new AdminApproveExt();
        adminApproveExtRecord.setApproveId(approveId);
        adminApproveExtRecord.setExtName(AdminApproveExt.APPROVE_OPERATION_RECORD);
        adminApproveExtRecord.setExtValue(jsonString);
        adminApproveExtBiz.insert(adminApproveExtRecord);

        return NewResponseUtil.makeSuccess(null);
    }

    @PostMapping(path = "approve-operation-record")
    @RequiresPermission("approve-comment:approve-operation-record")
    Response<List<AdminApproveOperateRecord>> approveOperationRecord(@RequestParam int approveId) {
        List<AdminApproveExt> adminApproveExtList = adminApproveExtBiz.listByApproveIdAndExtName(approveId, AdminApproveExt.APPROVE_OPERATION_RECORD);
        if (CollectionUtils.isEmpty(adminApproveExtList)) {
            return NewResponseUtil.makeSuccess(new ArrayList<>());
        }
        List<AdminApproveOperateRecord> operateRecordList = adminApproveExtList.stream()
                .map(m -> JSONObject.parseObject(m.getExtValue(), AdminApproveOperateRecord.class))
                .collect(Collectors.toList());
        return NewResponseUtil.makeSuccess(operateRecordList);
    }

    /**
     * <AUTHOR>
     * 评论筛选数据
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    private static class ApproveCommentFilterVO {

        @ApiModelProperty("筛选用组织列表")
        private Set<String> organizations;

        @ApiModelProperty("筛选用用户列表")
        private Map<Integer, AdminUserAccountModel> userMap;
    }

}
