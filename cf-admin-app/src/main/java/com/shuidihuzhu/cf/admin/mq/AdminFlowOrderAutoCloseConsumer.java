package com.shuidihuzhu.cf.admin.mq;

import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.admin.AdminWorkOrderBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderFlowBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderFlowRecordBiz;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminWorkOrderFlowConst;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderFlow;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderFlowRecord;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowParam;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

import static com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst.Status.CREATED;

@Service
@RocketMQListener(id = MQTagCons.ADMIN_CLEW_NOTICE_TO_END_FLOW_ORDER,
        group = "cf-admin"+MQTagCons.ADMIN_CLEW_NOTICE_TO_END_FLOW_ORDER,
        tags = MQTagCons.ADMIN_CLEW_NOTICE_TO_END_FLOW_ORDER,
        topic = MQTopicCons.CF)
@Slf4j
public class AdminFlowOrderAutoCloseConsumer implements MessageListener<AdminFlowOrderAutoCloseConsumer.NoticeCloseWorkOrderModel> {

    @Autowired
    private AdminWorkOrderFlowBiz orderFlowBiz;

    @Autowired
    private AdminWorkOrderBiz orderBiz;

    @Autowired
    private SeaAccountClientV1 clientV1;

    @Autowired
    private AdminWorkOrderFlowRecordBiz flowRecordBiz;

    Set<Integer> EVENT_TYPES = Sets.newHashSet(0, 1, 2);
    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<NoticeCloseWorkOrderModel> mqMessage) {

        log.info("外呼平台自动关闭工单 收到的信息 msg：{}", mqMessage);
        if (mqMessage == null || mqMessage.getPayload() == null || !EVENT_TYPES.contains(mqMessage.getPayload().getEventType())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        NoticeCloseWorkOrderModel handleOrderModel = mqMessage.getPayload();
        AdminWorkOrderFlow flowOrder = orderFlowBiz.selectByWorkOrderId(handleOrderModel.getWorkOrderId());
        List<AdminWorkOrder> workOrders = orderBiz.selectByIds(Arrays.asList(handleOrderModel.getWorkOrderId()));


        if (flowOrder == null || CollectionUtils.isEmpty(workOrders)) {
            log.error("外呼平台自动关闭工单 不能找到对应工单 workOrderId:{}, flowOrder:{}, order:{}, misName:{}", handleOrderModel.getWorkOrderId(),
                    flowOrder, workOrders, handleOrderModel.getMis());
            return ConsumeStatus.CONSUME_SUCCESS;
        }


        AdminWorkOrder parentOrder = workOrders.get(0);
        if (parentOrder.getOrderStatus() == AdminWorkOrderConst.Status.HANDLE_SUCCESS.getCode() ||
                parentOrder.getOrderStatus() == AdminWorkOrderConst.Status.NO_NEED_HANDLE.getCode()) {
            log.info("外呼平台自动关闭工单 工单已是终态 workOrderId:{} orderStatus:{}", parentOrder.getId(), parentOrder.getOrderStatus());
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        AuthRpcResponse<List<AdminUserAccountModel>> authRpcResponse = clientV1.getUserAccountsByMisLike(handleOrderModel.getMis(),1);

        int userId = 0;
        if (authRpcResponse != null && CollectionUtils.isNotEmpty(authRpcResponse.getResult())) {
            for (AdminUserAccountModel accountModel : authRpcResponse.getResult()) {
                if (handleOrderModel.getMis().equals(accountModel.getMis())) {
                    userId = accountModel.getId();
                    break;
                }
            }
        }

        if (StringUtils.isNotBlank(handleOrderModel.getSource()) && "BD".equals(handleOrderModel.getSource())){
            parentOrder.setTaskType(AdminWorkOrderConst.TaskType.flow_workorder_bd.getCode());
            if (userId == 0){
                userId = 102;
            }
        }

        if ("cailiaoWorkOrder".equals(handleOrderModel.getSource()) && userId == 0){
            userId = 102;
        }

        if (userId == 0 && (handleOrderModel.getEventType() == 0 || handleOrderModel.getEventType() == 1)) {
            log.error("外呼平台自动关闭工单 mis:{} 不能找到用户名", handleOrderModel.getMis());
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        if (handleOrderModel.getEventType() == 0) {
            closeFlowOrder(parentOrder, flowOrder, userId);
        }

        if (handleOrderModel.getEventType() == 1) {
            assignFlowOrder(parentOrder, flowOrder, userId);
        }
        //工单状态扭转为不需要处理
        if (handleOrderModel.getEventType() == 2) {
            int u = 102;
            if ("cailiaoWorkOrder".equals(handleOrderModel.getSource())){
                u = userId;
            }
            noNeedHandleFlowOrder(parentOrder, flowOrder, u);
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }


    private void closeFlowOrder(AdminWorkOrder parentOrder, AdminWorkOrderFlow flowOrder, int userId) {
        AdminWorkOrderFlowParam.HandleParam param = new AdminWorkOrderFlowParam.HandleParam();

        if (parentOrder.getOperatorId() == 0) {
            log.info("外呼平台自动关闭工单 workOrderId:{} currUseId:{}", parentOrder.getId(), userId);
            param.setUserId(userId);
            String comment = "外呼";
            if (parentOrder.getTaskType() == AdminWorkOrderConst.TaskType.reject_workorder_test.getCode()){
                comment = "1V1测试";
            }
            if (parentOrder.getTaskType() == AdminWorkOrderConst.TaskType.flow_workorder_bd.getCode()){
                comment = "线下BD关联工单";
            }
            // 添加一条的日志记录
            AdminWorkOrderFlowRecord record =
                    new AdminWorkOrderFlowRecord(flowOrder, userId, comment,
                            AdminWorkOrderFlowConst.OperateTypeEnum.ASSIGN_WORK_FLOW.getCode(), parentOrder.getLevel(), flowOrder.getSecondClassifyId());
            flowRecordBiz.insertOne(record);
            log.info("外呼平台信息流转工单 userId:{} 领取了工单 {}", userId, parentOrder.getId());

        } else {
            param.setUserId(parentOrder.getOperatorId());
        }

        param.setWorkOrderId(parentOrder.getId());
        param.setComment("系统自动关闭");
        param.setHandleType(AdminWorkOrderFlowConst.handleType.FINISH.getValue());
        param.setLevel(parentOrder.getLevel());

        orderFlowBiz.handleWorkFlowOrder(param);

    }


    private void noNeedHandleFlowOrder(AdminWorkOrder parentOrder, AdminWorkOrderFlow flowOrder, int userId) {
        AdminWorkOrderFlowParam.HandleParam param = new AdminWorkOrderFlowParam.HandleParam();

        param.setUserId(userId);

        String comment = "外呼";
        if (parentOrder.getTaskType() == AdminWorkOrderConst.TaskType.reject_workorder_test.getCode()){
            comment = "1V1测试";
        }
        if (parentOrder.getTaskType() == AdminWorkOrderConst.TaskType.flow_workorder_bd.getCode()){
            comment = "线下BD关联工单";
        }
        // 添加一条的日志记录
        AdminWorkOrderFlowRecord record =
                new AdminWorkOrderFlowRecord(flowOrder, userId, comment,
                        AdminWorkOrderFlowConst.OperateTypeEnum.NO_HANDLE_WORK_FLOW.getCode(), parentOrder.getLevel(), flowOrder.getSecondClassifyId());
        flowRecordBiz.insertOne(record);
        log.info("外呼平台信息:{}舍弃线索,工单不需要处理:{}", userId, record);

        param.setWorkOrderId(parentOrder.getId());
        param.setComment("系统自动关闭");
        param.setHandleType(AdminWorkOrderFlowConst.handleType.NO_HANDLE.getValue());
        param.setLevel(parentOrder.getLevel());

        orderFlowBiz.handleWorkFlowOrder(param);

    }

    private void assignFlowOrder(AdminWorkOrder parentOrder, AdminWorkOrderFlow flowOrder, int userId) {
        if (parentOrder.getOperatorId() != 0) {
            log.info("当前工单已有操作人.order:{} operatorId:{}", parentOrder.getId(), parentOrder.getOperatorId());
            return;
        }

        orderBiz.updateWithOperatorIds(Arrays.asList(parentOrder.getId()), userId, CREATED.getCode());
        log.info("外呼平台信息流转工单 userId:{} 领取了工单 {}", userId, parentOrder.getId());

        String comment = "外呼";

        if (parentOrder.getTaskType() == AdminWorkOrderConst.TaskType.reject_workorder_test.getCode()){
            comment = "1V1测试";
        }
        if (parentOrder.getTaskType() == AdminWorkOrderConst.TaskType.flow_workorder_bd.getCode()){
            comment = "线下BD关联工单";
        }
        AdminWorkOrderFlowRecord record =
                new AdminWorkOrderFlowRecord(flowOrder, userId, comment,
                        AdminWorkOrderFlowConst.OperateTypeEnum.ASSIGN_WORK_FLOW.getCode(), parentOrder.getLevel(), flowOrder.getSecondClassifyId());
        flowRecordBiz.insertOne(record);

    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class NoticeCloseWorkOrderModel {

        private long workOrderId;
        private String mis;
        // 0 关闭 1 处理中
        private int eventType;

        private String source;

    }

}
