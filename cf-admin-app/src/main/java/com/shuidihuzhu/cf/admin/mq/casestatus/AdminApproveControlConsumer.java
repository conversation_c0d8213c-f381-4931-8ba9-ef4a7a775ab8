package com.shuidihuzhu.cf.admin.mq.casestatus;

import com.shuidihuzhu.cf.admin.mq.common.BaseMessageConsumer;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.event.ApproveControlReleaseEvent;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.approve.ApproveControlService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 30m回收锁逻辑
 * <AUTHOR>
 */
@Service
@Slf4j
@RocketMQListener(id = MQTagCons.CF_APPROVE_CONTROL_RELEASE,
        tags = MQTagCons.CF_APPROVE_CONTROL_RELEASE,
        topic = MQTopicCons.CF)
public class AdminApproveControlConsumer extends BaseMessageConsumer<ApproveControlReleaseEvent>
        implements MessageListener<ApproveControlReleaseEvent> {

    @Autowired
    private ApproveControlService approveControlService;

    @Override
    protected boolean handle(ConsumerMessage<ApproveControlReleaseEvent> consumerMessage) {
        ApproveControlReleaseEvent approveControlReleaseEvent = consumerMessage.getPayload();
        approveControlService.releaseControl(approveControlReleaseEvent.getControlId());
        return true;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
