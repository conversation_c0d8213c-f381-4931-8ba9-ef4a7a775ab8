package com.shuidihuzhu.cf.admin.configuration;

import brave.propagation.ExtraFieldPropagation;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Slf4j
@Configuration
public class SeaAuthUserIdConfigurerAdapter implements WebMvcConfigurer {

    @Bean
    public SeaAuthUserIdInterceptor seaAuthUserIdInterceptor(){
        return new SeaAuthUserIdInterceptor();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(seaAuthUserIdInterceptor())
                .addPathPatterns("/**");
    }

    class SeaAuthUserIdInterceptor extends HandlerInterceptorAdapter {
        @Override
        public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object o) throws Exception {
            String userId = request.getParameter("userId");
            if (StringUtils.isEmpty(userId)) {
                userId = request.getHeader("userId");
            }
            log.debug("SeaAuthUserIdInterceptor userId={}", userId);
            if (!Strings.isNullOrEmpty(userId)) {
                ExtraFieldPropagation.set("sea-auth-userid", userId);
            }
            return true;
        }

        @Override
        public void postHandle(HttpServletRequest request, HttpServletResponse response, Object o, ModelAndView modelAndView) throws Exception {
        }

        @Override
        public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object o, Exception ex) throws Exception {

        }
    }
}
