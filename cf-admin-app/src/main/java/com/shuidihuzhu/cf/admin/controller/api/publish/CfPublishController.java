package com.shuidihuzhu.cf.admin.controller.api.publish;

import com.shuidihuzhu.cf.facade.eagle.EagleRecordService;
import com.shuidihuzhu.cf.service.CloudAppActivityService;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.devops.cloud.app.activity.client.vo.CloudAppActivityVO;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/4/29
 */

@Controller
@RequestMapping(path = "/admin/cf/publish")
public class CfPublishController {

    @Resource
    private EagleRecordService eagleRecordService;

    @Resource
    private CloudAppActivityService cloudAppActivityService;

    @RequestMapping(path = "/get-eagle-record", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<PageResult<EagleRecordService.EagleRecord>> getEagleRecord(@RequestParam(required = false, name = "startTime") String startTime,
                                                                               @RequestParam(required = false, name = "endTime") String endTime,
                                                                               @RequestParam(required = false, defaultValue = "1", name = "pageNum") int pageNum,
                                                                               @RequestParam(required = false, defaultValue = "10", name = "pageSize") int pageSize,
                                                                               @RequestParam(required = false, name = "projectId") String projectId) {

        Date updateBeginTime = DateUtil.parseDateTime(startTime);
        Date updateEndTime = DateUtil.parseDateTime(endTime);

        PageResult<EagleRecordService.EagleRecord> result = eagleRecordService.getRecords(projectId, updateBeginTime, updateEndTime, pageNum, pageSize);
        return NewResponseUtil.makeSuccess(result);
    }

    @RequestMapping(path = "/get-app-activity", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<PageResult<CloudAppActivityVO>> get(@RequestParam(required = false, name = "startTime") String startTime,
                                                        @RequestParam(required = false, name = "endTime") String endTime,
                                                        @RequestParam(required = false, defaultValue = "1", name = "pageNum") int pageNum,
                                                        @RequestParam(required = true, defaultValue = "60", name = "projectId") int projectId) {


        PageResult<CloudAppActivityVO> pageResult = cloudAppActivityService.queryAppActivity(projectId, pageNum, startTime, endTime);

        return NewResponseUtil.makeSuccess(pageResult);
    }
}
