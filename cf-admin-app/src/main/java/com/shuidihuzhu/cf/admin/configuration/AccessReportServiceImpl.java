package com.shuidihuzhu.cf.admin.configuration;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.client.alarm.center.report.ResponseCodeReportServiceImpl;
import com.shuidihuzhu.common.web.model.AccessInfo;
import com.shuidihuzhu.common.web.service.report.AbstractAccessReportService;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import com.shuidihuzhu.data.analytics.javasdk.core.UserTagTypeEnum;
import com.shuidihuzhu.data.servicelog.meta.cf.OpUserOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Objects;


@Slf4j
@Service
public class AccessReportServiceImpl extends AbstractAccessReportService {

    @Autowired
    private Analytics analytics;

    private ResponseCodeReportServiceImpl responseCodeReportService;

    @PostConstruct
    public void init() {
        responseCodeReportService = new ResponseCodeReportServiceImpl();
        responseCodeReportService.setAnalytics(analytics);
    }

    @Override
    protected String getUserUniqueId(AccessInfo accessInfo) {
        long adminUserId = ContextUtil.getAdminUserId();
        return String.valueOf(adminUserId);
    }

    @Override
    protected boolean reportAsync(AccessInfo accessInfo) {
        if (Objects.isNull(accessInfo) || StringUtils.isEmpty(accessInfo.getUserId()) || StringUtils.equals("0", accessInfo.getUserId())) {
            log.debug("ignore this report");
            reportResponseCode(accessInfo);
            return true;
        }

        OpUserOperation opUserOperation = new OpUserOperation();
        opUserOperation.setOp_user_id(accessInfo.getUserId());

        opUserOperation.setOp_request(JSON.toJSONString(accessInfo.getParam()));
        opUserOperation.setOp_response(JSON.toJSONString(accessInfo.getResponse()));

        opUserOperation.setUri(accessInfo.getUri());
        opUserOperation.setOp_user_org_id("-1");
        opUserOperation.setOp_user_name("UNKNOWN");

        opUserOperation.setUser_tag(String.valueOf(accessInfo.getUserId()));
        opUserOperation.setUser_tag_type(UserTagTypeEnum.userid);

        analytics.track(opUserOperation);
        log.info("report access info {} {}", opUserOperation.getOp_user_id(), opUserOperation.getUri());
        reportResponseCode(accessInfo);
        return true;
    }

    private void reportResponseCode(AccessInfo accessInfo) {
        try {
            //线程套线程运行，放最后调用，只能
            responseCodeReportService.report(accessInfo);
        } catch (Throwable e) {
            log.warn("responseCodeReportService error", e);
        }
    }
}
