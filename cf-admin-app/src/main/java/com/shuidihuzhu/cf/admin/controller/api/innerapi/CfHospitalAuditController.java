package com.shuidihuzhu.cf.admin.controller.api.innerapi;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfHospitalAuditBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCfFundUseAuditDao;
import com.shuidihuzhu.cf.delegate.finance.IFinanceDelegate;
import com.shuidihuzhu.cf.finance.model.vo.CfDrawCashApplyVo;
import com.shuidihuzhu.cf.model.admin.CfHospitalAuditInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCfFundUseAuditInfo;
import com.shuidihuzhu.client.cf.admin.model.HospitalAudit;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import jodd.util.ThreadUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @DATE 2019/9/11
 */
@Slf4j
@RestController
@RequestMapping("innerapi/cf/admin/hospital")
public class CfHospitalAuditController {
    @Resource
    private AdminCfHospitalAuditBiz biz;
    @Autowired
    private AdminCfFundUseAuditDao adminCfFundUseAuditDao;
    @Autowired
    private IFinanceDelegate financeDelegate;

    @PostMapping("get")
    public Response<HospitalAudit> getHospitalAudit(@RequestParam(value = "caseUuid") String caseUuid){

        HospitalAudit hospitalAudit = new HospitalAudit();

        CfHospitalAuditInfoExt ext = biz.getByInfoUuid(caseUuid);

        if (ext == null){
            return NewResponseUtil.makeSuccess(hospitalAudit);
        }

        hospitalAudit.setId(ext.getId());
        hospitalAudit.setAuditStatus(ext.getAuditStatus());
        hospitalAudit.setInfoUuid(ext.getInfoUuid());
        String supplyReason = ext.getReason();
        //设置下发原因
        if (StringUtils.isNotBlank(ext.getReasonSupply())) {
            supplyReason += ";" + ext.getReasonSupply();
        }
        hospitalAudit.setSupplyReason("下发原因:" + supplyReason);

        return NewResponseUtil.makeSuccess(hospitalAudit);
    }

    @PostMapping("update-progress-time")
    public Response<String> updateProgressTime(int start, int end){
        for (int i = start; i<end; i++) {
            AdminCfFundUseAuditInfo cfFundUseAuditInfo = adminCfFundUseAuditDao.selectByProgressId(i);
            if (null == cfFundUseAuditInfo) {
                continue;
            }
            Response<CfDrawCashApplyVo> response = financeDelegate.getApplyInfo(cfFundUseAuditInfo.getCrowdfundingId());
            CfDrawCashApplyVo drawCashApplyVo = response.getData();
            if (null == drawCashApplyVo) {
                log.warn("update-v1异常 caseId:{}", cfFundUseAuditInfo.getCrowdfundingId());
                continue;
            }

            Timestamp drawFinishTime = drawCashApplyVo.getFinishTime();
            if (null == drawFinishTime){
                log.warn("update-v2异常 caseId:{}", cfFundUseAuditInfo.getCrowdfundingId());
                continue;
            }
            adminCfFundUseAuditDao.updateDrawTime(i, drawFinishTime);
            log.warn("update-v3数据变更 caseId:{} drawFinishTime:{}", cfFundUseAuditInfo.getCrowdfundingId(),drawFinishTime);
            if (i % 1000 == 0) {
                ThreadUtil.sleep(100);
            }
        }
        return NewResponseUtil.makeSuccess("ok");
    }
}
