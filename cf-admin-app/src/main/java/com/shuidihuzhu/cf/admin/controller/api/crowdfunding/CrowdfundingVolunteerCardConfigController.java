package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.shuidihuzhu.cf.delegate.SeaAccountDelegate;
import com.shuidihuzhu.cf.service.crowdfunding.CrowdfundingVolunteerCardConfigService;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteerCardConfigDO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by lixiaoshuang on 2018/2/27.
 */
@RestController
@RequestMapping(path = "/admin/crowdfunding/volunteer/card", produces = "application/json;charset=UTF-8")
@Slf4j
public class CrowdfundingVolunteerCardConfigController {

    @Autowired
    private CrowdfundingVolunteerCardConfigService crowdfundingVolunteerCardConfigService;

    @Autowired
    private SeaAccountDelegate seaAccountDelegate;

    /**
     * 获取顾问或案例卡片展示配置
     */
    @ApiOperation("获取顾问卡片配置")
    @RequiresPermission("volunteerCard:get")
    @RequestMapping(path = "/get-card-config", method = RequestMethod.POST)
    public Response<CrowdfundingVolunteerCardConfigDO> getVolunteerCardConfig(@RequestParam(name = "configType") Integer configType,
                                                @RequestParam(name = "caseId", required = false, defaultValue = "0") Integer caseId,
                                                @RequestParam(name = "uniqueCode", required = false, defaultValue = "") String uniqueCode){
        CrowdfundingVolunteerCardConfigDO param = new CrowdfundingVolunteerCardConfigDO();
        param.setConfigType(configType);
        param.setCaseId(caseId);
        param.setUniqueCode(uniqueCode);
        CrowdfundingVolunteerCardConfigDO config = crowdfundingVolunteerCardConfigService.getVolunteerCardConfig(param);
        return NewResponseUtil.makeSuccess(config);
    }

    /**
     * 展示顾问或案例卡片展示配置
     */
    @ApiOperation("展示顾问卡片")
    @RequiresPermission("volunteerCard:show")
    @RequestMapping(path = "/show-card-config", method = RequestMethod.POST)
    public Response<String> showVolunteerCardConfig(@RequestParam(name = "configType") Integer configType,
                                                    @RequestParam(name = "caseId", required = false, defaultValue = "0") Integer caseId,
                                                    @RequestParam(name = "uniqueCode", required = false, defaultValue = "") String uniqueCode,
                                                    @RequestParam(name = "reasonDesc", required = false, defaultValue = "") String reasonDesc){
        CrowdfundingVolunteerCardConfigDO param = new CrowdfundingVolunteerCardConfigDO();
        param.setConfigType(configType);
        param.setCaseId(caseId);
        param.setUniqueCode(uniqueCode);
        param.setReasonDesc(reasonDesc);
        long userId = ContextUtil.getAdminLongUserId();
        String name = seaAccountDelegate.getNameByUserId((int) userId);
        param.setOperateUserId(userId);
        param.setOperateUserName(name);
        return crowdfundingVolunteerCardConfigService.showVolunteerCardConfig(param);
    }


    /**
     * 隐藏顾问或案例卡片展示配置
     */
    @ApiOperation("隐藏顾问卡片")
    @RequiresPermission("volunteerCard:hide")
    @RequestMapping(path = "/hide-card-config", method = RequestMethod.POST)
    public Response<String> hideVolunteerCardConfig(@RequestParam(name = "configType") Integer configType,
                                                    @RequestParam(name = "caseId", required = false, defaultValue = "0") Integer caseId,
                                                    @RequestParam(name = "uniqueCode", required = false, defaultValue = "") String uniqueCode,
                                                    @RequestParam(name = "reasonDesc", required = false, defaultValue = "") String reasonDesc){
        CrowdfundingVolunteerCardConfigDO param = new CrowdfundingVolunteerCardConfigDO();
        param.setConfigType(configType);
        param.setCaseId(caseId);
        param.setUniqueCode(uniqueCode);
        param.setReasonDesc(reasonDesc);
        long userId = ContextUtil.getAdminLongUserId();
        String name = seaAccountDelegate.getNameByUserId((int) userId);
        param.setOperateUserId(userId);
        param.setOperateUserName(name);
        return crowdfundingVolunteerCardConfigService.hideVolunteerCardConfig(param);
    }

}
