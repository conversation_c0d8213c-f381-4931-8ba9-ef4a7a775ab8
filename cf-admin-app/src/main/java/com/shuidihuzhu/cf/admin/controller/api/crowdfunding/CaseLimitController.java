package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.risk.client.admin.hit.RiskStrategyHitClient;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistCallPhaseEnum;
import com.shuidihuzhu.cf.service.crowdfunding.CfAdminLimitService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @DATE 2020/9/9
 */
@RefreshScope
@RestController
@Slf4j
@RequestMapping(path = "/admin/crowdfunding/limit")
public class CaseLimitController {

    @Resource
    private RiskStrategyHitClient riskStrategyHitClient;
    @Resource
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;

    @Value("${cf.sea.poor.label.new.text.time:2021-03-20 00:00:00}")
    private String seaPoorLabelNewTextTime;

    @PostMapping("payee")
    @RequiresPermission("cf-limit:payee")
    public Response<Boolean> payeeLimit(@RequestParam("caseId") int caseId){

        Response<Boolean> abiResp = riskStrategyHitClient.hadRiskHitPayee(caseId);
        log.info("payeeLimit caseId={}, response={}", caseId, abiResp);
        if (abiResp != null && abiResp.ok() && abiResp.getData()) {
            return NewResponseUtil.makeSuccess(true);
        }
//        boolean flag = cfAdminLimitService.caseInLimit(caseId, BlacklistCallPhaseEnum.SUBMIT_MATERIAL_REVIEW);

      return NewResponseUtil.makeSuccess(false);
    }

    @PostMapping("case-text-limit")
    @RequiresPermission("cf-limit:case-text")
    public Response<Boolean> caseTextLimit(@RequestParam("infoId") String infoId){
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoId);
        if (Objects.isNull(crowdfundingInfo)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        boolean after = crowdfundingInfo.getCreateTime().after(com.shuidihuzhu.common.util.DateUtil.getStr2LDate(seaPoorLabelNewTextTime));
        return NewResponseUtil.makeSuccess(after);
    }
}
