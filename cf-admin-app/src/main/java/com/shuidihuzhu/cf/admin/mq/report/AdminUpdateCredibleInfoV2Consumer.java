package com.shuidihuzhu.cf.admin.mq.report;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.biz.crowdfunding.IAdminCredibleInfoService;
import com.shuidihuzhu.cf.client.adminpure.constants.ReportCons;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.crowdfunding.CredibleTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfCredibleInfoDO;
import com.shuidihuzhu.cf.model.crowdfunding.CfSendProve;
import com.shuidihuzhu.cf.model.report.schedule.ReportPendingEntryPayload;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.cf.service.report.CfReportWorkOrderService;
import com.shuidihuzhu.cf.service.report.ReportOperationService;
import com.shuidihuzhu.cf.service.stream.StreamBizService;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderStaffClient;
import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.StaffStatusEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import com.shuidihuzhu.msg.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @time 2020/1/10 下午5:37
 * @desc
 */
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.CF_UPDATE_CREDIBLE_STATUS_V2, tags = MQTagCons.CF_UPDATE_CREDIBLE_STATUS_V2, topic = MQTopicCons.CF)
public class AdminUpdateCredibleInfoV2Consumer implements MessageListener<CfSendProve> {

    @Autowired
    private IAdminCredibleInfoService credibleInfoService;

    @Autowired
    private ReportOperationService reportOperationService;

    @Autowired
    private StreamBizService streamService;

    @Autowired(required = false)
    private Producer producer;

    @Autowired
    private ApplicationService applicationService;

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @Autowired
    private CfWorkOrderStaffClient staffClient;

    @Resource
    private CfReportWorkOrderService cfReportWorkOrderService;


    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfSendProve> mqMessage) {
        CfSendProve cfSendProve = mqMessage.getPayload();
        if (Objects.isNull(cfSendProve)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        CfCredibleInfoDO cfCredibleInfoDO = credibleInfoService.queryBySubId(cfSendProve.getId(), CredibleTypeEnum.HELP_PROVE.getKey());
        if (Objects.isNull(cfCredibleInfoDO) || (cfCredibleInfoDO.getAuditStatus() != CrowdfundingInfoStatusEnum.UN_SUBMITTED.getCode()
                && cfCredibleInfoDO.getAuditStatus() != CrowdfundingInfoStatusEnum.REJECTED.getCode())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        int res = credibleInfoService.updateSubmitInfo(cfSendProve.getId(), CrowdfundingInfoStatusEnum.SUBMITTED.getCode(), CredibleTypeEnum.HELP_PROVE.getKey());
        if (res > 0) {
            cfReportWorkOrderService.createInsteadInputWorkOrder(cfCredibleInfoDO);
        }

        reportOperationService.operation(cfSendProve.getCaseId(), ReportCons.ActionType.UserSubmitGhost, 0);

        return ConsumeStatus.CONSUME_SUCCESS;
    }

}
