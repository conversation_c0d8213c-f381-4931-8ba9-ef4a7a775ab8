package com.shuidihuzhu.cf.admin.mq;

import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfRefuseReasonMsgBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoDataStatusTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonMsg;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingTreatment;
import com.shuidihuzhu.cf.service.ai.AiImageMaskServiceImpl;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.constant.CfClientMQTopicCons;
import com.shuidihuzhu.client.model.enums.ImageMaskBizEnum;
import com.shuidihuzhu.client.model.event.InfoApproveEvent;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import com.shuidihuzhu.data.analytics.javasdk.core.UserTagTypeEnum;
import com.shuidihuzhu.data.servicelog.meta.cf.MaterialAuditEvent;
import com.shuidihuzhu.data.servicelog.meta.cf.MaterialAuditPoint;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2019/9/23 下午5:09
 * @desc
 */
@Slf4j
@Service
@RefreshScope
@RocketMQListener(id = "MaterialAuditTrack_" + CfClientMQTagCons.INFO_APPROVE_MSG,
        tags = CfClientMQTagCons.INFO_APPROVE_MSG,
        topic = CfClientMQTopicCons.CF,
        group = "MaterialAuditTrack_" + CfClientMQTagCons.INFO_APPROVE_MSG)
public class MaterialAuditTrackConsumer implements MessageListener<InfoApproveEvent> {

    @Autowired
    private Analytics analytics;

    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;

    @Autowired
    private AdminCfRefuseReasonMsgBiz cfRefuseReasonMsgBiz;

    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;

    @Resource
    private AiImageMaskServiceImpl aiImageMaskService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<InfoApproveEvent> mqMessage) {

        if(Objects.isNull(mqMessage) || Objects.isNull(mqMessage.getPayload())){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        InfoApproveEvent approveEvent = mqMessage.getPayload();

        int caseId = approveEvent.getCaseId();
        List<Integer> passIds = approveEvent.getPassIds();
        List<Integer> refuseIds = approveEvent.getRefuseIds();

        // 材审通过，发送掩码通知消息
        HashSet<Integer> passIdSet = CollectionUtils.isNotEmpty(passIds) ? Sets.newHashSet(passIds) : Sets.newHashSet();
        if (passIdSet.contains(CrowdfundingInfoDataStatusTypeEnum.BASE_INFO_SUBMIT.getCode())) {
            log.info("AiImageMaskServiceImpl sendImageMaskMq {} {}", caseId, ImageMaskBizEnum.CF_DETAIL_IMAGE.getDesc());
            aiImageMaskService.sendImageMaskMq(caseId, ImageMaskBizEnum.CF_DETAIL_IMAGE.getCode());
        }

        CrowdfundingInfo cfCase = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if(Objects.isNull(cfCase)){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        if(CollectionUtils.isEmpty(passIds) && CollectionUtils.isEmpty(refuseIds)){
            return ConsumeStatus.CONSUME_SUCCESS;
        }


        /**
         * 没人定义过材料审核几项的枚举，只能给个magicNumber
         * 4:患者确诊信息 就是sea后台材料审核页面的诊断信息
         * 用于判断【患者确诊信息】是否审核通过
         */
        CfRefuseReasonMsg refuseReasonMsg = cfRefuseReasonMsgBiz.selectByInfoIdAndType(cfCase.getInfoId(), 4);

        String treatmentApprove = "true";
        //明确驳回
        if(Objects.nonNull(refuseReasonMsg)){
            treatmentApprove = "false";
        }

        //既没通过也没驳回
        if((CollectionUtils.isEmpty(passIds) || !passIds.contains(4)) && (CollectionUtils.isEmpty(refuseIds) || !refuseIds.contains(4))){
            treatmentApprove = "false";
        }

        FeignResponse<CrowdfundingTreatment> treatmentRes = crowdfundingFeignClient.getCrowdfundingTreatment(caseId);
        CrowdfundingTreatment treatment = Objects.nonNull(treatmentRes) ? treatmentRes.getData() : null;

        MaterialAuditEvent mae = new MaterialAuditEvent();
        try {
            mae.setDisease_name(Objects.nonNull(treatment) ? treatment.getDiseaseName() : "");
            mae.setIs_reject(treatmentApprove);
            mae.setCase_id(cfCase.getInfoId());
            mae.setInfo_id(Long.valueOf(caseId));
            mae.setUser_tag(String.valueOf(cfCase.getUserId()));
            mae.setUser_tag_type(UserTagTypeEnum.userid);

            analytics.track(mae);
            log.info("大数据打点上报,材料审核事件:{}", mae);
        } catch (Exception e) {
            log.error("大数据打点上报异常,材料审核事件:{}", mae, e);
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
