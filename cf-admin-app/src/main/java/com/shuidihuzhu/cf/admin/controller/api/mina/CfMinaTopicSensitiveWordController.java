package com.shuidihuzhu.cf.admin.controller.api.mina;

import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import com.aliyun.oss.model.ObjectMetadata;
import com.shuidihuzhu.cf.admin.util.CfDataUploadUtils;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.service.crowdfunding.CfMinaSensitiveWordService;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.aliyun.enums.OSSBucketEnum;
import com.shuidihuzhu.common.web.util.http.HttpResponseModel;
import com.shuidihuzhu.common.web.util.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by niejiangnan on 2018/3/7.
 */
@Slf4j
@RestController
@RequestMapping("admin/cf/mina/topic")
public class CfMinaTopicSensitiveWordController {



    //禁止词url
    private final static String URL_FORBID = "https://sdchou-static.oss-cn-beijing.aliyuncs.com/file/forbidWord.txt";
    //敏感词url
    private final static String URL_SENSITIVE = "https://sdchou-static.oss-cn-beijing.aliyuncs.com/file/sensitiveWord.txt";
    //redis保存时长
    private final static int SAVE_TIME = 30 * 60 * 1000;

    //敏感词和禁止词存入redis的Key
    private final static String FORBID_TOKEN = "minaTopicForbid321230";
    private final static String SENSTIVE_TOKEN = "minaTopicSensitive126087";

    private final static int SENSITIVE = 1;
    private final static int FORBID = 2;


    @Resource(name = "cfRedissonHandler")
    private RedissonHandler cfRedissonHandler;

    @Autowired
    private CfMinaSensitiveWordService cfMinaSensitiveWordService;


    @RequiresPermission("topic-sensitive-word:downloadFile-forbid")
    @RequestMapping(path = "/downloadFile/forbid", method = RequestMethod.POST)
    @ResponseBody
    public Response downloadFileForbid() {
        try {
            return NewResponseUtil.makeSuccess(URL_FORBID);
        } catch (Exception e) {
            log.error("", e);
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
        }
    }

    @RequiresPermission("topic-sensitive-word:downloadFile-sensitive")
    @RequestMapping(path = "/downloadFile/sensitive", method = RequestMethod.POST)
    @ResponseBody
    public Response downloadFileSensitive() {
        try {
            return NewResponseUtil.makeSuccess(URL_SENSITIVE);
        } catch (Exception e) {
            log.error("", e);
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
        }
    }

    @RequiresPermission("topic-sensitive-word:updateFile-sensitiveWord")
    @RequestMapping(path = "/updateFile/sensitiveWord", method = RequestMethod.POST)
    @ResponseBody
    public Response updateSensitiveWordFile(MultipartHttpServletRequest request,
                                            @RequestParam(defaultValue = "") String name) {
        try {
            Iterator<String> iterator = request.getFileNames();
            if (iterator.hasNext()) {
                String fileName = iterator.next();
                MultipartFile multipartFile = request.getFile(fileName);
                if (multipartFile != null) {
                    if (StringUtils.isBlank(name)) {
                        name = "sensitiveWord";
                    }
                    doUploadFile(multipartFile, name + ".txt");
                    putRedis(SENSITIVE);
                    cfMinaSensitiveWordService.reload();
                }
            }
            return getInfo(URL_SENSITIVE);
        } catch (Exception e) {
            log.error("", e);
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
        }

    }

    @RequiresPermission("topic-sensitive-word:updateFile-forbid")
    @RequestMapping(path = "/updateFile/forbid", method = RequestMethod.POST)
    @ResponseBody
    public Response updateForbidWordFile(MultipartHttpServletRequest request,
                                         @RequestParam(defaultValue = "") String name) {
        try {
            Iterator<String> iterator = request.getFileNames();
            if (iterator.hasNext()) {
                String fileName = iterator.next();
                MultipartFile multipartFile = request.getFile(fileName);
                if (multipartFile != null) {
                    if (StringUtils.isBlank(name)) {
                        name = "forbidWord";
                    }
                    doUploadFile(multipartFile, name + ".txt");
                    putRedis(FORBID);
                }
            }
            return getInfo(URL_FORBID);
        } catch (Exception e) {
            log.error("", e);
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
        }

    }


    @RequiresPermission("topic-sensitive-word:get-info")
    @RequestMapping(path = "/get-info", method = RequestMethod.POST)
    @ResponseBody
    public Response getInfo(String url) {
        try {
            int size = getWordSet(url).size();
            Map<String, Object> result = Maps.newHashMap();
            result.put("size", size);
            return NewResponseUtil.makeSuccess(result);
        } catch (Exception e) {
            log.error("", e);
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
        }
    }



    private Set<String> getWordSet(String url) {
        Set<String> wordSet = Sets.newHashSet();
        HttpResponseModel httpGet = HttpUtil.httpGet(url);
        if (httpGet.getStatusCode() == HttpStatus.SC_OK) {
            List<String> keyList = Splitter.on("\n").splitToList(httpGet.getBodyString());
            Set<String> keySet = Sets.newHashSet(keyList);
            wordSet.addAll(keySet);
        }
        return wordSet;
    }


    private void doUploadFile(MultipartFile multipartFile, String fileName) throws Exception {
        InputStream is = multipartFile.getInputStream();
        byte[] contentBytes = IOUtils.toByteArray(is);
        Long contentLength = Long.valueOf(contentBytes.length);
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentLength(contentLength);
        int pos = fileName.lastIndexOf(".");
        String fileType = null;
        if (pos >= 0) {
            fileType = fileName.substring(pos).toLowerCase().replace(".", "");
        }
        metadata.setContentType(fileType);


        // 上传阿里云
        String filePath = "file/" + fileName;
        CfDataUploadUtils.uploadImageToOSS(new ByteArrayInputStream(contentBytes), OSSBucketEnum.SDCHOU_STATIC, filePath);
    }


    private void putRedis(int fileType) {
        if (fileType == SENSITIVE) {
            log.info("put redis SENSITIVE");
            //讲敏感词写入redis
            if (cfRedissonHandler != null){
                cfRedissonHandler.setEX(SENSTIVE_TOKEN,getWordSet(URL_SENSITIVE),SAVE_TIME);
            }
        } else if (fileType == FORBID) {
            log.info("put redis FORBID");
            //讲敏感词写入redis
            if (cfRedissonHandler != null){
                cfRedissonHandler.setEX(FORBID_TOKEN,getWordSet(URL_FORBID),SAVE_TIME);
            }
        }
    }

}
