package com.shuidihuzhu.cf.admin.mq;

import com.shuidihuzhu.cf.biz.report.RepeatCaseAutoReportService;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.REPEAT_THE_CASE_AUTO_REPORT, tags = MQTagCons.REPEAT_THE_CASE_AUTO_REPORT, topic = MQTopicCons.CF)
public class RepeatTheCaseAutoReportConsumer implements MessageListener<CrowdfundingInfo> {

    @Resource
    private RepeatCaseAutoReportService repeatCaseAutoReportService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdfundingInfo> mqMessage) {
        if (Objects.isNull(mqMessage) || Objects.isNull(mqMessage.getPayload())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        CrowdfundingInfo crowdfundingInfo = mqMessage.getPayload();

        repeatCaseAutoReportService.repeatCaseAutoReport(crowdfundingInfo);

        return ConsumeStatus.CONSUME_SUCCESS;
    }

}
