package com.shuidihuzhu.cf.admin.controller.api.innerapi;

import com.shuidihuzhu.cf.service.CfScheduleAlarmService;
import com.shuidihuzhu.client.cf.admin.client.CfScheduleAlarmFeignClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/7/15
 */

@Slf4j
@RestController
public class CfScheduleAlarmFeignClientController implements CfScheduleAlarmFeignClient {

    @Resource
    private CfScheduleAlarmService cfScheduleAlarmService;

    @Override
    public Response<Void> run() {
        cfScheduleAlarmService.run(new Date());
        return NewResponseUtil.makeSuccess(null);
    }
}
