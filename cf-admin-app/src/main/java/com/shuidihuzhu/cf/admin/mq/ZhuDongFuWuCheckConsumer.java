package com.shuidihuzhu.cf.admin.mq;

import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.event.OrderZhuDongFuWuCheckPayload;
import com.shuidihuzhu.cf.service.workorder.cailiao.ZhuDongFuWuWorkOrderService;
import com.shuidihuzhu.client.constant.CfClientMQTopicCons;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Slf4j
@Service
@RefreshScope
@RocketMQListener(id = MQTagCons.CF_ZHU_DONG_FU_WU_CHECK,
        tags = MQTagCons.CF_ZHU_DONG_FU_WU_CHECK,
        topic = CfClientMQTopicCons.CF,
        group = MQTagCons.CF_ZHU_DONG_FU_WU_CHECK)
public class ZhuDongFuWuCheckConsumer implements MessageListener<OrderZhuDongFuWuCheckPayload> {

    @Autowired
    private ZhuDongFuWuWorkOrderService zhuDongFuWuWorkOrderService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<OrderZhuDongFuWuCheckPayload> mqMessage) {

        if(Objects.isNull(mqMessage) || Objects.isNull(mqMessage.getPayload())){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        OrderZhuDongFuWuCheckPayload approveEvent = mqMessage.getPayload();

        log.error("依旧收到了message CF_ZHU_DONG_FU_WU_CHECK approveEvent:{}", approveEvent);
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
