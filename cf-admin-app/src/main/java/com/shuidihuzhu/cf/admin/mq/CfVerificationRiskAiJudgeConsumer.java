package com.shuidihuzhu.cf.admin.mq;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.ai.AiRiskBizEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.deliver.CrowdFundingVerificationDeliver;
import com.shuidihuzhu.cf.service.ai.CfRiskAiJudgeService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description: 证实风险因子识别
 * @Author: panghairui
 * @Date: 2025/7/14 16:29
 */
@Slf4j
@Service
@RocketMQListener(id = "cf-ai-judge-" + MQTagCons.CF_VERIFICATION_MSG,
        group = "cf-ai-judge-group-" + MQTagCons.CF_VERIFICATION_MSG,
        tags = MQTagCons.CF_VERIFICATION_MSG,
        topic = MQTopicCons.CF)
public class CfVerificationRiskAiJudgeConsumer implements MessageListener<CrowdFundingVerificationDeliver> {

    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Resource
    private CfRiskAiJudgeService cfRiskAiJudgeService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdFundingVerificationDeliver> mqMessage) {

        if (mqMessage == null || mqMessage.getPayload() == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        log.info("CfVerificationRiskAiJudgeConsumer receive message: {}", mqMessage.getPayload());
        CrowdFundingVerificationDeliver crowdFundingVerificationDeliver = mqMessage.getPayload();

        CrowdfundingInfo crowdfundingInfo = getCrowdfundingInfo(crowdFundingVerificationDeliver.getCrowdFundingInfoId());
        if (crowdfundingInfo == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        cfRiskAiJudgeService.aiJudgeRisk(crowdfundingInfo, parseContent(crowdFundingVerificationDeliver), AiRiskBizEnum.CF_VERIFY);

        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private String parseContent(CrowdFundingVerificationDeliver crowdFundingVerificationDeliver) {
        return new StringBuilder()
                .append("证实内容：").append(StringUtils.defaultString(crowdFundingVerificationDeliver.getDescription())).append("\n")
                .toString();
    }

    /**
     * 获取众筹信息
     */
    private CrowdfundingInfo getCrowdfundingInfo(String infoUuid) {
        CrowdfundingInfo info = adminCrowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (info == null) {
            log.info("众筹信息不存在，infoUuid: {}", infoUuid);
            return null;
        }
        return info;
    }

}
