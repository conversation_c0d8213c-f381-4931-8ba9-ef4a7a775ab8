package com.shuidihuzhu.cf.admin.controller.api.innerapi.webflux;

import com.shuidihuzhu.cf.service.ai.AiGenerateServiceImpl;
import com.shuidihuzhu.client.cf.admin.model.AIGenerateParam;
import com.shuidihuzhu.client.model.ChatChunk;
import com.shuidihuzhu.client.model.ChatParam;
import com.shuidihuzhu.client.model.ChatStreamResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2025/4/29 17:33
 */
@RestController
@RequestMapping(path = "/admin/smart-assistant/inner/ai-generate")
@Api(tags = "ai生成内容inner")
@Slf4j
public class AiGenerateInnerController {

    @Resource
    private AiGenerateServiceImpl aiGenerateService;

    @ApiOperation(value = "流式生成文章")
    @PostMapping(path = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ChatChunk<ChatStreamResult>> reactive(@RequestBody ChatParam chatParam) {
        return aiGenerateService.stream(chatParam);
    }
}
