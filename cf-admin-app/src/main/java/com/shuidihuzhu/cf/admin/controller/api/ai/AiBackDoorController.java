package com.shuidihuzhu.cf.admin.controller.api.ai;

import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.model.ai.AiRiskConfigResult;
import com.shuidihuzhu.cf.service.ai.CfRiskAiJudgeService;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.cf.admin.model.AIGenerateParam;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;

/**
 * @Description: ai后门接口
 * @Author: pangh<PERSON><PERSON>
 * @Date: 2025/7/1 16:14
 */
@Slf4j
@RestController
@RequestMapping("/admin/cf/ai/backdoor")
public class AiBackDoorController {

    @Resource
    private CfRiskAiJudgeService cfRiskAiJudgeService;

    @ApiOperation("AI风控操作配置")
    @PostMapping("ai-risk-action-config")
    public Response<AiRiskConfigResult> actionAiRisk(@RequestParam("actionType") String actionType,
                                                     @RequestParam("sceneType") String sceneType,
                                                     @RequestParam(value = "riskFactor", required = false, defaultValue = "") String riskFactor,
                                                     @RequestParam(value = "judgePrompt", required = false, defaultValue = "") String judgePrompt) {
        return NewResponseUtil.makeSuccess(cfRiskAiJudgeService.actionAiRisk(actionType, sceneType, riskFactor, judgePrompt));
    }

}
