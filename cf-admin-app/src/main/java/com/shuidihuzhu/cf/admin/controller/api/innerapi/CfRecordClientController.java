package com.shuidihuzhu.cf.admin.controller.api.innerapi;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.admin.controller.api.crowdfunding.CrowdfundingApproveController;
import com.shuidihuzhu.cf.admin.controller.api.workorder.CailiaoWorkOrderController;
import com.shuidihuzhu.cf.admin.util.AdminListUtil;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfQuestionnaireBiz;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationRecordDTO;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.admin.workorder.imageContent.CPublishImageContent;
import com.shuidihuzhu.cf.model.crowdfunding.CfMaterialVerityHistory;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cf.service.approve.ApproveSnapshotService;
import com.shuidihuzhu.cf.service.approve.lifecircle.CaseApproveLifeCircleService;
import com.shuidihuzhu.cf.service.cfOperatingProfile.CfOperatingCaseLabelBiz;
import com.shuidihuzhu.cf.service.workorder.promoteBill.CfPromoteBillService;
import com.shuidihuzhu.cf.vo.approve.ApproveLifeCircleVO;
import com.shuidihuzhu.client.cf.admin.model.CfCaseApproveDetail;
import com.shuidihuzhu.client.model.CfPromoteBillConditionVO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE 2019/12/13
 */
@RestController
@RequestMapping(path="innerapi/cf/admin/record")
@Slf4j
public class CfRecordClientController {

    @Autowired
    private CfQuestionnaireBiz biz;

    @Autowired
    private CaseApproveLifeCircleService caseApproveLifeCircleService;

    @Autowired
    private ApproveSnapshotService approveSnapshotService;

    @Autowired
    private CfOperatingCaseLabelBiz caseLabelBiz;
    @Autowired
    private CfPromoteBillService promoteBillService;

    @RequestMapping(path = "saveCaseId", method = RequestMethod.POST)
    public Response<Integer> saveCaseId(long recordId, int caseId) {
        log.info("saveCaseId recordId={} caseId={}",recordId,caseId);
        int a = biz.saveCaseId(recordId,caseId);
        return NewResponseUtil.makeSuccess(a);
    }

    /**
     * 获取案例审核生命周期
     * @param caseId
     * @return   List<String>  String 为 cf-admin-model中的 ApproveLifeCircleVO json
     */
    @PostMapping("getCaseLifeCircle")
    Response<List<String>> getCaseLifeCircle(@RequestParam("caseId") int caseId){
        OpResult<List<ApproveLifeCircleVO>> opResult = caseApproveLifeCircleService.getLifeCircle(caseId);
        if (opResult.isSuccessWithNonNullData()){
            return NewResponseUtil.makeSuccess(AdminListUtil.getListStringFromListModel(opResult.getData()));
        }
        return NewResponseUtil.makeSuccess(AdminListUtil.getListStringFromListModel(Lists.newArrayList()));
    }

    @PostMapping("getCaseApproveDetail")
    public Response<Map<Integer, CfCaseApproveDetail>> getCaseApproveDetail(@RequestParam("caseIds") List<Integer> caseIds) {
        return NewResponseUtil.makeSuccess(caseApproveLifeCircleService.getCaseApproveDetail(caseIds));
    }

    @PostMapping("material/audit/selectHandleSnapshot")
    public Response<String> selectHandleSnapshot(@RequestParam("caseId") int caseId,
                                                 @RequestParam("workOrderId") long workOrderId) {

        return approveSnapshotService.selectHandleSnapshot(caseId, workOrderId);
    }

    @PostMapping("case/label/selectBillCondition")
    public Response<CfPromoteBillConditionVO> selectBillCondition(@RequestParam("caseId") int caseId,
                                                                  @RequestParam("content") String content) {

        CfPromoteBillConditionVO caseLabelVO = new CfPromoteBillConditionVO();
        caseLabelVO.setMatchLabel(caseLabelBiz.selectHasLabelContent(caseId, content));
        caseLabelVO.setHasSubmitFundUse(promoteBillService.getFirstUploadDate(caseId) != null);

        return NewResponseUtil.makeSuccess(caseLabelVO);

    }

}
