package com.shuidihuzhu.cf.admin.mq;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderFlowBiz;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.client.cf.admin.model.WorkOrderFlowParam;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RocketMQListener(id = MQTagCons.ADMIN_AUTO_GENERATE_FLOW_ORDER,
        group = "cf-admin"+MQTagCons.ADMIN_AUTO_GENERATE_FLOW_ORDER,
        tags = MQTagCons.ADMIN_AUTO_GENERATE_FLOW_ORDER,
        topic = MQTopicCons.CF)
public class AdminFlowOrderGenerateConsumerV1
        implements MessageListener<WorkOrderFlowParam> {

    @Autowired
    private AdminWorkOrderFlowBiz workOrderFlowBiz;
    @Autowired
    private ApplicationService applicationService;
    @Autowired(required = false)
    private Producer producer;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<WorkOrderFlowParam> mqMessage) {

        log.info("自动创建信息传递工单 message:{} body：{}", mqMessage, mqMessage == null ? null : mqMessage.getPayload());
        if (mqMessage == null || mqMessage.getPayload() == null) {
            log.error("自动创建信息传递工单 收到的消息为空. message:{}", mqMessage);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        WorkOrderFlowParam orderFlow = mqMessage.getPayload();

        AdminWorkOrderFlowView vo = workOrderFlowBiz.buildFlowViewFromWorkOrder(orderFlow);
        if (vo == null) {
            log.info("自动创建信息传递工单 参数错误. message:{}", mqMessage);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        if (!canCreateNewFlowOrder(orderFlow)) {
            log.info("自动创建信息传递工单, 不生成新的工单。 message:{}", mqMessage);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        Response result = workOrderFlowBiz.createWorkOrderFlow(vo);
        handleFlowWorkOrder(orderFlow, result);

        return ConsumeStatus.CONSUME_SUCCESS;
    }

    //  当用户访问老接口发起案例被拒绝时，需要自动生成一条信息传递工单 同一个手机号，一天内多次点击调用老接口，只创建一条信息传递工单
    private boolean canCreateNewFlowOrder(WorkOrderFlowParam orderFlow) {
        if (orderFlow.getTaskType() != AdminWorkOrderConst.TaskType.VISIT_OLD_RAISE_CASE_RECALL.getCode()) {
            return true;
        }

        return CollectionUtils.isEmpty(workOrderFlowBiz.selectByMobileAndTaskType(orderFlow.getMobile(), orderFlow.getTaskType()));
    }

    private void handleFlowWorkOrder(WorkOrderFlowParam orderFlow, Response result) {
        handleSingleRefund(orderFlow, result);
    }

    // 单笔退款的处理
    private void handleSingleRefund(WorkOrderFlowParam orderFlow, Response result) {

        if (result.getData() != null &&
                orderFlow.getTaskType() == AdminWorkOrderConst.TaskType.SINGLE_REFUND_DELAY_48H_NOTICE.getCode()) {
            DateTime now = new DateTime();
            long dispatchTime = now.plusDays(2).getMillis();

            if (!applicationService.isProduction()) {
                dispatchTime = now.plusMinutes(2).getMillis();
            }

            Message message = Message.ofSchedule(MQTopicCons.CF, MQTagCons.ADMIN_FLOW_ORDER_SINGLE_REFUND_DELAY_48H,
                    "" + System.nanoTime(), result.getData(), dispatchTime / 1000);
            MessageResult messageResult = producer.send(message);
            log.info("单笔退款48小时消息发送. msg：{}, result:{}", message, messageResult);
        }

    }


}
