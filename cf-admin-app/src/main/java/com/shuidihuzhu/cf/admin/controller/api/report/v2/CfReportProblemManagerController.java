package com.shuidihuzhu.cf.admin.controller.api.report.v2;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.enums.crowdfunding.CfReportPageEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportModuleOperationLog;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportProblem;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportProblemOperationLog;
import com.shuidihuzhu.cf.model.crowdfunding.vo.*;
import com.shuidihuzhu.cf.service.crowdfunding.report.CfReportProblemManagerService;
import com.shuidihuzhu.cf.vo.crowdfunding.AdminCfReportProblemClassifyVo;
import com.shuidihuzhu.cf.vo.crowdfunding.AdminCfReportProblemClassifyAndModuleVo;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @author: fengxuan
 * @create 2019-12-10 19:32
 **/
@Api("问题配置管理")
@Slf4j
@RestController
@RequestMapping(path = "/admin/cf/report/v2/problem/manager")
public class CfReportProblemManagerController {

    @Autowired
    CfReportProblemManagerService managerService;

    @ApiOperation("创建问题")
    @RequestMapping(path = "create-problem", method = RequestMethod.POST)
    @RequiresPermission("problem-manager:create-problem")
    public Response<Boolean> createProblem(@RequestParam("param") String param) {
        CfReportProblemParam problemParam = JSON.parseObject(param, CfReportProblemParam.class);//已检查过
        if (illegalCreateParam(problemParam)) {
            log.warn("创建问题参数错误,param:{}", param);
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (!CfReportPageEnum.REPORT_PAGE_LIST.contains(problemParam.getShowLocation())
                || !CfReportPageEnum.REPORT_PAGE_LIST.contains(problemParam.getMustUse())){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        return managerService.createProblem(problemParam);
    }


    @ApiOperation("重新编辑问题")
    @RequestMapping(path = "update-problem", method = RequestMethod.POST)
    @RequiresPermission("problem-manager:update-problem")
    public Response<Boolean> updateProblem(@RequestParam("param") String param) {
        CfReportProblemParam problemParam = JSON.parseObject(param, CfReportProblemParam.class);//已检查过
        if (illegalCreateParam(problemParam) || problemParam.getId() <= 0) {
            log.warn("修改问题参数错误,param:{}", param);
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (!CfReportPageEnum.REPORT_PAGE_LIST.contains(problemParam.getShowLocation())
                || !CfReportPageEnum.REPORT_PAGE_LIST.contains(problemParam.getMustUse())){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        return managerService.updateProblem(ContextUtil.getAdminUserId(), problemParam);
    }



    @ApiOperation("问题列表")
    @RequestMapping(path = "list-problem", method = RequestMethod.POST)
    @RequiresPermission("problem-manager:list-problem")
    public Response<CfReportProblemPageResult> listProblem(@RequestParam("problemDesc") String problemDesc,
                                                           @RequestParam("pageSize") int pageSize,
                                                           @RequestParam("current") int current,
                                                           @RequestParam(value = "id", required = false)Integer id,
                                                           @RequestParam(value = "isUse", required = false)Integer isUse,
                                                           @RequestParam(value = "collation", required = false) Integer collation) {
        return managerService.listProblem(problemDesc, current, pageSize, id, isUse, collation);
    }


    @ApiOperation("点查问题")
    @RequestMapping(path = "get-problem", method = RequestMethod.POST)
    @RequiresPermission("problem-manager:get-problem")
    public Response<CfReportProblemVo> getByProblemId(@RequestParam("problemId") int problemId) {
        if (problemId <= 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        return managerService.getByProblemId(problemId);
    }


    @ApiOperation("获取同标签下其他问题信息")
    @RequestMapping(path = "list-same-label-problem", method = RequestMethod.POST)
    @RequiresPermission("problem-manager:list-same-label-problem")
    public Response<List<CfReportProblemVo>> listSameLabelProblem(@RequestParam("problemId") int problemId, @RequestParam("labelId") int labelId) {
        if (problemId <= 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        return managerService.listSameLabelProblem(problemId, labelId);
    }


    @ApiOperation("绑定问题")
    @RequestMapping(path = "bind-problem", method = RequestMethod.POST)
    @RequiresPermission("problem-manager:bind-problem")
    public Response<Boolean> bindProblem(@RequestParam("param") String param) {
        List<CfReportProblemParam.BindParam> bindParams = JSON.parseArray(param, CfReportProblemParam.BindParam.class);//已检查过
        log.info("bindParams:{}", JSON.toJSONString(bindParams));
        if (CollectionUtils.isEmpty(bindParams)) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        //如果存在非文本类型问题且选项为空的绑定，直接返回错误
//        boolean presentErrorChoice = bindParams.stream()
//                .anyMatch(item -> !CfReportProblem.noChoiceProblemType(item.getProblemType())
//                    && StringUtils.isBlank(item.getChoice()));
//        if (presentErrorChoice) {
//            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
//        }
        return managerService.bindProblem(bindParams);
    }

    @ApiOperation("创建模块")
    @RequestMapping(path = "create-module", method = RequestMethod.POST)
    @RequiresPermission("problem-manager:create-module")
    public Response<Boolean> createModule(@RequestParam("id") int id, @RequestParam("moduleName") String moduleName) {
        return NewResponseUtil.makeSuccess(managerService.createModule(id, moduleName));
    }

    @ApiOperation("更新是否启用")
    @RequestMapping(path = "update-use-status", method = RequestMethod.POST)
    @RequiresPermission("problem-manager:update-use-status")
    public Response<Boolean> updateUseStatus(@RequestParam("id") int id, @RequestParam("isUse") int isUse) {
        return NewResponseUtil.makeSuccess(managerService.updateUseStatus(id, isUse));
    }

    @ApiOperation("增加/更新分类")
    @RequestMapping(path = "classify/add-or-update", method = RequestMethod.POST)
    @RequiresPermission("problem-manager:classify/add-or-update")
    public Response<Boolean> addOrUpdateClassify(@RequestParam(value = "status")int status,
                                                 @RequestParam(value = "classifyName")String classifyName,
                                                 @RequestParam("sort")int sort,
                                                 @RequestParam(value = "id", defaultValue = "0") int id){
        return managerService.addOrUpdateClassify(status, classifyName, sort, id);
    }

    @ApiOperation("分类信息是否启用")
    @RequestMapping(path = "classify/update-use-status", method = RequestMethod.POST)
    @RequiresPermission("problem-manager:classify/update-use-status")
    public Response<Integer> updateProblemClassifyUseStatus(@RequestParam(value = "id") int id, @RequestParam("isUse") int isUse){
        return managerService.updateProblemClassifyUseStatus(id, isUse);
    }

    @ApiOperation("分类操作&模块操作日志信息")
    @RequestMapping(path = "classify/get-operation-log", method = RequestMethod.POST)
    @RequiresPermission("problem-manager:classify/get-operation-log")
    public Response<List<CfReportModuleOperationLog>> getModuleAndClassifyOperationLog(int id){
        return managerService.getModuleAndClassifyOperationLog(id);
    }

    @ApiOperation("返回全部分类信息")
    @RequestMapping(path = "classify/get-all", method = RequestMethod.POST)
    @RequiresPermission("problem-manager:classify/get-all")
    public Response<List<AdminCfReportProblemClassifyVo>> getClassifyAll(){
        return managerService.getClassifyAll();
    }



    @ApiOperation("增加/更新模块")
    @RequestMapping(path = "module/add-or-update", method = RequestMethod.POST)
    @RequiresPermission("problem-manager:module/add-or-update")
    public Response<Boolean> addOrUpdateModule(@RequestParam(value = "status")int status,
                                               @RequestParam(value = "moduleName")String moduleName,
                                               @RequestParam(value = "isMandatory", defaultValue = "0")int isMandatory,
                                               @RequestParam(value = "classifyId") int classifyId,
                                               @RequestParam(value = "sort")int sort,
                                               @RequestParam(value = "id", defaultValue = "0") int id){
        return managerService.addOrUpdateModule(status, moduleName, isMandatory, classifyId, sort, id);
    }

    @ApiOperation("模块信息是否启用")
    @RequestMapping(path = "module/update-use-status", method = RequestMethod.POST)
    @RequiresPermission("problem-manager:module/update-use-status")
    public Response<Integer> updateModuleUseStatus(@RequestParam(value = "id") int id, @RequestParam("isUse") int isUse){
        return managerService.updateModuleUseStatus(id, isUse);
    }

    @ApiOperation("返回全部分类&模块信息")
    @RequestMapping(path = "module/get-all", method = RequestMethod.POST)
    @RequiresPermission("problem-manager:module/get-all")
    public Response<List<AdminCfReportProblemClassifyAndModuleVo>> getProblemModuleAll(){
        return managerService.getProblemModuleAll();
    }

    @ApiOperation("具体问题日志信息")
    @RequestMapping(path = "problem/get-operation-log", method = RequestMethod.POST)
    @RequiresPermission("problem-manager:problem/get-operation-log")
    public Response<List<CfReportProblemOperationLog>> getProblemOperationLog(int id){
        return managerService.getProblemOperationLog(id);
    }


    private boolean illegalCreateParam(CfReportProblemParam problemParam) {
        boolean check = problemParam == null || problemParam.getLabelId() <= 0
                || StringUtils.isBlank(problemParam.getProblem());

        if (check) {
            return true;
        }
        boolean notChoiceProblemType = CfReportProblem.noChoiceProblemType(problemParam.getProblemType());
        CfReportProblem.ReportProblemType problemType = CfReportProblemParam.ReportProblemType.findByCode(problemParam.getProblemType());
        CfReportProblem.ReportAnswerType answerType = CfReportProblem.ReportAnswerType.findByCode(problemParam.getAnswerType());
        check = !notChoiceProblemType && CollectionUtils.isEmpty(problemParam.getChoiceDescribes());

        check = check || (answerType == null && notChoiceProblemType) || problemType == null;
        return check;
    }
}
