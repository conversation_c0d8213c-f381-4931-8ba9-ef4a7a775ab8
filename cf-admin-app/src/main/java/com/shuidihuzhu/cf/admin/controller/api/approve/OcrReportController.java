package com.shuidihuzhu.cf.admin.controller.api.approve;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationRecordDTO;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.delegate.SeaAccountDelegate;
import com.shuidihuzhu.cf.enums.admin.UserCommentSourceEnum;
import com.shuidihuzhu.cf.enums.approve.CaseInfoOCRLocationEnum;
import com.shuidihuzhu.cf.enums.approve.MaterialImageOCRLocationEnum;
import com.shuidihuzhu.cf.model.admin.UserComment;
import com.shuidihuzhu.cf.service.approve.CaseInteractionService;
import com.shuidihuzhu.cf.service.notice.workwx.WorkWeiXinContentBuilder;
import com.shuidihuzhu.cf.vo.approve.CaseInteractionDetailVO;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.Min;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(value = "OCR报错接口 https://wiki.shuiditech.com/pages/viewpage.action?pageId=*********")
@RestController
@RequestMapping("/admin/cf/approve/ocr")
public class OcrReportController {

    @Autowired
    private CommonOperationRecordClient commonOperationRecordClient;

    @Resource
    private AlarmClient alarmClient;

    @Autowired
    private SeaAccountDelegate organizationDelegate;

    @ApiOperation("材料审核OCR识别报错")
    @PostMapping("report-error")
    @RequiresPermission("ocr-report:report-error")
    public Response<Void> reportError(@ApiParam("工单id") @RequestParam long workOrderId,
                                      @ApiParam("案例id") @RequestParam("caseId") int caseId,
                                      @ApiParam("图片url") @RequestParam("imageUrl") String imageUrl,
                                      @ApiParam("报错位置") @RequestParam("content") CaseInfoOCRLocationEnum locationEnum){

        List<OperationRecordDTO> records = commonOperationRecordClient.listByBizIdAndActionTypes(workOrderId,
                OperationActionTypeEnum.OCR_APPROVE_CASE_INFO_REPORT_ERROR);
        boolean hasSameLocationError = records.stream()
                .map(OperationRecordDTO::getExtMap)
                .map(v -> v.get("locationName"))
                .anyMatch(l -> StringUtils.equals(l, locationEnum.name()));
        if (hasSameLocationError) {
            return NewResponseUtil.makeFail("已经报错过此位置");
        }
        int adminUserId = ContextUtil.getAdminUserId();
        commonOperationRecordClient.create()
                .buildBasicPlatform(workOrderId, adminUserId, OperationActionTypeEnum.OCR_APPROVE_CASE_INFO_REPORT_ERROR)
                .buildExt("location", locationEnum.getMsg())
                .buildExt("locationCode", String.valueOf(locationEnum.getCode()))
                .buildExt("locationName", locationEnum.name())
                .buildExt("url", imageUrl)
                .buildExt("caseId", String.valueOf(caseId))
                .save();

        String content = WorkWeiXinContentBuilder.create()
                .subject("【图片识别结果报错】")
                .payload("案例ID", caseId)
                .payload("报错来源", "材审详情页")
                .payload("工单ID", workOrderId)
                .payload("图片url", imageUrl)
                .payload("报错位置", locationEnum.getShowMsg())
                .payload("报错时间", DateUtil.getCurrentDateStr())
                .payload("报错人", organizationDelegate.getNameWithOrgByUserId(adminUserId))
                .build();

        alarmClient.sendByGroup("wx-alarm-prod-20200311-0001", content);

        return NewResponseUtil.makeSuccess(null);
    }

}
