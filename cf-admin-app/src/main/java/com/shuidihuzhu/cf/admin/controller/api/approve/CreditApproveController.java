package com.shuidihuzhu.cf.admin.controller.api.approve;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.admin.river.IRiverAssembleInterface;
import com.shuidihuzhu.cf.admin.river.RiverHelpService;
import com.shuidihuzhu.cf.admin.river.impl.RiverCreditFacadeImpl;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseReasonEntityBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseReasonTagBiz;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonTag;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cf.model.river.RiverUsageTypeEnum;
import com.shuidihuzhu.cf.vo.approve.CreditPageInfoVO;
import com.shuidihuzhu.cf.vo.approve.RiverDetailVO;
import com.shuidihuzhu.cf.vo.approve.RiverHandleParamVO;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("admin/cf/approve/credit")
public class CreditApproveController {

    @Autowired
    private RiverCreditFacadeImpl riverCreditAdapter;

    @Autowired
    private CfWorkOrderClient workOrderClient;


    @PostMapping("handle")
    @RequiresPermission("approve-credit:handle")
    public Response<Void> handle(@RequestBody RiverHandleParamVO param){
        param.setOperatorId(ContextUtil.getAdminUserId());
        return riverCreditAdapter.handle(param);
    }

    @PostMapping("detail")
    @RequiresPermission("approve-credit:detail")
    public Response<RiverDetailVO<CreditPageInfoVO>> detail(@RequestParam int caseId, @RequestParam long workOrderId){
        RiverDetailVO<CreditPageInfoVO> detail = riverCreditAdapter.getDetail(caseId, workOrderId);
        return NewResponseUtil.makeSuccess(detail);
    }

    @PostMapping("check")
    @RequiresPermission("approve-credit:check")
    public Response<Boolean> check(long workOrderId) {
        Response<WorkOrderVO> workOrderVOResponse = workOrderClient.getWorkOrderById(workOrderId);
        if (workOrderVOResponse.ok()) {
            if (workOrderVOResponse.getData().getHandleResult() == HandleResultEnum.manual_lock.getType()) {
                return NewResponseUtil.makeSuccess(true);
            }
        }
        return NewResponseUtil.makeSuccess(false);
    }

    @ApiOperation("获取驳回项")
    @PostMapping("get-refuse-options")
    @RequiresPermission("approve-credit:get-refuse-options")
    public Response<List<CfRefuseReasonTag>> getRefuseOptions(){
        return NewResponseUtil.makeSuccess(riverCreditAdapter.getRefuseReasonTags());
    }
}
