package com.shuidihuzhu.cf.admin.mq.approve;

import com.shuidihuzhu.cf.biz.admin.common.OperationHistorySummaryBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfAdminOperationRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfMaterialVerityHistoryBiz;
import com.shuidihuzhu.cf.client.ugc.caseprocessstatus.model.CaseProcessStatusEnum;
import com.shuidihuzhu.cf.client.ugc.caseprocessstatus.service.CaseProcessStatusClient;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.enums.BackgroundLogEnum;
import com.shuidihuzhu.cf.enums.admin.common.OperationType;
import com.shuidihuzhu.cf.enums.crowdfunding.CfOperatingRecordEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfOperationRecordEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingOperationEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.model.crowdfunding.CfMaterialVerityHistory;
import com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cf.service.admin.CfHospitalAuditService;
import com.shuidihuzhu.cf.service.crowdfunding.CfAdminDrawCashApproveService;
import com.shuidihuzhu.cf.vo.approve.ApproveAuditMsgV2;
import com.shuidihuzhu.cf.vo.crowdfunding.AdminApproveVo;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Author: wangpeng
 * @Date: 2023/3/27 15:28
 * @Description:
 */
@Slf4j
@Service
@RefreshScope
@RocketMQListener(id = "case-info-approve_" + MQTagCons.INFO_APPROVE_MSG_V2,
        tags = MQTagCons.INFO_APPROVE_MSG_V2,
        topic = MQTopicCons.CF,
        group = "case-info-approve_" + MQTagCons.INFO_APPROVE_MSG_V2)
public class ApproveAuditMsgV2Consumer implements MessageListener<ApproveAuditMsgV2> {

    @Resource
    private CommonOperationRecordClient commonOperationRecordClient;
    @Resource
    private OperationHistorySummaryBiz operationHistorySummaryBiz;
    @Resource
    private CfHospitalAuditService cfHospitalAuditService;
    @Resource
    private CfMaterialVerityHistoryBiz verityHistoryBiz;
    @Resource
    private AdminApproveService adminApproveService;
    @Resource
    private CfAdminOperationRecordBiz cfAdminOperationRecordBiz;
    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;
    @Resource
    private CaseProcessStatusClient caseProcessStatusClient;
    @Resource
    private CfAdminDrawCashApproveService cfAdminDrawCashApproveService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<ApproveAuditMsgV2> mqMessage) {
        log.info("ApproveAuditMsgV2Consumer is success: {}", mqMessage);

        if(Objects.isNull(mqMessage) || Objects.isNull(mqMessage.getPayload())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        ApproveAuditMsgV2 payload = mqMessage.getPayload();
        if (Objects.isNull(payload.getAdminApproveVo())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        CrowdfundingInfo crowdfundingInfo = payload.getBeforeCrowdfundingInfo();
        if(Objects.isNull(crowdfundingInfo)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        int caseId = payload.getCaseId();
        AdminApproveVo adminApproveVo = payload.getAdminApproveVo();
        int status = payload.getStatus();
        int userId = payload.getUserId();
        long hospitalAuditWorkOrderId = payload.getHospitalAuditWorkOrderId();
        List<Integer> passIds = adminApproveVo.getPassIds();
        List<Integer> refuseIds = adminApproveVo.getRefuseIds();
        long yanhouWorkOrderId = adminApproveVo.getYanhouWorkOrderId();
        String commentText = payload.getCommentText();
        String infoId = crowdfundingInfo.getInfoId();
        CfOperatingRecord cfOperatingRecord = payload.getCfOperatingRecord();


        try {
            if (status == CrowdfundingStatus.APPROVE_DENIED.value()) {
                commonOperationRecordClient.create()
                        .buildBasicPlatform(caseId, userId, OperationActionTypeEnum.CASE_INFO_REFUSE_RECORD_EVERY_TIME)
                        .save();
            }
            operationHistorySummaryBiz.addOperationHistorySummary(OperationType.CROWDFUNDING_CASE_JUDGE, userId, "筹款审批, infoId=" + infoId);

        } catch (Exception e) {
            log.info("ApproveAuditMsgV2Consumer save record is error {} {}", payload, e);
        }

        try {
            cfHospitalAuditService.onCaseInfoApprove(caseId, hospitalAuditWorkOrderId, userId);
        } catch (Exception e) {
            log.info("ApproveAuditMsgV2Consumer onCaseInfoApprove is error {} {}", payload, e);
        }

        try {
            // 工单操作历史的快照
            verityHistoryBiz.recordVerityHistory(new CfMaterialVerityHistory.CfMaterialVerityHistoryRecord()
                    .buildCaseId(caseId).buildInfoId(infoId).buildPassIds(passIds)
                    .buildRejectIds(refuseIds).buildComment(commentText).buildUserId(userId)
                    .buildSuggestModifyDetails(adminApproveVo.getSuggestModifyDetails()), yanhouWorkOrderId, adminApproveVo.getControlRecordId());
        } catch (Exception e) {
            log.info("ApproveAuditMsgV2Consumer recordVerityHistory is error {} {}", payload, e);
        }

        try {
            crowdfundingDelegate.afterCfOperatingRecord(cfOperatingRecord);
            adminApproveService.updateOperationStatus(infoId, CrowdfundingOperationEnum.OPERATED, BackgroundLogEnum.CASE_APPROVE.getMessage(), userId);
            statusNoEqual(payload);
        } catch (Exception e) {
            log.info("ApproveAuditMsgV2Consumer recordVerityHistory is error {} {}", payload, e);
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private void statusNoEqual(ApproveAuditMsgV2 payload) {
        CrowdfundingInfo crowdfundingInfo = payload.getBeforeCrowdfundingInfo();
        String refuseComment = payload.getRefuseComment();
        String userName = payload.getUserName();
        int userId = payload.getUserId();
        int caseOldStatus = crowdfundingInfo.getStatus().value();
        String infoId = crowdfundingInfo.getInfoId();
        int caseId = crowdfundingInfo.getId();
        int status = payload.getStatus();
        if (caseOldStatus == status) {
            return;
        }
        if (status == CrowdfundingStatus.APPROVE_DENIED.value()) {
            cfAdminOperationRecordBiz.addOneOperationRecord(infoId, userId, CfOperationRecordEnum.RETURN_TO_MODIFY.value(), refuseComment);
            crowdfundingDelegate.before(crowdfundingInfo, userId, userName, CfOperatingRecordEnum.Type.AUDIT_FAILED, CfOperatingRecordEnum.Role.OPERATOR);
            commonOperationRecordClient.create().buildBasicPlatform(caseId, userId, OperationActionTypeEnum.REFUSE_INFO_APPROVE).save();
            caseProcessStatusClient.update(caseId, CaseProcessStatusEnum.CAN_SHARE);
        }
        if (status == CrowdfundingStatus.CROWDFUNDING_STATED.value()) {
            cfAdminOperationRecordBiz.addOneOperationRecord(infoId, userId, CfOperationRecordEnum.PASS.value(), refuseComment);
            crowdfundingDelegate.before(crowdfundingInfo, userId, userName, CfOperatingRecordEnum.Type.AUDIT_SUCCESS, CfOperatingRecordEnum.Role.OPERATOR);
            cfAdminDrawCashApproveService.publishCfAuditPassToDonor(crowdfundingInfo);
            caseProcessStatusClient.update(caseId, CaseProcessStatusEnum.CAN_DRAW_CASH);
        }
    }
}
