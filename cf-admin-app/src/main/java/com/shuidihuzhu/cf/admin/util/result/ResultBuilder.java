package com.shuidihuzhu.cf.admin.util.result;

import com.google.common.collect.Maps;

import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018-07-26  18:27
 */
public class ResultBuilder {

    private Map<String, Object> resultMap = Maps.newHashMap();

    public static ResultBuilder create(){
        return new ResultBuilder();
    }

    private ResultBuilder() {
    }

    public ResultBuilder put(String key, Object value){
        resultMap.put(key,value);
        return this;
    }

    public Map<String, Object> getResultMap() {
        return resultMap;
    }

    public Response buildSuccessResponse(){
        return NewResponseUtil.makeSuccess(resultMap);
    }
}
