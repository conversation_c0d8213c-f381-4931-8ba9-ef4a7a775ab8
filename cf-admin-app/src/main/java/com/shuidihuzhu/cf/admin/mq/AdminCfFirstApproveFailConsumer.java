package com.shuidihuzhu.cf.admin.mq;


import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.admin.UserOperationStatConstant;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.crowdfunding.CfSensitiveWordService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @DATE 2018/8/9
 *
 * 接收前置审核重填消息 重新插入工单
 */
@Service
@RocketMQListener(id = MQTagCons.CF_FIRST_APPROVE_RETRY,
        group = "cf-admin"+MQTagCons.CF_FIRST_APPROVE_RETRY,
        tags = MQTagCons.CF_FIRST_APPROVE_RETRY,
        topic = MQTopicCons.CF)
@Slf4j
public class AdminCfFirstApproveFailConsumer implements MessageListener<CrowdfundingInfo> {

    @Resource
    private CfSensitiveWordService cfSensitiveWordService;

    @Resource
    private MeterRegistry meterRegistry;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdfundingInfo> consumerMessage) {

        if(consumerMessage == null || consumerMessage.getPayload() == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        log.info("receive first approve retry message: caseId={}",consumerMessage.getPayload().getInfoId());

        try {

            //上报一次打点信息
            meterRegistry.counter(UserOperationStatConstant.USER_OPERATING_STAT,
                    UserOperationStatConstant.OPERATION, UserOperationStatConstant.FIRST_APPROVE).increment();
            cfSensitiveWordService.addFirstApprove2Work(consumerMessage.getPayload().getInfoId());
        }catch (Exception e){
            log.error("AdminCfFirstApproveFailConsumer error",e);
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
