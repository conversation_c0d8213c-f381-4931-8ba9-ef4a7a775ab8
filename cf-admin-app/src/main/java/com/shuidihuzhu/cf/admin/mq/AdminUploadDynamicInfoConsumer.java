package com.shuidihuzhu.cf.admin.mq;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminPushDynamicMsgService;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.model.admin.vo.CfDynamicMsgVo;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @package: com.shuidihuzhu.cf.admin.mq
 * @Author: liujiawei
 * @Date: 2020-01-11  17:41
 * 疑似已废弃 无回调
 */
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.CF_UPLOADING_DYNAMIC_INFO_MSG,
        group = "cf-admin-" + MQTagCons.CF_UPLOADING_DYNAMIC_INFO_MSG + "-group",
        tags = MQTagCons.CF_UPLOADING_DYNAMIC_INFO_MSG,
        topic = MQTopicCons.CF)
public class AdminUploadDynamicInfoConsumer implements MessageListener<CfDynamicMsgVo> {
    @Autowired
    private AdminPushDynamicMsgService adminPushDynamicMsgService;
    @Resource(name = "cf2RedissonHandler")
    private RedissonHandler redissonHandler;


    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfDynamicMsgVo> mqMessage) {

        CfDynamicMsgVo cfDynamicMsgVo = mqMessage.getPayload();

        log.info("【下发动态审核上传】接收到mq消息:{}", cfDynamicMsgVo);

        String key = "cf_supply_progress_create_work_" + cfDynamicMsgVo.getSupplyActionId();
        String tryLock = null;
        try {
            tryLock = redissonHandler.tryLock(key, 0, 10 * 1000L);
        } catch (Exception e) {
            log.error("加锁异常", e);
        }
        //短时间(ms)内,同一条消息重复消费
        if (StringUtils.isBlank(tryLock)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        try {
            adminPushDynamicMsgService.executeUploadDynamicInfo(cfDynamicMsgVo);
        } catch (Exception e) {
            log.error("【下发动态审核上传】异常,{},", cfDynamicMsgVo, e);
            return ConsumeStatus.RECONSUME_LATER;
        } finally {
            try {
                if (StringUtils.isNotBlank(tryLock)) {
                    redissonHandler.unLock(key, tryLock);
                }
            } catch (Exception e) {
                log.info("释放锁异常", e);
            }
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
