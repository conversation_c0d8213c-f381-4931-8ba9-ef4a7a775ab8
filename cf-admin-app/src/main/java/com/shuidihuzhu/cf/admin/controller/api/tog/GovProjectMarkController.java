package com.shuidihuzhu.cf.admin.controller.api.tog;

import com.shuidihuzhu.cf.model.tog.GovMarkInfoVO;
import com.shuidihuzhu.cf.model.tog.GuangzhouMarkShowVO;
import com.shuidihuzhu.cf.service.tog.GovMarkService;
import com.shuidihuzhu.client.cf.api.model.enums.GovProjectEnum;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @Description: 政府项目信息
 * @Author: panghairui
 * @Date: 2025/3/31 15:11
 */
@Slf4j
@RestController
@RequestMapping("admin/cf/gov-project-mark")
public class GovProjectMarkController {

    @Resource
    private GovMarkService govMarkService;

    @PostMapping("query-gov-mark-info")
    @ApiOperation("查询该案例所有政府信息")
    public Response<List<GovMarkInfoVO>> queryGovMarkInfo(int caseId) {
        if(caseId <= 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        return NewResponseUtil.makeSuccess(govMarkService.listAllGovInfoByCaseId(caseId));
    }

    @PostMapping("query-gov-enum")
    @ApiOperation("获取政府枚举")
    public Response<Map<Integer, String>> queryGovEnum() {
        return NewResponseUtil.makeSuccess(GovProjectEnum.getAllProjectsMap());
    }

}
