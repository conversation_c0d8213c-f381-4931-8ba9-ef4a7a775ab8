package com.shuidihuzhu.cf.admin.controller.api.mask;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.mask.CfAdminMaskService;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCrowdfundingInfoSlaveDao;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.admin.AdminMaskParam;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.auth.saas.annotation.OperationLog;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 做什么？：给已掩码的 身份证号 手机号 银行卡号 做查看处理
 * 为什么做？：让sea后台运营人员可以查看对应的信息进行工作
 * 怎么做？：根据传入的参数重新查询对应的信息
 * @Author: panghairui
 * @Date: 2022/7/5 7:45 下午
 */
@Slf4j
@RestController
@RequestMapping("/admin/cf/sea/mask")
public class CfAdminMaskController {

    @Resource
    private CfAdminMaskService cfAdminMaskService;

    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @ApiOperation("查看掩码信息")
    @PostMapping("view-mask-info")
    @OperationLog(name = "查看掩码信息")
    public Response<List<AdminMaskParam>> getMaskInfo(@RequestBody List<AdminMaskParam> maskParam) {

        log.info("CfAdminMaskController getMaskInfo maskParam:{}", maskParam);

        int adminUserId = ContextUtil.getAdminUserId();
        if (adminUserId <= 0) {
            return NewResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        // 拼装参数
        List<AdminMaskParam> params = new ArrayList<>();
        maskParam.forEach(param -> {
            AdminMaskParam curParam = buildMaskParam(param.getId(), param.getCaseId(), param.getMaskCodeType());
            if (Objects.nonNull(curParam)) {
                params.add(curParam);
            }
        });
        if (CollectionUtils.isEmpty(params)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        // 查看掩码字段
        List<AdminMaskParam> resultMask = cfAdminMaskService.viewMaskInfo(params);
        if (CollectionUtils.isEmpty(resultMask)) {
            NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        return NewResponseUtil.makeSuccess(resultMask);


    }

    private AdminMaskParam buildMaskParam(Long id, Integer caseId, Integer maskCodeType) {

        CrowdfundingInfo crowdfundingInfo = new CrowdfundingInfo();
        if (caseId != null) {
            crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
            if (Objects.isNull(crowdfundingInfo)) {
                return null;
            }
        }

        return AdminMaskParam.builder()
                .id(id)
                .caseId(caseId)
                .userId(caseId != null ? crowdfundingInfo.getUserId() : null)
                .infoId(caseId != null ? crowdfundingInfo.getInfoId() : null)
                .maskCodeType(maskCodeType)
                .build();
    }


}
