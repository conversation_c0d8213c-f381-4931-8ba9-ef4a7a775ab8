package com.shuidihuzhu.cf.admin.mq;

import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.finance.enums.BroadcastMsgEnum;
import com.shuidihuzhu.cf.finance.model.CfFinanceBroadcastMsg;
import com.shuidihuzhu.cf.finance.mq.FinanceMQTagCons;
import com.shuidihuzhu.client.cf.workorder.CfCailiaoWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.CailiaoHandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @DATE 2020/11/9
 */
@Slf4j
@Service
@RocketMQListener(id = FinanceMQTagCons.CF_FINANCE_BROADCAST_MSG,
        tags = FinanceMQTagCons.CF_FINANCE_BROADCAST_MSG,
        group = FinanceMQTagCons.CF_FINANCE_BROADCAST_MSG+"_admin_group",
        topic = MQTopicCons.CF)
public class FinanceBroadcastComsumer  implements MessageListener<CfFinanceBroadcastMsg> {

    @Autowired
    private CfWorkOrderClient workOrderClient;

    @Autowired
    private CfCailiaoWorkOrderClient cfCailiaoWorkOrderClient;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfFinanceBroadcastMsg> mqMessage) {

        log.info("FinanceBroadcastComsumer mqMessage={}",mqMessage);

        CfFinanceBroadcastMsg msg = mqMessage.getPayload();

        if (msg.getBizType() != BroadcastMsgEnum.BizType.REFUND_APPLY){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        int caseId = msg.getCaseId();


        Response<WorkOrderVO> response =  workOrderClient.getLastWorkOrder(caseId, WorkOrderType.cailiao_fuwu.getType());

        WorkOrderVO vo = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(null);

        if (vo != null && (vo.getHandleResult() == HandleResultEnum.undoing.getType() || vo.getHandleResult() == HandleResultEnum.doing.getType())){
            CailiaoHandleOrderParam param = new CailiaoHandleOrderParam();
            param.setWorkOrderId(vo.getWorkOrderId());
            param.setOrderType(vo.getOrderType());
            param.setHandleResult(HandleResultEnum.exception_done.getType());
            param.setOperComment("用户申请全部退款");
            Response r = cfCailiaoWorkOrderClient.handleCailiao(param);
            log.info("handleCailiao param={} r={}",param,r);
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
