package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingDetailSendMsgTemplateBiz;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.MobileUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 在线招募短信模板管理
 * <AUTHOR>
 * @date 20210127
 */
@Slf4j
@Controller
@RequestMapping(path = "/admin/crowdfunding/message/online-recruit")
@RestController
public class CrowdfundingOnlineRecruitSendMsgTemplateController {

    /**
     * 自定义消息model_num
     */
    private static final String CUSTOM_MODEL_NUM = "KQY6406";
    public static final int SMS_MAX_SIZE = 249;

    @Resource(name = "adminCrowdfundingOnlineRecruitSendMsgTemplateBizImpl")
    private AdminCrowdfundingDetailSendMsgTemplateBiz crowdfundingDetailSendMsgTemplateBiz;
    @Resource
    private AdminApproveService adminApproveService;

    @PostMapping("/sms/select-auth")
    @RequiresPermission("recruit-message:view")
    public Response selectUserAuth(@RequestParam("authType") int authType) {
        return  ResponseUtil.makeSuccess(crowdfundingDetailSendMsgTemplateBiz
                .getAllSmsAuthGroup(ContextUtil.getAdminUserId(),  authType));
    }

    // 通过modelnum 查找短信模版
    @PostMapping("/sms/select-by-modelnum")
    @RequiresPermission("recruit-message:view")
    public Response selectSmsByModelNum(@RequestParam("modelNum") String modelNum) {

        return  ResponseUtil.makeSuccess(crowdfundingDetailSendMsgTemplateBiz
                .selectTemplateByModelNum(modelNum));
    }

    @PostMapping("/sms/add-template")
    @RequiresPermission("recruit-message:view")
    public Response addSmsTemplate(@RequestParam("smsGroup") int smsGroup,
                                   @RequestParam("modelNum") String modelNum) {

        return ResponseUtil.makeSuccess(crowdfundingDetailSendMsgTemplateBiz.addSmsTemplate(smsGroup,
                modelNum,
                ContextUtil.getAdminUserId()));
    }

    @PostMapping("/sms/enable-or-disable")
    @RequiresPermission("recruit-message:view")
    public Response enableOrDisable(@RequestParam("id") int id,
                                    @RequestParam("dataStatus") int dataStatus) {
        return ResponseUtil.makeSuccess(crowdfundingDetailSendMsgTemplateBiz.updateStatusById(id, dataStatus, ContextUtil.getAdminUserId()
                ));
    }

    @PostMapping("/sms/change-sort")
    @RequiresPermission("recruit-message:view")
    public Response changeSmsSort(@RequestParam("upId") int upId,
                                  @RequestParam("downId") int downId,
                                  @RequestParam("operateType") int operateType) {
        return  ResponseUtil.makeSuccess(crowdfundingDetailSendMsgTemplateBiz
                .updateSmsTemplatePriority(upId, downId, operateType,  ContextUtil.getAdminUserId()));
    }

    @PostMapping("/sms/select-template-list")
    @RequiresPermission("recruit-message:view")
    public Response selectTemplateList(@RequestParam("smsGroup") int smsGroup,
                                       @RequestParam("templateTitle") String templateTitle,
                                       @RequestParam("operatorId") Integer operatorId,
                                       @RequestParam("dataStatus") Integer dataStatus) {
        return ResponseUtil.makeSuccess(crowdfundingDetailSendMsgTemplateBiz
                .selectTemplateByParam(smsGroup, templateTitle, operatorId, dataStatus));
    }

    @PostMapping("/sms/select-content-list")
    @RequiresPermission("recruit-message:view")
    public Response selectContentList() {
        return ResponseUtil.makeSuccess(crowdfundingDetailSendMsgTemplateBiz.selectAuthSmsContent(ContextUtil.getAdminUserId()));
    }

    @PostMapping("/sms/select-operate-list")
    @RequiresPermission("recruit-message:view")
    public Response selectOperateList(@RequestParam("id") int id) {
        return ResponseUtil.makeSuccess(crowdfundingDetailSendMsgTemplateBiz.selectRecordByTemplateId(id));
    }

    @PostMapping(path = "/sms/send-message")
    @RequiresPermission("recruit-message:send-message")
    public Response<Void> sendMessage(String mobile, String message, @RequestParam(required = false, name = "modelNum") String modelNum) {
        log.info("线上招募 SendMessage mobile:{};recruit-message:{}", mobile, message);
        mobile = StringUtils.trimToEmpty(mobile);
        if (MobileUtil.illegal(mobile)) {
            return NewResponseUtil.makeError(AdminErrorCode.MOBILE_FORMAT_ERROR);
        }

        if (StringUtils.isAllBlank(message, modelNum) ||
                StringUtils.isNotBlank(message) && StringUtils.isNotBlank(modelNum)) {
            return NewResponseUtil.makeResponse(AdminErrorCode.SYSTEM_PARAM_IS_NULL.getCode(), "参数不合法", null);
        }

        if (StringUtils.isNotBlank(message) && message.length() > SMS_MAX_SIZE) {
            return NewResponseUtil.makeResponse(AdminErrorCode.SYSTEM_PARAM_ERROR.getCode(), "自定义短信内容太长", null);
        }

        adminApproveService.sendOnlineRecruitSmsWithRecord(mobile, message, modelNum, ContextUtil.getAdminUserId(), CUSTOM_MODEL_NUM);
        return NewResponseUtil.makeSuccess(null);
    }

}
