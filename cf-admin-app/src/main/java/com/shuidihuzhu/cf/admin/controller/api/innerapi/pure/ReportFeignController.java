package com.shuidihuzhu.cf.admin.controller.api.innerapi.pure;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingReportBiz;
import com.shuidihuzhu.cf.client.adminpure.feign.ReportFeignClient;
import com.shuidihuzhu.cf.client.adminpure.model.report.CrowdfundingReportVo;
import com.shuidihuzhu.cf.client.adminpure.model.report.ReportDependenceInfoVo;
import com.shuidihuzhu.cf.client.adminpure.model.report.ReportInfoVO;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport;
import com.shuidihuzhu.cf.service.crowdfunding.report.CfReportService;
import com.shuidihuzhu.cf.service.report.ReportDependenceService;
import com.shuidihuzhu.cf.service.report.ReportOperationService;
import com.shuidihuzhu.common.util.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2020/2/27
 */
@RestController
public class ReportFeignController implements ReportFeignClient {

    @Autowired
    private ReportOperationService reportOperationService;

    @Resource
    private ReportDependenceService reportDependenceService;

    @Resource
    private CfReportService cfReportService;

    @Autowired
    private AdminCrowdfundingReportBiz adminCrowdfundingReportBiz;

    @Override
    public OperationResult<ReportInfoVO> getReportInfoByCaseId(int caseId) {
        return reportOperationService.getInfo(caseId);
    }

    @Override
    public OperationResult<ReportDependenceInfoVo> getReportDependenceInfoByCaseId(int caseId) {
        return reportDependenceService.getReportDependenceInfo(caseId);
    }

    @Override
    public OperationResult<Boolean> autoMarkBusinessForward(int caseId, String prob) {
        return OperationResult.success(cfReportService.autoMarkBusinessForward(caseId, prob));
    }

    @Override
    public OperationResult<Boolean> autoMarkUserShareAndView(long userId) {
        return OperationResult.success(cfReportService.autoMarkUserShareAndView(userId));
    }

    @Override
    public OperationResult<List<CrowdfundingReportVo>> getReportListByIds(List<Integer> ids) {
        List<CrowdfundingReportVo> res = new ArrayList<>();
        if (CollectionUtils.isEmpty(ids)) {
            return OperationResult.success(res);
        }
        List<CrowdfundingReport> reports = adminCrowdfundingReportBiz.getListByReportIds(ids);
        reports.forEach(report -> {
            CrowdfundingReportVo vo = new CrowdfundingReportVo();
            BeanUtils.copyProperties(report, vo);
            res.add(vo);
        });
        return OperationResult.success(res);
    }
}
