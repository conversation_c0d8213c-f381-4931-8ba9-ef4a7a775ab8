package com.shuidihuzhu.cf.admin.controller.api.innerapi;

import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.client.cf.admin.client.ApproveRemarkFeignClient;
import com.shuidihuzhu.client.cf.admin.model.AdminApproveRemark;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/12/11 2:33 PM
 */
@Slf4j
@RestController
public class ApproveRemarkFeignController implements ApproveRemarkFeignClient {

    @Resource
    private ApproveRemarkOldService approveRemarkOldService;

    @Override
    public Response<Void> addApproveRemark(AdminApproveRemark adminApproveRemark) {

        if (Objects.isNull(adminApproveRemark)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        approveRemarkOldService.add(adminApproveRemark.getCaseId(), adminApproveRemark.getOperatorId(), adminApproveRemark.getRemark());
        return NewResponseUtil.makeSuccess();
    }
}
