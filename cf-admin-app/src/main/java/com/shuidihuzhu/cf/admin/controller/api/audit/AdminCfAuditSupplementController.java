package com.shuidihuzhu.cf.admin.controller.api.audit;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.apipure.feign.audit.CfAuditFeignClient;
import com.shuidihuzhu.cf.client.apipure.model.audit.CfAuditInfoSupplement;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.FinanceApproveService;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Author: wangpeng
 * @Date: 2023/5/17 20:07
 * @Description:
 */
@Api("审核补充信息")
@Slf4j
@RequestMapping("admin/cf/audit/supplement")
@RestController
public class AdminCfAuditSupplementController {

    @Resource
    private CfAuditFeignClient cfAuditFeignClient;
    @Resource
    private FinanceApproveService financeApproveService;
    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @RequiresPermission("audit-supplement:get-by-caseId")
    @ApiOperation("查询补充信息")
    @PostMapping("list-by-caseId")
    public Response<List<CfAuditInfoSupplement>> searchByCondition(@RequestParam() int caseId) {
        return cfAuditFeignClient.getAuditInfoSupplementByCaseIdId(caseId);
    }

    @RequiresPermission("audit-supplement:insert-or-update")
    @ApiOperation("补充")
    @PostMapping("insert-or-update-supplement")
    public Response<Integer> insertOrUpdateSupplement(@RequestBody() List<CfAuditInfoSupplement> supplementList) {
        if (CollectionUtils.isEmpty(supplementList)) {
            return  NewResponseUtil.makeSuccess(AdminErrorCode.SYSTEM_PARAM_ERROR.getCode());
        }
        Integer caseId = supplementList.stream()
                .findFirst()
                .map(CfAuditInfoSupplement::getCaseId)
                .orElse(0);
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
        if (Objects.isNull(crowdfundingInfo)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        supplementList.forEach(f -> {
            CfAuditInfoSupplement cfAuditInfoSupplement = new CfAuditInfoSupplement();
            cfAuditInfoSupplement.setCaseId(f.getCaseId());
            cfAuditInfoSupplement.setSupplementType(f.getSupplementType());
            cfAuditInfoSupplement.setContent(f.getContent());
            cfAuditFeignClient.insertOrUpdateSupplementInfo(cfAuditInfoSupplement);
            financeApproveService.addApprove(crowdfundingInfo, "审核补充信息", f.getContent(), (int) ContextUtil.getAdminLongUserId());
        });
        return NewResponseUtil.makeSuccess();
    }

}
