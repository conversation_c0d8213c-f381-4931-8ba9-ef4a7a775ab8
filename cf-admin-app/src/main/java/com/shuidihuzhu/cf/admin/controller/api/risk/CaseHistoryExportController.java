package com.shuidihuzhu.cf.admin.controller.api.risk;

import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingUserDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.vo.crowdfunding.CrowdfundingSimpleInfoVo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 * Created by sven on 2019/6/20.
 *
 * <AUTHOR>
 */
@Api("导出历史风控字段的接口")
@RestController
@Slf4j
@RefreshScope
@RequestMapping(path = "/admin/crowdfunding/risk")
public class CaseHistoryExportController {

    private final static String FILENAME_PREFIX = "risk_history_";

    private final static DateFormat dateFormat = new SimpleDateFormat("yyyMMdd HH:mm:ss");

    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Resource
    private IRiskDelegate riskDelegate;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;

    @Resource
    private ICrowdfundingUserDelegate crowdfundingUserDelegate;

    @Autowired
    private MaskUtil maskUtil;

    @RequiresPermission("risk:history/search")
    @ApiOperation("检索接口")
    @RequestMapping("/history/search")
    public Response<List<CrowdfundingSimpleInfoVo>> search(@RequestParam(value = "userId", required = false,defaultValue = "0") long userId,
                       @RequestParam(value = "mobile", required = false, defaultValue = "")String mobile,
                       @RequestParam(value = "caseId", required = false, defaultValue = "0") int caseId){

        if(userId == 0 && StringUtils.isEmpty(mobile) && caseId ==0){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<Integer> caseIds = getCaseIds(userId, mobile, caseId);

        List<CrowdfundingSimpleInfoVo> simpleInfoVos = getSimpleVos(caseIds);

        return NewResponseUtil.makeSuccess(simpleInfoVos);
    }

    private List<Integer> getCaseIds(long userId, String mobile, int caseId) {
        List<Integer> caseIds = Lists.newArrayList();
        if(caseId > 0){
            caseIds.add(caseId);
            return caseIds;
        }
        if(userId > 0) {
            List<Integer> ids = getCrowdfundingIds(userId);
            if(CollectionUtils.isNotEmpty(ids)){
                caseIds.addAll(ids);
                return caseIds;
            }
        }
        if(StringUtils.isNotEmpty(mobile)){
            UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByMobile(mobile);
            if(userInfoModel != null){
                List<Integer> ids = getCrowdfundingIds(userInfoModel.getUserId());
                if(CollectionUtils.isNotEmpty(ids)) {
                    caseIds.addAll(ids);
                }
            }
        }
        return caseIds;
    }

    private List<CrowdfundingSimpleInfoVo> getSimpleVos(List<Integer> caseIds) {
        List<CrowdfundingSimpleInfoVo> list = Lists.newArrayList();
        if(CollectionUtils.isEmpty(caseIds)){
            return list;
        }

        for(Integer caseId : caseIds){
            CrowdfundingSimpleInfoVo crowdfundingSimpleInfoVo = new CrowdfundingSimpleInfoVo();
            crowdfundingSimpleInfoVo.setCaseId(caseId);
            CrowdfundingInfo crowdfundingInfo = crowdfundingDelegate.getFundingInfoById(caseId);
            if(crowdfundingInfo == null){
                continue;
            }
            crowdfundingSimpleInfoVo.setTile(crowdfundingInfo.getTitle());
            crowdfundingSimpleInfoVo.setTargetAmount(crowdfundingInfo.getTargetAmount());
            crowdfundingSimpleInfoVo.setCreateDateStr(dateFormat.format(crowdfundingInfo.getCreateTime()));
            crowdfundingSimpleInfoVo.setAuthorName(getAuthorName(caseId));
            UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByUserId(crowdfundingInfo.getUserId());
            crowdfundingSimpleInfoVo.setMobile(null);
            crowdfundingSimpleInfoVo.setMobileMask(maskUtil.buildByEncryptPhone(userInfoModel.getCryptoMobile()));
            list.add(crowdfundingSimpleInfoVo);
        }
        return list;
    }

    private List<Integer> getCrowdfundingIds(long userId) {

        List<Integer> caseIds = Lists.newArrayList();

        List<CrowdfundingInfo> crowdfundingInfoViews = crowdfundingDelegate.getCrowdfundingInfoByUserId(userId);
        if(CollectionUtils.isNotEmpty(crowdfundingInfoViews)){
            crowdfundingInfoViews.stream().forEach(r->caseIds.add(r.getId()));
        }

        return caseIds;
    }

    private String getAuthorName(int caseId){

        CrowdfundingAuthor author = crowdfundingUserDelegate.getCrowdfundingAuthor(caseId);
        if(author != null){
            return author.getName();
        }

        CfFirsApproveMaterial cfFirsApproveMaterial = riskDelegate.getCfFirsApproveMaterialByInfoId(caseId);
        if(cfFirsApproveMaterial != null){
            return cfFirsApproveMaterial.getPatientRealName();
        }

        return StringUtils.EMPTY;
    }
}
