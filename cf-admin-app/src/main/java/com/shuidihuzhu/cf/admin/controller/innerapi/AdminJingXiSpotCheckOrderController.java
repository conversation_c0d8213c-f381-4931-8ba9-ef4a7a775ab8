package com.shuidihuzhu.cf.admin.controller.innerapi;

import com.github.pagehelper.PageInfo;
import com.shuidihuzhu.cf.client.adminpure.feign.AdminJingXiSpotCheckOrderClient;
import com.shuidihuzhu.cf.client.adminpure.model.caseinfo.AdminWorkOrderFlowView;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.service.workorder.read.AdminJingxiSpotCheckService;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
public class AdminJingXiSpotCheckOrderController implements AdminJingXiSpotCheckOrderClient {

    @Autowired
    private AdminJingxiSpotCheckService adminJingxiSpotCheckService;

    @Override
    public OperationResult<Map<String, Object>> getExtInfo(long workOrderId, int orderType) {
        return adminJingxiSpotCheckService.getExtInfoV2(workOrderId, orderType);
    }

    @Override
    public OperationResult<PageInfo<AdminWorkOrderFlowView>> getTransferInfo(String searchParamJson) {
        return adminJingxiSpotCheckService.getTransferInfo(searchParamJson);
    }

}
