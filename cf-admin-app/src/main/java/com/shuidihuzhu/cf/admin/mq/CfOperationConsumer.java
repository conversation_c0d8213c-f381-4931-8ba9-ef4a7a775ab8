package com.shuidihuzhu.cf.admin.mq;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfClewChannelInfoBiz;
import com.shuidihuzhu.cf.client.adminpure.model.caseinfo.CfClewChannelInfo;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.mq.OcrMedicalCaseInfoMqBody;
import com.shuidihuzhu.cf.admin.river.impl.RiverDiBaoFacadeImpl;
import com.shuidihuzhu.cf.admin.river.impl.RiverPinKunFacadeImpl;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingAttachmentBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.GeneralConstant;
import com.shuidihuzhu.cf.constants.admin.UserOperationStatConstant;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingOperationDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingUserDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.delegate.shorturl.ShortUrlDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfVersion;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.CrowdfundingInitialAuditInfo;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cf.service.CfCaseWorkOrderService;
import com.shuidihuzhu.cf.service.apollo.ApolloService;
import com.shuidihuzhu.cf.service.message.VolunteerHelpService;
import com.shuidihuzhu.cf.service.notice.workwx.WorkWeiXinContentBuilder;
import com.shuidihuzhu.cf.service.risk.repeat.CfRepeatCaseService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditCreateOrder;
import com.shuidihuzhu.cf.util.crowdfunding.CrowdfundingUtil;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewBaseInfoDO;
import com.shuidihuzhu.client.cf.growthtool.client.CfBdCaseInfoFeignClient;
import com.shuidihuzhu.client.cf.growthtool.client.ClewPreproseMaterialFeignClient;
import com.shuidihuzhu.client.cf.workorder.CfUgcWorkOrderClient;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * Created by sven on 18/8/2.
 *
 * <AUTHOR>
 * <p>
 * 接受消息时候创建的是图文审核工单
 */
@Service
@RocketMQListener(id = MQTagCons.CF_OPERATION_MSG,
        group = "cf-admin-operation-group",
        tags = MQTagCons.CF_OPERATION_MSG,
        topic = MQTopicCons.CF)
@Slf4j
public class CfOperationConsumer implements MessageListener<CfOperatingRecord> {


    @Resource
    private CfCaseWorkOrderService cfCaseWorkOrderService;

    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Resource
    private AdminCfInfoExtBiz adminCfInfoExtBiz;

    @Resource
    private MeterRegistry meterRegistry;

    @Resource
    private VolunteerHelpService volunteerHelpService;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;

    @Resource
    private IRiskDelegate riskDelegate;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Resource
    private ICrowdfundingUserDelegate crowdfundingUserDelegate;


    @Autowired
    private ICrowdfundingOperationDelegate crowdfundingOperationDelegate;

    @Autowired
    private InitialAuditCreateOrder initialAuditCreateOrder;

    @Resource
    private ShortUrlDelegate shortUrlDelegate;

    @Autowired
    private RiverPinKunFacadeImpl riverPinKunFacade;

    @Autowired
    private RiverDiBaoFacadeImpl riverDiBaoFacade;

    @Resource
    private CfBdCaseInfoFeignClient cfBdCaseInfoFeignClient;

    @Autowired
    private ApolloService apolloService;

    @Resource
    private AdminCrowdfundingAttachmentBiz adminCrowdfundingAttachmentBiz;

    @Autowired(required = false)
    private Producer producer;

    @Resource
    private CfClewChannelInfoBiz cfClewChannelInfoBiz;

    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Resource
    private CfRepeatCaseService cfRepeatCaseService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfOperatingRecord> mqMessage) {

        if (mqMessage == null || mqMessage.getPayload() == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        //上报一次打点信息
        meterRegistry.counter(UserOperationStatConstant.USER_OPERATING_STAT,
                UserOperationStatConstant.OPERATION, UserOperationStatConstant.ADD_BASE_INFO).increment();

        log.info("receive CfOperatingRecord message: {}", mqMessage.getPayload());

        CfOperatingRecord cfOperatingRecord = mqMessage.getPayload();

        createOrder(cfOperatingRecord);
        //消息开关
        if (apolloService.getCaseInitMsgSwitch() != 0) {
            sendNotice2BD(cfOperatingRecord);
        }

        saveClewChannelAndSendMqToG(cfOperatingRecord);

        cfRepeatCaseService.getRepeatCaseIdList(cfOperatingRecord.getInfoUuid());

        return ConsumeStatus.CONSUME_SUCCESS;
    }

    /**
     * 发起成功给对应的线下BD发送通知
     *
     * @param cfOperatingRecord
     */
    private void sendNotice2BD(CfOperatingRecord cfOperatingRecord) {
        try {
            String infoUuid = cfOperatingRecord.getInfoUuid();
            CrowdfundingInfo fundingInfo = crowdfundingDelegate.getCrowdfundingInfoByInfoId(infoUuid);
            CfInfoExt infoExt = adminCfInfoExtBiz.getByInfoUuid(infoUuid);
            String volunteerUniqueCode = infoExt.getVolunteerUniqueCode();

            if (StringUtils.isBlank(volunteerUniqueCode)) {
                return;
            }

            long userId = fundingInfo.getUserId();
            UserInfoModel userInfo = userInfoServiceBiz.getUserInfoByUserId(userId);
            String userMobile = shuidiCipher.decrypt(userInfo.getCryptoMobile());
            String name = CrowdfundingUtil.getTelephoneMask(userInfo.getRealName());
            String caseUrl = shortUrlDelegate.process(GeneralConstant.caseBaseUrl + cfOperatingRecord.getInfoUuid());
            CrowdfundingAuthor crowdfundingAuthor = crowdfundingUserDelegate.getCrowdfundingAuthor(fundingInfo.getId());
            CfFirsApproveMaterial approveMaterialMap = riskDelegate.getCfFirsApproveMaterialByInfoId(fundingInfo.getId());
            if (crowdfundingAuthor != null) {
                name = crowdfundingAuthor.getName();
            } else if (approveMaterialMap != null) {
                name = approveMaterialMap.getPatientRealName();
            }

            WorkWeiXinContentBuilder cb = WorkWeiXinContentBuilder.create()
                    .subject("【水滴筹】案例发起结果")
                    .payload("案例名称", "<a href=\"" + caseUrl + "\">" + fundingInfo.getTitle() + "</a>");
            String huanhaoText = "";
            try {
                Response<String> checkResult = cfBdCaseInfoFeignClient.getPreposeExchagePhone(fundingInfo.getId());
                if (checkResult.ok() && StringUtils.isNotEmpty(checkResult.getData())) {
                    cb.payload("发起人手机号", CrowdfundingUtil.getTelephoneMask(userMobile) + "【换号发起】");
                    cb.payload("录入手机号", CrowdfundingUtil.getTelephoneMask(checkResult.getData()));
                    huanhaoText = "如需更换发起手机号,请联系小鲸鱼客服";
                } else {
                    cb.payload("发起人手机号", CrowdfundingUtil.getTelephoneMask(userMobile));
                }
            } catch (Exception e) {
                log.warn("checkPreposeExchagePhone_caseId:{},error", fundingInfo.getId(), e);
                cb.payload("发起人手机号", CrowdfundingUtil.getTelephoneMask(userMobile));
            }
            cb.payload("患者姓名", name)
                    .payload("案例状态", "【已发起】")
                    .payload("发起时间", DateUtil.getCurrentDateStr());
            if (org.apache.commons.lang.StringUtils.isNotEmpty(huanhaoText)) {
                cb.payload("注意", huanhaoText);
            }
            String content = cb.build();
            volunteerHelpService.send(volunteerUniqueCode, content);
        } catch (Exception e) {
            log.error("cfOperatingRecord: {}", cfOperatingRecord, e);
        }
    }

    private void createOrder(CfOperatingRecord operatingRecord) {
        String infoUuid = operatingRecord.getInfoUuid();
        if (StringUtils.isEmpty(infoUuid)) {
            log.error("invalid CfOperatingRecord {}", operatingRecord);
        }
        CrowdfundingInfo crowdfundingInfo = crowdfundingDelegate.getCrowdfundingInfoByInfoId(infoUuid);
        if (Objects.isNull(crowdfundingInfo)) {
            return;
        }

        try {
            //创建敏感词的UGC任务
//            cfSensitiveWordService.addBaseInfoToWorkOrderOne(operatingRecord, UGCAction.RAISE);

            //如果是不需要首次审核的案例，在这里创建首次沟通的工单，需要首次审核的案例创建请参见AdminCfFirstApproveConsumer

            CfInfoExt cfInfoExt = adminCfInfoExtBiz.getByInfoUuid(infoUuid);
            FirstApproveStatusEnum statusEnum = FirstApproveStatusEnum.parse(cfInfoExt.getFirstApproveStatus());

            /** 仅需要处理老案例的首次沟通工单 */
            switch (statusEnum) {
                case DEFAULT:
                    cfCaseWorkOrderService.addWorkOrder(crowdfundingInfo.getId());
                    break;
                case APPLYING:
                    //待处理标识 是需要首次审核的   仅仅需要时才要首次审核

                    //上报一次打点信息
                    meterRegistry.counter(UserOperationStatConstant.USER_OPERATING_STAT,
                            UserOperationStatConstant.OPERATION, UserOperationStatConstant.INITIAL_AUDIT).increment();

//                    initialAuditCreateOrder.createChuci(crowdfundingInfo.getId(), 0, true);

                    insertInitialAuditInfo(cfInfoExt);

                    break;
                default:
                    break;
            }

        } catch (Exception e) {
            log.error("CfOperationConsumer异常", e);
        }

        try {
            Message message = Message.ofDelay(MQTopicCons.CF, com.shuidihuzhu.cf.constants.admin.MQTagCons.CASE_HEAD_IMAGE_CREATE_WORK_ORDER,
                    com.shuidihuzhu.cf.constants.admin.MQTagCons.CASE_HEAD_IMAGE_CREATE_WORK_ORDER + "_" + crowdfundingInfo.getId(),
                    crowdfundingInfo.getId(), 2, TimeUnit.MINUTES);
            producer.send(message);
        } catch (Exception e) {
            log.error("CfOperationConsumer creatHeadImgWorkOrder is error : {}, {}", crowdfundingInfo.getId(), e.getMessage());
        }

        // 保险供给匹配策略消息
        try {
            log.debug("CfOperationConsumer ocrMedicalCaseInfoMq send is success : {}", crowdfundingInfo.getId());

            List<CrowdfundingAttachment> attachmentList = adminCrowdfundingAttachmentBiz.getAttachmentsByType(crowdfundingInfo.getId(), AttachmentTypeEnum.ATTACH_FIRST_APPROVE_MEDICAL);
            if (CollectionUtils.isEmpty(attachmentList)) {
                log.info("CfOperationConsumer ocrMedicalCaseInfoMq attachmentList is empty : {}", crowdfundingInfo.getId());
                return;
            }
            OcrMedicalCaseInfoMqBody caseInfoMqBody = OcrMedicalCaseInfoMqBody.builder()
                    .medicalCaseId(String.valueOf(crowdfundingInfo.getId()))
                    .title(crowdfundingInfo.getTitle())
                    .content(crowdfundingInfo.getContent())
                    .pictureUrl(attachmentList.get(0).getUrl())
                    .build();
            Message<OcrMedicalCaseInfoMqBody> message = new Message<>(OcrMedicalCaseInfoMqBody.MQ_TOPIC, OcrMedicalCaseInfoMqBody.MQ_TAG, OcrMedicalCaseInfoMqBody.MQ_TAG + "_" + crowdfundingInfo.getId(), caseInfoMqBody);
            producer.send(message);
        } catch (Exception e) {
            log.error("ocrMedicalCaseInfoMq is error : {}, {}", crowdfundingInfo.getId(), e.getMessage());
        }


    }

    private void saveClewChannelAndSendMqToG(CfOperatingRecord cfOperatingRecord) {
        CrowdfundingInfo fundingInfo = adminCrowdfundingInfoBiz.getFundingInfo(cfOperatingRecord.getInfoUuid());
        if (Objects.isNull(fundingInfo)) {
            log.error("sendLiaoNingMission is error fundingInfo is null: {}", cfOperatingRecord);
            return;
        }
        try {
            cfClewChannelInfoBiz.sendLiaoNingMission(fundingInfo.getId());
        } catch (Exception e) {
            log.error("sendLiaoNingMission is error : {} {}", fundingInfo.getId(), e);
        }
    }

    private void insertInitialAuditInfo(CfInfoExt cfInfoExt) {
        int caseId = cfInfoExt.getCaseId();
        CrowdfundingInitialAuditInfo param = new CrowdfundingInitialAuditInfo()
                .buildCaseId(caseId)
                .buildBaseInfo(InitialAuditItem.MaterialStatus.SUBMIT.getCode())
                .buildFirstApproveInfo(InitialAuditItem.MaterialStatus.SUBMIT.getCode())
                .buildRejectDetail(null);
        if (CfVersion.isInitialProperty(cfInfoExt.getCfVersion())) {
            param.setCreditInfo(InitialAuditItem.MaterialStatus.SUBMIT.getCode());
        }

        crowdfundingOperationDelegate.insertCrowdfundingInitialAuditInfo(param);
        log.info("生成初审的状态表:caseId:{}", cfInfoExt.getCaseId());
        riverDiBaoFacade.onSubmit(caseId);
        riverPinKunFacade.onSubmit(caseId);
    }
}
