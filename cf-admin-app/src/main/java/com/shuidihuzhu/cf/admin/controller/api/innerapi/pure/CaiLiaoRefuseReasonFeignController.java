package com.shuidihuzhu.cf.admin.controller.api.innerapi.pure;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseReasonEntityBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseReasonTagBiz;
import com.shuidihuzhu.cf.client.adminpure.feign.CaiLiaoRefuseReasonFeignClient;
import com.shuidihuzhu.cf.client.adminpure.model.refuse.RefuseDataTypeDetailVO;
import com.shuidihuzhu.cf.client.adminpure.model.refuse.RefuseInfoVO;
import com.shuidihuzhu.cf.client.adminpure.model.refuse.RefuseItemVO;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonTag;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@RestController
public class CaiLiaoRefuseReasonFeignController implements CaiLiaoRefuseReasonFeignClient {

    @Autowired
    private CfRefuseReasonEntityBiz cfRefuseReasonEntityBiz;

    @Autowired
    private CfRefuseReasonTagBiz cfRefuseReasonTagBiz;

    @Override
    public OperationResult<RefuseInfoVO> selectRefuseInfoByReasonIds(List<Integer> reasonIds) {
        RefuseInfoVO v = new RefuseInfoVO();
        List<RefuseDataTypeDetailVO> detailList = Lists.newArrayList();
        v.setDataTypeList(detailList);
        if (CollectionUtils.isEmpty(reasonIds)) {
            return OperationResult.success(v);
        }
        List<CfRefuseReasonEntity> list = cfRefuseReasonEntityBiz.selectByReasonIds(Sets.newHashSet(reasonIds), 0);
        Set<Integer> tagIds = list.stream().map(CfRefuseReasonEntity::getTagId).collect(Collectors.toSet());
        List<CfRefuseReasonTag> tagInfoList = cfRefuseReasonTagBiz.selectByTagIds(tagIds);

        Map<Integer, List<CfRefuseReasonTag>> dataTypeTagMap = Maps.newHashMap();
        for (CfRefuseReasonTag tag : tagInfoList) {
            List<CfRefuseReasonTag> t = dataTypeTagMap.computeIfAbsent(tag.getDataType(), i -> Lists.newArrayList());
            t.add(tag);
        }

        Map<Integer, List<CfRefuseReasonEntity>> groupItemMap = list.stream().collect(Collectors.groupingBy(CfRefuseReasonEntity::getTagId));
        dataTypeTagMap.keySet()
                .forEach(dataType -> {
                    List<CfRefuseReasonTag> tags = dataTypeTagMap.get(dataType);
                    RefuseDataTypeDetailVO t = new RefuseDataTypeDetailVO();
                    t.setId(dataType);
                    t.setContent(getContentByDataType(dataType));
                    t.setItemList(Lists.newArrayList());
                    for (CfRefuseReasonTag tag : tags) {
                        List<RefuseItemVO> is = groupItemMap.get(tag.getId()).stream().map(this::convert).collect(Collectors.toList());
                        t.getItemList().addAll(is);
                    }
                    detailList.add(t);
                });
        return OperationResult.success(v);
    }

    private String getContentByDataType(Integer dataType) {
        switch (dataType) {
            case 1:
                return "图文信息";
            case 2:
                return "患者信息";
            case 3:
                return "收款人信息";
            case 4:
                return "诊断证明";
            case 7:
                return "资金用途";
            default:
                return "";
        }
    }

    private RefuseItemVO convert(CfRefuseReasonEntity cfRefuseReasonEntity) {
        RefuseItemVO i = new RefuseItemVO();
        i.setId(cfRefuseReasonEntity.getId());
        i.setContent(cfRefuseReasonEntity.getContent());
        return i;
    }
}
