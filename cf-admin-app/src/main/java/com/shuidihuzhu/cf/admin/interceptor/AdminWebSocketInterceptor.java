package com.shuidihuzhu.cf.admin.interceptor;

import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAuthClientV1;
import com.shuidihuzhu.cf.biz.websocket.AdminWebSocketHandler;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;

import java.io.OutputStream;
import java.net.URI;
import java.util.Map;

public class AdminWebSocketInterceptor implements HandshakeInterceptor {


    @Autowired
    private SeaAuthClientV1 seaAuthClientV1;
    @Override
    public boolean beforeHandshake(ServerHttpRequest serverHttpRequest, ServerHttpResponse serverHttpResponse,
                                   WebSocketHandler webSocketHandler, Map<String, Object> map) throws Exception {

        URI requestUri = serverHttpRequest.getURI();
        String rawPath = requestUri.getRawPath();
        String token = "";
        if (StringUtils.isNotBlank(rawPath)) {
            int position = rawPath.lastIndexOf("/");
            if (position != -1) {
                token = rawPath.substring(position + 1);
            }
        }

        AuthRpcResponse<Integer> authRpcResponse =  seaAuthClientV1.checkAuthentication(token);
        if (authRpcResponse == null || !authRpcResponse.isSuccess()) {
            OutputStream servletOutputStream = serverHttpResponse.getBody();
            servletOutputStream.write(ResponseUtil.toJson(authRpcResponse.getCode(), authRpcResponse.getMsg(), (Object)null).getBytes("UTF-8"));
            return false;
        }

        map.put(AdminWebSocketHandler.USERID, authRpcResponse.getResult());

        return true;
    }

    @Override
    public void afterHandshake(ServerHttpRequest serverHttpRequest, ServerHttpResponse serverHttpResponse, WebSocketHandler webSocketHandler,
                               Exception e) {

    }

}
