package com.shuidihuzhu.cf.admin.mq.casestatus;

import com.shuidihuzhu.cf.admin.mq.common.BaseMessageConsumer;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.approve.ApproveControlService;
import com.shuidihuzhu.cf.service.workorder.cailiao.ZhuDongFuWuWorkOrderService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 案例提交审核
 * <AUTHOR>
 */
@Service
@Slf4j
@RocketMQListener(id = MQTagCons.CF_SUBMIT_FOR_REVIEW,
        tags = MQTagCons.CF_SUBMIT_FOR_REVIEW,
        topic = MQTopicCons.CF)
public class AdminApproveSubmitForReviewConsumer extends BaseMessageConsumer<CrowdfundingInfo>
        implements MessageListener<CrowdfundingInfo> {

    @Autowired
    private ApproveControlService approveControlService;

    @Autowired
    private ZhuDongFuWuWorkOrderService zhuDongFuWuWorkOrderService;

    @Override
    protected boolean handle(ConsumerMessage<CrowdfundingInfo> consumerMessage) {
        CrowdfundingInfo fundingInfo = consumerMessage.getPayload();
        try {
            approveControlService.onUserSubmit(fundingInfo.getId());
        } catch (Exception e) {
            log.error("AdminApproveSubmitForReviewConsumer approve control", e);
        }

        try {
            zhuDongFuWuWorkOrderService.onUserSubmit(fundingInfo.getId());
        } catch (Exception e) {
            log.error("AdminApproveSubmitForReviewConsumer zhuDongFuWuWorkOrderService", e);
        }

        return true;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
