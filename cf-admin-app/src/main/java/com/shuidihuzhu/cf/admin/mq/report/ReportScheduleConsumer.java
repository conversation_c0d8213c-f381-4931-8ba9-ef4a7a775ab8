package com.shuidihuzhu.cf.admin.mq.report;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.enhancer.mq.BaseMessageConsumer;
import com.shuidihuzhu.cf.model.report.schedule.ReportSchedulePayload;
import com.shuidihuzhu.cf.service.report.ReportScheduleService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@RocketMQListener(id = MQTagCons.REPORT_SCHEDULE_PUSH,
        group = "cf-admin-" + MQTagCons.REPORT_SCHEDULE_PUSH,
        tags = MQTagCons.REPORT_SCHEDULE_PUSH,
        topic = MQTopicCons.CF)
@Slf4j
public class ReportScheduleConsumer extends BaseMessageConsumer<String> implements MessageListener<String> {

    @Autowired
    private ReportScheduleService reportScheduleService;

    @Override
    protected boolean handle(ConsumerMessage<String> consumerMessage) {
        String json = consumerMessage.getPayload();
        reportScheduleService.onDelayHandle(json);
        return true;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
