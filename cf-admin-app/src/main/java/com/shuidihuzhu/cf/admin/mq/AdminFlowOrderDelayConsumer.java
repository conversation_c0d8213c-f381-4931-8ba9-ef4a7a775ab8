package com.shuidihuzhu.cf.admin.mq;


import com.shuidihuzhu.cf.biz.admin.AdminWorkOrderBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderFlowBiz;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminWorkOrderFlowConst;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowParam;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView;
import com.shuidihuzhu.cf.service.workorder.WorkFlowOrderCommonService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@RocketMQListener(id = MQTagCons.ADMIN_FLOW_ORDER_SINGLE_REFUND_DELAY_48H,
        group = "cf-admin"+MQTagCons.ADMIN_FLOW_ORDER_SINGLE_REFUND_DELAY_48H,
        tags = MQTagCons.ADMIN_FLOW_ORDER_SINGLE_REFUND_DELAY_48H,
        topic = MQTopicCons.CF)
@Slf4j
public class AdminFlowOrderDelayConsumer implements MessageListener<String> {

    @Autowired
    private AdminWorkOrderFlowBiz workOrderFlowBiz;

    @Autowired
    private WorkFlowOrderCommonService flowOrderCommonService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<String> mqMessage) {

        log.info("信息流转工单delay48小时，收到msg:{}", mqMessage);
        if (mqMessage == null || StringUtils.isBlank(mqMessage.getPayload())) {
            return  ConsumeStatus.CONSUME_SUCCESS;
        }

        long flowId = flowOrderCommonService.decodeFromFlowIdString(mqMessage.getPayload());
        AdminWorkOrderFlowView flowView = workOrderFlowBiz.selectOrderFlowViewById(flowId);
        if (flowView == null) {
            log.error("信息流转工单的");
        }

        if (flowView.getWorkOrderStatus() == AdminWorkOrderConst.Status.HANDLE_SUCCESS.getCode()
            || flowView.getWorkOrderStatus() == AdminWorkOrderConst.Status.NO_NEED_HANDLE.getCode()) {
            log.info("信息流转工单已经是终态不需要处理。id : {}， status:{}", flowId, flowView.getWorkOrderStatus());
            return ConsumeStatus.CONSUME_SUCCESS;
        }


        // 主动将工单状态修改为处理完成
        AdminWorkOrderFlowParam.HandleParam param = new AdminWorkOrderFlowParam.HandleParam();
        param.setHandleType(AdminWorkOrderFlowConst.handleType.FINISH.getValue());
        param.setOperatorId(flowView.getOperatorId());
        param.setLevel(flowView.getLevel());
        param.setUserId(param.getOperatorId());
        param.setComment("系统关闭 超过48小时自动将工单更新为处理完成");
        param.setWorkOrderId(flowView.getWorkOrderId());
        workOrderFlowBiz.handleWorkFlowOrder(param);

//        flowOrderCommonService.sendRefundFlowOrderHandleSuccMsg(flowView.getCreateTime(), flowView.getId());

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
