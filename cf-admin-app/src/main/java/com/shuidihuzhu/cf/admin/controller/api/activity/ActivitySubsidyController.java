package com.shuidihuzhu.cf.admin.controller.api.activity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.activity.enums.ActivityTypeEnum;
import com.shuidihuzhu.cf.activity.enums.CaseSubsidyStatusEnum;
import com.shuidihuzhu.cf.activity.enums.DonateCooperateActivityStatusEnum;
import com.shuidihuzhu.cf.activity.enums.FeeTypeEnum;
import com.shuidihuzhu.cf.activity.feign.CaseCountFeignClient;
import com.shuidihuzhu.cf.activity.feign.CfActivityFeignClient;
import com.shuidihuzhu.cf.activity.feign.CfRuleLevelFeignClient;
import com.shuidihuzhu.cf.activity.feign.admin.ActivityAdminClient;
import com.shuidihuzhu.cf.activity.feign.v2.ActivityMutexFeignClient;
import com.shuidihuzhu.cf.activity.model.ActivityDetail;
import com.shuidihuzhu.cf.activity.model.DonateCooperateFeeVO;
import com.shuidihuzhu.cf.activity.model.DonateCooperateRuleLevelModel;
import com.shuidihuzhu.cf.activity.model.admin.ActivityAdminSimpleInfo;
import com.shuidihuzhu.cf.activity.model.admin.ActivityCaseAdminVO;
import com.shuidihuzhu.cf.activity.model.param.ActivityBaseParam;
import com.shuidihuzhu.cf.activity.model.param.ActivityLimitParam;
import com.shuidihuzhu.cf.activity.model.param.ActivityMoneyParam;
import com.shuidihuzhu.cf.activity.model.param.CreateActivityParam;
import com.shuidihuzhu.cf.admin.util.EnumPropertyUtils;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.apipure.feign.CfActivityJoinFeignClient;
import com.shuidihuzhu.cf.client.base.enums.BaseErrorCodeEnum;
import com.shuidihuzhu.cf.client.base.page.v1.model.AnchorPageVO;
import com.shuidihuzhu.cf.client.base.page.v1.model.PaginationListVO;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.feign.activity.ActivityApiFeignClient;
import com.shuidihuzhu.cf.domain.activity.ActivityWaveDO;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.activity.ActivityAdminShowEnumTypeEnum;
import com.shuidihuzhu.cf.enums.activity.ActivityKeyAreaTypeEnum;
import com.shuidihuzhu.cf.enums.activity.ActivityWaveTypeEnum;
import com.shuidihuzhu.cf.enums.activity.EnumPropertyVO;
import com.shuidihuzhu.cf.enums.activity.wave.WaveJoinTypeEnum;
import com.shuidihuzhu.cf.enums.activity.wave.WaveScopeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.util.CaseUtils;
import com.shuidihuzhu.cf.vo.activity.ActivityCaseAssembleAdminVO;
import com.shuidihuzhu.cf.vo.activity.ActivityCaseFundAdminVO;
import com.shuidihuzhu.cf.vo.activity.MultiAddActivityWaveVO;
import com.shuidihuzhu.client.cf.growthtool.client.ICrmHospitalFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.CfCrmStandardHospitalSimpleModel;
import com.shuidihuzhu.client.cf.growthtool.model.PageReturnModel;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 配捐杠杆实验
 * 配捐 配转 捐助金
 * wiki https://wiki.shuiditech.com/pages/viewpage.action?pageId=*********
 *
 * <AUTHOR>
 */
@Api("配捐活动")
@RestController
@Slf4j
@RequestMapping("admin/cf/activity/subsidy")
public class ActivitySubsidyController {

    @Autowired
    private CfActivityFeignClient cfActivityFeignClient;

    @Autowired
    private CaseCountFeignClient caseCountFeignClient;

    @Autowired
    private ActivityAdminClient activityAdminClient;

    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;

    @Autowired
    private ActivityApiFeignClient activityApiFeignClient;

    @Autowired
    private ICrmHospitalFeignClient crmHospitalFeignClient;

    @Autowired
    private CfActivityJoinFeignClient cfActivityJoinFeignClient;

    @Autowired
    private AdminCfInfoExtBiz adminCfInfoExtBiz;

    @Autowired
    private ActivityMutexFeignClient activityMutexFeignClient;
    @Autowired
    private CfRuleLevelFeignClient cfRuleLevelFeignClient;

    private Convertor<ActivityCaseAdminVO, ActivityCaseAssembleAdminVO> caseConvertor = new Convertor<>() {
        @Override
        public ActivityCaseAssembleAdminVO map(ActivityCaseAdminVO activityInfo) {
            int caseId = activityInfo.getCaseId();
            ActivityCaseAssembleAdminVO result = new ActivityCaseAssembleAdminVO();
            result.setActivityCaseInfo(activityInfo);

            ActivityCaseFundAdminVO fundInfo = new ActivityCaseFundAdminVO();
            CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
            if (crowdfundingInfo != null){
                fundInfo.setInfoUuid(crowdfundingInfo.getInfoId());
                fundInfo.setEnd(CaseUtils.hasEnd(crowdfundingInfo));
                fundInfo.setRaiseTime(crowdfundingInfo.getCreateTime());
                fundInfo.setTitle(crowdfundingInfo.getTitle());
            }
            fundInfo.setCaseId(caseId);
            result.setFundInfo(fundInfo);

            OperationResult<Boolean> canJoinResult = activityApiFeignClient.checkCanJoinHugeActivity(caseId);
            if (canJoinResult.isSuccess()) {
                result.setCanJoinHugeActivity(canJoinResult.getData());
            }

            return result;
        }
    };

    @PostMapping("join-activity-by-wave-manual")
    @RequiresPermission("activity:join-activity-by-wave-manual")
    public Response<Boolean> joinActivity(@RequestParam("activityId") long activityId,
                                          @RequestParam("caseId") int caseId,
                                          @RequestParam("waveId") int waveId) {
        CrowdfundingInfo fundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
        if (CaseUtils.hasEnd(fundingInfo)) {
            return NewResponseUtil.makeFail("加入失败，案例已结束");
        }
        CfInfoExt ext = adminCfInfoExtBiz.getByCaseId(caseId);
        if (ext.getFirstApproveStatus() != FirstApproveStatusEnum.APPLY_SUCCESS.getCode()) {
            return NewResponseUtil.makeFail("加入失败，未过初审");
        }
        OperationResult<Boolean> resp = cfActivityJoinFeignClient.joinActivityManual(activityId, caseId, waveId);
        return NewResponseUtil.makeResponse(resp.getCode(), resp.getMsg(), resp.getData());
    }

    @PostMapping("get-case-info")
    @RequiresPermission("activity:get-case-info")
    public Response<CaseSubsidyInfoVO> getCaseInfo(@RequestParam("caseId") int caseId) {
        Response<ActivityDetail> resp = mapResponse(cfActivityFeignClient.getActivityForRaiser(caseId));
        if (resp.notOk()) {
            return NewResponseUtil.makeResponse(resp.getCode(), resp.getMsg(), null);
        }
        ActivityDetail detail = resp.getData();
        if (detail == null) {
            return NewResponseUtil.makeSuccess(null);
        }
        CaseSubsidyInfoVO vo = new CaseSubsidyInfoVO();
        vo.setDetail(detail);
        vo.setStatus(CaseSubsidyStatusEnum.getSubsidyStatus(detail));
        return NewResponseUtil.makeSuccess(vo);
    }

    @PostMapping("list-subsidy-record")
    @RequiresPermission("activity:list-subsidy-record")
    public Response<List<DonateCooperateFeeVO>> listSubsidyRecord(@RequestParam("caseId") int caseId) {
        return mapResponse(caseCountFeignClient.getAllAvailable(caseId));
    }

    @PostMapping("list-subsidy-record-by-anchor")
    @RequiresPermission("activity:list-subsidy-record-by-anchor")
    public Response<AnchorPageVO<DonateCooperateFeeVO>> listSubsidyRecord(
            @RequestParam("caseId") int caseId,
            @RequestParam("anchor") long anchor,
            @RequestParam("pageSize") int pageSize,
            @RequestParam(value = "isPre", required = false, defaultValue = "false") boolean isPre) {
        return mapResponse(caseCountFeignClient.getAllAvailableByAnchor(caseId, anchor, pageSize, isPre));
    }

    @PostMapping("get-activity-list-by-condition")
    @RequiresPermission("activity:get-activity-list-by-condition")
    OperationResult<PaginationListVO<ActivityAdminSimpleInfo>> getActivityListByCondition(
            @RequestParam(value = "activityId", required = false, defaultValue = "0") long activityId,
            @RequestParam(value = "status", required = false) Integer status,
            @RequestParam(value = "current", required = false, defaultValue = "0") int current,
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") int pageSize
    ) {
        if (activityId > 0) {
            OperationResult<ActivityAdminSimpleInfo> activityResult = activityAdminClient.getActivityById(activityId);
            ActivityAdminSimpleInfo activity = activityResult.getData();
            PaginationListVO<ActivityAdminSimpleInfo> result = PaginationListVO.create(Lists.newArrayList(activity), 1, pageSize, 1);
            return OperationResult.success(result);
        }
        return activityAdminClient.getActivityListByCondition(status, current, pageSize);
    }


    @PostMapping("get-case-list-by-condition")
    @RequiresPermission("activity:get-case-list-by-condition")
    OperationResult<AnchorPageVO<ActivityCaseAssembleAdminVO>> getCaseListByCondition(
            @RequestParam("activityId") long activityId,
            @RequestParam(value = "caseId", required = false, defaultValue = "0") int caseId,
            @RequestParam(value = "caseStatus", required = false) Integer caseStatus,
            @RequestParam(value = "anchor", required = false, defaultValue = "0") long anchor,
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") int pageSize,
            @RequestParam(value = "isPre", required = false, defaultValue = "false") boolean isPre) {
        if (caseId > 0) {
            OperationResult<ActivityCaseAdminVO> activityResult = activityAdminClient.getCaseById(activityId, caseId);
            ActivityCaseAdminVO activity = activityResult.getData();
            if (activity == null){
                AnchorPageVO vo = new AnchorPageVO();
                return OperationResult.success(vo);
            }
            AnchorPageVO<ActivityCaseAdminVO> result = new AnchorPageVO<>();
            result.setList(Lists.newArrayList(activity));
            return mapCase(OperationResult.success(result));
        }
        return mapCase(activityAdminClient.getCaseListByCondition(activityId, caseStatus, anchor, pageSize, isPre));
    }

    @ApiOperation("更新案例的活动状态")
    @PostMapping("update-case-status")
    @RequiresPermission("activity:update-case-status")
    public Response<Void> updateCaseStatus(@RequestParam("activityId") long activityId,
                                           @RequestParam("caseId") int caseId,
                                           @RequestParam("targetStatus") int targetStatus) {
        int adminUserId = ContextUtil.getAdminUserId();
        return updateStatus(activityId, caseId, targetStatus, adminUserId);
    }

    @ApiOperation("更新案例的活动状态为暂停")
    @PostMapping("pause-case")
    @RequiresPermission("activity:pause-case")
    public Response<Void> pauseCase(@RequestParam("activityId") long activityId,
                                    @RequestParam("caseId") int caseId) {
        int adminUserId = ContextUtil.getAdminUserId();
        return updateStatus(activityId, caseId, DonateCooperateActivityStatusEnum.PAUSE.getCode(), adminUserId);
    }

    /**
     * @param enumType {@link ActivityAdminShowEnumTypeEnum}
     * @return
     */
    @ApiOperation("查询枚举取值范围")
    @PostMapping("get-activity-enum-property")
    @RequiresPermission("activity:get-activity-enum-property")
    OperationResult<List<EnumPropertyVO>> getActivityEnumProperty(
            @ApiParam("ActivityAdminShowEnumTypeEnum \n" +
                    "    /**\n" +
                    "     */\n" +
                    "    @ApiModelProperty(\"活动类型\")\n" +
                    "    ACTIVITY_TYPE(1),\n" +
                    "\n" +
                    "    @ApiModelProperty(\"补贴类型\")\n" +
                    "    FEE_TYPE(2),\n" +
                    "\n" +
                    "    @ApiModelProperty(\"参加活动规则类型\")\n" +
                    "    WAVE_TYPE(3),\n" +
                    "\n" +
                    "    @ApiModelProperty(\"重点区域类型\")\n" +
                    "    KEY_AREA_TYPE(4),\n" +
                    "\n" +
                    "    @ApiModelProperty(\"参加范围枚举\")\n" +
                    "    SCOPE_TYPE(5),\n" +
                    "\n" +
                    "    @ApiModelProperty(\"参加方式枚举\")\n" +
                    "    JOIN_TYPE(6),") @RequestParam("enumType") int enumType) {
        ActivityAdminShowEnumTypeEnum enumTypeEnum = ActivityAdminShowEnumTypeEnum.parse(enumType);
        List<EnumPropertyVO> results = Lists.newArrayList();
        switch (enumTypeEnum) {
            case FEE_TYPE:
                results = Lists.newArrayList(
                        c(FeeTypeEnum.SHARE_COOPERATE_FEE),
                        c(FeeTypeEnum.ALLOWANCES_COOPERATE_FEE),
                        c(FeeTypeEnum.TASK)
                );
                break;
            case WAVE_TYPE:
                results = Lists.newArrayList();
                break;
            case ACTIVITY_TYPE:
                results = Lists.newArrayList(
                        c(ActivityTypeEnum.PERSONAL),
                        c(ActivityTypeEnum.PUBLIC),
                        c(ActivityTypeEnum.VENUE_PERSONAL),
                        c(ActivityTypeEnum.KEY_AREA),
                        c(ActivityTypeEnum.KEY_AREA_TASK),
                        c(ActivityTypeEnum.DISTRIBUTION_TRANSFE),
                        c(ActivityTypeEnum.QQ)
                );
                break;
            case KEY_AREA_TYPE:
                RpcResult<List<DonateCooperateRuleLevelModel>> result = cfRuleLevelFeignClient.queryAllRuleLevel();
                if (result.isSuccess() && CollectionUtils.isNotEmpty(result.getData())) {
                    List<DonateCooperateRuleLevelModel> data = result.getData();
                    data = data.stream().filter(s -> !s.isBasic()).collect(Collectors.toList());
                    results = data.stream().map(s -> beanCovertVo(s)).collect(Collectors.toList());
                }
                break;
            case JOIN_TYPE:
                results = Arrays.stream(WaveJoinTypeEnum.values()).map(this::c).collect(Collectors.toList());
                break;
            case SCOPE_TYPE:
                results = Arrays.stream(WaveScopeEnum.values()).map(this::c).collect(Collectors.toList());
                break;
            default:
                return OperationResult.fail(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        return OperationResult.success(results);
    }

    private EnumPropertyVO c(Enum<?> e) {
        return EnumPropertyUtils.parseEnum(e);
    }

    private EnumPropertyVO beanCovertVo(DonateCooperateRuleLevelModel e){
        //执行枚举方法获得枚举实例对应的值
        EnumPropertyVO result = new EnumPropertyVO();

        String json = JSON.toJSONString(e);
        JSONObject jsonObject = JSON.parseObject(json);
        result.setData(jsonObject);
        Integer code = e.getCode();
        if (code != null) {
            result.setValue(code);
        }

        String msg = e.getMsg();
        if (msg != null) {
            result.setContent(msg);
        }
        result.setName(msg);
        return result;
    }

    @ApiOperation("创建活动")
    @RequiresPermission("activity:create-activity")
    @PostMapping("create-activity")
    OperationResult<Void> createActivity(@RequestBody CreateActivityParam createActivityParam) {
        int adminUserId = ContextUtil.getAdminUserId();
        createActivityParam.setSeaUserId(adminUserId);
        createActivityParam.setSeaUserName("");
        ActivityBaseParam baseParam = createActivityParam.getBaseParam();
        ActivityMoneyParam moneyParam = createActivityParam.getMoneyParam();
        ActivityLimitParam limitParam = createActivityParam.getLimitParam();
        if (StringUtils.length(baseParam.getActivityName()) > 48) {
            OperationResult.failWithMsg("活动名称不能大于48个字符");
        }
        if (baseParam.getActivityTypeEnum().isTaskSubsidy()) {
            if (moneyParam.getTaskGroupId() <= 0) {
                return OperationResult.failWithMsg("任务补贴需要配置任务组id taskGroupId");
            }
        }
        if (StringUtils.equals(moneyParam.getUnitName(), "元")) {
            if (moneyParam.getMoneyExchangeUnit() != 100) {
                return OperationResult.failWithMsg("确定活动单位配置为元，换算单位不为 1元 == 100分吗？");
            }
        }
        if (limitParam.getTotalCount() > 2147483147) {
            return OperationResult.failWithMsg("预算上限最大支持2147483147.00");
        }
        if (limitParam.getCaseCount() > 2147483147) {
            return OperationResult.failWithMsg("案例硬帽最大支持2147483147.00");
        }
        if (moneyParam.getSureValue() > 2147483147) {
            return OperationResult.failWithMsg("一口价最大支持2147483147.00");
        }
        return activityAdminClient.createActivity(createActivityParam);
    }

    //    @PostMapping("add-wave")
    Response<Void> addWave(@RequestBody ActivityWaveDO param) {
        return activityApiFeignClient.addWave(param);
    }

    @PostMapping("add-rule-level")
    @RequiresPermission("activity:add-rule-level")
    Response<Void> addRuleLevel(@RequestParam String sign,
                                @RequestParam int caseSoftAmountLimit) {

        DonateCooperateRuleLevelModel model = new DonateCooperateRuleLevelModel();
        model.setBasic(false);
        model.setCaseSoftAmountLimit(caseSoftAmountLimit);
        model.setMsg(sign);
        model.setSign(sign);
        cfRuleLevelFeignClient.saveRuleLevel(model);
        return NewResponseUtil.makeSuccess(null);

    }

    @PostMapping("multi-add-wave")
    @RequiresPermission("activity:multi-add-wave")
    Response<Void> multiAddWave(@RequestBody MultiAddActivityWaveVO param) {
        long activityId = param.getActivityId();
        Date endTime = param.getEndTime();
        OperationResult<ActivityAdminSimpleInfo> activityResult = activityAdminClient.getActivityById(activityId);
        if (activityResult == null || activityResult.isFail()) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
        }
        ActivityAdminSimpleInfo activity = activityResult.getData();
        if (activity == null) {
            return NewResponseUtil.makeFail("活动id不存在");
        }
        ActivityTypeEnum activityTypeEnum = activity.getActivityTypeEnum();
        int dayCount = activity.getDayCount();
        if (dayCount > 0) {
            LocalDateTime joinEndTime = LocalDateTime.ofInstant(endTime.toInstant(), ZoneId.systemDefault());
            LocalDateTime earliestActivityEndTime = joinEndTime.plusDays(dayCount - 1).withHour(23).withMinute(59).withSecond(59);
            LocalDateTime activityEndTime = LocalDateTime.ofInstant(activity.getEndTime().toInstant(), ZoneId.systemDefault());
            if (activityEndTime.isBefore(earliestActivityEndTime)) {
                return NewResponseUtil.makeFail("失败: 配置加入活动的结束时间 + " + dayCount + "天 要早于配置的活动的结束时间");
            }
        }
        if (activity.getStatusEnum() != DonateCooperateActivityStatusEnum.APPROVE) {
            return NewResponseUtil.makeFail("失败: 配置的活动为非进行中活动");
        }
        boolean onlyOneThreshold = ((param.getThreshold() > 0 ? 1 : 0)
                + (param.getDonateCountThreshold() > 0 ? 1 : 0)
                + (param.getDonateMoneyThreshold() > 0 ? 1 : 0)
        ) == 1;

        if (activity.getActivityTypeEnum() == ActivityTypeEnum.KEY_AREA && onlyOneThreshold && param.getThreshold() <= 0) {
            return NewResponseUtil.makeFail("仅有一个提现门槛时必须为补贴金额门槛");
        }

        List<MultiAddActivityWaveVO.WaveItem> waveItemList = param.getWaveItemList();
        int size = CollectionUtils.size(waveItemList);
        if (size <= 0) {
            return NewResponseUtil.makeFail("必须选定范围");
        }
        for (MultiAddActivityWaveVO.WaveItem i : waveItemList) {
            int requestWaveType = i.getWaveType();
            int waveType;
            // TODO delete 过渡
            if (requestWaveType != 0 && i.getScope() != 0) {
                return NewResponseUtil.makeFail("参数错误 waveType已废弃不要传");
            }
            if (requestWaveType != 0) {
                waveType = mapWaveTypeKeyArea2Area(requestWaveType, activity.getActivityTypeEnum());
            } else {
                waveType = getWaveTypeByActivityType(activityTypeEnum).getCode();
            }
            i.setWaveType(waveType);
            ActivityWaveDO w = new ActivityWaveDO();
            BeanUtils.copyProperties(param, w);
            w.setWaveType(i.getWaveType());
            w.setCityId(i.getCityId());
            w.setHospitalCode(StringUtils.trimToEmpty(i.getHospitalCode()));
            w.setHospitalName(StringUtils.trimToEmpty(i.getHospitalName()));
            w.setScope(i.getScope());
            w.setMaxPlace(param.getMaxPlace());
            Response<Void> addWaveResponse = activityApiFeignClient.addWave(w);
            log.info("add wave wave:{}, response:{}", w, addWaveResponse);
            if (addWaveResponse == null || addWaveResponse.notOk()) {
                return addWaveResponse;
            }
        }
        return NewResponseUtil.makeSuccess(null);
    }

    private ActivityWaveTypeEnum getWaveTypeByActivityType(ActivityTypeEnum activityTypeEnum) {
        if (activityTypeEnum.isTaskSubsidy()) {
            return ActivityWaveTypeEnum.V2_TASK_SUBSIDY;
        }
        if (activityTypeEnum.isAreaDonateSubsidy()) {
            return ActivityWaveTypeEnum.V2_BIG_SUBSIDY;
        }
        return ActivityWaveTypeEnum.V2_DEFAULT;
    }

    /**
     * 临时兼容 为了前端不用改
     *
     * @param waveType
     * @param activityTypeEnum
     * @return
     */
    private int mapWaveTypeKeyArea2Area(int waveType, ActivityTypeEnum activityTypeEnum) {
        if (waveType == ActivityWaveTypeEnum.KEY_AREA_CITY.getCode()) {
            if (activityTypeEnum.isTaskSubsidy()) {
                return ActivityWaveTypeEnum.AREA_TASK_SUBSIDY_CITY.getCode();
            }
            if (activityTypeEnum.isAreaDonateSubsidy()) {
                return ActivityWaveTypeEnum.KEY_AREA_CITY.getCode();
            }
            return ActivityWaveTypeEnum.AREA_SUBSIDY_CITY.getCode();
        }
        if (waveType == ActivityWaveTypeEnum.KEY_AREA_PROVINCE.getCode()) {
            if (activityTypeEnum.isTaskSubsidy()) {
                return ActivityWaveTypeEnum.AREA_TASK_SUBSIDY_PROVINCE.getCode();
            }
            if (activityTypeEnum.isAreaDonateSubsidy()) {
                return ActivityWaveTypeEnum.KEY_AREA_PROVINCE.getCode();
            }
            return ActivityWaveTypeEnum.AREA_SUBSIDY_PROVINCE.getCode();
        }
        throw new RuntimeException("没有此对应的waveType");
    }

    @PostMapping("get-wave-list-by-condition")
    @RequiresPermission("activity:get-wave-list-by-condition")
    OperationResult<PaginationListVO<ActivityWaveDO>> getWaveListByCondition(
            @ApiParam("筛选是否在活动中") @RequestParam(value = "status", required = false) Boolean status,
            @RequestParam("current") int current,
            @RequestParam("pageSize") int pageSize) {
        return activityApiFeignClient.getWaveListByCondition(status, current, pageSize);
    }

    @ApiOperation("随机获取n个同地区补贴案例")
    @PostMapping("change-key-area-type")
    @RequiresPermission("activity:change-key-area-type")
    public Response<Void> changeKeyAreaType(@RequestParam("caseId") int caseId,
                                            @RequestParam("areaType") ActivityKeyAreaTypeEnum activityKeyAreaTypeEnum) {
        return activityApiFeignClient.changeKeyAreaType(caseId, activityKeyAreaTypeEnum.getCode());
    }

    @ApiOperation("根据城市获取对应医院")
    @PostMapping("get-hospital-by-city")
    @RequiresPermission("activity:get-hospital-by-city")
    public Response<PageReturnModel<CfCrmStandardHospitalSimpleModel>> getHospitalByCity(
            @RequestParam(value = "hospitalKey", required = false) String hospitalKey,
            @RequestParam("city") String city,
            @RequestParam("current") int current,
            @RequestParam("pageSize") int pageSize) {
        return crmHospitalFeignClient.pageMainStandard(hospitalKey, city, current, pageSize);
    }


    @NotNull
    private Response<Void> updateStatus(@RequestParam("activityId") long activityId, @RequestParam("caseId") int caseId, @RequestParam("targetStatus") int targetStatus, int adminUserId) {
        AuthRpcResponse<String> misResponse = seaAccountClientV1.getNameByUserId(adminUserId);
        String adminUserName = "";
        if (misResponse != null && misResponse.isSuccess()) {
            adminUserName = misResponse.getResult();
        }

        RpcResult<Void> voidRpcResult = caseCountFeignClient.updateCaseStatus(activityId, caseId, targetStatus, adminUserId, StringUtils.trimToEmpty(adminUserName));
        return mapResponse(voidRpcResult);
    }

    private OperationResult<AnchorPageVO<ActivityCaseAssembleAdminVO>> mapCase(OperationResult<AnchorPageVO<ActivityCaseAdminVO>> caseListByCondition) {
        return map(caseListByCondition, caseConvertor);
    }

    private <Source, Target> OperationResult<AnchorPageVO<Target>> map(OperationResult<AnchorPageVO<Source>> sourceResult, Convertor<Source, Target> convertor) {
        if (sourceResult == null || sourceResult.isFail()) {
            return OperationResult.fail(BaseErrorCodeEnum.FALL_BACK);
        }
        AnchorPageVO<Source> data = sourceResult.getData();
        AnchorPageVO<Target> targetData = new AnchorPageVO<>();
        targetData.setHasNext(data.isHasNext());
        targetData.setNextAnchor(data.getNextAnchor());
        targetData.setPreAnchor(data.getPreAnchor());
        targetData.setSize(data.getSize());

        List<Source> sourceList = data.getList();
        if (CollectionUtils.isEmpty(sourceList)) {
            targetData.setList(Lists.newArrayList());
            return OperationResult.success(targetData);
        }
        List<Target> targetList = sourceList.stream().map(convertor::map).collect(Collectors.toList());
        targetData.setList(targetList);
        return OperationResult.success(targetData);
    }

    @NotNull
    private <T> Response<T> mapResponse(RpcResult<T> result) {
        return NewResponseUtil.makeResponse(result.getCode(), result.getMsg(), result.getData());
    }

    @Data
    public class CaseSubsidyInfoVO {

        @ApiModelProperty("案例活动状态")
        private CaseSubsidyStatusEnum status;

        @ApiModelProperty("案例活动详情")
        private ActivityDetail detail;

    }

    interface Convertor<Source, Target> {
        Target map(Source source);
    }


}
