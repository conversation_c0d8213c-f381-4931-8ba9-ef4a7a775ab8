package com.shuidihuzhu.cf.admin.configuration;

import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.util.redisson.RedissonHandlerWrapper;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/12/12.
 */
@Configuration
public class RedissonConfiguration {

    @Bean("cfRedissonHandler")
    @ConfigurationProperties("redisson-handler.cf-admin-api-cf-redisson-handler.cf-admin-api")
    public RedissonHandler cfRedissonHandler() {
        return new RedissonHandlerWrapper();
    }

    @Bean("cf2RedissonHandler")
    @ConfigurationProperties("redisson-handler.cf-admin-api-cf2-redisson-handler.cf-admin-api")
    public RedissonHandler cf2RedissonHandler() {
        return new RedissonHandlerWrapper();
    }

    @Bean("cfCoreRedissonHandler")//专供支付回调
    @ConfigurationProperties("redisson-handler.cf-admin-api-cf-core-redisson-handler.cf-admin-api")
    public RedissonHandler cfCoreRedissonHandler() {
        return new RedissonHandlerWrapper();
    }

}
