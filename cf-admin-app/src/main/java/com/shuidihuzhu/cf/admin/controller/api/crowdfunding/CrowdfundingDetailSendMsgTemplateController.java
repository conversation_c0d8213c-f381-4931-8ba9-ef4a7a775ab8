package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingDetailSendMsgTemplateBiz;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingMsgContent;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingMsgTemplateVo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by niejiangnan on 2017/7/19.
 */
@Controller
@RequestMapping(path = "/admin/crowdfunding/message/")
@RestController
public class CrowdfundingDetailSendMsgTemplateController {

    private static final Logger LOGGER = LoggerFactory.getLogger(CrowdfundingDetailSendMsgTemplateController.class);

    @Autowired
    private AdminCrowdfundingDetailSendMsgTemplateBiz crowdfundingDetailSendMsgTemplateBiz;


    //读取模板
    @RequiresPermission("message:get-msg-template")
    @RequestMapping(path = "get-msg-template",method = RequestMethod.POST,produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response getMsgTemplate() {
        LOGGER.info("CrowdfundingDetailSendMsgTemplateController getMsgTemplate ");
        List<CrowdfundingMsgContent> sendMsgTemplatelist=crowdfundingDetailSendMsgTemplateBiz.getAllMsgTemplate();
        List<CrowdfundingMsgTemplateVo> sendMSgTemplateVolist= new ArrayList<>();
        sendMSgTemplateVolist=crowdfundingDetailSendMsgTemplateBiz.setVolist(sendMSgTemplateVolist,sendMsgTemplatelist);
        return NewResponseUtil.makeSuccess(sendMSgTemplateVolist);
    }

    //更新模板
    @RequiresPermission("message:update-msg-template")
    @RequestMapping(path = "update-msg-template",method = RequestMethod.POST,produces = "application/json;charset=UTF-8")
    @ResponseBody
    public  Response updateMsgTemplate(String key,String title,String content) {
        LOGGER.info("CrowdfundingDetailSendMsgTemplateController updateMsgTemplate title={}, content={}, key={}, ",
                title,content,key);
       if( StringUtils.isEmpty(key) ||StringUtils.isEmpty(content)||StringUtils.isEmpty(title)) {
           return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
       }
       CrowdfundingMsgContent crowdfundingMsgContent=crowdfundingDetailSendMsgTemplateBiz.getByKey(key);
       if(crowdfundingMsgContent==null) {
           return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
       }
       crowdfundingMsgContent.setContent(content);
       crowdfundingMsgContent.setName(title);
       crowdfundingDetailSendMsgTemplateBiz.updateMsgTemplate(crowdfundingMsgContent);
       return  NewResponseUtil.makeSuccess(null);
    }


    // 新版的短信服务
    @PostMapping("/sms/select-auth")
    @RequiresPermission("message:select-auth")
    public Response selectUserAuth(@RequestParam("authType") int authType) {
        return  ResponseUtil.makeSuccess(crowdfundingDetailSendMsgTemplateBiz
                .getAllSmsAuthGroup(ContextUtil.getAdminUserId(),  authType));
    }

    // 通过modelnum 查找短信模版
    @PostMapping("/sms/select-by-modelnum")
    @RequiresPermission("message:select-by-modelnum")
    public Response selectSmsByModelNum(@RequestParam("modelNum") String modelNum) {

        return  ResponseUtil.makeSuccess(crowdfundingDetailSendMsgTemplateBiz
                .selectTemplateByModelNum(modelNum));
    }

    @PostMapping("/sms/add-template")
    @RequiresPermission("message:add-template")
    public Response addSmsTemplate(@RequestParam("smsGroup") int smsGroup,
                                   @RequestParam("modelNum") String modelNum) {

        return ResponseUtil.makeSuccess(crowdfundingDetailSendMsgTemplateBiz.addSmsTemplate(smsGroup,
                modelNum,
                ContextUtil.getAdminUserId()));
    }

    @PostMapping("/sms/enable-or-disable")
    @RequiresPermission("message:enable-or-disable")
    public Response enableOrDisable(@RequestParam("id") int id,
                                    @RequestParam("dataStatus") int dataStatus) {
        return ResponseUtil.makeSuccess(crowdfundingDetailSendMsgTemplateBiz.updateStatusById(id, dataStatus, ContextUtil.getAdminUserId()
                ));
    }

    @PostMapping("/sms/change-sort")
    @RequiresPermission("message:change-sort")
    public Response changeSmsSort(@RequestParam("upId") int upId,
                                  @RequestParam("downId") int downId,
                                  @RequestParam("operateType") int operateType) {
        return  ResponseUtil.makeSuccess(crowdfundingDetailSendMsgTemplateBiz
                .updateSmsTemplatePriority(upId, downId, operateType,  ContextUtil.getAdminUserId()));
    }

    @PostMapping("/sms/select-template-list")
    @RequiresPermission("message:select-template-list")
    public Response selectTemplateList(@RequestParam("smsGroup") int smsGroup,
                                       @RequestParam("templateTitle") String templateTitle,
                                       @RequestParam(value = "operatorId", required = false, defaultValue = "0") Integer operatorId,
                                       @RequestParam(value = "dataStatus", required = false, defaultValue = "0") Integer dataStatus) {
        return ResponseUtil.makeSuccess(crowdfundingDetailSendMsgTemplateBiz
                .selectTemplateByParam(smsGroup, templateTitle, operatorId, dataStatus));
    }

    @PostMapping("/sms/select-content-list")
    @RequiresPermission("message:select-content-list")
    public Response selectContentList() {
        return ResponseUtil.makeSuccess(crowdfundingDetailSendMsgTemplateBiz.selectAuthSmsContent(ContextUtil.getAdminUserId()));
    }

    @PostMapping("/sms/select-operate-list")
    @RequiresPermission("message:select-operate-list")
    public Response selectOperateList(@RequestParam("id") int id) {
        return ResponseUtil.makeSuccess(crowdfundingDetailSendMsgTemplateBiz.selectRecordByTemplateId(id));
    }
}
