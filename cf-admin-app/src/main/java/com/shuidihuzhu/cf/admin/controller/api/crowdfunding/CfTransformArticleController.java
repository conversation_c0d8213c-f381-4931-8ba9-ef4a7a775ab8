package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.other.IOtherDelegate;
import com.shuidihuzhu.common.web.model.Response;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Author   : roy
 * Date     : 2018-05-05  16:13
 * Describe : 转化内容文章
 *
 * <AUTHOR>
 * @link https://wiki.shuidihuzhu.com/pages/viewpage.action?pageId=12392538
 * @link http://git.shuiditech.com/cf/cf-admin/issues/1
 */
@RestController
@RequestMapping(path = "/admin/cf/article")
public class CfTransformArticleController {
    private static final Logger LOGGER = LoggerFactory.getLogger(CfTransformArticleController.class);
    private static final String DEFAULT_PAGE_SIZE = "10";

    @Autowired
    private IOtherDelegate otherDelegate;

    @RequestMapping(path = "add", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("cf-article:add")
    public Response add(@RequestParam String imgUrl,
                        @RequestParam String title,
                        @RequestParam String url) {
        LOGGER.info("add imgUrl:{}; title:{} url:{}", imgUrl, title, url);
        return otherDelegate.insertCfTransformArticle(title, url, imgUrl);
    }

    @RequestMapping(path = "delete", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("cf-article:delete")
    public Response delete(@RequestParam long id) {
        LOGGER.info("delete id:{}", id);
        return otherDelegate.deleteCfTransformArticle(id);
    }

    @RequestMapping(path = "get-anchor-list", method = RequestMethod.POST, produces =
            "application/json;charset=UTF-8")
    @RequiresPermission("cf-article:get-anchor-list")
    public Response getList(@RequestParam(required = false, defaultValue = "0")
                                    int anchorId,
                            @RequestParam(required = false, defaultValue = DEFAULT_PAGE_SIZE)
                                    int size) {
        LOGGER.info("get-list anchorId:{}, size:{}", anchorId, size);
        return otherDelegate.listCfTransformArticleByAnchor(anchorId, size);
    }

    @RequestMapping(path = "get-list", method = RequestMethod.POST, produces =
            "application/json;charset=UTF-8")
    @RequiresPermission("cf-article:getlist")
    public Response getPageList(
            @ApiParam("页码")
            @RequestParam(required = false, defaultValue = "1")
                    Integer current,
            @ApiParam("每页数组最大容量")
            @RequestParam(required = false, defaultValue = DEFAULT_PAGE_SIZE)
                    Integer pageSize,
            @ApiParam("搜索关键字")
            @RequestParam(required = false)
                    String titleKey
    ) {
        LOGGER.info("get-page-list current:{}, pageSize:{}, titleKey:{}", current,
                pageSize, titleKey);
        return otherDelegate.listByPageAndTitleKey(current, pageSize, titleKey);
    }
}
