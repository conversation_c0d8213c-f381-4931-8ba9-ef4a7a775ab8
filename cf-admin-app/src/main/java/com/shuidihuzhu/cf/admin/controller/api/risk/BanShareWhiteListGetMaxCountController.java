package com.shuidihuzhu.cf.admin.controller.api.risk;

import com.shuidihuzhu.cf.client.adminpure.feign.BanShareWhiteListFeignClient;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.service.BanShareWhiteListService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
public class BanShareWhiteListGetMaxCountController implements BanShareWhiteListFeignClient {
    @Autowired
    BanShareWhiteListService banShareWhiteListService;

    @Override
    public OperationResult<Integer> getMaxCount(long cfUserId) {

        Integer maxCount = banShareWhiteListService.getMaxCount(cfUserId);
        return OperationResult.success(maxCount);
    }

    @Override
    public OperationResult<Void> updateState() {
        banShareWhiteListService.updateState();
        return null;
    }
}
