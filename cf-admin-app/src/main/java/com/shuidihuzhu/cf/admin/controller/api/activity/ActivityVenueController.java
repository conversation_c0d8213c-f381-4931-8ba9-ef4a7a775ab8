package com.shuidihuzhu.cf.admin.controller.api.activity;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.admin.util.ReadExcelUtil;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.charity.client.api.ActivityVenueClient;
import com.shuidihuzhu.charity.client.api.ActivityVenueManageFeignClient;
import com.shuidihuzhu.charity.client.enums.activity.Activity111CaseTypeEnum;
import com.shuidihuzhu.charity.client.enums.activity.ActivityCaseType;
import com.shuidihuzhu.charity.client.model.activity.ActivityVenueCaseModel;
import com.shuidihuzhu.charity.client.vo.activity.ActivityVenueCaseModeListVo;
import com.shuidihuzhu.charity.client.vo.activity.ActivityVenueStatusVo;
import com.shuidihuzhu.client.pf.rpc.client.v1.FundraisingInfoClient;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.pf.model.FundraisingInfoRpc;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.stream.Collectors;

@Api("小善日后台")
@RestController
@Slf4j
@RequestMapping("admin/cf/activity/venue")
public class ActivityVenueController {

    @Autowired
    private ActivityVenueManageFeignClient activityVenueManageFeignClient;

    @Autowired
    private AdminCfInfoExtBiz adminCfInfoExtBiz;

    @Autowired
    private ActivityVenueClient activityVenueClient;

    @Autowired
    private FundraisingInfoClient fundraisingInfoClient;

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler cfRedissonHandler;

    private static final String DOWN_FILE_KEY= "fail-list-venue-key";

    @ApiOperation("获取主会场后台分类枚举")
    @PostMapping("get-case-type-enum")
    @RequiresPermission("venue:get-case-type-enum")
    public  Response<List<Pair>> getCaseTypeEnum(){
        List<Pair> list = Arrays.stream(ActivityCaseType.values())
                .map(v -> new Pair(v.getCode(), v.getDesc()))
                .collect(Collectors.toList());
        return NewResponseUtil.makeSuccess(list);
    }

    @ApiOperation("根据案例id查询")
    @PostMapping("get-by-case-id")
    @RequiresPermission("venue:get-case-type-enum")
    public  Response<ActivityVenueCaseModel> getByCaseId(@RequestParam int activityId, @RequestParam("caseId") String id){
        Response<ActivityVenueStatusVo> venueStatusResp = activityVenueClient.getVenueStatus(activityId);
        if (venueStatusResp.notOk()) {
            return NewResponseUtil.makeFail("活动id请求失败");
        }
        int caseId = 0;
        ActivityVenueStatusVo venueStatus = venueStatusResp.getData();
        Activity111CaseTypeEnum typeEnum = venueStatus.getTypeEnum();
        if (typeEnum == Activity111CaseTypeEnum.PF) {
            List<String> pfInfoIds = Lists.newArrayList(id);
            Map<String, Long> pfInfoIdCaseIdMap = Optional.ofNullable(fundraisingInfoClient.getCaseInfos(pfInfoIds))
                    .stream()
                    .flatMap(Collection::stream)
                    .collect(Collectors.toMap(FundraisingInfoRpc::getInfoNum, FundraisingInfoRpc::getInfoId));
            caseId = Math.toIntExact(pfInfoIdCaseIdMap.getOrDefault(id, 0L));//已检查过
        } else {
            caseId = Integer.parseInt(id);//已检查过
        }

        return activityVenueManageFeignClient.getByCaseId(caseId, activityId);
    }

    @ApiOperation("修改")
    @PostMapping("update-by-id")
    @RequiresPermission("venue:update-by-id")
    public  Response<Void> updateById(@RequestBody ActivityVenueCaseModel data){
        int caseId = data.getCaseId();
        int poolType = data.getPoolType();
        int activityId = data.getActivityId();
        // 上线 检查初审状态
        if (poolType == 3) {
            Response<ActivityVenueStatusVo> venueStatusResp = activityVenueClient.getVenueStatus(activityId);
            if (venueStatusResp.notOk()) {
                return NewResponseUtil.makeFail("活动id请求失败");
            }
            ActivityVenueStatusVo venueStatus = venueStatusResp.getData();
            Activity111CaseTypeEnum typeEnum = venueStatus.getTypeEnum();
            if (typeEnum == Activity111CaseTypeEnum.CF) {
                CfInfoExt ext = adminCfInfoExtBiz.getByCaseId(caseId);
                int firstApproveStatus = ext.getFirstApproveStatus();
                if (FirstApproveStatusEnum.isNotPassed(FirstApproveStatusEnum.parse(firstApproveStatus))) {
                    return NewResponseUtil.makeFail("初审未通过 不允许上线");
                }
            }
            if (isParamNotPass(data, true)) {
                return NewResponseUtil.makeFail("上线案例必须填写全部内容");
            }
        }
        return activityVenueManageFeignClient.updateById(data);
    }

    @ApiOperation("上传")
    @PostMapping("create")
    @RequiresPermission("venue:create")
    public Response<ActivityVenueCaseModel> create(@RequestBody ActivityVenueCaseModel data){
        Response<ActivityVenueStatusVo> venueStatusResp = activityVenueClient.getVenueStatus(data.getActivityId());
        if (venueStatusResp.notOk()) {
            return NewResponseUtil.makeFail("活动id请求失败");
        }
        ActivityVenueStatusVo venueStatus = venueStatusResp.getData();
        Activity111CaseTypeEnum typeEnum = venueStatus.getTypeEnum();

        Map<String, Long> pfInfoIdCaseIdMap = null;
        if (typeEnum == Activity111CaseTypeEnum.PF) {
            List<String> pfInfoIds = Lists.newArrayList(data.getInfoId());
            pfInfoIdCaseIdMap = Optional.ofNullable(fundraisingInfoClient.getCaseInfos(pfInfoIds))
                    .stream()
                    .flatMap(Collection::stream)
                    .collect(Collectors.toMap(FundraisingInfoRpc::getInfoNum, FundraisingInfoRpc::getInfoId));
        }

        return createNew(data, typeEnum, pfInfoIdCaseIdMap);
    }

    @ApiOperation(value = "列表", notes = "不传caseTypes为查询所有")
    @PostMapping("list-by-case-type")
    @RequiresPermission("venue:list-by-case-type")
    public Response<ActivityVenueCaseModeListVo> listByCaseType(
            @RequestParam int activityId,
            @RequestParam("caseTypes") List<Integer> caseTypes,
            @RequestParam("current") long current,
            @RequestParam("pageSize") int pageSize){
        if (caseTypes == null) {
            caseTypes = Lists.newArrayList();
        }
        return activityVenueManageFeignClient.listByCaseType(activityId, caseTypes, current, pageSize);
    }

    @RequestMapping(path = "/upload")
    @ResponseBody
    @RequiresPermission("venue:upload")
    public Response<Void> upload(@RequestParam(value="file") MultipartFile file, @RequestParam int activityId, HttpServletResponse response) throws Exception {
        return uploadByActivityId(file, activityId, response);
    }

    @NotNull
    private Response<Void> uploadByActivityId(MultipartFile file, int activityId, HttpServletResponse response) throws Exception {
        if (file.isEmpty()){
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        Response<ActivityVenueStatusVo> venueStatusResp = activityVenueClient.getVenueStatus(activityId);
        if (venueStatusResp.notOk()) {
            return NewResponseUtil.makeFail("活动id请求失败");
        }
        ActivityVenueStatusVo venueStatus = venueStatusResp.getData();
        Activity111CaseTypeEnum typeEnum = venueStatus.getTypeEnum();

        Map<String,String> sheet1TitleMap = Maps.newHashMapWithExpectedSize(3);
        if (typeEnum == Activity111CaseTypeEnum.CF) {
            sheet1TitleMap.put("案例id","caseId");
        } else if (typeEnum == Activity111CaseTypeEnum.PF){
            sheet1TitleMap.put("案例id","infoId");
        }
        sheet1TitleMap.put("标题","title");
        sheet1TitleMap.put("简介","content");
        sheet1TitleMap.put("缩略图url","titleImg");
        sheet1TitleMap.put("主会场分类","type");
        sheet1TitleMap.put("主会场标签","desc");
        sheet1TitleMap.put("优先级","order");
        sheet1TitleMap.put("地区","caseLabel");
        sheet1TitleMap.put("活动id","activityId");
        List<ActivityVenueCaseModel> list = (List<ActivityVenueCaseModel>) ReadExcelUtil.parseExcel(file.getInputStream(),
                file.getOriginalFilename(), sheet1TitleMap, 1, ActivityVenueCaseModel.class);

        Map<String, Long> pfInfoIdCaseIdMap = null;
        if (typeEnum == Activity111CaseTypeEnum.PF){
            List<String> pfInfoIds = list.stream().map(ActivityVenueCaseModel::getInfoId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            pfInfoIdCaseIdMap = Optional.ofNullable(fundraisingInfoClient.getCaseInfos(pfInfoIds))
                    .stream()
                    .flatMap(Collection::stream)
                    .collect(Collectors.toMap(FundraisingInfoRpc::getInfoNum, FundraisingInfoRpc::getInfoId));
        }

        List<ExcelFailVo> failList = Lists.newArrayListWithCapacity(list.size());
        for (ActivityVenueCaseModel l : list) {
            l.setPoolType(1);
            String beforeInfoId = l .getInfoId();
            Response<ActivityVenueCaseModel> resp = createNew(l, typeEnum, pfInfoIdCaseIdMap);
            if (resp.notOk()) {
                ExcelFailVo excelFailVo = new ExcelFailVo();
                excelFailVo.setCaseId(typeEnum == Activity111CaseTypeEnum.PF ? beforeInfoId : String.valueOf(l.getCaseId()));
                excelFailVo.setFailMsg(resp.getMsg());
                failList.add(excelFailVo);
            }
            log.info("upload single resp:{}, data:{}", JSON.toJSON(resp), l);
        }
        cfRedissonHandler.addListEX(DOWN_FILE_KEY, failList, RedissonHandler.ONE_HOUR);
        log.info("upload excel success");
        return NewResponseUtil.makeSuccess(null);
    }

    private Response<ActivityVenueCaseModel> createNew(ActivityVenueCaseModel data,
                                                       Activity111CaseTypeEnum typeEnum,
                                                       Map<String, Long> pfInfoIdCaseIdMap) {
        data.setTitle(StringUtils.trimToEmpty(data.getTitle()));
        data.setTitleImg(StringUtils.trimToEmpty(data.getTitleImg()));
        data.setContent(StringUtils.trimToEmpty(data.getContent()));
        data.setPoolType(1);
        int caseId = data.getCaseId();

        // 查询type
        if (typeEnum == null) {
            return NewResponseUtil.makeFail("typeEnum null");
        }

        // 设置公益id
        if (typeEnum == Activity111CaseTypeEnum.PF) {
            Long orDefault = pfInfoIdCaseIdMap.getOrDefault(data.getInfoId(), 0L);
            if (orDefault == 0) {
                return NewResponseUtil.makeFail("infoid不存在");
            }
            data.setCaseId(Math.toIntExact(orDefault));//已检查过
        }

        if (typeEnum == Activity111CaseTypeEnum.CF) {
            CfInfoExt ext = adminCfInfoExtBiz.getByCaseId(caseId);
            if (ext == null) {
                return NewResponseUtil.makeFail("案例id不存在");
            }
            int firstApproveStatus = ext.getFirstApproveStatus();
            if (FirstApproveStatusEnum.isNotPassed(FirstApproveStatusEnum.parse(firstApproveStatus))) {
                return NewResponseUtil.makeFail("初审未通过 不允许创建");
            }
        }

        if (isParamNotPass(data, false)) {
            return NewResponseUtil.makeFail("内容填写不全");
        }
        return activityVenueManageFeignClient.create(data);
    }

    private boolean isParamNotPass(ActivityVenueCaseModel data, boolean checkAll) {
        if (data.getCaseId() <= 0) {
            return true;
        }
        if (StringUtils.isBlank(data.getDesc())) {
            return true;
        }
        // 是否检查全部
        if (!checkAll) {
            return false;
        }
        if (StringUtils.isBlank(data.getTitle())) {
            return true;
        }
        if (StringUtils.isBlank(data.getTitleImg())) {
            return true;
        }
        if (StringUtils.isBlank(data.getContent())) {
            return true;
        }
        return false;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static class Pair{
        private int code;
        private String msg;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static class ExcelFailVo{
        private String caseId;
        private String failMsg;
    }

}
