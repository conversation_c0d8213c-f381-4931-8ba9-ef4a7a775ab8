package com.shuidihuzhu.cf.admin.controller.api.workorder;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.admin.util.AdminModulusUtil;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingOrderBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfLabelMarkBiz;
import com.shuidihuzhu.cf.client.feign.CfFriendFeignClient;
import com.shuidihuzhu.cf.client.feign.CfShareFeignClient;
import com.shuidihuzhu.cf.client.feign.CfVerificationFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.admin.JuanzhuanWorkOrderVo;
import com.shuidihuzhu.cf.model.admin.workorder.FriendsData;
import com.shuidihuzhu.cf.model.admin.workorder.FriendsDataVO;
import com.shuidihuzhu.cf.model.admin.workorder.JuanzhuanDetailVo;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.workorder.juanzhan.JuanzhuanWorkOrderService;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.api.chaiku.CfInfoShareRecordFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewTrackCallRobotClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CallRobotModel;
import com.shuidihuzhu.client.cf.workorder.CfJuanzhuanWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.JuanzhuanHandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderListParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2020/5/7
 */
@Api("1v1捐转工单")
@Slf4j
@RestController
@RequestMapping(path = "/admin/workorder/juanzhuan")
public class JuanzhuanWorkOrderController {

    private final static int MODULUS_NUM = 200;

    @Resource
    private CfJuanzhuanWorkOrderClient client;

    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Resource
    private CfWorkOrderClient cfWorkOrderClient;

    @Resource
    private JuanzhuanWorkOrderService juanzhuanWorkOrderService;
    
    @Resource
    private CfShareFeignClient cfShareFeignClient;

    @Resource
    private CfVerificationFeignClient verificationFeignClient;

    @Resource
    private CfInfoShareRecordFeignClient cfInfoShareRecordFeignClient;

    @Resource
    private CfFriendFeignClient cfFriendFeignClient;

    @Resource
    private AdminCrowdfundingOrderBiz adminCrowdfundingOrderBiz;

    @Resource
    private CfClewTrackCallRobotClient cfClewTrackCallRobotClient;

    @Resource
    private ShuidiCipher shuidiCipher;

    @Autowired
    private CfLabelMarkBiz cfLabelMarkBiz;

    private DateFormat formatStart = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
    private DateFormat formatEnd   = new SimpleDateFormat("yyyy-MM-dd 23:59:59");

    private DateFormat format      = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequiresPermission("juanzhuan:handle")
    @RequestMapping(path = "hanlde-juanzhuan", method = RequestMethod.POST)
    public Response hanldeJuanzhuan(@RequestParam("param") String param) {

        JuanzhuanHandleOrderParam handleOrderParam = JSON.parseObject(param,JuanzhuanHandleOrderParam.class);

        Response<WorkOrderVO> response = cfWorkOrderClient.getWorkOrderById(handleOrderParam.getWorkOrderId());
        WorkOrderVO workOrderVO = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);
        Set<Integer> set = HandleResultEnum.unDoResult().stream().collect(Collectors.toSet());
        if (workOrderVO == null || !set.contains(workOrderVO.getHandleResult())){
            return NewResponseUtil.makeError(AdminErrorCode.ALREADY_DOME);
        }

        if (handleOrderParam.getHandleResult() == HandleResultEnum.done.getType()){

            CfInfoStat stat = crowdfundingDelegate.getById(handleOrderParam.getCaseId());
            if (stat != null){
                handleOrderParam.setDonationCount(stat.getDonationCount()+"");
                handleOrderParam.setShareCount(stat.getShareCount()+"");
                handleOrderParam.setAmount(stat.getAmount()+"");
            }
        }

        return client.hanldeJuanzhuan(handleOrderParam);
    }

    @RequiresPermission("juanzhuan:orderlist")
    @RequestMapping(path = "juanzhuan-orderlist",method = RequestMethod.POST)
    Response juanzhuanOrderlist(@RequestParam("param") String param){

        WorkOrderListParam listParam = JSON.parseObject(param, WorkOrderListParam.class);

        Response<PageResult<WorkOrderVO>> response = client.juanzhuanOrderList(listParam);

        List<WorkOrderVO> list = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .map(PageResult::getPageList)
                .orElse(Lists.newArrayList());

        if (CollectionUtils.isEmpty(list)){
            return response;
        }
        List<Integer> ids = list.stream().map(WorkOrderVO::getCaseId).collect(Collectors.toList());
        Map<Integer, CfInfoStat> statMap = crowdfundingDelegate.mapByIds(ids);

        //封装admin 特殊需要的参
        List<JuanzhuanWorkOrderVo> orderVos = list.stream().map(r->{
            JuanzhuanWorkOrderVo vo = new JuanzhuanWorkOrderVo();
            BeanUtils.copyProperties(r, vo);

            CfInfoStat s = statMap.get(Integer.valueOf(vo.getCaseId()));
            if (s != null){
                vo.setDonationCount(s.getDonationCount());
                vo.setShareCount(s.getShareCount());
                vo.setAmount(s.getAmount());
            }

            return vo;

        }).collect(Collectors.toList());

        PageResult<JuanzhuanWorkOrderVo> result = new PageResult<>();
        result.setHasNext(response.getData().isHasNext());
        result.setPageList(orderVos);

        return NewResponseUtil.makeSuccess(result);

    }

    @RequiresPermission("juanzhuan:detail")
    @RequestMapping(path = "juanzhuan-detail",method = RequestMethod.POST)
    Response<JuanzhuanDetailVo> juanzhuanDetail(@RequestParam("workOrderId") long workOrderId,
                                                @RequestParam("caseId") int caseId){
        OpResult<JuanzhuanDetailVo> opResult = juanzhuanWorkOrderService.getJuanzhuanDetailVo(caseId,workOrderId);
        if (opResult.isFail()){
            return NewResponseUtil.makeError(opResult.getErrorCode());
        }

        return NewResponseUtil.makeSuccess(opResult.getData());

    }

    @RequiresPermission("juanzhuan:check")
    @RequestMapping(path = "juanzhuan-check", method = RequestMethod.POST)
    public Response juanzhuanCheck(@RequestParam("caseId") int caseId,
                               @RequestParam("workOrderId") long workOrderId,
                               @RequestParam("userId") long userId,
                               @RequestParam("orderType") int orderType){

        Response<WorkOrderVO> response = cfWorkOrderClient.getWorkOrderById(workOrderId);
        WorkOrderVO workOrderVO = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);
        Set<Integer> set = HandleResultEnum.unDoResult().stream().collect(Collectors.toSet());
        if (workOrderVO == null || !set.contains(workOrderVO.getHandleResult())){
            return NewResponseUtil.makeSuccess(true);
        }

        CrowdfundingInfo c  = crowdfundingDelegate.getCaseInfoById(caseId);
        //案例结束关闭
        if (c.getEndTime().before(new Date())){

            JuanzhuanHandleOrderParam j = new JuanzhuanHandleOrderParam();
            j.setWorkOrderId(workOrderId);
            j.setOrderType(orderType);
            j.setHandleResult(HandleResultEnum.exception_done.getType());
            CfInfoStat stat = crowdfundingDelegate.getById(caseId);
            if (stat != null){
                j.setDonationCount(stat.getDonationCount()+"");
                j.setShareCount(stat.getShareCount()+"");
                j.setAmount(stat.getAmount()+"");
            }
            client.hanldeJuanzhuan(j);
            return NewResponseUtil.makeSuccess(true);
        }

        return NewResponseUtil.makeSuccess(false);
    }

    @RequiresPermission("juanzhuan:call")
    @RequestMapping(path = "juanzhuan-call",method = RequestMethod.POST)
    Response<String> juanzhuanCall(@RequestParam("workOrderId") long workOrderId,
                                   @RequestParam("phone") String phone,
                                   @RequestParam("time") String time,
                                   @RequestParam("status") int status){
        phone = shuidiCipher.encrypt(phone);
        time = LocalDate.now()+" "+time;
        Response<CallRobotModel> response = cfClewTrackCallRobotClient.doCallRobotInfo(workOrderId+"",phone,time,status,0);
        log.info("juanzhuanCall workOrderId={},phone={},time={},status={} response={}",workOrderId,phone,time,status,response);

        return NewResponseUtil.makeResponse(response.getCode(),response.getMsg(),"");
    }

    @RequiresPermission("juanzhuan:friends-data")
    @RequestMapping(path = "juanzhuan-friends-data", method = RequestMethod.POST)
    public Response<FriendsDataVO> getFriendsData(@RequestParam("caseId") int caseId) throws ParseException {

        CrowdfundingInfo crowdfundingInfo = crowdfundingDelegate.getCaseInfoById(caseId);

        if(crowdfundingInfo == null){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        long raiserUserId = crowdfundingInfo.getUserId();
        FeignResponse<Set<Long>> feignResponse = cfFriendFeignClient.getUserAllFriendsUserIds(raiserUserId);
        Response<Integer> response = cfInfoShareRecordFeignClient.getCountByUseridAndInfoId(raiserUserId, caseId);

        int raiserShareCount = response.getData() == null? -1 : response.getData();

        Set<Long> userIdSet = feignResponse.getData() == null? Sets.newHashSet(): feignResponse.getData();

        int allFriendsShareUserCount = this.getShareUserCount(caseId, userIdSet,
                new Timestamp(crowdfundingInfo.getCreateTime().getTime()), new Timestamp((new Date()).getTime()));
        int allFriendsShareCount = this.getAllShareCount(caseId, userIdSet,
                new Timestamp(crowdfundingInfo.getCreateTime().getTime()), new Timestamp((new Date()).getTime()));

        int allFriendsVerifyCount = this.getVerificationAll(crowdfundingInfo.getInfoId(), userIdSet);

        //第一笔捐单
        List<CrowdfundingOrder> orders = adminCrowdfundingOrderBiz.getByPage(caseId,0,1);

        //排空指针
        if (CollectionUtils.isEmpty(orders)){
            FriendsDataVO fd = new FriendsDataVO();
            return NewResponseUtil.makeSuccess(fd);
        }

        Date d0Start = orders.get(0).getCtime();
        Date d0End   = format.parse(formatEnd.format(d0Start));

        Date d1 = DateUtils.addDays(d0Start, 1);

        Date d1Start = format.parse(formatStart.format(d1));
        Date d1End   = format.parse(formatEnd.format(d1));

        Date d2 = DateUtils.addDays(d1, 1);

        Date d2Start = format.parse(formatStart.format(d2));
        Date d2End   = format.parse(formatEnd.format(d2));

        Date d3 = DateUtils.addDays(d2, 1);

        Date d3Start = format.parse(formatStart.format(d3));
        Date d3End   = new Date();


        FriendsData raiserData = FriendsData.builder()
                .userCount(1)
                .shareCountAll(raiserShareCount)
                .shareCountD0(getShareCount(caseId, Sets.newHashSet(raiserUserId), d0Start, d0End))
                .shareCountD1(getShareCount(caseId, Sets.newHashSet(raiserUserId), d1Start, d1End))
                .shareCountD2(getShareCount(caseId, Sets.newHashSet(raiserUserId), d2Start, d2End))
                .shareCountD3(getShareCount(caseId, Sets.newHashSet(raiserUserId), d3Start, d3End))
                .shareUserCountD0(getShareUserCount(caseId, Sets.newHashSet(raiserUserId), d0Start, d0End))
                .shareUserCountD1(getShareUserCount(caseId, Sets.newHashSet(raiserUserId), d1Start, d1End))
                .shareUserCountD2(getShareUserCount(caseId, Sets.newHashSet(raiserUserId), d2Start, d2End))
                .shareUserCountD3(getShareUserCount(caseId, Sets.newHashSet(raiserUserId), d3Start, d3End))
                .verificationCountAll(0)
                .verificationCountD0(0)
                .verificationCountD1(0)
                .verificationCountD2(0)
                .verificationCountD3(0)
                .build();

        FriendsData friendsData = FriendsData.builder()
                .userCount(allFriendsShareUserCount)
                .shareCountAll(allFriendsShareCount)
                .shareCountD0(getShareCount(caseId, userIdSet, d0Start, d0End))
                .shareCountD1(getShareCount(caseId, userIdSet, d1Start, d1End))
                .shareCountD2(getShareCount(caseId, userIdSet, d2Start, d2End))
                .shareCountD3(getShareCount(caseId, userIdSet, d3Start, d3End))
                .shareUserCountD0(getShareUserCount(caseId, userIdSet, d0Start, d0End))
                .shareUserCountD1(getShareUserCount(caseId, userIdSet, d1Start, d1End))
                .shareUserCountD2(getShareUserCount(caseId, userIdSet, d2Start, d2End))
                .shareUserCountD3(getShareUserCount(caseId, userIdSet, d3Start, d3End))
                .verificationCountAll(allFriendsVerifyCount)
                .verificationCountD0(getVerifiction(crowdfundingInfo.getInfoId(), userIdSet, d0Start, d0End))
                .verificationCountD1(getVerifiction(crowdfundingInfo.getInfoId(), userIdSet, d1Start, d1End))
                .verificationCountD2(getVerifiction(crowdfundingInfo.getInfoId(), userIdSet, d2Start, d2End))
                .verificationCountD3(getVerifiction(crowdfundingInfo.getInfoId(), userIdSet, d3Start, d3End))
                .build();

        FriendsDataVO friendsDataVO = FriendsDataVO.builder()
                .friendsData(friendsData)
                .raiserData(raiserData)
                .build();

        return NewResponseUtil.makeSuccess(friendsDataVO);
    }

    @RequestMapping(path = "qq-label", method = RequestMethod.POST)
    public Response<Boolean> hitQQLabel(@RequestParam("caseId") int caseId){
        return NewResponseUtil.makeSuccess(cfLabelMarkBiz.hitQQLabel(caseId));
    }

    private Map<String, List<Long>> getShardingMap(Set<Long> userIds) {
        Map<String, List<Long>> shardingMap = Maps.newHashMap();
        userIds.forEach(userId -> {
            String sharding = AdminModulusUtil.getModulusIndex(userId, MODULUS_NUM);
            if (StringUtils.isNotBlank(sharding)) {
                if (shardingMap.containsKey(sharding)) {
                    shardingMap.get(sharding).add(userId);
                } else {
                    shardingMap.put(sharding, Lists.newArrayList(userId));
                }
            }
        });
        return shardingMap;
    }

    private int getAllShareCount(int caseId, Set<Long> userIds, Date startTime, Date endTime) {

        Map<String, List<Long>> shardingMap = getShardingMap(userIds);
        if (MapUtils.isEmpty(shardingMap)) {
            return 0;
        }

        return shardingMap.keySet()
                .stream()
                .map(sharding -> cfShareFeignClient
                        .selectByUserIdsAndCaseId(caseId, Sets.newHashSet(shardingMap.get(sharding)), new Timestamp(startTime.getTime()), new Timestamp(endTime.getTime())))
                .map(Response::getData)
                .filter(CollectionUtils::isNotEmpty)
                .mapToInt(Collection::size).sum();
    }

    private int getShareCount(int caseId, Set<Long> userIds, Date startTime, Date endTime){

        Map<String, List<Long>> shardingMap = this.getShardingMap(userIds);
        if (MapUtils.isEmpty(shardingMap)) {
            return 0;
        }

        return shardingMap.keySet()
                .stream()
                .map(sharding -> cfShareFeignClient
                    .countByUserIdsAndCaseId(caseId, Sets.newHashSet(shardingMap.get(sharding)), new Timestamp(startTime.getTime()), new Timestamp(endTime.getTime())))
                .filter(cfInfoShareRecords -> cfInfoShareRecords.getData() != null)
                .mapToInt(Response::getData).sum();
    }

    private int getShareUserCount(int caseId, Set<Long> userIds, Date startTime, Date endTime){

        Map<String, List<Long>> shardingMap = this.getShardingMap(userIds);
        if (MapUtils.isEmpty(shardingMap)) {
            return 0;
        }

        return shardingMap.keySet()
                .stream()
                .map(sharding -> cfShareFeignClient
                        .selectByUserIdsAndCaseId(caseId, Sets.newHashSet(shardingMap.get(sharding)),
                                new Timestamp(startTime.getTime()), new Timestamp(endTime.getTime())))
                .map(Response::getData)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .map(CfInfoShareRecord::getUserId)
                .collect(Collectors.toSet()).size();
    }

    private int getVerificationAll(String infoUuid, Set<Long> userIds) {

        List<Long> userIdList = new ArrayList<>(userIds);
        List<List<Long>> userIdLists = Lists.partition(userIdList, MODULUS_NUM);

        return userIdLists.stream()
                .map(singleList -> verificationFeignClient
                    .getFriendsVerificationAll(infoUuid, Sets.newHashSet(singleList)))
                .filter(response -> response.getData() != null && CollectionUtils.isNotEmpty(response.getData()))
                .map(Response::getData)
                .flatMap(Collection::stream)
                .collect(Collectors.toSet()).size();
    }

    private int getVerifiction(String infoUuid, Set<Long> userIds, Date startTime, Date endTime){

        List<Long> userIdList = new ArrayList<>(userIds);
        List<List<Long>> userIdLists = Lists.partition(userIdList, MODULUS_NUM);

        return userIdLists.stream()
                .map(singleList -> verificationFeignClient.getFriendsVerificationByTime(infoUuid, Sets.newHashSet(singleList),
                            new Timestamp(startTime.getTime()), new Timestamp(endTime.getTime())))
                .filter(response -> response.getData() != null && CollectionUtils.isNotEmpty(response.getData()))
                .mapToInt(response -> CollectionUtils.size(response.getData())).sum();
    }

}
