package com.shuidihuzhu.cf.admin.controller.api.innerapi;

import com.shuidihuzhu.cf.finance.client.model.CaseInteractionVo;
import com.shuidihuzhu.cf.service.approve.impl.CaseInteractionServiceImpl;
import com.shuidihuzhu.client.cf.admin.client.CfCaseInteractionFeignClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2024/12/2 3:10 PM
 */
@Slf4j
@RestController
public class CfCaseInteractionFeignController implements CfCaseInteractionFeignClient {

    @Resource
    private CaseInteractionServiceImpl caseInteractionService;

    @Override
    public Response<List<CaseInteractionVo>> queryCaseInteration(int caseId) {
        return NewResponseUtil.makeSuccess(caseInteractionService.getCaseInteractionList(caseId));
    }
}
