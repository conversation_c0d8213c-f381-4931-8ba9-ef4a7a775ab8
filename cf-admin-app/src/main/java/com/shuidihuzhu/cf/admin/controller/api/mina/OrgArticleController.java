package com.shuidihuzhu.cf.admin.controller.api.mina;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.admin.convert.OrgArticleVoConvert;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.vo.frame.OrgArticleVo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.frame.client.grpc.OrgArticleGrpcClient;
import com.shuidihuzhu.frame.client.model.enums.ResultEnum;
import com.shuidihuzhu.frame.client.service.mina.orgarticle.AddOrUpdateRequest;
import com.shuidihuzhu.frame.client.service.mina.orgarticle.AddOrUpdateResponse;
import com.shuidihuzhu.frame.client.service.mina.orgarticle.DeleteRequest;
import com.shuidihuzhu.frame.client.service.mina.orgarticle.DeleteResponse;
import com.shuidihuzhu.frame.client.service.mina.orgarticle.GetDetailRequest;
import com.shuidihuzhu.frame.client.service.mina.orgarticle.GetDetailResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * Created by wuxinlong on 17/11/23.
 */
@Controller
@RequestMapping(path = "/admin/cf/content/org/article")
public class OrgArticleController {
	private static final Logger LOGGER = LoggerFactory.getLogger(OrgArticleController.class);

	@Autowired
	private OrgArticleGrpcClient orgArticleGrpcClient;


	@RequiresPermission("org-article:add-or-update")
	@RequestMapping(path = "add-or-update", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	@ResponseBody
	public Object addOrUpdate(String data, @RequestParam(value = "tagIds", required = false) String tagIds,
			@RequestParam(value = "clean", required = false, defaultValue = "false") Boolean clean) {
		OrgArticleVo orgArticleVo = null;
		try {
			orgArticleVo = JSON.parseObject(data, OrgArticleVo.class);//已检查过
		} catch (Exception e) {
			LOGGER.error("OrgArticle parse error:", e);
			return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
		}

		if (orgArticleVo == null || StringUtils.isEmpty(orgArticleVo.getTitle())
				|| StringUtils.isEmpty(orgArticleVo.getBody())) {
			LOGGER.error("参数错误");
			return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
		}

		AddOrUpdateRequest request = AddOrUpdateRequest.newBuilder()
				.setOrgArticle(OrgArticleVoConvert.getInstance().convertToPB(orgArticleVo))
				.setClean(clean)
				.setTagIds(tagIds)
				.build();

		AddOrUpdateResponse response = this.orgArticleGrpcClient.addOrUpdate(request);

		return NewResponseUtil.makeSuccess(OrgArticleVoConvert.getInstance().convertForPB(response.getOrgAritcle()));
	}

	@RequiresPermission("org-article:detail")
	@RequestMapping(path = "detail", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	@ResponseBody
	public Object getDetail(@RequestParam("id")Long id){
		GetDetailRequest request = GetDetailRequest.newBuilder().setId(id).build();
		GetDetailResponse response = this.orgArticleGrpcClient.getDetail(request);
		if (response.getResultEnum() != ResultEnum.SUCCESS){
			return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
		}
		return NewResponseUtil.makeSuccess(OrgArticleVoConvert.getInstance().convertForPB(response.getOrgAritcle()));
	}

	@RequiresPermission("org-article:delete")
	@RequestMapping(path = "delete", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	@ResponseBody
	public Response delete(@RequestParam("id")Long id){
		DeleteRequest request = DeleteRequest.newBuilder().setId(id).build();
		DeleteResponse response = this.orgArticleGrpcClient.delete(request);
		if (response.getResultEnum() != ResultEnum.SUCCESS){
			return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
		}
		return NewResponseUtil.makeSuccess(CfErrorCode.SUCCESS);
	}

}
