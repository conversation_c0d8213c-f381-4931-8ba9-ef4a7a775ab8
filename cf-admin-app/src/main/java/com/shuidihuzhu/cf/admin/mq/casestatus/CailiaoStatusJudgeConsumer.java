package com.shuidihuzhu.cf.admin.mq.casestatus;

import com.shuidihuzhu.cf.admin.mq.common.BaseMessageConsumer;
import com.shuidihuzhu.cf.biz.admin.UserCommentBiz;
import com.shuidihuzhu.cf.constants.AdminUserIDConstants;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.service.workorder.initialAudit.ai.AiRuleJudgeService;
import com.shuidihuzhu.cf.enums.InitialAudit.InitialAuditNoSmartReason;
import com.shuidihuzhu.cf.enums.admin.UserCommentSourceEnum;
import com.shuidihuzhu.cf.model.admin.UserComment;
import com.shuidihuzhu.cf.model.crowdfunding.ai.CfAiMaterials;
import com.shuidihuzhu.cf.param.InitialAuditCreateOrderParam;
import com.shuidihuzhu.cf.service.workorder.CfAiMaterialsService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditCreateOrder;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditOperateService;
import com.shuidihuzhu.cf.vo.crowdfunding.InitialAuditSmartRejectVo;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.ChuciWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.model.event.WorkOrderResultChangeEvent;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @DATE 2020/12/18
 */


@Service
@Slf4j
@RocketMQListener(id = "cailiao_" + CfClientMQTagCons.WORK_ORDER_STATUS_CHANGE,
        tags = CfClientMQTagCons.WORK_ORDER_STATUS_CHANGE,
        group = CfClientMQTagCons.WORK_ORDER_STATUS_CHANGE + "_cailiao_group",
        topic = MQTopicCons.CF)
public class CailiaoStatusJudgeConsumer extends BaseMessageConsumer<WorkOrderResultChangeEvent>
        implements MessageListener<WorkOrderResultChangeEvent> {

    @Autowired
    private AiRuleJudgeService aiRuleJudgeService;

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;
    @Resource
    private InitialAuditOperateService initialAuditOperateService;
    @Resource
    private CfAiMaterialsService cfAiMaterialsService;
    @Resource
    private InitialAuditCreateOrder initialAuditCreateOrder;
    @Resource
    private UserCommentBiz commentBiz;


    @Override
    protected boolean handle(ConsumerMessage<WorkOrderResultChangeEvent> consumerMessage) {

        log.info("CailiaoStatusJudgeConsumer is start {}", consumerMessage);

        WorkOrderResultChangeEvent event = consumerMessage.getPayload();

        if (event.getOrderType() != WorkOrderType.ai_content.getType() && event.getOrderType() != WorkOrderType.ai_photo.getType()) {
            return true;
        }
        if (event.getHandleResult() != HandleResultEnum.done.getType()) {
            return true;
        }
        Response<WorkOrderVO> response = cfWorkOrderClient.getWorkOrderById(event.getWorkOrderId());
        WorkOrderVO vo = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);
        if (Objects.isNull(vo)) {
            return true;
        }
        int orderType = WorkOrderType.ai_content.getType();
        if (vo.getOrderType() == orderType) {
            orderType = WorkOrderType.ai_photo.getType();
        }
        Response<WorkOrderVO> lastWorkOrder = cfWorkOrderClient.getLastWorkOrder(vo.getCaseId(), orderType);
        WorkOrderVO workOrderVO = Optional.ofNullable(lastWorkOrder)
                .filter(Response::ok)
                .map(Response::getData)
                .filter(r -> r.getHandleResult() == HandleResultEnum.done.getType())
                .orElse(null);

        if (Objects.isNull(workOrderVO)) {
            return true;
        }
        boolean autoEnterHuman = autoEnterHuman(workOrderVO);
        if (autoEnterHuman) {
            log.info("CailiaoStatusJudgeConsumer autoEnterHuman {}", workOrderVO);
            return true;
        }
        aiRuleJudgeService.Judge(vo.getWorkOrderId(), workOrderVO.getWorkOrderId(), vo.getCaseId());
        return true;
    }

    private boolean autoEnterHuman(WorkOrderVO vo) {
        int caseId = vo.getCaseId();
        log.info("CailiaoStatusJudgeConsumer autoEnterHuman begin {}", vo);
        CfAiMaterials aiMaterials = cfAiMaterialsService.getByCaseId(caseId, CfAiMaterials.tType);
        if (Objects.isNull(aiMaterials)) {
            return false;
        }
        InitialAuditSmartRejectVo initialAuditSmartRejectVo = initialAuditOperateService.autoEnterHuman(Collections.singletonList(aiMaterials), true);
        if (Objects.isNull(initialAuditSmartRejectVo.getInitialAuditNoSmartReason())) {
            return false;
        }
        ChuciWorkOrder chuciWorkOrder = new ChuciWorkOrder();
        chuciWorkOrder.setCaseId(caseId);
        chuciWorkOrder.setOrderType(WorkOrderType.ai_erci.getType());
        chuciWorkOrder.setSmartWorkOrderRealCreateTime(DateUtil.formatDateTime(vo.getCreateTime()));
        InitialAuditCreateOrderParam orderParam = InitialAuditCreateOrderParam.builder()
                .condition(0)
                .caseId(caseId)
                .chuciWorkOrder(chuciWorkOrder)
                .diseaseName(initialAuditSmartRejectVo.getDiseaseName())
                .noSmartAuditReason(InitialAuditNoSmartReason.ENTER_PEOPLE_AUDIT.getMsg())
                .build();
        initialAuditCreateOrder.create(orderParam);

        UserComment comment = new UserComment();
        comment.setOperatorId(AdminUserIDConstants.SYSTEM);
        comment.setCaseId(caseId);
        comment.setCommentSource(UserCommentSourceEnum.INITIAL_AUDIT.getCode());
        comment.setCommentType(UserCommentSourceEnum.CommentType.SUBMIT_VALUE_REMARK.getCode());
        comment.setOperateMode("自动进入人工审核");
        comment.setWorkOrderId(vo.getWorkOrderId());
        comment.setComment("");
        comment.setOperateDesc(StringUtils.isNotEmpty(initialAuditSmartRejectVo.getRemarkMsg()) ? initialAuditSmartRejectVo.getRemarkMsg() : "");
        commentBiz.insert(comment);
        return true;
    }


    @Override
    protected Logger getLogger() {
        return log;
    }
}
