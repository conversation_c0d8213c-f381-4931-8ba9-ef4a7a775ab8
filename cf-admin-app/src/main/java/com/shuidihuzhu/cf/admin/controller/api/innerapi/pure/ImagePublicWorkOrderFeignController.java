package com.shuidihuzhu.cf.admin.controller.api.innerapi.pure;

import com.shuidihuzhu.cf.client.adminpure.feign.workOrder.ImagePublicWorkOrderFeignClient;
import com.shuidihuzhu.cf.client.adminpure.model.caseinfo.CfCasePublicInfo;
import com.shuidihuzhu.cf.client.adminpure.model.caseinfo.CfCasePublicInfoImageAIRecord;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.service.workorder.imagePublic.CfCasePublicInfoService;
import com.shuidihuzhu.cf.service.workorder.imagePublic.ImagePublicWorkOrderService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2021/8/21 15:50
 * @Description:
 */
@RestController
public class ImagePublicWorkOrderFeignController implements ImagePublicWorkOrderFeignClient {

    @Resource
    private CfCasePublicInfoService cfCasePublicInfoService;

    @Override
    public OperationResult<List<CfCasePublicInfo>> getImagePublicInfo(String infoUuid) {
        if (StringUtils.isEmpty(infoUuid)) {
            return OperationResult.success(new ArrayList<>());
        }
        List<CfCasePublicInfo> listByInfoUuidAndType = cfCasePublicInfoService.getListByInfoUuidAndType(infoUuid);
        return OperationResult.success(listByInfoUuidAndType);
    }
}
