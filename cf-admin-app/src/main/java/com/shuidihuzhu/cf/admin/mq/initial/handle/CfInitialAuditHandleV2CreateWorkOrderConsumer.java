package com.shuidihuzhu.cf.admin.mq.initial.handle;

import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.service.workorder.initialAudit.CfInitialAuditHandleV2ConsumerService;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditOperationMqVO;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Author: wangpeng
 * @Date: 2022/6/14 17:25
 * @Description:
 */
@Service
@RocketMQListener(id = "handle-create-work-" + MQTagCons.CF_INITIAL_AUDIT_HANDLE_V2,
        group = "handle-create-work-" + MQTagCons.CF_INITIAL_AUDIT_HANDLE_V2,
        tags = MQTagCons.CF_INITIAL_AUDIT_HANDLE_V2,
        topic = MQTopicCons.CF)
@Slf4j
public class CfInitialAuditHandleV2CreateWorkOrderConsumer implements MessageListener<InitialAuditOperationMqVO> {

    @Resource
    private CfInitialAuditHandleV2ConsumerService cfInitialAuditHandleV2ConsumerService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<InitialAuditOperationMqVO> mqMessage) {
        if (Objects.isNull(mqMessage) || Objects.isNull(mqMessage.getPayload())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        log.info("CfInitialAuditHandleV2CreateWorkOrderConsumer is begin : {}", mqMessage);
        InitialAuditOperationMqVO initialAuditOperationMqVO = mqMessage.getPayload();
        try {
            cfInitialAuditHandleV2ConsumerService.canCreateMedicalWorkOrderAndTrack(initialAuditOperationMqVO);
        } catch (Exception e) {
            log.info("CfInitialAuditHandleV2CreateWorkOrderConsumer canCreateMedicalWorkOrderAndTrack error {} {}", initialAuditOperationMqVO, e);
        }

        try {
            cfInitialAuditHandleV2ConsumerService.handleAfterCreateYiYuanBuChongWork(initialAuditOperationMqVO);
        } catch (Exception e) {
            log.info("CfInitialAuditHandleV2CreateWorkOrderConsumer handleAfterCreateYiYuanBuChongWork error {} {}", initialAuditOperationMqVO, e);
        }
        try {
            cfInitialAuditHandleV2ConsumerService.saveInitialAuditSnapshot(initialAuditOperationMqVO);
        } catch (Exception e) {
            log.info("CfInitialAuditHandleV2CreateWorkOrderConsumer saveInitialAuditSnapshot error {} {}", initialAuditOperationMqVO, e);
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
