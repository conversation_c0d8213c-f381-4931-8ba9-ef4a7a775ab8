package com.shuidihuzhu.cf.admin.mq.juanzhuan;

import com.shuidihuzhu.cf.admin.mq.common.BaseMessageConsumer;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingOrderBiz;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.model.admin.workorder.JuanzhanOrderCreate;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.service.workorder.juanzhan.JuanzhuanWorkOrderService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 生成延时任务,防止主从延时
 * <AUTHOR>
 * @DATE 2020/5/8
 */
@Slf4j
@Service
@RocketMQListener(
        id = MQTagCons.juanzhuan_workorder_handle_delay,
        topic = MQTopicCons.CF,
        tags = MQTagCons.juanzhuan_workorder_handle_delay,
        group = MQTagCons.juanzhuan_workorder_handle_delay + "_group"
)
public class JuanzhuanDelayCreateConsumer extends BaseMessageConsumer<JuanzhanOrderCreate> implements MessageListener<JuanzhanOrderCreate> {

    @Autowired
    private AdminCrowdfundingOrderBiz adminCrowdfundingOrderBiz;

    @Autowired
    private JuanzhuanWorkOrderService juanzhuanWorkOrderService;

    @Override
    protected boolean handle(ConsumerMessage<JuanzhanOrderCreate> consumerMessage) {

        JuanzhanOrderCreate order = consumerMessage.getPayload();
        long orderId = order.getOrderId();
        int caseId = order.getCaseId();

        //第一笔捐单
        List<CrowdfundingOrder> orders = adminCrowdfundingOrderBiz.getByPage(caseId, 0, 1);

        if (CollectionUtils.isEmpty(orders) || orderId != orders.get(0).getId().longValue()) {
            log.info("juanzhuan caseId={} id={} first={}", caseId, orderId, CollectionUtils.isEmpty(orders) ? null : orders.get(0).getId());
            return true;
        }

        juanzhuanWorkOrderService.createD0(orderId, caseId, true);

        return true;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
