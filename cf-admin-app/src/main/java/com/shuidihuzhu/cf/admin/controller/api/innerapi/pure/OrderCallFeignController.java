package com.shuidihuzhu.cf.admin.controller.api.innerapi.pure;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.adminpure.feign.OrderCallFeignClient;
import com.shuidihuzhu.cf.client.adminpure.model.call.CfCallOutRecordMsg;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.model.admin.workorder.CfCallOutRecord;
import com.shuidihuzhu.cf.service.CfCaseWorkOrderService;
import com.shuidihuzhu.cf.service.callout.CfCallOutService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
public class OrderCallFeignController implements OrderCallFeignClient {

    @Autowired
    private CfCaseWorkOrderService cfCaseWorkOrderService;

    @Resource
    private CfCallOutService cfCallOutService;

    @Override
    public OperationResult<Integer> selectAllCallDurationByOrderHandleFinish(long workOrderId) {
        CfCallOutRecord v = cfCaseWorkOrderService.selectQcCallRecordsAll(workOrderId, 0, 0);
        return OperationResult.success(v.getTotalDuration());
    }

    @Override
    public OperationResult<Integer> selectCallDurationBySourceOrder(long workOrderId) {
        CfCallOutRecord v = cfCaseWorkOrderService.selectCallRecords(workOrderId, 0, 0);
        return OperationResult.success(v.getTotalDuration());
    }

    @Override
    public OperationResult<CfCallOutRecordMsg> selectZhuDongCallRecords(long workOrderId) {
        CfCallOutRecord v = cfCaseWorkOrderService.selectQcCallRecordsAll(workOrderId, 0, 0);
        return OperationResult.success(build(v));
    }

    @Override
    public OperationResult<CfCallOutRecordMsg> selectCallRecords(long workOrderId) {
        CfCallOutRecord v = cfCaseWorkOrderService.selectCallRecords(workOrderId, 0, 0);
        return OperationResult.success(build(v));
    }

    @Override
    public OperationResult<CfCallOutRecordMsg> selectHighRiskCallRecords(long workOrderId, long qcWorkOrderId) {
        CfCallOutRecord v = cfCallOutService.selectCallRecords(workOrderId, 0, 0, qcWorkOrderId);
        return OperationResult.success(build(v));
    }

    private CfCallOutRecordMsg build(CfCallOutRecord v) {
        CfCallOutRecordMsg cfCallOutRecordMsg = new CfCallOutRecordMsg();
        cfCallOutRecordMsg.setTotalDuration(v.getTotalDuration());
        List<CfCallOutRecord.CallOutDetail> callOutDetailList = v.getCallOutDetails();
        List<CfCallOutRecordMsg.CallOutDetail> callOutDetails = Lists.newArrayList();
        for (CfCallOutRecord.CallOutDetail callOutDetail : callOutDetailList) {
            CfCallOutRecordMsg.CallOutDetail call = new CfCallOutRecordMsg.CallOutDetail();
            call.setCallRecordId(callOutDetail.getCallRecordId());
            call.setCallTag(callOutDetail.getCallTag());
            call.setCreateTime(callOutDetail.getCreateTime());
            call.setAnswerTime(callOutDetail.getAnswerTime());
            call.setEndTime(callOutDetail.getEndTime());
            call.setDuration(callOutDetail.getDuration());
            call.setMobile(callOutDetail.getMobile());
            call.setPhoneStatus(callOutDetail.getPhoneStatus());
            call.setVideoUrl(callOutDetail.getVideoUrl());
            call.setEndReason(callOutDetail.getEndReason());
            callOutDetails.add(call);
        }
        cfCallOutRecordMsg.setCallOutDetails(callOutDetails);
        return cfCallOutRecordMsg;
    }

}
