package com.shuidihuzhu.cf.admin.controller.api.workorder;

/**
 * @Author: wangpeng
 * @Date: 2022/12/4 16:00
 * @Description:
 */

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.domain.label.risk.RiskLabelMarkParam;
import com.shuidihuzhu.cf.domain.label.risk.RiskLabelMarkRecordDO;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.admin.workorder.RiskLabelMarkWorkOrderVO;
import com.shuidihuzhu.cf.service.label.risk.RiskLabelService;
import com.shuidihuzhu.cf.service.workorder.risk.WorkOrderRiskLabelMarkService;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCoreFeignClient;
import com.shuidihuzhu.client.cf.workorder.model.HandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderListParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

@Api("风险标签标识工单")
@Slf4j
@RestController
@RequestMapping(path = "/admin/workorder/risk/label/mark")
public class RiskLabelMarkWorkOrderController {

    @Resource
    private WorkOrderRiskLabelMarkService workOrderRiskLabelMarkService;

    @Resource
    private RiskLabelService riskLabelService;

    @Resource
    private WorkOrderCoreFeignClient workOrderCoreFeignClient;

    @RequiresPermission("riskLabelMark:order-list")
    @ApiOperation("任务处理中心列表")
    @PostMapping("order-list")
    public Response<PageResult<RiskLabelMarkWorkOrderVO>> riskMarkOrderList(@RequestParam("param") String param) {
        WorkOrderListParam workOrderListParam = JSON.parseObject(param, WorkOrderListParam.class);
        return workOrderRiskLabelMarkService.riskLabelMarkWorkOrderList(workOrderListParam);
    }

    @RequiresPermission("riskLabelMark:handle")
    @ApiOperation("处理工单")
    @PostMapping("handle")
    public Response<Void> handle(@RequestBody RiskLabelMarkParam param) {
        if (Objects.isNull(param) || Objects.isNull(param.getMarkData())) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        boolean mark = riskLabelService.mark(param);
        if (!mark) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_OPERATION_FAILED);
        }
        RiskLabelMarkRecordDO markData = param.getMarkData();
        HandleOrderParam handleOrderParam = new HandleOrderParam();
        handleOrderParam.setWorkOrderId(markData.getMarkOrderId());
        handleOrderParam.setOrderType(WorkOrderType.risk_label_mark.getType());
        handleOrderParam.setHandleResult(HandleResultEnum.done.getType());
        handleOrderParam.setUserId(ContextUtil.getAdminLongUserId());
        Response<Void> handle = workOrderCoreFeignClient.handle(handleOrderParam);
        if (handle.notOk()) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_OPERATION_FAILED);
        }
        return NewResponseUtil.makeSuccess();
    }

}
