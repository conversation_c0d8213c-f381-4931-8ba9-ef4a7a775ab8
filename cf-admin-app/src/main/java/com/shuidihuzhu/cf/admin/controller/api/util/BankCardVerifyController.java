package com.shuidihuzhu.cf.admin.controller.api.util;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.commonservice.ICommonServiceDelegate;
import com.shuidihuzhu.cf.util.BackCardUtil;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.IdCardUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.model.crowdfunding.BankCardVerifyResult;
import com.shuidihuzhu.common.web.util.NewResponseUtil;

/**
 * Author: wuxinlong Date: 16/10/18 19:18
 */

@Controller
@RequestMapping(path = "/admin/cf/util/bankCardVerify")
public class BankCardVerifyController {
	private static final Logger logger = LoggerFactory.getLogger(BankCardVerifyController.class);

	@Autowired
	ICommonServiceDelegate commonServiceDelegate;

	@RequestMapping(path = "", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	@ResponseBody
	@RequiresPermission("bank-card:verify")
	public Object bankCardVerify(String data) {
		logger.info("BankCardVerifyController data:{}, userId:{}", data, ContextUtil.getAdminUserId());
		String holderName = null;
		String idCard = null;
		String bankCardNum = null;
		try {
			JSONObject jsonObject = JSON.parseObject(data);//已检查过
			holderName = jsonObject.getString("holderName");
			idCard = jsonObject.getString("idCard");
			bankCardNum = jsonObject.getString("bankCardNum");
		} catch (Exception e) {
			return NewResponseUtil.makeResponse(AdminErrorCode.SYSTEM_PARAM_ERROR.getCode(), e.toString(),
					e.getStackTrace().toString());
		}
		if (StringUtils.isEmpty(holderName)) {
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
		}
		if (StringUtils.isEmpty(idCard) || IdCardUtil.illegal(idCard)) {
			return NewResponseUtil.makeError(AdminErrorCode.IDCARD_VERIFY_FAILED);
		}
		if (StringUtils.isEmpty(bankCardNum) || !BackCardUtil.checkBankCard(bankCardNum)) {
			return NewResponseUtil.makeError(AdminErrorCode.BACKCARD_VERIFY_FAILED);
		}
		BankCardVerifyResult result = commonServiceDelegate.verify(holderName, UserIdentityType.identity, idCard,
				bankCardNum, 1L);
		if (result.isOk()) {
			return NewResponseUtil.makeResponse(0, "", "验证成功");
		} else {
			return NewResponseUtil.makeResponse(0, result.getResponseMsg(), result.getFinalMessage());
		}
	}

}
