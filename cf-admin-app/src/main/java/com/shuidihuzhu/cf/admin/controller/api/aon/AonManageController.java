package com.shuidihuzhu.cf.admin.controller.api.aon;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.client.ugc.model.domain.aon.AonShuntDO;
import com.shuidihuzhu.cf.client.ugc.model.domain.aon.AonVarDO;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationRecordDTO;
import com.shuidihuzhu.cf.client.ugc.service.AonClient;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.vo.admin.AonVarInfoVO;
import com.shuidihuzhu.cf.vo.admin.AonVarVO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("admin/cf/aon")
public class AonManageController {

    @Autowired
    private AonClient aonClient;

    @Autowired
    private CommonOperationRecordClient commonOperationRecordClient;

    @ApiOperation("获取策略列表")
    @PostMapping("list-vars")
    @RequiresPermission("aon:list-vars")
    public Response<List<AonVarVO>> listVars(@RequestParam(value = "key", defaultValue = "") String key){
        return mapVO(aonClient.listVars(key));
    }

    private Response<List<AonVarVO>> mapVO(Response<List<AonVarDO>> resp) {
        if (resp.notOk()) {
            return NewResponseUtil.makeResponse(resp.getCode(), resp.getMsg(), null);
        }
        List<AonVarDO> data = resp.getData();
        if (CollectionUtils.isEmpty(data)) {
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        List<AonVarVO> vos = data.stream()
                .map(v -> {
                    OperationRecordDTO last = commonOperationRecordClient.getLastByBizIdAndActionTypes(v.getId(),
                            OperationActionTypeEnum.AON_VAR);
                    String newestOperator = Optional.ofNullable(last).map(OperationRecordDTO::getNameWithOrg).orElse("");
                    return AonVarVO.create(v, newestOperator);
                })
                .collect(Collectors.toList());
        return NewResponseUtil.makeSuccess(vos);
    }

    @ApiOperation("获取分流列表")
    @PostMapping("list-shunts")
    @RequiresPermission("aon:list-shunts")
    public Response<List<AonShuntDO>> listShuntByVarId(@RequestParam("varId") long varId){
        return aonClient.listShuntByVarId(varId);
    }

    @ApiOperation("获取策略详情")
    @PostMapping("get-var-info")
    @RequiresPermission("aon:get-var-info")
    public Response<AonVarInfoVO> getVarInfo(@RequestParam("varId") long varId){
        return getVarInfoPage(varId);
    }

    @NotNull
    private Response<AonVarInfoVO> getVarInfoPage(@RequestParam("varId") long varId) {
        Response<AonVarDO> varResp = aonClient.getVarById(varId);
        if (varResp.notOk()) {
            return makeRelayError(varResp);
        }
        AonVarDO var = varResp.getData();
        Response<List<AonShuntDO>> listResponse = aonClient.listShuntByVarId(varId);
        if (listResponse.notOk()) {
            return makeRelayError(listResponse);
        }
        List<AonShuntDO> list = listResponse.getData();

        AonVarInfoVO vo = new AonVarInfoVO();
        vo.setShuntList(list);

        AonShuntDO firstShunt = list.stream().filter(v -> v.getId() == var.getDefaultShuntId()).findAny().orElse(null);

        if (firstShunt != null) {
            AonShuntDO defaultShunt = new AonShuntDO();
            BeanUtils.copyProperties(firstShunt, defaultShunt);
            int precent = getDefaultPrecent(list);
            defaultShunt.setVarPrecent(precent);
            vo.setDefaultShunt(defaultShunt);
        }
        return NewResponseUtil.makeSuccess(vo);
    }

    private int getDefaultPrecent(List<AonShuntDO> list) {
        boolean hasAverage = list.stream()
                .filter(AonShuntDO::isValid)
                .anyMatch(AonShuntDO::isAverage);
        if (hasAverage) {
            return 0;
        }
        Integer storagePrecent = list.stream()
                .filter(AonShuntDO::isValid)
                .filter(v -> !v.isAverage()).map(AonShuntDO::getVarPrecent)
                .reduce(Integer::sum)
                .orElse(0);
        return 100 - storagePrecent;
    }

    @ApiOperation(
            value = "保存策略",
            notes = "传id为修改 不传为新增, 其他参数必传"
    )
    @PostMapping("save-var")
    @RequiresPermission("aon:save-var")
    public Response<AonVarDO> saveVar(@RequestBody AonVarDO aonVarDO){
        boolean isUpdate = aonVarDO.getId() > 0;
        Response<AonVarDO> varByNameResp = aonClient.getVarByName(aonVarDO.getVarName());
        if (varByNameResp.notOk()) {
            NewResponseUtil.makeResponse(varByNameResp.getCode(), varByNameResp.getMsg(), null);
        }
        AonVarDO sameNameVar = varByNameResp.getData();
        if (sameNameVar != null && sameNameVar.getId() != aonVarDO.getId()) {
            return NewResponseUtil.makeFail("已存在同名策略");
        }
        Response<AonVarDO> res = aonClient.saveVar(aonVarDO);
        if (res.notOk()) {
            return res;
        }
        AonVarDO data = res.getData();
        String tag = isUpdate ? "编辑策略" : "新增策略";
        String msg = aonVarDO.getVarName();
        if (isUpdate) {
            Long preDefaultShuntId = Optional.ofNullable(sameNameVar).map(AonVarDO::getDefaultShuntId).orElse(0L);
            if (preDefaultShuntId != 0 && preDefaultShuntId != aonVarDO.getDefaultShuntId()) {
                AonShuntDO preShunt = aonClient.getShuntById(preDefaultShuntId).getData();
                AonShuntDO shunt = aonClient.getShuntById(aonVarDO.getDefaultShuntId()).getData();
                msg += "\n编辑兜底变量" + preShunt.getVarValue() + "为" + shunt.getVarValue();
            }
        }
        long varId = data.getId();
        log(varId, tag, msg);
        return res;
    }

    @ApiOperation(
            value = "保存分流",
            notes = "传id为修改 不传为新增, 其他参数必传"
    )
    @PostMapping("save-shunt")
    @RequiresPermission("aon:save-shunt")
    public Response<AonVarInfoVO> saveShunt(@RequestBody AonShuntDO aonShuntDO){
        long varId = aonShuntDO.getVarId();
        boolean isUpdate = aonShuntDO.getId() > 0;
        AonShuntDO pre = null;
        if (isUpdate) {
            Response<AonShuntDO> readResp = aonClient.getShuntById(aonShuntDO.getId());
            if (readResp.notOk()) {
                return makeRelayError(readResp);
            }
            pre = readResp.getData();
        }
        Response<AonShuntDO> res = aonClient.saveShunt(aonShuntDO);
        if (res.ok()) {
            logShunt(aonShuntDO, varId, isUpdate, pre);
        }

        Response<AonVarInfoVO> refreshResp = refresh(varId);
        if (refreshResp.notOk()){
            return makeRelayError(refreshResp);
        }
        return getVarInfoPage(varId);
    }

    private void logShunt(@RequestBody AonShuntDO aonShuntDO, long varId, boolean isUpdate, AonShuntDO pre) {
        String tag = isUpdate ? "编辑" : "新增";
        String msg = "";
        if (isUpdate) {
            String varValue = aonShuntDO.getVarValue();
            if (pre.getVarPrecent() != aonShuntDO.getVarPrecent()){
                msg += String.format("修改变量%s分流比例%d%%到%d%%\n",
                        varValue, pre.getVarPrecent(), aonShuntDO.getVarPrecent());
            }
            if (pre.isValid() != aonShuntDO.isValid()){
                msg += String.format("%s变量%s\n",
                        aonShuntDO.isValid() ? "启用" : "禁用", varValue);
            }
            if (pre.isAverage() != aonShuntDO.isAverage()){
                msg += String.format("%s变量%s\n",
                        aonShuntDO.isValid() ? "均分" : "取消均分", varValue);
            }
        }else {
            msg = "变量值:" + aonShuntDO.getVarValue();
        }
        log(varId, tag, msg);
    }

    private Response<AonVarInfoVO> refresh(long varId) {
        Response<AonVarDO> varResp = aonClient.getVarById(varId);
        if (varResp.notOk()) {
            makeRelayError(varResp);
        }
        AonVarDO varDO = varResp.getData();
        Response<List<AonShuntDO>> listResponse = aonClient.listShuntByVarId(varId);
        if (listResponse.notOk()) {
            return makeRelayError(listResponse);
        }
        List<AonShuntDO> list = listResponse.getData();

        Integer storagePrecent = list.stream()
                .filter(AonShuntDO::isValid)
                .filter(v -> !v.isAverage())
                .map(AonShuntDO::getVarPrecent)
                .reduce(Integer::sum)
                .orElse(0);


        long averageCount = list.stream()
                .filter(AonShuntDO::isValid)
                .filter(AonShuntDO::isAverage)
                .count();

        if (averageCount > 0) {
            int totalAverage = 100 - storagePrecent;
            int averagePrecent = totalAverage / Math.toIntExact(averageCount);//已检查过
            long lastPrecent = totalAverage % averageCount;
            List<AonShuntDO> averageList = list.stream()
                    .filter(AonShuntDO::isValid)
                    .filter(AonShuntDO::isAverage).collect(Collectors.toList());
            for(AonShuntDO a : averageList) {
                int p = averagePrecent;
                if (lastPrecent > 0) {
                    lastPrecent--;
                    p++;
                }
                a.setVarPrecent(p);
                aonClient.saveShunt(a);
            }
        }

        long defaultShuntId = varDO.getDefaultShuntId();
        Response<AonShuntDO> defaultShuntResp = aonClient.getShuntById(defaultShuntId);
        if (defaultShuntResp.notOk()) {
            return makeRelayError(defaultShuntResp);
        }
        AonShuntDO defaultShunt = defaultShuntResp.getData();
        if (defaultShuntId <= 0 || defaultShunt == null || !defaultShunt.isValid()) {
            // 重新计算默认分流
            list.stream()
                    .filter(AonShuntDO::isValid)
                    .filter(v -> !v.isAverage()).findFirst().ifPresent(v -> {
                varDO.setDefaultShuntId(v.getId());
                aonClient.saveVar(varDO);
                String preValue = Optional.ofNullable(defaultShunt).map(AonShuntDO::getVarValue).orElse("未设置");
                String msg = "\n自动编辑兜底变量" + preValue + "为" + v.getVarValue();
                log(varId, "编辑策略", msg);
            });
        }

        return NewResponseUtil.makeSuccess(null);
    }

    @ApiOperation(value = "查询操作日志")
    @PostMapping("list-operation-records")
    @RequiresPermission("aon:list-operation-records")
    public Response<List<OperationRecordDTO>> listOperationRecords(@RequestParam("varId") long varId){
        List<OperationRecordDTO> records = commonOperationRecordClient.listByBizIdAndActionTypes(varId,
                OperationActionTypeEnum.AON_VAR);
        return NewResponseUtil.makeSuccess(records);
    }

    private void log(long varId, String tag, String msg) {
        int adminUserId = ContextUtil.getAdminUserId();
        commonOperationRecordClient.create()
                .buildBasicPlatform(varId, adminUserId, OperationActionTypeEnum.AON_VAR)
                .buildRemark(tag + ":" + msg)
                .save();
    }

    @NotNull
    private <T> Response<T> makeRelayError(Response result) {
        return NewResponseUtil.makeResponse(result.getCode(), result.getMsg(), null);
    }

}
