package com.shuidihuzhu.cf.admin.controller.api.security;

import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import com.shuidihuzhu.data.analytics.javasdk.core.UserTagTypeEnum;
import com.shuidihuzhu.data.servicelog.meta.cf.OpUserCopy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/admin/cf/security/copy")
@Slf4j
public class CopyReportController {

    @Autowired
    private Analytics analytics;

    @RequestMapping(value = "/report", method = RequestMethod.POST)
    public Response<Void> report(@RequestParam("locationHref") String locationHref,
                                 @RequestParam("copyContent") String copyContent){

        if(StringUtils.isEmpty(locationHref) || StringUtils.isEmpty(copyContent)){
            return Response.OK;
        }

        OpUserCopy opUserCopy = new OpUserCopy();
        opUserCopy.setLocation_href(locationHref);
        opUserCopy.setCopy_content(copyContent);
        opUserCopy.setOp_user_id(String.valueOf(ContextUtil.getAdminUserId()));

        opUserCopy.setUser_tag(String.valueOf(ContextUtil.getAdminUserId()));
        opUserCopy.setUser_tag_type(UserTagTypeEnum.userid);

        analytics.track(opUserCopy);

        log.debug("report OpUserCopy {} {} {}", ContextUtil.getAdminUserId(), copyContent, locationHref);

        return Response.OK;
    }
}
