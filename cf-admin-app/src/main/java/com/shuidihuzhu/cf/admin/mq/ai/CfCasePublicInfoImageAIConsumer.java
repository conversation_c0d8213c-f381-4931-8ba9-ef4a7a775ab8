package com.shuidihuzhu.cf.admin.mq.ai;

import com.shuidihuzhu.alps.feign.config.OceanApiMqConfig;
import com.shuidihuzhu.alps.feign.ocean.OceanApiMQResponse;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.service.workorder.imagePublic.ImagePublicWorkOrderService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import groovyjarjarantlr.debug.TraceEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Author: wangpeng
 * @Date: 2021/11/2 16:50
 * @Description:
 */
@Slf4j
@Service
@RocketMQListener(id = OceanApiMqConfig.TOPIC,
        tags = "ai-info-mosaic_10007",
        topic = OceanApiMqConfig.TOPIC)
public class CfCasePublicInfoImageAIConsumer implements MessageListener<OceanApiMQResponse> {

    @Resource
    private ImagePublicWorkOrderService imagePublicWorkOrderService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<OceanApiMQResponse> mqMessage) {
        log.info("CfCasePublicInfoImageAIConsumer is begin {}", mqMessage);
        if (Objects.isNull(mqMessage)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        OceanApiMQResponse apiMQResponse = mqMessage.getPayload();
        if (Objects.isNull(apiMQResponse)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        imagePublicWorkOrderService.submitAiMarkImage(apiMQResponse);
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
