package com.shuidihuzhu.cf.admin.controller.api.gif;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.service.gift.SeaDonationGiftService;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.cf.api.client.DonationGiftFeignClient;
import com.shuidihuzhu.client.cf.api.model.enums.RegistrationTypeEnum;
import com.shuidihuzhu.client.model.ReceivingGoodsDto;
import com.shuidihuzhu.client.model.ReceivingGoodsInfo;
import com.shuidihuzhu.common.web.annotation.SessionKeyValidateRequired;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/6/8 4:28 PM
 */
@Slf4j
@Controller
@Api("捐赠分发礼物后台接口")
@RequestMapping("admin/cf/donate/distribute-gift")
public class SeaDonationGiftController {

    @Resource
    private ShuidiCipher shuidiCipher;

    @Resource
    private SeaDonationGiftService seaDonationGiftService;

    @ResponseBody
    @ApiOperation("sea后台录入收货信息")
    @PostMapping("sea-add-receiving-info")
    @RequiresPermission("distribute-gift:receiving")
    public Response<Void> seaAddReceivingInfo(@RequestBody ReceivingGoodsInfo receivingGoodsInfo) {

        if (Objects.isNull(receivingGoodsInfo)) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }

        if (ReceivingGoodsInfo.validateFail(receivingGoodsInfo)) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }

        // 敏感信息加密
        receivingGoodsInfo.setReceivingPhone(shuidiCipher.encrypt(receivingGoodsInfo.getReceivingPhone()));
        seaDonationGiftService.seaAddReceivingInfo(receivingGoodsInfo, ContextUtil.getAdminLongUserId(), RegistrationTypeEnum.SEA_BACK_REGISTRATION.getCode());

        return NewResponseUtil.makeSuccess(null);
    }

    @ResponseBody
    @ApiOperation("sea后台查询收货信息")
    @PostMapping("sea-query-receiving-info")
    @RequiresPermission("distribute-gift:receiving")
    public Response<Map<String, Object>> seaQueryReceivingInfo(@RequestParam(required = false, defaultValue = "") String receivingName,
                                                                   @RequestParam(required = false, defaultValue = "") String receivingPhone,
                                                                   @RequestParam(required = false, defaultValue = "") String receivingAddress,
                                                                   @RequestParam(required = false, defaultValue = "") String beginTime,
                                                                   @RequestParam(required = false, defaultValue = "") String endTime,
                                                                   @RequestParam(required = false, defaultValue = "1") int current) {

        if (current < 1) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }

        if (StringUtils.isNotBlank(receivingPhone)) {
            receivingPhone = shuidiCipher.encrypt(receivingPhone);
        }
        Map<String, Object> resultMap = seaDonationGiftService.seaQueryReceivingInfo(receivingName, receivingPhone, receivingAddress,
                beginTime, endTime, current);
        if (Objects.isNull(resultMap)) {
            return NewResponseUtil.makeSuccess(Maps.newHashMap());
        }

        return NewResponseUtil.makeSuccess(resultMap);
    }

    @ResponseBody
    @ApiOperation("sea后台导出收货信息excel")
    @PostMapping("sea-export-receiving-excel")
    @RequiresPermission("distribute-gift:receiving")
    public Response<Void> seaExportReceivingExcel(@RequestParam(name = "beginTime") String beginTime,
                                                  @RequestParam(name = "endTime") String endTime) {

        // 校验时间跨度
        if (!isDateWithin100Days(beginTime, endTime)) {
            return NewResponseUtil.makeFail("时间跨度过大");
        }

        Boolean successFlag = seaDonationGiftService.seaExportReceivingExcel(beginTime, endTime, ContextUtil.getAdminLongUserId());
        if (!successFlag) {
            return NewResponseUtil.makeFail("导出失败");
        }

        return NewResponseUtil.makeSuccess();

    }

    private Boolean isDateWithin100Days(String beginTime, String endTime) {
        LocalDate beginDate = LocalDate.parse(beginTime);
        LocalDate endDate = LocalDate.parse(endTime);
        // 计算 beginDate 和 endDate 之间相差的天数
        long daysBetween = ChronoUnit.DAYS.between(beginDate, endDate);
        // 如果天数大于 100，则时间跨度超过 100 天
        return daysBetween <= 100;
    }

}
