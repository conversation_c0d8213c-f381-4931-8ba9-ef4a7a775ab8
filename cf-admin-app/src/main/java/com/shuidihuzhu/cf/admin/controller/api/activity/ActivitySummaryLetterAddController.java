package com.shuidihuzhu.cf.admin.controller.api.activity;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.admin.util.ReadExcelUtil;
import com.shuidihuzhu.cf.client.feign.SummaryLetterClient;
import com.shuidihuzhu.cf.domain.activity.SummaryLetterDO;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.activity.summary.SummaryLetterTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.PageRecommendPositionModel;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/cf/activity/summary-letter")
@Slf4j
public class ActivitySummaryLetterAddController {

    @Autowired
    private SummaryLetterClient summaryLetterClient;


    @RequestMapping(path = "/upload")
    @ResponseBody
    @RequiresPermission("summary-letter:upload")
    public Response<Void> upload(@RequestParam(value="file") MultipartFile file) throws Exception {
        return uploadByType(file, SummaryLetterTypeEnum.TAIL.getValue());
    }

    @RequestMapping(path = "/upload-for-platform")
    @ResponseBody
    @RequiresPermission("summary-letter:upload-for-platform")
    public Response<Void> uploadForPlatform(@RequestParam(value="file") MultipartFile file) throws Exception {
        return uploadByType(file, SummaryLetterTypeEnum.PLATFORM.getValue());
    }

    @NotNull
    private Response<Void> uploadByType(MultipartFile file, int letterType) throws Exception {
        if (file.isEmpty()){
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        Map<String,String> sheet1TitleMap = Maps.newHashMapWithExpectedSize(3);
        sheet1TitleMap.put("案例id","caseId");
        sheet1TitleMap.put("内容","content");
        sheet1TitleMap.put("图片","imageUrl");
        List<SummaryLetterDO> letters = (List<SummaryLetterDO>) ReadExcelUtil.parseExcel(file.getInputStream(),
                file.getOriginalFilename(), sheet1TitleMap, 1, SummaryLetterDO.class);

        for (SummaryLetterDO l : letters) {
            summaryLetterClient.addWithType(
                    l.getCaseId(),
                    l.getContent(),
                    Optional.ofNullable(l.getImageUrl()).orElse(""),
                    letterType);
        }

        log.info("upload excel success");
        summaryLetterClient.syncBindInfo();


        return NewResponseUtil.makeSuccess(null);
    }
}
