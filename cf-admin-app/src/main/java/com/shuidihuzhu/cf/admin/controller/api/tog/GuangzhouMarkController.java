package com.shuidihuzhu.cf.admin.controller.api.tog;

import com.shuidihuzhu.cf.model.tog.GuangzhouMarkShowVO;
import com.shuidihuzhu.cf.service.tog.GuangzhouService;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.protocol.types.Field;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("admin/cf/guangzhou-mark-show")
@Slf4j
public class GuangzhouMarkController {

    @Autowired
    private GuangzhouService guangzhouService;

    @PostMapping("sea-label")
    @ApiOperation("后台-页面调用显示是否穗好办标签案例")
    public Response<GuangzhouMarkShowVO> seaLabel(int caseId) {

        if(caseId <= 0){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        return NewResponseUtil.makeSuccess(guangzhouService.getGuangzhouMarkShowVO(caseId));
    }

    @PostMapping("c-show")
    @ApiOperation("后台-案例详情页手动判定c端是否展示广州标签")
    public Response<Boolean> cShow(int caseId, int showStatusToC, String reason) {

        if(caseId <= 0){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        int adminUserId = ContextUtil.getAdminUserId();
        return NewResponseUtil.makeSuccess(guangzhouService.updateShowStatusToC(caseId, showStatusToC, reason, adminUserId));
    }
}
