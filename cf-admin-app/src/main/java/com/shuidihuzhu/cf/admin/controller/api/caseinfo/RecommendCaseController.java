package com.shuidihuzhu.cf.admin.controller.api.caseinfo;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.admin.util.ReadExcelUtil;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCrowdfundingRecommendCaseDO;
import com.shuidihuzhu.cf.service.admin.AdminPageRecommendCaseService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.pf.common.v2.model.pagehelper.PaginationListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: liyan
 * @date: 2021-04-08 14:33
 **/
@Api("案例白名单")
@RestController
@RequestMapping("admin/cf/recommend-case")
@Slf4j
public class RecommendCaseController {

    @Autowired
    private AdminPageRecommendCaseService recommendCaseService;

    @ApiOperation("新增案例白名单")
    @PostMapping("/create")
    public Response<Void> create(@RequestParam("infoUuid") String infoUuid,
                                 @RequestParam(value = "type",required = false,defaultValue = "1") int type,
                                 @RequestParam(value = "sort",required = false,defaultValue = "1") int sort,
                                 @RequestParam("userId") int userId) {
        return recommendCaseService.addCase(infoUuid,type,sort,userId);
    }

    @ApiOperation("批量新增案例白名单")
    @PostMapping("/create-batch")
    public Response<Void> createBatch(@RequestParam(value = "file") MultipartFile file) {

        long adminLongUserId = ContextUtil.getAdminLongUserId();

        try {
            List<AdminCrowdfundingRecommendCaseDO> list = parseExcel(file);
            return recommendCaseService.addCaseBatch(list, adminLongUserId, 1, 1);
        } catch (Exception e) {
            return NewResponseUtil.makeFail(e.getMessage());
        }

    }


    @ApiOperation("删除白名单")
    @PostMapping("/delete")
    public Response<Void> delete(@RequestParam("id")long id,@RequestParam("userId") int userId) {
        return recommendCaseService.delete(id,userId);
    }

    @ApiOperation("案例白名单列表")
    @PostMapping("/get-case-list")
    public Response<PaginationListVO<AdminCrowdfundingRecommendCaseDO>> getCaseList(@RequestParam(value = "infoUuid",required = false,defaultValue = "")String infoUuid,
                                                                                    @RequestParam(value = "creator",required = false,defaultValue = "")String creator,
                                                                                    @RequestParam(value = "caseStatus",required = false) Integer status,
                                                                                    @RequestParam(value = "limit",required = false,defaultValue = "10")Integer limit,
                                                                                    @RequestParam(value = "current",required = false,defaultValue = "1")Integer current) {
        return recommendCaseService.getCaseList(infoUuid,creator,status,limit,current);
    }

    private List<AdminCrowdfundingRecommendCaseDO> parseExcel(MultipartFile file) throws Exception {

        Map<String, String> map = Maps.newHashMapWithExpectedSize(1);

        map.put("案例id", "infoId");

        return (List<AdminCrowdfundingRecommendCaseDO>) ReadExcelUtil.parseExcel(file.getInputStream(),
                file.getOriginalFilename(), map, 1, AdminCrowdfundingRecommendCaseDO.class);
    }


}
