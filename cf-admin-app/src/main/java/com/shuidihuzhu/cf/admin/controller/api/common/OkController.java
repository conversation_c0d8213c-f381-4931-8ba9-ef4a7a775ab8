package com.shuidihuzhu.cf.admin.controller.api.common;

import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

/**
 * Created by yao on 16/4/21.
 */
@Controller
@RequestMapping("/admin/cf")
public class OkController {
	
    private static final Logger LOGGER = LoggerFactory.getLogger(OkController.class);

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler cfRedissonHandler;

    @RequestMapping(path = "/ok", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Object oauthUri(String url, Integer type) {
        return "OK";
    }
    
    @RequestMapping(path = "/redisson/test", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public String redisson() {
        try {
            LOGGER.info("aixinchou : {}", cfRedissonHandler);
			return "OK";
		} catch (Exception e) {
			LOGGER.error("OkController redisson Error!", e);
			return "Failed";
		}
    }

}
