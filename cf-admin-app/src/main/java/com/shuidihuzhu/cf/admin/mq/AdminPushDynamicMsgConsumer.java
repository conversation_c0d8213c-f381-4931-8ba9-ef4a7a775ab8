package com.shuidihuzhu.cf.admin.mq;


import com.shuidihuzhu.cf.biz.crowdfunding.AdminPushDynamicMsgService;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.model.admin.CfPushDynamicMsgModel;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @package: com.shuidihuzhu.cf.admin.mq
 * @Author: liujiawei
 * @Date: 2020-01-10  15:21
 */
@Service
@RocketMQListener(id = MQTagCons.CF_PUSH_DYNAMIC_MSG,
        group = "cf-admin-"  + MQTagCons.CF_PUSH_DYNAMIC_MSG + "-group",
        tags = MQTagCons.CF_PUSH_DYNAMIC_MSG,
        topic = MQTopicCons.CF)
@Slf4j
public class AdminPushDynamicMsgConsumer implements MessageListener<CfPushDynamicMsgModel> {

    @Autowired
    private AdminPushDynamicMsgService adminPushDynamicMsgService;


    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfPushDynamicMsgModel> mqMessage) {

        CfPushDynamicMsgModel cfPushDynamicMsgModel = mqMessage.getPayload();

        log.info("【下发动态审核】接收到mq消息:{}", cfPushDynamicMsgModel);

        adminPushDynamicMsgService.executeDynamicMsg(cfPushDynamicMsgModel);

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
