package com.shuidihuzhu.cf.admin.util;

import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by ahrievil on 2017/1/12.
 */
public class ReadText {
    public static List<String> readOneLine(InputStream is) throws Exception{
        List<String> userIdList = new ArrayList<>();
        InputStreamReader isr = new InputStreamReader(is, "utf-8");
        BufferedReader br = new BufferedReader(isr);
        String lineTxt;
        while ((lineTxt = br.readLine()) != null) {
            if(StringUtils.isNotBlank(lineTxt)){
                userIdList.add(StringUtils.trimToEmpty(lineTxt));
            }
        }
        br.close();
        return userIdList;
    }
}
