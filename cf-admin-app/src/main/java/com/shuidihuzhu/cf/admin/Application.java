package com.shuidihuzhu.cf.admin;

import com.shuidihuzhu.cf.enhancer.subject.druid.EnableDruidMonitor;
import com.shuidihuzhu.cf.enhancer.subject.threadpool.annotation.EnableDynamicThreadPool;
import com.shuidihuzhu.eb.grafana.configuration.plugin.check.EnableFeignClientsCheck;
import com.shuidihuzhu.eb.grafana.configuration.plugin.groupdatasource.check.EnableGroupDataSourceUseCheck;
import com.shuidihuzhu.eb.grafana.configuration.plugin.health.EnableFakeCommonsClient;
import com.shuidihuzhu.eb.grafana.configuration.plugin.health.EnableFakeDataSourceHealth;
import com.shuidihuzhu.eb.grafana.configuration.plugin.health.EnableFakeRedissonHealth;
import com.shuidihuzhu.eb.grafana.configuration.plugin.hystrix.executionhook.EnableCfMetricsHystrixCommandExecutionHook;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchRestClientAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.circuitbreaker.EnableCircuitBreaker;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.netflix.hystrix.dashboard.EnableHystrixDashboard;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import reactivefeign.spring.config.EnableReactiveFeignClients;

/**
 * Created by zhouyou on 2017/12/20.
 */
@SpringBootApplication(scanBasePackages = {
        "com.shuidihuzhu.cf.admin.first",
        "com.shuidihuzhu",
        "com.shuidi", "com.shuidihuzhu.cf.client"},
        exclude ={
                DataSourceAutoConfiguration.class,
                ElasticsearchRestClientAutoConfiguration.class
        })
@EnableAspectJAutoProxy(proxyTargetClass = true)
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.shuidihuzhu.wx", "com.shuidihuzhu.account", "com.shuidihuzhu.client", "com.shuidihuzhu.hz.client",
        "com.shuidihuzhu.cf.feign", "com.shuidihuzhu.data", "com.shuidihuzhu.delay",
        "com.shuidihuzhu.cf.adminfeign", "com.shuidihuzhu.auth","com.shuidihuzhu.frame.client",
        "com.shuidihuzhu.cf.client.feign",
        "com.shuidihuzhu.cf.client.pressure",
        "com.shuidihuzhu.cf.client.subject",
        "com.shuidihuzhu.cf.client.ugc",
        "com.shuidihuzhu.cf.finance.client",
        "com.shuidihuzhu.cf.client.material",
        "com.shuidihuzhu.cf.client.ugc",
        "com.shuidihuzhu.cf.risk.client",
        "com.shuidihuzhu.cf.activity",
        "com.shuidihuzhu.algo.medicare.client",
        "com.shuidihuzhu.cf.client.stat",
        "com.shuidihuzhu.charity.client",
        "com.shuidihuzhu.cf.finance.client.feign",
        "com.shuidihuzhu.cf.client.pressure.feign",
        "com.shuidihuzhu.ai",
        "com.shuidihuzhu.cs",
        "com.shuidihuzhu.algo",
        "com.shuidihuzhu.account.verify.client",
        "com.shuidihuzhu.cf.admin.pure.feign",
        "com.shuidihuzhu.cf.client.apipure",
        "com.shuidihuzhu.cf.client.adminpure"
        ,"com.shuidihuzhu.recruit.client.feign"
        ,"com.shuidihuzhu.sdb.order.client",
        "com.shuidihuzhu.devops",
        "com.shuidihuzhu.alps.feign",
        "com.shuidihuzhu.infra",
        "com.shuidihuzhu.charity.client.api",
        "com.shuidihuzhu.cf.data.platform.client",
        "com.shuidihuzhu.client.cf.growthtool.client",
        "com.shuidihuzhu.tax.client.feign",
        "com.shuidihuzhu.kratos.client.feign",
        "com.shuidihuzhu.client.baseservice.alarm",
        "com.shuidihuzhu.cf.enhancer",
        "com.shuidihuzhu.cf.lion"
})
@EnableCircuitBreaker
@EnableHystrixDashboard
@EnableTransactionManagement
@EnableAsync
@EnableScheduling
@EnableFakeRedissonHealth
@EnableFakeDataSourceHealth
@EnableFeignClientsCheck
@EnableFakeCommonsClient
@EnableGroupDataSourceUseCheck
@EnableCfMetricsHystrixCommandExecutionHook
@EnableDynamicThreadPool(globalModel = false, noticeKey = "fdde176e-0c08-4cf1-96b3-aa62bb270e06")
@EnableDruidMonitor
@EnableReactiveFeignClients(basePackages = {"com.shuidihuzhu"})
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }

}
