package com.shuidihuzhu.cf.admin.util;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectResult;
import com.shuidihuzhu.common.web.util.aliyun.OssClientWrapper;
import com.shuidihuzhu.common.web.util.aliyun.enums.OSSBucketEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/6/14 2:42 PM
 */
@Component
public class CfOssClient {

    private static OssClientWrapper staticOssClientWrapper;

    @Resource(name = "oss-aliyun.cf-api")
    private OssClientWrapper ossClientWrapper;

    @PostConstruct
    public void init() {
        staticOssClientWrapper = ossClientWrapper;
    }

    private static final Logger logger = LoggerFactory.getLogger(CfOssClient.class);

    public static com.aliyun.oss.OSSClient getOSSClient() {
        return staticOssClientWrapper.getOSSClient();
    }

    public static com.aliyun.oss.OSSClient getOSSClient(OSSBucketEnum ossBucketEnum) {
        return staticOssClientWrapper.getOSSClient(ossBucketEnum);
    }

    /**
     * 上传文件流
     *
     * @param bucketName  bucket名称
     * @param key         bucket中的key
     * @param inputStream 文件流
     * @throws IOException
     */
    public static void putObject(String bucketName, String key, InputStream inputStream) throws IOException {
        // 初始化OSSClient
        com.aliyun.oss.OSSClient client = CfOssClient.getOSSClient();
        putObject(client, bucketName, key, inputStream);
    }

    /**
     * 上传文件流
     *
     * @param client
     * @param bucketName
     * @param key
     * @param inputStream
     * @throws IOException
     */
    public static void putObject( com.aliyun.oss.OSSClient client, String bucketName, String key, InputStream inputStream) throws IOException {
        // 创建上传Object的Metadata
        ObjectMetadata meta = new ObjectMetadata();
        // 必须设置ContentLength
        meta.setContentLength(inputStream.available());
        // 上传Object.
        PutObjectResult result = client.putObject(bucketName, key, inputStream, meta);
        // 打印ETag
        if (logger.isDebugEnabled()) {
            logger.debug("upload file success! file = " + key + " etag = " + result.getETag());
        }
    }

    /**
     * 上传文件流
     *
     * @param bucketName  bucket名称
     * @param key         bucket中的key
     * @param inputStream 文件流
     * @throws IOException
     */
    public static void putObject(String bucketName, String key, InputStream inputStream, ObjectMetadata meta) throws IOException {
        // 初始化OSSClient
        com.aliyun.oss.OSSClient client = CfOssClient.getOSSClient();
        putObject(client, bucketName, key, inputStream, meta);
    }

    /**
     * 上传文件流
     *
     * @param client
     * @param bucketName
     * @param key
     * @param inputStream
     * @param meta
     * @throws IOException
     */
    public static void putObject( com.aliyun.oss.OSSClient client, String bucketName, String key, InputStream inputStream, ObjectMetadata meta) throws IOException {
        // 上传Object.
        PutObjectResult result = client.putObject(bucketName, key, inputStream, meta);
        // 打印ETag
        if (logger.isDebugEnabled()) {
            logger.debug("upload file success! file = " + key + " etag = " + result.getETag());
        }
    }


}
