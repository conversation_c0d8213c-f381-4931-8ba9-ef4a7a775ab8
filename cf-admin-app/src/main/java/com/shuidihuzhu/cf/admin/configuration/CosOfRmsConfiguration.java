package com.shuidihuzhu.cf.admin.configuration;

import com.shuidihuzhu.cf.constants.admin.CosConstants;
import com.shuidihuzhu.infra.starter.cos.configuration.CosClientWrapper;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CosOfRmsConfiguration {

    @Bean(CosConstants.BEAN_OF_CF_IMAGES)
    @ConfigurationProperties("rms.cos.cf-images.cf-admin-api")
    public CosClientWrapper cosCfImages() {
        return new CosClientWrapper();
    }

    @Bean(CosConstants.BEAN_OF_CF_IMAGE)
    @ConfigurationProperties("rms.cos.cf-image.cf-admin-api")
    public CosClientWrapper cosCfImage() {
        return new CosClientWrapper();
    }

    @Bean(CosConstants.CF_TASK_IMAGES_BUCKET)
    @ConfigurationProperties("rms.cos.cf-risk.cf-admin-api")
    public CosClientWrapper cosClientWrapper() {
        return new CosClientWrapper();
    }

}
