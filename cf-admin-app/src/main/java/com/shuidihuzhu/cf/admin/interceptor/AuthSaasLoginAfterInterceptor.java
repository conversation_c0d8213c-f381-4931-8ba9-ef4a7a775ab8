package com.shuidihuzhu.cf.admin.interceptor;

import com.google.gson.GsonBuilder;
import com.shuidihuzhu.client.auth.saas.annotation.NoLoginRequired;
import com.shuidihuzhu.client.auth.saas.enums.AuthSaasRpcErrorCode;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;

public class AuthSaasLoginAfterInterceptor extends HandlerInterceptorAdapter {
 
    private static final Logger log = LoggerFactory.getLogger(AuthSaasLoginAfterInterceptor.class);
 
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        long authSaasUserId = 0;
        try {
            // 兜底不需要登录场景，AuthSaasContext.getAuthSaasUserId()方法会跑异常
            authSaasUserId = AuthSaasContext.getAuthSaasUserId();
        } catch (Exception e) {
            log.warn("用户未登录，未获取到userId");
            return true;
        }

        try {
            ContextUtil.setAdminUserId(Math.toIntExact(authSaasUserId));
        } catch (Exception e) {
            log.error("转换userId异常 authSaasUserId:{}", authSaasUserId, e);
            errorWriter(response, AuthSaasRpcErrorCode.USER_NOT_EXIST);
            return false;
        }
        log.info("登录的userId：{}, {}", AuthSaasContext.getAuthSaasUserId(), ContextUtil.getAdminUserId());
        return true;
    }
 
    private void errorWriter(HttpServletResponse response, AuthSaasRpcErrorCode errorCode) throws IOException {
        log.warn("AuthSaasLoginAfterInterceptor convert userId failed errorCode = {}", errorCode);
        response.setContentType("application/json;charset=UTF-8");
        ServletOutputStream servletOutputStream = response.getOutputStream();
        servletOutputStream.write((new GsonBuilder()).disableHtmlEscaping().create()
                .toJson(new Response<>(errorCode.getCode(), errorCode.getMsg()))
                .getBytes(StandardCharsets.UTF_8));
        servletOutputStream.flush();
    }
}