package com.shuidihuzhu.cf.admin.controller.api.basic;

import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.dao.basic.IAccessLogDAO;
import com.shuidihuzhu.cf.delegate.OrganizationDelegate;
import com.shuidihuzhu.cf.domain.basic.AccessLogDO;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/admin/cf/sea/access-log")
public class AdminSeaAccessLogController {

    @Resource
    private OrganizationDelegate organizationDelegate;

    @Resource
    private SeaAccountClientV1 seaAccountClientV1;

    @Resource
    private IAccessLogDAO accessLogDAO;

    @RequestMapping("/add-page-enter")
    public Response<Void> addPageEnter(@RequestParam("userId") int userId, @RequestParam("pageName") String pageName){

        if(StringUtils.isEmpty(pageName)){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        String orgName = organizationDelegate.getSimpleOrganization(userId);

        AuthRpcResponse<String> rpcResponse = seaAccountClientV1.getNameByUserId(userId);
        String name = StringUtils.EMPTY;
        if(rpcResponse != null && StringUtils.isNoneEmpty(rpcResponse.getResult())){
            name = rpcResponse.getResult();
        }

        AccessLogDO accessLogDO = AccessLogDO.builder().userId(userId).pageName(pageName).userName(name).orgName(orgName).build();

        accessLogDAO.insert(accessLogDO);

        return Response.OK;
    }
}
