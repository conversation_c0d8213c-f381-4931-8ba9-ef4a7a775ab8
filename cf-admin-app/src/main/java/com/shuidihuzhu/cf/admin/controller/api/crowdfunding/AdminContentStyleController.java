package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.shuidihuzhu.cf.biz.admin.AdminContentStyleBiz;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.cf.api.model.CfContentStyleModel;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @Description:
 * @Author: panghair<PERSON>
 * @Date: 2023/11/8 5:04 PM
 */
@Slf4j
@RestController
@RequestMapping("/admin/cf/content")
public class AdminContentStyleController {

    @Resource
    private AdminContentStyleBiz adminContentStyleBiz;

    @ApiOperation("新增或更新关键字样式")
    @PostMapping("/add-or-update-style")
    @RequiresPermission("content-style:add-or-update-style")
    public Response<Void> addOrUpdateStyle(@RequestBody CfContentStyleModel cfContentStyleModel) {
        long userId = ContextUtil.getAdminLongUserId();
        adminContentStyleBiz.addOrUpdateStyle(cfContentStyleModel, userId);
        return NewResponseUtil.makeSuccess(null);
    }

    @ApiOperation("查询案例样式")
    @PostMapping("/get-style-by-case-id")
    @RequiresPermission("content-style:get-style-by-case-id")
    public Response<CfContentStyleModel> getStyleByCaseId(@RequestParam("caseId") Integer caseId) {
        return NewResponseUtil.makeSuccess(adminContentStyleBiz.selectStyleByCaseId(caseId));
    }

}
