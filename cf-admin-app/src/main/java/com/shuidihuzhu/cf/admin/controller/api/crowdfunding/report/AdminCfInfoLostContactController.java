package com.shuidihuzhu.cf.admin.controller.api.crowdfunding.report;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.admin.util.result.ResultBuilder;
import com.shuidihuzhu.cf.admin.util.result.ResultUtils;
import com.shuidihuzhu.cf.biz.crowdfunding.report.AdminCfInfoLostContactService;
import com.shuidihuzhu.cf.constant.ResultFieldConstants;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.common.web.model.Response;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2018-07-26  17:52
 */
@RestController
@RequestMapping(path = "/admin/cf/info/lost-contact")
public class AdminCfInfoLostContactController {

    @Resource
    private AdminCfInfoLostContactService adminCfInfoLostContactService;

    @ApiOperation("更新失联状态")
    @PostMapping(path = "/update")
    @RequiresPermission("lost-contact:update")
    public Response update(
            @RequestParam(defaultValue = "0") int userId,
            @ApiParam("筹款案例id") @RequestParam("infoUuid") String infoUuid,
            @ApiParam("是否失联 true: 失联 false: 未失联") @RequestParam boolean hasLost,
            @ApiParam("失联原因 目前最大2048字符") @RequestParam(required = false, defaultValue = "") String reason) {

        OpResult opResult = adminCfInfoLostContactService.update(userId, infoUuid, hasLost, reason);
        return ResultUtils.transformOpResult2Response(opResult);
    }

    @ApiOperation("获取某筹款案例是否失联")
    @PostMapping(path = "/has-lost")
    @RequiresPermission("lost-contact:has-lost")
    public Response hasLost(@ApiParam("筹款案例id") @RequestParam("infoUuid") String infoUuid) {

        boolean hasLost = adminCfInfoLostContactService.hasLost(infoUuid);
        return ResultBuilder.create()
                .put(ResultFieldConstants.CONTACT_HAS_LOST, hasLost)
                .buildSuccessResponse();
    }
}
