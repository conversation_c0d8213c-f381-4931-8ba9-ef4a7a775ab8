package com.shuidihuzhu.cf.admin.controller.api.common;

import com.google.common.base.*;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.delegate.commonservice.ICommonServiceDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Created by wuxinlong on 17/5/11.
 */
@Slf4j
@Controller
@RequestMapping(path = "/admin/redisKv/")
public class RedisKVController {

    private static final String WHITE_LIST_KEY = "filteruserids";

    @Autowired
    private ICommonServiceDelegate commonServiceDelegate;

    @RequestMapping(path = "insert", produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response insert(String k, String v) {
        if (StringUtils.isEmpty(k) || StringUtils.isEmpty(v)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        commonServiceDelegate.saveRedisKv(k, v);

        return NewResponseUtil.makeSuccess(null);
    }

    @RequestMapping(path = "update", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response update(String k, String v) {
        if (StringUtils.isEmpty(k) || StringUtils.isEmpty(v)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        commonServiceDelegate.updateRedisKv(k, v);

        return NewResponseUtil.makeSuccess(null);
    }

    @RequestMapping(path = "/whitelist/add")
    @ResponseBody
    public Response setWhiteList(@RequestParam(value = "addUserIds", required = true)String userIds) {
        String ids = commonServiceDelegate.queryValueByKey(WHITE_LIST_KEY);
        if (Strings.isNullOrEmpty(userIds)) {
            log.info("新增加的白名单为空");
            return NewResponseUtil.makeSuccess(ids);
        }
        List<String> addUserIdList = Lists.newArrayList(Splitter.on(",").split(userIds));
        List<String> originUserIds = Lists.newArrayList();
        if (ids != null) {
            originUserIds = Lists.newArrayList(Splitter.on(",").split(ids));
        }
        for (String addUserId : addUserIdList) {
            if (!originUserIds.contains(addUserId)) {
                originUserIds.add(addUserId);
            }
        }

        String newids = Joiner.on(",").join(originUserIds);
        log.info("白名单:{};新增加的白名单:{}", ids, userIds);
        commonServiceDelegate.updateRedisKv(WHITE_LIST_KEY, newids);
        ids = commonServiceDelegate.queryValueByKey(WHITE_LIST_KEY);
        log.info("新白名单:{}", ids);
        return NewResponseUtil.makeSuccess(ids);
    }

    @RequestMapping(path = "/whitelist/show")
    @ResponseBody
    public Response<String> showWhiteList() {
        String userIds = commonServiceDelegate.queryValueByKey(WHITE_LIST_KEY);
        return NewResponseUtil.makeSuccess(userIds == null ? "": userIds);
    }

    @RequestMapping(path = "/whitelist/deleteExpired")
    @ResponseBody
    public Response<String> deleteExpire(@RequestParam(value = "deleteUserIds", required = true) String deleteUserIds) {
        if (StringUtils.isEmpty(deleteUserIds)) {
            return NewResponseUtil. makeFail("删除的userId不能为空");
        }
        if (deleteUserIds.contains("，")) {
            return NewResponseUtil.makeFail("userId用,分割");
        }
        boolean hasDeleteUserId = false; 
        List<String> deleteUserIdList = Splitter.on(",").splitToList(deleteUserIds);
        log.info("whiteList 需要删除的userId:{}", deleteUserIdList);
        String ids = commonServiceDelegate.queryValueByKey(WHITE_LIST_KEY);
        //Splitter转化出的list是不可操作的
        List<String> originIds = ids == null ? Lists.newArrayList() : Lists.newArrayList(Splitter.on(",").split(ids));
        log.info("whiteList 删除前的userId:{}", originIds);
        for (String deleteUserId : deleteUserIdList) {
            originIds.remove(deleteUserId);
            hasDeleteUserId = true;
        }
        log.info("whiteList 删除后的userId:{}", originIds);
        if (hasDeleteUserId) {
            String newWhiteList = Joiner.on(",").join(originIds);
            commonServiceDelegate.updateRedisKv(WHITE_LIST_KEY, newWhiteList);
            return NewResponseUtil.makeSuccess(newWhiteList);
        } else {
            return NewResponseUtil.makeFail("userId不在白名单列表中");
        } 
    }


}
