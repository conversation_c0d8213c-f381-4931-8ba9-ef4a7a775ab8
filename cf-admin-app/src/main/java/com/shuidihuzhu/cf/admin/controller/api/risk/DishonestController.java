package com.shuidihuzhu.cf.admin.controller.api.risk;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.risk.IDishonestService;
import com.shuidihuzhu.cf.model.risk.Dishonest;
import com.shuidihuzhu.cf.risk.client.risk.DishonestFeign;
import com.shuidihuzhu.cf.risk.model.risk.DishonestDto;
import com.shuidihuzhu.cf.risk.model.risk.DisthonestModelList;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: zhengqiu
 * @date: 2021-04-19 20:44
 **/
@Slf4j
@RestController
public class DishonestController implements DishonestFeign {

    @Autowired
    private IDishonestService dishonestService;

    @Override
    public Response<Boolean> save(DishonestDto dishonestDto) {
        Dishonest dishonest = new Dishonest();
        BeanUtils.copyProperties(dishonestDto, dishonest);
        return dishonestService.insert(dishonest) > 0 ? NewResponseUtil.makeSuccess(Boolean.TRUE) : NewResponseUtil.makeFail(Boolean.FALSE);
    }


    @Override
    public Response<DishonestDto> getInfo(int caseId, int userType,String name) {
        Dishonest dishonestInfo = dishonestService.getDishonestInfo(caseId, userType, name);
        if (dishonestInfo == null){
            return NewResponseUtil.makeSuccess(null);
        }
        DishonestDto dishonestDto = new DishonestDto();
        BeanUtils.copyProperties(dishonestInfo, dishonestDto);
        return NewResponseUtil.makeSuccess(dishonestDto);
    }

    @Override
    public Response<List<DishonestDto>> getInfoList(DisthonestModelList disthonestModelList) {
        List<DishonestDto> dishonestDtos = new ArrayList<>();
        Map<Integer, String> infoMap = disthonestModelList.getInfoMap();
        if (MapUtils.isEmpty(infoMap)) {
            log.warn("getInfoList dishonest empty {}", disthonestModelList);
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        for (Map.Entry<Integer, String> entry : infoMap.entrySet()) {
            Dishonest info = dishonestService.getDishonestInfo(entry.getKey(), disthonestModelList.getUserType(), entry.getValue());
            DishonestDto dishonestDto = new DishonestDto();
            if (info != null) {
                BeanUtils.copyProperties(info, dishonestDto);
            }
            dishonestDto.setCaseId(entry.getKey());
            dishonestDtos.add(dishonestDto);
        }
        return NewResponseUtil.makeSuccess(dishonestDtos);
    }


}
