package com.shuidihuzhu.cf.admin.controller.api.common;

import brave.Tracing;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.shuidihuzhu.client.auth.saas.annotation.NoLoginRequired;
import com.shuidihuzhu.cf.delegate.commonservice.ICommonServiceDelegate;
import com.shuidihuzhu.cf.model.admin.vo.JobStatusVo;
import com.shuidihuzhu.cf.service.admin.dream.AdminCheckTaskPublishService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.eb.grafana.configuration.endpoint.Dependency;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * cf-Task job 状态查询及job操作
 * Author: zhangzhi
 * Date: 2018/11/14 14:37
 */
@RefreshScope
@RestController
@RequestMapping("/admin/cftask")
public class CfTaskController {
    private static final Logger logger = LoggerFactory.getLogger(CfTaskController.class);
    @Autowired
    private DiscoveryClient discoveryClient;

    @Autowired
    private Tracing tracing;

    @Autowired
    private ICommonServiceDelegate commonServiceDelegate;

    @Autowired
    private AdminCheckTaskPublishService checkTaskPublishService;

    @Autowired
    private RestTemplate restTemplate;

    @Value("${serviceids-value:cf-api,cf-admin-api,cf-task}")
    private String serviceIdsValue;
    @Value("${exclusion-serviceids-value:cf-nickname-mgr,cf-risk-mgr}")
    private String exclusionServiceIdsValue;
    private static final String CF_TASK_PUBLISH_SWITCH = "cf-task-publish-switch";
    private static ExecutorService executorService;
    //发版token 不要改动
    private static final String CF_TASK_PUBLISH_TOKEN = "xUJCBDvKxE0aaYpp";

    @PostConstruct
    public void init() {
        executorService = tracing.currentTraceContext().executorService(Executors.newFixedThreadPool(3));
    }


    @RequestMapping(path = "/hosts")
    public Response getHost() {

        List<ServiceInstance> instances = discoveryClient.getInstances("cf-task");

        return NewResponseUtil.makeSuccess(instances);
    }

    /**
     * 检查cf-task是否可以发布
     *
     * @return 1--可以发布  0--不可以发布
     */
    @NoLoginRequired
    @RequestMapping(path = "/check-allow-publish", method = RequestMethod.GET)
    public int checkAllowPublish(@RequestParam(value = "min", defaultValue = "1") int min,
                                 @RequestParam(value = "apiToken", defaultValue = "") String apiToken) {
//        return 1;
        if (min < 1 || min > 10) {
            return 0;
        }
        if (!CF_TASK_PUBLISH_TOKEN.equals(apiToken)) {
            logger.error("cf-task发版token错误:{}", apiToken);
            return 0;
        }
        int status = commonServiceDelegate.getGrayTestBySelfTag(CF_TASK_PUBLISH_SWITCH, 111111, "");
        if (status == 1) {
            return 1;
        }
        try {
            if (checkTaskPublishService.checkPublish(min)) {
                return 1;
            }
        } catch (Exception e) {
            logger.error("检查失败", e);
        }
        return 0;
    }

    /**
     * 查看正在run的job
     * host 主机
     *
     * @return
     */
    @RequestMapping(path = "/running-jobs")
    public Response getRunningJobList(String host) {
        List<JobStatusVo> listAll = new ArrayList<>();
        List<JobStatusVo> list = new ArrayList<>();
        List<ServiceInstance> instances = discoveryClient.getInstances("cf-task");
        if (instances == null || instances.size() == 0) {
            return NewResponseUtil.makeSuccess(list);
        }
        if (!StringUtils.isEmpty(host)) {
            instances = instances.stream().filter(((ServiceInstance b) -> b.getHost().equals(host))).collect(Collectors.toList());
        }
        for (ServiceInstance instance : instances) {
            //String url = "http://" + instance.getHost() + ":" + instance.getPort();
            String url = instance.getUri().toString();
            String runningjobsUrl = url + "/admin/get/running-jobs";
            String runningjobsInfo = restTemplate.getForObject(runningjobsUrl, String.class);
            if (!StringUtils.isEmpty(runningjobsInfo)) {
                list = JSON.parseArray(runningjobsInfo, JobStatusVo.class);//已检查过
                for (JobStatusVo job : list) {
                    job.setIp(instance.getHost());
                }
                logger.info("runningjobsInfo:{}", runningjobsInfo);
            }
            if (list != null && list.size() > 0) {
                listAll.addAll(list);
            }
        }
        return NewResponseUtil.makeSuccess(listAll);
    }


    /**
     * 查看准备run的job
     * host 主机
     *
     * @return
     */
    @RequestMapping(path = "/todo-jobs")
    public Response getToDoJobList(@RequestParam(value = "min", defaultValue = "5") int minute, String host) {
        List<JobStatusVo> list = new ArrayList<>();
        List<ServiceInstance> instances = discoveryClient.getInstances("cf-task");
        if (instances == null || instances.size() == 0) {
            return NewResponseUtil.makeSuccess(list);
        }
        if (!StringUtils.isEmpty(host)) {
            instances = instances.stream().filter(((ServiceInstance b) -> b.getHost().equals(host))).collect(Collectors.toList());
        }
        for (ServiceInstance instance : instances) {
            //String url = "http://" + instance.getHost() + ":" + instance.getPort();
            String url = instance.getUri().toString();
            String todojobsUrl = url + "/admin/get/todo-jobs?min=" + minute;
            String todojobsInfo = restTemplate.getForObject(todojobsUrl, String.class);
            if (!StringUtils.isEmpty(todojobsInfo)) {
                list = JSON.parseArray(todojobsInfo, JobStatusVo.class);//已检查过
                for (JobStatusVo job : list) {
                    job.setIp(instance.getHost());
                }
                logger.info("todojobsInfo:{}", todojobsInfo);
            }
        }
        return NewResponseUtil.makeSuccess(list);
    }


    /**
     * 执行某个job
     * host 主机
     *
     * @return
     */
    @RequestMapping(path = "/runjob")
    public Response runjob(@RequestParam("host") String host, @RequestParam("jobName") String jobName,
                           @RequestParam(value = "jobParameter", required = false) String jobParameter) {
        List<ServiceInstance> instances = discoveryClient.getInstances("cf-task");
        if (instances != null && instances.size() > 0) {
            ServiceInstance instance = instances.stream().filter(((ServiceInstance b) -> b.getHost().equals(host))).findFirst().get();

            //String url = "http://" + instance.getHost() + ":" + instance.getPort();
            String url = instance.getUri().toString();
            String runjobUrl = url + "/admin/task/runjob";

            MultiValueMap<String, String> paramMap = new LinkedMultiValueMap<>();
            paramMap.add("jobName", jobName);
            if (!StringUtils.isEmpty(jobParameter)) {
                paramMap.add("jobParameter", jobParameter);
            }

            String json = restTemplate.postForObject(runjobUrl, paramMap, String.class);
            logger.info("runjobInfo:{}", json);
        }
        return NewResponseUtil.makeSuccess(null);
    }

    @Deprecated
    @RequestMapping(path = "/dependencyold")
    public Response dependencyold(@RequestParam(value = "service", required = false) String service,
                                  @RequestParam(value = "pattern", required = false) String pattern) {
        List<JarDependency> JarDependencyList = new ArrayList<>();
        long begin = System.currentTimeMillis();
        if (Strings.isNullOrEmpty(serviceIdsValue)) {
            return NewResponseUtil.makeSuccess(null);
        }
        String[] ids = serviceIdsValue.split(",");
        List<String> serviceIds = new ArrayList<>();
        if (Strings.isNullOrEmpty(service)) {
            serviceIds = Arrays.asList(ids);
        } else {
            for (int i = 0; i < ids.length; i++) {
                String serviceId = ids[i];
                if (serviceId.startsWith(service)) {
                    serviceIds.add(serviceId);
                }
            }
        }
        for (String serviceId : serviceIds) {
            ServiceInstance instance = discoveryClient.getInstances(serviceId).get(0);
            String url = instance.getUri() + "/actuator/dependency";
            if (!Strings.isNullOrEmpty(pattern)) {
                url += "?pattern=" + pattern;
            }
            try {
                String dependencyStr = restTemplate.getForObject(url, String.class);
                List<Dependency> dependencyList = new ArrayList<>();
                if (!Strings.isNullOrEmpty(dependencyStr)) {
                    dependencyList = JSONObject.parseArray(dependencyStr, Dependency.class);
                    JarDependency jarDependency = new JarDependency();
                    jarDependency.setServiceId(serviceId);
                    jarDependency.setDependencyList(dependencyList);
                    JarDependencyList.add(jarDependency);
                }
            } catch (Exception e) {
                logger.error("dependency serviceId={};url={}", serviceId, url, e);
            }
        }
        long end = System.currentTimeMillis();
        logger.info("dependency cost={}", (end - begin));
        return NewResponseUtil.makeSuccess(JarDependencyList);
    }

    @RequestMapping(path = "/dependency")
    public Response dependency(@RequestParam(value = "service", required = false) String service,
                               @RequestParam(value = "pattern", required = false) String pattern) {
        long begin = System.currentTimeMillis();
        List<JarDependency> JarDependencyList = new ArrayList<>();

        List<String> exServiceIds = new ArrayList<>();//排除项
        if (!Strings.isNullOrEmpty(exclusionServiceIdsValue)) {
            exServiceIds = Arrays.asList(exclusionServiceIdsValue.split(","));
        }
        List<String> ids = discoveryClient.getServices();//所有服务
        List<String> serviceIds = new ArrayList<>();//检查项
        for (String id : ids) {
            if (exServiceIds.contains(id)) {
                continue;
            }
            if (Strings.isNullOrEmpty(service)) {
                if (id.startsWith("cf-")) {
                    serviceIds.add(id);
                }
            } else {
                if (id.startsWith("cf-") && id.startsWith(service)) {
                    serviceIds.add(id);
                }
            }
        }

        for (String serviceId : serviceIds) {
            List<Dependency> dependencyList = new ArrayList<>();
            ServiceInstance instance = discoveryClient.getInstances(serviceId).get(0);
            String url = instance.getUri() + "/actuator/dependency";
            if (!Strings.isNullOrEmpty(pattern)) {
                url += "?pattern=" + pattern;
            }
            try {
                String dependencyStr = restTemplate.getForObject(url, String.class);
                if (!Strings.isNullOrEmpty(dependencyStr)) {
                    dependencyList = JSONObject.parseArray(dependencyStr, Dependency.class);
                    JarDependency jarDependency = new JarDependency();
                    jarDependency.setServiceId(serviceId);
                    jarDependency.setDependencyList(dependencyList);
                    JarDependencyList.add(jarDependency);
                }
            } catch (Exception e) {
                JarDependency jarDependency = new JarDependency();
                jarDependency.setServiceId(serviceId);
                jarDependency.setDescription("未升级eb-grafana-starter(2.0.13)");
                JarDependencyList.add(jarDependency);
                logger.error("dependency serviceId={};url={};e={}", serviceId, url, e.getMessage());
            }
        }
        long end = System.currentTimeMillis();
        logger.info("dependency cost={}", (end - begin));
        return NewResponseUtil.makeSuccess(JarDependencyList);
    }
}

@Data
class JarDependency {
    private String serviceId;
    private List<Dependency> dependencyList;
    private String description;
}
