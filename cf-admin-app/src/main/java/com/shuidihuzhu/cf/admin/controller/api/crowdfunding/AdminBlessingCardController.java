package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminUserRealService;
import com.shuidihuzhu.cf.biz.crowdfunding.impl.AdminUserRealServiceImpl;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.UserRealInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminBlessingCardVo;
import com.shuidihuzhu.cf.service.crowdfunding.AdminBlessingCardService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/11/24  9:17 下午
 */
@Slf4j
@Controller
@RequestMapping(path = "/admin/crowdfunding/case")
public class AdminBlessingCardController {

    @Autowired
    private AdminBlessingCardService adminBlessingCardService;
    @Autowired
    private AdminUserRealService userRealService;

    @ResponseBody
    @RequestMapping(path = "/blessing-card/add-and-update", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("blessing-card:add-and-update")
    public Response<Integer> blessingCardAddAndUpdate(@RequestParam(name = "id") int id,
                                                      @RequestParam(name = "image") String image,
                                                      @RequestParam(name = "text") String text,
                                                      @RequestParam(name = "amount") int amount) {
        if (StringUtils.isAnyEmpty(image, text) || amount <= 0) {
            NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<AdminBlessingCardVo> adminBlessingCardVos = adminBlessingCardService.selectListByImageOrText(image, text,amount);
        adminBlessingCardVos = adminBlessingCardVos.stream().filter(v -> v.getId() != id).collect(Collectors.toList());
        adminBlessingCardVos = adminBlessingCardVos.stream().filter(v -> v.getBlessingCardAmount() == amount || v.getBlessingCardImage().equals(image) || v.getBlessingCardText().equals(text)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(adminBlessingCardVos)) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }

        int userId = ContextUtil.getAdminUserId();
        return NewResponseUtil.makeSuccess(adminBlessingCardService.blessingCardAddAndUpdate(id, image, text, amount, userId));
    }

    @ResponseBody
    @RequestMapping(path = "/blessing-card/del", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("blessing-card:del")
    public Response<Void> blessingCardDel(@RequestParam(name = "id") int id) {
        if (id <= 0) {
            NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        int userId = ContextUtil.getAdminUserId();
        adminBlessingCardService.blessingCardDel(id, userId);
        return NewResponseUtil.makeSuccess(null);
    }

    @ResponseBody
    @RequestMapping(path = "/blessing-card/select-list", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("blessing-card:select-list")
    public Response<List<AdminBlessingCardVo>> blessingCardSelectList() {
        return NewResponseUtil.makeSuccess(adminBlessingCardService.getList());
    }

    @ResponseBody
    @RequestMapping(path = "/user-real-info/delete", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("user-real-info:unbind")
    public Response<Integer> unbind(long realUserId, int id, String comment, String pic) {
        return NewResponseUtil.makeSuccess(userRealService.unbind(realUserId, ContextUtil.getAdminUserId(), id, comment, pic));
    }

    @ResponseBody
    @RequestMapping(path = "/user-real-info/select-once-list", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("user-real-info:select-once-success")
    public Response<List<AdminUserRealServiceImpl.UserRealInfoView>> getOnceAllSuccess(long realUserId) {
        return NewResponseUtil.makeSuccess(userRealService.getOnceAllSuccess(realUserId));
    }

    @ResponseBody
    @RequestMapping(path = "/user-real-info/select-list", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("user-real-info:select-list")
    public Response<List<AdminUserRealServiceImpl.UserRealInfoView>> getByUserIdAndIdCard(String mobile, String idCard) {
        return NewResponseUtil.makeSuccess(userRealService.getByUserIdAndIdCard(mobile, idCard));
    }


}
