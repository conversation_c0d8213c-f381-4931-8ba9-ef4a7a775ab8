package com.shuidihuzhu.cf.admin.mq;


import com.shuidihuzhu.cf.biz.crowdfunding.CfSensitiveWordRecordBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.UserOperationStatConstant;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.deliver.CrowdfundingOrderDeliver;
import com.shuidihuzhu.cf.service.admin.CfHospitalAuditService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service
@RocketMQListener(id = MQTagCons.CF_DONATION_SUCCESS_MSG,
        group = "cf-admin-pay-group",
        tags = MQTagCons.CF_DONATION_SUCCESS_MSG,
        topic = MQTopicCons.CF)
@Slf4j
public class CfPayRecordConsumer implements MessageListener<CrowdfundingOrderDeliver> {

    @Resource
    private CfSensitiveWordRecordBiz cfSensitiveWordRecordBiz;

    @Resource
    private MeterRegistry meterRegistry;

    @Autowired
    private CfHospitalAuditService cfHospitalAuditService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdfundingOrderDeliver> consumerMessage) {

        CrowdfundingOrderDeliver payload = consumerMessage.getPayload();
        if(consumerMessage == null || payload == null){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        //上报一次打点信息
        meterRegistry.counter(UserOperationStatConstant.USER_OPERATING_STAT,
                UserOperationStatConstant.OPERATION, UserOperationStatConstant.ORDER).increment();

        log.info("receive CrowdfundingOrder message: {}", payload);

        /** CfDonationSuccessMsg cf-api中的这个代码有这个逻辑，会把捐款消息重复发送多次 */

        if(payload.getConsumeStatus() != 0){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        Integer caseId = payload.getCrowdfundingId();
        try {
            cfHospitalAuditService.onDonate(caseId);
        } catch (Exception e) {
            log.error("on donate error caseId:{}, orderId:{}", caseId, payload.getId(), e);
        }

        try {
            cfSensitiveWordRecordBiz.buildOneOrder(payload);
        }catch (Exception e){
            log.error("CfPayRecordConsumer error consumerMessage={}",consumerMessage,e);
            return ConsumeStatus.RECONSUME_LATER;
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
