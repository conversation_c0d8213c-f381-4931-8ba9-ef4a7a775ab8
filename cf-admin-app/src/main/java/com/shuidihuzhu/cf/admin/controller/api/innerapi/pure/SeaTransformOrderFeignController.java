package com.shuidihuzhu.cf.admin.controller.api.innerapi.pure;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.admin.AdminOrganizationBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderFlowBiz;
import com.shuidihuzhu.cf.client.adminpure.feign.ReportFeignClient;
import com.shuidihuzhu.cf.client.adminpure.feign.SeaTransformOrderClient;
import com.shuidihuzhu.cf.client.adminpure.model.caseinfo.SeaTransformCaseInfoVO;
import com.shuidihuzhu.cf.client.adminpure.model.caseinfo.SeaTransformCaseSearchParam;
import com.shuidihuzhu.cf.client.adminpure.model.report.ReportInfoVO;
import com.shuidihuzhu.cf.client.adminpure.model.transformorder.*;
import com.shuidihuzhu.cf.client.base.page.v1.model.PaginationListVO;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.facade.CaseSearchFacade;
import com.shuidihuzhu.cf.model.admin.AdminOrganization;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView;
import com.shuidihuzhu.cf.service.report.ReportOperationService;
import com.shuidihuzhu.cf.vo.crowdfunding.OrganizationEmployeesVo;
import com.shuidihuzhu.cf.vo.crowdfunding.account.SeaUserAccountVo;
import com.shuidihuzhu.client.cf.growthtool.client.ICrmHospitalFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.CfCrmStandardHospitalSimpleModel;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2020/2/27
 */
@RestController
public class SeaTransformOrderFeignController implements SeaTransformOrderClient {

    @Autowired
    private CaseSearchFacade caseSearchFacade;
    @Autowired
    private AdminOrganizationBiz adminOrganizationBiz;
    @Autowired
    private AdminWorkOrderFlowBiz orderFlowBiz;
    @Autowired
    private ICrmHospitalFeignClient hospitalFeignClient;

    @Override
    public OperationResult<PaginationListVO<SeaTransformCaseInfoVO>> getCaseInfoList(SeaTransformCaseSearchParam param) {
        return caseSearchFacade.getCaseInfoListForTransform(param);
    }

    @Override
    public OperationResult<Long> createTransformOrder(TransformOrderCreateParam param) {
        return orderFlowBiz.createTransformOrder(param);
    }

    @Override
    public OperationResult<Long> syncOrderInfo(SyncOrderInfoParam param) {
        return orderFlowBiz.updateTransformOrder(param);
    }

    @Override
    public OperationResult<List<TransformOrgVO>> getOrgTree(){
        List<AdminOrganization> organizationTree = adminOrganizationBiz.getOrganizationTree();
        if (CollectionUtils.isEmpty(organizationTree)) {
            return OperationResult.success(Lists.newArrayList());
        }
        List<TransformOrgVO> list = organizationTree.stream().map(this::map).collect(Collectors.toList());
        return OperationResult.success(list);
    }

    @Override
    public OperationResult<OrganizationOperatorInfoVO> getOrgOperatorInfo(int orgId) {
        OrganizationEmployeesVo organizationEmployees = adminOrganizationBiz.getOrganizationEmployees(orgId, 1, 1000);
        OrganizationOperatorInfoVO v = new OrganizationOperatorInfoVO();
        v.setOrganizationRelation(organizationEmployees.getOrganizationRelation());
        List<SeaUserAccountVo> userAccount = organizationEmployees.getUserAccount();
        if (CollectionUtils.isEmpty(userAccount)) {
            v.setUserAccount(Lists.newArrayList());
        } else {
            List<OperatorVO> operators = userAccount.stream().map(this::mapOperator).collect(Collectors.toList());
            v.setUserAccount(operators);
        }
        return OperationResult.success(v);
    }

    @Override
    public OperationResult<List<CrmStandardHospitalSimpleVo>> getHospitals(String city, String hospitalName) {
        Response<List<CfCrmStandardHospitalSimpleModel>> response = hospitalFeignClient.listStandardByFuzzyName(hospitalName, city);
        List<CfCrmStandardHospitalSimpleModel> cfCrmStandardHospitalSimpleModels = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList());
        List<CrmStandardHospitalSimpleVo> crmStandardHospitalSimpleVoList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(cfCrmStandardHospitalSimpleModels)) {
            for (CfCrmStandardHospitalSimpleModel cfCrmStandardHospitalSimpleModel : cfCrmStandardHospitalSimpleModels) {
                CrmStandardHospitalSimpleVo crmStandardHospitalSimpleVo = new CrmStandardHospitalSimpleVo();
                BeanUtils.copyProperties(cfCrmStandardHospitalSimpleModel, crmStandardHospitalSimpleVo);
                crmStandardHospitalSimpleVoList.add(crmStandardHospitalSimpleVo);
            }
        }
        return OperationResult.success(crmStandardHospitalSimpleVoList);
    }

    private OperatorVO mapOperator(SeaUserAccountVo seaUserAccountVo) {
        if (seaUserAccountVo == null) {
            return null;
        }
        OperatorVO o = new OperatorVO();
        o.setId(seaUserAccountVo.getId());
        o.setName(seaUserAccountVo.getName());
        o.setMis(seaUserAccountVo.getMis());
        return o;
    }

    private TransformOrgVO map(AdminOrganization o){
        TransformOrgVO t = new TransformOrgVO();
        t.setId(o.getId());
        t.setName(o.getName());
        t.setParentOrgId(o.getParentOrgId());
        if (CollectionUtils.isEmpty(o.getChidrenOrgs())) {
            return t;
        }
        List<TransformOrgVO> list = o.getChidrenOrgs().stream().map(this::map).collect(Collectors.toList());
        t.setChidrenOrgs(list);
        return t;
    }

}
