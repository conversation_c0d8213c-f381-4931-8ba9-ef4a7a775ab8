package com.shuidihuzhu.cf.admin.mq.common;

import com.shuidihuzhu.cf.admin.util.MQLogUtils;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2018-07-16  15:30
 */
@Service
@Slf4j
public class CommonConsumerHelper {

    /**
     * @param consumerMessage
     * @param handler
     * @param discardable     是否可以在异常的时候丢弃
     * @param <T>
     * @return
     */
    public <T> ConsumeStatus consumeMessage(ConsumerMessage<T> consumerMessage, Handler<T> handler, boolean discardable) {
        MQLogUtils.logWithSelf(handler, consumerMessage, null);
        boolean success = false;
        try {
            success = handler.handle(consumerMessage);
        } catch (Exception e) {
            MQLogUtils.logErrorWithSelf(handler, consumerMessage, e, null);
            // 如果可以丢弃 则在异常的时候丢弃
            if (discardable) {
                success = true;
            }
        }
        return success ? ConsumeStatus.CONSUME_SUCCESS : ConsumeStatus.RECONSUME_LATER;
    }

    public <T> ConsumeStatus consumeMessage(ConsumerMessage<T> consumerMessage, Handler<T> handler,
                                            boolean discardable, Logger logger) {
        MQLogUtils.logWithLogger(logger, consumerMessage, null);
        boolean success = false;
        try {
            success = handler.handle(consumerMessage);
        } catch (Exception e) {
            MQLogUtils.logErrorWithLogger(logger, consumerMessage, e, null);
            // 如果可以丢弃 则在异常的时候丢弃
            if (discardable) {
                success = true;
            }
        }
        return success ? ConsumeStatus.CONSUME_SUCCESS : ConsumeStatus.RECONSUME_LATER;
    }

    public interface Handler<T> {

        /**
         * 处理消息函数
         *
         * @param consumerMessage
         * @return
         */
        boolean handle(ConsumerMessage<T> consumerMessage);
    }

}
