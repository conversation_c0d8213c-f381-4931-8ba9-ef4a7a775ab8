package com.shuidihuzhu.cf.admin.mq;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingOperationBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.UserOperationStatConstant;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingOperationEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation;
import com.shuidihuzhu.cf.model.event.CailiaoConditionEvent;
import com.shuidihuzhu.cf.service.AdminEventPublishService;
import com.shuidihuzhu.cf.service.crowdfunding.CfCailiaoService;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Created by niejiangnan on 2018/3/28.
 */
@RefreshScope
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.CF_WORK_ORDER_APPROVE_LAUNCH,
        tags = MQTagCons.CF_WORK_ORDER_APPROVE_LAUNCH, topic = MQTopicCons.CF)
public class CfWorkOrderApproveConsumer implements MessageListener<String> {

    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private AdminCrowdfundingOperationBiz adminCrowdfundingOperationBiz;

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler cfRedissonHandler;

    @Resource
    private MeterRegistry meterRegistry;

    @Autowired
    private AdminEventPublishService adminEventPublishService;

    @Autowired
    private CfCailiaoService cfCailiaoService;

    @Value("${active-service-case:true}")
    private boolean activeServiceCase;

    private static final String CASE_APPROVE_CREATE_LOCK_NAME = "CF_WORK_ORDER_CASE_APPROVE_CREATE_";


    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<String> consumerMessage) {
        String infoUuid = consumerMessage.getPayload();

        //上报一次打点信息
        meterRegistry.counter(UserOperationStatConstant.USER_OPERATING_STAT,
                UserOperationStatConstant.OPERATION, UserOperationStatConstant.APPROVE).increment();

        log.info("CfWorkOrderApproveConsumer produceOneApprove infoUuid:{}", infoUuid);
        if (StringUtils.isBlank(infoUuid)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (crowdfundingInfo == null) {
            log.info("crowdfundingInfo is null! infoUuid:{}", infoUuid);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        //优化判断条件当  案例当前是  驳回或者通过状态  不能生成工单
        if (crowdfundingInfo.getStatus() == CrowdfundingStatus.APPROVE_DENIED ||
                crowdfundingInfo.getStatus() == CrowdfundingStatus.CROWDFUNDING_STATED) {
            log.info("crowdfundingInfo status is :{}", crowdfundingInfo.getStatus().value());
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        // 获取案例审核工单生成锁失败
        String identifier = "";
        String lockName = CASE_APPROVE_CREATE_LOCK_NAME + infoUuid;
        try {
            identifier = cfRedissonHandler.tryLock(lockName, 10 * 1000L);
            if (StringUtils.isBlank(identifier)) {
                log.info(" 获取案例审核工单生成锁失败，identifier:{}", identifier);
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            //标记是否为主动服务案例
            CrowdfundingOperation crowdfundingOperation = adminCrowdfundingOperationBiz.getByInfoId(infoUuid);
            if (crowdfundingOperation.getFuwuType() == 0){
                if (activeServiceCase){
                    adminCrowdfundingOperationBiz.updateFuwuType(crowdfundingOperation.getId(),1);
                } else {
                    adminCrowdfundingOperationBiz.updateFuwuType(crowdfundingOperation.getId(),2);
                }
            }

            //提交材料审核&案例未被置为不再处理 or 延后电话联系
            if (crowdfundingOperation.getOperation() != CrowdfundingOperationEnum.NEVER_PROCESSING.value()
                    && crowdfundingOperation.getOperation() != CrowdfundingOperationEnum.DEFER_CONTACT.value()){
                CailiaoConditionEvent event = new CailiaoConditionEvent(this,crowdfundingInfo.getId(), crowdfundingInfo.getInfoId());
                event.setAmount(crowdfundingInfo.getAmount());
                event.setOperation(crowdfundingOperation.getOperation());
//                adminEventPublishService.publish(event);
                cfCailiaoService.createWorkOrder(event);
            }

            log.info("CfWorkOrderApproveConsumer produceOneApprove infoUuid:{} lockName:{}, " +
                    "SUCCESS", infoUuid, lockName);
            return ConsumeStatus.CONSUME_SUCCESS;
        }  catch (Exception e) {
            log.error("CfWorkOrderApproveConsumer error:", e);
            return ConsumeStatus.RECONSUME_LATER;
        } finally {
            if(StringUtils.isNotBlank(identifier)) {
                cfRedissonHandler.unLock(lockName, identifier);
            }
        }
    }



}
