package com.shuidihuzhu.cf.admin.controller.api.innerapi;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingAttachmentBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.feign.CfAttachmentFeignClient;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.admin.client.CfExcelSendFeignClient;
import com.shuidihuzhu.client.cf.growthtool.client.CfExcelGenerateFeignClient;
import com.shuidihuzhu.client.model.ExcelGenerateParam;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2022/9/15 4:44 下午
 */
@Slf4j
@RestController
public class CfExcelSendFeignController implements CfExcelSendFeignClient {

    @Resource
    private AdminCrowdfundingAttachmentBiz cfAttachmentBiz;
    @Resource
    private AdminCfInfoExtBiz adminCfInfoExtBiz;
    @Resource
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;
    @Resource
    private CfExcelGenerateFeignClient cfExcelGenerateFeignClient;

    @Override
    public Response<Void> sendImageExcel(Timestamp caseStartTime, Timestamp caseEndTime, Timestamp startTime, Timestamp endTime) {


        List<CrowdfundingInfo> crowdfundingInfo = crowdfundingInfoBiz.getByCreateTime(caseStartTime, caseEndTime);
        if (CollectionUtils.isEmpty(crowdfundingInfo)) {
            log.info("sendImageExcel crowdfundingInfo is null");
            return NewResponseUtil.makeSuccess(null);
        }

        List<CrowdfundingInfo> validCases = Lists.newArrayList();
        crowdfundingInfo.forEach(cf -> {
            CfInfoExt cfInfoExt = adminCfInfoExtBiz.getByCaseId(cf.getId());
            if (FirstApproveStatusEnum.APPLY_SUCCESS.getCode() == cfInfoExt.getFirstApproveStatus()
                    && cfInfoExt.getFirstApproveTime().after(startTime)
                    && cfInfoExt.getFirstApproveTime().before(endTime)) {
                validCases.add(cf);
            }
        });
        if (CollectionUtils.isEmpty(validCases)) {
            log.info("sendImageExcel validCases is null");
            return NewResponseUtil.makeSuccess(null);
        }

        String[] headers = {"案例id", "图片id", "图片url"};
        List<Map<String, Object>> collections = Lists.newArrayList();

        for (CrowdfundingInfo caseInfo : validCases) {
            List<CrowdfundingAttachment> crowdfundingAttachments = cfAttachmentBiz.getByParentId(caseInfo.getId());
            if (CollectionUtils.isEmpty(crowdfundingAttachments)) {
                continue;
            }

            for (CrowdfundingAttachment attachment : crowdfundingAttachments) {
                if (attachment.getType() == AttachmentTypeEnum.ATTACH_CF) {
                    Map<String, Object> line = Maps.newHashMap();
                    line.put(headers[0], caseInfo.getId());
                    line.put(headers[1], attachment.getId());
                    line.put(headers[2], attachment.getUrl());
                    collections.add(line);
                }
            }
        }

        List<List<Map<String, Object>>> collectionsList = Lists.partition(collections, 500);
        int index = 0;
        for (List<Map<String, Object>> collectionList : collectionsList) {
            ExcelGenerateParam excelGenerateParam = ExcelGenerateParam.builder()
                    .collections(collectionList)
                    .fileName("待打标案例列表 " + DateUtil.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss") + " " + index++)
                    .headers(headers)
                    .alarmUser(Lists.newArrayList("5d2d88f8-d3af-4afe-837c-0cf9c76cfed6")).build();
            log.info("sendImageExcel excelGenerateParam is {}", JSONObject.toJSONString(excelGenerateParam));
            cfExcelGenerateFeignClient.generateExcelUrl(excelGenerateParam);
        }

        return NewResponseUtil.makeSuccess(null);
    }
}
