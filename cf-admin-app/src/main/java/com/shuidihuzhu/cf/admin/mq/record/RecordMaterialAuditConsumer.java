package com.shuidihuzhu.cf.admin.mq.record;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfQuestionnaireBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.model.QuestionnaireRecord;
import com.shuidihuzhu.cf.model.admin.CfQuestionnaire;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.client.cf.admin.model.CfQuestionnaireVo;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.constant.CfClientMQTopicCons;
import com.shuidihuzhu.client.model.event.InfoApproveEvent;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @DATE 2020/2/12
 */
@Slf4j
@Service
@RefreshScope
@RocketMQListener(id = "cailiao_"+CfClientMQTagCons.INFO_APPROVE_MSG,
        tags = CfClientMQTagCons.INFO_APPROVE_MSG,
        topic = CfClientMQTopicCons.CF,
        group = "record_cailiao_audit_group")
public class RecordMaterialAuditConsumer implements MessageListener<InfoApproveEvent> {

    @Autowired
    private CfQuestionnaireBiz cfQuestionnaireBiz;

    @Resource
    private AdminCfInfoExtBiz adminCfInfoExtBiz;

    @Autowired
    private ApplicationService applicationService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<InfoApproveEvent> mqMessage) {


        InfoApproveEvent event = mqMessage.getPayload();

        if (event == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        log.info("RecordMaterialAuditConsumer event={}",event);

        //通过才处理
        if (event.getStatus() == CrowdfundingStatus.CROWDFUNDING_STATED.value()) {

            int caseId = event.getCaseId();

            QuestionnaireRecord qr = cfQuestionnaireBiz.canSend(caseId, CfQuestionnaireBiz.cailiao_source);

            if (qr.isFlag()) {

                Date sendTime = getSendTime(event.getCaseId(), qr.getCard());
                long id = cfQuestionnaireBiz.save(qr.getUserId(), caseId, qr.getChannel(), qr.getCard(), qr.getMobile(),CfQuestionnaireBiz.cailiao_source, sendTime);
                qr.setId(id);
                cfQuestionnaireBiz.sendDelayMQForMsg(qr, (sendTime.getTime() - System.currentTimeMillis()) /  1000L);
            }
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private Date getSendTime(int caseId, String card) {

        if (applicationService.isDevelopment()) {
            return DateUtils.addMinutes(new Date(), 3);
        }

        CfInfoExt cfInfoExt = adminCfInfoExtBiz.getByCaseId(caseId);
        if (cfInfoExt != null && cfInfoExt.getFirstApproveTime() != null) {
            Timestamp firstApproveTime = cfInfoExt.getFirstApproveTime();
            int hour = com.shuidihuzhu.common.util.DateUtil.getHoursBetween(firstApproveTime, new Timestamp(System.currentTimeMillis()));
            if (hour <= 24) {
                Date chuciSendTime = getChuciSendTime(card);
                return chuciSendTime == null ? DateUtils.addHours(firstApproveTime, 24) : DateUtils.addHours(chuciSendTime, 24);
            }
        }

        Date now = new Date();
        Date todayZero = DateUtils.truncate(now, Calendar.DATE);
        Date today_06_00 = DateUtils.addHours(todayZero, 6);
        Date today_18_30 = DateUtils.addMinutes(DateUtils.addHours(todayZero, 18), 30);

        Date sendTime = null;
        if (now.before(today_06_00)) {
            sendTime = DateUtils.addHours(todayZero, 9);
        } else if (now.before(today_18_30)) {
            sendTime = DateUtils.addHours(now, 3);
        } else {
            sendTime = DateUtils.addHours(DateUtils.addDays(todayZero, 1), 9);
        }
        return sendTime;
    }

    private Date getChuciSendTime(String card) {
        if (StringUtils.isBlank(card)) {
            return null;
        }

        List<CfQuestionnaire> questionnaires = cfQuestionnaireBiz.getListByCard(card);
        if (CollectionUtils.isEmpty(questionnaires)) {
            return null;
        }

        Optional<CfQuestionnaire> optional = questionnaires.stream().filter(r -> CfQuestionnaireVo.chuci_source.equals(r.getSource())).findFirst();

        return optional.map(CfQuestionnaire::getSendTime).orElse(null);
    }
}

