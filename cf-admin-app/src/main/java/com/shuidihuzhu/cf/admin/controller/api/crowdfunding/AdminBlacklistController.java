package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.model.service.MobileUserIdModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CfSensitiveWordRecordBiz;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CfBlacklistEnum;
import com.shuidihuzhu.cf.model.admin.BlacklistUserInfoVo;
import com.shuidihuzhu.cf.model.admin.UserSensitiveWordsInfoView;
import com.shuidihuzhu.cf.model.crowdfunding.CfSensitiveWordRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingBlackList;
import com.shuidihuzhu.client.cf.risk.client.CfRiskBlackListClient;
import com.shuidihuzhu.client.grpc.account.v1.feign.SimpleUserAccountServiceClient;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by Ahrievil on 2017/11/5
 */
@Api(value = "/admin/cf/blacklist", description = "sea后台黑名单处理")
@Slf4j
@Controller
@RequestMapping(path = "/admin/cf/blacklist")
public class AdminBlacklistController {

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Autowired
    private CfSensitiveWordRecordBiz cfSensitiveWordRecordBiz;
    @Resource(name = "cfRedissonHandler")
    private RedissonHandler cfRedissonHandler;
    @Resource
    private SimpleUserAccountServiceClient simpleUserAccountServiceClient;
    @Autowired
    private CfRiskBlackListClient cfRiskBlackListClient;

//    @ApiOperation(value = "列表数据", notes = "列表", response = Response.class, responseContainer = "Map")
//    @RequestMapping(path = "/get-list", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
//    @ResponseBody
//    @RequiresPermission("blacklist:get-list")
//    public Response getList(@ApiParam(value = "blackUserId") Long blackUserId,
//                            @ApiParam(value = "mobile") @RequestParam(defaultValue = "") String mobile,
//                            @ApiParam(value = "limitType") @RequestParam(defaultValue = "0") int limitType,
//                            @ApiParam(value = "pageSize") @RequestParam(name = "pageSize", defaultValue = "" + PageCons.DEFAULT_PAGE_SIZE) Integer pageSize,
//                            @ApiParam(value = "current") @RequestParam(name = "current", defaultValue = "1") Integer current) {
//        log.info("AdminBlacklistController getList mobile:{}, nickname:{}, userId:{}, limitType:{}, pageSize:{}, current:{}",
//                mobile, "", ContextUtil.getAdminUserId(), limitType, pageSize, current);
//        Set<Long> userIds = this.getUserId(blackUserId, mobile);
//        CfRiskBlackListEnum.LimitType limitTypeEnum = CfRiskBlackListEnum.LimitType.getTypeByValue(limitType);
//        Response<CfBlacklistBaseResult> blacklistRes = cfRiskBlackListClient.selectByPage(current, pageSize, userIds, limitTypeEnum.getValue());
//
//        if(ErrorCode.SUCCESS.getCode() != blacklistRes.getCode() || Objects.isNull(blacklistRes.getData())){
//           return ResponseUtil.makeError(ErrorCode.SYSTEM_ERROR);
//        }
//
//        CfBlacklistBaseResult blacklistBaseResult = blacklistRes.getData();
//        List<CfRiskBlacklistDO> blacklistDOS = blacklistBaseResult.getBlacklistDOS();
//        Map<String, Object> pagination = blacklistBaseResult.getPagination();
//
//        List<UserInfoModel> userByUserIds = accountService.getUserInfoByUserIdBatch(blacklistDOS.stream().map(val ->  val.getUserId()).distinct().collect(Collectors.toList()));
//        Map<Long, UserInfoModel> collects = userByUserIds.stream().collect(Collectors.toMap(UserInfoModel::getUserId, Function.identity()));
//
//        List<CfBlacklistVo> blackListVos = blacklistDOS.stream().map(val -> {
//            Long userId1 = val.getUserId();
//            UserInfoModel userInfoModel = collects.get(userId1);
//            String nick = "";
//            if (userInfoModel != null) {
//                nick = userInfoModel.getNickname();
//            }
//            return new CfBlacklistVo(val, nick);
//        }).collect(Collectors.toList());
//
//        Map<String, Object> result = Maps.newHashMap();
//        result.put("pagination", pagination);
//        result.put("list", blackListVos);
//        return NewResponseUtil.makeSuccess(result);
//    }

//    @ApiOperation(value = "添加黑名单", notes = "添加黑名单", response = Response.class)
//    @RequestMapping(path = "add", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
//    @ResponseBody
//    @RequiresPermission("blacklist:add")
//    public Response add(@ApiParam(value = "blackUserId", required = true) Long blackUserId,
//                        @ApiParam(value = "limitType", required = true) int limitType,
//                        @ApiParam(value = "reason") @RequestParam(defaultValue = "") String reason) {
//        int userId = ContextUtil.getAdminUserId();
//        if(userId <= 0){
//            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
//        }
//        log.info("AdminBlacklistController add userId:{}, blackUserId:{}, limitType:{}, reason:{}", userId, blackUserId, limitType, reason);
//
//        CfRiskBlackListEnum.LimitType limitTypeEnum = CfRiskBlackListEnum.LimitType.getTypeByValue(limitType);
//        if(blackUserId <= 0 || Objects.isNull(limitTypeEnum)){
//            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
//        }
//
//        CfRiskBlacklistParam param = new CfRiskBlacklistParam();
//        param.setUserId(blackUserId);
//        param.setContent(reason);
//        param.setMode(CfRiskBlackListEnum.Mode.ONLY_HIMSELF.getValue());
//        param.setLimitRange(CfRiskBlackListEnum.LimitRange.SYSTEM.getValue());
//        param.setLimitType(limitTypeEnum.getValue());
//        param.setLimitPart(limitTypeEnum == CfRiskBlackListEnum.LimitType.UGC ? CfBlacklistEnum.LimitPart.BASE_INFO.getValue() : CfBlacklistEnum.LimitPart.FUNDRAISING.getValue());
//        param.setOperatorId(userId);
//
//        Response<String> response = cfRiskBlackListClient.saveBlacklist(param);
//        if(ErrorCode.SUCCESS.getCode() != response.getCode()){
//            return NewResponseUtil.makeResponse(response.getCode(), response.getMsg(), null);
//        }
//
//        return NewResponseUtil.makeSuccess(null);
//    }

    @ApiOperation(value = "查询用户的信息", notes = "查询信息，包括敏感词命中的信息", response = Response.class, responseContainer = "Map")
    @RequestMapping(path = "get-user-info", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response getUserInfo(@ApiParam(value = "blackUserId") Long blackUserId,
                                @ApiParam(value = "mobile") @RequestParam(defaultValue = "") String mobile,
                                @ApiParam(value = "limitType", required = true) @RequestParam(defaultValue = "1") int limitType) {
        log.info("AdminBlacklistController getUserInfo userId:{}, mobile:{}, limitType:{}", ContextUtil.getAdminUserId(), mobile, limitType);
        Set<Long> userIds = Sets.newHashSet();
        try {
            userIds.addAll(this.getUserId(blackUserId, mobile));
        } catch (Exception e) {
            log.error("error msg", e);
        }
        Map<String, Object> result = Maps.newHashMap();
        if (CollectionUtils.isEmpty(userIds)) {
            result.put("list", Lists.newArrayList());
            return NewResponseUtil.makeSuccess(result);
        }
        List<UserInfoModel> userInfoByUserIds = userInfoServiceBiz.getUserInfoByUserIdBatch(Lists.newArrayList(userIds));
        switch (CfBlacklistEnum.LimitType.getTypeByValue(limitType)) {
            case UGC:
                Map<Long, UserInfoModel> userInfoModelMap = userInfoByUserIds.stream()
                        .collect(Collectors.toMap(UserInfoModel::getUserId, Function.identity()));
                List<CfSensitiveWordRecord> cfSensitiveWordRecords = cfSensitiveWordRecordBiz.selectByUserId(userIds);
                Map<Long, List<CfSensitiveWordRecord>> userIdSensitiveWordListMap = cfSensitiveWordRecords.stream()
                        .collect(Collectors.groupingBy(CfSensitiveWordRecord::getUserId));
                List<UserSensitiveWordsInfoView> collect = userIds.stream().map(val -> {
                    UserInfoModel userInfoModel = userInfoModelMap.get(val);
                    List<CfSensitiveWordRecord> cfSensitiveWordRecordList = userIdSensitiveWordListMap.get(val);
                    return new UserSensitiveWordsInfoView(userInfoModel.getUserId(), userInfoModel.getNickname(), cfSensitiveWordRecordList);
                }).collect(Collectors.toList());
                result.put("list", collect);
                break;
            case FUNDRAISING:
                List<BlacklistUserInfoVo> resultList = userInfoByUserIds.stream()
                        .map(val -> new BlacklistUserInfoVo(val.getUserId(), val.getNickname())).collect(Collectors.toList());
                result.put("list", resultList);
                break;
            default:
        }

        return NewResponseUtil.makeSuccess(result);
    }

//    @ApiOperation(value = "删除黑名单", notes = "去掉用户的黑名单", response = Response.class)
//    @RequestMapping(path = "delete", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
//    @ResponseBody
//    public Response delete(@ApiParam(value = "blackUserId", required = true) @RequestParam(defaultValue = "0") Long blackUserId,
//                           @ApiParam(value = "limitType", required = true) @RequestParam(required = true) int limitType,
//                           @ApiParam(value = "reason") @RequestParam(defaultValue = "") String reason) {
//        int userId = ContextUtil.getAdminUserId();
//        if(userId <= 0){
//            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
//        }
//
//        log.info("AdminBlacklistController delete blackUserId:{}, userId:{}, reason:{}", blackUserId, userId, reason);
//
//        CfRiskBlackListEnum.LimitType limitTypeEnum = CfRiskBlackListEnum.LimitType.getTypeByValue(limitType);
//        if(blackUserId <= 0 || Objects.isNull(limitTypeEnum)){
//            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
//        }
//
//        CfRiskBlacklistParam param = new CfRiskBlacklistParam();
//        param.setUserId(blackUserId);
//        param.setLimitType(limitTypeEnum.getValue());
//        param.setContent(reason);
//        param.setOperatorId(userId);
//
//        Response<String> response = cfRiskBlackListClient.removeBlacklist(param);
//
//        if(ErrorCode.SUCCESS.getCode() != response.getCode()){
//            return NewResponseUtil.makeResponse(response.getCode(), response.getMsg(), null);
//        }
//
//        return NewResponseUtil.makeSuccess(null);
//    }

    @RequestMapping(path = "refresh", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response refresh(String code) {
        log.info("AdminBlacklistController refresh userId:{}, code:{}", ContextUtil.getAdminUserId(), code);
        if (!"balalanengliang".equals(code)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        this.removeRedis();
        return NewResponseUtil.makeSuccess(null);
    }

    private Set<Long> getUserId(Long blackUserId, String mobile) {
        Set<Long> userIds = Sets.newHashSet();
        if (blackUserId != null && blackUserId > 0) {
            UserInfoModel baseUserInfoByUserId = userInfoServiceBiz.getUserInfoByUserId(blackUserId);
            if (baseUserInfoByUserId != null) {
                userIds.add(baseUserInfoByUserId.getUserId());
            }
        }
        if (StringUtils.isNotBlank(mobile)) {
            MobileUserIdModel userIdByMobile = simpleUserAccountServiceClient.getUserIdByMobile(mobile);
            if (userIdByMobile != null) {
                userIds.add(userIdByMobile.getUserId());
            }
        }
        return userIds;
    }

    private void removeRedis() {
        try {
            cfRedissonHandler.del("cf-blacklist");
            log.info("AdminBlacklistController removeRedis");
        } catch (Exception e) {
            log.error("error msg", e);
        }
    }

    public void handleBlacklistModel(CrowdfundingBlackList crowdfundingBlackList, CfBlacklistEnum.LimitType limitType) {
        CfBlacklistEnum.Mode mode = CfBlacklistEnum.Mode.ONLY_HIMSELF;
        CfBlacklistEnum.LimitRange limitRange = CfBlacklistEnum.LimitRange.SYSTEM;
        CfBlacklistEnum.LimitPart limitPart = CfBlacklistEnum.LimitPart.NO;
        switch (limitType) {
            case UGC:
                limitPart = CfBlacklistEnum.LimitPart.BASE_INFO;
                break;
            case FUNDRAISING:
                limitPart = CfBlacklistEnum.LimitPart.FUNDRAISING;
                break;
            default:
        }
        crowdfundingBlackList.setMode(mode.getValue());
        crowdfundingBlackList.setLimitRange(limitRange.getValue());
        crowdfundingBlackList.setLimitType(limitType.getValue());
        crowdfundingBlackList.setLimitPart(limitPart.getValue());
    }

}
