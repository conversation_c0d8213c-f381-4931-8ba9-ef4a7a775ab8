package com.shuidihuzhu.cf.admin.controller.api.disease;

import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.disease.DiseaseKnowledge;
import com.shuidihuzhu.cf.model.disease.DiseaseKnowledgeRecord;
import com.shuidihuzhu.cf.service.disease.DiseaseKnowledgeService;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/admin/cf/disease-knowledge")
public class DiseaseKnowledgeController {


    @Resource
    private DiseaseKnowledgeService diseaseKnowledgeService;

    @RequestMapping(path = "/insert", method = RequestMethod.POST)
    @RequiresPermission("disease-knowledge:insert")
    public Response<Integer> insert(@RequestParam("diseaseNorm") String diseaseNorm,
                                    @RequestParam("diseaseIntro") String diseaseIntro,
                                    @RequestParam("cureAndCost") String cureAndCost,
                                    @RequestParam("prognosis") String prognosis) {

        if (StringUtils.isAnyBlank(diseaseNorm, diseaseIntro, cureAndCost, prognosis)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        long adminUserId = ContextUtil.getAdminLongUserId();

        return diseaseKnowledgeService.insert(diseaseNorm, diseaseIntro, cureAndCost, prognosis, adminUserId);
    }

    @RequestMapping(path = "/update", method = RequestMethod.POST)
    @RequiresPermission("disease-knowledge:update")
    public Response<Integer> update(@RequestParam("id") long id,
                                    @RequestParam("diseaseIntro") String diseaseIntro,
                                    @RequestParam("cureAndCost") String cureAndCost,
                                    @RequestParam("prognosis") String prognosis) {

        if (id <= 0 || StringUtils.isAnyBlank(diseaseIntro, cureAndCost, prognosis)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        long adminUserId = ContextUtil.getAdminLongUserId();

        return diseaseKnowledgeService.update(id, diseaseIntro, cureAndCost, prognosis, adminUserId);
    }

    @RequestMapping(path = "/delete", method = RequestMethod.POST)
    @RequiresPermission("disease-knowledge:delete")
    public Response<Integer> delete(@RequestParam("id") long id) {

        if (id <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        long adminUserId = ContextUtil.getAdminLongUserId();

        return diseaseKnowledgeService.delete(id, adminUserId);
    }

    @RequestMapping(path = "/select-list", method = RequestMethod.POST)
    @RequiresPermission("disease-knowledge:select-list")
    public Response<Map<String, Object>> selectList(@RequestParam(value = "diseaseNorm", required = false, defaultValue = "") String diseaseNorm,
                                                    @RequestParam(value = "current", required = false, defaultValue = "1") int current,
                                                    @RequestParam(value = "pageSize", required = false, defaultValue = "10") int pageSize) {
        return diseaseKnowledgeService.selectList(diseaseNorm, current, pageSize);
    }

    @RequestMapping(path = "/select", method = RequestMethod.POST)
    @RequiresPermission("disease-knowledge:select")
    public Response<DiseaseKnowledge> select(@RequestParam(value = "diseaseNorm") String diseaseNorm) {
        return diseaseKnowledgeService.select(diseaseNorm);
    }

    @RequestMapping(path = "/select-record-list", method = RequestMethod.POST)
    @RequiresPermission("disease-knowledge:select-record-list")
    public Response<List<DiseaseKnowledgeRecord>> selectRecordList(@RequestParam(value = "id") int id) {
        return diseaseKnowledgeService.selectRecordList(id);
    }


}
