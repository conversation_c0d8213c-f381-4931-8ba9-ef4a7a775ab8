package com.shuidihuzhu.cf.admin.controller.api.innerapi.redis.inner;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.FeignClientBuilder;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Component
public class CfRedisToolManagerImpl {
    @Autowired
    private ApplicationContext applicationContext;

    private ConcurrentHashMap<String/** appName**/, CfRedisToolInnerFeignClient> cfRedisToolInnerFeignClients = new ConcurrentHashMap<>(50);


    public CfRedisToolInnerFeignClient getByAppName(String appName) {
        return cfRedisToolInnerFeignClients.computeIfAbsent(appName,
                t -> {
                    FeignClientBuilder.Builder builder = new FeignClientBuilder(applicationContext).forType(CfRedisToolInnerFeignClient.class, appName);
                    builder.fallback(CfRedisToolInnerFeignClientFallback.class);
                    return (CfRedisToolInnerFeignClient) builder.build();
                }
        );
    }
}
