package com.shuidihuzhu.cf.admin.controller.api.crowdfunding.approve;

import com.shuidihuzhu.cf.admin.util.admin.AdminCfIdCardUtil;
import com.shuidihuzhu.cf.admin.util.lock.RedisDistributedLock;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingAuthorBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.material.feign.CfPayeeMaterialClient;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.BankCardVerifyStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoHospitalPayee;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoPayee;
import com.shuidihuzhu.cf.model.crowdfunding.approve.CrowdfundingInfoHospitalPayeeParam;
import com.shuidihuzhu.cf.model.crowdfunding.approve.CrowdfundingInfoPayeeParam;
import com.shuidihuzhu.cf.param.DepositPayeeAccountParam;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.cf.util.BackCardUtil;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.enums.Platform;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.IdCardUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Objects;

/**
 * @Author: wangpeng
 * @Date: 2021/2/26 14:06
 * @Description:
 */
@RestController
@Slf4j
@RequestMapping("admin/cf/approve/payee")
public class ApprovePayeeController extends AbstractApproveController {

    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Resource
    private ApproveRemarkOldService approveRemarkOldService;

    @Resource
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;

    @Resource
    private CfPayeeMaterialClient cfPayeeMaterialClient;

    @Resource
    private AdminCrowdfundingAuthorBiz adminCrowdfundingAuthorBiz;

    @Resource
    private Producer producer;

    @RequiresPermission("approve-payee:hospital-update")
    @PostMapping("hospital-update")
    @ApiOperation("修改收款人信息（医院）")
    @RedisDistributedLock(key = "approve_hospital_payee_update_infoId_#{payeeParam.infoUuid}")
    public Response<Void> hospitalPayeeUpdate(@RequestBody CrowdfundingInfoHospitalPayeeParam payeeParam) {

        Response<Void> voidResponse = validateHospitalPayeeUpdateParam(payeeParam);
        if (voidResponse.notOk()) {
            return voidResponse;
        }

        String infoUuid = payeeParam.getInfoUuid();
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (Objects.isNull(crowdfundingInfo)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (Objects.equals(crowdfundingInfo.getStatus(), CrowdfundingStatus.CROWDFUNDING_STATED) || crowdfundingInfo.getEndTime().before(new Date())) {
            return NewResponseUtil.makeError(ErrorCode.CF_CAN_NOT_EDIT);
        }

        payeeParam.setRelationType(CrowdfundingRelationType.getCode(CrowdfundingRelationType.LOCATION_HOSPITAL_ACCOUNT));
        CrowdfundingInfoHospitalPayee crowdfundingInfoHospitalPayee = crowdfundingDelegate.getCrowdfundingInfoHospitalPayeeByInfoUuid(infoUuid);
        CrowdfundingInfoPayee crowdfundingInfoPayee = crowdfundingDelegate.getCrowdfundingInfoPayeeByInfoUuid(infoUuid);

        // 操作备注
        String payeeUpdateComment = handlerHospitalPayeeUpdateComment(crowdfundingInfo, crowdfundingInfoPayee, crowdfundingInfoHospitalPayee, payeeParam);
        if (StringUtils.isEmpty(payeeUpdateComment)) {
            return NewResponseUtil.makeError(AdminErrorCode.NON_UPDATE_ERROR);
        }
        approveRemarkOldService.add(crowdfundingInfo.getId(), ContextUtil.getAdminUserId(), payeeUpdateComment);
        // 更新收款人医院信息
        int updateResult;
        if (Objects.isNull(crowdfundingInfoHospitalPayee)) {
            CrowdfundingInfoHospitalPayee payee = new CrowdfundingInfoHospitalPayee();
            BeanUtils.copyProperties(payeeParam, payee);
            updateResult = crowdfundingDelegate.addCrowdfundingInfoHospitalPayee(payee);
        } else {
            payeeParam.setId(crowdfundingInfoHospitalPayee.getId());
            BeanUtils.copyProperties(payeeParam, crowdfundingInfoHospitalPayee);
            updateResult = crowdfundingDelegate.updateCrowdfundingInfoHospitalPayee(crowdfundingInfoHospitalPayee);
        }

        if (updateResult <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_OPERATION_FAILED);
        }

        // 更新案例信息
        crowdfundingInfo.setRelationType(CrowdfundingRelationType.LOCATION_HOSPITAL_ACCOUNT);
        crowdfundingInfoBiz.updateRelationType(crowdfundingInfo);

        //更新素材
        cfPayeeMaterialClient.addOrUpdateHospitalAccount(crowdfundingInfo.getId(), crowdfundingInfoHospitalPayee, new HashMap<>());
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("approve-payee:update")
    @PostMapping("update")
    @ApiOperation("修改收款人信息（个人）")
    @RedisDistributedLock(key = "approve_payee_update_infoId_#{payeeParam.infoUuid}")
    public Response<Void> payeeUpdate(@RequestBody CrowdfundingInfoPayeeParam payeeParam) {
        if (Objects.isNull(payeeParam) || StringUtils.isEmpty(payeeParam.getInfoUuid())) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        String infoUuid = payeeParam.getInfoUuid();
        String payeeName = StringUtils.trim(payeeParam.getPayeeName());
        payeeParam.setPayeeName(StringUtils.trimToEmpty(payeeName));
        String bankCard = StringUtils.trim(payeeParam.getBankCard());
        String bankName = StringUtils.trim(payeeParam.getBankName());
        String idCard = StringUtils.trim(payeeParam.getIdCard());
        CrowdfundingRelationType relationType = payeeParam.getRelationType();

        payeeParam.setIdType(UserIdentityType.identity);

        String bankBranchName = StringUtils.trim(StringUtils.isEmpty(payeeParam.getBankBranchName()) ? "" : payeeParam.getBankBranchName());
        String cnapsBranchId = StringUtils.trim(StringUtils.isEmpty(payeeParam.getCnapsBranchId()) ? "" : payeeParam.getCnapsBranchId());

        if (StringUtils.isBlank(bankName) || StringUtils.isBlank(payeeName) || relationType == null) {
            return NewResponseUtil.makeError(CfErrorCode.CF_INFO_PARAM_ERROR_PAYEE);
        }
        if (StringUtils.isBlank(bankCard) || !BackCardUtil.checkBankCard(bankCard)) {
            return NewResponseUtil.makeError(CfErrorCode.CF_BANK_CARD_VERIFY_FAILED);
        }
        if (StringUtils.isEmpty(idCard)) {
            return NewResponseUtil.makeError(CfErrorCode.CF_INFO_PARAM_ERROR_PAYEE);
        }
        if (IdCardUtil.illegal(idCard)) {
            return NewResponseUtil.makeError(CfErrorCode.CF_PARAM_ERROR_PAYEE_ID_CARD_ERROR);
        }
        if (!Objects.equals(relationType, CrowdfundingRelationType.self) && payeeParam.getRelativesType() <= 0) {
            return NewResponseUtil.makeError(CfErrorCode.PAYEE_RELATION_ERROR);
        }
        //身份证不足18位不让提交（过滤掉15位的身份证）
        if (!AdminCfIdCardUtil.isValidIdCard(idCard)) {
            return NewResponseUtil.makeError(CfErrorCode.USER_INFO_ID_CARD_ERROR);
        }
        if (AdminCfIdCardUtil.getAge(idCard) < 18) {
            return NewResponseUtil.makeError(CfErrorCode.PAYEE_NOT_ALLOW_OF_AGE);
        }
        idCard = idCard.replaceAll("x","X");
        payeeParam.setIdCard(idCard);

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (Objects.isNull(crowdfundingInfo)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (Objects.equals(crowdfundingInfo.getStatus(), CrowdfundingStatus.CROWDFUNDING_STATED) || crowdfundingInfo.getEndTime().before(new Date())) {
            return NewResponseUtil.makeError(ErrorCode.CF_CAN_NOT_EDIT);
        }

        CrowdfundingAuthor author = adminCrowdfundingAuthorBiz.get(crowdfundingInfo.getId());
        if (Objects.equals(relationType, CrowdfundingRelationType.self) && Objects.nonNull(author) && Objects.equals(author.getIdType(), UserIdentityType.identity)) {
            String decrypt = shuidiCipher.decrypt(author.getCryptoIdCard());
            if (!StringUtils.equals(decrypt, idCard) || !StringUtils.equals(payeeName, author.getName())) {
                return NewResponseUtil.makeError(AdminErrorCode.AUTHOR_PAYEE_NOT_EQUALS);
            }
        }

        DepositPayeeAccountParam depositPayeeAccountParam = new DepositPayeeAccountParam();
        depositPayeeAccountParam.setBankBranchName(bankBranchName);
        depositPayeeAccountParam.setPayeeName(payeeParam.getPayeeName());
        depositPayeeAccountParam.setBankCard(payeeParam.getBankCard());
        depositPayeeAccountParam.setBankName(payeeParam.getBankName());
        depositPayeeAccountParam.setCaseId(crowdfundingInfo.getId());
        depositPayeeAccountParam.setInfoUuid(crowdfundingInfo.getInfoId());
        depositPayeeAccountParam.setIdCard(payeeParam.getIdCard());
        depositPayeeAccountParam.setCnapsBranchId(cnapsBranchId);
        depositPayeeAccountParam.setUserId(crowdfundingInfo.getUserId());
        depositPayeeAccountParam.setPlatform(Platform.OTHER);
        depositPayeeAccountParam.setClientIp(ContextUtil.getClientIp());
        Response<Void> checkAcctResponse = crowdfundingDelegate.checkOrOpenWithDrawAcct(depositPayeeAccountParam);
        if (checkAcctResponse.notOk()) {
            return checkAcctResponse;
        }

        String cryptoIdCard = oldShuidiCipher.aesEncrypt(idCard);
        String cryptoBankCard = oldShuidiCipher.aesEncrypt(bankCard);

        CrowdfundingInfoPayee crowdfundingInfoPayee = crowdfundingDelegate.getCrowdfundingInfoPayeeByInfoUuid(infoUuid);

        // 操作备注
        String payeeUpdateComment = handlerPayeeUpdateComment(crowdfundingInfoPayee, payeeParam, crowdfundingInfo);
        if (StringUtils.isEmpty(payeeUpdateComment)) {
            return NewResponseUtil.makeError(AdminErrorCode.NON_UPDATE_ERROR);
        }
        approveRemarkOldService.add(crowdfundingInfo.getId(), ContextUtil.getAdminUserId(), payeeUpdateComment);

        if (Objects.isNull(crowdfundingInfoPayee)) {
            crowdfundingInfoPayee = new CrowdfundingInfoPayee();
            crowdfundingInfoPayee.setBankCard(cryptoBankCard);
            crowdfundingInfoPayee.setBankName(bankName);
            crowdfundingInfoPayee.setIdCard(cryptoIdCard);
            crowdfundingInfoPayee.setIdType(UserIdentityType.identity.getCode());
            crowdfundingInfoPayee.setInfoUuid(infoUuid);
            crowdfundingInfoPayee.setName(payeeName);
            crowdfundingInfoPayee.setRelationType(CrowdfundingRelationType.getCode(relationType));
            crowdfundingInfoPayee.setCaseId(crowdfundingInfo.getId());
            crowdfundingInfoPayee.setRelativesType(payeeParam.getRelativesType());

            crowdfundingInfoPayee.setBankBranchName("");

            crowdfundingInfoPayee.setMobile("");
            crowdfundingInfoPayee.setEmergency("");
            crowdfundingInfoPayee.setEmergencyPhone("");
            crowdfundingInfoPayee.setFaceIdResult(0);

            crowdfundingDelegate.addCrowdfundingInfoPayee(crowdfundingInfoPayee);

        } else {
            crowdfundingInfoPayee.setBankBranchName("");
            crowdfundingInfoPayee.setBankCard(cryptoBankCard);
            crowdfundingInfoPayee.setBankName(bankName);
            crowdfundingInfoPayee.setIdCard(cryptoIdCard);
            crowdfundingInfoPayee.setIdType(UserIdentityType.identity.getCode());
            crowdfundingInfoPayee.setName(payeeName);
            crowdfundingInfoPayee.setRelationType(CrowdfundingRelationType.getCode(relationType));
            crowdfundingInfoPayee.setRelativesType(payeeParam.getRelativesType());

            crowdfundingDelegate.updateCrowdfundingInfoPayee(crowdfundingInfoPayee);
        }

        crowdfundingInfo.setPayeeName(StringUtils.trimToEmpty(payeeName));
        crowdfundingInfo.setPayeeIdCard(cryptoIdCard);
        crowdfundingInfo.setPayeeBankName(bankName);
        crowdfundingInfo.setPayeeBankBranchName("");
        crowdfundingInfo.setPayeeBankCard(cryptoBankCard);
        crowdfundingInfo.setRelationType(relationType);
        crowdfundingInfo.setRelation(relationType.getDescription());
        crowdfundingInfo.setBankCardVerifyStatus(BankCardVerifyStatus.passed);
        crowdfundingInfo.setBankCardVerifyMessage("认证通过");
        crowdfundingInfo.setBankCardVerifyMessage2("认证通过");
        crowdfundingInfoBiz.updatePayeeInfo(crowdfundingInfo);

        // 收款人变更时 发送消息
        producer.send(new Message<>(MQTopicCons.CF,
                MQTagCons.CF_BASE_INFO_CHANGE_TAG, MQTagCons.CF_BASE_INFO_CHANGE_TAG + "-" +
                crowdfundingInfo.getId()
                + "-" + System.currentTimeMillis(), crowdfundingInfo.getId(), DelayLevel.S5));

        // 收款人变更，发消息给用户平台
        crowdfundingDelegate.sendPayeeInfoRelation(crowdfundingInfo);

        return NewResponseUtil.makeSuccess(null);
    }
}
