package com.shuidihuzhu.cf.admin.controller.api.risk.word;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.client.ugc.feign.CommonOperationRecordFeignClient;
import com.shuidihuzhu.cf.client.ugc.model.domain.risk.RiskControlCategoryQueryParam;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationRecordDO;
import com.shuidihuzhu.cf.client.ugc.service.RiskControlWordManageClient;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.client.ugc.model.domain.risk.RiskControlWordCategoryDO;
import com.shuidihuzhu.cf.domain.risk.CfRiskWordEnumResult;
import com.shuidihuzhu.cf.enums.AdminErrorCode;

import com.shuidihuzhu.common.web.enums.MyErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.admin.PageUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

import static com.shuidihuzhu.common.web.util.admin.PageUtil.MAX_PAGE_SIZE;

/**
 * <AUTHOR>
 * @date 2018-08-17  14:12
 * https://wiki.shuiditech.com/pages/viewpage.action?pageId=22092355
 */
@Api("风控词汇分类管理")
@RequestMapping("admin/cf/risk-control/word/category")
@Slf4j
@RestController
public class CfRiskControlWordCategoryController {

    private static final String DEFAULT_PAGE_SIZE = "20";

    @Autowired
    private RiskControlWordManageClient riskControlWordManageClient;

    @Autowired
    private CommonOperationRecordFeignClient operationFeignClient;

    @RequiresPermission("risk-control:word-category-search")
    @ApiOperation("搜索分类 取消分页")
    @PostMapping("search-by-condition")
    public Response<Map<String, Object>> searchByCondition(@RequestParam(required = false, defaultValue = "0") int type,
                                                           @RequestParam(required = false) String key,
                                                           @RequestParam(required = false, defaultValue = "1") Integer current,
                                                           @RequestParam(required = false, defaultValue = DEFAULT_PAGE_SIZE) Integer pageSize,
                                                           @RequestParam(required = false, defaultValue = "0") int useScene) {
        if (pageSize > MAX_PAGE_SIZE) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        RiskControlCategoryQueryParam queryParam = new RiskControlCategoryQueryParam();
        queryParam.setType(type);
        queryParam.setContent(key);
        queryParam.setUseScene(useScene);
        Response<List<RiskControlWordCategoryDO>> listResponse = riskControlWordManageClient.searchCategoryByParam(queryParam);
        if (listResponse.notOk()) {
            log.error("searchByCondition fegin接口返回失败:{}", JSON.toJSONString(listResponse));
            return NewResponseUtil.makeSuccess(wrapPageData(Lists.newArrayList()));
        }

        return NewResponseUtil.makeSuccess(wrapPageData(listResponse.getData()));
    }

    @RequiresPermission("risk-control:word-category-list")
    @ApiOperation("获取某类型下所有分类")
    @PostMapping("list-by-type")
    public Response<List<RiskControlWordCategoryDO>> list(@RequestParam(required = false, defaultValue = "0") int type) {
        Response<List<RiskControlWordCategoryDO>> r = riskControlWordManageClient.searchByTypeAndKey(type, "");
        if (r.notOk()) {
            log.error("listRiskControlWordCategoryDOByType fegin接口返回失败:{}", JSON.toJSONString(r));
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        List<RiskControlWordCategoryDO> list = r.getData();
        //根据id升序
        if(CollectionUtils.isEmpty(list)){
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        list.sort(Comparator.comparing(RiskControlWordCategoryDO::getId));

        return NewResponseUtil.makeSuccess(list);
    }

    @RequiresPermission("risk-control:word-category-remove")
    @ApiOperation("删除分类")
    @PostMapping("remove")
    public Response<Void> remove(@RequestParam long id) {
        return riskControlWordManageClient.categoryRemoveById(id);
    }

    @RequiresPermission("risk-control:word-category-add")
    @ApiOperation("添加")
    @PostMapping("add")
    public Response<RiskControlWordCategoryDO> add(@RequestParam(required = false, defaultValue = "0") int type,
                        @RequestParam(required = false) String content) {
        return riskControlWordManageClient.categoryAdd(type, content);
    }

    @RequiresPermission("risk-control:word-category-add")
    @ApiOperation("添加")
    @PostMapping("add-v1")
    public Response<RiskControlWordCategoryDO> addV1(@RequestBody RiskControlWordCategoryDO categoryDO) {
        if (categoryDO == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        categoryDO.setUserId(ContextUtil.getAdminUserId());

        return riskControlWordManageClient.categoryAddV1(categoryDO);
    }

    @RequiresPermission("risk-control:select-enum-type")
    @ApiOperation("查询枚举")
    @PostMapping("select-enum-type")
    public Response<CfRiskWordEnumResult> selectEnumType() {


        return NewResponseUtil.makeSuccess(CfRiskWordEnumResult.result);
    }

    private Map<String, Object> wrapPageData(List dataList) {
        Map<String, Object> result = Maps.newHashMap();
        result.put("list", dataList);
        result.put("pagination", PageUtil.transform2PageMap(dataList));
        return result;
    }

}
