package com.shuidihuzhu.cf.admin.controller.api.innerapi;

import com.google.common.base.Splitter;
import com.shuidihuzhu.cf.delegate.OrganizationDelegate;
import com.shuidihuzhu.cf.facade.AdminApolloCofig;
import com.shuidihuzhu.client.cf.workorder.JuanzhanStatClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2020/6/23
 */
@RestController
@Slf4j
public class JuanzhuanStatFeignController {

    @Autowired
    private JuanzhanStatClient juanzhanStatClient;

    @Autowired
    private OrganizationDelegate organizationDelegate;


    @PostMapping("innerapi/cf/admin/stat/juanzhan")
    public Response<String> juanzhan(){

        juanzhanStatClient.juanzhanstat();

        String org = AdminApolloCofig.getValueFromApollo(AdminApolloCofig.cf_juanzhuan_stat_org,"");

        if (StringUtils.isNotBlank(org)){
            List<Integer> orgIds = Splitter.on(",").splitToList(org).stream().map(Integer::valueOf).collect(Collectors.toList());
            juanzhanStatClient.juanzhanstatOrg(orgIds);
        }

        return NewResponseUtil.makeSuccess("");
    }
}
