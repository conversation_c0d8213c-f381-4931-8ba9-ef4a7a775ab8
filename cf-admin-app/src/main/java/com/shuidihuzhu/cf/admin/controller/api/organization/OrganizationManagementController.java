package com.shuidihuzhu.cf.admin.controller.api.organization;

import com.google.common.collect.Maps;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.OrganizationClientV1;
import com.shuidihuzhu.cf.biz.admin.AdminOrganizationBiz;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.admin.AdminOrganizationUserMap;
import com.shuidihuzhu.cf.vo.crowdfunding.OrganizationEmployeesVo;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: WangYing on 2018/9/17
 */
@RestController
@RequestMapping(value = "/admin/cf/organization", produces = "application/json;charset=UTF-8")
@Slf4j
public class OrganizationManagementController {
    @Autowired
    private AdminOrganizationBiz adminOrganizationBiz;
    @Autowired
    private OrganizationClientV1 organizationClientV1;

    @RequiresPermission("organization:add")
    @ApiOperation("新增组织")
    @RequestMapping(path = "add-organization", method = RequestMethod.POST)
    public Object addOrganizationNode(String organizationName, @RequestParam(required = false, defaultValue = "0") int parentOrganizationId) {
        organizationName = organizationName.trim();
        if (StringUtils.isEmpty(organizationName)) {
            return NewResponseUtil.makeFail(AdminErrorCode.SYSTEM_PARAM_IS_NULL.getMsg());
        }
        if (adminOrganizationBiz.hasSameNameAndParentOrg(organizationName, parentOrganizationId)) {
            return NewResponseUtil.makeFail(AdminErrorCode.EXIST_SAME_ORGANIZATION_NAME.getMsg());
        }
        adminOrganizationBiz.addOrganizationNode(organizationName, parentOrganizationId);
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("organization:edit")
    @ApiOperation("编辑组织")
    @RequestMapping(path = "edit-organization", method = RequestMethod.POST)
    public Object editOrganizationNode(String newOrganizationName, int organizationId) {
        if (StringUtils.isEmpty(newOrganizationName)) {
            return NewResponseUtil.makeFail(AdminErrorCode.SYSTEM_PARAM_IS_NULL.getMsg());
        }
        newOrganizationName = newOrganizationName.trim();
        if (!newOrganizationName.equals(adminOrganizationBiz.getAdminOrganizationById(organizationId).getName()) && adminOrganizationBiz.hasSameNameAndParentOrg(newOrganizationName, organizationId)) {
            return NewResponseUtil.makeFail(AdminErrorCode.EXIST_SAME_ORGANIZATION_NAME.getMsg());
        }
        adminOrganizationBiz.editOrganizationNode(organizationId, newOrganizationName);
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("organization:delete")
    @ApiOperation("删除组织")
    @RequestMapping(path = "delete-organization", method = RequestMethod.POST)
    public Object deleteOrganizationNode(int organizationId) {
        if (CollectionUtils.isNotEmpty(adminOrganizationBiz.getAdminOrganizationByParentOrgId(organizationId))) {
            return NewResponseUtil.makeFail(AdminErrorCode.ORGANIZATION_HAS_SUB_ORGANIZATION.getMsg());
        }
        if (CollectionUtils.isNotEmpty(adminOrganizationBiz.getOrganizationEmployees(organizationId, 1, 1).getUserAccount())) {
            return NewResponseUtil.makeFail(AdminErrorCode.ORGANIZATION_HAS_EMPLOYEES.getMsg());
        }
        adminOrganizationBiz.deleteOrganizationNode(organizationId);
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("organization:get-tree")
    @ApiOperation("获取组织树")
    @RequestMapping(path = "get-organization-tree", method = RequestMethod.POST)
    public Object getOrganizationTree() {
        return NewResponseUtil.makeSuccess(adminOrganizationBiz.getOrganizationTree());
    }

    @RequiresPermission("organization:add-employee")
    @ApiOperation("新增组织员工")
    @RequestMapping(path = "add-organization-employee", method = RequestMethod.POST)
    public Object addEmployeeToNode(Integer uid, Integer organizationId) {
        if (Objects.isNull(uid) || Objects.isNull(organizationId)){
            return NewResponseUtil.makeSuccess(AdminErrorCode.SYSTEM_PARAM_IS_NULL);
        }

        if (null != adminOrganizationBiz.getAdminOrganizationUserMap(uid, organizationId)) {
            return NewResponseUtil.makeFail(AdminErrorCode.EMPLOYEE_EXIST_IN_ORGANIZATION.getMsg());
        }
        adminOrganizationBiz.addEmployeeToNode(uid, organizationId);
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("organization:delete-employee")
    @ApiOperation("删除组织员工")
    @RequestMapping(path = "delete-organization-employee", method = RequestMethod.POST)
    public Object deleteEmployeeFromNode(int uid, int organizationId) {
        adminOrganizationBiz.deleteEmployeeFromNode(uid, organizationId);
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("organization:set-manager")
    @ApiOperation("设置组织管理者")
    @RequestMapping(path = "set-organization-manager", method = RequestMethod.POST)
    public Object setManagerOfNode(int uid, int organizationId) {
        AdminOrganizationUserMap organizationUserMap = adminOrganizationBiz.getAdminOrganizationUserMap(uid, organizationId);
        if (null != organizationUserMap && 1 == organizationUserMap.getIsManager()) {
            return NewResponseUtil.makeFail(AdminErrorCode.EMPLOYEE_IS_ORGANIZATION_MANAGER.getMsg());
        }
        adminOrganizationBiz.setManagerOfNode(uid, organizationId);
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("organization:get-employee")
    @ApiOperation("获取组织内员工")
    @RequestMapping(path = "get-organization-employees", method = RequestMethod.POST)
    public Object getOrganizationEmployee(int organizationId, int current, int pageSize) {
        OrganizationEmployeesVo organizationEmployeesVo = adminOrganizationBiz.getOrganizationEmployees(organizationId, current, pageSize);
        Map<String, Object> result = Maps.newHashMap();
        Map<String, Object> pagination = Maps.newHashMap();
        pagination.put("current", current);
        pagination.put("pageSize", pageSize);
        int total = organizationEmployeesVo.getUserAccount().size();
        pagination.put("total", total);
        result.put("pagination", pagination);
        try {
            if (organizationEmployeesVo.getUserAccount().size() <= pageSize) {
                organizationEmployeesVo.setUserAccount(organizationEmployeesVo.getUserAccount());
            } else if (current * pageSize >= total) {
                organizationEmployeesVo.setUserAccount(organizationEmployeesVo.getUserAccount().subList((current - 1) * pageSize, total));
            } else {
                organizationEmployeesVo.setUserAccount(organizationEmployeesVo.getUserAccount().subList((current - 1) * pageSize, current * pageSize));
            }
        } catch (Exception e) {
            return NewResponseUtil.makeError(AdminErrorCode.PAGE_INFO_ERROR);
        }
        result.put("data", organizationEmployeesVo);
        return NewResponseUtil.makeSuccess(result);
    }

    @RequiresPermission("organization:get-managers")
    @ApiOperation("获取组织内管理者")
    @RequestMapping(path = "get-organization-managers", method = RequestMethod.POST)
    public Object getOrganizationManagers(int organizationId) {
        return NewResponseUtil.makeSuccess(adminOrganizationBiz.getOrganizationManagers(organizationId));
    }

    @RequiresPermission("organization:search-emplyees")
    @ApiOperation("搜索组织内员工")
    @RequestMapping(path = "search-organization-emplyees", method = RequestMethod.POST)
    public Object searchEmployeeByMis(int organizationId, String mis, int current, int pageSize) {

        if (organizationId == 0 || StringUtils.isBlank(mis) || current == 0 || pageSize == 0) {
            log.error("搜索组织内员工 参数错误 {}-{}-{}-{}", organizationId, mis, current, pageSize);
            return NewResponseUtil.makeFail("请输入要查找的用户名");
        }

        List<AdminUserAccountModel> adminUserAccounts = adminOrganizationBiz.searchEmployeesByMis(organizationId, mis);
        Map<String, Object> result = Maps.newHashMap();
        Map<String, Object> pagination = Maps.newHashMap();
        pagination.put("current", current);
        pagination.put("pageSize", pageSize);
        int total = adminUserAccounts.size();
        pagination.put("total", total);
        result.put("pagination", pagination);
        if (adminUserAccounts.size() <= pageSize) {
            result.put("data", adminUserAccounts);
        } if (current * pageSize >= total) {
            result.put("data", adminUserAccounts.subList((current - 1) * pageSize, total));
        } else {
            result.put("data", adminUserAccounts.subList((current - 1) * pageSize, current * pageSize));
        }
        return NewResponseUtil.makeSuccess(result);
    }

//    @RequiresPermission("organization:get-all")
//    @ApiOperation("获取组织-扁平的")
//    @RequestMapping(path = "get-all-organizations", method = RequestMethod.POST)
//    public Object getAllOrganizations() {
//        AuthRpcResponse authRpcResponse = organizationClientV1.getAllSimpleOrganizations();
//        if (authRpcResponse == null || !authRpcResponse.isSuccess()) {
//            return NewResponseUtil.makeResponse(-1,"系统繁忙，稍后刷新","");
//        }
//
//        return NewResponseUtil.makeSuccess(authRpcResponse.getResult());
//    }

}
