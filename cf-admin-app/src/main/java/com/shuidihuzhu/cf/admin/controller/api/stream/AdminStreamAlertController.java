package com.shuidihuzhu.cf.admin.controller.api.stream;

import com.shuidihuzhu.cf.service.stream.StreamActionConst;
import com.shuidihuzhu.cf.service.stream.manager.StreamData;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.async.DeferredResult;

/**
 * @description:
 * @author: zheng<PERSON>u
 * @date: 2021-08-10 20:00
 * TODO DELETE
 **/
@Deprecated
@RestController
@RequestMapping(path = "/admin/cf/stream")
@Slf4j
public class AdminStreamAlertController {

    @RequestMapping(path = "/alert", method = {RequestMethod.GET, RequestMethod.POST})
    public DeferredResult<Response<StreamData>> operator(Integer userId) {
        StreamData streamData = new StreamData();
        streamData.setFlag(StreamActionConst.RETRY);
        return new DeferredResult<>(40000L, () -> NewResponseUtil.makeSuccess(streamData));
    }


}
