package com.shuidihuzhu.cf.admin.controller.api.approve;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.risk.client.rpc.DiseaseClient;
import com.shuidihuzhu.cf.risk.model.risk.DiseaseAmountResultRecord;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationRecordDTO;
import com.shuidihuzhu.cf.risk.model.risk.diease.InfoReasonableAmountResultVo;
import com.shuidihuzhu.cf.risk.model.risk.diease.SpecialDiseaseChoiceInfoVo;
import com.shuidihuzhu.cf.service.approve.OCRApproveService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("admin/cf/ocr-approve")
@Slf4j
public class OcrApproveController {

    @Autowired
    private OCRApproveService ocrApproveService;

    @Resource
    private DiseaseClient diseaseClient;

    @ApiOperation("目标金额合理性自动判断---在初审中的应用 https://wiki.shuiditech.com/pages/viewpage.action?pageId=449577206")
    @PostMapping("ocr-disease-names")
    @RequiresPermission("ocr-approve:ocr-disease-names")
    public Response<String> ocrDiseaseNames(@RequestParam int caseId,
                                            @RequestParam long workOrderId,
                                            @RequestParam @ApiParam("原图url") String resourceUrl,
                                            @RequestParam @ApiParam("运营选择的图片地址") List<String> urls
    ) {
        int adminUserId = ContextUtil.getAdminUserId();
        return ocrApproveService.ocrDiseaseNames(caseId, workOrderId, resourceUrl, urls, adminUserId);
    }

    @ApiOperation("目标金额合理性自动判断---在初审中的应用 https://wiki.shuiditech.com/pages/viewpage.action?pageId=449577206")
    @PostMapping("analyse-disease-names")
    @RequiresPermission("ocr-approve:analyse-disease-names")
    public Response<SpecialDiseaseChoiceInfoVo> analyseDiseaseNames(@RequestParam int caseId,
                                                                    @RequestParam long workOrderId,
                                                                    @RequestParam @ApiParam("运营修改后的疾病名称") String diseaseNames,
                                                                    @RequestParam String specialRaiseChoiceInfo) {
        int adminUserId = ContextUtil.getAdminUserId();
        return ocrApproveService.analyseDiseaseNames(caseId, workOrderId, diseaseNames, adminUserId, specialRaiseChoiceInfo);
    }

    @ApiOperation("目标金额合理性自动判断---在初审中的应用 https://wiki.shuiditech.com/pages/viewpage.action?pageId=449577206")
    @PostMapping("analyse-special-raise")
    @RequiresPermission("ocr-approve:analyse-special-raise")
    public Response<SpecialDiseaseChoiceInfoVo> analyseSpecialRaise(@RequestParam int caseId,
                                                                    @RequestParam long workOrderId,
                                                                    @RequestParam @ApiParam("运营修改后的疾病名称") String diseaseNames) {
        int adminUserId = ContextUtil.getAdminUserId();
        return ocrApproveService.analyseSpecialRaise(caseId, workOrderId, diseaseNames, adminUserId);
    }


    @ApiOperation("计算未来花费 目标金额合理性自动判断---在初审中的应用 https://wiki.shuiditech.com/pages/viewpage.action?pageId=449577206" +
            "技术文档： https://wiki.shuiditech.com/pages/viewpage.action?pageId=477397483")
    @PostMapping("decide-info-amount")
    @RequiresPermission("ocr-approve:decide-info-amount")
    public Response<InfoReasonableAmountResultVo> decideInfoAmount(@RequestParam int caseId,
                                                                   @RequestParam long workOrderId,
                                                                   @RequestParam(required = false, defaultValue = "0")  int workOrderType,
                                                                   @RequestParam @ApiParam("疾病归一结果") List<String> classifyDiseaseNames,
                                                                   @RequestParam(required = false) @ApiParam("ocr识别结果疾病") String ocrDiseaseNames,
                                                                   @RequestParam(required = false) @ApiParam("原图url") String resourceUrl,
                                                                   @RequestParam(required = false) @ApiParam("运营选择的图片地址") List<String> urls,
                                                                   @RequestParam @ApiParam("运营修改后的疾病名称") String diseaseNames,
                                                                   @RequestParam(required = false, defaultValue = "") @ApiParam("其他的疾病信息") String otherInfo,
                                                                   @RequestParam String specialRaiseChoiceInfo,
                                                                   @RequestParam(required = false, defaultValue = "0") @ApiParam("工单处理状态") int handleResult) {
        int adminUserId = ContextUtil.getAdminUserId();
        log.info("decideInfoAmount caseId:{}, workOrderId:{} classifyDiseaseNames:{} ocrDiseaseNames:{}, " +
                        "resourceUrl:{} resourceUrl:{} urls:{}  diseaseNames:{} otherInfo:{}", caseId, workOrderId, JSON.toJSON(classifyDiseaseNames),
                ocrDiseaseNames, resourceUrl, JSON.toJSON(urls), JSON.toJSON(diseaseNames), otherInfo);
        return ocrApproveService.decideInfoAmount(caseId, workOrderId, workOrderType, adminUserId, ocrDiseaseNames, classifyDiseaseNames,
                resourceUrl, urls, otherInfo, diseaseNames, specialRaiseChoiceInfo, handleResult);
    }


    @ApiOperation("查看未来花费计算历史")
    @PostMapping("list-case-analyse-history")
    @RequiresPermission("ocr-approve:list-case-analyse-history")
    public Response<List<OperationRecordDTO>> listCaseAnalyseHistory(@RequestParam int caseId) {
        return NewResponseUtil.makeSuccess(ocrApproveService.listCaseAnalyseHistory(caseId));
    }

    @ApiOperation("查看工单计算快照")
    @PostMapping("get-last-analyse-by-work-order")
    @RequiresPermission("ocr-approve:get-last-analyse-by-work-order")
    public Response<OperationRecordDTO> getLastAnalyseByWorkOrder(@RequestParam long workOrderId) {
        return NewResponseUtil.makeSuccess(ocrApproveService.getLastAnalyseByWorkOrder(workOrderId));
    }

    @ApiOperation("保存疾病计算小工具使用记录")
    @PostMapping("save-disease-tool-record")
    @RequiresPermission("ocr-approve:save-disease-tool-record-without-choice")
    public Response<Void> saveDiseaseToolRecordWithoutChoice(@RequestParam String diseaseClassNames,
                                                             @RequestParam String submitDiseaseNames,
                                                             @RequestParam(required = false, defaultValue = "0") int workOrderType,
                                                             @RequestParam(required = false, defaultValue = "0") long workOrderId,
                                                             @RequestParam(required = false, defaultValue = "0") int caseId,
                                                             @RequestParam(required = false, defaultValue = "0") int handleResult) {
        long userId = ContextUtil.getAdminLongUserId();
        if(StringUtils.isEmpty(diseaseClassNames) || StringUtils.isEmpty(submitDiseaseNames)) {
            return NewResponseUtil.makeFail("参数错误");
        }
        return ocrApproveService.saveDiseaseToolRecordWithoutChoice(diseaseClassNames,
                submitDiseaseNames, workOrderType, workOrderId, caseId, userId, handleResult);
    }

    @ApiOperation("查看疾病计算小工具的使用记录")
    @PostMapping("get-tool-record")
    @RequiresPermission("ocr-approve:get-tool-record")
    public Response<List<DiseaseAmountResultRecord>> getToolRecord(@RequestParam int caseId) {
        return diseaseClient.getToolRecordByCaseId(caseId);
    }
}
