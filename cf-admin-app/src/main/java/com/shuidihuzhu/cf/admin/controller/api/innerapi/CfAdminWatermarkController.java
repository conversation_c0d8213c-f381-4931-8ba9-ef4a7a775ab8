package com.shuidihuzhu.cf.admin.controller.api.innerapi;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingAttachmentBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.client.cf.admin.client.CfAdminWatermarkClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
public class CfAdminWatermarkController implements CfAdminWatermarkClient {

    @Autowired
    private AdminCrowdfundingAttachmentBiz attachmentBiz;

    @Override
    public Response<Map<Integer, Integer>> getIdTWatermarkByAttachIds(List<Integer> ids) {
        return NewResponseUtil.makeSuccess(attachmentBiz.getImageWatermarkByIds(ids));
    }

    @Override
    public Response<Map<String, Integer>> getUrlTWatermarkByTypes(List<Integer> caseIds, int imageUrlType) {
        return  NewResponseUtil.makeSuccess(attachmentBiz.getImageUrlWatermarkByAttachments(
                attachmentBiz.getListByInfoIdListAndType(caseIds, AttachmentTypeEnum.getAttachmentTypeEnum(imageUrlType))));
    }
}
