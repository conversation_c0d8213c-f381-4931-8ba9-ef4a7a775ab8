package com.shuidihuzhu.cf.admin.controller.api.workorder;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderAnalysisClient;
import com.shuidihuzhu.client.cf.workorder.model.vo.WorkOrderAnalysisVO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2019-11-25 10:29
 **/
@RestController
@RequestMapping("/admin/workorder/analysis")
@Slf4j
public class WorkOrderAnalysisController {

    @Autowired
    CfWorkOrderAnalysisClient analysisClient;

    @RequiresPermission("analysis:whole")
    @RequestMapping(path = "whole-analysis", method = RequestMethod.POST)
    public Response<List<WorkOrderAnalysisVO>> getWholeDay() {
        return analysisClient.wholeAnalysis();
    }

    @RequiresPermission("analysis:period")
    @RequestMapping(path = "period-analysis", method = RequestMethod.POST)
    public Response<List<WorkOrderAnalysisVO>> periodAnalysis(@RequestParam(value = "oneType", required = false) Integer oneType){
        if (oneType == null) {
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        return analysisClient.periodAnalysis(oneType);
    }

}
