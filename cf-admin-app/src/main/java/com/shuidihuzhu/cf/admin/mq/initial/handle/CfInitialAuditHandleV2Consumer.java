package com.shuidihuzhu.cf.admin.mq.initial.handle;

import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseReasonEntityBiz;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.enums.InitialAudit.CfRefuseReasonCustomEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonCustomEntity;
import com.shuidihuzhu.cf.service.ai.AiImageMaskServiceImpl;
import com.shuidihuzhu.cf.service.workorder.initialAudit.CfInitialAuditHandleV2ConsumerService;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditOperationItem;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditOperationMqVO;
import com.shuidihuzhu.client.model.enums.ImageMaskBizEnum;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.checkerframework.checker.units.qual.C;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Author: wangpeng
 * @Date: 2022/1/6 19:48
 * @Description:
 */
@Service
@RocketMQListener(id = "cf-admin-handle-" + MQTagCons.CF_INITIAL_AUDIT_HANDLE_V2,
        group = "cf-admin-handle-" + MQTagCons.CF_INITIAL_AUDIT_HANDLE_V2,
        tags = MQTagCons.CF_INITIAL_AUDIT_HANDLE_V2,
        topic = MQTopicCons.CF)
@Slf4j
public class CfInitialAuditHandleV2Consumer implements MessageListener<InitialAuditOperationMqVO> {

    public final static int BASE_INFO_TAG = 1;

    @Resource
    private AiImageMaskServiceImpl aiImageMaskService;
    @Resource
    private CfInitialAuditHandleV2ConsumerService cfInitialAuditHandleV2ConsumerService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<InitialAuditOperationMqVO> mqMessage) {
        if (Objects.isNull(mqMessage) || Objects.isNull(mqMessage.getPayload())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        log.info("CfInitialAuditHandleV2CustomConsumer is begin : {}", mqMessage.getPayload());
        InitialAuditOperationMqVO handleCaseInfoParam = mqMessage.getPayload();

        try {
            cfInitialAuditHandleV2ConsumerService.customRefuse(handleCaseInfoParam);
        } catch (Exception e) {
            log.info("CfInitialAuditHandleV2CustomConsumer customRefuse is error {}, {}", handleCaseInfoParam, e);
        }

        try {
            cfInitialAuditHandleV2ConsumerService.createHighRiskQcWorkOrder(handleCaseInfoParam);
        } catch (Exception e) {
            log.info("CfInitialAuditHandleV2CustomConsumer createHighRiskQcWorkOrder is error {}, {}", handleCaseInfoParam, e);
        }

        try {
            cfInitialAuditHandleV2ConsumerService.ocrMedicalCaseInfo(handleCaseInfoParam);
        } catch (Exception e) {
            log.info("CfInitialAuditHandleV2CustomConsumer ocrMedicalCaseInfo is error {}, {}", handleCaseInfoParam, e);
        }

        try {
            cfInitialAuditHandleV2ConsumerService.sendProgressAfterInitialAuditPass(handleCaseInfoParam);
        } catch (Exception e) {
            log.info("CfInitialAuditHandleV2CustomConsumer sendProgressAfterInitialAuditPass is error {}, {}", handleCaseInfoParam, e);
        }

        try {
            // 保存公示的审核通过动作
            cfInitialAuditHandleV2ConsumerService.savePublicAuditAction(handleCaseInfoParam);
        } catch (Exception e) {
            log.info("CfInitialAuditHandleV2CustomConsumer savePublicAuditAction is error {} {}", handleCaseInfoParam, e);
        }

        // 发送图片掩码消息
        if (CollectionUtils.isNotEmpty(handleCaseInfoParam.getPassIds()) && handleCaseInfoParam.getPassIds().contains(BASE_INFO_TAG)) {
            log.info("AiImageMaskServiceImpl sendImageMaskMq {} {}", handleCaseInfoParam.getCaseId(), ImageMaskBizEnum.CF_DETAIL_IMAGE.getDesc());
            aiImageMaskService.sendImageMaskMq(handleCaseInfoParam.getCaseId(), ImageMaskBizEnum.CF_DETAIL_IMAGE.getCode());
        }

        // 保存初审可发疾病
        try {
            cfInitialAuditHandleV2ConsumerService.saveInitialAuditDisease(handleCaseInfoParam);
        } catch (Exception e) {
            log.info("CfInitialAuditHandleV2CustomConsumer saveInitialAuditDisease is error {} {}", handleCaseInfoParam, e);
        }

        // 患者多平台发起管控
        try {
            cfInitialAuditHandleV2ConsumerService.patientMultiPlatformControl(handleCaseInfoParam);
        } catch (Exception e) {
            log.info("CfInitialAuditHandleV2CustomConsumer patientMultiPlatformControl is error {} {}", handleCaseInfoParam, e);
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
