package com.shuidihuzhu.cf.admin.controller.api.innerapi;

import com.shuidihuzhu.cf.param.InitialAuditCreateOrderParam;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditCreateOrderService;
import com.shuidihuzhu.client.cf.admin.client.CfObtainChannelFeignClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: fengxuan
 * @create 2019-11-13 11:32
 **/
@RestController
@RequestMapping("innerapi/cf/admin/channel")
@Slf4j
public class CfObtainChannelFeignController implements CfObtainChannelFeignClient {

    @Autowired
    InitialAuditCreateOrderService auditCreateOrderService;

    @RequestMapping(value = "get", method = RequestMethod.POST)
    @Override
    public Response<Void> getChannel(int caseId, String channel) {

        InitialAuditCreateOrderParam orderParam = InitialAuditCreateOrderParam.builder()
                .caseId(caseId)
                .condition(0)
                .first(true)
                .to1V1(true)
                .checkTargetAmount(true)
                .build();
        auditCreateOrderService.createInitialAuditOrder(orderParam);
        return NewResponseUtil.makeSuccess(null);
    }
}
