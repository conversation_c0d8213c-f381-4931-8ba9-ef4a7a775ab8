package com.shuidihuzhu.cf.admin.controller.api.stream;

import com.shuidihuzhu.cf.service.stream.StreamActionConst;
import com.shuidihuzhu.cf.service.stream.manager.StreamData;
import com.shuidihuzhu.cf.service.stream.manager.StreamDataManageService;
import com.shuidihuzhu.cf.service.stream.manager.StreamStringManageService;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.async.DeferredResult;


/**
 * https://wiki.shuiditech.com/pages/viewpage.action?pageId=904842057
 */
@RestController
@RequestMapping(path="/admin/cf/stream")
@Slf4j
public class AdminStreamController {

    @Autowired
    private StreamDataManageService streamV2Service;

    @Autowired
    private StreamStringManageService streamStringManageService;

    /**
     * 基于操作人的长连接
     * @param userId
     * @return
     */
    @RequestMapping(path = "/operator", method = {RequestMethod.GET,RequestMethod.POST})
    public DeferredResult<Response<String>> operator(Long userId){
        return streamStringManageService.getDeferredResult(StreamActionConst.Subject.operator_string, userId);
    }

    @RequestMapping(path = "/operator-all-in", method = {RequestMethod.GET, RequestMethod.POST})
    public DeferredResult<Response<String>> operatorAllIn(Long userId) {
        return streamStringManageService.getDeferredResult(StreamActionConst.Subject.cailiao_fuwu, userId);
    }

    @RequestMapping(path = "/operator-report-pending-entry", method = {RequestMethod.GET, RequestMethod.POST})
    public DeferredResult<Response<String>> operatorReportPendingEntry(Long userId) {
        return streamStringManageService.getDeferredResult(StreamActionConst.Subject.report_pending_entry, userId);
    }

    /**
     *
     */
    @RequestMapping(path = "/operator-group-assign", method = {RequestMethod.GET,RequestMethod.POST})
    public DeferredResult<Response<StreamData>> operatorGroupAssign(Long userId){
        return streamV2Service.getDeferredResult(StreamActionConst.Subject.operator_group_assign, userId);
    }


}
