package com.shuidihuzhu.cf.admin.controller.api.remark;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.constants.remark.RemarkTypeConsts;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingOperationDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.domain.RemarkDO;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cf.vo.RemarkVO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018-08-17  14:12
 */
@Api("操作记录备注查询")
@Slf4j
@RequestMapping("admin/cf/remark")
@RestController
public class RemarkController {

    @Resource
    private ICrowdfundingOperationDelegate crowdfundingOperationDelegate;

    @Resource
    private AdminApproveService adminApproveService;

    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Resource
    private IRiskDelegate riskDelegate;

    @RequiresPermission("risk-control:word-category-search")
    @ApiOperation("查询记录")
    @PostMapping("list-by-condition")
    public Response searchByCondition(@RequestParam() long caseId,
                                      @RequestParam(required = false, defaultValue = "") List<Integer> remarkTypes) {
        List<RemarkDO> remarkDOS = crowdfundingOperationDelegate.listByCaseIdAndRemarkTypes(caseId, remarkTypes);
        if (CollectionUtils.isEmpty(remarkDOS)) {
            return NewResponseUtil.makeSuccess(Collections.emptyList());
        }
        List<RemarkVO> voList = remarkDOS.stream().map(this::convertVO).collect(Collectors.toList());
        return NewResponseUtil.makeSuccess(voList);
    }

    @RequiresPermission("risk-control:save")
    @ApiOperation("插入记录")
    @PostMapping("save")
    public Response save(@RequestParam() long caseId,
                         @RequestParam(required = false, defaultValue = "0") int remarkType,
                         @RequestParam(required = false, defaultValue = "") String content
    ) {
        int adminUserId = ContextUtil.getAdminUserId();
        RemarkDO v = RemarkDO.builder().caseId(caseId)
                .remarkType(remarkType)
                .content(content)
                .operatorId(adminUserId)
                .build();
        crowdfundingOperationDelegate.saveRemarkDO(v);
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("risk-control:save-and-sync-detail-page")
    @ApiOperation("插入记录 同时同步到案例详情页备注")
    @PostMapping("save-and-sync-detail-page")
    public Response saveAndSyncDetailPage(@RequestParam() int caseId,
                                          @RequestParam(required = false, defaultValue = "0") int remarkType,
                                          @RequestParam(required = false, defaultValue = "") String content
    ) {
        int adminUserId = ContextUtil.getAdminUserId();
        RemarkDO v = RemarkDO.builder().caseId(caseId)
                .remarkType(remarkType)
                .content(content)
                .operatorId(adminUserId)
                .build();
        crowdfundingOperationDelegate.saveRemarkDO(v);

        // TODO 拆分单独controller method
        if (remarkType == RemarkTypeConsts.CASE_DATA_INFO_RISK ||
                remarkType == RemarkTypeConsts.CASE_DATA_DRAW_CASH_RISK) {
            riskDelegate.onRemark(caseId, remarkType);
        }

        CrowdfundingInfo fundingInfo = crowdfundingDelegate.getFundingInfoById(caseId);
        adminApproveService.addApprove(fundingInfo, "数据异常案例跟进", content, adminUserId);
        return NewResponseUtil.makeSuccess(null);
    }

    private RemarkVO convertVO(RemarkDO remarkDO) {
        return crowdfundingOperationDelegate.convertVO(remarkDO);
    }

}
