package com.shuidihuzhu.cf.admin.mq.common;

import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import org.slf4j.Logger;

import javax.annotation.Resource;

/**
 * 继承此类依然要显示实现 implements MessageListener<T> 不然payload解析会异常
 * <AUTHOR>
 * @date 2018-07-16  15:30
 */
public abstract class BaseMessageConsumer<T> implements MessageListener<T> {

    @Resource
    private CommonConsumerHelper commonConsumerHelper;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<T> mqMessage) {
        return commonConsumerHelper.consumeMessage(mqMessage, this::handle, isDiscardable(), getLogger());
    }

    /**
     * 异常时是否可以丢弃
     * 不丢弃会重试
     * 默认为false 不丢弃
     * @return
     */
    protected boolean isDiscardable(){
        return false;
    }

    /**
     * 处理消息接收后的逻辑
     * @param consumerMessage 消息内容
     * @return true -> success, false -> fail
     */
    protected abstract boolean handle(ConsumerMessage<T> consumerMessage);

    protected abstract Logger getLogger();
}
