package com.shuidihuzhu.cf.admin.controller.api.common;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.shuidihuzhu.client.constant.ApolloNameSpaceCons;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 */
@Api(value = "获取apollo配置的内容", tags = "https://wiki.shuiditech.com/pages/viewpage.action?pageId=156370176")
@Controller
@RequestMapping(path = "admin/cf/common/apollo")
public class ApolloController {

//	@ApiOperation(value = "根据key获取对应value", tags = "namespace:store")
//	@PostMapping("get-store")
//	@ResponseBody
//	public Response<String> getStore(@RequestParam() String key,
//                                      @RequestParam(required = false, defaultValue = "") String defaultValue) {
//		Config config = ConfigService.getConfig(ApolloNameSpaceCons.PUBLIC);
//		String value = config.getProperty(key, defaultValue);
//		return NewResponseUtil.makeSuccess(value);
//	}

	@ApiOperation(value = "根据key获取对应value", tags = "namespace:json-store")
	@PostMapping("get-json-store")
	@ResponseBody
	public Response<Object> getJsonStore(@RequestParam() String key,
                                          @RequestParam(required = false, defaultValue = "") String defaultValue) {
		Config config = ConfigService.getConfig(ApolloNameSpaceCons.ADMIN_JSON_STORE);
		String value = config.getProperty(key, defaultValue);
		return NewResponseUtil.makeSuccess(JSON.parse(value));//已检查过
	}

}
