package com.shuidihuzhu.cf.admin.controller.api.common;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.admin.UserCommentBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfMaterialVerityHistoryBiz;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingUserDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.admin.UserCommentSourceEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoDataStatusTypeEnum;
import com.shuidihuzhu.cf.model.admin.AdminConfirmHospitalParam;
import com.shuidihuzhu.cf.model.admin.UserComment;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.cf.service.workorder.read.WorkOrderReadService;
import com.shuidihuzhu.cf.vo.approve.TreatmentVO;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import com.shuidihuzhu.client.cf.admin.model.HospitalOptSourceEnum;
import com.shuidihuzhu.client.cf.api.client.CfApiHospitalFeignClient;
import com.shuidihuzhu.client.cf.growthtool.client.CfHospitalNormalFeignClient;
import com.shuidihuzhu.client.cf.growthtool.client.ICrmHospitalFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.CfCrmStandardHospitalSimpleModel;
import com.shuidihuzhu.client.cf.workorder.CfChuciWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.ChuciHandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.model.hospital.CfHospitalEditParam;
import com.shuidihuzhu.client.model.hospital.CfHospitalNormal;
import com.shuidihuzhu.client.model.hospital.CfUserSubmitHospitalVo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @author: fengxuan
 * @create 2020-06-08 20:07
 **/
@RestController
@Slf4j
@RequestMapping("admin/cf/hospital-normal")
public class AdminCfHospitalNormalController {

    @Autowired
    private CfHospitalNormalFeignClient cfHospitalNormalFeignClient;

    @Autowired
    private CfChuciWorkOrderClient chuciWorkOrderClient;

    @Autowired
    private ApproveRemarkOldService approveRemarkOldService;

    @Autowired
    private CfApiHospitalFeignClient cfApiHospitalFeignClient;

    @Autowired
    private UserCommentBiz commentBiz;

    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;

    @Autowired
    private ICrowdfundingUserDelegate crowdfundingUserDelegate;

    @Autowired
    private CfMaterialVerityHistoryBiz cfMaterialVerityHistoryBiz;

    @Resource
    private AlarmClient alarmClient;

    @Autowired
    private ICrmHospitalFeignClient hospitalFeignClient;

    @Autowired
    private WorkOrderReadService workOrderReadService;

    //报备医院不可对公
    @PostMapping("not-report-public")
    public Response notReportPublic(@RequestParam("infoUuid") String infoUuid,
                                    @RequestParam("materialId") int materialId,
                                    @RequestParam(value = "remark", required = false) String remark,
                                    @RequestParam(value = "hospitalType") int hospitalType) {

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }

        if (materialId != CrowdfundingInfoDataStatusTypeEnum.TREATMENT_INFO_SUBMIT.getCode()) {
            return NewResponseUtil.makeSuccess(null);
        }

        //1:就诊医院 2:确认医院 3:就诊/确诊医院
        if (!Lists.newArrayList(1, 2, 3).contains(hospitalType)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        TreatmentVO treatmentVO = crowdfundingUserDelegate.getCrowdfundingTreatmentVO(crowdfundingInfo.getId());
        if (treatmentVO == null) {
            return NewResponseUtil.makeSuccess(null);
        }

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("【报备医院不可对公】").append("\n");
        stringBuilder.append("案例id：").append(crowdfundingInfo.getId()).append("\n");
        stringBuilder.append("模块：").append("诊断信息").append("\n");
        if (hospitalType == 1) {
            stringBuilder.append("就诊医院：").append(treatmentVO.getHospitalName()).append("\n");
        } else if (hospitalType == 2) {
            stringBuilder.append("确诊医院：").append(treatmentVO.getDiagnoseHospitalName()).append("\n");
        } else {
            stringBuilder.append("就诊/确诊医院：").append(treatmentVO.getHospitalName()).append("\n");
        }
        stringBuilder.append("备注：").append(StringUtils.isBlank(remark) ? StringUtils.EMPTY : remark).append("\n");
        stringBuilder.append("报备时间：").append(DateUtil.formatDateTime(new Date())).append("\n");
        stringBuilder.append("报备人：").append(cfMaterialVerityHistoryBiz.queryOperateDetail(ContextUtil.getAdminUserId()));

        alarmClient.sendByGroup("wx-alarm-prod-20200709-0002", stringBuilder.toString());
        return NewResponseUtil.makeSuccess(null);
    }

    //获取医院信息
    @PostMapping("list-hospital")
    public Response<List<CfHospitalNormal>> listHospitals(@RequestParam("provinceId") int provinceId, @RequestParam("cityId") int cityId,
                                                          @RequestParam("hospitalName") String hospitalName) {
        Response<List<CfHospitalNormal>> hospitalInfos = cfHospitalNormalFeignClient.listHospitals(provinceId, cityId, hospitalName);
        if (hospitalInfos.notOk()) {
            log.info("调用listHospitals失败,结果为:{}", JSON.toJSONString(hospitalInfos));
            return NewResponseUtil.makeFail(hospitalInfos.getMsg());
        }
        return hospitalInfos;
    }


    //确认医院信息
    @PostMapping("confirm-hospital")
    public Response<Boolean> confirmHospital(AdminConfirmHospitalParam confirmHospitalParam) {
        if (confirmHospitalParam == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        HospitalOptSourceEnum optSourceEnum = HospitalOptSourceEnum.findByCode(confirmHospitalParam.getHospitalEditSource());
        if (HospitalOptSourceEnum.findByCode(confirmHospitalParam.getHospitalEditSource()) == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (!(confirmHospitalParam.getChushenObtainHospital() == 0 && optSourceEnum == HospitalOptSourceEnum.chushen_edit)) {
            if (StringUtils.isBlank(confirmHospitalParam.getHospitalName()) || confirmHospitalParam.getCityId() <= 0 || confirmHospitalParam.getAdminUserId() <= 0) {
                log.info("医院名称，城市id, 省份id, adminUserId不能为空");
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
            }
            Response<Boolean> notifyResponse = cfHospitalNormalFeignClient.notifyGrowthSeaConfirmHospital(confirmHospitalParam);
            if (notifyResponse.notOk()) {
                log.info("调用notifyGrowthSeaConfirmHospital失败,结果为:{}", JSON.toJSONString(notifyResponse));
                return NewResponseUtil.makeFail(notifyResponse.getMsg());
            }
        }
        if (optSourceEnum == HospitalOptSourceEnum.chushen_edit) {
            long workOrderId = confirmHospitalParam.getWorkOrderId();
            if (workOrderId <= 0) {
                log.info("初审需要传入workId");
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
            }
            boolean canNotHandle = workOrderReadService.checkCanNotHandle(workOrderId, confirmHospitalParam.getAdminUserId());
            if (canNotHandle) {
                return NewResponseUtil.makeError(AdminErrorCode.ORDER_HAS_CALLBACK);
            }
            //初审确认医院，关闭工单记录操作
            handleWorkOrder(confirmHospitalParam);

        }
        saveComment(confirmHospitalParam, optSourceEnum);
        //通知
        if (optSourceEnum == HospitalOptSourceEnum.cailiao_edit_confirm || optSourceEnum == HospitalOptSourceEnum.cailiao_edit_treatment) {
            CfHospitalEditParam cfHospitalEditParam = buildEditParam(confirmHospitalParam, optSourceEnum);
            cfApiHospitalFeignClient.editHospitalInfo(cfHospitalEditParam);
        }
        return NewResponseUtil.makeSuccess(true);
    }


    //获取标准医院库信息
    @PostMapping("get-hospitals")
    public Response<List<CfCrmStandardHospitalSimpleModel>> getHospitals(@RequestParam(value = "city",required = false,defaultValue = "") String city,
                                                                         @RequestParam("hospitalName") String hospitalName) {
        Response<List<CfCrmStandardHospitalSimpleModel>> response = hospitalFeignClient.listStandardByFuzzyName(hospitalName,city);
        return response;
    }

    private void saveComment(AdminConfirmHospitalParam param, HospitalOptSourceEnum optSourceEnum) {
        int caseId = param.getCaseId();
        int adminUserId = param.getAdminUserId();
        // 材审就诊医院
        if (optSourceEnum == HospitalOptSourceEnum.cailiao_edit_treatment) {
            Response<CfUserSubmitHospitalVo> preResp = cfApiHospitalFeignClient.getHospitalByCaseId(caseId);
            CfUserSubmitHospitalVo preData = preResp.getData();
            if (Objects.isNull(preData)) {
                log.warn("查询材审就诊医院为空");
                return;
            }
            CfUserSubmitHospitalVo.HospitalInfo treatmentHospital = preData.getTreatmentHospital();
            if (Objects.isNull(treatmentHospital)) {
                log.warn("查询材审就诊医院为空");
                return;
            }
            String approveComment = convertApproveComment(treatmentHospital.getProvince(), treatmentHospital.getCity(), treatmentHospital.getHospitalName());
            String approveCommentChange = convertApproveComment(param.getProvince(), param.getCity(), param.getHospitalName());
            String content = approveComment + "修改为" + approveCommentChange;
            approveRemarkOldService.add(caseId, adminUserId, "【修改就诊医院】", content);
            return;
        }
        // 材审确诊医院
        if (optSourceEnum == HospitalOptSourceEnum.cailiao_edit_confirm) {
            Response<CfUserSubmitHospitalVo> preResp = cfApiHospitalFeignClient.getHospitalByCaseId(caseId);
            CfUserSubmitHospitalVo preData = preResp.getData();
            if (Objects.isNull(preData)) {
                log.warn("查询材审确诊医院为空");
                return;
            }
            CfUserSubmitHospitalVo.HospitalInfo confirmHospital = preData.getConfirmHospital();
            if (Objects.isNull(confirmHospital)) {
                log.warn("查询材审确诊医院为空");
                return;
            }
            String approveComment = convertApproveComment(confirmHospital.getProvince(), confirmHospital.getCity(), confirmHospital.getHospitalName());
            String approveCommentChange = convertApproveComment(param.getProvince(), param.getCity(), param.getHospitalName());
            String content = approveComment + "修改为" + approveCommentChange;
            approveRemarkOldService.add(caseId, adminUserId, "【修改确诊医院】", content);
            return;
        }

        // 初审补充医院
        if (optSourceEnum == HospitalOptSourceEnum.chushen_edit) {
            boolean hasObtainHospital = param.getChushenObtainHospital() == 1;
            String department = StringUtils.isNotEmpty(param.getDepartmentName()) ? ":" + param.getDepartmentName() : "";
            if (hasObtainHospital) {
                String content = param.getHospitalName() + "-" + param.getProvince() + "-" + param.getCity();
                saveComment2Initial(param, adminUserId, "【预审补充医院信息】" + content + department);
            } else {
                saveComment2Initial(param, adminUserId, "【预审补充医院信息】：医疗材料中无医院信息" + department);
            }
        }
    }

    private String convertApproveComment(String province, String city, String hospitalName) {
        StringBuilder stringBuilder = new StringBuilder("\"");
        if (StringUtils.isNotEmpty(province)) {
            stringBuilder.append(province);
            stringBuilder.append("-");
        }
        if (StringUtils.isNotEmpty(city)) {
            stringBuilder.append(city);
            stringBuilder.append("-");
        }
        if (StringUtils.isNotEmpty(hospitalName)) {
            stringBuilder.append(hospitalName);
        }
        stringBuilder.append("\"");
        return stringBuilder.toString();
    }

    private void saveComment2Initial(AdminConfirmHospitalParam param, int adminUserId, String content) {
        UserComment comment = new UserComment();
        comment.setOperatorId(adminUserId);
        comment.setCaseId(param.getCaseId());
        comment.setCommentSource(UserCommentSourceEnum.INITIAL_AUDIT.getCode());
        comment.setCommentType(0);
        comment.setOperateMode("");
        comment.setWorkOrderId(param.getWorkOrderId());
        comment.setComment("");
        comment.setOperateDesc(content);
        commentBiz.insert(comment);
    }

    private void handleWorkOrder(AdminConfirmHospitalParam confirmHospitalParam) {
        ChuciHandleOrderParam workOrderParam = new ChuciHandleOrderParam();

        workOrderParam.setWorkOrderId(confirmHospitalParam.getWorkOrderId());
        workOrderParam.setUserId(confirmHospitalParam.getAdminUserId());
        workOrderParam.setOperComment("");
        workOrderParam.setCaseId(confirmHospitalParam.getCaseId());
        workOrderParam.setOrderType(WorkOrderType.bu_chong_yi_yuan_xin_xi.getType());

        workOrderParam.setHandleResult(HandleResultEnum.done.getType());
        Response<Map<Integer, Long>> result = chuciWorkOrderClient.hanldeChuciV2(workOrderParam);
        log.info("补充医院信息工单. workOrderParam:{}, Response:{} ", workOrderParam, JSON.toJSONString(result));
    }

    private CfHospitalEditParam buildEditParam(AdminConfirmHospitalParam param, HospitalOptSourceEnum optSourceEnum) {
        CfHospitalEditParam hospitalEditParam = new CfHospitalEditParam();
        hospitalEditParam.setCaseId(param.getCaseId());
        hospitalEditParam.setAdminUserId(param.getAdminUserId());
        hospitalEditParam.setHospitalOptSourceEnum(optSourceEnum);
        hospitalEditParam.setHospitalName(param.getHospitalName());
        hospitalEditParam.setProvince(param.getProvince());
        hospitalEditParam.setProvinceId(param.getProvinceId());
        hospitalEditParam.setCity(param.getCity());
        hospitalEditParam.setCityId(param.getCityId());
        hospitalEditParam.setHospitalCode(param.getHospitalCode());
        if (StringUtils.isEmpty(hospitalEditParam.getHospitalCode())){
            hospitalEditParam.setHospitalCode("hospitalCode");
        }
        hospitalEditParam.setHospitalId(param.getHospitalId());
        return hospitalEditParam;
    }
}
