package com.shuidihuzhu.cf.admin.configuration;

import com.shuidihuzhu.cf.constants.admin.AdminDS;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.jdbc.readwrite.separation.group.ReadWriteSeparationDataSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import javax.sql.DataSource;

/**
 * @author: cuikexiang
 */
@Configuration
public class ReadWriteSeparationDataSourceConfiguration {

    @Resource(name = DS.CF)
    private DataSource crowdfundingDataSource;

    @Resource(name = DS.CF_SLAVE)
    private DataSource crowdfundingSlaveDataSource;


    @Bean(name = AdminDS.CF_RW)
    public DataSource crowdfundingRWDataSource() {
        return new ReadWriteSeparationDataSource(crowdfundingSlaveDataSource, crowdfundingDataSource);
    }


    @Resource(name = "shuidiCfAdminDataSource")
    private DataSource shuidiCfAdminDataSource;

    @Resource(name = "shuidiCfAdminSlaveDataSource")
    private DataSource shuidiCfAdminSlaveDataSource;


    @Bean(name = AdminDS.CF_ADMIN_RW)
    DataSource shuidiCfAdminRWDataSource() {
        return new ReadWriteSeparationDataSource(shuidiCfAdminSlaveDataSource, shuidiCfAdminDataSource);
    }

}
