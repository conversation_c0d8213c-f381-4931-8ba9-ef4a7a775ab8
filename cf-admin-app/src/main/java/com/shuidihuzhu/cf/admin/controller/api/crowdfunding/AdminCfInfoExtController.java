package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.response.OpResult;

import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * @author: wanghui
 * @time: 2018/11/5 4:25 PM
 * @description: cf-info-ext 相关属性修改
 */
@RestController
@Slf4j
@RefreshScope
@RequestMapping("/admin/cf/cf-info-ext")
public class AdminCfInfoExtController {


	@Autowired
	private AdminCfInfoExtBiz cfInfoExtBiz;

	@ApiOperation("修改cf-info-ext状态")
	@RequestMapping(value = "/update-transfer-finish-status", method = RequestMethod.POST)
	@RequiresPermission("cf-info-ext:update-transfer-finish-status")
	public Response updateTransferFinishStatus(@RequestParam(value = "infoUuid") String infoUuid){

        OpResult updateOpResult = cfInfoExtBiz.updateTransferStatusAndFinishStatus(infoUuid);
        if (updateOpResult.isSuccess()){
            return NewResponseUtil.makeSuccess(infoUuid);
        }
		return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
	}
}
