package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.admin.util.lock.RedisDistributedLock;
import com.shuidihuzhu.cf.enhancer.subject.redislock.RedisLock;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.admin.common.SupplyProgressReasonItem;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyAction;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfInfoSupplyActionVo;
import com.shuidihuzhu.cf.model.param.SupplyActionSearchParam;
import com.shuidihuzhu.cf.service.crowdfunding.CfSupplyActionService;
import com.shuidihuzhu.cf.service.crowdfunding.CfSupplyProgressService;
import com.shuidihuzhu.cf.service.rejectmanager.impl.SupplyProgressReasonService;
import com.shuidihuzhu.cf.vo.crowdfunding.CfSupplyProgressDetailListVo;
import com.shuidihuzhu.cf.vo.crowdfunding.CfSupplyProgressVo;
import com.shuidihuzhu.client.cf.admin.enums.CfProgressReasonEnum;
import com.shuidihuzhu.client.cf.admin.enums.CfProgressReasonRelationEnum;
import com.shuidihuzhu.client.cf.admin.model.CfInfoSupplyField;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2020-01-10 11:38
 **/
@Api("下发动态相关")
@RestController
@RequestMapping("/admin/cf/supply/progress")
public class CfSupplyProgressController {

    @Autowired
    private SupplyProgressReasonService supplyProgressRejectService;

    @Autowired
    private CfSupplyProgressService cfSupplyProgressService;

    @Autowired
    private CfSupplyActionService cfSupplyActionService;

    @ApiOperation("下发原因信息")
    @PostMapping("list-reason")
    @RequiresPermission("supply-progress:list-reason")
    public Response<List<SupplyProgressReasonItem>> listReason(){
        List<SupplyProgressReasonItem> list =  Arrays.stream(CfProgressReasonEnum.values())
                .filter(item-> item.getIsDelete() == 0)
                .sorted(Comparator.comparing(CfProgressReasonEnum::getSort))
                .map(item -> new SupplyProgressReasonItem(item.getCode(), item.getName(),item.getTemplate()))
                .collect(Collectors.toList());

        return NewResponseUtil.makeSuccess(list);
    }

    @ApiOperation("下发原因信息")
    @PostMapping("list-reason-field")
    @RequiresPermission("supply-progress:list-reason-field")
    public Response<List<CfInfoSupplyField>> listReasonField(@RequestParam("code") int code){

        List<CfInfoSupplyField> list = CfProgressReasonRelationEnum.getByCfInfoSupplyFieldsCode(code);
        return NewResponseUtil.makeSuccess(list);

    }

    @ApiOperation("下发")
    @RequiresPermission("supply-progress:supply")
    @PostMapping("do-supply-new")
    public Response<Boolean> doSupplyNew(@RequestBody CfInfoSupplyAction action){
        return NewResponseUtil.makeFail("上线中请稍后再试");
//        int adminUserId = ContextUtil.getAdminUserId();
//        action.setSupplyUserId(adminUserId);
//
//        if (CollectionUtils.isEmpty(action.getSupplyFields()) || StringUtils.isBlank(action.getComment())
//            || action.getCaseId() <= 0 || StringUtils.isEmpty(action.getSupplyReason())){
//            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
//        }
//
//       return cfSupplyActionService.doSupply(action);
    }

    @ApiOperation("下发")
    @RequiresPermission("supply-progress:supply")
    @PostMapping("supply")
    @RedisLock(key = "supply_progress_supply_#{action.caseId}")
    public Response<Boolean> supply(@RequestBody CfInfoSupplyAction action){
        int adminUserId = ContextUtil.getAdminUserId();
        action.setSupplyUserId(adminUserId);

        if (CollectionUtils.isEmpty(action.getSupplyFields()) || StringUtils.isBlank(action.getComment())
                || action.getCaseId() <= 0 || StringUtils.isEmpty(action.getSupplyReason())){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        return cfSupplyActionService.doSupply(action);
    }

    @ApiOperation("审核")
    @RequiresPermission("supply-progress:update")
    @PostMapping("do-update")
    public Response<Boolean> doUpdate(@RequestBody CfInfoSupplyAction action){
        int adminUserId = ContextUtil.getAdminUserId();
        action.setSupplyUserId(adminUserId);

        if (action.getId() <= 0){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_IS_NULL);
        }

        if(action.getHandleStatus() != CfInfoSupplyAction.SupplyHandleStatus.reject.getCode()
            && action.getHandleStatus() != CfInfoSupplyAction.SupplyHandleStatus.pass.getCode()
            && action.getHandleStatus() != CfInfoSupplyAction.SupplyHandleStatus.cancel.getCode()){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        return cfSupplyActionService.doUpdate(action);
    }

    @ApiOperation("下发动态列表")
    @RequiresPermission("supply-progress:actionlist")
    @PostMapping("get-action-list")
    public Response<List<CfInfoSupplyAction>> getActionList(@RequestParam("caseId") int caseId){

        return NewResponseUtil.makeSuccess(cfSupplyActionService.getActionList(caseId));
    }

    @ApiOperation("下发动态详情")
    @RequiresPermission("supply-progress:detail")
    @PostMapping("get-detail")
    public Response<CfInfoSupplyActionVo> getDetail(@RequestParam("actionId") long actionId,
                                                    @RequestParam("workOrderId") long workOrderId){

        return cfSupplyActionService.getCfInfoSupplyActionVo(actionId,workOrderId);
    }

    @ApiOperation("详情页动态列表")
    @RequiresPermission("supply-progress:list")
    @PostMapping("get-detail-list")
    public Response<List<CfInfoSupplyAction>> getDetailList(@RequestParam("caseId") int caseId){

        return NewResponseUtil.makeSuccess(cfSupplyActionService.getDetailList(caseId));
    }

    @ApiOperation("已经下发过的类型")
    @PostMapping("get-already-code")
    @RequiresPermission("supply-progress:get-already-code")
    public Response<Set<Integer>> getAlreadyCode(@RequestParam("caseId") int caseId){

        Set<Integer> set = cfSupplyActionService.getAlreadyCode(caseId);

        return NewResponseUtil.makeSuccess(set);
    }
    @ApiOperation("代用户修改记录")
    @RequiresPermission("supply-progress:rejectList")
    @PostMapping("get-reject-list")
    public Response<List<CfInfoSupplyAction>> getRejectList(@RequestParam("actionId") long  actionId){
        List<CfInfoSupplyAction> list = cfSupplyActionService.getRecord(actionId);
        return NewResponseUtil.makeSuccess(list);
    }


    @ApiOperation("查看相关动态")
    @RequiresPermission("supply-progress:view")
    @PostMapping("show-progress")
    public Response<List<CfSupplyProgressVo.ProgressWorkOrderInfo>> showProgress(@RequestParam("caseId") int caseId) {
        return NewResponseUtil.makeSuccess(cfSupplyActionService.showProgress(caseId));
    }



    @ApiOperation("查看下发原因信息")
    @PostMapping("list-supply-reason")
    @Deprecated
    @RequiresPermission("supply-progress:list-supply-reason")
    public Response<List<SupplyProgressReasonItem>> listSupplyReason() {
        List<SupplyProgressReasonItem> rejectItems = supplyProgressRejectService.listAllRejectInfo();
        return NewResponseUtil.makeSuccess(rejectItems);
    }




    @ApiOperation("下发")
    @RequiresPermission("supply-progress:supply")
    @PostMapping("do-supply")
    @Deprecated
    public Response<Boolean> doSupply(@RequestParam("caseId") int caseId,
                                      @RequestParam("rejectIds") String rejectIds,
                                      @RequestParam("comment") String comment) {

        return NewResponseUtil.makeFail("不可下发动态，请下发代修改动态");

//        int adminUserId = ContextUtil.getAdminUserId();
//        //check rejectIds is validate
//        List<Integer> rejectIdList = Splitter.on(',').splitToList(rejectIds).stream().map(Integer::valueOf).collect(Collectors.toList());
//        boolean validateResult = supplyProgressRejectService.validateRejectIds(rejectIdList);
//        if (!validateResult) {
//            return NewResponseUtil.makeFail("下发传入的原因不对");
//        }
//        comment = Optional.ofNullable(comment).orElse("");
//        if (comment.length() > 300) {
//            return NewResponseUtil.makeFail("下发备注超过300字");
//        }
//        return cfSupplyProgressService.doSupply(caseId, rejectIds, comment, adminUserId);
    }


    @ApiOperation("撤销")
    @RequiresPermission("supply-progress:cancel")
    @PostMapping("do-cancel")
    public Response<Boolean> doCancel(@RequestParam("caseId") int caseId,
                                      @RequestParam("supplyId") long supplyId) {
        int adminUserId = ContextUtil.getAdminUserId();
        return cfSupplyProgressService.doCancel(caseId, supplyId, adminUserId);
    }


    @ApiOperation("查看相关动态")
    @RequiresPermission("supply-progress:view")
    @PostMapping("show-relation-progress")
    public Response<CfSupplyProgressVo> showRelationProgress(@RequestParam("caseId") int caseId,
                                                             @RequestParam("supplyId") long supplyId) {
        return cfSupplyProgressService.showRelationProgress(caseId, supplyId);
    }


    @ApiOperation("下发动态列表页")
    @RequiresPermission("supply-progress:list")
    @PostMapping("list")
    @Deprecated
    public Response<CfSupplyProgressDetailListVo> list(@Validated SupplyActionSearchParam searchParam, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            String msg = bindingResult.getFieldErrors().stream().map(FieldError::getField).collect(Collectors.joining(","));
            return NewResponseUtil.makeFail(String.format("参数%s异常", msg));
        }
        return cfSupplyProgressService.listSupplyActionSearchParam(searchParam);
    }
}
