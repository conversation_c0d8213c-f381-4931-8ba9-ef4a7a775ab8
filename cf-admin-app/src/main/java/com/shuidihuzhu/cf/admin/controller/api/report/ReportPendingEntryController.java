package com.shuidihuzhu.cf.admin.controller.api.report;

import com.shuidihuzhu.cf.enhancer.model.response.EhResponse;
import com.shuidihuzhu.cf.model.report.schedule.ReportPendingEntryVO;
import com.shuidihuzhu.cf.model.report.schedule.ReportScheduleVO;
import com.shuidihuzhu.cf.service.report.ReportPendingEntryService;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.common.web.util.ContextUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Api("举报跟进标记接口")
@Slf4j
@RestController
@RequestMapping("admin/cf/report/report-pending-entry")
public class ReportPendingEntryController {

    @Autowired
    private ReportPendingEntryService reportPendingEntryService;

    @RequiresPermission("report-mark:get-mark-list")
    @ApiOperation("查询操作人跟进列表")
    @PostMapping("get-list-by-operator-id")
    public EhResponse<List<ReportPendingEntryVO>> getListByOperatorId() {
        int adminUserId = ContextUtil.getAdminUserId();
        return reportPendingEntryService.getListByOperatorId(adminUserId);
    }

    @RequiresPermission("report-mark:get-mark-list")
    @ApiOperation("判断")
    @PostMapping("judge")
    public EhResponse<Boolean> judge(@RequestParam long id) {
        return reportPendingEntryService.judge(id);
    }

}
