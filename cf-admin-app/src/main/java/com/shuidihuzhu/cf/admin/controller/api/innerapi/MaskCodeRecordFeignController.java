package com.shuidihuzhu.cf.admin.controller.api.innerapi;

import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.admin.common.MaskCodeOperationRecordBiz;
import com.shuidihuzhu.cf.model.common.MaskCodeOperationRecord;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

@RestController
@RequestMapping("innerapi/cf/admin/mask-code")
public class MaskCodeRecordFeignController {
    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;
    @Autowired
    private MaskCodeOperationRecordBiz maskCodeOperationRecordBiz;

    @RequestMapping(path = "crm-mask-code-operation", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response recordCrmMaskQuery(@RequestParam("userId") int userId, @RequestParam("accessPage") String accessPage,
                                       @RequestParam("operationType") String operationType, @RequestParam("extInfo") String extInfo) {
        MaskCodeOperationRecord maskCodeOperationRecord = new MaskCodeOperationRecord();
        maskCodeOperationRecord.setOperationPage(accessPage);
        maskCodeOperationRecord.setOperationType(operationType);
        maskCodeOperationRecord.setQueryContent(extInfo);
        maskCodeOperationRecord.setOperatorId(userId);
        maskCodeOperationRecord.setOperationTime(DateUtil.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
        AuthRpcResponse<String> response = seaAccountClientV1.getMisByUserId(userId);
        if (null != response && response.isSuccess() && StringUtil.isNotBlank(response.getResult())) {
            maskCodeOperationRecord.setOperatorMis(response.getResult());
        }
        maskCodeOperationRecordBiz.addMaskOperationRecord(maskCodeOperationRecord);
        return NewResponseUtil.makeSuccess(null);
    }
    @RequestMapping(path = "crm-mask-code-operation-new", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response recordCrmMaskQueryNew(@RequestParam("userId") int userId,
                                          @RequestParam("accessPage") String accessPage,
                                          @RequestParam("operationType") String operationType,
                                          @RequestParam("extInfo") String extInfo,
                                          @RequestParam(value = "clewId",required = false,defaultValue = "0") Integer clewId,
                                          @RequestParam(value = "caseId",required = false,defaultValue = "0") Integer caseId,
                                          @RequestParam(value = "wechatId",required = false,defaultValue = "") String wechatId) {
        MaskCodeOperationRecord maskCodeOperationRecord = new MaskCodeOperationRecord();
        maskCodeOperationRecord.setOperationPage(accessPage);
        maskCodeOperationRecord.setOperationType(operationType);
        maskCodeOperationRecord.setQueryContent(extInfo);
        maskCodeOperationRecord.setOperatorId(userId);
        maskCodeOperationRecord.setOperationTime(DateUtil.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
        maskCodeOperationRecord.setClewId(clewId);
        maskCodeOperationRecord.setCaseId(caseId);
        maskCodeOperationRecord.setWechatId(wechatId);
        AuthRpcResponse<String> response = seaAccountClientV1.getMisByUserId(userId);
        if (null != response && response.isSuccess() && StringUtil.isNotBlank(response.getResult())) {
            maskCodeOperationRecord.setOperatorMis(response.getResult());
        }
        maskCodeOperationRecordBiz.addMaskOperationRecord(maskCodeOperationRecord);
        return NewResponseUtil.makeSuccess(null);
    }

}
