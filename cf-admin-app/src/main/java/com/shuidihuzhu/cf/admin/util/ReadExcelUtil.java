package com.shuidihuzhu.cf.admin.util;

import com.alibaba.fastjson.JSON;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: wanghui
 * @create: 2018/10/22 4:08 PM
 */
public class ReadExcelUtil {

    private final static String excel2003L = ".xls"; // 2003- 版本的excel
    private final static String excel2007U = ".xlsx"; // 2007+ 版本的excel

    /**
     * 将流中的Excel数据转成List<Map>
     *
     * @param in
     *            输入流
     * @param fileName
     *            文件名（判断Excel版本）
     * @param mapping
     *            字段名称映射
     * @param sheetNum
     *            读取的 第几个sheet （从 1 开始）  if null -> 读取所有sheet页  else -> 读取指定sheet页
     * @return
     * @throws Exception
     */
    public static List<? extends Object> parseExcel(InputStream in, String fileName,
                                                    Map<String, String> mapping, Integer sheetNum, Class tClass) throws Exception {
        if (tClass==null){
            throw new Exception("tClass is not allowed to be null");
        }
        // 根据文件名来创建Excel工作薄
        Workbook work = getWorkbook(in, fileName);
        if (null == work) {
            throw new Exception("创建Excel工作薄为空！");
        }
        Sheet sheet = null;
        Row row = null;
        Cell cell = null;
        // 返回数据
        List<? extends Object> ls = new ArrayList<>();

        // 遍历Excel中所有的sheet
        for (int i = sheetNum==null?0:sheetNum-1; i < (sheetNum==null?work.getNumberOfSheets():sheetNum); i++) {  //  TODO   sheet
            sheet = work.getSheetAt(i);
            if (sheet == null)
                continue;

            // 取第一行标题
            row = sheet.getRow(0);
            String title[] = null;

            if (row == null)
                continue;

            title = new String[row.getLastCellNum()];

            for (int y = row.getFirstCellNum(); y < row.getLastCellNum(); y++) {
                cell = row.getCell(y);
                title[y] = (String) getCellValue(cell);
            }
            ls = getValues(mapping,sheet,title,tClass);
        }
        work.close();
        return ls;
    }

    public static List<? extends Object> getValues(Map<String, String> mapping, Sheet sheet, String title[], Class tClass){
        List<Object> ls = new ArrayList<>();
        Row row = null;
        Cell cell = null;
        // 遍历当前sheet中的所有行
        for (int j = 1; j < sheet.getLastRowNum() + 1; j++) {
            row = sheet.getRow(j);
            Map<String, Object> m = new HashMap<String, Object>();
            // 遍历所有的列
            if (row==null || row.getLastCellNum()>title.length||row.getCell(0)==null||row.getCell(0).toString().trim()==""){
                break;
            }
            for (int y = row.getFirstCellNum(); y < row.getLastCellNum(); y++) {
                cell = row.getCell(y);
                String key = title[y];
                m.put(mapping.get(key), getCellValue(cell));
            }
            ls.add(JSON.parseObject(JSON.toJSONString(m),tClass));//已检查过
        }
        return ls;
    }

    /**
     * 描述：根据文件后缀，自适应上传文件的版本
     *
     * @param inStr
     *            ,fileName
     * @return
     * @throws Exception
     */
    public static Workbook getWorkbook(InputStream inStr, String fileName) throws Exception {
        Workbook wb = null;
        String fileType = fileName.substring(fileName.lastIndexOf("."));
        if (excel2003L.equals(fileType)) {
            wb = new HSSFWorkbook(inStr); // 2003-
        } else if (excel2007U.equals(fileType)) {
            wb = new XSSFWorkbook(inStr); // 2007+
        } else {
            throw new Exception("文件格式错误(.xls  .xlsx),解析的文件格式有误！");
        }
        return wb;
    }

    /**
     * 描述：对表格中数值进行格式化
     *
     * @param cell
     * @return
     */
    public static Object getCellValue(Cell cell) {
        Object value = null;
        DecimalFormat df = new DecimalFormat("0"); // 格式化number String字符
        SimpleDateFormat sdf = new SimpleDateFormat("yyy-MM-dd"); // 日期格式化
        DecimalFormat df2 = new DecimalFormat("0"); // 格式化数字
        if (cell==null){
            return "";
        }
        switch (cell.getCellType()) {
            case Cell.CELL_TYPE_STRING:
                value = cell.getRichStringCellValue().getString();
                break;
            case Cell.CELL_TYPE_NUMERIC:
                if ("General".equals(cell.getCellStyle().getDataFormatString())) {
                    value = df.format(cell.getNumericCellValue());
                } else if ("m/d/yy".equals(cell.getCellStyle().getDataFormatString())) {
                    value = sdf.format(cell.getDateCellValue());
                } else {
                    value = df2.format(cell.getNumericCellValue());
                }
                break;
            case Cell.CELL_TYPE_BOOLEAN:
                value = cell.getBooleanCellValue();
                break;
            case Cell.CELL_TYPE_BLANK:
                value = "";
                break;
            default:
                break;
        }
        return value;
    }

    public static void main(String[] args) throws Exception {
        File file = new File("/Users/<USER>/IdeaProjects/java_farm_ing/src/main/java/111.xlsx");
        FileInputStream fis = new FileInputStream(file);
        Map<String,String> sheet1TitleMap = new HashMap<>();
        sheet1TitleMap.put("位置","index");
        sheet1TitleMap.put("案例标题","title");
        sheet1TitleMap.put("案例ID","crowdfundingInfoId");
        sheet1TitleMap.put("案例标签","label");
        System.out.println(JSON.toJSONString(parseExcel(fis,file.getName(),sheet1TitleMap,1,Map.class)));
        FileInputStream fis1 = new FileInputStream(file);
        Map<String,String> sheet2TitleMap = new HashMap<>();
        sheet2TitleMap.put("位置","index");
        sheet2TitleMap.put("案例id","crowdfundingInfoId");
        sheet2TitleMap.put("文章标题","title");
        System.out.println(JSON.toJSONString(parseExcel(fis1,file.getName(),sheet2TitleMap,2,Map.class)));
    }
}
