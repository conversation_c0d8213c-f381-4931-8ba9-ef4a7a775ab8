package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.admin.UserCommentBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderFlowBiz;
import com.shuidihuzhu.cf.client.adminpure.model.caseinfo.CfModifyMobileRecord;
import com.shuidihuzhu.cf.client.apipure.feign.CrowdfundingInfoFeignClient;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.admin.UserCommentSourceEnum;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceCapitalAccountFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.admin.UserComment;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderFlow;
import com.shuidihuzhu.cf.model.crowdfunding.CfCapitalAccount;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminCrowdfundingInfoView;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoVo;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.cf.service.crowdfunding.modify.CfModifyMobileService;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2019/3/31 下午3:43
 * @desc
 */
@Slf4j
@RestController
@Api("风控管理-修改注册手机号")
@RequestMapping(path = "/admin/cf/modifymobile")
public class AdminModifyRegisterMobileController {

    @Autowired
    private UserCommentBiz userCommentBiz;

    @Autowired
    private AdminCfInfoExtBiz adminCfInfoExtBiz;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;

    @Autowired
    private CfFinanceCapitalAccountFeignClient cfFinanceCapitalAccountFeignClient;

    @Autowired
    private AdminWorkOrderFlowBiz adminWorkOrderFlowBiz;

    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;

    @Autowired
    private ApproveRemarkOldService approveRemarkOldService;

    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;
    @Resource
    private CfModifyMobileService cfModifyMobileService;
    @Resource
    private CrowdfundingInfoFeignClient crowdfundingInfoFeignClient;
    @Autowired
    private MaskUtil maskUtil;


    @ApiOperation("风控管理-修改注册手机号-查询案例基本信息")
    @RequestMapping(path = "query-case-baseinfo", method = RequestMethod.POST)
    @RequiresPermission("modifymobile:query-case-baseinfo")
    public Response queryCaseBaseInfo(@ApiParam("int类型案例id") @RequestParam(value = "caseId") int caseId) {
        int adminUserId = ContextUtil.getAdminUserId();
        log.info("query-case-baseinfo request caseId:{}, adminUserId:{}", caseId, adminUserId);

        if (adminUserId <= 0) {
            return NewResponseUtil.makeError(CfErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        if (caseId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        CfInfoSimpleModel cfInfoSimpleModel = crowdfundingDelegate.getCfInfoSimpleModelById(caseId);
        CrowdfundingInfoVo crowdfundingInfoVo = crowdfundingInfoBiz.getFundingInfoVoByInfoUuid(null != cfInfoSimpleModel ? cfInfoSimpleModel.getInfoId() : "");
        if (null == cfInfoSimpleModel || null == crowdfundingInfoVo) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }
        CfCapitalAccount cfCapitalAccount = null;
        try {
            FeignResponse<CfCapitalAccount> response = cfFinanceCapitalAccountFeignClient.capitalAccountGetByInfoUuid(cfInfoSimpleModel.getInfoId());
            if (response == null || response.notOk()) {
                log.error("finance服务异常");
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
            }
            cfCapitalAccount = response.getData();
        } catch (Exception e) {
            log.error("finance服务异常", e);
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
        }

        AdminCrowdfundingInfoView crowdfundingInfoView = new AdminCrowdfundingInfoView();
        crowdfundingInfoView.setId(crowdfundingInfoVo.getId());
        crowdfundingInfoView.setInfoId(crowdfundingInfoVo.getInfoId());
        crowdfundingInfoView.setUserId(crowdfundingInfoVo.getUserId());
        crowdfundingInfoView.setTitle(crowdfundingInfoVo.getTitle());

        crowdfundingInfoView.setTargetAmount(crowdfundingInfoVo.getTargetAmount() / 100);
        if (cfCapitalAccount != null) {
            crowdfundingInfoView.setAmount(cfCapitalAccount.getFundsAmountInFen() / 100);
        } else {
            crowdfundingInfoView.setAmount(crowdfundingInfoVo.getAmount() / 100);
        }

        UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByUserId(crowdfundingInfoVo.getUserId());
        if (userInfoModel != null && StringUtils.isNotBlank(userInfoModel.getCryptoMobile())) {
            crowdfundingInfoView.setApplicantMobile(StringUtils.EMPTY);
            crowdfundingInfoView.setApplicantMobileMask(maskUtil.buildByEncryptPhone(userInfoModel.getCryptoMobile()));
        } else {
            if (StringUtils.isNotBlank(crowdfundingInfoVo.getCryptoRegisterMobile())) {
                crowdfundingInfoView.setApplicantMobile(StringUtils.EMPTY);
                crowdfundingInfoView.setApplicantMobileMask(maskUtil.buildByEncryptPhone(crowdfundingInfoVo.getCryptoRegisterMobile()));
            }
        }

        log.info("query-case-baseinfo response caseId:{}, adminUserId:{},result:{}", caseId, adminUserId, crowdfundingInfoView);

        return NewResponseUtil.makeSuccess(crowdfundingInfoView);
    }

    @ApiOperation("风控管理-修改注册手机号")
    @RequiresPermission("modifymobile:modify-register-mobile")
    @RequestMapping(path = "modify-register-mobile", method = RequestMethod.POST)
    public Response modifyRegisterMobile(@ApiParam("int类型案例id") @RequestParam(value = "caseId") int caseId,
                                         @ApiParam("原手机号") @RequestParam(value = "originalMobile") String originalMobile,
                                         @ApiParam("信息传递工单id") @RequestParam(value = "transferWorkOrderId") long transferWorkOrderId,
                                         @ApiParam("新手机号") @RequestParam(value = "targetMobile") String targetMobile,
                                         @ApiParam("目标userId") @RequestParam(value = "targetUserId") long targetUserId,
                                         @ApiParam("操作原因") @RequestParam(value = "oprComment") String oprComment) {
        int adminUserId = ContextUtil.getAdminUserId();

        log.info("modify-register-mobile request caseId:{},adminUserId:{},originalMobile:{},transferWorkOrderId:{},targetMobile:{},targetUserId:{},oprComment:{}",
                caseId, adminUserId, originalMobile, transferWorkOrderId, targetMobile, targetUserId, oprComment);

        if (adminUserId <= 0) {
            return NewResponseUtil.makeError(CfErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        if (caseId <= 0 || StringUtils.isEmpty(originalMobile) || StringUtils.isEmpty(targetMobile) || originalMobile.equals(targetMobile) || targetUserId <= 0 || transferWorkOrderId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if (null == crowdfundingInfo) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }

        //存在对应的信息传递工单才允许修改手机号
        //sea后台显示的信息传递工单的格式为：yyyyMMdd + 工单id。工单id不足6位用0补全
        String workOrderIdStr = String.valueOf(transferWorkOrderId);
        if (workOrderIdStr.length() < 9) {
            return NewResponseUtil.makeFail("工单id输入错误，请重新输入");
        }

        AdminWorkOrderFlow workOrderFlow = adminWorkOrderFlowBiz.selectOrderFlowViewById(Integer.parseInt(workOrderIdStr.substring(8)));//已检查过
        if (null == workOrderFlow) {
            return NewResponseUtil.makeError(AdminErrorCode.WORK_ORDER_NOT_FOUND);
        }

        //新手机号和新userId匹配才允许修改手机号
        UserInfoModel userInfo = userInfoServiceBiz.getUserInfoByMobile(targetMobile);
        if (null == userInfo || userInfo.getUserId() <= 0 || targetUserId != userInfo.getUserId()) {
            return NewResponseUtil.makeError(AdminErrorCode.MOBILE_PHONE_USER_NOT_MATCH);
        }

        OperationResult<Void> transferCf = crowdfundingInfoFeignClient.transferCf(crowdfundingInfo.getUserId(), targetUserId, crowdfundingInfo.getId());
        if (Objects.isNull(transferCf) || transferCf.isFail()) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_OPERATION_FAILED);
        }

        int result2 = adminCfInfoExtBiz.updateCryptoRegisterMobile(crowdfundingInfo.getInfoId(), oldShuidiCipher.aesEncrypt(targetMobile));

        if (result2 <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_OPERATION_FAILED);
        }

        CfModifyMobileRecord cfModifyMobileRecord = new CfModifyMobileRecord();
        cfModifyMobileRecord.setCaseId(caseId);
        cfModifyMobileRecord.setOperatorId(adminUserId);
        cfModifyMobileRecord.setOriginUserId(crowdfundingInfo.getUserId());
        cfModifyMobileRecord.setTargetUserId(targetUserId);
        cfModifyMobileService.addRecord(cfModifyMobileRecord);

        originalMobile = originalMobile.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
        targetMobile = targetMobile.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");

        StringBuilder comment = new StringBuilder();
        comment.append("原手机号");
        comment.append(originalMobile);
        comment.append("变更为新手机号");
        comment.append(targetMobile + ",");
        comment.append("原因为" + oprComment);
        approveRemarkOldService.add(crowdfundingInfo.getId(), adminUserId, "修改手机号", comment.toString());

        StringBuilder operateMode = new StringBuilder();
        operateMode.append(originalMobile);
        operateMode.append("修改为");
        operateMode.append(targetMobile);

        String operateDesc = "信息传递工单ID:" + transferWorkOrderId;

        UserComment userComment = new UserComment(crowdfundingInfo.getId(), UserCommentSourceEnum.RISK_MOBILE_PHONE, UserCommentSourceEnum.CommentType.MODIFY_REGISTER_MOBILE, adminUserId, oprComment, operateMode.toString(), operateDesc);
        userCommentBiz.add(userComment);

        log.info("modify-register-mobile response caseId:{},adminUserId:{}", caseId, adminUserId);
        return NewResponseUtil.makeSuccess(null);
    }
}
