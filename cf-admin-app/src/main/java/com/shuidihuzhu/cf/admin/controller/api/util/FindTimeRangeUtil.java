package com.shuidihuzhu.cf.admin.controller.api.util;


import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;

import java.util.Date;

/**
 * @author: wuyubin
 * @date: 2019-08-21 10:22
 */
@Slf4j
public class FindTimeRangeUtil {

    public static boolean getFindTimeRangeUtil(Date startTime, Date endTime, int day) {

        if (startTime == null && endTime == null) {
            log.debug("用户没有选中时间进行查找");
            return true;
        } else {
            Date now = DateUtils.addDays(startTime, day);
            if (now.before(endTime)) {
                log.debug("用户选中时间超过7天的范围");
                return false;
            } else {
                log.debug("用户选中时间未超过7天的范围");
                return true;
            }
        }
    }

}

