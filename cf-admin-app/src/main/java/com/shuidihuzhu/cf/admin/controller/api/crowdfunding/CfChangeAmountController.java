package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.apipure.feign.CrowdfundingInfoFeignClient;
import com.shuidihuzhu.cf.client.apipure.model.crowdfunding.CfCaseUpdateAmountRecordParam;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.client.feign.CaseInfoFeignClient;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceBcpWhitelistFeignClient;
import com.shuidihuzhu.cf.finance.enums.BcpWhiteTypeEnum;
import com.shuidihuzhu.cf.model.admin.CfAmountVo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.client.cf.admin.model.AdminCfModifyAmountMessage;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.MoneyUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by ahrievil on 2017/7/17.
 */
@Controller
@RequestMapping(path = "admin/crowdfunding/change-amount")
public class CfChangeAmountController {

    private static final Logger LOGGER = LoggerFactory.getLogger(CfChangeAmountController.class);

    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private AdminApproveService adminApproveService;
    @Autowired
    private CfFinanceBcpWhitelistFeignClient cfFinanceBcpWhitelistFeignClient;
    
    @Resource
    private CaseInfoFeignClient caseInfoFeignClient;
    @Resource
    private CrowdfundingInfoFeignClient crowdfundingInfoFeignClient;

    @RequiresPermission("change-amount:get-info")
    @RequestMapping(path = "get-info", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response getInfo(@RequestParam Integer caseId) {
        LOGGER.info("CfChangeAmountController getInfo caseId:{}", caseId);
        if (caseId == null || caseId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }
        CfAmountVo cfAmountVo = new CfAmountVo();
        BeanUtils.copyProperties(crowdfundingInfo, cfAmountVo);
        cfAmountVo.setInfoUuid(crowdfundingInfo.getInfoId());
        Map<String, Object> result = Maps.newHashMap();
        result.put("data", cfAmountVo);
        return NewResponseUtil.makeSuccess(result);
    }

    @RequiresPermission("change-amount:change-target-amount")
    @RequestMapping(path = "change-target-amount", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response changeTargetAmount(@RequestParam Integer caseId, @RequestParam Integer targetAmount,
                                       @RequestParam(value = "reason", required = false, defaultValue = "") String reason) {

        int seaUserId = ContextUtil.getAdminUserId();

        LOGGER.info("CfChangeAmountController changeAmount caseId:{}, targetAmount:{}", caseId, targetAmount);
        if (caseId == null || caseId <= 0 || targetAmount <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }
        if (targetAmount * 100 == crowdfundingInfo.getTargetAmount()) {
            return NewResponseUtil.makeFail("目标金额未发生变化");
        }
        if (StringUtils.isBlank(reason)) {
            reason = "";
        }
        if (reason.length() > 200) {
            return NewResponseUtil.makeFail("原因超出200字");
        }
        //案例备注
        String comment = "原始目标金额：" + MoneyUtil.buildBalance(crowdfundingInfo.getTargetAmount())
                + "元；修改后目标金额：" + targetAmount + "元；修改原因：" + reason;
        int userId = ContextUtil.getAdminUserId();
        if (targetAmount * 100 < crowdfundingInfo.getTargetAmount()) {
            //写入白名单，幂等
            cfFinanceBcpWhitelistFeignClient.addToBcpWhitelist(caseId, BcpWhiteTypeEnum.IGNORE_TARGET_AMOUNT, comment, userId);
        }
        try {
            //修改目标金额
            crowdfundingInfoBiz.updateTargetAmount(caseId, targetAmount * 100);
            adminApproveService.addApprove(crowdfundingInfo, "修改目标金额", comment, userId);
            CfCaseUpdateAmountRecordParam recordParam = CfCaseUpdateAmountRecordParam.builder()
                    .reason(reason)
                    .userId(seaUserId)
                    .caseId(caseId)
                    .targetAmount(targetAmount * 100)
                    .originalAmount(crowdfundingInfo.getTargetAmount())
                    .build();
            crowdfundingInfoFeignClient.saveFixTargetAmountRecord(recordParam);
        } catch (Exception e) {
            LOGGER.error("CfChangeAmountController changeAmount error", e);
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
        }
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("change-amount:get-change-target-amount")
    @RequestMapping(path = "get-change-target-amount", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response<List<CfCaseUpdateAmountRecordParam>> getChangeTargetAmountRecord(@RequestParam Integer caseId) {
        OperationResult<List<CfCaseUpdateAmountRecordParam>> listOperationResult = crowdfundingInfoFeignClient.getFixTargetAmountRecord(caseId);
        if (Objects.nonNull(listOperationResult) && listOperationResult.isSuccess() && CollectionUtils.isNotEmpty(listOperationResult.getData())) {
            return NewResponseUtil.makeSuccess(listOperationResult.getData());
        }
        return NewResponseUtil.makeSuccess(Collections.emptyList());
    }
}
