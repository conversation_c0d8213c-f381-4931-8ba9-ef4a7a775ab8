//package com.shuidihuzhu.cf.admin.controller.api.notice;
//
//import com.google.common.collect.Maps;
//import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
//
//import com.shuidihuzhu.cf.admin.util.HuzhuRequestUtil;
//import com.shuidihuzhu.cf.enums.AdminErrorCode;
//import com.shuidihuzhu.cf.util.aliyun.CfDataUploadUtils;
//import com.shuidihuzhu.common.web.util.NewResponseUtil;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.cloud.context.config.annotation.RefreshScope;
//import org.springframework.stereotype.Controller;
//import org.springframework.util.StringUtils;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestMethod;
//import org.springframework.web.bind.annotation.ResponseBody;
//import org.springframework.web.multipart.MultipartFile;
//import org.springframework.web.multipart.MultipartHttpServletRequest;
//
//import java.io.IOException;
//import java.util.Iterator;
//import java.util.Map;
//import java.util.UUID;
//
///**
// * Created by chao on 16/12/12.
// */
//@RefreshScope
//@Controller
//@RequestMapping("admin/cf/notice/userinfo")
//public class NoticeUserInfoController {
//
//	@Value("${url.shuidi.api:}")
//	private String apiUrl;
//	private String baseUrl = null;
//
//	private static final Logger LOGGER = LoggerFactory.getLogger(NoticeUserInfoController.class);
//
//	@RequestMapping(path = "basicUserInfo", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
//	@ResponseBody
//	@RequiresPermission("notice-userinfo:get")
//	public Object basicUserInfo(Integer id, Integer insuranceId) {
//		return HuzhuRequestUtil.getRequestResult(getBaseUrl(), "basicUserInfo", "id", "" + id, "insuranceId", "" + insuranceId);
//	}
//
//
//	@RequestMapping(path = "addOrUpdate", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
//	@ResponseBody
//	@RequiresPermission("notice-userinfo:get")
//	public Object addOrUpdate(String data) {
//		return HuzhuRequestUtil.getRequestResult(getBaseUrl(), "addOrUpdate", "data", data);
//	}
//
//	@RequestMapping(path = "delete", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
//	@ResponseBody
//	@RequiresPermission("notice-userinfo:get")
//	public Object delete(Integer id) {
//		return HuzhuRequestUtil.getRequestResult(getBaseUrl(), "delete", "id", "" + id);
//	}
//
//	@RequestMapping(path = "getByInsuranceId", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
//	@ResponseBody
//	@RequiresPermission("notice-userinfo:get")
//	public Object getByInsuranceId(Integer insuranceId) {
//		return HuzhuRequestUtil.getRequestResult(getBaseUrl(), "getByInsuranceId", "insuranceId", "" + insuranceId);
//	}
//
//	@RequestMapping(path = "getById", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
//	@ResponseBody
//	@RequiresPermission("notice-userinfo:get")
//	public Object getById(Integer id) {
//		return HuzhuRequestUtil.getRequestResult(getBaseUrl(), "getById", "id", "" + id);
//	}
//
//	@RequestMapping(path = "getNoticeUserList", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
//	@ResponseBody
//	@RequiresPermission("notice-userinfo:get")
//	public Object getByNoticeNo(String noticeNo) {
//		return HuzhuRequestUtil.getRequestResult(getBaseUrl(), "getNoticeUserList", "noticeNo", noticeNo);
//	}
//
//	@RequestMapping(path = "getAllNoticeUserInfo", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
//	@ResponseBody
//	@RequiresPermission("notice-userinfo:get")
//	public Object getAllNoticeUser(Integer pageNo, Integer pageSize) {
//		return HuzhuRequestUtil.getRequestResult(getBaseUrl(), "getAllNoticeUserInfo", "pageNo", "" + pageNo, "pageSize", "" + pageSize);
//	}
//
//	@RequestMapping(path = "updateSharingDescription", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
//	@ResponseBody
//	@RequiresPermission("notice-userinfo:get")
//	public Object updateSharingDescription(Integer userInfoId, String descriptionBeforeTransfer,
//	                                         String descriptionAfterTransfer) {
//		if (null == userInfoId) {
//			return NewResponseUtil.makeFail("userInfoId不能为空");
//		}
//		return HuzhuRequestUtil.getRequestResult(getBaseUrl(), "updateSharingDescription", "userInfoId", "" + userInfoId,
//		                                         "descriptionBeforeTransfer", descriptionBeforeTransfer, "descriptionAfterTransfer", descriptionAfterTransfer);
//	}
//
//	@RequestMapping(path = "upload", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
//	@ResponseBody
//	@RequiresPermission("notice-userinfo:get")
//	public Object upload(MultipartHttpServletRequest request) {
//		try {
//			Map<String, String> urlMap = Maps.newHashMap();
//			Iterator<String> iter = request.getFileNames();
//			while (iter.hasNext()) {
//				String fileName = iter.next();
//				MultipartFile file = request.getFile(fileName);
//				String originalFileName = UUID.randomUUID().toString().replace("-", "").concat("-").concat(file.getOriginalFilename());
//				if (file != null) {
//					String suffix = originalFileName.substring(originalFileName.indexOf(".") + 1);
//					String imageUrl = CfDataUploadUtils.uploadCFImageToOSS(file.getInputStream(), originalFileName, suffix);
//					if (!StringUtils.isEmpty(imageUrl)) {
//						urlMap.put(fileName, imageUrl);
//					}
//				}
//			}
//
//			return NewResponseUtil.makeSuccess(urlMap);
//		} catch (IOException e) {
//			LOGGER.error("图片上传失败", e);
//			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
//		}
//	}
//
//	private String getBaseUrl() {
//		if (null == baseUrl) {
//			baseUrl = apiUrl + "/admin/notice/userinfo/";
//		}
//
//		return baseUrl;
//	}
//}
