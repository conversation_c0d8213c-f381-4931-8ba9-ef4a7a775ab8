package com.shuidihuzhu.cf.admin.util.excel.importfromexcel.service;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.admin.util.ReadExcelUtil;
import com.shuidihuzhu.cf.delegate.saas.ExcelEasyVO;
import com.shuidihuzhu.cf.delegate.saas.ExcelParamsVO;
import com.shuidihuzhu.cf.domain.activity.SummaryLetterDO;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.client.cf.risk.model.enums.UserOperationEnum;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.FileInputStream;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@NoArgsConstructor
public class ExcelReadService {

    private XSSFSheet sheet;

    /**
     * 构造函数，初始化excel数据
     *
     * @param filePath  excel路径
     * @param sheetName sheet表名
     */
    public ExcelReadService(String filePath, String sheetName) {
        FileInputStream fileInputStream = null;
        try {
            fileInputStream = new FileInputStream(filePath);
            XSSFWorkbook sheets = new XSSFWorkbook(fileInputStream);
            //获取sheet
            sheet = sheets.getSheet(sheetName);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 根据行和列的索引获取单元格的数据
     *
     * @param row
     * @param column
     * @return
     */
    public String getExcelDateByIndex(int row, int column) {
        XSSFRow row1 = sheet.getRow(row);
        String cell = row1.getCell(column).toString();
        return cell;
    }

    /**
     * 根据某一列值为“******”的这一行，来获取该行第x列的值
     *
     * @param caseName
     * @param currentColumn 当前单元格列的索引
     * @param targetColumn  目标单元格列的索引
     * @return
     */
    public String getCellByCaseName(String caseName, int currentColumn, int targetColumn) {
        String operateSteps = "";
        //获取行数
        int rows = sheet.getPhysicalNumberOfRows();
        for (int i = 0; i < rows; i++) {
            XSSFRow row = sheet.getRow(i);
            String cell = row.getCell(currentColumn).toString();
            if (cell.equals(caseName)) {
                operateSteps = row.getCell(targetColumn).toString();
                break;
            }
        }
        return operateSteps;
    }

    //打印excel数据
    public List<ExcelParamsVO> addExcelData() {
        //获取行数
        int rows = sheet.getPhysicalNumberOfRows();
        List<ExcelParamsVO> excelParamsVOList= Lists.newArrayList();
        for (int i = 0; i < rows; i++) {
            //获取列数
            XSSFRow row = sheet.getRow(i);
            if(row == null ){
                break;
            }
            if(row.getCell(1) == null) {
                row.createCell(1);
            }
//            row.getCell(1).setCellType(CellType.STRING);
//            if(row.getCell(1).toString() == null || row.getCell(1).toString() == ""){
//                break;
//            }

            XSSFCell xssfCell = row.getCell(1);
            String test = xssfCell.toString();
            List<String> banActions = Splitter.on("、").splitToList(test);
            for (String banAction : banActions) {
                ExcelParamsVO excelParamsVO = new ExcelParamsVO();

                row.getCell(0).setCellType(CellType.STRING);
                excelParamsVO.setRiskUserId(0);
                String id = row.getCell(0).toString().trim();
                if(id != null && !id.equals("")) {
                    excelParamsVO.setRiskUserId(Long.parseLong(row.getCell(0).toString().trim()));
                }
                if (banAction.equals(UserOperationEnum.VIEW.getMsg())) {
                    excelParamsVO.setOpEnum(UserOperationEnum.VIEW.getCode());
                }
                if (banAction.equals(UserOperationEnum.VERIFY.getMsg())) {
                    excelParamsVO.setOpEnum(UserOperationEnum.VERIFY.getCode());
                }
                excelParamsVO.setOpSourceEnum(100);
                excelParamsVO.setAction(false);
                excelParamsVOList.add(excelParamsVO);
            }
        }
        return excelParamsVOList;
    }

    public List<ExcelParamsVO> getExcelData(MultipartFile file) throws Exception {

        Map<String, String> sheet1TitleMap = Maps.newHashMapWithExpectedSize(2);
        sheet1TitleMap.put("用户id", "riskUserId");
        sheet1TitleMap.put("动作", "riskAction");
        List<ExcelEasyVO> easyVOS = (List<ExcelEasyVO>) ReadExcelUtil.parseExcel(file.getInputStream(),
                file.getOriginalFilename(), sheet1TitleMap, 1, ExcelEasyVO.class);
        List<ExcelParamsVO> excelParamsVOList = Lists.newArrayList();
        for (ExcelEasyVO easyVO : easyVOS) {
            if(easyVO == null){
                break;
            }
            List<String> banActions = Splitter.on("、").splitToList(StringUtils.trimToEmpty(easyVO.getRiskAction()));
            for (String banAction : banActions) {
                ExcelParamsVO excelParamsVO = new ExcelParamsVO();
                excelParamsVO.setRiskUserId(Long.parseLong(easyVO.getRiskUserId()));
                excelParamsVO.setOpEnum(UserOperationEnum.msgParse(StringUtils.trimToEmpty(banAction)).getCode());
                excelParamsVO.setOpSourceEnum(100);
                excelParamsVO.setAction(false);
                excelParamsVOList.add(excelParamsVO);
            }
        }
        return excelParamsVOList;
    }
    }
