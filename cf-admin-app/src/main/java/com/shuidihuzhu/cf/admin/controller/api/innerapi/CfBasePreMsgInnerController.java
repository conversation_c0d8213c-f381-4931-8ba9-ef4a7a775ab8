package com.shuidihuzhu.cf.admin.controller.api.innerapi;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.crowdfunding.CfBasePreMsgService;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.model.crowdfunding.CfBasePreMsg;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.admin.model.CfBasePreMsgVo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2019/10/14
 */
@RestController
@RequestMapping("innerapi/cf/admin/premsg")
@Slf4j
public class CfBasePreMsgInnerController {

    @Autowired
    private CfBasePreMsgService CfBasePreMsgService;


    /**
     * 查看信息
     * @param mobile
     * @return
     */
    @PostMapping("/getMsgByMobile")
    public Response<CfBasePreMsgVo> getMsg(String mobile) {

        log.info("getMsgByMobile mobile={}",mobile);

        CfBasePreMsg cfBasePreMsg = CfBasePreMsgService.getMsg(mobile);

        if (cfBasePreMsg == null){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_NO_ID);
        }
        CfBasePreMsgVo vo = new CfBasePreMsgVo();

        BeanUtils.copyProperties(cfBasePreMsg,vo);

        if (cfBasePreMsg.getPatientIdType() == UserIdentityType.birth.getCode()){
            vo.setPatientBornCard(cfBasePreMsg.getPatientCryptoIdcard());
            vo.setPatientCryptoIdcard("");
        }


        return NewResponseUtil.makeSuccess(vo);
    }

    @PostMapping("/getMsgListByMobile")
    public Response<List<CfBasePreMsgVo>> getMsgList(String mobile,String startTime,String endTime) {

        log.info("getMsgListByMobile mobile={},startTime={},endTime={}",mobile,startTime,endTime);

        OpResult<List<CfBasePreMsg>> opResult = CfBasePreMsgService.get1V1MsgList(mobile,startTime,endTime);
        if (opResult.isFail()){
            return NewResponseUtil.makeError(opResult.getErrorCode());
        }
        List<CfBasePreMsgVo> cfBasePreMsgVoList = Lists.newArrayList();
        for (CfBasePreMsg cfBasePreMsg : opResult.getData()){
            CfBasePreMsgVo vo = new CfBasePreMsgVo();
            BeanUtils.copyProperties(cfBasePreMsg,vo);

            if (cfBasePreMsg.getPatientIdType() == UserIdentityType.birth.getCode()){
                vo.setPatientBornCard(cfBasePreMsg.getPatientCryptoIdcard());
                vo.setPatientCryptoIdcard("");
            }
            cfBasePreMsgVoList.add(vo);
        }
        return NewResponseUtil.makeSuccess(cfBasePreMsgVoList);
    }
}
