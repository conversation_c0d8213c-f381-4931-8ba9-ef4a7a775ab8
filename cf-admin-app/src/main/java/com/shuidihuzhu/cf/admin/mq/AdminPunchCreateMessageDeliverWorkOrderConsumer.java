package com.shuidihuzhu.cf.admin.mq;

import com.google.common.collect.Lists;
import com.google.common.collect.Ordering;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderFlowBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.impl.AdminWorkOrderFlowBizImpl;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.enhancer.model.response.EhResponse;
import com.shuidihuzhu.cf.enums.report.ReportPayMethodEnum;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceCapitalAccountFeignClient;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceDrawCashFeignClient;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceRefundFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.model.finance.drawcash.CfDrawCashRecordV2;
import com.shuidihuzhu.cf.finance.model.vo.refund.CfRefundApplyV2Vo;
import com.shuidihuzhu.cf.model.AdminInfoLaunchSuccess;
import com.shuidihuzhu.cf.model.crowdfunding.CfCapitalAccount;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView;
import com.shuidihuzhu.cf.model.report.schedule.ReportScheduleVO;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.cf.service.report.ReportScheduleService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/12/17  5:30 下午
 */
@Service
@Slf4j
@RocketMQListener(id = MQTagCons.PUNCH_CREATE_MESSAGE_DELIVER_WORK_ORDER + "-cf-admin",
        group = "cf-admin" + MQTagCons.PUNCH_CREATE_MESSAGE_DELIVER_WORK_ORDER + "-group",
        tags = MQTagCons.PUNCH_CREATE_MESSAGE_DELIVER_WORK_ORDER,
        topic = MQTopicCons.CF)
public class AdminPunchCreateMessageDeliverWorkOrderConsumer implements MessageListener<AdminInfoLaunchSuccess> {

    @Resource
    private ReportScheduleService reportScheduleService;

    @Resource
    private CfFinanceCapitalAccountFeignClient cfFinanceCapitalAccountFeignClient;

    @Resource
    private CfFinanceDrawCashFeignClient cfFinanceDrawCashFeignClient;

    @Resource
    private CfFinanceRefundFeignClient cfFinanceRefundFeignClient;

    @Resource
    private AdminWorkOrderFlowBiz adminWorkOrderFlowBiz;

    @Resource
    private ApplicationService applicationService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<AdminInfoLaunchSuccess> mqMessage) {
        log.info("AdminPunchCreateMessageDeliverWorkOrderConsumer start..");
        if (Objects.isNull(mqMessage) || Objects.isNull(mqMessage.getPayload())) {
            log.info("AdminPunchCreateMessageDeliverWorkOrderConsumer data null..");
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        AdminInfoLaunchSuccess payload = mqMessage.getPayload();
        int caseId = payload.getCaseId();
        String infoUuid = payload.getInfoUuid();
        long timestamp = payload.getTimestamp();

        //获取最近打款成功时间作为标识
        FeignResponse<List<CfDrawCashRecordV2>> listFeignResponse = cfFinanceDrawCashFeignClient.getSuccessDrawCashRecordV2List(caseId);
        List<CfDrawCashRecordV2> cfDrawCashRecordV2List = Optional.ofNullable(listFeignResponse).filter(FeignResponse::ok).map(FeignResponse::getData).orElse(Lists.newArrayList());
        if (CollectionUtils.isNotEmpty(cfDrawCashRecordV2List)) {
            Ordering<CfDrawCashRecordV2> createTime = Ordering.natural().reverse().onResultOf(CfDrawCashRecordV2::getCreateTime);
            long newTimestamp = cfDrawCashRecordV2List.stream().sorted(createTime).map(v -> v.getCreateTime().getTime()).findFirst().orElse(0L);
            //如果在某次打款成功之后的90天内，案例又打款成功一次，则需要取最新一次的打款成功时间+90天 判断是否要生成信息传递工单。
            if (timestamp != newTimestamp) {
                log.info("AdminPunchCreateMessageDeliverWorkOrderConsumer timestamp:{} newTimestamp:{}", timestamp, newTimestamp);
                return ConsumeStatus.CONSUME_SUCCESS;
            }
        }

        //案例是分批打款案例
        ReportPayMethodEnum payMethodEnum = reportScheduleService.getPayMethodByCaseId(caseId);
        int code = Optional.ofNullable(payMethodEnum).map(ReportPayMethodEnum::getCode).orElse(0);
        if (code != ReportPayMethodEnum.PAY_IN_BATCH.getCode()) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        //该案例没有标记过”在打款成功的日期和生成信息传递工单日期“之间的跟进提醒
        EhResponse<ReportScheduleVO> ehResponse = reportScheduleService.getByCaseId(caseId);
        if (Objects.nonNull(ehResponse.getData())) {
            long targetTime = ehResponse.getData().getTargetTime().getTime();
            if (targetTime > timestamp && timestamp < System.currentTimeMillis()) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
        }

        //案例的账户余额>0  资金
        FeignResponse<CfCapitalAccount> feignResponse = cfFinanceCapitalAccountFeignClient.capitalAccountGetByInfoUuid(infoUuid);
        CfCapitalAccount cfCapitalAccount = Optional.ofNullable(feignResponse).filter(FeignResponse::ok).map(FeignResponse::getData).orElse(null);
        if (Objects.nonNull(cfCapitalAccount)) {
            if (cfCapitalAccount.getSurplusAmount() <= 0) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
        }

        //未申请全部退款 资金
        FeignResponse<CfRefundApplyV2Vo> response = cfFinanceRefundFeignClient.getRefundApplyV2(caseId);
        CfRefundApplyV2Vo cfRefundApplyV2Vo = Optional.ofNullable(response).filter(FeignResponse::ok).map(FeignResponse::getData).orElse(null);
        if (Objects.nonNull(cfRefundApplyV2Vo) && cfRefundApplyV2Vo.getApplyStatus() == 2) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        //生成信息传递工单
        createWorkOrder(caseId);

        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private void createWorkOrder(int caseId) {
        AdminWorkOrderFlowView vo = new AdminWorkOrderFlowView();

        //获取案例信息
        AdminWorkOrderFlowBizImpl.CaseTitleAndMobile caseTitleAndMobile = adminWorkOrderFlowBiz.getTitleAndMobileByCaseId(caseId);
        if (Objects.nonNull(caseTitleAndMobile)) {
            vo.setCaseTitle(caseTitleAndMobile.getTitle());
            vo.setMobile(caseTitleAndMobile.getMobile());
        }

        vo.setCaseId(caseId);
        vo.setProblemContent("举报分批打款案例需主动跟进");
        //中
        vo.setLevel(1);
        //系统创建
        vo.setCreatorId(102);
        //组织id 问题分类id
        if (applicationService.isProduction()) {
            vo.setProblemType(4);
            vo.setNewFirstClassifyId(2805);
            vo.setNewSecondClassifyId(2810);
        } else {
            vo.setProblemType(90);
            vo.setNewFirstClassifyId(1092);
            vo.setNewSecondClassifyId(1097);
        }

        //创建前判断是否能生成，同案例+同问题类型+未处理完
        Response<String> existRelateFlow = adminWorkOrderFlowBiz.existRelateFlow(vo);
        if (existRelateFlow.notOk()) {
            log.info("AdminPunchCreateMessageDeliverWorkOrderConsumer error msg:{}", existRelateFlow.getMsg());
        }

        Response response = adminWorkOrderFlowBiz.createWorkOrderFlow(vo);
        log.info("AdminPunchCreateMessageDeliverWorkOrderConsumer error response:{}", response.getMsg());
    }
}
