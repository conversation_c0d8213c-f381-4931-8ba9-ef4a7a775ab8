package com.shuidihuzhu.cf.admin.mq;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderFlowBiz;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminWorkOrderFlowConst;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowParam;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/4/24  14:26
 */
@Service
@Slf4j
@RocketMQListener(id = MQTagCons.WORK_ORDER_CREATE_CLEW_ORDER_HANDLE,
        group = "cf-admin-" + MQTagCons.WORK_ORDER_CREATE_CLEW_ORDER_HANDLE,
        tags = MQTagCons.WORK_ORDER_CREATE_CLEW_ORDER_HANDLE,
        topic = MQTopicCons.CF)
public class AdminWorkOrderCreateClewOrderHandleConsumer implements MessageListener<AdminWorkOrderFlowView> {

    @Autowired
    private AdminWorkOrderFlowBiz adminWorkOrderFlowBiz;


    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<AdminWorkOrderFlowView> mqMessage) {
        if (mqMessage == null || mqMessage.getPayload() == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        AdminWorkOrderFlowView parentOrder = mqMessage.getPayload();
        AdminWorkOrderFlowParam.HandleParam param = new AdminWorkOrderFlowParam.HandleParam();
        param.setUserId(102);
        param.setWorkOrderId(parentOrder.getWorkOrderId());
        param.setComment("系统自动关闭");
        param.setHandleType(AdminWorkOrderFlowConst.handleType.NO_HANDLE.getValue());
        param.setLevel(parentOrder.getLevel());
        adminWorkOrderFlowBiz.handleWorkFlowOrder(param);
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
