package com.shuidihuzhu.cf.admin.mq.workorder;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.enhancer.mq.BaseMessageConsumer;
import com.shuidihuzhu.cf.risk.client.risk.QcOrderInnerFeignClient;
import com.shuidihuzhu.cf.risk.model.admin.qc.QcZhuDongCreateParam;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 主动服务工单处理事件
 * <AUTHOR>
 */
@Service
@Slf4j
@RocketMQListener(id = MQTagCons.WORK_ORDER_HANDLE_ZHU_DONG,
        tags = MQTagCons.WORK_ORDER_HANDLE_ZHU_DONG,
        topic = MQTopicCons.CF)
public class WorkOrderHandleZhuDongDelayConsumer extends BaseMessageConsumer<String>
        implements MessageListener<String> {

    @Resource
    private QcOrderInnerFeignClient qcOrderInnerFeignClient;

    @Override
    protected boolean handle(ConsumerMessage<String> consumerMessage) {
        String payload = consumerMessage.getPayload();
        QcZhuDongCreateParam p = JSON.parseObject(payload, QcZhuDongCreateParam.class);
        Response<Void> resp = qcOrderInnerFeignClient.createZhuDongQcOrder(p);
        return resp.ok();
    }

    @Override
    protected int maxRetryCount() {
        return 2;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
