package com.shuidihuzhu.cf.admin.controller.api.workorder;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.cf.workorder.CfCreditWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderListParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.model.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @author: fengxuan
 * @create 2020-01-02 16:16
 **/
@Api("增信工单")
@Slf4j
@RestController
@RequestMapping(path = "/admin/workorder/credit")
public class CreditWorkOrderController {

    @Autowired
    private CfCreditWorkOrderClient creditWorkOrderClient;

    @RequiresPermission("credit:orderlist")
    @ApiOperation("增信工单任务处理中心列表")
    @PostMapping("credit-orderlist")
    public Response<PageResult<WorkOrderVO>> creditOrderList(@RequestParam("param") String param) {
        WorkOrderListParam workOrderListParam = JSON.parseObject(param, WorkOrderListParam.class);//已检查过
        Response<PageResult<WorkOrderVO>> pageResultResponse = creditWorkOrderClient.creditOrderlist(workOrderListParam);
        log.debug("pageResultResponse:{}", JSON.toJSONString(pageResultResponse));
        return pageResultResponse;
    }


}
