package com.shuidihuzhu.cf.admin.mq.ai;

import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.model.ai.CfApproveAuditAiCallRecord;
import com.shuidihuzhu.cf.service.workorder.cailiao.CaiAuditRejectAICallService;
import com.shuidihuzhu.client.constant.CfClientMQTopicCons;
import com.shuidihuzhu.client.model.event.InfoApproveEvent;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Author: wangpeng
 * @Date: 2022/7/12 17:26
 * @Description:
 */
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.CF_APPROVE_AI_CALL_THREE, tags = MQTagCons.CF_APPROVE_AI_CALL_THREE, topic = CfClientMQTopicCons.CF)
public class CfApproveAuditAICallAgainThreeConsumer implements MessageListener<CfApproveAuditAiCallRecord> {

    @Resource
    private CaiAuditRejectAICallService caiAuditRejectAICallService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfApproveAuditAiCallRecord> mqMessage) {
        log.info("CfApproveAuditAICallAgainThreeConsumer is begin {}", mqMessage);
        if (Objects.isNull(mqMessage.getPayload())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        caiAuditRejectAICallService.callThreeTimes(mqMessage.getPayload());
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
