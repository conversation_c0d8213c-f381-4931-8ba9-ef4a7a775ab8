package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.activity.feign.v2.ActivityMutexFeignClient;
import com.shuidihuzhu.cf.activity.model.v2.ActivityMutexVO;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.NoHandlingFeeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.FinanceApproveService;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.constant.CfClientMQTopicCons;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;


/**
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("/admin/cf")
public class CfNoHandlingFeeController {
    @Autowired
    private AdminCfInfoExtBiz adminCfInfoExtBiz;

    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Autowired
    private FinanceApproveService financeApproveService;

    @Autowired
    private ActivityMutexFeignClient activityMutexFeignClient;

    @Autowired(required = false)
    private Producer producer;

    @ApiOperation("判断是否免手续费")
    @PostMapping("is-no-handling-fee")
    @RequiresPermission("handlingFee:is-no-handling-fee")
    public Response<Boolean> isNoHandlingFee(@RequestParam("infoUuid") String infoUuid) {
        CfInfoExt cfInfoExt = adminCfInfoExtBiz.getByInfoUuid(infoUuid);
        if (cfInfoExt == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        boolean noHandlingFee = cfInfoExt.getNoHandlingFee() == NoHandlingFeeEnum.NO_FEE.getValue() ? false : true;

        return NewResponseUtil.makeSuccess(noHandlingFee);
    }

    @ApiOperation("手动标记补贴手续费")
    @PostMapping("manually-mark-subsidy-fees")
    @RequiresPermission("handlingFee:manually-mark-subsidy-fees")
    public Response<Void> isNoHandlingFee(@RequestParam("infoUuid") String infoUuid, @RequestParam("comment") String comment) {
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        int caseId = crowdfundingInfo.getId();
        OperationResult<ActivityMutexVO> mutexResp = activityMutexFeignClient.checkMutexActivity(caseId);
        boolean hasJoinMutexActivity = Optional.ofNullable(mutexResp)
                .map(OperationResult::getData)
                .filter(v -> !v.isPassed())
                .isPresent();
        if (hasJoinMutexActivity) {
            String hasJoinedActivityName = mutexResp.getData().getHasJoinedActivityName();
            log.info("已参加活动 caseId:{}, {}", caseId, hasJoinedActivityName);
            return NewResponseUtil.makeFail("加入失败，已参加" + hasJoinedActivityName);
        }

        int res = adminCfInfoExtBiz.updateNoHandlingFeeByInfoUuid(infoUuid, NoHandlingFeeEnum.MANUAL_FEE_FREE.getValue());
        if (res == 1) {
            CfInfoExt cfInfoExt = adminCfInfoExtBiz.getByInfoUuid(infoUuid);
            if (cfInfoExt == null) {
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
            }
            producer.send(new Message<>(CfClientMQTopicCons.CF, CfClientMQTagCons.FEE_FREE_SEND_MQ, CfClientMQTagCons.FEE_FREE_SEND_MQ + infoUuid, cfInfoExt));
        } else {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_OPERATION_FAILED);
        }

        financeApproveService.addApprove(crowdfundingInfo, "【手动标记补贴手续费】", comment, ContextUtil.getAdminUserId());

        return NewResponseUtil.makeSuccess(null);
    }
}
