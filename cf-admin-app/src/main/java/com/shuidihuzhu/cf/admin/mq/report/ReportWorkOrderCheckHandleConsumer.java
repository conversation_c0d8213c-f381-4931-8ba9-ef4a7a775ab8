package com.shuidihuzhu.cf.admin.mq.report;

import com.shuidihuzhu.cf.biz.crowdfunding.report.ReportWorkOrderFollowActionBiz;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.enums.ReportFollowActionEnum;
import com.shuidihuzhu.cf.model.report.ReportWorkOrderFollowAction;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * @author: lixiaoshuang
 * @create: 2020-12-23 11:47
 **/
@RocketMQListener(id = MQTagCons.REPORT_WORK_ORDER_HANDLE_REMIND, tags = MQTagCons.REPORT_WORK_ORDER_HANDLE_REMIND, topic = MQTopicCons.CF)
@Slf4j
@Service
public class ReportWorkOrderCheckHandleConsumer implements MessageListener<String> {


    @Autowired
    private ReportWorkOrderFollowActionBiz reportWorkOrderFollowActionBiz;
    @Resource(name = "cf2RedissonHandler")
    private RedissonHandler cf2RedissonHandler;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<String> mqMessage) {
        String payload = mqMessage.getPayload();
        log.info("ReportWorkOrderCheckHandleConsumer payload:{}", payload);
        if (Objects.isNull(payload)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        List<String> collect = Stream.of(payload.split(";")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        String content = collect.get(0);
        long workOrderId = Long.parseLong(collect.get(1));
        List<ReportWorkOrderFollowAction> reportWorkOrderFollowActions = reportWorkOrderFollowActionBiz.getByWorkOrderId(workOrderId,
                ReportFollowActionEnum.promptAction);
        if (CollectionUtils.isNotEmpty(reportWorkOrderFollowActions)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        cf2RedissonHandler.setEX(ReportFollowActionEnum.REPORT_FOLLOW_ACTION_PROMPT.name() + workOrderId, content);
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
