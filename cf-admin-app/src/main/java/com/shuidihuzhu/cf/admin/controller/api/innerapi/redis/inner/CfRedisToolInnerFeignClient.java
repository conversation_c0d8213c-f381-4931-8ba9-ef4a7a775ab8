package com.shuidihuzhu.cf.admin.controller.api.innerapi.redis.inner;

import com.shuidihuzhu.common.web.model.Response;
import lombok.Data;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 */
public interface CfRedisToolInnerFeignClient {
    @PostMapping("/innerapi/feign/cf-redis-tool/ttl")
    Response<String> ttl(@RequestParam("ip") String ip, @RequestParam("key") String key);

    @PostMapping("/innerapi/feign/cf-redis-tool/del")
    Response<Boolean> del(@RequestParam("ip") String ip, @RequestParam("key") String key);

    @PostMapping("/innerapi/feign/cf-redis-tool/get")
    Response<RedisqueryResponseDTO> get(@RequestParam("ip") String ip, @RequestParam("key") String key);

    @Data
    class RedisqueryResponseDTO {
        private String type;
        private Object data;

    }
}
