package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidi.weixin.common.util.StringUtils;
import com.shuidihuzhu.cf.biz.admin.channel.CfChannelBiz;
import com.shuidihuzhu.cf.biz.admin.channel.CfChannelGroupBiz;
import com.shuidihuzhu.cf.biz.admin.channel.CfQrcodeChannelMapBiz;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.admin.OperationHistoryType;
import com.shuidihuzhu.cf.model.admin.OperationHistory;
import com.shuidihuzhu.cf.model.admin.channel.CfChannel;
import com.shuidihuzhu.cf.model.admin.channel.CfChannelGroup;
import com.shuidihuzhu.cf.model.admin.channel.CfQrcodeChannel;
import com.shuidihuzhu.cf.model.admin.channel.ChannelRecordView;
import com.shuidihuzhu.cf.service.huzhu.HZChannelRecordService;
import com.shuidihuzhu.cf.vo.crowdfunding.CfQrCodeVo;

import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.admin.PageUtil;
import org.apache.commons.collections.CollectionUtils;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by wangsf on 17/3/29.
 */

@Controller
@RequestMapping(path="/admin/cf/channel")
public class CfQrCodeController {

	private Logger LOGGER = LoggerFactory.getLogger(CfQrCodeController.class);

	@Autowired
	private HZChannelRecordService hzChannelRecordService;

	@Autowired
	private CfQrcodeChannelMapBiz qrcodeChannelMapBiz;

	@Autowired
	private CfChannelBiz channelBiz;

	@Autowired
	private CfChannelGroupBiz channelGroupBiz;



	@RequiresPermission("qrcode:list")
	@RequestMapping(path = "qrcode/list", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	@ResponseBody
	public Object qrCodeList(
			@RequestParam(required = false, name = "pagination", defaultValue = "{}") String pagination,
			@RequestParam(required = false, name = "qrcodeName") String qrcodeName,
			@RequestParam(required = false, name = "channelName") String channelName) {

		String search = "";

		if (!StringUtils.isBlank(channelName)) {
			search = channelName;
		}

		if (!StringUtils.isBlank(qrcodeName)) {
			search = qrcodeName;
		}

		PageUtil pageUtil = PageUtil.parseJsonString(pagination);
		List<ChannelRecordView> channelRecordList =
				hzChannelRecordService.getByDesc(search, (pageUtil.getPageNo() - 1) * pageUtil.getPageSize(), pageUtil.getPageSize());

		LOGGER.info("CfQrcodeController qrcode/list channelRecordList: {}", channelRecordList);
		List<CfQrCodeVo> cfQrCodeVos = Lists.newArrayList();
		if(!CollectionUtils.isEmpty(channelRecordList)) {
			List<Integer> qrCodeIds = Lists.newArrayList();
			for(ChannelRecordView channelRecordView : channelRecordList) {
				String url = channelRecordView.getUrl();
				if(url != null && !url.startsWith("http")) {
					url = "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=" + url;
				}
				cfQrCodeVos.add(new CfQrCodeVo(channelRecordView.getId(), channelRecordView.getChannel(), channelRecordView.getDescr(), url, channelRecordView.getOperator(), channelRecordView.getDescr()));
				qrCodeIds.add(channelRecordView.getId());
			}

			Map<Integer, CfQrcodeChannel> cfQrcodeChannelMap = this.qrcodeChannelMapBiz.getMapByIds(qrCodeIds);
			LOGGER.info("CfQrcodeController qrcode/list qrchannelMap: {}", cfQrcodeChannelMap);

			if(cfQrcodeChannelMap != null) {
				Set<Integer> channelIdSet = Sets.newHashSet();
				Set<Integer> groupIdSet = Sets.newHashSet();
				for(Map.Entry<Integer, CfQrcodeChannel> entry : cfQrcodeChannelMap.entrySet())  {
					CfQrcodeChannel cfQrcodeChannel = entry.getValue();
					channelIdSet.add(cfQrcodeChannel.getCfChannelId());
					groupIdSet.add(cfQrcodeChannel.getCfChannelGroupId());
				}

				Map<Integer, CfChannel> cfChannelMap = Maps.newHashMap();
				if (channelIdSet != null) {
					cfChannelMap = this.channelBiz.getMapByIds(Lists.newArrayList(channelIdSet));
				}
				LOGGER.info("CfQrcodeController qrcode/list cfChannelMap: {}", cfChannelMap);

				Map<Integer, CfChannelGroup> cfChannelGroupMap = Maps.newHashMap();
				if(groupIdSet != null) {
					cfChannelGroupMap = this.channelGroupBiz.getMapByIds(Lists.newArrayList(groupIdSet));
				}
				LOGGER.info("CfQrcodeController qrcode/list cfChannelGroupMap: {}", cfChannelGroupMap);

				for (CfQrCodeVo cfQrCodeVo : cfQrCodeVos) {
					int qrcodeId = cfQrCodeVo.getId();
					CfQrcodeChannel qrcodeChannel = cfQrcodeChannelMap.get(qrcodeId);
					if(qrcodeChannel != null) {
						if(cfChannelMap != null) {
							CfChannel channel = cfChannelMap.get(qrcodeChannel.getCfChannelId());
							cfQrCodeVo.setChannel(channel);
						}

						if(cfChannelGroupMap != null) {
							CfChannelGroup group = cfChannelGroupMap.get(qrcodeChannel.getCfChannelGroupId());
							cfQrCodeVo.setChannelGroup(group);
						}
					}
				}
			}
		}

		Map<String, Object> result = Maps.newHashMap();
		result.put("data", cfQrCodeVos);
		result.put("pagination", PageUtil.transform2PageMap(channelRecordList));
		return NewResponseUtil.makeSuccess(result);
	}

	@RequiresPermission("qrcode:edit")
	@RequestMapping(path = "qrcode/edit", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	@ResponseBody
	public Response editQrcodeChannel(@RequestParam("qrcodeId") Integer qrcodeId, @RequestParam("channelId") Integer channelId) {
		if(qrcodeId == null || channelId == null || qrcodeId <= 0 || channelId <= 0) {
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
		}

		try {
			CfQrcodeChannel qrcodeChannel = this.qrcodeChannelMapBiz.getByQrcodeId(qrcodeId);
			if (qrcodeChannel == null) { //没有记录
				CfChannel cfChannel = this.channelBiz.get(channelId);
				if (cfChannel == null || cfChannel.getGroupId() <= 0) {
					return NewResponseUtil.makeError(AdminErrorCode.CHANNEL_NOT_EXISTS);
				}
				CfQrcodeChannel cfQrcodeChannel = new CfQrcodeChannel();
				cfQrcodeChannel.setQrcodeId(qrcodeId);
				cfQrcodeChannel.setCfChannelId(channelId);
				cfQrcodeChannel.setCfChannelGroupId(cfChannel.getGroupId());
				this.qrcodeChannelMapBiz.add(cfQrcodeChannel);
				return NewResponseUtil.makeSuccess(null);

//			return NewResponseUtil.makeAdminError(com.shuidihuzhu.common.web.enums.admin.AdminErrorCode.CHANNEL_NOT_EXISTS);
			}

			int result = this.qrcodeChannelMapBiz.update(qrcodeId, channelId);
			if (result == -1) {
				return NewResponseUtil.makeError(AdminErrorCode.CHANNEL_NOT_EXISTS);
			}

			return NewResponseUtil.makeSuccess(null);
		} catch (Exception e) {
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
		}
	}
}
