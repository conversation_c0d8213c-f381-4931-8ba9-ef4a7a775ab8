package com.shuidihuzhu.cf.admin.mq;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.shuidihuzhu.alps.feign.ocean.OceanApiClient;
import com.shuidihuzhu.alps.feign.ocean.OceanApiRequest;
import com.shuidihuzhu.alps.feign.ocean.OceanApiResponse;
import com.shuidihuzhu.cf.client.feign.CfAttachmentFeignClient;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.ai.AiImageRecognitionResultBody;
import com.shuidihuzhu.cf.enhancer.subject.aiagent.AiAgent;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment;
import com.shuidihuzhu.cf.store.enums.CfDomainEnum;
import com.shuidihuzhu.cf.store.model.AnalysisUrl;
import com.shuidihuzhu.client.cf.api.client.CfAttachmentAttrFeignClient;
import com.shuidihuzhu.client.cf.api.model.AttachmentAttrModel;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Author: wangpeng
 * @Date: 2022/12/23 19:13
 * @Description:
 */
@Service
@Slf4j
@RefreshScope
@RocketMQListener(id = "ai-recognition" + MQTagCons.CF_ATTACHMENT_MARK_ATTR_STAT,
        tags = MQTagCons.CF_ATTACHMENT_MARK_ATTR_STAT,
        topic = MQTopicCons.CF)
public class ImageAIRecognitionConsumer implements MessageListener<List<CrowdfundingAttachment>> {

    @Resource
    private OceanApiClient oceanApiClient;

    @Resource
    private CfAttachmentFeignClient cfAttachmentFeignClient;

    @Resource
    private CfAttachmentAttrFeignClient cfAttachmentAttrFeignClient;

    @Value("${ai-image-recognition-consumer:true}")
    private boolean aiImageRecognitionConsumer;

    public static String TAG = "ai-ps-recognition";
    public static String NEW_TAG = "ai-ps-yolo-detection";
    public static String USER_ID = "10007";
    public static String TOKEN = "168124af9fc10ac8";

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<List<CrowdfundingAttachment>> mqMessage) {
        log.info("ImageAIRecognitionConsumer is begin {}", mqMessage);
        if (!aiImageRecognitionConsumer) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        List<CrowdfundingAttachment> attachments = mqMessage.getPayload();
        if (CollectionUtils.isEmpty(attachments)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        for (CrowdfundingAttachment crowdfundingAttachment : attachments) {
            // 获取算法侧p图结果
            AttachmentAttrModel attachmentAttrModel = requestAiPsResult(crowdfundingAttachment);
            // 更新
            cfAttachmentAttrFeignClient.updateAiResult(attachmentAttrModel);
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private AttachmentAttrModel requestAiPsResult(CrowdfundingAttachment crowdfundingAttachment) {

        AnalysisUrl analysisUrl = AnalysisUrl.parse(crowdfundingAttachment.getUrl());
        if (CfDomainEnum.COS_IMAGES.getDomain().equals(analysisUrl.getHost()) || CfDomainEnum.OSS_OSS.getDomain().equals(analysisUrl.getHost())) {
            analysisUrl.setHost(CfDomainEnum.COS_IMAGE.getDomain());
        }

        AttachmentAttrModel attachmentAttrModel = AttachmentAttrModel.builder()
                .attachmentId(crowdfundingAttachment.getId())
                .aiTime(System.currentTimeMillis())
                .build();

        // 请求ai旧模型，获取p图结果
        fullOldAiPsResult(attachmentAttrModel, crowdfundingAttachment, analysisUrl);

        // 请求ai新模型，获取p图结果
        fullNewAiPsResult(attachmentAttrModel, crowdfundingAttachment, analysisUrl);

        return attachmentAttrModel;

    }

    private void fullNewAiPsResult(AttachmentAttrModel attachmentAttrModel, CrowdfundingAttachment crowdfundingAttachment, AnalysisUrl analysisUrl) {
        // 请求
        Map<String, Object> map = buildRequestAiMap(crowdfundingAttachment.getId(), analysisUrl.toUrlString());
        OceanApiRequest oceanApiRequest = buildInitRequest(NEW_TAG, JSONObject.toJSONString(map), "104309a4d0d9207b");
        Response<OceanApiResponse> agent = oceanApiClient.agent(oceanApiRequest);
        log.info("ImageAIRecognitionConsumer new ai-ps-recognition {} {}", oceanApiRequest, agent);

        // 解析
        String body = Optional.ofNullable(agent)
                .filter(Response::ok)
                .map(Response::getData)
                .map(OceanApiResponse::getBody)
                .orElse("");
        AiImageRecognitionResultBody aiImageRecognitionResultBody = JSONObject.parseObject(body, AiImageRecognitionResultBody.class);
        if (Objects.isNull(aiImageRecognitionResultBody)) {
            attachmentAttrModel.setNewAiPs("");
            return;
        }

        JSONObject bodyObject = JSONObject.parseObject(aiImageRecognitionResultBody.getBody());
        attachmentAttrModel.setNewAiPs(Optional.ofNullable(bodyObject).map(f -> f.getString("ps")).orElse(""));
    }

    private void fullOldAiPsResult(AttachmentAttrModel attachmentAttrModel, CrowdfundingAttachment crowdfundingAttachment, AnalysisUrl analysisUrl) {

        // 请求
        Map<String, Object> map = buildRequestAiMap(crowdfundingAttachment.getId(), analysisUrl.toUrlString());
        OceanApiRequest oceanApiRequest = buildInitRequest(TAG, JSONObject.toJSONString(map), TOKEN);
        Response<OceanApiResponse> agent = oceanApiClient.agent(oceanApiRequest);
        log.info("ImageAIRecognitionConsumer old ai-ps-recognition {} {}", oceanApiRequest, agent);

        // 解析
        String body = Optional.ofNullable(agent)
                .filter(Response::ok)
                .map(Response::getData)
                .map(OceanApiResponse::getBody)
                .orElse("");
        AiImageRecognitionResultBody aiImageRecognitionResultBody = JSONObject.parseObject(body, AiImageRecognitionResultBody.class);
        if (Objects.isNull(aiImageRecognitionResultBody)
                || !StringUtils.equals(aiImageRecognitionResultBody.getImg_id(), String.valueOf(crowdfundingAttachment.getId()))) {
            attachmentAttrModel.setAiPsProb("");
            attachmentAttrModel.setAiPsSoftware("");
            return;
        }

        attachmentAttrModel.setAiPsProb(Optional.ofNullable(String.valueOf(aiImageRecognitionResultBody.getPs_prob())).orElse(""));
        attachmentAttrModel.setAiPsSoftware(Optional.ofNullable(aiImageRecognitionResultBody.getPs_software()).orElse(""));
    }

    private OceanApiRequest buildInitRequest(String tag, String body, String token) {
        OceanApiRequest oceanApiRequest = new OceanApiRequest();
        oceanApiRequest.setTag(tag);
        oceanApiRequest.setUserId(USER_ID);
        oceanApiRequest.setToken(token);
        oceanApiRequest.setBody(body);
        return oceanApiRequest;
    }

    private Map<String, Object> buildRequestAiMap(int id, String url) {
        Map<String, Object> map = new HashMap<>();
        map.put("img_id", id);
        map.put("img_url", url);
        return map;
    }

}
