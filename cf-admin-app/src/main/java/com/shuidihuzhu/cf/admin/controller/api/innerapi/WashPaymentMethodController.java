package com.shuidihuzhu.cf.admin.controller.api.innerapi;

import com.alibaba.excel.EasyExcel;
import com.shuidihuzhu.cf.admin.controller.api.util.ExcelListener;
import com.shuidihuzhu.cf.model.admin.WashPaymentMethod;
import com.shuidihuzhu.cf.service.report.ReportScheduleService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/12/24  3:17 下午
 */
@RestController
@Slf4j
@RequestMapping("innerapi/cf/admin/wash")
public class WashPaymentMethodController {

    @Resource
    private ReportScheduleService reportScheduleService;

    @PostMapping("/upload-excel")
    public Response<String> upload(MultipartFile file) throws IOException {
        try {
            ExcelListener excelListener = new ExcelListener();
            EasyExcel.read(file.getInputStream(), WashPaymentMethod.class, excelListener).sheet().doRead();
            List<WashPaymentMethod> dataList = excelListener.getList().stream()
                    .map(item -> (WashPaymentMethod) item).collect(Collectors.toList());
            reportScheduleService.washTheData(dataList);
            return NewResponseUtil.makeSuccess("success");
        } catch (IOException e) {
            return NewResponseUtil.makeSuccess("false");
        }
    }
}
