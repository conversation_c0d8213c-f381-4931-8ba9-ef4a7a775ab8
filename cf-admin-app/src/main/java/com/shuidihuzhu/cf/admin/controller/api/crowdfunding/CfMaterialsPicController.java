package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.model.admin.CfMaterialsPic;
import com.shuidihuzhu.cf.service.admin.CfMaterialsPicService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2021/3/18
 */
@RestController
@RequestMapping(path = "/admin/material/pic/")
public class CfMaterialsPicController {

    @Autowired
    private CfMaterialsPicService cfMaterialsPicService;

    @PostMapping("/get")
    @RequiresPermission("materials-pic:get")
    public Response<List<CfMaterialsPic>> get(@RequestParam("caseId") int caseId) {

        return NewResponseUtil.makeSuccess(cfMaterialsPicService.getByCaseId(caseId));
    }



    @PostMapping("/save")
    @RequiresPermission("materials-pic:save")
    public Response<String> save(@RequestBody List<CfMaterialsPic> list) {

        cfMaterialsPicService.save(list, ContextUtil.getAdminUserId());

        return NewResponseUtil.makeSuccess("succ");
    }
}
