package com.shuidihuzhu.cf.admin.controller.api.patientRecruit;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import com.shuidihuzhu.recruit.client.feign.projectManage.PatientProjectClient;
import com.shuidihuzhu.recruit.client.model.projectManage.ProjectDiseaseDO;
import com.shuidihuzhu.recruit.client.model.projectManage.ProjectOperateLog;
import com.shuidihuzhu.recruit.client.model.projectManage.ProjectResearchCenterDO;
import com.shuidihuzhu.recruit.client.model.projectManage.ProjectSearchParam;
import com.shuidihuzhu.recruit.client.model.projectManage.ProjectUpdateParam;
import com.shuidihuzhu.recruit.client.model.projectManage.RecruitProjectDO;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

@RestController
@Slf4j
@RequestMapping("admin/cf/patient-recruit/project")
public class ProjectManageController {

    @Autowired
    private PatientProjectClient projectClient;

    @ApiOperation("添加或修改")
    @PostMapping("add-or-update")
    public Response<String> addOrUpdate(@RequestBody RecruitProjectDO projectDO){
        projectDO.setUserId(ContextUtil.getAdminUserId());

        RpcResult<String> result = projectClient.addOrUpdate(projectDO);

        return result != null ? ResponseUtil.makeResponse(result.getCode(), result.getMsg(), "") :
        ResponseUtil.makeError(ErrorCode.SYSTEM_UNRECOGNIZED_ERROR);
    }

    @ApiOperation("列表页查询")
    @PostMapping("select-by-param")
    public Response<PageInfo<RecruitProjectDO>> selectByParam(@RequestBody ProjectSearchParam param) {

        RpcResult<PageInfo<RecruitProjectDO>> result = projectClient.selectByParam(param);
        return ResponseUtil.makeSuccess(result == null ? null : result.getData());
    }

    @ApiOperation("疾病名称的查询")
    @PostMapping("select-disease")
    public Response<List<ProjectDiseaseDO>> selectByNameLike(@RequestParam String diseaseName) {

        RpcResult<List<ProjectDiseaseDO>> result = projectClient.selectDiseaseByNameLike(
                StringUtils.trimToEmpty(diseaseName));

        return ResponseUtil.makeSuccess(result == null ? null : result.getData());
    }

    @ApiOperation("批量/单个 案例状态修改")
    @PostMapping("update-status")
    public Response<Integer> updateProjectStatus(@RequestBody ProjectUpdateParam updateParam) {

        updateParam.setLastUpdateId(ContextUtil.getAdminUserId());
        RpcResult<Integer> result = projectClient.updateProjectStatus(updateParam);

        return result != null && Objects.requireNonNullElse(result.getData(), 0) > 0 ?
                ResponseUtil.makeSuccess("") : ResponseUtil.makeFail("没有满足条件的修改");
    }

    @ApiOperation("查找日志")
    @PostMapping("select-logs")
    public Response<Integer> selectLogByPage(int current, int pageSize) {

        RpcResult<PageInfo<ProjectOperateLog>> result = projectClient.selectLogByPage(current, pageSize);

        return ResponseUtil.makeSuccess(result == null ?  null : result.getData());
    }

    @ApiOperation("查找研究中心")
    @PostMapping("select-research-center")
    public Response<List<ProjectResearchCenterDO>> selectResearchByNameLike(@RequestParam String name) {


        RpcResult<List<ProjectResearchCenterDO>> result = projectClient.selectResearchByNameLike(
                StringUtils.trimToEmpty(name));

        return ResponseUtil.makeSuccess(result == null ? null : result.getData());
    }

    @ApiOperation("查找项目")
    @PostMapping("select-project-by-id")
    public Response<RecruitProjectDO> selectProjectById(@RequestParam("id") long id) {

        RpcResult<List<RecruitProjectDO>> result = projectClient.selectProjectByIds(
                Lists.newArrayList(id));

        return ResponseUtil.makeSuccess(result != null && CollectionUtils.isNotEmpty(result.getData()) ?
                result.getData().get(0) : null);
    }

}
