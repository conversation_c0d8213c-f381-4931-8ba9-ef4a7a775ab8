package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.admin.channel.CfChannelBiz;
import com.shuidihuzhu.cf.biz.admin.channel.CfChannelGroupBiz;
import com.shuidihuzhu.common.web.constants.CrowdfundingCons;
import com.shuidihuzhu.cf.enums.AdminErrorCode;

import com.shuidihuzhu.cf.enums.admin.OperationHistoryType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.cf.model.admin.OperationHistory;
import com.shuidihuzhu.cf.model.admin.channel.CfChannel;
import com.shuidihuzhu.cf.model.admin.channel.CfChannelGroup;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.admin.PageUtil;
import com.shuidihuzhu.cf.vo.crowdfunding.CfChannelVo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by wangsf on 17/2/16.
 */

@Controller
@RequestMapping(path="/admin/cf/channel")
public class CfChannelController {

	private static final Logger LOGGER = LoggerFactory.getLogger(CfChannelController.class);

	@Autowired
	private CfChannelGroupBiz cfChannelGroupBiz;

	@Autowired
	private CfChannelBiz cfChannelBiz;



	@RequestMapping(path="/groups", method = RequestMethod.POST)
	@ResponseBody
	@RequiresPermission("cf-channel:groups")
	public Response listGroups(Integer current, Integer pageSize) {
		if (pageSize == null || pageSize < CrowdfundingCons.MIN_PAGE_SIZE
				|| pageSize > CrowdfundingCons.MAX_PAGE_SIZE) {
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
		}
		try {
			List<CfChannelGroup> cfChannelGroupList = this.cfChannelGroupBiz.listGroupsByPage(current, pageSize);
			Map<String, Object> response = Maps.newHashMap();
			response.put("groups", cfChannelGroupList);
			response.put("pagination", PageUtil.transform2PageMap(cfChannelGroupList));
			return NewResponseUtil.makeSuccess(response);
		} catch (Exception e) {
			LOGGER.error("CfChannelController listGroups error! ", e);
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
		}
	}

	@RequestMapping(path = "/list", method = RequestMethod.POST)
	@ResponseBody
	@RequiresPermission("cf-channel:list")
	public Response listChannelsByGroupId(@RequestParam(name="groupId", required = true) Integer groupId,
										  Integer current, Integer pageSize) {
//        if (pageSize == null || pageSize < CrowdfundingCons.MIN_PAGE_SIZE
//                || pageSize > CrowdfundingCons.MAX_PAGE_SIZE) {
//            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
//        }
		if(groupId == null) {
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
		}
		try {
			List<CfChannel> cfChannelList;
			if (current == null && pageSize ==null) {
				cfChannelList = cfChannelBiz.listByGroupIdAndPage(groupId);
			} else {
				cfChannelList = groupId == 0 ?
						this.cfChannelBiz.listByPage(current, pageSize) :
						this.cfChannelBiz.listByGroupIdAndPage(groupId, current, pageSize);
			}
			List<CfChannelVo> channelVoList = getCfChannelVos(cfChannelList);
			Map<String, Object> response = Maps.newHashMap();
			response.put("pagination", PageUtil.transform2PageMap(cfChannelList));
			response.put("channels", channelVoList);

			return NewResponseUtil.makeSuccess(response);

		} catch (Exception e) {
			LOGGER.error("CfChannelController listGroups error!", e);
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
		}
	}

	@RequestMapping(path = "/add", method = RequestMethod.POST)
	@ResponseBody
	@RequiresPermission("cf-channel:add")
	public Response addChannel(@RequestParam(name ="groupId", required = true) Integer groupId,
								@RequestParam(name="name", required = true) String name,
	                           @RequestParam(name="description", required = true) String description) {
		if(groupId == null || groupId <= 0 || StringUtils.isBlank(name) || StringUtils.isBlank(description)) {
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
		}

		try {
			CfChannel cfChannel = this.cfChannelBiz.getByName(name);
			if(cfChannel != null) {
				return NewResponseUtil.makeError(AdminErrorCode.CHANNEL_EXISTS);
			}

			this.cfChannelBiz.add(new CfChannel(groupId, name, description));
			CfChannel channel = this.cfChannelBiz.getByName(name);
			if(channel == null) {
				LOGGER.error("CfChannelController add-channel failed!");
				return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
			}
			
			return NewResponseUtil.makeSuccess(channel);
		} catch (Exception e) {
			LOGGER.error("CfChannelController add-channel error!", e);
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
		}
	}

	@RequestMapping(path = "/edit", method = RequestMethod.POST)
	@ResponseBody
	@RequiresPermission("cf-channel:edit")
	public Response editChannel( @RequestParam(name = "channelId") Integer channelId,
								@RequestParam(name ="groupId", required = true) Integer groupId,
	                           @RequestParam(name="description", required = true) String description) {
		if(channelId == null || channelId <= 0 ||
				groupId == null || groupId <= 0 || StringUtils.isBlank(description)) {
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
		}

		try {
			CfChannel cfChannel = this.cfChannelBiz.get(channelId);
			if(cfChannel == null) {
				return NewResponseUtil.makeError(AdminErrorCode.CHANNEL_NOT_EXISTS);
			}

			this.cfChannelBiz.update(channelId, groupId, description);
			CfChannel channel = this.cfChannelBiz.get(channelId);
			if(channel == null) {
				LOGGER.error("CfChannelController edit-channel failed!");
				return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
			}

			return NewResponseUtil.makeSuccess(null);
		} catch (Exception e) {
			LOGGER.error("CfChannelController edit-channel error!", e);
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
		}
	}

	@RequestMapping(path = "/s", method = RequestMethod.POST)
	@ResponseBody
	@RequiresPermission("cf-channel:search")
	public Response searchChannel(@RequestParam(name = "keyword", required = true) String keyword,
	                              @RequestParam(name = "groupId", required = false) Integer groupId,
								  @RequestParam Integer current, @RequestParam Integer pageSize) {
		try {
			if(groupId == null || groupId < 0) {
				groupId = 0;
			}
            List<CfChannel> cfChannelList;
            cfChannelList =
					this.cfChannelBiz.prefixSearch(keyword, groupId, current, pageSize);
			if(cfChannelList == null) {
				cfChannelList = Lists.newArrayList();
			}
			List<CfChannelVo> channelVoList = getCfChannelVos(cfChannelList);

			Map<String, Object> response = Maps.newHashMap();
			response.put("pagination", PageUtil.transform2PageMap(cfChannelList));
			response.put("channels", channelVoList);

			return NewResponseUtil.makeSuccess(response);

		} catch (Exception e) {
			LOGGER.error("CfChannelController listGroups error!", e);
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
		}
	}

	private List<CfChannelVo> getCfChannelVos(List<CfChannel> cfChannelList) {
		List<CfChannelVo> channelVoList = Lists.newArrayList();
		Set<Integer> groupIdSet = Sets.newHashSet();
		for(CfChannel channel : cfChannelList) {
			CfChannelVo cfChannelVo= new CfChannelVo(channel);
			cfChannelVo.setGroupId(channel.getGroupId());
			channelVoList.add(cfChannelVo);
			groupIdSet.add(channel.getGroupId());
		}

		if(groupIdSet.size() > 0) {
			Map<Integer, CfChannelGroup> groups = this.cfChannelGroupBiz.getMapByIds(Lists.newArrayList(groupIdSet));
			for(CfChannelVo cfChannelVo : channelVoList) {
				int cfGroupId = cfChannelVo.getGroupId();
				if(cfGroupId > 0) {
					CfChannelGroup cfChannelGroup = groups.get(cfGroupId);
					if(cfChannelGroup != null) {
						cfChannelVo.setGroupName(cfChannelGroup.getName());
					}
				}
			}
		}
		return channelVoList;
	}

	@RequestMapping(path = "add-channel-group", method = RequestMethod.POST)
    @ResponseBody
	@RequiresPermission("cf-channel:add-channel-group")
	public Response addChannelGroup(@RequestParam String name, @RequestParam String desc) {
	    LOGGER.info("addChannelGroup name:{}, desc:{}, userId:{}", name, desc, ContextUtil.getAdminUserId());
	    if (StringUtils.isBlank(name) || StringUtils.isBlank(desc)) {
	        return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CfChannelGroup cfChannelGroup = new CfChannelGroup();
        cfChannelGroup.setName(name);
        cfChannelGroup.setDescription(desc);
        cfChannelGroupBiz.insertOne(cfChannelGroup);
	    return NewResponseUtil.makeSuccess(null);
    }
}
