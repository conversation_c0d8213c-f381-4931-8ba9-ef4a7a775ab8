package com.shuidihuzhu.cf.admin.controller.api.innerapi.pure;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.adminpure.feign.CaseApproveLifeCircleFeignClient;
import com.shuidihuzhu.cf.client.adminpure.model.transformorder.ApproveLifeCircleDTO;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.service.approve.lifecircle.CaseApproveLifeCircleService;
import com.shuidihuzhu.cf.vo.approve.ApproveLifeCircleVO;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
public class CaseApproveLifeCircleFeignController implements CaseApproveLifeCircleFeignClient {

    @Autowired
    private CaseApproveLifeCircleService caseApproveLifeCircleService;

    @Override
    public OperationResult<List<ApproveLifeCircleDTO>> getLifeCircle(int caseId) {
        List<ApproveLifeCircleVO> approveLifeCircleVOList = caseApproveLifeCircleService.getLifeCircle(caseId).getData();
        if(CollectionUtils.isEmpty(approveLifeCircleVOList)){
            return OperationResult.success();
        }
        List<ApproveLifeCircleDTO> approveLifeCircleDTOList = transform2ApproveLifeCircleDTO(approveLifeCircleVOList);
        return OperationResult.success(approveLifeCircleDTOList);
    }

    @NotNull
    private List<ApproveLifeCircleDTO> transform2ApproveLifeCircleDTO(List<ApproveLifeCircleVO> approveLifeCircleVOList) {
        List<ApproveLifeCircleDTO> approveLifeCircleDTOList = Lists.newArrayList();
        for(ApproveLifeCircleVO approveLifeCircleVO : approveLifeCircleVOList){
            ApproveLifeCircleDTO approveLifeCircleDTO = new ApproveLifeCircleDTO();
            approveLifeCircleDTO.setName(approveLifeCircleVO.getName());
            approveLifeCircleDTO.setStatus(approveLifeCircleVO.getStatus());
            approveLifeCircleDTO.setTime(approveLifeCircleVO.getTime());
            approveLifeCircleDTO.setNodeStatus(approveLifeCircleVO.getNodeStatus().name());
            approveLifeCircleDTO.setRejectMsgs(approveLifeCircleVO.getRejectMsgs());
            approveLifeCircleDTOList.add(approveLifeCircleDTO);
        }
        return approveLifeCircleDTOList;
    }
}
