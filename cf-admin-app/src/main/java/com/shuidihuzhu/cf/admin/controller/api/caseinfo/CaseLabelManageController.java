package com.shuidihuzhu.cf.admin.controller.api.caseinfo;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.model.label.CfCaseLabelInfo;
import com.shuidihuzhu.cf.service.tag.CfCaseLabelService;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.cf.growthtool.model.RuleJudge;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description: 案例标签管理
 * @Author: panghairui
 * @Date: 2023/8/9 2:47 PM
 */
@Api("案例标签管理")
@RestController
@RequestMapping("/admin/cf/caseinfo/label-manage")
public class CaseLabelManageController {

    @Resource
    private CfCaseLabelService cfCaseLabelService;

    private final static List<String> provinceList = Lists.newArrayList("福建省","西藏自治区","中国香港","贵州省","上海市","广东省","湖北省","湖南省","澳门特别行政区","四川省","安徽省","新疆维吾尔自治区","江苏省","吉林省","宁夏回族自治区","河北省","河南省","广西壮族自治区","中国台湾","海南省","江西省","重庆市","云南省","北京市","甘肃省","山东省","陕西省","浙江省","内蒙古自治区","青海省","天津市","辽宁省","黑龙江省","山西省");

    @ApiOperation("案例标签管理-添加案例标签")
    @PostMapping("add-case-label")
    @RequiresPermission("label-manage:add-case-label")
    public Response<Long> saveCaseLabel(@RequestBody CfCaseLabelInfo cfCaseLabelInfo) {

        if (Objects.isNull(cfCaseLabelInfo)) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }

        cfCaseLabelInfo.setOperatorId(ContextUtil.getAdminLongUserId());

        return cfCaseLabelService.addOrUpdateCaseLabel(cfCaseLabelInfo);
    }

    @ApiOperation("案例标签管理-查询案例标签")
    @PostMapping("query-case-label")
    @RequiresPermission("label-manage:query-case-label")
    public Response<Map<String, Object>> queryCaseLabel(@RequestParam(value = "labelName", required = false) String labelName,
                                                        @RequestParam(value = "status", required = false) Integer status,
                                                        @RequestParam(value = "current", required = false, defaultValue = "1") Integer current,
                                                        @RequestParam(value = "size", required = false, defaultValue = "20") Integer size) {
        return NewResponseUtil.makeSuccess(cfCaseLabelService.queryCaseLabel(labelName, status, current, size));
    }

    @ApiOperation("案例标签管理-标签启用禁用")
    @PostMapping("enable-case-label")
    @RequiresPermission("label-manage:enable-case-label")
    public Response<List<CfCaseLabelInfo>> enableCaseLabel(@RequestParam("id") Long id, @RequestParam("status") Integer status) {

        long userId = ContextUtil.getAdminLongUserId();
        Boolean isSuccess = cfCaseLabelService.enableCaseLabel(id, status, userId);
        if (!isSuccess) {
            return NewResponseUtil.makeFail("存在优先级冲突");
        }

        return NewResponseUtil.makeSuccess(null);
    }

    @ApiOperation("案例标签管理-查询单个标签数据")
    @PostMapping("query-case-label-by-id")
    @RequiresPermission("label-manage:query-case-label-by-id")
    public Response<CfCaseLabelInfo> queryCaseLabelById(@RequestParam("id") Long id) {
        return NewResponseUtil.makeSuccess(cfCaseLabelService.queryCaseLabelById(id));
    }

    @ApiOperation("案例标签管理-字段枚举")
    @PostMapping("enum-case-label")
    @RequiresPermission("label-manage:enum-case-label")
    public Response<Map<String, List<String>>> enumCaseLabel() {

        Map<String, List<String>> result = Maps.newHashMap();

        PreposeMaterialModel.AccidentType[] accidentTypes = PreposeMaterialModel.AccidentType.values();
        result.put("caseType", Arrays.stream(accidentTypes).map(PreposeMaterialModel.AccidentType::getDesc).collect(Collectors.toList()));

        PreposeMaterialModel.PatientIdentity[] patientIdentity = PreposeMaterialModel.PatientIdentity.values();
        result.put("patientIdentity", Arrays.stream(patientIdentity).map(PreposeMaterialModel.PatientIdentity::getDesc).collect(Collectors.toList()));

        result.put("province", provinceList);

        return NewResponseUtil.makeSuccess(result);
    }

}
