package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.admin.util.admin.AdminCfIdCardUtil;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.finance.client.feign.deposit.CfDepositAccountFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.model.vo.CfPaBankBranchVo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.util.BackCardUtil;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.baseservice.pay.model.CardBinParam;
import com.shuidihuzhu.client.baseservice.pay.model.CardBinResult;
import com.shuidihuzhu.client.baseservice.pay.model.PayRpcResponse;
import com.shuidihuzhu.client.baseservice.pay.model.v1.PaySubBankQueryReq;
import com.shuidihuzhu.client.baseservice.pay.model.v1.PaySubBankQueryRes;
import com.shuidihuzhu.client.baseservice.pay.v1.BankCardBinInfoClient;
import com.shuidihuzhu.client.baseservice.pay.v1.PaySubBankClientV1;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.enums.Platform;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.IdCardUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Base64Utils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/admin/cf/bankcard/")
public class CfAdminBankCardController {
    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;
    @Autowired
    private CfDepositAccountFeignClient cfDepositAccountFeignClient;
    @Autowired
    private BankCardBinInfoClient bankCardBinInfoClient;
    @Autowired
    private PaySubBankClientV1 paySubBankClientV1;

    /**
     * 获取开户行
     * @param bankCard
     * @return
     */
    @PostMapping("get-opening-bank")
    @RequiresPermission("bankCard:get-opening-bank")
    public Response<CardBinResult> getOpeningBank(@RequestParam(value = "bankCard") String bankCard) {
        if (StringUtils.isEmpty(bankCard)) {
            return NewResponseUtil.makeFail("银行卡信息不可以为空");
        }
        if (bankCard.length() < 9) {
            return NewResponseUtil.makeFail("银行卡位数不足，请检查");
        }
        if (!StringUtils.isNumeric(bankCard)) {
            return NewResponseUtil.makeFail("银行卡号只能是阿拉伯数字，请检查");
        }
        CardBinParam cardBinParam = new CardBinParam();
        cardBinParam.setBankCardNum(Base64Utils.encodeToString(bankCard.getBytes()));
        cardBinParam.setClientId(16);
        PayRpcResponse<CardBinResult> payRpcResponse = bankCardBinInfoClient.getBankInfoByCardBin(cardBinParam);
        log.info("CfAdminBankCardController payRpcResponse:{}\tbankCard:{}", JSON.toJSONString(payRpcResponse), bankCard);

        if (payRpcResponse.isSuccess() && payRpcResponse.getResult() != null) {
            return NewResponseUtil.makeSuccess(payRpcResponse.getResult());
        } else {
            return NewResponseUtil.makeFail("未获取到银行卡信息，请手动填写");
        }
    }

    /**
     * 获取开户支行信息
     */
    @PostMapping("get-pa-branch-bank")
    @RequiresPermission("bankCard:get-pa-branch-bank")
    public Response<List<CfPaBankBranchVo>> getPaBranchBank(@RequestParam(value = "bankNameKeyWord", required = false, defaultValue = "") @ApiParam(value = "银行名称关键字") String bankNameKeyWord,
                                                              @RequestParam(value = "bankBranchKeyWord", required = false, defaultValue = "") @ApiParam(value = "网点名称关键字") String bankBranchKeyWord,
                                                              @RequestParam(value = "cityCode", required = false, defaultValue = "") String cityCode) {

        if (StringUtils.isEmpty(bankNameKeyWord)) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (StringUtils.isBlank(cityCode)) {
            if (StringUtils.isEmpty(bankBranchKeyWord)) {
                return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
            }
            FeignResponse<List<CfPaBankBranchVo>> branchNames = cfDepositAccountFeignClient.getBranchNames(bankNameKeyWord, bankBranchKeyWord);
            if (branchNames.ok()) {
                return NewResponseUtil.makeSuccess(branchNames.getData());
            }
        } else {
            PaySubBankQueryReq queryReq = PaySubBankQueryReq.builder()
                    .bankGroupName(bankNameKeyWord)
                    .cityCode(cityCode)
                    .bankName(bankBranchKeyWord)
                    .build();
            PayRpcResponse<List<PaySubBankQueryRes>> response = paySubBankClientV1.listSubBank(queryReq);
            if (Objects.nonNull(response) && response.isSuccess() && CollectionUtils.isNotEmpty(response.getResult())) {

                List<PaySubBankQueryRes> result = response.getResult();

                return NewResponseUtil.makeSuccess(result.stream().map(r -> {
                    CfPaBankBranchVo cfPaBankBranchVo = new CfPaBankBranchVo();
                    cfPaBankBranchVo.setBankBranchName(r.getBankName());
                    cfPaBankBranchVo.setCnapsCode(r.getBankCode());
                    return cfPaBankBranchVo;
                }).collect(Collectors.toList()));
            }
        }
        return NewResponseUtil.makeSuccess(Collections.emptyList());
    }

    @PostMapping("open-payee-account")
    @RequiresPermission("bankCard:open-payee-account")
    public Response<Void> openPayeeAccount(@RequestParam @ApiParam(value = "案例infoUuid") String infoUuid,
                                           @RequestParam @ApiParam(value = "持卡人姓名") String payeeName,
                                           @RequestParam @ApiParam(value = "持卡人卡号") String bankCard,
                                           @RequestParam @ApiParam(value = "发卡行名称") String bankName,
                                           @RequestParam @ApiParam(value = "持卡人身份证号") String idCard,
                                           @RequestParam(required = false, defaultValue = "") @ApiParam(value = "开户支行对应的联行号") String cnapsBranchId,
                                           @RequestParam(required = false, defaultValue = "") @ApiParam(value = "开户支行营业网点信息") String bankBranchName) {


        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_NOT_EXIST_RECORD_ERROR);
        }

        payeeName = StringUtils.trimToEmpty(payeeName);
        bankCard = StringUtils.trimToEmpty(bankCard);
        bankName = StringUtils.trimToEmpty(bankName);
        idCard = StringUtils.trimToEmpty(idCard);
        cnapsBranchId = StringUtils.trimToEmpty(cnapsBranchId);

        if (StringUtils.isBlank(bankName) || StringUtils.isBlank(payeeName)) {
            return NewResponseUtil.makeError(CfErrorCode.CF_INFO_PARAM_ERROR_PAYEE);
        }
        if (StringUtils.isBlank(bankCard) || !BackCardUtil.checkBankCard(bankCard)) {
            return NewResponseUtil.makeError(CfErrorCode.CF_BANK_CARD_VERIFY_FAILED);
        }
        if (IdCardUtil.illegal(idCard)) {
            return NewResponseUtil.makeError(CfErrorCode.CF_PARAM_ERROR_PAYEE_ID_CARD_ERROR);
        }
        //身份证不足18位不让提交（过滤掉15位的身份证）
        if (!AdminCfIdCardUtil.isValidIdCard(idCard)) {
            return NewResponseUtil.makeError(CfErrorCode.USER_INFO_ID_CARD_ERROR);
        }

        // 年龄小于18
        if (AdminCfIdCardUtil.getIdCardAge(idCard) < 18) {
            return NewResponseUtil.makeError(CfErrorCode.PAYEE_NOT_ALLOW_OF_AGE);
        }

        FeignResponse<Void> response = cfDepositAccountFeignClient.openPayeeAccount(infoUuid, payeeName, bankCard, bankName, idCard, cnapsBranchId, bankBranchName, ContextUtil.getAdminLongUserId(), ContextUtil.getPlatform(), ContextUtil.getClientIp());
        return NewResponseUtil.makeResponse(response.getCode(), response.getMsg(), null);
    }
}
