package com.shuidihuzhu.cf.admin.controller.api.innerapi;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.admin.controller.api.workorder.InitialAuditPreModifyController;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditPreModifyService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditSearchService;
import com.shuidihuzhu.cf.vo.admin.initialAudit.*;
import com.shuidihuzhu.client.cf.admin.client.InitialAuditPreModifyFeign;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @author: fengxuan
 * @create 2021-04-02 3:56 下午
 * {@link InitialAuditPreModifyController}
 **/
@Slf4j
@RestController
public class InitialAuditPreModifyFeignController implements InitialAuditPreModifyFeign {

    @Autowired
    private InitialAuditSearchService auditSearchService;

    @Autowired
    private InitialAuditPreModifyService preModifyService;
    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;


    /**
     * 参考
     *
     * @param param
     * @return
     */
    @Override
    public Response<String> queryCanModifyState(String param) {
        InitialPreModifyHandleVo handleVo = null;
        try {
            handleVo = JSON.parseObject(param, InitialPreModifyHandleVo.class);
        } catch (Exception e) {
            log.error("参数解析错误 param:{} ", param, e);
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        //找到有哪些驳回项
        if (handleVo.getCaseId() <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        InitialCanPreModifyState state = null;
        InitialAuditOperationItem.RejectOptionSet rejectOptionSet = auditSearchService.queryRejectList(handleVo.getCaseId(), 0);
        if (rejectOptionSet == null || CollectionUtils.isEmpty(rejectOptionSet.getRejectIds())) {
            return NewResponseUtil.makeSuccess(null);
        }
        //设置参数
        handleVo.setRejectIds(rejectOptionSet.getRejectIds());
        handleVo.setWorkOrderType(WorkOrderType.bohui.getType());
        //找到
        state = preModifyService.queryCanModifyState(handleVo);
        if (state != null) {
            return NewResponseUtil.makeSuccess(JSON.toJSONString(state));
        }
        return NewResponseUtil.makeSuccess("");
    }

    @Override
    public Response<String> savePreModifyMaterials(String param) {
        return doSavePreModifyMaterials(param);
    }

    @NotNull
    private Response<String> doSavePreModifyMaterials(String param) {
        //userId已经在调用方设置了
        InitialPreModifyHandleVo handleVo = null;
        try {
            handleVo = JSON.parseObject(param, InitialPreModifyHandleVo.class);
        } catch (Exception e) {
            log.error("参数解析错误 param:{} ", param, e);
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo info = adminCrowdfundingInfoBiz.getFundingInfoById(handleVo.getCaseId());

        InitialPreModifyHandleVo.InitialMaterial modifyMaterials = handleVo.getModifyMaterials();
        if (Objects.nonNull(modifyMaterials) && Objects.nonNull(modifyMaterials.getCaseBaseInfo())) {
            InitialAuditCaseDetail.CaseBaseInfo caseBaseInfo = modifyMaterials.getCaseBaseInfo();
            caseBaseInfo.setTargetAmount(info.getTargetAmount());
            modifyMaterials.setCaseBaseInfo(caseBaseInfo);
        }


        InitialPreModifyHandleVo.InitialMaterial userSourceMaterials = handleVo.getUserSourceMaterials();
        if (Objects.nonNull(userSourceMaterials) && Objects.nonNull(userSourceMaterials.getCaseBaseInfo())) {
            InitialAuditCaseDetail.CaseBaseInfo userSourceMaterialsCaseBaseInfo = userSourceMaterials.getCaseBaseInfo();
            userSourceMaterialsCaseBaseInfo.setTargetAmount(info.getTargetAmount());
            userSourceMaterials.setCaseBaseInfo(userSourceMaterialsCaseBaseInfo);
        }

        handleVo.setModifyMaterials(modifyMaterials);
        handleVo.setUserSourceMaterials(userSourceMaterials);

        AdminErrorCode result = preModifyService.savePreModifyMaterials(handleVo);
        return result == AdminErrorCode.SUCCESS ? NewResponseUtil.makeSuccess("") : NewResponseUtil.makeError(result);
    }

    @Override
    public Response<String> savePreModifyMaterialsV2(String param) {
        return doSavePreModifyMaterials(param);
    }


    @Override
    public Response<String> selectPreModifyHistory(int caseId) {
        List<InitialPreModifyHandleHistory> handleHistoryList = preModifyService.selectPreModifyHistory(caseId);
        String result = "";
        if (CollectionUtils.isNotEmpty(handleHistoryList)) {
            result = JSON.toJSONString(handleHistoryList);
        }
        return NewResponseUtil.makeSuccess(result);
    }


    @Override
    public Response<Integer> selectCaseVersion(String infoUuid) {
        return NewResponseUtil.makeSuccess(preModifyService.selectCaseVersion(infoUuid));
    }

    @Override
    public Response<String> verifyIdCard(String param, int userId) {
        InitialAuditCaseDetail.FirstApproveCaseInfo firstApproveCaseInfo = null;
        try {
            firstApproveCaseInfo = JSON.parseObject(param, InitialAuditCaseDetail.FirstApproveCaseInfo.class);
        } catch (Exception e) {
            log.error("参数解析错误 param:{} ", param, e);
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CfErrorCode cfErrorCode = preModifyService.verifyIdCard(firstApproveCaseInfo, userId);
        return NewResponseUtil.makeError(cfErrorCode);
    }

    @Override
    public Response<String> selectIdCardForPreModify(int caseId) {
        InitialAuditCaseDetail.FirstApproveCaseInfo firstApproveCaseInfo = preModifyService.selectIdCardForPreModify(caseId);
        return NewResponseUtil.makeSuccess(JSON.toJSONString(firstApproveCaseInfo));
    }
}
