package com.shuidihuzhu.cf.admin.configuration;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import io.shardingjdbc.core.api.ShardingDataSourceFactory;
import io.shardingjdbc.core.api.config.ShardingRuleConfiguration;
import io.shardingjdbc.core.api.config.TableRuleConfiguration;
import io.shardingjdbc.core.api.config.strategy.InlineShardingStrategyConfiguration;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.Collections;
import java.util.Map;
import java.util.Properties;

@Configuration
@RefreshScope
public class CFCommentShardingDataSourceConfiguration {

    @Resource(name = CfDataSource.SHUIDI_CF_COMMENT)
    private DataSource cfCommentDataSource;

    @Resource(name = "datasource-cf-api-cf-comment-slave")
    private DataSource cfCommentSlaveDataSource;

    @Value("${shardingjdbc.sql.show:true}")
    private String sqlShow;

    @Value("${shardingjdbc.executor.size:20}")
    private String executorSize;


    @Bean(CfDataSource.SHUIDI_CF_COMMENT_SHARE)
    public DataSource cfCommentShardingCrowdfunding() throws SQLException {
        return buildDataSource(Collections.singletonMap("cfComment", cfCommentDataSource));
    }

    @Bean("cfCommentSlaveShardingCrowdfunding")
    public DataSource cfCommentSlaveShardingCrowdfunding() throws SQLException {
        return buildDataSource(Collections.singletonMap("cfComment", cfCommentSlaveDataSource));
    }

    private DataSource buildDataSource(Map<String, DataSource> dataSource) throws SQLException {
        TableRuleConfiguration commentIdtocrowdfundingId = new TableRuleConfiguration();
        commentIdtocrowdfundingId.setLogicTable("crowdfunding_comment_id_to_crowdfunding_id");
        commentIdtocrowdfundingId.setActualDataNodes("cfComment.crowdfunding_comment_id_to_crowdfunding_id_${0..99}");
        commentIdtocrowdfundingId.setTableShardingStrategyConfig(new InlineShardingStrategyConfiguration("id", "crowdfunding_comment_id_to_crowdfunding_id_${id % 100}"));

        TableRuleConfiguration crowdfundingCommentShare = new TableRuleConfiguration();
        crowdfundingCommentShare.setLogicTable("crowdfunding_comment_share");
        crowdfundingCommentShare.setActualDataNodes("cfComment.crowdfunding_comment_share_${0..99}");
        crowdfundingCommentShare.setTableShardingStrategyConfig(new InlineShardingStrategyConfiguration("crowdfunding_id", "crowdfunding_comment_share_${crowdfunding_id % 100}"));

        ShardingRuleConfiguration shardingRuleConfig = new ShardingRuleConfiguration();
        shardingRuleConfig.setDefaultDataSourceName("cfComment");
        shardingRuleConfig.getTableRuleConfigs().add(commentIdtocrowdfundingId);
        shardingRuleConfig.getTableRuleConfigs().add(crowdfundingCommentShare);

        Properties prop = new Properties();
        prop.setProperty("sql.show", sqlShow);
        prop.setProperty("executor.size", executorSize);
        return ShardingDataSourceFactory.createDataSource(dataSource, shardingRuleConfig, Collections.emptyMap(), prop);
    }

    public static void main(String[] args) {

        String str = "create table crowdfunding_comment_share_%s\n" +
                "select *\n" +
                "from crowdfunding_comment_share where 1=2;";
        for (int i = 0; i<100;i++){
            System.out.println(String.format(str, i));
        }
    }
}


