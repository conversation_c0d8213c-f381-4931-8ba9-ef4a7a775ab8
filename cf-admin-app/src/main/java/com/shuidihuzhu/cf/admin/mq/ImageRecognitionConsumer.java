package com.shuidihuzhu.cf.admin.mq;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.ai.algo.client.ClaimAiPictureClient;
import com.shuidihuzhu.ai.algo.model.param.WatermarkDetectionParam;
import com.shuidihuzhu.ai.algo.model.vo.ClaimWatermarkVo;
import com.shuidihuzhu.cf.client.feign.CfAttachmentFeignClient;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment;
import com.shuidihuzhu.cf.service.AdminImageService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.List;

@Service
@Slf4j
@RocketMQListener(id = MQTagCons.CF_ATTACHMENT_MARK_ATTR_STAT,
        tags = MQTagCons.CF_ATTACHMENT_MARK_ATTR_STAT,
        topic = MQTopicCons.CF)
public class ImageRecognitionConsumer implements MessageListener<List<CrowdfundingAttachment>> {

    @Autowired
    private AdminImageService adminImageService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<List<CrowdfundingAttachment>> mqMessage) {
        List<CrowdfundingAttachment> attachments = mqMessage.getPayload();
        if (CollectionUtils.isEmpty(attachments)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        for (CrowdfundingAttachment attachment : attachments) {
            if (attachment == null
                    || attachment.getType() == AttachmentTypeEnum.ATTACH_VIDEO
                    || attachment.getType() == AttachmentTypeEnum.ATTACH_PAYEE_RELATION_VIDEO) {
                continue;
            }
            String url = attachment.getUrl();
            String replaceUrl = adminImageService.convertSingleOriginUrl(url);
            if (StringUtils.isBlank(url) || url.equals(replaceUrl)){
                continue;
            }
            int responseCode = imageRecognition(replaceUrl, attachment.getId());
            if (responseCode == 1){
                //需要重试
                log.info("ImageRecognitionConsumer retry id:{}", attachment.getId());
                return ConsumeStatus.RECONSUME_LATER;
            }
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private int imageRecognition(String url, long id) {
        return 0;
    }

}
