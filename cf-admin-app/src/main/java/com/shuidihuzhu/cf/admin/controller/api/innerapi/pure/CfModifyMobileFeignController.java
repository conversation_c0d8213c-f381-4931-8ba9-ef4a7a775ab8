package com.shuidihuzhu.cf.admin.controller.api.innerapi.pure;

import com.shuidihuzhu.cf.client.adminpure.feign.CfModifyMobileFeignClient;
import com.shuidihuzhu.cf.client.adminpure.model.caseinfo.CfModifyMobileRecord;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.service.crowdfunding.modify.CfModifyMobileService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2021/9/14 21:34
 * @Description:
 */
@RestController
public class CfModifyMobileFeignController implements CfModifyMobileFeignClient {

    @Resource
    private CfModifyMobileService cfModifyMobileService;

    @Override
    public OperationResult<List<CfModifyMobileRecord>> selectCfModifyMobileRecordByCaseId(int caseId) {
        List<CfModifyMobileRecord> cfModifyMobileRecordList = cfModifyMobileService.getByCaseId(caseId);
        return OperationResult.success(cfModifyMobileRecordList);
    }
}
