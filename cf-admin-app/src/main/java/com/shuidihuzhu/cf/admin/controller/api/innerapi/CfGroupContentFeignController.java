package com.shuidihuzhu.cf.admin.controller.api.innerapi;


import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingDetailSendMsgTemplateBiz;
import com.shuidihuzhu.client.cf.admin.client.CfGroupContentFeignClient;
import com.shuidihuzhu.client.model.SmsContentModel;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Slf4j
public class CfGroupContentFeignController implements CfGroupContentFeignClient {

    @Autowired
    private AdminCrowdfundingDetailSendMsgTemplateBiz crowdfundingDetailSendMsgTemplateBiz;

    /**
     * 获取某一指定类型的模板
     */
    @Override
    public Response<List<SmsContentModel>> getGroupContentList(@RequestParam("smsGroupCode") int smsGroupCode){
        return ResponseUtil.makeSuccess(crowdfundingDetailSendMsgTemplateBiz.getGroupContentList(smsGroupCode));
    }


}

