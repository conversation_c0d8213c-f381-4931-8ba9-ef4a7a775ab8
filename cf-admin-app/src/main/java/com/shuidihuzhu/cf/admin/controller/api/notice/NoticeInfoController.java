//package com.shuidihuzhu.cf.admin.controller.api.notice;
//
//import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
//import com.shuidihuzhu.cf.enums.AdminErrorCode;
//import org.apache.commons.lang3.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.cloud.context.config.annotation.RefreshScope;
//import org.springframework.stereotype.Controller;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestMethod;
//import org.springframework.web.bind.annotation.ResponseBody;
//
//
//import com.shuidihuzhu.cf.admin.util.HuzhuRequestUtil;
//import com.shuidihuzhu.common.web.util.NewResponseUtil;
//
//
///**
// * Created by chao on 16/12/12.
// */
//@RefreshScope
//@Controller
//@RequestMapping("admin/cf/notice/info")
//public class NoticeInfoController {
//
//	@Value("${url.shuidi.api:}")
//	private String apiUrl;
//	private String baseUrl = null;
//
//	private static final Logger LOGGER = LoggerFactory.getLogger(NoticeInfoController.class);
//
//	@RequestMapping(path = "addNotice", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
//	@ResponseBody
//	@RequiresPermission("notice-info:operate")
//	public Object addNotice(String noticeNo, Integer insuranceId, String userInfoIds, Integer showSequence) {
//		if (null == insuranceId) {
//			return NewResponseUtil.makeFail("insuranceId不能为空");
//		}
//		if (null == showSequence) {
//			showSequence = 0;
//		}
//		return HuzhuRequestUtil.getRequestResult(getBaseUrl(), "addNotice", "noticeNo", noticeNo, "insuranceId", "" + insuranceId,
//		                                         "userInfoIds", userInfoIds, "showSequence", "" + showSequence);
//	}
//
//	@RequestMapping(path = "addOrUpdateNoticeInfo", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
//	@ResponseBody
//	@RequiresPermission("notice-info:operate")
//	public Object addNoticeInfo(String data) {
//		return HuzhuRequestUtil.getRequestResult(getBaseUrl(), "addOrUpdateNoticeInfo", "data", data);
//	}
//
//	@RequestMapping(path = "updateShowStatus", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
//	@ResponseBody
//	@RequiresPermission("notice-info:operate")
//	public Object updateShowStatus(String noticeNo, Integer showStatus) {
//		if (null == showStatus) {
//			return NewResponseUtil.makeFail("showStatus不能为空");
//		}
//		return HuzhuRequestUtil.getRequestResult(getBaseUrl(), "updateShowStatus", "noticeNo", noticeNo, "showStatus", "" + showStatus);
//	}
//
//	@RequestMapping(path = "detail", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
//	@ResponseBody
//	@RequiresPermission("notice-info:operate")
//	public Object detail(String noticeNo) {
//		return HuzhuRequestUtil.getRequestResult(getBaseUrl(), "detail", "noticeNo", noticeNo);
//	}
//
//	@RequestMapping(path = "allNotice", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
//	@ResponseBody
//	@RequiresPermission("notice-info:operate")
//	public Object allNotice(Integer pageNo, Integer pageSize) {
//		if (null == pageNo || pageNo <= 0 || null == pageSize || pageSize <= 0) {
//			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
//		}
//		return HuzhuRequestUtil.getRequestResult(getBaseUrl(), "allNotice", "pageNo", "" + pageNo, "pageSize", "" + pageSize);
//	}
//
//	@RequestMapping(path = "allNotPaidNotice", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
//	@ResponseBody
//	@RequiresPermission("notice-info:operate")
//	public Object allNotPaidNotice() {
//		return HuzhuRequestUtil.getRequestResult(getBaseUrl(), "allNotPaidNotice");
//	}
//
//	@RequestMapping(path = "allNoticeInfo", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
//	@ResponseBody
//	@RequiresPermission("notice-info:operate")
//	public Object allNoticeInfo(Integer pageNo, Integer pageSize) {
//		if (null == pageNo || pageNo <= 0 || null == pageSize || pageSize <= 0) {
//			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
//		}
//		return HuzhuRequestUtil.getRequestResult(getBaseUrl(), "allNoticeInfo", "pageNo", "" + pageNo, "pageSize", "" + pageSize);
//	}
//
//	/**
//	 * 是否可以执行下一步
//	 * @param noticeNo
//	 * @return
//	 */
//	@RequestMapping(path = "displayNotice", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
//	@ResponseBody
//	@RequiresPermission("notice-info:operate")
//	public Object displayNotice(String noticeNo) {
//		if (StringUtils.isEmpty(noticeNo)) {
//			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
//		}
//		return HuzhuRequestUtil.getRequestResult(getBaseUrl(), "displayNotice", "noticeNo", noticeNo);
//	}
//
//	@RequestMapping(path = "sharingInfo", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
//	@ResponseBody
//	@RequiresPermission("notice-info:operate")
//	public Object getSharingInfo(String noticeNo, Integer insuranceId) {
//		if (StringUtils.isEmpty(noticeNo) || insuranceId == null || insuranceId <= 0) {
//			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
//		}
//		return HuzhuRequestUtil.getRequestResult(getBaseUrl(), "sharingInfo", "noticeNo", noticeNo, "insuranceId", "" + insuranceId);
//	}
//
//	private String getBaseUrl() {
//		if (null == baseUrl) {
//			baseUrl = apiUrl + "/admin/notice/info/";
//		}
//
//		return baseUrl;
//	}
//}
