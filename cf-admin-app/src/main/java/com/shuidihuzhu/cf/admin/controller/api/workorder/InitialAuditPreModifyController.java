package com.shuidihuzhu.cf.admin.controller.api.workorder;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.client.feign.cipher.constants.DesensitizeEnum;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.client.auth.saas.annotation.OperationLog;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditPreModifyService;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditCaseDetail;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialCanPreModifyState;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialPreModifyHandleHistory;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialPreModifyHandleVo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@RestController
@Slf4j
@RequestMapping("/admin/cf/initial-audit/pre-modify")
public class InitialAuditPreModifyController {

    @Autowired
    private InitialAuditPreModifyService preModifyService;

    @Autowired
    private MaskUtil maskUtil;

    @RequiresPermission("pre-modify:select-can-modify-fields")
    @ApiOperation("初审代修改-可以修改那些字段")
    @PostMapping("select-can-modify-fields")
    public Response<InitialCanPreModifyState> queryCanModifyState(@RequestParam String param) {

        InitialPreModifyHandleVo handleVo = null;
        try {
            handleVo = JSON.parseObject(param, InitialPreModifyHandleVo.class);
        } catch (Exception e) {
            log.error("参数解析错误 param:{} ", param, e);
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        InitialCanPreModifyState result;
        try {
            result = preModifyService.queryCanModifyState(handleVo);
        } catch (Throwable e) {
            log.error("查询运营可以修改的字段异常.handleVo:{} ", handleVo, e);
            return NewResponseUtil.makeFail(e.getMessage());
        }
        return NewResponseUtil.makeSuccess(result);
    }

    @RequiresPermission("pre-modify:save-pre-modify-material")
    @ApiOperation("初审代修改-保存")
    @PostMapping("save-pre-modify-material")
    public Response<String> savePreModifyMaterials(@RequestParam String param) {

        InitialPreModifyHandleVo handleVo = null;
        try {
            handleVo = JSON.parseObject(param, InitialPreModifyHandleVo.class);
        } catch (Exception e) {
            log.error("参数解析错误 param:{} ", param, e);
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        handleVo.setUserId(ContextUtil.getAdminUserId());

        AdminErrorCode result = preModifyService.savePreModifyMaterials(handleVo);

        return result == AdminErrorCode.SUCCESS ? NewResponseUtil.makeSuccess("") : NewResponseUtil.makeError(result);
    }

    @RequiresPermission("pre-modify:select-pre-modify-history")
    @ApiOperation("初审代修改-查看修改的历史")
    @PostMapping("select-pre-modify-history")
    public Response<List<InitialPreModifyHandleHistory>> selectPreModifyHistory(@RequestParam int caseId) {
        List<InitialPreModifyHandleHistory> initialPreModifyHandleHistories = preModifyService.selectPreModifyHistory(caseId);
        if (CollectionUtils.isNotEmpty(initialPreModifyHandleHistories)) {
            initialPreModifyHandleHistories.forEach(item -> {
                InitialPreModifyHandleVo.InitialMaterial userSourceMaterial = item.getUserSourceMaterials();
                this.mask(userSourceMaterial);
                InitialPreModifyHandleVo.InitialMaterial modifyMaterial = item.getModifyMaterials();
                this.mask(modifyMaterial);

            });
        }
        return NewResponseUtil.makeSuccess(initialPreModifyHandleHistories);
    }

    private void mask(InitialPreModifyHandleVo.InitialMaterial modifyMaterial)
    {
        Optional.ofNullable(modifyMaterial)
                .filter(r -> Objects.nonNull(r.getFirstApproveCaseInfo()))
                .ifPresent(r -> {
                    InitialAuditCaseDetail.FirstApproveCaseInfo firstApproveCaseInfo = r.getFirstApproveCaseInfo();
                    firstApproveCaseInfo.setSelfIdCard(StringUtils.EMPTY);
                    firstApproveCaseInfo.setPatientIdCard(StringUtils.EMPTY);
                    firstApproveCaseInfo.setPatientBornCard(StringUtils.EMPTY);
                });
    }

    @RequiresPermission("pre-modify:select-case-version")
    @ApiOperation("查看案例的版本")
    @PostMapping("select-case-version")
    public Response<Integer> selectCaseVersion(@RequestParam String infoUuid) {
        return NewResponseUtil.makeSuccess(preModifyService.selectCaseVersion(infoUuid));
    }

    @RequiresPermission("pre-modify:verify-idcard")
    @ApiOperation("校验身份证")
    @PostMapping("verify-idcard")
    public Response<Response<String>> verifyIdCard(@RequestBody InitialAuditCaseDetail.FirstApproveCaseInfo caseInfo) {

        CfErrorCode errorCode = preModifyService.verifyIdCard(caseInfo, ContextUtil.getAdminUserId());
        return NewResponseUtil.makeSuccess(NewResponseUtil.makeResponse(errorCode.getCode(), errorCode.getMsg(), ""));
    }

    @RequiresPermission("pre-modify:select-idcard")
    @ApiOperation("查看具体的身份做修改～")
    @PostMapping("select-idcard")
    @OperationLog(name = "查看身份证号")
    public Response<InitialAuditCaseDetail.FirstApproveCaseInfo> selectIdCardForPreModify(@RequestParam int caseId) {
        InitialAuditCaseDetail.FirstApproveCaseInfo caseInfo = preModifyService.selectIdCardForPreModify(caseId);
        if (Objects.nonNull(caseInfo)) {
            if (StringUtils.isNotBlank(caseInfo.getPatientIdCard())) {
                caseInfo.setPatientIdCardMask(maskUtil.buildByDecryptStrAndType(caseInfo.getPatientIdCard(), DesensitizeEnum.IDCARD));
                caseInfo.setPatientIdCard(null);
            }

            if (StringUtils.isNotBlank(caseInfo.getSelfIdCard())) {
                caseInfo.setSelfIdCardMask(maskUtil.buildByDecryptStrAndType(caseInfo.getSelfIdCard(), DesensitizeEnum.IDCARD));
                caseInfo.setSelfIdCard(null);
            }
        }
        return NewResponseUtil.makeSuccess(caseInfo);
    }

}
