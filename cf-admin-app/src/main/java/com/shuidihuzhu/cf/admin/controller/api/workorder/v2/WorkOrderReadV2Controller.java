package com.shuidihuzhu.cf.admin.controller.api.workorder.v2;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.client.base.page.v1.model.PaginationListVO;
import com.shuidihuzhu.cf.model.crowdfunding.vo.QueryListResultVo;
import com.shuidihuzhu.cf.param.workorder.WorkOrderListQueryOldParam;
import com.shuidihuzhu.cf.service.workorder.read.WorkOrderReadService;
import com.shuidihuzhu.common.web.model.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RequestMapping("/admin/work-order/v2")
@RestController
public class WorkOrderReadV2Controller {

    @Autowired
    private WorkOrderReadService workOrderReadService;

    @RequiresPermission("work-order:get-workorder-list")
    @RequestMapping(path = "get-work-order-list-v2")
    public Response<PaginationListVO<QueryListResultVo>> getWorkOrderList(@RequestBody() WorkOrderListQueryOldParam queryListParam) {
        return workOrderReadService.getWorkOrderList(queryListParam);

    }

}
