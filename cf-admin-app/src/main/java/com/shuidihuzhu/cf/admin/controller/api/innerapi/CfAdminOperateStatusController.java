package com.shuidihuzhu.cf.admin.controller.api.innerapi;


import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfCaseOperateStatus;
import com.shuidihuzhu.cf.service.cfOperatingProfile.CfOperateStatusBiz;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("innerapi/cf/admin/operate")
@Slf4j
@RefreshScope
public class CfAdminOperateStatusController {

    @Autowired
    private CfOperateStatusBiz operateStatusBiz;

    @Value("${apollo.allow.user.submit.payee.relation.video:1}")
    private int allowSubmitPayeeVideo = 1;
    /**
     * 用户是否需要提交 收款人关系的视频
     * @param infoUuid
     * @return
     */
    @PostMapping("/canSubmitPayeeRelationVideo")
    public Response<Boolean> canSubmitPayeeRelationVideo(String infoUuid) {

        if (allowSubmitPayeeVideo == 0) {
            log.info("allowSubmitPayeeVideo 为0 不允许用户提交收款人关系视频");
            return  NewResponseUtil.makeSuccess(false);
        }
       return  operateStatusBiz.getRelationVideoOperateType(infoUuid) == CfCaseOperateStatus
               .OperateStatus.CANCEL_PAYEE_RELATION.getCode() ?
               NewResponseUtil.makeSuccess(true) : NewResponseUtil.makeSuccess(false);
    }
}
