package com.shuidihuzhu.cf.admin.controller.api.customer;


import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.service.MobileUserIdModel;
import com.shuidihuzhu.account.utils.CryptoUserIdUtil;
import com.shuidihuzhu.cf.biz.customer.SeaChatHistoryBiz;
import com.shuidihuzhu.cf.biz.customer.UdeskChatRecordBiz;
import com.shuidihuzhu.cf.customer.CfChatRecordDO.ChannelFrom;
import com.shuidihuzhu.cf.customer.ChatHistoryModelVo;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.client.grpc.account.v1.feign.SimpleUserAccountServiceClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.admin.PageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Map;

/**
 * @package: com.shuidihuzhu.cf.admin.controller.api.customer
 * @Author: zhoutain
 * @Date: 2019-06-26  14:15
 */
@Controller
@RequestMapping(path = "/admin/cf/customer")
@Slf4j
public class SeaChatHistoryController {

    @Autowired
    private SeaChatHistoryBiz seaChatHistoryBiz;
    @Autowired
    private UdeskChatRecordBiz udeskChatRecordBiz;
    @Autowired
    private SimpleUserAccountServiceClient simpleUserAccountServiceClient;

    /**
     * 获得聊天记录
     *
     * @param accountId
     * @param phoneNumber
     * @param startTime
     * @param endTime
     * @return
     */
    @ResponseBody
    @RequestMapping(path = "/get-chat-history", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    public Response<Map<String, Object>> createTask(@RequestParam(name = "accountId", required = false, defaultValue = "0") long accountId,
                                                    @RequestParam(name = "phoneNumber", required = false, defaultValue = "") String phoneNumber,
                                                    @RequestParam(name = "startTime", required = false, defaultValue = "") String startTime,
                                                    @RequestParam(name = "endTime", required = false, defaultValue = "") String endTime,
                                                    @RequestParam(name = "current", required = false, defaultValue = "1") int current,
                                                    @RequestParam(name = "pageSize", required = false, defaultValue = "20") int pageSize) {
        log.info("accountId={},phoneNumber:{} 获取从{}到{}的呼入聊天记录列表，当前页={}, pageSize={}",
                accountId, phoneNumber, startTime, endTime, current, pageSize);

        // 再进行参数效验
        if (accountId <= 0L && StringUtils.isEmpty(phoneNumber)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        // 再进行参数效验
        if (accountId > 0L && StringUtils.isNotEmpty(phoneNumber)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<Long> userIds = Lists.newArrayList();
        List<String> cryptoUserIds = Lists.newArrayList();
        if (accountId > 0) {
            userIds.add(accountId);
            cryptoUserIds.add(CryptoUserIdUtil.cryptoUserId(accountId));
        } else {
            MobileUserIdModel mobileUserIdResponse = simpleUserAccountServiceClient.getUserIdByMobile(phoneNumber);
            long mobileUserId = null == mobileUserIdResponse ? 0 : mobileUserIdResponse.getUserId();
            if (mobileUserId != 0) {
                userIds.add(mobileUserId);
                cryptoUserIds.add(CryptoUserIdUtil.cryptoUserId(mobileUserId));
            }
        }
        // 首先获取userId/phoneNumber对应的cid集合
        List<String> cids = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(cryptoUserIds)) {
            cids = seaChatHistoryBiz.getCidsByCryptoUserIds(cryptoUserIds);
        }

        List<ChatHistoryModelVo> chatHistoryByCids = seaChatHistoryBiz
                    .getChatHistoryByCids(startTime, endTime, cids);
        if(CollectionUtils.isEmpty(chatHistoryByCids)) {
            chatHistoryByCids = Lists.newArrayList();
        }

        List<ChatHistoryModelVo> udeskChatRecords = udeskChatRecordBiz.getUdeskChatRecords(startTime, endTime, userIds);
        if (!CollectionUtils.isEmpty(udeskChatRecords)) {
            chatHistoryByCids.addAll(udeskChatRecords);
        }
        if (CollectionUtils.isEmpty(chatHistoryByCids)) {
            return NewResponseUtil.makeSuccess(null);
        }

        List<ChatHistoryModelVo> result;
        int total = chatHistoryByCids.size();
        try {
            if (total <= pageSize) {
                result = chatHistoryByCids;
            } else if (current * pageSize >= total) {
                result = chatHistoryByCids.subList((current - 1) * pageSize, total);
            } else {
                result = chatHistoryByCids.subList((current - 1) * pageSize, current * pageSize);
            }
        } catch (Exception e) {
            return NewResponseUtil.makeError(AdminErrorCode.PAGE_INFO_ERROR);
        }

        Map<String, String> pageInfo = Maps.newHashMap();
        pageInfo.put("total", String.valueOf(total));
        pageInfo.put("pageSize", String.valueOf(pageSize));
        pageInfo.put("current", String.valueOf(current));
        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put("pageInfo", pageInfo);
        resultMap.put("record", result);
        return NewResponseUtil.makeSuccess(resultMap);
    }

    @ResponseBody
    @RequestMapping(path = "/get-chat-record-by-cid", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    public Response<Map<String, Object>> queryChatRecord(@RequestParam(name = "cId") String cId,
                                                    @RequestParam(name = "channelFrom", required = false, defaultValue = "1") int channelFrom,
                                                    @RequestParam(name = "current", required = false, defaultValue = "1") int current,
                                                    @RequestParam(name = "pageSize", required = false, defaultValue = "20") int pageSize){
        if(StringUtils.isEmpty(cId)){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        ChannelFrom channel = ChannelFrom.getByCode(channelFrom);
        if (channel == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<ChatHistoryModelVo> chatHistoryModelVos = Lists.newArrayList();
        if (channel == ChannelFrom.ZHICI) {
            chatHistoryModelVos = seaChatHistoryBiz.queryChatRecordByCid(cId, current, pageSize);
        } else {
            boolean numeric = StringUtils.isNumeric(cId);
            if (!numeric) {
                log.warn("在线咨询,cid为非数字,cid:{}", cId);
            }
            chatHistoryModelVos = udeskChatRecordBiz.getUdeskByCid(Integer.parseInt(cId), current, pageSize);//已检查过
        }

        if(CollectionUtils.isEmpty(chatHistoryModelVos)){
            return NewResponseUtil.makeSuccess(null);
        }

        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put("pageInfo", PageUtil.transform2PageMap(chatHistoryModelVos));
        resultMap.put("record", chatHistoryModelVos);
        return NewResponseUtil.makeSuccess(resultMap);
    }

}
