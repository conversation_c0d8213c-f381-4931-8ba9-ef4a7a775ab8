package com.shuidihuzhu.cf.admin.mq.casestatus;

import com.shuidihuzhu.cf.admin.mq.common.CommonConsumerHelper;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderCaseBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.crowdfunding.CfBaseStatus;
import com.shuidihuzhu.cf.model.crowdfunding.message.CfStatusChangeRecord;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2018-12-23  15:55
 */
@Service
@Slf4j
@RocketMQListener(id = MQTagCons.CF_STATUS_CHANGE_MSG,
        group = "cf-admin-status-group",
        tags = MQTagCons.CF_STATUS_CHANGE_MSG,
        topic = MQTopicCons.CF)
public class AdminCaseStatusChangeConsumer implements MessageListener<CfStatusChangeRecord>, CommonConsumerHelper.Handler<CfStatusChangeRecord> {

    @Resource
    private CommonConsumerHelper commonConsumerHelper;

    @Resource
    private AdminWorkOrderCaseBiz adminWorkOrderCaseBiz;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfStatusChangeRecord> mqMessage) {
        return commonConsumerHelper.consumeMessage(mqMessage, this, false, log);
    }

    @Override
    public boolean handle(ConsumerMessage<CfStatusChangeRecord> consumerMessage) {
        CfStatusChangeRecord record = consumerMessage.getPayload();
        int status = record.getStatus();
        if (status == CfBaseStatus.cf_end.getCode()) {
            handleCaseEnd(record);
        }
        return true;
    }

    private void handleCaseEnd(CfStatusChangeRecord record) {
        long caseId = record.getCaseId();
        log.info("案例结束自动结束首次沟通工单 caseId={}", caseId);
        adminWorkOrderCaseBiz.onCaseEnd(caseId);
    }
}
