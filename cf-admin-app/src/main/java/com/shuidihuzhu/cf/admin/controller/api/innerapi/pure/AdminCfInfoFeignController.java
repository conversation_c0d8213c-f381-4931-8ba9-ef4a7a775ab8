package com.shuidihuzhu.cf.admin.controller.api.innerapi.pure;

import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.client.adminpure.feign.AdminCfInfoFeignClient;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.enums.approve.ApproveLifeCircleNodeStatusEnum;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.approve.lifecircle.CaseApproveLifeCircleService;
import com.shuidihuzhu.cf.vo.approve.ApproveLifeCircleVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: wangpeng
 * @Date: 2022/3/14 11:03
 * @Description:
 */
@RestController
public class AdminCfInfoFeignController implements AdminCfInfoFeignClient {

    @Resource
    private CaseApproveLifeCircleService caseApproveLifeCircleService;

    @Override
    public OperationResult<String> getCurrentLifeCircle(int caseId) {
        OpResult<List<ApproveLifeCircleVO>> lifeCircle = caseApproveLifeCircleService.getLifeCircle(caseId);
        List<ApproveLifeCircleVO> circleVOList = Optional.ofNullable(lifeCircle)
                .map(OpResult::getData)
                .orElse(Collections.emptyList())
                .stream()
                .filter(f -> Objects.equals(f.getNodeStatus(), ApproveLifeCircleNodeStatusEnum.PROCESSING) || Objects.equals(f.getNodeStatus(), ApproveLifeCircleNodeStatusEnum.PASSED))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(circleVOList)) {
            return OperationResult.success();
        }

        return OperationResult.success(JSONObject.toJSONString(circleVOList.get(circleVOList.size() - 1)));
    }
}
