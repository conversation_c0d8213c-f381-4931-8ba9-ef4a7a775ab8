package com.shuidihuzhu.cf.admin.util;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.admin.annotation.NoVersionRequired;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.vo.crowdfunding.account.UserInfoModelVo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.MobileUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiParam;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by ahrievil on 2017/2/21.
 */
@RestController
@RequestMapping(path = "/admin/crowdfunding/useless")
public class UselessController {

    private static final Logger LOGGER = LoggerFactory.getLogger(UselessController.class);
    private String privateToken = "1ddccd17-cf30-4ae0-8455-56be282d7be3";
    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @NoVersionRequired
    @RequestMapping(path = "/sendFrequency", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response sendAndEmptyFrequency(String code, String beginStr, String endStr){
        if (!privateToken.equals(code)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_TOKEN_ERROR);
        }
        return NewResponseUtil.makeSuccess(null);
    }

    @RequestMapping(path = "/get-info-from-mobile", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response getInfoFromMobile(String mobiles, @RequestParam(defaultValue = "0") Long accountUserId, Integer userId) {
        LOGGER.info("UselessController getInfoFromMobile mobiles:{}, accountUserId:{}, userId:{}", mobiles, accountUserId, userId);
        if(StringUtil.isBlank(mobiles) && accountUserId == 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        Set<String> hashSet;
        if (StringUtils.isNotBlank(mobiles)) {
            hashSet = Sets.newHashSet(mobiles.split(","));
        } else {
            hashSet = Sets.newHashSet();
        }
        List<UserInfoModelVo> userInfoModelVoList = Lists.newArrayList();
        if (hashSet.stream().anyMatch(MobileUtil::illegal)) {
            return NewResponseUtil.makeResponse(1, "手机号输入错误", null);
        }

        Map<String, Object> result = Maps.newHashMap();
        try {
            List<UserInfoModel> userInfoByMobile = Lists.newArrayList();
            hashSet.forEach(val -> {
                UserInfoModel userInfoByMobile1 = userInfoServiceBiz.getUserInfoByMobile(val);
                if (! Objects.isNull(userInfoByMobile1)) userInfoByMobile.add(userInfoByMobile1);
            });
            Set<Long> userIdList = Sets.newHashSet();
            userIdList.addAll(userInfoByMobile.stream().map(UserInfoModel::getUserId).collect(Collectors.toList()));
            if (accountUserId != 0) {
                userIdList.add(accountUserId);
                UserInfoModel userInfoByUserId = userInfoServiceBiz.getUserInfoByUserId(accountUserId);
                userInfoByMobile.add(userInfoByUserId);
            }
            Map<Long, UserInfoModel> userInfoModelMap = userInfoByMobile.stream().collect(Collectors.toMap(UserInfoModel::getUserId, Function.identity()));
            userIdList.forEach(value -> {
                UserInfoModelVo userInfoModelVo = new UserInfoModelVo();
                UserInfoModel userInfoModel = userInfoModelMap.get(value);
                if (userInfoModel != null) {
                    userInfoModelVo.setUserId(userInfoModel.getUserId());
                }
                userInfoModelVoList.add(userInfoModelVo);
            });
            result.put("list", userInfoModelVoList);
        } catch (Exception e) {
            LOGGER.error("getInfoFromMobile error", e);
        }
        return NewResponseUtil.makeSuccess(result);
    }

    @RequestMapping(path = "/modify-case-userid", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response modifyCaseUserId(@ApiParam("案例标识 id或info_id") @RequestParam(value = "caseKey") String caseKey,
                                     @ApiParam("目标用户的手机号") @RequestParam(value = "phone", required = false) String phone,
                                     @ApiParam("目标用户的userId") @RequestParam(value = "userId", required = false) long userId){
        if(StringUtils.isEmpty(phone) && userId <= 0){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        long targetUserId = userId;

        if(StringUtils.isNotEmpty(phone)){
            UserInfoModel userInfo = userInfoServiceBiz.getUserInfoByMobile(phone);
            if(null == userInfo || userInfo.getUserId() <= 0){
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
            }
            targetUserId = userInfo.getUserId();
        }

        int caseId = handlerCaseKey(caseKey);

        int result = adminCrowdfundingInfoBiz.updateCaseUserId(caseId, targetUserId);

        return NewResponseUtil.makeSuccess(result > 0 ? "success" : "fail");
    }

    Integer handlerCaseKey(String caseKey) {
        Integer caseId = 0;
        if (caseKey.contains("-")) {
            CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(caseKey);
            caseId = crowdfundingInfo == null ? null : crowdfundingInfo.getId();
        } else {
            caseId = Integer.parseInt(caseKey);//已检查过
        }

        return caseId;
    }
}
