package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.service.MobileUserIdModel;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfPropertyTransferHistoryBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingOrderBiz;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.grpc.account.v1.feign.SimpleUserAccountServiceClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.Consts;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.admin.PageUtil;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by Ahrievil on 2017/12/12
 */
@Controller
@RequestMapping(path = "/admin/cf/order/handle", method = RequestMethod.POST, produces = "application/json;charset=utf-8")
public class AdminCfOrderHandleController {

    private final static Logger LOGGER = LoggerFactory.getLogger(AdminCfOrderHandleController.class);

    @Autowired
    private AdminCrowdfundingOrderBiz adminCrowdfundingOrderBiz;
    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;
    @Autowired
    private AdminCfPropertyTransferHistoryBiz adminCfPropertyTransferHistoryBiz;
    @Autowired
    private SimpleUserAccountServiceClient simpleUserAccountServiceClient;

    @RequestMapping(path = "/get-order-list")
    @ResponseBody
    @RequiresPermission("cf-order-handle:get-order-list")
    public Response getOrderList(String mobile, Integer caseId, Integer thirdType,
                                 @RequestParam(defaultValue = "1") Integer current,
                                 @RequestParam(defaultValue = "10") Integer pageSize) {
        LOGGER.info("AdminCfOrderHandleController getOrderList mobile:{}, caseId:{}, thirdType:{}, current:{}, pageSize:{}",
                mobile, caseId, thirdType, current, pageSize);
        if (StringUtils.isBlank(mobile) && caseId == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        Long userId = null;
        if (StringUtils.isNotBlank(mobile) && !Consts.pMobile.matcher(mobile).matches()) {
            return NewResponseUtil.makeError(AdminErrorCode.MOBILE_FORMAT_ERROR);
        }

        if (StringUtils.isNotBlank(mobile)) {
            MobileUserIdModel userIdByMobile = simpleUserAccountServiceClient.getUserIdByMobile(mobile);
            if (userIdByMobile != null) {
                userId = userIdByMobile.getUserId();
            } else {
                return NewResponseUtil.makeError(AdminErrorCode.NO_SUCH_PERSON);
            }
        }
        if (caseId != null) {
            CrowdfundingInfo crowdfunding = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
            if (crowdfunding == null) {
                return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
            }
        }
        Map<String, Object> result = Maps.newHashMap();
        List<CrowdfundingOrder> crowdfundingOrders = adminCrowdfundingOrderBiz.selectByUserIdAndThirdTypeTidbFeign(userId,
                caseId, thirdType, current, pageSize);
        result.put("list", crowdfundingOrders);
        result.put("pagination", PageUtil.transform2PageMap(crowdfundingOrders));
        return NewResponseUtil.makeSuccess(result);
    }

    @RequestMapping(path = "get-case-order")
    @ResponseBody
    @RequiresPermission("cf-order-handle:get-case-order")
    public Response getCaseOrder(@RequestParam Integer caseId, String beginTime, String endTime,
                                 @RequestParam(defaultValue = "1") Integer current,
                                 @RequestParam(defaultValue = "10") Integer pageSize) {
        LOGGER.info("AdminCfOrderHandleController getCaseOrder caseId:{}, beginTime:{}, endTime:{}, current:{}, pageSize",
                caseId, beginTime, endTime, current, pageSize);
        if (caseId == null || StringUtil.isBlank(beginTime) || StringUtil.isBlank(endTime)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }
        List<CrowdfundingOrder> crowdfundingOrders = adminCrowdfundingOrderBiz.selectByCaseIdAndTimeTidbFeign(caseId,
                beginTime, endTime, current, pageSize);
        Map<String, Object> result = Maps.newHashMap();
        result.put("pagination", PageUtil.transform2PageMap(crowdfundingOrders));
        result.put("list", crowdfundingOrders);
        return NewResponseUtil.makeSuccess(result);
    }

    @RequestMapping(path = "get-first-and-last")
    @ResponseBody
    @RequiresPermission("cf-order-handle:get-first-and-last")
    public Response getFirstAndLast(Integer caseId) {
        LOGGER.info("AdminCfOrderHandleController getFirstAndLast caseId:{}", caseId);
        int minHandle = 1;
        int maxHandle = 2;
        CrowdfundingOrder min = adminCrowdfundingOrderBiz.selectExtreme(caseId, minHandle);
        CrowdfundingOrder max = adminCrowdfundingOrderBiz.selectExtreme(caseId, maxHandle);
        Map<String, Object> result = Maps.newHashMap();
        result.put("first", min);
        result.put("last", max);
        return NewResponseUtil.makeSuccess(result);
    }

    @RequestMapping(path = "get-transfer")
    @ResponseBody
    @RequiresPermission("cf-order-handle:get-transfer")
    public Response getTransfer(Long accountId, @RequestParam(defaultValue = "1") Integer current,
                                @RequestParam(defaultValue = "10") Integer pageSize) {
        LOGGER.info("AdminCfOrderHandleController getTransfer userId:{}, current:{}, pageSize:{}", accountId, current, pageSize);
        if (accountId == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<CrowdfundingOrder> crowdfundingOrders = adminCfPropertyTransferHistoryBiz.selectByBizType(accountId, 1, current, pageSize);
        List<Long> userIdList = crowdfundingOrders.stream().map(CrowdfundingOrder::getUserId).distinct().collect(Collectors.toList());
        Map<String, Object> result = Maps.newHashMap();
        result.put("list", crowdfundingOrders);
        result.put("pagination", PageUtil.transform2PageMap(crowdfundingOrders));
        result.put("toUserIds", userIdList);
        return NewResponseUtil.makeSuccess(result);
    }
}
