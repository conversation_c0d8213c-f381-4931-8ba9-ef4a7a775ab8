package com.shuidihuzhu.cf.admin.mq.record;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.biz.crowdfunding.CfQuestionnaireBiz;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @DATE 2019/12/12
 */
@Service
@RocketMQListener(id = "QS_ANSWER_ADMIN",
        group = "cf-admin-QS_ANSWER",
        tags = "QS_ANSWER",
        topic = "SD_DATA_COMMON_TOPIC")
@Slf4j
public class SdDataSubmitConsumer implements MessageListener<JSONObject> {

    @Autowired
    private CfQuestionnaireBiz questionnaireBiz;


    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<JSONObject> mqMessage) {

        log.info("SdDataSubmitConsumer msg={}", mqMessage);

        JSONObject model = mqMessage.getPayload();
        if (model == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        Object questionnaireName = model.get("questionnaireName");
        Object startAnsweringTime = model.get("startTime");
        Object endTime = model.get("endTime");

        JSONObject ext = model.getJSONObject("ext");

        if (ext == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        long questionnaireId = Optional.ofNullable(ext.getLong(CfQuestionnaireBiz.ext_questionnaireId)).orElse(0L);

        if (questionnaireId == 0) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        JSONArray array = model.getJSONArray("quAnswerIdDOList");

        //更新问题答案 状态
        questionnaireBiz.updateContentByUserId(questionnaireId, array.toJSONString(), CfQuestionnaireBiz.submit_status, questionnaireName.toString(), startAnsweringTime.toString(), endTime.toString());

        return ConsumeStatus.CONSUME_SUCCESS;

    }
}
