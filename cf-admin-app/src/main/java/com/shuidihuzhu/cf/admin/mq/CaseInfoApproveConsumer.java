package com.shuidihuzhu.cf.admin.mq;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfClewChannelInfoBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.crowdfunding.CfCailiaoService;
import com.shuidihuzhu.cf.service.workorder.cailiao.CaiAuditRejectAICallService;
import com.shuidihuzhu.client.cf.workorder.CfTwWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.TwHandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.constant.CfClientMQTopicCons;
import com.shuidihuzhu.client.model.event.InfoApproveEvent;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import com.shuidihuzhu.data.analytics.javasdk.core.UserTagTypeEnum;
import com.shuidihuzhu.data.servicelog.meta.cf.MaterialAuditEvent;
import com.shuidihuzhu.data.servicelog.meta.cf.MaterialAuditPoint;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
@RefreshScope
@RocketMQListener(id = "case-info-approve_" + CfClientMQTagCons.INFO_APPROVE_MSG,
        tags = CfClientMQTagCons.INFO_APPROVE_MSG,
        topic = CfClientMQTopicCons.CF,
        group = "case-info-approve_" + CfClientMQTagCons.INFO_APPROVE_MSG)
public class CaseInfoApproveConsumer implements MessageListener<InfoApproveEvent> {

    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;

    @Autowired
    private CfCailiaoService cfCailiaoService;

    @Resource
    private CfClewChannelInfoBiz cfClewChannelInfoBiz;

    @Value("${zhu-dong-work-order-refuse-ids:[]}")
    private List<Integer> zhuDongWorkOrderRefuseIds;

    @Resource
    private CaiAuditRejectAICallService caiAuditRejectAICallService;

    @Resource
    private Analytics analytics;


    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<InfoApproveEvent> mqMessage) {
        log.info("CaseInfoApproveConsumer is success: {}", mqMessage);

        if(Objects.isNull(mqMessage) || Objects.isNull(mqMessage.getPayload())){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        InfoApproveEvent approveEvent = mqMessage.getPayload();

        int caseId = approveEvent.getCaseId();
        List<Integer> passIds = approveEvent.getPassIds();
        List<Integer> refuseIds = approveEvent.getRefuseIds();

        CrowdfundingInfo cfCase = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if(Objects.isNull(cfCase)){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        // 材料审核 通过/驳回 案例纬度埋点
        try {
            if (approveEvent.getStatus() == CrowdfundingStatus.CROWDFUNDING_STATED.value() || approveEvent.getStatus() == CrowdfundingStatus.APPROVE_DENIED.value()) {
                MaterialAuditPoint materialAuditPoint = new MaterialAuditPoint();
                materialAuditPoint.setCase_id(cfCase.getInfoId());
                materialAuditPoint.setMaterial_deal_type((long) approveEvent.getStatus());
                materialAuditPoint.setUser_tag(String.valueOf(cfCase.getUserId()));
                materialAuditPoint.setUser_tag_type(UserTagTypeEnum.userid);
                materialAuditPoint.setWork_order_id(approveEvent.getWorkOrderId());
                analytics.track(materialAuditPoint);
                log.info("大数据打点上报，材料审核通过/驳回事件:{}", materialAuditPoint);
            }
        } catch (Exception e) {
            log.error("大数据打点上报异常,材料审核 通过/驳回 案例纬度埋点", e);
        }

        if(CollectionUtils.isEmpty(passIds) && CollectionUtils.isEmpty(refuseIds)){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        // 是否是驳回
        boolean isRefuse = CollectionUtils.isNotEmpty(approveEvent.getRefuseIds()) && zhuDongWorkOrderRefuseIds.stream().anyMatch(refuseIds::contains);
        if (isRefuse){
            int result = cfCailiaoService.createFuwuWorkOrder(caseId,0,"", approveEvent.getFollowLabel());
            log.info("createFuwuWorkOrder caseid={}, result={}, zhuDongWorkOrderRefuseIds:{}, refuseIds:{}, followLabel:{}", caseId, result, zhuDongWorkOrderRefuseIds, refuseIds, approveEvent.getFollowLabel());
        }

        try {
            if (approveEvent.getStatus() == CrowdfundingStatus.CROWDFUNDING_STATED.value()) {
                cfClewChannelInfoBiz.sendLiaoNingMission(caseId);
            }
        } catch (Exception e) {
            log.info("CaseInfoApproveConsumer sendLiaoNingMission is error {} {}", caseId, e);
        }

        try {
            caiAuditRejectAICallService.rejectAICallFirst(approveEvent);
        } catch (Exception e) {
            log.info("CaseInfoApproveConsumer rejectAICallFirst is error {} {}", approveEvent, e);
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
