package com.shuidihuzhu.cf.admin.controller.api.amount;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.admin.util.MaskUtil;
import com.shuidihuzhu.cf.biz.amount.AmountReasonableBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.adminpure.enums.AmountReasonableTaskPlan;
import com.shuidihuzhu.cf.client.adminpure.enums.AmountReasonableTaskStatus;
import com.shuidihuzhu.cf.client.adminpure.enums.AmountReasonableTaskType;
import com.shuidihuzhu.cf.client.adminpure.model.amount.CfAmountReasonableTask;
import com.shuidihuzhu.cf.client.adminpure.model.amount.CfAmountReasonableTaskWorkOrder;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.domain.approve.ApproveControlRecordDO;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.approve.ApproveControlSourceTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.cf.vo.amount.AmountReasonableTaskMqVo;
import com.shuidihuzhu.cf.vo.amount.AmountReasonableTaskWorkOrderVo;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCoreFeignClient;
import com.shuidihuzhu.client.cf.workorder.model.HandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.QueryListResult;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.cf.workorder.read.WorkOrderReadFeignClient;
import com.shuidihuzhu.common.util.BeanUtils;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.xhtmlrenderer.layout.TextUtil;

import javax.annotation.Resource;
import javax.validation.constraints.Min;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: wangpeng
 * @Date: 2022/8/26 17:34
 * @Description:
 */
@Api(value = "款项合理性")
@RequestMapping("admin/cf/amount/reasonable/task")
@RestController
public class CfAmountReasonableTaskController {

    @Resource
    private AmountReasonableBiz amountReasonableBiz;

    @Resource
    private WorkOrderReadFeignClient workOrderReadFeignClient;

    @Resource
    private WorkOrderCoreFeignClient workOrderCoreFeignClient;

    @Resource
    private MsgClientV2Service msgClientV2Service;

    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Resource
    private Producer producer;

    @ApiOperation("根据案例获取数据")
    @PostMapping("getByCaseId")
    @RequiresPermission("amount-reasonable-task:getByCaseId")
    public Response<List<AmountReasonableTaskWorkOrderVo>> getByCaseId(@RequestParam int caseId) {
        List<CfAmountReasonableTaskWorkOrder> reasonableTaskWorkOrders = amountReasonableBiz.getByCaseId(caseId);
        if (CollectionUtils.isEmpty(reasonableTaskWorkOrders)) {
            return NewResponseUtil.makeSuccess(new ArrayList<>());
        }
        List<Long> workOrderIdList = reasonableTaskWorkOrders.stream()
                .map(CfAmountReasonableTaskWorkOrder::getWorkOrderId)
                .collect(Collectors.toList());
        Response<List<QueryListResult>> listByOrderIdListOld = workOrderReadFeignClient.getListByOrderIdListOld(workOrderIdList);
        Map<Long, QueryListResult> workOrderMap = Optional.ofNullable(listByOrderIdListOld)
                .map(Response::getData)
                .orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.toMap(QueryListResult::getWorkOrderId, Function.identity(), (x, y) -> x));
        List<AmountReasonableTaskWorkOrderVo> list = new ArrayList<>();
        for (CfAmountReasonableTaskWorkOrder cfAmountReasonableTaskWorkOrder : reasonableTaskWorkOrders) {
            AmountReasonableTaskWorkOrderVo vo = new AmountReasonableTaskWorkOrderVo();
            BeanUtils.copyProperties(cfAmountReasonableTaskWorkOrder, vo);
            QueryListResult queryListResult = workOrderMap.get(cfAmountReasonableTaskWorkOrder.getWorkOrderId());
            if (Objects.nonNull(queryListResult)) {
                vo.setOperator(queryListResult.getOperName());
                vo.setHandleResult(queryListResult.getHandleResult());
                vo.setOperatorId(queryListResult.getOperId());
                vo.setWorkOrderUpdateTime(queryListResult.getUpdateTime());
            }
            list.add(vo);
        }
        return NewResponseUtil.makeSuccess(list);
    }

    @ApiOperation("审核款项任务")
    @PostMapping("audit")
    @RequiresPermission("amount-reasonable-task:audit")
    public Response<Void> audit(@RequestParam long workOrderId,
                                @RequestParam long taskWorkOrderId,
                                @RequestParam int handleResult,
                                @RequestParam String remark,
                                @RequestParam(value = "rejectReason", required = false, defaultValue = "") String rejectReason) {

        CfAmountReasonableTaskWorkOrder reasonableTaskWorkOrder = amountReasonableBiz.getTaskWorkOrderById(taskWorkOrderId);
        if (Objects.isNull(reasonableTaskWorkOrder)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CfAmountReasonableTask reasonableTask = amountReasonableBiz.getTaskById(reasonableTaskWorkOrder.getTaskId());
        if (Objects.isNull(reasonableTask)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(reasonableTask.getCaseId());
        if (Objects.isNull(crowdfundingInfo)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        HandleOrderParam handleOrderParam = new HandleOrderParam();
        handleOrderParam.setWorkOrderId(workOrderId);
        handleOrderParam.setOrderType(WorkOrderType.fund_use_amount_reasonable.getType());
        handleOrderParam.setHandleResult(handleResult);
        handleOrderParam.setUserId(ContextUtil.getAdminLongUserId());
        Response<Void> handleWorkOrder = workOrderCoreFeignClient.handle(handleOrderParam);
        if (handleWorkOrder.notOk()) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
        }
        reasonableTask.setRejectReason(rejectReason);
        reasonableTask.setTaskStatus(handleResult);
        amountReasonableBiz.updateCfAmountReasonableTask(reasonableTask);

        reasonableTaskWorkOrder.setRejectReason(rejectReason);
        reasonableTaskWorkOrder.setTaskStatus(handleResult);
        reasonableTaskWorkOrder.setRemark(remark);
        amountReasonableBiz.updateCfAmountReasonableTaskWorkOrder(reasonableTaskWorkOrder);

        // 发送消息
        Map<Integer, String> params = Maps.newHashMap();
        params.put(1, reasonableTask.getTaskInfoId());
        params.put(2, AmountReasonableTaskType.getByCode(reasonableTask.getTaskType()).getDesc());
        params.put(3, MaskUtil.limitComment(reasonableTask.getRejectReason()));
        Map<Long, Map<Integer, String>> mapMap = Maps.newHashMap();
        mapMap.put(crowdfundingInfo.getUserId(), params);
        String wxNum = handleResult == HandleResultEnum.audit_reject.getType() ? "RBT2218" : "BDJ2132";
        msgClientV2Service.sendWxParamsMsg(wxNum, mapMap);

        if (AmountReasonableTaskPlan.getByCode(reasonableTaskWorkOrder.getTaskPlan()).getAmountReasonableTaskStatus().getCode() == AmountReasonableTaskStatus.AFTER_SUBMIT.getCode() &&
                reasonableTaskWorkOrder.getTaskAfterDays() > 0 &&
                (handleResult == HandleResultEnum.audit_pass.getType() || handleResult == HandleResultEnum.not_to_c_show.getType())) {
            // 延时消息
            AmountReasonableTaskMqVo taskMqVo = new AmountReasonableTaskMqVo();
            taskMqVo.setCaseId(reasonableTask.getCaseId());
            taskMqVo.setTaskInfoId(reasonableTask.getTaskInfoId());
            taskMqVo.setTaskType(reasonableTask.getTaskType());
            taskMqVo.setTaskWorkOrderId(taskWorkOrderId);
            int taskAfterDays = reasonableTaskWorkOrder.getTaskAfterDays();
            taskMqVo.setDelayCount(taskAfterDays, TimeUnit.DAYS);
            Message message = Message.ofDelay(MQTopicCons.CF, MQTagCons.CF_AMOUNT_REASONABLE_TASK, MQTagCons.CF_AMOUNT_REASONABLE_TASK + "_" + taskWorkOrderId, taskMqVo, taskMqVo.nextDelaySeconds());
            producer.send(message);
        }
        return NewResponseUtil.makeSuccess();
    }
}
