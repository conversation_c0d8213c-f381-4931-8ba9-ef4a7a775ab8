package com.shuidihuzhu.cf.admin.controller.api.approve;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.domain.approve.ApproveControlRecordDO;
import com.shuidihuzhu.cf.enums.approve.ApproveControlSourceTypeEnum;
import com.shuidihuzhu.cf.service.approve.ApproveControlService;
import com.shuidihuzhu.cf.vo.approve.ApproveControlRecordVO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.Min;


/**
 * <AUTHOR>
 */
@Api(value = "案例审核控制重复处理")
@RequestMapping("admin/cf/approve/control")
@RestController
public class CaseApproveControlController {

    @Autowired
    private ApproveControlService approveControlService;

    @ApiOperation("检查是否可处理")
    @PostMapping("check")
    @RequiresPermission("approve-control:check")
    public Response<Void> check(
            @RequestParam @Min(1) int caseId,
            @RequestParam(value = "userId") int operatorId,
            @RequestParam ApproveControlSourceTypeEnum sourceType,
            @RequestParam(required = false, defaultValue = "0") long workOrderId) {
        Response<ApproveControlRecordDO> r = approveControlService.touch2GetControl(caseId, operatorId, sourceType, workOrderId);
        return NewResponseUtil.makeResponse(r.getCode(), r.getMsg(), null);
    }

    @ApiOperation("获取当前控制详情")
    @PostMapping("get-control-info")
    @RequiresPermission("approve-control:get-control-info")
    public Response<ApproveControlRecordVO> getControlInfo(@RequestParam @Min(1) int caseId){
        ApproveControlRecordDO domain = approveControlService.getControlInfo(caseId);
        ApproveControlRecordVO vo = approveControlService.trans2VO(domain);
        return NewResponseUtil.makeSuccess(vo);
    }

    @ApiOperation("放弃操作")
    @PostMapping("give-up-operation")
    @RequiresPermission("approve-control:give-up-operation")
    public Response<Void> giveUpOperation(@RequestParam int controlRecordId,
                                                     @RequestParam(value = "userId") int operatorId){
        return approveControlService.giveUpOperation(controlRecordId, operatorId);
    }

    @RequiresPermission("approve-control:force-release-control")
    @ApiOperation("强制关闭")
    @PostMapping("force-release-control")
    public Response<Void> forceReleaseControl(@RequestParam int controlRecordId, @RequestParam(value = "userId") int operatorId){
        return approveControlService.forceReleaseControl(controlRecordId, operatorId);
    }

}
