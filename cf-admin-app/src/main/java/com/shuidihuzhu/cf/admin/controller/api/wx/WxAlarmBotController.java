package com.shuidihuzhu.cf.admin.controller.api.wx;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.wx.WxBotSendMsgTypeEnum;
import com.shuidihuzhu.cf.model.param.WxAlarmBotParam;
import com.shuidihuzhu.cf.service.wx.WxAlarmBotService;
import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Author: wangpeng
 * @Date: 2021/3/2 17:33
 * @Description: 可定制企业微信消息内容接口
 */
@Slf4j
@RestController
@RequestMapping(path = "/admin/cf/wx/bot")
public class WxAlarmBotController {

    @Resource
    private WxAlarmBotService wxAlarmBotService;

    @RequiresPermission("bot:send-msg")
    @ApiOperation(value = "企业微信机器人发消息")
    @PostMapping(path = "send-msg")
    public Response<Void> botSendMsg(@RequestBody WxAlarmBotParam wxAlarmBotParam) {
        if (Objects.isNull(wxAlarmBotParam)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        WxBotSendMsgTypeEnum botSendMsgTypeEnum = WxBotSendMsgTypeEnum.getByValue(wxAlarmBotParam.getCode());
        if (Objects.isNull(botSendMsgTypeEnum)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        wxAlarmBotParam.setOperatorId(ContextUtil.getAdminUserId());
        String alarmMsgContent = wxAlarmBotService.getAlarmMsgContent(wxAlarmBotParam);
        if (StringUtils.isEmpty(alarmMsgContent)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        AlarmBotService.sentText(botSendMsgTypeEnum.getBotKey(), alarmMsgContent, null, null);
        return NewResponseUtil.makeSuccess(null);
    }

}
