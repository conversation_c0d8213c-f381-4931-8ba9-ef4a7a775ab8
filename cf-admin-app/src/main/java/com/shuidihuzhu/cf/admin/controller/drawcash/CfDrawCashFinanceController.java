package com.shuidihuzhu.cf.admin.controller.drawcash;

import com.shuidihuzhu.cf.delegate.finance.IFinanceDelegate;
import com.shuidihuzhu.cf.finance.model.drawcash.CfDrawCashLimit;
import com.shuidihuzhu.cf.finance.model.vo.draw.CfAmountInfoVo;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(path="/admin/crowdfunding/draw-cash")
@Slf4j
public class CfDrawCashFinanceController {

    @Autowired
    private IFinanceDelegate financeDelegate;

    @RequiresPermission("cailiao:confirm-draw-cash-limit")
    @RequestMapping(path = "confirm-draw-cash-limit", method = RequestMethod.POST)
    Response<String> confirmDrawCashLimit(@RequestBody CfDrawCashLimit caseLimit) {

        caseLimit.setOperateId(ContextUtil.getAdminUserId());
        String result =  financeDelegate.confirmDrawCashLimit(caseLimit);

        return StringUtils.isBlank(result) ? NewResponseUtil.makeSuccess("") : NewResponseUtil.makeFail(result);
    }

    @RequiresPermission("cailiao:confirm-draw-cash-limit")
    @RequestMapping(path = "compute-cash-limit", method = RequestMethod.POST)
    Response<Integer> computeCashLimit(@RequestParam int caseId, @RequestParam int totalLimitAmount) {

        try {
            CfDrawCashLimit.CfAmountCaseLimitVo limitVo = financeDelegate.computeCashLimit(caseId, totalLimitAmount);

            return NewResponseUtil.makeSuccess(limitVo.getCurLimitAmount());
        } catch (Exception e) {
            return NewResponseUtil.makeFail(e.getMessage());
        }
    }

    @RequiresPermission("cailiao:confirm-draw-cash-limit")
    @RequestMapping(path = "get-raiser-amount", method = RequestMethod.POST)
    Response<CfAmountInfoVo> getFundraiserAmountInfo(String infoUuid) {
        return NewResponseUtil.makeSuccess(financeDelegate.getFundraiserAmountInfo(infoUuid));
    }


}
