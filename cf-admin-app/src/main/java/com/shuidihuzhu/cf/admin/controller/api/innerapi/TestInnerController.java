package com.shuidihuzhu.cf.admin.controller.api.innerapi;

import com.shuidihuzhu.cf.delegate.ai.AiUgcRiskDelegate;
import com.shuidihuzhu.cf.enums.risk.UgcTypeEnum;
import com.shuidihuzhu.cf.facade.AdminApolloCofig;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @DATE 2019/89
 */
@RestController
@RequestMapping("innerapi/cf/admin/test")
@RefreshScope
public class TestInnerController {

    @Value("${apollo_t:''}")
    private String apollo_t;

    @Autowired
    private AiUgcRiskDelegate aiUgcRiskDelegate;

    @PostMapping("apollo")
    public Response<String> getPublic(@RequestParam() String key,
                                      @RequestParam(required = false, defaultValue = "") String defaultValue) {
        String value = AdminApolloCofig.getValueFromApollo(key,defaultValue);
        return NewResponseUtil.makeSuccess(value);
    }


    @PostMapping("apollo-t")
    public Response<String> getPublic() {

        return NewResponseUtil.makeSuccess(apollo_t);
    }

    @PostMapping("aiUgcRiskCheck")
    public Response<Boolean> aiUgcRiskCheck(String content) {
        final boolean hit = aiUgcRiskDelegate.checkHit(0, UgcTypeEnum.DEFAULT, content);
        return NewResponseUtil.makeSuccess(hit);
    }
}
