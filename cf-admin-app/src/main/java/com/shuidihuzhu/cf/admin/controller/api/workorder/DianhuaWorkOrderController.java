package com.shuidihuzhu.cf.admin.controller.api.workorder;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfHospitalAuditBiz;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoStatusEnum;
import com.shuidihuzhu.cf.model.admin.CfHospitalAuditInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.workorder.CfDianhuaWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.DianhuaHandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.DianhuaWorkOrderListParam;
import com.shuidihuzhu.client.cf.workorder.model.DianhuaWorkOrderVo;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2019/8/15
 */
@RestController
@RequestMapping(path="/admin/workorder/dianhua")
@Slf4j
public class DianhuaWorkOrderController {

    @Autowired
    private CfDianhuaWorkOrderClient workOrderClient;

    @Autowired
    private CrowdfundingFeignClient feignClient;

    @Autowired
    private AdminCfHospitalAuditBiz adminCfHospitalAuditBiz;

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @RequiresPermission("dianhua:hanlde")
    @RequestMapping(path = "hanlde-dianhua",method = RequestMethod.POST)
    public Response hanldeDianhua(@RequestParam("param") String param ){

        DianhuaHandleOrderParam handleOrderParam = JSON.parseObject(param,DianhuaHandleOrderParam.class);//已检查过

        if (handleOrderParam.getCaseId() == 0){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        FeignResponse<CrowdfundingInfo> feignResponse = feignClient.getCaseInfoById(handleOrderParam.getCaseId());

        if (feignResponse == null || feignResponse.notOk() || feignResponse.getData() == null){
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }
        CrowdfundingInfo fundintInfo = feignResponse.getData();
        String caseUuid = fundintInfo.getInfoId();

        CfHospitalAuditInfoExt cfHospitalAuditInfoExt = adminCfHospitalAuditBiz.getByInfoUuid(caseUuid);

        if (cfHospitalAuditInfoExt == null){
            return NewResponseUtil.makeError(AdminErrorCode.HOSPITAL_AUDIT_HAS_AUTO_CLOSE);
        }

        boolean autoClose = checkAutoClose(fundintInfo.getId(), handleOrderParam.getWorkOrderId(), handleOrderParam.getOrderType(), fundintInfo.getInfoId());
        if (autoClose) {
            return NewResponseUtil.makeFail("工单已自动异常关闭");
        }

        if (CrowdfundingInfoStatusEnum.SUBMITTED.getCode() ==  cfHospitalAuditInfoExt.getAuditStatus()){
            return NewResponseUtil.makeResponse(AdminErrorCode.SYSTEM_NOT_PRIVILEGE.getCode(),"医院核实未处理，无法关闭工单",null);
        }

        return workOrderClient.hanldeDianhua(handleOrderParam);
    }

    @RequiresPermission("dianhua:orderlist")
    @RequestMapping(path = "dianhua-orderlist", method = RequestMethod.POST)
    public Response dianhuaOrderList(@RequestParam("param") String param) {

        DianhuaWorkOrderListParam p = JSON.parseObject(param, DianhuaWorkOrderListParam.class);//已检查过

        Response<PageResult<DianhuaWorkOrderVo>> response = workOrderClient.dianhuaOrderList(p);

        if (response == null || response.notOk()){
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }

        List<DianhuaWorkOrderVo> pageList = response.getData().getPageList();

        if (CollectionUtils.isEmpty(pageList)){
            return response;
        }

        List<String> uuids = pageList.stream().map(DianhuaWorkOrderVo::getCaseUuid).distinct().collect(Collectors.toList());
        Map<String,Boolean> map = canClose(uuids);
        pageList.stream()
                .filter(r->r.getOrderType() == WorkOrderType.heshi.getType())
                .filter(r-> r.getHandleResult() == HandleResultEnum.doing.getType() || r.getHandleResult() == HandleResultEnum.later_doing.getType())
                .forEach(r->r.setCanClose(map.get(r.getCaseUuid())==null?false:map.get(r.getCaseUuid())));

        return response;
    }


    private Map<String,Boolean> canClose(List<String> uuids){

        Map<String,Boolean> result = Maps.newHashMap();

        List<CfHospitalAuditInfoExt> list = adminCfHospitalAuditBiz.getByInfoUuids(uuids);

        if (CollectionUtils.isEmpty(list)){
            return result;
        }

        Map<String,List<CfHospitalAuditInfoExt>> map = list.stream().collect(Collectors.groupingBy(CfHospitalAuditInfoExt::getInfoUuid));
        //取最新一条的状态判断
        map.keySet().stream().filter(r->map.get(r) != null).forEach(r->{
            Optional<CfHospitalAuditInfoExt> o =  map.get(r).stream().sorted(Comparator.comparing(CfHospitalAuditInfoExt::getId).reversed()).findFirst();
            if (o.isPresent()){
                result.put(r,o.get().getAuditStatus() != CrowdfundingInfoStatusEnum.SUBMITTED.getCode());
            }
        });

        return result;
    }

    @RequiresPermission("dianhua:check")
    @RequestMapping(path = "dianhua-check", method = RequestMethod.POST)
    public Response dianhuaCheck(@RequestParam("caseId") int caseId,
                                 @RequestParam("workOrderId") long workOrderId,
                                 @RequestParam("orderType") int orderType,
                                 @RequestParam("caseUuid") String caseUuid) {

        return NewResponseUtil.makeSuccess(checkAutoClose(caseId, workOrderId, orderType, caseUuid));
    }

    @NotNull
    private boolean checkAutoClose(int caseId, long workOrderId, int orderType, String caseUuid) {
        CfHospitalAuditInfoExt cfHospitalAuditInfoExt = adminCfHospitalAuditBiz.getByInfoUuid(caseUuid);

        if (cfHospitalAuditInfoExt == null){
            hanlde(workOrderId,orderType);
            return true;
        }

//        if (orderType == WorkOrderType.heshi.getType()
//                && CrowdfundingInfoStatusEnum.SUBMITTED.getCode() !=  cfHospitalAuditInfoExt.getAuditStatus()){
//            hanlde(workOrderId,orderType);
//            return NewResponseUtil.makeSuccess(true);
//        }

        if (orderType != WorkOrderType.genjin.getType()){
            return false;
        }

        Response<WorkOrderVO> response = cfWorkOrderClient.getLastWorkOrder(caseId, WorkOrderType.genjin.getType());
        boolean hasNewerGenJinCreated = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .map(WorkOrderVO::getWorkOrderId)
                .filter(v -> v != workOrderId)
                .isPresent();
        if (hasNewerGenJinCreated) {
            hanlde(workOrderId,orderType);
            return true;
        }

        if ((CrowdfundingInfoStatusEnum.SUBMITTED.getCode() ==  cfHospitalAuditInfoExt.getAuditStatus()
                ||CrowdfundingInfoStatusEnum.PASSED.getCode() ==  cfHospitalAuditInfoExt.getAuditStatus())){
            hanlde(workOrderId,orderType);
            return true;
        }

        return false;
    }


    private void hanlde(long workOrderId,int orderType){

        DianhuaHandleOrderParam p = new DianhuaHandleOrderParam();
        p.setWorkOrderId(workOrderId);
        p.setOrderType(orderType);
        p.setHandleResult(HandleResultEnum.exception_done.getType());

        workOrderClient.hanldeDianhua(p);

    }
}
