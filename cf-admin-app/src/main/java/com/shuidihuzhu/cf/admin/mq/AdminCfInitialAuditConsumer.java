package com.shuidihuzhu.cf.admin.mq;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfClewChannelInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfLabelMarkBiz;
import com.shuidihuzhu.cf.biz.risk.IDishonestService;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.UserLabelAlterEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cf.model.risk.Dishonest;
import com.shuidihuzhu.cf.mq.producer.impl.MQProdecer;
import com.shuidihuzhu.cf.risk.client.admin.hit.RiskStrategyHitClient;
import com.shuidihuzhu.cf.risk.client.risk.BrokenPromisesClientV2;
import com.shuidihuzhu.cf.risk.model.admin.hit.StrategyCallResultDto;
import com.shuidihuzhu.cf.risk.model.admin.hit.StrategyHitDto;
import com.shuidihuzhu.cf.risk.model.enums.hit.RiskStrategyCallResultEnum;
import com.shuidihuzhu.cf.risk.model.enums.hit.RiskStrategyEnum;
import com.shuidihuzhu.cf.risk.model.enums.hit.RiskStrategySecondEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistCallPhaseEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistVerifyRoleEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistVerifyTypeEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.IdentifySpotInitiatorHitEnum;
import com.shuidihuzhu.cf.risk.model.risk.BlacklistVerifyDto;
import com.shuidihuzhu.cf.service.CfCaseWorkOrderService;
import com.shuidihuzhu.cf.service.tog.GuangzhouService;
import com.shuidihuzhu.cf.service.workorder.WorkOrderCreditService;
import com.shuidihuzhu.cf.service.workorder.cailiao.ZhuDongFuWuWorkOrderService;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@RocketMQListener(id = com.shuidihuzhu.cf.constants.MQTagCons.CF_INITIAL_AUDIT_OPERATION_MSG,
        group = "cf-admin" + com.shuidihuzhu.cf.constants.MQTagCons.CF_INITIAL_AUDIT_OPERATION_MSG,
        tags = com.shuidihuzhu.cf.constants.MQTagCons.CF_INITIAL_AUDIT_OPERATION_MSG,
        topic = MQTopicCons.CF)
@Slf4j
public class AdminCfInitialAuditConsumer implements MessageListener<InitialAuditItem.InitialAuditOperation>  {

    @Autowired
    private MQProdecer mqProdecer;
    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private AdminCfInfoExtBiz cfInfoExtBiz;
    @Autowired
    private CfCaseWorkOrderService caseWorkOrderService;
    @Autowired
    private WorkOrderCreditService creditService;

    @Autowired
    private ZhuDongFuWuWorkOrderService zhuDongFuWuWorkOrderService;

    @Autowired
    private CfLabelMarkBiz cfLabelMarkBiz;

    @Autowired
    private ShuidiCipher shuidiCipher;

    @Resource
    private BrokenPromisesClientV2 brokenPromisesClientV2;

    @Autowired
    private IDishonestService dishonestService;

    @Autowired
    private RiskStrategyHitClient riskStrategyHitClient;

    @Autowired
    private IRiskDelegate riskDelegate;

    @Resource
    private CfClewChannelInfoBiz cfClewChannelInfoBiz;

    @Resource
    private GuangzhouService guangzhouService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<InitialAuditItem.InitialAuditOperation> mqMessage) {

        log.info("receive CF_INITIAL_AUDIT_OPERATION_MSG message: {}", mqMessage);
        if (mqMessage == null || mqMessage.getPayload() == null) {
            log.error("CF_INITIAL_AUDIT_OPERATION_MSG消息体为空");
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        int caseId = mqMessage.getPayload().getCaseId();
        CrowdfundingInfo cfInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);

        CfInfoExt cfInfoExt = cfInfoExtBiz.selectByInfoUuidFromMaster(cfInfo.getInfoId());
        boolean casePass = (cfInfoExt != null && FirstApproveStatusEnum.isPassed(FirstApproveStatusEnum.parse(cfInfoExt.getFirstApproveStatus()))) ;

        //审核通过或者驳回，更新用户的标签信息
        mqProdecer.sendUserLabelAlterMq(cfInfo.getId(), cfInfo.getUserId(), casePass ? UserLabelAlterEnum.FIRST_PPROVE : UserLabelAlterEnum.FIRST_REJECT, DelayLevel.S1);

        if (casePass) {
            try {
                zhuDongFuWuWorkOrderService.onInitialApprovePassed(cfInfo);
            } catch (Exception e) {
                log.error("onInitialApprovePassed error", e);
            }

        }

        // 创建首次工单
        createFirstWorkOrder(cfInfo, casePass);

        // 创建增信工单
        try {
            creditService.create(caseId);
        } catch (Exception e) {
            log.error("创建增信工单失败,caseId:{}", caseId);
            return ConsumeStatus.RECONSUME_LATER;
        }

        //标记qq高潜标签
        if (casePass){
            try {
                cfLabelMarkBiz.addQQLabel(cfInfo);
            }catch (Exception e){
                log.error("qq高潜标签失败,caseId:{}", caseId);
            }
        }

        if (casePass){
            try {
                //患者为失信被执行人识别处理
                handleDishonest(cfInfo,caseId);
            }catch (Exception e){
                log.error("患者为失信被执行人识别处理失败,caseId:{}", caseId);
            }
        }

        if (casePass){
            try {
                //toG项目打标
                cfClewChannelInfoBiz.sendLiaoNingMission(caseId);
            } catch (Exception e){
                log.error("AdminCfInitialAuditConsumer sendLiaoNingMission is error,caseId:{} {}", caseId, e);
            }
        }
        // 广州打标(是否通过都打标)
        try {
            guangzhouService.addOrUpdateGuangzhouLabel(caseId);
        } catch (Exception e) {
            log.info("AdminCfInitialAuditConsumer saveGuangzhouLabel is error {} {}", caseId, e);
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }


    private void createFirstWorkOrder(CrowdfundingInfo cfInfo, boolean casePass) {
        if (cfInfo.getEndTime() != null && cfInfo.getEndTime().before(new Date())) {
            log.info("案例id:{} 不生成首次工单 案例已结束 endtime:{}", cfInfo.getId(), cfInfo.getEndTime());
            return;
        }

        if (!casePass) {
            log.info("案例id:{} 案例不是初审审核通过状态", cfInfo.getId());
            return;
        }
        caseWorkOrderService.addWorkOrder(cfInfo.getId());
    }

    private void handleDishonest(CrowdfundingInfo cfInfo, int caseId) {
        log.info("begin handleDishonest caseId:{}", caseId);
        CfFirsApproveMaterial material = riskDelegate.getCfFirsApproveMaterialByInfoId(cfInfo.getId());
        Date createTime = cfInfo.getCreateTime();
        String username = material.getPatientRealName();
        String idCard = shuidiCipher.decrypt(material.getPatientCryptoIdcard());
        if (StringUtils.isEmpty(idCard)){
            idCard = material.getPatientBornCard();
        }
        log.info("失信被执行人username:{},idcard:{}", username, shuidiCipher.encrypt(idCard));
        long userId = cfInfo.getUserId();
        Response<Boolean> response = brokenPromisesClientV2.checkInfo(username, idCard);
        if(response == null || response.notOk() || response.getData() == null){
            log.error("远程调用查看是否是被失信人接口失败");
            return;
        }
        Boolean isDishonest = response.getData();
        if(!isDishonest){
           return;
        }
        // 插入风险命中结果
        StrategyCallResultDto callResultDto = new StrategyCallResultDto();
        callResultDto.setCaseId(caseId);
        callResultDto.setRiskStrategy(RiskStrategyEnum.DISHONEST_PEOPLE_DISCERN.getCode());
        callResultDto.setSecondStrategy(RiskStrategySecondEnum.PATIENT_DISHONEST_DISCERN.getCode());
        callResultDto.setCallResult(RiskStrategyCallResultEnum.PATIENT_CALL_RESULT.getCode());
        callResultDto.setCallTime(DateUtil.getCurrentDate());
        callResultDto.setUserName(username);
        callResultDto.setUserIdCard(shuidiCipher.encrypt(idCard));
        riskStrategyHitClient.saveCallResult(callResultDto);

        // 插入患者为失信人记录
        Dishonest dishonest=new Dishonest();
        dishonest.setUserId(userId);
        dishonest.setUsername(username);
        dishonest.setUserType(0);
        dishonest.setUserIdCard(shuidiCipher.encrypt(idCard));
        dishonest.setDishonest(1);
        dishonest.setCaseId(caseId);
        dishonestService.insert(dishonest);

        //插入风控引擎策略
        StrategyHitDto strategyHitDto = new StrategyHitDto();
        strategyHitDto.setCaseId(caseId);
        strategyHitDto.setLaunchTime(createTime);
        strategyHitDto.setHitPhase(BlacklistCallPhaseEnum.SUBMIT_PRE_TRIAL.getCode());
        strategyHitDto.setRiskStrategy(RiskStrategyEnum.DISHONEST_PEOPLE_DISCERN.getCode());
        strategyHitDto.setSecondStrategy(RiskStrategySecondEnum.PATIENT_DISHONEST_DISCERN.getCode());
        strategyHitDto.setRiskType(IdentifySpotInitiatorHitEnum.FORTY.getCode());

        List<BlacklistVerifyDto> list = new ArrayList<>();
        BlacklistVerifyDto userBlack = new BlacklistVerifyDto(BlacklistVerifyRoleEnum.PATIENT.getCode(), BlacklistVerifyTypeEnum.USER.getCode(),username);
        userBlack.setHit(true);
        userBlack.setLimitActionIds(Lists.newArrayList());
        userBlack.setTypeIds(Lists.newArrayList());

        BlacklistVerifyDto paientBlack = new BlacklistVerifyDto(BlacklistVerifyRoleEnum.PATIENT.getCode(),BlacklistVerifyTypeEnum.ID_CARD.getCode(),shuidiCipher.encrypt(idCard));
        paientBlack.setHit(true);
        paientBlack.setLimitActionIds(Lists.newArrayList());
        paientBlack.setTypeIds(Lists.newArrayList());
        list.add(userBlack);
        list.add(paientBlack);
        String hitInfo = JSON.toJSONString(list);
        strategyHitDto.setHitInfo(hitInfo);

        riskStrategyHitClient.saveHitRecord(strategyHitDto);
    }

}
