package com.shuidihuzhu.cf.admin.mq;

import com.shuidihuzhu.cf.admin.river.impl.RiverDiBaoFacadeImpl;
import com.shuidihuzhu.cf.admin.river.impl.RiverPinKunFacadeImpl;
import com.shuidihuzhu.cf.biz.crowdfunding.CfClewChannelInfoBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.UserOperationStatConstant;
import com.shuidihuzhu.cf.service.tog.GuangzhouService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditCreateOrder;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @DATE 2019/5/21
 * 初审材料重新提交  生成初审工单
 */
@Service
@RocketMQListener(id =MQTagCons.CF_SUBMIT_INITIAL_AUDIT ,
        group = "cf-admin-initialAudit-group",
        tags = MQTagCons.CF_SUBMIT_INITIAL_AUDIT,
        topic = MQTopicCons.CF)
@Slf4j
public class CfSubmitInitialAuditConsumer implements MessageListener<Integer> {

    @Resource
    private MeterRegistry meterRegistry;

    @Autowired
    private InitialAuditCreateOrder initialAuditCreateOrder;

    @Autowired
    private RiverPinKunFacadeImpl riverPinKunFacade;

    @Autowired
    private RiverDiBaoFacadeImpl riverDiBaoFacade;

    @Resource
    private CfClewChannelInfoBiz cfClewChannelInfoBiz;

    @Resource
    private GuangzhouService guangzhouService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<Integer> mqMessage) {

        log.info("CfSubmitInitialAuditConsumer message:{}",mqMessage);

        if(mqMessage == null || mqMessage.getPayload() == null){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        //上报一次打点信息
        meterRegistry.counter(UserOperationStatConstant.USER_OPERATING_STAT,
                UserOperationStatConstant.OPERATION, UserOperationStatConstant.INITIAL_AUDIT).increment();


        int caseId = mqMessage.getPayload();

        initialAuditCreateOrder.createChuci(caseId,0, true);

        riverDiBaoFacade.onSubmit(caseId);
        riverPinKunFacade.onSubmit(caseId);

        try {
            cfClewChannelInfoBiz.sendLiaoNingMission(caseId);
        } catch (Exception e) {
            log.info("CfSubmitInitialAuditConsumer sendLiaoNingMission is error {} {}", caseId, e);
        }

//        // 广州打标
//        try {
//            guangzhouService.addOrUpdateGuangzhouLabel(caseId);
//        } catch (Exception e) {
//            log.info("CfSubmitInitialAuditConsumer saveGuangzhouLabel is error {} {}", caseId, e);
//        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
