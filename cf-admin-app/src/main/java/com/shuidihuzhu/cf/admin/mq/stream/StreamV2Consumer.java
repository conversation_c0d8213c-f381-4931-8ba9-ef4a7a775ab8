package com.shuidihuzhu.cf.admin.mq.stream;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.shuidihuzhu.cf.admin.mq.common.BaseMessageConsumer;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.model.broadcasting.PushBroadcastingV2;
import com.shuidihuzhu.cf.service.stream.StreamActionConst;
import com.shuidihuzhu.cf.service.stream.manager.StreamDataManageService;
import com.shuidihuzhu.cf.service.stream.manager.StreamStringManageService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 *
 */
@Service
@Slf4j
@RocketMQListener(id = MQTagCons.COMMON_STREAM_V2,
        tags = MQTagCons.COMMON_STREAM_V2,
        topic = MQTopicCons.CF,
        group = "cf-admin-" + MQTagCons.COMMON_STREAM_V2 + "-group")
public class StreamV2Consumer extends BaseMessageConsumer<String> implements MessageListener<String> {

    @Autowired
    private StreamDataManageService streamDataManageService;

    @Autowired
    private StreamStringManageService streamStringManageService;

    @Override
    protected boolean handle(ConsumerMessage<String> consumerMessage) {
        log.info("StreamV2Consumer consumerMessage:{}", consumerMessage);

        if (Objects.isNull(consumerMessage) || Objects.isNull(consumerMessage.getPayload())) {
            return true;
        }

        String jsonString = consumerMessage.getPayload();
        PushBroadcastingV2 pushBroadcasting = JSON.parseObject(jsonString, PushBroadcastingV2.class);
        String key = pushBroadcasting.getKey();
        String value = pushBroadcasting.getValue();
        String subject = pushBroadcasting.getSubject();

        if (StringUtils.equals(StreamActionConst.Subject.operator_group_assign, subject)) {
            streamDataManageService.pushSingle(subject, key, JSON.parseObject(value, new TypeReference<>() {
            }));
        } else {
            streamStringManageService.pushSingle(subject, key, JSON.parseObject(value, new TypeReference<>() {
            }));
        }
        return true;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
