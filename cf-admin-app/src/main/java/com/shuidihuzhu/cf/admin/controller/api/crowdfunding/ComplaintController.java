package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.dto.ComplaintVerifyDTO;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.service.crowdfunding.complaint.ComplaintService;
import com.shuidihuzhu.common.web.annotation.SessionKeyValidateRequired;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(path = "admin/cf/complaint")
@Api(value = "投诉相关API")
@Slf4j
public class ComplaintController {

    @Autowired
    private ComplaintService complaintService;

    @RequiresPermission("ugc_complaint:ugc_complaint_verify")
    @ApiOperation("审核投诉工单")
    @RequestMapping(path = "verify",method=RequestMethod.POST)
    public Response verify(ComplaintVerifyDTO complaintVerifyDTO) {

        log.info("ComplaintController verify requestParam: {}", complaintVerifyDTO);

        if (StringUtils.isBlank(complaintVerifyDTO.getUgcWorkOrderId())) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_IS_NULL);
        }

        int userId = ContextUtil.getAdminUserId();

        return NewResponseUtil.makeSuccess(complaintService.verify(complaintVerifyDTO, userId));

    }
}
