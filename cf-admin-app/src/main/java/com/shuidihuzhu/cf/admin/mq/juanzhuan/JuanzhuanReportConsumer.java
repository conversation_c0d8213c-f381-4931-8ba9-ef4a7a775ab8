package com.shuidihuzhu.cf.admin.mq.juanzhuan;

import com.shuidihuzhu.cf.admin.mq.common.BaseMessageConsumer;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.workorder.juanzhan.JuanzhuanWorkOrderService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @DATE 2020/5/8
 */
@Service
@Slf4j
@RocketMQListener(id = "juanzhuan_"+ MQTagCons.CF_WORK_ORDER_REPORT_NEW_LOGIC,
        tags = MQTagCons.CF_WORK_ORDER_REPORT_NEW_LOGIC,
        group = "juanzhuang_report_group",
        topic = MQTopicCons.CF)
public class JuanzhuanReportConsumer extends BaseMessageConsumer<Map<String, String>>
        implements MessageListener<Map<String, String>> {

    @Autowired
    private JuanzhuanWorkOrderService juanzhuanWorkOrderService;

    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Override
    protected boolean handle(ConsumerMessage<Map<String, String>> consumerMessage) {

        Map<String, String> reportMap = consumerMessage.getPayload();
        String infoUuid = reportMap.get("infoUuid");
        CrowdfundingInfo cfInfo = crowdfundingDelegate.getCrowdfundingInfoByInfoId(infoUuid);
        int caseId = Optional.ofNullable(cfInfo).map(CrowdfundingInfo::getId).orElse(0);
        juanzhuanWorkOrderService.report4Close(caseId);

        return true;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
