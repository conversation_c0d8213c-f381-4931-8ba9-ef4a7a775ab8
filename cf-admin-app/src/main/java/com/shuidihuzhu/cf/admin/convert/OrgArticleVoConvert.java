package com.shuidihuzhu.cf.admin.convert;

import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.protobuf.util.Timestamps;
import com.shuidihuzhu.cf.vo.frame.OrgArticleVo;
import com.shuidihuzhu.cf.vo.frame.OrgTagVo;
import com.shuidihuzhu.frame.client.model.mina.OrgArticleProto;
import com.shuidihuzhu.frame.client.model.mina.OrgTagProto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * Created by dongcf on 2018/5/4
 */
public final class OrgArticleVoConvert implements IConvertService<OrgArticleVo, OrgArticleProto> {

    private static final long MILLIS_PER_SECOND = 1000;

    private OrgArticleVoConvert() {
        //禁止实例化
    }

    private static OrgArticleVoConvert instance = new OrgArticleVoConvert();

    public static OrgArticleVoConvert getInstance() {
        return instance;
    }

    @Override
    public OrgArticleVo convertForPB(OrgArticleProto orgArticleProto) {
        Preconditions.checkNotNull(orgArticleProto);
        OrgArticleVo orgArticle = new OrgArticleVo();
        orgArticle.setId(orgArticleProto.getId());
        orgArticle.setTime(new Date(orgArticleProto.getTime().getSeconds() * MILLIS_PER_SECOND));
        orgArticle.setContentUrl(orgArticleProto.getContentUrl());
        orgArticle.setAccount(orgArticleProto.getAccount());
        orgArticle.setAuthor(orgArticleProto.getAuthor());
        orgArticle.setBody(orgArticleProto.getBody());
        orgArticle.setCoverUrl(orgArticleProto.getCoverUrl());
        orgArticle.setCreateTime(new Date(orgArticleProto.getCreateTime().getSeconds() * MILLIS_PER_SECOND));
        orgArticle.setUpdateTime(new Date(orgArticleProto.getUpdateTime().getSeconds() * MILLIS_PER_SECOND));
        orgArticle.setHasCover(orgArticleProto.getHasCover());
        orgArticle.setIsOrigin(orgArticleProto.getIsOrigin());
        orgArticle.setPageUrl(orgArticleProto.getPageUrl());
        orgArticle.setPlatform(orgArticleProto.getPlatform());
        orgArticle.setSummary(orgArticleProto.getSummary());
        orgArticle.setTitle(orgArticleProto.getTitle());
        orgArticle.setUuid(orgArticleProto.getUuid());
        orgArticle.setValid(orgArticleProto.getValid());
        orgArticle.setBaseImgUrl(orgArticleProto.getBaseImgUrl());
        orgArticle.setActionType(orgArticleProto.getActionType());
        orgArticle.setActionUrl(orgArticleProto.getActionUrl());
        orgArticle.setAppId(orgArticleProto.getAppId());
        List<OrgTagVo> orgTagList = Lists.newArrayList();
        List<OrgTagProto> orgTagProtoList = orgArticleProto.getOrgTagsList();
        if (CollectionUtils.isNotEmpty(orgTagProtoList)) {
            for (OrgTagProto proto : orgTagProtoList) {
                OrgTagVo orgTag = new OrgTagVo();
                orgTag.setId(proto.getId());
                orgTag.setTag(proto.getTag());
                orgTag.setCreateTime(new Date(proto.getCreateTime().getSeconds() * MILLIS_PER_SECOND));
                orgTag.setUpdateTime(new Date(proto.getUpdateTime().getSeconds() * MILLIS_PER_SECOND));
                orgTag.setValid(proto.getValid());
                orgTagList.add(orgTag);
            }
        }
        orgArticle.setTags(orgTagList);
        return orgArticle;
    }

    @Override
    public OrgArticleProto convertToPB(OrgArticleVo orgArticle) {
        Preconditions.checkNotNull(orgArticle);
        OrgArticleProto.Builder builder = OrgArticleProto.newBuilder();
        if (CollectionUtils.isNotEmpty(orgArticle.getTags())) {
            for (OrgTagVo orgTag : orgArticle.getTags()) {
                OrgTagProto proto = OrgTagProto.newBuilder()
                        .setId(orgTag.getId())
                        .setTag(orgTag.getTag())
                        .setCreateTime(Timestamps.fromMillis(orgTag.getCreateTime().getTime()))
                        .setUpdateTime(Timestamps.fromMillis(orgTag.getUpdateTime().getTime()))
                        .setValid(orgTag.getValid()).build();
                builder.addOrgTags(proto);
            }
        }
        builder.setId(orgArticle.getId())
                .setAuthor(orgArticle.getAuthor())
                .setBody(orgArticle.getBody())
                .setPageUrl(orgArticle.getPageUrl())
                .setTitle(orgArticle.getTitle())
                .setBaseImgUrl(orgArticle.getBaseImgUrl() == null ? "" : orgArticle.getBaseImgUrl())
                .setActionType(orgArticle.getActionType() == null ? 0 : orgArticle.getActionType())
                .setActionUrl(orgArticle.getActionUrl() == null ? "" : orgArticle.getActionUrl())
                .setAppId(orgArticle.getAppId() == null ? "" : orgArticle.getAppId());
        if (StringUtils.isNotBlank(orgArticle.getCoverUrl())){
                builder.setCoverUrl(orgArticle.getCoverUrl())
                        .setHasCover(true);
        }else {
            builder.setCoverUrl("")
                    .setHasCover(false);
        }
        if (orgArticle.getId() <= 0) {
            builder.setTime(Timestamps.fromMillis(new Date().getTime()))
                    .setContentUrl("")
                    .setValid(1)
                    .setUuid("")
                    .setAccount("")
                    .setSummary("")
                    .setIsOrigin(1)
                    .setTime(Timestamps.fromMillis(new Date().getTime()))
                    .setCreateTime(Timestamps.fromMillis(new Date().getTime()))
                    .setUpdateTime(Timestamps.fromMillis(new Date().getTime()))
                    .setPlatform(0);

        } else {
            builder.setTime(Timestamps.fromMillis(orgArticle.getTime().getTime()))
                    .setContentUrl(orgArticle.getContentUrl()).setValid(orgArticle.getValid())
                    .setUuid(orgArticle.getUuid())
                    .setAccount(orgArticle.getAccount())
                    .setSummary(orgArticle.getSummary())
                    .setIsOrigin(orgArticle.getIsOrigin())
                    .setCreateTime(Timestamps.fromMillis(orgArticle.getCreateTime().getTime()))
                    .setUpdateTime(Timestamps.fromMillis(orgArticle.getUpdateTime().getTime()))
                    .setPlatform(orgArticle.getPlatform());
        }
        return builder.build();
    }
}
