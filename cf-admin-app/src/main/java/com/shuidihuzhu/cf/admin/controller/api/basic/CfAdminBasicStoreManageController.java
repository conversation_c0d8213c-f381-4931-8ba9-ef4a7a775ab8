package com.shuidihuzhu.cf.admin.controller.api.basic;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.frame.client.api.basic.BasicStoreManageClient;
import com.shuidihuzhu.frame.client.model.basic.BasicStoreParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
@Api("公共存储管理")
@RequestMapping(value = "admin/cf/basic/store", method = {RequestMethod.POST})
public class CfAdminBasicStoreManageController {

    @Resource
    private BasicStoreManageClient basicStoreClient;

    @ApiOperation("插入activity的配置信息")
    @RequestMapping(value = "insert", method = RequestMethod.POST)
    public Response insert(@ApiParam("活动id") @RequestParam("activity_id") long activityId,
                           @ApiParam("需要存储的内容，必须要是json格式的") @RequestParam("content") String content,
                           @ApiParam("操作的adminUserId") @RequestParam int userId) {
        Response response = this.checkJson(content);
        if (Objects.nonNull(response)){
            return response;
        }
        return basicStoreClient.insert(activityId,"", content, userId);
    }

    @ApiOperation("修改activity的配置信息")
    @RequestMapping(value = "update", method = RequestMethod.POST)
    public Response getInfoByActivityId(@ApiParam("活动id") @RequestParam("activity_id") long activityId,
                                        @ApiParam("新的内容，必须要是json格式的") @RequestParam("content") String content,
                                        @ApiParam("之前通过get拿到的版本号，必须要匹配当前库中的版本号，否则不成功") @RequestParam("old_version") long oldVersion,
                                        @ApiParam("新的版本号，更新成功后库中数据为当前版本号，建议取now()") @RequestParam("new_version") long newVersion,
                                        @ApiParam("操作的adminUserId") @RequestParam int userId) {
        Response response = this.checkJson(content);
        if (Objects.nonNull(response)){
            return response;
        }
        BasicStoreParam basicStoreParam = new BasicStoreParam();
        basicStoreParam.setActivityId(activityId);
        basicStoreParam.setContent(content);
        basicStoreParam.setOldVersion(oldVersion);
        basicStoreParam.setNewVersion(newVersion);
        basicStoreParam.setUserId(userId);
        return basicStoreClient.updateV2(basicStoreParam);
    }


    public Response checkJson(String content) {
        if (StringUtils.isEmpty(content)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_JSON_PARSE_ERROR);
        }
        try {
            JSON.parseObject(content);//已检查过
        } catch (Exception e) {
            log.error("CfAdminBasicStoreManageController parse json error", e);
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_JSON_PARSE_ERROR);
        }
        return null;
    }

}
