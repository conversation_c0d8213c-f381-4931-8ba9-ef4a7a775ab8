package com.shuidihuzhu.cf.admin.controller.api.innerapi;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingCommentBiz;
import com.shuidihuzhu.client.cf.admin.client.CfAdminUgcServiceFeignClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.sql.Timestamp;

@RestController
@Slf4j
public class CfAdminUgcServiceFeignClientController implements CfAdminUgcServiceFeignClient {

    @Autowired
    private AdminCrowdfundingCommentBiz adminCrowdfundingCommentBiz;

    @Override
    public Response<Integer> selectCommentCountByMin(Timestamp begin, Timestamp end) {
        return NewResponseUtil.makeSuccess(adminCrowdfundingCommentBiz.selectCountByMin(begin, end));
    }
}
