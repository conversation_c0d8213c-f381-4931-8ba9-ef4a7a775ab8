package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.admin.TargetAmountAuditRecordBiz;
import com.shuidihuzhu.cf.biz.admin.UserCommentBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.adminpure.enums.WorkOrderExtContentTypeEnum;
import com.shuidihuzhu.cf.client.adminpure.model.rule.EconomyModel;
import com.shuidihuzhu.cf.client.material.model.authenticity.enumModel.EconomicSituationEnum;
import com.shuidihuzhu.cf.client.material.model.authenticity.enumModel.PatientMarriedEnum;
import com.shuidihuzhu.cf.enums.admin.UserCommentSourceEnum;
import com.shuidihuzhu.cf.model.admin.UserComment;
import com.shuidihuzhu.cf.model.record.TargetAmountAuditRecord;
import com.shuidihuzhu.cf.service.risk.highrisk.HighRiskService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditBrainService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditSearchService;
import com.shuidihuzhu.cf.vo.admin.initialAudit.DiseaseStrategyResult;
import com.shuidihuzhu.cf.vo.disease.SmallToolCostVo;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.saas.AdminOrganization;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.OrganizationClientV1;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRiskRuleBiz;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.material.feign.CfMaterialReadClient;
import com.shuidihuzhu.cf.client.material.model.CfPropertyInsuranceInfoModel;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.client.material.model.PropertyInsuranceCaseType;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.delegate.PreposeMaterialDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.rule.RiskRuleResult;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.workorder.WorkOrderExtService;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCoreFeignClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2020/1/2
 */
@RestController
@RequestMapping(path = "/admin/cf/rule")
@Slf4j
@RefreshScope
public class CfRiskRuleController {

    @Autowired
    private CfRiskRuleBiz cfRiskRuleBiz;

    @Autowired
    private WorkOrderExtService workOrderExtService;

    @Autowired
    private SeaAccountClientV1 v1;

    @Autowired
    private CfMaterialReadClient materialReadClient;

    @Autowired
    private CrowdfundingFeignClient client;

    @Autowired
    private PreposeMaterialDelegate preposeMaterialDelegate;

    @Autowired
    private OrganizationClientV1 orgClientV1;

    @Autowired
    private HighRiskService highRiskService;
    @Resource
    private InitialAuditBrainService initialAuditBrainService;
    @Resource
    private CfWorkOrderClient cfWorkOrderClient;
    @Resource
    private UserCommentBiz userCommentBiz;
    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Resource
    private TargetAmountAuditRecordBiz targetAmountAuditRecordBiz;

    @Value("${apollo.admin.high-risk.v2.enable:true}")
    private boolean highRiskV2Enable;

    @RequiresPermission("rule:judge")
    @RequestMapping(path = "/judge", method = RequestMethod.POST)
    public Response judge(@RequestParam("caseId") int caseId,
                          @RequestParam("userId") int userId,
                          @RequestParam(value = "useCredit",required = false) boolean useCredit,
                          @RequestParam(value = "workOrderId",required = false) Long workOrderId,
                          @RequestParam(value = "param",required = false,defaultValue = "") String param) {

        log.info("rule judge params, caseId:{}, userId:{}, useCredit:{}, param:{}", caseId, userId, useCredit, param);
        if (!highRiskV2Enable || StringUtils.isEmpty(param)) {
            return judgeV1(caseId, userId, useCredit, param);
        }
        return judgeV2(workOrderId, caseId, userId, param);
    }

    @RequiresPermission("rule:judge")
    @RequestMapping(path = "/get-m", method = RequestMethod.POST)
    public Response getMResult(@RequestParam("caseId") int caseId) {

        if(caseId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        log.info("rule judge getMResult caseId:{}", caseId);
        Integer mResult = highRiskService.getMResult(caseId);
        if(mResult == null || mResult < 0) {
            log.info("获取房产阈值失败 caseId:{}", caseId);
            // 取不到写死返回值100万
            return NewResponseUtil.makeSuccess(1000000);
        }
        return NewResponseUtil.makeSuccess(mResult);
    }
    @RequiresPermission("rule:judge")
    @RequestMapping(path = "/get-enum", method = RequestMethod.POST)
    public Response getMResult() {
        Map<String, Map<Integer, String>> enumMap = Maps.newHashMap();

        Map<Integer, String> houseIncomeEnum = EconomicSituationEnum.getEnumMap();
        enumMap.put("houseIncomeEnum", houseIncomeEnum);
        Map<Integer, String> selfHouseRangeEnum = PatientMarriedEnum.getEnumMap();
        enumMap.put("selfHouseRangeEnum", selfHouseRangeEnum);

        return NewResponseUtil.makeSuccess(enumMap);
    }

    @RequiresPermission("rule:smallToolCost")
    @RequestMapping(path = "/small-tool-cost", method = RequestMethod.POST)
    public Response<SmallToolCostVo> smallToolCost(@RequestParam("caseId") int caseId,
                                                   @RequestParam("userId") int userId,
                                                   @RequestParam(value = "workOrderId", required = false) Long workOrderId) {
        //TODO 此逻辑后续会改成结构化的字段，先基于历史逻辑这样做吧，为了尽快上线
        SmallToolCostVo vo = new SmallToolCostVo();
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
        if (Objects.isNull(crowdfundingInfo)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        Response<WorkOrderVO> lastWorkOrder = cfWorkOrderClient.getLastWorkOrder(caseId, WorkOrderType.target_amount_reasonable_audit.getType());
        WorkOrderVO workOrderVO = Optional.ofNullable(lastWorkOrder)
                .filter(Response::ok)
                .map(Response::getData)
                .filter(f -> f.getHandleResult() == HandleResultEnum.audit_reject.getType())
                .orElse(null);

        // 根据目标审核工单操作记录设置金额上限与下限
        if (Objects.nonNull(workOrderVO)) {
            // 如果对应工单ID有操作记录
            TargetAmountAuditRecord record = targetAmountAuditRecordBiz.getRecordByWorkOrderId(workOrderVO.getWorkOrderId());
            if (Objects.nonNull(record) && record.isReject()) {
                vo.setCostLow(String.valueOf(record.getMinCostAmount() / 10000.0));
                vo.setCostHigh(String.valueOf(record.getMaxCostAmount() / 10000.0));
                return NewResponseUtil.makeSuccess(vo);
            }
        }

        DiseaseStrategyResult diseaseStrategyResult = initialAuditBrainService.selectDiseaseStrategySnapshot(caseId, userId, workOrderId, null);
        if (Objects.nonNull(diseaseStrategyResult) && StringUtils.isNotEmpty(diseaseStrategyResult.getAmountReasonableResultHit()) &&
                diseaseStrategyResult.getAmountReasonableResultHit().contains("未来花费建议为")) {
            String amountReasonableResultHit = diseaseStrategyResult.getAmountReasonableResultHit();
            String low = amountReasonableResultHit.substring(amountReasonableResultHit.indexOf("为") + 1, amountReasonableResultHit.indexOf("-"));
            String high = amountReasonableResultHit.substring(amountReasonableResultHit.indexOf("-") + 1, amountReasonableResultHit.indexOf("w"));
            double highFen = NumberUtils.isNumber(high) ? Double.parseDouble(high) * 1000000 : 0;
            highFen = Math.max(crowdfundingInfo.getTargetAmount(), highFen) / 1000000;
            vo.setCostLow(low);
            vo.setCostHigh(String.valueOf(highFen));
        }

        return NewResponseUtil.makeSuccess(vo);
    }

    private Response judgeV2(Long workOrderId, int caseId, int userId, String param) {
        // 手动调用
        EconomyModel economyModel = JSON.parseObject(param, EconomyModel.class);//已检查过
        economyModel = highRiskService.judgeManual(caseId, workOrderId, userId, economyModel);
        economyModel.setTime(LocalDateTime.now().toString());
        recordOperator(userId, economyModel);
        workOrderExtService.save(caseId,System.currentTimeMillis(), WorkOrderExtContentTypeEnum.RISK_RULE_SNAPSHOT,economyModel);
        return NewResponseUtil.makeSuccess(economyModel.getConvertResult());
    }

    @NotNull
    private Response<?> judgeV1(int caseId, int userId, boolean useCredit, String param) {
        OpResult<Set<RiskRuleResult>> opResult = null;
        EconomyModel economyModel = null;
        //如果填写了  按照填写的计算
        if (StringUtils.isNotEmpty(param)){
            economyModel = JSON.parseObject(param,EconomyModel.class);//已检查过
            economyModel.setStrategy(EconomyModel.strategy_C);
        }else {
            //如果有增信或者前置 使用增信或前置计算
            OpResult<EconomyModel> result = change(caseId, useCredit);
            if (result.isFail()){
                if (result.getErrorCode() == AdminErrorCode.ADMIN_CASE_DIFF
                        || result.getErrorCode() == AdminErrorCode.ADMIN_NO_MATERIAL){
                    return NewResponseUtil.makeSuccess(result.getData().getResult());
                }
                return NewResponseUtil.makeError(result.getErrorCode());
            }
            economyModel = result.getData();

        }
        economyModel.setTime(LocalDateTime.now().toString());

        //如果手动输入的记录操作人
        if (StringUtils.isNotEmpty(param)){

            recordOperator(userId, economyModel);
        }

        opResult = judgeByEconomyModel(economyModel);

        if (opResult.isFail() || opResult.getData() == null){
            return NewResponseUtil.makeError(opResult.getErrorCode());
        }

        List<Integer> list = opResult.getData().stream().map(RiskRuleResult::getCode).collect(Collectors.toList());
        economyModel.setResult(list);
        //因为数据有唯一键  工单id传时间戳
        workOrderExtService.save(caseId,System.currentTimeMillis(), WorkOrderExtContentTypeEnum.RISK_RULE_SNAPSHOT,economyModel);

        List<String> tips = cfRiskRuleBiz.assembleTipInfo(economyModel);

        if (log.isDebugEnabled()) {
            log.debug("rule judge model:{}", economyModel);
        }
        log.info("rule judge resp, ids:{}, tips:{}", economyModel.getResult(), tips);

        return NewResponseUtil.makeSuccess(tips);
    }

    private void recordOperator(int userId, EconomyModel economyModel) {
        economyModel.setUserId(userId);
        AuthRpcResponse<String> authRpcResponse = v1.getMisByUserId(userId);
        economyModel.setUserName(Optional.ofNullable(authRpcResponse).map(AuthRpcResponse::getResult).orElse("-"));

        AuthRpcResponse<AdminOrganization> rpcResponse = orgClientV1.getUserOrgInfo(userId);
        String org = Optional.ofNullable(rpcResponse)
                .filter(AuthRpcResponse::isSuccess)
                .map(AuthRpcResponse::getResult)
                .map(AdminOrganization::getName)
                .orElse("");

        economyModel.setOrg(org);
    }


    @RequiresPermission("rule:list")
    @RequestMapping(path = "/list", method = RequestMethod.POST)
    public Response list(@RequestParam("caseId") int caseId){
        log.info("rule list params, caseId:{}", caseId);

        List<EconomyModel> list = workOrderExtService.listByCaseIdAndClazz(caseId,WorkOrderExtContentTypeEnum.RISK_RULE_SNAPSHOT,EconomyModel.class);

        list = list.stream()
                .peek(economyModel -> economyModel.setConvertResult(cfRiskRuleBiz.assembleTipInfo(economyModel)))
                .sorted(Comparator.comparing(EconomyModel::getTime).reversed())
                .collect(Collectors.toList());

        return NewResponseUtil.makeSuccess(list);
    }

    private OpResult<Set<RiskRuleResult>> judgeByEconomyModel(EconomyModel economyModel){

        if (economyModel == null || economyModel.getTargetAmount() <=0 ){
            return OpResult.createFailResult(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        return cfRiskRuleBiz.judge(economyModel);
    }


    private OpResult<EconomyModel> change(int caseId,boolean useCredit){

        FeignResponse<CrowdfundingInfo> feignResponse = client.getCaseInfoById(caseId);

        CrowdfundingInfo crowdfundingInfo = Optional.ofNullable(feignResponse)
                .filter(FeignResponse::ok)
                .map(FeignResponse::getData)
                .orElse(null);

        if (crowdfundingInfo == null){
            return OpResult.createFailResult(AdminErrorCode.NOT_CASE_DATA);
        }
        //单位转换成元
        int targetAmount = crowdfundingInfo.getTargetAmount()/100;
        EconomyModel economyModel = null;

        RpcResult<CfPropertyInsuranceInfoModel> rpcResult = materialReadClient.selectCfPropertyInsuranceInfo(caseId);

        CfPropertyInsuranceInfoModel infoModel = Optional.ofNullable(rpcResult)
                .filter(RpcResult::isSuccess)
                .map(RpcResult::getData)
                .orElse(null);

        PreposeMaterialModel.MaterialInfoVo vo = getMaterialInfoVo(caseId);
        CfPropertyInsuranceInfoModel materialInfoVo = CfPropertyInsuranceInfoModel.convertFromPrepose(vo);

        // true 代表是增信工单来的
        if (!useCredit && infoModel != null){
            FeignResponse<Integer> typeResponse = client.selectPropertyCaseType(caseId);
            int type = Optional.ofNullable(typeResponse)
                    .filter(FeignResponse::ok)
                    .map(FeignResponse::getData)
                    .orElse(0);
            //如果是初审的工单  增加不是初审添加的 不对比增信
            if (PropertyInsuranceCaseType.INITIAL_WITH_INSURANCE.getCode() != type){
                infoModel = null;
            }
        }

        //全部存在判断是否一致  是有使用增信的时候才判断一致
        if (infoModel != null && materialInfoVo != null){
            if (allEquals(infoModel,materialInfoVo)){
                economyModel = format(infoModel,targetAmount,vo);
                economyModel.setStrategy(EconomyModel.strategy_B);
            }else {
                economyModel = format(infoModel,targetAmount,vo);
                economyModel.setStrategy(EconomyModel.strategy_B);
                /*log.info("结果不一致 caseId={}",caseId);
                economyModel = new EconomyModel();
                economyModel.setResult(Lists.newArrayList(RiskRuleResult.r_1.getCode()));
                return OpResult.createFailResult(AdminErrorCode.ADMIN_CASE_DIFF,economyModel);*/
            }
        }

        //只有前置
        if (materialInfoVo != null && economyModel == null){

            if (!isAll(materialInfoVo,targetAmount,vo)){
                economyModel = new EconomyModel();
                economyModel.setResult(Lists.newArrayList(RiskRuleResult.r_2.getCode()));
                return OpResult.createFailResult(AdminErrorCode.ADMIN_NO_MATERIAL,economyModel);
            }

            economyModel = format(materialInfoVo,targetAmount,vo);
            economyModel.setStrategy(EconomyModel.strategy_B);
        }

        //只有增信
        if (infoModel != null && economyModel == null){
            economyModel = format(infoModel,targetAmount,null);
            economyModel.setStrategy(EconomyModel.strategy_B);
        }

        if (economyModel == null){
            return OpResult.createFailResult(AdminErrorCode.ADMIN_CASE_NO);
        }

        return OpResult.createSucResult(economyModel);
    }

    private boolean isAll(CfPropertyInsuranceInfoModel infoModel,int targetAmount,PreposeMaterialModel.MaterialInfoVo vo){
        EconomyModel economyModel = formatDefault(infoModel,targetAmount,vo);
        if (economyModel.getSpentAmount() < 0
                || (economyModel.getHouseAmount() == 0 && economyModel.getHouseNum() > 0)
                || (economyModel.getCarAmount() == 0 && economyModel.getCarNum() > 0)
                || economyModel.getFinanceAmount() < 0
                || economyModel.getDebtAmount() < 0){
            return false;
        }
        return true;
    }


    private EconomyModel formatDefault(CfPropertyInsuranceInfoModel infoModel,int targetAmount,PreposeMaterialModel.MaterialInfoVo vo){

        EconomyModel em = new EconomyModel();

        em.setTargetAmount(targetAmount);
        if (vo != null){
            em.setTargetAmount(Optional.ofNullable(vo.getTargetAmount()).orElse(0));
            em.setSpentAmount(Optional.ofNullable(vo.getExpectCost()).orElse(0));
        }


        em.setHouseAmount(Optional.ofNullable(infoModel.getHouseProperty()).map(CfPropertyInsuranceInfoModel.HousePropertyInfo::getTotalValueUserDefined).orElse(-1));
        if (em.getHouseAmount() == -1){
            em.setHouseAmount(Optional.ofNullable(infoModel.getHouseProperty())
                    .map(CfPropertyInsuranceInfoModel.HousePropertyInfo::getTotalValueRangeType)
                    .filter(Objects::nonNull)
                    .filter(r -> r > 0)
                    .map(CfPropertyInsuranceInfoModel.HouseValueRange::valueOfCode)
                    .map(CfPropertyInsuranceInfoModel.HouseValueRange::getTo)
                    .orElse(-1));
        }
        em.setHouseNum(Optional.ofNullable(infoModel.getHouseProperty()).map(CfPropertyInsuranceInfoModel.HousePropertyInfo::getTotalCount).orElse(-1));
        em.setCarAmount(Optional.ofNullable(infoModel.getCarProperty()).map(CfPropertyInsuranceInfoModel.CarPropertyInfo::getTotalValueUserDefined).orElse(-1));

        if (em.getCarAmount() == -1){
            em.setCarAmount(Optional.ofNullable(infoModel.getCarProperty())
                    .map(CfPropertyInsuranceInfoModel.CarPropertyInfo::getTotalValueRangeType)
                    .filter(Objects::nonNull)
                    .filter(r -> r > 0)
                    .map(CfPropertyInsuranceInfoModel.CarValueRange::valueOfCode)
                    .map(CfPropertyInsuranceInfoModel.CarValueRange::getTo)
                    .orElse(-1));
        }

        em.setCarNum(Optional.ofNullable(infoModel.getCarProperty()).map(CfPropertyInsuranceInfoModel.CarPropertyInfo::getTotalCount).orElse(-1));
        em.setFinanceAmount(Optional.ofNullable(infoModel.getHomeStockUserDefined()).orElse(-1));
        if (em.getFinanceAmount() == -1){
            em.setFinanceAmount(Optional.ofNullable(infoModel.getHomeStockRangeType())
                    .filter(Objects::nonNull)
                    .filter(r -> r > 0)
                    .map(CfPropertyInsuranceInfoModel.HomeStockValueRange::valueOfCode)
                    .map(CfPropertyInsuranceInfoModel.HomeStockValueRange::getTo)
                    .orElse(-1));
        }

        em.setDebtAmount(Optional.ofNullable(infoModel.getHomeDebtDecimalAmount())
                .filter(Objects::nonNull)
                .map(BigDecimal::intValue)
                .orElse(-1));

        return em;
    }


    private EconomyModel format(CfPropertyInsuranceInfoModel infoModel,int targetAmount,PreposeMaterialModel.MaterialInfoVo vo){

        EconomyModel em = new EconomyModel();

        em.setTargetAmount(targetAmount);
        if (Objects.nonNull(infoModel.getHomeIncomeUserDefined())){
            em.setHomeIncomeRange(infoModel.getHomeIncomeUserDefined());
        }else {
            CfPropertyInsuranceInfoModel.HomeIncomeValueRange homeIncomeValueRange = CfPropertyInsuranceInfoModel.HomeIncomeValueRange.valueOfCode(infoModel.getHomeIncomeRangeType());
            em.setHomeIncomeRange(homeIncomeValueRange.getTo());
        }
        em.setRemainAmount(Optional.ofNullable(infoModel.getOtherPlatform()).map(CfPropertyInsuranceInfoModel.RaiseOnOtherPlatform::getRemainAmount).orElse(0));
        if (vo != null && vo.getExpectCost() != null){
            em.setSpentAmount(vo.getExpectCost());
        }

        em.setHouseAmount(Optional.ofNullable(infoModel.getHouseProperty()).map(CfPropertyInsuranceInfoModel.HousePropertyInfo::getTotalValueUserDefined).orElse(0));
        if (em.getHouseAmount() == 0){
            em.setHouseAmount(Optional.ofNullable(infoModel.getHouseProperty())
                    .map(CfPropertyInsuranceInfoModel.HousePropertyInfo::getTotalValueRangeType)
                    .filter(Objects::nonNull)
                    .filter(r -> r > 0)
                    .map(CfPropertyInsuranceInfoModel.HouseValueRange::valueOfCode)
                    .map(CfPropertyInsuranceInfoModel.HouseValueRange::getTo)
                    .orElse(0));
        }
        em.setHouseNum(Optional.ofNullable(infoModel.getHouseProperty()).map(CfPropertyInsuranceInfoModel.HousePropertyInfo::getTotalCount).orElse(0));
        em.setOtherHouseSellingCount(Optional.ofNullable(infoModel.getHouseProperty()).map(CfPropertyInsuranceInfoModel.HousePropertyInfo::getSaleCount).orElse(0));
        em.setCarAmount(Optional.ofNullable(infoModel.getCarProperty()).map(CfPropertyInsuranceInfoModel.CarPropertyInfo::getTotalValueUserDefined).orElse(0));

        if (em.getCarAmount() == 0){
            em.setCarAmount(Optional.ofNullable(infoModel.getCarProperty())
                    .map(CfPropertyInsuranceInfoModel.CarPropertyInfo::getTotalValueRangeType)
                    .filter(Objects::nonNull)
                    .filter(r -> r > 0)
                    .map(CfPropertyInsuranceInfoModel.CarValueRange::valueOfCode)
                    .map(CfPropertyInsuranceInfoModel.CarValueRange::getTo)
                    .orElse(0));
        }

        em.setCarNum(Optional.ofNullable(infoModel.getCarProperty()).map(CfPropertyInsuranceInfoModel.CarPropertyInfo::getTotalCount).orElse(0));
        em.setFinanceAmount(Optional.ofNullable(infoModel.getHomeStockUserDefined()).orElse(0));
        if (em.getFinanceAmount() == 0){
            em.setFinanceAmount(Optional.ofNullable(infoModel.getHomeStockRangeType())
                    .filter(Objects::nonNull)
                    .filter(r -> r > 0)
                    .map(CfPropertyInsuranceInfoModel.HomeStockValueRange::valueOfCode)
                    .map(CfPropertyInsuranceInfoModel.HomeStockValueRange::getTo)
                    .orElse(0));
        }

        em.setDebtAmount(Optional.ofNullable(infoModel.getHomeDebtDecimalAmount())
                .filter(Objects::nonNull)
                .map(BigDecimal::intValue)
                .orElse(0));

        if (infoModel.getSelfBuiltHouse() != null) {
            em.setHasSelfHouse(true);
            CfPropertyInsuranceInfoModel.HousePropertyInfo selfBuiltHouse = infoModel.getSelfBuiltHouse();
            em.setSelfHouseNum(Optional.ofNullable(selfBuiltHouse.getTotalCount()).orElse(0));
            em.setSelfHouseSellingCount(Optional.ofNullable(selfBuiltHouse.getSaleCount()).orElse(0));
            if (Objects.nonNull(selfBuiltHouse.getTotalValueUserDefined()) && selfBuiltHouse.getTotalValueUserDefined() > 0) {
                em.setSelfHouseValue(selfBuiltHouse.getTotalValueUserDefined());
            } else if (Objects.nonNull(selfBuiltHouse.getTotalValueRangeType())
                    && Objects.nonNull(CfPropertyInsuranceInfoModel.HouseValueRange.valueOfCode(selfBuiltHouse.getTotalValueRangeType()))) {
                CfPropertyInsuranceInfoModel.HouseValueRange houseValueRange = CfPropertyInsuranceInfoModel.HouseValueRange.valueOfCode(selfBuiltHouse.getTotalValueRangeType());
                em.setSelfHouseValue(houseValueRange.getTo());
            }
        }

        em.setHasPersonalInsurance(Optional.ofNullable(infoModel.getLifeInsurance()).orElse(-1));
        em.setAccidentStatus(Optional.ofNullable(vo).map(PreposeMaterialModel.MaterialInfoVo::getAccidentType).orElse(-1));
        em.setPropertySoldStatus(Optional.ofNullable(infoModel.getHouseProperty().getSaleStatus()).orElse(-1));

        return em;
    }

    private PreposeMaterialModel.MaterialInfoVo getMaterialInfoVo(int caseId){

        PreposeMaterialModel.MaterialInfoVo o = preposeMaterialDelegate.queryByCaseId(caseId);

        return o;
    }

    private boolean allEquals(CfPropertyInsuranceInfoModel o1,CfPropertyInsuranceInfoModel o2){

        return  Objects.equals(o1.getMedicalInsurance(), o2.getMedicalInsurance()) &&
                Objects.equals(o1.getLifeInsurance(), o2.getLifeInsurance()) &&
                Objects.equals(o1.getPropertyInsurance(), o2.getPropertyInsurance()) &&
                Objects.equals(o1.getGovRelief(), o2.getGovRelief()) &&
                Objects.equals(o1.getTotalGovReliefDecimalAmount(), o2.getTotalGovReliefDecimalAmount()) &&
                Objects.equals(o1.getHomeIncomeRangeType(), o2.getHomeIncomeRangeType()) &&
                Objects.equals(o1.getHomeIncomeUserDefined(), o2.getHomeIncomeUserDefined()) &&
                Objects.equals(o1.getHasHomeStock(), o2.getHasHomeStock()) &&
                Objects.equals(o1.getHomeStockRangeType(), o2.getHomeStockRangeType()) &&
                Objects.equals(o1.getHomeStockUserDefined(), o2.getHomeStockUserDefined()) &&
                Objects.equals(o1.getHomeDebt(), o2.getHomeDebt()) &&
                Objects.equals(Optional.ofNullable(o1.getHomeDebtDecimalAmount()).orElse(BigDecimal.ZERO), Optional.ofNullable(o2.getHomeDebtDecimalAmount()).orElse(BigDecimal.ZERO)) &&
                Objects.equals(o1.getCarProperty(), o2.getCarProperty()) &&
                Objects.equals(o1.getHouseProperty(), o2.getHouseProperty());
    }
}
