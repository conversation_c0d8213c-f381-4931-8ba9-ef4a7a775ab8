package com.shuidihuzhu.cf.admin.controller.api.basic;

import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationRecordDTO;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Api("获取操作记录")
@RestController
@RequestMapping("admin/cf/common-operation")
public class CommonOperationController {

    @Autowired
    private CommonOperationRecordClient commonOperationRecordClient;

    @ApiOperation("公共操作记录方法")
    @PostMapping("list-by-param")
    public Response<List<OperationRecordDTO>> listByParam(@RequestParam("bizId") int bizId,
                                                          @RequestParam("types") List<Integer> types){
        List<OperationRecordDTO> list = commonOperationRecordClient.listByBizIdAndActionTypes(bizId, types);
        return NewResponseUtil.makeSuccess(list);
    }

}
