package com.shuidihuzhu.cf.admin.controller.api.innerapi.redis;

import com.shuidihuzhu.cf.admin.controller.api.innerapi.redis.inner.CfRedisToolInnerFeignClient;
import com.shuidihuzhu.cf.admin.controller.api.innerapi.redis.inner.CfRedisToolManagerImpl;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * QA 处理redis 大key报警工具
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/innerapi/cf/admin/cf-redis-tool")
public class CfRedisToolFeignController {

    @Autowired
    private CfRedisToolManagerImpl cfRedisToolManager;

    @PostMapping("/ttl")
    public Response<String> ttl(@RequestParam("appName") String appName, @RequestParam("ip") String ip, @RequestParam("key") String key) {
        return cfRedisToolManager.getByAppName(appName).ttl(ip, key);
    }


    @PostMapping("/del")
    public Response<Boolean> del(@RequestParam("appName") String appName, @RequestParam("ip") String ip, @RequestParam("key") String key) {
        return cfRedisToolManager.getByAppName(appName).del(ip, key);
    }


    @PostMapping("/get")
    public Response<CfRedisToolInnerFeignClient.RedisqueryResponseDTO> get(@RequestParam("appName") String appName, @RequestParam("ip") String ip, @RequestParam("key") String key) {
        return cfRedisToolManager.getByAppName(appName).get(ip, key);
    }
}
