package com.shuidihuzhu.cf.admin.controller.api.common;

import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.base.page.v1.model.PaginationListVO;
import com.shuidihuzhu.cf.risk.client.risk.RiskStrategyBizClient;
import com.shuidihuzhu.cf.risk.model.FamilyFinancialSituation;
import com.shuidihuzhu.cf.risk.model.param.CityParam;
import com.shuidihuzhu.cf.risk.model.risk.CarNetValueInfo;
import com.shuidihuzhu.cf.risk.model.risk.ComputingRecord;
import com.shuidihuzhu.cf.risk.model.risk.ComputingToolResult;
import com.shuidihuzhu.cf.risk.model.risk.HouseNetValueInfo;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.cf.api.client.CrowdfundingCityFeignClient;
import com.shuidihuzhu.client.cf.api.model.CardMsgVO;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * @Author: wangpeng
 * @Date: 2023/4/26 15:07
 * @Description:
 */
@Slf4j
@Controller
@RequestMapping(path = "/admin/cf/tool")
public class ToolController {

    @Resource
    private RiskStrategyBizClient riskStrategyBizClient;
    @Resource
    private CrowdfundingCityFeignClient crowdfundingCityFeignClient;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    @RequestMapping(path = "/id-card-extractor", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "身份证")
    @RequiresPermission("admin-tool:id-card-extractor")
    public Response<CardMsgVO> cityHouseThreshold(@RequestParam @ApiParam(value = "身份证") String idCard) {
        return crowdfundingCityFeignClient.getCityByIdCard(idCard);
    }

    @ResponseBody
    @ApiOperation(value = "判断车产品牌是否名牌")
    @PostMapping(path = "/check-if-luxury")
    public Response<Boolean> checkIfLuxury(@RequestParam @ApiParam(value = "车产品牌") String carBrand) {

        if (StringUtils.isEmpty(carBrand)) {
            return NewResponseUtil.makeSuccess(false);
        }

        Response<Boolean> response = riskStrategyBizClient.checkIfLuxury(carBrand);
        Boolean isLuxury = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(false);
        return NewResponseUtil.makeSuccess(isLuxury);
    }

    @ResponseBody
    @ApiOperation(value = "计算房产净值")
    @PostMapping(path = "/count-house-net-value")
    public Response<List<Integer>> countHouseNetValue(@RequestBody List<HouseNetValueInfo> houseValue) {
        if (CollectionUtils.isEmpty(houseValue)) {
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        return riskStrategyBizClient.countHouseNetValue(houseValue);
    }

    @ResponseBody
    @ApiOperation(value = "计算车产净值")
    @PostMapping(path = "/count-car-net-value")
    public Response<List<Integer>> countCarNetValue(@RequestBody List<CarNetValueInfo> carNetValueInfos) {
        if (CollectionUtils.isEmpty(carNetValueInfos)) {
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        return riskStrategyBizClient.countCarNetValue(carNetValueInfos);
    }

    @ResponseBody
    @ApiOperation(value = "小工具内容提交计算接口")
    @PostMapping(path = "/submit")
    public Response<ComputingToolResult> submit(@RequestParam String extName,
                                                @RequestParam long extId,
                                                @RequestParam String submitJson) {
        if(StringUtils.isEmpty(extName) || extId <= 0 || StringUtils.isBlank(submitJson)){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        long adminUserId = ContextUtil.getAdminLongUserId();
        return riskStrategyBizClient.submit(extName, extId, adminUserId, submitJson);
    }

    @ResponseBody
    @ApiOperation(value = "回显上次填写内容接口 && 引用至小工具")
    @PostMapping(path = "/get-record-by-id")
    public Response<FamilyFinancialSituation> getRecordById(@RequestParam String extName,
                                                       @RequestParam long extId,
                                                       @RequestParam int recordId) {
        if(StringUtils.isEmpty(extName) || extId <= 0){
            return NewResponseUtil.makeSuccess();
        }
        long adminUserId = ContextUtil.getAdminLongUserId();
        return riskStrategyBizClient.getRecordById(extName, extId, recordId);
    }


    @ResponseBody
    @ApiOperation(value = "小工具内容提交显示历史记录")
    @PostMapping(path = "/get-record-list")
    public Response<List<ComputingRecord>> getRecordList(@RequestParam String extName,
                                                         @RequestParam long extId,
                                                         @RequestParam(defaultValue = "1") int current,
                                                         @RequestParam(defaultValue = "10") int pageSize) {
        if(StringUtils.isEmpty(extName) || extId <= 0){
            return NewResponseUtil.makeSuccess();
        }

        Response<List<ComputingRecord>> listResponse = riskStrategyBizClient.getRecordList(extName, extId);
        if (listResponse.notOk()) {
            return NewResponseUtil.makeFail("获取历史记录失败");
        }
        List<ComputingRecord> list = listResponse.getData();
        if(CollectionUtils.isEmpty(list)){
            return NewResponseUtil.makeSuccess();
        }
        PaginationListVO<ComputingRecord> pageReturnModel = PaginationListVO.create(list, current, pageSize, list.size());
        Page page = new Page(current, pageSize);
        page.addAll(pageReturnModel != null ? pageReturnModel.getList() : Lists.newArrayList());
        page.setTotal(pageReturnModel != null ? pageReturnModel.getPagination().getTotal() : 0);
        return NewResponseUtil.makeSuccess(page);
    }

    @ResponseBody
    @ApiOperation(value = "查看某次计算结果")
    @PostMapping(path = "/get-result-record-by-id")
    public Response<ComputingToolResult> getResultRecordById(@RequestParam String extName,
                                                                  @RequestParam long extId,
                                                                  @RequestParam int recordId) {
        if(StringUtils.isEmpty(extName) || extId <= 0){
            return NewResponseUtil.makeSuccess();
        }
        long adminUserId = ContextUtil.getAdminLongUserId();
        return riskStrategyBizClient.getResultRecordById(extName, extId, recordId);
    }

    @ResponseBody
    @ApiOperation("加密接口")
    @PostMapping(path = "/encrypt")
    public Response<String> encrypt(@RequestParam String content) {
        if (StringUtils.isEmpty(content)) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        String encryptMobile = oldShuidiCipher.aesEncrypt(content);
        return NewResponseUtil.makeSuccess(encryptMobile);
    }
}
