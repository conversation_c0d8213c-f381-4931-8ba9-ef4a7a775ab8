package com.shuidihuzhu.cf.admin.mq;


import com.shuidihuzhu.cf.biz.crmUserManage.CrmUserRoleBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfRepeatInfoBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingComment;
import com.shuidihuzhu.cf.service.admin.CfHospitalAuditService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

// 发起人身份证 发起人手机号  患者身份证 患者姓名  收款人身份证
// 没有走首次的 患者的姓名和患者身份证 crowdfunding_author.name crowdfunding_author.crypto_id_card 加过密
// 发起人身份证 crowdfunding_id_case  发起人手机?
// 收款人身份证 crowdfunding_info_payee

// 走首次了 发起人身份证 发起人手机号 患者姓名 患者身份证号  cf_first_approve_material
//
@Service
@RocketMQListener(id = MQTagCons.CF_BASE_INFO_CHANGE_TAG,
        group = "cf-admin-"  + MQTagCons.CF_BASE_INFO_CHANGE_TAG,
        tags = MQTagCons.CF_BASE_INFO_CHANGE_TAG,
        topic = MQTopicCons.CF)
@Slf4j
public class CfBaseInfoRepeatConsumer implements MessageListener<Integer> {

    @Autowired
    private AdminCfRepeatInfoBiz cfRepeatInfoBiz;

    @Autowired
    private CfHospitalAuditService cfHospitalAuditService;

    @Autowired
    private CrmUserRoleBiz userRoleBiz;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<Integer> mqMessage) {
        Integer caseId = mqMessage.getPayload();

        try {
            cfHospitalAuditService.onBaseInfoSubmit(caseId);
        } catch (Exception e) {
            log.error("onBaseInfoSubmit caseId:{}", caseId, e);
        }

        log.info("筹款重复二次发起。收到的message caseId:{}", caseId);

//        userRoleBiz.updateCrmManageUserRoleMapping(caseId);

        cfRepeatInfoBiz.handleCfMaterialRepeat(caseId, true);

        return ConsumeStatus.CONSUME_SUCCESS;
    }


}
