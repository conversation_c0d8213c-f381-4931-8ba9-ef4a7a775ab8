package com.shuidihuzhu.cf.admin.controller.api.workorder;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingCityBiz;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.admin.UserCommentBiz;
import com.shuidihuzhu.cf.biz.aiphoto.ImageWatermarkService;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdFundingProgressBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfSensitiveWordRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.supply.CfSupplyActionBiz;
import com.shuidihuzhu.cf.client.apipure.enums.complaint.ComplaintBizTypeEnum;
import com.shuidihuzhu.cf.client.apipure.enums.complaint.ComplaintResultEnum;
import com.shuidihuzhu.cf.client.apipure.enums.complaint.ComplaintTypeEnum;
import com.shuidihuzhu.cf.client.apipure.model.crowdfunding.complaint.CfComplaintDO;
import com.shuidihuzhu.cf.client.feign.CfUserInfoFeignClient;
import com.shuidihuzhu.cf.client.feign.CfVerificationFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminUGCTask;
import com.shuidihuzhu.cf.enums.crowdfunding.CfSensitiveWordRecordEnum;
import com.shuidihuzhu.cf.model.admin.UgcWorkOrderVO;
import com.shuidihuzhu.cf.model.admin.common.SupplyProgressReasonItem;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.param.SupplyOrgEnum;
import com.shuidihuzhu.cf.model.param.SupplyProgressWorkHandleParam;
import com.shuidihuzhu.cf.model.param.SupplyProgressWorkOrderParam;
import com.shuidihuzhu.cf.risk.client.rpc.DiscussionCommentClient;
import com.shuidihuzhu.cf.risk.model.CommentVO;
import com.shuidihuzhu.cf.service.crowdfunding.CfSupplyActionService;
import com.shuidihuzhu.cf.service.crowdfunding.CfSupplyProgressService;
import com.shuidihuzhu.cf.service.crowdfunding.CfTemplateFieldService;
import com.shuidihuzhu.cf.service.crowdfunding.complaint.ComplaintService;
import com.shuidihuzhu.cf.service.rejectmanager.impl.SupplyProgressReasonService;
import com.shuidihuzhu.cf.util.crowdfunding.LocalDateTimeUtils;
import com.shuidihuzhu.cf.vo.crowdfunding.CfSupplyProgressWorkDetailVo;
import com.shuidihuzhu.cf.vo.crowdfunding.CfVerificationVo;
import com.shuidihuzhu.client.cf.admin.enums.CfProgressReasonEnum;
import com.shuidihuzhu.client.cf.workorder.CfUgcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.UgcHandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderListParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.frame.client.api.mina.HopeTreeClient;
import com.shuidihuzhu.frame.client.model.mina.hopetree.HopeTreeStateRecordDO;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2019/10/10
 */
@RestController
@RequestMapping(path = "/admin/workorder/ugc")
@Slf4j
public class UgcWorkOrderController {

    @Resource
    private CfUgcWorkOrderClient ugcWorkOrderClient;

    @Autowired
    private CfSensitiveWordRecordBiz sensitiveWordRecordBiz;

    @Autowired
    private AdminCrowdFundingProgressBiz adminCrowdFundingProgressBiz;

    @Resource
    private IRiskDelegate riskDelegate;

    @Resource
    private CfSupplyProgressService cfSupplyProgressService;

    @Resource
    private CfSupplyActionBiz cfSupplyActionBiz;

    @Autowired
    private SupplyProgressReasonService seaRejectService;

    @Resource
    private CfWorkOrderClient cfWorkClient;

    @Autowired
    private DiscussionCommentClient discussionCommentClient;

    @Autowired
    private ImageWatermarkService watermarkService;

    @Autowired
    private UserCommentBiz commentBiz;

    @Autowired
    private CfSupplyActionService cfSupplyActionService;

    @Autowired
    private CfTemplateFieldService cfTemplateFieldService;

    @Autowired
    private HopeTreeClient hopeTreeClient;

    @Resource
    private CfUserInfoFeignClient cfUserInfoFeignClient;

    @Resource
    private CfVerificationFeignClient cfVerificationFeignClient;

    @Autowired
    private AdminCrowdfundingCityBiz adminCrowdfundingCityBiz;

    @Resource
    private ComplaintService complaintService;


    @RequiresPermission("ugc-work-order:orderList")
    @RequestMapping(path = "ugc-orderlist", method = RequestMethod.POST)
    Response ugcOrderlist(@RequestParam("param") String param) {

        WorkOrderListParam p = JSON.parseObject(param, WorkOrderListParam.class);//已检查过

        Response<PageResult<WorkOrderVO>> response = ugcWorkOrderClient.ugcOrderlist(p);

        if (response == null || response.notOk() || response.getData() == null) {
            return response;
        }

        PageResult<WorkOrderVO> pageResult = response.getData();

        List<WorkOrderVO> list = pageResult.getPageList();

        if (CollectionUtils.isEmpty(list)) {
            return response;
        }
        List<Long> ids = list.stream()
                .filter(r -> StringUtils.isNotBlank(r.getWordId()))
                .map(r -> Long.valueOf(r.getWordId()))
                .collect(Collectors.toList());

        List<CfSensitiveWordRecord> records = sensitiveWordRecordBiz.selectByIds(ids);

        Map<Long, CfSensitiveWordRecord> map = records.stream().collect(Collectors.toMap(CfSensitiveWordRecord::getId, Function.identity()));

        Map<Long, CommentVO> pingyiMap = getPingyiList(ids);

        List<UgcWorkOrderVO> ugcWorkOrderVOList = list.stream()
                .map(r -> {
                    UgcWorkOrderVO vo = new UgcWorkOrderVO();
                    BeanUtils.copyProperties(r, vo);
                    vo.setVerificationId(r.getVerificationId());
                    vo.setContentTypeStr(AdminUGCTask.Content.getByCode(Integer.valueOf(r.getContentType())).getWord());

                    if (AdminUGCTask.Content.PINGYI.getCode() == Integer.valueOf(r.getContentType())) {
                        if (StringUtils.isNotEmpty(r.getWordId())){
                            CommentVO commentVO = pingyiMap.get(Long.valueOf(r.getWordId()));
                            if (commentVO != null) {
                                vo.setContent(commentVO.getContent());
                                vo.setUserId(commentVO.getUserId());
                                vo.setSensitiveWord(commentBiz.formatHitWordsTDisplay(commentVO.getSensitiveWord()));
                            }
                        }
                    } else {
                        if (StringUtils.isNotEmpty(r.getWordId())){
                            CfSensitiveWordRecord record = map.get(Long.valueOf(r.getWordId()));
                            if (record != null) {
                                vo.setContent(record.getContent());
                                vo.setUserId(record.getUserId());
                                vo.setSensitiveWord(record.getSensitiveWord());
                            }
                        }
                    }
                    return vo;
                }).collect(Collectors.toList());

        Map<Integer, CrowdFundingVerification> verificationMap = null;

        List<Long> verificationIdList = list.stream()
                .filter(r -> StringUtils.isNotEmpty(r.getVerificationId()))
                .map( e -> Long.parseLong(e.getVerificationId()))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(verificationIdList)) {
            Response<List<CrowdFundingVerification>> verificationResponse = cfVerificationFeignClient.getVerificationList(verificationIdList);
            if(Objects.nonNull(verificationResponse) && Objects.nonNull(verificationResponse.getData())){
                List<CrowdFundingVerification> verificationList = verificationResponse.getData();
                if (CollectionUtils.isNotEmpty(verificationList)) {
                    verificationMap = verificationList.stream().collect(Collectors.toMap(CrowdFundingVerification::getId, Function.identity(), (a, b)->b));
                }
            }

            Map<String, String> provinceMap = null;
            List<CrowdfundingCity> provinceList = adminCrowdfundingCityBiz.getProvince();
            if (CollectionUtils.isNotEmpty(provinceList)) {
                provinceMap = provinceList.stream().collect(Collectors.toMap(e -> e.getCode(), e -> e.getName(), (a, b)->b));
            }

            for (UgcWorkOrderVO ugcWorkOrderVO : ugcWorkOrderVOList) {
                String stringVerificationId = ugcWorkOrderVO.getVerificationId();
                if (StringUtils.isEmpty(stringVerificationId)) {
                    continue;
                }
                Long verificationId = Long.parseLong(stringVerificationId);
                if (Objects.nonNull(verificationMap)) {
                    CrowdFundingVerification verification = verificationMap.get(verificationId.intValue());
                    if (Objects.nonNull(verification)) {
                        // 用户id取用户证实的id
                        ugcWorkOrderVO.setUserId(verification.getVerifyUserId());

                        // 医护标签升级新增属性
                        ugcWorkOrderVO.setMedicalImageList(verification.getMedicalImageList());
                        ugcWorkOrderVO.setHospitalName(verification.getHospitalName());
                        if (Objects.nonNull(verification.getProvinceCode())) {
                            ugcWorkOrderVO.setProvinceName(provinceMap.get(String.valueOf(verification.getProvinceCode())));
                        }

                        FeignResponse<UserRealInfo> userRealInfoFeignResponse = cfUserInfoFeignClient.getByUserId(verification.getVerifyUserId());
                        if (Objects.nonNull(userRealInfoFeignResponse) && Objects.nonNull(userRealInfoFeignResponse.getData())) {
                            UserRealInfo userRealInfo = userRealInfoFeignResponse.getData();
                            ugcWorkOrderVO.setMedicalVerifyResultImageUrl(userRealInfo.getMedicalImageUrl());

                            // 快照逻辑,
                            if (StringUtils.isNotEmpty(ugcWorkOrderVO.getMedicalStatus())) {
                                ugcWorkOrderVO.setIsMedical(Integer.parseInt(ugcWorkOrderVO.getMedicalStatus()));
                            } else {
                                ugcWorkOrderVO.setIsMedical(userRealInfo.getMedicalStatus());
                            }
                        }
                        ugcWorkOrderVO.setMedicalWorkType(ugcWorkOrderVO.getMedicalWorkType());
                        ugcWorkOrderVO.setVerifyName(verification.getUserName());

                        if (ugcWorkOrderVO.getOrderType() == WorkOrderType.ugc_complaint_verify.getType()){
                            ugcWorkOrderVO.setContent(verification.getDescription());
                            List<CfComplaintDO> cfComplaintDOS = complaintService.listByBizIdAndBizTypeAndComplaintResult(verificationId, ComplaintBizTypeEnum.VERIFICATION, ComplaintResultEnum.YES);
                            List<CfVerificationVo.ComplaintInfo> complaintInfoList = cfComplaintDOS.stream().map(e -> {

                                CfVerificationVo.ComplaintInfo complaintInfo = new CfVerificationVo.ComplaintInfo();
                                complaintInfo.setComplaintUserId(e.getComplaintUserId());
                                complaintInfo.setComplaintMsg(ComplaintTypeEnum.parse(e.getComplaintType()).getDesc());
                                String complaintTime = LocalDateTimeUtils.localDateTime2String(e.getComplaintTime());
                                complaintInfo.setComplaintTime(complaintTime);

                                return complaintInfo;
                            }).collect(Collectors.toList());
                            ugcWorkOrderVO.setComplaintInfoList(complaintInfoList);
                            ugcWorkOrderVO.setComplaintInfoSize(complaintInfoList.size());
                            // 敏感词
                            List<CfSensitiveWordRecord> cfSensitiveWordRecords = sensitiveWordRecordBiz.listByBizIdAndBizType(verificationId, CfSensitiveWordRecordEnum.BizType.VERIFICATION);
                            if(CollectionUtils.isNotEmpty(cfSensitiveWordRecords)){
                                CfSensitiveWordRecord record = cfSensitiveWordRecords.get(0);
                                if (record != null) {
                                    ugcWorkOrderVO.setContent(record.getContent());
                                    ugcWorkOrderVO.setSensitiveWord(record.getSensitiveWord());
                                    ugcWorkOrderVO.setContentTypeStr(AdminUGCTask.Content.getByCode(Integer.valueOf(ugcWorkOrderVO.getContentType())).getWord());
                                }
                            }
                        }
                    }
                }
            }
        }


        PageResult<UgcWorkOrderVO> result = new PageResult<>();
        result.setHasNext(pageResult.isHasNext());
        result.setPageList(ugcWorkOrderVOList);

        return NewResponseUtil.makeSuccess(result);
    }

    private Map<Long, CommentVO> getPingyiList(List<Long> list) {

        Response<List<CommentVO>> response = discussionCommentClient.findById(list);
        List<CommentVO> commentVOS = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(Lists.newArrayList());

        return commentVOS.stream().collect(Collectors.toMap(CommentVO::getId, Function.identity()));
    }

    @RequiresPermission("ugc-work-order:ugc-progress-orderList")
    @RequestMapping(path = "ugc-progress-orderlist", method = RequestMethod.POST)
    Response ugcProgressOrderlist(@RequestParam("param") String param) {

        WorkOrderListParam p = JSON.parseObject(param, WorkOrderListParam.class);//已检查过

        Response<PageResult<WorkOrderVO>> response = ugcWorkOrderClient.ugcOrderlist(p);

        if (response == null || response.notOk() || response.getData() == null) {
            return response;
        }

        PageResult<WorkOrderVO> pageResult = response.getData();

        List<WorkOrderVO> list = pageResult.getPageList();

        if (CollectionUtils.isEmpty(list)) {
            return response;
        }

        Map<Integer, CrowdFundingProgress> map = Maps.newHashMap();
        if (p.getOrderType() == WorkOrderType.ugcprogress.getType()) {
            map.putAll(adminCrowdFundingProgressBiz.getMapByIds(
                    list.stream()
                            .filter(item -> StringUtils.isNotBlank(item.getWordId()))
                            .map(r -> Integer.valueOf(r.getWordId()))
                            .collect(Collectors.toList())));
        }
        Map<Long, CfInfoSupplyAction> actionMap = Maps.newHashMap();
        if (p.getOrderType() == WorkOrderType.xiafaprogress.getType()) {

            List<Long> ids = list.stream()
                    .map(WorkOrderVO::getSupplyActionId)
                    .filter(l -> l > 0)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(ids)) {
                actionMap.putAll(cfSupplyActionService.getActionMap(ids));
            }

        }
        Map<Long, CfInfoSupplyProgress> supplyProgressMap = cfSupplyProgressService.getSupplyProgressList(list, p);

        Map<Long, HopeTreeStateRecordDO> stateMap = Maps.newHashMap();
        if (p.getOrderType() == WorkOrderType.ugcprogress.getType()) {
            List<Long> ids = list.stream()
                    .filter(r->StringUtils.isNotBlank(r.getHopeTreeStateId()))
                    .filter(r->AdminUGCTask.Content.getByCode(Integer.valueOf(r.getContentType()))== AdminUGCTask.Content.HOPE_TREE_STATE)
                    .map(r->Long.valueOf(r.getHopeTreeStateId()))
                    .collect(Collectors.toList());
            Response<List<HopeTreeStateRecordDO>> stateRecord = hopeTreeClient.getListById(ids);
            if(stateRecord.ok()){
                stateMap.putAll(stateRecord.getData().stream().collect(Collectors.toMap(HopeTreeStateRecordDO::getId, Function.identity())));
            }
        }

        List<UgcWorkOrderVO> ugcWorkOrderVOList = list.stream()
                .map(r -> {
                    UgcWorkOrderVO vo = new UgcWorkOrderVO();
                    BeanUtils.copyProperties(r, vo);
                    if (r.getOrderType() == WorkOrderType.ugcprogress.getType()) {
                        if (StringUtils.isNotEmpty(r.getContentType()) && NumberUtils.isNumber(r.getContentType())) {
                            AdminUGCTask.Content content = AdminUGCTask.Content.getByCode(Integer.parseInt(r.getContentType()));
                            vo.setContentTypeStr(Objects.nonNull(content) ? content.getWord() : "");
                        }

                        CrowdFundingProgress record = null;
                        if (StringUtils.isNotBlank(r.getWordId())) {
                            record = map.get(Integer.valueOf(r.getWordId()));
                        }

                        if (record != null) {
                            vo.setContent(record.getContent());
                            vo.setUserId(record.getUserId());
                            vo.setAttachmentUrls(record.getImageUrls());
                            Set<String> sensitiveWords = riskDelegate.getHitWords(record.getContent());
                            vo.setSensitiveWord(Joiner.on(",").join(sensitiveWords));
                            watermarkService.fillUgcProgressWatermark(record.getActivityId(), vo);
                        }

                        //希望树动态
                        if(Integer.valueOf(r.getContentType()) == AdminUGCTask.Content.HOPE_TREE_STATE.getCode()){
                            HopeTreeStateRecordDO stateRecordDO = stateMap.get(Long.valueOf(r.getHopeTreeStateId()));
                            if(stateRecordDO != null) {
                                vo.setContent(stateRecordDO.getContent());
                                vo.setAttachmentUrls(stateRecordDO.getImageUrl());
                                Set<String> sensitiveWords = riskDelegate.getHitWords(stateRecordDO.getContent());
                                vo.setSensitiveWord(Joiner.on(",").join(sensitiveWords));
                            }
                        }
                    }

                    if (r.getOrderType() == WorkOrderType.xiafaprogress.getType()) {
                        CfInfoSupplyAction a = actionMap.get(r.getSupplyActionId());
                        if (a != null) {
                            vo.setContent(a.getComment());
                            vo.setAttachmentUrls(a.getImgUrls());
                            if (a.getUseTemplate() == 0) {
                                vo.setSupplyReasons(Lists.newArrayList(CfProgressReasonEnum.getByCode(Integer.parseInt(a.getSupplyReason())).getName()));
                            } else {
                                vo.setSupplyReasons(Lists.newArrayList(cfTemplateFieldService.getById(Long.parseLong(a.getSupplyReason())).getTemplateName()));
                            }
                            Set<String> sensitiveWords = riskDelegate.getHitWords(vo.getContent());
                            vo.setSensitiveWord(Joiner.on(",").join(sensitiveWords));
                            vo.setSupplyOrgName(a.getSupplyOrgName());
                        }
                        CfInfoSupplyProgress pp = supplyProgressMap.get(r.getSupplyProgressId());
                        if (pp != null) {
                            vo.setContent(pp.getContent());
                            vo.setAttachmentUrls(pp.getImgUrls());
                            Set<String> sensitiveWords = riskDelegate.getHitWords(pp.getContent());
                            vo.setSensitiveWord(Joiner.on(",").join(sensitiveWords));
                            CfInfoSupplyAction supplyAction = cfSupplyActionBiz.getById(pp.getProgressActionId());
                            List<Integer> supplyReasonIds = Splitter.on(',')
                                    .splitToList(supplyAction.getSupplyReason())
                                    .stream().map(Integer::valueOf)
                                    .collect(Collectors.toList());
                            List<String> supplyReasons = seaRejectService.listRejectByIds(supplyReasonIds)
                                    .stream()
                                    .map(SupplyProgressReasonItem::getDescribe)
                                    .collect(Collectors.toList());
                            vo.setSupplyReasons(supplyReasons);
                            vo.setSupplyOrgName(supplyAction.getSupplyOrgName());
                        }
                    }

                    return vo;
                }).collect(Collectors.toList());

        PageResult<UgcWorkOrderVO> result = new PageResult<>();
        result.setHasNext(pageResult.isHasNext());
        result.setPageList(ugcWorkOrderVOList);

        return NewResponseUtil.makeSuccess(result);
    }

    @RequiresPermission("ugc-work-order:supply-progress-orderList")
    @RequestMapping(path = "supply-progress-orderlist", method = RequestMethod.POST)
    @Deprecated
    public Response supplyProgressOrderList(@RequestParam("param") String param) {
        SupplyProgressWorkOrderParam p = JSON.parseObject(param, SupplyProgressWorkOrderParam.class);//已检查过

        if (p.getSupplyReasonId() > 0 || p.getSupplyOrg() > 0) {
            SupplyOrgEnum supplyOrgEnum = SupplyOrgEnum.findByCode(p.getSupplyOrg());
            if (supplyOrgEnum == null) {
                p.setSupplyOrg(0);
            }
            List<CfInfoSupplyAction> supplyActions = cfSupplyActionBiz.listCaseIdByReasonAndOrg(p.getSupplyReasonId(), p.getSupplyOrg());
            List<Integer> caseIds = supplyActions.stream().map(CfInfoSupplyAction::getCaseId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(caseIds)) {
                caseIds.add(p.getCaseId());
            }
        }

        Response<PageResult<WorkOrderVO>> response = ugcWorkOrderClient.ugcOrderlist(p);

        if (response == null || response.notOk() || response.getData() == null) {
            return response;
        }

        PageResult<WorkOrderVO> pageResult = response.getData();

        List<WorkOrderVO> list = pageResult.getPageList();

        if (CollectionUtils.isEmpty(list)) {
            return response;
        }

        Map<Long, CfInfoSupplyProgress> supplyProgressMap = cfSupplyProgressService.getSupplyProgressList(list, p);

        List<UgcWorkOrderVO> ugcWorkOrderVOList = list.stream()
                .map(r -> {
                    UgcWorkOrderVO vo = new UgcWorkOrderVO();
                    BeanUtils.copyProperties(r, vo);
                    CfInfoSupplyProgress cfInfoSupplyProgress = supplyProgressMap.get(r.getSupplyProgressId());

                    if (cfInfoSupplyProgress != null) {
                        vo.setContent(cfInfoSupplyProgress.getContent());
                        vo.setAttachmentUrls(cfInfoSupplyProgress.getImgUrls());
                        Set<String> sensitiveWords = riskDelegate.getHitWords(cfInfoSupplyProgress.getContent());
                        vo.setSensitiveWord(Joiner.on(",").join(sensitiveWords));
                        CfInfoSupplyAction supplyAction = cfSupplyActionBiz.getById(cfInfoSupplyProgress.getProgressActionId());
                        if (supplyAction != null) {
                            vo.setSupplyOrgName(supplyAction.getSupplyOrgName());
                            List<Integer> supplyReasonIds = Splitter.on(',')
                                    .splitToList(supplyAction.getSupplyReason())
                                    .stream().map(Integer::valueOf)
                                    .collect(Collectors.toList());
                            if (p.getSupplyReasonId() > 0 && !supplyReasonIds.contains(p.getSupplyReasonId())) {
                                return null;
                            }
                            if (p.getSupplyOrg() > 0 && supplyAction.getOrgForSearch() != p.getSupplyOrg()) {
                                return null;
                            }
                            List<String> supplyReasons = seaRejectService.listRejectByIds(supplyReasonIds)
                                    .stream()
                                    .map(SupplyProgressReasonItem::getDescribe)
                                    .collect(Collectors.toList());
                            vo.setSupplyReasons(supplyReasons);
                        }
                    }
                    return vo;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        PageResult<UgcWorkOrderVO> result = new PageResult<>();
        result.setHasNext(pageResult.isHasNext());
        result.setPageList(ugcWorkOrderVOList);

        return NewResponseUtil.makeSuccess(result);
    }

    @RequiresPermission("ugc-work-order:supply-progress-detail")
    @ApiOperation("下发动态详情,进入操作页面")
    @PostMapping("/supply/progress/detail")
    public Response<CfSupplyProgressWorkDetailVo> supplyProgressDetail(@RequestParam("caseId") int caseId,
                                                                       @RequestParam("workOrderId") int workOrderId,
                                                                       @RequestParam("supplyProgressId") long supplyProgressId) {
        return cfSupplyProgressService.viewWorkDetail(caseId, workOrderId, supplyProgressId, CfSupplyProgressService.ViewPageSource.my_work_order);
    }

    @RequiresPermission("ugc-work-order:supply-progress-history")
    @ApiOperation("下发动态详情历史纪录")
    @PostMapping("/supply/progress/history")
    public Response<CfSupplyProgressWorkDetailVo> supplyProgressHistory(@RequestParam("caseId") int caseId,
                                                                        @RequestParam("workOrderId") int workOrderId,
                                                                        @RequestParam("supplyProgressId") long supplyProgressId) {
        return cfSupplyProgressService.viewWorkDetail(caseId, workOrderId, supplyProgressId, CfSupplyProgressService.ViewPageSource.work_order_list);
    }

    @RequiresPermission("ugc-work-order:supply-progress-check")
    @ApiOperation("校验是否能对下发动态工单操作,true表示未通过,false通过")
    @PostMapping("/supply/progress/check")
    public Response<Boolean> supplyProgressCheck(@RequestParam("caseId") int caseId,
                                                 @RequestParam(value = "supplyProgressId", defaultValue = "0", required = false) long supplyProgressId,
                                                 @RequestParam(value = "actionId", defaultValue = "0", required = false) long actionId,
                                                 @RequestParam("workOrderId") long workOrderId,
                                                 @RequestParam("userId") int userId) {

        if (supplyProgressId == 0 && actionId == 0) {
            return NewResponseUtil.makeSuccess(true);
        }
        if (supplyProgressId > 0) {
            boolean supplyMessageCheck = cfSupplyProgressService.checkCanHandle(supplyProgressId);
            if (!supplyMessageCheck) {
                log.warn("supplyProgressCheck校验下发信息错误,caseId:{},supplyProgressId:{}", caseId, supplyProgressId);
                return NewResponseUtil.makeSuccess(true);
            }
        }

        if (actionId > 0) {
            CfInfoSupplyAction action = cfSupplyActionBiz.getById(actionId);
            if (action == null || (action.getHandleStatus() != CfInfoSupplyAction.SupplyHandleStatus.wait_audit.getCode()
                    && action.getHandleStatus() != CfInfoSupplyAction.SupplyHandleStatus.reject.getCode())) {
                log.info("supplyActionCheck null caseId={},actionId={},workOrderId={}", caseId, actionId, workOrderId);
                return NewResponseUtil.makeSuccess(true);
            }
        }

        return check(caseId, workOrderId, userId);
    }

    private Response<Boolean> check(int caseId, long workOrderId, int userId) {

        Response<WorkOrderVO> response = cfWorkClient.getWorkOrderById(workOrderId);
        if (response.notOk()) {
            log.warn("获取工单异常caseId:{},response:{}", caseId, response);
            return NewResponseUtil.makeSuccess(true);
        }

        WorkOrderVO workOrderVO = response.getData();
        //如果工单没有被分配   尝试分配工单   之后才可以进行处理
        if (workOrderVO.getHandleResult() == HandleResultEnum.undoing.getType()) {
            Response<Long> assignResponse = cfWorkClient.assignWorkOrder(workOrderVO.getWorkOrderId(), userId, userId);
            if (assignResponse.notOk()) {
                return NewResponseUtil.makeResponse(assignResponse.getCode(), assignResponse.getMsg(), null);
            }
        }

        if (workOrderVO.getHandleResult() == HandleResultEnum.audit_pass.getType()
                || workOrderVO.getHandleResult() == HandleResultEnum.audit_reject.getType()) {
            log.warn("查询工单状态非处理中response:{}", response);
            return NewResponseUtil.makeSuccess(true);
        }

        return NewResponseUtil.makeSuccess(false);
    }

    @RequiresPermission("ugc-work-order:supply-progress-pass")
    @ApiOperation("审核通过")
    @PostMapping("/supply/progress/pass")
    public Response<Boolean> supplyProgressPass(@RequestParam("param") String param) {
        SupplyProgressWorkHandleParam handleParam = JSON.parseObject(param, SupplyProgressWorkHandleParam.class);//已检查过
        if (handleParam.getCaseId() <= 0 || handleParam.getWorkOrderId() <= 0 || handleParam.getSupplyProgressId() <= 0
                || handleParam.getSupplyActionId() <= 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        int adminUserId = ContextUtil.getAdminUserId();
        //检查下发动态id是否和工单id匹配

        Response<Boolean> passResponse = cfSupplyProgressService.pass(handleParam, adminUserId);
        if (passResponse.notOk()) {
            return passResponse;
        }
        //处理新工单
        UgcHandleOrderParam ugcHandleOrderParam = new UgcHandleOrderParam();
        ugcHandleOrderParam.setWorkOrderIds(Lists.newArrayList((long) handleParam.getWorkOrderId()));
        ugcHandleOrderParam.setWorkOrderId(handleParam.getWorkOrderId());
        ugcHandleOrderParam.setOrderType(WorkOrderType.xiafaprogress.getType());
        ugcHandleOrderParam.setHandleResult(HandleResultEnum.audit_pass.getType());
        ugcHandleOrderParam.setUserId(adminUserId);
        ugcHandleOrderParam.setOperComment("");
        Response workResponse = ugcWorkOrderClient.handleUgc(ugcHandleOrderParam);
        if (workResponse.notOk()) {
            return NewResponseUtil.makeFail(workResponse.getMsg());
        }
        return NewResponseUtil.makeSuccess(true);
    }

    @RequiresPermission("ugc-work-order:supply-progress-reject")
    @ApiOperation("驳回")
    @PostMapping("/supply/progress/reject")
    public Response<Boolean> supplyProgressReject(@RequestParam("param") String param) {
        SupplyProgressWorkHandleParam handleParam = JSON.parseObject(param, SupplyProgressWorkHandleParam.class);//已检查过
        if (handleParam.getCaseId() <= 0 || handleParam.getWorkOrderId() <= 0 || handleParam.getSupplyProgressId() <= 0
                || handleParam.getSupplyActionId() <= 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (handleParam.getContentHandleStatus() == 1 && handleParam.getImgUrlsHandleStatus() == 1) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        handleParam.setComment(Optional.ofNullable(handleParam.getComment()).orElse(""));
        if (handleParam.getComment().length() > 300) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        int adminUserId = ContextUtil.getAdminUserId();
        //检查下发动态id是否和工单id匹配
        Response<Boolean> rejectResponse = cfSupplyProgressService.reject(handleParam, adminUserId);
        if (rejectResponse.notOk()) {
            return rejectResponse;
        }
        //处理新工单
        UgcHandleOrderParam ugcHandleOrderParam = new UgcHandleOrderParam();
        ugcHandleOrderParam.setWorkOrderId(handleParam.getWorkOrderId());
        ugcHandleOrderParam.setOrderType(WorkOrderType.xiafaprogress.getType());
        ugcHandleOrderParam.setHandleResult(HandleResultEnum.audit_reject.getType());
        ugcHandleOrderParam.setUserId(adminUserId);
        ugcHandleOrderParam.setOperComment(handleParam.getComment());
        Response workResponse = ugcWorkOrderClient.handleUgc(ugcHandleOrderParam);
        if (workResponse.notOk()) {
            return NewResponseUtil.makeFail(workResponse.getMsg());
        }
        return NewResponseUtil.makeSuccess(true);
    }

    @RequiresPermission("ugc-work-order:supply-progress-worklist")
    @ApiOperation("下发动态列表")
    @PostMapping("/supply/progress/worklist")
    public Response<List<CfInfoSupplyAction>> supplyProgressWorklist(@RequestParam("caseId") int caseId) {

        List<CfInfoSupplyAction> result = Lists.newArrayList();

        List<Integer> l = HandleResultEnum.unDoResult();
        l.add(HandleResultEnum.audit_pass.getType());
        l.add(HandleResultEnum.audit_reject.getType());
        Response<List<WorkOrderVO>> response = cfWorkClient.listByCaseIdAndTypeAndResult(caseId, Lists.newArrayList(WorkOrderType.xiafaprogress.getType()), l);

        List<WorkOrderVO> list = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(Lists.newArrayList());

        if (CollectionUtils.isEmpty(list)) {
            return NewResponseUtil.makeSuccess(result);
        }

        result = cfSupplyProgressService.getByWorkOrder(list);

        return NewResponseUtil.makeSuccess(result);
    }

}
