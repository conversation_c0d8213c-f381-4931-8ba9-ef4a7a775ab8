package com.shuidihuzhu.cf.admin.controller.api.innerapi;

import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.service.credit.CfIncreaseCreditService;
import com.shuidihuzhu.client.cf.admin.client.CfIncreaseCreditFeignClient;
import com.shuidihuzhu.client.cf.admin.model.CfCreditInfo;
import com.shuidihuzhu.client.cf.admin.model.CfUserIdentity;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Description: 查询增信
 * @Author: panghairui
 * @Date: 2023/3/14 5:46 PM
 */
@Slf4j
@RestController
public class CfIncreaseCreditFeignController implements CfIncreaseCreditFeignClient {

    @Resource
    private CfIncreaseCreditService cfIncreaseCreditService;

    @Override
    public Response<CfCreditInfo> requestCreditInfo(CfUserIdentity cfUserIdentity) {

        if (Objects.isNull(cfUserIdentity)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        log.info("CfIncreaseCreditFeignController requestCreditInfo cfUserIdentity:{}", JSONObject.toJSONString(cfUserIdentity));
        CfCreditInfo cfCreditInfo = null;
        try {
            cfCreditInfo = cfIncreaseCreditService.queryCreditInfo(cfUserIdentity);
        } catch (Exception e) {
            log.error("CfIncreaseCreditFeignController requestCreditInfo error ", e);
        }
        return NewResponseUtil.makeSuccess(cfCreditInfo);
    }
}
