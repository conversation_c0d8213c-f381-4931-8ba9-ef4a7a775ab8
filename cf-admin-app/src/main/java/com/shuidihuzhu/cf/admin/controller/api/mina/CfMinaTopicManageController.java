package com.shuidihuzhu.cf.admin.controller.api.mina;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.mina.AdminCfKeywordPhaseRelationBiz;
import com.shuidihuzhu.cf.biz.mina.*;
import com.shuidihuzhu.cf.delegate.other.IMiniAppDelegate;
import com.shuidihuzhu.cf.delegate.ugc.IUgcDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.vo.mina.*;
import com.shuidihuzhu.cf.enums.CfErrorCode;

import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.cf.model.miniprogram.*;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.admin.PageUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping(path = "/admin/cf/mina/topic")
public class CfMinaTopicManageController {
    @Autowired
    private IUgcDelegate ugcDelegate;
    @Autowired
    private AdminTopicBiz adminTopicBiz;
    @Autowired
    private AdminCfPublishPhaseBiz adminCfPublishPhaseBiz;
    @Autowired
    private IMiniAppDelegate miniAppDelegate;
    @Autowired
    private AdminTopicCountBiz adminTopicCountBiz;
    @Autowired
    private AdminKeywordBiz adminKeywordBiz;
    @Autowired
    private AdminCfTopicCommentBiz adminCfTopicCommentBiz;
    @Autowired
    private AdminCfKeywordPhaseRelationBiz adminCfKeywordPhaseRelationBiz;

    private static final int COMMENT_TOP = 1;
    private static final int SORTED_BY_COMMENT_NUM = 1;
    private static final int SORTED_BY_PRAISED_NUM = 2;
    private static Logger LOGGER = LoggerFactory.getLogger(CfMinaTopicManageController.class);

    @RequiresPermission("topic-manage:get-topics")
    @RequestMapping(path = "/get-topics", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    public Response getTopicList(int current, int pageSize,
                                 @RequestParam(required = false) Integer sortMode,
                                 @RequestParam(required = false) String title) {
        if (current < 0 || pageSize < 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        LOGGER.info("current:{}, pageSize:{}, sortMode:{}, title:{}", current, pageSize, sortMode, title);

        Timestamp now = DateUtil.nowTime();
        List<CfPublishPhase> cfPublishPhases = miniAppDelegate.listCfPublishPhaseByTimeLimit(now, Integer.MAX_VALUE);
        if (CollectionUtils.isEmpty(cfPublishPhases) || cfPublishPhases.get(0).getPhaseId() == 0) {
            LOGGER.info("have no phase now:{}", DateUtil.getCurrentDateTimeStr());
            return NewResponseUtil.makeError(CfErrorCode.HAVE_NO_PHASE_TOPIC);
        }

        //int phaseId = cfPublishPhase.get(0).getPhaseId();
        List<Integer> phaseIds = cfPublishPhases.stream().map(CfPublishPhase::getPhaseId).collect(Collectors.toList());

        Map<Integer, Integer> praiseMap = Maps.newHashMap();
        Map<Integer, Integer> commentMap = Maps.newHashMap();
        Map<Integer, Integer> shareMap = Maps.newHashMap();
        Map<Integer, Timestamp> publishTimeMap;
        Map<Integer, Integer> commentsTopMap;
        List<CfMinaTopicVo> topicList;
        List<CfTopic> topics;
        if (sortMode != null) {

            //获取topicId
            List<Integer> cfTopicIds = Lists.newArrayList();
            if (StringUtils.isBlank(title)) {
                List<CfTopic> cfTopicPublish = adminTopicBiz.getPublishTopics(title);
                if (CollectionUtils.isEmpty(cfPublishPhases)) {
                    return NewResponseUtil.makeError(CfErrorCode.TOPIC_NOT_EXIT);
                }
                cfTopicIds = cfTopicPublish.stream().map(CfTopic::getId).collect(Collectors.toList());
            } else {
                topics = adminTopicBiz.getByTitle(phaseIds, title, current, pageSize);
                cfTopicIds = topics.stream().map(CfTopic::getId).collect(Collectors.toList());
            }

            List<CfTopicShareCommentCount> topicShareCommentCounts =
                    adminTopicCountBiz.listTopicCountOrderPage(current, pageSize, sortMode, cfTopicIds);

            List<Integer> topicIds = Lists.newArrayList();
            topicShareCommentCounts.forEach(v -> {
                praiseMap.put(v.getTopicId(), v.getPraiseCount());
                commentMap.put(v.getTopicId(), v.getCommentCount());
                shareMap.put(v.getTopicId(), v.getShareCount());
                topicIds.add(v.getTopicId());
            });

            topics = miniAppDelegate.listCfTopicByIds(topicIds);

            if (CollectionUtils.isEmpty(topics)) {
                LOGGER.info("have no topic phaseIds:{}", phaseIds.toString());
                return NewResponseUtil.makeError(CfErrorCode.TOPIC_NOT_EXIT);
            }

            publishTimeMap = getPublishTimeMap(topics);
            commentsTopMap = getCommentsTopMap(topicIds);

            topicList = getTopicList(topics, shareMap, commentMap, praiseMap, commentsTopMap, publishTimeMap);

            if (sortMode == SORTED_BY_COMMENT_NUM) {
                topicList = topicList.stream()
                        .sorted(Comparator.comparing(CfMinaTopicVo :: getCommentNum).reversed())
                        .collect(Collectors.toList());
            } else if (sortMode == SORTED_BY_PRAISED_NUM) {
                topicList = topicList.stream()
                        .sorted(Comparator.comparing(CfMinaTopicVo :: getPraiseNum).reversed())
                        .collect(Collectors.toList());
            }

            Map<String, Object> result = Maps.newHashMap();
            result.put("pagination", PageUtil.transform2PageMap(topicShareCommentCounts));
            result.put("data", topicList);
            return NewResponseUtil.makeSuccess(result);
        } else {
            topics = adminTopicBiz.getByTitle(phaseIds, title, current, pageSize);
            if (CollectionUtils.isEmpty(topics)) {
                LOGGER.info("have no topic phaseIds:{}", phaseIds.toString());
                return NewResponseUtil.makeError(CfErrorCode.TOPIC_NOT_EXIT);
            }

            List<Integer> topicIds = Lists.newArrayList();
            topics.forEach(s -> topicIds.add(s.getId()));
            List<CfTopicShareCommentCount> shareCommentCounts = ugcDelegate.listByTopicIds(topicIds);
            shareCommentCounts.forEach(v -> {
                praiseMap.put(v.getTopicId(), v.getPraiseCount());
                commentMap.put(v.getTopicId(), v.getCommentCount());
                shareMap.put(v.getTopicId(), v.getShareCount());
            });

            publishTimeMap = getPublishTimeMap(topics);
            commentsTopMap = getCommentsTopMap(topicIds);
            topicList = getTopicList(topics, shareMap, commentMap, praiseMap, commentsTopMap, publishTimeMap);

            Map<String, Object> result = Maps.newHashMap();
            result.put("pagination", PageUtil.transform2PageMap(topics));
            result.put("data", topicList);
            return NewResponseUtil.makeSuccess(result);
        }
    }

    @RequiresPermission("topic-manage:topics-detail")
    @RequestMapping(path = "/topics-detail", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    public Response getTopicDetailList(int current, int pageSize, @RequestParam(required = false) String title) {
        if (current < 0 || pageSize < 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        //获取已发布的话题详情
        List<CfMinaTopicDetailVo> cfMinaTopicDetailVos = adminTopicBiz.getTopicsPage(current, pageSize, title);
        if (CollectionUtils.isEmpty(cfMinaTopicDetailVos)) {
            return NewResponseUtil.makeError(CfErrorCode.TOPIC_NOT_EXIT);
        }


        Set<Integer> phaseIds = Sets.newHashSet();
        cfMinaTopicDetailVos.forEach(v -> phaseIds.add(v.getPhaseId()));
        LOGGER.info("phaseIds:{}", phaseIds);

        Map<Integer, Timestamp> publishTimeMap = Maps.newHashMap();
        Map<Integer, Integer> publishStatusMap = Maps.newHashMap();
        List<CfPublishPhase> cfPublishPhases = miniAppDelegate.listCfPublishPhaseByPhaseIds(Lists.newArrayList(phaseIds));
        cfPublishPhases.forEach(v -> {
            publishStatusMap.put(v.getPhaseId(), v.getPublishStatus());
            publishTimeMap.put(v.getPhaseId(), v.getPublishTime());
        });
        cfMinaTopicDetailVos.forEach(v -> {
            v.setPublishTime(publishTimeMap.getOrDefault(v.getPhaseId(), null));
            v.setPublishStatus(publishStatusMap.getOrDefault(v.getPhaseId(), 0));
        });

        Map<String, Object> result = Maps.newHashMap();
        result.put("pagination", PageUtil.transform2PageMap(cfMinaTopicDetailVos));
        result.put("data", cfMinaTopicDetailVos);
        return NewResponseUtil.makeSuccess(result);
    }

    @RequiresPermission("topic-manage:topic-info")
    @RequestMapping(path = "/topic-info", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    public Response getTopicInfo(int topicId) {
        if (topicId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        CfTopic cfTopic = miniAppDelegate.selectCfTopicById(topicId);
        Map<String, String> topicMap = Maps.newHashMap();
        topicMap.put("title", cfTopic.getTitle());
        topicMap.put("description", cfTopic.getDescription());
        topicMap.put("imgUrl", cfTopic.getIcon());

        return NewResponseUtil.makeSuccess(topicMap);
    }

    @RequiresPermission("topic-manage:add-or-update-topic")
    @RequestMapping(path = "/add-or-update-topic", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    public Response addOrUpdateTopic(String title,
                                     @RequestParam(defaultValue = "") String description,
                                     String imgUrl,
                                     @RequestParam(value = "topicId", required = false) Integer topicId,
                                     @RequestParam(defaultValue = "", required = false) String imgUrls ) {
        if (StringUtils.isEmpty(title)) {
            return NewResponseUtil.makeError(AdminErrorCode.EMPTY_TOPIC_TITLE);
        }
        if (StringUtils.isEmpty(imgUrl)) {
            return NewResponseUtil.makeError(AdminErrorCode.EMPTY_TOPIC_IMGURL);
        }
        if (topicId != null && topicId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        if (topicId != null) {
            CfTopic cfTopic = miniAppDelegate.selectCfTopicById(topicId);
            if (cfTopic == null) {
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
            }
            cfTopic.setTitle(title);
            cfTopic.setDescription(description);
            cfTopic.setIcon(imgUrl);
            cfTopic.setImgUrls(imgUrls);
            miniAppDelegate.updateCfTopicById(topicId, cfTopic);
        } else {
            CfTopic cfTopic = new CfTopic();
            cfTopic.setTitle(title);
            cfTopic.setDescription(description);
            cfTopic.setIcon(imgUrl);
            cfTopic.setImgUrls(imgUrls);
            adminTopicBiz.insertOne(cfTopic);
            CfTopicShareCommentCount cfTopicShareCommentCount =
                    new CfTopicShareCommentCount(cfTopic.getId(), 0, 0, 0);
            ugcDelegate.insertCfTopicShareCommentCount(cfTopicShareCommentCount);
        }

        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("topic-manage:delete-topic")
    @RequestMapping(path = "/delete-topic", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    public Response deleteTopic(int topicId) {
        if (topicId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        //获取话题
        CfTopic cfTopic = miniAppDelegate.selectCfTopicById(topicId);
        if (cfTopic == null){
            return NewResponseUtil.makeError(CfErrorCode.TOPIC_NOT_EXIT);
        }
        //删除话题
        miniAppDelegate.deleteCfTopicById(topicId);
        //删除话题相关记录
        adminTopicCountBiz.deleteByTopicId(topicId);
        //删除话题相关的评论
        adminCfTopicCommentBiz.deleteByTopicId(topicId);

        //获取发版消息 若该期数下话题为空  则删除
        List<CfTopic> cfTopics = adminTopicBiz.getByTitle(
                Lists.newArrayList(cfTopic.getPhaseId()),null,0,1);
        if (CollectionUtils.isEmpty(cfTopics)){
            LOGGER.info("delete cfPublishPhase id:{}", cfTopic.getPhaseId());
            adminCfPublishPhaseBiz.delete(cfTopic.getPhaseId());
        }
        return NewResponseUtil.makeSuccess("删除成功");
    }

    //设置发布话题列表
    @RequiresPermission("topic-manage:list-phase")
    @RequestMapping(path = "/list-phase", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    public Response getPhaseList(int current, int pageSize) {
        if (current < 0 || pageSize < 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        //更新发布状态
        adminCfPublishPhaseBiz.updatePhaseStatusByTime(DateUtil.nowTime());
        List<CfPublishPhase> cfPublishPhases = miniAppDelegate.getCfPublishPhaseListPage(current, pageSize);
        if (CollectionUtils.isEmpty(cfPublishPhases)) {
            LOGGER.info("have no publish topic");
            return NewResponseUtil.makeSuccess(Collections.EMPTY_LIST);
        }
        List<Integer> phaseIds = Lists.newArrayList();
        Set<Integer> keywordIds = Sets.newHashSet();
        cfPublishPhases.forEach(v -> {
            phaseIds.add(v.getPhaseId());
            keywordIds.add(v.getKeywordId());
        });

        //获取关键字
        Map<Integer, String> keywordMap =
                miniAppDelegate.listCfTopicKeywordListByIds(Lists.newArrayList(keywordIds))
                        .stream().collect(Collectors.toMap(v -> v.getId(), v -> v.getKeyword()));
        Map<Integer, String> phaseIdKeywordMap = Maps.newHashMap();
        cfPublishPhases.forEach(v -> phaseIdKeywordMap.put(v.getPhaseId(), keywordMap.get(v.getKeywordId())));

        //获取话题
        LOGGER.info("phaseIds:{}", phaseIds);
        List<CfMinaTopicDetailVo> topics = adminTopicBiz.listByPhaseIds(phaseIds);
        if (CollectionUtils.isEmpty(topics)) {
            LOGGER.info("phaseIds 【{}】 have no topics", phaseIds);
            return NewResponseUtil.makeSuccess(Collections.EMPTY_LIST);
        }

        Map<Integer, String> phaseIdTitleMap = getPhraseIdTitleMap(topics);
        Map<Integer, java.util.Date> phaseIdCreateTimeMap = getPhraseIdTimeMap(topics);

        List<CfMinaPublishPhaseVo> cfMinaPublishPhaseVos = cfPublishPhases.stream().map(v -> {
            java.util.Date publishTime = v.getPublishTime();
            String title = phaseIdTitleMap.getOrDefault(v.getPhaseId(), "");
            java.util.Date createTime = phaseIdCreateTimeMap.get(v.getPhaseId());
            String keyword = phaseIdKeywordMap.getOrDefault(v.getPhaseId(), "");
            return new CfMinaPublishPhaseVo(v.getPhaseId(), v.getPublishStatus(), keyword, title, createTime, publishTime);
        }).collect(Collectors.toList());

        Map<String, Object> result = Maps.newHashMap();
        result.put("pagination", PageUtil.transform2PageMap(cfPublishPhases));
        result.put("data", cfMinaPublishPhaseVos);
        return NewResponseUtil.makeSuccess(result);
    }

    @RequiresPermission("topic-manage:title-list")
    @RequestMapping(path = "/title-list", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    public Response getTopicTitleList(int current, int pageSize, @RequestParam(required = false) String title) {
        if (current <= 0 || pageSize <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<CfMinaTopicDetailVo> cfMinaTopicDetailVos = adminTopicBiz.getUnPublishTopics(current, pageSize, title);
        List<CfMinaTopicTitleVo> topicTitles = cfMinaTopicDetailVos.stream()
                                                                   .map(v -> new CfMinaTopicTitleVo(v.getId(), v.getTitle()))
                                                                   .collect(Collectors.toList());

        Map<String, Object> result = Maps.newHashMap();
        result.put("pagination", PageUtil.transform2PageMap(cfMinaTopicDetailVos));
        result.put("data", topicTitles);
        return NewResponseUtil.makeSuccess(result);
    }

    @RequiresPermission("topic-manage:phase-detail")
    @RequestMapping(path = "/phase-detail", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    public Response getPhaseDetail(int phaseId) {
        if (phaseId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        CfPublishPhase cfPublishPhase = miniAppDelegate.selectCfPublishPhaseByPhaseId(phaseId);
        if (cfPublishPhase == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        LOGGER.info("cfPublishPhase:{}", cfPublishPhase);
        Timestamp publishTime = cfPublishPhase.getPublishTime();
        CfTopicKeyword cfTopicKeyword = miniAppDelegate.selectCfTopicKeywordById(cfPublishPhase.getKeywordId());
        String keyword = cfTopicKeyword.getKeyword();
        List<CfMinaTopicTitleVo> cfMinaTopicTitleVos =
                miniAppDelegate.selectCfTopicByPhaseId(phaseId).stream()
                        .map(v -> new CfMinaTopicTitleVo(v.getId(), v.getTitle()))
                        .collect(Collectors.toList());

        Map<String, Object> phaseMap = Maps.newHashMap();
        phaseMap.put("publishTime", publishTime);
        phaseMap.put("keyword", keyword);
        phaseMap.put("topicIdTitle", cfMinaTopicTitleVos);
        return NewResponseUtil.makeSuccess(phaseMap);
    }

    @RequiresPermission("topic-manage:delete-phase")
    @RequestMapping(path = "/delete-phase", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    public Response deletePhase(int phaseId) {
        if (phaseId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        LOGGER.info("CfMinaTopicManageController deletePhase phase:{}", phaseId);
        // 将该期对应的话题phaseId 重置
        adminTopicBiz.deleteByPhaseId(phaseId);
        adminCfPublishPhaseBiz.delete(phaseId);
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("topic-manage:add-or-update-phase")
    @RequestMapping(path = "/add-or-update-phase", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    public Response addOrUpdatePhase(String keyword, Long date, Integer[] topicIds,
                                     @RequestParam(required = false) Integer phaseId) {

        LOGGER.info("keyword:{}, date:{}, topicIds:{}, phaseIs:{}", keyword, date, topicIds, phaseId);
        if (phaseId != null && phaseId < 1) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (StringUtils.isEmpty(keyword)) {
            return NewResponseUtil.makeError(AdminErrorCode.EMPTY_TOPIC_KEYWORD);
        }
        if (date == null) {
            return NewResponseUtil.makeError(AdminErrorCode.EMPTY_PHASE_DATE);
        }
        if (topicIds.length == 0) {
            return NewResponseUtil.makeError(AdminErrorCode.EMPTY_PHASE_TOPIC_ID);
        }

        Timestamp publishTime = new Timestamp(date);
        List<Integer> cfTopicIds = Lists.newArrayList(topicIds);
        int keywordId = getKeywordId(keyword);
        if (phaseId != null) {
            //删除话题之后  需要将该话题的发布  首先移除
            List<CfTopic> cfTopics = miniAppDelegate.selectCfTopicByPhaseId(phaseId);
            if (CollectionUtils.isNotEmpty(cfTopicIds)){
                List<Integer> beforeIds = cfTopics.stream().map(CfTopic::getId).collect(Collectors.toList());
                //首先移除不需要更新的话题
                //将原来的cfTopicIds 做一个复制体 然后再对 cfTopicIds进行操作
                List<Integer> cfTopicIdsCopy = Lists.newArrayList(topicIds);
                cfTopicIds.removeAll(beforeIds);
                //将删除的话题  phaseId置为0 由于cfTopicIds已经进行了操作  因此使用cfTopicIdsCopy
                beforeIds.removeAll(cfTopicIdsCopy);
                if (CollectionUtils.isNotEmpty(beforeIds)){
                    adminTopicBiz.deletePhaseById(beforeIds);
                }
            }
            //更新phase
            updatePhaseId(phaseId, cfTopicIds);

            //获取发布消息
            CfPublishPhase cfPublishPhase = miniAppDelegate.selectCfPublishPhaseByPhaseId(phaseId);
            LOGGER.info("cfPublishPhase:{}", cfPublishPhase);
            if (cfPublishPhase == null) {
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
            }
            if (cfPublishPhase.getKeywordId() != keywordId) {
                CfKeywordPhaseRelation cfKeywordPhaseRelation = miniAppDelegate.selectCfKeywordPhaseRelationByPhaseId(phaseId);
                cfKeywordPhaseRelation.setKeywordId(keywordId);
                //更新关键词 发布映射表
                adminCfKeywordPhaseRelationBiz.updateByPhaseId(phaseId, cfKeywordPhaseRelation);
            }
            cfPublishPhase.setKeywordId(keywordId);
            cfPublishPhase.setPublishTime(publishTime);
            adminCfPublishPhaseBiz.updateByPhaseId(phaseId, cfPublishPhase);
        } else {
            int phaseIdNext = 1;
            CfPublishPhase cfPublishPhase = adminCfPublishPhaseBiz.getLatestPhase();
            if (cfPublishPhase != null) {
                phaseIdNext = cfPublishPhase.getPhaseId() + 1;
            }

            CfPublishPhase cfPublishPhaseNew = new CfPublishPhase(keywordId, phaseIdNext, publishTime);
            miniAppDelegate.insertCfPublishPhase(cfPublishPhaseNew);
            CfKeywordPhaseRelation cfKeywordPhaseRelation = new CfKeywordPhaseRelation(keywordId, phaseIdNext, 0, 0);
            miniAppDelegate.addCfKeywordPhaseRelation(cfKeywordPhaseRelation);
            for (Integer topicId : topicIds) {
                adminTopicBiz.updatePhaseId(topicId, phaseIdNext);
            }
        }
        return NewResponseUtil.makeSuccess(null);
    }

    private Map<Integer, Timestamp> getPublishTimeMap(List<CfTopic> cfTopics) {
        Set<Integer> phaseIds = Sets.newHashSet();
        cfTopics.forEach(s -> phaseIds.add(s.getPhaseId()));
        Map<Integer, Timestamp> publishTimeMap =
                miniAppDelegate.listCfPublishPhaseByPhaseIds(Lists.newArrayList(phaseIds)).stream()
                        .collect(Collectors.toMap(CfPublishPhase::getPhaseId, CfPublishPhase::getPublishTime));
        return publishTimeMap;
    }

    private void updatePhaseId(int phaseId, List<Integer> topicIds) {
        List<CfTopic> cfTopics = miniAppDelegate.selectCfTopicByPhaseId(phaseId);
        if (CollectionUtils.isEmpty(cfTopics)) {
            LOGGER.info("phase:{} have no topic before");
            for (Integer topicId : topicIds) {
                adminTopicBiz.updatePhaseId(topicId, phaseId);
            }
        } else {
            List<Integer> topicIdsBefore = cfTopics.stream().map(v -> v.getId()).collect(Collectors.toList());
            List<Integer> topicIdAllHave = Lists.newArrayList();
            topicIdsBefore.forEach(v -> {
                if (topicIds.contains(v)) {
                    topicIdAllHave.add(v);
                }
            });
            topicIdsBefore.removeAll(topicIdAllHave);
            topicIds.removeAll(topicIdAllHave);
            for (Integer topicId : topicIdsBefore) {
                adminTopicBiz.updatePhaseId(topicId, 0);
            }
            for (Integer topicId : topicIds) {
                adminTopicBiz.updatePhaseId(topicId, phaseId);
            }
        }
    }

    private int getKeywordId(String keyword) {
        int keywordId;
        CfTopicKeyword cfTopicKeyword = miniAppDelegate.selectCfTopicKeywordByKeyword(keyword);
        if (cfTopicKeyword != null) {
            keywordId = cfTopicKeyword.getId();
        } else {
            AdminKeyword adminKeyword = new AdminKeyword(keyword);
            adminKeywordBiz.addOne(adminKeyword);
            keywordId = adminKeyword.getId();
        }
        LOGGER.info("keywordId:{}", keywordId);
        return keywordId;
    }

    //获取话题标题Map
    private Map<Integer,String> getPhraseIdTitleMap(List<CfMinaTopicDetailVo> topics) {
        Map<Integer, String> phaseIdTitleMap = Maps.newHashMap();
        topics.forEach(v -> {
            //生成话题标题Map
            if (phaseIdTitleMap.get(v.getPhaseId()) == null) {
                phaseIdTitleMap.put(v.getPhaseId(), v.getTitle());
            } else {
                String titles = phaseIdTitleMap.get(v.getPhaseId()) + "\n" + v.getTitle();
                phaseIdTitleMap.put(v.getPhaseId(), titles);
            }
        });
        return phaseIdTitleMap;
    }

    private Map<Integer, java.util.Date> getPhraseIdTimeMap(List<CfMinaTopicDetailVo> topics){
        Map<Integer, java.util.Date> phaseIdCreateTimeMap = Maps.newHashMap();
        topics.forEach(v -> {
            //生成话题时间Map
            if (phaseIdCreateTimeMap.get(v.getPhaseId()) == null) {
                phaseIdCreateTimeMap.put(v.getPhaseId(), v.getCreateTime());
            }
        });
        return phaseIdCreateTimeMap;
    }

    private Map<Integer, Integer> getCommentsTopMap(List<Integer> topicIds) {
        Map<Integer, Integer> commentsTopMap = Maps.newHashMap();

        List<CfCommentDynamic> cfCommentDynamics = ugcDelegate.listByIdsAndStatus(topicIds, COMMENT_TOP);
        cfCommentDynamics.forEach(v -> {
            if (commentsTopMap.get(v.getTopicId()) == null) {
                commentsTopMap.put(v.getTopicId(), 1);
            } else {
                commentsTopMap.put(v.getTopicId(), commentsTopMap.get(v.getTopicId()) + 1);
            }
        });
        return commentsTopMap;
    }

    private List<CfMinaTopicVo> getTopicList(List<CfTopic> topics, Map<Integer, Integer> shareMap,
                                             Map<Integer, Integer> commentMap, Map<Integer, Integer> praiseMap,
                                             Map<Integer, Integer> commentsTopMap, Map<Integer, Timestamp> publishTimeMap) {
        List<CfMinaTopicVo> topicList = Lists.newArrayList();
        for (CfTopic cfTopic : topics) {
            Integer shareNum = praiseMap.get(cfTopic.getId());
            Integer praiseNum = praiseMap.get(cfTopic.getId());
            Integer commentNum = commentMap.get(cfTopic.getId());
            Integer topCommentNum = commentsTopMap.get(cfTopic.getId());
            CfMinaTopicVo cfMinaTopicVo = new CfMinaTopicVo(cfTopic.getId(),
                    shareNum == null ? 0 : shareNum,
                    commentNum == null ? 0 : commentNum,
                    praiseNum == null ? 0 : praiseNum,
                    topCommentNum == null ? 0 : topCommentNum,
                    cfTopic.getTitle(),
                    publishTimeMap.get(cfTopic.getPhaseId()));
            topicList.add(cfMinaTopicVo);
        }
        return topicList;
    }
}
