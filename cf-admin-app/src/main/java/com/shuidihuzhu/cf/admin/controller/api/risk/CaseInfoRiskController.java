package com.shuidihuzhu.cf.admin.controller.api.risk;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.ugc.model.domain.risk.CfBaseInfoRiskHitVO;
import com.shuidihuzhu.cf.client.ugc.service.RiskControlWordManageClient;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.domain.CaseRaiseRiskDO;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-04-04  12:11
 *
 * 案例图文命中 蔡博士AI敏感词 + 疾病 详情
 */
@Api("案例图文敏感词详情")
@RestController
@Slf4j
@RequestMapping("/admin/cf/risk/case-info")
public class CaseInfoRiskController {

    @Resource
    private IRiskDelegate riskDelegate;
    @Autowired
    private RiskControlWordManageClient riskControlWordManageClient;
    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;

    @RequiresPermission("risk-case-info:get")
    @PostMapping("get-risk-info")
    @ApiOperation("获取命中详情")
    public Response info(String infoUuid){
        if (StringUtils.isBlank(infoUuid)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CaseRaiseRiskDO data = riskDelegate.getByInfoUuid(infoUuid);
        return ResponseUtil.makeSuccess(data);
    }


    @RequiresPermission("risk-case-info:get-v1")
    @PostMapping("get-risk-info-v1")
    @ApiOperation("获取命中详情")
    public Response<CfBaseInfoRiskHitVO> getBaseRiskInfo(String infoUuid){
        CrowdfundingInfo info = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (info == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        Response<List<CfBaseInfoRiskHitVO>> result = riskControlWordManageClient.selectHitVoByCaseIds(Lists.newArrayList(info.getId()));

        return ResponseUtil.makeSuccess(result != null && CollectionUtils.isNotEmpty(result.getData()) ?
                result.getData().get(0) : null);
    }

}
