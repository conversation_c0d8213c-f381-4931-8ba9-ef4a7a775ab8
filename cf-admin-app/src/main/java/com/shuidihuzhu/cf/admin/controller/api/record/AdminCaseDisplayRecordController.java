package com.shuidihuzhu.cf.admin.controller.api.record;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.client.adminpure.model.AdminCaseDisplayRecordVo;
import com.shuidihuzhu.cf.client.adminpure.model.CaseDisplaySetting;
import com.shuidihuzhu.cf.client.apipure.feign.CfCaseDisplaySettingFeignClient;
import com.shuidihuzhu.cf.client.apipure.model.crowdfunding.CaseDisplaySettingVo;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.service.record.AdminCaseDisplayRecordService;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.apache.kafka.clients.admin.Admin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping(path = "/admin/crowdfunding/case-display-record")
public class AdminCaseDisplayRecordController {
    @Autowired
    private AdminCaseDisplayRecordService adminCaseDisplayRecordService;

    @Autowired
    private CfCaseDisplaySettingFeignClient cfCaseDisplaySettingFeignClient;

    @Autowired
    private SeaAccountClientV1 seaAccountClient;

    @PostMapping("/show")
    @RequiresPermission("case-display-record:show")
    public Response<CaseDisplaySettingVo> show(@RequestParam("caseId") int caseId) {
        if (caseId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CaseDisplaySettingVo caseDisplaySettingVo = new CaseDisplaySettingVo();
        caseDisplaySettingVo.setCaseId(caseId);
        OperationResult<CaseDisplaySettingVo> response = cfCaseDisplaySettingFeignClient.getCaseDisplaySetting(caseDisplaySettingVo);
        if (!response.isSuccess()) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_OPERATION_FAILED);
        }
        return NewResponseUtil.makeSuccess(response.getData());
    }
    @PostMapping("/query")
    @RequiresPermission("case-display-record:query")
    public Response<List<AdminCaseDisplayRecordVo>> query(@RequestParam("caseId") int caseId) {
        if (caseId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        return NewResponseUtil.makeSuccess(adminCaseDisplayRecordService.searchRecordByCaseIdOrUuid("", caseId));
    }

    @PostMapping("/update")
    @RequiresPermission("case-display-record:update")
    public Response<Void> update(@RequestBody AdminCaseDisplayRecordVo adminCaseDisplayRecordVo) {
        if (adminCaseDisplayRecordVo.getCaseId() <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        long adminLongUserId = ContextUtil.getAdminLongUserId();
        if (adminLongUserId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_NOT_LOGIN_ERROR);
        }
        // 修改案例展示
        CaseDisplaySettingVo caseDisplaySettingVo = new CaseDisplaySettingVo();
        caseDisplaySettingVo.setCaseId(adminCaseDisplayRecordVo.getCaseId());
        caseDisplaySettingVo.setColor(adminCaseDisplayRecordVo.getAfter().getColor());
        caseDisplaySettingVo.setLanguage(adminCaseDisplayRecordVo.getAfter().getLanguage());
        caseDisplaySettingVo.setReason(adminCaseDisplayRecordVo.getReason());
        caseDisplaySettingVo.setUpdateChannel(CaseDisplaySettingVo.UPDATE_CHANNEL_TYPE.SEA.getType());
        caseDisplaySettingVo.setOperatorId(adminLongUserId);
        OperationResult<Void> operationResult = cfCaseDisplaySettingFeignClient.addOrUpdateCaseDisplaySetting(caseDisplaySettingVo);
        if (operationResult != null && operationResult.isSuccess()) {
            return NewResponseUtil.makeSuccess();
        }
        return NewResponseUtil.makeError(AdminErrorCode.OPERATION_FAILED);
    }
}
