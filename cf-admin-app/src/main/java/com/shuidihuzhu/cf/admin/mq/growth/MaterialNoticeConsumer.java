package com.shuidihuzhu.cf.admin.mq.growth;

import com.shuidihuzhu.cf.biz.crowdfunding.NoticeGrowthToolBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @DATE 2020/4/27
 */
@Slf4j
@Service
@RocketMQListener(id = "notice_growth_material",
                  tags = MQTagCons.CF_WORK_ORDER_APPROVE_LAUNCH,
                  group = "notice_growth_" + MQTagCons.CF_WORK_ORDER_APPROVE_LAUNCH,
                  topic = MQTopicCons.CF)
public class MaterialNoticeConsumer implements MessageListener<String> {

    @Autowired
    private NoticeGrowthToolBiz growthToolBiz;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<String> mqMessage) {

        String infoUuid = mqMessage.getPayload();

        growthToolBiz.notice(infoUuid);

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
