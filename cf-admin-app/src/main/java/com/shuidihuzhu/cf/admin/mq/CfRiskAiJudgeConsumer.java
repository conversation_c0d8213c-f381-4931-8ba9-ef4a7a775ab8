package com.shuidihuzhu.cf.admin.mq;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.ai.AiRiskBizEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.ai.CfRiskAiJudgeService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description: 大模型判断案例风险
 * @Author: panghairui
 * @Date: 2025/5/20 15:53
 */
@Slf4j
@Service
@RocketMQListener(id = "cf-ai-judge-" + MQTagCons.CF_OPERATION_MSG,
        group = "cf-ai-judge-group",
        tags = MQTagCons.CF_OPERATION_MSG,
        topic = MQTopicCons.CF)
public class CfRiskAiJudgeConsumer implements MessageListener<CfOperatingRecord> {

    @Resource
    private CfRiskAiJudgeService cfRiskAiJudgeService;

    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfOperatingRecord> mqMessage) {

        if (mqMessage == null || mqMessage.getPayload() == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        log.info("CfRiskAiJudgeConsumer receive message: {}", mqMessage.getPayload());
        CfOperatingRecord cfOperatingRecord = mqMessage.getPayload();

        CrowdfundingInfo crowdfundingInfo = getCrowdfundingInfo(cfOperatingRecord.getInfoUuid());
        if (crowdfundingInfo == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        cfRiskAiJudgeService.aiJudgeRisk(crowdfundingInfo, parseContent(crowdfundingInfo), AiRiskBizEnum.CF_CONTENT);

        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private String parseContent(CrowdfundingInfo info) {
        return new StringBuilder()
                .append("标题：").append(StringUtils.defaultString(info.getTitle())).append("\n")
                .append("文章：").append(StringUtils.defaultString(info.getContent())).append("\n")
                .toString();
    }

    /**
     * 获取众筹信息
     */
    private CrowdfundingInfo getCrowdfundingInfo(String infoUuid) {
        CrowdfundingInfo info = adminCrowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (info == null) {
            log.info("众筹信息不存在，infoUuid: {}", infoUuid);
            return null;
        }
        return info;
    }

}
