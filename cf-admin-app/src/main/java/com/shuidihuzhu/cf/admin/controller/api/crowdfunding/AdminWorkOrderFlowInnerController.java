package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;


import com.github.pagehelper.PageInfo;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderFlowBiz;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowParam;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Slf4j
@RequestMapping("/innerapi/cf/flow/order")
public class AdminWorkOrderFlowInnerController {

    @Autowired
    private AdminWorkOrderFlowBiz workOrderFlowBiz;

    @Autowired(required = false)
    private Producer producer;

    @Autowired
    private ApplicationService applicationService;

    @RequestMapping(path = "create", method = RequestMethod.POST)
    @ApiOperation(value = "创建工单", notes = "")
    public Response create(@RequestBody AdminWorkOrderFlowBiz.WorkOrderFlow orderFlow) {


        AdminWorkOrderFlowView vo = workOrderFlowBiz.buildFlowViewFromWorkOrder(orderFlow);
        if (vo == null) {
            return NewResponseUtil.makeFail("参数错误");
        }

        Response result = workOrderFlowBiz.createWorkOrderFlow(vo);

        if (result.getData() != null &&
                orderFlow.getTaskType() == AdminWorkOrderConst.TaskType.SINGLE_REFUND_DELAY_48H_NOTICE.getCode()) {
            DateTime now = new DateTime();
            long dispatchTime = now.plusDays(2).getMillis();
            if (applicationService.isDevelopment()) {
                dispatchTime = now.plusDays(0).plusHours(0).plusMinutes(2).getMillis();
            }
            Message message = Message.ofSchedule(MQTopicCons.CF, MQTagCons.ADMIN_FLOW_ORDER_SINGLE_REFUND_DELAY_48H,
                    "" + System.nanoTime(), result.getData(), dispatchTime / 1000);
            MessageResult messageResult = producer.send(message);
            log.info("单笔退款48小时消息发送. msg：{}, result:{}", message, messageResult);
        }

        return result;
    }

    @RequestMapping(path = "assign-work-order", method = RequestMethod.POST)
    @ApiOperation(value = "流转工单", notes = "")
    public Response assignWorkOrder(@RequestParam("workFlowId") String generateWorkFlowId, @RequestParam("appendProblemDesc") String appendProblemDesc,
                           @RequestParam("orgNames") List<String> orgNames) {
        workOrderFlowBiz.assignToGivenOrg(generateWorkFlowId, appendProblemDesc, orgNames);
        return ResponseUtil.makeSuccess("");
    }

    /**
     * 根据工单号查询
     *
     * @param flowIdStr -
     * @return -
     */
    @PostMapping(path = "find-by-flow-id-str")
    Response<AdminWorkOrderFlowView> findByFlowIdStr(@RequestParam("flowIdStr") String flowIdStr) {
        AdminWorkOrderFlowParam.SearchParam param = new AdminWorkOrderFlowParam.SearchParam();
        param.setFlowIdStr(flowIdStr);
        param.setPageSize(1);
        param.setPageNum(1);
        PageInfo<AdminWorkOrderFlowView> adminWorkOrderFlowViewPageInfo = workOrderFlowBiz.selectAdminWorkOrderByParam(param);
        if (CollectionUtils.isEmpty(adminWorkOrderFlowViewPageInfo.getList())) {
            return NewResponseUtil.makeSuccess(null);
        }
        AdminWorkOrderFlowView adminWorkOrderFlowView = adminWorkOrderFlowViewPageInfo.getList().get(0);
        return NewResponseUtil.makeSuccess(adminWorkOrderFlowView);
    }

}
