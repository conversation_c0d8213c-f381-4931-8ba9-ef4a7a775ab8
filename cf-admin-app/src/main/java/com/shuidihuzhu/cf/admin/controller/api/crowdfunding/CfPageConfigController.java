package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.commonservice.ICommonServiceDelegate;
import com.shuidihuzhu.cf.admin.util.admin.IntegerUtil;

import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminCfPageTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfAbTestCode;
import com.shuidihuzhu.cf.model.crowdfunding.CfPageGlobalConfig;
import com.shuidihuzhu.cf.model.graytest.CfGrayTestSwitch;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;


@RestController
@RequestMapping(path = "/admin/cf/pageconfig")
public class CfPageConfigController {

    private static final Logger LOGGER = LoggerFactory.getLogger(CfPageConfigController.class);


    @Autowired
    private ICommonServiceDelegate commonServiceDelegate;

    @RequestMapping(path = "list", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("pageConfig:list")
    public Response list(int globalType){

        if (globalType != AdminCfPageTypeEnum.leftPage.getCode()){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<CfPageGlobalConfig>  list = commonServiceDelegate.getListByType(globalType);
        CfGrayTestSwitch cfGrayTestSwitch = commonServiceDelegate.getByCode(CfAbTestCode.CF_BACK_PAGE_URL_TEST.getCode());
        String casePercentage = cfGrayTestSwitch.getCasePercentage();
        String[] percentages = casePercentage.split(",");

        if (list.size() != percentages.length){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR,"url和比例数量不匹配");
        }

        for (int a=0;a<list.size();a++){
            CfPageGlobalConfig cc = list.get(a);
            cc.setPercent(IntegerUtil.parseInt(percentages[a]));
        }
        Map<String, Object> result = Maps.newHashMap();
        result.put("list", list);
        result.put("size", list.size());

        return NewResponseUtil.makeSuccess(result);
    }


    @RequestMapping(path = "add", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("pageConfig:add")
    public Response add(String data){

        if (StringUtils.isEmpty(data)){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<CfPageGlobalConfig> list = JSON.parseArray(data,CfPageGlobalConfig.class);//已检查过

//        if (list.size() > 20){
//            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR,"数据数量过多");
//        }

        int sum = 0;
        for (CfPageGlobalConfig cfPageGlobalConfig : list){
            if (AdminCfPageTypeEnum.leftPage.getCode()!=cfPageGlobalConfig.getGlobalType()){
                LOGGER.info("CfPageConfigController add error globalType={}",cfPageGlobalConfig);
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR,"GlobalType类型错误");
            }
            if (StringUtils.isEmpty(cfPageGlobalConfig.getJumperUrl())){
                LOGGER.info("CfPageConfigController add error globalType={}",cfPageGlobalConfig);
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR,"JumperUrl不能为空");
            }

            sum += cfPageGlobalConfig.getPercent();
        }

        if (sum != 100){
            LOGGER.info("CfPageConfigController add error percent={}",sum);
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR,"测试比例相加必须是100");
        }

        boolean flag = commonServiceDelegate.add(list,AdminCfPageTypeEnum.leftPage.getCode());

        if (flag){
            return NewResponseUtil.makeSuccess("succ");
        }

        return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);

    }

}
