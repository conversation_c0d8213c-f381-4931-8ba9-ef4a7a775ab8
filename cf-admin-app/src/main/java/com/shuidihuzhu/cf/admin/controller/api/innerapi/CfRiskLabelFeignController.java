package com.shuidihuzhu.cf.admin.controller.api.innerapi;

import com.shuidihuzhu.cf.service.label.risk.RiskLabelServiceImpl;
import com.shuidihuzhu.client.cf.admin.client.CfRiskLabelFeignClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @Description: 风险标签
 * @Author: panghairui
 * @Date: 2023/10/12 2:01 PM
 */
@Slf4j
@RestController
public class CfRiskLabelFeignController implements CfRiskLabelFeignClient {

    @Resource
    private RiskLabelServiceImpl riskLabelService;

    @Override
    public Response<Boolean> judgeNewRiskLabel(Integer caseId) {
        return NewResponseUtil.makeSuccess(riskLabelService.judgeNewRiskLabel(caseId));
    }
}
