package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RefreshScope
@RequestMapping(path = "admin/cf/sea-frequency")
public class AdminSeaFrequencyController {


    @RequestMapping(path = "add", method = RequestMethod.GET)
    public Response add(@RequestParam(name = "type", defaultValue = "", required = false) String type,
                        @RequestParam(name = "name", defaultValue = "", required = false) String name,
                        @RequestParam(name = "location_href", defaultValue = "", required = false) String locationHref,
                        @RequestParam(name = "location_path_name", defaultValue = "", required = false) String locationPathName,
                        @RequestParam(name = "location_hash", defaultValue = "", required = false) String locationHash,
                        @RequestParam(name = "env", defaultValue = "", required = false) String env) {


        return NewResponseUtil.makeSuccess(AdminErrorCode.SUCCESS);
    }
}
