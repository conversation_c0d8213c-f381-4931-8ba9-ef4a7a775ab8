package com.shuidihuzhu.cf.admin.controller.api.innerapi;

import com.shuidihuzhu.cf.risk.client.risk.RiskStrategyBizClient;
import com.shuidihuzhu.cf.risk.model.param.CityParam;
import com.shuidihuzhu.client.cf.admin.client.CfAdminToolFeignClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Slf4j
@RestController
public class CfAdminToolFeignController implements CfAdminToolFeignClient {

    @Resource
    private RiskStrategyBizClient riskStrategyBizClient;

    @Override
    public Response<List<String>> searchCar(String carKey) {
        if (StringUtils.isEmpty(carKey)) {
            return NewResponseUtil.makeSuccess(Collections.emptyList());
        }
//        Response<List<String>> listResponse = riskStrategyBizClient.carBrand(carKey);
//        if (listResponse.notOk()) {
//            return NewResponseUtil.makeSuccess(Collections.emptyList());
//        }
//        return NewResponseUtil.makeSuccess(listResponse.getData());
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public Response<Boolean> houseCommunityNecessary(String province, String city, String county) {
        if (StringUtils.isEmpty(province) || StringUtils.isEmpty(city)) {
            return NewResponseUtil.makeSuccess(false);
        }
        CityParam cityParam = new CityParam();
        cityParam.setProvince(province);
        cityParam.setCity(city);
        cityParam.setCounty(county);
//        Response<Boolean> booleanResponse = riskStrategyBizClient.cityUseCommunity(cityParam);
//        if (booleanResponse.notOk()) {
//            return NewResponseUtil.makeSuccess(false);
//        }
//        return NewResponseUtil.makeSuccess(booleanResponse.getData());
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public Response<Double> cityHouseThreshold(String province, String city, String county) {
        CityParam cityParam = new CityParam();
        cityParam.setProvince(province);
        cityParam.setCity(city);
        cityParam.setCounty(county);
//        Response<Integer> integerResponse = riskStrategyBizClient.cityAmonutThreshold(cityParam);
//        if (integerResponse.notOk()) {
//            return NewResponseUtil.makeSuccess(0.0);
//        }
//        return NewResponseUtil.makeSuccess(integerResponse.getData() * 1.5);
        return NewResponseUtil.makeSuccess(null);
    }

}
