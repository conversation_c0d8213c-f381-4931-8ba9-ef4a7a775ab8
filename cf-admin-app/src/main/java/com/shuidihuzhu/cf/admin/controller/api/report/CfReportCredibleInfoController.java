package com.shuidihuzhu.cf.admin.controller.api.report;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.shuidihuzhu.cf.vo.report.ReportProveInfoVO;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.crowdfunding.IAdminCredibleInfoService;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CredibleTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfCredibleInfoDO;
import com.shuidihuzhu.cf.model.crowdfunding.CfSendProveTemplate;
import com.shuidihuzhu.cf.service.report.CfReportCredibleInfoService;
import com.shuidihuzhu.cf.vo.report.CfReportDisposeActionTemplateVO;
import com.shuidihuzhu.cf.vo.report.CfSendProveInfoRecord;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * @author: lixiaoshuang
 * @create: 2020-05-06 16:29
 **/
@RestController
@Slf4j
@RequestMapping(path = "admin/cf/report/credible-info", method = RequestMethod.POST)
@Api("举报可信信息接口")
public class CfReportCredibleInfoController {

    @Autowired
    private CfReportCredibleInfoService cfReportCredibleInfoService;
    @Autowired
    private IAdminCredibleInfoService iAdminCredibleInfoService;
    @Resource
    private CfWorkOrderClient cfWorkOrderClient;

    @RequestMapping(path = "add-trust/cancel")
    @ApiOperation("撤销补充证明接口")
    @RequiresPermission("report:add-trust-cancel")
    public Response cancelAddTrust(@ApiParam("可信信息id") @RequestParam("id") long id,
                                   @ApiParam("撤销原因") @RequestParam("reason") String reason) {
        log.info("CfReportCredibleInfoController.cancelAddTrust id:{},reason:{}", id, reason);
        int strLength = 100;
        if (id <= 0 || reason.length() > strLength) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CfCredibleInfoDO cfCredibleInfoDO = iAdminCredibleInfoService.queryById(id);
        if (Objects.isNull(cfCredibleInfoDO)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (cfCredibleInfoDO.getType() != CredibleTypeEnum.SUPPLY_VERFIFY.getKey()) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (cfCredibleInfoDO.getAuditStatus() == CrowdfundingInfoStatusEnum.SUBMITTED.getCode()
                || cfCredibleInfoDO.getAuditStatus() == CrowdfundingInfoStatusEnum.PASSED.getCode()
                || cfCredibleInfoDO.getAuditStatus() == CrowdfundingInfoStatusEnum.REJECTED.getCode()) {
            return NewResponseUtil.makeError(AdminErrorCode.NO_CANCEL);
        }
        int userId = ContextUtil.getAdminUserId();
        cfReportCredibleInfoService.cancelAddTrust(cfCredibleInfoDO.getSubId(), reason, userId);
        return NewResponseUtil.makeSuccess(null);
    }

    @RequestMapping(path = "get-action-template")
    @ApiOperation("获取处理动作对应的模板")
    public Response<List<CfReportDisposeActionTemplateVO>> getActionTemplate(@ApiParam("处理动作id") @RequestBody List<Long> actionIds) {
        log.info("getActionTemplate actionIds:{}", actionIds);
        if (CollectionUtils.isEmpty(actionIds)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        var actionTemplate = cfReportCredibleInfoService.getActionTemplate(actionIds);
        return NewResponseUtil.makeSuccess(actionTemplate);
    }


    @RequestMapping(path = "send-prove")
    @ApiOperation("下发补充证明")
    public Response sendProve(@RequestParam("cfSendProveTemplates") String cfSendProveTemplates,
                              @RequestParam("caseId") int caseId) {
        log.info("sendProve cfSendProveTemplates:{},caseId:{}", cfSendProveTemplates, caseId);
        List<CfSendProveTemplate> cfSendProveTemplateList = JSON.parseObject(cfSendProveTemplates, new TypeReference<List<CfSendProveTemplate>>() {
        });
        int userId = ContextUtil.getAdminUserId();
        return cfReportCredibleInfoService.sendProve(cfSendProveTemplateList, caseId, userId);
    }


    @RequestMapping(path = "get-prove-info", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation("查看补充证明详情")
    public Response<ReportProveInfoVO> getProveInfo(@ApiParam("案例id") @RequestParam(name = "caseId") int caseId,
                                                    @ApiParam("补充证明id") @RequestParam(name = "subId") long subId) {
        log.info("getProveInfo caseId:{},subId:{}", caseId, subId);
        if (caseId <= 0 || subId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        ReportProveInfoVO result = cfReportCredibleInfoService.getProveInfo(caseId, subId);
        return NewResponseUtil.makeSuccess(result);
    }

    @RequestMapping(path = "prove-audit")
    @ApiOperation("证明审核")
    public Response proveAudit(@ApiParam("补充证明id") @RequestParam("proveId") long proveId,
                               @ApiParam("模板") @RequestParam("templateJson") String templateJson,
                               @ApiParam("模板驳回原因") @RequestParam(value = "rejectedReason", defaultValue = "") String rejectedReason,
                               @ApiParam("模板撤回原因") @RequestParam(value = "cancelReason", defaultValue = "") String cancelReason,
                               @ApiParam("图片") @RequestParam("pictureUrl") String pictureUrl,
                               @ApiParam("图片驳回状态") @RequestParam("pictureAuditStatus") int pictureAuditStatus,
                               @ApiParam("图片驳回原因") @RequestParam(value = "pictureRejectedReason", defaultValue = "") String pictureRejectedReason,
                               @ApiParam("工单Id") @RequestParam(value = "workOrderId", required = false, defaultValue = "0") long workOrderId) {
        log.info("proveAudit templateJson:{},proveId:{},pictureUrl:{},rejectedReason:{},cancelReason:{},pictureAuditStatus:{}pictureRejectedReason,{}",
                templateJson, proveId, pictureUrl, rejectedReason, cancelReason, pictureAuditStatus, pictureRejectedReason);
        if (proveId <= 0 || StringUtils.isEmpty(templateJson)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (workOrderId > 0) {
            // 获取工单处理结果
            Response<WorkOrderVO> workOrderVOResponse = cfWorkOrderClient.getWorkOrderById(workOrderId);
            int workOrderHandle = Optional.ofNullable(workOrderVOResponse)
                    .map(Response::getData)
                    .map(WorkOrderVO::getHandleResult)
                    .orElse(-2);
            // 如果是审核通过或者审核驳回 ALREADY_DOME(180051,"工单已经处理完成，请返回列表")
            if (workOrderHandle == HandleResultEnum.audit_pass.getType() || workOrderHandle == HandleResultEnum.audit_reject.getType()) {
                return NewResponseUtil.makeError(AdminErrorCode.ALREADY_DOME);
            }
        }
        List<CfSendProveTemplate> cfSendProveTemplates = JSON.parseObject(templateJson, new TypeReference<List<CfSendProveTemplate>>() {
        });
        cfReportCredibleInfoService.proveAudit(proveId, cfSendProveTemplates, rejectedReason, cancelReason, pictureUrl,
                pictureAuditStatus, pictureRejectedReason, ContextUtil.getAdminUserId(), workOrderId);
        return NewResponseUtil.makeSuccess(null);
    }

    @RequestMapping(path = "prove-info-cancel")
    @ApiOperation("撤回补充证明接口")
    public Response proveInfoCancel(@ApiParam("可信信息id") @RequestParam("id") long id,
                                    @ApiParam("撤销原因") @RequestParam("reason") String reason) {
        log.info("CfReportCredibleInfoController.proveInfoCancel id:{},reason:{}", id, reason);
        int strLength = 100;
        if (id <= 0 || reason.length() > strLength) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CfCredibleInfoDO cfCredibleInfoDO = iAdminCredibleInfoService.queryById(id);
        if (Objects.isNull(cfCredibleInfoDO)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (cfCredibleInfoDO.getType() != CredibleTypeEnum.HELP_PROVE.getKey()) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (cfCredibleInfoDO.getAuditStatus() == CrowdfundingInfoStatusEnum.SUBMITTED.getCode()
                || cfCredibleInfoDO.getAuditStatus() == CrowdfundingInfoStatusEnum.PASSED.getCode()) {
            return NewResponseUtil.makeError(AdminErrorCode.NO_CANCEL);
        }
        int operatorId = ContextUtil.getAdminUserId();
        cfReportCredibleInfoService.cancelCredible(cfCredibleInfoDO, reason, operatorId);
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("report:prove-info-record")
    @RequestMapping(path = "prove-info-record")
    @ApiOperation("补充证明代修改记录")
    public Response<List<CfSendProveInfoRecord>> proveInfoRecord(@ApiParam("案例id") @RequestParam(name = "caseId") int caseId,
                                                                 @ApiParam("补充证明id") @RequestParam(name = "subId") long subId) {
        log.info("CfReportCredibleInfoController.proveInfoRecord caseId:{},subId:{}", caseId, subId);
        if (caseId <= 0 || subId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<CfSendProveInfoRecord> cfSendProveInfoRecords = cfReportCredibleInfoService.proveInfoRecord(caseId, subId);
        return NewResponseUtil.makeSuccess(cfSendProveInfoRecords);
    }

    @RequestMapping(path = "check-send-prove")
    @ApiOperation("检查是否可以下发证明")
    @RequiresPermission("report:check-send-prove")
    public Response<Map<String, Object>> checkSendProve(@ApiParam("案例id") @RequestParam(name = "caseId") int caseId) {
        log.info("CfReportCredibleInfoController.checkSendProve caseId:{}", caseId);
        if (caseId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        var map = cfReportCredibleInfoService.checkSendProve(caseId);
        return NewResponseUtil.makeSuccess(map);
    }

}
