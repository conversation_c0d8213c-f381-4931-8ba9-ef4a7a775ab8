package com.shuidihuzhu.cf.admin.controller.api.workorder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfRepeatInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfCallOutConditionBiz;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingConstant;
import com.shuidihuzhu.cf.model.admin.ShouciWorkOrderVO;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.CfCaseWorkOrderService;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cf.service.workorder.WorkOrderRemarkService;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.baseservice.msg.v2.MsgClientV2;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.ClewCallRecordModel;
import com.shuidihuzhu.client.cf.workorder.CfShouciWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.ShouciHandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.ShouciWorkOrderListParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OneTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import com.shuidihuzhu.data.analytics.javasdk.core.UserTagTypeEnum;
import com.shuidihuzhu.data.servicelog.meta.cf.FirstCallOut;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2019/4/24
 */
@RestController
@RequestMapping(path="/admin/workorder/shouci")
@Slf4j
public class ShouciWorkOrderController {

    @Autowired
    private CfShouciWorkOrderClient shouciWorkOrderClient;

    @Autowired
    private AdminCfRepeatInfoBiz cfRepeatInfoBiz;

    @Autowired
    private AdminApproveService adminApproveService;

    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Autowired
    private CfCallOutConditionBiz cfCallOutConditionBiz;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private CfCaseWorkOrderService workOrderService;

    @Autowired
    private Analytics analytics;

    @Autowired
    private SeaAccountClientV1 seaAccountClientV1;

    @Autowired
    private CfClewtrackFeignClient cfClewtrackFeignClient;

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @RequiresPermission("shouci:handle")
    @RequestMapping(path = "hanlde-shouci", method = RequestMethod.POST)
    public Response Handleshouci(@RequestParam("param") String param,@RequestParam("userId") int userId) {

        ShouciHandleOrderParam p = JSON.parseObject(param, ShouciHandleOrderParam.class);//已检查过
        String tag = "首次沟通-";
        if(p.getHandleResult() == HandleResultEnum.later_doing.getType()){
            tag+="稍后处理";
        }
        if(p.getHandleResult() == HandleResultEnum.done.getType()){
            tag+="提交结果";
        }
        if(p.getHandleResult() == HandleResultEnum.stop_case.getType()){
            tag+="停止筹款";
        }
        CrowdfundingInfo crowdfundingInfo = crowdfundingDelegate.getFundingInfoById(p.getCaseId());

        String comment = p.getOperComment();

        if ("1".equals(p.getCallStatus())){
            comment = "呼通。"+comment;
        }
        if ("2".equals(p.getCallStatus())){
            comment = "未呼通。"+comment;
        }
        adminApproveService.addApprove(crowdfundingInfo, tag,comment, userId);

        Response response = shouciWorkOrderClient.handleshouci(p);

        UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByUserId(crowdfundingInfo.getUserId());
        if (p.getHandleResult() == HandleResultEnum.later_doing.getType()||
                p.getHandleResult() == HandleResultEnum.done.getType()){
            //查询电话号码
            String mobile = "";
            if (userInfoModel != null && StringUtils.isNotBlank(userInfoModel.getCryptoMobile())) {
                mobile = shuidiCipher.decrypt(userInfoModel.getCryptoMobile());
            }
            cfCallOutConditionBiz.sendFirstCallOutMsg(crowdfundingInfo.getUserId(),mobile,crowdfundingInfo);
        }

        track(crowdfundingInfo, userInfoModel, p.getCallStatus(), "", userId);
        return response;
    }

    private void track(CrowdfundingInfo crowdfundingInfo, UserInfoModel userInfoModel, String callStatus, String callUnicode, int adminUserId){
        if(Objects.isNull(crowdfundingInfo)){
            return;
        }
        String cfUserId = String.valueOf(crowdfundingInfo.getUserId());
        String infoUuid = crowdfundingInfo.getInfoId();
        String mobile = Objects.nonNull(userInfoModel) ? shuidiCipher.decrypt(userInfoModel.getCryptoMobile()) : "";

        AuthRpcResponse<String> response = seaAccountClientV1.getMisByUserId(adminUserId);
        String mis = Objects.nonNull(response) && StringUtils.isNotEmpty(response.getResult()) ? response.getResult() : "";

        Map<String, Object> map = Maps.newHashMap();
        map.put("user_id", cfUserId);
        map.put("mobile", mobile);
        map.put(CrowdfundingConstant.INFO_ID_KEY, crowdfundingInfo.getId());
        map.put(CrowdfundingConstant.CASE_ID_KEY, infoUuid);
        map.put("employee_id", mis);
        map.put("is_connnected", callStatus);
        map.put("unique_id", callUnicode);

        FirstCallOut fco = new FirstCallOut();
        try {
            fco.setIs_connnected(StringUtils.trimToEmpty(callStatus));
            fco.setEmp_id(mis);
            fco.setUser_encrypt_mobile(Optional.ofNullable(userInfoModel).map(UserInfoModel::getCryptoMobile).orElse(""));
            fco.setInfo_id(Long.valueOf(crowdfundingInfo.getId()));
            fco.setCase_id(StringUtils.trimToEmpty(infoUuid));
            fco.setUser_tag(String.valueOf(cfUserId));
            fco.setUser_tag_type(UserTagTypeEnum.userid);

            analytics.track(fco);
            log.info("大数据打点上报,首次外呼:{}", JSONObject.toJSONString(fco));
        } catch (Exception e){
            log.error("大数据打点上报异常,首次外呼:{}", JSONObject.toJSONString(fco));
        }
    }

    @RequiresPermission("shouci:orderlist")
    @RequestMapping(path = "shouci-orderlist", method = RequestMethod.POST)
    public Response shouciOrderList(@RequestParam("param") String param) {

        int userId = ContextUtil.getAdminUserId();
        ShouciWorkOrderListParam p = JSON.parseObject(param, ShouciWorkOrderListParam.class);//已检查过
        p.setUserId(userId);
        workOrderService.fillTimeIfNullByHandleResult(p, OneTypeEnum.shouci);


        Response<PageResult<WorkOrderVO>> response = shouciWorkOrderClient.shouciOrderList(p);
        PageResult<WorkOrderVO> pageResult = response.getData();
        if (pageResult == null || CollectionUtils.isEmpty(pageResult.getPageList())){
            pageResult = new PageResult<>();
            pageResult.setPageList(Lists.newArrayList());
            return NewResponseUtil.makeSuccess(pageResult);
        }

        List<WorkOrderVO> list = pageResult.getPageList();

        Map<Long, String> map = adminApproveService.mapFilter(list.stream().map(WorkOrderVO::getCaseUserId).collect(Collectors.toList()));
        //封装admin 特殊需要的参数
        List<ShouciWorkOrderVO> shouci = list.stream().map(r->{

            ShouciWorkOrderVO shouciWorkOrderVO = new ShouciWorkOrderVO();
            BeanUtils.copyProperties(r, shouciWorkOrderVO);
            // 案例重复或二次发起的情况
            shouciWorkOrderVO.setRepeatStatusList(cfRepeatInfoBiz.selectRepeatStatusByCaseId(r.getCaseId()));

            shouciWorkOrderVO.setFollow(map.get(shouciWorkOrderVO.getCaseUserId()));

            return shouciWorkOrderVO;

        }).collect(Collectors.toList());

        PageResult<ShouciWorkOrderVO> result = new PageResult<>();
        result.setHasNext(pageResult.isHasNext());
        result.setPageList(shouci);

        return NewResponseUtil.makeSuccess(result);
    }

    @RequiresPermission("shouci:check")
    @RequestMapping(path = "shouci-check", method = RequestMethod.POST)
    public Response shouciCheck(@RequestParam("caseId") int caseId,@RequestParam("workOrderId") long workOrderId,@RequestParam("userId") long userId){

        if (caseId <= 0 || workOrderId <= 0){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        CrowdfundingInfo cf = crowdfundingDelegate.getFundingInfoById(caseId);
        //如果案例关闭了 结束工单
        if (cf.getEndTime().before(new Date())){
            ShouciHandleOrderParam p = new ShouciHandleOrderParam();
            p.setWorkOrderId(workOrderId);
            p.setHandleResult(HandleResultEnum.exception_done.getType());
            p.setUserId(userId);
            shouciWorkOrderClient.handleshouci(p);
            return NewResponseUtil.makeSuccess(true);
        }
        return NewResponseUtil.makeSuccess(false);
    }

    @RequiresPermission("shouci:bind-call-unicode")
    @RequestMapping(path = "shouci-bind-call-unicode",method = RequestMethod.POST)
    public Response<Void> bindShouCiCallUnicode(@RequestParam long workOrderId, @RequestParam String callUnicode){
        return shouciWorkOrderClient.bindShouCiCallUnicode(workOrderId, callUnicode);
    }

    @RequiresPermission("shouci:check-call-record")
    @RequestMapping(path = "check-call-record",method = RequestMethod.POST)
    public Response<List<ClewCallRecordModel>> checkCallRecord(@RequestParam long workOrderId){
        Response<List<WorkOrderExt>> response = cfWorkOrderClient.queryAllWorkExtIgnoreDelete(workOrderId, Lists.newArrayList(OrderExtName.callUnicode.getName()));
        if (response == null || CollectionUtils.isEmpty(response.getData())) {
            return NewResponseUtil.makeSuccess(new ArrayList<>());
        }
        List<String> uniqueIds = response.getData()
                .stream()
                .map(WorkOrderExt::getExtValue)
                .collect(Collectors.toList());
        return cfClewtrackFeignClient.getClewCallRecordsByUniqueIds(uniqueIds);
    }


}
