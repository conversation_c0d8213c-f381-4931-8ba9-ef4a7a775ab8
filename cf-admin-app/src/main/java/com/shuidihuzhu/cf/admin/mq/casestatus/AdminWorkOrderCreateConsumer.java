package com.shuidihuzhu.cf.admin.mq.casestatus;

import com.shuidihuzhu.cf.admin.mq.common.BaseMessageConsumer;
import com.shuidihuzhu.cf.biz.aiphoto.PhotoAiService;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingAttachmentBiz;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType;
import com.shuidihuzhu.cf.model.crowdfunding.CfAIPhotoModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoPayee;
import com.shuidihuzhu.cf.service.tog.impl.GuangzhouServiceImpl;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditOperateService;
import com.shuidihuzhu.cf.service.workorder.initialAudit.InitialAuditSearchService;
import com.shuidihuzhu.cf.delegate.ai.AiAIContentWorkOrderResultDelegate;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderTypeFeignClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.ClassifyTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OneTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.model.event.WorkOrderResultChangeEvent;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * 工单创建事件
 * <AUTHOR>
 */
@Service
@Slf4j
@RocketMQListener(id = CfClientMQTagCons.WORK_ORDER_CREATE,
        tags = CfClientMQTagCons.WORK_ORDER_CREATE,
        topic = MQTopicCons.CF)
public class AdminWorkOrderCreateConsumer extends BaseMessageConsumer<WorkOrderResultChangeEvent>
        implements MessageListener<WorkOrderResultChangeEvent> {

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @Autowired
    private InitialAuditSearchService initialAuditSearchService;

    @Autowired
    private PhotoAiService photoAiService;

    @Autowired
    private AdminCrowdfundingAttachmentBiz adminCrowdfundingAttachmentBiz;

    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Resource
    private AiAIContentWorkOrderResultDelegate aiAIContentWorkOrderResultDelegate;

    @Resource
    private CfWorkOrderTypeFeignClient cfWorkOrderTypeFeignClient;

    @Autowired
    private GuangzhouServiceImpl guangzhouService;


    @Override
    protected boolean handle(ConsumerMessage<WorkOrderResultChangeEvent> consumerMessage) {
        WorkOrderResultChangeEvent e = consumerMessage.getPayload();
        long workOrderId = e.getWorkOrderId();
        int handleResult = e.getHandleResult();

        Response<WorkOrderVO> r = cfWorkOrderClient.getWorkOrderById(workOrderId);
        if (r.notOk()) {
            return false;
        }
        WorkOrderVO workOrderVO = r.getData();
        int orderType = workOrderVO.getOrderType();
        int caseId = workOrderVO.getCaseId();

        // 创建材料工单 预加载ocr信息
        try {
            int oneType = 0;
            Response<Integer> resp = cfWorkOrderTypeFeignClient.getOneFromTwo(orderType);
            if(resp.ok()){
                oneType = resp.getData();
            }
            if (oneType == OneTypeEnum.cailiao.getType() && orderType != WorkOrderType.cailiao_fuwu.getType()){
                AttachmentTypeEnum attachmentType = AttachmentTypeEnum.ATTACH_ID_CARD;
                String idUrl = getUrl(caseId, attachmentType);
                String onlyIdUrl = getUrl(caseId, AttachmentTypeEnum.ATTACH_ONLY_ID_CARD);
                CfAIPhotoModel aiPhotoModel = new CfAIPhotoModel(caseId, idUrl, onlyIdUrl);
                photoAiService.aiCheckPhotos(aiPhotoModel, workOrderId,0);

                //材审工单创建
                //当收款人信息中收款方式为：“配偶或近亲属收款”，且身份验证不是使用人脸识别的且生成材审工单时：收款人信息模块调用ocr识别
                payeeOcr(caseId,workOrderId);
            }
        } catch (Exception ex) {
            log.warn("ocr识别预加载失败 workOrderId:{}", workOrderId);
        }

        // 创建初审工单 预加载ocr信息
        try {
            if (InitialAuditOperateService.chushenWorkReProcessList.contains(orderType) &&
                    handleResult == HandleResultEnum.undoing.getType()){
                initialAuditSearchService.loadOcrInfo(workOrderId, workOrderVO.getCaseId());
            }
        } catch (Exception ex) {
            log.warn("ocr识别预加载失败 workOrderId:{}", workOrderId);
        }

        try {
            aiAIContentWorkOrderResultDelegate.aiContentWorkResult(caseId, orderType, workOrderId);
        } catch (Exception ex) {
            log.warn("图文录入工单获取AI结果 workOrderId:{} {}", workOrderId, ex);
        }
        // 新创建的工单是否过广州标签
        try {
            if(GuangzhouServiceImpl.Guangzhou_chuci_WORK_ORDER_LIST.contains(orderType) ||
                    GuangzhouServiceImpl.Guangzhou_tuwen_WORK_ORDER_LIST.contains(orderType)){
                guangzhouService.addOrUpdateGuangzhouLabel(caseId, orderType, workOrderId);
            }
        } catch (Exception ex) {
            log.warn("广州标签处理失败 workOrderId:{}", workOrderId);
        }
        return true;
    }

    @NotNull
    private String getUrl(int caseId, AttachmentTypeEnum attachmentType) {
        List<CrowdfundingAttachment> idUrls = adminCrowdfundingAttachmentBiz
                .getAttachmentsByParentIdAndType(caseId, attachmentType);
        return Optional.ofNullable(idUrls)
                .filter(CollectionUtils::isNotEmpty)
                .map(v -> v.get(0))
                .map(CrowdfundingAttachment::getUrl)
                .orElse("");
    }


    private void payeeOcr(int caseId,long workOrderId){

        CrowdfundingInfo c = crowdfundingDelegate.getCaseInfoById(caseId);

        //不是近亲属不验证
        if (c.getRelationType() == CrowdfundingRelationType.LOCATION_HOSPITAL_ACCOUNT || c.getRelationType() == CrowdfundingRelationType.charitable_organization
             || c.getRelationType() == CrowdfundingRelationType.self){
            return;
        }

        CrowdfundingInfoPayee payee = crowdfundingDelegate.getCrowdfundingInfoPayeeByInfoUuid(c.getInfoId());

        //人脸识别不验证
        if (payee.getFaceIdResult() == 20){
            return;
        }

        String idUrl = getUrl(caseId, AttachmentTypeEnum.ATTACH_PAYEE_ID_CARD);
        String onlyIdUrl = getUrl(caseId, AttachmentTypeEnum.ATTACH_PAYEE_ONLY_ID_CARD);

        CfAIPhotoModel aiPhotoModel = new CfAIPhotoModel(caseId, idUrl, onlyIdUrl);
        photoAiService.aiCheckPhotos(aiPhotoModel, workOrderId,100);


    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
