package com.shuidihuzhu.cf.admin.mq.robotalarm;

import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.service.alarm.CfRobotSqlAlarmService;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @Description: 机器人播报自动化执行消费者
 * @Author: panghairui
 * @Date: 2022/1/7 5:19 下午
 */
@Slf4j
@Service
@RocketMQListener(id = "CF_ROBOT_ALARM_EXECUTOR_SQL",
        group = "cf-admin-robot-group",
        tags = "CF_ROBOT_ALARM_EXECUTOR_SQL",
        topic = MQTopicCons.CF)
public class CfRobotSqlAlarmConsumer implements MessageListener<String> {

    @Resource
    private AlarmClient alarmClient;

    @Resource
    private CfRobotSqlAlarmService cfRobotSqlAlarmService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<String> mqMessage) {

        log.info("CfRobotSqlAlarmConsumer start");

        // 判空
        if(mqMessage == null || mqMessage.getPayload() == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        // 取出数据
        String alarmMsg = mqMessage.getPayload();
        if (StringUtils.isEmpty(alarmMsg)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        log.info("CfRobotSqlAlarmConsumer alarmMsg is {}", alarmMsg);

        try {
            // 数据转换
            JSONObject alarmMsgJson = JSONObject.parseObject(alarmMsg);
            if (Objects.isNull(alarmMsgJson)) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            // 获取触发人
            String sendName = Optional.ofNullable(alarmMsgJson.getString("corpUserName")).orElse("");
            if (StringUtils.isEmpty(sendName)) {
                log.info("CfRobotSqlAlarmConsumer 无发送人信息 {}", alarmMsgJson);
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            // 获取内容
            String content = Optional.ofNullable(alarmMsgJson.getString("content")).orElse("");
            log.info("CfRobotSqlAlarmConsumer content is {}", content);
            if (StringUtils.isEmpty(content)) {
                alarmClient.sendByUser(List.of(sendName), "机器人播报命令内容为空，无法执行！");
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            // 执行内容
            cfRobotSqlAlarmService.parseTRobotAlarmSql(content, sendName);

            // 结束提醒
            alarmClient.sendByUser(List.of(sendName), "您的播报已执行完毕！");

        } catch (Exception e) {
            log.warn("CfRobotSqlAlarmConsumer parse json exception ", e);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
