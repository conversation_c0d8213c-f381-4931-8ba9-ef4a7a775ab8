package com.shuidihuzhu.cf.admin.controller.api.report;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.enhancer.model.response.EhResponse;
import com.shuidihuzhu.cf.model.report.schedule.ReportScheduleVO;
import com.shuidihuzhu.cf.service.report.ReportScheduleService;
import com.shuidihuzhu.common.web.util.ContextUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api("举报跟进标记接口")
@Slf4j
@RestController
@RequestMapping("admin/cf/report/mark")
public class ReportScheduleController {

    @Autowired
    private ReportScheduleService reportScheduleService;

    private static final SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequiresPermission("approve:mark-follow-time")
    @ApiOperation("添加跟进")
    @PostMapping("add")
    public EhResponse<Void> add(@RequestParam int caseId,
                                @RequestParam String targetTime) {
        int adminUserId = ContextUtil.getAdminUserId();
        Date time = parseTime(targetTime);
        return reportScheduleService.add(adminUserId, caseId, time);
    }

    @RequiresPermission("approve:mark-follow-time")
    @ApiOperation("修改跟进")
    @PostMapping("update")
    public EhResponse<Void> update(@RequestParam int id,
                                   @RequestParam String targetTime) {
        int adminUserId = ContextUtil.getAdminUserId();
        Date time = parseTime(targetTime);
        return reportScheduleService.update(adminUserId, id, time);
    }

    @RequiresPermission("approve:mark-follow-time")
    @ApiOperation("删除跟进")
    @PostMapping("remove")
    public EhResponse<Void> remove(@RequestParam int id){
        int adminUserId = ContextUtil.getAdminUserId();
        return reportScheduleService.remove(adminUserId, id);
    }

    @ApiOperation("处理跟进")
    @PostMapping("done")
    public EhResponse<Void> done(@RequestParam int id){
        int adminUserId = ContextUtil.getAdminUserId();
        return reportScheduleService.done(adminUserId, id);
    }

    @ApiOperation("根据案例id查询跟进")
    @PostMapping("get-by-case-id")
    public EhResponse<ReportScheduleVO> getByCaseId(@RequestParam int caseId) {
        return reportScheduleService.getByCaseId(caseId);
    }

    @RequiresPermission("report-mark:get-mark-list")
    @ApiOperation("查询操作人跟进列表")
    @PostMapping("get-list-by-operator-id")
    public EhResponse<List<ReportScheduleVO>> getListByOperatorId() {
        int adminUserId = ContextUtil.getAdminUserId();
        return reportScheduleService.getListByOperatorId(adminUserId);
    }

    private Date parseTime(String targetTime) {
        try {
            return dateTimeFormat.parse(targetTime);
        } catch (ParseException e) {
            log.error("ReportScheduleController parseTime error", e);
            return null;
        }
    }
}
