package com.shuidihuzhu.cf.admin.controller.api.approve;

import com.google.common.collect.Maps;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.admin.river.IRiverAssembleInterface;
import com.shuidihuzhu.cf.admin.river.RiverHandleRecordService;
import com.shuidihuzhu.cf.admin.river.RiverHelpService;
import com.shuidihuzhu.cf.admin.river.impl.RiverCreditFacadeImpl;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonTag;
import com.shuidihuzhu.cf.model.river.RiverUsageTypeEnum;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cf.vo.approve.*;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("admin/cf/river")
public class RiverController {

    @Autowired
    private RiverHandleRecordService riverHandleRecordService;

    @Autowired
    private AdminApproveService approveService;

    @ApiOperation("获取材料审核记录列表")
    @PostMapping("list-operation-record")
    @RequiresPermission("river:list-operation-record")
    public Response<List<RiverHandleRecordVO>> listOperationRecord(@RequestParam int caseId,
                                                                   @RequestParam RiverUsageTypeEnum usageType) {
        return riverHandleRecordService.list(caseId, usageType);
    }

    @ApiOperation("发送短信")
    @PostMapping("send-sms")
    @RequiresPermission("river:send-sms")
    public Response<Void> sendSms(@RequestBody RiverSendSmsParamVO param){
        // 发短信
        int userId = ContextUtil.getAdminUserId();
        approveService.sendCaseApproveSmsWithRecord(param.getMobile(), param.getContent(), param.getModelNum(),
                param.getCaseId(), userId, param.getParamMap());
        riverHandleRecordService.saveSendSmsLog(param, userId);
        return NewResponseUtil.makeSuccess(null);
    }

}
