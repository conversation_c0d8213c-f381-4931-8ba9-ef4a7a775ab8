package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;


import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.admin.AdminWorkOrderBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingOperationBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderCaseBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderCaseRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfAdminOperationRecordBiz;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.BackgroundLogEnum;
import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminWorkOrderCaseConst;
import com.shuidihuzhu.cf.enums.crowdfunding.CfOperatingRecordEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfOperationRecordEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingOperationEnum;
import com.shuidihuzhu.cf.model.admin.AdminWorkOrder;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderCase;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderCaseRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.FinanceApproveService;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cf.service.crowdfunding.DealWithStatusService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Date;
import java.util.List;

/**
 * 案例垃圾箱 Created by ahrievil on 2017/5/27.
 */
@Controller
@Slf4j
@RequestMapping(path = "admin/crowdfunding/deal-with-status")
public class CfDealWithStatusController {

	@Autowired
	private ICrowdfundingDelegate crowdfundingDelegate;
	@Autowired
	private AdminApproveService adminApproveService;
	@Autowired
	private FinanceApproveService financeApproveService;
	@Autowired
	private CfAdminOperationRecordBiz cfAdminOperationRecordBiz;
	@Autowired
	private AdminWorkOrderBiz adminWorkOrderBiz;
	@Autowired
	private AdminWorkOrderCaseBiz adminWorkOrderCaseBiz;
	@Autowired
	private AdminWorkOrderCaseRecordBiz adminWorkOrderCaseRecordBiz;

	@Autowired
	private DealWithStatusService dealWithStatusService;

	@Autowired
	private AdminCrowdfundingOperationBiz adminCrowdfundingOperationBiz;


	@RequiresPermission("deal-with-status:add")
	@RequestMapping(path = "add", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	@ResponseBody
	public Response add(@RequestParam String infoUuid, Integer status,
						@RequestParam(required = false, defaultValue = "") String comment,
						Integer workType, Long workId,
						@RequestParam(required = false, defaultValue = "0") int deferContactReasonType) {
		int userId = ContextUtil.getAdminUserId();
		log.info("CfDealWithStatusController add infoUuid:{};status:{};userId:{};comment:{};workType:{};workId:{};deferContactReasonType:{}", infoUuid, status,
				userId, comment, workType, workId, deferContactReasonType);
		comment = comment == null ? StringUtils.EMPTY : comment;
		if (StringUtils.isBlank(infoUuid) || status == null) {
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
		}
		CrowdfundingInfo crowdfundingInfo = crowdfundingDelegate.getCrowdfundingInfoByInfoId(infoUuid);
		if (crowdfundingInfo == null) {
			return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
		}
		//新增workId,orderType兼容处理前端没上线前走老逻辑，上线后走else的逻辑
		if (workId == null || workType == null || workId.equals(0L)) {
			return dealWithStatusService.add(status, comment, userId, crowdfundingInfo, deferContactReasonType);
		} else {
			return dealWithStatusService.addWithWorkType(status, comment, userId, crowdfundingInfo, workType, workId, deferContactReasonType);
		}

	}


	//update 工单状态
	private Response updateWorkOrderCaseApprove(int caseId, String infoUuid, int userId, String comment,CrowdfundingOperationEnum crowdfundingOperationEnum) {
		log.info("CfDealWithStatusController updateWorkOrderCaseApprove infoUuid:{}, userId:{}", infoUuid, userId);
        //首先查询案例对应的材料审核工单
		List<AdminWorkOrderCase> adminWorkOrderCases = adminWorkOrderCaseBiz.selectByCaseIdAndTypeAndStatusAndTime(
				caseId, AdminWorkOrderCaseConst.CaseType.CASE_APPROVE, AdminWorkOrderCaseConst.Status.PROCESSING,
				null, null);
		//兼容未进入工单的案例
		if (CollectionUtils.isEmpty(adminWorkOrderCases) || adminWorkOrderCases.size() > 1) {
			log.info("updateWorkOrderCaseApprove Error! infoUuid:{}, errorParam:{}", infoUuid, JSON.toJSON(adminWorkOrderCases));
			return null;
		}
		long workOrderId = adminWorkOrderCases.get(0).getWorkOrderId();
		AdminWorkOrder adminWorkOrder = adminWorkOrderBiz.selectById(workOrderId);
		//更新工单状态
		adminWorkOrderBiz.updateChangeable(adminWorkOrder.getId(), AdminWorkOrderConst.Status.FINISHED,
				AdminWorkOrderConst.Result.HANDLE_SUCCESS, comment);
		//更新工单任务对应的映射表任务状态
		AdminWorkOrderCase adminWorkOrderCase1 = adminWorkOrderCaseBiz.selectById(adminWorkOrderCases.get(0).getId());
		adminWorkOrderCase1.setStatus(AdminWorkOrderCaseConst.Status.COMPLETE.getCode());
		adminWorkOrderCase1.setApproveResult(changToResult(crowdfundingOperationEnum));
		adminWorkOrderCaseBiz.update(adminWorkOrderCase1);
		//添加工单任务日志

		AdminWorkOrderCaseRecord record = new AdminWorkOrderCaseRecord(adminWorkOrderCase1, adminWorkOrder.getOperatorId());
		adminWorkOrderCaseRecordBiz.insertOne(record);
		return null;
	}

	private String changToResult(CrowdfundingOperationEnum crowdfundingOperationEnum) {
		switch (crowdfundingOperationEnum){
			// 延后审核
			case DEFER_APPROVE:
				return AdminWorkOrderCaseConst.ApproveResult.DEFER_APPROVE.getCode()+"";
			//延后电话联系
			case DEFER_CONTACT:
				return AdminWorkOrderCaseConst.ApproveResult.DEFER_CONTACT.getCode()+"";
			//不再处理
			case NEVER_PROCESSING:
				return AdminWorkOrderCaseConst.ApproveResult.NEVER_PROCESSING.getCode()+"";
			default:
				return "";
		}
	}


	@RequiresPermission("deal-with-status:reset")
	@RequestMapping(path = "reset", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Response reset(@RequestParam String infoUuid, @RequestParam String comment) {
		int userId = ContextUtil.getAdminUserId();
		log.info("CfDealWithStatusController reset infoUuid:{}, userId:{}, comment:{}", infoUuid, userId, comment);
        if (StringUtils.isBlank(infoUuid))
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        CrowdfundingInfo crowdfundingInfo = crowdfundingDelegate.getCrowdfundingInfoByInfoId(infoUuid);
        if (crowdfundingInfo == null)
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        BackgroundLogEnum backLogEnum = BackgroundLogEnum.DEFERRED_RECOVER;
        financeApproveService.addApprove(crowdfundingInfo, backLogEnum.getMessage(), comment, userId);
		adminApproveService.dealWithChangeOperation(infoUuid, CrowdfundingOperationEnum.OPERATED, comment, userId);
        CfOperatingRecordEnum.Type type = CfOperatingRecordEnum.Type.DELAY_RECOVER;
        adminApproveService.saveOperatingRecord(userId, infoUuid, type);
        cfAdminOperationRecordBiz.addOneOperationRecord(infoUuid, userId, CfOperationRecordEnum.DEFERRED_RECOVER.value(), comment);
		log.info(
                "客服后台log：deal-with operationTime:{};operator:{};operationReason:{};operationType:{};infoId:{};status:{};dataStatus:{};infoStatus:{}",
                DateUtil.formatDateTime(new Date()), userId, backLogEnum.getMessage(),
                backLogEnum.getMessage(), infoUuid, crowdfundingInfo.getStatus(),
                crowdfundingInfo.getDataStatus(), crowdfundingInfo.getEndTime().before(new Date()) ? "结束" : "未结束");
        return NewResponseUtil.makeSuccess(null);
    }


	@RequiresPermission("deal-with-status:resetCreditStatus")
	@RequestMapping(path = "resetCreditStatus", method = RequestMethod.POST)
	@ResponseBody
	public Response resetCreditStatus(@RequestParam int caseId, @RequestParam String comment) {

		if (caseId <=0 || StringUtils.isBlank(comment)){
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
		}

		int userId = ContextUtil.getAdminUserId();
		CrowdfundingInfo crowdfundingInfo = crowdfundingDelegate.getCaseInfoById(caseId);

		financeApproveService.addApprove(crowdfundingInfo, "撤销手动关闭", comment, userId);

		int a = adminCrowdfundingOperationBiz.updateCreditStatus(caseId,0);

		log.info("resetCreditStatus caseId={} userId={}",caseId,userId);

		return NewResponseUtil.makeSuccess(a);
	}
}
