package com.shuidihuzhu.cf.admin.controller.api.approve;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.service.approve.CaseInteractionService;
import com.shuidihuzhu.cf.vo.approve.CaseInteractionDetailVO;
import com.shuidihuzhu.common.web.model.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.Min;

/**
 * <AUTHOR>
 */
@Api(value = "用户与平台交互", tags = {"https://wiki.shuiditech.com/pages/viewpage.action?pageId=360907975"})
@RestController
@RequestMapping("/admin/cf/approve/interaction")
public class CaseInteractionController {

    @Autowired
    private CaseInteractionService caseInteractionService;

    @ApiOperation(value = "获取案例维度的交互详情")
    @PostMapping("detail")
    @RequiresPermission("approve-interaction:detail")
    public Response<CaseInteractionDetailVO> detail(@RequestParam() @Min(1) int caseId){
        return caseInteractionService.detail(caseId);
    }
}
