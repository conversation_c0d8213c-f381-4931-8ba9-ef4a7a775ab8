package com.shuidihuzhu.cf.admin.util;

import com.google.common.base.Preconditions;
import com.shuidihuzhu.common.web.util.aliyun.enums.OSSBucketDir;
import com.shuidihuzhu.common.web.util.aliyun.enums.OSSBucketEnum;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.UUID;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/6/14 2:58 PM
 */
public class CfDataUploadUtils {

    protected final static Logger logger = LoggerFactory.getLogger(CfDataUploadUtils.class);

    private static final String OSS_BUCKET_NAME = "shuidi-wx-content";

    private static final String OSS_SHUIDI_STATIC_BUCKET_NAME = "shuidi-static";
    private static final String OSS_SHUIDI_STATIC_IMAGE_URL = "http://shuidi-wx-content.oss-cn-shanghai.aliyuncs.com";
    public static final String OSS_SHUIDI_CF_IMAGE_URL = "http://cf.alioss.shuidihuzhu.com";
    /**
     * 将图片写入oss,访问地址为：http://shuidi-wx-content.oss-cn-shanghai.aliyuncs.com/{destFileName}
     *
     * @param in
     * @return 文件名
     * @throws IOException
     */
    public static String uploadWxImageToOSS(InputStream in, String fileName) throws IOException {
        String destFileName = OSSBucketDir.SHUIDI_WX_CONTENT_IMAGE.getBucketDirName() + "/" + fileName;
        CfOssClient.putObject(OSS_BUCKET_NAME, destFileName, in);
        if (logger.isDebugEnabled()) {
            logger.debug("upload image file success");
        }
        IOUtils.closeQuietly(in);
        return destFileName;
    }

    /**
     * 将图片写入oss,访问地址为：http://shuidi-wx-content.oss-cn-shanghai.aliyuncs.com/{destFileName}
     * @param in
     * @param fileName
     * @return
     * @throws IOException
     */
    public static String uploadCFImageToOSS(InputStream in, String fileName, String suffix) throws IOException {
        String newFileName = genFileName();
        String destFileName = OSSBucketDir.SHUIDI_CF_IMAGE.getBucketDirName() + "/" + newFileName + "." + suffix;
        CfOssClient.putObject(OSS_BUCKET_NAME, destFileName, in);
        if (logger.isDebugEnabled()) {
            logger.debug("upload image file success");
        }
        IOUtils.closeQuietly(in);
        return OSS_SHUIDI_STATIC_IMAGE_URL + "/" + destFileName;
    }


    /**
     * 将多媒体写入oss,访问地址为：http://shuidi-wx-content.oss-cn-shanghai.aliyuncs.com/{destFileName}
     *
     * @param in
     * @return 文件名
     * @throws IOException
     */
    public static String uploadWxMediaToOSS(InputStream in, String fileName) throws IOException {
        String destFileName = OSSBucketDir.SHUIDI_WX_CONTENT_MEDIA.getBucketDirName() + "/" + fileName;
        CfOssClient.putObject(OSS_BUCKET_NAME, destFileName, in);
        if (logger.isDebugEnabled()) {
            logger.debug("upload media file success");
        }
        IOUtils.closeQuietly(in);
        return destFileName;
    }

    /**
     * 图片的最终路径是: http://{bucketEnum.getBucketName()}.oss-cn-beijing.aliyuncs.com/{destFileName}
     * @param in
     * @param bucketEnum
     * @param directory 目录
     * @param suffix 文件后缀
     * @return
     * @throws IOException
     */
    public static String uploadImageToOSS(InputStream in, OSSBucketEnum bucketEnum, String directory, String suffix) throws IOException {
        Preconditions.checkNotNull(bucketEnum);
        String newFileName = genFileName() + (StringUtils.isEmpty(suffix) ? "" : "." + suffix);
        String destFileName = directory + "/" + newFileName;
        destFileName = destFileName.replace("//", "/");

        com.aliyun.oss.OSSClient client = CfOssClient.getOSSClient(bucketEnum);
        CfOssClient.putObject(client, bucketEnum.getBucketName(), destFileName, in);
        if (logger.isDebugEnabled()) {
            logger.debug("upload image file success: {}", destFileName);
        }
        IOUtils.closeQuietly(in);
        return getFileUrl(bucketEnum, destFileName);
    }

    /**
     * 图片的最终路径是: http://{bucketEnum.getBucketName()}.oss-cn-beijing.aliyuncs.com/{filePath}
     * 注意：filePath对应的文件如果存在，则覆盖，请确保你正确地设置了filePath
     *
     * @param in
     * @param bucketEnum
     * @param filePath 文件路径（包含文件名）
     * @return
     * @throws IOException
     */
    public static String uploadImageToOSS(InputStream in, OSSBucketEnum bucketEnum, String filePath) throws IOException {
        Preconditions.checkNotNull(bucketEnum);
        com.aliyun.oss.OSSClient client = CfOssClient.getOSSClient(bucketEnum);
        CfOssClient.putObject(client, bucketEnum.getBucketName(), filePath, in);
        if (logger.isDebugEnabled()) {
            logger.debug("upload image file success: {}", filePath);
        }
        IOUtils.closeQuietly(in);
        return getFileUrl(bucketEnum, filePath);
    }

    public static String genFileName() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    public static String getFileUrl(OSSBucketEnum ossBucketEnum, String destFileName) {
        return "http://" + ossBucketEnum.getBucketName() + "." + ossBucketEnum.getHost() + ("/" + destFileName).replace("//", "/");
    }

    public static void main(String[] args) throws Exception {
        File file = new File("/Users/<USER>/Pictures/617.jpg");
        InputStream input = new FileInputStream(file);
//        System.out.println(CfDataUploadUtils.uploadWxImageToOSS(input, "pictures/617.jpg"));
//        System.out.println(CfDataUploadUtils.uploadCFImageToOSS(input, "testfile", "jpg"));
//        System.out.println(CfDataUploadUtils.uploadImageToOSS(input, OSSBucketEnum.SDHUZHU_STATIC, "testdir", "jpg"));
        System.out.println(CfDataUploadUtils.uploadImageToOSS(input, OSSBucketEnum.SDHUZHU_STATIC, "testdir/test.jpg"));
    }

}
