package com.shuidihuzhu.cf.admin.controller.api.innerapi.redis.inner;

import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
class CfRedisToolInnerFeignClientFallback implements CfRedisToolInnerFeignClient {
    @Override
    public Response<String> ttl(String ip, String key) {
        return NewResponseUtil.makeFail("fall back");
    }

    @Override
    public Response<Boolean> del(String ip, String key) {
        return NewResponseUtil.makeFail("fall back");
    }

    @Override
    public Response<RedisqueryResponseDTO> get(String ip, String key) {
        return NewResponseUtil.makeFail("fall back");
    }
}
