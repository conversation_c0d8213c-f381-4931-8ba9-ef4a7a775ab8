package com.shuidihuzhu.cf.admin.controller.api.user;

import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAuthClientV1;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/9/7
 */

@Slf4j
@RestController
@RequestMapping(path = "/admin/cf/account/permission")
public class AdminUserPermissionController {

    @Autowired
    private SeaAuthClientV1 seaAuthClientV1;


    @PostMapping("valid-user-permission")
    @ResponseBody
    public Response<Boolean> validUserPermission(@RequestParam String permission) {
        int userId = ContextUtil.getAdminUserId();
        if (userId <= 0) {
            return NewResponseUtil.makeError(CfErrorCode.USER_ACCOUNT_NO_LOGIN);
        }
        AuthRpcResponse<Boolean> response = seaAuthClientV1.hasPermissionWithUser(userId, permission);
        boolean result = Objects.isNull(response) || Objects.isNull(response.getResult()) ? false : response.getResult().booleanValue();
        return NewResponseUtil.makeSuccess(result);
    }


}
