package com.shuidihuzhu.cf.admin.mq;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingOperationBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfFundUseDetailBiz;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.dao.admin.IPromoteWebCallDao;
import com.shuidihuzhu.cf.delegate.finance.IFinanceDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.CaseReportStatusEnum;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cf.enums.report.ReportPayMethodEnum;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceDrawCashFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.model.drawcash.CfDrawCashLimit;
import com.shuidihuzhu.cf.finance.model.vo.draw.CfAmountInfoVo;
import com.shuidihuzhu.cf.model.admin.workorder.CfFundUseDetailDO;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.cf.service.cfOperatingProfile.CfOperatingCaseLabelBiz;
import com.shuidihuzhu.cf.service.report.ReportScheduleService;
import com.shuidihuzhu.cf.service.workorder.WorkOrderFundUseService;
import com.shuidihuzhu.cf.service.workorder.promoteBill.CfPromoteBillService;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCoreFeignClient;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCreateParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderLevel;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @author: fengxuan
 * @create 2019-12-20 17:44
 **/
@RefreshScope
@Service
@RocketMQListener(id = "cf-fund-use-create-work",
        group = "cf_fund_use_create_work_group",
        tags = "CF_FUND_USE_CREATE_WORK",
        topic = MQTopicCons.CF)
@Slf4j
public class CfFundUseCreateWorkConsumer implements MessageListener<CrowdFundingProgress> {

    @Autowired
    CfFundUseDetailBiz detailBiz;

    @Autowired
    WorkOrderFundUseService fundUseService;

    @Autowired
    AdminCrowdfundingOperationBiz operationBiz;

    @Autowired
    AdminCrowdfundingInfoBiz crowdfundingInfoBiz;

    @Autowired
    CfOperatingCaseLabelBiz caseLabelBiz;

    @Resource(name = "cf2RedissonHandler")
    private RedissonHandler redissonHandler;

    @Resource
    private IPromoteWebCallDao promoteWebCallDao;

    @Autowired
    private CfPromoteBillService promoteBillService;

    @Resource
    private CfFinanceDrawCashFeignClient cfFinanceDrawCashFeignClient;

    @Resource
    private IFinanceDelegate iFinanceDelegate;
    @Resource
    private CfWorkOrderClient cfWorkOrderClient;
    @Resource
    private WorkOrderCoreFeignClient workOrderCoreFeignClient;
    @Resource
    private ReportScheduleService reportScheduleService;
    @Resource
    private SeaAccountClientV1 seaAccountClientV1;

    @Autowired
    private ApplicationService applicationService;

    @Value("${report-split-draw-limit-rate:0.9}")
    private double reportSplitDrawLimitRate;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdFundingProgress> mqMessage) {
        if (mqMessage == null || mqMessage.getPayload() == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        CrowdFundingProgress crowdFundingProgress = mqMessage.getPayload();
        log.info("CfFundUseCreateWorkConsumer msg:{},key:{}", JSON.toJSONString(crowdFundingProgress), mqMessage.getKeys());
        Integer caseId = crowdFundingProgress.getActivityId();
        Integer fundingProgressId = crowdFundingProgress.getId();

        if (fundingProgressId == null || fundingProgressId <= 0 || caseId == null || caseId <= 0) {
            log.warn("crowdFundingProgress id error:{}", JSON.toJSONString(crowdFundingProgress));
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        String key = "cf_fund_use_create_work_" + fundingProgressId;
        String tryLock = null;
        try {
            tryLock = redissonHandler.tryLock(key, 0, 60 * 1000L);
        } catch (Exception e) {
            log.error("error", e);
        }

        if (StringUtils.isBlank(tryLock)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        try {
            realConsumer(crowdFundingProgress, caseId);
        } catch (Exception e) {
            log.error("资金用途审核生成工单异常重试", e);
            return ConsumeStatus.RECONSUME_LATER;
        } finally {
            try {
                if (StringUtils.isNotBlank(tryLock)) {
                    redissonHandler.unLock(key, tryLock);
                }
            } catch (Exception e) {
                log.info("释放锁异常", e);
            }
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }


    private void realConsumer(CrowdFundingProgress crowdFundingProgress, Integer caseId) {
        //判断生成哪种类型的工单
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(crowdFundingProgress.getActivityId());
        if (crowdfundingInfo == null) {
            log.warn("case not exist:{}", JSON.toJSONString(crowdFundingProgress));
            return;
        }
        //插入记录
        insertFundUseDetail(crowdFundingProgress, caseId, crowdFundingProgress.getId());
        //生成工单
        long workOrderId = createWorkOrder(crowdfundingInfo, String.valueOf(crowdFundingProgress.getId()));
        // 如果有催票据信息，更新外呼记录上传次数信息
        updateWebCallInfo(caseId);

        // 关闭未分配的催票据上传工单
        promoteBillService.closeLastPromoteWork(caseId);

        try {
            //案例举报处理的最新结果标记了分批打款，标记分批打款之后该案例生成资金用途审核工单的时候，企微提醒
            awoke(workOrderId, caseId);
        } catch (Exception e) {
            log.error("案例举报处理的最新结果标记了分批打款，标记分批打款之后该案例生成资金用途审核工单的时候，企微提醒失败 caseId:{}", caseId, e);
        }

    }

    private void updateWebCallInfo(Integer caseId) {

        Integer publishNum = promoteWebCallDao.selectPublishNumByCaseId(caseId);
        if (publishNum != null) {
            if (publishNum == 0) {
                promoteWebCallDao.updateByCaseId(caseId, DateUtil.getCurrentDateStr(), publishNum + 1);
            } else {
                promoteWebCallDao.updatePublishNumByCaseId(caseId, publishNum + 1);
            }
        }
    }

    private long createWorkOrder(CrowdfundingInfo crowdfundingInfo, String fundUseProgressId) {
        FeignResponse<CfDrawCashLimit> drawCashLimitFeignResponse = cfFinanceDrawCashFeignClient.getDrawCashLimit(crowdfundingInfo.getId());
        Integer haseLimit = Optional.ofNullable(drawCashLimitFeignResponse)
                .map(FeignResponse::getData)
                .map(CfDrawCashLimit::getHasLimit)
                .orElse(0);
        Integer totalLimitAmount = Optional.ofNullable(drawCashLimitFeignResponse)
                .map(FeignResponse::getData)
                .map(CfDrawCashLimit::getTotalLimitAmount)
                .orElse(0);
        CfAmountInfoVo amountInfo = iFinanceDelegate.getFundraiserAmountInfo(crowdfundingInfo.getInfoId());
        Integer alreadyDrawCashAmount = Optional.ofNullable(amountInfo)
                .map(CfAmountInfoVo::getAlreadyDrawCashAmountInFen)
                .orElse(0);
        Response<WorkOrderVO> workOrderVOResponse = cfWorkOrderClient.getLastWorkOrderByTypes(crowdfundingInfo.getId(), WorkOrderType.REPORT_TYPES);
        WorkOrderVO workOrderVO = Optional.ofNullable(workOrderVOResponse)
                .map(Response::getData)
                .orElse(null);
        double amount = (double) alreadyDrawCashAmount / (double) totalLimitAmount;
        if (haseLimit == 1 && Objects.nonNull(workOrderVO) && amount >= reportSplitDrawLimitRate) {
            WorkOrderCreateParam workOrderCreateParam = new WorkOrderCreateParam();
            workOrderCreateParam.setOrderType(WorkOrderType.report_split_draw.getType());
            workOrderCreateParam.setCaseId(crowdfundingInfo.getId());
            workOrderCreateParam.setOrderlevel(OrderLevel.D.getType());
            workOrderCreateParam.addExt(OrderExtName.fundUseProgressId, fundUseProgressId);
            Response<Long> longResponse = workOrderCoreFeignClient.create(workOrderCreateParam);
            log.info("CfFundUseCreateWorkConsumer createWorkOrder success {}", longResponse);
            return Optional.ofNullable(longResponse)
                    .map(Response::getData)
                    .orElse(0L);
        }
        return fundUseService.createFundUseWorkOrder(crowdfundingInfo.getId(), fundUseProgressId, WorkOrderType.funduseshenhe.getType());
    }

    private void insertFundUseDetail(CrowdFundingProgress crowdFundingProgress, Integer caseId, Integer fundingProgressId) {
        String content = crowdFundingProgress.getContent();
        List<String> images = Splitter.on(',').splitToList(crowdFundingProgress.getImageUrls());
        List<CfFundUseDetailDO> detailDOList = Lists.newArrayList();
        if (StringUtils.isNotBlank(content)) {
            CfFundUseDetailDO detailDO = new CfFundUseDetailDO();
            detailDO.setContent(content);
            detailDO.setContentType(CfFundUseDetailDO.FundUseContentType.comment.getCode());
            detailDO.setCaseId(caseId);
            detailDO.setFundUseProgressId(fundingProgressId);
            detailDOList.add(detailDO);
        }
        for (String image : images) {
            CfFundUseDetailDO detailDO = new CfFundUseDetailDO();
            detailDO.setContent(image);
            detailDO.setContentType(CfFundUseDetailDO.FundUseContentType.image.getCode());
            detailDO.setCaseId(caseId);
            detailDO.setFundUseProgressId(fundingProgressId);
            detailDOList.add(detailDO);
        }
        detailBiz.batchInsert(detailDOList);
    }

    private void awoke(long workOrderId, int caseId) {
        if (workOrderId == 0L) {
            return;
        }

        ReportPayMethodEnum payMethodEnum = reportScheduleService.getPayMethodByCaseId(caseId);
        int code = Optional.ofNullable(payMethodEnum).map(ReportPayMethodEnum::getCode).orElse(0);
        if (code != ReportPayMethodEnum.PAY_IN_BATCH.getCode()) {
            return;
        }

        Response<WorkOrderVO> response = cfWorkOrderClient.getLastWorkOrderByTypes(caseId, WorkOrderType.REPORT_TYPES);
        WorkOrderVO workOrderVO = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(null);
        long operatorId = 0L;
        if (Objects.nonNull(workOrderVO) && workOrderVO.getOperatorId() != 0L) {
            operatorId = workOrderVO.getOperatorId();
        } else {
            Response<List<WorkOrderVO>> result = cfWorkOrderClient.listByCaseIdAndTypeAndResult(caseId, WorkOrderType.REPORT_TYPES, Lists.newArrayList());
            List<WorkOrderVO> workOrderVOList = Optional.ofNullable(result).filter(Response::ok).map(Response::getData).orElse(null);
            if (CollectionUtils.isNotEmpty(workOrderVOList)) {
                operatorId = workOrderVOList.stream()
                        .filter(v -> v.getOperatorId() != 0L)
                        .sorted(Comparator.comparing(WorkOrderVO::getWorkOrderId).reversed())
                        .map(WorkOrderVO::getOperatorId)
                        .findFirst()
                        .orElse(0L);
            }
        }

        String mis = StringUtils.EMPTY;
        if (operatorId != 0L) {
            AuthRpcResponse<String> authRpcResponse = seaAccountClientV1.getMisByLongUserId(operatorId);
            mis = StringUtils.defaultString(authRpcResponse.getResult());
        }

        String[] at = new String[]{"liyuanyuan", "gaoxu", "liruixin", "zhonglian", mis};

        String content = "【用户医疗花费票据已上传，请跟进】" + "\n" + "【案例ID】：" + caseId;

        String key = "e1c9909e-cd33-49fb-a198-8110d01dd908";

        if (applicationService.isDevelopment()) {
            key = "d3171907-5c7e-491a-895c-ab9b175e4aa9";
        }

        //发送到企业微信
        AlarmBotService.sentText(key, content, at, null);

    }
}
