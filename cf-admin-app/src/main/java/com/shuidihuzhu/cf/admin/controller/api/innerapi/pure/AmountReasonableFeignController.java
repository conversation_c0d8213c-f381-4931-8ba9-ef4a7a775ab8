package com.shuidihuzhu.cf.admin.controller.api.innerapi.pure;

import com.shuidihuzhu.cf.biz.amount.AmountReasonableBiz;
import com.shuidihuzhu.cf.client.adminpure.feign.AmountReasonableFeignClient;
import com.shuidihuzhu.cf.client.adminpure.model.amount.CfAmountReasonableTask;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.service.amount.AmountReasonableService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2022/8/24 19:38
 * @Description:
 */
@RestController
public class AmountReasonableFeignController implements AmountReasonableFeignClient {

    @Resource
    private AmountReasonableService amountReasonableService;

    @Resource
    private AmountReasonableBiz amountReasonableBiz;

    @Override
    public OperationResult<Integer> getCommonRuleResult(int caseId, int taskType) {
        return OperationResult.success(amountReasonableService.getCommonRuleResult(caseId, taskType));
    }

    @Override
    public OperationResult<Void> firstIssueTask(int caseId, int taskType) {
        amountReasonableService.firstIssueTask(caseId, taskType);
        return OperationResult.success();
    }

    @Override
    public OperationResult<Void> submitTask(CfAmountReasonableTask cfAmountReasonableTask) {
        amountReasonableService.submitTask(cfAmountReasonableTask);
        return OperationResult.success();
    }

    @Override
    public OperationResult<List<CfAmountReasonableTask>> getTaskByCaseId(int caseId) {
        return OperationResult.success(amountReasonableBiz.getTaskByCaseId(caseId));
    }

    @Override
    public OperationResult<CfAmountReasonableTask> getTaskDetail(String taskInfoId) {
        return OperationResult.success(amountReasonableBiz.getByTaskInfoId(taskInfoId));
    }
}
