package com.shuidihuzhu.cf.admin.controller.api.innerapi;

import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.service.tag.impl.CfCaseLabelServiceImpl;
import com.shuidihuzhu.cf.service.tag.impl.CfThreeBodyServiceImpl;
import com.shuidihuzhu.client.cf.admin.client.CfThreeBodyFeignClient;
import com.shuidihuzhu.client.cf.admin.model.ThreeBodyTag;
import com.shuidihuzhu.client.cf.growthtool.model.CaseLabelParam;
import com.shuidihuzhu.client.cf.growthtool.model.RuleJudge;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2022/8/24 7:26 下午
 */
@Slf4j
@RestController
public class CfThreeBodyFeignController implements CfThreeBodyFeignClient {

    @Resource
    private CfThreeBodyServiceImpl cfThreeBodyService;
    @Resource
    private CfCaseLabelServiceImpl cfCaseLabelService;

    @Override
    public Response<Boolean> judgeIsThreeBodyTag(Integer caseId) {
        if (Objects.isNull(caseId)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        return NewResponseUtil.makeSuccess(cfThreeBodyService.judgeIsThreeBodyTag(caseId));
    }

    @Override
    public Response<List<ThreeBodyTag>> judgeThreeBodyTagByCaseIds(List<Integer> caseIds) {

        if (CollectionUtils.isEmpty(caseIds)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        return NewResponseUtil.makeSuccess(cfCaseLabelService.getCaseLabelByCaseIds(caseIds));
    }

    @Override
    public Response<Boolean> judgeThreeBodyRule(RuleJudge ruleJudge) {

        if (Objects.isNull(ruleJudge)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        Boolean result = false;
        try {
            result = cfCaseLabelService.remindCaseLabel(ruleJudge);
        } catch (Exception e) {
            log.error("judgeThreeBodyRule error, ruleJudge:{}", ruleJudge, e);
        }
        log.info("judgeThreeBodyRule result:{}, ruleJudge:{}", result, ruleJudge);

        return NewResponseUtil.makeSuccess(result);
    }

    @Override
    public Response<String> judgeCaseLabelRule(RuleJudge ruleJudge) {
        if (Objects.isNull(ruleJudge)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        String result = "";
        try {
            result = cfCaseLabelService.judgeCaseLabel(ruleJudge);
        } catch (Exception e) {
            log.error("judgeThreeBodyRule error, ruleJudge:{}", ruleJudge, e);
        }
        log.info("judgeCaseLabelRule result:{}, ruleJudge:{}", result, ruleJudge);

        return NewResponseUtil.makeSuccess(result);
    }

    @Override
    public Response<ThreeBodyTag> getCaseLabel(Integer caseId, Integer predictDonateCount) {
        return NewResponseUtil.makeSuccess(cfCaseLabelService.getCaseLabel(caseId, predictDonateCount));
    }

    @Override
    public Response<Boolean> judgeMajorCaseByParam(CaseLabelParam caseLabelParam) {
        return NewResponseUtil.makeSuccess(cfCaseLabelService.judgeMajorCase(caseLabelParam));
    }

    @Override
    public Response<List<ThreeBodyTag>> getAllLabelInfo() {
        return NewResponseUtil.makeSuccess(cfCaseLabelService.getAllLabelInfo());
    }

}
