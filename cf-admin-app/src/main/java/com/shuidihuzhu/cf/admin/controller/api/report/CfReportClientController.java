package com.shuidihuzhu.cf.admin.controller.api.report;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderReportBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.report.AdminCfInfoLostContactService;
import com.shuidihuzhu.cf.biz.risk.IAllUserBehaviorService;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.ReportSourceEnum;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderReport;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.crowdfunding.report.CfReportService;
import com.shuidihuzhu.client.cf.admin.model.AdminMarkReport;
import com.shuidihuzhu.client.cf.admin.model.AdminMarkReportParam;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @author: lixiaoshuang
 * @create: 2020-02-25 18:48
 **/
@RestController
@RequestMapping(path = "innerapi/cf/admin/report")
public class CfReportClientController {

    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private CfReportService cfReportService;
    @Autowired
    private AdminWorkOrderReportBiz adminWorkOrderReportBiz;
    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;
    @Autowired
    private AdminCfInfoLostContactService adminCfInfoLostContactService;


    @RequestMapping(path = "mark-report", method = RequestMethod.POST)
    public Response<Map<String, Integer>> markReport(@RequestBody AdminMarkReportParam adminMarkReportParam) {
        if (StringUtils.isEmpty(adminMarkReportParam.getContent())) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<String> reportTypeList = Lists.newArrayList(adminMarkReportParam.getReportTypes().split(","));
        if (CollectionUtils.isEmpty(reportTypeList)) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(adminMarkReportParam.getCaseId());
        if (Objects.isNull(crowdfundingInfo)) {
            return NewResponseUtil.makeError(ErrorCode.CF_NOT_FOUND);
        }

        if (adminMarkReportParam.isRealNameReport() && (StringUtils.isEmpty(adminMarkReportParam.getReporterName())
                || StringUtils.isEmpty(adminMarkReportParam.getReporterIdentity()))) {
            return NewResponseUtil.makeError(CfErrorCode.REPOERT_REAL_NAME_VALIDATE_ERROR);
        }
        int reportId = cfReportService.markReport(adminMarkReportParam.getCaseId(), adminMarkReportParam.getContent(),
                adminMarkReportParam.getReporterName(), adminMarkReportParam.getReporterMobile(), adminMarkReportParam.getReportTypes(),
                crowdfundingInfo, adminMarkReportParam.isRealNameReport(), adminMarkReportParam.getReporterIdentity(),
                adminMarkReportParam.getOperatorId(),adminMarkReportParam.getReportChannel(),
                StringUtils.trimToEmpty(adminMarkReportParam.getReportChannelOther()),adminMarkReportParam.getImageUrls(),
                ReportSourceEnum.ADMIN,null);
        return NewResponseUtil.makeSuccess(Map.of("reportId", reportId));
    }

    @RequestMapping(path = "get-mark-report-info", method = RequestMethod.POST)
    public Response<AdminMarkReport> getMarkReportInfo(@RequestParam("caseId") int caseId, @RequestParam("reportId") int reportId) {
        if (caseId <= 0 || reportId <= 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        boolean hasLost = adminCfInfoLostContactService.hasLost(crowdfundingInfo.getInfoId());
        AdminWorkOrderReport adminWorkOrderReport = adminWorkOrderReportBiz.findByCaseIdAndReportId(caseId, reportId);
        AdminMarkReport adminMarkReport = new AdminMarkReport();
        adminMarkReport.setCaseId(caseId);
        adminMarkReport.setReportId(reportId);
        adminMarkReport.setHasLost(hasLost);
        if (Objects.nonNull(adminWorkOrderReport)) {
            adminMarkReport.setNew(false);
            adminMarkReport.setWorkOrderId(adminWorkOrderReport.getWorkOrderId());
            adminMarkReport.setHandleStatus(adminWorkOrderReport.getDealResult());
            return NewResponseUtil.makeSuccess(adminMarkReport);
        }
        //查新工单
        Response<List<WorkOrderExt>> listResponse = cfWorkOrderClient.queryExtByCase(caseId, WorkOrderType.REPORT_TYPES,
                OrderExtName.reportId.getName(), String.valueOf(reportId));
        if (listResponse.ok() && CollectionUtils.isNotEmpty(listResponse.getData())) {
            List<WorkOrderExt> workOrderExts = listResponse.getData();
            WorkOrderExt workOrderExt = workOrderExts.get(0);
            Response<WorkOrderVO> workOrderById = cfWorkOrderClient.getWorkOrderById(workOrderExt.getWorkOrderId());
            if (workOrderById.ok()) {
                WorkOrderVO workOrderVO = workOrderById.getData();
                adminMarkReport.setNew(true);
                adminMarkReport.setWorkOrderId(workOrderVO.getWorkOrderId());
                adminMarkReport.setHandleStatus(workOrderVO.getHandleResult());
            }
        }
        return NewResponseUtil.makeSuccess(adminMarkReport);
    }

    @Autowired
    private IAllUserBehaviorService behaviorService;

}
