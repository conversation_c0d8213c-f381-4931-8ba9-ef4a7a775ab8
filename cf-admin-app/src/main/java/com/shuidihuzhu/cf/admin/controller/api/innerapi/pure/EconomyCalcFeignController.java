package com.shuidihuzhu.cf.admin.controller.api.innerapi.pure;

import com.shuidihuzhu.cf.biz.crowdfunding.CfRiskRuleBiz;
import com.shuidihuzhu.cf.client.adminpure.enums.WorkOrderExtContentTypeEnum;
import com.shuidihuzhu.cf.client.adminpure.feign.EconomyCalcFeignClient;
import com.shuidihuzhu.cf.client.adminpure.model.rule.EconomyModel;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.service.workorder.WorkOrderExtService;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@Slf4j
public class EconomyCalcFeignController  implements EconomyCalcFeignClient {

    @Autowired
    private CfRiskRuleBiz cfRiskRuleBiz;

    @Autowired
    private WorkOrderExtService workOrderExtService;

    @Override
    public OperationResult<List<EconomyModel>> getEconomyMModelsByCaseId(int caseId) {
        List<EconomyModel> list = workOrderExtService.listByCaseIdAndClazz(caseId, WorkOrderExtContentTypeEnum.RISK_RULE_SNAPSHOT,EconomyModel.class);

        if(CollectionUtils.isEmpty(list)){
            return OperationResult.success();
        }

        list = list.stream()
                .peek(economyModel -> economyModel.setConvertResult(cfRiskRuleBiz.assembleTipInfo(economyModel)))
                .sorted(Comparator.comparing(EconomyModel::getTime).reversed())
                .collect(Collectors.toList());

        return OperationResult.success(list);
    }

    @Override
    public OperationResult<EconomyModel> getLatestEconomyModelByCaseId(int caseId) {

        EconomyModel economyModel = workOrderExtService.getByCaseIdAndClazz(caseId, WorkOrderExtContentTypeEnum.RISK_RULE_SNAPSHOT, EconomyModel.class);
        if(economyModel == null){
            return OperationResult.success();
        }

        economyModel.setConvertResult(cfRiskRuleBiz.assembleTipInfo(economyModel));

        return OperationResult.success(economyModel);
    }
}
