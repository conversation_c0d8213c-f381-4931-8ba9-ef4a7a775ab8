package com.shuidihuzhu.cf.admin.mq;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.ConfigService;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingReportBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.IReportCommunicaterListService;
import com.shuidihuzhu.cf.client.adminpure.constants.ReportCons;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cf.enums.crowdfunding.CfReportPageEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfReportRelationEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportCommunicaterDO;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport;
import com.shuidihuzhu.cf.service.crowdfunding.report.CfReportService;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.cf.service.crowdfunding.SeaAccountService;
import com.shuidihuzhu.cf.service.report.ReportOperationService;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.client.cf.workorder.CfReportWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderStaffClient;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.StaffStatusEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Array;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @time 2019/12/26 下午5:07
 * @desc
 */
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.CF_WORK_ORDER_REPORT_NEW_LOGIC, tags = MQTagCons.CF_WORK_ORDER_REPORT_NEW_LOGIC, topic = MQTopicCons.CF)
public class AdminWorkOrderReportNewLogicConsumer implements MessageListener<Map<String, String>> {
    @Resource(name = "cfRedissonHandler")
    private RedissonHandler cfRedissonHandler;

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Autowired
    private CfWorkOrderStaffClient cfWorkOrderStaffClient;

    @Autowired
    private CfReportWorkOrderClient cfReportWorkOrderClient;

    @Autowired
    private AdminCrowdfundingReportBiz adminCrowdfundingReportBiz;

    @Autowired
    private IReportCommunicaterListService reportCommunicaterListService;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    @Autowired
    private CfReportService cfReportService;

    @Autowired
    private ReportOperationService reportOperationService;

    @Autowired
    private SeaAccountService seaAccountService;

    @Autowired
    private ApproveRemarkOldService approveRemarkOldService;

    @Autowired(required = false)
    private Producer producer;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<Map<String, String>> mqMessage) {
        if(Objects.isNull(mqMessage) || MapUtils.isEmpty(mqMessage.getPayload())){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        Map<String, String> reportMap = mqMessage.getPayload();
        String infoUuid = reportMap.get("infoUuid");
        Integer reportId = Integer.valueOf(reportMap.get("reportId"));
        String amountRaisedIndex = reportMap.get("amountRaisedIndex");

        log.info("CF_WORK_ORDER_REPORT_NEW_LOGIC 回调 param {}", reportMap);

        if(StringUtils.isEmpty(infoUuid) || Objects.isNull(reportId) || reportId <= 0){
            log.error("CF_WORK_ORDER_REPORT_NEW_LOGIC 消息参数异常 param {}", reportMap);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        CrowdfundingInfo cfInfo = crowdfundingDelegate.getCrowdfundingInfoByInfoId(infoUuid);
        if(Objects.isNull(cfInfo)){
            log.error("CF_WORK_ORDER_REPORT_NEW_LOGIC 查询案例信息失败 infoUuid {}", infoUuid);
            return ConsumeStatus.RECONSUME_LATER;
        }

        int caseId = cfInfo.getId();

        CrowdfundingReport report = adminCrowdfundingReportBiz.query(caseId, reportId);
        if(Objects.isNull(report)){
            log.error("CF_WORK_ORDER_REPORT_NEW_LOGIC 没查询到举报条目 param {}", reportMap);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        log.info("ReportNewLogicConsumer param caseId:{},infoUuid:{},reportId:{}", caseId, infoUuid, reportId);

        String identifier = "";
        String lockName = "reportWorkOrderNewLogic_" + caseId;
        try {
            //防并发 这里必须保证每次举报都能得到处理，所以waitTime > leaseTime
            identifier = cfRedissonHandler.tryLock(lockName, 11 * 1000L,10 * 1000L);
            if (StringUtils.isBlank(identifier)) {
                log.info("ReportNewLogicConsumer redislock empty caseId:{},infoUuid:{},reportId:{}", caseId, infoUuid, reportId);
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            //幂等处理
            Response<List<WorkOrderExt>> extRes = cfWorkOrderClient.queryExtByCase(caseId, WorkOrderType.REPORT_TYPES, OrderExtName.reportId.getName(), String.valueOf(reportId));
            if(ErrorCode.SUCCESS.getCode() == extRes.getCode() && CollectionUtils.isNotEmpty(extRes.getData())){
                log.info("ReportNewLogicConsumer repeat handle caseId:{},infoUuid:{},reportId:{}", caseId, infoUuid, reportId);
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            insertQuestionerInfo(report);

            //已筹金额大于30w自动生成二线工单
            if (StringUtils.equals("amountRaisedIndex", amountRaisedIndex)) {
                return this.createErXianOrder(caseId, reportId);
            }

            //查询该案例的最近一次举报工单
            Response<WorkOrderVO> workOrderRes = cfWorkOrderClient.getLastWorkOrderByTypes(caseId,WorkOrderType.REPORT_TYPES);
            if(ErrorCode.SUCCESS.getCode() != workOrderRes.getCode()){
                log.info("ReportNewLogicConsumer query lastest error caseId:{},infoUuid:{},reportId:{}", caseId, infoUuid, reportId);
                return ConsumeStatus.RECONSUME_LATER;
            }

            WorkOrderVO latestReport = workOrderRes.getData();

            /**
             * 该案例目前没有举报工单，生成"案例首次举报工单"
             */
            if(Objects.isNull(latestReport)){
                ReportWorkOrder workOrder = new ReportWorkOrder();
                workOrder.setCaseId(caseId);
                workOrder.setReportId(reportId);
                workOrder.setOrderType(WorkOrderType.casefirstreport.getType());
                workOrder.setHandleResult(HandleResultEnum.undoing.getType());
                workOrder.setOperatorId(0L);
                workOrder.setDealOperatorId(0L);//案例首次举报工单的处理人随机分配
                workOrder.setComment("生成案例首次举报工单");
                Response<Long> createRes = cfReportWorkOrderClient.createReport(workOrder);
                log.info("ReportNewLogicConsumer createRes caseId:{},reportId:{},result:{}", caseId, reportId,createRes.getCode());
                cfReportService.sendCheckHandleMq(createRes.getData() == null ? 0 : createRes.getData());
                if (createRes.notOk()) {
                    return ConsumeStatus.RECONSUME_LATER;
                }
                reportOperationService.operation(caseId, ReportCons.ActionType.OrderCreate, 0);
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            /**
             * 最新举报工单处理未结束，将本次举报关联到最新举报工单
             */
            HandleResultEnum handleResultEnum = HandleResultEnum.getFromType(latestReport.getHandleResult());
            if(handleResultEnum != HandleResultEnum.noneed_deal && handleResultEnum != HandleResultEnum.end_deal){

                ReportMappingParam mappingParam = new ReportMappingParam();
                mappingParam.setWorkOrderId(latestReport.getWorkOrderId());
                mappingParam.setReportId(reportId);
                Response<Long> mapRes = cfReportWorkOrderClient.mappingReport(mappingParam);
                log.info("ReportNewLogicConsumer mapRes caseId:{},reportId:{},result:{}", caseId, reportId,mapRes.getCode());

                return ErrorCode.SUCCESS.getCode() == mapRes.getCode() ? ConsumeStatus.CONSUME_SUCCESS : ConsumeStatus.RECONSUME_LATER;
            }


            /**
             * 最新举报工单处理人在线则生成"案例历史举报工单"，直接分配给处理人
             */
            long operatorId = latestReport.getOperatorId();

            Response<StaffStatus> staffStatusRes = cfWorkOrderStaffClient.getStaffStatus(latestReport.getOrderType(), operatorId);
            if(Objects.isNull(staffStatusRes) || Objects.isNull(staffStatusRes.getData())){
                return ConsumeStatus.RECONSUME_LATER;
            }

            StaffStatus staffStatus = staffStatusRes.getData();

            boolean isHistoryType = StaffStatusEnum.online.getType() == staffStatus.getStaffStatus() && 1 == staffStatus.getAutoAllocation();
//            WorkOrderType workOrderType = isHistoryType ? WorkOrderType.casehistoryreport : WorkOrderType.casefirstreport;
            long dealOperatorId = isHistoryType ? operatorId : 0L;
            String comment = "生成案例历史举报工单";

            ReportWorkOrder reportWorkOrder = new ReportWorkOrder();
            reportWorkOrder.setCaseId(caseId);
            reportWorkOrder.setReportId(reportId);
            reportWorkOrder.setOrderType(WorkOrderType.casehistoryreport.getType());
            reportWorkOrder.setHandleResult(HandleResultEnum.undoing.getType());
            reportWorkOrder.setOperatorId(0L);
            //案例首次举报工单的处理人随机分配;案例历史举报工单的处理人为最近一个工单的处理人(在线)
            reportWorkOrder.setDealOperatorId(dealOperatorId);
            reportWorkOrder.setComment(comment);
            Response<Long> createNewRes = cfReportWorkOrderClient.createReport(reportWorkOrder);
            log.info("ReportNewLogicConsumer createNewRes caseId:{},reportId:{},result:{}", caseId, reportId,createNewRes.getCode());
            cfReportService.sendCheckHandleMq(createNewRes.getData() == null ? 0 : createNewRes.getData());
            if (createNewRes.notOk()) {
                return ConsumeStatus.RECONSUME_LATER;
            }
            reportOperationService.operation(caseId, ReportCons.ActionType.OrderCreate, 0);
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (Exception e){
            log.error("ReportNewLogicConsumer create reportWorkOrder error", e);
        } finally {
            if(StringUtils.isNotEmpty(identifier)) {
                cfRedissonHandler.unLock(lockName, identifier);
            }
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    //每个质疑人加进质疑人跟进页面的沟通列表
    private void insertQuestionerInfo(CrowdfundingReport report){
        if(Objects.isNull(report)){
            return;
        }

        String mobile = report.getEncryptContact();
        CfReportCommunicaterDO communicater = reportCommunicaterListService.queryByMobile(report.getActivityId(), report.getId(), mobile);
        if(Objects.nonNull(communicater)){
            return;
        }

        CfReportCommunicaterDO communicaterDO = new CfReportCommunicaterDO();
        communicaterDO.setCaseId(report.getActivityId());
        communicaterDO.setReportId(report.getId());
        communicaterDO.setType(CfReportPageEnum.QUESTIONER.getKey());
        communicaterDO.setRelationKey(CfReportRelationEnum.QUESTIONER.getKey());
        communicaterDO.setRelationValue(CfReportRelationEnum.QUESTIONER.getValue());
        communicaterDO.setMobile(mobile);
        communicaterDO.setOperatorId(1L);
        communicaterDO.setManualAdd(false);

        reportCommunicaterListService.insert(communicaterDO);
    }

    private ConsumeStatus createErXianOrder(int caseId, int reportId) {
        Response<List<WorkOrderVO>> response = cfWorkOrderClient.listByCaseIdAndTypeAndResult(caseId, WorkOrderType.REPORT_TYPES, Lists.newArrayList(HandleResultEnum.undoing.getType(), HandleResultEnum.doing.getType(),
                HandleResultEnum.reach_agree.getType(), HandleResultEnum.later_doing.getType(), HandleResultEnum.done.getType()));
        List<WorkOrderVO> workOrderVOList = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(null);
        if (Objects.isNull(workOrderVOList)) {
            log.info("ReportNewLogicConsumer createErXianOrder error caseId:{}", caseId);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        //当前没有未处理完成的举报：
        //该案例从来没有被举报过
        //该案例被举报过，但是举报工单全部都处理完成了
        if (CollectionUtils.isEmpty(workOrderVOList) || CollectionUtils.size(workOrderVOList.stream().filter(v ->
                v.getHandleResult() == HandleResultEnum.done.getType()).collect(Collectors.toList())) == CollectionUtils.size(workOrderVOList)) {
            this.createOrder(caseId, reportId);
            log.info("当前没有未处理完成的举报 caseId:{} reportId:{}", caseId, reportId);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        //当前有未处理完成的二线举报工单：
        //该案例当前有一个二线举报工单，且其状态等于未分配、稍后处理、处理中、达成一致
        if (CollectionUtils.isNotEmpty(workOrderVOList.stream().filter(v ->
                v.getOrderType() == WorkOrderType.up_grade_second.getType()).filter(v ->
                v.getHandleResult() != HandleResultEnum.done.getType()).collect(Collectors.toList()))) {
            log.info("当前有未处理完成的二线举报工单 caseId:{} reportId:{}", caseId, reportId);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        //当前有未处理完成的失联工单：
        //且其状态等于未分配、处理中
        List<WorkOrderVO> lostContactOrderList = workOrderVOList.stream().filter(v ->
                v.getOrderType() == WorkOrderType.lost_report.getType()).filter(v ->
                v.getHandleResult() == HandleResultEnum.undoing.getType() ||
                        v.getHandleResult() == HandleResultEnum.doing.getType()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(lostContactOrderList)) {
            WorkOrderVO workOrderVO = lostContactOrderList.get(0);
            int operatorId = (int) workOrderVO.getOperatorId();
            //获取操作人mis
            String operatorMis = seaAccountService.getMis(operatorId);
            String content = this.getWxSend(caseId);
            //发送到企业微信
            AlarmBotService.sentText("7a970ee0-0f34-49f2-8798-445ece129818",
                    content, getMentionedList(operatorMis), null);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        //当前有未分配的一线举报工单：
        //该案例当前有一个一线举报工单，且其状态等于未分配
        List<WorkOrderVO> undoingOrderList = workOrderVOList.stream().filter(v ->
                v.getOrderType() == WorkOrderType.casefirstreport.getType() || v.getOrderType() == WorkOrderType.casehistoryreport.getType()).filter(v ->
                v.getHandleResult() == HandleResultEnum.undoing.getType()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(undoingOrderList)) {
            log.info("当前有未分配的一线举报工单 caseId:{} reportId:{}", caseId, reportId);
            WorkOrderVO workOrderVO = undoingOrderList.get(0);
            ReportWorkOrder reportWorkOrder = new ReportWorkOrder();
            reportWorkOrder.setId(workOrderVO.getWorkOrderId());
            reportWorkOrder.setHandleResult(HandleResultEnum.end_deal_upgrade.getType());
            reportWorkOrder.setReportId(reportId);
            reportWorkOrder.setOrderType(workOrderVO.getOrderType());
            reportWorkOrder.setCaseId(caseId);
            try {
                producer.send(new Message<>(MQTopicCons.CF, WorkOrderMQ.report_close_order_create_new_order_mq,
                        WorkOrderMQ.report_close_order_create_new_order_mq + "_" + System.currentTimeMillis(), reportWorkOrder));
                approveRemarkOldService.add(caseId, 0, "系统自动升级二线：已筹金额≥30万");
            } catch (Exception e) {
                log.error("mq发送失败 caseId:{}", caseId, e);
            }
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        //当前有处理中的一线举报工单：
        //该案例当前有一个一线举报工单，且其状态等于稍后处理、处理中、达成一致
        List<WorkOrderVO> doingOrderList = workOrderVOList.stream().filter(v ->
                v.getOrderType() == WorkOrderType.casefirstreport.getType() ||
                        v.getOrderType() == WorkOrderType.casehistoryreport.getType()).filter(v ->
                v.getHandleResult() == HandleResultEnum.doing.getType() ||
                        v.getHandleResult() == HandleResultEnum.reach_agree.getType() ||
                        v.getHandleResult() == HandleResultEnum.later_doing.getType()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(doingOrderList)) {
            WorkOrderVO workOrderVO = doingOrderList.get(0);
            int operatorId = (int) workOrderVO.getOperatorId();
            //获取操作人mis
            String operatorMis = seaAccountService.getMis(operatorId);
            String content = this.getWxSend(caseId);
            //发送到企业微信
            String[] mentioned_list = getMentionedList(operatorMis);
            AlarmBotService.sentText("7a970ee0-0f34-49f2-8798-445ece129818",
                    content, mentioned_list, null);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private String[] getMentionedList(String operatorMis) {
        final String defaultValue = "liyuanyuan,gaoxu,chenmengpu,yangkunlin";
        final String key = "apollo.report.notice.auto_report";
        final String noticeStr = ConfigService.getAppConfig().getProperty(key, defaultValue);

        final ArrayList<String> ls = Lists.newArrayList(StringUtils.split(noticeStr, ","));
        ls.add(0, operatorMis);
        return ls.toArray(new String[]{});
    }

    private String getWxSend(int caseId) {
        return "\n发送时间：" + DateUtil.getCurrentDateStr() +
                "\n案例ID：" + caseId +
                "\n问题描述：" + "该案例已筹金额≥30万，已自动标记举报，请优先处理并评估是否升级二线\n";
    }

    private void createOrder(int caseId, int reportId) {
        ReportWorkOrder workOrder = new ReportWorkOrder();
        workOrder.setCaseId(caseId);
        workOrder.setReportId(reportId);
        workOrder.setOrderType(WorkOrderType.up_grade_second.getType());
        workOrder.setHandleResult(HandleResultEnum.undoing.getType());
        workOrder.setOperatorId(0L);
        workOrder.setDealOperatorId(0L);
        workOrder.setComment("生成二线工单");
        Response<Long> createRes = cfReportWorkOrderClient.createReport(workOrder);
        if (createRes.notOk()) {
            return;
        }
        approveRemarkOldService.add(caseId, 0, "系统自动升级二线：已筹金额≥30万");
    }

}