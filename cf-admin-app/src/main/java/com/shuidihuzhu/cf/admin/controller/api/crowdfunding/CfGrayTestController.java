package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.delegate.commonservice.ICommonServiceDelegate;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * @author: lixuan
 * @date: 2018/3/25 11:18
 */
@RestController
@RequestMapping(path = "/admin/cf/gray/test")
public class CfGrayTestController {

    @Autowired
    private ICommonServiceDelegate commonServiceDelegate;

    @PostMapping("/mode")
    public Response getMode(String code, String selfTag) {

        if(StringUtils.isEmpty(selfTag) || StringUtils.isEmpty(code)) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }

        long contextUserId = ContextUtil.getAdminUserId();
        long userId = 0;
        if (contextUserId > 0) {
            userId = contextUserId;
        }

        int mode = this.commonServiceDelegate.getGrayTestBySelfTag(code, userId, selfTag);
        mode = mode < 0 ? 0 : mode;
        Map<String, String> response = Maps.newHashMap();
        response.put("mode", mode + "");
        return NewResponseUtil.makeSuccess(response);
    }
}
