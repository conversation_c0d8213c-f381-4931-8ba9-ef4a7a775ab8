package com.shuidihuzhu.cf.admin.controller.api.record;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.client.adminpure.enums.WorkOrderExtContentTypeEnum;
import com.shuidihuzhu.cf.client.adminpure.model.record.CfInitialAuditRecordVo;
import com.shuidihuzhu.cf.domain.cf.WorkOrderExt;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.service.workorder.WorkOrderExtService;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditCaseDetail;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RequestMapping("/admin/cf/record/initial-audit-record")
@RestController
@Slf4j
public class CfInitialAuditRecordController {
    @Autowired
    private WorkOrderExtService workOrderExtService;

    @PostMapping("/query")
    @RequiresPermission("initial-audit-record:query")
    public Response<List<CfInitialAuditRecordVo>> query(@RequestParam int caseId) {
        if (caseId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
        }
        List<CfInitialAuditRecordVo> res = new ArrayList<>();
        List<WorkOrderExt> workOrderExtList = workOrderExtService.listByCaseIdAndType(caseId, WorkOrderExtContentTypeEnum.INITIAL_AUDIT_FIRST_APPROVE);
        workOrderExtList.forEach(workOrderExt -> {
            InitialAuditCaseDetail.FirstApproveCaseInfo firstApproveCaseInfo = JSON.parseObject(workOrderExt.getContent(), InitialAuditCaseDetail.FirstApproveCaseInfo.class);
            CfInitialAuditRecordVo vo = new CfInitialAuditRecordVo();
            if (firstApproveCaseInfo.getUserRelationType() == UserRelTypeEnum.SELF.getValue()) {
                vo.setApplicant(firstApproveCaseInfo.getPatientRealName());
                vo.setApplicantIdCardMask(firstApproveCaseInfo.getPatientIdCardMask());
            } else {
                vo.setApplicant(firstApproveCaseInfo.getSelfRealName());
                vo.setApplicantIdCardMask(firstApproveCaseInfo.getSelfIdCardMask());
            }
            vo.setPatientIdCardMask(firstApproveCaseInfo.getPatientIdCardMask());
            vo.setPatientBornCardMask(firstApproveCaseInfo.getPatientBornCardMask());
            vo.setPatient(firstApproveCaseInfo.getPatientRealName());
            vo.setCreateTime(workOrderExt.getCreateTime());
            if (firstApproveCaseInfo.getPatientIdType() == UserIdentityType.identity.getCode()) {
                vo.setPatientBornCardMask(null);
            } else if (firstApproveCaseInfo.getPatientIdType() == UserIdentityType.birth.getCode()) {
                vo.setPatientIdCardMask(null);
            }
            res.add(vo);
        });
        return NewResponseUtil.makeSuccess(res);
    }
}
