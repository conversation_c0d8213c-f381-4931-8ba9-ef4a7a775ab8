package com.shuidihuzhu.cf.admin.controller.api.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.admin.impl.AdminCfInitialAuditCheckInfoRefService;
import com.shuidihuzhu.common.web.model.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 上传前置信息中需要校验的部分的参考内容
 * 例：上传真实医院名称列表
 *
 * <AUTHOR>
 */
@Api("上传前置信息中需要校验的部分的参考内容")
@RestController
@Slf4j
@RequestMapping(path = "/admin/cf/initial-audit/util")
public class UploadCfInitialAuditCheckInfoRefController {

    @Resource
    private AdminCfInitialAuditCheckInfoRefService checkInfoRefService;

    @RequiresPermission("initial-audit:upload-check-info")
    @ApiOperation("上传真实医院名称列表")
    @PostMapping("/upload-check-info")
    public Response<Void> uploadCheckInfo(@RequestParam("file") MultipartFile file) {
        String filename = file.getOriginalFilename();
        if (filename == null || (!filename.endsWith("xls") && !filename.endsWith("xlsx"))) {
            return new Response<>(1, "格式不对，上传失败！");
        }
        InputStream is = null;
        try {
            is = file.getInputStream();
        } catch (IOException e) {
            log.error("", e);
        }
        if (is == null) {
            return new Response<>(2, "获取文件出错！");
        }
        EasyExcel.read(is, ExcelData.class, new DataListener(checkInfoRefService)).sheet().doRead();
        return new Response<>(0, "上传成功");
    }

    @Data
    public static class ExcelData {
        @ExcelProperty("name")
        private String name;
    }

    public static class DataListener extends AnalysisEventListener<ExcelData> {

        private static final int BATCH_COUNT = 1000;
        private AdminCfInitialAuditCheckInfoRefService checkInfoRefService;
        private List<ExcelData> dataList = Lists.newArrayList();

        public DataListener(AdminCfInitialAuditCheckInfoRefService checkInfoRefService) {
            this.checkInfoRefService = checkInfoRefService;
        }

        @Override
        public void invoke(ExcelData data, AnalysisContext context) {
            dataList.add(data);
            if (dataList.size() >= BATCH_COUNT) {
                this.saveData();
                dataList.clear();
            }
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            this.saveData();
        }

        private void saveData() {
            List<String> collect = dataList.stream().map(ExcelData::getName).collect(Collectors.toList());
            checkInfoRefService.batchInsertHospitalName(collect);
            log.info("batchInsertHospitalName list={}", JSON.toJSONString(collect));
        }
    }
}
