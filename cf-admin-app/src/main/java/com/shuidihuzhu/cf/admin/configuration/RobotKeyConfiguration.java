package com.shuidihuzhu.cf.admin.configuration;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.enhancer.subject.redislock.RedisLockAop;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;


@Slf4j
@Component
public class RobotKeyConfiguration implements InitializingBean {

    @Override
    public void afterPropertiesSet() throws Exception {
        HashMap<String, String> map = Maps.newHashMap();
        map.put("b8985a27-2d51-4a65-a05a-1e46c071558b", "a588a586-5058-40f1-99df-b60a681478e5");
        map.put("67373970-1d75-4fd1-bf8a-22a61f0ec143", "73537272-cbc9-44e9-a00e-1fb654ab7281");
        map.put("7a970ee0-0f34-49f2-8798-445ece129818", "d2eed4e4-37bc-41cf-9d9b-e2fa9b83987d");
        map.put("e1c9909e-cd33-49fb-a198-8110d01dd908", "13b050b5-2d45-4b9f-a1c0-2c405528d3c0");
        map.put("d3171907-5c7e-491a-895c-ab9b175e4aa9", "4813ddb6-a31c-42c6-ac84-8c2bb62e00b");
        map.put("e5f3acd4-36fb-44c1-9d42-20267bd4310a", "3c1002a3-60e1-425b-8e6e-ef6e745de1c6");
        map.put("a0c714a2-8449-4391-a00f-38ff672138ac", "6104a38c-53e7-41fd-aa34-62187e6a57c1");
        map.put("07077bb0-383b-4dd4-b0ae-97905ac8108d", "3dece3b1-9631-4fa1-9906-d1c8c7d1e566");
        map.put("02ce42ed-d431-4d4d-8eb4-95826ce146b9", "9700a957-d590-48b1-b484-d839fcfa77b1");
        map.put("e54cc7a8-82b3-40d3-995f-73be85153b3e", "bd1bf366-d798-43ee-b9ca-eb0f8a1e8f50");
        map.put("162ad519-f3b1-4e42-a420-ab6289baca7b", "facf694f-baea-427b-9ebe-e520ada17082");
        map.put("d1e98cef-80a0-4cbd-8721-ee69a291db2c", "a1f3e5d3-7a7b-4672-999c-4804e9a98a03");
        map.put("4a16eab1-e7b2-491a-be1d-3ac4298275f9", "2e272860-16a2-40a9-af89-3a922a0dadbb");
        map.put("a39f392d-0c7c-4239-a8ab-5c161d7b817e", "10524dfe-f93d-4e38-99cf-379bbba2d547");
        map.put("f8b23bfb-e6b4-423f-96a5-31f3ec4a425b", "690cc794-4681-464e-aa46-88f2c95685c8");
        map.put("dfa2caf4-fba6-42ec-8ec8-7fe81d928521", "5b0d3c4e-3511-4069-b373-7e6e037dbefb");
        map.put("0c3b6576-fa8e-4f97-807a-5bcee6007658", "246b652f-4122-4e27-a610-e3a68d59dc62");
        AlarmBotService.setFeishu2WeworkKeyMap(map);
    }

}