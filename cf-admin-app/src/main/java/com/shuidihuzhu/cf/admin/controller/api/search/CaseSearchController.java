package com.shuidihuzhu.cf.admin.controller.api.search;

import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.service.admin.search.CaseSearchService;
import com.shuidihuzhu.cf.vo.admin.SimilarCaseInfo;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.cf.admin.model.AIGenerateParam;
import com.shuidihuzhu.client.cf.search.model.SimilarCaseSearchParam;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * @Description: 优秀案例推荐
 * @Author: panghairui
 * @Date: 2024/11/13 3:36 PM
 */
@Slf4j
@RestController
@RequestMapping("/admin/cf/case/search")
public class CaseSearchController {

    @Resource
    private CaseSearchService caseSearchService;

    @ApiOperation("推荐相似优秀案例")
    @PostMapping("similar-case-search")
    @RequiresPermission("search:case-search")
    public Response<List<SimilarCaseInfo>> similarCaseSearch(@RequestParam(value = "diseaseName") String diseaseName,
                                                             @RequestParam(value = "idCard") String idCard) {

        if (StringUtils.isAnyBlank(diseaseName, idCard)) {
            return NewResponseUtil.makeFail("请检查是否已填写身份证号或疾病名称！");
        }

        return NewResponseUtil.makeSuccess(caseSearchService.similarCaseSearch(diseaseName, idCard));
    }

}
