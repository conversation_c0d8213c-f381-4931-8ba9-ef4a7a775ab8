package com.shuidihuzhu.cf.admin.controller.api.workorder;

import com.shuidihuzhu.cf.biz.crowdfunding.WorkOrderAssignBiz;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.async.DeferredResult;


/**
 * <AUTHOR>
 * @DATE 2019/11/22
 */
@RestController
@RequestMapping(path="/admin/workorder/stream")
@Slf4j
@RefreshScope
public class WorkOrderAssignController {

    @Autowired
    private WorkOrderAssignBiz workOrderAssignBiz;

    @Value("${apollo.cf-admin.assign-timeout:30000}")
    private long timeout;



    @RequiresPermission("assign:get")
    @RequestMapping(path = "/get", method = {RequestMethod.GET,RequestMethod.POST})
    public DeferredResult<String> get(Integer userId,Integer orderType){//使用者id，类型；返回值为Model

        //建立DeferredResult对象，设置超时时间，以及超时返回超时结果
        DeferredResult<String> dr = new DeferredResult(timeout,()-> NewResponseUtil.makeSuccess("retry"));

        if (userId == null || orderType == null) {
            log.warn("deferredResult param err,userId={},orderType={}",userId,orderType);
            dr.setErrorResult("retry");
            return dr;
        }

        String key = workOrderAssignBiz.getKey(userId,orderType);

        log.info("Deferred get key={}",key);
        //将DeferredResult放入集合中
        workOrderAssignBiz.addDeferredResult(key,dr);
        //设定DeferredResult的超时回调方法和完成回调方法，超时和完成都会将本次请求产生的DeferredResult从集合中remove
        //因为不论onCompletion还是onTimeout，每次调用结束后都会将DeferredResult对象移除，所以map集合中存放的都是等待请求结果的DeferredResult对象
        dr.onCompletion(()-> { //完成回调
            workOrderAssignBiz.delDeferredResult(key);
            log.info("Deferred key={} onCompletion",key);
        });


        dr.onTimeout(()-> { //超时回调
            workOrderAssignBiz.delDeferredResult(key);
            log.info("Deferred key={} onTimeout",key);
        });

        return dr;
    }

}
