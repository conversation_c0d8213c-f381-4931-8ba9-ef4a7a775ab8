package com.shuidihuzhu.cf.admin.controller.api.workorder;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.admin.controller.api.util.FindTimeRangeUtil;
import com.shuidihuzhu.cf.admin.util.UserUtil;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.risk.model.enums.quality.sampling.QualitySpotSceneEnum;
import com.shuidihuzhu.client.auth.saas.feign.PermissionFeignClient;
import com.shuidihuzhu.client.auth.saas.model.param.PermissionParam;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderQualitySpotClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderStaffClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderTypeFeignClient;
import com.shuidihuzhu.client.cf.workorder.model.RiskQualitySpotWorkOrderUserConfig;
import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderTypeConstants;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderTypeRecord;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2018/12/29
 */
@RestController
@RequestMapping(path="/admin/workorder/staff")
@Slf4j
public class WorkOrderStaffController {

    @Autowired
    private CfWorkOrderStaffClient staffClient;
    @Resource
    private CfWorkOrderQualitySpotClient qualitySpotClient;
    @Resource
    private PermissionFeignClient permissionFeignClient;
    @Resource
    private CfWorkOrderTypeFeignClient cfWorkOrderTypeFeignClient;


    @RequiresPermission("staff:changeStatus")
    @RequestMapping(path = "changeStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Response changeStatus(@RequestParam("staffStatus") String staffStatus ){

        StaffStatus staff = JSON.parseObject(staffStatus,StaffStatus.class);//已检查过
        if (staff.getOperType() == 1) {
            final long userId = ContextUtil.getAdminLongUserId();
            PermissionParam validPermissionParam = PermissionParam.builder()
                    .appCode(AuthSaasContext.getAuthAppCode())
                    .userId(userId)
                    .permissions(Lists.newArrayList("staff:changeStatusByAdmin"))
                    .build();
            Response<Boolean> permissionResp = permissionFeignClient.validUserPermission(validPermissionParam);
            if (permissionResp == null || permissionResp.notOk() || permissionResp.getData() == null) {
                return NewResponseUtil.makeFail("权限校验失败");
            }
            Boolean valid = permissionResp.getData();
            if (!valid) {
                return NewResponseUtil.makeFail("您没有此操作权限");
            }
        }

        log.info("staffStatus={}",staff);
        return  staffClient.changeStatus(staff);
    }

    @RequiresPermission("staff:update-receipt-threshold")
    @RequestMapping(path = "update-receipt-threshold", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response updateReceiptThreshold(@RequestParam("staffStatus") String staffStatus ){

        long adminUserId = ContextUtil.getAdminUserId();
        if(adminUserId <= 0){
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }


        StaffStatus staff = JSON.parseObject(staffStatus,StaffStatus.class);//已检查过
        staff.setOperationId(adminUserId);

        WorkOrderTypeRecord workOrderTypeRecord = cfWorkOrderTypeFeignClient.getByOrderTypeCode(staff.getOrderType()).getData();

        if(Objects.isNull(workOrderTypeRecord)){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        if(Objects.isNull(staff) || staff.getUserId() <= 0 || staff.getReceiptThreshold() < 0){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        return staffClient.updateReceiptThreshold(staff);
    }

    @RequiresPermission("staff:update-auto-allocation")
    @RequestMapping(path = "update-auto-allocation", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response updateAutoAllocation(@RequestParam("staffStatus") String staffStatus ){

        long adminUserId = ContextUtil.getAdminUserId();
        if(adminUserId <= 0){
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        StaffStatus staff = JSON.parseObject(staffStatus, StaffStatus.class);//已检查过
        staff.setOperationId(ContextUtil.getAdminUserId());

        if(!WorkOrderType.REPORT_TYPES.contains(staff.getOrderType())){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        if(Objects.isNull(staff) || staff.getUserId() <= 0 || (0 != staff.getAutoAllocation() && 1 != staff.getAutoAllocation())){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        return staffClient.updateAutoAllocation(staff);
    }

    @RequiresPermission("staff:get-staff-status")
    @RequestMapping(path = "get-staff-status")
    Response<StaffStatus> getStaffStatus(@RequestParam("orderType") int orderType, @RequestParam("userId") long userId){
        log.info("getStaffStatus orderType={} userId={}",orderType,userId);
        return  staffClient.getStaffStatus(orderType,userId);
    }


    @RequiresPermission("staff:get-staff-types")
    @RequestMapping(path = "get-staff-types",method = RequestMethod.POST)
    public Response<List<StaffStatus>> getStaffType(String orderType,long operId){

        log.info("getStaffType orderType={} userId={}",orderType,operId);

        Response<List<StaffStatus>> response = staffClient.getStaffType(orderType,operId);

        return response;

    }

    @RequiresPermission("staff:get-staff-list")
    @RequestMapping(path = "get-staff-list",method = RequestMethod.POST)
    public Response<PageResult<StaffStatus>> getStaffStatusList(String orderType, int pageSize, String paging, long operId,
                                                                @RequestParam(value = "isAll",required = false) boolean isAll,
                                                                @RequestParam(value = "operatorId",required = false,defaultValue = "0") long operatorId,
                                                                @RequestParam(value = "isAllStaff", required = false, defaultValue = "false") boolean isAllStaff,
                                                                int userId) {

        log.info("getStaffStatusList orderType={} userId={},pageSize={},paging={},isAll={},isAllStaff:{}", orderType, operId, pageSize, paging, isAll, isAllStaff);

        if(StringUtils.isBlank(orderType)){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
        }

        //兼容举报处理查询，获取工单下的全部工作人员
        if (isAllStaff) {
            Response<List<StaffStatus>> statusPageResult = staffClient.getStaffStatusByOrderType(orderType);
            List<StaffStatus> staffStatusList = statusPageResult.getData();
            PageResult pageResult = new PageResult();
            pageResult.setPageList(staffStatusList);
            fullQcList(staffStatusList, orderType);
            return ResponseUtil.makeSuccess(pageResult);
        }


        Response<PageResult<StaffStatus>> response = staffClient.getStaffStatusList(orderType,pageSize,paging,operId,isAll,operatorId);
        if (response == null || response.notOk()) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
        }
        // bug-hotfix 返回值里没有orderType
        if (StringUtils.isNotBlank(orderType) && !orderType.contains(",")
                && response.getData() != null  && CollectionUtils.isNotEmpty(response.getData().getPageList())) {

            int orderTypeNum = Integer.valueOf(orderType);
            for (StaffStatus staff : response.getData().getPageList()) {
                staff.setOrderType(orderTypeNum);
            }
        }


        PageResult<StaffStatus> pageResult = response.getData();
        List<StaffStatus> result = Lists.newArrayList();
        if (Objects.nonNull(pageResult) && CollectionUtils.isNotEmpty(pageResult.getPageList())) {
            result = showUser(userId, pageResult.getPageList());
            pageResult.setPageList(result);
        }

        fullQcList(result, orderType);
        return NewResponseUtil.makeSuccess(pageResult);

    }


    private void  fullQcList(List<StaffStatus> list, String orderType) {
        if (CollectionUtils.isEmpty(list) || orderType.contains(",")){
            return;
        }
        int realOrderType = Integer.parseInt(orderType);
        if (!WorkOrderType.QC_WORK_ORDER_LIST.contains(realOrderType)) {
            return;
        }
        List<Long> userIds = list.stream().map(StaffStatus::getUserId).collect(Collectors.toList());
        Response<List<RiskQualitySpotWorkOrderUserConfig>> response
                = qualitySpotClient.listByUserIds(userIds,
                getSceneByWorkOrderType(realOrderType));
        if (response.notOk()){
            return;
        }
        Map<Long, RiskQualitySpotWorkOrderUserConfig> configMap = response.getData()
                .stream().collect(Collectors.toMap(RiskQualitySpotWorkOrderUserConfig::getUserId, Function.identity(), (a, b) -> a));
        list.forEach( v -> {
            RiskQualitySpotWorkOrderUserConfig config  = configMap.get(v.getUserId());
            if (config  == null){
                return;
            }
            v.setDistributionType(config.getDistributionType());
        });
    }

    private long getSceneByWorkOrderType(int orderType) {
        switch (orderType){
            case WorkOrderTypeConstants.QC_COMMON:
                return 3;
            case WorkOrderTypeConstants.QC_WX_1V1:
                return 4;
            case WorkOrderTypeConstants.QC_CALL:
                return 6;
            case WorkOrderTypeConstants.QC_MATERIAL_AUDIT:
                return QualitySpotSceneEnum.MATERIALS_AUDIT_QUALITY_WORK_ORDER.getCode();
            case WorkOrderTypeConstants.QC_HOSPITAL_DEPT:
                return 11;
            default:
                return 0;
        }
    }

    @RequiresPermission("staff:get-staff-permissions")
    @RequestMapping(path = "get-staff-permissions",method = RequestMethod.POST)
    public Response<Set<StaffStatus>> getStaffPermissions() {

        return staffClient.getStaffPermissions();
    }

    @RequiresPermission("staff:change-auto-on-line-status")
    @RequestMapping(path = "change-auto-on-line-status", method = RequestMethod.POST)
    public Response<String> changeAutoOnLineStatus(@RequestParam("param") String param) {

        StaffStatus staffStatus = JSON.parseObject(param, StaffStatus.class);//已检查过

        return staffClient.changeAutoOnLineStatus(staffStatus);
    }

    @RequiresPermission("staff:allow-assign-ordertype")
    @RequestMapping(path = "allow-assign-ordertype", method = RequestMethod.POST)
    public Response<String> allowAssignOrdertype(@RequestParam(value = "operId") long operId,
                                                 @RequestParam(value = "allowAssign") String allowAssign) {
       return staffClient.allowAssignOrdertype(operId,allowAssign);
    }


    @RequestMapping(path = "updateThresholdByType", method = RequestMethod.POST)
    Response updateThresholdByType(@RequestParam("orderType") int orderType, @RequestParam("threshold") int threshold){
       return staffClient.updateThresholdByType(orderType,threshold);
    }


    @RequestMapping(path = "freeByUserId", method = RequestMethod.POST)
    Response freeByUserId(@RequestParam("orderType") int orderType, @RequestParam("userIds") String userIds,
                          @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                          @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime){
        boolean flagCreate = FindTimeRangeUtil.getFindTimeRangeUtil(startTime, endTime, 7);
        if (!flagCreate) {
            return NewResponseUtil.makeError(AdminErrorCode.DAY_RANGE_ERROR);
        }
        return staffClient.freeByUserIdV2(orderType,userIds, startTime, endTime);
    }

    @RequestMapping(path = "getThresholdByType", method = RequestMethod.POST)
    Response getThresholdByType(@RequestParam("orderType") int orderType,
                                @RequestParam(value = "operId",defaultValue = "0",required = false) Long operId){
        return staffClient.getThresholdByType(orderType,operId);
    }


    /**
     * 外部账号  只展示外部账号
     * @param userId
     * @param staffStatusList
     * @return
     */
    private List<StaffStatus> showUser(int userId,List<StaffStatus> staffStatusList){

        if (UserUtil.isInternalStaff(userId)){
            return staffStatusList;
        }

        return staffStatusList.stream().filter(r->r.getUserId() > UserUtil.limit).collect(Collectors.toList());
    }

}
