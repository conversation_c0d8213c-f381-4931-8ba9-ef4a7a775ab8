package com.shuidihuzhu.cf.admin.mq.casestatus;

import com.shuidihuzhu.cf.admin.mq.common.BaseMessageConsumer;
import com.shuidihuzhu.cf.admin.river.impl.RiverCreditFacadeImpl;
import com.shuidihuzhu.cf.client.material.model.CfPropertyInsuranceInfoModel;
import com.shuidihuzhu.cf.client.material.model.mq.MqTagsConstant;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@RocketMQListener(
        id = MqTagsConstant.MQ_OLD_CASE_SUBMIT_PROPERTY,
        group = "cf-admin-" + MqTagsConstant.MQ_OLD_CASE_SUBMIT_PROPERTY,
        tags = MqTagsConstant.MQ_OLD_CASE_SUBMIT_PROPERTY,
        topic = MQTopicCons.CF)
@Slf4j
public class CreditInfoSubmitConsumer extends BaseMessageConsumer<CfPropertyInsuranceInfoModel> implements MessageListener<CfPropertyInsuranceInfoModel> {

    @Autowired
    private RiverCreditFacadeImpl riverFacade;

    @Override
    protected boolean handle(ConsumerMessage<CfPropertyInsuranceInfoModel> consumerMessage) {
        CfPropertyInsuranceInfoModel caseEndModel = consumerMessage.getPayload();
        int caseId = caseEndModel.getCaseId();
        return riverFacade.onSubmit(caseId).ok();
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
