package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingCommentBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingReportBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingReportRecordBiz;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCommentVo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReportRecord;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2018/8/30
 *
 * 十分钟执行一次   查询前11分钟的举报 是否已经生成工单
 *
 */

@RestController
@Slf4j
@RefreshScope
@RequestMapping("/innerapi/cf/report")
public class CheckReportFeignController {

    @Autowired
    private AdminCrowdfundingReportBiz reportBiz;
    @Autowired
    private CrowdfundingReportRecordBiz crowdfundingReportRecordBiz;


    @ApiOperation("查询10分钟内的举报是否生成工单")
    @RequestMapping(value = "/check", method = RequestMethod.POST)
    public Response check(){

//        reportBiz.checkReportByTimes();

        return NewResponseUtil.makeSuccess("succ");
    }

    @ApiOperation("查询举报操作记录")
    @RequestMapping(value = "/get-records", method = RequestMethod.POST)
    public Response<List<CrowdfundingReportRecord>> getRecordListByIds(@RequestParam("reportIds") List<Integer> reportIds){
        if (CollectionUtils.isEmpty(reportIds)) {
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        List<CrowdfundingReportRecord> crowdfundingReportRecordList = crowdfundingReportRecordBiz.getreportRecordListByReportIds(reportIds);
        return NewResponseUtil.makeSuccess(crowdfundingReportRecordList);
    }

    @Autowired
    private AdminCrowdfundingCommentBiz crowdfundingCommentBiz;

    @PostMapping("testTiDbSearch")
    public void testTiDbSearch(@RequestParam("parentId") long parentId) {
        long cur = System.currentTimeMillis();
        List<CrowdfundingCommentVo> result = crowdfundingCommentBiz.getCommentByParentIdFromTiDb(parentId, 2000);

        log.info("从tidb查询耗时:{} 数据个数:{} 具体数据:{}", System.currentTimeMillis() - cur, result.size(),
                JSON.toJSONString(result));
    }

    @PostMapping("testTiDbParentSearch")
    public void testTiDbParentSearch(@RequestParam("userId") long userId) {
        long cur = System.currentTimeMillis();
        List<CrowdfundingCommentVo> result = crowdfundingCommentBiz.getCommentByUserIdAndTypeFromTiDb(userId, 1, 2000);

        log.info("从tidb查询耗时:{} 数据个数:{} 具体数据:{}", System.currentTimeMillis() - cur, result.size(),
                JSON.toJSONString(result));
    }

}
