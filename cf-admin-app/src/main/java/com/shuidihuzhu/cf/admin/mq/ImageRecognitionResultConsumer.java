package com.shuidihuzhu.cf.admin.mq;

import com.shuidihuzhu.ai.algo.model.vo.ClaimWatermarkVo;
import com.shuidihuzhu.cf.client.feign.CfAttachmentFeignClient;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
@RocketMQListener(id = com.shuidihuzhu.cf.constants.admin.MQTagCons.CF_ATTACHMENT_MARK_ATTR_STAT_RESULT,
        tags = com.shuidihuzhu.cf.constants.admin.MQTagCons.CF_ATTACHMENT_MARK_ATTR_STAT_RESULT,
        topic = MQTopicCons.CF)
public class ImageRecognitionResultConsumer implements MessageListener<ClaimWatermarkVo> {

    @Autowired
    private CfAttachmentFeignClient cfAttachmentFeignClient;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<ClaimWatermarkVo> mqMessage) {
        ClaimWatermarkVo claimWatermarkVo = mqMessage.getPayload();
        if (claimWatermarkVo == null){
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        log.info("ImageRecognitionResultConsumer updateWaterMarkClaimVo:{}", claimWatermarkVo.toString());
        Integer label = claimWatermarkVo.getLabel();
        Long id = claimWatermarkVo.getId();
        if (label == null || id == null || id <= 0){
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        int result = 0;
        //大数据返回 -1需要重试，0无水印，1有水印
        //cf需要     0未知,     1有水印,2无水印
        if (label == 0) {
            result = 2;
        }
        if (label == 1){
            result = 1;
        }
        if (label == -1){
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        log.info("ImageRecognitionResultConsumer updateWaterMarkId:{} result:{}", id, result);
        cfAttachmentFeignClient.updateWaterMark(id.intValue(), result);
        return ConsumeStatus.CONSUME_SUCCESS;
    }




}
