package com.shuidihuzhu.cf.admin.mq.domain;

import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.model.domain.CfHeavyVolume;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/3/9  5:21 下午
 */
@Service
@Slf4j
@RocketMQListener(id = MQTagCons.DOMAIN_TRAFFIC_DISTRIBUTION_STRATEGY,
        tags = MQTagCons.DOMAIN_TRAFFIC_DISTRIBUTION_STRATEGY,
        topic = MQTopicCons.CF)
public class DomainTrafficDistributionStrategyConsumer implements MessageListener<CfHeavyVolume> {

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfHeavyVolume> mqMessage) {
        if (Objects.isNull(mqMessage) || Objects.isNull(mqMessage.getPayload())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
