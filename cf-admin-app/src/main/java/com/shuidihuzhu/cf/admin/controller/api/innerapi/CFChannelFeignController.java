package com.shuidihuzhu.cf.admin.controller.api.innerapi;

import com.shuidihuzhu.cf.biz.admin.channel.CfChannelBiz;
import com.shuidihuzhu.cf.biz.admin.channel.CfChannelGroupBiz;
import com.shuidihuzhu.cf.biz.admin.channel.CfQrcodeChannelMapBiz;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.admin.channel.CfChannel;
import com.shuidihuzhu.cf.model.admin.channel.CfChannelGroup;
import com.shuidihuzhu.cf.model.admin.channel.CfQrcodeChannel;
import com.shuidihuzhu.client.cf.admin.client.CFChannelFeignClient;
import com.shuidihuzhu.client.cf.admin.model.CfChannelDTO;
import com.shuidihuzhu.client.cf.admin.model.CfChannelGroupDTO;
import com.shuidihuzhu.client.cf.admin.model.CfQrcodeChannelDTO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: cuikexiang
 * @date: 2020/12/21
 */
@RestController
@RequestMapping(CFChannelFeignClient.PATH)
@Slf4j
public class CFChannelFeignController implements CFChannelFeignClient {

    @Autowired
    private CfChannelBiz cfChannelBiz;

    @Autowired
    private CfChannelGroupBiz cfChannelGroupBiz;

    @Autowired
    private CfQrcodeChannelMapBiz cfQrcodeChannelMapBiz;


    @Override
    public Response addChannel(CfChannelDTO channel) {
        CfChannel cfChannel = new CfChannel();
        cfChannel.setGroupId(channel.getGroupId());
        cfChannel.setName(channel.getName());
        cfChannel.setDescription(channel.getDescription());
        cfChannel.setType(channel.getType());
        int reuslt = cfChannelBiz.add(cfChannel);
        if (reuslt >= 1) {
            return NewResponseUtil.makeSuccess(1);
        }

        return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
    }

    @Override
    public Response<CfChannelDTO> getByName(String name) {
        CfChannel cfChannel = cfChannelBiz.getByName(name);
        if (Objects.isNull(cfChannel)) {
            return NewResponseUtil.makeSuccess(null);
        }


        return NewResponseUtil.makeSuccess(of(cfChannel));
    }

    @Override
    public Response<CfChannelDTO> getById(int id) {
        CfChannel cfChannel = cfChannelBiz.get(id);
        if (Objects.isNull(cfChannel)) {
            return NewResponseUtil.makeSuccess(null);
        }

        return NewResponseUtil.makeSuccess(of(cfChannel));
    }

    @Override
    public Response<List<CfChannelGroupDTO>> listChannelGroupByIds(List<Integer> ids) {
        List<CfChannelGroup> cfChannelGroups = cfChannelGroupBiz.listByIds(ids);
        List<CfChannelGroupDTO> result = cfChannelGroups.stream().map(CFChannelFeignController::of).collect(Collectors.toList());
        return NewResponseUtil.makeSuccess(result);
    }

    @Override
    public Response addQrcodeChannel(CfQrcodeChannelDTO dto) {
        CfQrcodeChannel qrcodeChannel = new CfQrcodeChannel();
        qrcodeChannel.setCfChannelGroupId(dto.getCfChannelGroupId());
        qrcodeChannel.setCfChannelId(dto.getCfChannelId());
        qrcodeChannel.setQrcodeId(dto.getQrcodeId());
        int reulst = cfQrcodeChannelMapBiz.add(qrcodeChannel);
        if (reulst >= 1) {
            return NewResponseUtil.makeSuccess(null);
        }

        return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
    }

    @Override
    public Response<List<CfQrcodeChannelDTO>> listGroupIdsByQrcodeIds(List<Integer> ids) {
        List<CfQrcodeChannel> cfQrcodeChannels = cfQrcodeChannelMapBiz.listByQrcodeIds(ids);
        List<CfQrcodeChannelDTO> result = cfQrcodeChannels.stream().map(CFChannelFeignController::of).collect(Collectors.toList());
        return NewResponseUtil.makeSuccess(result);
    }

    private static CfChannelDTO of(final CfChannel cfChannel) {
        CfChannelDTO cfChannelDTO = new CfChannelDTO();
        cfChannelDTO.setId(cfChannel.getId());
        cfChannelDTO.setGroupId(cfChannel.getGroupId());
        cfChannelDTO.setName(cfChannel.getName());
        cfChannelDTO.setDescription(cfChannel.getDescription());
        cfChannelDTO.setType(cfChannel.getType());
        cfChannelDTO.setCreateTime(cfChannel.getCreateTime());
        cfChannelDTO.setUpdateTime(cfChannel.getUpdateTime());
        return cfChannelDTO;
    }

    private static CfChannelGroupDTO of(final CfChannelGroup cfChannelGroup) {
        CfChannelGroupDTO dto = new CfChannelGroupDTO();
        dto.setId(cfChannelGroup.getId());
        dto.setName(cfChannelGroup.getName());
        dto.setValid(cfChannelGroup.getValid());
        dto.setDescription(cfChannelGroup.getDescription());
        return dto;
    }

    private static CfQrcodeChannelDTO of(final CfQrcodeChannel cfQrcodeChannel) {
        CfQrcodeChannelDTO dto = new CfQrcodeChannelDTO();
        dto.setCfChannelGroupId(cfQrcodeChannel.getCfChannelGroupId());
        dto.setCfChannelId(cfQrcodeChannel.getCfChannelId());
        dto.setQrcodeId(cfQrcodeChannel.getQrcodeId());
        return dto;
    }
}
