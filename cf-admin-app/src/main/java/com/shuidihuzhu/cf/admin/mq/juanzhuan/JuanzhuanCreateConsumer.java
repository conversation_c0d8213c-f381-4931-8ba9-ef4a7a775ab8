package com.shuidihuzhu.cf.admin.mq.juanzhuan;

import com.shuidihuzhu.cf.admin.mq.common.BaseMessageConsumer;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.model.admin.workorder.JuanzhanOrderCreate;
import com.shuidihuzhu.cf.service.workorder.juanzhan.JuanzhuanWorkOrderService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @DATE 2020/5/8
 */
@Slf4j
@Service
@RocketMQListener(
        id = MQTagCons.juanzhuan_workorder_create,
        topic = MQTopicCons.CF,
        tags = MQTagCons.juanzhuan_workorder_create,
        group = MQTagCons.juanzhuan_workorder_create+"_group"
)
public class JuanzhuanCreateConsumer extends BaseMessageConsumer<JuanzhanOrderCreate> implements MessageListener<JuanzhanOrderCreate> {

    @Autowired
    private JuanzhuanWorkOrderService juanzhuanWorkOrderService;

    @Override
    protected boolean handle(ConsumerMessage<JuanzhanOrderCreate> consumerMessage) {

        JuanzhanOrderCreate order = consumerMessage.getPayload();

        juanzhuanWorkOrderService.createJuanzhuan(order);

        return true;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
