package com.shuidihuzhu.cf.admin.controller.api.workorder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.dao.workorder.WorkOrderFollowLabelDao;
import com.shuidihuzhu.cf.domain.workorder.FollowLabelDO;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingOperationBiz;
import com.shuidihuzhu.cf.call.CallTagVO;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingOperationDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CfFinishStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingOperationEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.model.admin.CailiaoWorkOrderVO;
import com.shuidihuzhu.cf.model.admin.workorder.CfCallOutRecord;
import com.shuidihuzhu.cf.model.admin.workorder.CfWorkOrderStatusDetail;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.param.workorder.ImagePublicHandleParam;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingApprove;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation;
import com.shuidihuzhu.cf.model.markfollowuptime.CfMarkFollowDo;
import com.shuidihuzhu.cf.service.CfCaseWorkOrderService;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.cf.service.workorder.imagePublic.ImagePublicWorkOrderService;
import com.shuidihuzhu.cf.service.markfollowuptime.CfMarkFollowService;
import com.shuidihuzhu.client.cf.workorder.CfCailiaoWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.CailiaoHandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderListParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OneTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.cf.workorder.v2.client.WorkOrderStatV2FeignClient;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2018/12/29
 */
@RestController
@RequestMapping(path="/admin/workorder/cailiao")
@Slf4j
public class CailiaoWorkOrderController {

    @Resource
    private CfCailiaoWorkOrderClient cailiaoWorkOrderClient;

    @Autowired
    private ICrowdfundingOperationDelegate crowdfundingOperationDelegate;

    @Autowired
    private AdminCrowdfundingOperationBiz crowdfundingOperationBiz;

    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Autowired
    private CfCaseWorkOrderService workOrderService;

    @Resource
    private CfWorkOrderClient cfWorkOrderClient;

    @Autowired
    private ApproveRemarkOldService approveRemarkOldService;

    @Autowired
    private CfMarkFollowService cfMarkFollowService;

    @Resource
    private AdminCrowdfundingAuthorBiz adminCrowdfundingAuthorBiz;
    @Resource
    private ImagePublicWorkOrderService imagePublicWorkOrderService;
    @Resource
    private AdminCfInfoExtBiz adminCfInfoExtBiz;
    @Resource
    private CrowdfundingFeignClient crowdfundingFeignClient;
    @Resource
    private WorkOrderStatV2FeignClient workOrderStatV2FeignClient;
    @Resource
    private WorkOrderFollowLabelDao workOrderFollowLabelDao;

    // 工单生成数量
    private static final Integer WORK_ORDER_COUNT_THRESHOLD = 4;

    private static List<Integer> NORMAL_CAILIAO_WORKS = Lists.newArrayList(
            WorkOrderType.cailiao_0.getType(),
            WorkOrderType.cailiao_1.getType(),
            WorkOrderType.cailiao_3.getType());

    private static List<Integer> NORMAL_NEED_HANDLE_STATUS = Lists.newArrayList(CrowdfundingOperationEnum.NEVER_PROCESSING.value(), CrowdfundingOperationEnum.DEFER_CONTACT.value(),
            CrowdfundingOperationEnum.DEFER_APPROVE.value());

    private static List<Integer> YANHOU_NEED_HANDLE_STATUS = Lists.newArrayList(CrowdfundingOperationEnum.NEVER_PROCESSING.value(), CrowdfundingOperationEnum.DEFER_CONTACT.value());



        @RequiresPermission("cailiao:handle")
        @RequestMapping(path = "hanlde-cailiao", method = RequestMethod.POST)
        public Response Handleshouci(@RequestParam("param") String param) {

            CailiaoHandleOrderParam p = JSON.parseObject(param, CailiaoHandleOrderParam.class);//已检查过

            if (p.getOrderType() == WorkOrderType.cailiao_zhu_dong_fu_wu.getType()) {
                Response<Void> result = preHandleZhuDongFuWuOrder(p, ContextUtil.getAdminUserId());
                if (result.notOk()) {
                    return result;
                }
            }
            if (p.getOrderType() == WorkOrderType.picture_publicity_review.getType()) {
                Response<Void> voidResponse = preHandlePicturePublicityReviewOrder(p);
                if (voidResponse.notOk()) {
                    return voidResponse;
                }
            }

            return cailiaoWorkOrderClient.handleCailiao(p);
        }

        @RequiresPermission("cailiao:orderlist")
        @RequestMapping(path = "cailiao-orderlist",method = RequestMethod.POST)
        Response cailoaoOrderlist(@RequestParam("param") String param,
                                  @RequestParam(value = "followLabel",required = false,defaultValue = "-1") int followLabel,
                                  @RequestParam(value = "caseId" ,required = false,defaultValue = "-1") int caseId,
                                  @RequestParam(value = "startFollowTime",required = false,defaultValue = "-1") long startFollowTime,
                                  @RequestParam(value = "endFollowTime",required = false,defaultValue = "-1") long endFollowTime,
                                  @RequestParam(value = "markFollowTime",required = false,defaultValue = "0") int markFollowTime){

            WorkOrderListParam p = JSON.parseObject(param, WorkOrderListParam.class);//已检查过

            workOrderService.fillTimeIfNullByHandleResult(p, OneTypeEnum.cailiao);

            Response<PageResult<WorkOrderVO>> response = cailiaoWorkOrderClient.cailoaoOrderlist(p);

            if (response == null || response.notOk() || response.getData() == null ){
                return response;
            }

            PageResult<WorkOrderVO> pageResult = response.getData();

            List<WorkOrderVO> list = pageResult.getPageList();

            if (CollectionUtils.isEmpty(list)){

                PageResult<CailiaoWorkOrderVO> result = new PageResult<>();
                result.setHasNext(pageResult.isHasNext());
                result.setPageList(Lists.newArrayList());

                return NewResponseUtil.makeSuccess(result);
            }

            List<String> infoids = list.stream().map(WorkOrderVO :: getCaseUuid).collect(Collectors.toList());
            List<Integer> caseIdList = list.stream()
                    .map(WorkOrderVO::getCaseId)
                    .collect(Collectors.toList());
            List<Long> workOrderIdList = list.stream()
                    .map(WorkOrderVO::getWorkOrderId)
                    .collect(Collectors.toList());
            Map<Integer, CrowdfundingAuthor> crowdfundingAuthorMap = adminCrowdfundingAuthorBiz.getByInfoIdList(caseIdList);
            FeignResponse<Map<Integer, CrowdfundingTreatment>> treatmentMapByInfoIdList = crowdfundingFeignClient.getTreatmentMapByInfoIdList(caseIdList);
            Map<Integer, CrowdfundingTreatment> treatmentMap = Objects.nonNull(treatmentMapByInfoIdList) && treatmentMapByInfoIdList.ok() ? treatmentMapByInfoIdList.getData() : new HashMap<>();

            Map<Long, List<String>> attachmentsMap = Objects.equals(p.getOrderType(), WorkOrderType.picture_publicity_review.getType()) ?
                    imagePublicWorkOrderService.getImagePublicByWorKOrderIdList(workOrderIdList) : new HashMap<>();
            Map<Long, List<String>> attachmentsMarkMap = Objects.equals(p.getOrderType(), WorkOrderType.picture_publicity_review.getType()) ?
                    imagePublicWorkOrderService.getMarkImagePublicByWorKOrderIdList(workOrderIdList) : new HashMap<>();

            List<CrowdfundingOperation> operations = crowdfundingOperationBiz.getByInfoIds(infoids);

            Map<String,CrowdfundingOperation> refuseMap = Maps.uniqueIndex(operations, new Function<CrowdfundingOperation, String>() {
                public String apply(CrowdfundingOperation input) {
                    return input.getInfoId();
                }
            });

            List<Integer> workOrderCaseIds = list.stream().map(WorkOrderVO::getCaseId).collect(Collectors.toList());
            Response<List<Integer>> workOrderResponse = workOrderStatV2FeignClient.listCaseIdsByTypeAndCount(workOrderCaseIds, WorkOrderType.cailiao_5.getType(), WORK_ORDER_COUNT_THRESHOLD);
            List<Integer> taggingCaseIds = workOrderResponse.ok() && CollectionUtils.isNotEmpty(workOrderResponse.getData()) ? workOrderResponse.getData() : new ArrayList<>();
            //封装admin 特殊需要的参数
            List<CailiaoWorkOrderVO> yanhou = list.stream().map(r->{

                CailiaoWorkOrderVO vo = new CailiaoWorkOrderVO();
                BeanUtils.copyProperties(r, vo);
                // 案例重复或二次发起的情况
//                vo.setRepeatStatusList(cfRepeatInfoBiz.selectRepeatStatusByCaseId(r.getCaseId()));

                CrowdfundingApprove lastCrowdfundingApproveWithComment = crowdfundingOperationDelegate.getLastWithCommentByCrowdfundingId(r.getCaseId());
                if (null != lastCrowdfundingApproveWithComment) {
                    vo.setLastComment(lastCrowdfundingApproveWithComment.getComment());
                }

                if (refuseMap.containsKey(r.getCaseUuid())){
                    vo.setRefuseCount(refuseMap.get(r.getCaseUuid()).getRefuseCount());
                    vo.setUserRefuseCount(refuseMap.get(r.getCaseUuid()).getUserRefuseCount());

                    int allRefuseCount = refuseMap.get(r.getCaseUuid()).getRefuseCount() + refuseMap.get(r.getCaseUuid()).getUserRefuseCount();
                    vo.setAllRefuseCount(allRefuseCount);
                }
                if (CollectionUtils.isNotEmpty(attachmentsMap.get(r.getWorkOrderId()))) {
                    vo.setAttachmentList(attachmentsMap.get(r.getWorkOrderId()));
                }
                CrowdfundingAuthor crowdfundingAuthor = crowdfundingAuthorMap.get(r.getCaseId());
                if (Objects.nonNull(crowdfundingAuthor)) {
                    vo.setPatientName(crowdfundingAuthor.getName());
                }
                if (CollectionUtils.isNotEmpty(attachmentsMarkMap.get(r.getWorkOrderId()))) {
                    vo.setMaskImageList(attachmentsMarkMap.get(r.getWorkOrderId()));
                }
                CrowdfundingTreatment crowdfundingTreatment = treatmentMap.get(r.getCaseId());
                if (Objects.nonNull(crowdfundingTreatment)) {
                    vo.setDiseaseName(crowdfundingTreatment.getDiseaseName());
                }
                if (CollectionUtils.isNotEmpty(taggingCaseIds)) {
                    vo.setRejectToMuch(taggingCaseIds.contains(r.getCaseId()));
                }
                if (Objects.isNull(r.getFollowLabel())) {
                    FollowLabelDO followLabelDO = workOrderFollowLabelDao.selectByWorkOrderId(r.getWorkOrderId());
                    vo.setFollowLabel(Objects.nonNull(followLabelDO) ? (followLabelDO.getFollowLabel() + "") : "0");
                    vo.setFollowTime(Objects.nonNull(followLabelDO) ? (followLabelDO.getCreateTime().getTime()) : 0);
                }

                return vo;

            }).collect(Collectors.toList());

            PageResult<CailiaoWorkOrderVO> result = new PageResult<>();
            result.setHasNext(pageResult.isHasNext());
            result.setPageList(yanhou);

            //材审服务工单  处理中的时候 单独增加筛选
            if (p.getOrderType() == WorkOrderType.cailiao_fuwu.getType() && p.getHandleResult().equals(HandleResultEnum.doing.getType()+"")){

                List<CailiaoWorkOrderVO> cailiaofuwu = yanhou.stream().filter(r->{
                    if (followLabel == -1){
                        return true;
                    }else if (followLabel == 0){
                        return StringUtils.isEmpty(r.getFollowLabel()) || String.valueOf(followLabel).equals(r.getFollowLabel());
                    }else {
                        return String.valueOf(followLabel).equals(r.getFollowLabel());
                    }
                }).filter(r->{
                    if (caseId == -1){
                        return true;
                    }else {
                        return r.getCaseId() == caseId;
                    }
                }).filter(r->{
                    if (startFollowTime == -1 || endFollowTime == -1){
                        return true;
                    }else {
                        return r.getFollowTime() >= startFollowTime && r.getFollowTime() <= endFollowTime;
                    }
                }).collect(Collectors.toList());

                List<CailiaoWorkOrderVO> workOrderVOList = Lists.newArrayList();
                List<Long> workOrderIds = cailiaofuwu.stream().map(WorkOrderVO::getWorkOrderId).collect(Collectors.toList());
                List<CfMarkFollowDo> cfMarkFollowDoList = cfMarkFollowService.getByBizIds(workOrderIds);

                Map<Long, CfMarkFollowDo> cfMarkFollowDoMap = cfMarkFollowDoList.stream().collect(Collectors.toMap(CfMarkFollowDo::getBizId, java.util.function.Function.identity(), (x, y) -> x));
                for (CailiaoWorkOrderVO cailiaoWorkOrderVO : cailiaofuwu) {
                    CfMarkFollowDo cfMarkFollowDo = cfMarkFollowDoMap.get(cailiaoWorkOrderVO.getWorkOrderId());
                    if (Objects.nonNull(cfMarkFollowDo)) {
                        cailiaoWorkOrderVO.setMarkFollowTime(DateUtil.getDate2LStr(cfMarkFollowDo.getTargetTime()));
                        workOrderVOList.add(cailiaoWorkOrderVO);
                    }
                }

                if (markFollowTime == 1) {
                    workOrderVOList = workOrderVOList.stream().sorted(Comparator.comparing(CailiaoWorkOrderVO::getMarkFollowTime).reversed()).collect(Collectors.toList());
                    result.setPageList(workOrderVOList);
                } else {
                    result.setPageList(cailiaofuwu);
                }
            }
            return NewResponseUtil.makeSuccess(result);
        }

        @RequiresPermission("cailiao:check")
        @RequestMapping(path = "cailiao-check",method = RequestMethod.POST)
        Response cailiaoCheck(@RequestParam(value = "caseId") int caseId,
        @RequestParam("workOrderType") int workOrderType,
        @RequestParam("workOrderId") long workOrderId){

            if (caseId == 0 ){
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
            }
            if (workOrderType == WorkOrderType.picture_publicity_review.getType()) {
                CailiaoHandleOrderParam cailiaoHandleOrderParam = new CailiaoHandleOrderParam();
                cailiaoHandleOrderParam.setOrderType(workOrderType);
                cailiaoHandleOrderParam.setCaseId(caseId);
                cailiaoHandleOrderParam.setWorkOrderId(workOrderId);
                Response<Void> voidResponse = preHandlePicturePublicityReviewOrder(cailiaoHandleOrderParam);
                if (voidResponse.notOk()) {
                    return NewResponseUtil.makeSuccess(true);
                }
                return NewResponseUtil.makeSuccess(false);
            }

            CrowdfundingInfo c = crowdfundingDelegate.getFundingInfoById(caseId);

            if (c.getStatus() != CrowdfundingStatus.SUBMITTED && workOrderType != WorkOrderType.cailiao_fuwu.getType()){
                closeWorkorder(workOrderId,workOrderType,"案例未提交");
                return NewResponseUtil.makeSuccess(true);
            }

            if (c.getStatus() == CrowdfundingStatus.CROWDFUNDING_STATED && workOrderType == WorkOrderType.cailiao_fuwu.getType()){
                closeWorkorder(workOrderId,workOrderType,"案例审核通过");
                return NewResponseUtil.makeSuccess(true);
            }

            CrowdfundingOperation crowdfundingOperation = crowdfundingOperationBiz.getByInfoId(c.getInfoId());


        //非延后材料工单 如果处于“不再处理”“延后电话联系”“延后处理”状态，关闭
        if (NORMAL_CAILIAO_WORKS.contains(workOrderType)){
            //判断  案例是都处于特殊状态
            if (NORMAL_NEED_HANDLE_STATUS.contains(crowdfundingOperation.getOperation())) {
                closeWorkorder(workOrderId,workOrderType,"延后电话联系或不再处理或延后处理");
                return NewResponseUtil.makeSuccess(true);
            }
        }

        //延后工单 如果处于“不再处理”“延后电话联系”状态，关闭
        if (WorkOrderType.yanhou.getType() == workOrderType
            ||WorkOrderType.cailiao_4.getType() == workOrderType
            ||WorkOrderType.cailiao_5.getType() == workOrderType
            ||WorkOrderType.cailiao_zhu_dong_fu_wu.getType() == workOrderType
            ||WorkOrderType.cailiao_fuwu.getType() == workOrderType
        ) {

            if (YANHOU_NEED_HANDLE_STATUS.contains(crowdfundingOperation.getOperation())){
                closeWorkorder(workOrderId, workOrderType,"延后电话联系或不再处理");
                return NewResponseUtil.makeSuccess(true);
            }
        }

        if (workOrderType == WorkOrderType.cailiao_zhu_dong_fu_wu.getType()) {
            Response<WorkOrderVO> workOrderResp = cfWorkOrderClient.getWorkOrderById(workOrderId);
            if (workOrderResp == null || workOrderResp.notOk()) {
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
            }
            WorkOrderVO workOrder = workOrderResp.getData();
            int nowResult = workOrder.getHandleResult();
            if (HandleResultEnum.exception_done.getType() == nowResult) {
                return NewResponseUtil.makeFail("工单自动异常关闭");
            }
        }

        return NewResponseUtil.makeSuccess(false);
    }

    @RequiresPermission("cailiao:getCailiaoList")
    @PostMapping(path = "getCailiaoList")
    public Response<List<WorkOrderVO>> getCailiaoList(@RequestParam("caseId") int caseId){

        ArrayList<Integer> orderTypes = Lists.newArrayList(WorkOrderType.cailiao_4.getType(), WorkOrderType.cailiao_5.getType(), WorkOrderType.cailiao_zhu_dong_fu_wu.getType());
        Response<List<WorkOrderVO>> response = cfWorkOrderClient.listByCaseIdAndTypeAndResult(caseId,
                orderTypes,Lists.newArrayList());

        List<WorkOrderVO> list = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(Lists.newArrayList());

        return NewResponseUtil.makeSuccess(list.stream().sorted(Comparator.comparing(WorkOrderVO::getCreateTime).reversed()).collect(Collectors.toList()));
    }

    @RequiresPermission("cailiao:handle-zhu-dong-fu-wu")
    @PostMapping("handle-zhu-dong-fu-wu")
    public Response handleZhuDongFuWu(@RequestParam("caseId") int caseId,
                                            @RequestParam("workOrderId") int workOrderId,
                                            @RequestParam("handleResult") HandleResultEnum handleResult,
                                            @RequestParam("callSuccess") boolean callSuccess,
                                            @RequestParam("comment") String comment
    ){
        int adminUserId = ContextUtil.getAdminUserId();
        CailiaoHandleOrderParam param = new CailiaoHandleOrderParam();
        param.setWorkOrderId(workOrderId);
        param.setOrderType(WorkOrderType.cailiao_zhu_dong_fu_wu.getType());
        param.setHandleResult(handleResult.getType());
        param.setOperComment(comment);
        param.setUserId(adminUserId);
        param.setCaseId(caseId);
        Response<Void> result = preHandleZhuDongFuWuOrder(param, adminUserId, callSuccess);
        if (result.notOk()) {
            return result;
        }

        return cailiaoWorkOrderClient.handleCailiao(param);
    }

    private Response<Void> preHandleZhuDongFuWuOrder(CailiaoHandleOrderParam param, int operatorId){
            return preHandleZhuDongFuWuOrder(param, operatorId, null);
    }

    private Response<Void> preHandleZhuDongFuWuOrder(CailiaoHandleOrderParam param, int operatorId, Boolean callSuccess){
        long workOrderId = param.getWorkOrderId();
        HandleResultEnum handleResult = HandleResultEnum.getFromType(param.getHandleResult());
        int caseId = param.getCaseId();
        String comment = param.getOperComment();
        if (handleResult != HandleResultEnum.done && handleResult != HandleResultEnum.later_doing){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        Response<WorkOrderVO> workOrderResp = cfWorkOrderClient.getWorkOrderById(workOrderId);
        if (workOrderResp == null || workOrderResp.notOk()) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        WorkOrderVO workOrder = workOrderResp.getData();
        int nowResult = workOrder.getHandleResult();
        if (HandleResultEnum.exception_done.getType() == nowResult) {
            return NewResponseUtil.makeError(AdminErrorCode.HOSPITAL_AUDIT_HAS_AUTO_CLOSE);
        }

        // 仅提交记录备注
        if (handleResult == HandleResultEnum.done) {
            approveRemarkOldService.add(caseId, operatorId, "材料审核主动服务",
                    callSuccess ? "呼通" : "未呼通" + "+" + handleResult.getShowMsg());

            approveRemarkOldService.add(caseId, operatorId, "备注", comment);
        }

        return NewResponseUtil.makeSuccess(null);
    }

    private Response<Void> preHandlePicturePublicityReviewOrder(CailiaoHandleOrderParam param){
        long workOrderId = param.getWorkOrderId();
        int caseId = param.getCaseId();
        CfInfoExt cfInfoExt = adminCfInfoExtBiz.getByCaseId(caseId);
        if (Objects.isNull(cfInfoExt)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (cfInfoExt.getFinishStatus() == CfFinishStatus.NOT_FINISH.getValue()) {
            return NewResponseUtil.makeSuccess(null);
        }

        log.info("preHandlePicturePublicityReviewOrder is close {}", param);
        closeWorkorder(workOrderId, param.getOrderType(), "案例已结束，自动关单");

        return NewResponseUtil.makeError(AdminErrorCode.HOSPITAL_AUDIT_HAS_AUTO_CLOSE);
    }

    private void closeWorkorder(long workOrderId,int orderType,String comment){

        CailiaoHandleOrderParam param = new CailiaoHandleOrderParam();
        param.setWorkOrderId(workOrderId);
        param.setOrderType(orderType);
        param.setHandleResult(HandleResultEnum.exception_done.getType());
        param.setOperComment(comment);

        Response response = cailiaoWorkOrderClient.handleCailiao(param);

        log.info("handleCailiao param={} response={}",param,response);
    }


    @PostMapping("select-work-order-status")
    public Response<CfWorkOrderStatusDetail> selectWorkOrderById(int caseId, long workOrderId) {
        return NewResponseUtil.makeSuccess(workOrderService.selectWorkOrderById(caseId, workOrderId));
    }

    @RequiresPermission("call:select-call-record-qc")
    @PostMapping("select-call-record")
    public Response<CfCallOutRecord> selectCallRecords(long workOrderId, int caseId, int operatorId) {
        return NewResponseUtil.makeSuccess(workOrderService.selectCallRecords(workOrderId, caseId, operatorId));
    }

    /**
     * 查询质检工单对应的被质检工单的录音信息
     */
    @RequiresPermission("call:select-call-record-all-qc")
    @PostMapping("select-call-record-all")
    public Response<CfCallOutRecord> selectCallRecordsAll(long workOrderId, int caseId, int operatorId) {
        return NewResponseUtil.makeSuccess(workOrderService.selectQcCallRecordsAll(workOrderId, caseId, operatorId));
    }

    @PostMapping("add-call-record-tag")
    public Response<Void> addCallRecordTag(long workOrderId, @RequestBody CallTagVO tagInfo) {
        return workOrderService.addCallRecordTag(workOrderId, tagInfo);
    }

    @PostMapping("submit-public-work-order")
    @RequiresPermission("cailiao:submit-public-work-order")
    public Response<Void> submitPublicWorkOrder(@RequestParam String param) {
        if (StringUtils.isEmpty(param)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        ImagePublicHandleParam imagePublicHandleParam = JSONObject.parseObject(param, ImagePublicHandleParam.class);
        if (Objects.isNull(imagePublicHandleParam)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<ImagePublicHandleParam.ImagePublicHandleImageListParam> markImageList = imagePublicHandleParam.getMarkImageList();
        if (CollectionUtils.isEmpty(markImageList)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        return imagePublicWorkOrderService.submitPublicWorkOrder(imagePublicHandleParam);
    }



}
