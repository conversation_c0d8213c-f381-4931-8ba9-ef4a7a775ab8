package com.shuidihuzhu.cf.admin.util;

import com.alibaba.fastjson.JSON;
import com.aliyun.oss.model.ObjectMetadata;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.http.HttpMethodName;
import com.qcloud.cos.model.GeneratePresignedUrlRequest;
import com.qcloud.cos.model.PutObjectResult;
import com.shuidihuzhu.cf.store.configuration.BucketAttribute;
import com.shuidihuzhu.cf.store.plugins.CosPlugins;
import com.shuidihuzhu.cf.util.crowdfunding.CosUploadUtil;
import com.shuidihuzhu.common.web.constants.CrowdfundingCons;
import com.shuidihuzhu.common.web.util.aliyun.enums.OSSBucketDir;
import com.shuidihuzhu.infra.starter.cos.configuration.CosClientWrapper;
import com.shuidihuzhu.pf.common.v2.cos.CosPlugin;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.Date;
import java.util.Objects;
import java.util.UUID;

/**
 * Created by ahrievil on 2017/5/22.
 */
@Service
@Slf4j
public class UploadUtil {
    @Autowired
    private CosPlugins cosPlugins;

    @Resource(name = "cos-cf-images")
    private CosClientWrapper cosClientWrapper;

    @Resource(name = "cos-cf-image")
    private CosClientWrapper cosClientWrapperV2;

    @Autowired
    private CosUploadUtil cosUploadUtil;

    private static final Logger LOGGER = LoggerFactory.getLogger(UploadUtil.class);

    public String doCosUpload(MultipartFile multipartFile) {

        String dt = DateFormatUtils.format(new Date(), "yyyyMMdd");
        String directory = CrowdfundingCons.IMG_BASE_DIR + dt;
        try {
            String originalFilename = multipartFile.getOriginalFilename();
            String suffix = "";
            if (originalFilename != null) {
                int index = originalFilename.lastIndexOf(".");
                suffix = index > 0 ? originalFilename.substring(index) : "";
            }
            return cosPlugins.uploadFile(originalFilename,
                    multipartFile.getInputStream(),
                    multipartFile.getContentType(),
                    "cf-images",
                    directory,
                    UUID.randomUUID().toString() + suffix);
        } catch (Exception e) {
            LOGGER.error("", e);
        }
        return "";
    }

    public String doCosUploadV2(MultipartFile multipartFile) {

        try {
            if (Objects.isNull(multipartFile) || Objects.isNull(multipartFile.getOriginalFilename())) {
                return org.apache.commons.lang.StringUtils.EMPTY;
            }

            String originalFilename = multipartFile.getOriginalFilename();

            String suffix = "";
            if (originalFilename != null) {
                int index = originalFilename.lastIndexOf(".");
                suffix = index > 0 ? originalFilename.substring(index) : "";
            }

            int pos = org.apache.commons.lang.StringUtils.lastIndexOf(originalFilename, ".");
            if (pos < 0) {
                return org.apache.commons.lang.StringUtils.EMPTY;
            }

            String dt = DateFormatUtils.format(new Date(), "yyyyMMdd");
            String dir = CrowdfundingCons.IMG_BASE_DIR + dt;

            String finalFileName = cosUploadUtil.getFileName(originalFilename, UUID.randomUUID().toString() + suffix, dir);



            PutObjectResult putObjectResult = cosClientWrapper.putObject(finalFileName, multipartFile.getInputStream(), null);
            log.info("cos上传 putObjectResult:{}", JSON.toJSONString(putObjectResult));


            String finalUrl = "https://image.shuidichou.com" + finalFileName;;

            log.info("上传地址为:{}", finalUrl);

            return finalUrl;
        } catch (IOException e) {
            log.error("upload error:", e);
        }

        return org.apache.commons.lang.StringUtils.EMPTY;
    }

    public String getTemporalUrlByUrl(String bucket, String image) {
        int expiration = 600;
        if (StringUtils.isAnyBlank(new CharSequence[]{bucket, image})) {
            return "";
        } else {
            String path = "";

            try {
                URL urlObject = new URL(image);
                path = urlObject.getPath();
            } catch (MalformedURLException var11) {
                log.error("", var11);
            }

            if (StringUtils.isBlank(path)) {
                return "";
            } else {
                if (cosClientWrapperV2 == null) {
                    log.warn("clientWrapper is null");
                    return "";
                } else {
                    COSClient cosClient = cosClientWrapperV2.getOriginCosClient();
                    if (cosClient == null) {
                        log.warn("cosClient is null");
                        return "";
                    } else {
                        GeneratePresignedUrlRequest req = new GeneratePresignedUrlRequest(bucket + "-" + cosClientWrapperV2.getAppId(), path.substring(1), HttpMethodName.GET);
                        Date expirationDate = new Date(System.currentTimeMillis() + (long)expiration * 1000L);
                        req.setExpiration(expirationDate);
                        URL url = cosClient.generatePresignedUrl(req);
                        String domain = "images.shuidichou.com";
                        return !StringUtils.isBlank(domain) && !domain.equals(url.getHost()) ? StringUtils.replace(url.toString(), "http://" + url.getHost(), "https://" + domain) : url.toString();
                    }
                }
            }
        }
    }

    public String doUpload(MultipartFile multipartFile) {
        String dt = DateFormatUtils.format(new Date(), "yyyyMMdd");
        // String fileName = multipartFile.getOriginalFilename();
        // String fileType = fileName.substring(fileName.lastIndexOf("."));
        // String objectName = CrowdfundingCons.IMG_BASE_DIR + dt + "/" +
        // UUID.randomUUID().toString() + fileType;
        String objectName = CrowdfundingCons.IMG_BASE_DIR + dt + "/" + UUID.randomUUID().toString();
        //上传美团云
//		AmazonS3 s3Client = AmazonS3ClientProvider.CreateAmazonS3Conn();
        try (InputStream is = multipartFile.getInputStream()) {
            byte[] contentBytes = IOUtils.toByteArray(is);
            Long contentLength = Long.valueOf(contentBytes.length);
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(contentLength);
            String contentType = multipartFile.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                String filename = multipartFile.getOriginalFilename();
                int pos = filename.lastIndexOf(".");
                String fileType = null;
                if (pos >= 0) {
                    fileType = filename.substring(pos).toLowerCase().replace(".", "");
                }
                if ("bmp".equals(fileType) || "jpg".equals(fileType) || "jpeg".equals(fileType)) {
                    contentType = "image/" + fileType;
                } else {
                    contentType = "image/jpeg";
                }
            }
            metadata.setContentType(contentType);
//			s3Client.putObject(new PutObjectRequest(CrowdfundingCons.BUCKET_NAME, objectName,
//			                                        multipartFile.getInputStream(), metadata));

            //上传阿里云
            CfOssClient.putObject(OSSBucketDir.SHUIDI_CF_IMAGE.getBucketDirName(), objectName, new ByteArrayInputStream(contentBytes),
                                metadata);
            return CfDataUploadUtils.OSS_SHUIDI_CF_IMAGE_URL + "/" + objectName;
        } catch (IOException e) {
            LOGGER.error("upload error:", e);
        }

        return "";
    }

    public String doUploadV2(MultipartFile multipartFile, String originalFileName) {
        try (InputStream is = multipartFile.getInputStream()) {
            byte[] contentBytes = IOUtils.toByteArray(is);
            if(StringUtils.isBlank(originalFileName)) {
                String originalFilename = multipartFile.getOriginalFilename();
                String fileType = originalFilename.substring(originalFilename.lastIndexOf("."));
                originalFileName =  UUID.randomUUID().toString() + fileType;
            }
            // 上传阿里云
            String bucketDirName = OSSBucketDir.SHUIDI_CF_IMAGE.getBucketDirName();
            String file = "file/" + originalFileName;
            CfOssClient.putObject(bucketDirName, file, new ByteArrayInputStream(contentBytes));
            return CfDataUploadUtils.OSS_SHUIDI_CF_IMAGE_URL + "/" + file;
        } catch (IOException e) {
            LOGGER.error("upload error:", e);
        }
        return "";
    }
}
