package com.shuidihuzhu.cf.admin.controller.api.report.v2;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.client.adminpure.feign.ReportFeignClient;
import com.shuidihuzhu.cf.client.adminpure.model.report.CrowdfundingReportVo;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.delegate.service.impl.UserInfoServiceBizImpl;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfReportView;
import com.shuidihuzhu.cf.service.EventCenterPublishService;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.cf.service.report.ReportScheduleService;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.cf.workorder.ext.WorkOrderExtFeignClient;
import com.shuidihuzhu.client.cf.workorder.ext.WorkOrderExtVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/cf/report/v2")
@Slf4j
public class ReportV2Controller {

    @Autowired
    private ReportScheduleService reportScheduleService;

    @Autowired
    private EventCenterPublishService eventCenterPublishService;
    @Autowired
    private WorkOrderExtFeignClient workOrderExtFeignClient;
    @Autowired
    private ReportFeignClient reportFeignClient;
    @Resource
    private MsgClientV2Service msgClientV2Service;
    @Autowired
    private UserInfoServiceBizImpl userInfoServiceBiz;

    @ApiOperation("标记打款方式")
    @PostMapping("mark-pay-method")
    public Response<Void> markPayMethod(@RequestParam int caseId, @RequestParam int payMethod, @RequestParam(required = false) int legalLetter) {
        long operatorId = ContextUtil.getAdminLongUserId();
        return reportScheduleService.markPayMethod(caseId, payMethod, operatorId, legalLetter);
    }

    @RequiresPermission("approve:send-message")
    @ApiOperation("举报工单处理完成发送短信通知")
    @PostMapping("send-sms-to-all-reporters")
    @Deprecated
    public Response<Void> sendSmsToAllReporters(@RequestParam("workOrderId") long workOrderId, @RequestParam("smsText") String smsText) {
        if (workOrderId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        eventCenterPublishService.sendReportWorkOrderEnd(workOrderId, smsText);
        return NewResponseUtil.makeSuccess();
    }

    @RequiresPermission("approve:send-message")
    @ApiOperation("举报工单处理完成发送短信通知")
    @PostMapping("send-sms-to-all-reporters-v2")
    public Response<Void> sendSmsToAllReportersV2(@RequestParam("workOrderId") long workOrderId,
                                                @RequestParam("caseId") int caseId,
                                                @RequestParam("smsText") String smsText,
                                                @RequestParam("type") int type) {
        if (workOrderId <= 0 || caseId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (type == 0) {    //发送固定模板消息
            eventCenterPublishService.sendReportWorkOrderEndV2(workOrderId, caseId);
        } else if (type == 1 && StringUtils.isNotBlank(smsText)) {  //发送自定义消息
            Response<List<WorkOrderExtVO>> response = workOrderExtFeignClient.getListByName(workOrderId, OrderExtName.reportId.getName());
            if (response == null || response.notOk()) {
                log.error("获取举报工单的质疑人失败, workOrderId: {}", workOrderId);
                return null;
            }
            List<Integer> reportIds = response.getData().stream().map(WorkOrderExtVO::getValue).map(Integer::parseInt).collect(Collectors.toList());
            OperationResult<List<CrowdfundingReportVo>> reportData = Optional.ofNullable(reportFeignClient.getReportListByIds(reportIds)).orElse(OperationResult.fail());
            if (!reportData.isSuccess() || reportData.getData() == null) {
                return null;
            }
            List<CrowdfundingReportVo> reportVos = reportData.getData();
            List<Long> userIds = reportVos.stream().map(CrowdfundingReportVo::getUserId).distinct().collect(Collectors.toList());
            List<UserInfoModel> userInfoModelList = Optional.ofNullable(userInfoServiceBiz.getUserInfoByUserIdBatch(userIds)).orElse(new ArrayList<>());
            List<String> mobiles = userInfoModelList.stream().map(UserInfoModel::getCryptoMobile).collect(Collectors.toList());
            Map<String, Map<Integer, String>> smsMsgMap = new HashMap<>();
            mobiles.forEach(mobile -> {
                Map<Integer, String> param = Maps.newHashMap();
                String validMessage = StringUtils.replace(smsText, ",", "，");
                param.put(1, validMessage);
                smsMsgMap.put(mobile, param);
            });
            msgClientV2Service.sendSmsParamsMsg("121duanxin1", smsMsgMap, true);
        }

        return NewResponseUtil.makeSuccess();
    }


}
