package com.shuidihuzhu.cf.admin.controller.api.innerapi.pure;

import com.shuidihuzhu.cf.biz.crowdfunding.IAdminCredibleInfoService;
import com.shuidihuzhu.cf.client.adminpure.feign.AdminCredibleInfoFeignClient;
import com.shuidihuzhu.cf.client.adminpure.model.report.CfCredibleInfoVo;
import com.shuidihuzhu.cf.client.base.enums.IBaseErrorCode;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CfCredibleInfoDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@RestController
public class AdminCredibleInfoFeignClientController implements AdminCredibleInfoFeignClient {
    @Autowired
    private IAdminCredibleInfoService credibleInfoService;

    /**
     * 查询指定处理状态的「补充信息」
     * @param queryParam
     * @return
     */
    @Override
    public OperationResult<List<CfCredibleInfoVo>> queryRecordWithParticularStatus(QueryParam queryParam) {
        if (queryParam == null || queryParam.getCaseId() <= 0) {
            return OperationResult.fail(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<CfCredibleInfoDO> cfCredibleInfoDOS = credibleInfoService.queryByCaseId(queryParam.getCaseId());
        cfCredibleInfoDOS = cfCredibleInfoDOS.stream().filter(cfCredibleInfoDO -> queryParam.getStatus().contains(cfCredibleInfoDO.getAuditStatus())).collect(Collectors.toList());
        List<CfCredibleInfoVo> data = new ArrayList<>();
        cfCredibleInfoDOS.forEach(cfCredibleInfoDO -> {
            CfCredibleInfoVo vo = new CfCredibleInfoVo();
            vo.setId(cfCredibleInfoDO.getId());
            vo.setAuditStatus(cfCredibleInfoDO.getAuditStatus());
            vo.setCaseId(cfCredibleInfoDO.getCaseId());
            vo.setType(cfCredibleInfoDO.getType());
            vo.setSubId(cfCredibleInfoDO.getSubId());
            data.add(vo);
        });
        return OperationResult.success(data);
    }
}
