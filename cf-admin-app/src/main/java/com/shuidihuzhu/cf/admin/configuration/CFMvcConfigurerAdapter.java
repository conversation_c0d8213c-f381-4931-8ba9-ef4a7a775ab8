package com.shuidihuzhu.cf.admin.configuration;

import com.shuidihuzhu.cf.admin.interceptor.AdminWebSocketInterceptor;
import com.shuidihuzhu.cf.admin.interceptor.AuthSaasLoginAfterInterceptor;
import com.shuidihuzhu.cf.admin.resolver.Param2JsonResolver;
import com.shuidihuzhu.cf.biz.websocket.AdminWebSocketHandler;
import com.shuidihuzhu.common.web.filter.LogRequestFilter;
import com.thetransactioncompany.cors.CORSConfiguration;
import com.thetransactioncompany.cors.CORSConfigurationException;
import com.thetransactioncompany.cors.CORSFilter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.servlet.FileCleanerCleanup;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.servlet.ServletListenerRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Properties;

/**
 * Created by zhouyou on 2017/12/8.
 */
@Slf4j
@Configuration
@EnableWebSocket
public class CFMvcConfigurerAdapter extends WebMvcConfigurerAdapter implements WebSocketConfigurer  {

//    @Bean
//    public AuthSaasLoginInterceptor loginInterceptor() { return new AuthSaasLoginInterceptor(); }
//
//    @Bean
//    public AuthSaasPermissionInterceptor requiresPermissionInterceptor() {
//        return new AuthSaasPermissionInterceptor();
//    }

    @Bean
    public AuthSaasLoginAfterInterceptor authSaasLoginAfterInterceptor() {
        return new AuthSaasLoginAfterInterceptor();
    }

    @Bean
    public AdminWebSocketInterceptor webSocketInterceptor() {
        return  new AdminWebSocketInterceptor();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {

        registry.addInterceptor(authSaasLoginAfterInterceptor())
                .order(Integer.MAX_VALUE)
                .excludePathPatterns("/innerapi/cf/*/**",
                        "/admin/cf/sea-frequency/add",
                        "/admin/cftask/qcheck-allow-publish",
                        "/admin/stat/juanzhan-list-excel",
                        "/admin/cf/schedule/alarm/run",
                        "/admin/cf/schedule/alarm/init-datasource"
                )
                .addPathPatterns("/**");
    }


    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> argumentResolvers) {
        argumentResolvers.add(new Param2JsonResolver());
    }

    @Bean
    public ServletListenerRegistrationBean<FileCleanerCleanup> fileCleanerCleanup() {
        return new ServletListenerRegistrationBean<>(new FileCleanerCleanup());
    }

    @Bean
    public FilterRegistrationBean logRequestFilter() {
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean(new LogRequestFilter());
        filterRegistrationBean.setUrlPatterns(Collections.singleton("/*"));
        filterRegistrationBean.setOrder(2);
        return filterRegistrationBean;
    }

    @Bean
    public FilterRegistrationBean corsFilter() throws CORSConfigurationException {
        Properties prop = new Properties();
        prop.setProperty("cors.maxAge", "3600");
        CORSConfiguration corsConfig = new CORSConfiguration(prop);
        CORSFilter filter = new CORSFilter(corsConfig);
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean(filter);
        filterRegistrationBean.setUrlPatterns(Collections.singleton("/*"));
        filterRegistrationBean.setOrder(3);
        return filterRegistrationBean;
    }

    @Bean
    public FilterRegistrationBean paramLoseFilter() {
        List<String> urlList = Arrays.asList(
                "/admin/crowdfunding/volunteer/get-city",
                "/admin/crowdfunding/base-info/template/add-or-update",
                "/admin/workorder/highrisk/select-call-record",
                "/admin/crowdfunding/first/query-first-approve-idcard-whiteList",
                "/admin/crowdfunding/volunteer/get-city",
                "/admin/crowdfunding/change-amount/change-target-amount",
                "/admin/cf/account/list-by-name");
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean(new ParamLoseLogFilter());
        filterRegistrationBean.setUrlPatterns(urlList);
        return filterRegistrationBean;
    }

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(new AdminWebSocketHandler(), "/admin/cf/websocket/hint/push/{token}").setAllowedOrigins("*").
                addInterceptors(webSocketInterceptor());
    }

}
