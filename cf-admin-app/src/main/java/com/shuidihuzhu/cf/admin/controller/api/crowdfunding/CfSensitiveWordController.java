package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.google.common.collect.Maps;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.admin.util.UploadUtil;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.cf.admin.util.wordfilter2.SensitiveWordInit;
import com.shuidihuzhu.cf.admin.util.wordfilter2.SensitivewordFilter;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.util.Iterator;
import java.util.Map;

@Controller
@RequestMapping("/admin/cf/sensitiveWord")
public class CfSensitiveWordController {

	private static final Logger LOGGER = LoggerFactory.getLogger(CfSensitiveWordController.class);

	@Autowired
	private UploadUtil uploadUtil;

	@RequestMapping(path = "/updateFile", method = RequestMethod.POST)
	@ResponseBody
	@RequiresPermission("sensitiveWord:updateFile")
	public Response updateFile(MultipartHttpServletRequest request, @RequestParam(defaultValue = "") String name) {
		try {
			Iterator<String> iterator = request.getFileNames();
			if (iterator.hasNext()) {
				String fileName = iterator.next();
				MultipartFile multipartFile = request.getFile(fileName);
				if (multipartFile != null) {
				    if (StringUtils.isBlank(name)) {
                        name = "sensitiveWord";
                    }
					uploadUtil.doUploadV2(multipartFile, name + ".txt");
				}
			}
			SensitivewordFilter.getInstance().reload();
			return getInfo();
		} catch (Exception e) {
			LOGGER.error("", e);
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
		}
	}

	@RequestMapping(path = "/downloadFile", method = RequestMethod.POST)
	@ResponseBody
	@RequiresPermission("sensitiveWord:downloadFile")
	public Response downloadFile() {
		try {
			return NewResponseUtil.makeSuccess(SensitiveWordInit.URL);
		} catch (Exception e) {
			LOGGER.error("", e);
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
		}
	}

	@RequestMapping(path = "/get-info", method = RequestMethod.POST)
	@ResponseBody
	@RequiresPermission("sensitiveWord:get-info")
	public Response getInfo() {
		try {
			int size = SensitivewordFilter.getInstance().getSensitiveWordSize();
			Map<String, Object> result = Maps.newHashMap();
			result.put("size", size);
			return NewResponseUtil.makeSuccess(result);
		} catch (Exception e) {
			LOGGER.error("", e);
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_ERROR);
		}
	}

}
