//package com.shuidihuzhu.cf.admin.controller.api.notice;
//
//import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
//import com.shuidihuzhu.cf.enums.AdminErrorCode;
//import org.apache.commons.lang3.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.cloud.context.config.annotation.RefreshScope;
//import org.springframework.stereotype.Controller;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestMethod;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.ResponseBody;
//
//
//import com.shuidihuzhu.cf.admin.util.HuzhuRequestUtil;
//import com.shuidihuzhu.common.web.util.NewResponseUtil;
//
///**
// * @Author: wuxinlong
// * @Since: 2017-03-28
// */
//@RefreshScope
//@Controller
//@RequestMapping(path = "/admin/cf/notice/template")
//public class NoticeTemplateController {
//	private static final Logger LOGGER = LoggerFactory.getLogger(NoticeTemplateController.class);
//	@Value("${url.shuidi.api:}")
//	private String apiUrl;
//	private String baseUrl = null;
//
//	@RequestMapping(path = "list", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
//	@ResponseBody
//	@RequiresPermission("notice-template:see")
//	public Object list(@RequestParam(name = "searchInfo", required = false, defaultValue = "{}") String searchInfo,
//	                   @RequestParam(name = "pagination", required = false, defaultValue = "{}") String pagination) {
//		return HuzhuRequestUtil.getRequestResult(getBaseUrl(), "list", "searchInfo", searchInfo, "pagination", pagination);
//	}
//
//	@RequestMapping(path = "detail", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
//	@ResponseBody
//	@RequiresPermission("notice-template:see")
//	public Object detail(Integer id) {
//		if (id == null || id < 0) {
//			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
//		}
//		return HuzhuRequestUtil.getRequestResult(getBaseUrl(), "detail", "id", id + "");
//	}
//
//	@RequestMapping(path = "add-or-update", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
//	@ResponseBody
//	@RequiresPermission("notice-template:see")
//	public Object addOrUpdate(String data) {
//		if (StringUtils.isEmpty(data)) {
//			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
//		}
//		return HuzhuRequestUtil.getRequestResult(getBaseUrl(), "add-or-update", "data", data);
//	}
//
//	private String getBaseUrl() {
//		if (null == baseUrl) {
//			baseUrl = apiUrl + "/admin/notice/template/";
//		}
//
//		return baseUrl;
//	}
//}
