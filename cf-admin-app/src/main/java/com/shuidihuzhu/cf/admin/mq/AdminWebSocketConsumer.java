package com.shuidihuzhu.cf.admin.mq;

import com.shuidihuzhu.cf.biz.websocket.AdminWebSocketHandler;
import com.shuidihuzhu.cf.biz.websocket.AdminWebSocketProducer;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.mq.payload.CaseRiskHighPayload;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
@Slf4j
@RocketMQListener(id = MQTagCons.ADMIN_WEB_SOCKET_MSG_SEND,
        group = "cf-admin-"+MQTagCons.ADMIN_WEB_SOCKET_MSG_SEND,
        tags = MQTagCons.ADMIN_WEB_SOCKET_MSG_SEND,
        topic = MQTopicCons.CF)
public class AdminWebSocketConsumer implements MessageListener<AdminWebSocketProducer.WebSocketMsg> {


    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<AdminWebSocketProducer.WebSocketMsg> mqMessage) {

        log.info("websocket消息的消费. msg：{}", mqMessage.getPayload());

        AdminWebSocketProducer.WebSocketMsg msg = mqMessage.getPayload();
        if (msg == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        AdminWebSocketHandler.sendMessage(msg.getUserId(), msg.getMessage());

        return ConsumeStatus.CONSUME_SUCCESS;
    }


}


