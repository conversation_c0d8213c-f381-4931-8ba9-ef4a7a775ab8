package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderClassifySettingsBiz;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderClassifySettingsRecord;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.*;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

// 工单问题分类设置
@Slf4j
@RestController
@RequestMapping(path = "admin/cf/work-order/classify-settings")
public class AdminWorkOrderClassifyController {

    @Autowired
    private AdminWorkOrderClassifySettingsBiz classifySettingsBiz;

    @RequestMapping(path = "list", method = RequestMethod.POST)
    @ApiOperation(value = "列表", notes = "")
    @RequiresPermission("work-order-classify:list")
    public Response getWorkOrderClassifyList(@RequestParam(value = "available", required = false) Integer available) {

        return NewResponseUtil.makeSuccess(classifySettingsBiz.queryAllValidSettings(available));
    }

    @RequestMapping(path = "update", method = RequestMethod.POST)
    @ApiOperation(value = "修改", notes = "")
    @RequiresPermission("work-order-classify:update")
    public Response updateOrderClassify(@RequestParam("id") long id, @RequestParam("parentId") long parentId,
                                        @RequestParam("userId") int userId, @RequestParam("allText") String allText,
                                        @RequestParam("autoTrigger") boolean autoTriger) {

        return classifySettingsBiz.update(id, parentId, userId, allText, autoTriger);
    }

    @RequestMapping(path = "delete", method = RequestMethod.POST)
    @ApiOperation(value = "删除", notes = "")
    @RequiresPermission("work-order-classify:delete")
    public Response deleteOrderClassify(@RequestParam("id") long id, @RequestParam("parentId") long parentId,
                                        @RequestParam("userId") int userId) {

        return classifySettingsBiz.delete(id, parentId, userId);
    }

    @RequestMapping(path = "add", method = RequestMethod.POST)
    @ApiOperation(value = "新增", notes = "")
    @RequiresPermission("work-order-classify:add")
    public Response insertOrderClassify(@RequestParam("parentId") long parentId,
                                        @RequestParam("userId") int userId, @RequestParam("allText") String allText,
                                        @RequestParam("autoTrigger") boolean autoTriger) {

        return classifySettingsBiz.insert(parentId, userId, allText, autoTriger);

    }


    @RequestMapping(path = "change", method = RequestMethod.POST)
    @ApiOperation("2:启用、3:弃用、4:上移、5:下移")
    @RequiresPermission("work-order-classify:change")
    public Response<Boolean> change(@RequestParam("classifyId") long classifyId, @RequestParam("changeAction") int changeAction,
                                    @RequestParam("upId") int upId, @RequestParam("downId") int downId) {
        int adminUserId = ContextUtil.getAdminUserId();
        AdminWorkOrderClassifySettingsRecord.OperateTypeEnum operateTypeEnum = AdminWorkOrderClassifySettingsRecord.OperateTypeEnum.findByCode(changeAction);
        if (operateTypeEnum == null) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        boolean handleResult = classifySettingsBiz.changeClassify(classifyId, changeAction, upId, downId, adminUserId);
        return NewResponseUtil.makeSuccess(handleResult);
    }


    @RequestMapping(path = "show-record", method = RequestMethod.POST)
    @RequiresPermission("work-order-classify:show-record")
    public Response<List<AdminWorkOrderClassifySettingsRecord>> showRecord(@RequestParam("classifyId") long classifyId) {
        List<AdminWorkOrderClassifySettingsRecord> recordList = classifySettingsBiz.showRecords(classifyId);
        return NewResponseUtil.makeSuccess(recordList);
    }






}
