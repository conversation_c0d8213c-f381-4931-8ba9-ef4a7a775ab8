package com.shuidihuzhu.cf.admin.controller.api.innerapi.pure;

import com.shuidihuzhu.cf.biz.crowdfunding.CaseEndReasonService;
import com.shuidihuzhu.cf.client.adminpure.feign.workOrder.DeliverWorkOrderFeignClient;
import com.shuidihuzhu.cf.client.adminpure.model.caseinfo.AdminWorkOrderFlowView;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.common.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
public class DeliverWorkOrderFeignController implements DeliverWorkOrderFeignClient {
    @Autowired
    private CaseEndReasonService caseEndReasonService;
    @Override
    public OperationResult<Void> patientDied(@RequestBody AdminWorkOrderFlowView param) {
        if (param == null) {
            return OperationResult.fail(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (param.getCaseId() <= 0) {
            return OperationResult.fail(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        log.info("CaseEndFeignController patientDiedEndCase param: {}",param);

        com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView adminWorkOrderFlowView = new com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView();
        BeanUtils.copyProperties(param, adminWorkOrderFlowView, com.shuidihuzhu.cf.model.crowdfunding.vo.AdminWorkOrderFlowView.class);
        return caseEndReasonService.patientDied(adminWorkOrderFlowView);
    }
}
