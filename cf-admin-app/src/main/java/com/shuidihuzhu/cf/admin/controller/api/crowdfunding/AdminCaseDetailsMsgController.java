package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.admin.UserCommentBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.adminpure.model.caseinfo.CfCasePublicInfo;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.admin.UserCommentSourceEnum;
import com.shuidihuzhu.cf.model.admin.UserComment;
import com.shuidihuzhu.cf.model.crowdfunding.CaseDetailsMsgConverge;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminCaseDetailsMsgVo;
import com.shuidihuzhu.cf.service.approve.remark.ApproveRemarkOldService;
import com.shuidihuzhu.cf.service.crowdfunding.AdminCaseDetailsMsgService;
import com.shuidihuzhu.cf.service.workorder.imagePublic.CfCasePublicInfoService;
import com.shuidihuzhu.client.cf.admin.model.AdminCaseDetailsMsg;
import com.shuidihuzhu.client.cf.admin.model.SeaCaseLabelType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/11/25  2:13 下午
 * 需求wiki：https://wiki.shuiditech.com/pages/viewpage.action?pageId=693929349
 */
@Slf4j
@Controller
@RequestMapping(path = "/admin/crowdfunding/case")
public class AdminCaseDetailsMsgController {

    @Resource
    private AdminCaseDetailsMsgService adminCaseDetailsMsgService;
    @Resource
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;
    @Resource
    private UserCommentBiz userCommentBiz;
    @Resource
    private CfCasePublicInfoService cfCasePublicInfoService;

    @ResponseBody
    @RequestMapping(path = "/case-details-msg/add-and-update", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("case-details-msg:add-and-update")
    public Response<Void> caseDetailsMsgAddAndUpdate(@RequestBody CaseDetailsMsgConverge caseDetailsMsg) {
        int id = caseDetailsMsg.getId();
        String infoUuid = caseDetailsMsg.getInfoUuid();
        String caseLabel = caseDetailsMsg.getCaseLabel();
        String caseLabelSort = caseDetailsMsg.getCaseLabelSort();
        String caseLabelSwitch = caseDetailsMsg.getCaseLabelSwitch();
        String carouselText = caseDetailsMsg.getCarouselText();
        String headPictureUrl = caseDetailsMsg.getHeadPictureUrl();

        if (StringUtils.isEmpty(infoUuid)) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        //可编辑轮播文案，要求不超过20字
        if (carouselText.length() > 20) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }

        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (Objects.isNull(crowdfundingInfo)) {
            return NewResponseUtil.makeError(CfErrorCode.CF_NOT_FOUND);
        }

        adminCaseDetailsMsgService.caseDetailsMsgAddAndUpdate(id, crowdfundingInfo.getId(), infoUuid, headPictureUrl, carouselText, caseLabel, caseLabelSort, caseLabelSwitch, ContextUtil.getAdminUserId());
        return NewResponseUtil.makeSuccess(null);
    }

    @ResponseBody
    @RequestMapping(path = "/case-details-msg/select", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("case-details-msg:select")
    public Response<AdminCaseDetailsMsgVo> blessingCardSelectList(@RequestParam(value = "infoUuid") String infoUuid) {
        if (StringUtils.isEmpty(infoUuid)) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (Objects.isNull(crowdfundingInfo)) {
            return NewResponseUtil.makeError(CfErrorCode.CF_NOT_FOUND);
        }
        AdminCaseDetailsMsg adminCaseDetailsMsg = adminCaseDetailsMsgService.getAdminCaseDetailsMsg(infoUuid);
        AdminCaseDetailsMsgVo adminCaseDetailsMsgVo = adminCaseDetailsMsgService.getAdminCaseDetailsMsgVoByInfoUuid(crowdfundingInfo.getId(), adminCaseDetailsMsg);
        adminCaseDetailsMsgService.getCaseLabelContent(crowdfundingInfo, adminCaseDetailsMsgVo, adminCaseDetailsMsg);
        return NewResponseUtil.makeSuccess(adminCaseDetailsMsgVo);
    }

    @ResponseBody
    @RequestMapping(path = "/case-details-msg/add-family-occupation", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("case-details-msg:add-family-occupation")
    public Response<Void> addFamilyOccupation(@RequestBody CaseDetailsMsgConverge caseDetailsMsgConverge) {
        int caseId = caseDetailsMsgConverge.getCaseId();
        int workOrderId = caseDetailsMsgConverge.getWorkOrderId();
        boolean hasOccupation = caseDetailsMsgConverge.isHasOccupation();
        boolean hasFamilySituation = caseDetailsMsgConverge.isHasFamilySituation();
        String familySituation = caseDetailsMsgConverge.getFamilySituation();
        String occupation = caseDetailsMsgConverge.getOccupation();
        if (caseId <= 0) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = adminCrowdfundingInfoBiz.getFundingInfoById(caseId);
        if (Objects.isNull(crowdfundingInfo) || StringUtils.isEmpty(crowdfundingInfo.getInfoId())) {
            return NewResponseUtil.makeError(CfErrorCode.CF_NOT_FOUND);
        }
        AdminCaseDetailsMsg adminCaseDetailsMsg = adminCaseDetailsMsgService.getAdminCaseDetailsMsg(crowdfundingInfo.getInfoId());
        // 同步标签内容，和运营工具设计的标签内容和样式保持一致
        AdminCaseDetailsMsgVo adminCaseDetailsMsgVo = adminCaseDetailsMsgService.getAdminCaseDetailsMsgVoByInfoUuid(crowdfundingInfo.getId(), adminCaseDetailsMsg);
        adminCaseDetailsMsgService.getCaseLabelContent(crowdfundingInfo, adminCaseDetailsMsgVo, adminCaseDetailsMsg);

        // 标签内容
        Map<String, String> caseLabelMap = adminCaseDetailsMsgVo.getCaseLabel()
                .stream()
                .filter(f -> StringUtils.isNotEmpty(f.getLabelEnglish()))
                .collect(Collectors.toMap(SeaCaseLabelType::getLabelEnglish, value -> Objects.requireNonNullElse(value.getLabelContent(), ""), (x, y) -> x));

        // 开关
        Map<String, Integer> caseLabelSwitchMap = adminCaseDetailsMsgVo.getCaseLabel()
                .stream()
                .filter(f -> StringUtils.isNotEmpty(f.getLabelEnglish()))
                .collect(Collectors.toMap(SeaCaseLabelType::getLabelEnglish, value -> Objects.requireNonNullElse(value.getNum(), -1), (x, y) -> x));

        caseLabelMap.put("occupation", StringUtils.trim(occupation));
        caseLabelSwitchMap.put("occupation", hasOccupation ? 1 : -1);

        caseLabelMap.put("familySituation", StringUtils.trim(familySituation));
        caseLabelSwitchMap.put("familySituation", hasFamilySituation ? 1 : -1);

        String caseLabel = JSON.toJSONString(caseLabelMap);

        // 排序
        String caseLabelSort = Joiner.on(",").join(adminCaseDetailsMsgVo.getCaseLabelSort());

        String caseLabelSwitch = JSON.toJSONString(caseLabelSwitchMap);

        adminCaseDetailsMsgService.caseDetailsMsgAddAndUpdate(adminCaseDetailsMsgVo.getId(), crowdfundingInfo.getId(), crowdfundingInfo.getInfoId(), Objects.requireNonNullElse(adminCaseDetailsMsgVo.getHeadPictureUrl(), ""),
                Objects.requireNonNullElse(adminCaseDetailsMsgVo.getCarouselText(), ""), caseLabel, caseLabelSort, caseLabelSwitch, ContextUtil.getAdminUserId());


        if (hasOccupation || hasFamilySituation) {
            // 新增操作备注
            StringBuilder stringBuilder = new StringBuilder("【新增标签】");
            if (StringUtils.isNotEmpty(occupation)) {
                stringBuilder.append("职业：");
                stringBuilder.append(occupation);
                stringBuilder.append("; ");
            }
            if (StringUtils.isNotEmpty(familySituation)) {
                stringBuilder.append("家庭情况：");
                stringBuilder.append(familySituation);
                stringBuilder.append(";");
            }

            if (StringUtils.isNotEmpty(stringBuilder.toString())) {
                UserComment userComment = new UserComment(caseId, UserCommentSourceEnum.INITIAL_AUDIT, UserCommentSourceEnum.CommentType.UGC_0, ContextUtil.getAdminUserId(), "", "", stringBuilder.toString());
                userComment.setWorkOrderId(workOrderId);
                userCommentBiz.insert(userComment);
            }
        }

        return NewResponseUtil.makeSuccess(null);
    }

    @ResponseBody
    @RequestMapping(path = "/public-info-select", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("case-details-msg:public-info-select")
    public Response<List<CfCasePublicInfo>> publicInfoSelect(@RequestParam(value = "infoUuid") String infoUuid) {
        if (StringUtils.isEmpty(infoUuid)) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        return NewResponseUtil.makeSuccess(cfCasePublicInfoService.getListByInfoUuidAndType(infoUuid));
    }

    @ResponseBody
    @RequestMapping(path = "/public-info-update", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("case-details-msg:public-info-update")
    public Response<Void> publicInfoUpdate(@RequestParam(value = "param") String param) {
        if (StringUtils.isEmpty(param)) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<CfCasePublicInfo> cfCasePublicInfos = JSONObject.parseArray(param, CfCasePublicInfo.class);
        if (CollectionUtils.isEmpty(cfCasePublicInfos)) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        for (CfCasePublicInfo cfCasePublicInfo : cfCasePublicInfos) {
            if (cfCasePublicInfo.getId() != 0) {
                cfCasePublicInfoService.update(cfCasePublicInfo);
                continue;
            }
            cfCasePublicInfoService.addCasePublicInfo(cfCasePublicInfo);
        }
        return NewResponseUtil.makeSuccess(null);
    }
}
