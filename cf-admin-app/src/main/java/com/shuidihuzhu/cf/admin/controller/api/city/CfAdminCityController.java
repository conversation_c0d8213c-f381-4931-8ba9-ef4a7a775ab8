package com.shuidihuzhu.cf.admin.controller.api.city;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingCityBiz;
import com.shuidihuzhu.cf.client.adminpure.model.AdminAreaVo;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/admin/cf/city/")
@Slf4j
public class CfAdminCityController {
    @Autowired
    private AdminCrowdfundingCityBiz adminCrowdfundingCityBiz;
    /**
     * 根据名称模糊查询市
     */
    @PostMapping("get-city-name-like")
    @RequiresPermission("adminCity:get-city-name-like")
    public Response<List<AdminAreaVo>> getCityNameLike(@RequestParam("cityNameLike") String cityNameLike) {
        List<CrowdfundingCity> cities = adminCrowdfundingCityBiz.getCitiesByNameAndLevel(cityNameLike, Lists.newArrayList(1));
        List<AdminAreaVo> res = new ArrayList<>();
        cities.stream().forEach(city -> {
            res.add(buildFromCity(city));
        });
        return NewResponseUtil.makeSuccess(res);
    }

    /**
     * 根据 ID 查询城市
     * @param id
     * @return
     */
    @PostMapping("get-city-by-id")
    @RequiresPermission("adminCity:get-city-by-id")
    public Response<AdminAreaVo> getCityById(@RequestParam("id") int id) {
        if (id <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingCity city = adminCrowdfundingCityBiz.getById(id);
        if (city == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_NOT_EXIST_RECORD_ERROR);
        }
        return NewResponseUtil.makeSuccess(buildFromCity(city));
    }
    private AdminAreaVo buildFromCity(CrowdfundingCity city) {
        AdminAreaVo adminAreaVo = new AdminAreaVo();
        adminAreaVo.setId(city.getId());
        adminAreaVo.setAreaCode(city.getCode());
        adminAreaVo.setAreaName(city.getName());
        adminAreaVo.setLevel(city.getLevel() + 1);
        adminAreaVo.setRealParentId(city.getRealParentId());
        return adminAreaVo;
    }
}
