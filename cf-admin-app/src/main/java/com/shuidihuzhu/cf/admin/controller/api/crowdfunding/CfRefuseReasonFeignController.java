package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.adminpure.feign.CfRefuseReasonFeignClient;
import com.shuidihuzhu.cf.client.adminpure.model.CfRefuseReasonVo;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminRefuseReasonVo;
import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/1/2  10:33
 */
@RestController
public class CfRefuseReasonFeignController implements CfRefuseReasonFeignClient {

    @Autowired
    private AdminApproveService adminApproveService;

    @Autowired
    private AdminCrowdfundingInfoBiz adminCrowdfundingInfoBiz;

    @Override
    public OperationResult<List<CfRefuseReasonVo>> getCfRefuseReasonList(List<Integer> caseIds) {
        if (CollectionUtils.isEmpty(caseIds)) {
            return OperationResult.success();
        }

        List<CfRefuseReasonVo> result = Lists.newArrayList();
        List<CrowdfundingInfo> crowdfundingInfoList = adminCrowdfundingInfoBiz.getListByIds(caseIds);
        for (CrowdfundingInfo crowdfundingInfo : crowdfundingInfoList) {
            if (crowdfundingInfo.getStatus() == CrowdfundingStatus.APPROVE_DENIED) {
                List<AdminRefuseReasonVo> adminRefuseReasonVoList = adminApproveService.getRejectDetail(crowdfundingInfo);
                if (CollectionUtils.isNotEmpty(adminRefuseReasonVoList)) {
                    CfRefuseReasonVo cfRefuseReasonVo = new CfRefuseReasonVo();
                    cfRefuseReasonVo.setCaseId(crowdfundingInfo.getId());
                    List<String> refuseReason = adminRefuseReasonVoList.stream().map(AdminRefuseReasonVo::getRefuseReason).flatMap(List::stream).collect(Collectors.toList());
                    cfRefuseReasonVo.setRefuseReason(refuseReason);
                    result.add(cfRefuseReasonVo);
                }
            }
        }

        return OperationResult.success(result);
    }
}
