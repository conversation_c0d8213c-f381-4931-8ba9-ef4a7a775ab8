package com.shuidihuzhu.cf.admin.mq.markfollowuptime;

import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.enhancer.mq.BaseMessageConsumer;
import com.shuidihuzhu.cf.service.markfollowuptime.CfMarkFollowService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@RocketMQListener(id = MQTagCons.MARK_FOLLOW_PUSH,
        group = "cf-admin-" + MQTagCons.MARK_FOLLOW_PUSH,
        tags = MQTagCons.MARK_FOLLOW_PUSH,
        topic = MQTopicCons.CF)
@Slf4j
public class CfMarkFollowConsumer extends BaseMessageConsumer<String> implements MessageListener<String> {

    @Autowired
    private CfMarkFollowService cfMarkFollowService;

    @Override
    protected boolean handle(ConsumerMessage<String> consumerMessage) {
        String json = consumerMessage.getPayload();
        cfMarkFollowService.onDelayHandle(json);
        return true;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
