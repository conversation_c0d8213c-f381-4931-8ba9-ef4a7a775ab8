package com.shuidihuzhu.cf.admin.mq.growth;

import com.shuidihuzhu.cf.biz.crowdfunding.NoticeGrowthToolBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.impl.CaseEndReasonServiceImpl;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.CaseEndModel;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @DATE 2020/4/9
 */
@Service
@RocketMQListener(id = "notice_growth_case_end",
        group = "notice_growth_" + MQTagCons.CF_CASE_END,
        tags = MQTagCons.CF_CASE_END,
        topic = MQTopicCons.CF)
@Slf4j
public class CaseEndNoticeConsumer implements MessageListener<CaseEndModel> {

    @Autowired
    private NoticeGrowthToolBiz noticeGrowthToolBiz;

    @Resource
    private CaseEndReasonServiceImpl caseEndReasonService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CaseEndModel> mqMessage) {
        CaseEndModel caseEndModel = mqMessage.getPayload();
        int caseId = caseEndModel.getCaseId();
        noticeGrowthToolBiz.notice(caseId);

        try {
            caseEndReasonService.raiserFinishCaseAction(caseEndModel);
        } catch (Exception e) {
            log.error("CaseEndNoticeConsumer raiserFinishCaseAction error", e);
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
