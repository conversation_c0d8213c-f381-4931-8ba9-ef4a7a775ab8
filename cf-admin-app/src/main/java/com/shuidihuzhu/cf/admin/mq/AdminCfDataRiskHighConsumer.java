package com.shuidihuzhu.cf.admin.mq;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.admin.mq.common.CommonConsumerHelper;
import com.shuidihuzhu.cf.biz.crowdfunding.CfAdminOperationRecordBiz;
import com.shuidihuzhu.cf.constants.AdminUserIDConstants;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.delegate.risk.IRiskDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.CfOperatingRecordEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfOperationRecordEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.risk.CfCaseRiskTypeEnum;
import com.shuidihuzhu.cf.facade.risk.CaseRiskConfig;
import com.shuidihuzhu.cf.finance.client.feign.CfFinancePauseFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.enums.CfDrawCashPauseRecordEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.mq.payload.CaseRiskHighPayload;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * 数据审核 案例高风险消息
 * 案例高风险 自动暂停打款
 */
@Service
@RocketMQListener(id = MQTagCons.CASE_DATE_RISK_CHECK_NON_PASSED,
        group = "cf-admin" + MQTagCons.CASE_DATE_RISK_CHECK_NON_PASSED,
        tags = MQTagCons.CASE_DATE_RISK_CHECK_NON_PASSED,
        topic = MQTopicCons.CF)
@Slf4j
public class AdminCfDataRiskHighConsumer implements MessageListener<CaseRiskHighPayload> {

    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Resource
    private CaseRiskConfig caseRiskConfig;

    @Resource
    private CommonConsumerHelper commonConsumerHelper;

    @Resource
    private IRiskDelegate riskDelegate;

    @Resource
    private CfAdminOperationRecordBiz cfAdminOperationRecordBiz;
    @Resource
    private CfFinancePauseFeignClient cfFinancePauseFeignClient;

    private static final int OPERATION_HANDLE = CfOperationRecordEnum.CASE_RISK_HANDLE.value();

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CaseRiskHighPayload> mqMessage) {
        return commonConsumerHelper.consumeMessage(mqMessage, this::handle, false);
    }

    protected boolean handle(ConsumerMessage<CaseRiskHighPayload> consumerMessage) {

        CaseRiskHighPayload payload = consumerMessage.getPayload();
        String infoUuid = payload.getInfoUuid();
        CfCaseRiskTypeEnum riskTypeEnum = CfCaseRiskTypeEnum.parse(payload.getRiskType());

        // 开关关着就不自动暂停打款
        if (riskTypeEnum == CfCaseRiskTypeEnum.CASE_INFO_RISK
                && !caseRiskConfig.isCheckInfoRisk()) {
            // 开关关着 默认自动通过
            riskDelegate.handleInfoRisk(infoUuid);
            cfAdminOperationRecordBiz.addOneOperationRecord(infoUuid, AdminUserIDConstants.SYSTEM, OPERATION_HANDLE, "系统自动通过");
            return true;
        }

        // 开关关着就不自动暂停打款
        if (riskTypeEnum == CfCaseRiskTypeEnum.DRAW_CASH_RISK
                && !caseRiskConfig.isCheckDrawCaskRisk()) {
            // 开关关着 默认自动通过
            riskDelegate.handleDrawCaskRisk(infoUuid);
            cfAdminOperationRecordBiz.addOneOperationRecord(infoUuid, AdminUserIDConstants.SYSTEM, OPERATION_HANDLE, "系统自动通过");
            return true;
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingDelegate.getCrowdfundingInfoByInfoId(infoUuid);

        //改为 cf-finance-api 的feign实现
        FeignResponse feignResponse = cfFinancePauseFeignClient.addPause(0, infoUuid,
                crowdfundingInfo.getId(), CfDrawCashPauseRecordEnum.PauseSourceTypeEnum.SYSTEM_RISK_CASE.getCode(),
                Lists.newArrayList(CfDrawCashPauseRecordEnum.PauseReasonTypeEnum.SYSTEM_RISK_CASE.getCode()),
                CfOperatingRecordEnum.Role.SYSTEM.getCode(), "cf-admin-案例高风险", "cf-admin-案例高风险", false);
        log.info("cf-admin-案例高风险\tfeignResponse:{}\tinfoId{}", JSON.toJSONString(feignResponse), crowdfundingInfo.getId());

        return true;
    }

    private String getReasonByRiskType(CfCaseRiskTypeEnum riskTypeEnum) {
        if (riskTypeEnum == CfCaseRiskTypeEnum.CASE_INFO_RISK) {
            return "暂停打款-审核数据异常";
        }
        if (riskTypeEnum == CfCaseRiskTypeEnum.DRAW_CASH_RISK) {
            return "暂停打款-提现数据异常";
        }
        return "暂停打款-未知风险异常";
    }

}
