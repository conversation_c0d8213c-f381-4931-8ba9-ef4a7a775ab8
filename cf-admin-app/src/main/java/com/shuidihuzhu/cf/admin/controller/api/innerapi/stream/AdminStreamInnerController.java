package com.shuidihuzhu.cf.admin.controller.api.innerapi.stream;

import com.shuidihuzhu.cf.service.stream.manager.StreamData;
import com.shuidihuzhu.cf.service.stream.manager.StreamDataManageService;
import com.shuidihuzhu.cf.service.stream.manager.StreamStringManageService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(path="innerapi/cf/cf-admin-api/stream")
@Slf4j
public class AdminStreamInnerController {

    @Autowired
    private StreamStringManageService streamStringManageService;

    @Autowired
    private StreamDataManageService streamDataManageService;

    @PostMapping("test-push-string")
    public Response<Void> testPushString(String subject, String key, String value){
        streamStringManageService.pushBroadcast(subject, key, NewResponseUtil.makeSuccess(value));
        return NewResponseUtil.makeSuccess(null);
    }

    @PostMapping("test-push-data")
    public Response<Void> testPushData(String subject, String key, @RequestBody StreamData value){
        streamDataManageService.pushBroadcast(subject, key, NewResponseUtil.makeSuccess(value));
        return NewResponseUtil.makeSuccess(null);
    }

}