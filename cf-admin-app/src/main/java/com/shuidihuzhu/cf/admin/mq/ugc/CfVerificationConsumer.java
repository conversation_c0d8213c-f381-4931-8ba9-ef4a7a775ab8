package com.shuidihuzhu.cf.admin.mq.ugc;

import com.shuidihuzhu.cf.biz.admin.exception.ManyVerifyInfoException;
import com.shuidihuzhu.cf.biz.crowdfunding.CfSensitiveWordRecordBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.UserOperationStatConstant;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification;
import com.shuidihuzhu.cf.model.crowdfunding.deliver.CrowdFundingVerificationDeliver;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Author: Alvin Tian
 * Date: 2017/11/9 20:33
 */
@Service
@RocketMQListener(id = MQTagCons.CF_VERIFICATION_MSG, tags = MQTagCons.CF_VERIFICATION_MSG, topic = MQTopicCons.CF, group = "Cf-admin-ConsumerGroup_CF_VERIFICATION_SUCCESS")
@Slf4j
public class CfVerificationConsumer implements MessageListener<CrowdFundingVerificationDeliver> {

    @Resource
	private CfSensitiveWordRecordBiz cfSensitiveWordRecordBiz;

	@Resource
	private MeterRegistry meterRegistry;

	@Override
	public ConsumeStatus consumeMessage(ConsumerMessage<CrowdFundingVerificationDeliver> mqMessage) {

	    if(mqMessage == null || mqMessage.getPayload() == null){
	    	return ConsumeStatus.CONSUME_SUCCESS;
		}

		//上报一次打点信息
		meterRegistry.counter(UserOperationStatConstant.USER_OPERATING_STAT,
				UserOperationStatConstant.OPERATION, UserOperationStatConstant.VERIFICATION).increment();

		log.info("receive CrowdFundingVerification message: {}", mqMessage.getPayload());

		try {
			cfSensitiveWordRecordBiz.buildVerificationOne(mqMessage.getPayload(), false);
		} catch (ManyVerifyInfoException e) {
			log.info("query user real info failed, wait next time");
			return ConsumeStatus.RECONSUME_LATER;
		} catch (Exception e){
	    	log.error("CfVerificationConsumer error {}", mqMessage.getPayload(), e);
			return ConsumeStatus.RECONSUME_LATER;
		}

		return ConsumeStatus.CONSUME_SUCCESS;
	}
}
