package com.shuidihuzhu.cf.admin.mq.workorder;

import com.shuidihuzhu.cf.service.workorder.imagePublic.ImagePublicWorkOrderService;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.constant.CfClientMQTopicCons;
import com.shuidihuzhu.client.model.event.InfoApproveEvent;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Author: wangpeng
 * @Date: 2021/8/19 11:47
 * @Description:
 */
@Slf4j
@Service
@RefreshScope
@RocketMQListener(id = "image-public-work-order" + CfClientMQTagCons.INFO_APPROVE_MSG, tags = CfClientMQTagCons.INFO_APPROVE_MSG,
        topic = CfClientMQTopicCons.CF, group = "image-public-work-order_" + CfClientMQTagCons.INFO_APPROVE_MSG)
public class ImagePublicWorkOrderConsumer implements MessageListener<InfoApproveEvent> {

    @Resource
    private ImagePublicWorkOrderService imagePublicWorkOrderService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<InfoApproveEvent> mqMessage) {
        log.info("imagePublicWorkOrderConsumer is recive is success {}", mqMessage);
        if (Objects.isNull(mqMessage) || Objects.isNull(mqMessage.getPayload())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        InfoApproveEvent infoApproveEvent = mqMessage.getPayload();
        imagePublicWorkOrderService.createWorkOrder(infoApproveEvent);
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
