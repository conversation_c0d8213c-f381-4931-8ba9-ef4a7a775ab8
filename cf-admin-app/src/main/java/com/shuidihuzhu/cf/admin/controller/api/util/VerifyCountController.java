package com.shuidihuzhu.cf.admin.controller.api.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.commonservice.ICommonServiceDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.model.crowdfunding.BankCardVerifyResult;
import com.shuidihuzhu.cf.service.admin.VerifyCountService;
import com.shuidihuzhu.cf.util.BackCardUtil;
import com.shuidihuzhu.cf.vo.admin.VerifyCountVo;
import com.shuidihuzhu.cf.vo.admin.initialAudit.DiseaseStrategyResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.Consts;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.IdCardUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 验证次数清零的工具
 * <AUTHOR>
 * @date 2020/11/04
 */

@RestController
@RequestMapping(path = "/admin/cf/util/verify")
@Slf4j
public class VerifyCountController {


	@Autowired
	private VerifyCountService verifyCountService;
	@Resource
	private UserInfoServiceBiz userInfoServiceBiz;

	@PostMapping(path = "/get-all")
	@ResponseBody
	@RequiresPermission("verify:get")
	@ApiOperation("获取节点信息以及限制次数的接口")
	public Response<List<VerifyCountVo>> getVerify(@ApiParam("手机号") String phoneNumber) {
		phoneNumber = StringUtils.trimToEmpty(phoneNumber);
		if (StringUtils.isBlank(phoneNumber) || !Consts.pMobile.matcher(phoneNumber).matches()) {
			return NewResponseUtil.makeError(AdminErrorCode.MOBILE_FORMAT_ERROR);
		}
		//获取userId
		long userId = getUserId(phoneNumber);
		if (userId == 0){
			return NewResponseUtil.makeFail("该手机号无绑定的用户");
		}
		return NewResponseUtil.makeSuccess(verifyCountService.getAll(userId));
	}

	@PostMapping(path = "/clear")
	@ResponseBody
	@RequiresPermission("verify:clear")
	@ApiOperation("清楚次数的接口")
	public Response<Boolean> getVerify(@ApiParam("手机号") String phoneNumber, @ApiParam("需要清除的key") String key) {
		key = StringUtils.trimToEmpty(key);
		phoneNumber = StringUtils.trimToEmpty(phoneNumber);
		if (StringUtils.isBlank(key)) {
			return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
		}
		if (StringUtils.isBlank(phoneNumber) || !Consts.pMobile.matcher(phoneNumber).matches()) {
			return NewResponseUtil.makeError(AdminErrorCode.MOBILE_FORMAT_ERROR);
		}
		//获取userId
		long userId = getUserId(phoneNumber);
		if (userId == 0){
			return NewResponseUtil.makeFail("该手机号无绑定的用户");
		}
		return NewResponseUtil.makeSuccess(verifyCountService.clear(userId, key));
	}


	/*@PostMapping(path = "/inner-test")
	@ResponseBody
	@RequiresPermission("verify:clear")
	@ApiOperation("清楚次数的接口")
	public Response<Void> innerTest(@ApiParam("手机号") String phoneNumber, @ApiParam("需要清除的key") String key) {
		key = StringUtils.trimToEmpty(key);
		//获取userId
		long userId = getUserId(phoneNumber);
		if (userId == 0){
			return NewResponseUtil.makeFail("该手机号无绑定的用户");
		}
		verifyCountService.innerTest(userId, key);
		return NewResponseUtil.makeSuccess(null);
	}*/

	private long getUserId(String phoneNumber) {
		UserInfoModel userInfoModel = userInfoServiceBiz.getUserInfoByMobile(phoneNumber);
		if (userInfoModel == null) {
			return 0L;
		}
		return  userInfoModel.getUserId();
	}
}
