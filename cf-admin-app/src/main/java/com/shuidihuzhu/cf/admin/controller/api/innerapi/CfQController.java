package com.shuidihuzhu.cf.admin.controller.api.innerapi;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.CfQuestionnaireBiz;
import com.shuidihuzhu.cf.model.admin.CfQuestionnaire;
import com.shuidihuzhu.client.cf.admin.client.CfQuestionnaireClient;
import com.shuidihuzhu.client.cf.admin.model.CfQuestionnaireVo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2020/2/27
 */
@RestController
public class CfQController implements CfQuestionnaireClient {

    @Autowired
    private CfQuestionnaireBiz biz;

    @Override
    public Response<List<CfQuestionnaireVo>> getByCard(String card) {

        List<CfQuestionnaire> list = biz.getListByCard(card);

        if (CollectionUtils.isEmpty(list)){
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }

        List<CfQuestionnaireVo> result = list.stream().map(r->{
                                                    CfQuestionnaireVo vo = new CfQuestionnaireVo();
                                                    BeanUtils.copyProperties(r,vo);
                                                    return vo;
                                                }).collect(Collectors.toList());


        return NewResponseUtil.makeSuccess(result);
    }
}
