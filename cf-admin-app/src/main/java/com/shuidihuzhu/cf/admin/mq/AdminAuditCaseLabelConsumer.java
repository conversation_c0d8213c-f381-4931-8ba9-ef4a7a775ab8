package com.shuidihuzhu.cf.admin.mq;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCaseDetailsMsgDao;
import com.shuidihuzhu.cf.enums.admin.TwModifyChannelEnum;
import com.shuidihuzhu.cf.facade.eagle.AdminEagleFacade;
import com.shuidihuzhu.cf.model.admin.TwModifyRecordDO;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cf.mq.producer.impl.MQProdecer;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import com.shuidihuzhu.client.cf.admin.model.AdminCaseDetailsMsg;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import com.shuidihuzhu.data.analytics.javasdk.core.UserTagTypeEnum;
import com.shuidihuzhu.data.servicelog.meta.cf.CaseManualTag;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
@RocketMQListener(id = "cf-case-label" + com.shuidihuzhu.cf.constants.MQTagCons.CF_INITIAL_AUDIT_OPERATION_MSG,
        group = "cf-admin-label" + com.shuidihuzhu.cf.constants.MQTagCons.CF_INITIAL_AUDIT_OPERATION_MSG,
        tags = com.shuidihuzhu.cf.constants.MQTagCons.CF_INITIAL_AUDIT_OPERATION_MSG,
        topic = MQTopicCons.CF)
@Slf4j
@RefreshScope
public class AdminAuditCaseLabelConsumer implements MessageListener<InitialAuditItem.InitialAuditOperation>  {

    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private AdminEagleFacade eagleFacade;
    @Value("${case_label_detail:}")
    private String caseLabel;
    @Autowired
    private Analytics analytics;
    @Autowired
    private AdminCaseDetailsMsgDao caseDetailsMsgDao;

    @Resource(name = "cf2RedissonHandler")
    private RedissonHandler redissonHandler;
    @Resource
    private AlarmClient alarmClient;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<InitialAuditItem.InitialAuditOperation> mqMessage) {

        log.info("receive case label CF_INITIAL_AUDIT_OPERATION_MSG message: {}", JSONObject.toJSONString(mqMessage));
        if (mqMessage == null || mqMessage.getPayload() == null) {
            log.error("CF_INITIAL_AUDIT_OPERATION_MSG消息体为空");
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        int caseId = mqMessage.getPayload().getCaseId();
        CrowdfundingInfo cfInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        List<Integer> passIds = mqMessage.getPayload().getPassIds();
        if(!passIds.contains(InitialAuditItem.CASE_BASE_INFO_MATERIAL)
                || !passIds.contains(InitialAuditItem.FIRST_APPROVE_INFO)
                || !passIds.contains(InitialAuditItem.CREDIT_INFO)){
            log.info("INITIAL_AUDIT passId is not pass");
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        String eye = eagleFacade.getStringByKey(caseId, "tag", "0");
        StringBuffer sbf = new StringBuffer();
        String contentLabel = "";

        String key = "adminAuditCaseLabelConsumer_" + caseId;
        String tryLock = null;
        try {
            tryLock = redissonHandler.tryLock(key, 1000L, 1000L);

            if (StringUtils.isNotBlank(tryLock)) {
                AdminCaseDetailsMsg caseMsg = caseDetailsMsgDao.getByCaseId(cfInfo.getId());
                if ("1".equals(eye)) {
                    log.info("update case msg tag caseId:{},eye:{}",caseId,eye);
                    //测试组 标题和求助说明关键词
                    if(StringUtils.isNotBlank(caseLabel)){
                        List<String> caseLabels = Lists.newArrayList(caseLabel.split("、"));
                        String title = cfInfo.getTitle();
                        String content = cfInfo.getContent();
                        caseLabels.stream().forEach(label->{
                            if(title.contains(label) || content.contains(label)){
                                sbf.append(label).append(",");
                            }
                        });
                        //没命中关键词 案例详情页不展示标签项
                        if(StringUtils.isNotBlank(sbf.toString())){
                            log.info("update case msg tag caseId:{},eye:{}",caseId,eye);
                            updateMsgTag(caseId, cfInfo, caseMsg);
                            contentLabel = sbf.substring(0, sbf.toString().length() - 1);
                        }
                    }
                }else{
                    log.info("update case msg tag caseId:{},eye:{}",caseId,eye);
                    updateMsgTag(caseId, cfInfo, caseMsg);
                }
            }

        } catch (Exception e) {
            log.error("加锁异常", e);
        } finally {
            try {
                if (StringUtils.isNotBlank(tryLock)) {
                    redissonHandler.unLock(key, tryLock);
                }
            } catch (Exception e) {
                log.info("释放锁异常", e);
            }
        }

        trackData(cfInfo.getId(),contentLabel,cfInfo.getUserId());
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private void updateMsgTag(int caseId, CrowdfundingInfo cfInfo, AdminCaseDetailsMsg caseMsg) {
        if(caseMsg ==null){
            caseMsg = new AdminCaseDetailsMsg();
            caseMsg.setCaseId(caseId);
            caseMsg.setShowTag(1);
            caseMsg.setInfoUuid(cfInfo.getInfoId());
            caseDetailsMsgDao.insert(caseMsg);
        }else{
            caseDetailsMsgDao.updateShowTag(cfInfo.getId());
        }
    }

    private void trackData(int caseId,String content,long userId){
        //打点上报
        CaseManualTag tag = new CaseManualTag();
        tag.setCase_content(content);
        tag.setCase_id(String.valueOf(caseId));
        tag.setUser_tag_type(UserTagTypeEnum.userid);
        tag.setUser_tag(String.valueOf(userId));

        try {
            analytics.track(tag);
            log.info("大数据打点上报,case label tag:{}", JSONObject.toJSONString(tag));
        } catch (Exception e) {
            log.error("大数据打点上报异常,case label tag 敏感词:{}", JSONObject.toJSONString(tag), e);
        }

    }

}
