package com.shuidihuzhu.cf.admin.controller.api.workorder;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.fundUse.CfFundUseProgressBiz;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.admin.FundUseCaseDetailVo;
import com.shuidihuzhu.cf.model.admin.FundUseWorkOrderInfo;
import com.shuidihuzhu.cf.model.admin.FundUseWorkOrderVO;
import com.shuidihuzhu.cf.model.admin.vo.PromoteBillHandleParam;
import com.shuidihuzhu.cf.service.workorder.WorkOrderFundUseService;
import com.shuidihuzhu.cf.service.workorder.promoteBill.CfPromoteBillService;
import com.shuidihuzhu.client.cf.workorder.model.FundUseHandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderListParam;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: fengxuan
 * @create 2019-12-18 21:29
 **/
@Api("资金用户审核")
@Slf4j
@RestController
@RequestMapping(path = "/admin/workorder/fundUse")
public class FundUseOrderController {

    private static final List<Integer> fundUseCanHandleStatus = Lists.newArrayList(
            HandleResultEnum.audit_pass.getType(),
            HandleResultEnum.audit_reject.getType()
    );

    @Autowired
    private WorkOrderFundUseService workOrderFundUseService;
    @Autowired
    private CfPromoteBillService promoteBillService;
    @Resource
    private CfFundUseProgressBiz cfFundUseProgressBiz;

    @RequiresPermission("fundUse:orderlist")
    @ApiOperation("任务处理中心列表")
    @PostMapping("funduse-orderlist")
    public Response<PageResult<FundUseWorkOrderVO>> fundUseOrderList(@RequestParam("param") String param) {
        WorkOrderListParam workOrderListParam = JSON.parseObject(param, WorkOrderListParam.class);//已检查过
        return workOrderFundUseService.fundUseOrderList(workOrderListParam);
    }

    @RequiresPermission("fundUse:get-detail")
    @ApiOperation("资金用途详情页-案例信息")
    @PostMapping(path = "get-detail")
    public Response<FundUseCaseDetailVo> getCaseDetail(@RequestParam("caseId") int caseId) {
        return workOrderFundUseService.getCaseDetail(caseId);
    }

    @RequiresPermission("fundUse:list-bycaseid")
    @ApiOperation("资金用途详情页-工单列表信息")
    @PostMapping(path = "funduse-list-bycaseid")
    public Response<List<FundUseWorkOrderInfo>> listFundUseWorkOrder(@RequestParam("caseId") int caseId,
                                                                     @RequestParam(value = "workOrderId", defaultValue = "0") int workOrderId) {
        int adminUserId = ContextUtil.getAdminUserId();
        if (adminUserId <= 0 || caseId <= 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        return workOrderFundUseService.listFundUseWorkOrder(caseId, workOrderId);
    }

    @RequiresPermission("fundUse:handle")
    @ApiOperation("操作工单,审核通过、驳回、举报介入")
    @PostMapping(path = "handle-funduse")
    public Response<Boolean> handleFundUse(@RequestParam("param") String param) {
        List<FundUseHandleOrderParam> auditParamList = JSON.parseArray(param, FundUseHandleOrderParam.class);//已检查过
        Response<Boolean> checkResponse = checkHandleParam(auditParamList);
        if (checkResponse.notOk()) {
            return checkResponse;
        }
        int adminUserId = ContextUtil.getAdminUserId();
        if (adminUserId <= 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        for (FundUseHandleOrderParam handleOrderParam : auditParamList) {
            Response<Boolean> handleFundUseWork = workOrderFundUseService.handleFundUseWork(handleOrderParam, adminUserId);
            if (handleFundUseWork.notOk() || !handleFundUseWork.getData()) {
                log.warn("FundUseOrderController handleFundUse is fail : {}", handleFundUseWork);
            }
        }
        return NewResponseUtil.makeSuccess(true);
    }


    @RequiresPermission("promote-bill:handle")
    @PostMapping(path = "handle-promote-bill")
    public Response<String> handlePromoteBill(@RequestBody PromoteBillHandleParam handleParam) {
        handleParam.setOperateId(ContextUtil.getAdminUserId());
        if (handleParam.getCaseId() == 0 || handleParam.getWorkOrderId() == 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (handleParam.getHandleResult() != HandleResultEnum.done.getType()
                && handleParam.getHandleResult() != HandleResultEnum.later_doing.getType()) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        return NewResponseUtil.makeError(promoteBillService.addResult(handleParam));
    }

    @RequiresPermission("promote-bill:select")
    @PostMapping(path = "get-promote-bill")
    public Response<PromoteBillHandleParam> getHandleParamInfo(@RequestParam long workOrderId) {

        return NewResponseUtil.makeSuccess( promoteBillService.selectLastByWorkOrderId(workOrderId));
    }

    @RequiresPermission("promote-bill:handle")
    @PostMapping(path = "check-promote-bill")
    public Response<String> promoteCheck(@RequestParam int caseId, @RequestParam long workOrderId) {

        return NewResponseUtil.makeError(promoteBillService.canExceptionClose(caseId, workOrderId));
    }

    @RequiresPermission("promote-bill:update-fund-use")
    @PostMapping(path = "update-fund-use")
    public Response<Integer> updateFundUse(@RequestParam long fundUseProgressId, @RequestParam String content, @RequestParam String imageUrls) {
        return NewResponseUtil.makeSuccess(cfFundUseProgressBiz.updateContentAndImg(fundUseProgressId, content, imageUrls));
    }



    private Response<Boolean> checkHandleParam(List<FundUseHandleOrderParam> paramList) {
        if (CollectionUtils.isEmpty(paramList)) {
            return NewResponseUtil.makeFail("传入参数错误!");
        }
        if (paramList.size() > 10) {
            return NewResponseUtil.makeFail("批量通过不能大于10条!");
        }
        long handleResultCount = paramList.stream()
                .map(FundUseHandleOrderParam::getHandleResult)
                .filter(fundUseCanHandleStatus::contains)
                .count();
        if (handleResultCount == 0L) {
            return NewResponseUtil.makeFail("传入的操作类型错误!");
        }
        if (StringUtils.isEmpty(paramList.get(0).getOperComment())) {
            return NewResponseUtil.makeFail("审核原因不能为空!");
        }
        return NewResponseUtil.makeSuccess(true);
    }
}
