package com.shuidihuzhu.cf.admin.controller.api.workorder.v2;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.workOrder.ExternalWorkOrderApiBiz;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.cs.work.order.client.dto.CascadeRuleFields;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2021/7/13  3:01 下午
 */
@RequestMapping("/admin/work-order/external")
@RestController
public class ExternalWorkOrderApiController {

    @Autowired
    private ExternalWorkOrderApiBiz externalWorkOrderApiBiz;

    @RequiresPermission("work-order:mapping-rule/fields/cascade/get-all")
    @RequestMapping(path = "mapping-rule/fields/cascade/get-all")
    public Response<CascadeRuleFields> getAll() {
        return NewResponseUtil.makeSuccess(externalWorkOrderApiBiz.queryAllFeldsCascade());
    }

}
