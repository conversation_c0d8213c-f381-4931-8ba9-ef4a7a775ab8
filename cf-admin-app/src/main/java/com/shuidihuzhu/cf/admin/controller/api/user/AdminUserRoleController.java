package com.shuidihuzhu.cf.admin.controller.api.user;

import com.shuidihuzhu.cf.delegate.saas.AdminUserAccountModel;
import com.shuidihuzhu.cf.delegate.saas.AuthRpcResponse;
import com.shuidihuzhu.cf.delegate.saas.SeaRoleClientV1;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/7
 */

@Slf4j
@RestController
@RequestMapping(path = "/admin/cf/account/role")
public class AdminUserRoleController {

    @Autowired
    private SeaRoleClientV1 seaRoleClientV1;


    @PostMapping("valid-user-role")
    @ResponseBody
    public Response<Boolean> validRoleOfUser(@RequestParam Long roleId) {
        int userId = ContextUtil.getAdminUserId();
        if (userId <= 0) {
            return NewResponseUtil.makeError(CfErrorCode.USER_ACCOUNT_NO_LOGIN);
        }
        boolean result = seaRoleClientV1.validUserRole(userId, roleId);
        return NewResponseUtil.makeSuccess(result);
    }


}
