package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCasePageAnnouncementManageBiz;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.admin.PageUtil;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api("案例页公告&辟谣管理")
@RequestMapping(path = "/admin/cf/case-page-announcement-manage")
public class AdminCasePageAnnouncementManageController {

    @Resource
    private AdminCasePageAnnouncementManageBiz adminCasePageAnnouncementManageBiz;


    @PostMapping("get-list")
    @RequiresPermission("case-page-announcement-manage:get-list")
    public Response<Map<String, Object>> getList(@RequestParam(value = "current", defaultValue = "1", required = false) int current,
                                                 @RequestParam(value = "pageSize", defaultValue = "10", required = false) int pageSize,
                                                 @RequestParam(value = "title", defaultValue = "", required = false) String title,
                                                 @RequestParam(value = "status", defaultValue = "-1", required = false) int status,
                                                 @RequestParam(value = "top", defaultValue = "-1", required = false) int top,
                                                 @RequestParam(value = "type", defaultValue = "-1", required = false) int type) {

        List<AdminCasePageAnnouncementManageVo> list = adminCasePageAnnouncementManageBiz.getList(current, pageSize, title, status, top, type);
        Map<String, Object> result = Maps.newHashMap();
        result.put("pagination", PageUtil.transform2PageMap(list));
        result.put("data", list);

        return NewResponseUtil.makeSuccess(result);
    }

    @PostMapping("add-or-update")
    @RequiresPermission("case-page-announcement-manage:add-or-update")
    public Response<Integer> addOrUpdate(@RequestParam(value = "id", defaultValue = "0", required = false) long id,
                                         @RequestParam(value = "title") String title,
                                         @RequestParam(value = "imgUrl") String imgUrl,
                                         @RequestParam(value = "type") int type,
                                         @RequestParam(value = "status", defaultValue = "0", required = false) int status,
                                         @RequestParam(value = "popImgUrl", defaultValue = "", required = false) String popImgUrl,
                                         @RequestParam(value = "shortcutUrl", defaultValue = "", required = false) String shortcutUrl,
                                         @RequestParam(value = "shortcutUrlDesc", defaultValue = "", required = false) String shortcutUrlDesc,
                                         @RequestParam(value = "top", defaultValue = "0", required = false) int top) {
        long adminUserId = ContextUtil.getAdminLongUserId();

        int res = adminCasePageAnnouncementManageBiz.addOrUpdate(id, title, status, top, type, imgUrl, popImgUrl, shortcutUrl, shortcutUrlDesc, adminUserId);

        return NewResponseUtil.makeSuccess(res);
    }

    @PostMapping("delete")
    @RequiresPermission("case-page-announcement-manage:delete")
    public Response<Integer> delete(@RequestParam(value = "id") long id) {
        long adminUserId = ContextUtil.getAdminLongUserId();

        int res = adminCasePageAnnouncementManageBiz.delete(id, adminUserId);

        return NewResponseUtil.makeSuccess(res);
    }

    @PostMapping("update-by-online")
    @RequiresPermission("case-page-announcement-manage:update-by-online")
    public Response<Integer> updateByOnline(@RequestParam(value = "id") long id,
                                            @RequestParam(value = "status") int status) {
        long adminUserId = ContextUtil.getAdminLongUserId();

        int res = adminCasePageAnnouncementManageBiz.updateByOnline(id, status, adminUserId);

        return NewResponseUtil.makeSuccess(res);
    }

    @PostMapping("update-by-top")
    @RequiresPermission("case-page-announcement-manage:update-by-top")
    public Response<Integer> updateByTop(@RequestParam(value = "id") long id,
                                         @RequestParam(value = "top") int top) {
        long adminUserId = ContextUtil.getAdminLongUserId();

        int res = adminCasePageAnnouncementManageBiz.updateByTop(id, top, adminUserId);

        return NewResponseUtil.makeSuccess(res);
    }

    @PostMapping("list-bar")
    @RequiresPermission("case-page-announcement-manage:list-bar")
    public Response<Map<Integer, String>> listBar(@RequestParam(value = "type") String type) {

        Map<Integer, String> map = adminCasePageAnnouncementManageBiz.listBar(type);

        return NewResponseUtil.makeSuccess(map);
    }


}
