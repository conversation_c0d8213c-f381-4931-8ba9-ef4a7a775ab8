package com.shuidihuzhu.cf.admin.first;

import com.shuidihuzhu.common.web.util.ApplicationContextUtil;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/**
 * 这个类存在的意义是为了保证ApplicationContextUtil能在最早时间填入ApplicationContext
 * Created by <PERSON>houyou on 2018/1/2.
 */
@Component
public class ApplicationContextUtilHacker {


	public ApplicationContextUtilHacker(ApplicationContext applicationContext) {
		new ApplicationContextUtil().setApplicationContext(applicationContext);
	}
}
