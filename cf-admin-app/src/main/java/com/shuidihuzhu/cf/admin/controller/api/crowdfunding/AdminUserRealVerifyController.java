package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.client.feign.CfCommonFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 * @time 2019/11/7 下午2:45
 * @desc
 */
@Slf4j
@Controller
@RequestMapping(path = "/admin/user/verify")
public class AdminUserRealVerifyController {

    @Autowired
    private CfCommonFeignClient cfCommonFeignClient;

    @ResponseBody
    @RequestMapping(path = "/user-real-verify", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("user-verify:user-real-verify")
    public Response<String> userRealVerify(@ApiParam("姓名") @RequestParam(name = "name") String name,
                                       @ApiParam("身份证号") @RequestParam(name = "idcard") String idcard,
                                       @ApiParam("用户userId") @RequestParam(name = "userId", required = false) long userId){

        FeignResponse<String> response = cfCommonFeignClient.realNameVerify(name, idcard, UserRelTypeEnum.SELF.getValue(), userId);

        return ResponseUtil.makeResponse(response.getCode(), response.getMsg(), "");
    }


}
