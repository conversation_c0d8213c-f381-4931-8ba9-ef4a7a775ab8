package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.google.common.collect.ImmutableList;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: lixuan
 * @date: 2018/4/18 20:06
 */
@Slf4j
@RestController
@RequestMapping(path = "/admin/cf/attachment")
public class CfAttachmentController {

    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;

    @RequiresPermission("attachment:add-video")
    @PostMapping("/add-video")
    public Response addVideo(int infoId, String videoUrl, String coverUrl) {
        log.info("addVideo infoId:{};videoUrl:{};coverUrl:{};userId:{}", infoId, videoUrl, coverUrl, ContextUtil.getAdminUserId());
        if(infoId <= 0 || StringUtils.isBlank(videoUrl) || StringUtils.isBlank(coverUrl)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = this.crowdfundingDelegate.getFundingInfoById(infoId);
        if(crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }
        this.crowdfundingDelegate.deleteByParentIdAndType(infoId, ImmutableList.of(AttachmentTypeEnum.ATTACH_VIDEO,
                AttachmentTypeEnum.ATTACH_VIDEO_COVER_PLAN));
        CrowdfundingAttachment crowdfundingAttachmentVideo = new CrowdfundingAttachment(infoId, AttachmentTypeEnum.ATTACH_VIDEO, videoUrl);
        CrowdfundingAttachment crowdfundingAttachmentCover = new CrowdfundingAttachment(infoId, AttachmentTypeEnum.ATTACH_VIDEO_COVER_PLAN, coverUrl);
        this.crowdfundingDelegate.addCrowdfundingAttachmentList(ImmutableList.of(crowdfundingAttachmentVideo, crowdfundingAttachmentCover));
        return NewResponseUtil.makeSuccess(null);
    }

}
