package com.shuidihuzhu.cf.admin.mq;

import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminTwModifyService;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.UserOperationStatConstant;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminTwModifyDao;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.enums.admin.TwModifyChannelEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.model.admin.TwModifyRecordDO;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.CfCaseWorkOrderService;
import com.shuidihuzhu.cf.service.crowdfunding.CfSensitiveWordService;
import com.shuidihuzhu.client.cf.workorder.CfTwWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.TwWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * Created by sven on 18/8/2.
 *
 * <AUTHOR>
 *
 * 接受消息时候创建的是图文审核工单
 *
 */
@Service
@RocketMQListener(id = MQTagCons.CF_UPDATE_BASE_INFO_MSG,
        group = "cf-admin-"+ MQTagCons.CF_UPDATE_BASE_INFO_MSG + "-group",
        tags = MQTagCons.CF_UPDATE_BASE_INFO_MSG,
        topic = MQTopicCons.CF)
@Slf4j
public class CfUpdateConsumer implements MessageListener<CfOperatingRecord> {

    @Resource
    private CfSensitiveWordService cfSensitiveWordService;

    @Resource
    private CfCaseWorkOrderService cfCaseWorkOrderService;

    @Resource
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Resource
    private AdminCfInfoExtBiz adminCfInfoExtBiz;

    @Resource
    private MeterRegistry meterRegistry;

    @Resource
    private AdminTwModifyService adminTwModifyService;

    @Autowired
    private CommonOperationRecordClient commonOperationRecordClient;

    @Autowired
    private CfTwWorkOrderClient cfTwWorkOrderClient;

    @Resource
    private AdminTwModifyDao adminTwModifyDao;

    @Resource(name = "cf2RedissonHandler")
    private RedissonHandler redissonHandler;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfOperatingRecord> mqMessage) {

        if(mqMessage == null || mqMessage.getPayload() == null){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        //上报一次打点信息
        meterRegistry.counter(UserOperationStatConstant.USER_OPERATING_STAT,
                UserOperationStatConstant.OPERATION, UserOperationStatConstant.UPDATE_BASE_INFO).increment();

        log.info("receive CF_UPDATE_BASE_INFO_MSG message: {}",mqMessage.getPayload());

        try {

            String infoUuid = mqMessage.getPayload().getInfoUuid();
            CfInfoExt ext = adminCfInfoExtBiz.getByInfoUuid(infoUuid);
            FirstApproveStatusEnum statusEnum = Optional.ofNullable(ext)
                    .map(CfInfoExt::getFirstApproveStatus)
                    .map(FirstApproveStatusEnum::parse)
                    .orElse(null);

            if (!FirstApproveStatusEnum.isPassed(statusEnum)) {
                log.info("案例不是初审核通过的状态 infoUuid:{}, " +
                                "" +
                                "firstApproveStatus:{}",
                        mqMessage.getPayload().getInfoUuid(), statusEnum);
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            //创建敏感词的UGC任务
//            cfSensitiveWordService.addBaseInfoToWorkOrderOne(mqMessage.getPayload(), UGCAction.MODIFY);

            TwWorkOrder to = new TwWorkOrder();
            to.setCaseId(ext.getCaseId());
            to.setOrderType(WorkOrderType.content.getType());

            Response<Long> response = cfTwWorkOrderClient.createTw(to);
            log.info("createTw caseId={} response={}",ext.getCaseId(),response);

            // 如果是用户自主修改图文，存记录
            if (mqMessage.getPayload().getTwModifyChannel() == TwModifyChannelEnum.Channel.API_USER_MODIFY.getValue()
                    && Objects.nonNull(response) && response.ok()) {

                log.info("CfUpdateConsumer saveTwModifyRecord infoUuid:{} workOrderId:{}", infoUuid, response.getData());
                String key = "cfUpdateConsumer_" + infoUuid;
                String tryLock = null;
                try {
                    tryLock = redissonHandler.tryLock(key, 0, 1000L);
                    if (StringUtils.isNotBlank(tryLock)) {
                        TwModifyRecordDO twModifyRecordDO = adminTwModifyDao.selectByWorkOrderId(response.getData());
                        if (Objects.isNull(twModifyRecordDO)) {
                            adminTwModifyService.saveTwModifyRecord(infoUuid, response.getData(), TwModifyChannelEnum.Channel.API_USER_MODIFY.getValue());
                        }
                    }
                } catch (Exception e) {
                    log.error("加锁异常", e);
                } finally {
                    try {
                        if (StringUtils.isNotBlank(tryLock)) {
                            redissonHandler.unLock(key, tryLock);
                        }
                    } catch (Exception e) {
                        log.info("释放锁异常", e);
                    }
                }
            }

            CrowdfundingInfo fundingInfo = crowdfundingDelegate.getCrowdfundingInfoByInfoId(infoUuid);
            commonOperationRecordClient.create()
                    .buildBasicUser(fundingInfo.getId(), OperationActionTypeEnum.SUBMIT_BASE_APPROVE).save();

        } catch (Exception e){
            log.error("CfOperationConsumer", e);
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
