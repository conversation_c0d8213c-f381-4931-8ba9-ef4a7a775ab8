package com.shuidihuzhu.cf.admin.controller.api.crowdfunding.approve;

import com.shuidihuzhu.cf.client.material.model.CfRaiseFundUseModel;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoHospitalPayee;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoPayee;
import com.shuidihuzhu.cf.model.crowdfunding.approve.CfRaiseFundUseParam;
import com.shuidihuzhu.cf.model.crowdfunding.approve.CrowdfundingInfoHospitalPayeeParam;
import com.shuidihuzhu.cf.model.crowdfunding.approve.CrowdfundingInfoPayeeParam;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.api.model.enums.RelativesTypeEnum;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.models.auth.In;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import javax.management.ObjectName;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * @Author: wangpeng
 * @Date: 2021/2/24 20:51
 * @Description:
 */
public abstract class AbstractApproveController {

    @Resource
    protected ShuidiCipher shuidiCipher;
    @Resource
    protected OldShuidiCipher oldShuidiCipher;

    protected String handlerApproveComment(CrowdfundingAuthor crowdfundingAuthor, String name, String idCard, String idCardType) {
        StringBuilder stringBuilder = new StringBuilder();
        // name compare
        if (!StringUtils.equals(crowdfundingAuthor.getName(), name)) {
            stringBuilder.append("修改患者姓名：由”");
            stringBuilder.append(crowdfundingAuthor.getName());
            stringBuilder.append("“改为”");
            stringBuilder.append(name);
            stringBuilder.append("”;");
        }

        // idCardType compare
        UserIdentityType userIdentityType = UserIdentityType.valueOf(idCardType);
        if (crowdfundingAuthor.getIdType().ordinal() != userIdentityType.ordinal()) {
            stringBuilder.append("修改证件类型：由”");
            stringBuilder.append(crowdfundingAuthor.getIdType().getMsg());
            stringBuilder.append("“改为”");
            stringBuilder.append(userIdentityType.getMsg());
            stringBuilder.append("”;");
        }

        // idCard compare
        String decrypt = shuidiCipher.decrypt(crowdfundingAuthor.getCryptoIdCard());
        if (!StringUtils.equals(decrypt, idCard)) {
            stringBuilder.append("修改");
            stringBuilder.append(crowdfundingAuthor.getIdType().getMsg());
            stringBuilder.append("号：由”");
            stringBuilder.append(decrypt);
            stringBuilder.append("“改为”");
            stringBuilder.append(userIdentityType.getMsg());
            stringBuilder.append("号：”");
            stringBuilder.append(idCard);
            stringBuilder.append("”;");
        }

        return stringBuilder.toString();
    }

    protected void convertUpdateFundUseRespData(CfRaiseFundUseModel fundUseRespData, CfRaiseFundUseParam param) {
        if (Objects.nonNull(param.getHasCostAmount())) {
            fundUseRespData.setHasCostAmount(param.getHasCostAmount());
        }
        if (Objects.nonNull(param.getFutureExpenditureAmount())) {
            fundUseRespData.setFutureExpenditureAmount(param.getFutureExpenditureAmount());
        }
        if (Objects.nonNull(param.getUserForNursing())) {
            fundUseRespData.setUserForNursing(param.getUserForNursing());
        }
        if (Objects.nonNull(param.getNursingAmount())) {
            fundUseRespData.setNursingAmount(param.getNursingAmount());
        }
        if (Objects.nonNull(param.getGovRelief())) {
            fundUseRespData.setGovRelief(param.getGovRelief());
        }
        if (Objects.nonNull(param.getTotalGovReliefAmount())) {
            fundUseRespData.setTotalGovReliefAmount(param.getTotalGovReliefAmount());
        }

    }

    protected String handlerApproveUseUpdateComment(CfRaiseFundUseModel fundUseRespData, CfRaiseFundUseParam param) {
        StringBuilder stringBuilder = new StringBuilder();

        if (Objects.nonNull(param.getHasCostAmount()) && !Objects.equals(fundUseRespData.getHasCostAmount(), param.getHasCostAmount())) {
            stringBuilder.append("修改已花费金额：由”");
            stringBuilder.append(convertAmountInt(fundUseRespData.getHasCostAmount()));
            stringBuilder.append("万元“改为”");
            stringBuilder.append(convertAmountInt(param.getHasCostAmount()));
            stringBuilder.append("万元”;");
        }

        if (Objects.nonNull(param.getFutureExpenditureAmount()) && !Objects.equals(fundUseRespData.getFutureExpenditureAmount(), param.getFutureExpenditureAmount())) {
            stringBuilder.append("修改未来花费金额：由”");
            stringBuilder.append(convertAmountLong(fundUseRespData.getFutureExpenditureAmount()));
            stringBuilder.append("万元“改为”");
            stringBuilder.append(convertAmountLong(param.getFutureExpenditureAmount()));
            stringBuilder.append("万元”;");
        }

        if (Objects.nonNull(param.getUserForNursing()) && !Objects.equals(fundUseRespData.getUserForNursing(), param.getUserForNursing())) {
            stringBuilder.append("修改是否有康复护理费用：由”");
            stringBuilder.append(convertYesAndNo(fundUseRespData.getUserForNursing()));
            stringBuilder.append("“改为”");
            stringBuilder.append(convertYesAndNo(param.getUserForNursing()));
            stringBuilder.append("”;");
        }
        if (Objects.nonNull(param.getNursingAmount()) && !Objects.equals(fundUseRespData.getNursingAmount(), param.getNursingAmount())) {
            stringBuilder.append("修改康复护理费用：由”");
            stringBuilder.append(convertAmountInt(fundUseRespData.getNursingAmount()));
            stringBuilder.append("万元“改为”");
            stringBuilder.append(convertAmountInt(param.getNursingAmount()));
            stringBuilder.append("万元”;");
        }
        if (Objects.nonNull(param.getGovRelief()) && !Objects.equals(fundUseRespData.getGovRelief(), param.getGovRelief())) {
            stringBuilder.append("修改是否获得政府医疗救助：由”");
            stringBuilder.append(convertYesAndNo(fundUseRespData.getGovRelief()));
            stringBuilder.append("“改为”");
            stringBuilder.append(convertYesAndNo(param.getGovRelief()));
            stringBuilder.append("”;");
        }

        if (Objects.nonNull(param.getTotalGovReliefAmount()) && !Objects.equals(fundUseRespData.getTotalGovReliefAmount(), param.getTotalGovReliefAmount())) {
            stringBuilder.append("修改政府救助：由”");
            stringBuilder.append(convertAmountInt(fundUseRespData.getTotalGovReliefAmount()));
            stringBuilder.append("万元“改为”");
            stringBuilder.append(convertAmountInt(param.getTotalGovReliefAmount()));
            stringBuilder.append("万元”;");
        }

        return stringBuilder.toString();
    }

    private String convertYesAndNo(Integer param) {
        if(Objects.isNull(param)){
            return "";
        }

        switch (param){
            case 0:
                return "无";
            case 1:
                return "有";
            case 2:
                return "不确定";
            default:
                return "";
        }
    }

    private double convertAmountInt(Integer amount) {
        return Objects.nonNull(amount) ?
                BigDecimal.valueOf(amount).divide(BigDecimal.valueOf(10000), 2, RoundingMode.HALF_DOWN).doubleValue()
                : 0D;
    }

    private double convertAmountLong(Long amount) {
        return Objects.nonNull(amount) ?
                BigDecimal.valueOf(amount).divide(BigDecimal.valueOf(10000), 2, RoundingMode.HALF_DOWN).doubleValue()
                : 0D;
    }

    protected Response<Void> validateHospitalPayeeUpdateParam(CrowdfundingInfoHospitalPayeeParam payeeParam) {
        // 参数校验
        if (Objects.isNull(payeeParam)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (StringUtils.isBlank(payeeParam.getInfoUuid())
                || StringUtils.isBlank(payeeParam.getDepartment())
                || StringUtils.isBlank(payeeParam.getBedNum())
                || StringUtils.isBlank(payeeParam.getHospitalizationNum())
                || StringUtils.isBlank(payeeParam.getHospitalAccountName())
                || StringUtils.isBlank(payeeParam.getHospitalBankCard())
                || StringUtils.isBlank(payeeParam.getHospitalBankBranchName())) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR_IS_NULL);
        }

        if (payeeParam.getHospitalAccountName().length() > 30
                || payeeParam.getDepartment().length() > 30
                || payeeParam.getBedNum().length() > 30
                || payeeParam.getHospitalizationNum().length() > 32) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR_TOO_LONG);
        }
        String hospitalBankCard = StringUtils.trimToEmpty(payeeParam.getHospitalBankCard())
                .replaceAll("\\s+","")
                .replaceAll("–","-");
        payeeParam.setHospitalBankCard(hospitalBankCard);
        String encryptHospitalBankCard = oldShuidiCipher.aesEncrypt(payeeParam.getHospitalBankCard());
        if (encryptHospitalBankCard.length() > 90) {
            return NewResponseUtil.makeFail("银行账户超出合法长度，请再次核对");
        }
        payeeParam.setHospitalBankCard(encryptHospitalBankCard);
        return NewResponseUtil.makeSuccess(null);
    }

    protected String handlerHospitalPayeeUpdateComment(CrowdfundingInfo crowdfundingInfo, CrowdfundingInfoPayee crowdfundingInfoPayee, CrowdfundingInfoHospitalPayee crowdfundingInfoHospitalPayee, CrowdfundingInfoHospitalPayeeParam payeeParam) {
        StringBuilder stringBuilder = new StringBuilder();
        // fix npe
        crowdfundingInfoHospitalPayee = Objects.isNull(crowdfundingInfoHospitalPayee) ? new CrowdfundingInfoHospitalPayee() : crowdfundingInfoHospitalPayee;
        String bedNum = StringUtils.isEmpty(crowdfundingInfoHospitalPayee.getBedNum()) ? "" : crowdfundingInfoHospitalPayee.getBedNum();
        String department = StringUtils.isEmpty(crowdfundingInfoHospitalPayee.getDepartment()) ? "" : crowdfundingInfoHospitalPayee.getDepartment();
        String hospitalizationNum = StringUtils.isEmpty(crowdfundingInfoHospitalPayee.getHospitalizationNum()) ? "" : crowdfundingInfoHospitalPayee.getHospitalizationNum();
        String hospitalBankCard = StringUtils.isEmpty(crowdfundingInfoHospitalPayee.getHospitalBankCard()) ? "" : crowdfundingInfoHospitalPayee.getHospitalBankCard();
        String hospitalAccountName = StringUtils.isEmpty(crowdfundingInfoHospitalPayee.getHospitalAccountName()) ? "" : crowdfundingInfoHospitalPayee.getHospitalAccountName();
        String hospitalBankBranchName = StringUtils.isEmpty(crowdfundingInfoHospitalPayee.getHospitalBankBranchName()) ? "" : crowdfundingInfoHospitalPayee.getHospitalBankBranchName();

        CrowdfundingRelationType relationType = crowdfundingInfo.getRelationType();
        if (Objects.nonNull(relationType) && !Objects.equals(relationType, CrowdfundingRelationType.LOCATION_HOSPITAL_ACCOUNT)) {
            int relativesType = Objects.nonNull(crowdfundingInfoPayee) ? crowdfundingInfoPayee.getRelativesType() : 0;
            RelativesTypeEnum relativesTypeEnum = RelativesTypeEnum.getByCode(relativesType);
            String relativesTypeEnumMsg = Objects.nonNull(relativesTypeEnum) ? relativesTypeEnum.getMsg() : "";
            String relationDesc = Objects.equals(relationType, CrowdfundingRelationType.other) ? "配偶或近亲属" + "-" + relativesTypeEnumMsg: relationType.getDescription();
            stringBuilder.append("修改收款方式：由”");
            stringBuilder.append(relationDesc);
            stringBuilder.append("“改为”");
            stringBuilder.append("医院收款");
            stringBuilder.append("”;");
        }


        if (!StringUtils.equals(bedNum, payeeParam.getBedNum())) {
            stringBuilder.append("修改床号：由”");
            stringBuilder.append(bedNum);
            stringBuilder.append("“改为”");
            stringBuilder.append(payeeParam.getBedNum());
            stringBuilder.append("”;");
        }

        if (!StringUtils.equals(department, payeeParam.getDepartment())) {
            stringBuilder.append("修改科室：由”");
            stringBuilder.append(department);
            stringBuilder.append("“改为”");
            stringBuilder.append(payeeParam.getDepartment());
            stringBuilder.append("”;");
        }

        if (!StringUtils.equals(hospitalizationNum, payeeParam.getHospitalizationNum())) {
            stringBuilder.append("修改住院号：由”");
            stringBuilder.append(hospitalizationNum);
            stringBuilder.append("“改为”");
            stringBuilder.append(payeeParam.getHospitalizationNum());
            stringBuilder.append("”;");
        }
        String decryptBankCard = shuidiCipher.decrypt(payeeParam.getHospitalBankCard());
        if (!StringUtils.equals(hospitalBankCard, decryptBankCard)) {
            stringBuilder.append("修改医院对公银行卡号：由”");
            stringBuilder.append(hospitalBankCard);
            stringBuilder.append("“改为”");
            stringBuilder.append(decryptBankCard);
            stringBuilder.append("”;");
        }
        if (!StringUtils.equals(hospitalAccountName, payeeParam.getHospitalAccountName())) {
            stringBuilder.append("修改医院对公账户名称：由”");
            stringBuilder.append(hospitalAccountName);
            stringBuilder.append("“改为”");
            stringBuilder.append(payeeParam.getHospitalAccountName());
            stringBuilder.append("”;");
        }

        if (!StringUtils.equals(hospitalBankBranchName, payeeParam.getHospitalBankBranchName())) {
            stringBuilder.append("修改医院开户支行名称：由”");
            stringBuilder.append(hospitalBankBranchName);
            stringBuilder.append("“改为”");
            stringBuilder.append(payeeParam.getHospitalBankBranchName());
            stringBuilder.append("”;");
        }
        return stringBuilder.toString();
    }

    protected String handlerPayeeUpdateComment(CrowdfundingInfoPayee crowdfundingInfoPayee, CrowdfundingInfoPayeeParam payeeParam, CrowdfundingInfo crowdfundingInfo) {

        crowdfundingInfoPayee = Objects.isNull(crowdfundingInfoPayee) ? new CrowdfundingInfoPayee() : crowdfundingInfoPayee;

        String relationType = Objects.isNull(crowdfundingInfoPayee.getRelationType()) ? "" : CrowdfundingRelationType.getByCode(crowdfundingInfoPayee.getRelationType()).getDescription();
        String idType = Objects.isNull(crowdfundingInfoPayee.getIdType()) ? "" : UserIdentityType.getByCode(crowdfundingInfoPayee.getIdType()).getMsg();
        String payeeName = StringUtils.isEmpty(crowdfundingInfoPayee.getName()) ? "" : crowdfundingInfoPayee.getName();
        String idCard = StringUtils.isEmpty(crowdfundingInfoPayee.getIdCard()) ? "" : shuidiCipher.decrypt(crowdfundingInfoPayee.getIdCard());
        String bankName = StringUtils.isEmpty(crowdfundingInfoPayee.getBankName()) ? "" : crowdfundingInfoPayee.getBankName();
        String bankCard = StringUtils.isEmpty(crowdfundingInfoPayee.getBankCard()) ? "" : shuidiCipher.decrypt(crowdfundingInfoPayee.getBankCard());
        String relativesType = Objects.equals(crowdfundingInfoPayee.getRelativesType(), 0) ? "" : RelativesTypeEnum.getByCode(crowdfundingInfoPayee.getRelativesType()).getMsg();

        StringBuilder stringBuilder = new StringBuilder();

        if (Objects.equals(crowdfundingInfo.getRelationType(), CrowdfundingRelationType.LOCATION_HOSPITAL_ACCOUNT)) {
            stringBuilder.append("修改收款方式：由”");
            stringBuilder.append("医院对公收款");
            stringBuilder.append("“改为”");
            stringBuilder.append("个人收款");
            stringBuilder.append("”;");
        }

        if (!StringUtils.equals(relationType, payeeParam.getRelationType().getDescription())) {
            stringBuilder.append("修改关系：由”");
            stringBuilder.append(relationType);
            stringBuilder.append("“改为”");
            stringBuilder.append(payeeParam.getRelationType().getDescription());
            stringBuilder.append("”;");
        }

        if (!StringUtils.equals(idType, payeeParam.getIdType().getMsg())) {
            stringBuilder.append("修改证件类型：由”");
            stringBuilder.append(idType);
            stringBuilder.append("“改为”");
            stringBuilder.append(payeeParam.getIdType().getMsg());
            stringBuilder.append("”;");
        }

        if (!StringUtils.equals(payeeName, payeeParam.getPayeeName())) {
            stringBuilder.append("修改收款人姓名：由”");
            stringBuilder.append(payeeName);
            stringBuilder.append("“改为”");
            stringBuilder.append(payeeParam.getPayeeName());
            stringBuilder.append("”;");
        }
        if (!StringUtils.equals(idCard, payeeParam.getIdCard())) {
            stringBuilder.append("修改身份证：由”");
            stringBuilder.append(idCard);
            stringBuilder.append("“改为”");
            stringBuilder.append(payeeParam.getIdCard());
            stringBuilder.append("”;");
        }
        if (!StringUtils.equals(bankName, payeeParam.getBankName())) {
            stringBuilder.append("修改银行名称：由”");
            stringBuilder.append(bankName);
            stringBuilder.append("“改为”");
            stringBuilder.append(payeeParam.getBankName());
            stringBuilder.append("”;");
        }
        if (!StringUtils.equals(bankCard, payeeParam.getBankCard())) {
            stringBuilder.append("修改银行卡号：由”");
            stringBuilder.append(bankCard);
            stringBuilder.append("“改为”");
            stringBuilder.append(payeeParam.getBankCard());
            stringBuilder.append("”;");
        }
        RelativesTypeEnum typeEnum = RelativesTypeEnum.getByCode(payeeParam.getRelativesType());
        if (Objects.nonNull(typeEnum) && !StringUtils.equals(relativesType, typeEnum.getMsg())) {
            stringBuilder.append("修改近亲属关系：由”");
            stringBuilder.append(relativesType);
            stringBuilder.append("“改为”");
            stringBuilder.append(typeEnum.getMsg());
            stringBuilder.append("”;");
        }

        return stringBuilder.toString();
    }

}
