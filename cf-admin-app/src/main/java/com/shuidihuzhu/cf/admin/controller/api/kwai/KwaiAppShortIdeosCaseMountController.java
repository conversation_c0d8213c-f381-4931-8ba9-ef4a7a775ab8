package com.shuidihuzhu.cf.admin.controller.api.kwai;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.admin.util.ReadExcelUtil;
import com.shuidihuzhu.cf.kwai.KwaiAppShortIdeosCaseMountBiz;
import com.shuidihuzhu.cf.model.kwai.KwaiAppShortIdeosCaseMountDto;
import com.shuidihuzhu.cf.model.kwai.PageInfo;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.admin.PageUtil;
import com.shuidihuzhu.pf.common.v2.model.pagehelper.PaginationListVO;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("admin/cf/kwai-app-short-ideos-case-mount")
@Slf4j
public class KwaiAppShortIdeosCaseMountController {

    @Resource
    private KwaiAppShortIdeosCaseMountBiz kwaiAppShortIdeosCaseMountBiz;

    @PostMapping("save")
    @RequiresPermission("kwai-app-short-ideos-case-mount:save")
    public Response<Integer> save(@RequestParam("infoUuid") String infoUuid,
                                  @RequestParam("mobile") String mobile) {


        long adminLongUserId = ContextUtil.getAdminLongUserId();

        KwaiAppShortIdeosCaseMountDto dto = KwaiAppShortIdeosCaseMountDto.builder()
                .infoUuid(infoUuid)
                .mobile(mobile)
                .operatorId(adminLongUserId)
                .designation("")
                .build();

        int res;
        try {
            res = kwaiAppShortIdeosCaseMountBiz.save(dto);
        } catch (Exception e) {
            return NewResponseUtil.makeFail(e.getMessage());
        }

        return NewResponseUtil.makeSuccess(res);
    }

    @PostMapping("save-batch")
    @RequiresPermission("kwai-app-short-ideos-case-mount:save-batch")
    public Response<Integer> saveBatch(@RequestParam(value = "file") MultipartFile file) {
        long adminLongUserId = ContextUtil.getAdminLongUserId();

        int res;
        try {
            List<KwaiAppShortIdeosCaseMountDto> dtoList = parseExcel(file);
            res = kwaiAppShortIdeosCaseMountBiz.saveBatch(dtoList,adminLongUserId);
        } catch (Exception e) {
            return NewResponseUtil.makeFail(e.getMessage());
        }

        return NewResponseUtil.makeSuccess(res);
    }


    @PostMapping("delete")
    @RequiresPermission("kwai-app-short-ideos-case-mount:delete")
    public Response<Integer> delete(@RequestParam("id") long id) {

        KwaiAppShortIdeosCaseMountDto dto = KwaiAppShortIdeosCaseMountDto.builder()
                .id(id)
                .build();

        int res;
        try {
            res = kwaiAppShortIdeosCaseMountBiz.remove(dto);
        } catch (Exception e) {
            return NewResponseUtil.makeFail(e.getMessage());
        }

        return NewResponseUtil.makeSuccess(res);
    }

    @PostMapping("delete-batch")
    @RequiresPermission("kwai-app-short-ideos-case-mount:delete-batch")
    public Response<Integer> deleteBatch(@RequestParam("ids") String ids) {

        KwaiAppShortIdeosCaseMountDto dto = KwaiAppShortIdeosCaseMountDto.builder()
                .ids(ids)
                .build();

        int res;
        try {
            res = kwaiAppShortIdeosCaseMountBiz.removeBatch(dto);
        } catch (Exception e) {
            return NewResponseUtil.makeFail(e.getMessage());
        }

        return NewResponseUtil.makeSuccess(res);
    }

    @PostMapping("get-case-list")
    @RequiresPermission("kwai-app-short-ideos-case-mount:get-case-list")
    public Response<Map<String, Object>> getCaseList(@RequestParam(value = "infoUuid", required = false, defaultValue = "") String infoUuid,
                                                     @RequestParam(value = "mobile", required = false, defaultValue = "") String mobile,
                                                     @RequestParam(value = "pageSize", required = false, defaultValue = "10") int pageSize,
                                                     @RequestParam(value = "current", required = false, defaultValue = "1") int current) {

        KwaiAppShortIdeosCaseMountDto dto = KwaiAppShortIdeosCaseMountDto.builder()
                .infoUuid(infoUuid)
                .mobile(mobile)
                .current(current)
                .pageSize(pageSize)
                .build();

        Map<String, Object> result = Maps.newHashMap();
        try {
            List<KwaiAppShortIdeosCaseMountDto> list = kwaiAppShortIdeosCaseMountBiz.getList(dto);
            result.put("list", list);
            result.put("pagination", list.stream().map(KwaiAppShortIdeosCaseMountDto::getPageMap).findFirst().orElse(null));
        } catch (Exception e) {
            return NewResponseUtil.makeFail(e.getMessage());
        }

        return NewResponseUtil.makeSuccess(result);
    }


    private List<KwaiAppShortIdeosCaseMountDto> parseExcel(MultipartFile file) throws Exception {

        Map<String, String> map = Maps.newHashMapWithExpectedSize(3);

        map.put("手机号", "mobile");
        map.put("案例id", "infoUuid");
        map.put("快手名称", "designation");

        return (List<KwaiAppShortIdeosCaseMountDto>) ReadExcelUtil.parseExcel(file.getInputStream(),
                file.getOriginalFilename(), map, 1, KwaiAppShortIdeosCaseMountDto.class);

    }


}
