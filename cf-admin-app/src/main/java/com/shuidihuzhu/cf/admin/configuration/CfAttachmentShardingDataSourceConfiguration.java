package com.shuidihuzhu.cf.admin.configuration;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.constants.admin.AdminDS;
import io.shardingjdbc.core.api.ShardingDataSourceFactory;
import io.shardingjdbc.core.api.config.MasterSlaveRuleConfiguration;
import io.shardingjdbc.core.api.config.ShardingRuleConfiguration;
import io.shardingjdbc.core.api.config.TableRuleConfiguration;
import io.shardingjdbc.core.api.config.strategy.InlineShardingStrategyConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.*;

import com.shuidihuzhu.common.datasource.DS;

@Configuration
public class CfAttachmentShardingDataSourceConfiguration {

    @Resource(name = DS.CF)
    private DataSource crowdfundingDataSourceMaster;

    @Resource(name = DS.CF_SLAVE)
    private DataSource crowdfundingDataSourceSlave1;

    @Resource(name = DS.CF_SLAVE_2)
    private DataSource crowdfundingDataSourceSlave2;

    @Bean(AdminDS.CF_ATTACHMENT_SHARDING)
    public DataSource cfAttachmentShardingMaster() throws SQLException {
        return buildDataSource();
    }

    public DataSource buildDataSource() throws SQLException {
        Map<String, DataSource> dataSourceMap = new HashMap<>();
        dataSourceMap.put("cfAttachmentShardingMaster", crowdfundingDataSourceMaster);
        dataSourceMap.put("cfAttachmentShardingSlave1", crowdfundingDataSourceSlave1);
        dataSourceMap.put("cfAttachmentShardingSlave2", crowdfundingDataSourceSlave2);

        // 配置主从读写分离规则
        MasterSlaveRuleConfiguration masterSlaveRuleConfig = new MasterSlaveRuleConfiguration();
        masterSlaveRuleConfig.setName("cfAttachmentMasterSlaveRule");
        masterSlaveRuleConfig.setMasterDataSourceName("cfAttachmentShardingMaster");
        masterSlaveRuleConfig.setSlaveDataSourceNames(Lists.newArrayList("cfAttachmentShardingSlave1", "cfAttachmentShardingSlave2"));

        // 配置分表规则
        TableRuleConfiguration cfAttachmentCaseIdSharding = new TableRuleConfiguration();
        cfAttachmentCaseIdSharding.setLogicTable("cf_attachment_case_id_sharding");
        cfAttachmentCaseIdSharding.setActualDataNodes(
                "cfAttachmentMasterSlaveRule.cf_attachment_case_id_sharding_0${0..9}${0..9}"
        );
        cfAttachmentCaseIdSharding.setTableShardingStrategyConfig(
                new InlineShardingStrategyConfiguration("parent_id", "cf_attachment_case_id_sharding_${String.format(\"%03d\", Math.abs(parent_id) % 100)}")
        );

        // 配置 Sharding 规则
        ShardingRuleConfiguration shardingRuleConfig = new ShardingRuleConfiguration();
        shardingRuleConfig.getTableRuleConfigs().add(cfAttachmentCaseIdSharding);
        shardingRuleConfig.setMasterSlaveRuleConfigs(Collections.singletonList(masterSlaveRuleConfig));

        // 额外属性
        Properties prop = new Properties();
        prop.setProperty("sql.show", "true"); // 打印 SQL 日志
        prop.setProperty("executor.size", "40"); // 线程池大小

        // 创建 Sharding 数据源
        return ShardingDataSourceFactory.createDataSource(dataSourceMap, shardingRuleConfig, Collections.emptyMap(), prop);
    }
}

