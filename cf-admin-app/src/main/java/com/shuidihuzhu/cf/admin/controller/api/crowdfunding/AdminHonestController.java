package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.client.cf.api.client.IDishonestForAiFeignClient;
import com.shuidihuzhu.client.cf.api.model.CfDishonestInfo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @time 2019/6/28 下午2:38
 * @desc
 */
@Slf4j
@RestController
@RequestMapping(path = "/admin/cf/dishonest")
public class AdminHonestController{

    @Autowired
    private IDishonestForAiFeignClient dishonestForAiFeignClient;

    @ApiOperation(value = "查询案例发起人、收款人、患者的诚信信息")
    @RequestMapping(path = "/query-dishonest-info", method = RequestMethod.POST)
    @RequiresPermission("cf-honest:query-dishonest-info")
    public Response<CfDishonestInfo> queryDishonestInfoByCaseId(@RequestParam(name = "caseId") int caseId) {
        int userId = ContextUtil.getAdminUserId();
        if(userId <= 0){
            return NewResponseUtil.makeError(CfErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        if(caseId <= 0){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        Response<CfDishonestInfo> response = dishonestForAiFeignClient.queryDishonestInfoByCaseId(caseId);

        log.info("query-dishonest-info caseId:{},result:{}", caseId, JSON.toJSONString(response));

        return response;
    }
}
