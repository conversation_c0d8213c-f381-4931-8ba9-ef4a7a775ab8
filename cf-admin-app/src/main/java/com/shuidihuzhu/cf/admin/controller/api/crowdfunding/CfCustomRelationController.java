package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.service.crowdfunding.CfCustomRelationService;
import com.shuidihuzhu.cf.vo.crowdfunding.CfCustomRelationCaseVo;
import com.shuidihuzhu.cf.vo.crowdfunding.CfCustomRelationHistoryVo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Created by dongcf on 2020/7/23
 */
@RestController
@RequestMapping("admin/cf/custom-relation")
public class CfCustomRelationController {

    @Autowired
    private CfCustomRelationService cfCustomRelationService;

    @ApiOperation("获取案例信息")
    @PostMapping("/get-case-info")
    @RequiresPermission("customRelation:get-case-info")
    public Response<CfCustomRelationCaseVo> getCaseInfo(@ApiParam("案例ID") @RequestParam("caseId") Integer caseId) {
        return cfCustomRelationService.getCaseInfo(caseId);
    }

    @ApiOperation("添加")
    @PostMapping("/add")
    @RequiresPermission("customRelation:add")
    public Response<Void> add(@ApiParam("案例ID") @RequestParam("caseId") Integer caseId,
                              @ApiParam("显示类型") @RequestParam("showType") Integer showType,
                              @ApiParam("关系类型") @RequestParam(value = "relationType", required = false, defaultValue = "1") Integer relationType,
                              @ApiParam("修改原因") @RequestParam("remark") String remark) {
        int userId = ContextUtil.getAdminUserId();
        Response<Void> response = cfCustomRelationService.add(caseId, userId, showType, relationType, remark);
        return response;
    }

    @ApiOperation("历史记录")
    @PostMapping("/find-history")
    @RequiresPermission("customRelation:find-history")
    public Response<List<CfCustomRelationHistoryVo>> findHistory(@ApiParam("案例ID") @RequestParam("caseId") Integer caseId) {
        return NewResponseUtil.makeSuccess(cfCustomRelationService.find(caseId));
    }
}
