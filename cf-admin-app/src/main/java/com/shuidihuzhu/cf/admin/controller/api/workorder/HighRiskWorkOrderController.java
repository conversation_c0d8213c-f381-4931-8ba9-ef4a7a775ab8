package com.shuidihuzhu.cf.admin.controller.api.workorder;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.admin.river.impl.RiverDiBaoFacadeImpl;
import com.shuidihuzhu.cf.biz.crowdfunding.CfMaterialVerityHistoryBiz;
import com.shuidihuzhu.cf.client.adminpure.enums.WorkOrderExtContentTypeEnum;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.admin.workorder.CfCallOutRecord;
import com.shuidihuzhu.cf.risk.client.highrisk.HighRiskClient;
import com.shuidihuzhu.cf.service.callout.CfCallOutService;
import com.shuidihuzhu.cf.service.workorder.WorkOrderExtService;
import com.shuidihuzhu.cf.vo.admin.ModuleAuditTimeVo;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditCaseDetail;
import com.shuidihuzhu.cf.vo.approve.InitialAuditAdditionInfoVO;
import com.shuidihuzhu.cf.vo.approve.RiverDetailVO;
import com.shuidihuzhu.cf.vo.approve.RiverHandleParamVO;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.msg.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @DATE 2018/12/29
 */
@RestController
@RequestMapping(path = "/admin/workorder/highrisk")
@Slf4j
public class HighRiskWorkOrderController {


    @Resource
    private CfWorkOrderClient cfWorkOrderClient;

    @Resource
    private CfMaterialVerityHistoryBiz verityHistoryBiz;

    @Resource
    private CfCallOutService cfCallOutService;

    @Resource
    private RiverDiBaoFacadeImpl riverDiBaoFacade;

    @Autowired
    private WorkOrderExtService workOrderSnapshotService;

    @Resource
    private HighRiskClient highRiskClient;


    @PostMapping("get-operate-time")
    @RequiresPermission("neishen:get-operate-time")
    public Response<ModuleAuditTimeVo> getOperateTime(@RequestParam("workOrderId") long workOrderId) {
        if (workOrderId <= 0L) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        Response<WorkOrderVO> response = cfWorkOrderClient.getWorkOrderById(workOrderId);
        WorkOrderVO workOrderVO = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(null);
        if (Objects.isNull(workOrderVO)) {
            return NewResponseUtil.makeError(AdminErrorCode.WORK_ORDER_NOT_FOUND);
        }

        ModuleAuditTimeVo moduleAuditTimeVo = new ModuleAuditTimeVo();
        InitialAuditCaseDetail.CaseBaseInfo baseInfoSnapshot = getBaseInfoSnapshot(workOrderVO.getWorkOrderId());
        InitialAuditCaseDetail.FirstApproveCaseInfo firstApproveSnapshot = getFirstApproveCaseInfoSnapshot(workOrderVO.getWorkOrderId());
        InitialAuditCaseDetail.CreditInfo creditInfoSnapshot = getCreditInfoSnapshot(workOrderVO.getWorkOrderId());
        RiverDetailVO<InitialAuditAdditionInfoVO> diBaoDetailSnapshot = riverDiBaoFacade.getDetail(workOrderVO.getCaseId(), workOrderVO.getWorkOrderId());

        if (Objects.nonNull(baseInfoSnapshot) && baseInfoSnapshot.getPass() == 0) {
            moduleAuditTimeVo.setImgWordTime(DateUtil.getDate2LStr(workOrderVO.getFinishTime()));
            moduleAuditTimeVo.setImgWordTimeOperateOrgName(verityHistoryBiz.queryOperateDetail(Math.toIntExact(workOrderVO.getOperatorId())));
        }

        if (Objects.nonNull(firstApproveSnapshot) && firstApproveSnapshot.getPass() == 0) {
            moduleAuditTimeVo.setPreInfoTime(DateUtil.getDate2LStr(workOrderVO.getFinishTime()));
            moduleAuditTimeVo.setPreInfoOperateOrgName(verityHistoryBiz.queryOperateDetail(Math.toIntExact(workOrderVO.getOperatorId())));
        }

        if (Objects.nonNull(creditInfoSnapshot) && creditInfoSnapshot.getPass() == 0) {
            moduleAuditTimeVo.setAddCreditTime(DateUtil.getDate2LStr(workOrderVO.getFinishTime()));
            moduleAuditTimeVo.setAddCreditOperateOrgName(verityHistoryBiz.queryOperateDetail(Math.toIntExact(workOrderVO.getOperatorId())));
        }

        if (Objects.nonNull(diBaoDetailSnapshot) && Objects.nonNull(diBaoDetailSnapshot.getHandleParam())) {
            RiverHandleParamVO riverHandleParamVO = diBaoDetailSnapshot.getHandleParam();
            if (riverHandleParamVO.getHandleType().getValue() == RiverHandleParamVO.HandleType.REJECT.getValue()) {
                moduleAuditTimeVo.setLowIncomeTime(DateUtil.getDate2LStr(workOrderVO.getFinishTime()));
                moduleAuditTimeVo.setLowIncomeOperateOrgName(verityHistoryBiz.queryOperateDetail(Math.toIntExact(workOrderVO.getOperatorId())));
            }
        }

        Response<List<WorkOrderVO>> listResponse = cfWorkOrderClient.listByCaseIdAndTypeAndResult(workOrderVO.getCaseId()
                , Lists.newArrayList(WorkOrderType.highriskshenhe.getType())
                , Lists.newArrayList(HandleResultEnum.audit_pass.getType(), HandleResultEnum.audit_reject.getType()));
        List<WorkOrderVO> workOrderVOList = Optional.ofNullable(listResponse).filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList());
        log.info("workOrderVOList工单列表 workOrderVOList:{}", JSON.toJSONString(workOrderVOList));
        if (CollectionUtils.isEmpty(workOrderVOList)) {
            return NewResponseUtil.makeSuccess(moduleAuditTimeVo);
        }

        for (WorkOrderVO orderVO : workOrderVOList) {
            long oldWorkOrderId = orderVO.getWorkOrderId();
            int caseId = orderVO.getCaseId();
            InitialAuditCaseDetail.CaseBaseInfo caseBaseInfo = getBaseInfoSnapshot(oldWorkOrderId);
            InitialAuditCaseDetail.FirstApproveCaseInfo firstApproveCaseInfo = getFirstApproveCaseInfoSnapshot(oldWorkOrderId);
            InitialAuditCaseDetail.CreditInfo creditInfo = getCreditInfoSnapshot(oldWorkOrderId);

            if (Objects.nonNull(caseBaseInfo) && caseBaseInfo.getPass() == 1) {
                if (StringUtils.isAnyEmpty(moduleAuditTimeVo.getImgWordTime(), moduleAuditTimeVo.getImgWordTimeOperateOrgName())) {
                    moduleAuditTimeVo.setImgWordTime(DateUtil.getDate2LStr(orderVO.getFinishTime()));
                    moduleAuditTimeVo.setImgWordTimeOperateOrgName(verityHistoryBiz.queryOperateDetail(Math.toIntExact(orderVO.getOperatorId())));
                }
            }

            if (Objects.nonNull(firstApproveCaseInfo) && firstApproveCaseInfo.getPass() == 1) {
                if (StringUtils.isAnyEmpty(moduleAuditTimeVo.getPreInfoTime(), moduleAuditTimeVo.getPreInfoOperateOrgName())) {
                    moduleAuditTimeVo.setPreInfoTime(DateUtil.getDate2LStr(orderVO.getFinishTime()));
                    moduleAuditTimeVo.setPreInfoOperateOrgName(verityHistoryBiz.queryOperateDetail(Math.toIntExact(orderVO.getOperatorId())));
                }
            }

            if (Objects.nonNull(creditInfo) && creditInfo.getPass() == 1) {
                if (StringUtils.isAnyEmpty(moduleAuditTimeVo.getAddCreditTime(), moduleAuditTimeVo.getAddCreditOperateOrgName())) {
                    moduleAuditTimeVo.setAddCreditTime(DateUtil.getDate2LStr(orderVO.getFinishTime()));
                    moduleAuditTimeVo.setAddCreditOperateOrgName(verityHistoryBiz.queryOperateDetail(Math.toIntExact(orderVO.getOperatorId())));
                }
            }

            RiverDetailVO<InitialAuditAdditionInfoVO> diBaoDetail = riverDiBaoFacade.getDetail(caseId, oldWorkOrderId);
            if (Objects.nonNull(diBaoDetail) && Objects.nonNull(diBaoDetail.getHandleParam())) {
                RiverHandleParamVO riverHandleParamVO = diBaoDetail.getHandleParam();
                if (riverHandleParamVO.getHandleType().getValue() == RiverHandleParamVO.HandleType.PASS.getValue()) {
                    if (StringUtils.isAnyEmpty(moduleAuditTimeVo.getLowIncomeTime(), moduleAuditTimeVo.getLowIncomeOperateOrgName())) {
                        moduleAuditTimeVo.setLowIncomeTime(DateUtil.getDate2LStr(orderVO.getFinishTime()));
                        moduleAuditTimeVo.setLowIncomeOperateOrgName(verityHistoryBiz.queryOperateDetail(Math.toIntExact(orderVO.getOperatorId())));
                    }
                }
            }
        }

        return NewResponseUtil.makeSuccess(moduleAuditTimeVo);
    }

    @PostMapping("get-manual-rule-code-all")
    @RequiresPermission("neishen:get-manual-rule-code-all")
    public Response<List<String>> getManualRuleCodeAll() {
        Response<List<String>> response = highRiskClient.getHighRiskManualEnum();
        List<String> list = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList());
        return NewResponseUtil.makeSuccess(list);
    }

    @PostMapping("select-call-record")
    @RequiresPermission("neishen:select-call-record")
    public Response<CfCallOutRecord> selectCallRecords(@RequestParam(value = "workOrderId") long workOrderId
            , @RequestParam(value = "caseId") int caseId
            , @RequestParam(value = "operatorId") long operatorId
            , @RequestParam(value = "qcWorkOrderId", required = false, defaultValue = "0") long qcWorkOrderId) {
        CfCallOutRecord cfCallOutRecord = cfCallOutService.selectCallRecords(workOrderId, caseId, operatorId, qcWorkOrderId);
        Optional.ofNullable(cfCallOutRecord)
                .filter(r -> CollectionUtils.isNotEmpty(r.getCallOutDetails()))
                .ifPresent(r -> r.getCallOutDetails().forEach(item -> item.setMobile(StringUtils.EMPTY)));
        return NewResponseUtil.makeSuccess(cfCallOutRecord);
    }

    private InitialAuditCaseDetail.CaseBaseInfo getBaseInfoSnapshot(long workOrderId) {
        return workOrderSnapshotService.getByClazz(workOrderId,
                WorkOrderExtContentTypeEnum.INITIAL_AUDIT_CASE_BASE_INFO, InitialAuditCaseDetail.CaseBaseInfo.class);
    }

    private InitialAuditCaseDetail.FirstApproveCaseInfo getFirstApproveCaseInfoSnapshot(long workOrderId) {
        return workOrderSnapshotService.getByClazz(workOrderId,
                WorkOrderExtContentTypeEnum.INITIAL_AUDIT_FIRST_APPROVE, InitialAuditCaseDetail.FirstApproveCaseInfo.class);
    }

    private InitialAuditCaseDetail.CreditInfo getCreditInfoSnapshot(long workOrderId) {
        return workOrderSnapshotService.getByClazz(workOrderId,
                WorkOrderExtContentTypeEnum.INITIAL_AUDIT_CREDIT_INFO, InitialAuditCaseDetail.CreditInfo.class);
    }

}
