package com.shuidihuzhu.cf.admin.controller.api.mina;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.admin.util.wordfilter2.SensitivewordFilter;
import com.shuidihuzhu.cf.biz.mina.AdminCfCommentPriseBiz;
import com.shuidihuzhu.cf.biz.mina.AdminCfTopicCommentBiz;
import com.shuidihuzhu.cf.biz.mina.AdminCommentDynamicBiz;
import com.shuidihuzhu.cf.constants.admin.PageCons;
import com.shuidihuzhu.cf.delegate.other.IMiniAppDelegate;
import com.shuidihuzhu.cf.delegate.ugc.IUgcDelegate;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.miniprogram.CfCommentDynamic;
import com.shuidihuzhu.cf.model.miniprogram.CfTopic;
import com.shuidihuzhu.cf.model.miniprogram.CfTopicComment;
import com.shuidihuzhu.cf.service.crowdfunding.CfMinaSensitiveWordService;
import com.shuidihuzhu.cf.vo.mina.CfIdCountVo;
import com.shuidihuzhu.cf.vo.mina.CfTopicCommentVo;

import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.admin.PageUtil;
import com.shuidihuzhu.frame.client.grpc.XcxTopicCommenGrpcClient;
import com.shuidihuzhu.frame.client.model.enums.CommentTypeEnum;
import com.shuidihuzhu.frame.client.model.enums.ResultEnum;
import com.shuidihuzhu.frame.client.service.mina.topiccomment.RemoveCommentRequest;
import com.shuidihuzhu.frame.client.service.mina.topiccomment.RemoveCommentResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Ahrievil
 */
@Slf4j
@RestController
@RequestMapping(path = "admin/cf/mina/topic/comment")
public class CfMinaTopicCommentController {

    @Autowired
    private IMiniAppDelegate miniAppDelegate;
    @Autowired
    private AdminCfTopicCommentBiz adminCfTopicCommentBiz;
    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;
    @Autowired
    private AdminCfCommentPriseBiz adminCfCommentPriseBiz;
    @Autowired
    private IUgcDelegate ugcDelegate;
    @Autowired
    private AdminCommentDynamicBiz adminCommentDynamicBiz;
    @Autowired
    private CfMinaSensitiveWordService cfMinaSensitiveWordService;
    @Autowired
    private XcxTopicCommenGrpcClient xcxTopicCommenGrpcClient;

    @RequiresPermission("topic-comment:get-list")
    @RequestMapping(path = "get-list", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    public Response getList(@RequestParam(value = "current", defaultValue = "" + PageCons.MIN_PAGE_NO) int current,
                            @RequestParam(value = "pageSize", defaultValue = "" + PageCons.DEFAULT_PAGE_SIZE) int pageSize,
                            @RequestParam(value = "topicId", required = false) Integer topicId,
                            @RequestParam(value = "comment", required = false) String comment,
                            @RequestParam(value = "commentId", required = false) Long commentId,
                            @RequestParam(value = "commentUserId", required = false) Integer commentUserId,
                            @RequestParam(value = "isSensitiveWord", required = false) Integer isSensitiveWord,
                            @RequestParam(value = "beginTime", required = false) Timestamp beginTime,
                            @RequestParam(value = "endTime", required = false) Timestamp endTime,
                            @RequestParam(value = "orderType", defaultValue = "0") Integer orderType) {
        log.info("CfMinaTopicCommentController getList current:{}, pageSize:{}, commentId:{}, topicId:{}, comment:{}, " +
                        "commentUserId:{}, isSensitiveWord:{}, beginTime:{}, endTime:{} orderType:{}", current, pageSize, commentId,
                topicId, comment, commentUserId, isSensitiveWord, beginTime, endTime, orderType);

        boolean isSensitiveWordNotValid = isSensitiveWord != null && (isSensitiveWord > 1 || isSensitiveWord < 0);
        boolean topicIdNotValid = topicId != null && (topicId <= 0 || topicId > Integer.MAX_VALUE);
        boolean commentIdNotValid = commentId != null && (commentId <= 0 || commentId > Integer.MAX_VALUE);
        boolean userIdNotValid = commentUserId != null && (commentUserId <= 0 || commentUserId > Integer.MAX_VALUE);
        if (topicIdNotValid || isSensitiveWordNotValid || commentIdNotValid || userIdNotValid) {
            return NewResponseUtil.makeSuccess(Collections.EMPTY_MAP);
        }


        //将endTime的后缀改为 23：59：59
        if (endTime != null) {
            long time  = endTime.getTime() + 1000 * 60 * 60 * 24;
            endTime = new Timestamp(time);
        }

        List<CfTopicComment> cfTopicComments = adminCfTopicCommentBiz.
                selectByPage(commentId, topicId, comment, commentUserId, isSensitiveWord,
                        beginTime, endTime, current, pageSize, orderType);
        Map<String, Object> result = Maps.newHashMap();

        //获取话题信息
        Set<Integer> topicIdList = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(cfTopicComments)){
            topicIdList = cfTopicComments.stream().map(CfTopicComment::getTopicId).collect(Collectors.toSet());
        } else if (topicId != null) {
            topicIdList.add(topicId);
        }
        List<CfTopic> cfTopics = miniAppDelegate.listCfTopicByIds(Lists.newArrayList(topicIdList));

        if (CollectionUtils.isEmpty(cfTopicComments)){
            if (topicId == null){
                result.put("topicTitle","");
            } else {
                result.put("topicTitle",cfTopics.get(0).getTitle());
            }
            result.put("list", Lists.newArrayList());
            result.put("pagination", PageUtil.transform2PageMap(cfTopicComments));
            return NewResponseUtil.makeSuccess(result);
        }

        Map<Integer, CfTopic> topicMap = cfTopics.stream().collect(Collectors.toMap(CfTopic::getId, Function.identity()));
        //获取用户信息
        List<Long> userIdList = cfTopicComments.stream().map(CfTopicComment::getUserId).collect(Collectors.toList());
        List<UserInfoModel> userInfoInfoList = userInfoServiceBiz.getUserInfoByUserIdBatch(userIdList);
        Map<Long, String> userIdNicknameMap = userInfoInfoList.stream().
                collect(Collectors.toMap(UserInfoModel::getUserId, UserInfoModel::getNickname));

        //获取评论信息
        Set<Long> commentIdList = cfTopicComments.stream().map(CfTopicComment::getId).collect(Collectors.toSet());
        List<CfIdCountVo> cfIdCountVos = adminCfCommentPriseBiz.selectPraiseCountByCommentIdList(commentIdList);
        Map<Long, Integer> idCountMap = cfIdCountVos.stream().collect(Collectors.toMap(CfIdCountVo::getId, CfIdCountVo::getCounts));
        //获取评论点赞数
        List<CfCommentDynamic> cfCommentDynamics = ugcDelegate.listByCommentIds(Lists.newArrayList(commentIdList));
        Map<Long, Integer> commentTopMap = cfCommentDynamics.stream().
                collect(Collectors.toMap(CfCommentDynamic::getCommentId, CfCommentDynamic::getTopStatus));

        List<CfTopicCommentVo> dataList = cfTopicComments.stream().map(val -> {
            //删除话题之后   应该删除对应的评论
            CfTopic cfTopic = topicMap.get(val.getTopicId());
            Set<String> sensitiveWord = cfMinaSensitiveWordService.get(val.getContent());
            String join = Joiner.on(",").join(sensitiveWord);
            String nickname = userIdNicknameMap.getOrDefault(val.getUserId(), "");
            Integer praiseCount = idCountMap.getOrDefault(val.getId(), 0);
            Integer topStatus = commentTopMap.getOrDefault(val.getId(), 0);
            boolean reply = val.getParentId() == 0l ? false : true;
            return new CfTopicCommentVo(cfTopic.getId(), cfTopic.getTitle(), val.getId(), val.getCreateTime(),
                    val.getContent(), join, val.getUserId(), nickname, praiseCount, topStatus, reply);
        }).collect(Collectors.toList());

        //添加话题标题
        result.put("topicTitle",cfTopics.get(0).getTitle());
        result.put("list", dataList);
        result.put("pagination", PageUtil.transform2PageMap(cfTopicComments));
        return NewResponseUtil.makeSuccess(result);
    }

    @RequiresPermission("topic-comment:handle")
    @RequestMapping(path = "handle")
    public Response handle(@RequestParam("commentId")Long commentId,@RequestParam("type")Integer type) {
        log.info("CfMinaTopicCommentController handle commentId:{}", commentId);
//        CfTopicComment cfTopicComment = adminCfTopicCommentBiz.selectByIdAndDeleteStatus(commentId);
//        if (cfTopicComment == null) {
//            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
//        }
//        CfCommentDynamic cfCommentDynamic = ugcDelegate.selectByCommentId(commentId);
//        //若为同一组下的删除他的所有评论
//        int deleteNum = adminCfTopicCommentBiz.deleteByGroupId(commentId) + 1;
//        log.info("CfMinaTopicCommentController handle delete comment num:{}", deleteNum);
//        if (cfCommentDynamic != null) {
//            ugcDelegate.deleteByCommentId(commentId);
//            CfTopicShareCommentCount cfTopicShareCommentCount = cfTopicShareCommentBiz.selectByTopicId(cfTopicComment.getTopicId());
//            if (cfTopicShareCommentCount != null) {
//                int praiseNum = cfCommentDynamic.getPraiseNumber();
//                log.info("CfMinaTopicCommentController delete before commentCount:{},praiseCount:{} ",
//                        cfTopicShareCommentCount.getCommentCount(), cfTopicShareCommentCount.getPraiseCount());
//                int allCommentNum = cfTopicShareCommentCount.getCommentCount() > 0
//                        ? cfTopicShareCommentCount.getCommentCount() - deleteNum : 0;
//                int allPraiseNum = cfTopicShareCommentCount.getPraiseCount() > praiseNum
//                        ? cfTopicShareCommentCount.getPraiseCount() - praiseNum : 0;
//                log.info("CfMinaTopicCommentController delete  deleteNum:{},praiseNum:{} ",
//                        deleteNum, praiseNum);
//                cfTopicShareCommentCount.setCommentCount(allCommentNum);
//                cfTopicShareCommentCount.setPraiseCount(allPraiseNum);
//                cfTopicShareCommentBiz.updateByTopicId(cfTopicComment.getTopicId(), cfTopicShareCommentCount);
//            }
//        }
//        adminCfTopicCommentBiz.deleteById(commentId);
        CommentTypeEnum commentTypeEnum = CommentTypeEnum.forNumber(type);
        RemoveCommentRequest request = RemoveCommentRequest.newBuilder()
                .setCommentId(commentId).setType(commentTypeEnum)
                .build();
        RemoveCommentResponse commentResponse = this.xcxTopicCommenGrpcClient.removeComment(request);
        if (commentResponse.getResultEnum() != ResultEnum.SUCCESS){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        return NewResponseUtil.makeSuccess(null, "删除成功");
    }

    @RequiresPermission("topic-comment:update-top")
    @RequestMapping(path = "update-top", method = RequestMethod.POST)
    public Response updateTopStatus(int status, long commentId) {
        if (status < 0 || status > 1 || commentId < 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        CfCommentDynamic cfCommentDynamic = ugcDelegate.selectCfCommentDynamicByCommentId(commentId);
        if (cfCommentDynamic == null) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (cfCommentDynamic.getTopStatus() != commentId) {
            adminCommentDynamicBiz.updateTopStatusByCommentId(status, commentId);
        }
        return NewResponseUtil.makeSuccess(null);
    }

    private SensitivewordFilter getSensitivewordFilter() {
        SensitivewordFilter instance = SensitivewordFilter.getInstance();
        instance.reload();
        return instance;
    }
}
