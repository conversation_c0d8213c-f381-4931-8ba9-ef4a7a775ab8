package com.shuidihuzhu.cf.admin.controller.api.report.v2;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.model.cfOperatingProfile.CfOperatingProfileResult;
import com.shuidihuzhu.cf.service.crowdfunding.report.CfReportRiskService;
import com.shuidihuzhu.cf.vo.crowdfunding.CfFundraiserVo;
import com.shuidihuzhu.cf.vo.crowdfunding.CfQuestionerVo;
import com.shuidihuzhu.cf.vo.crowdfunding.CfRiskSelectedVo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/3/11
 */
@RestController
@RequestMapping(path = "/admin/cf/report/v2/risk")
public class CfReportRiskController {
    @Autowired
    private CfReportRiskService cfReportRiskService;

    @ApiOperation("质疑人沟通内容")
    @RequestMapping(path = "get-questioner-list", method = RequestMethod.POST)
    @RequiresPermission("risk:get-questioner-list")
    public Response<List<CfQuestionerVo>> getQuestionerList(@ApiParam("案例id") @RequestParam("caseId") int caseId) {
        return NewResponseUtil.makeSuccess(cfReportRiskService.getQuestionerList(caseId));
    }

    @ApiOperation("筹款方沟通内容")
    @RequestMapping(path = "get-fundraiser-list", method = RequestMethod.POST)
    @RequiresPermission("risk:get-fundraiser-list")
    public Response<List<CfFundraiserVo>> getFundraiserVoList(@ApiParam("案例id") @RequestParam("caseId") int caseId) {
        return NewResponseUtil.makeSuccess(cfReportRiskService.getCfFundraiserVoList(caseId));
    }


    @ApiOperation("风险标签")
    @RequestMapping(path = "get-risk-label-list", method = RequestMethod.POST)
    @RequiresPermission("risk:get-risk-label-list")
    public Response<List<CfOperatingProfileResult>> getRiskLabelList(@ApiParam("案例id") @RequestParam("caseId") int caseId,
                                                                     @ApiParam("0:全部标签,1:人工判断标签") @RequestParam("status") int status) {
        return NewResponseUtil.makeSuccess(cfReportRiskService.getRiskLabelList(caseId, status));
    }


    @ApiOperation("案例选中的风险标签")
    @RequestMapping(path = "get-case-risk-label", method = RequestMethod.POST)
    @RequiresPermission("risk:get-case-risk-label")
    public Response<List<CfRiskSelectedVo>> getCaseRiskLabel(@ApiParam("案例id") @RequestParam("caseId") int caseId) {
        return NewResponseUtil.makeSuccess(cfReportRiskService.getCaseRiskLabel(caseId));
    }

    @ApiOperation("保存选中的风险标签")
    @RequestMapping(path = "add-case-risk-label", method = RequestMethod.POST)
    @RequiresPermission("risk:add-case-risk-label")
    public Response<Integer> addCaseRiskLabel(@ApiParam("风险标签") @RequestParam("labelJson") String labelJson,
                                                             @ApiParam("案例id") @RequestParam("caseId") int caseId) {
        return NewResponseUtil.makeSuccess(cfReportRiskService.addCaseRiskLabel(labelJson, caseId));
    }


    @ApiOperation("举报条目增加标签")
    @RequestMapping(path = "add-report-risk-label", method = RequestMethod.POST)
    @RequiresPermission("risk:add-report-risk-label")
    public Response<Integer> addReportRiskLabel(@ApiParam("案例id") @RequestParam("reportId") int reportId,
                                                @ApiParam("风险标签") @RequestParam("label") String label) {
        return NewResponseUtil.makeSuccess(cfReportRiskService.addReportRiskLabel(reportId, label));
    }
}
