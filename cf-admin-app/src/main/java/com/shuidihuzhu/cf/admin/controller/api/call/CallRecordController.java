package com.shuidihuzhu.cf.admin.controller.api.call;

import com.google.common.collect.Maps;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.call.CallRecordBiz;
import com.shuidihuzhu.cf.call.CallInModel;
import com.shuidihuzhu.cf.call.CallOutModel;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.admin.PageUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping(value = "/admin/cf/call", produces = "application/json;charset=UTF-8")
public class CallRecordController {
    @Autowired
    private CallRecordBiz callRecordBiz;

    @ApiOperation("根据日期获取呼入录音列表")
    @RequestMapping(path = "get-call-in-records", method = RequestMethod.POST)
    @RequiresPermission("call:get-call-in-records")
    public Object getCallInRecords(@RequestParam String startTime,  @RequestParam String endTime,
                                   @RequestParam(required = false) String customerPhoneNumber,
                                   @RequestParam(required = false, defaultValue = "1") int current,
                                   @RequestParam(required = false, defaultValue = "10") int pageSize) {
        log.info("userId={} 获取从{}到{}的呼入电话列表，当前页={}, pageSize={}", ContextUtil.getAdminUserId(), startTime, endTime, current, pageSize);
        Response checkResponse = checkTimeFormat(startTime, endTime);
        if (checkResponse.notOk()) {
            return checkResponse;
        }
        List<CallInModel> callInModels = callRecordBiz.getCallInRecords(startTime, endTime, customerPhoneNumber, current, pageSize);
        Map<String, Object> result = Maps.newHashMap();
        result.put("pagination", PageUtil.transform2PageMap(callInModels));
        result.put("data", callInModels);
        return NewResponseUtil.makeSuccess(result);
    }

    private Response checkTimeFormat(String startTime, String endTime) {
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            if (StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime) || dateFormat.parse(startTime).after(dateFormat.parse(endTime))) {
                return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
            }
            long days = (dateFormat.parse(endTime).getTime() - dateFormat.parse(startTime).getTime()) / (3600 * 24 * 1000);
            if (days > 7) {
                return NewResponseUtil.makeError(AdminErrorCode.DAY_RANGE_ERROR);
            }
        } catch (ParseException e) {
            return NewResponseUtil.makeFail("开始或结束日期不符合 yyyy-MM-dd 格式");
        }
        return NewResponseUtil.makeSuccess(null);
    }

    @ApiOperation("根据日期获取呼出录音列表")
    @RequestMapping(path = "get-call-out-records", method = RequestMethod.POST)
    @RequiresPermission("call:get-call-out-records")
    public Object getCallOutRecords(@RequestParam String startTime,  @RequestParam String endTime,
                                    @RequestParam(required = false) String customerPhoneNumber,
                                    @RequestParam(required = false, defaultValue = "1") int current,
                                    @RequestParam(required = false, defaultValue = "10") int pageSize) {
        log.info("userId={} 获取从{}到{}的呼出电话列表，当前页={}, pageSize={}", ContextUtil.getAdminUserId(), startTime, endTime, current, pageSize);
        Response checkResponse = checkTimeFormat(startTime, endTime);
        if (checkResponse.notOk()) {
            return checkResponse;
        }
        List<CallOutModel> callOutModels = callRecordBiz.getCallOutRecords(startTime, endTime, customerPhoneNumber, current, pageSize);
        Map<String, Object> result = Maps.newHashMap();
        result.put("pagination", PageUtil.transform2PageMap(callOutModels));
        result.put("data", callOutModels);
        return NewResponseUtil.makeSuccess(result);
    }
}
