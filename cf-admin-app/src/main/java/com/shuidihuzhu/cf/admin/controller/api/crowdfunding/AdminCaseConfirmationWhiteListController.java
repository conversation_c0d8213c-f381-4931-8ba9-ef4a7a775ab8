package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;


import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCaseConfirmationWhiteListBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.model.crowdfunding.CfCaseEndWhiteList;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.admin.PageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DurationFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Slf4j
@Controller
@RequestMapping(path = "/admin/cf/case-confirmation")
public class AdminCaseConfirmationWhiteListController {
    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;

    @Autowired
    private AdminCaseConfirmationWhiteListBiz adminCaseConfirmationWhiteListBiz;

    @ResponseBody
    @RequestMapping(path = "/show-case", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("case-confirmation-white-list:show-case")
    public Response<CfCaseEndWhiteList> showCase(@RequestParam(name = "caseId") int caseId) {

        CrowdfundingInfo info = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if (info == null) {
            return NewResponseUtil.makeFail("案例不存在");
        }

        Date now = new Date();
        String dayStr = "0";
        if (info.getEndTime().getTime() < now.getTime()) {
            //案例未结束会导致异常
            dayStr = DurationFormatUtils.formatPeriod(info.getEndTime().getTime(), now.getTime(), "d");
        }
        int days = Integer.parseInt(dayStr);
        if (days < 90) {
            return NewResponseUtil.makeFail("案例未结束90天");
        }

        CfCaseEndWhiteList cfCaseEndWhiteList = adminCaseConfirmationWhiteListBiz.getList(caseId);
        if (cfCaseEndWhiteList != null) {
            return NewResponseUtil.makeFail("案例已在白名单");
        }

        CfCaseEndWhiteList caseEndWhiteList = new CfCaseEndWhiteList();
        caseEndWhiteList.setTitle(info.getTitle());
        caseEndWhiteList.setCaseId(caseId);
        caseEndWhiteList.setInfoUuid(info.getInfoId());

        return NewResponseUtil.makeSuccess(caseEndWhiteList);
    }

    @ResponseBody
    @RequestMapping(path = "/add-white-list", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("case-confirmation-white-list:add-white-list")
    public Response<CfCaseEndWhiteList> addWhiteList(@RequestParam(name = "caseId") int caseId) {

        int userId = ContextUtil.getAdminUserId();
        adminCaseConfirmationWhiteListBiz.add(caseId, userId);

        return NewResponseUtil.makeSuccess(null);
    }

    @ResponseBody
    @RequestMapping(path = "/select-white-list", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("case-confirmation-white-list:select-white-list")
    public Response<Map<String, Object>> selectWhiteList(@RequestParam("current") int current,
                                                         @RequestParam("pageSize") int pageSize,
                                                         @RequestParam("caseId") int caseId) {

        List<CfCaseEndWhiteList> cfCaseEndWhiteListList = adminCaseConfirmationWhiteListBiz.selectWhiteList(caseId,current, pageSize);

        if (CollectionUtils.isEmpty(cfCaseEndWhiteListList)) {
            Map<String, Object> result = Maps.newHashMap();
            cfCaseEndWhiteListList = Lists.newArrayList();
            result.put("pagination", PageUtil.transform2PageMap(cfCaseEndWhiteListList));
            result.put("list", cfCaseEndWhiteListList);
            return NewResponseUtil.makeSuccess(result);
        }

        Map<String, Object> result = Maps.newHashMap();
        result.put("pagination", PageUtil.transform2PageMap(cfCaseEndWhiteListList));
        result.put("list", cfCaseEndWhiteListList);
        return NewResponseUtil.makeSuccess(result);
    }
}
