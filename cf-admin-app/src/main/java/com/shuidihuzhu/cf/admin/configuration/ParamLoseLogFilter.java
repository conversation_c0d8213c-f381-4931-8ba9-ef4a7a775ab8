package com.shuidihuzhu.cf.admin.configuration;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.Globals;
import org.apache.catalina.connector.Request;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.tomcat.util.http.Parameters;
import org.springframework.web.util.ContentCachingRequestWrapper;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.Enumeration;
import java.util.Objects;

/**
 * @Author: wangpeng
 * @Date: 2023/4/17 20:23
 * @Description:
 */
@Slf4j
public class ParamLoseLogFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, Filter<PERSON>hain chain) throws IOException, ServletException {
        log.info("ParamLoseLogFilter begin {}", request.getContentLength());
        ContentCachingRequestWrapper contentCachingRequestWrapper = null;
        if (request instanceof HttpServletRequest) {
            contentCachingRequestWrapper = new ContentCachingRequestWrapper((HttpServletRequest) request);
        }
        chain.doFilter(request, response);

        JSONObject requestHeader = getRequestHeader(contentCachingRequestWrapper);
        JSONObject requestParamJSON = getRequestParamJSON(contentCachingRequestWrapper);
        Parameters.FailReason reason = (Parameters.FailReason) request.getAttribute(Globals.PARAMETER_PARSE_FAILED_REASON_ATTR);

        outParamError(reason, requestParamJSON, request);

        log.info("ParamLoseLogFilter len:{} header:{} param:{} reason:{}", request.getContentLength(), requestHeader, requestParamJSON, reason);
    }

    private void outParamError(Parameters.FailReason reason, JSONObject requestParamJSON, ServletRequest request) {
        if (Objects.isNull(reason) && MapUtils.isNotEmpty(requestParamJSON)) {
            return;
        }
        try {
            Field f = request.getClass().getDeclaredField("request");
            // grant access to (protected) field
            f.setAccessible(true);
            Request realRequest = (Request) f.get(request);
            if (Objects.isNull(realRequest)) {
                return;
            }
            org.apache.coyote.Request coyoteRequest = realRequest.getCoyoteRequest();
            if (Objects.isNull(coyoteRequest)) {
                return;
            }
            Exception errorException = coyoteRequest.getErrorException();
            log.info("ParamLoseLogFilter parse param error", errorException);

        } catch (Exception e) {
            log.info("ParamLoseLogFilter parse request error", e);
        }
    }

    private JSONObject getRequestHeader(ContentCachingRequestWrapper requestWrapper) {

        if (Objects.isNull(requestWrapper)) {
            return null;
        }
        Enumeration<String> headerNames = requestWrapper.getHeaderNames();
        JSONObject headerJsonObject = new JSONObject();
        if(!ObjectUtils.isEmpty(headerNames)) {
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                headerJsonObject.put(headerName,requestWrapper.getHeader(headerName));
            }
        }
        return  headerJsonObject;
    }

    private JSONObject getRequestParamJSON(ContentCachingRequestWrapper requestWrapper) {
        if (Objects.isNull(requestWrapper)) {
            return null;
        }
        Enumeration<String> parameterEnum=requestWrapper.getParameterNames();
        JSONObject paramJsonObject = new JSONObject();
        if(!ObjectUtils.isEmpty(parameterEnum)) {
            while (parameterEnum.hasMoreElements()) {
                String name = parameterEnum.nextElement();
                String[] value = requestWrapper.getParameterValues(name);
                paramJsonObject.put(name,value);
            }
        }
        return  paramJsonObject;
    }
}
