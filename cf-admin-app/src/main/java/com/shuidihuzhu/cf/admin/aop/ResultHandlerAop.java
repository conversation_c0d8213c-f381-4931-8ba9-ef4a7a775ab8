package com.shuidihuzhu.cf.admin.aop;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.model.ConfigChange;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.shuidihuzhu.cf.service.resulthandler.*;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.List;

/**
 * @author: fengxuan
 * @create 2019-09-20 03:23
 **/
@Component
@Aspect
@Slf4j
@RefreshScope
public class ResultHandlerAop {

    @Value("${apollo.sensitive.result.switch:false}")
    private boolean sensitiveResultSwitch;

    @Value("${apollo.sensitive.result.noneedhandler:[]}")
    private String sensitiveResultNoNeedHandler;

    /**
     * 由于@RefreshScope使用了懒加载模式
     * 用作初次将apollo配置加载到
     * @see AbstractContentShowHandler 的noNeedFilterWordList
     */
    private boolean firstCall = true;


    @Around("@annotation(resultSensitiveHandler)")
    public Object handleReturnData(JoinPoint joinPoint, ResultSensitiveHandler resultSensitiveHandler) throws Throwable {
        Object ret = ((ProceedingJoinPoint) joinPoint).proceed();

        if (sensitiveResultSwitch) {
            Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
            if (firstCall) {
                init();
                firstCall = false;
            }
            AopHandlerHelper.handleData(ret, method);
        }
        return ret;
    }


    public void init() {
        try {
            List<WordManager> wordManagers = JSON.parseArray(sensitiveResultNoNeedHandler, WordManager.class);//已检查过
            DefaultContentShowFactory.setNoNeedHandleWord(wordManagers);
        } catch (Exception e) {
            log.error("start sensitiveResultNoNeedHandler:{} not json", sensitiveResultNoNeedHandler, e);
        }
    }

    /**
     * 配置发生变化时，更新
     * @see AbstractContentShowHandler 的noNeedFilterWordList
     * @param changeEvent
     */
    @ApolloConfigChangeListener
    public void listConfigChange(ConfigChangeEvent changeEvent) {
        log.info("ApolloConfigUtil Changes nameSpace:{}", changeEvent.getNamespace());
        for (String key : changeEvent.changedKeys()) {
            if ("apollo.sensitive.result.noneedhandler".equals(key)) {
                ConfigChange change = changeEvent.getChange(key);
                log.info("sensitive.result.noneedhandler change oldValue:{},newValue:{},changeType:{}", change.getOldValue(), change.getNewValue(), change.getChangeType());
                String newValue = change.getNewValue();
                try {
                    List<WordManager> wordManagers = JSON.parseArray(newValue, WordManager.class);//已检查过
                    DefaultContentShowFactory.setNoNeedHandleWord(wordManagers);
                } catch (Exception e) {
                    log.error("sensitive.result.noneedhandler format error not json! value:{}", newValue, e);
                }
            }
        }
    }



}
