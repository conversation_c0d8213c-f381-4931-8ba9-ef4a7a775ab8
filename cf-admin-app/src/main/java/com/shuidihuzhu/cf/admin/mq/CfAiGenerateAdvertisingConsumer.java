package com.shuidihuzhu.cf.admin.mq;

import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord;
import com.shuidihuzhu.cf.service.ai.AdGenerationService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description: ai生成广告语
 * @Author: panghairui
 * @Date: 2025/6/19 14:11
 */
@Slf4j
@Service
@RefreshScope
@RocketMQListener(id = "cf-ai-advertising-" + MQTagCons.CF_OPERATION_MSG,
        group = "cf-ai-advertising-group",
        tags = MQTagCons.CF_OPERATION_MSG,
        topic = MQTopicCons.CF)
public class CfAiGenerateAdvertisingConsumer implements MessageListener<CfOperatingRecord> {

    @Resource
    private AdGenerationService adGenerationService;

    @Value("${apollo.admin.ai.ad-switch:false}")
    private boolean adSwitch;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfOperatingRecord> mqMessage) {
        if (mqMessage == null || mqMessage.getPayload() == null || !adSwitch) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        log.info("CfAiGenerateAdvertisingConsumer receive message: {}", mqMessage.getPayload());
        CfOperatingRecord cfOperatingRecord = mqMessage.getPayload();
        if (StringUtils.isEmpty(cfOperatingRecord.getInfoUuid())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        adGenerationService.generateAllAdsForCase(cfOperatingRecord.getInfoUuid());

        return ConsumeStatus.CONSUME_SUCCESS;
    }

}
