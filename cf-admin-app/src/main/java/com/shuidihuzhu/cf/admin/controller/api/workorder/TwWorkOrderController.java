package com.shuidihuzhu.cf.admin.controller.api.workorder;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.service.CfCaseWorkOrderService;
import com.shuidihuzhu.client.cf.workorder.CfTwWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderListParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OneTypeEnum;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @DATE 2020/8/31
 */
@RestController
@RequestMapping(path="/admin/workorder/tw")
@Slf4j
public class TwWorkOrderController {

    @Autowired
    private CfTwWorkOrderClient cfTwWorkOrderClient;

    @Autowired
    private CfCaseWorkOrderService workOrderService;

    @Autowired
    private CfWorkOrderClient workOrderClient;

    @RequiresPermission("tw-work-order:orderList")
    @RequestMapping(path = "tw-orderlist", method = RequestMethod.POST)
    public Response chuciOrderList(@RequestParam("param") String param) {

        WorkOrderListParam p = JSON.parseObject(param, WorkOrderListParam.class);//已检查过

        workOrderService.fillTimeIfNullByHandleResult(p, OneTypeEnum.tw);

        return cfTwWorkOrderClient.twOrderlist(p);
    }

    @RequiresPermission("tw-work-order:check")
    @RequestMapping(path = "tw-check", method = RequestMethod.POST)
    public Response chuciCheck(@RequestParam("caseId") int caseId,
                               @RequestParam("workOrderId") long workOrderId){

        if (caseId <= 0 || workOrderId <= 0){
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        Response<WorkOrderVO> response = workOrderClient.getWorkOrderById(workOrderId);
        //如果工单未处理才可以处理
        if (response != null && response.getData() != null){
            WorkOrderVO workOrderVO = response.getData();
            if (workOrderVO.getHandleResult() == HandleResultEnum.doing.getType() ||
                workOrderVO.getHandleResult() == HandleResultEnum.later_doing.getType()){
                return NewResponseUtil.makeSuccess(false);
            }else {
                return NewResponseUtil.makeSuccess(true);
            }
        }

        return NewResponseUtil.makeSuccess(false);
    }

}
