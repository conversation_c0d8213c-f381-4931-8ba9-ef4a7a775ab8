package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;

import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CfFinishStatus;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;

import com.shuidihuzhu.cf.service.admin.AdminApproveService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * @author: wanghui
 * @time: 2018/10/25 2:45 PM
 * @description: crowfunding_info 内容操作
 */
@RestController
@Slf4j
@RefreshScope
@RequestMapping("/admin/cf/crowdfunding-info")
public class CrowdfundingInfoController {


    @Autowired
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private AdminCfInfoExtBiz adminCfInfoExtBiz;
    @Autowired
    private CommonOperationRecordClient commonOperationRecordClient;
    @Autowired
    private AdminApproveService adminApproveService;

    @ApiOperation("延长案例时间")
    @RequiresPermission("crowdfunding-info:update-end-time")
    @RequestMapping(value = "/update-end-time", method = RequestMethod.POST)
    public Response updateShow(@RequestParam(value = "caseKey") String caseKey,
                               @RequestParam("endTime") Date endTime,
                               @RequestParam(required = false, defaultValue = "") String reason){
        int id = 0;
        CrowdfundingInfo crowdfundingInfo;
        // caseKey 带有- 说明 值为 info_uuid
        if (caseKey.contains("-")){
            crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(caseKey.trim());
            if (crowdfundingInfo==null) return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
            id = crowdfundingInfo.getId();
        }else {
            id = Integer.parseInt(caseKey.trim());//已检查过
            crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(id);
        }
        if (StringUtils.isBlank(reason)) {
            reason = "";
        }
        if (reason.length() > 200) {
            return NewResponseUtil.makeFail("原因超出200字");
        }
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeFail(AdminErrorCode.CF_NOT_FOUND);
        }

        crowdfundingInfoBiz.updateEndTime(crowdfundingInfo.getId(),endTime);
        adminCfInfoExtBiz.updateFinishStatusByCaseId(crowdfundingInfo.getId(), CfFinishStatus.NOT_FINISH.getValue());
        commonOperationRecordClient.create()
                .buildBasicPlatform(crowdfundingInfo.getId(), ContextUtil.getAdminUserId(), OperationActionTypeEnum.DELAY_CASE_END_TIME)
                .buildRemark("new_end_time: " + DateUtil.formatDateTime(endTime))
                .save();
        //案例备注
        String comment = "默认结束时间：" + DateUtil.formatDateTime(crowdfundingInfo.getEndTime()) +
                "；修改结束时间：" + DateUtil.formatDateTime(endTime) + "；修改原因：" + reason;
        adminApproveService.addApprove(crowdfundingInfo, "延长结束时间", comment, ContextUtil.getAdminUserId());
        return NewResponseUtil.makeSuccess(null);
    }
}
