package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.model.admin.vo.*;
import com.shuidihuzhu.cf.model.crowdfunding.CfDiseaseClassifyDO;
import com.shuidihuzhu.cf.model.crowdfunding.CfDiseaseManagerDO;
import com.shuidihuzhu.cf.service.crowdfunding.DiseaseManagerService;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import com.shuidihuzhu.pf.common.v2.model.pagehelper.PaginationListVO;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2019-11-08 17:12
 **/
@RestController
@Slf4j
@RequestMapping(value = "/admin/cf/disease/manager")
@Api("疾病管理")
public class CfDiseaseManagerController {

    @Autowired
    DiseaseManagerService diseaseManagerService;

    @ApiOperation("创建疾病信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "param", required = true, dataType = "String", dataTypeClass = CfDiseaseManagerDO.class)
    })
    @RequestMapping(path = "create", method = RequestMethod.POST)
    @RequiresPermission("diseaseManager:create")
    public Response<Boolean> create(@RequestParam("param") String param) {
        int adminUserId = ContextUtil.getAdminUserId();
        Response<CfDiseaseManagerDO> managerDOResponse = buildWhenCreate(param);
        if (managerDOResponse.notOk()) {
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        return diseaseManagerService.create(managerDOResponse.getData(), adminUserId);
    }


    @ApiOperation("修改疾病信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "param", required = true, dataType = "String", dataTypeClass = CfDiseaseManagerDO.class)
    })
    @RequestMapping(path = "edit", method = RequestMethod.POST)
    @RequiresPermission("diseaseManager:edit")
    public Response<Boolean> edit(@RequestParam(value = "param") String param) {
        int adminUserId = ContextUtil.getAdminUserId();
        Response<CfDiseaseManagerDO> managerDOResponse = buildWhenCreate(param);
        if (managerDOResponse.notOk() || managerDOResponse.getData().getId() <= 0) {
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        CfDiseaseManagerDO data = managerDOResponse.getData();
        return diseaseManagerService.edit(data, adminUserId);
    }


    @ApiOperation("删除疾病信息")
    @RequestMapping(path = "delete", method = RequestMethod.POST)
    @RequiresPermission("diseaseManager:delete")
    public Response<Boolean> delete(@RequestParam("diseaseManagerId") long diseaseManagerId) {
        int adminUserId = ContextUtil.getAdminUserId();
        return diseaseManagerService.delete(adminUserId, diseaseManagerId);
    }


    @ApiOperation("查找列表信息")
    @RequestMapping(path = "list", method = RequestMethod.POST)
    @RequiresPermission("diseaseManager:list")
    public Response<PaginationListVO<CfDiseaseManagerVo>> list(@RequestParam(value = "diseaseClassifyId", defaultValue = "0") int diseaseClassifyId,
                                                               @RequestParam(value = "standardName", required = false) String standardName,
                                                               @RequestParam(value = "medicalName", required = false) String medicalName,
                                                               @RequestParam(value = "normalName", required = false) String normalName,
                                                               @RequestParam(value = "raiseType", defaultValue = "0") int raiseType,
                                                               @RequestParam(defaultValue = "1") Integer current,
                                                               @RequestParam(defaultValue = "10") Integer pageSize) {
        Response<CfDiseaseManagerDO> diseaseResponse = buildWhenQuery(diseaseClassifyId, standardName, medicalName, normalName, raiseType);
        if (diseaseResponse.notOk()) {
            return ResponseUtil.makeFail(diseaseResponse.getMsg());
        }
        return diseaseManagerService.list(diseaseResponse.getData(), current, pageSize);
    }


    @ApiOperation("通过列表返回的id,点查接口,用于进入编辑页")
    @RequestMapping(path = "get-by-managerId", method = RequestMethod.POST)
    @RequiresPermission("diseaseManager:get-by-managerId")
    public Response<CfDiseaseManagerVo> get(@RequestParam("diseaseManagerId") long diseaseManagerId) {
        return diseaseManagerService.get(diseaseManagerId);
    }


    @ApiOperation("查看操作记录接口")
    @RequestMapping(path = "list-record", method = RequestMethod.POST)
    @RequiresPermission("diseaseManager:list-record")
    public Response<List<CfDiseaseManagerRecordVo>> listRecord(@RequestParam("diseaseManagerId") long diseaseManagerId) {
        return diseaseManagerService.listRecord(diseaseManagerId);
    }


    @ApiOperation("查看科室信息、治疗方案、疾病类型")
    @RequestMapping(path = "list-disease-nature", method = RequestMethod.POST)
    @RequiresPermission("diseaseManager:list-disease-nature")
    public Response<DiseaseNatureVo> listDiseaseClassify() {
        DiseaseNatureVo diseaseNatureVo = new DiseaseNatureVo();
        diseaseNatureVo.buildTreatmentProject()
                .buildRaiseType()
                .buildDiseaseClassify(diseaseManagerService.listAllClassifyInfo());
        return ResponseUtil.makeSuccess(diseaseNatureVo);
    }


    private Response<CfDiseaseManagerDO> buildWhenCreate(String param) {
        CfDiseaseManagerDO managerDO = JSON.parseObject(param, CfDiseaseManagerDO.class);//已检查过
        log.info("managerDO:{}", JSON.toJSONString(managerDO));
        //check
        boolean checkParam = managerDO.getDiseaseClassifyId() > 0;
        //standardName
        checkParam = checkParam && StringUtils.isNotBlank(managerDO.getStandardName());
        //raiseType
        checkParam = checkParam && CfDiseaseManagerDO.RaiseTypeEnum.findByCode(managerDO.getRaiseType()) != null;
        //treatmentProject
        checkParam = checkParam && CfDiseaseManagerDO.TreatmentProjectEnum.findByCode(managerDO.getTreatmentProject()) != null;
        //customTreatment
        checkParam = checkParam && Optional.ofNullable(managerDO.getCustomTreatment()).orElse("").length() <= 50;
        //check 科室
        checkParam = checkParam && diseaseManagerService.listAllClassifyInfo().stream()
                .map(CfDiseaseClassifyDO::getId)
                .collect(Collectors.toList())
                .contains(managerDO.getDiseaseClassifyId());
        if (!checkParam) {
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        //限制输入文本信息
        if (Optional.ofNullable(managerDO.getStandardName()).orElse("").length() > 100 &&
            Optional.ofNullable(managerDO.getMedicalName()).orElse("").length() > 200 &&
            Optional.ofNullable(managerDO.getNormalName()).orElse("").length() > 200 &&
            Optional.ofNullable(managerDO.getCustomTreatment()).orElse("").length() > 100
        ) {
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR_TOO_LONG);
        }
        //治疗费校验
        if (managerDO.getMinTreatmentFee() > managerDO.getMaxTreatmentFee()) {
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (StringUtils.isBlank(managerDO.getCustomTreatment())) {
            managerDO.setCustomTreatment("");
        }
        if (StringUtils.isBlank(managerDO.getNormalName())) {
            managerDO.setNormalName("");
        }
        if (StringUtils.isBlank(managerDO.getMedicalName())) {
            managerDO.setMedicalName("");
        }
        return ResponseUtil.makeSuccess(managerDO);
    }


    private Response<CfDiseaseManagerDO> buildWhenQuery(int diseaseClassifyId, String standardName, String medicalName,
                                                        String normalName, int raiseType) {
        //check
        CfDiseaseManagerDO managerDO = new CfDiseaseManagerDO();
        if (diseaseClassifyId > 0) {
            managerDO.setDiseaseClassifyId(diseaseClassifyId);
        }
        if (StringUtils.isNotBlank(standardName)) {
            managerDO.setStandardName(standardName);
        }
        if (StringUtils.isNotBlank(medicalName)) {
            managerDO.setMedicalName(medicalName);
        }
        if (StringUtils.isNotBlank(normalName)) {
            managerDO.setNormalName(normalName);
        }
        if (raiseType > 0) {
            managerDO.setRaiseType(raiseType);
        }
        return ResponseUtil.makeSuccess(managerDO);
    }

}
