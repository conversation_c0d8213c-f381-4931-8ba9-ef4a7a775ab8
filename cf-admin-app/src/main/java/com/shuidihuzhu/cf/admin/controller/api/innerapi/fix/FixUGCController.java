package com.shuidihuzhu.cf.admin.controller.api.innerapi.fix;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.model.crowdfunding.deliver.CrowdfundingCommentDeliver;
import com.shuidihuzhu.cf.service.crowdfunding.report.CfReportRiskService;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.ugc.IUgcDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingComment;
import com.shuidihuzhu.cf.service.sensitive.adapter.SensitiveCommentAdapter;
import com.shuidihuzhu.cf.service.sensitive.processor.SensitiveProcessService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("innerapi/cf/admin/fix/ugc")
public class FixUGCController {

    private String lastKey;

    @Resource
    private SensitiveProcessService sensitiveProcessService;

    @Autowired
    private IUgcDelegate ugcDelegate;

    @Autowired
    private SensitiveCommentAdapter sensitiveCommentAdapter;

    @Autowired
    private CfReportRiskService cfReportRiskService;


    @PostMapping("comment-1")
    public Response fixComment1(String key){
        if (isRepeat(key)) return ResponseUtil.makeSuccess("重复调用");

        String[] strings = StringUtils.split(COMMENT_IDS, "\n");
        for(String commentId : strings) {
            processComment(Long.valueOf(commentId));
        }

        return NewResponseUtil.makeSuccess(null);
    }

    private void processComment(Long id) {
        try {
            CrowdfundingComment comment = ugcDelegate.getByIdNoCareDeleted(id);
            CrowdfundingCommentDeliver crowdfundingCommentDeliver = new CrowdfundingCommentDeliver();
            BeanUtils.copyProperties(comment, crowdfundingCommentDeliver);
            crowdfundingCommentDeliver.setUniquelyIdentifies("comment");

            sensitiveProcessService.process(crowdfundingCommentDeliver, sensitiveCommentAdapter);
        } catch (Exception e) {
            log.error("commentId: {}", id, e);
        }
    }


    private boolean isRepeat(String key) {
        if (StringUtils.equals(key, lastKey)) {
            return true;
        }
        lastKey = key;
        return false;
    }

    public static void main(String [] args) {
        String[] strings = StringUtils.split(COMMENT_IDS, "\n");
        System.out.println("strings = " + JSON.toJSONString(strings));
    }

    private static String COMMENT_IDS = "558866210\n";

}
