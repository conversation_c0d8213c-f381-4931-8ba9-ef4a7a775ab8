package com.shuidihuzhu.cf.admin.controller.api.workorder;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminWorkOrderReportBiz;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderReport;
import com.shuidihuzhu.client.model.OldReportWorkOrder;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-02-23 16:46
 **/
@RestController
@RequestMapping(path = "innerapi/cf/admin/work-order")
@Slf4j
public class CfOldReportWorkOrderClientController {

    @Autowired
    private AdminWorkOrderReportBiz adminWorkOrderReportBiz;

    @RequestMapping(path = "get-old-report", method = RequestMethod.POST)
    public Response<List<OldReportWorkOrder>> getOldReportWorkOrder(int caseId, int dealResult) {
        if (caseId <= 0) {
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        List<AdminWorkOrderReport> adminWorkOrderReports = adminWorkOrderReportBiz.findByCaseIdAndDealResult(caseId, List.of(dealResult));
        if (CollectionUtils.isEmpty(adminWorkOrderReports)) {
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        List<OldReportWorkOrder> reportWorkOrders = adminWorkOrderReports.stream()
                .map(adminWorkOrderReport -> new OldReportWorkOrder(adminWorkOrderReport.getId(), adminWorkOrderReport.getWorkOrderId(),
                        adminWorkOrderReport.getType(), adminWorkOrderReport.getCaseId(), adminWorkOrderReport.getReportId(),
                        adminWorkOrderReport.getCaseRisk(), adminWorkOrderReport.getFollowType(), adminWorkOrderReport.getDealResult()))
                .collect(Collectors.toList());
        return NewResponseUtil.makeSuccess(reportWorkOrders);
    }

    @RequestMapping(path = "get-old-report-by-status", method = RequestMethod.POST)
    public Response<List<OldReportWorkOrder>> getOldReportWorkOrderByStatus(int caseId, String dealResults) {
        if (caseId <= 0 || StringUtils.isEmpty(dealResults)) {
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        List<Integer> dealResultList = Stream.of(dealResults.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        List<AdminWorkOrderReport> adminWorkOrderReports = adminWorkOrderReportBiz.findByCaseIdAndDealResult(caseId, dealResultList);
        if (CollectionUtils.isEmpty(adminWorkOrderReports)) {
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        List<OldReportWorkOrder> reportWorkOrders = adminWorkOrderReports.stream()
                .map(adminWorkOrderReport -> new OldReportWorkOrder(adminWorkOrderReport.getId(), adminWorkOrderReport.getWorkOrderId(),
                        adminWorkOrderReport.getType(), adminWorkOrderReport.getCaseId(), adminWorkOrderReport.getReportId(),
                        adminWorkOrderReport.getCaseRisk(), adminWorkOrderReport.getFollowType(), adminWorkOrderReport.getDealResult()))
                .collect(Collectors.toList());
        return NewResponseUtil.makeSuccess(reportWorkOrders);
    }

}
