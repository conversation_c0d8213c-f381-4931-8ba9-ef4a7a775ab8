package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.google.common.collect.Maps;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCfRepeatInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingInfoBiz;
import com.shuidihuzhu.cf.enums.AdminErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.cf.service.crowdfunding.CfContentImageService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020/4/8
 */

@Slf4j
@RestController
@RequestMapping(path = "/admin/cf/content/image")
public class CfContentImageController {

    @Resource
    private AdminCrowdfundingInfoBiz crowdfundingInfoBiz;

    @Resource
    private AdminCfRepeatInfoBiz repeatInfoBizService;

    @Resource
    private CfContentImageService cfContentImageService;

    @ApiOperation(value = "手动结束图文混排")
    @RequiresPermission("contentImage:close")
    @RequestMapping(path = "close", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response close(@RequestParam("infoUuid") String infoUuid) {
        if (StringUtils.isBlank(infoUuid)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        int userId = ContextUtil.getAdminUserId();
        if (userId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_NOT_LOGIN_ERROR);
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }

        return cfContentImageService.close(userId, crowdfundingInfo);
    }

    @ApiOperation(value = "保存图文混排信息")
    @RequiresPermission("contentImage:save")
    @RequestMapping(path = "save", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Response<Boolean> save(@RequestParam("infoUuid") String infoUuid, @RequestParam("contentImage") String contentImage) {
        if (StringUtils.isAnyBlank(infoUuid, contentImage)) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_PARAM_ERROR);
        }

        int userId = ContextUtil.getAdminUserId();
        if (userId <= 0) {
            return NewResponseUtil.makeError(AdminErrorCode.SYSTEM_NOT_LOGIN_ERROR);
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }

        return cfContentImageService.save(userId, crowdfundingInfo, contentImage);
    }

    @ApiOperation(value = "图文混排信息")
    @RequestMapping(path = "detail", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("contentImage:detail")
    public Response<Map<String, Object>> detail(@RequestParam("infoUuid") String infoUuid) {
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }

        int caseId = crowdfundingInfo.getId();

        Map<String, Object> result = Maps.newHashMap();

        result.put("crowdfundingInfo", crowdfundingInfo);
        result.put("crowdfundingAttachmentList", repeatInfoBizService.getFundingAttachmentWithRepeatInfo(caseId));

        return NewResponseUtil.makeSuccess(result);
    }

    @ApiOperation(value = "图文混排信息-补充展示")
    @RequestMapping(path = "detail-supplement", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @RequiresPermission("contentImage:detail-supplement")
    public Response<List<CrowdfundingAttachmentVo>> detailSupplement(@RequestParam("infoUuid") String infoUuid) {
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(AdminErrorCode.CF_NOT_FOUND);
        }

        int caseId = crowdfundingInfo.getId();

        List<CrowdfundingAttachmentVo> fundingAttachmentWithRepeatInfo = repeatInfoBizService.getFundingAttachmentWithRepeatInfo(caseId);
        List<CrowdfundingAttachmentVo> repeatInfoSupplement = repeatInfoBizService.getFundingAttachmentWithRepeatInfoSupplement(caseId);
        fundingAttachmentWithRepeatInfo.addAll(repeatInfoSupplement);
        return NewResponseUtil.makeSuccess(fundingAttachmentWithRepeatInfo);
    }
}
