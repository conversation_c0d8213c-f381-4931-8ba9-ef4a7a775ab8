package com.shuidihuzhu.cf.admin.mq;

import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.admin.MQTagCons;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditOperationItem;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @Author: wangpeng
 * @Date: 2021/6/11 14:56
 * @Description:
 */
@Service
@RocketMQListener(id = MQTagCons.WORK_ORDER_END_CASE,
        group = "cf-admin-end-case" + MQTagCons.WORK_ORDER_END_CASE,
        tags = MQTagCons.WORK_ORDER_END_CASE,
        topic = MQTopicCons.CF)
@Slf4j
public class AdminCfInitialAuditEndCaseConsumer implements MessageListener<InitialAuditOperationItem.HandleCaseInfoParam > {

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<InitialAuditOperationItem.HandleCaseInfoParam> mqMessage) {
        log.info("adminCfInitialAuditEndCaseConsumer Receive WORK_ORDER_END_CASE message: {}", mqMessage);
        if (Objects.isNull(mqMessage) || Objects.isNull(mqMessage.getPayload())) {
            log.info("adminCfInitialAuditEndCaseConsumer WORK_ORDER_END_CASE 消息体为空");
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        InitialAuditOperationItem.HandleCaseInfoParam payload = mqMessage.getPayload();
        if (!Objects.equals(payload.getHandleType(), InitialAuditOperationItem.HandleTypeEnum.END_CASE.getCode())) {
            log.info("adminCfInitialAuditEndCaseConsumer WORK_ORDER_END_CASE 不是停止筹款");
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        long workOrderId = payload.getWorkOrderId();

        Response<WorkOrderVO> orderVOResponse = cfWorkOrderClient.getWorkOrderById(workOrderId);
        if (Objects.isNull(orderVOResponse) || orderVOResponse.notOk() || Objects.isNull(orderVOResponse.getData())) {
            log.info("adminCfInitialAuditEndCaseConsumer WORK_ORDER_END_CASE 工单数据为空");
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        WorkOrderVO data = orderVOResponse.getData();

        aiContentWorkOrder(data, payload);

        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private void aiContentWorkOrder(WorkOrderVO data, InitialAuditOperationItem.HandleCaseInfoParam handleCaseInfoParam) {
        if (!Objects.equals(data.getOrderType(), WorkOrderType.ai_content.getType())) {
            log.info("adminCfInitialAuditEndCaseConsumer aiContentWorkOrder is not content {}", handleCaseInfoParam);
            return;
        }
        int caseId = data.getCaseId();
        Response<WorkOrderVO> lastWorkOrder = cfWorkOrderClient.getLastWorkOrder(caseId, WorkOrderType.ai_photo.getType());
        if (Objects.isNull(lastWorkOrder) || lastWorkOrder.notOk() || Objects.isNull(lastWorkOrder.getData())) {
            log.info("adminCfInitialAuditEndCaseConsumer ai_photo is null {}", handleCaseInfoParam);
            return;
        }

        WorkOrderVO orderVO = lastWorkOrder.getData();
        if (Objects.equals(orderVO.getHandleResult(), HandleResultEnum.undoing.getType()) || Objects.equals(orderVO.getHandleResult(), HandleResultEnum.doing.getType()) ||
                Objects.equals(orderVO.getHandleResult(), HandleResultEnum.later_doing.getType())) {
            cfWorkOrderClient.closeOrderBycaseIdAndType(caseId, orderVO.getOrderType(), HandleResultEnum.exception_done.getType(), handleCaseInfoParam.getUserId(),"");

        }
    }
}
