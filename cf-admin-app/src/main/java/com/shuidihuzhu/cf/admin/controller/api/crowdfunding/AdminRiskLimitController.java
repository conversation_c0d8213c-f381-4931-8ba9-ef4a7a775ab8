package com.shuidihuzhu.cf.admin.controller.api.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.admin.util.excel.importfromexcel.service.ExcelReadService;
import com.shuidihuzhu.cf.delegate.saas.ExcelParamsVO;
import com.shuidihuzhu.cf.delegate.service.UserInfoServiceBiz;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.cf.delegate.saas.SeaAccountClientV1;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCaseVisitConfigBiz;
import com.shuidihuzhu.cf.delegate.crowdfunding.ICrowdfundingDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCfCaseVisitConfig;
import com.shuidihuzhu.client.cf.risk.client.CfRiskClient;
import com.shuidihuzhu.client.cf.risk.client.CfRiskOperateDetailClient;
import com.shuidihuzhu.client.cf.risk.client.CfRiskPlatformClient;
import com.shuidihuzhu.client.cf.risk.model.*;
import com.shuidihuzhu.client.cf.risk.model.enums.RiskOperateSourceEnum;
import com.shuidihuzhu.client.cf.risk.model.enums.UserOperationEnum;
import com.shuidihuzhu.client.cf.risk.model.result.UserOperationResponse;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2019/7/10 下午3:06
 * @desc
 */
@Slf4j
@RestController
@RequestMapping(path = "/admin/crowdfunding/risk")
public class AdminRiskLimitController {
    @Autowired
    private CfRiskPlatformClient cfRiskPlatformClient;

    @Autowired
    private CfRiskOperateDetailClient cfRiskOperateDetailClient;

    @Resource
    private SeaAccountClientV1 seaAccountClientV1;

    @Autowired
    private ICrowdfundingDelegate crowdfundingDelegate;

    @Resource
    private UserInfoServiceBiz userInfoServiceBiz;

    @Autowired
    private CfRiskClient cfRiskClient;

    @Autowired
    private AdminCaseVisitConfigBiz caseVisitConfigBiz;

    @Resource
    private ExcelReadService excelReadService;

    @ApiOperation(value = "查询风控操作动作集合")
    @RequestMapping(path = "/get-user-operations", method = RequestMethod.POST)
    @RequiresPermission("risk:get-user-operations")
    public Response<Map<String, String>> queryOperation(){

        UserOperationResponse opResult = cfRiskPlatformClient.getUserOperations();

        return ResponseUtil.makeSuccess(opResult.getContent());
    }

    @ApiOperation(value = "查询风控操作来源集合")
    @RequestMapping(path = "/get-operate-source", method = RequestMethod.POST)
    @RequiresPermission("risk:get-operate-source")
    public Response<Map<String, String>> queryOperationSource(){

        UserOperationResponse opResult = cfRiskPlatformClient.getUserOperationsSource();

        return ResponseUtil.makeSuccess(opResult.getContent());
    }


    @ApiOperation(value = "查询风控操作详情")
    @RequestMapping(path = "/get-operate-detail", method = RequestMethod.POST)
    @RequiresPermission("risk:get-operate-detail")
    public Response<List<CfRiskOperateDetail>> queryOperationDetail(@RequestParam(name = "riskUserId") long riskUserId,
                                                                    @RequestParam(name = "caseId") int caseId,
                                                                    @RequestParam(name = "opEnum") int opEnum){

        UserOperationEnum userOpEnum = UserOperationEnum.parse(opEnum);
        if((riskUserId <= 0 && caseId <= 0) || Objects.isNull(opEnum)){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        long adminUserId = ContextUtil.getAdminUserId();
        if(adminUserId <= 0){
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        Response validateResult = validate(riskUserId, caseId, userOpEnum);
        if(0 != validateResult.getCode()){
            return validateResult;
        }

        Response<List<CfRiskOperateDetail>> response = cfRiskOperateDetailClient.getOpeateDetail(riskUserId, caseId, userOpEnum);

        log.info("AdminRiskLimitController.queryOperationDetail userId:{},caseId:{},opEnum:{},result:{}", riskUserId,caseId,userOpEnum, JSON.toJSONString(response));

        return response;
    }

    private Response validate(long userId, int caseId, UserOperationEnum userOpEnum){

        if(userId > 0 && Objects.isNull(userInfoServiceBiz.getUserInfoByUserId(userId))){
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NOT_EXISTS);
        }

        if(caseId > 0 && Objects.isNull(crowdfundingDelegate.getCfInfoSimpleModelById(caseId))){
            return ResponseUtil.makeError(ErrorCode.CF_NOT_FOUND);
        }

        return ResponseUtil.makeSuccess(null);

    }

    @ApiOperation(value = "查询风控操作日志详情")
    @RequestMapping(path = "/get-operate-log", method = RequestMethod.POST)
    @RequiresPermission("risk:get-operate-log")
    public Response<List<CfRiskOperateLogDetail>> queryOperationLogDetail(@RequestParam(name = "riskUserId") long riskUserId,
                                                                          @RequestParam(name = "caseId") int caseId,
                                                                          @RequestParam(name = "opEnum") int opEnum,
                                                                          @RequestParam(name = "opSourceEnum") int opSourceEnum){

        UserOperationEnum userOpEnum = UserOperationEnum.parse(opEnum);
        RiskOperateSourceEnum operateSourceEnum = RiskOperateSourceEnum.parse(opSourceEnum);
        if((riskUserId <= 0 && caseId <= 0) || Objects.isNull(userOpEnum) || Objects.isNull(operateSourceEnum)){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        long adminUserId = ContextUtil.getAdminUserId();
        if(adminUserId <= 0){
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        Response validateResult = validate(riskUserId, caseId, userOpEnum);
        if(0 != validateResult.getCode()){
            return validateResult;
        }

        Response<List<CfRiskOperateLogDetail>> response = cfRiskOperateDetailClient.getOpeateLogDetail(riskUserId, caseId, userOpEnum, operateSourceEnum);

        log.info("AdminRiskLimitController.queryOperationLogDetail userId:{},caseId:{},opEnum:{},opSource:{},result:{}", riskUserId,caseId,userOpEnum,operateSourceEnum,JSON.toJSONString(response));

        return response;
    }

    @ApiOperation(value = "封禁、解禁")
    @RequestMapping(path = "/add-or-cancel", method = RequestMethod.POST)
    @RequiresPermission("risk:add-or-cancel")
    public Response<String> addOrCancelLimit(@RequestParam(name = "riskUserId") long riskUserId,
                                             @RequestParam(name = "caseId") int caseId,
                                             @RequestParam(name = "opEnum") int opEnum,
                                             @RequestParam(name = "action") boolean action,
                                             @RequestParam(name = "opSourceEnum") int opSourceEnum){

        return addCancelLimit(riskUserId, caseId, opEnum, action, opSourceEnum, null);
    }

    @ApiOperation(value = "封禁、解禁")
    @RequestMapping(path = "/add-or-cancel-ext", method = RequestMethod.POST)
    @RequiresPermission("risk:add-or-cancel-ext")
    public Response<String> addCancelLimit(@RequestParam(name = "riskUserId") long riskUserId,
                                           @RequestParam(name = "caseId") int caseId,
                                           @RequestParam(name = "opEnum") int opEnum,
                                           @RequestParam(name = "action") boolean action,
                                           @RequestParam(name = "opSourceEnum") int opSourceEnum,
                                           @RequestParam(name = "extInfo", required = false, defaultValue = "") String extInfo){

        UserOperationEnum userOpEnum = UserOperationEnum.parse(opEnum);
        RiskOperateSourceEnum operateSourceEnum = RiskOperateSourceEnum.parse(opSourceEnum);
        if((riskUserId <= 0 && caseId <= 0) || Objects.isNull(userOpEnum) || Objects.isNull(operateSourceEnum)){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        int adminUserId = ContextUtil.getAdminUserId();
        if(adminUserId <= 0){
            return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
        }

        Response validateResult = validate(riskUserId, caseId, userOpEnum);
        if(0 != validateResult.getCode()){
            return validateResult;
        }

        CfRiskOperateLimitExtParam extParam = JSON.parseObject(extInfo, new TypeReference<CfRiskOperateLimitExtParam>(){});//已检查过
        Response<Void> riskRpcResponse = caseVisitConfigBiz.judeRiskCase(caseId, adminUserId, action, riskUserId, userOpEnum, operateSourceEnum, extParam);
        /**
         *适配风控管理-异常案例处理的动作至visit-config表
         */
        List<UserOperationEnum> userOpEnums = Lists.newArrayList(UserOperationEnum.SHARE,UserOperationEnum.SHOW,UserOperationEnum.BANNER);
        if(operateSourceEnum == RiskOperateSourceEnum.RISK_QUERY && userOpEnums.contains(userOpEnum) && caseId > 0){
            AdminCfCaseVisitConfig visitConfig = caseVisitConfigBiz.get(caseId);

            if(userOpEnum == UserOperationEnum.BANNER){
                visitConfig.setShowBanner(!action);
            }

            if(userOpEnum == UserOperationEnum.SHARE){
                visitConfig.setSharable(action);
            }

            if(userOpEnum == UserOperationEnum.SHOW){
                visitConfig.setAbnormalHidden(!action);
                visitConfig.setAbnormalHiddenSelfTitle(Objects.nonNull(extParam) ? extParam.getShowSelfTitle() : "");
                visitConfig.setAbnormalHiddenOtherTitle(Objects.nonNull(extParam) ? extParam.getShowOtherTitle() : "");
            }
            caseVisitConfigBiz.update(visitConfig);
        }
        //TODO ###请勿改动、待删除####

        log.info("AdminRiskLimitController.addOrCancelLimit userId:{},caseId:{},opEnum:{},action:{},opSource:{},result:{}",riskUserId, caseId,userOpEnum,action,operateSourceEnum, JSON.toJSONString(riskRpcResponse));

        return ResponseUtil.makeSuccess("操作成功");
    }
    @ApiOperation(value = "批量封禁、解禁")
    @RequestMapping(path = "/add-or-cancel-ext-batch", method = RequestMethod.POST)
    @RequiresPermission("risk:add-or-cancel-ext")
    public Response<String> addCancelLimitBatch(MultipartFile file) throws Exception{
        if (file.isEmpty()){
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        String extInfo = "{\"showSelfTitle\":\"\",\"showOtherTitle\":\"\"}";
        List<ExcelParamsVO> excelParamsVOList = excelReadService.getExcelData(file);
        for(ExcelParamsVO excelParam : excelParamsVOList){
            long riskUserId = excelParam.getRiskUserId();
            int caseId = excelParam.getCaseId();
            int opEnum = excelParam.getOpEnum();
            boolean action = false;
            int opSourceEnum = excelParam.getOpSourceEnum();
            UserOperationEnum userOpEnum = UserOperationEnum.parse(opEnum);
            RiskOperateSourceEnum operateSourceEnum = RiskOperateSourceEnum.parse(opSourceEnum);
            if((riskUserId <= 0 && caseId <= 0) || Objects.isNull(userOpEnum) || Objects.isNull(operateSourceEnum)){
                return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
            }

            int adminUserId = ContextUtil.getAdminUserId();
            if(adminUserId <= 0){
                return ResponseUtil.makeError(ErrorCode.USER_ACCOUNT_NO_LOGIN);
            }

            Response validateResult = validate(riskUserId, caseId, userOpEnum);
            if(0 != validateResult.getCode()){
                return validateResult;
            }
            updateSql(riskUserId, caseId, opEnum, action, opSourceEnum, extInfo, adminUserId, userOpEnum, operateSourceEnum);
        }
        return ResponseUtil.makeSuccess("操作成功");
    }

    private void updateSql(long riskUserId, int caseId, int opEnum, boolean action, int opSourceEnum, String extInfo, int adminUserId, UserOperationEnum userOpEnum, RiskOperateSourceEnum operateSourceEnum){
        CfRiskOperateLimitExtParam extParam = JSON.parseObject(extInfo, new TypeReference<CfRiskOperateLimitExtParam>(){});//已检查过
        Response<Void> riskRpcResponse = caseVisitConfigBiz.judeRiskCase(caseId, adminUserId, action, riskUserId, userOpEnum, operateSourceEnum, extParam);
        /**
         *适配风控管理-异常案例处理的动作至visit-config表
         */
        List<UserOperationEnum> userOpEnums = Lists.newArrayList(UserOperationEnum.SHARE,UserOperationEnum.SHOW,UserOperationEnum.BANNER);
        if(operateSourceEnum == RiskOperateSourceEnum.RISK_QUERY && userOpEnums.contains(userOpEnum) && caseId > 0){
            AdminCfCaseVisitConfig visitConfig = caseVisitConfigBiz.get(caseId);

            if(userOpEnum == UserOperationEnum.BANNER){
                visitConfig.setShowBanner(!action);
            }

            if(userOpEnum == UserOperationEnum.SHARE){
                visitConfig.setSharable(action);
            }

            if(userOpEnum == UserOperationEnum.SHOW){
                visitConfig.setAbnormalHidden(!action);
                visitConfig.setAbnormalHiddenSelfTitle(Objects.nonNull(extParam) ? extParam.getShowSelfTitle() : "");
                visitConfig.setAbnormalHiddenOtherTitle(Objects.nonNull(extParam) ? extParam.getShowOtherTitle() : "");
            }
            caseVisitConfigBiz.update(visitConfig);
        }
        //TODO ###请勿改动、待删除####

        log.info("AdminRiskLimitController.addOrCancelLimit userId:{},caseId:{},opEnum:{},action:{},opSource:{},result:{}",riskUserId, caseId,userOpEnum,action,operateSourceEnum, JSON.toJSONString(riskRpcResponse));
    }
}
