package com.shuidihuzhu.cf.pr;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class PrDetailsPageCollocate {
    private long id;

    @ApiModelProperty("案例id")
    private int caseId;

    @ApiModelProperty("案例uuid")
    private String infoUuid;

    @ApiModelProperty("案例状态 0是筹款中,1是案例结束")
    private int caseEndType;

    @ApiModelProperty("品牌模式 0是普通模式,1是品牌模式")
    private int brandType;

    @ApiModelProperty("案例标题")
    private String caseTitle;

    @ApiModelProperty("求助人头像")
    private String handImgUrl;

    @ApiModelProperty("求助人姓名")
    private String helpName;

    @ApiModelProperty("求助人故事")
    private String helpStory;

    private Date createTime;

    private Date updateTime;

}
