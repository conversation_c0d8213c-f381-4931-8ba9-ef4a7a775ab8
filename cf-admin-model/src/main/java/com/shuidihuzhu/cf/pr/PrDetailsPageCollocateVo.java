package com.shuidihuzhu.cf.pr;

import com.shuidihuzhu.pf.common.v2.model.pagehelper.PaginationVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PrDetailsPageCollocateVo {

    @ApiModelProperty("每行显示信息")
    private List<PrDetailsPageCollocateVo.PrDetailsPageCollocateResult> prDetailsPageCollocateResultList;

    @ApiModelProperty("分页信息")
    private PaginationVO paginationVO;

    @ApiModel
    @Data
    public static class PrDetailsPageCollocateResult {
        private long id;

        @ApiModelProperty("案例id")
        private int caseId;

        @ApiModelProperty("案例uuid链接")
        private String infoUuidUrl;

        @ApiModelProperty("案例状态 0是筹款中,1是案例结束")
        private String caseEndType;

        @ApiModelProperty("品牌模式 0是普通模式,1是品牌模式")
        private int brandType;

        @ApiModelProperty("案例标题")
        private String caseTitle;

        @ApiModelProperty("案例添加时间")
        private String createTime;
    }

}
