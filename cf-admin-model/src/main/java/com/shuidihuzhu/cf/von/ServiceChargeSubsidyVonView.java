package com.shuidihuzhu.cf.von;

import com.shuidihuzhu.client.cf.admin.model.RefuseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/10/16 4:34 PM
 */
@Data
public class ServiceChargeSubsidyVonView {

    @ApiModelProperty("案例标题")
    private String title;

    @ApiModelProperty("案例uuid")
    private String caseUuid;

    @ApiModelProperty("案例归属渠道")
    private String channelStr;

    @ApiModelProperty("服务费收取比例")
    private String serviceFeePercentage;

    @ApiModelProperty("服务费收取上限")
    private String serviceFeeThreshold;

    @ApiModelProperty("申请发起时间")
    private String subsidySubmitTime;

    @ApiModelProperty("服务费补贴比例")
    private String serviceSubsidyPercentage;

    @ApiModelProperty("补贴后收取比例")
    private String serviceSubsidyAfterPercentage;

    @ApiModelProperty("补贴身份")
    private String serviceSubsidyIdentity;

    @ApiModelProperty("原因说明")
    private String subsidyReasonNote;

    @ApiModelProperty("原因说明：图片")
    private String subsidyReasonNoteImages;

    @ApiModelProperty("驳回原因")
    private String refuseModel;

}
