package com.shuidihuzhu.cf.model.content.vo;

import com.shuidihuzhu.cf.model.content.AdminSeoArticle;

import java.sql.Timestamp;
import java.util.List;

/**
 * Created by wangsf on 17/7/3.
 */
public class AdminSeoArticleVo {
	private int id;
	private String uuid;
	private String title;
	private String cover;
	private String body;
	private String keywords;
	private String description;
	private String pageUrl;
	private Timestamp time;
	private String author;
	private String source;

//	private List<SeoTagGroupVo> groups;
	private List<SeoArticleTagVo> tags;

	public AdminSeoArticleVo() {

	}

	public AdminSeoArticleVo(AdminSeoArticle seoArticle) {
		this.id = seoArticle.getId();
		this.uuid = seoArticle.getUuid();
		this.title = seoArticle.getTitle();
		this.cover = seoArticle.getCover();
		this.body = seoArticle.getBody();
		this.keywords = seoArticle.getKeywords();
		this.description = seoArticle.getDescription();
		this.pageUrl = seoArticle.getPageUrl();
		this.time = seoArticle.getTime();
		this.author = seoArticle.getAuthor();
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getUuid() {
		return uuid;
	}

	public void setUuid(String uuid) {
		this.uuid = uuid;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getCover() {
		return cover;
	}

	public void setCover(String cover) {
		this.cover = cover;
	}

	public String getBody() {
		return body;
	}

	public void setBody(String body) {
		this.body = body;
	}

	public String getKeywords() {
		return keywords;
	}

	public void setKeywords(String keywords) {
		this.keywords = keywords;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getPageUrl() {
		return pageUrl;
	}

	public void setPageUrl(String pageUrl) {
		this.pageUrl = pageUrl;
	}

	public Timestamp getTime() {
		return time;
	}

	public void setTime(Timestamp time) {
		this.time = time;
	}

	public String getAuthor() {
		return author;
	}

	public void setAuthor(String author) {
		this.author = author;
	}

//	public List<SeoTagGroupVo> getGroups() {
//		return groups;
//	}
//
//	public void setGroups(List<SeoTagGroupVo> groups) {
//		this.groups = groups;
//	}

	public List<SeoArticleTagVo> getTags() {
		return tags;
	}

	public void setTags(List<SeoArticleTagVo> tags) {
		this.tags = tags;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}
}
