package com.shuidihuzhu.cf.model.admin;

import org.apache.commons.lang.builder.ToStringBuilder;

import java.sql.Timestamp;

/**
 * Created by Ahrievil on 2017/10/19
 */
public class CfCallTrackingOperatorInfo {
    private int id;
    private int operatorId;
    private String position;
    private String name;
    private Timestamp createTime;
    private Timestamp updateTime;
    private int isDelete;

    public CfCallTrackingOperatorInfo() {
    }

    public CfCallTrackingOperatorInfo(int operatorId, String position) {
        this.operatorId = operatorId;
        this.position = position;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(int operatorId) {
        this.operatorId = operatorId;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
