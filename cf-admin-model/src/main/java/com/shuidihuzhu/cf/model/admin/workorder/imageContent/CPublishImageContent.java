package com.shuidihuzhu.cf.model.admin.workorder.imageContent;

import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CPublishImageContent {

    @ApiModelProperty(value = "公示中的文章")
    public String content;
    @ApiModelProperty(value = "公示中的标题")
    public String title;
    @ApiModelProperty(value = "存在未完成的图文工单")
    public int noFinishImageOrder;
    @ApiModelProperty(value = "存在未完成的图文工单")
    public List<CrowdfundingAttachmentVo> attachmentVos;

}
