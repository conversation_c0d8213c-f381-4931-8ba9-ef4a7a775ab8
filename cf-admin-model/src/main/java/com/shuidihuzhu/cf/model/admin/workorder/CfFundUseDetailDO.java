package com.shuidihuzhu.cf.model.admin.workorder;

import io.swagger.annotations.*;
import lombok.Data;
import lombok.Getter;

/**
 * @author: fengxuan
 * @create 2019-12-19 10:39
 **/
@ApiModel("用户提交资金用途明细")
@Data
public class CfFundUseDetailDO {

    private int id;

    @ApiModelProperty("案例id")
    private int caseId;

    /**
     * @see FundUseContentType
     */
    @ApiModelProperty("内容类型,0:描述；1:图片")
    private int contentType;

    @ApiModelProperty("用途描述或者图片地址")
    private String content;

    @ApiModelProperty("对应资金用途表的id")
    private int fundUseProgressId;

    @ApiModelProperty("票据金额")
    private int billMoney;

    /**
     * @see FundUseDetailAuditEnum
     */
    @ApiModelProperty("审核结果,详见FundUseDetailAuditEnum")
    private int auditResult;

    @ApiModelProperty("自定义审核结果")
    private String customAuditResult;

    @ApiModelProperty("有无自付,0：无自付，1：有自付")
    private int selfPayTag;

    //非数据库字段
    @ApiModelProperty("票据金额,元")
    private double billMoneyInYuan;

    @Getter
    public enum FundUseDetailAuditEnum {
        un_audit(0, "未验证"),
        pass(1, "验证通过"),
        picture_cannot_read(2, "图片不清楚"),
        not_patient_bill(3, "非患者票据"),
        other(4, "其他"),
        ;

        private int code;

        private String desc;

        FundUseDetailAuditEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static FundUseDetailAuditEnum getByCode(int code) {
            for (FundUseDetailAuditEnum value : FundUseDetailAuditEnum.values()) {
                if (code == value.getCode()) {
                    return value;
                }
            }
            return null;
        }
    }

    @Getter
    public enum FundUseContentType {
        comment(0, "描述"),
        image(1, "图片"),
        ;
        private int code;
        private String desc;

        FundUseContentType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static FundUseContentType getByCode(int code) {
            for (FundUseContentType value : FundUseContentType.values()) {
                if (value.getCode() == code) {
                    return value;
                }
            }
            return null;
        }
    }
}
