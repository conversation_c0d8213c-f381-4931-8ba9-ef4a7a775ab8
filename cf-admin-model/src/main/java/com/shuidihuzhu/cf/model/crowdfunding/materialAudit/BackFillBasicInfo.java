package com.shuidihuzhu.cf.model.crowdfunding.materialAudit;

import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 回填需要的基本信息
 * @Author: panghairui
 * @Date: 2023/12/8 10:47 AM
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BackFillBasicInfo {

    @ApiModelProperty("患者姓名")
    private String patientRealName;

    @ApiModelProperty("关系")
    private UserRelTypeEnum relationType;

    @ApiModelProperty("患者证件类型")
    private UserIdentityType patientIdType;

    @ApiModelProperty("患者证件号")
    private String patientIdCard;

    @ApiModelProperty("发起人真实姓名")
    private String raiserRealName;

    @ApiModelProperty("发起人证件类型")
    private String raiserIdType;

    @ApiModelProperty("发起人证件号")
    private String raiserIdCard;

    @ApiModelProperty("新增-患者证件类型，未避免原接口调用，兼容字段")
    private UserIdentityType patientIdTypeAuthor;

    @ApiModelProperty("新增-患者证件号，未避免原接口调用，兼容字段")
    private String patientIdCardAuthor;

    @ApiModelProperty("新增-患者真实姓名，未避免原接口调用，兼容字段")
    private String patientRealNameAuthor;

    @ApiModelProperty("新增-患者证件是否验证过，未避免原接口调用，兼容字段")
    private boolean patientIdCardVerifiedAuthor;

}
