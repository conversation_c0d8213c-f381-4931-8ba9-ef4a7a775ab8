package com.shuidihuzhu.cf.model.message;

import com.shuidihuzhu.wx.enums.AccountBizTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

@Data
public class WxArticleReadStat {

    /**
     * 数据的日期
     */
    private String refDate;

    /**
     * 数据的小时
     */
    private String refHour;

    /**
     * 用户来源：0:会话;1.好友;2.朋友圈;3.腾讯微博;4.历史消息页;5.其他;6.看一看;7.搜一搜
     */
    private int userSource;

    /**
     * 阅读人数
     */
    private int intPageReadUser;

    /**
     * 阅读次数
     */
    private int intPageReadCount;

    /**
     * 原文页（点击阅读）的阅读人数
     */
    private int oriPageReadUser;

    /**
     * 原文页的阅读次数
     */
    private int oriPageReadCount;

    /**
     * 分享的人数
     */
    private int sharUser;

    /**
     * 分享的次数
     */
    private int shareCount;

    /**
     * 收藏的人数
     */
    private int addToFavUser;

    /**
     * 收藏的次数
     */
    private int addToFavCount;

    /**
     * 公众号
     */
    private int thirdType;

    @Getter
    @AllArgsConstructor
    public enum UserSourceEnum {
        SESSION(0, "会话"),
        FRIEND(1, "好友"),
        FRIEND_CIRCLE(2, "朋友圈"),
        TX_WB(3, "腾讯微博"),
        HISTORY_PAGE(4, "历史消息页"),
        OTHER(5, "其他"),
        LOOK(6, "看一看"),
        SEARCH(7, "搜一搜");

        private int code;
        private String desc;

        public static UserSourceEnum valueOf(int code) {
            for (UserSourceEnum type : values()) {
                if (type.getCode() == code) {
                    return type;
                }
            }
            return SESSION;
        }
    }
}