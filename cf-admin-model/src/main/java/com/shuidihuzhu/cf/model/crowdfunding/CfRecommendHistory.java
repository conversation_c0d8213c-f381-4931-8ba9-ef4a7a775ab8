package com.shuidihuzhu.cf.model.crowdfunding;

import java.util.Date;

public class CfRecommendHistory {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 最新推荐id
     */
    private Integer recommendId;

    /**
     * 案例infoid
     */
    private String infoId;

    /**
     * 推荐类型
     */
    private Integer recommendType;

    /**
     * 是否删除：0 否，1 是
     */
    private Byte isDelete;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 主键id
     * @return id 主键id
     */
    public Long getId() {
        return id;
    }

    /**
     * 主键id
     * @param id 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 最新推荐id
     * @return recommend_id 最新推荐id
     */
    public Integer getRecommendId() {
        return recommendId;
    }

    /**
     * 最新推荐id
     * @param recommendId 最新推荐id
     */
    public void setRecommendId(Integer recommendId) {
        this.recommendId = recommendId;
    }

    /**
     * 案例infoid
     * @return info_id 案例infoid
     */
    public String getInfoId() {
        return infoId;
    }

    /**
     * 案例infoid
     * @param infoId 案例infoid
     */
    public void setInfoId(String infoId) {
        this.infoId = infoId == null ? null : infoId.trim();
    }

    /**
     * 推荐类型
     * @return recommend_type 推荐类型
     */
    public Integer getRecommendType() {
        return recommendType;
    }

    /**
     * 推荐类型
     * @param recommendType 推荐类型
     */
    public void setRecommendType(Integer recommendType) {
        this.recommendType = recommendType;
    }

    /**
     * 是否删除：0 否，1 是
     * @return is_delete 是否删除：0 否，1 是
     */
    public Byte getIsDelete() {
        return isDelete;
    }

    /**
     * 是否删除：0 否，1 是
     * @param isDelete 是否删除：0 否，1 是
     */
    public void setIsDelete(Byte isDelete) {
        this.isDelete = isDelete;
    }

    /**
     * 更新时间
     * @return update_time 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 更新时间
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 创建时间
     * @return create_time 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}