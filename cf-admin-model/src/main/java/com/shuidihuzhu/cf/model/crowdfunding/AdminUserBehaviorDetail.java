package com.shuidihuzhu.cf.model.crowdfunding;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.*;

/**
 * <AUTHOR>
 * @time 2019/10/11 下午5:06
 * @desc
 */
@Data
public class AdminUserBehaviorDetail {
    private Date time;
    /**
     * @see com.shuidihuzhu.cf.enums.crowdfunding.UserBehaviorEnum
     */
    private int behaviorType;

    private List<String> behavoir;

    private List<String> url = Lists.newArrayList();

    private UserInfoDetail userInfoDetail;

    private List<Object> subBehavoirDetails;

    private Map<String, Object> extInfo;

    private String cId;


    @Data
    public static class UserCaseProperty {

        @ApiModelProperty("发起案例记录")
        private int caseRaise;
        @ApiModelProperty("捐款记录")
        private int donateCount;
        @ApiModelProperty("捐款金额")
        private int donateAmount;
        @ApiModelProperty("证实记录")
        private int verifyCnt;
        @ApiModelProperty("加油记录")
        private int blessingCnt;
        @ApiModelProperty("总积分")
        private int score;
        @ApiModelProperty("爱心值")
        private int loveValue;

        @ApiModelProperty("是否可以转移 0 不可以 1 可以")
        private int canTransfer;
        @ApiModelProperty("canTransfer = 0 时提示语")
        private String msg;


        private String fromMobile;
        private String toMobile;

        private int userId;
    }
}
