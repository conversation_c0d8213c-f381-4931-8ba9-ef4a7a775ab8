package com.shuidihuzhu.cf.model.crowdfunding.vo.goods;

import com.shuidihuzhu.cf.vo.crowdfunding.AdminGoodsGear;

import java.util.List;

/**
 * Created by ahrievil on 2017/4/26.
 */
public class GoodsVo {
    private String infoUuid;
    private String name;
    private String mobile;
    private String title;
    private String intro;
    private Integer amount;
    private String amountInFen;
    private String story;
    private List<String> attachments;
    private String detail;
    private List<AdminGoodsGear> goodsGears;

    public String getInfoUuid() {
        return infoUuid;
    }

    public void setInfoUuid(String infoUuid) {
        this.infoUuid = infoUuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getIntro() {
        return intro;
    }

    public void setIntro(String intro) {
        this.intro = intro;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public String getStory() {
        return story;
    }

    public void setStory(String story) {
        this.story = story;
    }

    public List<String> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<String> attachments) {
        this.attachments = attachments;
    }

    public String getAmountInFen() {
        return amountInFen;
    }

    public void setAmountInFen(String amountInFen) {
        this.amountInFen = amountInFen;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public List<AdminGoodsGear> getGoodsGears() {
        return goodsGears;
    }

    public void setGoodsGears(List<AdminGoodsGear> goodsGears) {
        this.goodsGears = goodsGears;
    }
}
