package com.shuidihuzhu.cf.model.crowdfunding;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 *  2018/8/22
 */
@Data
@ApiModel("工单明细")
public class AdminReportDataVo {

    @ApiModelProperty("工单号")
    private long workId;
    @ApiModelProperty("案例id")
    private long caseId;
    @ApiModelProperty("案例标题")
    private String title;
    @ApiModelProperty("举报次数")
    private int reportCount;
    @ApiModelProperty("举报内容")
    private String content;
    @ApiModelProperty("图片")
    private String pic;
    @ApiModelProperty("举报时间")
    private Date time;
    @ApiModelProperty("状态")
    private int status;
    @ApiModelProperty("状态")
    private String statusStr;
    @ApiModelProperty("处理人id")
    private int operatorId;
    @ApiModelProperty("处理人名字")
    private String name = "";
    @ApiModelProperty("处理时间")
    private Date dealime;
    @ApiModelProperty("案例uuid")
    private String caseUuid;
    @ApiModelProperty("举报工单id")
    private long reportId;
    @ApiModelProperty("领取时间")
    private Date handleTime;
    @ApiModelProperty("创建时间")
    private Date createTime;

}
