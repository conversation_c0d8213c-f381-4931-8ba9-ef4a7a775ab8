package com.shuidihuzhu.cf.model.ai;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2024/5/29 2:21 PM
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AIGenerateParam {

    @ApiModelProperty("线索id")
    private Integer clewId;

    @ApiModelProperty("生成类型")
    private Integer generateType;

    @ApiModelProperty("大模型类型")
    private Integer modelType;

    @ApiModelProperty("唯一id")
    private String uuid;

    private Long operatorId;

    @ApiModelProperty("基础信息")
    private AiGenerateBaseInfo aiGenerateBaseInfo;

    @ApiModelProperty("暂用信息")
    private AiGenerateBaseInfo aiGenerateBaseInfoContext;

    private String context;

}
