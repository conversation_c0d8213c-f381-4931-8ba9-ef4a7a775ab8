package com.shuidihuzhu.cf.model.admin.workorder;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FriendsData {

    @ApiModelProperty("人数量")
    private int userCount = 1;

    @ApiModelProperty("总转发次数")
    private int shareCountAll;

    @ApiModelProperty("d0转发次数")
    private int shareCountD0;

    @ApiModelProperty("d0转发人数")
    private int shareUserCountD0;

    @ApiModelProperty("d1转发次数")
    private int shareCountD1;

    @ApiModelProperty("d1转发人数")
    private int shareUserCountD1;

    @ApiModelProperty("d2转发次数")
    private int shareCountD2;

    @ApiModelProperty("d2转发人数")
    private int shareUserCountD2;

    @ApiModelProperty("d3转发次数")
    private int shareCountD3;

    @ApiModelProperty("d3转发人数")
    private int shareUserCountD3;

    @ApiModelProperty("总证实次数")
    private int verificationCountAll;

    @ApiModelProperty("d0证实次数")
    private int verificationCountD0;

    @ApiModelProperty("d1证实次数")
    private int verificationCountD1;

    @ApiModelProperty("d2证实次数")
    private int verificationCountD2;

    @ApiModelProperty("d3证实次数")
    private int verificationCountD3;
}
