package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport;
import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * Created by Ahrievil on 2017/8/15
 */
@Data
public class CfReportView extends CrowdfundingReport {
    private String operator;

    private long workId;

    //举报标签
    private List<String> reportTypes;

    private int reportWorkOrderHandleResult;
    //昵称
    private String nickName;

    //举报人画像描述
    private Set<Integer> reporterImageSet;

    //手机号
    private String contact;
    private NumberMaskVo contactMask;
    //举报次数
    private int reportNums;

    //举报新逻辑的处理人
    private String newOperator;

    //风险标签
    private String riskLabel;

    /**
     * 命中策略名称
     */
    private List<String> hitStrategy;

    /**
     * 修改后举报类型
     */
    private List<String> modifyReportTypes;

    /**
     * 修改后举报类型
     */
    private List<Integer> modifyReportCodeTypes;
    /**
     * 修改后举报详情（举报类型为其他时）
     */
    private List<String> modifyReportComments;

    // 在职顾问身份证
    private NumberMaskVo suspectVolunteerIdentity;
    // 在职顾问角色类型
    private int suspectVolunteerLevel;
    // 在职顾问角色类型描述
    private String suspectVolunteerRoleDesc;
    // 在职顾问电话
    private NumberMaskVo suspectVolunteerMobile;

    // 离职顾问身份证
    private NumberMaskVo invalidVolunteerIdentity;
    // 离职顾问电话
    private NumberMaskVo invalidVolunteerMobile;
    // 离职顾问姓名
    private String invalidVolunteerName = "";

    private NumberMaskVo identifyMask;
}
