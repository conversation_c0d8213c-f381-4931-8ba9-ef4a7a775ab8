package com.shuidihuzhu.cf.model.ai;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description:
 * @Author: pan<PERSON><PERSON><PERSON>
 * @Date: 2025/2/20 19:15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StreamChatGptRequest {

    private String appCode;
    private RequestBody requestBody;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RequestBody {
        private String model;
        private List<Message> messages;
        private Boolean stream;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Message {
        private String role;
        private String content;
    }

}
