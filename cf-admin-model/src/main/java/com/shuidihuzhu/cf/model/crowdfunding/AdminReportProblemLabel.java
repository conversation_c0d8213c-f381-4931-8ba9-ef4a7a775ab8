package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

/**
 * <AUTHOR>
 * @time 2019/12/12 下午2:13
 * @desc
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AdminReportProblemLabel {
    /**
     * 对应cf_report_problem表的id、labelId、problem、problem_type、answer_type、direct_show、unknow_tag、must_answer
     */
    @NonNull
    private Integer prefixId;
    @NonNull
    private Integer prefixLabelId;
    @NonNull
    private String prefixProblem;
    @NonNull
    private Integer prefixProblemType;
    @NonNull
    private Integer prefixAnswerType;
    @NonNull
    private Integer prefixDirectShow;
    @NonNull
    private Integer unknowTag;
    @NonNull
    private Integer mustAnswer;
    /**
     * 问题的答案
     */
    @NonNull
    private String answer;

    private int isNeedVerify;

    private String answerId;
}
