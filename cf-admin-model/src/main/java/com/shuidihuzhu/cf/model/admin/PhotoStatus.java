package com.shuidihuzhu.cf.model.admin;

import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @package: com.shuidihuzhu.cf.biz.aiphoto.aiphoto
 * @Author: liujiawei
 * @Date: 2018/7/26  20:39
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class PhotoStatus {
    Integer status;
    String description;
    Integer artificialRes;
    String name;
    String idNumber;
    String idCardNumber;
    NumberMaskVo idNumberMask;
}
