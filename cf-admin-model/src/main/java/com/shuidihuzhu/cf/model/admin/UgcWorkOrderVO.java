package com.shuidihuzhu.cf.model.admin;

import com.shuidihuzhu.cf.vo.crowdfunding.CfVerificationVo;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2019/10/10
 */
@Data
@ApiModel("ugc工单")
public class UgcWorkOrderVO extends WorkOrderVO {

    private long userId;

    private String content;

    private String contentTypeStr;

    private String sensitiveWord;

    private String attachmentUrls;

    private List<Integer> watermarks;

    private String supplyOrgName;

    private List<String> supplyReasons;

    private String reasonDesc;

    /**
     * 是否为医护身份 0 非医护  1医护
     */
    private int isMedical;

    /**
     * 证实人上传图片列表
     */
    private String medicalImageList;

    private String provinceName;

    private String hospitalName;

    /**
     * 证实结果图片url
     */
    private String medicalVerifyResultImageUrl;

    private String verifyName;

    /***
     * 有效投诉个数
     */
    private int complaintInfoSize;

    private List<CfVerificationVo.ComplaintInfo> complaintInfoList;

    @Data
    public static class ComplaintInfo{

        /**
         * 投诉时间
         */
        private String complaintTime;

        /**
         * 投诉人id
         */
        private Long complaintUserId;

        /**
         * 投诉类型
         */
        private String complaintMsg;
    }

}
