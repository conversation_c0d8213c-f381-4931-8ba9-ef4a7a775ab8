package com.shuidihuzhu.cf.model.crowdfunding.caseRepeat;

import lombok.Data;
import lombok.Getter;

import java.util.Date;

@Data
public class AdminCfRepeatFinishInfo {
    long id;
    int caseId;
    int finishCaseId;
    String repeatBaseInfo;
    int finishType;
    int finishId;
    Date createTime;
    Date updateTime;

    @Getter
    public enum FinishCasePeriod {
         INITIAL_AUDIT_PASS_REPEAT(0, "初审审核-通过"),
         INITIAL_AUDIT_PAST_DUE(1, "初审重复判断-结束过期预审中的案例"),
         ;

         private int code;
         private String desc;

         FinishCasePeriod(int code, String desc) {
             this.code = code;
             this.desc = desc;
         }
     }
}
