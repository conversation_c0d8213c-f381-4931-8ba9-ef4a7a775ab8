package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.sql.Timestamp;
import java.util.Set;

/**
 * Created by Ahrievil on 2017/8/17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AdminCrowdfundingView extends CrowdfundingInfo {
    private String applicantMobile;
    private NumberMaskVo applicantMobileMask;
    private String authorName;
    private String lastComment;
    private boolean hasApplyMultiTimeFakeDiscoveryClientHealthIndicator;
    private boolean finished;
    private boolean hasSensitiveWord;
    private Set<String> sensitiveWords;
    private Integer operation;
    private Integer isReport;
    private Integer shareCount;
    private Integer verificationCount;
    private Timestamp auditCommitTime;
    private Integer isContact;
    @ApiModelProperty("材料审核拒绝次数")
    private Integer refuseCount;
    @ApiModelProperty("审核拒绝总次数")
    private Integer allRefuseCount;
    @ApiModelProperty("用户主动申请的驳回次数")
    private Integer userRefuseCount;
    private Integer callCount;
    private Integer callStatus;
    private String operator;
    private int characterCount;
    private int imgageCount;

    private boolean isCaseRepeat;
    private boolean isSecondRaise;

    private NumberMaskVo payeeMobileMask;

    public boolean getIsCaseRepeat() {
        return isCaseRepeat;
    }

    public void setIsCaseRepeat(boolean caseRepeat) {
        isCaseRepeat = caseRepeat;
    }

    public boolean getIsSecondRaise() {
        return isSecondRaise;
    }

    public void setIsSecondRaise(boolean secondRaise) {
        isSecondRaise = secondRaise;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
