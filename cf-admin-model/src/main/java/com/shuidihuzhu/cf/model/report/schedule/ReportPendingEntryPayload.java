package com.shuidihuzhu.cf.model.report.schedule;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ReportPendingEntryPayload {

    //subId
    private long subId;

    private int credibleType;

    private String time;

}
