package com.shuidihuzhu.cf.model.risk;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BanShareWhiteListDO  {
    private long id;
    private long userId;
    //操作人姓名
    private String operatorName;
    //失效时间
    private Date validTime;
    //阈值
    private int maxCount;
    //状态（0有效，1失效）
    private int validState;
    //添加原因
    private String operateReason;

    private Date createTime;

}