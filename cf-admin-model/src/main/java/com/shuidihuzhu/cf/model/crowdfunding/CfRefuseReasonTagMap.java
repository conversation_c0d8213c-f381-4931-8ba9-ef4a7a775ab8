package com.shuidihuzhu.cf.model.crowdfunding;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;

/**
 * Created by Ahrievil on 2017/7/27
 */
public class CfRefuseReasonTagMap {
    private int id;
    private int tagId;
    private String reasonIds;
    private int isdelete;
    private List<Integer> reasonList;
    private List<CfRefuseReasonEntity> entityList;

    public CfRefuseReasonTagMap() {
    }

    public CfRefuseReasonTagMap(int tagId, String reasonIds) {
        this.tagId = tagId;
        this.reasonIds = reasonIds;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getTagId() {
        return tagId;
    }

    public void setTagId(int tagId) {
        this.tagId = tagId;
    }

    public String getReasonIds() {
        return reasonIds;
    }

    public void setReasonIds(String reasonIds) {
        this.reasonIds = reasonIds;
    }

    public int getIsdelete() {
        return isdelete;
    }

    public void setIsdelete(int isdelete) {
        this.isdelete = isdelete;
    }

    public List<Integer> getList() {
        if (StringUtils.isBlank(reasonIds))
            return Collections.emptyList();
        return Lists.transform(Splitter.on(",").splitToList(reasonIds), Integer::parseInt);
    }

    public List<CfRefuseReasonEntity> getEntityList() {
        return entityList;
    }

    public void setEntityList(List<CfRefuseReasonEntity> entityList) {
        this.entityList = entityList;
    }
}
