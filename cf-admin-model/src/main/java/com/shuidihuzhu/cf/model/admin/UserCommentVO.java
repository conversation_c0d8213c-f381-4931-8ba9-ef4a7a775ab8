package com.shuidihuzhu.cf.model.admin;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2018/7/31
 */
@Data
public class UserCommentVO {


    @ApiModelProperty("案例id")
    private long caseId;

    @ApiModelProperty("案例来源")
    private int commentSource;

    @ApiModelProperty("备注来源内的分类")
    private int commentType;

    @ApiModelProperty("备注来源内的分类")
    private String commentTypeStr;

    @ApiModelProperty("操作人")
    private String operatorName;

    @ApiModelProperty("备注信息")
    private String comment;

    @ApiModelProperty("操作方式")
    private String operateMode;

    @ApiModelProperty("创建时间")
    private String createTime;

    @ApiModelProperty("说明")
    private String operateDesc;

    private long workOrderId;


    public UserCommentVO(UserComment userComment) {
        this.caseId = userComment.getCaseId();
        this.commentSource = userComment.getCommentSource();
        this.commentType = userComment.getCommentType();
        this.comment = userComment.getComment();
        this.operateMode = userComment.getOperateMode();
        this.operateDesc = userComment.getOperateDesc();
        this.workOrderId = userComment.getWorkOrderId();
    }

    public UserCommentVO() {
    }
}
