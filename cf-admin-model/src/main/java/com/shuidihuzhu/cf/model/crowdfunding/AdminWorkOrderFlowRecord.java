package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.AdminWorkOrderFlowConst;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.sql.Timestamp;
import java.util.Date;

/**
 * Created by Ahrievil on 2017/12/25
 */
@Data
public class AdminWorkOrderFlowRecord {
    private long id;
    private long flowId;
    private long workOrderId;
    private int problemType;
    private int currentProblemType;
    private int orderOperatorId;
    private String problemContent;
    private String problemImg;
    private String mobile;
    private int caseId;
    private int operatorId;
    private String comment;
    private int operateType;
    private int level;
    private long secondClassifyId;
    private Date createTime;
    private Date updateTime;
    private String encryptMobile;


    public AdminWorkOrderFlowRecord() {
    }

    public AdminWorkOrderFlowRecord(AdminWorkOrderFlow adminWorkOrderFlow, int operatorId,
                                    String comment, int operateType, int level, long secondClassifyId) {
        BeanUtils.copyProperties(adminWorkOrderFlow, this);
        this.id = 0;
        this.flowId = adminWorkOrderFlow.getId();
        this.operatorId = operatorId;
        this.comment = StringUtils.defaultString(comment);
        this.operateType = operateType;
        this.level = level;
        this.secondClassifyId = secondClassifyId;

        if (operateType == AdminWorkOrderFlowConst.OperateTypeEnum.CREATE_WORK_FLOW.getCode()) {
            currentProblemType = problemType;
        } else if (operateType == AdminWorkOrderFlowConst.OperateTypeEnum.ASSIGN_WORK_FLOW.getCode()) {
            currentProblemType = problemType;
            orderOperatorId = operatorId;
        }
    }


}
