package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import java.util.Date;
import java.util.List;
import java.util.Set;

public class AdminWorkOrderFlowParam {


    // 信息流转工单 查询页面入口参数
    @Data
    public static class SearchParam {

        String flowIdStr;
        int flowId;

        Long caseId;
        String mobile;
        String encryptMobile;
        Long firstClassifyId;
        Long secondClassifyId;
        /**
         * 新版问题分类 一级分类
         */
        Integer newFirstClassifyId;
        /**
         * 新版问题分类 二级分类
         */
        Integer newSecondClassifyId;
        /**
         * 新版问题分类 三级分类
         */
        Integer newThirdClassifyId;

        Integer workOrderStatus;

        Long level;
        Date createStartTime;
        Date createEndTime;

        Date updateStartTime;
        Date updateEndTime;

        Integer creatorId;

        private List<Long> secondClassifyIds;
        private Set<Long> workOrderIds;
        private List<Integer> workOrderStatusList;
        // 处理人
        private Integer operatorId;
        private Integer roleId;
        private List<Integer> orgIdList;

        // flow_record 表
        private Integer recordOperatorId;
        private List<Integer> recordOperatorTypes;

        int pageSize;
        int pageNum;

        int limitSize;
        int offset;

        Integer followTag;

        Date createTime;

        @Override
        public String toString() {
            return JSON.toJSONString(this);
        }
    }

    // 信息流转工单 处理参数
    @Data
    public static class HandleParam {

        private long workOrderId;
        private int handleType;
        private String comment;
        private String handleImg;
        private int roleId;
        private int operatorId;
        private int userId;
        private int level;
        private int secondClassifyId;
        /**
         * 新版问题分类 一级分类
         */
        private int newFirstClassifyId;
        /**
         * 新版问题分类 二级分类
         */
        private int newSecondClassifyId;
        /**
         * 新版问题分类 三级分类
         */
        private int newThirdClassifyId;
        private List<Integer> followTagIds;

        private int cityId;
        private String cityName;

        private int provinceId;
        private String provinceName;

        private int countyId;
        private String countyName;

        private String hospital;

        private int hospitalId;

    }
}
