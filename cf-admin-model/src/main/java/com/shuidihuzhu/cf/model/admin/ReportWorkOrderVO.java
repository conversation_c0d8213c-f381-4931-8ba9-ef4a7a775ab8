package com.shuidihuzhu.cf.model.admin;

import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoStatusEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfReportOfficialLetterStatusEnum;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @time 2019/12/31 下午3:12
 * @desc
 */
@Data
@ApiModel("举报工单工单")
public class ReportWorkOrderVO extends WorkOrderVO {

    /**
     * '状态: 0-审批中, 1-被驳回，2-筹款中，3-已结束', 4-已提交,
     */
    @ApiModelProperty("案例审核状态")
    private int auditStatus;

    /**
     * public enum ApplyStatus {
     * 		EMPTY_VALUE(-1, ""),
     * 		UNSUBMIT(0, "未提交"),
     * 		SUBMIT_APPROVE_PENDING(1, "未审核"),
     * 		APPROVE_SUCCESS(2, "审核成功"),
     * 		APPROVE_REJECT(3, "审核驳回"),
     *      APPROVE_CLOSE(4,"撤销申请和关闭");
     * }
     */
    @ApiModelProperty("申请提现的状态")
    private int applyStatus;

    /**
     * public enum DrawStatus {
     * 		EMPTY_VALUE(-2),
     * 		UNBUILD(-1),
     * 		UNHANDLE(0),
     * 		HANDLING(1),
     * 		HANDLE_SUCCESS(2),
     * 		HANDLE_FAILED(3),
     * 		HANDLE_PARTIAL_SUCCESS(4),
     * 		MANUAL_SUCCESS(5);
     * 	}
     */
    @ApiModelProperty("提现状态")
    private int cashStatus;

    @ApiModelProperty("案例的重复情况")
    //参考/admin/cf/work/order/report/report-select的repeatStatusList
    private Set<Integer> repeatStatusList;

    @ApiModelProperty("是否有新增举报 0:遗留 1:新增")
    private int isNewReport;

    @ApiModelProperty("是否有新增举报")
    private boolean realNameReport;

    @ApiModelProperty("被举报次数")
    private int reportNumber;

    @ApiModelProperty("是否失联 true:失联")
    private boolean lostStatus;

    @ApiModelProperty("跟进人")
    private String operator;

    @ApiModelProperty("首次举报时间")
    private Date firstReportTime;

    @ApiModelProperty("最新举报时间")
    private Date lastReportTime;

    @ApiModelProperty("举报标签")
    private String reportLabels;

    @ApiModelProperty("是否有高危案例 true:高危")
    private boolean highRisk;

    @ApiModelProperty("最后一次操作时间")
    private Date lastApproveTime;

    @ApiModelProperty("最后一次操作备注")
    private String lastApproveComment;

    /**
     * @see CrowdfundingInfoStatusEnum
     */
    @ApiModelProperty("补充证明状态")
    private int supplyAuditStatus;

    /**
     * @see CfReportOfficialLetterStatusEnum
     */
    @ApiModelProperty("公函状态")
    private List<Integer> letterStatus;

    /**
     * @see com.shuidihuzhu.cf.enums.crowdfunding.AddTrustAuditStatusEnum
     */
    @ApiModelProperty("补充证明待录入状态")
    private int proveStatus;

    @ApiModelProperty("举报跟进提示")
    private String followTip;

    @ApiModelProperty("下次跟进时间")
    private Date scheduleTime;

    @ApiModelProperty("举报组最近一次备注")
    private String lastReportRemark;

    @ApiModelProperty("举报组标记的打款方式 0为未标记")
    private int payMethod;

    @ApiModelProperty("患者姓名")
    private String patientName;
    @ApiModelProperty("资金用途描述")
    private String fundUseContent;
    @ApiModelProperty("资金用途图片")
    private String fundUseAttachmentUrls;


}
