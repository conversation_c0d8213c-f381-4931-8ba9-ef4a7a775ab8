package com.shuidihuzhu.cf.model.CrmUserManage;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.shuidihuzhu.cf.vo.approve.ApproveLifeCircleVO;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewBaseInfoDO;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewtrackHistoryMarkModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;

import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class UserManage {

    public static String LOCK_NAME_UPDATE_ROLE = "LOCK_NAME_UPDATE_ROLE_%d";
    public static String PERSON_ID_PREFIX = "person_id_";
    public static String UUID_ID_PREFIX = "uuid_id_";


    public static class UuidDimensionData {

    }


    public static class PersonIdDimensionData {

    }


    public static class MayUserFulUserData {


        @ApiModelProperty("企业微信外部联系人ID")
        private String wxOutId;
        @ApiModelProperty("ip解析的省市")
        private String ipProvince;

        @ApiModelProperty("peasonId")
        private String personId;
        @ApiModelProperty("用户角色")
        private List<UserRoleModel> userRoleModels;



        @ApiModelProperty("最近一次在线咨询问题类型")
        private List<RaiseCaseData> caseList;

        @ApiModelProperty("捐款次数（含退款")
        private int donateCount;
        @ApiModelProperty("捐款累计金额")
        private long donateAmount;
        @ApiModelProperty("转发次数")
        private int shareCount;
        @ApiModelProperty("举报次数")
        private int reportCount;
        @ApiModelProperty("证实次数")
        private int verifyCount;

        @ApiModelProperty("在线咨询次数")
        private int totalConsultSize;
        @ApiModelProperty("最近一次在线咨询时间")
        private long lastConsultTime;
        @ApiModelProperty("最近一次在线咨询问题类型")
        private String lastConsultType;

        @ApiModelProperty("呼入次数")
        private int callSize;
        @ApiModelProperty("最近一次呼入时间")
        private long lastCallTime;
        @ApiModelProperty("最近一次呼入咨询问题类型")
        private int lasCallType;
        @ApiModelProperty("线索手机号")
        private String clewPhone;
        @ApiModelProperty("线索登记次数")
        private int registerClewSize;
        @ApiModelProperty("最近一次线索登记时间")
        private long lastRegisterClewTime;
        @ApiModelProperty("最近一次线索的类型")
        private long lastClewType;

        @ApiModelProperty("状态")
        private int caseStatus;

    }

    @Data
    @ApiModel
    public static class UserData {

        @ApiModelProperty("手机号")
        private String mobile;

        @ApiModelProperty("发起案例相关数据")
        private RaiseBaseData raiseBaseData;

        @ApiModelProperty("用户案例生命周期")
        private List<RaiseCaseData> caseList;

        @ApiModelProperty("用户行为记录")
        private DonorData donorData;

        @ApiModelProperty("外呼相关数据")
        private OutCallClewData clewData;

        @ApiModelProperty("在线咨询相关数据")
        private UserManage.UdeskSessionVo udeskSessionVo;

        @ApiModelProperty("线索相关信息")
        private UserClewData clewDetail;

        @ApiModelProperty("30天内有效案例数")
        private long validCaseCount;

        @ApiModelProperty("案例记录信息")
        private List<CaseInfo> caseInfoList;
    }

    @Data
    @ApiModel("线索记录相关")
    public static class UserClewData {
        @ApiModelProperty("30天内的登记线索,只展示最近3条")
        private List<ClewRecord> thirtyDaysClewRecords;

        @ApiModelProperty("30天内的登记线索数")
        private int thirtyDaysClewCount;

        @ApiModelProperty("当前线索登记记录")
        private ClewRecord currentClewRecord;

        public static UserClewData convertFromClewMarkModel(CfClewtrackHistoryMarkModel markModel, long clewId, CfClewBaseInfoDO cfClewBaseInfoDO) {
            if (markModel == null) {
                return null;
            }
            List<CfClewtrackHistoryMarkModel.SimplyClewRecord> simplyClewRecordList = markModel.getSimplyClewRecordList();
            UserClewData clewData = new UserClewData();
            if (CollectionUtils.isNotEmpty(simplyClewRecordList)) {
                List<ClewRecord> clewRecords = simplyClewRecordList.stream().map(ClewRecord::convertBySimplyClewRecord)
                        .limit(3)
                        .collect(Collectors.toList());
                clewData.setThirtyDaysClewRecords(clewRecords);
                clewData.setThirtyDaysClewCount(simplyClewRecordList.size());
            }
            if (cfClewBaseInfoDO != null) {
                ClewRecord clewRecord = new ClewRecord();
                clewRecord.setClewId(cfClewBaseInfoDO.getId());
                clewRecord.setChannel(cfClewBaseInfoDO.getPrimaryChannel());
                clewRecord.setRegisterTime(cfClewBaseInfoDO.getCreateTime());
                clewData.setCurrentClewRecord(clewRecord);
            }
            return clewData;
        }
    }

    @ApiModel
    @Data
    public static class ClewRecord {

        @ApiModelProperty("线索id")
        private long clewId;

        @ApiModelProperty("渠道")
        private String channel;

        @ApiModelProperty("登记时间")
        private Date registerTime;

        public static ClewRecord convertBySimplyClewRecord(CfClewtrackHistoryMarkModel.SimplyClewRecord simplyClewRecord) {
            if (simplyClewRecord == null) {
                return null;
            }
            ClewRecord clewRecord = new ClewRecord();
            clewRecord.setClewId(simplyClewRecord.getClewId());
            clewRecord.setChannel(simplyClewRecord.getPrimaryChannel());
            if (StringUtils.isNotBlank(simplyClewRecord.getRegisterTime())) {
                clewRecord.setRegisterTime(new DateTime(simplyClewRecord.getRegisterTime()).toDate());
            }
            return clewRecord;
        }
    }

    @ApiModel
    @Data
    public static class CaseInfo {

        @ApiModelProperty("案例id")
        private int caseId;

        @ApiModelProperty("案例标题")
        private String title;

        @ApiModelProperty("初审状态")
        private int firstApproveStatus;

        @ApiModelProperty("目标金额")
        private int targetAmount;

        @ApiModelProperty("已筹金额")
        private int amount;

        @ApiModelProperty("案例状态")
        private int caseStatus;

        @ApiModelProperty("案例发起时间")
        private Date caseRaiseTime;

        @ApiModelProperty("infoUuid")
        private String infoUuid;

        @ApiModelProperty("true:有效案例,false:无效案例")
        private boolean validCaseStatus;
    }


    @Data
    @ApiModel
    public static class OutCallClewData {
        @ApiModelProperty("呼入次数")
        private int callSize;
        @ApiModelProperty("最近一次呼入时间")
        private long lastCallTime;
        @ApiModelProperty("最近一次呼入咨询问题类型")
        private int lasCallType;

        @ApiModelProperty("线索手机号")
        private String clewPhone;
        @ApiModelProperty("线索登记次数")
        private int registerClewSize;
        @ApiModelProperty("最近一次线索登记时间")
        private long lastRegisterClewTime;
        @ApiModelProperty("最近一次线索的类型")
        private Integer lastClewType;

        @ApiModelProperty("状态")
        private int caseStatus;

        @ApiModelProperty("ip解析的省")
        private String ipProvince;
        @ApiModelProperty("ip解析的城市")
        private String ipCity;


        public static OutCallClewData convertFromClewMarkModel(CfClewtrackHistoryMarkModel
                                                               markModel) {

            if (markModel == null) {
                return null;
            }

            OutCallClewData outCallData = new OutCallClewData();
            outCallData.setCallSize(markModel.getInCallCount());
            outCallData.setLastCallTime(markModel.getLatestInCallTime() != null ?
                    markModel.getLatestInCallTime().getTime() : 0);
            outCallData.setLasCallType(markModel.getLatestInCallAskType());

            outCallData.setClewPhone(markModel.getPhone());
            outCallData.setRegisterClewSize(markModel.getRegisterCount());

            outCallData.setLastRegisterClewTime(markModel.getLatestRegisterTime() != null
            ? markModel.getLatestRegisterTime().getTime() : 0);
            outCallData.setLastClewType(markModel.getSourceType());
            outCallData.setCaseStatus(markModel.getCaseStatus());
            outCallData.setIpProvince(markModel.getProvince());
            outCallData.setIpCity(markModel.getCity());

            return outCallData;
        }

    }

    @Data
    @ApiModel
    public static class RaiseBaseData {

        @ApiModelProperty("手机号")
        private String mobile;
        @ApiModelProperty("手机归属地")
        private String mobileBelong;
        @ApiModelProperty("用户id")
        private Long userId;
        @ApiModelProperty("微信昵称")
        private String nickName;
        @ApiModelProperty("身份证")
        private String idCard;
        @ApiModelProperty("姓名")
        private String userName;
        @ApiModelProperty("身份证归属地")
        private String province;
        @ApiModelProperty("openId")
        private Set<String> openIds;
        @ApiModelProperty("unionId")
        private Set<String> unionIds;

        @ApiModelProperty("企业微信外部联系人ID")
        private String wxOutId;


        @ApiModelProperty("peasonId")
        private String personId;
        @ApiModelProperty("用户角色")
        private List<UserRoleModel> userRoleModels;
    }

    @Data
    @ApiModel
    public static class RaiseCaseData {
        private int caseId;
        @ApiModelProperty("案例生命周期阶段")
        private List<ApproveLifeCircleVO> lifeCircleList;
        @ApiModelProperty("案例获捐金额")
        private int amount;
    }
    
    @Data
    @ApiModel("用户C端的轨迹")
    public static class DonorData {
        @ApiModelProperty("是否关注公众号")
        private boolean subscribe;
        @ApiModelProperty("最近关注时间")
        private Date subscribeTime;
        @ApiModelProperty("捐款次数（含退款")
        private int donateCount;
        @ApiModelProperty("捐款累计金额")
        private long donateAmount;
        @ApiModelProperty("转发次数")
        private int shareCount;
        @ApiModelProperty("举报次数")
        private int reportCount;
        @ApiModelProperty("证实次数")
        private int verifyCount;
    }

    @Data
    @ApiModel
    public static class UserAccount {

        private long id;
        @ApiModelProperty("合并后的uuid")
        private String uuid;
        @ApiModelProperty("personId 用户的唯一标示")
        private String personId;
        @ApiModelProperty("加密的手机号")
        private String cryptoMobile;
        @ApiModelProperty("加密身份证")
        private String cryptoIdCard;
        @ApiModelProperty("身份证明文")
        private String idCard;
        @ApiModelProperty("姓名")
        private String userName;
        @ApiModelProperty("发起案例的意愿")
        private int raiseCaseQuality;
    }

    @Data
    public static class UserManageLog {

        private String cryptoMobile;
        private String personId;
        private String uuid;
        private String cryptoIdCard;
        private String userName;

        private String version;

        private int operateSource;
        private long operateId;
        private String operateComment;

        private String extComment;

        public static UserManageLog buildRecordFromAccount(UserAccount account) {

            if (account == null) {
                return null;
            }
            UserManageLog record = new UserManageLog();

            record.setCryptoMobile(account.getCryptoMobile());

            record.setPersonId(StringUtils.trimToEmpty(account.getPersonId()));
            record.setUuid(StringUtils.trimToEmpty(account.getUuid()));

            record.setCryptoIdCard(StringUtils.trimToEmpty(account.getCryptoIdCard()));
            record.setUserName(StringUtils.trimToEmpty(account.getUserName()));

            return record;
        }
    }

    @Getter
    public enum UserMergeType {

        OPERATE(1, "运营操作"),
        SYSTEM_RULE_1(2, "系统操作"),
        ;

        UserMergeType(int code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        private int code;
        private String msg;
    }

    @Data
    public static class UserManageExt {

        private String mark;
        private String markValue;
        private String extKey;
        private String extValue;
    }

    @Data
    public static class UdeskSessionVo {
        @ApiModelProperty("在线咨询次数")
        private int totalConsultSize;
        @ApiModelProperty("最近一次在线咨询时间")
        private long lastConsultTime;
        @ApiModelProperty("最近一次在线咨询问题类型")
        private String lastConsultType;
    }

    @Data
    public static class UdeskBizRecord {
        private String bizRecordClassify;
        private Date createdAt;
    }

    @Data
    public static class UserRoleModel {
        @JsonIgnore
        private long id;

        @ApiModelProperty("加密的手机号")
        @JsonIgnore
        private String cryptoMobile;

        @ApiModelProperty("案例id")
        private int caseId;
        @ApiModelProperty("用户角色 1 发起人 2 患者 3 收款人")
        private int userRole;

        public UserRoleModel(String cryptoMobile, int caseId, int userRole) {
            this.cryptoMobile = cryptoMobile;
            this.caseId = caseId;
            this.userRole = userRole;
        }

        public UserRoleModel() {
        }
    }

    @Getter
    public enum UserRoleEnum {
        RAISE(1, "发起人"),
        PATIENT(2, "患者"),
        PAYEE(3, "收款人"),
        ;

        UserRoleEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private int code;
        private String desc;

    }

    @Getter
    public enum RaiseCaseQuality {

        NORMAL(1, "一般"),
        ORDINARY(2, "普通"),
        IMPORTANT(3, "重要"),
        URGENCY(4, "紧急"),

        ;

        RaiseCaseQuality(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private int code;
        private String desc;

        public static RaiseCaseQuality valueOfCode(int code) {
            RaiseCaseQuality[] values = RaiseCaseQuality.values();
            for (RaiseCaseQuality quality : values) {
                if (quality.getCode() == code) {
                    return quality;
                }
            }

            return null;
        }
    }

    @Getter
    public enum ExtMainFieldEnum {
        USER_PID(1, "user_pid_classify",  "用户的标示"),
        USER_UID(1, "user_uid_classify",  "用户的标示"),
        ;

        ExtMainFieldEnum(int code, String fieldName, String desc) {
            this.code = code;
            this.fieldName = fieldName;
            this.desc = desc;
        }

        private int code;
        private String fieldName;
        private String desc;
    }

    public static String RECORD_PID_COMMENT = "新手机号关联pid";
    public static String RECORD_UUID_COMMENT = "手机号合并生成uuid";
    public static String RECORD_IDCARD_COMMENT = "更新身份证.caseId: %d";

    @Getter
    public enum ExtFieldEnum {
        USER_RAISE_QUALITY(1, "raise_case_quality",  "用户发起案例的意愿"),
        ;

        ExtFieldEnum(int code, String fieldName, String desc) {
            this.code = code;
            this.fieldName = fieldName;
            this.desc = desc;
        }

        private int code;
        private String fieldName;
        private String desc;
    }

}
