package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class AdminWorkOrderFlowStatistics {

    @Data
    public static class searchParam {

        Date beginTime;
        Date endTime;

        int level; // -1 表示所有的level
        long firstLevelClassifyId;

        int staffId;

        int memberType; // 0 所有组织  1 所有人
        int orgId;


        List<Long> secondLevelClassifyIds;
        List<Integer> createIds;
        List<Integer> operatorIds;
        List<Integer> orderStatusSet;
        // 操作的类型
        List<Integer> operateTypeCodes;
        // 问题当前的处理组织
        List<Integer> problemTypeCodes;
        Date moreThanTimeNoHandle;

        int memberId;
        private String idType;

        Date createTime;
    }

    @Data
    public static class AdminWorkOrderFlowStatisticsView {

        int memberId;
        String memberName;

        int date;

        // 创建工单：创建工单
        int create;
        List<Long> createIds = Lists.newArrayList();

        // 未分配，待领取
        int noAssign;
        List<Long> noAssignIds = Lists.newArrayList();

        // 未处理：未处理
        int noStartHandle;
        List<Long> noStartHandleIds = Lists.newArrayList();

        // 处理中：处理中
        int handing;
        List<Long> handingIds = Lists.newArrayList();

        //工单总量：
        int allTo;
        List<Long> allToIds = Lists.newArrayList();

        int totalAllTo;

        // 流经过
        int flowPassing;
        List<Long> flowPassingIds = Lists.newArrayList();

        // 处理完成：处理完成
        int finish;
        List<Long> finishIds = Lists.newArrayList();

        // 不需要处理：不需要处理
        int noNeedHandle;
        List<Long> noNeedHandleIds = Lists.newArrayList();

        // 内部处理
//        int innerHandle;
//        List<Long> innerHandleIds = Lists.newLinkedList();

        int noHandleMoreThan48Hour;
        List<Long> noHandleMoreThan48HourIds = Lists.newArrayList();

        // 当天创建、当天处理完成的工单数量
        int finishOnTheSameDay;
        List<Long> finishOnTheSameDayIds = Lists.newArrayList();

        // 当天创建、当天未处理的工单数量
        int unprocessedOnTheSameDay;
        List<Long> unprocessedOnTheSameDayIds = Lists.newArrayList();

        // 当天创建、非当天未处理的工单数量
        int unprocessedOnTheNextDay;
        List<Long> unprocessedOnTheNextDayIds = Lists.newArrayList();

        // 当天创建、非当天处理的工单数量
        int theFinishOnNextDay;
        List<Long> theFinishOnNextDayIds = Lists.newArrayList();

        // 工单当天处理率=（处理完成2+流经工单数）／（处理完成2+流经工单数+未处理+处理中），工单当天0点到23:59:59的数据统计，过了对应时间，不会再随时间发生变化
        String finishRateOnTheSameDay = "0";

        //平均领单时长(s)
        double averageOrderTime;

        //平均首次响应时长
        double averageFirstHandleTime;

        //平均处理时长
        double averageFinishTime;

        //催单次数（对同一工单去重）
        int remindOrderNums;

        //组内+组外工单流转次数
        int reAllotCount;

        //同组流转工单数
        int sameOrgAllotCount;

        //组外流转工单数
        int diffOrgAllotCount;

        //平均流转次数（组内+组外工单流转次数/组内流转次数）
        String averageReAllotCount;

        //重复建单数
        int sameFlowCount;

        //空闲时间,min
        long freeTime;

        //处理工单（当天创建）
        int processingWorkOrder;
        List<Long> processingWorkOrderIds = Lists.newArrayList();

        public AdminWorkOrderFlowStatisticsView() {
        }

        public AdminWorkOrderFlowStatisticsView(int memberId) {
            this.memberId = memberId;
        }

//        static public void sortStatisticsByCreateTime(List<AdminWorkOrderFlowStatisticsView> views) {
//            if (CollectionUtils.isEmpty(views)) {
//                return;
//            }
//
//            for (AdminWorkOrderFlowStatisticsView view : views) {
//                sortViewByCreateTime(view);
//            }
//        }


        public void sortByIdDesc() {

            LongComparator lc = new LongComparator();

            sortById(createIds, lc);
            sortById(noAssignIds, lc);
            sortById(noStartHandleIds, lc);
            sortById(handingIds, lc);
            sortById(allToIds, lc);
            sortById(finishIds, lc);
            sortById(noNeedHandleIds, lc);
            sortById(noHandleMoreThan48HourIds, lc);
        }

        private void sortById(List<Long> l, Comparator r) {
            if (CollectionUtils.isEmpty(l)) {
                return ;
            }
            l.sort(r);
        }

        // 创建 和 分配 需要去重
        public void removeRepeat() {
            createIds = createIds.stream().collect(Collectors.toSet()).stream().collect(Collectors.toList());
            create = createIds.size();

            flowPassingIds = flowPassingIds.stream().collect(Collectors.toSet()).stream().collect(Collectors.toList());
            flowPassing = flowPassingIds.size();

            //当天创建、当天处理完成的工单数量  去重
            finishOnTheSameDayIds = finishOnTheSameDayIds.stream().collect(Collectors.toSet()).stream().collect(Collectors.toList());
            finishOnTheSameDay = finishOnTheSameDayIds.size();

            //当天创建、非当天未处理的工单数量    去重
            unprocessedOnTheNextDayIds = unprocessedOnTheNextDayIds.stream().collect(Collectors.toSet()).stream().collect(Collectors.toList());

            //当天创建、非当天未处理的工单数量    去重
            theFinishOnNextDayIds = theFinishOnNextDayIds.stream().collect(Collectors.toSet()).stream().collect(Collectors.toList());

            List<Long> workOrderIds = Lists.newArrayList();
            workOrderIds.addAll(handingIds);
            workOrderIds.addAll(noStartHandleIds);
            workOrderIds.addAll(flowPassingIds);
            workOrderIds.addAll(finishIds);
            workOrderIds.addAll(noAssignIds);

            allToIds = workOrderIds.stream().collect(Collectors.toSet()).stream().collect(Collectors.toList());
            allTo = allToIds.size();

            int numerator = finishOnTheSameDay + flowPassing;
            int denominator = finishOnTheSameDay + flowPassing + noStartHandle + handing;
            if (numerator > 0 && denominator > 0) {
                finishRateOnTheSameDay = String.format("%.2f%%", numerator * 100.0 / denominator);
            }

            //当天创建（当天未处理）
            List<Long> unprocessedOnIds = Lists.newArrayList();
            unprocessedOnIds.addAll(unprocessedOnTheNextDayIds);
            unprocessedOnIds.addAll(theFinishOnNextDayIds);

            unprocessedOnTheSameDayIds = unprocessedOnIds.stream().collect(Collectors.toSet()).stream().collect(Collectors.toList());
            unprocessedOnTheSameDay = unprocessedOnTheSameDayIds.size();

            //处理工单（当天创建）
            List<Long> WorkOrderTheSameIds = Lists.newArrayList();
            WorkOrderTheSameIds.addAll(flowPassingIds);
            WorkOrderTheSameIds.addAll(finishOnTheSameDayIds);
            WorkOrderTheSameIds.addAll(unprocessedOnTheSameDayIds);
            WorkOrderTheSameIds.addAll(handingIds);

            processingWorkOrderIds = WorkOrderTheSameIds.stream().collect(Collectors.toSet()).stream().collect(Collectors.toList());
            processingWorkOrder = processingWorkOrderIds.size();


        }

        public static void main(String[] args) {
            System.out.println(String.format("%.2f%%", 34.78 * 100 / 100));
        }

        public List<Long> getCaseIdsByType(String idType) {
            if (StringUtils.isEmpty(idType)) {
                return Arrays.asList();
            }
            switch (CaseIdType.valueOf(idType)) {
                case CREATE:
                    return createIds;
                case NOASSIGN:
                    return noAssignIds;
                case NOSTARTHANDLE:
                    return noStartHandleIds;
                case HANDING:
                    return handingIds;
                case ALLTO:
                    return allToIds;
                case FINISH:
                    return finishIds;
                case NONEEDHANDLE:
                    return noNeedHandleIds;
                case NOHANDLEMORETHAN48HOUR:
                    return noHandleMoreThan48HourIds;
            }
            return Arrays.asList();
        }
    }

    static class LongComparator implements Comparator<Long> {

        @Override
        public int compare(Long o1, Long o2) {
            return (int)(o2 - o1);
        }
    }




    enum CaseIdType {
        CREATE,
        NOASSIGN,
        NOSTARTHANDLE,
        HANDING,
        ALLTO,
        FINISH,
        NONEEDHANDLE,
        NOHANDLEMORETHAN48HOUR
    }

}








