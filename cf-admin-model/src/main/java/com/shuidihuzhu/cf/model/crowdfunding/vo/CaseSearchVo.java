package com.shuidihuzhu.cf.model.crowdfunding.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 案例查询vo<br/>
 * isContact
 * dataStatus
 * isReported
 * content
 * creditStatus
 * 这几天根据前端提供，案例列表页是没有的
 * <AUTHOR>
 * @date 2020/5/26 16:38
 */
@ApiModel(description = "案例查询vo")
@Data
public class CaseSearchVo {

    @ApiModelProperty("当前页数")
    private Integer current = 1;
    @ApiModelProperty("页面规模")
    private Integer pageSize = 10;
    @ApiModelProperty("提交人手机号")
    private String mobile;
    @ApiModelProperty("案例id")
    private Integer id;
    @ApiModelProperty("标题")
    private String title;
    @ApiModelProperty("发起人userId")
    private String caseUserId;
    @ApiModelProperty("驳回次数")
    private Integer refuseCountHandle;
    @ApiModelProperty("案例状态: 0-审批中, 1-修改中,2-筹款中, 4-已提交")
    private Integer status;
    /*@ApiModelProperty("是否沟通")
    private Integer isContact;*/
    @ApiModelProperty("案例状态: 是否结束")
    private Integer finished;
    @ApiModelProperty("操作状态: 0-未操作 1-已备注 2-已操作")
    private Integer operationStatus;
    /*@ApiModelProperty("数据状态: ")
    private Integer dataStatus;*/
    @ApiModelProperty("排序方式: 1-创建时间 2-筹款金额 3-操作时间 4-提审时间 6-驳回次数")
    private Integer sortHandle;
    @ApiModelProperty("开始日期")
    private String startTime;
    @ApiModelProperty("结束日期")
    private String endTime;
    @ApiModelProperty("特殊操作类型: 3-延后等待审核 4-延后等待联系 5-不在处理案例")
    private Integer handle;
    /*@ApiModelProperty("是否被举报")
    private Integer isReported;*/
    @ApiModelProperty("筹款文字内容")
    private String content;
    /*@ApiModelProperty("增信手动关闭")
    private int creditStatus;*/


    @ApiModelProperty("患者姓名")
    private String patientName;
    @ApiModelProperty("患者身份证号")
    private String patientIdCard;
    @ApiModelProperty("发起人姓名")
    private String selfRealName;

    @ApiModelProperty("发起人身份证号")
    private String selfIdCard;
    @ApiModelProperty("发起人户籍地-注意：需要传户籍接口返回的code字段值")
    private String selfCensus;
    @ApiModelProperty("发起人微信昵称")
    private String selfWechatName;
    @ApiModelProperty("患者户籍地-注意：需要传户籍接口返回的code字段值")
    private String patientCensus;
    @ApiModelProperty("患者年龄开始")
    private Integer patientAgeStart;
    @ApiModelProperty("患者年龄结束")
    private Integer patientAgeEnd;
    @ApiModelProperty("收款人姓名")
    private String payeeName;
    @ApiModelProperty("收款人手机号")
    private String payeeMobile;
    @ApiModelProperty("收款人身份证号")
    private String payeeIdCard;
    @ApiModelProperty("举报人手机号")
    private String reportMobile;

    @ApiModelProperty("目标金额开始")
    private String targetAmountStart;
    @ApiModelProperty("目标金额结束")
    private String targetAmountEnd;
    @ApiModelProperty("已筹金额开始")
    private String amountStart;
    @ApiModelProperty("已筹金额结束")
    private String amountEnd;
    @ApiModelProperty("证实内容")
    private String verificationContent;
    @ApiModelProperty("动态内容")
    private String trendContent;
    @ApiModelProperty(value = "提现状态 0：未提交 1：待审核 2：审核通过 3：审核驳回 4：审核撤销")
    private Integer withdrawState;
    @ApiModelProperty("疾病名称")
    private String diseaseName;
    @ApiModelProperty("就诊/确诊医院")
    private String confirmedHospital;
    @ApiModelProperty(value = "举报类型 0：无举报 1：内部举报 2：外部举报")
    private List<Integer> reportType;
    @ApiModelProperty(value = "打款账户性质 3：个人 4：对公医院 5：对公基金会")
    private Integer payeeAccountType;
    @ApiModelProperty(value = "打款类型 1：全额 2：分批 3：随筹随取")
    private Integer payeeType;
    @ApiModelProperty(value = "退款状态")
    private Integer refundStatus;
    @ApiModelProperty(value = "是否特殊报备")
    private Boolean firstApproveSpecialReport;
    @ApiModelProperty(value = "顾问姓名")
    private String volunteerName;
    @ApiModelProperty(value = "发起渠道")
    private String caseChannel;
    @ApiModelProperty(value = "患者出生证")
    private String patientBornCard;
    @ApiModelProperty(value = "代录入发起手机号")
    private String preposeRaiseMobile;

    @ApiModelProperty(value = "扩展案例Id")
    private List<Integer> extCaseIds;

}
