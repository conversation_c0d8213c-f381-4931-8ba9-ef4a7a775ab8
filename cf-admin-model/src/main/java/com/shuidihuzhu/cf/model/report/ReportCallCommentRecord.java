package com.shuidihuzhu.cf.model.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-04-03 17:20
 **/
@Data
@ApiModel
public class ReportCallCommentRecord {
    @ApiModelProperty("id")
    private long id;
    @ApiModelProperty("案例id")
    private int caseId;
    @ApiModelProperty("举报id")
    private int reportId;
    @ApiModelProperty("备注")
    private String comment;
    /**
     * @see com.shuidihuzhu.cf.enums.crowdfunding.CfReportConnectStatus
     * 1- 未沟通，2-已沟通
     */
    @ApiModelProperty("沟通状态")
    private int callStatus;
    /**
     * @see com.shuidihuzhu.cf.enums.crowdfunding.CfReportPageEnum
     */
    @ApiModelProperty("备注类型")
    private int type;
    @ApiModelProperty("创建时间")
    private Timestamp createTime;
}
