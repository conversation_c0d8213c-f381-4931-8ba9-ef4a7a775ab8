package com.shuidihuzhu.cf.model.common;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class MaskCodeOperationRecord {
    private int id;
    private int operatorId;
    private String operatorMis="";
    private String queryContent;
    private String operationPage = "";
    private String operationType = "";
    private int caseId;
    private String operationTime;
    private int clewId;
    private String wechatId = "";

    public String getOperatorMis() {
        return StringUtils.isNotBlank(operatorMis)?operatorMis:"";
    }
}
