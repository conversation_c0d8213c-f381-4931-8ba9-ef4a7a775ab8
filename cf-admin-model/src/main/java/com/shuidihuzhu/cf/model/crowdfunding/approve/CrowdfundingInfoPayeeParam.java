package com.shuidihuzhu.cf.model.crowdfunding.approve;

import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: wangpeng
 * @Date: 2021/2/25 19:56
 * @Description:
 */
@Data
public class CrowdfundingInfoPayeeParam implements Serializable {
    private static final long serialVersionUID = -7249169157685709021L;
    private String infoUuid;
    private CrowdfundingRelationType relationType;
    private UserIdentityType idType;
    private String payeeName;
    private String idCard;
    private String bankName;
    private String bankCard;
    private String bankBranchName;
    /**
     * 近亲属关系
     * @see com.shuidihuzhu.client.cf.api.model.enums.RelativesTypeEnum
     */
    private int relativesType;
    private String mobile;
    private String cnapsBranchId;

}
