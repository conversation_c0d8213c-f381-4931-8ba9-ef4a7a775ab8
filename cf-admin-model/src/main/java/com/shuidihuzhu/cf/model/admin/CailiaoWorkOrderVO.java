package com.shuidihuzhu.cf.model.admin;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE 2019/1/2
 */
@Data
@ApiModel("材料审核工单")
public class CailiaoWorkOrderVO extends WorkOrderVO {

    @ApiModelProperty("材料审核拒绝次数")
    private Integer refuseCount;

    @ApiModelProperty("审核拒绝总次数")
    private Integer allRefuseCount;

    @ApiModelProperty("用户主动申请的驳回次数")
    private Integer userRefuseCount;

    private String lastComment;

    // 案例的重复情况
    private Set<Integer> repeatStatusList;

    private String markFollowTime;

    /**
     * 诊断证明图片
     */
    private List<String> attachmentList;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 掩码
     */
    private List<String> maskImageList;
    /**
     * 疾病名称
     */
    private String diseaseName;

    /**
     * 驳回次数过多标签
     */
    private boolean rejectToMuch;

}
