package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.model.admin.vo.AdminWorkOrderMoneyBackExtVO;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderFlow;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;

@Data
public class AdminWorkOrderFlowView extends AdminWorkOrderFlow {

    private String workOrderFlowId;
    private String caseTitle;
    private String caseInfoId;
    private String firstClassifyDesc;
    private long firstClassifyId;
    private String secondClassifyDesc;
    private int level;
    private int workOrderStatus;

    private String comment;

    private int operatorId;
    private String operatorName;
    private String operatorRoleName;

    private int creatorId;
    private String creatorName;
    private String creatorRoleName;

    private int taskType;

    private String creatorChannel;

    private int remindNums;
    @ApiModelProperty("工单催单是否展示高亮")
    private boolean remindHighlight;

    private List<Integer> followTagIds;

    private String newClassifyDesc;

    private String oldClassifyDesc;
    
    @ApiModelProperty("返还款相关表单信息")
    private List<AdminWorkOrderMoneyBackExtVO> moneyBackExtList;
    //发起问题-线索流转至线下顾问，顾问UniqueCode
    private String clewSendBDUniqueCode;
    //发起问题-线索流转至线下顾问，手机号
    private String clewSendBDPhone;

    @Getter
    @AllArgsConstructor
    public enum  WorkOrderFlowUserIdentity {
        DEFAULT(0, "默认"),
        ORIGINATOR_SELF(1, "发起人本机"),
        PATIENT_SELF(2, "患者本人及亲属(非发起人)"),
        PATIENT_FRIEND(3, "患者同事、朋友(非发起人)"),
        DONORS_NET(4, "捐款人或网友"),
        POTENTIAL_ORIGINATOR(5, "潜在发起人"),
        OFFLINE_VOLUNTEERS(6, "线下筹款顾问"),
        SENSITIVE_GROUP(7, "敏感群体(媒体、政府、举报人等)"),
        SHUIDIHUZHU(8, "水滴互助、保险等其它客户"),

        ;

        int code;
        String desc;

        public static WorkOrderFlowUserIdentity getByCode(int code) {
            WorkOrderFlowUserIdentity[] values = WorkOrderFlowUserIdentity.values();

            for (WorkOrderFlowUserIdentity identity : values) {
                if (identity.getCode() == code) {
                    return identity;
                }
            }
            return null;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum WorkOrderFollowTags {
        NO(0, "无"),
        NOBODY_ANSWER_THE_PHONE(1, "电话暂时无人接听"),
        OFFICIAL_LETTER_IS_ISSUED(2, "公函开具中"),
        WAIT_FOR_USER_PUBLIC_PROGRESS(3, "等待用户发布动态"),
        JOIN_SHUIDI_PUBLIC(4, "已对接水滴公益"),
        NOTICE_BUG_GROUP(5, "已对接BUG群"),
        ACTIVITY_NOTICE_BP(6, "活动问题已反馈BP等回复"),
        WAIT_OFFLINE_BP_REPLY(7, "已对接线下BP等回复"),
        REFUND_PERSON_REMAIN_MONEY(8, "个人收款剩余款项需退回"),
        ACTIVITY_VENUE_REMIT_BP_REPLY(9, "小善日打款时效已反馈BP等回复"),
        OTHER(10, "其他"),

        ;

        int code;
        String desc;

        private static Map<Integer, WorkOrderFollowTags> DATA_HOLDER = Maps.newHashMap();
        static {
            WorkOrderFollowTags[] values = WorkOrderFollowTags.values();

            for (WorkOrderFollowTags followTag : values) {
                DATA_HOLDER.put(followTag.getCode(), followTag);
            }
        }


        public static WorkOrderFollowTags getByCode(int code) {
            return DATA_HOLDER.get(code);
        }

        public static String getByCodes(List<Integer> codes) {
            if(CollectionUtils.isEmpty(codes)){
                return "";
            }
            List<String> result = Lists.newArrayList();
            for (Integer code : codes) {
                result.add(getByCode(code).getDesc());
            }
            return String.join(",", result);
        }
    }

}
