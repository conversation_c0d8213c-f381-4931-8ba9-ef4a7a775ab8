package com.shuidihuzhu.cf.model.message;

import java.sql.Timestamp;

/**
 * Created by ahrievil on 2017/5/18.
 */
public class UrlThirdTrack {
    private Integer id;
    private String urlThird;
    private Integer mpType;
    private String channel;
    private String campaign;
    private String tag;
    private String urlSd;
    private String code;
    private String parameterType;
    private Integer valid;
    private Integer operatorId;
    private Timestamp createTime;
    private Timestamp lastModified;

    private int isNeedAccredit;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUrlThird() {
        return urlThird;
    }

    public void setUrlThird(String urlThird) {
        this.urlThird = urlThird;
    }

    public Integer getMpType() {
        return mpType;
    }

    public void setMpType(Integer mpType) {
        this.mpType = mpType;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getCampaign() {
        return campaign;
    }

    public void setCampaign(String campaign) {
        this.campaign = campaign;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getUrlSd() {
        return urlSd;
    }

    public void setUrlSd(String urlSd) {
        this.urlSd = urlSd;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getParameterType() {
        return parameterType;
    }

    public void setParameterType(String parameterType) {
        this.parameterType = parameterType;
    }

    public Integer getValid() {
        return valid;
    }

    public void setValid(Integer valid) {
        this.valid = valid;
    }

    public Integer getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Integer operatorId) {
        this.operatorId = operatorId;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getLastModified() {
        return lastModified;
    }

    public void setLastModified(Timestamp lastModified) {
        this.lastModified = lastModified;
    }

    public int getIsNeedAccredit() {
        return isNeedAccredit;
    }

    public void setIsNeedAccredit(int isNeedAccredit) {
        this.isNeedAccredit = isNeedAccredit;
    }
}
