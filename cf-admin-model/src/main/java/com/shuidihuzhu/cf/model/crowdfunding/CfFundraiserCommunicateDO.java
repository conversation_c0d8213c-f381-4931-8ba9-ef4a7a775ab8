package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @time 2019/12/16 下午8:25
 * @desc
 */
@Data
public class CfFundraiserCommunicateDO {
    //调query-fundraiser-follow-record这个接口时要回传在followId
    private Long id;
    private Integer caseId;
    //与患者关系key
    private Integer relationKey;
    //与患者关系value
    private String relationValue;
    //联系方式
    private String mobile;
    //联系方式掩码（后台用）
    private NumberMaskVo mobileMask;
    //接通状态 CfReportConnectStatus
    private Integer connectStatus;
    //跟进记录对应的答案的主键集合
    private String answerIds;
    //处理人
    private Long operatorId;
    //处理人
    private String operator;
    //沟通时间
    private Date createTime;
    private Date updateTime;
    // 本次沟通对象
    private String connectObject;
}
