package com.shuidihuzhu.cf.model.crowdfunding;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("用户关系返回结果")
public class UserFriendRelationshipResult {

    @ApiModelProperty("现在用户好友数量")
    private int currentFriendsCount;
    @ApiModelProperty("案例")
    private int friendsCountWhenRaise;
    private List<UserFriendRelationship> friendRelationshipList = Lists.newArrayList();
}
