package com.shuidihuzhu.cf.model.cfOperatingProfile;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.util.Date;
import java.util.List;
import java.util.Set;

@Data
public class CfOperatingProfileLog {
    private int id;

    private int businessId;

    private int operateId;
    private String operateName;

    private int operateType;
    private String operateComment;
    private int operateLogType;
    private Date createTime;

    @Data
    @AllArgsConstructor
    public static class  CfOperatingProfileLogView {
        private String operateName;
        private String operateComment;
        private long operateTime;
    }

    @Getter
    public enum OperateType {

        // 配置相关
        ENABLE(0, "启用"),
        DISABLE(1, "弃用"),
        DELETE(2, "删除"),
        UP(3, "上移"),
        DOWN(4, "下移"),
        ADD(5, "新增"),
        ALTER_SYSTEM_MARK(6, "系统标记条件"),
        CHANGE_PROBLEM_REMARK(7, "举报问题配置修改-问题名称/备选"),
        ;


        ;


        private int code;
        private String desc;

        public static List<Integer> DATA_STATUS_SET = Lists.newArrayList(ENABLE.getCode(), DISABLE.getCode(), DELETE.getCode());

        OperateType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        static public OperateType valueOfCode(int code) {
            OperateType[] var1 = OperateType.values();
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                OperateType type = var1[var3];
                if (type.getCode() == code) {
                    return type;
                }
            }

            throw new RuntimeException("OperateType code错误" + code);
        }
    }

    @Getter
    public enum OperateLogTye {

        CASE_LABEL(1, "案例标签配置"),
        ;

        int code;
        String desc;

        OperateLogTye(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    @Data
    public static class AddTagsParam {
        private int parentId;
        private String content;
        private int profileType;
        private Set<String> propertyList;
    }
}
