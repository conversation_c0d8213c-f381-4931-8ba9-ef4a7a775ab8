package com.shuidihuzhu.cf.model.content;

import java.sql.Timestamp;
import java.util.List;

/**
 * Created by wangsf on 17/6/6.
 */
public class AdminSeoArticle {
//`id` as id,
//		`tag` as tag,
//		`keywords` as keywords,
//		`description` as description,
//		`page_url` as pageUrl,
//		`body`	as body,
//		`time` as time,
//		`comefrom` as comefrom,
//		`nav` as nav,
//		`title` as title,
//		`uuid` as uuid

	private int id;
	private String uuid;
	private String title;
	private String cover;
	private String body;
	private String keywords;
	private String description;
	private String pageUrl;
	private Timestamp time;
	private String author;
	private String source;

	private List<SeoArticleTag> tags;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getUuid() {
		return uuid;
	}

	public void setUuid(String uuid) {
		this.uuid = uuid;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getCover() {
		return cover;
	}

	public void setCover(String cover) {
		this.cover = cover;
	}

	public String getBody() {
		return body;
	}

	public void setBody(String body) {
		this.body = body;
	}

	public String getKeywords() {
		return keywords;
	}

	public void setKeywords(String keywords) {
		this.keywords = keywords;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getPageUrl() {
		return pageUrl;
	}

	public void setPageUrl(String pageUrl) {
		this.pageUrl = pageUrl;
	}

	public Timestamp getTime() {
		return time;
	}

	public void setTime(Timestamp time) {
		this.time = time;
	}

	public String getAuthor() {
		return author;
	}

	public void setAuthor(String author) {
		this.author = author;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public List<SeoArticleTag> getTags() {
		return tags;
	}

	public void setTags(List<SeoArticleTag> tags) {
		this.tags = tags;
	}

	@Override
	public String toString() {
		return "AdminSeoArticle{" +
				"id=" + id +
				", uuid='" + uuid + '\'' +
				", title='" + title + '\'' +
				", cover='" + cover + '\'' +
				", body='" + body + '\'' +
				", keywords='" + keywords + '\'' +
				", description='" + description + '\'' +
				", pageUrl='" + pageUrl + '\'' +
				", time=" + time +
				", author='" + author + '\'' +
				'}';
	}
}
