package com.shuidihuzhu.cf.model.crowdfunding;

import java.sql.Timestamp;

/**
 * Created by Ahrievil on 2017/8/31
 */
public class CfFakeShareRecord {
    private int id;
    private String infoUuid;
    private long userId;
    private int isDelete;
    private Timestamp createTime;
    private Timestamp updateTime;

    public CfFakeShareRecord() {
    }

    public CfFakeShareRecord(String infoUuid, long userId) {
        this.infoUuid = infoUuid;
        this.userId = userId;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getInfoUuid() {
        return infoUuid;
    }

    public void setInfoUuid(String infoUuid) {
        this.infoUuid = infoUuid;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }
}
