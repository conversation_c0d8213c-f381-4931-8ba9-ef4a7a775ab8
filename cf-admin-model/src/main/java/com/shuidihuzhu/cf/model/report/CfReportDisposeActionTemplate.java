package com.shuidihuzhu.cf.model.report;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-09-16 11:28
 **/
@NoArgsConstructor
@Data
public class CfReportDisposeActionTemplate {
    /**
     * id
     */
    private long id;
    /**
     * 处理动作id
     */
    private long actionId;
    /**
     * 模板标题
     */
    private String title;
    /**
     * 模板内容
     */
    private String content;
    /**
     * 承诺内容
     */
    private String commitmentContent;

    public CfReportDisposeActionTemplate(long actionId, String title, String content, String commitmentContent) {
        this.actionId = actionId;
        this.title = title;
        this.content = content;
        this.commitmentContent = commitmentContent;
    }
}
