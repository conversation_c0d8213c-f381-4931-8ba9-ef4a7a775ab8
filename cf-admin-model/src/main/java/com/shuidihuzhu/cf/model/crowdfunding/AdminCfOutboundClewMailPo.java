package com.shuidihuzhu.cf.model.crowdfunding;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.util.Date;

@Data
public class AdminCfOutboundClewMailPo {
    private Integer id;

    /**
     * 生成时间
     */
    private Date mailTime;
    /**
     * 一级渠道
     */
    private String firstPlatform;
    /**
     * 二级渠道
     */
    private String secondPlatform;
    /**
     * 线索电话
     */
    private String mobile;
    /**
     * 与患者关系
     */
    private String relation;
    /**
     * 疾病名称
     */
    private String disease;
    /**
     * 是否关注水滴筹
     */
    private String subscribe;
    /**
     * 详细地址
     */
    private String address;

    private String fileName;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
