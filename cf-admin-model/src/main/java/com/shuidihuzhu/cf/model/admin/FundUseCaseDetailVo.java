package com.shuidihuzhu.cf.model.admin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: fengxuan
 * @create 2019-12-18 21:44
 **/
@ApiModel("案例详情")
@Data
public class FundUseCaseDetailVo {

    @ApiModelProperty("案例id")
    private int caseId;

    @ApiModelProperty("给c端展示的案例id")
    private String infoUUId;

    @ApiModelProperty("案例状态")
    private String caseStatus;

    @ApiModelProperty("举报状态")
    private String reportStatus;

    @ApiModelProperty("案例标签")
    private List<String> caseLabels;

    @ApiModelProperty("筹款人姓名")
    private String applicantName;

    @ApiModelProperty("筹款人联系方式")
    private String raiserPhone;

    @ApiModelProperty("案例结束状态")
    private String caseEndStatus;

    @ApiModelProperty("已筹款金额,元")
    private double hadCfAmount;

    @ApiModelProperty("待打款金额,当前余额,元")
    private double unPayAmount;

    @ApiModelProperty("成功打款金额,元")
    private double drawCashAmount;

    @ApiModelProperty("成功打款次数")
    private int drawCashNums;

    @ApiModelProperty("最后一次打款时间")
    private Date lastDrawTime;

    @ApiModelProperty("最后一次打款金额,元")
    private double lastDrawAmount;

    @ApiModelProperty("案例发起时间")
    private Date caseRaiseTime;

    @ApiModelProperty("已提交票据总额,元")
    private double commitBillAmount;

    @ApiModelProperty("待提交票据总额,元")
    private double unCommitBillAmount;

    @ApiModelProperty("需要提示待提交票据金额")
    private boolean needMarkUnCommitBill;

    @ApiModelProperty("费用类型,true有自付,false无自付")
    private boolean hasSelfPay;

}
