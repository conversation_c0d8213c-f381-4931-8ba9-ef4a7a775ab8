package com.shuidihuzhu.cf.model.crowdfunding;

import org.apache.commons.lang.builder.ToStringBuilder;

import java.util.List;

/**
 * Created by ahrievil on 2017/1/17.
 *
 * CREATE TABLE `cf_refuse_reason_item` (
 * `id` int(20) unsigned NOT NULL,
 * `content` varchar(255) NOT NULL DEFAULT '',
 * `type` int(6) NOT NULL,
 * `date_created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
 * `last_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
 * PRIMARY KEY (`id`)
 ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='驳回信息分类表'
 */
public class CfRefuseReasonItem {

    private int id;
    private String content;
    private int type;
    private int groupRank;
    private int proType;
    private List<CfRefuseReason> cfRefuseReasons;
    private List<Integer> reasonIds;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getProType() {
        return proType;
    }

    public void setProType(int proType) {
        this.proType = proType;
    }

    public List<CfRefuseReason> getCfRefuseReasons() {
        return cfRefuseReasons;
    }

    public void setCfRefuseReasons(List<CfRefuseReason> cfRefuseReasons) {
        this.cfRefuseReasons = cfRefuseReasons;
    }

    public List<Integer> getReasonIds() {
        return reasonIds;
    }

    public void setReasonIds(List<Integer> reasonIds) {
        this.reasonIds = reasonIds;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }

    public int getGroupRank() {
        return groupRank;
    }

    public void setGroupRank(int groupRank) {
        this.groupRank = groupRank;
    }
}
