package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/12/13 下午5:49
 * @desc
 */
@Data
public class CfFollowRecordPageDO {
    //质疑人等的联系方式
    private List<CfReportCommunicaterDO> communicaters;
    //质疑方的答案
    private List<AdminReportProblemAnswerDetail> questionerAnswer;
    //举报图片
    private String imageUrls;

    /**
     * 案例代录入填写的手机号
     */
    private String mobile;
    private NumberMaskVo mobileMask;

    /**
     * 收款人手机号
     */
    private String payeeMobile;
    private NumberMaskVo payeeMobileMask;

    /**
     * 紧急联系人手机号
     */
    private String emergencyPhone;
    private NumberMaskVo emergencyPhoneMask;
}
