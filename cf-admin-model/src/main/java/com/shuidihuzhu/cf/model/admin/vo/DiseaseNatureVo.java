package com.shuidihuzhu.cf.model.admin.vo;

import com.shuidihuzhu.cf.model.crowdfunding.CfDiseaseClassifyDO;
import com.shuidihuzhu.cf.model.crowdfunding.CfDiseaseManagerDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.collections.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2019-11-08 18:01
 **/
@Data
@ApiModel("疾病管理的几个可选属性")
public class DiseaseNatureVo {
    @ApiModelProperty("科室信息")
    private List<SimpleInfo> diseaseClassifyInfos;
    @ApiModelProperty("治疗方案")
    private List<SimpleInfo> treatmentProjectInfos;
    @ApiModelProperty("疾病类型")
    private List<SimpleInfo> raiseTypeInfos;

    public DiseaseNatureVo buildRaiseType() {
        this.raiseTypeInfos = Arrays.stream(CfDiseaseManagerDO.RaiseTypeEnum.values())
                .map(item -> new SimpleInfo(item.getCode(), item.getDesc()))
                .collect(Collectors.toList());
        return this;
    }

    public DiseaseNatureVo buildTreatmentProject() {
        this.treatmentProjectInfos = Arrays.stream(CfDiseaseManagerDO.TreatmentProjectEnum.values())
                .map(item -> new SimpleInfo(item.getCode(), item.getDesc()))
                .collect(Collectors.toList());
        return this;
    }

    public DiseaseNatureVo buildDiseaseClassify(List<CfDiseaseClassifyDO> classifyDOList) {
        if (CollectionUtils.isNotEmpty(classifyDOList)) {
            this.diseaseClassifyInfos = classifyDOList.stream()
                    .map(item -> new SimpleInfo((int) item.getId(), item.getClassifyDesc()))
                    .collect(Collectors.toList());
        }
        return this;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public class SimpleInfo {
        @ApiModelProperty("编码")
        private int code;
        @ApiModelProperty("描述")
        private String desc;
    }
}
