package com.shuidihuzhu.cf.model.admin;

import java.io.Serializable;
import java.util.Date;

public class MailTaskInfo implements Serializable {
    private static final long serialVersionUID = 7774603812646223852L;
    private Integer id;
    private String mailReceivers;
    private String mailSubject;
    private String mailComment;
    private String descr;
    private Byte status;
    private Integer oprid;
    private Integer lastoprid;
    private Date lastoprtm;
    private String sqlText;
    private String dbName;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getMailReceivers() {
        return mailReceivers;
    }

    public void setMailReceivers(String mailReceivers) {
        this.mailReceivers = mailReceivers;
    }

    public String getMailSubject() {
        return mailSubject;
    }

    public void setMailSubject(String mailSubject) {
        this.mailSubject = mailSubject;
    }

    public String getMailComment() {
        return mailComment;
    }

    public void setMailComment(String mailComment) {
        this.mailComment = mailComment;
    }

    public String getDescr() {
        return descr;
    }

    public void setDescr(String descr) {
        this.descr = descr;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Integer getOprid() {
        return oprid;
    }

    public void setOprid(Integer oprid) {
        this.oprid = oprid;
    }

    public Integer getLastoprid() {
        return lastoprid;
    }

    public void setLastoprid(Integer lastoprid) {
        this.lastoprid = lastoprid;
    }

    public Date getLastoprtm() {
        return lastoprtm;
    }

    public void setLastoprtm(Date lastoprtm) {
        this.lastoprtm = lastoprtm;
    }

    public String getSqlText() {
        return sqlText;
    }

    public void setSqlText(String sqlText) {
        this.sqlText = sqlText;
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }
}

