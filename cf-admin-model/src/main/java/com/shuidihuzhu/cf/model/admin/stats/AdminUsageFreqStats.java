package com.shuidihuzhu.cf.model.admin.stats;

import lombok.Data;

import java.util.Date;

@Data
public class AdminUsageFreqStats {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 功能类型
     */
    private Integer bizType;

    /**
     * 统计维度
     */
    private String statsDim;

    /**
     * 统计key
     */
    private String statsKey;

    /**
     * 统计value
     */
    private Long statsValue;

    /**
     * 是否删除：0 否，1 是
     */
    private Byte isDelete;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

}