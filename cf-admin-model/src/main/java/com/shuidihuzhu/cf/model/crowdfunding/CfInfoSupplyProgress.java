package com.shuidihuzhu.cf.model.crowdfunding;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;

import java.util.Date;

/**
 * @author: fengxuan
 * @create 2020-01-09 15:49
 **/
@ApiModel("通过下发填写的动态信息")
@Data
public class CfInfoSupplyProgress {

    @ApiModelProperty("id")
    private long id;

    @ApiModelProperty("c端用户的id")
    private long userId;

    @ApiModelProperty("caseId")
    private int caseId;

    @ApiModelProperty("infoUUId")
    private String infoUUId;

    @ApiModelProperty("下发记录id")
    private long progressActionId;

    @ApiModelProperty("动态id,只有审核通过后才有对应的id")
    private long progressId;

    @ApiModelProperty("用户填写文字信息")
    private String content;

    @ApiModelProperty("用户添加图片信息")
    private String imgUrls;

    /**
     * @see SupplyProgressStatus
     */
    @ApiModelProperty("对文字审核的状态")
    private int contentStatus;

    /**
     * @see SupplyProgressStatus
     */
    @ApiModelProperty("对图片审核的状态")
    private int imgStatus;

    private Date createTime;

    private boolean delete;

    @Getter
    public enum SupplyProgressStatus {
        init(0, "待审核"),
        pass(1, "通过"),
        reject(2, "驳回"),
        ;
        int code;
        String desc;

        SupplyProgressStatus(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    public static SupplyProgressStatus findByCode(int code) {
        for (SupplyProgressStatus value : SupplyProgressStatus.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }

}
