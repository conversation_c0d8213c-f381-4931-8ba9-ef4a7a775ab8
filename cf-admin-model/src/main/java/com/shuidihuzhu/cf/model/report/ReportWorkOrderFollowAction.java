package com.shuidihuzhu.cf.model.report;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-07-27 11:03
 **/
@NoArgsConstructor
@Data
public class ReportWorkOrderFollowAction {
    /**
     * id
     */
    private long id;
    /**
     * 工单id
     */
    private long workOrderId;
    /**
     * 操作人id
     */
    private long operatorId;
    /**
     * 工单类型
     */
    private int orderType;
    /**
     * 跟进动作
     */
    private int actionType;

    public ReportWorkOrderFollowAction(long workOrderId, long operatorId, int orderType, int actionType) {
        this.workOrderId = workOrderId;
        this.operatorId = operatorId;
        this.orderType = orderType;
        this.actionType = actionType;
    }
}
