package com.shuidihuzhu.cf.model.crowdfunding;

import com.google.common.base.Objects;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.sql.Timestamp;

/**
 * Created by ahrievil on 2017/6/21.
 */
@NoArgsConstructor
@Data
@ToString
public class CfRepeatUserIdRecord {
    private int id;
    private long userId;
    private int remainCounts;
    private Timestamp dateCreated;
    private Timestamp lastModified;

    public CfRepeatUserIdRecord(long userId, int remainCounts) {
        this.userId = userId;
        this.remainCounts = remainCounts;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CfRepeatUserIdRecord that = (CfRepeatUserIdRecord) o;
        return userId == that.userId;
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(userId);
    }
}
