package com.shuidihuzhu.cf.model.crowdfunding;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.admin.model.CfInfoSupplyField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2020-01-09 15:49
 **/
@ApiModel("sea后台下发动作,一次下发对应一条记录")
@Data
public class CfInfoSupplyAction {

    @ApiModelProperty("下发记录id")
    private long id;

    @ApiModelProperty("案例id")
    private int caseId;

    /**
     * @see ActionType
     */
    @ApiModelProperty("下发操作类型")
    private int actionType;

    /**
     * @see SupplyHandleStatus
     */
    @ApiModelProperty("下发操作的处理状态")
    private int handleStatus;

    @ApiModelProperty("下发人")
    private int supplyUserId;

    @ApiModelProperty("下发名字")
    private String operator;

    @ApiModelProperty("下发组织")
    private int supplyOrgId;

    @ApiModelProperty("下发组织名称")
    private String supplyOrgName;

    @ApiModelProperty("处理人姓名")
    private String handleOperatorName;

    @ApiModelProperty("处理人组织名称")
    private String handleOperatorOrgName;

    /**
     * 具体使用参考
     */
    @ApiModelProperty("下发原因,对应的勾选项")
    private String supplyReason;

    @ApiModelProperty("对固定下发原因的补充")
    private String comment;

    @ApiModelProperty("下发时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("用作查询条件的组织关系")
    private int orgForSearch;

    private boolean delete;

    @ApiModelProperty("工单id")
    private long workOrderId;

    @ApiModelProperty("工单状态")
    private int workOrderStatus;

    private long supplyProgressId;

    @ApiModelProperty("infoUUId")
    private String infoUUId;

    //具体填写内容
    private List<CfInfoSupplyField> supplyFields;

    //图片
    private String imgUrls;

    //是否使用模板
    private int useTemplate;
    
    //---------详情页展示用
    //用户填写内容
    private List<CfInfoSupplyField> userSupplyFields;

    //客服填写内容
    private List<CfInfoSupplyField> serviceSupplyFields;

    public boolean isNeiShenSupply() {
        Collection<Integer> neiShenGroupIds = getNerShenGroupIds();
        int supplyOrgId = getSupplyOrgId();
        return neiShenGroupIds.contains(supplyOrgId);
    }

    private Collection<Integer> getNerShenGroupIds() {
        Config config = ConfigService.getConfig("sdc.inner");
        String value = config.getProperty("apollo.sea.group.nei-shen.id-arr", "876,877,878,879,880");
        String[] split = StringUtils.split(value, ",");
        return Arrays.stream(split)
                .map(Integer::valueOf)
                .collect(Collectors.toSet());
    }

    @Getter
    public enum SupplyHandleStatus {
        init(0, "已下发"),
        wait_audit(1, "待审核"),
        pass(2, "审核通过"),
        reject(3, "审核驳回"),
        cancel(4, "已撤销"),

        ;
        private int code;
        private String desc;

        SupplyHandleStatus(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }


    @Getter
    public enum ActionType {
        progress(1, "下发动态审核"),
        progress_new(2, "下发动态审核"),

        ;
        private int code;
        private String desc;

        ActionType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    public static SupplyHandleStatus findHandleStatusByCode(int code) {
        for (SupplyHandleStatus value : SupplyHandleStatus.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }


    private static final List<SupplyHandleStatus> user_can_submit_status = Lists.newArrayList(
            SupplyHandleStatus.init,
            SupplyHandleStatus.reject
    );


    public static final List<SupplyHandleStatus> can_not_create_new_supply_action = Lists.newArrayList(
            SupplyHandleStatus.init,
            SupplyHandleStatus.wait_audit,
            SupplyHandleStatus.reject
    );


    public static final List<SupplyHandleStatus> not_allow_reprocess_status = Lists.newArrayList(
            SupplyHandleStatus.init,
            SupplyHandleStatus.wait_audit
    );


    /**
     * 用户是否能提交 = 链接是否失效
     * true:能提交
     */
    public static boolean canSubmit(int handleResultEnum) {
        SupplyHandleStatus handleStatusByCode = findHandleStatusByCode(handleResultEnum);
        return user_can_submit_status.contains(handleStatusByCode);
    }




}
