package com.shuidihuzhu.cf.model.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/07/09
 */
@Data
public class AdminCfFundUseAuditInfo {
    private String userMobile;
    private NumberMaskVo userMobileMask;
    private Integer crowdfundingId;
    private String patientName;
    private String infoUuid;
    private Long userId;
    private Integer progressId;
    private Timestamp drawCashTime;
    private Timestamp fundUseSubmitTime;
    private String fundUseContent;
    private String fundUseImageMaterial;
    private List<Integer> watermarks;
    private Integer fundAuditStatus;
    private Integer riskLabelMarkWorkOrderScene;
    /**
     * 存在举报记录
     */
    private boolean hasReport;
    private String fundUseRejectedReason;


    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}