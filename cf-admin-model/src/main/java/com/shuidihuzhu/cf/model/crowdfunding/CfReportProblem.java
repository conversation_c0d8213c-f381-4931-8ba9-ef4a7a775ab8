package com.shuidihuzhu.cf.model.crowdfunding;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * @author: fengxuan
 * @create 2019-12-10 19:22
 **/
@ApiModel("问题相关信息")
@Data
public class CfReportProblem {

    private int id;

    @ApiModelProperty("标签 id")
    private int labelId;

    @ApiModelProperty("问题说明")
    private String problem;

    @ApiModelProperty("问题类型，参考ReportProblemType")
    private int problemType;

    @ApiModelProperty("答案类型，参考ReportAnswerType")
    private int answerType;

    @ApiModelProperty("常驻问题,0常驻,非0非常驻")
    private int directShow;

    @ApiModelProperty("是否展示不知道,0是展示,1是不展示")
    private int unknowTag;

    @ApiModelProperty("是否必填答案,0是,1否")
    private int mustAnswer;

    @ApiModelProperty("是否启用0:未启用，1:启用")
    private int isUse;

    @ApiModelProperty("是否需要再次核实0:不需要，1:需要")
    private int isNeedVerify;

    @ApiModelProperty("问题排序")
    private int sort;

    @ApiModelProperty("展示位置")
    private int showLocation;

    @ApiModelProperty("必填项作用于")
    private int mustUse;



    @Getter
    public enum ReportProblemType {
        shuru(1, "输入框"),
        duohangshuru(2, "多行输入框"),
        xiala(3, "下拉框"),
        kaiguan(4, "开关控件"),
        duoxuan(5, "多选框"),
        danxuan(6, "单选框"),

        ;

        int code;

        String desc;
        ReportProblemType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static ReportProblemType findByCode(int code) {
            for (ReportProblemType value : ReportProblemType.values()) {
                if (value.getCode() == code) {
                    return value;
                }
            }
            return null;
        }

    }


    @Getter
    public enum ReportAnswerType {
        shuzhi(1, Integer.class),
        zhifuchuan(2, String.class),

        ;
        int code;
        Class clazz;
        ReportAnswerType(int code, Class clazz) {
            this.code = code;
            this.clazz = clazz;
        }

        public static ReportAnswerType findByCode(int code) {
            for (ReportAnswerType value : ReportAnswerType.values()) {
                if (value.getCode() == code) {
                    return value;
                }
            }
            return null;
        }
    }

    /**
     * 文本框问题类型
     */
    public static boolean noChoiceProblemType(int problemType) {
        ReportProblemType type = ReportProblemType.findByCode(problemType);
        return type == ReportProblemType.shuru || type == ReportProblemType.duohangshuru;
    }


}
