package com.shuidihuzhu.cf.model.label;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.lang.reflect.Field;
import java.util.List;

/**
 * @Description: 案例指标信息
 * @Author: panghairui
 * @Date: 2023/8/9 10:47 AM
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CfCaseMetricsInfo {

    /**
     * 目标金额
     */
    private Integer targetAmount;

    /**
     * 归一后疾病
     */
    private List<String> diseaseNormNames;

    /**
     * 患者年龄
     */
    private Integer age;

    /**
     * 案例类型
     */
    private List<String> caseTypes;

    /**
     * 患者身份
     */
    private List<String> patientIdentity;

    /**
     * 案例所属省份
     */
    private List<String> province;

    /**
     * 发起人与患者关系
     */
    private List<String> userRelationType;

    /**
     * 二发标签
     */
    private List<String> repeatLabel;

    /**
     * 医疗材料城市
     */
    private List<String> medicalCity;

    /**
     * 案例预测捐单量
     */
    private Integer predictDonateCount;

    /**
     * 是否重点案例
     */
    private List<String> majorCaseTag;

    /**
     * 患者职业
     */
    private List<String> patientProfession;

    public Object getFieldValue(String fieldName) throws NoSuchFieldException, IllegalAccessException {
        Field field = this.getClass().getDeclaredField(fieldName); // 获取字段对象
        field.setAccessible(true); // 设置字段可访问
        return field.get(this); // 获取字段的值
    }

}
