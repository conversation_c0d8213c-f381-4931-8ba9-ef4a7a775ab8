package com.shuidihuzhu.cf.model.report.schedule;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ReportPendingEntryVO {
    @ApiModelProperty("id")
    private long id;
    @ApiModelProperty("案例id")
    private int caseId;
    @ApiModelProperty("跟进时间")
    private String targetTime;
    @ApiModelProperty("工单状态desc")
    private String orderResultDesc;
    @ApiModelProperty("工单状态")
    private int orderResult;
    @ApiModelProperty("案例id")
    private String infoUuid;
    @ApiModelProperty("工单id")
    private long workOrderId;
    @ApiModelProperty("工单类型")
    private int orderType;
    @ApiModelProperty("工单状态")
    private int workOrderStatus;
}
