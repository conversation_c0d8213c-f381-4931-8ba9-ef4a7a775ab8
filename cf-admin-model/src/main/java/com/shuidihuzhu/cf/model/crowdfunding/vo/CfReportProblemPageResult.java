package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.cf.model.crowdfunding.CfReportProblem;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2019-12-10 21:14
 **/
@ApiModel("问题列表")
@Data
public class CfReportProblemPageResult {
    private int current;
    private int pageSize;
    private long total;
    private List<CfReportProblemVo> list;
}
