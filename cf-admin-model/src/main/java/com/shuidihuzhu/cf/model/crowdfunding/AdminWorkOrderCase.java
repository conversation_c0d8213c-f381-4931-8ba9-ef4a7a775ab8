package com.shuidihuzhu.cf.model.crowdfunding;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminWorkOrderCaseConst;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR> Ahrievil
 */
@Data
public class AdminWorkOrderCase {
    private long id;
    private long workOrderId;
    private long caseId;
    private int type;
    private int status;
    private int callStatus;
    private String approveResult;
    private Timestamp createTime;
    private Timestamp updateTime;
    private int isDelete;


    public AdminWorkOrderCase() {
    }

    public AdminWorkOrderCase(long workOrderId, long caseId, AdminWorkOrderCaseConst.CaseType caseType) {
        this.workOrderId = workOrderId;
        this.caseId = caseId;
        this.type = caseType.getCode();
    }
}
