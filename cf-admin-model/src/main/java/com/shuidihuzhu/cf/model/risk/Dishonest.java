package com.shuidihuzhu.cf.model.risk;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: zhen<PERSON><PERSON>u
 * @date: 2021-04-19 20:31
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Dishonest {
    private int id;
    private long userId;
    /**
     * 用户类型 0代表患者 1代表收款人
     */
    private int userType;
    private String username;
    private String userIdCard;
    /**
     * 是否是失信被执行人 0代表不是 1代表是
     */
    private int dishonest;
    private int caseId;
}
