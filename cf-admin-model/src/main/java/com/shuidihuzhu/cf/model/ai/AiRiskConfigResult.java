package com.shuidihuzhu.cf.model.ai;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2025/7/1 15:43
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiRiskConfigResult {

    @ApiModelProperty("场景类型")
    private String sceneType;

    @ApiModelProperty("风险因子合集")
    private String riskFactor;

    @ApiModelProperty("提示词配置")
    private String judgePrompt;

}
