package com.shuidihuzhu.cf.model.param;

import io.swagger.annotations.*;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.*;
import java.util.Date;

/**
 * @author: fengxuan
 * @create 2020-01-14 14:26
 **/
@ApiModel("查询参数")
@Data
public class SupplyActionSearchParam {

    private static final String format = "yyyy-MM-dd HH:mm:ss";

    @ApiModelProperty("案例id")
    private Integer caseId;

    @ApiModelProperty("下发状态")
    private Integer handleStatus;

    @ApiModelProperty("材料审核状态")
    private Integer caseStatus;

    @ApiModelProperty("组织id")
    private Integer orgId;

    @DateTimeFormat(pattern = format)
    @ApiModelProperty("开始时间")
    private Date createStartTime;

    @DateTimeFormat(pattern = format)
    @ApiModelProperty("结束时间")
    private Date createEndTime;

    @DateTimeFormat(pattern = format)
    @ApiModelProperty("用户提交时间，开始时间")
    private Date submitStartTime;

    @DateTimeFormat(pattern = format)
    @ApiModelProperty("用户提交时间，结束时间")
    private Date submitEndTime;

    @DateTimeFormat(pattern = format)
    @ApiModelProperty("最新操作时间，开始时间")
    private Date handleStartTime;

    @DateTimeFormat(pattern = format)
    @ApiModelProperty("最新操作时间，结束时间")
    private Date handleEndTime;

    @ApiModelProperty("当前页面")
    private int current = 1;

    @ApiModelProperty("页面大小")
    private int pageSize = 20;
}
