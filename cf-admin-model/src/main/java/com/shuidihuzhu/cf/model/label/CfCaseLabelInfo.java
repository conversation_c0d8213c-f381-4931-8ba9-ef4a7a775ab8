package com.shuidihuzhu.cf.model.label;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * @Description: 案例标签
 * @Author: pangh<PERSON><PERSON>
 * @Date: 2023/8/9 2:36 PM
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CfCaseLabelInfo {

    private Long id;
    /**
     * 标签优先级
     */
    private Integer priority;
    /**
     * 标签名
     */
    private String name;
    /**
     * 标签状态 0:启用 1:禁用
     */
    private Integer status;
    /**
     * 标签备注
     */
    private String remarks;
    /**
     * 最新操作人
     */
    private Long operatorId;
    /**
     * 最新操作人名
     */
    private String operatorName;

    private List<CfCaseLabelRule> rules;

    private String ruleJson;

    private Timestamp updateTime;

}
