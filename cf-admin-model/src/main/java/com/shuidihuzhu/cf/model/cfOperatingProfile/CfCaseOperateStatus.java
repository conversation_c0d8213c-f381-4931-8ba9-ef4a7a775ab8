package com.shuidihuzhu.cf.model.cfOperatingProfile;

import lombok.Data;
import lombok.Getter;

import java.util.Date;

@Data
public class CfCaseOperateStatus {

    private long id;
    private int caseId;
    /**
     * {@link OperateStatus}
     */
    private int operateType;
    private int operateId;
    private String organization;
    private String operateComment;

    private Date createTime;
    private Date updateTime;




    @Getter
    public enum OperateStatus {
        PAYEE_RELATION(1, "可以提交收款人关系视频"),
        CANCEL_PAYEE_RELATION(2, "取消-提交收款人关系"),

        ;
        private int code;
        private String desc;

        OperateStatus(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }


        public static OperateStatus valueOfCode(int code) {
            OperateStatus[] statusArray = OperateStatus.values();

            for (OperateStatus status : statusArray) {
                if (status.getCode() == code) {
                    return status;
                }
            }
            return null;
        }

    }

}
