package com.shuidihuzhu.cf.model.admin.workorder;

import lombok.Data;

/**
 * <AUTHOR>
 * @DATE 2020/5/8
 */
@Data
public class JuanzhanOrderCreate{

    private int caseId;

    private int orderlevel;

    private int orderType;

    private String showName;
    //是否开始任务(第一笔捐款or首次逻辑)
    private boolean createBegin;

    //是否二次任务
    private boolean createSecond;

    // 是否是触发自捐款
    private boolean fromPaySuccess;

    //订单id
    private long orderId;

    //uuid
    private String infoId;

    //额外类型
    private String orderExtType;


}
