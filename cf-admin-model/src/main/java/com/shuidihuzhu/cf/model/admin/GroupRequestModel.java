package com.shuidihuzhu.cf.model.admin;

import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class GroupRequestModel {
    private int id;
    private String name;
    private int isMain;
    private int bizType;
    private String wxMpConfigs;
    public GroupRequestModel(String name,int isMain,int bizType,String wxMpConfigs){
        this.name = name;
        this.isMain = isMain;
        this.bizType = bizType;
        this.wxMpConfigs = wxMpConfigs;
    }
    public GroupRequestModel(int id,String name,int isMain,int bizType,String wxMpConfigs){
        this.name = name;
        this.isMain = isMain;
        this.bizType = bizType;
        this.wxMpConfigs = wxMpConfigs;
        this.id = id;
    }
}
