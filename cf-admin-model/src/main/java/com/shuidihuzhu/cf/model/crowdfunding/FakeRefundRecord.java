package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@NoArgsConstructor
@Data
public class FakeRefundRecord {
    private long id;
    private String operationUser;
    private String payUid;
    private Integer crowdfundingOrderId;
    private Integer crowdfundingId;
    private Integer amount;
    private Integer refundType;
    private Timestamp createTime;
    private Timestamp updateTime;
    private Integer isDelete;

    public FakeRefundRecord(String operationUser, String payUid, Integer crowdfundingOrderId, Integer crowdfundingId, Integer amount, Integer refundType) {
        this.operationUser = operationUser;
        this.payUid = payUid;
        this.crowdfundingOrderId = crowdfundingOrderId;
        this.crowdfundingId = crowdfundingId;
        this.amount = amount;
        this.refundType = refundType;
    }
}
