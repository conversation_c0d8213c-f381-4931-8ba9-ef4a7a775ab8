package com.shuidihuzhu.cf.model.admin.channel;

import java.io.Serializable;

/**
 * Created by wuxin<PERSON> on 5/27/16.
 */
public class ChannelRecord implements Serializable {

	private static final long serialVersionUID = -737365660235341042L;

	private Integer id;

    private String channel;

    private String descr;

    private Short type;

    private String url;

    private Integer oprid;

    private Integer atime;

    private Integer wxMpType;

    private int thirdType;

    private String cfChannel;

    private Integer cfChannelGroupId;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getDescr() {
        return descr;
    }

    public void setDescr(String descr) {
        this.descr = descr;
    }

    public Short getType() {
        return type;
    }

    public void setType(Short type) {
        this.type = type;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getOprid() {
        return oprid;
    }

    public void setOprid(Integer oprid) {
        this.oprid = oprid;
    }

    public Integer getAtime() {
        return atime;
    }

    public void setAtime(Integer atime) {
        this.atime = atime;
    }

    public Integer getWxMpType() {
        return wxMpType;
    }

    public void setWxMpType(Integer wxMpType) {
        this.wxMpType = wxMpType;
    }

    public int getThirdType() {
        return thirdType;
    }

    public void setThirdType(int thirdType) {
        this.thirdType = thirdType;
    }

    public String getCfChannel() {
        return cfChannel;
    }

    public void setCfChannel(String cfChannel) {
        this.cfChannel = cfChannel;
    }

    public Integer getCfChannelGroupId() {
        return cfChannelGroupId;
    }

    public void setCfChannelGroupId(Integer cfChannelGroupId) {
        this.cfChannelGroupId = cfChannelGroupId;
    }
}
