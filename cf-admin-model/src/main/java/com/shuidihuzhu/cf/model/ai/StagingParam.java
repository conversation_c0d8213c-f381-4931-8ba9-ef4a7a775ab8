package com.shuidihuzhu.cf.model.ai;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description:
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/5/29 3:05 PM
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StagingParam {

    @ApiModelProperty("线索id")
    private Integer clewId;

    @ApiModelProperty("暂存类型")
    private Integer stagingType;

    @ApiModelProperty("暂存内容")
    private String stagingBaseInfo;

}
