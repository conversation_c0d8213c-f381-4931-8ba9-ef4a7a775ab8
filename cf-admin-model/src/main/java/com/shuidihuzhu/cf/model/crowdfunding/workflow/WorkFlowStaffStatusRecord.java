package com.shuidihuzhu.cf.model.crowdfunding.workflow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.checkerframework.checker.units.qual.A;

import java.util.Date;

/**
 * @author: fengxuan
 * @create 2020-02-14 10:14
 **/
@ApiModel("用户在线变更记录")
@Data
public class WorkFlowStaffStatusRecord {

    private long id;

    private long userId;
    @ApiModelProperty("操作人id")
    private long operatorId;
    @ApiModelProperty("操作人名称")
    private String operatorName;
    @ApiModelProperty("本人还是组织还是系统")
    private int optType;
    @ApiModelProperty("状态,1:在线,2:离线")
    private int staffStatus;
    @ApiModelProperty("变更时间")
    private Date createTime;

}
