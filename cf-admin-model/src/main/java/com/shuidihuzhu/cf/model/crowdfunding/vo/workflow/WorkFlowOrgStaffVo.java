package com.shuidihuzhu.cf.model.crowdfunding.vo.workflow;

import com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowStaffStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: fengxuan
 * @create 2020-02-13 19:24
 **/
@ApiModel("人员管理-组织人员信息")
@Data
public class WorkFlowOrgStaffVo {
    @ApiModelProperty("组织id,全部为0")
    private int orgId;
    /**
     * @see WorkFlowStaffStatus.OrgTypeEnum
     */
    @ApiModelProperty("组织")
    private int orgType;
    @ApiModelProperty("在线人数")
    private int onlineCount;
    @ApiModelProperty("离线人数")
    private int offlineCount;
    @ApiModelProperty(value = "暂停人数")
    private int pauseLineCount;
    @ApiModelProperty("待领取工单数")
    private int unAllotCount;

}
