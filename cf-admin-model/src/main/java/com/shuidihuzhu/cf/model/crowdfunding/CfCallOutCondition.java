package com.shuidihuzhu.cf.model.crowdfunding;

import java.sql.Timestamp;

/**
 * Created by ahrievil on 2017/5/15.
 */
public class CfCallOutCondition {
    private int id;
    private String infoUuid;
    private int status;
    private int operator;
    private String comment;
    private Timestamp dateCreated;
    private Timestamp lastModified;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getInfoUuid() {
        return infoUuid;
    }

    public void setInfoUuid(String infoUuid) {
        this.infoUuid = infoUuid;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getOperator() {
        return operator;
    }

    public void setOperator(int operator) {
        this.operator = operator;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public Timestamp getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Timestamp dateCreated) {
        this.dateCreated = dateCreated;
    }

    public Timestamp getLastModified() {
        return lastModified;
    }

    public void setLastModified(Timestamp lastModified) {
        this.lastModified = lastModified;
    }
}