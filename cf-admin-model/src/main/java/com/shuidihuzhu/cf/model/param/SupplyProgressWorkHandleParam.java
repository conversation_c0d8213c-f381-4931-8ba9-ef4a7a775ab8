package com.shuidihuzhu.cf.model.param;

import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyProgress;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: fengxuan
 * @create 2020-01-12 14:45
 **/
@ApiModel("下发操作参数")
@Data
public class SupplyProgressWorkHandleParam {

    @ApiModelProperty("案例id")
    private int caseId;

    @ApiModelProperty("下发动态id")
    private long supplyProgressId;

    @ApiModelProperty("下发操作id")
    private long supplyActionId;

    @ApiModelProperty("工单id")
    private long workOrderId;

    /**
     * @see CfInfoSupplyProgress.SupplyProgressStatus
     */
    @ApiModelProperty("图片审核状态,1:通过,2:驳回")
    private int imgUrlsHandleStatus;

    /**
     * @see CfInfoSupplyProgress.SupplyProgressStatus
     */
    @ApiModelProperty("文字审核状态,1:通过,2:驳回")
    private int contentHandleStatus;

    @ApiModelProperty("审核后的图片,用','分割需要保证顺序性")
    private String imgUrls;

    @ApiModelProperty("自定义备注")
    private String comment;
}
