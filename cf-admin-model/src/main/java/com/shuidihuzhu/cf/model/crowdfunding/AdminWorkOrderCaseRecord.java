package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;

/**
 * <AUTHOR> Ahrievil
 */
@Data
public class AdminWorkOrderCaseRecord {
    private long id;
    private long orderCaseId;
    private long workOrderId;
    private long caseId;
    private int status;
    private int type;
    private int operatorId;
    private String approveResult;
    private Timestamp createTime;
    private Timestamp updateTime;

    public AdminWorkOrderCaseRecord() {
    }

    public AdminWorkOrderCaseRecord(AdminWorkOrderCase adminWorkOrderCase, int operatorId) {
        this.caseId = adminWorkOrderCase.getCaseId();
        this.orderCaseId = adminWorkOrderCase.getId();
        this.workOrderId = adminWorkOrderCase.getWorkOrderId();
        this.status = adminWorkOrderCase.getStatus();
        this.type = adminWorkOrderCase.getType();
        this.approveResult = StringUtils.trimToEmpty(adminWorkOrderCase.getApproveResult());
        this.createTime = adminWorkOrderCase.getUpdateTime();
        this.operatorId = operatorId;
    }
}
