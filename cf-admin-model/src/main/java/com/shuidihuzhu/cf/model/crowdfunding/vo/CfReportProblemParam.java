package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.cf.model.crowdfunding.CfReportProblem;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2019-12-13 11:28
 **/
@ApiModel("创建/修改问题")
@Data
public class CfReportProblemParam extends CfReportProblem {

    private List<String> choiceDescribes;


    @Data
    public static class BindParam {
        private int problemId;
        private String choice;
        private int nextProblemId;
        @ApiModelProperty("拉下框,多选框")
        private int problemType;
    }
}
