package com.shuidihuzhu.cf.model.crowdfunding;

import org.apache.commons.lang.builder.ToStringBuilder;

import java.sql.Timestamp;

/**
 * Created by Ahrievil on 2017/11/5
 */
public class CfBlackListRecord {
    private int id;
    private int userId;
    private int isBlacklist;
    private int operatorId;
    private int limitRange;
    private int limitType;
    private int limitPart;
    private int mode;
    private String reason;
    private int contentValid;
    private Timestamp createTime;
    private Timestamp updateTime;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public int getIsBlacklist() {
        return isBlacklist;
    }

    public void setIsBlacklist(int isBlacklist) {
        this.isBlacklist = isBlacklist;
    }

    public int getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(int operatorId) {
        this.operatorId = operatorId;
    }

    public int getLimitRange() {
        return limitRange;
    }

    public void setLimitRange(int limitRange) {
        this.limitRange = limitRange;
    }

    public int getLimitType() {
        return limitType;
    }

    public void setLimitType(int limitType) {
        this.limitType = limitType;
    }

    public int getLimitPart() {
        return limitPart;
    }

    public void setLimitPart(int limitPart) {
        this.limitPart = limitPart;
    }

    public int getMode() {
        return mode;
    }

    public void setMode(int mode) {
        this.mode = mode;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public int getContentValid() {
        return contentValid;
    }

    public void setContentValid(int contentValid) {
        this.contentValid = contentValid;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
