package com.shuidihuzhu.cf.model.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.util.Date;
import java.util.List;

@Data
public class AdminCfRepeatView {

    int caseId;
    String infoId;
    String title;
    String mobile;
    NumberMaskVo mobileMask;
    String patientName;

    /**
     * 已筹金额
     * TODO 上线后删除
     */
    @Deprecated
    int targetAmount;

    /**
     * 已筹金额
     */
    int amount;

    /**
     * 目标金额
     */
    int realTargetAmount;

    /**
     * 已打款金额
     */
    int paidAmount;

    /**
     * 举报处理状态
     */
    int reportHandleResult;

    /**
     * 举报最新处理人
     */
    String reportOperateName;

    /**
     * 举报处理结果
     */
    int reportMark;

    String auditStatus;

    /**
     * TODO 上线后删除
     */
    @Deprecated
    String ifEnd;

    boolean end;

    String payAccountTime;
    int repeatStatus;

    String repeatReason;

    /**
     * 图片数量
     */
    int imageCount;

    /**
     * 文章字数
     */
    int contentCount;

    /**
     * 发起时间
     */
    Date raiseTime;

    //举报数量
    private int reportCount;

    /**
     * 举报信息
     */
    private List<ReportInfo> reportInfoList;

    public static final List<Integer> repeatSummaryCodeSet = Lists.newArrayList();
    public static final List<Integer> twiceSummaryCodeSet = Lists.newArrayList();

    static {
        // 1 + 2 + 4 + 8
        for (int i = 1; i < 16; ++i) {
            if ((i & 1) != 0 || (i & 2) != 0) {
                repeatSummaryCodeSet.add(i);
            }

            if ((i & 4) != 0 || (i & 8) != 0) {
                twiceSummaryCodeSet.add(i);
            }
        }
    }

    @AllArgsConstructor
    @Getter
    public enum RepeatReasonView {
        DEFALT(0, "非二次发起", 0),
        REPEAT(1, "重复发起", 1),
        MAYBE_REPEAT(2, "可能重复发起", 2),
        TWICE(3, "二次发起", 4),
        MAYBE_TWICE(4, "可能二次发起", 8),

        ;

        int code;
        String desc;
        int summaryCode;

        public static RepeatReasonView getReasonByCode(Integer code) {
            if (code == null) {
                return DEFALT;
            }
            RepeatReasonView[] values = RepeatReasonView.values();
            for (RepeatReasonView reason : values) {
                if (reason.code == code) {
                    return reason;
                }
            }
            return DEFALT;
        }

        public static RepeatReasonView getReasonViewByRepeatReason(AdminCfRepeatInfo.RepeatReason reason) {

            if (reason == null) {
                return DEFALT;
            }

            switch (reason) {
                case R7:
                    return MAYBE_REPEAT;
                case R8:
                    return MAYBE_TWICE;
                default:
                    return reason.getCode() % 2 == 1 ? REPEAT : TWICE;
            }
        }
    }
    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    @Data
    public static
    class ReportInfo {
        private String phone;
        private String content;
    }
}
