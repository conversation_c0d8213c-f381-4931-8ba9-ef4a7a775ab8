package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @time 2019/10/10 下午8:37
 * @desc
 */
@Data
public class CaseCommonDetailInfo {
    private int caseId;
    private String infoUuid;
    // 参考/admin/crowdfunding/repeat/get-repeat-list-v2接口的repeatStatus字段
    private int repeatStatus;
    //案例标题
    private String title;
    //发起人手机号
    private String raiserMobile;
    private NumberMaskVo raiseMobileMask;
    //发起人姓名
    private String raiserName;
    //患者姓名
    private String authorName;
    //收款人姓名
    private String payeeName;
    //已筹金额 单位：分
    private int amount;
    //目标金额 单位：分
    private int targetAmount;
    //材料审核状态 CrowdfundingStatus
    private int auditStatus;
    //资金状态 参考接口/admin/crowdfunding/approve/detail的cfCapitalStatus字段
    private int cfCapitalStatus;
    //案例发起时间
    private Date startTime;
    //案例结束时间
    private Date endTime;
    //引导用户发起渠道
    private String channel;
    //医院核实状态 CrowdfundingInfoStatusEnum 0:未下发,1:已下发,2:通过,3:驳回,4:已提交
    private int hospitalStatus;
    //举报次数
    private int reportCount;
    //举报的处理状态 CaseReportStatusEnum
    private int reportStatus;
    //案例特殊动作
    private String riskAction;
}
