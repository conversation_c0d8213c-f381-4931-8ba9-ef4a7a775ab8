package com.shuidihuzhu.cf.model.admin;

import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * @author: fengxuan
 * @create 2019-12-23 17:42
 **/
@Data
public class FundUseWorkOrderVO extends WorkOrderVO {
    @ApiModelProperty("案例标签")
    private List<String> caseLabels;
    @ApiModelProperty("待提交票据总额")
    private double unCommitBillAmount;
    @ApiModelProperty("需要提示待提交票据金额")
    private boolean needMarkUnCommitBill;
    private boolean hasReport;
    private String patientName;
    private Timestamp drawFinishTime;
    private String fundUseRejectedReason;
    private String content;
    private String attachmentUrls;


    private Date firstDrawSuccessTime;
    private long firstDrawAmount;
    private long totalDrawAmount;

    private String raiseName;
    private String raiseMobile;
    private NumberMaskVo raiseMobileMask;
    private String prePostMobile;
    private NumberMaskVo prePostMobileMask;

    private Date firstUpdateBillTime;

    private long amountReasonableTaskId;
    private int amountReasonableTaskType;
    private String amountReasonableTaskImages;
    private String amountReasonableTaskContent;
    private int amountReasonableTaskAmountStart;
    private int amountReasonableTaskAmountEnd;
    private int amountReasonableTaskPlan;
    private long amountReasonableTaskPlanWorkDataId;
    private long amountReasonableAfterDays;

}
