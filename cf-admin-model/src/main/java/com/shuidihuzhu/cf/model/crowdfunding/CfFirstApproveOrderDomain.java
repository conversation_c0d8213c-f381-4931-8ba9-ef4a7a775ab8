package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.AdminUGCTask;
import lombok.Data;
import org.joda.time.DateTime;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

public class CfFirstApproveOrderDomain {

    @Data
    static public class OrderReportSearchParam {
        public static final int DATE = 1;
        public static final int PERSON = 2;

        private Date rawBeginDate;
        private Date rawEndDate;

        private DateTime beginDate;
        private DateTime endDate;
        private int operatorId;

        private int current;
        private int pageSize;

        private int contentType;
        private int limit = 2000;

        private List<Integer> resultStatus;

        private int orderType;
        private int orderTask;

        private int searchType; // 1 时间 2 操作人
        private int orderStatus;
        private List<Integer> operatorIds;
    }

    @Data
    static public class FirstApproveWorkSummary {
        private long assignNum;
        private long passNum;
        private long rejectNum;
        private double passRatio;
        private String handleDuration;
        private String answerDuration;

        private long handleDurationMills;
        private long answerDurationMills;
    }

    @Data
    static public class FirstApproveWorkSummaryDate extends FirstApproveWorkSummary {
        private long createNum;
        private Date dateFrom;
    }

    @Data
    static public class FirstApproveWorkSummaryPerson extends FirstApproveWorkSummary {
        private String operatorName;
        private int  operatorId;
    }


    @Data
    static public class FirstApproveFailMsgSendTemplate {
        CrowdfundingInfo info;
        String failMag;
        String title;
        String msgKey1;
        String msgKey2;
        int rejectType;  // 运营驳回的类型  默认 0 表示正常的驳回，  1表示建议停止筹款
    }

}

