package com.shuidihuzhu.cf.model.admin;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by w<PERSON><PERSON><PERSON> on 16/8/15.
 */
@Data
public class OperationHistory implements Serializable {

    private static final long serialVersionUID = 2219352550761913561L;
    private Integer id;
    private int operatorId;
    private Integer operationType;
    private String tableName;
    private Long rawId;
    private String fieldName;
    private String oldValue;
    private String newValue;
    private Date addTime;
    private Integer status;

    public OperationHistory() {
    }

    public OperationHistory(int operatorId, Integer operationType, String tableName, Long rawId, String fieldName, String oldValue, String newValue, Date addTime) {
        this.operatorId = operatorId;
        this.operationType = operationType;
        this.tableName = tableName;
        this.rawId = rawId;
        this.fieldName = fieldName;
        this.oldValue = oldValue;
        this.newValue = newValue;
        this.addTime = addTime;
    }
}
