package com.shuidihuzhu.cf.model.label;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.checkerframework.common.value.qual.ArrayLen;

import java.util.List;

/**
 * @Description: 规则
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/8/9 2:39 PM
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CfCaseLabelRule {

    private List<CfCaseLabelCondition> conditionList;

}
