package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import com.shuidihuzhu.cf.enums.crowdfunding.CfReportPageEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfReportRelationEnum;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @time 2019/12/13 下午4:44
 * @desc
 */
@Data
public class CfReportCommunicaterDO {
    private Long id;
    private Integer caseId;
    /**
     * 当是筹款方的联系人时(type=2)，reportId=0
     * @see CfReportPageEnum
     */
    private Integer reportId;
    /**
     * 代表是质疑方的联系人还是筹款方的联系人
     * @see CfReportPageEnum
     */
    private Integer type;

    /**
     * @see CfReportRelationEnum
     */
    private Integer relationKey;
    private String relationValue;
    private String mobile;
    private NumberMaskVo mobileMask;
    private Integer callCount;
    private Long operatorId;
    private Integer isDelete;
    private Date createTime;
    private Date updateTime;
    /**
     * 是否是手动添加
     */
    private boolean isManualAdd;
}
