package com.shuidihuzhu.cf.model.pay.wx;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.client.baseservice.pay.model.stock.WxStockCreateParam;
import com.shuidihuzhu.client.baseservice.pay.model.stock.dto.*;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2020/5/6 10:19 上午
 * @Description:
 */
@Data
public class CfWxStockCreateRecord {
    private long id;

    private String channel;

    private long operatorUserId;
    private String operatorUserName;

    /**
     * 商家券批次名称
     * 批次名称，字数上限为24个字节长度（中文按UTF8编码算字节数）。
     * 示例值：8月1日活动券
     */
    private String stockName;
    /**
     * 批次归宿商户号
     * 商户商户编号不能为空
     * true
     */
    private int bizType;
    /**
     * 批次备注
     * 仅配置商户可见，用于自定义信息。
     * 示例值：活动使用
     * string(20)
     * false
     */
    private String comment;

    /**
     * 适用商品范围
     * 用来描述批次在哪些商品可用，会显示在微信卡包中。
     * string(15)
     * true
     */
    private String goodsName;

    /**
     * 批次类型
     * 批次类型 WxStockTypeEnum
     * NORMAL：固定面额满减券批次
     * DISCOUNT：折扣券批次
     * EXCHANGE：换购券批次
     * string(32)
     * true
     */
    private String stockTypeEnumName;

    /**
     * 核销规则
     * 券核销相关规则
     * true
     */
    private WxStockCouponUseRule wxStockCouponUseRule;

    private String wxStockCouponUseRuleJson;

    /**
     * 发放规则
     * 券发放相关规则
     * true
     */
    private WxStockSendRule wxStockSendRule;

    private String wxStockSendRuleJson;

    /**
     * 商户请求单号
     * 商户创建批次凭据号（格式：商户id+日期+流水号），商户侧需保持唯一性。
     * 示例值：100002322019090134234sfdf
     */
    private String outRequestNo;

    /**
     * 自定义入口
     * 卡详情页面，可选择多种入口引导用户。
     * <p>
     * false
     */
    private WxStockCustomerEnhance wxStockCustomerEnhance;
    private String wxStockCustomerEnhanceJson;

    /**
     * 样式信息
     * 创建批次时的样式信息。
     * <p>
     * false
     */
    private WxStockCreateDisplayPatternInfo wxStockCreateDisplayPatternInfo;
    private String wxDisplayPatternInfoJson;
    /**
     * 券code模式
     * 枚举值：
     * WECHATPAY_MODE：系统分配券code。（固定22位纯数字）
     * MERCHANT_API：商户发放时接口指定券code。
     * MERCHANT_UPLOAD：商户上传自定义code，发券时系统随机选取上传的券code。
     * 示例值：WECHATPAY_MODE
     * <p>
     * true
     */
    private String couponCodeModeEnumName;

    /**
     * 事件回调通知商户的配置。
     * <p>
     * false
     */
    private WxStockNotifyConfig wxStockNotifyConfig;
    private String wxStockNotifyConfigJson;

    /**
     * 上传执行结果
     */
    private String createMsg;
    /**
     * 上传执行结果
     */
    private String createCode;
    /**
     * 商家券批次号
     */
    private String stockId;
    /**
     * 商家券创建时间
     */
    private String stockCreateTime;

    /**
     * 逻辑删除
     */
    private int isDelete;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


    public CfWxStockCreateRecord() {
    }

    public static CfWxStockCreateRecord buildWxStockCreateRecord(WxStockCreateParam wxStockCreateParam) {
        CfWxStockCreateRecord cfWxStockCreateRecord = new CfWxStockCreateRecord();
        cfWxStockCreateRecord.setStockName(wxStockCreateParam.getStockName());
        cfWxStockCreateRecord.setBizType(wxStockCreateParam.getBizType());
        cfWxStockCreateRecord.setComment(wxStockCreateParam.getComment());
        cfWxStockCreateRecord.setGoodsName(wxStockCreateParam.getGoodsName());
        cfWxStockCreateRecord.setStockTypeEnumName(wxStockCreateParam.getStockType().name());

        cfWxStockCreateRecord.setWxStockCouponUseRule(wxStockCreateParam.getCouponUseRule());
        cfWxStockCreateRecord.setWxStockCouponUseRuleJson(JSON.toJSONString(wxStockCreateParam.getCouponUseRule()));

        cfWxStockCreateRecord.setWxStockSendRule(wxStockCreateParam.getStockSendRule());
        cfWxStockCreateRecord.setWxStockSendRuleJson(JSON.toJSONString(wxStockCreateParam.getStockSendRule()));

        cfWxStockCreateRecord.setOutRequestNo(wxStockCreateParam.getOutRequestNo());

        cfWxStockCreateRecord.setWxStockCustomerEnhance(wxStockCreateParam.getCustomEntranceData());
        cfWxStockCreateRecord.setWxStockCustomerEnhanceJson(JSON.toJSONString(wxStockCreateParam.getCustomEntranceData()));

        cfWxStockCreateRecord.setWxStockCreateDisplayPatternInfo(wxStockCreateParam.getWxStockCreateDisplayPatternInfo());
        cfWxStockCreateRecord.setWxDisplayPatternInfoJson(JSON.toJSONString(wxStockCreateParam.getWxStockCreateDisplayPatternInfo()));

        cfWxStockCreateRecord.setCouponCodeModeEnumName(wxStockCreateParam.getCouponCodeMode().name());

        cfWxStockCreateRecord.setWxStockNotifyConfig(wxStockCreateParam.getNotifyConfig());
        cfWxStockCreateRecord.setWxStockNotifyConfigJson(JSON.toJSONString(wxStockCreateParam.getNotifyConfig()));

        return cfWxStockCreateRecord;
    }
}
