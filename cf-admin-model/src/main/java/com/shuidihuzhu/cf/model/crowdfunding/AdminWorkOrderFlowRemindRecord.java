package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.Data;

import java.util.Date;

/**
 * @author: fengxuan
 * @create 2019-11-27 16:20
 **/
@Data
public class AdminWorkOrderFlowRemindRecord {

    private long id;

    private int flowId;
    //这个字段多余但是为了展示以及查询使用，和flowId重复，大致结构为“yyyyMMdd00”+flowId
    private String workOrderFlowId;

    //当前工单操作人
    private int currentOperatorId;

    private String currentOperatorName;

    private String operatorRoleName;

    //催单人
    private int remindOperatorId;

    private String remindOperatorName;

    private String remindOperatorOrg;

    private String comment;

    private Date createTime;
}
