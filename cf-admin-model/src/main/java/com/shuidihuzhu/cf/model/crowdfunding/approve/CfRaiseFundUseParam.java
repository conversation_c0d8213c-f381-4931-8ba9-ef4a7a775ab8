package com.shuidihuzhu.cf.model.crowdfunding.approve;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: wangpeng
 * @Date: 2021/2/24 21:00
 * @Description:
 */
@Data
public class CfRaiseFundUseParam implements Serializable {
    private static final long serialVersionUID = 6008317049763453529L;
    @ApiModelProperty("已花费金额 单位元")
    private Integer hasCostAmount;
    @ApiModelProperty("未来花费金额 单位元")
    private Long futureExpenditureAmount;
    @ApiModelProperty("医疗药品费用 0 无 1 有 2 不确定")
    private Integer userForMedicalDrug;
    @ApiModelProperty("医疗药品费用 单位：元")
    private Integer medicalDrugAmount;
    @ApiModelProperty("康复护理费用 0 无 1 有 2 不确定")
    private Integer userForNursing;
    @ApiModelProperty("康复护理费用 单位：元")
    private Integer nursingAmount;
    @ApiModelProperty("政府补助 0 无 1有")
    private Integer govRelief;
    @ApiModelProperty("政府救助-单位元")
    private Integer totalGovReliefAmount;
    @ApiModelProperty("案例Id")
    private String infoUuid;
    @ApiModelProperty("案例Id")
    private Integer caseId;
}
