package com.shuidihuzhu.cf.model.admin.vo;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * @package: com.shuidihuzhu.cf.vo
 * @Author: liujiawei
 * @Date: 2020-01-10  17:10
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class CfDynamicMsgVo {

    private String infoUuid;

    /**
     * 0 已下发
     * 1 待审核
     * 2 审核通过
     * 3 审核驳回
     * 4 已撤销
     */
    private int pushStatus = -1;

    /**
     * 是否可编辑文字
     */
    private boolean canEditorContent = true;

    /**
     * 是否可编辑图片
     */
    private boolean canEditorImgs = true;

    /**
     * 用户回显文字
     */
    private String content;

    /**
     * 用户图片
     */
    private List<String> imgs = Lists.newArrayList();

    /**
     * 下发/驳回原因
     */
    private List<String> reason = Lists.newArrayList();


    private int supplyActionId = 0;


    private int supplyProgressId = 0;

}
