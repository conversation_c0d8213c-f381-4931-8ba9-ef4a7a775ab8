package com.shuidihuzhu.cf.model.admin.vo;

import com.shuidihuzhu.cf.model.crowdfunding.CfDiseaseManagerDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: fengxuan
 * @create 2019-11-08 17:04
 **/
@Data
@ApiModel("疾病详情")
public class CfDiseaseManagerVo extends CfDiseaseManagerDO {
    @ApiModelProperty("发起方式描述")
    private String raiseTypeDesc;
    @ApiModelProperty("疾病科室描述")
    private String diseaseClassifyDesc;
    @ApiModelProperty("治疗方案描述")
    private String treatmentProjectDesc;
}
