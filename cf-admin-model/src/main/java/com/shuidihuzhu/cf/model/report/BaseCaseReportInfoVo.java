package com.shuidihuzhu.cf.model.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-05-08 14:58
 **/
@Data
@ApiModel
public class BaseCaseReportInfoVo {
    @ApiModelProperty("案例id")
    private int caseId;
    @ApiModelProperty("举报数量")
    private int reportNumber;
    @ApiModelProperty("举报处理状态")
    private int handleStatus;
    @ApiModelProperty("首次举报时间")
    private Date firstReportTime;
    @ApiModelProperty("最新举报时间")
    private Date lastReportTime;
    @ApiModelProperty("最新操作时间")
    private Date lastOperationTime;
}
