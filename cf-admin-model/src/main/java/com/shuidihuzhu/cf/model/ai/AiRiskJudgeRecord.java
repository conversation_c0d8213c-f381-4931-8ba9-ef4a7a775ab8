package com.shuidihuzhu.cf.model.ai;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: ai判断内容风险记录
 * @Author: pangh<PERSON><PERSON>
 * @Date: 2025/5/21 11:12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiRiskJudgeRecord {

    @ApiModelProperty("案例id")
    private Integer caseId;

    @ApiModelProperty("案例infoId")
    private String infoId;

    @ApiModelProperty("场景类型")
    private String sceneType;

    @ApiModelProperty("检测内容类型")
    private String judgeType;

    @ApiModelProperty("大模型类型")
    private Integer modelType;

    @ApiModelProperty("命中的风险因子")
    private String hitRiskFactor;

    @ApiModelProperty("命中风险结果")
    private String hitRiskResult;

    @ApiModelProperty("检测内容详情")
    private String judgeContent;

    @ApiModelProperty("增信详情")
    private String creditContent;

    @ApiModelProperty("模型判断依据")
    private String judgeReason;

    @ApiModelProperty("发起顾问组织架构")
    private String bdOrg;

}
