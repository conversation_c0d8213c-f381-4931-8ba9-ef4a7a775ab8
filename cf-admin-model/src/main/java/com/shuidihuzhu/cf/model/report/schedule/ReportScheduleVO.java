package com.shuidihuzhu.cf.model.report.schedule;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class ReportScheduleVO {
    @ApiModelProperty("跟进id")
    private long id;
    @ApiModelProperty("案例id")
    private int caseId;
    @ApiModelProperty("操作人")
    private int operatorId;
    @ApiModelProperty("操作人姓名")
    private String operatorName;
    @ApiModelProperty("是否已处理")
    private boolean done;
    @ApiModelProperty("跟进时间")
    private Date targetTime;


    @ApiModelProperty("案例标题")
    private String caseTitle;

    @ApiModelProperty("工单类型")
    private int orderType;

    @ApiModelProperty("工单状态")
    private int orderResult;

}
