package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderClassifySettings;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AdminWorkOrderClassifySettingsVo {
    private long id;
    private String allText;
    private int userId;
    @ApiParam("上一层分类的Id")
    private long parentId;
    private int available;
    private int weight;
    private boolean forbidAbort;
    @ApiParam("是否能自动触发")
    private boolean autoTrigger;
    List<AdminWorkOrderClassifySettingsVo> next;

    public static AdminWorkOrderClassifySettingsVo valueOf(AdminWorkOrderClassifySettings settings) {
        AdminWorkOrderClassifySettingsVo vo = new AdminWorkOrderClassifySettingsVo();
        if (settings == null) {
            return vo;
        }
        vo.setId(settings.getId());
        vo.setAllText(settings.getAllText());
        vo.setParentId(settings.getParentId());
        vo.setAutoTrigger(settings.isAutoTrigger());
        vo.setAvailable(settings.getAvailable());
        vo.setWeight(settings.getWeight());
        return vo;
    }

    public static AdminWorkOrderClassifySettings toModel(AdminWorkOrderClassifySettingsVo vo) {
        AdminWorkOrderClassifySettings settings = new AdminWorkOrderClassifySettings();
        settings.setId(vo.getId());
        settings.setAllText(vo.getAllText());
        settings.setParentId(vo.getParentId());
        settings.setOperatorId(vo.getUserId());
        return settings;
    }


}
