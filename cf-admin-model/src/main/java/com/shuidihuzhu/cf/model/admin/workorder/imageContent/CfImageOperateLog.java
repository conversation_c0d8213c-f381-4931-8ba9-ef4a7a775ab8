package com.shuidihuzhu.cf.model.admin.workorder.imageContent;

import lombok.Data;
import org.springframework.boot.convert.DataSizeUnit;

@Data
public class CfImageOperateLog {

    private long workOrderId;
    private long operateTime;
    private String operator;
    private OperateDetail operateDetail;


    @Data
    public static class OperateDetail {
        private String auditResult;
        private String handleNotes;
        private String comment;

        public OperateDetail() {
        }

        public OperateDetail(String auditResult, String handleNotes, String comment) {
            this.auditResult = auditResult;
            this.handleNotes = handleNotes;
            this.comment = comment;
        }
    }

}
