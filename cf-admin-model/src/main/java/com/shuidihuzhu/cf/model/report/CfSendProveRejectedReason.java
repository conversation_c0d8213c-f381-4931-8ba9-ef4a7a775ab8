package com.shuidihuzhu.cf.model.report;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-09-24 11:04
 **/
@NoArgsConstructor
@Data
public class CfSendProveRejectedReason {
    /**
     * id
     */
    private long id;
    /**
     * 案例id
     */
    private int caseId;
    /**
     * 补充证明id
     */
    private long proveId;
    /**
     * 驳回原因
     */
    private String rejectedReason;
    /**
     * 1-模板撤回,2-模板驳回,3-图片驳回
     */
    private int type;

    public CfSendProveRejectedReason(int caseId, long proveId, String rejectedReason, int type) {
        this.caseId = caseId;
        this.proveId = proveId;
        this.rejectedReason = rejectedReason;
        this.type = type;
    }
}
