package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import com.shuidihuzhu.cf.enums.crowdfunding.AddTrustAuditStatusEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CredibleTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoStatusEnum;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @time 2020/1/10 下午2:19
 * @desc
 */
@Data
public class CfCredibleInfoDO {
    private Long id;
    private Integer caseId;
    //增信信息id
    private Long subId;
    //上传者手机号
    private String mobile;
    private NumberMaskVo mobileMask;
    /**
     * 形式
     * @see CredibleTypeEnum
     */
    private Integer type;

    /**
     * 补充证明状态
     * @see AddTrustAuditStatusEnum
     */
    private Integer auditStatus;
    //操作人
    private Long operatorId;
    private String operator;
    private Integer isDelete;
    //下发增信时间
    private Date sendTime;
    //用户提交增信时间
    private Date submitTime;
    //运营审核增信时间
    private Date auditTime;
    private Date createTime;
    private Date updateTime;

    /**
     * 这个字段在表中不存在，实际存在的意义是为了兼容页面上的驳回记录按钮是否展示
     * true 代表有驳回记录，展示按钮
     */
    private boolean auditRecord;

    private long workOrderId;
}
