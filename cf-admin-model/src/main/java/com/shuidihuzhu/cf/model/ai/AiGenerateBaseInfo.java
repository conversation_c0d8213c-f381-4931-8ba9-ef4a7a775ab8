package com.shuidihuzhu.cf.model.ai;

import com.shuidihuzhu.cf.enums.ai.AiMockDataEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2024/5/29 2:24 PM
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiGenerateBaseInfo {

    @ApiModelProperty("为谁筹款")
    @AiBaseFieldMapping(
            aiPrompt = "为谁筹款"
    )
    private String raisePatientRelation;

    @AiBaseFieldMapping(
            aiPrompt = "发起人姓名",
            aiMockData = AiMockDataEnum.RAISER_NAME_MOCK
    )
    @ApiModelProperty("发起人姓名")
    private String raiseName;

    @AiBaseFieldMapping(
            aiPrompt = "发起人年龄",
            aiMockData = AiMockDataEnum.AGE_MOCK
    )
    @ApiModelProperty("发起人年龄")
    private Integer raiserAge;

    @AiBaseFieldMapping(
            aiPrompt = "发起人身份"
    )
    @ApiModelProperty("发起人身份")
    private String raiserStanding;

    @AiBaseFieldMapping(
            aiPrompt = "患者姓名",
            aiMockData = AiMockDataEnum.PATIENT_NAME_MOCK
    )
    @ApiModelProperty("患者姓名")
    private String patientName;

    @AiBaseFieldMapping(
            aiPrompt = "患者年龄",
            aiMockData = AiMockDataEnum.AGE_MOCK
    )
    @ApiModelProperty("患者年龄")
    private Integer patientAge;

    @AiBaseFieldMapping(
            aiPrompt = "患者性别"
    )
    @ApiModelProperty("患者性别")
    private String patientGender;

    @AiBaseFieldMapping(
            aiPrompt = "患者职业"
    )
    @ApiModelProperty("患者职业")
    private String patientOccupation;

    @AiBaseFieldMapping(
            aiPrompt = "患者身份"
    )
    @ApiModelProperty("患者身份")
    private String patientIdentity;

    @AiBaseFieldMapping(
            aiPrompt = "患者家庭住址",
            aiMockData = AiMockDataEnum.ADDRESS_MOCK
    )
    @ApiModelProperty("患者家庭住址")
    private String patientAddress;

    @AiBaseFieldMapping(
            aiPrompt = "疾病名称"
    )
    @ApiModelProperty("疾病名称")
    private String medicalDisease;

    @AiBaseFieldMapping(
            aiPrompt = "目标筹款金额"
    )
    @ApiModelProperty("目标筹款金额")
    private Integer targetAmount;

    @AiBaseFieldMapping(
            aiPrompt = "案例类型"
    )
    @ApiModelProperty("案例类型")
    private String accidentType;

    @AiBaseFieldMapping(
            aiPrompt = "患病过程"
    )
    @ApiModelProperty("患病过程")
    private String diseaseProcess;

    @AiBaseFieldMapping(
            aiPrompt = "家庭情况"
    )
    @ApiModelProperty("家庭情况")
    private String familySituation;

    @AiBaseFieldMapping(
            aiPrompt = "其他情况"
    )
    @ApiModelProperty("其他情况")
    private String otherSituation;

    @AiBaseFieldMapping(
            aiPrompt = "使用转发语的人与病患关系"
    )
    @ApiModelProperty("使用转发语的人与病患关系")
    private String forwardRelation;

    @AiBaseFieldMapping(
            aiPrompt = "转发给谁"
    )
    @ApiModelProperty("转发给谁")
    private String forwardForWho;

    @AiBaseFieldMapping(
            aiPrompt = "转发语转发时间"
    )
    @ApiModelProperty("转发语转发时间")
    private String forWardTime;

}
