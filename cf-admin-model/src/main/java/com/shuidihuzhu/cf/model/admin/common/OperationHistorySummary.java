package com.shuidihuzhu.cf.model.admin.common;

import com.shuidihuzhu.cf.enums.admin.common.OperationType;

import java.io.Serializable;
import java.util.Date;

/**
 * Author: wuxinlong
 * Date: 16/10/9 15:25
 */
public class OperationHistorySummary implements Serializable {

	private static final long serialVersionUID = 5190025211625181960L;

	public OperationHistorySummary() {}

	public OperationHistorySummary(OperationType operationType, Integer operatorId, String summary) {
		this.operationType = operationType;
		this.operatorId = operatorId;
		this.summary = summary;
	}

	/**
	 * id
	 */
	private Long id;

	/**
	 * 操作类型
	 */
	private OperationType operationType;

	/**
	 * 操作的详细信息
	 */
	private String summary;

	/**
	 * @see com.shuidihuzhu.cf.admin.auth.model.Login#id
	 */
	private Integer operatorId;

	/**
	 * 记录时间
	 */
	private Date createTime;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public OperationType getOperationType() {
		return operationType;
	}

	public void setOperationType(OperationType operationType) {
		this.operationType = operationType;
	}

	public String getSummary() {
		return summary;
	}

	public void setSummary(String summary) {
		this.summary = summary;
	}

	public Integer getOperatorId() {
		return operatorId;
	}

	public void setOperatorId(Integer operatorId) {
		this.operatorId = operatorId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
}