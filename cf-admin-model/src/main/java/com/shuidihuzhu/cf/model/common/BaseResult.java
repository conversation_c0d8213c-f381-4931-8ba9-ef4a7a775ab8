package com.shuidihuzhu.cf.model.common;

import com.shuidihuzhu.cf.enums.ResultCodeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class BaseResult<T> implements Serializable {
    private static final long serialVersionUID = -1L;

    private BaseResult(){}

    private BaseResult(int code) {
        this.code = code;
    }

    private BaseResult(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private BaseResult(int code, String desc, T data) {
        this.code = code;
        this.desc = desc;
        this.data = data;
    }

    public static BaseResult build(ResultCodeEnum dataCode){
        return new BaseResult(dataCode.getCode(), dataCode.getDesc());
    }

    public static BaseResult build(int code, String desc){
        return new BaseResult(code, desc);
    }

    public static <T> BaseResult<T> build(int code, String desc, T data){
        return new BaseResult(code, desc, data);
    }


    public static <T> BaseResult<T> createSucResult(){
        return new BaseResult<T>(ResultCodeEnum.SUCCESS.getCode());
    }

    public static <T> BaseResult<T> createSucResult(T data){
        return new BaseResult<T>(ResultCodeEnum.SUCCESS.getCode(), ResultCodeEnum.SUCCESS.getDesc(), data);
    }

    public static <T> BaseResult<T> createFailResult() {
        return new BaseResult<>(ResultCodeEnum.FAIL.getCode());
    }

    public static <T> BaseResult<T> createFailResult(ResultCodeEnum codeEnum) {
        return new BaseResult<>(codeEnum.getCode(),codeEnum.getDesc());
    }

    public static <T> BaseResult<T> createFailResult(ResultCodeEnum codeEnum, T data) {
        return new BaseResult<>(codeEnum.getCode(),codeEnum.getDesc(), data);
    }

    public boolean isSuccess() {
        return code == ResultCodeEnum.SUCCESS.getCode();
    }

    public boolean isFail() {
        return !isSuccess();
    }

    private int code;
    private String desc;
    private T data;


    @Override
    public String toString() {
        return "BaseResult{" +
                "code=" + code +
                ", data=" + data +
                ", desc='" + desc + '\'' +
                '}';
    }

}
