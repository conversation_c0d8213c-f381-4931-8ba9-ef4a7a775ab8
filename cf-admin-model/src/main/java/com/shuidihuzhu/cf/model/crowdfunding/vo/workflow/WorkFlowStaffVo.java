package com.shuidihuzhu.cf.model.crowdfunding.vo.workflow;

import com.shuidihuzhu.cf.model.crowdfunding.workflow.WorkFlowStaffStatus;
import com.shuidihuzhu.pf.common.v2.model.pagehelper.PaginationVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-02-13 19:39
 **/
@ApiModel("人员管理-人员信息搜索")
@Data
public class WorkFlowStaffVo {

    @ApiModelProperty("每行显示信息")
    private List<StaffSearchResult> searchResultList;

    @ApiModelProperty("分页信息")
    private PaginationVO paginationVO;

    @ApiModel
    @Data
    public static class StaffSearchResult extends WorkFlowStaffStatus {
        @ApiModelProperty("姓名")
        private String name;
        @ApiModelProperty("组织名称")
        private String staffOrgName;
        @ApiModelProperty("在线小时")
        private long hours;
        @ApiModelProperty("在线分钟")
        private long minutes;
        @ApiModelProperty("在线s")
        private long seconds;
        @ApiModelProperty("未处理的工单数量限制")
        private Integer noHandleLimit;
    }
}
