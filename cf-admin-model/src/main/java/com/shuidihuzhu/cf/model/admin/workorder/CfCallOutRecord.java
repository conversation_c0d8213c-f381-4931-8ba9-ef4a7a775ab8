package com.shuidihuzhu.cf.model.admin.workorder;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class CfCallOutRecord {

    private int totalSize;
    private int validSize;
    private int totalDuration;
    private String name;
    private List<CallOutDetail> callOutDetails;

    public CfCallOutRecord withName(String name) {
        this.name = name;
        return this;
    }

    @Data
    public static class CallOutDetail {

        private int callRecordId;
        private String mobile;

        @ApiModelProperty("客户接通时间")
        private Date answerTime;
        private Date createTime;
        private Date endTime;
        private int duration;

        private String videoUrl;

        private int phoneStatus;

        @ApiModelProperty("录音标签{0: 未选中, 1: 问题录音, 2: 优秀录音}")
        private int callTag;

        @ApiModelProperty("1001:客户挂机 1000:坐席挂机")
        private String endReason;
    }
}
