package com.shuidihuzhu.cf.model.pay.wx;

import lombok.Data;

import java.util.Date;

/**
 * @Author: lian<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020/8/5 21:06
 */

/**
 * 微信支付有礼图片上传记录
 */
@Data
public class CfWxCouponImgRecord {
    /**
     * 主键
     */
    private long id;

    /**
     * 商户商户编号不能为空
     */
    private int wxBizType;

    /**
     * 原始图片地址
     */
    private String originImgUrl;

    /**
     * 支付活动图片地址
     */
    private String couponImgUrl;

    /**
     * 支付活动图片名称
     */
    private String couponImgName;

    /**
     * 上传执行结果
     */
    private String uploadMsg;

    /**
     * 图片上传人user id
     */
    private long operatorUserId;

    /**
     * 图片上传人user name
     */
    private String operatorUserName;

    /**
     * 逻辑删除
     */
    private int isDelete;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    public CfWxCouponImgRecord() {
    }

    public CfWxCouponImgRecord(int wxBizType, String originImgUrl, String couponImgName,
                               long operatorUserId, String operatorUserName) {
        this.wxBizType = wxBizType;
        this.originImgUrl = originImgUrl;
        this.couponImgName = couponImgName;
        this.operatorUserId = operatorUserId;
        this.operatorUserName = operatorUserName;
    }
}