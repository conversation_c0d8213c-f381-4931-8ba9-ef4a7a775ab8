package com.shuidihuzhu.cf.model.admin;

import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @DATE 2019/12/11
 */
@Data
public class CfQuestionnaire {

    public final static int q_status_submit = 1;

    public final static int q_status_unSubmit = 0;



    private long id;

    private long recordId;

    private String qid;

    private String source;

    private String channel;

    private Date sendTime;

    private String sendTimeStr;

    private Date submitTime;

    private String submitTimeStr;

    private int caseId;

    private String org;

    private String name;

    private String mobile;
    private NumberMaskVo mobileMask;

    private long userId;

    private String card;

    private int qStatus;

    private int operatorId;

    private String operatorName;

    private Date updateTime;

    private String updateTimeStr;

    private String content;

    private String comment;

    private String wenjuanURl;

    private String wenjuanId;

    private String qname;

    private String startAnsweringTime;

    private String endTime;

    private Date createTime;

}
