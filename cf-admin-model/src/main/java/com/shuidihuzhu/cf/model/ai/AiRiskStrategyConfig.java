package com.shuidihuzhu.cf.model.ai;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: ai判断内容风险策略配置
 * @Author: pan<PERSON><PERSON><PERSON>
 * @Date: 2025/5/20 17:41
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiRiskStrategyConfig {

    @ApiModelProperty("场景类型")
    private String sceneType;

    @ApiModelProperty("风险因子合集")
    private String riskFactor;

    @ApiModelProperty("大模型类型")
    private Integer modelType;

    @ApiModelProperty("提示词配置")
    private String judgePrompt;

    @ApiModelProperty("判断类型")
    private Integer judgeType;

}
