package com.shuidihuzhu.cf.model.kwai;

import com.google.common.base.Splitter;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.ValidationException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/7/28  16:27
 */
@Data
public class KwaiAppShortIdeosCaseMount {

    private long id;

    private String ids;
    private List<Long> idList;
    private List<String> infoUuidList;
    private String infoUuid;
    private long operatorId;
    private String operatorName;
    private String mobile;
    private String encryptMobile;
    private int current;
    private int pageSize;
    private int total;
    private Date createTime;
    private String createTimeDesc;
    private Map<String, Object> pageMap;

    public void splitter() {
        if (StringUtils.isEmpty(ids)) {
            throw new ValidationException("id为空");
        }

        this.idList = Splitter.on(",").splitToList(ids).stream().map(Long::parseLong).collect(Collectors.toList());
    }

    public void validateId(){
        if(id <= 0){
            throw new ValidationException("id为空");
        }
    }

    public void validateEncryptMobile() {
        if (StringUtils.isEmpty(encryptMobile)) {
            throw new ValidationException("手机号为空");
        }
    }

}
