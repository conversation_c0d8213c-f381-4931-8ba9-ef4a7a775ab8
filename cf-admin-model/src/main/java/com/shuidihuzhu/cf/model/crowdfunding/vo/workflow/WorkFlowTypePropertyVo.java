package com.shuidihuzhu.cf.model.crowdfunding.vo.workflow;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class WorkFlowTypePropertyVo {
    private long typeId;
    @ApiModelProperty(value = "系统分配时间")
    private String systemAllocationTime = StringUtils.EMPTY;

    @ApiModelProperty(value = "首次系统分配处理人")
    private String firstSystemAssignsUser = StringUtils.EMPTY;

    @ApiModelProperty(value = "首次处理时间")
    private String firstHandleTime = StringUtils.EMPTY;

    public WorkFlowTypePropertyVo() {

    }

    public WorkFlowTypePropertyVo(long typeId, String systemAllocationTime,
                                  String firstSystemAssignsUser, String firstHandleTime) {
        this.typeId = typeId;
        this.systemAllocationTime = systemAllocationTime;
        this.firstSystemAssignsUser = firstSystemAssignsUser;
        this.firstHandleTime = firstHandleTime;
    }
}
