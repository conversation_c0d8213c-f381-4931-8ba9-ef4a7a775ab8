package com.shuidihuzhu.cf.model.crowdfunding;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2018/12/12
 */
@Data
public class BaseInfoModel {


    //案例信息
    private CrowdfundingInfo crowdfundingInfo;

    //材料及状态
    private List<CrowdfundingInfoStatus> list;

    //案例结束状态
    private String finishMsg;

    //前置审核状态
    private int firstApproveStatus;

    private List<CrowdfundingAttachmentVo> attachments = Lists.newArrayList();

    private String headPictureUrl;

}
