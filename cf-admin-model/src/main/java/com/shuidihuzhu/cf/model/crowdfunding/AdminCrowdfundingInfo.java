package com.shuidihuzhu.cf.model.crowdfunding;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.sql.Timestamp;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/3/2.
 */
@Data
public class AdminCrowdfundingInfo {
    /**
     * 发起时间
     */
    private Timestamp createTime;
    private int id;
    /**
     * 志愿者类型
     */
    private int volunteerType;
    /**
     * 志愿者姓名
     */
    private String volunteerName;
    /**
     * 志愿者手机号
     */
    private String volunteerMobile;
    /**
     * 所属省份id
     */
    private int provinceId;
    /**
     * 所属城市id
     */
    private int cityId;
    /**
     * 所属区县id
     */
    private int countyId;
    /**
     * 发起人手机号
     */
    private String mobile;
    /**
     * 标题
     */
    private String title;
    /**
     * 筹款金额,单位:分
     */
    private int targetAmount;
    /**
     * 已筹金额,单位:分
     */
    private int amount;
    /**
     * 捐助次数
     */
    private int donationCount;
    /**
     * 证实人数
     */
    private int verifyCount;
    /**
     * 转发数量
     */
    private int shareCount;
    /**
     * 志愿者唯一标识
     */
    private String volunteerUniqueCode;
    /**
     * 案例的uuid
     */
    private String infoId;
    /**
     * 发起筹款人的id
     */
    private long userId;
    /**
     * 省份名称
     */
    private String provinceName;
    /**
     * 城市名称
     */
    private String cityName;
    /**
     * 区县名称
     */
    private String countyName;

    /**
     * 案例发起方式：BD引导发起，兼职发起，拎包发起，用户自己发起
     */
    private String caseMethod;
    /**
     * 案例来源渠道：扫楼、转介绍、物料案例、其他
     */
    private String caseSource;
    /**
     * 该用户是否首次众筹：是、否、不确定
     */
    private String firstRaise;
    /**
     * 统计该案例捐款人为首次捐款数量
     */
    private Integer caseFirstCount;

    private String email;

    private String mis;
    // 分组名称
    private String groupName;
    // 区域名称
    private String areaName;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

}
