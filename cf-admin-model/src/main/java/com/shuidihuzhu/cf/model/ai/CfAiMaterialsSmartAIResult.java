package com.shuidihuzhu.cf.model.ai;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.model.crowdfunding.ai.LayOutField;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2022/12/8 15:53
 * @Description:
 */
@Data
public class CfAiMaterialsSmartAIResult {
    private int caseId;
    private long workOrderId;
    private String aiContentWorkOrderResult;
    private String titleDiseaseName;
    private String contentDiseaseName;
    private List<LayOutField> aiContentWorkOrderResultList;

    public List<LayOutField> getAiContentWorkOrderResultList() {
        if (StringUtils.isNotEmpty(getAiContentWorkOrderResult())){
            return JSON.parseArray(getAiContentWorkOrderResult(), LayOutField.class);
        }
        return aiContentWorkOrderResultList;
    }
}
