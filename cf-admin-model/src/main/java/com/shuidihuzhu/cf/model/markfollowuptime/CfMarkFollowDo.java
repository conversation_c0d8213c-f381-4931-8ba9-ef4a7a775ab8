package com.shuidihuzhu.cf.model.markfollowuptime;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/8/13  4:47 下午
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CfMarkFollowDo {
    @ApiModelProperty("跟进id")
    private long id;
    @ApiModelProperty("任意类型的id")
    private long bizId;
    @ApiModelProperty("操作人")
    private int operatorId;
    @ApiModelProperty("工单类型")
    private int orderType;
    @ApiModelProperty("跟进时间")
    private Date targetTime;
    @ApiModelProperty("版本")
    private long version;
    @ApiModelProperty("是否失效")
    private int disable;
    @ApiModelProperty("是否已处理")
    private int done;

}
