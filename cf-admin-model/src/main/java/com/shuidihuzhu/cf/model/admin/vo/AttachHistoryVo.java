package com.shuidihuzhu.cf.model.admin.vo;

import com.shuidihuzhu.wx.grpc.client.common.QueryAttachHistoryV2;
import com.shuidihuzhu.wx.grpc.model.QueryAttachHistoryV2Dto;
import lombok.Data;

@Data
public class AttachHistoryVo {
    private int id;
    private int contentId;
    private String title;
    private String content;
    private String description;
    private String url;
    private String picTitle;
    private String picUrl;
    private String pagePath;
    private String appid;
    private int greetingType;
    private int isDelete;
    private long creatTime;
    private String attachScene;

    public static AttachHistoryVo convertAttachHistoryVo(QueryAttachHistoryV2 queryAttachHistoryV2){
        AttachHistoryVo attachHistoryVo = null;
        if (queryAttachHistoryV2 != null) {
            attachHistoryVo = new AttachHistoryVo();
            attachHistoryVo.setId(queryAttachHistoryV2.getId());
            attachHistoryVo.setContentId(queryAttachHistoryV2.getContentId());
            attachHistoryVo.setTitle(queryAttachHistoryV2.getTitle());
            attachHistoryVo.setContent(queryAttachHistoryV2.getContent());
            attachHistoryVo.setDescription(queryAttachHistoryV2.getDescription());
            attachHistoryVo.setUrl(queryAttachHistoryV2.getUrl());
            attachHistoryVo.setPicUrl(queryAttachHistoryV2.getPicUrl());
            attachHistoryVo.setPagePath(queryAttachHistoryV2.getPagePath());
            attachHistoryVo.setAppid(queryAttachHistoryV2.getAppid());
            attachHistoryVo.setGreetingType(queryAttachHistoryV2.getGreetingType());
            attachHistoryVo.setIsDelete(queryAttachHistoryV2.getIsDelete());
            attachHistoryVo.setCreatTime(queryAttachHistoryV2.getCreatTime());
            attachHistoryVo.setAttachScene(queryAttachHistoryV2.getAttachScene());
        }
        return attachHistoryVo;
    }

    public static AttachHistoryVo convertAttachHistoryVo(QueryAttachHistoryV2Dto queryAttachHistoryV2){
        AttachHistoryVo attachHistoryVo = null;
        if (queryAttachHistoryV2 != null) {
            attachHistoryVo = new AttachHistoryVo();
            attachHistoryVo.setId(queryAttachHistoryV2.getId());
            attachHistoryVo.setContentId(queryAttachHistoryV2.getContentId());
            attachHistoryVo.setTitle(queryAttachHistoryV2.getTitle());
            attachHistoryVo.setContent(queryAttachHistoryV2.getContent());
            attachHistoryVo.setDescription(queryAttachHistoryV2.getDescription());
            attachHistoryVo.setUrl(queryAttachHistoryV2.getUrl());
            attachHistoryVo.setPicUrl(queryAttachHistoryV2.getPicUrl());
            attachHistoryVo.setPagePath(queryAttachHistoryV2.getPagePath());
            attachHistoryVo.setAppid(queryAttachHistoryV2.getAppid());
            attachHistoryVo.setGreetingType(queryAttachHistoryV2.getGreetingType());
            attachHistoryVo.setIsDelete(queryAttachHistoryV2.getIsDelete());
            attachHistoryVo.setCreatTime(queryAttachHistoryV2.getCreatTime());
            attachHistoryVo.setAttachScene(queryAttachHistoryV2.getAttachScene());
        }
        return attachHistoryVo;
    }
}
