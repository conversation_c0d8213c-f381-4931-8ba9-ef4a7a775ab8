package com.shuidihuzhu.cf.model.event;

import lombok.Getter;

/**
 * <AUTHOR>
 * @DATE 2020/3/18
 */
@Getter
public enum  CailiaoCondition {


    condition_1(1,"着急提现"),
    condition_2(2,"多次驳回"),
    condition_3(3,"高金额案例"),
    condition_4(4,"命中特殊驳回原因案例"),
    condition_5(5,"延后等待审核"),

    /**
     * https://wiki.shuiditech.com/pages/viewpage.action?pageId=538609756
     */
    condition_11(11,"高金额未提材审"),
    condition_12(12,"材审驳回后未提交"),

    ;


    private int code;

    private String msg;


    CailiaoCondition(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
