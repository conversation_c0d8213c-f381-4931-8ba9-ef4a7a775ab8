package com.shuidihuzhu.cf.model.admin;

import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import com.shuidihuzhu.cf.model.crowdfunding.CfHospitalAuditInfo;

import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CfHospitalAuditInfoExt extends CfHospitalAuditInfo {
    @ApiModelProperty("发起人账户内手机号")
    private String raiserMobile;
    @ApiModelProperty("发起人账户内手机号脱敏")
    private NumberMaskVo raiserMobileMask;
    private Date createTime;
    private Date updateTime;
}
