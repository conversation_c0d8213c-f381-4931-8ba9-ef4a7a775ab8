package com.shuidihuzhu.cf.model.crowdfunding;

import org.apache.commons.lang.builder.ToStringBuilder;

import java.util.List;

/**
 * Created by Ahrievil on 2017/7/26
 */
public class CfRefuseReasonTag {
    private int id;
    private String describe;
    private int dataType;
    @Deprecated
    private String reasonIds;
    private int isDelete;
    private List<CfRefuseReasonEntity> entityList;

    private int dataStep;

    public CfRefuseReasonTag() {
    }

    public CfRefuseReasonTag(String describe, int dataType, String reasonIds) {
        this.describe = describe;
        this.dataType = dataType;
        this.reasonIds = reasonIds;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }

    public int getDataType() {
        return dataType;
    }

    public void setDataType(int dataType) {
        this.dataType = dataType;
    }

    public String getReasonIds() {
        return reasonIds;
    }

    public void setReasonIds(String reasonIds) {
        this.reasonIds = reasonIds;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }

    public List<CfRefuseReasonEntity> getEntityList() {
        return entityList;
    }

    public void setEntityList(List<CfRefuseReasonEntity> entityList) {
        this.entityList = entityList;
    }


    public int getDataStep() {
        return dataStep;
    }

    public void setDataStep(int dataStep) {
        this.dataStep = dataStep;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
