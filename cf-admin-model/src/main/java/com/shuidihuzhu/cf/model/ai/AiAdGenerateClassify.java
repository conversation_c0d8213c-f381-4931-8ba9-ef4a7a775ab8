package com.shuidihuzhu.cf.model.ai;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description:
 * @Author: pan<PERSON><PERSON><PERSON>
 * @Date: 2025/6/23 16:50
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiAdGenerateClassify {

    private List<String> scenarioList;

    private List<String> empathyList;

}
