package com.shuidihuzhu.cf.model.domain;

import lombok.Data;

import java.util.Date;

/**
 * Created by dongcf on 2021/5/21
 */
@Data
public class CfNewDomainOperationNeedMsg {

    /**
     * 顾问姓名
     */
    private String misName;
    /**
     * 顾问mis账号
     */
    private String mis;
    /**
     * 顾问架构
     */
    private String orgName;
    /**
     * 患者姓名
     */
    private String patientName;
    /**
     * 案例发起时间
     */
    private Date caseCreateTime;
    /**
     * 案例infoId
     */
    private String infoUuid;
    /**
     * 被封or下掉域名
     */
    private String oldDomain;
    /**
     * 用来替换的域名
     */
    private String newDomain;

}
