package com.shuidihuzhu.cf.model.message;

import lombok.Data;

import java.util.Date;

@Data
public class CfWxArticleTotal {
    private long id;
    private int thirdType;
    //群发日期(与statDate区分)
    private String refDate;
    //文章群发 msg_data_id
    private String msgDataId;
    //文章的脚标
    private String index;
    //文章标题
    private String title;
    //文章数据统计日期
    private String statDate;
    private int targetUser;
    private int pageReadUser;
    private int pageReadCount;
    private int oriPageReadUser;
    private int oriPageReadCount;
    private int shareUser;
    private int shareCount;
    private int addToFavUser;
    private int addToFavCount;
    private int pageFromSessionReadUser;
    private int pageFromSessionReadCount;
    private int pageFromHistMsgReadUser;
    private int pageFromHistMsgReadCount;
    private int pageFromFeedReadUser;
    private int pageFromFeedReadCount;
    private int pageFromFriendsReadUser;
    private int pageFromFriendsReadCount;
    private int pageFromOtherReadUser;
    private int pageFromOtherReadCount;
    private int feedShareFromSessionUser;
    private int feedShareFromSessionCnt;
    private int feedShareFromFeedUser;
    private int feedShareFromFeedCnt;
    private int feedShareFromOtherUser;
    private int feedShareFromOtherCnt;
    private int isDelete;
    private Date createTime;
    private Date updateTime;

}
