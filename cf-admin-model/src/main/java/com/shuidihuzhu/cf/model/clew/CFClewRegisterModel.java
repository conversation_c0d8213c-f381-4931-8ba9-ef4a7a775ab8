package com.shuidihuzhu.cf.model.clew;

import lombok.*;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class CFClewRegisterModel {
    String displayId;//线索ID
    String phone;//电话号码
    Integer status;//案例状态
    String diseaseName;//疾病
    String fundraisingObject;//为谁筹款
    String isFollow;//是否关注水滴筹
    String primaryChannel;//渠道名称
    Integer registerMode;//登记方式
    String registerName;//登记人
    String clewCreateTime;//线索登记时间
    String cfCreateTime;//案例发起时间
    String specialMark;//备注
}
