package com.shuidihuzhu.cf.model.admin.vo;

import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 返还款相关信息工单VO
 */
@Data
@ApiModel("返还款相关信息工单VO")
public class AdminWorkOrderMoneyBackExtVO {
    
    @ApiModelProperty("工单ID")
    private Long workOrderId;
    
    @ApiModelProperty("案例ID")
    private Integer caseId;
    
    @ApiModelProperty("金额 单位元")
    private String amountStr;

    @ApiModelProperty("手机号")
    private String mobile;

    private String title;

    private NumberMaskVo mobileMask;
} 