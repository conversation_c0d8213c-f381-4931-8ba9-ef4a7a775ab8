package com.shuidihuzhu.cf.model.admin;

import java.sql.Timestamp;

/**
 * Created by ahrievil on 2017/3/31.
 */
public class CfAndroidChannel {
    private int id;
    private String selectDate;
    private String channelName;
    private int newUsers;
    private int activeUsers;
    private int startUps;
    private String times;
    private double retentionRate;
    private int lineNum;
    private Timestamp date_created;
    private Timestamp last_modified;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getSelectDate() {
        return selectDate;
    }

    public void setSelectDate(String selectDate) {
        this.selectDate = selectDate;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public int getNewUsers() {
        return newUsers;
    }

    public void setNewUsers(int newUsers) {
        this.newUsers = newUsers;
    }

    public int getActiveUsers() {
        return activeUsers;
    }

    public void setActiveUsers(int activeUsers) {
        this.activeUsers = activeUsers;
    }

    public int getStartUps() {
        return startUps;
    }

    public void setStartUps(int startUps) {
        this.startUps = startUps;
    }

    public String getTimes() {
        return times;
    }

    public void setTimes(String times) {
        this.times = times;
    }

    public double getRetentionRate() {
        return retentionRate;
    }

    public void setRetentionRate(double retentionRate) {
        this.retentionRate = retentionRate;
    }

    public int getLineNum() {
        return lineNum;
    }

    public void setLineNum(int lineNum) {
        this.lineNum = lineNum;
    }

    public Timestamp getDate_created() {
        return date_created;
    }

    public void setDate_created(Timestamp date_created) {
        this.date_created = date_created;
    }

    public Timestamp getLast_modified() {
        return last_modified;
    }

    public void setLast_modified(Timestamp last_modified) {
        this.last_modified = last_modified;
    }
}
