package com.shuidihuzhu.cf.model.message;

import com.shuidi.weixin.mp.bean.datacube.WxDataCubeArticleResult;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.Date;

@Data
public class CfWxArticleSummary {
    private long id;
    private int thirdType;
    //文章数据统计日期
    private String refDate;
    //文章群发 msg_data_id
    private String msgDataId;
    //文章的脚标
    private String index;
    //文章标题
    private String title;

    private int pageReadUser;
    private int pageReadCount;
    private int oriPageReadUser;
    private int oriPageReadCount;
    private int shareUser;
    private int shareCount;
    private int addToFavUser;
    private int addToFavCount;
    private int isDelete;
    private Date createTime;
    private Date updateTime;

    public CfWxArticleSummary() {

    }

    public CfWxArticleSummary(WxDataCubeArticleResult wxDataCubeArticleResult, int thirdType) {
        if (null != wxDataCubeArticleResult) {
            this.thirdType = thirdType;
            this.refDate = wxDataCubeArticleResult.getRefDate();
            String msgId = wxDataCubeArticleResult.getMsgId();
            if (!StringUtils.isEmpty(msgId) && msgId.contains("_")) {
                String[] msgIds = msgId.split("_");
                this.msgDataId = msgIds[0];
                this.index = msgIds[1];
            }
            this.title = wxDataCubeArticleResult.getTitle();
            this.pageReadUser = wxDataCubeArticleResult.getIntPageReadUser();
            this.pageReadCount = wxDataCubeArticleResult.getIntPageReadCount();
            this.oriPageReadUser = wxDataCubeArticleResult.getOriPageReadUser();
            this.oriPageReadCount = wxDataCubeArticleResult.getOriPageReadCount();
            this.shareUser = wxDataCubeArticleResult.getShareUser();
            this.shareCount = wxDataCubeArticleResult.getShareCount();
            this.addToFavUser = wxDataCubeArticleResult.getAddToFavUser();
            this.addToFavCount = wxDataCubeArticleResult.getAddToFavCount();
        }
    }
}
