package com.shuidihuzhu.cf.model.crowdfunding;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * @author: fengxuan
 * @create 2019-11-06 22:02
 **/
@Data
@Slf4j
@NoArgsConstructor
@ApiModel("疾病详情")
public class CfDiseaseManagerDO {

    @ApiModelProperty("manager id")
    private long id;

    @ApiModelProperty("科室id")
    private long diseaseClassifyId;

    @ApiModelProperty("标准名称")
    private String standardName;

    @ApiModelProperty("医学名称")
    private String medicalName;

    @ApiModelProperty("口头称")
    private String normalName;

    @ApiModelProperty("治疗方案")
    private int treatmentProject;

    @ApiModelProperty("自定义治疗方案")
    private String customTreatment;

    @ApiModelProperty("治疗最小费用")
    private int minTreatmentFee;

    @ApiModelProperty("治疗最大费用")
    private int maxTreatmentFee;

    @ApiModelProperty("发起方式,可发起筹款/不可发起筹款/需要医学专家审核/需要人工二次审核")
    private int raiseType;

    private Date createTime;

    @JsonIgnore
    private Date updateTime;

    private boolean delete;



    @Getter
    public enum RaiseTypeEnum {
        CAN_NOT_RAISE(1, "不可发起筹款"),
        CAN_RAISE(2, "可发起筹款"),
        NEED_PROFESSIONAL_VERIFY(3, "需要医学专家审核"),
        NEED_DOUBLE_VERIFY(4, "需要人工二次审核"),
        ;
        int code;
        String desc;

        RaiseTypeEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static RaiseTypeEnum findByCode(int code) {
            for (RaiseTypeEnum value : RaiseTypeEnum.values()) {
                if (value.getCode() == code) {
                    return value;
                }
            }
            return null;
        }
    }

    @Getter
    public enum TreatmentProjectEnum {
        no_limit(1, "无要求"),
        need_operation(2, "做手术"),
        steel_needle(3, "取钢针"),
        custom(4, "自定义"),
        ;
        int code;
        String desc;

        TreatmentProjectEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static TreatmentProjectEnum findByCode(int code) {
            for (TreatmentProjectEnum value : TreatmentProjectEnum.values()) {
                if (value.getCode() == code) {
                    return value;
                }
            }
            return null;
        }
    }
}
