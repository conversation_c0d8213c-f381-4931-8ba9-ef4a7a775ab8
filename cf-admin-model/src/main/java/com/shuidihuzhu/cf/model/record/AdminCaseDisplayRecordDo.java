package com.shuidihuzhu.cf.model.record;

import com.shuidihuzhu.cf.client.adminpure.model.CaseDisplaySetting;
import lombok.Data;

import java.sql.Timestamp;

@Data
public class AdminCaseDisplayRecordDo {
    private int id;
    private int caseId;
    private String before;
    private String after;
    private String uniqueCode;
    private long operatorId;
    private int updateChannel;
    private String reason;
    private Timestamp createTime;
    private Timestamp updateTime;
    private int isDelete;
}
