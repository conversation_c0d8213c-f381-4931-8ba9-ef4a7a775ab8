package com.shuidihuzhu.cf.model.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-05-21 14:33
 **/
@Data
@ApiModel
public class CfReportOfficialLetter {
    @ApiModelProperty("id")
    private long id;
    @ApiModelProperty("案例id")
    private int caseId;
    @ApiModelProperty("公函类型")
    private String letterType;
    @ApiModelProperty("快递编号")
    private String num;
    @ApiModelProperty("图片")
    private String images;
    @ApiModelProperty("公函状态")
    private int letterStatus;
    @ApiModelProperty("备注")
    private String comment;
    @ApiModelProperty("通知人姓名")
    private String name;
    @ApiModelProperty("通知时间")
    private Timestamp createTime;
    @ApiModelProperty("处理时间")
    private Timestamp updateTime;

}
