package com.shuidihuzhu.cf.model.markfollowuptime;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CfMarkFollowRecordVO {
    @ApiModelProperty("跟进id")
    private long id;
    @ApiModelProperty("操作人")
    private int operatorId;
    @ApiModelProperty("操作人姓名")
    private String operatorName;
    @ApiModelProperty("跟进时间")
    private String  targetTime;
}
