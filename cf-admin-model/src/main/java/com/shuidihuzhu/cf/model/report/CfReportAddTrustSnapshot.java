package com.shuidihuzhu.cf.model.report;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.model.crowdfunding.CfCredibleInfoDO;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportAddTrust;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportCommitmentInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfReportAddTrustDisposeVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfReportAddTrustVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfReportCredibleInfoVO;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Objects;

/**
 * @author: lixiaoshuang
 * @create: 2020-03-26 00:47
 **/
@Data
public class CfReportAddTrustSnapshot {
    /**
     * id
     */
    private long id;
    /**
     * 增信id
     */
    private long addTrustId;
    /**
     * 增信审核状态
     */
    private int auditStatus;
    /**
     * 增信信息快照
     */
    private String addTrustSnapshot;
    /**
     * 增信vo
     */
    private CfReportCredibleInfoVO cfReportCredibleInfoVO;


    public void buildAddTrustSnapshot(CfReportAddTrust cfReportAddTrust, CfCredibleInfoDO cfCredibleInfoDO,
                                      CfReportCommitmentInfo cfReportCommitmentInfo, List<CfReportAddTrustDisposeVo> disposeAction) {
        if (Objects.isNull(cfReportCommitmentInfo)) {
            cfReportCommitmentInfo = new CfReportCommitmentInfo();
        }
        this.cfReportCredibleInfoVO = new CfReportCredibleInfoVO();
        if (cfReportAddTrust != null) {
            BeanUtils.copyProperties(cfReportAddTrust, cfReportCredibleInfoVO);
            cfReportCredibleInfoVO.setCfReportCommitmentInfo(cfReportCommitmentInfo);
        }
        if (cfCredibleInfoDO != null) {
            cfReportCredibleInfoVO.setSendTime(cfCredibleInfoDO.getSendTime());
            cfReportCredibleInfoVO.setAuditTime(cfCredibleInfoDO.getAuditTime());
            cfReportCredibleInfoVO.setSubmitTime(cfCredibleInfoDO.getSubmitTime());
            cfReportCredibleInfoVO.setMobile(cfCredibleInfoDO.getMobile());
        }
        this.cfReportCredibleInfoVO.setDisposeAction(disposeAction);
        this.addTrustSnapshot = JSON.toJSONString(cfReportCredibleInfoVO);
    }
}
