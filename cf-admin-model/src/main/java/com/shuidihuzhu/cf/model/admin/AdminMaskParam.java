package com.shuidihuzhu.cf.model.admin;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 掩码查看参数
 * @Author: pan<PERSON><PERSON><PERSON>
 * @Date: 2022/7/5 8:03 下午
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdminMaskParam {

    private Long id;

    private Integer caseId;

    private Long userId;

    private String infoId;

    private Integer maskCodeType;

    private String result;

}
