package com.shuidihuzhu.cf.model.crowdfunding;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * Created by ahrie<PERSON> on 2017/6/30.
 */
public class AdminWxMessageTemplate {

    public static final String KEY_FIRST = "first";

    public static final String KEY_KEYWORD1 = "keyword1";

    public static final String KEY_KEYWORD2 = "keyword2";

    public static final String KEY_KEYWORD3 = "keyword3";

    public static final String KEY_KEYWORD4 = "keyword4";

    public static final String KEY_KEYWORD5 = "keyword5";

    public static final String KEY_KEYWORD6 = "keyword6";

    public static final String KEY_REMARK = "remark";

    public static List<String> getAll() {
        List<String> list = Lists.newArrayList();
        list.add(AdminWxMessageTemplate.KEY_FIRST);
        list.add(AdminWxMessageTemplate.KEY_KEYWORD1);
        list.add(AdminWxMessageTemplate.KEY_KEYWORD2);
        list.add(AdminWxMessageTemplate.KEY_KEYWORD3);
        list.add(AdminWxMessageTemplate.KEY_KEYWORD4);
        list.add(AdminWxMessageTemplate.KEY_KEYWORD5);
        list.add(AdminWxMessageTemplate.KEY_KEYWORD6);
        list.add(AdminWxMessageTemplate.KEY_REMARK);
        return list;
    }
}
