package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.CfReportInfoSourceEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfReportPageEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfReportRelationEnum;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/12/16 上午10:18
 * @desc
 */
@Data
public class AdminReportProblemAnswerDetail {
    //当保存筹款方答案时这个id需要回传save-fundraiser-communicater-record这个接口的answerId入参
    private Integer id;
    private Integer caseId;

    /**
     * 当是筹款方的联系人时(type=2)，reportId=0
     * @see CfReportPageEnum
     */
    private Integer reportId;

    /**
     * 质疑方答案还是筹款方答案
     * @see CfReportPageEnum
     */
    private Integer type;

    /**
     * 与患者关系的key
     * 与患者关系的value
     * 根据是质疑方还是筹款方选择不同的关系类型
     * @see CfReportRelationEnum
     */
    private Integer relationKey;
    private String relationValue;

    /**
     * 本次沟通对象
     */
    private String connectObject;

    /**
     * 信息来源的key
     * 信息来源的vaule
     * @see CfReportInfoSourceEnum
     */
    private Integer infoSourceKey;
    private String infoSourceValue;

    //备忘录：运营和用户沟通时记录的东西，相当于草稿
    private String memo;
    /**
     * 风险类型的答案详情
     * @see List<AdminReportProblemAnswer>
     */
    private String answerDetail;
    private Long operatorId;
    private Date createTime;
    private Date updateTime;
    private String problemOptions;
    private String mandatoryInfo;
    private boolean isNewStrategy;
}
