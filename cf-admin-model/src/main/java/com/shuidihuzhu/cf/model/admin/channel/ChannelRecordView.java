package com.shuidihuzhu.cf.model.admin.channel;

import java.io.Serializable;

/**
 * Created by wux<PERSON><PERSON> on 5/27/16.
 */
public class ChannelRecordView implements Serializable {
    
	private static final long serialVersionUID = -7258342236553293661L;

	private Integer id;

    private String channel;

    private String descr;

    private Short type;

    private String url;

    private Integer oprid;

    private Integer atime;

    private Integer wxMpType;

    private String group;

    private String operator;
    
	private CfChannelGroup cfChannelGroup;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getDescr() {
        return descr;
    }

    public void setDescr(String descr) {
        this.descr = descr;
    }

    public Short getType() {
        return type;
    }

    public void setType(Short type) {
        this.type = type;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getOprid() {
        return oprid;
    }

    public void setOprid(Integer oprid) {
        this.oprid = oprid;
    }

    public Integer getAtime() {
        return atime;
    }

    public void setAtime(Integer atime) {
        this.atime = atime;
    }

    public Integer getWxMpType() {
        return wxMpType;
    }

    public void setWxMpType(Integer wxMpType) {
        this.wxMpType = wxMpType;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

	public CfChannelGroup getCfChannelGroup() {
		return cfChannelGroup;
	}

	public void setCfChannelGroup(CfChannelGroup cfChannelGroup) {
		this.cfChannelGroup = cfChannelGroup;
	}
    
}

