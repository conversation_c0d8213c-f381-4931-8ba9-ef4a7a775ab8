package com.shuidihuzhu.cf.model.admin.rule;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.enums.rule.RiskRuleCondition;
import com.shuidihuzhu.cf.enums.rule.RiskRuleResult;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2020/1/2
 */
@Data
public class RiskRuleModel {

    public final static int million_500 = 5000000;

    public final static int million_300 = 3000000;

    public final static int million_200 = 2000000;

    public final static int million_250 = 2500000;

    public final static int million_100 = 1000000;

    public final static int num_1 = 1;

    public final static int num_2 = 2;

    public final static int num_3 = 3;

    public final static int hundred_thousand_3 = 300000;

    public final static int hundred_thousand_2 = 200000;

    public final static List<RiskRuleResult> raise = Lists.newArrayList(RiskRuleResult.A_0);


    //判断方式
    private RiskRuleCondition riskRuleCondition;

    //阈值
    private List<Integer> threshold;

    //需要判断的值
    private int value;

    //命中的结果
    private List<RiskRuleResult> result;

    //二级条件有  继续判断
    private RiskRuleModel riskRuleModel;


    public static RiskRuleModel strategy(int value,int threshold,RiskRuleCondition condition,List<RiskRuleResult> result){

        RiskRuleModel r = new RiskRuleModel();
        r.setValue(value);
        r.setThreshold(Lists.newArrayList(threshold));
        r.setRiskRuleCondition(condition);
        r.setResult(result);

        return r;
    }

    public static RiskRuleModel strategy(int value,List<Integer> thresholds,RiskRuleCondition condition,List<RiskRuleResult> result){

        RiskRuleModel r = new RiskRuleModel();
        r.setValue(value);
        r.setThreshold(thresholds);
        r.setRiskRuleCondition(condition);
        r.setResult(result);

        return r;
    }

}
