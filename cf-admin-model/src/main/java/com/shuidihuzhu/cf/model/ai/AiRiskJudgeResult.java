package com.shuidihuzhu.cf.model.ai;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: ai判断风险结果
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/5/21 14:58
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiRiskJudgeResult {

    @ApiModelProperty("是否有风险")
    private String isHitRisk;

    @ApiModelProperty("判断依据")
    private String judgeReason;
}
