package com.shuidihuzhu.cf.model.cfOperatingProfile;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Data
public class CfOperatingProfileSettings {

    private int id;
    @JsonIgnore
    private int rank;
    @JsonIgnore
    private long userSize;
    private String content;
    private int parentId;
    @JsonIgnore
    private int profileType;
    private int isDelete;
    @JsonIgnore
    private Date createTime;
    @JsonIgnore
    private Date updateTime;

    private int canDelete;
    private int canDisable;

    // 二级标签的属性
    private List<String> propertyList;

    private ProfileProblemSettings problemSettings;

    @Data
    public static class ProfileProblemSettings {
        private long profileId;

        private String moduleContent;
        private int moduleId;

        private String problemContent;
        private int problemId;

        private String remark;
        private int remarkId;

        @JsonIgnore
        private int userId;

        private int type;

    }

    @Data
    @NoArgsConstructor
    public static class ReportProblemSettingsResult {

        private String moduleContent;
        private int moduleId;

        private List<ReportProblemName> problemNames;

        public ReportProblemSettingsResult(String moduleContent, int moduleId) {
            this.moduleContent = moduleContent;
            this.moduleId = moduleId;
        }
    }

    @Data
    public static class ReportProblemName {
        private String problemContent;
        private int problemId;

        private List<ReportRemark> reportRemarks;

        public ReportProblemName(String problemContent, int problemId) {
            this.problemContent = problemContent;
            this.problemId = problemId;
        }
    }

    @Data
    public static class ReportRemark {
        private String remark;
        private int remarkId;

        public ReportRemark(String remark, int remarkId) {
            this.remark = remark;
            this.remarkId = remarkId;
        }
    }

    @Getter
    public enum ReportSearchType {
        MODULE(1, "模块"),
        NAME(2, "问题名称"),
        REMARK(3, "备选项"),
        ;

        ReportSearchType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private int code;
        private String desc;

        static public ReportSearchType parseCode(int code) {

            ReportSearchType[] values = ReportSearchType.values();
            for (ReportSearchType type : values) {
                if (type.getCode() == code) {
                    return type;
                }
            }
            return null;
        }
    }

    @Data
    @NoArgsConstructor
    public static class ProfileSettingsExt {

        private long profileId;
        private String extName;
        private String extValue;
        private String extInfo;

        public ProfileSettingsExt(long profileId, String extName, String extValue, String extInfo) {
            this.profileId = profileId;
            this.extName = extName;
            this.extValue = extValue;
            this.extInfo = extInfo;
        }
    }


    @Getter
    public enum ProfileExtName {

        MODULE(1, "模块", "module_id"),
        NAME(2, "问题名称", "name_id"),
        REMARK(3, "备选项", "remark_id"),
        PROFILE_LABELS(4, "标签的属性", "profile_property"),
        ;

        ProfileExtName(int code, String desc, String name) {
            this.code = code;
            this.desc = desc;
            this.name = name;
        }

        private int code;
        private String desc;
        private String name;

        static public ProfileExtName parseCode(int code) {

            ProfileExtName[] values = ProfileExtName.values();
            for (ProfileExtName type : values) {
                if (type.getCode() == code) {
                    return type;
                }
            }
            return null;
        }

        static public ProfileExtName parseName(String name) {

            ProfileExtName[] values = ProfileExtName.values();
            for (ProfileExtName type : values) {
                if (Objects.equals(name, type.getName())) {
                    return type;
                }
            }
            return null;
        }
    }
}
