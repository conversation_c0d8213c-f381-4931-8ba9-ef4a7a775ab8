package com.shuidihuzhu.cf.model.crowdfunding;

import org.apache.commons.lang.builder.ToStringBuilder;

import java.sql.Timestamp;
import java.util.List;

/**
 * Created by ahrievil on 2017/4/24.
 */
public class CfCommitVerifyItem {
    private int id;
    private int pid;
    private String describe;
    private List<CfRefuseReasonItem> cfRefuseReasonItems;
    private List<CfRefuseReasonTag> tags;
    private Timestamp dateCreated;
    private Timestamp lastModified;

    public CfCommitVerifyItem() {
    }

    public CfCommitVerifyItem(int id, List<CfRefuseReasonItem> cfRefuseReasonItems) {
        this.id = id;
        this.cfRefuseReasonItems = cfRefuseReasonItems;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getPid() {
        return pid;
    }

    public void setPid(int pid) {
        this.pid = pid;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }

    public List<CfRefuseReasonItem> getCfRefuseReasonItems() {
        return cfRefuseReasonItems;
    }

    public void setCfRefuseReasonItems(List<CfRefuseReasonItem> cfRefuseReasonItems) {
        this.cfRefuseReasonItems = cfRefuseReasonItems;
    }

    public List<CfRefuseReasonTag> getTags() {
        return tags;
    }

    public void setTags(List<CfRefuseReasonTag> tags) {
        this.tags = tags;
    }

    public Timestamp getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Timestamp dateCreated) {
        this.dateCreated = dateCreated;
    }

    public Timestamp getLastModified() {
        return lastModified;
    }

    public void setLastModified(Timestamp lastModified) {
        this.lastModified = lastModified;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
