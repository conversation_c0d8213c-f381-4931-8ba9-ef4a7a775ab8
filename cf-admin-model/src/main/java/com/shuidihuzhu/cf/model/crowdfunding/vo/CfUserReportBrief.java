package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport;
import com.shuidihuzhu.cipher.ShuidiCipher;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

/**
 * @author: fengxuan
 * @create 2019-11-26 17:35
 * 举报信息中,举报用户的简捷信息
 **/
@Data
public class CfUserReportBrief {
    //电话号码
    private String contact;
    private NumberMaskVo contactMask;
    //昵称
    private String nickName;
    //举报次数
    private int reportNums;
    //举报案例数
    private int reportCrowdNums;

    private List<DetailGroupByCaseId> details;


    @Data
    public static class DetailGroupByCaseId {
        private int activityId;
        private String infoUuId;
        private List<Detail> reportDetails;
    }


    @Data
    public static class Detail {
        private long id;
        private Date createTime;
        private String content;
        private int activityId;
        private int dealStatus;
        private String mobile;
        private NumberMaskVo mobileMask;

    }

    public static Detail createDetail(CrowdfundingReport report) {
        Detail detail = new Detail();
        detail.setId(report.getId());
        detail.setActivityId(report.getActivityId());
        detail.setContent(report.getContent());
        detail.setCreateTime(report.getCreateTime());
        detail.setDealStatus(report.getDealStatus());
        detail.setMobile(report.getEncryptContact());
        return detail;
    }

}
