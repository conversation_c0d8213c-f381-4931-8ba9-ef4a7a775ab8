package com.shuidihuzhu.cf.model.clew;

import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import com.shuidihuzhu.client.cf.clewtrack.model.CfUserInvitedLaunchCaseRecordModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("邀请渠道信息")
@NoArgsConstructor
@AllArgsConstructor
public class CfUserInvitedLaunchCaseRecordVO extends CfUserInvitedLaunchCaseRecordModel {

    public CfUserInvitedLaunchCaseRecordVO(CfUserInvitedLaunchCaseRecordModel cfUserInvitedLaunchCaseRecordModel) {
        super(cfUserInvitedLaunchCaseRecordModel.getInfoId(),
                cfUserInvitedLaunchCaseRecordModel.getUserId(),
                cfUserInvitedLaunchCaseRecordModel.getChannel(),
                cfUserInvitedLaunchCaseRecordModel.getEncryptServiceUserInfo(),
                cfUserInvitedLaunchCaseRecordModel.getPartnerTag());
    }

    @ApiModelProperty("前缀，和后面的phone一起拼接显示")
    private String prefix;
    @ApiModelProperty("明文phone字段，需要前端打码展示,如果为空直接展示前面的prefix就行了")
    private String phone;
    @ApiModelProperty("打码后的phone字段 后台用")
    private NumberMaskVo phoneMask;
    @ApiModelProperty("原文")
    private String originData;

    @ApiModelProperty("如果这个字段为true,直接显示originData")
    private boolean exception;
}
