package com.shuidihuzhu.cf.model.pay;

import com.shuidihuzhu.common.web.model.AbstractModel;

import java.sql.Timestamp;

/**
 * 
 * Created by lixuan on 2017/02/16.
 *
 */
public class PayRecordV2 extends AbstractModel {

	private static final long serialVersionUID = 7668898421389958104L;

	private long id;
	private String payUid;
	private String orderId;
	private String wxOpenId;
	private String orderName;
	private int bizType;
	private int payType;
	private int serviceType;
	private int platform;
	private int prePayAmount;
	private int realPayAmount;
	private int payStatus;
	private String callbackUrl;
	private String frontNotifyUrl;
	private String businessInfo;
	private Timestamp callbackTime;
	private Timestamp dateCreated;
	private Timestamp lastModified;
	// alipay支付
	private int alipayBizType;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getPayUid() {
		return payUid;
	}

	public void setPayUid(String payUid) {
		this.payUid = payUid;
	}

	public String getOrderId() {
		return orderId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	public String getOrderName() {
		return orderName;
	}

	public void setOrderName(String orderName) {
		this.orderName = orderName;
	}

	public int getBizType() {
		return bizType;
	}

	public void setBizType(int bizType) {
		this.bizType = bizType;
	}

	public int getPayType() {
		return payType;
	}

	public void setPayType(int payType) {
		this.payType = payType;
	}

	public int getPlatform() {
		return platform;
	}

	public void setPlatform(int platform) {
		this.platform = platform;
	}

	public int getPrePayAmount() {
		return prePayAmount;
	}

	public void setPrePayAmount(int prePayAmount) {
		this.prePayAmount = prePayAmount;
	}

	public int getRealPayAmount() {
		return realPayAmount;
	}

	public void setRealPayAmount(int realPayAmount) {
		this.realPayAmount = realPayAmount;
	}

	public int getPayStatus() {
		return payStatus;
	}

	public void setPayStatus(int payStatus) {
		this.payStatus = payStatus;
	}

	public String getCallbackUrl() {
		return callbackUrl;
	}

	public void setCallbackUrl(String callbackUrl) {
		this.callbackUrl = callbackUrl;
	}

	public String getFrontNotifyUrl() {
		return frontNotifyUrl;
	}

	public void setFrontNotifyUrl(String frontNotifyUrl) {
		this.frontNotifyUrl = frontNotifyUrl;
	}

	public String getBusinessInfo() {
		return businessInfo;
	}

	public void setBusinessInfo(String businessInfo) {
		this.businessInfo = businessInfo;
	}

	public Timestamp getCallbackTime() {
		return callbackTime;
	}

	public void setCallbackTime(Timestamp callbackTime) {
		this.callbackTime = callbackTime;
	}

	public Timestamp getDateCreated() {
		return dateCreated;
	}

	public void setDateCreated(Timestamp dateCreated) {
		this.dateCreated = dateCreated;
	}

	public Timestamp getLastModified() {
		return lastModified;
	}

	public void setLastModified(Timestamp lastModified) {
		this.lastModified = lastModified;
	}

	public String getWxOpenId() {
		return wxOpenId;
	}

	public void setWxOpenId(String wxOpenId) {
		this.wxOpenId = wxOpenId;
	}

	public int getServiceType() {
		return serviceType;
	}

	public void setServiceType(int serviceType) {
		this.serviceType = serviceType;
	}

	public int getAlipayBizType() {
		return alipayBizType;
	}

	public void setAlipayBizType(int alipayBizType) {
		this.alipayBizType = alipayBizType;
	}

}