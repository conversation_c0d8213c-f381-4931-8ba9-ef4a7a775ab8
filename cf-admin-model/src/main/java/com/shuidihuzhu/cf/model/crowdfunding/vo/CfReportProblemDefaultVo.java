package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.cf.model.crowdfunding.CfReportProblem;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2019-12-12 19:57
 *
 * 默认展示常驻信息
 **/
@ApiModel("模块常驻问题信息")
@Data
public class CfReportProblemDefaultVo {

    private int labelId;

    private String labelDesc;

    private List<CfReportProblemVo> defaultProblems;

    @ApiModelProperty("是否可以添加")
    private boolean canRepeat;

}
