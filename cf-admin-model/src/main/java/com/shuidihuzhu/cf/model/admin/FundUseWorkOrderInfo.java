package com.shuidihuzhu.cf.model.admin;

import com.shuidihuzhu.cf.model.admin.workorder.CfFundUseDetailDO;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.common.web.util.MoneyUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Optional;

/**
 * @author: fengxuan
 * @create 2019-12-20 11:05
 **/
@ApiModel("用途审核工单")
@Data
public class FundUseWorkOrderInfo extends WorkOrderVO {

    @ApiModelProperty("资金用途材料")
    private List<CfFundUseDetailExtend> fundUseDetailList;

    @ApiModelProperty("单次提交票据金额")
    private double singleCommitBillAmount;

    @ApiModelProperty("审核人")
    private String operator;

    @ApiModelProperty("备注")
    private String comment;

    @ApiModel
    @Data
    public static class CfFundUseDetailExtend extends CfFundUseDetailDO {
        @ApiModelProperty("审核状态描述")
        private String auditDesc;
        @ApiModelProperty("是否审核过,新分配的工单为false")
        private boolean hasAudit;
    }


    public static CfFundUseDetailExtend createFundUseDetailExtend(CfFundUseDetailDO cfFundUseDetailDO) {
        if (cfFundUseDetailDO == null) {
            return null;
        }
        CfFundUseDetailExtend detailExtend = new CfFundUseDetailExtend();
        BeanUtils.copyProperties(cfFundUseDetailDO, detailExtend);
        CfFundUseDetailDO.FundUseDetailAuditEnum detailAuditEnum = CfFundUseDetailDO.FundUseDetailAuditEnum.getByCode(detailExtend.getAuditResult());
        if (detailAuditEnum == CfFundUseDetailDO.FundUseDetailAuditEnum.other) {
            detailExtend.setAuditDesc(detailExtend.getCustomAuditResult());
        } else {
            detailExtend.setAuditDesc(Optional.ofNullable(detailAuditEnum).map(CfFundUseDetailDO.FundUseDetailAuditEnum::getDesc).orElse(""));
        }
        //给前端添加一个是否验证的参数
        if (detailAuditEnum != CfFundUseDetailDO.FundUseDetailAuditEnum.un_audit) {
            detailExtend.setHasAudit(true);
        }
        detailExtend.setBillMoneyInYuan(Double.parseDouble(MoneyUtil.buildBalance(detailExtend.getBillMoney())));
        return detailExtend;
    }

}
