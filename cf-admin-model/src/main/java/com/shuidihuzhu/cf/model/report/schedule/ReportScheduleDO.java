package com.shuidihuzhu.cf.model.report.schedule;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class ReportScheduleDO {
    @ApiModelProperty("跟进id")
    private long id;
    @ApiModelProperty("案例id")
    private int caseId;
    @ApiModelProperty("操作人")
    private int operatorId;
    @ApiModelProperty("操作人姓名")
    private int operatorName;
    @ApiModelProperty("是否已处理")
    private boolean done;
    @ApiModelProperty("跟进时间")
    private Date targetTime;
    @ApiModelProperty("版本")
    private long version;
}
