package com.shuidihuzhu.cf.model.param;

import lombok.Getter;

/**
 * @author: fengxuan
 * @create 2020-02-03 17:30
 **/
@Getter
public enum SupplyOrgEnum {
    all(0, "所有"),
    ca<PERSON><PERSON><PERSON><PERSON>(1, "材料审核组"),
    erxian(2, "二线组"),
    other(3, "其他"),
    ;


    private int code;
    private String desc;

    SupplyOrgEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public static SupplyOrgEnum findByCode(int code) {
        for (SupplyOrgEnum value : SupplyOrgEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
