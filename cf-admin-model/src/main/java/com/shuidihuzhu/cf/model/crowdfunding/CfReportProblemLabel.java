package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.Data;

/**
 * @author: fengxuan
 * @create 2019-12-10 19:20
 **/
@Data
public class CfReportProblemLabel {

    private int id;

    private String labelDesc;

    private int parentId;

    private int labelLevel;

    private int isUse;

    private int sort;

    private int isMandatory;




    public static CfReportProblemLabel buildProblemClassify(String labelDesc, int labelLevel, int sort) {
        CfReportProblemLabel cfReportProblemLabel = new CfReportProblemLabel();
        cfReportProblemLabel.setLabelDesc(labelDesc);
        cfReportProblemLabel.setLabelLevel(labelLevel);
        cfReportProblemLabel.setSort(sort);
        return cfReportProblemLabel;
    }


    public static CfReportProblemLabel buildProblemModule(String moduleName, int isMandatory,
                                                          int classifyId, int sort, int labelLevel) {
        CfReportProblemLabel cfReportProblemLabel = new CfReportProblemLabel();
        cfReportProblemLabel.setLabelDesc(moduleName);
        cfReportProblemLabel.setLabelLevel(labelLevel);
        cfReportProblemLabel.setSort(sort);
        cfReportProblemLabel.setIsMandatory(isMandatory);
        cfReportProblemLabel.setParentId(classifyId);
        return cfReportProblemLabel;
    }
}
