package com.shuidihuzhu.cf.model.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoDataStatusTypeEnum;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditOperationItem;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by Ahrievil on 2017/7/26
 */
@Data
public class CfRefuseReasonEntity {

    public static final String CHINESE_COMMA = ",";

    private int id;
    private String content;
    private int tagId;
    private String itemIds;
    private int frequency;
    private int isDelete;
    private List<String> refuseReasonItems;
    private List<CfRefuseReasonItem> itemList;
    private String choiceRelationIds;
    private String suggestModifyIds;
    private String suggestModifyText;
    private int weightServiceLib;

    private List<Integer> useSceneIds;
    private Set<Integer> validRelationIds;
    private Set<Integer> inValidRelationIds;
    private Set<String> totalRefuseReasonItems;
    private Map<Integer, Set<Integer>> useSceneRelationIds;
    private boolean customType;
    private List<CfRefuseReasonEntityRiskLabel> riskLabelList;


    public CfRefuseReasonEntity() {

    }
    public CfRefuseReasonEntity(String content, int tagId, String itemIds, String suggestModifyIds,
                                int serviceLib, boolean customType) {
        this.content = content;
        this.tagId = tagId;
        this.itemIds = itemIds;
        this.choiceRelationIds = "";
        this.suggestModifyIds = StringUtils.trimToEmpty(suggestModifyIds);
        this.weightServiceLib = serviceLib;
        this.customType = customType;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }

    @Data
    public static class CfRefuseReasonOperateLog {
        private int id;
        private int reasonEntityId;
        private String operator;
        private String action;
        private Date createTime;
    }

    @Data
    public static class CfRefuseReasonUseSceneRankMapping {
        private int id;
        private int reasonEntityId;
        private int useScene;
        private int rank;
        private int isDelete;
    }

    @Getter
    public enum CfReasonEntityOperateEnum {
        ENABLE(1, "启用"),
        DELETE(2, "删除"),
        DISABLE(3, "弃用"),
        ADD_ENTITY(4, "新增"),
        MODIFY_USE_SCENE_IDS(5, "修改使用场景"),

            ;

        CfReasonEntityOperateEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private int code;
        private String desc;

        public static CfReasonEntityOperateEnum getByDeleteStatus(InitialAuditOperationItem.RejectOperation operation) {

            if (operation == InitialAuditOperationItem.RejectOperation.ENABLE) {
                return ENABLE;
            }
            if (operation == InitialAuditOperationItem.RejectOperation.DELETE) {
                return DELETE;
            }
            if (operation == InitialAuditOperationItem.RejectOperation.DISABLE) {
                return DISABLE;
            }

            throw new RuntimeException("驳回理由操作不合法");
        }
    }

    // 驳回项的使用场景
    @Getter
    public enum RejectOptionUseSceneEnum {

        /**
         * 初审驳回
         */
        INITIAL_AUDIT(1, "初审驳回"),

        MATERIAL_VERIFY(2, "材料审核驳回"),
        CREDIT_AUDIT(3, "增信审核驳回"),
        IMAGE_CONTENT_AUDIT(4, "图文审核驳回"),
        PAY_AUDIT(5, "付款审核"),
        REFUND_AUDIT(6, "退款审核"),
        FIX_PAYEE_AUDIT(7, "修改收款人审核"),

        ;
        private int code;
        private String desc;

        RejectOptionUseSceneEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static List<RejectOptionObject> getAllUseSceneObject(int dataType) {
            List<RejectOptionObject> result = Lists.newArrayList();
            switch (dataType) {
                case 0 :
                    RejectOptionUseSceneEnum[] allScenes = RejectOptionUseSceneEnum.values();
                    for (RejectOptionUseSceneEnum sceneEnum : allScenes) {
                        result.add(new RejectOptionObject(sceneEnum.getCode(), sceneEnum.getDesc()));
                    }
                    break;
                case 1:
                    result.add(new RejectOptionObject(INITIAL_AUDIT.getCode(), INITIAL_AUDIT.getDesc()));
                    result.add(new RejectOptionObject(MATERIAL_VERIFY.getCode(), MATERIAL_VERIFY.getDesc()));
                    result.add(new RejectOptionObject(IMAGE_CONTENT_AUDIT.getCode(), IMAGE_CONTENT_AUDIT.getDesc()));
                    break;
                case 2:
                case 3:
                case 4:
                case 5:
                    result.add(new RejectOptionObject(MATERIAL_VERIFY.getCode(), MATERIAL_VERIFY.getDesc()));
                    break;
                case 7:
                    result.add(new RejectOptionObject(MATERIAL_VERIFY.getCode(), MATERIAL_VERIFY.getDesc()));
                    break;
                case 8:
                    result.add(new RejectOptionObject(PAY_AUDIT.getCode(), PAY_AUDIT.getDesc()));
                    break;
                case 9:
                    result.add(new RejectOptionObject(REFUND_AUDIT.getCode(), REFUND_AUDIT.getDesc()));
                    break;
                case 10:
                case 11:
                    result.add(new RejectOptionObject(FIX_PAYEE_AUDIT.getCode(), FIX_PAYEE_AUDIT.getDesc()));
                    break;
                case 100:
                    result.add(new RejectOptionObject(INITIAL_AUDIT.getCode(), INITIAL_AUDIT.getDesc()));
                    break;
                case 20:
                    result.add(new RejectOptionObject(INITIAL_AUDIT.getCode(), INITIAL_AUDIT.getDesc()));
                    result.add(new RejectOptionObject(CREDIT_AUDIT.getCode(), CREDIT_AUDIT.getDesc()));
                    break;
                case 31:
                    result.add(new RejectOptionObject(INITIAL_AUDIT.getCode(), INITIAL_AUDIT.getDesc()));
                    break;
                case 32:
                    result.add(new RejectOptionObject(INITIAL_AUDIT.getCode(), INITIAL_AUDIT.getDesc()));
                    break;

            }

            return result;
        }


        public static Set<Integer> getAllUseSceneCode() {
            RejectOptionUseSceneEnum[] allScenes = RejectOptionUseSceneEnum.values();
            Set<Integer> result = Sets.newHashSet();
            for (RejectOptionUseSceneEnum sceneEnum : allScenes) {
                result.add(sceneEnum.getCode());
            }
            return result;
        }


        public static String getUseSceneDesc(String useSceneIds) {
            if (StringUtils.isBlank(useSceneIds)) {
                return "";
            }

            return getUseSceneDesc(Lists.transform(Splitter.on(CHINESE_COMMA)
                    .splitToList(useSceneIds), Integer::parseInt));
        }

        public static String getUseSceneDesc(List<Integer> useSceneIds) {
            if (CollectionUtils.isEmpty(useSceneIds)) {
                return "";
            }

            List<String> useScenes = Lists.newArrayList();
            for (CfRefuseReasonEntity.RejectOptionUseSceneEnum sceneEnum :
                    CfRefuseReasonEntity.RejectOptionUseSceneEnum.values()) {
                for (Integer code : useSceneIds) {
                    if (sceneEnum.getCode() == code) {
                        useScenes.add("\"" + sceneEnum.getDesc() + "\"");
                        break;
                    }
                }
            }
            return Joiner.on(CHINESE_COMMA).join(useScenes);
        }
    }

    @AllArgsConstructor
    @Data
    public static class RejectOptionObject {
        int code;
        String desc;
    }

    public static String getServiceLibLog(int serviceLib) {
        return serviceLib == 0 ? "\"否\"" : "\"是\"";
    }
}
