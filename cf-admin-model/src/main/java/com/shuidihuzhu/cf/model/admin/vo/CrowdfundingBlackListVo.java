package com.shuidihuzhu.cf.model.admin.vo;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingBlackList;
import org.springframework.beans.BeanUtils;

/**
 * Created by Ahrievil on 2017/11/6
 */
public class CrowdfundingBlackListVo extends CrowdfundingBlackList {
    private String nickname;

    public CrowdfundingBlackListVo() {
    }

    public CrowdfundingBlackListVo(CrowdfundingBlackList crowdfundingBlackList, String nickname) {
        BeanUtils.copyProperties(crowdfundingBlackList, this);
        this.nickname = nickname;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }
}
