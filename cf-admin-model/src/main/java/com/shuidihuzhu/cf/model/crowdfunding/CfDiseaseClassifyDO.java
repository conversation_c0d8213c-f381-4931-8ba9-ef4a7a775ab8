package com.shuidihuzhu.cf.model.crowdfunding;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * @author: fengxuan
 * @create 2019-11-06 22:02
 **/
@Data
@Slf4j
public class CfDiseaseClassifyDO {

    //"科室id"
    private long id;

    //"科室名称"
    private String classifyDesc;

    private Date createTime;

    @JsonIgnore
    private Date updateTime;

    private boolean delete;

}
