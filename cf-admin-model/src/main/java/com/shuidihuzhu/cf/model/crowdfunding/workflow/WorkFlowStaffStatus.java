package com.shuidihuzhu.cf.model.crowdfunding.workflow;

import com.google.common.collect.Lists;
import lombok.Data;
import lombok.Getter;

import java.util.Date;
import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-02-13 11:43
 **/
@Data
public class WorkFlowStaffStatus {

    private long id;

    private long userId;
    /**
     * @see StaffStatusEnum
     * 在线状态
     */
    private int staffStatus;

    /**
     *@see OptTypeEnum
     * 是否是本人操作
     */
    private int optType;

    private long operatorId;

    private Date assignTime;

    /**
     * @see OrgTypeEnum
     */
    private int orgType;

    private Date createTime;

    private Date updateTime;


    @Getter
    public enum StaffStatusEnum {
        online(1, "在线"),
        offline(2, "离线"),
        PAUSE(3, "暂停"),
        ;

        private int code;
        private String desc;
        StaffStatusEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }


    @Getter
    public enum OptTypeEnum {
        self(0, "本人"),
        other(1, "组长"),
        system(2, "系统"),
        ;
        private int code;
        private String desc;

        OptTypeEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    public static List<Integer> specialOrgTypes = Lists.newArrayList(
            OrgTypeEnum.er_xian.getCode(),
            OrgTypeEnum.report.getCode(),
            OrgTypeEnum.zi_jin.getCode()
    );

    @Getter
    public enum OrgTypeEnum {
        all(0),
        //二线
        er_xian(1),
        //举报
        report(2),
        //资金
        zi_jin(3),
        other(4),
//        //举报一二线组
//        report_1(5),
//        report_2(6),

        ;
        private int code;

        OrgTypeEnum(int code) {
            this.code = code;
        }
    }

}
