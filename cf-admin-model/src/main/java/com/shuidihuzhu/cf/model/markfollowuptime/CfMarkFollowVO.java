package com.shuidihuzhu.cf.model.markfollowuptime;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CfMarkFollowVO {
    @ApiModelProperty("跟进id")
    private long id;
    @ApiModelProperty("案例id")
    private int caseId;
    @ApiModelProperty("案例标题")
    private String caseTitle;
    @ApiModelProperty("工单状态")
    private String orderStatus;
    @ApiModelProperty("下次跟进时间")
    private String targetTime;
    @ApiModelProperty("判断结果")
    private boolean flag;
    @ApiModelProperty("案例id")
    private String infoUuid;
    @ApiModelProperty("工单id")
    private long workOrderId;
    @ApiModelProperty("工单类型")
    private int orderType;
    @ApiModelProperty("工单状态")
    private int handleResult;
    @ApiModelProperty("判断结果")
    private int workOrderStatus;

}
