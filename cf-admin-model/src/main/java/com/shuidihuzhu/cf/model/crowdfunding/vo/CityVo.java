package com.shuidihuzhu.cf.model.crowdfunding.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/3/1.
 */
@Data
@ApiModel(description = "区域实体")
public class CityVo {
    @ApiModelProperty(value = "id", required = true)
    private int id;
    @ApiModelProperty(value = "名称", required = true)
    private String name;
    @ApiModelProperty(value = "行政代码", required = true)
    private String code;
    @ApiModelProperty(value = "父级id", required = true)
    private int parentId;
}
