package com.shuidihuzhu.cf.model.tog;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GuangzhouMarkShowVO {

    @ApiModelProperty("案例是否是广州打标案例")
    private boolean guangzhouLabel;

    @ApiModelProperty("上一次手动选择c端是否展示广州标签")
    int showStatusToC;

    @ApiModelProperty("是否困难群众 0非困难 1困难 2默认")
    private int sfknqz;
    @ApiModelProperty("家庭人口数")
    private int jtrks;
    @ApiModelProperty("是不是原因c端展示 0默认值 1展示 2不展示")
    private int userGrant;
    @ApiModelProperty("经济核查报告 0没有 1有")
    private int hcbg;
}
