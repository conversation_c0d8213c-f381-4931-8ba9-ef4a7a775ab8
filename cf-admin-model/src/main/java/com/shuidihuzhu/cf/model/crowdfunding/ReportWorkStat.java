package com.shuidihuzhu.cf.model.crowdfunding;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE 2018/8/16
 *
 */
@Data
@ApiModel("举报工单统计")
public class ReportWorkStat {

    @ApiModelProperty("新领取工单")
    private int newNum;
    @ApiModelProperty("新有效举报工单")
    private int newActivityNum;
    @ApiModelProperty("总有效举报工单")
    private int allActivityNum;
    @ApiModelProperty("完成有效工单")
    private int finishNum;
    @ApiModelProperty("完成新有效工单")
    private int finishNewNum;
    @ApiModelProperty("失联工单")
    private int lostNum;
    @ApiModelProperty("高风险工单")
    private int risktNum;
    @ApiModelProperty("用户id")
    private int operatorId;
    @ApiModelProperty("用户姓名")
    private String operatorName;


}
