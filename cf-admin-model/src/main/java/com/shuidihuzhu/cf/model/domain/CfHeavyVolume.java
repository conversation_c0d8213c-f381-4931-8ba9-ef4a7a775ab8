package com.shuidihuzhu.cf.model.domain;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/3/8  10:44 上午
 */
@Data
public class CfHeavyVolume {
    private String domainAddress;
    private int heavyVolumeCount;
    private int everyHeavyQuantity;
    private boolean addOrReduce;
    private long fastenTime;

    public CfHeavyVolume(String domainAddress,int heavyVolumeCount,int everyHeavyQuantity,boolean addOrReduce,long fastenTime){
        this.domainAddress = domainAddress;
        this.heavyVolumeCount = heavyVolumeCount;
        this.everyHeavyQuantity = everyHeavyQuantity;
        this.addOrReduce = addOrReduce;
        this.fastenTime = fastenTime;
    }
}
