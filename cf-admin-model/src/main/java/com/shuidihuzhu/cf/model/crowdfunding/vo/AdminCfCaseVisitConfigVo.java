package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.client.cf.risk.model.CfRiskLimitDetail;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import lombok.Data;

import java.util.Objects;

/**
 * Created by wangsf on 18/4/16.
 */
@Data
public class AdminCfCaseVisitConfigVo {

	/**
	 * 案例id
	 */
	private int caseId;

	/** 是否显示订制banner */
	private boolean showBanner;

	/** 是否可分享 */
	private boolean sharable;

	/** 案例图片地址 */
	private String bannerImgUrl;

	/** 案例banner跳转的地址 */
	private String bannerUrl;

	/** 是否展示弹窗 */
	private boolean showPopup;

	/** 弹窗文本 */
	private String popupText;

	/** 弹窗标题 */
	private String popupTitle;

	/** 左滑页面的url */
	private String backPageUrl;

	/** 左滑页对应的组 */
	private int backPageUrlGroup;

	/**
	 * 案例标题
	 */
	private String caseTitle;

	/**
	 * 案例发起人手机号
	 */
	private String raiserMobile;

	/**
	 * 案例uuid
	 */
	private String infoUuid;


	//是否修改标题
	private boolean changeitle;
	//是否修改官方动态
	private boolean officialDynamic;
	//操作人
	private String operator = "system";
	//操作人id
	private int operatorId = 0;

	//处理时间  兼容前端逻辑
	private String end;


	/**
	 * https://wiki.shuiditech.com/pages/viewpage.action?pageId=33588039
	 * 异常案例屏蔽详情页
	 */
	private Boolean abnormalHidden;

	private String abnormalHiddenSelfTitle;

	private String abnormalHiddenOtherTitle;

	public static Response<AdminCfCaseVisitConfigVo> build(CfRiskLimitDetail limitDetail){
		if(Objects.isNull(limitDetail)){
			return ResponseUtil.makeError(ErrorCode.SYSTEM_ERROR);
		}
		AdminCfCaseVisitConfigVo configVo = new AdminCfCaseVisitConfigVo();
		configVo.setCaseId(limitDetail.getCaseId());
		configVo.setShowBanner(limitDetail.isShowBanner());
		configVo.setBannerImgUrl(limitDetail.getBannerImgUrl());
		configVo.setBannerUrl(limitDetail.getBannerUrl());
		configVo.setSharable(limitDetail.isShare());
		configVo.setShowPopup(limitDetail.isShowPopup());
		configVo.setPopupText(limitDetail.getPopupText());
		configVo.setPopupTitle(limitDetail.getPopupTitle());
		configVo.setOfficialDynamic(false);
		configVo.setAbnormalHidden(limitDetail.isShow());
		configVo.setAbnormalHiddenSelfTitle(limitDetail.getShowSelfTitle());
		configVo.setAbnormalHiddenOtherTitle(limitDetail.getShowOtherTitle());
		return ResponseUtil.makeSuccess(configVo);
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}
