package com.shuidihuzhu.cf.model.message;

import lombok.Data;

import java.util.Date;

@Data
public class PushWxArticleSubtaskAutoVo {
    //文章群发任务父级id
    private long articleTaskId;
    private String articleTaskName;
    private String articleTaskDescription;

    //文章群发任务子任务id
    private long articleSubtaskId;
    //子任务配置名称
    private String articleSubtaskName;
    private Date articleSubtaskAutoSendTime;

    //文章推送账号主体third_type
    private int thirdType;
    //消息推送账号主体名称
    private String thirdName;

    //文章素材id
    private String articleMediaId;
    //文章内容描述(来自微信)
    private String articleDescription;

    //文章群发用户数据id
    private int sqlId;
    //互助SQL操作人的姓名
    private String sqlUserName;
    //SQL的名字
    private String sqlName;
    //SQL任务的描述
    private String sqlDescription;
    //互助SQL语句的创建时间
    private Date sqlCreateTime;
    //互助SQL用户数
    private int sqlResultCount;
}
