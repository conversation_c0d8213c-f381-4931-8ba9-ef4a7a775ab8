package com.shuidihuzhu.cf.model.crowdfunding.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 */
@ApiModel(description = "证实查询vo")
@Data
public class VerificationSearchVo {
    @ApiModelProperty("当前页数")
    private Integer current = 1;
    @ApiModelProperty("页面规模")
    private Integer pageSize = 10;
    @ApiModelProperty("案例id")
    private Integer caseId;
    @ApiModelProperty("证实内容")
    private String verificationContent;
    @ApiModelProperty("证实姓名")
    private String userName;
    @ApiModelProperty("证实人userId")
    private Long verifyUserId;
    @ApiModelProperty("证实人手机号")
    private String mobile;
    @ApiModelProperty("案例id/infoUuid")
    private String infoUuid;
}
