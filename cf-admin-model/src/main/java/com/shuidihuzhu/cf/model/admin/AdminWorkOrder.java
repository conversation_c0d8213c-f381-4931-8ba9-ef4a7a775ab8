package com.shuidihuzhu.cf.model.admin;

import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import lombok.Data;

import java.sql.Timestamp;

/**
 * Created by Ahrievil on 2017/11/30
 */
@Data
public class AdminWorkOrder {

    private long id;

    /**
     * @see AdminWorkOrderConst.Type
     */
    private int orderType;

    /**
     * @see AdminWorkOrderConst.Task
     */
    private int orderTask;

    private int creatorType;
    private int creatorId;
    private int operatorRole;
    private int operatorId;
    private int orderStatus;
    private int handleResult;
    private String comment;
    private int level;
    private Timestamp createTime;
    private Timestamp updateTime;
    private int isDelete;
    private int taskType;

    public static AdminWorkOrder create(AdminWorkOrderConst.Type orderType, AdminWorkOrderConst.Task orderTask,
                                        AdminWorkOrderConst.Role roleType, int operatorId, int level, String comment) {
        AdminWorkOrder adminWorkOrder = new AdminWorkOrder();
        adminWorkOrder.setOrderType(orderType.getCode());
        adminWorkOrder.setOrderTask(orderTask.getCode());
        adminWorkOrder.setCreatorType(roleType.getCode());
        adminWorkOrder.setCreatorId(operatorId);
        adminWorkOrder.setOperatorId(0);
        adminWorkOrder.setOrderStatus(AdminWorkOrderConst.Status.CREATED.getCode());
        adminWorkOrder.setHandleResult(AdminWorkOrderConst.Result.INIT.getCode());
        adminWorkOrder.setLevel(level);
        adminWorkOrder.setComment(comment);
        return adminWorkOrder;
    }


}
