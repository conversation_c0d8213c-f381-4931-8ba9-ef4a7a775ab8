package com.shuidihuzhu.cf.model.crowdfunding.workflow;

import lombok.Data;
import lombok.Getter;

import java.util.Date;

@Data
public class WorkFlowTypeProperty {

    private int flowType;
    private long typeId;

    private int propertyType;
    private String propertyValue;

    private Date createTime;

    @Getter
    public enum FlowTypeEnum {
        LOWEST_ORDER_ID(1, "组织id"),
        WORK_FLOW_ID(2, "信息传递工单id"),

        ;

        private int code;
        private String desc;

        FlowTypeEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    @Getter
    public enum PropertyTypeEnum {

        MAX_NO_HANDLE_COUNT(1, "待处理工单数量限制"),

        AUTO_ASSIGN_TIME(2, "自动分配时间"),
        AUTO_ASSIGN_USER(3, "自动分配的处理人"),

        AUTO_FIRST_HANDLE_USER(4, "点击处理中操作的用户"),
        AUTO_FIRST_HANDLE_TIME(5, "点击处理中操作的时间"),

        AUTO_CLICK_HANDLE_USER_ID(6, "点击处理操作的用户-userId"),
        AUTO_CLICK_HANDLE_TIME(7, "点击处理操作的时间"),
        ;

        private int code;
        private String desc;

        PropertyTypeEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    @Data
    public static class FlowTypePropertyEntity {
        Integer noHandleCount;
    }

}
