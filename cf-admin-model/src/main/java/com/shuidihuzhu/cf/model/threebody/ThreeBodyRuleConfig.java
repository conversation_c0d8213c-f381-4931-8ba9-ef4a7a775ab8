package com.shuidihuzhu.cf.model.threebody;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 三体案例规则配置
 * @Author: pan<PERSON><PERSON><PERSON>
 * @Date: 2023/3/1 3:00 PM
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ThreeBodyRuleConfig {

    private Integer totalAmountOrConfig;

    private Integer totalAmountAndConfig;

    private String diseaseConfig;

    private String patientIdentityConfig;

    private String caseTypeConfig;

    private String regionConfig;

    private Integer ageConfig;

}
