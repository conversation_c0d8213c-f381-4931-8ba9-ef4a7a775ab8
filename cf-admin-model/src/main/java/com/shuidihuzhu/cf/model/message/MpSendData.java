package com.shuidihuzhu.cf.model.message;

import lombok.Data;

import java.util.List;

@Data
public class MpSendData {
    private List<String> openidList;
    private int thirdType;
    private String msgType;
    //发送text时
    private String content;
    private String mediaId;
    //0 不允许转载 1 允许转载,底层在转为json强制为 1
    //private int sendIgnoreReprint;

    //在发送视频时
    private String title;
    private String description;
    private long taskId;
    private long subtaskId;
}
