package com.shuidihuzhu.cf.model.admin.workorder.imageContent;

import com.shuidihuzhu.cf.client.ugc.model.domain.risk.CfBaseInfoRiskHitVO;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditCaseDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
public class CfImageContentAuditView {

    private InitialAuditCaseDetail.CaseBaseInfo publicCaseBaseInfo;

    private InitialAuditCaseDetail.CaseBaseInfo caseBaseInfo;

    private InitialAuditCaseDetail.FirstApproveCaseInfo firstApproveCaseInfo;

    private InitialAuditCaseDetail.CreditInfo creditInfo;

    private InitialAuditCaseDetail.CfBasicLivingGuardView additionInfo;

    private String finishContent;

    private CfBaseInfoRiskHitVO riskHitVO;

    private Set<Integer> passTypes;

    // 图片或文章的审核驳回情况
    private Map<Integer, String> rejectDetails;

    private int callStatus;

    @ApiModelProperty("是否是初审高风险")
    boolean initialAuditHighRisk;

}
