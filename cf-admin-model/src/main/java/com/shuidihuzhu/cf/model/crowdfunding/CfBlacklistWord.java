package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.CfBlacklistWordTypeEnum;
import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR> Ahrievil
 */
@Data
public class CfBlacklistWord {
    private long id;
    private String word;
    /**
     * @see CfBlacklistWordTypeEnum
     */
    private int type;
    private int disable;
    private Timestamp createTime;
    private Timestamp updateTime;
    private int isDelete;

    public CfBlacklistWord() {
    }

    public CfBlacklistWord(String word, int type) {
        this.word = word;
        this.type = type;
    }
}
