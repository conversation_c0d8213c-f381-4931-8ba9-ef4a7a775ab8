package com.shuidihuzhu.cf.model.admin;

import org.apache.commons.lang.builder.ToStringBuilder;

import java.sql.Timestamp;

/**
 * Created by Ahrievil on 2017/10/17
 */
public class CfCallTrackingClues {
    private int id;
    private String result;
    private Timestamp buildTime;
    private String taskName;
    private String customerName;
    private String position;
    private String mobile;
    private String cryptoMobile;
    private String callResult;
    private int callCount;
    private String callTime;
    private String remark;
    private String infoUuid;
    private Integer userId;
    private int lineNum;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public Timestamp getBuildTime() {
        return buildTime;
    }

    public void setBuildTime(Timestamp buildTime) {
        this.buildTime = buildTime;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getCryptoMobile() {
        return cryptoMobile;
    }

    public void setCryptoMobile(String cryptoMobile) {
        this.cryptoMobile = cryptoMobile;
    }

    public String getCallResult() {
        return callResult;
    }

    public void setCallResult(String callResult) {
        this.callResult = callResult;
    }

    public int getCallCount() {
        return callCount;
    }

    public void setCallCount(int callCount) {
        this.callCount = callCount;
    }

    public String getCallTime() {
        return callTime;
    }

    public void setCallTime(String callTime) {
        this.callTime = callTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getInfoUuid() {
        return infoUuid;
    }

    public void setInfoUuid(String infoUuid) {
        this.infoUuid = infoUuid;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public int getLineNum() {
        return lineNum;
    }

    public void setLineNum(int lineNum) {
        this.lineNum = lineNum;
    }
//
//    public String getRealMobile(){
//        if(StringUtils.isBlank(cryptoMobile)){
//            return "";
//        }
//        return shuidiCipher.decrypt(cryptoMobile);
//    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
