package com.shuidihuzhu.cf.model.admin.workorder;

import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE 2020/5/7
 */
@Data
public class JuanzhuanCaseDetail {
    //案例id
    private int caseId;

    private String infoId;
    //标题
    private String title;
    //文章
    private String content;
    //图片
    private String pic;
    //手机号
    private String phone;
    //手机号加密（后台用）
    private NumberMaskVo phoneMask;
    //患者姓名
    private String patientName;
    //案例结束
    private String caseEnd;
    //材审状态
    private String cailiaoStatus;
    //活动名称
    private String activityName;
    //活动状态
    private int activityStatus;
    //活动金额
    private int activityAmount;
    //目标金额
    private int targetAmount;
    //筹款金额
    private Integer amount;
    //转发
    private Integer shareCount;
    //捐单
    private Integer donationCount;
    //首次转发时间
    private String firstTime;
    //发起时间
    private String createTime;
    //初审通过时间
    private String chuciTime;
    //捐单目标数
    private Integer targetDonationCount;
    //任务名称
    private String showName;

    //线索手机号
    private String newPhone;
    //线索手机号（掩码）
    private NumberMaskVo newPhoneMask;
    @ApiModelProperty("是否是无服务")
    private boolean noService;

}
