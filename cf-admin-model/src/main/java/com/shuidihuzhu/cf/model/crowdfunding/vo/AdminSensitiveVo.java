package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @DATE 2018/10/18
 */
@Data
public class AdminSensitiveVo {

    private long caseId;

    private String caseTitle;

    private String caseUUid;

    private long commentUserId;

    private String content;

    private int contentType;

    private String  contentTypeStr;

    private String handleResult;

    private int operatorId;

    private String operatorName;

    private Date operatorTime;

    private String operatorTimeStr;

    private long workOrderId;

    private String sensitiveWords;

    /**
     * @see AdminWorkOrderConst.Task
     */
    private int taskType;

}
