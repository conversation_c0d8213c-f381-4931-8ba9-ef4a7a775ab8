package com.shuidihuzhu.cf.model.admin;

import org.apache.commons.lang.builder.ToStringBuilder;

/**
 * Created by ahrievil on 2017/7/13.
 */
public class ExportMinaMajorDisease {
    private String diseaseName;
    private String diseaseReason;
    private String title;
    private String content;

    public String getDiseaseName() {
        return diseaseName;
    }

    public void setDiseaseName(String diseaseName) {
        this.diseaseName = diseaseName;
    }

    public String getDiseaseReason() {
        return diseaseReason;
    }

    public void setDiseaseReason(String diseaseReason) {
        this.diseaseReason = diseaseReason;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
