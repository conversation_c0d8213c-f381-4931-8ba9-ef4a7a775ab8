package com.shuidihuzhu.cf.model.record;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 目标金额审核工单操作记录实体类
 * CREATE TABLE `target_amount_audit_record` (
 * `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
 * `case_id` bigint(20) unsigned NOT NULL COMMENT '案例 ID',
 * `work_order_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '工单 ID',
 * `min_cost_amount` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '合理目标金额下限 单位：元',
 * `max_cost_amount` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '合理目标金额上限 单位：元',
 * `reject_reason` varchar(2048) NOT NULL DEFAULT '' COMMENT '驳回原因',
 * `create_time` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
 * `update_time` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
 * `is_delete` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否逻辑删除',
 * PRIMARY KEY (`id`),
 * KEY `idx_create_time` (`create_time`),
 * KEY `idx_update_time` (`update_time`),
 * KEY `idx_case_id` (`case_id`),
 * KEY `idx_work_order_id` (`work_order_id`)
 * ) ENGINE=InnoDB AUTO_INCREMENT=365954 DEFAULT CHARSET=utf8mb4 COMMENT='目标金额审核工单操作记录表';
 *
 * <AUTHOR>
 * @date 2023/09/15
 */
@Data
@ApiModel("目标金额审核工单操作记录表")
public class TargetAmountAuditRecord {

    private long id;
    @ApiModelProperty("案例 ID")
    private long caseId;
    @ApiModelProperty("工单 ID")
    private long workOrderId;
    @ApiModelProperty("合理目标金额下限 单位：元")
    private int minCostAmount;
    @ApiModelProperty("合理目标金额上限 单位：元")
    private int maxCostAmount;
    @ApiModelProperty("驳回原因")
    private String rejectReason = "";
    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("更新时间")
    private Date updateTime;
    @ApiModelProperty("是否驳回")
    private boolean isReject;
}
