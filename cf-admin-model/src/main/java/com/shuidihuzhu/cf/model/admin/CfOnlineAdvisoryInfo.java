package com.shuidihuzhu.cf.model.admin;

public class CfOnlineAdvisoryInfo {
    private long id;
    private long dialogId;
    private long servicerId;
    private String dialogStatus;
    private String mobileAddress;
    private String customerMobile;
    private String cryptoMobile;
    private String servicerName;
    private String label;
    private String contactClue;
    private String targetUrl;
    private String contactUrl;
    private String operatingSystem;
    private String browser;
    private String ipAddress;
    private String responseTimeOrigin;
    private int responseTime;
    private String continuedTimeOrigin;
    private int continuedTime;
    private String beginTime;
    private String assess;
    private String platform;
    private int isDelete;

    public CfOnlineAdvisoryInfo(long dialogId, long servicerId, String dialogStatus, String mobileAddress, String customerMobile, String cryptoMobile, String servicerName, String label, String contactClue, String targetUrl, String contactUrl, String operatingSystem, String browser, String ipAddress, String responseTimeOrigin, int responseTime, String continuedTimeOrigin, int continuedTime, String beginTime, String assess, String platform) {
        this.dialogId = dialogId;
        this.servicerId = servicerId;
        this.dialogStatus = dialogStatus;
        this.mobileAddress = mobileAddress;
        this.customerMobile = customerMobile;
        this.cryptoMobile = cryptoMobile;
        this.servicerName = servicerName;
        this.label = label;
        this.contactClue = contactClue;
        this.targetUrl = targetUrl;
        this.contactUrl = contactUrl;
        this.operatingSystem = operatingSystem;
        this.browser = browser;
        this.ipAddress = ipAddress;
        this.responseTimeOrigin = responseTimeOrigin;
        this.responseTime = responseTime;
        this.continuedTimeOrigin = continuedTimeOrigin;
        this.continuedTime = continuedTime;
        this.beginTime = beginTime;
        this.assess = assess;
        this.platform = platform;
    }

    public CfOnlineAdvisoryInfo() {
    }

    public String getCryptoMobile() {
        return cryptoMobile;
    }

    public void setCryptoMobile(String cryptoMobile) {
        this.cryptoMobile = cryptoMobile;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getDialogId() {
        return dialogId;
    }

    public void setDialogId(long dialogId) {
        this.dialogId = dialogId;
    }

    public long getServicerId() {
        return servicerId;
    }

    public void setServicerId(long servicerId) {
        this.servicerId = servicerId;
    }

    public String getDialogStatus() {
        return dialogStatus;
    }

    public void setDialogStatus(String dialogStatus) {
        this.dialogStatus = dialogStatus;
    }

    public String getMobileAddress() {
        return mobileAddress;
    }

    public void setMobileAddress(String mobileAddress) {
        this.mobileAddress = mobileAddress;
    }

    public String getCustomerMobile() {
        return customerMobile;
    }

    public void setCustomerMobile(String customerMobile) {
        this.customerMobile = customerMobile;
    }

    public String getServicerName() {
        return servicerName;
    }

    public void setServicerName(String servicerName) {
        this.servicerName = servicerName;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getContactClue() {
        return contactClue;
    }

    public void setContactClue(String contactClue) {
        this.contactClue = contactClue;
    }

    public String getTargetUrl() {
        return targetUrl;
    }

    public void setTargetUrl(String targetUrl) {
        this.targetUrl = targetUrl;
    }

    public String getContactUrl() {
        return contactUrl;
    }

    public void setContactUrl(String contactUrl) {
        this.contactUrl = contactUrl;
    }

    public String getOperatingSystem() {
        return operatingSystem;
    }

    public void setOperatingSystem(String operatingSystem) {
        this.operatingSystem = operatingSystem;
    }

    public String getBrowser() {
        return browser;
    }

    public void setBrowser(String browser) {
        this.browser = browser;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getResponseTimeOrigin() {
        return responseTimeOrigin;
    }

    public void setResponseTimeOrigin(String responseTimeOrigin) {
        this.responseTimeOrigin = responseTimeOrigin;
    }

    public int getResponseTime() {
        return responseTime;
    }

    public void setResponseTime(int responseTime) {
        this.responseTime = responseTime;
    }

    public String getContinuedTimeOrigin() {
        return continuedTimeOrigin;
    }

    public void setContinuedTimeOrigin(String continuedTimeOrigin) {
        this.continuedTimeOrigin = continuedTimeOrigin;
    }

    public int getContinuedTime() {
        return continuedTime;
    }

    public void setContinuedTime(int continuedTime) {
        this.continuedTime = continuedTime;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getAssess() {
        return assess;
    }

    public void setAssess(String assess) {
        this.assess = assess;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }

    @Override
    public String toString() {
        return "CfOnlineAdvisoryInfo{" +
                "id=" + id +
                ", dialogId=" + dialogId +
                ", servicerId=" + servicerId +
                ", dialogStatus='" + dialogStatus + '\'' +
                ", mobileAddress='" + mobileAddress + '\'' +
                ", customerMobile='" + customerMobile + '\'' +
                ", cryptoMobile='" + cryptoMobile + '\'' +
                ", servicerName='" + servicerName + '\'' +
                ", label='" + label + '\'' +
                ", contactClue='" + contactClue + '\'' +
                ", targetUrl='" + targetUrl + '\'' +
                ", contactUrl='" + contactUrl + '\'' +
                ", operatingSystem='" + operatingSystem + '\'' +
                ", browser='" + browser + '\'' +
                ", ipAddress='" + ipAddress + '\'' +
                ", responseTimeOrigin='" + responseTimeOrigin + '\'' +
                ", responseTime=" + responseTime +
                ", continuedTimeOrigin='" + continuedTimeOrigin + '\'' +
                ", continuedTime=" + continuedTime +
                ", beginTime='" + beginTime + '\'' +
                ", assess='" + assess + '\'' +
                ", platform='" + platform + '\'' +
                ", isDelete=" + isDelete +
                '}';
    }
}
