package com.shuidihuzhu.cf.model.crowdfunding;

import java.sql.Timestamp;
import java.util.List;

/**
 * Created by ahrievil on 2017/3/27.
 */
public class CfTagGroup {
    private int id;
    private String describe;
    private List<CfTagItem> itemList;
    private Timestamp dateCreated;
    private Timestamp lastModified;
    private int type;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }

    public List<CfTagItem> getItemList() {
        return itemList;
    }

    public void setItemList(List<CfTagItem> itemList) {
        this.itemList = itemList;
    }

    public Timestamp getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Timestamp dateCreated) {
        this.dateCreated = dateCreated;
    }

    public Timestamp getLastModified() {
        return lastModified;
    }

    public void setLastModified(Timestamp lastModified) {
        this.lastModified = lastModified;
    }
}
