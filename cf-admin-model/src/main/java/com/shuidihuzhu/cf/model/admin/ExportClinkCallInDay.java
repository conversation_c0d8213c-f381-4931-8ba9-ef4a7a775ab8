package com.shuidihuzhu.cf.model.admin;

import java.sql.Timestamp;
import java.util.Date;

/**
 * Created by ahrievil on 2017/4/7.
 */
public class ExportClinkCallInDay {
    private int id;
    private Date dayKey;
    private String summary;
    private String customerCalls;
    private String region;
    private String hotlineNumber;
    private String ivr;
    private String queueNum;
    private String queueName;
    private String workNum;
    private String workName;
    private String workMobile;
    private String startTime;
    private String beginTime;
    private String contactTime;
    private String state;
    private String state2;
    private String totalTime;
    private String recording;
    private String charges;
    private String custom;
    private String comment;
    private String judge;
    private String qualityInspection;
    private String qualityComment;
    private Timestamp dateCreated;
    private Timestamp lastModified;
    private int status;
    private int lineNum;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public Date getDayKey() {
        return dayKey;
    }

    public void setDayKey(Date dayKey) {
        this.dayKey = dayKey;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getCustomerCalls() {
        return customerCalls;
    }

    public void setCustomerCalls(String customerCalls) {
        this.customerCalls = customerCalls;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getHotlineNumber() {
        return hotlineNumber;
    }

    public void setHotlineNumber(String hotlineNumber) {
        this.hotlineNumber = hotlineNumber;
    }

    public String getIvr() {
        return ivr;
    }

    public void setIvr(String ivr) {
        this.ivr = ivr;
    }

    public String getQueueNum() {
        return queueNum;
    }

    public void setQueueNum(String queueNum) {
        this.queueNum = queueNum;
    }

    public String getQueueName() {
        return queueName;
    }

    public void setQueueName(String queueName) {
        this.queueName = queueName;
    }

    public String getWorkNum() {
        return workNum;
    }

    public void setWorkNum(String workNum) {
        this.workNum = workNum;
    }

    public String getWorkName() {
        return workName;
    }

    public void setWorkName(String workName) {
        this.workName = workName;
    }

    public String getWorkMobile() {
        return workMobile;
    }

    public void setWorkMobile(String workMobile) {
        this.workMobile = workMobile;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getContactTime() {
        return contactTime;
    }

    public void setContactTime(String contactTime) {
        this.contactTime = contactTime;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getState2() {
        return state2;
    }

    public void setState2(String state2) {
        this.state2 = state2;
    }

    public String getTotalTime() {
        return totalTime;
    }

    public void setTotalTime(String totalTime) {
        this.totalTime = totalTime;
    }

    public String getRecording() {
        return recording;
    }

    public void setRecording(String recording) {
        this.recording = recording;
    }

    public String getCharges() {
        return charges;
    }

    public void setCharges(String charges) {
        this.charges = charges;
    }

    public String getCustom() {
        return custom;
    }

    public void setCustom(String custom) {
        this.custom = custom;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getJudge() {
        return judge;
    }

    public void setJudge(String judge) {
        this.judge = judge;
    }

    public String getQualityInspection() {
        return qualityInspection;
    }

    public void setQualityInspection(String qualityInspection) {
        this.qualityInspection = qualityInspection;
    }

    public String getQualityComment() {
        return qualityComment;
    }

    public void setQualityComment(String qualityComment) {
        this.qualityComment = qualityComment;
    }

    public Timestamp getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Timestamp dateCreated) {
        this.dateCreated = dateCreated;
    }

    public Timestamp getLastModified() {
        return lastModified;
    }

    public void setLastModified(Timestamp lastModified) {
        this.lastModified = lastModified;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getLineNum() {
        return lineNum;
    }

    public void setLineNum(int lineNum) {
        this.lineNum = lineNum;
    }

    @Override
    public String toString() {
        return "ExportClinkCallInDay{" +
                "id=" + id +
                ", dayKey=" + dayKey +
                ", summary='" + summary + '\'' +
                ", customerCalls='" + customerCalls + '\'' +
                ", region='" + region + '\'' +
                ", hotlineNumber='" + hotlineNumber + '\'' +
                ", ivr='" + ivr + '\'' +
                ", queueNum='" + queueNum + '\'' +
                ", queueName='" + queueName + '\'' +
                ", workNum='" + workNum + '\'' +
                ", workName='" + workName + '\'' +
                ", workMobile='" + workMobile + '\'' +
                ", startTime='" + startTime + '\'' +
                ", beginTime='" + beginTime + '\'' +
                ", contactTime='" + contactTime + '\'' +
                ", state='" + state + '\'' +
                ", state2='" + state2 + '\'' +
                ", totalTime='" + totalTime + '\'' +
                ", recording='" + recording + '\'' +
                ", charges='" + charges + '\'' +
                ", custom='" + custom + '\'' +
                ", comment='" + comment + '\'' +
                ", judge='" + judge + '\'' +
                ", qualityInspection='" + qualityInspection + '\'' +
                ", qualityComment='" + qualityComment + '\'' +
                ", dateCreated=" + dateCreated +
                ", lastModified=" + lastModified +
                ", status=" + status +
                ", lineNum=" + lineNum +
                '}';
    }
}
