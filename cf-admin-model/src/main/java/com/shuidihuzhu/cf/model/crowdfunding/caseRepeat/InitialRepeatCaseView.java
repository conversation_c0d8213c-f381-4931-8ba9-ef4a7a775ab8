package com.shuidihuzhu.cf.model.crowdfunding.caseRepeat;

import com.google.common.base.Joiner;
import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.vo.approve.CreditInfoNewVO;
import com.shuidihuzhu.cf.vo.approve.CreditInfoVO;
import com.shuidihuzhu.common.web.util.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Data
@ApiModel
public class InitialRepeatCaseView {

    @ApiModelProperty("案例重复类型")
    private Integer repeatType;
    @ApiModelProperty("案例重复类型描述")
    private String repeatTypeDesc;

    @ApiModelProperty("案例id")
    private int caseId;
    @ApiModelProperty("infoUuid")
    private String infoUuid;
    @ApiModelProperty("案例标题")
    private String title;
    @ApiModelProperty("案例文章")
    private String content;
    @ApiModelProperty("发起渠道")
    private String channel;
    @ApiModelProperty("案例创建时间")
    private long caseCreateTime;

    @ApiModelProperty("已筹金额 单位元")
    private int amount;
    @ApiModelProperty("目标金额 单位元")
    private int targetAmount;

    @ApiModelProperty("初审的状态")
    private int initialStatus;

    @ApiModelProperty("发起人的姓名")
    private String selfRealName;
    @ApiModelProperty("发起人的身份证")
    private String selfIdcard;
    @ApiModelProperty("发起人的电话号码")
    private String selfMobile;
    @ApiModelProperty("发起人的电话号码（掩码）")
    private NumberMaskVo selfMobileMask;
    @ApiModelProperty("患者姓名")
    private String patientRealName;
    @ApiModelProperty("患者身份证")
    private String patientIdcard;
    @ApiModelProperty("患者出生证")
    private String patientBornCard;
    @ApiModelProperty("证件类型")
    private int patientIdType;

    @ApiModelProperty("患者关系")
    private int userRelationType;
    @ApiModelProperty("材料审核的状态")
    private int materialReviewStatus;
    @ApiModelProperty("案例风险等级 0：高风险；1：低风险")
    private int caseRiskLevel;
    @ApiModelProperty("案例结束状态")
    private int finishStatus;
    @ApiModelProperty("案例结束原因")
    private String finishReason;
    @ApiModelProperty("增信信息对象")
    private CreditInfoVO creditInfoVO;
    @ApiModelProperty("增信信息对象")
    private boolean compareCredit;
    @ApiModelProperty("举报信息")
    private ReportInfo reportInfo;

    @Data
    public static class ReportInfo {

        @ApiModelProperty("被举报次数")
        private int reportNumber;

        @ApiModelProperty("工单状态")
        private int handleResult;

        @ApiModelProperty("最新处理人")
        private String operatorName;

        @ApiModelProperty("举报组标记的打款方式 0为未标记")
        private int payMethod;
    }


    public InitialRepeatCaseView() {

    }


    public InitialRepeatCaseView(int caseId, Set<InitialRepeatCaseView.RepeatType> repeatTypes) {
        this.caseId = caseId;

        if (CollectionUtils.isNotEmpty(repeatTypes)) {
            this.repeatType = new ArrayList<>(repeatTypes).get(0).getId();
            repeatTypeDesc = Joiner.on(" ").join(repeatTypes.stream().map(InitialRepeatCaseView.RepeatType::getDesc).collect(Collectors.toList()));
        }
    }

    public InitialRepeatCaseView(String repeatTypeDesc, int caseId) {
        this.repeatTypeDesc = repeatTypeDesc;
        this.caseId = caseId;
    }

    @Getter
    public enum RepeatType {

        INITIAL_AUDIT_PAST_DUE(1, "case_repeat_expired_pre-review", "过期预审中案例"),
        INITIAL_AUDIT_PASS_CURRENT_PERIOD(2, "case_repeat_caseing_same_period", "在筹案例"),
        INITIAL_AUDIT_CURRENT_PERIOD(3, "case_repeat_pretrial_same_period", "预审中案例"),
        CASE_REPEAT_NEAR_STOP_THEN_RE(4, "case_repeat_near_stop_then_re", "短期内停止后再发案例");

        private int id;
        private String riskMark;
        private String desc;


        RepeatType(int id, String riskMark, String desc) {
            this.id = id;
            this.riskMark = riskMark;
            this.desc = desc;
        }

        static public RepeatType parseByRiskMark(String riskMark) {

            if (StringUtils.isBlank(riskMark)) {
                return null;
            }

            for (RepeatType currType : RepeatType.values()) {
                if (riskMark.equals(currType.getRiskMark())) {
                    return currType;
                }
            }

            return null;
        }
    }

    @Data
    static public class InitialRepeatRiskParam {
        private String currTime;
        private Integer caseId;
        private int patientIdType;
        private String cryptoIdCard;
        private String patientBornCard;

        private String patientName;
        private long workOrderId;
        private String callName;
        private long callUserId;
        private String callOrg;

        public static InitialRepeatRiskParam convertFromFirstMaterial(CfFirsApproveMaterial currentMaterial) {
            if (currentMaterial == null) {
                return null;
            }

            InitialRepeatCaseView.InitialRepeatRiskParam riskParam = new InitialRepeatCaseView.InitialRepeatRiskParam();
            riskParam.setCurrTime(DateUtil.getCurrentDateTimeStr());
            riskParam.setCaseId(currentMaterial.getInfoId());
            riskParam.setPatientIdType(currentMaterial.getPatientIdType());
            riskParam.setCryptoIdCard(currentMaterial.getPatientCryptoIdcard());
            riskParam.setPatientBornCard(currentMaterial.getPatientBornCard());
            riskParam.setPatientName(currentMaterial.getPatientRealName());

            return riskParam;
        }
    }

    @Data
    public static class InitialRepeatSnapshot {
        private int caseId;
        private long workOrderId;
        private int userId;
        private List<InitialRepeatCaseView> caseView;
    }
}
