package com.shuidihuzhu.cf.model.message;

import com.shuidi.weixin.mp.bean.datacube.WxCommentMessage;
import lombok.Data;

import java.util.Date;

@Data
public class CfWxArticleComment {
    private long id;
    private int thirdType;
    private String msgDataId;
    private String index;
    private int userCommentId;
    private String content;
    private int commentType;
    private String openid;
    private Date contentCreateTime;
    private String replyContent;
    private Date replyCreateTime;
    private int isDelete;
    private Date createTime;
    private Date updateTime;

    public CfWxArticleComment() {

    }

    public CfWxArticleComment(WxCommentMessage.WxCommentMessageItem wxCommentMessageItem, int thirdType, int index,
                              String msgDataId) {
        this.thirdType = thirdType;
        this.msgDataId = msgDataId;
        this.index = String.valueOf(index);
        this.userCommentId = wxCommentMessageItem.getUserCommentId();
        this.content = wxCommentMessageItem.getContent();
        this.commentType = wxCommentMessageItem.getCommentType();
        this.openid = wxCommentMessageItem.getOpenid();
        //秒到毫秒
        this.contentCreateTime = new Date(wxCommentMessageItem.getCreateTime() * 1000);
        WxCommentMessage.WxCommentMessageReply wxCommentMessageReply = wxCommentMessageItem.getReply();
        if (null != wxCommentMessageReply) {
            this.replyContent = wxCommentMessageReply.getContent();
            this.replyCreateTime = new Date(wxCommentMessageReply.getCreateTime() * 1000);
        } else {
            this.replyContent = "";
        }
    }
}
