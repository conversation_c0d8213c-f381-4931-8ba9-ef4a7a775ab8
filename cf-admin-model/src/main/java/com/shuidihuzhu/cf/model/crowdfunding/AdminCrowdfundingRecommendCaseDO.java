package com.shuidihuzhu.cf.model.crowdfunding;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * @author: wanghui
 * @create: 2018/10/22 11:32 AM
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@ToString
public class AdminCrowdfundingRecommendCaseDO {
    private Integer id; // id
    private String infoId; // 项目uuid
    private Integer sort; // 排序字段
    private Date createTime; // 创建时间
    private Date updateTime; // 更新时间
    private Integer valid; // 逻辑删除字段 1：有效 0：删除
    private Integer type;  // 推荐案例类型 0-大病  99-梦想
    @ApiModelProperty("案例id")
    private Long caseId;
    @ApiModelProperty("案例状态")
    private Integer caseStatus;
    @ApiModelProperty("创建人")
    private String creator;
    private int userId;
    @ApiModelProperty("患者姓名")
    private String patientName;
    @ApiModelProperty("顾问姓名")
    private String adviserName;
}
