package com.shuidihuzhu.cf.model.questionnaire;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 *  此处基于数据导入，为降低复杂度及统一命名，将标签和标签值对象合并为一个对象
 *  即： parentId =0 对应标签，其子类即标签值，也可以理解为：标签含有子标签。
 *
 */
@Data
public class WxSdTag {
    private int id;
    private int parentId;
    private String tagCode;
    private String tagName;
//    private boolean isDelete;
//    private String createBy;
//    private Date createTime;
//    private String updateBy;
//    private Date updateTime;

    private List<WxSdTag> tagValues;
}
