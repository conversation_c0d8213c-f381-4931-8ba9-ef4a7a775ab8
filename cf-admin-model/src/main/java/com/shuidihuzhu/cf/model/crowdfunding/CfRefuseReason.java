package com.shuidihuzhu.cf.model.crowdfunding;

import org.apache.commons.lang.builder.ToStringBuilder;

/**
 * Created by ahrievil on 2017/1/17.
 *
 *
 * create table cf_refuse_reason(
 * id int(20) unsigned NOT NULL,
 * pid int(20) NOT NULL,
 * content varchar(255) NOT NULL DEFAULT '',
 * per_type INT NOT NULL DEFAULT 0 COMMENT '材料类型',
 * frequency bigint(20) NOT NULL DEFAULT '0',
 * date_created timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
 * last_modified timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
 * PRIMARY KEY (id)
 * ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='驳回信息明细表'
 */
public class CfRefuseReason {
    private int id;
    private int pid;
    private String content;
    private int preType;
    private int frequency;

    public CfRefuseReason() {
    }

    public CfRefuseReason(int id) {
        this.id = id;
    }

    public CfRefuseReason(int pid, String content) {
        this.pid = pid;
        this.content = content;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getPid() {
        return pid;
    }

    public void setPid(int pid) {
        this.pid = pid;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getPreType() {
        return preType;
    }

    public void setPreType(int preType) {
        this.preType = preType;
    }

    public int getFrequency() {
        return frequency;
    }

    public void setFrequency(int frequency) {
        this.frequency = frequency;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
