package com.shuidihuzhu.cf.model.crowdfunding;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;


/**
 * Created by Ahrievil on 2017/12/21
 * <AUTHOR>
 */
@Data
public class AdminWorkOrderFlow {
    private long id;
    private long workOrderId;
    private int problemType;
    private String problemContent;
    private String problemImg;
    private String handleImg;
    private String mobile;
    // 掩码手机号（后台用）
    private NumberMaskVo mobileMask;
    private int caseId;
    private long secondClassifyId;
    private int userIdentity;
    private int assignChannel;
    private Date createTime;
    private Date updateTime;
    private String encryptMobile;

    private String followTags;

    private int cityId;
    private String cityName;
    private int provinceId;
    private String provinceName;

    private int countyId;
    private String countyName;

    private String hospital;
    private int hospitalId;

    /**
     * 新版问题分类 一级分类
     */
    private int newFirstClassifyId;
    /**
     * 新版问题分类 二级分类
     */
    private int newSecondClassifyId;
    /**
     * 新版问题分类 三级分类
     */
    private int newThirdClassifyId;

    //疾病名称
    private String diseaseName;

    //为谁发起
    private String forWhom;

    /**
     * 鲸息系统生成工单的传 1 ,不是鲸息系统生成的传 0
     */
    private int jingxiChannel;


    public static List<Integer> splitterFollowTagViews(String followTags) {
        return getIdListSplitterByComma(followTags);
    }

    public static String joinComma(List<Integer> followTags) {
        if (CollectionUtils.isEmpty(followTags)) {
            return StringUtils.EMPTY;
        }

        return CfRefuseReasonEntity.CHINESE_COMMA + Joiner.on(CfRefuseReasonEntity.CHINESE_COMMA).join(followTags)
                + CfRefuseReasonEntity.CHINESE_COMMA;
    }

    public static List<Integer> getIdListSplitterByComma(String concatByCommaIds) {
        List<Integer> result = Lists.newArrayList();
        if (StringUtils.isNotBlank(concatByCommaIds)) {

            List<String> l = Splitter.on(CfRefuseReasonEntity.CHINESE_COMMA).splitToList(concatByCommaIds);
            for (String w : l) {
                if (StringUtils.isBlank(w)) {
                    continue;
                }

                result.add(Integer.valueOf(w));
            }
        }

        return result;
    }

}
