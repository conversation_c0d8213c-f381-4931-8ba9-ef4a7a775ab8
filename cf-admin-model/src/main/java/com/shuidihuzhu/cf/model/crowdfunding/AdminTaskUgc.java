package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.Data;

import java.sql.Timestamp;

/**
 * Created by Ahrievil on 2017/11/30
 */
@Data
public class AdminTaskUgc {

    private long id;
    private long workOrderId;
    private int modules;
    private int contentType;
    private long extId;
    private long wordId;
    private int result;
    private int caseId;
    private Timestamp createTime;
    private Timestamp updateTime;
    private int isDelete;

    /**
     * 用于记录图文UGC审核工单是 发起触发 还是修改触发
     * @see com.shuidihuzhu.cf.enums.sona.UGCAction
     */
    private int action;

    private String hitWords;
}
