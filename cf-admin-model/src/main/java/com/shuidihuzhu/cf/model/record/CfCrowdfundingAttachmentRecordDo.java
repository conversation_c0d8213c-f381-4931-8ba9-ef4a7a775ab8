package com.shuidihuzhu.cf.model.record;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/9/6  7:46 下午
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CfCrowdfundingAttachmentRecordDo {
    private int caseId;
    private int type;
    private long attachmentId;
    private int valid;
    private Date createTime;

    public static CfCrowdfundingAttachmentRecordDo build(int caseId, long attachmentId, int valid, int type) {
        return CfCrowdfundingAttachmentRecordDo.builder()
                .caseId(caseId)
                .attachmentId(attachmentId)
                .valid(valid)
                .type(type)
                .build();
    }
}
