package com.shuidihuzhu.cf.model.admin;

import java.sql.Timestamp;

/**
 * Created by Ahrievil on 2017/9/7
 */
public class CfInfoDailySettlement {
    private long id;
    private String infoUuid;
    /**
     * 捐款金额
     */
    private long donationAmount;
    /**
     * 捐款次数
     */
    private int donationCount;
    /**
     * 补贴款金额
     */
    private long subsidyAmount;
    /**
     * 补贴款次数
     */
    private int subsidyCount;
    /**
     * 退款金额
     */
    private long refundAmount;
    /**
     * 退款次数
     */
    private int refundCount;
    /**
     * 代付金额
     */
    private long otherPayAmount;
    /**
     * 代付次数
     */
    private int otherPayCount;
    /**
     * 人工打款金额
     */
    private long drawAmount;
    /**
     * 人工打款次数
     */
    private int drawCount;
    /**
     * 公众号
     */
    private int thirdType;
    /**
     * 案例类型 0大病，99梦想
     */
    private int caseType;
    /**
     * 对应商户号
     */
    private int bizType;
    /**
     * 对应主体
     */
    private int mainBody;
    /**
     * 打款途径
     */
    private int drawChannel;
    /**
     * 资金流通类型
     */
    private int settleType;
    private Timestamp accountDay;
    private Timestamp createTime;
    private Timestamp updateTime;
    private int isDelete;

    public CfInfoDailySettlement() {
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getInfoUuid() {
        return infoUuid;
    }

    public void setInfoUuid(String infoUuid) {
        this.infoUuid = infoUuid;
    }

    public long getDonationAmount() {
        return donationAmount;
    }

    public void setDonationAmount(long donationAmount) {
        this.donationAmount = donationAmount;
    }

    public int getDonationCount() {
        return donationCount;
    }

    public void setDonationCount(int donationCount) {
        this.donationCount = donationCount;
    }

    public long getSubsidyAmount() {
        return subsidyAmount;
    }

    public void setSubsidyAmount(long subsidyAmount) {
        this.subsidyAmount = subsidyAmount;
    }

    public int getSubsidyCount() {
        return subsidyCount;
    }

    public void setSubsidyCount(int subsidyCount) {
        this.subsidyCount = subsidyCount;
    }

    public long getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(long refundAmount) {
        this.refundAmount = refundAmount;
    }

    public int getRefundCount() {
        return refundCount;
    }

    public void setRefundCount(int refundCount) {
        this.refundCount = refundCount;
    }

    public long getOtherPayAmount() {
        return otherPayAmount;
    }

    public void setOtherPayAmount(long otherPayAmount) {
        this.otherPayAmount = otherPayAmount;
    }

    public int getOtherPayCount() {
        return otherPayCount;
    }

    public void setOtherPayCount(int otherPayCount) {
        this.otherPayCount = otherPayCount;
    }

    public long getDrawAmount() {
        return drawAmount;
    }

    public void setDrawAmount(long drawAmount) {
        this.drawAmount = drawAmount;
    }

    public int getDrawCount() {
        return drawCount;
    }

    public void setDrawCount(int drawCount) {
        this.drawCount = drawCount;
    }

    public Timestamp getAccountDay() {
        return accountDay;
    }

    public void setAccountDay(Timestamp accountDay) {
        this.accountDay = accountDay;
    }

    public int getThirdType() {
        return thirdType;
    }

    public void setThirdType(int thirdType) {
        this.thirdType = thirdType;
    }

    public int getCaseType() {
        return caseType;
    }

    public void setCaseType(int caseType) {
        this.caseType = caseType;
    }

    public int getBizType() {
        return bizType;
    }

    public void setBizType(int bizType) {
        this.bizType = bizType;
    }

    public int getMainBody() {
        return mainBody;
    }

    public void setMainBody(int mainBody) {
        this.mainBody = mainBody;
    }

    public int getDrawChannel() {
        return drawChannel;
    }

    public void setDrawChannel(int drawChannel) {
        this.drawChannel = drawChannel;
    }

    public int getSettleType() {
        return settleType;
    }

    public void setSettleType(int settleType) {
        this.settleType = settleType;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }
}
