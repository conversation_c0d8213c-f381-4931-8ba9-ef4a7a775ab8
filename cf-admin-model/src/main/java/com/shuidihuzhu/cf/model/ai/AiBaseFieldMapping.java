package com.shuidihuzhu.cf.model.ai;

import com.shuidihuzhu.cf.enums.ai.AiMockDataEnum;

import java.lang.annotation.*;

/**
 * @Description:
 * @Author: pan<PERSON><PERSON><PERSON>
 * @Date: 2024/5/30 4:24 PM
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD})
@Inherited
@Documented
public @interface AiBaseFieldMapping {

    String aiPrompt() default "";

    AiMockDataEnum aiMockData() default AiMockDataEnum.DEFAULT;

}
