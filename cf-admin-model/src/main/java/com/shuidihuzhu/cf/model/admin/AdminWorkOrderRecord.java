package com.shuidihuzhu.cf.model.admin;

import com.shuidihuzhu.common.util.BeanUtils;
import lombok.Data;

import java.sql.Timestamp;

/**
 * Created by Ahrievil on 2017/11/30
 */
@Data
public class AdminWorkOrderRecord {

    private long id;
    private long workOrderId;
    private int orderType;
    private int orderTask;
    private int creatorType;
    private int creatorId;
    private int operatorRole;
    private int operatorId;
    private int orderStatus;
    private int handleResult;
    private String comment;
    private Timestamp createTime;
    private Timestamp updateTime;
    private int isDelete;
    private int taskType;
    private int operateType;

    public AdminWorkOrderRecord() {
    }

    public AdminWorkOrderRecord(AdminWorkOrder adminWorkOrder) {
        BeanUtils.copyProperties(adminWorkOrder, this);
        this.id = 0;
        this.workOrderId = adminWorkOrder.getId();
    }

    public AdminWorkOrderRecord(AdminWorkOrder adminWorkOrder, int operatorRole,
                                int operatorId, int orderStatus, int handleResult, String comment) {
        adminWorkOrder.setOperatorRole(operatorRole);
        adminWorkOrder.setOperatorId(operatorId);
        adminWorkOrder.setOrderStatus(orderStatus);
        adminWorkOrder.setHandleResult(handleResult);
        adminWorkOrder.setComment(comment);
        BeanUtils.copyProperties(adminWorkOrder, this);
        this.id = 0;
        this.workOrderId = adminWorkOrder.getId();
    }

}
