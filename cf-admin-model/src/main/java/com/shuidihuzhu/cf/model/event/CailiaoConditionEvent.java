package com.shuidihuzhu.cf.model.event;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2020/3/18
 */
@Getter
@Setter
@ToString
public class CailiaoConditionEvent extends ApplicationEvent {

    //参数
    private int caseId;

    private String caseUuid;

    //当前筹款金额  单位分
    private int amount;
    /**
     * 案例标记
     * @sea CrowdfundingOperationEnum
     */
    private int operation;



    //结果
    private int orderType;

    private int orderLevel;

    private List<CailiaoConditionResult> results;

    private String comment;


    public CailiaoConditionEvent(Object source) {
        super(source);
    }

    public CailiaoConditionEvent(Object source, int caseId, String caseUuid) {
        super(source);
        this.caseId = caseId;
        this.caseUuid = caseUuid;
    }
}
