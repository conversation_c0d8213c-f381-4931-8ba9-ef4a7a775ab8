package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.Data;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/10.
 */
@Data
public class AdminWorkOrderReportRecord {
    private int id;
    private int workOrderReportId;
    private long workOrderId;
    private int type;
    private int caseId;
    private int reportId;
    private int caseRisk;
    private int followType;
    private int dealResult;
    private int operatorId;

    public static AdminWorkOrderReportRecord create(AdminWorkOrderReport adminWorkOrderReport, long workOrderId,int userId) {
        AdminWorkOrderReportRecord adminWorkOrderReportRecord = new AdminWorkOrderReportRecord();
        adminWorkOrderReportRecord.setWorkOrderReportId(adminWorkOrderReport.getId());
        adminWorkOrderReportRecord.setWorkOrderId(workOrderId);
        adminWorkOrderReportRecord.setType(adminWorkOrderReport.getType());
        adminWorkOrderReportRecord.setCaseId(adminWorkOrderReport.getCaseId());
        adminWorkOrderReportRecord.setReportId(adminWorkOrderReport.getReportId());
        adminWorkOrderReportRecord.setCaseRisk(adminWorkOrderReport.getCaseRisk());
        adminWorkOrderReportRecord.setFollowType(adminWorkOrderReport.getFollowType());
        adminWorkOrderReportRecord.setDealResult(adminWorkOrderReport.getDealResult());
        adminWorkOrderReportRecord.setOperatorId(userId);
        return adminWorkOrderReportRecord;
    }
}
