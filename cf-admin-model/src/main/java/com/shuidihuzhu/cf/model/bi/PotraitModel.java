package com.shuidihuzhu.cf.model.bi;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.sql.Timestamp;

/**
 * @package: com.shuidihuzhu.cf.model.bi
 * @Author: l<PERSON>jiaw<PERSON>
 * @Date: 2019-03-18  14:20
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
public class PotraitModel {
    /**
     * 人群包Id
     */
    private String crowdId = "";
    /**
     * 人群包名称
     */
    private String crowdName = "";
    /**
     * 人群包大小(人数)
     */
    private int packageSize;
    /**
     * 最后更新时间(此参数用于拿具体人群包)
     */
    private Timestamp computeLastTime;

    /**
     * 包类型，分为1：人群包 2：others
     */
    private int packageType;
}
