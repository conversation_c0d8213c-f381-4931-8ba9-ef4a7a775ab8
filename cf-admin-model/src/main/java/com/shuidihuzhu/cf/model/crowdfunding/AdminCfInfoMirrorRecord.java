package com.shuidihuzhu.cf.model.crowdfunding;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AdminCfInfoMirrorRecord extends CfInfoMirrorRecord {
	
	private static final Logger LOGGER = LoggerFactory.getLogger(AdminCfInfoMirrorRecord.class);

	private static final long serialVersionUID = 3576584687270579601L;

//	public AdminPayeeInfo buildAdminPayeeInfo(PayeeInfo payeeInfo) {
//		if (payeeInfo == null) {
//			return null;
//		}
//		AdminPayeeInfo adminPayeeInfo = new AdminPayeeInfo();
//		BeanUtils.copyProperties(payeeInfo, adminPayeeInfo);
//		return adminPayeeInfo.set(payeeInfo);
//	}

//	public AdminPatientInfo buildAdminPatientInfo(PatientInfo patientInfo) {
//		if(patientInfo == null){
//			return null;
//		}
//		AdminPatientInfo adminPatientInfo = new AdminPatientInfo();
//		BeanUtils.copyProperties(patientInfo, adminPatientInfo);
//		return adminPatientInfo.set(patientInfo);
//	}

	public class AdminPayeeInfo extends PayeeInfo {
		private String decodeIdCard;
		private String decodeMobile;
		private String decodeBankCard;

		public String getDecodeIdCard() {
			return decodeIdCard;
		}

		public void setDecodeIdCard(String decodeIdCard) {
			this.decodeIdCard = decodeIdCard;
		}

		public String getDecodeMobile() {
			return decodeMobile;
		}

		public void setDecodeMobile(String decodeMobile) {
			this.decodeMobile = decodeMobile;
		}

		public String getDecodeBankCard() {
			return decodeBankCard;
		}

		public void setDecodeBankCard(String decodeBankCard) {
			this.decodeBankCard = decodeBankCard;
		}

	}

	public class AdminPatientInfo extends PatientInfo {
		private String decodeIdCard;


		public String getDecodeIdCard() {
			return decodeIdCard;
		}

		public void setDecodeIdCard(String decodeIdCard) {
			this.decodeIdCard = decodeIdCard;
		}

	}

}
