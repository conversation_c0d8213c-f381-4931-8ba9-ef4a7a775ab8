package com.shuidihuzhu.cf.model.crowdfunding.ai;

import lombok.Getter;

/**
 * <AUTHOR>
 * @DATE 2020/12/21
 */
@Getter
public enum AiConditionEnum {
    you(1,"有"),
    wu(2,"无"),
    <PERSON>hi(3,"是"),
    f<PERSON>(4,"否"),
    <PERSON><PERSON><PERSON>(5,"制式材料"),
    f<PERSON><PERSON><PERSON>(6,"非制式材料"),
    <PERSON><PERSON><PERSON><PERSON>(7,"用于患者治疗"),
    f<PERSON><PERSON><PERSON><PERSON>(8,"用于非患者治疗"),
    w<PERSON><PERSON><PERSON>(9,"无描述"),
    fan<PERSON><PERSON><PERSON><PERSON>(10,"房产总数"),
    ziji<PERSON>fan<PERSON>(11,"自建房数量"),
    q<PERSON><PERSON><PERSON>(12,"其他房屋数量"),
    z<PERSON><PERSON><PERSON><PERSON><PERSON>(13,"自建房屋"),
    q<PERSON><PERSON><PERSON><PERSON>(14,"其他房屋"),
    we<PERSON><PERSON><PERSON><PERSON>(15,"未知房屋"),


    ;

    private int code;

    private String desc;

    AiConditionEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getStringCode() {
        return code+"";
    }

    /**
     *         1: '有',
     *         2: '无',
     *         3: '是',
     *         4: '否',
     *         5: '制式材料',
     *         6: '非制式材料',
     *         7: '用于患者治疗',
     *         8: '用于非患者治疗',
     *         9: '无描述',
     *         10: '房产总数',
     *         11: '自建房数量',
     *         12: '其他房屋数量'
     * @return
     */
}
