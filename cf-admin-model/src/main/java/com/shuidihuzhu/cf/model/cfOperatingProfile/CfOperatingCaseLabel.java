package com.shuidihuzhu.cf.model.cfOperatingProfile;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Sets;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;
import java.util.Set;

@Data
public class CfOperatingCaseLabel {

    @JsonIgnore
    private long id;
    private int caseId;
    private int caseLabelId;
    @JsonIgnore
    private int operateId;
    private Date createTime;
    private Date updateTime;


    public CfOperatingCaseLabel(int caseId, int caseLabelId, int operateId) {
        this.caseId = caseId;
        this.caseLabelId = caseLabelId;
        this.operateId = operateId;
    }

    @Data
    public static class QueryParam {
        int caseId;
        Set<Integer> labelIds;
        Set<Integer> oldLabelIds;
        int source;
        int userId;
    }

}
