package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/12/12 下午2:21
 * @desc
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AdminReportProblemAnswer {
    /**
     * 当level=1时
     * 对应cf_report_problem_label表的id、parentId、labelDesc、level
     */
    @NonNull
    private Integer firstId;
    @NonNull
    private Integer firstParentId;
    @NonNull
    private String firstLabelDesc;
    @NonNull
    private Integer firstLevel;

    /**
     * 当level=2时
     * 对应cf_report_problem_label表的id、parentId、labelDesc、level
     */
    @NonNull
    private Integer secondId;
    @NonNull
    private Integer secondParentId;
    @NonNull
    private String secondLabelDesc;
    @NonNull
    private Integer secondLevel;

    /**
     * 该类型答案的详情
     */
    @NonNull
    private List<AdminReportProblemLabelAnswer> problemLabelAnswers;
}
