package com.shuidihuzhu.cf.model.kwai;

import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/28  16:27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class KwaiAppShortIdeosCaseMountDto {
    private String infoUuid;
    private String designation;
    private long operatorId;
    private String operatorName;
    private String mobile;
    private String encryptMobile;
    private NumberMaskVo mobileMask;
    private long id;
    private String ids;
    private List<String> infoUuidList;

    private int current;
    private int pageSize;
    private String createTimeDesc;
    private Map<String, Object> pageMap;

}
