package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @time 2019/8/15 上午11:46
 * @desc
 */
@Data
public class ReportUpDownGradeRecord {
    private Integer id;
    private Integer caseId;
    //被升级或降级的举报工单id
    private Integer workOrderId;
    //1:降级一线 2:升级二线
    private Integer type;
    //接受工单的目标人员userId
    private Integer targetUserId;
    //接受工单的目标人员userName
    private String targetUserName;
    private Integer operatorId;
    private String operator;
    private Date createTime;
    private Date updateTime;
}
