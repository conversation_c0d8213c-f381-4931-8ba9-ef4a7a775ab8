package com.shuidihuzhu.cf.model.message;

import lombok.Data;

import java.util.Date;

//@Data
//public class PushWxArticleTask {
//    private long id;
//    //文章配置名称
//    private String taskName;
//    //图文群发任务描述
//    private String description;
//    //文章推送账号主体third_type
//    private int thirdType;
//    //消息推送账号主体名称
//    private String thirdName;
//    private int hasSend;
//    //是否去重,1去重
//    private int notAllowRepeat;
//    //是否删除
//    private int isDelete;
//    //任务有效时间
//    private Date expiresTime;
//    private Date createTime;
//    private Date updateTime;
//    private int status;
//}
