package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import com.shuidihuzhu.cf.model.crowdfunding.CfReportCommitmentInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-03-27 16:24
 **/
@Data
@ApiModel
public class CfReportCredibleInfoVO {

    private long id;

    @ApiModelProperty("案例Id")
    private String infoUuid;

    @ApiModelProperty("审核状态")
    private int auditStatus;

    @ApiModelProperty("举报增信说明")
    private String operatorContent;

    @ApiModelProperty("用户填写的增信内容")
    private String content;

    @ApiModelProperty("图片地址")
    private String imageUrls;

    @ApiModelProperty("是否下发承诺书")
    private boolean issuedCommitment;

    @ApiModelProperty("承诺书")
    private CfReportCommitmentInfo cfReportCommitmentInfo;

    @ApiModelProperty("上传者手机号")
    private String mobile;

    private NumberMaskVo mobileMask;

    @ApiModelProperty("审核时间")
    private Date auditTime;

    @ApiModelProperty("提交时间")
    private Date submitTime;

    @ApiModelProperty("下发时间")
    private Date sendTime;

    @ApiModelProperty("处理动作")
    private List<CfReportAddTrustDisposeVo> disposeAction;
}
