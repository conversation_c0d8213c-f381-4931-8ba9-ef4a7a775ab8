package com.shuidihuzhu.cf.model.ai;

import com.google.errorprone.annotations.NoAllocation;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description: ai生成记录
 * @Author: panghair<PERSON>
 * @Date: 2024/6/3 12:29 PM
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiGenerateRecord {

    @ApiModelProperty("线索id")
    private Integer clewId;

    @ApiModelProperty("生成类型")
    private Integer generateType;

    @ApiModelProperty("大模型类型")
    private Integer modelType;

    @ApiModelProperty("唯一id")
    private String uuid;

    @ApiModelProperty("大模型生成内容")
    private String aiGenerateResults;

    @ApiModelProperty("大模型生成内容")
    private Long operatorId;

}
