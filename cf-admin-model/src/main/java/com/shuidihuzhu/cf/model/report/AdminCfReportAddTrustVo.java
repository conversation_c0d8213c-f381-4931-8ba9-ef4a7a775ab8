package com.shuidihuzhu.cf.model.report;

import com.shuidihuzhu.cf.model.crowdfunding.vo.CfReportAddTrustDisposeVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfReportAddTrustVo;
import lombok.Data;

import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-04-28 16:25
 **/
@Data
public class AdminCfReportAddTrustVo extends CfReportAddTrustVo {

    private List<CfReportAddTrustDisposeVo> disposeAction;

    private List<Integer> watermarks;
}
