package com.shuidihuzhu.cf.model.crowdfunding;

import java.sql.Timestamp;
import java.util.Date;

/**
 * <AUTHOR>
 * @time 2019/11/5 下午8:53
 * @desc
 */
public class CfAttachmentVo extends CrowdfundingAttachment {
    private int priority;
    private int watermark;
    private Timestamp aiFinishTime;

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    public Timestamp getAiFinishTime() {
        return aiFinishTime;
    }

    public void setAiFinishTime(Timestamp aiFinishTime) {
        this.aiFinishTime = aiFinishTime;
    }

    public int getWatermark() {
        return watermark;
    }

    public void setWatermark(int watermark) {
        this.watermark = watermark;
    }
}
