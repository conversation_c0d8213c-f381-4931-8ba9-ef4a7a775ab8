package com.shuidihuzhu.cf.model.aiphoto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @Author: l<PERSON><PERSON>awei
 * @Date: 2018/7/21  14:30
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class PhotoAiInfoModel {
    Integer infoId;
    String idCard = "";
    String onlyIdCard = "";
    Integer imageSimilarityScore = 0;//图片相似度，大于85表示同一图片
    Integer shotsScore = 0;//截了人脸位置的，人脸相似度，大于60通过
    Integer artworkScore = 0; //原始图片的，人脸相似度，大于55通过
    Integer nameScore = 0; //两张图片中，名字的匹配度
    Integer idScore = 0;//两张图片中，身份证号码的匹配度
    Integer status = 0;//成功标志位，0为成功，1-4代表错误码
    String name = "无法识别";//身份证上的姓名
    String idNumber = "无法识别";//身份证上的身份证号码
    String idCardNumber = "无法识别";//身份证上的身份证号码
    String description = "";

    // 手持身份证照片识别结果
    String holdIdCardName = "无法识别";//手持身份证上的姓名
    String holdIdCardIdNumber = "无法识别";//手持身份证上的身份证号码

    /**
     * 照片类型  0  默认患者  100 收款人
     */
    private int photoType;
}
