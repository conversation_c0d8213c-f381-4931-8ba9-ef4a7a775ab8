package com.shuidihuzhu.cf.model.ai;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description:
 * @Author: pan<PERSON><PERSON><PERSON>
 * @Date: 2024/6/3 4:36 PM
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiGenerateRecordVO {

    @ApiModelProperty("线索id")
    private Integer clewId;

    @ApiModelProperty("生成类型")
    private Integer generateType;

    @ApiModelProperty("大模型类型")
    private Integer modelType;

    @ApiModelProperty("唯一id")
    private String uuid;

    @ApiModelProperty("大模型生成内容")
    private List<AiGenerateResult> aiGenerateResults;

}
