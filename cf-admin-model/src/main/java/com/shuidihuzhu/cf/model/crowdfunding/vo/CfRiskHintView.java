package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.cf.domain.RemarkDO;
import com.shuidihuzhu.cf.vo.crowdfunding.CrowdfundingApproveCommentVo;
import com.shuidihuzhu.cf.vo.visitconfig.VisitConfigLogVO;
import lombok.Data;

import java.util.List;

@Data
public class CfRiskHintView  {
    //风控
    private int riskHitCount;
    private int riskCommentCount;
    /**
     * 风控的数据在cf-damin
     */
    private int riskCommentCountTX;//提现
    private int riskCommentCountCL;//材料
    private int riskCommentCountYX;//异常

    //提现
    private List<CrowdfundingApproveCommentVo> remarkDOList101;
    //材料
    private List<CrowdfundingApproveCommentVo> remarkDOList102;
    //异常
    private List<CrowdfundingApproveCommentVo> visitConfigLogVOS;

    public void sum(){
        riskHitCount = this.riskCommentCountTX + this.riskCommentCountCL + this.riskCommentCountYX;
    }
}
