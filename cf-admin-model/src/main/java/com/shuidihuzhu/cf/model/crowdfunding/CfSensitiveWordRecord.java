package com.shuidihuzhu.cf.model.crowdfunding;

import java.sql.Timestamp;

public class CfSensitiveWordRecord {

	private Long id;
	private String infoUuid;
	private long userId;
	private long bizId;
	private long parentBizId;
	private int bizType;
	private String sensitiveWord;
	private String content;
	private int mode;
	private int contentValid;
	private Timestamp bizTime;
	private Timestamp dateCreated;
	private Timestamp lastModified;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getInfoUuid() {
        return infoUuid;
    }

    public void setInfoUuid(String infoUuid) {
        this.infoUuid = infoUuid;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public long getBizId() {
        return bizId;
    }

    public void setBizId(long bizId) {
        this.bizId = bizId;
    }

    public long getParentBizId() {
        return parentBizId;
    }

    public void setParentBizId(long parentBizId) {
        this.parentBizId = parentBizId;
    }

    public int getBizType() {
        return bizType;
    }

    public void setBizType(int bizType) {
        this.bizType = bizType;
    }

    public String getSensitiveWord() {
        return sensitiveWord;
    }

    public void setSensitiveWord(String sensitiveWord) {
        this.sensitiveWord = sensitiveWord;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getMode() {
        return mode;
    }

    public void setMode(int mode) {
        this.mode = mode;
    }

    public int getContentValid() {
        return contentValid;
    }

    public void setContentValid(int contentValid) {
        this.contentValid = contentValid;
    }

    public Timestamp getBizTime() {
        return bizTime;
    }

    public void setBizTime(Timestamp bizTime) {
        this.bizTime = bizTime;
    }

    public Timestamp getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Timestamp dateCreated) {
        this.dateCreated = dateCreated;
    }

    public Timestamp getLastModified() {
        return lastModified;
    }

    public void setLastModified(Timestamp lastModified) {
        this.lastModified = lastModified;
    }

    @Override
    public String toString() {
        return "CfSensitiveWordRecord{" +
                "id=" + id +
                ", infoUuid='" + infoUuid + '\'' +
                ", userId=" + userId +
                ", bizId=" + bizId +
                ", parentBizId=" + parentBizId +
                ", bizType=" + bizType +
                ", sensitiveWord='" + sensitiveWord + '\'' +
                ", content='" + content + '\'' +
                ", mode=" + mode +
                ", contentValid=" + contentValid +
                ", bizTime=" + bizTime +
                ", dateCreated=" + dateCreated +
                ", lastModified=" + lastModified +
                '}';
    }
}
