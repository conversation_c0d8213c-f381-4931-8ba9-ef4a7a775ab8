package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.cf.vo.crowdfunding.CfVerificationVo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/2  11:49 上午
 */
@Data
public class CrowdFundingVerificationView {
    private int caseId;
    /**
     * 证实时间
     */
    private String verificationTime;
    /**
     * 和患者的关系
     */
    private String relationShip;
    /**
     * 证实内容
     */
    private String description;
    /**
     * 审核结果
     */
    private String handleResult;
    /**
     * 是否删除(是否有效)
     */
    private String valid;
    /**
     * 是否删除(是否有效)
     */
    private int verificationValid;
    /**
     * 用户userId
     */
    private long userId;
    /**
     * 证实人手机号码
     */
    private String mobile;
    /**
     * 证实人姓名
     */
    private String name;
    /**
     * 证实人身份证号
     */
    private String identityCard;
    /**
     * 证实ID
     */
    private long verificationId;
    /**
     * UGC工单ID
     */
    private long ugcId;
    /**
     * UGC投诉工单ID
     */
    private long ugcComplaintId;

    private int complaintInfoSize;

    private List<CfVerificationVo.ComplaintInfo> complaintInfoList;
}
