package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.cf.vo.crowdfunding.CfVerificationVo;
import com.shuidihuzhu.client.cf.workorder.model.QueryListResult;
import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2019/10/11
 */
@Data
@ToString(callSuper = true)
public class QueryListResultVo extends QueryListResult {


    private long userId;

    private String content;

    private String contentTypeStr;

    private String sensitiveWord;

    private String attachmentUrls;

    private List<Integer> watermarks;

    private Integer nowShareCount;

    private Integer nowDonationCount;

    // 举报跟进提示
    private String followTip;

    /**
     * 是否为医护身份 0 普通工单 1 医护工单未通过 1 医护工单已通过
     */
    private int isMedical;

    /**
     * 证实人上传图片列表
     */
    private String medicalImageList;

    private String provinceName;

    private String hospitalName;

    /**
     * 证实结果图片url
     */
    private String medicalVerifyResultImageUrl;

    private String verifyName;

    private boolean hasReport;

    private String patientName;

    private Timestamp drawFinishTime;

    private String fundUseRejectedReason;


    private Date firstDrawSuccessTime;
    private long firstDrawAmount;
    private long totalDrawAmount;

    private String raiseName;
    private String raiseMobile;
    private NumberMaskVo raiseMobileMask;
    private String prePostMobile;
    private NumberMaskVo prePostMobileMask;

    private Date firstUpdateBillTime;
    private String treatmentImages;
    private String treatmentMarkImages;

    /***
     * 有效投诉个数
     */
    private int complaintInfoSize;

    /**
     * 驳回次数过多标签
     */
    private boolean rejectToMuch;

    private List<CfVerificationVo.ComplaintInfo> complaintInfoList;

    private int amountReasonableTaskType;
    private String amountReasonableTaskImages;
    private String amountReasonableTaskContent;
    private int amountReasonableTaskAmountStart;
    private int amountReasonableTaskAmountEnd;
    private int amountReasonableTaskPlan;
    private int amountReasonableAfterDays;

    /**
     * 图片审核工单生成原因 0:驳回生成；1:用户主动修改
     */
    private int twWorkOrderReason;

    @Data
    public static class ComplaintInfo{

        /**
         * 投诉时间
         */
        private String complaintTime;

        /**
         * 投诉人id
         */
        private Long complaintUserId;

        /**
         * 投诉类型
         */
        private String complaintMsg;
    }

    private CommentInfo commentInfo;
    @Data
    public static class CommentInfo{

        /**
         * 放心捐留言id
         */
        private long commentId;

        /**
         * 内容类型
         */
        private int moduleType;

        private long userId;

        private String nickName;

        /**
         * 观点持方
         */
        private int support;

        private String headUrl;
    }

}
