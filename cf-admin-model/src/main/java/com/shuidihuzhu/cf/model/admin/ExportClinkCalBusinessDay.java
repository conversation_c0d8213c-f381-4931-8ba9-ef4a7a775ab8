package com.shuidihuzhu.cf.model.admin;

import java.sql.Timestamp;
import java.util.Date;

/**
 * Created by ahrie<PERSON> on 2017/4/7.
 */
public class ExportClinkCalBusinessDay {
    private int id;
    private Date dayKey;
    private String comment;
    private String businessTypes;
    private String name;
    private String phone;
    private String mobile;
    private String serviceState;
    private String level;
    private String founder;
    private String createTime;
    private String modifier;
    private String updateTime;
    private String types;
    private String orderStatus;
    private String processor;
    private Timestamp dateCreated;
    private Timestamp lastModified;
    private int lineNum;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public Date getDayKey() {
        return dayKey;
    }

    public void setDayKey(Date dayKey) {
        this.dayKey = dayKey;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getBusinessTypes() {
        return businessTypes;
    }

    public void setBusinessTypes(String businessTypes) {
        this.businessTypes = businessTypes;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getServiceState() {
        return serviceState;
    }

    public void setServiceState(String serviceState) {
        this.serviceState = serviceState;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getFounder() {
        return founder;
    }

    public void setFounder(String founder) {
        this.founder = founder;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getTypes() {
        return types;
    }

    public void setTypes(String types) {
        this.types = types;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getProcessor() {
        return processor;
    }

    public void setProcessor(String processor) {
        this.processor = processor;
    }

    public Timestamp getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Timestamp dateCreated) {
        this.dateCreated = dateCreated;
    }

    public Timestamp getLastModified() {
        return lastModified;
    }

    public void setLastModified(Timestamp lastModified) {
        this.lastModified = lastModified;
    }

    public int getLineNum() {
        return lineNum;
    }

    public void setLineNum(int lineNum) {
        this.lineNum = lineNum;
    }

    @Override
    public String toString() {
        return "ExportClinkCalBusinessDay{" +
                "id=" + id +
                ", dayKey=" + dayKey +
                ", comment='" + comment + '\'' +
                ", businessTypes='" + businessTypes + '\'' +
                ", name='" + name + '\'' +
                ", phone='" + phone + '\'' +
                ", mobile='" + mobile + '\'' +
                ", serviceState='" + serviceState + '\'' +
                ", level='" + level + '\'' +
                ", founder='" + founder + '\'' +
                ", createTime='" + createTime + '\'' +
                ", modifier='" + modifier + '\'' +
                ", updateTime='" + updateTime + '\'' +
                ", types='" + types + '\'' +
                ", orderStatus='" + orderStatus + '\'' +
                ", processor='" + processor + '\'' +
                ", dateCreated=" + dateCreated +
                ", lastModified=" + lastModified +
                ", lineNum=" + lineNum +
                '}';
    }
}
