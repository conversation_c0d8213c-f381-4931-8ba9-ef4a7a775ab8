package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.model.crowdfunding.AdminWorkOrderFlowRemindRecord;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class AdminWorkOrderFlowDetailView extends AdminWorkOrderFlowView {

    private List<WorkOrderFlowLog> flowLogs;

    private List<NewWorkOrderFlowLog> newFlowLogs;

    private int remindNums;

    private List<AdminWorkOrderFlowRemindRecord> remindRecords;

    // 小坤坤需要 后端没有作用
    private List<OrgObj> organization = Lists.newArrayList(new OrgObj());


    //为什么是这种结构，是为了方便前端展示
    @Data
    public static class NewWorkOrderFlowLog {

        private long flowId;

        private String workFlowId;

        private Date createTime;

        private String creatorName;

        private String creatorOrg;

        private List<WorkOrderFlowLog> logs;
    }

    @Data
    public static class WorkOrderFlowLog {
        private String operateDesc;
        private String operatorName;
        private String roleName;
        private String levelDesc;
        private String allText;
        private String operateTime;
        private String comment;
    }

    // 张坤需要 后端没有作用
    @Data
    public static class OrgObj {
        private String problemType;
        private List<String> chidrenOrgs = Lists.newArrayList();

    }
}
