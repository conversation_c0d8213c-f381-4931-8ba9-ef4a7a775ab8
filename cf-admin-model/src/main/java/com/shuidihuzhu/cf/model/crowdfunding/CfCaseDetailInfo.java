package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @time 2019/8/20 下午2:41
 * @desc
 */
@Data
public class CfCaseDetailInfo {
    /**
     * 案例发起时间
     * 筹款开始时间
     * 筹款结束时间
     * 案例审核状态 @see CrowdfundingStatus
     * 案例发起渠道
     * 目标金额
     * 已筹金额
     * 证实次数
     * 举报次数
     */
    private Date createTime;
    private Date beginTime;
    private Date endTime;
    private int auditStatus;
    private String channel;
    private int targetAmount;
    private int amount;
    private int verifyCount;
    private int reportCount;

    /**
     *  发起人微信昵称
     *  发起人姓名
     *  发起人userId
     *  发起人身份证号
     *  发起人手机号(没有)
     *  发起人与患者关系
     */
    private String raiserNickName;
    private String raiserName;
    private long raiserUserId;
    private String raiserIdentity;
    private String raiserMobile;
    private int raiserRelation;

    /**
     *  患者姓名
     *  患者证件类型 others(0),identity(1),birth(2)
     *  患者证件号
     *  患者手机号
     *  患者所在医院
     *  确诊医院名称
     */
    private String authorName;
    private int idType;
    private String authorIdcard;
    private String authorMobile;
    private String treatHospital;
    private String diagnoseHospital;

    /**
     *  收款人姓名
     *  收款人身份证号
     *  收款人手机号
     *  收款人与患者关系 @see CrowdfundingRelationType
     */
    private String payeeName;
    private String payeeIdentity;
    private String payeeMobile;
    private int payeeRelation;
}
