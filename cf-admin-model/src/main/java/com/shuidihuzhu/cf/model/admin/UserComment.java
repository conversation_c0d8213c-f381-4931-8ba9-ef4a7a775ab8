package com.shuidihuzhu.cf.model.admin;

import com.shuidihuzhu.cf.enums.admin.UserCommentSourceEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @DATE 2018/7/31
 */
@Data
@ApiModel("运营人员备注记录表")
public class UserComment {

    private long id;

    @ApiModelProperty("案例id")
    private long caseId;
    @ApiModelProperty("工单id")
    private long workOrderId;
    @ApiModelProperty("案例来源")
    private int commentSource;
    @ApiModelProperty("备注来源内的分类")
    private int commentType;
    @ApiModelProperty("操作人")
    private long operatorId;
    @ApiModelProperty("备注信息")
    private String comment;
    @ApiModelProperty("操作方式")
    private String operateMode;
    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("说明")
    private String operateDesc;


    public UserComment(long caseId, UserCommentSourceEnum commentSource, UserCommentSourceEnum.CommentType commentType, long operatorId, String comment, String operateMode, String operateDesc) {
        this.caseId = caseId;
        this.commentSource = commentSource.getCode();
        this.commentType = commentType.getCode();
        this.operatorId = operatorId;
        this.comment = comment;
        this.operateMode = operateMode;
        this.operateDesc = operateDesc;
    }


    public UserComment(UserCommentSourceEnum commentSource, long workOrderId, UserCommentSourceEnum.CommentType  commentType, long operatorId, String comment, String operateMode, String operateDesc) {
        this.commentSource = commentSource.getCode();
        this.workOrderId = workOrderId;
        this.commentType = commentType.getCode();
        this.operatorId = operatorId;
        this.comment = comment;
        this.operateMode = operateMode;
        this.operateDesc = operateDesc;
    }


    public UserComment() {
    }
}
