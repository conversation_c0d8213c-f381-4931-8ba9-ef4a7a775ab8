package com.shuidihuzhu.cf.model.report;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2019/4/8
 */
@Data
public class ReportStatTotal {

    private int userNum;

    private GongDanNum gongDanNum;

    private GongDanDealNum gongDanDoneNum;

    private GongDanDealNum gongDanDoingNum;

    private String rateNum = "0";

    private int lostNum;

    private int riskNum;

    private int userId;

    private String userName;


    @Data
    public static class ReportWorkOrderTotalCount {
        private int assignWorkOrderUserNum;
        private int dutyUserNum;

        private NewlyAddReportWorkOrder newlyAddReportWorkOrder;
        @JsonIgnore
        private LeftOverReportWorkOrder leftOverReportWorkOrder;
        private FinishedReportWorkOrder finishedReportWorkOrder;
        private RemainReportWorkOrder remainReportWorkOrder;

        public ReportWorkOrderTotalCount() {

            this.newlyAddReportWorkOrder = new NewlyAddReportWorkOrder();
//            this.leftOverReportWorkOrder = new LeftOverReportWorkOrder();
            this.finishedReportWorkOrder = new FinishedReportWorkOrder();
            this.remainReportWorkOrder = new RemainReportWorkOrder();
        }
    }

    @Data
    public static class NewlyAddReportWorkOrder {
        private int totalWorkOrderNum;
        private int totalReportEntry;

        private int assignedWorkOrderNum;
        private int assignedReportEntry;

        private int noAssignWorkOrderNum;
        private int noAssignReportEntry;
    }

    @Data
    public static class SimpleWorkIdAndOperatorId {
        long workOrderId;
        int operatorId;
    }

    @Data
    public static class SimpleOrderHandleResult {
        long id;
        long workOrderId;
        int caseId;
        int dealResult;
        int operatorId;
        int handleResult;
        Date createTime;
        Date updateTime;
        Date handleTime;
    }


    @Data
    public static class LeftOverReportWorkOrder {
        private int totalWorkOrderNum;
        private int totalReportEntry;

        private int handingWorkOrderNum;
        private int handingReportEntry;

        private int noAssignWorkOrderNum;
        private int noAssignReportEntry;
    }

    @Data
    public static class FinishedReportWorkOrder {
        private int totalWorkOrderNum;
        private int newAddWorkOrderNum;
        private int leftOverWorkOrderNum;

        private int totalReportEntry;

        private int finishedWorkOrderNum;
        private int finishedReportEntry;
        private List<EntryStatNum> finishedReportEntryStats;

        private int noNeedHandleWorkOrderNum;
        private int noNeedHandleReportEntry;
        private List<EntryStatNum> noNeedHandleEntryStats;
    }

    @Data
    public static class RemainReportWorkOrder {
        private int totalWorkOrderNum;
        private int totalReportEntry;

        private int newAddWorkOrderNum;
        private int leftOverWorkOrderNum;

        private int handingWorkOrderNum;
        private int handingReportEntry;
        private List<EntryStatNum> handingReportEntryStats;

        private int noAssignedWorkOrderNum;
        private int noAssignedReportEntry;
        private List<EntryStatNum> noAssignedReportEntryStats;


        private int noBeginHandleWorkOrderNum;
        private int noBeginHandleReportEntry;
        private List<EntryStatNum> noBeginHandleEntryStats;
    }


    @Data
    public static class ReportWorkOrderUserProcess {
        private int userId;
        private String userName;
        private String orgName;

        private int assignWorkOrderNum;
        @JsonIgnore
        private List<Long> assignWorkOrderIds = Lists.newArrayList();
        private int assignReportEntry;

        private int leftOverWorkOrder;
        private int leftOverReportEntry;

        private ReportWorkOrderProcess finish = new ReportWorkOrderProcess();
        private ReportWorkOrderProcess noNeedHandle = new ReportWorkOrderProcess();
        private ReportWorkOrderProcess handing = new ReportWorkOrderProcess();

        private int noBeginHandleWorkOrder;
        @JsonIgnore
        private List<Long> noBeginHandleWorkOrderIds = Lists.newArrayList();
        private int noBeginHandleReportEntry;
        private int lostCaseNum;
    }

    @Data
    public static class ReportWorkOrderProcess {
        private int totalWorkOrderNum;
        private int totalReportEntry;

        private int newAddWorkOrderNum;
        private int leftOverWorkOrderNum;

        @JsonIgnore
        private List<Long> totalWorkOrderIds = Lists.newArrayList();
        private List<EntryStatNum> reportEntryStats;
    }

}
