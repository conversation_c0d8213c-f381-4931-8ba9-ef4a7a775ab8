package com.shuidihuzhu.cf.model.message;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;
import java.util.Objects;

@Data
public class HuZhuSqlRecord {
    @JSONField(serialize = false)
    private int id;
    private int sqlId;
    //SQL的名字
    private String name;
    //SQL任务的描述
    private String description;
    //公众号Type
    private int bizType;
    //互助的查询SQL
    private String hiveSql;
    //用户昵称
    private int resultCount;
    //  文件的路径oss
    private String resultPath;
    //操作状态
    private int status;
    //互助SQL操作人的id
    @JSONField(name = "operatorId")
    private int userId;
    //互助SQL操作人的姓名
    @JSONField(name = "operatorName")
    private String userName;
    //互助SQL语句的创建时间
    private Date sqlCreateTime;
    //互助SQL语句的更新时间
    private Date sqlUpdateTime;
    private int idDelete;
    @JSONField(serialize = false)
    private Date createTime;
    @JSONField(serialize = false)
    private Date updateTime;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof HuZhuSqlRecord)) return false;
        HuZhuSqlRecord that = (HuZhuSqlRecord) o;
        return getSqlId() == that.getSqlId() &&
                getBizType() == that.getBizType() &&
                getResultCount() == that.getResultCount() &&
                getUserId() == that.getUserId() &&
                Objects.equals(getName(), that.getName()) &&
                Objects.equals(getDescription(), that.getDescription()) &&
                Objects.equals(getResultPath(), that.getResultPath()) &&
                Objects.equals(getUserName(), that.getUserName());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getSqlId(), getName(), getDescription(), getBizType(), getResultCount(), getResultPath(), getUserId(), getUserName());
    }
}
