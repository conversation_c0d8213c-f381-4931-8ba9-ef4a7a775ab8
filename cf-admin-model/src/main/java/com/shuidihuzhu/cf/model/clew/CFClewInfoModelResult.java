package com.shuidihuzhu.cf.model.clew;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author: wanghui
 * @time: 2019/1/18 5:07 PM
 * @description:
 * @param:  * @param null :
 * @return:  * @return : null
 */
@Data
public class CFClewInfoModelResult<T> implements Serializable {
    /**
     * 任务总数
     */
    private int totalCount;
    /**
     * 线索详情
     */
    List<T> cfClewInfos;
}
