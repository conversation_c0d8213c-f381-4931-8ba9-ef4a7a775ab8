package com.shuidihuzhu.cf.model.pay.nowpay;

import com.shuidihuzhu.common.web.model.AbstractModel;
import com.shuidihuzhu.common.web.model.AbstractModel;

public class NowpayAccount extends AbstractModel {

	private static final long serialVersionUID = -1L;

	private String appId;
	private String privateKey;
	private String merchantId;
	private String publicKey;

	public NowpayAccount() {
		super();
	}
	
	public NowpayAccount(String appId, String privateKey, String merchantId, String publicKey) {
		super();
		this.appId = appId;
		this.privateKey = privateKey;
		this.merchantId = merchantId;
		this.publicKey = publicKey;
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getPrivateKey() {
		return privateKey;
	}

	public void setPrivateKey(String privateKey) {
		this.privateKey = privateKey;
	}

	public String getMerchantId() {
		return merchantId;
	}

	public void setMerchantId(String merchantId) {
		this.merchantId = merchantId;
	}

	public String getPublicKey() {
		return publicKey;
	}

	public void setPublicKey(String publicKey) {
		this.publicKey = publicKey;
	}

}
