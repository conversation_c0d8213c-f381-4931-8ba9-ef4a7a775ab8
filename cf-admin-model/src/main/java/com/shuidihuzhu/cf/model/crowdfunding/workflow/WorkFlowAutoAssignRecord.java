package com.shuidihuzhu.cf.model.crowdfunding.workflow;

import lombok.Data;

import java.util.Comparator;

@Data
public class WorkFlowAutoAssignRecord {

    public static AutoAssignComparator ASSIGN_COMPARATOR = new AutoAssignComparator();

    private long id;
    private int userId;
    // yyyyMMdd
    private String assignDate;

    private int assignNum;

    private long todayFirstAssignTime;


    public static class AutoAssignComparator implements Comparator<WorkFlowAutoAssignRecord> {

        @Override
        public int compare(WorkFlowAutoAssignRecord o1, WorkFlowAutoAssignRecord o2) {
            if (o1.getAssignNum() != o2.getAssignNum()) {
                return o1.getAssignNum() - o2.getAssignNum();
            }

            if (o1.getTodayFirstAssignTime() == o2.getTodayFirstAssignTime()) {
                return 0;
            }

            return o1.getTodayFirstAssignTime() - o2.getTodayFirstAssignTime() > 0 ? 1 : -1;
        }
    }


}


