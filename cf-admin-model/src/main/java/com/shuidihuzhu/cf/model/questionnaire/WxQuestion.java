package com.shuidihuzhu.cf.model.questionnaire;

import lombok.Data;

import java.util.Date;

@Data
public class WxQuestion {
    private int id;
    private int qnrId; //问卷ID
    private String tagCode;
    private int tagId;
    private String tagName;
    private String content;
    private int replyType;
    private String option; // [{"label":"健康","value":1},{"label":"育儿","value":4}]
    private int maxValue;
    private int minValue;
    private String remark;
    private int sequence;
    private boolean isDelete;
    private String createBy;
    private Date createTime;
    private String updateBy;
    private Date updateTime;

}
