package com.shuidihuzhu.cf.model.message;

import lombok.Data;

import java.util.Date;

//@Data
//public class PushWxArticleSubtask {
//    private long id;
//    //文章群发任务父级id
//    private long articleTaskId;
//    //文章推送账号主体third_type
//    private int thirdType;
//    //消息推送账号主体名称
//    private String thirdName;
//    //子任务配置名称
//    private String subtaskName;
//    //文章素材id
//    private String articleMediaId;
//    //文章内容描述(来自微信)
//    private String articleDescription;
//    //文章群发用户数据id
//    private int sqlId;
//    //sql子文件名
//    private String fileName;
//    //发送状态,0默认待发送,1发送中,2发送成功,3发送失败
//    private int status;
//    //任务失败原因
//    private String failReason;
//    private int isDelete;
//    private Date createTime;
//    private Date autoSendTime;
//    private Date updateTime;
//    //文章群发用户的SQL数据量
//    private int sqlResultCount;
//    //文章群发本地set数据量
//    private int setNoRepeatCount;
//    //文章群发推送微信数据量
//    private int pushSendCount;
//    private String statusString;
//}
