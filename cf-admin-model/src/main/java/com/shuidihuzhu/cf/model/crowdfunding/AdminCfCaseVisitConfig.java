package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.Data;

import java.util.Date;

/**
 * Created by wangsf on 18/4/16.
 */

@Data
public class AdminCfCaseVisitConfig {

	private int id;

	/** 案例id */
	private int caseId;

	/** 是否显示订制banner */
	private boolean showBanner;

	/** 是否可分享  这个需要注意这个特殊  false表示不能转发，默认是true 可转发*/
	private boolean sharable;

	/** 案例图片地址 */
	private String bannerImgUrl="";

	/** 案例banner跳转的地址 */
	private String bannerUrl="";

	/** 是否展示弹窗 */
	private boolean showPopup;

	/** 弹窗文本 */
	private String popupText="";

	/** 弹窗title */
	private String popupTitle="";

	//是否修改标题
	private boolean changeitle;
	//是否修改官方动态
	private boolean officialDynamic;
	//操作人
	private String operator = "system";
	//操作人id  默认系统检查出的案例用为1
	private int operatorId = 1;

	/**
	 * https://wiki.shuiditech.com/pages/viewpage.action?pageId=33588039
	 * 异常案例屏蔽详情页
	 */
	private Boolean abnormalHidden;

	private String abnormalHiddenSelfTitle;

	private String abnormalHiddenOtherTitle;

	//操作时间
	private Date updateTime;


}
