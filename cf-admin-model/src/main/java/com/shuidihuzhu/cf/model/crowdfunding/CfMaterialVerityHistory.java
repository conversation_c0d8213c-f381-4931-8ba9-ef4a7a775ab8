package com.shuidihuzhu.cf.model.crowdfunding;


import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.model.crowdfunding.material.AuditSuggestModifyDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

@Data
public class CfMaterialVerityHistory {

    public static final int PASS_TYPE = 1;
    public static final int REJECT_TYPE = 2;
    public static final int TO_AUDIT_TYPE = 3;

    public static final int OTHER_OPERATOR_TYPE = 0;
    public static final int MATERIAL_VERITY_OPERATOR_TYPE = 1;

    public static final String MATERIAL_VERITY_SEPARATOR = ",";

    public static final int MATERIAL_SAVE = 1;
    public static final int MATERIAL_SUBMIT = 2;

// 水滴筹-筹款顾问-石家庄顾问团队-材料审核组
    public static final List<String> ORG_NAME_List = Lists.newArrayList("水滴筹", "筹款顾问", "石家庄顾问团队",
        "材料审核组");

    private long id;
    private int caseId;
    private String infoId;
    private int handleType;
    private int materialId;
    private String refuseIds;
    private String materialInfo;
    private String materialInfoEncrypt;
    private String materialPicInfo;
    private String materialInfoExt;
    private String materialInfoExtEncrypt;
    private int operatorId;
    private int operatorType;
    private long workOrderId;
    private String comment;
    private Date createTime;
    private Date updateTime;
    private long approveControlId;

    private String operatorDetail;
    private String materialOpTime;
    private int materialOpTimeType;


    @Data
    public static class CfMaterialVerityHistoryVo {

        private String operatorTime;
        private String rejectReason;

        private Object materialInfo;
        private Object materialPicInfo;
        private Object materialInfoExt;

        private String operatorDetail;
        private String materialOpTime;
        private int materialOpTimeType;

        private long workOrderId;

        private int handleType;
        private int materialId;
    }



    @Data
    public static class HistoryOverviewVO {

        @ApiModelProperty("记录总数")
        private long total;

        @ApiModelProperty("记录详情")
        private CfMaterialVerityHistoryVo recordInfo;

    }


    @Data
    public static class CfMaterialVerityHistoryRecord {
        private int caseId;
        private String infoId;
        private List<Integer> passIds;
        private  List<Integer> rejectIds;
        private String comment;
        private int userId;

        private List<AuditSuggestModifyDetail> suggestModifyDetails;

        public CfMaterialVerityHistoryRecord buildCaseId(int caseId) {
            this.caseId = caseId;
            return this;
        }

        public CfMaterialVerityHistoryRecord buildInfoId(String infoId) {
            this.infoId = infoId;
            return this;
        }

        public CfMaterialVerityHistoryRecord buildPassIds(List<Integer> passIds) {
            this.passIds = passIds;
            return this;
        }

        public CfMaterialVerityHistoryRecord buildRejectIds(List<Integer> rejectIds) {
            this.rejectIds = rejectIds;
            return this;
        }

        public CfMaterialVerityHistoryRecord buildComment(String comment) {
            this.comment = comment;
            return this;
        }

        public CfMaterialVerityHistoryRecord buildUserId(int userId) {
            this.userId = userId;
            return this;
        }

        public CfMaterialVerityHistoryRecord buildSuggestModifyDetails(List<AuditSuggestModifyDetail> modifyDetails) {
            this.suggestModifyDetails  = modifyDetails;
            return this;
        }
    }

    public enum MaterialInfoExtEnum {
        PAYEE_IDCARD_VERITY_RESULT(1, "PAYEE_IDCARD_VERITY_RESULT", "银行卡校验的结果"),
        PAYEE_RELATION_TYPE(2, "PAYEE_RELATION_TYPE", "收款人类型"),
        ATTACH_PAYEE_RELATION_VIDEO(3, "PAYEE_RELATION_VIDEO", "收款人与患者关系视频"),
        MATERIAL_AUDIT_SUGGEST_USER_MODIFY(4, "SUGGEST_USER_MODIFY", "建议用户修改tips"),
        MATERIAL_AUDIT_SUGGEST_MODIFY_ID(5, "SUGGEST_MODIFY_IDS", "材料审核选择的修改建议"),
        PAYEE_FACE_RECOGNITION(6, "PAYEE_FACE_RECOGNITION", "收款人人脸识别"),
        ;

        private int code;
        private String word;
        private String desc;

        private MaterialInfoExtEnum(int code, String word, String desc) {
            this.code = code;
            this.word = word;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getWord() {
            return word;
        }

        public String getDesc() {
            return desc;
        }
    }


    public static boolean containsPrimaryOrg(String name) {
        for (LastOrgName value : LastOrgName.values()) {
            if (value.primaryOrg.equals(name)) {
                return true;
            }
        }
        return false;
    }

    public static LastOrgName findByDesc(String primaryOrg, String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        for (LastOrgName value : LastOrgName.values()) {
            if (value.primaryOrg.equals(primaryOrg) && name.contains(value.desc)) {
                return value;
            }
        }
        return null;
    }

    public enum LastOrgName {
        shenhe("石家庄顾问团队","材料审核组", 1),
        erxian("石家庄顾问团队","石家庄二线组", 2),
        jubao("北京顾问团队","举报组", 3),
        zaixian("石家庄顾问团队","在线组", 4),
        huchu("石家庄顾问团队", "呼入组", 5),
        weichat1v1_beijin("北京顾问团队", "微信1v1服务组", 6),
        weichat1v1_sjz("石家庄顾问团队","微信1v1服务组", 6),
        ;
        private String primaryOrg;
        private String desc;

        private int code;

        LastOrgName(String primaryOrg, String desc, int code) {
            this.primaryOrg = primaryOrg;
            this.desc = desc;
            this.code = code;
        }


        public String getDesc() {
            return desc;
        }

        public int getCode() {
            return code;
        }

        public String getPrimaryOrg() {
            return primaryOrg;
        }
    }


    @Data
    public static class MaterialOpDetail {
        private int materialStatus;
        private String passOrRejectTime;
        private String operatorDetail;

        private MaterialOpTimeObj userOpTimeDetail;
    }


    @Data
    public static class MaterialOpTimeObj {
        private String materialOpTime;
        private int materialOpType;

        public MaterialOpTimeObj(String materialOpTime, int materialOpType) {
            this.materialOpTime = materialOpTime;
            this.materialOpType = materialOpType;
        }
    }
}
