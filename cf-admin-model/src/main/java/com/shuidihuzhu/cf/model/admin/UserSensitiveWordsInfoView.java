package com.shuidihuzhu.cf.model.admin;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.enums.crowdfunding.CfSensitiveWordRecordEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfSensitiveWordRecord;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by Ahrievil on 2017/11/6
 */
public class UserSensitiveWordsInfoView extends BlacklistUserInfoVo {
    private List<SensitiveWordView> sensitiveWordsList;

    public UserSensitiveWordsInfoView() {
    }

    public UserSensitiveWordsInfoView(long userId, String nickname, List<CfSensitiveWordRecord> sensitiveWordsList) {
        setUserId(userId);
        setNickname(nickname);
        if (CollectionUtils.isNotEmpty(sensitiveWordsList)) {
            this.sensitiveWordsList = sensitiveWordsList.stream().map(val -> {
                SensitiveWordView sensitiveWordView = new SensitiveWordView();
                sensitiveWordView.setContent(val.getContent());
                sensitiveWordView.setContentType(CfSensitiveWordRecordEnum.BizType.getByValue(val.getBizType()).desc());
                sensitiveWordView.setSensitiveWords(val.getSensitiveWord());
                return sensitiveWordView;
            }).collect(Collectors.toList());
        } else {
            this.sensitiveWordsList = Lists.newArrayList();
        }
    }


    public List<SensitiveWordView> getSensitiveWordsList() {
        return sensitiveWordsList;
    }

    public void setSensitiveWordsList(List<SensitiveWordView> sensitiveWordsList) {
        this.sensitiveWordsList = sensitiveWordsList;
    }

    public class SensitiveWordView {
        private String contentType;
        private String sensitiveWords;
        private String content;
        public String getContentType() {
            return contentType;
        }

        public void setContentType(String contentType) {
            this.contentType = contentType;
        }

        public String getSensitiveWords() {
            return sensitiveWords;
        }

        public void setSensitiveWords(String sensitiveWords) {
            this.sensitiveWords = sensitiveWords;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }
    }
}
