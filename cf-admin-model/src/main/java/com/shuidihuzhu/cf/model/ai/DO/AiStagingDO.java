package com.shuidihuzhu.cf.model.ai.DO;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2024/5/30 11:44 AM
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiStagingDO {

    @ApiModelProperty("线索id")
    private Integer clewId;

    @ApiModelProperty("暂存类型")
    private Integer stagingType;

    @ApiModelProperty("暂存内容")
    private String stagingBaseInfo;

    @ApiModelProperty("操作人id")
    private Long operatorId;

}
