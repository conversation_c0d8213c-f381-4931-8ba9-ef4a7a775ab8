package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

public class AdminSmsTemplateSettingsInfo {

    @Getter
    public enum SmsGroup {

        FIRST_CUSTOM_SERVICE( 1, "一线客服", "sms:first-custom-service", "sms:add-first-custom-service"),
        SECOND_CUSTOM_SERVICE(2, "二线客服", "sms:second-custom-service", "sms:add-second-custom-service"),
        WEIXIN_1V1(3, "微信1V1", "sms:weixin-1v1", "sms:add-weixin-1v1"),
        OUTBOUND_CALL(4, "外呼（", "sms:outbound-call", "sms:add-outbound-call"),
        FIRST_CONTACT(5, "首次沟通", "sms:first-contact", "sms:add-first-contact"),
        INITIAL_AUDIT(6, "初审", "sms:initial-audit", "sms:add-initial-audit"),
        CASE_APPROVE(7, "材料审核", "sms:case-approve", "sms:add-case-approve"),
        FINANCE_AUDIT(8, "资金", "sms:finance-audit", "sms:add-finance-audit"),
        CASE_REPORT(9, "举报", "sms:case-report", "sms:add-case-report"),
        UGC_VERIFY(10, "内容审核", "sms:ugc-verify", "sms:add-ugc-verify"),
        JUANZHUAN_1V1(11, "1V1捐转", "sms:juanzhuan-1v1", "sms:add-juanzhuan-1v1"),
        FRIEND(12, "病友社群", "sms:friend", "sms:add-friend"),
        MEDICAL_DATA_CENTER(13,"医疗数据中心","sms:mdc","sms:add-mdc")
        ;

        private int code;
        private String desc;
        private String sendTemplateAuth;
        private String addTemplateAuth;

        SmsGroup(int code, String desc, String sendTemplateAuth, String addTemplateAuth) {
            this.code = code;
            this.desc = desc;
            this.sendTemplateAuth = sendTemplateAuth;
            this.addTemplateAuth = addTemplateAuth;
        }

        public static SmsGroup parseByCode(int code) {
            SmsGroup[] allGroup = SmsGroup.values();
            for (SmsGroup group : allGroup) {
                if (group.getCode() == code) {
                    return group;
                }
            }

            throw new IllegalArgumentException("sms模版组code错误. code：" + code);
        }
    }

    @AllArgsConstructor
    @Getter
    public enum OnlineRecruitSmsGroup {

        ONLINE_RECRUIT(100, "线上招募", "sms:online-recruit", "sms:add-online-recruit"),

        ;

        private int code;
        private String desc;
        private String sendTemplateAuth;
        private String addTemplateAuth;


        public static OnlineRecruitSmsGroup fromCode(int code) {
            for (OnlineRecruitSmsGroup group : values()) {
                if (group.getCode() == code) {
                    return group;
                }
            }

            throw new IllegalArgumentException("online recruit sms模版组code错误. code：" + code);
        }
    }


    @Getter
    public enum SmsDataType {
        ENABLE(0, "启用"),
        DISABLE(1 , "弃用"),
        DELETE(2 , "删除"),
        ;
        private int code;
        private String desc;

        SmsDataType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

          public static int getOperateTypeByCode(int code) {
                switch(parseByCode(code)) {
                    case ENABLE:
                        return SmsOperateType.ENABLE.getCode();
                    case DISABLE:
                        return SmsOperateType.DISABLE.getCode();
                    case DELETE:
                        return SmsOperateType.DELETE.getCode();
                }
              throw new IllegalArgumentException("sms模版状态code错误. code：" + code);
          }

        public static SmsDataType parseByCode(int code) {
            SmsDataType[] allGroup = SmsDataType.values();
            for (SmsDataType group : allGroup) {
                if (group.getCode() == code) {
                    return group;
                }
            }

            throw new IllegalArgumentException("sms模版状态code错误. code：" + code);
        }

    }

    @Getter
    public enum SmsOperateType {
        ADD(1, "新增"),
        ENABLE(2, "启用"),
        DISABLE(3, "弃用"),
        DELETE(4, "删除"),
        UP(5, "上移"),
        DOWN(6, "下移"),
        ;

        private int code;
        private String desc;

        SmsOperateType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static SmsOperateType parseByCode(int code) {
            SmsOperateType[] allGroup = SmsOperateType.values();
            for (SmsOperateType group : allGroup) {
                if (group.getCode() == code) {
                    return group;
                }
            }

            throw new IllegalArgumentException("sms模版操作code错误. code：" + code);
        }

    }


    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class SmsSettings {

        private int id;
        private String modelNum;
        private int priority;
        private int smsGroup;
        private int operatorId;
        private int isDelete;
        private Date updateTime;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class SmsTemplateView extends SmsSettings {
        private String templateTitle;
        private String templateContent;
        private String templateMsgType;
        private String operatorName;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class SmsOperateLog {
        private String operatorName;
        private Date updateTime;
        private String operateAction;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class SmsContentResult {
        private int smsGroup;
        private List<SmsContent> contentList;
    }


    @NoArgsConstructor
    @Data
    public static class SmsContent {
        String modelNum;
        String templateContent;
        String templateTitle;

        public SmsContent(String modelNum, String templateContent, String templateTitle) {
            this.modelNum = modelNum;
            this.templateContent = templateContent;
            this.templateTitle = templateTitle;
        }
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class SmsSettingsRecord {
        private int id;
        private int smsTemplateSettingsId;
        private int operateType;
        private int operatorId;
        private Date updateTime;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class SmsSettingsRecordView {
        private String operateName;
        private String operateTypeName;
        private Date updateTime;
    }


}
