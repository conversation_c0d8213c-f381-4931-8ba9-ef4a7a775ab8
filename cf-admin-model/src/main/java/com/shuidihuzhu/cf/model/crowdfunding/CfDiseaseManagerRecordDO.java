package com.shuidihuzhu.cf.model.crowdfunding;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * @author: fengxuan
 * @create 2019-11-06 22:07
 **/
@Data
@Slf4j
@NoArgsConstructor
@ApiModel
public class CfDiseaseManagerRecordDO {
    @ApiModelProperty("id")
    private long id;
    @ApiModelProperty("疾病科室id")
    private long diseaseManagerId;
    @ApiModelProperty("操作人id")
    private long operatorId;
    @ApiModelProperty("操作人名称")
    private String operatorName;
    @ApiModelProperty("操作类型id")
    private int operateType;
    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("修改时间")
    private Date updateTime;
    @ApiModelProperty("是否删除")
    private boolean delete;


    public static CfDiseaseManagerRecordDO create(long diseaseManagerId, long operatorId, String operatorName, int operateType) {
        CfDiseaseManagerRecordDO recordDO = new CfDiseaseManagerRecordDO();
        recordDO.operatorId = operatorId;
        recordDO.diseaseManagerId = diseaseManagerId;
        recordDO.operatorName = operatorName;
        recordDO.operateType = operateType;
        return recordDO;
    }

    @Getter
    public enum OperateTypeEnum {
        CREATE(1, "新增"),
        EDIT(2, "编辑"),
        DELETE(3, "删除")
        ;
        int code;
        String des;

        OperateTypeEnum(int code, String des) {
            this.code = code;
            this.des = des;
        }

        public static OperateTypeEnum findByCode(int code) {
            for (OperateTypeEnum value : OperateTypeEnum.values()) {
                if (value.getCode() == code) {
                    return value;
                }
            }
            return null;
        }

    }
}
