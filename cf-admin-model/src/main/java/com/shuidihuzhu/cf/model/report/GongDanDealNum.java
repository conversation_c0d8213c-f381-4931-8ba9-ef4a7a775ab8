package com.shuidihuzhu.cf.model.report;

import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2019/4/9
 */
@Data
public class GongDanDealNum {


    private int num;

    private int jinriNum;

    private int yiliuNum;

    private List<EntryStatNum> entryStatNums;

    public int getNum() {
        return this.getJinriNum()+this.getYiliuNum();
    }

    public int getAllEntryNum(){

        if (CollectionUtils.isNotEmpty(this.getEntryStatNums())){
            return this.getEntryStatNums().stream().mapToInt(EntryStatNum::getNum).sum();
        }
        return 0;
    }
}
