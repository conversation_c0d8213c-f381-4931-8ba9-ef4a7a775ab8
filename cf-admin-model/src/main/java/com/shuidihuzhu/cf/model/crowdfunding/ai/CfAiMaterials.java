package com.shuidihuzhu.cf.model.crowdfunding.ai;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2020/12/17
 */
@Data
public class CfAiMaterials {

    /**
     * 图片录入工单
     */
    public final static int tType = 1;

    /**
     * 文章录入工单-基本信息
     */
    public final static int jType = 2;

    /**
     * 文章录入工单-增信信息
     */
    public final static int zType = 3;

    /**
     * 文章录入工单-其他增信信息
     */
    public final static int qType = 4;
    //材料类型

    private long id;

    @ApiModelProperty("工单id")
    private long workOrderId;

    @ApiModelProperty("工单处理结果  提交 2   稍后处理 3")
    private int handleResult;

    @ApiModelProperty("工单类型")
    private int workOrderType;

    @ApiModelProperty("案例id")
    private int caseId;

    @ApiModelProperty("材料类型    1.图片  2.基本信息   3.增信信息  4.其他增信信息")
    private int materialsType;

    @ApiModelProperty("录入字段")
    private List<LayOutField> fields;


    private String materials;

    //审核结果
    private String result;


    public List<LayOutField> getFields() {

        if (StringUtils.isNotEmpty(getMaterials())){
            return JSON.parseArray(getMaterials(),LayOutField.class);
        }
        return fields;
    }
}
