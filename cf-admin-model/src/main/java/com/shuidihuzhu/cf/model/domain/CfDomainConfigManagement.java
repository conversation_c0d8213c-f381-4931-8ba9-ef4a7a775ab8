package com.shuidihuzhu.cf.model.domain;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/3/8  10:44 上午
 */
@Data
public class CfDomainConfigManagement {
    private long id;
    private String domainAddress;
    private int domainStatus;
    private int accountUniteConfig;
    private int thirdType;
    private int frontEndConfig;
    private int authlistConfig;
    private int backEndConfig;
    private int ingressConfig;
    private int nginxConfig;
    private int crossDomainConfig;
    private String crossDomain;
    private int operationCrossDomainConfig;
    private int strongJumpDomainConfig;
    private String strongJumpDomainPage;
    private String grafanaAddress;
    private int nodeConfig;
    private int clientConfig;
}
