package com.shuidihuzhu.cf.model.admin.workorder.imageContent;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

@Data
public class CfImageContentHandleParam {

    private int caseId;
    private long workOrderId;

    @ApiModelProperty("是否呼通{1: 呼通, 2:未呼通}")
    private int callStatus;
    @ApiModelProperty("附加评论")
    private String handleComment;

    private Set<Integer> passTypes;
    private Set<Integer> rejectIds;

    @ApiModelProperty("操作人id")
    private long userId;


    private CfImageContentAuditView currImageAuditView;

    @JsonIgnore
    private String infoUuid;


}
