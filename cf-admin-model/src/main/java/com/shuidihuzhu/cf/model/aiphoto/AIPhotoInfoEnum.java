package com.shuidihuzhu.cf.model.aiphoto;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2018/8/24  22:58
 *
 */
public enum AIPhotoInfoEnum {

    SUCCESS(0,"验证通过"),
    SIMILARITY(1,"疑似照片相同，请人工核实"),
    NO_FACE(2,"照片中没有人脸信息，请人工核实"),
    TOW_SIMILARITY_LOW(3,"两张人脸照片相似度过低，请人工核实"),
    IDCARD_INFO_ERROR(4,"身份证信息验证不通过，请人工核实"),
	;
	
	private static Map<Integer, AIPhotoInfoEnum> enumMap = Maps.newHashMap();

	static {
		for (AIPhotoInfoEnum tmpEnum : AIPhotoInfoEnum.values()) {
			enumMap.put(tmpEnum.value(), tmpEnum);
		}
	}

	public static AIPhotoInfoEnum fromCode(int code) {
		return enumMap.get(code);
	}
    
    private int value;
    private String desc;

    AIPhotoInfoEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int value() {
        return value;
    }
    
    public String getDesc() {
        return desc;
    }
}
