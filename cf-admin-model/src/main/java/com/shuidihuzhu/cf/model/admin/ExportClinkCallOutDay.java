package com.shuidihuzhu.cf.model.admin;

import java.sql.Timestamp;
import java.util.Date;

/**
 * Created by ahrievil on 2017/4/10.
 */
public class ExportClinkCallOutDay {
    private int id;
    private Date dayKey;
    private String summary;
    private String customerCalls;
    private String region;
    private String relayNumber;
    private String workNum;
    private String workName;
    private String workMobile;
    private String accessTime;
    private String state;
    private String contactTime;
    private String charges;
    private String totalCharges;
    private String totalTime;
    private String callType;
    private String callDuty;
    private String recording;
    private String comment;
    private String hangUp;
    private String judge;
    private String qualityInspection;
    private Timestamp dateCreated;
    private Timestamp lastModified;
    private int status;
    private int lineNum;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public Date getDayKey() {
        return dayKey;
    }

    public void setDayKey(Date dayKey) {
        this.dayKey = dayKey;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getCustomerCalls() {
        return customerCalls;
    }

    public void setCustomerCalls(String customerCalls) {
        this.customerCalls = customerCalls;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getRelayNumber() {
        return relayNumber;
    }

    public void setRelayNumber(String relayNumber) {
        this.relayNumber = relayNumber;
    }

    public String getWorkNum() {
        return workNum;
    }

    public void setWorkNum(String workNum) {
        this.workNum = workNum;
    }

    public String getWorkName() {
        return workName;
    }

    public void setWorkName(String workName) {
        this.workName = workName;
    }

    public String getWorkMobile() {
        return workMobile;
    }

    public void setWorkMobile(String workMobile) {
        this.workMobile = workMobile;
    }

    public String getAccessTime() {
        return accessTime;
    }

    public void setAccessTime(String accessTime) {
        this.accessTime = accessTime;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getContactTime() {
        return contactTime;
    }

    public void setContactTime(String contactTime) {
        this.contactTime = contactTime;
    }

    public String getCharges() {
        return charges;
    }

    public void setCharges(String charges) {
        this.charges = charges;
    }

    public String getTotalCharges() {
        return totalCharges;
    }

    public void setTotalCharges(String totalCharges) {
        this.totalCharges = totalCharges;
    }

    public String getTotalTime() {
        return totalTime;
    }

    public void setTotalTime(String totalTime) {
        this.totalTime = totalTime;
    }

    public String getCallType() {
        return callType;
    }

    public void setCallType(String callType) {
        this.callType = callType;
    }

    public String getCallDuty() {
        return callDuty;
    }

    public void setCallDuty(String callDuty) {
        this.callDuty = callDuty;
    }

    public String getRecording() {
        return recording;
    }

    public void setRecording(String recording) {
        this.recording = recording;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getHangUp() {
        return hangUp;
    }

    public void setHangUp(String hangUp) {
        this.hangUp = hangUp;
    }

    public String getJudge() {
        return judge;
    }

    public void setJudge(String judge) {
        this.judge = judge;
    }

    public String getQualityInspection() {
        return qualityInspection;
    }

    public void setQualityInspection(String qualityInspection) {
        this.qualityInspection = qualityInspection;
    }

    public Timestamp getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Timestamp dateCreated) {
        this.dateCreated = dateCreated;
    }

    public Timestamp getLastModified() {
        return lastModified;
    }

    public void setLastModified(Timestamp lastModified) {
        this.lastModified = lastModified;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getLineNum() {
        return lineNum;
    }

    public void setLineNum(int lineNum) {
        this.lineNum = lineNum;
    }

    @Override
    public String toString() {
        return "ExportClinkCallOutDay{" +
                "id=" + id +
                ", dayKey=" + dayKey +
                ", summary='" + summary + '\'' +
                ", customerCalls='" + customerCalls + '\'' +
                ", region='" + region + '\'' +
                ", relayNumber='" + relayNumber + '\'' +
                ", workNum='" + workNum + '\'' +
                ", workName='" + workName + '\'' +
                ", workMobile='" + workMobile + '\'' +
                ", accessTime='" + accessTime + '\'' +
                ", state='" + state + '\'' +
                ", contactTime='" + contactTime + '\'' +
                ", charges='" + charges + '\'' +
                ", totalCharges='" + totalCharges + '\'' +
                ", totalTime='" + totalTime + '\'' +
                ", callType='" + callType + '\'' +
                ", callDuty='" + callDuty + '\'' +
                ", recording='" + recording + '\'' +
                ", comment='" + comment + '\'' +
                ", hangUp='" + hangUp + '\'' +
                ", judge='" + judge + '\'' +
                ", qualityInspection='" + qualityInspection + '\'' +
                ", dateCreated=" + dateCreated +
                ", lastModified=" + lastModified +
                ", status=" + status +
                ", lineNum=" + lineNum +
                '}';
    }
}
