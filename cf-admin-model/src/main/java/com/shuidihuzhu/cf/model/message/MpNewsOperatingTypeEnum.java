package com.shuidihuzhu.cf.model.message;

import com.google.common.collect.Maps;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2018/7/12 10:49
 * 与wx-push里的要保持一致
 */
public enum MpNewsOperatingTypeEnum {
    EMPTY(0, "空的"),
    CREATE_TASK(1, "创建文章任务"),
    CREATE_SUBTASK(2, "新增分组"),
    DELETE_SUBTASK(3, "删除分组"),
    UPDATE_SUBTASK(4, "修改分组"),
    UPDATE_USER_ID(5, "修改分组用户"),
    SEND_SUBTASK(6, "发送分组"),
    UPDATE_TASK(7, "修改文章任务"),
    DELETE_TASK(8, "删除文章任务"),
    KILLED_SENDING(9, "终止发送分组"),
    SET_AUTO_SEND(10, "设置文章任务自动发送"),
    REVOCATION_AUTO_SUBTASK(11, "撤销文章自动发送"),
    DELETE_WX_PUSH(12, "删除微信文章"),
    SUBTASK_AUTO_TO_WAIT_FAILED(13, "定时子任务进入发送等待状态失败"),
    SUBTASK_AUTO_TO_WAIT_SUCCEED(14, "定时子任务进入发送等待状态成功");
    private int code;
    private String type;
    private static Map<Integer, MpNewsOperatingTypeEnum> enumMap;

    static {
        enumMap = new HashMap<>();
        for (MpNewsOperatingTypeEnum mpNewsOperatingTypeEnum : values()) {
            enumMap.put(mpNewsOperatingTypeEnum.getCode(), mpNewsOperatingTypeEnum);
        }
    }

    MpNewsOperatingTypeEnum(int code, String type) {
        this.code = code;
        this.type = type;
    }

    private static Map<Integer, MpNewsOperatingTypeEnum> map = Maps.newHashMap();

    static {
        for (MpNewsOperatingTypeEnum type : MpNewsOperatingTypeEnum.values()) {
            map.put(type.getCode(), type);
        }
    }

    public static MpNewsOperatingTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        MpNewsOperatingTypeEnum type = map.get(code);
        return type != null ? type : MpNewsOperatingTypeEnum.EMPTY;
    }

    public int getCode() {
        return code;
    }

    public static MpNewsOperatingTypeEnum getEnum(Integer code) {
        return enumMap.get(code);
    }

    public String getType() {
        return type;
    }

}
