package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingOperationEnum;
import lombok.Data;

import java.sql.Timestamp;

/**
 * Created by ahrievil on 2017/7/2.
 */
@Data
public class AdminCrowdfundingOperation {
    private int id;
    private String infoId;
    private Integer operation = CrowdfundingOperationEnum.NONE_OPERATION.value();
    private Integer operatorId;
    private String reason;
    private Timestamp auditCommitTime;
    private Timestamp operateTime;
    private Integer refuseCount;
    private Integer userRefuseCount;
    private Integer approveRefuseCount;
    private Integer callCount;
    private Integer callStatus;
    private Integer reportStatus;
    private Timestamp dateCreated;
    private Timestamp lastModified;
    private Integer caseId;
}
