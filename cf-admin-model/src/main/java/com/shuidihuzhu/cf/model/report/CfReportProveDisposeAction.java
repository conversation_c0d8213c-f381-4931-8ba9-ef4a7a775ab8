package com.shuidihuzhu.cf.model.report;

import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class CfReportProveDisposeAction {

    private String disposeAction;
    private String infoUuid;
    private int type;
    private long trustId;


    public CfReportProveDisposeAction(String disposeAction, String infoUuid, int type, long trustId) {
        this.disposeAction = disposeAction;
        this.infoUuid = infoUuid;
        this.type = type;
        this.trustId = trustId;
    }
}
