package com.shuidihuzhu.cf.model.ai;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2024/11/14 4:22 PM
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiPromptConfig {

    private Integer generateType;

    private Integer modelType;
    // 前端入参用string传入的，区分一下参数名
    private String modelTypeStr;

    private int bizType;

    private String prompt;

}
