package com.shuidihuzhu.cf.model.crowdfunding.materialAudit;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.Data;

@Data
public class CfCaseAuditVersion {

    private int version;

    public CfCaseAuditVersion(int version) {
        this.version = version;
    }

    public CfCaseAuditVersion() {
    }

    public static void main(String[] args) {
        CfCaseAuditVersion version = new CfCaseAuditVersion();
        version.setVersion(1);

        System.out.println(JSON.toJSONString(NewResponseUtil.makeSuccess(version)));
    }


}
