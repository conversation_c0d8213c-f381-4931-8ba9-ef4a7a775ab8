package com.shuidihuzhu.cf.model.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import io.swagger.models.auth.In;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
public class AdminCfRepeatInfo {

    private int id;
    private int caseId;
    private String repeatBaseInfo = "";
    private String repeatDiagnosisInfo = "";
    private String cfPicExpInfo;
    private int needUpdateBaseInfo;
    private int repeatSummary;
    private Date createTime;
    private Date updateTime;

    public static final int NEED_UPDATE = 1;
    public static final int NO_NEED_UPDATE = 0;


    public Set<Integer> getRepeatInfoCaseIdSetExcludeMayRepeat() {

        Set<Integer> resSet = Sets.newHashSet();
        if (StringUtils.isEmpty(repeatBaseInfo)) {
            return resSet;
        }

        Map<Integer, Set<Integer>> caseIdInfos = getRepeatBaseInfoMap();
        if (MapUtils.isEmpty(caseIdInfos)) {
            return resSet;
        }

        for (Map.Entry<Integer, Set<Integer>> entry : caseIdInfos.entrySet()) {
            Integer key = entry.getKey();
            Collection t = entry.getValue();
            if (key != RepeatReason.R7.getCode() && key != RepeatReason.R8.getCode()) {
                resSet.addAll(Sets.newHashSet(t));
            }
        }
        return resSet;
    }


    public Set<Integer> getRepeatInfoCaseIdSet() {

        Set<Integer> resSet = Sets.newHashSet();
        if (StringUtils.isEmpty(repeatBaseInfo)) {
            return resSet;
        }

        Map<Integer, Set<Integer>> caseIdInfos = getRepeatBaseInfoMap();
        if (MapUtils.isEmpty(caseIdInfos)) {
            return resSet;
        }

        for (Map.Entry<Integer, Set<Integer>> entry : caseIdInfos.entrySet()) {
            if (CollectionUtils.isNotEmpty(entry.getValue())) {
                resSet.addAll(Sets.newHashSet(entry.getValue()));
            }
        }
        return resSet;
    }


    public Map<Integer, Set<Integer>> getRepeatBaseInfoMap() {
        try {
            return JSON.parseObject(repeatBaseInfo, Map.class);//已检查过
        } catch (Exception e) {

        }
        return Maps.newHashMap();
    }

    public AdminCfRepeatInfo() {

    }
    public AdminCfRepeatInfo(int caseId, String repeatBaseInfo) {
        this.caseId = caseId;
        this.repeatBaseInfo = repeatBaseInfo;
    }

    @Data
    public static class AdminCfMaterial {
        // 发起人身份证号
        private String originatorIdCard;
        // 发起人手机
        private String originatorIMobile;
        // 判断身份证还是出生证
        private int patientIdType;
        // 患者身份证号 或 出生证
        private String patientIdCard;
        // 患者姓名
        private String patientName;
        // 收款人身份证
        private String payeeIdCard;
        private long userId;
    }

    @AllArgsConstructor
    public enum RepeatReason {
        DEFAULT(0, "", "", ""),
        R1(1, "发起人身份证", "发起人身份证重复", "同一人为不同人发起多次且都在筹"),
        R2(2, "发起人身份证", "发起人身份证二次", "同一人为不同人在不同时期发起多次"),
        R3(3, "发起手机号", "发起手机号重复", "手机号重复为不同人发起多次且都在筹"),
        R4(4, "发起手机号", "发起手机号二次", "手机号重复为不同人在不同时期发起多次"),
        R5(5, "患者身份证", "患者身份证重复", "不同人为同一人发起多次且都在筹"),
        R6(6, "患者身份证", "患者身份证二次", "不同人为同一人在不同时期发起多次"),
        R7(7, "患者姓名", "患者姓名疑似重复", "疑似不同人为同一人发起多次且都在筹"),
        R8(8, "患者姓名", "患者姓名疑似二次", "疑似不同人为同一人在不同时期发起多次"),
        R9(9, "收款人身份证", "收款人身份证重复", "同一收款人发起多次且都在筹"),
        R10(10, "收款人身份证", "收款人身份证二次", "同一收款人在不同时期发起多次"),
        R11(11, "发起人身份证、发起人手机号", "发起人身份证、发起人手机号重复", "同一人为不同人发起多次且都在筹"),
        R12(12, "发起人身份证、发起人手机号",  "发起人身份证、发起人手机号二次", "同一人为不同人在不同时期发起多次"),
        R13(13, "发起人身份证、患者身份证", "发起人身份证、患者身份证重复", "同一人为同一人发起多次且都在筹"),
        R14(14, "发起人身份证、患者身份证", "发起人身份证、患者身份证二次", "同一人为同一人在不同时期发起多次"),
        R15(15, "发起人身份证、患者姓名", "发起人身份证、患者姓名重复", "同一人为同一人发起多次且都在筹"),
        R16(16, "发起人身份证、患者姓名", "发起人身份证、患者姓名二次", "同一人为同一人在不同时期发起多次"),
        R17(17, "发起人身份证、收款人身份证", "发起人身份证、收款人身份证重复", "同一人为不同人发起多次且都在筹"),
        R18(18, "发起人身份证、收款人身份证", "发起人身份证、收款人身份证二次", "同一人为不同人在不同时期发起多次"),
        R19(19, "发起人手机号、患者身份证", "发起人手机号、患者身份证重复", "手机号重复为同一人发起多次且都在筹"),
        R20(20, "发起人手机号、患者身份证", "发起人手机号、患者身份证二次", "手机号重复为同一人在不同时期发起多次"),
        R21(21, "发起人手机号、患者姓名", "发起人手机号、患者姓名重复", "手机号重复为同一人发起多次且都在筹"),
        R22(22, "发起人手机号、患者姓名", "发起人手机号、患者姓名二次", "手机号重复为同一人在不同时期发起多次"),
        R23(23, "发起人手机号、收款人身份证", "发起人手机号、收款人身份证重复", "手机号重复为不同人发起多次且都在筹"),
        R24(24, "发起人手机号、收款人身份证", "发起人手机号、收款人身份证二次", "手机号重复为不同人在不同时期发起多次"),
        R25(25, "患者身份证、患者姓名", "患者身份证、患者姓名重复", "不同人为同一人发起多次且都在筹"),
        R26(26, "患者身份证、患者姓名", "患者身份证、患者姓名二次", "不同人为同一人在不同时期发起多次"),
        R27(27, "患者身份证、收款人身份证", "患者身份证、收款人身份证重复", "不同人为同一人发起多次且都在筹"),
        R28(28, "患者身份证、收款人身份证", "患者身份证、收款人身份证二次", "不同人为同一人在不同时期发起多次"),
        R29(29, "患者姓名、收款人身份证", "患者姓名、收款人身份证重复", "不同人为同一人发起多次且都在筹"),
        R30(30, "患者姓名、收款人身份证", "患者姓名、收款人身份证二次", "不同人为同一人在不同时期发起多次"),
        R31(31, "发起人身份证、发起人手机号、患者身份证", "发起人身份证、发起人手机号、患者身份证重复", "同一人为同一人发起多次且都在筹"),
        R32(32, "发起人身份证、发起人手机号、患者身份证", "发起人身份证、发起人手机号、患者身份证二次", "同一人为同一人在不同时期发起多次"),
        R33(33, "发起人身份证、发起人手机号、患者姓名", "发起人身份证、发起人手机号、患者姓名重复", "同一人为同一人发起多次且都在筹"),
        R34(34, "发起人身份证、发起人手机号、患者姓名", "发起人身份证、发起人手机号、患者姓名二次", "同一人为同一人在不同时期发起多次"),
        R35(35, "发起人身份证、发起人手机号、收款人身份证", "发起人身份证、发起人手机号、收款人身份证重复", "同一人为不同人发起多次且都在筹"),
        R36(36, "发起人身份证、发起人手机号、收款人身份证", "发起人身份证、发起人手机号、收款人身份证二次", "同一人为不同人在不同时期发起多次"),
        R37(37, "发起人身份证、患者身份证、患者姓名", "发起人身份证、患者身份证、患者姓名重复", "同一人为同一人发起多次且都在筹"),
        R38(38, "发起人身份证、患者身份证、患者姓名", "发起人身份证、患者身份证、患者姓名二次", "同一人为同一人在不同时期发起多次"),
        R39(39, "发起人身份证、患者身份证、收款人身份证", "发起人身份证、患者身份证、收款人身份证重复", "同一人为同一人发起多次且都在筹"),
        R40(40, "发起人身份证、患者身份证、收款人身份证", "发起人身份证、患者身份证、收款人身份证二次", "同一人为同一人在不同时期发起多次"),
        R41(41, "发起人身份证、患者姓名、收款人身份证", "发起人身份证、患者姓名、收款人身份证重复", "同一人为同一人发起多次且都在筹"),
        R42(42, "发起人身份证、患者姓名、收款人身份证", "发起人身份证、患者姓名、收款人身份证二次", "同一人为同一人在不同时期发起多次"),
        R43(43, "发起人手机号、患者身份证、患者姓名", "发起人手机号、患者身份证、患者姓名重复", "手机号重复为同一人发起多次且都在筹"),
        R44(44, "发起人手机号、患者身份证、患者姓名", "发起人手机号、患者身份证、患者姓名二次", "手机号重复为同一人在不同时期发起多次"),
        R45(45, "发起人手机号、患者身份证、收款人身份证", "发起人手机号、患者身份证、收款人身份证重复", "手机号重复为同一人发起多次且都在筹"),
        R46(46, "发起人手机号、患者身份证、收款人身份证", "发起人手机号、患者身份证、收款人身份证二次", "手机号重复为同一人在不同时期发起多次"),
        R47(47, "发起人手机号、患者姓名、收款人身份证", "发起人手机号、患者姓名、收款人身份证重复", "手机号重复为同一人发起多次且都在筹"),
        R48(48, "发起人手机号、患者姓名、收款人身份证", "发起人手机号、患者姓名、收款人身份证二次", "手机号重复为同一人在不同时期发起多次"),
        R49(49, "患者身份证、患者姓名、收款人身份证", "患者身份证、患者姓名、收款人身份证重复", "不同人为同一人发起多次且都在筹"),
        R50(50, "患者身份证、患者姓名、收款人身份证", "患者身份证、患者姓名、收款人身份证二次", "不同人为同一人在不同时期发起多次"),
        R51(51, "发起人身份证、发起人手机号、患者身份证、患者姓名", "发起人身份证、发起人手机号、患者身份证、患者姓名重复", "同一人为同一人发起多次且都在筹"),
        R52(52, "发起人身份证、发起人手机号、患者身份证、患者姓名", "发起人身份证、发起人手机号、患者身份证、患者姓名二次", "同一人为同一人在不同时期发起多次"),
        R53(53, "发起人身份证、发起人手机号、患者身份证、收款人身份证", "发起人身份证、发起人手机号、患者身份证、收款人身份证重复", "同一人为同一人发起多次且都在筹"),
        R54(54, "发起人身份证、发起人手机号、患者身份证、收款人身份证", "发起人身份证、发起人手机号、患者身份证、收款人身份证二次", "同一人为同一人在不同时期发起多次"),
        R55(55, "发起人身份证、发起人手机号、患者姓名、收款人身份证", "发起人身份证、发起人手机号、患者姓名、收款人身份证重复", "同一人为同一人发起多次且都在筹"),
        R56(56, "发起人身份证、发起人手机号、患者姓名、收款人身份证", "发起人身份证、发起人手机号、患者姓名、收款人身份证二次", "同一人为同一人在不同时期发起多次"),
        R57(57, "发起人身份证、患者身份证、患者姓名、收款人身份证", "发起人身份证、患者身份证、患者姓名、收款人身份证重复", "同一人为同一人发起多次且都在筹"),
        R58(58, "发起人身份证、患者身份证、患者姓名、收款人身份证", "发起人身份证、患者身份证、患者姓名、收款人身份证二次", "同一人为同一人在不同时期发起多次"),
        R59(59, "发起人手机号、患者身份证、患者姓名、收款人身份证", "发起人手机号、患者身份证、患者姓名、收款人身份证重复", "手机号重复为同一人发起多次且都在筹"),
        R60(60, "发起人手机号、患者身份证、患者姓名、收款人身份证", "发起人手机号、患者身份证、患者姓名、收款人身份证二次", "手机号重复为同一人在不同时期发起多次"),
        R61(61, "发起人身份证、发起人手机号、患者身份证、患者姓名、收款人身份证", "发起人身份证、发起人手机号、患者身份证、患者姓名、收款人身份证重复", "同一人为同一人发起多次且都在筹\t"),
        R62(62, "发起人身份证、发起人手机号、患者身份证、患者姓名、收款人身份证", "发起人身份证、发起人手机号、患者身份证、患者姓名、收款人身份证二次", "同一人为同一人在不同时期发起多次"),
        ;
        int code;
        String repeatContent;
        String desc;
        // 重复二次先前展示的文案
        String preDesc;

        // 重复情况对照表  1 发起人身份证  2 发起手机号  3 患者身份证  4 患者姓名  5 收款人身份证
        private static Map<String, Integer> repeatStr2Enum = Maps.newHashMap();
        static {
            repeatStr2Enum.put("1", 1);
            repeatStr2Enum.put("2", 3);
            repeatStr2Enum.put("3", 5);
            repeatStr2Enum.put("4", 7);
            repeatStr2Enum.put("5", 9);
            repeatStr2Enum.put("1,2", 11);
            repeatStr2Enum.put("1,3", 13);
            repeatStr2Enum.put("1,4", 15);
            repeatStr2Enum.put("1,5", 17);
            repeatStr2Enum.put("2,3", 19);
            repeatStr2Enum.put("2,4", 21);
            repeatStr2Enum.put("2,5", 23);
            repeatStr2Enum.put("3,4", 25);
            repeatStr2Enum.put("3,5", 27);
            repeatStr2Enum.put("4,5", 29);
            repeatStr2Enum.put("1,2,3", 31);
            repeatStr2Enum.put("1,2,4", 33);
            repeatStr2Enum.put("1,2,5", 35);
            repeatStr2Enum.put("1,3,4", 37);
            repeatStr2Enum.put("1,3,5", 39);
            repeatStr2Enum.put("1,4,5", 41);
            repeatStr2Enum.put("2,3,4", 43);
            repeatStr2Enum.put("2,3,5", 45);
            repeatStr2Enum.put("2,4,5", 47);
            repeatStr2Enum.put("3,4,5", 49);
            repeatStr2Enum.put("1,2,3,4", 51);
            repeatStr2Enum.put("1,2,3,5", 53);
            repeatStr2Enum.put("1,2,4,5", 55);
            repeatStr2Enum.put("1,3,4,5", 57);
            repeatStr2Enum.put("2,3,4,5", 59);
            repeatStr2Enum.put("1,2,3,4,5", 61);
        }

        public int getCode() {
            return code;
        }

        public String getRepeatContent() {
            return repeatContent;
        }

        public String getDesc() {
            return desc;
        }

        public static RepeatReason valueOfCode(int code) {

            RepeatReason[] values = RepeatReason.values();
            for (RepeatReason reason : values) {
                if (reason.code == code) {
                    return reason;
                }
            }
            return DEFAULT;
        }

        // repeatStr表示的是那几项数据是重复的用逗号隔开 eg 1,2  1,3,5
        public static  int getCodeByRepeatContent(String repeatStr, Date endTime, Date now) {
            Integer code = repeatStr2Enum.get(repeatStr);
            if (code == null) {
                throw new RuntimeException("重复类型不能找到对应的重复code, repeatStr: " + repeatStr);
            }

            if (endTime.before(now)) {
                code += 1;
            }
            return code;
        }

    }
}
