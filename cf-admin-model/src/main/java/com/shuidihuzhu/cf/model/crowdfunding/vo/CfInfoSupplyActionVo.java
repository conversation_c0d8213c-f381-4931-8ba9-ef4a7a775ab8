package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyAction;
import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE 2020/7/16
 */
@Data
public class CfInfoSupplyActionVo {

    //动态信息
    private CfInfoSupplyAction action;
    //工单id
    private long workOrderId;
    //审核人
    private String name;
    //审核时间
    private String time;
    //审核状态
    private int status;
    //案例id
    private int caseId;
    //案例标题
    private String title;
    //发起人
    private String initiator;
    //发起人手机
    private String initiatorPhone;
    private NumberMaskVo initiatorPhoneMask;
    //收款人
    private String receiver;
    //收款人手机
    private String receiverPhone;
    private NumberMaskVo receiverPhoneMask;

    private String infoUuid;
    //模板
    private String template;


}
