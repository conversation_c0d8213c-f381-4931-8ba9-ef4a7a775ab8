package com.shuidihuzhu.cf.model.crowdfunding.ai;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2020/12/17
 */
@Data
@NoArgsConstructor
public class AiRules  {


    private String param;
    /**
     * >
     * <
     * >=
     * <=
     * =
     */
    private String rule;

    /**
     * @see AiConditionEnum
     */
    private String condition;

    private String value;

    private String specialCheck;


    public AiRules(String param, String rule, String condition, String value) {
        this.param = param;
        this.rule = rule;
        this.condition = condition;
        this.value = value;
    }

    public AiRules(String param, String rule, String condition, String value, String specialCheck) {
        this.param = param;
        this.rule = rule;
        this.condition = condition;
        this.value = value;
        this.specialCheck = specialCheck;
    }
    //https://wiki.shuiditech.com/pages/viewpage.action?pageId=693928457  判断条件
    public static List<AiRules> getAiCheckRule(){

        List<AiRules> list = Lists.newArrayList();

        list.add(new AiRules("isMdNormalShow","=",AiConditionEnum.fou.getStringCode(),"323"));
        list.add(new AiRules("isMdClear","=",AiConditionEnum.fou.getStringCode(),"316"));
        list.add(new AiRules("hasPatientName","=",AiConditionEnum.wu.getStringCode(),"320"));
        list.add(new AiRules("hasDiseaseName","=",AiConditionEnum.wu.getStringCode(),"317"));
        list.add(new AiRules("hasOfficialseal","=",AiConditionEnum.fou.getStringCode(),"314"));
        list.add(new AiRules("hasModified","=",AiConditionEnum.shi.getStringCode(),"321"));
        list.add(new AiRules("hasWaterMark","=",AiConditionEnum.shi.getStringCode(),"322"));
        list.add(new AiRules("patientNameInMd","=",AiConditionEnum.shi.getStringCode(),"318,354","aiNameCondition"));

        list.add(new AiRules("hasMultiPatient","=","3","341"));
        list.add(new AiRules("hasVenerealDiseaseDesc","=",AiConditionEnum.wu.getStringCode(),"532"));
        list.add(new AiRules("fundUsage","=",AiConditionEnum.feihuanzhe.getStringCode(),"336"));
        list.add(new AiRules("commitMedicalInsuranceInTime","","","334,348","aiMedicalInsuranceCondition"));
        list.add(new AiRules("hasHealthInsurance","","","333,349","aiHasHealthInsuranceCondition"));
        list.add(new AiRules("hasPoverty","","","324","aiHasPoverty"));
        list.add(new AiRules("hasAccidentDesc","=",AiConditionEnum.you.getStringCode(),"537"));
        list.add(new AiRules("alreadyGetAmount","","","326,347","aiAlreadyGetAmount"));
        list.add(new AiRules("remainAmount","","","326","aiRemainAmount"));
        list.add(new AiRules("patientNameInTitle","","","350,354","aiNameCondition"));
        list.add(new AiRules("patientNameInContent","","","340,354","aiNameCondition"));


        return list;

    }

    public static List<AiRules> getAiMixCheckRule(){
        List<AiRules> list = Lists.newArrayList();

        list.add(new AiRules("name_1","","patientNameInContent","340,318","aiMixName"));
        list.add(new AiRules("name_2","","patientNameInTitle","350,318","aiMixName"));

        list.add(new AiRules("disease_1","","diseaseNameInTitle","319,351","aiMixDisease"));
        list.add(new AiRules("disease_2","","diseaseNameInContent","319,338","aiMixDisease"));

        list.add(new AiRules("disease_name","","hasDiseaseNameInMd","339","aiMixDiseaseName"));

        list.add(new AiRules("house_self","","13","546,535","aiMixHouse"));
        list.add(new AiRules("house_other","","14","549,535","aiMixHouse"));
        list.add(new AiRules("house_unknown","","15","535,547","aiMixHouse"));

        list.add(new AiRules("car","","hasCarDesc","538,548","aiMixCar"));


        list.add(new AiRules("targetAmountInDesc","special","","526","aiMixLabel"));



        return list;
    }

    public static List<AiRules> getAiSingleCheckRule(){
        List<AiRules> list = Lists.newArrayList();
        list.add(new AiRules("","","","","aiSpecialReportCondition"));
        list.add(new AiRules("","","","","aiPatientCivilActionCondition"));
        return list;
    }


}
