package com.shuidihuzhu.cf.model.admin.vo;

import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import lombok.Data;
import lombok.Getter;

import java.util.Date;

@Data
public class PromoteBillUploadVO extends WorkOrderVO {

    private Date firstDrawCashTime;

    private long firstDrawCashAmount;

    private long totalDrawCashAmount;

    private String caseRaiseName;

    private String raiseMobile;

    private String enterMobile;


    // com.shuidihuzhu.cf.enums.crowdfunding.CfCallOutConditionTypeEnum

    @Getter
    public enum UserFitLevel {

        LOW(1, "低"),
        MIDDLE(2, "中"),
        HIGH(3, "高"),

        ;

        private int code;
        private String msg;

        UserFitLevel(int code, String msg) {
            this.code = code;
            this.msg = msg;
        }
    }

    @Getter
    public enum UserNoFitReason {
        DEFAULT(0, "默认"),
        HANG_UP(1, "接通后挂断"),
        PATIENT_DEATH(2, "患者去世"),
        NOT_PAY(3, "还没交费"),
        NOT_HAVE_BILL(4, "没有票据"),
        LATER_UPLOAD(5, "后期上传"),
        CAN_NOT_OPERATE(6, "不会操作"),
        INCONVENIENT(7, "不方便没时间"),
        NOT_RAISE(8, "没发起过筹款"),
        REJECT_UPLOAD(9, "拒绝上传"),
        OTHER(10, "其他原因"),
        ;

        private int code;
        private String msg;

        UserNoFitReason(int code, String msg) {
            this.code = code;
            this.msg = msg;
        }
    }
}
