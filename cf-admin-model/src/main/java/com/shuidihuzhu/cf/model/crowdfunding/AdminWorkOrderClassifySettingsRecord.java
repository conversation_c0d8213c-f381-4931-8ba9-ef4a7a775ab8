package com.shuidihuzhu.cf.model.crowdfunding;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.Getter;

import java.util.Date;

@Data
public class AdminWorkOrderClassifySettingsRecord {

    private long id;

    private long classifyId;

    //操作类型参考
    private int operateType;

    private String comment;

    private String operatorName;

    private Date createTime;


    @ApiModel("操作类型")
    @Getter
    public enum OperateTypeEnum {
        create(1, "新增"),
        qi_yong(2, "启用"),
        fei_qi(3, "弃用"),
        shang_yi(4, "上移"),
        xia_yi(5, "下移");

        int code;
        String desc;

        OperateTypeEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static OperateTypeEnum findByCode(int code) {
            for (OperateTypeEnum value : OperateTypeEnum.values()) {
                if (value.getCode() == code) {
                    return value;
                }
            }
            return null;
        }
    }


    public static AdminWorkOrderClassifySettingsRecord populateRecord(int level, OperateTypeEnum operateTypeEnum,
                                                                String text, String operatorName, long problemClassifyId) {
        AdminWorkOrderClassifySettingsRecord record = new AdminWorkOrderClassifySettingsRecord();
        record.setClassifyId(problemClassifyId);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(operateTypeEnum.getDesc())
                .append("问题")
                .append(level == 1 ? "一级" : "二级")
                .append("分类")
                .append("\"").append(text)
                .append("\"");
        record.setComment(stringBuilder.toString());
        record.setOperateType(operateTypeEnum.getCode());
        record.setOperatorName(operatorName);
        return record;

    }


}
