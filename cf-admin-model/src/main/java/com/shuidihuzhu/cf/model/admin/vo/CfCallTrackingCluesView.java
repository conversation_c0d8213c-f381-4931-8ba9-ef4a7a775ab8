package com.shuidihuzhu.cf.model.admin.vo;

import com.shuidihuzhu.cf.model.admin.CfCallTrackingClues;
import org.springframework.beans.BeanUtils;

import java.sql.Timestamp;

/**
 * Created by Ahrievil on 2017/10/19
 */
public class CfCallTrackingCluesView extends CfCallTrackingClues {
    private int hasCase;
    private Timestamp caseCreateTime;

    public CfCallTrackingCluesView() {
    }

    public CfCallTrackingCluesView(CfCallTrackingClues cfCallTrackingClues, int hasCase, Timestamp caseCreateTime) {
        BeanUtils.copyProperties(cfCallTrackingClues, this);
        this.hasCase = hasCase;
        this.caseCreateTime = caseCreateTime;
    }

    public int getHasCase() {
        return hasCase;
    }

    public void setHasCase(int hasCase) {
        this.hasCase = hasCase;
    }

    public Timestamp getCaseCreateTime() {
        return caseCreateTime;
    }

    public void setCaseCreateTime(Timestamp caseCreateTime) {
        this.caseCreateTime = caseCreateTime;
    }
}
