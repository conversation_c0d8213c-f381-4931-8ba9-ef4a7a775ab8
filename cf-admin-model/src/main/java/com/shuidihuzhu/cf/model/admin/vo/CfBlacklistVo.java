package com.shuidihuzhu.cf.model.admin.vo;

import com.shuidihuzhu.client.cf.risk.model.CfRiskBlacklistDO;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @time 2019/9/24 上午10:28
 * @desc
 */
public class CfBlacklistVo extends CfRiskBlacklistDO {

    private String nickname;
    public CfBlacklistVo() {
    }

    public CfBlacklistVo(CfRiskBlacklistDO cfRiskBlacklistDO, String nickname) {
        BeanUtils.copyProperties(cfRiskBlacklistDO, this);
        this.nickname = nickname;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }
}
