package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation;

import java.time.LocalTime;

public class CrowdfundingOperationVo extends CrowdfundingOperation {
	
	private String operatorName;

	private boolean check;

	public String getOperatorName() {
		return operatorName;
	}

	public void setOperatorName(String operatorName) {
		this.operatorName = operatorName;
	}

	public boolean isCheck() {

		//不是主动服务
		if (this.getFuwuType() == 2){
			return true;
		}

		if (this.getFuwuType() == 1){
			if ((LocalTime.now().isBefore(LocalTime.of(9,0)) && LocalTime.now().isAfter(LocalTime.of(0,0)))
					|| (LocalTime.now().isAfter(LocalTime.of(21,30)) && LocalTime.now().isBefore(LocalTime.of(23,59)))  ){
				return true;
			}else {
				return false;
			}
		}

		return true;
	}

	public void setCheck(boolean check) {
		this.check = check;
	}
}
