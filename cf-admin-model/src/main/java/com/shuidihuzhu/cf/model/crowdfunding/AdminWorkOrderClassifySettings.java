package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.Data;
import lombok.Getter;

import java.util.Date;

@Data
public class AdminWorkOrderClassifySettings {
    private long id;
    private int operatorId;
    private String allText;
    private long parentId;
    private boolean autoTrigger;
    private boolean delete;
    //启动/废弃,0启动,1废弃
    private int available;
    //权重影响展示排序,同一个状态值越小排序越靠前
    private int weight;
    private Date createTime;
    private Date updateTime;

    @Getter
    public enum AvailableEnum {
        activity(0, "启动"),
        not_activity(1, "废弃"),
        ;

        int code;
        String desc;

        AvailableEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }


}
