package com.shuidihuzhu.cf.model.crowdfunding.vo;

import lombok.Data;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/17.
 */
@Data
public class WorkOrderReportWorkStatus {
    //人员名称
    private String name;
    //已领取
    private int getCount;
    //不需要处理
    private int noDeal;
    //新增跟进
    private int newFollow;
    //遗留跟进
    private int oldFollow;
    //今日跟进有跟进动作
    private int nowFollow;
    //今日遗留跟进
    private int nowLeaveFollow;
    //完成率
    private String ompleteRate;
    //处理完成
    private int dealComplete;
    private int userId;
}
