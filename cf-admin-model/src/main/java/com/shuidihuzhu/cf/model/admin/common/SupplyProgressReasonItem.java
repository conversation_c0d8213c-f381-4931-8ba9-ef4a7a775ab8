package com.shuidihuzhu.cf.model.admin.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2020-01-09 19:31
 **/
@NoArgsConstructor
@Data
@ApiModel("下发动态原因")
public class SupplyProgressReasonItem {

    @ApiModelProperty("原因id")
    private int id;

    @ApiModelProperty("原因描述")
    private String describe;

    @ApiModelProperty("模板")
    private String template;


    public SupplyProgressReasonItem(int id, String describe) {
        this.id = id;
        this.describe = describe;
    }

    public SupplyProgressReasonItem(int id, String describe, String template) {
        this.id = id;
        this.describe = describe;
        this.template = template;
    }

    @Getter
    public enum RejectItemEnum {
        reject_1(1, "部分审核后通过修改材料"),
        reject_2(2, "患者去世"),
        reject_3(3, "文诊不符"),
        reject_4(4, "无法提供诊断证明"),
        reject_5(5, "二次票据不足或未上传二次票据"),
        reject_6(6, "诊断证明无公章(有相同住院号或其他辅助材料)"),
        reject_7(7, "为多人筹款(无法确定为谁筹款)"),
        reject_8(8, "无法提供关系证明且无法对公"),
        reject_9(9, "增信内容不符"),
        reject_10(10, "患者姓名与诊断不符(错别字)"),
        ;

        private int code;
        private String desc;

        RejectItemEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    public static final List<SupplyProgressReasonItem> all_rejectItems = Arrays.stream(RejectItemEnum.values())
            .map(item -> new SupplyProgressReasonItem(item.getCode(), item.getDesc()))
            .collect(Collectors.toList());


    public static final List<Integer> all_rejectIds = Arrays.stream(RejectItemEnum.values())
            .map(RejectItemEnum::getCode)
            .collect(Collectors.toList());


}
