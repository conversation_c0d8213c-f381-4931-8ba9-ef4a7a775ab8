package com.shuidihuzhu.cf.model.record;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/9/1  3:10 下午
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CfAllOperateRecordDo {
    //    `case_id`      bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '案例id',
    //    `biz_id`       bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'biz_id',
    //    `biz_type`     int(11) unsigned    NOT NULL DEFAULT '0' COMMENT 'biz_type',
    //    `operate_type` int(11) unsigned    NOT NULL DEFAULT '0' COMMENT '操作类型',
    //    `content`      varchar(1000)       NOT NULL DEFAULT '' COMMENT '操作内容',
    //    `page`         varchar(100)        NOT NULL DEFAULT '' COMMENT '操作页面',
    //    `operator_id`  int(11) unsigned    NOT NULL DEFAULT '102' COMMENT '操作人id',
    //    `operator`     varchar(100)        NOT NULL DEFAULT 'system' COMMENT '操作人',
    //    `department`   varchar(128)        NOT NULL DEFAULT '' COMMENT '部门',
    private int caseId;
    private long bizId;
    private int bizType;
    private int operateType;
    private String content;
    private int pageType;
    private int operatorId;
    private String operator;
    private String department;
    private Date createTime;

    public static CfAllOperateRecordDo build(
            int operateType, int bizType, long bizId, int caseId, String content, int adminUserId, String adminUserName, int pageType, String organization) {
        return CfAllOperateRecordDo.builder()
                .operateType(operateType)
                .bizType(bizType)
                .bizId(bizId)
                .caseId(caseId)
                .content(content)
                .operatorId(adminUserId)
                .operator(adminUserName)
                .pageType(pageType)
                .department(organization)
                .build();
    }
}
