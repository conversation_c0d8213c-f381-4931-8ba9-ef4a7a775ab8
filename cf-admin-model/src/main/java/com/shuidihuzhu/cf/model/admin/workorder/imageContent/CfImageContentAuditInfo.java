package com.shuidihuzhu.cf.model.admin.workorder.imageContent;

import lombok.Data;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
public class CfImageContentAuditInfo {

    public static final Integer IMAGE = 1;
    public static final Integer CONTENT = 2;

    private int caseId;
    private long workOrderId;
    private int infoStatus;
    private String rejectDetail;

    private long operatorId;
    private String operatorDetail;



    public static class RejectInfo {

        private Map<Integer, String> rejectMsgs;

        private String comment;
    }



    @Getter
    public static enum ImageContextStatus {
        NO(0, "默认值"),
        IMAGE_PASS(1, "图片通过文章驳回"),
        CONTENT_PASS(2, "文章通过图片驳回"),
        PASS(3, "通过"),
        REJECT(4, "驳回"),
        ;

        ImageContextStatus(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private int code;
        private String desc;

        public static int getAuditStatus(Set<Integer> passTypes) {
            if (CollectionUtils.isEmpty(passTypes)) {
                return ImageContextStatus.REJECT.getCode();
            }

            if (passTypes.size() > 1) {
                return ImageContextStatus.PASS.getCode();
            }

            return passTypes.contains(IMAGE) ? ImageContextStatus.IMAGE_PASS.getCode() :
                    ImageContextStatus.CONTENT_PASS.getCode();
        }
    }


}
