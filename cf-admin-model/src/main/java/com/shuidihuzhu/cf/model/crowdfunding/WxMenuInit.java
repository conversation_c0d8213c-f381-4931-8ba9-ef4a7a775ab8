package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.enums.admin.common.WxMenuExcelCodeEnum;
import lombok.Data;
import me.chanjar.weixin.common.bean.menu.WxMenuButton;
import me.chanjar.weixin.common.bean.menu.WxMenuRule;
import org.apache.commons.lang3.StringUtils;

/**
 * Author:梁洪超
 * Date:2017/12/11
 */
@Data
public class WxMenuInit {
	
	private String menuType;
	private String bachFlag;
	private String bachs;
	private String channel;
	private String matchRuleFlag;
	private WxMenuRule wxMenuRule;
	private WxMenuButton wxMenuButton;

	
	public void checkBach() {
		if (WxMenuExcelCodeEnum.OK.getCodeString().equalsIgnoreCase(this.bachFlag)) {
			if (StringUtils.isEmpty(this.bachs)) {
				throw new IllegalArgumentException("批量修改时，bachs不能为空");
			}
			String[] arrays = bachs.trim().split(",");
			try {
				for (String indexs : arrays) {
					if (indexs.contains("_")) {
						String[] idexsString = indexs.trim().split("_");
						Integer.parseInt(idexsString[0].trim());
						Integer.parseInt(idexsString[1].trim());
					} else {
						Integer.parseInt(indexs.trim());
					}
				}
			} catch (Exception e) {
				throw new IllegalArgumentException("批量修改时，bachs格式类似 1_22,24,27_40");
			}
		} else {
			if (!StringUtils.isEmpty(menuType) && menuType.contains("1_0")) {
				try {
					Integer.parseInt(bachs.trim());
				} catch (Exception e) {
					throw new IllegalArgumentException("非批量修改时,1_0的bachs不能为空，且为整数类型");
				}
			}
			
		}
	}
	
	public void checkRuleFlag() {
		if (WxMenuExcelCodeEnum.OK.getCodeString().equalsIgnoreCase(this.matchRuleFlag)) {
			if (null == this.wxMenuRule) {
				throw new IllegalArgumentException("matchRuleFlag 为是时，必须填写比较规则");
			}
			if (StringUtils.isEmpty(this.wxMenuRule.getCity()) && StringUtils.isEmpty(this.wxMenuRule.getProvince())
			    && StringUtils.isEmpty(this.wxMenuRule.getTagId()) &&
			    StringUtils.isEmpty(this.wxMenuRule.getClientPlatformType())
			    && StringUtils.isEmpty(this.wxMenuRule.getCountry()) &&
			    StringUtils.isEmpty(this.wxMenuRule.getLanguage())
			    && StringUtils.isEmpty(this.wxMenuRule.getSex())) {
				throw new IllegalArgumentException("matchRuleFlag 为是时，必须填写比较规则,且比较字段至少一个有值");
			}
			if (StringUtils.isEmpty(this.wxMenuRule.getCountry()) &&
			    !StringUtils.isEmpty(this.wxMenuRule.getProvince())) {
				throw new IllegalArgumentException(
						"matchRuleFlag 为是时，地区信息从大到小验证，小的可以不填，即若填写了省份信息，则国家信息也必填并且匹配，城市信息可以不填。");
			}
			if (StringUtils.isEmpty(this.wxMenuRule.getProvince()) &&
			    !StringUtils.isEmpty(this.wxMenuRule.getCity())) {
				throw new IllegalArgumentException(
						"matchRuleFlag 为是时，地区信息从大到小验证，小的可以不填，即若填写了省份信息，则国家信息也必填并且匹配，城市信息可以不填。");
			}
		}
	}
}
