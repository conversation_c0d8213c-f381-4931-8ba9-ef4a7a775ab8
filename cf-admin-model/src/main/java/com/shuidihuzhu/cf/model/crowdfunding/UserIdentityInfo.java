package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/10/10 下午8:32
 * @desc
 */
@Data
public class UserIdentityInfo {
    //身份证号
    private String identity;
    private NumberMaskVo identityMask;
    //归属地
    private String identityOwnerShip;
    //姓名
    private String name;
    //银行卡列表
    private List<String> idcards;
    private List<NumberMaskVo> idCardsMask;

    //true:白名单
    private boolean whiteUser;
}
