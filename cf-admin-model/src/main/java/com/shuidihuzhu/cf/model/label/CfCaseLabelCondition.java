package com.shuidihuzhu.cf.model.label;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @Description: 条件
 * @Author: pan<PERSON><PERSON><PERSON>
 * @Date: 2023/8/9 2:40 PM
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CfCaseLabelCondition {

    /**
     * 条件名
     */
    private String conditionName;
    /**
     * 条件明细
     */
    private Map<String, Object> conditionDetail;

}
