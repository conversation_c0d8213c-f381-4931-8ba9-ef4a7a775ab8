package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.google.common.base.Joiner;
import com.google.common.collect.*;
import com.shuidihuzhu.cf.constants.admin.GeneralConstant;
import com.shuidihuzhu.cf.enums.crowdfunding.CfReportAddressEnum;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2019-12-10 19:37
 **/
@ApiModel("问题信息")
@Data
public class CfReportProblemVo extends CfReportProblem {

    @ApiModelProperty("加工后的id")
    private String attachId;

    @ApiModelProperty("下拉框信息")
    private List<Choice> choiceList;

    @ApiModelProperty("模块名称")
    private String labelDesc;

    @ApiModelProperty("什么时候显示例如problem:贷款总额 whenShow:是否有房贷-有,只有管理需要使用")
    private String whenShow;

    @ApiModelProperty("给listSameLabelProblem提供的字段,true:已经绑定过了")
    private boolean hasBind;

    @ApiModelProperty("地址信息 1:表示省,展示省下拉框 2:表示市,展示市下拉框")
    private int addressInfo;

    @ApiModel("下拉框或者文本框信息")
    @Data
    public static class Choice {
        @ApiModelProperty("下拉框id")
        private int answerId;
        @ApiModelProperty("下拉框")
        private String content;
        @ApiModelProperty("下拉框已经绑定的问题")
        private List<NextProblemInfo> nextProblemInfos;
        @ApiModelProperty("是否存在下级联动问题")
        private boolean hasNext;
    }


    @Data
    public static class NextProblemInfo {
        @ApiModelProperty("下拉框已经绑定的问题id")
        private int nextProblemId;
        @ApiModelProperty("下拉框已经绑定的问题名称")
        private String nextProblem;
    }


    /**
     *
     * 填充attachId、choiceList
     */
    public static List<CfReportProblemVo> fillExtInfo(List<CfReportProblem> problemList, List<CfReportProblemRelationship> relationships, List<CfReportProblemLabel> labels) {
        if (CollectionUtils.isEmpty(problemList)) {
            return Lists.newArrayList();
        }
        List<Integer> problemIds = problemList.stream().map(CfReportProblem::getId).collect(Collectors.toList());

        relationships = CollectionUtils.isNotEmpty(relationships) ? relationships : Lists.newArrayList();

        //当前问题关联的下级问题
        Multimap<Integer, CfReportProblemRelationship> problemId2Relationship =
                Multimaps.index(relationships.stream().filter(item -> problemIds.contains(item.getProblemId())).collect(Collectors.toList()), CfReportProblemRelationship::getProblemId);

        //当前问题关联的上级问题
        Multimap<Integer, CfReportProblemRelationship> nextProblemId2Relationship =
                Multimaps.index(relationships.stream().filter(item -> problemIds.contains(item.getNextProblemId())).collect(Collectors.toList()), CfReportProblemRelationship::getNextProblemId);


        labels = CollectionUtils.isNotEmpty(labels) ? labels : Lists.newArrayList();
        ImmutableMap<Integer, CfReportProblemLabel> labelId2Label = Maps.uniqueIndex(labels, CfReportProblemLabel::getId);

        List<CfReportProblemVo> problemVoList = Lists.newArrayList();
        for (CfReportProblem cfReportProblem : problemList) {
            CfReportProblemVo problemVo = new CfReportProblemVo();
            BeanUtils.copyProperties(cfReportProblem, problemVo);

            fillChoiceInfo(problemId2Relationship, problemVo);

            CfReportProblemLabel problemLabel = labelId2Label.get(cfReportProblem.getLabelId());
            if (problemLabel != null) {
                problemVo.setLabelDesc(problemLabel.getLabelDesc());
            }

            Set<String> showDesc = nextProblemId2Relationship
                    .get(cfReportProblem.getId())
                    .stream()
                    .map(item -> item.getProblem() + "-" + item.getContent())
                    .collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(showDesc)) {
                problemVo.setWhenShow(Joiner.on(";").join(showDesc));
                problemVo.setHasBind(true);
            }

            /**
             * 这几行代码写的膈应人
             */
            if("省".equals(problemVo.getProblem())){
                problemVo.setAddressInfo(CfReportAddressEnum.PROVINCE.getKey());
            } else if("市".equals(problemVo.getProblem())){
                problemVo.setAddressInfo(CfReportAddressEnum.CITY.getKey());
            } else {
                problemVo.setAddressInfo(CfReportAddressEnum.DEFAULT.getKey());
            }

            problemVoList.add(problemVo);

        }
        return problemVoList;
    }




    private static void fillChoiceInfo(Multimap<Integer, CfReportProblemRelationship> problemId2Relationship, CfReportProblemVo problemVo) {
        //填充choiceList
        int problemId = problemVo.getId();
        List<Choice> choiceList = Lists.newArrayList();
        ImmutableListMultimap<String, CfReportProblemRelationship> content2Relations =
                Multimaps.index(problemId2Relationship.get(problemId), item -> Optional.ofNullable(item).map(CfReportProblemRelationship::getContent).orElse(""));

        for (String content : content2Relations.keySet()) {
            Choice choice = new Choice();
            choice.setContent(content);
            ImmutableList<CfReportProblemRelationship> cfReportProblemRelationship = content2Relations.get(content);
            if (CollectionUtils.isNotEmpty(cfReportProblemRelationship)) {
                List<CfReportProblemRelationship> cfReportProblemRelationships =  cfReportProblemRelationship.stream().filter(t -> StringUtils.isNotBlank(content) && t != null && t.getNextProblemId() == 0).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(cfReportProblemRelationships)) {
                    CfReportProblemRelationship cfReportProblemRelationship1 = Optional.ofNullable(cfReportProblemRelationships.get(0)).orElseGet(CfReportProblemRelationship::new);
                    choice.setAnswerId(cfReportProblemRelationship1.getId());
                }
            }
            List<NextProblemInfo> nextProblemInfoList = Lists.newArrayList();
            content2Relations
                    .get(content)
                    .stream()
                    .filter(item -> item.getNextProblemId() > 0)
                    .filter(distinctByKey(CfReportProblemRelationship::getNextProblemId))
                    .forEach(item -> {
                        NextProblemInfo nextProblemInfo = new NextProblemInfo();
                        nextProblemInfo.setNextProblem(item.getNextProblem());
                        nextProblemInfo.setNextProblemId(item.getNextProblemId());
                        nextProblemInfoList.add(nextProblemInfo);
                    });
            choice.setNextProblemInfos(nextProblemInfoList);
            choice.setHasNext(CollectionUtils.isNotEmpty(nextProblemInfoList));
            choiceList.add(choice);
        }

        problemVo.setChoiceList(choiceList);
        problemVo.setAttachId(GeneralConstant.PROBLEM_PREFIX + problemId);
    }


    public static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> concurrentHashMap = Maps.newConcurrentMap();
        return t -> concurrentHashMap.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }


}
