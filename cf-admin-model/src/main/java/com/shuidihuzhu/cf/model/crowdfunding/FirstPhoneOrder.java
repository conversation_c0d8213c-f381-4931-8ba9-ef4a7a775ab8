package com.shuidihuzhu.cf.model.crowdfunding;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @DATE 2018/11/27
 */
@Data
public class FirstPhoneOrder {

    private long id;

    private long userId;

    private long operatorId;

    private String operatorName;

    private int orderStatus;

    private String title;

    private String content;

    private int targetAmount;

    private String mobile;

    private int firstStatus;

    private Date createTime;

    private boolean showPhone;


    private int callStatus;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

}

