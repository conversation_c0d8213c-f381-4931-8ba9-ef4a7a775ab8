package com.shuidihuzhu.cf.model.message;

import com.shuidihuzhu.msg.model.PushWxArticleTask;
import lombok.Data;

import java.util.Date;

/**
 * @Author: lian<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2018/9/27 15:47
 */
@Data
public class PushWxArticleTaskVo extends PushWxArticleTask {
    String createUserName;

    public PushWxArticleTaskVo() {
    }

    public PushWxArticleTaskVo(PushWxArticleTask pushWxArticleTask) {
        super.setId(pushWxArticleTask.getId());
        //文章配置名称
        super.setTaskName(pushWxArticleTask.getTaskName());
        //图文群发任务描述
        super.setDescription(pushWxArticleTask.getDescription());
        //文章推送账号主体third_type
        super.setThirdType(pushWxArticleTask.getThirdType());
        //消息推送账号主体名称
        super.setThirdName(pushWxArticleTask.getThirdName());
        super.setHasSend(pushWxArticleTask.getHasSend());
        //是否去重,1去重
        super.setNotAllowRepeat(pushWxArticleTask.getNotAllowRepeat());
        //是否删除
        super.setIsDelete(pushWxArticleTask.getIsDelete());
        //任务有效时间
        super.setExpiresTime(pushWxArticleTask.getExpiresTime());
        super.setCreateTime(pushWxArticleTask.getCreateTime());
        super.setUpdateTime(pushWxArticleTask.getUpdateTime());
        super.setStatus(pushWxArticleTask.getStatus());
    }
}
