package com.shuidihuzhu.cf.model.pay.wx;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.client.baseservice.pay.model.stock.WxStockActivityCreateParam;
import com.shuidihuzhu.client.baseservice.pay.model.stock.dto.ActivityBaseInfo;
import com.shuidihuzhu.client.baseservice.pay.model.stock.dto.AdvancedSetting;
import com.shuidihuzhu.client.baseservice.pay.model.stock.dto.AwardSendRule;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

import lombok.Data;

/**
 * @Author: lianghongchao
 * @Date: 2020/8/6 23:50
 */

/**
 * 微信支付有礼创建
 */
@ApiModel(value = "微信支付有礼创建")
@Data
public class CfWxStockActivityCreateRecord {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 商户侧需保持唯一性
     */
    @ApiModelProperty(value = "商户侧需保持唯一性")
    private String outRequestNo;

    /**
     * 商户商户编号不能为空
     */
    @ApiModelProperty(value = "商户商户编号不能为空")
    private Integer bizType;

    /**
     * 商户侧需保持唯一性。
     */
    @ApiModelProperty(value = "商户侧需保持唯一性。")
    private String channel;

    /**
     * 图片上传人user id
     */
    @ApiModelProperty(value = "图片上传人user id")
    private long operatorUserId;

    /**
     * 图片上传人user name
     */
    @ApiModelProperty(value = "图片上传人user name")
    private String operatorUserName;

    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    private String activityName;

    /**
     * 活动副标题
     */
    @ApiModelProperty(value = "活动副标题")
    private String activitySecondTitle;

    /**
     * 商户logo
     */
    @ApiModelProperty(value = "商户logo")
    private String merchantLogoUrl;

    /**
     * 背景颜色
     */
    @ApiModelProperty(value = "背景颜色")
    private String backgroundColor;

    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    private String activityBeginTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    private String activityEndTime;

    /**
     * 投放目的
     */
    @ApiModelProperty(value = "投放目的")
    private String deliveryPurposeEnumName;

    /**
     * 商家小程序appid
     */
    @ApiModelProperty(value = "商家小程序appid")
    private String miniProgramsAppid;

    /**
     * 商家小程序path
     */
    @ApiModelProperty(value = "商家小程序path")
    private String miniProgramsPath;

    /**
     * 消费金额门槛
     */
    @ApiModelProperty(value = "消费金额门槛")
    private Long transactionAmountMinimum;

    /**
     * 发放内容，可选单张券或礼包
     */
    @ApiModelProperty(value = "发放内容，可选单张券或礼包")
    private String sendContent;

    /**
     * 奖品类型，暂时只支持商家券
     */
    @ApiModelProperty(value = "奖品类型，暂时只支持商家券")
    private String awardType;

    /**
     * 奖品基本信息列表
     */
    @ApiModelProperty(value = "奖品基本信息列表")
    private String awardList;

    /**
     * 发券商户号选项
     */
    @ApiModelProperty(value = "发券商户号选项")
    private String merchantOption;

    /**
     * 发券商户号，列表
     */
    @ApiModelProperty(value = "发券商户号，列表")
    private String merchantIdList;

    /**
     * 领取用户范围
     */
    @ApiModelProperty(value = "领取用户范围")
    private String deliveryUserCategoryName;

    /**
     * 商户下单时需要传入相同的标记(goods_tag)
     */
    @ApiModelProperty(value = "商户下单时需要传入相同的标记(goods_tag)")
    private String goodsTags;

    /**
     * 执行结果
     */
    @ApiModelProperty(value = "执行结果")
    private String createMsg;

    /**
     * 执行结果
     */
    @ApiModelProperty(value = "执行结果")
    private String createCode;

    /**
     * 微信支付有礼批次号
     */
    @ApiModelProperty(value = "微信支付有礼批次号")
    private String activityId;

    /**
     * 微信支付有礼创建时间
     */
    @ApiModelProperty(value = "微信支付有礼创建时间")
    private String activityCreateTime;

    /**
     * 逻辑删除
     */
    @ApiModelProperty(value = "逻辑删除")
    private Byte isDelete;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    // TODO 字符串 null 的暂未处理
    public static CfWxStockActivityCreateRecord buildByWxStockActivityCreateParam(WxStockActivityCreateParam wxStockActivityCreateParam) {
        CfWxStockActivityCreateRecord cfWxStockActivityCreateRecord = new CfWxStockActivityCreateRecord();
        cfWxStockActivityCreateRecord.setBizType(wxStockActivityCreateParam.getWxBizType());

        ActivityBaseInfo activityBaseInfo = wxStockActivityCreateParam.getActivityBaseInfo();
        cfWxStockActivityCreateRecord.setActivityName(activityBaseInfo.getActivityName());
        cfWxStockActivityCreateRecord.setActivitySecondTitle(activityBaseInfo.getActivitySecondTitle());
        cfWxStockActivityCreateRecord.setMerchantLogoUrl(activityBaseInfo.getMerchantLogoUrl());
        cfWxStockActivityCreateRecord.setBackgroundColor(activityBaseInfo.getBackgroundColor());
        cfWxStockActivityCreateRecord.setActivityBeginTime(activityBaseInfo.getBeginTime());
        cfWxStockActivityCreateRecord.setActivityEndTime(activityBaseInfo.getEndTime());
        cfWxStockActivityCreateRecord.setOutRequestNo(activityBaseInfo.getOutRequestNo());
        cfWxStockActivityCreateRecord.setDeliveryPurposeEnumName(activityBaseInfo.getDeliveryPurpose().name());
        cfWxStockActivityCreateRecord.setMiniProgramsAppid(activityBaseInfo.getMiniProgramsAppid());
        cfWxStockActivityCreateRecord.setMiniProgramsPath(activityBaseInfo.getMiniProgramsPath());

        AwardSendRule awardSendRule = wxStockActivityCreateParam.getAwardSendRule();
        cfWxStockActivityCreateRecord.setTransactionAmountMinimum(awardSendRule.getTransactionAmountMinimum());
        cfWxStockActivityCreateRecord.setSendContent(awardSendRule.getSendContent().name());
        cfWxStockActivityCreateRecord.setAwardType(awardSendRule.getAwardType().name());
        cfWxStockActivityCreateRecord.setAwardList(JSON.toJSONString(awardSendRule.getActiveAwards()));
        cfWxStockActivityCreateRecord.setMerchantOption(awardSendRule.getMerchantOption().name());
        cfWxStockActivityCreateRecord.setMerchantIdList(JSON.toJSONString(awardSendRule.getMerchantIdList()));

        AdvancedSetting advancedSetting = wxStockActivityCreateParam.getAdvancedSetting();
        cfWxStockActivityCreateRecord.setDeliveryUserCategoryName(advancedSetting.getDeliveryUserCategory().name());
        cfWxStockActivityCreateRecord.setGoodsTags(JSON.toJSONString(advancedSetting.getGoodsTags()));


        return cfWxStockActivityCreateRecord;
    }
}