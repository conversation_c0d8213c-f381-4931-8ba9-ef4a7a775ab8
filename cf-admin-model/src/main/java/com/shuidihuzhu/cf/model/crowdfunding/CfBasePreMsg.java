package com.shuidihuzhu.cf.model.crowdfunding;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * <AUTHOR>
 * @DATE 2019/10/14
 */
@ApiModel("1v1生成案例前置信息")
@Data
public class CfBasePreMsg {

    private long id;

    private String mobile;

    private int caseId;

    private String infoId;

    private long taskId;

    private String diseaseName = StringUtils.EMPTY;

    private String reason = StringUtils.EMPTY;

    private String patientName = "";

    private int patientIdType;

    private String patientCryptoIdcard = "";

    private int relationType;

    private String initiatorName = "";

    private String initiatorCryptoIdcard = "";

    private String treatmentUrl ="";

    private int msgStatus;

    private String title = "";

    private String content = "";

    private int targetAmount = 0;

    private String picUrl = "";

    private String msgUrl;

    private int operatorId;

    private String operatorOrg;

    private int isDelete;

    private Date createTime;

    private Date updateTime;

    private UseTemplateRecord useRecord;

    /**
     * 换号标记
     */
    private int exchangeFlag;

    @Data
    public static class UseTemplateRecord {
        private long preId;
        private int titleId;
        private int contentId;
        private int relationType;
        private String diseaseName;
        private int age;
    }
}
