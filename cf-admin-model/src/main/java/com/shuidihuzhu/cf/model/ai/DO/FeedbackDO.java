package com.shuidihuzhu.cf.model.ai.DO;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2024/5/30 11:28 AM
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FeedbackDO {

    @ApiModelProperty("反馈类型")
    private Integer feedbackType;

    @ApiModelProperty("反馈内容")
    private String feedbackInfo;

    @ApiModelProperty("反馈人id")
    private Long operatorId;

}
