package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Set;

/**
 * Created by Ahrievil on 2017/8/15
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AdminCrowdfundingInfoView extends AdminCrowdfundingView {

    // 当前C端展示文章
    private String curContent;

    //公众号描述
    private String thirdType;
    // 案例的重复情况
    private Set<Integer> repeatStatusList;
    //来源渠道
    private String sourceChannel;

    /**
     * 筹款金额,单位:元
     */
    private double targetAmountInDouble;
    /**
     * 已筹金额,单位:元
     */
    private double amountInDouble;

    /**
     * 案例有效余额 单位元
     */
    private double surplusAmountInDouble;

    /**
     * 引导用户发起渠道
     */
    private String listGuideUserLaunchChannel;

    //待提交票据总额
    private double unCommitBillAmount;

    //需要提示待提交票据金额
    private boolean needMarkUnCommitBill;

    @ApiModelProperty(value = "打款成功金额---元")
    private String successDrawCashAmount;

    private Boolean payeeDithonest;

    private NumberMaskVo payeeIdCardMask;

    private NumberMaskVo payeeBankCardMask;
    /**
     * 收款人信息是否存在其他非该患者的案例上
     */
    private boolean payeeRepeatWarn;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
