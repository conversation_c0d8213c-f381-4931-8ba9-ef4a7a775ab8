package com.shuidihuzhu.cf.model.content.vo;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.model.content.SeoTagGroup;

import java.util.List;

/**
 * Created by wangsf on 17/7/3.
 */
public class SeoTagGroupVo {
	private int id;
	private String name;
	private String description;

	private List<SeoArticleTagVo> tags = Lists.newArrayList();

	public SeoTagGroupVo() {

	}

	public SeoTagGroupVo(SeoTagGroup seoTagGroup) {
		this.id = seoTagGroup.getId();
		this.name = seoTagGroup.getName();
		this.description = seoTagGroup.getDescription();
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public List<SeoArticleTagVo> getTags() {
		return tags;
	}

	public void setTags(List<SeoArticleTagVo> tags) {
		this.tags = tags;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;
		if (o == null || getClass() != o.getClass()) return false;

		SeoTagGroupVo that = (SeoTagGroupVo) o;

		return id == that.id;
	}

	@Override
	public int hashCode() {
		return id;
	}
}
