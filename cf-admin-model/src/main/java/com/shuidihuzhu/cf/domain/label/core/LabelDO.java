package com.shuidihuzhu.cf.domain.label.core;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class LabelDO {

  @ApiModelProperty("id")
  private long id;

  @ApiModelProperty("UUID")
  private String uuid;

  @ApiModelProperty("名称")
  private String name;

  @ApiModelProperty("全路径名")
  private String pathName;

  @ApiModelProperty("场景说明")
  private String labelDesc;

  @ApiModelProperty("顺序 小的在前")
  private int seq;

  /**
   * {@link com.shuidihuzhu.cf.domain.label.core.LabelConst.LabelStatusEnum}
   */
  @ApiModelProperty("启用状态{0: 初始化, 1: 启用, 2: 弃用, 10: 删除}")
  private int labelStatus;

  /**
   * {@link com.shuidihuzhu.cf.domain.label.core.LabelConst.RiskLevelEnum}
   */
  @ApiModelProperty("风险等级{0: 初始化, 10: 高, 20: 中, 30: 低}")
  private int riskLevel;

  @ApiModelProperty("最近一个父标签id")
  private long parentId;

  @ApiModelProperty("父节点id拼凑成字符串path eg: 1,5,9")
  private String parentPath;

}
