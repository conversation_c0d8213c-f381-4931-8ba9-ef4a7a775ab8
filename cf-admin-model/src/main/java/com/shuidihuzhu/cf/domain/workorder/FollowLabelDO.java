package com.shuidihuzhu.cf.domain.workorder;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * @Description: 跟进标签模型
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/7/22 2:26 下午
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FollowLabelDO {

    private Long workOrderId;

    private int followLabel;

    private Timestamp createTime;

}
