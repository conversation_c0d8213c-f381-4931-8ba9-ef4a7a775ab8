package com.shuidihuzhu.cf.domain.approve;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * @Description: 服务费补贴申请记录
 * @Author: pan<PERSON><PERSON><PERSON>
 * @Date: 2023/11/3 2:53 PM
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SubsidyApplyRecordDO {

    private Long workOrderId;

    private Long caseId;

    /**
     * 审核结果
     */
    private String auditResult;

    /**
     * 处理备注
     */
    private String handleRemark;

    private long operatorId;

    /**
     * 组织快照
     */
    private String organization;

    private Timestamp createTime;

}
