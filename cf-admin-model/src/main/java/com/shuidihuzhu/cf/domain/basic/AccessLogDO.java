package com.shuidihuzhu.cf.domain.basic;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccessLogDO {

    private long id;
    private int userId;
    private String pageName;
    private String userName;
    private String orgName;
    private Date createTime;
    private Date updateTime;
}
