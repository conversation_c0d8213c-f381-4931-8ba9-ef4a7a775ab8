package com.shuidihuzhu.cf.domain.label.risk;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class RiskLabelMarkRecordDO {

  @ApiModelProperty("id")
  private long id;

  @ApiModelProperty("案例id")
  private int caseId;

  @ApiModelProperty("场景")
  private int scene;

  @ApiModelProperty("'审核工单id'")
  private long orderId;

  @ApiModelProperty("'标签标记工单id'")
  private long markOrderId;

  /**
   * {@link RiskLabelConst.CaseRiskTypeEnum}
   */
  @ApiModelProperty("案例风险类型{0: 未选择, 1: 有风险, 2: 无风险}")
  private int caseRiskType;

  @ApiModelProperty("说明")
  private String markDesc;

  /**
   * {@link RiskLabelConst.RiskOverflowStatus}
   */
  @ApiModelProperty("风险溢出类型{0: 未选择, 1: 溢出, 2: 未溢出, 3: 不确定}")
  private int riskOverflowStatus;

  @ApiModelProperty("'操作人id'")
  private long operatorId;

  @ApiModelProperty("'操作人组织姓名'")
  private String operatorOrgName;

  @ApiModelProperty("标记类型{0: 普通标记, 1: 标签修正}")
  private int markType;

  @ApiModelProperty("创建时间")
  private Date createTime;

}
