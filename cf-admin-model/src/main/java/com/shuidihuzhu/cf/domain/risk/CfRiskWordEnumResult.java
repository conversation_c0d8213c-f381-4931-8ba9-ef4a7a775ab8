package com.shuidihuzhu.cf.domain.risk;


import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.ugc.model.domain.risk.RiskControlWordCategoryDO;
import lombok.Data;

import java.util.List;

@Data
public class CfRiskWordEnumResult {

    private List<EnumDisplay> useScenes;
    private List<EnumDisplay> colourTags;


    @Data
    private static class EnumDisplay {
        private String key;
        private int code;
        private String desc;

        public EnumDisplay(String key, int code, String desc) {
            this.key = key;
            this.code = code;
            this.desc = desc;
        }
    }

    public static CfRiskWordEnumResult result = new CfRiskWordEnumResult();
    static {
        List<EnumDisplay> useScenes = Lists.newArrayList();
        for (RiskControlWordCategoryDO.RiskWordUseScene useScene : RiskControlWordCategoryDO.RiskWordUseScene.values()) {
            useScenes.add(new EnumDisplay(useScene.name(), useScene.getCode(), useScene.getDesc()));
        }
        result.setUseScenes(useScenes);

        List<EnumDisplay> colourTags = Lists.newArrayList();
        for (RiskControlWordCategoryDO.RiskWorkColourTag tag : RiskControlWordCategoryDO.RiskWorkColourTag.values()) {
            colourTags.add(new EnumDisplay(tag.name(), tag.getCode(), tag.getDesc()));
        }
        result.setColourTags(colourTags);

    }
}
