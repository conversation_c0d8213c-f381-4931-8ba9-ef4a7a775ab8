package com.shuidihuzhu.cf.domain.label.risk;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class RiskLabelMarkRecordRel {

  @ApiModelProperty("'标签标记记录id'")
  private long labelMarkRecordId;

  @ApiModelProperty("'标签id'")
  private long labelId;

  @ApiModelProperty("'标签信息'")
  private Label label;

  @ApiModelProperty("说明")
  private String description;

  @ApiModelProperty("'风险捕获类型{0: 未选择, 1: 已捕获, 2: 未捕获}'")
  private int riskCatchStatus;

}
