package com.shuidihuzhu.cf.domain.approve;

import com.shuidihuzhu.cf.enums.approve.ApproveControlFlowTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class ApproveControlRecordDO {

    private long id;

    private int caseId;

    /**
     * @see ApproveControlFlowTypeEnum
     */
    @ApiModelProperty("流程类型 {0:默认, 1:正常任务工单 2:自动领取工单}")
    private int flowType;

    /**
     * @see com.shuidihuzhu.cf.enums.approve.ApproveControlHandleStatusEnum
     */
    @ApiModelProperty("处理状态{1:处理中,2:处理完成,3:失效}")
    private int handleStatus;

    /**
     * @see com.shuidihuzhu.cf.enums.approve.ApproveControlSourceTypeEnum
     */
    @ApiModelProperty("来源类型")
    private int sourceType;

    private long workOrderId;

    private int operatorId;

    @ApiModelProperty("处理时间")
    private Date handleTime;

    @ApiModelProperty("开始时间")
    private Date createTime;

    private Date updateTime;
}
