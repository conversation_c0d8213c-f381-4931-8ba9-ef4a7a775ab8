package com.shuidihuzhu.cf.domain.label.risk;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2022/12/3 15:40
 * @Description:
 */
@Data
public class RiskLabelAuditMarkParam {

    @ApiModelProperty("案例id")
    private int caseId;

    /**
     * @see com.shuidihuzhu.cf.domain.label.risk.RiskLabelConst.Scene
     */
    @ApiModelProperty("场景")
    private int scene;

    @ApiModelProperty("审核工单id")
    private long orderId;

    @ApiModelProperty("驳回项Id集合")
    private List<Integer> rejectIdList;

    @ApiModelProperty("'操作人id'")
    private long operatorId;

    @ApiModelProperty("'操作人组织姓名'")
    private String operatorOrgName;


}
