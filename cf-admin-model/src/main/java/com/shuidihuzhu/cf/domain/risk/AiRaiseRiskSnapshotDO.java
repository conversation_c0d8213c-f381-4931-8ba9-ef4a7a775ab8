package com.shuidihuzhu.cf.domain.risk;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2018-12-27  14:10
 */
@Data
@AllArgsConstructor
public class AiRaiseRiskSnapshotDO {

    private long id;

    private long caseId;

    private int template;
    private int templateTitleId;
    private int templateContentId;

    private String title;
    private String content;
    private String titleModified;
    private String contentModified;

    private String opLabel;
    private int opType;
    private String opComment;
    private String opReason;

    private String aiLabel;
    private int aiType;
    private int aiCode;
    private String aiResult;


}
