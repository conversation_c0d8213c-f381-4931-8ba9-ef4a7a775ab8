package com.shuidihuzhu.cf.domain.label.core;

import com.shuidihuzhu.cf.domain.label.risk.Label;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * 标签节点模型
 * <AUTHOR>
 */
@Data
public class LabelNodeModel {

  @ApiModelProperty("当前节点标签")
  private Label label;

  @ApiModelProperty("当前节点子节点列表")
  private List<LabelNodeModel> childLabelNodeList;

//  @ApiModelProperty("当前是否是叶子节点")
//  public boolean isLeaf() {
//    return CollectionUtils.isEmpty(childLabelNodeList);
//  }

}
