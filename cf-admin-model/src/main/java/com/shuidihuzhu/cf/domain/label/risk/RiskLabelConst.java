package com.shuidihuzhu.cf.domain.label.risk;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

/**
 * <AUTHOR>
 */
public class RiskLabelConst {

    @Getter
    public enum Scene {
        /**
         * 初审-停止筹款；初审-驳回；图文审核-驳回；材审-停止筹款；材审-驳回；举报-结束处理；举报详情页-散点；案例详情页-停止筹款；案例详情页-散点；付款审核-驳回；退款审核-拒绝申请；修改收款人审核-驳回
         */
        INITIAL_AUDIT_STOP_CASE(1, "初审-停止筹款"),
        INITIAL_AUDIT_REFUSE(2, "初审-驳回"),
        INFO_AUDIT_STOP_CASE(3, "材审-停止筹款"),
        INFO_AUDIT_REFUSE(4, "材审-驳回"),
        REPORT_END_DEAL(5, "举报-结束处理"),
        REPORT_DETAIL(6, "举报详情页-散点"),
        CASE_DETAIL_STOP_CASE(7, "案例详情页-停止筹款"),
        CASE_DETAIL(8, "案例详情页-散点"),
        PAY_AUDIT_REFUSE(9, "付款审核-驳回"),
        REFUND_AUDIT_REFUSE(10, "退款审核-拒绝申请"),
        MODIFY_PAYEE_AUDIT_REFUSE(11, "修改收款人审核-驳回"),
        HIGH_RISK_INITIAL_AUDIT_STOP_CASE(12, "高风险工单-停止筹款"),
        HIGH_RISK_INITIAL_AUDIT_REFUSE(13, "高风险工单-驳回"),
        CONTENT_AUDIT_REFUSE(14, "图文审核工单-驳回"),
        ;

        private final int value;
        private final String desc;

        Scene(int value, String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    @Getter
    public enum CaseRiskTypeEnum {
        UNDEFINE(0, "未选择"),
        HAVE_RISK(1, "有风险"),
        NO_RISK(2, "无风险"),
        ;

        private final int value;
        private final String desc;

        CaseRiskTypeEnum(int value, String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    @Getter
    public enum RiskCatchStatusEnum {
        NO_CHOOSE(0, "未选择"),
        HAS_CATCH(1, "已捕获"),
        NO_CATCH(2, "未捕获"),
        ;

        private final int value;
        private final String desc;

        RiskCatchStatusEnum(int value, String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    @Getter
    public enum RiskOverflowStatus {
        NO_CHOOSE(0, "未选择"),
        HAS_OVERFLOW(1, "溢出"),
        NO_OVERFLOW(2, "未溢出"),
        NOT_CONFIRM(2, "不确定"),
        ;

        private final int value;
        private final String desc;

        RiskOverflowStatus(int value, String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    @Getter
    public enum RiskMarkType {
        NORMAL(0, "普通标记"),
        FIX(1, "标签修正"),
        ;

        private final int value;
        private final String desc;

        RiskMarkType(int value, String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

}
