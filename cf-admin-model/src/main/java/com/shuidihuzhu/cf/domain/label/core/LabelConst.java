package com.shuidihuzhu.cf.domain.label.core;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 */
public class LabelConst {


    @Getter
    public enum LabelStatusEnum {
        INIT(0, "初始化"),
        ENABLE(1, "启用"),
        DISABLE(2, "弃用"),
        REMOVE(10, "已删除"),
        ;

        private static final List<Integer> ALL_SHOW_LIST = Lists.newArrayList(
                INIT.getValue(),
                ENABLE.getValue(),
                DISABLE.getValue());

        private final int value;
        private final String desc;

        public static List<Integer> getAllShowList(){
            return ALL_SHOW_LIST;
        }

        public static String getOperateMsg(int status){
            if (status == INIT.getValue()) {
                return "新增";
            }
            if (status == ENABLE.getValue()) {
                return "启用";
            }
            if (status == DISABLE.getValue()) {
                return "弃用";
            }
            if (status == REMOVE.getValue()) {
                return "删除";
            }
            return "未知";
        }

        LabelStatusEnum(int value, String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    @Getter
    public enum RiskLevelEnum {
        INIT(0, "初始化"),
        HIGH(10, "高"),
        MIDDLE(20, "中"),
        LOW(30, "低"),
        ;

        private final int value;
        private final String desc;

        RiskLevelEnum(int value, String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

}
