package com.shuidihuzhu.cf.domain.approve;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Author: wangpeng
 * @Date: 2021/7/1 21:12
 * @Description:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdminApproveOperateRecord {
    private String operateReason;
    private int operateType;
    private String operateOrgName;
    private String operateName;
    private int operateId;
    private Date operateTime;
}
