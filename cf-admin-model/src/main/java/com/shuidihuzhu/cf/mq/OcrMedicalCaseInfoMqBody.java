package com.shuidihuzhu.cf.mq;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: wangpeng
 * @Date: 2021/4/20 20:09
 * @Description:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OcrMedicalCaseInfoMqBody {

    /**
     * 消息的topic
     */
    public static final String MQ_TOPIC = "OCR_MEDICAL_CASE_INFO_TOPIC";

    /**
     * 消息的tag
     */
    public static final String MQ_TAG = "OCR_MEDICAL_CASE_INFO_TAG";

    /**
     * 案例Id
     */
    private String medicalCaseId;

    /**
     * 案例标题
     */
    private String title;

    /**
     * 案例内容
     */
    private String content;

    /**
     * 医疗材料图片地址
     */
    private String pictureUrl;

}
