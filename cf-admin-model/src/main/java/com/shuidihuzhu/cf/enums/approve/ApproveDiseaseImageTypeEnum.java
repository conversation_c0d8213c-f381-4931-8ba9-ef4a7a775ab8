package com.shuidihuzhu.cf.enums.approve;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@ApiModel("初审疾病图片材料类型")
public enum ApproveDiseaseImageTypeEnum {

    /**
     */
    DEFAULT(0, "未知"),

    @ApiModelProperty("机打材料")
    MACHINE_PRINT(1, "机打材料"),

    @ApiModelProperty("手写材料")
    HAND_WRITE(2, "手写材料"),

    ;

    @Getter
    private final int value;

    @Getter
    private final String msg;

    ApproveDiseaseImageTypeEnum(int value, String msg) {
        this.value = value;
        this.msg = msg;
    }

    public static ApproveDiseaseImageTypeEnum parse(int code) {
        for (ApproveDiseaseImageTypeEnum e : ApproveDiseaseImageTypeEnum.values()){
            if (e.getValue() == code) {
                return e;
            }
        }
        throw new IllegalArgumentException("ApproveDiseaseImageTypeEnum not found code: " + code);
    }
}
