package com.shuidihuzhu.cf.enums.approve;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

/**
 * <AUTHOR>
 */
public enum ApproveControlFlowTypeEnum {

    /**
     * 默认逻辑
     */
    @ApiModelProperty("默认逻辑")
    DEFAULT(0, true),

    @ApiModelProperty("任务工单正常流程")
    NORMAL_WORK_ORDER_FLOW(1, false),

    @ApiModelProperty("自动领取工单流程")
    AUTO_WORK_ORDER_FLOW(2, true),

    ;

    @Getter
    private final int value;

    @Getter
    private final boolean canAutoReleaseLock;

    ApproveControlFlowTypeEnum(int value, boolean canAutoReleaseLock) {
        this.value = value;
        this.canAutoReleaseLock = canAutoReleaseLock;
    }

    public static ApproveControlFlowTypeEnum parse(int value){
        for (ApproveControlFlowTypeEnum e : ApproveControlFlowTypeEnum.values()) {
            if (value == e.getValue()) {
                return e;
            }
        }
        throw new IllegalArgumentException("can not find ApproveControlFlowTypeEnum value: " + value);
    }
}
