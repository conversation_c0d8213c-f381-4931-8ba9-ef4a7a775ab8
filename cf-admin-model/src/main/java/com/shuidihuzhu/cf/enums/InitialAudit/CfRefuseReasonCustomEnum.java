package com.shuidihuzhu.cf.enums.InitialAudit;

import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: wangpeng
 * @Date: 2022/1/6 20:04
 * @Description:
 */
public enum CfRefuseReasonCustomEnum {
    NORMAL(1, "正在使用"),
    NOT_NORMAL(2, "废弃");


    private final static Map<Integer, CfRefuseReasonCustomEnum> map = Maps.newHashMap();

    static {
        map.putAll(Arrays.stream(CfRefuseReasonCustomEnum.values()).collect(Collectors.toMap(CfRefuseReasonCustomEnum::getType, Function.identity())));
    }

    @Getter
    private final int type;

    @Getter
    private final String msg;


    CfRefuseReasonCustomEnum(int type, String msg) {
        this.type = type;
        this.msg = msg;
    }


    public static List<Integer> getList(){
        return new ArrayList<>(map.keySet());
    }
}
