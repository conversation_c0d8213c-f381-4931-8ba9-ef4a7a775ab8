package com.shuidihuzhu.cf.enums.admin;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * @package: com.shuidihuzhu.cf.enums.admin
 * @Author: liu<PERSON><PERSON><PERSON>
 * @Date: 2020-01-09  11:14
 */
public class DynamicMsgEnum {


    /**
     * 均为产品配置消息模型
     */
    public enum PushMsgType {


        ACTIVE_PUSH(1, "主动下发", "AMHUGR100250024", "FJTTGJ100264047", "FJTTGJ100264047"),
        REJECT_PUSH(2, "驳回下发", "XTCOFR100264052", "QBPUWN100264057", "QBPUWN100264057"),
        /**
         * 系统内部使用，入参请勿传
         */
        URGE_24H(3, "24小时催下发", "TEBAGQ100264051", "SFUBVU100264058", "SFUBVU100264058"),
        URGE_72H(4, "72小时催下发", "BLWFPT100250030", "CDXPUU100264059", "CDXPUU100264059"),
        ;

        private int code;

        private String description;

        private String wxMsgModelNum;

        private String smsMsgModelNum;

        private String reissueSmsMsgModelNum;

        public String getReissueSmsMsgModelNum() {
            return reissueSmsMsgModelNum;
        }

        public void setReissueSmsMsgModelNum(String reissueSmsMsgModelNum) {
            this.reissueSmsMsgModelNum = reissueSmsMsgModelNum;
        }

        public String getWxMsgModelNum() {
            return wxMsgModelNum;
        }

        public void setWxMsgModelNum(String wxMsgModelNum) {
            this.wxMsgModelNum = wxMsgModelNum;
        }

        public String getSmsMsgModelNum() {
            return smsMsgModelNum;
        }

        public void setSmsMsgModelNum(String smsMsgModelNum) {
            this.smsMsgModelNum = smsMsgModelNum;
        }

        private static final Map<Integer, PushMsgType> MAP = Maps.newHashMap();

        static {
            for (PushMsgType feedBackStateEnum : PushMsgType.values()) {
                MAP.put(feedBackStateEnum.getCode(), feedBackStateEnum);
            }
        }

        PushMsgType(int code, String description, String wxMsgModelNum, String smsMsgModelNum, String reissueSmsMsgModelNum) {
            this.code = code;
            this.description = description;
            this.wxMsgModelNum = wxMsgModelNum;
            this.smsMsgModelNum = smsMsgModelNum;
            this.reissueSmsMsgModelNum = reissueSmsMsgModelNum;
        }

        public int getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static PushMsgType getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            return MAP.get(code);
        }

    }
}
