package com.shuidihuzhu.cf.enums;

public enum GoodsStatusEnum {
	PAY_NOT(0, "待支付"),
	SHIPMENT_PENDING(1, "待发货"),
	SHIPPED(2, "已发货"),
	CLOSE(10, "关闭");
	private Integer code;
	private String message;

	GoodsStatusEnum(Integer code, String message) {
		this.code = code;
		this.message = message;
	}

	public Integer getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}

	public static GoodsStatusEnum getByValue (int code) {
		GoodsStatusEnum[] values = GoodsStatusEnum.values();
		for (GoodsStatusEnum value : values) {
			if (value.getCode() == code) {
				return value;
			}
		}
		return PAY_NOT;
	}
}
