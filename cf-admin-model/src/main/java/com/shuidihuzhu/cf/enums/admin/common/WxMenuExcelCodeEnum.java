package com.shuidihuzhu.cf.enums.admin.common;

/**
 * Author:梁洪超
 * Date:2017/12/11
 */
public enum WxMenuExcelCodeEnum {
	TYPE(1, "type"),
	NAME(2, "name"),
	KEY(3, "key"),
	URL(4, "url"),
	MEDIA_ID(5, "mediaId"),
	TAG_ID(6, "tagId"),
	SEX(7, "sex"),
	COUNTRY(8, "country"),
	PROVINCE(9, "province"),
	CITY(10, "city"),
	CLIENT_PLATFORM_TYPE(11, "clientPlatformType"),
	LANGUAGE(12, "language"),
	MENU_TYPE(13, "menuType"),
	MATCHRULE(14, "matchrule"),
	BACH_FLAG(15, "bachFlag"),
	BACHS(16, "bachs"),
	CHANNEL(17, "channel"),
	OK(18, "是"),
	APP_ID(19,"appId"),
	PAGE_PATH(20,"pagePath");
	
	private int code;
	private String codeString;
	
	public int getCode() {
		return code;
	}
	
	WxMenuExcelCodeEnum(int code, String codeString) {
		this.code = code;
		this.codeString = codeString;
	}

	public String getCodeString() {
		return codeString;
	}

}
