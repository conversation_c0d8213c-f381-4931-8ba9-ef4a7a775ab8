package com.shuidihuzhu.cf.enums.crowdfunding;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * Created by w<PERSON><PERSON><PERSON> on 7/7/16.
 *
 * Altered by <PERSON> 2016-09-09
 */

public enum AdminAttachmentTypeEnum {
	ATTACH_CF(0, "图文信息-图片"),       // 筹款详情图片,取第一张作为封面图
	ATTACH_ID_CARD(1, "受助人(患者)手持身份证照"),  // 受助人(患者)手持身份证照
	ATTACH_TREATMENT(2, "医疗诊断证明"), // 医疗诊断证明
	ATTACH_EXTRA(3, "附加图片"),    // 附加图片
	ATTACH_PAYEE_ID_CARD(4, "收款人手持身份证照片"), // 收款人手持身份证照片
	ATTACH_PAYEE_RELATION(5, "收款人和受助人关系证明图片"), // 收款人和受助人关系证明图片(户口本|结婚证)
	ATTACH_TREATMENT_VERIFY(6, "患者手持医疗证明照片"), // 患者手持医疗证明照片
	ATTACH_APPLY_DRAW(7, "提现申请附件图片"), // 提现申请附件图片
	ATTCH_ORGANIZATION_QUALIFICATION(8, "梦想筹组织资质证明"), // 梦想筹组织资质证明
	ATTACH_DREAM_CONFIRM(9, "梦想筹项目证明图片"), // 梦想筹项目证明图片
    ATTACH_VIDEO(10, "筹款视频"), // 筹款视频
    ATTACH_VIDEO_COVER_PLAN(11, "筹款视频封面图"), // 筹款视频封面图
    ATTACH_MEDICAL_RECORD_HOME(12, "病案首页"), //病案首页
    ATTACH_PASS_HOSPITAL(13, "出院或者入院证明"), // 出院或者入院证明
    ATTACH_TREATMENT_NOTE(14, "医疗票据"), // 医疗票据
    ATTACH_INSPECTION_REPORT(15, "检查报告"), // 检查报告
    ATTACH_ONLY_ID_CARD(16, "单独身份证照片"),//单独身份证照片
    ATTACH_CHARITY(17, "慈善组织证明照片"),//慈善组织证明照片
    ATTACH_PAYEE_RELATION_VIDEO(18, "收款人与患者关系视频"), // 收款人与患者关系视频

    ATTACH_ACTIVITY_PAYEE_ACCOUNT_RELATION(27, "案例活动收款账户的关系照片"), // 案例活动收款账户的关系照片
    ATTACH_ACTIVITY_PAYEE_ACCOUNT_IDCARD(28, "案例活动收款账户的身份证照片"), // 案例活动收款账户的身份证照片

    //********收款人与患者的关系证明***********
    ATTACH_PAYEE_HUKOUBEN(29, "户口本"), // 户口本
    ATTACH_PAYEE_JIEHUNZHENG(30, "结婚证"), // 结婚证
    ATTACH_PAYEE_CHUSHENGZHENG(31, "出生证"), // 出生证
    ATTACH_PAYEE_ZHENGMING(32, "村委会/居委会/派出所证明"), //村委会/居委会/派出所证明
    ATTACH_PAYEE_WEITUOSHU(33, "授权委托书"), //授权委托书

    //********收款人与患者的关系证明***********

    //********收款人的身份证***********
    ATTACH_PAYEE_ONLY_ID_CARD(34, "收款人的身份证"),
    //********收款人的身份证***********

    ATTACH_IN_HOSPITAL(35, "住院证明"),   //住院证明
    ATTACH_LEAVE_HOSPITAL(36, "出院证明"), //出院证明

    ATTACH_PAYEE_WEITUOSHU_HEZHAO(37, "患者和授权委托书合照"), //患者和授权委托书合照

    // begin 人脸识别的视频会被删掉
    ATTACH_PATIENT_FACE_ID(38, "患者身份证人脸识别结果"), //患者身份证人脸识别结果

    ATTACH_PAYEE_FACE_ID(39, "收款人身份证人脸识别结果"), //收款人身份证人脸识别结果
    // end 人脸识别的视频会被删掉

    MOST_EXPENSIVE_MEDICINE_PIC(40, "最贵药品图片"), // 最贵药品图片

    ;
    ;


    private int status;
    private String word;

    AdminAttachmentTypeEnum(int value, String word) {
        this.status = value;
        this.word = word;
    }

    public int value() {
        return status;
    }

    public String getWord() {
        return word;
    }

    public static String getWord(int value) {
        for (AdminAttachmentTypeEnum approveListOrderByWhat: AdminAttachmentTypeEnum.values()) {
            if (approveListOrderByWhat.value() == value) {
                return approveListOrderByWhat.getWord();
            }
        }
        return "";
    }

    public static Map<Integer, AttachmentTypeEnum> map = Maps.newHashMap();

    static {
        for (AttachmentTypeEnum attachmentTypeEnum : AttachmentTypeEnum.values()) {
            map.put(attachmentTypeEnum.value(), attachmentTypeEnum);
        }
    }

    public static Map<Integer, String> emnuMap = Maps.newHashMap();

    static {
        for (AdminAttachmentTypeEnum adminAttachmentTypeEnum : AdminAttachmentTypeEnum.values()) {
            emnuMap.put(adminAttachmentTypeEnum.value(), adminAttachmentTypeEnum.getWord());
        }
    }
}
