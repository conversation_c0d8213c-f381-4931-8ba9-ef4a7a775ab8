package com.shuidihuzhu.cf.enums.crowdfunding;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * @Auther: subing
 * @Date: 2020/4/22
 */
public enum AdminCfReportProblemModuleMandatoryStatus {
    //
    IS_MANDATORY(1, "必填"),
    IS_NOT_MANDATORY(0, "非必填"),
    ;
    private int type;
    private String description;

    public int getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }

    AdminCfReportProblemModuleMandatoryStatus(int type, String description) {
        this.type = type;
        this.description = description;
    }
    static Map<Integer, String> maps =  Maps.newHashMap();

    static {
        for (AdminCfReportProblemModuleMandatoryStatus fundUseAuditStatusType : AdminCfReportProblemModuleMandatoryStatus.values()) {
            maps.put(fundUseAuditStatusType.type, fundUseAuditStatusType.description);
        }
    }

    public static String getDescription(int type) {
        for (AdminCfReportProblemModuleMandatoryStatus fundUseAuditStatusType : AdminCfReportProblemModuleMandatoryStatus.values()) {
            if (fundUseAuditStatusType.getType() == type) {
                return  fundUseAuditStatusType.getDescription();
            }
        }
        return "";
    }
}
