package com.shuidihuzhu.cf.enums.crowdfunding;

import lombok.Getter;
import org.apache.commons.collections.map.HashedMap;

import java.util.Map;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2021-01-03 18:42
 **/
public enum ReportHandleReasonEnum {

    REASON_1(1, "未联系上质疑人"),
    REASON_2(2, "未联系上发起人"),
    REASON_3(3, "未联系上筹款顾问"),
    REASON_4(4, "待与发起人核实信息"),
    REASON_5(5, "特殊报备待确认方案"),
    REASON_6(6, "其他"),

    ;

    @Getter
    public int code;
    @Getter
    public String desc;

    private static Map<Integer, ReportHandleReasonEnum> map = new HashedMap();

    static {
        for (ReportHandleReasonEnum value : ReportHandleReasonEnum.values()) {
            map.put(value.getCode(), value);
        }
    }

    ReportHandleReasonEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ReportHandleReasonEnum getByCode(int code) {
        return map.get(code);
    }
}
