package com.shuidihuzhu.cf.enums.rule;


import lombok.Getter;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2020/1/2
 */
@Getter
public enum RiskRuleCondition {


    equal(1,"等于"),

    equal_less(2,"小于等于"),

    equal_greater(3,"大于等于"),

    less(4,"小于"),

    greater(5,"大于"),

    ;


    private int code;

    private String msg;

    RiskRuleCondition(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }


    public static boolean condition(int value, List<Integer> thresholds, RiskRuleCondition condition){

        if (thresholds.size() == 1){
            int threshold = thresholds.get(0);
            switch (condition){
                case less:
                    return value < threshold;
                case equal:
                    return value == threshold;
                case greater:
                    return value > threshold;
                case equal_less:
                    return value <= threshold;
                case equal_greater:
                    return value >= threshold;
                default:
                    return false;
            }
        }

        if (thresholds.size() == 2){

            int min = thresholds.get(0);
            int max = thresholds.get(1);
            switch (condition){
                case equal:
                    return value >= min && value <= max;
                case equal_less:
                    return value > min && value <= max;
                case equal_greater:
                    return value >= min && value < max;
                default:
                    return false;
            }

        }
        return false;
    }
}
