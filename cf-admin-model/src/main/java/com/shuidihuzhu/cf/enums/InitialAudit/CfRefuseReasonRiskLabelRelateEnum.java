package com.shuidihuzhu.cf.enums.InitialAudit;

import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: wangpeng
 * @Date: 2022/12/1 17:49
 * @Description:
 */
public enum CfRefuseReasonRiskLabelRelateEnum {
    NO(0, "无关联"),
    AUTO(1, "自动关联"),
    HUMAN(2, "人工关联");


    private final static Map<Integer, CfRefuseReasonRiskLabelRelateEnum> map = Maps.newHashMap();
    private static CfRefuseReasonRiskLabelRelateEnum cfRefuseReasonRiskLabelRelateEnum;

    static {
        map.putAll(Arrays.stream(CfRefuseReasonRiskLabelRelateEnum.values()).collect(Collectors.toMap(CfRefuseReasonRiskLabelRelateEnum::getType, Function.identity())));
    }

    @Getter
    private final int type;

    @Getter
    private final String msg;


    CfRefuseReasonRiskLabelRelateEnum(int type, String msg) {
        this.type = type;
        this.msg = msg;
    }

    public static CfRefuseReasonRiskLabelRelateEnum getCfRefuseReasonRiskLabelRelateEnum(int type){
        return map.get(type);
    }

    public static List<Integer> getList(){
        return new ArrayList<>(map.keySet());
    }
}
