package com.shuidihuzhu.cf.enums;

import lombok.Getter;

@Getter
public enum NotifyOnlineVolunteerEventEnum {
    INITIAL_AUDIT_EMERGENCY_EVENT(1, "初审工单升级应急组"),
    TARGET_AMOUNT_WORK_ORDER_CRATE_EVENT(2, "目标金额工单创建"),
    MEDICAL_WORK_ORDER_CRATE_EVENT(3, "医疗审核工单创建"),
    TARGET_AMOUNT_WORK_ORDER_REJECT_EVENT(4, "目标金额工单驳回"),
    MEDICAL_WORK_ORDER_REJECT_EVENT(5, "医疗审核工单驳回"),
    ER_CI_WORK_ORDER_REJECT_EVENT(6, "二次审核工单驳回");
    private int type;
    private String desc;
    NotifyOnlineVolunteerEventEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
