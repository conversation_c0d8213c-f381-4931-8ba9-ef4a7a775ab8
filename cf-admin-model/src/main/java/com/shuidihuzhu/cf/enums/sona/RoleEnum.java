package com.shuidihuzhu.cf.enums.sona;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2018-09-29  16:22
 * 用户角色枚举
 * 枚举一定要按value顺序排列
 */
public enum RoleEnum {

    DEFAULT(0, ""),


    // 首次审核 头腰尾角色
    /**
     * 头部工单
     */
    FIRST_APPROVE_A(27, ""),

    /**
     * 腰部
     */
    FIRST_APPROVE_B(28, ""),

    /**
     * 尾部
     */
    FIRST_APPROVE_C(29, ""),

    /**
     * 可以举报工单传递的角色
     */
    REPORT_TRANSFER(30, "work-order:report-transfer"),

    /**
     * 【特殊处理】强制驳回材料 材料审核通过后也可以驳回
     */
    REFUSE_FORCE(35, "approve:refuse-force"),

    /**
     * ugc风控组长 可以查看全部工单
     */
    SENSITIVE_LEADER(36, "ugc:all-work-orders"),

    ;

    @Getter
    int value;
    @Getter
    String permission;

    RoleEnum(int v, String permission) {
        this.value = v;
        this.permission = permission;
    }

    public static RoleEnum parse(int v){
        for (RoleEnum e : RoleEnum.values()) {
            if (e.getValue() == v) {
                return e;
            }
        }
        throw new IllegalArgumentException("can't find value of RoleEnum");
    }
}
