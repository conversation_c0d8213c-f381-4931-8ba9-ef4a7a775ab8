package com.shuidihuzhu.cf.enums.record;

import com.shuidihuzhu.cf.enums.crowdfunding.UgcManageEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/9/1  4:13 下午
 */
public enum RecordBizTypeEnum {
    OPERATE_IMAGE(1,"操作图片"),

            ;

    @Getter
    private int code;

    @Getter
    private String desc;

    RecordBizTypeEnum(int code, String desc){
        this.code = code;
        this.desc = desc;
    }

    public static RecordBizTypeEnum parse(int code){
        for (RecordBizTypeEnum recordBizTypeEnum : RecordBizTypeEnum.values()){
            if(code == recordBizTypeEnum.getCode()){
                return recordBizTypeEnum;
            }
        }
        return null;
    }

}
