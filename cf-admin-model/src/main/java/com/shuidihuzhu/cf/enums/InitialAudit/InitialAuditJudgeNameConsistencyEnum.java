package com.shuidihuzhu.cf.enums.InitialAudit;

import com.google.common.base.Joiner;
import com.shuidihuzhu.cf.risk.model.enums.DiseaseStrategyResultEnum;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyResponse;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseStrategyResultInfo;
import com.shuidihuzhu.cf.vo.admin.initialAudit.DiseaseStrategyResult;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;

/**
 * <AUTHOR>
 */

@Getter
public enum InitialAuditJudgeNameConsistencyEnum {

    /**
     * 一致
     */
    OCR_JUDGE_NAME_SUC(0, "医疗材料中的姓名与患者姓名一致", false),
    TITLE_ARTICLE_JUDGE_NAME_SUC(1, "文章中的姓名与患者姓名一致", false),

    CONTENT_DISEASE_FIT(2, "文章中的疾病名称与医疗材料中的疾病一致", false),
    CONTENT_DISEASE_PART_FIT(3, "文章中的疾病名称与医疗材料中的疾病部分一致", false),

    MEDICAL_DISEASE_FIT(4, "医疗材料中的疾病名称与用户填写疾病一致", false),
    MEDICAL_DISEASE_PART_FIT(5, "医疗材料中的疾病名称与用户填写疾病部分一致", false),

    /**
     * 不一致
     */
    MANUAL_INPUT_DISEASE(40, "请手动录入医疗材料中的疾病后，再判断疾病是否可发", false),
    MANUAL_JUDGE_DISEASE(41, "请人工判断医疗材料中的疾病和患者所患疾病是否一致，若不一致请手动输入医疗中的疾病", false),
    CONTENT_DISEASE_CANT_FIT(42, "文章中无疾病名称或与医疗材料不一致，请人工判断", true),
    DISEASE_CANT_NORMAL(43, "患者所患疾病未被归一规则覆盖，无法判断是否可发，请按现有流程判断", false),


    OCR_JUDGE_NAME_FAIL(50, "医疗材料中无患者姓名或不一致，请人工判断", true),
    TITLE_ARTICLE_JUDGE_NAME_FAIL(51, "文章中无患者姓名或不一致，请人工判断", false),

    ORDER(99,"", false);
    private int code;
    private String message;
    private boolean reportError;

    InitialAuditJudgeNameConsistencyEnum(Integer code, String desc, boolean reportError) {
        this.code = code;
        this.message = desc;
        this.reportError = reportError;
    }




    public static String getByValue (int code) {
        InitialAuditJudgeNameConsistencyEnum[] values = InitialAuditJudgeNameConsistencyEnum.values();
        for (InitialAuditJudgeNameConsistencyEnum value : values) {
            if (value.getCode() == code) {
                return value.message;
            }
        }
        return ORDER.message;
    }


    public static void fillStrategyFitDescByUserWrite(DiseaseStrategyResult strategyResult,
                                                      DiseaseStrategyResponse response) {

        int strategyId = response.getDiseaseRaiseStrategyResult() != null ?
                response.getDiseaseRaiseStrategyResult().getResult() : -1;

        int medicalId = response.getDiseaseMaterialCoherenceStrategyResult() != null ?
                response.getDiseaseMaterialCoherenceStrategyResult().getResult() : -1;

        int contentId = response.getDiseaseContentCoherenceStrategyResult() != null ?
                response.getDiseaseContentCoherenceStrategyResult().getResult() : -1;


        if (strategyId == DiseaseStrategyResultEnum.UserWriteDiseaseStrategyEnum.WRITE_DISEASE_NULL.getCode()
            || strategyId == DiseaseStrategyResultEnum.UserWriteDiseaseStrategyEnum.WRITE_DISEASE_NOT_MERGE_RULE.getCode()) {
            strategyResult.setMedicalFitDesc(MANUAL_INPUT_DISEASE.getMessage());
            strategyResult.setMedicalFitStatus(DiseaseStrategyResult.FitStatus.OTHER.getCode());

            return;
        }

        if (strategyId == DiseaseStrategyResultEnum.UserWriteDiseaseStrategyEnum.ALL_DISEASE_CAN_NOT_RAISE.getCode()
                || strategyId == DiseaseStrategyResultEnum.UserWriteDiseaseStrategyEnum.SPECIAL_DISEASE_CAN_NOT_RAISE.getCode()) {
            strategyResult.setMedicalFitDesc(MANUAL_INPUT_DISEASE.getMessage());
            strategyResult.setMedicalFitStatus(DiseaseStrategyResult.FitStatus.OTHER.getCode());
            return;
        }

        if ( (strategyId == DiseaseStrategyResultEnum.UserWriteDiseaseStrategyEnum.SPECIAL_DISEASE_CAN_RAISE.getCode()
                || strategyId == DiseaseStrategyResultEnum.UserWriteDiseaseStrategyEnum.DISEASE_CAN_RAISE.getCode())
               && medicalId ==  DiseaseStrategyResultEnum.UserWriteDiseaseStrategyEnum.DISEASE_MATERIAL_NOT_SAME.getCode()) {
            strategyResult.setMedicalFitDesc(MANUAL_JUDGE_DISEASE.getMessage());
            strategyResult.setMedicalFitStatus(DiseaseStrategyResult.FitStatus.OTHER.getCode());
            return;
        }

        if (medicalId ==  DiseaseStrategyResultEnum.UserWriteDiseaseStrategyEnum.DISEASE_MATERIAL_SAME.getCode()) {
            strategyResult.setMedicalFitDesc(MEDICAL_DISEASE_FIT.getMessage());
            strategyResult.setMedicalFitStatus(DiseaseStrategyResult.FitStatus.FIT.getCode());
            strategyResult.setMedicalFit(getFitDesc(response.getDiseaseMaterialCoherenceStrategyResult()));
        }

        if (medicalId ==  DiseaseStrategyResultEnum.UserWriteDiseaseStrategyEnum.DISEASE_MATERIAL_PART_SAME.getCode()) {
            strategyResult.setMedicalFitDesc(MEDICAL_DISEASE_PART_FIT.getMessage());
            strategyResult.setMedicalFitStatus(DiseaseStrategyResult.FitStatus.PART_FIT.getCode());
            strategyResult.setMedicalFit(getFitDesc(response.getDiseaseMaterialCoherenceStrategyResult()));
        }

        if (contentId ==  DiseaseStrategyResultEnum.UserWriteDiseaseStrategyEnum.DISEASE_MATERIAL_CONTENT_NOT_SAME.getCode()) {
            strategyResult.setContentFitDesc(CONTENT_DISEASE_CANT_FIT.getMessage());
            strategyResult.setContentFitStatus(DiseaseStrategyResult.FitStatus.NO_FIT.getCode());
            strategyResult.setReportError(CONTENT_DISEASE_CANT_FIT.isReportError());
        }

        if (contentId ==  DiseaseStrategyResultEnum.UserWriteDiseaseStrategyEnum.DISEASE_MATERIAL_CONTENT_SAME.getCode()) {
            strategyResult.setContentFitDesc(CONTENT_DISEASE_FIT.getMessage());
            strategyResult.setContentFitStatus(DiseaseStrategyResult.FitStatus.FIT.getCode());
            strategyResult.setContentFit(getFitDesc(response.getDiseaseContentCoherenceStrategyResult()));
        }

        if (contentId ==  DiseaseStrategyResultEnum.UserWriteDiseaseStrategyEnum.DISEASE_MATERIAL_CONTENT_PART_SAME.getCode()) {
            strategyResult.setContentFitDesc(CONTENT_DISEASE_PART_FIT.getMessage());
            strategyResult.setContentFitStatus(DiseaseStrategyResult.FitStatus.PART_FIT.getCode());
            strategyResult.setContentFit(getFitDesc(response.getDiseaseContentCoherenceStrategyResult()));
        }
    }

    private  static String getFitDesc(DiseaseStrategyResultInfo resultInfo) {

        if (resultInfo == null || CollectionUtils.isEmpty(resultInfo.getMaterialsDiseaseNameList())) {
            return "";
        }

        return Joiner.on(",").join(resultInfo.getMaterialsDiseaseNameList());
    }


    public static void fillStrategyFitDescByManual(DiseaseStrategyResult strategyResult,
                                                   DiseaseStrategyResponse response) {

        int strategyId = response.getDiseaseRaiseStrategyResult() != null ?
                response.getDiseaseRaiseStrategyResult().getResult() : -1;

        int contentId = response.getDiseaseContentCoherenceStrategyResult() != null ?
                response.getDiseaseContentCoherenceStrategyResult().getResult() : -1;


        if (strategyId == DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.WRITE_DISEASE_NOT_MERGE_RULE.getCode()) {
            strategyResult.setMedicalFitDesc(DISEASE_CANT_NORMAL.getMessage());
            strategyResult.setMedicalFitStatus(DiseaseStrategyResult.FitStatus.OTHER.getCode());
            return;
        }

        if (strategyId == DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.SPECIAL_DISEASE_CAN_RAISE.getCode()
            || strategyId == DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.DISEASE_CAN_RAISE.getCode()) {

            if (contentId == DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.DISEASE_MATERIAL_NOT_SAME.getCode()
                || contentId == DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.DISEASE_CONTENT_NOT_SAME.getCode()) {
                strategyResult.setContentFitDesc(CONTENT_DISEASE_CANT_FIT.getMessage());
                strategyResult.setReportError(CONTENT_DISEASE_CANT_FIT.isReportError());
                strategyResult.setContentFitStatus(DiseaseStrategyResult.FitStatus.NO_FIT.getCode());
            }

//            if (contentId == DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.DISEASE_MATERIAL_SAME.getCode()) {
//                strategyResult.setMedicalFitDesc(CONTENT_DISEASE_FIT.getMessage());
//                strategyResult.setMedicalFitStatus(DiseaseStrategyResult.FitStatus.FIT.getCode());
//                strategyResult.setMedicalFit(getFitDesc(response.getDiseaseContentCoherenceStrategyResult()));
//            }

            if (contentId == DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.DISEASE_MATERIAL_SAME.getCode()
            || contentId == DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.DISEASE_MATERIAL_PART_SAME.getCode()
            || contentId == DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.DISEASE_CONTENT_SAME.getCode()
            || contentId == DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.DISEASE_CONTENT_PART_SAME.getCode()) {
                strategyResult.setContentFitDesc(CONTENT_DISEASE_PART_FIT.getMessage());
                strategyResult.setContentFitStatus(DiseaseStrategyResult.FitStatus.PART_FIT.getCode());
                strategyResult.setContentFit(getFitDesc(response.getDiseaseContentCoherenceStrategyResult()));
            }
        }

        if (strategyId == DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.ALL_DISEASE_CAN_NOT_RAISE.getCode()
                || strategyId == DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.SPECIAL_DISEASE_CAN_NOT_RAISE.getCode()) {

            if (contentId == DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.DISEASE_MATERIAL_NOT_SAME.getCode()
                || contentId == DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.DISEASE_CONTENT_NOT_SAME.getCode()) {
                strategyResult.setContentFitDesc(CONTENT_DISEASE_CANT_FIT.getMessage());
                strategyResult.setContentFitStatus(DiseaseStrategyResult.FitStatus.NO_FIT.getCode());
                strategyResult.setReportError(CONTENT_DISEASE_CANT_FIT.isReportError());
                return;
            }

            if (contentId == DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.DISEASE_MATERIAL_SAME.getCode()
                    || contentId == DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.DISEASE_MATERIAL_PART_SAME.getCode()
                    || contentId == DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.DISEASE_CONTENT_PART_SAME.getCode()) {
                strategyResult.setContentFitDesc(CONTENT_DISEASE_PART_FIT.getMessage());
                strategyResult.setContentFitStatus(DiseaseStrategyResult.FitStatus.PART_FIT.getCode());
                strategyResult.setContentFit(getFitDesc(response.getDiseaseContentCoherenceStrategyResult()));
                return;
            }

            if (contentId == DiseaseStrategyResultEnum.ManualWriteDiseaseStrategyEnum.DISEASE_CONTENT_SAME.getCode()) {
                strategyResult.setContentFitDesc(CONTENT_DISEASE_FIT.getMessage());
                strategyResult.setContentFitStatus(DiseaseStrategyResult.FitStatus.FIT.getCode());
                strategyResult.setContentFit(getFitDesc(response.getDiseaseContentCoherenceStrategyResult()));
                return;
            }
        }
    }


    public static void fillAmountReasonInfo(DiseaseStrategyResult strategyResult, DiseaseStrategyResponse response) {
        if (response.getDiseaseAmountStrategyResult() == null || response.getDiseaseAmountStrategyResult().getResult() == 0) {
            return;
        }
        strategyResult.setAmountReasonableResult(response.getDiseaseAmountStrategyResult().getResult());
        strategyResult.setAmountReasonableResultDesc(response.getDiseaseAmountStrategyResult().getResultDesc());
        strategyResult.setAmountReasonableResultHit(response.getDiseaseAmountStrategyResult().getResultHint());
    }
}
