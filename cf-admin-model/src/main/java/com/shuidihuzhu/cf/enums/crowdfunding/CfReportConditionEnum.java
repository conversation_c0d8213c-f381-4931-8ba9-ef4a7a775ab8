package com.shuidihuzhu.cf.enums.crowdfunding;

/**
 * Created by Ahrievil on 2017/9/4
 */
public enum CfReportConditionEnum {

    DEFAULT(0, "默认"),
    REPORT_COUNT_CONDITION(1, "【高风险案例-被举报大等于3次】"),
    AMOUNT_OVER_30W(2, "【高风险案例-实际筹款大于30万】"),
    FAKE_SHARE(3, "【高风险案例-疑似商业转发】")
    ;


    CfReportConditionEnum(int value, String word) {
        this.value = value;
        this.word = word;
    }
    private int value;
    private String word;

    public int getValue() {
        return value;
    }

    public String getWord() {
        return word;
    }

}
