package com.shuidihuzhu.cf.enums.admin.interfaces;


import com.shuidihuzhu.cf.enums.admin.errors.ErrorGroupEnum;

/**
 * Author: wuxinlong
 * Date: 16/10/21 11:34
 *
 * 通用错误接口
 *
 */
public interface ICommonError {

    /**
     * 获取所属组
     * @return
     */
    ErrorGroupEnum getGroupEnum();

    /**
     * 获取错误码
     * @return
     */
    int getCode();

    /**
     * 获取错误描述
     * @return
     */
    String getDescription();
}
