package com.shuidihuzhu.cf.enums.export;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/6/9 11:25 AM
 */
@Getter
public enum ReceivingGoodsDownloadField {

    ID(1, "id"),
    RECEIVING_NAME(2, "收货人姓名"),
    RECEIVING_PHONE(3, "收货人电话"),
    RECEIVING_ADDRESS(4, "收货人地址"),
    CERTIFICATE_NICKNAME(5, "收货人证书用昵称"),
    CREATE_TIME(6, "登记时间"),
    REGISTRATION_TYPE(7, "登记方式");

    private final int code;
    private final String desc;
    private static final Map<Integer, ReceivingGoodsDownloadField> enumMap = new HashMap();

    ReceivingGoodsDownloadField(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public static ReceivingGoodsDownloadField getByCode(int code) {
        return enumMap.get(code);
    }

}
