package com.shuidihuzhu.cf.enums.crowdfunding;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * <AUTHOR> Ahrievil
 */
public class AdminWorkOrderCaseConst {

    public enum CaseType {

        NO(0, ""),
        // notice  线下bd 发起的案例 不生成首次沟通工单
        FIRST_CONTACT(1, "首次沟通"),
        CASE_APPROVE(2, "案例审核"),
        ;

        private int code;
        private String word;

        private final static Map<Integer, CaseType> MAP = Maps.newHashMap();

        static {
            for (CaseType caseType : CaseType.values()) {
                MAP.put(caseType.getCode(), caseType);
            }
        }

        CaseType(int code, String word) {
            this.code = code;
            this.word = word;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getWord() {
            return word;
        }

        public void setWord(String word) {
            this.word = word;
        }

        public static CaseType getByCode(Integer code) {
            if (code == null) {
                return CaseType.NO;
            }
            return MAP.get(code);
        }
    }

    public enum Status{

        CREATE(0, "创建"),
        PROCESSING(1, "处理中"),
        SHUT_DOWN(2, "已关闭"),
        COMPLETE(3, "完成"),
        SYSTEM_COMPLETE(5, "系统完成"),
        ;

        private int code;
        private String word;

        private final static Map<Integer, Status> STATUS_MAP = Maps.newHashMap();

        static {
            for (Status status : Status.values()) {
                STATUS_MAP.put(status.getCode(), status);
            }

        }

        Status(int code, String word) {
            this.code = code;
            this.word = word;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getWord() {
            return word;
        }

        public void setWord(String word) {
            this.word = word;
        }

        public static Status getByCode(Integer code) {
            if (code == null) {
                return Status.CREATE;
            }
            return STATUS_MAP.get(code);
        }
    }

    public enum CallStatus {

        DEFAULT(0, "未打电话"),
        CALL_OUT_SUCCESS(1, "呼通"),
        CALL_OUT_FAIL(2, "未呼通")
        ;

        private int code;
        private String word;

        private final static Map<Integer, CallStatus> CALL_STATUS_MAP = Maps.newHashMap();

        static {
            for (CallStatus callStatus : CallStatus.values()) {
                CALL_STATUS_MAP.put(callStatus.getCode(), callStatus);
            }
        }

        CallStatus(int code, String word) {
            this.code = code;
            this.word = word;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getWord() {
            return word;
        }

        public void setWord(String word) {
            this.word = word;
        }

        public static CallStatus getByCode(Integer code) {
            if (code == null) {
                return DEFAULT;
            }
            return CALL_STATUS_MAP.get(code);
        }
    }


    public enum ApproveResult {

        DEFAULT(0, "默认"),
        DEFER_APPROVE(1, "延后等待审核"),
        DEFER_CONTACT(2, "延后电话联系"),
        NEVER_PROCESSING(3, "不再处理"),
        CASE_APPROVE_PASS(4, "案例审核通过"),
        CASE_APPROVE_REFUSE_CONTENT(5, "驳回图文"),
        CASE_APPROVE_REFUSE_USER_INFO(6, "驳回用户信息"),
        CASE_APPROVE_REFUSE_PAYEE_INFO(7, "驳回收款人信息"),
        CASE_APPROVE_REFUSE_TREATMENT_INFO(8, "驳回诊断信息"),
        ;

        private int code;
        private String word;

        private final static Map<Integer, ApproveResult> APPROVE_RESULT_MAP = Maps.newHashMap();

        static {
            for (ApproveResult approveResult : ApproveResult.values()) {
                APPROVE_RESULT_MAP.put(approveResult.getCode(), approveResult);
            }
        }

        ApproveResult(int code, String word) {
            this.code = code;
            this.word = word;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getWord() {
            return word;
        }

        public void setWord(String word) {
            this.word = word;
        }

        public static ApproveResult getByCode(Integer code) {
            if (code == null) {
                return DEFAULT;
            }
            return APPROVE_RESULT_MAP.get(code);
        }
    }
}
