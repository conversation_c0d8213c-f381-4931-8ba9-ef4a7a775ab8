package com.shuidihuzhu.cf.enums.crowdfunding;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/10.
 */
public class AdminWorkOrderReportConst {
    public enum ReportType {
        DEFAULT(0, "默认"),
        CASE_REPORT(1, "案例举报"),;

        private int code;
        private String value;
        private static Map<Integer, AdminWorkOrderReportConst.ReportType> map = Maps.newHashMap();
        private static Set<AdminWorkOrderReportConst.ReportType> VALID_MAP;

        ReportType(int code, String value) {
            this.code = code;
            this.value = value;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        static {
            for (ReportType type : ReportType.values()) {
                map.put(type.getCode(), type);
            }
            VALID_MAP = ImmutableSet.of(DEFAULT, CASE_REPORT);
        }

        public static ReportType getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            ReportType type = map.get(code);
            return type != null ? type : ReportType.DEFAULT;
        }
    }

    public enum CaseRisk{
        DEFAULT(0,"默认"),
        HIGH_RISK(1,"高危"),
        ;
        private int code;
        private String value;
        private static Map<Integer, AdminWorkOrderReportConst.CaseRisk> map = Maps.newHashMap();
        private static Set<AdminWorkOrderReportConst.CaseRisk> VALID_MAP;

        CaseRisk(int code, String value) {
            this.code = code;
            this.value = value;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
        static {
            for (CaseRisk caseRisk : CaseRisk.values()) {
                map.put(caseRisk.getCode(), caseRisk);
            }
            VALID_MAP = ImmutableSet.of(DEFAULT, HIGH_RISK);
        }

        public static CaseRisk getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            CaseRisk type = map.get(code);
            return type != null ? type : CaseRisk.DEFAULT;
        }
    }

    public enum DealResult{
        DEFAULT(0,"默认"),
        REPORT_FOLLOW(1,"举报跟进中"),
        DEAL_COMPLETE(2,"处理完成"),
        NO_DEAL(3,"不需要处理"),
        LOST_CONTACT(4,"已失联"),
        ;
        private int code;
        private String value;
        private static Map<Integer, AdminWorkOrderReportConst.DealResult> map = Maps.newHashMap();
        private static Set<AdminWorkOrderReportConst.DealResult> VALID_MAP;

        public static final Set<Integer> DONE_SET = ImmutableSet.of(
                REPORT_FOLLOW.code,
                DEAL_COMPLETE.code,
                NO_DEAL.code,
                LOST_CONTACT.code
        );

        /**
         * 仅 '不需要处理' 才是安全的
         */
        public static final List<Integer> UN_SAFE_CODE_LIST = ImmutableList.of(
                DEFAULT.code,
                REPORT_FOLLOW.code,
                DEAL_COMPLETE.code,
                LOST_CONTACT.code
        );

        DealResult(int code, String value) {
            this.code = code;
            this.value = value;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
        static {
            for (DealResult dealResult : DealResult.values()) {
                map.put(dealResult.getCode(), dealResult);
            }
            VALID_MAP = ImmutableSet.of(DEFAULT, DEAL_COMPLETE,NO_DEAL);
        }

        public static DealResult getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            DealResult type = map.get(code);
            return type != null ? type : DealResult.DEFAULT;
        }
    }

    public enum FollowType{
        DEFAULT(0,"默认"),
        SEND_ADD_CREDIT(1,"发送增信"),
        REJECT_ADD_CREDIT(2,"驳回增信"),
        PASS_ADD_CREDIT(3,"审核通过增信"),
        ADD_FOLLOW_COMMENT(4,"添加跟进备注"),
        ;
        private int code;
        private String value;
        private static Map<Integer, AdminWorkOrderReportConst.FollowType> map = Maps.newHashMap();
        private static Set<AdminWorkOrderReportConst.FollowType> VALID_MAP;

        FollowType(int code, String value) {
            this.code = code;
            this.value = value;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
        static {
            for (FollowType followType : FollowType.values()) {
                map.put(followType.getCode(), followType);
            }
            VALID_MAP = ImmutableSet.of(DEFAULT, SEND_ADD_CREDIT,REJECT_ADD_CREDIT,PASS_ADD_CREDIT,ADD_FOLLOW_COMMENT);
        }

        public static FollowType getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            FollowType type = map.get(code);
            return type != null ? type : FollowType.DEFAULT;
        }
    }

}
