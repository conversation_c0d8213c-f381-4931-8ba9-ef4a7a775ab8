package com.shuidihuzhu.cf.enums.InitialAudit;


import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: wangpeng
 * @Date: 2021/5/25 19:29
 * @Description:
 */
public enum InitialAuditNoSmartReason {
    SUCCESS(0, "智能审核流程"),
    SWITCH_OFF(1, "智能审核流程未开启"),
    XIAN_XIA_BD(2, "线下顾问引导发起案例不进入智能审核"),
    REPEAT_CASE(3, "重复发起案例"),
    SPECIAL_PRE_POSE(4, "工牌报备案例"),
    LIVING_ALLOWANCE_AND_POVERTY(5, "低保/贫困信息选择了是"),
    AMOUNT_NOT_REASONABLE(6, "目标金额不合理或需要选择治疗方案"),
    BIRTH_CARD_CASE(7, "出生证发起案例"),
    DISEASE_NAME_EXIT_SYSTEM(8, "患者所患疾病不在系统范围内"),
    ENTER_PEOPLE_AUDIT(9, "审核同学手动标记进入"),
    MATERIAL_DISEASE_NAME_EXIT_SYSTEM(10, "医疗材料中的疾病不在疾病库中"),
    MATERIAL_DISEASE_NAME_SPECIAL(11, "医疗材料中的疾病包含特殊可发"),
    CROWDFUNDING_INFO(12, "案例数据为空"),
    EAGLE(13, "鹰眼分流"),
    HIGH_RISK_WORK_ORDER(14, "可以生成高风险工单"),
    LIVING_ALLOWANCE_AND_POVERTY_EMPTY(15, "低保/贫困信息查询为空"),
    DISEASE_NAME_EMPTY(16, "疾病名称为空"),
    LAST_NO_SMART_WORK_ORDER(17, "上次未走智能审核"),
    UN_LIMIT_INITIAL_AUDIT(18, "命中“限制预审通过”动作"),
    TARGET_AMOUNT_WORK_ORDER(19, "目标金额审核工单不通过"),
    AUTHENTICITY_CASE(20, "真实性案例"),
    ;


    private final static Map<Integer, InitialAuditNoSmartReason> map = Maps.newHashMap();

    static {
        map.putAll(Arrays.stream(InitialAuditNoSmartReason.values()).collect(Collectors.toMap(InitialAuditNoSmartReason::getType, Function.identity())));
    }

    @Getter
    private final int type;

    @Getter
    private final String msg;


    InitialAuditNoSmartReason(int type, String msg) {
        this.type = type;
        this.msg = msg;
    }


    public static InitialAuditNoSmartReason getInitialAuditNoSmartReasonFromType(int type){
        return map.get(type);
    }

    public static List<Integer> getList(){
        return new ArrayList<>(map.keySet());
    }
}
