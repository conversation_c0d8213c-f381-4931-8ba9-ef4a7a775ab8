package com.shuidihuzhu.cf.enums.message;

/**
 * @package: com.shuidihuzhu.cf.enums.message
 * @Author: liujiaw<PERSON>
 * @Date: 2019-03-13  20:53
 */
public enum WxPushConfigStatusEnum {
    /**
     * 初始状态
     */
    INIT(0, "初始状态"),
    NEED_BUILD(1, "待构建"),
    /**
     * 构建中终止会变为4
     * 成功请求大数据，开始推送数据后
     */
    BUILD_ING(2, "构建中，1阶段"),
    /**
     * 当大数据推送完数据后
     * 这里的状态码没有顺序后移，不是连续的。
     * 原因：为了防止使用明文状态判断导致逻辑出错
     */
    BUILD_ING_2(10, "构建中,2阶段"),
    /**
     * 当本地数据全部接收到后
     */
    BUILD_SUCCESS(3, "构建完成"),
    /**
     * 删除不可终止
     */
    WATI_DELETE(4, "待删除"),
    /**
     * 结束后变为0
     */

    DELETE_ING(5, "删除中"),
    WAIT_PUSH(6, "待推送"),
    /**
     *  终止后变为3
     */
    PUSH_ING(7, "推送中"),
    PUSH_SUCCESS(8, "推送完成"),
    /**
     * 任意执行中状态都可强制停止，程序在终止后会自动复位
     */
    SHUTDOWN(9, "强制停止"),
    /**
     * 发送构建请求失败
     */
    BUILD_FAIL_1(-1,"发送构建请求失败"),

    /**
     * 大数据构建失败
     */
    BUILD_FAIL_2(-2,"大数据构建失败");
    private int code;
    private String desc;

    WxPushConfigStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static WxPushConfigStatusEnum getByCode(int value) {
        for (WxPushConfigStatusEnum wxPushConfigStatusEnum : WxPushConfigStatusEnum.values()) {
            if (wxPushConfigStatusEnum.getCode() == value) {
                return wxPushConfigStatusEnum;
            }
        }
        return INIT;
    }


}
