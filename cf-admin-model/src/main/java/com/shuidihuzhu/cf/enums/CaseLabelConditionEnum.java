package com.shuidihuzhu.cf.enums;

import com.shuidihuzhu.cf.enums.maskcode.MaskTypeEnum;

/**
 * @Description:
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/8/9 8:16 PM
 */
public enum CaseLabelConditionEnum {

    TARGET_AMOUNT_CONDITION(1, "目标金额", "targetAmount"),
    NORM_DISEASE_CONDITION(2, "归一后疾病", "diseaseNormNames"),
    PATIENT_AGE_CONDITION(3, "患者年龄", "age"),
    CASE_TYPE_CONDITION(4, "案例类型", "caseTypes"),
    PATIENT_IDENTITY_CONDITION(5, "患者身份", "patientIdentity"),
    CASE_PROVINCE_CONDITION(6, "案例所属省份", "province"),
    USER_RELATION_TYPE(7, "发起人与患者关系", "userRelationType"),
    REPEAT_LABEL(8, "是否二发", "repeatLabel"),
    MEDICAL_CITY(9, "医疗材料上的医院所在城市", "medicalCity"),
    PREDICT_DONATE_COUNT(10, "预测捐单", "predictDonateCount"),
    MAJOR_CASE_TAG(11, "是否重点案例规则", "majorCaseTag"),
    PATIENT_PROFESSION(12, "患者职业", "patientProfession"),
    ;

    private int code;
    private String description;
    private String fieldName;

    CaseLabelConditionEnum(int code, String description, String fieldName) {
        this.code = code;
        this.description = description;
        this.fieldName = fieldName;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    private String getFieldName() {
        return fieldName;
    }

    public static String getDescription(int code) {
        for (CaseLabelConditionEnum value : CaseLabelConditionEnum.values()) {
            if (value.getCode() == code) {
                return value.getDescription();
            }
        }
        return  "";
    }

    public static String getFieldName(String description) {
        for (CaseLabelConditionEnum value : CaseLabelConditionEnum.values()) {
            if (value.getDescription().equals(description)) {
                return value.getFieldName();
            }
        }
        return  "";
    }

}
