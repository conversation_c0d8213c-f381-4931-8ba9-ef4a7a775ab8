package com.shuidihuzhu.cf.enums.message;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * Created by lixuan on 2017/3/15.
 */
public enum MessageBizTypeEnum {

    BIZ_TYPE_DEFAULT(0, "默认"),
    BIZ_TYPE_HUZHU(1, "互助"),
    BIZ_TYPE_AIXINCHOU(2, "爱心筹"),
    BIZ_TYPE_DIDABAO(3, "滴答保"),
    BIZ_TYPE_SHUIDIBAO(4, "水滴保"),
    BIZ_TYPE_WX_APP_DEFAULT(10, "微信小程序默认"),
    BIZ_TYPE_WX_APP_HUZHU(11, "微信小程序互助"),
    BIZ_TYPE_WX_APP_AIXINCHOU(21, "微信小程序爱心筹"),
    BIZ_TYPE_WX_APP_DIDABAO(31, "微信小程序滴答保");

    private int code;
    private String name;

    MessageBizTypeEnum(int code, String name){
        this.code = code;
        this.name = name;
    }

    public static Map<Integer, String> map = Maps.newHashMap();

    static {
        for (MessageBizTypeEnum messageBizTypeEnum : MessageBizTypeEnum.values()) {
            map.put(messageBizTypeEnum.getCode(), messageBizTypeEnum.getName());
        }
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getName(int value) {
        for (MessageBizTypeEnum messageBizTypeEnum : MessageBizTypeEnum.values()) {
            if (messageBizTypeEnum.getCode() == value) {
                return messageBizTypeEnum.getName();
            }
        }
        return MessageBizTypeEnum.BIZ_TYPE_DEFAULT.getName();
    }
}
