package com.shuidihuzhu.cf.enums.maskcode;

public enum MaskCodePageEnum {
    CHU_SHEN_DETAIL_PAGE(1, "初审详情页"),
    MATERIAL_AUDIT_DETAIL_PAGE(2, "材料审核详情页"),
    OPERATION_COMMUNICATION_DETAIL_PAGE(3, "运营沟通详情页"),
    REPORT_HANDLE_DETAIL_PAGE(4, "举报处理详情页"),
    HOSPITAL_CHECK(5, "医院核实"),
    WAIHU_DETAIL_REGISTRY(6, "外呼平台详情登记-发送短信"),
    FIRST_APPROVE_WAIHU_DETAIL_REGISTRY(7, "前置外呼平台详情登记-发送短信"),
    SERVICE_MANAGEMENT_DETAIL(8, "服务平台管理详情-发送短信"),
    INITIAL_VERIFY_WORK_ORDER(9, "初审-审核工单详情页"),
    INITIAL_RETURN_VISIT_WORK_ORDER(10, "初审-回访工单详情页"),
    INITIAL_REJECT_WORK_ORDER(11, "初审-驳回工单详情页"),
    SERVICE_MANAGEMENT_DETAIL_PHONE(12, "服务平台管理详情-手机"),
    SERVICE_MANAGEMENT_DETAIL_NEW_PHONE(13, "服务平台管理详情-新手机"),
    SERVICE_MANAGEMENT_DETAIL_WECHAR(14, "服务平台管理详情-微信"),
    SERVICE_GROUP_MEMBER_SERVICE_LIST(15, "服务平台组员-服务列表"),
    VOLUNTEER_MANAGEMENT_CASE_RAISED_DETAIL(16, "筹款顾问管理-发起案例详情"),
    SEA_OFFLINE_CLEW_POOL(17, "SEA后台-线下线索池"),
    WAIHU_GROUP_LEADER_CALL_LIST(18, "外呼工作台组长-呼叫列表"),
    WAIHU_GROUP_LEADER_PENDING_AREA(19, "外呼工作台组长-待呼区"),
    WAIHU_GROUP_LEADER_HANDLED_LIST(20, "外呼工作台组长-已处理列表"),
    WAIHU_GROUP_LEADER_UNASSIGNED_CALL_LIST(21, "外呼工作台组长-待分配呼叫列表"),

    CASE_DETAIL_PAGE(22, "案例详情页"),
    DIAN_HUA_SHEN_HE_DETAIL_PAGE(23, "初审电话审核详情页"),
    USER_MANAGE_DETAIL(24, "用户管理详情页"),

    SERVE_WORKSTATION_GROUP_LIST(25, "服务工作台组长-服务列表"),
    SERVE_WORKSTATION_GROUP_FINISHED_CASE(26, "服务工作台组长-已结束案例"),
    INITIAL_HIGH_RISK_WORK_ORDER(27, "高风险工单-工单详情"),
    QIANZHI_BAOBEI_GUANLI(28, "线下管理-前置报备管理"),
    WH_MEMBER_DETAIL_PAGE(29, "外呼平台-组员(组长)-详情页"),
    ACTIVITY_ACCOUNT_PAGE(30, "资金管理-活动账户"),


    QC_DETAIL_PAGE(31, "质检详情页"),
    QC_SEARCH_LIST(32, "质检搜索列表"),
    QC_TASK_LIST(33, "质检工作台列表"),

    FIRST_AUDIT_PAGE(34,"初筛详情页"),
    SUPPLEMENT_AUDIT_PAGE(35,"补采详情页")
    ;

    private int code;
    private String pageName;

    MaskCodePageEnum(int code, String pageName) {
        this.code = code;
        this.pageName = pageName;
    }

    public String getPageName() {
        return this.pageName;
    }
}
