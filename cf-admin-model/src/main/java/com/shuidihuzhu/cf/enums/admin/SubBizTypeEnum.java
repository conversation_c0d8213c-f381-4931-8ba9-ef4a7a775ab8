package com.shuidihuzhu.cf.enums.admin;

import java.util.Map;

import com.google.common.collect.Maps;

/**
 * Created by ahrievil on 2017/3/7.
 */
public enum SubBizTypeEnum {

    SUB_BIZ_TYPE_DEFAULT(1,"默认"),
    SUB_BIZ_TYPE_HZ(10,"互助默认"),
    SUB_BIZ_TYPE_HZ_USUSMS(11,"互助任务--未关注>>转化为关注"),
    SUB_BIZ_TYPE_HZ_UJUWC(12,"互助任务--已关注未加入>>转化为加入"),
    SUB_BIZ_TYPE_CF(20,"爱心筹默认"),
    SUB_BIZ_TYPE_U_VERIFY_CODE(101,"验证码"),
    SUB_BIZ_TYPE_U_CHANGEMOBILE(102,"改手机号"),
    SUB_BIZ_TYPE_U_DYM(103,"领取保障"),
    SUB_BIZ_TYPE_U_DRAW(104,"提现"),
    SUB_BIZ_TYPE_U_URL(105,"url"),
    SUB_BIZ_TYPE_U_AUDIT(106,"审核"),
    SUB_BIZ_TYPE_U_COUPON(107,"优惠券"),
    SUB_BIZ_TYPE_U_GIFT(108,"礼品"),
    SUB_BIZ_TYPE_U_CHARGE_AWARD(109,"充值奖励"),
    SUB_BIZ_TYPE_U_INVITE_AWARD(110,"邀请奖励"),
    SUB_BIZ_TYPE_U_COMMENT(111,"评论"),
    SUB_BIZ_TYPE_U_TREND(112,"动态"),
    SUB_BIZ_TYPE_U_PAYORDER(113,"支付订单"),
    SUB_BIZ_TYPE_U_INVITE(114,"邀请"),
    SUB_BIZ_TYPE_U_JOIN(115,"加入"),
    SUB_BIZ_TYPE_U_WX_MSG(116,"公众号留言"),
    SUB_BIZ_TYPE_U_WX_MENU(117,"公众号菜单"),
    SUB_BIZ_TYPE_U_JUNTAN(118,"均摊业务"),
    SUB_BIZ_TYPE_U_ZHUSHOU(119,"健康小助手"),
    SUB_BIZ_TYPE_U_CF_FOR_HELP(120,"爱心筹求助业务"),
    SUB_BIZ_TYPE_U_CF_NOTICE(121,"爱心筹用户通知"),
    SUB_BIZ_TYPE_U_ORDER_PROBLEM(122,"订单问题"),
    SUB_BIZ_TYPE_U_GROUP(123,"圈子用户通知"),
    SUB_BIZ_TYPE_U_TRANS_PLAN(124,""),
    SUB_BIZ_TYPE_U_IMPROVE(125,""),
    SUB_BIZ_TYPE_U_CF_SUBMIT_BASEINFO(126,"爱心筹提交图文"),
    SUB_BIZ_TYPE_U_CF_SHARE(127,"爱心筹催分享"),
    SUB_BIZ_TYPE_U_CF_PASS(128,"爱心筹审核通过"),
    SUB_BIZ_TYPE_U_CF_REJECT(129,"爱心筹审核拒绝"),
    SUB_BIZ_TYPE_U_CF_REPLENISH(130,"爱心筹补齐资料"),
    SUB_BIZ_TYPE_U_CF_RECOMMEND_CHEATS(131,"爱心筹推荐秘籍"),
    SUB_BIZ_TYPE_U_SD_INSUFFICIENT_BALANCE(132,"水滴余额不足提醒"),
    SUB_BIZ_TYPE_U_CF_REFUND_SUCCESS(133,"爱心筹退款审核通过"),
    SUB_BIZ_TYPE_U_CF_NOT_JOIN_SD(134,"加入爱心筹没有加入水滴"),
    SUB_BIZ_TYPE_U_CF_DRAW_APPLY_SUCCESS(135,"爱心筹提现审核通过"),
    SUB_BIZ_TYPE_U_CLICK_IN(136,"打卡"),
    SUB_BIZ_TYPE_U_CF_INSURANCE(137,"爱心筹保险"),
    SUB_BIZ_TYPE_U_SD_INSURANCE(138,"互助保险"),
    SUB_BIZ_TYPE_U_SD_TRANS_PLAN(139,"互助转计划"),
    SUB_BIZ_TYPE_U_SD_ENSURE_EFFECT(140,"保障生效"),
    SUB_BIZ_TYPE_U_CF_VERIFICATION_TRANSMIT(141,"爱心筹证实转发"),
    SUB_BIZ_TYPE_U_SD_JOIN_FOR_FALIMY(142,"水滴家庭加入"),
    SUB_BIZ_TYPE_U_CF_DRAW_SUCCESS(143,"爱心筹提现成功"),
    SUB_BIZ_TYPE_U_CLOCK_START(144,"打卡开始"),
    SUB_BIZ_TYPE_U_CLOCK_NOTIFY(145,"提前提醒打卡"),
    SUB_BIZ_TYPE_U_CLOCK_SUCCESS(146,"打卡成功"),
    SUB_BIZ_TYPE_U_CLOCK_FAIL(147,"打卡失败"),
    SUB_BIZ_TYPE_U_CLOCK_RECALL(148,"晚上提醒加入下一期"),
    SUB_BIZ_TYPE_U_CLOCK_NEW_USER(149,"提示新关注的用户加入打卡"),
    SUB_BIZ_TYPE_U_CLOCK_TRANSFER_SUCCESS(150,"奖励到账通知"),
    SUB_BIZ_TYPE_U_CLOCK_TOP100_TRANSFER_SUCCESS(151,"top100奖励到账"),
    SUB_BIZ_TYPE_U_CLOCK_TOP100(152,"top100榜单通知"),
    SUB_BIZ_TYPE_U_CHUNYU_REPLY(153,"春雨医生回调"),
    SUB_BIZ_TYPE_M_ACTIVITY(201,"活动"),
    SUB_BIZ_TYPE_W_REPORT(301,"举报"),
    SUB_BIZ_TYPE_W_AUDIT(302,"审核"),
    SUB_BIZ_TYPE_U_HZ_THIRD_TRACK(10000, "第三方链接跟踪"),
    ;
	
	private static Map<Integer, SubBizTypeEnum> enumMap = Maps
			.newHashMapWithExpectedSize(SubBizTypeEnum.values().length);

	static {
		for (SubBizTypeEnum tmpEnum : SubBizTypeEnum.values()) {
			enumMap.put(tmpEnum.getKey(), tmpEnum);
		}
	}

	public static String getWords(int key) {
		SubBizTypeEnum subBizTypeEnum = enumMap.get(key);
		if (subBizTypeEnum != null) {
			return subBizTypeEnum.getValue();
		}
		return "";
	}

	private Integer key;
	private String value;

	SubBizTypeEnum(Integer key, String value) {
		this.key = key;
		this.value = value;
	}

	public Integer getKey() {
		return key;
	}

	public String getValue() {
		return value;
	}
}
