package com.shuidihuzhu.cf.enums.InitialAudit;

import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: wangpeng
 * @Date: 2021/8/12 14:21
 * @Description:
 */
public enum InitialAuditRiskWorkOrderReason {
    CREDIT_RISK(1, "增信信息为高风险"),
    MISTAKE_CASE(2, "事故案例"),
    REPEAT_RISK(3, "重复案例包含预审高风险案例"),
    LAST_IS_RISK(4, "上次生成的工单为高风险"),
    RISK_SWITCH(5, "高风险工单开关打开"),
    BLACK_LIST(6, "案例的患者为黑名单用户，且限制动作为：进入高风险库"),
    HUMAN_UPGRADE(7, "人工升级"),
    ;

    private final static Map<Integer, InitialAuditRiskWorkOrderReason> map = Maps.newHashMap();

    static {
        map.putAll(Arrays.stream(InitialAuditRiskWorkOrderReason.values()).collect(Collectors.toMap(InitialAuditRiskWorkOrderReason::getType, Function.identity())));
    }

    @Getter
    private final int type;

    @Getter
    private final String msg;


    InitialAuditRiskWorkOrderReason(int type, String msg) {
        this.type = type;
        this.msg = msg;
    }


    public static InitialAuditRiskWorkOrderReason getInitialAuditNoSmartReasonFromType(int type){
        return map.get(type);
    }

    public static List<Integer> getList(){
        return new ArrayList<>(map.keySet());
    }
}
