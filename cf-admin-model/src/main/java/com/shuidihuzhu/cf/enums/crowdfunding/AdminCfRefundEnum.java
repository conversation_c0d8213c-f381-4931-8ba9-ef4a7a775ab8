package com.shuidihuzhu.cf.enums.crowdfunding;

import java.util.HashMap;
import java.util.Map;

import com.google.common.collect.Maps;

public class AdminCfRefundEnum {

	public enum PayType {
		WX_WAP(2), WX_MP(3);

		private int code;

		private PayType(int code) {
			this.code = code;
		}

		public int getCode() {
			return code;
		}
	}

	public enum RefundStatus {
		UNHANDLE(0), HANDLING(1), HANDLE_SUCCESS(2), HANDLE_FAILED(3), MANUAL_SUCCESS(4);

		private static Map<Integer, RefundStatus> enumMap = new HashMap<Integer, RefundStatus>();

		static {
			for (RefundStatus tmpEnum : RefundStatus.values()) {
				enumMap.put(tmpEnum.getCode(), tmpEnum);
			}
		}

		public static RefundStatus fromCode(int code) {
			return enumMap.get(code);
		}

		private int code;

		private RefundStatus(int code) {
			this.code = code;
		}

		public int getCode() {
			return code;
		}

	}

	public enum ThirdType {
		new_pay(1);

		private int code;

		private ThirdType(int code) {
			this.code = code;
		}

		public int getCode() {
			return code;
		}
	}

	public enum ApplyStatus {
		UNSUBMIT(0), SUBMIT_APPROVE_PENDING(1), APPROVE_SUCCESS(2), APPROVE_REJECT(3);

		private int code;

		private ApplyStatus(int code) {
			this.code = code;
		}

		public int getCode() {
			return code;
		}

	}

	public enum RefundType {
		ALL(0), PART(1);

		private int code;

		private RefundType(int code) {
			this.code = code;
		}

		public int getCode() {
			return code;
		}
	}

	public enum OperationType {
		AUTOMATIC(0), MANUAL(1);

		private static Map<Integer, OperationType> enumMap = Maps.newHashMap();

		static {
			for (OperationType tmpEnum : OperationType.values()) {
				enumMap.put(tmpEnum.getCode(), tmpEnum);
			}
		}

		public static OperationType fromCode(int code) {
			return enumMap.get(code);
		}

		private int code;

		private OperationType(int code) {
			this.code = code;
		}

		public int getCode() {
			return code;
		}
	}

	public enum BizType {
		SERIOUS_ILLNESS(0), DREAM(1);

		private int code;

		private BizType(int code) {
			this.code = code;
		}

		public int getCode() {
			return code;
		}
	}

}
