package com.shuidihuzhu.cf.enums.approve;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@ApiModel("OCR识别报错位置枚举")
public enum CaseInfoOCRLocationEnum {


    /**
     *
     */
    // 以下为材料审核
    @ApiModelProperty("患者姓名")
    PATIENT_NAME(1, "患者姓名"),

    @ApiModelProperty("患者证件号码")
    PATIENT_ONLY_ID_CARD(2, "患者证件号码"),

    @ApiModelProperty("手持身份证照片")
    PATIENT_ID_CARD(3, "手持身份证照片"),
    ;

    @Getter
    private final String msg;
    @Getter
    private final int code;

    CaseInfoOCRLocationEnum(int code, String msg){
        this.msg = msg;
        this.code = code;
    }

    public String getShowMsg(){
        return "患者信息中" + msg;
    }

}
