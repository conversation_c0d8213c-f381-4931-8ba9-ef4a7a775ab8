package com.shuidihuzhu.cf.enums.crowdfunding;

import lombok.Getter;

/**
 * <AUTHOR>
 * @time 2019/12/13 下午5:13
 * @desc 与患者关系
 */
public enum CfCommunicaterRelationEnum {
    DEFAULT(0, "默认"),
    QUESTIONER(1, "质疑人"),
    QUESTIONER_OTHER(2, "质疑人-其他")
    ;
    @Getter
    private int key;

    @Getter
    private String value;

    CfCommunicaterRelationEnum(int key, String value){
        this.key = key;
        this.value = value;
    }

    public static CfCommunicaterRelationEnum parse(int key){
        for (CfCommunicaterRelationEnum cfCommunicaterRelationEnum : CfCommunicaterRelationEnum.values()){
            if(key == cfCommunicaterRelationEnum.getKey()){
                return cfCommunicaterRelationEnum;
            }
        }
        return null;
    }
}
