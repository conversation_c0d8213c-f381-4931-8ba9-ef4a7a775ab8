package com.shuidihuzhu.cf.enums.crowdfunding;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.enums.BackgroundLogEnum;

import java.util.List;

/**
 * Created by ahrievil on 2017/5/27.
 */
public enum CfDealWithStatusEnum {

    DEFAULT(0, "未处理"),
    DEFERRED_APPROVE(1, "延迟审核处理"),
    DISREGARD(2, "该案例不再处理"),
    DEFERRED_CONTACT(3, "延迟电话联系")
    ;
    CfDealWithStatusEnum (int value, String message) {
        this.value = value;
        this.message = message;
    }

    private int value;
    private String message;

    public int getValue() {
        return this.value;
    }

    public String getMessage () {
        return this.message;
    }

    public static CfDealWithStatusEnum getByValue (int code) {
        CfDealWithStatusEnum[] values = CfDealWithStatusEnum.values();
        for (CfDealWithStatusEnum value : values) {
            if (value.getValue() == code) {
                return value;
            }
        }
        return DEFAULT;
    }
    public BackgroundLogEnum getBackLogEnum() {
        List<CfDealWithStatusEnum> cfDealWithStatusEnums = Lists.newArrayList(CfDealWithStatusEnum.values());
        if (!cfDealWithStatusEnums.contains(this.value)) {
            switch (this) {
                case DEFERRED_APPROVE:
                    return BackgroundLogEnum.DEFERRED_PROCESSING;
                case DISREGARD:
                    return BackgroundLogEnum.NO_MORE_PROCESSING;
                case DEFERRED_CONTACT:
                    return BackgroundLogEnum.DEFERRED_CONTACT;
                default:
                    return BackgroundLogEnum.REMARK;
            }
        }
        return BackgroundLogEnum.REMARK;
    }

    public CfOperationRecordEnum getOperationRecordEnum() {
        List<CfDealWithStatusEnum> cfDealWithStatusEnums = Lists.newArrayList(CfDealWithStatusEnum.values());
        if (!cfDealWithStatusEnums.contains(this.value)) {
            switch (this) {
                case DEFERRED_APPROVE:
                    return CfOperationRecordEnum.DEFERRED_PROCESSING;
                case DISREGARD:
                    return CfOperationRecordEnum.NO_MORE_PROCESSING;
                case DEFERRED_CONTACT:
                    return CfOperationRecordEnum.DEFERRED_CONTACT;
                default:
                    return CfOperationRecordEnum.REMARK;
            }
        }
        return CfOperationRecordEnum.REMARK;
    }

    public CrowdfundingOperationEnum getOperationEnum() {
        List<CfDealWithStatusEnum> cfDealWithStatusEnums = Lists.newArrayList(CfDealWithStatusEnum.values());
        if (!cfDealWithStatusEnums.contains(this.value)) {
            switch (this) {
                case DEFERRED_APPROVE:
                    return CrowdfundingOperationEnum.DEFER_APPROVE;
                case DISREGARD:
                    return CrowdfundingOperationEnum.NEVER_PROCESSING;
                case DEFERRED_CONTACT:
                    return CrowdfundingOperationEnum.DEFER_CONTACT;
                default:
                    return CrowdfundingOperationEnum.NONE_OPERATION;
            }
        }
        return CrowdfundingOperationEnum.NONE_OPERATION;
    }
}
