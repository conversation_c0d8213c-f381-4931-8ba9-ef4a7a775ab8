package com.shuidihuzhu.cf.enums.crowdfunding;

/**
 * Created by ahrievil on 2017/6/29.
 */
public enum AdminCaseReportDealStatus {

    NO_HANDLE(0, "某条举报未处理"),
    HANDLEING(1, "某条举报处理中"),
    FINISH(2, "某条举报处理完成"),
    DISREGARD(3, "某条举报不再处理"),
    SIMILAR_REPORT(4, "某条举报是相似举报"),
    BLACK_DISREGARD(5, "某条举报是黑名单用户举报-无需处理"),
    ;


    AdminCaseReportDealStatus(int value, String words) {
        this.value = value;
        this.words = words;
    }
    private int value;
    private String words;

    public int getValue() {
        return value;
    }

    public String getWords() {
        return words;
    }


    public static AdminCaseReportDealStatus getByValue(int value) {
        AdminCaseReportDealStatus[] values = AdminCaseReportDealStatus.values();
        for (AdminCaseReportDealStatus adminCaseReportStatusEnum : values) {
            if (value == adminCaseReportStatusEnum.value) {
                return adminCaseReportStatusEnum;
            }
        }
        return NO_HANDLE;
    }
}
