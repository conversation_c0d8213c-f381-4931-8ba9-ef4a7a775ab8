package com.shuidihuzhu.cf.enums.approve;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

/**
 * <AUTHOR>
 */
public enum ApproveControlSourceTypeEnum {

    /**
     * 任务工单跳转处理
     */
    @ApiModelProperty("任务工单跳转处理")
    WORK_ORDER_HANDLE(1, "任务工单跳转处理"),

    @ApiModelProperty("案例-详情页-让他去修改")
    CASE_INFO_PAGE_MODIFY(2, "案例详情页点击”让它去修改“"),

    @ApiModelProperty("材料审核-详情页-让他去修改")
    APPROVE_INFO_PAGE_MODIFY(3, "材料审核详情页点击”让它去修改“"),

    @ApiModelProperty("举报处理-详情页-让他去修改")
    REPORT_PAGE_MODIFY(4, "举报处理详情页点击”让它去修改“"),

    @ApiModelProperty("运营沟通-详情页-让他去修改")
    COMMUNICATION_PAGE_MODIFY(5, "运营沟通详情页点击”让它去修改“"),
    ;

    @Getter
    private final int value;

    @Getter
    private final String msg;

    ApproveControlSourceTypeEnum(int value, String msg) {
        this.value = value;
        this.msg = msg;
    }

    public static ApproveControlSourceTypeEnum parse(int value){
        for(ApproveControlSourceTypeEnum e : ApproveControlSourceTypeEnum.values()) {
            if (e.getValue() == value) {
                return e;
            }
        }
        return null;
    }
}
