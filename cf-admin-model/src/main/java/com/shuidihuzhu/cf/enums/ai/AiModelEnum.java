package com.shuidihuzhu.cf.enums.ai;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2024/5/29 7:39 PM
 */
public enum AiModelEnum {

    WEN_XIN(1, "文心一言"),

    DOU_BAO(2, "豆包"),

    ZHI_PU(3, "智谱清言"),
    MOON_SHOT(4, "moonshot-v1-128k"),
    TONG_YI(5, "通义千问"),
    DEEPSEEK_R1(6, "deepseekr1"),
    ;

    @Getter
    private final String msg;
    @Getter
    private final int code;

    AiModelEnum(int code, String msg){
        this.msg = msg;
        this.code = code;
    }

    private static final Map<Integer, String> map = new HashMap();

    public static Map<Integer, String> getEnumMap() {
        return map;
    }

    static {
        AiModelEnum[] var0 = values();
        int var1 = var0.length;

        for(int var2 = 0; var2 < var1; ++var2) {
            AiModelEnum e = var0[var2];
            map.put(e.getCode(), e.getMsg());
        }

    }

}
