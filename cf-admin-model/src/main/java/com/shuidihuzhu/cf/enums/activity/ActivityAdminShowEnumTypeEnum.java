package com.shuidihuzhu.cf.enums.activity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

/**
 * 注意*********** 此类配置的枚举必须有以下字段,没有会导致展示前端展示异常
 * code 或 value
 * msg
 * <AUTHOR>
 */
@ApiModel("admin可查询枚举类型")
@Getter
public enum ActivityAdminShowEnumTypeEnum {

    /**
     */
    @ApiModelProperty("活动类型")
    ACTIVITY_TYPE(1),

    @ApiModelProperty("补贴类型")
    FEE_TYPE(2),

    @ApiModelProperty("参加活动规则类型")
    WAVE_TYPE(3),

    @ApiModelProperty("重点区域类型")
    KEY_AREA_TYPE(4),

    @ApiModelProperty("参加范围枚举")
    SCOPE_TYPE(5),

    @ApiModelProperty("参加方式枚举")
    JOIN_TYPE(6),

    ;

    private final int value;

    ActivityAdminShowEnumTypeEnum(int value){
        this.value = value;
    }

    public static ActivityAdminShowEnumTypeEnum parse(int value) {
        for (ActivityAdminShowEnumTypeEnum statusEnum : ActivityAdminShowEnumTypeEnum.values()) {
            if (statusEnum.getValue() == value) {
                return statusEnum;
            }
        }
        throw new IllegalArgumentException(String.valueOf(value));
    }

}
