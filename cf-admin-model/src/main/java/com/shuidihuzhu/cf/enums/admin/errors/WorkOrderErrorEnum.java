package com.shuidihuzhu.cf.enums.admin.errors;

import com.shuidihuzhu.cf.enums.admin.interfaces.ICommonError;

public enum WorkOrderErrorEnum implements ICommonError {

    INVALID_TYPE(30001, "无效的类型"),
    ASSIGNING_TASK_NO_PERMISSION(30002, "该用户没有获取工单权限"),
    ASSIGNING_TASK_FAILED(30003, "获取工单失败"),
    UPDATE_TASK_STATUS_FAILED(30004, "更新工单状态失败"),

    FIRST_APPROVE_OPERATOR_NOT_EXIST(31005, "前置审核人员不存在")
    ;

    private int code;
    private String description;

    WorkOrderErrorEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public ErrorGroupEnum getGroupEnum() {
        return null;
    }

    @Override
    public int getCode() {
        return 0;
    }

    @Override
    public String getDescription() {
        return null;
    }
}
