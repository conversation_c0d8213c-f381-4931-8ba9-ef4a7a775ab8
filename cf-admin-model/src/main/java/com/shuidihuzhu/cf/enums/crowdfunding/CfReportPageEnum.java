package com.shuidihuzhu.cf.enums.crowdfunding;

import com.google.common.collect.ImmutableList;
import lombok.Getter;

/**
 * <AUTHOR>
 * @time 2019/12/13 下午5:08
 * @desc
 */
public enum CfReportPageEnum {
    QUESTIONER(1, "质疑方"),
    FUNDRAISER(2, "筹款方"),
    QUESTIONER_AND_FUNDRAISER(3,"质疑方&筹款方")
    ;
    @Getter
    private int key;

    @Getter
    private String value;

    public static final ImmutableList<Integer> REPORT_PAGE_LIST = ImmutableList.of(
            QUESTIONER.key,FUNDRAISER.key,QUESTIONER_AND_FUNDRAISER.key);

    CfReportPageEnum(int key, String value){
        this.key = key;
        this.value = value;
    }

    public static CfReportPageEnum parse(int key){
        for (CfReportPageEnum cfReportPageEnum : CfReportPageEnum.values()){
            if(key == cfReportPageEnum.getKey()){
                return cfReportPageEnum;
            }
        }
        return null;
    }
}
