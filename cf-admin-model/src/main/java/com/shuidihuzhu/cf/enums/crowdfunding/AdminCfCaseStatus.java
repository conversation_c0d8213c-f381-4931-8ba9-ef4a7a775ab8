package com.shuidihuzhu.cf.enums.crowdfunding;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/8/6.
 */
public enum AdminCfCaseStatus {
    No(0,"无状态"),
    APPROVE_NO(1,"审核未通过"),
    APPROVE_FINISH(2,"审核通过"),
    DRAWCASH_SUBMIT(3,"已申请提现"),
    DRAWCASH_APPROVE(4,"提现审核通过还未生成"),
    DRAWCASH_BUILD(5,"已生成"),
    DRAWCASH_SUCCESS(6,"已打款"),
    REFUND_SUBMIT(7,"已申请退款"),
    ;

    private String message;
    private int value;

    AdminCfCaseStatus(int value, String message) {
        this.message = message;
        this.value = value;
    }

    public String getMessage() {
        return message;
    }

    public int getValue() {
        return value;
    }
}
