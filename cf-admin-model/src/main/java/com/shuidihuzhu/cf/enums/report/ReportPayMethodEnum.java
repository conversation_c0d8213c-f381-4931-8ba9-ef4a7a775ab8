package com.shuidihuzhu.cf.enums.report;

import com.shuidihuzhu.cf.enhancer.enums.EnumUtils;
import com.shuidihuzhu.cf.enhancer.enums.IBaseEnum;
import lombok.Getter;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
public enum ReportPayMethodEnum implements IBaseEnum {

    /**
     * 打款方式枚举值为对公打款、分批打款、资金监管、暂不需特殊处理、维持已有打款方式
     */
    NONE(99, "不标记/维持已有打款方式"),
    PUBLIC(1, "对公打款"),
    PAY_IN_BATCH(2, "分批打款"),
    FUND_SUPERVISION(3, "资金监管"),
    NO_SPECIAL(4, "暂不需特殊处理"),

    ;

    private final int code;
    private final String msg;

    ReportPayMethodEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private static final Map<Integer, ReportPayMethodEnum> ENUM_CODE_MAP = EnumUtils.getEnumCodeMap(ReportPayMethodEnum.class);

    public static ReportPayMethodEnum parse(int code){
        return ENUM_CODE_MAP.get(code);
    }

    public static boolean shouldAddRemark(ReportPayMethodEnum methodEnum){
        return methodEnum != NONE;
    }

}
