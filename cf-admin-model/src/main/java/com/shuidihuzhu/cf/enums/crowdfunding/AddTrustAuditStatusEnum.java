package com.shuidihuzhu.cf.enums.crowdfunding;

import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.Map;

public enum AddTrustAuditStatusEnum {

    /**
     * 未审核
     */
    UN_SUBMITTED(1, "已下发"),
    /**
     * 审核通过
     */
    PASSED(2, "审核通过"),
    /**
     * 审核被拒绝
     */
    REJECTED(3, "审核驳回"),
    /**
     * 已提交
     */
    SUBMITTED(4, "待审核"),
    /**
     * 撤回
     */
    CANCEL(5, "已撤回");

    @Getter
    private final String msg;
    @Getter
    private int code;

    private static Map<Integer, AddTrustAuditStatusEnum> addTrustAuditStatusEnumHashMap = Maps.newHashMap();

    static {
        for (AddTrustAuditStatusEnum addTrustAuditStatusEnum : values()) {
            addTrustAuditStatusEnumHashMap.put(addTrustAuditStatusEnum.getCode(), addTrustAuditStatusEnum);
        }
    }

    AddTrustAuditStatusEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static AddTrustAuditStatusEnum getByCode(int code) {
        return addTrustAuditStatusEnumHashMap.get(code);
    }

}
