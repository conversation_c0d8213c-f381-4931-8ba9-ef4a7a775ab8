package com.shuidihuzhu.cf.enums;

/**
 * @Description: 风险类型
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/10/16 3:54 PM
 */
public enum CaseRiskTypeEnum {

    TARGET_AMOUNT_CONDITION(0, "未选择"),
    NORM_DISEASE_CONDITION(1, "有风险"),
    PATIENT_AGE_CONDITION(2, "无风险"),
    ;

    private int code;
    private String description;

    CaseRiskTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static String getDescription(int code) {
        for (CaseRiskTypeEnum value : CaseRiskTypeEnum.values()) {
            if (value.getCode() == code) {
                return value.getDescription();
            }
        }
        return  "";
    }

}
