package com.shuidihuzhu.cf.enums.shutdown;

import lombok.Getter;

/**
 * <AUTHOR>
 * @time 2018/11/27 下午2:40
 * @desc
 */
public enum BizTypeEnum {
    draw_launth(1, "draw_launth", "打款", 130000),
    draw_recover(2, "draw_recover", "打款重置", 2000),
    refund_infoid(3, "refund_infoid", "根据案例id退款", 70000),
    refund_uids(4, "refund_uids", "根据支付uid退款", 70000),
    refund_approve(5, "refund_approve", "审核退款申请", 2000),
    ;

    @Getter
    public int code;

    @Getter
    public String english;

    @Getter
    public String chinese;

    @Getter
    public long timeout;

    BizTypeEnum(int code, String english, String chinese, long timeout){
        this.code = code;
        this.english = english;
        this.chinese = chinese;
        this.timeout = timeout;
    }

}
