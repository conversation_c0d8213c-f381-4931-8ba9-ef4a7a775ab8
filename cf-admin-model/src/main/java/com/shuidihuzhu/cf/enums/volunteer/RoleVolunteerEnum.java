package com.shuidihuzhu.cf.enums.volunteer;

import lombok.Getter;


/**
 * <AUTHOR>
 * @date 2019-05-29
 * 志愿者权限设置
 */
public enum RoleVolunteerEnum {

    TEAM(1, "volunteer:team"),
    SERVE_ADMIN(2, "volunteer:serve"),
    PART_TIME(3, "volunteer:partTime"),
    WATER_DROP_VOLUNTEER(4, "volunteer:waterDrop"),
    SERIOUS_ILLNESS_SALVAGE_ANGEL(5, "volunteer:angel");

    @Getter
    int value;
    @Getter
    String permission;

    RoleVolunteerEnum(int v, String permission) {
        this.value = v;
        this.permission = permission;
    }

    public static RoleVolunteerEnum parse(String v){
        for (RoleVolunteerEnum e : RoleVolunteerEnum.values()) {
            if (e.getPermission().equals(v)) {
                return e;
            }
        }
        throw new IllegalArgumentException("can't find value of RoleEnum");
    }
}
