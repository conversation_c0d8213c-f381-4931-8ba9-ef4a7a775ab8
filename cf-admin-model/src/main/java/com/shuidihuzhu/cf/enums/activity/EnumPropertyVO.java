package com.shuidihuzhu.cf.enums.activity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 枚举数据类
 * <AUTHOR>
 */
@ApiModel("枚举数据类")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EnumPropertyVO {

    @ApiModelProperty("枚举值")
    private int value;

    @ApiModelProperty("枚举值名称")
    private String name;

    @ApiModelProperty("枚举值内容")
    private String content;

    @ApiModelProperty("是否可用")
    private boolean enable = true;

    @ApiModelProperty("枚举其他数据")
    private Object data;
}
