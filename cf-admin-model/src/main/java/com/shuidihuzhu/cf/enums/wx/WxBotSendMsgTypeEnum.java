package com.shuidihuzhu.cf.enums.wx;

/**
 * @Author: wangpeng
 * @Date: 2021/3/2 17:37
 * @Description:
 */
public enum WxBotSendMsgTypeEnum {

    APPROVE_NOTICE(1, "b8985a27-2d51-4a65-a05a-1e46c071558b", "材审复审提示"),
    INITIAL_SMART_AUDIT_SUCCESS(2, "67373970-1d75-4fd1-bf8a-22a61f0ec143", "初审自动审核通过异常报警");

    private final int code;
    private final String botKey;
    private final String desc;

    WxBotSendMsgTypeEnum(int code, String botKey, String desc) {
        this.code = code;
        this.botKey = botKey;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getBotKey() {
        return botKey;
    }

    public String getDesc() {
        return desc;
    }

    public static WxBotSendMsgTypeEnum getByValue(int code) {
        WxBotSendMsgTypeEnum[] values = values();
        for (WxBotSendMsgTypeEnum value : values) {
            if (Integer.valueOf(code).equals(value.getCode())) {
                return value;
            }
        }
        return null;
    }
}