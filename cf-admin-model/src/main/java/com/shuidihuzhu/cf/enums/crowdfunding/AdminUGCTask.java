package com.shuidihuzhu.cf.enums.crowdfunding;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by Ahrievil on 2017/11/30
 */
public class AdminUGCTask {

    public static final int ADMIN_ORDER_TABLE_SCAN_SIZE = 50;

    public enum Modules {

        NO(0, ""),
        FUNDING(1, "筹款"),
        ;

        private int code;
        private String word;
        private static Map<Integer, Modules> map = Maps.newHashMap();

        Modules(int code, String word) {
            this.code = code;
            this.word = word;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getWord() {
            return word;
        }

        public void setWord(String word) {
            this.word = word;
        }

        static {
            for (Modules modules : Modules.values()) {
                map.put(modules.getCode(), modules);
            }
        }

        public static Modules getByCode(int code) {
            Modules modules = map.get(code);
            return modules != null ? modules : Modules.NO;
        }
    }

    public enum Content {

        NO(0, ""),
        BASE_INFO(1, "图文信息"),
        ORDER(2, "订单"),
        PROGRESS(3, "动态"),
        COMMENT_ORDER(4, "订单评论"),
        COMMENT_PROGRESS(5, "动态评论"),
        VERIFICATION(6, "证实"),
        FIRST_APPROVE(7,"前置审核"),
        HEAD_IMAGE_URL(8,"案例头图"),
        HOPE_TREE_STATE(9,"患者社群动态"),
        PINGYI(10000,"评议评论"),
        ;

        private int code;
        private String word;
        private static Map<Integer, Content> map = Maps.newHashMap();

        Content(int code, String word) {
            this.code = code;
            this.word = word;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getWord() {
            return word;
        }

        public void setWord(String word) {
            this.word = word;
        }

        static {
            for (Content content : Content.values()) {
                map.put(content.getCode(), content);
            }
        }

        public static Content getByCode(int code) {
            Content content = map.get(code);
            return content != null ? content : Content.NO;
        }

    }

    public enum Result {

        NO(0, "未处理"),
        DELETED(1, "删除"),
        EDIT(2, "修改"),
        NOT_HANDLE(3, "审核通过"),
        SUGGEST_STOP_CROWDFUNDING(4, "停止筹款"),

        PASS_AND_SHOW(5, "通过展示"),
        ONLY_SELF(6, "仅自己可见"),

        AUTO_CLOSE(7, "自动异常关闭"),

        FIRST_APPROVE_REJECT(15, "前置审核工单-审核驳回"),
        FIRST_APPROVE_PASS(16, "前置审核工单-审核通过"),
        ;

        private int code;
        private String word;
        private static Map<Integer, Result> map = Maps.newHashMap();

        Result(int code, String word) {
            this.code = code;
            this.word = word;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getWord() {
            return word;
        }

        public void setWord(String word) {
            this.word = word;
        }

        static {
            for (Result result : Result.values()) {
                map.put(result.getCode(), result);
            }
        }

        public static Result getByCode(int code) {
            Result result = map.get(code);
            return result != null ? result : Result.NO;
        }

        public static Set<Integer> getUgcHandleCode() {
            return Sets.newHashSet(EDIT.getCode(), NOT_HANDLE.getCode(), SUGGEST_STOP_CROWDFUNDING.getCode());
        }
    }

}
