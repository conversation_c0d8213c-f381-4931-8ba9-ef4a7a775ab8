package com.shuidihuzhu.cf.enums.export;

import com.google.common.collect.Lists;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: wangpeng
 * @Date: 2022/11/8 15:34
 * @Description:
 */
@Getter
public enum CfWorkOrderFlowV2DownloadField {
    DATE(1, "日期"),
    MEMBER(2, "成员"),
    TODAY_CREATE_WORK_ORDER(3, "当日创建工单"),
    TODAY_ADD_WORK_ORDER(4, "当天新增工单量"),
    WORK_ORDER_ACTUAL_HANDLE_COUNT(5, "工单实际处理量"),
    NO_HANDLE(6, "未处理"),
    DOING(7, "处理中"),
    DONE_ALL_TODAY(8, "处理完成(当天所有)"),
    DONE_CREATE_TODAY(9, "处理完成(当天创建)"),
    NO_DONE_CREATE_TODAY(10, "未处理完(当天创建)"),
    NOT_NEED_HANDLE(11, "不需要处理"),
    THROUGH_WORK_ORDER(12, "流经过工单"),
    WORK_ORDER_24_HOUR_DONE_RATE(13, "工单24小时完成率"),
    AVERAGE_FIRST_RESPONSE_TIME(14, "平均首次响应时长"),
    AVERAGE_PROCESSING_TIME(15, "平均处理时长"),
    URGED_SIMPLEX_SINGULAR(16, "被催单工单数"),
    NUMBER_ASSIGNED_GROUP(17, "组内分配工单数"),
    FLOW_OUT_GROUP_WORKER_SINGULAR(18, "流转至组外工单数"),
    FREE_TIME(19, "空闲时间"),
    WORK_ORDER_RECEIVED(20, "待领取工单"),
    MOVING_WORKER_SINGULAR(21, "流转工单数"),
    AVERAGE_NUMBER_TURNOVER_WORK_ORDERS(22, "平均流转工单次数"),
    REPEAT_WORK_ORDER_COUNT(23, "重复建单数"),
    AVERAGE_DURATION_ORDER_TIME(24, "平均领单时长(分)"),
    ;

    private final int code;
    private final String desc;
    private static final Map<Integer, CfWorkOrderFlowV2DownloadField> enumMap = new HashMap();

    CfWorkOrderFlowV2DownloadField(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public static CfWorkOrderFlowV2DownloadField getByCode(int code) {
        return enumMap.get(code);
    }

    public static List<CfWorkOrderFlowV2DownloadField> getListByCodes(List<Integer> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Lists.newArrayList();
        } else {
            List<CfWorkOrderFlowV2DownloadField> fieldArrayList = Lists.newArrayList();
            codes.forEach((code) -> {
                fieldArrayList.add(enumMap.get(code));
            });
            return fieldArrayList;
        }
    }

    public static List<String> getHeaderNameByMemberType(int memberType) {
        List<String> fieldArrayList = Lists.newArrayList();
        for (Map.Entry<Integer, CfWorkOrderFlowV2DownloadField> entry : enumMap.entrySet()) {
            if (memberType == 1 &&
                    (CfWorkOrderFlowV2DownloadField.WORK_ORDER_RECEIVED.code == entry.getKey() || CfWorkOrderFlowV2DownloadField.MOVING_WORKER_SINGULAR.code == entry.getKey() ||
                     CfWorkOrderFlowV2DownloadField.AVERAGE_NUMBER_TURNOVER_WORK_ORDERS.code == entry.getKey() || CfWorkOrderFlowV2DownloadField.REPEAT_WORK_ORDER_COUNT.code == entry.getKey())) {
                continue;
            }
            if (memberType != 1 && (CfWorkOrderFlowV2DownloadField.WORK_ORDER_24_HOUR_DONE_RATE.code == entry.getKey() || CfWorkOrderFlowV2DownloadField.NUMBER_ASSIGNED_GROUP.code == entry.getKey() ||
                    CfWorkOrderFlowV2DownloadField.FLOW_OUT_GROUP_WORKER_SINGULAR.code == entry.getKey())) {
                continue;
            }
            fieldArrayList.add(entry.getValue().name());
        }
        return fieldArrayList;
    }

    static {
        CfWorkOrderFlowV2DownloadField[] var0 = values();
        int var1 = var0.length;

        for(int var2 = 0; var2 < var1; ++var2) {
            CfWorkOrderFlowV2DownloadField tmpEnum = var0[var2];
            enumMap.put(tmpEnum.getCode(), tmpEnum);
        }

    }
}
