package com.shuidihuzhu.cf.enums.approve;

import com.google.common.collect.ImmutableMap;
import io.swagger.annotations.ApiModel;
import lombok.Getter;

import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@ApiModel("评论来源类型")
public enum ApproveSourceTypeEnum {

    /**
     * 默认类型 老评论备注都是此类型
     */
    DEFAULT(0),

    /**
     * 医院核实下发专有备注
     * 医院核实备注 不同步四大详情页
     */
    HOSPITAL_AUDIT_PRIVATE(1),

    /**
     * 医院核实备注 需同步四大详情页
     */
    HOSPITAL_AUDIT_PUBLIC(2),

    /**
     * 增信审核 同步详情页
     */
    CREDIT_APPROVE_ON_HOME(3),

    /**
     * 增信审核 不同步详情页
     */
    CREDIT_APPROVE_WITHOUT_HOME(4),

    RIVER_DEFAULT(5),
    ;

    /**
     * 分别配置不同页面展示哪些来源的评论
     */
    public interface Pages{

        /**
         * 四大详情页
         */
        ApproveSourceTypeEnum[] MAIN_PAGE = new ApproveSourceTypeEnum[]{DEFAULT, HOSPITAL_AUDIT_PUBLIC, CREDIT_APPROVE_ON_HOME};

        /**
         * 医院核实审核
         */
        ApproveSourceTypeEnum[] HOSPITAL_AUDIT = new ApproveSourceTypeEnum[]{HOSPITAL_AUDIT_PRIVATE, HOSPITAL_AUDIT_PUBLIC};
    }

    @Getter
    private final int value;

    private static ImmutableMap<Integer, ApproveSourceTypeEnum> valueMap = ImmutableMap.copyOf(Arrays.stream(ApproveSourceTypeEnum.values()).collect(Collectors.toMap(ApproveSourceTypeEnum::getValue, v -> v)));

    ApproveSourceTypeEnum(int value) {
        this.value = value;
    }

    public static ApproveSourceTypeEnum parse(int value){
        ApproveSourceTypeEnum v = valueMap.get(value);
        if (v == null) {
            throw new IllegalArgumentException("can not find value of ApproveSourceTypeEnum: " + value);
        }
        return v;
    }
}
