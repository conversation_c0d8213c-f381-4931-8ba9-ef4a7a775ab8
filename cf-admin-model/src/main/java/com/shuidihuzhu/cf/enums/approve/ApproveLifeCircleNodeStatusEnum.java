package com.shuidihuzhu.cf.enums.approve;

import com.google.common.collect.ImmutableSet;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.model.river.RiverStatusEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;

/**
 * <AUTHOR>
 * @date 2019-03-31  15:25
 */
public enum ApproveLifeCircleNodeStatusEnum {

    /**
     * 通过
     */
    PASSED(),

    /**
     * 进行中
     */
    PROCESSING(),

    /**
     * 未开始
     */
    TODO(),
    ;

    ApproveLifeCircleNodeStatusEnum(){ }

    private static final ImmutableSet<Object> PASSED_TRANS_OBJ_SET = ImmutableSet.of(
            FirstApproveStatusEnum.APPLY_SUCCESS,
            AdminUGCTask.Result.NOT_HANDLE,
            AdminUGCTask.Result.EDIT,
            AdminUGCTask.Result.SUGGEST_STOP_CROWDFUNDING,
            CrowdfundingStatus.CROWDFUNDING_STATED,
            CrowdfundingInfoStatusEnum.PASSED,
            CfDrawCashConstant.ApplyStatus.APPROVE_SUCCESS,
            CfPayeeInfoChangeEnum.TypeRecordStatusEnum.APPROVED,
            CfPayeeInfoChangeEnum.TypeRecordStatusEnum.CLOSED,
            UseProgressStatusEnum.AUDIT_SUCCESS,
            RiverStatusEnum.PASS,
            HandleResultEnum.audit_pass,
            AddTrustAuditStatusEnum.PASSED,
            AddTrustAuditStatusEnum.CANCEL
    );

    public static ApproveLifeCircleNodeStatusEnum trans(Object obj) {
        return PASSED_TRANS_OBJ_SET.contains(obj) ? PASSED : PROCESSING;
    }

}
