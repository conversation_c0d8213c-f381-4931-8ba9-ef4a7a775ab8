package com.shuidihuzhu.cf.enums.crowdfunding;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * @Auther: subing
 * @Date: 2020/3/19
 */
public enum  AdminCfReportLabelStatusEnum {
    //
    ALL_LABEL(0, "全部标签"),
    PERSON_LABEL(1, "人工判断标签"),
    AUTO_LABEL(2, "自动匹配标签")
    ;
    private int type;
    private String description;

    public int getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }

    AdminCfReportLabelStatusEnum(int type, String description) {
        this.type = type;
        this.description = description;
    }
    static Map<Integer, String> maps =  Maps.newHashMap();

    static {
        for (AdminCfReportLabelStatusEnum fundUseAuditStatusType : AdminCfReportLabelStatusEnum.values()) {
            maps.put(fundUseAuditStatusType.type, fundUseAuditStatusType.description);
        }
    }

    public static String getDescription(int type) {
        for (AdminCfReportLabelStatusEnum fundUseAuditStatusType : AdminCfReportLabelStatusEnum.values()) {
            if (fundUseAuditStatusType.getType() == type) {
                return  fundUseAuditStatusType.getDescription();
            }
        }
        return "";
    }
}
