package com.shuidihuzhu.cf.enums.wx;

import com.shuidihuzhu.cf.enums.BackgroundLogEnum;

/**
 * @Author: liangh<PERSON><PERSON><PERSON>
 * @Date: 2020/8/7 10:49
 */
public enum BizTypeMerchantNoEnum {
    MERC_NO_118(118, "1569594041"),
    ;

    private Integer bizType;
    private String merchantNo;

    BizTypeMerchantNoEnum(Integer bizType, String merchantNo) {
        this.bizType = bizType;
        this.merchantNo = merchantNo;
    }

    public Integer getBizType() {
        return bizType;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public static BizTypeMerchantNoEnum getByValue(int code) {
        BizTypeMerchantNoEnum[] values = values();
        for (BizTypeMerchantNoEnum value : values) {
            if (Integer.valueOf(code).equals(value.getBizType())) {
                return value;
            }
        }
        return null;
    }
}
