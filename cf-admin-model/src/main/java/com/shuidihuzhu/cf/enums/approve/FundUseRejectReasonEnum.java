package com.shuidihuzhu.cf.enums.approve;

import lombok.Getter;

/**
 * @Author: wangpeng
 * @Date: 2021/5/31 15:44
 * @Description:
 */
public enum FundUseRejectReasonEnum {
    /**
     * 未审核
     */
    REASON_ONE(1, "请描述所筹金额的花费情况，及带有患者名字且清晰的相关票据"),

    OTHER(2, "其他"),
    ;

    @Getter
    private final int code;

    @Getter
    private final String msg;

    FundUseRejectReasonEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static FundUseRejectReasonEnum parse(int code) {
        for (FundUseRejectReasonEnum e : FundUseRejectReasonEnum.values()){
            if (e.getCode() == code) {
                return e;
            }
        }
        throw new IllegalArgumentException("FundUseRejectReasonEnum not found code: " + code);
    }
}
