package com.shuidihuzhu.cf.enums.crowdfunding;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * <AUTHOR> Ahrievil
 */
public enum CfBlacklistWordTypeEnum {

    OTHER(0, "其他"),
    CROWDFUNDING(1, "筹款"),
    MINA(2, "小程序")
    ;

    private int code;
    private String desc;
    private static final Map<Integer, CfBlacklistWordTypeEnum> map = Maps.newHashMap();

    CfBlacklistWordTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public Map<Integer, CfBlacklistWordTypeEnum> getMap() {
        return map;
    }

    static {
        for (CfBlacklistWordTypeEnum cfBlacklistWordTypeEnum : CfBlacklistWordTypeEnum.values()) {
            map.put(cfBlacklistWordTypeEnum.getCode(), cfBlacklistWordTypeEnum);
        }
    }

    public static CfBlacklistWordTypeEnum getByCode(int code) {
        return map.getOrDefault(code, CfBlacklistWordTypeEnum.OTHER);
    }
}
