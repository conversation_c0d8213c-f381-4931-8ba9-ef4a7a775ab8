package com.shuidihuzhu.cf.enums.verification;

public enum VerifyStatusEnum {
	MEDICAL_NOT_VERIFY(0,"医护未认证"),
	MEDICAL_VERIFY(1, "医护已认证");

	private int code;
	private String message;

	VerifyStatusEnum(Integer code, String message) {
		this.code = code;
		this.message = message;
	}

	public Integer getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}

	public static VerifyStatusEnum parse (int code) {
		VerifyStatusEnum[] values = VerifyStatusEnum.values();
		for (VerifyStatusEnum value : values) {
			if (value.getCode() == code) {
				return value;
			}
		}
		return null;
	}
}
