package com.shuidihuzhu.cf.enums.crowdfunding;

/**
 * Created by ahrievil on 2017/6/10.
 */
public enum CfApproveCaseHandleEnum {


    DEFAULT(0, "默认"),
    OLD_REPEAT(1, "重复案例-兼容梦想筹"),
    GARBAGE(2, "垃圾案例"),
    DELAY_APPROVE(3, "延迟处理案例"),
    DELAY_CONTACT(4, "延迟电话联系"),
    NO_LONGER_HANDLE(5, "不再处理案例"),
    REPEAT_CASE(6, "重复案例"),
    TWICE_CASE(7, "二次案例"),
    ;
    CfApproveCaseHandleEnum (int value, String message) {
        this.value = value;
        this.message = message;
    }

    private int value;
    private String message;

    public int getValue() {
        return this.value;
    }

    public String getMessage () {
        return this.message;
    }

    public static CfApproveCaseHandleEnum getByValue (int code) {
        CfApproveCaseHandleEnum[] values = CfApproveCaseHandleEnum.values();
        for (CfApproveCaseHandleEnum value : values) {
            if (value.getValue() == code) {
                return value;
            }
        }
        return DEFAULT;
    }
}
