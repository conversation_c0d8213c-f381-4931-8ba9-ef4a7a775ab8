package com.shuidihuzhu.cf.enums.report;

import com.shuidihuzhu.cf.enums.record.RecordBizTypeEnum;
import lombok.Getter;

/**
 * @Author: wangpeng
 * @Date: 2022/5/25 18:32
 * @Description:
 */
@Getter
public enum AdminCfReportLabelRiskType {
    RISK(1,"核实有风险"),
    NO_RISK(2,"核实无风险"),

    ;

    private int code;
    private String desc;

    AdminCfReportLabelRiskType(int code, String desc){
        this.code = code;
        this.desc = desc;
    }

    public static AdminCfReportLabelRiskType parse(int code){
        for (AdminCfReportLabelRiskType recordBizTypeEnum : AdminCfReportLabelRiskType.values()){
            if(code == recordBizTypeEnum.getCode()){
                return recordBizTypeEnum;
            }
        }
        return null;
    }
}
