package com.shuidihuzhu.cf.enums.admin;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminCapitalStatusInfo;

import java.util.Map;

/**
 * Created by ahrievil on 2017/1/18.
 */
public enum ApproveListOrderByWhat {
    CREATE_TIME(1,"ci.create_time desc"),
    AMOUNT(2,"ci.amount desc"),
    OPERATION_TIME(3,"cfo.operate_time desc"),
    AUDIT_COMMIT_TIME(4,"cfo.audit_commit_time asc"),
    CALL_COUNT(5,"cfo.call_count desc"),
    REFUSE_COUNT(6,"cfo.refuse_count desc");

    private Integer code;
    private String value;

    ApproveListOrderByWhat(Integer code,String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    private static Map<Integer, ApproveListOrderByWhat> MAP = Maps.newHashMapWithExpectedSize(ApproveListOrderByWhat.values().length);

    static {
        for (ApproveListOrderByWhat approveListOrderByWhat : ApproveListOrderByWhat.values()) {
            MAP.put(approveListOrderByWhat.getCode(), approveListOrderByWhat);
        }
    }


    public static ApproveListOrderByWhat getByCode(int code) {
        return MAP.get(code);
    }

    public static String getValue(int code) {
        for (ApproveListOrderByWhat approveListOrderByWhat: ApproveListOrderByWhat.values()) {
            if (approveListOrderByWhat.getCode() == code) {
                return approveListOrderByWhat.getValue();
            }
        }
        return ApproveListOrderByWhat.CREATE_TIME.getValue();
    }
}
