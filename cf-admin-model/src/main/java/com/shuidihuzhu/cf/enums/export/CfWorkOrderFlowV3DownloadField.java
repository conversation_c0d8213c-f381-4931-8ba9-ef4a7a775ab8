package com.shuidihuzhu.cf.enums.export;

import com.google.common.collect.Lists;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: wangpeng
 * @Date: 2022/11/8 17:30
 * @Description:
 */
@Getter
public enum CfWorkOrderFlowV3DownloadField {
    WORK_ORDER_ID(1, "工单号"),
    CASE_ID(2, "案例id"),
    CASE_TITLE(3, "案例标题"),
    QUESTION_ONE_LEVE(4, "问题一级分类"),
    QUESTION_TWO_LEVE(5, "问题二级分类"),
    QUESTION_DES(6, "问题描述"),
    DEGREE_EMERGENCY(7, "紧急程度"),
    STATUS(8, "状态"),
    HANDLE_ACTION(9, "处理动作"),
    HANDLE_RESULT(10, "处理结果"),
    LAST_OPERATOR(11, "最新处理人"),
    OPERATOR_ORG(12, "处理人组织"),
    CREATOR(13, "创建人"),
    CREATOR_ORG(14, "创建人组织"),
    CREATE_TIME(15, "创建时间"),
    HANDLE_TIME(16, "处理时长(min)"),
    LAST_OPERATE_TIME(17, "最新操作时间"),
    NUMBER_REMINDERS(18, "被催单次数"),
    REPEAT_WORK_ORDER_COUNT(19, "重复建单次数"),
    SYS_ASS_TIME(20, "系统分配时间"),
    FIRST_SYS_ASS_OPERATOR(21, "首次系统分配处理人"),
    FIRST_HANDLE_TIME(22, "首次处理时间"),
    ;
    private final int code;
    private final String desc;
    private static final Map<Integer, CfWorkOrderFlowV3DownloadField> enumMap = new HashMap();

    CfWorkOrderFlowV3DownloadField(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public static CfWorkOrderFlowV3DownloadField getByCode(int code) {
        return enumMap.get(code);
    }

    public static List<CfWorkOrderFlowV3DownloadField> getListByCodes(List<Integer> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Lists.newArrayList();
        } else {
            List<CfWorkOrderFlowV3DownloadField> fieldArrayList = Lists.newArrayList();
            codes.forEach((code) -> {
                fieldArrayList.add(enumMap.get(code));
            });
            return fieldArrayList;
        }
    }

    static {
        CfWorkOrderFlowV3DownloadField[] var0 = values();
        int var1 = var0.length;

        for(int var2 = 0; var2 < var1; ++var2) {
            CfWorkOrderFlowV3DownloadField tmpEnum = var0[var2];
            enumMap.put(tmpEnum.getCode(), tmpEnum);
        }

    }
}
