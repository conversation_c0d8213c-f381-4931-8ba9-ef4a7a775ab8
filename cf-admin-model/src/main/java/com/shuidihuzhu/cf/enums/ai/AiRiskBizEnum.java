package com.shuidihuzhu.cf.enums.ai;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description: ai风控业务枚举
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/7/14 16:41
 */
public enum AiRiskBizEnum {

    CF_CONTENT(0, "标题文章"),

    CF_VERIFY(1, "证实信息"),
    ;

    @Getter
    private final String msg;
    @Getter
    private final int code;

    AiRiskBizEnum(int code, String msg){
        this.msg = msg;
        this.code = code;
    }

}
