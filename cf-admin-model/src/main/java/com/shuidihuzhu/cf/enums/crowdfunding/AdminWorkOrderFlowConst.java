package com.shuidihuzhu.cf.enums.crowdfunding;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;

import java.util.*;

/**
 * Created by Ahrievil on 2018/1/2
 */
public class AdminWorkOrderFlowConst {

    public enum ProblemType {

        OUT_BOUND(3, "外呼"),
        SECOND_LINE(4, "二线"),
        FLOW_FIRST(11, "首次"),

        APPROVE(302, "审核"),
        REPORT(303, "举报"),
        HOSPITAL_AUDIT(304, "医院核实"),
        DRAW_CASH_AND_REFUND(305, "打退款"),
        BUG(306, "BUG"),
        OTHER(307, "其他");

        private int code;
        private String word;
        private static Map<Integer, ProblemType> map = Maps.newHashMap();

        ProblemType(int code, String word) {
            this.code = code;
            this.word = word;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getWord() {
            return word;
        }

        public void setWord(String word) {
            this.word = word;
        }

        static {
            for (ProblemType role : ProblemType.values()) {
                map.put(role.getCode(), role);
            }
        }

        public static ProblemType getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            ProblemType role = map.get(code);
            return role != null ? role : ProblemType.OTHER;
        }

        public static Map<Integer, String> getOutInfo() {
            Map<Integer, String> result = Maps.newHashMap();
            result.put(ProblemType.OUT_BOUND.getCode(), ProblemType.OUT_BOUND.getWord());
            return result;
        }

        public static Map<Integer, String> getFirsetInfo() {
            Map<Integer, String> result = Maps.newHashMap();
            result.put(ProblemType.FLOW_FIRST.getCode(), ProblemType.FLOW_FIRST.getWord());
            return result;
        }


        public static Map<Integer, String> getSecondLineInfo() {
            Map<Integer, String> result = Maps.newHashMap();
            map.entrySet().stream().skip(1).forEach(val -> result.put(val.getKey(), val.getValue().getWord()));
            result.remove(ProblemType.OUT_BOUND.getCode());
            result.remove(ProblemType.FLOW_FIRST.getCode());
            return result;
        }

    }


    public enum handleType {

        NO_HANDLE(0, "不需要处理"),
        FINISH(1, "完成"),
        ALLOT(2, "分配"),
        HANDLE(3, "开始处理任务"),
        ;

        private int value;
        private String word;
        private static Map<Integer, handleType> map = Maps.newHashMap();

        static {
            for (handleType handleType : handleType.values()) {
                map.put(handleType.getValue(), handleType);
            }
        }

        public static handleType getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            handleType handleType = map.get(code);
            return handleType;
        }

        public int getValue() {
            return value;
        }

        public void setValue(int value) {
            this.value = value;
        }

        public String getWord() {
            return word;
        }

        public void setWord(String word) {
            this.word = word;
        }

        handleType(int value, String word) {
            this.value = value;
            this.word = word;
        }
    }


    public final static List<Integer> finishedOptList = Lists.newArrayList(
            OperateTypeEnum.NO_HANDLE_WORK_FLOW.getCode(),
            OperateTypeEnum.HANDLE_SUCCESS_WORK_FLOW.getCode()
    );

    @AllArgsConstructor
    public enum OperateTypeEnum {
        DEFAULT(0, "默认值"),
        CREATE_WORK_FLOW(2, "创建"),
        ASSIGN_WORK_FLOW(4, "领取"),
        HANDLE_SUCCESS_WORK_FLOW(8, "处理完成"),
        NO_HANDLE_WORK_FLOW(16, "不需要处理"),
        ALLOT_WORK_FLOW(24, "分配"),
        MODIFY_ORDER_LEVEL(25, "修改紧急程度"),
        MODIFY_ORDER_SECOND_ID(26, "修改问题类型"),
        HANDLE_WORK_FLOW(32, "开始处理"),
        SYSTEM_ALLOT_WORK_FLOW(33, "系统分配"),
        JING_XI_REPLY(34, "回复"),
        ;


        private int code;
        private String desc;

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static OperateTypeEnum getEnumByCode(int code) {
            OperateTypeEnum[] values = OperateTypeEnum.values();
            for (OperateTypeEnum typeEnum : values) {
                if (code == typeEnum.getCode()) {
                    return typeEnum;
                }
            }
            return null;
        }

        public static List<Integer> getHasHandledOperateCode() {
            List<Integer> operateCodes = new ArrayList<>();
            operateCodes.add(HANDLE_SUCCESS_WORK_FLOW.code);
            operateCodes.add(NO_HANDLE_WORK_FLOW.code);
            operateCodes.add(ALLOT_WORK_FLOW.code);
            return operateCodes;
        }

        public static List<Integer> getOperateHisCode() {
            List<Integer> operateCodes = new ArrayList<>();
            operateCodes.add(CREATE_WORK_FLOW.code);
            operateCodes.add(ASSIGN_WORK_FLOW.code);

            operateCodes.add(HANDLE_SUCCESS_WORK_FLOW.code);
            operateCodes.add(NO_HANDLE_WORK_FLOW.code);

            operateCodes.add(ALLOT_WORK_FLOW.code);
            operateCodes.add(HANDLE_WORK_FLOW.code);

            operateCodes.add(MODIFY_ORDER_LEVEL.code);
            operateCodes.add(MODIFY_ORDER_SECOND_ID.code);

            operateCodes.add(SYSTEM_ALLOT_WORK_FLOW.code);

            operateCodes.add(JING_XI_REPLY.code);
            return operateCodes;
        }

    }


    public enum MarkType {
        staff_offline(0),
        org_has_work_staff_free(1),
        ;
        int code;

        MarkType(int code) {
            this.code = code;
        }
    }

}
