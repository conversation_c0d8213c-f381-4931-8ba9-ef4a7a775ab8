package com.shuidihuzhu.cf.enums.crowdfunding;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/10/31.
 *
 * 资金相关的操作。
 */
public class AdminCapitalStatusInfo {

    //-1 状态为无

    //提现资金状态
    public enum DrawCashStatus {

        SUBMIT_DRAW(1, "已申请提现"),
        UN_BUILD_DRAW(2, "未生成打款"),
        BUILD_DRAW(3, "已生成打款，待财务付款"),
        DRAW_SUCCESS(4, "打款成功"),
        DRAW_FAIL(5, "打款失败"),
        NEED_DRAW_MANUAL(6, "需要人工打款"),
        CANCEL_MANUAL(7, "撤销人工打款"),
        SUBMIT_FINANCE(8, "后台人工打款已提交财务,待财务付款"),
        WAIT_FINANCE_DRAW(9, "等待财务付款"),
        DRAW_RECOVER(10, "打款被重置"),
        AGAIN_DRAW(11, "打款失败后再一次打款"),
        SECOND_KIND_CARD(12, "收款卡被标为二类卡"),
        //15.银行退回
        BANK_BACK(15, "确定银行已退回，需要与用户沟通重确打款消息"),
        APPROVE_PASS_PUB(16, "提现审核通过并公示"),
        DRAW_MANUAL_SUCCESS(14, "人工打款成功"),
        DRAW_MANUAL_PART(17, "人工打款："),
        DRAW_MANUAL_FAIL(18, "人工打款被退回："),
        SECOND_CARD_DRAW_SUCCESS(19, "二类卡打款成功"),
        SECOND_CARD_DRAW_FAIL(20, "二类卡打款失败"),
        CANCEL_DRAW_CASH(21, "撤销打款"),
        CANCEL_REFUND(22, "撤销退款"),
        APPROVE_REJECT(23,"提现申请被驳回"),
        BACK_MONEY(24, "运营同学操作了资金返还"),
        RECHARGE(25, "案例充值"),
        OFFLINE_LAUNCH_CONFIRM(26, "线下打款的确认"),
        REFUND_SINGLE_PART(27, "单笔退款操作"),
        REFUND_PART_AMOUNT(28, "按比例退款操作")
        ;


        private int code;

        private String word;

        DrawCashStatus(int code, String word) {
            this.code = code;
            this.word = word;
        }

        private static Map<Integer, DrawCashStatus> MAP = Maps.newHashMapWithExpectedSize(DrawCashStatus.values().length);

        static {
            for (DrawCashStatus drawCashStatus : DrawCashStatus.values()) {
                MAP.put(drawCashStatus.getCode(), drawCashStatus);
            }
        }

        public static DrawCashStatus getByCode(int code) {
            return MAP.get(code);
        }

        public String getWord() {
            return word;
        }

        public void setWord(String word) {
            this.word = word;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

    }

    //退款资金状态
    public  enum  RefundStatus
    {

        SUBMIT_REFUND(1, "已申请退款"),
        REFUND_FAIL(2, "退款失败"),
        REFUND_SUCCESS(3, "退款成功"),
        HAVE_PART_REFUND(4, "单笔退款"),
        APPROVE_PASS(5, "退款申请通过"),
        REFUND_HANDLING(6, "退款中");

        private int code;

        private String word;

        RefundStatus(int code, String word) {
            this.code = code;
            this.word = word;
        }

        private static Map<Integer, RefundStatus> MAP = Maps.newHashMapWithExpectedSize(RefundStatus.values().length);

        static {
            for (RefundStatus refundStatus : RefundStatus.values()) {
                MAP.put(refundStatus.getCode(), refundStatus);
            }
        }

        public static RefundStatus getByCode(int code) {
            return MAP.get(code);
        }

        public String getWord() {
            return word;
        }

        public void setWord(String word) {
            this.word = word;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }
    }


    //案例资金状态
    public  enum  CfCapitalStatus
    {
        SUBMIT_DRAW(1, "已申请提现"),
        SUB_SHOW(2, "公示中(待生成)"),
        BUILD_DRAW(3, "待打款"),
        DRAW_SUCCESS(4, "打款成功"),
        DRAW_FAIL(5, "打款失败"),
        SUBMIT_SUCCESS(6, "已申请退款"),
        REFUND_FAIL(7, "退款失败"),
        REFUND_SUCCESS(8, "退款成功"),
        REFUND_HANDING(9, "退款中"),
        DRAW_HANDING(10, "打款中"),
        DRAW_PART_SUCCESS(11, "打款部分成功")
        ;


        private int code;

        private String word;

        CfCapitalStatus(int code, String word) {
            this.code = code;
            this.word = word;
        }

        private static Map<Integer, CfCapitalStatus> MAP = Maps.newHashMapWithExpectedSize(CfCapitalStatus.values().length);

        static {
            for (CfCapitalStatus cfCapitalStatus : CfCapitalStatus.values()) {
                MAP.put(cfCapitalStatus.getCode(), cfCapitalStatus);
            }
        }

        public static CfCapitalStatus getByCode(int code) {
            return MAP.get(code);
        }

        public String getWord() {
            return word;
        }

        public void setWord(String word) {
            this.word = word;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }
    }
}
