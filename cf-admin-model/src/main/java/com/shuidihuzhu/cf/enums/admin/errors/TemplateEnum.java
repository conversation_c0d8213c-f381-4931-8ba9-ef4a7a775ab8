package com.shuidihuzhu.cf.enums.admin.errors;

import com.shuidihuzhu.cf.enums.admin.interfaces.ICommonError;

/**
 * 本枚举仅用来拷贝并生成新的枚举
 *
 * @Author: wuxinlong
 * @Since: 2016-11-09
 */
public enum TemplateEnum implements ICommonError {
	// 在这里新增枚举值
	;

	// 请在ErrorGroupEnum中添加枚举组的值，并修改此枚举组的值
	private ErrorGroupEnum errorGroup = ErrorGroupEnum.GROUP_COMMON;
	private int code;
	private String description;

	TemplateEnum(int code, String description) {
		this.code = code;
		this.description = description;
	}

	@Override
	public ErrorGroupEnum getGroupEnum() {
		return errorGroup;
	}

	@Override
	public int getCode() {
		return errorGroup.getCode() * 1000 + code;
	}

	@Override
	public String getDescription() {
		return description;
	}
}
