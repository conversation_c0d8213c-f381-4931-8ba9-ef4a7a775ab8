package com.shuidihuzhu.cf.enums.crowdfunding;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * @Auther: subing
 * @Date: 2020/4/17
 */
public enum AdminCfReportDisposeUseStatusEnum {
    //
    NO_USE(0, "未启用"),
    IS_USE(1, "启用")
    ;
    private int type;
    private String description;

    public int getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }

    AdminCfReportDisposeUseStatusEnum(int type, String description) {
        this.type = type;
        this.description = description;
    }
    static Map<Integer, String> maps =  Maps.newHashMap();

    static {
        for (AdminCfReportDisposeUseStatusEnum fundUseAuditStatusType : AdminCfReportDisposeUseStatusEnum.values()) {
            maps.put(fundUseAuditStatusType.type, fundUseAuditStatusType.description);
        }
    }

    public static String getDescription(int type) {
        for (AdminCfReportDisposeUseStatusEnum fundUseAuditStatusType : AdminCfReportDisposeUseStatusEnum.values()) {
            if (fundUseAuditStatusType.getType() == type) {
                return  fundUseAuditStatusType.getDescription();
            }
        }
        return "";
    }
}
