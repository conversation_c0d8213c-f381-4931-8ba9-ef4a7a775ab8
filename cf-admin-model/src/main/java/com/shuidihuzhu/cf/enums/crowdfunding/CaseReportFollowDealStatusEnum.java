package com.shuidihuzhu.cf.enums.crowdfunding;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/9/6.
 */
public enum CaseReportFollowDealStatusEnum {

    NO_HANDLE(0, "跟进未处理"),
    WAIT_DYNAMIC_RELEASE(1, "等待动态发布"),
    SPECIAL_FOLLOW(2, "主动致电"),
    ACTIVE_CALL(3, "特殊跟进"),
    ADD_TRUST(4,"增信说明"),
    ;


    CaseReportFollowDealStatusEnum(int value, String words) {
        this.value = value;
        this.words = words;
    }
    private int value;
    private String words;

    public int getValue() {
        return value;
    }

    public String getWords() {
        return words;
    }

    public static CaseReportFollowDealStatusEnum getByValue(int value) {
        CaseReportFollowDealStatusEnum[] values = CaseReportFollowDealStatusEnum.values();
        for (CaseReportFollowDealStatusEnum caseReportFollowDealStatusEnum : values) {
            if (value == caseReportFollowDealStatusEnum.value) {
                return caseReportFollowDealStatusEnum;
            }
        }
        return NO_HANDLE;
    }
}
