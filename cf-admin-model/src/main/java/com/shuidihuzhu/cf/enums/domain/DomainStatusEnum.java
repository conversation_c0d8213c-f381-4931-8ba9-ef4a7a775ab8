package com.shuidihuzhu.cf.enums.domain;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * <AUTHOR>
 */
public enum DomainStatusEnum {
    ADD_NOT_CONFIGURED(1, "已添加未配置完"),
    ALREADY_CONFIGURED(2, "已配置"),
    ALREADY_ONLINE_NOT_OPENING(3, "已上线未开量"),
    ALREADY_OPENING(4, "已开量"),
    ALREADY_OFFLINE(5, "已下线"),
    IS_LIMITED(6, "被限制"),
    ALREADY_RELEASE(7, "已释放");

    private int status;
    private String desc;

    DomainStatusEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    private static Map<Integer, DomainStatusEnum> map = Maps.newHashMap();

    static {
        for (DomainStatusEnum statusEnum : DomainStatusEnum.values()) {
            map.put(statusEnum.getStatus(), statusEnum);
        }
    }

    public static DomainStatusEnum getByStatus(int status) {
        return map.get(status);
    }

    public int getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

}
