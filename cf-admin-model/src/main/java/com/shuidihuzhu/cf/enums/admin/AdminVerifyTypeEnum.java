package com.shuidihuzhu.cf.enums.admin;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/11/4
 */
public enum AdminVerifyTypeEnum {

    ID_CARD_VERIFY(1,"身份证验证"),
    BANK_CARD_VERIFY(2,"银行卡验证"),
    ;

    @Getter
    private int type;
    @Getter
    private String desc;

    private static final Map<Integer, AdminVerifyTypeEnum> MAP = Maps.newHashMap();

    static {
        for (AdminVerifyTypeEnum feedBackStateEnum : AdminVerifyTypeEnum.values()) {
            MAP.put(feedBackStateEnum.getType(), feedBackStateEnum);
        }
    }

    AdminVerifyTypeEnum(Integer type, String desc){
        this.type = type;
        this.desc = desc;
    }

    public static AdminVerifyTypeEnum parse(int type){
        return MAP.get(type);
    }
}
