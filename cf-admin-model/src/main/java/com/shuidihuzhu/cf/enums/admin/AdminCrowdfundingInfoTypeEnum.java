package com.shuidihuzhu.cf.enums.admin;

import com.google.common.collect.ImmutableList;
import lombok.Getter;

/**
 * @author: wanghui
 * @create: 2018/10/16 下午2:10
 */
public enum AdminCrowdfundingInfoTypeEnum {
    TYPE_10(10,"推荐位展示的案例类型"),
    TYPE_11(11,"列表位展示的案例类型"),
    TYPE_1(1,"快手小程序展示的案例"),
    ;

    @Getter
    private Integer type;
    @Getter
    private String desc;

    /**
     * 首页展示的类型
     */
    public static final ImmutableList<Integer> RECOMMEND_LIST = ImmutableList.of(
            TYPE_10.getType(),
            TYPE_11.getType()
    );

    AdminCrowdfundingInfoTypeEnum(Integer type, String desc){
        this.type = type;
        this.desc = desc;
    }

    public static AdminCrowdfundingInfoTypeEnum parse(Integer type){
        for(AdminCrowdfundingInfoTypeEnum crowdfundingInfoTypeEnum : values()){
            if(type == crowdfundingInfoTypeEnum.getType()){
                return crowdfundingInfoTypeEnum;
            }
        }
        return null;
    }
}
