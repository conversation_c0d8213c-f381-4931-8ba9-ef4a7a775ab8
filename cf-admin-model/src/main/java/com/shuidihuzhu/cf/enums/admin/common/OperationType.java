package com.shuidihuzhu.cf.enums.admin.common;

/**
 * Author: wuxinlong
 * Date: 16/10/9 15:29
 */

public enum OperationType {
	// 共6位，最前面3位用来记录类别
	// 用户信息相关
	VERIFY_CODE_QUERY(101001),
	USER_INFO_MODIFY(101002),
	USER_QUIT_ORDER(101003),
	USER_MOBILE_CHANGE(101004),

	// 爱心筹相关
	CROWDFUNDING_APPROVE(102001),
	CROWDFUNDING_PAUSE(102002),
	CROWDFUNDING_RECOVER(102003),
    CROWDFUNDING_CASE_JUDGE(102004),

	// channel相关
	CHANNEL_EDIT(103001),

	// UTIL相关
	UTIL_MAIL_TASK_EDIT(104001),
//	WX_REPLY_EDIT(104002)


	;

	private int type;

	private OperationType(int value) {
		type = value;
	}

	public int value() {
		return type;
	}
}