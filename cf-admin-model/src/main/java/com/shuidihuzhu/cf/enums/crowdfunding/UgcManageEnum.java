package com.shuidihuzhu.cf.enums.crowdfunding;

import lombok.Getter;

/**
 * <AUTHOR>
 * @time 2019/11/1 下午5:27
 * @desc
 */
public enum UgcManageEnum {

    VERIFY_DEL(101, "删除证实"),
    VERIFY_SEE_ONESELF(102, "证实仅自己可见"),
    VERIFY_SHOW(103, "展示证实"),

    PROGRESS_DEL(201, "删除动态"),
    PROGRESS_SEE_ONESELF(202, "动态仅自己可见"),
    PROGRESS_SHOW(203, "展示动态"),
    PROGRESS_DEL_IMAGE(204, "删除动态图片"),
    PROGRESS_DEL_MASK_IMAGE(205, "删除动态图片(掩码后)"),

    ORDER_EDIT(301, "编辑订单留言"),
    ORDER_SEE_ONESELF(302, "订单仅自己可见"),
    ORDER_SHOW(303, "展示订单"),

    PROGRESS_COMMENT_DEL(401, "删除动态评论"),
    PROGRESS_COMMENT_SEE_ONESELF(402, "动态评论仅自己可见"),
    PROGRESS_COMMENT_SHOW(403, "展示动态评论"),

    ORDER_COMMENT_DEL(501, "删除订单评论"),
    ORDER_COMMENTSEE_ONESELF(502, "订单评论仅自己可见"),
    ORDER_COMMENT_SHOW(503, "展示订单评论"),

    IMAGE_DEL_IMAGE(601, "删除图片"),
    IMAGE_UPLOAD_IMAGE(602, "上传图片"),

    PINGYI_COMMENT_DEL(701, "删除评议评论"),
    PINGYI_COMMENTSEE_ONESELF(702, "评议评论仅自己可见"),
    PINGYI_COMMENT_SHOW(703, "展示评议评论"),

    ;

    @Getter
    private int key;

    @Getter
    private String vaule;

    private UgcManageEnum(int key, String vaule){
        this.key = key;
        this.vaule = vaule;
    }

    public static UgcManageEnum parse(int key){
        for (UgcManageEnum ugcManageEnum : UgcManageEnum.values()){
            if(key == ugcManageEnum.getKey()){
                return ugcManageEnum;
            }
        }
        return null;
    }
}
