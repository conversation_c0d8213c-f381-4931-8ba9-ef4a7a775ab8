package com.shuidihuzhu.cf.enums.InitialAudit;

import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: wangpeng
 * @Date: 2023/1/12 15:06
 * @Description:
 */
public enum TargetAmountAuditWorkOrderScene {
    FIRST(1, "首次"),
    REJECT(2, "驳回");


    private final static Map<Integer, TargetAmountAuditWorkOrderScene> map = Maps.newHashMap();

    static {
        map.putAll(Arrays.stream(TargetAmountAuditWorkOrderScene.values()).collect(Collectors.toMap(TargetAmountAuditWorkOrderScene::getType, Function.identity())));
    }

    @Getter
    private final int type;

    @Getter
    private final String msg;


    TargetAmountAuditWorkOrderScene(int type, String msg) {
        this.type = type;
        this.msg = msg;
    }


    public static List<Integer> getList(){
        return new ArrayList<>(map.keySet());
    }
}
