package com.shuidihuzhu.cf.enums.crowdfunding;

/**
 * Created by apple on 2017/12/1.
 */
public enum WorkOrderRoleEnum {
    //角色名称
    UGC_SENSITIVE_WORD_PROCESSORS(1001,"UGC敏感词处理员"),
    WORK_ORDER_REPORT(6,"举报处理人员"),

    //角色描述
    DISPOSE_SENSITIVE_WORD(2001,"处理ugc敏感词");


    private int index;
    private String name;

    WorkOrderRoleEnum(int index, String name) {
        this.index = index;
        this.name = name;
    }

    public int getIndex() {
        return index;
    }

    public String getName() {
        return name;
    }


}
