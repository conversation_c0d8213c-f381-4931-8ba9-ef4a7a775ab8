package com.shuidihuzhu.cf.enums.crowdfunding;

import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.Map;

public enum CfReportOfficialLetterStatusEnum {
    /**
     * 公函状态
     */
    NO_LETTER(1, "无需开函"),
    WAIT_LETTER(2, "待开函"),
    HAS_LETTER(3, "已开函"),
    SEND_LETTER(4, "发送中"),
    NOT_RECRIVED_LETTER(5, "开函未签收"),
    RECRIVED_LETTER(6, "开函已签收"),
    CONTACT(7, "开函已恢复联系"),
    REFUSED_RECRIVED(8, "开函拒签收"),
    OA_UNDER_REVIEW(9, "OA审批中")
    ;

    @Getter
    private int code;
    @Getter
    private String desc;

    private static Map<Integer, CfReportOfficialLetterStatusEnum> map = Maps.newHashMap();

    static {
        for (CfReportOfficialLetterStatusEnum value : CfReportOfficialLetterStatusEnum.values()) {
            map.put(value.code, value);
        }
    }

    CfReportOfficialLetterStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CfReportOfficialLetterStatusEnum getByCode(int code) {
        return map.get(code);
    }

}
