package com.shuidihuzhu.cf.enums.message;

/**
 * Created by ah<PERSON><PERSON> on 2017/5/18.
 */
public enum HostEnum {

    DEFAULT(0, ""),
    HUZHU(1, "https://www.shuidihuzhu.com"),
//    AIXINCHOU(1, "http://www.shuidihuzhu.net"),
    AIXINCHOU(2, "https://www.shuidichou.com"),
    SHUIDIBAO(4, "http://www.sdbao.com")
    ;

    private int key;
    private String value;

    HostEnum(int key, String value) {
        this.key = key;
        this.value = value;
    }

    public int getKey() {
        return this.key;
    }

    public String getValue() {
        return value;
    }

    public static String getValueByKey(int key) {
        for (HostEnum hostEnum : HostEnum.values()) {
            if (hostEnum.getKey() == key) {
                return hostEnum.getValue();
            }
        }
        return HostEnum.DEFAULT.getValue();
    }
}
