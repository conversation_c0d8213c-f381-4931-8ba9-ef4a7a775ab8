package com.shuidihuzhu.cf.enums.approve;

import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoDataStatusTypeEnum;
import lombok.Getter;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2019-03-31  15:25
 */
public enum ApproveSubItemEnum {

    /**
     * 基本信息-文章
     *
     * boolean {@link com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoDataStatusTypeEnum#BASE_INFO_SUBMIT}
     * = {@link #ARTICLE} && {@link #IMAGE};
     * 文章 + 图片 全部通过才算'基本信息'通过
     */
    ARTICLE(11, CrowdfundingInfoDataStatusTypeEnum.BASE_INFO_SUBMIT),

    /**
     * 基本信息-图片
     */
    IMAGE(12, CrowdfundingInfoDataStatusTypeEnum.BASE_INFO_SUBMIT),
    ;

    @Getter
    private final int value;

    /**
     * 子项对应的主项
     */
    @Getter
    private final CrowdfundingInfoDataStatusTypeEnum main;

    ApproveSubItemEnum(int value, CrowdfundingInfoDataStatusTypeEnum main) {
        this.value = value;
        this.main = main;
    }

    public static Set<ApproveSubItemEnum> getByMain(CrowdfundingInfoDataStatusTypeEnum main) {
        Set<ApproveSubItemEnum> r = Sets.newHashSet();
        for(ApproveSubItemEnum v : ApproveSubItemEnum.values()){
            if (v.getMain() == main) {
                r.add(v);
            }
        }
        return r;
    }

    public static ApproveSubItemEnum parse(int v) {
        for (ApproveSubItemEnum e : ApproveSubItemEnum.values()) {
            if (e.getValue() == v) {
                return e;
            }
        }
        throw new IllegalArgumentException("ApproveSubItemEnum 找不到对应枚举" + v);
    }
}
