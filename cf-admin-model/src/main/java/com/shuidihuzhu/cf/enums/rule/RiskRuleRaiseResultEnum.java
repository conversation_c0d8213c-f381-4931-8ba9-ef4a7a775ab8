package com.shuidihuzhu.cf.enums.rule;

import lombok.Getter;

/**
 * <AUTHOR>
 * @DATE 2020/1/2
 */
@Getter
public enum RiskRuleRaiseResultEnum {

    A_0(0,"可发起"),
    A_1(1,"未来花费金额"),
    A_2(2,"家庭金融资产总价值"),
    A_3(3,"目标金额"),
    A_4(4,"房产总价值"),

    A_7(7,"车产价值"),
    A_8(8,"房产数量"),
    A_9(9,"目标金额"),
    A_10(10,"家庭欠款"),
    A_11(11,"家庭年收入"),
    A_12(12,"剩余款项"),


    r_1(100,"前置报备和增信不一致"),
    r_2(101," 前置信息字段不全"),

    /**
     * 以下类型为：需要进一步询问
     */
    C_1(200, "需进一步询问“房产价值”、“房产数量”、“变卖状况”、“卖房资金金额”、“剩余卖房资金金额”，并比较剩余卖房资金与未来花费的大小"),
    C_2(201, "需进一步询问“房产价值”、“房产数量”、“变卖状况”、“房产目前分配状态”"),
    C_3(202, "需进一步询问“房产价值”、“房产数量”、“房产目前分配状态”"),
    C_4(203, "需进一步询问“车产数量”、“车产总价值”、“车辆品牌”、“车产目前分配状态”"),
    C_5(204, "需进一步询问“人身险状态”、“赔付状态”、“理赔资金金额”、“目前理赔资金剩余金额”，并比较剩余理赔资金与未来花费的大小"),
    C_6(205, "需进一步询问“事故状态”、“赔付状态”、“赔付资金金额”、“目前赔付资金剩余金额”，并比较剩余赔付资金与未来花费的大小")

    ;

    private int code;

    private String msg;

    RiskRuleRaiseResultEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
