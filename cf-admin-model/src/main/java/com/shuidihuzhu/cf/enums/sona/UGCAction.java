package com.shuidihuzhu.cf.enums.sona;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2018-08-23  15:02
 * @see com.shuidihuzhu.cf.model.crowdfunding.AdminTaskUgc
 */
public enum UGCAction {

    DEFAULT(0),

    /**
     * 发起触发的图文审核
     */
    RAISE(1),

    /**
     * 修改触发的图文审核
     */
    MODIFY(2)
    ;

    @Getter
    private int value;

    UGCAction (int value) {
        this.value = value;
    }

    public static UGCAction parse(int v){
        for (UGCAction action : UGCAction.values()) {
            if (action.value == v) {
                return action;
            }
        }
        throw new IllegalArgumentException("找不到对应 Action");
    }

}
