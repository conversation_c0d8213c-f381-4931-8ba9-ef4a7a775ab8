package com.shuidihuzhu.cf.enums.admin;

import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Maps;

import java.util.Map;
import java.util.Set;

/**
 * @Description: 图文修改渠道枚举
 * @Author: pangh<PERSON>ui
 * @Date: 2022/3/10 11:19 上午
 */
public class TwModifyChannelEnum {

    public enum Channel {

        API_USER_MODIFY(1, "C端用户自主修改"),
        ;

        private int value;
        private String message;

        Channel(int value, String message) {
            this.value = value;
            this.message = message;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public int getValue() {
            return value;
        }

        public void setValue(int value) {
            this.value = value;
        }

    }

    public enum Flag {

        USER_MODIFY_FLAG(1, "用户已修改图文"),
        ;

        private int value;
        private String message;

        Flag(int value, String message) {
            this.value = value;
            this.message = message;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public int getValue() {
            return value;
        }

        public void setValue(int value) {
            this.value = value;
        }

    }

}
