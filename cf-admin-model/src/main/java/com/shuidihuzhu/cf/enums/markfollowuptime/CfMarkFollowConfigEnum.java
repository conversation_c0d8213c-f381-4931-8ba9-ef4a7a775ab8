package com.shuidihuzhu.cf.enums.markfollowuptime;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.enums.admin.ApproveListOrderByWhat;
import lombok.Getter;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/8/16  11:12 上午
 */
public enum CfMarkFollowConfigEnum {

    CASE_ID("caseId"),
    WORK_ORDER_ID("workOrderId"),

    ;

    @Getter
    private String name;

    CfMarkFollowConfigEnum(String name) {
        this.name = name;
    }

    private static Map<String, CfMarkFollowConfigEnum> MAP = Maps.newHashMapWithExpectedSize(CfMarkFollowConfigEnum.values().length);

    static {
        for (CfMarkFollowConfigEnum cfMarkFollowConfigEnum : CfMarkFollowConfigEnum.values()) {
            MAP.put(cfMarkFollowConfigEnum.getName(), cfMarkFollowConfigEnum);
        }
    }

    public static CfMarkFollowConfigEnum getByName(String name) {
        return MAP.get(name);
    }

}
