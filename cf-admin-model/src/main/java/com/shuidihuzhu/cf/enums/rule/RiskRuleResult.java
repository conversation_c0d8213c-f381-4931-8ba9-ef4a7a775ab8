package com.shuidihuzhu.cf.enums.rule;

import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2020/1/2
 */
@AllArgsConstructor
@Getter
public enum RiskRuleResult {

    /**
     * 可发起
     */
    A_0(0,"家庭经济情况初步系统预判较合理"),

    /**
     * 以下类型为：不可发起因素[0, 199]
     */
    A_1(1,"未来花费金额"),
    A_2(2,"家庭金融资产总价值"),
    A_3(3,"目标金额"),
    A_4(4,"房产总价值"),
    A_7(7,"车产价值"),
    A_8(8,"房产数量"),
    A_9(9,"目标金额"),
    A_10(10,"家庭欠款"),
    A_11(11,"家庭年收入"),
    A_12(12,"剩余款项"),
    A_13(13, "变卖状态"),
    A_14(14, "变卖资金"),
    A_15(15, "居住状态"),
    A_16(16, "车产数量"),
    A_17(17, "车辆品牌"),
    A_18(18, "使用状态"),
    A_19(19, "人身险状态"),
    A_20(20, "赔付状态"),
    A_21(21, "理赔资金"),
    A_22(22, "事故状态"),
    A_23(23,"变卖中房产数量"),
    A_24(24,"居住分配情况"),

    /**
     * 以下类型为：前置条件不足[100, 101]
     */
    r_1(100,"前置报备和增信不一致"),
    r_2(101," 前置信息字段不全"),

    /**
     * 以下类型为：需要进一步询问[200, 399]
     */
    C_0(200, "需进一步询问“房产总价值”、“房产数量”、“变卖状况”、“卖房资金金额”、“剩余卖房资金金额”，并比较剩余卖房资金与未来花费金额的大小"),
    C_1(201, "需进一步询问“房产总价值”、“房产数量”、“变卖状况”、“房产目前分配状态”"),
    C_2(202, "需进一步询问“房产总价值”、“房产数量”、“房产目前分配状态”"),
    C_3(203, "需进一步询问“车产数量”、“车产价值”、“车辆品牌”、“车产目前分配状态”"),
    C_4(204, "需进一步询问“人身险状态”、“赔付状态”、“理赔资金金额”、“目前理赔资金剩余金额”，并比较剩余理赔资金与未来花费金额的大小"),
    C_5(205, "需进一步询问“事故状态”、“赔付状态”、“赔付资金金额”、“目前赔付资金剩余金额”，并比较剩余赔付资金与未来花费金额的大小"),

    /**
     * 以下类型为手动调用小工具，可发，但是有提示的枚举定义[400, 599]
     */
    A_A_0(400, "需说明变卖成功后若款项可覆盖后续花费，筹款愿退回赠与人"),
    A_A_1(401, "需补充说明家庭房产居住分配情况、房产变卖情况及资金剩余情况"),
    A_A_2(402, "需补充说明家庭房产居住分配情况"),
    A_A_3(403, "需补充说明车辆品牌及车产使用情况"),
    A_A_4(404, "需补充说明保险不可赔付情况"),
    A_A_5(405, "需补充说明若后续保险赔付后可覆盖后续花费，筹款愿退回赠与人"),
    A_A_6(406, "需补充说明保险赔付情况及保险理赔款目前使用情况"),
    A_A_7(407, "需补充说明若后续第三方或保险的不可赔偿情况"),
    A_A_8(408, "需补充说明若后续有第三方或保险赔偿后可覆盖后续花费，筹款愿退回赠与人"),
    A_A_9(409, "需补充说明第三方或保险赔偿情况及赔偿金使用情况"),
    A_A_10(410, "需补充说明家庭房产居住分配情况，并说明变卖成功后若款项可覆盖后续花费，筹款愿退回赠与人")

    ;

    public static Set<RiskRuleResult> caleValidResult(List<RiskRuleResult> ruleResults) {
        HashSet<RiskRuleResult> resultSet = new LinkedHashSet<>(ruleResults);
        if (ruleResults.size() > 1) {
            resultSet.remove(A_0);
        }
        if (ruleResults.size() == 0) {
            resultSet.add(A_0);
        }
        return resultSet;
    }

    public static List<Integer> removeResultType(List<Integer> ruleResults, RiskRuleResultTypeEnum typeEnum) {
        return ruleResults.stream().filter(integer -> !hasResultByType(List.of(integer), Sets.newHashSet(typeEnum)))
                .collect(Collectors.toList());
    }

    public static boolean hasResultByType(Collection<Integer> ruleResults, Set<RiskRuleResultTypeEnum> typeEnums){
        return ruleResults.stream().anyMatch(integer -> {
            if (typeEnums.contains(RiskRuleResultTypeEnum.ALLOW_LAUNCH) && isAllowLaunch(integer)) {
                return true;
            }
            if (typeEnums.contains(RiskRuleResultTypeEnum.ALLOW_AND_ADD_LAUNCH) && isAllowLaunchAttach(integer)) {
                return true;
            }
            if (typeEnums.contains(RiskRuleResultTypeEnum.FURTHER_INQUIRY) && isAskFurther(integer)) {
                return true;
            }
            if (typeEnums.contains(RiskRuleResultTypeEnum.NOT_ALLOW_LAUNCH) && isNotAllowLaunch(integer)) {
                return true;
            }
            return false;
        });
    }

    public static boolean isNotAllowLaunch(Integer resultCode) {
        return resultCode >=1 && resultCode <= 199 && !List.of(100, 101).contains(resultCode);
    }

    public static boolean isAskFurther(Integer resultCode) {
        return resultCode >=200 && resultCode <= 399;
    }

    public static boolean isAllowLaunch(Integer resultCode) {
        return resultCode == 0;
    }

    public static boolean isAllowLaunchAttach(Integer resultCode) {
        return resultCode >=400 && resultCode <= 599;
    }

    public static String fromCode(Integer code){
        RiskRuleResult riskRuleResult = enumMap.get(code);
        if (riskRuleResult == null) {
            return "";
        }
        return riskRuleResult.getMsg();
    }


    private int code;
    private String msg;

    private static final Map<Integer, RiskRuleResult> enumMap = Arrays.stream(values())
            .collect(Collectors.toMap(RiskRuleResult::getCode, Function.identity()));
}
