package com.shuidihuzhu.cf.enums.crowdfunding;

/**
 * <AUTHOR>
 * @date 2018/07/11
 */
public enum AdminCfProgressEnum {
    NEED_AUDIT(0, "需要审核"),
    AUDIT_PASS(1, "审核通过"),
    AUDIT_REJECTED(2, "审核后被拒绝")
    ;
    private int status;
    private String description;

    AdminCfProgressEnum(int status, String description) {
        this.status = status;
        this.description = description;
    }

    public int getStatus() {
        return status;
    }

    public String getDescription() {
        return description;
    }

    public static String getDescription(int status) {
        for (AdminFundUseAuditStatusType adminFundUseAuditStatusType : AdminFundUseAuditStatusType.values()) {
            if (adminFundUseAuditStatusType.getType() == status) {
                return adminFundUseAuditStatusType.getDescription();
            }
        }
        return  "无法得知目前进展状态";
    }
}
