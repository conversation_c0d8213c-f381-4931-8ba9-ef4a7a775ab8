package com.shuidihuzhu.cf.enums.crowdfunding;

/**
 * Created by ni<PERSON><PERSON><PERSON><PERSON> on 2018/2/2.
 */
public enum CfFirstCallOutMsg {

    // 这里的description 只是为了log， 现在短信模版是可配置的。

    NO(0,  ""),
    //主号、小程序、外围号发起的，有关注主号   转发次数 < 5
    //有关注【水滴筹】主号（所有渠道）转发次数 < 5
    ATTENTION_SHARE_COUNT_LESS(526,  "526duanxin"),
    //主号、小程序、外围号发起的，有关注主号   5 < 转发次数  < 20
    //关注【水滴筹】主号（所有渠道）5 < 转发次数  < 20
    ATTENTION_SHARE_COUNT_MIDDLE(527,  "527duanxin"),
    //主号、小程序、外围号发起的，有关注主号   转发次数 > 20
    //关注【水滴筹】主号（所有渠道）转发次数 > 20
    ATTENTION_SHARE_COUNT_MORE(528,  "528duanxin"),
    //主号、小程序、外围号发起的，未关注主号   转发次数 < 5
    //外围号、小程序、app发起的，没有关注【水滴筹】主号  转发次数 < 5
    NO_ATTENTION_SHARE_COUNT_LESS(529,  "PKPKOS1560407343"),
    //主号、小程序、外围号发起的，未关注主号   5 < 转发次数  < 20
    //外围号、小程序、app发起的，没有关注【水滴筹】主号  5 < 转发次数  < 20
    NO_ATTENTION_SHARE_COUNT_MIDDLE(530,  "530duanxin"),
    //主号、小程序、外围号发起的，未关注主号   转发次数 > 20
    //外围号、小程序、app发起的，没有关注【水滴筹】主号  转发次数 > 20
    NO_ATTENTION_SHARE_COUNT_MORE(531,  "DJNEIB1560407747"),
    //	APP发起的  转发次数 < 5
    //除外围号和app发起以外，没有关注【水滴筹】主号的用户  转发次数 < 5
    APP_SHARE_COUNT_LESS(532,  "FUWIQV1560408078"),
    //	APP发起的  5 < 转发次数
    //除外围号和app发起以外，没有关注【水滴筹】主号的用户 5 < 转发次数
    APP_SHARE_COUNT_MIDDLE_MORE(533, "RRDPQB1560346225"),
    ;


    CfFirstCallOutMsg(int sbType, String modelNum) {
        this.sbType = sbType;
        this.modelNum = modelNum;
    }

    private int sbType;
    private String modelNum;

    public int getSbType() {
        return sbType;
    }


    public String getModelNum() {
        return modelNum;
    }}
