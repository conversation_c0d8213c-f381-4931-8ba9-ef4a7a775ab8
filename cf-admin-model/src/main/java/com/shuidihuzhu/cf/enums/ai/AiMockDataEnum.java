package com.shuidihuzhu.cf.enums.ai;

import lombok.Getter;

/**
 * @Description:
 * @Author: pan<PERSON><PERSON><PERSON>
 * @Date: 2024/5/31 3:03 PM
 */
public enum AiMockDataEnum {

    DEFAULT(0, new String[]{}),
    PATIENT_NAME_MOCK(1, new String[]{"龢龘龗"}),
    RAISER_NAME_MOCK(2, new String[]{"龖骉齺"}),
    ADDRESS_MOCK(3, new String[]{"吉林省长春市幸福乡南部家园"}),
    AGE_MOCK(4, new String[]{}),
    ;

    @Getter
    private final int code;
    @Getter
    private final String[] mockData;

    AiMockDataEnum(int code, String[] mockData) {
        this.code = code;
        this.mockData = mockData;
    }


}
