package com.shuidihuzhu.cf.enums.record;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/9/1  4:13 下午
 */
public enum RecordOperateTypeEnum {

    IMAGE_DEL_IMAGE(101, "删除图片"),


    ;

    @Getter
    private int code;

    @Getter
    private String desc;

    RecordOperateTypeEnum(int code, String desc){
        this.code = code;
        this.desc = desc;
    }

    public static RecordOperateTypeEnum parse(int code){
        for (RecordOperateTypeEnum recordOperateTypeEnum : RecordOperateTypeEnum.values()){
            if(code == recordOperateTypeEnum.getCode()){
                return recordOperateTypeEnum;
            }
        }
        return null;
    }

}
