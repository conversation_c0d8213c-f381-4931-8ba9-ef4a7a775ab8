package com.shuidihuzhu.cf.enums.crowdfunding;

import lombok.Getter;

/**
 * <AUTHOR>
 * @time 2019/12/16 上午11:07
 * @desc
 */
public enum CfReportInfoSourceEnum {
    DEFAULT(0, "默认"),
    HEAR(1, "听说"),
    EVIDENCE(2, "可以拿出证据"),
    SEE_OWN_EYE(3, "亲眼所见"),
    OTHER(4, "其他"),
    ;

    @Getter
    private int key;
    @Getter
    private String value;

    CfReportInfoSourceEnum(int key, String value){
        this.key = key;
        this.value = value;
    }

    public static CfReportInfoSourceEnum parse(int key){
        for (CfReportInfoSourceEnum infoSourceEnum : CfReportInfoSourceEnum.values()){
            if(infoSourceEnum.getKey() == key){
                return infoSourceEnum;
            }
        }
        return null;
    }
}
