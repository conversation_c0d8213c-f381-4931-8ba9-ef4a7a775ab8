package com.shuidihuzhu.cf.enums.approve;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

/**
 * <AUTHOR>
 */
public enum ApproveControlHandleStatusEnum {

    /**
     * 处理中
     */
    @ApiModelProperty("处理中")
    DOING(1),

    @ApiModelProperty("失效")
    INVALID(2),

    ;

    @Getter
    private final int value;

    ApproveControlHandleStatusEnum(int value) {
        this.value = value;
    }

    public static ApproveControlHandleStatusEnum parse(int value){
        for (ApproveControlHandleStatusEnum e : ApproveControlHandleStatusEnum.values()) {
            if (value == e.getValue()) {
                return e;
            }
        }
        throw new IllegalArgumentException("can not find ApproveControlHandleStatusEnum value: " + value);
    }
}
