package com.shuidihuzhu.cf.enums.crowdfunding;

/**
 * Created by wangsf on 17/4/13.
 */
public enum CfTagGroupTypeEnum {

	ALL(0, "对所有案例类型都有"),
	ILLNESS(1, "仅对大病"),
	DREAM(2, "仅对梦想")

	;

	private int value;
	private String description;

	CfTagGroupTypeEnum(int value, String description) {
		this.value = value;
		this.description = description;
	}

	public int getValue() {
		return value;
	}

	public String getDescription() {
		return description;
	}
}
