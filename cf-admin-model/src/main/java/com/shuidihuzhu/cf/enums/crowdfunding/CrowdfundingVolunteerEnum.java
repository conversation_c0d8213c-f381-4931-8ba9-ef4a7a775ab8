package com.shuidihuzhu.cf.enums.crowdfunding;

import lombok.Getter;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/2/27.
 */
public class CrowdfundingVolunteerEnum {
    public enum volunteerType {

        TEAM(1, "线下团队"),
        SERVE_ADMIN(2, "服务站长"),
        PART_TIME(3, "兼职"),
        WATER_DROP_VOLUNTEER(4, ""),
        SERIOUS_ILLNESS_SALVAGE_ANGEL(5, "大病救助天使");

        private int value;
        private String desc;

        volunteerType(int value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public static volunteerType parse(int v){
            for (volunteerType e : volunteerType.values()) {
                if (e.getValue() == v ) {
                    return e;
                }
            }
            throw new IllegalArgumentException("can't find value of RoleEnum");
        }

        public int getValue() {
            return value;
        }

        public void setValue(int value) {
            this.value = value;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }
    }

    public enum workStatusEnum {
        DIMISSIOM(1, "离职"),
        ON_THE_JOB(2, "在职")
        ;
        private int value;
        private String desc;

        workStatusEnum(int value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public int getValue() {
            return value;
        }

        public void setValue(int value) {
            this.value = value;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }
    }

    public enum isGenerate {
        GENERATE(1, "未生成"),
        NOT_GENERATE(2, "已生成");
        private int value;
        private String desc;

        isGenerate(int value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public int getValue() {
            return value;
        }

        public void setValue(int value) {
            this.value = value;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }
    }
    /**
     * @author: wanghui
     * @create: 2019/8/8 4:59 PM
     */
    public enum VolunteerLevelEnum {
        AREA_LEADER(1, "区域经理"),
        PROVINCE_LEADER(2, "省级经理"),
        COMMON_LEADER(3, "普通人"),
        SUPER_LEADER(5, "超级管理员"),
        ;
        @Getter
        private Integer level;
        @Getter
        private String desc;

        VolunteerLevelEnum(int level, String desc) {
            this.level = level;
            this.desc = desc;
        }

        public static VolunteerLevelEnum parse(int level) {
            for (VolunteerLevelEnum volunteerLevelEnum : values()) {
                if (volunteerLevelEnum.level == level) {
                    return volunteerLevelEnum;
                }
            }
            return null;
        }
    }
    /**
     * 志愿者 在职状态
     */
    public enum AccountTypeEnum {
        INDIVIDUAL(0, "个人账号"),
        UNITARY_ACCOUNT(1, "统一账号")
        ;
        private int value;
        private String desc;

        AccountTypeEnum(int value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public int getValue() {
            return value;
        }

        public void setValue(int value) {
            this.value = value;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }
        public static AccountTypeEnum parse(int value) {
            for (AccountTypeEnum accountTypeEnum : values()) {
                if (accountTypeEnum.value == value) {
                    return accountTypeEnum;
                }
            }
            return null;
        }
    }

}
