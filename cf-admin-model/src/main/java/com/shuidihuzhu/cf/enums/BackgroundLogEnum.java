package com.shuidihuzhu.cf.enums;

/**
 * Created by ahrievil on 2017/1/18.
 */
public enum BackgroundLogEnum {

    /**
     * 0-通过、1-让TA去修改、2-暂停筹款、3-初始化状态、4-备注、5-标记举报、6-更改来源、7-修改文章、8-校验银行卡、9-发送短信、
     * 10-添加tag标签、11-获取第一次接触、12-梦想转大病、13-大病转梦想、14-呼通、15-未呼通、16-呼通回访
     *  17-没有处理, 18-延后处理, 19-不再处理, 20-举报处理中, 21-举报处理完成, 22-举报不用处理, 23-停止打款 24-开启打款
     *  25-梦想筹初审未过, 26-梦想筹初审通过, 27-延后电话联系, 28-撤销延后处理, 29-案例审核
     *
     */

    PASS(0, "通过"),
    REFUSE(1, "驳回"),
    OVER(2, "结束"),
    INITIALIZATION(3, "初始化"),
    REMARK(4, "备注"),
    REPORT(5, "举报"),
    EDIT_FROM(6, "更改来源"),
    EDIT_CONTENT(7, "修改文章"),
    VERIFY_BANK_CARD(8, "校验银行卡"),
    SEND_MESSAGE(9, "发送短信"),
    ADD_CASE_TAG(10, "添加tag标签"),
    ADD_UPDATE_CONTACT(11, "获取第一次接触"),
    CHANGE_DREAM_TO_SERIOUS_ILLNESS(12, "梦想转大病"),
    CHANGE_SERIOUS_ILLNESS_TO_DREAM(13, "大病转梦想"),
    CALL_OUT_SUCCESS(14, "呼通"),
    CALL_OUT_FAIL(15, "未呼通"),
    CALL_OUT_CALL_BACK(16, "呼通回访"),
    NOT_PROCESSED(17, "没有处理"),
    DEFERRED_PROCESSING(18, "延后审核"),
    NO_MORE_PROCESSING(19, "不再处理"),
    HANDLEING(20, "举报处理中"),
    FINISH(21, "举报处理完成"),
    DISREGARD(22, "举报不用处理"),
    STOP_DRAW_CASH(23, "停止打款"),
    RECOVER_DRAW_CASH(24, "开启打款"),
    DREAM_FIRST_JUDGE_REFUSE(25, "梦想筹初审未过"),
    DREAM_FIRST_JUDGE_PASS(26, "梦想筹初审通过"),
    DEFERRED_CONTACT(27, "延后电话联系"),
    DEFERRED_RECOVER(28, "撤销不再处理"),
    CASE_APPROVE(29, "案例审核"),
    CREDIT_SUPPLEMENT_REFUSE(30,"增信审核未通过"),
    CREDIT_SUPPLEMENT_PASS(31,"增信审核通过"),
    FLOW_ORDER_REMARK(32, "工单处理意见"),
    INITIAL_AUDIT_HANDLE(33, "初次审核"),

    CASE_RECOVER(34, "【恢复筹款状态】"),
    CASE_SPECIAL_RECOVER(35, "【特殊恢复筹款】"),
    IMAGE_CONTENT_HANDLE(36, "图文审核"),

    ;

    private Integer code;
    private String message;

    BackgroundLogEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static BackgroundLogEnum getByValue (int code) {
        BackgroundLogEnum[] values = BackgroundLogEnum.values();
        for (BackgroundLogEnum value : values) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return REMARK;
    }

}
