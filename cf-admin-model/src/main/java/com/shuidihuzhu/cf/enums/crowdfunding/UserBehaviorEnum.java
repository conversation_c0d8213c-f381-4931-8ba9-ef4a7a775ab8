package com.shuidihuzhu.cf.enums.crowdfunding;

import lombok.Getter;

/**
 * <AUTHOR>
 * @time 2019/10/11 下午4:47
 * @desc
 */
@Getter
public enum UserBehaviorEnum {
    /**
     *
     */
    DEFAULT(0, "全部标签", true, "admin-user-center:behavior-all"),
    SUBSCRIBE(1, "关注行为", true, "admin-user-center:behavior-subscribe"),
    REGISTER(2, "注册行为", true, "admin-user-center:behavior-register"),
    REGISTER_CLUE(3, "登记线索", true, "admin-user-center:behavior-registerClue"),
    REGISTER_DRAFT(4, "登记草稿", true, "admin-user-center:behavior-draft"),
    ORDER(5, "捐款", true, "admin-user-center:behavior-order"),
    SHARE(6, "转发", true, "admin-user-center:behavior-share"),
    REPORT(7, "举报", true, "admin-user-center:behavior-report"),
    PROGRESS(8, "发布动态", true, "admin-user-center:behavior-progress"),
    PROGRESS_COMMENT(9, "发布动态评论", true, "admin-user-center:behavior-progressComment"),
    VERIFY(10, "发布证实", true, "admin-user-center:behavior-verify"),
    ORDER_COMMENT(11, "发布订单留言", true, "admin-user-center:behavior-orderComment"),
    USER_CALL(12, "用户主动进电", true, "admin-user-center:behavior-callIn"),
    PLATFORM_CALL(13, "平台主动外呼", true, "admin-user-center:behavior-callOut"),
    ONLINE_CONSULE(14, "用户在线咨询", true, "admin-user-center:behavior-onlineConsule"),
    SMS_RECORD(15, "短信记录", false, "permission"),
    TWEET_READ(16, "推文阅读记录", false, "permission"),
    MSG_SEND(17, "发送消息记录", false, "permission"),
    HELP_PURCHASE(18, "互助购买记录", false, "permission"),
    INSURANCE_PURCHASE(19, "水滴保购买记录", false, "permission"),
    RAISE(20, "发起案例", false, "permission"),
    SEND_SMS_RECORD(21, "发送短信记录", true, "admin-user-center:behavior-smsRecord"),

    BLESSING_RECORD(22, "加油", true, "admin-user-center:behavior-blessingRecord" ),

    ;

    private final boolean enable;
    private final String permission;
    private final int key;
    private final String value;

    UserBehaviorEnum(int key, String value, boolean enable, String permission){
        this.key = key;
        this.value = value;
        this.enable = enable;
        this.permission = permission;
    }

    public static UserBehaviorEnum parse(int key){
        for (UserBehaviorEnum behaviorEnum : UserBehaviorEnum.values()){
            if(key == behaviorEnum.getKey()){
                return behaviorEnum;
            }
        }

        return UserBehaviorEnum.DEFAULT;
    }
}
