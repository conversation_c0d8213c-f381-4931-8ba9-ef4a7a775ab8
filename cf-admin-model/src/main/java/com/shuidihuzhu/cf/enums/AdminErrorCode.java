package com.shuidihuzhu.cf.enums;

import com.shuidihuzhu.cf.client.base.enums.IBaseErrorCode;
import com.shuidihuzhu.common.web.enums.MyErrorCode;

public enum AdminErrorCode implements MyErrorCode , IBaseErrorCode {

    SUCCESS(0, "success"),
    // 100XXX:系统
    SYSTEM_PARAM_ERROR(100001, "参数错误"),
    SYSTEM_PARAM_JSON_PARSE_ERROR(100002, "JSON解析错误"),
    SYSTEM_STREAM_READ_ERROR(100003, "读取文件内容错误"),
    SYSTEM_PARAM_IS_NULL(100004, "参数为空"),
    SYSTEM_TASK_IS_RUNNING(100005, "任务正在执行，请稍后操作"),
    SYSTEM_FILE_FORMAT_ERROR(100007, "文件格式错误"),
    SYSTEM_OPERATION_FAILED(100008, "操作失败"),
    SYSTEM_UNRECOGNIZED_ERROR(100201, "未识别错误"),
    SYSTEM_ERROR(100301, "系统错误"),
    SYSTEM_NOT_LOGIN_ERROR(100302, "未登录"),
    SYSTEM_LOGIN_ERROR(100303, "用户名或密码错误"),
    SYSTEM_NO_PCODE_PRIVILEGE(100304, "无接口权限"),
    SYSTEM_NO_ID(100305, "ID不存在"),
    SYSTEM_TOKEN_ERROR(100306, "token错误"),
    SYSTEM_FE_VERSION_ERROR(100307, "__version错误"),
    SYSTEM_FE_VERSION_NOT_EXISTS(100308, "表中没有FE_VERSION的记录"),
    SYSTEM_LOGIN_ADMIN_PASSWORD_ERROR(100309, "管理员密码错误"),
    WX_MESSAGE_TEMPLATE_ERROR(100310, "微信消息模板错误"),
    SYSTEM_FILE_NAME_NOT_FIND(100311, "请更改为正确的文件名"),
    SYSTEM_FILE_DATA_ERROR(100312, "文件有误"),
    SYSTEM_PARAM_TOO_LONG(100313, "参数过长"),
    MOBILE_FORMAT_ERROR(100314, "手机号格式错误"),
    THE_OPERATION_IS_NOT_EXECUTED(100315, "该操作没有执行"),
    PLEASE_ENTER_THE_REASONS_FOR_THE_OPERATION(100316, "请输入操作理由"),
    SYSTEM_NOT_PRIVILEGE(100317, "没有操作权限"),
    SYSTEM_PARAM_ERROR_VALID_DATA_RANGE(100317, "字段不在有效的范围内"),
    SYSTEM_RELEASING_WAIT_RETRY(100318, "系统发布中，请稍后重试"),
    SYSTEM_FUNCTION_OFFLINE(100319, "该功能已下线，请到风控管理-风控动作查询中操作"),
    FAIL_TO_GET_LOCK(100320, "有相同操作正在执行中，请稍后再操作"),
    HOSPITAL_AUDIT_USER_SUBMIT_ERROR(100321, "”用户提交时间”查询, 开始时间与结束时间的间隔,不能超过7天"),
    DAY_RANGE_ERROR(100322, "时间范围超过一周，请重新选择"),
    DAY_FORMAT_ERROR(100323, "时间格式错误"),
    END_DAY_ERROR(100324, "结束时间不能大于今天"),
    END_BEFORE_BEGIN_ERROR(100325, "结束时间不能大于今天"),
    DAY_RANGE_MONTH_ERROR(100326, "时间范围超过一个月，请重新选择"),
    PAGE_INFO_ERROR(100327, "分页数据错误"),
    USER_REAL_INFO_ERROR(100328, "身份信息不全"),
    USER_REAL_INFO_FAIL(100329, "身份实名认证失败"),
    FINANCE_FEIGN_ERROR(100330, "资金数据获取失败，请重试"),
    REPORT_SYSTEM_REDIS_LOCK_ERROR(100331, "操作过快,不能连续重复标记举报"),
    CLEW_TASK_ERROR(100332, "线索任务信息错误"),
    CLEW_TASK_LINK_ERROR(100333, "该线索任务已经进行了初审录入"),
    CASE_END_ERROR(100334,"案例已经结束，请返回刷新"),
    CRONTAB_ERROR(100335,"cron表达式不正确"),
    ORDER_LIMIT_ERROR(100336,"案例被“限制预审通过”策略命中，无法生成预审工单，请联系风控解锁后方可再次操作"),
    NON_UPDATE_ERROR(100337,"未更新"),


    // 110XXX:二维码管理
    CHANNEL_EXISTS(110001, "channel号已经存在"),
    CHANNEL_NOT_EXISTS(110002, "channel号不存在"),

    // 120XXX:用户信息管理
    USER_INFO_THE_SAME_MOBILE(120001, "新手机号不能与旧手机号相同"),
    USER_MOBILE_NOT_EXISTS(120002, "该手机号不存在"),
    NO_ORDER_SELECTED(120003, "没有选择任何保单"),
    NO_SUCH_PERSON(120004, "查无此人"),
    USER_ALREADY_EXISTS(120005, "此用户已存在"),
    USER_ACCOUNT_NOT_EXISTS(120107, "该用户不存在"),
    MOBILE_PHONE_USER_NOT_MATCH(120108, "注册手机号和userId不匹配"),
    AUTHOR_PAYEE_NOT_EQUALS(120109, "收款人与患者不一致，请修改收款人信息"),

    // 130XXX: sea后台权限管理
    ADMIN_ACCOUNT_EXISTS(130001, "账户名已存在"),
    ADMIN_ACCOUNT_TYPE_ENUM_ERROR(130002, "账户类型不正确"),
    ADMIN_ACCOUNT_STATUS_ENUM_ERROR(130003, "账户状态不正确"),
    ADMIN_ACCOUNT_NOT_EXISTS(130004, "账户不存在"),
    ADMIN_ACCOUNT_NOT_WORKING(130005, "账户无效"),

    //140XXX: case操作管理
    ADMIN_CASE_ORIGIN_HAS_BEEN_MARKED(140001, "案例来源已被系统自动标注"),
    ADMIN_TAG_EXISTS(140002, "案例标签已存在"),
    ADMIN_IN_ADD_TRUST(140003, "存在待审核的补充证明，请先审核"),
    ADD_TRUST_STATUS_ERROR(140004, "未提交信息"),
    ADMIN_IN_HOSPITAL_AUDIT(140005, "案例医院核实信息进行中"),
    AUTHOR_NO_EXIST_ERROR(140006, "患者信息未提交"),
    ADMIN_IN_HOSPITAL(140007, "案例医院核实信息未提交"),
    ADMIN_CASE_DIFF(140008, "增信信息和前置报备信息不一致"),
    ADMIN_CASE_NO(140009, "不存在增信信息和前置报备信息"),
    ADMIN_NO_STRATEGY(140010, "策略不存在"),
    ADMIN_NO_MATERIAL(140011, "前置信息字段不全"),

    //150XXX: sql任务
    ADMIN_MAIL_NOT_EXISTS(150001, "任务不存在"),
    NOT_ONLINE(150002, "测试环境不允许被执行"),

    //160XXX: redis操作
    CF_REDIS_KEY_EXISTS(160001, "该键值已存在"),
    CF_REDIS_KEY_NOT_EXIST(160002, "该键值不存在"),

    //170XXX: 水滴筹业务
    CF_NOT_FOUND(170001, "不存在对应的筹款"),
    CF_DREAM_NOT_FOUND(170001, "不存在对应的梦想筹案例"),
    CF_INFO_TYPE_ERROR(170002, "筹款类型有误"),
    CF_INFO_ERROR_DRAW_IS_NOT_BUILD(170003, "提现任务没有生成"),
    CF_INFO_ERROR_DRAW_IS_BUILD(170004, "提现任务已经生成"),
    CF_INFO_ERROR_DRAW_IS_LAUNCH(170005, "提现任务已经发起"),
    CF_INFO_ORDER_INVALID(170006, "无效的订单"),
    CF_INFO_ORDER_REFUND_FAILED(170007, "退款失败"),
    CF_INFO_ORDER_REFUND_IS_LAUNCH(170008, "退款已经发起"),
    CF_INFO_DRAW_HAVE_APPLY(170009, "提现已经申请"),
    CF_INFO_DRAW_NOT_APPLY(170010, "提现没有申请"),
    CF_INFO_DRAW_APPLY_NOT_APPROVE(170011, "提现申请还没有审核通过"),
    CF_INFO_DRAW_NOT_FAILED(170012, "提现状态还没有失败"),
    CF_CRAWING_CASH(170012, "该筹款已经打款或被正在打款中"),
    CF_ORDER_NOT_FOUND(170013, "该筹款无捐款"),
    CF_ORDER_REFOUND(170014, "已经退款"),
    CF_REFUND_FLOW(170015, "该筹款已经进入退款流程"),
    CF_DRAWCASH_FLOW(170016, "该筹款已经进入提现流程"),
    CF_INFO_REFUND_NOT_APPLY(170017, "用户没有申请退款"),
    CF_INFO_DRAW_HAVE_PAUSE(170018, "提现已经被暂停"),
    CF_DRAW_NOT_FOUND(170019, "不存在对应的提现数据"),
    CF_DRAW_MANUAL_HAS_SUBMIT(170020, "人工打款申请已经提交"),
    CF_DRAW_MANUAL_MATERIAL_ERROR(170021, "申请材料不齐全"),
    CF_DRAW_INNER_AMOUNT_ERROR(170022, "金额必须大于0并小于50000000"),
    CF_DRAW_ERROR(170023, "提现失败"),
    CF_DRAW_BILLING_STATUS_ERROR(170024, "对账失败"),
    CF_NOT_NEED_APPROVE(170025, "该案例不需要审核"),
    CF_STATUS_CHANGE_ERROR(170026, "状态转移失败"),
    CF_HOSPITAL_AUDIT_IS_NULL(170027, "未发现医院核实信息"),
    CF_HOSPITAL_AUDIT_IS_PASS(170028, "医院核实信息已通过"),
    IDCARD_VERIFY_FAILED(170029, "身份证校验失败"),
    BACKCARD_VERIFY_FAILED(170030, "银行卡校验失败"),
    CF_HOSPITAL_AUDIT_IS_DUPLICATE(170031, "医院重复下发"),
    CF_IS_OFFLINE(170032, "此案例已被标为随筹随取案例"),
    CF_IS_DRAW_HOSPITAL(170033, "此案例为对公打款案例"),
    CF_APPROVE_STATUS_IS_SPECIAL(170034, "案例处于特殊状态或该案例驳回次数大于7"),
    CF_SURPLUS_AMOUNT_NOT_ENOUGH(170035, "案例余额不足"),
    CF_EXCEED_TIMES(170036, "超出操作次数"),
    INPUT_FORMAT_ERROR(170037, "请检查输入的数据格式"),
    CF_REFUND_LIMIT(170038, "退款限制"),
    CF_STATUS_NOT_UNIFY(170040, "当前进展无法进行该操作"),
    CF_PROGRESS_NOT_FOUND(170041, "不存在当前进展"),
    CF_DRAW_CASH_APPROVE_APPLY_FAIL_FOR_RISK(170042, "该案例数据异常，已进入异常案例列表"),
    CF_NO_NEED_VERIFY(170043, "无需认证身份证号"),
    CF_NO_PRE_APPROVE_MATERIAL(170044, "不存在初审资料"),
    CF_BASE_INFO_TEMPLATE_CONTENT_ERROR(170045, "智能发起文章填写有误"),
    CF_LAUNCH_PAYEE_VALID_FAIL(170046, "打款时收款人信息校验失败"),
    CF_LAUNCH_INDIVIDUAL_TYPE_MISMATCH(170047, "打款时对个人打款类型不匹配"),
    CF_LAUNCH_CASE_PAYEE_MISMATCH(170048, "打款时提现收款人与案例收款人不匹配"),
    CF_LAUNCH_PAYEE_MISMATCH(170049, "打款时提现收款人与收款人信息不匹配"),
    CF_LAUNCH_UPDATE_PAYEE_MISMATCH(170050, "打款时提现收款人与最新的收款人信息不匹配"),
    CF_LAUNCH_IHOSPITAL_TYPE_MISMATCH(170051, "打款时对公打款类型不匹配"),
    CF_LAUNCH_IHOSPITAL_PAYEE_MISMATCH(170052, "打款时对公收款人信息不匹配"),
    CF_LAUNCH_CHARITY_PAYEE_MISMATCH(170053, "打款时对公益收款人类型不匹配"),
    CF_ONLY_ORIGIN_PUBLIC_TO_PERSONAL(170046,"只有原来是对公打款才可以修改打款为个人"),
    CF_ONLY_ORIGIN_PUBLIC_TO_NO_PUBLIC(170047,"仅支持修改到患者本人和非本人"),
    CF_NO_REFUND_TASK(170048,"没有匹配的退款任务"),
    CF_UPDATE_TRANSFER_FAIL(170049,"更新打款记录的状态失败"),
    CF_REFUND_AMOUNT_ERROR(170050,"退款金额不符"),
    CF_REFUND_NO_USER_THIRD(170051,"未关注任何公众号，无法打款"),
    CF_REPORT_STATUS_ERROR(170052,"举报状态不是处理中"),
    CF_REPORT_WORK_STATUS_ERROR(170053,"举报工单状态不是达成一致"),
    CF_VOLUNTEER_IDENTITYREPEAT_ERROR(170054,"已经有此人员，请勿重复添加"),
    CF_VOLUNTEER_EMAILREPEAT_ERROR(170055,"此邮箱已经被其他在职的线下人员使用，请确认邮箱后再添加"),
    CF_HOSPITAL_AUDIT_IS_CANCEL(170056, "医院核实已撤销，无需再审核"),
    CF_HOSPITAL_AUDIT_WORK_ORDER_NOT_FINISH(170057, "医院核实工单未处理完成，不允许再次下发。"),
    CF_CONTENT_IMAGE_STATUS_VALID_FAIL(170058, "图文混排状态不对，禁止操作"),
    CF_INITIAL_AUDIT_VALID_FAIL(170059, "初审未通过，不允许进行编辑图文混排"),
    CF_REPORT_ACTION_CLASSIFY_ERROR(170060, "该动作分类下存在启用状态的处理动作，不能弃用"),
    CF_REPORT_PROBLEM_CLASSIFY_ERROR(170061, "存在启用的模块，不可弃用分类"),
    CF_REPORT_PROBLEM_MODULE_ERROR(170062, "存在启用的问题，不可弃用模块"),
    CF_DISEASE_TYPE_NOT_NULL(170063, "材料类型不能为空"),
    OPERATION_TOO_FAST(170064, "操作过快"),
    CF_VOLUNTEER_QRCODESTATUS_ERROR(170065,"个人帐号设置为离职时,必须将二维码状态设置为冻结"),
    BIRTH_CARD_LIMIT_FAIL(170066,"出生证最多输入50位"),


    //180XXX:工单业务
    NO(180001, "no"),
    ROLE_NOT_EXISTS(180002, "角色不存在"),
    ROLE_NAME_ALREADY_EXISTS(180003, "角色名已存在"),
    USER_HAVE_PERMISSION(180004, "用户已拥有该角色"),
    PERMISSION_EXISTS(180005, "角色已拥有该工单任务权限"),
    WORK_ORDER_NOT_FOUND(180006, "工单不存在"),
    NO_ACCESS_TO_GET_WORK_ORDER(180007, "没有权限获取工单"),
    NO_MORE_WORK_ORDER(180008, "没有更多的工单"),
    ERROR_TASK_CODE(180009, "任务类型错误"),
    PLEASE_FINISH_THE_REST_OF_THE_WORK_LIST_FIRST(180010, "请先完成剩余的工单"),
    ONE_GET_WORK_ORDER_MOST_IS_TEN(180011, "每次最多领取10条工单"),
    ERROR_RESULT(180012, "处理结果错误"),
    CASE_WORK_ORDER_EXIST(180013, "工单已存在"),
    OPERATION_FAILED(180014, "操作失败"),
    NO_MORE_TYPE_WORK_ORDER(180015, "没有更多该类型工单"),
    GET_MISSION_TOO_FAST(180016, "工单领取中，请稍后再试"),
    EXIST_ORDER_NO_HANDLE(180017, "请处理完已领工单"),
    REPORT_WORK_ORDER_CANNOT_HANDLE(180018, "不能处理已被其他人领取的工单"),
    REPORT_WORK_ORDER_STATUS_HANDLE(180019, "举报跟进中才能进行该操作"),
    WORK_ORDER_NOT_RECEIVE(180020, "当前工单未被领取"),
    WORK_ORDER_NOT_PERSONNEL(180021, "未查询到相关举报工单处理人员"),
    OPERATE_PERSONNEL_NOT_MATCH(180022, "操作与人员类型不匹配"),
    REPORT_PERSONNEL_NOT_MATCH(180023, "不允许查询同类型的举报处理人员"),
    GET_WORK_ORDER_FAILED(180024, "获取工单失败"),
    CASE_MSG_EXIST(180025, "记录已存在"),
    CASE_MSG_NO_CHANGE(180026, "记录不能修改"),
    CASE_MSG_NO_PROPERTY(180027, "记录信息不完整"),
    CASE_MSG_ERROR_TYPE(180028, "发起人和患者不能是同一人"),
    IDCARD_FAILED_PATIENT(180029, "患者姓名和身份证不匹配"),
    IDCARD_ERROR_PATIENT(180030, "患者姓名或身份证错误"),
    IDCARD_ERROR_INITIATOR(180031, "发起人姓名或身份证错误"),
    IDCARD_FAILED_INITIATOR(180032, "发起人姓名和身份证不匹配"),
    ORDER_HAS_AUTO_CLOSE(180033, "存在已自动异常关闭的工单，请重新操作"),
    REPORT_NOT_EXIST(180034, "不存在对应的举报信息"),
    REPORT_ANSWER_PAGE_ERROR(180035, "保存举报答案类型错误"),
    REPORT_RELATION_TYPE_ERROR(180036, "与患者的关系类型或者信息来源为空"),
    REPORT_COMMUNICATER_REPEAT_ERROR(180036, "同一个举报或案例不能重复添加联系人"),
    REPORT_ANSWER_EMPTY_ERROR(180037, "答案不能为空"),
    REPORT_ANSWER_DETAIL_EMPTY_ERROR(180038, "答案条目详情不能为空"),
    REPORT_ANSWER_TYPE_DIGTAL_ERROR(180039, "答案只能为数字"),
    REPORT_ANSWER_MUST_ERROR(180040, "请填写必填项"),
    REPORT_RECORD_ERROR(180041, "举报记录错误"),
    REPORT_HANDLE_RESULT_ERROR(180042, "举报处理结果状态错误"),
    REPORT_REACH_AGREE_ERROR(180043, "工单状态已变成达成一致"),
    REPORT_NO_HANDLE_ERROR(180044, "存在未沟通的质疑人举报"),
    REPORT_REACH_OVER_ERROR(180045, "本案例的举报工单状态均达成一致或处理完成"),
    REPORT_END_DEAL_AGREE_ERROR(180046, "请先和筹款方达成一致再结束处理"),
    REPORT_ALL_END_DEAL_ERROR(180047, "举报工单均已处理完成"),
    REPORT_CONTACT_NOT_UPDATE(180048, "非手动添加的联系人不能操作"),
    REPORT_WORK_ORDER_NOT_UPGRADE(180049,"仅一线人员可操作升级二线工单"),
    REPORT_EXIST_NOT_FINISH(180050,"该案例存在未处理完的二线工单"),
    ALREADY_DOME(180051,"工单已经处理完成，请返回列表"),
    WORK_ORDER_REPROCESS_FAIL_ON_CASE_END(180052,"该案例目前已停止筹款，请先恢复筹款再操作。"),
    REPORT_WORK_ORDER_NOT_LOST(180053,"失联库员工不可操作转入失联工单"),
    REPORT_LOST_EXIST_NOT_FINISH(180054,"该案例存在未处理完的失联工单"),
    REPORT_LOST_NOT_MODIFY(180055,"无法将失联工单置为达成一致或无需处理"),
    NOT_ONLINE_FAIL(180056,"您不是在线状态，无法领取工单"),
    BATCH_HANDLE_WORK_ORDER_ERROR(180057,"请单独审核头图工单"),
    BATCH_HANDLE_STATE_WORK_ORDER_ERROR(180058,"请单独审核社群动态工单"),
    DISEASE_NAME_EXITS_ERROR(180059,"患者疾病在疾病库范围内"),
    DISEASE_NAME_CONTAIN_SPECIAL_DISEASE_ERROR(180060,"患者疾病不包含特殊可发"),
    CAILIAO_WORK_ORDER_LIMIT_CONTENT_WORK_ORDER(180061,"存在待审核的图文审核工单，材审环节暂不允许提交图文信息的审核"),
    ORDER_HAS_CALLBACK(180062,"该工单已经回收，无法提交"),
    REPORT_NO_ASSIGN_ORDER_IGNORE_END_DEAL(180063, "未分配举报工单，不允许结束处理"),
    CONTENT_TEMPLATE_NO_DISEASE(180064, "未包含疾病名称"),
    REPORT_TYPE_ERROR(180065, "请确认举报类型"),



    HOSPITAL_AUDIT_PASS_NEED_APPROVE_CASE(181001, "医院核实通过，请审核材料审核"),
    HOSPITAL_AUDIT_HAS_AUTO_CLOSE(181002, "工单已自动异常关闭"),

    APPROVE_CONTROL_HAS_SOME_ONE_DOING(182001, "有人在处理中"),
    APPROVE_CONTROL_CASE_HAS_PASSED(182002, "该材料已经审核通过无法修改"),
    APPROVE_CONTROL_SUBMIT_LOCK_HAS_LOST(182003, "案例状态已更新，请刷新后重试。"),

    WORK_FLOW_CAN_NOT_ASSIGN(181012, "系统将自动分配工单，请勿手动领取"),

    EMPTY_TOPIC_TITLE(190001, "请填写话题标题"),
    EMPTY_TOPIC_DESCRIPTION(190002, "请填写话题内容"),
    EMPTY_TOPIC_IMGURL(190003, "请填写话题头图"),
    EMPTY_TOPIC_KEYWORD(190004, "请填写话题关键词"),
    EMPTY_PHASE_DATE(190005, "请选择发布日期"),
    EMPTY_PHASE_TOPIC_ID(190006, "请选择发布话题"),

    //200XXX:志愿者管理
    ADD_FAILED(200001, "添加失败"),
    UPDATE_FAILURE(200002, "修改失败"),
    NO_QR_CODE(200003, "该员工没有生成二维码"),
    QR_CODE(200004, "该员工已生成过二维码"),
    GENERATE_FAILED(200005, "二维码生成失败"),
    SMS_SEND_FAILED(200006, "短信发送失败"),
    NOT_CASE_DATA(200007, "没有发起案例数据"),
    NOT_VOLUNTEER_DATA(200008, "没有筹款顾问数据"),
    NOT_CHOOSE_VOLUNTEERTYPE(200009,"没有选择筹款顾问类型"),
    NOT_CONNECT(200010,"连接失败，请稍后重试"),

    //300XXX:分组推文
    PUSH_WX_ARTICLE_ID(300001, "Id要大于0"),
    PUSH_WX_ARTICLE_NO_TASK(300002, "父任务不存在或者被删除"),
    PUSH_WX_ARTICLE_TASK_NO_UPDATE(300003, "修改父任务失败,该任务状态不支持修改"),
    PUSH_WX_ARTICLE_HAS_SEND(300004, "删除、修改父任务失败,该任务不支持修改或删除,已经有子任务发送过了或者有定时任务存在"),
    PUSH_WX_ARTICLE_TASK_REPETITION(300005, "创建任务失败,参数错误或者同公众号下已有同名任务"),
    PUSH_WX_ARTICLE_EXPIRES_TASK(300006, "父任务不存在或者超出有效期"),
    PUSH_WX_ARTICLE_NO_SEND_DATA_RECORD(300007, "发送任务数据回调不存在对应发送信息"),
    PUSH_WX_ARTICLE_FILE_NAME_ERROR(300008, "用户文件名错误"),

    PUSH_WX_ARTICLE_SUBTASK_NO_TASK(300501, "父任务不存在或已被删除"),
    PUSH_WX_ARTICLE_TASK_LOSE_EFFICACY(300502, "父任务的有效时间已过,不允许创建新、修改、发送子任务"),
    PUSH_WX_ARTICLE_NO_SUBTASK(300503, "子任务不可除或已被删除"),
    PUSH_WX_ARTICLE_SUBTASK_JSON_FAILED(300504, "sqlId的JSON格式化失败"),
    PUSH_WX_ARTICLE_NO_SQL(300505, "sql的信息被删除了或者不存在"),
    PUSH_WX_ARTICLE_ON_CONFORMITY(300506, "父任务与子任务的关联信息不一致"),
    PUSH_WX_ARTICLE_SUBTASK_REPETITION(300507, "创建子任务失败,参数错误或者父任务下已有同名任务"),
    PUSH_WX_ARTICLE_SUBTASK_NO_UPDATE(300508, "修改任务失败,该任务状态不支持修改"),
    PUSH_WX_ARTICLE_SUBTASK_KILLED_EXCEPTION(300509, "终止子任务失败,该任务状态不可被终止"),
    PUSH_WX_ARTICLE_SUBTASK_REVOCATION_EXCEPTION(300510, "撤销定时子任务失败,该任务状态不可被终止"),
    PUSH_WX_ARTICLE_SUBTASK_UPDATE_ALREADY(300511, "任务已经被更新或者更新条件不符合"),
    PUSH_WX_ARTICLE_SUBTASK_WXTEMPATEID_ALREADY(300512, "该子任务人群包中的分组或人群已存在，尝试更换分组或人群包！"),
    PUSH_WX_ARTICLE_SUBTASK_UPDATE_TASK_ERROR(300513,"不可修改已创建子任务的任务"),
    DRAWCASH_FINANCIAL_FAILED_DG_NOT_SUCCESS(400101, "对公打款未成功"),
    DRAWCASH_FINANCIAL_FAILED_NOT_DG(400102, "当前不是对公打款"),

    //500xxx:信息流转工单问题分类设置
    ROOT_CLASSIFY_SETTINGS_CANNT_DEL(50001, "一级目录下有二级目录不能直接删除"),
    CLASSIFY_SETTINGS_NOT_EXISTS(50002, "工单分类设置不存在"),
    CLASSIFY_SETTINGS_HAS_MODIFY(50003, "工单分类设置数据已被修改，请刷新页面在试试"),
    CANOT_ASSIGN_FLOW_ORDER(50004, "没有工单可供领取"),
    ALLOT_WITHOUT_OPERATERID(50005, "工单分配至少选择到组织"),

    //600xxx组织管理
    EMPLOYEE_EXIST_IN_ORGANIZATION(60001, "员工已在组织内"),
    ORGANIZATION_HAS_SUB_ORGANIZATION(60002, "组织下有下级组织，请删除下级组织再删除"),
    ORGANIZATION_HAS_EMPLOYEES(60003, "组织下员工，请删除员工再删除"),
    EMPLOYEE_IS_ORGANIZATION_MANAGER(60004, "员工已是组内管理员"),
    EXIST_SAME_ORGANIZATION_NAME(60005, "该组织已存在，请检查后重新填写。"),


    // 700xxx 举报业务
    NO_CANCEL(700001, "补充证明已提交或已审核，无法撤回"),
    REPORT_SCHEDULE_TARGET_TIME_PASSED(700002, "跟进时间已过。"),



    CF_INFO_ERROR_STATUS_INVALID(800113, "筹款信息状态有误"),
    CF_BANK_CARD_ALREADY_VERIFIED(800125, "银行卡已校验通过"),
    CF_PARAM_ERROR_PAYEE(800108, "收款人信息不全"),

    // 敏感词检查
    SENSITIVE_CHECK_OK(810001, "敏感内容检查通过"),

    // 点击不再处理检查结果
    NO_LONGER_PROCESS_CHECK_AMOUNT_NOT_EMPTY(820001, "当前余额不为0，不可点击“不再处理”"),
    NO_LONGER_PROCESS_CHECK_CASE_NOT_END(820001, "案例未结束，不可点击“不再处理”"),

    OPERATE_MATERIAL_MUST_INITIAL_AUDIT(810002, "初审未通过, 不可操作材料审核"),
    CAN_NOT_REPROCESS(810003, "不符合重新审核条件"),

    EXIST_SAME_CONTENT(810004, "标签名已存在"),
    APPROVE_PASS_REFUSE(810005, "单一材料驳回和通过不能同时进行"),
    APPROVE_REFUSE_PAYEE(810006, "已下发修改收款人通道，不允许驳回收款人信息"),
    SECOND_LABELS_EXIST_EABLE(810010, "该一级标签有正在启用的二级标签，不能弃用"),
    FIRST_LABLES_REPEAT(810011, "一级标签名称不能重复，请重新输入"),
    SECOND_LABLES_REPEAT(810012, "二级标签不能重复，请重新输入"),
    CONCURRENT_MODIFY_CASE_LABELS(810013, "当前案例标签已更新，请刷新后重试"),

    CANNOT_DELETE(810005, "不能弃用或删除"),
    CASE_NOT_ALLOW_INITIAL(820001, "案例不能被初始化，请刷新页面"),
    SUPPLY_STATUS_NOT_MATCH(820002, "下发状态不符合"),

    WORK_FLOW_FINISHED(820003, "该工单已处理完成"),

    NEED_HAVE_USE_SCENE(820002, "需要有使用场景"),
    USER_HAS_SUBMIT(820004, "用户已开始提交材料,不能帮代修改"),
    OPERATOR_HAS_NOT_MODIFY(820005, "当前没有代修改过材料项"),

    HAS_SDB_ORDER(830000, ""),
    USER_NOT_REGISTER_SDC(830001, "转入手机号未注册水滴筹，无法转入资产"),


    WORK_ORDER_EXCEPTION_CLOSE(900001, "该工单已自动异常关闭"),

    SYSTEM_NOT_EXIST_RECORD_ERROR(900002,"记录不存在"),

    DISEASE_CAN_NOT_REPEATED(900003,"疾病不可重复添加"),

    ;

    private int code;

    private String msg;

    AdminErrorCode(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }

}
