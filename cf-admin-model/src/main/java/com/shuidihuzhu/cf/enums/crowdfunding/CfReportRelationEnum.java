package com.shuidihuzhu.cf.enums.crowdfunding;

import lombok.Getter;

/**
 * <AUTHOR>
 * @time 2019/12/16 上午11:02
 * @desc
 */
public enum CfReportRelationEnum {
    /**
     * 质疑方时的关系枚举
     */
    QUESTIONER(101, "质疑人"),
    RELATIVES(102, "亲属"),
    NEIGHBOR(103, "邻居"),
    FRIEND(104, "朋友"),
    CLASSMATE(105, "同学"),

    /**
     * 筹款方时的关系枚举
     */
    RAISER(201, "发起人"),
    PAYEE(202, "收款人"),
    AUTHOR(203, "患者"),
    OFFLINE_BD(204, "线下BD"),

    OTHER(305, "其他"),
    ;

    @Getter
    private int key;
    @Getter
    private String value;

    CfReportRelationEnum(int key, String value){
        this.key = key;
        this.value = value;
    }

    public static CfReportRelationEnum parse(int key){
        for (CfReportRelationEnum reportRelationEnum : CfReportRelationEnum.values()){
            if(reportRelationEnum.getKey() == key){
                return reportRelationEnum;
            }
        }
        return null;
    }
}
