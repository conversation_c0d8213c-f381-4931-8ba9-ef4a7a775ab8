package com.shuidihuzhu.cf.enums.record;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/9/1  4:13 下午
 */
public enum RecordPageTypeEnum {

    UGC_MANAGE(1, "ugc管理"),
    HIGH_RISK_WORK_ORDER(2, "高风险工单"),
    SECOND_REVIEW_WORK_ORDER(3, "二次审核工单"),
    IMAGE_TEXT_REVIEW_WORK_ORDER(4, "图文审核工单"),
    PICTURE_ENTRY_WORK_ORDER(5, "图片录入工单"),
    MEDICAL_REVIEW_WORK_ORDER(6, "医疗审核工单"),
    SUPPLEMENTARY_HOSPITAL_INFORMATION(7, "补充医院信息"),
    EXAMINATION_PASSED(8, "初审通过"),

    ;

    @Getter
    private int code;

    @Getter
    private String desc;

    RecordPageTypeEnum(int code, String desc){
        this.code = code;
        this.desc = desc;
    }

    public static RecordPageTypeEnum parse(int code){
        for (RecordPageTypeEnum recordPageTypeEnum : RecordPageTypeEnum.values()){
            if(code == recordPageTypeEnum.getCode()){
                return recordPageTypeEnum;
            }
        }
        return null;
    }

}
