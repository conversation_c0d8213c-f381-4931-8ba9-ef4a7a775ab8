package com.shuidihuzhu.cf.enums;

import lombok.Getter;

import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-07-27 11:05
 **/
public enum ReportFollowActionEnum {

    REPORT_FOLLOW_ACTION_PROMPT(0, ""),

    CALL(1, "拨打电话"),
    FOLLOW(2, "点击新建跟进or跟进记录"),
    DEAL(3, "筹款方、质疑人列表”已处理“操作"),
    COMMENT(4, "举报详情页：评论"),
    REACH_AGREE(5, "达成一致"),
    DEAL_WITH_LATER(6, "稍后处理");

    @Getter
    private int code;
    @Getter
    private String desc;

    public static List<Integer> promptAction = List.of(CALL.getCode(), FOLLOW.getCode(), DEAL.getCode(), COMMENT.getCode());

    public static ReportFollowActionEnum getByCode(int code) {
        ReportFollowActionEnum[] values = ReportFollowActionEnum.values();
        for (ReportFollowActionEnum value : values) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }

    ReportFollowActionEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
