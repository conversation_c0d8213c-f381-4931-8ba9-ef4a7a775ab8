package com.shuidihuzhu.cf.enums.crowdfunding;

/**
 * Created by ahrievil on 2017/2/7.
 */
public enum CfOperationRecordEnum {
    /**
     * 0-通过、1-让TA去修改、2-暂停筹款、3-初始化状态、4-备注、5-标记举报、6-更改来源、7-修改文章、8-校验银行卡、9-发送短信、
     * 10-添加tag标签、11-获取第一次接触、12-梦想转大病、13-大病转梦想、14-外呼成功、15-外呼失败、16-电话回访, 17-没有处理，
     * 18-延后处理, 19-不再处理, 20-举报处理中, 21-举报处理完成, 22-举报不用处理, 23-停止打款 24-开启打款 25-梦想筹初审未过
     * 26-梦想筹初审通过, 27-延后电话联系, 28-撤销延后处理, 29-案例审核   33-信息流转工单的处理意见
     * 34-相似的举报工单  35-运营修改用户举报的举报类型   36-修改初审的驳回项
     */
    PASS(0),
    RETURN_TO_MODIFY(1),
    PAUSE(2),
    INITIALIZATION(3),
    REMARK(4),
    REPORT(5),
    EDIT_FROM(6),
    EDIT_CONTENT(7),
    VERIFY_BANK_CARD(8),
    SEND_MESSAGE(9),
    ADD_CASE_TAG(10),
    ADD_UPDATE_CONTACT(11),
    CHANGE_DREAM_TO_SERIOUS_ILLNESS(12),
    CHANGE_SERIOUS_ILLNESS_TO_DREAM(13),
    CALL_OUT_SUCCESS(14),
    CALL_OUT_FAIL(15),
    CALL_OUT_CALL_BACK(16),
    NOT_PROCESSED(17),
    DEFERRED_PROCESSING(18),
    NO_MORE_PROCESSING(19),
    HANDLEING(20),
    FINISH(21),
    DISREGARD(22),
    STOP_DRAW_CASH(23),
    RECOVER_DRAW_CASH(24),
    DREAM_FIRST_JUDGE_REFUSE(25),
    DREAM_FIRST_JUDGE_PASS(26),
    DEFERRED_CONTACT(27),
    DEFERRED_RECOVER(28),
    CASE_APPROVE(29),

    /** 首审通过 */
    FIRST_APPROVE_SUCCESS(30),
    /** 首审失败 */
    FIRST_APPROVE_FAIL(31),

    /**
     * https://wiki.shuiditech.com/pages/viewpage.action?pageId=********
     * 处理数据规则检测异常案例
     */
    CASE_RISK_HANDLE(32),

    FLOW_ORDER_REMARK(33),

    SIMILAR_REPORT(34),

    CHANGE_REPORT_LABELS(35),

    MODIFY_INITIAL_AUDIT_REJECT_OPTION(36),
    ;

    private int value;
    
    CfOperationRecordEnum(int value) {
        this.value = value;
    }
    
    public CfOperatingRecordEnum.Type getNewType() {
    	switch (this) {
    	case NOT_PROCESSED:
    		return CfOperatingRecordEnum.Type.NO_HANDLE;
		case DEFERRED_PROCESSING:
			return CfOperatingRecordEnum.Type.DELAY_APPROVE;
		case NO_MORE_PROCESSING:
			return CfOperatingRecordEnum.Type.NO_MORE_HANDLE;
		case DEFERRED_CONTACT:
            return CfOperatingRecordEnum.Type.DELAY_CONTACT;
		default:
			return null;
		}
    }

    public CfOperationRecordEnum getByInfoStatus(CrowdfundingStatus crowdfundingStatus) {
        switch (crowdfundingStatus) {
            case APPROVE_DENIED:
                return CfOperationRecordEnum.RETURN_TO_MODIFY;
            case SUBMITTED:
                return CfOperationRecordEnum.PASS;
                default:
                return CfOperationRecordEnum.REMARK;

        }
    }

    public int value() {
        return value;
    }
}
