package com.shuidihuzhu.cf.enums.admin;

/**
 * <AUTHOR>
 */
public enum AdminCfInitialAuditCheckInfoEnum {
    HOSPITAL_TITLE(1, "抬头中医院"),
    HOSPITAL_OFFICIAL_SEAL(2, "公章处医院"),
    HOSPITAL_CONTENT(3, "正文中医院"),
    ;
    private int type;
    private String desc;

    AdminCfInitialAuditCheckInfoEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static String getDescByType(int type) {
        for (AdminCfInitialAuditCheckInfoEnum value : AdminCfInitialAuditCheckInfoEnum.values()) {
            if (value.getType() == type) {
                return value.getDesc();
            }
        }
        return null;
    }

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
