package com.shuidihuzhu.cf.enums.crowdfunding;

import lombok.Getter;

/**
 * <AUTHOR>
 * @time 2019/11/4 下午3:54
 * @desc
 */
public enum UgcBizType {
    BASE_INFO(1, "图文信息"),
    ORDER(2, "订单"),
    PROGRESS(3, "动态"),
    COMMENT_ORDER(4, "订单评论"),
    COMMENT_PROGRESS(5, "动态评论"),
    VERIFICATION(6, "证实"),
    SHOW_IMAGE(7, "展示图片"),
    UPLOAD_IMAGE(8, "上传图片"),
    PINGYI(9,"评议评论"),
    MASK_PROGRESS(10, "掩码动态"),
    ;

    @Getter
    private int key;
    @Getter
    private String value;

    UgcBizType(int key, String value){
        this.key = key;
        this.value = value;
    }

    public static UgcBizType parse(int key){

        for (UgcBizType ugcBizType : UgcBizType.values()){
            if(key == ugcBizType.getKey()){
                return ugcBizType;
            }
        }

        return null;

    }
}
