package com.shuidihuzhu.cf.enums.crowdfunding;

/**
 * Created by ah<PERSON><PERSON> on 2017/5/15.
 */
public enum CfCallOutConditionTypeEnum {

	DEFAULT(0, "未打电话"), CALL_OUT_SUCCESS(1, "呼通"), CALL_OUT_FAIL(2, "未呼通"), CALL_OUT_CALL_BACK(3, "呼通回访");
	CfCallOutConditionTypeEnum(int value, String message) {
		this.value = value;
		this.message = message;
	}

	private int value;
	private String message;

	public int getValue() {
		return this.value;
	}

	public String getMessage() {
		return this.message;
	}

	public CfOperatingRecordEnum.Type getType() {
		switch (this) {
		case CALL_OUT_SUCCESS:
			return CfOperatingRecordEnum.Type.OUTBOUND_SUCCESS;
		case CALL_OUT_FAIL:
			return CfOperatingRecordEnum.Type.OUTBOUND_FAILED;
		case CALL_OUT_CALL_BACK:
			return CfOperatingRecordEnum.Type.TELEPHONE_CALLBACK;
		default:
			return null;
		}
	}

	public static CfCallOutConditionTypeEnum getByValue(int code) {
		CfCallOutConditionTypeEnum[] values = CfCallOutConditionTypeEnum.values();
		for (CfCallOutConditionTypeEnum value : values) {
			if (value.getValue() == code) {
				return value;
			}
		}
		return DEFAULT;
	}
}
