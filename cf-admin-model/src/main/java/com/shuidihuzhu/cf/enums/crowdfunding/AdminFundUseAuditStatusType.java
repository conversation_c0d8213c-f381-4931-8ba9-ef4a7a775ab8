package com.shuidihuzhu.cf.enums.crowdfunding;

import com.google.common.collect.Maps;

import java.util.Map;

public enum  AdminFundUseAuditStatusType {
    SUCCESS(0, "审核中"),
    NEED_AUDIT(1, "审核通过"),
    AUDIT_REJECTED(2, "审核被拒绝")
    ;
    private int type;
    private String description;

    public int getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }

    AdminFundUseAuditStatusType(int type, String description) {
        this.type = type;
        this.description = description;
    }
    static Map<Integer, String> maps =  Maps.newHashMap();

    static {
        for (AdminFundUseAuditStatusType fundUseAuditStatusType : AdminFundUseAuditStatusType.values()) {
            maps.put(fundUseAuditStatusType.type, fundUseAuditStatusType.description);
        }
    }

    public static String getDescription(int type) {
        for (AdminFundUseAuditStatusType fundUseAuditStatusType : AdminFundUseAuditStatusType.values()) {
            if (fundUseAuditStatusType.getType() == type) {
                return  fundUseAuditStatusType.getDescription();
            }
        }
        return "报错";
    }
}
