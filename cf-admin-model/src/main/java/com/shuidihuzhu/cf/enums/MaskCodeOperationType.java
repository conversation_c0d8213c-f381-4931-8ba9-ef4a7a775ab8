package com.shuidihuzhu.cf.enums;

public enum MaskCodeOperationType {
    PAYEE(1, "收款人手机号"),
    RAISER(2, "发起人账户内手机号"),
    REPORTER(3, "举报人联系方式"),
    WAIHU_REGISTER(4, "查看外呼登记手机号"),
    FIRST_APPROVE_WAIHU(5, "查看前置外呼手机号"),
    SERVICE_1V1(6, "查看1V1服务手机号"),
    SERVICE_1V1_NEW_PHONE(7, "查看1V1服务新手机号"),
    SERVICE_1V1_WECHAR(8, "查看1V1服务微信"),
    COPY_1V1_SERVICE_DATE(9, "复制1V1服务日期+电话+疾病"),
    COPY_1V1_SERVICE_MOBILE(10, "复制1V1服务手机"),
    VOLUNTEER_RAISER_PHONE(11, "发起人手机"),
    OFFLINE_CLEW_PHONE(12, "查看线下线索池线索手机号"),
    INITIAL_VIEW_PATIENT_IDCARD(13, "初审-查看患者身份证号码"),
    WAIHU_GROUP_LEADER_CALL(14, "查看外呼组长呼叫列表手机号"),
    WAIHU_GROUP_LEADER_PENDING(15, "查看外呼组长待呼区手机号"),
    WAIHU_GROUP_LEADER_HANDLED(16, "查看外呼组长已处理列表手机号"),
    WAIHU_GROUP_LEADER_UNASSIGNED_CALL(17, "查看外呼组长待分配呼叫列表手机号"),

    USER_MANAGE_DETAIL_CLICK_IDENTITY(18, "查看用户管理详情页的身份证号"),

    SERVE_GROUP_LEADER_SERVE_LIST(19, "查看服务组长服务列表手机号"),
    SERVE_GROUP_LEADER_FINISHED_CASE(20, "查看服务组长已结束案例手机号"),
    QIANZHI_BAOBEI(21, "查看前置报备管理中发起人手机号"),

    ACTIVITY_ACCOUNT_IDCARD(22, "查看活动账户的身份证号"),
    ACTIVITY_ACCOUNT_BANKCARD(23, "查看活动账户的银行卡号"),

    BESIDES_QC_INFO(24, "查看外呼质检手机号"),

    PRE_POST_MOBILE(25, "代录入手机号"),

    VIEW_PATIENT_MOBILE_NUMBER(26,"查看患者手机号")
    ;

    private int code;
    private String operationTypeDesc;

    MaskCodeOperationType(int code, String operationTypeDesc) {
        this.code = code;
        this.operationTypeDesc = operationTypeDesc;
    }

    public String getOperationTypeDesc() {
        return this.operationTypeDesc;
    }
}
