package com.shuidihuzhu.cf.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @time 2019/8/16 上午11:35
 * @desc
 */
public enum ReportWorkOrderOpEnum {
    DOWN_GRAND_FRIST(1, "降级一线"),
    UP_GRADE_SECOND(2, "升级二线"),
    CHANGE_REPORT_TYPE(3, "修改举报类型"),
    CALL(4, "拨打"),
    LOOKUP(5, "查看"),
    DEAL_STATUS_CHANGE(6, "状态变更"),
    ;

    @Getter
    private int key;

    @Getter
    private String vaule;

    ReportWorkOrderOpEnum(int key, String vaule){
        this.key = key;
        this.vaule = vaule;
    }

    public static ReportWorkOrderOpEnum parse(int key){
        for (ReportWorkOrderOpEnum opEnum : ReportWorkOrderOpEnum.values()){
            if(key == opEnum.getKey()){
                return opEnum;
            }
        }
        return null;
    }
}
