package com.shuidihuzhu.cf.enums.admin;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminUGCTask;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingOperationEnum;

import java.util.Map;

/**
 * <AUTHOR>
 * @DATE 2018/7/31
 */
public enum UserCommentSourceEnum {

    UGC(1, "ugc来源的评论记录"),
    CASE_APPROVE(2, "材料审核来源的相关记录"),
    RISK_MOBILE_PHONE(3, "风控管理-发起手机号修改"),
    INITIAL_AUDIT(4, "初次审核操作");


    private int code;
    private String desc;

    UserCommentSourceEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


    public static UserCommentSourceEnum parse(int code){
        for (UserCommentSourceEnum sourceEnum : UserCommentSourceEnum.values()){
            if(sourceEnum.getCode() == code){
                return sourceEnum;
            }
        }
        return null;
    }

    public enum CommentType {

        UGC_0(0, "未知来源"),
        UGC_1(1, "ugc来源的动态处理"),
        UGC_2(2, "ugc来源的房产车产"),
        UGC_3(3, "ugc来源的内容"),
        UGC_4(4, "ugc来源的前置审核"),
        UGC_5(5, "ugc来源的图文"),
        UGC_6(6, "ugc来源的领取图文审核"),

        CASE_APPROVE_DEFAULT(10, "材料审核"),
        CASE_APPROVE_RECEIVE(11, "领取材料审核"),
        CASE_APPROVE_DEFERRED_APPROVE(12, "延迟审核处理"),
        CASE_APPROVE_DISREGARD(13, "该案例不再处理"),
        CASE_APPROVE_DEFERRED_CONTACT(14, "延迟电话联系"),
        CASE_APPROVE_PASS(15, "案例材料审核通过"),
        CASE_APPROVE_REJECT(16, "案例材料审核驳回"),

        MODIFY_REGISTER_MOBILE(20, "修改发起手机号"),

        // 初次审核的动作 begin
        INITIAL_AUDIT_PASS(21, "审核通过"),
        INITIAL_AUDIT_REJECT(22, "驳回"),
        INITIAL_AUDIT_RETURN_VISIT(23, "回访处理"),
        INITIAL_AUDIT_DELAY_HANDLE(24, "稍后处理"),
        INITIAL_AUDIT_END_CASE(25, "停止筹款"),
        INITIAL_AUDIT_EDIT(26, "修改"),
        REJECT_SHUTDOWN(27, "驳回工单-提交"),
        CHANGE_ORDER_LEVEL(28, "催单"),
        SEND_SMS(29, "短信"),
        INITIAL_SENCOND_VERIFY_IDCARD(30, "二次身份校验"),
        CHECK_INFO(31, "信息名称校验"),
        INITIAL_AUDIT_OCR_REPORT_ERROR(32, "图片识别结果报错"),
        INITIAL_AUDIT_TWICE_END_CASE(33, "二次停止筹款"),
        /**
         * ocr识别医疗材料中疾病名称&姓名一致性识别结果报错操作
         */
        HANDWRITTEN_MATERIALS(34, "手写材料"),
        USER_FILL_DISEASES_AND_MATERIALS_DIFFERENTIAL(35, "用户填写疾病与材料不一致"),
        PICTURE_NO_SPIN(36, "图片未旋转"),
        PICTURE_NO_DISTINCT(37, "图片不清晰"),
        /**
         * 图文信息中的“报错”操作
         */
        ERROR_WORD(38, "错别字/别称/别名/少字"),
        COVER_DISEASE_NAME_UNRECOGNIZED(39, "医疗材料中包含疾病名称但未识别出"),
        ORDER_REASON(40,"其他原因"),

        ADD_BLACK_LIST(41,"初审加入黑名单"),

        SUBMIT_VALUE_REMARK(42,"添加信息备注"),
        CLEAR_INITIAL_AUDIT_REJECT(43,"清空预审驳回项"),
        DELETE_BLACK_LIST(44,"初审解除黑名单"),

        // 初次审核的动作 end
        ;

        private static Map<Integer, CommentType> map = Maps.newHashMap();

        static {
            for (CommentType type : CommentType.values()) {
                map.put(type.getCode(), type);
            }
        }

        private int code;
        private String desc;

        CommentType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static CommentType getCommetTypefromCode(int code) {
            return map.get(code);
        }


        public static CommentType getCommetType(AdminUGCTask.Content content) {
            switch (content) {
                case BASE_INFO:
                    return UGC_5;

                case PROGRESS:
                    return UGC_1;

                case COMMENT_ORDER:
                case COMMENT_PROGRESS:
                case VERIFICATION:
                case ORDER:
                    return UGC_3;

                case FIRST_APPROVE:
                    return UGC_4;
                default:
                    return UGC_0;
            }
        }

        public static CommentType getCommetType(CrowdfundingOperationEnum crowdfundingOperationEnum) {
            switch (crowdfundingOperationEnum) {
                case DEFER_APPROVE:
                    return CASE_APPROVE_DEFERRED_APPROVE;

                case NEVER_PROCESSING:
                    return CASE_APPROVE_DISREGARD;

                case DEFER_CONTACT:
                    return CASE_APPROVE_DEFERRED_CONTACT;

                default:
                    return CASE_APPROVE_DEFAULT;
            }
        }

        public static String getByValue (int code) {
            UserCommentSourceEnum.CommentType[] values = UserCommentSourceEnum.CommentType.values();
            for (UserCommentSourceEnum.CommentType value : values) {
                if (value.getCode() == code) {
                    return value.desc;
                }
            }
            return ORDER_REASON.desc;
        }
    }
}
