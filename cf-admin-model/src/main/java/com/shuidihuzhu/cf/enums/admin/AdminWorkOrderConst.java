package com.shuidihuzhu.cf.enums.admin;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by Ahrievil on 2017/11/30
 */
public class AdminWorkOrderConst {

    public enum Type {

        NO(0, "默认"),
        UGC(1, "用户输入"),
        FLOW(2, "信息流转工单"),
        CASE(3, "案例"),
        CASE_APPROVE(4, "案例审核"),
        CASE_REPORT(5,"案例举报"),
        ;


        private int code;
        private String word;
        private static Map<Integer, Type> map = Maps.newHashMap();
        private static Set<Type> VALID_MAP;

        Type(int code, String word) {
            this.code = code;
            this.word = word;
        }

        static {
            for (Type type : Type.values()) {
                map.put(type.getCode(), type);
            }
            VALID_MAP = ImmutableSet.of(UGC, FLOW, CASE, CASE_APPROVE,CASE_REPORT);
        }

        public static Type getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            Type type = map.get(code);
            return type != null ? type : Type.NO;
        }

        public static boolean isValid(Type type) {
            return VALID_MAP.contains(type);
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getWord() {
            return word;
        }

        public void setWord(String word) {
            this.word = word;
        }

    }

    public enum Task {

        NO(0, "默认"),
        SENSITIVE_WORD(1, "敏感词处理"),
        FORBIDDEN_WORD(2, "禁止词处理"),
        OUT_BOUND(3, "外呼"),
        SECOND_LINE(4, "二线"),
        HUZHU(5, "互助"),
        SDB(6, "水滴保"),
        FIRST_CONTACT(7, "首次沟通"),
        INFO_BASE_WORD(8, "基础信息"),
        PROGRESS_WORD(9, "动态信息"),
        CASE_APPROVE(10, "案例标准审核"),
        FLOW_FIRST(11, "首次"),
        CASE_REPORT_DEAL(12,"案例举报处理"),
        FIRST_APPROVE(13,"前置审核"),

        /**
         * 敏感号码
         * - 身份证
         * - 手机号
         * - 微信号
         */
        SENSITIVE_NUMBER(14,"敏感号码"),

        /**
         * 敏感渠道
         * 针对特殊渠道发布的ugc内容 全量ugc工单生成
         */
        SENSITIVE_CHANNEL(15,"敏感渠道"),
        SENSITIVE_OTHERS(16,"UGC审核-其他类别"),

        /**
         * 特殊符号
         */
        SPECIAL_SYMBOL(17, "特殊符号"),
        ILLEGAL_SYMBOL(18, "非法字符"),
        UGC_USE_SCENE_FILTER(19, "使用场景为Ugc的地方"),
        EXP_VERIFY_MANY_TIMES(20, "异常多次证实"),

        AI_RISK(21, "算法模型"),
        ;

        private int code;
        private String word;
        private static Map<Integer, Task> map = Maps.newHashMap();
        private static Set<Task> VALID_MAP;

        /**
         * 给task分类 便于维护
         */
        public static final List<Task> UGC_TASKS = ImmutableList.of(SENSITIVE_WORD, FORBIDDEN_WORD, SENSITIVE_NUMBER,
                SENSITIVE_CHANNEL, SENSITIVE_OTHERS);
        public static final List<Integer> UGC_TASK_CODES = ImmutableList.copyOf(UGC_TASKS.stream().map(v -> v.getCode()).collect(Collectors.toList()));

        static {
            for (Task task : Task.values()) {
                map.put(task.getCode(), task);
            }
            VALID_MAP = ImmutableSet.of(SENSITIVE_WORD, FORBIDDEN_WORD, SENSITIVE_NUMBER, SENSITIVE_CHANNEL, SENSITIVE_OTHERS, OUT_BOUND
                    , SECOND_LINE, FIRST_CONTACT, INFO_BASE_WORD, PROGRESS_WORD, CASE_APPROVE, FLOW_FIRST,
                    CASE_REPORT_DEAL,FIRST_APPROVE);
        }

        public static Task getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            Task task = map.get(code);
            return  task != null ? task : Task.NO;
        }

        public static boolean isValid(Task task) {
            return VALID_MAP.contains(task);
        }

        public static boolean isUgcTasks(int task){
            return isUgcTasks(getByCode(task));
        }

        public static boolean isUgcTasks(Task task){
            for(Task t : UGC_TASKS) {
                if (task == t) {
                    return true;
                }
            }
            return false;
        }

        Task(int code, String word) {
            this.code = code;
            this.word = word;
        }

        public static Task getByRole(OperatorRole operatorRole) {
            switch (operatorRole) {
                case OUT_BOUND:
                    return Task.OUT_BOUND;
                case SECOND_LINE:
                case APPROVE:
                case REPORT:
                case CASH_HANDLE:
                case HOSPITAL_CHECK:
                case BUG:
                    return Task.SECOND_LINE;
                default:
                    return Task.NO;
            }
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getWord() {
            return word;
        }

        public void setWord(String word) {
            this.word = word;
        }
    }

    public enum TaskType {

        CREATED(0, "默认"),
        HEAD_CASE(1, "头部案例"),
        WAIST_CASE(2, "腰部案例"),
        TAIL_CASE(3, "尾部案例"),

        //多部门传递类型
        APPROVE(302,"审核"),
        REPORT(303,"举报"),
        HOSPITAL_AUDIT(304 ,"医院核实"),
        DRAW_CASH_AND_REFUND( 305,"打退款"),
        Bug(306,"BUG"),
        OTHER(307,"其他"),


        //案例审核任务类型
        ZERO_REFUSE(1001, "0次驳回"),
        ONE_TO_THREE_REFUSE(1002, "1-3次驳回"),
        THREE_MORE_REFUSE(1003, "3次以上驳回"),

        // 信息流转工单类型
        SINGLE_REFUND_DELAY_48H_NOTICE(50001, "【退款申请】详情页面提交成功时，生成资金系统退款工单, 生成后激活48小时（172800秒）计时任务"),
        VISIT_OLD_RAISE_CASE_RECALL(50002, "当用户访问老接口发起案例被拒绝时，需要自动生成一条信息传递工单"),
        reject_workorder_test(50003,"驳回工单鹰眼分流测试"),
        flow_workorder_bd(50004,"线下BD平台关联"),


        ;

        private int code;
        private String word;
        private static Map<Integer, TaskType> map = Maps.newHashMap();

        TaskType(int code, String word) {
            this.code = code;
            this.word = word;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getWord() {
            return word;
        }

        public void setWord(String word) {
            this.word = word;
        }

        static {
            for (TaskType taskType : TaskType.values()) {
                map.put(taskType.getCode(), taskType);
            }
        }

        /**
         * 获取是否是首次审核的 头腰尾类型
         * @param type
         * @return
         */
        public static boolean isFirstApprove(TaskType type){
            return type == HEAD_CASE || type == WAIST_CASE || type == TAIL_CASE;
        }

        public static TaskType getByCount(int refuseCount){
           switch (refuseCount){
               case 0:
                   return ZERO_REFUSE;
               case 1:
               case 2:
               case 3:
                   return ONE_TO_THREE_REFUSE;
               default:
                   return THREE_MORE_REFUSE;
           }
        }

        public static TaskType getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            TaskType taskType = map.get(code);
            return taskType != null ? taskType : TaskType.CREATED;
        }
    }

    public enum Status {

        CREATED(0, "已创建"),
        HANDLING(1, "处理中"),
        SHUTDOWN(2, "已关闭"),
        FINISHED(3, "已解决"),
        // 信息传递工单 begin
        NO_NEED_HANDLE(4, "无需处理"),
        HANDLE_SUCCESS(5, "处理完成"),
        // 信息传递工单 end
        FIRST_APPROVE_CANCEL(6, "前置工单被撤回"),

        ;

        private int code;
        private String word;
        private static Map<Integer, Status> map = Maps.newHashMap();

        Status(int code, String word) {
            this.code = code;
            this.word = word;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getWord() {
            return word;
        }

        public void setWord(String word) {
            this.word = word;
        }

        static {
            for (Status status : Status.values()) {
                map.put(status.getCode(), status);
            }
        }

        public static Status getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            Status status = map.get(code);
            return status != null ? status : Status.CREATED;
        }
    }

    public enum Result {

        INIT(0, "未处理"),
        HANDLING(1, "处理中"),
        HANDLE_SUCCESS(2, "处理成功"),
        HANDLE_FAILED(3, "处理失败"),
        NO_NEED_HANDLE(4, "无需处理"),
        SYSTEM_COMPLETE(5, "系统自动处理"),
        CROWDFUNDINF_FAILED(6,"筹款停止"),
        ;

        private int code;
        private String word;
        private static Map<Integer, Result> map = Maps.newHashMap();

        Result(int code, String word) {
            this.code = code;
            this.word = word;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getWord() {
            return word;
        }

        public void setWord(String word) {
            this.word = word;
        }

        static {
            for (Result result : Result.values()) {
                map.put(result.getCode(), result);
            }
        }

        public static Result getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            Result result = map.get(code);
            return result != null ? result : Result.INIT;
        }
    }

    public enum Role {

        NO(0, ""),
        SYSTEM(1, "系统"),
        POST(2, "角色/职务"),
        PERSON(3, "工作人员"),
        ROLE_GROUP(4, "角色组");

        private int code;
        private String word;
        private static Map<Integer, Role> map = Maps.newHashMap();

        Role(int code, String word) {
            this.code = code;
            this.word = word;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getWord() {
            return word;
        }

        public void setWord(String word) {
            this.word = word;
        }

        static {
            for (Role role : Role.values()) {
                map.put(role.getCode(), role);
            }
        }

        public static Role getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            Role role = map.get(code);
            return role != null ? role : Role.NO;
        }
    }

    public enum Level {

        NO(0, "无优先级"),
        LOW(1, "低"),
        MEDIUM(2, "中"),
        HIGH(3, "高"),
        ;

        private int code;
        private String word;
        private static Map<Integer, Level> map = Maps.newHashMap();

        static {
            for (Level level : Level.values()) {
                map.put(level.getCode(), level);
            }
        }

        Level(int code, String word) {
            this.code = code;
            this.word = word;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getWord() {
            return word;
        }

        public void setWord(String word) {
            this.word = word;
        }

        public static Level getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            Level level = map.get(code);
            if (level != null) {
                return level;
            } else {
                return Level.NO;
            }
        }



        public static String getWordByCode(Integer code) {
            if (code == null) {
                return null;
            }
            Level level = map.get(code);
            if (level != null) {
                switch (level){
                    case NO:
                        return "非紧急";
                    default:
                        return "紧急";
                }
            } else {
                return "非紧急";
            }
        }
    }

    public enum OperatorRole {

        //与task相对应

        OUT_BOUND(3, "外呼", 69),
        SECOND_LINE(4, "二线", 70),
        FLOW_FIRST(11, "信息传递-首次", 110),

        //与task_type相对应

        APPROVE(302, "审核", 71),
        REPORT(303, "举报", 72),
        HOSPITAL_CHECK(304, "医院核实", 73),
        CASH_HANDLE(305, "打退款", 74),
        BUG(306, "bug", 75),
        OTHER(307, "其他", 0),
        ;



        private int code;
        private String word;
        private int roleId;
        private final static Map<Integer, OperatorRole> CODE_MAP = Maps.newHashMap();
        private final static Map<Integer, OperatorRole> ROLE_MAP = Maps.newHashMap();
        private final static List<Integer> ROLE_ID_LIST = Lists.newArrayList();

        OperatorRole(int code, String word, int roleId) {
            this.code = code;
            this.word = word;
            this.roleId = roleId;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getWord() {
            return word;
        }

        public void setWord(String word) {
            this.word = word;
        }

        public int getRoleId() {
            return roleId;
        }

        public void setRoleId(int roleId) {
            this.roleId = roleId;
        }

        static {
            for (OperatorRole operatorRole : OperatorRole.values()) {
                CODE_MAP.put(operatorRole.getCode(), operatorRole);
                ROLE_MAP.put(operatorRole.getRoleId(), operatorRole);
                ROLE_ID_LIST.add(operatorRole.getRoleId());
            }
        }

        public static OperatorRole getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            OperatorRole operatorRole = CODE_MAP.get(code);
            if (operatorRole != null) {
                return operatorRole;
            } else {
                return null;
            }
        }

        public static OperatorRole getByRoleId(Integer roleId) {
            if (roleId == null) {
                return null;
            }
            OperatorRole operatorRole = ROLE_MAP.get(roleId);
            if (operatorRole != null) {
                return operatorRole;
            } else {
                return null;
            }
        }

        //获取taskType的id
        public static List<Integer> getTaskByRoleIdList(List<Integer> roleIds) {
            List<Integer> taskIds = Lists.newArrayList();
            for (Integer roleId : roleIds) {
                OperatorRole operatorRole = ROLE_MAP.get(roleId);
                if (operatorRole != null){
                    taskIds.add(operatorRole.getCode());
                }
            }
            return taskIds;
        }


        public static List<Integer> getRoleIdList() {
            return Lists.newArrayList(ROLE_ID_LIST);
        }
    }

    @AllArgsConstructor
    public enum AdminWorkOrderOperateTypeEnum {
        DEFAULT(0, "默认值"),

        // 举报工单 begin
        CREATE_REPORT_ORDER(1, "系统创建举报工单"),
        ASSIGN__REPORT_ORDER(2, "举报工单领取"),
        WORK_REPORT_ORDER_PASS(8, "举报工单传递"),

        // 举报工单 end

        // 前置审核ugc 工单 begin
        FIRST_APPROVE_ORDER_ASSIGN(10, "ugc前置审核工单领取"),
        // 首次ugc 工单 end
        ;
        private int code;
        private String desc;

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static AdminWorkOrderOperateTypeEnum getEnumByCode(int code) {
            AdminWorkOrderOperateTypeEnum[] values = AdminWorkOrderOperateTypeEnum.values();
            for (AdminWorkOrderOperateTypeEnum typeEnum : values) {
                if (code == typeEnum.getCode()) {
                    return typeEnum;
                }
            }
            return null;
        }
    }
}













