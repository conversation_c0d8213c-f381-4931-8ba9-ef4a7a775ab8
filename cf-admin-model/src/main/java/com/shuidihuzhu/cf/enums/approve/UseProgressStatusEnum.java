package com.shuidihuzhu.cf.enums.approve;

import lombok.Getter;

/**
 * <AUTHOR>
 */
public enum UseProgressStatusEnum {

    /**
     * 未审核
     */
    NEED_AUDIT_STATUS(0, "未审核"),

    AUDIT_SUCCESS(1, "通过"),

    AUDIT_REJECTED_STATUS(2, "驳回"),

    ;

    @Getter
    private final int code;

    @Getter
    private final String msg;

    UseProgressStatusEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static UseProgressStatusEnum parse(int code) {
        for (UseProgressStatusEnum e : UseProgressStatusEnum.values()){
            if (e.getCode() == code) {
                return e;
            }
        }
        throw new IllegalArgumentException("UseProgressStatusEnum not found code: " + code);
    }
}
