package com.shuidihuzhu.cf.enums.message;

import java.util.HashMap;
import java.util.Map;

public enum PushWxSubtaskStatusEnum {
    INITIAL(0, "初始化"),
    WAIT_SEND(1, "待发送"),
    SUCCESS(2, "发送成功"),
    FAILED(3, "发送失败"),
    SENDING(4, "发送中"),
    KILLED_SENDING(5, "任务手动终止"),
    LOAD_DATA(6, "加载数据中"),
    AUTO_SEND(7, "子任务自动定时发送"),
    AUTO_SEND_FAILED(8,"子任务定时自动发送前，父任务被删除");
    private int status;
    private String desc;
    private static Map<Integer, PushWxSubtaskStatusEnum> mpas = new HashMap<>();

    static {
        for (PushWxSubtaskStatusEnum statusEnum : values()) {
            mpas.put(statusEnum.getStatus(), statusEnum);
        }
    }

    PushWxSubtaskStatusEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public int getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    public static PushWxSubtaskStatusEnum getByStatus(int status) {
        return mpas.get(status);
    }
}
