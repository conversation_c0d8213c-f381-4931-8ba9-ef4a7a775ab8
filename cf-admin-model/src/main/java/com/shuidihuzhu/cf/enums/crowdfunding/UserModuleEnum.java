package com.shuidihuzhu.cf.enums.crowdfunding;

import com.shuidihuzhu.cf.constants.admin.PermissionConst;
import lombok.Getter;

/**
 * 用户管理-模块枚举
 */
@Getter
public enum UserModuleEnum {
    /**
     *
     */
    BASIC(1, "基本信息", true, PermissionConst.USER_MODULE_BASIC),
    USER_BEHAVIOR(2, "用户行为", true, PermissionConst.USER_MODULE_BEHAVIOR),

    ;

    private final boolean enable;
    private final String permission;
    private final int value;
    private final String msg;

    UserModuleEnum(int key, String value, boolean enable, String permission){
        this.value = key;
        this.msg = value;
        this.enable = enable;
        this.permission = permission;
    }

    public static UserModuleEnum parse(int key){
        for (UserModuleEnum behaviorEnum : UserModuleEnum.values()){
            if(key == behaviorEnum.getValue()){
                return behaviorEnum;
            }
        }
        throw new RuntimeException("can not find UserModuleEnum of code" + key);
    }
}
