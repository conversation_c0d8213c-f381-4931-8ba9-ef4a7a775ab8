package com.shuidihuzhu.cf.enums.admin.errors;


import com.shuidihuzhu.cf.enums.admin.interfaces.ICommonError;

/**
 * 通用错误
 *
 * @Author: wuxinlong
 * @Since: 2016-11-09
 */
public enum CommonErrorEnum implements ICommonError {
	// 系统相关
	OUT_SERVICE_REQUEST_ERROR(1, "外部服务调用失败"),

	// 参数相关
	INVALID_ENUM_VALUE(30, "不正确的枚举值"),
	PARAMETER_ERROR(31, "参数不正确"),
	PARAMETER_IS_EMPTY(32, "参数不能为空"),
	URI_ERROR(33, "uri错误"),
	PARAMETER_PAGE_NOT_EXISTS(34, "page不能为空"),
	;

	private ErrorGroupEnum errorGroup = ErrorGroupEnum.GROUP_COMMON;
	private int code;
	private String description;

	CommonErrorEnum(int code, String description) {
		this.code = code;
		this.description = description;
	}

	@Override
	public ErrorGroupEnum getGroupEnum() {
		return errorGroup;
	}

	@Override
	public int getCode() {
		return errorGroup.getCode() * 1000 + code;
	}

	@Override
	public String getDescription() {
		return description;
	}
}
