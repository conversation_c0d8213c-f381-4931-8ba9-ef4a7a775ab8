package com.shuidihuzhu.cf.enums.crowdfunding;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * @Auther: subing
 * @Date: 2020/4/17
 */
public enum AdminCfReportDisposeShowStatusEnum {
    //
    ALL(1, "全部"),
    IS_USE(2, "启用")
    ;
    private int type;
    private String description;

    public int getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }

    AdminCfReportDisposeShowStatusEnum(int type, String description) {
        this.type = type;
        this.description = description;
    }
    static Map<Integer, String> maps =  Maps.newHashMap();

    static {
        for (AdminCfReportDisposeShowStatusEnum fundUseAuditStatusType : AdminCfReportDisposeShowStatusEnum.values()) {
            maps.put(fundUseAuditStatusType.type, fundUseAuditStatusType.description);
        }
    }

    public static String getDescription(int type) {
        for (AdminCfReportDisposeShowStatusEnum fundUseAuditStatusType : AdminCfReportDisposeShowStatusEnum.values()) {
            if (fundUseAuditStatusType.getType() == type) {
                return  fundUseAuditStatusType.getDescription();
            }
        }
        return "";
    }
}
