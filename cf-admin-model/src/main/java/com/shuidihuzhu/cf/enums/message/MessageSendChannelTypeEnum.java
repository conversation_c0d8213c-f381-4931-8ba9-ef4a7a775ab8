package com.shuidihuzhu.cf.enums.message;

import java.util.Map;

import com.google.common.collect.Maps;

/**
 * Created by ahrievil on 2017/3/3.
 * 对标shuidi_message.message_statistics的type字段
 */
public enum MessageSendChannelTypeEnum {

    DEFAULT(0, "默认"),
    PUSH(1,"客户端消息"),
    SMS(2, "短信"),
    WX(3, "微信模板消息"),
    WX_CUSTOM(4, "微信客服消息"),
	WX_QUEUE(5, "微信模板队列消息"),
    WX_MINI_APP(6,"微信小程序消息"),
	;
	
	private static Map<Integer, MessageSendChannelTypeEnum> enumMap = Maps.newHashMap();

	static {
		for (MessageSendChannelTypeEnum tmpEnum : MessageSendChannelTypeEnum.values()) {
			enumMap.put(tmpEnum.value(), tmpEnum);
		}
	}

	public static MessageSendChannelTypeEnum fromCode(int code) {
		return enumMap.get(code);
	}
    
    private int value;
    private String name;

    MessageSendChannelTypeEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public int value() {
        return value;
    }
    
    public String getName() {
        return name;
    }
}
