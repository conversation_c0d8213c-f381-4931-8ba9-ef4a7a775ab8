package com.shuidihuzhu.cf.enums.pr;

import lombok.Getter;
import lombok.Setter;

public enum PrDetailsPageCollocateEnum {
    CASE_EXIST("案例已存在"),
    CASE_INEXISTENCE("无法识别案例id"),
    CASE_INCONFORMITY_A("该案例不符合保存条件，请检查案例状态或是否有风控问题"),
    CASE_INCONFORMITY_B("该案例不符合保存条件，请至少开启水滴助力或媒体报道中的一项"),
    CASE_INCONFORMITY_C("该案例不符合保存条件，请至少开启除筹款顾问外，水滴助力各模块中的一项"),
    CASE_END("该案例已停止筹款，不能切换成品牌模式"),
    CASE_BRAND("请先将该案例切换至普通模式，才可删除"),
    REPETITION_SORT("模块排序重复，请重新选择"),
    MEDIA_COVERAGE_MAX_FIVE("最多5条媒体报道"),
    CASE_IS_BRAND_TYPE("请先将该案例切换至普通模式，才可删除"),
    CASE_TITLE_MAX_NUM("筹款标题不能超过25个字"),
    HELP_NAME_MAX_NUM("求助人姓名不能超过10个字"),

    ;

    @Getter
    @Setter
    private String desc;

    PrDetailsPageCollocateEnum(String desc) {
        this.desc = desc;
    }
}
