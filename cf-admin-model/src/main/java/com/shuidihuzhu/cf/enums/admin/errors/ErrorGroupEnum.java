package com.shuidihuzhu.cf.enums.admin.errors;

/**
 * 错误分组
 *
 * 为了统一，本枚举类型里面的每个枚举值，请都加上'GROUP_'的前缀
 *
 * Author: wuxinlong
 * Date: 16/10/21 12:09
 */

public enum ErrorGroupEnum {

	// 1-50为系统通用分组
	GROUP_COMMON(1, "通用错误"),

	// 51及以后为普通分组
	GROUP_ADMIN_PRIVILEGE(51, "sea后台权限相关"),
	;

	private int code;
	private String msg;

	ErrorGroupEnum(int code, String msg) {
		this.code = code;
		this.msg = msg;
	}

	public int getCode() {
		return code;
	}

	public String getMsg() {
		return msg;
	}

}
