package com.shuidihuzhu.cf.enums.ai;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description: ai生成内容类型
 * @Author: pan<PERSON><PERSON><PERSON>
 * @Date: 2024/5/29 5:28 PM
 */
public enum AiGenerateEnum {

    ARTICLE(1, "文章"),

    TITLE(2, "标题"),

    FORWARD(3, "转发语"),
    ACKNOWLEDGMENTS(4, "感谢语"),
    ;

    @Getter
    private final String msg;
    @Getter
    private final int code;

    AiGenerateEnum(int code, String msg){
        this.msg = msg;
        this.code = code;
    }

    private static final Map<Integer, String> map = new HashMap();

    public static Map<Integer, String> getEnumMap() {
        return map;
    }

    static {
        AiGenerateEnum[] var0 = values();
        int var1 = var0.length;

        for(int var2 = 0; var2 < var1; ++var2) {
            AiGenerateEnum e = var0[var2];
            map.put(e.getCode(), e.getMsg());
        }

    }

}
