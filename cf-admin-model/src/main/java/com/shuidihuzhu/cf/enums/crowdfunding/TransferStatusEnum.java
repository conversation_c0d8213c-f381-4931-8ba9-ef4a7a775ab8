package com.shuidihuzhu.cf.enums.crowdfunding;

public enum TransferStatusEnum {
    INITIAL(0),     // 初始化，未处理
    IN_PROGRESS(10), // 处理中
    HANDLE_SUCCESS(20),     // 处理成功，并不表示打款成功
    PAY_SUCCESS(21),     // 打款成功
    FAIL(30),        // 处理失败
    PAY_FAIL(31),        // 转账失败
    ;

    private int code;

    TransferStatusEnum(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public TransferStatusEnum getByCode(int code) throws IllegalArgumentException {
        for (TransferStatusEnum statusEnum : TransferStatusEnum.values()) {
            if (statusEnum.getCode() == code) {
                return statusEnum;
            }
        }
        throw new IllegalArgumentException("code无法找到对应的枚举");
    }
}
