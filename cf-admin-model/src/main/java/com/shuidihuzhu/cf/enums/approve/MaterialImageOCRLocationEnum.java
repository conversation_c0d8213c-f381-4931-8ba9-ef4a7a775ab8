package com.shuidihuzhu.cf.enums.approve;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@ApiModel("OCR识别报错位置枚举")
public enum MaterialImageOCRLocationEnum {


    /**
     *
     */
    @ApiModelProperty("患者姓名")
    NAME(1, "患者姓名"),

    @ApiModelProperty("日期")
    TIME(2, "日期"),

    @ApiModelProperty("医院名称")
    HOSPITAL_NAME(2, "医院名称"),

    ;

    @Getter
    private final String msg;
    @Getter
    private final int code;

    MaterialImageOCRLocationEnum(int code, String msg){
        this.msg = msg;
        this.code = code;
    }

    public String getShowMsg(){
        return "医疗材料中" + msg;
    }

}
