package com.shuidihuzhu.cf.enums.maskcode;

/**
 * @Description: 掩码类型
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/7/5 8:11 下午
 */
public enum MaskTypeEnum {

    VIEW_RAISE_MOBILE(1, "查看用户手机号"),
    VIEW_RAISE_ID_CARD(2, "查看发起人身份证号"),
    VIEW_PATIENT_ID_CARD(3, "查看患者身份证号"),
    VIEW_PAYEE_ID_CARD(4, "查看收款人身份证号"),
    VIEW_PAYEE_BANK_CARD(5, "查看收款人银行卡号"),
    VIEW_CONFIRM_ID_CARD(6, "查看证实人身份证号"),
    VIEW_CONTRARY_PAYEE(7, "查看对公医院银行卡号"),
    ;

    private int code;
    private String description;

    MaskTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static String getDescription(int code) {
        for (MaskTypeEnum value : MaskTypeEnum.values()) {
            if (value.getCode() == code) {
                return value.getDescription();
            }
        }
        return  "";
    }

}
