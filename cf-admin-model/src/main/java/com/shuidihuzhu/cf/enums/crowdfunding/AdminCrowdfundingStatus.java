package com.shuidihuzhu.cf.enums.crowdfunding;

/**
 * Created by Ahrievil on 2017/9/14
 */
public enum AdminCrowdfundingStatus {
    /**
     * '状态: 0-审批中, 1-被驳回，2-筹款中，3-已结束', 4-已提交,
     */
    APPROVE_PENDING(0, "审批中"),
    APPROVE_DENIED(1, "被驳回"),
    CROWDFUNDING_STATED(2, "筹款中"),

    // 去掉以结束用endtime表示
    @Deprecated
    FINISHED(3, "已结束"),

    SUBMITTED(4, "已提交");
    private int status;
    private String word;

    AdminCrowdfundingStatus(int value, String word) {
        status = value;
        this.word = word;
    }

    public int value() {
        return status;
    }

    public String word() {
        return word;
    }

    public static AdminCrowdfundingStatus getByValue(int value) {
        for (AdminCrowdfundingStatus adminCrowdfundingStatus : AdminCrowdfundingStatus.values()) {
            if (adminCrowdfundingStatus.value() == value) {
                return adminCrowdfundingStatus;
            }
        }
        return AdminCrowdfundingStatus.APPROVE_PENDING;
    }
}
