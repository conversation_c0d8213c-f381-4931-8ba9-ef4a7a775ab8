package com.shuidihuzhu.cf.enums.crowdfunding;

import com.google.common.collect.Maps;

import java.util.Map;

public enum AdminReportPronlemUseEnum {
    //
    NO_USE(0, "弃用"),
    USE(1, "启用"),
    ALL(2, "全部")
    ;
    private int type;
    private String description;

    public int getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }

    AdminReportPronlemUseEnum(int type, String description) {
        this.type = type;
        this.description = description;
    }
    static Map<Integer, String> maps =  Maps.newHashMap();

    static {
        for (AdminReportPronlemUseEnum fundUseAuditStatusType : AdminReportPronlemUseEnum.values()) {
            maps.put(fundUseAuditStatusType.type, fundUseAuditStatusType.description);
        }
    }

    public static String getDescription(int type) {
        for (AdminReportPronlemUseEnum fundUseAuditStatusType : AdminReportPronlemUseEnum.values()) {
            if (fundUseAuditStatusType.getType() == type) {
                return  fundUseAuditStatusType.getDescription();
            }
        }
        return AdminReportPronlemUseEnum.ALL.getDescription();
    }
}
