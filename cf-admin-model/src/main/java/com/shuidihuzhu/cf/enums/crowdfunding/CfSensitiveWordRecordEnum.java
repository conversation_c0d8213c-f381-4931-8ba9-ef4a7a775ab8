package com.shuidihuzhu.cf.enums.crowdfunding;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.enums.risk.UgcTypeEnum;

import java.util.Map;

public class CfSensitiveWordRecordEnum {

	public enum BizType {

		BASE_INFO(1, "图文信息"),
        ORDER(2, "订单"),
        PROGRESS(3, "动态"),
        COMMENT_ORDER(4, "订单评论"),
        COMMENT_PROGRESS(5, "动态评论"),
        VERIFICATION(6, "证实");

		private int value;
		private String desc;

		BizType(int value, String desc) {
			this.value = value;
			this.desc = desc;
		}

		public int value() {
			return value;
		}

		public String desc() {
			return desc;
		}

		public static BizType getByValue(int value) {
			return map.get(value);
		}

		public static Map<Integer, BizType> map = Maps.newHashMap();

		static {
			for (BizType bizType : BizType.values()) {
				map.put(bizType.value(), bizType);
			}
		}

		public UgcTypeEnum getUgcTypeEnum (){
			UgcTypeEnum ugcTypeEnum = null;
			switch (this) {
				case ORDER:
					ugcTypeEnum = UgcTypeEnum.ORDER;
					break;
				case COMMENT_ORDER:
				case COMMENT_PROGRESS:
					ugcTypeEnum = UgcTypeEnum.COMMENT;
					break;
				case VERIFICATION:
					ugcTypeEnum = UgcTypeEnum.VERIFICATION;
					break;
				default:
					break;
			}
			return ugcTypeEnum;
		}

	}
}
