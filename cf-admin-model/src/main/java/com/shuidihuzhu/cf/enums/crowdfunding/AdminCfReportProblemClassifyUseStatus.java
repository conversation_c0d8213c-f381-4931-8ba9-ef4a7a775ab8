package com.shuidihuzhu.cf.enums.crowdfunding;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * @Auther: subing
 * @Date: 2020/4/22
 */
public enum  AdminCfReportProblemClassifyUseStatus {
    //
    IS_USE(1, "启用"),
    NOT_USE(0, "弃用"),
    ;
    private int type;
    private String description;

    public int getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }

    AdminCfReportProblemClassifyUseStatus(int type, String description) {
        this.type = type;
        this.description = description;
    }
    static Map<Integer, String> maps =  Maps.newHashMap();

    static {
        for (AdminCfReportProblemClassifyUseStatus fundUseAuditStatusType : AdminCfReportProblemClassifyUseStatus.values()) {
            maps.put(fundUseAuditStatusType.type, fundUseAuditStatusType.description);
        }
    }

    public static String getDescription(int type) {
        for (AdminCfReportProblemClassifyUseStatus fundUseAuditStatusType : AdminCfReportProblemClassifyUseStatus.values()) {
            if (fundUseAuditStatusType.getType() == type) {
                return  fundUseAuditStatusType.getDescription();
            }
        }
        return "";
    }
}
