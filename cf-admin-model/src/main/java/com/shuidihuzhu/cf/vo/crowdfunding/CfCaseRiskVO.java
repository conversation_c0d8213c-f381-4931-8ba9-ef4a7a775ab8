package com.shuidihuzhu.cf.vo.crowdfunding;

import io.swagger.annotations.ApiParam;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018-08-13  14:47
 */
@Data
public class CfCaseRiskVO {

    private long id;

    private long caseId;

    private String infoUuid;

    private String mobile;

    private String title;

    private String operatorName;

    @ApiParam("等待处理: 为true 就显示审核通过按钮 false则不可点击")
    private boolean waitingProcess;

    private int risk;

    private Map riskData;

    private int handleStatus;

    private Timestamp updateTime;

    private String lastRemarkContent;

}
