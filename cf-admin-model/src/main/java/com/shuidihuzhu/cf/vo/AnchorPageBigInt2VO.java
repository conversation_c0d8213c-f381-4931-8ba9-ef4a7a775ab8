package com.shuidihuzhu.cf.vo;

import com.shuidihuzhu.common.web.model.AbstractModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-05-08  10:53
 * @TODO notice javascript Number类型超过17位会不精准 需要转成 String
 * @see AnchorPageVo
 * 为了适配新的bigint id
 */
@Data
@NoArgsConstructor
public class AnchorPageBigInt2VO<T> extends AbstractModel {

    private static final long serialVersionUID = 3837984368501882465L;

    private Long preAnchor;
    private Long nextAnchor;
    private int size;
    private boolean hasNext;
    private List<T> list = Collections.emptyList();
    private int total;

}
