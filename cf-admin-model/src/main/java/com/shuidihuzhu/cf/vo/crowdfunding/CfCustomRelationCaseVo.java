package com.shuidihuzhu.cf.vo.crowdfunding;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * Created by dongcf on 2020/7/24
 */
@ApiModel("患者关系案例信息")
@Data
public class CfCustomRelationCaseVo {

    @ApiModelProperty("支持修改标记")
    private boolean modifyFlag = true;

    @ApiModelProperty("筹款人填写关系类型")
    private int originRelationType;

    @ApiModelProperty("筹款人填写关系名称")
    private String originRelation;

    @ApiModelProperty("患者姓名")
    private String patientName;

    @ApiModelProperty("筹款人昵称")
    private String crowdFounderNickname;

    @ApiModelProperty("展示类型")
    private int showType;

    @ApiModelProperty("自定义关系集合")
    private Map<Integer, String> customRelationMap;
}
