package com.shuidihuzhu.cf.vo.mina;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.util.List;

public class CfMinaTopicDetailVo {
    private int id;
    private int phaseId;
    private String description;
    private String imgUrl;
    private String title;
    private int publishStatus;
    private Timestamp createTime;
    private Timestamp publishTime;
    private List<String> imgUrls;

    public CfMinaTopicDetailVo() {
    }

    public CfMinaTopicDetailVo(int id, int phaseId, String description,
                               String imgUrl, String title, int publishStatus,
                               Timestamp createTime, Timestamp publishTime,
                               String imgUrls) {
        this.id = id;
        this.phaseId = phaseId;
        this.description = description;
        this.imgUrl = imgUrl;
        this.title = title;
        this.publishStatus = publishStatus;
        this.createTime = createTime;
        this.publishTime = publishTime;
        if (StringUtils.isNotBlank(imgUrls)) {
            this.imgUrls = Splitter.on(",").splitToList(imgUrls);
        }
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(int phaseId) {
        this.phaseId = phaseId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(Timestamp publishTime) {
        this.publishTime = publishTime;
    }

    public int getPublishStatus() {
        return publishStatus;
    }

    public void setPublishStatus(int publishStatus) {
        this.publishStatus = publishStatus;
    }

    public List<String> getImgUrls() {
        return imgUrls;
    }

    public void setImgUrls(String imgUrls) {
        this.imgUrls = Lists.newArrayList();
        if (StringUtils.isNotBlank(imgUrls)) {
            this.imgUrls = Splitter.on(",").splitToList(imgUrls);
        }
    }

    @Override
    public String toString() {
        return "CfMinaTopicDetailVo{" +
                "id=" + id +
                ", phaseId=" + phaseId +
                ", description='" + description + '\'' +
                ", imgUrl='" + imgUrl + '\'' +
                ", title='" + title + '\'' +
                ", publishStatus=" + publishStatus +
                ", createTime=" + createTime +
                ", publishTime=" + publishTime +
                '}';
    }
}
