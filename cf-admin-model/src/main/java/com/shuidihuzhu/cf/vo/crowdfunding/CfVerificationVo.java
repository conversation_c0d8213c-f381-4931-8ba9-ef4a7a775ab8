package com.shuidihuzhu.cf.vo.crowdfunding;

import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/10/30 下午2:41
 * @desc
 */
@Data
public class CfVerificationVo extends CrowdfundingfundingVerificationVo {
    private String identity;

    private NumberMaskVo identityMask;
    //0:正常展示 1:仅自己可见
    private int status = 0;

    private long workOrderId;

    private int complaintInfoSize;

    private List<ComplaintInfo> complaintInfoList;

    private NumberMaskVo mobileMask;

    @Data
    @NoArgsConstructor
    public static class ComplaintInfo{

        /**
         * 投诉时间
         */
        private String complaintTime;

        /**
         * 投诉人id
         */
        private Long complaintUserId;

        /**
         * 投诉类型
         */
        private String complaintMsg;



    }


}
