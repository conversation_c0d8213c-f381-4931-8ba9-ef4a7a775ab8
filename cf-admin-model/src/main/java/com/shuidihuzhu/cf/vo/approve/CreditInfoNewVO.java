package com.shuidihuzhu.cf.vo.approve;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.client.material.model.CfPropertyInsuranceInfoModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * @Author: wangpeng
 * @Date: 2021/7/30 15:59
 * @Description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class CreditInfoNewVO extends CfPropertyInsuranceInfoModel {

    private String homeIncomeDesc;

    private String homeStockDesc;

    @ApiModelProperty("车产")
    private CreditInfoNewVO.CarPropertyInfoVO carPropertyNew;

    @ApiModelProperty("房产")
    private CreditInfoNewVO.HousePropertyInfoVO housePropertyNew;

    @ApiModelProperty("自建房产")
    private CreditInfoNewVO.HousePropertyInfoVO selfBuiltHouseNew;

    public static CreditInfoNewVO build(CfPropertyInsuranceInfoModel info){
        if (info == null) {
            return null;
        }
        CreditInfoNewVO v ;
        String jsonString = JSON.toJSONString(info);
        v = JSON.parseObject(jsonString, CreditInfoNewVO.class);//已检查过
        build(v.getCarPropertyNew());
        build(v.getHousePropertyNew());
        build(v.getSelfBuiltHouseNew());

        String homeIncome = buildValue(v.getHomeIncomeUserDefined(), v.getHomeIncomeRangeType(), new CreditInfoNewVO.HomeIncomeFunction());
        v.setHomeIncomeDesc(homeIncome);

        String homeStock = buildValue(v.getHomeStockUserDefined(), v.getHomeStockRangeType(), new CreditInfoNewVO.HomeStockFunction());
        v.setHomeStockDesc(homeStock);

        return v;
    }

    private static String buildValue(Integer userDefined, Integer rangeType, CreditInfoNewVO.ParseFunction parseFunction){
        if (userDefined != null && userDefined >= 0) {
            BigDecimal bg = new BigDecimal(userDefined);
            double f = bg.divide(BigDecimal.valueOf(10000), 2,  RoundingMode.HALF_DOWN).doubleValue();
            return f + "万元";
        }
        if (rangeType == null) {
            return "";
        }
        if (rangeType <= 0) {
            log.error("rangeType 不合法:{}", rangeType);
            return "";
        }
        CfPropertyInsuranceInfoModel.IValueRange range = parseFunction.parse(rangeType);
        return range.getDesc();
    }

    private static void build(CreditInfoNewVO.CarPropertyInfoVO carProperty) {
        if (carProperty == null) {
            return;
        }
        CreditInfoNewVO.ParseFunction carFunction = new CreditInfoNewVO.CarFunction();

        String saleValueDesc = buildValue(carProperty.getSaleValueUserDefined(), carProperty.getSaleValueRangeType(), carFunction);
        carProperty.setSaleValueDesc(saleValueDesc);

        String total = buildValue(carProperty.getTotalValueUserDefined(), carProperty.getTotalValueRangeType(), carFunction);
        carProperty.setTotalValueDesc(total);
    }

    private static void build(CreditInfoNewVO.HousePropertyInfoVO property) {
        if (property == null) {
            return;
        }
        CreditInfoNewVO.HouseFunction houseFunction = new CreditInfoNewVO.HouseFunction();

        String sale = buildValue(property.getSaleValueUserDefined(), property.getSaleValueRangeType(), houseFunction);
        property.setSaleValueDesc(sale);

        String total = buildValue(property.getTotalValueUserDefined(), property.getTotalValueRangeType(), houseFunction);
        property.setTotalValueDesc(total);
    }

    @Data
    public static class CarPropertyInfoVO extends CfPropertyInsuranceInfoModel.CarPropertyInfo{
        private String totalValueDesc;
        private String saleValueDesc;
    }

    @Data
    public static class HousePropertyInfoVO extends CfPropertyInsuranceInfoModel.HousePropertyInfo{
        private String totalValueDesc;
        private String saleValueDesc;
    }

    public interface ParseFunction {
        CfPropertyInsuranceInfoModel.IValueRange parse(int value);
    }

    private static class CarFunction implements CreditInfoNewVO.ParseFunction {

        @Override
        public CfPropertyInsuranceInfoModel.CarValueRange parse(int value) {
            return CfPropertyInsuranceInfoModel.CarValueRange.valueOfCode(value);
        }
    }

    public static class HouseFunction implements CreditInfoNewVO.ParseFunction {

        @Override
        public CfPropertyInsuranceInfoModel.HouseValueRange parse(int value) {
            return CfPropertyInsuranceInfoModel.HouseValueRange.valueOfCode(value);
        }
    }

    private static class HomeIncomeFunction implements CreditInfoNewVO.ParseFunction {

        @Override
        public CfPropertyInsuranceInfoModel.HomeIncomeValueRange parse(int value) {
            return CfPropertyInsuranceInfoModel.HomeIncomeValueRange.valueOfCode(value);
        }
    }

    public static class HomeStockFunction implements CreditInfoNewVO.ParseFunction {

        @Override
        public CfPropertyInsuranceInfoModel.HomeStockValueRange parse(int value) {
            return CfPropertyInsuranceInfoModel.HomeStockValueRange.valueOfCode(value);
        }
    }
}
