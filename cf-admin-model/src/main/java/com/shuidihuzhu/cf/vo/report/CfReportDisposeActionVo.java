package com.shuidihuzhu.cf.vo.report;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.model.report.CfReportDisposeAction;
import com.shuidihuzhu.cf.model.report.CfReportDisposeActionExt;
import lombok.Data;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/4/16
 */
@Data
public class CfReportDisposeActionVo {
    private String actionClassify;
    private long actionClassifyId;
    private List<CfReportDisposeAction> cfReportDisposeActions = Lists.newArrayList();
    private List<CfReportDisposeActionExt> cfReportDisposeActionExts = Lists.newArrayList();


    public static CfReportDisposeActionVo buildVo(String actionClassify, long actionClassifyId,
                                                  List<CfReportDisposeAction> cfReportDisposeActions) {
        CfReportDisposeActionVo cfReportDisposeActionVo = new CfReportDisposeActionVo();
        cfReportDisposeActionVo.setActionClassify(actionClassify);
        cfReportDisposeActionVo.setActionClassifyId(actionClassifyId);
        cfReportDisposeActionVo.setCfReportDisposeActions(cfReportDisposeActions);
        return cfReportDisposeActionVo;
    }

    public static CfReportDisposeActionVo buildVoForExt(String actionClassify, long actionClassifyId,
                                                        List<CfReportDisposeActionExt> cfReportDisposeActions) {
        CfReportDisposeActionVo cfReportDisposeActionVo = new CfReportDisposeActionVo();
        cfReportDisposeActionVo.setActionClassify(actionClassify);
        cfReportDisposeActionVo.setActionClassifyId(actionClassifyId);
        cfReportDisposeActionVo.setCfReportDisposeActionExts(cfReportDisposeActions);
        return cfReportDisposeActionVo;
    }
}
