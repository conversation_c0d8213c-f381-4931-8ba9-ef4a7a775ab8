package com.shuidihuzhu.cf.vo.message;

import com.shuidi.weixin.mp.bean.result.WxMpMaterialNewsBatchGetResult;
import com.shuidihuzhu.cf.model.bi.PotraitModel;
import com.shuidihuzhu.cf.vo.wx.push.WxPushStatusDescription;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * @package: com.shuidihuzhu.cf.vo.message
 * @Author: liujiawei
 * @Date: 2019-03-13  20:35
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
public class WxPushConfigVo {
    long id;
    long groupId;//组id
    int thirdType;
    long taskId;//子任务id
    String crowdId = "";//人群包id
    String subtaskName;//子任务名
    /**
     * 分组数
     */
    int crowdGroupSize;
    /**
     * 分组序号
     */
    int crowdGroupNum;
    String crowdName = "";//人群包名称
    int packageType;//人群包类型：1人群包， 2 others
    int status;//状态位
    String statusDesc;//状态位描述
    int packageSize = -1;//人群包大小
    int createdSize = -1;//当前导入量
    int pushSize = -1;//当前发送量
    String wxTemplateId = "";//文章id，来自微信
    WxPushStatusDescription wxPushStatus; // 微信发送数量描述
    int isDelete;
    Date createTime;
    Date updateTime;

    int packageGroupSize;
    boolean whetherLimitGroup;
    ArticleResultItem articleResultItem;

    public WxPushConfigVo(long groupId, int thirdType, long taskId, String crowdId, int crowdGroupSize,
                          int crowdGroupNum, String crowdName, int packageType, int packageSize, String wxTemplateId,String subtaskName) {
        this.groupId = groupId;
        this.thirdType = thirdType;
        this.taskId = taskId;
        this.crowdId = crowdId;
        this.crowdName = crowdName;
        this.crowdGroupSize = crowdGroupSize;
        this.crowdGroupNum = crowdGroupNum;
        this.packageType = packageType;
        this.packageSize = packageSize;
        this.wxTemplateId = wxTemplateId;
        this.subtaskName = subtaskName;
    }


}
