package com.shuidihuzhu.cf.vo.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplatize;
import org.springframework.beans.BeanUtils;

import java.util.List;

/**
 * <AUTHOR> Ahrievil
 * @date : 2018/9/11 17:32
 */
public class CfBaseInfoTemplatizeVo extends CfBaseInfoTemplatize {

    private List<String> replaceHolders;

    public CfBaseInfoTemplatizeVo(CfBaseInfoTemplatize cfBaseInfoTemplatize, List<String> replaceHolders) {
        BeanUtils.copyProperties(cfBaseInfoTemplatize, this);
        this.replaceHolders = replaceHolders;
    }

    public List<String> getReplaceHolders() {
        return replaceHolders;
    }

    public void setReplaceHolders(List<String> replaceHolders) {
        this.replaceHolders = replaceHolders;
    }
}
