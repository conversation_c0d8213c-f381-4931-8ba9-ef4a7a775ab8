package com.shuidihuzhu.cf.vo.admin;

import com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord;
import lombok.Data;

import java.util.List;

/**
 * @Author: l<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2018/11/14 23:44
 * 审核页面强提示vo
 * 在删除操作记录时,refuseCount记录的是被删除的id
 */
@Data
public class AdminReminderVo {
    //操作记录
    private List<CfOperatingRecord> operatingRecordList;
    //true 案例暂停 false 案例未暂停
    private boolean isdrawCashPause;
}
