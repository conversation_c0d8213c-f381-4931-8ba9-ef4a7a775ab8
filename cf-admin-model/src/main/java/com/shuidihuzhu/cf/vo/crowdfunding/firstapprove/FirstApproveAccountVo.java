package com.shuidihuzhu.cf.vo.crowdfunding.firstapprove;

import lombok.Data;

@Data
public class FirstApproveAccountVo {
    private int userId;
    private String userName;
    private int totalTaskCount;
    private int unhandleTaskCount;
    private int auditRefuseTaskCount;
    private int auditPassTaskCount;
    private String createTime;

    /**
     * 限制一次领取工单数
     */
    private int limit;

    public FirstApproveAccountVo(int userId, String userName, int totalTaskCount, int unhandleTaskCount, int auditRefuseTaskCount, int auditPassTaskCount, String createTime, int limit) {
        this.userId = userId;
        this.userName = userName;
        this.totalTaskCount = totalTaskCount;
        this.unhandleTaskCount = unhandleTaskCount;
        this.auditRefuseTaskCount = auditRefuseTaskCount;
        this.auditPassTaskCount = auditPassTaskCount;
        this.createTime = createTime;
        this.limit = limit;
    }
}
