package com.shuidihuzhu.cf.vo.crowdfunding;

import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created by sven on 2019/7/4.
 *
 * <AUTHOR>
 */
@Data
@ApiModel("筹款基本信息")
public class CrowdfundingSimpleInfoVo {

    @ApiModelProperty("案例id")
    private int caseId;

    @ApiModelProperty("案例标题")
    private String tile;

    @ApiModelProperty("目标金额")
    private int targetAmount;

    @ApiModelProperty("患者姓名")
    private String authorName;

    @ApiModelProperty("发起人手机")
    private String mobile;
    private NumberMaskVo mobileMask;

    @ApiModelProperty("发起时间")
    private String createDateStr;
}
