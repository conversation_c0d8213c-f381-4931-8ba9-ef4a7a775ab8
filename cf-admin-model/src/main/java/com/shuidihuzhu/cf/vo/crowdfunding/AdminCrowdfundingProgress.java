package com.shuidihuzhu.cf.vo.crowdfunding;

import com.shuidihuzhu.common.web.model.AbstractModel;
import lombok.Data;

import java.util.Date;


/**
 * <AUTHOR>
 * @date 2018/07/12
 */
@Data
public class AdminCrowdfundingProgress extends AbstractModel {
    private Integer id;
    private Long userId;
    private Integer crowdfundingId;
    private Integer status;
    private String fundUseRejectedReason;
    private String title;
    private String content;
    private String imageUrls;
    private Date createTime;
    private Date updateTime;
    private String drawFinishTimeStr;

}
