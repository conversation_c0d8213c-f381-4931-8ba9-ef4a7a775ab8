package com.shuidihuzhu.cf.vo.admin.initialAudit;

import com.shuidihuzhu.cf.domain.cf.AdminCfInitialAuditCheckInfoDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class AdminCfInitialAuditCheckInfoVO {
    @ApiModelProperty("校验类型 1=医疗材料抬头中医院 2=医院材料公章处医院 3=医院材料正文中医院")
    private Integer checkType;
    @ApiModelProperty("待校验名称")
    private String checkName;
    @ApiModelProperty("校验结果 1=真 0=假")
    private Integer checkResult;

    public AdminCfInitialAuditCheckInfoVO(AdminCfInitialAuditCheckInfoDO infoDO) {
        this.checkType = infoDO.getCheckType();
        this.checkName = infoDO.getCheckName();
        this.checkResult = infoDO.getCheckResult();
    }

    public AdminCfInitialAuditCheckInfoVO(Integer checkType, String checkName, Integer checkResult) {
        this.checkType = checkType;
        this.checkName = checkName;
        this.checkResult = checkResult;
    }
}
