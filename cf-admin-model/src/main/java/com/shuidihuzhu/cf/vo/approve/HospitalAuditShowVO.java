package com.shuidihuzhu.cf.vo.approve;

import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class HospitalAuditShowVO {


    /**
     *  医院核实属性
     */
    private long id;
    private String infoUuid;
    private String patientName;
    private String hospitalName;
    private String department;
    private String floorNumber;
    private String bedNumber;
    private String hospitalizationNumber;
    private String doctorName;
    private String operatorContent;
    private String departmentTelNumber;

    @ApiModelProperty("审核状态{0:未下发,1:已下发,2:通过,3:驳回,4:已提交}")
    private int auditStatus;

    private int type;
    private int isDelete;

    @ApiModelProperty("下发原因")
    private String reason;

    @ApiModelProperty("下发原因补充")
    private String reasonSupply;

    @ApiModelProperty("方便核实时间")
    private String easyToVerifyTime;

    /**
     * @see com.shuidihuzhu.cf.enums.EasyToVerifyStatusEnum
     */
    @ApiModelProperty("方便核实时间填写状态 {0:未填写,1:知道,2:不知道}")
    private int easyToVerifyTimeStatus;

    @ApiModelProperty("下发人id")
    private int operatorId;

    @ApiModelProperty("下发人姓名")
    private String operatorName;

    @ApiModelProperty("下发人组织")
    private String operatorOrg;

    @ApiModelProperty("处理人id")
    private int auditOperatorId;

    @ApiModelProperty("处理人姓名")
    private String auditOperatorName;

    @ApiModelProperty("处理人组织")
    private String auditOperatorOrg;

    /**
     * 下发的时候是否生成了工单
     * @see com.shuidihuzhu.cf.enums.BooleanEnum
     */
    @ApiModelProperty("是否走工单 {0:老数据无状态,1:是,2:否}")
    private int onWorkOrder;

    @ApiModelProperty("下发时间")
    private Date createTime;

    @ApiModelProperty("用户提交时间")
    private Date submitTime;

    @ApiModelProperty("处理时间")
    private Date auditTime;

//    ------------------ 其他属性

    @ApiModelProperty("案例标题")
    private String title;

    @ApiModelProperty("案例id")
    private int caseId;

    @ApiModelProperty("引导用户发起渠道")
    private String channel;

    @ApiModelProperty("材料审核状态")
    private CrowdfundingStatus crowdfundingStatus;

}
