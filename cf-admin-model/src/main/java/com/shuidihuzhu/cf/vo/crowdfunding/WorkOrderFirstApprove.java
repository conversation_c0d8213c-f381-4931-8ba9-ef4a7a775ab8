package com.shuidihuzhu.cf.vo.crowdfunding;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.domain.CaseRaiseRiskDO;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoStatus;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.client.cf.risk.model.result.UserTagHistoryUnit;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2018/8/9
 */
@Data
@ApiModel
public class WorkOrderFirstApprove {

    @ApiModelProperty("工单id")
    private int workOrderId;

    @ApiModelProperty("案例id")
    private int caseId;

    // TODO 后期删掉
    @Deprecated
    @ApiModelProperty("渠道")
    private String channelStr;

    @ApiModelProperty("状态 0未提交  10已提交  20 驳回  30 通过")
    private int firstStatus;

    @ApiModelProperty("原因")
    private String reason;

    @ApiModelProperty("发起人姓名")
    private String selfRealName;

    @ApiModelProperty("发起人身份证")
    private String selfIdCard;

    @ApiModelProperty("患者姓名")
    private String patientRealName;

    @ApiModelProperty("患者身份证")
    private String patientIdCard;

    @ApiModelProperty("照片")
    private String imageUrl = "";

    @ApiModelProperty("发起人关系  1是本人")
    private int userRelationType;

    @ApiModelProperty("工单状态  0 待处理  3 处理完成")
    private int ugcStatus;

    @ApiModelProperty("身份证验证状态 0：不匹配 2：在白名单里 3：系统错误 4:匹配")
    private int firstApproveIdcardVerifyStatus;

    @ApiModelProperty("用户标签等级")
    private List<UserTagHistoryUnit> units = Lists.newArrayList();

    private int operatorId;

    @ApiModelProperty("操作人")
    private String operateName;
    @ApiModelProperty("操作时间")
    private Date operatorDate;
    @ApiModelProperty("筹款用途描述")
    private String targetAmountDesc;

    @ApiModelProperty("筹款目标金额")
    private int targetAmount;

    @ApiModelProperty("贫困  0 默认  1  是  2 否")
    private Integer poverty;

    @ApiModelProperty("贫困证明材料")
    private String povertyImageUrl;

    @ApiModelProperty("是否儿童")
    private boolean isChild;

    @ApiModelProperty("患者出生证")
    private String patientBornCard;

    @ApiModelProperty("患者证件类型 " +
            "identity, // 身份证 1\n" +
            "\tbirth, //   出生证2\n" +
            "\tpassport, // 护照3\n" +
            "\tothers; // 其他0")
    private int patientIdType;

    private String infoUuid;

    private String title;

    private String content;

    private List<CrowdfundingAttachmentVo> attachments;

    private CaseRaiseRiskDO data;

    @ApiModelProperty("该案例发起的城市")
    private String sensitiveArea;

    private String channel;
    private String listGuideUserLaunchChannel;

    private String rejectDetail;

    private long ugcBaseInfoOrderId;
    private int ugcBaseInfoResult;

    private List<CrowdfundingInfoStatus> dataTypeList;

    //区分线下
    public String getChannelStr() {

        if ("cf_volunteer".equals(channelStr)){
            return "线下";
        }
        return "其他";
    }

}
