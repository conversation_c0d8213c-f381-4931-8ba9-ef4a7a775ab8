package com.shuidihuzhu.cf.vo.wx.greeting;

import com.shuidihuzhu.wx.grpc.client.common.AttachKeyItem;
import com.shuidihuzhu.wx.grpc.model.AttacheKeyItemDto;
import lombok.Data;

/**
 * @Author: duchao
 * @Date: 2018/6/12 下午8:42
 */
@Data
public class AttachKeyVO {
	private String key;
	private String name;

	public static AttachKeyVO convert(AttachKeyItem keyItem) {
		AttachKeyVO vo = new AttachKeyVO();
		vo.setKey(keyItem.getKey());
		vo.setName(keyItem.getName());
		return vo;
	}

	public static AttachKeyVO convertV2(AttacheKeyItemDto keyItem) {
		AttachKeyVO vo = new AttachKeyVO();
		vo.setKey(keyItem.getKey());
		vo.setName(keyItem.getName());
		return vo;
	}
}
