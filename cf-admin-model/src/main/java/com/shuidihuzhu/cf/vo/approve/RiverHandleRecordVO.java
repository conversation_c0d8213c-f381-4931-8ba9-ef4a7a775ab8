package com.shuidihuzhu.cf.vo.approve;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class RiverHandleRecordVO {

    @ApiModelProperty("工单id")
    private long workOrderId;

    @ApiModelProperty("审核结果")
    private String handleResult;

    @ApiModelProperty("备注内容 (内容里\\n表示换行)")
    private String content;

    @ApiModelProperty("评论内容")
    private String comment;

    @ApiModelProperty("操作时间")
    private Date actionTime;

    @ApiModelProperty("操作人")
    private String operator;

    public RiverHandleRecordVO(long workOrderId, String handleResult, String content, String comment, Date actionTime, String operator) {
        this.workOrderId = workOrderId;
        this.handleResult = handleResult;
        this.content = content;
        this.comment = comment;
        this.actionTime = actionTime;
        this.operator = operator;
    }
}
