package com.shuidihuzhu.cf.vo.admin.initialAudit;

import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseInfoVo;
import com.shuidihuzhu.cf.risk.model.risk.diease.SpecialDiseaseChoiceInfoVo;
import lombok.Data;
import lombok.Getter;

import java.util.List;

@Data
public class DiseaseStrategyResult {

    private String medicalFitDesc;
    // 0 未匹配 1 部分匹配  2 完全匹配
    private Integer medicalFitStatus;
    // 具体命中的疾病 逗号分隔
    private String medicalFit;

    private String contentFitDesc;
    // 0 未匹配 1 部分匹配  2 完全匹配
    private Integer contentFitStatus;
    // 具体命中的疾病 逗号分隔
    private String contentFit;



    /**
     * 目标金额策略结果
     */
    private Integer amountReasonableResult;
    /**
     * 目标金额策略结果描述
     */
    private String amountReasonableResultDesc;
    /**
     * 目标金额策略结果提示
     */
    private String amountReasonableResultHit;

    private boolean reportError;

    private int strategyType;

    /**
     * 用于判断疾病的显示 1：显示用户填写疾病  2: OCR/手动填写
     */
    private int beforeStrategyType;

    // 每个疾病的情况
    private List<DiseaseInfoVo> diseaseInfoList;

    // 所有特殊可发病例的特殊治疗方案
    private List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> specialDiseaseChoiceInfoList;


    /**
     *     所有多治疗方案病例的特殊治疗方案
     */
    private List<SpecialDiseaseChoiceInfoVo.SpecialDiseaseChoiceInfo> multipleDiseaseChoiceInfoList;

    /**
     * 治疗方案信息
     */
    private String treatmentInfo;

    /**
     * 清楚老信息
     */
    public void clearContentInfo() {
        setMedicalFit("");
        setMedicalFitDesc("");
        setContentFitStatus(null);
        setContentFit("");
        setContentFitDesc("");
        setContentFitStatus(null);
    }

    @Getter
    public enum FitStatus {

        NO_FIT(0, "未匹配"),
        PART_FIT(1, "部分匹配"),
        FIT(2, "完全匹配"),
        OTHER(3, "其它情况"),

        ;

        FitStatus(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private int code;
        private String desc;
    }
}
