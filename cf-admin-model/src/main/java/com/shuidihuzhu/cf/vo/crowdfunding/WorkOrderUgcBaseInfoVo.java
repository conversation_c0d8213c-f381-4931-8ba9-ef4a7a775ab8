package com.shuidihuzhu.cf.vo.crowdfunding;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.ugc.model.domain.risk.CfBaseInfoRiskHitVO;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminUGCTask;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.vo.CfFirsApproveMaterialVO;
import com.shuidihuzhu.client.cf.risk.model.result.UserTagHistoryUnit;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: lixuan
 * @date: 2018/3/9 17:34
 */
@Data
@ApiModel
public class WorkOrderUgcBaseInfoVo {

    private long workOrderId;
    private long workOrderUgcId;
    private String title;
    private String content;
    private int type;
    private int infoId;

    private String infoUuId;

    private String attachmentUrls;
    private List<Integer> watermarks;
    private int ugcResult;
    private String reason;
    private String sensitiveWords;
    /**
     * 首审状态，参见 {@link com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum}
     * 默认值为default，支持老案例
     */
    @ApiModelProperty("不可能为空，老版本给的是default")
    private int firstApproveStatus = FirstApproveStatusEnum.DEFAULT.getCode();

    /**
     * 初审照片地址，默认为空，如有多张用,分割
     */
    @ApiModelProperty("初审照片地址，有可能为空")
    private String firstApproveImageUrls;

    /**
     * 初次审核材料
     */
    @ApiModelProperty("初审具体材料，有可能为空，老版本发起的就是空")
    private CfFirsApproveMaterialVO material;

    @ApiModelProperty("案例的基本材料")
    private CrowdfundingInfo crowdfundingInfo;

    @ApiModelProperty("患者信息，可能为空")
    private CrowdfundingAuthor crowdfundingAuthor;

    private int amount;
    private int targetAmount;
    private String caseStatus;
    private String moblie;

    /**
     * @see com.shuidihuzhu.cf.enums.crowdfunding.CaseRaiseRiskLevelEnum
     */
    @ApiModelProperty("图文风险等级")
    private int infoRiskLevel;

    @Deprecated
    @ApiModelProperty("图文风险详细数据")
    private String infoRiskData;

    @ApiModelProperty("图文风险数据-v1")
    private CfBaseInfoRiskHitVO riskHitVO;

    @ApiModelProperty("触发节点")
    private int ugcAction;

    @ApiModelProperty("发起人用户风险标签，可能为空")
    private List<UserTagHistoryUnit> units = Lists.newArrayList();

    @ApiModelProperty("运营id")
    private int operatorId;

    @ApiModelProperty("运营名字")
    private String operatorName;

    @ApiModelProperty("操作时间")
    private Date operatorTime;

    @ApiModelProperty("是否已经被执行先审后发")
    private boolean firstTrial;

    public static WorkOrderUgcBaseInfoVo buildBaseInfo(long workOrderId, long adminTaskUgcId, String reason, AdminUGCTask.Content content,
                                                       CrowdfundingInfo crowdfundingInfo, List<CrowdfundingAttachment> crowdfundingAttachments,
                                                       Set<String> sensitiveWords, int taskUgcResult,CfCapitalAccount cfCapitalAccount,String moblie) {
        WorkOrderUgcBaseInfoVo workOrderUgcBaseInfoVo = new WorkOrderUgcBaseInfoVo();
        workOrderUgcBaseInfoVo.setWorkOrderId(workOrderId);
        workOrderUgcBaseInfoVo.setWorkOrderUgcId(adminTaskUgcId);
        workOrderUgcBaseInfoVo.setUgcResult(taskUgcResult);
        workOrderUgcBaseInfoVo.setType(content.getCode());
        workOrderUgcBaseInfoVo.setReason(reason);
        workOrderUgcBaseInfoVo.setInfoId(crowdfundingInfo.getId());
        workOrderUgcBaseInfoVo.setTitle(crowdfundingInfo.getTitle());
        workOrderUgcBaseInfoVo.setContent(crowdfundingInfo.getContent());
        workOrderUgcBaseInfoVo.setSensitiveWords(Joiner.on(",").join(sensitiveWords));
        if(CollectionUtils.isNotEmpty(crowdfundingAttachments)) {
            List<String> urlList = crowdfundingAttachments.stream().map(CrowdfundingAttachment::getUrl).collect
                    (Collectors.toList());
            workOrderUgcBaseInfoVo.setAttachmentUrls(Joiner.on(",").join(urlList));
        } else {
            workOrderUgcBaseInfoVo.setAttachmentUrls("");
        }

        workOrderUgcBaseInfoVo.setTargetAmount(crowdfundingInfo.getTargetAmount()/100);
        int amount = 0;
        if (cfCapitalAccount != null){
            amount = cfCapitalAccount.getFundsAmountInFen() / 100;
        }
        workOrderUgcBaseInfoVo.setAmount(amount);
        workOrderUgcBaseInfoVo.setCaseStatus(crowdfundingInfo.getEndTime().before(new Date())?"已结束":"未结束");
        workOrderUgcBaseInfoVo.setMoblie(moblie);

        return workOrderUgcBaseInfoVo;
    }

    public static WorkOrderUgcBaseInfoVo buildProcess(long workOrderId, long adminTaskUgcId, String reason, AdminUGCTask.Content content,
                                                      CrowdFundingProgress crowdFundingProgress, Set<String> sensitiveWords, int taskUgcResult,
        CrowdfundingInfo crowdfundingInfo,CfCapitalAccount cfCapitalAccount,String moblie) {
        WorkOrderUgcBaseInfoVo workOrderUgcBaseInfoVo = new WorkOrderUgcBaseInfoVo();
        workOrderUgcBaseInfoVo.setWorkOrderId(workOrderId);
        workOrderUgcBaseInfoVo.setWorkOrderUgcId(adminTaskUgcId);
        workOrderUgcBaseInfoVo.setUgcResult(taskUgcResult);
        workOrderUgcBaseInfoVo.setType(content.getCode());
        workOrderUgcBaseInfoVo.setReason(reason);
        workOrderUgcBaseInfoVo.setInfoId(crowdFundingProgress.getActivityId());
        workOrderUgcBaseInfoVo.setTitle(crowdFundingProgress.getTitle());
        workOrderUgcBaseInfoVo.setContent(crowdFundingProgress.getContent());
        workOrderUgcBaseInfoVo.setSensitiveWords(Joiner.on(",").join(sensitiveWords));
        workOrderUgcBaseInfoVo.setAttachmentUrls(crowdFundingProgress.getImageUrls());

        workOrderUgcBaseInfoVo.setTargetAmount(crowdfundingInfo.getTargetAmount()/100);
        int amount = 0;
        if (cfCapitalAccount != null){
            amount = cfCapitalAccount.getFundsAmountInFen() / 100;
        }
        workOrderUgcBaseInfoVo.setAmount(amount);
        workOrderUgcBaseInfoVo.setCaseStatus(crowdfundingInfo.getEndTime().before(new Date())?"已结束":"未结束");
        workOrderUgcBaseInfoVo.setMoblie(moblie);

        return workOrderUgcBaseInfoVo;
    }

}
