package com.shuidihuzhu.cf.vo.approve;

import com.shuidihuzhu.cf.model.crowdfunding.CfHospitalAuditInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CfHospitalAuditInfoTel;
import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CfHospitalAuditInfoVO {

    @ApiModelProperty("医院核实详情")
    private CfHospitalAuditInfo cfHospitalAuditInfo;

    @ApiModelProperty("发起人账户内手机号掩码（后台用）")
    private NumberMaskVo raiserMobileMask;

    @ApiModelProperty("是否为新版")
    private boolean isNew;

    @ApiModelProperty("科室座机号码")
    private List<CfHospitalAuditInfoTel> cfHospitalAuditInfoTels;

    public CfHospitalAuditInfoVO() {
    }

}