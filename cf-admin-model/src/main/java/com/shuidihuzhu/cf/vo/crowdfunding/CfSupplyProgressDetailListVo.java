package com.shuidihuzhu.cf.vo.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyAction;
import com.shuidihuzhu.pf.common.v2.model.pagehelper.PaginationVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-01-10 11:25
 **/
@ApiModel("下发动态列表")
@Data
public class CfSupplyProgressDetailListVo {

    @ApiModelProperty("待审核任务")
    private int unHandleWorkNum;

    @ApiModelProperty("列表")
    private List<CfSupplyProgressDetail> details;

    @ApiModelProperty("分页信息")
    private PaginationVO paginationVO;

    @ApiModel("列表")
    @Data
    public static class CfSupplyProgressDetail {

        @ApiModelProperty("下发信息")
        private CfInfoSupplyAction cfInfoSupplyAction;

        @ApiModelProperty("infoUUId")
        private String infoUUId;

        @ApiModelProperty("案例标题")
        private String caseTitle;

        @ApiModelProperty("下发人名称")
        private String supplyOperatorName;

        @ApiModelProperty("材料审核状态")
        private int caseStatus;

        @ApiModelProperty("下发相关的描述")
        private List<String> supplyReasons;

        @ApiModelProperty("工单创建时间")
        private Date workCreateTime;

        @ApiModelProperty("操作人+组织")
        private String operatorAndOrgName;

        @ApiModelProperty("操作时间")
        private Date optTime;

    }


}
