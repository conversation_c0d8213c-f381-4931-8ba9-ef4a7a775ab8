package com.shuidihuzhu.cf.vo.activity;

import com.shuidihuzhu.cf.activity.model.admin.ActivityCaseAdminVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ActivityCaseAssembleAdminVO {

    @ApiModelProperty("案例筹款信息")
    private ActivityCaseFundAdminVO fundInfo;

    @ApiModelProperty("案例活动信息")
    private ActivityCaseAdminVO activityCaseInfo;

    @ApiModelProperty("是否可以参加巨额配捐")
    private Boolean canJoinHugeActivity;

}
