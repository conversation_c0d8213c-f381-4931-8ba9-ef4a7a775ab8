package com.shuidihuzhu.cf.vo.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-05-21 14:33
 **/
@NoArgsConstructor
@Data
@ApiModel
public class CfReportOfficialLetterLogVo {
    @ApiModelProperty("id")
    private long id;
    @ApiModelProperty("公函id")
    private long letterId;
    @ApiModelProperty("操作用户")
    private long userId;
    @ApiModelProperty("备注")
    private String comment;
    @ApiModelProperty("组织架构")
    private String mis;
    @ApiModelProperty("操作人名")
    private String name;
    @ApiModelProperty("创建时间")
    private Timestamp createTime;
}
