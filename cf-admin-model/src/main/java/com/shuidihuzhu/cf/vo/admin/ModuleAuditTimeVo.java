package com.shuidihuzhu.cf.vo.admin;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/10/15  6:51 下午
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ModuleAuditTimeVo {
    @ApiModelProperty("前置信息审核时间")
    private String preInfoTime;
    @ApiModelProperty("前置信息审核操作人")
    private String preInfoOperateOrgName;
    @ApiModelProperty("图文信息审核时间")
    private String imgWordTime;
    @ApiModelProperty("图文信息审核操作人")
    private String imgWordTimeOperateOrgName;
    @ApiModelProperty("增信信息审核时间")
    private String addCreditTime;
    @ApiModelProperty("增信信息审核操作人")
    private String addCreditOperateOrgName;
    @ApiModelProperty("低保信息审核时间")
    private String lowIncomeTime;
    @ApiModelProperty("低保信息审核操作人")
    private String lowIncomeOperateOrgName;

}
