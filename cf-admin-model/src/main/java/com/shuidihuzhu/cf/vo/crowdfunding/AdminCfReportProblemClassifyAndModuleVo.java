package com.shuidihuzhu.cf.vo.crowdfunding;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/4/21
 */
@Data
public class AdminCfReportProblemClassifyAndModuleVo {
    private String classifyName;
    private int classifyId;
    private List<AdminCfReportProblemModuleVo> adminCfReportProblemModuleVos = Lists.newArrayList();

    public static AdminCfReportProblemClassifyAndModuleVo buildVo(String classifyName, int classifyId,
                                                                  List<AdminCfReportProblemModuleVo> adminCfReportProblemModuleVos){
        AdminCfReportProblemClassifyAndModuleVo adminCfReportProblemModuleVo = new AdminCfReportProblemClassifyAndModuleVo();
        adminCfReportProblemModuleVo.setClassifyName(classifyName);
        adminCfReportProblemModuleVo.setClassifyId(classifyId);
        adminCfReportProblemModuleVo.setAdminCfReportProblemModuleVos(adminCfReportProblemModuleVos);
        return adminCfReportProblemModuleVo;
    }

}
