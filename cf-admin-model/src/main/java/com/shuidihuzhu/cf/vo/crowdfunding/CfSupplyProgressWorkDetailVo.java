package com.shuidihuzhu.cf.vo.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyAction;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyProgress;
import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-01-10 11:25
 **/
@ApiModel("下发动态工单详情")
@Data
public class CfSupplyProgressWorkDetailVo {

    @ApiModelProperty("案例id")
    private int caseId;

    @ApiModelProperty("工单id")
    private int workOrderId;

    @ApiModelProperty("案例标题")
    private String caseTitle;

    @ApiModelProperty("发起人名称")
    private String raiserName;

    @ApiModelProperty("发起人手机号")
    private String raiserMobile;

    @ApiModelProperty("发起人手机号掩码")
    private NumberMaskVo raiserMobileMask;

    @ApiModelProperty("收款人名称")
    private String payeeName;

    @ApiModelProperty("收款人手机号")
    private String payeeMobile;

    @ApiModelProperty("收款人手机号掩码")
    private NumberMaskVo payeeMobileMask;

    @ApiModelProperty("下发信息")
    private CfInfoSupplyAction cfInfoSupplyAction;

    @ApiModelProperty("下发人名称")
    private String supplyerName;

    @ApiModelProperty("下发原因")
    private List<String> supplyReasons;

    @ApiModelProperty("当前处理的动态信息")
    private SupplyProgressRecord currentSupplyProgress;

    @ApiModelProperty("驳回记录")
    private List<SupplyProgressRecord> rejectRecords;

    @ApiModel("操作信息")
    @Data
    public static class SupplyProgressStatusInfo {

        @ApiModelProperty("审核人")
        private String auditor;

        @ApiModelProperty("驳回备注")
        private String rejectComment;

        @ApiModelProperty("审核结果，驳回或者通过")
        private int auditStatus;

        @ApiModelProperty("操作时间")
        private Date operateTime;
    }


    @ApiModel("下发动态以及操作信息")
    @Data
    public static class SupplyProgressRecord {

        @ApiModelProperty("下发动态")
        private CfInfoSupplyProgress cfInfoSupplyProgress;

        @ApiModelProperty("敏感词")
        private String sensitiveWord;

        @ApiModelProperty("操作信息")
        private SupplyProgressStatusInfo supplyProgressStatusInfo;
    }
}
