package com.shuidihuzhu.cf.vo.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfReportProblemLabel;
import lombok.Data;

/**
 * @Auther: subing
 * @Date: 2020/4/21
 */
@Data
public class AdminCfReportProblemModuleVo {
    private String moduleName;
    private int isUse;
    private int sort;
    private int id;
    private int isMandatory;

    public static AdminCfReportProblemModuleVo buildVo(CfReportProblemLabel cfReportProblemLabel){
        if (cfReportProblemLabel == null){
            return new AdminCfReportProblemModuleVo();
        }
        AdminCfReportProblemModuleVo adminCfReportProblemClassifyVo = new AdminCfReportProblemModuleVo();
        adminCfReportProblemClassifyVo.setId(cfReportProblemLabel.getId());
        adminCfReportProblemClassifyVo.setSort(cfReportProblemLabel.getSort());
        adminCfReportProblemClassifyVo.setIsUse(cfReportProblemLabel.getIsUse());
        adminCfReportProblemClassifyVo.setModuleName(cfReportProblemLabel.getLabelDesc());
        adminCfReportProblemClassifyVo.setIsMandatory(cfReportProblemLabel.getIsMandatory());
        return adminCfReportProblemClassifyVo;
    }
}
