package com.shuidihuzhu.cf.vo.approve;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.vo.crowdfunding.AdminApproveVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * @Author: wangpeng
 * @Date: 2023/3/23 15:16
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ApproveAuditParam {
    private int userId;
    private long hospitalAuditWorkOrderId;
    private String commentText;
    private CrowdfundingInfo crowdfundingInfo;
    private AdminApproveVo adminApproveVo;
    private Set<Integer> refuseDataTypeSet;
    // 是否触发多案例收款人逻辑
    private boolean payeeRepeatWarn;
}
