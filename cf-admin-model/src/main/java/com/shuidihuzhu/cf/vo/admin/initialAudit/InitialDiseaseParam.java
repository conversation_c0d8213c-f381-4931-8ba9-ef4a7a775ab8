package com.shuidihuzhu.cf.vo.admin.initialAudit;

import lombok.Data;
import lombok.Getter;

import java.util.List;

@Data
public class InitialDiseaseParam {

    private int caseId;
    private long workOrderId;

    private int userId;

    private String inputDisease;

    private List<String> canRaiseDiseases;
    private List<String> specialDiseases;

    private String treatmentInfo;

    private String multipleTreatmentInfo;

    private int strategyType;

    private int reasonAmountType;


    @Getter
    public static enum DiseaseType  {

        FIRST(1, "caseId调用"),
        INPUT_DISEASES(2, "运营输入疾病调用"),
        ;

        private int code;
        private String desc;

        DiseaseType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }






}
