package com.shuidihuzhu.cf.vo.wx.greeting;


import com.shuidihuzhu.wx.grpc.client.common.GreetingWxGroupAttach;
import com.shuidihuzhu.wx.grpc.client.common.GreetingWxGroupAttachV2;
import com.shuidihuzhu.wx.grpc.model.GreetingWxGroupAttachV2Dto;
import lombok.Data;

/**
 * @Author: duchao
 * @Date: 2018/6/12 下午9:06
 */
@Data
public class GreetingWxGroupAttachVO {
	private int groupId;
	private String content;
	private String key;
	private int matchRule;
	private int greetingId;
	private int attachId;
	private int subBizType;
	private int contentId;

	public static GreetingWxGroupAttachVO convert(GreetingWxGroupAttach attach) {
		GreetingWxGroupAttachVO vo = new GreetingWxGroupAttachVO();
		vo.setGroupId(attach.getWxGroupId());
		vo.setKey(attach.getMatchMethod(0).getKey());
		vo.setMatchRule(attach.getMatchMethod(0).getMatchRuleValue());
		vo.setGreetingId(attach.getGreetingId());
		vo.setAttachId(attach.getAttachId());
		vo.setSubBizType(attach.getSubBizType());
		return vo;
	}
	public static GreetingWxGroupAttachVO convert(GreetingWxGroupAttachV2 attach) {
		GreetingWxGroupAttachVO vo = new GreetingWxGroupAttachVO();
		vo.setGroupId(attach.getWxGroupId());
		vo.setKey(attach.getMatchMethod(0).getKey());
		vo.setMatchRule(attach.getMatchMethod(0).getMatchRuleValue());
		vo.setContentId(attach.getContentId());
		vo.setAttachId(attach.getAttachId());
		vo.setSubBizType(attach.getSubBizType());
		return vo;
	}
	public static GreetingWxGroupAttachVO convert(GreetingWxGroupAttachV2Dto attach) {
		GreetingWxGroupAttachVO vo = new GreetingWxGroupAttachVO();
		vo.setGroupId(attach.getWxGroupId());
		vo.setKey(attach.getMatchMethod().get(0).getKey());
		vo.setMatchRule(attach.getMatchMethod().get(0).getMatchRule().getValue());
		vo.setContentId(attach.getContentId());
		vo.setAttachId(attach.getAttachId());
		vo.setSubBizType(attach.getSubBizType());
		return vo;
	}
}
