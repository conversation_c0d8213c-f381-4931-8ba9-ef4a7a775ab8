package com.shuidihuzhu.cf.vo.approve;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class RiverSendSmsParamVO {

    private int caseId;
    private long workOrderId;
    private String mobile;
    private String content;
    private String modelNum = "";
    private String param;

    private int operatorId;

    public Map<Integer, String> getParamMap() {
        if (StringUtils.isEmpty(param)) {
            return Maps.newHashMap();
        }
        return JSON.parseObject(param, new TypeReference<Map<Integer, String>>() {});
    }

}
