package com.shuidihuzhu.cf.vo.approve;

import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.model.river.RiverRejectReasonVO;
import com.shuidihuzhu.cf.model.river.RiverUsageTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RiverHandleParamVO {

    private int caseId;

    @ApiModelProperty("材料类型")
    private RiverUsageTypeEnum usageTypeEnum;

    @ApiModelProperty("附加评论")
    private String handleComment;

    @ApiModelProperty("停止筹款原因")
    private String stopReason;

    @ApiModelProperty("处理类型")
    private HandleType handleType;

    @ApiModelProperty("工单id")
    private long workOrderId;

    @ApiModelProperty("工单类型")
    private int orderType;

    @ApiModelProperty("驳回原因id")
    private List<Integer> rejectIds;

    @ApiModelProperty("操作人id")
    private int operatorId;

    @ApiModelProperty("是否呼通{1: 呼通, 2:未呼通}")
    private int callStatus;

    // --------------------- 以上为传参 ---------------------

    @ApiModelProperty("驳回详情")
    private Map<Integer, List<RiverRejectReasonVO>> rejectDetail;

    public enum HandleType {

        /**
         * 通过
         */
        @ApiModelProperty("通过")
        PASS(1, "通过", HandleResultEnum.audit_pass, OperationActionTypeEnum.RIVER_PASS, true),

        @ApiModelProperty("驳回")
        REJECT(2, "驳回", HandleResultEnum.audit_reject, OperationActionTypeEnum.RIVER_REFUSE, true),

        @ApiModelProperty("停止筹款")
        STOP_CASE(3, "停止筹款", HandleResultEnum.stop_case, OperationActionTypeEnum.RIVER_STOP_CASE, true),

        @ApiModelProperty("稍后处理")
        HANDLE_LATER(4, "稍后处理", HandleResultEnum.later_doing, OperationActionTypeEnum.RIVER_LATER_DO, false),

        @ApiModelProperty("发送短信")
        SEND_SMS(5, "发送短信", null, OperationActionTypeEnum.RIVER_SEND_SMS, false),

        @ApiModelProperty("手动关闭")
        MANUAL_LOCK(6, "手动关闭", HandleResultEnum.manual_lock, OperationActionTypeEnum.CREDIT_APPROVE_MANUAL_CLOSE, false),
        ;

        @Getter
        private final int value;

        @Getter
        private final String msg;

        @Getter
        private final HandleResultEnum orderResult;

        @Getter
        private final OperationActionTypeEnum operationActionTypeEnum;

        /**
         * 是否记录工单快照
         */
        @Getter
        private final boolean shootOrderSnapshot;

        HandleType(int value, String msg,
                   HandleResultEnum orderResult,
                   OperationActionTypeEnum operationActionTypeEnum,
                   boolean shootOrderSnapshot) {
            this.value = value;
            this.msg = msg;
            this.orderResult = orderResult;
            this.operationActionTypeEnum = operationActionTypeEnum;
            this.shootOrderSnapshot = shootOrderSnapshot;
        }
    }

    /**
     * 根据工单类型获取审核类型
     * @param orderType 工单类型
     * @return 审核类型
     */
    @Nullable
    public static RiverUsageTypeEnum getUsageTypeByOrderType(int orderType){
        if (WorkOrderType.isCreditApprove(orderType)){
            return RiverUsageTypeEnum.CREDIT_INFO;
        }
        return null;
    }
}
