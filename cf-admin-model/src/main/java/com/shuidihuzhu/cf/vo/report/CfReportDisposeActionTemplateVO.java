package com.shuidihuzhu.cf.vo.report;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-09-16 11:28
 **/
@NoArgsConstructor
@Data
public class CfReportDisposeActionTemplateVO {
    /**
     * id
     */
    private long id;
    /**
     * 处理动作id
     */
    private long actionId;
    /**
     * 处理动作
     */
    private String disposeAction;
    /**
     * 模板标题
     */
    private String title;
    /**
     * 模板内容
     */
    private String content;
    /**
     * 承诺内容
     */
    private String commitmentContent;
    /**
     * 是否支持待录入
     */
    private boolean isHelp;
    /**
     * 有无模板
     */
    private boolean hasTemplate;
    /**
     * 模板id
     */
    private long templateId;
    /**
     * 分类名
     */
    private String actionClassify;

    public CfReportDisposeActionTemplateVO(long actionId, String title, String content, String commitmentContent) {
        this.actionId = actionId;
        this.title = title;
        this.content = content;
        this.commitmentContent = commitmentContent;
    }
}
