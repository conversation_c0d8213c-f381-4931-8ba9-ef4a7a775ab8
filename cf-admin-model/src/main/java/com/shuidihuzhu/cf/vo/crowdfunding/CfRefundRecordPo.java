package com.shuidihuzhu.cf.vo.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.NewCfRefundRecord;
import org.springframework.beans.BeanUtils;

/**
 * Created by Ahrievil on 2017/11/13
 */
public class CfRefundRecordPo extends NewCfRefundRecord {

    private int bizType;
    private int thirdType;
    private int mainBody;

    public CfRefundRecordPo() {
    }

    public CfRefundRecordPo(NewCfRefundRecord cfRefundRecord, int bizType, int thirdType) {
        BeanUtils.copyProperties(cfRefundRecord, this);
        this.bizType = bizType;
        this.thirdType = thirdType;
    }

    public int getBizType() {
        return bizType;
    }

    public void setBizType(int bizType) {
        this.bizType = bizType;
    }

    public int getThirdType() {
        return thirdType;
    }

    public void setThirdType(int thirdType) {
        this.thirdType = thirdType;
    }
}
