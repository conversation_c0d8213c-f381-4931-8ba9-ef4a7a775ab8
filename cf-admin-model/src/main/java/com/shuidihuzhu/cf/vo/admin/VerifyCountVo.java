package com.shuidihuzhu.cf.vo.admin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/11/4
 */
@Data
@ApiModel("系统限制Vo")
public class VerifyCountVo {

    @ApiModelProperty("限制相关的key")
    private String key;

    @ApiModelProperty("节点名称")
    private String name;

    @ApiModelProperty("类型 1:身份证认证 2:银行卡认证")
    private int type;

    @ApiModelProperty("当前已使用数量")
    private int currentCount;

    @ApiModelProperty("限制数量")
    private int limitCount;

}
