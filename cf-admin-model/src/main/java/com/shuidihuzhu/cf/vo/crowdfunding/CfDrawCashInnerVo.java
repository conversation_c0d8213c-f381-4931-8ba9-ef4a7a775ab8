package com.shuidihuzhu.cf.vo.crowdfunding;

import com.alibaba.fastjson.JSON;

import java.sql.Timestamp;

/**
 * Created by lixuan on 2017/2/15.
 */
public class CfDrawCashInnerVo {
    private String infoUuid;
    private double amount;
    private String name;
    private String bankCard;
    private Timestamp createTime;
    private String operatorName;
    private String remark;
    private Integer drawStatus;

    public String getInfoUuid() {
        return infoUuid;
    }

    public void setInfoUuid(String infoUuid) {
        this.infoUuid = infoUuid;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBankCard() {
        return bankCard;
    }

    public void setBankCard(String bankCard) {
        this.bankCard = bankCard;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getDrawStatus() {
        return drawStatus;
    }

    public void setDrawStatus(Integer drawStatus) {
        this.drawStatus = drawStatus;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
