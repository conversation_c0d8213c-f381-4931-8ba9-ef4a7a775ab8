package com.shuidihuzhu.cf.vo.mina;

import java.util.Date;

public class CfMinaTopicVo {
    private int id;
    private int shareNum;
    private int commentNum;
    private int praiseNum;
    private int topCommentNum;
    private String title;
    private Date showDate;

    public CfMinaTopicVo() {
    }

    public CfMinaTopicVo(int id, int shareNum, int commentNum, int praiseNum, int topCommentNum, String title, Date showDate) {
        this.id = id;
        this.shareNum = shareNum;
        this.commentNum = commentNum;
        this.praiseNum = praiseNum;
        this.topCommentNum = topCommentNum;
        this.title = title;
        this.showDate = showDate;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getShareNum() {
        return shareNum;
    }

    public void setShareNum(int shareNum) {
        this.shareNum = shareNum;
    }

    public int getCommentNum() {
        return commentNum;
    }

    public void setCommentNum(int commentNum) {
        this.commentNum = commentNum;
    }

    public int getTopCommentNum() {
        return topCommentNum;
    }

    public void setTopCommentNum(int topCommentNum) {
        this.topCommentNum = topCommentNum;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Date getShowDate() {
        return showDate;
    }

    public void setShowDate(Date showDate) {
        this.showDate = showDate;
    }

    public int getPraiseNum() {
        return praiseNum;
    }

    public void setPraiseNum(int praiseNum) {
        this.praiseNum = praiseNum;
    }

    @Override
    public String toString() {
        return "CfMinaTopicVo{" +
                "id=" + id +
                ", shareNum=" + shareNum +
                ", commentNum=" + commentNum +
                ", praiseNum=" + praiseNum +
                ", topCommentNum=" + topCommentNum +
                ", title='" + title + '\'' +
                ", showDate=" + showDate +
                '}';
    }
}
