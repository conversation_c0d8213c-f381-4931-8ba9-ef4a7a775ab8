package com.shuidihuzhu.cf.vo.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.material.AuditSuggestModifyDetail;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by Ahrievil on 2017/9/18
 */
@Data
public class AdminApproveVo {

    private List<Integer> passIds;
    private List<Integer> refuseIds;
    private long yanhouWorkOrderId;
    private int orderType;

    private List<AuditSuggestModifyDetail> suggestModifyDetails;

    private Map<String, Boolean> subItemStatus;

    private int reasonCode;

    private String reasonMsg;

    private int followLabel;

    private String followMsg;

    private int handleResult;

    private boolean sendMsgFlag;

    private String followRemark;

    private String reasonRemark;

    /**
     * 筹款用途审核模块备注
     */
    private String fundUseRemark;

    private Boolean createFuWuWorkOrder;

    private long controlRecordId;


}
