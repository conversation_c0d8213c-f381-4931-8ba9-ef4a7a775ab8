package com.shuidihuzhu.cf.vo.approve;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CaseInteractionDetailVO {

//    @ApiModelProperty("各个环节互动次数列表")
//    private List<InteractionCountNodeVO> interactionCountList;

    @ApiModelProperty("互动详情列表")
    private List<InteractionDataVO> dataList;

    @ApiModelProperty("每个环节互动次数列表")
    private List<InteractionCountNodeVO> countList;


}
