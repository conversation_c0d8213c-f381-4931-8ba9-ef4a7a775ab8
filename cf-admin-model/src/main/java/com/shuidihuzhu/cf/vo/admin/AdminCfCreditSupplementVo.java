package com.shuidihuzhu.cf.vo.admin;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.model.crowdfunding.CfCreditSupplement;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class AdminCfCreditSupplementVo {
    private int infoId;
    private String infoUuid;
    private String title;
    private String applicantMobile;
    private long applicantUserId;
    private String fundingNickname;
    private String targetAmountYuan;
    private String operatorName;
    private int infoStatus;
    private int creditSupplementStatus;
    private Date createTime;
    private Date submitTime;
    private List<CfCreditSupplement> cfCreditSupplementList = Lists.newArrayList();
}
