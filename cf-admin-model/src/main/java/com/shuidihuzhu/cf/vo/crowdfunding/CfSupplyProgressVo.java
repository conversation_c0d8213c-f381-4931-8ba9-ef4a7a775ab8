package com.shuidihuzhu.cf.vo.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSupplyAction;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import io.swagger.annotations.*;
import lombok.Data;
import lombok.Getter;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-01-10 11:25
 **/
@ApiModel("下发信息")
@Data
public class CfSupplyProgressVo {

    @ApiModelProperty("下发信息")
    private CfInfoSupplyAction cfInfoSupplyAction;

    @ApiModelProperty("下发原因,勾选项详细")
    private List<String> supplyReasons;

    @ApiModelProperty("相关动态工单信息")
    private List<ProgressWorkOrderInfo> relationWorkList;

    @ApiModelProperty("是否展示撤销,true:展示")
    private boolean showCancelAction = false;

    @ApiModelProperty("下发人名称")
    private String supplyOperatorName;

    @ApiModelProperty("相关动态工单数量")
    private int relationWorkNum;

    @Data
    @ApiModel("下发按钮显示信息")
    public static class SupplyProgressButtonInfo {
        @ApiModelProperty("标号,前端用于判断调用那个接口,1:调用2:调用")
        private int id;
        @ApiModelProperty("下发id,只有当展示‘相关动态’时才有相应的值")
        private long supplyId;
        @ApiModelProperty("显示‘下发动态’还是‘相关动态’")
        private String buttonMsg;
    }

    @Data
    @ApiModel("相关动态工单信息")
    public static class ProgressWorkOrderInfo extends WorkOrderVO{
        @ApiModelProperty("文字内容")
        private String content;
        @ApiModelProperty("图片")
        private String imgUrls;
        @ApiModelProperty("敏感词")
        private String sensitiveWord;
    }

    @Getter
    public enum SupplyButtonMsgEnum {
        button_msg_1(1, "下发动态"),
        button_msg_2(2, "相关动态"),
        ;
        private int code;
        private String buttonMsg;

        SupplyButtonMsgEnum(int code, String buttonMsg) {
            this.code = code;
            this.buttonMsg = buttonMsg;
        }


    }
}
