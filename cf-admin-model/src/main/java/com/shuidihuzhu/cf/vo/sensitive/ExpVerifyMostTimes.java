package com.shuidihuzhu.cf.vo.sensitive;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 异常多次证实识别策略
 * <AUTHOR>
 * @date 2020-11-25 15:53:31
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExpVerifyMostTimes {

    @ApiModelProperty(value = "证实人id", required = true)
    private Long verifyUserId;

    @ApiModelProperty(value = "caseId", required = true)
    private Integer caseId;

    @ApiModelProperty(value = "超过当前证实数", required = true)
    private Boolean excessVerify;

    @ApiModelProperty(value = "证实人手机号", required = true)
    private String cipherVerifyMobile;

    @ApiModelProperty(value = "加密证实身份证号", required = true)
    private String cipherVerificationIdCard;


}