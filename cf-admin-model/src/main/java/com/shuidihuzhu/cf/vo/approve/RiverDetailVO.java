package com.shuidihuzhu.cf.vo.approve;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class RiverDetailVO<InfoVO> {

    @ApiModelProperty("详情数据")
    private InfoVO info;

    @ApiModelProperty("处理参数快照")
    private RiverHandleParamVO handleParam;

    @ApiModelProperty("是否通过")
    private boolean passed;

    @ApiModelProperty("当前驳回ids")
    private List<Integer> rejectIds;

    public RiverDetailVO(InfoVO info, RiverHandleParamVO handleParam, boolean passed, List<Integer> rejectIds) {
        this.info = info;
        this.handleParam = handleParam;
        this.passed = passed;
        this.rejectIds = rejectIds;
    }
}
