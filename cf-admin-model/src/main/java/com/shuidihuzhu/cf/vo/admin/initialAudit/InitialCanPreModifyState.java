package com.shuidihuzhu.cf.vo.admin.initialAudit;

import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoDataStatusTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;
import java.util.Set;

@Data
public class InitialCanPreModifyState {

    @ApiModelProperty("每一项材料可以代修改的字段")
    private Map<CrowdfundingInfoDataStatusTypeEnum, Set<String>> canModifyFields;
    @ApiModelProperty("用户已经提交的材料项")
    private Set<CrowdfundingInfoDataStatusTypeEnum> hasSubmitMaterials;
//    @ApiModelProperty("用户已经审核通过材料项")
//    private Set<String> hasPassMaterials;

    public static InitialCanPreModifyState create() {
        return new InitialCanPreModifyState();
    }

    public InitialCanPreModifyState withCanModifyFields(Map<CrowdfundingInfoDataStatusTypeEnum, Set<String>> canModifyFields) {
        this.canModifyFields = canModifyFields;
        return this;
    }

    public InitialCanPreModifyState withHasSubmitMaterials(Set<CrowdfundingInfoDataStatusTypeEnum> hasSubmitMaterials) {
        this.hasSubmitMaterials = hasSubmitMaterials;
        return this;
    }


}
