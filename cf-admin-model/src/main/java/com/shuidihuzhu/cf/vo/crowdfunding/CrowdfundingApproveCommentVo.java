package com.shuidihuzhu.cf.vo.crowdfunding;

import com.shuidihuzhu.cf.enums.approve.ApproveCommentTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Author: wuxinlong
 * @Since: 2016-11-25
 */
@Data
public class CrowdfundingApproveCommentVo {
    private String operator;
    private Date oprtime;
    private String comment;
    private int oprid;

    /**
     * 运营组名
     */
    private String organization;

    /**
     * 仅用于工单分组评论
     * 工单id
     */
    private long workOrderId;

    @ApiModelProperty("评论类型")
    private ApproveCommentTypeEnum typeEnum;
    private int approveId;
    private int approveStatus;

}
