package com.shuidihuzhu.cf.vo.report;

import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import com.shuidihuzhu.cf.model.crowdfunding.CfSendProveSnapshotBO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-09-25 14:42
 **/
@Data
public class CfSendProveInfoRecord {

    /**
     * 发送时间
     */
    private Date sendTime;
    /**
     * 提交时间
     */
    private Date submitTime;
    /**
     * 审核时间
     */
    private Date auditTime;
    /**
     * 上传者手机号
     */
    private String mobile;
    private NumberMaskVo mobileMask;
    /**
     * 补充证明快照
     */
    private List<CfSendProveSnapshotBO> cfSendProveSnapshotBO;
}
