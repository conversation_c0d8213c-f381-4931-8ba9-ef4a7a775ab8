package com.shuidihuzhu.cf.vo.approve;

import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminCrowdfundingInfoView;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

@Data
public class RecoverRepeatCaseVo {
    private AdminCrowdfundingInfoView adminCrowdfundingInfoView;
    private int repeatType;

    @Getter
    @AllArgsConstructor
    public enum RepeatScenarioEnum {
        SCEN1(1, "A案例患者身份证+患者姓名 = B案例患者身份证+患者姓名"),
        SCEN2(2, "A案例患者身份证+患者姓名 = B案例患者出生证+患者姓名"),
        SCEN3(3, "A案例患者出生证+患者姓名 = B案例患者身份证+患者姓名"),
        SCEN4(4, "A案例患者出生证+患者姓名 = B案例患者出生证+患者姓名"),
        SCEN5(5, "A案例发起人身份证+患者姓名 = B案例发起人身份证+患者姓名"),
        SCEN6(6, "A案例发起人userId + 患者姓名 = B案例发起人userId + 患者姓名"),
        SCEN7(7, "A案例代录入手机号码 + 患者姓名 = B案例代录入手机号码 + 患者姓名");
        private int code;
        private String desc;

    }
}