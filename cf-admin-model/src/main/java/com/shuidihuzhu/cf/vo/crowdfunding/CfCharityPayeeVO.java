package com.shuidihuzhu.cf.vo.crowdfunding;

import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import com.shuidihuzhu.cf.client.feign.cipher.constants.DesensitizeEnum;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.model.crowdfunding.CfCharityPayee;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.Optional;

/**
 * 公益机构收款信息
 *
 * <AUTHOR>
 * @since 2024/10/23
 */
@Data
public class CfCharityPayeeVO {
    private long id;
    private String infoUuid;

    private int caseId;

    private String orgName;

    private NumberMaskVo orgMobileMask;

    private String orgBankName;

    private String orgBankBranchName;

    private NumberMaskVo orgBankCardMask;

    //图片地址 多个逗号分隔
    private String orgPic;

    private CfCharityPayeeVO() {}

    public static CfCharityPayeeVO build(CfCharityPayee cfCharityPayee, MaskUtil maskUtil) {
        if (Objects.isNull(cfCharityPayee) || Objects.isNull(maskUtil)) {
            return null;
        }
        CfCharityPayeeVO vo = new CfCharityPayeeVO();
        vo.setId(cfCharityPayee.getId());
        vo.setInfoUuid(cfCharityPayee.getInfoUuid());
        vo.setCaseId(cfCharityPayee.getCaseId());
        vo.setOrgName(cfCharityPayee.getOrgName());
        vo.setOrgMobileMask(Optional.ofNullable(cfCharityPayee.getOrgMobile())
                .filter(StringUtils::isNotBlank)
                .map(maskUtil::buildByDecryptPhone)
                .orElse(null)
        );
        vo.setOrgBankName(cfCharityPayee.getOrgBankName());
        vo.setOrgBankBranchName(cfCharityPayee.getOrgBankBranchName());
        vo.setOrgBankCardMask(
                Optional.ofNullable(cfCharityPayee.getOrgBankCard())
                        .filter(StringUtils::isNotBlank)
                        .map(r -> maskUtil.buildByDecryptStrAndType(r, DesensitizeEnum.BANKNO))
                        .orElse(null)
        );
        vo.setOrgPic(cfCharityPayee.getOrgPic());
        return vo;
    }

}
