package com.shuidihuzhu.cf.vo.crowdfunding;

import com.shuidihuzhu.cf.model.admin.channel.CfChannel;
import com.shuidihuzhu.cf.model.admin.channel.CfChannelGroup;

/**
 * Created by wangsf on 17/3/29.
 */
public class CfQrCodeVo {

	private int id;
	private String scene;
	private String description;
	private String url;
	private String descr;
	private CfChannel channel;
	private CfChannelGroup channelGroup;
	private String operator;

	public CfQrCodeVo(int id, String scene, String description, String url, String operator, String descr) {
		this.id = id;
		this.scene = scene;
		this.description = description;
		this.url = url;
		this.operator = operator;
		this.descr = descr;
	}

	public String getOperator() {
		return operator;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getScene() {
		return scene;
	}

	public void setScene(String scene) {
		this.scene = scene;
	}

	public String getDescr() {
		return descr;
	}

	public void setDescr(String descr) {
		this.descr = descr;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public CfChannel getChannel() {
		return channel;
	}

	public void setChannel(CfChannel channel) {
		this.channel = channel;
	}

	public CfChannelGroup getChannelGroup() {
		return channelGroup;
	}

	public void setChannelGroup(CfChannelGroup channelGroup) {
		this.channelGroup = channelGroup;
	}
}
