package com.shuidihuzhu.cf.vo.crowdfunding;

import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @time 2019/11/1 下午4:21
 * @desc
 */
@Data
public class CfOrderVo extends CrowdfundingOrderVo {
    //0:正常展示 1:仅自己可见
    private int status;
    // 中间四位掩码
    private String mobile;

    @ApiModelProperty("原始手机号")
    private String originMobile;

    private NumberMaskVo originMobileMask;

    /**
     * 订单关联资助金额
     */
    private String contributeAmount;

    /**
     * 支付方式
     */
    private String payModeDesc;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }
}
