package com.shuidihuzhu.cf.vo.crowdfunding;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * @Description:
 * @Author: pangh<PERSON>ui
 * @Date: 2023/11/3 3:25 PM
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SubsidyApplyRecordVO {

    private Long workOrderId;

    private Long caseId;

    /**
     * 审核结果
     */
    private String auditResult;

    /**
     * 处理备注
     */
    private String handleRemark;

    private Long operatorId;

    private String operatorName;

    /**
     * 组织快照
     */
    private String organization;

    private Timestamp createTime;

}
