package com.shuidihuzhu.cf.vo.activity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class ActivityCaseFundAdminVO {

    @ApiModelProperty("案例id")
    private int caseId;
    @ApiModelProperty("案例infoUuid")
    private String infoUuid;
    @ApiModelProperty("案例标题")
    private String title;
    @ApiModelProperty("案例是否结束")
    private boolean end;
    @ApiModelProperty("案例发起时间")
    private Date raiseTime;

}
