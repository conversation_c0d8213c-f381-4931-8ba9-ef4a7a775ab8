package com.shuidihuzhu.cf.vo.crowdfunding;

/**
 * @Author: duchao
 * @Date: 2018/6/21 下午3:07
 */
public enum DrawCashVoStatusEnum {
	BUILD(1, "生成"),
	CANNOT_PAY(2, "付不了"),
	FINANCE_HAS_NOT_PAY(3, "财务还未付款"),
	PAY_IN_PROGRESS(4, "付款中"),
	RESET(5, "重置"),
	SEE_DETAIL(6, "查看"),
	;

	private int code;
	private String name;

	DrawCashVoStatusEnum(int code, String name) {
		this.code = code;
		this.name = name;
	}

	public int getCode() {
		return code;
	}

	public String getName() {
		return name;
	}
}
