package com.shuidihuzhu.cf.vo.activity;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MultiAddActivityWaveVO {

    private long activityId;
    private Date startTime;
    private Date endTime;

    /**
     * 提现门槛
     */
    @ApiModelProperty("补贴金额提现门槛 单位分")
    private int threshold;
    @ApiModelProperty("捐款金额们门槛 单位分")
    private int donateMoneyThreshold;
    @ApiModelProperty("捐单次数门槛")
    private int donateCountThreshold;

    /**
     * key area 硬帽 案例最大补贴数量限制(非金额)
     */
    private int caseHardAmountLimit;

    /**
     * {@link com.shuidihuzhu.cf.enums.activity.wave.WaveJoinTypeEnum}
     */
    @ApiModelProperty("参加方式{1: 初审自动加入,2: 机器人申请}")
    private int joinType;

    @ApiModelProperty("每月名额")
    private int monthlyPlace;

    @ApiModelProperty("最大参加案例数 -1表示不限制")
    private int maxPlace;

    private List<WaveItem> waveItemList = Lists.newArrayList();

    @Data
    public static class WaveItem {
        /**
         * {@link com.shuidihuzhu.cf.enums.activity.ActivityWaveTypeEnum}
         */
        @Deprecated
        @ApiModelProperty("已废弃 请使用scope替代")
        private int waveType;
        private int cityId;
        @ApiModelProperty("医院唯一标示")
        private String hospitalCode;
        @ApiModelProperty("医院名称")
        private String hospitalName;
        /**
         * {@link com.shuidihuzhu.cf.enums.activity.wave.WaveScopeEnum}
         */
        @ApiModelProperty("参加范围{1: all,11: province, 12: city, 13: hospital}")
        private int scope;


    }
}
