package com.shuidihuzhu.cf.vo.approve;

import com.shuidihuzhu.cf.client.ugc.caseprocessstatus.model.CaseProcessStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 */
@ApiModel("互动次数VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InteractionCountNodeVO {

    @ApiModelProperty("互动环节枚举")
    private CaseProcessStatusEnum caseProcessStatus;

    @ApiModelProperty("时间")
    private Date actionTime;

    @ApiModelProperty("互动环节对应的互动次数")
    private int actionCount;
}
