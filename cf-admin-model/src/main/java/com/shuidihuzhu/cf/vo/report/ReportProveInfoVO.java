package com.shuidihuzhu.cf.vo.report;

import com.shuidihuzhu.cf.enums.crowdfunding.AddTrustAuditStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfSendProve;
import com.shuidihuzhu.cf.model.crowdfunding.CfSendProveSnapshotBO;
import com.shuidihuzhu.cf.model.crowdfunding.CfSendProveTemplate;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfReportAddTrustDisposeVo;
import lombok.Data;

import java.util.List;

/**
 * 迁移
 *
 * <AUTHOR>
 */
@Data
public class ReportProveInfoVO {

    private List<CfSendProveTemplate> template;

    private CfSendProve cfSendProve;

    private CfSendProveSnapshotBO snapshot;

    /**
     * 补充证明状态
     *
     * @see AddTrustAuditStatusEnum
     */
    private Integer credibleStatus;

    private List<CfReportAddTrustDisposeVo> proveDisposeAction;
}
