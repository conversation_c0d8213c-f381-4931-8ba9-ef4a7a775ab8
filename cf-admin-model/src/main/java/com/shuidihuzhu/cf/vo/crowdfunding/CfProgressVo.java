package com.shuidihuzhu.cf.vo.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdFundingProgressVo;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/11/1 下午2:22
 * @desc
 */
public class CfProgressVo extends CrowdFundingProgressVo {
    private long workOrderId;
    private int status;
    private String maskImageUrls;

    private List<Integer> watermarks;

    public long getWorkOrderId() {
        return workOrderId;
    }

    public void setWorkOrderId(long workOrderId) {
        this.workOrderId = workOrderId;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public List<Integer> getWatermarks() {
        return watermarks;
    }

    public void setWatermarks(List<Integer> watermarks) {
        this.watermarks = watermarks;
    }

    public String getMaskImageUrls() {
        return maskImageUrls;
    }

    public void setMaskImageUrls(String maskImageUrls) {
        this.maskImageUrls = maskImageUrls;
    }
}
