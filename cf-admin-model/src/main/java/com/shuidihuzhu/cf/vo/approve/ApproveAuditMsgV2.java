package com.shuidihuzhu.cf.vo.approve;

import com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.vo.crowdfunding.AdminApproveVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: wangpeng
 * @Date: 2023/3/27 15:07
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ApproveAuditMsgV2 {
    private AdminApproveVo adminApproveVo;
    private int caseId;
    private int status;
    private int userId;
    private long hospitalAuditWorkOrderId;
    private String commentText;
    private String refuseComment;
    private String userName;
    private CrowdfundingInfo beforeCrowdfundingInfo;
    private CfOperatingRecord cfOperatingRecord;
}
