package com.shuidihuzhu.cf.vo.message;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * @package: com.shuidihuzhu.cf.vo.message
 * @Author: l<PERSON><PERSON>aw<PERSON>
 * @Date: 2019-03-22  17:27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ArticleResultItem {
    private String date;
    private String mediaId;
    private List<String> articleTitleList;
}
