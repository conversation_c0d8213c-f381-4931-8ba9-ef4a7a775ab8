package com.shuidihuzhu.cf.vo.approve;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@ApiModel("OCR识别结果")
@Data
public class InitialAuditOCRResultVO {

    @ApiModelProperty("患者名称")
    private String patientName;

    @ApiModelProperty("患者名称是否相同")
    private boolean samePatientName;

    @ApiModelProperty("日期")
    private Date date;

    @ApiModelProperty("日期是否是一年内")
    private boolean newlyDate;

    @ApiModelProperty("日期字符串")
    private String dateStr;

    @ApiModelProperty("医院名称")
    private String hospitalName;

    @ApiModelProperty("医院名称存在是否")
    private Boolean hospitalExist;

    @ApiModelProperty("科室名称")
    private String departmentName;

}
