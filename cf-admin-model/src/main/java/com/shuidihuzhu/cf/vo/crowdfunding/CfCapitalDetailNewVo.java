package com.shuidihuzhu.cf.vo.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.AdminCapitalStatusInfo;

import java.sql.Timestamp;

import lombok.Data;

/**
 * Created by niejiangnan on 2017/10/31.
 */
@Data
public class CfCapitalDetailNewVo {
    private int drawStatus;
    private int refundStatus;
    private String reason;
    private Timestamp operatingTime;
    private String operator;
    private String status;
    private int role;

    public CfCapitalDetailNewVo(String reason, Timestamp operatingTime, String operator) {
        this.reason = reason;
        this.operatingTime = operatingTime;
        this.operator = operator;
    }

    public CfCapitalDetailNewVo() {
    }

    public void setDrawStatusEnum(AdminCapitalStatusInfo.DrawCashStatus drawStatusEnum) {
        this.drawStatus = drawStatusEnum.getCode();
        this.status = drawStatusEnum.getWord();
    }
}
