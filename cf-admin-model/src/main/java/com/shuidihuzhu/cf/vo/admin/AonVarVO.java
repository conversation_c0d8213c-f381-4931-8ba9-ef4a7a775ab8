package com.shuidihuzhu.cf.vo.admin;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.client.ugc.model.domain.aon.AonVarDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AonVarVO extends AonVarDO{

    @ApiModelProperty("最新操作人")
    private String newestOperator;

    public static AonVarVO create(AonVarDO data, String newestOperator) {
        AonVarVO vo = JSON.parseObject(JSON.toJSONString(data), AonVarVO.class);//已检查过
        vo.newestOperator = newestOperator;
        return vo;
    }
}
