package com.shuidihuzhu.cf.vo.crowdfunding;

import com.shuidihuzhu.cf.model.goods.GoodsGear;
import org.springframework.beans.BeanUtils;

/**
 * Created by Ahrievil on 2017/9/13
 */
public class AdminGoodsGear extends GoodsGear {
    private String amountInFen;

    public AdminGoodsGear() {
    }

    public static AdminGoodsGear getAdminGoodsGear(GoodsGear goodsGear) {
        AdminGoodsGear adminGoodsGear = new AdminGoodsGear();
        BeanUtils.copyProperties(goodsGear, adminGoodsGear);
        return adminGoodsGear;
    }

    public String getAmountInFen() {
        return amountInFen;
    }

    public void setAmountInFen(String amountInFen) {
        this.amountInFen = amountInFen;
    }
}
