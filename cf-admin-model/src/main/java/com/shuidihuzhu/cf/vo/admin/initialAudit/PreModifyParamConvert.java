package com.shuidihuzhu.cf.vo.admin.initialAudit;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.client.feign.cipher.constants.DesensitizeEnum;
import com.shuidihuzhu.cf.client.material.model.CfContentInfoModel;
import com.shuidihuzhu.cf.client.material.model.CfPropertyInsuranceInfoModel;
import com.shuidihuzhu.cf.client.material.model.preSubmit.CfMaterialModifyGroup;
import com.shuidihuzhu.cf.client.material.model.preSubmit.CfMaterialPreModifyHandleVo;
import com.shuidihuzhu.cf.client.material.model.preSubmit.CfRejectPositionFields;
import com.shuidihuzhu.cf.client.material.utils.MaterialCollectionUtils;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.enums.crowdfunding.BaseInfoTemplateConst;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoDataStatusTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.cf.vo.approve.CreditInfoVO;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Service
public class PreModifyParamConvert {

    @Autowired
    private OldShuidiCipher oldShuidiCipher;
    @Resource
    private MaskUtil maskUtil;

    public List<InitialPreModifyHandleHistory> convertHisListFromCfHandleVos(
            List<CfMaterialPreModifyHandleVo> materialVos, ShuidiCipher shuidiCipher) {

        if (CollectionUtils.isEmpty(materialVos)) {
            return Lists.newArrayList();
        }

        List<InitialPreModifyHandleHistory> handleResult = Lists.newArrayList();
        for (CfMaterialPreModifyHandleVo handleVo : materialVos) {
            handleResult.add(convertHistoryFromCfHandleVo(handleVo, shuidiCipher));
        }

        return handleResult;
    }

    public InitialPreModifyHandleHistory convertHistoryFromCfHandleVo(CfMaterialPreModifyHandleVo materialVo,
                                                                             ShuidiCipher shuidiCipher) {
        if (materialVo == null) {
            return null;
        }

        InitialPreModifyHandleHistory history = new InitialPreModifyHandleHistory();
        history.setCaseId(materialVo.getCaseId());
        history.setUserId(materialVo.getUserId());
        history.setOrgName(materialVo.getOrgName());
        history.setOperateTime(materialVo.getOperateTime());
        history.setWorkOrderId(materialVo.getWorkOrderId());

        history.setUserSourceMaterials(convertFromPreModifyMaterial(materialVo.getUserSourceMaterials(), shuidiCipher));
        history.setModifyMaterials(convertFromPreModifyMaterial(materialVo.getModifyMaterials(), shuidiCipher));

        history.setCanModifyFields(materialVo.getCanModifyFields());
        if (MapUtils.isNotEmpty(materialVo.getHasModifyFields())) {
            Map<CrowdfundingInfoDataStatusTypeEnum, Set<String>> hasModifyFields = Maps.newHashMap();
            for (Map.Entry<CrowdfundingInfoDataStatusTypeEnum, CfRejectPositionFields> modifyField :
                    materialVo.getHasModifyFields().entrySet()) {

                hasModifyFields.put(modifyField.getKey(), MaterialCollectionUtils.generateValueSet(
                        modifyField.getValue().getRejectPositionForSea()));
            }

            history.setHasModifyFields(hasModifyFields);
        }

        return history;
    }

    public CfMaterialPreModifyHandleVo convertFromInitialHandleSeaVo(InitialPreModifyHandleVo initialSeaVo) {
        if (initialSeaVo == null) {
            return null;
        }

        CfMaterialPreModifyHandleVo preModifyVo = new CfMaterialPreModifyHandleVo();

        preModifyVo.setCaseId(initialSeaVo.getCaseId());
        preModifyVo.setUserId(initialSeaVo.getUserId());
        preModifyVo.setWorkOrderId(initialSeaVo.getWorkOrderId());

        preModifyVo.setUserSourceMaterials(convertFromInitialMaterialSeaVo(initialSeaVo.getCaseId(), initialSeaVo.getUserSourceMaterials()));
        preModifyVo.setModifyMaterials(convertFromInitialMaterialSeaVo(initialSeaVo.getCaseId(), initialSeaVo.getModifyMaterials()));

        preModifyVo.setCanModifyFields(initialSeaVo.getCanModifyFields());

        return preModifyVo;
    }

    public InitialPreModifyHandleVo.InitialMaterial convertFromPreModifyMaterial(CfMaterialModifyGroup modifyGroup,
                                                                                        ShuidiCipher shuidiCipher) {

        if (modifyGroup == null) {
            return null;
        }
        InitialPreModifyHandleVo.InitialMaterial initialMaterial = new InitialPreModifyHandleVo.InitialMaterial();

        initialMaterial.setCaseBaseInfo(convertFromContentInfo(modifyGroup.getTitleImageParam()));
        initialMaterial.setFirstApproveCaseInfo(convertFromApproveInfo(modifyGroup.getFirstApproveParam(), shuidiCipher));

        initialMaterial.setCreditInfo(convertFromInsuranceModel(modifyGroup.getPropertyInsuranceParam()));
        initialMaterial.setAdditionInfo(modifyGroup.getLivingGuardParam());

        return initialMaterial;
    }

    public CfMaterialModifyGroup convertFromInitialMaterialSeaVo(int caseId, InitialPreModifyHandleVo.InitialMaterial material) {
        if (material == null) {
            return null;
        }
        CfMaterialModifyGroup modifyGroup = new CfMaterialModifyGroup();

        modifyGroup.setTitleImageParam(convertFromBaseInfo(material.getCaseBaseInfo()));
        modifyGroup.setFirstApproveParam(convertFromApproveMaterial(caseId, material.getFirstApproveCaseInfo()));

        modifyGroup.setLivingGuardParam(material.getAdditionInfo());
        modifyGroup.setPropertyInsuranceParam(convertFromCreditInfo(material.getCreditInfo()));

        return modifyGroup;
    }

    /**
     * 图文的转换
     * @param caseBaseInfo
     * @return
     */
    public CfContentInfoModel convertFromBaseInfo(InitialAuditCaseDetail.CaseBaseInfo caseBaseInfo) {
        if (caseBaseInfo == null) {
            return null;
        }
        CfContentInfoModel infoModel = new CfContentInfoModel();
        infoModel.setTitle(caseBaseInfo.getTitle());
        infoModel.setContent(caseBaseInfo.getContent());
        if (CollectionUtils.isNotEmpty(caseBaseInfo.getAttachments())) {
            List<String> urls = Lists.newArrayList();
            caseBaseInfo.getAttachments().stream().filter(item-> StringUtils.isNotBlank(item.getUrl())).forEach(
                    item->{urls.add(item.getUrl());}
            );

            infoModel.setAttachments(urls);

        }
        infoModel.setTargetAmount(caseBaseInfo.getTargetAmount());

        return infoModel;
    }

    public InitialAuditCaseDetail.CaseBaseInfo convertFromContentInfo(CfContentInfoModel infoModel) {
        if (infoModel == null) {
            return null;
        }

        InitialAuditCaseDetail.CaseBaseInfo caseInfo = new InitialAuditCaseDetail.CaseBaseInfo();
        caseInfo.setTitle(infoModel.getTitle());
        caseInfo.setContent(infoModel.getContent());
        caseInfo.setTargetAmount(Objects.requireNonNullElse(infoModel.getTargetAmount(), 0));

        if (CollectionUtils.isNotEmpty(infoModel.getAttachments())) {
            List<CrowdfundingAttachmentVo> attachments = Lists.newArrayList();
            for (String uri : infoModel.getAttachments()) {
                CrowdfundingAttachmentVo attachmentVo = new CrowdfundingAttachmentVo();
                attachmentVo.setUrl(uri);
                attachments.add(attachmentVo);
            }
            caseInfo.setAttachments(attachments);
        }
        return caseInfo;
    }


    /**
     *  前置
     * @param firstInfo
     * @return
     */
    public CfFirsApproveMaterial convertFromApproveMaterial(int caseId, InitialAuditCaseDetail.FirstApproveCaseInfo firstInfo) {
        if (firstInfo == null) {
            return null;
        }

        CfFirsApproveMaterial material = new CfFirsApproveMaterial();
        material.setInfoId(caseId);
        material.setSelfRealName(firstInfo.getSelfRealName());
        material.setSelfCryptoIdcard(oldShuidiCipher.aesEncrypt(firstInfo.getSelfIdCard()));

        material.setPatientRealName(firstInfo.getPatientRealName());
        material.setPatientCryptoIdcard(oldShuidiCipher.aesEncrypt(firstInfo.getPatientIdCard()));
        material.setPatientBornCard(firstInfo.getPatientBornCard());

        material.setUserRelationTypeForC(firstInfo.getUserRelationTypeForC());
        material.setUserRelationType(firstInfo.getUserRelationType());

        material.setPatientIdType(firstInfo.getPatientIdType());
        material.setImageUrl(firstInfo.getImageUrl());
        material.setImageUrlType(firstInfo.getImageUrlType());

        return material;
    }

    public InitialAuditCaseDetail.FirstApproveCaseInfo convertFromApproveInfo(CfFirsApproveMaterial material,
                                                                                     ShuidiCipher shuidiCipher) {
        if (material == null) {
            return null;
        }

        InitialAuditCaseDetail.FirstApproveCaseInfo firstInfo = new InitialAuditCaseDetail.FirstApproveCaseInfo();

        firstInfo.setSelfRealName(material.getSelfRealName());
        firstInfo.setSelfIdCard(shuidiCipher.decrypt(material.getSelfCryptoIdcard()));
        firstInfo.setSelfIdCardMask(maskUtil.buildByDecryptStrAndType(firstInfo.getSelfIdCard(), DesensitizeEnum.IDCARD));

        firstInfo.setPatientRealName(material.getPatientRealName());
        firstInfo.setPatientIdCard(shuidiCipher.decrypt(material.getPatientCryptoIdcard()));
        firstInfo.setPatientIdCardMask(maskUtil.buildByDecryptStrAndType(firstInfo.getPatientIdCard(), DesensitizeEnum.IDCARD));
        firstInfo.setPatientBornCard(material.getPatientBornCard());
        firstInfo.setPatientBornCardMask(maskUtil.buildByDecryptStrAndType(firstInfo.getPatientBornCard(), DesensitizeEnum.IDCARD));

        firstInfo.setUserRelationTypeForC(material.getUserRelationTypeForC());
        firstInfo.setUserRelationType(material.getUserRelationType());
        if (material.getUserRelationTypeForC() != 0) {
            firstInfo.setUserRelationType(BaseInfoTemplateConst.CfBaseInfoRelationshipEnum
                    .convertRaiseRealType(material.getUserRelationTypeForC()).getValue());
        }

        firstInfo.setPatientIdType(material.getPatientIdType());
        firstInfo.setImageUrl(material.getImageUrl());
        firstInfo.setImageUrlType(material.getImageUrlType());

        return firstInfo;
    }

    public InitialAuditCaseDetail.CreditInfo convertFromInsuranceModel(CfPropertyInsuranceInfoModel infoModel) {

        if (infoModel == null) {
            return null;
        }

        return new InitialAuditCaseDetail.CreditInfo(CreditInfoVO.build(infoModel));
    }

    public CfPropertyInsuranceInfoModel convertFromCreditInfo(InitialAuditCaseDetail.CreditInfo creditInfo) {

        if (creditInfo == null || creditInfo.getInfo() == null) {
            return null;
        }
        CfPropertyInsuranceInfoModel infoModel = new CfPropertyInsuranceInfoModel();

        BeanUtils.copyProperties(creditInfo.getInfo(), infoModel);

        return infoModel;
    }


}
