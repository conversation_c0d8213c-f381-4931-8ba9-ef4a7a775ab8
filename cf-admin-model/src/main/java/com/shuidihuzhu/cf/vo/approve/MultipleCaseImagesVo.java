package com.shuidihuzhu.cf.vo.approve;

import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.cf.vo.crowdfunding.CfProgressVo;
import lombok.Data;

import java.util.Date;
import java.util.ArrayList;
import java.util.List;

/**
 * 材审详情页票据归纳
 */
@Data
public class MultipleCaseImagesVo {
    private int haveMultipleCaseRisk;
    private Date firstApproveTime;
    private long receivedMoneyInFen;
    private List<CaseImages> caseImages = new ArrayList<>();
    @Data
    public static class CaseImages {
        private int caseId;
        /**
         * 材审疾病诊断证明
         */
        private List<CrowdfundingAttachmentVo> hospitalAttachmentList = new ArrayList<>();
        /**
         * 前置图片
         */
        private String preAuditImageUrl = "";
        /**
         * 动态图片
         */
        private List<CfProgressVo> cfProgressVoList = new ArrayList<>();
        /**
         * 图文信息中展示的图片
         */
        private List<CrowdfundingAttachmentVo> imageAuditAttachmentList = new ArrayList<>();

    }
}
