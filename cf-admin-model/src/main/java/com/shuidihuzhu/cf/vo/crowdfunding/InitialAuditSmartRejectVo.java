package com.shuidihuzhu.cf.vo.crowdfunding;

import com.shuidihuzhu.cf.enums.InitialAudit.InitialAuditNoSmartReason;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: wangpeng
 * @Date: 2021/5/31 21:49
 * @Description:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InitialAuditSmartRejectVo {
    private String diseaseName;
    private InitialAuditNoSmartReason initialAuditNoSmartReason;
    private String remarkMsg;
}
