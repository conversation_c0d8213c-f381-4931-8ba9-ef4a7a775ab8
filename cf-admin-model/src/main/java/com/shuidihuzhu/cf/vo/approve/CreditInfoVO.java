package com.shuidihuzhu.cf.vo.approve;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.client.material.model.CfPropertyInsuranceInfoModel;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@Slf4j
public class CreditInfoVO extends CfPropertyInsuranceInfoModel{

    private String homeIncomeDesc;

    private String homeStockDesc;

    @Nullable
    @ApiModelProperty("车产")
    private CarPropertyInfoVO carProperty;

    @Nullable
    @ApiModelProperty("房产")
    private HousePropertyInfoVO houseProperty;

    @Nullable
    @ApiModelProperty("自建房产")
    private HousePropertyInfoVO selfBuiltHouse;

    @ApiModelProperty("保险赔付状态")
    private Integer insuranceCompensationStatus;

    @ApiModelProperty("人身险 已赔付金额")
    private Integer paidInsuranceAmount;

    /**
     * 报备信息-案例类型 // 原来有 accidentType
     * @see PreposeMaterialModel.AccidentType
     */
    private Integer accidentType;

    @ApiModelProperty("事故定责") // 原来有 accidentDuty
    private Integer accidentDuty;

    @ApiModelProperty("事故经过") // 原来没有
    private String accidentDetail;

    @ApiModelProperty("已赔付/垫付金额") // 原来有 paidAmount
    private Integer paidAmount;

    @ApiModelProperty("后续将要赔付/垫付金额") // 原来有 willPayAmount
    private Integer willPayAmount;

    private Integer willPayAmountArea;

    @ApiModelProperty("疾病花费最大值 分")
    private Integer maxTreatmentCost;


    // 从报备转换
    public static CreditInfoVO build(PreposeMaterialModel.MaterialInfoVo data) {
        CreditInfoVO v = CreditInfoVO.build(CfPropertyInsuranceInfoModel.convertFromPrepose(data));
        if (data != null && Objects.nonNull(v)) {
            v.setWillPayAmountArea(data.getWillPayAmountArea());
            // 是否有人身险 1 是 0 否
            Integer hasPersonalInsurance = data.getHasPersonalInsurance();

            // 保险赔付状态  是否有人身险选择<是>设置此字段，并且insurancePayStatus字段要是合法的，否则为null
            Integer insurancePayStatus = data.getInsurancePayStatus();
            if (hasPersonalInsurance != null && hasPersonalInsurance == 1 && insurancePayStatus != null && PreposeMaterialModel.InsurancePayStatusEnum.valueOfCode(insurancePayStatus) != null) {
                v.setInsuranceCompensationStatus(insurancePayStatus);
                // 已赔付金额  有人身险，并且保险赔付状态选择<可赔付且已赔付>时设置此字段，否则为null
                if (insurancePayStatus == PreposeMaterialModel.InsurancePayStatusEnum.PAY_YET.getCode()) {
                    v.setPaidInsuranceAmount(data.getPersonalPaidAmount());
                }
            }

            // 案例类型
            v.setAccidentType(data.getAccidentType());
            // 事故定责
            v.setAccidentDuty(data.getAccidentDuty());
            // 事故经过
            v.setAccidentDetail(data.getAccidentDetail());
            // 已赔付/垫付金额
            v.setPaidAmount(data.getPaidAmount());
            // 后续将要赔付/垫付金额
            v.setWillPayAmount(data.getWillPayAmount());
        }
        return v;
    }

    public static CreditInfoVO build(CfPropertyInsuranceInfoModel info){
        if (info == null) {
            return null;
        }
        CreditInfoVO v ;
        String jsonString = JSON.toJSONString(info);
        v = JSON.parseObject(jsonString, CreditInfoVO.class);//已检查过
        build(v.getCarProperty());
        build(v.getHouseProperty());
        build(v.getSelfBuiltHouse());

        String homeIncome = buildValue(v.getHomeIncomeUserDefined(), v.getHomeIncomeRangeType(), new HomeIncomeFunction());
        v.setHomeIncomeDesc(homeIncome);

        String homeStock = buildValue(v.getHomeStockUserDefined(), v.getHomeStockRangeType(), new HomeStockFunction());
        v.setHomeStockDesc(homeStock);

        return v;
    }

    private static String buildValue(Integer userDefined, Integer rangeType, ParseFunction parseFunction){
        if (userDefined != null && userDefined >= 0) {
            BigDecimal bg = new BigDecimal(userDefined);
            bg = bg.divide(BigDecimal.valueOf(10000));
            double f = bg.setScale(2, RoundingMode.HALF_DOWN).doubleValue();
            return f + "万元";
        }
        if (rangeType == null) {
            return "";
        }
        if (rangeType <= 0) {
            log.error("rangeType 不合法:{}", rangeType);
            return "";
        }
        CfPropertyInsuranceInfoModel.IValueRange range = parseFunction.parse(rangeType);
        return range.getDesc();
    }

    private static void build(CarPropertyInfoVO carProperty) {
        if (carProperty == null) {
            return;
        }
        ParseFunction carFunction = new CarFunction();

        String saleValueDesc = buildValue(carProperty.getSaleValueUserDefined(), carProperty.getSaleValueRangeType(), carFunction);
        carProperty.setSaleValueDesc(saleValueDesc);

        String total = buildValue(carProperty.getTotalValueUserDefined(), carProperty.getTotalValueRangeType(), carFunction);
        carProperty.setTotalValueDesc(total);
    }

    private static void build(HousePropertyInfoVO property) {
        if (property == null) {
            return;
        }
        HouseFunction houseFunction = new HouseFunction();

        String sale = buildValue(property.getSaleValueUserDefined(), property.getSaleValueRangeType(), houseFunction);
        property.setSaleValueDesc(sale);

        String total = buildValue(property.getTotalValueUserDefined(), property.getTotalValueRangeType(), houseFunction);
        property.setTotalValueDesc(total);

        String pure = buildValue(property.getPureValueUserDefined(), property.getPureValueRangeType(), houseFunction);
        property.setPureValueDesc(pure);
    }

    @Data
    public static class CarPropertyInfoVO extends CfPropertyInsuranceInfoModel.CarPropertyInfo{
        private String totalValueDesc;
        private String saleValueDesc;
    }

    @Data
    public static class HousePropertyInfoVO extends CfPropertyInsuranceInfoModel.HousePropertyInfo{
        private String totalValueDesc;
        private String saleValueDesc;
        private String pureValueDesc;
    }

    public interface ParseFunction {
        CfPropertyInsuranceInfoModel.IValueRange parse(int value);
    }

    public static class CarFunction implements ParseFunction{

        @Override
        public CfPropertyInsuranceInfoModel.CarValueRange parse(int value) {
            return CfPropertyInsuranceInfoModel.CarValueRange.valueOfCode(value);
        }
    }

    public static class HouseFunction implements ParseFunction{

        @Override
        public CfPropertyInsuranceInfoModel.HouseValueRange parse(int value) {
            return CfPropertyInsuranceInfoModel.HouseValueRange.valueOfCode(value);
        }
    }

    public static class HomeIncomeFunction implements ParseFunction{

        @Override
        public CfPropertyInsuranceInfoModel.HomeIncomeValueRange parse(int value) {
            return CfPropertyInsuranceInfoModel.HomeIncomeValueRange.valueOfCode(value);
        }
    }

    public static class HomeStockFunction implements ParseFunction{

        @Override
        public CfPropertyInsuranceInfoModel.HomeStockValueRange parse(int value) {
            return CfPropertyInsuranceInfoModel.HomeStockValueRange.valueOfCode(value);
        }
    }

    public static class HomeDebtFunction implements ParseFunction{

        @Override
        public HomeDebtValueRange parse(int value) {
            return CfPropertyInsuranceInfoModel.HomeDebtValueRange.valueOfCode(value);
        }
    }
}
