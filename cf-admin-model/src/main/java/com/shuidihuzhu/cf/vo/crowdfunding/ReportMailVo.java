package com.shuidihuzhu.cf.vo.crowdfunding;

import com.google.common.base.Objects;
import com.shuidihuzhu.cf.enums.crowdfunding.CfReportConditionEnum;
import org.apache.commons.lang.builder.ToStringBuilder;

/**
 * Created by ahrievil on 2017/1/20.
 */
public class ReportMailVo {
    private Integer activityId;
    private String infoId;
    private int type;
    private String title;
    private String url;
    private String content;
    private int amount;
    private String imageUrl;
    private String reportContent;
    private String contact;
    private CfReportConditionEnum cfReportConditionEnum;
    private String showChannel;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ReportMailVo that = (ReportMailVo) o;
        return Objects.equal(infoId, that.infoId) &&
                Objects.equal(imageUrl, that.imageUrl) &&
                Objects.equal(reportContent, that.reportContent) &&
                Objects.equal(contact, that.contact);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(infoId, imageUrl, reportContent, contact);
    }

    public Integer getActivityId() {
        return activityId;
    }

    public void setActivityId(Integer activityId) {
        this.activityId = activityId;
    }

    public String getInfoId() {
        return infoId;
    }

    public void setInfoId(String infoId) {
        this.infoId = infoId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getReportContent() {
        return reportContent;
    }

    public void setReportContent(String reportContent) {
        this.reportContent = reportContent;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public int getAmount() {
        return amount;
    }

    public void setAmount(int amount) {
        this.amount = amount;
    }

    public CfReportConditionEnum getCfReportConditionEnum() {
        return cfReportConditionEnum;
    }

    public void setCfReportConditionEnum(CfReportConditionEnum cfReportConditionEnum) {
        this.cfReportConditionEnum = cfReportConditionEnum;
    }

    public String getShowChannel() {
        return showChannel;
    }

    public void setShowChannel(String showChannel) {
        this.showChannel = showChannel;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
