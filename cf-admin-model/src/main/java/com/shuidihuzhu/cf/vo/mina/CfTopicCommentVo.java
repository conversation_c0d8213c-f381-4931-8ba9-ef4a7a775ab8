package com.shuidihuzhu.cf.vo.mina;

import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR> <PERSON><PERSON>vil
 */
@Data
public class CfTopicCommentVo {
    private int topicId;
    private String topicTitle;
    private long commentId;
    private Timestamp commentTime;
    private String comment;
    private String sensitiveWord;
    private long userId;
    private String nickname;
    private int praiseCount;
    private int topStatus;
    private boolean reply;

    public CfTopicCommentVo() {

    }

    public CfTopicCommentVo(int topicId, String topicTitle, long commentId, Timestamp commentTime, String comment,
                            String sensitiveWord, long userId, String nickname, int praiseCount, int topStatus,
                            boolean reply) {
        this.topicId  = topicId;
        this.topicTitle = topicTitle;
        this.commentId = commentId;
        this.commentTime = commentTime;
        this.comment = comment;
        this.sensitiveWord = sensitiveWord;
        this.userId = userId;
        this.nickname = nickname;
        this.praiseCount = praiseCount;
        this.topStatus = topStatus;
        this.reply = reply;
    }
}
