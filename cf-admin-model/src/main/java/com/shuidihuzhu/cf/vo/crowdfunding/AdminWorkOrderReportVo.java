package com.shuidihuzhu.cf.vo.crowdfunding;

import java.util.Date;
import java.util.List;
import java.util.Set;

import lombok.Data;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/15.
 */
@Data
public class AdminWorkOrderReportVo extends CaseStatusBase {
    private int id;
    private int caseId;
    private int operatorId;
    private String followPeople;
    private int amount;
    private int reportNumber;
    private int isNewReport;
    private int caseRisk;
    private int isRepeat;  //是否重复案例，默认0，重复1
    private int dealResult;
    private int pauseState;
    private int addTrustAuditStatus;
    private String reportRemark;
    private Date firstReportTime;
    private Date lastReportTime;
    private Date lastOperationTime;
    private int status;
    private String infoUuid;
    private int reportId;
    private long userId;

    private long workOrderId;
    // 案例的重复情况
    private Set<Integer> repeatStatusList;
    //举报标签
    private List<String> reportTypes;
    private boolean realNameReport;
    private String lastComment;
    private Date appointTime;
}
