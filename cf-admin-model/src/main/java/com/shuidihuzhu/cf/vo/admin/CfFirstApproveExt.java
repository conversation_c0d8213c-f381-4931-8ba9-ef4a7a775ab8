package com.shuidihuzhu.cf.vo.admin;

import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import com.shuidihuzhu.cf.client.material.model.CfBasicLivingGuardModel;
import com.shuidihuzhu.cf.vo.CfFirsApproveMaterialVO;
import lombok.Data;
import org.springframework.beans.BeanUtils;

@Data
public class CfFirstApproveExt extends CfFirsApproveMaterialVO {

    private int auditImageUrlWatermark;

    public CfFirstApproveExt() {
    }

    public CfFirstApproveExt(CfFirsApproveMaterialVO model) {
        if (model != null) {
            BeanUtils.copyProperties(model, this);
        }
    }
}
