package com.shuidihuzhu.cf.vo.crowdfunding.firstapprove;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@ApiModel
public class FirstApproveConfiguration {
    @ApiModelProperty("待处理任务数")
    private int unhandleTaskCount;

    @ApiModelProperty("正在处理任务数")
    private int handlingTaskCount;

    @ApiModelProperty("已完成任务数")
    private int finishedTaskCount;

    @ApiModelProperty("前置审核人员列表")
    List<FirstApproveAccountVo> firstApproveAccounts;

    @ApiModelProperty("前置审核人员列表")
    Map<String, Object> pagination;
}
