package com.shuidihuzhu.cf.vo.wx.greeting;

import com.shuidihuzhu.wx.grpc.client.common.WxConfig;
import com.shuidihuzhu.wx.grpc.model.WxConfigModel;
import lombok.Data;

/**
 * @Author: duchao
 * @Date: 2018/6/11 下午10:45
 */
@Data
public class WxConfigVO {
	private String name;
	private int thirdType;

	public static WxConfigVO fromWxConfig(WxConfig wxConfig) {
		WxConfigVO vo = new WxConfigVO();
		vo.name = wxConfig.getDescription();
		vo.thirdType = wxConfig.getThirdType();
		return vo;
	}

	public static WxConfigVO fromWxConfigV2(WxConfigModel wxConfigModel) {
		WxConfigVO vo = new WxConfigVO();
		vo.name = wxConfigModel.getName();
		vo.thirdType = wxConfigModel.getThirdType();
		return vo;
	}
}
