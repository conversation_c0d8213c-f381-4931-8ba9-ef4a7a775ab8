package com.shuidihuzhu.cf.vo.approve;

import com.shuidihuzhu.cf.client.ugc.caseprocessstatus.model.CaseProcessStatusEnum;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.ActorIdentifyEnum;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jetbrains.annotations.NotNull;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class InteractionDataVO implements Comparable<InteractionDataVO>{

//    -----------------------     呼通相关

    @ApiModelProperty("是否呼通")
    private Boolean callConnected;

    @ApiModelProperty("呼通开始时间")
    private Date callStartTime;

    @ApiModelProperty("呼通挂断时间")
    private Date callEndTime;

    @ApiModelProperty("呼通时长-秒")
    private int callDuration;

    @ApiModelProperty("呼叫电话号码")
    private String mobile;

    @ApiModelProperty("通话录音链接")
    private String callAudioRecordUrl;

    @ApiModelProperty("在线咨询唯一标识")
    private String cId;

//    --------------------- 通用属性

    @ApiModelProperty("互动时间 提交-> 提交时间 驳回-> 驳回时间 ...")
    private Date actionTime;

    @ApiModelProperty("操作人姓名+组织")
    private String operatorNameWithOrg;

    @ApiModelProperty("所处环节")
    private CaseProcessStatusEnum caseProcessStatus;

    @ApiModelProperty("展示用所处环节")
    private String displayCaseProcessStatus;

    @ApiModelProperty("动作类型")
    private OperationActionTypeEnum actionType;

    @ApiModelProperty("动作发出者身份")
    private ActorIdentifyEnum actorIdentify;

    @Override
    public int compareTo(@NotNull InteractionDataVO o) {
        return this.getActionTime().compareTo(o.getActionTime());
    }
}
