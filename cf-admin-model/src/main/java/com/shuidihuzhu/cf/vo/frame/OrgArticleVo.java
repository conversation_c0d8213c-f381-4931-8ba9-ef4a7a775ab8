package com.shuidihuzhu.cf.vo.frame;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * Created by dongcf on 2018/5/3
 */
@Data
public class OrgArticleVo {

    private long id;
    private String uuid;
    private int platform;
    private String account;
    private int isOrigin;
    private String title;
    private String author;
    private String summary;
    private Date time;
    private String pageUrl;
    private String contentUrl;
    private String coverUrl;
    private boolean hasCover;
    private Date createTime;
    private Date updateTime;
    private int valid;

    private List<OrgTagVo> tags;
    private String body;
    private String baseImgUrl;
    private Integer actionType;
    private String actionUrl;
    private String appId;
}
