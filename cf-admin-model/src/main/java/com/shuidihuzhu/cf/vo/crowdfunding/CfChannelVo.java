package com.shuidihuzhu.cf.vo.crowdfunding;

import java.sql.Timestamp;

import com.shuidihuzhu.cf.model.admin.channel.CfChannel;


/**
 * Created by wangsf on 17/3/29.
 */
public class CfChannelVo {

	private int id;
	private String name;
	private String description;

	private int groupId;
	private String groupName;
	private Timestamp createTime;
	private Timestamp updateTime;

	public CfChannelVo(CfChannel channel) {
		if(channel != null) {
			this.name = channel.getName();
			this.description = channel.getDescription();
			this.id = channel.getId();
			this.createTime = channel.getCreateTime();
			this.updateTime = channel.getUpdateTime();
		}
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public int getGroupId() {
		return groupId;
	}

	public void setGroupId(int groupId) {
		this.groupId = groupId;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public Timestamp getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Timestamp createTime) {
		this.createTime = createTime;
	}

	public Timestamp getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Timestamp updateTime) {
		this.updateTime = updateTime;
	}
}
