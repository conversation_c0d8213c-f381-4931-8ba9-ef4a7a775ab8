package com.shuidihuzhu.cf.vo.amount;

import com.shuidihuzhu.cf.enhancer.mq.DelayBasePayLoad;
import lombok.Data;

/**
 * @Author: wangpeng
 * @Date: 2022/8/26 15:25
 * @Description:
 */
@Data
public class AmountReasonableTaskMqVo extends DelayBasePayLoad {
    private int caseId;
    private String taskInfoId;
    private int taskType;
    /**
     * 0:任务提交逻辑延时
     * 1:任务首次下发延时，未避免用户长时间为提交
     */
    private int delayReason;
    /**
     * 是否继续延迟消息，针对180天的场景，mq目前支持最大90天
     */
    private boolean delayContinue;
    private long taskWorkOrderId;
}
