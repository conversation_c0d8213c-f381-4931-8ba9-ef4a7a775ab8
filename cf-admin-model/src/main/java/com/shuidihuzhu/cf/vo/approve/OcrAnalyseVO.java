package com.shuidihuzhu.cf.vo.approve;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.risk.model.risk.diease.InfoReasonableAmountResultVo;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class OcrAnalyseVO extends InfoReasonableAmountResultVo {

    private String submitDiseaseNames;

    public static OcrAnalyseVO create(InfoReasonableAmountResultVo vo){
        return JSON.parseObject(JSON.toJSONString(vo), OcrAnalyseVO.class);//已检查过
    }
}
