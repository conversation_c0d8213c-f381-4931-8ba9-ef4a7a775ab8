package com.shuidihuzhu.cf.vo.approve;

import com.shuidihuzhu.cf.enums.approve.ApproveLifeCircleNodeStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApproveLifeCircleVO {

    @ApiModelProperty("审核项")
    private String name;

    @ApiModelProperty("审核状态")
    private String status;

    @ApiModelProperty("操作时间")
    private String time;

    @ApiModelProperty(value = "节点状态")
    private ApproveLifeCircleNodeStatusEnum nodeStatus;

    @ApiModelProperty(value = "驳回原因")
    private List<String> rejectMsgs;
}
