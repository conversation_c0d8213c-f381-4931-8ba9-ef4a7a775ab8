package com.shuidihuzhu.cf.vo.crowdfunding;

import java.sql.Timestamp;

import com.shuidihuzhu.common.web.model.AbstractModel;
import com.shuidihuzhu.cf.model.crowdfunding.CfDrawCashApplyRecord;

public class CfCapitalDetailVo extends AbstractModel {

	private static final long serialVersionUID = -4947038666069424980L;

	public static final int draw = 1;
	public static final int refund = 2;

	private int capitalType;
	private Integer applyStatus;
	private Integer status;
	private String refundReason;
	private CfDrawCashApplyRecord cfDrawCashApplyRecord;
	private String errorMsg;
	private Timestamp businessTime;

	public CfCapitalDetailVo() {
	}

	public void setApplyByRefund(int applyStatus, String refundReason, Timestamp businessTime) {
		this.capitalType = refund;
		this.applyStatus = applyStatus;
		this.refundReason = refundReason;
		this.businessTime = businessTime;
	}

	public void setApplyByDrawCash(int applyStatus, CfDrawCashApplyRecord cfDrawCashApplyRecord,
			Timestamp businessTime) {
		this.capitalType = draw;
		this.applyStatus = applyStatus;
		this.cfDrawCashApplyRecord = cfDrawCashApplyRecord;
		this.businessTime = businessTime;
	}

	public void setStatus(int capitalType, int status, String errorMsg, Timestamp businessTime) {
		this.capitalType = capitalType;
		this.status = status;
		this.errorMsg = errorMsg;
		this.businessTime = businessTime;
	}

	public int getCapitalType() {
		return capitalType;
	}

	public void setCapitalType(int capitalType) {
		this.capitalType = capitalType;
	}

	public Integer getApplyStatus() {
		return applyStatus;
	}

	public void setApplyStatus(Integer applyStatus) {
		this.applyStatus = applyStatus;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getRefundReason() {
		return refundReason;
	}

	public void setRefundReason(String refundReason) {
		this.refundReason = refundReason;
	}

	public CfDrawCashApplyRecord getCfDrawCashApplyRecord() {
		return cfDrawCashApplyRecord;
	}

	public void setCfDrawCashApplyRecord(CfDrawCashApplyRecord cfDrawCashApplyRecord) {
		this.cfDrawCashApplyRecord = cfDrawCashApplyRecord;
	}

	public String getErrorMsg() {
		return errorMsg;
	}

	public void setErrorMsg(String errorMsg) {
		this.errorMsg = errorMsg;
	}

	public Timestamp getBusinessTime() {
		return businessTime;
	}

	public void setBusinessTime(Timestamp businessTime) {
		this.businessTime = businessTime;
	}

}
