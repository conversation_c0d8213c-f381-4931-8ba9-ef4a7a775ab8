package com.shuidihuzhu.cf.vo.approve;

import com.shuidihuzhu.cf.model.river.RiverReviewDO;
import com.shuidihuzhu.cf.vo.admin.initialAudit.AdminCfInitialAuditCheckInfoVO;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditCaseDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
public class CreditPageInfoVO {

    @ApiModelProperty("材料审核信息")
    private RiverReviewDO reviewDO;

    @ApiModelProperty("驳回原因id")
    private Set<Integer> rejectSets;

    @ApiModelProperty("是否通过")
    private int pass;

    @ApiModelProperty("增信信息")
    private CreditInfoVO creditInfo;

    @ApiModelProperty("前置报备")
    private CreditInfoVO propose;

    @ApiModelProperty("图文信息")
    private InitialAuditCaseDetail.CaseBaseInfo baseInfo;

    @ApiModelProperty("前置信息")
    private InitialAuditCaseDetail.FirstApproveCaseInfo firstApproveInfo;

    @ApiModelProperty("校验信息")
    private List<AdminCfInitialAuditCheckInfoVO> checkInfo;

}
