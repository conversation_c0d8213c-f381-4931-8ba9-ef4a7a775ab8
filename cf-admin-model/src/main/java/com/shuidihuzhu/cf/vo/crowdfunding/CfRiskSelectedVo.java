package com.shuidihuzhu.cf.vo.crowdfunding;

import lombok.Data;

import java.util.List;

/**
 * @Auther: subing
 * @Date: 2020/3/11
 */
@Data
public class CfRiskSelectedVo {
    private String content;
    private List<String> propertyList;
    private String status;
    private int id;
    private int parentId;

    public static CfRiskSelectedVo buildVo(CfReportLabelVo cfReportLabelVo){
        CfRiskSelectedVo cfRiskSelectedVo = new CfRiskSelectedVo();
        cfRiskSelectedVo.setContent(cfReportLabelVo.getContent());
        cfRiskSelectedVo.setPropertyList(cfReportLabelVo.getPropertyList());
        cfRiskSelectedVo.setId(cfReportLabelVo.getId());
        cfRiskSelectedVo.setParentId(cfReportLabelVo.getParentId());
        return cfRiskSelectedVo;
    }
}
