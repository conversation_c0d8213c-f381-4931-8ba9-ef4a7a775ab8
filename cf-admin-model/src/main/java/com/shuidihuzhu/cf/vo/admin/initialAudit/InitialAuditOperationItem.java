package com.shuidihuzhu.cf.vo.admin.initialAudit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.enums.admin.UserCommentSourceEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonEntity;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonTag;
import com.shuidihuzhu.cf.model.river.RiverUsageTypeEnum;
import com.shuidihuzhu.cf.vo.approve.RiverHandleParamVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
public class InitialAuditOperationItem {

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class HandleCaseInfoParam {
        private long workOrderId;
        private int caseId;
        private int handleType;
        private String handleComment;

        private int orderType;

        private int callStatus;

        private String callUnicode;

        private int userCallStatus;

        private String callComment;

        private List<Integer> passIds;
        private List<Integer> rejectIds;

        private int userId;

        @JsonIgnore
        private Set<Integer> allHandleTypes;

        @ApiModelProperty("贫困材料处理参数")
        private RiverHandleParamVO pinKunHandleParam;
        private String pinKunComment;

        @ApiModelProperty("低保材料处理参数")
        private RiverHandleParamVO diBaoHandleParam;
        private String diBaoComment;

        @ApiModelProperty("是否为系统自动提交初审审核 1:是；0:不是, 默认不是")
        private int systemAutoAudit;

        @ApiModelProperty("没有打过电话强制提交")
        private boolean aiErForceOp;

        @ApiModelProperty("是否特殊报备发起")
        private boolean specialReport;

        @ApiModelProperty("自定义驳回项内容")
        private Map<Integer, String> customRefuseReason;

        public RiverHandleParamVO getPinKunHandleParamWithLoaded() {
            return loadParamFromParent(this, pinKunHandleParam);
        }

        public RiverHandleParamVO getDiBaoHandleParamWithLoaded() {
            return loadParamFromParent(this, diBaoHandleParam);
        }

        public static void main(String[] args) {
             Map<Integer, String> customRefuseReason = new HashMap<>();
             customRefuseReason.put(1, "12");
            System.out.println(JSONObject.toJSONString(customRefuseReason));

        }
        /**
         * 继承参数
         */
        private RiverHandleParamVO loadParamFromParent(HandleCaseInfoParam parent, RiverHandleParamVO param){
            if (param == null) {
                return null;
            }
            param.setWorkOrderId(parent.getWorkOrderId());
            param.setCaseId(parent.getCaseId());
            param.setHandleComment(parent.getHandleComment());
            param.setOrderType(parent.getOrderType());
            param.setCallStatus(parent.getCallStatus());
            param.setOperatorId(parent.getUserId());
            RiverHandleParamVO.HandleType handleType = param.getHandleType();
            if (handleType != RiverHandleParamVO.HandleType.PASS && handleType != RiverHandleParamVO.HandleType.REJECT) {
                param.setHandleType(null);
            }
            return param;
        }
    }

    @ApiModel("处理初审新增材料参数")
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class HandleAdditionParam{

        @ApiModelProperty("材料类型")
        private RiverUsageTypeEnum usageTypeEnum;

        @ApiModelProperty("处理类型")
        private RiverHandleParamVO.HandleType handleType;

        @ApiModelProperty("驳回原因id")
        private List<Integer> rejectIds;

    }

    @Getter
    public enum HandleTypeEnum {
        SUBMIT(1, "提交"),
        END_CASE(2, "停止筹款"),
        RETURN_VISIT(3, "回访处理"),
        DELAY_HANDLE(4, "稍后处理"),
        ;

        HandleTypeEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private int code;
        private String desc;

        public static HandleTypeEnum parseCode(int code) {

            for (HandleTypeEnum type : HandleTypeEnum.values()) {
                if (type.getCode() == code) {
                    return type;
                }
            }

            return null;
        }
    }

    @Getter
    public enum RejectOperation {
        ENABLE(0, "启用"),
        DELETE(1, "删除"),
        DISABLE(2, "弃用"),
        ;

       RejectOperation(int code, String desc) {
           this.code = code;
           this.desc = desc;
       }

       private int code;
       private String desc;

       static public RejectOperation codeOf(int code) {
           RejectOperation[] rejects = values();
           for (RejectOperation reject : rejects) {
               if (reject.getCode() == code) {
                   return reject;
               }
           }

           return null;
       }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class EditBaseInfo {
        private int caseId;
        private String title;
        private String content;
        private String imgUrls;
        private String reason;
        private int userId;
        private long workOrderId;
        private String delImgUrls;
        private String headPictureUrl;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RejectOptionSet {
        private List<CfRefuseReasonTag> baseInfoRejectOption;
        private List<CfRefuseReasonTag> firstApproveRejectOption;
        private List<CfRefuseReasonTag> endCaseRejectOption;
        private List<CfRefuseReasonTag> creditInfoRejectOption;
        private List<CfRefuseReasonTag> diBaoRejectOption;
        private List<CfRefuseReasonTag> pinKunRejectOption;
        private List<Integer> passIds;
        private List<Integer> rejectIds;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CaseInitialAuditResult {
        private int handleResult;
        UserCommentSourceEnum.CommentType operateType;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SortedReasonEntity {
        private int dataType;
        private List<CfRefuseReasonEntity> sortedReasonEntities;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class InitialAuditSmsMsg {
        String mobile;
        String content;
        int caseId;
        String infoId;
        int userId;
        long workOrderId;
        String modelNum;
        private String param;

        public Map<Integer, String> getParamMap() {
            if (StringUtils.isEmpty(param)) {
                return Maps.newHashMap();
            }
            return JSON.parseObject(param, new TypeReference<Map<Integer, String>>() {});
        }
    }


    @Data
    public static class CanSubmitResult {
        boolean canSubmit;
    }

}
