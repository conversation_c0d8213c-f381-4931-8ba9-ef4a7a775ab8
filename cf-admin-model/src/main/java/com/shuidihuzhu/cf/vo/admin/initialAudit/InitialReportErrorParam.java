package com.shuidihuzhu.cf.vo.admin.initialAudit;

import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.web.bind.annotation.RequestParam;

@Data
public class InitialReportErrorParam {

    private long workOrderId;
    private int caseId;
    private String imageUrl;
    private String patientName;
    private long userId;

    private int errorSource;
    private int errorType;
    private String errorContent;

    private String tips;


    @Getter
    public enum SourceType {
        MEDICAL_DISEASE(1, "医疗材料中的疾病名称"),
        MEDICAL_PATIENT_NAME(2, "医疗材料中的患者姓名"),
        CONTENT_DISEASE(3, "图文信息中的疾病名称"),
        CONTENT_PATIENT_NAME(4, "图文信息中的患者姓名"),
        ;
        private int code;
        private String msgs;

        SourceType(int code, String msgs) {
            this.code = code;
            this.msgs = msgs;
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OperatorDetails {

        private String patientName;
        private String tips;
        private String errorContent;
        private String imgUrl;
        private String diseaseName;

        private String hitDiseaseName;

        private int errorSource;
    }

}
