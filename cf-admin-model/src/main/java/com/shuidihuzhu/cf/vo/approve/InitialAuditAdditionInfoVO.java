package com.shuidihuzhu.cf.vo.approve;

import com.shuidihuzhu.cf.client.material.model.CfBasicLivingGuardModel;
import com.shuidihuzhu.cf.model.river.RiverReviewDO;
import com.shuidihuzhu.cf.vo.admin.initialAudit.AdminCfInitialAuditCheckInfoVO;
import com.shuidihuzhu.cf.vo.admin.initialAudit.InitialAuditCaseDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
public class InitialAuditAdditionInfoVO {

    @ApiModelProperty("贫困与低保信息")
    private InitialAuditCaseDetail.CfBasicLivingGuardView diBaoAndPinKunInfo;


}
