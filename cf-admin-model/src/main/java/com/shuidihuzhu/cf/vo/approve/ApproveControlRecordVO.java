package com.shuidihuzhu.cf.vo.approve;

import com.shuidihuzhu.cf.domain.approve.ApproveControlRecordDO;
import com.shuidihuzhu.cf.enums.approve.ApproveControlFlowTypeEnum;
import com.shuidihuzhu.cf.enums.approve.ApproveControlHandleStatusEnum;
import com.shuidihuzhu.cf.enums.approve.ApproveControlSourceTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class ApproveControlRecordVO {

    private long id;

    private int caseId;

    /**
     * @see com.shuidihuzhu.cf.enums.approve.ApproveControlHandleStatusEnum
     */
    @ApiModelProperty("处理状态")
    private ApproveControlHandleStatusEnum handleStatus;

    /**
     * @see ApproveControlFlowTypeEnum
     */
    @ApiModelProperty("流程类型 {0:默认, 1:正常任务工单 2:自动领取工单}")
    private ApproveControlFlowTypeEnum flowType;

    /**
     * @see com.shuidihuzhu.cf.enums.approve.ApproveControlSourceTypeEnum
     */
    @ApiModelProperty("来源类型")
    private ApproveControlSourceTypeEnum sourceType;

    private long workOrderId;

    private int operatorId;

    @ApiModelProperty("处理时间")
    private Date handleTime;

    @ApiModelProperty("开始时间")
    private Date createTime;

    private Date updateTime;

//   ----------------------------------------

    private String operatorName;

    private String operatorOrg;

    @ApiModelProperty("案例标题")
    private String caseTitle;

    private String infoUuid;

    public static ApproveControlRecordVO create(ApproveControlRecordDO domain){
        ApproveControlRecordVO v = new ApproveControlRecordVO();
        v.setId(domain.getId());
        v.setCaseId(domain.getCaseId());
        v.setHandleStatus(ApproveControlHandleStatusEnum.parse(domain.getHandleStatus()));
        v.setSourceType(ApproveControlSourceTypeEnum.parse(domain.getSourceType()));
        v.setWorkOrderId(domain.getWorkOrderId());
        v.setOperatorId(domain.getOperatorId());
        v.setHandleTime(domain.getHandleTime());
        v.setCreateTime(domain.getCreateTime());
        v.setUpdateTime(domain.getUpdateTime());

        v.setFlowType(ApproveControlFlowTypeEnum.parse(domain.getFlowType()));
        return v;
    }

}
