package com.shuidihuzhu.cf.vo.crowdfunding;

import com.shuidihuzhu.client.baseservice.pay.model.WxBillingDetailV2;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;


/**
 * Created by ahrievil on 2017/3/21.
 */
public class WxBillingDetailVo {
    private Timestamp businessTime;
    private String productName;
    private double amount;
    private double processingFee;
    private double settlementAmount;

    public WxBillingDetailVo(WxBillingDetailV2 wxBillingDetailV2) {
        if (wxBillingDetailV2 == null) {
            return;
        }
        this.businessTime = wxBillingDetailV2.getBusinessTime();
        this.productName = StringUtils.trimToEmpty(wxBillingDetailV2.getProductName());
        this.amount = divide(wxBillingDetailV2.getTotalAmount(), 100);
        this.processingFee = divide(wxBillingDetailV2.getFee(), 100);
        this.settlementAmount = divide(wxBillingDetailV2.getTotalAmount() - wxBillingDetailV2.getFee(), 100);
    }

    public double divide(Integer number, int dividend) {
        if (number == null) {
            return 0;
        }
        BigDecimal bd = new BigDecimal(number);
        BigDecimal bd2 = new BigDecimal(dividend);
        return bd.divide(bd2, 2, RoundingMode.HALF_UP).doubleValue();
    }

    public Timestamp getBusinessTime() {
        return businessTime;
    }

    public void setBusinessTime(Timestamp businessTime) {
        this.businessTime = businessTime;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public double getProcessingFee() {
        return processingFee;
    }

    public void setProcessingFee(double processingFee) {
        this.processingFee = processingFee;
    }

    public double getSettlementAmount() {
        return settlementAmount;
    }

    public void setSettlementAmount(double settlementAmount) {
        this.settlementAmount = settlementAmount;
    }
}
