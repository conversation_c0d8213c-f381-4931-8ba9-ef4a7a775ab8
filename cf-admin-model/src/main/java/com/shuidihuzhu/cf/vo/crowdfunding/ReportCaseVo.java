package com.shuidihuzhu.cf.vo.crowdfunding;

import java.util.Date;

import io.swagger.annotations.ApiModelProperty;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/9/10.
 */
public class ReportCaseVo extends CaseStatusBase {
    private int id;
    private String infoUuid;
    private String title;
    private String mostNewProgress;
    private int isHaveNewReport;
    private int type;
    private int followStatus;
    private int reportNumber;
    private int addTrustAuditStatus;
    private int hospitalAuditStatus;
    private Date submitHospitalAuditTime;
    private Date hospitalAuditSendTime;
    private Date firstReportTime;
    private Date lastReportTime;
    private Date lastOperationTime;

    @ApiModelProperty("下发医院核实人组织")
    private String operatorOrg;

    public int getAddTrustAuditStatus() {
        return addTrustAuditStatus;
    }

    public void setAddTrustAuditStatus(int addTrustAuditStatus) {
        this.addTrustAuditStatus = addTrustAuditStatus;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getMostNewProgress() {
        return mostNewProgress;
    }

    public void setMostNewProgress(String mostNewProgress) {
        this.mostNewProgress = mostNewProgress;
    }

    public int getIsHaveNewReport() {
        return isHaveNewReport;
    }

    public void setIsHaveNewReport(int isHaveNewReport) {
        this.isHaveNewReport = isHaveNewReport;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getFollowStatus() {
        return followStatus;
    }

    public void setFollowStatus(int followStatus) {
        this.followStatus = followStatus;
    }

    public int getReportNumber() {
        return reportNumber;
    }

    public void setReportNumber(int reportNumber) {
        this.reportNumber = reportNumber;
    }

    public Date getFirstReportTime() {
        return firstReportTime;
    }

    public void setFirstReportTime(Date firstReportTime) {
        this.firstReportTime = firstReportTime;
    }

    public Date getLastOperationTime() {
        return lastOperationTime;
    }

    public void setLastOperationTime(Date lastOperationTime) {
        this.lastOperationTime = lastOperationTime;
    }

    public Date getLastReportTime() {
        return lastReportTime;
    }

    public void setLastReportTime(Date lastReportTime) {
        this.lastReportTime = lastReportTime;
    }

    public String getInfoUuid() {
        return infoUuid;
    }

    public void setInfoUuid(String infoUuid) {
        this.infoUuid = infoUuid;
    }

    public int getHospitalAuditStatus() {
        return hospitalAuditStatus;
    }

    public void setHospitalAuditStatus(int hospitalAuditStatus) {
        this.hospitalAuditStatus = hospitalAuditStatus;
    }

    public Date getHospitalAuditSendTime() {
        return hospitalAuditSendTime;
    }

    public void setHospitalAuditSendTime(Date hospitalAuditSendTime) {
        this.hospitalAuditSendTime = hospitalAuditSendTime;
    }

    public Date getSubmitHospitalAuditTime() {
        return submitHospitalAuditTime;
    }

    public void setSubmitHospitalAuditTime(Date submitHospitalAuditTime) {
        this.submitHospitalAuditTime = submitHospitalAuditTime;
    }

    public String getOperatorOrg() {
        return operatorOrg;
    }

    public void setOperatorOrg(String operatorOrg) {
        this.operatorOrg = operatorOrg;
    }
}
