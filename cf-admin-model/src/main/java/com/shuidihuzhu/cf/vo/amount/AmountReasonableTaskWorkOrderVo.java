package com.shuidihuzhu.cf.vo.amount;

import com.shuidihuzhu.cf.client.adminpure.model.amount.CfAmountReasonableTaskWorkOrder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Author: wangpeng
 * @Date: 2022/8/26 17:38
 * @Description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AmountReasonableTaskWorkOrderVo extends CfAmountReasonableTaskWorkOrder {

    private long handleResult;
    private String operator;
    private int operatorId;
    private Date workOrderUpdateTime;


}
