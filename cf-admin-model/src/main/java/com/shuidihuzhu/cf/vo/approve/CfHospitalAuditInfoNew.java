package com.shuidihuzhu.cf.vo.approve;

import com.shuidihuzhu.cf.model.admin.CfHospitalAuditInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CfHospitalAuditInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CfHospitalAuditInfoTel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CfHospitalAuditInfoNew extends CfHospitalAuditInfo {

    @ApiModelProperty("是否为新版")
    private boolean isNew;

    @ApiModelProperty("科室座机号码")
    private List<CfHospitalAuditInfoTel> cfHospitalAuditInfoTels;

}