package com.shuidihuzhu.cf.vo.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfReportProblemLabel;
import lombok.Data;

/**
 * @Auther: subing
 * @Date: 2020/4/21
 */
@Data
public class AdminCfReportProblemClassifyVo {
    private String classifyName;
    private int isUse;
    private int sort;
    private int id;
    private int isMandatory;

    public static AdminCfReportProblemClassifyVo buildVo(CfReportProblemLabel cfReportProblemLabel){
        if (cfReportProblemLabel == null){
            return new AdminCfReportProblemClassifyVo();
        }
        AdminCfReportProblemClassifyVo adminCfReportProblemClassifyVo = new AdminCfReportProblemClassifyVo();
        adminCfReportProblemClassifyVo.setId(cfReportProblemLabel.getId());
        adminCfReportProblemClassifyVo.setSort(cfReportProblemLabel.getSort());
        adminCfReportProblemClassifyVo.setIsUse(cfReportProblemLabel.getIsUse());
        adminCfReportProblemClassifyVo.setClassifyName(cfReportProblemLabel.getLabelDesc());
        adminCfReportProblemClassifyVo.setIsMandatory(cfReportProblemLabel.getIsMandatory());
        return adminCfReportProblemClassifyVo;
    }
}
