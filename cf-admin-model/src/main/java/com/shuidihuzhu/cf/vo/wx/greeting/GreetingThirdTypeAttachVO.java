package com.shuidihuzhu.cf.vo.wx.greeting;

import com.shuidihuzhu.wx.grpc.client.common.GreetingThirdTypeAttach;
import lombok.Data;

/**
 * @Author: duchao
 * @Date: 2018/6/12 下午9:11
 */
@Data
public class GreetingThirdTypeAttachVO {
	private int thirdType;
	private int greetingId;
	private String content;
	private String key;
	private int matchRule;
	private long attachDate;
	private String title;
	private int subBizType;
	private int contentId;

	public static GreetingThirdTypeAttachVO convert(GreetingThirdTypeAttach attach) {
		GreetingThirdTypeAttachVO vo = new GreetingThirdTypeAttachVO();
		vo.setGreetingId(attach.getGreetingId());
		vo.setContent(attach.getContent());
		vo.setKey(attach.getKey());
		vo.setMatchRule(attach.getMatchRuleValue());
		vo.setThirdType(attach.getThirdType());
		vo.setAttachDate(attach.getDate());
		vo.setTitle(attach.getTitle());
		vo.setSubBizType(attach.getSubBizType());
		return vo;
	}
}
