package com.shuidihuzhu.cf.vo.crowdfunding;

import com.shuidihuzhu.client.baseservice.pay.model.pingan.PADuiGongParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NegativeOrZero;
import java.util.List;

/**
 * @package: com.shuidihuzhu.cf.vo.crowdfunding
 * @Author: liujiawei
 * @Date: 2018/11/15  16:01
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@ToString
public class PACheckResultVo {
    private int statusCode;
    private String msg;
    private List<PADuiGongParam> paramList;
    private PADuiGongParam self;
}
