package com.shuidihuzhu.cf.vo.report;

import com.shuidihuzhu.cf.risk.model.admin.list.HospitalAuditRiskDto;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020-08-06
 **/
@Data
public class HospitalAuditRiskDtoExt extends HospitalAuditRiskDto {
    private long id;

    public HospitalAuditRiskDtoExt(Integer caseId, Long workOrderId, Integer hospitalId, String hospitalCode, String hospitalName,
                                   String province, String city, String departments, String areaCode, String landline, String extension,
                                   Date callTime, Integer cityId, Integer provinceId, long id) {
        super(caseId, workOrderId, hospitalId, hospitalCode, hospitalName, province, city, departments, areaCode, landline,
                extension, callTime, cityId, provinceId);
        this.id = id;
    }

    public HospitalAuditRiskDtoExt(long id) {
        this.id = id;
    }

    public HospitalAuditRiskDtoExt() {
    }

}