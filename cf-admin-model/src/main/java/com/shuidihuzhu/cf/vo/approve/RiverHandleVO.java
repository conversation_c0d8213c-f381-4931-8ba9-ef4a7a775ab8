package com.shuidihuzhu.cf.vo.approve;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class RiverHandleVO {

    @ApiModelProperty("提交时间")
    private Date submitTime;

    @ApiModelProperty("操作时间 通过/驳回")
    private Date operationTime;

    /**
     * {@link com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem.MaterialStatus}
     */
    @ApiModelProperty("状态 {1:提交, 2:驳回, 3:通过}")
    private int status;

    private String rejectReason;

    @ApiModelProperty("驳回次数")
    private int rejectCount;

    @ApiModelProperty("操作人详情")
    private String operatorDetail;
}
