package com.shuidihuzhu.cf.vo.approve;

import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationRecordDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class RiverOperationRecordModel {

    private OperationRecordDTO operationRecord;

    private RiverHandleParamVO handleParam;

    @ApiModelProperty("备注内容 (内容里\\n表示换行)")
    private String content;

}
