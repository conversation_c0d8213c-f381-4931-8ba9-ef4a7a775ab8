package com.shuidihuzhu.cf.vo.wx.greeting;

import com.shuidihuzhu.wx.grpc.client.common.Greeting;
import lombok.Data;

/**
 * @Author: duchao
 * @Date: 2018/6/1 上午11:02
 */
@Data
public class GreetingVO {
	private String name;
	private String content;
	private int id;

	public static GreetingVO fromGrpcObject(Greeting greeting) {
		GreetingVO greetingVO = new GreetingVO();
		greetingVO.setContent(greeting.getContent());
		greetingVO.setName(greeting.getName());
		greetingVO.setId(greeting.getId());
		return greetingVO;
	}
}
