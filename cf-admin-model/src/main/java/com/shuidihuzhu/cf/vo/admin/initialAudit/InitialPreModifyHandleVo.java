package com.shuidihuzhu.cf.vo.admin.initialAudit;

import com.shuidihuzhu.cf.client.material.model.CfBasicLivingGuardModel;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoDataStatusTypeEnum;
import com.shuidihuzhu.cf.vo.initialaudit.CfPropertyInsuranceVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
public class InitialPreModifyHandleVo {


    private int caseId;
    private long workOrderId;
    private List<Integer> rejectIds;
    private int workOrderType;


    private InitialMaterial userSourceMaterials;

    private InitialMaterial modifyMaterials;

    private Map<CrowdfundingInfoDataStatusTypeEnum, Set<String>> canModifyFields;

    private Map<CrowdfundingInfoDataStatusTypeEnum, Set<String>> hasModifyFields;

    @ApiModelProperty("用户已经提交的材料项")
    private Set<CrowdfundingInfoDataStatusTypeEnum> hasSubmitMaterials;

    private long userId;

    @Data
    public static class InitialMaterial {
        @ApiModelProperty("图文信息")
        private InitialAuditCaseDetail.CaseBaseInfo caseBaseInfo;

        @ApiModelProperty("身份证信息")
        private InitialAuditCaseDetail.FirstApproveCaseInfo firstApproveCaseInfo;

        @ApiModelProperty("增信信息")
        private InitialAuditCaseDetail.CreditInfo creditInfo;

        @ApiModelProperty("贫困与低保信息")
        private CfBasicLivingGuardModel additionInfo;
    }
}
