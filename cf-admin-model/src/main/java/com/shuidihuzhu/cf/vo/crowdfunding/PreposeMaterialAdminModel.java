package com.shuidihuzhu.cf.vo.crowdfunding;

import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.client.cf.growthtool.model.ClewPreproseMaterialResult;
import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PreposeMaterialAdminModel extends PreposeMaterialModel.MaterialInfoVo {

    private ClewPreproseMaterialResult approvePoints;

    @ApiModelProperty("发起人信息-手机号掩码（后台用）")
    private NumberMaskVo raiseMobileMask;

    private NumberMaskVo patientIdCardMask;

    private NumberMaskVo selfCryptoIdcardMask;


}
