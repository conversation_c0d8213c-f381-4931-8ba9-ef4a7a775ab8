package com.shuidihuzhu.cf.event;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderZhuDongFuWuCheckPayload {

    private int caseId;

    private LocalDateTime sendTime;

    /**
     * {@link FlowTypeEnum}
     */
    private int flowType;

    /**
     * 发送次数
     */
    private int sendCount;

    public enum FlowTypeEnum {

        /**
         * 高金额未提材审
         */
        HIGH_AMOUNT(1),

        /**
         * 材审驳回后未提交
         */
        CASE_INFO_REFUSE(2),
        ;

        @Getter
        private final int value;

        FlowTypeEnum(int value) {
            this.value = value;
        }
    }
}
