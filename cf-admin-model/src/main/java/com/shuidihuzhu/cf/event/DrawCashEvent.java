package com.shuidihuzhu.cf.event;

import com.shuidihuzhu.cf.enums.shutdown.BizTypeEnum;
import com.shuidihuzhu.cf.enums.shutdown.ExecuteStatusEnum;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @time 2018/11/27 下午3:59
 * @desc
 */
public class DrawCashEvent  extends ApplicationEvent {

    @Getter
    private BizTypeEnum bizType;

    @Getter
    private ExecuteStatusEnum executeStatus;

    public DrawCashEvent(BizTypeEnum bizType, ExecuteStatusEnum executeStatus){
        super(StringUtils.EMPTY);
        this.bizType = bizType;
        this.executeStatus = executeStatus;
    }
}
