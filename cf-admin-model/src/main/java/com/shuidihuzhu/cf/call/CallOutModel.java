package com.shuidihuzhu.cf.call;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class CallOutModel {
    private int id;
    private Timestamp dayKey;
    private String summary;
    private String customPhoneNumber;
    private String region;
    private String k3;
    private String seatNumber;
    private String seatName;
    private String seatPhoneNumber;
    private String callTime;
    private String connectTime;
    private String callDuration;
    private String answerStatus;
    private String isCustomHangUp;
    private String totalTime;
    private String callType;
    private String callOutTask;
    private String recordUrl;
    private String callCharge;
    private String totalCallCharge;
    private String comment;
    private String isInCfInfo;
    private String qualityGrade;
    private int status;
    private Timestamp createTime;
    private Timestamp updateTime;
    private int isDelete;
    private String  encryptCustomPhoneNumber;
    private String  encryptSeatPhoneNumber;
}
