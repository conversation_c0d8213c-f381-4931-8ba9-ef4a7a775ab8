package com.shuidihuzhu.cf.call;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class CallInModel {
    private int id;
    private Timestamp dayKey;
    private String summary;
    private String customPhoneNumber;
    private String region;
    private String hotineNumber;
    private String ivr;
    private String queueNumber;
    private String queueName;
    private String seatNumber;
    private String seatName;
    private String seatPhoneNumber;
    private String callTime;
    private String connectTime;
    private String callDuration;
    private String answerStatus;
    private String isHangUp;
    private String totalTime;

    private String recordUrl;
    private String callCharge;
    private String k18;
    private String comment;
    private String isInCfInfo;
    private String qualityGrade;
    private String qualityComment;
    private int status;
    private Timestamp createTime;
    private Timestamp updateTime;
    private int isDelete;
    private String encryptCustomPhoneNumber;
}
