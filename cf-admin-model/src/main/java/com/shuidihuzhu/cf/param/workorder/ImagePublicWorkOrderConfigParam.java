package com.shuidihuzhu.cf.param.workorder;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: wangpeng
 * @Date: 2021/8/19 19:34
 * @Description:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImagePublicWorkOrderConfigParam {
    private Integer orderPeopleNumberLow;
    private Integer orderPeopleNumberHigh;
    private Integer auditTimeLow;
    private Integer auditTimeHigh;
    private int workOrderLevel;
}
