package com.shuidihuzhu.cf.param;

import com.shuidihuzhu.client.cf.workorder.model.ChuciWorkOrder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: wangpeng
 * @Date: 2021/4/22 14:27
 * @Description:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InitialAuditCreateOrderParam {
    /**
     * 案例Id
     */
    private int caseId;
    /**
     * 是否首次创建初审工单
     */
    private boolean first;
    /**
     * 风控限制表述 0；1；
     */
    private int condition;
    /**
     * 案例发起渠道
     */
    private String channel;
    /**
     * 初审创建工单对象
     */
    private ChuciWorkOrder chuciWorkOrder;
    /**
     * 为走智能审核原因
     */
    private String noSmartAuditReason;
    /**
     * 疾病名称
     */
    private String diseaseName;
    /**
     * 工单id
     */
    private long workOrderId;

    private boolean to1V1;
    /**
     * 是否为真高风险
     */
    private boolean realRisk;
    /**
     * 生成高风险工单原因
     */
    private String riskWorkOrderReason;
    /**
     * 是否为解锁预审黑名单、风控流程
     */
    private boolean unLimitInitialAudit;
    /**
     * 是否进行目标金额合理性拦截
     */
    private boolean checkTargetAmount;
}
