package com.shuidihuzhu.cf.param.workorder;

import lombok.Data;

import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2021/8/20 17:41
 * @Description:
 */
@Data
public class ImagePublicHandleParam {
    private long workOrderId;
    private int caseId;
    private int operatorId;
    private List<ImagePublicHandleImageListParam> markImageList;

    @Data
    public static class ImagePublicHandleImageListParam {
        /**
         * @see com.shuidihuzhu.cf.client.adminpure.enums.CfCasePublicInfoImageHandleStatusEnum
         */
        private int handleStatus;
        private String imgUrl;
    }
}
