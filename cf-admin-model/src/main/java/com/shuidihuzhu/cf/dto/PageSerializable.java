package com.shuidihuzhu.cf.dto;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.model.Pagination;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@Data
public class PageSerializable<R> {
    private static final long serialVersionUID = 1L;
    protected Pagination pagination;
    protected List<R> data;

    public PageSerializable(PageInfo pageInfo, List<R> data) {
        if (pageInfo!=null){
            this.pagination = new Pagination(pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getTotal());
            this.data = data;
        }else {
            this.pagination = new Pagination(0, 0, 0);
            this.data = Lists.newArrayList();
        }


    }
}
