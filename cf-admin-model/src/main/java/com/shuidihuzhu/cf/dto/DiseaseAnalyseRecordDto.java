package com.shuidihuzhu.cf.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DiseaseAnalyseRecordDto {
    private long id;
    private int caseId;
    private long workOrderId;
    private String inputDiseaseName;
    private String outputNormalizedResult;
    private String errorMsg;
    private String operator;
    private int hasError;
}
