package com.shuidihuzhu.cf.customer;

import lombok.Data;
import lombok.Getter;

import java.util.Date;

/**
 * <AUTHOR>
 * @time 2019/10/16 下午2:35
 * @desc
 */
@Data
public class CfChatRecordDO {
    private int id;
    private String cId;
    private String partnerId;
    private Date startTime;
    private Date endTime;
    private int conversationDuration;
    private String staffEmail;
    private String staffName;
    //渠道来源,智齿还是udesk
    private int channelFrom;


    @Getter
    public enum ChannelFrom {
        ZHICI(1, "智齿"),
        UDESK(2, "udesk"),
        ;
        private int code;
        private String desc;
        ChannelFrom(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static ChannelFrom getByCode(int code) {
            for (ChannelFrom value : ChannelFrom.values()) {
                if (code == value.code) {
                    return value;
                }
            }
            return null;
        }
    }
}
