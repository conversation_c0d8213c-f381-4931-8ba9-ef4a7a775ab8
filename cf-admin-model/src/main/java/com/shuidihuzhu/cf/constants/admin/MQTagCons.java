package com.shuidihuzhu.cf.constants.admin;

/**
 * Created by wangsf on 18/8/2.
 */
public interface MQTagCons {

	String ADMIN_MQ_TAG_REFUND_ENTRY_OPEN = "ADMIN_MQ_TAG_REFUND_ENTRY_OPEN";

	/**
	 * 发送首次审核通过的mq
	 */
	String ADMIN_MQ_TAG_FIRST_APPROVE_SUCCESS = "ADMIN_MQ_TAG_FIRST_APPROVE_SUCCESS";

	/**
	 * 发送首次审核通过的mq
	 */
	String ADMIN_MQ_TAG_FIRST_APPROVE_FAIL = "ADMIN_MQ_TAG_FIRST_APPROVE_FAIL";

	/**
	 * 组织organization 变更消息 cf-clewtrack 消费
	 */
	String CF_CLEWTRACK_CHANGE_ORGANIZATION_MSG = "CF_CLEWTRACK_CHANGE_ORGANIZATION_MSG";

	String CF_CLEWTRACK_CHANGE_ORGANIZATION_USER_MSG = "CF_CLEWTRACK_CHANGE_ORGANIZATION_MSG";

	// 前置审核工单驳回后，召回用户重新提交材料
	String ADMIN_FIRST_APPROVE_RECALL_SUBMIT_AFTER_REJECT = "ADMIN_FIRST_APPROVE_RECALL_SUBMIT_AFTER_REJECT";

	// 资金操作自动生成工单
	String ADMIN_FINANCE_OPERATER_GENERATE_FLOW_ORDER = "ADMIN_FINANCE_OPERATER_GENERATE_FLOW_ORDER";

	String ADMIN_WEB_SOCKET_MSG_SEND = "ADMIN_WEB_SOCKET_MSG_SEND";

	String ADMIN_FLOW_ORDER_NOTICE_CLEW_V2 = "ADMIN_FLOW_ORDER_NOTICE_CLEW_V2";

	String ADMIN_CLEW_NOTICE_TO_END_FLOW_ORDER = "CLEW_NOTICE_ADMIN_CLOSE_WORK_ORDER";

	String ADMIN_FLOW_ORDER_SINGLE_REFUND_DELAY_48H = "ADMIN_FLOW_ORDER_SINGLE_REFUND_DELAY_48H";

	String ADMIN_DONOR_REFUND_APPLY_48_HOUR  = "DONOR_REFUND_APPLY_48_HOUR";


	// 爱心首页
	String ADMIN_MQ_LOVE_HOME = "ADMIN_MQ_LOVE_HOME";


	// 初次审核工单驳回后，召回用户重新提交材料
	String ADMIN_INITIAL_AUDIT_RECALL_SUBMIT_AFTER_REJECT = "ADMIN_INITIAL_AUDIT_RECALL_SUBMIT_AFTER_REJECT";

	// 平台恢复筹款消息
	String ADMIN_MQ_RESTORE_FUNDRAISING = "ADMIN_MQ_RESTORE_FUNDRAISING";

	// 自动生成工单
	String ADMIN_AUTO_GENERATE_FLOW_ORDER = "ADMIN_AUTO_GENERATE_FLOW_ORDER";

	String BLACK_USER_AUTO_HANDLE_REPORT = "BLACK_USER_AUTO_HANDLE_REPORT";

	String CF_DISHONEST_LABLE_REPORT_MQ = "CF_DISHONEST_LABLE_REPORT_MQ";

	/**
	 * 工单回收mq
	 */
	String CF_WORK_ORDER_RELEASE = "CF_WORK_ORDER_RELEASE";

	/**
	 * 材料审核处理释放
	 */
	String CF_APPROVE_CONTROL_RELEASE = "CF_APPROVE_CONTROL_RELEASE";
	/**
	 * 案例恢复筹款
	 */
	String CF_RECOVER_CASE = "CF_RECOVER_CASE";


	/**
	 *  前置报备发送消息后 24小时后确认消息
	 */
	String record_later_check = "record_later_check";


	/**
	 * 下发动态审核消息MQ
	 */
	String CF_PUSH_DYNAMIC_MSG = "CF_PUSH_DYNAMIC_MSG";

	/**
	 * 动态下发审核回传消息 cf-admin接收
	 */
	public static final String CF_UPLOADING_DYNAMIC_INFO_MSG = "CF_UPLOADING_DYNAMIC_INFO_MSG";
	/**
	 * 1小时后确认消息  初审和材料审核
	 */
	String record_1h_check = "record_1h_check";

	/**
	 * 捐转工单创建mq
	 */
	String juanzhuan_workorder_create = "juanzhuan_workorder_create";

	String juanzhuan_workorder_handle_delay = "juanzhuan_workorder_handle_delay";

	/**
	 * 图片识别结果回调
	 */
	String CF_ATTACHMENT_MARK_ATTR_STAT_RESULT = "CF_ATTACHMENT_MARK_ATTR_STAT_RESULT";
	/**
	 * 检查是否需要生成主动服务工单
	 */
	String CF_ZHU_DONG_FU_WU_CHECK = "CF_ZHU_DONG_FU_WU_CHECK";
	/**
	 * 检查是否生成二次审核工单
	 */
	String SECOND_REVIEW_WORK_ORDER = "SECOND_REVIEW_WORK_ORDER";

	/**
	 * 举报工单检查是否首次触达消息
	 */
	String REPORT_WORK_ORDER_HANDLE_REMIND = "REPORT_WORK_ORDER_HANDLE_REMIND";


	/**
	 * 提交主动服务工单
	 */
	String WORK_ORDER_HANDLE_ZHU_DONG = "WORK_ORDER_HANDLE_ZHU_DONG";

	/**
	 * 上传头图生成动态工单
	 */
	String CASE_HEAD_IMAGE_CREATE_WORK_ORDER = "CASE_HEAD_IMAGE_CREATE_WORK_ORDER";

	/**
	 * 域名流量分配策略
	 */
	String DOMAIN_TRAFFIC_DISTRIBUTION_STRATEGY = "DOMAIN_TRAFFIC_DISTRIBUTION_STRATEGY";

	/**
	 * 工单停止筹款
	 */
	String WORK_ORDER_END_CASE = "WORK_ORDER_END_CASE";

	/**
	 * 举报跟进延时回调
	 */
	String REPORT_SCHEDULE_PUSH = "REPORT_SCHEDULE_PUSH";

	/**
	 * 工单跟进延时回调
	 */
	String MARK_FOLLOW_PUSH = "MARK_FOLLOW_PUSH";

	/**
	 * 举报待录入跟进延时回调V1
	 */
	String REPORT_SCHEDULE_PUSH_V1 = "REPORT_SCHEDULE_PUSH_V1";

	/**
	 * 举报待录入跟进延时回调V2
	 */
	String REPORT_SCHEDULE_PUSH_V2 = "REPORT_SCHEDULE_PUSH_V2";

	/**
	 * 广播模式使用  跟进通知
	 */
	String MARK_FOLLOW_TIME_BROADCASTING = "MARK_FOLLOW_TIME_BROADCASTING";

	/**
	 * 广播模式使用 群发通知
	 */
	String COMMON_STREAM_V2 = "COMMON_STREAM_V2";

	/**
	 * 重复发起案例自动标记举报
	 */
	String REPEAT_THE_CASE_AUTO_REPORT = "REPEAT_THE_CASE_AUTO_REPORT";

	/**
	 * 打款后生成信息传递工单
	 * 需求wiki：https://wiki.shuiditech.com/pages/viewpage.action?pageId=928122586
	 */
	String PUNCH_CREATE_MESSAGE_DELIVER_WORK_ORDER = "PUNCH_CREATE_MESSAGE_DELIVER_WORK_ORDER";

	/**
	 * 初审操作，无论是什么操作都发送的topic
	 */
	String CF_INITIAL_AUDIT_HANDLE_V2 = "CF_INITIAL_AUDIT_HANDLE_V2";

	/**
	 * ai第三次复拨
	 */
	String CF_APPROVE_AI_CALL_THREE = "CF_APPROVE_AI_CALL_THREE";

	/**
	 * 款项合理性
	 */
	String CF_AMOUNT_REASONABLE_TASK = "CF_AMOUNT_REASONABLE_TASK";

	/**
	 * 材审后新版消息
	 */
	String INFO_APPROVE_MSG_V2 = "INFO_APPROVE_MSG_V2";

	/**
	 * 发起问题-线索流转至线下顾问。生成线索
	 */
	String WORK_ORDER_CREATE_CLEW = "WORK_ORDER_CREATE_CLEW";

    /**
     * 发起问题-线索流转至线下顾问。工单处理
     */
    String WORK_ORDER_CREATE_CLEW_ORDER_HANDLE = "WORK_ORDER_CREATE_CLEW_ORDER_HANDLE";

}
