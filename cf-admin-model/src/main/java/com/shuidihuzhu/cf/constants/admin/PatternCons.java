package com.shuidihuzhu.cf.constants.admin;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2019-04-24  14:52
 *
 * 所有正则谨慎修改
 * 检查业务调用是否有特殊要求后修改
 */
public interface PatternCons {

    /**
     * 仅空格
     */
    Pattern SPACE = Pattern.compile(" ");

    /**
     * 匹配任何空白字符，包括空格、制表符、换页符等等。等价于 [ \f\n\r\t\v]。
     *
     * 1.不间断空格\u00A0,主要用在office中,让一个单词在结尾处不会换行显示,快捷键ctrl+shift+space ;
     * 2.半角空格(英文符号)\u0020,代码中常用的;
     * 3.全角空格(中文符号)\u3000,中文文章中使用;
     */
    Pattern BLANK = Pattern.compile("[\\s\\u3000\\u00A0]");

    /**
     * 纯字母匹配
     */
    Pattern PURE_WORD_PATTERN = Pattern.compile("^[a-zA-Z]+$");

    /**
     * 微信号匹配
     */
    Pattern WX_PATTERN = Pattern.compile("[a-zA-Z][-_a-zA-Z0-9]{5,19}");

    Pattern SENSITIVE_NUMBER_PATTERN = Pattern.compile("\\d{7,19}");

    /**
     * 不能表示数字的字符
     */
    Pattern UNLIKE_NUMBERS = Pattern.compile(
            "[^0-9" +
//                    汉字容易误伤
//                    "一二三四五六七八九十" +
            "❶❷❸❹❺❻❼❽❾❿" +
            "º¹²³⁴⁵⁶⁷⁸⁹⁰" +
            "₀₁₂₃₄₅₆₇₈₉" +
            "⓪①②③④⑤⑥⑦⑧⑨⑩⑪⑫⑬⑭⑮⑯⑰⑱⑲⑳" +
            "壹贰叁肆伍陆柒捌玖拾" +
            "⑴⑵⑶⑷⑸⑹⑺⑻⑼⑽⑾⑿⒀⒁⒂⒃⒄⒅⒆⒇" +
            "ⅠⅡⅢⅣⅤⅥⅦⅧⅨⅩⅪⅫ" +
            "⒈⒉⒊⒋⒌⒍⒎⒏⒐⒑⒒⒓⒔⒕⒖⒗⒘⒙⒚⒛" +
            "]"
    );

    static void main(String[] args){
        String content = "加176ސ00181236 能帮助到你，祝患者早日康复";
        content = PatternCons.SENSITIVE_NUMBER_PATTERN.matcher(content).replaceAll("");
        System.out.println("content = " + content);
        System.out.println("AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz。 ， 、 ： ？ ！ … - · ˉ ˇ ¨ ‘ ' “ ” 々 ～ ‖ ∶ ＂ ＇ ｀ ｜ 〃 〔〕 〈〉 《》 「」 『』 ． 〖〗 【】 （） ［］ ｛｝, . ; ≈≡≠＝≤≥＜＞≮≯∷±＋－×÷／∫∮∝∞∧∨∑∏∪∩∈∵∴⊥∥∠⌒⊙≌∽√°′″＄￡￥‰％℃¤￠○┌┍┎┏┐┑┒┓─┄┈├┝┞┟┠┡┢┣│┆┊┬┭┮┯┰┱┲┳┼┽┾┿╀╁╂╃§№☆★○●◎◇◆□■△▲※→←↑↓〓＃＆＠＼＾＿＆＠＼＾".replaceAll(" ",""));
    }
}
