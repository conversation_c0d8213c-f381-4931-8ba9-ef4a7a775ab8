package com.shuidihuzhu.cf.constants.admin;

/**
 * Created by sven on 18/8/19.
 *
 * <AUTHOR>
 */
public final class WorkOrderStatConstant {

    private WorkOrderStatConstant() {
        //禁止实例化
    }

    /**
     * 在grafana中的上报总类别
     */
    public static final String ADMIN_WORK_ORDER = "cf_admin_work_order";

    /**
     * 工单种类，相当于AdminWorkOrderConst.Type
     *
     * @see {@link com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst.Type}
     */
    public static final String ADMIN_WORK_ORDER_CATEGORY = "category";

    /**
     * 工单子类型，相当于AdminWorkOrderConst.Task
     *
     * @see {@link com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst.Task}
     */
    public static final String ADMIN_WORK_ORDER_SUBTYPE = "subType";

    /**
     * 工单taskCode，相当于AdminWorkOrderConst.TaskType
     *
     * @see {@link com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst.TaskType}
     */
    public static final String ADMIN_WORK_ORDER_TASK_CODE = "taskCode";

    /**
     * 工单taskCode，相当于AdminWorkOrderConst.Role
     *
     * @see {@link com.shuidihuzhu.cf.enums.admin.AdminWorkOrderConst.Role}
     */
    public static final String ADMIN_WORK_ORDER_ROLE = "role";
}
