package com.shuidihuzhu.cf.constants.crowdfunding;

import org.apache.commons.lang3.tuple.Pair;


/**
 * <AUTHOR>
 */
public interface AdminCasePageAnnouncementManageConst {
    /**
     * ONLINE
     */
    Pair<Integer, String> LINE = Pair.of(0, "下线");
    Pair<Integer, String> ONLINE = Pair.of(1, "上线");


    /**
     * TOP
     */
    Pair<Integer, String> NON_TOP = Pair.of(0, "非置顶");
    Pair<Integer, String> TOP = Pair.of(1, "置顶");

    /**
     * TYPE
     */
    Pair<Integer, String> ANNOUNCEMENT_TYPE = Pair.of(0, "公告");

    Pair<Integer, String> RUMOUR_TYPE = Pair.of(1, "谣言");

}
