package com.shuidihuzhu.cf.admin.util.admin;


import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

/**
 * @package: com.shuidihuzhu.cf.admin.util.admin
 * @Author: l<PERSON>jiaw<PERSON>
 * @Date: 2018/10/24  16:10
 */
public class IntegerUtil {

    /**
     * 去空格，错误默认返回 0，浮点值自动四舍五入
     * @param value
     * @return
     */
    public static int parseInt(final String value) {
        if (StringUtils.isBlank(value)){
            return 0;
        }
        if (value.contains(".")) {
            return (int) Math.round(NumberUtils.toDouble(value.trim()));
        }
        return NumberUtils.toInt(value.trim());
    }

    /**
     * 去空格，错误默认返回defaultValue
     * @param value
     * @param defaultValue
     * @return
     */
    public static int parseInt(final String value, Integer defaultValue) {
        if (StringUtils.isBlank(value) || defaultValue == null){
            return 0;
        }
        return NumberUtils.toInt(value.trim(),defaultValue);
    }


}



