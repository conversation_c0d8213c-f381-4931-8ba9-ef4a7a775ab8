package com.shuidihuzhu.cf.admin.util.admin;

import com.shuidihuzhu.common.web.model.IdcardInfoExtractor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Date;
import java.util.Objects;

/**
 * @Author: duchao
 * @Date: 2018/8/15 下午2:44
 */
@Slf4j
public class AdminCfIdCardUtil {

	/**
	 * 合法的身份证长度（水滴全系统不支持15位身份证）
	 */
	private final static int VALID_ID_CARD_LENGTH = 18;

	/**
	 * 身份证的正则表达式（水滴全系统不支持15位身份证）
	 */
	private final static String VALID_ID_CARD_REG = "^\\d{17}[0-9Xx]$";


	/**
	 * 校验身份证格式是否符合水滴的要求
	 * 水滴全系统不支持15位身份证
	 *
	 * @param idCard
	 * @return
	 */
	public static boolean isValidIdCard(String idCard){
	    //如果为空说明有问题
		if(StringUtils.isBlank(idCard)){
			return false;
		}
		//隐藏字符过滤
		idCard = filiterHideCode(idCard);

		if(idCard.length() != VALID_ID_CARD_LENGTH){
			return false;
		}

		if(!idCard.matches(VALID_ID_CARD_REG)){
			return false;
		}

		return true;
	}

	public static int getAge(String idCard) {
		//隐藏字符过滤
		idCard = filiterHideCode(idCard);
		try {
			int year = LocalDate.now().getYear();
			if (idCard.length() == 18) {
				return year - Integer.parseInt(idCard.substring(6, 10));
			} else if (idCard.length() == 15) {
				return year - Integer.parseInt("19" + idCard.substring(6, 8));
			}
		} catch (Exception e) {
			log.error("身份证获取年龄失败", e);
		}
		return 0;
	}

	public static int getIdCardAge(String idCard) {
		idCard = filiterHideCode(idCard);
		if (idCard.length() != 18 && idCard.length() != 15) {
			return -1;
		}

		try {
			int nowYear = LocalDate.now().getYear();
			int idCardYear = new IdcardInfoExtractor(idCard).getYear();
			return calculateAge(nowYear, idCardYear);
		} catch (Exception e) {
			log.error("身份证获取年龄失败 getIdCardAge ", e);
		}
		return -1;
	}

	private static int calculateAge(int nowYear, int idCardYear) {
		return (nowYear - idCardYear) >= 0 ? (nowYear - idCardYear) : -1;
	}

	public static String filiterHideCode(String idCard) {
		return idCard.replaceAll("\\p{C}", "").trim();
	}

	public static void main(String[] args) {
		System.out.println(isValidIdCard("4115a1198612053959"));
		System.out.println(isValidIdCard("6321231982-9270517"));
		System.out.println(isValidIdCard("36082219921102645x"));
		System.out.println(isValidIdCard("3608221988077265"));
		System.out.println(isValidIdCard("36082219820707136a"));
		System.out.println(isValidIdCard("36082219850204022"));
		System.out.println(isValidIdCard("110114201703190528"));
		String idCard = "\u202d1101142";
		idCard = filiterHideCode(idCard);
		System.out.println(Integer.parseInt(idCard));
	}

	// 翻转身份证的最后一位大小写  比如 1323X-->1323x   1323x-->1323X
	public static String flipLastChar(String idCard) {

		if (StringUtils.isBlank(idCard)) {
			return idCard;
		}

		int lastIndex = idCard.length() - 1;
		char lastChar = idCard.charAt(lastIndex);

		if (Character.isDigit(lastChar)) {
			return idCard;
		}

		return idCard.substring(0, lastIndex) + (Character.isUpperCase(lastChar)
				? Character.toLowerCase(lastChar) : Character.toUpperCase(lastChar));
	}

	// 将身份证的最后一位转为大写  比如 1323x-->1323X   1323-->1323
	public static String convertLastCharUpper(String idCard) {

		if (StringUtils.isBlank(idCard)) {
			return idCard;
		}

		int lastIndex = idCard.length() - 1;
		char lastChar = idCard.charAt(lastIndex);

		if (Character.isDigit(lastChar) || Character.isUpperCase(lastChar)) {
			return idCard;
		}

		return idCard.substring(0, lastIndex) + Character.toUpperCase(lastChar);
	}

	public static String parsePatientBirth(String idCard) {

		if (StringUtils.isBlank(idCard)) {
			return "";
		}

		String birthDateStr = "";
		if (idCard.length() == 18) {
			birthDateStr = idCard.substring(6, 14);
		} else if (idCard.length() == 15) {
			birthDateStr = "19" + idCard.substring(6, 12);
		}
		return birthDateStr;
	}

}
