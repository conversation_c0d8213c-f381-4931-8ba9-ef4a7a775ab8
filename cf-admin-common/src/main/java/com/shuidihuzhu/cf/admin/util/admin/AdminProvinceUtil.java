package com.shuidihuzhu.cf.admin.util.admin;

import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @DATE 2018/7/30
 */
public final class AdminProvinceUtil {

    private AdminProvinceUtil() {
    }

    private final static Map<Integer,String> pMap = Maps.newHashMap();

    static {
        pMap.put(11,"北京市");
        pMap.put(43,"湖南省");
        pMap.put(12,"天津市");
        pMap.put(44,"广东省");
        pMap.put(13,"河北省");
        pMap.put(45,"广西壮族自治区");
        pMap.put(14,"山西省");
        pMap.put(46,"海南省");
        pMap.put(15,"内蒙古自治区");
        pMap.put(50,"重庆市");
        pMap.put(21,"辽宁省");
        pMap.put(51,"四川省");
        pMap.put(22,"吉林省");
        pMap.put(52,"贵州省");
        pMap.put(23,"黑龙江省");
        pMap.put(53,"云南省");
        pMap.put(31,"上海市");
        pMap.put(54,"西藏自治区");
        pMap.put(32,"江苏省");
        pMap.put(61,"陕西省");
        pMap.put(33,"浙江省");
        pMap.put(62,"甘肃省");
        pMap.put(34,"安徽省");
        pMap.put(63,"青海省");
        pMap.put(35,"福建省");
        pMap.put(64,"宁夏回族自治区");
        pMap.put(36,"江西省");
        pMap.put(65,"新疆维吾尔自治区");
        pMap.put(37,"山东省");
        pMap.put(71,"台湾省");
        pMap.put(41,"河南省");
        pMap.put(81,"香港特别行政区");
        pMap.put(42,"湖北省");
        pMap.put(82,"澳门特别行政区");
    }


    public static String getProvinceBycode(int code){
        return pMap.get(code);
    }

    public static int getProvinceByName(String name){

        if (StringUtils.isEmpty(name)){
            return 0;
        }
        String pname = name.substring(0,2);
        for (Integer key : pMap.keySet()){
            String value = pMap.get(key);
            if (pname.equals(value.substring(0,2))){
                return key;
            }
        }
        return 0;
    }
}
