package com.shuidihuzhu.cf.admin.util.lock;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Author: wangpeng
 * @Date: 2021/1/25 16:00
 * @Description: 注解动态参数解析通用工具类.
 * 例子：key_#{方法参数}
 */
@Slf4j
@Component
public class AnnotationAopParamResolver {

    public String resolver(JoinPoint joinpoint, String toResolverStr) throws Exception {
        if (StringUtils.isEmpty(toResolverStr)) {
            return "";
        }
        StringBuffer stringBuffer = new StringBuffer();
        String pattern = "#\\{(.+?)\\}";
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(toResolverStr);
        while (m.find()) {
            String key = m.group()
                    .replaceAll("#\\{", "")
                    .replaceAll("\\}", "");
            String keyVar = "";
            if (key.contains(".")) {
                keyVar = complexResolver(joinpoint, key);
            } else {
                keyVar = simpleResolver(joinpoint, key);
            }
            if (StringUtils.isEmpty(keyVar) || StringUtils.equals(keyVar, "0")) {
                return "";
            }
            m.appendReplacement(stringBuffer, keyVar);

        }
        return stringBuffer.toString();
    }

    private String simpleResolver(JoinPoint joinPoint, String str) {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        String[] names = methodSignature.getParameterNames();
        Object[] args = joinPoint.getArgs();
        for (int i = 0; i < names.length; i++) {
            if (str.equals(names[i])) {
                return args[i].toString();
            }
        }
        return null;
    }

    private String complexResolver(JoinPoint joinPoint, String str) throws Exception {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        String[] names = methodSignature.getParameterNames();
        Object[] args = joinPoint.getArgs();
        String[] paramNames = str.split("\\.");
        for (int i = 0; i < names.length; i++) {
            if (paramNames[0].equals(names[i])) {
                Object obj = args[i];
                Method method = obj.getClass().getDeclaredMethod(getMethodName(paramNames[1]),  null);
                Object value = method.invoke(args[i]);
                Object valueObject = getValue(value, 1, paramNames);
                return Objects.isNull(valueObject) ? "" : valueObject.toString();
            }
        }
        return null;
    }

    private Object getValue(Object obj, int index, String[] paramNames) {
        try {
            if (obj != null && index < paramNames.length - 1) {
                Method method = obj.getClass().getDeclaredMethod(getMethodName(paramNames[index + 1]), null);
                obj = method.invoke(obj);
                getValue(obj, index + 1, paramNames);
            }
            return obj;
        } catch (Exception e) {
            log.error("annotationAopParamResolver complexResolver getValue is error : {} {}", paramNames, e);
            return null;
        }
    }

    private String getMethodName(String name) {
        return "get" + name.replaceFirst(name.substring(0, 1), name.substring(0, 1).toUpperCase());
    }

}
