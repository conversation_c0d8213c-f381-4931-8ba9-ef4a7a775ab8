package com.shuidihuzhu.cf.admin.util.wordfilter2;

import com.google.common.base.Splitter;
import com.google.common.collect.Sets;
import com.shuidihuzhu.common.web.util.http.HttpResponseModel;
import com.shuidihuzhu.common.web.util.http.HttpUtil;
import com.shuidihuzhu.common.web.util.http.HttpResponseModel;
import com.shuidihuzhu.common.web.util.http.HttpUtil;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

public class SensitiveWordInit {
	private static final Logger LOGGER = LoggerFactory.getLogger(SensitiveWordInit.class);

	public final static String URL = "http://cf-images.oss-cn-shanghai.aliyuncs.com/file/sensitiveWord.txt"; 
	
	@SuppressWarnings("rawtypes")
	private HashMap sensitiveWordMap;
	
	private int size;

	public SensitiveWordInit() {
		initKeyWord();
	}

	private void initKeyWord() {
		try {
			// 读取敏感词库
			Set<String> keyWordSet = readSensitiveWordFile();
			// 将敏感词库加入到HashMap中
			addSensitiveWordToHashMap(keyWordSet);
			// spring获取application，然后application.setAttribute("sensitiveWordMap",sensitiveWordMap);
		} catch (Exception e) {
			LOGGER.error("initKeyWord error!", e);
		}
	}

	/**
	 * 读取敏感词库，将敏感词放入HashSet中，构建一个DFA算法模型：<br>
	 * 中 = { isEnd = 0 国 = {<br>
	 * isEnd = 1 人 = {isEnd = 0 民 = {isEnd = 1} } 男 = { isEnd = 0 人 = { isEnd =
	 * 1 } } } } 五 = { isEnd = 0 星 = { isEnd = 0 红 = { isEnd = 0 旗 = { isEnd = 1
	 * } } } }
	 * 
	 * @param keyWordSet
	 *            敏感词库
	 * @version 1.0
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	private void addSensitiveWordToHashMap(Set<String> keyWordSet) {
		sensitiveWordMap = new HashMap(keyWordSet.size()); // 初始化敏感词容器，减少扩容操作
		String key = null;
		Map nowMap = null;
		Map<String, String> newWorMap = null;
		// 迭代keyWordSet
		Iterator<String> iterator = keyWordSet.iterator();
		while (iterator.hasNext()) {
			key = iterator.next(); // 关键字
			nowMap = sensitiveWordMap;
			for (int i = 0; i < key.length(); i++) {
				char keyChar = key.charAt(i); // 转换成char型
				Object wordMap = nowMap.get(keyChar); // 获取

				if (wordMap != null) { // 如果存在该key，直接赋值
					nowMap = (Map) wordMap;
				} else { // 不存在则，则构建一个map，同时将isEnd设置为0，因为他不是最后一个
					newWorMap = new HashMap<String, String>();
					newWorMap.put("isEnd", "0"); // 不是最后一个
					nowMap.put(keyChar, newWorMap);
					nowMap = newWorMap;
				}

				if (i == key.length() - 1) {
					nowMap.put("isEnd", "1"); // 最后一个
				}
			}
		}
	}

	/**
	 * 读取敏感词库中的内容，将内容添加到set集合中
	 * 
	 * @version 1.0
	 * @throws Exception
	 */
	private Set<String> readSensitiveWordFile() throws Exception {
		HttpResponseModel httpGet = HttpUtil.httpGet(URL);
		if (httpGet.getStatusCode() == HttpStatus.SC_OK) {
			List<String> keyList = Splitter.on("\n").splitToList(httpGet.getBodyString());
            List<String> collect = keyList.stream().map(val -> val.trim().replace("\\r", "")).collect(Collectors.toList());
            LOGGER.info("原始数据量:{}", collect.size());
			Set<String> keySet = Sets.newHashSet(collect);
			LOGGER.info("去重数据量:{}", keySet.size());
			size = keySet.size();
			return keySet;
		}
		return Collections.emptySet();
	}

	@SuppressWarnings("rawtypes")
	public HashMap getSensitiveWordMap() {
		return sensitiveWordMap;
	}
	
	public int getSize() {
		return size;
	}
}
