package com.shuidihuzhu.cf.admin.util.lock;

import java.lang.annotation.*;

/**
 * @Author: wangpeng
 * @Date: 2021/1/25 14:16
 * @Description: Redis lock define。
 * @notice 对内部调用方法加此注解时，注意aop是否能获取到代理对象 @see CrowdFundingProgress#saveProgress
 */
@Documented
@Target(value = {ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface RedisDistributedLock {

    /**
     * redis lack key. For example: supply_progress_supply_#{action.caseId}
     *
     * @see com.shuidihuzhu.cf.admin.util.lock.AnnotationAopParamResolver
     */
    String key() default "";

    /**
     * lock time default 5 second
     */
    long lockTimeMillis() default DEFAULT_TIME;

    /**
     * lock max time default 10 minute
     */
    long lockMaxTimeMillis() default MAX_TIME;

    /**
     * lock min time default 1 second
     */
    long lockMinTimeMillis() default MIN_TIME;

    /**
     * wait lock time default 5 second
     */
    long lockWaitTimeMillis() default MIN_TIME;

    long DEFAULT_TIME = 60 * 1000L;

    long MAX_TIME = 10 * 60 * 1000L;

    long MIN_TIME = 5 * 1000L;;
}
