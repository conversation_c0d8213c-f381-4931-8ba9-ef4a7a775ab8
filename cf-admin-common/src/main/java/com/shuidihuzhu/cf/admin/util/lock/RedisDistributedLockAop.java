package com.shuidihuzhu.cf.admin.util.lock;

import com.shuidihuzhu.cf.admin.util.lock.RedisDistributedLock;
import com.shuidihuzhu.cf.admin.util.lock.AnnotationAopParamResolver;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Author: wangpeng
 * @Date: 2021/1/25 14:29
 * @Description: redis lock aop impl
 */
@Slf4j
@Aspect
@Component
public class RedisDistributedLockAop {

    @Resource
    private AnnotationAopParamResolver annotationAopParamResolver;
    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    @Around(value = "@annotation(redisDistributedLock)")
    public Object doAround(ProceedingJoinPoint joinPoint, RedisDistributedLock redisDistributedLock) throws Throwable {

        String lockName = annotationAopParamResolver.resolver(joinPoint, redisDistributedLock.key());
        if (StringUtils.isEmpty(lockName)) {
            log.error("redisDistributedLockAop doAround resolver lockName is empty !!!  {}", redisDistributedLock.key());
            throw new Exception("请构建正确的分布式锁名称！");
        }

        long lockTimeMillis = redisDistributedLock.lockTimeMillis() <= 0 ?
                redisDistributedLock.lockMinTimeMillis() :
                (redisDistributedLock.lockTimeMillis() > redisDistributedLock.lockMaxTimeMillis() ?
                        redisDistributedLock.lockMinTimeMillis() : redisDistributedLock.lockTimeMillis());

        String identifier = "";

        try {
            identifier = redissonHandler.tryLock(lockName, redisDistributedLock.lockWaitTimeMillis(), lockTimeMillis);
            if (StringUtils.isEmpty(identifier)) {
                log.error("redisDistributedLockAop doAround tryLock is false !!! lockName : {}", redisDistributedLock.key());
                throw new Exception("获取分布式锁失败！");
            }

            return joinPoint.proceed();
        } catch (Exception exception) {
            log.error("redisDistributedLockAop doAround proceed is error !!! lockName : {} msg : {}", redisDistributedLock.key(), exception);
            throw new Exception("系统错误！");
        } finally {
            try {
                redissonHandler.unLock(lockName, identifier);
            } catch (Exception e) {
                log.info("redisDistributedLockAop unLock is error lockName {}", lockName, e);
            }
        }
    }

}
